{"accessibility.onboarding.accessibility.button": "Əlçatan<PERSON><PERSON>q <PERSON>ı...", "accessibility.onboarding.screen.narrator": "Danışmanı aktivləşdirmək üçün enter düyməsini basın", "accessibility.onboarding.screen.title": "Minecraft-a xoş gəlmisiniz!\n\n Narratoru aktivləşdirmək və ya əlçatanlıq menyusuna daxil olmaq istərdinizmi?", "addServer.add": "Bitdi", "addServer.enterIp": "Server <PERSON><PERSON><PERSON>", "addServer.enterName": "Server <PERSON><PERSON>", "addServer.resourcePack": "Server <PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.disabled": "Passiv", "addServer.resourcePack.enabled": "Aktiv", "addServer.resourcePack.prompt": "Sor", "addServer.title": "Server Məlumatın<PERSON> Düzəlt", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Mod", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Həmişə aktiv", "advMode.mode.conditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstone": "<PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Qırm<PERSON><PERSON>ı daş tələb olunur", "advMode.mode.sequence": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "Yaradıcılıq modundaki bir idarəçi olmalısınız", "advMode.notEnabled": "<PERSON><PERSON><PERSON> blokları bu serverdə aktiv edilmədi", "advMode.previousOutput": "Əvvəlki Çıxış", "advMode.setCommand": "Blok üçün <PERSON>", "advMode.setCommand.success": "Əmr nizamlandı: %s", "advMode.trackOutput": "Çıxış verilənlərini izlə", "advMode.triggering": "İşəsalınma", "advMode.type": "Növ", "advancement.advancementNotFound": "Nam<PERSON>lum nəaliyyət: %s", "advancements.adventure.adventuring_time.description": "Bütün biomları kəşf et", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON><PERSON>dı", "advancements.adventure.arbalistic.description": "<PERSON><PERSON> müxtə<PERSON><PERSON> can<PERSON>ını bir arbalet atışı ilə öldür", "advancements.adventure.arbalistic.title": "Arbalestik", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON> a<PERSON>kar etmə<PERSON>in qarşısını almaq üçün Sculc Sensor və ya Keşikçinin yanında gizlənin", "advancements.adventure.avoid_vibration.title": "Sakitlik 100", "advancements.adventure.blowback.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-shot Wind Charge ilə Breeze öldürün", "advancements.adventure.blowback.title": "Keçmişd<PERSON><PERSON> qayıt", "advancements.adventure.brush_armadillo.description": "Fırçadan istifadə edərək armadillonu tərəzidən təmizləyin", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON>, <PERSON><PERSON> de<PERSON>?", "advancements.adventure.bullseye.description": "Ən azı 30 metr məsafədən Hədəf blokunun mərkəzinə vurun", "advancements.adventure.bullseye.title": "<PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "4 Saxsı Qırığından Naxışlı Qab yaradın", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Ehmalcasına Bərpa", "advancements.adventure.crafters_crafting_crafters.description": "İstehsalçı istehsal edəndə onun yanında ol", "advancements.adventure.crafters_crafting_crafters.title": "Hazırlayıcı Hazırlayıcını Hazırlayır", "advancements.adventure.fall_from_world_height.description": "Dünyanın zirvəsindən (inşa həddi) dünyanın sonuna qədər sərbəst düş və sağ qal", "advancements.adventure.fall_from_world_height.title": "Mağaralar və uçurumlar", "advancements.adventure.heart_transplanter.description": "Bir Cırıltılı Ürəyi iki Solğun Palıd Odunu bloku arasına düzgün şəkildə yerləşdir", "advancements.adventure.heart_transplanter.title": "Ürək <PERSON>", "advancements.adventure.hero_of_the_village.description": "Kəndi uğurla basqından müdafiə et", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Düşüşünüzü zəiflətmək üçün Bal Blokuna tullanın", "advancements.adventure.honey_block_slide.title": "Yapışqan Vəziyyət", "advancements.adventure.kill_a_mob.description": "Hər hansı bir düşmən bədh<PERSON><PERSON><PERSON><PERSON>n", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "Hər düşmən bəheybətdən bir dənə öldürün", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Sculk Catalyst yaxınlığında bir canlını öldürün", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "O yayılır", "advancements.adventure.lighten_up.description": "Mis lampanı daha parlaq etmək üçün balta ilə təmizləyin", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> alın", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "<PERSON><PERSON><PERSON><PERSON> yanğın başlamadan arzuolun<PERSON>z <PERSON> qoruyun", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.minecraft_trials_edition.description": "Obsidian otağına daxil olun", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON>ına<PERSON> (lər) Buraxılışı", "advancements.adventure.ol_betsy.description": "Arbalet ilə vurun", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON>' <PERSON>", "advancements.adventure.overoverkill.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> hər vuruşu üçün 50 ədəd ürək zədəsi verir", "advancements.adventure.overoverkill.title": "Yüksəl-Yox Et", "advancements.adventure.play_jukebox_in_meadows.description": "Jukebox-dan musiqi səsi ilə Meadows-u canlandırın", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON> səsi", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Komparatordan istifadə edərək kəsilmiş kitab rəfinin güc siqnalını oxuyun", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Kitabların <PERSON>", "advancements.adventure.revaulting.description": "Lənətli Açarla Lənılli Seyfi aç", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.root.description": "Mac<PERSON><PERSON>, kəşf və müharibə", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "Saxsı Qırığını almaq üçün Şubhəli bloku təmizləyin", "advancements.adventure.salvage_sherd.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.shoot_arrow.description": "Arbalet ilə nəyəsə vurun", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "Yenidən doğulma məkanı dəyişmək üçün Yataqda yatın", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> yux<PERSON>", "advancements.adventure.sniper_duel.description": "Ən azı 50 metr məsafədən bir skeleti öldürün", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.spyglass_at_dragon.description": "Spyglass vasitəsilə Ender Dragon-a baxın", "advancements.adventure.spyglass_at_dragon.title": "Bu bir təyyarədir?", "advancements.adventure.spyglass_at_ghast.description": "Bir Spyglass vasitəsilə Ghast baxın", "advancements.adventure.spyglass_at_ghast.title": "Bu bir şardır?", "advancements.adventure.spyglass_at_parrot.description": "Spyglass vasitəsilə tutuquşuya baxın", "advancements.adventure.spyglass_at_parrot.title": "Bu bir quşdur?", "advancements.adventure.summon_iron_golem.description": "Kəndlilərə kömək üçün Dəmir qolem çağır", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON><PERSON> kö<PERSON>", "advancements.adventure.throw_trident.description": "Nəyəsə üçbaşlı əsa ilə atın.\nQeyd: <PERSON><PERSON><PERSON> silahınızı atmaq yaxşı fikir deyil.", "advancements.adventure.throw_trident.title": "T<PERSON>k istifadəlik zarafat", "advancements.adventure.totem_of_undying.description": "Ölümü fırıldaq etmək üçün bir Totem istifadə edin", "advancements.adventure.totem_of_undying.title": "YarıÖlü", "advancements.adventure.trade.description": "Bir kəndli ilə müvəffəqiyyətlə ticarət et", "advancements.adventure.trade.title": "Razılaşdıq!", "advancements.adventure.trade_at_world_height.description": "Kə<PERSON><PERSON> ilə ən yuxarı nöqtədə ticarət edin", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON> taciri", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Bu dəmirçinin şablonları ən azı bir dəfə işlədin: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Qabırğa, Qəyyum, Süküt, Fitnəçi, Qabarma, Bələdçi", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Stillə Dəmirçilik", "advancements.adventure.trim_with_any_armor_pattern.description": "Dəmirçilik Masasında bəzək zirehi yaradın", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Bir pirsinq ox ilə iki <PERSON>", "advancements.adventure.two_birds_one_arrow.title": "Bir oxla iki quş", "advancements.adventure.under_lock_and_key.description": "Sınaq Açarı ilə Kassa Kilidini Açın", "advancements.adventure.under_lock_and_key.title": "Kilid və Açar Altında", "advancements.adventure.use_lodestone.description": "Bir Kompası Maqnit daşı üzərində istifadə et", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ni Evə Cəzb Et.", "advancements.adventure.very_very_frightening.description": "<PERSON>ir kəndlini ildırım ilə vur", "advancements.adventure.very_very_frightening.title": "Çox çox qorxuducu", "advancements.adventure.voluntary_exile.description": "Bir basqın kapitanını öldürün.\nBəlkə bundan sonra kəndlərdən uzaq durmağı nəzərdən keçirəcək...", "advancements.adventure.voluntary_exile.title": "Könüll<PERSON>ü<PERSON>ün", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Tozlu Qarın üstündə gəzin... ona batmadan", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON><PERSON> kimi parla", "advancements.adventure.who_needs_rockets.description": "Özünüzü 8 Blok Yuxarıya Atmaq Üçün Külək Enerjisindən İstifadə Edin", "advancements.adventure.who_needs_rockets.title": "Fişəng istəyən var?", "advancements.adventure.whos_the_pillager_now.description": "Quldura öz dərmanının dadını göstər", "advancements.adventure.whos_the_pillager_now.title": "İndi kimdir quldur?", "advancements.empty": "Burada bir şey varmış kimi görünmür...", "advancements.end.dragon_breath.description": "Əjdaha Nəfəsini Şüşə Butulkaya Toplayın", "advancements.end.dragon_breath.title": "<PERSON><PERSON><PERSON><PERSON> ehtiya<PERSON>ın var", "advancements.end.dragon_egg.description": "<PERSON><PERSON>daha yumurtasını tut", "advancements.end.dragon_egg.title": "<PERSON>öv<PERSON><PERSON><PERSON> nə<PERSON>l", "advancements.end.elytra.description": "Elitra Tap", "advancements.end.elytra.title": "Göy üzü sərhəddir", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON> q<PERSON>ın", "advancements.end.enter_end_gateway.title": "Uzaq qaçış", "advancements.end.find_end_city.description": "<PERSON><PERSON><PERSON><PERSON> gir, nə ola bilər ki?", "advancements.end.find_end_city.title": "<PERSON><PERSON>un sonundakı şəhər", "advancements.end.kill_dragon.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.kill_dragon.title": "<PERSON>\"a <PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Şalkerin hücumundan sonra 50 blok yuxarı uç", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> möhtəşəm mənzərə", "advancements.end.respawn_dragon.description": "End əjdahasını canlandır", "advancements.end.respawn_dragon.title": "Son... Yenə...", "advancements.end.root.description": "<PERSON><PERSON><PERSON> b<PERSON>ğı<PERSON>?", "advancements.end.root.title": "SON?", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Allay-dan bir qeyd blokuna bir tort buraxın", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "Allay sizə malları çatdırsın", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON><PERSON>nin Məndə Dostun Var", "advancements.husbandry.axolotl_in_a_bucket.description": "Vedrədə bir Axolotl tutun", "advancements.husbandry.axolotl_in_a_bucket.title": "Ən şirin yırtıcı", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON><PERSON><PERSON> olan hə<PERSON>, hətta sizin üçün yaxşı olmasa belə, yeyin", "advancements.husbandry.balanced_diet.title": "<PERSON><PERSON>", "advancements.husbandry.breed_all_animals.description": "Bütün heyvanları ç<PERSON>alt!", "advancements.husbandry.breed_all_animals.title": "İki-iki", "advancements.husbandry.breed_an_animal.description": "İki heyvan bir yerdə ç<PERSON>aldın", "advancements.husbandry.breed_an_animal.title": "Tutuquşular və yarasalar", "advancements.husbandry.complete_catalogue.description": "Bütün Pişik variantlarını əhliləşdirin!", "advancements.husbandry.complete_catalogue.title": "Tamamlanmış kataloq", "advancements.husbandry.feed_snifflet.description": "Snifflet qidalandırın", "advancements.husbandry.feed_snifflet.title": "Balaca <PERSON>lə<PERSON>", "advancements.husbandry.fishy_business.description": "Bir balıq tut", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>q i<PERSON>i", "advancements.husbandry.froglights.description": "Bütün <PERSON>alar inventarınızda olsun", "advancements.husbandry.froglights.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON>ümü<PERSON> ilə!", "advancements.husbandry.kill_axolotl_target.description": "Aks<PERSON>tl ilə komanda ol və döyüş qazan", "advancements.husbandry.kill_axolotl_target.title": "Dostluğun sağaldıcı gücü!", "advancements.husbandry.leash_all_frog_variants.description": "Bütün Qurbağa variantlarını Yəhərə keçirin", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON><PERSON> harada, dirilik orada", "advancements.husbandry.make_a_sign_glow.description": "Bir lövhə yazısını parlat", "advancements.husbandry.make_a_sign_glow.title": "Parlat və izlə!", "advancements.husbandry.netherite_hoe.description": "Çapanı təkmilləşdirmək üçün Kürdəmit Külçəsindən istifadə edin və sonra həyat seçimlərinizi yenidən qiymətləndirin", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON>di həsretmə", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON><PERSON> Yumurtası əldə edin", "advancements.husbandry.obtain_sniffer_egg.title": "Maraqlı Qoxular", "advancements.husbandry.place_dried_ghast_in_water.description": "<PERSON><PERSON> blo<PERSON>nu suya qoy", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON> qalma!", "advancements.husbandry.plant_any_sniffer_seed.description": "İstənilən Sniffer toxumu ək", "advancements.husbandry.plant_any_sniffer_seed.title": "Keçmişin <PERSON>", "advancements.husbandry.plant_seed.description": "Toxum ək və böyüməyinə tamaşa et", "advancements.husbandry.plant_seed.title": "Toxumlu yer", "advancements.husbandry.remove_wolf_armor.description": "Снимите Волчью броню с Волка с помощью ножниц", "advancements.husbandry.remove_wolf_armor.title": "Острый интеллект", "advancements.husbandry.repair_wolf_armor.description": "Armadillo Qabəğı istifadə edərək zədələnmiş Canavar Zirehini təmir edin", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON>", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Qayıqda Bir keçi İlə Üz", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON>ə<PERSON><PERSON>ə oturub keçi ilə da<PERSON>!", "advancements.husbandry.root.description": "<PERSON><PERSON><PERSON>la və qidalarla dolu", "advancements.husbandry.root.title": "Əkinçilik və Heyvandarlıq", "advancements.husbandry.safely_harvest_honey.description": "Arıları şiddətləndirmədən şüşə butulkadan istifadə edərək arıdan bal toplamaq üçün Kamp atəşi istifadə edin", "advancements.husbandry.safely_harvest_honey.title": "QonağımıZzz olun", "advancements.husbandry.silk_touch_nest.description": "İpək Toxunuşundan istifadə edərək içində 3 ədəd arı olan bir arı yuvasını köçürün", "advancements.husbandry.silk_touch_nest.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tactical_fishing.description": "Balıq Tut... Qarmaqsız", "advancements.husbandry.tactical_fishing.title": "Taktiki Balıqçılıq", "advancements.husbandry.tadpole_in_a_bucket.description": "Bir vedrədə bir tadpole tutun", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "Bir heyvanı evcilləşdir", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON> Qədər Birlikdə", "advancements.husbandry.wax_off.description": "Mis blokundan mumu çıxarın!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Mis blokuna bal pətəyi tətbiq et!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON>ından bi<PERSON> əhliləşdir<PERSON>", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dəstə", "advancements.nether.all_effects.description": "<PERSON><PERSON><PERSON> anda bütün təsirlərə sahib ol", "advancements.nether.all_effects.title": "Buraya Necə Gəldik?", "advancements.nether.all_potions.description": "<PERSON><PERSON><PERSON> anda bütün eliksir təsirlərinə sahib ol", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kokteyl", "advancements.nether.brew_potion.description": "<PERSON>ir iksir də<PERSON>ləyin", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "Respaun ankerini maksimum səviyyəsində yüklə", "advancements.nether.charge_respawn_anchor.title": "\"Doqquz\" can da bəs etmir", "advancements.nether.create_beacon.description": "Fənər hazırla və herləştir", "advancements.nether.create_beacon.title": "<PERSON>ə<PERSON><PERSON>ri evə gətir", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON><PERSON> tam gücə gətir", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "Piqlinlərin başını qızılla aldat", "advancements.nether.distract_piglin.title": "Oo, parlaq", "advancements.nether.explore_nether.description": "Bütün Nezer biomlarını kəşf et", "advancements.nether.explore_nether.title": "Qaynar turist nöqtələri", "advancements.nether.fast_travel.description": "Dünyada 7 km səyahət etmək üçün Nezerdən istifadə et", "advancements.nether.fast_travel.title": "Yarıboşluq köpüyü", "advancements.nether.find_bastion.description": "<PERSON><PERSON><PERSON> daxil ol", "advancements.nether.find_bastion.title": "<PERSON><PERSON>, keç<PERSON>ş günlər", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON><PERSON> Nezer qalasına tərəf salın", "advancements.nether.find_fortress.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qala", "advancements.nether.get_wither_skull.description": "Vizer skeleti kəlləsi əldə et", "advancements.nether.get_wither_skull.title": "Qorxunc və vahiməli skelet", "advancements.nether.loot_bastion.description": "Bastion qalıqlarında bir sandığı yağmalayın", "advancements.nether.loot_bastion.title": "Müharibə donuzları", "advancements.nether.netherite_armor.description": "<PERSON>ü<PERSON>", "advancements.nether.netherite_armor.title": "<PERSON><PERSON>ni qırıntılarla ört", "advancements.nether.obtain_ancient_debris.description": "Antik qırıntı tap", "advancements.nether.obtain_ancient_debris.title": "D<PERSON><PERSON><PERSON><PERSON>ərdə gizlənmiş", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON><PERSON>u əldə et", "advancements.nether.obtain_blaze_rod.title": "Alovların İ<PERSON>inə", "advancements.nether.obtain_crying_obsidian.description": "Ağlayan obsidian tap", "advancements.nether.obtain_crying_obsidian.title": "So<PERSON><PERSON><PERSON><PERSON> kəsən kimdir?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON> G<PERSON><PERSON>'i alov topu ilə məhv et", "advancements.nether.return_to_sender.title": "Gö<PERSON>ə<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.ride_strider.description": "Çubuqda əyri göbələk ilə <PERSON>der sür", "advancements.nether.ride_strider.title": "<PERSON>u qayığın aya<PERSON>ları var", "advancements.nether.ride_strider_in_overworld_lava.description": "Bir strayder götür və Dünyadakı bir lava gölü üzərində uzuuuun gəzintiyə çıx", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON><PERSON> kimi", "advancements.nether.root.description": "<PERSON><PERSON> pal<PERSON>ını gətir", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Vizer çağır", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON> təpələr", "advancements.nether.uneasy_alliance.description": "Nezerdən qast xilas et, onu Dünyaya gətir... və sonra onu öldür", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON>", "advancements.nether.use_lodestone.description": "Lodestone üzərində kompasdan istifadə edin", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> yolla gedən yorulmaz", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Bir zombi kəndlini zəiflət və onu sağalt", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Qalxanla mərmi ya<PERSON>ı<PERSON>ı<PERSON>ın", "advancements.story.deflect_arrow.title": "<PERSON><PERSON><PERSON><PERSON> yox, sa<PERSON> ol", "advancements.story.enchant_item.description": "Sehrba<PERSON><PERSON><PERSON>q masasında bir əşyanı sehrlə", "advancements.story.enchant_item.title": "Sehrbaz", "advancements.story.enter_the_end.description": "<PERSON> <PERSON><PERSON> daxil olun", "advancements.story.enter_the_end.title": "Son?", "advancements.story.enter_the_nether.description": "Kürdə<PERSON>uru<PERSON>, işıqlandırın və daxil olun", "advancements.story.enter_the_nether.title": "Daha Dərinə Enməmiz Gərə<PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> təqib et", "advancements.story.follow_ender_eye.title": "Göz agenti", "advancements.story.form_obsidian.description": "Obsidian bloku əldə et", "advancements.story.form_obsidian.title": "Buz vedrəsi yarışı", "advancements.story.iron_tools.description": "Qazmanı təkmilləşdir", "advancements.story.iron_tools.title": "Bu dəmir qazma deyil ki?", "advancements.story.lava_bucket.description": "Vedrəyə lava doldur", "advancements.story.lava_bucket.title": "<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.description": "Almaz tədarük et", "advancements.story.mine_diamond.title": "Almazlar!", "advancements.story.mine_stone.description": "Yeni Külüng ilə Daşı Qaz", "advancements.story.mine_stone.title": "Daş Dövrü", "advancements.story.obtain_armor.description": "<PERSON>ir dəmir zireh hissə<PERSON> hazırla", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON>", "advancements.story.root.description": "Oyunun ürəyi və hekayəsi", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Bir almaz zireh hissəsi hazırla", "advancements.story.shiny_gear.title": "<PERSON><PERSON>ni alma<PERSON>", "advancements.story.smelt_iron.description": "<PERSON><PERSON><PERSON><PERSON> ərit", "advancements.story.smelt_iron.title": "T<PERSON>chizat Tədarük Et", "advancements.story.upgrade_tools.description": "Daha Yaxşı Külüng Hazırla", "advancements.story.upgrade_tools.title": "Təkmilləşdirmə", "advancements.toast.challenge": "Mücadilə Tamamlandı!", "advancements.toast.goal": "Məqsədə çatıldı!", "advancements.toast.task": "İnkişaf edildi!", "argument.anchor.invalid": "Etibarsız bir təşkilat lövbər mövqeyi %s", "argument.angle.incomplete": "Tamamlanmamış (gözlənilən 1 koordinat)", "argument.angle.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bucaq", "argument.block.id.invalid": "Naməlum rəng '%s'", "argument.block.property.duplicate": "'%s' mülkiyyəti %s bloku üçün yalnız bir dəfə təyin edilə bilər", "argument.block.property.invalid": "Blok %s əm<PERSON> '%s' qəbul etmir", "argument.block.property.novalue": "%s blokunda '%s' əmlakının gözlənilən dəyəri", "argument.block.property.unclosed": "Blok dövlət xüsusiyyətləri üçün gözlənilən bağlama]", "argument.block.property.unknown": "Blokda %s '%s' mülkiy<PERSON><PERSON><PERSON> yoxdur", "argument.block.tag.disallowed": "Burada teqlərə icazə verilmir, yalnız faktiki əşyalar", "argument.color.invalid": "Naməlum rəng '%s'", "argument.component.invalid": "Etibarsız çat komponenti: %s", "argument.criteria.invalid": "<PERSON><PERSON><PERSON> meyar '%s'", "argument.dimension.invalid": "Naməlum ölçü '%s'", "argument.double.big": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.double.low": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.entity.invalid": "Yanlış ad və ya UUID", "argument.entity.notfound.entity": "<PERSON><PERSON> bir müəssi<PERSON>ə tapılmadı", "argument.entity.notfound.player": "Heç bir oyunçu tapılmadı", "argument.entity.options.advancements.description": "İnkişafı olan oyunçular", "argument.entity.options.distance.description": "M<PERSON><PERSON><PERSON>səyə olan məsafə", "argument.entity.options.distance.negative": "<PERSON>ə<PERSON><PERSON>ə mənfi ola bi<PERSON>z", "argument.entity.options.dx.description": "X və x + dx arasındakı varlıqlar", "argument.entity.options.dy.description": "X və x + dx arasındakı varlıqlar", "argument.entity.options.dz.description": "X və x + dx arasındakı varlıqlar", "argument.entity.options.gamemode.description": "<PERSON>yun rejimi ilə oyunçular", "argument.entity.options.inapplicable": "'%s' seç<PERSON>i burada tətbiq edilmir", "argument.entity.options.level.description": "Tə<PERSON>r<PERSON><PERSON>ə əldə edildi", "argument.entity.options.level.negative": "Səvi<PERSON><PERSON>ə mənfi <PERSON>", "argument.entity.options.limit.description": "Qayıdacaq qurumların maksimum sayı", "argument.entity.options.limit.toosmall": "Limit ən azı 1 olmalıdır", "argument.entity.options.mode.invalid": "Yanlış və ya naməlum sort növü '%s'", "argument.entity.options.name.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adı", "argument.entity.options.nbt.description": "<PERSON><PERSON><PERSON><PERSON> şə<PERSON>lər", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON><PERSON> predikat", "argument.entity.options.scores.description": "<PERSON><PERSON><PERSON><PERSON> şə<PERSON>lər", "argument.entity.options.sort.description": "Varlıqları sırala", "argument.entity.options.sort.irreversible": "Yanlış və ya naməlum sort növü '%s'", "argument.entity.options.tag.description": "Etiketli Varlıqlar", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lı<PERSON>", "argument.entity.options.type.description": "Varlığın T<PERSON>", "argument.entity.options.type.invalid": "Yanlış və ya naməlum sort növü '%s'", "argument.entity.options.unknown": "<PERSON><PERSON><PERSON> seçim '%s'", "argument.entity.options.unterminated": "Seç<PERSON><PERSON><PERSON><PERSON> gözlənilən sonu", "argument.entity.options.valueless": "'%s' seç<PERSON>i üçün gözlə<PERSON>lən dəyər", "argument.entity.options.x.description": "x mövqeyi", "argument.entity.options.x_rotation.description": "Varlığın x dönm<PERSON>si", "argument.entity.options.y.description": "y mövqeyi", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> x dönməsi", "argument.entity.options.z.description": "z mövqeyi", "argument.entity.selector.allEntities": "<PERSON>ü<PERSON>ün <PERSON>", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON><PERSON>n <PERSON>", "argument.entity.selector.missing": "Selektor növü yoxdur", "argument.entity.selector.nearestEntity": "<PERSON>n yaxın varlıq", "argument.entity.selector.nearestPlayer": "<PERSON>n yaxın o<PERSON>", "argument.entity.selector.not_allowed": "Seçiciyə icazə verilmir", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.self": "<PERSON><PERSON><PERSON><PERSON><PERSON> müə<PERSON>ə", "argument.entity.selector.unknown": "Naməlum rəng '%s'", "argument.entity.toomany": "Yalnız bir müəssisəyə icazə verilir, ancaq təmin edilmiş selektor birdən çoxuna icazə verir", "argument.enum.invalid": "Yanlış dəyər \"%s\"", "argument.float.big": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.float.low": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.gamemode.invalid": "<PERSON><PERSON><PERSON> oyun rejimi: %s", "argument.hexcolor.invalid": "Yanlış hex rəng kodu \"%s\"", "argument.id.invalid": "Keçərsiz UUID", "argument.id.unknown": "Nam<PERSON>lum şəxs nömrəsi: %s", "argument.integer.big": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.integer.low": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.item.id.invalid": "Naməlum element '%s'", "argument.item.tag.disallowed": "Burada teqlərə icazə verilmir, yalnız faktiki əşyalar", "argument.literal.incorrect": "Gözlənilən hərfi %s", "argument.long.big": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.long.low": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.message.too_long": "Söhbət mesajı çox uzun idi (%s > maksimum %s simvol)", "argument.nbt.array.invalid": "Yanlış sıra növü '%s'", "argument.nbt.array.mixed": "%s siyahısına %s daxil etmək olmur", "argument.nbt.expected.compound": "<PERSON><PERSON><PERSON><PERSON> et<PERSON>", "argument.nbt.expected.key": "Gözlə<PERSON><PERSON>ə<PERSON> a<PERSON>r", "argument.nbt.expected.value": "Gö<PERSON><PERSON><PERSON><PERSON><PERSON>ən dəyər", "argument.nbt.list.mixed": "%s siyahısına %s daxil etmək olmur", "argument.nbt.trailing": "Gözlənilməyən izləmə məlumatları", "argument.player.entities": "Bu ə<PERSON>r yalnız oyunçulara təsir göstərə bilər, ancaq təqdim olunan seçiciyə müəssisələr daxildir", "argument.player.toomany": "Yalnız bir müəssisəyə icazə verilir, ancaq təmin edilmiş selektor birdən çoxuna icazə verir", "argument.player.unknown": "O oyunçu yoxdur", "argument.pos.missing.double": "Bir koordinat gözlənilir", "argument.pos.missing.int": "Bir blok mövqeyi gözlə<PERSON>lir", "argument.pos.mixed": "Dünya və yerli koordinatları qarışdırmaq olmur (hər şey ^ istifadə etməlidir ya da istifadə etməməlidir)", "argument.pos.outofbounds": "Bu vəziyyət icazə verilən sərhədlərin xaricindədir.", "argument.pos.outofworld": "Bu mövqe bu dünyadan xari<PERSON>!", "argument.pos.unloaded": "<PERSON>u mövqe yü<PERSON>", "argument.pos2d.incomplete": "Itkin (2 koordinat gözlənilir)", "argument.pos3d.incomplete": "Tamamlanmamış (gözlənilən 3 koordinat)", "argument.range.empty": "Gözlə<PERSON><PERSON>ən dəyər və ya dəyərlər aralığı", "argument.range.ints": "<PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>, yalnız rəqəmlərə icazə verilir", "argument.range.swapped": "<PERSON><PERSON><PERSON><PERSON><PERSON>dan b<PERSON><PERSON><PERSON>k ola bi<PERSON>z", "argument.resource.invalid_type": "'%s' teqində səhv '%s' növü var (gözlənilən '%s')", "argument.resource.not_found": "'%s' tipli '%s' teqini tapmaq mü<PERSON><PERSON><PERSON>l", "argument.resource_or_id.failed_to_parse": "Strukturu təhlil etmək alınmadı: %s", "argument.resource_or_id.invalid": "Yanlış ID və ya etiket", "argument.resource_or_id.no_such_element": "“%s” reyestrində “%s” adlı element tapılmamışdır", "argument.resource_selector.not_found": "'%s' seçicisindən '%s' növdən bərabərlik tapılmadı", "argument.resource_tag.invalid_type": "'%s' teqində səhv '%s' növü var (gözlənilən '%s')", "argument.resource_tag.not_found": "'%s' tipli '%s' teqini tapmaq mü<PERSON><PERSON><PERSON>l", "argument.rotation.incomplete": "Tamamlanmamış (gözlənilən 2 koordinat)", "argument.scoreHolder.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.scoreboardDisplaySlot.invalid": "'%s' deyə bir gör<PERSON><PERSON><PERSON> slotu yoxdur", "argument.style.invalid": "Yanlış üslub: %s", "argument.time.invalid_tick_count": "Tick sayısı negatif <PERSON>", "argument.time.invalid_unit": "Etibarsız vahid", "argument.time.tick_count_too_low": "İkiqat %s - dən az olmamalıdır, %s tapıldı", "argument.uuid.invalid": "Keçərsiz UUID", "argument.waypoint.invalid": "Seçilmiş obyekt bir istiqamət nöqtəsi deyil", "arguments.block.tag.unknown": "Naməlum rəng '%s'", "arguments.function.tag.unknown": "<PERSON>ə<PERSON> funksiya '%s'", "arguments.function.unknown": "Naməlum funksiya '%s", "arguments.item.component.expected": "Gözlənilən element komponenti", "arguments.item.component.malformed": "Səhv formalaşmış '%s' komponenti: '%s'", "arguments.item.component.repeated": "Maddə komponenti '%s' təkrar olu<PERSON>, amma yalnız bir dəyər müəyyən etmək olar", "arguments.item.component.unknown": "<PERSON><PERSON><PERSON>l maddə komponenti '%s'", "arguments.item.malformed": "Səhv maddə: '%s'", "arguments.item.overstacked": "%s yalnız %s qədər yığa bilər", "arguments.item.predicate.malformed": "Səhv formalaşmış '%s' predikatı: '%s'", "arguments.item.predicate.unknown": "Naməlum element şərti '%s'", "arguments.item.tag.unknown": "Naməlum element etiketi '%s'", "arguments.nbtpath.node.invalid": "Yanlış NBT yol elementi", "arguments.nbtpath.nothing_found": "%s yə uyğun element tapılmadı", "arguments.nbtpath.too_deep": "Nəticədə NBT çox dərin yuvalanmışdır", "arguments.nbtpath.too_large": "Nəticədə NBT çox dərin yuvalanmışdır", "arguments.objective.notFound": "Inməyən tablonun hədəfi '%s'", "arguments.objective.readonly": "Coreboard məqsədi '%s' yalnız oxunur", "arguments.operation.div0": "<PERSON><PERSON><PERSON><PERSON>ra bölmək olmur", "arguments.operation.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments.swizzle.invalid": "Yanlış qarışıqlıq, gözlənilən birləşmə 'x', 'y' və 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%%%s %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%%%s %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%%%s %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Zireh", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_knockback": "Hü<PERSON>lu itələmə", "attribute.name.attack_speed": "<PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.block_interaction_range": "Blokla qarşılıqlı təsir", "attribute.name.burning_time": "Yanma Vaxtı", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON>", "attribute.name.entity_interaction_range": "Varlıq İnteraksiya Diapazonu", "attribute.name.explosion_knockback_resistance": "Partlayışdan <PERSON>", "attribute.name.fall_damage_multiplier": "<PERSON><PERSON><PERSON><PERSON><PERSON> Multiplikatoru", "attribute.name.flying_speed": "Uçuş Sürəti", "attribute.name.follow_range": "Canlının <PERSON> Diapazonu", "attribute.name.generic.armor": "Zireh", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_knockback": "Hü<PERSON>lu itələmə", "attribute.name.generic.attack_speed": "<PERSON><PERSON><PERSON>", "attribute.name.generic.block_interaction_range": "Blok Qarşılıqlı Məsafə", "attribute.name.generic.burning_time": "Yanma vaxtı", "attribute.name.generic.entity_interaction_range": "<PERSON>byektin qarşılıqlı əlaqə məsafəsi", "attribute.name.generic.explosion_knockback_resistance": "Partlayışa qarşı sürüşmə müqaviməti", "attribute.name.generic.fall_damage_multiplier": "Düşmə Zərər Multiplikatoru", "attribute.name.generic.flying_speed": "<PERSON><PERSON><PERSON>", "attribute.name.generic.follow_range": "Canlı Təqib Mənzili", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "<PERSON>lam<PERSON> Gü<PERSON>", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Effektivliyi", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "Oksigen Bonusu", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mə<PERSON>fə", "attribute.name.generic.scale": "Ölçək", "attribute.name.generic.step_height": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.water_movement_efficiency": "Suda hərə<PERSON><PERSON><PERSON> effekt<PERSON>yi", "attribute.name.gravity": "Qravitasiya", "attribute.name.horse.jump_strength": "At Tullanma Qüvvəti", "attribute.name.jump_strength": "<PERSON>llan<PERSON><PERSON>", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.luck": "Şans", "attribute.name.max_absorption": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.max_health": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "<PERSON><PERSON><PERSON> sə<PERSON>ə<PERSON>", "attribute.name.movement_efficiency": "Hərə<PERSON><PERSON>t səmərə<PERSON><PERSON>yi", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "Oksigen Bonusu", "attribute.name.player.block_break_speed": "Blok Qır<PERSON><PERSON>", "attribute.name.player.block_interaction_range": "Blok Qarşılıqlı Məsafə", "attribute.name.player.entity_interaction_range": "<PERSON>byektin qarşılıqlı əlaqə məsafəsi", "attribute.name.player.mining_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "Eğilərkən Hərəkət <PERSON>ü<PERSON>ə<PERSON>", "attribute.name.player.submerged_mining_speed": "Sualtı qazma sürəti", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "<PERSON><PERSON><PERSON><PERSON> zamanı hərəkət sürəti", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON> gü<PERSON>lə<PERSON>", "attribute.name.step_height": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.submerged_mining_speed": "Sualtı hasilat sü<PERSON>ti", "attribute.name.sweeping_damage_ratio": "Sarsıdıcı zərbə əmsalı", "attribute.name.tempt_range": "Mobun cəlbetmə məsafəsi", "attribute.name.water_movement_efficiency": "Suda hərəkət səmərə<PERSON><PERSON>yi", "attribute.name.waypoint_receive_range": "İstiqamət <PERSON>öq<PERSON>", "attribute.name.waypoint_transmit_range": "İstiqamət Nöqt<PERSON>", "attribute.name.zombie.spawn_reinforcements": "Zombi Çağrılması", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "Bambuk cəngəlliyi", "biome.minecraft.basalt_deltas": "Bazalt deltası", "biome.minecraft.beach": "Çimərlik", "biome.minecraft.birch_forest": "<PERSON>", "biome.minecraft.cherry_grove": "Albalı bağı", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "Al meşə", "biome.minecraft.dark_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON>şə", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "Ç<PERSON>l", "biome.minecraft.dripstone_caves": "Damcı daşı mağaraları", "biome.minecraft.end_barrens": "Barrensi bitir", "biome.minecraft.end_highlands": "<PERSON>", "biome.minecraft.end_midlands": "<PERSON>", "biome.minecraft.eroded_badlands": "Aşınmış Badlands", "biome.minecraft.flower_forest": "Çiçək meşə<PERSON>", "biome.minecraft.forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON> zirvələr", "biome.minecraft.frozen_river": "Dondurulmuş çay", "biome.minecraft.grove": "Ağaclıq", "biome.minecraft.ice_spikes": "Buz tırmanışları", "biome.minecraft.jagged_peaks": "Çıxıntılı zirvələr", "biome.minecraft.jungle": "<PERSON><PERSON>ng<PERSON><PERSON>k", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "Çəmənlik", "biome.minecraft.mushroom_fields": "Göbələk tarlaları", "biome.minecraft.nether_wastes": "<PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Qoca böyümüş ağcaqayın meşəsi", "biome.minecraft.old_growth_pine_taiga": "Qoca şam tayqası", "biome.minecraft.old_growth_spruce_taiga": "Qoca küknar tayqası", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON> bağ", "biome.minecraft.plains": "Düzənliklər", "biome.minecraft.river": "Çay", "biome.minecraft.savanna": "Savanna", "biome.minecraft.savanna_plateau": "Savanna <PERSON>", "biome.minecraft.small_end_islands": "Kiçik Son Adaları", "biome.minecraft.snowy_beach": "Qarlı çimərlik", "biome.minecraft.snowy_plains": "Qarlı düzənliklər", "biome.minecraft.snowy_slopes": "Qarlı yamaclar", "biome.minecraft.snowy_taiga": "Qarlı Taiga", "biome.minecraft.soul_sand_valley": "<PERSON>uh qumu vadisi", "biome.minecraft.sparse_jungle": "Seyr<PERSON>k cəngəllik", "biome.minecraft.stony_peaks": "Daşlı zirvələr", "biome.minecraft.stony_shore": "Daşlı sahil", "biome.minecraft.sunflower_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>ə<PERSON>", "biome.minecraft.swamp": "Bataqlıq", "biome.minecraft.taiga": "Tayqa", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "Boşluq", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON> meşə", "biome.minecraft.windswept_forest": "K<PERSON><PERSON><PERSON><PERSON><PERSON> meşə", "biome.minecraft.windswept_gravelly_hills": "K<PERSON><PERSON><PERSON><PERSON><PERSON> çınqıllı təpələr", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> təpələr", "biome.minecraft.windswept_savanna": "Küləkli savanna", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bedlends", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON>ə<PERSON>", "block.minecraft.acacia_fence_gate": "Akasiya Çəpər Qapısı", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON>ə<PERSON>", "block.minecraft.acacia_leaves": "Akas<PERSON> Yarpaqları", "block.minecraft.acacia_log": "<PERSON><PERSON><PERSON>ü<PERSON>", "block.minecraft.acacia_planks": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "<PERSON>kas<PERSON> Təyziq <PERSON>ə<PERSON>", "block.minecraft.acacia_sapling": "Akasiya Fidanı", "block.minecraft.acacia_sign": "Akasiya Lövhə", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "<PERSON><PERSON><PERSON>ə<PERSON>", "block.minecraft.acacia_trapdoor": "Akasiya Tələ Qapısı", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON>ə<PERSON>", "block.minecraft.acacia_wall_sign": "Divarda Akasiya Lövhə", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.activator_rail": "Aktivləyiçi Rels", "block.minecraft.air": "<PERSON><PERSON>", "block.minecraft.allium": "<PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametist bloku", "block.minecraft.amethyst_cluster": "Ametist salxımı", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON><PERSON> qalıq", "block.minecraft.andesite": "Andezit", "block.minecraft.andesite_slab": "<PERSON><PERSON><PERSON> pillə", "block.minecraft.andesite_stairs": "Andezit Pilləkən", "block.minecraft.andesite_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.anvil": "Dəmirçi Zindanı", "block.minecraft.attached_melon_stem": "Bağlı Qarpız Sapı", "block.minecraft.attached_pumpkin_stem": "Bağlı balqabaq Sapı", "block.minecraft.azalea": "Azaliya", "block.minecraft.azalea_leaves": "Azaliya yarpaqları", "block.minecraft.azure_bluet": "Hüstoniya", "block.minecraft.bamboo": "Bambuk", "block.minecraft.bamboo_block": "Bambuk bloku", "block.minecraft.bamboo_button": "Bambuk Düymə", "block.minecraft.bamboo_door": "Bambuk qapı", "block.minecraft.bamboo_fence": "Bambuk Çəpər", "block.minecraft.bamboo_fence_gate": "Bambuk Çəpər Qapısı", "block.minecraft.bamboo_hanging_sign": "Bambuk Asma İşarəsi", "block.minecraft.bamboo_mosaic": "Bambuk mozaika", "block.minecraft.bamboo_mosaic_slab": "Bambuk mozaika plitəsi", "block.minecraft.bamboo_mosaic_stairs": "Bambuk mozaika <PERSON>əri", "block.minecraft.bamboo_planks": "Bambuk taxtalar", "block.minecraft.bamboo_pressure_plate": "Bambuk təzyiq lö<PERSON>", "block.minecraft.bamboo_sapling": "Bambuk fidanı", "block.minecraft.bamboo_sign": "Bambuk işarəsi", "block.minecraft.bamboo_slab": "Bambuk Plitələr", "block.minecraft.bamboo_stairs": "Bambuk pillə<PERSON>ənlər", "block.minecraft.bamboo_trapdoor": "Bambukdan tələ qapısı", "block.minecraft.bamboo_wall_hanging_sign": "Bambuk Divardan <PERSON>", "block.minecraft.bamboo_wall_sign": "Bambuk Divar İşarəsi", "block.minecraft.banner.base.black": "Tam qara sahə", "block.minecraft.banner.base.blue": "<PERSON> göy sahə", "block.minecraft.banner.base.brown": "Tam qəhvəyi sahə", "block.minecraft.banner.base.cyan": "Tam firuzəyi sahə", "block.minecraft.banner.base.gray": "<PERSON> boz sahə", "block.minecraft.banner.base.green": "<PERSON> yaşıl sahə", "block.minecraft.banner.base.light_blue": "<PERSON> açıq göy sahə", "block.minecraft.banner.base.light_gray": "<PERSON> açıq boz sahə", "block.minecraft.banner.base.lime": "<PERSON> açıq yaşıl sahə", "block.minecraft.banner.base.magenta": "<PERSON> macenta sahə", "block.minecraft.banner.base.orange": "Tam narıncı sahə", "block.minecraft.banner.base.pink": "Tam çəhrayı sahə", "block.minecraft.banner.base.purple": "Tam bənövşəyi sahə", "block.minecraft.banner.base.red": "Tam qırmızı sahə", "block.minecraft.banner.base.white": "<PERSON> ağ sahə", "block.minecraft.banner.base.yellow": "Tam sarı sahə", "block.minecraft.banner.border.black": "Qara Çərçivə", "block.minecraft.banner.border.blue": "G<PERSON>y Çərçivə", "block.minecraft.banner.border.brown": "Qəh<PERSON><PERSON><PERSON> Çərçivə", "block.minecraft.banner.border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.border.gray": "Boz Çə<PERSON>ç<PERSON>ə", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON>q <PERSON> Çərçivə", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "Magenta <PERSON>çivə", "block.minecraft.banner.border.orange": "Narıncı Çərçivə", "block.minecraft.banner.border.pink": "Çəhrayı Çərçivə", "block.minecraft.banner.border.purple": "Bənövşəyi Çərçivə", "block.minecraft.banner.border.red": "Qırmızı Çərçivə", "block.minecraft.banner.border.white": "<PERSON><PERSON>", "block.minecraft.banner.border.yellow": "Sarı Çərçivə", "block.minecraft.banner.bricks.black": "Qara <PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "Açık Göy Kərpic Desəni", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.orange": "Narınc<PERSON>ərp<PERSON>", "block.minecraft.banner.bricks.pink": "Çəhrayı Kərpic <PERSON>ni", "block.minecraft.banner.bricks.purple": "Bənövş<PERSON>yi Kərp<PERSON>", "block.minecraft.banner.bricks.red": "Qırmızı Kərpic <PERSON>ni", "block.minecraft.banner.bricks.white": "<PERSON><PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.black": "Qara Dairə", "block.minecraft.banner.circle.blue": "Göy Dairə", "block.minecraft.banner.circle.brown": "Qəhvəyi Dairə", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "Boz Dairə", "block.minecraft.banner.circle.green": "Ya<PERSON><PERSON>l Dairə", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON>q <PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "Magenta Dairə", "block.minecraft.banner.circle.orange": "Narıncı Dairə", "block.minecraft.banner.circle.pink": "Çəhrayı Dairə", "block.minecraft.banner.circle.purple": "Bənövşəyi Dairə", "block.minecraft.banner.circle.red": "Qırmızı Dairə", "block.minecraft.banner.circle.white": "<PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "Sarı Dairə", "block.minecraft.banner.creeper.black": "Qara Creeper Şəkli", "block.minecraft.banner.creeper.blue": "Göy Creeper Şəkli", "block.minecraft.banner.creeper.brown": "Qəhvəyi Creeper Şəkli", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "Boz Creeper Şəkli", "block.minecraft.banner.creeper.green": "Yaşıl Creeper Şəkli", "block.minecraft.banner.creeper.light_blue": "Açıq Göy Creeper Şəkli", "block.minecraft.banner.creeper.light_gray": "Açıq Boz Creeper Şəkli", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON>q <PERSON>ıl Creeper Şəkli", "block.minecraft.banner.creeper.magenta": "Magenta Creeper Şəkli", "block.minecraft.banner.creeper.orange": "Narıncı Creeper Şəkli", "block.minecraft.banner.creeper.pink": "Çəhrayı Creeper Şəkli", "block.minecraft.banner.creeper.purple": "Bənövşəyi Creeper Şəkli", "block.minecraft.banner.creeper.red": "Qırmızı Creeper Şəkli", "block.minecraft.banner.creeper.white": "Ağ Creeper Şəkli", "block.minecraft.banner.creeper.yellow": "Sarı Creeper Şəkli", "block.minecraft.banner.cross.black": "Qara Çarpı", "block.minecraft.banner.cross.blue": "Göy Çarpı", "block.minecraft.banner.cross.brown": "Qəhvəyi Çarpı", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Boz Çarpı", "block.minecraft.banner.cross.green": "Yaşıl Çarpı", "block.minecraft.banner.cross.light_blue": "Açıq Göy Çarpı", "block.minecraft.banner.cross.light_gray": "Açıq Boz Çarpı", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON>q <PERSON> Çarpı", "block.minecraft.banner.cross.magenta": "Magenta Çarpı", "block.minecraft.banner.cross.orange": "Narıncı Çarpı", "block.minecraft.banner.cross.pink": "Çəhrayı Çarpı", "block.minecraft.banner.cross.purple": "Bənövşəyi Çarpı", "block.minecraft.banner.cross.red": "Qırmızı Çarpı", "block.minecraft.banner.cross.white": "Ağ Çarpı", "block.minecraft.banner.cross.yellow": "Sarı Çarpı", "block.minecraft.banner.curly_border.black": "Qara Mişar Dişi Çərçivə", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> Mişar <PERSON> Ç<PERSON>ç<PERSON>", "block.minecraft.banner.curly_border.brown": "Qəh<PERSON><PERSON><PERSON> Mişar <PERSON> Çərçivə", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.gray": "Boz Mişar Dişi Ç<PERSON>çivə", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON> Çərçivə", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON>q <PERSON>ö<PERSON> Mişar Dişi Çərçivə", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON>q <PERSON>ar Dişi Çərçivə", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>ar Dişi Çərçivə", "block.minecraft.banner.curly_border.magenta": "Magenta Mişar Diş<PERSON> Ç<PERSON>ç<PERSON>ə", "block.minecraft.banner.curly_border.orange": "Narıncı Mişar Dişi Çərçivə", "block.minecraft.banner.curly_border.pink": "Çəhrayı Mişar Dişi Çərçivə", "block.minecraft.banner.curly_border.purple": "Bənövşəyi Mişar Dişi Çərçivə", "block.minecraft.banner.curly_border.red": "Qırmızı Mişar Dişi Çərçivə", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON> Dişi Çərçivə", "block.minecraft.banner.curly_border.yellow": "Sarı Mişar Dişi Çərçivə", "block.minecraft.banner.diagonal_left.black": "Qara Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.blue": "Göy Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.brown": "Qəhvəyi Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON> Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.gray": "Boz Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.green": "Yaşıl Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.light_blue": "Açıq Göy Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.light_gray": "Açıq Boz Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.lime": "Açıq Yaşıl Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.magenta": "Magenta Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.orange": "Narıncı Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.pink": "Çəhrayı Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.purple": "Bənövşəyi Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.red": "Qırmızı Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.white": "Ağ Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_left.yellow": "Sarı Sol Üst Üçbucaq", "block.minecraft.banner.diagonal_right.black": "Qara Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.blue": "Göy Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.brown": "Qəhvəyi Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.cyan": "Mavi Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.gray": "Boz Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.green": "Yaşıl Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.light_blue": "Açıq Göy Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.light_gray": "Açıq Boz Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.lime": "Açıq Yaşıl Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.magenta": "Magenta Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.orange": "Narıncı Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.pink": "Çəhrayı Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.purple": "Bənövşəyi Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.red": "Qırmızı Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.white": "Ağ Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_right.yellow": "Sarı Sağ Üst Üçbucaq", "block.minecraft.banner.diagonal_up_left.black": "Qara Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.blue": "Göy Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.brown": "Qəhvəyi Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON> Üçbucaq", "block.minecraft.banner.diagonal_up_left.gray": "Boz Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.green": "Yaşıl Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.light_blue": "Açıq Göy Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.light_gray": "Açıq Boz Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.lime": "Açıq Yaşıl Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.orange": "Narıncı Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.pink": "Çəhrayı Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.purple": "Bənövşəyi Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.red": "Qırmızı Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.white": "Ağ Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_left.yellow": "Sarı Sol Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.black": "Qara Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.blue": "Göy Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.brown": "Qəhvəyi Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.cyan": "Mavi Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.gray": "Boz Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.green": "Yaşıl Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.light_blue": "Açıq Göy Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.light_gray": "Açıq Boz Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.lime": "Açıq Yaşıl Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.orange": "Narıncı Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.pink": "Çəhrayı Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.purple": "Bənövşəyi Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.red": "Qırmızı Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.white": "Ağ Sağ Alt Üçbucaq", "block.minecraft.banner.diagonal_up_right.yellow": "Sarı Sağ Alt Üçbucaq", "block.minecraft.banner.flow.black": "Qara Axın", "block.minecraft.banner.flow.blue": "<PERSON><PERSON>", "block.minecraft.banner.flow.brown": "Qəhvəyi Axın", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Boz Axın", "block.minecraft.banner.flow.green": "Yaş<PERSON>l Axın", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_gray": "Açıq <PERSON>z A<PERSON>ın", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "block.minecraft.banner.flow.magenta": "Magenta Axın", "block.minecraft.banner.flow.orange": "Narıncı Axın", "block.minecraft.banner.flow.pink": "Çəhrayı Axın", "block.minecraft.banner.flow.purple": "Bənövşəyi Axın", "block.minecraft.banner.flow.red": "Qırmızı Axın", "block.minecraft.banner.flow.white": "<PERSON><PERSON>", "block.minecraft.banner.flow.yellow": "Sarı Axın", "block.minecraft.banner.flower.black": "Qara Çiçək Şəkli", "block.minecraft.banner.flower.blue": "Göy Çiçək Şəkli", "block.minecraft.banner.flower.brown": "Qəhvəyi Çiçək Şəkli", "block.minecraft.banner.flower.cyan": "<PERSON>vi Çiçək Şəkli", "block.minecraft.banner.flower.gray": "Boz Çiçək Şəkli", "block.minecraft.banner.flower.green": "Yaşıl Çiçək Şəkli", "block.minecraft.banner.flower.light_blue": "Açıq Göy Çiçək Şəkli", "block.minecraft.banner.flower.light_gray": "Açıq Boz Çiçək Şəkli", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON>q <PERSON>ıl Çiçək Şəkli", "block.minecraft.banner.flower.magenta": "Magenta Çiçək Şəkli", "block.minecraft.banner.flower.orange": "Narıncı Çiçək Şəkli", "block.minecraft.banner.flower.pink": "Çəhrayı Çiçək Şəkli", "block.minecraft.banner.flower.purple": "Bənövşəyi Çiçək Şəkli", "block.minecraft.banner.flower.red": "Qırmızı Çiçək Şəkli", "block.minecraft.banner.flower.white": "Ağ Çiçək Şəkli", "block.minecraft.banner.flower.yellow": "Sarı Çiçək Şəkli", "block.minecraft.banner.globe.black": "Qara kürə", "block.minecraft.banner.globe.blue": "<PERSON><PERSON><PERSON> kürə", "block.minecraft.banner.globe.brown": "Qəhvəyi kürə", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON><PERSON> cə<PERSON>ələr", "block.minecraft.banner.globe.gray": "<PERSON>z kürə", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON><PERSON> kürə", "block.minecraft.banner.globe.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.light_gray": "Açıq-boz kürə", "block.minecraft.banner.globe.lime": "<PERSON><PERSON><PERSON><PERSON> kü<PERSON>", "block.minecraft.banner.globe.magenta": "Bənövşəyi Qlobus", "block.minecraft.banner.globe.orange": "Narı<PERSON><PERSON> kürə", "block.minecraft.banner.globe.pink": "Çəhrayı kürə", "block.minecraft.banner.globe.purple": "Bənövşəyi kürə", "block.minecraft.banner.globe.red": "Qırmızı qlobus", "block.minecraft.banner.globe.white": "<PERSON><PERSON> kürə", "block.minecraft.banner.globe.yellow": "Sarı kürə", "block.minecraft.banner.gradient.black": "Yuxarıdan Aşağı Qara Keçiş", "block.minecraft.banner.gradient.blue": "Yuxarıdan Aşağı Göy Keçiş", "block.minecraft.banner.gradient.brown": "Yuxarıdan Aşağı Qəhvəyi Keçiş", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> qradient", "block.minecraft.banner.gradient.gray": "Yuxarıdan Aşağı Boz Keçiş", "block.minecraft.banner.gradient.green": "Yuxarıdan Aşağı Yaşıl Keçiş", "block.minecraft.banner.gradient.light_blue": "Yuxarıdan Aşağı Açıq Göy Keçiş", "block.minecraft.banner.gradient.light_gray": "Yuxarıdan Aşağı Açıq Boz Keçiş", "block.minecraft.banner.gradient.lime": "Yuxarıdan Aşağı Açıq <PERSON>", "block.minecraft.banner.gradient.magenta": "Macenta qradient", "block.minecraft.banner.gradient.orange": "Yuxarıdan Aşağı Narıncı Keçiş", "block.minecraft.banner.gradient.pink": "Yuxarıdan Aşağı Çəhrayı Keçiş", "block.minecraft.banner.gradient.purple": "Bənövş<PERSON>yi qradient", "block.minecraft.banner.gradient.red": "Yuxarıdan Aşağı Qırmızı Keçiş", "block.minecraft.banner.gradient.white": "Yuxarıdan Aşağı Ağ Keçiş", "block.minecraft.banner.gradient.yellow": "Yuxarıdan Aşağı Sarı Keçiş", "block.minecraft.banner.gradient_up.black": "Aşağıdan Yuxarı Qara Keçiş", "block.minecraft.banner.gradient_up.blue": "Aşağıdan Yuxarı Göy Keçiş", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>dan Yuxarı Qəhvəyi Keçiş", "block.minecraft.banner.gradient_up.cyan": "Firu<PERSON><PERSON>yi baza qradienti", "block.minecraft.banner.gradient_up.gray": "Aşağıdan Yuxarı Boz Keçiş", "block.minecraft.banner.gradient_up.green": "Aşağ<PERSON>dan Yuxarı Yaşıl Keçiş", "block.minecraft.banner.gradient_up.light_blue": "Aşağ<PERSON>dan Yuxarı Açıq <PERSON>", "block.minecraft.banner.gradient_up.light_gray": "Aşağıdan Yuxarı Açıq Boz Keçiş", "block.minecraft.banner.gradient_up.lime": "Aşağ<PERSON>dan Yuxarı Açıq <PERSON>", "block.minecraft.banner.gradient_up.magenta": "Aşağıdan Yuxarı Magenta Keçiş", "block.minecraft.banner.gradient_up.orange": "Aşağıdan Yuxarı Narıncı Keçiş", "block.minecraft.banner.gradient_up.pink": "Aşağ<PERSON>dan Yuxarı Çəhrayı Keçiş", "block.minecraft.banner.gradient_up.purple": "Bənövş<PERSON>yi baza qradienti", "block.minecraft.banner.gradient_up.red": "Aşağıdan Yuxarı Qırmızı Keçiş", "block.minecraft.banner.gradient_up.white": "Aşağıdan Yuxarı Ağ Keçiş", "block.minecraft.banner.gradient_up.yellow": "Aşağıdan Yuxarı Sarı Keçiş", "block.minecraft.banner.guster.black": "Qara Fırtına", "block.minecraft.banner.guster.blue": "<PERSON><PERSON>", "block.minecraft.banner.guster.brown": "Qəhvəyi Fırtına", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON> tozu", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "Çəhrayı Leysan", "block.minecraft.banner.guster.purple": "<PERSON>ənöv<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.red": "Qırmızı Leysan", "block.minecraft.banner.guster.white": "<PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "Sarı <PERSON>an", "block.minecraft.banner.half_horizontal.black": "Qara Üst Yarı", "block.minecraft.banner.half_horizontal.blue": "Göy Üst Yarı", "block.minecraft.banner.half_horizontal.brown": "Qəhvəyi Üst Yarı", "block.minecraft.banner.half_horizontal.cyan": "Mavi Üst Yarı", "block.minecraft.banner.half_horizontal.gray": "Boz Üst Yarı", "block.minecraft.banner.half_horizontal.green": "Yaşıl Üst Yarı", "block.minecraft.banner.half_horizontal.light_blue": "Açıq Göy Üst Yarı", "block.minecraft.banner.half_horizontal.light_gray": "Açıq Boz Üst Yarı", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON>q <PERSON>ıl Üst Yarı", "block.minecraft.banner.half_horizontal.magenta": "Magenta Üst Yarı", "block.minecraft.banner.half_horizontal.orange": "Narıncı Üst Yarı", "block.minecraft.banner.half_horizontal.pink": "Çəhrayı Üst Yarı", "block.minecraft.banner.half_horizontal.purple": "Bənövşəyi Üst Yarı", "block.minecraft.banner.half_horizontal.red": "Qırmızı Üst Yarı", "block.minecraft.banner.half_horizontal.white": "Ağ Üst Yarı", "block.minecraft.banner.half_horizontal.yellow": "Sarı Üst Yarı", "block.minecraft.banner.half_horizontal_bottom.black": "Qara Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.blue": "Göy Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.brown": "Qəhvəyi Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "Boz Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.green": "Yaşıl Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Açıq Göy Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Açıq Boz Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.lime": "Açıq Yaşıl Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magenta Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.orange": "Narıncı Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.pink": "Çəhrayı Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.purple": "Bənövşəyi Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.red": "Qırmızı Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.white": "Ağ Alt Yarı", "block.minecraft.banner.half_horizontal_bottom.yellow": "Sarı Alt Yarı", "block.minecraft.banner.half_vertical.black": "Qara Sol Yarı", "block.minecraft.banner.half_vertical.blue": "Göy Sol Yarı", "block.minecraft.banner.half_vertical.brown": "Qəhvəyi Sol Yarı", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "Boz Sol Yarı", "block.minecraft.banner.half_vertical.green": "Yaşıl Sol Yarı", "block.minecraft.banner.half_vertical.light_blue": "Açıq Göy Sol Yarı", "block.minecraft.banner.half_vertical.light_gray": "Açıq Boz Sol Yarı", "block.minecraft.banner.half_vertical.lime": "A<PERSON>ıq <PERSON>ıl Sol Yarı", "block.minecraft.banner.half_vertical.magenta": "Magenta Sol Yarı", "block.minecraft.banner.half_vertical.orange": "Narıncı Sol Yarı", "block.minecraft.banner.half_vertical.pink": "Çəhrayı Sol Yarı", "block.minecraft.banner.half_vertical.purple": "Bənövşəyi Sol Yarı", "block.minecraft.banner.half_vertical.red": "Qırmızı Sol Yarı", "block.minecraft.banner.half_vertical.white": "Ağ Sol Yarı", "block.minecraft.banner.half_vertical.yellow": "Sarı Sol Yarı", "block.minecraft.banner.half_vertical_right.black": "Qara Sağ Yarı", "block.minecraft.banner.half_vertical_right.blue": "Göy Sağ Yarı", "block.minecraft.banner.half_vertical_right.brown": "Qəhvəyi Sağ Yarı", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON> Sağ Yarı", "block.minecraft.banner.half_vertical_right.gray": "Boz Sağ Yarı", "block.minecraft.banner.half_vertical_right.green": "Yaşıl Sağ Yarı", "block.minecraft.banner.half_vertical_right.light_blue": "Açıq Göy Sağ Yarı", "block.minecraft.banner.half_vertical_right.light_gray": "Açıq Boz Sağ Yarı", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON>q <PERSON> Sağ Yarı", "block.minecraft.banner.half_vertical_right.magenta": "Magenta Sağ Yarı", "block.minecraft.banner.half_vertical_right.orange": "Narıncı Sağ Yarı", "block.minecraft.banner.half_vertical_right.pink": "Çəhrayı Sağ Yarı", "block.minecraft.banner.half_vertical_right.purple": "Bənövşəyi Sağ Yarı", "block.minecraft.banner.half_vertical_right.red": "Qırmızı Sağ Yarı", "block.minecraft.banner.half_vertical_right.white": "Ağ Sağ Yarı", "block.minecraft.banner.half_vertical_right.yellow": "Sarı Sağ Yarı", "block.minecraft.banner.mojang.black": "<PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>h<PERSON><PERSON><PERSON>ey", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON>q <PERSON>", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "Narıncı Şey", "block.minecraft.banner.mojang.pink": "Çəhrayı Şey", "block.minecraft.banner.mojang.purple": "Bənövşəyi Şey", "block.minecraft.banner.mojang.red": "Qırmızı Şey", "block.minecraft.banner.mojang.white": "<PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "Sarı Şey", "block.minecraft.banner.piglin.black": "<PERSON><PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> burun", "block.minecraft.banner.piglin.brown": "<PERSON>ə<PERSON><PERSON>əyi burun", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> mavi burun", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON>urun", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON> burun", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON><PERSON> mavi burun", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON><PERSON> boz burun", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON> burun", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON>latun <PERSON> Burnu", "block.minecraft.banner.piglin.orange": "Narıncı burun", "block.minecraft.banner.piglin.pink": "Çəhrayı burun", "block.minecraft.banner.piglin.purple": "Bənöv<PERSON>əyi burun", "block.minecraft.banner.piglin.red": "Qırmızı burun", "block.minecraft.banner.piglin.white": "<PERSON><PERSON> burun", "block.minecraft.banner.piglin.yellow": "Sarı burun", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "Yaşıl R<PERSON>", "block.minecraft.banner.rhombus.light_blue": "Açıq <PERSON>", "block.minecraft.banner.rhombus.light_gray": "Açıq <PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Narıncı Romb", "block.minecraft.banner.rhombus.pink": "Çəhrayı Romb", "block.minecraft.banner.rhombus.purple": "Bənövşəyi Romb", "block.minecraft.banner.rhombus.red": "Qırmızı Romb", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "Sarı Romb", "block.minecraft.banner.skull.black": "Qara Quru Baş Şəkli", "block.minecraft.banner.skull.blue": "Göy Quru Baş Şəkli", "block.minecraft.banner.skull.brown": "Qəhvəyi Quru Baş Şəkli", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON>", "block.minecraft.banner.skull.gray": "Boz Quru Baş Şəkli", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON> Quru Baş Şəkli", "block.minecraft.banner.skull.light_blue": "Açıq <PERSON>ö<PERSON> Quru Baş Şəkli", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON>q <PERSON> Quru Baş Şəkli", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON> Baş Şəkli", "block.minecraft.banner.skull.magenta": "Magenta Quru Ba<PERSON>kli", "block.minecraft.banner.skull.orange": "Narıncı Quru Baş Şəkli", "block.minecraft.banner.skull.pink": "Çəhrayı Quru Baş Şəkli", "block.minecraft.banner.skull.purple": "Bənövşəyi Quru Baş Şəkli", "block.minecraft.banner.skull.red": "Qırmızı Quru Baş Şəkli", "block.minecraft.banner.skull.white": "<PERSON><PERSON> <PERSON> Baş Şəkli", "block.minecraft.banner.skull.yellow": "Sarı Quru Baş Şəkli", "block.minecraft.banner.small_stripes.black": "Qara İncə Sütunlar", "block.minecraft.banner.small_stripes.blue": "Göy İncə <PERSON>ütunlar", "block.minecraft.banner.small_stripes.brown": "Qə<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "Boz İncə Sütunlar", "block.minecraft.banner.small_stripes.green": "Yaş<PERSON>l İncə Sütunlar", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON>ıq <PERSON> İncə <PERSON>ütunlar", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON><PERSON>tun<PERSON>", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON><PERSON>tun<PERSON>", "block.minecraft.banner.small_stripes.magenta": "Magenta İncə <PERSON>ütunlar", "block.minecraft.banner.small_stripes.orange": "Narıncı İncə Sütunlar", "block.minecraft.banner.small_stripes.pink": "Çəhrayı İncə Sütunlar", "block.minecraft.banner.small_stripes.purple": "Bənövş<PERSON>yi İncə <PERSON>", "block.minecraft.banner.small_stripes.red": "Qırmızı İncə Sütunlar", "block.minecraft.banner.small_stripes.white": "<PERSON>ğ İncə Sütunlar", "block.minecraft.banner.small_stripes.yellow": "Sarı İncə Sütunlar", "block.minecraft.banner.square_bottom_left.black": "Qara Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.blue": "Göy Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.brown": "Qəhvəyi Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON> Dördbucaq", "block.minecraft.banner.square_bottom_left.gray": "Boz Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.green": "Yaşıl Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.light_blue": "Açıq Göy Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.light_gray": "Açıq Boz Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.lime": "Açıq Yaşıl Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.magenta": "Magenta Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.orange": "Narıncı Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.pink": "Çəhrayı Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.purple": "Bənövşəyi Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.red": "Qırmızı Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.white": "Ağ Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_left.yellow": "Sarı Sol Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.black": "Qara Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.blue": "Göy Sağ Alt Düzbucaqlı", "block.minecraft.banner.square_bottom_right.brown": "Qəhvəyi Sağ Alt Düzbucaqlı", "block.minecraft.banner.square_bottom_right.cyan": "Mavi Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.gray": "Boz Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.green": "Yaşıl Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.light_blue": "Açıq Göy Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.light_gray": "Açıq Boz Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.lime": "Açıq Yaşıl Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.magenta": "Magenta Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.orange": "Narıncı Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.pink": "Çəhrayı Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.purple": "Bənövşəyi Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.red": "Qırmızı Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.white": "Ağ Sağ Alt Dördbucaq", "block.minecraft.banner.square_bottom_right.yellow": "Sarı Sağ Alt Dördbucaq", "block.minecraft.banner.square_top_left.black": "Qara Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.blue": "Göy Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.brown": "Qəhvəyi Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.cyan": "<PERSON><PERSON> Dördbucaq", "block.minecraft.banner.square_top_left.gray": "Boz Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.green": "Yaşıl Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.light_blue": "Açıq Göy Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.light_gray": "Açıq Boz Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.lime": "Açıq <PERSON>şıl Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.magenta": "Magenta Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.orange": "Narıncı Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.pink": "Çəhrayı Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.purple": "Bənövşəyi Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.red": "Qırmızı Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.white": "Ağ Sol Üst Dördbucaq", "block.minecraft.banner.square_top_left.yellow": "Sarı Sol Üst Dördbucaq", "block.minecraft.banner.square_top_right.black": "Qara Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.blue": "Göy Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.brown": "Qəhvəyi Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON> Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.gray": "Boz Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.green": "Yaşıl Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.light_blue": "Açıq Göy Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.light_gray": "Açıq Boz Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.lime": "Açıq <PERSON>ıl Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.magenta": "Magenta Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.orange": "Narıncı Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.pink": "Çəhrayı Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.purple": "Bənövşəyi Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.red": "Qırmızı Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.white": "Ağ Sağ Üst Dördbucaq", "block.minecraft.banner.square_top_right.yellow": "Sarı Sağ Üst Dördbucaq", "block.minecraft.banner.straight_cross.black": "Qara Xaç", "block.minecraft.banner.straight_cross.blue": "Göy <PERSON>", "block.minecraft.banner.straight_cross.brown": "Qəhvəyi Xaç", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "Boz X<PERSON>ç", "block.minecraft.banner.straight_cross.green": "Yaşıl Xaç", "block.minecraft.banner.straight_cross.light_blue": "Açıq Göy Xaç", "block.minecraft.banner.straight_cross.light_gray": "Açıq <PERSON>z <PERSON>", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON>q <PERSON>", "block.minecraft.banner.straight_cross.magenta": "Magenta Xaç", "block.minecraft.banner.straight_cross.orange": "Narıncı Xaç", "block.minecraft.banner.straight_cross.pink": "Çəhrayı Xaç", "block.minecraft.banner.straight_cross.purple": "Bənövşəyi Xaç", "block.minecraft.banner.straight_cross.red": "Qırmızı Xaç", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "Sarı Xaç", "block.minecraft.banner.stripe_bottom.black": "Altdan Qara Sətir", "block.minecraft.banner.stripe_bottom.blue": "Altdan Gö<PERSON>", "block.minecraft.banner.stripe_bottom.brown": "Altdan Qəhvəyi <PERSON>ətir", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "Altdan <PERSON>ti<PERSON>", "block.minecraft.banner.stripe_bottom.green": "Altdan <PERSON>ı<PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Altdan Açıq Gö<PERSON> Sətir", "block.minecraft.banner.stripe_bottom.light_gray": "Altdan Açıq <PERSON>r", "block.minecraft.banner.stripe_bottom.lime": "Altdan Açıq <PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON> Ma<PERSON>", "block.minecraft.banner.stripe_bottom.orange": "Altdan Narıncı Sətir", "block.minecraft.banner.stripe_bottom.pink": "Altdan Çəhrayı Sətir", "block.minecraft.banner.stripe_bottom.purple": "Altdan Bənövşəyi Sətir", "block.minecraft.banner.stripe_bottom.red": "Altdan Qırmızı Sətir", "block.minecraft.banner.stripe_bottom.white": "Altdan Ağ Sətir", "block.minecraft.banner.stripe_bottom.yellow": "Altdan Sarı Sətir", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON>ınc<PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON>ırmızı Sütun", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "Ortadan Sa<PERSON>tun", "block.minecraft.banner.stripe_downleft.black": "Qara Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.blue": "G<PERSON><PERSON> Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.brown": "Qəhvəyi Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON>arpaz Lent", "block.minecraft.banner.stripe_downleft.gray": "Boz Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON>l Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.light_blue": "Açıq <PERSON>öy Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.light_gray": "Açıq Boz Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON><PERSON><PERSON> Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.magenta": "Magenta Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.orange": "Narıncı Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.pink": "Çəhrayı Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.purple": "Bənövşəyi Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.red": "Qırmızı Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON> Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downleft.yellow": "Sarı Tərs Çarpaz Lent", "block.minecraft.banner.stripe_downright.black": "Qara Çarpaz Lent", "block.minecraft.banner.stripe_downright.blue": "Göy Çarpaz Lent", "block.minecraft.banner.stripe_downright.brown": "Qəhvəyi Çarpaz Lent", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "Boz Çarpaz Lent", "block.minecraft.banner.stripe_downright.green": "Yaşıl Çarpaz Lent", "block.minecraft.banner.stripe_downright.light_blue": "Açıq Göy Çarpaz Lent", "block.minecraft.banner.stripe_downright.light_gray": "Açıq Boz Çarpaz Lent", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON>q <PERSON> Çarpaz Lent", "block.minecraft.banner.stripe_downright.magenta": "Magenta Çarpaz Lent", "block.minecraft.banner.stripe_downright.orange": "Narıncı Çarpaz Lent", "block.minecraft.banner.stripe_downright.pink": "Çəhrayı Çarpaz Lent", "block.minecraft.banner.stripe_downright.purple": "Bənövşəyi Çarpaz Lent", "block.minecraft.banner.stripe_downright.red": "Qırmızı Çarpaz Lent", "block.minecraft.banner.stripe_downright.white": "Ağ Çarpaz Lent", "block.minecraft.banner.stripe_downright.yellow": "Sarı Çarpaz Lent", "block.minecraft.banner.stripe_left.black": "Soldan Qara Sütun", "block.minecraft.banner.stripe_left.blue": "Soldan Göy <PERSON>", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON>hvə<PERSON>", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.gray": "Soldan Boz Sütun", "block.minecraft.banner.stripe_left.green": "Soldan Yaşıl <PERSON>ü<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Soldan Açıq Göy Sütun", "block.minecraft.banner.stripe_left.light_gray": "Soldan Açıq <PERSON>z <PERSON>", "block.minecraft.banner.stripe_left.lime": "Soldan Açıq <PERSON>", "block.minecraft.banner.stripe_left.magenta": "Soldan Magent<PERSON>", "block.minecraft.banner.stripe_left.orange": "Soldan Narıncı Sütun", "block.minecraft.banner.stripe_left.pink": "Soldan Çəhrayı Sütun", "block.minecraft.banner.stripe_left.purple": "Soldan Bənövşəyi Sütun", "block.minecraft.banner.stripe_left.red": "Soldan Qırmızı Sütun", "block.minecraft.banner.stripe_left.white": "Soldan Ağ Sütun", "block.minecraft.banner.stripe_left.yellow": "Soldan Sarı Sütun", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON> Na<PERSON>ınc<PERSON>ətir", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON>tir", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON>ti<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>ı<PERSON>ızı Sətir", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.blue": "Sağdan <PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_blue": "<PERSON>ğdan Açıq <PERSON>ö<PERSON>", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON>ıq <PERSON>", "block.minecraft.banner.stripe_right.lime": "<PERSON>ğdan <PERSON>çıq <PERSON>", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON>dan Ma<PERSON>", "block.minecraft.banner.stripe_right.orange": "Sağdan Narıncı Sütun", "block.minecraft.banner.stripe_right.pink": "Sağdan Çəhrayı Sütun", "block.minecraft.banner.stripe_right.purple": "<PERSON>ğdan Bənövşəyi Sütun", "block.minecraft.banner.stripe_right.red": "Sağdan Qırmızı Sütun", "block.minecraft.banner.stripe_right.white": "Sağdan Ağ Sütun", "block.minecraft.banner.stripe_right.yellow": "Sağdan Sarı Sütun", "block.minecraft.banner.stripe_top.black": "Üstdən Q<PERSON> Sətir", "block.minecraft.banner.stripe_top.blue": "Üstdən Gö<PERSON>ti<PERSON>", "block.minecraft.banner.stripe_top.brown": "Üst<PERSON><PERSON>n <PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON>st<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "Üstdən <PERSON>tir", "block.minecraft.banner.stripe_top.green": "Üstdən <PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Üstdən Açıq Gö<PERSON> Sətir", "block.minecraft.banner.stripe_top.light_gray": "Üstdən Açıq <PERSON>", "block.minecraft.banner.stripe_top.lime": "Üstdən A<PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "Üst<PERSON>ən <PERSON>", "block.minecraft.banner.stripe_top.orange": "Üstdən Narıncı Sətir", "block.minecraft.banner.stripe_top.pink": "Üstdən Çəhrayı Sətir", "block.minecraft.banner.stripe_top.purple": "Üstdən Bənövşəyi Sətir", "block.minecraft.banner.stripe_top.red": "Üstdən Qırmızı Sətir", "block.minecraft.banner.stripe_top.white": "Üstdən A<PERSON>", "block.minecraft.banner.stripe_top.yellow": "Üstdən Sarı Sətir", "block.minecraft.banner.triangle_bottom.black": "Altdan Qara Üçbucaq", "block.minecraft.banner.triangle_bottom.blue": "Altdan Göy Üçbucaq", "block.minecraft.banner.triangle_bottom.brown": "Altdan Qəhvəyi Üçbucaq", "block.minecraft.banner.triangle_bottom.cyan": "Altdan Mavi Üçbucaq", "block.minecraft.banner.triangle_bottom.gray": "Altdan Boz Üçbucaq", "block.minecraft.banner.triangle_bottom.green": "Altdan Yaşıl Üçbucaq", "block.minecraft.banner.triangle_bottom.light_blue": "Altdan Açıq Göy Üçbucaq", "block.minecraft.banner.triangle_bottom.light_gray": "Altdan Açıq Boz Üçbucaq", "block.minecraft.banner.triangle_bottom.lime": "Altdan Açıq Yaşıl Üçbucaq", "block.minecraft.banner.triangle_bottom.magenta": "Altdan Magenta Üçbucaq", "block.minecraft.banner.triangle_bottom.orange": "Altdan Narıncı Üçbucaq", "block.minecraft.banner.triangle_bottom.pink": "Altdan Çəhrayı Üçbucaq", "block.minecraft.banner.triangle_bottom.purple": "Altdan Bənövşəyi Üçbucaq", "block.minecraft.banner.triangle_bottom.red": "Altdan Qırmızı Üçbucaq", "block.minecraft.banner.triangle_bottom.white": "Altdan Ağ Üçbucaq", "block.minecraft.banner.triangle_bottom.yellow": "Altdan Sarı Üçbucaq", "block.minecraft.banner.triangle_top.black": "Üstdən Qara Üçbucaq", "block.minecraft.banner.triangle_top.blue": "Üstdən Göy Üçbucaq", "block.minecraft.banner.triangle_top.brown": "Üstdən Qəhvəyi Üçbucaq", "block.minecraft.banner.triangle_top.cyan": "Üstdən Mavi Üçbu<PERSON>q", "block.minecraft.banner.triangle_top.gray": "Üstdən Boz Üçbucaq", "block.minecraft.banner.triangle_top.green": "Üstdən Yaşıl Üçbucaq", "block.minecraft.banner.triangle_top.light_blue": "Üstdən Açıq Göy Üçbucaq", "block.minecraft.banner.triangle_top.light_gray": "Üstdən Açıq Boz Üçbucaq", "block.minecraft.banner.triangle_top.lime": "Üstdən Açıq Yaşıl Üçbucaq", "block.minecraft.banner.triangle_top.magenta": "Üstdən Magenta Üçbucaq", "block.minecraft.banner.triangle_top.orange": "Üstdən Narıncı Üçbucaq", "block.minecraft.banner.triangle_top.pink": "Üstdən Çəhrayı Üçbucaq", "block.minecraft.banner.triangle_top.purple": "Üstdən Bənövşəyi Üçbucaq", "block.minecraft.banner.triangle_top.red": "Üstdən Qırmızı Üçbucaq", "block.minecraft.banner.triangle_top.white": "Üstdən Ağ Üçbucaq", "block.minecraft.banner.triangle_top.yellow": "Üstdən Sarı Üçbucaq", "block.minecraft.banner.triangles_bottom.black": "Altdan Qara Mi<PERSON>", "block.minecraft.banner.triangles_bottom.blue": "Altdan Göy <PERSON>", "block.minecraft.banner.triangles_bottom.brown": "Altdan Qəhvəyi <PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.gray": "Altdan <PERSON>", "block.minecraft.banner.triangles_bottom.green": "Altdan <PERSON>ıl <PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Altdan Açıq Göy Mi<PERSON>", "block.minecraft.banner.triangles_bottom.light_gray": "Altdan Açıq Boz <PERSON>ar <PERSON>i", "block.minecraft.banner.triangles_bottom.lime": "Altdan Açıq <PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON>dan Magenta <PERSON>", "block.minecraft.banner.triangles_bottom.orange": "Altdan Narıncı Mişar Dişi", "block.minecraft.banner.triangles_bottom.pink": "Altdan Çəhrayı Mişar Dişi", "block.minecraft.banner.triangles_bottom.purple": "Altdan Bənövşəyi Mişar <PERSON>", "block.minecraft.banner.triangles_bottom.red": "Altdan Qırmızı Mişar Dişi", "block.minecraft.banner.triangles_bottom.white": "Altdan Ağ Yaşıl Mişar Dişi", "block.minecraft.banner.triangles_bottom.yellow": "Altdan Sarı Mişar Dişi", "block.minecraft.banner.triangles_top.black": "Üstdən Qara Mişar Di<PERSON>", "block.minecraft.banner.triangles_top.blue": "Üstdən Gö<PERSON>", "block.minecraft.banner.triangles_top.brown": "Üstdən <PERSON>", "block.minecraft.banner.triangles_top.cyan": "Üst<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.gray": "Üstdən Boz <PERSON>", "block.minecraft.banner.triangles_top.green": "Üstdən <PERSON>", "block.minecraft.banner.triangles_top.light_blue": "Üstdən Açıq Gö<PERSON> Mi<PERSON>", "block.minecraft.banner.triangles_top.light_gray": "Üstdən Açıq <PERSON>z <PERSON>ar Dişi", "block.minecraft.banner.triangles_top.lime": "Üstdən A<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.magenta": "Üstdən <PERSON>", "block.minecraft.banner.triangles_top.orange": "Üstdən Narıncı Mişar Dişi", "block.minecraft.banner.triangles_top.pink": "Üstdən Çəhrayı Mişar Dişi", "block.minecraft.banner.triangles_top.purple": "Üstdən Bənövşəyi Mişar <PERSON>", "block.minecraft.banner.triangles_top.red": "Üstdən Qırmızı Mişar Dişi", "block.minecraft.banner.triangles_top.white": "Üstdən Ağ Mi<PERSON>", "block.minecraft.banner.triangles_top.yellow": "Üstdən Sarı Mişar Dişi", "block.minecraft.barrel": "Çəllək", "block.minecraft.barrier": "<PERSON>yer", "block.minecraft.basalt": "Bazalt", "block.minecraft.beacon": "Maya<PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bed.no_sleep": "Yalnız gecə və ya tufan zamanı yata bilərsiniz", "block.minecraft.bed.not_safe": "İndi dincələ bilməzsiniz; yaxınl<PERSON>q<PERSON>ər var", "block.minecraft.bed.obstructed": "Bu çarpayı doludur", "block.minecraft.bed.occupied": "Bu çarpayı dolu", "block.minecraft.bed.too_far_away": "İndi dincələ bilməzsən; çarpayı çox uzaqdadır", "block.minecraft.bedrock": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON>ı", "block.minecraft.bee_nest": "Arı <PERSON>ı", "block.minecraft.beehive": "<PERSON><PERSON>ı <PERSON>ətəyi", "block.minecraft.beetroots": "Çuğundurlar", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Böyük damcıyarpaq", "block.minecraft.big_dripleaf_stem": "Böyük damcıyarpaq saplağı", "block.minecraft.birch_button": "Ağcaqayın <PERSON>", "block.minecraft.birch_door": "Ağcaqayın <PERSON>", "block.minecraft.birch_fence": "Ağcaqayın <PERSON>", "block.minecraft.birch_fence_gate": "Ağcaqayın Çəpər Qapısı", "block.minecraft.birch_hanging_sign": "Ağcaqayın <PERSON>ə<PERSON>", "block.minecraft.birch_leaves": "Ağcaqayın <PERSON>ı", "block.minecraft.birch_log": "Ağcaqayın kötüyü", "block.minecraft.birch_planks": "Ağcaqayın Taxta", "block.minecraft.birch_pressure_plate": "Ağcaqayın Tə<PERSON>ə<PERSON>", "block.minecraft.birch_sapling": "Ağcaqayın <PERSON>ı", "block.minecraft.birch_sign": "Ağcaqayın Lövhə", "block.minecraft.birch_slab": "Ağcaqayın <PERSON>", "block.minecraft.birch_stairs": "Ağcaqayın <PERSON>ə<PERSON>", "block.minecraft.birch_trapdoor": "Ağcaqayın Tələ Qapısı", "block.minecraft.birch_wall_hanging_sign": "Ağcaqayın <PERSON>", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON>ın Lövhə", "block.minecraft.birch_wood": "Ağcaqa<PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "Qara Bayraq", "block.minecraft.black_bed": "Qara Çarpayı", "block.minecraft.black_candle": "<PERSON><PERSON>", "block.minecraft.black_candle_cake": "Qara şamlı tort", "block.minecraft.black_carpet": "Qara Xalça", "block.minecraft.black_concrete": "<PERSON><PERSON>", "block.minecraft.black_concrete_powder": "Qara Sement", "block.minecraft.black_glazed_terracotta": "Qara Şirli Terrakot<PERSON>", "block.minecraft.black_shulker_box": "<PERSON><PERSON>", "block.minecraft.black_stained_glass": "Qara Rəngli Şüşə", "block.minecraft.black_stained_glass_pane": "Qara Rəngli İncə Şüşə", "block.minecraft.black_terracotta": "Qara Terrakotta", "block.minecraft.black_wool": "<PERSON><PERSON>", "block.minecraft.blackstone": "Qaradaş", "block.minecraft.blackstone_slab": "Qaradaş <PERSON>", "block.minecraft.blackstone_stairs": "Qaradaş pilləkən", "block.minecraft.blackstone_wall": "Qaradaş divar", "block.minecraft.blast_furnace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "Göy Bayraq", "block.minecraft.blue_bed": "Göy Çarpayı", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "Göy şamlı tort", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "G<PERSON><PERSON> Se<PERSON>", "block.minecraft.blue_glazed_terracotta": "G<PERSON>y <PERSON>", "block.minecraft.blue_ice": "Göy <PERSON>", "block.minecraft.blue_orchid": "Göy <PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "Göy Rəngli Şüşə", "block.minecraft.blue_stained_glass_pane": "Göy Rəngli İncə Şüşə", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bone_block": "Sümük Bloku", "block.minecraft.bookshelf": "<PERSON><PERSON>", "block.minecraft.brain_coral": "<PERSON><PERSON>", "block.minecraft.brain_coral_block": "<PERSON><PERSON> mərcan bloku", "block.minecraft.brain_coral_fan": "<PERSON><PERSON>", "block.minecraft.brain_coral_wall_fan": " Üfüqi mərcan divarı", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON> divar", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "Qəhv<PERSON>yi Bayraq", "block.minecraft.brown_bed": "Qəhvəyi Çarpayı", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Qəhvəyi şamlı tort", "block.minecraft.brown_carpet": "Qəhv<PERSON>yi Xalça", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Qəhvəyi Sement", "block.minecraft.brown_glazed_terracotta": "Qəhvəyi Şirli <PERSON>", "block.minecraft.brown_mushroom": "Qəhv<PERSON>yi Göbələk", "block.minecraft.brown_mushroom_block": "Qəhvəyi Göbələk Bloku", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Qəhvəyi Rəngli Şüşə", "block.minecraft.brown_stained_glass_pane": "Qəhvəyi Rəngli İncə Şüşə", "block.minecraft.brown_terracotta": "Q<PERSON>h<PERSON><PERSON>yi <PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.bubble_column": "Baloncuk Sütunu", "block.minecraft.bubble_coral": "Baloncuk mərcan", "block.minecraft.bubble_coral_block": "Baloncuk mərcan bloku", "block.minecraft.bubble_coral_fan": "Bubble Coral Fan", "block.minecraft.bubble_coral_wall_fan": "Köpük mərcan dıvarı", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON>lanan ametist", "block.minecraft.bush": "<PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON> Gülü", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Kalibrlənmiş Sculk Sensor", "block.minecraft.campfire": "Düşərg<PERSON> tonqalı", "block.minecraft.candle": "<PERSON>am", "block.minecraft.candle_cake": "Şamlı Tort", "block.minecraft.carrots": "Yerkök<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Xəriteçi Masası", "block.minecraft.carved_pumpkin": "Oyulmuş balqabaq", "block.minecraft.cauldron": "Qazan", "block.minecraft.cave_air": "Mağara Havası", "block.minecraft.cave_vines": "Mağara sarmaşıqları", "block.minecraft.cave_vines_plant": "Mağara sarmaşığı bitkisi", "block.minecraft.chain": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Zəncirləmə Əmr Bloku", "block.minecraft.cherry_button": "Albalı dü<PERSON>ə<PERSON>", "block.minecraft.cherry_door": "Albalı Qapı", "block.minecraft.cherry_fence": "Albalı Çəpər", "block.minecraft.cherry_fence_gate": "Albalı hasar qapısı", "block.minecraft.cherry_hanging_sign": "Albalı Asma İşarəsi", "block.minecraft.cherry_leaves": "Albalı yarpaqları", "block.minecraft.cherry_log": "Albalı Girişi", "block.minecraft.cherry_planks": "Albalı taxtaları", "block.minecraft.cherry_pressure_plate": "Albalı təzyiq lö<PERSON>", "block.minecraft.cherry_sapling": "Albalı Fidanı", "block.minecraft.cherry_sign": "Albalı işarəsi", "block.minecraft.cherry_slab": "Albalı Plitəsi", "block.minecraft.cherry_stairs": "<PERSON><PERSON>ı <PERSON>ə<PERSON>ə<PERSON>", "block.minecraft.cherry_trapdoor": "Albalı tələ qapısı", "block.minecraft.cherry_wall_hanging_sign": "Albalı Divardan As<PERSON>ı", "block.minecraft.cherry_wall_sign": "Albalı Divar İşarəsi", "block.minecraft.cherry_wood": "Albalı ağacı", "block.minecraft.chest": "Sandıq", "block.minecraft.chipped_anvil": "Çatlamış Örs", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kitab rəfi", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> mis", "block.minecraft.chiseled_deepslate": "Oyulmuş şifer daşı", "block.minecraft.chiseled_nether_bricks": "Naxışlı Nezer kərpici", "block.minecraft.chiseled_polished_blackstone": "Naxışlı hamar q<PERSON>", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON>a Qırmızı Qumdaşı", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON> qatran kərpic bloku", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuf", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> tufdan kə<PERSON>ər", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.clay": "<PERSON>", "block.minecraft.closed_eyeblossom": "Qapalı Göz Ağzı", "block.minecraft.coal_block": "Kö<PERSON>ür <PERSON>", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Cod Torpaq", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>ı", "block.minecraft.cobbled_deepslate_slab": "Daşlamış şifer pillə", "block.minecraft.cobbled_deepslate_stairs": "Daşlatmış şifer pilləkan", "block.minecraft.cobbled_deepslate_wall": "Daşlaşmiş şifer divar", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>kə<PERSON>", "block.minecraft.cobblestone_wall": "Çaydaşı Divar", "block.minecraft.cobweb": "Hörümçək Toru", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "<PERSON><PERSON><PERSON>", "block.minecraft.comparator": "Qızıl Daş Qarşılaşdırıcı", "block.minecraft.composter": "Ko<PERSON>ster", "block.minecraft.conduit": "Kə<PERSON>ər", "block.minecraft.copper_block": "<PERSON><PERSON> bloku", "block.minecraft.copper_bulb": "<PERSON><PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON> filizi", "block.minecraft.copper_trapdoor": "Mis Tələ Qapısı", "block.minecraft.cornflower": "G<PERSON><PERSON>ə<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> kə<PERSON>", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>li", "block.minecraft.cracked_nether_bricks": "Çatlamış Nezer kərpici", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> hamar q<PERSON> kə<PERSON>", "block.minecraft.cracked_stone_bricks": "Çatlaq <PERSON>", "block.minecraft.crafter": "Hazırlayıcı Masa", "block.minecraft.crafting_table": "Hazırlama Masası", "block.minecraft.creaking_heart": "Cırıldayan Ürək", "block.minecraft.creeper_head": "Creeper Başı", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_button": "<PERSON>", "block.minecraft.crimson_door": "Al qapı", "block.minecraft.crimson_fence": "Al Ç<PERSON>ə<PERSON>", "block.minecraft.crimson_fence_gate": "Al Çəpər Qapısı", "block.minecraft.crimson_fungus": "Al göbələk", "block.minecraft.crimson_hanging_sign": "Crimson <PERSON>", "block.minecraft.crimson_hyphae": "Al hifa", "block.minecraft.crimson_nylium": "Al nilium", "block.minecraft.crimson_planks": "Al taxta", "block.minecraft.crimson_pressure_plate": "<PERSON> təzyiq lövhəsi", "block.minecraft.crimson_roots": "Al köklər", "block.minecraft.crimson_sign": "Al lövhə", "block.minecraft.crimson_slab": "<PERSON>", "block.minecraft.crimson_stairs": "Al Pilləkən", "block.minecraft.crimson_stem": "Qırmızı saplaq", "block.minecraft.crimson_trapdoor": "Al tələ qapısı", "block.minecraft.crimson_wall_hanging_sign": "Qırmızı divardan asma nişanı", "block.minecraft.crimson_wall_sign": "Qırmızı Divar işarəsi", "block.minecraft.crying_obsidian": "Ağlayan obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>ş mis", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mis pillə", "block.minecraft.cut_copper_stairs": "K<PERSON><PERSON>lmiş Mis Pilləkən", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>ş qırmızı qum daşı", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qırmızı qum daşı pillə", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qum daşı", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qum daşı pillə", "block.minecraft.cyan_banner": "<PERSON><PERSON>", "block.minecraft.cyan_bed": "Firuzəyi çarpayı", "block.minecraft.cyan_candle": "Firu<PERSON><PERSON><PERSON> şam", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l şam ilə tort", "block.minecraft.cyan_carpet": "<PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "Firuzəyi şirli terrakotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON> Rəngli İncə Şüşə", "block.minecraft.cyan_terracotta": "Firuzəyi terrakotta", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "Sınmış Örs", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Qara Palıd <PERSON>", "block.minecraft.dark_oak_door": "Qara Palıd Qapı", "block.minecraft.dark_oak_fence": "Qara Palıd <PERSON>", "block.minecraft.dark_oak_fence_gate": "Qara Palıd Çəpə<PERSON>apısı", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON> palıd asma lö<PERSON>", "block.minecraft.dark_oak_leaves": "Qara Palıd <PERSON>ı", "block.minecraft.dark_oak_log": "Qara Palıd <PERSON>", "block.minecraft.dark_oak_planks": "Qara Palıd Taxta", "block.minecraft.dark_oak_pressure_plate": "Qara Palıd Tə<PERSON>", "block.minecraft.dark_oak_sapling": "Qara Palıd Fidanı", "block.minecraft.dark_oak_sign": "Qara Palıd Lövhə", "block.minecraft.dark_oak_slab": "Qara Palıd Pillə", "block.minecraft.dark_oak_stairs": "Qara Palıd <PERSON>", "block.minecraft.dark_oak_trapdoor": "Qara Palıd Tələ Qapısı", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> palıd di<PERSON>an asma nişanı", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON> Qara Palıd Lövhə", "block.minecraft.dark_oak_wood": "Qara Palıd <PERSON>", "block.minecraft.dark_prismarine": "Qara Prizmarin", "block.minecraft.dark_prismarine_slab": "Qara prizmarin pillə", "block.minecraft.dark_prismarine_stairs": "Qara prizmarin <PERSON>n", "block.minecraft.daylight_detector": "Gün İşığı Dedektoru", "block.minecraft.dead_brain_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON> beyin mərcan bloku", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> beyin mərcan fanatı", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> beyin mərcan divar fanatı\n", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON>loncuk Mərcan", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON> baloncuk mərcan bloku", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON> beyin mərcan fanatı\n", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON>ü köpük mərcan divar fanatı", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral": "<PERSON><PERSON><PERSON>ə<PERSON> Mərcan", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON> atəş mərcan bloku", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON> yandırmiş mərcan", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> yanğın mərcan divar fanatı", "block.minecraft.dead_horn_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON> buynuz mərcan bloku", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON> mərcan ", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON> buynuz mərcan divar fanatı", "block.minecraft.dead_tube_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON> boru mərcan bloku", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON> boru mərcan", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.decorated_pot": "Bəzək<PERSON>", "block.minecraft.deepslate": "<PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "<PERSON>ifer daşı kərpicindən pillə", "block.minecraft.deepslate_brick_stairs": "<PERSON><PERSON> daşı kərpicindən pilləkən", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> daşı kərpicindən divar", "block.minecraft.deepslate_bricks": "<PERSON><PERSON> da<PERSON> kə<PERSON>", "block.minecraft.deepslate_coal_ore": "Ş<PERSON><PERSON> kömür fili<PERSON>", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON> mis filizi", "block.minecraft.deepslate_diamond_ore": "Şiferli almaz filizi", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON> zümrüd <PERSON>", "block.minecraft.deepslate_gold_ore": "Şiferli qızıl filizi", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON> də<PERSON> fili<PERSON>", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "Şiferli qırmızıdaş filizi", "block.minecraft.deepslate_tile_slab": "<PERSON><PERSON> daşı kafelindən pillə", "block.minecraft.deepslate_tile_stairs": "<PERSON><PERSON> daşı kafelindən pilləkən", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> daşı kafelindən divar", "block.minecraft.deepslate_tiles": "<PERSON><PERSON> ka<PERSON>li", "block.minecraft.detector_rail": "<PERSON><PERSON><PERSON>", "block.minecraft.diamond_block": "Almaz Bloku", "block.minecraft.diamond_ore": "<PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt": "Torpaq", "block.minecraft.dirt_path": "Torpaq <PERSON>", "block.minecraft.dispenser": "Atıcı", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "Əjdaha Başı", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON> başı", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dripstone_block": "Damcı daşı bloku", "block.minecraft.dropper": "Buraxıcı", "block.minecraft.emerald_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "Sehrbaz<PERSON><PERSON>q <PERSON>", "block.minecraft.end_gateway": "<PERSON> keçidi", "block.minecraft.end_portal": "<PERSON>", "block.minecraft.end_portal_frame": "End portalı strukturu", "block.minecraft.end_rod": "<PERSON>", "block.minecraft.end_stone": "<PERSON>", "block.minecraft.end_stone_brick_slab": "End daşı kərpici pilləsi", "block.minecraft.end_stone_brick_stairs": "End Daşı Kərpici Pilləkən", "block.minecraft.end_stone_brick_wall": "End daşı kərpici divarı", "block.minecraft.end_stone_bricks": "<PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> mis", "block.minecraft.exposed_copper": "Müda<PERSON><PERSON><PERSON><PERSON> qalmış mis", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_trapdoor": "Mumlu Açıq <PERSON>", "block.minecraft.exposed_cut_copper": "Müda<PERSON><PERSON><PERSON><PERSON> qalmış kəsilmiş mis", "block.minecraft.exposed_cut_copper_slab": "Müda<PERSON><PERSON><PERSON><PERSON> qalmış kəsilmiş mis pillə", "block.minecraft.exposed_cut_copper_stairs": "Müda<PERSON><PERSON><PERSON><PERSON>silmiş Mis Pilləkən", "block.minecraft.farmland": "Tarla Ərazısı", "block.minecraft.fern": "<PERSON>ı<PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON> mərcan", "block.minecraft.fire_coral_block": "Atəş Mərcan Bloku", "block.minecraft.fire_coral_fan": "Yanğın Mərcan fanatı", "block.minecraft.fire_coral_wall_fan": "Yandırm<PERSON>ş boru mərcan divarı", "block.minecraft.firefly_bush": "İşıldaquş Kolu", "block.minecraft.fletching_table": "Oxçu masası", "block.minecraft.flower_pot": "Saxsı", "block.minecraft.flowering_azalea": "Çiçəkləyən azaliya", "block.minecraft.flowering_azalea_leaves": "Çiçəkləyən azaliya yarpaqları", "block.minecraft.frogspawn": "Qurbağa Yumurtaları", "block.minecraft.frosted_ice": "Şaxta", "block.minecraft.furnace": "Soba", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>ara<PERSON>", "block.minecraft.glass": "Şüşə", "block.minecraft.glass_pane": "İncə Şüşə", "block.minecraft.glow_lichen": "Parı<PERSON>yan <PERSON>", "block.minecraft.glowstone": "İş<PERSON>q <PERSON>", "block.minecraft.gold_block": "Qızıl Bloku", "block.minecraft.gold_ore": "Qız<PERSON>l <PERSON>", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "<PERSON><PERSON><PERSON> pill<PERSON>", "block.minecraft.granite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.grass": "<PERSON>t", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON>", "block.minecraft.gravel": "Çınq<PERSON>l", "block.minecraft.gray_banner": "Boz Bayraq", "block.minecraft.gray_bed": "Boz Çarpayı", "block.minecraft.gray_candle": "<PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON>z şam ilə tort", "block.minecraft.gray_carpet": "<PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "Boz Sement", "block.minecraft.gray_glazed_terracotta": "Boz Şirli <PERSON>", "block.minecraft.gray_shulker_box": "<PERSON><PERSON>", "block.minecraft.gray_stained_glass": "Boz Rəngli Şüşə", "block.minecraft.gray_stained_glass_pane": "Boz Rəngli İncə Şüşə", "block.minecraft.gray_terracotta": "Boz terrakotta", "block.minecraft.gray_wool": "Boz Yun", "block.minecraft.green_banner": "Yaşıl Bayraq", "block.minecraft.green_bed": "Yaşıl çarpayı", "block.minecraft.green_candle": "Ya<PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "Yaşıl şamlı tort", "block.minecraft.green_carpet": "Yaşıl Xalça", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "Yaşıl Sement", "block.minecraft.green_glazed_terracotta": "Yaşıl Şirli Terrakotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON>l Rəngli Şüşə", "block.minecraft.green_stained_glass_pane": "Ya<PERSON><PERSON>l Rəngli İncə Şüşə", "block.minecraft.green_terracotta": "Yaşıl terrakotta", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "İtiləmə", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> k<PERSON>ər", "block.minecraft.hay_block": "<PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> nüvə", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON><PERSON><PERSON> Ağırlıqlı Təyziq Təbəqəsi", "block.minecraft.honey_block": "Bal Bloku", "block.minecraft.honeycomb_block": "Bal Pətəyi Bloku", "block.minecraft.hopper": "Süzgəc", "block.minecraft.horn_coral": "<PERSON><PERSON><PERSON> mərcan", "block.minecraft.horn_coral_block": "<PERSON><PERSON>z mərcan bloku", "block.minecraft.horn_coral_fan": "<PERSON><PERSON>z mərcan fanat", "block.minecraft.horn_coral_wall_fan": "Buynuz Mərcan Divar fanatı", "block.minecraft.ice": "Buz", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_cobblestone": "Daraşılmış Çaydaşı", "block.minecraft.infested_cracked_stone_bricks": "Yoluxmuş Çatlamış Da<PERSON>", "block.minecraft.infested_deepslate": "Daraşılmış Şifer Daşı", "block.minecraft.infested_mossy_stone_bricks": "Daraşılmış Ma<PERSON>ırlı <PERSON>", "block.minecraft.infested_stone": "Daraşılmış Daş", "block.minecraft.infested_stone_bricks": "Daraş<PERSON>lm<PERSON>ş <PERSON>", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "<PERSON>ə<PERSON> Tələ Qapısı", "block.minecraft.jack_o_lantern": "Balqabaq Lampası", "block.minecraft.jigsaw": "Pazl Bloku", "block.minecraft.jukebox": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_button": "Cəngəllik Ağacı Düymə", "block.minecraft.jungle_door": "Cəngəllik Ağacı Qapı", "block.minecraft.jungle_fence": "Cəngəllik Ağacı Çəpər", "block.minecraft.jungle_fence_gate": "Cəngəllik Ağacı Çəpər Qapısı", "block.minecraft.jungle_hanging_sign": "Jungle Asma <PERSON>şarəsi", "block.minecraft.jungle_leaves": "Cəngəllik Ağacı Yarpaqları", "block.minecraft.jungle_log": "Cəngəllik kötüyü", "block.minecraft.jungle_planks": "Cəngəllik Ağacı Taxta", "block.minecraft.jungle_pressure_plate": "Cəngəllik Ağacı Təyziq Təbəqəsi", "block.minecraft.jungle_sapling": "Cəngəllik Ağacı Fidanı", "block.minecraft.jungle_sign": "Cəngəllik Ağacı Lövhə", "block.minecraft.jungle_slab": "Cəngəllik Ağacı Pillə", "block.minecraft.jungle_stairs": "Cəngəllik Ağacı Pilləkən", "block.minecraft.jungle_trapdoor": "Cəngəllik Ağacı Tələ Qapısı", "block.minecraft.jungle_wall_hanging_sign": "Cəngəllik Divarında Asma Nişanı", "block.minecraft.jungle_wall_sign": "<PERSON>varda Cəngəllik Ağacı Lövhə", "block.minecraft.jungle_wood": "Cəngəllik Ağacı Odunu", "block.minecraft.kelp": "<PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.ladder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Fənər", "block.minecraft.lapis_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lapis_ore": "Lapis <PERSON>", "block.minecraft.large_amethyst_bud": "Böyük ametist tum<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lava qazanı", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "<PERSON>", "block.minecraft.light": "İşıq", "block.minecraft.light_blue_banner": "Açıq Göy Bayraq", "block.minecraft.light_blue_bed": "A<PERSON>ıq göy çarpayı", "block.minecraft.light_blue_candle": "Açıq <PERSON>", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON><PERSON><PERSON> mavi şam ilə tort", "block.minecraft.light_blue_carpet": "Açıq Göy Xalça", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete_powder": "Açıq Gö<PERSON> Sement", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON>q göy şirli terrakotta", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass": "Açıq Göy Rəngli Şüşə", "block.minecraft.light_blue_stained_glass_pane": "Açıq Göy Rəngli İncə Şüşə", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON><PERSON> gö<PERSON> terrakotta", "block.minecraft.light_blue_wool": "Açıq <PERSON>", "block.minecraft.light_gray_banner": "Açıq Boz Bayraq", "block.minecraft.light_gray_bed": "Açıq Boz Çarpayı", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON><PERSON> boz <PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON><PERSON><PERSON> boz şam ilə tort", "block.minecraft.light_gray_carpet": "<PERSON><PERSON>ıq <PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "Açıq Boz Sement", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> boz çirli terrakotta", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON>q <PERSON>z Rəngli Şüşə", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON><PERSON>q <PERSON>z Rəngli İncə Şüşə", "block.minecraft.light_gray_terracotta": "Açıq <PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_weighted_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>ırlıqlı Təyziq <PERSON>ə<PERSON>əqə<PERSON>", "block.minecraft.lightning_rod": "İldırım ötürücü", "block.minecraft.lilac": "Ya<PERSON>ə<PERSON>ən", "block.minecraft.lily_of_the_valley": "May in<PERSON><PERSON><PERSON><PERSON><PERSON>yi", "block.minecraft.lily_pad": "Suzanbağı", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON>q <PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON>q <PERSON>arpayı", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON>ə<PERSON> şam ilə tort", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON>q <PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON>q <PERSON>", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON>ə<PERSON> Şüşə", "block.minecraft.lime_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>əngli İncə Şüşə", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON>ıl terrakotta", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>ı", "block.minecraft.magenta_banner": "Magenta Bayraq", "block.minecraft.magenta_bed": "Macenta çarpayı", "block.minecraft.magenta_candle": "Macenta şam", "block.minecraft.magenta_candle_cake": "Bənövşəyi-qırmızı şam ilə tort", "block.minecraft.magenta_carpet": "Magenta Rəngli Xalça", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "Magenta Sement", "block.minecraft.magenta_glazed_terracotta": "Magenta Şirli <PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "Magenta Rəngli Şüşə", "block.minecraft.magenta_stained_glass_pane": "Magenta Rəngli İncə Şüşə", "block.minecraft.magenta_terracotta": "Macenta terrakotta", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.magma_block": "Magma Bloku", "block.minecraft.mangrove_button": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON>rov <PERSON>", "block.minecraft.mangrove_hanging_sign": "Mangrov Asma İşarəsi", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_planks": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_sign": "<PERSON><PERSON><PERSON> Lövhə", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_trapdoor": "Manqrov Tələ Qapısı", "block.minecraft.mangrove_wall_hanging_sign": "Mangrov Divar Asma <PERSON>arə<PERSON>", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "Orta ametist tum<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.melon": "Qarpız", "block.minecraft.melon_stem": "Qarpız sapı", "block.minecraft.moss_block": "<PERSON><PERSON><PERSON><PERSON> bloku", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "Mamırlı Çaydaşı", "block.minecraft.mossy_cobblestone_slab": "Mamırlı Çaydaşı Pillə", "block.minecraft.mossy_cobblestone_stairs": "Mamırlı Çaydaşı Pilləkən", "block.minecraft.mossy_cobblestone_wall": "Mamırlı Qırıq Daş Divarı", "block.minecraft.mossy_stone_brick_slab": "Mam<PERSON><PERSON><PERSON>ş Kərpic Pillə", "block.minecraft.mossy_stone_brick_stairs": "Mam<PERSON><PERSON><PERSON> Daş Kərpic Pilləkən", "block.minecraft.mossy_stone_brick_wall": "Mamırlı Daş <PERSON> Di<PERSON>", "block.minecraft.mossy_stone_bricks": "Ma<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud": "Palçıq", "block.minecraft.mud_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> kə<PERSON> pillə", "block.minecraft.mud_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> kə<PERSON>ə<PERSON>ən", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> kə<PERSON> divar", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "Palçıqlı Manqrov Kökləri", "block.minecraft.mushroom_stem": "Göbələk Sapı", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "<PERSON>her Kərpici Çəpər", "block.minecraft.nether_brick_slab": "Nether Kərpici Pillə", "block.minecraft.nether_brick_stairs": "Nether Kərpici Pilləkən", "block.minecraft.nether_brick_wall": "Nezer kə<PERSON> divarı", "block.minecraft.nether_bricks": "<PERSON><PERSON>", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qızıl filizi", "block.minecraft.nether_portal": "<PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "Nether Kvarsı Filizi", "block.minecraft.nether_sprouts": "<PERSON><PERSON>", "block.minecraft.nether_wart": "<PERSON><PERSON>", "block.minecraft.nether_wart_block": "Nezer ziyi<PERSON> bloku", "block.minecraft.netherite_block": "Nezerit bloku", "block.minecraft.netherrack": "Nether Daşı", "block.minecraft.note_block": "Not Bloku", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON><PERSON>d <PERSON>", "block.minecraft.oak_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON>d Təl<PERSON> Qapısı", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.observer": "Gözləmçi", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "Oxra qurbağaişıq", "block.minecraft.ominous_banner": "Uğursuz bayraq", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_banner": "Narıncı Bayraq", "block.minecraft.orange_bed": "Narıncı çarpayı", "block.minecraft.orange_candle": "Narıncı Şam", "block.minecraft.orange_candle_cake": "Narıncı şamlı tort", "block.minecraft.orange_carpet": "Narıncı Xalça", "block.minecraft.orange_concrete": "Narıncı Beton", "block.minecraft.orange_concrete_powder": "Narıncı Sement", "block.minecraft.orange_glazed_terracotta": "Narıncı şirli terrakotta", "block.minecraft.orange_shulker_box": "Na<PERSON>ı<PERSON><PERSON> Shulker <PERSON>", "block.minecraft.orange_stained_glass": "Narıncı Rəngli Şüşə", "block.minecraft.orange_stained_glass_pane": "Narıncı Rəngli İncə Şüşə", "block.minecraft.orange_terracotta": "Narıncı terrakotta", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_wool": "Narıncı Yun", "block.minecraft.oxeye_daisy": "Çobanyastığı", "block.minecraft.oxidized_chiseled_copper": "Oksidl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kəsilm<PERSON>ş mis", "block.minecraft.oxidized_copper": "Oksidləşmiş mis", "block.minecraft.oxidized_copper_bulb": "Oksidləşdirilmiş mis lampa", "block.minecraft.oxidized_copper_door": "Oksidləşdirilmiş Mis Qapı", "block.minecraft.oxidized_copper_grate": "Oksidləşdirilmiş Mis ızgara", "block.minecraft.oxidized_copper_trapdoor": "Oksidləşmiş Mis Qapı", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kəsilm<PERSON>ş mis", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kəsilmiş mis pillə", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş Mis Pilləkən", "block.minecraft.packed_ice": "Paketlənmiş Buz", "block.minecraft.packed_mud": "Bükülmüş palçıq", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON> sol<PERSON> mamır", "block.minecraft.pale_moss_block": "<PERSON><PERSON><PERSON> mamır bloku", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON>un mamır <PERSON>", "block.minecraft.pale_oak_button": "<PERSON><PERSON><PERSON> palıd <PERSON>", "block.minecraft.pale_oak_door": "<PERSON>ğun palıd qapısı", "block.minecraft.pale_oak_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>apısı", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_log": "<PERSON><PERSON>", "block.minecraft.pale_oak_planks": "Ağ Palıd Taxtası", "block.minecraft.pale_oak_pressure_plate": "Solğun Palıd Basqı Plitəsi", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON> Palıd <PERSON>", "block.minecraft.pale_oak_sign": "<PERSON><PERSON>", "block.minecraft.pale_oak_slab": "Ağ Palıd YarımTaxtası", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON>", "block.minecraft.pale_oak_trapdoor": "<PERSON><PERSON> <PERSON><PERSON>ıd Tələ Qapısı", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_wood": "<PERSON><PERSON>", "block.minecraft.pearlescent_froglight": "S<PERSON><PERSON><PERSON><PERSON>li qurbağaişıq", "block.minecraft.peony": "Pion", "block.minecraft.petrified_oak_slab": "Daşlaşmış Palıd Pillə", "block.minecraft.piglin_head": "<PERSON><PERSON> rəhbəri", "block.minecraft.piglin_wall_head": "Piglin Divar Başı", "block.minecraft.pink_banner": "Çəhrayı Bayraq", "block.minecraft.pink_bed": "Çəhrayı Çarpayı", "block.minecraft.pink_candle": "Çəhrayı Şam", "block.minecraft.pink_candle_cake": "Çəhrayı şam ilə tort", "block.minecraft.pink_carpet": "Çəhrayı Xalça", "block.minecraft.pink_concrete": "Çəhrayı Beton", "block.minecraft.pink_concrete_powder": "Çəhrayı Sement", "block.minecraft.pink_glazed_terracotta": "Çəhrayı Şirli Terrakotta", "block.minecraft.pink_petals": "Çəhrayı ləçəklər", "block.minecraft.pink_shulker_box": "Çəhrayı Shulker <PERSON>", "block.minecraft.pink_stained_glass": "Çəyrayı Rəngli Şüşə", "block.minecraft.pink_stained_glass_pane": "Çəyrayı Rəngli İncə Şüşə", "block.minecraft.pink_terracotta": "Çəhrayı terrakotta", "block.minecraft.pink_tulip": "Çəhrayı Lalə", "block.minecraft.pink_wool": "Çəhrayı Yun", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON>ston başlığı", "block.minecraft.pitcher_crop": "Pitcher C<PERSON>", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head": "Oyunçu Başı", "block.minecraft.player_head.named": "%s'in Başı", "block.minecraft.player_wall_head": "Oyuncu Başı", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "İtiuclu damcı daşı bloku", "block.minecraft.polished_andesite": "Cilalanmış Andezit", "block.minecraft.polished_andesite_slab": "Cilalanmış Andezit Pillə", "block.minecraft.polished_andesite_stairs": "Cilalanmış Andezit Pilləkan", "block.minecraft.polished_basalt": "<PERSON><PERSON> baz<PERSON>", "block.minecraft.polished_blackstone": "<PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON> qara<PERSON> kərp<PERSON> pilləsi", "block.minecraft.polished_blackstone_brick_stairs": "Cilalı Qaradaş Kərpic Pilləkən", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> kərpic divar", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON> kə<PERSON>", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON> qara<PERSON> dü<PERSON>", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON> q<PERSON> tə<PERSON> lö<PERSON>", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON> qarada<PERSON> pillə", "block.minecraft.polished_blackstone_stairs": "Cilalı Qaradaş Pilləkən", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> divar", "block.minecraft.polished_deepslate": "<PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "<PERSON><PERSON> şifer daşı pillə", "block.minecraft.polished_deepslate_stairs": "<PERSON>ar şifer daşı pillə<PERSON>ən", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> da<PERSON> divar", "block.minecraft.polished_diorite": "Cilalanmış Diorit", "block.minecraft.polished_diorite_slab": "Cilalanmış Diorit Pillə", "block.minecraft.polished_diorite_stairs": "Cilalanmış Diorit Pilləkən", "block.minecraft.polished_granite": "Cilalanmış Qranit", "block.minecraft.polished_granite_slab": "Cilalanmış Qranit Pillə", "block.minecraft.polished_granite_stairs": "Cilalanmış Qranit Pilləkən", "block.minecraft.polished_tuff": "Cilalanmış tuf", "block.minecraft.polished_tuff_slab": "Cilalanmış tuf pilləsi", "block.minecraft.polished_tuff_stairs": "Cilalanmış Tü<PERSON>", "block.minecraft.polished_tuff_wall": "Cilalanmış Tuf <PERSON>", "block.minecraft.poppy": "Xaşxaş", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Akasiya Fidanı Saxsısı", "block.minecraft.potted_allium": "Dib<PERSON><PERSON><PERSON><PERSON> soğan", "block.minecraft.potted_azalea_bush": "Dibçəkli azaliya", "block.minecraft.potted_azure_bluet": "Hüstoniya Saxsısı", "block.minecraft.potted_bamboo": "Dibçəkli Bambuk", "block.minecraft.potted_birch_sapling": "Dibçəkli Ağcaqayın Fidanı", "block.minecraft.potted_blue_orchid": "Dibç<PERSON><PERSON><PERSON> göy səhləb", "block.minecraft.potted_brown_mushroom": "Qəhvəyi Göbələk Saxsısı", "block.minecraft.potted_cactus": "Dibç<PERSON><PERSON><PERSON>", "block.minecraft.potted_cherry_sapling": "Saxsı Albalı Fidanı", "block.minecraft.potted_closed_eyeblossom": "Qazanmış göz qapağı çiçək", "block.minecraft.potted_cornflower": "Dibçəkli Göy Reyhan", "block.minecraft.potted_crimson_fungus": "Dibç<PERSON><PERSON><PERSON> al göbələk", "block.minecraft.potted_crimson_roots": "Dib<PERSON><PERSON><PERSON><PERSON> al köklər", "block.minecraft.potted_dandelion": "Zəncirotu Dibçəyi", "block.minecraft.potted_dark_oak_sapling": "Qara Palıd Fidanı Saxsısı", "block.minecraft.potted_dead_bush": "Dibçəkli Ölü Bitki", "block.minecraft.potted_fern": "Qı<PERSON>ı <PERSON>ə<PERSON>", "block.minecraft.potted_flowering_azalea_bush": "Dibç<PERSON><PERSON><PERSON> çiçəkləyən azaliya", "block.minecraft.potted_jungle_sapling": "Cəngəllik Ağacı Fidanı Saxsısı", "block.minecraft.potted_lily_of_the_valley": "İnciçiçəyi-da bir qazan", "block.minecraft.potted_mangrove_propagule": "<PERSON>b<PERSON><PERSON><PERSON><PERSON> manqrov fidanı", "block.minecraft.potted_oak_sapling": "Palıd Fidanı Saxsısı", "block.minecraft.potted_open_eyeblossom": "Saksıda Açıq Göz Ç<PERSON>çəyi", "block.minecraft.potted_orange_tulip": "Narıncı Lalə Saxsısı", "block.minecraft.potted_oxeye_daisy": "Dibçəkli Çobanyastığı", "block.minecraft.potted_pale_oak_sapling": "Saxsıda Ağ Palıd Şitilı", "block.minecraft.potted_pink_tulip": "Dibçəkli Çəhrayı Lalə", "block.minecraft.potted_poppy": "<PERSON>b<PERSON><PERSON><PERSON><PERSON> Lalə", "block.minecraft.potted_red_mushroom": "Qırmızı Göbələk Saxsısı", "block.minecraft.potted_red_tulip": "Qırmızı Lalə Saxsısı", "block.minecraft.potted_spruce_sapling": "Ladin Fidanı Saxsısı", "block.minecraft.potted_torchflower": "Saxsı Məşəl Çiçəyi", "block.minecraft.potted_warped_fungus": "Dibçəkli əyri gö<PERSON>ələk", "block.minecraft.potted_warped_roots": "Dibçəkli əyri köklər", "block.minecraft.potted_white_tulip": "Dibçəkli A<PERSON> Lalə", "block.minecraft.potted_wither_rose": "Dibçəkli Vizer Gülü", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON> qar", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON> qar qazanı", "block.minecraft.powered_rail": "Gü<PERSON><PERSON>ə<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON><PERSON> kə<PERSON>ə", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON> pill<PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "Balqabaq", "block.minecraft.pumpkin_stem": "Balqabaq Sapı", "block.minecraft.purple_banner": "Bənövşəyi Bayraq", "block.minecraft.purple_bed": "Bənövşəyi çarpayı", "block.minecraft.purple_candle": "Bənövşəyi şam", "block.minecraft.purple_candle_cake": "Bənövşəyi şamlı tort", "block.minecraft.purple_carpet": "Bənövşəyi Xalça", "block.minecraft.purple_concrete": "Bənövş<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "Bənövşəyi Sement", "block.minecraft.purple_glazed_terracotta": "Bənövşəyi Şirli Terrakotta", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Bənövşəyi Rəngli Şüşə", "block.minecraft.purple_stained_glass_pane": "Bənovşəyi Rəngli İncə Şüşə", "block.minecraft.purple_terracotta": "Bənövşəyi terrakotta", "block.minecraft.purple_wool": "Bənovş<PERSON>yi Yun", "block.minecraft.purpur_block": "Purpur Bloku", "block.minecraft.purpur_pillar": "P<PERSON><PERSON> Stün", "block.minecraft.purpur_slab": "Pur<PERSON> Pillə", "block.minecraft.purpur_stairs": "Purpur Pilləkən", "block.minecraft.quartz_block": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "<PERSON>am mis bloku", "block.minecraft.raw_gold_block": "<PERSON><PERSON> qı<PERSON>ıl bloku", "block.minecraft.raw_iron_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_banner": "Qırmızı Bayraq", "block.minecraft.red_bed": "Qırmızı Çarpayı", "block.minecraft.red_candle": "Qırmızı şam", "block.minecraft.red_candle_cake": "Qırmızı şamlı tort", "block.minecraft.red_carpet": "Qırmızı Xalça", "block.minecraft.red_concrete": "Qırmızı Beton", "block.minecraft.red_concrete_powder": "Qırmızı Sement", "block.minecraft.red_glazed_terracotta": "Qırmızı Şirli Terrakotta", "block.minecraft.red_mushroom": "Qırmızı Göbələk", "block.minecraft.red_mushroom_block": "Qırmızı Göbələk Bloku", "block.minecraft.red_nether_brick_slab": "Qırmızı Nezer kərpici pilləsi", "block.minecraft.red_nether_brick_stairs": "Qırmızı Nezer kərpici pilləkanı", "block.minecraft.red_nether_brick_wall": "Qırmızı Nezer kərpici divarı", "block.minecraft.red_nether_bricks": "Qırmızı Nether Kərpici", "block.minecraft.red_sand": "Qırmızı Qum", "block.minecraft.red_sandstone": "Qırmızı Qumdaşı", "block.minecraft.red_sandstone_slab": "Qırmızı Qumdaşı Pillə", "block.minecraft.red_sandstone_stairs": "Qırmızı Qumdaşı Pilləkən", "block.minecraft.red_sandstone_wall": "Qırmızı Qumdaşı Divar", "block.minecraft.red_shulker_box": "Qırmızı Shulker Qutusu", "block.minecraft.red_stained_glass": "Qırmızı Rəngli Şüşə", "block.minecraft.red_stained_glass_pane": "Qırmızı Rəngli İncə Şüşə", "block.minecraft.red_terracotta": "Qırmızı Terrakotta", "block.minecraft.red_tulip": "Qırmızı Lalə", "block.minecraft.red_wool": "Qırmızı Yun", "block.minecraft.redstone_block": "Qızıl Daş Bloku", "block.minecraft.redstone_lamp": "Qızıl Daş Lampası", "block.minecraft.redstone_ore": "Qızıl Daş Filizi", "block.minecraft.redstone_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.redstone_wall_torch": "Kızıltaş Duvar Meşalesi", "block.minecraft.redstone_wire": "Qırmızı daş simi", "block.minecraft.reinforced_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>", "block.minecraft.repeater": "Q<PERSON>z<PERSON>l Daş Təzələyici", "block.minecraft.repeating_command_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_block": "Qatran bloku", "block.minecraft.resin_brick_slab": "Q<PERSON><PERSON>", "block.minecraft.resin_brick_stairs": "Q<PERSON><PERSON>", "block.minecraft.resin_brick_wall": "Qatran <PERSON>", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.resin_clump": "Qatran Parçası", "block.minecraft.respawn_anchor": "Respaun mayakı", "block.minecraft.rooted_dirt": "Kök<PERSON><PERSON> to<PERSON>aq", "block.minecraft.rose_bush": "Qızılgül Kolu", "block.minecraft.sand": "<PERSON><PERSON>", "block.minecraft.sandstone": "Qumdaşı", "block.minecraft.sandstone_slab": "Qumdaşı Pillə", "block.minecraft.sandstone_stairs": "Qumdaşı Pilləkən", "block.minecraft.sandstone_wall": "Qumdaşı Divar", "block.minecraft.scaffolding": "Səhnəyə", "block.minecraft.sculk": "Skalk", "block.minecraft.sculk_catalyst": "Skalk Katalizörü", "block.minecraft.sculk_sensor": "Skalk sensoru", "block.minecraft.sculk_shrieker": "Qışqıran skalk", "block.minecraft.sculk_vein": "Skalk damarı", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON>z Fənə<PERSON>", "block.minecraft.sea_pickle": "Dəniz xiyarı", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "<PERSON><PERSON><PERSON>n nöqtəsi təyin o<PERSON>u", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "Göbə<PERSON>ək lampası", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Skelet Kəlləsi", "block.minecraft.skeleton_wall_skull": "Skelet başı", "block.minecraft.slime_block": "Palçıq Bloku", "block.minecraft.small_amethyst_bud": "Kiçik ametist tum<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.small_dripleaf": "Kiçik damcıyarpaq", "block.minecraft.smithing_table": "Dəmirçi Masası", "block.minecraft.smoker": "Şəhərim:", "block.minecraft.smooth_basalt": "<PERSON><PERSON> baz<PERSON>", "block.minecraft.smooth_quartz": "<PERSON><PERSON> bloku", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_red_sandstone": "Düzgün Qırmızı Qumdaşı", "block.minecraft.smooth_red_sandstone_slab": "Düzgün Qırmızı Qumdaşı Pillə", "block.minecraft.smooth_red_sandstone_stairs": "Düzgün Qırmızı Qumdaşı Pilləkən", "block.minecraft.smooth_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>llə", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON> Pilləkə<PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Qar Bloku", "block.minecraft.soul_campfire": "<PERSON>uh tonqalı", "block.minecraft.soul_fire": "<PERSON><PERSON> alovu", "block.minecraft.soul_lantern": "<PERSON><PERSON>", "block.minecraft.soul_sand": "<PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON>ğ<PERSON>", "block.minecraft.soul_torch": "<PERSON>uh məşəli", "block.minecraft.soul_wall_torch": "<PERSON>uh divarı məşəli", "block.minecraft.spawn.not_valid": "Eviniz və ya yüklü yenidən yaranma lövbəriniz yoxdur və ya məhv edilib", "block.minecraft.spawner": "<PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Interact with Spawn Egg:", "block.minecraft.spawner.desc2": "<PERSON><PERSON> təyin edir", "block.minecraft.sponge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "Spor çiçəyi", "block.minecraft.spruce_button": "<PERSON><PERSON>", "block.minecraft.spruce_door": "<PERSON><PERSON>", "block.minecraft.spruce_fence": "<PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON>", "block.minecraft.spruce_leaves": "<PERSON><PERSON>", "block.minecraft.spruce_log": "<PERSON><PERSON>k<PERSON>", "block.minecraft.spruce_planks": "<PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON>", "block.minecraft.spruce_sapling": "<PERSON><PERSON>", "block.minecraft.spruce_sign": "<PERSON><PERSON>", "block.minecraft.spruce_slab": "<PERSON><PERSON>", "block.minecraft.spruce_stairs": "<PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "<PERSON>din Tələ Qapısı", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON> odu<PERSON>", "block.minecraft.sticky_piston": "Ya<PERSON>ış<PERSON><PERSON>", "block.minecraft.stone": "Daş", "block.minecraft.stone_brick_slab": "<PERSON><PERSON>", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON>", "block.minecraft.stone_brick_wall": "<PERSON><PERSON>", "block.minecraft.stone_bricks": "<PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON> T<PERSON>yziq <PERSON>", "block.minecraft.stone_slab": "<PERSON><PERSON>llə", "block.minecraft.stone_stairs": "<PERSON>ş Pilləkən", "block.minecraft.stonecutter": "<PERSON><PERSON> k<PERSON>", "block.minecraft.stripped_acacia_log": "Soyulmuş akasiya k<PERSON>ü<PERSON>", "block.minecraft.stripped_acacia_wood": "Soyulmuş Akasya Kütüğü", "block.minecraft.stripped_bamboo_block": "Soyulmuş Bambuk Bloku", "block.minecraft.stripped_birch_log": "Soyulmuş ağcaqayın kötüyü", "block.minecraft.stripped_birch_wood": "Soyulmuş Huş Kütüğü", "block.minecraft.stripped_cherry_log": "Soyulmuş Albalı Girişi", "block.minecraft.stripped_cherry_wood": "Soyulmuş Albalı Ağacı", "block.minecraft.stripped_crimson_hyphae": "Soyulmuş al hifa", "block.minecraft.stripped_crimson_stem": "Soyulmuş al saplaq", "block.minecraft.stripped_dark_oak_log": "Soyulmuş Qara Palıd Kötüyü", "block.minecraft.stripped_dark_oak_wood": "Soyulmuş Qara Palıd Odunu", "block.minecraft.stripped_jungle_log": "Soyulmuş cəngəllik kötüyü", "block.minecraft.stripped_jungle_wood": "Soyulmuş Orman Ağacı Kütüğü", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_log": "Soyulmuş Palıd Kö<PERSON>ü<PERSON>ü", "block.minecraft.stripped_oak_wood": "Soyulmuş Palıd <PERSON>", "block.minecraft.stripped_pale_oak_log": "Soyulmuş Ağ Palıd Odunu", "block.minecraft.stripped_pale_oak_wood": "Soyulmuş Solğun Palıd Taxta", "block.minecraft.stripped_spruce_log": "Soyulmuş küknar kötüyü", "block.minecraft.stripped_spruce_wood": "Soyulmuş küknar kötüyü", "block.minecraft.stripped_warped_hyphae": "Soyulmuş əyri hifalar", "block.minecraft.stripped_warped_stem": "Soyulmuş əyri saplaq", "block.minecraft.structure_block": "Struktur Bloku", "block.minecraft.structure_void": "Struk<PERSON>lu<PERSON>", "block.minecraft.sugar_cane": "Şəkərqamışı", "block.minecraft.sunflower": "Günəbaxan", "block.minecraft.suspicious_gravel": "Şüb<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON>n giləmeyvə kol", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_grass": "Uzun Ot", "block.minecraft.tall_seagrass": "<PERSON><PERSON>n <PERSON>", "block.minecraft.target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Sınaq Bloku", "block.minecraft.test_instance_block": "Sınaq Nümunə Bloku", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT partlayışları deaktiv edilib", "block.minecraft.torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON> ç<PERSON>əyi", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON> çiçəyi məhsulu", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.trial_spawner": "S<PERSON>naq <PERSON>", "block.minecraft.tripwire": "<PERSON>ə<PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON><PERSON>ğı", "block.minecraft.tube_coral": "<PERSON><PERSON>ə<PERSON>", "block.minecraft.tube_coral_block": "<PERSON><PERSON> mərcan bloku", "block.minecraft.tube_coral_fan": "Bubble Coral Fan", "block.minecraft.tube_coral_wall_fan": "<PERSON>ru mərcan divarı", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON> kə<PERSON>əs", "block.minecraft.tuff_brick_stairs": "<PERSON><PERSON> kə<PERSON>", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON>", "block.minecraft.tuff_bricks": "<PERSON><PERSON>", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Tısbağa yumurtası", "block.minecraft.twisting_vines": "Burulmuş sarmaşıq", "block.minecraft.twisting_vines_plant": "Burulmuş sarmaş<PERSON>q <PERSON>", "block.minecraft.vault": "Tonoz", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON>ıq", "block.minecraft.vine": "Sarmaşıq", "block.minecraft.void_air": "Boşluq ha<PERSON>ı", "block.minecraft.wall_torch": "<PERSON><PERSON> mə<PERSON>ə<PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON> dü<PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON> qapı", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON> Çəpə<PERSON>", "block.minecraft.warped_fence_gate": "Əyri Çəpər Qapısı", "block.minecraft.warped_fungus": "<PERSON><PERSON>i göbələk", "block.minecraft.warped_hanging_sign": "Əzilmiş Asma İşarəsi", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_nylium": "Əyri nilium", "block.minecraft.warped_planks": "Əyri taxta", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON><PERSON> tə<PERSON> lö<PERSON>", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON> kö<PERSON>lər", "block.minecraft.warped_sign": "Əyri lövhə", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON> pillə", "block.minecraft.warped_stairs": "<PERSON>yri Pilləkən", "block.minecraft.warped_stem": "Əyri saplaq", "block.minecraft.warped_trapdoor": "Əyri tələ qapısı", "block.minecraft.warped_wall_hanging_sign": "Əzilmiş Divar Asma Ni<PERSON>anı", "block.minecraft.warped_wall_sign": "Çarpan <PERSON> i<PERSON>", "block.minecraft.warped_wart_block": "Əyri ziyil bloku", "block.minecraft.water": "Su", "block.minecraft.water_cauldron": "Su Qazanı", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON> işlənmiş mis", "block.minecraft.waxed_copper_block": "Mumlanmış mis bloku", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON>", "block.minecraft.waxed_copper_door": "Mumlu Mis Qapı", "block.minecraft.waxed_copper_grate": "Mumlu Mis Bar<PERSON>ı<PERSON>", "block.minecraft.waxed_copper_trapdoor": "Mumlu Mis Tələ Qapısı", "block.minecraft.waxed_cut_copper": "Mumlanmış kəsilmiş mis", "block.minecraft.waxed_cut_copper_slab": "Mumlanmış kəsilmiş mis pillə", "block.minecraft.waxed_cut_copper_stairs": "Mumlanmış Kəsilmiş Mis Pilləkən", "block.minecraft.waxed_exposed_chiseled_copper": "Mumlu Açıq <PERSON> Mis", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON> müda<PERSON>ə<PERSON>z qalmış mis", "block.minecraft.waxed_exposed_copper_bulb": "Mumlu oksidləşdirilmiş mis lampa", "block.minecraft.waxed_exposed_copper_door": "Mumlu oksidləşdirilmiş mis lampa", "block.minecraft.waxed_exposed_copper_grate": "Mumlu Açıq Mis Barmaqlıq", "block.minecraft.waxed_exposed_copper_trapdoor": "Mumlu Açıq <PERSON>", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON> müda<PERSON>ə<PERSON><PERSON> qalmış kəsilmiş mis", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON> müda<PERSON>ə<PERSON><PERSON> qalmış kəsilmiş mis pillə", "block.minecraft.waxed_exposed_cut_copper_stairs": "<PERSON><PERSON> müda<PERSON>ə<PERSON><PERSON> qalmış kəsilmiş mis pilləkən", "block.minecraft.waxed_oxidized_chiseled_copper": "Mumlu Oksidləşdirilmiş Yonmuş Mis", "block.minecraft.waxed_oxidized_copper": "Mumlanmış okdisləşmiş mis", "block.minecraft.waxed_oxidized_copper_bulb": "Mumlu oksidləşdirilmiş mis lampa", "block.minecraft.waxed_oxidized_copper_door": "Mumlu Oksidləşdirilmiş Mis Qapı", "block.minecraft.waxed_oxidized_copper_grate": "Mumlu oksidləşmiş mis ızgarası", "block.minecraft.waxed_oxidized_copper_trapdoor": "Mumlu Oksidləşdirilmiş Mis Qapağı", "block.minecraft.waxed_oxidized_cut_copper": "Mumlanmış oksidləşmiş kəsilmiş mis", "block.minecraft.waxed_oxidized_cut_copper_slab": "Mumlanmış oksidləş<PERSON>ş kəsilmiş mis pillə", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Mumlanmış okdis<PERSON><PERSON><PERSON><PERSON>ş kəsilmiş mis pilləkən", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON>u ilə işlən<PERSON>ş kəsilmiş mis", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON> solmuş mis", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON>u ilə işlənmiş mis lampa", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON>u ilə işlənmiş Mis Qapı", "block.minecraft.waxed_weathered_copper_grate": "Ba<PERSON><PERSON>u ilə işlənmiş mis ızgara", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON><PERSON>u ilə işlənmiş Mis Qapı", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON> solmuş kəsilmiş mis", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON> solmuş kəsilmiş mis pillə", "block.minecraft.waxed_weathered_cut_copper_stairs": "<PERSON><PERSON> solmuş kəsilmiş mis pilləkən", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zərb edil<PERSON>ş mis", "block.minecraft.weathered_copper": "Solmuş mis", "block.minecraft.weathered_copper_bulb": "İşlənmiş Mis Lampa", "block.minecraft.weathered_copper_door": "İşlənmiş Mis Qapı", "block.minecraft.weathered_copper_grate": "İşlənmiş Mis Barmaqlıq", "block.minecraft.weathered_copper_trapdoor": "İşlənmiş Mis Qapı", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON> kə<PERSON>lm<PERSON>ş mis", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> kəsilmiş mis pillə", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON>ş Mis Pilləkən", "block.minecraft.weeping_vines": "Ağlayan sa<PERSON>şıq", "block.minecraft.weeping_vines_plant": "Ağlayan <PERSON><PERSON>", "block.minecraft.wet_sponge": "İslaq <PERSON>", "block.minecraft.wheat": "B<PERSON>ğda əkini", "block.minecraft.white_banner": "<PERSON>ğ <PERSON>", "block.minecraft.white_bed": "Ağ çarpayı", "block.minecraft.white_candle": "<PERSON><PERSON>", "block.minecraft.white_candle_cake": "Ağ şamlı tort", "block.minecraft.white_carpet": "<PERSON>ğ <PERSON>al<PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON>ğ Sement", "block.minecraft.white_glazed_terracotta": "Ağ Şirli <PERSON>", "block.minecraft.white_shulker_box": "<PERSON><PERSON>", "block.minecraft.white_stained_glass": "<PERSON><PERSON>üş<PERSON>", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON> Rəngli İncə Şüşə", "block.minecraft.white_terracotta": "Ağ terrakotta", "block.minecraft.white_tulip": "<PERSON><PERSON>", "block.minecraft.white_wool": "<PERSON><PERSON>", "block.minecraft.wildflowers": "Yabanı Gül", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_wall_skull": "Vizer Skelet başı", "block.minecraft.yellow_banner": "Sarı Bayraq", "block.minecraft.yellow_bed": "Sarı çarpayı", "block.minecraft.yellow_candle": "Sarı Şam", "block.minecraft.yellow_candle_cake": "Sarı şam ilə tort", "block.minecraft.yellow_carpet": "Sarı Xalça", "block.minecraft.yellow_concrete": "Sarı Beton", "block.minecraft.yellow_concrete_powder": "Sarı Sement", "block.minecraft.yellow_glazed_terracotta": "Sarı şirli terrakotta", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> Shulker <PERSON>", "block.minecraft.yellow_stained_glass": "Sarı Rəngli Şüşə", "block.minecraft.yellow_stained_glass_pane": "Sarı Rəngli İncə Şüşə", "block.minecraft.yellow_terracotta": "Sarı terrakotta", "block.minecraft.yellow_wool": "Sarı Yun", "block.minecraft.zombie_head": "Zombi Başı", "block.minecraft.zombie_wall_head": "Zombi Başı", "book.byAuthor": "Yazar: %1$s", "book.edit.title": "Kitab Redaktə Ekranı", "book.editTitle": "Kitab Başlığı:", "book.finalizeButton": "İmzala və bağla", "book.finalizeWarning": "Not! Əgər kitabı im<PERSON>, bundan belə üzərində dəyişiklik edilə bilməz.", "book.generation.0": "Orijinal", "book.generation.1": "Orijinalın n<PERSON>", "book.generation.2": "<PERSON>ir nüsxənin nüsxəsi", "book.generation.3": "Paramparça", "book.invalid.tag": "* Mümkünsüz kitab etiketi *", "book.pageIndicator": "Səyifə %1$s/%2$s", "book.page_button.next": "Növ<PERSON><PERSON><PERSON> səhifə", "book.page_button.previous": "Əvvəlki səhifə", "book.sign.title": "Kitab İmzalama Ekranı", "book.sign.titlebox": "Başlıq", "book.signButton": "İmzala", "book.view.title": "Kitab Baxış Ekranı", "build.tooHigh": "Bina üçün hündürlük həddi %s", "chat.cannotSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>t mesajı göndərilə bilmədi", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Teleport olmak üçün klikləyin", "chat.copy": "<PERSON><PERSON><PERSON>", "chat.copy.click": "Panoya kopyalamaq üçün vurun", "chat.deleted_marker": "Bu çat mesajı server tərəfindən silinib.", "chat.disabled.chain_broken": "<PERSON><PERSON>nc<PERSON> qırıldığı üçün çat deaktiv edildi. Lütfən, yenidən qoşulmağa cəhd edin.", "chat.disabled.expiredProfileKey": "Müddəti bitmiş profil açıq açarı səbəbindən çat deaktiv edilib. Lütfən, yenidən qoşulmağa cəhd edin.", "chat.disabled.invalid_command_signature": "Komandada gözlənilməz və ya çatışmayan komanda arqument imzaları var idi.", "chat.disabled.invalid_signature": "Söhbətdə səhv imza var idi. Zəhmət olmasa yenidən qoşulmağa çalışın.", "chat.disabled.launcher": "Çat başladıcı nizamlamalarında bağlanıb. <PERSON>j göndəril<PERSON> bilmir.", "chat.disabled.missingProfileKey": "Profil açıq açarı olmadığına görə çat deaktiv edilib. Lütfən, yenidən qoşulmağa cəhd edin.", "chat.disabled.options": "Alıcı nizamlamalarında çat bağlanılıb.", "chat.disabled.out_of_order_chat": "Çat sıradan çıxdı. Sisteminizin saatı dəyişdi?", "chat.disabled.profile": "<PERSON><PERSON>b qurğuları ilə çata icazə verilmir. Daha çox məlumat üçün yenə '%s' düyməsini basın.", "chat.disabled.profile.moreInfo": "Hesabın ayarları ilə sizə çat istifadə etməkinin icazəsi yoxdur. Mesaj göndərilmək və baxışlanmaq bilməz.", "chat.editBox": "Çat", "chat.filtered": "Server tərə<PERSON><PERSON><PERSON><PERSON>.", "chat.filtered_full": "server sizin ismarıclarınızı bəzi oyunçular üçün dəyişib.", "chat.link.confirm": "Bu web saytını açmaq istədiyinizə əminsiniz?", "chat.link.confirmTrusted": "Bu bağlantını açmaq və ya panoya kopyalamaq istədiyinizdən əminsiz?", "chat.link.open": "Brauzerdə aç", "chat.link.warning": "Güvənmədiyiniz şəxslərdən gələn bağlantıları əsla açmayın!", "chat.queue": "[+%s gözlənilən sətir]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server et<PERSON><PERSON><PERSON><PERSON> mesaj <PERSON>.", "chat.tag.modified": "Mesaj server tərəfindən dəyişdirildi. Orijinal:", "chat.tag.not_secure": "Doğrulanmamış mesaj. Məlumat verilə bilməz.", "chat.tag.system": "Server mesajı. Məlumat verilə bilməz.", "chat.tag.system_single_player": "Server mesajı.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s, %s mücadiləsini tamamladı", "chat.type.advancement.goal": "%s, %s hədəfinə çatdı", "chat.type.advancement.task": "%s, %s inkişafını etdi", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON> q<PERSON>u", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %%ilə", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s, %s dedi", "chat.validation_error": "Çatın doğrulanması xətası", "chat_screen.message": "Göndəriləcək mesaj: %s", "chat_screen.title": "Çat ekranı", "chat_screen.usage": "<PERSON><PERSON>ı daxil et və göndərmək üçün Enter bas", "chunk.toast.checkLog": "Ətraflı məlumat üçün qeydlərə baxın", "chunk.toast.loadFailure": "Ətraflı məlumat üçün qeydlərə baxın", "chunk.toast.lowDiskSpace": "Az disk sahəsi!", "chunk.toast.lowDiskSpace.description": "Az disk sahəsi!", "chunk.toast.saveFailure": "%s-də yığını saxlamaq alınmadı", "clear.failed.multiple": "%s oyunçusunda heç bir şey tapılmadı", "clear.failed.single": "%s oyunçusunda heç bir şey tapılmadı", "color.minecraft.black": "Qara", "color.minecraft.blue": "G<PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON>yi", "color.minecraft.gray": "Boz", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Açıq <PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON>q <PERSON>", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Narıncı", "color.minecraft.pink": "Çəhrayı", "color.minecraft.purple": "Bənövşəyi", "color.minecraft.red": "Qırmızı", "color.minecraft.white": "Ağ", "color.minecraft.yellow": "Sarı", "command.context.here": "<--[BURADA]", "command.context.parse_error": "%s mövqeyində %s: %s", "command.exception": "Komanda təhlil edilə bilmədi: %s", "command.expected.separator": "Bir mübahisənin bitməsi üçün boşluq boşlu<PERSON><PERSON>u gö<PERSON>lədilər, ancaq son məlumatlar tapıldı", "command.failed": "Bu ə<PERSON>ri icra edərkən gözlənilməz bir səhv baş verdi", "command.forkLimit": "Maks<PERSON>um kontekst sayına çatıldı (%s)", "command.unknown.argument": "<PERSON><PERSON><PERSON><PERSON><PERSON> yanlış dəlil", "command.unknown.command": "Bilinməyən və ya natamam komanda, səhv üçün aşağıya baxın", "commands.advancement.criterionNotFound": "'%1$s' inkişafı '%2$s' meyarını ehtiva etmir", "commands.advancement.grant.criterion.to.many.failure": "'%2$s' inkişafının '%1$s' meyarı %3$s'ə verilə bilmədi. Oyunçu meyara hal hazırda sahib", "commands.advancement.grant.criterion.to.many.success": "'%2$s' inkişafının '%1$s' meyarı %3$s'ə verildi", "commands.advancement.grant.criterion.to.one.failure": "'%2$s' inkişafının '%1$s' meyarı %3$s'ə verilə bilmədi. Oyunçu meyara hal hazırda sahib", "commands.advancement.grant.criterion.to.one.success": "'%2$s' inkişafının '%1$s' meyarı %3$s'ə verildi", "commands.advancement.grant.many.to.many.failure": "Verə bilmədi %s üçün irəliləyişlər %s onlarda olduğu kimi oyunçular", "commands.advancement.grant.many.to.many.success": "Verilir %s üçün irəliləyişlər %s oyunçular", "commands.advancement.grant.many.to.one.failure": "Verə bilmədi %s üçün irəliləyişlər %s onlarda olduğu kimi oyunçular", "commands.advancement.grant.many.to.one.success": "'%s' inkişafının hamısı %s'ə verildi", "commands.advancement.grant.one.to.many.failure": "'%s' inkişafı %s'ə verilə bilmədi. Oyunçu inkişafa hal hazırda sahib", "commands.advancement.grant.one.to.many.success": "Verilir %s üçün irəliləyişlər %s oyunçular", "commands.advancement.grant.one.to.one.failure": "'%s' inkişafı %s'ə verilə bilmədi. Oyunçu inkişafa hal hazırda sahib", "commands.advancement.grant.one.to.one.success": "'%s' inkişafının hamısı %s'ə verildi", "commands.advancement.revoke.criterion.to.many.failure": "'%2$s' inkişafının '%1$s' meyarı %3$s'dən alına bilmədi. Oyunçu meyara hal hazırda sahib deyil", "commands.advancement.revoke.criterion.to.many.success": "'%2$s' inkişafının '%1$s' meyarı %3$s'dən alındı", "commands.advancement.revoke.criterion.to.one.failure": "'%2$s' inkişafının '%1$s' meyarı %3$s'dən alına bilmədi. Oyunçu meyara hal hazırda sahib deyil", "commands.advancement.revoke.criterion.to.one.success": "'%2$s' inkişafının '%1$s' meyarı %3$s'dən alındı", "commands.advancement.revoke.many.to.many.failure": "'%s' inkişafı %s'dən alına bilmədi. Oyunçu inkişafa hal hazırda sahib deyil", "commands.advancement.revoke.many.to.many.success": "%s'dən '%s' inkişafının hamısı alındı", "commands.advancement.revoke.many.to.one.failure": "'%s' inkişafı %s'dən alına bilmədi. Oyunçu inkişafa hal hazırda sahib deyil", "commands.advancement.revoke.many.to.one.success": "%s'dən '%s' inkişafının hamısı alındı", "commands.advancement.revoke.one.to.many.failure": "'%s' inkişafı %s'dən alına bilmədi. Oyunçu inkişafa hal hazırda sahib deyil", "commands.advancement.revoke.one.to.many.success": "%s'dən '%s' inkişafının hamısı alındı", "commands.advancement.revoke.one.to.one.failure": "'%s' inkişafı %s'dən alına bilmədi. Oyunçu inkişafa hal hazırda sahib deyil", "commands.advancement.revoke.one.to.one.success": "%s'dən '%s' inkişafının hamısı alındı", "commands.attribute.base_value.get.success": "%s xüsusiyyətinin %s varlığı üçün baza dəyəri %s", "commands.attribute.base_value.reset.success": "\"%s varlığı üçün %s xüsusiyyəti, varsayılan %s dəyərinə sıfırlandı\"", "commands.attribute.base_value.set.success": "%s xüsusiyyətinin %s varlığı üçün baza dəyəri %s kimi müəyyən olunub", "commands.attribute.failed.entity": "%s bu əmr üçün etibarlı bir təşkilat deyil", "commands.attribute.failed.modifier_already_present": "%s təyinedicisi hazırda %s xüsusiyyətli %s varlığı üçündür", "commands.attribute.failed.no_attribute": "%s varlığının %s xüsusiyyəti yoxdur", "commands.attribute.failed.no_modifier": "%s xüsusiyyətinin %s varlığı üçün %s təyinedicisi yoxdur", "commands.attribute.modifier.add.success": "%s təyinedicisi %s xüsusiyyətli %s varlığına əlavə olundu", "commands.attribute.modifier.remove.success": "%s təyinedicisi %s xüsusiyyətli %s varlığından silindi", "commands.attribute.modifier.value.get.success": "%s təyinedicisinin %s xüsusiyyətli %s varlığı üçün dəyəri %s", "commands.attribute.value.get.success": "%s xüsusiyyətinin %s varlığı üçün dəyəri %s", "commands.ban.failed": "Heçnə dəyişmədi. Bu Ip qadağan deyil", "commands.ban.success": "%s Ip adresi Blok oldu", "commands.banip.failed": "Heçnə dəyişmədi. Bu IP qadağan deyil", "commands.banip.info": "Bu qadağa %s oyunçuya təsir edir: %s", "commands.banip.invalid": "Yanlış IP ünvanı və ya naməlum oyunçu", "commands.banip.success": "%s IP adresi Blok oldu", "commands.banlist.entry": "%s qadağan edildi %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON>)", "commands.banlist.list": "<PERSON><PERSON><PERSON><PERSON> var:", "commands.banlist.none": "<PERSON><PERSON><PERSON><PERSON> yox<PERSON>r", "commands.bossbar.create.failed": "Artıq bossbar '%s' şəxsiyyəti ilə mövcuddur", "commands.bossbar.create.success": "Xüsusi bossbar %s yaratdı", "commands.bossbar.get.max": "Xüsusi bossbar %s %s dəyəri var", "commands.bossbar.get.players.none": "Xüsusi bossbar %s-də hazırda onlayn oyunçu yoxdur", "commands.bossbar.get.players.some": "%s xüsusi bossbar %s hazırda %s çalarları var: %s", "commands.bossbar.get.value": "Xüsusi bossbar %s %s dəyəri var", "commands.bossbar.get.visible.hidden": "Xüsusi bossbar %s hazırda göstərilir", "commands.bossbar.get.visible.visible": "Xüsusi bossbar %s hazırda göstərilir", "commands.bossbar.list.bars.none": "<PERSON><PERSON><PERSON> bossbars yoxdur", "commands.bossbar.list.bars.some": "%s x<PERSON><PERSON>i bossbarları fəaldır: %s", "commands.bossbar.remove.success": "Xüsusi bossbar %s yaratdı", "commands.bossbar.set.color.success": "Xüsusi bossbar %s rəng də<PERSON>", "commands.bossbar.set.color.unchanged": "Heçnə dəyişmədi. Artıq bu bossbarın rəngidir", "commands.bossbar.set.max.success": "Xüsusi bossbar %s %s dəyəri var", "commands.bossbar.set.max.unchanged": "Heçnə dəyişmədi. Artıq bu bossbarın adı", "commands.bossbar.set.name.success": "Üsusi bossbar %s adı dəyişdirildi", "commands.bossbar.set.name.unchanged": "Heçnə dəyişmədi. Artıq bu bossbarın adı", "commands.bossbar.set.players.success.none": "Xüsusi bossbar %s <PERSON>ı<PERSON> oyun<PERSON>u yoxdur", "commands.bossbar.set.players.success.some": "%s xüsusi bossbar%s indi %s çalarları var: %s", "commands.bossbar.set.players.unchanged": "Heçnə dəyişmədi. Bu oyunçular artıq bossbarın içindədir və əlavə etmək və ya çıxarmaq üçün heç kim yoxdur", "commands.bossbar.set.style.success": "Xüsusi bossbar %s rəng də<PERSON>", "commands.bossbar.set.style.unchanged": "Heçnə dəyişmədi. Artıq bu bossbarın adı", "commands.bossbar.set.value.success": "Xüsusi bossbar %s %s dəyəri var", "commands.bossbar.set.value.unchanged": "Heçnə dəyişmədi. <PERSON>ıq bu bossbarın dəyəri", "commands.bossbar.set.visibility.unchanged.hidden": "Heçnə dəyişmədi. Bu Ip qadağan deyil", "commands.bossbar.set.visibility.unchanged.visible": "Heçnə dəyişmədi. Bu Ip qadağan deyil", "commands.bossbar.set.visible.success.hidden": "Xüsusi bossbar %s <PERSON>ı<PERSON>", "commands.bossbar.set.visible.success.visible": "Xüsusi bossbar %s <PERSON>ı<PERSON>", "commands.bossbar.unknown": "Artıq bossbar '%s' şəxsiyyəti ilə mövcuddur", "commands.clear.success.multiple": "%s element(lər) %s oyunçudan silindi", "commands.clear.success.single": "%s element(lər) %s oyunçusundan silindi", "commands.clear.test.multiple": "%s oyunçuda %s uyğun element(lər) tapıldı", "commands.clear.test.single": "%s oyunçuda %s uyğun element(lər) tapıldı", "commands.clone.failed": "<PERSON><PERSON> bir blok kopyalana bilmədi", "commands.clone.overlap": "Qaynaq və hədəf örtüşə bilməz", "commands.clone.success": "%s bloku uğurla klonlaşdırılır", "commands.clone.toobig": "Göstərilən ərazidə çox sayda blok (maksimum %s, göstərilən %s)", "commands.damage.invulnerable": "<PERSON>ə<PERSON><PERSON><PERSON> ve<PERSON> zərər növünə qarşı toxunulmazdır", "commands.damage.success": "%s zədəsi %s-ə tətbiq edildi", "commands.data.block.get": "%s anbarda %s miqyas amilindən sonra %s edir %s", "commands.data.block.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> blo<PERSON> k<PERSON>l", "commands.data.block.modified": "%s,%s,%s modifikasiya edilmiş blok məlumatları", "commands.data.block.query": "%s,%s,%s aşağıdakı blok məlumatlarına malikdir: %s", "commands.data.entity.get": "%s anbarda %s miqyas amilindən sonra %s edir %s", "commands.data.entity.invalid": "Oyunçu məlumatlarını dəyişdirmək olmur", "commands.data.entity.modified": "%s dəyişdirilmiş müəssisə məlumatları", "commands.data.entity.query": "%s aşağıdakı şəxs məlumatlarına malikdir: %s", "commands.data.get.invalid": "%s əldə etmək olmur; yalnız rəqəmli etiketlərə icazə verilir", "commands.data.get.multiple": "Bu arqument tək bir NBT dəyərini qəbul edir", "commands.data.get.unknown": "%s əldə etmək olmur; etiket yoxdur", "commands.data.merge.failed": "<PERSON>ç nə dəyişməyib. Göst<PERSON><PERSON><PERSON><PERSON> xüsusiyyətlər artıq bu mənalara malikdir", "commands.data.modify.expected_list": "Gözlənilən siyahı, alındı: %s", "commands.data.modify.expected_object": "Gözlənilən siyahı, alındı: %s", "commands.data.modify.expected_value": "Gözləni<PERSON>ən dəyər, alındı: %s", "commands.data.modify.invalid_index": "Yanlış siyahı indeksi: %s", "commands.data.modify.invalid_substring": "Yanlış alt sətir indeksləri: %s - %s", "commands.data.storage.get": "%s anbarda %s miqyas amilindən sonra %s edir %s", "commands.data.storage.modified": "Dəyişdirilmiş saxlama %s", "commands.data.storage.query": "Saxlama %s aşağıdakı məzmuna malikdir: %s", "commands.datapack.create.already_exists": "'%s' adlı paket artıq mövcuddur", "commands.datapack.create.invalid_full_name": "'%s' adlı yeni paket adı düzgün deyil", "commands.datapack.create.invalid_name": "'%s' adlı yeni paket adında düzgün olmayan simvollar var", "commands.datapack.create.io_failure": "'%s' adlı paket ya<PERSON>, qey<PERSON><PERSON><PERSON> yox<PERSON>ın", "commands.datapack.create.metadata_encode_failure": "'%s' adlı paket üçün metadatanı kodlaşdırmaq mümkün olmadı: %s", "commands.datapack.create.success": "'%s' adlı yeni boş paket yaradıldı", "commands.datapack.disable.failed": "'%s' paketi artıq aktivdir!", "commands.datapack.disable.failed.feature": "%s-də yığını saxlamaq alınmadı", "commands.datapack.enable.failed": "'%s' paketi artıq aktivdir!", "commands.datapack.enable.failed.no_flags": "'%s' paketini aktivləşdirmək mümkün deyil, çünki bu dünyada tələb olunan bayraqlar aktiv deyil: %s!", "commands.datapack.list.available.none": "Məlumat paketləri aktiv deyil", "commands.datapack.list.available.success": "Əlçatan %s data paket(lər) var: %s", "commands.datapack.list.enabled.none": "Məlumat paketləri aktiv deyil", "commands.datapack.list.enabled.success": "%s data paketi aktivləşdirilib: %s", "commands.datapack.modify.disable": "Disabling data pack %s", "commands.datapack.modify.enable": "Məlumat paketini aktivləşdirmək %s", "commands.datapack.unknown": "<PERSON><PERSON><PERSON> məlumat paketi '%s'", "commands.debug.alreadyRunning": "Xəta analizi profili artıq ba<PERSON>", "commands.debug.function.noRecursion": "Funksiya daxilində izlənilə bilməz", "commands.debug.function.noReturnRun": "İzləmə tərs başlanğıcda istifadə edilə bilməz", "commands.debug.function.success.multiple": "%s funksiyasından %s çıxış faylına %s əmr(lər) i izlənildi", "commands.debug.function.success.single": "'%s' funksiyasından %s çıxış faylına %s əmr(lər) i izlənildi", "commands.debug.function.traceFailed": "Funksiyanı izləmək alınmadı", "commands.debug.notRunning": "Xəta analizi profili b<PERSON>", "commands.debug.started": "<PERSON><PERSON><PERSON> tapma profili baş<PERSON>ı", "commands.debug.stopped": "Xəta analizi %s saniyə və  %s xətadan (hər saniyə %s xəta) sonra dayandırıldı", "commands.defaultgamemode.success": "Defolt oyun rejimi indi %s", "commands.deop.failed": "Heçnə dəyişmədi. Oyunçu operator deyil", "commands.deop.success": "%s bir server operatoru etdi", "commands.dialog.clear.multiple": "%s oyunçu üçün dialoq təmizləndi", "commands.dialog.clear.single": "%s oyunçular üçün dialoq təmizləndi", "commands.dialog.show.multiple": "%s oyunçulara dialoq gö<PERSON>ərildi", "commands.dialog.show.single": "%s <PERSON><PERSON>ün dialoq gö<PERSON>ril<PERSON>", "commands.difficulty.failure": "Çətin<PERSON> dəyişmə<PERSON>; artıq %s olaraq təyin edilmişdir", "commands.difficulty.query": "Çətinlik %s", "commands.difficulty.success": "Çətinlik %s olar<PERSON> təyin olundu", "commands.drop.no_held_items": "Varlıq əşya saxlaya bilməz", "commands.drop.no_loot_table": "Varlık %s loot masası yok", "commands.drop.no_loot_table.block": "%s bloku üçün qənimət cədvəli yoxdur", "commands.drop.success.multiple": "Düşürüldü %s * %s", "commands.drop.success.multiple_with_table": "Düşdü %s %s masa qənimət %s", "commands.drop.success.single": "Batdı %s %s", "commands.drop.success.single_with_table": "Düşdü %s %s masa qənimət %s", "commands.effect.clear.everything.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> aradan qaldırılması üçün təsiri yoxdur", "commands.effect.clear.everything.success.multiple": "%s'dən hər təsir silindi", "commands.effect.clear.everything.success.single": "%s'dən hər təsir silindi", "commands.effect.clear.specific.failed": "<PERSON>ə<PERSON><PERSON><PERSON> tələb olunan effekti vermir", "commands.effect.clear.specific.success.multiple": "Tətbiq olunan təsir %s ilə %s", "commands.effect.clear.specific.success.single": "%s təsirindən %s silindi", "commands.effect.give.failed": "Bu effekti tətbiq etmək olmur (hədəf ya təsirə qarşıdır, ya da güclü bir şey var)", "commands.effect.give.success.multiple": "Tətbiq olunan təsir %s ilə %s", "commands.effect.give.success.single": "Tətbiq olunan təsir %s ilə %s", "commands.enchant.failed": "Heçnə dəyişmədi. Hədəflərin əllərində heç bir maddə yoxdur və ya sehr tətbiq edilə bilməz", "commands.enchant.failed.entity": "%s bu əmr üçün uyğun obyekt deyil", "commands.enchant.failed.incompatible": "%s bu cazibəni dəstəkləyə bilmir", "commands.enchant.failed.itemless": "%s heç bir element tutmur", "commands.enchant.failed.level": "%s bu cazibə tərəfindən dəstəklənən %s səviyyəsindən yüksəkdir", "commands.enchant.success.multiple": "%s maddəsinə %s tətbiq edildi", "commands.enchant.success.single": "%s maddəsinə %s tətbiq edildi", "commands.execute.blocks.toobig": "Göstərilən ərazidə həddən artıq blok var (maksimum %s, müəyyən %s)", "commands.execute.conditional.fail": "Test keçdi", "commands.execute.conditional.fail_count": "Test keçdi, sayı: %s", "commands.execute.conditional.pass": "Test keçdi", "commands.execute.conditional.pass_count": "Test keçdi, sayı: %s", "commands.execute.function.instantiationFailure": "%s: %s s funk<PERSON>yasının quraşdırılması uğursuz oldu", "commands.experience.add.levels.success.multiple": "%2$s'e %1$s təcrübə verildi", "commands.experience.add.levels.success.single": "%2$s'e %1$s təcrübə verildi", "commands.experience.add.points.success.multiple": "%2$s'e %1$s təcrübə verildi", "commands.experience.add.points.success.single": "%2$s'e %1$s təcrübə verildi", "commands.experience.query.levels": "%s təcrübə %s nöqtələrinə malikdir", "commands.experience.query.points": "%s təcrübə %s nöqtələrinə malikdir", "commands.experience.set.levels.success.multiple": "%2$s'e %1$s təcrübə verildi", "commands.experience.set.levels.success.single": "%2$s'e %1$s təcrübə verildi", "commands.experience.set.points.invalid": "Təcrübə nöqtələrini oyunçunun hazırkı səviyyəsi üçün maksimum nöqtələrdən yuxarı təyin etmək olmur", "commands.experience.set.points.success.multiple": "%2$s'e %1$s təcrübə verildi", "commands.experience.set.points.success.single": "%2$s'e %1$s təcrübə verildi", "commands.fill.failed": "<PERSON><PERSON> bir blok kopyalana bilmədi", "commands.fill.success": "%s bloku uğurla doldurdu", "commands.fill.toobig": "Göstərilən ərazidə həddən artıq blok var (maksimum %s, müəyyən %s)", "commands.fillbiome.success": "Biomlar %s, %s, %s və %s, %s, %s arasındadır", "commands.fillbiome.success.count": "%s biom girişi/girişləri %s, %s, %s və %s, %s, %s arasında təyin olunub", "commands.fillbiome.toobig": "Müəyyən edilmiş həcmdə həddən artıq blok var (maksimum %s, göstərilən %s)", "commands.forceload.added.failure": "<PERSON><PERSON><PERSON> yüklənməsi üçün heç bir hissə qeyd edilmədi", "commands.forceload.added.multiple": "Güc yüklənmək üçün %s %s dən %s dək %s qeyd edildi", "commands.forceload.added.none": "Güclə yüklənmiş çubu də tapıldı", "commands.forceload.added.single": "Güc yüklənmək üçün %s ilə %s işarələndi", "commands.forceload.list.multiple": "Güclə yüklənmiş çubu %s də tapıldı: %s", "commands.forceload.list.single": "Güclə yüklənmiş çubu %s də tapıldı: %s", "commands.forceload.query.failure": "Güc yüklənməsi üçün %s də %s qeyd edilməyib", "commands.forceload.query.success": "Güc yüklənməsi üçün %s də %s qeyd edildi", "commands.forceload.removed.all": "%s də bütün güc yüklən<PERSON>ş hissələr işarələnməmişdir", "commands.forceload.removed.failure": "<PERSON><PERSON><PERSON> yüklənməsi üçün heç bir hissə qeyd edilmədi", "commands.forceload.removed.multiple": "Güc yüklənmək üçün %s %s dən %s dək %s qeyd edildi", "commands.forceload.removed.single": "Güc yüklənmək üçün %s ilə %s işarələndi", "commands.forceload.toobig": "Göstərilən ərazidə həddən artıq çoxdur (maksimum %s, göstərilən %s)", "commands.function.error.argument_not_compound": "Etibarsız arqument növü: %s, gözlənilən birləşmə", "commands.function.error.missing_argument": "Çatışmayan arqument %2$s to function %1$s", "commands.function.error.missing_arguments": "%s funksiyası üçün arqumentlər çatışmayır", "commands.function.error.parse": "Ma<PERSON><PERSON>u <PERSON>ən %s: Command '%s' xətaya səbəb olub: %s", "commands.function.instantiationFailure": "%s: %s s funk<PERSON>yasının quraşdırılması uğursuz oldu", "commands.function.result": "%s funksiyası %s s-ni qaytardı", "commands.function.scheduled.multiple": "Funksiyalarının icrası %s", "commands.function.scheduled.no_functions": "Funksiya %s adı üçün tapıla bilməz", "commands.function.scheduled.single": "%s funksiyasının icrası", "commands.function.success.multiple": "%s funksiyalarından %s əmr(lər) i icra edildi", "commands.function.success.multiple.result": "%s funksiyası yerinə yet<PERSON>ldi", "commands.function.success.single": "'%s' funksiyasından %s əmr(lər) i icra edildi", "commands.function.success.single.result": "'%2$s' funksiyası %1$s-ı qaytardı", "commands.gamemode.success.other": "%s'in oyun modu %s' olaraq nizamlandı", "commands.gamemode.success.self": "Oyun modun %s olaraq nizamlandı", "commands.gamerule.query": "Gamerule %s hazırda təyin edilmişdir: %s", "commands.gamerule.set": "Gamerule %s hazırda təyin edilmişdir: %s", "commands.give.failed.toomanyitems": "<PERSON><PERSON><PERSON><PERSON> miqdar %s ola bilər", "commands.give.success.multiple": "%s oyunu %s-yə %s oyunçu verdi", "commands.give.success.single": "%3$s'e %2$s %1$s verildi", "commands.help.failed": "<PERSON><PERSON><PERSON> əmr və ya icazələrin olma<PERSON>ı", "commands.item.block.set.success": "Replaced a slot at %s, %s, %s with %s", "commands.item.entity.set.success.multiple": "%s obyektindəki yuva %s ilə əvəz olundu", "commands.item.entity.set.success.single": "Replaced a slot on %s with %s", "commands.item.source.no_such_slot": "Mənbə yuvada %s yoxdur", "commands.item.source.not_a_container": "Mənbə nöqtəsi %s, %s, %s bu yerdə ola bilməz", "commands.item.target.no_changed.known_item": "Hədəflər %s əşyasını %s yuvasına qəbul etmədi", "commands.item.target.no_changes": "Hədəflər %s yuvasına əşya qəbul etmədi", "commands.item.target.no_such_slot": "Hədəfdə %s yuvası yoxdur", "commands.item.target.not_a_container": "Hədəf nöqtəsi %s, %s, %s bu yerdə ola bilməz", "commands.jfr.dump.failed": "JFR rekort tökmə uğursuz oldu: %s", "commands.jfr.start.failed": "JFR profil yarada bi<PERSON>ədi", "commands.jfr.started": "JFR profilləşdirmə başladı", "commands.jfr.stopped": "JFR profilləşdirmə dayandı və %s töküldü", "commands.kick.owner.failed": "LAN oyununda Server <PERSON><PERSON><PERSON><PERSON> q<PERSON><PERSON><PERSON> mü<PERSON><PERSON><PERSON><PERSON>l", "commands.kick.singleplayer.failed": "Oflayn tək oyunçu oyununa başlamaq mümkün deyil", "commands.kick.success": "%s başlandı: %s", "commands.kill.success.multiple": "%s şəxs öldürüldü", "commands.kill.success.single": "%s öldürüldü", "commands.list.nameAndId": "\n%s (%s)", "commands.list.players": "%s: online oyunçuların %s maksimum məbləği %s var", "commands.locate.biome.not_found": "Uyğun məsafədə \"%s\" tipli bir biome tapa bilmədik", "commands.locate.biome.success": "Ən yaxın %s %s səviyyəsindədir (%s kənarda)", "commands.locate.poi.not_found": "Ağlabatan məsafədə \"%s\" tipli maraq nöqtəsini tapmaq mümkün olmadı", "commands.locate.poi.success": "Ən yaxın %s %s səviyyəsindədir (%s kənarda)", "commands.locate.structure.invalid": "\"%s\" tipli struktur yoxdur", "commands.locate.structure.not_found": "Yaxınlıqda \"%s\" tipli struktur tapmaq mümkün olmadı", "commands.locate.structure.success": "Ən yaxın %s %s səviyyəsindədir (%s kənarda)", "commands.message.display.incoming": "%s sənə fısıldıyır: %s", "commands.message.display.outgoing": "%s'a fısıldadın: %s", "commands.op.failed": "Heçnə dəyişmədi. Oyunçu operator deyil", "commands.op.success": "%s bir server operatoru etdi", "commands.pardon.failed": "Heçnə dəyişmədi. Bu Ip qadağan deyil", "commands.pardon.success": "Qadağan edilməmiş %s", "commands.pardonip.failed": "Heçnə dəyişmədi. Bu IP qadağan deyil", "commands.pardonip.invalid": "Adlı oyuncunun İP banı qaldırıldı", "commands.pardonip.success": "Qadağan olunmamış IP %s", "commands.particle.failed": "<PERSON><PERSON><PERSON> heç kim <PERSON>n görünmürdü", "commands.particle.success": "%s hissəcik göstərilir", "commands.perf.alreadyRunning": "Debug <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.perf.notRunning": "Performans profilləri hələ baş<PERSON>ıb", "commands.perf.reportFailed": "Failed to create debug report", "commands.perf.reportSaved": "%s-də sazlama hesabatı yaradıldı", "commands.perf.started": "10 saniyəlik performans profili başlad<PERSON> (dayandırmaq üçün '/perf stop' daxil edin)", "commands.perf.stopped": "%s saniyə və %s işarədən sonra performans profili dayandırıldı (saniyədə %s işarəsi)", "commands.place.feature.failed": "Funksiyanı yerləşdirmək alınmadı", "commands.place.feature.invalid": "\"%s\" növü ilə heç bir funksiya yoxdur", "commands.place.feature.success": "%s, %s, %s ünvanlarında \"%s\" yerləşdirildi", "commands.place.jigsaw.failed": "Yapboz ya<PERSON><PERSON> al<PERSON>", "commands.place.jigsaw.invalid": "\"%s\" tipli şablon hovuzu yoxdur", "commands.place.jigsaw.success": "%s, %s, %sdə Jigsaw yaradıldı", "commands.place.structure.failed": "Struktur yerləşdirmək alınmadı", "commands.place.structure.invalid": "\"%s\" tipli struktur yoxdur", "commands.place.structure.success": "%s, %s, %s də \"%s\" Strukturası generasiya olundu", "commands.place.template.failed": "Şablonu yerləşdirmək alınmadı", "commands.place.template.invalid": "\"%s\" id ilə heç bir şablon yoxdur", "commands.place.template.success": "Yüklənmiş şablon \"%s\" %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON>s eşitmək üçün çox uzaqdır", "commands.playsound.success.multiple": "'%s' səsi %s üçün çalındı", "commands.playsound.success.single": "'%s' səsi %s üçün çalındı", "commands.publish.alreadyPublished": "Multiplayer oyunu artıq %s limanında keçirilib", "commands.publish.failed": "<PERSON><PERSON><PERSON> <PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.publish.started": "Hazırkı oyun %s portundan başladılıb", "commands.publish.success": "Multiplayer oyunu artıq %s limanında keçirilib", "commands.random.error.range_too_large": "<PERSON><PERSON><PERSON><PERSON><PERSON> dəyər diapazonu ən çox 2147483646 olmalıdır", "commands.random.error.range_too_small": "Təsad<PERSON><PERSON> dəyər diapazonu ən azı 2 olmalıdır", "commands.random.reset.all.success": "Sıfırlayın %s təsadüfi ardıcıllıq(lar)", "commands.random.reset.success": "Təsadüfi ardıcıllığı sıfırlayın %s", "commands.random.roll": "%s %s yuvarlandı (%s ilə %s arasında)", "commands.random.sample.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> dəyər: %s", "commands.recipe.give.failed": "Yeni reseptlər <PERSON>", "commands.recipe.give.success.multiple": "%s üçün kilidlənmiş %s reseptləri", "commands.recipe.give.success.single": "%s üçün kilidlənmiş %s reseptləri", "commands.recipe.take.failed": "<PERSON><PERSON> bir resept unut<PERSON><PERSON>la bilm<PERSON>z", "commands.recipe.take.success.multiple": "%s oyunçusundan %s reseptləri <PERSON>", "commands.recipe.take.success.single": "%s reseptini %s-dən götürdü", "commands.reload.failure": "<PERSON><PERSON><PERSON>ən yük<PERSON>ə<PERSON>, köhnə məlumatlar saxlanılır", "commands.reload.success": "Yenidən yüklənir!", "commands.ride.already_riding": "%s artıq %s sür<PERSON>r", "commands.ride.dismount.success": "%s %s sürməyi dayandırdı", "commands.ride.mount.failure.cant_ride_players": "Oyunçulara minmək olmaz", "commands.ride.mount.failure.generic": "%s %s sürməyə başlaya bilmədi", "commands.ride.mount.failure.loop": "Müəssisəni özünə və ya onun hər hansı bir sərnişininə quraşdıra bilməz", "commands.ride.mount.failure.wrong_dimension": "Fər<PERSON>li ölçüdə obyekti quraşdırmaq mümkün deyil", "commands.ride.mount.success": "%s %s sürməyə başladı", "commands.ride.not_riding": "%s heç bir avtomobil sürmür", "commands.rotate.success": "%s istiqamətləndi", "commands.save.alreadyOff": "Qeydiyyat hal hazırda bağlıdır", "commands.save.alreadyOn": "Qeydiyyat hal hazırda açıqdır", "commands.save.disabled": "Avtomatik qeyd etmə passiv edildi", "commands.save.enabled": "Avtomatik qeyd etmə aktiv edildi", "commands.save.failed": "<PERSON><PERSON>u saxlaya bilmədim (disk sahəsi kifayətdirmi?)", "commands.save.saving": "Oyun qənaət edin (bu bir dəqiqə çəkə bilər!)", "commands.save.success": "<PERSON><PERSON> qeyd edildi", "commands.schedule.cleared.failure": "İd ilə cədvəl yoxdur %s", "commands.schedule.cleared.success": "%s id ilə %s cədvəl(lər) silindi", "commands.schedule.created.function": "%s oyun zamanı %s işarəsində '%s' planlaşdırılmış funksiyası", "commands.schedule.created.tag": "%s oyunda %s işarələdiyi '%s' planlanmış funksiyası %s", "commands.schedule.macro": "Makro cədvəlləşdirilə bilmir", "commands.schedule.same_tick": "Cari gənə üçün cədvəl verilə bilməz", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON> artıq bu ad<PERSON> mö<PERSON>ur", "commands.scoreboard.objectives.add.success": "%s yeni hədəf ya<PERSON>dı", "commands.scoreboard.objectives.display.alreadyEmpty": "Heçnə dəyişmədi. Bu ekran yuvası artıq boşdur", "commands.scoreboard.objectives.display.alreadySet": "He<PERSON>nə dəyişmədi. Bu ekran yuvası artıq bu məqsədi göstərir", "commands.scoreboard.objectives.display.cleared": "'%s' g<PERSON><PERSON><PERSON><PERSON><PERSON> slotundaki obyektiv qaldırıldı", "commands.scoreboard.objectives.display.set": "Məqsədli %s göstərmək üçün %s ekran yuvasını təyin edin", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "commands.scoreboard.objectives.list.success": "%s məqsəd(lər) var: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Obyektiv %s üçün displey avtomatik yeniləməsi deaktiv edildi", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Obyektiv %s üçün ekranın avtomatik güncəllənməsi aktivləşdirildi", "commands.scoreboard.objectives.modify.displayname": "%s %s ekran adı dəyişdi", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "%s o<PERSON><PERSON><PERSON><PERSON> defolt nömrə formatı silindi", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Obyektivin standart nömrə formatı dəyişdirildi", "commands.scoreboard.objectives.modify.rendertype": "Obyektiv %s Ekran növü dəyişdirin", "commands.scoreboard.objectives.remove.success": "Silindi obyekt %s", "commands.scoreboard.players.add.success.multiple": "%s üçün %s -yə %s əlavə edildi indi %s", "commands.scoreboard.players.add.success.single": "%s üçün %s -yə %s əlavə edildi (indi %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "\n%s daxilində %s obyekt üçün displey adı silindi", "commands.scoreboard.players.display.name.clear.success.single": "%s daxilində %s ü<PERSON>ün displey adı silindi", "commands.scoreboard.players.display.name.set.success.multiple": "%s daxilində %s obyekt üçün displey adı %s olar<PERSON> dəyişdirildi", "commands.scoreboard.players.display.name.set.success.single": "%s daxilində %s obyekt üçün displey adı %s olar<PERSON> dəyişdirildi", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "%s daxilində %s obyekt üçün displey adı %s olar<PERSON> dəyişdirildi", "commands.scoreboard.players.display.numberFormat.clear.success.single": "%s daxilində %s üçün nömrə formatı silindi", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "%s daxilində %s üçün nömrə formatı silindi", "commands.scoreboard.players.display.numberFormat.set.success.single": "%s daxilində %s üçün rəqəm formatı dəyişdirildi", "commands.scoreboard.players.enable.failed": "Heçnə dəyişmədi. Bu trigger artıq aktivdir", "commands.scoreboard.players.enable.invalid": "Yalnız trigger-məqsədlər üzərində işləməyi aktiv edin", "commands.scoreboard.players.enable.success.multiple": "Tətikləyici %s, %s üçün aktiv edildi", "commands.scoreboard.players.enable.success.single": "Tətikləyici %s, %s üçün aktiv edildi", "commands.scoreboard.players.get.null": "%s üçün %s dəyəri ala bi<PERSON>; heç biri qoyulmur", "commands.scoreboard.players.get.success": "%s %s %s var", "commands.scoreboard.players.list.empty": "Obyektl<PERSON><PERSON>də etiketlər yoxdur", "commands.scoreboard.players.list.entity.empty": "%s -nin göstərmək üçün puanları yoxdur", "commands.scoreboard.players.list.entity.entry": "\n%s: %s", "commands.scoreboard.players.list.entity.success": "%s %s xalı var:", "commands.scoreboard.players.list.success": "%s izlənilən obyekt/müəssisə var: %s", "commands.scoreboard.players.operation.success.multiple": "Üçün %s -yə %s əlavə edildi indi %s", "commands.scoreboard.players.operation.success.single": "%s üçün %s üçün %s təyin edin", "commands.scoreboard.players.remove.success.multiple": "%s üçün %s -yə %s əlavə edildi (indi %s", "commands.scoreboard.players.remove.success.single": "%s üçün %s -yə %s əlavə edildi (indi %s)", "commands.scoreboard.players.reset.all.multiple": "%s üçün bütün balları sıfırlayın", "commands.scoreboard.players.reset.all.single": "%s üçün bütün balları sıfırlayın", "commands.scoreboard.players.reset.specific.multiple": "Üçün %s -yə %s əlavə edildi indi %s", "commands.scoreboard.players.reset.specific.single": "%s üçün %s sıfırlayın", "commands.scoreboard.players.set.success.multiple": "%s obyektləri üçün %s parametrini %s olaraq təyin edin", "commands.scoreboard.players.set.success.single": "%s üçün %s-dən %s -yə %s təyin edin", "commands.seed.success": "Dünya Yaradıcısı Kodu: %s", "commands.setblock.failed": "<PERSON>lo<PERSON> təyin edə bilmədi", "commands.setblock.success": "Bloku %s, %s,%s dəyişdi", "commands.setidletimeout.success": "Oyunçunun boş vaxtı indi %s dəqiqədir", "commands.setidletimeout.success.disabled": "Oyunçu bo<PERSON>da qalma vaxtı indi deaktiv edilib", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON><PERSON>z yerüstü dünya üçün dünya kürü təyin edə bilər", "commands.setworldspawn.success": "Dünya başlangıc nöqtəsi dəyişdirildi (%s, %s, %s)", "commands.spawnpoint.success.multiple": "%4$s bölgəsindəki %5$s üçün başlanğıc nöqtəsi %1$s, %2$s, %3$s olaraq seçildi", "commands.spawnpoint.success.single": "Oyunçular üçün %s, %s, %s Renaissance nöqtəsi set", "commands.spectate.not_spectator": "%s tamaşaçı rejimində deyil", "commands.spectate.self": "Özünüzü seyr edə bilməzsiniz", "commands.spectate.success.started": "İndi seyrçi %s", "commands.spectate.success.stopped": "<PERSON><PERSON>q bir varlığı seyr etmirəm", "commands.spreadplayers.failed.entities": "%s obyekti/obyektini %s, %s ətrafında yaymaq mümkün olmadı (yer üçün həddən artıq çox var - ən çox %s yayılmasından istifadə edin)", "commands.spreadplayers.failed.invalid.height": "Yanlış maxHeight %s; dünya minimum %s səviyyəsindən yüksək gözlənilir", "commands.spreadplayers.failed.teams": "%s komanda(lar) ı %s, %s ətrafında yaymaq mümkün olmadı (yer üçün həddən artıq çox var - ən çox %s yayılmasından istifadə etməyə çalışın)", "commands.spreadplayers.success.entities": "%s oyunçu(ları) %s ətrafında, %s orta məsafədə %s blok ara ilə yaydırın", "commands.spreadplayers.success.teams": "%s komandanı(ları) %s ətrafında, %s orta məsafədə %s blok ara ilə yaydırın", "commands.stop.stopping": "Server dayandırılır", "commands.stopsound.success.source.any": "Bütün '%s' səsləri <PERSON>ı", "commands.stopsound.success.source.sound": "%$3s üçün '%2$s' qaynağından gələn '%1$s' səsi susdurulurdu", "commands.stopsound.success.sourceless.any": "Bütün '' s<PERSON><PERSON><PERSON><PERSON><PERSON>ı", "commands.stopsound.success.sourceless.sound": "Dayandırılmış səs '%s'", "commands.summon.failed": "Obyekt çağırıla bilmədi", "commands.summon.failed.uuid": "Varlıq kopyalama UUIDlərinə görə çağırıla bilməz", "commands.summon.invalidPosition": "<PERSON><PERSON><PERSON> ü<PERSON>ün etibarsız mövqe", "commands.summon.success": "Yeni %s çağırıldı", "commands.tag.add.failed": "Hədəf ya etiket var, ya da çox etiket var", "commands.tag.add.success.multiple": "Üçün %s -yə %s əlavə edildi indi %s", "commands.tag.add.success.single": "'%s' etiketi %s'ə əlavə edildi", "commands.tag.list.multiple.empty": "%s obyektlərində etiketlər yoxdur", "commands.tag.list.multiple.success": "%s qurumlarında %s <PERSON><PERSON>i etiket var: %s", "commands.tag.list.single.empty": "%s-də etiketlər yoxdur", "commands.tag.list.single.success": "%s-də %s etiketi var: %s", "commands.tag.remove.failed": "Hədəfdə bu etiket yoxdur", "commands.tag.remove.success.multiple": "Üçün %s -yə %s əlavə edildi indi %s", "commands.tag.remove.success.single": "'%s' etiketi %s'dən silindi", "commands.team.add.duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON> artıq bu ad<PERSON> mö<PERSON>ur", "commands.team.add.success": "%s komandası yaradıldı", "commands.team.empty.success": "Üzvləri komandadan çıxarın)", "commands.team.empty.unchanged": "Heçnə dəyişmədi. Bu ekran yuvası artıq boşdur", "commands.team.join.success.multiple": "Bütün %s oyunçu %s komandasıdan qaldırıldı", "commands.team.join.success.single": "Vaxta %s əlavə edildi", "commands.team.leave.success.multiple": "Bütün %s oyunçu %s komandasıdan qaldırıldı", "commands.team.leave.success.single": "%s hər hansı bir komandadan silindi", "commands.team.list.members.empty": "%s komandasında üzv yoxdur", "commands.team.list.members.success": "%s komandasının %s üzvü var: %s", "commands.team.list.teams.empty": "<PERSON><PERSON><PERSON><PERSON> yox<PERSON>r", "commands.team.list.teams.success": "%s komanda(lar) var: %s", "commands.team.option.collisionRule.success": "%s komandası üçün toqquşma qaydası indi \"%s\" dir", "commands.team.option.collisionRule.unchanged": "He<PERSON>n<PERSON> dəyişmədi. Nametag görünürlüğü artıq bu dəyərdir", "commands.team.option.color.success": "%s-dən %s qrupu üçün rəng yeniləndi", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> dəyişmədi. Həmin komanda artıq bu rəngə sahibdir", "commands.team.option.deathMessageVisibility.success": "%s komandası üçün nametag görüntüsü indi \"%s\" dir", "commands.team.option.deathMessageVisibility.unchanged": "He<PERSON>n<PERSON> dəyişmədi. Nametag görünürlüğü artıq bu dəyərdir", "commands.team.option.friendlyfire.alreadyDisabled": "Heçnə dəyişmədi. Artıq komanda üçün yoldaşlıq atəşi aktivdir", "commands.team.option.friendlyfire.alreadyEnabled": "Heçnə dəyişmədi. Artıq komanda üçün yoldaşlıq atəşi aktivdir", "commands.team.option.friendlyfire.disabled": "Komanda %s üçün dostu atəş", "commands.team.option.friendlyfire.enabled": "Komanda %s üçün dostu atəş", "commands.team.option.name.success": "Yenilənib komanda adı %s", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON><PERSON> dəyişmədi. Həmin komanda artıq bu rəngə sahibdir", "commands.team.option.nametagVisibility.success": "%s komandası üçün nametag görüntüsü indi \"%s\" dir", "commands.team.option.nametagVisibility.unchanged": "He<PERSON>n<PERSON> dəyişmədi. Nametag görünürlüğü artıq bu dəyərdir", "commands.team.option.prefix.success": "Komanda prefiksi %s olaraq təyin edildi", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON>nə dəyişmədi. Həmin komanda artıq görünməyən komanda yoldaşlarını görə bilər", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON>nə dəyişmədi. Həmin komanda artıq görünməyən komanda yoldaşlarını görə bilər", "commands.team.option.seeFriendlyInvisibles.disabled": "Komanda %s artıq görünməyən komanda yoldaşlarını görə bilər", "commands.team.option.seeFriendlyInvisibles.enabled": "Komanda %s artıq görünməyən komanda yoldaşlarını görə bilər", "commands.team.option.suffix.success": "Komanda prefiksi %s olaraq təyin edildi", "commands.team.remove.success": "%s komandası qaldırıldı", "commands.teammsg.failed.noteam": "Komandanıza mesaj vermək üçün bir komandada olmalısınız", "commands.teleport.invalidPosition": "Teleport üçün etibarsız mövqe", "commands.teleport.success.entity.multiple": "%s obyektləri %s-yə bildirildi", "commands.teleport.success.entity.single": "%s, %s'e teleport edildi", "commands.teleport.success.location.multiple": "%s obyektləri %s-yə bildirildi", "commands.teleport.success.location.single": "%s; %s, %s, %s nöqtəsinə teleport edildi", "commands.test.batch.starting": "%s mühit %s partiyası başladılır", "commands.test.clear.error.no_tests": "Təmizləmək üçün heç bir sınaq tapı<PERSON>dı", "commands.test.clear.success": "%s strukturu təmizləndi", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Buferə kopyalamaq üçün k<PERSON>ləyin", "commands.test.create.success": "%s testi üçün sınaq hazırlığı yaradıldı", "commands.test.error.no_test_containing_pos": "%s, %s, %s ehtiva edən test nümunəsi tapılmadı", "commands.test.error.no_test_instances": "Heç bir test nümunəsi tapılmadı", "commands.test.error.non_existant_test": "%s adlı test tapılmadı", "commands.test.error.structure_not_found": "%s adlı test strukturu tapılmadı", "commands.test.error.test_instance_not_found": "Test nümunəsi blok varlığı tapılmadı", "commands.test.error.test_instance_not_found.position": "%s, %s, %s koordinatlarında yerləşən test üçün test nümunəsi blok varlığı tapılmadı", "commands.test.error.too_large": "tikilinin boyu hər tərəfə %s blokdan az olmalıdır", "commands.test.locate.done": "Yer axtarı<PERSON><PERSON> tamamlandı, %s quruluş(lar) tapıldı", "commands.test.locate.found": "tikili %s kordinatlarında tapıldı (məsafə: %s)", "commands.test.locate.started": "Test quruluşların axtarışı başlandı, bu bir az vaxt ala bilər...", "commands.test.no_tests": "belə kommand yoxdur", "commands.test.relative_position": "%s'ə nisbən kordinat: %s", "commands.test.reset.error.no_tests": "Sıfırlamaq üçün heç bir sınaq tapılmadı", "commands.test.reset.success": "%s-dəki struktur(lar) yenilənir", "commands.test.run.no_tests": "kommand tapılmadı", "commands.test.run.running": "test(lər) %s-də ișləyir...", "commands.test.summary": "Oyun Sınağı tamamlandı! %s sınaq(lar) işlədirdi", "commands.test.summary.all_required_passed": "<PERSON><PERSON><PERSON><PERSON><PERSON> tələb olunan sınaqlar uğurlu oldu:)", "commands.test.summary.failed": "%s tələb olunan sınaq(lar) uğurlu olmadı :(", "commands.test.summary.optional_failed": "%s istəyə bağlı sınaq(lar) uğursuz oldu", "commands.tick.query.percentiles": "Faizlər: P50: %sms P95: %sms P99: %sms, nümunə: %s", "commands.tick.query.rate.running": "Hədəf gənə tezliyi: saniyədə %s saniyə.\nTik başına orta vaxt: %sms (hədəf dəyəri: %sms)", "commands.tick.query.rate.sprinting": "Hədəf gənə tezliyi: saniyədə %s (nəzər<PERSON> alınmır, yalnız istinad üçün).\nOrta gənə vaxtı: %sms", "commands.tick.rate.success": "Hədəf gənə tezliyini saniyədə %s olaraq təyin edin", "commands.tick.sprint.report": "Sprint saniyədə %s gənə və ya hər gənə üçün %s ms tezliyi ilə tamamlanır", "commands.tick.sprint.stop.fail": "Tıqqıltı ilə Sprint edilmir", "commands.tick.sprint.stop.success": "Cari gənə sprintini kəsdi", "commands.tick.status.frozen": "oyun donur", "commands.tick.status.lagging": "<PERSON><PERSON> i<PERSON><PERSON>, amma hədəf dövr sürətinə çatmaqda çətinlik çəkir", "commands.tick.status.running": "oyun normal i<PERSON>r", "commands.tick.status.sprinting": "<PERSON><PERSON> qaçır", "commands.tick.step.fail": "Oyuna davam edilə bilmir - əvvəlcə oyunun dondurulması lazımdı", "commands.tick.step.stop.fail": "<PERSON><PERSON> bir addım irəliləməkdə deyil", "commands.tick.step.stop.success": "<PERSON><PERSON> bir addım irəliləməkdə deyil", "commands.tick.step.success": "%s vaxt birimi addımlayır", "commands.time.query": "Vaxt %s", "commands.time.set": "Vaxt %s olaraq nizamlandı", "commands.title.cleared.multiple": "%s <PERSON><PERSON><PERSON><PERSON> başlıq<PERSON>", "commands.title.cleared.single": "%s <PERSON><PERSON><PERSON><PERSON> başlıq<PERSON>", "commands.title.reset.multiple": "%s üçün başlıq seçimlərini yenidən qurun", "commands.title.reset.single": "%s üçün başlıq seçimlərini yenidən qurun", "commands.title.show.actionbar.multiple": "%s üçün yeni başlıq göstərilir", "commands.title.show.actionbar.single": "%s üçün yeni başlıq göstərilir", "commands.title.show.subtitle.multiple": "%s üçün yeni başlıq göstərilir", "commands.title.show.subtitle.single": "%s üçün yeni başlıq göstərilir", "commands.title.show.title.multiple": "%s üçün yeni başlıq göstərilir", "commands.title.show.title.single": "%s üçün yeni başlıq göstərilir", "commands.title.times.multiple": "%s üçün başlıq göstərmə vaxtı dəyişdirildi", "commands.title.times.single": "%s üçün başlıq göstərmə vaxtı dəyişdirildi", "commands.transfer.error.no_players": "Köçürmək üçün ən azı bir oyuncu göstərilməlidir", "commands.transfer.success.multiple": "%s-dəki oyunçular %s:%s-ə köçürdülür", "commands.transfer.success.single": "%s %s:%s-ə köçürdülür", "commands.trigger.add.success": "Tetiklenmiş %s (dəyərə %s əlavə edildi)", "commands.trigger.failed.invalid": "<PERSON><PERSON><PERSON><PERSON> \"tetikley<PERSON>\" tipli hədəfləri tetikleyeb<PERSON>z", "commands.trigger.failed.unprimed": "Hələ bu hədəfi işə sala bilməz<PERSON>iz", "commands.trigger.set.success": "Tetiklenmiş %s (dəyəri %s olaraq təyin et)", "commands.trigger.simple.success": "%s işə salındı", "commands.version.build_time": "tikinti_vaxtı = %s", "commands.version.data": "veri = %s", "commands.version.header": "Server versiyası:", "commands.version.id": "i̇d = %s", "commands.version.name": "ad = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Havanı təmizləmək üçün seçin", "commands.weather.set.rain": "Havanı təmizləmək üçün seçin", "commands.weather.set.thunder": "Havanı yağış və göy gurultusu üçün seçin", "commands.whitelist.add.failed": "Oyunçu artıq ağ siyahıya salınmışdır", "commands.whitelist.add.success": "%s ağ səhifəyə əlavə olundu", "commands.whitelist.alreadyOff": "<PERSON>ğ siyahı indi açılmışdır", "commands.whitelist.alreadyOn": "<PERSON>ğ siyahı indi açılmışdır", "commands.whitelist.disabled": "<PERSON>ğ siyahı indi açılmışdır", "commands.whitelist.enabled": "<PERSON>ğ siyahı indi açılmışdır", "commands.whitelist.list": "%s ağ siyahıya alınmış oyunçu(lar) var: %s", "commands.whitelist.none": "<PERSON><PERSON> siyahıya alınmış oyunçu yoxdur", "commands.whitelist.reloaded": "Ağ səhifə yenidən yükləndı", "commands.whitelist.remove.failed": "Oyunçu artıq ağ siyahıya salınmışdır", "commands.whitelist.remove.success": "%s ağ səhifədən çıxarıldı", "commands.worldborder.center.failed": "He<PERSON>nə dəyişmədi. Dü<PERSON> sərhədi artıq oradadır", "commands.worldborder.center.success": "Dünya sərhədinin mərkəzini %s,%s olaraq təyin edin", "commands.worldborder.damage.amount.failed": "He<PERSON><PERSON>ə dəyişmədi. Dünya sərhədinə vurulan ziyan onsuz da bu məbləğdir", "commands.worldborder.damage.amount.success": "<PERSON>ər i<PERSON>ci blokunun %s dünya sərhəd zərər seçin", "commands.worldborder.damage.buffer.failed": "He<PERSON><PERSON>ə dəyişmədi. Dü<PERSON> sərhədinə zərər tamponu artıq bu məsafədədir", "commands.worldborder.damage.buffer.success": "Dünya sərhə<PERSON>in zədələnməsi buferini %s bloka təyin edin", "commands.worldborder.get": "Hazırda dünya sərhədinin eni %s blokdur", "commands.worldborder.set.failed.big": "Dünya sərhədi %s blok genişliyindən böyük ola bilməz", "commands.worldborder.set.failed.far": "Dünya sərhədi %s blok genişliyindən böyük ola bilməz", "commands.worldborder.set.failed.nochange": "He<PERSON>nə dəyişmədi. Dü<PERSON> sərhədi artıq oradadır", "commands.worldborder.set.failed.small": "Dünya sərhəddi eni 1 blokdan kiçik ola bilməz", "commands.worldborder.set.grow": "Dünya sərhədi %s saniyədən çox %s bloka qədər böyüdü", "commands.worldborder.set.immediate": "Dünya sərhədini %s blok(lar) genişliyinə təyin edin", "commands.worldborder.set.shrink": "Dünya sərhədinin %s saniyə ərzində eni %s blok(lar) a qədər kiçilməsi", "commands.worldborder.warning.distance.failed": "Heçnə dəyişmədi. Dünya sərhəd xəbərdarlığı artıq o məsafədədir", "commands.worldborder.warning.distance.success": "Dünya sərhədi xəbərdarlığı məsafəsini %s bloka təyin edin", "commands.worldborder.warning.time.failed": "He<PERSON>nə dəyişmədi. Dü<PERSON> sərhədi artıq oradadır", "commands.worldborder.warning.time.success": "Dünya sərhədi xəbərdarlığı vaxtını %s saniyəyə təyin edin", "compliance.playtime.greaterThan24Hours": "24 saatdan artıqdır ki, oynayırsınız", "compliance.playtime.hours": "Siz %s saatdır ki, oynayırsınız", "compliance.playtime.message": "Hə<PERSON><PERSON><PERSON>n artıq oyun normal gündəlik həyata müdaxilə edə bilər", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Daxil olunur...", "connect.connecting": "Serverə qoşulur...", "connect.encrypting": "Şifrələmə...", "connect.failed": "<PERSON>ə qoşula bilmədi", "connect.joining": "Dünyaya qoşulur...", "connect.negotiating": "Danışıqların aparılması...", "connect.reconfiging": "<PERSON><PERSON><PERSON>ən konfiqurasiya edilir...", "connect.reconfiguring": "<PERSON><PERSON><PERSON>ən qurun...", "connect.transferring": "Transferring to new server...", "container.barrel": "Çəllək", "container.beacon": "Maya<PERSON>", "container.beehive.bees": "Arılar: %s / %s", "container.beehive.honey": "Bal: %s / %s", "container.blast_furnace": "Əritmə sobası", "container.brewing": "<PERSON><PERSON><PERSON>", "container.cartography_table": "Xəritəçilik masası", "container.chest": "Sandıq", "container.chestDouble": "Geniş Sandıq", "container.crafter": "Hazırlayıcı Masa", "container.crafting": "Hazırlama", "container.creative": "<PERSON><PERSON><PERSON>", "container.dispenser": "Atıcı", "container.dropper": "Damcı", "container.enchant": "Sehr<PERSON><PERSON><PERSON>ə", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapis <PERSON>", "container.enchant.lapis.one": "1 Lapis Lazuli", "container.enchant.level.many": "%s Sehr <PERSON>əviyyəsi", "container.enchant.level.one": "1 Sehr Səviyyəsi", "container.enchant.level.requirement": "Səviyyə tələbi: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "Soba", "container.grindstone_title": "Təmir və sehr ləğv etmə", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "İnventarlaşdırma", "container.isLocked": "%s kilidli!", "container.lectern": "<PERSON><PERSON><PERSON><PERSON>", "container.loom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>ı", "container.repair": "Təmir Etmə və Adlandırma", "container.repair.cost": "Sehr Maliyyəti: %1$s", "container.repair.expensive": "Çox Bahalı!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "və %s daha çox...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Tüstü sobası", "container.spectatorCantOpen": "<PERSON><PERSON><PERSON>la bilmir. Qənimət hələ yaradılmadı.", "container.stonecutter": "<PERSON><PERSON> k<PERSON>", "container.upgrade": "<PERSON><PERSON><PERSON>", "container.upgrade.error_tooltip": "Elementi bu şəkildə təkmilləşdirmək mümkün deyil", "container.upgrade.missing_template_tooltip": "Smithing Şablonu əlavə edin", "controls.keybinds": "Qısayol düymələri...", "controls.keybinds.duplicateKeybinds": "Bu açar həmçinin aşağıdakılar üçün istifadə olunur:\n %s", "controls.keybinds.title": "<PERSON><PERSON><PERSON><PERSON>", "controls.reset": "Sıfırla", "controls.resetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controls.title": "Ko<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Lütfən bir biom seçin", "createWorld.customize.buffet.title": "Bufet dünyasının fərdiləşdirilməsi", "createWorld.customize.flat.height": "Yüksəklik", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Alt - %s", "createWorld.customize.flat.layer.top": "Üst - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.tile": "Təbəqə Materialı", "createWorld.customize.flat.title": "Dümdüz Ö<PERSON>ll<PERSON>şdirmə", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "Alternativ olaraq, daha əvvəl etdiklərimizdən bəziləri!", "createWorld.customize.presets.select": "Hazır Nizamı İstifadə Et", "createWorld.customize.presets.share": "Hazır parametrləri biriləriylə paylaşmaq istəyirsən? Aşağıdakı qutudan istifadə et!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.preparing": "Dünya yaradılmasına ha<PERSON>ı<PERSON>ılır...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "Dünya", "credits_and_attribution.button.attribution": "Atribut", "credits_and_attribution.button.credits": "Kreditlər", "credits_and_attribution.button.licenses": "Licenses", "credits_and_attribution.screen.title": "Kreditlər və Atributlar", "dataPack.bundle.description": "Eksperimental Bundle elementini aktivləşdirir", "dataPack.bundle.name": "Çanta", "dataPack.locator_bar.description": "çoxoyunçuluda digər oyunçuların istiqamətlərin gö<PERSON>ər", "dataPack.locator_bar.name": "<PERSON><PERSON><PERSON>", "dataPack.minecart_improvements.description": "Vaqonçaların təkmilləşdirilmiş hərəkəti", "dataPack.minecart_improvements.name": "Minecart Təkmilləşdirmələri", "dataPack.redstone_experiments.description": "Redstounla bağlı eksperimental dəyişikliklər", "dataPack.redstone_experiments.name": "Redstounla bağlı eksperimentlər", "dataPack.title": "Məlumat paketlə<PERSON> seçin", "dataPack.trade_rebalance.description": "<PERSON>ə<PERSON><PERSON> üçün yenilənən ticarət", "dataPack.trade_rebalance.name": "Kə<PERSON><PERSON><PERSON><PERSON><PERSON> ticarətin ta<PERSON>ı", "dataPack.update_1_20.description": "Minecraft 1.20 üçün yeni xüsusiyyətlər və məzmun", "dataPack.update_1_20.name": "Yeniləmə 1.20", "dataPack.update_1_21.description": "Minecraft 1.21 üçün yeni xüsus<PERSON>yyətlər və kontent", "dataPack.update_1_21.name": "Yeniləmə 1.21", "dataPack.validation.back": "<PERSON><PERSON>", "dataPack.validation.failed": "<PERSON>ə<PERSON><PERSON> paketinin təsdiqlənməsi uğ<PERSON>uz oldu!", "dataPack.validation.reset": "De<PERSON>lt olaraq yenidən qurun", "dataPack.validation.working": "Seçilmiş məlumat paketləri yo<PERSON>ılır...", "dataPack.vanilla.description": "Minecraft üçün standart məlumatlar", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "Qış dropunun yeni imkanları və kontenti", "dataPack.winter_drop.name": "<PERSON>ış dropu", "datapackFailure.safeMode": "Təhlükəsizlik modu", "datapackFailure.safeMode.failed.description": "Bu dünyada etibarsız və ya zədələnmiş yadda saxlama məlumatları var.", "datapackFailure.safeMode.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z rejimdə dünyanı yükləmək alınmadı.", "datapackFailure.title": "<PERSON><PERSON><PERSON><PERSON> seçilmiş məlumat paketlərindəki xətalar dünyanın yüklənməsinə mane oldu.\nSiz ya onu yalnız vanil məlumat paketi (\"təhlükəsiz rejim\") ilə yükləməyə cəhd edə bilərsiniz, ya da başlıq ekranına qayıdıb əl ilə düzəldə bilərsiniz.", "death.attack.anvil": "%1$s düşən bir dəmirçi zindanı tərəfindən əzildi", "death.attack.anvil.player": "%1$s, %2$s ilə döyüşərkən düşən bir örs tərəfindən əzildi", "death.attack.arrow": "%1$s, %2$s tərəfindən vuruldu", "death.attack.arrow.item": "%1$s, %3$s'dən istifadə edərək %2$s tərəfindən vuruldu", "death.attack.badRespawnPoint.link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oyun dizaynı", "death.attack.badRespawnPoint.message": "%1$s tərəfindən öldürüldü %2$s", "death.attack.cactus": "%1$s ölümünə iynələndi", "death.attack.cactus.player": "%1$s, %2$s'dən qaçarkən kaktusa toxunaraq öldü", "death.attack.cramming": "%1$s çox əzildi", "death.attack.cramming.player": "%1$s %2$s'ni əzdi", "death.attack.dragonBreath": "%1$s əjdaha nəfəsində qovruldu", "death.attack.dragonBreath.player": "%1$s %2$s tərəfindən əjdaha nəfəsində qovruldu", "death.attack.drown": "%1$s boğuldu", "death.attack.drown.player": "%1$s, %2$s'dən qaçmağa çalışırkən boğuldu", "death.attack.dryout": "%1$s susu<PERSON><PERSON><PERSON><PERSON>", "death.attack.dryout.player": " %2$s tərəfindən təqib edilərkən %1$s susuzluqdan öldü", "death.attack.even_more_magic": "%1$s daha çox sehr tərəfindən öldürüldü", "death.attack.explosion": "%1$s partladı", "death.attack.explosion.player": "%1$s, %2$s tərəfindən partladıldı", "death.attack.explosion.player.item": "%1$s %3$s istifadə edərək %2$s tərəfindən partladı", "death.attack.fall": "%1$s yerə çox sərt düşdü", "death.attack.fall.player": "%1$s qaçmağa çalışarkən yerə sərt bir şəkildə vurdu %2$s", "death.attack.fallingBlock": "%1$s düşən bir blok tərəfindən əzildi", "death.attack.fallingBlock.player": "%1$s, %2$s ilə döyüşərkən düşən bir blok tərəfindən əzildi", "death.attack.fallingStalactite": "%1$s düşən stalaktit tərəfindən şişləndi", "death.attack.fallingStalactite.player": "%1$s %2$s ilə döyüşərkən düşən stalaktit tərəfindən şişləndi", "death.attack.fireball": "%1$s, %2$s tərəfindən bombalandı", "death.attack.fireball.item": "%1$s, %3$s'dən istifadə edərək %2$s tərəfindən bombalandı", "death.attack.fireworks": "%1$s fişəng tərəfindən partladıldı", "death.attack.fireworks.item": "%1$s, %3$s'in %2$sinin atəş etdiyi atəş nəticəsində öldü", "death.attack.fireworks.player": "%1$s mübarizə apararkən bir bang ilə getdi %2$s", "death.attack.flyIntoWall": "%1$s kinetik enerjiyə məruz qaldı", "death.attack.flyIntoWall.player": "%1$s qaçmağa çalışarkən təcrübəli kinetik enerji %2$s", "death.attack.freeze": "%1$s donaraq <PERSON>ld<PERSON>", "death.attack.freeze.player": "%1$s %2$s tərəfindən dondurularaq öldürüldü", "death.attack.generic": "%1$s öldü", "death.attack.generic.player": "%1$s %2$snin ucbatından öldü", "death.attack.genericKill": "%1$s öldürüldü", "death.attack.genericKill.player": "%1$s %2$s ilə döyüşərkən öldürüldü", "death.attack.hotFloor": "%1$s döşəmənin lava olduğunu aşkar etdi", "death.attack.hotFloor.player": "%1$s %2$s-dən ötrü təhlükəli zonaya getdi", "death.attack.inFire": "%1$s alov aldı", "death.attack.inFire.player": "%1$s, %2$s ile döyüşərkən atəşə getdi", "death.attack.inWall": "%1$s divarın içində boğuldu", "death.attack.inWall.player": "%2$s ilə mübarizə apararkən %1$s divarda boğ<PERSON>u", "death.attack.indirectMagic": "%1$s sehr ilə %2$s tərəfindən öldürüldü", "death.attack.indirectMagic.item": "%1$s, %3$s'dən istifadə edərək %2$s tərəfindən öldürüldü", "death.attack.lava": "%1$s lavada üzməyə çalışdı", "death.attack.lava.player": "%1$s, %2$s'dən qaçmaq üçün lavada üzməyə çalışdı", "death.attack.lightningBolt": "%1$s ildırım tərəfindən vuruldu", "death.attack.lightningBolt.player": "%2$s ilə mübarizə apararkən %1$s ildırım vurdu", "death.attack.mace_smash": "%1$s %2$s tərəfindən əzildi", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s sehrlə öldürüldü", "death.attack.magic.player": "%1$s %2$s qaçmağa çalışırken sehrli tərəfindən öldürüldü", "death.attack.message_too_long": "<PERSON>s<PERSON>ə qalsa, mesaj tam çatdırılma üçün çox uzundur. Bağışlayın! Qısaldılmış versiya budur: %s", "death.attack.mob": "%1$s, %2$s tərəfindən qətl edildi", "death.attack.mob.item": "%1$s, %3$s'dən istifadə edərək %2$s tərəfindən öldürüldü", "death.attack.onFire": "%1$s yanaraq öldü", "death.attack.onFire.item": "%1$s, %2$s ilə döyüşərkən yandı", "death.attack.onFire.player": "%1$s, %2$s ilə döyüşərkən yandı", "death.attack.outOfWorld": "%1$s dünyadan aşağı düşdü", "death.attack.outOfWorld.player": "%1$s ilə eyni dünyada yaşamaq istəmirdi %2$s", "death.attack.outsideBorder": "%1$s dünyanın əngəllərindən düşdü", "death.attack.outsideBorder.player": "%1$s %2$s ilə döyüş aparnda dünyanın əngəllərindən düşdü", "death.attack.player": "%1$s %2$s tərəfindən öldürüldü", "death.attack.player.item": "%1$s %2$s tərəfindən %3$s istifadə edilərək öldürüldü", "death.attack.sonic_boom": "%1$s səs-<PERSON><PERSON><PERSON><PERSON> qışqırıqla məhv edildi", "death.attack.sonic_boom.item": "%1$s, %3$s vasitəsi ilə %2$s-dan qaçmağa çalışarkən, səs-k<PERSON><PERSON><PERSON> qışqırıqla məhv edildi", "death.attack.sonic_boom.player": "%1$s, %2$s-dan qaçmaq istəyərkən səs-kü<PERSON>ü qışqırıqla məhv edildi", "death.attack.stalagmite": "%1$s stalaqmitə şişləndi", "death.attack.stalagmite.player": "%1$s %2$s ilə döyüşərkən stalaqmitə şişləndi", "death.attack.starve": "%1$s aclıqdan <PERSON>", "death.attack.starve.player": "%1$s %2$s-lə dalaşa-dalaşa acından öldü", "death.attack.sting": "%1$s ölümünə iynələndi", "death.attack.sting.item": "%1$s %3$s istifadə edərək %2$s tərəfindən sancılaraq öldürüldü", "death.attack.sting.player": "%1$s, %2$s tarafından ölümünə iynələndi", "death.attack.sweetBerryBush": "%1$s tatlı meyve çalısında kazığa oturdu", "death.attack.sweetBerryBush.player": "%1$s %2$s den kaçmaya çalışırken tatlı meyve çalısında kazığa oturdu", "death.attack.thorns": "%1$s, %2$s'e xəsarət vermək istəyərkən öldürüldü", "death.attack.thorns.item": "%1$s, %2$s'e zərər vermək istəyərkən %3$s'ı öldürüldü", "death.attack.thrown": "%2$s %1$s adlı oyunçunu qızartdı", "death.attack.thrown.item": "%2$s %1$s adlı oyunçunu %3$s istifadə edərək yumruladı", "death.attack.trident": "%1$s, %2$s tərəfindən mühasirəyə alındı", "death.attack.trident.item": "%1$s, %2$s ilə %3$s birlikdə diriltdi", "death.attack.wither": "%1$s soldu", "death.attack.wither.player": "%1$s, %2$s ilə döyüşərkən quruyub getdi", "death.attack.witherSkull": "%1$s %2$s başına vuruldu", "death.attack.witherSkull.item": "%1$s %3$s istifadə edərək %2$s tərəfindən kəllə tərəfindən vuruldu", "death.fell.accident.generic": "%1$s yüksək bir yerdən düşdü", "death.fell.accident.ladder": "%1$s nərdivandan düşdü", "death.fell.accident.other_climbable": "%1$s dırmaşan zaman düşdü", "death.fell.accident.scaffolding": "%1$s tikinti pilləkənlərindən düşdü", "death.fell.accident.twisting_vines": "%1$s bir neçə burulmuş sarmaşıqdan düşdü", "death.fell.accident.vines": "%1$s sarmaş<PERSON>qdan dü<PERSON>", "death.fell.accident.weeping_vines": "%1$s bir neçə ağlayan sarmaşıqdan düşdü", "death.fell.assist": "%1$s, %2$s tərəfindən düşürülərək öldürüldü", "death.fell.assist.item": "%1$s, %3$s'dən istifadə edərək %2$s tərəfindən düşürüldü", "death.fell.finish": "%1$s yüksəklikdən düşdü və %2$s tərəfindən öldürüldü", "death.fell.finish.item": "%1$s yüksəklikdən düşdü və %3$s istifadə edərək %2$s tərəfindən öldürüldü", "death.fell.killer": "%1$s düşürüldü", "deathScreen.quit.confirm": "Çıxmaq istədiyinizə əminsiniz?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON><PERSON> başla", "deathScreen.score": "<PERSON>al", "deathScreen.score.value": "Hesab: %s", "deathScreen.spectate": "İzlə dünyanı", "deathScreen.title": "<PERSON><PERSON>ün!", "deathScreen.title.hardcore": "Oyun bitdi!", "deathScreen.titleScreen": "Baş Menyu", "debug.advanced_tooltips.help": "F3 + H = Ətraflı izahatlar", "debug.advanced_tooltips.off": "Qabaqcıl alət ipucları: gizli", "debug.advanced_tooltips.on": "Qabaqc<PERSON><PERSON> alət ipucları: g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.chunk_boundaries.help": "F3 + G = <PERSON>ığın səhrədlə<PERSON> göstər", "debug.chunk_boundaries.off": "<PERSON><PERSON><PERSON><PERSON>n sərhədləri: gizli", "debug.chunk_boundaries.on": "<PERSON><PERSON><PERSON><PERSON>n sərhədləri: görün<PERSON>n", "debug.clear_chat.help": "F3 + D = Söhbəti təmizlə", "debug.copy_location.help": "F3 + C = <PERSON><PERSON> k<PERSON>ın kimi /tp əmr, tutun F3 + C to oyunu çökdürmək", "debug.copy_location.message": "Məkan panoya kopyalandı", "debug.crash.message": "F3 + C tutulur. Buraxılmadıqda bu oyun qəzaya uğrayacaq.", "debug.crash.warning": "Qəzaya uğrayır %s...", "debug.creative_spectator.error": "<PERSON>yun rejimini dəyiş<PERSON><PERSON>ək mümkün deyi<PERSON>; ica<PERSON>ə yoxdur", "debug.creative_spectator.help": "F3 + N = Əvvəlki oyun rejiminə keçin <-> ta<PERSON><PERSON><PERSON><PERSON>ı", "debug.dump_dynamic_textures": "Dinamik teksturalar %s-də yadda saxlanıldı", "debug.dump_dynamic_textures.help": "F3 + S = Dinamik dokuları boşaltın", "debug.gamemodes.error": "<PERSON>yun modu dəyişdiricisini aça bilmir, ica<PERSON>ə yoxdur", "debug.gamemodes.help": "F3 + F4 = Oyun rejimi dəyişdiricisini açın", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Sonrakı", "debug.help.help": "F3 + Q = Bu siyahıyı göstər", "debug.help.message": "Qısayol düymələri:", "debug.inspect.client.block": "Server yan blok məlumatlarını panoya kopyaladı", "debug.inspect.client.entity": "Server yan blok məlumatlarını panoya kopyaladı", "debug.inspect.help": "F3 + I = Müəssisəni kopyalayın və ya məlumatları panoya köçürün", "debug.inspect.server.block": "Server yan blok məlumatlarını panoya kopyaladı", "debug.inspect.server.entity": "Server tərə<PERSON> mə<PERSON>larını panoya kopyaladı", "debug.pause.help": "F3 + Esc durdurma menüsüz durdur\n(eğer durdurma mümkünse)", "debug.pause_focus.help": "F3 + P = Fokus itincə fasilə et", "debug.pause_focus.off": "İtirilmiş fokusda fasilə: qeyri-aktiv", "debug.pause_focus.on": "İtirilmiş fokusda fasilə: aktiv", "debug.prefix": "[<PERSON><PERSON><PERSON>]:", "debug.profiling.help": "F3 + L = Başla/dayan profilləşdirmə", "debug.profiling.start": "%s saniyə içərisində profilləşmə başlayıb. Erkən dayandırmaq üçün F3 + L istifadə edin", "debug.profiling.stop": "Profilləşdirmə başa çatdı. Nəticələr %s-ə saxlandı", "debug.reload_chunks.help": "F3 + A = Yığınları yenidən yüklə", "debug.reload_chunks.message": "<PERSON>ü<PERSON>ün yığınlar yenidən yüklənir", "debug.reload_resourcepacks.help": "F3 + T = Qaynaq paketlərini yenidən yüklə", "debug.reload_resourcepacks.message": "<PERSON>ay<PERSON>q paketləri yenidən yükləndi", "debug.show_hitboxes.help": "F3 + B = Vurma qutularını göstər", "debug.show_hitboxes.off": "Vurma qutuları: gizli", "debug.show_hitboxes.on": "Vurma qutuları: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Bu sınaq versiyası beş oyun günü davam edəcək. Əlindən gələnin ən yaxşısını et!", "demo.day.2": "<PERSON><PERSON><PERSON>", "demo.day.3": "Üçüncü Gün", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demo.day.5": "Bu sənin son gün<PERSON><PERSON><PERSON><PERSON>!", "demo.day.6": "Beşinci gününüzü keçdiniz. Sizin yaradılması ekran saxlamaq üçün %s istifadə edin.", "demo.day.warning": "Vax<PERSON><PERSON><PERSON>ın bitməsinə az qaldı!", "demo.demoExpired": "<PERSON><PERSON><PERSON><PERSON> müddəti doldu!", "demo.help.buy": "İndi Al!", "demo.help.fullWrapped": "Bu demo davam edəcək 5 in-oyun gün (təxminən 1 saat 40 dəqiqə real vaxt). İpuçları üçün nailiyyətləri yoxlayın! Əylən!", "demo.help.inventory": "%1$s düyməsinə basaraq inventarını aç", "demo.help.jump": "%1$s düyməsinə basaraq tullan ", "demo.help.later": "Oynamağa Davam Et!", "demo.help.movement": "%1$s, %2$s, %3$s, %4$s və siçandan istifadə edərək ətrafda gəz", "demo.help.movementMouse": "Siçandan istifadə edərək, ətrafa baxın", "demo.help.movementShort": "%1$s, %2$s, %3$s, %4$s düymələrinə basaraq hərəkət et", "demo.help.title": "Minecraft Sınaq Modu", "demo.remainingTime": "Qalan müddət: %s", "demo.reminder": "Nümayiş müddəti başa çatdı. Davam etmək və ya yeni bir dünya başlamaq üçün oyun almaq lazımdır!", "difficulty.lock.question": "Bu dünyanın çətinliyini kilidləmək istədiyinizə əminsiniz? Bu dünya hər zaman %1$s olaraq qalacaq və bunu bir daha dəyişdirə bilməyəcəksiniz.", "difficulty.lock.title": "Dünya Çətinliyini Kilidlə", "disconnect.endOfStream": "<PERSON><PERSON><PERSON><PERSON><PERSON> sonu", "disconnect.exceeded_packet_rate": "<PERSON><PERSON> həddini aşdığına görə vuruldu", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Status sorğusuna məhəl qoyulmur", "disconnect.loginFailedInfo": "Daxil oluna bilmədi: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Çox oyunçulu yararsızlaşdırılıb, lütfən Microsoft hesab ayarlarınızı yoxlayın.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON><PERSON><PERSON> hesab (Oyunu və başladıcını yenidən başlatmağı cəhd edin)", "disconnect.loginFailedInfo.serversUnavailable": "Təsdiqlə<PERSON>ə serverləri hazırda əlçatan deyil. Zə<PERSON>ət olmasa bir daha cəhd edin.", "disconnect.loginFailedInfo.userBanned": "<PERSON><PERSON><PERSON> onlayn oynamaq qadağandır", "disconnect.lost": "Əlaqə İtdi", "disconnect.packetError": "Şəbəkə protokolu xətası", "disconnect.spam": "<PERSON>m səbəbi<PERSON>ə atıldın", "disconnect.timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON> sonu", "disconnect.transfer": "Başqa bir serverə köçürüldü", "disconnect.unknownHost": "Naməlum server", "download.pack.failed": "%s out of %s pack(s) failed to download", "download.pack.progress.bytes": "Tərəqqi: %s (ümumi ölçüsü Bilinmir)", "download.pack.progress.percent": "Tərəqqi: %s%%", "download.pack.title": "Downloading resource pack %s/%s", "editGamerule.default": "Varsayılan: %s", "editGamerule.title": "<PERSON><PERSON> qaydalarını dəyi<PERSON>dir", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorbsiya", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON> ə<PERSON>", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "<PERSON><PERSON>", "effect.minecraft.darkness": "Qaranlıq", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON> səxa<PERSON>ə<PERSON>", "effect.minecraft.fire_resistance": "<PERSON><PERSON>", "effect.minecraft.glowing": "Parlama", "effect.minecraft.haste": "Tələsgənlik", "effect.minecraft.health_boost": "Səhiyyət Artdırıcı", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON> qəhrəmanı", "effect.minecraft.hunger": "Aclıq", "effect.minecraft.infested": "İnfeksiya", "effect.minecraft.instant_damage": "<PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON>z <PERSON>", "effect.minecraft.invisibility": "Görünməzlik", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Şans", "effect.minecraft.mining_fatigue": "Mədənçi Yorğunluğu", "effect.minecraft.nausea": "Bulantı", "effect.minecraft.night_vision": "Gecə Görüşü", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON>", "effect.minecraft.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "<PERSON>s<PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Təzələnmə", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Doyğunluq", "effect.minecraft.slow_falling": "<PERSON><PERSON> dü<PERSON>ə", "effect.minecraft.slowness": "Yavaşlıq", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "<PERSON>üvv<PERSON><PERSON>", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "<PERSON><PERSON>", "effect.minecraft.water_breathing": "Suda Nəfəs Alma", "effect.minecraft.weakness": "Zəiflik", "effect.minecraft.weaving": "Toxuculuq", "effect.minecraft.wind_charged": "Külək uçdu", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Su Adaptasiyası", "enchantment.minecraft.bane_of_arthropods": "B<PERSON>ğumayaq<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.binding_curse": "Bağlanma Lənəti", "enchantment.minecraft.blast_protection": "Partlayış Qoruması", "enchantment.minecraft.breach": "Pozuntu", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> isti<PERSON>ndirmə<PERSON>", "enchantment.minecraft.density": "Sıxlıq", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Effektivlik", "enchantment.minecraft.feather_falling": "Tük Enişi", "enchantment.minecraft.fire_aspect": "Alovdan <PERSON>", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON>orumas<PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Şaxtalaşdırıcı yeriyici", "enchantment.minecraft.impaling": "Şişləmə", "enchantment.minecraft.infinity": "Sonsuzluq", "enchantment.minecraft.knockback": "Səpələmə", "enchantment.minecraft.looting": "Qə<PERSON>mət", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Dəniz Şansı", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Təmir", "enchantment.minecraft.multishot": "Çoxlu atəş", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Projektil <PERSON>ı", "enchantment.minecraft.protection": "<PERSON>orum<PERSON>", "enchantment.minecraft.punch": "Yumruq", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>i yüklənmə", "enchantment.minecraft.respiration": "Qə<PERSON>əmə", "enchantment.minecraft.riptide": "<PERSON><PERSON>", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "İpəksi Toxunuş", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "<PERSON><PERSON> sü<PERSON>", "enchantment.minecraft.sweeping": "Sü<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kənar", "enchantment.minecraft.sweeping_edge": "Süpürü<PERSON><PERSON>", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Qırılmazlıq", "enchantment.minecraft.vanishing_curse": "İtmə Lənəti", "enchantment.minecraft.wind_burst": "Külək partlaması", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> qayıq", "entity.minecraft.acacia_chest_boat": "Akas<PERSON> sandıqlı qayıq", "entity.minecraft.allay": "<PERSON><PERSON><PERSON>", "entity.minecraft.area_effect_cloud": "Sahə təsiri buludu", "entity.minecraft.armadillo": "Armadil", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "Ox", "entity.minecraft.axolotl": "Aks<PERSON>tl", "entity.minecraft.bamboo_chest_raft": "Bambuk sandıqlı sal", "entity.minecraft.bamboo_raft": "Bambuk sal", "entity.minecraft.bat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Arı", "entity.minecraft.birch_boat": "Tozağacı qayıq", "entity.minecraft.birch_chest_boat": "Tozağacı sandıqlı qayıq", "entity.minecraft.blaze": "Bleyz", "entity.minecraft.block_display": "Blok Ekranı", "entity.minecraft.boat": "Qayıq", "entity.minecraft.bogged": "Bataqlıq", "entity.minecraft.breeze": "meh", "entity.minecraft.breeze_wind_charge": "Külək şarjı", "entity.minecraft.camel": "Dəvə", "entity.minecraft.cat": "Pişik", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "kiraz qayıq", "entity.minecraft.cherry_chest_boat": "sandıqlı kiraz bot", "entity.minecraft.chest_boat": "Sandıqlı Qayıq", "entity.minecraft.chest_minecart": "Sandıqlı Vaqon", "entity.minecraft.chicken": "Toyuq", "entity.minecraft.cod": "Treska", "entity.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.cow": "İnək", "entity.minecraft.creaking": "qıcırtı", "entity.minecraft.creaking_transient": "qıcırtı", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Qara Palıd <PERSON>ı", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON>nd palıd sandı<PERSON> qayıq", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "Uzunqulaq", "entity.minecraft.dragon_fireball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.drowned": "Boğulmuş", "entity.minecraft.egg": "Atılmış yumurta", "entity.minecraft.elder_guardian": "Yaşlı Mühafizəçi", "entity.minecraft.end_crystal": "End <PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "Atılmış ender incisi", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "Oyadıcı dişləri", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> şü<PERSON> o 'cazibədar", "entity.minecraft.experience_orb": "Təcr<PERSON><PERSON><PERSON> Kürə<PERSON>", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>", "entity.minecraft.falling_block": "Düşən Blok", "entity.minecraft.falling_block_type": "Düşən %s", "entity.minecraft.fireball": "<PERSON>ov Top<PERSON>", "entity.minecraft.firework_rocket": "Fişəng Raketi", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON>", "entity.minecraft.fox": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Qurbağa", "entity.minecraft.furnace_minecart": "Sobalı Vaqon", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Div", "entity.minecraft.glow_item_frame": "Parıltılı Əşya Çərçivəsi", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Mühavizəçi", "entity.minecraft.happy_ghast": "xo<PERSON>b<PERSON><PERSON>t ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.horse": "At", "entity.minecraft.husk": "<PERSON><PERSON><PERSON>", "entity.minecraft.illusioner": "İllüziyaçı", "entity.minecraft.interaction": "Qarşılıqlı əlaqə", "entity.minecraft.iron_golem": "<PERSON><PERSON><PERSON>", "entity.minecraft.item": "Əşya", "entity.minecraft.item_display": "Element Ekranı", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Tropik ağac qayıq", "entity.minecraft.jungle_chest_boat": "Tropik ağac sandıqlı qayıq", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Qayış düyümü", "entity.minecraft.lightning_bolt": "İldırım", "entity.minecraft.lingering_potion": "Sürəkli iksir", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON>", "entity.minecraft.magma_cube": "Magma Kubu", "entity.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON> sandı<PERSON>ı qayıq", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Qatır", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON> qayıq", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Bədbəxt Element Kürü", "entity.minecraft.painting": "<PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON> palıd qayıq", "entity.minecraft.pale_oak_chest_boat": "<PERSON><PERSON>un palıd sandı<PERSON>ı qayıq", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON>", "entity.minecraft.pig": "Donuz", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON> piqlin", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON>", "entity.minecraft.player": "Oyunçu", "entity.minecraft.polar_bear": "Qütb Ayısı", "entity.minecraft.potion": "İksir", "entity.minecraft.pufferfish": "Kirpi Balığı", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Dağıdıcı", "entity.minecraft.salmon": "Qızılbalıq", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Skelet", "entity.minecraft.skeleton_horse": "Skelet At", "entity.minecraft.slime": "Palçıq", "entity.minecraft.small_fireball": "Kiçik Alov Topu", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON>", "entity.minecraft.snowball": "<PERSON><PERSON>", "entity.minecraft.spawner_minecart": "Monster Spawner ilə Minecart", "entity.minecraft.spectral_arrow": "Spektral Ox", "entity.minecraft.spider": "Hörümcək", "entity.minecraft.splash_potion": "Partlayıcı iksir", "entity.minecraft.spruce_boat": "K<PERSON>k<PERSON>ayı<PERSON>", "entity.minecraft.spruce_chest_boat": "Küknar <PERSON>ı<PERSON>lı qayıq", "entity.minecraft.squid": "Mürəkkəb Balığı", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "Çömçəquyruq", "entity.minecraft.text_display": "Mətn Ekranı", "entity.minecraft.tnt": "Aktivləşdirilmiş TNT", "entity.minecraft.tnt_minecart": "TNT-li <PERSON>aqon", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Üçbaşlı Nizə", "entity.minecraft.tropical_fish": "Tropik Balıq", "entity.minecraft.tropical_fish.predefined.0": "Aktiniya", "entity.minecraft.tropical_fish.predefined.1": "Qara cərrahbalığı", "entity.minecraft.tropical_fish.predefined.10": "Moorish İdol", "entity.minecraft.tropical_fish.predefined.11": "Kəpənək balığı", "entity.minecraft.tropical_fish.predefined.12": "Parrotfish", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "Qırmızı Cichlid", "entity.minecraft.tropical_fish.predefined.15": "Qırmızı Liped Blenny", "entity.minecraft.tropical_fish.predefined.16": "Qırmızı Snayper", "entity.minecraft.tropical_fish.predefined.17": "Diş ipi", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Tetik balıq", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>ı", "entity.minecraft.tropical_fish.predefined.20": "Sarıtail Parrotfish", "entity.minecraft.tropical_fish.predefined.21": "Sarı Tan", "entity.minecraft.tropical_fish.predefined.3": "Kəpənək balığı", "entity.minecraft.tropical_fish.predefined.4": "Tsixlosom", "entity.minecraft.tropical_fish.predefined.5": "Təlxək balığı", "entity.minecraft.tropical_fish.predefined.6": "Siam balığı", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "İmperator Qırmızı Snapper", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>i balığı", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Kəpənək", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON> bal<PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Parıltı", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Zolaqlı", "entity.minecraft.tropical_fish.type.sunstreak": "Günəş şüası balığı", "entity.minecraft.turtle": "Tısbağa", "entity.minecraft.vex": "Zəhlətökən", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Xəriteçi", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "Əkinçi", "entity.minecraft.villager.fisherman": "Balıqçı", "entity.minecraft.villager.fletcher": "Oxçu", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Kitabxanaçı", "entity.minecraft.villager.mason": "<PERSON>", "entity.minecraft.villager.nitwit": "Avam", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON>t ustası", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON>", "entity.minecraft.vindicator": "İntiqamçı", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON> tacir", "entity.minecraft.warden": "Gözətçi", "entity.minecraft.wind_charge": "Külək şarjı", "entity.minecraft.witch": "Cadugər", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON> kəlləsi", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Zombi At", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "Zombiləşmiş piqlin", "entity.not_summonable": "%s tipli obyekti çağırmaq mümkün deyil", "event.minecraft.raid": "Basqın", "event.minecraft.raid.defeat": "Məğlubiyyət", "event.minecraft.raid.defeat.full": "Basqın - Məğlubiyyət", "event.minecraft.raid.raiders_remaining": "Reyderlər qalıb:%s", "event.minecraft.raid.victory": "Qələbə", "event.minecraft.raid.victory.full": "Basqın - Qələbə", "filled_map.buried_treasure": "Basdırılmış xəzinə xəritəsi", "filled_map.explorer_jungle": "Cəngəllik K<PERSON>", "filled_map.explorer_swamp": "Bataqlıq <PERSON>", "filled_map.id": "No. %s", "filled_map.level": "(Səviyyə %s/%s)", "filled_map.locked": "Bağlanıb", "filled_map.mansion": "Məşəlik Kəşfiyyatçısı Xəritəsi", "filled_map.monument": "Okean Kəşfiyyatçısı Xəritəsi", "filled_map.scale": "Miqyas: 1:%s", "filled_map.trial_chambers": "Sınaq <PERSON>ları Xəritəsi", "filled_map.unknown": "<PERSON><PERSON><PERSON> xəritə", "filled_map.village_desert": "<PERSON><PERSON><PERSON>", "filled_map.village_plains": "Düzənlik Kənd Xərit<PERSON>", "filled_map.village_savanna": "Savanna <PERSON>", "filled_map.village_snowy": "Qarlı Kənd <PERSON>", "filled_map.village_taiga": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Klassik Düz", "flat_world_preset.minecraft.desert": "Ç<PERSON>l", "flat_world_preset.minecraft.overworld": "Dünya", "flat_world_preset.minecraft.redstone_ready": "Qızıl Daşa Hazır", "flat_world_preset.minecraft.snowy_kingdom": "Qarlı Krallıq", "flat_world_preset.minecraft.the_void": "Boşluq", "flat_world_preset.minecraft.tunnelers_dream": "Tunelaçan<PERSON>ın <PERSON>", "flat_world_preset.minecraft.water_world": "Su Dünyası", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON><PERSON>", "gameMode.changed": "Oyun modunuz %s o<PERSON><PERSON> də<PERSON>dirildi", "gameMode.creative": "Yarad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "<PERSON><PERSON><PERSON>!", "gameMode.spectator": "İzləyici Modu", "gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "İnkişafları elan et", "gamerule.blockExplosionDropDecay": "Blokların qarşılıqlı partlamalarında bəzi bloklar qənimətini atmayacaq", "gamerule.blockExplosionDropDecay.description": "Blokların qarşılıqlı təsirindən yaranan partlayışlar nəticəsində məhv olan bloklardan bəzi damcılar partlayışda itir.", "gamerule.category.chat": "Çat", "gamerule.category.drops": "Droplar", "gamerule.category.misc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.mobs": "Canl<PERSON>lar", "gamerule.category.player": "Oyunçu", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.updates": "<PERSON><PERSON><PERSON> gü<PERSON>əllə<PERSON>ə<PERSON>", "gamerule.commandBlockOutput": "<PERSON><PERSON>r bloku çıxışlarını yayımla", "gamerule.commandModificationBlockLimit": "Komanda Modifikasiyası Blok Limiti", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": "Elitra hərəkət kontrolunu bağla", "gamerule.disableRaids": "Basqınları bağla", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> avan<PERSON> vaxtı", "gamerule.doEntityDrops": "Varlıq ləvazimatı dü<PERSON>ün", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON>n (inventarlar daxil), ə<PERSON><PERSON>, q<PERSON><PERSON><PERSON><PERSON>an və s. düşən əşyalar kontrol edilsin.", "gamerule.doFireTick": "<PERSON><PERSON> ye<PERSON>ən<PERSON>ə<PERSON>", "gamerule.doImmediateRespawn": "<PERSON>ə<PERSON><PERSON> yenidən başlama", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON> spaun olsun", "gamerule.doLimitedCrafting": "İstehsal üçün resept tələb olunsun", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON>ə<PERSON> aktiv<PERSON>ə, oyunçular ancaq açıq reseptləri yarada bilərlər.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON><PERSON> qəni<PERSON>ət dü<PERSON>n", "gamerule.doMobLoot.description": "Tə<PERSON><PERSON><PERSON><PERSON>ə kürələri də daxil olmaqla dəstələrdən qaynaq düşmələrinə nəzarət edir.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON> spaun olsun", "gamerule.doMobSpawning.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ın ayrı qaydaları ola bilər.", "gamerule.doPatrolSpawning": "<PERSON><PERSON>ur patrulları spaun olsun", "gamerule.doTileDrops": "Bloklar düş<PERSON>ün", "gamerule.doTileDrops.description": "Tə<PERSON><PERSON><PERSON><PERSON>ə orbs da daxil olmaqla bloklardan qaynaq düşməsinə nəzarət edir.", "gamerule.doTraderSpawning": "Spawn Wandering Traders", "gamerule.doVinesSpread": "Üzümlər yayıldı", "gamerule.doVinesSpread.description": "Vines blokunun təsadüfi olaraq qonşu bloklara yayılıb yayılmamasına nəzarət edir. Ağlayan üzümlər, bükülmüş üzümlər və s. kimi digər üzüm bloklarına təsir göstərmir.", "gamerule.doWardenSpawning": "<PERSON>", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON> və<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON><PERSON>ə<PERSON> al", "gamerule.enderPearlsVanishOnDeath": "At<PERSON>lan Ender İnciləri <PERSON>ü<PERSON>ə yox olur", "gamerule.enderPearlsVanishOnDeath.description": "Bir oyunçunun atdığı Ender İnciləri o oyunçu öləndə yox olur.", "gamerule.entitiesWithPassengersCanUsePortals": "S<PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON> olan qurumlar portallardan istifadə edə bilərlər", "gamerule.entitiesWithPassengersCanUsePortals.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> olan quru<PERSON>a Nether Portals, End Portals və End Gateways vasitəsilə teleportasiya etməyə icazə verin.", "gamerule.fallDamage": "<PERSON>üş<PERSON>ə xəsarəti al", "gamerule.fireDamage": "<PERSON><PERSON> xə<PERSON><PERSON><PERSON> al", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON>n oyunçuları bağışlayın", "gamerule.forgiveDeadPlayers.description": "Qəz<PERSON><PERSON><PERSON>ənmiş neytral dəstələr hədəf alan oyunçunun yaxınlığında öldüyündə qəzəblənməyi dayandırırlar.", "gamerule.freezeDamage": "Soyuqa görə xəsarət al", "gamerule.globalSoundEvents": "<PERSON><PERSON>bal sə<PERSON>", "gamerule.globalSoundEvents.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ən oyun hadisələri baş verəndə, mə<PERSON><PERSON><PERSON>ə<PERSON>, bir <PERSON> kürü tökür, səs hər yerdə eşidilir.", "gamerule.keepInventory": "Öləndən sonra inventarını saxla", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "Axan lava iki tərəfdən lava mənbələri ilə əhatə olunduqda mənbəyə çevrilir.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "<PERSON><PERSON> ə<PERSON> ya<PERSON>", "gamerule.maxCommandChainLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> ölçü limiti", "gamerule.maxCommandChainLength.description": "Komanda blok zəncirlərinə və funksiyalarına aiddir.", "gamerule.maxCommandForkCount": "Komandanın kontekst limiti", "gamerule.maxCommandForkCount.description": "\"Fərqlə icra et\" kimi əmrlər tərəfindən istifadə edilə bilən kontekstlərin maksimum sayı.", "gamerule.maxEntityCramming": "Varlıq sıxışması alt sınırı", "gamerule.minecartMaxSpeed": "vaqon maksimum sürət", "gamerule.minecartMaxSpeed.description": "<PERSON>uda hərəkət edən <PERSON>tın maksimal standart sürəti.", "gamerule.mobExplosionDropDecay": "<PERSON><PERSON><PERSON><PERSON><PERSON> part<PERSON>ı<PERSON><PERSON>a bəzi bloklar qənimətini yerə atmayacaq", "gamerule.mobExplosionDropDecay.description": "Kütl<PERSON><PERSON><PERSON><PERSON> törətdiyi partlayışlar nəticəsində dağılan blokların damcılarının bir hissəsi partlayışda itir.", "gamerule.mobGriefing": "Dağıdıcı mob hərəkətlərinə icazə verin", "gamerule.naturalRegeneration": "Can yenilənsin", "gamerule.playersNetherPortalCreativeDelay": "Oyunçunun Nether portalı yaradıcı rejimdə gecikmə", "gamerule.playersNetherPortalCreativeDelay.description": "Yaradıcı rejim oyunçusunun ölçüləri dəyişməzdən əvvəl Nether portalında dayanmalı olduğu vaxt (gənə ilə).", "gamerule.playersNetherPortalDefaultDelay": "Qeyri-kreativ rejimdə oyunçunun Nether portal gecikməsi", "gamerule.playersNetherPortalDefaultDelay.description": "Qeyri-kreativ rejim oyunçusunun ölçüləri dəyişməzdən əvvəl Nether portalında dayanması lazım olan vaxt (gənə ilə).", "gamerule.playersSleepingPercentage": "<PERSON><PERSON><PERSON> faizi", "gamerule.playersSleepingPercentage.description": "Gecəni keçmək üçün yatmalı olan oyunçuların faizi.", "gamerule.projectilesCanBreakBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> blokları qıra bilər", "gamerule.projectilesCanBreakBlocks.description": "<PERSON>ə<PERSON>ə mərmilərinin onlar tərəfindən məhv edilə bilən blokları məhv edib-etməməsinə nəzarət edir.", "gamerule.randomTickSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gənə sürəti", "gamerule.reducedDebugInfo": "Debug məlumatını azaldın", "gamerule.reducedDebugInfo.description": "Debug ekranının məzmununu məhdudlaşdırır.", "gamerule.sendCommandFeedback": "<PERSON><PERSON><PERSON> geri bi<PERSON><PERSON> g<PERSON>r", "gamerule.showDeathMessages": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>ər", "gamerule.snowAccumulationHeight": "Qar yığılmasının hündürlüyü", "gamerule.snowAccumulationHeight.description": "Qar yağanda yerdə ən çox bu sayda təbəqəyə qədər qar təbəqələri əmələ gəlir.", "gamerule.spawnChunkRadius": "K<PERSON>rü yığını radiusu", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kürü mövqeyi ətrafında yüklü qalan parçaların miqdarı.", "gamerule.spawnRadius": "<PERSON>zdən doğul<PERSON>", "gamerule.spawnRadius.description": "Oyunçuların kürü tökə biləcəyi kürü nöqtəsi ətrafındakı ərazinin ölçüsünə nəzarət edir.", "gamerule.spectatorsGenerateChunks": "İzləyicilərin ərazi yaradılmasına icazə ver", "gamerule.tntExplodes": "TNT-nin işə salınmasına və partlamasına icazə verin", "gamerule.tntExplosionDropDecay": "TNT partlayışlarında bəzi bloklar qənimətini atmayacaq", "gamerule.tntExplosionDropDecay.description": "TNT-nin yaratdığı partlayışlar nəticəsində dağılan blokların damcılarının bir hissəsi partlayış zamanı itir.", "gamerule.universalAnger": "Universaldır hirs", "gamerule.universalAnger.description": "Qəz<PERSON>blənmiş neytral dəstələr yalnız onları qəzəbləndirən oyunçuya deyil, yaxın<PERSON><PERSON><PERSON><PERSON>ı hər hansı bir oyunçuya hücum edirlər. BağışlayınDeadPlayers əlil olduqda yaxşı işləyir.", "gamerule.waterSourceConversion": "Su mənbəyə çevrilir", "gamerule.waterSourceConversion.description": "Axan su iki tərəfdən su mənbələri ilə əhatə olunduqda mənbəyə çevrilir.", "generator.custom": "<PERSON><PERSON>", "generator.customized": "Köhnə özəlləşdirilmiş", "generator.minecraft.amplified": "GENİŞLƏDİLMİŞ", "generator.minecraft.amplified.info": "Qeyd: <PERSON><PERSON><PERSON><PERSON> əyləncə <PERSON>ü<PERSON>ü<PERSON>, güc<PERSON>ü bir kompüter tələb edir.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON><PERSON>", "generator.minecraft.flat": "Dümdüz", "generator.minecraft.large_biomes": "Böyük Biomlar", "generator.minecraft.normal": "Varsayılan", "generator.minecraft.single_biome_surface": "Bir biom", "generator.single_biome_caves": "<PERSON><PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "Bu hesabatı təqdim etməklə siz təqdim etdiyiniz məlumatların dəqiq və tam məlumatlı olduğunu təsdiq edirsiniz.", "gui.abuseReport.comments": "Şə<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Təfərrüatları bölüşmək bizə yaxşı məlumatlı qərar qəbul etməyə kömək edəcək.", "gui.abuseReport.discard.content": "Əgər tərk etsəniz, bu hesabatı və şərhlərinizi itirəcəksiniz.\nAyrılmaq istədiyinizə əminsiniz?", "gui.abuseReport.discard.discard": "Çıx və Xəbərdarlığı Sil", "gui.abuseReport.discard.draft": "Qaralama o<PERSON>la", "gui.abuseReport.discard.return": "Düzəltməyə Dəvam et", "gui.abuseReport.discard.title": "<PERSON><PERSON><PERSON> və şər<PERSON>ər <PERSON>?", "gui.abuseReport.draft.content": "Mövcud hesabatı redaktə etməyə davam etmək və ya onu ləğv edib yenisini yaratmaq istərdiniz?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Düzəltməyə Dəvam et", "gui.abuseReport.draft.quittotitle.content": "Onu redaktə etməyə davam etmək və ya ləğv etmək istərdiniz?", "gui.abuseReport.draft.quittotitle.title": "Çıxdığın<PERSON>z zaman itiriləcək qaralama söhbət hesabatınız var", "gui.abuseReport.draft.title": "Qaralama söhbət hesabatı redaktə edilsin?", "gui.abuseReport.error.title": "Şikayəti göndərmək alınmadı", "gui.abuseReport.message": "Pis davranışı harada müşahidə etdiniz?\nBu, iş<PERSON>zi araşdırmaqda bizə kömək edəcək.", "gui.abuseReport.more_comments": "Nə baş verdiyini təsvir edin:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "Siz \"%s\" haqqında məlumat verirsiniz.", "gui.abuseReport.name.title": "Oyunçunun adını bildirin", "gui.abuseReport.observed_what": "Bunu niyə xəbər verirsən?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON> haqqında məlumat əldə edin", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkotik və ya alkoqol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON><PERSON> başqalarını narkotiklə əlaqəli qeyri-qanuni fəaliyyətlərdə iştirak etməyə təşviq edir və ya yetkinlik yaşına çatmayanları içki içməyə təşviq edir.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Uşağa qarşı seksual istismar və ya təcavüz", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON><PERSON> olan nalayiq davranı<PERSON> danışır və ya başqa cür təbliğ edir.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON>, şəxsi<PERSON>yətə oxşama və ya yalan məlumat", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON><PERSON> başqasının reputasiyasına xələl gə<PERSON>, özünü olmayan biri kimi təqdim edir və ya başqalarını istismar etmək və ya çaşdırmaq məqsədi ilə yalan məlumat paylaşır.", "gui.abuseReport.reason.description": "Təsvir:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON>", "gui.abuseReport.reason.generic": "<PERSON>ən onlara hesabat vermək istəyirəm", "gui.abuseReport.reason.generic.description": "Onlara əsəbləşirəm / bəyənmədiyim bir iş görüblər.", "gui.abuseReport.reason.harassment_or_bullying": "Seksual qısnama və ya zorbalıq", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>ə sizi və ya başqasını utandırır, hücum edir və ya təhqir edir. Buraya kimsə razılıq olmadan dəfələrlə sizinlə və ya başqası ilə əlaqə saxlamağa cəhd etdikdə və ya razılıq olmadan sizin və ya başqası haqqında şəxsi şəxsi məlumat yerləşdirməsi (\"doxing\") daxildir.", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON><PERSON> nitqi", "gui.abuseReport.reason.hate_speech.description": "kimsə xüsusiyyətlərinə və ya şəxsiyyətinə, dininə, irqinə və ya cinsiyyət növünə görə sizə və ya başqa bir oyunçuya hücum edir", "gui.abuseReport.reason.imminent_harm": "<PERSON>a<PERSON><PERSON><PERSON><PERSON> zərər - Baş<PERSON>larına zərər verməklə hədələmə", "gui.abuseReport.reason.imminent_harm.description": "Kimsə sizə və ya başqasına real həyatda zərər verməklə hədələyir.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Konse<PERSON><PERSON> o<PERSON>yan intim g<PERSON>", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON>ə şəxsi və intim şəkillərdən danışır, pay<PERSON>ş<PERSON>r və ya başqa cür təbliğ edir.", "gui.abuseReport.reason.self_harm_or_suicide": "Qaç<PERSON><PERSON><PERSON> zərər - Özünə zərər və ya intihar", "gui.abuseReport.reason.self_harm_or_suicide.description": "Kimsə real həyatda özünə zərər verməklə hədələyir və ya real həyatda özünə zərər verməkdən danışır.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Cinsi hərəkətlərə, cinsi orqanlara və cinsi zorakılığa aid qrafik xarakterli dərilər.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorizm və ya şiddətli ekstremizm", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON><PERSON>, dini, ideoloji və ya digər səbəblərdən terror aktları və ya zorakı ekstremizm haqqında danışır, təbliğ edir və ya törətməklə hədələyir.", "gui.abuseReport.reason.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.report_sent_msg": "Hesabatınızı uğurla aldıq. Çox sağ ol!\n\nKomandamız onu ən qısa zamanda nəzərdən keçirəcək.", "gui.abuseReport.select_reason": "<PERSON><PERSON><PERSON>", "gui.abuseReport.send": "<PERSON><PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>ı<PERSON>ın", "gui.abuseReport.send.error_message": "Hesabatınızı göndərərkən xəta qaytarıldı:\n'%s'", "gui.abuseReport.send.generic_error": "Hesabatınızı göndərərkən gözlənilməz xəta ilə qarşılaşdı.", "gui.abuseReport.send.http_error": "Xəbərdarlıq göndərilərkən gözlənilməz HTTP xətası baş verdi.", "gui.abuseReport.send.json_error": "Hesabatınızı göndərərkən düzgün olmayan yüklə qarşılaşdınız.", "gui.abuseReport.send.no_reason": "<PERSON><PERSON><PERSON><PERSON><PERSON> olma<PERSON> hesabat kate<PERSON> seçin", "gui.abuseReport.send.not_attested": "<PERSON><PERSON><PERSON><PERSON>t olmasa yuxarıdakı mətni oxuyun və hesabatı göndərə bilmək üçün qeyd xanasına işarələyin", "gui.abuseReport.send.service_unavailable": "Sui-istifadə Reportinq xidmətinə daxil olmaq mümkün deyil. İnternetə qoşulduğunuzdan əmin olun və yenidən cəhd edin.", "gui.abuseReport.sending.title": "Şikayətiniz göndərilir...", "gui.abuseReport.sent.title": "Şikayət <PERSON>", "gui.abuseReport.skin.title": "Oyunçu Görünüşündən Şikayət edin", "gui.abuseReport.title": "Şikayət Oyunçu", "gui.abuseReport.type.chat": "Çat Mesajları", "gui.abuseReport.type.name": "Oyunçu Adı", "gui.abuseReport.type.skin": "Oyunçu Görünüşü", "gui.acknowledge": "Qəbul etmək", "gui.advancements": "İnkişaflar", "gui.all": "Hamısı", "gui.back": "<PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\n<PERSON>ha çoxunu öyrənmək üçün linkə daxil olun: %s", "gui.banned.description.permanent": "<PERSON><PERSON>b<PERSON><PERSON><PERSON>z həmişəlik qadağan edilib, yəni siz onlayn oynaya və ya Realms-a qoşula bilməyəcəksiniz.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "Kod: %s", "gui.banned.description.reason_id_message": "Kod: %s - %s", "gui.banned.description.temporary": "%s O vaxta qədər siz onlayn oynaya və ya Realms-a qoşula bilməzsiniz.", "gui.banned.description.temporary.duration": "Hesabın<PERSON>z müvəqqəti olaraq dayandırılıb və %s sonra yenidən aktivləşdiriləcək.", "gui.banned.description.unknownreason": "Bu yaxınlarda hesabınız tərəfindən pis davranışla bağlı hesabat aldıq. Moderatorlarımız indi işinizi nəzərdən keçirdilər və bunun Minecraft İcma Standartlarına zidd olduğunu müəyyən etdilər.", "gui.banned.name.description": "Sizin indiki adınız - \"%s\" - İcma Standartlarımızı pozur. Siz tək oyunçulu oynaya bilərsiniz, lakin onlayn oynamaq üçün adınızı dəyişməlisiniz\n\nƏtraflı məlumat almaq üçün və ya apelyasiya vermək üçün göstərilən keçidə daxil olun: %s", "gui.banned.name.title": "Multiplayerdə ada icazə verilmir", "gui.banned.reason.defamation_impersonation_false_information": "Başqalarını istismar etmək və ya aldatmaq üçün imitasiya və ya məlumatın paylaşılması", "gui.banned.reason.drugs": "Qanunsuz narkotiklərə istinadlar", "gui.banned.reason.extreme_violence_or_gore": "Real həyatda həddindən artıq zorakılıq və ya qan təsvirləri", "gui.banned.reason.false_reporting": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> artıq yalan və ya qeyri-dəqiq <PERSON>", "gui.banned.reason.fraud": "Saxta şəkildə məzmunun əldə edilməsi və ya istifadəsi", "gui.banned.reason.generic_violation": "İcma <PERSON>artlarının pozulması", "gui.banned.reason.harassment_or_bullying": "İstiq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zərərli şəkildə istifadə edilən təhqiramiz dil", "gui.banned.reason.hate_speech": "Nifrət nitqi və ya ayrı-seçkilik", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON><PERSON> q<PERSON>na, terror təşkilatlarına və ya bədnam şəxsiyyətlərə istinadlar", "gui.banned.reason.imminent_harm_to_person_or_property": "İnsanlara və ya əmlaka real həyatda zərər vurmaq niyyəti", "gui.banned.reason.nudity_or_pornography": "Əxlaqsız və ya pornoqrafik materialın nümayişi", "gui.banned.reason.sexually_inappropriate": "Cinsi xarakterli mövzular və ya məzmunlar", "gui.banned.reason.spam_or_advertising": "Spam və ya reklam", "gui.banned.skin.description": "Sizin indiki görünüşünüz İcma Standartlarımızı pozur. Siz ilkin görüşünüz ilə oyuna dəvam edə bilərsiz, və ya yenisini seçin.\n\nƏtraflı məlumat almaq üçün və ya apelyasiya vermək üçün göstərilən keçidə daxil olun: %s", "gui.banned.skin.title": "Görünüş İcazə Verilmir", "gui.banned.title.permanent": "<PERSON><PERSON><PERSON> da<PERSON><PERSON>", "gui.banned.title.temporary": "<PERSON><PERSON><PERSON> <PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>", "gui.cancel": "Ləğv Et", "gui.chatReport.comments": "Şə<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Təfərrüatları bölüşmək bizə yaxşı məlumatlı qərar qəbul etməyə kömək edəcək.", "gui.chatReport.discard.content": "Əgər tərk etsəniz, bu hesabatı və şərhlərinizi itirəcəksiniz.\nAyrılmaq istədiyinizə əminsiniz?", "gui.chatReport.discard.discard": "Çıx və Xəbərdarlığı Sil", "gui.chatReport.discard.draft": "Qaralama olaraq saxlaya", "gui.chatReport.discard.return": "Redaktə etməyə Davam et", "gui.chatReport.discard.title": "<PERSON><PERSON><PERSON> və şər<PERSON>ər <PERSON>?", "gui.chatReport.draft.content": "Mövcud hesabatı redaktə etməyə davam etmək və ya ondan imtina edib yenisini yaratmaq istəyirsiniz?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "<PERSON><PERSON><PERSON>ya davam edin", "gui.chatReport.draft.quittotitle.content": "Onu redaktə etməyə davam etmək və ya ləğv etmək istərdiniz?", "gui.chatReport.draft.quittotitle.title": "Çıxsanız itiriləcək bir çat hesabatı layihəniz var", "gui.chatReport.draft.title": "Çat hesabatı layihəsini redaktə edirsiniz?", "gui.chatReport.more_comments": "Nə baş verdiyini təsvir edin:", "gui.chatReport.observed_what": "Bunu niyə xəbərdarlıq edirsiniz?", "gui.chatReport.read_info": "Şikayət Haqqında Məlumat Öyrən", "gui.chatReport.report_sent_msg": "Hesabatınızı uğurla aldıq. Çox sağ ol!\n\nKomandamız onu ən qısa zamanda nəzərdən keçirəcək.", "gui.chatReport.select_chat": "Şikayət etmək üçün Çat Mesajlarını seçin", "gui.chatReport.select_reason": "Hesabat mifologiyası seçin", "gui.chatReport.selected_chat": "%s Çat Mesajı(lar) ı Şikayət etmək üçün Seçildi", "gui.chatReport.send": "<PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmazsa mesajı qısaldın", "gui.chatReport.send.no_reason": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>t kate<PERSON> seçin", "gui.chatReport.send.no_reported_messages": "Şikayət etmək üçün ən azı bir çat mesajı seçin", "gui.chatReport.send.too_many_messages": "Şikayətdə çoxlu xəbər seçməyə çalışın", "gui.chatReport.title": "<PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Bu seçimi əhatə edən mesajlar əlavə kontekst təmin etmək üçün daxil ediləcək", "gui.chatSelection.fold": "%s mesaj(lar) gizlədilib", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s çata qoşuldu", "gui.chatSelection.message.narrate": "%s dedi: %s, yeri %s", "gui.chatSelection.selected": "%s/%s mesaj(lar) seçildi", "gui.chatSelection.title": "Şikayət etmək üçün Çat Mesajlarını seçin", "gui.continue": "<PERSON><PERSON><PERSON> et", "gui.copy_link_to_clipboard": "Linki panoya kopyalayın", "gui.days": "%s gün(lər)", "gui.done": "Bitdi", "gui.down": "Aşağı", "gui.entity_tooltip.type": "Növ: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s fayl rədd edildi", "gui.fileDropFailure.title": "Faylları əlavə etmək alınmadı", "gui.hours": "%s saat(lar)", "gui.loadingMinecraft": "Minecraft Yüklənir", "gui.minutes": "%s dəqiqə(lər)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s düymə", "gui.narrate.editBox": "redaktə pəncərə:", "gui.narrate.slider": "%s sürüşdürən", "gui.narrate.tab": "%s nişanı", "gui.no": "<PERSON><PERSON><PERSON>", "gui.none": "<PERSON><PERSON>", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "<PERSON><PERSON><PERSON> açın", "gui.proceed": "Davam edin", "gui.recipebook.moreRecipes": "Daha çoxu üçün siçanın sağ düyməsini basın", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Axtar...", "gui.recipebook.toggleRecipes.all": "Hamısı göstərilir", "gui.recipebook.toggleRecipes.blastable": "Ərid<PERSON>ə bilinən filizlər gö<PERSON>ərilir", "gui.recipebook.toggleRecipes.craftable": "Haz<PERSON>rlana bilənlər g<PERSON>ir", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>ə bilinənlər g<PERSON>ərilir", "gui.recipebook.toggleRecipes.smokable": "Tüstül<PERSON><PERSON><PERSON>n göstərilir", "gui.report_to_server": "Serverə hesabat verin", "gui.socialInteractions.blocking_hint": "Microsoft hesabı ilə idarə edin", "gui.socialInteractions.empty_blocked": "Çatda bloklanan oyunçu yoxdur", "gui.socialInteractions.empty_hidden": "Çatda gizlədilən oyunçu yoxdur", "gui.socialInteractions.hidden_in_chat": "%s tərəfindən göndərilən çat mesajları gizlədiləcək", "gui.socialInteractions.hide": "Çatda gizlə", "gui.socialInteractions.narration.hide": "Mesajı %s gizlə", "gui.socialInteractions.narration.report": "Hesabat oyunçu %s", "gui.socialInteractions.narration.show": "Mesajı %s göstər", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON>u adla heç bir oyunçu tapılmadı", "gui.socialInteractions.search_hint": "Axtarış...", "gui.socialInteractions.server_label.multiple": "%s - %s oyunçular", "gui.socialInteractions.server_label.single": "%s - %s oyunçu", "gui.socialInteractions.show": "Söhbətdə göstər", "gui.socialInteractions.shown_in_chat": "%s tərəfindən göndərilən çat mesajları göstəriləcək", "gui.socialInteractions.status_blocked": "Bloklanıb", "gui.socialInteractions.status_blocked_offline": "Bloklandı - Oflayn", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Gizli - Oflayn", "gui.socialInteractions.status_offline": "Oflayn", "gui.socialInteractions.tab_all": "Hamısı", "gui.socialInteractions.tab_blocked": "Bloklandı", "gui.socialInteractions.tab_hidden": "Gizlət", "gui.socialInteractions.title": "<PERSON><PERSON><PERSON>ər", "gui.socialInteractions.tooltip.hide": "Mesajlarını gizlə", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "Şika<PERSON>ət sistemi istifadə edilə bilməz", "gui.socialInteractions.tooltip.report.no_messages": "Oyunçudan hesabat veriləcək mesaj yoxdur", "gui.socialInteractions.tooltip.report.not_reportable": "Bu oyunçu haqqında məlumat verilə bilməz, çü<PERSON><PERSON> onun serverdəki mesajları təsdiqlənə bilmir", "gui.socialInteractions.tooltip.show": "Mesajları gö<PERSON>ər", "gui.stats": "Statistikalar", "gui.toMenu": "Server si<PERSON><PERSON><PERSON><PERSON><PERSON> qayıt", "gui.toRealms": "Realms siyahısına qayıt", "gui.toTitle": "<PERSON><PERSON><PERSON>t", "gui.toWorld": "Dünyaların siyahısına qayıt", "gui.togglable_slot": "Slotu kilidləmək üçün klikləyin", "gui.up": "Yuxarı", "gui.waitingForResponse.button.inactive": "geri (%ss)", "gui.waitingForResponse.title": "server ü<PERSON><PERSON>n <PERSON>", "gui.yes": "<PERSON><PERSON><PERSON>", "hanging_sign.edit": "Asma İşarə Mesajını Redaktə edin", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Çağırış", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "Hiss et", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Seek", "instrument.minecraft.sing_goat_horn": "Oxu", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory.binSlot": "Əşyanı Sil", "inventory.hotbarInfo": "%1$s+%2$s isti panel saxla", "inventory.hotbarSaved": "İsti maddə paneli saxlanılır (%1$s+%2$s ilə bərpa edin)", "item.canBreak": "Qırabilər:", "item.canPlace": "<PERSON><PERSON><PERSON><PERSON>ə yerləşdirilə bilər:", "item.canUse.unknown": "<PERSON><PERSON><PERSON>", "item.color": "Rəng: %s", "item.components": "%s komponent(lər)", "item.disabled": "Deaktiv edilmiş element", "item.durability": "Dözümlülük: %s / %s", "item.dyed": "Boyalı", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON>ı<PERSON>", "item.minecraft.acacia_chest_boat": "Sandıqlı Akasiya Qayıq", "item.minecraft.allay_spawn_egg": "Kürü Yumurtasını Allay", "item.minecraft.amethyst_shard": "Ametist parçası", "item.minecraft.angler_pottery_shard": "Balıqçı Saxsı Parçası", "item.minecraft.angler_pottery_sherd": "Balıqçı dulusçuluq qabığı", "item.minecraft.apple": "Alma", "item.minecraft.archer_pottery_shard": "Oxatan du<PERSON>ı", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON><PERSON> du<PERSON> qabı", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON> pulu", "item.minecraft.armadillo_spawn_egg": "Armadillo çağırma yumurtası", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON> s<PERSON>i", "item.minecraft.arms_up_pottery_shard": "Dulusçuluq parçasını silahlandırın", "item.minecraft.arms_up_pottery_sherd": "Dulusçuluq qabını silahlandırın", "item.minecraft.arrow": "Ox", "item.minecraft.axolotl_bucket": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.axolotl_spawn_egg": "Aksolotl spaun yumurtası", "item.minecraft.baked_potato": "Bişmiş <PERSON>", "item.minecraft.bamboo_chest_raft": "Sinə ilə bambuk sal", "item.minecraft.bamboo_raft": "Bambuk sal", "item.minecraft.bat_spawn_egg": "Yarasa çağırma yumurtası", "item.minecraft.bee_spawn_egg": "<PERSON>rı çağırma yumurtası", "item.minecraft.beef": "Çiy İnək <PERSON>", "item.minecraft.beetroot": "Çuğundur", "item.minecraft.beetroot_seeds": "Çuğundur Toxumu", "item.minecraft.beetroot_soup": "Çuğundur Şorbası", "item.minecraft.birch_boat": "Ağcaqayı<PERSON>", "item.minecraft.birch_chest_boat": "Sandıqlı Ağcaqayın Qayıq", "item.minecraft.black_bundle": "qara kəsə", "item.minecraft.black_dye": "Qara Boya", "item.minecraft.black_harness": "Qara <PERSON>ə<PERSON>ər", "item.minecraft.blade_pottery_shard": "Bıçaqlı dulusçuluq parçası", "item.minecraft.blade_pottery_sherd": "Bıçaqlı dulusçuluq parçası", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "İfrit ç<PERSON><PERSON><PERSON>rma yumurtası", "item.minecraft.blue_bundle": "<PERSON><PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Bataq Çağırma Yumurtası", "item.minecraft.bolt_armor_trim_smithing_template": "Dəmirçilik Şablonu", "item.minecraft.bolt_armor_trim_smithing_template.new": "İld<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bone": "Sümük", "item.minecraft.bone_meal": "Sümük Tozu", "item.minecraft.book": "<PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "Yay", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "Çörək", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "İfrit ç<PERSON><PERSON><PERSON>rma yumurtası", "item.minecraft.brewer_pottery_shard": "<PERSON>və qabı qabığı", "item.minecraft.brewer_pottery_sherd": "Pivə Saxsı Parçası", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON>", "item.minecraft.brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_dye": "Qəh<PERSON><PERSON>yi Boya", "item.minecraft.brown_egg": "<PERSON><PERSON>h<PERSON><PERSON><PERSON> Yumurta", "item.minecraft.brown_harness": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> Yəhər", "item.minecraft.brush": "Fırça", "item.minecraft.bucket": "Vedrə", "item.minecraft.bundle": "Boğça", "item.minecraft.bundle.empty": "Boş", "item.minecraft.bundle.empty.description": "Qarış<PERSON><PERSON> ə<PERSON><PERSON><PERSON><PERSON> bir yığım daşıya bilər", "item.minecraft.bundle.full": "Do<PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Dulusçuluq parçasını yandırın", "item.minecraft.burn_pottery_sherd": "Saxsı qabı yandırın", "item.minecraft.camel_spawn_egg": "Dəvə Kürü Yumu<PERSON>sı", "item.minecraft.carrot": "Yerkökü", "item.minecraft.carrot_on_a_stick": "Yerkökü Tilovu", "item.minecraft.cat_spawn_egg": "Pişik çağırma yumurtası", "item.minecraft.cauldron": "Qazan", "item.minecraft.cave_spider_spawn_egg": "Mağara hörümcəyi çağırma yumurtası", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "Albalı qayığı", "item.minecraft.cherry_chest_boat": "Sinə ilə albalı qayıq", "item.minecraft.chest_minecart": "Sandıqlı Vaqon", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON><PERSON> ç<PERSON><PERSON>rma yumurtası", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "<PERSON>", "item.minecraft.clock": "Saat", "item.minecraft.coal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "Treska Vedrəsi", "item.minecraft.cod_spawn_egg": "Treska çağırma yumurtası", "item.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.compass": "Kompas", "item.minecraft.cooked_beef": "Bifşteks", "item.minecraft.cooked_chicken": "Bişmiş <PERSON>", "item.minecraft.cooked_cod": "Bişmiş Treska", "item.minecraft.cooked_mutton": "Bişmiş Qoyun Əti", "item.minecraft.cooked_porkchop": "Bişmiş Donuz Əti", "item.minecraft.cooked_rabbit": "Bişmiş <PERSON>", "item.minecraft.cooked_salmon": "Bişmiş Salmon", "item.minecraft.cookie": "Kökə", "item.minecraft.copper_ingot": "<PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "İnək çağırma yumurtası", "item.minecraft.creaking_spawn_egg": "Cırıldayan Çağırma Yumurtası", "item.minecraft.creeper_banner_pattern": "Banner şablonu", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.new": "Creeper Şarj <PERSON>i", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON> ç<PERSON>ğırma yumurtası", "item.minecraft.crossbow": "Arbalet", "item.minecraft.crossbow.projectile": "Avadanlıq:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Proyektil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dulusçulu<PERSON> parçası", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>ulu<PERSON> qabı", "item.minecraft.dark_oak_boat": "Qara Palıd <PERSON>", "item.minecraft.dark_oak_chest_boat": "Sandıqlı Qara Palıd Qayıq", "item.minecraft.debug_stick": "<PERSON><PERSON><PERSON>", "item.minecraft.debug_stick.empty": "%s xüsusiyyətsizdir", "item.minecraft.debug_stick.select": "\"%s\" (%s) seçilib", "item.minecraft.debug_stick.update": "\"%%n\" üçün \"%%n\"\n", "item.minecraft.diamond": "Almaz", "item.minecraft.diamond_axe": "Almaz Balta", "item.minecraft.diamond_boots": "Almaz çəkmə", "item.minecraft.diamond_chestplate": "<PERSON>z <PERSON>", "item.minecraft.diamond_helmet": "Almaz <PERSON>", "item.minecraft.diamond_hoe": "Almaz Kərki", "item.minecraft.diamond_horse_armor": "Almaz At Zirehi", "item.minecraft.diamond_leggings": "<PERSON>z <PERSON>al<PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON>", "item.minecraft.diamond_shovel": "Almaz <PERSON>ü<PERSON>k", "item.minecraft.diamond_sword": "<PERSON>z <PERSON>", "item.minecraft.disc_fragment_5": "Disk Fraqmenti", "item.minecraft.disc_fragment_5.desc": "Musiqi Diski - 5", "item.minecraft.dolphin_spawn_egg": "Delfin çağırma yumurtası", "item.minecraft.donkey_spawn_egg": "Uzunqulaq çağırma yumurtası", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Boğulmuş çağırma yumurtası", "item.minecraft.dune_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.echo_shard": "Echo Shard", "item.minecraft.egg": "Yumurta", "item.minecraft.elder_guardian_spawn_egg": "Yaşlı mühafizəçi çağırma yumurtası", "item.minecraft.elytra": "Qanadüst<PERSON>", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> qı<PERSON>ıl <PERSON>", "item.minecraft.end_crystal": "<PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Ender <PERSON> Kü<PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Ender<PERSON> ça<PERSON><PERSON>rma yumurt<PERSON>ı", "item.minecraft.endermite_spawn_egg": "Endermit <PERSON><PERSON><PERSON><PERSON><PERSON> yum<PERSON>ı", "item.minecraft.evoker_spawn_egg": "Oyadıcı çağırma yumurtası", "item.minecraft.experience_bottle": "Təcrübə Eliksiri", "item.minecraft.explorer_pottery_shard": "Kəşfiyyatçı dulusçuluq parçası", "item.minecraft.explorer_pottery_sherd": "Kəşfiyyatçı dulusçuluq qabı", "item.minecraft.eye_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.feather": "Tük", "item.minecraft.fermented_spider_eye": "Mayalı Hörümcək Gözü", "item.minecraft.field_masoned_banner_pattern": "Kərpic Bayraq Naxışı", "item.minecraft.filled_map": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON>ov Top<PERSON>", "item.minecraft.firework_rocket": "Fişəng Raketi", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON><PERSON>ş Müddəti:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "<PERSON><PERSON><PERSON>ng <PERSON>uz<PERSON>", "item.minecraft.firework_star.black": "Qara", "item.minecraft.firework_star.blue": "G<PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Keçiş Rəngi", "item.minecraft.firework_star.flicker": "Parıltı", "item.minecraft.firework_star.gray": "Boz", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Açıq <PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON>q <PERSON>", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Narıncı", "item.minecraft.firework_star.pink": "Çəhrayı", "item.minecraft.firework_star.purple": "Bənövşəyi", "item.minecraft.firework_star.red": "Qırmızı", "item.minecraft.firework_star.shape": "Bilinməyən Şə<PERSON>l", "item.minecraft.firework_star.shape.burst": "Partlama", "item.minecraft.firework_star.shape.creeper": "Creeper Şəkilli", "item.minecraft.firework_star.shape.large_ball": "Böyük top", "item.minecraft.firework_star.shape.small_ball": "Kiçik Top", "item.minecraft.firework_star.shape.star": "Ulduz Şəkilli", "item.minecraft.firework_star.trail": "İz", "item.minecraft.firework_star.white": "Ağ", "item.minecraft.firework_star.yellow": "Sarı", "item.minecraft.fishing_rod": "Tilov", "item.minecraft.flint": "Çaxmax Daşı", "item.minecraft.flint_and_steel": "Çaxmax Daşı və Polad", "item.minecraft.flow_armor_trim_smithing_template": "Dəmirçilik Şablonu", "item.minecraft.flow_armor_trim_smithing_template.new": "Axış Zireh Modeli", "item.minecraft.flow_banner_pattern": "Bayraq Naxışı", "item.minecraft.flow_banner_pattern.desc": "Axış", "item.minecraft.flow_banner_pattern.new": "Axış Bayraq Naxışı", "item.minecraft.flow_pottery_sherd": "Axış Dulusçuluq Parçası", "item.minecraft.flower_banner_pattern": "Bayraq Modeli", "item.minecraft.flower_banner_pattern.desc": "Çiçək şə<PERSON>", "item.minecraft.flower_banner_pattern.new": "Gül Bayraq Naxışı", "item.minecraft.flower_pot": "Saxsı", "item.minecraft.fox_spawn_egg": "Tülkü çağırma yumurtası", "item.minecraft.friend_pottery_shard": "Dost dulusçuluq parçası", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.frog_spawn_egg": "Qurbağa Çağırma Yumurtası", "item.minecraft.furnace_minecart": "Sobalı Vaqon", "item.minecraft.ghast_spawn_egg": "Qast çağırma yumurtası", "item.minecraft.ghast_tear": "Ghast Gözyaşı", "item.minecraft.glass_bottle": "Şüşə Butulka", "item.minecraft.glistering_melon_slice": "Pa<PERSON><PERSON><PERSON>yan qapız dilimi", "item.minecraft.globe_banner_pattern": "Bayraq Modeli", "item.minecraft.globe_banner_pattern.desc": "Kürə", "item.minecraft.globe_banner_pattern.new": "<PERSON>r <PERSON>ü<PERSON>ə<PERSON> Bayraq Naxışı", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON> giləmeyvələr", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON> mürəkkə<PERSON> kisəsi", "item.minecraft.glow_item_frame": "Parıldayan əşya çə<PERSON>ə<PERSON>", "item.minecraft.glow_squid_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> osminoq spaun yumurtası", "item.minecraft.glowstone_dust": "İşıq <PERSON>zu", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON><PERSON> spaun yumurtası", "item.minecraft.gold_ingot": "Qız<PERSON>l Külçə", "item.minecraft.gold_nugget": "Qızıl Parçası", "item.minecraft.golden_apple": "Qız<PERSON>l Alma", "item.minecraft.golden_axe": "Qızıl Balta", "item.minecraft.golden_boots": "Qı<PERSON>ıl çəkmə", "item.minecraft.golden_carrot": "Qızıl Yerkökü", "item.minecraft.golden_chestplate": "Qızıl Sinəlik", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>l Dəbilqə", "item.minecraft.golden_hoe": "Qızıl Kərki", "item.minecraft.golden_horse_armor": "Qızıl at zirehi", "item.minecraft.golden_leggings": "Qızıl şalvar", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "Qızıl Kürək", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON>z <PERSON>", "item.minecraft.green_bundle": "Ya<PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Mühafizəçi çağırma yumurtası", "item.minecraft.gunpowder": "Barıt", "item.minecraft.guster_banner_pattern": "Bayraq Naxışı", "item.minecraft.guster_banner_pattern.desc": "Çovğun", "item.minecraft.guster_banner_pattern.new": "Çovğun Bayraq Naxışı", "item.minecraft.guster_pottery_sherd": "Çovğun Dulusçuluq Parçası", "item.minecraft.happy_ghast_spawn_egg": "Xoşbəxt Qast Çağırma Yumurtası", "item.minecraft.harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Də<PERSON>zin ürəyi", "item.minecraft.heart_pottery_shard": "Ürək dulusçuluq parçası", "item.minecraft.heart_pottery_sherd": "Ürək dulusçuluq qabı", "item.minecraft.heartbreak_pottery_shard": "Ürək qıran dulusçuluq parçası", "item.minecraft.heartbreak_pottery_sherd": "Ürək qıran dulusçuluq", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON> çağırma yumurtası", "item.minecraft.honey_bottle": "Bal butul<PERSON>ı", "item.minecraft.honeycomb": "<PERSON>l pətəyi", "item.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.horse_spawn_egg": "At çağırma yumurtası", "item.minecraft.host_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.host_armor_trim_smithing_template.new": "<PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Howl Saxsı Parçası", "item.minecraft.howl_pottery_sherd": "Howl Saxsı Sherd", "item.minecraft.husk_spawn_egg": "Hask çağırma yumurtası", "item.minecraft.ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Dəmir Golem Kürü Yumurtası", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Dəmir At Zirehi", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Cəngəllik Ağacı Qayıq", "item.minecraft.jungle_chest_boat": "Sandıqlı Cəngəllik Ağacı Qayıq", "item.minecraft.knowledge_book": "Bilik kitabı", "item.minecraft.lapis_lazuli": "<PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON>", "item.minecraft.lead": "Qayış", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Dəri at zirehi", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "<PERSON>çıq <PERSON>", "item.minecraft.light_blue_dye": "Açıq <PERSON>", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON>q <PERSON>", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.awkward": "Daimi Qəribə Eliksir", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON> Eliks<PERSON>", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON>ti Eliksiri", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.healing": "Daimi <PERSON>hiyyət Eliksiri", "item.minecraft.lingering_potion.effect.invisibility": "Daimi Görünməzlik Eliksiri", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.levitation": "Daimi Yüksəlmə Eliksiri", "item.minecraft.lingering_potion.effect.luck": "Daimi Şans Eliksiri", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "Daimi Gecə Görüşü Eliksiri", "item.minecraft.lingering_potion.effect.oozing": "Daimi Sızma Iksiri", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "Daimi Təzələnmə Eliksiri", "item.minecraft.lingering_potion.effect.slow_falling": "Daimi Yavaş Eniş Eliksiri", "item.minecraft.lingering_potion.effect.slowness": "Daimi <PERSON>lıq <PERSON>", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON>üvv<PERSON>t Eliksiri", "item.minecraft.lingering_potion.effect.swiftness": "Daimi Çeviklik Eliksiri", "item.minecraft.lingering_potion.effect.thick": "Daimi Sıx <PERSON>ir", "item.minecraft.lingering_potion.effect.turtle_master": "Daimi Tısbağa Ustası Eliksiri", "item.minecraft.lingering_potion.effect.water": "Daimi Su <PERSON>ulkas<PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "Daimi Suda Nəfəs Alma Eliksiri", "item.minecraft.lingering_potion.effect.weakness": "Daimi Zəiflik Eliksiri", "item.minecraft.lingering_potion.effect.weaving": "Qalıcı Örmə Eliksiri", "item.minecraft.lingering_potion.effect.wind_charged": "Qalıcı Rüzgar Dolu Eliksiri", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> yum<PERSON>ı", "item.minecraft.lodestone_compass": "Maq<PERSON>tlə<PERSON><PERSON>ş kompas", "item.minecraft.mace": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_dye": "Ma<PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magma Kremi", "item.minecraft.magma_cube_spawn_egg": "<PERSON><PERSON><PERSON> kubu ça<PERSON>ırma yumurtası", "item.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.map": "<PERSON>ş Xəritə", "item.minecraft.melon_seeds": "Qarpız Toxumu", "item.minecraft.melon_slice": "Qarpız <PERSON>limi", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Mədənçi Saxsı Parçası", "item.minecraft.miner_pottery_sherd": "Mədənçi dulusçuluq qabığı", "item.minecraft.mojang_banner_pattern": "Bayraq Modeli", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "Şey Banner Naxışı", "item.minecraft.mooshroom_spawn_egg": "Muuşruum çağırma yumurtası", "item.minecraft.mourner_pottery_shard": "Yaslı Saxsı Parçası", "item.minecraft.mourner_pottery_sherd": "Yaslı Saxsı Sherd", "item.minecraft.mule_spawn_egg": "Qatır çağırma yumurtası", "item.minecraft.mushroom_stew": "Göbələk Pörtləməsi", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Musiqi valı", "item.minecraft.music_disc_creator.desc": "<PERSON> – C<PERSON>", "item.minecraft.music_disc_creator_music_box": "Musiqi valı", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (Mahnı Qutusu)", "item.minecraft.music_disc_far": "Musiqi valı", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_lava_chicken.desc": "Lavalı Toyuq", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON> diski", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Musiqi valı", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Musiqi valı", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON>", "item.minecraft.music_disc_precipice": "Musiqi valı", "item.minecraft.music_disc_precipice.desc": "<PERSON> – Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON><PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_tears.desc": "<PERSON> - <PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Ad Etiketi", "item.minecraft.nautilus_shell": "Nautilus qabı<PERSON>ı", "item.minecraft.nether_brick": "<PERSON><PERSON>", "item.minecraft.nether_star": "<PERSON><PERSON>", "item.minecraft.nether_wart": "<PERSON><PERSON>", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON> balta", "item.minecraft.netherite_boots": "Nezerit çəkmə", "item.minecraft.netherite_chestplate": "Nezerit zireh", "item.minecraft.netherite_helmet": "Nezerit dəbilqə", "item.minecraft.netherite_hoe": "Nezerit toxa", "item.minecraft.netherite_ingot": "Nezerit külçəsi", "item.minecraft.netherite_leggings": "Nezerit <PERSON>alvar", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON><PERSON>az<PERSON>", "item.minecraft.netherite_scrap": "Nezerit parçası", "item.minecraft.netherite_shovel": "Nezerit kürək", "item.minecraft.netherite_sword": "Nezerit qılınc", "item.minecraft.netherite_upgrade_smithing_template": "Dəmirçi Şablonu", "item.minecraft.netherite_upgrade_smithing_template.new": "Kürdəmit Təkmilləşdirmə", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "Sandıqlı Palıd <PERSON>", "item.minecraft.ocelot_spawn_egg": "Oselot çağırma yumurtası", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Narı<PERSON>ı kisə", "item.minecraft.orange_dye": "Narıncı Boya", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "<PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON> palıd qayıq", "item.minecraft.pale_oak_chest_boat": "<PERSON><PERSON>un palıd sandı<PERSON>ı qayıq", "item.minecraft.panda_spawn_egg": "Panda çağırma yumurtası", "item.minecraft.paper": "Kağız", "item.minecraft.parrot_spawn_egg": "Tutuquşu çağırma yumurtası", "item.minecraft.phantom_membrane": "Fan<PERSON> membranı", "item.minecraft.phantom_spawn_egg": "Fantom çağırma yumurtası", "item.minecraft.pig_spawn_egg": "Donuz çağırma yumurtası", "item.minecraft.piglin_banner_pattern": "Banner şablonu", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON> burnu", "item.minecraft.piglin_banner_pattern.new": "“Sifət” bayraq naxışı", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> çağırma yumurtası", "item.minecraft.piglin_spawn_egg": "Piqlin çağırma yumurtası", "item.minecraft.pillager_spawn_egg": "Quldur çağırma yumurtası", "item.minecraft.pink_bundle": "Çəhra<PERSON>ı kisə", "item.minecraft.pink_dye": "Çəhrayı Boya", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Pitcher Under", "item.minecraft.plenty_pottery_shard": "Bol dulusçuluq <PERSON>ı", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON> <PERSON><PERSON><PERSON> qabı", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.polar_bear_spawn_egg": "Qütb ayısı çağırma yumurtası", "item.minecraft.popped_chorus_fruit": "Partlamış Nəqarət Meyvəsi", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON>", "item.minecraft.potion": "<PERSON>ks<PERSON>", "item.minecraft.potion.effect.awkward": "Qəribə Eliksir", "item.minecraft.potion.effect.empty": "Hazırlanmayan Eliksir", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON> mü<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.harming": "<PERSON>ə<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.healing": "S<PERSON><PERSON><PERSON><PERSON>ət Eliksiri", "item.minecraft.potion.effect.infested": "Təzələnmə Eliksiri", "item.minecraft.potion.effect.invisibility": "Görünməzlik Eliksiri", "item.minecraft.potion.effect.leaping": "Tullanma Eliksiri", "item.minecraft.potion.effect.levitation": "Yüksəlmə Eliksiri", "item.minecraft.potion.effect.luck": "Şans Eliksiri", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.potion.effect.night_vision": "Gecə Görüşü Eliksiri", "item.minecraft.potion.effect.oozing": "Sızma Eliksiri", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "Təzələnmə Eliksiri", "item.minecraft.potion.effect.slow_falling": "Yavaş Eniş Eliksiri", "item.minecraft.potion.effect.slowness": "Yavaşlıq <PERSON>", "item.minecraft.potion.effect.strength": "Qüvvət Eliksiri", "item.minecraft.potion.effect.swiftness": "Çeviklik Eliksiri", "item.minecraft.potion.effect.thick": "Sıx Eliksir", "item.minecraft.potion.effect.turtle_master": "Tısbağa Ustası Eliksiri", "item.minecraft.potion.effect.water": "Su Butulkası", "item.minecraft.potion.effect.water_breathing": "Suda Nəfəs Alma Eliksiri", "item.minecraft.potion.effect.weakness": "Zəiflik Eliksiri", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "Oxatan du<PERSON>ı", "item.minecraft.pottery_shard_arms_up": "Dulusçuluq parçasını silahlandırın", "item.minecraft.pottery_shard_prize": "Mükafat dulusçuluq parçası", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON><PERSON><PERSON> qabığı", "item.minecraft.powder_snow_bucket": "<PERSON><PERSON><PERSON> qar vedrəsi", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "Mükafat dulusçuluq parçası", "item.minecraft.prize_pottery_sherd": "Mükafat dulusçuluq qabı", "item.minecraft.pufferfish": "Kirpi Balığı", "item.minecraft.pufferfish_bucket": "Kirpi Balığı Vedrəsi", "item.minecraft.pufferfish_spawn_egg": "Kirpi balığı çağırma yumurtası", "item.minecraft.pumpkin_pie": "Balqabaq piroqu", "item.minecraft.pumpkin_seeds": "Balqabaq Toxumu", "item.minecraft.purple_bundle": "Bənövşəyi çanta", "item.minecraft.purple_dye": "Bənövşəyi Boya", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON>her <PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "Dovşan A<PERSON>ğı", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Dovşan çağırma yumurtası", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "Dağıdıcı çağırma yumurtası", "item.minecraft.raw_copper": "<PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON>", "item.minecraft.recovery_compass": "Bərpa Kompası", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "Qırmızı Boya", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Qızıl Daş Tozu", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.rib_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "Çürük Ət", "item.minecraft.saddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "<PERSON>", "item.minecraft.salmon_spawn_egg": "Qızılbal<PERSON>q çağırma yumurtası", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "Saxsı qab qırıqları", "item.minecraft.sheaf_pottery_sherd": "Saxsı qab", "item.minecraft.shears": "Qayçı", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON>un çağırma yumurtası", "item.minecraft.shelter_pottery_shard": "Sığınacaq Saxsı Parçası", "item.minecraft.shelter_pottery_sherd": "Sığınacaq dulusçuluq qabığı", "item.minecraft.shield": "Qalxan", "item.minecraft.shield.black": "Qara Qalxan", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Qalxan", "item.minecraft.shield.cyan": "<PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON>al<PERSON>n", "item.minecraft.shield.light_blue": "Açıq Göy Qalxan", "item.minecraft.shield.light_gray": "A<PERSON>ıq <PERSON>z Qalxan", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON> Qalxan", "item.minecraft.shield.orange": "Narıncı Qalxan", "item.minecraft.shield.pink": "Çəhrayı Qalxan", "item.minecraft.shield.purple": "Bənövşəyi Qalxan", "item.minecraft.shield.red": "Qırmızı Qalxan", "item.minecraft.shield.white": "<PERSON><PERSON>", "item.minecraft.shield.yellow": "Sarı Qalxan", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>er ça<PERSON><PERSON>rma yumurtası", "item.minecraft.sign": "Lövhə", "item.minecraft.silence_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "Gümüş Böcəyi Peyda Yumurtası", "item.minecraft.skeleton_horse_spawn_egg": "Skelet at çağırma yumurtası", "item.minecraft.skeleton_spawn_egg": "Skelet çağırma yumurtası", "item.minecraft.skull_banner_pattern": "Banner şablonu", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>ə<PERSON>", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> qabığı", "item.minecraft.skull_pottery_sherd": "<PERSON>ə<PERSON><PERSON> dulusçuluq qabı", "item.minecraft.slime_ball": "<PERSON><PERSON><PERSON>ıq <PERSON>", "item.minecraft.slime_spawn_egg": "Pal<PERSON><PERSON>q çağırma yumurtası", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON><PERSON><PERSON> olunur:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Külçə və ya kristal əlavə edin", "item.minecraft.smithing_template.armor_trim.applies_to": "Zireh", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Bir parça zireh əlavə edin", "item.minecraft.smithing_template.armor_trim.ingredients": "K<PERSON>lç<PERSON><PERSON><PERSON><PERSON> və Kristallar", "item.minecraft.smithing_template.ingredients": "Tərkibi:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ülç<PERSON> əlavə edin", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON>z <PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON>, silah və ya alət əlavə edin", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.upgrade": "Təkmilləşdir: ", "item.minecraft.sniffer_spawn_egg": "Sniffer Kürü <PERSON>", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "Snort Saxsı Sherd", "item.minecraft.snout_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Snow Golem Kürü <PERSON>mu<PERSON>sı", "item.minecraft.snowball": "<PERSON><PERSON>", "item.minecraft.spectral_arrow": "Spektral Ox", "item.minecraft.spider_eye": "Hörümçək Gözü", "item.minecraft.spider_spawn_egg": "Hörümçək çağırma yumurtası", "item.minecraft.spire_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "At<PERSON>la Bilən Eliksir", "item.minecraft.splash_potion.effect.awkward": "At<PERSON>la Bilən Qəribə Eliksir", "item.minecraft.splash_potion.effect.empty": "At<PERSON>la Bilən Hazırlanmayan Eliksir", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON> Bilən <PERSON>ov Müqaviməti Eliksiri", "item.minecraft.splash_potion.effect.harming": "At<PERSON>la Bilən Xəsarət Eliksiri", "item.minecraft.splash_potion.effect.healing": "At<PERSON>la Bilən Səhiyyət Eliksiri", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "Atıla Bilən Görünməzlik Eliksiri", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON>la Bilən Tullanma Eliksiri", "item.minecraft.splash_potion.effect.levitation": "Atıla Bilən Yüksəlmə Eliksiri", "item.minecraft.splash_potion.effect.luck": "Atıla Bilən Şans Eliksiri", "item.minecraft.splash_potion.effect.mundane": "At<PERSON>la Bilən Adi Eliksir", "item.minecraft.splash_potion.effect.night_vision": "Atıla Bilən Gecə Görüşü Eliksiri", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON> Bilən <PERSON>ə<PERSON>", "item.minecraft.splash_potion.effect.regeneration": "At<PERSON>la Bilən Təzələnmə Eliksiri", "item.minecraft.splash_potion.effect.slow_falling": "Atıla Bilən Yavaş Eniş Eliksiri", "item.minecraft.splash_potion.effect.slowness": "Atıla Bilən Yavaşlıq Eliksiri", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON>la Bilən Qüvvət Eliksiri", "item.minecraft.splash_potion.effect.swiftness": "Atıla Bilən Çeviklik Eliksiri", "item.minecraft.splash_potion.effect.thick": "Atıla Bilən Sıx Eliksir", "item.minecraft.splash_potion.effect.turtle_master": "Atıla Bilən Tısbağa Ustası Eliksiri", "item.minecraft.splash_potion.effect.water": "Atıla Bilən Su Butulkası", "item.minecraft.splash_potion.effect.water_breathing": "Atıla Bilən Suda Nəfəs Alma Eliksiri", "item.minecraft.splash_potion.effect.weakness": "Atıla Bilən Zəiflik Eliksiri", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "<PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "Sandı<PERSON><PERSON>ı <PERSON>din <PERSON>ı<PERSON>", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON>r çağırma yumurtası", "item.minecraft.stick": "Çubuq", "item.minecraft.stone_axe": "Daş Balta", "item.minecraft.stone_hoe": "<PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON>ş Kürək", "item.minecraft.stone_sword": "Daş Qılınc", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON> çağırma yumurtası", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON><PERSON> ç<PERSON>ğırma yumurtası", "item.minecraft.string": "İp", "item.minecraft.sugar": "Şəkər", "item.minecraft.suspicious_stew": "Şübhəli pörtləmə", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON> giləme<PERSON>və", "item.minecraft.tadpole_bucket": "Çömçəquyruq Vedrə<PERSON>", "item.minecraft.tadpole_spawn_egg": "Çömçəquyruq Çağırma Yumurtası", "item.minecraft.tide_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "<PERSON>ks<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON>ks<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.empty": "Hazırlanmayan Eliksirli Ox", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON> müqa<PERSON>ti oxu", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> O<PERSON>u", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON>ət Oxu", "item.minecraft.tipped_arrow.effect.infested": "Təzələnmə Oxu", "item.minecraft.tipped_arrow.effect.invisibility": "Görünməzlik Oxu", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON> O<PERSON>", "item.minecraft.tipped_arrow.effect.levitation": "Yüksəlmə Oxu", "item.minecraft.tipped_arrow.effect.luck": "Şans Oxu", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON>ks<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "Gecə Görüşü Oxu", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "Təzələnmə Oxu", "item.minecraft.tipped_arrow.effect.slow_falling": "Yavaş Eniş Oxu", "item.minecraft.tipped_arrow.effect.slowness": "Yavaşlıq O<PERSON>", "item.minecraft.tipped_arrow.effect.strength": "Qüvvət Oxu", "item.minecraft.tipped_arrow.effect.swiftness": "Çeviklik Oxu", "item.minecraft.tipped_arrow.effect.thick": "<PERSON>ks<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "Tısbağa ustası oxu", "item.minecraft.tipped_arrow.effect.water": "Su Oxu", "item.minecraft.tipped_arrow.effect.water_breathing": "Suda Nəfəs Alma Oxu", "item.minecraft.tipped_arrow.effect.weakness": "Zəiflik Oxu", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "TNT-li <PERSON>aqon", "item.minecraft.torchflower_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> çiçəyi toxumları", "item.minecraft.totem_of_undying": "Ö<PERSON>əmə Totemi", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON> çağırma yumurtası", "item.minecraft.trial_key": "Sınaq A<PERSON>rı", "item.minecraft.trident": "Üçbaşlı əsa", "item.minecraft.tropical_fish": "Tropik Balıq", "item.minecraft.tropical_fish_bucket": "T<PERSON>ik balıq vedrəsi", "item.minecraft.tropical_fish_spawn_egg": "Tropik balıq çağırma yumurtası", "item.minecraft.turtle_helmet": "Tısbağa qabığı", "item.minecraft.turtle_scute": "Tısbağa Pulcuğu", "item.minecraft.turtle_spawn_egg": "Tısbağa çağırma yumurtası", "item.minecraft.vex_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.vex_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Z<PERSON>", "item.minecraft.vex_spawn_egg": "Veks ç<PERSON>ğırma yumurtası", "item.minecraft.villager_spawn_egg": "Kəndli çağırma yumurtası", "item.minecraft.vindicator_spawn_egg": "İntiqamçı çağırma yumurtası", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> tacir ça<PERSON><PERSON>rma yumurtası", "item.minecraft.ward_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Gözətçi Çağırma Yumurtası", "item.minecraft.warped_fungus_on_a_stick": "Çubuqlu əyri gö<PERSON>ələk", "item.minecraft.water_bucket": "<PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wheat_seeds": "B<PERSON>ğda Toxumu", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "<PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "Dəmirçi Şablonu", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Wind Charge", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> çağırma yumurtası", "item.minecraft.wither_skeleton_spawn_egg": "Vizer skeleti çağırma yumurtası", "item.minecraft.wither_spawn_egg": "Solmuş Kürü Yumurtası", "item.minecraft.wolf_armor": "<PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Canavar çağırma yumurtası", "item.minecraft.wooden_axe": "Taxta Balta", "item.minecraft.wooden_hoe": "<PERSON>ta <PERSON>", "item.minecraft.wooden_pickaxe": "Tax<PERSON>az<PERSON>", "item.minecraft.wooden_shovel": "Taxta Kürək", "item.minecraft.wooden_sword": "Taxta Qılınc", "item.minecraft.writable_book": "Kitab və Tük", "item.minecraft.written_book": "Yazılı Kitab", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "Sarı Boya", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Z<PERSON>qlin çağırma yumurtası", "item.minecraft.zombie_horse_spawn_egg": "Zombi at çağırma yumurtası", "item.minecraft.zombie_spawn_egg": "Zombi çağırma yumurtası", "item.minecraft.zombie_villager_spawn_egg": "Zombi kəndli çağırma yumurtası", "item.minecraft.zombified_piglin_spawn_egg": "Zombiləşmiş piqlin çağırma yumurtası", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "Gövdədə ikən:", "item.modifiers.feet": "Ayaqda ikən:", "item.modifiers.hand": "When held:", "item.modifiers.head": "Başda ikən:", "item.modifiers.legs": "Ayaqda ikən:", "item.modifiers.mainhand": "Ana əldə ikən:", "item.modifiers.offhand": "İşlədilməyən əldə ikən:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s etiket", "item.op_block_warning.line1": "Xəbərdarlıq", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "Qırılmaz", "itemGroup.buildingBlocks": "İnşa Blokları", "itemGroup.coloredBlocks": "Rə<PERSON>li Bloklar", "itemGroup.combat": "Döyüş", "itemGroup.consumables": "İstehlak materialları", "itemGroup.crafting": "Sənətkarlıq", "itemGroup.foodAndDrink": "Yimay & içkilər", "itemGroup.functional": "Funksional bloklar", "itemGroup.hotbar": "Saxlanılmış alətlər paneli", "itemGroup.ingredients": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.inventory": "Həyatda Qalma İnventarı", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "itemGroup.op": "Operator Utilities", "itemGroup.redstone": "Qırmızıdaş bloklar", "itemGroup.search": "Əşya Axtar", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "<PERSON><PERSON><PERSON><PERSON>r və Utilitlər", "item_modifier.unknown": "Naməlum əşya dəyişkəni: %s", "jigsaw_block.final_state": "Buna çevir:", "jigsaw_block.generate": "<PERSON><PERSON>", "jigsaw_block.joint.aligned": "Düzülmüş", "jigsaw_block.joint.rollable": "Fırladıla bilən", "jigsaw_block.joint_label": "Qoşulma növü:", "jigsaw_block.keep_jigsaws": "Pazlları saxla", "jigsaw_block.levels": "Səviyyələr: %s", "jigsaw_block.name": "Ad:", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "<PERSON>ə<PERSON><PERSON><PERSON> hovu<PERSON>:", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "Hədə<PERSON> adı:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.attack": "Hücum Etmək/Qazmaq", "key.back": "Geriyə Getmək", "key.categories.creative": "Yarad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.gameplay": "Oynayış", "key.categories.inventory": "İnventar", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON>t", "key.categories.multiplayer": "Çoxoyunçulu", "key.categories.ui": "Oyun İnterfeysi", "key.chat": "<PERSON><PERSON><PERSON>b<PERSON><PERSON> Açmaq", "key.command": "<PERSON><PERSON><PERSON>", "key.drop": "Seçilən Əşyanı Buraxmaq", "key.forward": "<PERSON><PERSON><PERSON><PERSON>", "key.fullscreen": "Tam Ekrana <PERSON>ək", "key.hotbar.1": "Alət Paneli Slotu 1", "key.hotbar.2": "Alət Paneli Slotu 2", "key.hotbar.3": "Alət Paneli Slotu 3", "key.hotbar.4": "Alət Paneli Slotu 4", "key.hotbar.5": "Alət Paneli Slotu 5", "key.hotbar.6": "Alət Paneli Slotu 6", "key.hotbar.7": "Alət Paneli Slotu 7", "key.hotbar.8": "Alət Paneli Slotu 8", "key.hotbar.9": "Alət Paneli Slotu 9", "key.inventory": "İnventar'ı Açmaq/Bağlamaq", "key.jump": "Tullanmaq", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Sil", "key.keyboard.down": "Aşağı Ox", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F34", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Klaviatura 0", "key.keyboard.keypad.1": "Klaviatura 1", "key.keyboard.keypad.2": "Klaviatura 2", "key.keyboard.keypad.3": "Klaviatura 3", "key.keyboard.keypad.4": "Klaviatura 4", "key.keyboard.keypad.5": "Klaviatura 5", "key.keyboard.keypad.6": "Klaviatura 6", "key.keyboard.keypad.7": "Klaviatura 7", "key.keyboard.keypad.8": "Klaviatura 8", "key.keyboard.keypad.9": "Klaviatura 9", "key.keyboard.keypad.add": "Klaviatura +", "key.keyboard.keypad.decimal": "Decimal Klaviatura", "key.keyboard.keypad.divide": "Klaviatura /", "key.keyboard.keypad.enter": "Klaviatura Giriş", "key.keyboard.keypad.equal": "Klaviatura =", "key.keyboard.keypad.multiply": "Klaviatura *", "key.keyboard.keypad.subtract": "Klaviatura -", "key.keyboard.left": "Sol Ox", "key.keyboard.left.alt": "Sol Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Sol Kontrol", "key.keyboard.left.shift": "Sol Shift", "key.keyboard.left.win": "Qazanmaq <PERSON>", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Ekran Çap", "key.keyboard.right": "<PERSON>ğ <PERSON>", "key.keyboard.right.alt": "Sağ Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Sağ Ko<PERSON>rol", "key.keyboard.right.shift": "Sağ Shift", "key.keyboard.right.win": "<PERSON><PERSON>ə<PERSON>ə", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Boşluq", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Bağlı deyil", "key.keyboard.up": "Yuxarı Ox", "key.keyboard.world.1": "1 Dünya", "key.keyboard.world.2": "2 Dünya", "key.left": "Sola Getmək", "key.loadToolbarActivator": "Aktivator İsti Yük Paneli", "key.mouse": "Düymə %1$s", "key.mouse.left": "<PERSON>", "key.mouse.middle": "<PERSON><PERSON>", "key.mouse.right": "Sağ <PERSON>", "key.pickItem": "Blok Seçmək", "key.playerlist": "Oyunçuları Siyahılamaq", "key.quickActions": "Quick Actions", "key.right": "<PERSON><PERSON><PERSON>", "key.saveToolbarActivator": "Aktivator Panelini Saxla", "key.screenshot": "Ekran Görünüşü<PERSON>ü <PERSON>", "key.smoothCamera": "Kinomatik Kameraya Keçmək", "key.sneak": "Əyilmək", "key.socialInteractions": "Sosial Əlaqələ<PERSON>ı", "key.spectatorOutlines": "Oyunçuları (İzləyiciləri) Vurğulamaq", "key.sprint": "Qaçmaq", "key.swapOffhand": "Elementi Offhand ilə dəyişdirin", "key.togglePerspective": "Perspektifi <PERSON>ə<PERSON>", "key.use": "Əşyadan İstifadə Etmək/Blok Qoymaq", "known_server_link.announcements": "Announcements", "known_server_link.community": "İcma", "known_server_link.community_guidelines": "İcma təlimatları", "known_server_link.feedback": "<PERSON><PERSON>qə", "known_server_link.forums": "Forumlar", "known_server_link.news": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.report_bug": "Serverdə bir səhv bildirin", "known_server_link.status": "Status", "known_server_link.support": "Dəstək", "known_server_link.website": "<PERSON><PERSON>t", "lanServer.otherPlayers": "<PERSON><PERSON><PERSON>r Oyunçular üçün Nizamlar", "lanServer.port": "Port nömrəsi", "lanServer.port.invalid": "Etibarlı port deyil.\n Redaktə qutusunu boş qoyun və ya 1024 və 65535 arasında bir nömrə daxil edin.", "lanServer.port.invalid.new": "Etibarlı port deyil.\n Redaktə qutusunu boş buraxın və ya %s və %s arasında rəqəm daxil edin.", "lanServer.port.unavailable": "Port mövcud deyil.\n Redaktə qutusunu boş qoyun və ya 1024 və 65535 arasında fərqli nömrə daxil edin.", "lanServer.port.unavailable.new": "Port mövcud deyil.\n Redaktə qutusunu boş buraxın və ya %s və %s arasında fərqli nömrə daxil edin.", "lanServer.scanning": "Lokal şəbəkəniz üzərindəki oyunlar axtarılır", "lanServer.start": "Lokal Şəbəkə Dünyasını Başlat", "lanServer.title": "Lokal Şəbəkə Dünyası", "language.code": "aze_AZ", "language.name": "Azərbaycanca", "language.region": "Azərbaycan", "lectern.take_book": "Kitabı götür", "loading.progress": "%s%%", "mco.account.privacy.info": "Gizlilik qanunları haqqında ətraflı məlumat əldə edin", "mco.account.privacy.info.button": "GDPR Haqqında Ətraflı məlumat", "mco.account.privacy.information": "<PERSON><PERSON><PERSON>, uşaqların onlayn məxfiliyinin qorunması qanununa (COPPA) və ümumi məlumatların qorunması qaydalarına (GDPR) riayət etmək də daxil olmaqla, uşaqları və onların məxfiliyini qorumağa kömək edən müəyyən prosedurları həyata keçirir.\n\nRealms hesabınıza daxil olmamışdan əvvəl valideyn razılığını almanız lazım ola bilər.", "mco.account.privacyinfo": "Mojang hü<PERSON> qanunu (COPPA) və Ümumi Məlumatların Mühafizəsi Qaydası (GDPR) üçün necə müraciət etmək üçün təlimatlar var.\n\nRealms hesabınıza daxil olmağınızdan əvvəl valideyn razılığını almalısınız.\n\nDaha yaşlı Minecraft hesabınız varsa, hesabınıza ehtiyacınız varmı.", "mco.account.update": "Hesabı yenilə", "mco.activity.noactivity": "Son %s gün ərzində fəaliyyət yoxdur", "mco.activity.title": "Oyunçu fəaliyyəti", "mco.backup.button.download": "<PERSON>n yeniyi endir", "mco.backup.button.reset": "Dünyayı sıfırla", "mco.backup.button.restore": "<PERSON><PERSON>", "mco.backup.button.upload": "<PERSON>ünya <PERSON>ü<PERSON>lə", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>ş<PERSON><PERSON>lər", "mco.backup.entry": "Yedəkləmə (%s)", "mco.backup.entry.description": "Açıqlama", "mco.backup.entry.enabledPack": "Aktiv Paket", "mco.backup.entry.gameDifficulty": "<PERSON>yun <PERSON>", "mco.backup.entry.gameMode": "<PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Oyun server versiyası", "mco.backup.entry.name": "Ad", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Şablon Adı", "mco.backup.entry.undefined": "Mü<PERSON><PERSON><PERSON><PERSON>n edilmə<PERSON>ş Dəyişiklik", "mco.backup.entry.uploaded": "Yükləndi", "mco.backup.entry.worldType": "Dünya Növü", "mco.backup.generate.world": "Dünya yarat", "mco.backup.info.title": "Son yedəklə<PERSON>ədən də<PERSON>ər", "mco.backup.narration": "%s-dən ehtiyat nüsxə", "mco.backup.nobackups": "Bu Realm'ın bu anda hər hansı bir ehtiyat nüsxəsi yoxdur.", "mco.backup.restoring": "Realm'ınız geri yüklə<PERSON>", "mco.backup.unknown": "NAMƏLUM", "mco.brokenworld.download": "Yüklə", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Lüt<PERSON><PERSON>n sıfırlayın ya da başqa bir dünya seçin.", "mco.brokenworld.message.line2": "Ayrıca dünyanı tək oyunçuluya endirməyi seçə bilərsən.", "mco.brokenworld.minigame.title": "Bu mini oyun artıq dəstə<PERSON>lənmir", "mco.brokenworld.nonowner.error": "Lütfən Realm sahibinin dünyanı sıfırlamasını gözləyin", "mco.brokenworld.nonowner.title": "<PERSON><PERSON><PERSON> ye<PERSON>l", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Sıfırla", "mco.brokenworld.title": "Mövcud dünyanız artıq də<PERSON>ə<PERSON>ə<PERSON>", "mco.client.incompatible.msg.line1": "Klientiniz Realms ilə uyğun deyil.", "mco.client.incompatible.msg.line2": "Lütfən Minecraft'ın ən son versiyasını istifadə edin.", "mco.client.incompatible.msg.line3": "Realms snapshot vers<PERSON>larıyla uyğun deyildir.", "mco.client.incompatible.title": "Klient Uyğun De<PERSON>l!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Reytinqin aşağı salınması", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Yeniləmə", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "<PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Oyunçu yayını müvəqqəti bağlıdır", "mco.configure.world.backup": "<PERSON><PERSON><PERSON> nü<PERSON>l<PERSON>", "mco.configure.world.buttons.activity": "Oyunçu fəaliyyəti", "mco.configure.world.buttons.close": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bağla", "mco.configure.world.buttons.delete": "Sil", "mco.configure.world.buttons.done": "Bitdi", "mco.configure.world.buttons.edit": "Parametrlər", "mco.configure.world.buttons.invite": "Oyunçu dəvət et", "mco.configure.world.buttons.moreoptions": "Daha çox seçimlər", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Səltən<PERSON>ti aç", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "Oyunçular", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Dünyanı sıfırla", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Sazlamalar", "mco.configure.world.buttons.subscription": "Abunəlik", "mco.configure.world.buttons.switchminigame": "Mini oyunu dəyi<PERSON>dir", "mco.configure.world.close.question.line1": "Realm'ın istifadə edilə bilməz vəziyyətə gələcək.", "mco.configure.world.close.question.line2": "Davam etmək istədiyinizə əminsiniz?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Realm bağlanılır...", "mco.configure.world.commandBlocks": "<PERSON><PERSON><PERSON>", "mco.configure.world.delete.button": "Realm'ı sil", "mco.configure.world.delete.question.line1": "Realm'ın qalıcı olaraq silinəcək", "mco.configure.world.delete.question.line2": "Davam etmək istədiyinizə əminsiniz?", "mco.configure.world.description": "Realm təsviri", "mco.configure.world.edit.slot.name": "Dünya adı", "mco.configure.world.edit.subscreen.adventuremap": "Hal hazırki xəritəniz bir macəra xəritəsi olduğu üçün bəzi nizamlar passiv edildi", "mco.configure.world.edit.subscreen.experience": "Hal hazırki xəritəniz bir təcrübə xəritəsi olduğu üçün bəzi nizamlar passiv edildi", "mco.configure.world.edit.subscreen.inspiration": "Hal hazırki xəritəniz bir ilham xəritəsi olduğu üçün bəzi nizamlar passiv edildi", "mco.configure.world.forceGameMode": "<PERSON>yun moduna məcbur et", "mco.configure.world.invite.narration": "%s yeni dəvətiniz var", "mco.configure.world.invite.profile.name": "Ad", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Dəvət edildi (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "İdarəçi", "mco.configure.world.invites.remove.tooltip": "Qaldır", "mco.configure.world.leave.question.line1": "Əgər bu <PERSON>'dan çı<PERSON> bir sonrakı dəvətdə geri qayıda biləcəksən", "mco.configure.world.leave.question.line2": "Davam etmək istədiyinizə əminsiniz?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "Yer", "mco.configure.world.minigame": "Cari: %s", "mco.configure.world.name": "Realm adı", "mco.configure.world.opening": "Realm açılır...", "mco.configure.world.players.error": "Daxil edilmiş oyunçu adı mövcud deyil", "mco.configure.world.players.inviting": "Oyun<PERSON>u dəvət olunur...", "mco.configure.world.players.title": "Oyunçular", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Realm'ın yenidən yaradılacak və hal hazırki Realm'ın qeyb olacaq", "mco.configure.world.reset.question.line2": "Davam etmək istədiyinizə əminsiniz?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "Bu Realm xüsusi bir qaynaq paketi tələb edir.", "mco.configure.world.resourcepack.question.line2": "Oynamak üçün avtomatik olaraq endirmək və qurmaq istəyirsiniz?", "mco.configure.world.restore.download.question.line1": "Dünya endiriləcək və tək oyunçulu dünyalarına əlavə olunacaq.", "mco.configure.world.restore.download.question.line2": "Davam etmək istədiyinizə əminsiz?", "mco.configure.world.restore.question.line1": "Realm'ın '%s' tarixindəki vəziyyətinə geri gətiriləcək (%s)", "mco.configure.world.restore.question.line2": "Davam etmək istədiyinizə əminsiniz?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Parametrlər", "mco.configure.world.slot": "Dünya %s", "mco.configure.world.slot.empty": "Boş", "mco.configure.world.slot.switch.question.line1": "Realm'ınız başqa bir dünya ilə dəyişdiriləcək", "mco.configure.world.slot.switch.question.line2": "Davam etmək istədiyinizə əminsiniz?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.active": "Qoşul", "mco.configure.world.slot.tooltip.minigame": "Mini oyuna keç", "mco.configure.world.spawnAnimals": "Heyvanları çağır", "mco.configure.world.spawnMonsters": "Canavarları çağır", "mco.configure.world.spawnNPCs": "NPC'lə<PERSON>", "mco.configure.world.spawnProtection": "Çağ<PERSON>rma q<PERSON>", "mco.configure.world.spawn_toggle.message": "Turning this option off will REMOVE ALL existing entities of that type", "mco.configure.world.spawn_toggle.message.npc": "Bu Xüsusiyyeti söndürmek, bu növdə BÜTÖV GURUMLAR SİLİNƏCƏK, məsə<PERSON> kəndlilər", "mco.configure.world.spawn_toggle.title": "Xəbərdarlıq!", "mco.configure.world.status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.days": "g<PERSON>n", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON><PERSON> doldu", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.less_than_a_day": "<PERSON>ir gündən daha az", "mco.configure.world.subscription.month": "ay", "mco.configure.world.subscription.months": "ay", "mco.configure.world.subscription.recurring.daysleft": "Avtomatik yenilənməyə qalan müddət:", "mco.configure.world.subscription.recurring.info": "Yığma vaxtı və ya təkrarlanan fakturanın söndürülməsi kimi Realms abunəliyinizə edilən dəyişikliklər növbəti hesab tarixinə qədər əks olunmayacaq.", "mco.configure.world.subscription.remaining.days": "%1$s gün(lər)", "mco.configure.world.subscription.remaining.months": "%1$s ay(lar)", "mco.configure.world.subscription.remaining.months.days": "%1$s ay, %2$s gün", "mco.configure.world.subscription.start": "Başlama ta<PERSON>i", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Qalan müddət", "mco.configure.world.subscription.title": "Abunə<PERSON><PERSON><PERSON>z", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "Dünya yarat", "mco.configure.world.switch.slot.subtitle": "<PERSON>u dünya boş, lütfən nə edəcəyinizi seçin", "mco.configure.world.title": "Realm'ı düzəlt:", "mco.configure.world.uninvite.player": "'%s' dəvətini ləğv etmək istədiyinizə əminsinizmi?", "mco.configure.world.uninvite.question": "Bu oyunçuya olan dəvəti qaldırmaq istədiyinizə əminsiniz:", "mco.configure.worlds.title": "Dünyalar", "mco.connect.authorizing": "Daxil olunur...", "mco.connect.connecting": "Realm'a əla<PERSON>...", "mco.connect.failed": "Səltənətə qoşulma uğursuz keçdi", "mco.connect.region": "Server region: %s", "mco.connect.success": "Bitdi", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Bir ad girməlisiniz!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "Dünya yaradılır...", "mco.create.world.skip": "<PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yeni Realm'ına hansı dünyanı qoymaq istədiyini seç", "mco.create.world.wait": "Realm yaradılır... ", "mco.download.cancelled": "Endirmə ləğv edildi", "mco.download.confirmation.line1": "Endirəcəyiniz dünya %s'dan böyükdür", "mco.download.confirmation.line2": "Bir daha bu dünyanı Realm'ına yükləyə bilməyəcəksiz", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON> bitdi", "mco.download.downloading": "İndiriliyor lütfen bekleyin bu işlem en fazla 10 yıl sürer", "mco.download.extracting": "Çıxarılır", "mco.download.failed": "<PERSON><PERSON><PERSON><PERSON> bilmədi", "mco.download.percent": "%s %%", "mco.download.preparing": "Endirməyə hazırlanılır", "mco.download.resourcePack.fail": "Resurs paketini yükləmək mümkün olmadı!", "mco.download.speed": "(%s/lər)", "mco.download.speed.narration": "%s/lər", "mco.download.title": "Ən axrınçı dünya endirilir", "mco.error.invalid.session.message": "Lütfən Minecraft'ı yenidən başlatmağı cəhd edin", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON><PERSON> hesab", "mco.errorMessage.6001": "<PERSON><PERSON> köhnə", "mco.errorMessage.6002": "<PERSON>d<PERSON><PERSON><PERSON> şə<PERSON>ləri qəbul edilmədi", "mco.errorMessage.6003": "Endirmə limitinə çatıldı", "mco.errorMessage.6004": "Yükləmə limitinə çatıldı", "mco.errorMessage.6005": "Dünya kilidləndi", "mco.errorMessage.6006": "Dünya köhnəlmişdir", "mco.errorMessage.6007": "Çox sahələrdə istifadəçi", "mco.errorMessage.6008": "Etibarsız sahə adı", "mco.errorMessage.6009": "Etibarsız Sahə Təsviri", "mco.errorMessage.connectionFailure": "Bir xəta meydana gəld<PERSON>, lütfən daha sonra yenidən cəhd edin.", "mco.errorMessage.generic": "Səhv oldu: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "<PERSON>əta təfərrüatları təmin edilməyib", "mco.errorMessage.realmsService": "Səhv baş verdi (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Realms-a qoşulmaq mümkün olmadı: %s", "mco.errorMessage.realmsService.realmsError": "Kürələr (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Uyğun versiyanı yoxlamaq mümkün olmadı, cavab alındı: %s", "mco.errorMessage.retry": "Əməliyyatı təkrarlayın", "mco.errorMessage.serviceBusy": "Hal<PERSON><PERSON><PERSON><PERSON><PERSON>lar məşğuldur. <PERSON><PERSON><PERSON>, bir neçə dəqiqə sonra yenidən bağlanmağa cəhd edin.", "mco.gui.button": "D<PERSON><PERSON>ə", "mco.gui.ok": "<PERSON><PERSON>", "mco.info": "Mə<PERSON>at!", "mco.invited.player.narration": "Invited player %s", "mco.invites.button.accept": "T<PERSON>sdiqlə", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON><PERSON><PERSON><PERSON>əyən dəvət yox!", "mco.invites.pending": "Yeni dəvət(lər)!", "mco.invites.title": "G<PERSON><PERSON><PERSON><PERSON><PERSON>ən dəvətlər", "mco.minigame.world.changeButton": "Başqa bir mini oyun seç", "mco.minigame.world.info.line1": "Bu dünyanızı müvəqqəti olaraq bir mini oyun ilə dəyişdirəcək!", "mco.minigame.world.info.line2": "<PERSON>ha sonra heç bir şey itirmədən orijinal dünyanıza dönə bilərsiniz.", "mco.minigame.world.noSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>n bir seçim edin", "mco.minigame.world.restore": "Mini oyun sona çatdırılır...", "mco.minigame.world.restore.question.line1": "Mini oyun sona çatdırılacak və Realm'ın yenilənəcək.", "mco.minigame.world.restore.question.line2": "Davam etmək istədiyinizə əminsiz?", "mco.minigame.world.selected": "Seçilən mini oyun:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON> də<PERSON>dirili<PERSON>...", "mco.minigame.world.startButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Mini oyun başlatılır...", "mco.minigame.world.stopButton": "Mini oyunu sona çatdır", "mco.minigame.world.switch.new": "Başqa bir mini oyun seçilsin?", "mco.minigame.world.switch.title": "Mini-oyunu də<PERSON>", "mco.minigame.world.title": "Realm'ı mini oyuna dəyişdir", "mco.news": "Realms xəbərləri", "mco.notification.dismiss": "<PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON> a<PERSON>", "mco.notification.visitUrl.message.default": "<PERSON><PERSON><PERSON><PERSON><PERSON> olma<PERSON> a<PERSON>dak<PERSON> linkə daxil olun", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "<PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "<PERSON><PERSON>", "mco.reset.world.inspiration": "İlham", "mco.reset.world.resetting.screen.title": "Dünya sıfırlanır...", "mco.reset.world.seed": "Dünya Yaradıcısı üçün Kod (İstəyə Bağlı)", "mco.reset.world.template": "Dünya şablonları", "mco.reset.world.title": "Dünyanı Sıfırla", "mco.reset.world.upload": "Dünya yüklə", "mco.reset.world.warning": "Realm'ının cari dünyası dəyişdiriləcək!", "mco.selectServer.buy": "Realm al!", "mco.selectServer.close": "Bağla", "mco.selectServer.closed": "Bağlı Realm", "mco.selectServer.closeserver": "Realm'ı Bağla", "mco.selectServer.configure": "Realm'ı düzəlt", "mco.selectServer.configureRealm": "Realm'ı düzəlt", "mco.selectServer.create": "Realm Yarat", "mco.selectServer.create.subtitle": "Select what world to put on your new Realm", "mco.selectServer.expired": "<PERSON>ü<PERSON><PERSON><PERSON> Dolmuş S<PERSON>ltənət", "mco.selectServer.expiredList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> müddəti doldu", "mco.selectServer.expiredRenew": "Təzələ", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "Sınağınız sona çatdı", "mco.selectServer.expires.day": "Bir gün içində sona çatacaq", "mco.selectServer.expires.days": "%s gün içində sona çatacaq", "mco.selectServer.expires.soon": "Yaxında sona çatacaq", "mco.selectServer.leave": "<PERSON>'dan a<PERSON>l", "mco.selectServer.loading": "Realm siyahı yüklənir", "mco.selectServer.mapOnlySupportedForVersion": "%s bu xəritəni dəstəkləmir", "mco.selectServer.minigame": "Mini Oyun:", "mco.selectServer.minigameName": "<PERSON> oyun", "mco.selectServer.minigameNotSupportedInVersion": "Bu mini oyunu %s versiyasında oynaya bilməzsiniz", "mco.selectServer.noRealms": "Sizin Realm'ınız yoxdur. Dostlarla oynamaq üçün Realm'ı əlavə edin.", "mco.selectServer.note": "Not:", "mco.selectServer.open": "Realm'ı Aç", "mco.selectServer.openserver": "Realm'ı aç", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Səltənət bir Minecraft dünyasını on nəfərlə eyni anda oynamanızı təmin edən sadə və etibarlı bir yoldur. Onlarla mini oyun və yüzlərlə xüsusi dünya dəstəkləyir. Yalnız Səltənətin sahibinin pul ödəməsi lazımdır.", "mco.selectServer.purchase": "Realm Əlavə Edin", "mco.selectServer.trial": "Bir sınaq al!", "mco.selectServer.uninitialized": "Yeni Realm'ınıza başlamaq üçün klik edin!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Yaratmaq Snapshot Realm?", "mco.snapshot.creating": "Yaratmaq Snapshot Realm...", "mco.snapshot.description": "%s ilə Cütləşib", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Realms are now available in Snapshots starting with Snapshot 23w41a. Every Realms subscription comes with a free Snapshot Realm that is separate from your normal Java Realm!", "mco.snapshotRealmsPopup.title": "Realms now available in Snapshots", "mco.snapshotRealmsPopup.urlText": "Ətraflı Məlumat", "mco.template.button.publisher": "<PERSON><PERSON><PERSON>", "mco.template.button.select": "Seç", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "<PERSON><PERSON><PERSON>", "mco.template.info.tooltip": "Naşir web saytı", "mco.template.name": "Şablon", "mco.template.select.failure": "Bu kateqoriya üçün məzmun siyahısını ala bilmədi.\nİnternet bağlantınızı yoxlayın və ya daha sonra yenidən cəhd edin.", "mco.template.select.narrate.authors": "Müəlliflər: %s", "mco.template.select.narrate.version": "versiya %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, bu məzmun kateqoriya hazırda boş<PERSON>r kimi gö<PERSON>ür.\n<PERSON><PERSON> sonra yenidən yoxlayın,.", "mco.template.select.none.linkTitle": "özünüzə bir şey təqdim etməyi düşünün", "mco.template.title": "Dünya şablonları", "mco.template.title.minigame": "Mini oyunlar", "mco.template.trailer.tooltip": "<PERSON>ə<PERSON><PERSON> fragmanı", "mco.terms.buttons.agree": "Qəbul Edirəm", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON> etmirəm", "mco.terms.sentence.1": "Minecraft Realms Xidmət <PERSON>lə<PERSON>", "mco.terms.sentence.2": "qəbul edirəm", "mco.terms.title": "Realms Xidmət <PERSON>ləri", "mco.time.daysAgo": "%1$s gün(lər) əvvəl", "mco.time.hoursAgo": "%1$s bir neçə saat əvvəl", "mco.time.minutesAgo": "%1$s dəqiqə(lər) əvvəl", "mco.time.now": "hal-hazırda", "mco.time.secondsAgo": "%1$s saniyə (lər) geri", "mco.trial.message.line1": "Öz Realm'ınıza sahib olmaq istəyirs<PERSON>?", "mco.trial.message.line2": "Daha çox məlumat üçün buraya basın!", "mco.upload.button.name": "Yüklə", "mco.upload.cancelled": "Yüklənmə ləğv edildi", "mco.upload.close.failure": "Realm'ın ba<PERSON><PERSON>, lütfən daha sonra yenidən cəhd edin", "mco.upload.done": "Yükl<PERSON><PERSON><PERSON>ə bitdi", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Yüklənə bilmədi! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>k<PERSON><PERSON><PERSON>", "mco.upload.hardcore": "<PERSON><PERSON><PERSON> modda olan dünyalar yüklənə bilməz!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Dünyanız hazırlanır", "mco.upload.select.world.none": "Heç bir tək oyunçulu dünya tapılmadı!", "mco.upload.select.world.subtitle": "Lütfən yükləmek üçün bir tək oyunçulu dünya seçin", "mco.upload.select.world.title": "<PERSON>ünya <PERSON>ü<PERSON>lə", "mco.upload.size.failure.line1": "'%s' çox böyük!", "mco.upload.size.failure.line2": "Bu fayl %s. İcazə verilən ölçü ən çox %s.", "mco.upload.uploading": "'%s' yüklənir", "mco.upload.verifying": "Xəritəniz doğrulanır", "mco.version": "Versiya: %s", "mco.warning": "Xəbərdarlıq!", "mco.worldSlot.minigame": "<PERSON>", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Tərk Et", "menu.feedback": "Əlaqə...", "menu.feedback.title": "<PERSON><PERSON>qə", "menu.game": "<PERSON><PERSON>", "menu.modded": " (Modlu)", "menu.multiplayer": "Çox Oyunçulu", "menu.online": "Minecraft Realms", "menu.options": "Seçimlər...", "menu.paused": "<PERSON>yun dayandırıldı", "menu.playdemo": "Sınaq <PERSON>ü<PERSON>ında Oyna", "menu.playerReporting": "Oyunçu Hesabatı", "menu.preparingSpawn": "Generasiya sahəsi hazırlanır: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "Oyundan Çıx", "menu.reportBugs": "Xətaları Bildir", "menu.resetdemo": "Sınaq Dünyasını Sıfırla", "menu.returnToGame": "<PERSON><PERSON><PERSON>", "menu.returnToMenu": "<PERSON><PERSON><PERSON> ve <PERSON>", "menu.savingChunks": "<PERSON><PERSON><PERSON><PERSON><PERSON> qeyd edilir", "menu.savingLevel": "Dünya yaddaşda saxlanır", "menu.sendFeedback": "Əks Əlaqə Ver", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "Lokal Şəbəkədə Paylaş", "menu.singleplayer": "Tək Oyunçulu", "menu.working": "İşləyir...", "merchant.deprecated": "Kənd<PERSON>lər öz ehtiyatlarını gündə iki dəfə yeniləyirlər.", "merchant.level.1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON>rd", "merchant.level.3": "Kiçik usta", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Ticarətlər", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Düşürmək üçün %1$s a basın", "multiplayer.applyingPack": "<PERSON><PERSON><PERSON> paketi tətbiq olunur", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Şəxsiyyət təsdiqləmə serverləri bağlıdır. Lütfən daha sonra yenidən cəhd edin, üzr istəyirik!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.banned.expiration": "Sizin Ban %s silinəcək", "multiplayer.disconnect.banned.reason": "Bu <PERSON>dən ban<PERSON>.\nSəbəb: %s", "multiplayer.disconnect.banned_ip.expiration": "\nQadağanız %s-də silinəcək", "multiplayer.disconnect.banned_ip.reason": "Sizin İP ünvanınız bu serverdə qadağa olunmuşdur.\nSəbəb: %s", "multiplayer.disconnect.chat_validation_failed": "Çat mesajının doğrulanması uğursuz oldu", "multiplayer.disconnect.duplicate_login": "Başqa bir yerdən daxil <PERSON>uz", "multiplayer.disconnect.expired_public_key": "Vaxtı keçmiş profil açıq açarı. Sistem vaxtınızın sinxronlaşdırıldığını yoxlayın və oyunu yenidən başladın.", "multiplayer.disconnect.flying": "Uçma bu serverdə aktiv deyil", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON><PERSON> kəsildi", "multiplayer.disconnect.idling": "Çox uzun müddət bir şey etmədən gözlədiniz!", "multiplayer.disconnect.illegal_characters": "Çatda keçərsiz simvollar", "multiplayer.disconnect.incompatible": "Uyğun olmayan istemci! Lütfən %s versiyasını istifadə edin", "multiplayer.disconnect.invalid_entity_attacked": "Keçərsiz bir varlığa hücum edilməyə çalışıldı", "multiplayer.disconnect.invalid_packet": "Server xə<PERSON><PERSON> paket gö<PERSON>b", "multiplayer.disconnect.invalid_player_data": "Xətalı oyunçu məlumatları", "multiplayer.disconnect.invalid_player_movement": "Keçə<PERSON><PERSON> oyunçu hərəkət paketi alındı", "multiplayer.disconnect.invalid_public_key_signature": "Di girasol bro", "multiplayer.disconnect.invalid_public_key_signature.new": "Profil açıq açarı üçün etibarsız imza.\n Oyununuzu yenidən başlatmağa çalışın.", "multiplayer.disconnect.invalid_vehicle_movement": "Keçə<PERSON><PERSON> vasitə hərəkət paketi alındı", "multiplayer.disconnect.ip_banned": "Siz IP bu server qadağan edilmişdir", "multiplayer.disconnect.kicked": "Bir idarəçi tərəfindən atıldın", "multiplayer.disconnect.missing_tags": "Server<PERSON><PERSON>n alınan etiketlərin natamam dəsti.\n<PERSON><PERSON><PERSON><PERSON>t olmasa server operatoru ilə əlaqə saxlayın.", "multiplayer.disconnect.name_taken": "Bu ad artıq mə<PERSON>ur", "multiplayer.disconnect.not_whitelisted": "Bu server ağ siyahıda deyil!", "multiplayer.disconnect.out_of_order_chat": "Etibarsız çat paketi alındı. Sistem vaxtınız dəyişib?", "multiplayer.disconnect.outdated_client": "Alıcı uyğun deyil! <PERSON><PERSON><PERSON><PERSON>t olmasa, %s versiyasını istifadə edin", "multiplayer.disconnect.outdated_server": "Alıcı uyğun deyil! <PERSON><PERSON><PERSON><PERSON>t olmasa, %s versiyasını istifadə edin", "multiplayer.disconnect.server_full": "Server doludur!", "multiplayer.disconnect.server_shutdown": "Server ba<PERSON><PERSON><PERSON>", "multiplayer.disconnect.slow_login": "Daxil olmaq çox uzun sürdü", "multiplayer.disconnect.too_many_pending_chats": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> artıq çoxlu qəbul edilməmiş çat mesajları", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Müşt<PERSON>ridən gözlənilməz istifadəçi məlumatları", "multiplayer.disconnect.unsigned_chat": "Çatışmayan və ya etibarsız imza ilə çat paketi alındı.", "multiplayer.disconnect.unverified_username": "İstifadəçi adı təsdiqlənə bilmədi!", "multiplayer.downloadingStats": "Statistikanın alın<PERSON>ı...", "multiplayer.downloadingTerrain": "<PERSON>razi yüklənir...", "multiplayer.lan.server_found": "Yeni server tapıldı: %s", "multiplayer.message_not_delivered": "Çat mesajını çatdırmaq mü<PERSON>, server qeydl<PERSON>rini yoxlayın: %s", "multiplayer.player.joined": "%s oyuna daxil oldu", "multiplayer.player.joined.renamed": "%s (əvvəlki adı ilə %s) oyuna daxil oldu", "multiplayer.player.left": "%s oyunu tərk etdi", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Onlayn oyunçular: %s", "multiplayer.requiredTexturePrompt.disconnect": "Bu server x<PERSON><PERSON><PERSON> bir qaynaq paketi tələb edir", "multiplayer.requiredTexturePrompt.line1": "Bu server x<PERSON><PERSON><PERSON> qaynaq paketi tələb edir.", "multiplayer.requiredTexturePrompt.line2": "Bu fərdi resurs paketinin rədd edilməsi sizi bu serverdən ayıracaq.", "multiplayer.socialInteractions.not_available": "Sosial Əlaqələr ekranı yalnız çox oyunçu səviyyələrində işləyir", "multiplayer.status.and_more": "... və %s daha ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON> edildi", "multiplayer.status.cannot_connect": "Serverə əlaqələnə bilmir", "multiplayer.status.cannot_resolve": "Host adı əldə edilə bilmir", "multiplayer.status.finished": "Bitdi", "multiplayer.status.incompatible": "Uyğun olmayan versiya!", "multiplayer.status.motd.narration": "Günün mesajı: %s", "multiplayer.status.no_connection": "(əlaqə yox)", "multiplayer.status.old": "Köhnə", "multiplayer.status.online": "Onlayn", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisaniyə", "multiplayer.status.pinging": "Qoşulur...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s of %s oyunçu onlayn", "multiplayer.status.quitting": "Çıxılır", "multiplayer.status.request_handled": "<PERSON>al və<PERSON><PERSON><PERSON><PERSON><PERSON> haqqında sorğu", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "İstənməyən vəziyyət alındı", "multiplayer.status.version.narration": "Server versiyası: %s", "multiplayer.stopSleeping": "Çarpayını Tərk Et", "multiplayer.texturePrompt.failure.line1": "Server resurs paketi tətbiq oluna bi<PERSON>ədi", "multiplayer.texturePrompt.failure.line2": "<PERSON><PERSON><PERSON>i mənbələr tələb edən hər hansı bir əməliyyat gözlənildiyi kimi işləməyə bilər", "multiplayer.texturePrompt.line1": "Bu server özəl bir qaynaq paketindən istifadə etməyi önərir.", "multiplayer.texturePrompt.line2": "Avtomatik olaraq endirilib yüklənməsini istəyirsiniz?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nServerdən mesaj:\n%s", "multiplayer.title": "Çox Oyunçulu Oyna", "multiplayer.unsecureserver.toast": "Bu serverə göndərilən mesajlar dəyişdirilə və orijinal mesajı əks etdirməyə bilər", "multiplayer.unsecureserver.toast.title": "Çat mesajlarını doğrulamaq mümkün deyil", "multiplayerWarning.check": "Bu ekranı bir daha gö<PERSON>ə<PERSON>əyin", "multiplayerWarning.header": "Diqqət: <PERSON><PERSON><PERSON><PERSON><PERSON> tərəfin onlayn oyunu", "multiplayerWarning.message": "‘Xəbərdarlıq: Mojang studios ya da Microsoft tarafından sahib olmayan, işlədilməyən və gözləməyən üçüncü parti serverların onlayn oyunları təmin edir. İnternet oyunları sırasında, bütün yaşlar üçün uyğun olmayan çat ismarışlarına və ya digər istifadəçilərin məlumatlarına göstəriləbilirsiniz.‘.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Düymə: %s", "narration.button.usage.focused": "Aktivləşdirmək üçün Enter düyməsini basın", "narration.button.usage.hovered": "Aktivləşdirmək üçün sola klikləyin", "narration.checkbox": "Təsdiq qutusu: %s", "narration.checkbox.usage.focused": "<PERSON>çid üçün Enter-ə basın", "narration.checkbox.usage.hovered": "<PERSON><PERSON><PERSON> ü<PERSON>ün sola k<PERSON>ləyin", "narration.component_list.usage": "Növbətə elementlə keçmək üçün Tab-a basın", "narration.cycle_button.usage.focused": "%s-ə keçid etmək üçün Enter-ə bas", "narration.cycle_button.usage.hovered": "%s-ə keçid etmək üçün sola kliklə", "narration.edit_box": "Redaktə qutusu: %s", "narration.item": "Item: %s", "narration.recipe": "%s üçün Spesifikasiya", "narration.recipe.usage": "Seçmək üçün sola k<PERSON>ləyin", "narration.recipe.usage.more": "Daha çox resept üçün sağa k<PERSON>ləyin", "narration.selection.usage": "Başqa başlığa keçmək üçün yuxarı və aşağı düymələrinə basın", "narration.slider.usage.focused": "<PERSON><PERSON><PERSON><PERSON><PERSON> dəyişmək üçün sol və ya sağ düymələrə basın", "narration.slider.usage.hovered": "<PERSON><PERSON><PERSON><PERSON><PERSON> dəyişmək üçün diyircəyi çəkin", "narration.suggestion": "Seçilmiş təklif %s çıxmaq %s: %s", "narration.suggestion.tooltip": "Seçilən təklif%s-dən%s:%s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Nişanlar arasında keçid etmək üçün Ctrl və Tab düymələrini basın", "narrator.button.accessibility": "Əlçatanlıq", "narrator.button.difficulty_lock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>ı", "narrator.button.difficulty_lock.locked": "Qıfıllandı", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "Dil", "narrator.controls.bound": "%s %s ilə əlaqələnib", "narrator.controls.reset": "%s dü<PERSON>əsini sıfırla", "narrator.controls.unbound": "%s əlaqələnməyib", "narrator.joining": "Qoşulur", "narrator.loading": "Yüklənir: %s", "narrator.loading.done": "Bitdi", "narrator.position.list": "Seçilmiş siyahı elementi %s/%s", "narrator.position.object_list": "Seçilmiş element %s/%s", "narrator.position.screen": "%s/%s ekran elementi", "narrator.position.tab": "%s paneldən %s seçildi", "narrator.ready_to_play": "Oynamağa hazır", "narrator.screen.title": "<PERSON>", "narrator.screen.usage": "Kursordan və ya Tab düyməsindən istifadə edərək element seçin", "narrator.select": "Seçildi: %s", "narrator.select.world": "Seçilib %s, son oynanılan %s,%s,%s, versiya: %s", "narrator.select.world_info": "%s se<PERSON><PERSON><PERSON>, son <PERSON><PERSON><PERSON><PERSON><PERSON>: %s, %s", "narrator.toast.disabled": "Mətn Oxuyucu Passiv", "narrator.toast.enabled": "Mətn Oxuyucu Aktiv", "optimizeWorld.confirm.description": "Bu, dünyanı dünyaya optimallaşdırmaq cəhdi. Bu dünyaya bağlı olaraq çox uzun müddət ala bilər. Bir dəfə başa çatdıqda, oyununuzu oynamaq mümkündür. Davam etmək istədiyinizə əminsinizmi?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Dünyanı Optimizə Edin", "optimizeWorld.info.converted": "Təkmilləşdirilmiş parçaları:%s", "optimizeWorld.info.skipped": "Atılan yığınlar: %s", "optimizeWorld.info.total": "Ümumi yığınlar: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Yığınlar sayılır...", "optimizeWorld.stage.failed": "Xəta baş verdi! :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Varlıqların yenilənməsini tamamlayırıq...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Dünya'yı optimallaşdırma '%s'", "options.accessibility": "Accessibility Settings...", "options.accessibility.high_contrast": "Yüksək Kontrast", "options.accessibility.high_contrast.error.tooltip": "Yüksək Kontrast resurs paketi mövcud deyil", "options.accessibility.high_contrast.tooltip": "UI elementlərinin kontrastını artırır", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Erişilebilirlik Rəhbəri", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON> is<PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panorama Sürüşü", "options.accessibility.text_background": "Mə<PERSON>nin arxa planı", "options.accessibility.text_background.chat": "Çat", "options.accessibility.text_background.everywhere": "<PERSON>ər yer", "options.accessibility.text_background_opacity": "Mətn Fonunda Qeyri-Şəffaflıq\n", "options.accessibility.title": "<PERSON><PERSON><PERSON><PERSON>...", "options.allowServerListing": "Server si<PERSON><PERSON><PERSON><PERSON>na ica<PERSON>ə ver", "options.allowServerListing.tooltip": "Serverlər ictimai statusunun bir hissəsi kimi onlayn oyunçuları siyahıya sala bilər.\nBu seçim söndürüldükdə adınız belə siyahılarda görünməyəcək.", "options.ao": "Yumuşaq İşıqlandırma", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minumum", "options.ao.off": "OFF", "options.attack.crosshair": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attack.hotbar": "<PERSON>ət Paneli", "options.attackIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.audioDevice": "<PERSON><PERSON><PERSON>", "options.audioDevice.default": "Başlanğıc sistem", "options.autoJump": "Avto-Tullanma", "options.autoSuggestCommands": "<PERSON><PERSON><PERSON>", "options.autosaveIndicator": "Avtoyaddaş göstəricisi", "options.biomeBlendRadius": "Biom qarışığı", "options.biomeBlendRadius.1": "BAĞLI (Ən sürətli)", "options.biomeBlendRadius.11": "11x11 (Aşırı)", "options.biomeBlendRadius.13": "13x13 (z<PERSON><PERSON> da sən?)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (Çox Yüksək)", "options.chat": "Çat Parametrləri...", "options.chat.color": "Rəng<PERSON><PERSON><PERSON>", "options.chat.delay": "Çat gecikməsi: %s saniyə", "options.chat.delay_none": "Çat gecikməsi: <PERSON><PERSON>", "options.chat.height.focused": "Fokuslaşmış Yüksəklik", "options.chat.height.unfocused": "Fokuslaşmamış Yüksəklik", "options.chat.line_spacing": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.links": "İnternet Linkləri", "options.chat.links.prompt": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.opacity": "Çat Mətnin Qeyri-Şəffaflığı", "options.chat.scale": "Çat Mətn Ölçüsü", "options.chat.title": "Çat Nizamları", "options.chat.visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON>", "options.chat.visibility.system": "Yalnız Əmrlər", "options.chat.width": "Genişlik", "options.chunks": "%s yığın", "options.clouds.fancy": "Xülya", "options.clouds.fast": "Tez", "options.controls": "Kontrollar...", "options.credits_and_attribution": "Kreditlər və Atributlar...", "options.damageTiltStrength": "<PERSON><PERSON><PERSON><PERSON><PERSON> ə<PERSON>", "options.damageTiltStrength.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON><PERSON>nin səbəb olduğu kamera silkələnməsinin miqdarı.", "options.darkMojangStudiosBackgroundColor": "Monoxrom loqo", "options.darkMojangStudiosBackgroundColor.tooltip": "Mojang Studios yükləmə ekranının arxaplanını qara rəngə dəyişdirir.", "options.darknessEffectScale": "Qaranlıq vurur", "options.darknessEffectScale.tooltip": "Mühafizə<PERSON><PERSON> və ya qışqıran skalkın sənə verdiyi qaranlıq effektini təmzinləyir.", "options.difficulty": "Ç<PERSON><PERSON>lik", "options.difficulty.easy": "<PERSON><PERSON>", "options.difficulty.easy.info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dəstələri kürü tökür, lakin daha az zərər verir. Aclıq çubuğu tükənir və sağlamlığı 5 ürəyə qədər qurudur.", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "D<PERSON>ş<PERSON>ən dəstələri kürü tökür və daha çox zərər verir. A<PERSON><PERSON><PERSON>q çubuğu bütün sağlamlığı tükəndirir və qurudur.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "<PERSON><PERSON>ş<PERSON>ən dəstələri kürü tökür və standart zərər verir. Aclıq çubuğu ürəyin yarısına qədər sağlamlığını tükəndirir və qurudur.", "options.difficulty.online": "<PERSON>", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "D<PERSON>ş<PERSON>ən dəstələri yoxdur və yalnız bəzi neytral dəstələr yaranır. Aclıq çubuğu tükənmir və sağlamlıq zamanla yenilənir.", "options.directionalAudio": "İstiqamətli audio", "options.directionalAudio.off.tooltip": "Klassik Stereo səsi", "options.directionalAudio.on.tooltip": "3D səsin simulyasiyasını təkmilləşdirmək üçün HRTF əsaslı istiqamətləndirici audiodan istifadə edir. HRTF uyğun audio avadanlığı tələb edir və qulaqlıqlarda ən yaxşı təcrübəyə malikdir.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON>", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON> mə<PERSON>", "options.entityShadows": "V<PERSON>l<PERSON>q <PERSON>", "options.font": "Font Settings...", "options.font.title": "Font Settings", "options.forceUnicodeFont": "Unicode'a Məcbur Et", "options.fov": "Baxış Bucağı", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "FOV Efektləri", "options.fovEffectScale.tooltip": "Baxış yeri effektinin kontrol oyun prosesinin effektlərindən ilə dəyişə bilər.", "options.framerate": "%s fps", "options.framerateLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.framerateLimit.max": "Limitsiz", "options.fullscreen": "<PERSON>", "options.fullscreen.current": "<PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Tam Ekran Çözünürlüğü", "options.fullscreen.unavailable": "Ayar geçersiz", "options.gamma": "Parlaqlıq", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "Parlaq", "options.gamma.min": "Qaranlıq", "options.generic_value": "%s: %s", "options.glintSpeed": "Parıltı Sürəti", "options.glintSpeed.tooltip": "Vizual parıltının sehrlənmiş əşyalar arasında nə qədər sürətlə parıldamasına nəzarət edir.", "options.glintStrength": "Parıltı Gücü", "options.glintStrength.tooltip": "Sehrlənmiş əşyalarda vizual parıltının nə qədər şəffaf olduğuna nəzarət edir.", "options.graphics": "Qrafikl<PERSON>r", "options.graphics.fabulous": "İnanılmaz!", "options.graphics.fabulous.tooltip": "%s qrafika hava, bulud və şəffaf bloklar və su arxasında hissəciklər çəkmək üçün ekran kölgələrindən istifadə edir.\nBu, portativ qurğular və 4K ekranların performansına ciddi təsir göstərə bilər.", "options.graphics.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "Fancy qrafika maşınların əksəriyyəti üçün performans və keyfiyyəti tarazlaşdırır.\nHava, bulud və hissəciklər şəffaf blokların və ya suyun arxasında görünə bilməz.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "S<PERSON>rətli qrafika görünən yağış və qar miqdarını azaldır.\nŞəffaflıq effektləri ağac yarpaqları kimi müxtəlif bloklar üçün əlil olur.", "options.graphics.warning.accept": "<PERSON><PERSON><PERSON><PERSON>k olmadan davam edin", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON> geri apar", "options.graphics.warning.message": "Qrafik cihazınız %s qrafika seçimi üçün dəstəklənməyən kimi aşkar edilmişdir.\n\nBuna məhəl qoymaya və davam edə bilərsiniz, lakin %s qrafikasını istifadə etməyi seçsəniz cihazınıza dəstək verilməyəcəkdir.", "options.graphics.warning.renderer": "Renderer aşkarlandı: [%s]", "options.graphics.warning.title": "Qrafik cihazı dəstəklənmir", "options.graphics.warning.vendor": "Satıcı aşkarlandı: [%s]", "options.graphics.warning.version": "OpenGL versiyası aşkarlandı: [%s]", "options.guiScale": "İnterfeys Miqyası", "options.guiScale.auto": "Avtomatik", "options.hidden": "<PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "İld<PERSON>rım çaxmalarını gizlət", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> parlaq şimşək şəkillərini qadağan edir. Özü isə şimşək xətləri hələ də görünəcəkdir.", "options.hideMatchedNames": "Uyğunlaşan Adları Gizləd", "options.hideMatchedNames.tooltip": "3-cü tərəf serverləri qeyri-standart formatlarda çat mesajları göndərə bilər.\nBu seçim aktiv olsa, gizli oyunçular söhbət göndərən adları əsasında eşləşdiriləcəkdir.", "options.hideSplashTexts": "Pop-up mesaj<PERSON>ını gizlət", "options.hideSplashTexts.tooltip": "<PERSON><PERSON><PERSON> menyuda sarı ekran qoruyucu mətni gizlədir.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimallaştır", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "<PERSON><PERSON><PERSON> <PERSON>", "options.japaneseGlyphVariants": "Yapon simvollarının variant<PERSON>ı", "options.japaneseGlyphVariants.tooltip": "Standart şriftdə CJK simvollarının Yapon variantlarından istifadə edir", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.language": "Dil...", "options.language.title": "Dil", "options.languageAccuracyWarning": "(Dil tərcümələri 100%% dəqiq olmaya bilər)", "options.languageWarning": "Dil tərcümələri %%100 dəqiq olmaya bilər", "options.mainHand": "Əsas <PERSON>", "options.mainHand.left": "Sol", "options.mainHand.right": "Sağ", "options.mipmapLevels": "Mipmap Səviyyələri", "options.modelPart.cape": "Örtük", "options.modelPart.hat": "Papa<PERSON>", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Sol Ayaq", "options.modelPart.left_sleeve": "Sol Qol", "options.modelPart.right_pants_leg": "<PERSON>ğ <PERSON>", "options.modelPart.right_sleeve": "Sağ Qol", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON>", "options.mouse_settings": "Siçan parametrləri...", "options.mouse_settings.title": "Siçan parametrləri", "options.multiplayer.title": "Çoxlu Oyunçulu Nizamları...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Mə<PERSON>n O<PERSON>ucu", "options.narrator.all": "Hamısını oxuyur", "options.narrator.chat": "Çatı Oxuyur", "options.narrator.notavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.narrator.off": "Sönük", "options.narrator.system": "<PERSON><PERSON><PERSON>", "options.notifications.display_time": "Bildiriş vaxtı", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> bildiri<PERSON>lərin ekranda görünmə müddətinə təsir edir.", "options.off": "Qapalı", "options.off.composed": "%s: OFF", "options.on": "Açıq", "options.on.composed": "%s: ON", "options.online": "Onlayn...", "options.online.title": "Onlayn nizamlamaları", "options.onlyShowSecureChat": "Ancaq Təhlükəsiz Çat Göstərilmə", "options.onlyShowSecureChat.tooltip": "Yalnız digər oyunçuların həmin oyunçu tərəfindən göndərilməsi təsdiqlənə bilən və dəyişdirilməmiş mesajları göstərin.", "options.operatorItemsTab": "Operator <PERSON><PERSON><PERSON><PERSON>", "options.particles": "Hissəcik<PERSON><PERSON><PERSON>", "options.particles.all": "Hamısı", "options.particles.decreased": "Azaldılmış", "options.particles.minimal": "Ən Az", "options.percent_add_value": "%s: %s (%%", "options.percent_value": "%s, %s, %%", "options.pixel_value": "%s: %s", "options.prioritizeChunkUpdates": "Çank qurucusu", "options.prioritizeChunkUpdates.byPlayer": "Yarı-b<PERSON>klama", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Çank daxilindəki bəzi proseslər çankı dərhal yenidən tərtib edəcək. Buraya blokların yerləşdirilməsi və məhv edilməsi daxildir.", "options.prioritizeChunkUpdates.nearby": "<PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Yaxınlıqdakı çanklar həmişə dərhal tərtib edilir. Bloklar yerləşdirildikdə və ya məhv edildikdə bu, oyun performansına təsir edə bilər.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Yaxınlıqdakı çanklar paralel sıra üzrə yığılıblar. Bu, bloklar məhv ediləndə vizual dəliklərə səbəb ola bilər.", "options.rawMouseInput": "<PERSON><PERSON>", "options.realmsNotifications": "Realms Xəbərləri və Dəvətlər", "options.realmsNotifications.tooltip": "Realms xəbərlərini və dəvətnamələrini Başlıq ekranına çıxarır və Realms düyməsindəki müvafiq işarəni göstərir.", "options.reducedDebugInfo": "<PERSON>z <PERSON>ə<PERSON> Məlumatı", "options.renderClouds": "Buludlar", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> məsafəsi", "options.resourcepack": "Qaynaq <PERSON>...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Seçmə effekti", "options.screenEffectScale.tooltip": "Baş gicəllənmənin gücü və cəhənnəm portalının effektləri. Aşağı qiymətlərdə başgicəllənmə effekti yaşıl əngin əlavə olunması ilə əvəzlənir.", "options.sensitivity": "Həssaslıq", "options.sensitivity.max": "YÜKSƏK SÜRƏT!!!", "options.sensitivity.min": "<əsnəmə>", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Altyazıları Göstər", "options.simulationDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> mə<PERSON>ə<PERSON>", "options.skinCustomisation": "Görünüş Özəlləşdirmə...", "options.skinCustomisation.title": "Görünüş Özəlləşdirmə", "options.sounds": "Musiqi və Səslər...", "options.sounds.title": "Musiqi və Səs Seç<PERSON>ləri", "options.telemetry": "Telemetriya Məlumatı...", "options.telemetry.button": "Məlumatların <PERSON>", "options.telemetry.button.tooltip": "\"%s\" yalnız tələb olunan məlumatları ehtiva edir.\n \"%s\" iste<PERSON><PERSON> bağlı, həmçinin tələb olunan məlumatları ehtiva edir.", "options.telemetry.disabled": "Telemetriya deaktivdir.", "options.telemetry.state.all": "Hamısı", "options.telemetry.state.minimal": "Ən Az", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "Se<PERSON><PERSON>l<PERSON><PERSON>", "options.touchscreen": "<PERSON><PERSON><PERSON><PERSON>", "options.video": "Video Nizamları...", "options.videoTitle": "Video Nizamları", "options.viewBobbing": "Sarsılmanı Göstər", "options.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft yaddaşı tükəndi.\n\nBuna oyundakı səhv və ya Java Virtual Maşınına kifayət qədər yaddaşın ayrılmaması səbəb ola bilər.\n\nSəviyyə korrupsiyasının qarşısını almaq üçün cari oyun dayandırıldı. Sizə əsas menyuya qayıtmaq və yenidən ifa etməyə imkan vermək üçün kifayət qədər yaddaş boşaltmağa çalışdıq, lakin bu, işləməyə bilər.\n\nBu mesajı yenidən görsəniz, oyunu yenidən başladın.", "outOfMemory.title": "Yaddaş çatmır!", "pack.available.title": "Mövcuddur", "pack.copyFailure": "Pak<PERSON>ləri k<PERSON> al<PERSON>", "pack.dropConfirm": "Aşağıdakı paketləri əlavə etmək istəyirsiniz Minecraft?", "pack.dropInfo": "Paket əlavə etmək üçün faylları bu pəncərəyə sürükləyin və atın", "pack.dropRejected.message": "Aşağıdakı girişlər etibarlı paketlər deyildi və kopyalanmadı: %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON>", "pack.folderInfo": "(<PERSON><PERSON> sənədlə<PERSON> burada yerləşdirin)", "pack.incompatible": "<PERSON><PERSON><PERSON><PERSON>", "pack.incompatible.confirm.new": "Bu paket Minecraftın daha yeni bir versiyası üçün hazırlanmışdı və artıq düzgün işləməyə bilər.", "pack.incompatible.confirm.old": "Bu paket Minecraftın köhnə bir versiyası üçün hazırlanmışdı və artıq düzgün işləməyə bilər.", "pack.incompatible.confirm.title": "Bu paketi yükləmək istədiyinizə əminsiniz?", "pack.incompatible.new": "(Daha yeni bir Minecraft versiyası üçün yapıldı)", "pack.incompatible.old": "(Daha köhnə bir Minecraft versiyası üçün yapıldı)", "pack.nameAndSource": "()", "pack.openFolder": "<PERSON><PERSON> qovluğ<PERSON>u açın", "pack.selected.title": "Seçilmiş", "pack.source.builtin": "daxili", "pack.source.feature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.local": "yerli", "pack.source.server": "server", "pack.source.world": "dünya", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Alban", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON>la Bombalandı", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON><PERSON><PERSON> Alov İçində", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Konq", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Yer", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Döyüşçülər", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Kibrit", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON> səhnəsi", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Hovuz", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Dənizkənarı", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Kəllə və Güllər", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Boşluq", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "<PERSON><PERSON>", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Su", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "kü<PERSON>ək", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Təsadüfi variant", "parsing.bool.expected": "Göz<PERSON><PERSON><PERSON><PERSON><PERSON>n boolean", "parsing.bool.invalid": "<PERSON><PERSON><PERSON>ş boolean, gö<PERSON>lə<PERSON>l<PERSON><PERSON> 'həqiqi' və ya 'yalan' amma '%s' tapıldı", "parsing.double.expected": "Gözlənilən ikiqat", "parsing.double.invalid": "Yanlış tam '%s'", "parsing.expected": "Gözlənilən '%s'", "parsing.float.expected": "Gözlə<PERSON>lən uzun", "parsing.float.invalid": "Yanlış üzmə '%s'", "parsing.int.expected": "Gözlənilən tam ədəd", "parsing.int.invalid": "Yanlış tam '%s'", "parsing.long.expected": "Gözlə<PERSON>lən uzun", "parsing.long.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> json: %s'", "parsing.quote.escape": "Sitat gətirilmiş sətirdə '\\%s' səhv qaçış ardıcıllığı", "parsing.quote.expected.end": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>m<PERSON><PERSON> ip", "parsing.quote.expected.start": "Bir simli başlamaq üçün gözlənilən kotirovka", "particle.invalidOptions": "Невозможно проанализировать параметры частиц: %s", "particle.notFound": "Naməlum predikat: %s", "permissions.requires.entity": "Bu komandanı burada işlətmək üçün bir oyunçu tələb olunur", "permissions.requires.player": "Bu komandanı burada işlətmək üçün bir oyunçu tələb olunur", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "İsifadə Edilərsə:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Naməlum predikat: %s", "quickplay.error.invalid_identifier": "Təqdim olunan identifikatorla dünyanı tapmaq mümkün olmadı", "quickplay.error.realm_connect": "Realms'a əlaqələnilə bilmir", "quickplay.error.realm_permission": "Bu Realms'a qoşulmaq üçün icazə yoxdur", "quickplay.error.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oynamaq alı<PERSON>", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> anlar şəklində dəstəklənmir", "recipe.notFound": "Naməlum predikat: %s", "recipe.toast.description": "<PERSON><PERSON><PERSON><PERSON>ızı yoxlayın", "recipe.toast.title": "Yeni Təriflər Açıldı!", "record.nowPlaying": "İndi çalır: %s", "recover_world.bug_tracker": "Xətaları Bildir", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Əv<PERSON>əlki vəziyyətdən bərpa oluna bi<PERSON>ədi.", "recover_world.done.success": "Bərpa uğurlu oldu!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "<PERSON><PERSON>", "recover_world.issue.none": "No issues", "recover_world.message": "\"Dünya\" qovluğunu \"%s\" oxumağa çalışarkən aşağıdakı problemlər ortaya çıxdı.\nBəlkə də dünyanı köhnə bir vəziyyətdən bərpa etmək üçün bir seçim var və ya bu problemi səhv izləmə sisteminə bildirə bilərsiniz.", "recover_world.no_fallback": "<PERSON>ə<PERSON><PERSON> vəziy<PERSON>əti yoxdur", "recover_world.restore": "<PERSON>ərpa etməyə cəhd", "recover_world.restoring": "<PERSON><PERSON><PERSON><PERSON><PERSON> bərpa etməyə çalışır...", "recover_world.state_entry": "%s-dən vəziyyət: ", "recover_world.state_entry.unknown": "na<PERSON><PERSON><PERSON>", "recover_world.title": "<PERSON>ünya yükləyə bilmədi", "recover_world.warning": "Dünya üzrə hesabat yükləmək mümkün olmayıb", "resourcePack.broken_assets": "QIRIQ VASİTƏLƏR AŞKAR EDİLDİ", "resourcePack.high_contrast.name": "Yüksək Kontrast", "resourcePack.load_fail": "Re<PERSON>rs yeni<PERSON>ənməsi uğ<PERSON>uz oldu", "resourcePack.programmer_art.name": "Proqramçı Art", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "Spesifik dünya resursları", "resourcePack.title": "Qaynaq Paketi Seç", "resourcePack.vanilla.description": "Minecraft-ın standart gö<PERSON><PERSON>n<PERSON>ş<PERSON> və hissi", "resourcePack.vanilla.name": "Varsayılan", "resourcepack.downloading": "<PERSON><PERSON><PERSON><PERSON>", "resourcepack.progress": "Fayl endirilir (%s MB)...", "resourcepack.requesting": "İstənilir...", "screenshot.failure": "Ekran görünüşü qeyd edilə bilmədi: %s", "screenshot.success": "Ekran görünüşü %s adı ilə qeyd edildi", "selectServer.add": "Server Əlavə E<PERSON>", "selectServer.defaultName": "Minecraft Severi", "selectServer.delete": "Sil", "selectServer.deleteButton": "Sil", "selectServer.deleteQuestion": "Bu serveri silmək istədiyinizə əminsiniz?", "selectServer.deleteWarning": "'%s' əbədi itkin olacaq! (<PERSON><PERSON><PERSON> müddətdir!)", "selectServer.direct": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON>)", "selectServer.refresh": "Yenilə", "selectServer.select": "Serverə Qoşul", "selectWorld.access_failure": "Dü<PERSON>ya daxil olma <PERSON>u", "selectWorld.allowCommands": "Hi<PERSON>ələrə icazə ver", "selectWorld.allowCommands.info": "/gamemode, /experience kimi əmrlər", "selectWorld.allowCommands.new": "Komandalara icazə verin", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> <PERSON>ə<PERSON><PERSON><PERSON>", "selectWorld.backupJoinConfirmButton": "Rezerv nüsxə yarat və yüklə", "selectWorld.backupJoinSkipButton": "Nə etdiyimi bilirəm!", "selectWorld.backupQuestion.customized": "<PERSON><PERSON><PERSON><PERSON> dünyalar artıq dəstə<PERSON>ə<PERSON>", "selectWorld.backupQuestion.downgrade": "Dünya əvvəlki versiyalara qaytarılmağı dəstəkləmir", "selectWorld.backupQuestion.experimental": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> parametrlərdən istifadə edən dünyalar dəstəklənmir", "selectWorld.backupQuestion.snapshot": "Bu dünyanı həqiqətən yüklemek istəyirsiniz?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ki, Minecraft'ın bu versiyasında xüsusi dünyalara dəstək vermirik. İndi özelleştirilemez. Narahatçılığa görə üzr istəyirik!", "selectWorld.backupWarning.downgrade": "Bu dünyada ən son %s versiyasında oynanıldı; siz %s versiyasındasınız. Bir dünyanın əvvəlki versiyasına qaytarılması nəticəsində bəzi xətalar baş verə bilər; dünyanın yüklənməsi və ya işləməsinə dair təminat verə bilmərik. Hələ də davam etmək istəyirsiniz isə dünyanızın nüsxəsini yaradın!", "selectWorld.backupWarning.experimental": "Bu dünya istənilən vaxt işini dayandıra biləcək eksperimental parametrlərdən istifadə edir. Yüklənəcəyinə və işləyəcəyinə zəmanət verə bilmərik. Budur əjdahalar!", "selectWorld.backupWarning.snapshot": "Bu dünya sonuncu dəfə %s oynadı; versiyası %s üzərindədir. Xahiş edirik korrupsiyanın dünyasını yaşayırsınız!", "selectWorld.bonusItems": "Bonus sandıq", "selectWorld.cheats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Çevrilməsi lazımdır!", "selectWorld.conversion.tooltip": "Bu dünyanı təhlükəsiz bir şəkildə çevirmək üçün köhnə bir versiyada (məs<PERSON><PERSON><PERSON>n, 1.6.4) açılmalıdır", "selectWorld.create": "<PERSON>ni <PERSON>", "selectWorld.customizeType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Məlumat paketləri", "selectWorld.data_read": "Dünya məlumatları oxunur...", "selectWorld.delete": "Sil", "selectWorld.deleteButton": "Sil", "selectWorld.deleteQuestion": "Bu dünyanı silmək istədiyinizə əminsiniz?", "selectWorld.deleteWarning": "'%s' əbədi itkin olacaq! (<PERSON><PERSON><PERSON> müddətdir!)", "selectWorld.delete_failure": "Dünyanı silmək uğursuz oldu", "selectWorld.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON><PERSON><PERSON> olun", "selectWorld.edit.backupCreated": "Yedildi:%s", "selectWorld.edit.backupFailed": "Ehtiyat köçürmə uğursuz oldu", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backupSize": "ölçüsü:%s MB", "selectWorld.edit.export_worldgen_settings": "<PERSON><PERSON><PERSON> yaratma parametrlərini ixrac edin", "selectWorld.edit.export_worldgen_settings.failure": "İxrac prosesi uğursuz oldu", "selectWorld.edit.export_worldgen_settings.success": "<PERSON>x<PERSON>", "selectWorld.edit.openFolder": "Dünya Qovluğunu Aç", "selectWorld.edit.optimize": "Dünyanı optimallaşdırın", "selectWorld.edit.resetIcon": "Simvolu Sıfırla", "selectWorld.edit.save": "<PERSON><PERSON><PERSON>", "selectWorld.edit.title": "Dünyanı Düzəlt", "selectWorld.enterName": "Dünya Adı", "selectWorld.enterSeed": "Dünya Yaradıcısı üçün Kod", "selectWorld.experimental": "Təcrübə üçün", "selectWorld.experimental.details": "Təfərrüatlar", "selectWorld.experimental.details.entry": "Tə<PERSON>əb olunan eksperimental funksiyalar: %s", "selectWorld.experimental.details.title": "Eksperimental xüsusiyyət tələbləri", "selectWorld.experimental.message": "Ehtiyatlı ol!\n Bu konfiqurasiya hələ inkişaf mərhələsində olan funksiyaları tələb edir. Dünyanız çökə, pozula və ya gələcək yeniləmələrlə işləməyə bilər.", "selectWorld.experimental.title": "Eksperimental Xüsusiyyətlər Xəbərdarlığı", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experiments.info": "Eksperimentlər potensial yeni xüsusiyyətlərdir. Ehtiyatlı olun, çünki əşyalar qıra bilər. Dünya yaradıldıqdan sonra təcrübələr söndürülə bilməz.", "selectWorld.futureworld.error.text": "Gələcək versiyada yaranmış dünyanı yükləyərkən problem yarandı. Bu riskli əməliyyat idi; təəssüfki alınmadı.", "selectWorld.futureworld.error.title": "Bir xəta baş verdi!", "selectWorld.gameMode": "<PERSON><PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Survival Mode ilə eynidir, lakin blokları əlavə etmək və ya silmək mümkün deyil.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON><PERSON><PERSON> modu ilə eyn<PERSON>ir, amma blo<PERSON>r yoxdur", "selectWorld.gameMode.adventure.line2": "əlavə edilmir və ya silinmir", "selectWorld.gameMode.creative": "Yaradı<PERSON>ıl<PERSON>q", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, qurun və kəşf edin. <PERSON>z u<PERSON> bilə<PERSON>z, sonsuz materiallara sahibsiniz və canavarlar tərəfindən incidə bilməzsiniz.", "selectWorld.gameMode.creative.line1": "Limitsiz mənbələr, sərbəst uçabilme və", "selectWorld.gameMode.creative.line2": "blokları dərhal qıra bilmə", "selectWorld.gameMode.hardcore": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "<PERSON><PERSON> qalma rejimi \"<PERSON><PERSON><PERSON>\" çətinliyə kilidlənib. Ölsən yenidən doğula bilməzsən.", "selectWorld.gameMode.hardcore.line1": "Həya<PERSON>da qalma modu ilə eyni, çətin modda sabitlənmişdir", "selectWorld.gameMode.hardcore.line2": "bağlı və sadəcə bir canın var", "selectWorld.gameMode.spectator": "İzləyici", "selectWorld.gameMode.spectator.info": "Baxa bilərsən amma toxuna bilməzsən.", "selectWorld.gameMode.spectator.line1": "Baxa bilərsən amma toxuna bilməzsən", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "Canavarlarla <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sən<PERSON><PERSON><PERSON><PERSON><PERSON>q etdiyiniz və döyüşdüyünüz sirli dünyanı kəşf edin.", "selectWorld.gameMode.survival.line1": "<PERSON>ən<PERSON>ə axtar, yarat, qazan", "selectWorld.gameMode.survival.line2": "əldə et; səhiyyət və aclıq", "selectWorld.gameRules": "<PERSON><PERSON> q<PERSON>ı", "selectWorld.import_worldgen_settings": "İdxal parametrləri", "selectWorld.import_worldgen_settings.failure": "Parametrləri idxal etmə xətası", "selectWorld.import_worldgen_settings.select_file": "Paramet<PERSON><PERSON>r fay<PERSON><PERSON> se<PERSON> (.json)", "selectWorld.incompatible.description": "Bu dünya bu versiyada açıla bilməz.\n<PERSON><PERSON><PERSON> dəfə %s versiyasında təkrarlandı.", "selectWorld.incompatible.info": "Uyğun olmayan versiya: %s", "selectWorld.incompatible.title": "Uyğun olmayan versiya: %s", "selectWorld.incompatible.tooltip": "Bu dünya açı<PERSON>, <PERSON><PERSON>nk<PERSON> uyğun olmayan bir versiya tərəfindən yaradılmışdır.", "selectWorld.incompatible_series": "Uyğun olmayan bir versiya tərəfindən yaradılmışdır", "selectWorld.load_folder_access": "Oyun dünya<PERSON>ının qeyd edildiyi qovluq oxuna bilmir ya da qovluğa ulaşım təmin edilə bilmir!", "selectWorld.loading_list": "Dünya siyahı yüklənir", "selectWorld.locked": "İşlə<PERSON>ən başqa Minecraft pəncərəsi tərəfindən qıfıllanıb", "selectWorld.mapFeatures": "Strukturları yarat", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gəmi qəzaları və s.", "selectWorld.mapType": "Dünya Növü", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Daha Çox Dünya Seçimi...", "selectWorld.newWorld": "<PERSON>ni <PERSON>", "selectWorld.recreate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Xüsusi dünyalar Minecraft-ın bu versiyasında artıq dəstəklənmir. Eyni dünya kodu və xüsusiyyətlə yenidən yaratmağa cəhd edə bilərik, amma bütün dünya xüsusiyyətləri itəcək. Narahatçılığa görə üzr istəyirik!", "selectWorld.recreate.customized.title": "<PERSON><PERSON><PERSON><PERSON> dünyalar artıq dəstə<PERSON>ə<PERSON>", "selectWorld.recreate.error.text": "Dünyanı yenidən yaratmağa çalışarkən xəta baş verdi.", "selectWorld.recreate.error.title": "<PERSON>əta baş verdi!", "selectWorld.resource_load": "Resursların hazı<PERSON>...", "selectWorld.resultFolder": "<PERSON><PERSON><PERSON> qeyd ediləcək:", "selectWorld.search": "dünyalar üçün axtarış", "selectWorld.seedInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON> boş bura<PERSON>ın", "selectWorld.select": "Seçilən Dünyada Oyna", "selectWorld.targetFolder": "Qovluğu yadda saxla: %s", "selectWorld.title": "Dünya <PERSON>", "selectWorld.tooltip.fromNewerVersion1": "<PERSON><PERSON>nya daha yeni bir versiyada qeyd edil<PERSON>,", "selectWorld.tooltip.fromNewerVersion2": "bu dünyanı yükləmək problemlər yarada bilər!", "selectWorld.tooltip.snapshot1": "Bu dünyanın nüsxəsini yaratmağı unutmayın", "selectWorld.tooltip.snapshot2": "yedeklemeyə unutmayın.", "selectWorld.unable_to_load": "Dünyalar yüklənə bilmir", "selectWorld.version": "Versiya:", "selectWorld.versionJoinButton": "Yenə Də Yüklə", "selectWorld.versionQuestion": "Bu dünyanı həqiqətən yüklemek istəyirsiniz?", "selectWorld.versionUnknown": "na<PERSON><PERSON><PERSON>", "selectWorld.versionWarning": "Bu dünya ən son '%s' versiyasında oynanmışdır və bu versiyada yüklənməsi pozulmasına səbəb ola bilər!", "selectWorld.warning.deprecated.question": "İstifadə olunan bəzi xüsusiyyətlər köhnəlmişdir və gələcəkdə işini dayandıracaqdır. Davam etmək istəyirsiniz?", "selectWorld.warning.deprecated.title": "Xəbərdarlıq! Bu parametrlər köhnəlmiş xüsusiyyətlərdən istifadə edir", "selectWorld.warning.experimental.question": "Bu parametrlər eksperimentaldır və bir gün işini dayandıra bilər. Davam etmək istəyirsiniz?", "selectWorld.warning.experimental.title": "Xəbərdarlıq! Bu parametrlər eksperimental xüsusiyyətlərdən istifadə edir", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Dünya", "sign.edit": "<PERSON><PERSON><PERSON>ı redaktə edin", "sleep.not_possible": "Bu gecəni keçmək üçün kifayət qədər oyunçu yatmır", "sleep.players_sleeping": "%s/%s oyunçu yatır", "sleep.skipping_night": "Bu gecə boyunca yatılır", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Naməlum rəng '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Ərazi/<PERSON><PERSON><PERSON>", "soundCategory.block": "Bloklar", "soundCategory.hostile": "<PERSON>üş<PERSON><PERSON><PERSON>", "soundCategory.master": "<PERSON>", "soundCategory.music": "<PERSON><PERSON><PERSON>", "soundCategory.neutral": "<PERSON><PERSON>", "soundCategory.player": "Oyunçular", "soundCategory.record": "Musiqi/Not Bloku", "soundCategory.ui": "UI", "soundCategory.voice": "Q<PERSON><PERSON>ş<PERSON>", "soundCategory.weather": "<PERSON><PERSON>", "spectatorMenu.close": "Menyunu Bağla", "spectatorMenu.next_page": "Növ<PERSON><PERSON><PERSON> səhifə", "spectatorMenu.previous_page": "Əvvəlki səhifə", "spectatorMenu.root.prompt": "Əmr seçmək üçün bir düyməyə basın, əmrdən istifadə etmək üçün təkrar basın.", "spectatorMenu.team_teleport": "Komanda İştirakçısına Teleport Ol", "spectatorMenu.team_teleport.prompt": "Teleport olunacak komanda seç", "spectatorMenu.teleport": "Oyunçuya Teleport Ol", "spectatorMenu.teleport.prompt": "Teleport olunacak bir oyunçu seç", "stat.generalButton": "Ümumi", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Cütləşdirilən <PERSON>", "stat.minecraft.aviate_one_cm": "Qanadüst<PERSON> ilə gedilən məsafə", "stat.minecraft.bell_ring": "<PERSON><PERSON>", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON><PERSON> ilə gedilən məsafə", "stat.minecraft.clean_armor": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.clean_banner": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.clean_shulker_box": "Açılan şalker qutuları", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n <PERSON>ə", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON><PERSON><PERSON> Absorbed", "stat.minecraft.damage_blocked_by_shield": "Qalxan tərəfindən bloklanmış zərbə", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Absorbed)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>lm<PERSON> (müqavimət göstərmişdir)", "stat.minecraft.damage_resisted": "Zə<PERSON><PERSON><PERSON><PERSON> qarşı çıxdı", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.drop": "Buraxılan Əşyalar", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON><PERSON>ən Ke<PERSON>i", "stat.minecraft.enchant_item": "Sehr<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ə", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON>", "stat.minecraft.fly_one_cm": "U<PERSON>ulan <PERSON>", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "At ilə gedilən məsafə", "stat.minecraft.inspect_dispenser": "Axtarılan Atıcılar", "stat.minecraft.inspect_dropper": "Axtarılan Buraxıcılar", "stat.minecraft.inspect_hopper": "Axtarılan Süzgəclər", "stat.minecraft.interact_with_anvil": "Zindan ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON> mü<PERSON>ər", "stat.minecraft.interact_with_blast_furnace": "Blast Ocağı ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "<PERSON>üş<PERSON><PERSON><PERSON> tonqalı ilə təmaslar", "stat.minecraft.interact_with_cartography_table": "Kartoqrafiya cədvəli ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_crafting_table": "Hazırlama Ma<PERSON>ı<PERSON>ü<PERSON>", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_grindstone": "Ham<PERSON><PERSON>a daşı ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_lectern": "Ders ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_loom": "Loom ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_smithing_table": "<PERSON><PERSON><PERSON><PERSON>i masası ilə təmaslar", "stat.minecraft.interact_with_smoker": "<PERSON>qa<PERSON> çəkən ilə qarşılıqlı əlaqə", "stat.minecraft.interact_with_stonecutter": "<PERSON>ş kəsici ilə qarşılıqlı əlaqə", "stat.minecraft.jump": "Tullanmalar", "stat.minecraft.leave_game": "Çıxılan oyunlar", "stat.minecraft.minecart_one_cm": "Vagon ile gedilən məsafə", "stat.minecraft.mob_kills": "Öldürdü<PERSON>ü<PERSON>", "stat.minecraft.open_barrel": "Barel Açıldı", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_enderchest": "Açılan <PERSON>", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON>lan <PERSON>lker <PERSON>ı", "stat.minecraft.pig_one_cm": "<PERSON>uz ilə gedilən məsafə", "stat.minecraft.play_noteblock": "Çalınan Not Blokları", "stat.minecraft.play_record": "<PERSON><PERSON><PERSON>", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "Oyunçu <PERSON>ə<PERSON>", "stat.minecraft.pot_flower": "Saxsıya tikilən bitkilər", "stat.minecraft.raid_trigger": "Basqın Triggerlandı", "stat.minecraft.raid_win": "Basqıncılar kazandı", "stat.minecraft.sleep_in_bed": "Çarpayıda Yatılan Müddət", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "Qa<PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON> ilə gedilən məsafə", "stat.minecraft.swim_one_cm": "Üz<PERSON><PERSON>ən <PERSON>ə", "stat.minecraft.talked_to_villager": "Danışılan Kəndlilər", "stat.minecraft.target_hit": "Hədə<PERSON>ə vuruldu", "stat.minecraft.time_since_death": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_rest": "<PERSON> is<PERSON><PERSON><PERSON><PERSON><PERSON>ən bəri vaxt", "stat.minecraft.total_world_time": "Dünyan<PERSON><PERSON> açıq qalma müddəti", "stat.minecraft.traded_with_villager": "T<PERSON>rət Edilə<PERSON>", "stat.minecraft.trigger_trapped_chest": "Tətiklənən Tələ Sandıqları", "stat.minecraft.tune_noteblock": "Nizamlanan Not Blokları", "stat.minecraft.use_cauldron": "Qazandan Alınan Sular", "stat.minecraft.walk_on_water_one_cm": "Su üzərindəki məsafə", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "Su altında gəzilən məsafə", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON>ə<PERSON><PERSON> qırıq", "stat_type.minecraft.crafted": "Hazırlama Miqdarı", "stat_type.minecraft.dropped": "Buraxmalar", "stat_type.minecraft.killed": "%s %s'i öldürdün", "stat_type.minecraft.killed.none": "%s'i heç <PERSON>", "stat_type.minecraft.killed_by": "%s səni %s dəfə öldürdü", "stat_type.minecraft.killed_by.none": "%s səni heç <PERSON>", "stat_type.minecraft.mined": "Qazma Miqdarı", "stat_type.minecraft.picked_up": "<PERSON><PERSON>", "stat_type.minecraft.used": "İstifadə miqdarı", "stats.none": "-", "structure_block.button.detect_size": "MÜƏYYƏN ET", "structure_block.button.load": "YÜKLƏ", "structure_block.button.save": "SAXLA", "structure_block.custom_data": "Özəl Məlumat Etiketi Adı", "structure_block.detect_size": "Yapının ö<PERSON>çüsünü ve pozisyonunu tespit et:", "structure_block.hover.corner": "Köşə: %s", "structure_block.hover.data": "Məlumat: %s", "structure_block.hover.load": "Yüklə: %s", "structure_block.hover.save": "<PERSON>eyd <PERSON>t: %s", "structure_block.include_entities": "Varlıkları daxil et:", "structure_block.integrity": "Struktur Bütövlüyü və Dünya Yaradıcısı Kodu", "structure_block.integrity.integrity": "Struk<PERSON> bütövlüyü", "structure_block.integrity.seed": "Struktur kodu", "structure_block.invalid_structure_name": "Keçərsiz struktur adı '%s'", "structure_block.load_not_found": "Struktur '%s' möv<PERSON>d deyil", "structure_block.load_prepare": "Struktur '%s' üçün yer hazırlandı", "structure_block.load_success": "'%s''den struktur yükləndi", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "Mə<PERSON>at", "structure_block.mode.load": "Yükləmə", "structure_block.mode.save": "<PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Künc modu - Yerl<PERSON>şdirmə və ölçü markeri", "structure_block.mode_info.data": "<PERSON><PERSON><PERSON><PERSON> modu - <PERSON><PERSON> məntiqi markeri", "structure_block.mode_info.load": "Yükləmə modu - <PERSON>ldan yüklə", "structure_block.mode_info.save": "<PERSON><PERSON><PERSON> modu - <PERSON><PERSON> yaz", "structure_block.position": "Nisbi Yer", "structure_block.position.x": "nisbi vəziyyət x", "structure_block.position.y": "nisbi vəziyyət y", "structure_block.position.z": "nisbi vəziyyət z", "structure_block.save_failure": "Struktur '%s' qeyd edilə bilmədi", "structure_block.save_success": "Struktur '%s' o<PERSON><PERSON> qeyd edildi", "structure_block.show_air": "Görün<PERSON><PERSON>z blokları göstər:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qutusunu göstər:", "structure_block.size": "Struktur <PERSON>ü", "structure_block.size.x": "struktur ölçüsü x", "structure_block.size.y": "struktur ölçüsü y", "structure_block.size.z": "struktur ölçüsü z", "structure_block.size_failure": "Struktur ölçüsünü müəyyən etmək mümkün deyil. Uyğun gələn struktur adlarına malik künclər əlavə edin", "structure_block.size_success": "'%s' üçün ölçü müvəffəqiyyətlə təsbit edildi", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Struktur Adı", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON>ci səs", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "Ametist c<PERSON><PERSON><PERSON> salır", "subtitles.block.amethyst_block.resonate": "Ametist rezonans verir", "subtitles.block.anvil.destroy": "Dəmirçi Zindanı qırıldı", "subtitles.block.anvil.land": "Dəmirçi Zindanı düşdü", "subtitles.block.anvil.use": "Dəmirçi Zindanından istifadə edildi", "subtitles.block.barrel.close": "Çəllək bağlanır", "subtitles.block.barrel.open": "Çəllək açılır", "subtitles.block.beacon.activate": "Mayak aktivləşir", "subtitles.block.beacon.ambient": "Mayak uğuldayır", "subtitles.block.beacon.deactivate": "<PERSON><PERSON>v<PERSON>əş<PERSON>", "subtitles.block.beacon.power_select": "Mayak gücü seçildi", "subtitles.block.beehive.drip": "<PERSON><PERSON>", "subtitles.block.beehive.enter": "Arı yuvaya daxil olur", "subtitles.block.beehive.exit": "<PERSON><PERSON>ı yuvadan çı<PERSON>ı<PERSON>", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON> kəsir", "subtitles.block.beehive.work": "<PERSON><PERSON><PERSON><PERSON> işləyir", "subtitles.block.bell.resonate": "Zəng rezonans verir", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Damcıyarpaq a<PERSON>ağı əyilir", "subtitles.block.big_dripleaf.tilt_up": "Damcıyarpaq yuxarı qalxır", "subtitles.block.blastfurnace.fire_crackle": "<PERSON>ə<PERSON><PERSON>n ocağı çartıldayır", "subtitles.block.brewing_stand.brew": "<PERSON>ksir <PERSON>ay<PERSON>ı<PERSON>", "subtitles.block.bubble_column.bubble_pop": "Köpü<PERSON><PERSON><PERSON><PERSON>layı<PERSON>", "subtitles.block.bubble_column.upwards_ambient": "Köpüklər üzür", "subtitles.block.bubble_column.upwards_inside": "Köpüklər vıyıldayır", "subtitles.block.bubble_column.whirlpool_ambient": "Köpü<PERSON><PERSON><PERSON><PERSON> fırlanır", "subtitles.block.bubble_column.whirlpool_inside": "Köpük<PERSON><PERSON><PERSON> uğuldayır", "subtitles.block.button.click": "Düymə<PERSON>ə basılır", "subtitles.block.cake.add_candle": "<PERSON><PERSON>", "subtitles.block.campfire.crackle": "Düşərg<PERSON> tonqalı çartıldayır", "subtitles.block.candle.crackle": "<PERSON><PERSON>", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON> ba<PERSON>ı", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON> a<PERSON>ı<PERSON>ı", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>üdü", "subtitles.block.comparator.click": "Qarşılaşdırıcı nizamlandı", "subtitles.block.composter.empty": "Komposter boşaldıldı", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON> doldu", "subtitles.block.composter.ready": "Komposter kompostləşdirir", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON><PERSON><PERSON> aktivləş<PERSON>", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON>ə<PERSON><PERSON><PERSON> hücum edir", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Copper Bulb turns off", "subtitles.block.copper_bulb.turn_on": "Copper Bulb turns on", "subtitles.block.copper_trapdoor.close": "Trapdoor closes", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter fails crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON> qırılır", "subtitles.block.dispenser.dispense": "Əşya atıldı", "subtitles.block.dispenser.fail": "Atıcı müvəffəqiyyətsiz oldu", "subtitles.block.door.toggle": "Qapı cırıldayır", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Sehr masası istifadə edildi", "subtitles.block.end_portal.spawn": "End portalı açılır", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON> q<PERSON>", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON> qapısı cırıldayır", "subtitles.block.fire.ambient": "<PERSON>ov <PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Çömç<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ır", "subtitles.block.furnace.fire_crackle": "Soba çartıldayır", "subtitles.block.generic.break": "Blok qırıldı", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON> sə<PERSON>", "subtitles.block.generic.hit": "Blok qırılır", "subtitles.block.generic.place": "Blok yerləşdirildi", "subtitles.block.grindstone.use": "Bülöv daşı istifadə olundu", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> k<PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.honey_block.slide": "Bal blokundan sürüşülür", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> Qapısı bağlanıldı", "subtitles.block.iron_trapdoor.open": "Tə<PERSON>ə Qapısı açıldı", "subtitles.block.lava.ambient": "<PERSON><PERSON> qay<PERSON>ır", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON>", "subtitles.block.note_block.note": "Not bloku çalır", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "<PERSON>ston hərəkət edir", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON>va damcılayır", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava qazana damcılayır", "subtitles.block.pointed_dripstone.drip_water": "Su damcılayır", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Su qazana damcılayır", "subtitles.block.pointed_dripstone.land": "Stalaktit aşağı düşür", "subtitles.block.portal.ambient": "Keçid vızıldır", "subtitles.block.portal.travel": "Portal səsi azalır", "subtitles.block.portal.trigger": "Portal səsi güclənir", "subtitles.block.pressure_plate.click": "<PERSON>ə<PERSON><PERSON> təbəqə<PERSON>ə basılır", "subtitles.block.pumpkin.carve": "Qayçı oyur", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.respawn_anchor.ambient": "Keçid vızıldır", "subtitles.block.respawn_anchor.charge": "Respaun ankeri yü<PERSON>", "subtitles.block.respawn_anchor.deplete": "Respaun ankeri boşalır", "subtitles.block.respawn_anchor.set_spawn": "<PERSON><PERSON><PERSON><PERSON> ankeri spaunu qurur", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Sculk baloncukları", "subtitles.block.sculk.spread": "Skulk yayılır", "subtitles.block.sculk_catalyst.bloom": "Sculk Catalist çiçək açır", "subtitles.block.sculk_sensor.clicking": "Skalk sensoru şı<PERSON>ıldamağa başlayır", "subtitles.block.sculk_sensor.clicking_stop": "Skalk sensoru ş<PERSON>ı dayandırır", "subtitles.block.sculk_shrieker.shriek": "Sculk Shrieker qışqırır", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> a<PERSON>ıld<PERSON>", "subtitles.block.sign.waxed_interact_fail": "İşarə yırğalanır", "subtitles.block.smithing_table.use": "Dəmirçi masası istifadə edildi", "subtitles.block.smoker.smoke": "Tüstü sobası", "subtitles.block.sniffer_egg.crack": "Sniffer Egg cracks", "subtitles.block.sniffer_egg.hatch": "Sniffer Yumurta lyukları", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "<PERSON><PERSON><PERSON><PERSON><PERSON> ə<PERSON>", "subtitles.block.sweet_berry_bush.pick_berries": "Giləmeyvə açılır", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "Tə<PERSON>ə qapısı cırıldayır", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Trial Spawner closes", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Trial Spawner opens", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Trial Spawner spawns a mob", "subtitles.block.tripwire.attach": "Tələ ipi qoşulur", "subtitles.block.tripwire.click": "Tələ ipi işə düşür", "subtitles.block.tripwire.detach": "Tələ ipi açılır", "subtitles.block.vault.activate": "Vault ignites", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Vault closes", "subtitles.block.vault.deactivate": "<PERSON>ault extinguishes", "subtitles.block.vault.eject_item": "Vault ejects item", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Vault opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "Su axır", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "Büyülü kitap yerleştirildi", "subtitles.chiseled_bookshelf.take": "Kitab götür<PERSON>üb", "subtitles.chiseled_bookshelf.take_enchanted": "Büyülü kitap alındı", "subtitles.enchant.thorns.hit": "Tikanlar batdı", "subtitles.entity.allay.ambient_with_item": "<PERSON>ay a<PERSON>", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON>", "subtitles.entity.allay.death": "<PERSON><PERSON>", "subtitles.entity.allay.hurt": "Allay ağ<PERSON>ı<PERSON>ı<PERSON>", "subtitles.entity.allay.item_given": "<PERSON>ay chortles", "subtitles.entity.allay.item_taken": "<PERSON>ay rəhmət elə<PERSON>", "subtitles.entity.allay.item_thrown": "Allay atır", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> grunts", "subtitles.entity.armadillo.brush": "<PERSON>ute is brushed off", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "Armadillo lands", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "Armadillo rolls up", "subtitles.entity.armadillo.scute_drop": "Armadillo sheds scute", "subtitles.entity.armadillo.unroll_finish": "Armadillo unrolls", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "Bir <PERSON><PERSON> düş<PERSON>ü", "subtitles.entity.arrow.hit": "<PERSON>x isabət etdi", "subtitles.entity.arrow.hit_player": "Oyunçu vuruldu", "subtitles.entity.arrow.shoot": "Ox atıldı", "subtitles.entity.axolotl.attack": "Aksolotl hücum edir", "subtitles.entity.axolotl.death": "<PERSON>ks<PERSON><PERSON>", "subtitles.entity.axolotl.hurt": "Aksolotl xəsarət alır", "subtitles.entity.axolotl.idle_air": "Aksolotl cikkildəyir", "subtitles.entity.axolotl.idle_water": "Aksolotl cikkildəyir", "subtitles.entity.axolotl.splash": "Aksolotl <PERSON>ıldayır", "subtitles.entity.axolotl.swim": "Aksolotl üzür", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.ambient": "Arı vızıldayır", "subtitles.entity.bee.death": "Arı ölür", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON>ır", "subtitles.entity.bee.loop": "Arı vızıldayır", "subtitles.entity.bee.loop_aggressive": "Arı əsəbi şəkildə vızıldayır", "subtitles.entity.bee.pollinate": "Arı xoşbəxtliklə vızıldayır", "subtitles.entity.bee.sting": "<PERSON>rı sancır", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON> nəfəs alır", "subtitles.entity.blaze.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON> atəş edir", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON><PERSON><PERSON>ə<PERSON>", "subtitles.entity.boat.paddle_water": "Avarçəkmə", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "Breeze charges", "subtitles.entity.breeze.death": "<PERSON><PERSON> dies", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> deflects", "subtitles.entity.breeze.hurt": "Breeze hurts", "subtitles.entity.breeze.idle_air": "Breeze flies", "subtitles.entity.breeze.idle_ground": "Breeze whirs", "subtitles.entity.breeze.inhale": "Breeze inhales", "subtitles.entity.breeze.jump": "<PERSON><PERSON> jumps", "subtitles.entity.breeze.land": "Breeze lands", "subtitles.entity.breeze.shoot": "Blaze atəş edir", "subtitles.entity.breeze.slide": "Breeze slides", "subtitles.entity.breeze.whirl": "Breeze whirls", "subtitles.entity.breeze.wind_burst": "Wind Charge bursts", "subtitles.entity.camel.ambient": "<PERSON>ə<PERSON>ə hönkür<PERSON>r", "subtitles.entity.camel.dash": "Dəvə yeets", "subtitles.entity.camel.dash_ready": "Dəvə yeets", "subtitles.entity.camel.death": "<PERSON>əvə ölür", "subtitles.entity.camel.eat": "<PERSON>ə<PERSON>ə yeyir", "subtitles.entity.camel.hurt": "Dəvə ağrıyır", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON>əvə oturur", "subtitles.entity.camel.stand": "<PERSON>əvə ayağa qalxır", "subtitles.entity.camel.step": "<PERSON>əvə addımları", "subtitles.entity.camel.step_sand": "<PERSON>əvə qumları", "subtitles.entity.cat.ambient": "Pişik miyavladı", "subtitles.entity.cat.beg_for_food": "Pişik ağız açır", "subtitles.entity.cat.death": "Pişik öldü", "subtitles.entity.cat.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.cat.hiss": "Pişik fısıldayır", "subtitles.entity.cat.hurt": "Pişik xəsarət aldı", "subtitles.entity.cat.purr": "<PERSON>şik mırıldayır", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON> gı<PERSON>ı", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> yum<PERSON>ladı", "subtitles.entity.chicken.hurt": "<PERSON><PERSON>q xəsarət aldı", "subtitles.entity.cod.death": "Treska ölür", "subtitles.entity.cod.flop": "Treska şappıldayır", "subtitles.entity.cod.hurt": "Treska xəsarət alır", "subtitles.entity.cow.ambient": "İnək böyürür", "subtitles.entity.cow.death": "İnək öldü", "subtitles.entity.cow.hurt": "İnək xəsarət aldı", "subtitles.entity.cow.milk": "İnək sağıldı", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "Creeper öldü", "subtitles.entity.creeper.hurt": "Creeper xəsarət aldı", "subtitles.entity.creeper.primed": "Creeper partlayır", "subtitles.entity.dolphin.ambient": "Delfin civildəyir", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON> fit çalır", "subtitles.entity.dolphin.attack": "Delfin hücum edir", "subtitles.entity.dolphin.death": "<PERSON><PERSON>", "subtitles.entity.dolphin.eat": "<PERSON><PERSON>", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON>anır", "subtitles.entity.dolphin.jump": "<PERSON><PERSON> tullanır", "subtitles.entity.dolphin.play": "<PERSON><PERSON>", "subtitles.entity.dolphin.splash": "Delfin sıçrayır", "subtitles.entity.dolphin.swim": "Delfin <PERSON>ür", "subtitles.entity.donkey.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.angry": "U<PERSON>n<PERSON><PERSON>q k<PERSON>r", "subtitles.entity.donkey.chest": "U<PERSON>nq<PERSON><PERSON>şdirili<PERSON>", "subtitles.entity.donkey.death": "U<PERSON>nq<PERSON><PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.hurt": "Uzunqulaq xəsarət aldı", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Boğulmuş quruldayır", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "Boğulmuş ölür", "subtitles.entity.drowned.hurt": "Boğulmuş xəsarət alır", "subtitles.entity.drowned.shoot": "Boğulmuş üçbaşlı əsa atır", "subtitles.entity.drowned.step": "Boğulmuş addımlayır", "subtitles.entity.drowned.swim": "Boğulmuş üzür", "subtitles.entity.egg.throw": "Yumurta atıldı", "subtitles.entity.elder_guardian.ambient": "Yaşlı Mühafizəçi inlir", "subtitles.entity.elder_guardian.ambient_land": "Yaşlı mühafizəçi çapalayır", "subtitles.entity.elder_guardian.curse": "Yaşlı mühafizəçi lənətləyir", "subtitles.entity.elder_guardian.death": "Yaşlı Mühafizəçi öldü", "subtitles.entity.elder_guardian.flop": "Yaşlı mühafizəçi şappıldayır", "subtitles.entity.elder_guardian.hurt": "Yaşlı Mühafizəçi xəsarət aldı", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON>a qanad çırpdı", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON><PERSON> xəsarət aldı", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON><PERSON> atə<PERSON> etdi", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> atıldı", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> <PERSON><PERSON>ıldı", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON> vu<PERSON><PERSON>r", "subtitles.entity.enderman.death": "<PERSON><PERSON> öldü", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> xəsarət aldı", "subtitles.entity.enderman.scream": "<PERSON><PERSON> qışqı<PERSON><PERSON>r", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON> qışqı<PERSON><PERSON><PERSON> atır", "subtitles.entity.enderman.teleport": "Enderman teleport oldu", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON> qaçır", "subtitles.entity.endermite.death": "Endermite öldü", "subtitles.entity.endermite.hurt": "Endermite xəsarət aldı", "subtitles.entity.evoker.ambient": "Oyadıcı donquldanır", "subtitles.entity.evoker.cast_spell": "Oyadıcı sehr edir", "subtitles.entity.evoker.celebrate": "Oyadıcı sevinir", "subtitles.entity.evoker.death": "Oyadıcı ölür", "subtitles.entity.evoker.hurt": "Oyadıcı xəsarət alır", "subtitles.entity.evoker.prepare_attack": "Oyadıcı hücuma hazırlaşır", "subtitles.entity.evoker.prepare_summon": "Oyadıcı çağırmağa hazırlaşır", "subtitles.entity.evoker.prepare_wololo": "Oyadıcı sehrləməyə hazırlaşır", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> qapır", "subtitles.entity.experience_orb.pickup": "Tə<PERSON>r<PERSON><PERSON>ə əldə edildi", "subtitles.entity.firework_rocket.blast": "Fişəng partladı", "subtitles.entity.firework_rocket.launch": "Fişəng atıldı", "subtitles.entity.firework_rocket.twinkle": "Fişəng pırıldayır", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON> geri <PERSON>i", "subtitles.entity.fishing_bobber.splash": "Tilov şappıldayır", "subtitles.entity.fishing_bobber.throw": "Tilov atıldı", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON><PERSON> qəzəblənir", "subtitles.entity.fox.ambient": "Tülkü cığıldayır", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON><PERSON> dişləyir", "subtitles.entity.fox.death": "Tülkü ölür", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON>lkü xəsarət alır", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON><PERSON> ulayır", "subtitles.entity.fox.sleep": "Tülkü xoruldayır", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>əyi<PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON><PERSON> tüpür<PERSON>r", "subtitles.entity.fox.teleport": "Tülkü teleport olur", "subtitles.entity.frog.ambient": "Qur<PERSON><PERSON><PERSON> quru<PERSON>ır", "subtitles.entity.frog.death": "Qurbağa öldü", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.frog.hurt": "Qurbağa xəsarət aldı", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON><PERSON><PERSON> yumurta qoyur", "subtitles.entity.frog.long_jump": "Qurbağa hoppanır", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "<PERSON><PERSON>", "subtitles.entity.generic.death": "Ö<PERSON>ə", "subtitles.entity.generic.drink": "İçmə", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Partlama", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "Bir şey xəsarət aldı", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>", "subtitles.entity.generic.splash": "Şapırdama", "subtitles.entity.generic.swim": "Üzmə", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>ı<PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.hurt": "G<PERSON>t xəsarət aldı", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>di", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Parlayan əşya çərçivəsi doldurulur", "subtitles.entity.glow_item_frame.break": "Parlayan əşya çərçivəsi sınır", "subtitles.entity.glow_item_frame.place": "Parlayan əşya çərçivəsi yerləşdirildi", "subtitles.entity.glow_item_frame.remove_item": "Parlayan əşya çə<PERSON><PERSON><PERSON><PERSON><PERSON> boşaldılır", "subtitles.entity.glow_item_frame.rotate_item": "Parlayan əşya çərçivəsi çevrilir", "subtitles.entity.glow_squid.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> osm<PERSON>q <PERSON>", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> osminoq xəsarət alır", "subtitles.entity.glow_squid.squirt": "<PERSON><PERSON><PERSON><PERSON><PERSON> osm<PERSON><PERSON> mü<PERSON>ə<PERSON><PERSON><PERSON> bura<PERSON>ır", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> mələ<PERSON>r", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON>ü<PERSON>", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.horn_break": "Keçi Buynuzu qırıldı", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> xəsarət alır", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.goat.milk": "<PERSON><PERSON>i sağılır", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON> tə<PERSON>ir", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> addımlayır", "subtitles.entity.guardian.ambient": "Mühavizəçi inlir", "subtitles.entity.guardian.ambient_land": "Mühafizəçi çapalayır", "subtitles.entity.guardian.attack": "Mühavizəçi atəş edir", "subtitles.entity.guardian.death": "Mühavizəçi öldü", "subtitles.entity.guardian.flop": "Mühafizəçi şappıldayır", "subtitles.entity.guardian.hurt": "Mühavizəçi xəsarət aldı", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> qəzəblə donquldanır", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> hü<PERSON> edir", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> zoqlinə çevrilir", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> xə<PERSON><PERSON>t alır", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.ambient": "At kişnəyir", "subtitles.entity.horse.angry": "At kişnəyir", "subtitles.entity.horse.armor": "At zirehi yerləşdirilir", "subtitles.entity.horse.breathe": "At nəfəs alır", "subtitles.entity.horse.death": "At öldü", "subtitles.entity.horse.eat": "At yedi", "subtitles.entity.horse.gallop": "At dördnala qaçır", "subtitles.entity.horse.hurt": "At xəsarət aldı", "subtitles.entity.horse.jump": "At tullandı", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.husk.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.converted_to_zombie": "Hask zombiyə çevrilir", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> xəsarət aldı", "subtitles.entity.illusioner.ambient": "İllüziyaçı donquldanır", "subtitles.entity.illusioner.cast_spell": "İllüziyaçı sehr edir", "subtitles.entity.illusioner.death": "İllüziyaçı ölür", "subtitles.entity.illusioner.hurt": "İllüziyaçı xəsarət alır", "subtitles.entity.illusioner.mirror_move": "İllüziyaçı yer dəyişir", "subtitles.entity.illusioner.prepare_blindness": "İllüziyaçı korluq hazırlayır", "subtitles.entity.illusioner.prepare_mirror": "İllüziyaçı güzgü görüntüsünü hazırlayır", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON><PERSON> hü<PERSON> etdi", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON><PERSON> q<PERSON> sı<PERSON>ır", "subtitles.entity.iron_golem.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.iron_golem.hurt": "Dəmir Golem xəsarət aldı", "subtitles.entity.iron_golem.repair": "<PERSON><PERSON><PERSON> qolem təmir o<PERSON>u", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON> qırıldı", "subtitles.entity.item.pickup": "<PERSON><PERSON>ya alındı", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON>çivə<PERSON> dolduruldu", "subtitles.entity.item_frame.break": "Əşya <PERSON>ərç<PERSON>ə<PERSON> qırıldı", "subtitles.entity.item_frame.place": "<PERSON><PERSON>ya <PERSON>ç<PERSON> yer<PERSON>şdiril<PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON>şya <PERSON>ərç<PERSON>ə<PERSON> boşaldıldı", "subtitles.entity.item_frame.rotate_item": "<PERSON>şya Çərçivəsindəki əşya çevrildi", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> düğümü kopar", "subtitles.entity.leash_knot.place": "Kement düğümü bağlandı", "subtitles.entity.lightning_bolt.impact": "İldırım düşdü", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON> qəzəblə mələyir", "subtitles.entity.llama.chest": "<PERSON> <PERSON><PERSON>", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON> ye<PERSON>", "subtitles.entity.llama.hurt": "<PERSON> xə<PERSON><PERSON><PERSON> aldı", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Magma Kubu öldü", "subtitles.entity.magma_cube.hurt": "Magma Kubu xəsarət aldı", "subtitles.entity.magma_cube.squish": "Magma Kubu düşdü", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON> gedir", "subtitles.entity.mooshroom.convert": "Muuşrum çevrilir", "subtitles.entity.mooshroom.eat": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mooshroom.milk": "Muuşrum sağılır", "subtitles.entity.mooshroom.suspicious_milk": "Muuşrum şübhəli şəkildə sağılır", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON>r k<PERSON><PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON><PERSON>şdirili<PERSON>", "subtitles.entity.mule.death": "Qatır öldü", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.hurt": "Qat<PERSON>r xəsarət aldı", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON> qırıldı", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda əsəbləşir", "subtitles.entity.panda.ambient": "Panda tövşüyür", "subtitles.entity.panda.bite": "Panda <PERSON>şləyi<PERSON>", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> b<PERSON>ü<PERSON><PERSON><PERSON>", "subtitles.entity.panda.death": "Panda ölür", "subtitles.entity.panda.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.panda.hurt": "Panda xəsarət alır", "subtitles.entity.panda.pre_sneeze": "Pandanın burnu qaşınır", "subtitles.entity.panda.sneeze": "Panda asqırır", "subtitles.entity.panda.step": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.worried_ambient": "Panda inildəyir", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.hurts": "Tu<PERSON>q<PERSON><PERSON><PERSON> xəsarət alır", "subtitles.entity.parrot.imitate.blaze": "Tu<PERSON>q<PERSON><PERSON><PERSON> nəfəs alır", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "Parrot whirs", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "Tu<PERSON><PERSON><PERSON><PERSON><PERSON> kriperi ya<PERSON>ı<PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> quru<PERSON>r", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "Tu<PERSON><PERSON><PERSON><PERSON><PERSON> əjd<PERSON><PERSON>ı ya<PERSON>ı<PERSON>ır", "subtitles.entity.parrot.imitate.endermite": "Tu<PERSON>q<PERSON><PERSON><PERSON> ender<PERSON><PERSON>", "subtitles.entity.parrot.imitate.evoker": "Tutuquşu çağırıcını yamsılayır", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "Tutuquşu magma kubunu təqlid edir", "subtitles.entity.parrot.imitate.phantom": "Tutuquşu ovucları", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ovsunçuyu təqlid edir", "subtitles.entity.parrot.imitate.ravager": "Tutuq<PERSON>şu zombi donuz adamı təqlid edir", "subtitles.entity.parrot.imitate.shulker": "Tutuq<PERSON><PERSON>u <PERSON> ya<PERSON>ılayı<PERSON>", "subtitles.entity.parrot.imitate.silverfish": "Tu<PERSON><PERSON><PERSON><PERSON><PERSON> gü<PERSON><PERSON> böcəyini yamsılayır", "subtitles.entity.parrot.imitate.skeleton": "Tutuquşu skeleti təqlid edir", "subtitles.entity.parrot.imitate.slime": "Tutuquşu magma kubunu təqlid edir", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hö<PERSON>ü<PERSON>çəyi təqlid edir", "subtitles.entity.parrot.imitate.stray": "Tutuquşu Axmağı təqlid edir", "subtitles.entity.parrot.imitate.vex": "Tutuqu<PERSON>u zəhlətökəni təqlid edir", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cadugəri təqlid edir", "subtitles.entity.parrot.imitate.wither": "Tutuqu<PERSON>u witherı təqlid edir", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "Tutuquşu civildədi", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zombini təqlid edir", "subtitles.entity.parrot.imitate.zombie_villager": "Tu<PERSON><PERSON><PERSON><PERSON><PERSON> təqlid edir", "subtitles.entity.phantom.ambient": "<PERSON><PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON>", "subtitles.entity.phantom.death": "<PERSON><PERSON>", "subtitles.entity.phantom.flap": "Fantom q<PERSON>d <PERSON>", "subtitles.entity.phantom.hurt": "<PERSON><PERSON>", "subtitles.entity.phantom.swoop": "Fantom şığıyır", "subtitles.entity.pig.ambient": "<PERSON>uz o<PERSON>li<PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON>", "subtitles.entity.pig.hurt": "Donuz xəsarət aldı", "subtitles.entity.pig.saddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON><PERSON> əşyanı bəyənir", "subtitles.entity.piglin.ambient": "<PERSON><PERSON><PERSON> fınxı<PERSON>ır", "subtitles.entity.piglin.angry": "<PERSON><PERSON><PERSON> qəzəblə fınxırır", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON><PERSON> se<PERSON>", "subtitles.entity.piglin.converted_to_zombified": "Pi<PERSON>lin zombiləşmiş piqlinə çevrilir", "subtitles.entity.piglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON><PERSON> həsədlə fınxırır", "subtitles.entity.piglin.retreat": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.ambient": "<PERSON>lin Brute qulaq asır", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON> qəzəblə fınxırır", "subtitles.entity.piglin_brute.converted_to_zombified": "Pi<PERSON>lin zombiləşmiş piqlinə çevrilir", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON> se<PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON>ı<PERSON>", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "İ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hü<PERSON>", "subtitles.entity.player.attack.strong": "<PERSON><PERSON><PERSON><PERSON><PERSON> hü<PERSON>", "subtitles.entity.player.attack.sweep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hü<PERSON>", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Oyunçu öldü", "subtitles.entity.player.freeze_hurt": "Oyunçu donur", "subtitles.entity.player.hurt": "Oyunçu xəsarət aldı", "subtitles.entity.player.hurt_drown": "Oyunçu boğulur", "subtitles.entity.player.hurt_on_fire": "Oyunçu yanır", "subtitles.entity.player.levelup": "Oyunçu səviyyə atladı", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "Qütb Ayısı homurdır", "subtitles.entity.polar_bear.ambient_baby": "Ya<PERSON>ru <PERSON>tb Ayısı homurdır", "subtitles.entity.polar_bear.death": "Qütb Ayısı öldü", "subtitles.entity.polar_bear.hurt": "Qütb Ayısı xəsarət aldı", "subtitles.entity.polar_bear.warning": "Qütb Ayısı kükrədi", "subtitles.entity.potion.splash": "Butulka çarpdı", "subtitles.entity.potion.throw": "Butulka atıldı", "subtitles.entity.puffer_fish.blow_out": "Kirpi balığı havasını boşaldır", "subtitles.entity.puffer_fish.blow_up": "Kirpi balığı şişir", "subtitles.entity.puffer_fish.death": "Kirpi balığı ölür", "subtitles.entity.puffer_fish.flop": "Kirpi balığı şappıldayır", "subtitles.entity.puffer_fish.hurt": "Kirpi balığı xəsarət alır", "subtitles.entity.puffer_fish.sting": "Kirpi balığı sancır", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı<PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON><PERSON> hü<PERSON> etdi", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.hurt": "Dovş<PERSON> xəsarət aldı", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON><PERSON> tull<PERSON>ı", "subtitles.entity.ravager.ambient": "Dağıdıcı donquldanır", "subtitles.entity.ravager.attack": "Dağıdıcı dişləyir", "subtitles.entity.ravager.celebrate": "Dağıdıcı sevinir", "subtitles.entity.ravager.death": "Dağıdıcı ölür", "subtitles.entity.ravager.hurt": "Dağıdıcı xəsarət alır", "subtitles.entity.ravager.roar": "Dağıdıcı nərildəyir", "subtitles.entity.ravager.step": "Dağıdıcı yer<PERSON>yir", "subtitles.entity.ravager.stunned": "Dağıdıcı sarsıdır", "subtitles.entity.salmon.death": "Qızılbal<PERSON>q <PERSON>ü<PERSON>", "subtitles.entity.salmon.flop": "Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.salmon.hurt": "<PERSON> xəsarət alır", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON> mələ<PERSON>r", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON> xəsarət aldı", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> b<PERSON>ğlanır", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> xəsarət aldı", "subtitles.entity.shulker.open": "Şalker açılır", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> etdi", "subtitles.entity.shulker.teleport": "<PERSON>lker teleport oldu", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> gü<PERSON><PERSON><PERSON> partlayı<PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> sınır", "subtitles.entity.silverfish.ambient": "G<PERSON><PERSON><PERSON>ş böcəyi fısıldayır", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.hurt": "Gümüşcün xəsarət aldı", "subtitles.entity.skeleton.ambient": "Skelet takırdır", "subtitles.entity.skeleton.converted_to_stray": "Skelet Streyə çevrilir", "subtitles.entity.skeleton.death": "Skelet öldü", "subtitles.entity.skeleton.hurt": "Skelet xəsarət aldı", "subtitles.entity.skeleton.shoot": "Skelet atəş etdi", "subtitles.entity.skeleton_horse.ambient": "Skelet At ağlayır", "subtitles.entity.skeleton_horse.death": "Skelet At öldü", "subtitles.entity.skeleton_horse.hurt": "Skelet At xəsarət aldı", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Skelet at üzür", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> hücum etdi", "subtitles.entity.slime.death": "Palçıq <PERSON>", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON>q xəsarət aldı", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON>ffer ayağa qalxır", "subtitles.entity.sniffer.drop_seed": "Sniffer toxum buraxır", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Yumurta lyukları", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> ləzzət verir", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> qoxular", "subtitles.entity.sniffer.searching": "Sniffer axtarışları", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.hurt": "Qar Golemi xəsarət aldı", "subtitles.entity.snowball.throw": "Qar Topu atıldı", "subtitles.entity.spider.ambient": "Hörümçək fısıldayır", "subtitles.entity.spider.death": "Hörümçək öldü", "subtitles.entity.spider.hurt": "Hörümçək xəsarət aldı", "subtitles.entity.squid.ambient": "Mürəkkəb Balığı üzür", "subtitles.entity.squid.death": "Mürəkkəb Balığı öldü", "subtitles.entity.squid.hurt": "Mürəkkəb Balığı xəsarət aldı", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON> mürə<PERSON><PERSON><PERSON><PERSON> saçır", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> xəsarət aldı", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> xə<PERSON>t alır", "subtitles.entity.strider.idle": "Strayder vığıldayır", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.death": "Çömçəquyruq ölür", "subtitles.entity.tadpole.flop": "Çömçəquyruq üzür", "subtitles.entity.tadpole.grow_up": "Tadpole böyüyür", "subtitles.entity.tadpole.hurt": "Çömçəquyruq xəsarət aldı", "subtitles.entity.tnt.primed": "TNT fitili yanır", "subtitles.entity.tropical_fish.death": "Tropik balıq <PERSON>ü<PERSON>", "subtitles.entity.tropical_fish.flop": "Tropik balıq <PERSON>", "subtitles.entity.tropical_fish.hurt": "Tropik balıq xəsarət alır", "subtitles.entity.turtle.ambient_land": "Tısbağa cırıldayır", "subtitles.entity.turtle.death": "Tısbağa ölür", "subtitles.entity.turtle.death_baby": "Tısbağa balası ölür", "subtitles.entity.turtle.egg_break": "Tısbağa yumurtası sınır", "subtitles.entity.turtle.egg_crack": "Tısbağa yumurtası çatlayır", "subtitles.entity.turtle.egg_hatch": "Tısbağa yumurtadan çıxır", "subtitles.entity.turtle.hurt": "Tısbağa xəsarət alır", "subtitles.entity.turtle.hurt_baby": "Tısbağa balası xəsarət alır", "subtitles.entity.turtle.lay_egg": "Tısbağa yumurta qoyur", "subtitles.entity.turtle.shamble": "Tısbağa astaca yer<PERSON>yir", "subtitles.entity.turtle.shamble_baby": "Tısbağa balası astaca yeriyir", "subtitles.entity.turtle.swim": "Tısbağa üzür", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>ökən əsəb pozur", "subtitles.entity.vex.charge": "Z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n qışqırır", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON>ətökən ölür", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kən xəsarət alır", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>nı<PERSON>", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ir", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON><PERSON> ö<PERSON>", "subtitles.entity.villager.hurt": "<PERSON>ə<PERSON><PERSON> xəsarət aldı", "subtitles.entity.villager.no": "Kə<PERSON><PERSON> razılaşmır", "subtitles.entity.villager.trade": "<PERSON>ə<PERSON><PERSON> ticarət edir", "subtitles.entity.villager.work_armorer": "Zirehçi işləyir", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> işləyi<PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fisherman": "Balıqçı işləyir", "subtitles.entity.villager.work_fletcher": "Oxçu işləyir", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> <PERSON> i<PERSON>", "subtitles.entity.villager.work_librarian": "Kitabxanaçı işləyir", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_toolsmith": "<PERSON>ət ustası işləyir", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON> ustası işləyir", "subtitles.entity.villager.yes": "<PERSON>ə<PERSON><PERSON> qəbul edir", "subtitles.entity.vindicator.ambient": "İntiqamçı homurdanır", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.death": "İntiqamçı öldü", "subtitles.entity.vindicator.hurt": "İntiqamçı xəsarət aldı", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON> tacir g<PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON> tacir <PERSON>", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON><PERSON><PERSON> tacir yoxa <PERSON>ı<PERSON>ır", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON><PERSON> tacir süd i<PERSON>ir", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON><PERSON> tacir el<PERSON>sir <PERSON>ir", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON><PERSON> tacir xəsarət alır", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON><PERSON> tacir r<PERSON><PERSON>", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON><PERSON> tacir peyda olur", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON><PERSON> tacir ticarət edir", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON> tacir qəbul edir", "subtitles.entity.warden.agitated": "Gözətçi qəzəblə uğuldayır", "subtitles.entity.warden.ambient": "Gözətçi zingildəyir", "subtitles.entity.warden.angry": "Gö<PERSON>ə<PERSON><PERSON><PERSON> qəzəblənir", "subtitles.entity.warden.attack_impact": "Gözətçi yerə zərbə vurur", "subtitles.entity.warden.death": "Gözətçi öldü", "subtitles.entity.warden.dig": "Gözətçi qazır", "subtitles.entity.warden.emerge": "Gözətçi ortaya çıxır", "subtitles.entity.warden.heartbeat": "Müha<PERSON><PERSON><PERSON><PERSON><PERSON>n ürəyi döyünür", "subtitles.entity.warden.hurt": "Gözətçi xəsarət aldı", "subtitles.entity.warden.listening": "Gözə<PERSON>çi hiss edir", "subtitles.entity.warden.listening_angry": "Gö<PERSON>ə<PERSON><PERSON><PERSON> qəzəblə hiss edir", "subtitles.entity.warden.nearby_close": "Gözətçi yaxınlaşır", "subtitles.entity.warden.nearby_closer": "Gözə<PERSON>çi irəliləyir", "subtitles.entity.warden.nearby_closest": "Gözətçi yaxınlaşır", "subtitles.entity.warden.roar": "Gözətçi nərə çəkir", "subtitles.entity.warden.sniff": "Gözətçi iyləyir", "subtitles.entity.warden.sonic_boom": "Mühafizəçi çalır", "subtitles.entity.warden.sonic_charge": "Mühafizəçi ittiham edir", "subtitles.entity.warden.step": "Gözətçi addımlayır", "subtitles.entity.warden.tendril_clicks": "Mühafizə<PERSON><PERSON>n tumurcuqları klikləyir", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> hı<PERSON>ı<PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>ir", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> xəsarət alır", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>r", "subtitles.entity.wither.ambient": "<PERSON><PERSON> hi<PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON>er xə<PERSON><PERSON>t aldı", "subtitles.entity.wither.shoot": "<PERSON><PERSON> hü<PERSON> etdi", "subtitles.entity.wither.spawn": "<PERSON>er sərbəst buraxıldı", "subtitles.entity.wither_skeleton.ambient": "Wither Skeleti takırdır", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON> S<PERSON>eti <PERSON>", "subtitles.entity.wither_skeleton.hurt": "Wither Skeleti xəsarət aldı", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON><PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON> <PERSON>", "subtitles.entity.wolf.hurt": "<PERSON>d xəsar<PERSON>t alır", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "<PERSON>d <PERSON>ə<PERSON>ə<PERSON>", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON><PERSON> qəzəblə donquldanır", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON><PERSON> hücum edir", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON><PERSON> xəsarət alır", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> inlir", "subtitles.entity.zombie.attack_wooden_door": "Qapı silkələnir", "subtitles.entity.zombie.break_wooden_door": "Qapı sınır", "subtitles.entity.zombie.converted_to_drowned": "<PERSON>ombi b<PERSON><PERSON><PERSON><PERSON><PERSON>a çevrilir", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.destroy_egg": "Tısbağa yumurtası əzildi", "subtitles.entity.zombie.hurt": "Zombi xəsarət aldı", "subtitles.entity.zombie.infect": "Kəndli infektdə oldu", "subtitles.entity.zombie_horse.ambient": "Zombi At ağlayır", "subtitles.entity.zombie_horse.death": "Zombi At öldü", "subtitles.entity.zombie_horse.hurt": "Zombi At xəsarət aldı", "subtitles.entity.zombie_villager.ambient": "<PERSON><PERSON><PERSON> in<PERSON>r", "subtitles.entity.zombie_villager.converted": "Zombi kəndli qışqırır", "subtitles.entity.zombie_villager.cure": "Zombi kəndli fısıldayır", "subtitles.entity.zombie_villager.death": "Zombi K<PERSON> ö<PERSON>", "subtitles.entity.zombie_villager.hurt": "Zombi Kəndli xəsarət aldi", "subtitles.entity.zombified_piglin.ambient": "Zombiləşmiş piqlin <PERSON>", "subtitles.entity.zombified_piglin.angry": "Zombified Piglin əsəbiləşir", "subtitles.entity.zombified_piglin.death": "Zombiləşmiş p<PERSON>", "subtitles.entity.zombified_piglin.hurt": "Zombiləşmiş piqlin ya<PERSON>ır", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "Dəhşətli buynuz barmaqları", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "subtitles.item.armor.equip_diamond": "<PERSON>z z<PERSON>h <PERSON>yi<PERSON>", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> guru<PERSON>", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>h c<PERSON>il<PERSON>əyir", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_netherite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_turtle": "Tısbağa çanağı taqqıldayır", "subtitles.item.armor.equip_wolf": "Wolf Armor is fastened", "subtitles.item.armor.unequip_wolf": "Wolf Armor snips away", "subtitles.item.axe.scrape": "Balta qırıntıları", "subtitles.item.axe.strip": "Balta soyur", "subtitles.item.axe.wax_off": "Mum təmizləndi", "subtitles.item.bone_meal.use": "Sümük Unu qatları", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON><PERSON>ə xışıldayır", "subtitles.item.book.put": "Kitab çırpıntıları", "subtitles.item.bottle.empty": "<PERSON>ul<PERSON> boşalır", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.item.brush.brushing.generic": "Fırçalama", "subtitles.item.brush.brushing.gravel": "Çınqılı<PERSON>ı<PERSON>ı", "subtitles.item.brush.brushing.gravel.complete": "Çınqılın fırçalanması tamamlandı", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand.complete": "Fırçalama Qum ta<PERSON>mlandı", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON><PERSON> boşaldılır", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON><PERSON> dolduruldu", "subtitles.item.bucket.fill_axolotl": "Aksolotl yaxalandı", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON><PERSON> tutuldu", "subtitles.item.bucket.fill_tadpole": "Tad<PERSON> tutuldu", "subtitles.item.bundle.drop_contents": "Boğça boşaldılır", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON>şdırılır", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "Əşya çıxarılıq", "subtitles.item.chorus_fruit.teleport": "Oyunçu teleport oldu", "subtitles.item.crop.plant": "<PERSON><PERSON>", "subtitles.item.crossbow.charge": "Arb<PERSON>", "subtitles.item.crossbow.hit": "<PERSON>x isabət etdi", "subtitles.item.crossbow.load": "Arbalet yük<PERSON>", "subtitles.item.crossbow.shoot": "Arbalet atəş açır", "subtitles.item.dye.use": "<PERSON>a əlavə olunur", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Alov Topu atıldı", "subtitles.item.flintandsteel.use": "Çaxmax Daşı və Polad çaxıldı", "subtitles.item.glow_ink_sac.use": "<PERSON><PERSON><PERSON><PERSON><PERSON> mürəkkə<PERSON> kisəsi boyayır", "subtitles.item.goat_horn.play": "Keçi Buynuzu çalınır", "subtitles.item.hoe.till": "Torpaq kərkiləndi", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON> tətbiq edildi", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kisəsi boyayır", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Maqnit kompası maqnit daşına qoşulur", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "<PERSON><PERSON>", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Qayçı kəsdi", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON>n maneə oldu", "subtitles.item.shovel.flatten": "<PERSON>ə<PERSON><PERSON><PERSON> dü<PERSON>", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON>", "subtitles.item.totem.use": "Totem aktiv oldu", "subtitles.item.trident.hit": "Trident bıçaqlayır", "subtitles.item.trident.hit_ground": "Trident titrəyir", "subtitles.item.trident.return": "Üçbaşlı əsa qayıdır", "subtitles.item.trident.riptide": "Trident zooms", "subtitles.item.trident.throw": "Trident cingilt<PERSON><PERSON>ri", "subtitles.item.trident.thunder": "Trident ildır<PERSON>m çatlar", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Wolf Armor takes damage", "subtitles.item.wolf_armor.repair": "Wolf Armor is repaired", "subtitles.particle.soul_escape": "<PERSON><PERSON> azad o<PERSON>r", "subtitles.ui.cartography_table.take_result": "rəsm kartları", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "İstifadə olunan dəzgah", "subtitles.ui.stonecutter.take_result": "Daş Kəsici istifadə olunur", "subtitles.weather.rain": "<PERSON><PERSON><PERSON><PERSON> yağır", "symlink_warning.message": "Sembole bağlantılar içeren klasörlerden dünyalar yüklemek, tam olarak ne yaptığınızı bilmiyorsanız güvensiz olabilir. Daha fazla bilgi edinmek için lütfen %s adresini ziyaret edin.", "symlink_warning.message.pack": "Tam olaraq nə etdiyinizi bilmirsinizsə, simvolik bağlantılarla paketlərin yüklənməsi təhlükəli ola bilər. Zəhmət olmasa ziyarət edin %s daha çox öyrənmək üçün.", "symlink_warning.message.world": "Əgər siz nə etdiyinizi dəqiq bilmirsinizsə, simvolik keçidləri olan qovluqlardan dünyaları yükləmək təhlükəli ola bilər. Ətraflı məlumat üçün %s'a/ə daxil olun.", "symlink_warning.more_info": "<PERSON><PERSON>", "symlink_warning.title": "Dünya klasörü sembolik bağlantılar içeriyor", "symlink_warning.title.pack": "<PERSON><PERSON><PERSON> edilmiş paket(lər)'də simvolik bağlantılar var", "symlink_warning.title.world": "<PERSON><PERSON><PERSON> qoltuqu simvolik əlaqələri ehtiva edir", "team.collision.always": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON> vaxt", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON><PERSON> koman<PERSON>arı itələyin", "team.collision.pushOwnTeam": "Öz komandanı itələyin", "team.notFound": "Naməlum rəng '%s'", "team.visibility.always": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "<PERSON><PERSON><PERSON><PERSON> komandalar üçün gizlət", "team.visibility.hideForOwnTeam": "Öz komandandan gizlət", "team.visibility.never": "<PERSON><PERSON> vaxt", "telemetry.event.advancement_made.description": "Bir başarı elde etme bağlamını anlamak, oyunun ilerlemesini daha iyi anlamamıza ve geliştirmemize yardımcı olabilir.", "telemetry.event.advancement_made.title": "İnkişaf edildi", "telemetry.event.game_load_times.description": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, başlangıç performansı iyileştirmelerinin nerede gerektiğini anlamamıza yardımcı olabilir. Başlangıç aşamalarının işlem sürelerini ölçerek hangi alanlarda iyileştirmeler yapılması gerektiğini belirleyebiliriz.", "telemetry.event.game_load_times.title": "<PERSON><PERSON><PERSON>", "telemetry.event.optional": "%s (İst<PERSON><PERSON>ə bağlı)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Minecraft-ın ümumi performans profilini bilmək bizə oyunu çoxlu maşın spesifikasiyası və əməliyyat sistemləri üçün tənzimləməyə və optimallaşdırmağa kömək edir.\n Minecraft-ın yeni versiyaları üçün performans profilini müqayisə etmək üçün oyun versiyası daxil edilmişdir.", "telemetry.event.performance_metrics.title": "Performans <PERSON>", "telemetry.event.required": "%s (<PERSON>ə<PERSON><PERSON><PERSON> olunur)", "telemetry.event.world_load_times.description": "Bir dünyaya qoşulmağın nə qədər vaxt tələb etdiyini və bunun zamanla necə dəyişdiyini anlamaq bizim üçün vacibdir. Məsələn, biz yeni funksiyalar əlavə etdikdə və ya daha böyük texniki dəyişikliklər etdikdə, bunun yükləmə müddətinə necə təsir etdiyini görməliyik.", "telemetry.event.world_load_times.title": "Dünya Yükləmə Saatları", "telemetry.event.world_loaded.description": "Oyunçuların Minecraftı necə oynadıqlarını bilmək (mə<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>yun Rejimi, müştəri və ya server modifikasiyası və oyun versiyası) oyunçuların ən çox maraqlandığı sahələri təkmilləşdirmək üçün oyun yeniləmələrinə diqqət yetirməyə imkan verir.\n Oyun sessiyasının nə qədər davam etdiyini hesablamaq üçün World Loaded hadisəsi World Unloaded hadisəsi ilə birləşdirilir.", "telemetry.event.world_loaded.title": "Dünya Yükləndi", "telemetry.event.world_unloaded.description": "Dünya sessiyasının nə qədər davam etdiyini hesablamaq üçün bu hadisə World Loaded hadisəsi ilə birləşdirilir.\n Müddət (saniyələrlə və işarələrlə) dünya sessiyası başa çatdıqda ölçülür (başl<PERSON><PERSON><PERSON> çıxmaq, serverlə əlaqəni kəsmək).", "telemetry.event.world_unloaded.title": "Dünya <PERSON>ald<PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON> (Tick'ler)", "telemetry.property.advancement_id.title": "Başarı Kimliği (Advancement ID)", "telemetry.property.client_id.title": "Müştəri ID", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON>", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON><PERSON><PERSON> (kB)", "telemetry.property.event_timestamp_utc.title": "Hadisə vaxtı damğası (UTC)", "telemetry.property.frame_rate_samples.title": "Çərçivə Tezliyi Nümunələri (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "Oyun Versiyası", "telemetry.property.launcher_name.title": "Launçerin <PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "Önyükleme Zamanı (Milisaniye)", "telemetry.property.load_time_loading_overlay_ms.title": "Yükleme Ekranındaki <PERSON> (Milisaniye)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON> açılmasına qədər vaxt (millisaniyə)", "telemetry.property.load_time_total_time_ms.title": "Dünya Yükləmə Vaxtı (Milisaniyələr)", "telemetry.property.minecraft_session_id.title": "Minecraft Sessiya İD", "telemetry.property.new_world.title": "<PERSON>ni <PERSON>", "telemetry.property.number_of_samples.title": "Nümunə sayı", "telemetry.property.operating_system.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sistemi", "telemetry.property.opt_in.title": "Daxil ol", "telemetry.property.platform.title": "Platforma", "telemetry.property.realms_map_content.title": "Realms Xəritə Məzmunu (Minioyun Adı)", "telemetry.property.render_distance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.render_time_samples.title": "<PERSON><PERSON><PERSON>", "telemetry.property.seconds_since_load.title": "Yük<PERSON><PERSON><PERSON> sonrakı vaxt (Saniyə)", "telemetry.property.server_modded.title": "Server <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.server_type.title": "Server növü", "telemetry.property.ticks_since_load.title": "Yük<PERSON><PERSON><PERSON>ı vaxt (Gənələr)", "telemetry.property.used_memory_samples.title": "İstifadə olunan Təsadüfi G<PERSON>", "telemetry.property.user_id.title": "İstifadəçi adı", "telemetry.property.world_load_time_ms.title": "Dünya Yükləmə Vaxtı (Milisaniyələr)", "telemetry.property.world_session_id.title": "Dünya Sessiyası ID", "telemetry_info.button.give_feedback": "Geridön<PERSON><PERSON> Ver", "telemetry_info.button.privacy_statement": "Məxfilik Bəyanatı", "telemetry_info.button.show_data": "Məlumatlarımı açın", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Daxil Data", "telemetry_info.screen.description": "Bu məlumatların toplanması bizə oyunçularımıza uyğun istiqamətlərə rəhbərlik etməklə Minecraft-ı təkmilləşdirməyə kömək edir.\n Siz həmçinin Minecraft-ı təkmilləşdirməyə davam etməmizə kömək etmək üçün əlavə rəy göndərə bilərsiniz.", "telemetry_info.screen.title": "Telemetriya məlumatlarının toplanması", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "<PERSON><PERSON><PERSON>", "test_block.mode.log": "Log", "test_block.mode.start": "Başla", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Xəta baş verdi! :(", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit sistem aşkarlandı: bu, gələcəkdə oynamağınıza mane ola bilər, <PERSON><PERSON><PERSON><PERSON> 64-bit sistem tələb olunacaq!", "title.32bit.deprecation.realms": "Minecraft tezliklə 64 bitlik sistem tələb edəcək və bu, bu cihazda Realms oynamağınıza və ya istifadə etməyinizə mane olacaq. İstənilən Realms abunəliyini əl ilə ləğv etməli olacaqsınız.", "title.32bit.deprecation.realms.check": "Bu ekranı bir daha gö<PERSON>ə<PERSON>əyin", "title.32bit.deprecation.realms.header": "32-bit sistem a<PERSON>kar edildi", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Çox oyunçulu yararsızlaşdırılıb, lütfən Microsoft hesab ayarlarınızı yoxlayın.", "title.multiplayer.disabled.banned.name": "Onlayn oynamadan əvvəl adınızı dəyişdirməlisiniz", "title.multiplayer.disabled.banned.permanent": "Hesabı<PERSON><PERSON><PERSON> müvəqqəti olaraq onlayn oyundan dayandırılıb", "title.multiplayer.disabled.banned.temporary": "Hesabı<PERSON><PERSON><PERSON> müvəqqəti olaraq onlayn oyundan dayandırılıb", "title.multiplayer.lan": "Çox oyunçu (Şəbəkə)", "title.multiplayer.other": "Multiplayer (üçüncü tərəf server)", "title.multiplayer.realms": "Çox oyunçu (səltənətlər)", "title.singleplayer": "Tək Oyunçulu", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s təkrar %s və %1$s ən sonda %s və əlavə olaraq təkrar %1$s!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "salam %", "translation.test.invalid2": "salam %s", "translation.test.none": "Salam, dünya!", "translation.test.world": "dünya", "trim_material.minecraft.amethyst": "Ametist materialı", "trim_material.minecraft.copper": "Mis material", "trim_material.minecraft.diamond": "Almaz materialı", "trim_material.minecraft.emerald": "Zümrüd materialı", "trim_material.minecraft.gold": "Qızıl material", "trim_material.minecraft.iron": "Dəmir material", "trim_material.minecraft.lapis": "Lapis materialı", "trim_material.minecraft.netherite": "Kürdəmit materialı", "trim_material.minecraft.quartz": "<PERSON><PERSON>s materialı", "trim_material.minecraft.redstone": "Qırmızı daş materialı", "trim_material.minecraft.resin": "Resin Material", "trim_pattern.minecraft.bolt": "Bolt Armor Trim", "trim_pattern.minecraft.coast": "<PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "Flow Armor Trim", "trim_pattern.minecraft.host": "Zırh Kəsməni keçirin", "trim_pattern.minecraft.raiser": "Artırıcı Zireh Trim", "trim_pattern.minecraft.rib": "Qabırğa Z<PERSON>", "trim_pattern.minecraft.sentry": "Keşikçi Zireh Trim", "trim_pattern.minecraft.shaper": "Formalaşdırıcı Zireh Trim", "trim_pattern.minecraft.silence": "<PERSON> <PERSON><PERSON>", "trim_pattern.minecraft.snout": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.spire": "Spire Arm<PERSON>", "trim_pattern.minecraft.tide": "Tide Armor Trim", "trim_pattern.minecraft.vex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Trim", "trim_pattern.minecraft.wild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tutorial.bundleInsert.description": "Əşya əlavə etmək üçün sağa kliklə", "tutorial.bundleInsert.title": "Bir boxça istifadə et", "tutorial.craft_planks.description": "Resept kitabı kömək edə bilər", "tutorial.craft_planks.title": "<PERSON><PERSON> hazı<PERSON>a", "tutorial.find_tree.description": "<PERSON>dun əldə etmək üçün ağacı yumruqla", "tutorial.find_tree.title": "<PERSON><PERSON> a<PERSON> tap", "tutorial.look.description": "Dönmək üçün siçandan istifadə et", "tutorial.look.title": "Ətrafa bax", "tutorial.move.description": "%s ilə tullan", "tutorial.move.title": "%s, %s, %s və %s ilə hərəkət et", "tutorial.open_inventory.description": "%s'ə bas", "tutorial.open_inventory.title": "İnventarını aç", "tutorial.punch_tree.description": "%s'i klik edili tut", "tutorial.punch_tree.title": "Ağacı yox et", "tutorial.socialInteractions.description": "Açmaq üçün %s düyməsinə basın", "tutorial.socialInteractions.title": "<PERSON><PERSON>l <PERSON>", "upgrade.minecraft.netherite_upgrade": "Nezerit Təkmilləşdirmə"}