{"accessibility.onboarding.accessibility.button": "Barrierefreiheitseinstöllungen...", "accessibility.onboarding.screen.narrator": "Druck de Eingobetaste um de Sprochausgob zu aktivieren", "accessibility.onboarding.screen.title": "Willkommen bei Minecraft!\n\nMehast du den Erzählor aktiviern oder die Einstellungan für Barrierfreiheit bsuachn?", "addServer.add": "<PERSON><PERSON>", "addServer.enterIp": "Serveradress", "addServer.enterName": "Servernom", "addServer.resourcePack": "Server-Ressourcenpakete", "addServer.resourcePack.disabled": "Deaktiviat", "addServer.resourcePack.enabled": "Aktiviat", "addServer.resourcePack.prompt": "Obfrog", "addServer.title": "Serverinfos beorbeitn", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Imma aktiv", "advMode.mode.conditional": "<PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Braucht Redstone", "advMode.mode.sequence": "Kettn", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "Nua a Operator im Kreativ-Modus ku Befehle eigebn", "advMode.notEnabled": "Befehlsbleck san auf dem <PERSON> ned e<PERSON>schoitn", "advMode.previousOutput": "Letzte Ausgob", "advMode.setCommand": "Befehl eigebn", "advMode.setCommand.success": "Befehl gsetzt: %s", "advMode.trackOutput": "Ausgob vafolgn", "advMode.triggering": "Auslösn", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Unbekonnta Fortschritt: %s", "advancements.adventure.adventuring_time.description": "Entdeck olle Biome", "advancements.adventure.adventuring_time.title": "Obnteiazeit", "advancements.adventure.arbalistic.description": "Bring fünf untaschiedliche Kreaturn mit oan Oambrustschuss um", "advancements.adventure.arbalistic.title": "Oambrustschütze", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON><PERSON><PERSON> in da Nähe von an Sculk-Sensor oda an Wärta um ned erwischt zu wean", "advancements.adventure.avoid_vibration.title": "Sneak 100", "advancements.adventure.blowback.description": "Bring a Böe mit ana obgwehrtn Böen-Windkugel um", "advancements.adventure.blowback.title": "Gegnwind", "advancements.adventure.brush_armadillo.description": "Benutz an Pinsl, um a Hornschüd vom Panzer von an Gürtltier zum kriagn", "advancements.adventure.brush_armadillo.title": "Is as ned liab?", "advancements.adventure.bullseye.description": "Triff de Mittn vo an Züblock aus mindestens 30 Blöckn Entfernung", "advancements.adventure.bullseye.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Bau an vaziertn Kruag aus vier Töpfascherbn", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Vuasichtige Restauration", "advancements.adventure.crafters_crafting_crafters.description": "Sei in da nähe von am Werka, wonn dea an Werka heastöht", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON>aka <PERSON>ln <PERSON>aka", "advancements.adventure.fall_from_world_height.description": "Moch an frein Foi von da Wödobagrenz (maximale Bauhechn) zua Wöduntagrenz und übaleb", "advancements.adventure.fall_from_world_height.title": "Höhln & Klippn", "advancements.adventure.heart_transplanter.description": "Bau a Knoazaheaz in da Richtings Orientation zwischn zwa Blosseichnstommbleck", "advancements.adventure.heart_transplanter.title": "Heazvapflonza", "advancements.adventure.hero_of_the_village.description": "Verteidig a Dorf erfolgreich vor an Überfoi", "advancements.adventure.hero_of_the_village.title": "Do<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Spring auf an Honigblock, um dein Sturz obzufongen", "advancements.adventure.honey_block_slide.title": "A klebrige Ongelegenheit", "advancements.adventure.kill_a_mob.description": "Bring a Monsta um", "advancements.adventure.kill_a_mob.title": "Mon<PERSON>ja<PERSON>", "advancements.adventure.kill_all_mobs.description": "Bring jeds Monsta mindestens oa Moi um", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON>ch an V<PERSON>h in da Nähe von an Sculk-Katalysatoa hin", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Es vabreitet si", "advancements.adventure.lighten_up.description": "<PERSON><PERSON><PERSON> mit ana <PERSON>t on ana <PERSON>, damit se hölla weat", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Sc<PERSON><PERSON>tz an Dorfbewohna vor an unerwünschtn Schock, ohne dabei an Brond auszlösn", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Übasponnungsschutz", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON> an <PERSON><PERSON> in a Prüfungskamma", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Prüfungs Ausgob", "advancements.adventure.ol_betsy.description": "Feia a Oambrust ob", "advancements.adventure.ol_betsy.title": "De oide <PERSON>", "advancements.adventure.overoverkill.description": "Moch 50 Herzn Schodn mit an Schlägel schlog", "advancements.adventure.overoverkill.title": "Is scho hin musst nit noamoll draufhaun", "advancements.adventure.play_jukebox_in_meadows.description": "Beleb die Alm mitn Klong da Musi aus an Plottnspiela", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON><PERSON> an Komparator, zumd Signalstärke vo am gearbeiteten Büacharegal zum leasa", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Die Kroft vo Biacha", "advancements.adventure.revaulting.description": "Öffne an unheilvolln Tresor imt am unheilvolln Prüfungschlussl", "advancements.adventure.revaulting.title": "Ponzaknocka", "advancements.adventure.root.description": "Obnteia, Erforschung und Kompf", "advancements.adventure.root.title": "Obnteia", "advancements.adventure.salvage_sherd.description": "Pinsel an komischen Block dasd a vasn stickal griagst", "advancements.adventure.salvage_sherd.title": "Respektiere de Reste", "advancements.adventure.shoot_arrow.description": "Das<PERSON>ß wos mit am Pfeil", "advancements.adventure.shoot_arrow.title": "Züübungen", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON>of in am Bett, um dein Wiedaeistiegspunkt zu ändan", "advancements.adventure.sleep_in_bed.title": "<PERSON>ram wos <PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Bring a Skelett aus ana Entfernung von mehr ois 50 Meter um", "advancements.adventure.sniper_duel.title": "Sc<PERSON><PERSON>chütznduell", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> schaugn mitn Binoggl", "advancements.adventure.spyglass_at_dragon.title": "Is des a Flugzeig?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON> mitn <PERSON>", "advancements.adventure.spyglass_at_ghast.title": "Is des a Ballon?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON>au<PERSON> mitn <PERSON>gl", "advancements.adventure.spyglass_at_parrot.title": "Is des a Vogl?", "advancements.adventure.summon_iron_golem.description": "<PERSON><PERSON> an Eisngolem, um bei da Vateidigung von an Dorf mitzuhöfn", "advancements.adventure.summon_iron_golem.title": "Stets zu Dienstn!", "advancements.adventure.throw_trident.description": "<PERSON><PERSON><PERSON><PERSON> an Dreizock auf irgendwos.\nAchtung: De<PERSON> oanzige Woffn wegzumschmeißn is koa guade Idee.", "advancements.adventure.throw_trident.title": "A Witz zum Wegschmeißn", "advancements.adventure.totem_of_undying.description": "Benutz a Totem der Unsterblichkeit, um an Tod zu entkemma", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "<PERSON><PERSON><PERSON><PERSON> an Hondl mit am Dorfbewohna ob", "advancements.adventure.trade.title": "Wos fia a Gschäft!", "advancements.adventure.trade_at_world_height.description": "Hondl mit am Dorfbewohna auf da maximaln Bauhechn", "advancements.adventure.trade_at_world_height.title": "Sternhändla", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Benutz jeds von de Schmiedevorlogn mindestens oamoi: <PERSON><PERSON><PERSON>z<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Gezeitn und Wegfinda", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Schmiedn mit Style", "advancements.adventure.trim_with_any_armor_pattern.description": "Erstöh a getrimmtes Rüstungteil bei an Schmiedetisch", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON><PERSON> a ne<PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Bring zwoa Phantome mit o<PERSON> um", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON> auf o<PERSON>", "advancements.adventure.under_lock_and_key.description": "Entsperr an Tresor mit am Prüfungsschlüssl", "advancements.adventure.under_lock_and_key.title": "Hinta Schloss und Riegl", "advancements.adventure.use_lodestone.description": "Leitstoa-Kompass verwendn", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON> <PERSON>, wo de Leits<PERSON>ana stengan", "advancements.adventure.very_very_frightening.description": "<PERSON>ff an Dorfbewohna mit an Blitz", "advancements.adventure.very_very_frightening.title": "<PERSON>hr, sehr ongsteiflößnd", "advancements.adventure.voluntary_exile.description": "Töt den Hauptmonn eines Übafolls.\nVielleicht soitest du in Betrocht z<PERSON>hn, di vore<PERSON> von <PERSON> fernz'hoiten...", "advancements.adventure.voluntary_exile.title": "Freiwilligs Exil", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "G<PERSON> auf Pulver Schnee ohne das du <PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON>t wie a Hos", "advancements.adventure.who_needs_rockets.description": "Benutz a Windkugl, um di 8 Blöcke in de höhe zum schleidan", "advancements.adventure.who_needs_rockets.title": "Wea braucht schon Raketen?", "advancements.adventure.whos_the_pillager_now.description": "G<PERSON> oam Plündara ane Kostprobn sana eiganan <PERSON>", "advancements.adventure.whos_the_pillager_now.title": "Wer is jetz dea Plündara?", "advancements.empty": "Do scheint nix zu sein ...", "advancements.end.dragon_breath.description": "So<PERSON>l Drochnatem in ana Glosfloschn", "advancements.end.dragon_breath.title": "Du brauchst a Pfefferminz", "advancements.end.dragon_egg.description": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "advancements.end.dragon_egg.title": "De naxte Generation", "advancements.end.elytra.description": "<PERSON>", "advancements.end.elytra.title": "Hintam Horizent geht's weita", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON><PERSON><PERSON> da Insl", "advancements.end.enter_end_gateway.title": "Im Transit", "advancements.end.find_end_city.description": "Geh scho rein, wos kunnt passian?", "advancements.end.find_end_city.title": "De Stodt am Ende vom Spü", "advancements.end.kill_dragon.description": "Vü Glück", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>", "advancements.end.levitate.description": "Schweb durch a Shulkerprojektil 50 Bleck in de Hech", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON> Aussicht von do obn", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON> an Enderdrochn ins Lebn zrugg", "advancements.end.respawn_dragon.title": "As Ende ... <PERSON><PERSON> wieda ...", "advancements.end.root.description": "... oda da <PERSON>?", "advancements.end.root.title": "<PERSON> <PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON> an Hüfsgeist an <PERSON>chn ba an Notnblock oblegn", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Geburtstogsliad", "advancements.husbandry.allay_deliver_item_to_player.description": "Loss da Gegnstend von am Hüfsgeist bringa", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON> <PERSON>n Freind in mia", "advancements.husbandry.axolotl_in_a_bucket.description": "Fong a Axolotl mit an Kiwe", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON>s gschtiaschteste Viech", "advancements.husbandry.balanced_diet.description": "<PERSON>s ois, wos essbor is, a wenn's ned guat fia di is", "advancements.husbandry.balanced_diet.title": "Ausgwogene Ernährung", "advancements.husbandry.breed_all_animals.description": "V<PERSON>hr oi Tierortn!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.breed_an_animal.description": "Vamehr zwoa Tiere", "advancements.husbandry.breed_an_animal.title": "<PERSON> He<PERSON> und de Blu<PERSON>", "advancements.husbandry.complete_catalogue.description": "<PERSON><PERSON><PERSON> oi <PERSON>tznortn!", "advancements.husbandry.complete_catalogue.title": "A kompletta Katerlog", "advancements.husbandry.feed_snifflet.description": "Füttere an Schnüffler", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "Ongl an Fisch", "advancements.husbandry.fishy_business.title": "Onglaglück", "advancements.husbandry.froglights.description": "<PERSON><PERSON> o<PERSON> im Inventar", "advancements.husbandry.froglights.title": "Mit vaeintn Kräftn!", "advancements.husbandry.kill_axolotl_target.description": "Vabünd di mit an Axolotl und gwinn an Kompf", "advancements.husbandry.kill_axolotl_target.title": "De heilende Kroft der Freindschoft!", "advancements.husbandry.leash_all_frog_variants.description": "Hob a jede <PERSON> an ana <PERSON>", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON> Trupp in die Stodt hüpft", "advancements.husbandry.make_a_sign_glow.description": "Loss an Text von an beliebign Schüd leichtn", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON>t und staun!", "advancements.husbandry.netherite_hoe.description": "Vawend an Netheritborrn, um a Hockn aufzuwertn und denk donn a bissl üba deine Lebnsentscheidungen noch", "advancements.husbandry.netherite_hoe.title": "Mei greß<PERSON> Leidnschoft", "advancements.husbandry.obtain_sniffer_egg.description": "Hoi a Schnüffla-Ei", "advancements.husbandry.obtain_sniffer_egg.title": "Riacht Interessant", "advancements.husbandry.place_dried_ghast_in_water.description": "Platziea an vatrocknetn Ghast in Wossa", "advancements.husbandry.place_dried_ghast_in_water.title": "Imma vü trinkn!", "advancements.husbandry.plant_any_sniffer_seed.description": "P<PERSON><PERSON><PERSON> irgend an <PERSON> von an <PERSON>hnüffla", "advancements.husbandry.plant_any_sniffer_seed.title": "De Vagongenheit pflonzn", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON> und schau eana beim <PERSON>n zua", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Entfean Woifsrüstung von am Woif mit ana Schea", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON><PERSON>-schnapp!", "advancements.husbandry.repair_wolf_armor.description": "Reparier a beschädigte Woifsrüstung mit Gürtltierhornschüdn komplett", "advancements.husbandry.repair_wolf_armor.title": "So guad wie neich", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Steig in a Boot und roas mid a Goaß", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON> is Hoas!", "advancements.husbandry.root.description": "<PERSON> is voi mit Freind und Essn", "advancements.husbandry.root.title": "Londwirtschoft", "advancements.husbandry.safely_harvest_honey.description": "<PERSON><PERSON> a <PERSON><PERSON>, zum mit a Glasfläsche Honig us am Bianastock zum neah, ohne d Biana zum reizn", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.silk_touch_nest.description": "Beweg a von drei Bienen bewohnts Bienennest mit Behutsomkeit", "advancements.husbandry.silk_touch_nest.title": "Bienenwondarung", "advancements.husbandry.tactical_fishing.description": "Fong an Fisch ... ohne Ongl!", "advancements.husbandry.tactical_fishing.title": "Taktischs Fisch", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON>ong a <PERSON>n mit an <PERSON>we", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "<PERSON>ähm a <PERSON>", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON><PERSON> Freind", "advancements.husbandry.wax_off.description": "<PERSON><PERSON>z des Wochs von an Kupfablock owa!", "advancements.husbandry.wax_off.title": "Entwochst", "advancements.husbandry.wax_on.description": "Wochs an Kupfablock mit ana Honigwo<PERSON>n!", "advancements.husbandry.wax_on.title": "Gwochst", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.husbandry.whole_pack.title": "Olle san do", "advancements.nether.all_effects.description": "Hob jedn Statuseffekt gleichzeitig", "advancements.nether.all_effects.title": "Wia hobn wia des g<PERSON>t?", "advancements.nether.all_potions.description": "<PERSON><PERSON> jedn Tronkeffekt gleichzeitig", "advancements.nether.all_potions.title": "Trunkenbold", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> an Tronk", "advancements.nether.brew_potion.title": "Oichemie", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON>on Anchor wurd maximal aufgfüt", "advancements.nether.charge_respawn_anchor.title": "<PERSON> gonz \"<PERSON><PERSON><PERSON>\" <PERSON><PERSON>n", "advancements.nether.create_beacon.description": "Erstöh und platzia a Leichtfeia", "advancements.nether.create_beacon.title": "An Nochborn hoamleichtn", "advancements.nether.create_full_beacon.description": "Bring a Leichtfeia auf voie Stärk", "advancements.nether.create_full_beacon.title": "Leichtturmwärta", "advancements.nether.distract_piglin.description": "<PERSON><PERSON>l mit Goid ob", "advancements.nether.distract_piglin.title": "Oh, wia glänznd!", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON> <PERSON> Hölln Biome an", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON>", "advancements.nether.fast_travel.description": "Benutz des Nether, um 7 km in da Obawöd zu reisn", "advancements.nether.fast_travel.title": "Subraumtransport", "advancements.nether.find_bastion.description": "<PERSON>ritt a Bastionsruin", "advancements.nether.find_bastion.title": "<PERSON> de Zeitn", "advancements.nether.find_fortress.description": "<PERSON><PERSON> in a Netherfestung ei", "advancements.nether.find_fortress.title": "A schreckliche Festung", "advancements.nether.get_wither_skull.description": "Beschoff an Witherskelettschädl", "advancements.nether.get_wither_skull.title": "<PERSON> gra<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "advancements.nether.loot_bastion.description": "Plünda a Kistn in ana Bastionsruin", "advancements.nether.loot_bastion.title": "Kompfschweina", "advancements.nether.netherite_armor.description": "Hob a volle Netheritrüstung", "advancements.nether.netherite_armor.title": "<PERSON><PERSON> mi mit Schutt", "advancements.nether.obtain_ancient_debris.description": "Beschoff Antikn Schrott", "advancements.nether.obtain_ancient_debris.title": "<PERSON>ast<PERSON><PERSON> in de Tiaf'n", "advancements.nether.obtain_blaze_rod.description": "Erleichta a Lohe um ia Ruatn", "advancements.nether.obtain_blaze_rod.title": "Spü mit'm <PERSON>ia", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON><PERSON> an Weinend'n Obsidian", "advancements.nether.obtain_crying_obsidian.title": "Wea schneidet do Zwiebln?", "advancements.nether.return_to_sender.description": "Schlog an Ghast mit seine eigenen Woffn", "advancements.nether.return_to_sender.title": "Zrugg zum Obsenda", "advancements.nether.ride_strider.description": "Reit an Schreita mit ana Wirrpüzongl", "advancements.nether.ride_strider.title": "Des Boot hot Haxn", "advancements.nether.ride_strider_in_overworld_lava.description": "Untanimm mit an Schreita a looooonge Reise auf an Laves<PERSON> in da Obawöd", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON>", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> <PERSON> wos <PERSON> on", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON> an <PERSON>er", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON> He<PERSON>n", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON> an <PERSON> ausm <PERSON>, bring eam sicha in de Obawöd... und bring eam donn um", "advancements.nether.uneasy_alliance.title": "Unheilvoie Allianz", "advancements.nether.use_lodestone.description": "Vawend an Kompass an am Leitstoa", "advancements.nether.use_lodestone.title": "<PERSON><PERSON> <PERSON>, wo de <PERSON><PERSON> stengan", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Schwäch an Dorfbewohnazombie und heil ihn donn", "advancements.story.cure_zombie_villager.title": "Zombieorzt", "advancements.story.deflect_arrow.description": "Lenk a Gschoss mit am Schüd ob", "advancements.story.deflect_arrow.title": "Heit ned, danke", "advancements.story.enchant_item.description": "Vazauba an Gegnstond an am Zaubatisch", "advancements.story.enchant_item.title": "Vazaubara", "advancements.story.enter_the_end.description": "Betret as Endportal", "advancements.story.enter_the_end.title": "As <PERSON>e?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, entz<PERSON> und betret a Netherportal", "advancements.story.enter_the_nether.title": "Wia miaßn nu tiafa", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> an <PERSON>ug", "advancements.story.follow_ender_eye.title": "As fliagnde Aug", "advancements.story.form_obsidian.description": "<PERSON><PERSON><PERSON> an Obsidianblock", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Vabessa dei Spitzhock", "advancements.story.iron_tools.title": "<PERSON>u vü E<PERSON>n im Bluat", "advancements.story.lava_bucket.description": "<PERSON><PERSON> in an Kiwe", "advancements.story.lava_bucket.title": "<PERSON><PERSON> megn's hoaß", "advancements.story.mine_diamond.description": "Be<PERSON>off Diamantn", "advancements.story.mine_diamond.title": "Diamantn!", "advancements.story.mine_stone.description": "<PERSON>u an Stoa mit deina ne<PERSON> ob", "advancements.story.mine_stone.title": "Steinzeit", "advancements.story.obtain_armor.description": "Schütz di mit am Rüstungsteil aus Eisn", "advancements.story.obtain_armor.title": "Moch di fesch", "advancements.story.root.description": "Da Kern und de Gschicht vom Spü", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamantrüstung rettet Lebn", "advancements.story.shiny_gear.title": "<PERSON><PERSON> mi mit Diamantn", "advancements.story.smelt_iron.description": "Schmöz <PERSON>z im Ofn zu an Eisnborrn", "advancements.story.smelt_iron.title": "Schmiedekunst", "advancements.story.upgrade_tools.description": "St<PERSON>h a bessare Spitzhock hea", "advancements.story.upgrade_tools.title": "Technischa Fortschritt", "advancements.toast.challenge": "Herausfordarung obgschlossn!", "advancements.toast.goal": "Zü erreicht!", "advancements.toast.task": "Fortsch<PERSON>t erzüht!", "argument.anchor.invalid": "Ungültige Objektonkaposition: %s", "argument.angle.incomplete": "Unvoiständig (1 Blickwinkl eawoatet)", "argument.angle.invalid": "Ungültiga Winkl", "argument.block.id.invalid": "Unbekonnta Blocktyp '%s'", "argument.block.property.duplicate": "Zuastond ‚%s‘ konn nua oamoi fia den Block %s gsetzt wean", "argument.block.property.invalid": "Block %s akzeptiat ‚%s‘ fia Blockzuastond ‚%s‘ ned", "argument.block.property.novalue": "Fehlenda Wert fia Zustaond ‚%s‘ vom Block %s", "argument.block.property.unclosed": "Schließendes ] für Blockeigenschoften erwortet", "argument.block.property.unknown": "Block %s hot den Zuastond ‚%s‘ ned", "argument.block.tag.disallowed": "Tags san do net erlaubt, nur richtige Bleck", "argument.color.invalid": "Unbekonnte Forb '%s'", "argument.component.invalid": "Ungütige Chat-Komponentn: %s", "argument.criteria.invalid": "Unbekonntes Kriterium '%s'", "argument.dimension.invalid": "Unbekonnte Dimension '%s'", "argument.double.big": "Kommazoi deaf ned greßa ois %s sei, %s is zu groß", "argument.double.low": "Kommazoi deaf ned kleana ois %s sei, %s is zu kloa", "argument.entity.invalid": "Ungültiga Name oder UUID", "argument.entity.notfound.entity": "<PERSON><PERSON> kaun ka Objekt gfunden wean", "argument.entity.notfound.player": "<PERSON><PERSON> kaun ka <PERSON>la gfunden wean", "argument.entity.options.advancements.description": "Spiela mit Fortschritte", "argument.entity.options.distance.description": "Entfernung zum Objekt", "argument.entity.options.distance.negative": "Distanz konn ned negativ sei", "argument.entity.options.dx.description": "Objekte zwischn X und X + dX", "argument.entity.options.dy.description": "Objekte zwischn Y und Y + dY", "argument.entity.options.dz.description": "Objekte zwischn Z und Z + dZ", "argument.entity.options.gamemode.description": "Spieler mit Spümodus", "argument.entity.options.inapplicable": "Filta ‚%s‘ is do ned onwendbor", "argument.entity.options.level.description": "Erfohrungsstufn", "argument.entity.options.level.negative": "Erfahrungsstufe sollte nit negativ sein", "argument.entity.options.limit.description": "Maximale Onzoi von zurückzuliefandn Objektn", "argument.entity.options.limit.toosmall": "Limit muas mindestens 1 sei", "argument.entity.options.mode.invalid": "Ungültiga oda unbekonnta Spümodus ‚%s‘", "argument.entity.options.name.description": "Objektnom", "argument.entity.options.nbt.description": "Objekte mit NBT", "argument.entity.options.predicate.description": "Benutzerdefinierts Prädiat", "argument.entity.options.scores.description": "Objekte mit Punktestände", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> sortian", "argument.entity.options.sort.irreversible": "Ungültige oda unbekonnte Sortierung ‚%s‘", "argument.entity.options.tag.description": "Objekte mit Tag", "argument.entity.options.team.description": "Objekte im Team", "argument.entity.options.type.description": "Objekte vom Typ", "argument.entity.options.type.invalid": "Ungültiga oda unbekonnta Objekttyp ‚%s‘", "argument.entity.options.unknown": "Unbekonnta Filta ‚%s‘", "argument.entity.options.unterminated": "Ende der Optionen erwortet", "argument.entity.options.valueless": "Wertfia Filta ‚%s‘ erwortet", "argument.entity.options.x.description": "X-Position", "argument.entity.options.x_rotation.description": "Kopfneigung vom Objekt", "argument.entity.options.y.description": "Y-Position", "argument.entity.options.y_rotation.description": "Blickrichtung vom Objekt", "argument.entity.options.z.description": "Z-Position", "argument.entity.selector.allEntities": "Oi Objekte", "argument.entity.selector.allPlayers": "<PERSON><PERSON>", "argument.entity.selector.missing": "Fehlenda Selektortyp", "argument.entity.selector.nearestEntity": "Naxtes Objekt", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON>", "argument.entity.selector.not_allowed": "Selektor net erlaubt", "argument.entity.selector.randomPlayer": "Zuafälliga <PERSON>", "argument.entity.selector.self": "Aktuells Objekt", "argument.entity.selector.unknown": "Unbekonnta Selektor ‚%s‘", "argument.entity.toomany": "Es is nur oa Objekt erlaubt, oba der zur Verfügung gstellte Selektor erlabt mehrere", "argument.enum.invalid": "Ungültiga Wert „%s“", "argument.float.big": "Kommazoi deaf ned greßa ois %s sei, %s is zu groß", "argument.float.low": "Kommazoi deaf ned kleana ois %s sei, %s is zu kloa", "argument.gamemode.invalid": "Unbekannter Spümodus: %s", "argument.hexcolor.invalid": "Ungültiga Hex Foabncode '%s'", "argument.id.invalid": "Ungütige ID", "argument.id.unknown": "Unbekonnte ID: %s", "argument.integer.big": "Gonzzoi deaf ned greßa ois %s sei, %s is zu groß", "argument.integer.low": "Gonzzoi deaf ned kleana ois %s sei, %s is zu kloa", "argument.item.id.invalid": "Unbekonnta Gegnstond ‚%s‘", "argument.item.tag.disallowed": "Tags san do net erlaubt, nur richtige Gegenständ", "argument.literal.incorrect": "Zeichnfolge '%s' erwortet", "argument.long.big": "Longe Ganzzahl doaf ned klana als %s sein, %s ist zu groß", "argument.long.low": "Longe Ganzzohl doaf ned größer ois %s sa, %s is zu groß", "argument.message.too_long": "Chat Nochricht woa zlong (%s > hechstns %s <PERSON><PERSON><PERSON>n)", "argument.nbt.array.invalid": "Ungültiga Array-Typ ‚%s‘", "argument.nbt.array.mixed": "%s konn ned in %s eigfügt werdn", "argument.nbt.expected.compound": "Vabunddatn erwoat", "argument.nbt.expected.key": "Schl<PERSON>l erwortet", "argument.nbt.expected.value": "Wert erwortet", "argument.nbt.list.mixed": "%s konn ned in de Listn von %s eigfügt werdn", "argument.nbt.trailing": "Unerwortete Folgedaten", "argument.player.entities": "<PERSON>ur Spiela kennen von dem <PERSON>fehl beeinflusst wean, oba der zur Verfügung gstellte Selektor erlaubt mehrare", "argument.player.toomany": "Es is nua oa S<PERSON>ler erlabt, oba der angegebene Selektor erlabt mehr also oan", "argument.player.unknown": "Der Spiela exestiert net", "argument.pos.missing.double": "Koordinate erwortet", "argument.pos.missing.int": "Blockposition erwortet", "argument.pos.mixed": "Absolute und relative Koordinaten diafn net gmischt wean (entweder olle oder kane benutzen \"^\")", "argument.pos.outofbounds": "De Position is ausshoib vom dalabtn Bereich.", "argument.pos.outofworld": "De Position is außer<PERSON><PERSON> der Wöd!", "argument.pos.unloaded": "De Position is net glodnt", "argument.pos2d.incomplete": "<PERSON><PERSON> (2 Koadintatn eawotet)", "argument.pos3d.incomplete": "Unvoiständig (3 Koordinaten erwortet)", "argument.range.empty": "Wert oda Wertebereich erwortet", "argument.range.ints": "<PERSON><PERSON> Gaunz<PERSON> erlau<PERSON>, kane <PERSON>", "argument.range.swapped": "Minimum deaf net greßa ois es Maximum sei", "argument.resource.invalid_type": "Element '%s' hotn foischn Typ '%s' ('%s' eawoatet)", "argument.resource.not_found": "Konn element '%s' von <PERSON> '%s' nit findn", "argument.resource_or_id.failed_to_parse": "Struktur hod ned analysiat wean kenna: %s", "argument.resource_or_id.invalid": "Ungültige ID oda Etikett", "argument.resource_or_id.no_such_element": "Des Element '%s' konn im Register '%s' ned gfundn werden", "argument.resource_selector.not_found": "<PERSON>stimmungen fia Selektor '%s' <PERSON> '%s'", "argument.resource_tag.invalid_type": "Tag '%s' hotn foischn Typ '%s' (%s eawoatet)", "argument.resource_tag.not_found": "Konn tag '%s' vom Typ '%s' nit findn", "argument.rotation.incomplete": "Unvoiständig (2 Koordinaten erwortet)", "argument.scoreHolder.empty": "<PERSON>s konn koa <PERSON>a Punktestondhoita gfundn wean", "argument.scoreboardDisplaySlot.invalid": "Unbekonnte Onzeigeposition '%s'", "argument.style.invalid": "Ungültige JSON: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON><PERSON> da Ticks deaf ned negativ sein", "argument.time.invalid_unit": "Ungültige Einheit", "argument.time.tick_count_too_low": "Tickonzohl darf nit kloaner als %s sein, %s gefundn", "argument.uuid.invalid": "Ungültiga UUID", "argument.waypoint.invalid": "De ausgwöhte Entität is ka Wegpunkt", "arguments.block.tag.unknown": "Unbekonnta Block-Tag '%s'", "arguments.function.tag.unknown": "Unbekonnta Funktionsalias ‚%s‘", "arguments.function.unknown": "Unbekonnte Funktion ‚%s‘", "arguments.item.component.expected": "Gegnstondskomponete eawoatet", "arguments.item.component.malformed": "Fehlahofte '%s'-Komponente: '%s'", "arguments.item.component.repeated": "Gegnstondskompontente '%s' wuad widaholt, es darf oba nua a Wert ongebn weadn", "arguments.item.component.unknown": "Unbekonnta Gegnstondskomponent '%s'", "arguments.item.malformed": "Fehlahofta Gegnstond: '%s'", "arguments.item.overstacked": "Maximale Stoplgreß von %s is %s", "arguments.item.predicate.malformed": "Fehlahofts '%s'-Prädikat: '%s'", "arguments.item.predicate.unknown": "Unbekonnts Gegnstondsprädikat: '%s'", "arguments.item.tag.unknown": "Unbekonnta Gegnstondsalias ‚%s‘", "arguments.nbtpath.node.invalid": "Ungültigs Element im NBT-Pfod", "arguments.nbtpath.nothing_found": "Es san koane Elemente gfundn wordn, de %s entsprechn", "arguments.nbtpath.too_deep": "Resultierenda NBT is zu tiaf vas<PERSON>lt", "arguments.nbtpath.too_large": "Resultierenda NBT is zu groß", "arguments.objective.notFound": "Unbekonnts Punktestond-Zü '%s'", "arguments.objective.readonly": "As Punktestond-Zü '%s' konn nua ausglesn wean", "arguments.operation.div0": "Konn net durch Null dividieren", "arguments.operation.invalid": "Ungütige Operation", "arguments.swizzle.invalid": "Ungültige Achsenkombination von x, y und z", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s %% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s %% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s %% %s", "attribute.name.armor": "Rüstung", "attribute.name.armor_toughness": "Rüstungshärte", "attribute.name.attack_damage": "Ongriffsschodn", "attribute.name.attack_knockback": "Ongriffs<PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_speed": "Ongriffsgschwindigkeit", "attribute.name.block_break_speed": "Blockobbautempo", "attribute.name.block_interaction_range": "Block Intaaktionsreichweite", "attribute.name.burning_time": "Brennzeit", "attribute.name.camera_distance": "Kameraentfernung", "attribute.name.entity_interaction_range": "Objekt Intaaktionsreichweite", "attribute.name.explosion_knockback_resistance": "Explosionsstondfestigkeit", "attribute.name.fall_damage_multiplier": "Foischodnmultiplikatoa", "attribute.name.flying_speed": "Fluggschwindigkeit", "attribute.name.follow_range": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor": "Rüstung", "attribute.name.generic.armor_toughness": "Rüstungshärte", "attribute.name.generic.attack_damage": "Ongriffsschodn", "attribute.name.generic.attack_knockback": "Ongriffs<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_speed": "Ongriffsgschwindigkeit", "attribute.name.generic.block_interaction_range": "Block-Erkennungsradius", "attribute.name.generic.burning_time": "Brondzeit", "attribute.name.generic.entity_interaction_range": "Objekt-Erkennungsradius", "attribute.name.generic.explosion_knockback_resistance": "Explosionsstondfestigkeit", "attribute.name.generic.fall_damage_multiplier": "Follschodn Vervüfacher", "attribute.name.generic.flying_speed": "Fluggschwindigkeit", "attribute.name.generic.follow_range": "Kreaturn-Folgedistanz", "attribute.name.generic.gravity": "Schweakroft", "attribute.name.generic.jump_strength": "Sprungstärke", "attribute.name.generic.knockback_resistance": "Stondfestigkeit", "attribute.name.generic.luck": "Glück", "attribute.name.generic.max_absorption": "Maximale Absorption", "attribute.name.generic.max_health": "Maximale Gsundheit", "attribute.name.generic.movement_efficiency": "Bewegungseffizienz", "attribute.name.generic.movement_speed": "Gschwindigkeit", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "Sichare-<PERSON>ö<PERSON>", "attribute.name.generic.scale": "Größe", "attribute.name.generic.step_height": "Schritthöhe", "attribute.name.generic.water_movement_efficiency": "Wossa‐Bewegungseffizienz", "attribute.name.gravity": "Schweakroft", "attribute.name.horse.jump_strength": "Roßsprungstärke", "attribute.name.jump_strength": "Sprungstärkn", "attribute.name.knockback_resistance": "Stondfestigkeit", "attribute.name.luck": "Glück", "attribute.name.max_absorption": "Maximale Absorption", "attribute.name.max_health": "Maximale Gsundheit", "attribute.name.mining_efficiency": "<PERSON><PERSON>ue<PERSON><PERSON><PERSON><PERSON>", "attribute.name.movement_efficiency": "Bewegungseffizienz", "attribute.name.movement_speed": "Gschwindigkeit", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blockobbautempo", "attribute.name.player.block_interaction_range": "Block-Erkennungsradius", "attribute.name.player.entity_interaction_range": "Objekt-Erkennungsradius", "attribute.name.player.mining_efficiency": "<PERSON><PERSON>ue<PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "Schleichgeschwindigkeit", "attribute.name.player.submerged_mining_speed": "Unterwosser‐Obbaugschwindigkeit", "attribute.name.player.sweeping_damage_ratio": "Schwungschodensverhötnis", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Schleichgschwindigkeit", "attribute.name.spawn_reinforcements": "Zombievastärkungen", "attribute.name.step_height": "Schritthöhe", "attribute.name.submerged_mining_speed": "Untawossa Obbaugschwindigkeit", "attribute.name.sweeping_damage_ratio": "Schwungschodensverhötnis", "attribute.name.tempt_range": "Kreaturen‐Onlockdistanz", "attribute.name.water_movement_efficiency": "Wossabewegungseffizienz", "attribute.name.waypoint_receive_range": "Wegpunkt Empfongsreichweite", "attribute.name.waypoint_transmit_range": "Wegpunkt Sendereichweite", "attribute.name.zombie.spawn_reinforcements": "Zombie-Vastärkung", "biome.minecraft.badlands": "Tofeberg", "biome.minecraft.bamboo_jungle": "Bambusdschungl", "biome.minecraft.basalt_deltas": "Basaltdeltas", "biome.minecraft.beach": "Strond", "biome.minecraft.birch_forest": "Birknwoid", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "<PERSON>chta W<PERSON>", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Tropfstoahöhln", "biome.minecraft.end_barrens": "End-Korglond", "biome.minecraft.end_highlands": "End-Hochlond", "biome.minecraft.end_midlands": "End-<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.eroded_badlands": "Obtrogene To<PERSON>berg", "biome.minecraft.flower_forest": "Blumenwoid", "biome.minecraft.forest": "Woid", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "G<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Eiszopfntundra", "biome.minecraft.jagged_peaks": "Zaklüftete Gipfl", "biome.minecraft.jungle": "Dschungl", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "Üppige Höhln", "biome.minecraft.mangrove_swamp": "Man<PERSON>v<PERSON>ump<PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Püzlond", "biome.minecraft.nether_wastes": "Nether-Ödl<PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Birkn-Urwoid", "biome.minecraft.old_growth_pine_taiga": "Kiefa-Urtaiga", "biome.minecraft.old_growth_spruce_taiga": "Fichtn-Urtaiga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Fluss", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Savannenhochebene", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_beach": "Vaschneita <PERSON>d", "biome.minecraft.snowy_plains": "Vaschneite Ebene", "biome.minecraft.snowy_slopes": "Vaschneite Häng", "biome.minecraft.snowy_taiga": "Vaschneite Taiga", "biome.minecraft.soul_sand_valley": "Seelnsondtol", "biome.minecraft.sparse_jungle": "Liachta Dschungl", "biome.minecraft.stony_peaks": "Fösgipfl", "biome.minecraft.stony_shore": "Fösküstn", "biome.minecraft.sunflower_plains": "Sonnenblumenebene", "biome.minecraft.swamp": "Sumpf", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON>", "biome.minecraft.the_void": "<PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.warped_forest": "Wirr<PERSON>id", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "Zerzauste Geröllhiagel", "biome.minecraft.windswept_hills": "Windverwehde Higl", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "Bewoidete Toflberg", "block.minecraft.acacia_button": "Akazienhoizknopf", "block.minecraft.acacia_door": "Akazienhoiztir", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "Akazienhoizzauntor", "block.minecraft.acacia_hanging_sign": "Akazienhängeschüd", "block.minecraft.acacia_leaves": "Akazienlab", "block.minecraft.acacia_log": "Akazienstomm", "block.minecraft.acacia_planks": "Akazienhoizbrettl", "block.minecraft.acacia_pressure_plate": "Akazeinhoizdruckplottn", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_sign": "Akazienhoizschüd", "block.minecraft.acacia_slab": "Akazienhoizstufn", "block.minecraft.acacia_stairs": "Akazienhoiztreppn", "block.minecraft.acacia_trapdoor": "Akazienhoizfoitir", "block.minecraft.acacia_wall_hanging_sign": "Akazienwondhängeschüd", "block.minecraft.acacia_wall_sign": "Akazienhoizwondschüd", "block.minecraft.acacia_wood": "Akazienhoiz", "block.minecraft.activator_rail": "Aktivierungsschiene", "block.minecraft.air": "Luft", "block.minecraft.allium": "<PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Amethyst-Block", "block.minecraft.amethyst_cluster": "Amethysthaufn", "block.minecraft.ancient_debris": "<PERSON><PERSON>", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Andesitstufn", "block.minecraft.andesite_stairs": "Andestittreppn", "block.minecraft.andesite_wall": "Andesitmaua", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Melonenronkn", "block.minecraft.attached_pumpkin_stem": "Kürbisronkn", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "Azaleenlab", "block.minecraft.azure_bluet": "Porz<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblock", "block.minecraft.bamboo_button": "Bambusknopf", "block.minecraft.bamboo_door": "Bambustir", "block.minecraft.bamboo_fence": "Bambuszaun", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_hanging_sign": "Bambushängeschüd", "block.minecraft.bamboo_mosaic": "Bambusmosaik", "block.minecraft.bamboo_mosaic_slab": "Bambusmosaikstufn", "block.minecraft.bamboo_mosaic_stairs": "Bambusmosaiktreppn", "block.minecraft.bamboo_planks": "Bambusbrettl", "block.minecraft.bamboo_pressure_plate": "Bambusdruckplottn", "block.minecraft.bamboo_sapling": "Bambussprössling", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON>chüd", "block.minecraft.bamboo_slab": "Bambusstufn", "block.minecraft.bamboo_stairs": "Bambustreppn", "block.minecraft.bamboo_trapdoor": "Bambusfoitir", "block.minecraft.bamboo_wall_hanging_sign": "Bambuswondhängeschüd", "block.minecraft.bamboo_wall_sign": "Bamb<PERSON><PERSON><PERSON>chüd", "block.minecraft.banner.base.black": "Komplett schworzs Föd", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.base.brown": "Komplett brauns Föd", "block.minecraft.banner.base.cyan": "Komplett türkises Föd", "block.minecraft.banner.base.gray": "Ko<PERSON>tt graus F<PERSON>", "block.minecraft.banner.base.green": "Komplett grüns <PERSON>", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON> hellblaus Föd", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON> hellgra<PERSON> F<PERSON>", "block.minecraft.banner.base.lime": "Komplett hellgrüns <PERSON>", "block.minecraft.banner.base.magenta": "Komplett magentas Föd", "block.minecraft.banner.base.orange": "Komplett oranges Föd", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON> rosa<PERSON>", "block.minecraft.banner.base.purple": "Komplett lilas <PERSON>", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON> rots Föd", "block.minecraft.banner.base.white": "Komplett weißes Föd", "block.minecraft.banner.base.yellow": "Komplett gelbs Föd", "block.minecraft.banner.border.black": "Schworza Bo<PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON>", "block.minecraft.banner.border.cyan": "Türk<PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "Oranga Bord", "block.minecraft.banner.border.pink": "<PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "Rota Bord", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON> sch<PERSON> g<PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON> blau <PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> braun g<PERSON>uad", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON> grau g<PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON> grün g<PERSON>", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.magenta": "Föd magenta gmauad", "block.minecraft.banner.bricks.orange": "Föd orange gmauad", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> rosa g<PERSON>", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON> lila <PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON> rot g<PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON> weiß g<PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> gelb g<PERSON>", "block.minecraft.banner.circle.black": "Schworze Kugl", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Türkise Kugl", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Hellblaue Kugl", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Orange Kugl", "block.minecraft.banner.circle.pink": "<PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "Rote Kugl", "block.minecraft.banner.circle.white": "Weiße Kugl", "block.minecraft.banner.circle.yellow": "Gelbe <PERSON>l", "block.minecraft.banner.creeper.black": "Schworza <PERSON>ree<PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Türkisa <PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "Hellblaua <PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "Oranga Creeper", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON>", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON>", "block.minecraft.banner.creeper.red": "Rota Creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Schworzs <PERSON>", "block.minecraft.banner.cross.blue": "<PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Türkises Andreaskreiz", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "Hell<PERSON>r<PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.cross.red": "Rots Andreas<PERSON>iz", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "Gelbs Andreaskreiz", "block.minecraft.banner.curly_border.black": "Schworza Spicklbord", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.cyan": "Türkisa Spicklbord", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "Hellblaua Spicklbord", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON>lbord", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.orange": "Oranga Spicklbord", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.red": "Rota Spicklbord", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON>lbor<PERSON>", "block.minecraft.banner.curly_border.yellow": "Gelba <PERSON>lbord", "block.minecraft.banner.diagonal_left.black": "Schworz schräglinks teilt", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON> schräglinks teilt", "block.minecraft.banner.diagonal_left.brown": "<PERSON> schräglinks teilt", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON><PERSON> schräglinks teilt", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> teilt", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON><PERSON> teilt", "block.minecraft.banner.diagonal_left.light_blue": "Hellblau schräglinks teilt", "block.minecraft.banner.diagonal_left.light_gray": "Hellgrau schräglinks teilt", "block.minecraft.banner.diagonal_left.lime": "Hellgr<PERSON>n schräglinks teilt", "block.minecraft.banner.diagonal_left.magenta": "Magenta schräglinks teilt", "block.minecraft.banner.diagonal_left.orange": "Orange schräglinks teilt", "block.minecraft.banner.diagonal_left.pink": "<PERSON> s<PERSON> teilt", "block.minecraft.banner.diagonal_left.purple": "<PERSON> s<PERSON>nks teilt", "block.minecraft.banner.diagonal_left.red": "<PERSON>ot schräglinks teilt", "block.minecraft.banner.diagonal_left.white": "<PERSON>ß schräglinks teilt", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> teilt", "block.minecraft.banner.diagonal_right.black": "Schworz schrägrechts teilt", "block.minecraft.banner.diagonal_right.blue": "Blau schrägrechts teilt", "block.minecraft.banner.diagonal_right.brown": "Braun schrägrechts teilt", "block.minecraft.banner.diagonal_right.cyan": "Türkis schrägrechts teilt", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON> schrägrechts teilt", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON> schrägrechts teilt", "block.minecraft.banner.diagonal_right.light_blue": "Hellblau schrägrechts teilt", "block.minecraft.banner.diagonal_right.light_gray": "Hellgrau schrägrechts teilt", "block.minecraft.banner.diagonal_right.lime": "Hellgrün schrägrechts teilt", "block.minecraft.banner.diagonal_right.magenta": "Magenta schrägrechts teilt", "block.minecraft.banner.diagonal_right.orange": "Orange schrägrechts teilt", "block.minecraft.banner.diagonal_right.pink": "<PERSON> s<PERSON>s teilt", "block.minecraft.banner.diagonal_right.purple": "<PERSON> s<PERSON>s teilt", "block.minecraft.banner.diagonal_right.red": "Rot schrägrechts teilt", "block.minecraft.banner.diagonal_right.white": "Weiß schrägrechts teilt", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON> schrägrechts teilt", "block.minecraft.banner.diagonal_up_left.black": "Schworz schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.blue": "Blau schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON> schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.cyan": "Türkis schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.gray": "G<PERSON><PERSON> schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON>grechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.light_blue": "Hellblau schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.light_gray": "Hellgrau schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.lime": "Hellgrün schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.orange": "Orange schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON> s<PERSON>r<PERSON> teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON> s<PERSON>s teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.red": "Rot schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.white": "<PERSON>ß schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON> schrägrechts teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.black": "Schworz schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON> schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON> schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.cyan": "Türkis schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON> teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.light_blue": "Hellblau schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.light_gray": "Hellgrau schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.lime": "Hellgrün schr<PERSON>glinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.orange": "Orange schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON> s<PERSON> teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON> s<PERSON> teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.red": "Rot schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.white": "<PERSON>ß schräglinks teilt (Invertiert)", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> teilt (Invertiert)", "block.minecraft.banner.flow.black": "Schwoaza Fluss", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.brown": "<PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "Türkisa Fluss", "block.minecraft.banner.flow.gray": "G<PERSON><PERSON>", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_blue": "Höblaua Flus<PERSON>", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.magenta": "Magenta Fluss", "block.minecraft.banner.flow.orange": "Oranga Fluss", "block.minecraft.banner.flow.pink": "Pink<PERSON> Fluss", "block.minecraft.banner.flow.purple": "<PERSON><PERSON>", "block.minecraft.banner.flow.red": "Rota Fluss", "block.minecraft.banner.flow.white": "<PERSON>ßa Fluss", "block.minecraft.banner.flow.yellow": "Göba Fluss", "block.minecraft.banner.flower.black": "Schworze Blume", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON>ü<PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON> Blu<PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.orange": "Orange Blume", "block.minecraft.banner.flower.pink": "<PERSON><PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON>", "block.minecraft.banner.flower.white": "Weiße Blume", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "Schworza Globus", "block.minecraft.banner.globe.blue": "Blaua Globus", "block.minecraft.banner.globe.brown": "Brauna Globus", "block.minecraft.banner.globe.cyan": "Türkisa Globus", "block.minecraft.banner.globe.gray": "Graua Globus", "block.minecraft.banner.globe.green": "Grüna Glo<PERSON>", "block.minecraft.banner.globe.light_blue": "Hellblaua Globus", "block.minecraft.banner.globe.light_gray": "Hellgraua Globus", "block.minecraft.banner.globe.lime": "Hellgrüna Globus", "block.minecraft.banner.globe.magenta": "Magentana Globus", "block.minecraft.banner.globe.orange": "Oranga Globus", "block.minecraft.banner.globe.pink": "Rosana Globus", "block.minecraft.banner.globe.purple": "Lilana Globus", "block.minecraft.banner.globe.red": "Rota Globus", "block.minecraft.banner.globe.white": "Weißa Globus", "block.minecraft.banner.globe.yellow": "Gelba Globus", "block.minecraft.banner.gradient.black": "Schworza <PERSON>auf", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Türkisa Forbvalauf", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Hell<PERSON><PERSON>", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.orange": "Oranga Forbvalauf", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient.red": "Rota Forbvalauf", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "Schworz<PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.cyan": "Türkisa Forbvalauf (Invertiert)", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.light_blue": "Hellblaua <PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.orange": "Oranga Forbvalauf (Invertiert)", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.red": "Rota Forbvalauf (Invertiert)", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.guster.black": "Schoaza Windstoßa", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "Türkisa <PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Oranga Windstoßa", "block.minecraft.banner.guster.pink": "<PERSON><PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "Rota Windstoßa", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "Obn schworz teilt", "block.minecraft.banner.half_horizontal.blue": "Obn blau teilt", "block.minecraft.banner.half_horizontal.brown": "Obn braun teilt", "block.minecraft.banner.half_horizontal.cyan": "Obn türkis teilt", "block.minecraft.banner.half_horizontal.gray": "Obn grau teilt", "block.minecraft.banner.half_horizontal.green": "Obn grün teilt", "block.minecraft.banner.half_horizontal.light_blue": "Obn hellblau teilt", "block.minecraft.banner.half_horizontal.light_gray": "Obn hell<PERSON>u teilt", "block.minecraft.banner.half_horizontal.lime": "Obn hellgrün teilt", "block.minecraft.banner.half_horizontal.magenta": "Obn magenta teilt", "block.minecraft.banner.half_horizontal.orange": "Obn orange teilt", "block.minecraft.banner.half_horizontal.pink": "Obn rosa teilt", "block.minecraft.banner.half_horizontal.purple": "Obn lila teilt", "block.minecraft.banner.half_horizontal.red": "Obn rot teilt", "block.minecraft.banner.half_horizontal.white": "Obn weiß teilt", "block.minecraft.banner.half_horizontal.yellow": "Obn gelb teilt", "block.minecraft.banner.half_horizontal_bottom.black": "Untn schworz teilt", "block.minecraft.banner.half_horizontal_bottom.blue": "Untn blau teilt", "block.minecraft.banner.half_horizontal_bottom.brown": "Untn braun teilt", "block.minecraft.banner.half_horizontal_bottom.cyan": "Untn türkis teilt", "block.minecraft.banner.half_horizontal_bottom.gray": "Untn grau teilt", "block.minecraft.banner.half_horizontal_bottom.green": "Untn grün teilt", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Untn hellblau teilt", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Untn hellgrau teilt", "block.minecraft.banner.half_horizontal_bottom.lime": "Untn hellgrün teilt", "block.minecraft.banner.half_horizontal_bottom.magenta": "Untn magenta teilt", "block.minecraft.banner.half_horizontal_bottom.orange": "Untn orange teilt", "block.minecraft.banner.half_horizontal_bottom.pink": "Untn rosa teilt", "block.minecraft.banner.half_horizontal_bottom.purple": "Untn lila teilt", "block.minecraft.banner.half_horizontal_bottom.red": "Untn rot teilt", "block.minecraft.banner.half_horizontal_bottom.white": "Untn weiß teilt", "block.minecraft.banner.half_horizontal_bottom.yellow": "Untn gelb teilt", "block.minecraft.banner.half_vertical.black": "Rechts schworz gspoitn", "block.minecraft.banner.half_vertical.blue": "Rechts blau gspoitn", "block.minecraft.banner.half_vertical.brown": "Rechts braun gspoitn", "block.minecraft.banner.half_vertical.cyan": "Rechts türkis gspoitn", "block.minecraft.banner.half_vertical.gray": "Rechts grau gspoitn", "block.minecraft.banner.half_vertical.green": "Rechts grün g<PERSON>n", "block.minecraft.banner.half_vertical.light_blue": "Rechts hellblau gspoitn", "block.minecraft.banner.half_vertical.light_gray": "Rechts hellgrau gspoitn", "block.minecraft.banner.half_vertical.lime": "Rechts hellgrün g<PERSON>n", "block.minecraft.banner.half_vertical.magenta": "Rechts magenta gspoitn", "block.minecraft.banner.half_vertical.orange": "Rechts orange gspoitn", "block.minecraft.banner.half_vertical.pink": "Rechts rosa gspoitn", "block.minecraft.banner.half_vertical.purple": "Rechts lila g<PERSON>n", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> rot gspoitn", "block.minecraft.banner.half_vertical.white": "Rechts weiß g<PERSON>n", "block.minecraft.banner.half_vertical.yellow": "Rechts gelb gspoitn", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON> schworz gspoitn", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> blau <PERSON><PERSON><PERSON>n", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON> braun gspoitn", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON> türk<PERSON> g<PERSON>n", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON> grau g<PERSON>n", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON> gr<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON> <PERSON><PERSON><PERSON> g<PERSON>n", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON> <PERSON><PERSON><PERSON> g<PERSON>n", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "Links magenta gspoitn", "block.minecraft.banner.half_vertical_right.orange": "Links orange gspoitn", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON> rosa g<PERSON>n", "block.minecraft.banner.half_vertical_right.purple": "<PERSON>s lila <PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON> rot gspoitn", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON> weiß g<PERSON>n", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON>s gelb g<PERSON><PERSON>n", "block.minecraft.banner.mojang.black": "Schworzs Mojang-Logo", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "Brauns <PERSON>-Logo", "block.minecraft.banner.mojang.cyan": "Türkises Mojang-Logo", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "Hellgr<PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "Magentas <PERSON>", "block.minecraft.banner.mojang.orange": "Orangs <PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>", "block.minecraft.banner.mojang.red": "Rots Mojang-Logo", "block.minecraft.banner.mojang.white": "Weißs Mojang-Logo", "block.minecraft.banner.mojang.yellow": "Gelbs Mojang-Logo", "block.minecraft.banner.piglin.black": "Schworze Schnauzn", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "Türkise Schnauzn", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Hellblaue Schnauzn", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.orange": "Orange Schnauzn", "block.minecraft.banner.piglin.pink": "<PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON>", "block.minecraft.banner.piglin.red": "Rote Schnauzn", "block.minecraft.banner.piglin.white": "Weiße Schnauzn", "block.minecraft.banner.piglin.yellow": "Gelbe Schnauzn", "block.minecraft.banner.rhombus.black": "Schworze Rautn", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "Türk<PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Orange Rautn", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "Weiße Rautn", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.black": "Schworz<PERSON>dl", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "Türkisa Schädl", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "Hellblaua Schädl", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "Oranga Schädl", "block.minecraft.banner.skull.pink": "<PERSON><PERSON>", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>", "block.minecraft.banner.skull.red": "Rota Schädl", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "Vier schworze Pfoi", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON> blaue <PERSON>", "block.minecraft.banner.small_stripes.brown": "Vier braune Pfoi", "block.minecraft.banner.small_stripes.cyan": "Vier türkise Pfoi", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON> graue Pfoi", "block.minecraft.banner.small_stripes.green": "<PERSON>ier gr<PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON>blaue Pfoi", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON> Pfoi", "block.minecraft.banner.small_stripes.lime": "<PERSON>ier hellgrü<PERSON>", "block.minecraft.banner.small_stripes.magenta": "Vier magentane Pfoi", "block.minecraft.banner.small_stripes.orange": "Vier orange Pfoi", "block.minecraft.banner.small_stripes.pink": "Vier rosane <PERSON>", "block.minecraft.banner.small_stripes.purple": "Vier lilane Pfoi", "block.minecraft.banner.small_stripes.red": "Vier rote Pfoi", "block.minecraft.banner.small_stripes.white": "Vier weiße Pfoi", "block.minecraft.banner.small_stripes.yellow": "Vier gelbe Pfoi", "block.minecraft.banner.square_bottom_left.black": "Schworzs rechts Untaegg", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON> Untaegg", "block.minecraft.banner.square_bottom_left.brown": "Brauns rechts Untaegg", "block.minecraft.banner.square_bottom_left.cyan": "Türkises rechts Untaegg", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> rechts Untaegg", "block.minecraft.banner.square_bottom_left.green": "Gr<PERSON>ns rechts Untaegg", "block.minecraft.banner.square_bottom_left.light_blue": "Hellblaus rechts Untaegg", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON> rechts Untaegg", "block.minecraft.banner.square_bottom_left.lime": "Hellgrüns rechts Untaegg", "block.minecraft.banner.square_bottom_left.magenta": "Magentas rechts Untaegg", "block.minecraft.banner.square_bottom_left.orange": "Orangs rechts Untaegg", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON> rechts Untaegg", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON> rechts Untaegg", "block.minecraft.banner.square_bottom_left.red": "Rots rechts Untaegg", "block.minecraft.banner.square_bottom_left.white": "Weißs rechts Untaegg", "block.minecraft.banner.square_bottom_left.yellow": "Gelbs rechts Untaegg", "block.minecraft.banner.square_bottom_right.black": "Schworzs links Untaegg", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON> links Untaegg", "block.minecraft.banner.square_bottom_right.brown": "Brauns links Untaegg", "block.minecraft.banner.square_bottom_right.cyan": "Türkises links Untaegg", "block.minecraft.banner.square_bottom_right.gray": "Graus links Untaegg", "block.minecraft.banner.square_bottom_right.green": "Grüns links Untaegg", "block.minecraft.banner.square_bottom_right.light_blue": "Hellblaus links Untaegg", "block.minecraft.banner.square_bottom_right.light_gray": "Hellgraus links Untaegg", "block.minecraft.banner.square_bottom_right.lime": "Hellgrüns links Untaegg", "block.minecraft.banner.square_bottom_right.magenta": "Magentas links Untaegg", "block.minecraft.banner.square_bottom_right.orange": "Orangs links Untaegg", "block.minecraft.banner.square_bottom_right.pink": "Rosas links Untaegg", "block.minecraft.banner.square_bottom_right.purple": "Lilas links Untaegg", "block.minecraft.banner.square_bottom_right.red": "Rots links Untaegg", "block.minecraft.banner.square_bottom_right.white": "Weißs links Untaegg", "block.minecraft.banner.square_bottom_right.yellow": "Gelbs links Untaegg", "block.minecraft.banner.square_top_left.black": "Schworzs rechts Obaegg", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON> Obaegg", "block.minecraft.banner.square_top_left.brown": "Brauns rechts Obaegg", "block.minecraft.banner.square_top_left.cyan": "Türkises rechts Obaegg", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> rechts Obaegg", "block.minecraft.banner.square_top_left.green": "G<PERSON><PERSON>ns rechts Obaegg", "block.minecraft.banner.square_top_left.light_blue": "Hell<PERSON>us rechts Obaegg", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON> rechts Obaegg", "block.minecraft.banner.square_top_left.lime": "Hellgrüns rechts Obaegg", "block.minecraft.banner.square_top_left.magenta": "Magentas rechts Obaegg", "block.minecraft.banner.square_top_left.orange": "Orangs rechts Obaegg", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON> re<PERSON>s Obaegg", "block.minecraft.banner.square_top_left.purple": "<PERSON><PERSON> rechts Obaegg", "block.minecraft.banner.square_top_left.red": "Rots rechts Obaegg", "block.minecraft.banner.square_top_left.white": "Weißs rechts Obaegg", "block.minecraft.banner.square_top_left.yellow": "Gelbs rechts Obaegg", "block.minecraft.banner.square_top_right.black": "Schworzs links Obaegg", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON> links Obaegg", "block.minecraft.banner.square_top_right.brown": "Brauns links Obaegg", "block.minecraft.banner.square_top_right.cyan": "Türkises links Obaegg", "block.minecraft.banner.square_top_right.gray": "Graus links Obaegg", "block.minecraft.banner.square_top_right.green": "Grüns links Obaegg", "block.minecraft.banner.square_top_right.light_blue": "Hellblaus links Obaegg", "block.minecraft.banner.square_top_right.light_gray": "Hellgraus links Obaegg", "block.minecraft.banner.square_top_right.lime": "Hellgrüns links Obaegg", "block.minecraft.banner.square_top_right.magenta": "Magentas links Obaegg", "block.minecraft.banner.square_top_right.orange": "Orangs links Obaegg", "block.minecraft.banner.square_top_right.pink": "Rosas links Obaegg", "block.minecraft.banner.square_top_right.purple": "Lilas links Obaegg", "block.minecraft.banner.square_top_right.red": "Rots links Obaegg", "block.minecraft.banner.square_top_right.white": "Weißs links Obaegg", "block.minecraft.banner.square_top_right.yellow": "Gelbs links Obaegg", "block.minecraft.banner.straight_cross.black": "Schworzs Kreiz", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON> Kreiz", "block.minecraft.banner.straight_cross.cyan": "Türkises Kreiz", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "Hellgrüns K<PERSON>", "block.minecraft.banner.straight_cross.magenta": "Ma<PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "Orang<PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "Rots Kreiz", "block.minecraft.banner.straight_cross.white": "Weißs Kreiz", "block.minecraft.banner.straight_cross.yellow": "Gelbs Kreiz", "block.minecraft.banner.stripe_bottom.black": "Schworza Ban<PERSON>ß", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Türkisa <PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Hellblaua <PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "Oranga Bannafuaß", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "Rota Bannafuaß", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "Schworza Pfoi", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "Brauna Pfoi", "block.minecraft.banner.stripe_center.cyan": "Türkisa Pfoi", "block.minecraft.banner.stripe_center.gray": "G<PERSON>ua <PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "Hellblaua Pfoi", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.lime": "Hell<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "Ma<PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "Oranga Pfoi", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "Rota Pfoi", "block.minecraft.banner.stripe_center.white": "<PERSON>ßa Pfoi", "block.minecraft.banner.stripe_center.yellow": "Gelba <PERSON>i", "block.minecraft.banner.stripe_downleft.black": "Schworza Schräglinksboikn", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON> Schräglinksboikn", "block.minecraft.banner.stripe_downleft.cyan": "Türkisa Schräglinksboikn", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON><PERSON>glinksbo<PERSON>n", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Hellblaua Schräglinksboikn", "block.minecraft.banner.stripe_downleft.light_gray": "Hellgraua Schräglinksboikn", "block.minecraft.banner.stripe_downleft.lime": "Hellgr<PERSON>na <PERSON>äglinksboikn", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON>glinksboikn", "block.minecraft.banner.stripe_downleft.orange": "Oranga Schräglinksboikn", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "Rota Schräglinksboikn", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON> Schräglinksboikn", "block.minecraft.banner.stripe_downleft.yellow": "G<PERSON>ba Schräglinksboikn", "block.minecraft.banner.stripe_downright.black": "Schworza Schrägboikn", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON> Schrägboikn", "block.minecraft.banner.stripe_downright.cyan": "Türkisa Schrägboikn", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Hellblaua Schrägboikn", "block.minecraft.banner.stripe_downright.light_gray": "Hell<PERSON>ua <PERSON>hrägboikn", "block.minecraft.banner.stripe_downright.lime": "Hell<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "Ma<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "Oranga Schrägboikn", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "Rota Schrägboikn", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON> Schrägboikn", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON><PERSON>hrägboikn", "block.minecraft.banner.stripe_left.black": "Schworze rechte Flonkn", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON> rechte Flonkn", "block.minecraft.banner.stripe_left.brown": "Braune rechte Flonkn", "block.minecraft.banner.stripe_left.cyan": "Türkise rechte Flonkn", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON> rechte Flonkn", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON><PERSON> rechte Flonkn", "block.minecraft.banner.stripe_left.light_blue": "Hellblaue rechte Flonkn", "block.minecraft.banner.stripe_left.light_gray": "Hell<PERSON><PERSON> rechte Flonkn", "block.minecraft.banner.stripe_left.lime": "Hellgr<PERSON>ne rechte Flonkn", "block.minecraft.banner.stripe_left.magenta": "Magentane rechte Flonkn", "block.minecraft.banner.stripe_left.orange": "Orange rechte Flonkn", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON> rechte Flonkn", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON> rechte Flonkn", "block.minecraft.banner.stripe_left.red": "Rote rechte Flonkn", "block.minecraft.banner.stripe_left.white": "Weiße rechte Flonkn", "block.minecraft.banner.stripe_left.yellow": "Gelbe rechte Flonkn", "block.minecraft.banner.stripe_middle.black": "Schworza Boikn", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Türkisa Boikn", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Hell<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "Oranga Boikn", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "Rota Boikn", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Schworze linke Flonkn", "block.minecraft.banner.stripe_right.blue": "Blaue linke Flonkn", "block.minecraft.banner.stripe_right.brown": "Braune linke Flonkn", "block.minecraft.banner.stripe_right.cyan": "Türkise linke Flonkn", "block.minecraft.banner.stripe_right.gray": "Graue linke Flonkn", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON>ne linke Flonkn", "block.minecraft.banner.stripe_right.light_blue": "Hellblaue linke Flonkn", "block.minecraft.banner.stripe_right.light_gray": "Hellgraue linke Flonkn", "block.minecraft.banner.stripe_right.lime": "Hellgrüne linke Flonkn", "block.minecraft.banner.stripe_right.magenta": "Magentane linke Flonkn", "block.minecraft.banner.stripe_right.orange": "Orange linke Flonkn", "block.minecraft.banner.stripe_right.pink": "Rosane linke Flonkn", "block.minecraft.banner.stripe_right.purple": "Lilane linke Flonkn", "block.minecraft.banner.stripe_right.red": "Rote linke Flonkn", "block.minecraft.banner.stripe_right.white": "Weiße linke Flonkn", "block.minecraft.banner.stripe_right.yellow": "Gelbe linke Flonkn", "block.minecraft.banner.stripe_top.black": "Schworza Bannakopf", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "Türkisa Bannakopf", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Hellblaua Ban<PERSON>kopf", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Oranga Bannakopf", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "Rota Bannakopf", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "Gelba <PERSON>", "block.minecraft.banner.triangle_bottom.black": "Schworze hoibe Spitzn", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON> hoibe Spitzn", "block.minecraft.banner.triangle_bottom.brown": "Braune hoibe Spitzn", "block.minecraft.banner.triangle_bottom.cyan": "Türkise hoibe Spitzn", "block.minecraft.banner.triangle_bottom.gray": "G<PERSON>ue hoibe Spitzn", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Spitzn", "block.minecraft.banner.triangle_bottom.light_blue": "Hellblaue hoibe Spitzn", "block.minecraft.banner.triangle_bottom.light_gray": "Hellgraue hoibe Spitzn", "block.minecraft.banner.triangle_bottom.lime": "Hellgrüne hoibe Spitzn", "block.minecraft.banner.triangle_bottom.magenta": "Magentane hoibe Spitzn", "block.minecraft.banner.triangle_bottom.orange": "Orange hoibe Spitzn", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON> ho<PERSON> Spitzn", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON> hoibe Spitzn", "block.minecraft.banner.triangle_bottom.red": "Rote hoibe Spitzn", "block.minecraft.banner.triangle_bottom.white": "Weiße hoibe Spitzn", "block.minecraft.banner.triangle_bottom.yellow": "Gelbe hoibe Spitzn", "block.minecraft.banner.triangle_top.black": "Schworze gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.blue": "Blaue gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.brown": "Braune gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.cyan": "Türkise gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.gray": "Graue gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.green": "Grüne gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.light_blue": "Hellblaue gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.light_gray": "Hellgraue gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.lime": "Hellgrüne gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.magenta": "Magentane gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.orange": "Orange gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.pink": "Rosane gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.purple": "Lilane gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.red": "Rote gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.white": "Weiße gstürzte hoibe Spitzn", "block.minecraft.banner.triangle_top.yellow": "Gelbe gstürzte hoibe Spitzn", "block.minecraft.banner.triangles_bottom.black": "Schworza gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "Türkisa gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON>ua g<PERSON> Bannafu<PERSON>ß", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Hellblaua gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.light_gray": "Hellgraua gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.lime": "Hellgrüna g<PERSON>lta Bannafu<PERSON>ß", "block.minecraft.banner.triangles_bottom.magenta": "Magentana g<PERSON>", "block.minecraft.banner.triangles_bottom.orange": "Oranga gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.red": "Rota gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.white": "<PERSON>ßa gspicklta Bannafuaß", "block.minecraft.banner.triangles_bottom.yellow": "Gelba gspicklta Ban<PERSON>ß", "block.minecraft.banner.triangles_top.black": "Schworzs gspicklta Bannakopf", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> g<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.cyan": "Türkises gspicklta Bannakopf", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON>kopf", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON>us g<PERSON>", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_top.lime": "Hellgrüns gspicklta Bannakopf", "block.minecraft.banner.triangles_top.magenta": "Magentas gspicklta Bannakopf", "block.minecraft.banner.triangles_top.orange": "Orangs gsp<PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.red": "Rots gspicklta Bannakopf", "block.minecraft.banner.triangles_top.white": "Weißs gspicklta Bannakopf", "block.minecraft.banner.triangles_top.yellow": "Gelbs gspicklta Bannakopf", "block.minecraft.barrel": "<PERSON><PERSON>", "block.minecraft.barrier": "Barriere", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Leichtfeia", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bed.no_sleep": "Du konnst nur nochts oder während eines Gewitters schlofn", "block.minecraft.bed.not_safe": "Du kust jetz nu ned sch<PERSON>n; es san Monsta in da Nähe", "block.minecraft.bed.obstructed": "<PERSON> is blockiat", "block.minecraft.bed.occupied": "<PERSON> is scho b'setzt", "block.minecraft.bed.too_far_away": "Du kost jetz ned schlofn; as <PERSON><PERSON> is zu weit weg", "block.minecraft.bedrock": "G<PERSON><PERSON>to<PERSON>", "block.minecraft.bee_nest": "B<PERSON>ennest", "block.minecraft.beehive": "Bienenstock", "block.minecraft.beetroots": "Rote Bete", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Großes Tropfblatt", "block.minecraft.big_dripleaf_stem": "Großer Tropfblattstiel", "block.minecraft.birch_button": "Birknhoizknopf", "block.minecraft.birch_door": "Birknhoiztir", "block.minecraft.birch_fence": "B<PERSON>nhoizzaun", "block.minecraft.birch_fence_gate": "Birknhoizzauntor", "block.minecraft.birch_hanging_sign": "Birknhängeschüd", "block.minecraft.birch_leaves": "Birknlab", "block.minecraft.birch_log": "Birknstomm", "block.minecraft.birch_planks": "Birknhoizbrettl", "block.minecraft.birch_pressure_plate": "Birknhoizdruckplottn", "block.minecraft.birch_sapling": "Birknsetzling", "block.minecraft.birch_sign": "Birknhoizschüd", "block.minecraft.birch_slab": "Birknhoizstufn", "block.minecraft.birch_stairs": "Birknhoiztreppn", "block.minecraft.birch_trapdoor": "Birknhoizfoitir", "block.minecraft.birch_wall_hanging_sign": "Birknwondhängeschüd", "block.minecraft.birch_wall_sign": "Birknhoizwondschüd", "block.minecraft.birch_wood": "Birknhoiz", "block.minecraft.black_banner": "Schworzs Banna", "block.minecraft.black_bed": "Schworzs Bett", "block.minecraft.black_candle": "Schworze Kerzn", "block.minecraft.black_candle_cake": "Kuchn mit schworza Kerzn", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete_powder": "Schworza Trocknbeton", "block.minecraft.black_glazed_terracotta": "Schworz glosiate Keramik", "block.minecraft.black_shulker_box": "Schworze Shulkerkistn", "block.minecraft.black_stained_glass": "Schworzs Glos", "block.minecraft.black_stained_glass_pane": "Schworze Glosscheibn", "block.minecraft.black_terracotta": "Schworze Keramik", "block.minecraft.black_wool": "Schworze Woin", "block.minecraft.blackstone": "Schworzstoa", "block.minecraft.blackstone_slab": "Schworzstoastufn", "block.minecraft.blackstone_stairs": "Schworzstoatreppn", "block.minecraft.blackstone_wall": "Schworzstoamaua", "block.minecraft.blast_furnace": "Schmözofn", "block.minecraft.blue_banner": "<PERSON><PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON>n mit blaua <PERSON>n", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "Blau glosiate Keramik", "block.minecraft.blue_ice": "<PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bone_block": "Knochnblock", "block.minecraft.bookshelf": "Biacharegal", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_fan": "Hiankorallnfächa", "block.minecraft.brain_coral_wall_fan": "Hi<PERSON>korallnwondfächa", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Ziaglstufn", "block.minecraft.brick_stairs": "Ziagltreppn", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Ziagl", "block.minecraft.brown_banner": "<PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON> Kerzn", "block.minecraft.brown_candle_cake": "<PERSON><PERSON>n mit brauna Kerzn", "block.minecraft.brown_carpet": "<PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "Braun glosiate Keramik", "block.minecraft.brown_mushroom": "Brauna Püz", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>üz<PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Braune Glosscheibn", "block.minecraft.brown_terracotta": "<PERSON><PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Blosnsäule", "block.minecraft.bubble_coral": "Blosnkoralln", "block.minecraft.bubble_coral_block": "Blosnkorallnblock", "block.minecraft.bubble_coral_fan": "Blosnkorallnfächa", "block.minecraft.bubble_coral_wall_fan": "Blosnkorallnwondfächa", "block.minecraft.budding_amethyst": "Wochsenda Amethyst", "block.minecraft.bush": "<PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Kaktusblütn", "block.minecraft.cake": "<PERSON><PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Kalibriata <PERSON>ulk-Sensor", "block.minecraft.campfire": "Logafeia", "block.minecraft.candle": "Kerzn", "block.minecraft.candle_cake": "<PERSON><PERSON>n mit Kerzn", "block.minecraft.carrots": "Karottn", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Gschnitzta <PERSON>ürbis", "block.minecraft.cauldron": "Kessl", "block.minecraft.cave_air": "Höhlnluft", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_vines_plant": "Höhlnlianenpflonz", "block.minecraft.chain": "Kettn", "block.minecraft.chain_command_block": "Kettn-Befehlsblock", "block.minecraft.cherry_button": "Kirschnhoizknopf", "block.minecraft.cherry_door": "Kirschnhoiztir", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "Ki<PERSON>nhoizzauntor", "block.minecraft.cherry_hanging_sign": "Kirschnhoizhängeschüd", "block.minecraft.cherry_leaves": "Kirschnlab", "block.minecraft.cherry_log": "Kirschnbamstomm", "block.minecraft.cherry_planks": "Kirschnhoizbrettl", "block.minecraft.cherry_pressure_plate": "Kirschnhoizdruckplottn", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Kirschnhoizschüd", "block.minecraft.cherry_slab": "Kirschnhoizstufn", "block.minecraft.cherry_stairs": "Kirschnhoiztreppn", "block.minecraft.cherry_trapdoor": "Kirschnhoizfoitir", "block.minecraft.cherry_wall_hanging_sign": "Kirschnhoizwondhängeschüd", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON>nhoizwondschüd", "block.minecraft.cherry_wood": "Ki<PERSON>nh<PERSON>z", "block.minecraft.chest": "Kistn", "block.minecraft.chipped_anvil": "Ugschlog<PERSON>", "block.minecraft.chiseled_bookshelf": "Chiseldel Buachregal", "block.minecraft.chiseled_copper": "Gmoaß<PERSON>", "block.minecraft.chiseled_deepslate": "Gmoaßlta Tiafnschiefa", "block.minecraft.chiseled_nether_bricks": "Gmoaßlte Netherziagl", "block.minecraft.chiseled_polished_blackstone": "Gmoaßlta Poliata Schworzstoa", "block.minecraft.chiseled_quartz_block": "Gmoaßlta Quarzblock", "block.minecraft.chiseled_red_sandstone": "Gmoaßlta rota Sondstoa", "block.minecraft.chiseled_resin_bricks": "Gmeißelte Hoazziagl", "block.minecraft.chiseled_sandstone": "Gmoaßlta Sondstoa", "block.minecraft.chiseled_stone_bricks": "Gmoaßlte Stoaziagl", "block.minecraft.chiseled_tuff": "Gmoaßlta Tuffstoa", "block.minecraft.chiseled_tuff_bricks": "Gmoaßlta Tuffstoaziagl", "block.minecraft.chorus_flower": "Chorusblütn", "block.minecraft.chorus_plant": "Choruspflonzn", "block.minecraft.clay": "Ton", "block.minecraft.closed_eyeblossom": "Gschlossene Augnbluam", "block.minecraft.coal_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coal_ore": "St<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "Bruchtiafnschiefa", "block.minecraft.cobbled_deepslate_slab": "Bruchtiafnschiefastufn", "block.minecraft.cobbled_deepslate_stairs": "Bruchtiafnschiefatreppn", "block.minecraft.cobbled_deepslate_wall": "Bruchtiafnschiefamaua", "block.minecraft.cobblestone": "Bruchstoa", "block.minecraft.cobblestone_slab": "Bruchstoastufn", "block.minecraft.cobblestone_stairs": "Bruchstoatreppn", "block.minecraft.cobblestone_wall": "Bruchstoamaua", "block.minecraft.cobweb": "Spinnennetz", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Befehlsblock", "block.minecraft.comparator": "Redstone-Komparator", "block.minecraft.composter": "Komposta", "block.minecraft.conduit": "Aquisator", "block.minecraft.copper_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_bulb": "Kupfalompn", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Kupfafoitir", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Rissige Tiafnschiefaziagl", "block.minecraft.cracked_deepslate_tiles": "Rissige Tiafnschiefafliesn", "block.minecraft.cracked_nether_bricks": "Rissige Netherziagl", "block.minecraft.cracked_polished_blackstone_bricks": "Rissige Poliate Schworzstoaziagl", "block.minecraft.cracked_stone_bricks": "Rissige Stoaziagl", "block.minecraft.crafter": "Crafter", "block.minecraft.crafting_table": "Werkbonk", "block.minecraft.creaking_heart": "Knarrendes Herz", "block.minecraft.creeper_head": "Creeperkopf", "block.minecraft.creeper_wall_head": "Creeper-Wondkopf", "block.minecraft.crimson_button": "Ka<PERSON><PERSON>nknopf", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "Karmesinpüz", "block.minecraft.crimson_hanging_sign": "Karmesinhängeschüd", "block.minecraft.crimson_hyphae": "Karmesinhyphn", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Karmesindruckplottn", "block.minecraft.crimson_roots": "Karmesinwurzln", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "Karmesinstufn", "block.minecraft.crimson_stairs": "Karmesintreppn", "block.minecraft.crimson_stem": "Karmesinstomm", "block.minecraft.crimson_trapdoor": "Ka<PERSON><PERSON>nfoitir", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>üd", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Weinender Obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper_slab": "Gschnittene Kupfastufn", "block.minecraft.cut_copper_stairs": "Gschnittene Kupfatreppn", "block.minecraft.cut_red_sandstone": "Gschnittena rota Sondstoa", "block.minecraft.cut_red_sandstone_slab": "Gschnittene rote Sondstoastufn", "block.minecraft.cut_sandstone": "Gschnittena Sondstoa", "block.minecraft.cut_sandstone_slab": "Gschnittene Sondstoastufn", "block.minecraft.cyan_banner": "Türkises Banna", "block.minecraft.cyan_bed": "Türkises Bett", "block.minecraft.cyan_candle": "Türkise Kerzn", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON>n mit türkisa Kerzn", "block.minecraft.cyan_carpet": "Türk<PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "Türkisa Trocknbeton", "block.minecraft.cyan_glazed_terracotta": "Türkis glosiate Keramik", "block.minecraft.cyan_shulker_box": "Türkise Shulkerkistn", "block.minecraft.cyan_stained_glass": "Türkises Glos", "block.minecraft.cyan_stained_glass_pane": "Türkise Glosscheibn", "block.minecraft.cyan_terracotta": "Türkise Keramik", "block.minecraft.cyan_wool": "Türkise Woin", "block.minecraft.damaged_anvil": "Beschädigta Am<PERSON>s", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Schworzoachnhoizknopf", "block.minecraft.dark_oak_door": "Schworzoachnhoiztir", "block.minecraft.dark_oak_fence": "Schworzoa<PERSON>nhoizzaun", "block.minecraft.dark_oak_fence_gate": "Schworzoachnhoizzauntor", "block.minecraft.dark_oak_hanging_sign": "Schworzoachnhängeschüd", "block.minecraft.dark_oak_leaves": "Schworzoachnlab", "block.minecraft.dark_oak_log": "Schworzoachnstomm", "block.minecraft.dark_oak_planks": "Schworzoachnhoizbrettl", "block.minecraft.dark_oak_pressure_plate": "Schworzoachndruckplottn", "block.minecraft.dark_oak_sapling": "Schworz<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sign": "Schworzoachnhoizschüd", "block.minecraft.dark_oak_slab": "Schworzoachnstufn", "block.minecraft.dark_oak_stairs": "Schworzoachnhoiztreppn", "block.minecraft.dark_oak_trapdoor": "Schworzoachnhoizfoitir", "block.minecraft.dark_oak_wall_hanging_sign": "Schworzoachnwondhängeschüd", "block.minecraft.dark_oak_wall_sign": "Schworzoachnhoizwondschüd", "block.minecraft.dark_oak_wood": "Schworzoachnhoiz", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Dunkle Prismarinstufn", "block.minecraft.dark_prismarine_stairs": "Dunkle Prismarintreppn", "block.minecraft.daylight_detector": "Togeslichtsensor", "block.minecraft.dead_brain_coral": "Obgstorbene Hiankoralln", "block.minecraft.dead_brain_coral_block": "Obgstorbena <PERSON>", "block.minecraft.dead_brain_coral_fan": "Obgstorbena Hiankorallnfächa", "block.minecraft.dead_brain_coral_wall_fan": "Obgstorbena <PERSON>wondfächa", "block.minecraft.dead_bubble_coral": "Obgstorbene Blosnkoralle", "block.minecraft.dead_bubble_coral_block": "Obgstorbena Blosnkorallnblock", "block.minecraft.dead_bubble_coral_fan": "Obgstorbena Blosnkorallnfächa", "block.minecraft.dead_bubble_coral_wall_fan": "Obgstorbena Blosnkorallnwondfächa", "block.minecraft.dead_bush": "Austrockneta Busch", "block.minecraft.dead_fire_coral": "Obgstorbene Feiakoralln", "block.minecraft.dead_fire_coral_block": "Obgstorbena <PERSON>allnblock", "block.minecraft.dead_fire_coral_fan": "Obgstorbena Feiakorallnfächa", "block.minecraft.dead_fire_coral_wall_fan": "Obgstorbena <PERSON>korallnwondfächa", "block.minecraft.dead_horn_coral": "Obgstorbene Geweihkoralle", "block.minecraft.dead_horn_coral_block": "Obgstorbena Geweihkorallnblock", "block.minecraft.dead_horn_coral_fan": "Obgstorbena Geweihkorallnfächa", "block.minecraft.dead_horn_coral_wall_fan": "Obgstorbena <PERSON>korallnwondfächa", "block.minecraft.dead_tube_coral": "Obgstorbene Orglkoralln", "block.minecraft.dead_tube_coral_block": "Obgstorbena Orglkorallnblock", "block.minecraft.dead_tube_coral_fan": "Obgstorbena Orglkorallnfächa", "block.minecraft.dead_tube_coral_wall_fan": "Obgstorbena Orglkorallnwondfächa", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate": "Tiafnschiefa", "block.minecraft.deepslate_brick_slab": "Tiafnschiefaziaglstufn", "block.minecraft.deepslate_brick_stairs": "Tiafnschiefaziagltreppn", "block.minecraft.deepslate_brick_wall": "Tiafnschiefaziaglmaua", "block.minecraft.deepslate_bricks": "Tiafnschiefaziagl", "block.minecraft.deepslate_coal_ore": "Tiafnschiefa-Stoakohle", "block.minecraft.deepslate_copper_ore": "Tiafnschiefa-Kupfaerz", "block.minecraft.deepslate_diamond_ore": "Tiafnschiefa-Diamanterz", "block.minecraft.deepslate_emerald_ore": "Tiafnschiefa-Smaragderz", "block.minecraft.deepslate_gold_ore": "Tiafnschiefa-Goiderz", "block.minecraft.deepslate_iron_ore": "Tiafnschiefa-Eisnerz", "block.minecraft.deepslate_lapis_ore": "Tiafnschiefa-Lapislazulierz", "block.minecraft.deepslate_redstone_ore": "Tiafnschiefa-Redstoneerz", "block.minecraft.deepslate_tile_slab": "Tiafnschiefafliesnstufn", "block.minecraft.deepslate_tile_stairs": "Tiafnschiefafliesntreppn", "block.minecraft.deepslate_tile_wall": "Tiafnschiefafliesnmaua", "block.minecraft.deepslate_tiles": "Tiafnschiefafliesn", "block.minecraft.detector_rail": "Sensorschiene", "block.minecraft.diamond_block": "Diamantblock", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioritstufn", "block.minecraft.diorite_stairs": "Diorittreppn", "block.minecraft.diorite_wall": "Di<PERSON>tma<PERSON>", "block.minecraft.dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt_path": "Tramplpfad", "block.minecraft.dispenser": "We<PERSON><PERSON>", "block.minecraft.dragon_egg": "Dr<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Drochn-Wondkopf", "block.minecraft.dried_ghast": "Vatrockneta G<PERSON>", "block.minecraft.dried_kelp_block": "Trockneta <PERSON>gblock", "block.minecraft.dripstone_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>lock", "block.minecraft.dropper": "Spenda", "block.minecraft.emerald_block": "Smaragdblock", "block.minecraft.emerald_ore": "Smaragderz", "block.minecraft.enchanting_table": "Zaubat<PERSON>", "block.minecraft.end_gateway": "Endtransitportal", "block.minecraft.end_portal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_portal_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_rod": "Endstob", "block.minecraft.end_stone": "Endstoa", "block.minecraft.end_stone_brick_slab": "Endstoaziaglstufn", "block.minecraft.end_stone_brick_stairs": "Endstoaziagltreppn", "block.minecraft.end_stone_brick_wall": "End<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_bricks": "Endstoaziagl", "block.minecraft.ender_chest": "Enderkistn", "block.minecraft.exposed_chiseled_copper": "Ausgsetzta gmoaßlta <PERSON>lock", "block.minecraft.exposed_copper": "Ausgsetzta Ku<PERSON>ablock", "block.minecraft.exposed_copper_bulb": "Ausgsetzte Kupfalompn", "block.minecraft.exposed_copper_door": "Ausgsetzte Kupfatir", "block.minecraft.exposed_copper_grate": "Ausgsetzta Kupfarost", "block.minecraft.exposed_copper_trapdoor": "Ausgsetzte Kupfafoitir", "block.minecraft.exposed_cut_copper": "Ausgsetzta gschnittena Kupfablock", "block.minecraft.exposed_cut_copper_slab": "Ausgsetzte gschnittene Kupferstufn", "block.minecraft.exposed_cut_copper_stairs": "Ausgsetzte gschnittene Kupfatreppn", "block.minecraft.farmland": "<PERSON><PERSON><PERSON>", "block.minecraft.fern": "Farn", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_fan": "Feiakorallnfächa", "block.minecraft.fire_coral_wall_fan": "Feiakorallnwondfächa", "block.minecraft.firefly_bush": "Glühwürmchenbuschn", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "Blu<PERSON><PERSON>", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "Bliands Azaleenlaub", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "Brüchigs Eis", "block.minecraft.furnace": "Ofn", "block.minecraft.gilded_blackstone": "Golddurchzogena Schworzstoa", "block.minecraft.glass": "Glos", "block.minecraft.glass_pane": "Glosscheibn", "block.minecraft.glow_lichen": "Leichtflechtn", "block.minecraft.glowstone": "<PERSON><PERSON>tsto<PERSON>", "block.minecraft.gold_block": "Goidblock", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Granitstufn", "block.minecraft.granite_stairs": "Granittreppn", "block.minecraft.granite_wall": "Granitmaua", "block.minecraft.grass": "Gros", "block.minecraft.grass_block": "Grosblock", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON><PERSON>n mit gra<PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "Grau glosiate Keramik", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON>n mit grüna <PERSON>", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "Grün glosiate Keramik", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>n", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "Schleifstoa", "block.minecraft.hanging_roots": "Hängende Wurzln", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Grobwägeplottn", "block.minecraft.honey_block": "Honigblock", "block.minecraft.honeycomb_block": "Honigwobnblock", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Geweihkoralln", "block.minecraft.horn_coral_block": "Geweihkorallnblock", "block.minecraft.horn_coral_fan": "Geweihkorallnfächa", "block.minecraft.horn_coral_wall_fan": "Geweihkorallnwondfächa", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Befoiene gmoaßlte Stoaziagl", "block.minecraft.infested_cobblestone": "Befoiena Bruchstoa", "block.minecraft.infested_cracked_stone_bricks": "Befoiene rissige Stoaziagl", "block.minecraft.infested_deepslate": "Befoiena T<PERSON>fnschiefa", "block.minecraft.infested_mossy_stone_bricks": "Befoiene bemooste Stoaziagl", "block.minecraft.infested_stone": "Befoiena Stoa", "block.minecraft.infested_stone_bricks": "Befoiene Stoaziagl", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Eisnblock", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Eisnfoitir", "block.minecraft.jack_o_lantern": "Kürbislatern", "block.minecraft.jigsaw": "Vabundblock", "block.minecraft.jukebox": "Plottnspiela", "block.minecraft.jungle_button": "Tropnhoizknopf", "block.minecraft.jungle_door": "Tropnhoiztir", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Tropnhoizzauntor", "block.minecraft.jungle_hanging_sign": "Tropnhoizhängeschüd", "block.minecraft.jungle_leaves": "Tropnbamlab", "block.minecraft.jungle_log": "Tropnbamstomm", "block.minecraft.jungle_planks": "Tropnhoizbrettl", "block.minecraft.jungle_pressure_plate": "Tropnhoizdruckplottn", "block.minecraft.jungle_sapling": "Tropnbamsetzling", "block.minecraft.jungle_sign": "Dschunglhoizschüd", "block.minecraft.jungle_slab": "Tropnhoizstufn", "block.minecraft.jungle_stairs": "Tropnhoiztreppn", "block.minecraft.jungle_trapdoor": "Tropnhoizfoitir", "block.minecraft.jungle_wall_hanging_sign": "Tropnhoizwondhängeschüd", "block.minecraft.jungle_wall_sign": "Dschunglhoizwondschüd", "block.minecraft.jungle_wood": "Tropnhoiz", "block.minecraft.kelp": "Seetong", "block.minecraft.kelp_plant": "Seetongpflonzn", "block.minecraft.ladder": "<PERSON><PERSON>", "block.minecraft.lantern": "<PERSON>n", "block.minecraft.lapis_block": "Lapislazuliblock", "block.minecraft.lapis_ore": "La<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "Große Amethystknospn", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavakessl", "block.minecraft.leaf_litter": "Lab", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle": "Hellbaue Kerzn", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON>n mit hell<PERSON>ua <PERSON>rzn", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete_powder": "Hellblaua Trocknbeton", "block.minecraft.light_blue_glazed_terracotta": "Hellblau glosiate Keramik", "block.minecraft.light_blue_shulker_box": "Hellblaue Shulkerkistn", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Hellblaue Glosscheibn", "block.minecraft.light_blue_terracotta": "Hellblaue Keramik", "block.minecraft.light_blue_wool": "Hellblaue Woin", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON>n mit hell<PERSON><PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Hellgrau glosiate Keramik", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass_pane": "Hellgraue Glosscheibn", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.light_weighted_pressure_plate": "Feinwägeplottn", "block.minecraft.lightning_rod": "Blitzobleita", "block.minecraft.lilac": "Flieda", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON>n mit <PERSON>g<PERSON><PERSON><PERSON>", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "Hellgrün glosiate Keramik", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "Hellgrüne Glosscheibn", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lodestone": "Leitstoa", "block.minecraft.loom": "Webstui", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "Ma<PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON>n mit magenta Kerzn", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_glazed_terracotta": "Magenta glosiate Keramik", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.magma_block": "Magmablock", "block.minecraft.mangrove_button": "Mangrovnhoizknopf", "block.minecraft.mangrove_door": "Mangrovnhoiztir", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "Mangrovnhoizzauntor", "block.minecraft.mangrove_hanging_sign": "Mangrovnhoizhängeschüd", "block.minecraft.mangrove_leaves": "Mangrovnlab", "block.minecraft.mangrove_log": "Mangrovnstomm", "block.minecraft.mangrove_planks": "Mangrovnhoizbrettl", "block.minecraft.mangrove_pressure_plate": "Mangrovnhoizdruckplottn", "block.minecraft.mangrove_propagule": "Mangrovn-Ke<PERSON>ling", "block.minecraft.mangrove_roots": "Mangrovnwurzln", "block.minecraft.mangrove_sign": "Mangrovnhoizschüd", "block.minecraft.mangrove_slab": "Mangrovnhoizstufn", "block.minecraft.mangrove_stairs": "Mangrovnhoiztreppn", "block.minecraft.mangrove_trapdoor": "Mangrovnhoizfoitir", "block.minecraft.mangrove_wall_hanging_sign": "Mangrovnhoizwondhängeschüd", "block.minecraft.mangrove_wall_sign": "Man<PERSON>vnhoizwondschüd", "block.minecraft.mangrove_wood": "Mangrovnhoiz", "block.minecraft.medium_amethyst_bud": "Mittlare Amethystknospn", "block.minecraft.melon": "<PERSON><PERSON>", "block.minecraft.melon_stem": "Melonenpflonzn", "block.minecraft.moss_block": "Moosblock", "block.minecraft.moss_carpet": "Moosteppich", "block.minecraft.mossy_cobblestone": "Bemoosta Bruchstoa", "block.minecraft.mossy_cobblestone_slab": "Bemooste Bruchstoastufn", "block.minecraft.mossy_cobblestone_stairs": "Bemooste Bruchstoatreppn", "block.minecraft.mossy_cobblestone_wall": "Bemooste Bruchstoamaua", "block.minecraft.mossy_stone_brick_slab": "Bemooste Stoaziaglstufn", "block.minecraft.mossy_stone_brick_stairs": "Bemooste Stoaziagltreppn", "block.minecraft.mossy_stone_brick_wall": "Bemooste Bruchstoamaua", "block.minecraft.mossy_stone_bricks": "Bemooste Stoaziagl", "block.minecraft.moving_piston": "Bewegta Block", "block.minecraft.mud": "Schlomm", "block.minecraft.mud_brick_slab": "Schlommige Ziaglstufn", "block.minecraft.mud_brick_stairs": "Schlommige Ziagltreppn", "block.minecraft.mud_brick_wall": "Schlommige Z<PERSON>", "block.minecraft.mud_bricks": "Schlommige Ziagl", "block.minecraft.muddy_mangrove_roots": "Schlommige Mangrovnwurzln", "block.minecraft.mushroom_stem": "Püzstengl", "block.minecraft.mycelium": "<PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Netherziaglzaun", "block.minecraft.nether_brick_slab": "Netherziaglstufn", "block.minecraft.nether_brick_stairs": "Netherziagltreppn", "block.minecraft.nether_brick_wall": "Netherziaglmaua", "block.minecraft.nether_bricks": "Netherziagl", "block.minecraft.nether_gold_ore": "Nethergoiderz", "block.minecraft.nether_portal": "Netherportal", "block.minecraft.nether_quartz_ore": "Netherquarzerz", "block.minecraft.nether_sprouts": "Nether-Spross", "block.minecraft.nether_wart": "Netherwarzn", "block.minecraft.nether_wart_block": "Netherwarznblock", "block.minecraft.netherite_block": "Netheritblock", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Notnblock", "block.minecraft.oak_button": "Oachnhoizknopf", "block.minecraft.oak_door": "Oachnhoiztir", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "Oachnhoizzauntor", "block.minecraft.oak_hanging_sign": "Oachnhoizhängeschüd", "block.minecraft.oak_leaves": "Oachnlab", "block.minecraft.oak_log": "Oachnstomm", "block.minecraft.oak_planks": "Oachnhoizbrettl", "block.minecraft.oak_pressure_plate": "Oachnhoizdruckplottn", "block.minecraft.oak_sapling": "O<PERSON><PERSON><PERSON>ling", "block.minecraft.oak_sign": "Oachnhoizschüd", "block.minecraft.oak_slab": "Oachnhoizstufn", "block.minecraft.oak_stairs": "Oachnhoiztreppn", "block.minecraft.oak_trapdoor": "Oachnhoizfoitir", "block.minecraft.oak_wall_hanging_sign": "Oachnhoizwondhängeschüd", "block.minecraft.oak_wall_sign": "Oachnhoizwondschüd", "block.minecraft.oak_wood": "Oachnhoiz", "block.minecraft.observer": "Beob<PERSON><PERSON>", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.open_eyeblossom": "<PERSON><PERSON>", "block.minecraft.orange_banner": "Or<PERSON><PERSON>", "block.minecraft.orange_bed": "Or<PERSON><PERSON>", "block.minecraft.orange_candle": "Orange Kerzn", "block.minecraft.orange_candle_cake": "<PERSON><PERSON>n mit oranga Kerzn", "block.minecraft.orange_carpet": "Oranga <PERSON>", "block.minecraft.orange_concrete": "Oranga Beton", "block.minecraft.orange_concrete_powder": "Oranga Trocknbeton", "block.minecraft.orange_glazed_terracotta": "Orange glosiate Keramik", "block.minecraft.orange_shulker_box": "Orange Shulkerkistn", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Orange Glosscheibn", "block.minecraft.orange_terracotta": "Orange Keramik", "block.minecraft.orange_tulip": "Orange Tulpn", "block.minecraft.orange_wool": "Orange Woin", "block.minecraft.oxeye_daisy": "Margerite", "block.minecraft.oxidized_chiseled_copper": "Varrosteta g<PERSON>ß<PERSON>", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_bulb": "Varrostete Ku<PERSON>alompn", "block.minecraft.oxidized_copper_door": "Varrostete <PERSON>r", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Varrostete Kupfafoitir", "block.minecraft.oxidized_cut_copper": "Varrosteta g<PERSON>a <PERSON>lock", "block.minecraft.oxidized_cut_copper_slab": "Varrostete gschnittene Kupfastufn", "block.minecraft.oxidized_cut_copper_stairs": "Varrostete gschnittene Kupfatreppn", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "Festa Schlomm", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON> Blassmo<PERSON>", "block.minecraft.pale_moss_block": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_button": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_door": "Blasse Eichentür", "block.minecraft.pale_oak_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "Blasses E<PERSON>nzauntor", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON>", "block.minecraft.pale_oak_leaves": "Blasse Eichenblätter", "block.minecraft.pale_oak_log": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_planks": "Blasse Eichenbretter", "block.minecraft.pale_oak_pressure_plate": "Blasse Eichendruckplatte", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_sign": "Blosseichnhoizschüd", "block.minecraft.pale_oak_slab": "Blosseichnhoizstufn", "block.minecraft.pale_oak_stairs": "Blosseichnhoiztreppn", "block.minecraft.pale_oak_trapdoor": "Blosseichnhoizfoitir", "block.minecraft.pale_oak_wall_hanging_sign": "Blosseichnhoizwondhängeschüd", "block.minecraft.pale_oak_wall_sign": "Blosseichnhoizwondschüd", "block.minecraft.pale_oak_wood": "Blosseichnhoiz", "block.minecraft.pearlescent_froglight": "Perlmuttans Froschliacht", "block.minecraft.peony": "Pfingstrosn", "block.minecraft.petrified_oak_slab": "Vasteinate Oachnhoizstufn", "block.minecraft.piglin_head": "Piglinkopf", "block.minecraft.piglin_wall_head": "Piglin-Wandkopf", "block.minecraft.pink_banner": "<PERSON><PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON>", "block.minecraft.pink_candle": "<PERSON><PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON><PERSON> mit rosa <PERSON>n", "block.minecraft.pink_carpet": "<PERSON><PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON>", "block.minecraft.pink_glazed_terracotta": "Rosa glos<PERSON>", "block.minecraft.pink_petals": "<PERSON><PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.pink_terracotta": "<PERSON><PERSON>", "block.minecraft.pink_tulip": "<PERSON><PERSON>", "block.minecraft.pink_wool": "<PERSON><PERSON>", "block.minecraft.piston": "Kolbn", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Konnenpflonznkapsl", "block.minecraft.pitcher_plant": "Konnenpflonzn", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "<PERSON><PERSON> von %s", "block.minecraft.player_wall_head": "Spiela-Wondkopf", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Spitza Tropfstoa", "block.minecraft.polished_andesite": "Poliata Andesit", "block.minecraft.polished_andesite_slab": "Poliate Andesitstufn", "block.minecraft.polished_andesite_stairs": "Poliate Andesittreppn", "block.minecraft.polished_basalt": "Poliata <PERSON>t", "block.minecraft.polished_blackstone": "Poliata Schworzstoa", "block.minecraft.polished_blackstone_brick_slab": "Poliate Schworzstoaziaglstufn", "block.minecraft.polished_blackstone_brick_stairs": "Poliate Schworzstoaziagltreppn", "block.minecraft.polished_blackstone_brick_wall": "Poliate Schworzstoaziaglmaua", "block.minecraft.polished_blackstone_bricks": "Poliata Schworzstoaziagl", "block.minecraft.polished_blackstone_button": "Poliata Schworzstoaknopf", "block.minecraft.polished_blackstone_pressure_plate": "Poliate Schworzstoadruckplottn", "block.minecraft.polished_blackstone_slab": "Poliate Schworzstoastufn", "block.minecraft.polished_blackstone_stairs": "Poliate Schworzstoatreppn", "block.minecraft.polished_blackstone_wall": "Poliate Schworzstoamaua", "block.minecraft.polished_deepslate": "Poliata <PERSON>", "block.minecraft.polished_deepslate_slab": "Poliate Tiafnschiefastufn", "block.minecraft.polished_deepslate_stairs": "Poliate Tiafnschiefatreppn", "block.minecraft.polished_deepslate_wall": "Poliate Tiafnschiefamaua", "block.minecraft.polished_diorite": "Poliata <PERSON>orit", "block.minecraft.polished_diorite_slab": "Poliate Dioritstufn", "block.minecraft.polished_diorite_stairs": "Poliate Diorittreppn", "block.minecraft.polished_granite": "Poliata Granit", "block.minecraft.polished_granite_slab": "Poliate Granitstufn", "block.minecraft.polished_granite_stairs": "Poliate Granittreppn", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "Polierte Tuffstoastufn", "block.minecraft.polished_tuff_stairs": "Polierte Tuffstoatreppn", "block.minecraft.polished_tuff_wall": "Polierte <PERSON>", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Eingsetzter Akaziensetzling", "block.minecraft.potted_allium": "Eingsetzta Sternlauch", "block.minecraft.potted_azalea_bush": "Eingsetzte Azalee", "block.minecraft.potted_azure_bluet": "Eingsetztes Porzellansternchen", "block.minecraft.potted_bamboo": "Eingsetzta Bambus", "block.minecraft.potted_birch_sapling": "Eingsetzta Birknsetzling", "block.minecraft.potted_blue_orchid": "Eingsetzte blaue Orchidee", "block.minecraft.potted_brown_mushroom": "Eingsetzta brauna Püz", "block.minecraft.potted_cactus": "Eingsetzta Kaktus", "block.minecraft.potted_cherry_sapling": "Eingsetza Kirschnsetzling", "block.minecraft.potted_closed_eyeblossom": "Eingetopfte gschlossene Augnbluam", "block.minecraft.potted_cornflower": "Eingsetzte Kornblume", "block.minecraft.potted_crimson_fungus": "Eingsetzta Karmesinpüz", "block.minecraft.potted_crimson_roots": "Eingsetzte Karmesinwurzln", "block.minecraft.potted_dandelion": "Eingsetzta <PERSON>öwen<PERSON>hn", "block.minecraft.potted_dark_oak_sapling": "Eingsetzta Schworzoachnsetzling", "block.minecraft.potted_dead_bush": "Eingsetzta austrockneta Busch", "block.minecraft.potted_fern": "Eingsetzta Farn", "block.minecraft.potted_flowering_azalea_bush": "Eingsetzte bliande Azalee", "block.minecraft.potted_jungle_sapling": "Eingsetzta Tropnbamsetzling", "block.minecraft.potted_lily_of_the_valley": "Eingsetztes Maiglöckchen", "block.minecraft.potted_mangrove_propagule": "Eingsetzta Mangrovn-Keimling", "block.minecraft.potted_oak_sapling": "Eingsetzta Oachnsetzling", "block.minecraft.potted_open_eyeblossom": "Eingetopfte offene Augnbluam", "block.minecraft.potted_orange_tulip": "Eingsetzte orange Tulpn", "block.minecraft.potted_oxeye_daisy": "Eingsetzte Magerite", "block.minecraft.potted_pale_oak_sapling": "Eingetopfta Blosseichnhoizsetzling", "block.minecraft.potted_pink_tulip": "Eingsetzte rosane <PERSON>n", "block.minecraft.potted_poppy": "Eingsetz<PERSON>", "block.minecraft.potted_red_mushroom": "Eingsetzta rota Püz", "block.minecraft.potted_red_tulip": "Eingsetzte rote Tulpn", "block.minecraft.potted_spruce_sapling": "Eingsetzta Fichtnsetzling", "block.minecraft.potted_torchflower": "Eingsetze Focklblume", "block.minecraft.potted_warped_fungus": "Eingsetzta Wirrpüz", "block.minecraft.potted_warped_roots": "Eingsetzte Wirrwurzln", "block.minecraft.potted_white_tulip": "Eingsetzte weiße Tulpn", "block.minecraft.potted_wither_rose": "Eingsetzte Witherrosn", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "Puivaschneekessl", "block.minecraft.powered_rail": "Ontriebsschiene", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarinziaglstufn", "block.minecraft.prismarine_brick_stairs": "Prismarinziagltreppn", "block.minecraft.prismarine_bricks": "Prismarinziagl", "block.minecraft.prismarine_slab": "Prismarinstufn", "block.minecraft.prismarine_stairs": "Prismarintreppn", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Kürbispflonzn", "block.minecraft.purple_banner": "<PERSON><PERSON>", "block.minecraft.purple_bed": "<PERSON><PERSON>", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "<PERSON><PERSON><PERSON> mit lila <PERSON>", "block.minecraft.purple_carpet": "<PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON>", "block.minecraft.purple_glazed_terracotta": "Lila glosiate <PERSON>", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON>", "block.minecraft.purpur_block": "Purpurblock", "block.minecraft.purpur_pillar": "Purpursäule", "block.minecraft.purpur_slab": "Purpurstufn", "block.minecraft.purpur_stairs": "Purpurtreppn", "block.minecraft.quartz_block": "Quarzblock", "block.minecraft.quartz_bricks": "Quarzziagl", "block.minecraft.quartz_pillar": "Quarzsäule", "block.minecraft.quartz_slab": "Quarzstufn", "block.minecraft.quartz_stairs": "Quarztreppn", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.raw_gold_block": "Rohgoidblock", "block.minecraft.raw_iron_block": "R<PERSON>eisnblock", "block.minecraft.red_banner": "Rots Banna", "block.minecraft.red_bed": "Rots Bett", "block.minecraft.red_candle": "Rote Kerzn", "block.minecraft.red_candle_cake": "<PERSON><PERSON>n mit rota Kerzn", "block.minecraft.red_carpet": "Rota Teppich", "block.minecraft.red_concrete": "Rota Beton", "block.minecraft.red_concrete_powder": "Rota Trocknbeton", "block.minecraft.red_glazed_terracotta": "Rot glosiate Keramik", "block.minecraft.red_mushroom": "Rota Püz", "block.minecraft.red_mushroom_block": "Rota Püzblock", "block.minecraft.red_nether_brick_slab": "Rote Netherziaglstufn", "block.minecraft.red_nether_brick_stairs": "Rote Netherziagltreppn", "block.minecraft.red_nether_brick_wall": "Rote Netherziaglmaua", "block.minecraft.red_nether_bricks": "Rote Netherziagl", "block.minecraft.red_sand": "Rota Sond", "block.minecraft.red_sandstone": "Rota Sondstoa", "block.minecraft.red_sandstone_slab": "Rote Sondstoastufn", "block.minecraft.red_sandstone_stairs": "Rote Sondstoatreppn", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON>", "block.minecraft.red_shulker_box": "Rote Shulkerkistn", "block.minecraft.red_stained_glass": "Rots Glos", "block.minecraft.red_stained_glass_pane": "Rote Glosscheibn", "block.minecraft.red_terracotta": "Rote Keramik", "block.minecraft.red_tulip": "Rote Tulpn", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Redstone-Block", "block.minecraft.redstone_lamp": "Redstone-Lompn", "block.minecraft.redstone_ore": "Redstoneerz", "block.minecraft.redstone_torch": "Redstone-Fockl", "block.minecraft.redstone_wall_torch": "Redstone-Wondfockl", "block.minecraft.redstone_wire": "Redstone-<PERSON><PERSON><PERSON>", "block.minecraft.reinforced_deepslate": "Vastärkta Tiafnschiefa", "block.minecraft.repeater": "Redstone-Vastärka", "block.minecraft.repeating_command_block": "Wiedahoi-<PERSON><PERSON><PERSON><PERSON>block", "block.minecraft.resin_block": "Hoazblock", "block.minecraft.resin_brick_slab": "Hoazziaglstufn", "block.minecraft.resin_brick_stairs": "Hoazziagltreppn", "block.minecraft.resin_brick_wall": "Hoazziag<PERSON>ua", "block.minecraft.resin_bricks": "Hoazziagl", "block.minecraft.resin_clump": "Hoazpatzn", "block.minecraft.respawn_anchor": "Wiedereinstiegsanker", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>ra<PERSON>", "block.minecraft.sand": "<PERSON><PERSON>", "block.minecraft.sandstone": "Sondstoa", "block.minecraft.sandstone_slab": "Sondstoastufn", "block.minecraft.sandstone_stairs": "Sondstoatreppn", "block.minecraft.sandstone_wall": "Sondstoamaua", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-Katalysatoa", "block.minecraft.sculk_sensor": "Sculk-Sensor", "block.minecraft.sculk_shrieker": "Sculk-Kreischa", "block.minecraft.sculk_vein": "Sculk-Åda", "block.minecraft.sea_lantern": "Seelatern", "block.minecraft.sea_pickle": "Meeresgurkn", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Wiedaeistiegspunkt gsetzt", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Shulkerkistn", "block.minecraft.skeleton_skull": "Skelettschädl", "block.minecraft.skeleton_wall_skull": "Skelett-Wondschädl", "block.minecraft.slime_block": "Schleimblock", "block.minecraft.small_amethyst_bud": "Kloane Amethystknospn", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smoker": "Räucherofn", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON>ta rota Sondstoa", "block.minecraft.smooth_red_sandstone_slab": "Glotte rote Stondstoastufn", "block.minecraft.smooth_red_sandstone_stairs": "Glotte rote Stondstoatreppn", "block.minecraft.smooth_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_stairs": "Glotte Sondstoatreppn", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "Glotte Stoastufn", "block.minecraft.sniffer_egg": "Schnüffla-Ei", "block.minecraft.snow": "Sc<PERSON><PERSON>", "block.minecraft.snow_block": "Schneeblock", "block.minecraft.soul_campfire": "Seelnlogafeia", "block.minecraft.soul_fire": "Seelnfeia", "block.minecraft.soul_lantern": "Seelnlatern", "block.minecraft.soul_sand": "Seelnsond", "block.minecraft.soul_soil": "Seelnerdn", "block.minecraft.soul_torch": "<PERSON>len<PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON>lenwondfockl", "block.minecraft.spawn.not_valid": "Du host koa <PERSON> und koan aufglodenen Seelnonker, oda dei Wiedaeistiegspunkt is blockiert woan.", "block.minecraft.spawner": "<PERSON><PERSON>er", "block.minecraft.spawner.desc1": "Mit Spawn-Ei interagieren:", "block.minecraft.spawner.desc2": "Setzt den Mob-Typ", "block.minecraft.sponge": "Sch<PERSON><PERSON>", "block.minecraft.spore_blossom": "Sporenblüte", "block.minecraft.spruce_button": "Fichtnhoizknopf", "block.minecraft.spruce_door": "Fichtnhoiztir", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "<PERSON>chtnhoizza<PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "Fichtnhängeschüd", "block.minecraft.spruce_leaves": "Fichtnnodln", "block.minecraft.spruce_log": "Fichtnstomm", "block.minecraft.spruce_planks": "Fichtnhoizbrettl", "block.minecraft.spruce_pressure_plate": "Fichtnhoizdruckplottn", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON><PERSON>ling", "block.minecraft.spruce_sign": "Fichtnhoizschüd", "block.minecraft.spruce_slab": "Fichtnhoizstufn", "block.minecraft.spruce_stairs": "Fichtnhoiztreppn", "block.minecraft.spruce_trapdoor": "Fichtnhoizfoitir", "block.minecraft.spruce_wall_hanging_sign": "Fichtnwondhängeschüd", "block.minecraft.spruce_wall_sign": "<PERSON>chtnhoizwondschüd", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON>z", "block.minecraft.sticky_piston": "Pickiga Kolbn", "block.minecraft.stone": "Stoa", "block.minecraft.stone_brick_slab": "Stoaziaglstufn", "block.minecraft.stone_brick_stairs": "Stoaziagltreppn", "block.minecraft.stone_brick_wall": "St<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "Stoaziagl", "block.minecraft.stone_button": "Stoaknopf", "block.minecraft.stone_pressure_plate": "Stoadruckplottn", "block.minecraft.stone_slab": "Stoastufn", "block.minecraft.stone_stairs": "Stoatreppn", "block.minecraft.stonecutter": "Stoasog", "block.minecraft.stripped_acacia_log": "Entrindeta Akazienstomm", "block.minecraft.stripped_acacia_wood": "Entrindets Akazienhoiz", "block.minecraft.stripped_bamboo_block": "Entrindeta Bambusblock", "block.minecraft.stripped_birch_log": "Entrindeta Birknstomm", "block.minecraft.stripped_birch_wood": "Entrindets Birknhoiz", "block.minecraft.stripped_cherry_log": "Entrindeta Kirschnstomm", "block.minecraft.stripped_cherry_wood": "Entrindets Kirschnhoiz", "block.minecraft.stripped_crimson_hyphae": "Entrindete Karmesinhyphn", "block.minecraft.stripped_crimson_stem": "Entrindeta Karmesinstomm", "block.minecraft.stripped_dark_oak_log": "Entrindeta Schworzoachnstomm", "block.minecraft.stripped_dark_oak_wood": "Entrindets Schworzoachnhoiz", "block.minecraft.stripped_jungle_log": "Entrindeta Tropnstomm", "block.minecraft.stripped_jungle_wood": "Entrindets Tropnhoiz", "block.minecraft.stripped_mangrove_log": "Entrindeta Mangrovnstomm", "block.minecraft.stripped_mangrove_wood": "Entrindets Mangrovnhoiz", "block.minecraft.stripped_oak_log": "Entrindeta Oachnstomm", "block.minecraft.stripped_oak_wood": "Entrindetes Schworzoichenholz", "block.minecraft.stripped_pale_oak_log": "Entrindeta Blosseichnhoizstomm", "block.minecraft.stripped_pale_oak_wood": "Entrindetes Blosseichnhoiz", "block.minecraft.stripped_spruce_log": "Entrindeta Fichtnstomm", "block.minecraft.stripped_spruce_wood": "Entrindets Fichtnhoiz", "block.minecraft.stripped_warped_hyphae": "Entrindete Wirrhyphn", "block.minecraft.stripped_warped_stem": "Entrindeta Wirrstomm", "block.minecraft.structure_block": "Konstruktionsblock", "block.minecraft.structure_void": "Konstruktionsleere", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Sonnenblume", "block.minecraft.suspicious_gravel": "Vadächtiga Kies", "block.minecraft.suspicious_sand": "Vadächtiga Sond", "block.minecraft.sweet_berry_bush": "Süßbeernstrauch", "block.minecraft.tall_dry_grass": "Hochs trockenes Gros", "block.minecraft.tall_grass": "Hochs Gros", "block.minecraft.tall_seagrass": "Hochs Seegros", "block.minecraft.target": "Züscheibnblock", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Testblock", "block.minecraft.test_instance_block": "Testinstanzblock", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-Explosionen san aus", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "F<PERSON><PERSON>b<PERSON>", "block.minecraft.torchflower_crop": "Focklblumensomen", "block.minecraft.trapped_chest": "Redstonekistn", "block.minecraft.trial_spawner": "Prüfungs-<PERSON>rschaffer", "block.minecraft.tripwire": "Stoipadroht", "block.minecraft.tripwire_hook": "Hokn", "block.minecraft.tube_coral": "Orglkoralln", "block.minecraft.tube_coral_block": "Orglkorallnblock", "block.minecraft.tube_coral_fan": "Orglkorallnfächa", "block.minecraft.tube_coral_wall_fan": "Orglkorallnwondfächa", "block.minecraft.tuff": "Tuffstoa", "block.minecraft.tuff_brick_slab": "Tuffstoaziaglstufn", "block.minecraft.tuff_brick_stairs": "Tuffstoaziegltreppn", "block.minecraft.tuff_brick_wall": "Tuffs<PERSON>aziag<PERSON><PERSON>", "block.minecraft.tuff_bricks": "Tuffstoaziagl", "block.minecraft.tuff_slab": "Tuffstoastufn", "block.minecraft.tuff_stairs": "Tuffstoatreppn", "block.minecraft.tuff_wall": "Tuffstoamaua", "block.minecraft.turtle_egg": "Schüdkrötnei", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines_plant": "Zwirbllianenpflonz", "block.minecraft.vault": "Tresor", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "Leernluft", "block.minecraft.wall_torch": "Wondfockl", "block.minecraft.warped_button": "Wirrknopf", "block.minecraft.warped_door": "W<PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "Wirrzaun", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "Wirrpüz", "block.minecraft.warped_hanging_sign": "W<PERSON>rhängeschüd", "block.minecraft.warped_hyphae": "Wirrhyphn", "block.minecraft.warped_nylium": "W<PERSON>rnetzl", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "Wirrdruckplottn", "block.minecraft.warped_roots": "Wirrwurzln", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_slab": "Wirrstufn", "block.minecraft.warped_stairs": "Wirrtreppn", "block.minecraft.warped_stem": "Wirrstomm", "block.minecraft.warped_trapdoor": "Wirrfoitir", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>dh<PERSON>chüd", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wart_block": "Wirrwarznblock", "block.minecraft.water": "Wossa", "block.minecraft.water_cauldron": "Wossakessl", "block.minecraft.waxed_chiseled_copper": "Gwochsta gmoaßlta <PERSON>lock", "block.minecraft.waxed_copper_block": "Gwochsta Kupfablock", "block.minecraft.waxed_copper_bulb": "Gwochste Kupfalompn", "block.minecraft.waxed_copper_door": "Gwochste Kupfatir", "block.minecraft.waxed_copper_grate": "Gwochsta Kupfarost", "block.minecraft.waxed_copper_trapdoor": "Gwochste Kupfafoitir", "block.minecraft.waxed_cut_copper": "Gwochsta gschnittena Kupfablock", "block.minecraft.waxed_cut_copper_slab": "Gwochste gschnittene Kupfastufn", "block.minecraft.waxed_cut_copper_stairs": "Gwochste gschnittene Kupfatreppn", "block.minecraft.waxed_exposed_chiseled_copper": "Gwochsta ausgsetzta gmoaßlta Kupfablock", "block.minecraft.waxed_exposed_copper": "Gwochsta ausgsetzta Kupfablock", "block.minecraft.waxed_exposed_copper_bulb": "Gwochste ausgsetzte Kupfalompn", "block.minecraft.waxed_exposed_copper_door": "Gwochste ausgsetzte Kupfatir", "block.minecraft.waxed_exposed_copper_grate": "Gwochsta ausgsetzta Kupfarost", "block.minecraft.waxed_exposed_copper_trapdoor": "Gwochste ausgsetzte Kupfafoitir", "block.minecraft.waxed_exposed_cut_copper": "Gwochsta ausgsetzta gschnittena Kupfablock", "block.minecraft.waxed_exposed_cut_copper_slab": "Gwochste ausgsetzte gschnittene Kupferstufn", "block.minecraft.waxed_exposed_cut_copper_stairs": "Gwochste ausgsetzte gschnittene Kupfertreppn", "block.minecraft.waxed_oxidized_chiseled_copper": "Gwochsta varrosteta g<PERSON>", "block.minecraft.waxed_oxidized_copper": "Gwochsta varrosteta <PERSON>", "block.minecraft.waxed_oxidized_copper_bulb": "Gwochste varrostete Kupfalompn", "block.minecraft.waxed_oxidized_copper_door": "Gwochste varrostete Kupfatir", "block.minecraft.waxed_oxidized_copper_grate": "Gwochsta varros<PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "Gwochste varrostete Kupfafoitir", "block.minecraft.waxed_oxidized_cut_copper": "Gwochsta varrosteta g<PERSON><PERSON>", "block.minecraft.waxed_oxidized_cut_copper_slab": "Gwochste varrostete gschnittene Kupfastufn", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Gwochste varrostete gschnittene Kupfatreppn", "block.minecraft.waxed_weathered_chiseled_copper": "Gwochsta vawittata gmoaß<PERSON>", "block.minecraft.waxed_weathered_copper": "Gwochsta vawittata <PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "Gwochste vawittate Kupfalompn", "block.minecraft.waxed_weathered_copper_door": "Gwochste vawittate Kupfatir", "block.minecraft.waxed_weathered_copper_grate": "Gwochsta vawittate Kupfarost", "block.minecraft.waxed_weathered_copper_trapdoor": "Gwochste vawittate Kupfafoitir", "block.minecraft.waxed_weathered_cut_copper": "Gwochsta vawittata gschnittena Kupfablock", "block.minecraft.waxed_weathered_cut_copper_slab": "Gwochste vawittate geschnittene Kupfastufn", "block.minecraft.waxed_weathered_cut_copper_stairs": "Gwochste vawittate geschnittene Kupfatreppn", "block.minecraft.weathered_chiseled_copper": "Vawittata g<PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper": "Vawitt<PERSON>", "block.minecraft.weathered_copper_bulb": "Vawittate Kupfalompn", "block.minecraft.weathered_copper_door": "Vawittate Kupfatir", "block.minecraft.weathered_copper_grate": "Vawitt<PERSON>", "block.minecraft.weathered_copper_trapdoor": "Vawittate Kupfafoitir", "block.minecraft.weathered_cut_copper": "Vawittata g<PERSON>ttena <PERSON>lock", "block.minecraft.weathered_cut_copper_slab": "Vawittate gschnittene Kupfastufn", "block.minecraft.weathered_cut_copper_stairs": "Vawittate gschnittne Kupfatreppn", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "Trauerlinanenpflonz", "block.minecraft.wet_sponge": "<PERSON><PERSON>", "block.minecraft.wheat": "Weiznpflonzn", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.white_candle": "Weiße Kerzn", "block.minecraft.white_candle_cake": "<PERSON>chn mit weißa <PERSON>rzn", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON>ßa Trocknbeton", "block.minecraft.white_glazed_terracotta": "Weiß glosiate Keramik", "block.minecraft.white_shulker_box": "Weiße Shulkerkistn", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.white_stained_glass_pane": "Weiße Glosscheibn", "block.minecraft.white_terracotta": "Weiße Keramik", "block.minecraft.white_tulip": "Weiße Tulpn", "block.minecraft.white_wool": "Weiße Woin", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "With<PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Witherskelettschädl", "block.minecraft.wither_skeleton_wall_skull": "Witherskelett-Wondschädl", "block.minecraft.yellow_banner": "Gelbs Banna", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON> Bett", "block.minecraft.yellow_candle": "Gelbe Kerzn", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON>n mit gelba Kerzn", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Gelb glosiate Keramik", "block.minecraft.yellow_shulker_box": "Gelbe Shulkerkistn", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> Glos", "block.minecraft.yellow_stained_glass_pane": "Gelbe Glosscheibn", "block.minecraft.yellow_terracotta": "Gelbe Keramik", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.zombie_head": "Zombiekopf", "block.minecraft.zombie_wall_head": "Zombie-<PERSON><PERSON><PERSON><PERSON>", "book.byAuthor": "von %1$s", "book.edit.title": "Buach ända Fensta ", "book.editTitle": "Buachnom eigebn:", "book.finalizeButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "book.finalizeWarning": "Hinweis! Wenn du <PERSON> Buach untaschreibst, kus nimma beorbeitet wean.", "book.generation.0": "Original", "book.generation.1": "Kopie vom Original", "book.generation.2": "<PERSON><PERSON> von an<PERSON>", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Ungültige Buachdatn *", "book.pageIndicator": "Seitn %1$s von %2$s", "book.page_button.next": "Nächste Seitn", "book.page_button.previous": "Vorherige Seitn", "book.sign.title": "Buach-untaschreib-Büdschirm", "book.sign.titlebox": "Titl", "book.signButton": "<PERSON><PERSON>", "book.view.title": "Buach-auschau-Büdschirm", "build.tooHigh": "De maximale Bauhechn is %s", "chat.cannotSend": "Chat-<PERSON><PERSON><PERSON><PERSON> hot ned vaschickt wean kina", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Druck zum Teleportian", "chat.copy": "<PERSON> k<PERSON>", "chat.copy.click": "<PERSON><PERSON><PERSON>, um in de Zwischnoblog zu kopiern", "chat.deleted_marker": "<PERSON><PERSON> is vom Server glöscht woadn.", "chat.disabled.chain_broken": "Da Chat wuad wegn ana duach<PERSON>chnen Kettn untabrochn. <PERSON><PERSON> vasua<PERSON>, di nu amoi zu vabindn.", "chat.disabled.expiredProfileKey": "Da Chat wuad aufgrund von an oglaufenen öffantlichn Profülschlissl deaktiviert. <PERSON><PERSON> vasuach, di erneit zu vabinden.", "chat.disabled.invalid_command_signature": "Der Auftrog woa ned wia erwoated oda fahlnde Auftrogsargument-Unterschrifftn", "chat.disabled.invalid_signature": "Da Chat hot a ungültige Signatur aufwisn. <PERSON><PERSON> vasua<PERSON>, di nei zu vabindn.", "chat.disabled.launcher": "Da Chat isch durchd Launcheristellung deaktiviert. Nochricht kann id gsended wöra.", "chat.disabled.missingProfileKey": "<PERSON> is wegn an fehlndn öffntlichn Profüschlüssls deaktiviat. Bitte tua di nu amoi vabindn.", "chat.disabled.options": "<PERSON><PERSON> is in da Clienteischtellunga ausgscholtn.", "chat.disabled.out_of_order_chat": "Der Schat wuad in foischa Reihenfolge empfongen. Hot sich de Systemzeit vaändat?", "chat.disabled.profile": "Chat isch duach d Kontoischtellunga nid aloubt. Druck ‚%s‘ füa mea Informationen.", "chat.disabled.profile.moreInfo": "Chat is duach Kontoeinstöllungen net alaubt. Nochrichtn kennan net gsendet oda aunzeigt werdn.", "chat.editBox": "schre<PERSON>n", "chat.filtered": "Vom Server gfiltat.", "chat.filtered_full": "Da Server hot dei Nochricht fia anige Spüla vasteckt.", "chat.link.confirm": "<PERSON><PERSON> <PERSON> sic<PERSON>, dass du de foignde Website aufmochn wüst?", "chat.link.confirmTrusted": "Wüst du diesn Link öffnan oda in de Zwischnoblog kopian?", "chat.link.open": "Aufmochn", "chat.link.warning": "<PERSON>ch nia <PERSON><PERSON>, deana du ned vatraust!", "chat.queue": "[+%s auss<PERSON><PERSON><PERSON>]", "chat.square_brackets": "[%s]", "chat.tag.error": "Da Server hot a ungültige Nochricht gschickt.", "chat.tag.modified": "<PERSON><PERSON><PERSON><PERSON> is von Server geändat woadn. Original:", "chat.tag.not_secure": "Unverifiziate Nochricht. Konn nit gmöded weadn.", "chat.tag.system": "Server-Nochricht. Konn nit gmöded weadn.", "chat.tag.system_single_player": "Server-<PERSON><PERSON><PERSON><PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s hot de Aufgob %s obgschlossn", "chat.type.advancement.goal": "%s hot as Zü %s erreicht", "chat.type.advancement.task": "%s hot den Fortschritt %s erreicht", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Team benochrichtign", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sogt %s", "chat.validation_error": "Chatüberprüfungsfehla", "chat_screen.message": "<PERSON>ua sendane Nochricht: %s", "chat_screen.title": "Nochrichtnfensta", "chat_screen.usage": "Nochricht eingebn und de Eingobetastn druckan zum Sendn", "chunk.toast.checkLog": "Schau im Ausgabeverlauf fia mehr Infos", "chunk.toast.loadFailure": "Chunk bei %s hot ned glodn wean kennan", "chunk.toast.lowDiskSpace": "Weinig freia Speichaplotz!", "chunk.toast.lowDiskSpace.description": "<PERSON><PERSON><PERSON> konn vielleicht ned gespeichat wean.", "chunk.toast.saveFailure": "Chunk bei %s hot ned gspeichat wean kennan", "clear.failed.multiple": "Bei %s Spiela san koane Gegnstend gfundn wordn", "clear.failed.single": "Bei %s san koane Gegnstend gfundn wordn", "color.minecraft.black": "Schworz", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.gray": "G<PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Hellblau", "color.minecraft.light_gray": "Hellgrau", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Orange", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON>", "color.minecraft.red": "Rot", "color.minecraft.white": "<PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[DO]", "command.context.parse_error": "%s an Stelle %s: %s", "command.exception": "Befehl fehlahoft: '%s'", "command.expected.separator": "Leerzeichen erwoatet zum Obschliaßn oanes Arguments erwoatet, oba oghängte Datn gfundn", "command.failed": "<PERSON><PERSON><PERSON><PERSON> da Ausführung von <PERSON> is a unerwordata Fehla auftretn", "command.forkLimit": "<PERSON><PERSON><PERSON><PERSON> an Befehlsrahmen (%s) erreicht", "command.unknown.argument": "Ungütigs Befehlsargument", "command.unknown.command": "A unbekannta oder unvollständiga <PERSON>hl, schau untn für<PERSON>", "commands.advancement.criterionNotFound": "Da Fortschritt %1$s enthält ned des Kriterium '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Kriterium '%s' vom Fortschritt %s hot %s <PERSON>piela ned gwährt wean kina, weil es scho erfüllt wor", "commands.advancement.grant.criterion.to.many.success": "Kriterium '%s' vom Fortschritt %s is %s Spielan gwährt wordn", "commands.advancement.grant.criterion.to.one.failure": "Kriterium '%s' vom Fortschritt %s hot %s ned gwährt wean kina, weil es scho erfüllt is", "commands.advancement.grant.criterion.to.one.success": "Kriterium '%s' vom Fortschritt %s is %s gwährt wordn", "commands.advancement.grant.many.to.many.failure": "%s Fortschritte hobn %s Spiela ned gwährt wean kina, weil sie scho erreicht wordn san", "commands.advancement.grant.many.to.many.success": "%s Fortschritte san %s Spiela gwährt wordn", "commands.advancement.grant.many.to.one.failure": "%s Fortschritte hobn %s ned gwährt wean kina, weil sie scho erreicht wordn san", "commands.advancement.grant.many.to.one.success": "%s Fortschritte san %s gwährt wordn", "commands.advancement.grant.one.to.many.failure": "Fortschritt %s hot %s <PERSON>piela ned gwährt wean kina, weil er scho erreicht wordn is", "commands.advancement.grant.one.to.many.success": "Fortschritt %s is %s Spiela gwährt wordn", "commands.advancement.grant.one.to.one.failure": "Fortschritt %s hot %s ned gwährt wean kina, weil er scho erreicht wordn is", "commands.advancement.grant.one.to.one.success": "Fortschritt %s is %s gwährt wordn", "commands.advancement.revoke.criterion.to.many.failure": "Kriterium ‚%s‘ vom Fortschritt %s hot %s <PERSON>pie<PERSON> ned entzogn wean kina, weil es ned erfüllt wor", "commands.advancement.revoke.criterion.to.many.success": "Kriterium '%s' vom Fortschritt %s is %s Spielan entzogn wordn", "commands.advancement.revoke.criterion.to.one.failure": "Kriterium '%s' vom Fortschritt %s hot %s ned entzogn wean kina, weil es ned erfüllt wor", "commands.advancement.revoke.criterion.to.one.success": "Kriterium '%s' vom Fortschritt %s is %s entzogn wordn", "commands.advancement.revoke.many.to.many.failure": "%s Fortschritte hobn %s <PERSON><PERSON><PERSON> ned entzogn wean kina, weil sie ned erreicht wordn san", "commands.advancement.revoke.many.to.many.success": "%s Fortschritte san %s Spiela entzogn wordn", "commands.advancement.revoke.many.to.one.failure": "%s Fortschritte hobn %s ned entzogn wean kina, weil sie ned erreicht wordn san", "commands.advancement.revoke.many.to.one.success": "%s Fortschritte san %s entzogn wordn", "commands.advancement.revoke.one.to.many.failure": "Fortschritt %s hot %s <PERSON>pielan ned entzogn wean kina, weil es ned erreicht wordn is", "commands.advancement.revoke.one.to.many.success": "Fortschritt %s is %s Spielan entzogn wordn", "commands.advancement.revoke.one.to.one.failure": "Fortschritt %s hot %s ned entzogn wean kina, weil er ned erreicht wordn is", "commands.advancement.revoke.one.to.one.success": "Fortschritt %s is %s entzogn wordn", "commands.attribute.base_value.get.success": "Grundwert vom Attribut %s vom Objekt %s is %s", "commands.attribute.base_value.reset.success": "Grudnweat fürs Attribut %s für Entität %s aufn Standard %s zrucksetzt", "commands.attribute.base_value.set.success": "Grundwert vom Attribut %s vom Objekt %s is auf %s gesetzt wordn", "commands.attribute.failed.entity": "%s is koa gültigs Objekt fia den Befehl", "commands.attribute.failed.modifier_already_present": "Modifikator %s is bei Attribut %s vom Objekt %s bereits vorhondn", "commands.attribute.failed.no_attribute": "Objekt %s hot koa Attribut %s", "commands.attribute.failed.no_modifier": "Attribut %s vom Objekt %s hot koan Modifikator %s", "commands.attribute.modifier.add.success": "Modifikator %s weat zum Attribut %s des Objekts %s hinzugefügt", "commands.attribute.modifier.remove.success": "Modifikator %s is vom Attribut %s vom Objekt %s entfernt wordn", "commands.attribute.modifier.value.get.success": "Wert vom Modifikator %s vom Attribut %s vom Objekt %s is %s", "commands.attribute.value.get.success": "Wert vom Attribut %s vom Objekt %s is %s", "commands.ban.failed": "<PERSON><PERSON> is nix vaändat wordn. <PERSON><PERSON> is scho gspat", "commands.ban.success": "%s is gspat wordn: %s", "commands.banip.failed": "Es is nix vaändat wordn. De IP is scho gspat", "commands.banip.info": "De Sperrung betrifft %s <PERSON><PERSON><PERSON>: %s", "commands.banip.invalid": "Ungütige IP-Adress oder unbekonnta Spiela", "commands.banip.success": "IP-Adress %s is gspat wordn: %s", "commands.banlist.entry": "%s is von %s gspat wordn: %s", "commands.banlist.entry.unknown": "(unbekonnt)", "commands.banlist.list": "Es gibt %s sperrung(a):", "commands.banlist.none": "<PERSON><PERSON> gibt koane <PERSON>", "commands.bossbar.create.failed": "A Bossleistn mit da ID ‚%s‘ existiat scho", "commands.bossbar.create.success": "Benutzadefinierte Bossleistn %s is erstöt wordn", "commands.bossbar.get.max": "Da Maximalwert vo da benutzadefiniertn Bossleistn %s is %s", "commands.bossbar.get.players.none": "Benutzadefinierte Bossleistn %s hot aktuell koane Spiela online", "commands.bossbar.get.players.some": "Benutzadefinierte Bosslischte %s hot grad %s Spiela(r) online: %s", "commands.bossbar.get.value": "Da aktuelle Wert vo da benutzadefiniertn Bossleistn %s is %s", "commands.bossbar.get.visible.hidden": "Benutzadefinierte Bossleistn %s is aktuell vasteckt", "commands.bossbar.get.visible.visible": "Benutzadefinierte Bossleistn %s is aktuell sichtbor", "commands.bossbar.list.bars.none": "Es san koane benutzadefiniertn Bossleistn aktiv", "commands.bossbar.list.bars.some": "Es sind %s benutzadefinierte Bosslischte(a) aktiv: %s", "commands.bossbar.remove.success": "Benutzadefinierte Bossleistn %s is entfernt wordn", "commands.bossbar.set.color.success": "Forb von da benutzadefiniertn Bossleistn %s is geändat wordn", "commands.bossbar.set.color.unchanged": "Es is nix vaändat wordn. <PERSON> Bossleistn hot scho de <PERSON>b", "commands.bossbar.set.max.success": "Maximalwert von da benutzadefiniertn Bossleistn %s is auf %s geändat wordn", "commands.bossbar.set.max.unchanged": "Es is nix vaändat wordn. De Bossleistn hot scho des Maximum", "commands.bossbar.set.name.success": "Benutzadefinierte Bossleistn %s is umbenonnt wordn", "commands.bossbar.set.name.unchanged": "Es is nix vaändat wordn. De Bossleistn hot scho den Nom", "commands.bossbar.set.players.success.none": "Benutzadefinierte Bossleistn %s hot koane S<PERSON>la mehr", "commands.bossbar.set.players.success.some": "Benutzadefinierte Bosslischte %s hot jetzt %s Spiela(r): %s", "commands.bossbar.set.players.unchanged": "Es is nix vaändat wordn. Dia Spiela san scho auf da Bossleistn, es gib koan zum Hinzuafügn oda Entfernen", "commands.bossbar.set.style.success": "Einteilung von da benutzadefiniertn Bossleistn %s is geändat wordn", "commands.bossbar.set.style.unchanged": "Es is nix vaändat wordn. De Bossleistn hot scho de Eiteilung", "commands.bossbar.set.value.success": "<PERSON><PERSON> von da benutzadefiniertn Bossleistn %s is auf %s geändat wordn", "commands.bossbar.set.value.unchanged": "Es is nix vaändat wordn. De Bossleistn hot scho den Wert", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON> is nix vaändat wordn. <PERSON> is scho <PERSON>t", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON> is nix vaändat wordn. <PERSON> is scho sichtbor", "commands.bossbar.set.visible.success.hidden": "Benutzadefinierte Bossleistn %s is jetz vasteckt", "commands.bossbar.set.visible.success.visible": "Benutzadefinierte Bossleistn %s is jetz sichtbor", "commands.bossbar.unknown": "Es exisitat koa Bossleistn mit da ID ‚%s‘", "commands.clear.success.multiple": "%s Gegnstond/-end san vo %s <PERSON><PERSON>ler entfernt wordn", "commands.clear.success.single": "%s Gegnston/-end san vo Spieler %s entfernt wordn", "commands.clear.test.multiple": "%s übaeistimmende Gegnstend san bei %s Spiela gfundn wordn", "commands.clear.test.single": "%s übereinstimmende(r) Gegenstand/‐stände wurde(n) bei %s gefunden", "commands.clone.failed": "<PERSON> bleck san kopiert woan", "commands.clone.overlap": "Quell- und Zübereich kennan se ned übaschneidn", "commands.clone.success": "%s Blöck(e) afolgreich kopiat", "commands.clone.toobig": "Zu vü Bleck im ongegebenen Bereich (maximal %s, ongegeben %s)", "commands.damage.invulnerable": "Zül konn duarch den ongegobenen Schodnstyp net verlotzt werdn", "commands.damage.success": "%s <PERSON><PERSON><PERSON> wurd auf %s angwandt", "commands.data.block.get": "%s vo Block bei (%s, %s, %s) mit %s multipliziat is %s", "commands.data.block.invalid": "Züblock is koa Blockobjekt mehr", "commands.data.block.modified": "Blockdatn bei (%s, %s, %s) san geändat wordn", "commands.data.block.query": "%s, %s, %s hot de foigndn Blockdatn: %s", "commands.data.entity.get": "%s von %s mit %s multipliziat is %s", "commands.data.entity.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> kinan ned va<PERSON>ndat wean", "commands.data.entity.modified": "Objektdatn von %s san geändat wordn", "commands.data.entity.query": "%s hot de foigndn Objektdatn: %s", "commands.data.get.invalid": "Konn '%s' ned obfrogn, es san nua numerische Typn dalabt", "commands.data.get.multiple": "Des Argument akzeptiat nua oan oanzelnen NBT-Wert", "commands.data.get.unknown": "Konn '%s' ned o<PERSON><PERSON><PERSON>, de Eignschoft existiat ned", "commands.data.merge.failed": "Nix hot sich geändert. De eigebenen Eignschoftn habn scho den Wert", "commands.data.modify.expected_list": "<PERSON>e erwortet, '%s' erhoitn", "commands.data.modify.expected_object": "Ob<PERSON><PERSON> er<PERSON><PERSON><PERSON>, '%s' erhoitn", "commands.data.modify.expected_value": "Wert erwortet, erholtn: %s", "commands.data.modify.invalid_index": "Ungültiga Listnindex: %s", "commands.data.modify.invalid_substring": "Ungültige Obschnittsindieze: %s bis %s", "commands.data.storage.get": "%s im Speicha %s mit %s multipliziat is %s", "commands.data.storage.modified": "Speicha %s is geändat wordn", "commands.data.storage.query": "Speicha %s hot etz foigende Datn: %s", "commands.datapack.create.already_exists": "A Paket mit dem Nomen '%s' gibts schon", "commands.datapack.create.invalid_full_name": "Ungültiger Name ‚%s‘ für neues Datenpaket", "commands.datapack.create.invalid_name": "Ungültige Zeichn im neichen Paketnomen '%s'", "commands.datapack.create.io_failure": "Dotnpaket mitm Nomen '%s' konn ned erstöllt wean; s<PERSON><PERSON> <PERSON>koll", "commands.datapack.create.metadata_encode_failure": "Metadatn fürs Paket mit dem Nomen '%s' hom ned koadiat wean kennen: %s", "commands.datapack.create.success": "Neichs leares Paket mitm Nomen '%s' is eastöllt woan", "commands.datapack.disable.failed": "Datnpaket '%s' is scho deaktiviat!", "commands.datapack.disable.failed.feature": "Dotnpaket '%s' konn ned ausgschoitn wean, weil es Teil von am aktivn Schoitas is!", "commands.datapack.enable.failed": "Datnpaket '%s' is scho aktiviat!", "commands.datapack.enable.failed.no_flags": "Es Dotnpacket '%s' konn nit aktiviat weadn, weil de benötigtn Scholta fia de Wöd nit aktiv san: %s!", "commands.datapack.list.available.none": "Es san koane weitaren Datnpakete vafügbor", "commands.datapack.list.available.success": "Es sind %s Datapacket(e) vafügba: %s", "commands.datapack.list.enabled.none": "Es san koane Datnpakete aktiviat", "commands.datapack.list.enabled.success": "Es sind %s Dataacket(e) igschalta: %s", "commands.datapack.modify.disable": "Deoktiviere Datnpaket %s", "commands.datapack.modify.enable": "Oktiviere Datnpaket %s", "commands.datapack.unknown": "Unbekonnts Datnpaket '%s'", "commands.debug.alreadyRunning": "Die Tick-Aufzeichnung wurd scho gestartet", "commands.debug.function.noRecursion": "<PERSON><PERSON> koan net as einer Funk<PERSON> heras afgezochnet werdn", "commands.debug.function.noReturnRun": "Ablaufaufzeichnung kann nicht mit „return run“ verwendet werden", "commands.debug.function.success.multiple": "%s Be<PERSON>hl(e) von %s Funktionen wurde(n) in die Ausgabedatei %s aufgezeichnet", "commands.debug.function.success.single": "%s Be<PERSON>hl(e) der Funktion ‚%s‘ wurde(n) in die Ausgabedatei %s aufgezeichnet", "commands.debug.function.traceFailed": "<PERSON><PERSON>o bam durchschaun der Funktio'", "commands.debug.notRunning": "Die Tick-Aufzeichnung is ned aktiv", "commands.debug.started": "Tick-Aufzeichnung wurd gestartet", "commands.debug.stopped": "Tick-Aufzeichnung wurd noch %s Sekunden und %s Ticks gstoppt (%s Ticks pro Sekunde)", "commands.defaultgamemode.success": "Da Standartspümodus auf dea Wöd is jetz %s", "commands.deop.failed": "<PERSON>s is nix vaändat wordn. <PERSON><PERSON> is koa Operator", "commands.deop.success": "%s is koa Serveroperator mehr", "commands.dialog.clear.multiple": "Dialog wird für %s Spieler geschlossen", "commands.dialog.clear.single": "Dialog wird für %s geschlossen", "commands.dialog.show.multiple": "Dialog wird %s Spielern angezeigt", "commands.dialog.show.single": "Dialog wird %s angezeigt", "commands.difficulty.failure": "De Schwierigkeit hot si ned geändat, sie lieg scho bei %s", "commands.difficulty.query": "De Schwierigkeit is %s", "commands.difficulty.success": "De Schwierigkeit is zu %s gsetzt wordn", "commands.drop.no_held_items": "Objekt konn koane Gegnstend trogn", "commands.drop.no_loot_table": "Objekt %s hot koa Beutetabelle", "commands.drop.no_loot_table.block": "Block %s hot ka Beutetabelln", "commands.drop.success.multiple": "%s <PERSON><PERSON><PERSON><PERSON> foin glossn", "commands.drop.success.multiple_with_table": "%s <PERSON><PERSON><PERSON><PERSON> aus da Beutetabelle %s foin glossn", "commands.drop.success.single": "%s %s folln glossn", "commands.drop.success.single_with_table": "%s %s aus Beutetabelln %s folln glossn", "commands.effect.clear.everything.failed": "Zü hot koane Effekte zum Entfernen", "commands.effect.clear.everything.success.multiple": "Oi Effekte san vo %s Zielen entfernt wordn", "commands.effect.clear.everything.success.single": "Oi Effekte san vo %s entfernt wordn", "commands.effect.clear.specific.failed": "Zü hot den Effekt ned", "commands.effect.clear.specific.success.multiple": "Effekt %s is vo %s Zielen entfernt wordn", "commands.effect.clear.specific.success.single": "Effekt %s is vo %s entfernt wordn", "commands.effect.give.failed": "Da Effekt konn ned gebn wean (<PERSON><PERSON> is entweder dagegn immun oda hot an stärkaren Effekt)", "commands.effect.give.success.multiple": "Effekt %s is auf %s Ziele ugwendt wordn", "commands.effect.give.success.single": "Effekt %s is auf %s ugwendt wordn", "commands.enchant.failed": "Es is nix va<PERSON><PERSON>t wordn, de Ziele hobn entweda koan <PERSON> in da Hond oda de Vazaubarung hot ned ugwendt wean kina", "commands.enchant.failed.entity": "%s is ka <PERSON>e Eingob fia des Befehl", "commands.enchant.failed.incompatible": "%s untastützt de Vazaubarung ned", "commands.enchant.failed.itemless": "%s hoit koan <PERSON>", "commands.enchant.failed.level": "Stufn %s is zu hoch, ded maximale Stufn vo dea Vazaubarung betrog %s", "commands.enchant.success.multiple": "Vazaubarung %s is auf %s Objekte ugwendt wordn", "commands.enchant.success.single": "Vazaubarung %s aufn Gegnstond von %s ugwendt", "commands.execute.blocks.toobig": "<PERSON><PERSON> v<PERSON> in da spezifischn Fläche (maximal %s, ongebn %s)", "commands.execute.conditional.fail": "Test fehlgschlogn", "commands.execute.conditional.fail_count": "Test fehlgschlogn, Anzohl: %s", "commands.execute.conditional.pass": "Test bestondn", "commands.execute.conditional.pass_count": "Test bestondn, <PERSON><PERSON><PERSON>: %s", "commands.execute.function.instantiationFailure": "Fuanktion ‚%s‘ konnte nicht ausgewertet werden: %s", "commands.experience.add.levels.success.multiple": "%s Erfohrungsstufn san an %s Spiela gebn wordn", "commands.experience.add.levels.success.single": "%s Erfohrungsstufn san an %s gegebn wordn", "commands.experience.add.points.success.multiple": "%s Erfohrungspunkte san an %s Spiela gegebn wordn", "commands.experience.add.points.success.single": "%s Erfohrungspunkte san an %s gebn wordn", "commands.experience.query.levels": "%s hot %s Erfoahrungsstufn", "commands.experience.query.points": "%s hot %s Erfohrungspunkte", "commands.experience.set.levels.success.multiple": "Erfohrungsstufn von %2$s Spielan san auf %1$s gsetzt wordn", "commands.experience.set.levels.success.single": "Erfohrungsstufn vo %s san auf %s gsetzt wordn", "commands.experience.set.points.invalid": "Erfohrungspunkte kinan ned üba dem Maximum da derzeitign Stufn vom Spiela gsetzt wean", "commands.experience.set.points.success.multiple": "Erfohrungspunkte von %s Spielan san auf %s gsetzt wordn", "commands.experience.set.points.success.single": "Erfohrungspunkte vo %s san auf %s gsetzt wordn", "commands.fill.failed": "<PERSON><PERSON> san platziat wordn", "commands.fill.success": "%s Blöck(e) sind afolgrich plaziert wora", "commands.fill.toobig": "<PERSON><PERSON> v<PERSON> in da spezifischn Fläche (maximal %s, ongebn %s)", "commands.fillbiome.success": "Biome zwischn %s, %s, %s und %s, %s, %s festgleg", "commands.fillbiome.success.count": "%s Biomitrag/Biomiträg zwischa %s, %s, %s und %s,%s,%s feschtglegt", "commands.fillbiome.toobig": "Zu vü Blöcke im ongebenen Raum (maximal %s, ongebn %s)", "commands.forceload.added.failure": "Koane Chunks san für de dauerhofte Lodn markiat wordn", "commands.forceload.added.multiple": "%s Chunks in %s von %s bis %s wean jetzt dauahoft glodn", "commands.forceload.added.none": "In %s wean koane <PERSON> daua<PERSON>t glodn", "commands.forceload.added.single": "Da markiate Chunk %s in %s wead dauerhoft glodn", "commands.forceload.list.multiple": "In %2$s wean %1$s Chunks dauahoft glodn: %s", "commands.forceload.list.single": "In %s wead a Chunk dauahoft glodn: %s", "commands.forceload.query.failure": "Chunk %s in %s wead ned dauahoft glodn", "commands.forceload.query.success": "Chunk %s in %s wead dauahoft glodn", "commands.forceload.removed.all": "In %s wean jetz koane Chunks mehr dauahoft glodn", "commands.forceload.removed.failure": "Nix hot si ge<PERSON>ndat, de Chunks san bis jetz a nu ned daua<PERSON>t glodn woan", "commands.forceload.removed.multiple": "%s Chunks in %s von %s bis %s wean jetz nimma dauahoft glodn", "commands.forceload.removed.single": "Chunk %s in %s wead jetz nimma dauahoft glodn", "commands.forceload.toobig": "Zu vü Chunks im ongegebenen Bereich (maximal %s, ongegeben %s)", "commands.function.error.argument_not_compound": "Ungültiger Aurgumenttyp: ‚%s‘ ist kein Verboand", "commands.function.error.missing_argument": "Fehlndes Argument %2$s füa de Funktion %1$s", "commands.function.error.missing_arguments": "Fehlende Aurgumente für die Fuanktion ‚%s‘", "commands.function.error.parse": "Bei da Auswertung des Makros '%s' hot da Befehl '%s' an Fehla vaursocht: %s", "commands.function.instantiationFailure": "Funktion %s hod net ausgwertet wean kennan: %s", "commands.function.result": "Funktion '%s' hot %s ausgebn", "commands.function.scheduled.multiple": "Fuanktionen laufen %s", "commands.function.scheduled.no_functions": "Es san kane Funktionen mit dem Nomen '%s' gfundn wean kennan", "commands.function.scheduled.single": "Fuanktion Laeuft %s", "commands.function.success.multiple": "%s Befehl(e) von %s Funktionen wurde(n) ausgeführt", "commands.function.success.multiple.result": "%s Funktiona usgführt", "commands.function.success.single": "%s Be<PERSON>hl(e) der Funktion ‚%s‘ wurde(n) ausgeführt", "commands.function.success.single.result": "Funktion '%2$s' is zruck %1$s", "commands.gamemode.success.other": "Da Spümodus is von %s auf %s gsetzt wordn", "commands.gamemode.success.self": "<PERSON><PERSON> is auf %s gsetzt wordn", "commands.gamerule.query": "De Spüregl %s is derzeit auf \"%s\" gsetzt", "commands.gamerule.set": "Spüregl %s is auf \"%s\" gsetzt wordn", "commands.give.failed.toomanyitems": "Kon nit mehr als %s %s gebn", "commands.give.success.multiple": "%s %s an %s Spiela gebn", "commands.give.success.single": "%s %s an %s gebn", "commands.help.failed": "Unbekonnta Befehl oda ungenügende Berechtigungen", "commands.item.block.set.success": "A Slot bei (%s, %s, %s) is mit %s austauscht wordn", "commands.item.entity.set.success.multiple": "A Slot von %s <PERSON>b<PERSON><PERSON><PERSON> is mit %s austauscht wordn", "commands.item.entity.set.success.single": "A Slot von %s is mit %s austauscht wordn", "commands.item.source.no_such_slot": "Quelle besitzt Inventarplatz %s ned", "commands.item.source.not_a_container": "Quellposition %s, %s, %s besitzt koa Inventar", "commands.item.target.no_changed.known_item": "Koa Zü lässt %s an Inventarplatz %s zu", "commands.item.target.no_changes": "Koa Zü lässt Gegnstände an Inventarplatz %s zu", "commands.item.target.no_such_slot": "Zü hot Inventarplotz %s ned", "commands.item.target.not_a_container": "Züposition %s, %s, %s besitzt koa Inventar", "commands.jfr.dump.failed": "<PERSON><PERSON><PERSON> bam <PERSON><PERSON><PERSON><PERSON> von <PERSON>-Aufzeichnung: %s", "commands.jfr.start.failed": "<PERSON><PERSON><PERSON> b<PERSON> von da JFR-Aufzeichnung", "commands.jfr.started": "JFR-Aufzeichnung gstartet", "commands.jfr.stopped": "JFR-Aufzeichnung gstoppt und in %s gspeichat", "commands.kick.owner.failed": "Da <PERSON> am LAN-Spül konn ned ausegwoafn wean", "commands.kick.singleplayer.failed": "In am Offlinem Anzlspilaspül konn ned ausegworfn wean", "commands.kick.success": "%s is ausigschmissn wordn: %s", "commands.kill.success.multiple": "%s Objekte san entfernt wordn", "commands.kill.success.single": "%s umbrocht", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Es san %s vo maximoi %s Spiela online: %s", "commands.locate.biome.not_found": "Es kun koa Biom des Typs „%s“ in angmessener Entförnung gfundn werdn. ", "commands.locate.biome.success": "Des nehste %s is bei %s (%s Blöcke weg)", "commands.locate.poi.not_found": "<PERSON>s hod in passenda Entfernung koa Zü vom Typ „%s“ gfundn wean kina", "commands.locate.poi.success": "Des neheste %s is bei %s (%s Blöcke entfeant)", "commands.locate.structure.invalid": "<PERSON>s gib kane Struktur vom Typ \"%s\"", "commands.locate.structure.not_found": "Es konn ka Struktur vom Typ \"%s\" in da Nähe gefunden werdn kön", "commands.locate.structure.success": "As naxte %s-Bauwerk is bei %s (%s Bleck entfernt)", "commands.message.display.incoming": "%s flüstat dia zua: %s", "commands.message.display.outgoing": "Du flüstast %s zua: %s", "commands.op.failed": "<PERSON><PERSON> is nix vaändat wordn. <PERSON> is scho a Operator", "commands.op.success": "%s is zum Serveroperator ernonnt wordn", "commands.pardon.failed": "<PERSON><PERSON> is nix vaändat wordn. <PERSON> is ned gspat", "commands.pardon.success": "%s is entspat wordn", "commands.pardonip.failed": "Es is nix vaändat wordn. De IP is ned gspat", "commands.pardonip.invalid": "Ungütige IP-Adress", "commands.pardonip.success": "IP-Adress %s is entspat wordn", "commands.particle.failed": "Partikl san für niemanden sichtbor", "commands.particle.success": "Partikl %s wead zoagt", "commands.perf.alreadyRunning": "Leistungs-Aufzeichnung is scho gestartet", "commands.perf.notRunning": "Leistungs-Aufzeichnung is ned aktiv", "commands.perf.reportFailed": "Debug‐Bericht hot ned erstöt weadn kina", "commands.perf.reportSaved": "Debug‐Bericht is erstöt wordn in %s", "commands.perf.started": "Stort 10-sek<PERSON><PERSON><PERSON> Leistungs-Aufzeichnung (benutz ‚/debug stop‘ ums vorzeitig zu beenden)", "commands.perf.stopped": "Leistungsaufzeichnung wurde nach %s Sekunde(n) und %s Tick(s) beendet (%s Tick(s) pro Sekunde)", "commands.place.feature.failed": "<PERSON><PERSON><PERSON><PERSON> hat das Place-feature", "commands.place.feature.invalid": "<PERSON>s gibt ka Merkmal vom Typ \"%s\"", "commands.place.feature.success": "\"%s\" bei %s, %s, %s platziert", "commands.place.jigsaw.failed": "Vabund hot ned generiad wean kina", "commands.place.jigsaw.invalid": "Es gib koan Vorlognpool vom Typ „%s“", "commands.place.jigsaw.success": "Verbuand wurde bei %s, %s, %s generiert", "commands.place.structure.failed": "Konstruktion konn ned platziert wean", "commands.place.structure.invalid": "<PERSON>s gibt kan Struktua mit dem typ \"%s\"", "commands.place.structure.success": "Konstruktion „%s“ is bei %s, %s, %s generiad wordn", "commands.place.template.failed": "Vorlog hot ned platziad wean kina", "commands.place.template.invalid": "Es gib koa Vorlog mit da ID „%s“", "commands.place.template.success": "Vorlog „%s“ bei %s, %s, %s glodn", "commands.playsound.failed": "<PERSON> is zu weit weg um ghert zu wean", "commands.playsound.success.multiple": "Geräusch %s is fia %s Spiela obgspüt wordn", "commands.playsound.success.single": "Geräusch %s is fia %s obgspüt wordn", "commands.publish.alreadyPublished": "LAN-Spü is scho unta Port %s erreichbor", "commands.publish.failed": "Es hot koa LAN-S<PERSON>ü erstöt wean kina", "commands.publish.started": "LAN-Spü unta Port %s erreichbor", "commands.publish.success": "LAN-Spü is jetz unta Port %s erreichbor", "commands.random.error.range_too_large": "Spanne darf hechstns 2147483646 Zufallswerte umfossn", "commands.random.error.range_too_small": "Spanne muss mindestns 2 Zufollswerte umfossn", "commands.random.reset.all.success": "Zuafals folge %s wuarde zurückgesetzt(s)", "commands.random.reset.success": "Zufoisfolge ‚%s‘ is zrugggsetzt wordn", "commands.random.roll": "%s hot %s gwürflt (zwischn %s und %s inklusive)", "commands.random.sample.success": "Zufoiswert: %s", "commands.recipe.give.failed": "<PERSON><PERSON> neien Rezepte san gleant wordn", "commands.recipe.give.success.multiple": "%s Rezepte sen fia %s Spiela freigscholtn wordn", "commands.recipe.give.success.single": "%s Rezepte san fia %s freigschoitet wordn", "commands.recipe.take.failed": "Es hobn koane Rezepte entfernt wean kina", "commands.recipe.take.success.multiple": "%s Rezepte san von %s Spiela entfernt wordn", "commands.recipe.take.success.single": "%s Rezepte san vo %s Spiela entfernt wordn", "commands.reload.failure": "Neichlona feh<PERSON>, alte Datn wern beibehoiten", "commands.reload.success": "Datnpakete wean nei glodn!", "commands.ride.already_riding": "%s reitet schua %s", "commands.ride.dismount.success": "%s hot aufgheart %s zu reitn", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON>lor konn nit gereitet werdn", "commands.ride.mount.failure.generic": "%s konnt nit %s reitn", "commands.ride.mount.failure.loop": "<PERSON>nns Objekt nit auf si selbst oder oan von seiner Mitfohrer aufsteign lossn", "commands.ride.mount.failure.wrong_dimension": "Auf Objekt in ondre Dimension konn net aufgstiegn werdn", "commands.ride.mount.success": "%s hot ongfongen %s zu reitn", "commands.ride.not_riding": "%s hot koan onderes Objekt bestiegn", "commands.rotate.success": "%s is gedraht woan", "commands.save.alreadyOff": "<PERSON><PERSON><PERSON><PERSON> is scho inaktiv", "commands.save.alreadyOn": "<PERSON><PERSON><PERSON><PERSON> is scho aktiv", "commands.save.disabled": "Automatischs Speichan is jetz deaktiviat", "commands.save.enabled": "Automatischs Speichan is jetz aktiviat", "commands.save.failed": "Spü hot ned gpeichat wean kina (is gnuag Festplottnspeicha vafügboa?)", "commands.save.saving": "Spü wead gspeichat (des konn an Moment dauan!)", "commands.save.success": "Spü is gspeichat wordn", "commands.schedule.cleared.failure": "<PERSON>s gib koane Planungen mit da ID %s", "commands.schedule.cleared.success": "<PERSON>s wurde(n) %s Planung(en) mit der ID %s entfernt", "commands.schedule.created.function": "Funktion ‚%s‘ wurde in %s Tick(s) zur Spielzeit %s eingeplant", "commands.schedule.created.tag": "Tag '%s' is in %s Ticks zua Spüzeit %s einplant wordn", "commands.schedule.macro": "Mak<PERSON> kennen net geplat weadn", "commands.schedule.same_tick": "Fia den aktuelln Tick konn nix einplant werdn", "commands.scoreboard.objectives.add.duplicate": "A Ziel mit dem Nom existiat scho", "commands.scoreboard.objectives.add.success": "Neis Zü %s is erstöt wordn", "commands.scoreboard.objectives.display.alreadyEmpty": "Es is nix vaändat wordn. De Onzeigeposition is scho laar", "commands.scoreboard.objectives.display.alreadySet": "E<PERSON> is nix vaändat wordn. De Onzeigeposition zoagt scho as <PERSON><PERSON>", "commands.scoreboard.objectives.display.cleared": "Oi Ziele in Onzeigeposition %s san glöscht wordn", "commands.scoreboard.objectives.display.set": "Onzeigeposition %s zoagt etz as Zü %s u", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON> gib koane Z<PERSON>", "commands.scoreboard.objectives.list.success": "Es gibt %s Ziel(a): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Automatische Aktualisierung des Onzeigenomns is fürs Punkte-Ziel '%s' deaktiviert woadn", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Automatische Aktualisierung vom Onzeigenomen is fürs Punkte‐Ziel '%s' aktiviert woadn", "commands.scoreboard.objectives.modify.displayname": "Da Onzeigenom vo %s is auf %s geändert worden", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Standard‐Zahlendoarstellung vom Punkte‐Ziel '%s' is zrückgesetzt woan", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Standard‐Zahlendarstellung vom Punkte‐Ziel '%s' is geändat woan", "commands.scoreboard.objectives.modify.rendertype": "Dorstellungsart vo Ziel %s geändert", "commands.scoreboard.objectives.remove.success": "Zü %s is entfernt wordn", "commands.scoreboard.players.add.success.multiple": "%s is fia %s Objekte um %s erhöht wordn", "commands.scoreboard.players.add.success.single": "%2$s is fia %3$s um %1$s erhöht wordn (jetz %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Onzeigenomen in %s is fia %s Objekte zurückgesetzt woan", "commands.scoreboard.players.display.name.clear.success.single": "Onzeigenomen in %s is fia %s zurückgesetzt woan", "commands.scoreboard.players.display.name.set.success.multiple": "Anzeigename in %3$s wurde für %2$s Objekte zu %1$s geändert", "commands.scoreboard.players.display.name.set.success.single": "Anzeigename in %3$s wurde für %2$s zu %1$s geändert", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Zahlendarstellung in %2$s wurde für %1$s Objekte zurückgesetzt", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Zoindorstellung in %s wird für %s zruckgsetzt", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Zahlendarstellung in %2$s wurde für %1$s Objekte geändert", "commands.scoreboard.players.display.numberFormat.set.success.single": "Zoinendorstellung in %s wird für %s gendert", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON> is nix vaändat wordn. <PERSON> is scho aktiviat", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON> Befehl funktioniat nua fia Auslösa (trigger-Ziele)", "commands.scoreboard.players.enable.success.multiple": "Auslösa %s is fia %s Objekte aktiviat wordn", "commands.scoreboard.players.enable.success.single": "Auslösa %s fi %s aktiviat", "commands.scoreboard.players.get.null": "Wert vo '%s' fia '%s' konn ned obgfrogt wean, weil koana gsetzt is", "commands.scoreboard.players.get.success": "%s hot %s %s", "commands.scoreboard.players.list.empty": "<PERSON>s gib koane übawochtn Objekte", "commands.scoreboard.players.list.entity.empty": "%s hot koane Punktestände", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s Hot %s Punkt(e):", "commands.scoreboard.players.list.success": "Es gibt %s übawachte Entitäten: %s", "commands.scoreboard.players.operation.success.multiple": "%s is fia %s Objekte geändat wordn", "commands.scoreboard.players.operation.success.single": "Setz %s fia %s zu %s", "commands.scoreboard.players.remove.success.multiple": "%2$s is fia %3$s Objekte um %1$s verringat wordn", "commands.scoreboard.players.remove.success.single": "%2$s is fia %3$s um %1$s verringat wordn (jetz %4$s)", "commands.scoreboard.players.reset.all.multiple": "Oi Punktestände fia %s Objekte san glöscht wordn", "commands.scoreboard.players.reset.all.single": "Oi Punktestände fia %s san glöscht wordn", "commands.scoreboard.players.reset.specific.multiple": "%s is fia %s Objekte glöscht wordn", "commands.scoreboard.players.reset.specific.single": "%s is fia %s zruggsetzt wordn", "commands.scoreboard.players.set.success.multiple": "%s is fia %s Objekte auf %s gsetzt wordn", "commands.scoreboard.players.set.success.single": "%s is fia %s auf %s gsetzt wordn", "commands.seed.success": "Startwert: %s", "commands.setblock.failed": "Block hot ned gsetzt wean kina", "commands.setblock.success": "Block bei (%s, %s, %s) is geändat wordn", "commands.setidletimeout.success": "Untätigkeitslimit wurde auf %s Minute(n) gesetzt", "commands.setidletimeout.success.disabled": "Spiela-Inaktivitätslimit is ausgschoitn woan", "commands.setworldspawn.failure.not_overworld": "Da Wölteinstiegspunkt konn nua in da Obawölt gsetzt wean", "commands.setworldspawn.success": "Welt-Einstiegspunkt wurd auf (%s, %s, %s [%s]) gsetzt", "commands.spawnpoint.success.multiple": "Einstiegspunkt von %6$s S<PERSON>lan wurd auf (%1$s, %2$s, %3$s) [%4$s] in %5$s gsetzt", "commands.spawnpoint.success.single": "Einstiegspunkt von %6$s wurd auf (%1$s, %2$s, %3$s) [%4$s] in %5$s gsetzt", "commands.spectate.not_spectator": "%s is ned im Zuschauamodus", "commands.spectate.self": "Du konnst di net selbst beobochtn", "commands.spectate.success.started": "Du schaust etz %s zua", "commands.spectate.success.stopped": "<PERSON> schaust etz koan Objekt mea zua", "commands.spreadplayers.failed.entities": "%s Objekt(e) konnte(n) nicht um (%s, %s) verteilt werden (zu wenig Platz für Objekte – versuche, höchstens %s zu verteilen)", "commands.spreadplayers.failed.invalid.height": "Ungültiga Weat fia maxHeight %s; muas höha sei ols de Wöduntagrenz %s", "commands.spreadplayers.failed.teams": "%s Team(s) konnte(n) nicht um (%s, %s) verteilt werden (zu wenig Platz für Teams – versuche, höchstens %s zu verteilen)", "commands.spreadplayers.success.entities": "%s <PERSON>pieler wurde(n) rund um (%s, %s) mit einem Abstand von durchschnittlich %s <PERSON><PERSON><PERSON><PERSON>n zueinander verteilt", "commands.spreadplayers.success.teams": "%s Team(s) sind rund um %s, %s mit am duachschnittlicha abstand vo %s Blö<PERSON> zuanand vatoalt wora", "commands.stop.stopping": "Server wead gstoppt", "commands.stopsound.success.source.any": "Oi %s-Geräusche san gstoppt wordn", "commands.stopsound.success.source.sound": "Geräusch %s fia de Geräuschart %s is gstoppt wordn", "commands.stopsound.success.sourceless.any": "Oi Geräusche san gstoppt wordn", "commands.stopsound.success.sourceless.sound": "Geräusch %s is gstoppt wordn", "commands.summon.failed": "Objekt hot ned erzeugt wean kina", "commands.summon.failed.uuid": "Objekt hot durch bereits vorhandener ned erzeugt werdn kina", "commands.summon.invalidPosition": "Ungültige Position zum Erschoffn", "commands.summon.success": "%s is erzeugt wordn", "commands.tag.add.failed": "Entweda hot des Objekt scho des Etikett oda es hot z'vü Etikettn", "commands.tag.add.success.multiple": "Tag '%s' is zu %s Objekte hinzuagfüg wordn", "commands.tag.add.success.single": "Tag '%s' is zu %s hinzuagfüg wordn", "commands.tag.list.multiple.empty": "De %s ausgwöhtn Objekte hobn koane Tags", "commands.tag.list.multiple.success": "De %s ausgwöhtn Objekte hobn insgesomt %s Tags: %s", "commands.tag.list.single.empty": "%s hot koane Tags", "commands.tag.list.single.success": "%s hot %s Tags: %s", "commands.tag.remove.failed": "Zü hot des Etikett ned", "commands.tag.remove.success.multiple": "Tag '%s' is vo %s Objektn entfernt wordn", "commands.tag.remove.success.single": "Tag '%s' is von %s entfernt wordn", "commands.team.add.duplicate": "A Team mit dem Nom existiat scho", "commands.team.add.success": "Team %s is erstöt wordn", "commands.team.empty.success": "%s Mitgliad(a) vom Team %s entfeant", "commands.team.empty.unchanged": "Es is nix vaändat wordn. Des Team is scho laar", "commands.team.join.success.multiple": "%s Mitglieda san zum Team %s hinzuagfüg wordn", "commands.team.join.success.single": "%s is zum Team %s hinzuagfüg wordn", "commands.team.leave.success.multiple": "%s Mitglieda san aus oi Teams entfernt wordn", "commands.team.leave.success.single": "%s is aus oi Teams entfernt wordn", "commands.team.list.members.empty": "Team %s hot koane <PERSON>", "commands.team.list.members.success": "Team %s hot %s Mitgliad(a): %s", "commands.team.list.teams.empty": "<PERSON>s gib koane Teams", "commands.team.list.teams.success": "Es gibt %s Team(s): %s", "commands.team.option.collisionRule.success": "Schieberegl fia Team %s is auf \"%s\" gsetzt wordn", "commands.team.option.collisionRule.unchanged": "Es is nix vaändat wordn. De Kollisionsregl lieg scho bei dem Wert", "commands.team.option.color.success": "Forb vom Team %s is auf %s gsetzt wordn", "commands.team.option.color.unchanged": "Es is nix vaändat wordn. Des Team hot scho de Forb", "commands.team.option.deathMessageVisibility.success": "Sichtborkeit dea Todesnochrichtn vom Team %s is auf \"%s\" gsetzt wordn", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON>s is nix vaändat wordn. De Sichtborkeit von de Todesmödungen is scho bei dem Wert", "commands.team.option.friendlyfire.alreadyDisabled": "Es is nix va<PERSON>ndat wordn. Fia des Team is <PERSON><PERSON><PERSON><PERSON><PERSON> scho <PERSON>t", "commands.team.option.friendlyfire.alreadyEnabled": "Es is nix vaändat wordn. Fia des Team is Eignbeschuss scho aktiviat", "commands.team.option.friendlyfire.disabled": "Eignbeschuss is fia Team %s deaktiviat wordn", "commands.team.option.friendlyfire.enabled": "Eignbeschuss is fia Team %s aktiviat wordn", "commands.team.option.name.success": "Nom vom Team %s is aktualisiert wordn", "commands.team.option.name.unchanged": "Es is nix vaändat wordn. Des Team hot scho den Nom", "commands.team.option.nametagVisibility.success": "Sichtborkeit dea Spielanomen vom Team %s is auf \"%s\" gsetzt wordn", "commands.team.option.nametagVisibility.unchanged": "Es is nix vaändat wordn. De Sichtborkeit von de Spielanomen lieg scho bei dem Wert", "commands.team.option.prefix.success": "Teamprefix is jetzt %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Es is nix vaändat wordn. Des Team konn unsichtbore Teammitglieda scho ned sehn", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Es is nix vaändat wordn. Des Team konn unsichtbore Teammitglieda scho <PERSON>hn", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s konn koane unsichtborn Teammitglieda mehr sehn", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s konn jetz unsichtbore Teammitglieda sehn", "commands.team.option.suffix.success": "Teamsuffix is jetzt %s", "commands.team.remove.success": "Team %s is entfernt wordn", "commands.teammsg.failed.noteam": "<PERSON> muast in an Team sei, um dei Team zu benochrichtign", "commands.teleport.invalidPosition": "Ungültige Position zum Teleportian", "commands.teleport.success.entity.multiple": "%s Objekte san zu %s teleportiat wordn", "commands.teleport.success.entity.single": "%s is zu %s teleportiat wordn", "commands.teleport.success.location.multiple": "%s Objekte san zu %s, %s, %s teleportiat wordn", "commands.teleport.success.location.single": "%s is zu %s, %s, %s teleportiat wordn", "commands.test.batch.starting": "Umgebung %s, Reihe %s wead gestartet", "commands.test.clear.error.no_tests": "Es san kane Prüfungen zum leern gfundn woan", "commands.test.clear.success": "%s Struktuan san gleert woan", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON><PERSON>, um in de Zwischnoblog zu kopian", "commands.test.create.success": "Testaufbau fian Test %s wuad gmocht", "commands.test.error.no_test_containing_pos": "Es konn ka Testinstanz gfundn wean de %s, %s, %s enhält", "commands.test.error.no_test_instances": "Kane Testinstanzn gfundn woan", "commands.test.error.non_existant_test": "Test %s hod ned gfundn wean kennen", "commands.test.error.structure_not_found": "Teststruktur %s hod ned gfundn wean kennen", "commands.test.error.test_instance_not_found": "Testinstanz-Blockobjekt hod ned gfundn wean kennen", "commands.test.error.test_instance_not_found.position": "Testinstanz-Blockobjekt hod ned bei am Test bei %s, %s, %s gfundn wean kennen", "commands.test.error.too_large": "De Konstruktion derf in olle Richtungen hechstens %s Bl<PERSON><PERSON> groß sein", "commands.test.locate.done": "<PERSON><PERSON><PERSON>, %s Struktur(n) hom gfundn wean kennen", "commands.test.locate.found": "Strucktur bei %s gfundn (Entfeanung: %s)", "commands.test.locate.started": "Teststruktuan weadn g<PERSON>t, des konn a bissl dauan...", "commands.test.no_tests": "Kane Tests zum Ausfian vorhondn", "commands.test.relative_position": "Position relativ zu %s: %s", "commands.test.reset.error.no_tests": "Es san kane Tests zum Zrucksetzn gfundn woan", "commands.test.reset.success": "%s Konstruktion(en) zruckgsetzt", "commands.test.run.no_tests": "Kane Tests san gfundn woan", "commands.test.run.running": "%s Tests wean ausgfiat...", "commands.test.summary": "Spütest featig! %s tests san ausgfiat woan", "commands.test.summary.all_required_passed": "<PERSON><PERSON> Tests hom gepasst :)", "commands.test.summary.failed": "%s eafoadalichn Tests san fehlschlogn :(", "commands.test.summary.optional_failed": "%s optionale Tests san fehlgschlogn", "commands.tick.query.percentiles": "Perzentile: P50: %s ms, P95: %s ms, P99: %s ms; Stichprob: %s", "commands.tick.query.rate.running": "Soi‐Tickrotn: %s pro Sekund.\nDurchschnittliche Zeit pro Tick: %s ms (Soi: %s ms)", "commands.tick.query.rate.sprinting": "Soi‐Tickrotn: %s pro Sekund (net berücksichtigt, nua zua Referenz).\nDurchschnittliche Zeit pro Tick: %s ms", "commands.tick.rate.success": "Soi‐Tickrotn is auf %s pro Sekund gesetzt wuan", "commands.tick.sprint.report": "Beschleinigung is mit %s Ticks pro Sekund bzw. %s ms pro Tick obgschlossn wuan", "commands.tick.sprint.stop.fail": "Es find daweil koa<PERSON>ick‐Beschleinigung stott", "commands.tick.sprint.stop.success": "Tick‐Beschleinigung is obrochn wuan", "commands.tick.status.frozen": "Es Spü is eingfrohrn", "commands.tick.status.lagging": "<PERSON><PERSON> <PERSON><PERSON><PERSON> laft, es konn oba ned mit da Ziel-Tickrate mithoitn", "commands.tick.status.running": "Es Spü lafft gonz normal", "commands.tick.status.sprinting": "<PERSON><PERSON> is tuat sprintn", "commands.tick.step.fail": "Spü hot net fortgschrittn wan kennen – es muas znaxt eigefruaen wean", "commands.tick.step.stop.fail": "Es find daweil koa Tick‐Beschleinigung stott", "commands.tick.step.stop.success": "Tick‐Beschleinigung is obrochn wuan", "commands.tick.step.success": "Es wead um %s Tick(s) fortgschrittn", "commands.time.query": "De aktuelle Zeit is %s", "commands.time.set": "Zeit auf %s gsetzt", "commands.title.cleared.multiple": "Titl san fia %s Spiela entfernt wordn", "commands.title.cleared.single": "Titl is fia %s entfernt wordn", "commands.title.reset.multiple": "Onzeigezeitn vom Titl san fia %s Spiela zrugggsetzt wordn", "commands.title.reset.single": "Onzeigezeitn vom Titl san fia %s zrugggsetzt wordn", "commands.title.show.actionbar.multiple": "Neia Aktionsleistntitl wead fia %s Spiela uzoagt", "commands.title.show.actionbar.single": "Neia Aktionsleistntitl wead fia %s uzoagt", "commands.title.show.subtitle.multiple": "Neia Untatitl wead fia %s Spiela uzoagt", "commands.title.show.subtitle.single": "Neia Untatitl wead fia %s uzoagt", "commands.title.show.title.multiple": "Neia Titl wead fia %s S<PERSON>la u<PERSON>t", "commands.title.show.title.single": "Neia Titl wead fia %s uzoagt", "commands.title.times.multiple": "Onzeigezeitn vom Titl san fia %s Spiela geändat wordn", "commands.title.times.single": "Onzeigezeitn vom Titl san fia %s geändat wordn", "commands.transfer.error.no_players": "Es muas mindestns a Spila zum Übatrogn ongegebn wean", "commands.transfer.success.multiple": "%s <PERSON><PERSON>la weadn on %s:%s übatrogn", "commands.transfer.success.single": "Übertrog %s zu %s:%s", "commands.trigger.add.success": "%s is ausglöst wordn (Wert is um %s erhöht wordn)", "commands.trigger.failed.invalid": "Du konnst nua Punktestond-Ziele vom Typ 'trigger' auslösn", "commands.trigger.failed.unprimed": "Du konnst des Punktestond-Zü no ned auslösn", "commands.trigger.set.success": "%s is ausglöst wordn (Wert is auf %s gsetzt wordn)", "commands.trigger.simple.success": "%s is ausglöst wordn", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server Versions Info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stabil = na", "commands.version.stable.yes": "stabil = jo", "commands.waypoint.list.empty": "<PERSON> in %s", "commands.waypoint.list.success": "%s Wegpunkt(e) in %s: %s", "commands.waypoint.modify.color": "Wegpunktfoab is jetz %s", "commands.waypoint.modify.color.reset": "Wegpunktfoab zrucksetztn", "commands.waypoint.modify.style": "Wegpunkt Stial gändert", "commands.weather.set.clear": "Wetta is auf Kloa geändat wordn", "commands.weather.set.rain": "Wetta is auf Regn geändat wordn", "commands.weather.set.thunder": "<PERSON><PERSON> is auf Gewitter geändat wordn", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON> is scho auf da Gästelistn", "commands.whitelist.add.success": "%s is zua Gästelistn hinzuagfüg wordn", "commands.whitelist.alreadyOff": "<PERSON> Gästelistn is scho <PERSON>ak<PERSON>t", "commands.whitelist.alreadyOn": "De Gästelistn is scho aktiviat", "commands.whitelist.disabled": "De Whitelist is hetz aus", "commands.whitelist.enabled": "De Whitelist is hetz ein", "commands.whitelist.list": "Es sind %s Spiela uf da Gästelischt: %s", "commands.whitelist.none": "<PERSON><PERSON> gib koane <PERSON>la auf da Gästelistn", "commands.whitelist.reloaded": "<PERSON> Gästelistn is nei glodn <PERSON>n", "commands.whitelist.remove.failed": "<PERSON><PERSON><PERSON> is ned auf da Gästelistn", "commands.whitelist.remove.success": "%s is von da Gästelistn entfernt wordn", "commands.worldborder.center.failed": "<PERSON>s is nix vaändat wordn. <PERSON>grenzung is scho do zentriat", "commands.worldborder.center.success": "<PERSON> <PERSON><PERSON> von da Wödbarriere is auf (%s, %s) gsetzt wordn", "commands.worldborder.damage.amount.failed": "Es is nix vaändat wordn. Da Wödbegrenzungsschodn hot scho den Wert", "commands.worldborder.damage.amount.success": "Setz en Wödbegrenzungsschoden auf %s pro Block jede Sekundn", "commands.worldborder.damage.buffer.failed": "Es is nix vaändat wordn. Da Unversehrheitsbereich um de Wödbarriere hot scho den Wert", "commands.worldborder.damage.buffer.success": "Da Unversehrtheitsbereich um d Wealtbarriere isch uf %s Blöck(e) gsezt wora", "commands.worldborder.get": "Die Wealtbarriere isch grad %s blöck(e) wit", "commands.worldborder.set.failed.big": "Da Durchmessa von da Wödbegrenzung deaf ned greßa ois %s <PERSON><PERSON><PERSON> sei", "commands.worldborder.set.failed.far": "De Wöldgrenz ka ned weita ols %s weg sei", "commands.worldborder.set.failed.nochange": "Es is nix vaändat wordn. <PERSON> Durchmessa von da Wödbegrenzung hot scho den Wert", "commands.worldborder.set.failed.small": "Da Durchmessa der Weltbegrenzung teaf ned kloana als 1 Block sein", "commands.worldborder.set.grow": "De Wödbarriere wead innahoib vo %2$s Sekundn auf a Weitn vo %1$s Bleck vagreßat", "commands.worldborder.set.immediate": "Die Weltbarriere wurde auf eine Weite von %s Block/Blöcken gesetzt", "commands.worldborder.set.shrink": "D Wealtbarriere wüad innahalb vo %s Sekund(a) uf a Weite vo %s Blöck(e) vakloanat", "commands.worldborder.warning.distance.failed": "Es is nix vaändat wordn. Da Warnbereich um de Wödbegrenzung hot scho den Wert", "commands.worldborder.warning.distance.success": "Da Warnberaich vo da Wealtbarriere isch uf %s Blöck(e) gsetzt wora", "commands.worldborder.warning.time.failed": "Es is nix vaändat wordn. De Warnzeit von da Weltbegrenzung hot scho den Wert", "commands.worldborder.warning.time.success": "D Warnzit vo da Wealtbarriere isch uf %s Sekund(n) gsetzt wora", "compliance.playtime.greaterThan24Hours": "<PERSON> spüst scho seit üba 24 stund", "compliance.playtime.hours": "Du spüst scho %s Stund(n)", "compliance.playtime.message": "Übamäsigs spün kon dein oitag beeinträchtign", "connect.aborted": "Obbrochn", "connect.authorizing": "Umödn ...", "connect.connecting": "Vabind zum Server ...", "connect.encrypting": "Vaschlüssln ...", "connect.failed": "Vabindungsaufbau fehlgschlogn", "connect.failed.transfer": "Verbindung bei Übertrogung an den Server is föhgschlogen", "connect.joining": "Wöd wead betretn ...", "connect.negotiating": "Aushondln ...", "connect.reconfiging": "<PERSON><PERSON> kon<PERSON> ...", "connect.reconfiguring": "<PERSON><PERSON> kon<PERSON> ...", "connect.transferring": "Übertrog an neuen Serva…", "container.barrel": "<PERSON><PERSON>", "container.beacon": "Leichtfeia", "container.beehive.bees": "Bienen: %s/%s", "container.beehive.honey": "Honig: %s/%s", "container.blast_furnace": "Schmözofn", "container.brewing": "<PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "container.chest": "Kistn", "container.chestDouble": "Große Kistn", "container.crafter": "Crafter", "container.crafting": "Hondwerk", "container.creative": "<PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "We<PERSON><PERSON>", "container.dropper": "Spenda", "container.enchant": "Vazauban", "container.enchant.clue": "%s, ...?", "container.enchant.lapis.many": "%s Lapislazuli", "container.enchant.lapis.one": "1 Lapislazuli", "container.enchant.level.many": "%s Erfohrungsstufn", "container.enchant.level.one": "1 Erfohrungsstufn", "container.enchant.level.requirement": "Benötigte Erfohrungsstufn: %s", "container.enderchest": "Enderkistn", "container.furnace": "Ofn", "container.grindstone_title": "Rep<PERSON> und entzauban", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Inventar", "container.isLocked": "%s is gspat!", "container.lectern": "<PERSON><PERSON><PERSON>", "container.loom": "Webstuhl", "container.repair": "<PERSON><PERSON><PERSON>", "container.repair.cost": "Erfohrungskostn: %1$s", "container.repair.expensive": "Zu teia!", "container.shulkerBox": "Shulkerkistn", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "und %s ondare...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Räuchaofn", "container.spectatorCantOpen": "Ku ned aufgmocht wean. Inhoit is nu ned generiat wordn.", "container.stonecutter": "Stoasog", "container.upgrade": "Ausrüstung vabessan", "container.upgrade.error_tooltip": "Gegnstond konn so ned aufgwertet wean", "container.upgrade.missing_template_tooltip": "Schmiedevorlog hinzuafügn", "controls.keybinds": "Tastnbelegung ...", "controls.keybinds.duplicateKeybinds": "De Tostn wird a verwendat fia:\n%s", "controls.keybinds.title": "Tastnbelegung", "controls.reset": "Standard", "controls.resetAll": "Tastn zruggsetzn", "controls.title": "Steiarung", "createWorld.customize.buffet.biome": "Bitte wöh a Biom", "createWorld.customize.buffet.title": "Eistellungen fia de Buffet-Wöd", "createWorld.customize.flat.height": "<PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bodn - %s", "createWorld.customize.flat.layer.top": "Obaflächn - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> entfernen", "createWorld.customize.flat.tile": "Schichtmaterial", "createWorld.customize.flat.title": "Flochlondeistellungen", "createWorld.customize.presets": "Vorlogn", "createWorld.customize.presets.list": "Alternativ kust a a boa von uns nemma!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON> heanemma", "createWorld.customize.presets.share": "Wüst du dei Vorlog ebban weidagem? <PERSON><PERSON> as <PERSON><PERSON><PERSON>fö<PERSON> untn!", "createWorld.customize.presets.title": "Vorlog auswöhn", "createWorld.preparing": "Weltastellung werd voabreitet …", "createWorld.tab.game.title": "Spü", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Nomensnennung", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "Lizenzn", "credits_and_attribution.screen.title": "Credits und Nomensnennung", "dataPack.bundle.description": "Aktiviat experimentelln Sa<PERSON>l-Gegnstond", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Zagt im Meahspielamodus de Richtung ondara Spiela on", "dataPack.locator_bar.name": "Oatungsleistn", "dataPack.minecart_improvements.description": "Vabessate Bewegungen <PERSON>", "dataPack.minecart_improvements.name": "Wagon <PERSON>", "dataPack.redstone_experiments.description": "Experimentelle Redstone Ändarungen", "dataPack.redstone_experiments.name": "Redstone Experimente", "dataPack.title": "Datenpakete auswöhln", "dataPack.trade_rebalance.description": "Aktualiserte Angeboate for Doarfbewohner", "dataPack.trade_rebalance.name": "Doarfbewohner Handeln Neubalancieren", "dataPack.update_1_20.description": "Neiche Funktionen und Inholte fia Minecract 1.20", "dataPack.update_1_20.name": "Aktualisierung 1.20", "dataPack.update_1_21.description": "Neue Fuanktionen und Inhaulte für Minecraft 1.21", "dataPack.update_1_21.name": "Aktualisierung 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON> geh", "dataPack.validation.failed": "Übaprüfung dea Datnpakete föhlgschlogen!", "dataPack.validation.reset": "Zrugsetzn", "dataPack.validation.working": "Ausgwöhte Datenpakete werdn übaprüft ...", "dataPack.vanilla.description": "De Standarddatn fia Minecraft", "dataPack.vanilla.name": "Standard", "dataPack.winter_drop.description": "Neiche Sochn im Winter-Drop", "dataPack.winter_drop.name": "Winter-Drop", "datapackFailure.safeMode": "Obgsich<PERSON>", "datapackFailure.safeMode.failed.description": "Diese Welt enthält ungültige oder beschädigte Speicherdaten.", "datapackFailure.safeMode.failed.title": "Laden der Welt im abgesicherten Modus fehlgeschlagen", "datapackFailure.title": "<PERSON><PERSON><PERSON><PERSON> in da derzitig usgwälta Datapacket honds Lada vo da Wealtvahindat.\nDu kasch entweda probiera, se nua mit da Vanilla-Datapacket zum Lada (\"abgsichata Modus\"), oda zum Houptmenü zruckgoh und s manuell behebn.", "death.attack.anvil": "%1$s is von an Amboss zaquetscht wordn", "death.attack.anvil.player": "%1$s is während an Kompf mit %2$s von an foiendn Amboss zaquetscht wordn", "death.attack.arrow": "%1$s is von %2$s daschossn wordn", "death.attack.arrow.item": "%1$s is von %2$s mit %3$s daschossn wordn", "death.attack.badRespawnPoint.link": "beobsichtigtn Spüdesign", "death.attack.badRespawnPoint.message": "%1$s is von %2$s umbrocht wordn", "death.attack.cactus": "%1$s is totgstochn wordn", "death.attack.cactus.player": "%1$s is be<PERSON> %2$s zu entkemma in an Kaktus grennt", "death.attack.cramming": "%1$s is zaquetscht wordn", "death.attack.cramming.player": "%1$s is von %2$s zaquetscht wordn", "death.attack.dragonBreath": "%1$s wurde in Drachenatem geröstet", "death.attack.dragonBreath.player": "%1$s wurde durch %2$s in Drachenatem geröstet", "death.attack.drown": "%1$s is ertrunkn", "death.attack.drown.player": "%1$s is be<PERSON>, %2$s zu entkemma, ertrunkn", "death.attack.dryout": "%1$sis an Wossermanhel gstorbn", "death.attack.dryout.player": "%1$s starb bei ein'm Fluchtversuach an Dehydration %2$s", "death.attack.even_more_magic": "%1$s is durch vastärkte Magie umbrocht wordn", "death.attack.explosion": "%1$s is in de Luft gspreng wordn", "death.attack.explosion.player": "%1$s is von %2$s in de Luft gspreng wordn", "death.attack.explosion.player.item": "%1$s is von %2$s mit %3$s in de Luft gspreng wordn", "death.attack.fall": "%1$s is da Schwerkroft zum Opfa gfoin", "death.attack.fall.player": "%1$s is be<PERSON>, %2$s zu entkemma, da Schwerkroft zum Opfa gfoin", "death.attack.fallingBlock": "%1$s is von an Block zaquetscht wordn", "death.attack.fallingBlock.player": "%1$s is während an Kompf mit %2$s von an foiendn Block zaqeutscht wordn", "death.attack.fallingStalactite": "%1$s wurd vo an fallendn Stlaktiten aufgspießt", "death.attack.fallingStalactite.player": "%1$s wurd während dem Kampf gegn %2$s vo an fallendn Stalaktitn aufgspießt", "death.attack.fireball": "%1$s is von %2$s flambiat wordn", "death.attack.fireball.item": "%1$s is von %2$s mit %3$s flambiat wordn", "death.attack.fireworks": "%1$s is mit an Knoi in de Luft gonga", "death.attack.fireworks.item": "%1$s flog von an Feiawerk, des vo %2$s mit %3$s abgschossen wordn is, mit an Knall in die Luft", "death.attack.fireworks.player": "%1$s is während an Kompf mit %2$s mit an Knoll in die Luft gongen", "death.attack.flyIntoWall": "%1$s hot kinetische Energie erfohrn", "death.attack.flyIntoWall.player": "%1$s hot be<PERSON>, %2$s zu entkemma, kinetische Energie erfohren", "death.attack.freeze": "%1$s is afroan", "death.attack.freeze.player": "%1$s is durch %2$s zu Tode afroan", "death.attack.generic": "%1$s is gstorbn", "death.attack.generic.player": "%1$s is wegn %2$s gstorbn", "death.attack.genericKill": "%1$s isch tötet wora", "death.attack.genericKill.player": "%1$s isch wärnd am kampf mit %2$s gschtorbn", "death.attack.hotFloor": "%1$s hot rausgfundn dass da Bodn Lava is", "death.attack.hotFloor.player": "%1$s geriet wegen %2$s in den Gefahrenbereich", "death.attack.inFire": "%1$s is in Flommn aufgonga", "death.attack.inFire.player": "%1$s is ins <PERSON><PERSON> g<PERSON>, während er mit %2$s kämpft hot", "death.attack.inWall": "%1$s is lebendig begrobn wordn", "death.attack.inWall.player": "%1$s is während an Kompf mit %2$s lebendig begrobn wordn", "death.attack.indirectMagic": "%1$s is von %2$s mit Magie umbrocht wordn", "death.attack.indirectMagic.item": "%1$s is von %2$s mit %3$s umbrocht wordn", "death.attack.lava": "%1$s hot vasuacht in <PERSON><PERSON> zu schwimma", "death.attack.lava.player": "%1$s is be<PERSON>, %2$s zu entkemma, in Lava gfoin", "death.attack.lightningBolt": "%1$s hot da Blitz troffn", "death.attack.lightningBolt.player": "%1$s hot während an Kompf mit %2$s da Blitz troffn", "death.attack.mace_smash": "%1$s is von %2$s zaschmettat woan", "death.attack.mace_smash.item": "%1$s is von %2$s mit %3$s zaschmettat woan", "death.attack.magic": "%1$s is durch Magie gstorbn", "death.attack.magic.player": "%1$s wurde be<PERSON>, %2$s zu entkommen, durch Magie getötet", "death.attack.message_too_long": "Le<PERSON> is de Nochricht zlong ums Onzumzoagn, tschuldige. Do is de kurze Version: %s", "death.attack.mob": "%1$s is von %2$s umbrocht wordn", "death.attack.mob.item": "%1$s is von %2$s mit %3$s umbrocht wordn", "death.attack.onFire": "%1$s is vabrennt", "death.attack.onFire.item": "%1$s is während an Kompf mit %2$s durch sei %3$s gröstet wordn", "death.attack.onFire.player": "%1$s is während an Kompf mit %2$s gröstet woadn", "death.attack.outOfWorld": "%1$s is aus da Wöd gfoin", "death.attack.outOfWorld.player": "%1$s woit ned in da söben Wöd wia %2$s lebn", "death.attack.outsideBorder": "%1$s hot d Grenzn vo da Wealt valo", "death.attack.outsideBorder.player": "%1$s hot d Grenzn vo da Wealt wärnd am kampf mit %2$s valo", "death.attack.player": "%1$s ist von %2$s getötet woadn", "death.attack.player.item": "%1$s is von %2$s umgebrocht, indem ea %3$s benutzt hot", "death.attack.sonic_boom": "%1$s wurd durch a schollglodenes Schrein ausglöscht", "death.attack.sonic_boom.item": "%1$s is be<PERSON> %2$s mit %3$s zu entkemma durch a schoiglodns Kreischn niedabügelt wordn", "death.attack.sonic_boom.player": "%1$s is be<PERSON>, %2$s zu entkemma, durch a schollglodenes Kreischn ausglöscht wordn", "death.attack.stalagmite": "%1$s is von an Stalagmitn aufgspießt wordn", "death.attack.stalagmite.player": "%1$s is während an Kompf mit %2$s von an Stalagmitn aufspießt wordn", "death.attack.starve": "%1$s is vahungat", "death.attack.starve.player": "%1$s is während an Kompf mit %2$s vahungat", "death.attack.sting": "%1$s is totgstochn wordn", "death.attack.sting.item": "%1$s is vo %2$s mit %3$s zommgstochn wordn", "death.attack.sting.player": "%1$s is vo %2$s totgstochn wordn", "death.attack.sweetBerryBush": "%1$s is durch an Süßbeernstrauch zu Tode gstochn wordn", "death.attack.sweetBerryBush.player": "%1$s is be<PERSON>, %2$s zu entkemma, durch an Süßbeernstrauch zu Tode gstochn wordn", "death.attack.thorns": "%1$s is be<PERSON>, %2$s <PERSON><PERSON><PERSON>, g<PERSON>rbn", "death.attack.thorns.item": "%1$s is be<PERSON> %2$s zu valetzn von %3$s umgebrocht wordn", "death.attack.thrown": "%1$s is von %2$s totprüglt wordn", "death.attack.thrown.item": "%1$s is von %2$s mit %3$s totprüglt wordn", "death.attack.trident": "%1$s is von %2$s aufgspießt wordn", "death.attack.trident.item": "%1$s is von %2$s mit %3$s aufgspießt wordn", "death.attack.wither": "%1$s is vadörrt", "death.attack.wither.player": "%1$s is während an Kompf mit %2$s vadörrt", "death.attack.witherSkull": "%1$s is vo %2$s mit an Schädl obgschossen woan", "death.attack.witherSkull.item": "%1$s is vo %2$s mit %3$s mit an Schädl daschossn wordn", "death.fell.accident.generic": "%1$s is aus zu großa Hech gfoin", "death.fell.accident.ladder": "%1$s is von ana Leita gfoin", "death.fell.accident.other_climbable": "%1$s stürzte beim <PERSON> ab", "death.fell.accident.scaffolding": "%1$s is von an Grüst gfolln", "death.fell.accident.twisting_vines": "%1$s is von ana Zwirblliane gfoin", "death.fell.accident.vines": "%1$s is von ana Liane gfoin", "death.fell.accident.weeping_vines": "%1$s is von ana Trauerliane gfoin", "death.fell.assist": "%1$s is von %2$s zum Foin vadommt wordn", "death.fell.assist.item": "%1$s is von %2$s mit %3$s zum Foin vadommt wordn", "death.fell.finish": "%1$s is zu tiaf gfoin und von %2$s umbrocht wordn", "death.fell.finish.item": "%1$s is zu tiaf gfoin und von %2$s mit %3$s umbrocht wordn", "death.fell.killer": "%1$s is zum Foin vadommt wordn", "deathScreen.quit.confirm": "<PERSON><PERSON> da sicha, dass du as Spü valossn wüst?", "deathScreen.respawn": "Wiedabelebn", "deathScreen.score": "Punktestond", "deathScreen.score.value": "Punktestond: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON>", "deathScreen.title": "Du bist gstorbn!", "deathScreen.title.hardcore": "Game over!", "deathScreen.titleScreen": "Titl Büdschiam", "debug.advanced_tooltips.help": "F3 + H = <PERSON>rweitate Schnöinfos", "debug.advanced_tooltips.off": "Erweitate Schnöinfos: unsichtbor", "debug.advanced_tooltips.on": "Erweitate Schnöinfos: sichtbor", "debug.chunk_boundaries.help": "F3 + G = Chunkgrenzn uzoagn", "debug.chunk_boundaries.off": "Chunkgrenzn: unsichtbor", "debug.chunk_boundaries.on": "Chunkgrenzn: sichtbor", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON> laarn", "debug.copy_location.help": "F3 + C = Position ois /tp-<PERSON><PERSON><PERSON>, F3 + <PERSON> druckt hoitn, um an Obsturz zu erzwingen", "debug.copy_location.message": "Koordinatn in de Zwischnoblog kopiat", "debug.crash.message": "F3 + C wenn druckt ghoidn. Des wird dei Spü zum Absturz bringa ausa du lost as los.", "debug.crash.warning": "Obsturz in %s ...", "debug.creative_spectator.error": "Koa Berechtigung zum ändra vom Spielmodus", "debug.creative_spectator.help": "F3 + N = Zwischa vorigm Spielmodus und Zuaschouamodus wechsla", "debug.dump_dynamic_textures": "Dynamische Texturen in %s gespeichert", "debug.dump_dynamic_textures.help": "F3 + S = Dynamische Texturen ausgeben", "debug.gamemodes.error": "Spielmodusauswohi konn wenga fehlender Berechtigung ned geöffnet werden", "debug.gamemodes.help": "F3 + F4 = Spielmodusaus<PERSON>hi öffnen", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s negster", "debug.help.help": "F3 + Q = <PERSON> <PERSON> u<PERSON>n", "debug.help.message": "Tastnbelegung:", "debug.inspect.client.block": "Clientseitige Blockdatn in Zwischnoblog kopiat", "debug.inspect.client.entity": "Clientseitige Entitydatn in Zwischnoblog kopiat", "debug.inspect.help": "F3 + I = Block- oda Objektdatn in Zwischnoblog kopian", "debug.inspect.server.block": "Serverseitige Blockdatn in Zwischnoblog kopiat", "debug.inspect.server.entity": "Serverseitige Entitydatn in Zwischnoblog kopiat", "debug.pause.help": "F3 + Esc = <PERSON><PERSON>-<PERSON><PERSON> pausian (falls pausian möglich ist)", "debug.pause_focus.help": "F3 + P = Pausian bei Fokusvalust", "debug.pause_focus.off": "Pausian bei Fokusvalust: deaktiviat", "debug.pause_focus.on": "Pausian bei Fokusvalust: aktiviat", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Profüerstöhung startn/beendn", "debug.profiling.start": "Profil-Aufzeichnung fia %s Sekundn gstartet. Benutze F3 + L, um vurzeitig zua beendn", "debug.profiling.stop": "Profüerstöhung beendet. Ergebnisse sand in %s gspeichat", "debug.reload_chunks.help": "F3 + A = <PERSON>ks nei lodn", "debug.reload_chunks.message": "<PERSON><PERSON> wean nei glodn", "debug.reload_resourcepacks.help": "F3 + T = Ressourcenpakete nei lodn", "debug.reload_resourcepacks.message": "Ressourcenpakete nei glodn", "debug.show_hitboxes.help": "F3 + B = Hitboxn uzoagn", "debug.show_hitboxes.off": "Hitboxn: unsichtbor", "debug.show_hitboxes.on": "Hitboxn: sichtbor", "debug.version.header": "Clientseitige Versionsinformationen:", "debug.version.help": "F3 + V = Client Versions Info", "demo.day.1": "De Demo dauat fün<PERSON>ü<PERSON>og, gib dei Bests!", "demo.day.2": "Tog 2", "demo.day.3": "Tog 3", "demo.day.4": "Tog 4", "demo.day.5": "Fünfta und letzta Tog!", "demo.day.6": "Dei fünfta Spültog is um, druck %s, um an Screenshot von deim Werk zu speichan.", "demo.day.warning": "<PERSON><PERSON> is scho fost um!", "demo.demoExpired": "De Demo-Zeit is obgloffn!", "demo.help.buy": "Jetz kaffn!", "demo.help.fullWrapped": "De Demo dauat 5 Spültog (ca. 1 Stund und 40 Minutn in Echtzeit). <PERSON><PERSON><PERSON> <PERSON> Fortschritte fia an Hinweise on! Vül Spaß!", "demo.help.inventory": "Mit %1$s mochst du dei Inventar auf", "demo.help.jump": "Druck %1$s, um zu springa", "demo.help.later": "Weidaspün!", "demo.help.movement": "Benutz %1$s, %2$s, %3$s, %4$s und de Maus, um di zu bewegn", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> di mit da Maus um", "demo.help.movementShort": "Beweg di mit %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft Demo-Modus", "demo.remainingTime": "Vableibnde Zeit: %s", "demo.reminder": "<PERSON><PERSON> is ob<PERSON><PERSON><PERSON><PERSON>, kaf da des <PERSON>pül oda erstöll da a neie Wöd!", "difficulty.lock.question": "<PERSON><PERSON> <PERSON> sicha, dass du de Schwierigkeit von dea Wöd <PERSON> wüst? Des wead de Wöd fia imma auf %1$s setzn und du weast de Eistellung nimma ändan kina.", "difficulty.lock.title": "Schwierigkeit dea <PERSON><PERSON>", "disconnect.endOfStream": "<PERSON><PERSON> dea <PERSON>rogung", "disconnect.exceeded_packet_rate": "Wegn Übaschreitung des Paketratnlimits ausagworfn", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Statusabfrog ignoriera", "disconnect.loginFailedInfo": "<PERSON><PERSON><PERSON> be<PERSON>: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Der Mehrspielermodus ist deaktiviert, überprüfe die Einstellungen deines Microsoft-Kontos.", "disconnect.loginFailedInfo.invalidSession": "Ungültige Sitzung (Vasuach as <PERSON><PERSON><PERSON> und an Launcher nei zu <PERSON>n)", "disconnect.loginFailedInfo.serversUnavailable": "De Authentifizierungsserver san aktuell ned erreichbor. Bitte vasuachs nuamoi.", "disconnect.loginFailedInfo.userBanned": "Sie wurden Gebannt vom Online-Spiel Sie können nur offline spielen", "disconnect.lost": "Vabindung untabrochn", "disconnect.packetError": "Fehla im Netzwerkprotokoll", "disconnect.spam": "Ausigschmissn wegn Spamming", "disconnect.timeout": "Zeitübaschreitung", "disconnect.transfer": "On an Onderen Server übertrogen", "disconnect.unknownHost": "Den Serva gibts ned", "download.pack.failed": "%s vom %s Paket(en) konnt(n) net runtergloden werden", "download.pack.progress.bytes": "Fortschritt: %s (Größe net bekannt)", "download.pack.progress.percent": "Fortschritt: %s %%", "download.pack.title": "Ressourcenpaket %s/%s wird heruntergelodn", "editGamerule.default": "Standard: %s", "editGamerule.title": "Spielregeln bearbeiten", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorption", "effect.minecraft.bad_omen": "Schlechts Omen", "effect.minecraft.blindness": "Blindheit", "effect.minecraft.conduit_power": "Me<PERSON>skroft", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Delfingunst", "effect.minecraft.fire_resistance": "Feiaresistenz", "effect.minecraft.glowing": "Leichtn", "effect.minecraft.haste": "Stress", "effect.minecraft.health_boost": "Gsundheits-Boost", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON> vom Dorf", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Befolln", "effect.minecraft.instant_damage": "Sofortschodn", "effect.minecraft.instant_health": "Sofortheilung", "effect.minecraft.invisibility": "Unsichtborkeit", "effect.minecraft.jump_boost": "Sprungkroft", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Glück", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Üblkeit", "effect.minecraft.night_vision": "Nochtsicht", "effect.minecraft.oozing": "Schleimen", "effect.minecraft.poison": "Vagiftung", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Regeneration", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Sättigung", "effect.minecraft.slow_falling": "Sonfta Foi", "effect.minecraft.slowness": "Longsomkeit", "effect.minecraft.speed": "Gschwindigkeit", "effect.minecraft.strength": "Stärke", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "Pech", "effect.minecraft.water_breathing": "Untawossaatmung", "effect.minecraft.weakness": "Schwäche", "effect.minecraft.weaving": "<PERSON>n", "effect.minecraft.wind_charged": "Windglodn", "effect.minecraft.wither": "Ausdörrung", "effect.none": "Wirkungslos", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Wossaaffinität", "enchantment.minecraft.bane_of_arthropods": "Nemesis der Gliederfüßer", "enchantment.minecraft.binding_curse": "<PERSON>luach der Bindung", "enchantment.minecraft.blast_protection": "Explosionsschutz", "enchantment.minecraft.breach": "Duachb<PERSON><PERSON>", "enchantment.minecraft.channeling": "Entlodung", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Effizienz", "enchantment.minecraft.feather_falling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fortune": "Glück", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Unendlichkeit", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Plündarung", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Glück des Meeres", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Reparatur", "enchantment.minecraft.multishot": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Stärke", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Schlog", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Sog", "enchantment.minecraft.sharpness": "Schärfe", "enchantment.minecraft.silk_touch": "Behutsomkeit", "enchantment.minecraft.smite": "Bonn", "enchantment.minecraft.soul_speed": "Seeln Gschwindigkeit", "enchantment.minecraft.sweeping": "Sc<PERSON>wungkroft", "enchantment.minecraft.sweeping_edge": "Sc<PERSON>wungkroft", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Hoitborkeit", "enchantment.minecraft.vanishing_curse": "Fluach des Vaschwindns", "enchantment.minecraft.wind_burst": "Windb<PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akazienhoizboot", "entity.minecraft.acacia_chest_boat": "Akazienhoizboot mit Kistn", "entity.minecraft.allay": "Hüfsgeist", "entity.minecraft.area_effect_cloud": "Paritklwoikn", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "Rüstungsstända", "entity.minecraft.arrow": "Pfeil", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambusfloß mit Kistn", "entity.minecraft.bamboo_raft": "Bambusfloß", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Biene", "entity.minecraft.birch_boat": "Birknhoizboot", "entity.minecraft.birch_chest_boat": "Birknhoizboot mit Kistn", "entity.minecraft.blaze": "Lohe", "entity.minecraft.block_display": "Blockdorstolla", "entity.minecraft.boat": "Boot", "entity.minecraft.bogged": "Sumpfskelett", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Windkugl", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Höhlnspinn", "entity.minecraft.cherry_boat": "Kirschnhoizboot", "entity.minecraft.cherry_chest_boat": "Kirschnhoizboot mit Kistn", "entity.minecraft.chest_boat": "Boot mit Kistn", "entity.minecraft.chest_minecart": "Wagon mit Kistn", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Wagon mit Commandblock", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Knoaza", "entity.minecraft.creaking_transient": "Knoaza", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Schworzoachnhoizboot", "entity.minecraft.dark_oak_chest_boat": "Schworzoachnhoizboot mit Kistn", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "Esl", "entity.minecraft.dragon_fireball": "Drochn-Feiakugl", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Gworfns Ei", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "<PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Magier", "entity.minecraft.evoker_fangs": "Fongzehn", "entity.minecraft.experience_bottle": "Gworfne Erfohrungsflosch", "entity.minecraft.experience_orb": "Erfohrungskugl", "entity.minecraft.eye_of_ender": "Endaauge", "entity.minecraft.falling_block": "Foienda Block", "entity.minecraft.falling_block_type": "Follenda %s", "entity.minecraft.fireball": "Feiakugl", "entity.minecraft.firework_rocket": "Feiaweak Racketn", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.fox": "Fux", "entity.minecraft.frog": "<PERSON><PERSON><PERSON>", "entity.minecraft.furnace_minecart": "<PERSON>agon mit Ofn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Leichtenda Item <PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Wagon mit Trichta", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "Wüstnzombie", "entity.minecraft.illusioner": "Illusionist", "entity.minecraft.interaction": "Interaktionsobjekt", "entity.minecraft.iron_golem": "Eisngolem", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Gegnstondsdorstolla", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Tropnhoizboot", "entity.minecraft.jungle_chest_boat": "Tropnhoizboot mit Kistn", "entity.minecraft.killer_bunny": "<PERSON>-Hos", "entity.minecraft.leash_knot": "Leinenknotn", "entity.minecraft.lightning_bolt": "Blitz", "entity.minecraft.lingering_potion": "Vaweiltronk", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Lamasp<PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmawürfl", "entity.minecraft.mangrove_boat": "Mangrovnhoizboot", "entity.minecraft.mangrove_chest_boat": "Mangrovnhoizboot mit Kistn", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Wagon", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Oachnhoizboot", "entity.minecraft.oak_chest_boat": "Oachnhoizboot mit Kistn", "entity.minecraft.ocelot": "Ozelot", "entity.minecraft.ominous_item_spawner": "Vadächtiga Item Spawner", "entity.minecraft.painting": "G<PERSON>ä<PERSON>", "entity.minecraft.pale_oak_boat": "Blosseichnhoizboot", "entity.minecraft.pale_oak_chest_boat": "Blosseichnhoiz mit Kistn", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Popagei", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin-Barbar", "entity.minecraft.pillager": "Plündara", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "Tronk", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Lachs", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Shulkergschoss", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Skelett", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Schleim", "entity.minecraft.small_fireball": "<PERSON><PERSON><PERSON>", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Schneegolem", "entity.minecraft.snowball": "Schneeboi", "entity.minecraft.spawner_minecart": "Wagon mit Spawner", "entity.minecraft.spectral_arrow": "Spektralpfeil", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Wurftronk", "entity.minecraft.spruce_boat": "Fichtnhoizboot", "entity.minecraft.spruce_chest_boat": "Fichtnhoizboot mit Kistn", "entity.minecraft.squid": "Tintnfisch", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Textdarstella", "entity.minecraft.tnt": "Gezündets TNT", "entity.minecraft.tnt_minecart": "Wagon mit TNT", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish": "Tropnfisch", "entity.minecraft.tropical_fish.predefined.0": "Anemonenfisch", "entity.minecraft.tropical_fish.predefined.1": "Schworza Seglflossndokta", "entity.minecraft.tropical_fish.predefined.10": "Halfterfisch", "entity.minecraft.tropical_fish.predefined.11": "Orangnstreifn-Foitafisch", "entity.minecraft.tropical_fish.predefined.12": "Popageifisch", "entity.minecraft.tropical_fish.predefined.13": "Königin-Englfisch", "entity.minecraft.tropical_fish.predefined.14": "Rota Buntborsch", "entity.minecraft.tropical_fish.predefined.15": "Rotlippn-Schleimfisch", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "Fodnflossa", "entity.minecraft.tropical_fish.predefined.18": "Rota Anemonenfisch", "entity.minecraft.tropical_fish.predefined.19": "Drückafisch", "entity.minecraft.tropical_fish.predefined.2": "Palettn-Doktorfisch", "entity.minecraft.tropical_fish.predefined.20": "Gelbschwonz-Popageifisch", "entity.minecraft.tropical_fish.predefined.21": "Gelba Seglflossndokta", "entity.minecraft.tropical_fish.predefined.3": "Foitafisch", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Zuckawattn-Kompffisch", "entity.minecraft.tropical_fish.predefined.7": "Zweagborsch", "entity.minecraft.tropical_fish.predefined.8": "Koasa<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Meerbarbe", "entity.minecraft.tropical_fish.type.betty": "Peitschnfisch", "entity.minecraft.tropical_fish.type.blockfish": "Blockfisch", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON>litz<PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Zoppla", "entity.minecraft.tropical_fish.type.glitter": "G<PERSON><PERSON><PERSON>sch", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Streifla", "entity.minecraft.tropical_fish.type.sunstreak": "Sonnenstreifnfisch", "entity.minecraft.turtle": "Schüdkrötn", "entity.minecraft.vex": "Plogegeist", "entity.minecraft.villager": "Dorfbewohna", "entity.minecraft.villager.armorer": "Rüstungsschmied", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Pfeilmocha", "entity.minecraft.villager.leatherworker": "G<PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliothekar", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Schwochkopf", "entity.minecraft.villager.none": "Dorfbewohna", "entity.minecraft.villager.shepherd": "Schäfa", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON>ns<PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON>", "entity.minecraft.wandering_trader": "Fohrenda Händla", "entity.minecraft.warden": "Wärta", "entity.minecraft.wind_charge": "Windkugl", "entity.minecraft.witch": "Hex", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherskelett", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Dorfbewohnazombie", "entity.minecraft.zombified_piglin": "Zombifiziata <PERSON>", "entity.not_summonable": "Konn ka Entität mit Dem Typ %s spawnen", "event.minecraft.raid": "Übafoi", "event.minecraft.raid.defeat": "Niederlogn", "event.minecraft.raid.defeat.full": "Übafoi – Niederlogn", "event.minecraft.raid.raiders_remaining": "Verbleibnde Räuber: %s", "event.minecraft.raid.victory": "Sieg", "event.minecraft.raid.victory.full": "Übafoi – Sieg", "filled_map.buried_treasure": "Schotzkortn", "filled_map.explorer_jungle": "Dschungl-Erkundungskortn", "filled_map.explorer_swamp": "Sumpf-Erkundungskortn", "filled_map.id": "Nr. #%s", "filled_map.level": "(Stufn %s/%s)", "filled_map.locked": "Gspat", "filled_map.mansion": "Woid-Erkundungskortn", "filled_map.monument": "Ozean-Erkundungskortn", "filled_map.scale": "Moßstob 1:%s", "filled_map.trial_chambers": "Prüfungskommakoatn", "filled_map.unknown": "Unbekonnte <PERSON>n", "filled_map.village_desert": "Wüstndorfkortn", "filled_map.village_plains": "Ebenendorfkortn", "filled_map.village_savanna": "Savannendorfkortn", "filled_map.village_snowy": "Schneedorfkortn", "filled_map.village_taiga": "Taigadorfkortn", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Klassischs Flochlond", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Obawöd", "flat_world_preset.minecraft.redstone_ready": "Redstone, fertig, los!", "flat_world_preset.minecraft.snowy_kingdom": "Schneekenigreich", "flat_world_preset.minecraft.the_void": "<PERSON> <PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Traum vom Tunnlbaua", "flat_world_preset.minecraft.water_world": "Wossawöd", "flat_world_preset.unknown": "???", "gameMode.adventure": "Ob<PERSON><PERSON><PERSON>", "gameMode.changed": "<PERSON><PERSON>ümo<PERSON> is zu %s geändat wordn", "gameMode.creative": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "gameMode.hardcore": "Hoata Modus!", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "Übaleb<PERSON><PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "Vo Spiela entfernts Feia aktualisiern", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON>, o<PERSON> <PERSON><PERSON> und Lava in einer Entfernung von mehr als 8 <PERSON><PERSON> von einem Spieler aktualisiert werden", "gamerule.announceAdvancements": "Fortschritte bekonnt gebn", "gamerule.blockExplosionDropDecay": "Block-Explosionen erzeugn koane Drops mehr", "gamerule.blockExplosionDropDecay.description": "A boa von de <PERSON> von Blocks zastöat von Explosianen duach Block Intaaktionen gehn in da Explosian valoan.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Vaschiedns", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Erzeugung", "gamerule.category.updates": "Wödaktualisierungen", "gamerule.commandBlockOutput": "Befehlsblockausgobn onzoagn", "gamerule.commandModificationBlockLimit": "Blockgrenz für Befehlsänderungn", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON> der Blöck, die mit einem Befehl wie \"fü<PERSON><PERSON>\" odr \"klonen\" gleichzeitig geändert werdn können.", "gamerule.disableElytraMovementCheck": "Elytrenflugübaprüfung deaktivian", "gamerule.disablePlayerMovementCheck": "Spilabewegung net übaprüfn", "gamerule.disableRaids": "Deaktiviar Übafälle", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doEntityDrops": "Objektausbeutn folln lossn", "gamerule.doEntityDrops.description": "Kontrolliat des Follenlossn von da <PERSON>sbe<PERSON> (eischließlich Inventaren), <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> usw.", "gamerule.doFireTick": "Feiaausbreitung", "gamerule.doImmediateRespawn": "Sofortiga Wiedaeistieg", "gamerule.doInsomnia": "<PERSON><PERSON>", "gamerule.doLimitedCrafting": "Rezepte zua Heastellung benötigt", "gamerule.doLimitedCrafting.description": "Wenn aktiv kinnen Spiela lei scho freigschoitene Rezepte heanemma.", "gamerule.doMobLoot": "K<PERSON>turnausbeut foin lossn", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON><PERSON>, ob Kreatura Beute falla lond, is<PERSON><PERSON><PERSON><PERSON>lich Erfahrungskugla.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON> e<PERSON>", "gamerule.doMobSpawning.description": "Manche Entitäten künnten oagene Regla ha.", "gamerule.doPatrolSpawning": "Plündera‐Patrouillen aschoffn", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON>cke foin lossn", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON><PERSON>, ob B<PERSON><PERSON>cke ihre Beute falla lond, is<PERSON><PERSON><PERSON><PERSON><PERSON> Erfahrungskugla.", "gamerule.doTraderSpawning": "Fohrende Händla <PERSON>ugn", "gamerule.doVinesSpread": "Ronknausbreitung", "gamerule.doVinesSpread.description": "<PERSON><PERSON><PERSON>, ob sech Ronkn zuafollig auf benochborte Blöcke ausbreitn kenna. Wirkt sech net auf ondre Ronknortn wia Trauaronkn, Zwirblronken usw. aus.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON> erzeugn", "gamerule.doWeatherCycle": "Wettawechsl", "gamerule.drowningDamage": "Schodn durch Ertrinkn", "gamerule.enderPearlsVanishOnDeath": "Gewoafnene Enderperln vaschwindn beim Sterbn", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, de von am S<PERSON> gworf wean, v<PERSON><PERSON><PERSON><PERSON>, wenn da <PERSON><PERSON><PERSON> stirbt.", "gamerule.entitiesWithPassengersCanUsePortals": "Portalnutzung durch Objekte mit Passagieren", "gamerule.entitiesWithPassengersCanUsePortals.description": "Ermöglicht es entities mit Passagieren, si durch Nether‐, End‐ und Endtransitportale zu teleportieren.", "gamerule.fallDamage": "<PERSON><PERSON><PERSON>ag", "gamerule.fireDamage": "<PERSON><PERSON><PERSON>a", "gamerule.forgiveDeadPlayers": "Gstorbenen Spiela vergebn", "gamerule.forgiveDeadPlayers.description": "Verärgerte neitrale Kreaturn hean auf, wü<PERSON> zu sei, wenn da Züspieler in da Nähe stirb.", "gamerule.freezeDamage": "Erfriarungsschodn", "gamerule.globalSoundEvents": "Globale Sound Events", "gamerule.globalSoundEvents.description": "<PERSON><PERSON> bestim<PERSON>te Spü-<PERSON> passian, wie zum Beispü a Boss wiad g<PERSON>awnd, konn man des üwar<PERSON> hean.", "gamerule.keepInventory": "Inventar noch dem Tod beholtn", "gamerule.lavaSourceConversion": "<PERSON>va wiad zu ana Q<PERSON>n", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON> fl<PERSON><PERSON><PERSON> von zwa Seitn Lava Quölln umgebn is, weads soba zu ana Quölln.", "gamerule.locatorBar": "Aktiviare Spüa aufspüa Leistn", "gamerule.locatorBar.description": "Weins aktiviat is wiad a Leistn am Büdschirm aunzagt, dei dei Richtung von den Spüan aunzagt.", "gamerule.logAdminCommands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.maxCommandChainLength": "Obergrenzn fia Befehlskettn", "gamerule.maxCommandChainLength.description": "<PERSON><PERSON><PERSON> uf Befehlsblockketta und Funktiona agwendet.", "gamerule.maxCommandForkCount": "Obergrenze f<PERSON><PERSON>", "gamerule.maxCommandForkCount.description": "Mauxima<PERSON> an Befehlsrauhmen, die von Befehlen wie „execute as“ benuatzt werden können.", "gamerule.maxEntityCramming": "Obagrenzn fia Objektgedränge", "gamerule.minecartMaxSpeed": "Maximale Wagon Gschwindigkeit", "gamerule.minecartMaxSpeed.description": "Normale Höchstgschwindigkeit von am Wagon des sich auf Lond bewegt", "gamerule.mobExplosionDropDecay": "Kreatur-Explosionen erzeugn koane Drops mehr", "gamerule.mobExplosionDropDecay.description": "A boa von <PERSON> von Blocks zastöat von Explosianen duach Mob Intaaktionen gehn in da Explosian valoan.", "gamerule.mobGriefing": "Zastörung durch Kreaturen", "gamerule.naturalRegeneration": "Gsundheit regeneriern", "gamerule.playersNetherPortalCreativeDelay": "Netherportalzeitverzögarung im Kreativ-Modus", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (in Ticks), de a Spila im Kreativ-Modus braucht, um duach a Netherportal zu reisn.", "gamerule.playersNetherPortalDefaultDelay": "Netherportalzeitverzögarung im Nicht-Kreativ-Modus", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (in Ticks), de a Spila im Nicht-Kreativ-Mo<PERSON> brauch<PERSON>, um duach a Netherportal zu reisn.", "gamerule.playersSleepingPercentage": "Schläfa-Onteil", "gamerule.playersSleepingPercentage.description": "De Prozentzoi an Spülan, die schlofn miaßn, um de Nocht zu übaspringa.", "gamerule.projectilesCanBreakBlocks": "Projektile kennan Blöcke zerstörn", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON>, ob aufprollnde Projektile Blöcke zerstörn, de duach se zerstörboar waratn.", "gamerule.randomTickSpeed": "Häufigkeit vo Zufallsereignissn", "gamerule.reducedDebugInfo": "Debug‐Infos reduziern", "gamerule.reducedDebugInfo.description": "Begrenzt d Inhalt vo da Debug-Azoage.", "gamerule.sendCommandFeedback": "Befehlsausgobn onzoagn", "gamerule.showDeathMessages": "Todesmeldungen onzoagn", "gamerule.snowAccumulationHeight": "Schnee-Logen", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON>, Scnee-Logn foamen am Bodn bis zu dea Onzohl on Logn.", "gamerule.spawnChunkRadius": "Spawn-<PERSON><PERSON>", "gamerule.spawnChunkRadius.description": "On<PERSON><PERSON> der Chunks um den Spawnpunkt in da Obawölt, die imma glodn bleibn.", "gamerule.spawnRadius": "<PERSON><PERSON> vom Wiedaeistiegsbereich", "gamerule.spawnRadius.description": "Steiat de Größe vom Bereich um den Spawnpunkt, in dem Spila in de Wölt einsteign kennan.", "gamerule.spectatorsGenerateChunks": "Zuschaua gena<PERSON>n Lo<PERSON>cho<PERSON>n", "gamerule.tntExplodes": "TNT derf entzündn wean und explodiern", "gamerule.tntExplosionDropDecay": "TNT-Explosionen erzeugn koane Drops mehr", "gamerule.tntExplosionDropDecay.description": "A boa von de <PERSON> von Blocks zastöat von Explosianen duach TNT gehn in da Explosian valoan.", "gamerule.universalAnger": "Ollgmeine Veaärgerung", "gamerule.universalAnger.description": "Veaärgerte neutrale Kreaturn greifn jedn S<PERSON>la in da Nähe on, ned le<PERSON>, de se verärgert hobn. Funktioniat am bestn, wenn forgiveDeadPlayers deaktiviert is.", "gamerule.waterSourceConversion": "Wossa wiad zu ana Quölln", "gamerule.waterSourceConversion.description": "When flias<PERSON> <PERSON><PERSON> von zwa Wossa Quölln umgeben ist, wiads söbst zu ana Quölln.", "generator.custom": "Benutzade<PERSON>ier<PERSON>", "generator.customized": "<PERSON><PERSON>", "generator.minecraft.amplified": "DURCHLECHAT", "generator.minecraft.amplified.info": "Hinweis: <PERSON><PERSON> o<PERSON> do<PERSON>, braucht an guadn Computer.", "generator.minecraft.debug_all_block_states": "Debug-Modus", "generator.minecraft.flat": "Flochlond", "generator.minecraft.large_biomes": "Große Biome", "generator.minecraft.normal": "Standard", "generator.minecraft.single_biome_surface": "Einzlnes Biom", "generator.single_biome_caves": "<PERSON><PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Schwebene Insl", "gui.abuseReport.attestation": "Mit der Einreichung von da Mödung versicherst du, dass dei Ongoben wahrheitsgetreu und voiständig is – noch bestem Wissen.", "gui.abuseReport.comments": "Kommentare", "gui.abuseReport.describe": "<PERSON>n du Details mit uns teils, konn uns des hölfn, wichtige Entscheidungen zum treffn.", "gui.abuseReport.discard.content": "<PERSON><PERSON> de Seitn valo<PERSON>t, geht de Mödung und deine Onmerkungen valuan.\n<PERSON><PERSON> <PERSON> sic<PERSON>, dasst des mochn wüßt?", "gui.abuseReport.discard.discard": "Valossn und Mödung vawerfn", "gui.abuseReport.discard.draft": "<PERSON><PERSON>", "gui.abuseReport.discard.return": "Beorbeitung fortsetzn", "gui.abuseReport.discard.title": "<PERSON>ö<PERSON>ng und Kommentare vaschmeißn?", "gui.abuseReport.draft.content": "Mogst du de bestehnde Mödung weita beorbeitn, oda se vawerfn und a neie erstelln?", "gui.abuseReport.draft.discard": "Vawerfn", "gui.abuseReport.draft.edit": "Beorbeitung fortsetzn", "gui.abuseReport.draft.quittotitle.content": "Mogst du eam weita beorbeitn oda vawerfn?", "gui.abuseReport.draft.quittotitle.title": "<PERSON> host an <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dea val<PERSON> geh<PERSON>, wenn du es Spü valosst.", "gui.abuseReport.draft.title": "Mödeentwurf beorbeitn?", "gui.abuseReport.error.title": "Problem beim <PERSON>en deiner Meldung", "gui.abuseReport.message": "Wo hod oana deppad tu?\nDe Auswoi erleichtat uns, dein Vorgong zu untasuachn.", "gui.abuseReport.more_comments": "<PERSON>te beschreib wos passiad is:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON>, warum du den Nomen möldn mechast:", "gui.abuseReport.name.reporting": "Du mödst „%s“.", "gui.abuseReport.name.title": "Spülanomn mödn", "gui.abuseReport.observed_what": "Warum mechast du des mödn?", "gui.abuseReport.read_info": "<PERSON><PERSON> wos übers mödn", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON><PERSON><PERSON> o<PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON> ermutigt and<PERSON>, an illegalen Drogen-bezogenen Aktivitäten teilzunehmen, oder ermutigt Minderjährige zum Trinken.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Sexuelle Ausbeutung oda Missbrauch vo Kinda", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> redet über onstößigs Verhoitn gegenüber Kinda oda fördats auf ondare Weise.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Nachahmung oder falsche Informationen", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON> sch<PERSON>gt den Ruf e<PERSON> anderen, g<PERSON><PERSON> vor, j<PERSON><PERSON> zu se<PERSON>, der er nicht ist, oder teilt falsche Information<PERSON> mit dem Ziel, andere auszubeuten oder in die Irre zu führen.", "gui.abuseReport.reason.description": "Beschreibung:", "gui.abuseReport.reason.false_reporting": "Foischmödungen", "gui.abuseReport.reason.generic": "I mechat die mödn", "gui.abuseReport.reason.generic.description": "<PERSON><PERSON> is lei beim um<PERSON>n / hot wos tu wos ma ned gfoid.", "gui.abuseReport.reason.harassment_or_bullying": "Belästigung oda Mobbing", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, gre<PERSON> an oder schikaniert Sie oder jemand anderen. <PERSON><PERSON> schließt ein, wenn jemand wieder<PERSON>t versucht, <PERSON><PERSON> oder eine andere Person ohne Zustimmung zu kontaktieren, oder ohne Zustimmung private persönliche Informationen über Sie oder eine andere Person veröffentlicht („Doxing“).", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON> greift di oda an ondan spiela aufgrund vo Sochn wia seina Religion, Ethnie oda Sexualität on.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>, and<PERSON><PERSON> zu<PERSON>gen", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> oder einer anderen Person im wirklichen Leben Schaden zuzufügen.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Intime Büldlan ohne Einverständns", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON> spricht über private und intime Bilder, teilt sie oder fördert sie auf andere <PERSON>.", "gui.abuseReport.reason.self_harm_or_suicide": "<PERSON><PERSON><PERSON> - Selbstverletzung oder Selbstmord", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON>, sich im wirklichen Leben zu verletzen oder spricht davon, sich im wirklichen Leben zu verletzen.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON> unangmes<PERSON>n", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins, die sexuelle Hondlungen, Gschlechtsorgane und/oder sexuelle Gewalt grafisch doarstölln.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismus oda Extremismus mit Gewoit", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> spricht über Terrorismus oder gewalttätigen Extremismus aus politischen, religiösen, ideologischen oder anderen Gründen, fö<PERSON><PERSON> ihn oder droht mit diesem.", "gui.abuseReport.reason.title": "Möld-Kate<PERSON><PERSON>w<PERSON>hn", "gui.abuseReport.report_sent_msg": "Mia hom dei Meldung erhoitn. Donk da!\n\nUnsa Team wead sich möglichst zeitnoh drum kümman.", "gui.abuseReport.select_reason": "Mödekategorie auswöhn", "gui.abuseReport.send": "Beschwerde sendn", "gui.abuseReport.send.comment_too_long": "Bitte kürz den Kommentar", "gui.abuseReport.send.error_message": "Beim Senden deiner Meldung ist ein Fehler aufgetreten:\n‚%s‘", "gui.abuseReport.send.generic_error": "<PERSON>im <PERSON>en deiner Meldung ist ein unerwarteter Fehler aufgetreten.", "gui.abuseReport.send.http_error": "Ein unerwartet HTTP Fehler beim Senden Ihres Berichts aufgetreten ist.", "gui.abuseReport.send.json_error": "Beim Senden Ihres Berichts wurde eine fehlerhafte Nutzlast festgestellt.", "gui.abuseReport.send.no_reason": "Bitte wöhl a Report-Kategorie aus", "gui.abuseReport.send.not_attested": "Bitte les da den text do oben durch und kreizel donn den Kontroikosten ob, um die Mödung absenden zu können", "gui.abuseReport.send.service_unavailable": "Der Missbrauchsmeldedienst kann nicht erreicht werden. <PERSON>te vergewissern <PERSON> sich, dass Sie mit dem Internet verbunden sind, und versuchen Sie es erneut", "gui.abuseReport.sending.title": "<PERSON><PERSON> wead obgschickt ...", "gui.abuseReport.sent.title": "Mödung obgschickt", "gui.abuseReport.skin.title": "Skin des Spülas mödn", "gui.abuseReport.title": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Chaetnachrichten", "gui.abuseReport.type.name": "Spülanomn", "gui.abuseReport.type.skin": "Skin", "gui.acknowledge": "V<PERSON>ond<PERSON>", "gui.advancements": "Errungenschoftn", "gui.all": "Oi", "gui.back": "Zrugg", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON>r mehr beim folgendn Link: %s", "gui.banned.description.permanent": "<PERSON><PERSON> ist dauerhaft gesperrt, was bedeu<PERSON><PERSON>, dass du nicht mehr Online spielen oder Realms beitreten kannst.", "gui.banned.description.reason": "Wir haben kürzlich eine Meldung wegen schlechten Verhaltens Ihres Kontos erhalten. Unsere Moderatoren haben Ihren Fall nun überprüft und ihn als %s identifiziert, was gegen die Minecraft-Community-Standards verstößt.", "gui.banned.description.reason_id": "Kode %s", "gui.banned.description.reason_id_message": "Kode %s-%s", "gui.banned.description.temporary": "%s Bis dahin kannst du nicht online spielen oder Realms beitreten.", "gui.banned.description.temporary.duration": "Ihr Konto ist vorübergehend gesperrt und wird in %s reaktiviert.", "gui.banned.description.unknownreason": "Wir haben kürzlich eine Meldung wegen schlechten Verhaltens Ihres Kontos erhalten. Unsere Moderatoren haben Ihren Fall nun überprüft und ihn identifiziert, was gegen die Minecraft-Community-Standards verstößt.", "gui.banned.name.description": "Dei jeziga Nome - '%s' - verstößt gegn unsare Community Standarts. Du konnst Anzspila spüln, oba du muast dein Nomen ändan, um online zu spüln.\n\nErfohr mea oda loss a Follübaprüfung beim folgenden Link do: %s", "gui.banned.name.title": "<PERSON><PERSON> ist nicht erlaubt in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Idontitätsdiebstohl oda Vorbreitung von Informationen, um ondre zu täuschn oda zu betriagn", "gui.banned.reason.drugs": "Erwähnung illgola Drogn", "gui.banned.reason.extreme_violence_or_gore": "Dorstollungn vo oxzessiva Gewolt oda bluatign Szonen aus dem wirklichn Löbn", "gui.banned.reason.false_reporting": "Übermößig vüle folsche oda ungnaue Möldungen", "gui.banned.reason.fraud": "Betriagarische Oneignung oda Verwertung vo Inholtn", "gui.banned.reason.generic_violation": "Verlötzung da Community‐Stondards", "gui.banned.reason.harassment_or_bullying": "Böleidigende Sproche, de gezült in verlötzender Weise verwendat wer", "gui.banned.reason.hate_speech": "Hossrede oda Diskriminierung", "gui.banned.reason.hate_terrorism_notorious_figure": "Erwöhnung vo Hossgruppn, terroristischn Organisationen oda gfährlichn Persönlichkeitn", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON><PERSON><PERSON><PERSON>, Personen oda Eigentum im wirklichn Löbn zua schödign", "gui.banned.reason.nudity_or_pornography": "Dorstellung vo onzüglichm oda pornografischm Moteriol", "gui.banned.reason.sexually_inappropriate": "<PERSON>n oda Inholte mit sexuellm Bezug", "gui.banned.reason.spam_or_advertising": "<PERSON>m oda Werbung", "gui.banned.skin.description": "Dei jeziga Skin verstößt gegn unsare Community Standarts. Du konnst mit am Standart Skin spüln, oda du nimmst an neichn.\n\nErfohr mea oda loss a Follübaprüfung beim folgenden Link do: %s", "gui.banned.skin.title": "Skin net erlaubt", "gui.banned.title.permanent": "Das Konto ist dauerhaft gesperrt", "gui.banned.title.temporary": "Konto vorübergehend gesperrt", "gui.cancel": "Obbrechn", "gui.chatReport.comments": "Kommentare", "gui.chatReport.describe": "Deteils teiln kon uns helfn bessere Entscheidungen zu treffn.", "gui.chatReport.discard.content": "<PERSON><PERSON> weggehst geht die Mödung verloren.\nBist da sicha das du des möchn wüßt?", "gui.chatReport.discard.discard": "Valossn und Mödung vaschmeißn", "gui.chatReport.discard.draft": "<PERSON><PERSON>", "gui.chatReport.discard.return": "Beorbeiten fortsetzen", "gui.chatReport.discard.title": "<PERSON>ö<PERSON>ng und Kommentare vaschmeißn?", "gui.chatReport.draft.content": "Mogst du de bestehnde Mödung weita beoabeitn, oda se vaweafn und a neie eastölln?", "gui.chatReport.draft.discard": "Vaweafn", "gui.chatReport.draft.edit": "Beoabeitung foatsetzn", "gui.chatReport.draft.quittotitle.content": "Mogst du ihn weita beoabeitn oda vaweafn?", "gui.chatReport.draft.quittotitle.title": "Du host an <PERSON><PERSON><PERSON><PERSON> von ana <PERSON>, dea oba valo<PERSON> geht, wenn du des S<PERSON>ü beendest", "gui.chatReport.draft.title": "Ent<PERSON><PERSON> da Chatmeldung beoabeitn?", "gui.chatReport.more_comments": "Bitte erkläre was passiert ist:", "gui.chatReport.observed_what": "Warum meachast du des Mödn?", "gui.chatReport.read_info": "<PERSON><PERSON> wos über Mödn", "gui.chatReport.report_sent_msg": "Mia hom dei Meldung erhoitn. Donk und Onerkennung!\n\nUnsa Team wead sies so früh wie möglich onschaun.", "gui.chatReport.select_chat": "<PERSON><PERSON> Nachricht zum Mödn aus", "gui.chatReport.select_reason": "<PERSON><PERSON> da a Report Kategorie aus", "gui.chatReport.selected_chat": "%s Chatnochricht(en) zum Mödn ausgsuacht", "gui.chatReport.send": "Beschwerde sendn", "gui.chatReport.send.comments_too_long": "Bitte kürze den Kommentar", "gui.chatReport.send.no_reason": "<PERSON>te suach da a Report Kategorie aus", "gui.chatReport.send.no_reported_messages": "Bitte wöh mindestens oa Chatnochricht zum mödn aus", "gui.chatReport.send.too_many_messages": "<PERSON> versuch<PERSON>, zu viele Nachrichten in die Meldung aufzunehmen", "gui.chatReport.title": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Na<PERSON><PERSON>ten rund um diese Auswahl werden eingeschlossen, um zusätzlichen Kontext bereitzustellen", "gui.chatSelection.fold": "%s Nochricht(n) vasteckt", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s is dem Chat beigetretn", "gui.chatSelection.message.narrate": "%s gsogt %s at %s", "gui.chatSelection.selected": "%s/%s Nachricht(en) ausgewählt", "gui.chatSelection.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, de du Mödn mogst", "gui.continue": "<PERSON><PERSON>", "gui.copy_link_to_clipboard": "<PERSON> k<PERSON>", "gui.days": "%s Tog", "gui.done": "<PERSON><PERSON>", "gui.down": "<PERSON><PERSON>", "gui.entity_tooltip.type": "Typ: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s <PERSON> <PERSON> g<PERSON> ned", "gui.fileDropFailure.title": "<PERSON><PERSON><PERSON><PERSON> hod ned hikaut", "gui.hours": "%s Stund(n)", "gui.loadingMinecraft": "Minecraft wird glodn", "gui.minutes": "%s Minut(n)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s Schoitflächn", "gui.narrate.editBox": "%s Box beorbeiten: %s", "gui.narrate.slider": "%s Sc<PERSON>eberegla", "gui.narrate.tab": "%s‐Reita", "gui.no": "Na", "gui.none": "<PERSON><PERSON>", "gui.ok": "Ok", "gui.open_report_dir": "Berichtsverzeichnis öffnen", "gui.proceed": "Fortfohrn", "gui.recipebook.moreRecipes": "Rechtsklick fia mehr", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Suachn...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> ois", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON>", "gui.report_to_server": "An den Serva meldn", "gui.socialInteractions.blocking_hint": "Mit Microsoft-<PERSON><PERSON> vawoltn", "gui.socialInteractions.empty_blocked": "Koane im Chat blockiatn Spiela", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON> im Chat ausblendet", "gui.socialInteractions.hidden_in_chat": "Chatnochrichtn von %s wean ausblendet", "gui.socialInteractions.hide": "<PERSON><PERSON> au<PERSON>", "gui.socialInteractions.narration.hide": "Versteck Nochricht von %s", "gui.socialInteractions.narration.report": "Spüla mödn %s", "gui.socialInteractions.narration.show": "Nochrichtn vo %s uzoagn", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "Es kann koa Spiela mit dem Nomn gfundn werdn", "gui.socialInteractions.search_hint": "Suachn...", "gui.socialInteractions.server_label.multiple": "%s - %s <PERSON><PERSON>la", "gui.socialInteractions.server_label.single": "%s - %s <PERSON><PERSON>la", "gui.socialInteractions.show": "<PERSON><PERSON>", "gui.socialInteractions.shown_in_chat": "Chatnochrichtn von %s wean onzo<PERSON>t", "gui.socialInteractions.status_blocked": "Blockiat", "gui.socialInteractions.status_blocked_offline": "Blockiat - Offline", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Ausblendet - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "Oi", "gui.socialInteractions.tab_blocked": "Blockiat", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Soziale Interaktionen", "gui.socialInteractions.tooltip.hide": "Nochrichtn vasteckn", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "Der Meldedienst ist nicht verfügbar", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON>ine meldbaren Nachrichten von S<PERSON>ler %s", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON>ser Spieler kann nicht gemeldet werden, da seine Chatnachrichten auf diesem Server nicht überprüft werden können", "gui.socialInteractions.tooltip.show": "Nochrichtn uzoagn", "gui.stats": "Statistikn", "gui.toMenu": "Zrück zua Server Listn", "gui.toRealms": "Zrugg zur Realms-Listn", "gui.toTitle": "Zrugg zum Hauptmenü", "gui.toWorld": "Zrugg zur Wödlistn", "gui.togglable_slot": "<PERSON><PERSON><PERSON>, um den Inventarplotz zum sperrn", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Zruck(%s)", "gui.waitingForResponse.title": "Woatn aufn Server", "gui.yes": "<PERSON>", "hanging_sign.edit": "Hängeschüdbeschriftung beorbeitn", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Ruafn", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "F<PERSON><PERSON>n", "instrument.minecraft.ponder_goat_horn": "Nochdenkn", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Gegnstond zastörn", "inventory.hotbarInfo": "Schnellzugriffsleiste mit %1$s+%2$s speichern", "inventory.hotbarSaved": "Schnellzugriffsleistn gspeichert (mit %1$s+%2$s wiederherstelln)", "item.canBreak": "<PERSON> obbaun:", "item.canPlace": "<PERSON> platziat wean auf:", "item.canUse.unknown": "Unbekonnt", "item.color": "Forb: %s", "item.components": "%s Komponent(n)", "item.disabled": "Deaktivierta Gegnstond", "item.durability": "Hoitborkeit: %s / %s", "item.dyed": "Gfärbt", "item.minecraft.acacia_boat": "Akazienhoizboot", "item.minecraft.acacia_chest_boat": "Akazienhoizboot mit Kistn", "item.minecraft.allay_spawn_egg": "Hüfsgeist-Spawn-Ei", "item.minecraft.amethyst_shard": "Amethystscherbn", "item.minecraft.angler_pottery_shard": "Ongla-Töpfascherbn", "item.minecraft.angler_pottery_sherd": "Ongla-Töpfascherbn", "item.minecraft.apple": "Op<PERSON>", "item.minecraft.archer_pottery_shard": "Schützn-Töpfascherbn", "item.minecraft.archer_pottery_sherd": "Schützn-Töpfascherbn", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Gürtltia Spawn Ei", "item.minecraft.armor_stand": "Rüstungsstända", "item.minecraft.arms_up_pottery_shard": "Gebärdn-Töpfascherbn", "item.minecraft.arms_up_pottery_sherd": "Gebärdn-Töpfascherbn", "item.minecraft.arrow": "Pfeil", "item.minecraft.axolotl_bucket": "Axolotlkiwe", "item.minecraft.axolotl_spawn_egg": "Axolotl-Spawn-Ei", "item.minecraft.baked_potato": "Ofnkartoffe", "item.minecraft.bamboo_chest_raft": "Bambusfloß mit Kistn", "item.minecraft.bamboo_raft": "Bambusfloß", "item.minecraft.bat_spawn_egg": "Fledamaus-Spawn-Ei", "item.minecraft.bee_spawn_egg": "Bienen-Spawn-Ei", "item.minecraft.beef": "Ruachs Rindfleisch", "item.minecraft.beetroot": "Rote Bete", "item.minecraft.beetroot_seeds": "Rote-Bete-Somen", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Birknhoizboot", "item.minecraft.birch_chest_boat": "Birknhoizboot mit Kistn", "item.minecraft.black_bundle": "Schwoaz<PERSON>", "item.minecraft.black_dye": "Schworze Forb", "item.minecraft.black_harness": "Schwoazes Gschirr", "item.minecraft.blade_pottery_shard": "Klingen-Töpfascherbn", "item.minecraft.blade_pottery_sherd": "Klingen-Töpfascherbn", "item.minecraft.blaze_powder": "Lohnstab", "item.minecraft.blaze_rod": "Lohenrutn", "item.minecraft.blaze_spawn_egg": "Lohen-Spawn-Ei", "item.minecraft.blue_bundle": "<PERSON><PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Sumpfskellet Spawn Ei", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolzn-Rüstungsbesotz", "item.minecraft.bone": "Knochn", "item.minecraft.bone_meal": "Knochnmöh", "item.minecraft.book": "<PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bow": "Bogn", "item.minecraft.bowl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON>", "item.minecraft.breeze_rod": "Böenruatn", "item.minecraft.breeze_spawn_egg": "Böen-Spawn-Ei", "item.minecraft.brewer_pottery_shard": "Brauer-Töpfascherbn", "item.minecraft.brewer_pottery_sherd": "Brauer-Töpfascherbn", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "Ziagl", "item.minecraft.brown_bundle": "<PERSON><PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON>", "item.minecraft.brush": "Pinsl", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Konn an gmischtn Stapl von Gegnständn aufkhoitn", "item.minecraft.bundle.full": "Voi", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Flommen-Töpfascherbn", "item.minecraft.burn_pottery_sherd": "Flommen-Töpfascherbn", "item.minecraft.camel_spawn_egg": "Kamel-Spawn-Ei", "item.minecraft.carrot": "Karottn", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON>nongl", "item.minecraft.cat_spawn_egg": "Kotz-Spawn-Ei", "item.minecraft.cauldron": "Kessl", "item.minecraft.cave_spider_spawn_egg": "Höhlnspinn-Spawn-Ei", "item.minecraft.chainmail_boots": "Kettnstiefe", "item.minecraft.chainmail_chestplate": "Kettnhemd", "item.minecraft.chainmail_helmet": "Kettnhaubn", "item.minecraft.chainmail_leggings": "Kettnhosn", "item.minecraft.charcoal": "Hoizkohle", "item.minecraft.cherry_boat": "Kirschnhoizboot", "item.minecraft.cherry_chest_boat": "Kirschnhoizboot mit Kistn", "item.minecraft.chest_minecart": "Wagon mit Kistn", "item.minecraft.chicken": "Ruachs Hendl", "item.minecraft.chicken_spawn_egg": "Hendl-Spawn-Ei", "item.minecraft.chorus_fruit": "Chorusfrucht", "item.minecraft.clay_ball": "Tonklumpen", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.coast_armor_trim_smithing_template.new": "Strond-Rüstungssotz", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "Kabeljaukiwe", "item.minecraft.cod_spawn_egg": "Kabeljau-Spawn-Ei", "item.minecraft.command_block_minecart": "Wagon mit Be<PERSON>hlsblock", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Steak", "item.minecraft.cooked_chicken": "Brotns Hendl", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "Brotns Hommefleisch", "item.minecraft.cooked_porkchop": "Schweinsbrotn", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "Keks", "item.minecraft.copper_ingot": "Kupfaborrn", "item.minecraft.cow_spawn_egg": "Kua-Spawn-Ei", "item.minecraft.creaking_spawn_egg": "Knoaza-Spawn-Ei", "item.minecraft.creeper_banner_pattern": "Ban<PERSON>ust<PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-Bannervualog", "item.minecraft.creeper_spawn_egg": "Creeper-Spawn-Ei", "item.minecraft.crossbow": "Oambrust", "item.minecraft.crossbow.projectile": "Gschoss:", "item.minecraft.crossbow.projectile.multiple": "Gschoss: %s x %s", "item.minecraft.crossbow.projectile.single": "Gschoss: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cyan_dye": "Türkise <PERSON>b", "item.minecraft.cyan_harness": "Türkises Gschirr", "item.minecraft.danger_pottery_shard": "Gfohrn-Töpfascherbn", "item.minecraft.danger_pottery_sherd": "Gfohrn-Töpfascherbn", "item.minecraft.dark_oak_boat": "Schworzoachnhoizboot", "item.minecraft.dark_oak_chest_boat": "Schworzoachnhoizboot mit Kistn", "item.minecraft.debug_stick": "Debug-Stab", "item.minecraft.debug_stick.empty": "%s hot koane <PERSON>n", "item.minecraft.debug_stick.select": "\"%s\" (%s) ausgwöhd", "item.minecraft.debug_stick.update": "‚%s‘ is jetz %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Diamantoxt", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "Diamantbrustplottn", "item.minecraft.diamond_helmet": "Diamanthöm", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Roßrüstung aus Diamant", "item.minecraft.diamond_leggings": "Diamanthax<PERSON>tz", "item.minecraft.diamond_pickaxe": "Diamantspitzhock", "item.minecraft.diamond_shovel": "Diamantschaufe", "item.minecraft.diamond_sword": "Diamantschwert", "item.minecraft.disc_fragment_5": "Schoiplottnscherbe", "item.minecraft.disc_fragment_5.desc": "Schoiplottn - 5", "item.minecraft.dolphin_spawn_egg": "Delfin-Spawn-Ei", "item.minecraft.donkey_spawn_egg": "Esl-Spawn-Ei", "item.minecraft.dragon_breath": "Drochnatem", "item.minecraft.dried_kelp": "Trockneta <PERSON>", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>-Spawn-Ei", "item.minecraft.dune_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.dune_armor_trim_smithing_template.new": "Dünen-Rüstungssotz", "item.minecraft.echo_shard": "Echoscherbn", "item.minecraft.egg": "<PERSON>i", "item.minecraft.elder_guardian_spawn_egg": "Großa-Wächta-Spawn-Ei", "item.minecraft.elytra": "Elytren", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Vazaubats <PERSON>", "item.minecraft.enchanted_golden_apple": "Vazaubata <PERSON>", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Enderdrochn-Spawn-Ei", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Enderman-Spawn-Ei", "item.minecraft.endermite_spawn_egg": "Endermite-Spawn-Ei", "item.minecraft.evoker_spawn_egg": "Magier-Spawn-Ei", "item.minecraft.experience_bottle": "Erfohrungsflosch", "item.minecraft.explorer_pottery_shard": "Entdecka-Töpfascherbn", "item.minecraft.explorer_pottery_sherd": "Entdecka-Töpfascherbn", "item.minecraft.eye_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.eye_armor_trim_smithing_template.new": "Augn-Rü<PERSON>ungssotz", "item.minecraft.feather": "Feda", "item.minecraft.fermented_spider_eye": "Fermentiads Spinnenaug", "item.minecraft.field_masoned_banner_pattern": "Mauarungs-Bannervualog", "item.minecraft.filled_map": "Kortn", "item.minecraft.fire_charge": "Feiakugl", "item.minecraft.firework_rocket": "Feiawerksraketn", "item.minecraft.firework_rocket.flight": "Flugdaua:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Feiawerksstern", "item.minecraft.firework_star.black": "Schworz", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON>", "item.minecraft.firework_star.custom_color": "Benutzade<PERSON>ier<PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Übagong zu", "item.minecraft.firework_star.flicker": "Funkln", "item.minecraft.firework_star.gray": "G<PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Hellblau", "item.minecraft.firework_star.light_gray": "Hellgrau", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Orange", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON>", "item.minecraft.firework_star.red": "Rot", "item.minecraft.firework_star.shape": "Unbekonnte Form", "item.minecraft.firework_star.shape.burst": "Explosion", "item.minecraft.firework_star.shape.creeper": "Creeperförmig", "item.minecraft.firework_star.shape.large_ball": "Große Kugl", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Stern<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.trail": "Schwoaf", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "Ongl", "item.minecraft.flint": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Feiazeig", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "Fluss-Rüstungssotz", "item.minecraft.flow_banner_pattern": "Bonnavualog", "item.minecraft.flow_banner_pattern.desc": "Fluss", "item.minecraft.flow_banner_pattern.new": "Fluss-Bannervualog", "item.minecraft.flow_pottery_sherd": "Flus<PERSON> Töpfavualog", "item.minecraft.flower_banner_pattern": "Ban<PERSON>ust<PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Blumen-Bannervualog", "item.minecraft.flower_pot": "Blu<PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "Fux-Spawn-Ei", "item.minecraft.friend_pottery_shard": "Freind-Töpfascherbn", "item.minecraft.friend_pottery_sherd": "Freind-Töpfascherbn", "item.minecraft.frog_spawn_egg": "Frosch-Spawn-Ei", "item.minecraft.furnace_minecart": "<PERSON>agon mit Ofn", "item.minecraft.ghast_spawn_egg": "Ghast-Spawn-Ei", "item.minecraft.ghast_tear": "Ghastträne", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Glitzande Melonenscheibn", "item.minecraft.globe_banner_pattern": "Ban<PERSON>ust<PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Globus-Bannervualog", "item.minecraft.glow_berries": "Leichtbeern", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Leichttintnfisch-Spawn-Ei", "item.minecraft.glowstone_dust": "Leichtstoastab", "item.minecraft.goat_horn": "Goa<PERSON>horn", "item.minecraft.goat_spawn_egg": "Goaß-Spawn-Ei", "item.minecraft.gold_ingot": "Goidborrn", "item.minecraft.gold_nugget": "Goidklumpn", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "Goidoxt", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Goidbrustplottn", "item.minecraft.golden_helmet": "Goidhöm", "item.minecraft.golden_hoe": "Goid<PERSON>ck", "item.minecraft.golden_horse_armor": "Roßrüstung aus Goid", "item.minecraft.golden_leggings": "Goidhaxnschutz", "item.minecraft.golden_pickaxe": "Goidspitzhock", "item.minecraft.golden_shovel": "Goidschaufe", "item.minecraft.golden_sword": "Goidschwert", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Wächta-Spawn-Ei", "item.minecraft.gunpowder": "Schworzpuiva", "item.minecraft.guster_banner_pattern": "Bonnavualog", "item.minecraft.guster_banner_pattern.desc": "Windstoßa", "item.minecraft.guster_banner_pattern.new": "Windstoßa-Bannervualog", "item.minecraft.guster_pottery_sherd": "Windstoßa Töpfascherbn", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON><PERSON> Spawn Ei", "item.minecraft.harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON>z des Meeres", "item.minecraft.heart_pottery_shard": "Herz-Töpfascherbn", "item.minecraft.heart_pottery_sherd": "Herz-Töpfascherbn", "item.minecraft.heartbreak_pottery_shard": "Herzbruch-Töpfascherbn", "item.minecraft.heartbreak_pottery_sherd": "Herzbruch-Töpfascherbn", "item.minecraft.hoglin_spawn_egg": "Hoglin-Spawn-Ei", "item.minecraft.honey_bottle": "Honigfloschn", "item.minecraft.honeycomb": "Honigwobn", "item.minecraft.hopper_minecart": "Wagon mit Trichta", "item.minecraft.horse_spawn_egg": "Roß-Spawn-Ei", "item.minecraft.host_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.host_armor_trim_smithing_template.new": "Wirts-Rüstungssotz", "item.minecraft.howl_pottery_shard": "Schreiate Töpfascherbn", "item.minecraft.howl_pottery_sherd": "Schreiate Töpfascherbn", "item.minecraft.husk_spawn_egg": "Wüstnzombie-Spawn-Ei", "item.minecraft.ink_sac": "Tintnbeitl", "item.minecraft.iron_axe": "Eisnoxt", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "Eisnbrustplottn", "item.minecraft.iron_golem_spawn_egg": "Eisngolem-Spawn-Ei", "item.minecraft.iron_helmet": "Eisnhöm", "item.minecraft.iron_hoe": "Eisnhock", "item.minecraft.iron_horse_armor": "Roßrüstung aus Eisn", "item.minecraft.iron_ingot": "Eisnborrn", "item.minecraft.iron_leggings": "Eisnhaxnschutz", "item.minecraft.iron_nugget": "E<PERSON>nklumpn", "item.minecraft.iron_pickaxe": "Eisnspitzhock", "item.minecraft.iron_shovel": "Eisnschaufe", "item.minecraft.iron_sword": "Eisnschwert", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Tropnhoizboot", "item.minecraft.jungle_chest_boat": "Tropnhoizboot mit Kistn", "item.minecraft.knowledge_book": "Buach des Wissns", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Lavakiwe", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "Ledastiefe", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "Ledakoppn", "item.minecraft.leather_horse_armor": "Roßrüstung aus Leda", "item.minecraft.leather_leggings": "Ledahos<PERSON>", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_dye": "Hellblaue Forb", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "<PERSON>ög<PERSON><PERSON>", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "Höllgrü<PERSON> G<PERSON>irr", "item.minecraft.lingering_potion": "Vaweiltronk", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON> bra<PERSON>", "item.minecraft.lingering_potion.effect.fire_resistance": "Vaweiltronk der Feiaresistenz", "item.minecraft.lingering_potion.effect.harming": "Vaweiltronk des Schodns", "item.minecraft.lingering_potion.effect.healing": "Vaweiltronk der Heilung", "item.minecraft.lingering_potion.effect.infested": "Vaweiltronk des Befolls", "item.minecraft.lingering_potion.effect.invisibility": "Vaweiltronk der Unsichtborkeit", "item.minecraft.lingering_potion.effect.leaping": "Vaweiltronk der Sprungkroft", "item.minecraft.lingering_potion.effect.levitation": "Vaweiltronk der Schwebekroft", "item.minecraft.lingering_potion.effect.luck": "Vaweiltronk des Glücks", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "Vaweiltronk der Nochtsicht", "item.minecraft.lingering_potion.effect.oozing": "Vaweiltronk des Schleimens", "item.minecraft.lingering_potion.effect.poison": "Vaweiltronk der Vagiftung", "item.minecraft.lingering_potion.effect.regeneration": "Vaweiltronk der Regeneration", "item.minecraft.lingering_potion.effect.slow_falling": "Vaweiltronk des sonftn Fois", "item.minecraft.lingering_potion.effect.slowness": "Vaweiltronk der Longsomkeit", "item.minecraft.lingering_potion.effect.strength": "Vaweiltronk der Stärke", "item.minecraft.lingering_potion.effect.swiftness": "Vaweiltronk der Schnöigkeit", "item.minecraft.lingering_potion.effect.thick": "Dick<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Vaweiltronk vom Schüdkrötn-Meista", "item.minecraft.lingering_potion.effect.water": "Vaweilnde Wossaflosch", "item.minecraft.lingering_potion.effect.water_breathing": "Vaweiltronk der Untawossaatmung", "item.minecraft.lingering_potion.effect.weakness": "Vaweiltronk der Schwäche", "item.minecraft.lingering_potion.effect.weaving": "Vaweiltronk des Webns", "item.minecraft.lingering_potion.effect.wind_charged": "Vaweiltronk da Windlodung", "item.minecraft.llama_spawn_egg": "Lama-Spawn-Ei", "item.minecraft.lodestone_compass": "Leitstoa-Kompass", "item.minecraft.mace": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmacreme", "item.minecraft.magma_cube_spawn_egg": "Magmawürlf-Spawn-Ei", "item.minecraft.mangrove_boat": "Mangrovnhoizboot", "item.minecraft.mangrove_chest_boat": "Mangrovnhoizboot mit Kistn", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "Melonenscheibn", "item.minecraft.milk_bucket": "Milchkibl", "item.minecraft.minecart": "Wagon", "item.minecraft.miner_pottery_shard": "Bergorbeita-Töpfascherbn", "item.minecraft.miner_pottery_sherd": "Bergorbeita-Töpfascherbn", "item.minecraft.mojang_banner_pattern": "Ban<PERSON>ust<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Mojang-Logo", "item.minecraft.mojang_banner_pattern.new": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "Mooshroom-Spawn-Ei", "item.minecraft.mourner_pottery_shard": "Sumsa-Töpfascherbn", "item.minecraft.mourner_pottery_sherd": "Sumsa-Töpfascherbn", "item.minecraft.mule_spawn_egg": "Maultier-Spawn-Ei", "item.minecraft.mushroom_stew": "Püzsuppn", "item.minecraft.music_disc_11": "Schoiplottn", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Schoiplottn", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Schoiplottn", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Schoiplottn", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Schoiplottn", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Schoiplottn", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Schoiplottn", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Schoiplottn", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (Plottnspiela)", "item.minecraft.music_disc_far": "Schoiplottn", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Schollplottn", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - <PERSON><PERSON>", "item.minecraft.music_disc_mall": "Schoiplottn", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Schoiplottn", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Schoiplottn", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Schoiplottn", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Schoiplottn", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Schoiplottn", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Schoiplottn", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Schoiplottn", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Schoiplottn", "item.minecraft.music_disc_tears.desc": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait": "Schoiplottn", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Schoiplottn", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Ruachs Hommefleisch", "item.minecraft.name_tag": "Nomnsschüd", "item.minecraft.nautilus_shell": "Natilusschö", "item.minecraft.nether_brick": "Netherziagl", "item.minecraft.nether_star": "Netherstern", "item.minecraft.nether_wart": "<PERSON><PERSON>", "item.minecraft.netherite_axe": "Netheritoxt", "item.minecraft.netherite_boots": "Netheritstiefe", "item.minecraft.netherite_chestplate": "Netheritbrustplottn", "item.minecraft.netherite_helmet": "Netherithöm", "item.minecraft.netherite_hoe": "Netherithock", "item.minecraft.netherite_ingot": "Netheritborrn", "item.minecraft.netherite_leggings": "Netherithaxnschutz", "item.minecraft.netherite_pickaxe": "Netheritspitzhock", "item.minecraft.netherite_scrap": "Netheritbrockn", "item.minecraft.netherite_shovel": "Netheritschaufe", "item.minecraft.netherite_sword": "Netheritschwert", "item.minecraft.netherite_upgrade_smithing_template": "Schmiedevorlog", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite V<PERSON>rung", "item.minecraft.oak_boat": "Oachnhoizboot", "item.minecraft.oak_chest_boat": "Oachnhoizboot mit Kistn", "item.minecraft.ocelot_spawn_egg": "Ozelot-Spawn-Ei", "item.minecraft.ominous_bottle": "Unheilvolle Floschn", "item.minecraft.ominous_trial_key": "Unheilvolla Prüfungschlüssl", "item.minecraft.orange_bundle": "<PERSON><PERSON>", "item.minecraft.orange_dye": "Orange Forb", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "<PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Blosseichnhoizboot", "item.minecraft.pale_oak_chest_boat": "Blosseichnhoizboot mit Kistn", "item.minecraft.panda_spawn_egg": "Panda-Spawn-Ei", "item.minecraft.paper": "Papia", "item.minecraft.parrot_spawn_egg": "Popagei-Spawn-Ei", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Phantom-Spawn-Ei", "item.minecraft.pig_spawn_egg": "Schwein-Spawn-Ei", "item.minecraft.piglin_banner_pattern": "Ban<PERSON>ust<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "Schnauzn", "item.minecraft.piglin_banner_pattern.new": "Schnauzn-Bannervualog", "item.minecraft.piglin_brute_spawn_egg": "Piglin-Barbar-Spawn-Ei", "item.minecraft.piglin_spawn_egg": "Piglin-Spawn-Ei", "item.minecraft.pillager_spawn_egg": "Plündara-Spawn-Ei", "item.minecraft.pink_bundle": "<PERSON><PERSON>", "item.minecraft.pink_dye": "<PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON><PERSON>", "item.minecraft.pitcher_plant": "Kannenpflanze", "item.minecraft.pitcher_pod": "Kannenpflanzenkapsel", "item.minecraft.plenty_pottery_shard": "Schmattige Töpfascherbn", "item.minecraft.plenty_pottery_sherd": "Schmattige Töpfascherbn", "item.minecraft.poisonous_potato": "<PERSON><PERSON> Ka<PERSON>offe", "item.minecraft.polar_bear_spawn_egg": "Eisbär-Spawn-Ei", "item.minecraft.popped_chorus_fruit": "Platzte Chorusfrucht", "item.minecraft.porkchop": "Ruachs Schweinefleisch", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "Tronk", "item.minecraft.potion.effect.awkward": "Komischa Tronk", "item.minecraft.potion.effect.empty": "<PERSON> braubora Tronk", "item.minecraft.potion.effect.fire_resistance": "Tronk der Feiaresistenz", "item.minecraft.potion.effect.harming": "Tronk des Schodns", "item.minecraft.potion.effect.healing": "Tronk der Heilung", "item.minecraft.potion.effect.infested": "Tronk des Befolls", "item.minecraft.potion.effect.invisibility": "Tronk der Unsichtborkeit", "item.minecraft.potion.effect.leaping": "Tronk der Sprungkroft", "item.minecraft.potion.effect.levitation": "Tronk der Schwebekroft", "item.minecraft.potion.effect.luck": "Tronk des Glücks", "item.minecraft.potion.effect.mundane": "Normala Tronk", "item.minecraft.potion.effect.night_vision": "Nochtsichttronk", "item.minecraft.potion.effect.oozing": "Tronk des Schleimens", "item.minecraft.potion.effect.poison": "Gifttronk", "item.minecraft.potion.effect.regeneration": "Tronk der Regeneration", "item.minecraft.potion.effect.slow_falling": "Tronk des sonftn Fois", "item.minecraft.potion.effect.slowness": "Tronk der Longsomkeit", "item.minecraft.potion.effect.strength": "Tronk der Stärke", "item.minecraft.potion.effect.swiftness": "Tronk der Schnöigkeit", "item.minecraft.potion.effect.thick": "Dickflüssiga Tronk", "item.minecraft.potion.effect.turtle_master": "Tronk vom Schüdkrötn-Meista", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "Tronk der Untawossaatmung", "item.minecraft.potion.effect.weakness": "Tronk der Schwäche", "item.minecraft.potion.effect.weaving": "Tronk des Webns", "item.minecraft.potion.effect.wind_charged": "Tronk da Windlodung", "item.minecraft.pottery_shard_archer": "Schützn-Töpfascherbn", "item.minecraft.pottery_shard_arms_up": "Gebärdn-Töpfascherbn", "item.minecraft.pottery_shard_prize": "Juweln-Töpfascherbn", "item.minecraft.pottery_shard_skull": "Schädl-Töpfascherbn", "item.minecraft.powder_snow_bucket": "Puivaschneekiwe", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Prismarinscherbe", "item.minecraft.prize_pottery_shard": "Juweln-Töpfascherbn", "item.minecraft.prize_pottery_sherd": "Juweln-Töpfascherbn", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "Kuglfischkiwe", "item.minecraft.pufferfish_spawn_egg": "Kuglfisch-Spawn-Ei", "item.minecraft.pumpkin_pie": "Kürbiskuachn", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "<PERSON><PERSON>", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "Hosnpfotn", "item.minecraft.rabbit_hide": "Hosnfö", "item.minecraft.rabbit_spawn_egg": "Hos-Spawn-Ei", "item.minecraft.rabbit_stew": "Hosnragout", "item.minecraft.raiser_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.raiser_armor_trim_smithing_template.new": "Aufziaga-Rüstungssotz", "item.minecraft.ravager_spawn_egg": "Vawüsta-Spawn-Ei", "item.minecraft.raw_copper": "Ruachs Kupfa", "item.minecraft.raw_gold": "Ruachs Goid", "item.minecraft.raw_iron": "Ruachs Eisn", "item.minecraft.recovery_compass": "Rettungskompass", "item.minecraft.red_bundle": "<PERSON><PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON>r", "item.minecraft.redstone": "Redstone-Stab", "item.minecraft.resin_brick": "Hoazziagl", "item.minecraft.resin_clump": "Hoazpatzn", "item.minecraft.rib_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.rib_armor_trim_smithing_template.new": "Rippn-Rüstungssotz", "item.minecraft.rotten_flesh": "Varottets Fleisch", "item.minecraft.saddle": "Sottl", "item.minecraft.salmon": "Ruacha Lachs", "item.minecraft.salmon_bucket": "Lachskiwe", "item.minecraft.salmon_spawn_egg": "Lachs-Spawn-Ei", "item.minecraft.scrape_pottery_sherd": "Obkrotz Töpfascherbn", "item.minecraft.scute": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.sentry_armor_trim_smithing_template.new": "Wochn-Rüstungssotz", "item.minecraft.shaper_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.shaper_armor_trim_smithing_template.new": "Künstla-Rüstungssotz", "item.minecraft.sheaf_pottery_shard": "Köcher-Töpfascherbn", "item.minecraft.sheaf_pottery_sherd": "Köcher-Töpfascherbn", "item.minecraft.shears": "<PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Schafe-Spawn-Ei", "item.minecraft.shelter_pottery_shard": "Heisl-Töpfascherbn", "item.minecraft.shelter_pottery_sherd": "Heisl-Töpfascherbn", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.blue": "<PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON>", "item.minecraft.shield.cyan": "Türkises <PERSON>ü<PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON>", "item.minecraft.shield.purple": "<PERSON><PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON>hüd", "item.minecraft.shulker_shell": "Shulkerschö", "item.minecraft.shulker_spawn_egg": "Shulker-Spawn-Ei", "item.minecraft.sign": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.silence_armor_trim_smithing_template.new": "Stille-Rüstungssotz", "item.minecraft.silverfish_spawn_egg": "Sübafisch-Spawn-Ei", "item.minecraft.skeleton_horse_spawn_egg": "Skelettroß-Spawn-Ei", "item.minecraft.skeleton_spawn_egg": "Skelett-Spawn-Ei", "item.minecraft.skull_banner_pattern": "Ban<PERSON>ust<PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Totnkopf--Bannervualog", "item.minecraft.skull_pottery_shard": "Schädl-Töpfascherbn", "item.minecraft.skull_pottery_sherd": "Schädl-Töpfascherbn", "item.minecraft.slime_ball": "Schleimboi", "item.minecraft.slime_spawn_egg": "Schleim-Spawn-Ei", "item.minecraft.smithing_template": "Schmiedevorlog", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON><PERSON> auf:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON><PERSON><PERSON> o<PERSON>", "item.minecraft.smithing_template.armor_trim.applies_to": "Rüstung", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Rüstungsteil hinzuafügn", "item.minecraft.smithing_template.armor_trim.ingredients": "Borren & Kristalle", "item.minecraft.smithing_template.ingredients": "Werkstoffe:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON>herit<PERSON><PERSON> hinz<PERSON>", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamantausrüstung", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Rü<PERSON>ungsteil, Werkzeig oda Schwert aus Diamant hinzuafügn", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netheritborrn", "item.minecraft.smithing_template.upgrade": "Aufwertung: ", "item.minecraft.sniffer_spawn_egg": "Schnüffla-Spawn-Ei", "item.minecraft.snort_pottery_shard": "Rotza-Töpfascherbn", "item.minecraft.snort_pottery_sherd": "Rotza-Töpfascherbn", "item.minecraft.snout_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.snout_armor_trim_smithing_template.new": "Schnauzn-Rüstungssotz", "item.minecraft.snow_golem_spawn_egg": "Schneegolem-Spawn-Ei", "item.minecraft.snowball": "Schneeboi", "item.minecraft.spectral_arrow": "Spektralpfeil", "item.minecraft.spider_eye": "Spinnenaug", "item.minecraft.spider_spawn_egg": "Spinnen-Spawn-Ei", "item.minecraft.spire_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.spire_armor_trim_smithing_template.new": "Turmspitzn-Rüstungssotz", "item.minecraft.splash_potion": "Wurftronk", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON> braub<PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "Wurftronk der Feiaresistenz", "item.minecraft.splash_potion.effect.harming": "Wurftronk des Schodns", "item.minecraft.splash_potion.effect.healing": "Wurftronk der Heilung", "item.minecraft.splash_potion.effect.infested": "Wuaftronk des Befolls", "item.minecraft.splash_potion.effect.invisibility": "Wurftronk der Unsichtborkeit", "item.minecraft.splash_potion.effect.leaping": "Wurftronk der Sprungkroft", "item.minecraft.splash_potion.effect.levitation": "Wurftronk der Schwebekroft", "item.minecraft.splash_potion.effect.luck": "Wurftronk des Glücks", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.night_vision": "Wurftronk der Nochtsicht", "item.minecraft.splash_potion.effect.oozing": "Wuaftronk des Schleimens", "item.minecraft.splash_potion.effect.poison": "Wurftronk der Vagiftung", "item.minecraft.splash_potion.effect.regeneration": "Wurftronk der Regeneration", "item.minecraft.splash_potion.effect.slow_falling": "Wurftronk des sonftn Fois", "item.minecraft.splash_potion.effect.slowness": "Wurftronk der Longsomkeit", "item.minecraft.splash_potion.effect.strength": "Wurftronk der Stärke", "item.minecraft.splash_potion.effect.swiftness": "Wurftronk der Schöigkeit", "item.minecraft.splash_potion.effect.thick": "Dick<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Wurftronk vom Schüdkrötn-Meista", "item.minecraft.splash_potion.effect.water": "Werf<PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "Wurftronk der Untawossaatmung", "item.minecraft.splash_potion.effect.weakness": "Wurftronk der Schwäche", "item.minecraft.splash_potion.effect.weaving": "Wuaftronk des Webns", "item.minecraft.splash_potion.effect.wind_charged": "Wuaftronk da Windlodung", "item.minecraft.spruce_boat": "Fichtnhoizboot", "item.minecraft.spruce_chest_boat": "Fichtnhoizboot mit Kistn", "item.minecraft.spyglass": "Binoggl", "item.minecraft.squid_spawn_egg": "Tintnfisch-Spawn-Ei", "item.minecraft.stick": "Stock", "item.minecraft.stone_axe": "Stoaoxt", "item.minecraft.stone_hoe": "Stoahock", "item.minecraft.stone_pickaxe": "St<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "St<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "Stoaschwert", "item.minecraft.stray_spawn_egg": "Eiswondara-Spawn-Ei", "item.minecraft.strider_spawn_egg": "Schreita-Spawn-Ei", "item.minecraft.string": "Fodn", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Vadächtige Suppn", "item.minecraft.sweet_berries": "Süßbeern", "item.minecraft.tadpole_bucket": "Ka<PERSON><PERSON>ppnkiwe", "item.minecraft.tadpole_spawn_egg": "Kaulquappn-Spawn-Ei", "item.minecraft.tide_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.tide_armor_trim_smithing_template.new": "Gezeitn-Rüstungssotz", "item.minecraft.tipped_arrow": "Getränkta Pfeil", "item.minecraft.tipped_arrow.effect.awkward": "Getränkta Pfeil", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON><PERSON> getränkta Pfeil", "item.minecraft.tipped_arrow.effect.fire_resistance": "Pfeil der Feiaresistenz", "item.minecraft.tipped_arrow.effect.harming": "Pfeil des Schodens", "item.minecraft.tipped_arrow.effect.healing": "Pfeil der Heilung", "item.minecraft.tipped_arrow.effect.infested": "<PERSON><PERSON><PERSON> des Befolls", "item.minecraft.tipped_arrow.effect.invisibility": "Pfeil der Unsichtborkeit", "item.minecraft.tipped_arrow.effect.leaping": "Pfeil der Sprungkroft", "item.minecraft.tipped_arrow.effect.levitation": "Pfeil der Schwebekroft", "item.minecraft.tipped_arrow.effect.luck": "Pfeil des Glücks", "item.minecraft.tipped_arrow.effect.mundane": "Gertänkta Pfeil", "item.minecraft.tipped_arrow.effect.night_vision": "Pfeil der Nochtsicht", "item.minecraft.tipped_arrow.effect.oozing": "Pfeil des Schleimens", "item.minecraft.tipped_arrow.effect.poison": "Pfeil der Vagiftung", "item.minecraft.tipped_arrow.effect.regeneration": "Pfeil der Regeneration", "item.minecraft.tipped_arrow.effect.slow_falling": "Pfeil des sonftn Fois", "item.minecraft.tipped_arrow.effect.slowness": "Pfeil der Longsomkeit", "item.minecraft.tipped_arrow.effect.strength": "Pfeil der Stärke", "item.minecraft.tipped_arrow.effect.swiftness": "Pfeil der Schöigkeit", "item.minecraft.tipped_arrow.effect.thick": "Getränkta Pfeil", "item.minecraft.tipped_arrow.effect.turtle_master": "Pfeil vom Schüdkrötn-Meista", "item.minecraft.tipped_arrow.effect.water": "Nossa Pfeil", "item.minecraft.tipped_arrow.effect.water_breathing": "Pfeil der Untawossaatmung", "item.minecraft.tipped_arrow.effect.weakness": "Pfeil der Schwäche", "item.minecraft.tipped_arrow.effect.weaving": "Pfeil des Webns", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON><PERSON>", "item.minecraft.tnt_minecart": "Wagon mit TNT", "item.minecraft.torchflower_seeds": "Fackelliliensamen", "item.minecraft.totem_of_undying": "Totem dea Unsterblichkeit", "item.minecraft.trader_llama_spawn_egg": "Händlalama-Spawn-Ei", "item.minecraft.trial_key": "Prüfungsschlüssl", "item.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "Tropnfisch", "item.minecraft.tropical_fish_bucket": "Tropnfischkiwe", "item.minecraft.tropical_fish_spawn_egg": "Tropnfisch-Spawn-Ei", "item.minecraft.turtle_helmet": "Schüdkrötnponza", "item.minecraft.turtle_scute": "Schüdkrötn Hornschüd", "item.minecraft.turtle_spawn_egg": "Schüdkrötn-Spawn-Ei", "item.minecraft.vex_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.vex_armor_trim_smithing_template.new": "Plogegeista-Rüstungssotz", "item.minecraft.vex_spawn_egg": "Plogegeist-Spawn-Ei", "item.minecraft.villager_spawn_egg": "Dorfbewohna-Spawn-Ei", "item.minecraft.vindicator_spawn_egg": "Diena-Spawn-Ei", "item.minecraft.wandering_trader_spawn_egg": "Fohrenda-Händla-Spawn-Ei", "item.minecraft.ward_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.ward_armor_trim_smithing_template.new": "Wärta-Rüstungssotz", "item.minecraft.warden_spawn_egg": "Wächta-Spawn-Ei", "item.minecraft.warped_fungus_on_a_stick": "W<PERSON>rpü<PERSON>l", "item.minecraft.water_bucket": "Wossakiwe", "item.minecraft.wayfinder_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wegfinda-Rüstungssotz", "item.minecraft.wheat": "Weizn", "item.minecraft.wheat_seeds": "Weiznkern", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON> Sackerl", "item.minecraft.white_dye": "Weiße Forb", "item.minecraft.white_harness": "Weißes Gschirr", "item.minecraft.wild_armor_trim_smithing_template": "Schmiedevorlog", "item.minecraft.wild_armor_trim_smithing_template.new": "Wüldniss-Rüstungssotz", "item.minecraft.wind_charge": "Windkugl", "item.minecraft.witch_spawn_egg": "Hexn-Spawn-Ei", "item.minecraft.wither_skeleton_spawn_egg": "Witherskelett-Spawn-Ei", "item.minecraft.wither_spawn_egg": "Wither-Spawn-Ei", "item.minecraft.wolf_armor": "Woifsrüstung", "item.minecraft.wolf_spawn_egg": "Woif-Spawn-Ei", "item.minecraft.wooden_axe": "Hoizoxt", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON>sch<PERSON><PERSON>", "item.minecraft.wooden_sword": "Hoizschwert", "item.minecraft.writable_book": "Buach und Feda", "item.minecraft.written_book": "Beschriebns Buach", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.yellow_dye": "Gelbe <PERSON>b", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "Zoglin-Spawn-Ei", "item.minecraft.zombie_horse_spawn_egg": "Zombieroß-Spawn-Ei", "item.minecraft.zombie_spawn_egg": "Zombie-Spawn-Ei", "item.minecraft.zombie_villager_spawn_egg": "Dorfbewohnazombie-Spawn-Ei", "item.minecraft.zombified_piglin_spawn_egg": "Zombifiziata-Piglin-Spawn-Ei", "item.modifiers.any": "Ausgrüstet:", "item.modifiers.armor": "Onglegt:", "item.modifiers.body": "Onglegt:", "item.modifiers.chest": "Am <PERSON>:", "item.modifiers.feet": "<PERSON> de <PERSON>ß:", "item.modifiers.hand": "Gehoitn:", "item.modifiers.head": "<PERSON>:", "item.modifiers.legs": "<PERSON> de Ha<PERSON>n:", "item.modifiers.mainhand": "In da Haupthond:", "item.modifiers.offhand": "In da Zwoathond:", "item.modifiers.saddle": "Gsottlt:", "item.nbt_tags": "NBT: %s Eignschoft(n)", "item.op_block_warning.line1": "Ochtung:", "item.op_block_warning.line2": "Den Block zu platzian kennt an Befehl ausfian", "item.op_block_warning.line3": "Verwend des ned, außer du weißt wos drin ist!", "item.unbreakable": "Unzastörbor", "itemGroup.buildingBlocks": "Baumaterial", "itemGroup.coloredBlocks": "Gfeab<PERSON>", "itemGroup.combat": "Kompf", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Hondweak", "itemGroup.foodAndDrink": "Essn & Trinkn", "itemGroup.functional": "Funktionale Blöcke", "itemGroup.hotbar": "Gespeicherte Schnellzugriffsleistn", "itemGroup.ingredients": "Werkstoffe", "itemGroup.inventory": "Inventar", "itemGroup.natural": "<PERSON><PERSON>", "itemGroup.op": "Operator <PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.redstone": "Redstone Blöcke", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "Spawn-Eia", "itemGroup.tools": "Tools & Hüfsmittl", "item_modifier.unknown": "Unbekonnta Gegnstondsmodifikator: %s", "jigsaw_block.final_state": "Wead zu:", "jigsaw_block.generate": "Generian", "jigsaw_block.joint.aligned": "Ausgrichtet", "jigsaw_block.joint.rollable": "Drehboar", "jigsaw_block.joint_label": "Vabindung:", "jigsaw_block.keep_jigsaws": "Veabindungn beibe<PERSON>n", "jigsaw_block.levels": "Stufn: %s", "jigsaw_block.name": "Nom:", "jigsaw_block.placement_priority": "Platzierungspriorität:", "jigsaw_block.placement_priority.tooltip": "Wenn si dea Vabundblock mit an Stuck varbint, ist des de Reihnfolg, in der des Stuck auf Vabindungen zua Gesomtkonstruktion vaorbeitet wead.\n\n<PERSON>e wean in obsteigender Priorität vaorbeitet, wobei Gleichständ durch de Eifügungsreihenfolg gelöst wean.", "jigsaw_block.pool": "Bezugsquelle:", "jigsaw_block.selection_priority": "Auswoipriorität:", "jigsaw_block.selection_priority.tooltip": "<PERSON><PERSON> as zu<PERSON><PERSON><PERSON><PERSON> Stuck auf Vabindungen vaorbeitet wead, i des die Reihenfolg, in der dea Vabundblock vasuacht, sich mit seinem Zülstuck zu vabinde.\n\nVerbind wean in osteigenda Priorität vaorbeitet, wobei Gleichständ durch a zuafällign Reihenfolg gelöst wean.", "jigsaw_block.target": "Zünom:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - C<PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - C<PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - <PERSON><PERSON>", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Fortschritte", "key.attack": "Ongreifn/O<PERSON>un", "key.back": "Rück<PERSON><PERSON><PERSON>", "key.categories.creative": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.gameplay": "Spümechanik", "key.categories.inventory": "Inventar", "key.categories.misc": "Vaschiedns", "key.categories.movement": "Bewegung", "key.categories.multiplayer": "Multiplayer", "key.categories.ui": "Spüsteiarung", "key.chat": "Cha<PERSON>", "key.command": "Befehlszeile aufmochn", "key.drop": "G<PERSON>nstond foin lossn", "key.forward": "Vorwärts", "key.fullscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.hotbar.1": "Schnöizuagriff 1", "key.hotbar.2": "Schnöizuagriff 2", "key.hotbar.3": "Schnöizuagriff 3", "key.hotbar.4": "Schnöizuagriff 4", "key.hotbar.5": "Schnöizuagriff 5", "key.hotbar.6": "Schnöizuagriff 6", "key.hotbar.7": "Schnöizuagriff 7", "key.hotbar.8": "Schnöizuagriff 8", "key.hotbar.9": "Schnöizuagriff 9", "key.inventory": "Inventar aufmochn/schliaßn", "key.jump": "<PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Rücktastn", "key.keyboard.caps.lock": "Feststöhtastn", "key.keyboard.comma": ",", "key.keyboard.delete": "Entf", "key.keyboard.down": "Untn", "key.keyboard.end": "<PERSON><PERSON>", "key.keyboard.enter": "Eingob", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Pos 1", "key.keyboard.insert": "Einfg", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "<PERSON><PERSON> ,", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "Links", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Strg links", "key.keyboard.left.shift": "Umschoit links", "key.keyboard.left.win": "Meta links", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "<PERSON><PERSON>", "key.keyboard.page.down": "<PERSON>ild obi", "key.keyboard.page.up": "<PERSON><PERSON><PERSON> aufi", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON>n", "key.keyboard.right": "<PERSON><PERSON><PERSON>", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON>rg rechts", "key.keyboard.right.shift": "Umschoit rechts", "key.keyboard.right.win": "<PERSON><PERSON> rechts", "key.keyboard.scroll.lock": "Rolln", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON>rta<PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON> be<PERSON>", "key.keyboard.up": "Obn", "key.keyboard.world.1": "Wöd 1", "key.keyboard.world.2": "Wöd 2", "key.left": "Links", "key.loadToolbarActivator": "Schnöllzugriffsleisten lodn", "key.mouse": "Taste %1$s", "key.mouse.left": "<PERSON>e Maustastn", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "Rechtsklick", "key.pickItem": "Block auswöhn", "key.playerlist": "<PERSON><PERSON><PERSON> auf<PERSON>n", "key.quickActions": "Schnö Aktionan", "key.right": "<PERSON><PERSON><PERSON>", "key.saveToolbarActivator": "Schnöllzugriffsleistn speichern", "key.screenshot": "Screenshot", "key.smoothCamera": "Kameravahoitn wechsln", "key.sneak": "Sc<PERSON><PERSON><PERSON>n", "key.socialInteractions": "Interaktions-Fensta", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> (Zuaschaua)", "key.sprint": "Sprintn", "key.swapOffhand": "Gegnstond mit Zwoathond tauschn", "key.togglePerspective": "Perspektive wechsln", "key.use": "<PERSON><PERSON><PERSON>/<PERSON>", "known_server_link.announcements": "Onkündigungen", "known_server_link.community": "Gemeinschoft", "known_server_link.community_guidelines": "Gemeinschoftsregeln", "known_server_link.feedback": "Rückmeldung", "known_server_link.forums": "<PERSON>en", "known_server_link.news": "Neuigkeitn", "known_server_link.report_bug": "Host an <PERSON><PERSON><PERSON> beim <PERSON>?", "known_server_link.status": "Status", "known_server_link.support": "Hilfe/Untastützung", "known_server_link.website": "Website", "lanServer.otherPlayers": "Eistellungen fia ondare Spiela", "lanServer.port": "Portnumma", "lanServer.port.invalid": "Ka gültiga Port.\nLoss des Eingobeföd lea oda gib a Zohl zwischn 1024 und 65535 ein.", "lanServer.port.invalid.new": "Kein gültiger Port.\nLass das Eingabefeld leer oder gib eine Nummer zwischen %s und %s ein.", "lanServer.port.unavailable": "Port nit vafügboa.\nLoss des Eingobeföd lea oda gib a ondare Zohl zwischn 1024 und 65535 ein.", "lanServer.port.unavailable.new": "Port nicht verfügbar.\nLass das Eingabefeld leer oder gib eine andere Nummer zwischen %s und %s ein.", "lanServer.scanning": "Suach noch Spiele in deim lokaln Netzwerk", "lanServer.start": "LAN-<PERSON><PERSON><PERSON>n", "lanServer.title": "LAN-Wöd", "language.code": "bar_AT", "language.name": "<PERSON><PERSON><PERSON>", "language.region": "Österreich", "lectern.take_book": "<PERSON>uch nehma", "loading.progress": "%s %%", "mco.account.privacy.info": "<PERSON>rfohr mehr üba Mojang und Datnschutzgsetze", "mco.account.privacy.info.button": "Erfoah mehr üba DSGVO", "mco.account.privacy.information": "Mojang wendet bestimmte Verfohren an, um Kinda und deren Privatsphäre zu schützn; so wean es „G<PERSON>tz zum Schutz da Privatsphäre vo Kindern im Internet“ (COPPA) und de Datenschutz‐Grundverordnung (DSGVO) eingeholtn.\n\nMöglicherweise musst du de Zustimmung deina Eltern eiholn, bevor du auf dei Realms‐Konto zuagreifn kannst.", "mco.account.privacyinfo": "Mojang wendet bestimmte Verfohren on, um Kinda und deren Privatsphäre zu schützn. So wean unta ondarem as \"<PERSON><PERSON><PERSON> zum Schutz der Privatsphäre vo Kinda im Intanet\" (COPPA) und de Datnschutz-Grundvaordnung (DSGVO) eighoitn.\n\nMöglichaweise benötigst du de Zuastimmung vo deine Ötan, um auf dei Realms-Konto zuazugreifn.\n\nWenn du a ötas Minecraft-Konto host (du mödst di mit deim Benutzanom on), muast du des in a Mojang-Konto umwondln, um auf Realms zuagreifn zu kina.", "mco.account.update": "Konto aktualisian", "mco.activity.noactivity": "Keine Aktivität in dem/den letzten %s Tag(en)", "mco.activity.title": "Spiela aktivität", "mco.backup.button.download": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.reset": "<PERSON><PERSON>d zruggsetzn", "mco.backup.button.restore": "Wiedaheastön", "mco.backup.button.upload": "<PERSON><PERSON><PERSON>", "mco.backup.changes.tooltip": "Ändaungen", "mco.backup.entry": "Sicharung (%s)", "mco.backup.entry.description": "Beschreibung", "mco.backup.entry.enabledPack": "Aktivierts Packet", "mco.backup.entry.gameDifficulty": "Spüschwierigkeit", "mco.backup.entry.gameMode": "Spümodus", "mco.backup.entry.gameServerVersion": "Spielserverversion", "mco.backup.entry.name": "Nom", "mco.backup.entry.seed": "Startwert", "mco.backup.entry.templateName": "Vorlognom", "mco.backup.entry.undefined": "Unbstimmte Ändarung", "mco.backup.entry.uploaded": "Hochglodn", "mco.backup.entry.worldType": "W<PERSON>dtyp", "mco.backup.generate.world": "<PERSON><PERSON><PERSON> generian", "mco.backup.info.title": "Vaändrunga gegaüba vo da letschta sicharung", "mco.backup.narration": "<PERSON><PERSON><PERSON> von %s", "mco.backup.nobackups": "Des Realm hot derzeit koane <PERSON>.", "mco.backup.restoring": "<PERSON><PERSON> wiedah<PERSON>ön", "mco.backup.unknown": "UNBEKONNT", "mco.brokenworld.download": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "Obaglodn", "mco.brokenworld.message.line1": "<PERSON><PERSON> setz de Wöd z<PERSON>g oda wöh a ondare aus.", "mco.brokenworld.message.line2": "Du kust de Wöd a fian Oanzlspielamodus obalodn.", "mco.brokenworld.minigame.title": "Des Minispü wead nimma untastützt", "mco.brokenworld.nonowner.error": "<PERSON>te wort auf's Zruggsetz<PERSON> von da Wöd durch'n Realm-Besitza", "mco.brokenworld.nonowner.title": "<PERSON> is vaoitet", "mco.brokenworld.play": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Zrucksetzn", "mco.brokenworld.title": "Dei aktuelle Wöd wead nimma untastützt", "mco.client.incompatible.msg.line1": "<PERSON><PERSON> is ned mit Realms kompatibl.", "mco.client.incompatible.msg.line2": "Bitte nimm de neieste Version von Minecraft hea.", "mco.client.incompatible.msg.line3": "Realms funktionian ned mit Entwicklungsversionen.", "mco.client.incompatible.title": "Inkompatibla Client!", "mco.client.outdated.stable.version": "Dei Version (%s) ist a bissi z`oit und ned mit Realms kompatibel.\n\nBitte verwende die neichste Version vo Minecraft.", "mco.client.unsupported.snapshot.version": "Dei Version (%s) is ned mit Realms kompatibel.\n\nRealms is für diese Entwicklungsversion ned verfügbar.", "mco.compatibility.downgrade": "Hearab Stuafen", "mco.compatibility.downgrade.description": "<PERSON> is as letz<PERSON> in da Version %s gspüt wordn; du bist jetz auf da Version %s. De Wöd mit ana ondan Version zum Spün ku zu ana Beschädigung von de Datn führn - mia kinan ned garantier<PERSON>, dass de Wöd lodt oda richtig geht. \n\nWenn du des etz trotzdem toa wüst, donn moch bitte a Sicherheitskopie.", "mco.compatibility.incompatible.popup.title": "Ned übereinstimmende Version", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON> Wöd, die du betreten wüst, ist mit deiner aktuellen Version ned kompatibel.", "mco.compatibility.incompatible.series.popup.message": "Diese Wöd wurde zu<PERSON>t in der Version %s gspüt; du bist aud da Version %s.\n\nDe Versionsstände sand ned miteinander kompatibel. Um in de vorliegenden Version zu spünn, wird anne neiche Wö<PERSON>.", "mco.compatibility.unverifiable.message": "<PERSON>s konnte ned festgstöt werden, in wöcher Version de Wöd letztens gspüt worden is, wenn du sie hoch‐ bzw. herabstufst, wird automatisch anne Sicherheitskopie erstöht und unter „Sicherungen“ gspeichert.", "mco.compatibility.unverifiable.title": "Kompatibilität ned überprüfbar", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "Die Wöd wurde letztens in da Version %s gspüt; du bist auf da Version %s.\n\n<PERSON>ter „Sicherungen“ wird anne Sicherheitskopie deiner Wöd gspeichert.\n\nBitte setz sie im Bedarfsfall darauf zurück.", "mco.compatibility.upgrade.friend.description": "Die Wöd wurde letztens in da Version %s gspüt; du bist auf da Version %s.\n\n<PERSON>ter „Sicherungen“ wird anne Sicherheitskopie deiner Wöd gspeichert.\n\nBitte setz sie im Bedarfsfall darauf zurück.", "mco.compatibility.upgrade.title": "Mogst du de wöd wiaklich lodn?", "mco.configure.current.minigame": "<PERSON><PERSON>u<PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Spielaaktivitätsonzeige vorübagehend ausgschoitn", "mco.configure.world.backup": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.activity": "Spielaaktivität", "mco.configure.world.buttons.close": "Realm schlia<PERSON>n", "mco.configure.world.buttons.delete": "L<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "<PERSON><PERSON>", "mco.configure.world.buttons.edit": "Einstöllungen", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "Mehr Optionen", "mco.configure.world.buttons.newworld": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.open": "Realm <PERSON>", "mco.configure.world.buttons.options": "Wöd-Optionen", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Region auswöhn...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON>d zruggsetzn", "mco.configure.world.buttons.save": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.settings": "Einstöllungen", "mco.configure.world.buttons.subscription": "Abonnement", "mco.configure.world.buttons.switchminigame": "Minispü wechsln", "mco.configure.world.close.question.line1": "<PERSON>i <PERSON> wead nimma vafüg<PERSON> sein.", "mco.configure.world.close.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.configure.world.close.question.title": "Muast wos Ändan ohne das wea Nervt?", "mco.configure.world.closing": "Realm wead gschlossn ...", "mco.configure.world.commandBlocks": "Befehlsbleck", "mco.configure.world.delete.button": "Realm löschn", "mco.configure.world.delete.question.line1": "Dei Realm wead endgültig glöscht", "mco.configure.world.delete.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.configure.world.description": "Beschreibung vom Realm", "mco.configure.world.edit.slot.name": "<PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "Monche Eistellungen san deaktiviat, da dei aktuelle Wöd a Obnteia is", "mco.configure.world.edit.subscreen.experience": "Monche Eistellungen san deaktiviat, da dei aktuelle Wöd a Erfohrungswöt is", "mco.configure.world.edit.subscreen.inspiration": "Monche Eistellungen san deaktiviat, da dei aktuelle Wöt a Inspirationswöd is", "mco.configure.world.forceGameMode": "Spümo<PERSON> er<PERSON>a", "mco.configure.world.invite.narration": "Du hast %s neue Einladung(en)", "mco.configure.world.invite.profile.name": "Nom", "mco.configure.world.invited": "E<PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Iglada (%s)", "mco.configure.world.invites.normal.tooltip": "Normala Spiela", "mco.configure.world.invites.ops.tooltip": "Operator", "mco.configure.world.invites.remove.tooltip": "Entfernen", "mco.configure.world.leave.question.line1": "<PERSON>n du den <PERSON> valosst, kust du's nimma betretn, bis du wieda eiglodn weast", "mco.configure.world.leave.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.configure.world.loading": "Realm wird gloadn", "mco.configure.world.location": "Ort", "mco.configure.world.minigame": "Grad: %s", "mco.configure.world.name": "Nom vom Realm", "mco.configure.world.opening": "Realm wead aufgmocht ...", "mco.configure.world.players.error": "Es existiat koa Spiela mit dem Nomen", "mco.configure.world.players.inviting": "<PERSON><PERSON><PERSON> wüad iglada...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "Spiela gegn <PERSON> (PvP)", "mco.configure.world.region_preference": "Beforzuagte Region", "mco.configure.world.region_preference.title": "Beforzuagte Region auswöhn", "mco.configure.world.reset.question.line1": "<PERSON><PERSON> Wöd wead nei generiat und dei aktuelle Wöd kimb weg", "mco.configure.world.reset.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.configure.world.resourcepack.question": "Du benötigst a benutzerdefiniertes Ressourcenpaket, um auf dem Realm zu spün.\n\nMechtast du`s da runterloden und damit spün?", "mco.configure.world.resourcepack.question.line1": "Du brauchst a eigns Ressourcenpaket um auf dem Realm spün zu kina", "mco.configure.world.resourcepack.question.line2": "<PERSON><PERSON><PERSON>'s automatisch obalodn und installian?", "mco.configure.world.restore.download.question.line1": "<PERSON> Wöd wead obaglodn und zu deine Oanzlspielawötn hinzuagfüg.", "mco.configure.world.restore.download.question.line2": "Wüst du fortfohrn?", "mco.configure.world.restore.question.line1": "<PERSON><PERSON> wead auf'n Stond vom '%s' (%s) zrugggsetzt", "mco.configure.world.restore.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.configure.world.settings.expired": "Die Einstellungen eines abgelaufenen Realms können nicht bearbeitet werden", "mco.configure.world.settings.title": "Eistellungen", "mco.configure.world.slot": "Wöd %s", "mco.configure.world.slot.empty": "Laar", "mco.configure.world.slot.switch.question.line1": "Dei Realm wead auf a ondare Wöd g<PERSON>chslt", "mco.configure.world.slot.switch.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON>", "mco.configure.world.slot.tooltip.active": "Betretn", "mco.configure.world.slot.tooltip.minigame": "Zu Minispü wechsln", "mco.configure.world.spawnAnimals": "<PERSON><PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Dorfbewohna", "mco.configure.world.spawnProtection": "Spawnpunktschutz", "mco.configure.world.spawn_toggle.message": "Wannst de Option ausschoitst, werden OLLE vorhandanen Objekte dieses Typs ENTFERNT", "mco.configure.world.spawn_toggle.message.npc": "Wannst de Option ausschoitst, werden OLLE vorhandanen Objekte dies<PERSON>, wia Dorfbewohna, ENTFERNT", "mco.configure.world.spawn_toggle.title": "Ochtung!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "<PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON>", "mco.configure.world.subscription.expired": "Obgloffn", "mco.configure.world.subscription.extend": "Abonnement valengan", "mco.configure.world.subscription.less_than_a_day": "Weniga ois an Tog", "mco.configure.world.subscription.month": "<PERSON><PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Automatisch valengat in", "mco.configure.world.subscription.recurring.info": "Änderungen an deinem Realms‐Abonnement, wie die Verlängerung der Laufzeit oder die Deaktivierung der wiederkehrenden Abrechnung, werden erst ab deinem nächsten Abrechnungstermin berücksichtigt.", "mco.configure.world.subscription.remaining.days": "%1$s Täg", "mco.configure.world.subscription.remaining.months": "%1$s Monat(e)", "mco.configure.world.subscription.remaining.months.days": "%1$s Monat(e), %2$s Tag(e)", "mco.configure.world.subscription.start": "Ufong", "mco.configure.world.subscription.tab": "Abonnement", "mco.configure.world.subscription.timeleft": "Übrige Zeit", "mco.configure.world.subscription.title": "Abonnementdetails", "mco.configure.world.subscription.unknown": "Unbekonnt", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot.subtitle": "<PERSON><PERSON> is laar, bitte wöh aus, wie du dei Wöd erstön wüst", "mco.configure.world.title": "Realm eistön:", "mco.configure.world.uninvite.player": "Bisch du sicha, dass du %s <PERSON><PERSON> magsch?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> <PERSON> <PERSON>, dass du den Spiela auslodn wüst:", "mco.configure.worlds.title": "Wötn", "mco.connect.authorizing": "Onmödn...", "mco.connect.connecting": "Vabindung zum Realm wead heagstöt ...", "mco.connect.failed": "Vabindung zum Realm hot ned heagstöt wean kina ...", "mco.connect.region": "Serverregion: %s", "mco.connect.success": "<PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Du muaßt an Nom eigebn!", "mco.create.world.failed": "Wöd hod net erstöllt wean kennan!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON> wead erstöt ...", "mco.create.world.skip": "Übaspringa", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON>, auf wovana Wöd dei neis Realm glodn wean soid (Optional)", "mco.create.world.wait": "Realm wead erstöt ...", "mco.download.cancelled": "<PERSON><PERSON><PERSON><PERSON> obbrochn", "mco.download.confirmation.line1": "<PERSON>, de du obalodn wüst, is greßa ois %s", "mco.download.confirmation.line2": "Du weast de Wöd nimma auf dein Realm hochlodn kina", "mco.download.confirmation.oversized": "<PERSON> Wöd, die du runterloden wüst, ist größer ois %s.\n\nDu wirst de Wöd ned wieder auf dein Realm hochloden kenna", "mco.download.done": "Obalodn obgschlossn", "mco.download.downloading": "<PERSON><PERSON><PERSON><PERSON>", "mco.download.extracting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.failed": "Obalodn fehlgschlogn", "mco.download.percent": "%s %%", "mco.download.preparing": "Obalodn vorbereitn", "mco.download.resourcePack.fail": "Abalada vom Ressourcenpacket fehlgschlaga!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "<PERSON><PERSON><PERSON>", "mco.error.invalid.session.message": "<PERSON><PERSON> vasuach, Minecraft nei zum Startn", "mco.error.invalid.session.title": "Ungültige Sitzung", "mco.errorMessage.6001": "Client zu oit", "mco.errorMessage.6002": "Nutzungsbedingungen ned ugnumma", "mco.errorMessage.6003": "Obalod-<PERSON><PERSON> er<PERSON>", "mco.errorMessage.6004": "Hochlodelimit erreicht", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON> g<PERSON>", "mco.errorMessage.6006": "Wealt isch vaaltet", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON> g<PERSON> zu viela Realms a", "mco.errorMessage.6008": "Ungültiga Realm Name", "mco.errorMessage.6009": "Ungültige Realm beschribung", "mco.errorMessage.connectionFailure": "A Fehla is auftretn, bitte vasuachs spada nuamoi.", "mco.errorMessage.generic": "An Fähla isch uftreata: ", "mco.errorMessage.initialize.failed": "Realm hod net initiallisiert wean kennan", "mco.errorMessage.noDetails": "Ka Föhlabeschreibung vorhondn", "mco.errorMessage.realmsService": "An Fähla isch uftreata (%s):", "mco.errorMessage.realmsService.configurationError": "<PERSON>im <PERSON> der Weltoptionen ist ein unerwarteter Fehler aufgetreten", "mco.errorMessage.realmsService.connectivity": "De Vabindung zum Server hot ned heagstöt weadn kina: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kompatible Version konnt ned überprüft werden kenna; erhaltene Antwort: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON> wiedahoin", "mco.errorMessage.serviceBusy": "De Realm is grode Ausgelasted Versuch es noch paar Minuten nochmol.", "mco.gui.button": "Schoitflächn", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "%s is einglodn woadn", "mco.invites.button.accept": "<PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON><PERSON> offnen Eilodungen!", "mco.invites.pending": "Neue Einladung(en)!", "mco.invites.title": "<PERSON>ne E<PERSON>n", "mco.minigame.world.changeButton": "Onas Minispü auswöhn", "mco.minigame.world.info.line1": "Des wead dei Wöd temporär mit an Minispü austauschn!", "mco.minigame.world.info.line2": "Du kust spada ohne wos zum Val<PERSON> dei oide Wöd zrugghoin.", "mco.minigame.world.noSelection": "<PERSON>te wöh wos aus", "mco.minigame.world.restore": "Minispü wead beendet ...", "mco.minigame.world.restore.question.line1": "Des Minispü wead beendet und dei Realm wiedaheagstöt.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON> da sicha, dass du fortfohrn wüst?", "mco.minigame.world.selected": "Ausgwöhts Minispü:", "mco.minigame.world.slot.screen.title": "Wöd wead gwechslt ...", "mco.minigame.world.startButton": "Wechsln", "mco.minigame.world.starting.screen.title": "Minispü wead gstartet ...", "mco.minigame.world.stopButton": "Minispü beendn", "mco.minigame.world.switch.new": "A onas Minispü auswöhn?", "mco.minigame.world.switch.title": "Minisü wechsln", "mco.minigame.world.title": "Realm zu Minispü wechsln", "mco.news": "Realms‐Neiigkeitn", "mco.notification.dismiss": "Schließen", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON>n", "mco.notification.transferSubscription.message": "Java‐Realms‐Abonnements werden in den Microsoft Store verschoben. Loss dei Abonnement ned auslaufen!\nJetzt übertragogen und 30 Tog Realms kostenlos krigen.\nGehe zum Profil auf minecraft.net, um dein Abonnement zu übertragen.", "mco.notification.visitUrl.buttonText.default": "<PERSON>", "mco.notification.visitUrl.message.default": "Bitte besuche den nachfolgenden Link", "mco.onlinePlayers": "Spüler online", "mco.play.button.realm.closed": "Realm is gschlossn", "mco.question": "<PERSON>", "mco.reset.world.adventure": "Obnteia", "mco.reset.world.experience": "Erfohrungswötn", "mco.reset.world.generate": "<PERSON><PERSON><PERSON>", "mco.reset.world.inspiration": "Inspirationswötn", "mco.reset.world.resetting.screen.title": "Wöd wead zrugggsetzt ...", "mco.reset.world.seed": "Startwert (Optional)", "mco.reset.world.template": "W<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.title": "<PERSON><PERSON><PERSON>", "mco.reset.world.upload": "<PERSON><PERSON><PERSON>", "mco.reset.world.warning": "Des wead de aktuelle Wöd von deim Realm austauschn!", "mco.selectServer.buy": "Kaf a <PERSON>!", "mco.selectServer.close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Gschlossns Realm", "mco.selectServer.closeserver": "Realm sch<PERSON>ßn", "mco.selectServer.configure": "Realm eistön", "mco.selectServer.configureRealm": "Realm onpossn", "mco.selectServer.create": "Realm erstön", "mco.selectServer.create.subtitle": "<PERSON><PERSON><PERSON>, auf wöche Wöd dei neiche Realm glodn wean soid", "mco.selectServer.expired": "Obgloffns Realm", "mco.selectServer.expiredList": "Dei <PERSON> is obgloffn", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Abonnian", "mco.selectServer.expiredTrial": "<PERSON><PERSON>-<PERSON> is obgloffn", "mco.selectServer.expires.day": "La<PERSON>t in an Tog o", "mco.selectServer.expires.days": "Lafft in %s Tog o", "mco.selectServer.expires.soon": "La<PERSON>t boid o", "mco.selectServer.leave": "Realm valossn", "mco.selectServer.loading": "Realm‐Auswoi wird glodn", "mco.selectServer.mapOnlySupportedForVersion": "<PERSON><PERSON> wead in %s ned untastützt", "mco.selectServer.minigame": "Minispü:", "mco.selectServer.minigameName": "Minispü: %s", "mco.selectServer.minigameNotSupportedInVersion": "Des Minispü konn ned mit %s gspüt wean", "mco.selectServer.noRealms": "<PERSON> scheinst kan Realm zu hoben. Füge ein Realm hinzu, um mit deinen Freinden zu spün.", "mco.selectServer.note": "<PERSON><PERSON><PERSON><PERSON>:", "mco.selectServer.open": "Realm aufmochn", "mco.selectServer.openserver": "Realm <PERSON>", "mco.selectServer.play": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms is a sichara und oafocha Weg, Minecraft online mit bis zu 10 Freind gleichzeitig zu spün. Es untastützt vü Minispiele und eigene Wötn! Nua da Besitza muas wos zoin.", "mco.selectServer.purchase": "Realm hinzuafügn", "mco.selectServer.trial": "Probias aus!", "mco.selectServer.uninitialized": "Realm erstön!", "mco.snapshot.createSnapshotPopup.text": "Du bist dabei, a kostenloses Realm für Entwicklungsversionen zu erstön, das mit deinem zahlungspflichtigen Realms‐Abonnement verknüpft wird. Des neiche Entwicklungsversionen‐Realm konn so long genutzt werden, wiea das zahlungspflichtige Abonnement aktiv ist. An deinem bezahlten Realm ändert si dadurch gornix.", "mco.snapshot.createSnapshotPopup.title": "Entwiacklungsversionen‐Realm erstellen?", "mco.snapshot.creating": "Entwicklungsversionen‐Realm wird erstöd…", "mco.snapshot.description": "Verkniapft mit „%s“", "mco.snapshot.friendsRealm.downgrade": "Du brauchast die Version %s, um dem Realm zu betreten", "mco.snapshot.friendsRealm.upgrade": "%s muss das Realm hochstufen, bevor du in de Version drauf spün konnst", "mco.snapshot.paired": "De Entwicklungsversionen‐Realm ist mit „%s“ verknüpft", "mco.snapshot.parent.tooltip": "Verwende de neichste Voiversion, um auf dem Realm zu spün", "mco.snapshot.start": "Kostenloses Entwicklungsversionen‐Realm onlegen", "mco.snapshot.subscription.info": "Dies ist ein Realm für Entwicklungsversionen, des mit dem Abonnement deines Realms „%s“ verknüpft ist. Es bleibt so longe aktiv, wie des damit verknüpfte Realm aktiv ist.", "mco.snapshot.tooltip": "Entwicklungsversionen‐Realms verschaffen dir annen Einblick in zukünftige Versionen von Minecraft, die möglicherweise neue Spielelemente und sonstige Änderungen enthoiten.\n\nDeine normalen Realms kannst du in der aktuellen Voiversion obrufen.", "mco.snapshotRealmsPopup.message": "Realms gibt es jetz a in Entwicklungsversionen, beginnend mit 23w41a. Jedes Realms‐Abonnement wird um ein kostenloses Realm für Entwicklungsversionen ergänzt, des von deinem normalen Java‐Realm getrennt ist!", "mco.snapshotRealmsPopup.title": "Realms jetz is Entwicklungsversionen verfügbar", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON> er<PERSON>arn", "mco.template.button.publisher": "<PERSON><PERSON>", "mco.template.button.select": "Auswöhn", "mco.template.button.trailer": "Vorschau", "mco.template.default.name": "Wödvor<PERSON>", "mco.template.info.tooltip": "Webseite vom Mocha", "mco.template.name": "Vorlog", "mco.template.select.failure": "<PERSON> Listn mit de Inhoite vo dea Kategorie hot ned obgruafn wean kina.\nBitte übaprüf dei Intanetvabindung oda vasuach es spada nuamoi.", "mco.template.select.narrate.authors": "Autoren: %s", "mco.template.select.narrate.version": "Version %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, de Inhoitskategorie scheint aktuell laar zu sein.\n<PERSON>te schau demnächst nuamoi vorbei, oda denk drüber noch,\nwenn du a Erstella bist, %s.", "mco.template.select.none.linkTitle": "sö<PERSON>t an Inhoit einzureichn", "mco.template.title": "W<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.title.minigame": "Minispiele", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.agree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "I nimm de Minecraft Realms", "mco.terms.sentence.2": "Nutzungsbedingungen u", "mco.terms.title": "Realms Nutzungsbedingungen", "mco.time.daysAgo": "Voa %1$s Täg", "mco.time.hoursAgo": "Voa %1$s Stund(a)", "mco.time.minutesAgo": "Voa %1$s Minuta", "mco.time.now": "grad jetz", "mco.time.secondsAgo": "Voa %1$s Sekund(n) ", "mco.trial.message.line1": "Wüst du dei eigns Realm hobn?", "mco.trial.message.line2": "Klick hier fia mehr Infos!", "mco.upload.button.name": "Ho<PERSON>lodn", "mco.upload.cancelled": "Hochlodn obbrochn", "mco.upload.close.failure": "<PERSON>i <PERSON> hot ned gsch<PERSON>n wean kina, bitte vasuachs spada nuamoi", "mco.upload.done": "Hochlodn obgschlossn", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Hochlodn fehlgschlogn! (%s)", "mco.upload.failed.too_big.description": "De ausgwölte Wöd is zgroß. De maximale Greß is %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON><PERSON> is zu groß", "mco.upload.hardcore": "Hardcore-<PERSON><PERSON><PERSON><PERSON> kinan ned hochglodn wean!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON> wead vorbereitet", "mco.upload.select.world.none": "<PERSON>ane <PERSON>lspielawötn gfundn!", "mco.upload.select.world.subtitle": "Bitte wöh a Oanzlspielawöd zum Auffelodn aus", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON>", "mco.upload.size.failure.line1": "'%s' is zu groß!", "mco.upload.size.failure.line2": "Es is %s. De maximal dalabte Greß is %s.", "mco.upload.uploading": "'%s' wead hochglodn", "mco.upload.verifying": "<PERSON><PERSON> wead übaprüft", "mco.version": "Version: %s", "mco.warning": "<PERSON><PERSON>!", "mco.worldSlot.minigame": "Minispiel", "menu.custom_options": "Benutzerdefinierte Optionen …", "menu.custom_options.title": "Benutzerdefinierte Optionen", "menu.custom_options.tooltip": "Hinweis: <PERSON><PERSON><PERSON> defini<PERSON>e Gschichten wean vom Serva und/oda vun am Drittn bereitgstöd. Bas auf!", "menu.custom_screen_info.button_narration": "<PERSON> is a benutzadefiniertes Menü. Druck do dast mea eafost.", "menu.custom_screen_info.contents": "De Inholte von dem Fensta wean von am Serva und Drittn koatn definiert, de Mojang Studios oda Microsoft ned kean, nu von denen betiebn wean \n\nBas auf! Sei voasichtig wonnst auf Links druckst und sog nia deine Persönlichn Daten (wia deine Onmödedaten).\n\nWenn di des Fensta am Spün hindat, kunnst de Verbindung zum Serva a üba da Schoitflechn untn trenna.", "menu.custom_screen_info.disconnect": "<PERSON><PERSON><PERSON>ts Menü obglehtn", "menu.custom_screen_info.title": "<PERSON><PERSON><PERSON><PERSON> zu benutza definiertn <PERSON>ü", "menu.custom_screen_info.tooltip": "<PERSON> is a benutzadefiniertes Menü. Druck do dast mea eafost.", "menu.disconnect": "Server valossn", "menu.feedback": "<PERSON><PERSON><PERSON>ö<PERSON>ng…", "menu.feedback.title": "Rückmödung", "menu.game": "Spümenü", "menu.modded": " (Modifiziat)", "menu.multiplayer": "Me<PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Optionen ...", "menu.paused": "S<PERSON><PERSON> pausiat", "menu.playdemo": "De<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.playerReporting": "<PERSON><PERSON><PERSON> melden", "menu.preparingSpawn": "Wödbereich wead vorbereitet: %s%%", "menu.quick_actions": "Schnellaktionen …", "menu.quick_actions.title": "Schnellaktionen", "menu.quit": "<PERSON><PERSON><PERSON> <PERSON>dn", "menu.reportBugs": "<PERSON><PERSON><PERSON> möden", "menu.resetdemo": "Demo-Wöd zruggsetzn", "menu.returnToGame": "Zrugg zum Spü", "menu.returnToMenu": "S<PERSON><PERSON><PERSON> und zrugg zum Hauptmenü", "menu.savingChunks": "<PERSON><PERSON> wean g<PERSON><PERSON><PERSON>t", "menu.savingLevel": "<PERSON><PERSON><PERSON> wead g<PERSON><PERSON><PERSON>t", "menu.sendFeedback": "Feedback gebn", "menu.server_links": "Server‐Links …", "menu.server_links.title": "Server‐Links", "menu.shareToLan": "Fias LAN aufmochn", "menu.singleplayer": "Oanzlspiela", "menu.working": "Wead beorbeitet ...", "merchant.deprecated": "Dorfbewohna fülln ihrn Wornbestond bis zu zwoamoi am Tog auf.", "merchant.level.1": "<PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "G<PERSON>l", "merchant.level.4": "Expert", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Ongebote", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Druck %1$s zum valossn", "multiplayer.applyingPack": "Ressourcnpaket wiad ongwandt", "multiplayer.confirm_command.parse_errors": "Du wüst an unbekonnt oder foischen Befehl ausführn.\nBist da sicha?\nBefehl: %s", "multiplayer.confirm_command.permissions_required": "Du probiast an <PERSON><PERSON><PERSON><PERSON> au<PERSON>, dea a he<PERSON>re Berechtigung braucht.\nDes kunnt si schlecht aufs Spüh auswiakn.\nBist da sicha? \nBeföh: %s", "multiplayer.confirm_command.title": "Befehlsausführung bestätigen", "multiplayer.disconnect.authservers_down": "De Authentifizierungsserver san zurzeit ned erreichbor. Bitte vasuach es spada nuamoi!", "multiplayer.disconnect.bad_chat_index": "Vapasste oda umgeordnete Chatnochricht vom Server erkonnt", "multiplayer.disconnect.banned": "Du bist auf dem <PERSON> gspat", "multiplayer.disconnect.banned.expiration": "\n<PERSON><PERSON> wead am %s aufghobn", "multiplayer.disconnect.banned.reason": "Du bist auf dem Server gspat wordn.\nGrund: %s", "multiplayer.disconnect.banned_ip.expiration": "\nDei Ban wiad am %s aufghobn", "multiplayer.disconnect.banned_ip.reason": "Dei IP-Adress is auf dem Server gspat wordn.\nGrund: %s", "multiplayer.disconnect.chat_validation_failed": "Fehler bei der Überprüfung der Chatnachricht", "multiplayer.disconnect.duplicate_login": "Du host di mit an zwoatn Client ugmödt", "multiplayer.disconnect.expired_public_key": "Öffentlicher Profilschlüssel abgelaufen. Schaue nach ob dein System synchronisiert ist und probiere dein Spiel neu zu starten.", "multiplayer.disconnect.flying": "Fliagn is auf dem <PERSON> ned da<PERSON>bt", "multiplayer.disconnect.generic": "Vabindung trennt", "multiplayer.disconnect.idling": "Du worst zu long inaktiv!", "multiplayer.disconnect.illegal_characters": "Unzulässige Zeichn im Chat", "multiplayer.disconnect.incompatible": "Inkompatibla Client! Vawend bitte %s", "multiplayer.disconnect.invalid_entity_attacked": "Es is vasuacht wordn, a ungültigs Objekt onzugreifn", "multiplayer.disconnect.invalid_packet": "Server hot a ungültigs Paket gsendet", "multiplayer.disconnect.invalid_player_data": "Ungültige Spielerdoatn", "multiplayer.disconnect.invalid_player_movement": "Ungültigs Paket zua Spielabewegung empfongen", "multiplayer.disconnect.invalid_public_key_signature": "Ungültige Signatur für den öffentlichen Profilschlüssel. Probiere dein Spiel neu zu starten.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ungültige Signatur füa da öffntliche Profilschlüssl.\nProbia s Spiel neustarta.", "multiplayer.disconnect.invalid_vehicle_movement": "Ungültigs Paket zua Fohrzeigbewegung empfongen", "multiplayer.disconnect.ip_banned": "Dei IP-Adress is auf dem Server gspat", "multiplayer.disconnect.kicked": "A Operator hot di vom Server gschmissn", "multiplayer.disconnect.missing_tags": "Unvollständige Tag-Lischte fum Server empfongn.\nBitte benochrichtige den Serverbesitza.", "multiplayer.disconnect.name_taken": "Dea Nom is schon vagebn", "multiplayer.disconnect.not_whitelisted": "Du bist ned auf da Gästelistn vo dem Server!", "multiplayer.disconnect.out_of_order_chat": "Unerwartetes Chat Paket erhalten. Hat sich deine Systemzeit verändert?", "multiplayer.disconnect.outdated_client": "Client nit kompatibl! Bitte nutz %s", "multiplayer.disconnect.outdated_server": "Inkompatibla Client! Bitte nutz %s", "multiplayer.disconnect.server_full": "Da Server is voi!", "multiplayer.disconnect.server_shutdown": "Server gschlossn", "multiplayer.disconnect.slow_login": "<PERSON> Umödevorgong hot zu long dauat", "multiplayer.disconnect.too_many_pending_chats": "<PERSON>u viele nicht anerkannte Chatnachrichten", "multiplayer.disconnect.transfers_disabled": "Der Server nimmt kanne Übertragungen entgegen", "multiplayer.disconnect.unexpected_query_response": "Unawoatete benutzadefiniate Datn vom Client erhoitn", "multiplayer.disconnect.unsigned_chat": "Chat Paket mit fehlender oder falschen Signatur erhalten.", "multiplayer.disconnect.unverified_username": "Benutzanom hot ned übaprüft wean kina!", "multiplayer.downloadingStats": "Statistikn werdn obgerufen...", "multiplayer.downloadingTerrain": "Londschoft wead glodn ...", "multiplayer.lan.server_found": "Nuior Server gfundn: %s", "multiplayer.message_not_delivered": "Chatnochricht konnt net zugstöllt werdn, bitte Protokolldatein des Servers prüfn: %s", "multiplayer.player.joined": "%s is auf'n Server kemma", "multiplayer.player.joined.renamed": "%s (friara bekonnt ois %s) is auf'n <PERSON> kemma", "multiplayer.player.left": "%s is vom Server gonga", "multiplayer.player.list.hp": "%s LP", "multiplayer.player.list.narration": "Spielr online: %s", "multiplayer.requiredTexturePrompt.disconnect": "Da Server braucht a eigns Ressourcenpaket", "multiplayer.requiredTexturePrompt.line1": "Dea Server braucht a eigns Ressourcenpaket.", "multiplayer.requiredTexturePrompt.line2": "Wonnst des benutzadefiniate Ressourcnpaket ablehnst, wiast vom Serva gschmissn.", "multiplayer.socialInteractions.not_available": "Soziale Interaktionen san nur in Mehrspiela Weltn vafügbor", "multiplayer.status.and_more": "... und %s weitare ...", "multiplayer.status.cancelled": "Obbrochn", "multiplayer.status.cannot_connect": "De Vabindung zum Server konn ned heagstöt wean", "multiplayer.status.cannot_resolve": "De Serveradress hot ned aufg<PERSON>öst wean kina", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Inkompatible Version!", "multiplayer.status.motd.narration": "Nochricht vom Tog: %s", "multiplayer.status.no_connection": "(<PERSON><PERSON> Vabindung)", "multiplayer.status.old": "V<PERSON><PERSON>t", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s Millisekundn", "multiplayer.status.pinging": "Obfrogn ...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s von %s Spielorn online", "multiplayer.status.quitting": "Wead beendet", "multiplayer.status.request_handled": "Statusonfrog is beorbeitet wordn", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Status ohne Onfrog erhoitn", "multiplayer.status.version.narration": "Server-Version: %s", "multiplayer.stopSleeping": "Aufste<PERSON>", "multiplayer.texturePrompt.failure.line1": "As Ressourcenpaket des Servas konnte ned ang'wandt wern", "multiplayer.texturePrompt.failure.line2": "Jegliche Funktionalitätn, de benutzadefiniate Ressourcen erfordat, funktionian möglichaweise ned wie erwartet", "multiplayer.texturePrompt.line1": "Dea Server empfüht de Vawendung von an benutzadefiniertn Ressourcenpaket.", "multiplayer.texturePrompt.line2": "<PERSON><PERSON><PERSON> <PERSON> 's automagisch obal<PERSON><PERSON> und installian?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nNochricht vom Serva:\n%s", "multiplayer.title": "Mehrspielamo<PERSON> spün", "multiplayer.unsecureserver.toast": "De auf dem Server gsendeten Nochrichtn kennan vaändert werdn und entsprechen mäglicherweis net mehr da ursprünglichn Nochricht", "multiplayer.unsecureserver.toast.title": "Chatnochrichtn kinan ned übaprüft wean", "multiplayerWarning.check": "<PERSON> ned mehr zoagen", "multiplayerWarning.header": "Vorsicht: Mehrspiela vo Drittonbietan", "multiplayerWarning.message": "Vorsicht: Da Mehrspiela wead üba Drittonbieta‐Server onboten, de Mojang oda Microsoft weda kean, nu vo eana betriebn oda übawocht wean. Bei da Vawendung vom Mehrspielamodus könntest du unmodarietn Chatnochrichten oda sonstign nutzagenariertn Inholtn ausgsetzt sei, de womöglich nit für jedn ongmessn sen.", "music.game.a_familiar_room": "<PERSON> – A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> – An Ordinary Day", "music.game.ancestry": "<PERSON> Ancestry", "music.game.below_and_above": "<PERSON> – Below and Above", "music.game.broken_clocks": "<PERSON> – Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - Far (G<PERSON><PERSON> Scheibn)", "music.game.comforting_memories": "<PERSON><PERSON> – Comforting Memories", "music.game.creative.aria_math": "C418 – Aria Math", "music.game.creative.biome_fest": "C418 – Biome Fest", "music.game.creative.blind_spots": "C418 – Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 – Taswell", "music.game.crescent_dunes": "<PERSON> – Crescent Dunes", "music.game.danny": "C418 – <PERSON>", "music.game.deeper": "<PERSON> – <PERSON>er", "music.game.dry_hands": "C418 – Dry Hands", "music.game.echo_in_the_wind": "<PERSON> – Echo in the Wind", "music.game.eld_unknown": "<PERSON> – <PERSON><PERSON>", "music.game.end.alpha": "C418 – Alpha", "music.game.end.boss": "C418 – <PERSON>", "music.game.end.the_end": "C418 – The End", "music.game.endless": "<PERSON> – End<PERSON>", "music.game.featherfall": "<PERSON> – Featherfall", "music.game.fireflies": "<PERSON> – Fireflies", "music.game.floating_dream": "<PERSON><PERSON> – Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> – Infinite Amethyst", "music.game.key": "C418 – Key", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> – Left to Bloom", "music.game.lilypad": "<PERSON> – Lily<PERSON>", "music.game.living_mice": "C418 – <PERSON> Mice", "music.game.mice_on_venus": "C418 – <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 – Mine<PERSON>", "music.game.nether.ballad_of_the_cats": "C418 – Ballad of the Cats", "music.game.nether.concrete_halls": "C418 – Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 – <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> – <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> – So Below", "music.game.nether.warmth": "C418 – Warmth", "music.game.one_more_day": "<PERSON> – One More Day", "music.game.os_piano": "<PERSON> – <PERSON>’s Piano", "music.game.oxygene": "C418 – Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> Puzzlebox", "music.game.stand_tall": "<PERSON> – Stand Tall", "music.game.subwoofer_lullaby": "C418 – Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> – Firebugs", "music.game.swamp.labyrinthine": "<PERSON> – Labyrinthine", "music.game.sweden": "C418 – Sweden", "music.game.watcher": "<PERSON> – Watcher", "music.game.water.axolotl": "C418 – Axolotl", "music.game.water.dragon_fish": "C418 – <PERSON> Fish", "music.game.water.shuniji": "C418 – <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON> – <PERSON>", "music.game.wet_hands": "C418 – <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> yakusoku", "music.menu.beginning_2": "C418 – Beginning 2", "music.menu.floating_trees": "C418 – Floating Trees", "music.menu.moog_city_2": "C418 – Moog City 2", "music.menu.mutation": "C418 – Mutation", "narration.button": "Knopf: %s", "narration.button.usage.focused": "Druck Enter zum Aktivirn", "narration.button.usage.hovered": "Linksklick zum Aktiviern", "narration.checkbox": "Checkbox: %s", "narration.checkbox.usage.focused": "Druck Enter zum umschöiten", "narration.checkbox.usage.hovered": "Linksklick zum umschoitn", "narration.component_list.usage": "Tabulatortostn drucken, um zum nächsten Element zu wechseln", "narration.cycle_button.usage.focused": "Druck die Eingabetostn um zu %s zu wechsln", "narration.cycle_button.usage.hovered": "Linksklick um zu %s zu wechsln", "narration.edit_box": "Eingobeföld: %s", "narration.item": "Gegenstand: %s", "narration.recipe": "Rezept fia %s", "narration.recipe.usage": "Linksklick um auszuwöhn", "narration.recipe.usage.more": "Rechtsklick um mehr Rezepte zan zorng", "narration.selection.usage": "Oware oder untere Pfeiltostn druckn, um zu an anderen Eintrag zu wechseln", "narration.slider.usage.focused": "<PERSON>e oda rechte Pfeiltastn druckn, um den Wert zu ändarn", "narration.slider.usage.hovered": "<PERSON><PERSON>, um den Wert zu ändarn", "narration.suggestion": "Vorschlog %s von %s ausgwöht: %s", "narration.suggestion.tooltip": "Vorschlog %s von %s ausgwöht: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Druck de Tabulatortosten, um zum nächsten Vorschlog zu blättern", "narration.suggestion.usage.cycle.hidable": "Dr<PERSON><PERSON> de Tabulatortoste, um zum nächsten Vorschlog zu blättern, oder Escape, um die Vorschläge zu verlossen", "narration.suggestion.usage.fill.fixed": "Drüacke die Taobulatoartoaste, um den Voarschlag zu verwenden", "narration.suggestion.usage.fill.hidable": "Dr<PERSON><PERSON> de Tabulatortoste, um zum nächsten Vorschlog zu blättern, oder Escape, um die Vorschläge zu verlossen", "narration.tab_navigation.usage": "Steuerung und Tabulatortaste drücken, um zwischen Reitern zu wechseln", "narrator.button.accessibility": "Barrierefreiheit", "narrator.button.difficulty_lock": "Schwierigkeitssperre", "narrator.button.difficulty_lock.locked": "Gspat", "narrator.button.difficulty_lock.unlocked": "Freigschoit", "narrator.button.language": "<PERSON><PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s is auf %s gsetzt", "narrator.controls.reset": "%s auf Standard zruggsetzn", "narrator.controls.unbound": "%s is ned zu<PERSON>n", "narrator.joining": "Betretn", "narrator.loading": "Lodn: %s", "narrator.loading.done": "<PERSON><PERSON>", "narrator.position.list": "Ausgewöhlte Listenzeiln %s von %s", "narrator.position.object_list": "Ausgewöhltes Zeilenelement %s von %s", "narrator.position.screen": "Büdschirmelement %s aus %s", "narrator.position.tab": "Reiter %s von %s ausgewählt", "narrator.ready_to_play": "Bereit zum spieln", "narrator.screen.title": "Hauptmenü", "narrator.screen.usage": "Verwendt den Mauszager oder die Tabulatortastn, um a Element auszuwöhln", "narrator.select": "Ausgwöht: %s", "narrator.select.world": "%s, z'letzt gspüt: %s, %s, %s, Version: %s", "narrator.select.world_info": "%s, als letztes gspüt: %s, %s", "narrator.toast.disabled": "Sprochausgob deaktiviat", "narrator.toast.enabled": "Sprochausgob aktiviat", "optimizeWorld.confirm.description": "Des wead dei Wöd durch Speichan vo de Datn im neiestn Format optimian. Des kunnt a sehr long dauan, obh<PERSON><PERSON><PERSON> von da G<PERSON>ß vo deine Wöd. Oamoi durchgführt, konn dei Wöd im Spü schnöia sei oba konn mit olte Versionen vom Spü nimma glodn wean. B<PERSON> du sicha, dass du fortfohrn wüst?", "optimizeWorld.confirm.proceed": "Kopie erstölln und optimian", "optimizeWorld.confirm.title": "<PERSON><PERSON><PERSON> optimian", "optimizeWorld.info.converted": "Aktualisiate Chunks: %s", "optimizeWorld.info.skipped": "Übasprüngene Chunks: %s", "optimizeWorld.info.total": "Gesomte Chunks: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s %%", "optimizeWorld.stage.counting": "<PERSON>ks wean zöd ...", "optimizeWorld.stage.failed": "Fehlgschlogn! :(", "optimizeWorld.stage.finished": "Fertig<PERSON><PERSON><PERSON>...", "optimizeWorld.stage.finished.chunks": "Chunk‐Aktualisierung wird obgschlossen…", "optimizeWorld.stage.finished.entities": "Objektaktualisierung wird obgschlossen…", "optimizeWorld.stage.finished.poi": "Zielpunktaktualisierung wird obgschlossen …", "optimizeWorld.stage.upgrading": "<PERSON><PERSON> wean aktualisiert...", "optimizeWorld.stage.upgrading.chunks": "<PERSON><PERSON> wean aktualisiert...", "optimizeWorld.stage.upgrading.entities": "<PERSON><PERSON>e wean aktualisiert...", "optimizeWorld.stage.upgrading.poi": "<PERSON><PERSON> wean aktualisiert...", "optimizeWorld.title": "Wöd '%s' wead optimiat", "options.accessibility": "Barrierefreiheit...", "options.accessibility.high_contrast": "<PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "Ressourcenpaket für hohen Kontrast ist nicht verfügbar", "options.accessibility.high_contrast.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Kontrast von Elementen der Benutzeroberfläche", "options.accessibility.high_contrast_block_outline": "Kontras<PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "Erhöht n Kontrast von da Umrisslinie vom ongezültn Block.", "options.accessibility.link": "Leitfodn zua Barrierefreiheit", "options.accessibility.menu_background_blurriness": "Menühintagrundsunschärfe", "options.accessibility.menu_background_blurriness.tooltip": "Ändat de Unschärfe von Menü-Hintagründn", "options.accessibility.narrator_hotkey": "Sprochausgobn‐Kurzonwoi", "options.accessibility.narrator_hotkey.mac.tooltip": "Ob de Sprochausgob mit [STRG]/[CMD] + [B] ein- oda ausgschoitn wean konn", "options.accessibility.narrator_hotkey.tooltip": "Ob de Sprochausgob mit [STRG]/[CMD] + [B] ein- oda ausgschoitn wean konn", "options.accessibility.panorama_speed": "Panorama Scroll Geschwindigkeit", "options.accessibility.text_background": "Texthintagrund", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Überoi", "options.accessibility.text_background_opacity": "Texthintagr.-<PERSON><PERSON><PERSON>", "options.accessibility.title": "Barrierefreiheitseistellungen...", "options.allowServerListing": "Server-Auflistung erlaben", "options.allowServerListing.tooltip": "Server können Online-Spieler als Tail ihres öffentlichen Status oflisten. Wenn die Option deaktiviert ist, erschoint dein Name nicht in diesen Listen.", "options.ao": "Woache Beleichtung", "options.ao.max": "Maximum", "options.ao.min": "Minimum", "options.ao.off": "AUS", "options.attack.crosshair": "Fodnkreiz", "options.attack.hotbar": "Schöizuagr.", "options.attackIndicator": "Ongriffsonzeige", "options.audioDevice": "G<PERSON><PERSON>", "options.audioDevice.default": "Systemstandard", "options.autoJump": "Automatisch springa", "options.autoSuggestCommands": "Befehlsvorschläg", "options.autosaveIndicator": "Autospeichan-Onzeige", "options.biomeBlendRadius": "Biomübagong", "options.biomeBlendRadius.1": "AUS (am schnöisten)", "options.biomeBlendRadius.11": "11x11 (extrem)", "options.biomeBlendRadius.13": "13x13 (protzig)", "options.biomeBlendRadius.15": "15x15 (maximal)", "options.biomeBlendRadius.3": "3x3 (schnöi)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (hoch)", "options.biomeBlendRadius.9": "9x9 (zach hoch)", "options.chat": "Chat-Einstellungen...", "options.chat.color": "Forbn", "options.chat.delay": "Chatvazögerung: %s Sekundn", "options.chat.delay_none": "Chatvazögerung: <PERSON><PERSON>", "options.chat.height.focused": "<PERSON><PERSON> (offn)", "options.chat.height.unfocused": "Hech (zua)", "options.chat.line_spacing": "Zeilnobstond", "options.chat.links": "Weblinks", "options.chat.links.prompt": "Links bestätign lossn", "options.chat.opacity": "Chat-<PERSON><PERSON><PERSON><PERSON>", "options.chat.scale": "Chat-Textgröße", "options.chat.title": "Chat-Eistellungen ...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON>", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s Chunks", "options.clouds.fancy": "<PERSON><PERSON><PERSON>", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Steiarung ...", "options.credits_and_attribution": "Mitwirkende & Namensnennung ...", "options.damageTiltStrength": "Schadensneigung", "options.damageTiltStrength.tooltip": "Die Stärke des Kamerawackelns, das beim Erleiden von Sc<PERSON>en auftritt.", "options.darkMojangStudiosBackgroundColor": "Einforbigs Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Ändert die Hintergrundfarbe des Ladebildschirms von Mojang Studios zu Schwoarz.", "options.darknessEffectScale": "Pulsierende Dunklheit", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON>, we stoak da Dunklheitseffekt pulsiert, wenn a Wärta oda a Sculk-Schreia iam an di weita gib.", "options.difficulty": "Schwierigkeit", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Feindliche Kreaturen erscheinen, verursachen aber nur geringen Schaden. Die Hungerleiste leert sich und zehrt die Gesundheit bis auf fünf <PERSON> auf.", "options.difficulty.hard": "<PERSON><PERSON><PERSON>", "options.difficulty.hard.info": "Feindliche Kreaturen erscheinen und verursachen hohen Schaden. Die Hungerleiste leert sich und kann zum Hungertod führen.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "<PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "Feindliche Kreaturen erscheinen und verursachen normalen Schaden. Die Hungerleiste leert sich und zehrt die Gesundheit bis auf ein halbes Herz auf.", "options.difficulty.online": "Schwierigkeit vom Server", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "<PERSON>ine feindlichen Kreaturen und nur manche neutrale Kreaturen erscheinen. Die Hungerleiste leert sich nicht und verlorene Gesundheit erholt sich mit der Zeit.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "<PERSON><PERSON><PERSON><PERSON>ere<PERSON>", "options.directionalAudio.on.tooltip": "Veawendet HRTF-<PERSON><PERSON><PERSON><PERSON>, fia de Vabessarung vom 3D-Ton. Braucht HRTF-kompatible Audiohardware und is am bestn mit Kopfhöran zum erlebn.", "options.discrete_mouse_scroll": "Schrittweisa Büdlauf", "options.entityDistanceScaling": "Objektentfernung", "options.entityShadows": "Objektschottn", "options.font": "Schriftorteinstöllungen...", "options.font.title": "Schriftorteinstöllungen", "options.forceUnicodeFont": "Unicode erzwinga", "options.fov": "Sichtföd (FOV)", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON><PERSON>", "options.fovEffectScale": "Sichtfoid-Effekte", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON>, we stoak si des Sichtföd duach Spümechanikeffekte verändan kaun.", "options.framerate": "%s FPS", "options.framerateLimit": "<PERSON><PERSON>", "options.framerateLimit.max": "Unbegrenzt", "options.fullscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.current": "Aktuell", "options.fullscreen.entry": "%s × %s (%s Hz, %s Bit)", "options.fullscreen.resolution": "Voibüdauflösung", "options.fullscreen.unavailable": "Eistellung ned verfügbor", "options.gamma": "Höigkeit", "options.gamma.default": "Standard", "options.gamma.max": "Hö", "options.gamma.min": "<PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Schimmerbewegung", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON>, wie schnell sich der Schimmer verzauberter Gegenstände bewegt.", "options.glintStrength": "Schimmerstärke", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON>, wie deutlich der Schimmer verzauberter Gegenstände sichtbar ist.", "options.graphics": "<PERSON>ik<PERSON><PERSON>", "options.graphics.fabulous": "Fablhaft!", "options.graphics.fabulous.tooltip": "Dea Graf<PERSON> „%s“ veawendet Shader, um Wetta, Wolkn und Partikl a hinta lichtduachlässign Blöckn und Wassa dozstelln.\nDes konn de Leistung auf trogborn Gerätn und 4K‐Büdschirmen erheblich beeinträchtign.", "options.graphics.fancy": "<PERSON><PERSON>", "options.graphics.fancy.tooltip": "<PERSON><PERSON><PERSON> Grafik stellt auf de meistn Gerätn an Ausgleich zwischn Leistung und Qualität hea.\nWetta, Wolkn und Partikl werdn hinta lichtduachlässign Blöckn oda Wassa nua bedingt dogstellt.", "options.graphics.fast": "Sc<PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "<PERSON><PERSON> Grofik<PERSON><PERSON> „Schnöll“ reduziert de Mengn an sichtborem Regn und Schnea.\nTransparenzeffekte san füa veaschiedene Blöcke we Lab deaktiviert.", "options.graphics.warning.accept": "<PERSON><PERSON>ng wita macha", "options.graphics.warning.cancel": "Bring <PERSON>", "options.graphics.warning.message": "<PERSON><PERSON> wurd akannt, dass dei Ozoaggrät den Grofikmodus „%s“ it untastützt.\n\n<PERSON> kunnsch de Meldung ignoriern und foatfohrn, aba füa dei Grät werd koane Untastützung obotn, wenn du di entscheidest, den Grofikmodus „%s“ zu veawendn.", "options.graphics.warning.renderer": "<PERSON><PERSON><PERSON> eakonnt: [%s]", "options.graphics.warning.title": "Ozoaggrät werd it untastützt", "options.graphics.warning.vendor": "Obieter eakonnt: [%s]", "options.graphics.warning.version": "OpenGL‐Version eakonnt: [%s]", "options.guiScale": "GUI-Greß", "options.guiScale.auto": "Automatisch", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON><PERSON> verst<PERSON>", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON><PERSON>, dass Blitze da Himml ufleuchta lond. D Blitz sind witerhi sichtbar.", "options.hideMatchedNames": "Übaoastimmende Nom ausblendn", "options.hideMatchedNames.tooltip": "A poor Server vo Drittonbieta schickn Chatnochrichtn in an ned standardmäßign Format. Wenn de Option aktiv is, werdn vasteckte Spiela eanare Nochrichtn anhond von de Chatobsendanomen zuageordnet.", "options.hideSplashTexts": "Menüsprüche ausblendn", "options.hideSplashTexts.tooltip": "Schoitet de Göbn Sprüche im Hauptmenü aus.", "options.inactivityFpsLimit": "Bildwi<PERSON><PERSON><PERSON><PERSON> varingan, wenn", "options.inactivityFpsLimit.afk": "Obwesnd", "options.inactivityFpsLimit.afk.tooltip": "Setzt de Büldwiedaholrotn auf 30, wenn des Spü a Minutn ka Eingobe kriagt. Noch 9 Weitan Minutn weats auf 10 Bülda pro Sekunde gestzt.", "options.inactivityFpsLimit.minimized": "Minimiat", "options.inactivityFpsLimit.minimized.tooltip": "Varingat de Büldwiedaholratn nua donn, wenn es Spüfensta mimimiat is.", "options.invertMouse": "<PERSON><PERSON> um<PERSON>n", "options.japaneseGlyphVariants": "Japanische Glyphenvarianten", "options.japaneseGlyphVariants.tooltip": "Vawendet japanische Vartanten von CJK-Zeichn in da Standartschriftoat", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Wechsln", "options.language": "Sproch ...", "options.language.title": "<PERSON><PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "(De Übasetzungen san vielleicht ned zu 100%% richtig)", "options.languageWarning": "De Übasetzungen san eventuell ned zu 100%% richtig", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "Links", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap-Stufn", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "Huat", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Links <PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON>l", "options.modelPart.right_pants_leg": "Rechts Hosnboa", "options.modelPart.right_sleeve": "Rechta Ärml", "options.mouseWheelSensitivity": "Mausrod-Empfindlichkeit", "options.mouse_settings": "Mauseistellungen ...", "options.mouse_settings.title": "Mauseistellungen", "options.multiplayer.title": "Mehrspielaeistellungen ...", "options.multiplier": "%sx", "options.music_frequency": "Musikhäufigkeit", "options.music_frequency.constant": "Durchgä<PERSON><PERSON>", "options.music_frequency.default": "Standard", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON>, wie häufig Musik beim Spielen in einer Welt wiedergegeben wird.", "options.narrator": "Sprochausgob", "options.narrator.all": "<PERSON>t olls", "options.narrator.chat": "<PERSON><PERSON> nur Cha<PERSON>", "options.narrator.notavailable": "Net verfügbor", "options.narrator.off": "AUS", "options.narrator.system": "Liest nur System", "options.notifications.display_time": "Benachrichtigungsdauer", "options.notifications.display_time.tooltip": "Beonflusst die Zeitsponn, in der olle Benochrichtigungn aufm Bildschirm onzoagt werdn.", "options.off": "Aus", "options.off.composed": "%s: Aus", "options.on": "<PERSON>i", "options.on.composed": "%s: An", "options.online": "Online...", "options.online.title": "Online-Optionen", "options.onlyShowSecureChat": "<PERSON><PERSON> sichean <PERSON> au<PERSON>", "options.onlyShowSecureChat.tooltip": "Zag nua Nochrichtn von aunderen Spülan aun, de nochwei<PERSON>lich vo denan gsendet und net vaändert wurdn.", "options.operatorItemsTab": "Operatoan Items Tab", "options.particles": "Partikl", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "Varringat", "options.particles.minimal": "Minimal", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Chunkkompilierung", "options.prioritizeChunkUpdates.byPlayer": "Hoiwad blockierend", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Einige Aktionen in nem Chunk führen gleich zu na Neukompilierung des Chunks. Dazu gehören des Platzieren und Zestören von Blöcken.", "options.prioritizeChunkUpdates.nearby": "Komplett blockierat", "options.prioritizeChunkUpdates.nearby.tooltip": "Nahe Chunks sind immer glaich kompiliert. Des konn die Spülleistung beeinträchtigon, wenn Blöcke plotziert oda zerstört werden.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Benachbarte Chunks wean parallel kompiliert. Des kunnt zu kuaz sichtborn Löchan fian, wenn Bleck zastört wean.", "options.rawMouseInput": "<PERSON><PERSON><PERSON>", "options.realmsNotifications": "Realms‐Neuigkeiten & Einladungen", "options.realmsNotifications.tooltip": "Ruft Realms‐Neuigkeiten und ‐Einladungen im Hauptmenü ob und zagt ihre jeweiligen Symbole auf da Schoitflächen „Realms“ an.", "options.reducedDebugInfo": "Reduziate Debug-Infos", "options.renderClouds": "Woikn", "options.renderCloudsDistance": "Wolkenreichweite", "options.renderDistance": "Sichtweitn", "options.resourcepack": "Ressourcenpakete ...", "options.rotateWithMinecart": "<PERSON><PERSON> mit<PERSON>hn", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON> <PERSON>rich<PERSON>g von m <PERSON> sich mit Wagons mitdrahn soii. Gibts nua in Wöltn, in de de experimentelle \"Wagon Vabessarung\" Einstöllung ein is.", "options.screenEffectScale": "Vazerrungseffekte", "options.screenEffectScale.tooltip": "Stärke fu Üblkeit- und Nethaportal-Vazerrungseffektn.\nBei niedrign Wertn werd der Üblkeit-Effekt duach ane griane Übalogerung austauscht.", "options.sensitivity": "Empfindlichkeit", "options.sensitivity.max": "TURBO!", "options.sensitivity.min": "*gähn*", "options.showNowPlayingToast": "Liadl Nom zoagn", "options.showNowPlayingToast.tooltip": "<PERSON><PERSON>gt eine Einblendung, sobald ein Lied zu spielen beginnt. Während das Lied abgespielt wird, ist diese Einblendung außerdem im Pausenmenü zu sehen.", "options.showSubtitles": "Untatitl uzoagn", "options.simulationDistance": "Simulationsweitn", "options.skinCustomisation": "Skin-Upassung ...", "options.skinCustomisation.title": "Skin-<PERSON><PERSON><PERSON>", "options.sounds": "Musik & Geräusche ...", "options.sounds.title": "Musik- & Geräuschoptionen", "options.telemetry": "Telemetriedotn...", "options.telemetry.button": "Datenerhebung", "options.telemetry.button.tooltip": "„%s“ enthält nur erforderliche Daten.\n„%s“ enthält sowohl optionale als auch erforderliche Daten.", "options.telemetry.disabled": "Telemetrie is ausschoitn.", "options.telemetry.state.all": "Alles", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "Optionen", "options.touchscreen": "Touchscreen-Modus", "options.video": "Grafikeistellungen ...", "options.videoTitle": "Grafikeistellungen", "options.viewBobbing": "Gehbewegung", "options.visible": "<PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft hot kan frein Orbeitsspeicha mea.\n\nDes kunnt an am Fehla im Spü liagn oda daran, dass da Java Virtual Machine ned genug Orbeitsspeicha zugwiesn wordn is.\n\nUm Beschädigungen an da Wöd zu vahindan, is as aktuelle Spü beendet wordn. <PERSON>ia hobn vasuacht, gnuag Orbeitsspeicha freizugeben, dasst ins Hauptmenü zruggeh und weitaspün kaust, owa des hot eventuell ned funktioniert.\n\nBitte start as <PERSON><PERSON><PERSON> nei, wenn du de Mödung nu amoi siachst.", "outOfMemory.title": "Ka freia Orbeitsspeicha mea!", "pack.available.title": "Vafügbor", "pack.copyFailure": "Pakete konntn nit kopiert werdn", "pack.dropConfirm": "Willsch du de folgendn Pakete zua Minecraft hinzuafügn?", "pack.dropInfo": "<PERSON><PERSON><PERSON> in des Fenschter und leg se do ab, um Pakete hinzuzufügn", "pack.dropRejected.message": "De folgenden Einträge woan kane gültign Pakete und san ned kopiat woan: %s", "pack.dropRejected.title": "Nicht‐Paket‐Einträge", "pack.folderInfo": "(Paketdatein do einfügn)", "pack.incompatible": "<PERSON> k<PERSON>l", "pack.incompatible.confirm.new": "Des Pak<PERSON> wurd für ane neiere Version fu Minecraft erstöllt und funktioniert möglichaweise nimma richtig.", "pack.incompatible.confirm.old": "Des Pak<PERSON> wurd für ane ältere Version fu Minecraft erstöllt und funktioniert möglichaweise nimma richtig.", "pack.incompatible.confirm.title": "Bisch du da sicha, dass du des Paket laden willsch?", "pack.incompatible.new": "(Fia a neiare Version von Minecraft gmocht wordn)", "pack.incompatible.old": "(Fia a ötare Version von Minecraft gmocht wordn)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Paketordner öffnen", "pack.selected.title": "Ausgwöht", "pack.source.builtin": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.feature": "funktion", "pack.source.local": "lokal", "pack.source.server": "server", "pack.source.world": "wöd", "painting.dimensions": "%s × %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Da <PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON>iel erfolgreich bombardiert", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Der Totenkopf in Flammen", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON> Büste", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Da Höhlenvogei", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Da Kostümwechsel", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Die Begegnung", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Erde", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Da Feind am ende annes spüs", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Farn", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON> Kämpfer", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Bescheiden", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Der Döner mit drei Pfefferonen", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Tiefnöbi", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativ", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Zitruseule", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Durchgong", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Das Schweinebildnis", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Der Pfennigbaum", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Der Zeigefinger", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON>", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Präriefahrt", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON>ü<PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Steabliche <PERSON>", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON><PERSON><PERSON> und Rosa", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "D Bühne isch bereit", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON>blumen", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "das Abendrot", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Gezeiten", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Entpackt", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Die Ödnis", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Luft", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "zuafäallige variante", "parsing.bool.expected": "Boolesche Variable erwortet", "parsing.bool.invalid": "Ungültiga boolescha Wert: '%s' is weda 'true' noch 'false'", "parsing.double.expected": "Gleitkommazoi (double) erwoatet", "parsing.double.invalid": "Ungültige Kommazoi: '%s'", "parsing.expected": "'%s' erwoatet", "parsing.float.expected": "Fliaßkommazoi (float) erwoatet", "parsing.float.invalid": "Ungütige Fliaßkommazoi '%s'", "parsing.int.expected": "Gonzzoi (int) erwoatet", "parsing.int.invalid": "Ungültige Gonzzoi: '%s'", "parsing.long.expected": "<PERSON><PERSON> erwo<PERSON>t", "parsing.long.invalid": "Ungültige longe Ganzzohl '%s'", "parsing.quote.escape": "Ungültige Escape-Sequenz '\\%s' in Zeichnkette", "parsing.quote.expected.end": "Fehlends schliaßnds Onführungszeichn", "parsing.quote.expected.start": "Onführungszeichn am Onfong von da Zeichnkette erwortet", "particle.invalidOptions": "Partikleignschoft hod ned ausgweatet wean kennen: %s", "particle.notFound": "Unbekonnts Partikl ‚%s‘", "permissions.requires.entity": "Der Befehl muass von an Objekt ausgeführt wean", "permissions.requires.player": "Der Befehl muass von an Spiela ausgeführt wean", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "<PERSON><PERSON>:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unbekonnts Rezept: %s", "quickplay.error.invalid_identifier": "Es kot ka Wöt mit dea onggegebenen Kennung gfundn weadn kennen", "quickplay.error.realm_connect": "De Vabindung zum Realm ho net heagsö weadn kina", "quickplay.error.realm_permission": "Du <PERSON> ka <PERSON>, diesm Realm beizutretn", "quickplay.error.title": "Schnöspün fehlgschlogn", "realms.configuration.region.australia_east": "New South Wales, Australien", "realms.configuration.region.australia_southeast": "Victoria, Australien", "realms.configuration.region.brazil_south": "Brasilien", "realms.configuration.region.central_india": "Indien", "realms.configuration.region.central_us": "Iowa, Vereinigte Staaten", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, Vereinigte Staaten", "realms.configuration.region.east_us_2": "North Carolina, Vereinigte Staaten", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.japan_west": "Westjapan", "realms.configuration.region.korea_central": "Südkorea", "realms.configuration.region.north_central_us": "Illinois, Vereinigte Staaten", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Texas, Vereinigte Staaten", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "Schweden", "realms.configuration.region.uae_north": "Vereinigte Arabische Emirate (VAE)", "realms.configuration.region.uk_south": "Südengland", "realms.configuration.region.west_central_us": "Utah, Vereinigte Staaten", "realms.configuration.region.west_europe": "Niederlande", "realms.configuration.region.west_us": "Kalifornien, Vereinigte Staaten", "realms.configuration.region.west_us_2": "Washington, Vereinigte Staaten", "realms.configuration.region_preference.automatic_owner": "Automatisch (nahe Realm‐Besitzer)", "realms.configuration.region_preference.automatic_player": "Automatisch (nahe 1. Spieler je Sitzung)", "realms.missing.snapshot.error.text": "Realms wead derzeit in Entwicklungsverionen ned untastützt", "recipe.notFound": "Unbekonnts Rezept: %s", "recipe.toast.description": "Schau in dei Rezeptbuach", "recipe.toast.title": "Neie Rezepte freigschoitn!", "record.nowPlaying": "Spüt jiatzt: %s", "recover_world.bug_tracker": "<PERSON> mödn", "recover_world.button": "Wiedaherstöllung versuachn", "recover_world.done.failed": "<PERSON><PERSON><PERSON> hod neg glodn wean kennan.", "recover_world.done.success": "De <PERSON>ld hot wida herstollt wean kennan!", "recover_world.done.title": "Wiaderherstellung abgeschloassen", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "De folgenden Fehla san beim Wöltordnalodn vom Ordna '%s' passiat.\nMöglichaweise losst sich de Wölt nua aus an öltarem Zuastond wiah<PERSON>tölln, oder du konnst des Problem im Bugtracker möldn.", "recover_world.no_fallback": "Ka Zustond zum widaherstölln vuahondn", "recover_world.restore": "Vasuach zum Widaherstölln", "recover_world.restoring": "<PERSON><PERSON> wead vasuacht, de Wölt wiad herzumstölln...", "recover_world.state_entry": "<PERSON><PERSON><PERSON> von %s: ", "recover_world.state_entry.unknown": "unbekonnt", "recover_world.title": "Wölt hot ned glodn wean kennan", "recover_world.warning": "Loaden der Woaltbeschreibung feahlgeschlagen", "resourcePack.broken_assets": "DEFEKTE RESSOURCN ERKONNT", "resourcePack.high_contrast.name": "<PERSON><PERSON>", "resourcePack.load_fail": "<PERSON><PERSON><PERSON> is fehlgsch<PERSON>n", "resourcePack.programmer_art.name": "<PERSON><PERSON><PERSON>", "resourcePack.runtime_failure": "Resourcepack-<PERSON><PERSON><PERSON>", "resourcePack.server.name": "Wödspezifische Ressourcen", "resourcePack.title": "Ressourcenpakete auswöhn", "resourcePack.vanilla.description": "Normals Minecraft, wie du es kennst", "resourcePack.vanilla.name": "Standard", "resourcepack.downloading": "Ressourcenpaket wead obaglodn", "resourcepack.progress": "Datei wead obaglodn (%s MB)...", "resourcepack.requesting": "Ofrog wead gschickt ...", "screenshot.failure": "Screenshot hot ned g<PERSON><PERSON><PERSON>t wean kina: %s", "screenshot.success": "Screenshot gspeichat unta %s", "selectServer.add": "Server hinz<PERSON><PERSON><PERSON><PERSON>", "selectServer.defaultName": "Minecraft-Server", "selectServer.delete": "L<PERSON><PERSON><PERSON>", "selectServer.deleteButton": "L<PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "<PERSON><PERSON> da sicha, dass du den Server entfernen wüst?", "selectServer.deleteWarning": "'%s' wiad fia imma weg sein (Des is a longe Zeit!)", "selectServer.direct": "Direkt vabindn", "selectServer.edit": "Beoabeitn", "selectServer.hiddenAddress": "(<PERSON>ast<PERSON><PERSON>)", "selectServer.refresh": "Aktualisian", "selectServer.select": "Aufn Server geh", "selectWorld.access_failure": "Zugriff auf Wöd vögschlogn", "selectWorld.allowCommands": "Schummln ealabn", "selectWorld.allowCommands.info": "Be<PERSON>hle wia /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "Zwischngspeichate Datn löschn", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON> er<PERSON>ön und lodn", "selectWorld.backupJoinSkipButton": "I woaß wos i dua!", "selectWorld.backupQuestion.customized": "Upasste W<PERSON>n wean nimma untastützt", "selectWorld.backupQuestion.downgrade": "As herobstufn vo ana Wöd wead ned untastützt", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON><PERSON>, de experimentelle Eistellungen vawendn, wean nid untastützt", "selectWorld.backupQuestion.snapshot": "Mogst de wöd wiaklich lodn?", "selectWorld.backupWarning.customized": "Leida wean upasste Wötn in dea Version von Minecaft nimma untastützt. De Wöd konn weitahin glodn wean und ois bleib so wias wor, oba nei generiate Londschoft wead nimma upasst sei. Wia entschuldign ins fia de Unonnehmlichkeitn!", "selectWorld.backupWarning.downgrade": "<PERSON><PERSON> is as letz<PERSON> <PERSON> in da Version %s gspüt wordn; du bist etz auf da Version %s. De Wöd mit ana ötan Version zum Spün ku zu ana Beschädigung von de Datn führn - mia kinan ned garantier<PERSON>, dass de Wöd lodt oda lafft. Wenn du des etz trotzdem toa wüst, donn moch bitte a Sicherheitskopie!", "selectWorld.backupWarning.experimental": "<PERSON> Wö<PERSON>t experimentelle Eistellungen, de jedazeit nimma funktionian kinan. <PERSON>ia kinan ned garan<PERSON>, dass si glodn wead oda funktioniat. Etz weads ernst!", "selectWorld.backupWarning.snapshot": "<PERSON> is as letzte moi in da Version %s gsp<PERSON>t wordn, du benutzt aktuell de Version %s. Bitte erstö a Sicherungskopie, weil de Wöd beschädigt wean konn!", "selectWorld.bonusItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.cheats": "Cheats", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "<PERSON><PERSON>ß umgwondlt wean!", "selectWorld.conversion.tooltip": "<PERSON><PERSON> muas in a öttan Version (wia 1.6.4) g<PERSON><PERSON><PERSON> wean damits sicha konvertiert wead", "selectWorld.create": "<PERSON><PERSON><PERSON>", "selectWorld.customizeType": "Upassn", "selectWorld.dataPacks": "Datnpakete", "selectWorld.data_read": "Weltdatn werdn glesn …", "selectWorld.delete": "L<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "L<PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "<PERSON><PERSON> da sicha, dass du de Wöd l<PERSON> wüst?", "selectWorld.deleteWarning": "'%s' is fia imma dahin! (imma is a longe Zeit!)", "selectWorld.delete_failure": "<PERSON><PERSON><PERSON><PERSON> da Wöd vögschlogn", "selectWorld.edit": "Beorbeitn", "selectWorld.edit.backup": "Sicharungskopie erstön", "selectWorld.edit.backupCreated": "Gsichat: %s", "selectWorld.edit.backupFailed": "Sicharungskopie fehlgschlogn", "selectWorld.edit.backupFolder": "Sicharungskopien-Ordna aufmochn", "selectWorld.edit.backupSize": "Greß: %s MB", "selectWorld.edit.export_worldgen_settings": "Wödgenerierungseistellungen exportian", "selectWorld.edit.export_worldgen_settings.failure": "Export fehlgschlong", "selectWorld.edit.export_worldgen_settings.success": "Exportiart", "selectWorld.edit.openFolder": "Wödordn<PERSON> auf<PERSON>n", "selectWorld.edit.optimize": "<PERSON><PERSON><PERSON> optimian", "selectWorld.edit.resetIcon": "Symbol zruggsetzn", "selectWorld.edit.save": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.title": "<PERSON><PERSON><PERSON>", "selectWorld.enterName": "<PERSON><PERSON>", "selectWorld.enterSeed": "Start<PERSON>t fian <PERSON>", "selectWorld.experimental": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Benötigte experimentölle Funktionen: %s", "selectWorld.experimental.details.title": "Onfoadarungen on experimentölle Funktionen", "selectWorld.experimental.message": "Gib Obacht!\nDe Konfiguration braucht Spüelemente, de no in Entwicklung san. <PERSON><PERSON>d kunnt o<PERSON>tü<PERSON>, kaputt<PERSON>h oda mit zukünftige Aktualisierungen nimma funktioniern.", "selectWorld.experimental.title": "Expeimentölle Funktionen Woanung", "selectWorld.experiments": "Experimente", "selectWorld.experiments.info": "Experimente sind mögliche neue Spielelemente. <PERSON><PERSON> <PERSON><PERSON>, da deine Welt beschädigt werden kann. Nach dem Erstellen der Welt können sie nicht mehr deaktiviert werden.", "selectWorld.futureworld.error.text": "<PERSON><PERSON> lodn oana Wöd aus oana neuchern Version is a Fehler auftretn. Des woar von <PERSON> on a riskantes Unterfongen; leider hot des ned funktioniert.", "selectWorld.futureworld.error.title": "A Fehla is auftretn!", "selectWorld.gameMode": "Spümodus", "selectWorld.gameMode.adventure": "Obnteia", "selectWorld.gameMode.adventure.info": "Wie der Überlebensmodus, aber Blöcke können nicht platziert oder abgebaut werden.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON><PERSON> w<PERSON>, <PERSON><PERSON><PERSON> kinan oba ned", "selectWorld.gameMode.adventure.line2": "platz<PERSON>t oda zastört wean", "selectWorld.gameMode.creative": "K<PERSON><PERSON>v", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, baue und erkunde ohne Grenzen. Du kannst fliegen, verfügst über unendlich viel Materialien und Monster können dir nichts anhaben.", "selectWorld.gameMode.creative.line1": "Unendlich vü R<PERSON>stoffe, Fliagn und", "selectWorld.gameMode.creative.line2": "moch <PERSON><PERSON>ck sofort hi", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Der Überlebensmodus, ges<PERSON>rt auf der Schwierigkeit „Schwer“. Du kannst dich nicht wiederbeleben, wenn du stirbst.", "selectWorld.gameMode.hardcore.line1": "Übalebnsmodus auf da hechstn", "selectWorld.gameMode.hardcore.line2": "Schwierigkeit ohne Wiedaeistieg", "selectWorld.gameMode.spectator": "Zuaschaua", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON> gucken, nicht anfassen.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON>, nix ufossn", "selectWorld.gameMode.survival": "Übalebn", "selectWorld.gameMode.survival.info": "Erkunde eine geheimnisvolle Welt, in der du baust, sammelst, herstellst und Monster bekämpfst.", "selectWorld.gameMode.survival.line1": "Suach noch Ressourcen, bau Werkzeige, somml", "selectWorld.gameMode.survival.line2": "Erfohrung, Gesundheit und Hunga", "selectWorld.gameRules": "Spüregln", "selectWorld.import_worldgen_settings": "Eistellungen importian", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON><PERSON> beim impoar<PERSON> der Einstellungn", "selectWorld.import_worldgen_settings.select_file": "Wö a Einstellungs Datn (.jcson)", "selectWorld.incompatible.description": "De Wölt kon ned in dea Version göffnet weadn.\nEs letze mol is es in da Version %s gspült woadn.", "selectWorld.incompatible.info": "Unkompatible Version: %s", "selectWorld.incompatible.title": "Unkompatible Version", "selectWorld.incompatible.tooltip": "De W<PERSON>lt hot ned gö<PERSON>net wean kennan, weil se in ana unkompatiblen Version erstöllt woan is.", "selectWorld.incompatible_series": "Vo a inkompatiblen Version erstöht", "selectWorld.load_folder_access": "<PERSON>f den Ordner wo de Wötn drinnen san, ku ned zuagriffn wean!", "selectWorld.loading_list": "W<PERSON>dl<PERSON>n wead glodn", "selectWorld.locked": "Von ana ondan laffndn Minecraft-Instanz gspat", "selectWorld.mapFeatures": "Strukturn genarian", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, Schiffswracks usw.", "selectWorld.mapType": "W<PERSON>dtyp", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Mehr Wödoptionen ...", "selectWorld.newWorld": "<PERSON><PERSON><PERSON>", "selectWorld.recreate": "<PERSON><PERSON>", "selectWorld.recreate.customized.text": "Upasste Wötn wean in dea Version von Minecraft nimma untastützt. Es wead vasuacht, de Wöd erneit mit demsöbn Startwert und deselbn Eignschoftn zu erzeugn, oba olle <PERSON>upassungen san weg. Wia entschuldign ins fia de Unonnehmlichkeitn!", "selectWorld.recreate.customized.title": "Ongepasste Wöltn wean nimma untastützt", "selectWorld.recreate.error.text": "<PERSON><PERSON> er<PERSON> von <PERSON> is wos fehlgschlogn.", "selectWorld.recreate.error.title": "A fehla is aufgetretn!", "selectWorld.resource_load": "Ressourcen wean vorbereitet ...", "selectWorld.resultFolder": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> in:", "selectWorld.search": "noch Wötn suachn", "selectWorld.seedInfo": "Laar lossn fia an zuafällign Startwert", "selectWorld.select": "Ausgwöhte Wöd <PERSON>ün", "selectWorld.targetFolder": "Speicharungsordna: %s", "selectWorld.title": "<PERSON><PERSON><PERSON>", "selectWorld.tooltip.fromNewerVersion1": "<PERSON> is in ana neian Version gspeichat wordn,", "selectWorld.tooltip.fromNewerVersion2": "wenn du sie jetz lodst, kunnt des problematische Folgn hobn!", "selectWorld.tooltip.snapshot1": "<PERSON><PERSON><PERSON> ned, a <PERSON>up vo da Wöd zmachn", "selectWorld.tooltip.snapshot2": "bevor du sie in dea Entwicklungsversion aufmochst.", "selectWorld.unable_to_load": "<PERSON><PERSON><PERSON><PERSON> kinan ned glodn wean", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "Trotzdem lodn", "selectWorld.versionQuestion": "Wüst du de Wöd wirklich lodn?", "selectWorld.versionUnknown": "unbekonnt", "selectWorld.versionWarning": "<PERSON> Wöd is es letzte <PERSON> in da Version '%s' gspüt wordn. Wenn du sie in dea Version lodst, ku des de <PERSON>öd beschädign!", "selectWorld.warning.deprecated.question": "Einige dea Funktionen sind vaoltet und weadn in Zukunft nimma funktionieren. Mogst trotzdem foatfoan?", "selectWorld.warning.deprecated.title": "Ochtung! De Einstöllungen san vaoltete Funktionen", "selectWorld.warning.experimental.question": "De Einstöllungen sind Expermentöll und kenntn iagndwon nimma funktionian. Mogst trotzdem foatsetzn?", "selectWorld.warning.experimental.title": "Ochtung! De Einstöllungen vawendn experimentölle Funktionen", "selectWorld.warning.lowDiskSpace.description": "Auf deim Gerät is neama vü Speichaplotz voahondn.\nWen<PERSON> kan Speichaplotz mea host, kennt des dei Wölt hin mochn.", "selectWorld.warning.lowDiskSpace.title": "Ochtung! Wenig Speichaplotz!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "Schüdbeschriftung beorbeitn", "sleep.not_possible": "De Nocht ko net übasprunga wern", "sleep.players_sleeping": "%s/%s <PERSON><PERSON><PERSON>' schlofn", "sleep.skipping_night": "Durch d'nocht durchschlofn", "slot.only_single_allowed": "Nua anzelne Inventarplätze san erlaubt, '%s' erholtn", "slot.unknown": "Unbekonnta Slot '%s'", "snbt.parser.empty_key": "<PERSON><PERSON><PERSON><PERSON> derf ned leer sein", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON><PERSON><PERSON><PERSON> erwoat", "snbt.parser.expected_decimal_numeral": "Dezimalzohl erwoat", "snbt.parser.expected_float_type": "Gleitkommazahl erwartet", "snbt.parser.expected_hex_escape": "Zeichnkettn von da Länge %s erwoat", "snbt.parser.expected_hex_numeral": "Hexadez<PERSON><PERSON><PERSON><PERSON> erwoat", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON><PERSON> erwoat", "snbt.parser.expected_non_negative_number": "Nichtnegative Zohl erwoat", "snbt.parser.expected_number_or_boolean": "<PERSON><PERSON><PERSON> oda Wahrheit erwoat", "snbt.parser.expected_string_uuid": "Zei<PERSON>nk<PERSON><PERSON> erwoat, de a gültige UUID darstellt", "snbt.parser.expected_unquoted_string": "Gültige Zeichnkettn ohne Anführungszeichn erwoat", "snbt.parser.infinity_not_allowed": "<PERSON> endliche Zohln san ned erlaubt", "snbt.parser.invalid_array_element_type": "Ungültiga Typ von Listnelement", "snbt.parser.invalid_character_name": "Ungültiga Name von Unicode-Zeichn", "snbt.parser.invalid_codepoint": "Ungültiga Wert für Unicode-Zeichen: %s", "snbt.parser.invalid_string_contents": "Ungültiga Inhalt von da Zeichnkettn", "snbt.parser.invalid_unquoted_start": "Zeichnkettn ohne Anführungszeichn derfn ned mit den Ziffern 0-9 oda mit den Zeichn + und - beginnen", "snbt.parser.leading_zero_not_allowed": "Dezimalzohln derfn ned mit 0 beginnen", "snbt.parser.no_such_operation": "Keine derartige Operation: %s", "snbt.parser.number_parse_failure": "Zohl hot ned glesen werdn kennan: %s", "snbt.parser.undescore_not_allowed": "Unterstrich san am Onfong und End ana <PERSON>l ned erlaubt", "soundCategory.ambient": "Atmosphäre/Umgebung", "soundCategory.block": "<PERSON><PERSON><PERSON>", "soundCategory.hostile": "Feindliche Kreaturn", "soundCategory.master": "Gesomtlautstärke", "soundCategory.music": "Mu<PERSON>", "soundCategory.neutral": "Freindliche Kreaturn", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Plottnspiela/Notnbleck", "soundCategory.ui": "Benutzeroberfläche", "soundCategory.voice": "Stimm/Sproch", "soundCategory.weather": "Wetta", "spectatorMenu.close": "<PERSON><PERSON>", "spectatorMenu.next_page": "Nexte Seitn", "spectatorMenu.previous_page": "Voaherige Seitn", "spectatorMenu.root.prompt": "Druck a <PERSON>n, um an <PERSON>hl auszuw<PERSON>hn, und nuamoi, um eam ausz<PERSON>hn.", "spectatorMenu.team_teleport": "<PERSON><PERSON>ed teleportian", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON> des Team, zu dem du di teleportian wüst", "spectatorMenu.teleport": "Zu Spiela teleportian", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON> den Spiela, zu dem du di teleportian wüst", "stat.generalButton": "Ollgemein", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Tiere züchtet", "stat.minecraft.aviate_one_cm": "St<PERSON><PERSON> mit Elytren gflogn", "stat.minecraft.bell_ring": "Glockn gl<PERSON>t", "stat.minecraft.boat_one_cm": "Streckn im Boot gfohn", "stat.minecraft.clean_armor": "Rüstungsteile gwoschn", "stat.minecraft.clean_banner": "<PERSON><PERSON>", "stat.minecraft.clean_shulker_box": "Shulkerkistn gwoschn", "stat.minecraft.climb_one_cm": "Streckn klettat", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> g<PERSON>n", "stat.minecraft.damage_absorbed": "Schodn absorb<PERSON>t", "stat.minecraft.damage_blocked_by_shield": "Sc<PERSON><PERSON> mit Schüd obgwehrt", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON>g", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> (absorbiat)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> (widastondn)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON> w<PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON>", "stat.minecraft.drop": "<PERSON><PERSON><PERSON><PERSON> foin glossn", "stat.minecraft.eat_cake_slice": "Kuchnsticke gessn", "stat.minecraft.enchant_item": "Gegnstend vazaubat", "stat.minecraft.fall_one_cm": "Streckn gfoin", "stat.minecraft.fill_cauldron": "Kessl gfüt", "stat.minecraft.fish_caught": "Fische gfong", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON> gflogn", "stat.minecraft.happy_ghast_one_cm": "Strecke auf glücklichem Ghast geflogen", "stat.minecraft.horse_one_cm": "St<PERSON><PERSON> auf an <PERSON>ß grittn", "stat.minecraft.inspect_dispenser": "<PERSON>rfa durch<PERSON>t", "stat.minecraft.inspect_dropper": "Spenda durch<PERSON>t", "stat.minecraft.inspect_hopper": "Trichta durch<PERSON>t", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON> heag<PERSON>", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON> heagnumma", "stat.minecraft.interact_with_blast_furnace": "Schmözofn heagnumma", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON><PERSON> heag<PERSON>", "stat.minecraft.interact_with_campfire": "Logafeia heagnumma", "stat.minecraft.interact_with_cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "Werkbenk heagnumma", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON> heagnum<PERSON>", "stat.minecraft.interact_with_grindstone": "Sc<PERSON><PERSON>fsto<PERSON> heagnumma", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON><PERSON> heagnum<PERSON>", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smoker": "Räuche<PERSON><PERSON><PERSON> heagnum<PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON> valossn", "stat.minecraft.minecart_one_cm": "St<PERSON><PERSON> in an Wagon gfohn", "stat.minecraft.mob_kills": "Kreaturn umbrocht", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON> au<PERSON>g<PERSON>", "stat.minecraft.open_chest": "Kistn aufgmocht", "stat.minecraft.open_enderchest": "Enderkistn aufgmocht", "stat.minecraft.open_shulker_box": "Shulkerkistn aufgmocht", "stat.minecraft.pig_one_cm": "<PERSON><PERSON>n auf an <PERSON>wein grittn", "stat.minecraft.play_noteblock": "Notnbleck ogspüt", "stat.minecraft.play_record": "Schoiplottn obgspielt", "stat.minecraft.play_time": "Spü<PERSON>ua", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> umbrocht", "stat.minecraft.pot_flower": "Pflonzn eingsetzt", "stat.minecraft.raid_trigger": "Übafälle ausglöst", "stat.minecraft.raid_win": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>gt", "stat.minecraft.sleep_in_bed": "In am Bett gschlofn", "stat.minecraft.sneak_time": "Gschlichne Zeit", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> gsp<PERSON>t", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON> auf an Schreita grittn", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> gschwumma", "stat.minecraft.talked_to_villager": "<PERSON><PERSON> gred", "stat.minecraft.target_hit": "Züscheibnblock troffn", "stat.minecraft.time_since_death": "Seit letztem Tod", "stat.minecraft.time_since_rest": "Seit letztem Schlofn", "stat.minecraft.total_world_time": "Zoat mit göffneta Weld", "stat.minecraft.traded_with_villager": "<PERSON><PERSON> Dorf<PERSON>wo<PERSON>a ghondlt", "stat.minecraft.trigger_trapped_chest": "Redstonekistn ausglöst", "stat.minecraft.tune_noteblock": "Notnbleck gstimmb", "stat.minecraft.use_cauldron": "Wossa aus Kessl gschepft", "stat.minecraft.walk_on_water_one_cm": "Steckn aufm Wossa gloffn", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON> gloffn", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON> unta Wossa gloffn", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON>agst<PERSON><PERSON>", "stat_type.minecraft.dropped": "<PERSON><PERSON>n g<PERSON>n", "stat_type.minecraft.killed": "Du host %s %s umbrocht", "stat_type.minecraft.killed.none": "Du host %s nia umbrocht", "stat_type.minecraft.killed_by": "%s hot di %s moi umbrocht", "stat_type.minecraft.killed_by.none": "Du bist von %s nia umbrocht wordn", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "Aufgsommlt", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "ERKENNA", "structure_block.button.load": "LODN", "structure_block.button.save": "SPEICHAN", "structure_block.custom_data": "Benutzadefinierte NBT-Datn", "structure_block.detect_size": "Strukturgreß und -position dakenna:", "structure_block.hover.corner": "Egg: %s", "structure_block.hover.data": "Datn: %s", "structure_block.hover.load": "Lodn: %s", "structure_block.hover.save": "Speichan: %s", "structure_block.include_entities": "Objekte eibeziagn:", "structure_block.integrity": "Voiständigkeit und Startwert der Konstruktion", "structure_block.integrity.integrity": "Konstruktionsvoiständigkeit", "structure_block.integrity.seed": "Konstruktions-Startwert", "structure_block.invalid_structure_name": "Ungültiga Strukturnom '%s'", "structure_block.load_not_found": "Konstruktion '%s' ned gfundn", "structure_block.load_prepare": "Position von Konstruktion '%s' vorbereitet", "structure_block.load_success": "Konstruktion '%s' glodn", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "Datn", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Eck-Modus - Markierung vo Position und Greß", "structure_block.mode_info.data": "Datn-Modus - Markierung der Spülogik", "structure_block.mode_info.load": "Lode-Modus - aus Datei lodn", "structure_block.mode_info.save": "Speicha-Modus - in <PERSON><PERSON> speichan", "structure_block.position": "Relative Position", "structure_block.position.x": "Relative X-Position", "structure_block.position.y": "Relative Y-Position", "structure_block.position.z": "Relative Z-Position", "structure_block.save_failure": "Konstruktion '%s' hot ned g<PERSON><PERSON>chat wean kina", "structure_block.save_success": "Konstruktion ois '%s' gspeichat", "structure_block.show_air": "<PERSON><PERSON><PERSON> un<PERSON><PERSON><PERSON>le<PERSON>:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON>:", "structure_block.size": "Konstruktionsgreß", "structure_block.size.x": "Konstruktionsgreß (X)", "structure_block.size.y": "Konstruktionsgreß (Y)", "structure_block.size.z": "Konstruktionsgreß (Z)", "structure_block.size_failure": "Konstruktionsgreß ku ned dakennt wean - füg Eggn mit an gleichn Konstruktionsnom hinzua", "structure_block.size_success": "<PERSON><PERSON><PERSON> von '%s' dakennt", "structure_block.strict": "Strikte Platzierung:", "structure_block.structure_name": "Konstruktionsnom", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.amethyst_block.chime": "Amethyst kling", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON> schwingt mit", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON> wead ben<PERSON>t", "subtitles.block.barrel.close": "<PERSON><PERSON> s<PERSON>t", "subtitles.block.barrel.open": "<PERSON>oss geht auf", "subtitles.block.beacon.activate": "Leichtfeia aktiviat", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> brummt", "subtitles.block.beacon.deactivate": "Leichtfeia schoit si aus", "subtitles.block.beacon.power_select": "Leichtfeiakroft ausgwöht", "subtitles.block.beehive.drip": "<PERSON><PERSON> tropft", "subtitles.block.beehive.enter": "Biene fliagt in Stock", "subtitles.block.beehive.exit": "Biene verlosst Stock", "subtitles.block.beehive.shear": "<PERSON><PERSON> schobt", "subtitles.block.beehive.work": "Bienen orbeitn", "subtitles.block.bell.resonate": "Glockn hallt", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON> bimmlt", "subtitles.block.big_dripleaf.tilt_down": "Tropfblatt kippt nach unten", "subtitles.block.big_dripleaf.tilt_up": "Tropfblatt kippt nach oben", "subtitles.block.blastfurnace.fire_crackle": "Schmözofn knirstat", "subtitles.block.brewing_stand.brew": "Braustond blubbad", "subtitles.block.bubble_column.bubble_pop": "Blosn platzn", "subtitles.block.bubble_column.upwards_ambient": "Blosn sprudln", "subtitles.block.bubble_column.upwards_inside": "B<PERSON>n s<PERSON>en", "subtitles.block.bubble_column.whirlpool_ambient": "Blosn wirbln", "subtitles.block.bubble_column.whirlpool_inside": "Blosn z<PERSON>hn", "subtitles.block.button.click": "<PERSON><PERSON><PERSON> klickt", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON> schwab<PERSON>", "subtitles.block.campfire.crackle": "Logafeia knistat", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON> knistat", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.locked": "Kistn gspat", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON> geht auf", "subtitles.block.chorus_flower.death": "Chorusblütn wökd", "subtitles.block.chorus_flower.grow": "Chorusblütn wochst", "subtitles.block.comparator.click": "Komp<PERSON><PERSON> klickt", "subtitles.block.composter.empty": "Komposta ausglaat", "subtitles.block.composter.fill": "Komposta befüllt", "subtitles.block.composter.ready": "Komposta kompostiat", "subtitles.block.conduit.activate": "Aquisator aktiviert", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON> puls<PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON> greift on", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON> schoit si aus", "subtitles.block.copper_bulb.turn_off": "Kupfalompn gehd aus", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON><PERSON><PERSON>n gehd ei", "subtitles.block.copper_trapdoor.close": "Troapdoar moacht zua", "subtitles.block.copper_trapdoor.open": "Foitir mocht auf", "subtitles.block.crafter.craft": "<PERSON><PERSON><PERSON>", "subtitles.block.crafter.fail": "Crafter v<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.hurt": "Knoazaheaz grummlt", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.spawn": "Knoazaheaz erwocht", "subtitles.block.deadbush.idle": "Zwoag knistan", "subtitles.block.decorated_pot.insert": "Vazierta Kruag gfüt", "subtitles.block.decorated_pot.insert_fail": "Vazierta Kruag wocklt", "subtitles.block.decorated_pot.shatter": "Vazierta Kruag zabricht", "subtitles.block.dispenser.dispense": "Gegnstond gschmissn", "subtitles.block.dispenser.fail": "Werfa vasog", "subtitles.block.door.toggle": "Tir knorrt", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON>au<PERSON><PERSON> ", "subtitles.block.dried_ghast.ambient_water": "Ausgetrockneter Ghast nimmt Was<PERSON> auf", "subtitles.block.dried_ghast.place_in_water": "Ausgetrockneter Ghast quillt", "subtitles.block.dried_ghast.transition": "Ausgetrocknetem Ghast geht es besser", "subtitles.block.dry_grass.ambient": "Wind waht", "subtitles.block.enchantment_table.use": "Zaubatisch wird benutzt", "subtitles.block.end_portal.spawn": "Endportal geht auf", "subtitles.block.end_portal_frame.fill": "Enderaug wird eingsetzt", "subtitles.block.eyeblossom.close": "Augnblu<PERSON> sch<PERSON>t", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON> knorrt", "subtitles.block.fire.ambient": "<PERSON><PERSON> kn<PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Glühwürmchen schwirrn", "subtitles.block.frogspawn.hatch": "Kaulquappn schlüpft", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> kn<PERSON>t", "subtitles.block.generic.break": "Block zastört", "subtitles.block.generic.fall": "Iagndwos foiit auf an Block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Block bricht", "subtitles.block.generic.place": "Block platziat", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "Pflonzn abgeschnittn", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "subtitles.block.honey_block.slide": "Honigblock oberutschn", "subtitles.block.iron_trapdoor.close": "Foitir mocht zua", "subtitles.block.iron_trapdoor.open": "Foitir geht auf", "subtitles.block.lava.ambient": "Lava blubbad", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON>", "subtitles.block.note_block.note": "<PERSON><PERSON><PERSON>", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.piston.move": "Kolbn orbeitet", "subtitles.block.pointed_dripstone.drip_lava": "Lava tropft", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava tropft in Kessl", "subtitles.block.pointed_dripstone.drip_water": "Wossa tropft", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Wossa tropft in Kessl", "subtitles.block.pointed_dripstone.land": "Stalaktit foit obe", "subtitles.block.portal.ambient": "<PERSON> rauscht", "subtitles.block.portal.travel": "Portalrauschn losst noch", "subtitles.block.portal.trigger": "Portalrauschn nimmt zua", "subtitles.block.pressure_plate.click": "Druckplot<PERSON>n klickt", "subtitles.block.pumpkin.carve": "<PERSON><PERSON>ch<PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> z<PERSON>t", "subtitles.block.respawn_anchor.ambient": "Portal wischat", "subtitles.block.respawn_anchor.charge": "Wiedereinstiegsanker aufgeladen", "subtitles.block.respawn_anchor.deplete": "Wiedereinstiegsanker verbraucht", "subtitles.block.respawn_anchor.set_spawn": "Wiedereistiegsonker gsetzt", "subtitles.block.sand.idle": "<PERSON>d waht", "subtitles.block.sand.wind": "Wind waht", "subtitles.block.sculk.charge": "Sculk brodlt", "subtitles.block.sculk.spread": "Sculk vabreitet si", "subtitles.block.sculk_catalyst.bloom": "Sculk-Katalysatoa erblüht", "subtitles.block.sculk_sensor.clicking": "Sculk-Sensor hot zum klickn ogfongt", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-Sensor hert auf zu klickn", "subtitles.block.sculk_shrieker.shriek": "Sculk-Schreia plärrt", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> geht auf", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "subtitles.block.smithing_table.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.smoker.smoke": "Räuchero<PERSON><PERSON> r<PERSON>", "subtitles.block.sniffer_egg.crack": "Schnüffla-<PERSON><PERSON> knackt", "subtitles.block.sniffer_egg.hatch": "Schnüfflajunges schlüpft", "subtitles.block.sniffer_egg.plop": "Schnüffla legt Ei", "subtitles.block.sponge.absorb": "Sch<PERSON><PERSON> soagt auf", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON> pflickt", "subtitles.block.trapdoor.close": "Foitir mocht zua", "subtitles.block.trapdoor.open": "Foitir mocht auf", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON> knarrt", "subtitles.block.trial_spawner.about_to_spawn_item": "Unheilvolla Gegnstond bereitet sich vua", "subtitles.block.trial_spawner.ambient": "Prüfungsspawner knistat", "subtitles.block.trial_spawner.ambient_charged": "Unheilvolla Prüfungsspawner knistat", "subtitles.block.trial_spawner.ambient_ominous": "Unheilvolles Knarzen\n", "subtitles.block.trial_spawner.charge_activate": "Unheil umhüllt Prüfungsspawner", "subtitles.block.trial_spawner.close_shutter": "Prüfungsspawner schliaßt", "subtitles.block.trial_spawner.detect_player": "Prüfungsspawner lodet sich auf", "subtitles.block.trial_spawner.eject_item": "Prüfungsspawner wirft Beute aus", "subtitles.block.trial_spawner.ominous_activate": "Unheil umhüt Prüfungs‐Spawner", "subtitles.block.trial_spawner.open_shutter": "Prüfungsspawner öffnet", "subtitles.block.trial_spawner.spawn_item": "Unheilvolla Gegnstond foin glossn", "subtitles.block.trial_spawner.spawn_item_begin": "Unheilvolla Gegnstond erscheint", "subtitles.block.trial_spawner.spawn_mob": "Prüfungsspawner erschofft a Geschöpf", "subtitles.block.tripwire.attach": "Stoipadroht gsponnt", "subtitles.block.tripwire.click": "Stoipadroht ausglöst", "subtitles.block.tripwire.detach": "Stoipadroht entsponnt", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "Tresor knistat", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "Tresor wirft Gegnstond aus", "subtitles.block.vault.insert_item": "Tresor entrieglt", "subtitles.block.vault.insert_item_fail": "Tresor ble<PERSON>t verriegelt", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "Tresor weist <PERSON><PERSON><PERSON><PERSON> ob", "subtitles.block.water.ambient": "Wossa fliaßt", "subtitles.block.wet_sponge.dries": "Schwomm trocknet", "subtitles.chiseled_bookshelf.insert": "Block plaziat", "subtitles.chiseled_bookshelf.insert_enchanted": "Vazaubats Buach plaziat", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON><PERSON> gnommen", "subtitles.chiseled_bookshelf.take_enchanted": "Vazaubats Buach gno", "subtitles.enchant.thorns.hit": "Dornen stechn", "subtitles.entity.allay.ambient_with_item": "Hüfsgeist suacht", "subtitles.entity.allay.ambient_without_item": "Hüfsgeist schmocht", "subtitles.entity.allay.death": "Hüfsgeist stirb", "subtitles.entity.allay.hurt": "Hüfsgeist <PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_given": "Hüfsge<PERSON> gluckst", "subtitles.entity.allay.item_taken": "Hüfsgeist hüft", "subtitles.entity.allay.item_thrown": "Hüfsgeist schleidat", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> grun<PERSON>t", "subtitles.entity.armadillo.brush": "Hornschüd obgebürstet", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON><PERSON><PERSON> schütz sich", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON><PERSON><PERSON> londet", "subtitles.entity.armadillo.peek": "Gürtltia späht", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON><PERSON><PERSON> rollt sich ein", "subtitles.entity.armadillo.scute_drop": "Gürtltier wiaft Hornschüd ob", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON><PERSON><PERSON> entrollt sich", "subtitles.entity.armadillo.unroll_start": "Gürtltia späht", "subtitles.entity.armor_stand.fall": "Rüstungsstända is umgfoin", "subtitles.entity.arrow.hit": "Pfeil trifft", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> tro<PERSON>n", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.attack": "Axolotl greift on", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.axolotl.hurt": "<PERSON>x<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.axolotl.idle_air": "Axolotl zwitschat", "subtitles.entity.axolotl.idle_water": "Axolotl zwitschat", "subtitles.entity.axolotl.splash": "Axolotl platscht", "subtitles.entity.axolotl.swim": "Axolotl schwimmb", "subtitles.entity.bat.ambient": "Fledamaus k<PERSON>cht", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.bat.takeoff": "Fledamaus fliag los", "subtitles.entity.bee.ambient": "Biene summt", "subtitles.entity.bee.death": "<PERSON><PERSON>e stirb", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.bee.loop": "Biene summt", "subtitles.entity.bee.loop_aggressive": "Biene summt grantig", "subtitles.entity.bee.pollinate": "Biene summt fröhlich", "subtitles.entity.bee.sting": "Biene sticht", "subtitles.entity.blaze.ambient": "<PERSON><PERSON> otmet", "subtitles.entity.blaze.burn": "<PERSON><PERSON> knistat", "subtitles.entity.blaze.death": "<PERSON><PERSON> stirb", "subtitles.entity.blaze.hurt": "<PERSON><PERSON> ni<PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON>", "subtitles.entity.boat.paddle_land": "Rudan", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Sumpfskellet kloppat", "subtitles.entity.bogged.death": "Sumpfskellet stiabt", "subtitles.entity.bogged.hurt": "Sumpfskelett nimmt Schodn", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON> lodet auf", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.breeze.deflect": "<PERSON><PERSON>e wehrt ob", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON> kriegt schodn", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON><PERSON> fliagt", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON> wirblt", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON> hoid <PERSON>", "subtitles.entity.breeze.jump": "Böe spring", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON> lo<PERSON>t", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON> w<PERSON>", "subtitles.entity.breeze.wind_burst": "Windkugl platzt", "subtitles.entity.camel.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON> preschat", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON> da<PERSON>t sich", "subtitles.entity.camel.death": "<PERSON><PERSON> stirb", "subtitles.entity.camel.eat": "<PERSON><PERSON> frisst", "subtitles.entity.camel.hurt": "<PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON><PERSON> setzt sich hi", "subtitles.entity.camel.stand": "<PERSON><PERSON> steht auf", "subtitles.entity.camel.step": "<PERSON><PERSON> g<PERSON>t", "subtitles.entity.camel.step_sand": "<PERSON><PERSON> la<PERSON> im Sond", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON>tz bettlt", "subtitles.entity.cat.death": "<PERSON><PERSON> stirb", "subtitles.entity.cat.eat": "<PERSON><PERSON> frisst", "subtitles.entity.cat.hiss": "<PERSON><PERSON> faucht", "subtitles.entity.cat.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON> schnurrt", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON> gackat", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.chicken.egg": "Hendl leg a Ei", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON>mm<PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.cod.flop": "Kabeljau platscht", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON> muht", "subtitles.entity.cow.death": "<PERSON><PERSON> stirb", "subtitles.entity.cow.hurt": "<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.cow.milk": "<PERSON>a wead gmolkn", "subtitles.entity.creaking.activate": "K<PERSON>aza schaut", "subtitles.entity.creaking.ambient": "Knoaza knoazt", "subtitles.entity.creaking.attack": "Knoaza attakiat", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON> beruhi<PERSON> sich", "subtitles.entity.creaking.death": "K<PERSON>aza brecklt", "subtitles.entity.creaking.freeze": "Knoaza erstarrt", "subtitles.entity.creaking.spawn": "Knoaza manifestiert", "subtitles.entity.creaking.sway": "Knoaza wead gschützt", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON><PERSON> zuckt", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON><PERSON> bewegt sich", "subtitles.entity.creeper.death": "Creeper stirb", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.creeper.primed": "C<PERSON>per z<PERSON>t", "subtitles.entity.dolphin.ambient": "Delfin zwitschat", "subtitles.entity.dolphin.ambient_water": "Delfin pfeift", "subtitles.entity.dolphin.attack": "Delfin greift on", "subtitles.entity.dolphin.death": "Delfin stirb", "subtitles.entity.dolphin.eat": "Delfin frisst", "subtitles.entity.dolphin.hurt": "Delfin nimmb <PERSON>", "subtitles.entity.dolphin.jump": "Delfin spring", "subtitles.entity.dolphin.play": "<PERSON><PERSON> spüt", "subtitles.entity.dolphin.splash": "Delfin platscht", "subtitles.entity.dolphin.swim": "Delfin schwimmb", "subtitles.entity.donkey.ambient": "Esl iaht", "subtitles.entity.donkey.angry": "E<PERSON>l wiehat", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON> u<PERSON>g", "subtitles.entity.donkey.death": "<PERSON><PERSON>l stirb", "subtitles.entity.donkey.eat": "Esl frisst", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.donkey.jump": "Esl springt", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON> gurglt", "subtitles.entity.drowned.ambient_water": "Dasoffena gua<PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON> sch<PERSON> Drei<PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON> schwi<PERSON>", "subtitles.entity.egg.throw": "<PERSON><PERSON> fliag", "subtitles.entity.elder_guardian.ambient": "G<PERSON>ßa Wächta stöhnt", "subtitles.entity.elder_guardian.ambient_land": "G<PERSON>ßa Wächta zoppet", "subtitles.entity.elder_guardian.curse": "G<PERSON>ßa Wächta vafluacht", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON> W<PERSON>cht<PERSON> stirb", "subtitles.entity.elder_guardian.flop": "G<PERSON>ßa Wächta platscht", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> W<PERSON>chta nimmb <PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON> stirb", "subtitles.entity.ender_dragon.flap": "Droch flattat", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON> faucht", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON> ni<PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON>", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "Enderaug g<PERSON>n", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> fliag", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> woopt", "subtitles.entity.enderman.death": "<PERSON><PERSON> stirb", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> schreit", "subtitles.entity.enderman.stare": "<PERSON><PERSON> schreit", "subtitles.entity.enderman.teleport": "Enderman teleportiat", "subtitles.entity.endermite.ambient": "Endermite krabblt", "subtitles.entity.endermite.death": "Endermite stirb", "subtitles.entity.endermite.hurt": "Endermite nimmb <PERSON>", "subtitles.entity.evoker.ambient": "Magier murmlt", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON> zaubat", "subtitles.entity.evoker.celebrate": "Magier freit si", "subtitles.entity.evoker.death": "Magier stirb", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.evoker.prepare_attack": "Magier bereitet Ongriff vor", "subtitles.entity.evoker.prepare_summon": "Magier bereitet Beschwörung vor", "subtitles.entity.evoker.prepare_wololo": "Magier bereitet Zauba vor", "subtitles.entity.evoker_fangs.attack": "Fongzehn schnappn zua", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON> griag", "subtitles.entity.firework_rocket.blast": "Feiawerk explodiad", "subtitles.entity.firework_rocket.launch": "Feiawerk zündet", "subtitles.entity.firework_rocket.twinkle": "Feiawerk funklt", "subtitles.entity.fish.swim": "Platschen", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON><PERSON> eighohlt", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON><PERSON> platscht", "subtitles.entity.fishing_bobber.throw": "Schwimma ausgworfn", "subtitles.entity.fox.aggro": "<PERSON>x wead beß", "subtitles.entity.fox.ambient": "<PERSON>x fiept", "subtitles.entity.fox.bite": "<PERSON><PERSON> beißt", "subtitles.entity.fox.death": "<PERSON><PERSON> stirb", "subtitles.entity.fox.eat": "<PERSON>x frisst", "subtitles.entity.fox.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.fox.screech": "<PERSON>x k<PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON> sch<PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON> spuckt", "subtitles.entity.fox.teleport": "Fux telpoatiat", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> quakt", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON> fris<PERSON>", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.long_jump": "Frosch spring", "subtitles.entity.generic.big_fall": "Iagnd<PERSON>s is owgfoin", "subtitles.entity.generic.burn": "Brenna", "subtitles.entity.generic.death": "Sterbn", "subtitles.entity.generic.drink": "Schlürfn", "subtitles.entity.generic.eat": "Essn", "subtitles.entity.generic.explode": "Explosion", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.generic.splash": "Platschn", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Windkugl plotzt", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> jammat", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> gurrt", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON> erscheint", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>t", "subtitles.entity.glow_item_frame.break": "Leich<PERSON>hm<PERSON> zabricht", "subtitles.entity.glow_item_frame.place": "Leichtrohmen platz<PERSON>t", "subtitles.entity.glow_item_frame.remove_item": "Leichtrohmen glaat", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>t", "subtitles.entity.glow_squid.ambient": "Leichttintnfisch schwimmb", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.glow_squid.hurt": "Leichttintn<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.glow_squid.squirt": "Leichttintnfisch vaspritzt Tinte", "subtitles.entity.goat.ambient": "<PERSON><PERSON> blökt", "subtitles.entity.goat.death": "<PERSON><PERSON> stirb", "subtitles.entity.goat.eat": "<PERSON><PERSON> frisst", "subtitles.entity.goat.horn_break": "Goaßhorn bricht ob", "subtitles.entity.goat.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.goat.long_jump": "<PERSON>ß hupft", "subtitles.entity.goat.milk": "<PERSON>ß wead gmolkn", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stompft", "subtitles.entity.goat.ram_impact": "Goaß romt", "subtitles.entity.goat.screaming.ambient": "Goaß plärrt", "subtitles.entity.goat.step": "<PERSON><PERSON> lafft", "subtitles.entity.guardian.ambient": "Wächta stöhnt", "subtitles.entity.guardian.ambient_land": "Wächta zoppet", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.guardian.flop": "Wächta platscht", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.happy_ghast.ambient": "Glücklicher Ghast trällert", "subtitles.entity.happy_ghast.death": "Glücklicher G<PERSON> stirbt", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "Glücklicher Ghast ist flugbereit", "subtitles.entity.happy_ghast.harness_goggles_up": "Glücklicher Ghast hält an", "subtitles.entity.happy_ghast.hurt": "Glücklicher Ghast nimm<PERSON>en", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>t", "subtitles.entity.hoglin.angry": "Hoglin grunzt vaärgat", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> greift on", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON>n wiad zombifiziat", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> ziagt si zruck", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> la<PERSON>t", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> wiehat", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> wiehat", "subtitles.entity.horse.armor": "Roßrüstung ugleg", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> schna<PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> frisst", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.horse.jump": "Roß spring", "subtitles.entity.horse.saddle": "Fok gsottlt", "subtitles.entity.husk.ambient": "Wüstnzombie stöhnt", "subtitles.entity.husk.converted_to_zombie": "<PERSON>ü<PERSON>nz<PERSON><PERSON> weat zu <PERSON>", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.husk.hurt": "<PERSON>üstnz<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.illusioner.ambient": "Illusionist mur<PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "Illusionist z<PERSON><PERSON>", "subtitles.entity.illusioner.death": "Illusionist stirb", "subtitles.entity.illusioner.hurt": "Illusionist <PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "Illusionist <PERSON><PERSON><PERSON><PERSON>ü<PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Illusionist bere<PERSON>t Blindheit vor", "subtitles.entity.illusioner.prepare_mirror": "Illusionist be<PERSON><PERSON><PERSON> Spieglbüd vor", "subtitles.entity.iron_golem.attack": "Eisngolem greift on", "subtitles.entity.iron_golem.damage": "Eisngolem zabricht", "subtitles.entity.iron_golem.death": "Eisngolem stirb", "subtitles.entity.iron_golem.hurt": "Eisngolem nimmb <PERSON>", "subtitles.entity.iron_golem.repair": "Eisngolem repariat", "subtitles.entity.item.break": "Gegnstond zabricht", "subtitles.entity.item.pickup": "Gegnstond aufkhobn", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON> au<PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON> g<PERSON>at", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON> k<PERSON>t", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> r<PERSON>t", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> a<PERSON>", "subtitles.entity.lightning_bolt.impact": "Blitzeischlog", "subtitles.entity.lightning_bolt.thunder": "<PERSON> g<PERSON>", "subtitles.entity.llama.ambient": "<PERSON> bl<PERSON>", "subtitles.entity.llama.angry": "<PERSON> bl<PERSON><PERSON> grantig", "subtitles.entity.llama.chest": "<PERSON> bepo<PERSON>t", "subtitles.entity.llama.death": "<PERSON> stirb", "subtitles.entity.llama.eat": "<PERSON> frisst", "subtitles.entity.llama.hurt": "<PERSON> ni<PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON> spuckt", "subtitles.entity.llama.step": "<PERSON> la<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Magmaw<PERSON><PERSON><PERSON> stirb", "subtitles.entity.magma_cube.hurt": "Magmawürfl nimmb <PERSON>", "subtitles.entity.magma_cube.squish": "Magmawürfl schwabblt", "subtitles.entity.minecart.inside": "Wagon klirrt", "subtitles.entity.minecart.inside_underwater": "Wagon klirrt untawossa", "subtitles.entity.minecart.riding": "<PERSON><PERSON> roit", "subtitles.entity.mooshroom.convert": "Mooshroom vawondlt sich", "subtitles.entity.mooshroom.eat": "Mooshroom frisst", "subtitles.entity.mooshroom.milk": "Mooshroom wead gmolkn", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom wead komisch gmolkn", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> wiehat", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> be<PERSON>t", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> fris<PERSON>", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda schna<PERSON>t", "subtitles.entity.panda.ambient": "Panda hechlt", "subtitles.entity.panda.bite": "Panda beißt", "subtitles.entity.panda.cant_breed": "Panda blökt", "subtitles.entity.panda.death": "Panda stirb", "subtitles.entity.panda.eat": "Panda frisst", "subtitles.entity.panda.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.panda.pre_sneeze": "Panda zsreissts gleich", "subtitles.entity.panda.sneeze": "<PERSON>da niast", "subtitles.entity.panda.step": "<PERSON><PERSON> la<PERSON>t", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON> wimmat", "subtitles.entity.parrot.ambient": "Popagei redt", "subtitles.entity.parrot.death": "Popagei stirb", "subtitles.entity.parrot.eats": "Popagei frisst", "subtitles.entity.parrot.fly": "Popagei flottert", "subtitles.entity.parrot.hurts": "Popagei nimmb <PERSON>", "subtitles.entity.parrot.imitate.blaze": "Popagei otmet", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "Popagei wirblt", "subtitles.entity.parrot.imitate.creaking": "Popagei knoazt", "subtitles.entity.parrot.imitate.creeper": "Popagei zischt", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "Popagei stöhnt", "subtitles.entity.parrot.imitate.ender_dragon": "Popagei brüllt", "subtitles.entity.parrot.imitate.endermite": "Popagei krabblt", "subtitles.entity.parrot.imitate.evoker": "Popagei murmlt", "subtitles.entity.parrot.imitate.ghast": "Popagei jammat", "subtitles.entity.parrot.imitate.guardian": "Popagei stöhnt", "subtitles.entity.parrot.imitate.hoglin": "Popagei grunzt", "subtitles.entity.parrot.imitate.husk": "Popagei stöhnt", "subtitles.entity.parrot.imitate.illusioner": "Popagei murmlt", "subtitles.entity.parrot.imitate.magma_cube": "Popagei schwabblt", "subtitles.entity.parrot.imitate.phantom": "Popagei kreischt", "subtitles.entity.parrot.imitate.piglin": "Popagei schna<PERSON>t", "subtitles.entity.parrot.imitate.piglin_brute": "Popagei schna<PERSON>t", "subtitles.entity.parrot.imitate.pillager": "Popagei murmlt", "subtitles.entity.parrot.imitate.ravager": "Popagei grunzt", "subtitles.entity.parrot.imitate.shulker": "Popagei lauat", "subtitles.entity.parrot.imitate.silverfish": "Popagei zischt", "subtitles.entity.parrot.imitate.skeleton": "Popagei klappat", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON> sch<PERSON>", "subtitles.entity.parrot.imitate.spider": "Popagei zischt", "subtitles.entity.parrot.imitate.stray": "Popagei klappat", "subtitles.entity.parrot.imitate.vex": "Popagei plogt", "subtitles.entity.parrot.imitate.vindicator": "Popagei murmlt", "subtitles.entity.parrot.imitate.warden": "Popagei sumst", "subtitles.entity.parrot.imitate.witch": "Popagei locht", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> wead beß", "subtitles.entity.parrot.imitate.wither_skeleton": "Popagei klappat", "subtitles.entity.parrot.imitate.zoglin": "Popagei grunzt", "subtitles.entity.parrot.imitate.zombie": "Popagei stöhnt", "subtitles.entity.parrot.imitate.zombie_villager": "Popagei stöhnt", "subtitles.entity.phantom.ambient": "Phantom kreischt", "subtitles.entity.phantom.bite": "<PERSON> beißt", "subtitles.entity.phantom.death": "Phantom stirb", "subtitles.entity.phantom.flap": "Phantom flattat", "subtitles.entity.phantom.hurt": "Phantom nimmb <PERSON>", "subtitles.entity.phantom.swoop": "Phantom stoaßt runta", "subtitles.entity.pig.ambient": "<PERSON><PERSON>wein grunzt", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.pig.saddle": "Sottl wiad aufgsetzt", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> bewundat G<PERSON>ond", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> schna<PERSON>t", "subtitles.entity.piglin.angry": "Piglin schnaubt vaärgat", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feiat", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> wead zombifiziat", "subtitles.entity.piglin.death": "<PERSON><PERSON> stirb", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> schna<PERSON>t ne<PERSON>", "subtitles.entity.piglin.retreat": "<PERSON>lin ziacht si zrug", "subtitles.entity.piglin.step": "<PERSON><PERSON> la<PERSON>t", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON>", "subtitles.entity.piglin_brute.angry": "Piglin-<PERSON><PERSON> schna<PERSON> vaärgat", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin-<PERSON><PERSON> wead zombifiziat", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON>-<PERSON><PERSON> stirb", "subtitles.entity.piglin_brute.hurt": "<PERSON>lin-<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON>lin-<PERSON><PERSON> la<PERSON>t", "subtitles.entity.pillager.ambient": "Plü<PERSON><PERSON> mur<PERSON>lt", "subtitles.entity.pillager.celebrate": "Plündara freit si", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "Rückstoßongriff", "subtitles.entity.player.attack.strong": "Storka Ongriff", "subtitles.entity.player.attack.sweep": "Schwungongriff", "subtitles.entity.player.attack.weak": "Schwocha Ongriff", "subtitles.entity.player.burp": "Rülpsa", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> bren<PERSON>", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> bi<PERSON>", "subtitles.entity.player.teleport": "Spiela teleportiat", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> knurrt", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON> brummt", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "Flosch zabricht", "subtitles.entity.potion.throw": "Flosch gschmissn", "subtitles.entity.puffer_fish.blow_out": "Kuglfisch schrumpft", "subtitles.entity.puffer_fish.blow_up": "Kuglfisch blost si auf", "subtitles.entity.puffer_fish.death": "Kuglfisch stirb", "subtitles.entity.puffer_fish.flop": "Kuglfisch platscht", "subtitles.entity.puffer_fish.hurt": "Kuglfisch nimmb Schodn", "subtitles.entity.puffer_fish.sting": "Kuglfisch sticht", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON> fiept", "subtitles.entity.rabbit.attack": "<PERSON><PERSON> greift on", "subtitles.entity.rabbit.death": "<PERSON>s stirb", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON> hopplt", "subtitles.entity.ravager.ambient": "Vawüsta grunzt", "subtitles.entity.ravager.attack": "Vawüsta beißt", "subtitles.entity.ravager.celebrate": "Vawüsta freit si", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.ravager.roar": "Vawüsta brüllt", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.stunned": "Vawüsta <PERSON>äub<PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON> stirb", "subtitles.entity.salmon.flop": "Lachs platscht", "subtitles.entity.salmon.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> stir<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> la<PERSON>t", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> si", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleportiat", "subtitles.entity.shulker_bullet.hit": "Shulkergschoss explodiad", "subtitles.entity.shulker_bullet.hurt": "Shulkergschoss zabricht", "subtitles.entity.silverfish.ambient": "<PERSON>üba<PERSON><PERSON> z<PERSON>t", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>mm<PERSON>", "subtitles.entity.skeleton.ambient": "Skelett klappat", "subtitles.entity.skeleton.converted_to_stray": "Skelett wead zum Eiswondara", "subtitles.entity.skeleton.death": "Skelett stirb", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.skeleton.shoot": "S<PERSON>ett schiaßt", "subtitles.entity.skeleton_horse.ambient": "Skelettroß schreit", "subtitles.entity.skeleton_horse.death": "Skelett<PERSON><PERSON> stirb", "subtitles.entity.skeleton_horse.hurt": "Skelett<PERSON>ß nimmb <PERSON>", "subtitles.entity.skeleton_horse.jump_water": "Skelettpferd springt", "subtitles.entity.skeleton_horse.swim": "Skelettpf<PERSON> schwimmb", "subtitles.entity.slime.attack": "Schleim greift on", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.slime.squish": "Schleim schwabblt", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.sniffer.digging": "Schnüffla wühlt", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON><PERSON> steht auf", "subtitles.entity.sniffer.drop_seed": "Schnüffla losst Somen foin", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.sniffer.egg_crack": "Schnüffla-<PERSON><PERSON> bricht", "subtitles.entity.sniffer.egg_hatch": "Schnüfflajunges schlüpft", "subtitles.entity.sniffer.happy": "Schnüffla gfreit si", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "Schnü<PERSON><PERSON> g<PERSON>t", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON><PERSON> wittert", "subtitles.entity.sniffer.searching": "Schnüff<PERSON> su<PERSON>t", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.death": "Schneegolem stirb", "subtitles.entity.snow_golem.hurt": "Sc<PERSON><PERSON>gol<PERSON> nimmb <PERSON>", "subtitles.entity.snowball.throw": "Schneeboi fliag", "subtitles.entity.spider.ambient": "<PERSON><PERSON> z<PERSON>", "subtitles.entity.spider.death": "Spinn stirb", "subtitles.entity.spider.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.squid.ambient": "Tintnfisch schwimmb", "subtitles.entity.squid.death": "Tintnfisch stirb", "subtitles.entity.squid.hurt": "Tintnfisch nimmb <PERSON>", "subtitles.entity.squid.squirt": "Tintnfisch vaspritzt Tintn", "subtitles.entity.stray.ambient": "Eiswondara k<PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.strider.happy": "Schreita trällat", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON> zirpt", "subtitles.entity.strider.retreat": "Schreita weicht zrug", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.tadpole.flop": "Kaulquappn platscht", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON><PERSON><PERSON> wochst", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.tnt.primed": "TNT zischt", "subtitles.entity.tropical_fish.death": "Tropnfisch stirb", "subtitles.entity.tropical_fish.flop": "Tropnfisch zopplt", "subtitles.entity.tropical_fish.hurt": "Tropnfisch nimmb <PERSON>", "subtitles.entity.turtle.ambient_land": "Schüdkrötn fiept", "subtitles.entity.turtle.death": "Schüdkrötn stirb", "subtitles.entity.turtle.death_baby": "Schüdkrötnjunges stirb", "subtitles.entity.turtle.egg_break": "Schüdkrötnei zabricht", "subtitles.entity.turtle.egg_crack": "Schüdkrötnei bricht", "subtitles.entity.turtle.egg_hatch": "Schüdkrötnei schlüpft", "subtitles.entity.turtle.hurt": "Schüdkrötn nimmb <PERSON>", "subtitles.entity.turtle.hurt_baby": "Schüdkrötnjunges nimmb <PERSON>", "subtitles.entity.turtle.lay_egg": "Schüdkrötn leg Ei", "subtitles.entity.turtle.shamble": "Schüdkrötn watschlt", "subtitles.entity.turtle.shamble_baby": "Schüdkrötnjunges watschlt", "subtitles.entity.turtle.swim": "Schüdkrötn schwimmb", "subtitles.entity.vex.ambient": "Plogegeist plogt", "subtitles.entity.vex.charge": "Plogegeist schreit", "subtitles.entity.vex.death": "Plogegeist stirb", "subtitles.entity.vex.hurt": "Plogegeist <PERSON><PERSON><PERSON>", "subtitles.entity.villager.ambient": "Dorfbewohna mur<PERSON>lt", "subtitles.entity.villager.celebrate": "Dorfbewohna freit si", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stirb", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.villager.no": "Dorfbewohna lehnt o", "subtitles.entity.villager.trade": "Dorfbewohna hondlt", "subtitles.entity.villager.work_armorer": "Rüstungsschmied orbeitet", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON>a orbei<PERSON>", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON> or<PERSON>", "subtitles.entity.villager.work_farmer": "Bauer orbeitet", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "Pfeilmocha orbeitet", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> or<PERSON>", "subtitles.entity.villager.work_librarian": "Bibliothekar orbeitet", "subtitles.entity.villager.work_mason": "<PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "Schäfa orbeitet", "subtitles.entity.villager.work_toolsmith": "G<PERSON>bschmied orbeitet", "subtitles.entity.villager.work_weaponsmith": "Woffnschmied orbeitet", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stimmb zua", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.vindicator.celebrate": "<PERSON>na freit si", "subtitles.entity.vindicator.death": "<PERSON><PERSON> stirb", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON>", "subtitles.entity.wandering_trader.ambient": "Wondanda Händla mua<PERSON>lt", "subtitles.entity.wandering_trader.death": "Wondanda Hä<PERSON> stirb", "subtitles.entity.wandering_trader.disappeared": "Fohrenda Händla v<PERSON>ch<PERSON>t", "subtitles.entity.wandering_trader.drink_milk": "Fohrenda Händla trinkt Müch", "subtitles.entity.wandering_trader.drink_potion": "Fohrenda Händla trinkt an Tronk", "subtitles.entity.wandering_trader.hurt": "Wondanda Hä<PERSON><PERSON>", "subtitles.entity.wandering_trader.no": "Wondanda Händla lehnt o", "subtitles.entity.wandering_trader.reappeared": "Fohrenda Händ<PERSON>", "subtitles.entity.wandering_trader.trade": "Wondanda Händla hondlt", "subtitles.entity.wandering_trader.yes": "Wondanda Händla stimmb zua", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON> knurrt grantig", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON>t", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON> haust", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON> londt an Treffa", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> grobt", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON> kimb", "subtitles.entity.warden.heartbeat": "Wärtaherz schlog", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> hoacht auf", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> hoacht grantig auf", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON> n<PERSON>t si", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON> rück<PERSON> vor", "subtitles.entity.warden.nearby_closest": "<PERSON><PERSON><PERSON> naht", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON> schnu<PERSON>t", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> knollt", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> lodt auf", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON> la<PERSON>t", "subtitles.entity.warden.tendril_clicks": "Wärtarankn klickn", "subtitles.entity.wind_charge.throw": "Windkugl fliagt", "subtitles.entity.wind_charge.wind_burst": "Windkugl plotzt", "subtitles.entity.witch.ambient": "<PERSON><PERSON> locht", "subtitles.entity.witch.celebrate": "Hex freit si", "subtitles.entity.witch.death": "Hex stirb", "subtitles.entity.witch.drink": "Hex trinkt", "subtitles.entity.witch.hurt": "<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON> sch<PERSON>", "subtitles.entity.wither.ambient": "Wither wead beß", "subtitles.entity.wither.death": "Wither stirb", "subtitles.entity.wither.hurt": "<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON>er greift on", "subtitles.entity.wither.spawn": "<PERSON><PERSON> ent<PERSON><PERSON>lt", "subtitles.entity.wither_skeleton.ambient": "Witherskelett klappat", "subtitles.entity.wither_skeleton.death": "Withers<PERSON><PERSON> stirb", "subtitles.entity.wither_skeleton.hurt": "Withers<PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON> he<PERSON>", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON> knurrt", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON> he<PERSON>", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON> s<PERSON> si", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON>lt", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>zt", "subtitles.entity.zoglin.angry": "Zoglin grunzt vaärgat", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> greift on", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> la<PERSON>t", "subtitles.entity.zombie.ambient": "<PERSON> stöhnt", "subtitles.entity.zombie.attack_wooden_door": "Tir wacklt", "subtitles.entity.zombie.break_wooden_door": "Tir bricht", "subtitles.entity.zombie.converted_to_drowned": "Zombie weat zu <PERSON>", "subtitles.entity.zombie.death": "Zombie stirb", "subtitles.entity.zombie.destroy_egg": "Schüdkrötnei zatretn", "subtitles.entity.zombie.hurt": "<PERSON> nimmb <PERSON>", "subtitles.entity.zombie.infect": "Zombie infiziat", "subtitles.entity.zombie_horse.ambient": "Zombieroß schreit", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> stirb", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> nimmb <PERSON>", "subtitles.entity.zombie_villager.ambient": "Dorfbewohnazombie stöhnt", "subtitles.entity.zombie_villager.converted": "Dorfbewohnazombie blährt", "subtitles.entity.zombie_villager.cure": "Dorfbewohnazombie riacht", "subtitles.entity.zombie_villager.death": "Dorfbewohnazombie stirb", "subtitles.entity.zombie_villager.hurt": "Dorfbewohnazombie nimmb <PERSON>", "subtitles.entity.zombified_piglin.ambient": "Zombifiziata <PERSON> g<PERSON>t", "subtitles.entity.zombified_piglin.angry": "Zombifiziata Piglin grunzt vaärgat", "subtitles.entity.zombified_piglin.death": "Zombifizia<PERSON> stirb", "subtitles.entity.zombified_piglin.hurt": "Zombifiziata <PERSON> nimmb <PERSON>", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON>il erg<PERSON>", "subtitles.event.mob_effect.raid_omen": "Überfoi kemt auf di zua", "subtitles.event.mob_effect.trial_omen": "Unheilvolle Prüfung kumt auf di zua", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON><PERSON> blärrt", "subtitles.item.armor.equip": "Ausrüstung ugleg", "subtitles.item.armor.equip_chain": "Kettnrüstung rasslt", "subtitles.item.armor.equip_diamond": "Diamantrüstung klirrt", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> raschln", "subtitles.item.armor.equip_gold": "Goidrüstung klimpat", "subtitles.item.armor.equip_iron": "Eisnrüstung scheppat", "subtitles.item.armor.equip_leather": "Ledarüstung knirscht", "subtitles.item.armor.equip_netherite": "Netheritrüstung scheppat", "subtitles.item.armor.equip_turtle": "Schildkrötnponza ongleg", "subtitles.item.armor.equip_wolf": "Wolf<PERSON>za is wieda guat", "subtitles.item.armor.unequip_wolf": "Wolfspanzer obgenomma", "subtitles.item.axe.scrape": "<PERSON><PERSON>t schobt", "subtitles.item.axe.strip": "<PERSON><PERSON>t schobt", "subtitles.item.axe.wax_off": "Entwochst", "subtitles.item.bone_meal.use": "Knochnmöh gries<PERSON>t", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON> rasch<PERSON>", "subtitles.item.book.put": "Buch klotscht", "subtitles.item.bottle.empty": "Floschn ausglaat", "subtitles.item.bottle.fill": "<PERSON><PERSON>ch gfüt", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON>", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON> ab<PERSON> abgschlossa", "subtitles.item.brush.brushing.sand": "<PERSON> ab<PERSON>", "subtitles.item.brush.brushing.sand.complete": "<PERSON> abpinsla abgschlossa", "subtitles.item.bucket.empty": "<PERSON><PERSON> au<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON> au<PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl aufgnumma", "subtitles.item.bucket.fill_fish": "Fisch eigfongen", "subtitles.item.bucket.fill_tadpole": "Kaulquappn eigfongen", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON> ausglaat", "subtitles.item.bundle.insert": "Gegnstond eipockt", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON> voii", "subtitles.item.bundle.remove_one": "Gegnstond auspockt", "subtitles.item.chorus_fruit.teleport": "Spiela teleportiat", "subtitles.item.crop.plant": "Pflonzn pflonzt", "subtitles.item.crossbow.charge": "Oambrust sponnt", "subtitles.item.crossbow.hit": "Pfeil trifft", "subtitles.item.crossbow.load": "Oambrust lodt", "subtitles.item.crossbow.shoot": "Oamb<PERSON> s<PERSON>", "subtitles.item.dye.use": "Forbstoff f<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON> fliagt", "subtitles.item.firecharge.use": "Feiakugl zischt", "subtitles.item.flintandsteel.use": "Feiazeig zint", "subtitles.item.glow_ink_sac.use": "Leichttintbeitl tropft", "subtitles.item.goat_horn.play": "Goaßhorn spüt", "subtitles.item.hoe.till": "<PERSON><PERSON> pflügt", "subtitles.item.honey_bottle.drink": "Sc<PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "Wochsn", "subtitles.item.horse_armor.unequip": "Pfeadlpanzer obgnumma", "subtitles.item.ink_sac.use": "Tintnbeitl tropft", "subtitles.item.lead.break": "<PERSON><PERSON> g<PERSON>", "subtitles.item.lead.tied": "Testkonstruktionsgreß", "subtitles.item.lead.untied": "<PERSON><PERSON>n", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON>ich abgestreift", "subtitles.item.lodestone_compass.lock": "<PERSON><PERSON><PERSON><PERSON> is auf Leitstoa grichtet woan", "subtitles.item.mace.smash_air": "Streitkoiben zerschmettert", "subtitles.item.mace.smash_ground": "<PERSON><PERSON><PERSON><PERSON> zertrümmert", "subtitles.item.nether_wart.plant": "Plonzn gepflonz", "subtitles.item.ominous_bottle.dispose": "Flosche bricht", "subtitles.item.saddle.unequip": "Sattel abgestreift", "subtitles.item.shears.shear": "<PERSON><PERSON> schneid", "subtitles.item.shears.snip": "<PERSON><PERSON> sch<PERSON>it", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON>d wehrt o", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON><PERSON> planiat", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON><PERSON> foht ein", "subtitles.item.spyglass.use": "Binoggl foht aus", "subtitles.item.totem.use": "Totem ausglöst", "subtitles.item.trident.hit": "Dreizock spießt auf", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON><PERSON> vibriat", "subtitles.item.trident.return": "Dreizock kehrt zrugg", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ht", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON><PERSON> klirrt", "subtitles.item.trident.thunder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.item.wolf_armor.break": "Wolfpanza is hin", "subtitles.item.wolf_armor.crack": "Wolfpanza is hin", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON> is a bisi zafetzt", "subtitles.item.wolf_armor.repair": "Wolf<PERSON>za is wieda guat", "subtitles.particle.soul_escape": "<PERSON><PERSON> ent<PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON><PERSON> zei<PERSON>", "subtitles.ui.hud.bubble_pop": "Luftvorrot nimmt ob", "subtitles.ui.loom.take_result": "Webstui g<PERSON>t", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.weather.rain": "<PERSON><PERSON> p<PERSON>lt", "symlink_warning.message": "Wötn aus Ordna mit symbolische Verknüpfungen zum Lodn is gor ned so ungfährlich wenn du koan Tau host wos du tuast. Bitte schau auf %s vorbei, donn bisd gscheida.", "symlink_warning.message.pack": "Wötn aus Ordna mit symbolische Verknüpfungen zum Lodn is gor ned so ungfährlich wenn du koan Tau host wos du tuast. Bitte schau auf %s vorbei, donn bisd gscheida.", "symlink_warning.message.world": "Wötn aus Ordna mit symbolische Verknüpfungen zum Lodn is gor ned so ungfährlich wenn du koan Tau host wos du tuast. Bitte schau auf %s vorbei, donn bisd gscheida.", "symlink_warning.more_info": "Waitare Informationen", "symlink_warning.title": "Wödordna hot symbolische Verknüpfungen", "symlink_warning.title.pack": "Hinzuagefuakte(s) Pakete(e) enthält/enthalten symboalische Verknuapfungen", "symlink_warning.title.world": "Wödordna hot symbolische Verknüpfungen", "team.collision.always": "Imma", "team.collision.never": "<PERSON><PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON><PERSON>", "team.collision.pushOwnTeam": "Schiab eigene Teammitglieda", "team.notFound": "Unbekonnts Team '%s'", "team.visibility.always": "Imma", "team.visibility.hideForOtherTeams": "Fia ondare Teams vasteckn", "team.visibility.hideForOwnTeam": "<PERSON><PERSON> as eigene Team vasteckn", "team.visibility.never": "<PERSON><PERSON>", "telemetry.event.advancement_made.description": "Zum Wissn wia Spiela Errungeschoftn freischoitn ku ins höfn, den Spüverlauf bessa zum Vasteh und weita zu vabessern.", "telemetry.event.advancement_made.title": "Foatschritt aziehlt", "telemetry.event.game_load_times.description": "Des Ereignis ku uns höfn herauszumfindn, wo es Vabesserungen vom Startvorgang braucht, indem es de Ausführungszeitn vo de oanzelnen Startphasn misst.", "telemetry.event.game_load_times.title": "Lade zit vom Spiel", "telemetry.event.optional": "%s (optional)", "telemetry.event.optional.disabled": "%s (optional) - deaktiviad", "telemetry.event.performance_metrics.description": "De genaue Ermittlung vom allgemeinen Leistungsprofil vo Minecraft hüft uns, unsa Spü an möglichst viele Endgeräte und Betriebssysteme onzupassn und dafia zu optimiern.\nDe Spüversion in da Metrik hüft uns, as Leistungsprofil mit neie Versionen vo Minecraft zu vergleichn.", "telemetry.event.performance_metrics.title": "Leistungsmetrik", "telemetry.event.required": "%s (brauchts)", "telemetry.event.world_load_times.description": "Es is wichtig fia uns zu versteh, wia lang es dauert, a <PERSON><PERSON><PERSON> zu betretn und wia sich des üba de Zeit ändat. Wenn mia zum Beispiel neie Spüelemente hinzufügn oda größere technische Änderungen mochn, miasn mia schaun, wos des fia Auswirkungen auf de Lodezeitn hot.", "telemetry.event.world_load_times.title": "Wödlodezeitn", "telemetry.event.world_loaded.description": "<PERSON><PERSON>, <PERSON>ia <PERSON>craft spün (z. B. Spümodus, Spüversion und modifiziata Client bzw. Server), mochts möglich, dass mia Spüaktualisierungen auf de Bereiche ausrichtn, de eana wichtig san.\nAs <PERSON><PERSON><PERSON>is „Wöd glodn“ is mit „Wöd entlodn“ koppelt, damit ma berechnen kinan, wia lang oa Spüsitzung dauert hot.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_unloaded.description": "<PERSON> Ereignis is mitn <PERSON><PERSON><PERSON><PERSON> „Wöd glodn“ kop<PERSON><PERSON>, damit ma berechnen kinan, wia long oa Wödsitzung dauert hot.\nWenn a Wödsitzung vorbei is, wead de <PERSON> (in Sekundn und Ticks) gmessn (wennst zrugg ins Hauptmenü gehst bzw. de Vabindung zu an Server trennst).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ticks)", "telemetry.property.advancement_id.title": "Foatschritt ID", "telemetry.property.client_id.title": "Client‐ID", "telemetry.property.client_modded.title": "Client modifiziat", "telemetry.property.dedicated_memory_kb.title": "Zug<PERSON><PERSON><PERSON> (KB)", "telemetry.property.event_timestamp_utc.title": "Zeitpunkt vom Ereignis (koordinierte Wödzeit)", "telemetry.property.frame_rate_samples.title": "Datnwerte FPS", "telemetry.property.game_mode.title": "Spümodus", "telemetry.property.game_version.title": "Spüversion", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Initialisierungszeit (Millisekundn)", "telemetry.property.load_time_loading_overlay_ms.title": "Zit im Ladebildschirm (Millisekunda)", "telemetry.property.load_time_pre_window_ms.title": "Zit befor sich s Feans<PERSON>net (Millisekunda)", "telemetry.property.load_time_total_time_ms.title": "Gsamte Ladezit (Millisekunda)", "telemetry.property.minecraft_session_id.title": "Minecraft‐Sitzungs‐ID", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON>", "telemetry.property.number_of_samples.title": "Onzoi Datnwerte", "telemetry.property.operating_system.title": "Betriebssystem", "telemetry.property.opt_in.title": "Erforderlichs/optionals <PERSON><PERSON>", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Realms Wealtinhält (Minispiel Name)", "telemetry.property.render_distance.title": "Sichtweitn", "telemetry.property.render_time_samples.title": "Datnwerte Renderzeit", "telemetry.property.seconds_since_load.title": "Zeit seit's Glodn hot (Sekundn)", "telemetry.property.server_modded.title": "Server modifiziat", "telemetry.property.server_type.title": "Servertyp", "telemetry.property.ticks_since_load.title": "Zeit seit's Glodn hot (Ticks)", "telemetry.property.used_memory_samples.title": "Vabra<PERSON><PERSON>tsspei<PERSON>", "telemetry.property.user_id.title": "Nutza‐ID", "telemetry.property.world_load_time_ms.title": "Wödlodezeit (Millisekundn)", "telemetry.property.world_session_id.title": "Wöd‐Sitzungs‐ID", "telemetry_info.button.give_feedback": "Feedback gebn", "telemetry_info.button.privacy_statement": "Doatenschuatzerkläerung", "telemetry_info.button.show_data": "<PERSON><PERSON> aufmochn", "telemetry_info.opt_in.description": "I stimm da Ibamittlung optionala Telemetriedatn zua", "telemetry_info.property_title": "Inkludierte Datn", "telemetry_info.screen.description": "De Erhebung von deana <PERSON> hüft uns, Minecraft bessa zu mochn, indem mia uns damit in de Richtungen weisn lossn, de unsere Spieler wichtig san. Du konnst a zusätzlichs Feedback obgebn, damit mia Minecraft weida bessa mochn kinan.", "telemetry_info.screen.title": "Telemetrie‐Datnerhebung", "test.error.block_property_mismatch": "Zustand %s wurde als %s erwartet, war aber %s", "test.error.block_property_missing": "Blockeignschoftn Föhn, Eigneschoft %s siot %s sei", "test.error.entity_property": "Objekt %s erfüllte Test nicht: %s", "test.error.entity_property_details": "Objekt %s hat Test %s nicht erfüllt: %s erwartet, war aber %s", "test.error.expected_block": "Block %s eawoatet, woa oba %s", "test.error.expected_block_tag": "Block woa in %s eawoatet, woa oba %s", "test.error.expected_container_contents": "Behöta soit %s enthoitn", "test.error.expected_container_contents_single": "Behöta soit oa %s enthoitn", "test.error.expected_empty_container": "Behö<PERSON> soit la sei", "test.error.expected_entity": "%s erwoatet", "test.error.expected_entity_around": "%s soit bei %s, %s, %s sei", "test.error.expected_entity_count": "%s Objekte vom Typ %s erwartet, %s gefunden", "test.error.expected_entity_data": "Objektdaten %s wurden erwartet, waren aber %s", "test.error.expected_entity_data_predicate": "Objektdaten für %s stimmen nicht überein", "test.error.expected_entity_effect": "Effekt für %s wurde als %s %s erwartet", "test.error.expected_entity_having": "Objektinventar hätte %s enthalten sollen", "test.error.expected_entity_holding": "Objekt hätte %s halten sollen", "test.error.expected_entity_in_test": "Erwartete %s im Test", "test.error.expected_entity_not_touching": "%s is ned ois %s, %s, %s ongrenzend eawoatet (relativ: %s, %s, %s)", "test.error.expected_entity_touching": "%s wiad ois on %s, %s, %s ongrenzend eawoatet (relativ: %s, %s, %s)", "test.error.expected_item": "Teil vom typ %s eawoatet", "test.error.expected_items_count": "%s Gegenstände vom Typ %s erwartet, aber %s gefunden", "test.error.fail": "Fehlkriterien erfüllt", "test.error.invalid_block_type": "%s bei %s, %s, %s (relativ: %s, %s, %s) in Tick %s", "test.error.missing_block_entity": "Fehlendes Blockobjekt", "test.error.position": "%s bei %s, %s, %s (relativ: %s, %s, %s) in Tick %s", "test.error.sequence.condition_already_triggered": "Bedingung bereits in Tick %s ausgelöst", "test.error.sequence.condition_not_triggered": "Bedingung nicht ausgelöst", "test.error.sequence.invalid_tick": "In ungültigem Tick stattgefunden: %s wurde erwartet", "test.error.sequence.not_completed": "Test vor Abschluss des Vorgangs abgelaufen", "test.error.set_biome": "Biom für Test konnte nicht festgelegt werden", "test.error.spawn_failure": "Objekt %s konnte nicht erzeugt werden", "test.error.state_not_equal": "Folscha Zuastond. %s erwoat, woa %s", "test.error.structure.failure": "Testkonstruktion fia %s hot ned platziert wean kenna", "test.error.tick": "%s in Tick %s", "test.error.ticking_without_structure": "Testausführung hot ongfongan, bevor die Konstruktion platziert wordn is", "test.error.timeout.no_result": "Weda Erfolg noch Fehlschlog innerholb von %s Ticks", "test.error.timeout.no_sequences_finished": "<PERSON>läufe innerholb von %s Ticks obgschlossn", "test.error.too_many_entities": "Nur a %s um %s, %s, %s erwoat, owa %s gfundn", "test.error.unexpected_block": "Block is ned ols %s erwoat woan", "test.error.unexpected_entity": "<PERSON> erwoat, %s vorzfindn", "test.error.unexpected_item": "Gegnstond vom Typ %s is ned erwoat woan", "test.error.unknown": "Unbekonnta interna Fehla: %s", "test.error.value_not_equal": "%s is ols %s erwoat woan, woa oba %s", "test.error.wrong_block_entity": "Folscha Blockobjekttyp: %s", "test_block.error.missing": "Testkonstruktion hot kan %s-Block", "test_block.error.too_many": "Zu vü %s-Bleck", "test_block.invalid_timeout": "Ungültigs Timeout (%s) - muaß a positive <PERSON><PERSON><PERSON> von T<PERSON> sein", "test_block.message": "Nochricht:", "test_block.mode.accept": "<PERSON><PERSON><PERSON>", "test_block.mode.fail": "Fehlgschlogn", "test_block.mode.log": "Protokoll", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Akzeptonzmodus - Nimmb (teilweisn) Erf<PERSON><PERSON> von an Test entgegn", "test_block.mode_info.fail": "Fehlamodus - Losst an Test fehlschlogn", "test_block.mode_info.log": "Protokollmodus - Protokollier a Nochricht", "test_block.mode_info.start": "Startmodus - <PERSON> von an <PERSON>", "test_instance.action.reset": "Zrucksetzn und lonan", "test_instance.action.run": "lonan und ausfian", "test_instance.action.save": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "test_instance.description.batch": "Teil: %s", "test_instance.description.failed": "Fehlgschlogn: %s", "test_instance.description.function": "Funktion: %s", "test_instance.description.invalid_id": "Ungültige Test‐ID", "test_instance.description.no_test": "Dieser Test existiert nicht", "test_instance.description.structure": "Konstruktion: %s", "test_instance.description.type": "Typ: %s", "test_instance.type.block_based": "Blockbasierter Test", "test_instance.type.function": "Integrierter Funktionstest", "test_instance_block.entities": "Objekte:", "test_instance_block.error.no_test": "Testinstonz bei %s, %s, %s konn ned ausgführt wean, wal ihra koa Test zugwiesn is", "test_instance_block.error.no_test_structure": "Testinstonz bei %s, %s, %s konn ned ausgführt wean, wal ihra koa Test zugwiesn is", "test_instance_block.error.unable_to_save": "Testkonstruktionsvoalog füa Testiinstonz bei %s,%s,%s ko ned gschpeichat wean", "test_instance_block.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test_instance_block.reset_success": "Test %s is eafoikreich zruckgestetzt", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Testkonstruktionsgreß", "test_instance_block.starting": "Test %s wird gestartet", "test_instance_block.test_id": "Testinstanz‐ID", "title.32bit.deprecation": "32-Bit System gefunden: des wird di vielleicht vom spüln in de Zukunft abhalten als a 64-Bit System wird benötigt!", "title.32bit.deprecation.realms": "Minecraft wird bald a 64-Bit System gebrauchen, welches di vom spülen oder vom benutzen von Realms auf diesem Gerät abhaltet.", "title.32bit.deprecation.realms.check": "Zag diesn Büldschiam nimma on", "title.32bit.deprecation.realms.header": "32-bit-System erkonnt", "title.credits": "Copyright Mojang AB. Ned weitavabreitn!", "title.multiplayer.disabled": "Multiplayer is deaktiviat. Bitte schau bei deinen Microsoft-Accout Einstellungen noch.", "title.multiplayer.disabled.banned.name": "Du muast dein Profilnom ändan, bevoa du online spün kust", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON> is fia imma vom Online-Spü ausgschlossn", "title.multiplayer.disabled.banned.temporary": "<PERSON><PERSON> is daweil vom Online-Spü ausgschlossn", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Drittonbieta-Server)", "title.multiplayer.realms": "Mehrspiela (Realms)", "title.singleplayer": "Singleplayer", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s nuamoi %s und %1$s endlich %s und a %1$s nuamoi!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "Hi %", "translation.test.invalid2": "Hi %s", "translation.test.none": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>!", "translation.test.world": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.amethyst": "Amethyst", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragd", "trim_material.minecraft.gold": "Gold", "trim_material.minecraft.iron": "Isa", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Netherit", "trim_material.minecraft.quartz": "Quartz", "trim_material.minecraft.redstone": "Redstone", "trim_material.minecraft.resin": "Hoazmaterial", "trim_pattern.minecraft.bolt": "Bolzn-Rüstungssotz", "trim_pattern.minecraft.coast": "Strond-Rüstungssotz", "trim_pattern.minecraft.dune": "Dünen-Rüstungssotz", "trim_pattern.minecraft.eye": "Augn-Rü<PERSON>ungssotz", "trim_pattern.minecraft.flow": "Fluss-Rüstungssotz", "trim_pattern.minecraft.host": "Gostwirts-Rüstungssotz", "trim_pattern.minecraft.raiser": "Aufzieha-Rüstungssotz", "trim_pattern.minecraft.rib": "Rippn-Rüstungssotz", "trim_pattern.minecraft.sentry": "Wochn-Rüstungssotz", "trim_pattern.minecraft.shaper": "Künstla-Rüstungssotz", "trim_pattern.minecraft.silence": "Stille-Rüstungssotz", "trim_pattern.minecraft.snout": "Schnauzn-Rüstungssotz", "trim_pattern.minecraft.spire": "Turmspitzn-Rüstungssotz", "trim_pattern.minecraft.tide": "Gezeitn-Rüstungssotz", "trim_pattern.minecraft.vex": "Plogegeista-Rüstungssotz", "trim_pattern.minecraft.ward": "Wärta-Rüstungssotz", "trim_pattern.minecraft.wayfinder": "Wegfinda-Rüstungssotz", "trim_pattern.minecraft.wild": "Wildnis-Rüstungssotz", "tutorial.bundleInsert.description": "Rechtsklick um Gegenstände hinzuzufügen", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON> heagnumma", "tutorial.craft_planks.description": "As Rezeptbuach hüft da", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON> hea", "tutorial.find_tree.description": "<PERSON>hlog eam fia Hoiz", "tutorial.find_tree.title": "Find an Bam", "tutorial.look.description": "Benutz dei Maus", "tutorial.look.title": "<PERSON><PERSON><PERSON> <PERSON>", "tutorial.move.description": "Spring mit %s", "tutorial.move.title": "Beweg di mit %s, %s, %s und %s", "tutorial.open_inventory.description": "Druck %s", "tutorial.open_inventory.title": "Moch dei Inventar auf", "tutorial.punch_tree.description": "Hoit %s druckt", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON> an <PERSON>", "tutorial.socialInteractions.description": "Drück %s zum öffnen", "tutorial.socialInteractions.title": "Soziale Intaaktionen", "upgrade.minecraft.netherite_upgrade": "Netheritaufwertung"}