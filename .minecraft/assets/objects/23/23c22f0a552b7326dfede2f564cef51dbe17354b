{"accessibility.onboarding.accessibility.button": "Pengaturan Aksesibilitas...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON> enter untuk mengaktifkan narator", "accessibility.onboarding.screen.title": "Selamat datang di Minecraft!\n\nApakah Anda ingin mengaktifkan Narator atau mengunjungi Pengaturan Aksesibilitas?", "addServer.add": "Se<PERSON><PERSON>", "addServer.enterIp": "<PERSON><PERSON><PERSON>", "addServer.enterName": "<PERSON><PERSON>", "addServer.resourcePack": "<PERSON><PERSON> Peladen", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "Diaktifkan", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.title": "Ubah Info Peladen", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Mode", "advMode.mode.auto": "Berulang", "advMode.mode.autoexec.bat": "Selalu Aktif", "advMode.mode.conditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstone": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Perlu Redstone", "advMode.mode.sequence": "Berantai", "advMode.mode.unconditional": "<PERSON><PERSON>", "advMode.notAllowed": "Hanya pemain operator di Mode Kreatif", "advMode.notEnabled": "Balok Perintah tidak diaktifkan pada peladen ini", "advMode.previousOutput": "Keluaran Sebelumnya", "advMode.setCommand": "Tetapkan Perintah Konsol untuk Balok", "advMode.setCommand.success": "Perintah diatur: %s", "advMode.trackOutput": "Lacak keluaran", "advMode.triggering": "Memicu", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Kemajuan tidak dikenal: %s", "advancements.adventure.adventuring_time.description": "Temukan set<PERSON> bioma", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.arbalistic.description": "Bunuh lima makhluk berbeda menggunakan satu tembakan busur silang", "advancements.adventure.arbalistic.title": "Sang Penembak Busur Silang", "advancements.adventure.avoid_vibration.description": "Seluduk di dekat Sensor Sculk atau Penunggu untuk mencegahnya mendeteksi Anda", "advancements.adventure.avoid_vibration.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.blowback.description": "<PERSON><PERSON><PERSON> dengan <PERSON> tembakan <PERSON>wana yang dipantulkan", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Gunakan <PERSON> pada Armadilo untuk mendapatkan Skutum Armadilo", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "advancements.adventure.bullseye.description": "Tembak tepat sasaran balok Sasaran setidaknya dari jarak 30 meter", "advancements.adventure.bullseye.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Buat Pasu dari 4 Serpihan Pasu", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON><PERSON>hati", "advancements.adventure.crafters_crafting_crafters.description": "Berada di dekat <PERSON> yang sedang membuat <PERSON>", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON>", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON><PERSON> bebas dari puncak dunia (batas pembangunan) sampai ke bagian terendah dunia dan selamat", "advancements.adventure.fall_from_world_height.title": "Gua & Tebing", "advancements.adventure.heart_transplanter.description": "Jajarkan <PERSON> (dengan arah yang benar) di antara dua Gelondong Ek Pucat", "advancements.adventure.heart_transplanter.title": "Pencangkok Jantung", "advancements.adventure.hero_of_the_village.description": "<PERSON><PERSON><PERSON><PERSON> desa dari serbuan", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Lompat ke Balok Madu untuk menghambat jatuh Anda", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "Bunuh monster jahat apa pun", "advancements.adventure.kill_a_mob.title": "Pemburu Monster", "advancements.adventure.kill_all_mobs.description": "Bunuh setiap monster jahat", "advancements.adventure.kill_all_mobs.title": "Monster Diburu", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Bunuh makhluk di dekat Katalisator Sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON>", "advancements.adventure.lighten_up.description": "Kikis <PERSON> Tembaga dengan Kapak untuk membuatnya lebih terang", "advancements.adventure.lighten_up.title": "Terangkanlah", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Lindungi Penduduk dari petir tanpa menimbulkan kebakaran", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Pelindung Lonjakan Arus", "advancements.adventure.minecraft_trials_edition.description": "Melangkah ke dalam Bilik Cobaan", "advancements.adventure.minecraft_trials_edition.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "Tembak Busur Silang", "advancements.adventure.ol_betsy.title": "<PERSON>", "advancements.adventure.overoverkill.description": "<PERSON><PERSON><PERSON> serangan sebesar 50 hati dalam satu bantingan Gada", "advancements.adventure.overoverkill.title": "Berlebih-lebih", "advancements.adventure.play_jukebox_in_meadows.description": "Hidupkan Padang Rumput dengan suara musik dari <PERSON>gu", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Baca sinyal daya dari <PERSON> Pahatan menggunakan Pembanding", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON>", "advancements.adventure.revaulting.description": "Buka Brankas Mengerikan menggunakan Kunci Cobaan Mengerikan", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON>, menjelajah, dan bertarung", "advancements.adventure.root.title": "Petualangan", "advancements.adventure.salvage_sherd.description": "Sikat balok mencurigakan untuk mendapatkan Serpihan Pasu", "advancements.adventure.salvage_sherd.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.shoot_arrow.description": "Tembak sesuatu dengan <PERSON>", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "Tidur di Tempat Tidur untuk mengubah titik bangkit Anda", "advancements.adventure.sleep_in_bed.title": "Tidur Nyenyak", "advancements.adventure.sniper_duel.description": "<PERSON><PERSON><PERSON> dari jarak setida<PERSON>nya 50 meter", "advancements.adventure.sniper_duel.title": "Tanding Tembak", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON>", "advancements.adventure.spyglass_at_dragon.title": "<PERSON><PERSON><PERSON><PERSON> Itu P<PERSON>awa<PERSON>?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON>", "advancements.adventure.spyglass_at_ghast.title": "Apakah Itu Balon?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON>", "advancements.adventure.spyglass_at_parrot.title": "<PERSON><PERSON><PERSON><PERSON> Itu <PERSON>ng?", "advancements.adventure.summon_iron_golem.description": "Panggil Golem Besi untuk mempertahankan desa", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Lempar Trisula kepada sesuatu.\nPerhatian: melempar satu-satunya senjata <PERSON>a bukanlah gagasan bagus.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON><PERSON> untuk menghindari kematian", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trade.description": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>gang dengan se<PERSON>g <PERSON>", "advancements.adventure.trade.title": "Kesepakatan Bagus!", "advancements.adventure.trade_at_world_height.description": "Berdagang dengan Penduduk di batas tinggi pembangunan", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Terapkan templat tempa ini setidaknya sekali: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "<PERSON>as zirah di Meja Tempa", "advancements.adventure.trim_with_any_armor_pattern.title": "Membuat Penampilan Baru", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON>uh dua <PERSON>-<PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON>, Satu Panah", "advancements.adventure.under_lock_and_key.description": "Buka Brankas menggunakan Kunci Co<PERSON>an", "advancements.adventure.under_lock_and_key.title": "Sangat Terjaga", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON><PERSON> pada Batu <PERSON>", "advancements.adventure.use_lodestone.title": "Biarpun <PERSON>", "advancements.adventure.very_very_frightening.description": "Sambar Penduduk dengan petir", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "<PERSON>unuh panglima penyerbu. \nMungkin pertimbangkan untuk menjauh dari desa untuk saat ini...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON><PERSON><PERSON><PERSON> di atas <PERSON>... tanpa terjatuh ke dalamnya", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON>", "advancements.adventure.who_needs_rockets.description": "<PERSON><PERSON><PERSON> untuk meluncurkan diri Anda setinggi 8 blok", "advancements.adventure.who_needs_rockets.title": "Untuk Apa Roket?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON><PERSON><PERSON> merasakan dari senja<PERSON>a sendiri", "advancements.adventure.whos_the_pillager_now.title": "Siapa Sang Penjar<PERSON>?", "advancements.empty": "Tidak ada apa pun di sini...", "advancements.end.dragon_breath.description": "Kumpulkan Napas Naga dalam Botol Kaca", "advancements.end.dragon_breath.title": "<PERSON><PERSON>ga", "advancements.end.dragon_egg.description": "Pegang Telur Naga", "advancements.end.dragon_egg.title": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.title": "Langitlah Batasnya", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON>", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON>", "advancements.end.find_end_city.description": "Ayo masuk! Apa yang mungkin dapat terjadi?", "advancements.end.find_end_city.title": "Kota pada Akhir <PERSON>", "advancements.end.kill_dragon.description": "<PERSON><PERSON><PERSON> be<PERSON>", "advancements.end.kill_dragon.title": "Pembebasan End", "advancements.end.levitate.description": "<PERSON><PERSON>ng hingga 50 blok dari se<PERSON><PERSON>", "advancements.end.levitate.title": "Tinggi, Tinggi Sekali", "advancements.end.respawn_dragon.description": "Bangkitkan kembali Na<PERSON>", "advancements.end.respawn_dragon.title": "<PERSON><PERSON><PERSON>... Lagi...", "advancements.end.root.description": "Apakah kita berada di pengujung?", "advancements.end.root.title": "End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Buat Andaru menjatuhkan Kue di Kotak Nada", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "Buat Andaru mengirimkan benda kepada Anda", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "Tangkap Axolotl ke dalam Ember", "advancements.husbandry.axolotl_in_a_bucket.title": "Predator <PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON>kan segala sesuatu yang dapat dimakan, bahkan bila itu tidak baik untuk Anda", "advancements.husbandry.balanced_diet.title": "Empat Sehat Lima Sempurna", "advancements.husbandry.breed_all_animals.description": "Kembangbiakkan semua hewan!", "advancements.husbandry.breed_all_animals.title": "Berpasang-pasang", "advancements.husbandry.breed_an_animal.description": "Kembangbiakkan dua hewan", "advancements.husbandry.breed_an_animal.title": "Bangau Pembawa Bayi", "advancements.husbandry.complete_catalogue.description": "<PERSON><PERSON><PERSON> semua jeni<PERSON>!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "<PERSON>ri makan bayi Pengendus", "advancements.husbandry.feed_snifflet.title": "Endusan Kecil", "advancements.husbandry.fishy_business.description": "Tangkap seekor ikan", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "<PERSON><PERSON><PERSON> se<PERSON>a <PERSON> di dalam persed<PERSON>", "advancements.husbandry.froglights.title": "Satukan Kekuatan Kita!", "advancements.husbandry.kill_axolotl_target.description": "Menangkan pertarungan bersama Axolotl", "advancements.husbandry.kill_axolotl_target.title": "Salamander Sahabat <PERSON>!", "advancements.husbandry.leash_all_frog_variants.description": "Dapatkan set<PERSON> jeni<PERSON> pada <PERSON> Pen<PERSON>", "advancements.husbandry.leash_all_frog_variants.title": "Regu Katak Tiba di Kota", "advancements.husbandry.make_a_sign_glow.description": "Buat teks pada jenis papan tanda apa pun bersinar", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>!", "advancements.husbandry.netherite_hoe.description": "Gunakan Netherit Batangan untuk meningkatkan Cangkul, lalu pikirkan kembali pilihan hidup <PERSON>a", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Dapatkan sebuah Telur Pengendus", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.place_dried_ghast_in_water.description": "Tempatkan Ghast Kering ke dalam air", "advancements.husbandry.place_dried_ghast_in_water.title": "Ada Akua?", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON> benih <PERSON> apa pun", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON> benih dan lihat itu sedang tumbuh", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Lepas Zirah Ser<PERSON> dari Ser<PERSON>la menggunakan Gunting", "advancements.husbandry.remove_wolf_armor.title": "Potongan Menakjubkan", "advancements.husbandry.repair_wolf_armor.description": "Perbaiki Zirah <PERSON> yang rusak dengan Skutum <PERSON>adilo", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> se<PERSON>i <PERSON>", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON> be<PERSON>", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON>-ambing!", "advancements.husbandry.root.description": "Dunia ini penuh dengan kawan-kawan dan makanan", "advancements.husbandry.root.title": "Peterna<PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Gunakan A<PERSON>un untuk mengumpulkan Madu dari <PERSON>ng <PERSON> menggunakan Botol Kaca tanpa mengganggu Lebah", "advancements.husbandry.safely_harvest_honey.title": "Ada Bunga Ada Lebah", "advancements.husbandry.silk_touch_nest.description": "Pindahkan Sarang Lebah alami atau buatan, dengan 3 Lebah di dalamnya, menggunakan Sentuhan Sutra", "advancements.husbandry.silk_touch_nest.title": "Sialan<PERSON>", "advancements.husbandry.tactical_fishing.description": "Tangkap seekor Ikan... tanpa Tongkat Pancing!", "advancements.husbandry.tactical_fishing.title": "Pemancing Taktis", "advancements.husbandry.tadpole_in_a_bucket.description": "Tangkap <PERSON> ke dalam Ember", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON> seekor hewan", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_off.description": "<PERSON><PERSON> dari <PERSON>!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Lumurkan Sarang Madu ke Balok Tembaga!", "advancements.husbandry.wax_on.title": "P<PERSON>umura<PERSON>", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON> j<PERSON>", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON><PERSON>", "advancements.nether.all_effects.description": "Dapatkan semua efek di saat bersa<PERSON>an", "advancements.nether.all_effects.title": "Bagaimana Kita Sampai di Sini?", "advancements.nether.all_potions.description": "Dapatkan semua efek ramuan di saat bersamaan", "advancements.nether.all_potions.title": "<PERSON><PERSON>", "advancements.nether.brew_potion.description": "<PERSON>cik sebotol <PERSON>", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON> hingga penuh", "advancements.nether.charge_respawn_anchor.title": "Tidak Persis \"Sembilan\" Nyawa", "advancements.nether.create_beacon.description": "<PERSON><PERSON> dan let<PERSON>", "advancements.nether.create_beacon.title": "Memulangkan Suar", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON> kekuatan penuh", "advancements.nether.create_full_beacon.title": "Tukang Suar", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON> per<PERSON>ian <PERSON> dengan emas", "advancements.nether.distract_piglin.title": "<PERSON><PERSON><PERSON>", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON><PERSON> semua bioma Nether", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON>", "advancements.nether.fast_travel.description": "Gunakan Nether untuk bepergian 7 km di Permukaan", "advancements.nether.fast_travel.title": "Lubang Cacing", "advancements.nether.find_bastion.description": "<PERSON><PERSON><PERSON> ke <PERSON><PERSON><PERSON>han <PERSON>", "advancements.nether.find_bastion.title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>?", "advancements.nether.find_fortress.description": "Temukan jalan ke Benteng Nether", "advancements.nether.find_fortress.title": "<PERSON><PERSON><PERSON> Mengerikan", "advancements.nether.get_wither_skull.description": "Dapatkan tengkorak Bengkarak Wither", "advancements.nether.get_wither_skull.title": "Rangka Jangka Langka", "advancements.nether.loot_bastion.description": "Rampas sebuah <PERSON>", "advancements.nether.loot_bastion.title": "<PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Dapatkan seperangkat zirah Netherit", "advancements.nether.netherite_armor.title": "Tutu<PERSON> den<PERSON>", "advancements.nether.obtain_ancient_debris.description": "Dapatkan Puing Purba", "advancements.nether.obtain_ancient_debris.title": "Tersemb<PERSON><PERSON> di <PERSON>aman", "advancements.nether.obtain_blaze_rod.description": "Dapatkan Tongkat Kemamang", "advancements.nether.obtain_blaze_rod.title": "Berapi-api", "advancements.nether.obtain_crying_obsidian.description": "Dapatkan Obsidian Menangis", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON>?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> G<PERSON> men<PERSON>kan Bola Api", "advancements.nether.return_to_sender.title": "<PERSON><PERSON><PERSON>", "advancements.nether.ride_strider.description": "<PERSON><PERSON> dengan Jam<PERSON>", "advancements.nether.ride_strider.title": "<PERSON><PERSON> In<PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Bawa Pengarung dalam perjalanan saaangat jauh mengarungi danau lava di Permukaan", "advancements.nether.ride_strider_in_overworld_lava.title": "Terasa Seperti di Rumah", "advancements.nether.root.description": "<PERSON><PERSON> baju musim panas", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Selamat<PERSON> G<PERSON>, bawa pulang ke Permukaan dengan selamat... kem<PERSON>an dibunuh", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON>", "advancements.nether.use_lodestone.description": "<PERSON><PERSON><PERSON> pada Batu <PERSON>", "advancements.nether.use_lodestone.title": "Biarpun <PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Lemahkan lalu sembuhkan Zombi Penduduk", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Tangkis proyektil dengan <PERSON>i", "advancements.story.deflect_arrow.title": "Tidak <PERSON>, <PERSON><PERSON> Kasi<PERSON>", "advancements.story.enchant_item.description": "Pesonakan satu benda di <PERSON>", "advancements.story.enchant_item.title": "<PERSON><PERSON>", "advancements.story.enter_the_end.description": "Masuk ke Gerbang End", "advancements.story.enter_the_end.title": "Tamat?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON><PERSON><PERSON>", "advancements.story.enter_the_nether.title": "<PERSON><PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON>", "advancements.story.follow_ender_eye.title": "<PERSON>", "advancements.story.form_obsidian.description": "Dapatkan balok Obsidian", "advancements.story.form_obsidian.title": "Tantangan Ember Es", "advancements.story.iron_tools.description": "Tingkatkan Beliung Anda", "advancements.story.iron_tools.title": "Bukan <PERSON>", "advancements.story.lava_bucket.description": "Isi Ember dengan lava", "advancements.story.lava_bucket.title": "<PERSON><PERSON>", "advancements.story.mine_diamond.description": "Dapatkan Berl<PERSON>", "advancements.story.mine_diamond.title": "<PERSON><PERSON><PERSON>!", "advancements.story.mine_stone.description": "Tambang Batu menggunakan Beliung baru Anda", "advancements.story.mine_stone.title": "<PERSON><PERSON>", "advancements.story.obtain_armor.description": "Lindungi diri Anda men<PERSON>", "advancements.story.obtain_armor.title": "Baju <PERSON>", "advancements.story.root.description": "<PERSON>tis<PERSON> kisah perma<PERSON>n", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON><PERSON><PERSON>wa", "advancements.story.shiny_gear.title": "Tutupi <PERSON> den<PERSON>", "advancements.story.smelt_iron.description": "<PERSON><PERSON>", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "<PERSON><PERSON>t <PERSON>ng yang lebih baik", "advancements.story.upgrade_tools.title": "Mendapatkan Peningkatan", "advancements.toast.challenge": "Tantangan Selesai!", "advancements.toast.goal": "<PERSON><PERSON><PERSON>!", "advancements.toast.task": "Kemajuan Tercapai!", "argument.anchor.invalid": "Posisi penambatan entitas '%s' tidak sah", "argument.angle.incomplete": "Tidak lengkap (mengharapkan 1 sudut)", "argument.angle.invalid": "Sudut tidak sah", "argument.block.id.invalid": "<PERSON><PERSON> balok '%s' tidak dikenal", "argument.block.property.duplicate": "Sifat '%s' hanya dapat diatur sekali setiap balok %s", "argument.block.property.invalid": "Balok %s tidak menerima '%s' sebagai sifat %s", "argument.block.property.novalue": "Mengharapkan nilai sifat '%s' pada balok %s", "argument.block.property.unclosed": "Menghara<PERSON><PERSON> ] penutup untuk properti keadaan balok", "argument.block.property.unknown": "Balok %s tidak memiliki sifat '%s'", "argument.block.tag.disallowed": "Etiket tidak diizinkan di sini, hanya balok", "argument.color.invalid": "Warna '%s' tidak dikenal", "argument.component.invalid": "Komponen obrolan tidak sah: %s", "argument.criteria.invalid": "Patokan '%s' tidak dikenal", "argument.dimension.invalid": "Dimensi '%s' tidak dikenal", "argument.double.big": "Angka ganda tidak dapat lebih dari %s, ditemukan %s", "argument.double.low": "Angka ganda tidak dapat kurang dari %s, ditemukan %s", "argument.entity.invalid": "Nama atau UUID tidak sah", "argument.entity.notfound.entity": "Tidak ada entitas yang di<PERSON>ukan", "argument.entity.notfound.player": "Tidak ada pemain yang di<PERSON>n", "argument.entity.options.advancements.description": "<PERSON><PERSON><PERSON> k<PERSON>", "argument.entity.options.distance.description": "Jarak ke entitas", "argument.entity.options.distance.negative": "Jarak tidak dapat negatif", "argument.entity.options.dx.description": "Entitas antara x dan x + dx", "argument.entity.options.dy.description": "Entitas antara y dan y + dy", "argument.entity.options.dz.description": "Entitas antara z dan z + dz", "argument.entity.options.gamemode.description": "Pemain dengan mode permainan", "argument.entity.options.inapplicable": "Pilihan '%s' tidak dapat diterapkan di sini", "argument.entity.options.level.description": "Tingkat pen<PERSON>aman", "argument.entity.options.level.negative": "Tingkat tidak dapat negatif", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON> maksimal entitas untuk dikembalikan", "argument.entity.options.limit.toosmall": "Batas harus setidaknya 1", "argument.entity.options.mode.invalid": "Mode permainan '%s' tidak dikenal atau tidak sah", "argument.entity.options.name.description": "<PERSON><PERSON> entitas", "argument.entity.options.nbt.description": "Entitas dengan NBT", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON> ubah <PERSON>n", "argument.entity.options.scores.description": "Entitas dengan nilai", "argument.entity.options.sort.description": "Urutkan entitas", "argument.entity.options.sort.irreversible": "<PERSON><PERSON> '%s' tidak dikenal atau tidak sah", "argument.entity.options.tag.description": "Entitas dengan etiket", "argument.entity.options.team.description": "Entitas pada regu", "argument.entity.options.type.description": "<PERSON><PERSON> en<PERSON>", "argument.entity.options.type.invalid": "<PERSON><PERSON> enti<PERSON> '%s' tidak dikenal atau tidak sah", "argument.entity.options.unknown": "Pilihan '%s' tidak dikenal", "argument.entity.options.unterminated": "Menghara<PERSON><PERSON> akhir pilihan", "argument.entity.options.valueless": "Mengharapkan nilai untuk pilihan '%s'", "argument.entity.options.x.description": "Posisi x", "argument.entity.options.x_rotation.description": "Rotasi x entitas", "argument.entity.options.y.description": "Posisi y", "argument.entity.options.y_rotation.description": "Rotasi y entitas", "argument.entity.options.z.description": "Posisi z", "argument.entity.selector.allEntities": "<PERSON><PERSON><PERSON> entitas", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON> p<PERSON>", "argument.entity.selector.missing": "<PERSON><PERSON> pem<PERSON> hilang", "argument.entity.selector.nearestEntity": "Entitas terdekat", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON> tidak <PERSON>", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON>", "argument.entity.selector.self": "Entitas saat ini", "argument.entity.selector.unknown": "<PERSON><PERSON> pem<PERSON> '%s' tidak dikenal", "argument.entity.toomany": "Hanya satu entitas yang diperbol<PERSON>, tetapi pemilih yang diberikan memungkinkan lebih dari satu entitas", "argument.enum.invalid": "<PERSON><PERSON> tidak sah \"%s\"", "argument.float.big": "Angka mengambang tidak dapat lebih dari %s, ditemukan %s", "argument.float.low": "Angka mengambang tidak dapat kurang dari %s, ditemukan %s", "argument.gamemode.invalid": "Mode permainan tidak dikenal: %s", "argument.hexcolor.invalid": "Kode warna heksadesimal '%s' tidak sah", "argument.id.invalid": "Pengenal tidak sah", "argument.id.unknown": "Pengenal tidak dikenal: %s", "argument.integer.big": "Angka bulat tidak dapat lebih dari %s, ditemukan %s", "argument.integer.low": "Angka bulat tidak dapat kurang dari %s, ditemukan %s", "argument.item.id.invalid": "Benda '%s' tidak dikenal", "argument.item.tag.disallowed": "Etiket tidak diizinkan di sini, hanya benda", "argument.literal.incorrect": "Mengharapkan literal %s", "argument.long.big": "Angka besar tidak dapat lebih dari %s, ditemukan %s", "argument.long.low": "Angka besar tidak dapat kurang dari %s, ditemukan %s", "argument.message.too_long": "Pesan obrolan terlalu panjang (%s > maksimal %s karakter)", "argument.nbt.array.invalid": "<PERSON><PERSON> la<PERSON> '%s' tidak sah", "argument.nbt.array.mixed": "Tidak dapat memasukkan %s ke dalam %s", "argument.nbt.expected.compound": "Mengharapkan etiket maje<PERSON>k", "argument.nbt.expected.key": "Mengharapkan kunci", "argument.nbt.expected.value": "Menghara<PERSON><PERSON> nilai", "argument.nbt.list.mixed": "Tidak dapat memasukkan %s ke dalam daftar %s", "argument.nbt.trailing": "Tidak mengharapkan data berekor", "argument.player.entities": "<PERSON><PERSON><PERSON> hanya memengar<PERSON>i pemain, tetapi pemilih yang diberikan menyertakan entitas", "argument.player.toomany": "<PERSON>ya satu pemain yang diperbol<PERSON>, tetapi pemilih yang diberikan memungkinkan lebih dari satu pemain", "argument.player.unknown": "<PERSON><PERSON><PERSON> tersebut tidak ada", "argument.pos.missing.double": "Mengharapkan koordinat", "argument.pos.missing.int": "Mengharapkan posisi balok", "argument.pos.mixed": "Tidak dapat mencampurkan koordinat dunia dan lokal (semua harus menggunakan antara ^ atau tidak)", "argument.pos.outofbounds": "Posisi itu berada di luar batas yang diperbolehkan.", "argument.pos.outofworld": "Posisi itu berada di luar dunia ini!", "argument.pos.unloaded": "Posisi itu tidak termuat", "argument.pos2d.incomplete": "Tidak lengkap (mengharapkan 2 koordinat)", "argument.pos3d.incomplete": "Tidak lengkap (mengharapkan 3 koordinat)", "argument.range.empty": "Mengharapkan nilai atau rentang nilai", "argument.range.ints": "<PERSON><PERSON> angka bulat, bukan pecahan", "argument.range.swapped": "Minimum tidak dapat lebih besar daripada maksimum", "argument.resource.invalid_type": "Elemen '%s' memiliki jenis '%s' yang salah (mengharapkan '%s')", "argument.resource.not_found": "Tidak dapat mencari elemen '%s' dengan jenis '%s'", "argument.resource_or_id.failed_to_parse": "Gagal menguraikan struktur: %s", "argument.resource_or_id.invalid": "Pengenal atau etiket tidak sah", "argument.resource_or_id.no_such_element": "Tidak dapat mencari elemen '%s' di pendaftaran '%s'", "argument.resource_selector.not_found": "Tidak ditemukan pemilih '%s' dengan jenis '%s'", "argument.resource_tag.invalid_type": "Etiket '%s' memiliki jenis '%s' yang salah (mengharapkan '%s')", "argument.resource_tag.not_found": "Tidak dapat menemukan etiket '%s' dari jenis '%s'", "argument.rotation.incomplete": "Tidak lengkap (mengharapkan 2 koordinat)", "argument.scoreHolder.empty": "Tidak dapat menemukan pemilik nilai", "argument.scoreboardDisplaySlot.invalid": "Petak tampil '%s' tidak dikenal", "argument.style.invalid": "Gaya tidak sah: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON><PERSON> tik harus bukan negatif", "argument.time.invalid_unit": "Satuan tidak sah", "argument.time.tick_count_too_low": "Jumlah tik tidak boleh kurang dari %s, ditemukan %s", "argument.uuid.invalid": "UUID tidak sah", "argument.waypoint.invalid": "Entitas terpilih bukan titik jalan", "arguments.block.tag.unknown": "Etiket balok '%s' tidak dikenal", "arguments.function.tag.unknown": "Etiket fungsi '%s' tidak dikenal", "arguments.function.unknown": "Fungsi '%s' tidak dikenal", "arguments.item.component.expected": "Mengharapkan komponen benda", "arguments.item.component.malformed": "Komponen '%s' cacat: '%s'", "arguments.item.component.repeated": "Komponen benda '%s' diulang, tetapi hanya satu nilai yang dapat ditentukan", "arguments.item.component.unknown": "Komponen benda '%s' tidak dikenal", "arguments.item.malformed": "Benda cacat: '%s'", "arguments.item.overstacked": "%s hanya dapat ditumpuk hingga %s", "arguments.item.predicate.malformed": "Predikat '%s' cacat: '%s'", "arguments.item.predicate.unknown": "Predikat benda tidak dikenal: %s''", "arguments.item.tag.unknown": "Etiket benda '%s' tidak dikenal", "arguments.nbtpath.node.invalid": "Elemen jalur NBT tidak sah", "arguments.nbtpath.nothing_found": "Tidak menemukan elemen yang sesuai dengan %s", "arguments.nbtpath.too_deep": "NBT yang dihasilkan bersarang terlalu dalam", "arguments.nbtpath.too_large": "NBT yang dihasilkan terlalu besar", "arguments.objective.notFound": "<PERSON><PERSON><PERSON> '%s' tidak dikenal", "arguments.objective.readonly": "<PERSON><PERSON><PERSON> '%s' bersifat hanya-baca", "arguments.operation.div0": "Tidak dapat membagi dengan nol", "arguments.operation.invalid": "Operasi tidak sah", "arguments.swizzle.invalid": "Kombinasi sumbu tidak sah, mengharapkan kombinasi 'x', 'y', dan 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Zira<PERSON>", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "Kekuatan Serangan", "attribute.name.attack_knockback": "Serangan <PERSON>", "attribute.name.attack_speed": "Kecepatan Serangan", "attribute.name.block_break_speed": "Kecepatan Hancur <PERSON>", "attribute.name.block_interaction_range": "Rentang Interaksi Balok", "attribute.name.burning_time": "<PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON>", "attribute.name.entity_interaction_range": "Rentang Interaksi Entitas", "attribute.name.explosion_knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.fall_damage_multiplier": "Pengali Luka Jatuh", "attribute.name.flying_speed": "Kecepatan Terbang", "attribute.name.follow_range": "Jarak Ikut Makhluk", "attribute.name.generic.armor": "Zira<PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "Kekuatan Serangan", "attribute.name.generic.attack_knockback": "Serangan <PERSON>", "attribute.name.generic.attack_speed": "Kecepatan Serangan", "attribute.name.generic.block_interaction_range": "Rentang Interaksi Balok", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Rentang Interaksi Entitas", "attribute.name.generic.explosion_knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.fall_damage_multiplier": "Pengali Luka Jatuh", "attribute.name.generic.flying_speed": "Kecepatan Terbang", "attribute.name.generic.follow_range": "Jarak Ikut Makhluk", "attribute.name.generic.gravity": "Gravitasi", "attribute.name.generic.jump_strength": "Kekuatan Lompatan", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON>ung<PERSON>", "attribute.name.generic.max_absorption": "Penyerapan Maksimal", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Efisiensi Pergerakan", "attribute.name.generic.movement_speed": "Kecepatan", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "<PERSON><PERSON><PERSON>", "attribute.name.generic.water_movement_efficiency": "Efisiensi Pergerakan Air", "attribute.name.gravity": "Gravitasi", "attribute.name.horse.jump_strength": "Kekuatan Lompatan Kuda", "attribute.name.jump_strength": "Kekuatan Lompatan", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.luck": "<PERSON><PERSON><PERSON>ung<PERSON>", "attribute.name.max_absorption": "Penyerapan Maksimal", "attribute.name.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "Efisiensi Menambang", "attribute.name.movement_efficiency": "Efisiensi Pergerakan", "attribute.name.movement_speed": "Kecepatan", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Kecepatan Hancur <PERSON>", "attribute.name.player.block_interaction_range": "Rentang Interaksi Balok", "attribute.name.player.entity_interaction_range": "Rentang Interaksi Entitas", "attribute.name.player.mining_efficiency": "Efisiensi Menambang", "attribute.name.player.sneaking_speed": "Kecepatan Seluduk", "attribute.name.player.submerged_mining_speed": "Kecepatan Menambang Bawah Air", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON>", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Kecepatan Seluduk", "attribute.name.spawn_reinforcements": "<PERSON><PERSON>", "attribute.name.step_height": "<PERSON><PERSON><PERSON>", "attribute.name.submerged_mining_speed": "Kecepatan Menambang Bawah Air", "attribute.name.sweeping_damage_ratio": "<PERSON><PERSON>", "attribute.name.tempt_range": "Jarak Merayu Makhluk", "attribute.name.water_movement_efficiency": "Efisiensi Pergerakan Air", "attribute.name.waypoint_receive_range": "Jarak Terima Titik Jalan", "attribute.name.waypoint_transmit_range": "Jarak Pancar Titik Jalan", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON>", "biome.minecraft.badlands": "<PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Rimba Bambu", "biome.minecraft.basalt_deltas": "Delta Basal", "biome.minecraft.beach": "Pantai", "biome.minecraft.birch_forest": "<PERSON><PERSON>", "biome.minecraft.cherry_grove": "<PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "<PERSON><PERSON>", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON>", "biome.minecraft.desert": "Gurun", "biome.minecraft.dripstone_caves": "Gua Batu <PERSON>", "biome.minecraft.end_barrens": "<PERSON><PERSON> G<PERSON>", "biome.minecraft.end_highlands": "<PERSON><PERSON>", "biome.minecraft.end_midlands": "<PERSON>ah <PERSON>", "biome.minecraft.eroded_badlands": "<PERSON><PERSON>", "biome.minecraft.flower_forest": "<PERSON><PERSON>", "biome.minecraft.forest": "Hutan", "biome.minecraft.frozen_ocean": "<PERSON><PERSON>", "biome.minecraft.frozen_peaks": "Puncak Beku", "biome.minecraft.frozen_river": "Sungai Be<PERSON>", "biome.minecraft.grove": "<PERSON><PERSON>", "biome.minecraft.ice_spikes": "<PERSON><PERSON>", "biome.minecraft.jagged_peaks": "Puncak Bercerancangan", "biome.minecraft.jungle": "Rimba", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "Rawa Bakau", "biome.minecraft.meadow": "Padan<PERSON>", "biome.minecraft.mushroom_fields": "Padan<PERSON> Jamur", "biome.minecraft.nether_wastes": "<PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON>", "biome.minecraft.old_growth_pine_taiga": "Taiga <PERSON>", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON>", "biome.minecraft.pale_garden": "<PERSON>bu<PERSON>", "biome.minecraft.plains": "Dataran", "biome.minecraft.river": "<PERSON><PERSON>", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "<PERSON>", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_beach": "Pantai Bersalju", "biome.minecraft.snowy_plains": "<PERSON><PERSON>", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_taiga": "<PERSON><PERSON>", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON>", "biome.minecraft.sparse_jungle": "R<PERSON><PERSON>", "biome.minecraft.stony_peaks": "Puncak Berbatu", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Dataran Bunga Matahari", "biome.minecraft.swamp": "<PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "<PERSON><PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON>", "biome.minecraft.windswept_forest": "Hutan Bukit Berangin", "biome.minecraft.windswept_gravelly_hills": "Bukit Kerikil Berangin", "biome.minecraft.windswept_hills": "Bukit Berangin", "biome.minecraft.windswept_savanna": "Sabana Be<PERSON>", "biome.minecraft.wooded_badlands": "<PERSON><PERSON>", "block.minecraft.acacia_button": "Tombol Akasia", "block.minecraft.acacia_door": "Pintu Akasia", "block.minecraft.acacia_fence": "Pagar <PERSON>", "block.minecraft.acacia_fence_gate": "Pintu Pagar Akasia", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON> Gantung Akasia", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Gelondong Akasia", "block.minecraft.acacia_planks": "<PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "<PERSON><PERSON>t <PERSON>", "block.minecraft.acacia_sapling": "<PERSON><PERSON>t <PERSON>", "block.minecraft.acacia_sign": "<PERSON><PERSON>", "block.minecraft.acacia_slab": "Lempeng Akasia", "block.minecraft.acacia_stairs": "Tangga Akasia", "block.minecraft.acacia_trapdoor": "<PERSON>nt<PERSON>", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON> Din<PERSON> Akasia", "block.minecraft.acacia_wall_sign": "<PERSON><PERSON>", "block.minecraft.acacia_wood": "<PERSON><PERSON>", "block.minecraft.activator_rail": "<PERSON><PERSON>", "block.minecraft.air": "<PERSON><PERSON><PERSON>", "block.minecraft.allium": "Bunga Bawang", "block.minecraft.amethyst_block": "Balok Kecubung", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON>", "block.minecraft.ancient_debris": "Puing Purba", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Lempeng Andesit", "block.minecraft.andesite_stairs": "Tangga Andesit", "block.minecraft.andesite_wall": "Tembok Andesit", "block.minecraft.anvil": "<PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Batang Semangka Tertempel", "block.minecraft.attached_pumpkin_stem": "Batang Labu Tertempel", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Dedaunan Azalea", "block.minecraft.azure_bluet": "Azure Bluet", "block.minecraft.bamboo": "Bambu", "block.minecraft.bamboo_block": "Balok Bambu", "block.minecraft.bamboo_button": "Tombol Bambu", "block.minecraft.bamboo_door": "Pintu Bambu", "block.minecraft.bamboo_fence": "Pagar Bambu", "block.minecraft.bamboo_fence_gate": "Pintu Pagar Bambu", "block.minecraft.bamboo_hanging_sign": "Papan Tanda Gantung Bambu", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON>", "block.minecraft.bamboo_mosaic_slab": "Lemp<PERSON> Anyaman Bambu", "block.minecraft.bamboo_mosaic_stairs": "Tangga Anyaman Bambu", "block.minecraft.bamboo_planks": "Papan <PERSON>", "block.minecraft.bamboo_pressure_plate": "<PERSON><PERSON>t <PERSON>n Ba<PERSON>", "block.minecraft.bamboo_sapling": "Rebung", "block.minecraft.bamboo_sign": "<PERSON>n <PERSON>", "block.minecraft.bamboo_slab": "Lempeng Bambu", "block.minecraft.bamboo_stairs": "Tangga Bambu", "block.minecraft.bamboo_trapdoor": "Pintu <PERSON> Bambu", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON>da Gantung Dinding Bambu", "block.minecraft.bamboo_wall_sign": "Papan Tanda Gantung Bambu", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.black": "<PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.border.lime": "<PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "Kotak-Kotak Hitam", "block.minecraft.banner.bricks.blue": "Kotak-Kotak Biru", "block.minecraft.banner.bricks.brown": "Kotak-Kotak Cokelat", "block.minecraft.banner.bricks.cyan": "Kotak-Kotak Sian", "block.minecraft.banner.bricks.gray": "Kotak-Kotak Kelabu", "block.minecraft.banner.bricks.green": "Kotak-Kotak Hijau", "block.minecraft.banner.bricks.light_blue": "Kotak-Kotak Biru Muda", "block.minecraft.banner.bricks.light_gray": "Kotak-Kotak Kelabu Muda", "block.minecraft.banner.bricks.lime": "Kotak-Kotak Hijau Pupus", "block.minecraft.banner.bricks.magenta": "Kotak-Kotak Magenta", "block.minecraft.banner.bricks.orange": "Kotak-Kotak Jingga", "block.minecraft.banner.bricks.pink": "Kotak-Kotak Merah Jambu", "block.minecraft.banner.bricks.purple": "Kotak-Kotak Ungu", "block.minecraft.banner.bricks.red": "Kotak-Kotak Merah", "block.minecraft.banner.bricks.white": "Kotak-Kotak Putih", "block.minecraft.banner.bricks.yellow": "Kotak-Kotak Kuning", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "Wajah Creeper Coklat", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON> Cree<PERSON>", "block.minecraft.banner.creeper.orange": "Wajah Cree<PERSON>", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON>h Creeper Merah Jam<PERSON>", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Be<PERSON>ilang Hitam", "block.minecraft.banner.cross.blue": "Bersilang Biru", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Bersilang Kelabu", "block.minecraft.banner.cross.green": "Bersilang Hijau", "block.minecraft.banner.cross.light_blue": "Bersilang B<PERSON>", "block.minecraft.banner.cross.light_gray": "Bersilang Kelabu <PERSON>", "block.minecraft.banner.cross.lime": "<PERSON><PERSON>ilang <PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON>ng <PERSON>", "block.minecraft.banner.cross.orange": "Bersilang Jingga", "block.minecraft.banner.cross.pink": "Bersilang Merah Jambu", "block.minecraft.banner.cross.purple": "Be<PERSON><PERSON>ng <PERSON>", "block.minecraft.banner.cross.red": "Bersilang Merah", "block.minecraft.banner.cross.white": "Bersilang Putih", "block.minecraft.banner.cross.yellow": "Bersilang Kuning", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.blue": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.green": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "Po<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.lime": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.orange": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.pink": "Pojok <PERSON> Jambu", "block.minecraft.banner.diagonal_left.purple": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.red": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.white": "Pojok <PERSON>", "block.minecraft.banner.diagonal_left.yellow": "Pojok <PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON>as Biru", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON> Ke<PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON> Hi<PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON>as B<PERSON>", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON>as Ke<PERSON>", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON><PERSON> Hi<PERSON>", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON> Ma<PERSON>", "block.minecraft.banner.diagonal_right.orange": "Pojok <PERSON> Atas Atas Jingga", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON>as Merah Jambu", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON> Me<PERSON>", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON> Put<PERSON>", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON>as Ku<PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.brown": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.green": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.light_gray": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.lime": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.magenta": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.orange": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.pink": "Pojok <PERSON> Jambu", "block.minecraft.banner.diagonal_up_left.purple": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.red": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.white": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_left.yellow": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON> B<PERSON>u", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.light_gray": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.orange": "Pojok <PERSON>", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON> Me<PERSON> Jambu", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.black": "<PERSON><PERSON>", "block.minecraft.banner.flow.blue": "<PERSON><PERSON>", "block.minecraft.banner.flow.brown": "<PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "<PERSON><PERSON>", "block.minecraft.banner.flow.green": "<PERSON><PERSON>", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.flow.lime": "<PERSON><PERSON>", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON>", "block.minecraft.banner.flow.orange": "<PERSON><PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON> Jambu", "block.minecraft.banner.flow.purple": "<PERSON><PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON>", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON>", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON>unga Biru", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON>a B<PERSON>u <PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON>unga <PERSON>a", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON><PERSON>unga Merah Jambu", "block.minecraft.banner.flower.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON> Merah", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "Globe Hitam", "block.minecraft.banner.globe.blue": "Globe Biru", "block.minecraft.banner.globe.brown": "Globe Cokelat", "block.minecraft.banner.globe.cyan": "Globe Sian", "block.minecraft.banner.globe.gray": "Globe Kelabu", "block.minecraft.banner.globe.green": "Globe Hijau", "block.minecraft.banner.globe.light_blue": "Globe Biru Muda", "block.minecraft.banner.globe.light_gray": "Globe Kelabu Muda", "block.minecraft.banner.globe.lime": "Globe Hijau Pupus", "block.minecraft.banner.globe.magenta": "Globe Magenta", "block.minecraft.banner.globe.orange": "Globe Jingga", "block.minecraft.banner.globe.pink": "Globe Merah Jambu", "block.minecraft.banner.globe.purple": "Globe Ungu", "block.minecraft.banner.globe.red": "Globe Merah", "block.minecraft.banner.globe.white": "Globe Putih", "block.minecraft.banner.globe.yellow": "Globe Kuning", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON> Me<PERSON> Jambu", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "Prahara Merah Jambu", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> Men<PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON>aruh Men<PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON> Men<PERSON>", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON> Men<PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON>aruh Men<PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "Separuh Mendatar Terbali<PERSON> Biru", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON>h Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON>aruh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "Separuh Mendatar Terbali<PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON>h Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.lime": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.magenta": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "Separuh Mendatar Terbalik Merah Jambu", "block.minecraft.banner.half_horizontal_bottom.purple": "Separuh Mendatar Terbali<PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "Separuh Mendatar <PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "Separuh Mendatar Terbalik <PERSON>", "block.minecraft.banner.half_vertical.black": "Separuh Te<PERSON>k Hitam", "block.minecraft.banner.half_vertical.blue": "Separuh Tegak Biru", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "Separuh Tegak Si<PERSON>", "block.minecraft.banner.half_vertical.gray": "Separuh Tegak Kelabu", "block.minecraft.banner.half_vertical.green": "Separuh Tegak Hijau", "block.minecraft.banner.half_vertical.light_blue": "Separuh Tegak Biru <PERSON>", "block.minecraft.banner.half_vertical.light_gray": "Separuh Tegak Kelabu <PERSON>", "block.minecraft.banner.half_vertical.lime": "Separuh Tegak Hijau Pupus", "block.minecraft.banner.half_vertical.magenta": "Separuh Tegak Magenta", "block.minecraft.banner.half_vertical.orange": "Separuh Tegak Jingga", "block.minecraft.banner.half_vertical.pink": "Separuh Tegak Merah Jambu", "block.minecraft.banner.half_vertical.purple": "Separuh Tegak Ungu", "block.minecraft.banner.half_vertical.red": "Separuh Tegak Merah", "block.minecraft.banner.half_vertical.white": "Separuh Tegak Putih", "block.minecraft.banner.half_vertical.yellow": "Separuh Tegak Kuning", "block.minecraft.banner.half_vertical_right.black": "Separuh Tegak Terbalik Hitam", "block.minecraft.banner.half_vertical_right.blue": "Separuh Tegak Terbalik Biru", "block.minecraft.banner.half_vertical_right.brown": "Separuh Tegak Terbalik Cokelat", "block.minecraft.banner.half_vertical_right.cyan": "Separuh Tegak Terbalik Sian", "block.minecraft.banner.half_vertical_right.gray": "Separuh Tegak Terbalik Kelabu", "block.minecraft.banner.half_vertical_right.green": "Separuh Tegak Terbalik Hijau", "block.minecraft.banner.half_vertical_right.light_blue": "Separuh Tegak Terbalik Biru Muda", "block.minecraft.banner.half_vertical_right.light_gray": "Separuh Tegak Terbalik Kelabu Muda", "block.minecraft.banner.half_vertical_right.lime": "Separuh Tegak Terbalik Hijau Pupus", "block.minecraft.banner.half_vertical_right.magenta": "Separuh Tegak Terbalik Magenta", "block.minecraft.banner.half_vertical_right.orange": "Separuh Tegak Terbalik Jingga", "block.minecraft.banner.half_vertical_right.pink": "Separuh Tegak Terbalik Merah Jambu", "block.minecraft.banner.half_vertical_right.purple": "Separuh Tegak Terbalik Ungu", "block.minecraft.banner.half_vertical_right.red": "Separuh Tegak Terbalik Merah", "block.minecraft.banner.half_vertical_right.white": "Separuh Tegak Terbalik Putih", "block.minecraft.banner.half_vertical_right.yellow": "Separuh Tegak Terbalik Kuning", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON> Logo Biru", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "Rupa Lo<PERSON>labu", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON> Logo Biru <PERSON>", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON> Lo<PERSON>", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON> Ma<PERSON>", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "Rupa Logo Merah Jambu", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON>a Logo Merah", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.black": "Moncong Hitam", "block.minecraft.banner.piglin.blue": "Moncong Biru", "block.minecraft.banner.piglin.brown": "Moncong <PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "Moncong Kelabu", "block.minecraft.banner.piglin.green": "Moncong Hijau", "block.minecraft.banner.piglin.light_blue": "Moncong Biru <PERSON>", "block.minecraft.banner.piglin.light_gray": "Moncong Kelabu <PERSON>", "block.minecraft.banner.piglin.lime": "Moncong Hijau <PERSON>", "block.minecraft.banner.piglin.magenta": "Moncong Magenta", "block.minecraft.banner.piglin.orange": "Moncong Jingga", "block.minecraft.banner.piglin.pink": "Moncong Merah Jambu", "block.minecraft.banner.piglin.purple": "Moncong Ungu", "block.minecraft.banner.piglin.red": "Moncong Merah", "block.minecraft.banner.piglin.white": "Moncong <PERSON>ih", "block.minecraft.banner.piglin.yellow": "Moncong Kuning", "block.minecraft.banner.rhombus.black": "Belah Ketupat Hitam", "block.minecraft.banner.rhombus.blue": "Belah Ketupat Biru", "block.minecraft.banner.rhombus.brown": "Belah Ketupat <PERSON>", "block.minecraft.banner.rhombus.cyan": "Belah Ketupat Si<PERSON>", "block.minecraft.banner.rhombus.gray": "Belah Ketupat Kelabu", "block.minecraft.banner.rhombus.green": "Belah Ketupat Hijau", "block.minecraft.banner.rhombus.light_blue": "Belah Ketupat Biru Muda", "block.minecraft.banner.rhombus.light_gray": "Belah Ketupat Kelabu Muda", "block.minecraft.banner.rhombus.lime": "Belah Ketupat Hijau <PERSON>", "block.minecraft.banner.rhombus.magenta": "Belah Ketupat Magenta", "block.minecraft.banner.rhombus.orange": "Belah Ketupat Jingga", "block.minecraft.banner.rhombus.pink": "Belah Ketupat Merah Jambu", "block.minecraft.banner.rhombus.purple": "Belah Ketupat Ungu", "block.minecraft.banner.rhombus.red": "Belah Ketupat Merah", "block.minecraft.banner.rhombus.white": "Belah Ketupat Putih", "block.minecraft.banner.rhombus.yellow": "Belah Ketupat Kuning", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON>h Tengkorak Merah Jambu", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.red": "Wajah Tengkorak Merah", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "Belang-Belang Hitam", "block.minecraft.banner.small_stripes.blue": "Belang-Belang Biru", "block.minecraft.banner.small_stripes.brown": "Belang-Belang <PERSON>", "block.minecraft.banner.small_stripes.cyan": "Belang-Belang Sian", "block.minecraft.banner.small_stripes.gray": "Belang-Belang Kelabu", "block.minecraft.banner.small_stripes.green": "Belang-Belang <PERSON>", "block.minecraft.banner.small_stripes.light_blue": "Belang-Belang Biru <PERSON>", "block.minecraft.banner.small_stripes.light_gray": "Belang-Belang <PERSON>", "block.minecraft.banner.small_stripes.lime": "Belang-Bel<PERSON>", "block.minecraft.banner.small_stripes.magenta": "Belang-Belang Ma<PERSON>", "block.minecraft.banner.small_stripes.orange": "Belang-Belang Jingga", "block.minecraft.banner.small_stripes.pink": "Belang-Belang Merah Jambu", "block.minecraft.banner.small_stripes.purple": "Belang-Belang Ungu", "block.minecraft.banner.small_stripes.red": "Belang-Belang Merah", "block.minecraft.banner.small_stripes.white": "Belang-Bel<PERSON>", "block.minecraft.banner.small_stripes.yellow": "Belang-Belang Kuning", "block.minecraft.banner.square_bottom_left.black": "Kotak Pojok Kiri Bawa<PERSON>", "block.minecraft.banner.square_bottom_left.blue": "Kotak Pojok Kiri Bawah Biru", "block.minecraft.banner.square_bottom_left.brown": "Kotak Pojok Kiri Bawa<PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "Kotak Pojok Kiri Bawa<PERSON>", "block.minecraft.banner.square_bottom_left.gray": "Kotak Pojok Kiri Bawah <PERSON>labu", "block.minecraft.banner.square_bottom_left.green": "Kotak Pojok Kiri Bawah Hi<PERSON>u", "block.minecraft.banner.square_bottom_left.light_blue": "Kotak Pojok Kiri Bawah Biru Muda", "block.minecraft.banner.square_bottom_left.light_gray": "Kotak Pojok Kiri Bawah Kelabu <PERSON>da", "block.minecraft.banner.square_bottom_left.lime": "Kotak Pojok Kiri Bawah Hi<PERSON>u Pupus", "block.minecraft.banner.square_bottom_left.magenta": "Kotak Pojok Kiri Bawa<PERSON> Ma<PERSON>a", "block.minecraft.banner.square_bottom_left.orange": "Kotak Pojok Kiri Bawah <PERSON>", "block.minecraft.banner.square_bottom_left.pink": "Kotak Pojok Kiri Bawah Merah Jambu", "block.minecraft.banner.square_bottom_left.purple": "Kotak Pojok Kiri Bawah Ungu", "block.minecraft.banner.square_bottom_left.red": "Kotak Pojok Kiri Bawah Merah", "block.minecraft.banner.square_bottom_left.white": "Kotak Pojok Kiri Bawah Putih", "block.minecraft.banner.square_bottom_left.yellow": "Kotak Pojok Kiri Bawah Kuning", "block.minecraft.banner.square_bottom_right.black": "Kotak Pojok Kanan Bawah Hitam", "block.minecraft.banner.square_bottom_right.blue": "Kotak Pojok Kanan Bawah Biru", "block.minecraft.banner.square_bottom_right.brown": "Kotak Pojok Kanan Bawah <PERSON>lat", "block.minecraft.banner.square_bottom_right.cyan": "Kotak Pojok Kanan Bawah Sian", "block.minecraft.banner.square_bottom_right.gray": "Kotak Pojok Kanan Bawah Kelabu", "block.minecraft.banner.square_bottom_right.green": "Kotak Pojok Kanan Bawah Hijau", "block.minecraft.banner.square_bottom_right.light_blue": "Kotak Pojok Kanan Bawah Biru Muda", "block.minecraft.banner.square_bottom_right.light_gray": "Kotak Pojok Kanan Bawah Kelabu Muda", "block.minecraft.banner.square_bottom_right.lime": "Kotak Pojok Kanan Bawah Hijau Pupus", "block.minecraft.banner.square_bottom_right.magenta": "Kotak Pojok Kanan Bawah Magenta", "block.minecraft.banner.square_bottom_right.orange": "Kotak Pojok Kanan Bawah Jingga", "block.minecraft.banner.square_bottom_right.pink": "Kotak Pojok Kanan Bawah Merah Jambu", "block.minecraft.banner.square_bottom_right.purple": "Kotak Pojok Kanan Bawah Ungu", "block.minecraft.banner.square_bottom_right.red": "Kotak Pojok Kanan Bawah Merah", "block.minecraft.banner.square_bottom_right.white": "Kotak Pojok Kanan Bawah Putih", "block.minecraft.banner.square_bottom_right.yellow": "Kotak Pojok Kanan Bawah Kuning", "block.minecraft.banner.square_top_left.black": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.blue": "Kotak Pojok Kiri Atas Biru", "block.minecraft.banner.square_top_left.brown": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.cyan": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.gray": "Kotak Pojok Kiri Atas <PERSON>bu", "block.minecraft.banner.square_top_left.green": "Kotak Pojok Kiri At<PERSON>", "block.minecraft.banner.square_top_left.light_blue": "Kotak Pojok Kiri Atas Biru Muda", "block.minecraft.banner.square_top_left.light_gray": "Kotak Pojok Kiri Atas Kelabu <PERSON>", "block.minecraft.banner.square_top_left.lime": "Kotak Pojok Kiri Atas Hi<PERSON>u Pupus", "block.minecraft.banner.square_top_left.magenta": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.orange": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.pink": "Kotak Pojok Kiri Atas Merah Jambu", "block.minecraft.banner.square_top_left.purple": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.red": "Kotak Pojok Kiri Atas Merah", "block.minecraft.banner.square_top_left.white": "Kotak Pojok Kiri <PERSON>", "block.minecraft.banner.square_top_left.yellow": "Kotak Pojok Kiri Atas Kuning", "block.minecraft.banner.square_top_right.black": "Kotak Pojok Kanan Atas Hitam", "block.minecraft.banner.square_top_right.blue": "Kotak Pojok Kanan Atas Biru", "block.minecraft.banner.square_top_right.brown": "Kotak Pojok Kanan Atas Cokelat", "block.minecraft.banner.square_top_right.cyan": "Kotak Pojok Kanan Atas Sian", "block.minecraft.banner.square_top_right.gray": "Kotak Pojok Kanan Atas Kelabu", "block.minecraft.banner.square_top_right.green": "Kotak Pojok Kanan Atas Hijau", "block.minecraft.banner.square_top_right.light_blue": "Kotak Pojok Kanan Atas Biru Muda", "block.minecraft.banner.square_top_right.light_gray": "Kotak Pojok Kanan Atas Kelabu Muda", "block.minecraft.banner.square_top_right.lime": "Kotak Pojok Kanan Atas Hijau Pupus", "block.minecraft.banner.square_top_right.magenta": "Kotak Pojok Kanan Atas Magenta", "block.minecraft.banner.square_top_right.orange": "Kotak Pojok Kanan Atas Jingga", "block.minecraft.banner.square_top_right.pink": "Kotak Pojok Kanan Atas Merah Jambu", "block.minecraft.banner.square_top_right.purple": "Kotak Pojok Kanan Atas Ungu", "block.minecraft.banner.square_top_right.red": "Kotak Pojok Kanan Atas Merah", "block.minecraft.banner.square_top_right.white": "Kotak Pojok Kanan Atas Putih", "block.minecraft.banner.square_top_right.yellow": "Kotak Pojok Kanan Atas Kuning", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "Bawah <PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "Bawah <PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "Pita Te<PERSON>k Hitam", "block.minecraft.banner.stripe_center.blue": "Pita Tegak Biru", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "Pita Tegak Sian", "block.minecraft.banner.stripe_center.gray": "Pita Tegak Kelabu", "block.minecraft.banner.stripe_center.green": "Pita Tegak Hijau", "block.minecraft.banner.stripe_center.light_blue": "Pita Tegak Biru Muda", "block.minecraft.banner.stripe_center.light_gray": "Pita Tegak Kelabu <PERSON>", "block.minecraft.banner.stripe_center.lime": "Pita Tegak Hijau <PERSON>", "block.minecraft.banner.stripe_center.magenta": "Pita Tegak Magenta", "block.minecraft.banner.stripe_center.orange": "Pita Tegak Jingga", "block.minecraft.banner.stripe_center.pink": "Pita Tegak Merah Jambu", "block.minecraft.banner.stripe_center.purple": "Pita Tegak Ungu", "block.minecraft.banner.stripe_center.red": "Pita Tegak Merah", "block.minecraft.banner.stripe_center.white": "Pita Tegak Putih", "block.minecraft.banner.stripe_center.yellow": "Pita Tegak Kuning", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Serong Ka<PERSON>", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "Ser<PERSON>", "block.minecraft.banner.stripe_downright.pink": "Serong Ki<PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "Ser<PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_left.blue": "Pita Tegak Kiri Biru", "block.minecraft.banner.stripe_left.brown": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_left.cyan": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_left.gray": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.green": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.light_gray": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.lime": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_left.magenta": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_left.orange": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.pink": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.purple": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.red": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_left.white": "Pita Tegak Kiri Putih", "block.minecraft.banner.stripe_left.yellow": "Pita Tegak Ki<PERSON>", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON> Men<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON> Men<PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Pita Te<PERSON>", "block.minecraft.banner.stripe_right.blue": "Pita Tegak Kanan Biru", "block.minecraft.banner.stripe_right.brown": "Pita Tegak <PERSON>", "block.minecraft.banner.stripe_right.cyan": "Pita Tegak Ka<PERSON>", "block.minecraft.banner.stripe_right.gray": "Pita Tegak Kanan <PERSON>bu", "block.minecraft.banner.stripe_right.green": "Pita Tegak Ka<PERSON> Hi<PERSON>u", "block.minecraft.banner.stripe_right.light_blue": "Pita Tegak Kanan B<PERSON>u <PERSON>", "block.minecraft.banner.stripe_right.light_gray": "Pita Tegak Ka<PERSON>", "block.minecraft.banner.stripe_right.lime": "Pita Tegak Ka<PERSON>", "block.minecraft.banner.stripe_right.magenta": "Pita Tegak Ka<PERSON>", "block.minecraft.banner.stripe_right.orange": "Pita Tegak Kanan Jingga", "block.minecraft.banner.stripe_right.pink": "Pita Tegak Kanan Merah Jambu", "block.minecraft.banner.stripe_right.purple": "Sepertiga Kanan Ungu", "block.minecraft.banner.stripe_right.red": "Pita Tegak Kanan Merah", "block.minecraft.banner.stripe_right.white": "Pita Tegak Kanan Putih", "block.minecraft.banner.stripe_right.yellow": "Pita Tegak Kanan Kuning", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "Atas Biru", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Atas B<PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Atas Jing<PERSON>", "block.minecraft.banner.stripe_top.pink": "Atas Merah Jambu", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "Atas Me<PERSON>", "block.minecraft.banner.stripe_top.white": "Atas Putih", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "Segitiga Hitam", "block.minecraft.banner.triangle_bottom.blue": "Segitiga Biru", "block.minecraft.banner.triangle_bottom.brown": "Segitiga <PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Segitiga Sian", "block.minecraft.banner.triangle_bottom.gray": "Segitiga Kelabu", "block.minecraft.banner.triangle_bottom.green": "Segitiga Hijau", "block.minecraft.banner.triangle_bottom.light_blue": "Segitiga Biru Muda", "block.minecraft.banner.triangle_bottom.light_gray": "Segitiga Kelabu Muda", "block.minecraft.banner.triangle_bottom.lime": "Segitiga Hijau Pupus", "block.minecraft.banner.triangle_bottom.magenta": "Segitiga Magenta", "block.minecraft.banner.triangle_bottom.orange": "Segitiga Jingga", "block.minecraft.banner.triangle_bottom.pink": "Segitiga Merah Jambu", "block.minecraft.banner.triangle_bottom.purple": "Segitiga Ungu", "block.minecraft.banner.triangle_bottom.red": "Segitiga Merah", "block.minecraft.banner.triangle_bottom.white": "Segitiga Putih", "block.minecraft.banner.triangle_bottom.yellow": "Segitiga Kuning", "block.minecraft.banner.triangle_top.black": "Segitiga Terbalik Hitam", "block.minecraft.banner.triangle_top.blue": "Segitiga Terbalik Biru", "block.minecraft.banner.triangle_top.brown": "Segitiga Terbalik Cokelat", "block.minecraft.banner.triangle_top.cyan": "Segitiga Terbalik Sian", "block.minecraft.banner.triangle_top.gray": "Segitiga Terbalik Kelabu", "block.minecraft.banner.triangle_top.green": "Segitiga Terbalik Hijau", "block.minecraft.banner.triangle_top.light_blue": "Segitiga Terbalik Biru Muda", "block.minecraft.banner.triangle_top.light_gray": "Segitiga Terbalik Kelabu Muda", "block.minecraft.banner.triangle_top.lime": "Segitiga Terbalik Hijau Pupus", "block.minecraft.banner.triangle_top.magenta": "Segitiga Terbalik Magenta", "block.minecraft.banner.triangle_top.orange": "Segitiga Terbalik Jingga", "block.minecraft.banner.triangle_top.pink": "Segitiga Terbalik Merah Jambu", "block.minecraft.banner.triangle_top.purple": "Segitiga Terbalik Ungu", "block.minecraft.banner.triangle_top.red": "Segitiga Terbalik Merah", "block.minecraft.banner.triangle_top.white": "Segitiga Terbalik Putih", "block.minecraft.banner.triangle_top.yellow": "Segitiga Terbalik Kuning", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.orange": "Bertakuk <PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON> Merah Jambu", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.yellow": "Bertakuk <PERSON>", "block.minecraft.barrel": "<PERSON>", "block.minecraft.barrier": "Penghalang", "block.minecraft.basalt": "Basal", "block.minecraft.beacon": "Suar", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bed.no_sleep": "Anda hanya dapat tidur saat malam atau selama badai petir", "block.minecraft.bed.not_safe": "<PERSON>a tak dapat tidur sekarang, banyak monster dekat sini", "block.minecraft.bed.obstructed": "Tempat tidur ini terhalangi", "block.minecraft.bed.occupied": "Tempat tidur ini ditempati", "block.minecraft.bed.too_far_away": "<PERSON>a tidak dapat be<PERSON> sekara<PERSON>, tempat tidur terlalu jauh", "block.minecraft.bedrock": "<PERSON><PERSON>", "block.minecraft.bee_nest": "Sarang Lebah", "block.minecraft.beehive": "Sarang Lebah Buatan", "block.minecraft.beetroots": "Bit", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "Batang Daun Te<PERSON> Besar", "block.minecraft.birch_button": "<PERSON><PERSON>", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_fence": "<PERSON><PERSON>", "block.minecraft.birch_fence_gate": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON>", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Gelondong <PERSON>", "block.minecraft.birch_planks": "<PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "<PERSON><PERSON>", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "Tangga <PERSON>ula", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON> Din<PERSON>", "block.minecraft.birch_wall_sign": "<PERSON><PERSON>", "block.minecraft.birch_wood": "<PERSON><PERSON>", "block.minecraft.black_banner": "Spanduk Hitam", "block.minecraft.black_bed": "Tempat Tidur Hitam", "block.minecraft.black_candle": "<PERSON><PERSON>", "block.minecraft.black_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "Terakota Berglasir Hitam", "block.minecraft.black_shulker_box": "<PERSON><PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON>", "block.minecraft.black_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.black_terracotta": "Terakota Hitam", "block.minecraft.black_wool": "<PERSON><PERSON>", "block.minecraft.blackstone": "<PERSON><PERSON>", "block.minecraft.blackstone_slab": "Lempeng Batu Hitam", "block.minecraft.blackstone_stairs": "Tangga Batu <PERSON>", "block.minecraft.blackstone_wall": "Tembok Batu Hitam", "block.minecraft.blast_furnace": "<PERSON><PERSON>", "block.minecraft.blue_banner": "Spanduk Biru", "block.minecraft.blue_bed": "Tempat Tidur Biru", "block.minecraft.blue_candle": "<PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "Terakota Berglasir Biru", "block.minecraft.blue_ice": "<PERSON><PERSON> B<PERSON>u", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "Kotak Shulker Biru", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "Panel Kaca Berwarna Biru", "block.minecraft.blue_terracotta": "Terakota Biru", "block.minecraft.blue_wool": "Wol Biru", "block.minecraft.bone_block": "Balok Tulang", "block.minecraft.bookshelf": "<PERSON><PERSON>", "block.minecraft.brain_coral": "Karang Otak", "block.minecraft.brain_coral_block": "Balok Karang Otak", "block.minecraft.brain_coral_fan": "Kipas Laut Otak", "block.minecraft.brain_coral_wall_fan": "Kipas Laut O<PERSON>", "block.minecraft.brewing_stand": "Alat Peramu", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.brick_stairs": "Tangga Bata", "block.minecraft.brick_wall": "Tembok Bata", "block.minecraft.bricks": "Balok Bata", "block.minecraft.brown_banner": "Spanduk <PERSON>", "block.minecraft.brown_bed": "Tempat Tidur <PERSON>lat", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "Terakota Berglasir <PERSON>", "block.minecraft.brown_mushroom": "<PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "Balok <PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.brown_terracotta": "Terakota Cokelat", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "Karang Gelembung", "block.minecraft.bubble_coral_block": "Balok Karang Gelembung", "block.minecraft.bubble_coral_fan": "Kipas Laut Gelembung", "block.minecraft.bubble_coral_wall_fan": "Kipas Laut Gelembung Dinding", "block.minecraft.budding_amethyst": "Kecubung Berkuncup", "block.minecraft.bush": "Semak", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON>a Kaktus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Sensor Sculk Terkalibrasi", "block.minecraft.campfire": "<PERSON><PERSON>", "block.minecraft.candle": "Lilin", "block.minecraft.candle_cake": "<PERSON><PERSON> den<PERSON>", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON>", "block.minecraft.carved_pumpkin": "<PERSON><PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_vines_plant": "Tan<PERSON>", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Balok Perintah Be<PERSON>ai", "block.minecraft.cherry_button": "<PERSON><PERSON>", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON>nt<PERSON>", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON> G<PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "Gelondong Ceri", "block.minecraft.cherry_planks": "<PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "<PERSON><PERSON>", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_stairs": "Tangga <PERSON>", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON> Din<PERSON>", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON>", "block.minecraft.cherry_wood": "<PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON>", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON>", "block.minecraft.chiseled_quartz_block": "Balok <PERSON>", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON>", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON>", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "<PERSON><PERSON>", "block.minecraft.chiseled_tuff": "<PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON>", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON>", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Bunga Mata Tertutup", "block.minecraft.coal_block": "Balok Batu Bara", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "Lempeng Batu Sabak Kasar", "block.minecraft.cobbled_deepslate_stairs": "Tangga Batu Sabak Kasar", "block.minecraft.cobbled_deepslate_wall": "Tembok Batu Sabak Kasar", "block.minecraft.cobblestone": "<PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Lempeng Batu Kasar", "block.minecraft.cobblestone_stairs": "Tangga Batu Ka<PERSON>", "block.minecraft.cobblestone_wall": "Tembok Batu Kasar", "block.minecraft.cobweb": "<PERSON>aring Laba-Laba", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Balok <PERSON>", "block.minecraft.comparator": "Pembanding Redstone", "block.minecraft.composter": "Ko<PERSON>ster", "block.minecraft.conduit": "Talang Salur", "block.minecraft.copper_block": "Balok <PERSON>mbaga", "block.minecraft.copper_bulb": "<PERSON><PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Bata Batu Sabak Retak", "block.minecraft.cracked_deepslate_tiles": "Ubin Batu Sabak Retak", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "Bata Batu Hitam Gilap Retak", "block.minecraft.cracked_stone_bricks": "Bata <PERSON>", "block.minecraft.crafter": "<PERSON><PERSON>", "block.minecraft.crafting_table": "<PERSON><PERSON>", "block.minecraft.creaking_heart": "<PERSON><PERSON><PERSON>", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON>", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON> Creeper", "block.minecraft.crimson_button": "Tombol Kirmizi", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON>iz<PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "Pintu Pagar Kirmizi", "block.minecraft.crimson_fungus": "<PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "Papan Tanda Gantung Kirmizi", "block.minecraft.crimson_hyphae": "Hifa Kirmizi", "block.minecraft.crimson_nylium": "Nilium Kirmizi", "block.minecraft.crimson_planks": "<PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Pelat Pen<PERSON>", "block.minecraft.crimson_roots": "<PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON>", "block.minecraft.crimson_slab": "Lempeng Kirmizi", "block.minecraft.crimson_stairs": "Tangga Kirmizi", "block.minecraft.crimson_stem": "Batang Kirmizi", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON> Tanda Gantung Dinding Kirmizi", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON>rmiz<PERSON>", "block.minecraft.crying_obsidian": "Obsidian Menangis", "block.minecraft.cut_copper": "Tembaga Potongan", "block.minecraft.cut_copper_slab": "Lempeng Tembaga Potongan", "block.minecraft.cut_copper_stairs": "Tangga Tembaga Potongan", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON>", "block.minecraft.cut_red_sandstone_slab": "Lempeng Batu Pa<PERSON> Me<PERSON>", "block.minecraft.cut_sandstone": "<PERSON><PERSON>", "block.minecraft.cut_sandstone_slab": "Lempeng Batu Pasir Potongan", "block.minecraft.cyan_banner": "Spanduk Sian", "block.minecraft.cyan_bed": "Tempat Tidur Sian", "block.minecraft.cyan_candle": "<PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "Terakota Berglasir Sian", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.cyan_terracotta": "Terakota Sian", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "Tombol Ek Gelap", "block.minecraft.dark_oak_door": "Pintu Ek Gelap", "block.minecraft.dark_oak_fence": "Pagar Ek Gelap", "block.minecraft.dark_oak_fence_gate": "Pintu Pagar Ek Gelap", "block.minecraft.dark_oak_hanging_sign": "Papan Tanda Gantung Ek Gelap", "block.minecraft.dark_oak_leaves": "Dedaunan Ek Gelap", "block.minecraft.dark_oak_log": "Gelondong Ek Gelap", "block.minecraft.dark_oak_planks": "Papan Ek Gelap", "block.minecraft.dark_oak_pressure_plate": "Pelat Penekan Ek Gelap", "block.minecraft.dark_oak_sapling": "Bibit Ek Gelap", "block.minecraft.dark_oak_sign": "<PERSON>n Tanda E<PERSON> Gelap", "block.minecraft.dark_oak_slab": "Lempeng Ek Gelap", "block.minecraft.dark_oak_stairs": "Tangga Ek Gelap", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON> Gelap", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON> Tanda Gantung Dinding Ek Gelap", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON> Ek Gelap", "block.minecraft.dark_oak_wood": "<PERSON><PERSON>", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON> P<PERSON>", "block.minecraft.daylight_detector": "Detektor <PERSON>", "block.minecraft.dead_brain_coral": "Karang Otak Mati", "block.minecraft.dead_brain_coral_block": "Balok Karang Otak Mati", "block.minecraft.dead_brain_coral_fan": "Kipas Laut Otak Mati", "block.minecraft.dead_brain_coral_wall_fan": "Kipas Laut Otak <PERSON>", "block.minecraft.dead_bubble_coral": "Karang Gelembung Mati", "block.minecraft.dead_bubble_coral_block": "Balok Karang Gelembung Mati", "block.minecraft.dead_bubble_coral_fan": "Kipas Laut Gelembung Mati", "block.minecraft.dead_bubble_coral_wall_fan": "Kipas Laut Gelembung Dinding Mati", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral": "<PERSON>ng <PERSON>", "block.minecraft.dead_fire_coral_block": "Balok Karang Api Mati", "block.minecraft.dead_fire_coral_fan": "Kipas Laut A<PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "Kipas Laut A<PERSON>", "block.minecraft.dead_horn_coral": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "Balok Karang Tanduk Mati", "block.minecraft.dead_horn_coral_fan": "Kipas Laut Tandu<PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "Kipas Laut Tan<PERSON>", "block.minecraft.dead_tube_coral": "Karang Pi<PERSON> Mati", "block.minecraft.dead_tube_coral_block": "Balok Karang Pipa Mati", "block.minecraft.dead_tube_coral_fan": "Kipas Laut Pipa Mati", "block.minecraft.dead_tube_coral_wall_fan": "Kipas Laut Pi<PERSON>", "block.minecraft.decorated_pot": "Pa<PERSON>", "block.minecraft.deepslate": "<PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "Lempeng Bata Batu Sabak", "block.minecraft.deepslate_brick_stairs": "Tangga Bata Batu <PERSON>", "block.minecraft.deepslate_brick_wall": "Tembok Bata Batu Sabak", "block.minecraft.deepslate_bricks": "<PERSON><PERSON>", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "Bijih Redstone Batu Sabak", "block.minecraft.deepslate_tile_slab": "Lempeng Ubin Batu <PERSON>", "block.minecraft.deepslate_tile_stairs": "Tangga Ubin Bat<PERSON>", "block.minecraft.deepslate_tile_wall": "Tembok Ubin Batu Sabak", "block.minecraft.deepslate_tiles": "<PERSON><PERSON>", "block.minecraft.detector_rail": "<PERSON><PERSON>", "block.minecraft.diamond_block": "Balok <PERSON>", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_stairs": "Tangga Diorit", "block.minecraft.diorite_wall": "Tembok Diorit", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "Pelempar", "block.minecraft.dragon_egg": "Telur Naga", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Balok <PERSON>", "block.minecraft.dripstone_block": "Balok Batu Tetes", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "<PERSON>lok <PERSON>", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON>", "block.minecraft.end_gateway": "Gapura End", "block.minecraft.end_portal": "Gerbang End", "block.minecraft.end_portal_frame": "Bingkai Gerbang End", "block.minecraft.end_rod": "Tongkat End", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Lempeng Bata Batu End", "block.minecraft.end_stone_brick_stairs": "Tangga Bata Batu End", "block.minecraft.end_stone_brick_wall": "Tembok Bata Batu End", "block.minecraft.end_stone_bricks": "<PERSON><PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Tembaga Pahatan <PERSON>", "block.minecraft.exposed_copper": "Tembaga Terekspos", "block.minecraft.exposed_copper_bulb": "Lampu Tembaga Terekspos", "block.minecraft.exposed_copper_door": "Pintu <PERSON> Terekspos", "block.minecraft.exposed_copper_grate": "Kisi-<PERSON><PERSON>rekspos", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON> Terekspos", "block.minecraft.exposed_cut_copper": "Tembaga Potongan Terekspos", "block.minecraft.exposed_cut_copper_slab": "Lempeng Tembaga Potongan Terekspos", "block.minecraft.exposed_cut_copper_stairs": "Tangga Tembaga Potongan Terekspos", "block.minecraft.farmland": "<PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON>", "block.minecraft.fire": "Api", "block.minecraft.fire_coral": "Karang <PERSON>", "block.minecraft.fire_coral_block": "Balok Karang Api", "block.minecraft.fire_coral_fan": "Kipas Laut Api", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON> Laut A<PERSON>", "block.minecraft.firefly_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.fletching_table": "<PERSON><PERSON>", "block.minecraft.flower_pot": "Pot Bunga", "block.minecraft.flowering_azalea": "Azalea <PERSON>unga", "block.minecraft.flowering_azalea_leaves": "Dedaunan Azalea Berbunga", "block.minecraft.frogspawn": "Telur Katak", "block.minecraft.frosted_ice": "<PERSON><PERSON>", "block.minecraft.furnace": "<PERSON>ng<PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON>", "block.minecraft.glass": "Ka<PERSON>", "block.minecraft.glass_pane": "Panel Kaca", "block.minecraft.glow_lichen": "<PERSON><PERSON><PERSON>", "block.minecraft.glowstone": "<PERSON><PERSON>", "block.minecraft.gold_block": "Balok Emas", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Lempeng Granit", "block.minecraft.granite_stairs": "Tangga Granit", "block.minecraft.granite_wall": "Tembok Granit", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "Balok <PERSON>", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "Spanduk Kelabu", "block.minecraft.gray_bed": "Tempat Tidur Kelabu", "block.minecraft.gray_candle": "<PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "Terakota Berglasir Kelabu", "block.minecraft.gray_shulker_box": "Kotak Shulker <PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "Panel Kaca Be<PERSON>", "block.minecraft.gray_terracotta": "Terakota Kelabu", "block.minecraft.gray_wool": "Wol Kelabu", "block.minecraft.green_banner": "Spanduk Hijau", "block.minecraft.green_bed": "Tempat Tidur Hijau", "block.minecraft.green_candle": "<PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "Terakota Berglasir Hi<PERSON>", "block.minecraft.green_shulker_box": "<PERSON>k <PERSON>lk<PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.green_terracotta": "Terakota Hijau", "block.minecraft.green_wool": "<PERSON><PERSON>u", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Pelat Penekan Pemberat Berat", "block.minecraft.honey_block": "Balok Madu", "block.minecraft.honeycomb_block": "Balok Sarang Madu", "block.minecraft.hopper": "Corong", "block.minecraft.horn_coral": "<PERSON><PERSON>", "block.minecraft.horn_coral_block": "Balok Karang Tanduk", "block.minecraft.horn_coral_fan": "Kipas <PERSON>", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON> Lau<PERSON>", "block.minecraft.ice": "Es", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON>", "block.minecraft.infested_cobblestone": "<PERSON><PERSON>", "block.minecraft.infested_cracked_stone_bricks": "<PERSON><PERSON>", "block.minecraft.infested_deepslate": "<PERSON><PERSON>", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON>", "block.minecraft.infested_stone": "<PERSON><PERSON>", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON>", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Balok Besi", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.jack_o_lantern": "<PERSON><PERSON>", "block.minecraft.jigsaw": "<PERSON><PERSON>k <PERSON>", "block.minecraft.jukebox": "Kotak Lagu", "block.minecraft.jungle_button": "Tombol Rimba", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence": "<PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "<PERSON>nt<PERSON> Rim<PERSON>", "block.minecraft.jungle_hanging_sign": "<PERSON><PERSON> G<PERSON>ung Rim<PERSON>", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "Gelondong Rimba", "block.minecraft.jungle_planks": "<PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_sign": "<PERSON><PERSON>", "block.minecraft.jungle_slab": "Lempeng Rimba", "block.minecraft.jungle_stairs": "Tangga Rimba", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON> Rimba", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON>", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON>", "block.minecraft.ladder": "Tangga", "block.minecraft.lantern": "Lentera", "block.minecraft.lapis_block": "Balok <PERSON>i", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON><PERSON> Ke<PERSON>bung Besar", "block.minecraft.large_fern": "<PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON>", "block.minecraft.leaf_litter": "<PERSON><PERSON>", "block.minecraft.lectern": "Podium", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "Spanduk Biru <PERSON>", "block.minecraft.light_blue_bed": "Tempat Tidur Biru Muda", "block.minecraft.light_blue_candle": "<PERSON><PERSON>", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete": "<PERSON><PERSON>", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON>", "block.minecraft.light_blue_glazed_terracotta": "Terakota Berglasir Biru Muda", "block.minecraft.light_blue_shulker_box": "Kotak <PERSON>lker B<PERSON>", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Panel Kaca Be<PERSON>na B<PERSON>u <PERSON>", "block.minecraft.light_blue_terracotta": "Terakota Biru Muda", "block.minecraft.light_blue_wool": "Wol Biru Muda", "block.minecraft.light_gray_banner": "Spanduk Kelabu Muda", "block.minecraft.light_gray_bed": "Tempat Tidur Kelabu Muda", "block.minecraft.light_gray_candle": "<PERSON><PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Terakota Berglasir Kelabu Mu<PERSON>", "block.minecraft.light_gray_shulker_box": "Kotak <PERSON>lker <PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON>", "block.minecraft.light_gray_stained_glass_pane": "Panel Kaca Be<PERSON>", "block.minecraft.light_gray_terracotta": "Terakota Kelabu Muda", "block.minecraft.light_gray_wool": "Wol Kelabu Muda", "block.minecraft.light_weighted_pressure_plate": "Pelat Penekan Pemberat Ringan", "block.minecraft.lightning_rod": "Penangkal Petir", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON>", "block.minecraft.lime_banner": "Spanduk Hijau <PERSON>", "block.minecraft.lime_bed": "Tempat Tidur Hijau Pupus", "block.minecraft.lime_candle": "<PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "Terakota Berglasir <PERSON>", "block.minecraft.lime_shulker_box": "<PERSON>k <PERSON>lk<PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.lime_terracotta": "Terakota Hijau Pupus", "block.minecraft.lime_wool": "Wol Hijau <PERSON>", "block.minecraft.lodestone": "<PERSON><PERSON>", "block.minecraft.loom": "Alat Tenun", "block.minecraft.magenta_banner": "Spanduk Magenta", "block.minecraft.magenta_bed": "Tempat Tidur Magenta", "block.minecraft.magenta_candle": "<PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON>", "block.minecraft.magenta_glazed_terracotta": "Terakota Berglasir Magenta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON>", "block.minecraft.magenta_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.magenta_terracotta": "Terakota Magenta", "block.minecraft.magenta_wool": "<PERSON><PERSON>", "block.minecraft.magma_block": "Balok Magma", "block.minecraft.mangrove_button": "Tombol Bakau", "block.minecraft.mangrove_door": "Pintu Ba<PERSON>u", "block.minecraft.mangrove_fence": "<PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "Pintu Pagar Bakau", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON> Gantung Bakau", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_log": "Gelondong Bakau", "block.minecraft.mangrove_planks": "<PERSON><PERSON>", "block.minecraft.mangrove_pressure_plate": "<PERSON>elat <PERSON>eka<PERSON>", "block.minecraft.mangrove_propagule": "Propagul Bakau", "block.minecraft.mangrove_roots": "<PERSON><PERSON>", "block.minecraft.mangrove_sign": "<PERSON><PERSON>", "block.minecraft.mangrove_slab": "Lempeng Bakau", "block.minecraft.mangrove_stairs": "Tangga Bakau", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON>ung Dinding Bakau", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON>", "block.minecraft.mangrove_wood": "<PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "Ku<PERSON>up Kecubung Sedang", "block.minecraft.melon": "Semangka", "block.minecraft.melon_stem": "Batang Semangka", "block.minecraft.moss_block": "Balok Lumut", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON>", "block.minecraft.mossy_cobblestone_slab": "Lempeng Batu Kasar <PERSON>", "block.minecraft.mossy_cobblestone_stairs": "Tangga Batu Ka<PERSON>", "block.minecraft.mossy_cobblestone_wall": "Tembok Batu Kasar Be<PERSON>", "block.minecraft.mossy_stone_brick_slab": "Lempeng Bata Batu Berlumut", "block.minecraft.mossy_stone_brick_stairs": "Tangga Bata Batu Berl<PERSON>ut", "block.minecraft.mossy_stone_brick_wall": "Tembok Bata Batu Berlumut", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON>", "block.minecraft.moving_piston": "<PERSON><PERSON>", "block.minecraft.mud": "Lumpur", "block.minecraft.mud_brick_slab": "Lempeng Bata Lumpur", "block.minecraft.mud_brick_stairs": "Tangga Bata Lumpur", "block.minecraft.mud_brick_wall": "Tembok Bata Lumpur", "block.minecraft.mud_bricks": "Bata Lumpur", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON>", "block.minecraft.mushroom_stem": "<PERSON><PERSON>", "block.minecraft.mycelium": "Miselium", "block.minecraft.nether_brick_fence": "<PERSON><PERSON>", "block.minecraft.nether_brick_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_stairs": "<PERSON><PERSON> Bat<PERSON>", "block.minecraft.nether_brick_wall": "Tembok Bata Nether", "block.minecraft.nether_bricks": "Balok <PERSON>", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_portal": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_sprouts": "Kecambah Nether", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Balok Ketuat Nether", "block.minecraft.netherite_block": "Balok Netherit", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Kotak Nada", "block.minecraft.oak_button": "Tombol Ek", "block.minecraft.oak_door": "Pintu Ek", "block.minecraft.oak_fence": "Pagar Ek", "block.minecraft.oak_fence_gate": "Pintu Pagar Ek", "block.minecraft.oak_hanging_sign": "Papan Tanda Gantung Ek", "block.minecraft.oak_leaves": "Dedaunan Ek", "block.minecraft.oak_log": "Gelondong Ek", "block.minecraft.oak_planks": "Papan Ek", "block.minecraft.oak_pressure_plate": "Pelat Penekan Ek", "block.minecraft.oak_sapling": "Bibit Ek", "block.minecraft.oak_sign": "<PERSON><PERSON>", "block.minecraft.oak_slab": "Lempeng Ek", "block.minecraft.oak_stairs": "Tangga Ek", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON> Tanda Gantung Dinding Ek", "block.minecraft.oak_wall_sign": "<PERSON><PERSON>", "block.minecraft.oak_wood": "<PERSON><PERSON>", "block.minecraft.observer": "Pengamat", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON>", "block.minecraft.ominous_banner": "Spanduk Mengerikan", "block.minecraft.open_eyeblossom": "Bunga Mata Terbuka", "block.minecraft.orange_banner": "Spanduk Jingga", "block.minecraft.orange_bed": "Tempat Tidur Jingga", "block.minecraft.orange_candle": "<PERSON><PERSON>", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "Terakota Berglasir Jingga", "block.minecraft.orange_shulker_box": "Kotak <PERSON>lk<PERSON>", "block.minecraft.orange_stained_glass": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panel Ka<PERSON>", "block.minecraft.orange_terracotta": "Terakota Jingga", "block.minecraft.orange_tulip": "<PERSON><PERSON>", "block.minecraft.orange_wool": "Wol Jingga", "block.minecraft.oxeye_daisy": "Marg<PERSON>", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper": "Tembaga <PERSON>", "block.minecraft.oxidized_copper_bulb": "Lampu Tembaga <PERSON>", "block.minecraft.oxidized_copper_door": "Pintu <PERSON>", "block.minecraft.oxidized_copper_grate": "Kisi-<PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "<PERSON>nt<PERSON>", "block.minecraft.oxidized_cut_copper": "Tembaga Potongan Teroksidasi", "block.minecraft.oxidized_cut_copper_slab": "Lempeng Tembaga Potongan Teroksidasi", "block.minecraft.oxidized_cut_copper_stairs": "Tangga Tembaga Potongan Teroksidasi", "block.minecraft.packed_ice": "<PERSON><PERSON>", "block.minecraft.packed_mud": "Lumpur Padat", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_moss_block": "Balok Lumut <PERSON>", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_button": "Tombol Ek Pucat", "block.minecraft.pale_oak_door": "Pintu Ek Pucat", "block.minecraft.pale_oak_fence": "Pagar Ek Pucat", "block.minecraft.pale_oak_fence_gate": "Pintu Pagar Ek Pucat", "block.minecraft.pale_oak_hanging_sign": "Papan Tanda Gantung Ek Pucat", "block.minecraft.pale_oak_leaves": "Dedaunan Ek Pucat", "block.minecraft.pale_oak_log": "Gelondong Ek Pucat", "block.minecraft.pale_oak_planks": "Papan Ek <PERSON>", "block.minecraft.pale_oak_pressure_plate": "Pelat Penekan Ek Pucat", "block.minecraft.pale_oak_sapling": "Bibit Ek Pucat", "block.minecraft.pale_oak_sign": "Papan Tanda Ek P<PERSON>", "block.minecraft.pale_oak_slab": "Lempeng Ek Pucat", "block.minecraft.pale_oak_stairs": "Tangga Ek Pucat", "block.minecraft.pale_oak_trapdoor": "Pintu <PERSON> E<PERSON>", "block.minecraft.pale_oak_wall_hanging_sign": "Papan Tanda Gantung Dinding Ek P<PERSON>t", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON> E<PERSON>", "block.minecraft.pale_oak_wood": "Kayu E<PERSON>", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON>", "block.minecraft.peony": "Peoni", "block.minecraft.petrified_oak_slab": "Lempeng Ek Membatu", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_banner": "Spanduk Merah Jambu", "block.minecraft.pink_bed": "Tempat Tidur Merah Jambu", "block.minecraft.pink_candle": "<PERSON><PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON> Merah Jambu", "block.minecraft.pink_concrete": "<PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON>", "block.minecraft.pink_glazed_terracotta": "Terakota Berglasir Merah Jambu", "block.minecraft.pink_petals": "Mahkota Merah Jambu", "block.minecraft.pink_shulker_box": "Kotak Shulker <PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "Panel Kaca Be<PERSON>warna <PERSON> Jambu", "block.minecraft.pink_terracotta": "Terakota Merah Jambu", "block.minecraft.pink_tulip": "<PERSON><PERSON>", "block.minecraft.pink_wool": "Wol Merah Jambu", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Tanaman <PERSON>", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.player_head": "<PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "Kepala %s", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON>", "block.minecraft.polished_andesite": "Andesit <PERSON>", "block.minecraft.polished_andesite_slab": "Lempeng Andesit Gilap", "block.minecraft.polished_andesite_stairs": "Tangga Andesit Gilap", "block.minecraft.polished_basalt": "<PERSON><PERSON>", "block.minecraft.polished_blackstone": "<PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Lempeng Bata Batu Hitam Gilap", "block.minecraft.polished_blackstone_brick_stairs": "Tangga Bata Batu <PERSON>", "block.minecraft.polished_blackstone_brick_wall": "Tembok Bata Batu Hitam Gilap", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON>", "block.minecraft.polished_blackstone_button": "Tombol Batu Hitam Gilap", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON>t <PERSON>", "block.minecraft.polished_blackstone_slab": "Lempeng Batu Hitam Gilap", "block.minecraft.polished_blackstone_stairs": "Tangga Batu Hitam Gil<PERSON>", "block.minecraft.polished_blackstone_wall": "Tembok Batu Hitam Gilap", "block.minecraft.polished_deepslate": "<PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Lempeng Batu Sabak Gilap", "block.minecraft.polished_deepslate_stairs": "Tangga Batu Sabak Gilap", "block.minecraft.polished_deepslate_wall": "Tembok Batu Sabak Gilap", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Lempeng Di<PERSON>", "block.minecraft.polished_diorite_stairs": "Tangga Diorit Gilap", "block.minecraft.polished_granite": "Granit <PERSON>", "block.minecraft.polished_granite_slab": "Lempeng Granit Gilap", "block.minecraft.polished_granite_stairs": "Tangga Granit Gilap", "block.minecraft.polished_tuff": "<PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "Tangga <PERSON>", "block.minecraft.polished_tuff_wall": "Tembok Tuf Gil<PERSON>", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Bibit Akasia Pot", "block.minecraft.potted_allium": "Bunga Bawang Pot", "block.minecraft.potted_azalea_bush": "Azalea Pot", "block.minecraft.potted_azure_bluet": "Azure Bluet Pot", "block.minecraft.potted_bamboo": "Bambu Pot", "block.minecraft.potted_birch_sapling": "Bibit Betula Pot", "block.minecraft.potted_blue_orchid": "Anggrek Biru Po<PERSON>", "block.minecraft.potted_brown_mushroom": "<PERSON><PERSON>", "block.minecraft.potted_cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_closed_eyeblossom": "Bunga Mata Tertutup Pot", "block.minecraft.potted_cornflower": "Bunga Jagung Pot", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON>", "block.minecraft.potted_crimson_roots": "<PERSON><PERSON>", "block.minecraft.potted_dandelion": "Randa Tapak Pot", "block.minecraft.potted_dark_oak_sapling": "Bibit Ek Gelap Pot", "block.minecraft.potted_dead_bush": "Semak <PERSON>", "block.minecraft.potted_fern": "<PERSON><PERSON>", "block.minecraft.potted_flowering_azalea_bush": "Azalea Berbunga Pot", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON><PERSON> Rimba <PERSON>t", "block.minecraft.potted_lily_of_the_valley": "<PERSON><PERSON>", "block.minecraft.potted_mangrove_propagule": "Propagul Bakau Pot", "block.minecraft.potted_oak_sapling": "Bibit Ek Pot", "block.minecraft.potted_open_eyeblossom": "Bunga Mata Terbuka Pot", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON>", "block.minecraft.potted_oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_pale_oak_sapling": "Bibit Ek Pucat Pot", "block.minecraft.potted_pink_tulip": "<PERSON><PERSON>", "block.minecraft.potted_poppy": "<PERSON><PERSON>", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON>", "block.minecraft.potted_red_tulip": "<PERSON><PERSON>", "block.minecraft.potted_spruce_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_torchflower": "Bunga Obor Pot", "block.minecraft.potted_warped_fungus": "<PERSON><PERSON>", "block.minecraft.potted_warped_roots": "<PERSON><PERSON>", "block.minecraft.potted_white_tulip": "<PERSON><PERSON>", "block.minecraft.potted_wither_rose": "<PERSON><PERSON>", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON>", "block.minecraft.powered_rail": "<PERSON><PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Lempeng Bata Prismarine", "block.minecraft.prismarine_brick_stairs": "Tangga Bata Prismarine", "block.minecraft.prismarine_bricks": "<PERSON><PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON> Prismarine", "block.minecraft.prismarine_wall": "Tembok Prismarine", "block.minecraft.pumpkin": "<PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Batang Labu", "block.minecraft.purple_banner": "Spanduk Ungu", "block.minecraft.purple_bed": "Tempat Tidur Ungu", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON>", "block.minecraft.purple_glazed_terracotta": "Terakota Berglasir Ungu", "block.minecraft.purple_shulker_box": "<PERSON>k <PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "Panel Kaca <PERSON>", "block.minecraft.purple_terracotta": "Terakota Ungu", "block.minecraft.purple_wool": "<PERSON><PERSON>", "block.minecraft.purpur_block": "Balok Purpur", "block.minecraft.purpur_pillar": "Pilar Purpur", "block.minecraft.purpur_slab": "Lempeng Purpur", "block.minecraft.purpur_stairs": "Tangga Purpur", "block.minecraft.quartz_block": "Balok <PERSON>a", "block.minecraft.quartz_bricks": "<PERSON><PERSON>", "block.minecraft.quartz_pillar": "<PERSON><PERSON>", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_stairs": "<PERSON><PERSON>", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Balok Tembaga Mentah", "block.minecraft.raw_gold_block": "Balok Emas Mentah", "block.minecraft.raw_iron_block": "Balok Besi Mentah", "block.minecraft.red_banner": "Spanduk Merah", "block.minecraft.red_bed": "Tempat Tidur Merah", "block.minecraft.red_candle": "<PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "Terakota Berglasir Merah", "block.minecraft.red_mushroom": "<PERSON><PERSON>", "block.minecraft.red_mushroom_block": "Balok Jam<PERSON>", "block.minecraft.red_nether_brick_slab": "Lempeng Bata Nether Merah", "block.minecraft.red_nether_brick_stairs": "Tangga Bata Nether Merah", "block.minecraft.red_nether_brick_wall": "Tembok Bata Nether Merah", "block.minecraft.red_nether_bricks": "<PERSON><PERSON>", "block.minecraft.red_sand": "<PERSON><PERSON>", "block.minecraft.red_sandstone": "<PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Lempeng Batu Pasir Merah", "block.minecraft.red_sandstone_stairs": "Tangga Batu Pa<PERSON>", "block.minecraft.red_sandstone_wall": "Tembok Batu Pasir Merah", "block.minecraft.red_shulker_box": "Kotak <PERSON>lk<PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON>", "block.minecraft.red_stained_glass_pane": "Panel Kaca <PERSON>", "block.minecraft.red_terracotta": "Terakota Merah", "block.minecraft.red_tulip": "<PERSON><PERSON>", "block.minecraft.red_wool": "Wol Merah", "block.minecraft.redstone_block": "Balok Redstone", "block.minecraft.redstone_lamp": "Lampu Redstone", "block.minecraft.redstone_ore": "Bijih Redstone", "block.minecraft.redstone_torch": "Obor Redstone", "block.minecraft.redstone_wall_torch": "<PERSON><PERSON><PERSON> Redstone", "block.minecraft.redstone_wire": "<PERSON><PERSON>", "block.minecraft.reinforced_deepslate": "Batu Sabak Diperkokoh", "block.minecraft.repeater": "Pengulang Redstone", "block.minecraft.repeating_command_block": "Balok Perintah <PERSON>lang", "block.minecraft.resin_block": "<PERSON><PERSON><PERSON>", "block.minecraft.resin_brick_slab": "Lemp<PERSON>", "block.minecraft.resin_brick_stairs": "Tangga Bata <PERSON>", "block.minecraft.resin_brick_wall": "Tembok Bata Resin", "block.minecraft.resin_bricks": "Balok Bat<PERSON>", "block.minecraft.resin_clump": "Gumpalan Resin", "block.minecraft.respawn_anchor": "<PERSON><PERSON>", "block.minecraft.rooted_dirt": "<PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.sand": "Pa<PERSON>", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Lempeng Batu Pasir", "block.minecraft.sandstone_stairs": "Tangga Batu Pa<PERSON>", "block.minecraft.sandstone_wall": "Tembok Batu Pasir", "block.minecraft.scaffolding": "Perancah", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Katalisator Sculk", "block.minecraft.sculk_sensor": "<PERSON><PERSON>", "block.minecraft.sculk_shrieker": "Sculk Penjerit", "block.minecraft.sculk_vein": "<PERSON><PERSON>", "block.minecraft.sea_lantern": "<PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON>", "block.minecraft.set_spawn": "Titik Bangkit ditetapkan", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "<PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON>", "block.minecraft.skeleton_skull": "Tengkorak Bengkarak", "block.minecraft.skeleton_wall_skull": "Tengkorak Dinding Bengkarak", "block.minecraft.slime_block": "Balok Geladir", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON><PERSON>ng Kecil", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON>", "block.minecraft.smoker": "Pengasap", "block.minecraft.smooth_basalt": "<PERSON><PERSON>", "block.minecraft.smooth_quartz": "Balok Kuarsa Hal<PERSON>", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_slab": "Lempeng Batu <PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "Tang<PERSON> Batu <PERSON>", "block.minecraft.smooth_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "Lemp<PERSON>", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "Lemp<PERSON>", "block.minecraft.sniffer_egg": "Tel<PERSON>", "block.minecraft.snow": "Salju", "block.minecraft.snow_block": "Balok Salju", "block.minecraft.soul_campfire": "<PERSON><PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON>", "block.minecraft.soul_lantern": "<PERSON><PERSON>", "block.minecraft.soul_sand": "<PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON>", "block.minecraft.spawn.not_valid": "Tempat tidur atau jangkar pembangkit Anda tidak ada atau terhalang", "block.minecraft.spawner": "Pembangkit Monster", "block.minecraft.spawner.desc1": "Interaksi dengan Telur Pewujudan:", "block.minecraft.spawner.desc2": "Atur Jenis Makhluk", "block.minecraft.sponge": "Spons", "block.minecraft.spore_blossom": "Kembang Spora", "block.minecraft.spruce_button": "<PERSON><PERSON>", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence": "<PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "Pintu <PERSON>", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON> G<PERSON>ung Ce<PERSON>", "block.minecraft.spruce_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_log": "Gelondong Cemara", "block.minecraft.spruce_planks": "<PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "<PERSON><PERSON>", "block.minecraft.spruce_slab": "<PERSON><PERSON><PERSON>mara", "block.minecraft.spruce_stairs": "Tangga Cemara", "block.minecraft.spruce_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON> Din<PERSON>mara", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON>", "block.minecraft.spruce_wood": "<PERSON><PERSON>", "block.minecraft.sticky_piston": "Piston Lengket", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Lempeng Bata Batu", "block.minecraft.stone_brick_stairs": "Tangga Bata Batu", "block.minecraft.stone_brick_wall": "Tembok Bata Batu", "block.minecraft.stone_bricks": "<PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON>u", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "<PERSON><PERSON>", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Gelondong Akasia <PERSON>", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON>", "block.minecraft.stripped_bamboo_block": "Balok Bambu Kupas", "block.minecraft.stripped_birch_log": "Gelondong Betula Kupas", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "Gelondong Ce<PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON>", "block.minecraft.stripped_crimson_hyphae": "Hifa Kirmizi Kupas", "block.minecraft.stripped_crimson_stem": "Batang Kirmizi Kupas", "block.minecraft.stripped_dark_oak_log": "Gelondong Ek Gelap Ku<PERSON>", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON> E<PERSON> Gelap <PERSON>", "block.minecraft.stripped_jungle_log": "Gelondong Rimba Kupas", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "Gelondong Bakau Kupas", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON>", "block.minecraft.stripped_oak_log": "Gelondong Ek Kupas", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "Gelondong Ek Pucat Ku<PERSON>", "block.minecraft.stripped_pale_oak_wood": "Kayu Ek Pucat <PERSON>", "block.minecraft.stripped_spruce_log": "Gelondong Cemara Ku<PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON>", "block.minecraft.stripped_warped_hyphae": "Hifa Kerukut <PERSON>", "block.minecraft.stripped_warped_stem": "Batang Kerukut <PERSON>", "block.minecraft.structure_block": "Balok Struktur", "block.minecraft.structure_void": "<PERSON><PERSON><PERSON>", "block.minecraft.sugar_cane": "Te<PERSON>", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "<PERSON><PERSON>", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_seagrass": "<PERSON><PERSON>", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terakota", "block.minecraft.test_block": "Balok Uji", "block.minecraft.test_instance_block": "Balok Peristiwa Uji", "block.minecraft.tinted_glass": "Kaca Film", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT diatur tidak dapat meledak", "block.minecraft.torch": "<PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Tanaman Bunga <PERSON>", "block.minecraft.trapped_chest": "<PERSON><PERSON>", "block.minecraft.trial_spawner": "Pembangkit Cobaan", "block.minecraft.tripwire": "<PERSON><PERSON>", "block.minecraft.tripwire_hook": "Pengait Kawat <PERSON>ol", "block.minecraft.tube_coral": "<PERSON><PERSON>", "block.minecraft.tube_coral_block": "Balok Karang Pipa", "block.minecraft.tube_coral_fan": "Kipas <PERSON>", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON> Laut <PERSON>", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Lempeng Bata Tu<PERSON>", "block.minecraft.tuff_brick_stairs": "Tangga Bata Tuf", "block.minecraft.tuff_brick_wall": "Tembok Bata Tuf", "block.minecraft.tuff_bricks": "<PERSON><PERSON>", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON>", "block.minecraft.tuff_wall": "Tembok Tuf", "block.minecraft.turtle_egg": "Telur Penyu", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines_plant": "Tan<PERSON>", "block.minecraft.vault": "<PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "<PERSON><PERSON><PERSON>", "block.minecraft.wall_torch": "<PERSON><PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON>", "block.minecraft.warped_fence_gate": "<PERSON>nt<PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "Papan Tanda Gantung Kerukut", "block.minecraft.warped_hyphae": "Hi<PERSON> Kerukut", "block.minecraft.warped_nylium": "Nilium Kerukut", "block.minecraft.warped_planks": "<PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_roots": "<PERSON><PERSON>", "block.minecraft.warped_sign": "<PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "Tangga <PERSON>", "block.minecraft.warped_stem": "Batang <PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON> Tanda Gantung Dinding Kerukut", "block.minecraft.warped_wall_sign": "<PERSON><PERSON>", "block.minecraft.warped_wart_block": "Balok Ketuat Kerukut", "block.minecraft.water": "Air", "block.minecraft.water_cauldron": "Kuali Air", "block.minecraft.waxed_chiseled_copper": "Tembaga Pa<PERSON>", "block.minecraft.waxed_copper_block": "Balok Tembaga Berlilin", "block.minecraft.waxed_copper_bulb": "Lampu Tembaga Berlilin", "block.minecraft.waxed_copper_door": "Pintu Tembaga Berlilin", "block.minecraft.waxed_copper_grate": "Kisi-<PERSON><PERSON>rlili<PERSON>", "block.minecraft.waxed_copper_trapdoor": "<PERSON>ntu <PERSON>mbaga Berlilin", "block.minecraft.waxed_cut_copper": "Tembaga Potongan Berlilin", "block.minecraft.waxed_cut_copper_slab": "Lempeng Tembaga Potongan Berlilin", "block.minecraft.waxed_cut_copper_stairs": "Tangga Tembaga Potongan Berlilin", "block.minecraft.waxed_exposed_chiseled_copper": "Tembaga Pahatan Terekspos Berlilin", "block.minecraft.waxed_exposed_copper": "Tembaga Terekspos Berlilin", "block.minecraft.waxed_exposed_copper_bulb": "Lampu Tembaga Terekspos Berlilin", "block.minecraft.waxed_exposed_copper_door": "Pintu Tembaga Terekspos Berlilin", "block.minecraft.waxed_exposed_copper_grate": "Kisi-<PERSON><PERSON> Terekspos Berlilin", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON>nt<PERSON>mbaga Terekspos Berlilin", "block.minecraft.waxed_exposed_cut_copper": "Tembaga Potongan Terekspos Berlilin", "block.minecraft.waxed_exposed_cut_copper_slab": "Lempeng Tembaga Potongan Terekspos Berlilin", "block.minecraft.waxed_exposed_cut_copper_stairs": "Tangga Tembaga Potongan Terekspos Berlilin", "block.minecraft.waxed_oxidized_chiseled_copper": "Tembaga Pahatan <PERSON> Berlilin", "block.minecraft.waxed_oxidized_copper": "Tembaga Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_copper_bulb": "Lampu Tembaga Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_copper_door": "Pintu Tembaga Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_copper_grate": "Kisi-<PERSON><PERSON> Tembaga Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_copper_trapdoor": "Pintu <PERSON> Tembaga Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_cut_copper": "Tembaga Potongan Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_cut_copper_slab": "Lempeng Tembaga Potongan Teroksidasi Berlilin", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Tangga Tembaga Potongan Teroksidasi Berlilin", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_weathered_copper": "Tembaga Berka<PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "Lampu Tembaga Berkarat <PERSON>", "block.minecraft.waxed_weathered_copper_door": "Pintu Tembaga Berkarat <PERSON>", "block.minecraft.waxed_weathered_copper_grate": "Kisi-<PERSON><PERSON> Berkarat <PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON>nt<PERSON>mbaga Berkarat <PERSON>", "block.minecraft.waxed_weathered_cut_copper": "Tembaga Potongan Berkarat <PERSON>", "block.minecraft.waxed_weathered_cut_copper_slab": "Lempeng Tembaga Potongan Berkarat Be<PERSON>n", "block.minecraft.waxed_weathered_cut_copper_stairs": "Tangga Tembaga Potongan Berkarat Be<PERSON>", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper": "Tembaga Berkarat", "block.minecraft.weathered_copper_bulb": "Lampu Tembaga Berkarat", "block.minecraft.weathered_copper_door": "Pintu Te<PERSON>ga Berkarat", "block.minecraft.weathered_copper_grate": "Kisi-<PERSON><PERSON>rat", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON>mbaga Berkarat", "block.minecraft.weathered_cut_copper": "Tembaga Potongan Berkarat", "block.minecraft.weathered_cut_copper_slab": "Lempeng Tembaga Potongan Berkarat", "block.minecraft.weathered_cut_copper_stairs": "Tangga Tembaga Potongan Berkarat", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON>", "block.minecraft.wet_sponge": "Spons Basah", "block.minecraft.wheat": "G<PERSON><PERSON>", "block.minecraft.white_banner": "Spanduk Putih", "block.minecraft.white_bed": "Tempat Tidur Putih", "block.minecraft.white_candle": "<PERSON><PERSON>", "block.minecraft.white_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "Terakota Berglasir Putih", "block.minecraft.white_shulker_box": "Kotak <PERSON>", "block.minecraft.white_stained_glass": "<PERSON><PERSON>", "block.minecraft.white_stained_glass_pane": "Panel Kaca Be<PERSON>", "block.minecraft.white_terracotta": "Terakota Putih", "block.minecraft.white_tulip": "<PERSON><PERSON>", "block.minecraft.white_wool": "Wol Putih", "block.minecraft.wildflowers": "Bunga Liar", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Tengkorak Bengkarak With<PERSON>", "block.minecraft.wither_skeleton_wall_skull": "Tengkorak Dinding Bengkara<PERSON>", "block.minecraft.yellow_banner": "Spanduk Kuning", "block.minecraft.yellow_bed": "Tempat Tidur Kuning", "block.minecraft.yellow_candle": "<PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> dengan <PERSON>", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Terakota Berglasir Kuning", "block.minecraft.yellow_shulker_box": "<PERSON>k <PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Panel Ka<PERSON> Be<PERSON>", "block.minecraft.yellow_terracotta": "Terakota Kuning", "block.minecraft.yellow_wool": "<PERSON><PERSON>", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON>", "book.byAuthor": "oleh %1$s", "book.edit.title": "<PERSON><PERSON>", "book.editTitle": "<PERSON><PERSON><PERSON><PERSON>:", "book.finalizeButton": "<PERSON><PERSON>", "book.finalizeWarning": "Perhatian! <PERSON><PERSON>a berikan paraf, buku ini tak dapat diubah kembali.", "book.generation.0": "<PERSON><PERSON>", "book.generation.1": "Salinan", "book.generation.2": "Salinan dari salinan", "book.generation.3": "Sobek", "book.invalid.tag": "* Etiket buku tidak sah *", "book.pageIndicator": "Halaman %1$s dari %2$s", "book.page_button.next": "Halaman Be<PERSON>", "book.page_button.previous": "Halaman Sebelumnya", "book.sign.title": "<PERSON><PERSON>", "book.sign.titlebox": "<PERSON><PERSON><PERSON>", "book.signButton": "<PERSON><PERSON>", "book.view.title": "<PERSON>ar <PERSON>", "build.tooHigh": "Batas tinggi untuk membangun adalah %s blok", "chat.cannotSend": "Tidak dapat mengirim pesan obrolan", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klik untuk teleportasi", "chat.copy": "<PERSON><PERSON> ke <PERSON>", "chat.copy.click": "Klik untuk Menyalin ke Papan K<PERSON>", "chat.deleted_marker": "<PERSON>esan obrolan ini telah dihapus oleh peladen.", "chat.disabled.chain_broken": "Obrolan dimatikan sebab rantai putus. Coba sambungkan kembali.", "chat.disabled.expiredProfileKey": "Obrolan dimatikan sebab kunci publik profil kedaluwarsa. Coba sambungkan kembali.", "chat.disabled.invalid_command_signature": "<PERSON><PERSON><PERSON> memiliki tanda tangan argumen yang tak diharapkan atau hilang.", "chat.disabled.invalid_signature": "Obrolan memiliki tanda tangan tidak sah. Coba sambungkan kembali.", "chat.disabled.launcher": "Obrolan dimatikan oleh pengaturan peluncur. Tidak dapat mengirim pesan.", "chat.disabled.missingProfileKey": "Obrolan dimatikan sebab kunci publik profil tidak ada. Coba sambungkan kembali.", "chat.disabled.options": "Obrolan dimatikan di pengaturan klien.", "chat.disabled.out_of_order_chat": "Obrolan yang diterima rusak. Apakah waktu sistem Anda diubah?", "chat.disabled.profile": "Obrolan tidak diizinkan oleh pengaturan akun. Tekan '%s' kembali untuk informasi lebih lanjut.", "chat.disabled.profile.moreInfo": "Obrolan tidak diizinkan oleh pengaturan akun. Tidak dapat mengirim atau melihat pesan.", "chat.editBox": "obrolan", "chat.filtered": "Telah disaring o<PERSON><PERSON> peladen.", "chat.filtered_full": "Peladen telah menyembunyikan pesan Anda dari beberapa pemain.", "chat.link.confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membuka situs berikut?", "chat.link.confirmTrusted": "<PERSON><PERSON><PERSON><PERSON> Anda ingin membuka tautan atau menyalin ke papan klip?", "chat.link.open": "<PERSON><PERSON> <PERSON>", "chat.link.warning": "<PERSON>an pernah membuka tautan dari orang yang tidak Anda percaya!", "chat.queue": "[+%s pesan tertunda]", "chat.square_brackets": "[%s]", "chat.tag.error": "Peladen mengirim pesan tidak sah.", "chat.tag.modified": "Pesan telah diubah oleh peladen. Pesan asli:", "chat.tag.not_secure": "Pesan belum diverifikasi. Tidak dapat dilaporkan.", "chat.tag.system": "Pesan peladen. Tidak dapat dilaporkan.", "chat.tag.system_single_player": "<PERSON><PERSON> peladen.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s telah menyelesaikan tantangan %s", "chat.type.advancement.goal": "%s telah mencapai sasaran %s", "chat.type.advancement.task": "%s telah meraih kema<PERSON> %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON>", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s berkata %s", "chat.validation_error": "Validasi obrolan gagal", "chat_screen.message": "Pesan untuk dikirim: %s", "chat_screen.title": "<PERSON><PERSON> o<PERSON>", "chat_screen.usage": "<PERSON><PERSON><PERSON><PERSON> pesan dan tekan Enter untuk mengirim", "chunk.toast.checkLog": "Lihat log untuk perincian lanjut", "chunk.toast.loadFailure": "Gagal memuat bingkah pada %s", "chunk.toast.lowDiskSpace": "Ruangan diska sedikit!", "chunk.toast.lowDiskSpace.description": "Tidak mungkin dapat menyimpan dunia.", "chunk.toast.saveFailure": "Gagal menyimpan bingkah pada %s", "clear.failed.multiple": "Tidak ada benda ditemukan pada %s pemain", "clear.failed.single": "Tidak ada benda ditemukan pada pemain %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "Biru", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON>", "color.minecraft.lime": "<PERSON><PERSON><PERSON>", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Jingga", "color.minecraft.pink": "<PERSON><PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<--[DI SINI]", "command.context.parse_error": "%s di posisi %s: %s", "command.exception": "Tidak dapat meguraikan perintah: %s", "command.expected.separator": "Mengharapkan spasi mengakhiri satu argumen, tetapi data berekor di<PERSON>ukan", "command.failed": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menja<PERSON>an perintah tersebut", "command.forkLimit": "<PERSON><PERSON><PERSON> maksimum konteks (%s) tercapai", "command.unknown.argument": "Argumen perintah salah", "command.unknown.command": "<PERSON><PERSON><PERSON> tidak dikenal atau tidak leng<PERSON>p, lihat kesalahan di bawah", "commands.advancement.criterionNotFound": "Kemajuan %1$s tidak mengandung patokan '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Tidak dapat memberikan patokan '%s' kemajuan %s sebab telah dimiliki oleh %s pemain", "commands.advancement.grant.criterion.to.many.success": "Memberikan patokan '%s' kemajuan %s ke %s pemain", "commands.advancement.grant.criterion.to.one.failure": "Tidak dapat memberikan patokan '%s' kemajuan %s sebab telah dimiliki oleh %s", "commands.advancement.grant.criterion.to.one.success": "Memberikan patokan '%s' dari kemajuan '%s' kepada %s", "commands.advancement.grant.many.to.many.failure": "Tidak dapat memberikan %s kemajuan kepada %s pemain sebab telah dimiliki", "commands.advancement.grant.many.to.many.success": "Memberikan %s kemajuan kepada %s pemain", "commands.advancement.grant.many.to.one.failure": "Tidak dapat memberikan %s kemajuan kepada %s sebab telah dimiliki", "commands.advancement.grant.many.to.one.success": "Memberikan %s kemajuan kepada %s", "commands.advancement.grant.one.to.many.failure": "Tidak dapat memberikan kemajuan %s kepada %s pemain sebab telah dimiliki", "commands.advancement.grant.one.to.many.success": "Memberikan kemajuan %s kepada %s pemain", "commands.advancement.grant.one.to.one.failure": "Tidak dapat memberikan kemajuan %s kepada %s sebab telah dimiliki", "commands.advancement.grant.one.to.one.success": "Memberikan kemajuan %s kepada %s", "commands.advancement.revoke.criterion.to.many.failure": "Tidak dapat mencabut patokan '%s' kemajuan %s dari %s pemain sebab tidak dimiliki", "commands.advancement.revoke.criterion.to.many.success": "Mencabut patokan'%s' kemajuan %s dari %s pemain", "commands.advancement.revoke.criterion.to.one.failure": "Tidak dapat mencabut patokan '%s' kemajuan %s dari %s sebab tidak dimiliki", "commands.advancement.revoke.criterion.to.one.success": "Mencabut patokan '%s' kemajuan %s dari %s", "commands.advancement.revoke.many.to.many.failure": "Tidak dapat mencabut %s kemajuan dari %s pemain sebab tidak dimiliki", "commands.advancement.revoke.many.to.many.success": "Mencabut %s kemajuan dari %s pemain", "commands.advancement.revoke.many.to.one.failure": "Tidak dapat mencabut %s kemajuan dari %s sebab tidak dimiliki", "commands.advancement.revoke.many.to.one.success": "Mencabut %s kemajuan dari %s", "commands.advancement.revoke.one.to.many.failure": "Tidak dapat mencabut kemajuan %s dari %s pemain sebab tidak dimiliki", "commands.advancement.revoke.one.to.many.success": "Mencabut kemajuan %s dari %s pemain", "commands.advancement.revoke.one.to.one.failure": "Tidak dapat mencabut kemajuan %s dari %s sebab tidak dimiliki", "commands.advancement.revoke.one.to.one.success": "Mencabut kemajuan %s dari %s", "commands.attribute.base_value.get.success": "<PERSON><PERSON> dasar dari atribut %s untuk entitas %s adalah %s", "commands.attribute.base_value.reset.success": "<PERSON><PERSON> dasar untuk atribut %s untuk entitas %s diatur menjadi asali %s", "commands.attribute.base_value.set.success": "<PERSON><PERSON> dasar untuk atribut %s untuk entitas %s diatur menjadi %s", "commands.attribute.failed.entity": "%s bukan entitas sah untuk perintah ini", "commands.attribute.failed.modifier_already_present": "Pengubah %s sudah ada pada atribut %s untuk entitas %s", "commands.attribute.failed.no_attribute": "Entitas %s tidak memiliki atribut %s", "commands.attribute.failed.no_modifier": "Atribut %s untuk entitas %s tidak memiliki pengubah %s", "commands.attribute.modifier.add.success": "Pengubah %s pada atribut %s untuk entitas %s telah ditambahkan", "commands.attribute.modifier.remove.success": "Pengubah %s dari atribut %s untuk entitas %s telah di<PERSON>pus", "commands.attribute.modifier.value.get.success": "<PERSON>lai dari pengubah %s pada atribut %s untuk entitas %s adalah %s", "commands.attribute.value.get.success": "<PERSON>lai dari atribut %s untuk entitas %s adalah %s", "commands.ban.failed": "<PERSON><PERSON><PERSON>, pemain tersebut telah dicekal", "commands.ban.success": "Mencekal %s: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON>, alamat IP tersebut telah dicekal", "commands.banip.info": "Pencekalan ini memengaruhi %s pemain: %s", "commands.banip.invalid": "Alamat IP tidak sah atau pemain tidak dikenal", "commands.banip.success": "Mencekal IP %s: %s", "commands.banlist.entry": "%s telah dicekal oleh %s: %s", "commands.banlist.entry.unknown": "(Tidak diketahui)", "commands.banlist.list": "Terdapat %s pencekalan:", "commands.banlist.none": "Tidak ada pencekalan", "commands.bossbar.create.failed": "Bilah suar dengan pengenal '%s' sudah ada", "commands.bossbar.create.success": "Membuat bilah suar ubah suaian baru %s", "commands.bossbar.get.max": "<PERSON><PERSON> maksimal bilah suar ubah suaian %s sebesar %s", "commands.bossbar.get.players.none": "Bilah suar ubah suaian %s tidak memiliki pemain daring", "commands.bossbar.get.players.some": "<PERSON><PERSON>h suar ubah suaian %s memiliki %s pemain daring: %s", "commands.bossbar.get.value": "<PERSON>lai bilah suar ubah suaian %s sebesar %s", "commands.bossbar.get.visible.hidden": "B<PERSON>h suar ubah suaian %s saat ini tersembunyi", "commands.bossbar.get.visible.visible": "B<PERSON>h suar ubah suaian %s saat ini tampak", "commands.bossbar.list.bars.none": "Tidak ada bilah suar ubah suaian aktif", "commands.bossbar.list.bars.some": "Terdapat %s bilah suar ubah suaian aktif: %s", "commands.bossbar.remove.success": "<PERSON><PERSON>h suar ubah suaian %s telah di<PERSON>", "commands.bossbar.set.color.success": "Warna bilah suar ubah suaian %s telah diubah", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON>, warna tersebut telah merupakan warna bilah suar", "commands.bossbar.set.max.success": "<PERSON><PERSON> maksimal bilah suar ubah suaian %s telah diubah ke %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON>, nilai maksimal tersebut telah merupakan nilai maksimal bilah suar", "commands.bossbar.set.name.success": "<PERSON>a bilah suar ubah suaian %s telah diubah", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON>, nama tersebut telah merupakan nama bilah suar", "commands.bossbar.set.players.success.none": "Bilah suar ubah suaian %s tidak memiliki pemain", "commands.bossbar.set.players.success.some": "Bilah suar ubah suaian %s memiliki %s pemain: %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON><PERSON>, p<PERSON>in tersebut telah pada bilah suar tanpa siapa pun ditambah atau dihapus", "commands.bossbar.set.style.success": "Gaya bilah suar ubah suaian %s telah diubah", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON>, gaya tersebut telah merupakan gaya bilah suar", "commands.bossbar.set.value.success": "<PERSON>lai bilah suar ubah suaian %s telah diubah ke %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON>, nilai tersebut telah merupakan nilai bilah suar", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON> be<PERSON>, bilah suar sudah tersembunyi", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON> be<PERSON>, bilah suar sudah tampak", "commands.bossbar.set.visible.success.hidden": "<PERSON><PERSON>h suar ubah suaian %s sekara<PERSON> tersembunyi", "commands.bossbar.set.visible.success.visible": "<PERSON><PERSON>h suar ubah suaian %s sekarang tampak", "commands.bossbar.unknown": "B<PERSON>h suar dengan pengenal '%s' tidak ada", "commands.clear.success.multiple": "Menghapus %s benda dari %s pemain", "commands.clear.success.single": "Menghapus %s benda dari pemain %s", "commands.clear.test.multiple": "Menemukan %s benda sesuai pada %s pemain", "commands.clear.test.single": "Menemukan %s benda sesuai pada pemain %s", "commands.clone.failed": "Tidak ada balok yang digandakan", "commands.clone.overlap": "<PERSON><PERSON><PERSON> asal dan tujuan tidak boleh tumpang tindih", "commands.clone.success": "Be<PERSON><PERSON>il menggandakan %s balok", "commands.clone.toobig": "Terlalu banyak balok di tempat tersebut (maksimal %s, ditentukan %s)", "commands.damage.invulnerable": "<PERSON><PERSON><PERSON> kebal terhadap tipe serangan yang diberikan", "commands.damage.success": "Memberi serangan sebesar %s pada %s", "commands.data.block.get": "%s pada balok %s, %s, %s setelah faktor skala %s adalah %s", "commands.data.block.invalid": "Balok sasaran bukan suatu entitas balok", "commands.data.block.modified": "Mengubah data balok pada %s, %s, %s", "commands.data.block.query": "%s, %s, %s memiliki data balok sebagai berikut: %s", "commands.data.entity.get": "%s pada %s setelah faktor skala %s adalah %s", "commands.data.entity.invalid": "Tidak dapat mengubah data pemain", "commands.data.entity.modified": "Data entitas %s telah diubah", "commands.data.entity.query": "%s memiliki data entitas sebagai berikut: %s", "commands.data.get.invalid": "Tidak dapat memperoleh %s, hanya etiket angka yang diizinkan", "commands.data.get.multiple": "Argumen ini menerima nilai tunggal NBT", "commands.data.get.unknown": "Tidak dapat memperoleh %s, etiket tidak ada", "commands.data.merge.failed": "<PERSON><PERSON><PERSON>, properti yang ditentukan sudah memiliki nilai-nilai ini", "commands.data.modify.expected_list": "Mengharapkan daftar, mendapatkan: %s", "commands.data.modify.expected_object": "Mengharapkan objek, mendapatkan: %s", "commands.data.modify.expected_value": "Menghara<PERSON><PERSON> nilai, mendapatkan: %s", "commands.data.modify.invalid_index": "Indeks daftar tidak sah: %s", "commands.data.modify.invalid_substring": "Indeks subuntaian tidak sah: %s hingga %s", "commands.data.storage.get": "%s dalam penyimpanan %s set<PERSON>h faktor skala %s adalah %s", "commands.data.storage.modified": "Memodifikasi penyimpanan %s", "commands.data.storage.query": "Penyimpanan %s memiliki konten sebagai berikut: %s", "commands.datapack.create.already_exists": "<PERSON>et dengan nama '%s' telah ada", "commands.datapack.create.invalid_full_name": "<PERSON>a paket baru '%s' tidak sah", "commands.datapack.create.invalid_name": "<PERSON><PERSON><PERSON> tak sah '%s' pada nama paket baru", "commands.datapack.create.io_failure": "Tidak dapat membuat paket dengan nama '%s', simak log", "commands.datapack.create.metadata_encode_failure": "Gagal mengodekan metadata untuk paket dengan nama '%s': %s", "commands.datapack.create.success": "Me<PERSON>uat paket kosong baru dengan nama '%s'", "commands.datapack.disable.failed": "Paket '%s' masih dimatikan!", "commands.datapack.disable.failed.feature": "Paket '%s' tidak dapat dinonaktif<PERSON>, sebab itu adalah bagian dari bendera aktif!", "commands.datapack.enable.failed": "Paket '%s' telah diakti<PERSON>!", "commands.datapack.enable.failed.no_flags": "Paket '%s' tidak dapat diaktifkan, sebab bendera yang diperlukan tidak diaktifkan di dunia ini: %s!", "commands.datapack.list.available.none": "Sudah tidak ada paket data tersedia", "commands.datapack.list.available.success": "Terdapat %s paket data tersedia: %s", "commands.datapack.list.enabled.none": "Tidak ada paket data yang aktif", "commands.datapack.list.enabled.success": "Terdapat %s paket data aktif: %s", "commands.datapack.modify.disable": "Menonaktifkan paket data %s", "commands.datapack.modify.enable": "Mengaktifkan paket data %s", "commands.datapack.unknown": "Paket data '%s' tidak dikenal", "commands.debug.alreadyRunning": "<PERSON><PERSON><PERSON><PERSON> tik sudah dimulai", "commands.debug.function.noRecursion": "Tidak dapat melacak dari dalam fungsi", "commands.debug.function.noReturnRun": "Pelacakan tidak dapat digunakan dengan return run", "commands.debug.function.success.multiple": "Melacak %s perintah dari %s fungsi ke keluaran berkas %s", "commands.debug.function.success.single": "Melacak %s perintah dari fungsi '%s' ke keluaran berkas %s", "commands.debug.function.traceFailed": "Gagal melacak fungsi", "commands.debug.notRunning": "<PERSON><PERSON><PERSON><PERSON> tik belum dimulai", "commands.debug.started": "<PERSON><PERSON><PERSON> tik", "commands.debug.stopped": "Menghentikan pengikhtisaran tik setelah %s detik dan %s tik (%s tik per detik)", "commands.defaultgamemode.success": "Mode permainan asali sekarang adalah %s", "commands.deop.failed": "<PERSON><PERSON><PERSON>, pemain tersebut bukan operator", "commands.deop.success": "%s sudah tidak lagi operator peladen", "commands.dialog.clear.multiple": "Membersihkan dialog untuk %s pemain", "commands.dialog.clear.single": "Membersihkan dialog untuk %s", "commands.dialog.show.multiple": "Menampilkan dialog kepada %s pemain", "commands.dialog.show.single": "Menampilkan dialog kepada %s", "commands.difficulty.failure": "T<PERSON>k be<PERSON>, kesulitan masih diatur ke %s", "commands.difficulty.query": "Kesulitan saat ini adalah %s", "commands.difficulty.success": "Kesulitan telah ditetapkan ke %s", "commands.drop.no_held_items": "Entitas tidak dapat memegang benda apa pun", "commands.drop.no_loot_table": "Entitas %s tidak memiliki tabel jarah", "commands.drop.no_loot_table.block": "Balok %s tidak memiliki tabel jarah", "commands.drop.success.multiple": "Menjatuhkan %s benda", "commands.drop.success.multiple_with_table": "Menjatuhkan %s benda dari tabel jarah %s", "commands.drop.success.single": "Menjatuhkan %s %s", "commands.drop.success.single_with_table": "Menjatuhkan %s %s dari tabel jarah %s", "commands.effect.clear.everything.failed": "Sasaran tidak memiliki efek yang dapat dihapus", "commands.effect.clear.everything.success.multiple": "Menghapus semua efek pada %s sasaran", "commands.effect.clear.everything.success.single": "Menghapus semua efek pada %s", "commands.effect.clear.specific.failed": "Sasaran tidak memiliki efek yang diminta", "commands.effect.clear.specific.success.multiple": "Menghapus efek %s pada %s sasaran", "commands.effect.clear.specific.success.single": "Menghapus efek %s pada %s", "commands.effect.give.failed": "Tidak dapat menerapkan efek ini (sasaran kebal ter<PERSON>ap efek, atau memiliki efek yang lebih kuat)", "commands.effect.give.success.multiple": "Menerapkan efek %s pada %s sasaran", "commands.effect.give.success.single": "Menerapkan efek %s pada %s", "commands.enchant.failed": "<PERSON><PERSON><PERSON>, sasaran tidak memiliki benda apa pun di tangan atau pesona tidak dapat diterapkan", "commands.enchant.failed.entity": "%s bukan entitas yang sah untuk perintah ini", "commands.enchant.failed.incompatible": "%s tidak mendukung pesona tersebut", "commands.enchant.failed.itemless": "%s tidak memegang benda apa pun", "commands.enchant.failed.level": "%s lebih besar dari tingkat maksimal %s yang didukung oleh pesona tersebut", "commands.enchant.success.multiple": "Menerapkan pesona %s kepada %s entitas", "commands.enchant.success.single": "Menerapkan pesona %s terhadap benda milik %s", "commands.execute.blocks.toobig": "Terlalu banyak balok di tempat tersebut (maksimal %s, ditentukan %s)", "commands.execute.conditional.fail": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "commands.execute.conditional.fail_count": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jumlah: %s", "commands.execute.conditional.pass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.execute.conditional.pass_count": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jumlah: %s", "commands.execute.function.instantiationFailure": "Gagal membuat fungsi %s: %s", "commands.experience.add.levels.success.multiple": "Memberikan tingkat pengalaman sejumlah %s kepada %s pemain", "commands.experience.add.levels.success.single": "Memberikan tingkat pengalaman sejumlah %s kepada %s", "commands.experience.add.points.success.multiple": "Memberikan nilai pen<PERSON>aman sejumlah %s kepada %s pemain", "commands.experience.add.points.success.single": "Memberikan nilai pen<PERSON>aman sejumlah %s kepada %s", "commands.experience.query.levels": "%s memiliki tingkat pengalaman sejumlah %s", "commands.experience.query.points": "%s memiliki nilai pengalaman sejumlah %s", "commands.experience.set.levels.success.multiple": "Mengatur tingkat pengalaman sejumlah %s pada %s pemain", "commands.experience.set.levels.success.single": "Mengatur tingkat pengalaman sejumlah %s pada %s", "commands.experience.set.points.invalid": "Tidak dapat mengatur nilai pengalaman lebih dari nilai maksimum untuk tingkat pemain saat ini", "commands.experience.set.points.success.multiple": "<PERSON><PERSON><PERSON> nilai pengalaman sejumlah %s pada %s pemain", "commands.experience.set.points.success.single": "<PERSON><PERSON><PERSON> nilai pengalaman sejumlah %s pada %s", "commands.fill.failed": "Balok tidak terisi", "commands.fill.success": "Be<PERSON><PERSON>il mengisi %s balok", "commands.fill.toobig": "Terlalu banyak balok di tempat tersebut (maksimal %s, ditentukan %s)", "commands.fillbiome.success": "Bioma diatur antara %s, %s, %s dan %s, %s, %s", "commands.fillbiome.success.count": "%s entri bioma diatur antara %s, %s, %s, dan %s, %s, %s", "commands.fillbiome.toobig": "Terlalu banyak balok dalam volume yang ditentukan (maksimum %s, ditentukan %s)", "commands.forceload.added.failure": "Tidak ada bingkah yang ditandai dengan muat paksa", "commands.forceload.added.multiple": "Menandai %s bing<PERSON><PERSON> da<PERSON> %s dari %s hingga %s sebagai muat paksa", "commands.forceload.added.none": "Tidak ada bingkah muat paksa yang ditemukan dalam %s", "commands.forceload.added.single": "Menandai %s bing<PERSON><PERSON> da<PERSON> %s sebagai muat paksa", "commands.forceload.list.multiple": "%s bingkah muat paksa ditemukan dalam %s pada: %s", "commands.forceload.list.single": "<PERSON><PERSON><PERSON> muat paksa ditemukan dalam %s pada: %s", "commands.forceload.query.failure": "Bingkah pada %s dalam %s tidak ditandai sebagai muat paksa", "commands.forceload.query.success": "Bingkah pada %s dalam %s ditandai sebagai muat paksa", "commands.forceload.removed.all": "Menghilangkan tanda muat paksa pada seluruh bing<PERSON>h dalam %s", "commands.forceload.removed.failure": "Tidak ada bingkah yang dihapus dari muat paksa", "commands.forceload.removed.multiple": "Menghilangkan tanda muat paksa pada %s bingkah dalam %s dari %s hingga %s", "commands.forceload.removed.single": "Menghilangkan tanda muat paksa pada bingkah %s dalam %s", "commands.forceload.toobig": "Terlalu banyak bingkah di wilayah tersebut (maksimal %s, ditentukan %s)", "commands.function.error.argument_not_compound": "Jenis argumen tidak sah: %s, mengharapkan COMPOUND", "commands.function.error.missing_argument": "Argumen hilang %2$s untuk fungsi %1$s", "commands.function.error.missing_arguments": "Argumen fungsi %s hilang", "commands.function.error.parse": "Ketika membuat makro %s: Perintah '%s' menyebabkan kesalahan: %s", "commands.function.instantiationFailure": "Gagal membuat fungsi %s: %s", "commands.function.result": "Fungsi %s mengembalikan %s", "commands.function.scheduled.multiple": "Menjalankan fungsi %s", "commands.function.scheduled.no_functions": "Tidak dapat menemukan fungsi apa pun untuk nama %s", "commands.function.scheduled.single": "Menjalankan fungsi %s", "commands.function.success.multiple": "Menjalankan %s perintah dari %s fungsi", "commands.function.success.multiple.result": "Menjalankan %s fungsi", "commands.function.success.single": "Menjalankan %s perintah dari fungsi '%s'", "commands.function.success.single.result": "Fungsi '%2$s' mengembalikan %1$s", "commands.gamemode.success.other": "Atur mode permainan %s menjadi %s", "commands.gamemode.success.self": "Atur mode permainan sendiri menjadi %s", "commands.gamerule.query": "Aturan Permainan %s saat ini diatur ke: %s", "commands.gamerule.set": "Aturan Permainan %s telah diatur ke: %s", "commands.give.failed.toomanyitems": "Tidak dapat memberi %s lebih dari %s", "commands.give.success.multiple": "Memberikan %s %s kepada %s pemain", "commands.give.success.single": "Memberikan %s %s kepada %s", "commands.help.failed": "<PERSON><PERSON><PERSON> tidak dikenal atau izin tidak memadai", "commands.item.block.set.success": "Mengganti petak pada %s, %s, %s dengan %s", "commands.item.entity.set.success.multiple": "Mengganti petak di %s entitas dengan %s", "commands.item.entity.set.success.single": "Mengganti petak di %s dengan %s", "commands.item.source.no_such_slot": "Sumber tidak memiliki petak %s", "commands.item.source.not_a_container": "Posisi sumber %s, %s, %s bukan merupakan wadah", "commands.item.target.no_changed.known_item": "Tidak ada sasaran yang menerima benda %s ke petak %s", "commands.item.target.no_changes": "Tidak ada sasaran yang menerima benda ke petak %s", "commands.item.target.no_such_slot": "Sasaran tidak memiliki petak %s", "commands.item.target.not_a_container": "Posisi sasaran %s, %s, %s bukan merupakan wadah", "commands.jfr.dump.failed": "<PERSON><PERSON> membuang rekaman JFR: %s", "commands.jfr.start.failed": "Gagal memulai pembuatan profil JFR", "commands.jfr.started": "Pembuatan profil JFR dimulai", "commands.jfr.stopped": "Pembuatan profil JFR dihentikan dan dibuang ke %s", "commands.kick.owner.failed": "Tidak bisa mengeluarkan pemilik peladen dari permainan lokal", "commands.kick.singleplayer.failed": "Tidak bisa mengeluarkan pemain dalam permainan luring", "commands.kick.success": "Mengeluarkan %s: %s", "commands.kill.success.multiple": "Membunuh %s entitas", "commands.kill.success.single": "Membunuh %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Terdapat %s dari maksimal %s pemain daring: %s", "commands.locate.biome.not_found": "Tidak dapat menemukan bioma jenis %s dalam jarak wajar", "commands.locate.biome.success": "%s terdekat berada pada %s (sejauh %s blok)", "commands.locate.poi.not_found": "Tidak dapat menemukan titik perhatian dari tipe \"%s\" dalam jarak yang wajar", "commands.locate.poi.success": "%s terdekat berada pada %s (sejauh %s blok)", "commands.locate.structure.invalid": "Tidak dapat menemukan struktur dengan jenis \"%s\"", "commands.locate.structure.not_found": "Tidak dapat menemukan struktur dengan jenis \"%s\" di sekitar sini", "commands.locate.structure.success": "%s terdekat berada pada %s (sejauh %s blok)", "commands.message.display.incoming": "%s berbisik kepada Anda: %s", "commands.message.display.outgoing": "Anda berbisik kepada %s: %s", "commands.op.failed": "<PERSON><PERSON><PERSON>, pemain tersebut sudah merupakan operator", "commands.op.success": "Berhasil menjadikan %s sebagai operator peladen", "commands.pardon.failed": "<PERSON><PERSON><PERSON> be<PERSON>, pemain tersebut telah tidak dicekal", "commands.pardon.success": "Menghapus pencekalan %s", "commands.pardonip.failed": "T<PERSON><PERSON>, alamat IP tersebut telah tidak dicekal", "commands.pardonip.invalid": "Alamat IP tidak sah", "commands.pardonip.success": "Menghapus pencekalan IP %s", "commands.particle.failed": "Part<PERSON>l masih tidak tampak untuk semua pemain", "commands.particle.success": "Menampilkan partikel %s", "commands.perf.alreadyRunning": "<PERSON><PERSON><PERSON><PERSON> kinerja sudah dimulai", "commands.perf.notRunning": "<PERSON><PERSON><PERSON><PERSON> kinerja belum dimulai", "commands.perf.reportFailed": "Gagal membuat laporan awakutu", "commands.perf.reportSaved": "Membuat laporan awakutu di %s", "commands.perf.started": "Memulai 10 detik penjalanan pengikhtisaran kinerja (gunakan '/perf stop' untuk menghentikannya lebih awal)", "commands.perf.stopped": "Menghentikan pengikhtisaran kinerja setelah %s detik dan %s tik (%s tik per detik)", "commands.place.feature.failed": "<PERSON><PERSON> menempa<PERSON>kan fitur", "commands.place.feature.invalid": "Tidak dapat menemukan fitur dengan jenis \"%s\"", "commands.place.feature.success": "Menempatkan \"%s\" pada %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON> k<PERSON>", "commands.place.jigsaw.invalid": "Tidak ada kumpulan pola acu dengan jenis \"%s\"", "commands.place.jigsaw.success": "<PERSON><PERSON><PERSON><PERSON>an kepingan pada %s, %s, %s", "commands.place.structure.failed": "Gagal menempatkan struktur", "commands.place.structure.invalid": "Tidak dapat menemukan struktur dengan jenis \"%s\"", "commands.place.structure.success": "<PERSON><PERSON><PERSON>lkan struktur %s pada %s, %s, %s", "commands.place.template.failed": "Gagal menempatkan pola acu", "commands.place.template.invalid": "Pola acu dengan pengenal \"%s\" tidak ada", "commands.place.template.success": "Memuat pola acu \"%s\" pada %s, %s, %s", "commands.playsound.failed": "Suara terlalu jauh untuk dapat didengar", "commands.playsound.success.multiple": "Memainkan suara %s kepada %s pemain", "commands.playsound.success.single": "Memainkan suara %s kepada %s", "commands.publish.alreadyPublished": "<PERSON><PERSON><PERSON> ma<PERSON>h ber<PERSON>lan pada porta %s", "commands.publish.failed": "Tidak dapat menjalankan permainan lokal", "commands.publish.started": "Permainan lokal dijalankan pada porta %s", "commands.publish.success": "<PERSON><PERSON><PERSON> pada porta %s", "commands.random.error.range_too_large": "Rentang nilai acak tidak boleh melebihi 2147483646", "commands.random.error.range_too_small": "Rentang nilai acak paling tidak 2", "commands.random.reset.all.success": "Mengatur ulang %s urutan acak", "commands.random.reset.success": "Mengatur ulang urutan acak %s", "commands.random.roll": "%s mendapat angka %s (antara %s dan %s)", "commands.random.sample.success": "Nilai acak: %s", "commands.recipe.give.failed": "Tidak ada resep baru yang dipelajari", "commands.recipe.give.success.multiple": "Membuka %s resep untuk %s pemain", "commands.recipe.give.success.single": "Membuka %s resep untuk %s", "commands.recipe.take.failed": "Tidak ada resep yang dapat dilupakan", "commands.recipe.take.success.multiple": "Mengambil %s resep dari %s pemain", "commands.recipe.take.success.single": "Mengambil %s resep dari %s", "commands.reload.failure": "<PERSON>l memuat ulang; menetapkan data lama", "commands.reload.success": "Me<PERSON>at ulang!", "commands.ride.already_riding": "%s sudah <PERSON> %s", "commands.ride.dismount.success": "%s ber<PERSON><PERSON> %s", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON><PERSON> tidak dapat di<PERSON>i", "commands.ride.mount.failure.generic": "%s gagal menunggangi %s", "commands.ride.mount.failure.loop": "Entitas tidak dapat menunggangi dirinya sendiri atau penumpangnya", "commands.ride.mount.failure.wrong_dimension": "Tidak dapat menunggangi entitas yang berada di dimensi yang berbeda", "commands.ride.mount.success": "%s ber<PERSON>il <PERSON> %s", "commands.ride.not_riding": "%s sedang tidak menunggangi kendaraan apa pun", "commands.rotate.success": "Memutarkan %s", "commands.save.alreadyOff": "Penyimpanan telah dimatikan", "commands.save.alreadyOn": "Penyimpanan telah din<PERSON>n", "commands.save.disabled": "Penyimpanan otomatis dimatikan", "commands.save.enabled": "Penyimpanan otomati<PERSON>", "commands.save.failed": "Tidak dapat menyimpan permainan (apakah tersedia ruang yang tersisa?)", "commands.save.saving": "Menyimpan permainan (mungkin memerlukan beberapa saat)", "commands.save.success": "Permainan ters<PERSON>", "commands.schedule.cleared.failure": "Tidak ada jadwal dengan pengenal %s", "commands.schedule.cleared.success": "Menghapus %s jadwal dengan pengenal %s", "commands.schedule.created.function": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi '%s' dalam %s tik pada waktu permainan %s", "commands.schedule.created.tag": "Etiket terjadwal '%s' dalam %s tik pada waktu permainan %s", "commands.schedule.macro": "Tidak dapat men<PERSON> makro", "commands.schedule.same_tick": "Tidak dapat dijad<PERSON>an untuk tik saat ini", "commands.scoreboard.objectives.add.duplicate": "<PERSON>a tujuan yang sama telah ada", "commands.scoreboard.objectives.add.success": "Membuat tujuan baru %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON><PERSON>, petak tampil tersebut telah kosong", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON><PERSON>, petak tampil tersebut telah menampilkan tujuan", "commands.scoreboard.objectives.display.cleared": "Membersihkan seluruh tujuan pada petak tampil %s", "commands.scoreboard.objectives.display.set": "Mengatur petak tampil %s untuk menampilkan tujuan %s", "commands.scoreboard.objectives.list.empty": "Tidak ada tujuan", "commands.scoreboard.objectives.list.success": "Terdapat %s tujuan: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Mematikan penampilan pembaruan otomatis untuk tujuan %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Mengaktifkan penampilan pembaruan otomatis untuk tujuan %s", "commands.scoreboard.objectives.modify.displayname": "Mengubah nama tampilan dari %s ke %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Menghapus format angka asali dari tujuan %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Mengubah format angka asali dari tujuan %s", "commands.scoreboard.objectives.modify.rendertype": "Mengubah jenis pen<PERSON>an tujuan %s", "commands.scoreboard.objectives.remove.success": "Menghapus tujuan %s", "commands.scoreboard.players.add.success.multiple": "Menambahkan %s ke %s untuk %s entitas", "commands.scoreboard.players.add.success.single": "Menambahkan %s ke %s untuk %s (sekarang %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Menghapus nama tampilan untuk %s entitas di %s", "commands.scoreboard.players.display.name.clear.success.single": "Menghapus nama tampilan untuk %s di %s", "commands.scoreboard.players.display.name.set.success.multiple": "Mengubah nama tampilan menjadi %s untuk %s entitas di %s", "commands.scoreboard.players.display.name.set.success.single": "Mengubah nama tampilan menjadi %s untuk %s di %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Menghapus format angka untuk %s entitas di %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Menghapus format angka untuk %s di %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Mengubah format angka untuk %s entitas di %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Mengubah format angka untuk %s di %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON>, pemicu tersebut sudah diaktifkan", "commands.scoreboard.players.enable.invalid": "Pengaktifan hanya berfungsi pada tujuan pemicu", "commands.scoreboard.players.enable.success.multiple": "Pemicu %s telah diaktifkan untuk %s entitas", "commands.scoreboard.players.enable.success.single": "Pemicu %s diaktifkan untuk %s", "commands.scoreboard.players.get.null": "Tidak dapat memperoleh nilai %s untuk %s; tidak ada yang diatur", "commands.scoreboard.players.get.success": "%s memiliki %s %s", "commands.scoreboard.players.list.empty": "Tidak ada entitas yang dila<PERSON>k", "commands.scoreboard.players.list.entity.empty": "%s tidak terdapat nilai yang dapat ditampilkan", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s memiliki %s nilai:", "commands.scoreboard.players.list.success": "Terdapat %s entitas dilacak: %s", "commands.scoreboard.players.operation.success.multiple": "Memperbarui %s untuk %s entitas", "commands.scoreboard.players.operation.success.single": "Mengatur %s untuk %s menjadi %s", "commands.scoreboard.players.remove.success.multiple": "Menghapus %s dari %s untuk %s entitas", "commands.scoreboard.players.remove.success.single": "Menghapus %s dari %s untuk %s (sekarang %s)", "commands.scoreboard.players.reset.all.multiple": "Mengatur ulang semua nilai untuk %s entitas", "commands.scoreboard.players.reset.all.single": "Mengatur ulang semua nilai untuk %s", "commands.scoreboard.players.reset.specific.multiple": "Mengatur ulang %s untuk %s entitas", "commands.scoreboard.players.reset.specific.single": "Mengatur ulang %s untuk %s", "commands.scoreboard.players.set.success.multiple": "Mengatur %s untuk %s entitas menjadi %s", "commands.scoreboard.players.set.success.single": "Tetapkan %s untuk %s menjadi %s", "commands.seed.success": "Benih: %s", "commands.setblock.failed": "Tidak dapat mengatur balok", "commands.setblock.success": "Mengubah balok pada %s, %s, %s", "commands.setidletimeout.success": "Batas waktu tunggu pemain menganggur sekarang %s menit", "commands.setidletimeout.success.disabled": "<PERSON><PERSON><PERSON> tunggu pemain menganggur dinonaktifkan", "commands.setworldspawn.failure.not_overworld": "<PERSON>ya dapat mengatur titik bangkit untuk Permukaan", "commands.setworldspawn.success": "Mengatur titik bangkit dunia di %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Mengatur titik bangkit di %s, %s, %s [%s] dalam %s untuk %s pemain", "commands.spawnpoint.success.single": "Mengatur titik bangkit di %s, %s, %s [%s] dalam %s untuk %s", "commands.spectate.not_spectator": "%s tidak sedang dalam mode penonton", "commands.spectate.self": "Tidak dapat menonton diri sendiri", "commands.spectate.success.started": "Sekarang menonton %s", "commands.spectate.success.stopped": "Tidak lagi menonton entitas", "commands.spreadplayers.failed.entities": "Tidak dapat menyebarkan %s entitas di sekitar %s, %s (terlalu banyak entitas dalam ruang, coba gunakan persebaran paling banyak %s", "commands.spreadplayers.failed.invalid.height": "maxHeight %s tidak sah; diharapkan lebih tinggi dari minimal dunia %s", "commands.spreadplayers.failed.teams": "Tidak dapat menyebarkan %s regu di sekitar %s, %s (terlalu banyak entitas dalam ruang - coba menggunakan sebaran paling banyak %s", "commands.spreadplayers.success.entities": "Menyebarkan %s entitas sekitar %s, %s dengan jarak rata-rata %s blok", "commands.spreadplayers.success.teams": "Menyebarkan %s regu sekitar %s, %s dengan jarak rata-rata %s blok", "commands.stop.stopping": "Menghentikan peladen", "commands.stopsound.success.source.any": "Menghentikan semua suara '%s'", "commands.stopsound.success.source.sound": "Menghentikan suara '%s' dengan sumber '%s'", "commands.stopsound.success.sourceless.any": "<PERSON>ghent<PERSON><PERSON> semua suara", "commands.stopsound.success.sourceless.sound": "Menghentikan suara '%s'", "commands.summon.failed": "Tidak dapat memanggil entitas", "commands.summon.failed.uuid": "Tidak dapat memanggil entitas sebab UUID duplikat", "commands.summon.invalidPosition": "Posisi tidak sah untuk pemanggilan", "commands.summon.success": "Memanggil %s baru", "commands.tag.add.failed": "<PERSON><PERSON><PERSON> telah memiliki etiket tersebut atau memiliki terlalu banyak etiket", "commands.tag.add.success.multiple": "Menambahkan etiket '%s' pada %s entitas", "commands.tag.add.success.single": "Menambahkan etiket '%s' pada %s", "commands.tag.list.multiple.empty": "Tidak ada etiket pada %s entitas", "commands.tag.list.multiple.success": "Sejumlah %s entitas memiliki %s etiket: %s", "commands.tag.list.single.empty": "%s tidak memiliki etiket", "commands.tag.list.single.success": "%s memiliki %s etiket: %s", "commands.tag.remove.failed": "Sasaran tidak memiliki etiket ini", "commands.tag.remove.success.multiple": "Menghapus etiket '%s' dari %s entitas", "commands.tag.remove.success.single": "Menghapus etiket '%s' dari %s", "commands.team.add.duplicate": "<PERSON>a regu yang sama telah ada", "commands.team.add.success": "Regu %s ditambah", "commands.team.empty.success": "Sejumlah %s anggota telah dihapus dari regu %s", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON> be<PERSON>, regu tersebut telah kosong", "commands.team.join.success.multiple": "Menambahkan %s anggota ke regu %s", "commands.team.join.success.single": "Menambahkan %s ke regu %s", "commands.team.leave.success.multiple": "Sejumlah %s anggota telah dihapus dari semua regu", "commands.team.leave.success.single": "%s telah di<PERSON>pus dari semua regu", "commands.team.list.members.empty": "Regu %s tidak memiliki anggota", "commands.team.list.members.success": "Regu %s memiliki %s anggota: %s", "commands.team.list.teams.empty": "Tidak ada regu", "commands.team.list.teams.success": "Terdapat %s regu: %s", "commands.team.option.collisionRule.success": "Aturan tumbukan untuk regu %s sekarang \"%s\"", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON>, aturan tumbukan sudah memiliki nilai tersebut", "commands.team.option.color.success": "Warna regu telah diperbarui dari %s ke %s", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON>, warna tersebut sudah merupakan warna regu", "commands.team.option.deathMessageVisibility.success": "Ketampakan pesan kematian untuk regu %s sekarang \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON>, ketampakan pesan kematian sudah memiliki nilai tersebut", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON> be<PERSON>, tembak teman telah dimatikan untuk regu tersebut", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON><PERSON> be<PERSON>, tembak teman masih diaktifkan untuk regu tersebut", "commands.team.option.friendlyfire.disabled": "Tembak teman dimatikan untuk regu %s", "commands.team.option.friendlyfire.enabled": "Tembak teman diaktifkan untuk regu %s", "commands.team.option.name.success": "Memperbarui nama regu %s", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON>, nama tersebut sudah merupakan nama regu", "commands.team.option.nametagVisibility.success": "Ketampakan label nama untuk regu %s sekarang \"%s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON><PERSON> be<PERSON>, ketampakan label nama sudah memiliki nilai tersebut", "commands.team.option.prefix.success": "Awalan regu ditetapkan ke %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON><PERSON> be<PERSON>, regu tersebut telah tidak dapat melihat rekan regu limunan", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON><PERSON>, regu tersebut telah dapat melihat rekan regu limunan", "commands.team.option.seeFriendlyInvisibles.disabled": "Regu %s sudah tidak dapat melihat rekan regu limunan", "commands.team.option.seeFriendlyInvisibles.enabled": "Regu %s sekarang dapat melihat rekan regu limunan", "commands.team.option.suffix.success": "Akhiran regu ditetapkan ke %s", "commands.team.remove.success": "Menghapus regu %s", "commands.teammsg.failed.noteam": "Anda harus dalam regu agar dapat mengirim pesan kepada regu Anda", "commands.teleport.invalidPosition": "Posisi tidak sah untuk teleportasi", "commands.teleport.success.entity.multiple": "Memindahkan %s entitas ke %s", "commands.teleport.success.entity.single": "Memindahkan %s ke %s", "commands.teleport.success.location.multiple": "Memindahkan %s entitas ke %s, %s, %s", "commands.teleport.success.location.single": "Memindahkan %s ke %s, %s, %s", "commands.test.batch.starting": "<PERSON><PERSON><PERSON> l<PERSON> %s tumpak %s", "commands.test.clear.error.no_tests": "Tidak dapat menemukan uji untuk dibersihkan", "commands.test.clear.success": "Membersihkan %s struktur", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klik untuk menyalin ke papan klip", "commands.test.create.success": "Membuat pemasangan uji untuk uji %s", "commands.test.error.no_test_containing_pos": "Tidak dapat mencari peristiwa uji yang mengandung %s, %s, %s", "commands.test.error.no_test_instances": "<PERSON><PERSON><PERSON>wa uji tidak di<PERSON>n", "commands.test.error.non_existant_test": "Uji %s tidak dapat ditem<PERSON>n", "commands.test.error.structure_not_found": "Struktur uji %s tidak dapat ditemukan", "commands.test.error.test_instance_not_found": "Entitas balok peristiwa uji tidak dapat ditemukan", "commands.test.error.test_instance_not_found.position": "Entitas balok peristiwa uji untuk uji pada %s, %s, %s tidak dapat ditemukan", "commands.test.error.too_large": "Ukuran struktur harus kurang dari %s blok sepanjang setiap sumbu", "commands.test.locate.done": "<PERSON><PERSON><PERSON><PERSON>, ditemukan %s struktur", "commands.test.locate.found": "Ditemukan struktur pada: %s (jarak: %s)", "commands.test.locate.started": "<PERSON><PERSON><PERSON> struktur uji, ini mungkin memerlukan waktu sebentar...", "commands.test.no_tests": "Tidak ada uji untuk di<PERSON>lankan", "commands.test.relative_position": "Posisi relatif terhadap %s: %s", "commands.test.reset.error.no_tests": "Tidak dapat mencari uji untuk diatur ulang", "commands.test.reset.success": "%s struktur diatur ulang", "commands.test.run.no_tests": "Uji tidak di<PERSON>n", "commands.test.run.running": "Menjalankan %s uji...", "commands.test.summary": "Uji permainan selesai! %s uji telah di<PERSON>an", "commands.test.summary.all_required_passed": "<PERSON><PERSON><PERSON> uji yang diperlukan lulus :)", "commands.test.summary.failed": "%s uji yang diperlukan gagal :(", "commands.test.summary.optional_failed": "%s uji opsional gagal", "commands.tick.query.percentiles": "Persentil: P50: %sms P95: %sms P99: %sms, sampel: %s", "commands.tick.query.rate.running": "Laju tik sasaran: %s per detik.\nRata-rata waktu per tik: %sms (Sasaran: %sms)", "commands.tick.query.rate.sprinting": "Laju tik sasaran: %s per detik (diabaikan, hanya rujukan).\nRata-rata waktu per tik: %sms", "commands.tick.rate.success": "Mengatur laju tik sasaran ke %s per detik", "commands.tick.sprint.report": "Sprint selesai dengan %s tik per detik, atau %s ms per tik", "commands.tick.sprint.stop.fail": "Tidak ada sprint tik yang sedang berlangsung", "commands.tick.sprint.stop.success": "Menginterupsi sprint tik", "commands.tick.status.frozen": "<PERSON><PERSON><PERSON><PERSON>", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON>, tetapi tidak bisa mengejar laju tik sasaran", "commands.tick.status.running": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON> normal", "commands.tick.status.sprinting": "Permainan sedang melakukan sprint", "commands.tick.step.fail": "Tidak dapat memajukan permainan - permainan harus dibekukan terlebih dahulu", "commands.tick.step.stop.fail": "Tidak ada pemajuan tik yang sedang berlangsung", "commands.tick.step.stop.success": "Menginterupsi pema<PERSON>an tik", "commands.tick.step.success": "Memajukan sejumlah %s tik", "commands.time.query": "Waktu saat ini adalah %s", "commands.time.set": "Mengatur waktu ke %s", "commands.title.cleared.multiple": "Membersihkan judul untuk %s pemain", "commands.title.cleared.single": "Membersihkan judul untuk %s", "commands.title.reset.multiple": "Mengatur ulang pengaturan judul untuk %s pemain", "commands.title.reset.single": "Mengatur ulang pengaturan judul untuk %s", "commands.title.show.actionbar.multiple": "Menampilkan judul bilah aksi baru untuk %s pemain", "commands.title.show.actionbar.single": "Menampilkan judul bilah aksi baru untuk %s", "commands.title.show.subtitle.multiple": "Menampilkan subjudul baru untuk %s pemain", "commands.title.show.subtitle.single": "Menampilkan subjudul baru untuk %s", "commands.title.show.title.multiple": "Menampilkan judul baru untuk %s pemain", "commands.title.show.title.single": "Menampilkan judul baru untuk %s", "commands.title.times.multiple": "Mengubah waktu tampil judul untuk %s pemain", "commands.title.times.single": "Mengubah waktu tampil judul untuk %s", "commands.transfer.error.no_players": "<PERSON><PERSON><PERSON> minimal satu pemain yang akan dipindahkan", "commands.transfer.success.multiple": "Memindahkan %s pemain ke %s:%s", "commands.transfer.success.single": "Memindahkan %s ke %s:%s", "commands.trigger.add.success": "Memicu %s (men<PERSON>bah %s nilai)", "commands.trigger.failed.invalid": "Anda hanya dapat memicu sasaran dengan jenis 'trigger'", "commands.trigger.failed.unprimed": "Anda belum dapat memicu sasaran ini", "commands.trigger.set.success": "Memicu %s (mengatur nilai sebesar %s)", "commands.trigger.simple.success": "Memicu %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Tidak ada titik jalan di %s", "commands.waypoint.list.success": "Ada %s titik jalan di %s: %s", "commands.waypoint.modify.color": "Warna titik jalan diubah menjadi %s", "commands.waypoint.modify.color.reset": "<PERSON><PERSON><PERSON> ulang warna titik jalan", "commands.waypoint.modify.style": "Mengubah gaya titik jalan", "commands.weather.set.clear": "Mengubah cuaca menjadi cerah", "commands.weather.set.rain": "Mengubah cuaca menjadi hujan", "commands.weather.set.thunder": "Mengubah cuaca menjadi hujan petir", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON> telah dalam daftar putih", "commands.whitelist.add.success": "Menambahkan %s ke dalam daftar putih", "commands.whitelist.alreadyOff": "Daftar putih telah dimatikan", "commands.whitelist.alreadyOn": "<PERSON>ftar putih telah diak<PERSON>fkan", "commands.whitelist.disabled": "Daftar putih telah dimatikan", "commands.whitelist.enabled": "<PERSON>ftar putih telah diak<PERSON>fkan", "commands.whitelist.list": "Terdapat %s pemain dalam daftar putih: %s", "commands.whitelist.none": "Tidak ada pemain dalam daftar putih", "commands.whitelist.reloaded": "<PERSON><PERSON><PERSON> ulang daftar putih", "commands.whitelist.remove.failed": "<PERSON><PERSON><PERSON> tidak dalam daftar putih", "commands.whitelist.remove.success": "Menghapus %s dari daftar putih", "commands.worldborder.center.failed": "<PERSON><PERSON><PERSON> be<PERSON>, batas dunia sudah berpusat di situ", "commands.worldborder.center.success": "Mengatur pusat batas dunia ke %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON>, luka serangan batas dunia sudah sebesar itu", "commands.worldborder.damage.amount.success": "Mengatur serangan batas dunia sebesar %s per balok setiap detik", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON><PERSON> be<PERSON>, jarak penyangga serangan batas dunia sudah pada jarak tersebut", "commands.worldborder.damage.buffer.success": "Mengatur penyangga serangan batas dunia menjadi %s balok", "commands.worldborder.get": "Batas dunia saat ini selebar %s blok", "commands.worldborder.set.failed.big": "Lebar batas dunia tidak boleh lebih besar dari %s blok", "commands.worldborder.set.failed.far": "Batas dunia tidak boleh lebih jauh dari %s blok", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON>, batas dunia sudah memiliki ukuran tersebut", "commands.worldborder.set.failed.small": "Batas dunia tidak boleh lebih kecil dari 1 blok", "commands.worldborder.set.grow": "Menumbuhkan batas dunia sebesar %s blok selama %s detik", "commands.worldborder.set.immediate": "Mengatur batas dunia menjadi selebar %s blok", "commands.worldborder.set.shrink": "Menyusutkan batas dunia menjadi selebar %s blok selama %s detik", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON><PERSON> be<PERSON>, jarak peringatan batas dunia sudah sejauh itu", "commands.worldborder.warning.distance.success": "Mengatur jarak peringatan batas dunia menjadi %s blok", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON> be<PERSON>, waktu peringatan batas dunia sudah selama itu", "commands.worldborder.warning.time.success": "Mengatur waktu peringatan batas dunia ke %s detik", "compliance.playtime.greaterThan24Hours": "<PERSON>a telah bermain lebih dari 24 jam", "compliance.playtime.hours": "Anda telah bermain selama %s jam", "compliance.playtime.message": "<PERSON><PERSON><PERSON> secara berle<PERSON>han dapat mengganggu kehidupan sehari-hari", "connect.aborted": "Di<PERSON><PERSON><PERSON>", "connect.authorizing": "Sedang masuk...", "connect.connecting": "Menghubungkan ke peladen...", "connect.encrypting": "Menyandikan...", "connect.failed": "<PERSON><PERSON>kan ke peladen", "connect.failed.transfer": "Sambungan gagal saat memindahkan ke peladen", "connect.joining": "Bergabung ke dunia...", "connect.negotiating": "Menghubungkan...", "connect.reconfiging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ulang...", "connect.reconfiguring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ulang...", "connect.transferring": "Memindahkan ke peladen baru...", "container.barrel": "<PERSON>", "container.beacon": "Suar", "container.beehive.bees": "Lebah: %s / %s", "container.beehive.honey": "Madu: %s / %s", "container.blast_furnace": "<PERSON><PERSON>", "container.brewing": "Alat Peramu", "container.cartography_table": "<PERSON><PERSON>", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON>", "container.crafter": "<PERSON><PERSON>", "container.crafting": "<PERSON><PERSON><PERSON><PERSON>", "container.creative": "<PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "Pelempar", "container.dropper": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s <PERSON><PERSON><PERSON>i", "container.enchant.lapis.one": "1 Lazuardi", "container.enchant.level.many": "%s Ting<PERSON>a", "container.enchant.level.one": "1 Tingkat Pesona", "container.enchant.level.requirement": "Persyaratan Tingkat: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "<PERSON>ng<PERSON>", "container.grindstone_title": "Perbaiki & Lepas Pesona", "container.hopper": "Corong", "container.inventory": "<PERSON><PERSON><PERSON><PERSON>", "container.isLocked": "%s terkunci!", "container.lectern": "Podium", "container.loom": "Alat Tenun", "container.repair": "Perbaiki & Namai", "container.repair.cost": "Biaya Pesona: %1$s", "container.repair.expensive": "<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>!", "container.shulkerBox": "<PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "dan %s lagi...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Pengasap", "container.spectatorCantOpen": "Tidak bisa dibuka. <PERSON><PERSON><PERSON> belum dihasil<PERSON>.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Tingkatkan Alat & Zirah", "container.upgrade.error_tooltip": "Benda tidak dapat ditingkatkan dengan cara ini", "container.upgrade.missing_template_tooltip": "Tambah Templat Tempa", "controls.keybinds": "Pengaturan Tombol...", "controls.keybinds.duplicateKeybinds": "Tombol ini juga digunakan untuk:\n%s", "controls.keybinds.title": "Pengaturan <PERSON>", "controls.reset": "<PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON>", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Harap memilih bioma", "createWorld.customize.buffet.title": "Pengubahsuaian Bioma Tunggal", "createWorld.customize.flat.height": "Tingg<PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bawah - %s", "createWorld.customize.flat.layer.top": "Atas - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON>", "createWorld.customize.flat.tile": "<PERSON><PERSON>", "createWorld.customize.flat.title": "Pengubahsuaian Superrata", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "<PERSON><PERSON>, inilah beberapa yang telah kami buat sebelumnya!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON>", "createWorld.customize.presets.share": "Ingin berbagi praaturmu dengan la<PERSON>? Gunakan kotak di bawah!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON>", "createWorld.preparing": "Mempersiapkan penciptaan dunia...", "createWorld.tab.game.title": "Permainan", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Dunia", "credits_and_attribution.button.attribution": "Atribusi", "credits_and_attribution.button.credits": "Kredit", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "<PERSON><PERSON><PERSON> dan <PERSON>", "dataPack.bundle.description": "Mengaktifkan benda per<PERSON>baan Kantong", "dataPack.bundle.name": "Kantong", "dataPack.locator_bar.description": "<PERSON><PERSON><PERSON><PERSON> arah pemain lain dalam <PERSON><PERSON><PERSON>", "dataPack.locator_bar.name": "Bilah Pelacak", "dataPack.minecart_improvements.description": "Peningkatan pergerakan Kereta <PERSON>", "dataPack.minecart_improvements.name": "Peningkatan Kereta <PERSON>", "dataPack.redstone_experiments.description": "Percobaan perubahan Redstone", "dataPack.redstone_experiments.name": "Percobaan Redstone", "dataPack.title": "<PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Dagangan Penduduk yang diperbarui", "dataPack.trade_rebalance.name": "Penyeimbangan Dagangan Penduduk", "dataPack.update_1_20.description": "Fitur dan konten baru untuk Minecraft 1.20", "dataPack.update_1_20.name": "Pembaruan 1.20", "dataPack.update_1_21.description": "Fitur dan konten baru untuk Minecraft 1.21", "dataPack.update_1_21.name": "Pembaruan 1.21", "dataPack.validation.back": "Kembali", "dataPack.validation.failed": "Validasi paket data gagal!", "dataPack.validation.reset": "<PERSON><PERSON> ulang ke <PERSON>", "dataPack.validation.working": "Memvalidasi paket data pilihan...", "dataPack.vanilla.description": "Data asali untuk Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON>", "dataPack.winter_drop.description": "Fitur dan konten baru untuk Kelu<PERSON>", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "<PERSON>", "datapackFailure.safeMode.failed.description": "Dunia ini berisi data tersimpan yang tidak sah atau rusak.", "datapackFailure.safeMode.failed.title": "<PERSON>l memuat dunia dalam <PERSON> Aman.", "datapackFailure.title": "Kesalahan dalam paket data yang dipilih saat ini menghalangi pemuatan dunia.\nAnda dapat mencoba memuatnya hanya dengan paket data asali (\"mode aman\"), atau kembali ke menu utama dan memperbaikinya secara manual.", "death.attack.anvil": "%1$s tertimpa paron", "death.attack.anvil.player": "%1$s tertimpa paron saat melawan %2$s", "death.attack.arrow": "%1$s dipanah oleh %2$s", "death.attack.arrow.item": "%1$s dipanah oleh %2$s menggunakan %3$s", "death.attack.badRespawnPoint.link": "Rancangan Permainan Disengaja", "death.attack.badRespawnPoint.message": "%1$s terbunuh oleh %2$s", "death.attack.cactus": "%1$s tertusuk hingga mati", "death.attack.cactus.player": "%1$s ber<PERSON><PERSON> ke kaktus saat mencoba kabur dari %2$s", "death.attack.cramming": "%1$s terlalu digencet", "death.attack.cramming.player": "%1$s tergencet oleh %2$s", "death.attack.dragonBreath": "%1$s dipanggang dalam napas naga", "death.attack.dragonBreath.player": "%1$s dipanggang dalam napas naga oleh %2$s", "death.attack.drown": "%1$s tenggelam", "death.attack.drown.player": "%1$s tenggelam saat mencoba kabur dari %2$s", "death.attack.dryout": "%1$s mati karena de<PERSON>si", "death.attack.dryout.player": "%1$s mati karena dehidrasi saat mencoba kabur dari %2$s", "death.attack.even_more_magic": "%1$s telah dibunuh oleh suatu kekuatan gaib", "death.attack.explosion": "%1$s meledak", "death.attack.explosion.player": "%1$s diledakkan oleh %2$s", "death.attack.explosion.player.item": "%1$s diledakkan oleh %2$s menggunakan %3$s", "death.attack.fall": "%1$s jatuh terlalu keras", "death.attack.fall.player": "%1$s terjatuh keras saat mencoba kabur dari %2$s", "death.attack.fallingBlock": "%1$s tergencet balok jatuh", "death.attack.fallingBlock.player": "%1$s tergencet balok jatuh saat melawan %2$s", "death.attack.fallingStalactite": "%1$s tertusuk stalaktit jatuh", "death.attack.fallingStalactite.player": "%1$s tertusuk stalaktit jatuh saat melawan %2$s", "death.attack.fireball": "%1$s ditembak bola api oleh %2$s", "death.attack.fireball.item": "%1$s ditembak bola api oleh %2$s menggunakan %3$s", "death.attack.fireworks": "%1$s meledak keras", "death.attack.fireworks.item": "%1$s meledak keras sebab kembang api yang ditembak dari %3$s oleh %2$s", "death.attack.fireworks.player": "%1$s meledak keras saat melawan %2$s", "death.attack.flyIntoWall": "%1$s mengalami energi kinetik", "death.attack.flyIntoWall.player": "%1$s mengalami energi kinetik saat mencoba kabur dari %2$s", "death.attack.freeze": "%1$s mati membeku", "death.attack.freeze.player": "%1$s membeku hingga tewas oleh %2$s", "death.attack.generic": "%1$s tewas", "death.attack.generic.player": "%1$s dibunuh oleh %2$s", "death.attack.genericKill": "%1$s dibunuh", "death.attack.genericKill.player": "%1$s dibunuh saat melawan %2$s", "death.attack.hotFloor": "%1$s mendapati lantai adalah lava", "death.attack.hotFloor.player": "%1$s ber<PERSON><PERSON> ke zona bahaya sebab %2$s", "death.attack.inFire": "%1$s tewas terbakar", "death.attack.inFire.player": "%1$s ber<PERSON><PERSON> ke api saat melawan %2$s", "death.attack.inWall": "%1$s tercekik dalam dinding", "death.attack.inWall.player": "%1$s tercekik dalam dinding saat melawan %2$s", "death.attack.indirectMagic": "%1$s dibunuh oleh %2$s menggunakan sihir", "death.attack.indirectMagic.item": "%1$s dibunuh oleh %2$s menggunakan %3$s", "death.attack.lava": "%1$s mencoba berenang di lava", "death.attack.lava.player": "%1$s mencoba berenang di lava untuk kabur dari %2$s", "death.attack.lightningBolt": "%1$s disambar petir", "death.attack.lightningBolt.player": "%1$s disambar petir saat melawan %2$s", "death.attack.mace_smash": "%1$s dibanting oleh %2$s", "death.attack.mace_smash.item": "%1$s dibanting oleh %2$s menggunakan %3$s", "death.attack.magic": "%1$s dibunuh oleh sihir", "death.attack.magic.player": "%1$s dibunuh oleh sihir saat mencoba kabur dari %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON><PERSON>, pesan terlalu panjang untuk dikirimkan seluruhnya. Maaf! Ini versi yang telah dipendekkan: %s", "death.attack.mob": "%1$s dibantai oleh %2$s", "death.attack.mob.item": "%1$s dibantai oleh %2$s menggunakan %3$s", "death.attack.onFire": "%1$s terbakar sampai mati", "death.attack.onFire.item": "%1$s terbakar menjadi abu saat melawan %2$s yang menggunakan %3$s", "death.attack.onFire.player": "%1$s terbakar menjadi abu saat melawan %2$s", "death.attack.outOfWorld": "%1$s jatuh dari dunia", "death.attack.outOfWorld.player": "%1$s tidak ingin hidup di dunia yang sama dengan %2$s", "death.attack.outsideBorder": "%1$s keluar dari batas dunia", "death.attack.outsideBorder.player": "%1$s keluar dari batas dunia saat melawan %2$s", "death.attack.player": "%1$s dibantai oleh %2$s", "death.attack.player.item": "%1$s dibantai oleh %2$s menggunakan %3$s", "death.attack.sonic_boom": "%1$s dimusnahkan oleh jeritan sonik", "death.attack.sonic_boom.item": "%1$s dimusnahkan oleh jeritan sonik saat mencoba kabur dari %2$s yang menggunakan %3$s", "death.attack.sonic_boom.player": "%1$s dimusnahkan oleh jeritan sonik saat mencoba kabur dari %2$s", "death.attack.stalagmite": "%1$s tertusuk stalagmit", "death.attack.stalagmite.player": "%1$s tertusuk stalagmit saat melawan %2$s", "death.attack.starve": "%1$s mati kelaparan", "death.attack.starve.player": "%1$s mati kelaparan saat melawan %2$s", "death.attack.sting": "%1$s disengat hingga mati", "death.attack.sting.item": "%1$s disengat hingga mati oleh %2$s menggunakan %3$s", "death.attack.sting.player": "%1$s disengat hingga mati oleh %2$s", "death.attack.sweetBerryBush": "%1$s tertusuk hingga mati oleh semak buni manis", "death.attack.sweetBerryBush.player": "%1$s tertusuk hingga mati oleh semak buni manis saat mencoba kabur dari %2$s", "death.attack.thorns": "%1$s terbunuh saat mencoba melukai %2$s", "death.attack.thorns.item": "%1$s terbunuh oleh %3$s saat mencoba melukai %2$s", "death.attack.thrown": "%1$s dihajar oleh %2$s", "death.attack.thrown.item": "%1$s dihajar oleh %2$s menggunakan %3$s", "death.attack.trident": "%1$s ditusuk oleh %2$s", "death.attack.trident.item": "%1$s ditusuk oleh %2$s menggunakan %3$s", "death.attack.wither": "%1$s mati kelayuan", "death.attack.wither.player": "%1$s mati kelayuan saat melawan %2$s", "death.attack.witherSkull": "%1$s ditembak dengan tengkorak dari %2$s", "death.attack.witherSkull.item": "%1$s ditembak dengan tengkorak dari %2$s menggunakan %3$s", "death.fell.accident.generic": "%1$s jatuh dari tempat tinggi", "death.fell.accident.ladder": "%1$s jatuh dari tangga", "death.fell.accident.other_climbable": "%1$s jatuh saat memanjat", "death.fell.accident.scaffolding": "%1$s jatuh dari perancah", "death.fell.accident.twisting_vines": "%1$s jatuh dari perambat melilit", "death.fell.accident.vines": "%1$s jatuh dari tumbuhan merambat", "death.fell.accident.weeping_vines": "%1$s jatuh dari perambat berkulai", "death.fell.assist": "%1$s tewas dijatuhkan oleh %2$s", "death.fell.assist.item": "%1$s tewas dijatuhkan oleh %2$s menggunakan %3$s", "death.fell.finish": "%1$s jatuh terlalu jauh dan dihabisi oleh %2$s", "death.fell.finish.item": "%1$s jatuh terlalu jauh dan dihabisi oleh %2$s menggunakan %3$s", "death.fell.killer": "%1$s tewas terjatuh", "deathScreen.quit.confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin keluar?", "deathScreen.respawn": "Bangkit <PERSON>li", "deathScreen.score": "<PERSON><PERSON>", "deathScreen.score.value": "Skor: %s", "deathScreen.spectate": "Tonton Dunia", "deathScreen.title": "Anda <PERSON>!", "deathScreen.title.hardcore": "Permainan <PERSON>ai!", "deathScreen.titleScreen": "<PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H = Petunjuk benda lanjutan", "debug.advanced_tooltips.off": "Petunjuk benda lanjutan: tersemb<PERSON>yi", "debug.advanced_tooltips.on": "Petunjuk benda lanjutan: tampak", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON><PERSON><PERSON> batas bingkah", "debug.chunk_boundaries.off": "<PERSON><PERSON><PERSON> perbat<PERSON>: terse<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.on": "<PERSON><PERSON><PERSON> perbat<PERSON>n: tampak", "debug.clear_chat.help": "F3 + D = Bersihkan obrolan", "debug.copy_location.help": "F3 + C = Salin lokasi sebagai perintah /tp, tahan F3 + C untuk memogokkan permainan", "debug.copy_location.message": "Lokasi disalin ke papan klip", "debug.crash.message": "F3 + C sedang ditekan. Permainan akan mogok kecuali tombol dilepas.", "debug.crash.warning": "Mogok dalam %s...", "debug.creative_spectator.error": "Tidak dapat beralih mode permainan; tidak ada izin", "debug.creative_spectator.help": "F3 + N = Putar mode permainan sebelumnya <-> penonton", "debug.dump_dynamic_textures": "Menyimpan tekstur dinamis ke %s", "debug.dump_dynamic_textures.help": "F3 + S = Simpan tekstur dinamis", "debug.gamemodes.error": "Tidak dapat membuka peralih mode permainan; tidak ada izin", "debug.gamemodes.help": "F3 + F4 = Buka peralih mode permainan", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Berikutnya", "debug.help.help": "F3 + Q = Perlihatkan daftar ini", "debug.help.message": "Pengaturan tombol:", "debug.inspect.client.block": "Data balok dari klien disalin ke dalam papan klip", "debug.inspect.client.entity": "Data entitas dari klien disalin ke papan klip", "debug.inspect.help": "F3 + I = Salin data entitas atau balok ke dalam papan klip", "debug.inspect.server.block": "Data balok dari peladen disalin ke dalam papan klip", "debug.inspect.server.entity": "Data entitas dari peladen disalin ke papan klip", "debug.pause.help": "F3 + Esc = <PERSON><PERSON> tanpa menu jeda (bila jeda memung<PERSON>)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON> saat hilang fokus", "debug.pause_focus.off": "Jeda saat hilang fokus: dimatikan", "debug.pause_focus.on": "Jeda saat hilang fokus: diak<PERSON><PERSON><PERSON>", "debug.prefix": "[<PERSON><PERSON><PERSON><PERSON>]:", "debug.profiling.help": "F3 + L = Mulai/hentikan pembuatan profil", "debug.profiling.start": "Pembuatan profil dimulai selama %s detik. Gunakan F3 + L untuk menghentikannya", "debug.profiling.stop": "Pembuatan profil selesai. <PERSON><PERSON> akan disimpan ke %s", "debug.reload_chunks.help": "F3 + A = <PERSON><PERSON> ulang bing<PERSON>h", "debug.reload_chunks.message": "<PERSON><PERSON><PERSON> ulang semua bingkah", "debug.reload_resourcepacks.help": "F3 + T = <PERSON>at ulang paket sumber", "debug.reload_resourcepacks.message": "<PERSON><PERSON>at ulang paket sumber", "debug.show_hitboxes.help": "F3 + B = Tampilkan kotak pukul", "debug.show_hitboxes.off": "Kotak pukul: terse<PERSON><PERSON>yi", "debug.show_hitboxes.on": "Kotak pukul: tampak", "debug.version.header": "Info versi klien:", "debug.version.help": "F3 + V = Info versi klien", "demo.day.1": "Demo ini akan bertahan selama lima hari permainan. <PERSON><PERSON><PERSON> yang terbaik!", "demo.day.2": "<PERSON>", "demo.day.3": "<PERSON>", "demo.day.4": "<PERSON>", "demo.day.5": "Ini adalah hari terakhir <PERSON>!", "demo.day.6": "<PERSON>a telah melewati hari kelima. Gunakan %s untuk menyimpan tangkapan layar karya <PERSON>a.", "demo.day.warning": "<PERSON><PERSON><PERSON>a hampir habis!", "demo.demoExpired": "Waktu demo telah habis!", "demo.help.buy": "<PERSON>i <PERSON>!", "demo.help.fullWrapped": "Demo ini akan berakhir dalam lima hari permainan (sekitar 1 jam dan 40 menit waktu nyata). <PERSON><PERSON><PERSON> kemajuan untuk bantuan! Selamat bersenang-senang!", "demo.help.inventory": "Gunakan %1$s untuk membuka persediaan <PERSON>a", "demo.help.jump": "Lompat dengan menekan %1$s", "demo.help.later": "Lanjutkan Bermain!", "demo.help.movement": "Gunakan %1$s, %2$s, %3$s, %4$s, dan tetikus untuk bergerak", "demo.help.movementMouse": "Melihat sekitar menggunakan tetikus", "demo.help.movementShort": "Bergerak dengan menekan %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Mode Demo Minecraft", "demo.remainingTime": "Waktu tersisa: %s", "demo.reminder": "Demo telah berakhir. Beli permainan untuk melanjutkan atau buat dunia baru!", "difficulty.lock.question": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengunci kesulitan dunia ini? Ini akan menjadikan dunia ini selalu menjadi %1$s, dan Anda tak akan dapat mengubah itu kembali.", "difficulty.lock.title": "<PERSON><PERSON><PERSON>", "disconnect.endOfStream": "<PERSON><PERSON><PERSON>", "disconnect.exceeded_packet_rate": "Dikeluarkan karena mele<PERSON>hi batas tingkat paket", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Mengabaikan permintaan status", "disconnect.loginFailedInfo": "Gagal log masuk: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON><PERSON>. Harap periksa pengaturan akun Microsoft Anda.", "disconnect.loginFailedInfo.invalidSession": "Sesi tidak sah (Coba jalankan ulang permainan dan peluncur Anda)", "disconnect.loginFailedInfo.serversUnavailable": "Peladen autentikasi saat ini tidak dapat dijangkau. Mohon coba lagi.", "disconnect.loginFailedInfo.userBanned": "Anda dicekal dari permainan daring", "disconnect.lost": "Sambungan Hilang", "disconnect.packetError": "Kesalahan Protokol Jaringan", "disconnect.spam": "Dikeluarkan karena spam", "disconnect.timeout": "<PERSON><PERSON><PERSON>", "disconnect.transfer": "Dipindahkan ke peladen lain", "disconnect.unknownHost": "<PERSON><PERSON> tidak dikenal", "download.pack.failed": "%s dari %s paket gagal diunduh", "download.pack.progress.bytes": "Progres: %s (ukuran keseluruhan tidak diketahui)", "download.pack.progress.percent": "Progres: %s%%", "download.pack.title": "Mengunduh paket sumber %s/%s", "editGamerule.default": "Asali: %s", "editGamerule.title": "Ubah Aturan Per<PERSON>n", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Penyerapan", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.blindness": "Kebutaan", "effect.minecraft.conduit_power": "<PERSON><PERSON>", "effect.minecraft.darkness": "Kegelapan", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "effect.minecraft.fire_resistance": "<PERSON><PERSON>", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Bergegas", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hunger": "<PERSON><PERSON><PERSON>", "effect.minecraft.infested": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_damage": "Cepat Sakit", "effect.minecraft.instant_health": "Ce<PERSON><PERSON>", "effect.minecraft.invisibility": "Limunan", "effect.minecraft.jump_boost": "Pendorong Lompat", "effect.minecraft.levitation": "Melayang", "effect.minecraft.luck": "<PERSON><PERSON><PERSON>ung<PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "<PERSON><PERSON>", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Bercucuran", "effect.minecraft.poison": "Keracunan", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Regenerasi", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "Tumbang Lambat", "effect.minecraft.slowness": "Kelambatan", "effect.minecraft.speed": "Kecepatan", "effect.minecraft.strength": "Kekuatan", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "Kesialan", "effect.minecraft.water_breathing": "Pernapasan Air", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Terembus", "effect.minecraft.wither": "Kelayuan", "effect.none": "Tak Berefek", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinitas Air", "enchantment.minecraft.bane_of_arthropods": "Penumpas Artropoda", "enchantment.minecraft.binding_curse": "Kutukan <PERSON>", "enchantment.minecraft.blast_protection": "<PERSON><PERSON>", "enchantment.minecraft.breach": "Terobos", "enchantment.minecraft.channeling": "Pemusatan", "enchantment.minecraft.density": "Kepadatan", "enchantment.minecraft.depth_strider": "Pej<PERSON>aman", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "<PERSON><PERSON>uh", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "<PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON>ung<PERSON>", "enchantment.minecraft.frost_walker": "Pejalan <PERSON>", "enchantment.minecraft.impaling": "Menikam", "enchantment.minecraft.infinity": "Ketakberhinggaan", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Keberuntungan Laut", "enchantment.minecraft.lure": "Umpan", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Tembakan Ganda", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Kekuatan", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "<PERSON><PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON>", "enchantment.minecraft.respiration": "Pernapasan", "enchantment.minecraft.riptide": "<PERSON><PERSON>", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Kecepatan Jiwa", "enchantment.minecraft.sweeping": "<PERSON><PERSON>", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON>", "enchantment.minecraft.swift_sneak": "Seluduk Cepat", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Tak <PERSON>", "enchantment.minecraft.vanishing_curse": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "<PERSON><PERSON>", "entity.minecraft.acacia_chest_boat": "<PERSON><PERSON>", "entity.minecraft.allay": "Andaru", "entity.minecraft.area_effect_cloud": "<PERSON><PERSON>", "entity.minecraft.armadillo": "Armadilo", "entity.minecraft.armor_stand": "Cagak <PERSON>", "entity.minecraft.arrow": "Panah", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Ra<PERSON>t Ba<PERSON> den<PERSON>", "entity.minecraft.bamboo_raft": "Rakit Bambu", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Lebah", "entity.minecraft.birch_boat": "<PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON>", "entity.minecraft.blaze": "Ke<PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "<PERSON><PERSON><PERSON>", "entity.minecraft.boat": "<PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Pawana", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON>", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "Kucing", "entity.minecraft.cave_spider": "Laba-Laba Gua", "entity.minecraft.cherry_boat": "<PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "<PERSON><PERSON>", "entity.minecraft.chest_boat": "<PERSON><PERSON>", "entity.minecraft.chest_minecart": "<PERSON><PERSON>", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "Kod", "entity.minecraft.command_block_minecart": "<PERSON><PERSON> dengan Balok Perintah", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Perahu E<PERSON>", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON> E<PERSON>", "entity.minecraft.dolphin": "Lumba-Lumba", "entity.minecraft.donkey": "Keledai", "entity.minecraft.dragon_fireball": "Bola <PERSON>", "entity.minecraft.drowned": "Zonggelam", "entity.minecraft.egg": "Telur Terlempar", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "Kristal End", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON>", "entity.minecraft.evoker_fangs": "<PERSON><PERSON>", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON>", "entity.minecraft.experience_orb": "<PERSON><PERSON>", "entity.minecraft.eye_of_ender": "<PERSON>", "entity.minecraft.falling_block": "Balok <PERSON>", "entity.minecraft.falling_block_type": "%s <PERSON><PERSON><PERSON>", "entity.minecraft.fireball": "<PERSON><PERSON>", "entity.minecraft.firework_rocket": "Roket Ke<PERSON>ng A<PERSON>", "entity.minecraft.fishing_bobber": "Pelampung Pancingan", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "<PERSON><PERSON> dengan <PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Bingkai Benda Sinar", "entity.minecraft.glow_squid": "Cumi-<PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON> dengan Corong", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "Zongkering", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "Interaksi", "entity.minecraft.iron_golem": "<PERSON><PERSON>", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON><PERSON>", "entity.minecraft.item_frame": "Bingkai Benda", "entity.minecraft.jungle_boat": "<PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON>", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Simpul Tali", "entity.minecraft.lightning_bolt": "Halilintar", "entity.minecraft.lingering_potion": "<PERSON><PERSON>", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON>", "entity.minecraft.magma_cube": "Kubus Magma", "entity.minecraft.mangrove_boat": "<PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON>", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Bagal", "entity.minecraft.oak_boat": "<PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "Perahu <PERSON>", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Pembangkit Bend<PERSON>", "entity.minecraft.painting": "<PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Perahu E<PERSON>", "entity.minecraft.pale_oak_chest_boat": "Perahu Ek P<PERSON>", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Bayan", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON>bi", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON>", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON><PERSON>", "entity.minecraft.pufferfish": "<PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Pembinasa", "entity.minecraft.salmon": "Salem", "entity.minecraft.sheep": "Domba", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Gegat", "entity.minecraft.skeleton": "Bengkara<PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON>", "entity.minecraft.slime": "G<PERSON><PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON>", "entity.minecraft.sniffer": "Pen<PERSON><PERSON>", "entity.minecraft.snow_golem": "Golem Salju", "entity.minecraft.snowball": "<PERSON><PERSON>", "entity.minecraft.spawner_minecart": "<PERSON><PERSON> dengan Pembangkit Monster", "entity.minecraft.spectral_arrow": "<PERSON><PERSON>", "entity.minecraft.spider": "Laba-Laba", "entity.minecraft.splash_potion": "<PERSON><PERSON>", "entity.minecraft.spruce_boat": "<PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON>", "entity.minecraft.squid": "Cumi-Cumi", "entity.minecraft.stray": "Bengka<PERSON>", "entity.minecraft.strider": "Pengarung", "entity.minecraft.tadpole": "<PERSON><PERSON>bong", "entity.minecraft.text_display": "Tampilan <PERSON>", "entity.minecraft.tnt": "TNT Tersulut", "entity.minecraft.tnt_minecart": "Kereta Tambang dengan TNT", "entity.minecraft.trader_llama": "<PERSON>", "entity.minecraft.trident": "Trisula", "entity.minecraft.tropical_fish": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Botana Hitam", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "Botana Biru", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON> Ku<PERSON>", "entity.minecraft.tropical_fish.predefined.21": "<PERSON>", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Siklid", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Cupang Kembang Gula", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Pengintai", "entity.minecraft.tropical_fish.type.spotty": "Bertotol", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON>", "entity.minecraft.turtle": "<PERSON><PERSON>", "entity.minecraft.vex": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager": "Penduduk", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "Tukang <PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "Pendet<PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Tukang <PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON>", "entity.minecraft.villager.none": "Penduduk", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Tukang Alat", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "<PERSON><PERSON>", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON>", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON>", "entity.not_summonable": "Tidak dapat memanggil entitas dengan jenis %s", "event.minecraft.raid": "<PERSON><PERSON>", "event.minecraft.raid.defeat": "<PERSON><PERSON>", "event.minecraft.raid.defeat.full": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Penyerbu Tersisa: %s", "event.minecraft.raid.victory": "<PERSON><PERSON>", "event.minecraft.raid.victory.full": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "filled_map.buried_treasure": "<PERSON><PERSON>", "filled_map.explorer_jungle": "<PERSON><PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON>", "filled_map.id": "Id #%s", "filled_map.level": "(Tingkat %s/%s)", "filled_map.locked": "Terkun<PERSON>", "filled_map.mansion": "<PERSON><PERSON>", "filled_map.monument": "<PERSON><PERSON>", "filled_map.scale": "Skala 1:%s", "filled_map.trial_chambers": "Peta Bilik Cobaan", "filled_map.unknown": "Peta tak dikenal", "filled_map.village_desert": "<PERSON><PERSON>", "filled_map.village_plains": "Peta Desa Dataran", "filled_map.village_savanna": "Peta Desa Sabana", "filled_map.village_snowy": "Peta Des<PERSON> Be<PERSON>lju", "filled_map.village_taiga": "Peta Des<PERSON>", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Superrata Klasik", "flat_world_preset.minecraft.desert": "Gurun", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Siap Redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Mimpi <PERSON>", "flat_world_preset.minecraft.water_world": "Dunia Air", "flat_world_preset.unknown": "???", "gameMode.adventure": "Mode Petualangan", "gameMode.changed": "Mode permainan Anda telah diperbarui ke %s", "gameMode.creative": "Mode Kreatif", "gameMode.hardcore": "Mode Menantang", "gameMode.spectator": "<PERSON>", "gameMode.survival": "Mode Sintas", "gamerule.allowFireTicksAwayFromPlayer": "<PERSON><PERSON> be<PERSON><PERSON>k jauh dari pemain", "gamerule.allowFireTicksAwayFromPlayer.description": "Mengendalikan apakah api dan lava dapat berdetik pada jarak lebih dari 8 bingkah dari setiap pemain", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON>", "gamerule.blockExplosionDropDecay": "Beberapa balok tidak menjatuhkan jarahan ledakan interaksi balok", "gamerule.blockExplosionDropDecay.description": "Beberapa jatuhan dari balok yang dihancurkan oleh ledakan yang disebabkan oleh interaksi balok hilang dalam ledakan.", "gamerule.category.chat": "<PERSON><PERSON>lan", "gamerule.category.drops": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.misc": "Serb<PERSON>", "gamerule.category.mobs": "Makhluk", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Pembangkitan", "gamerule.category.updates": "Pembaruan Du<PERSON>", "gamerule.commandBlockOutput": "Siarkan keluaran balok perintah", "gamerule.commandModificationBlockLimit": "Batas balok modifikasi perintah", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON> balok yang dapat diubah sekaligus dengan satu perintah, seperti fill atau clone.", "gamerule.disableElytraMovementCheck": "<PERSON><PERSON><PERSON> periksa pergerakan elitron", "gamerule.disablePlayerMovementCheck": "<PERSON><PERSON><PERSON> periksa pergerakan pemain", "gamerule.disableRaids": "<PERSON><PERSON><PERSON> serbuan", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON> waktu", "gamerule.doEntityDrops": "Jatuhkan peralatan entitas", "gamerule.doEntityDrops.description": "Mengendal<PERSON><PERSON> jatuhan dari kereta tamba<PERSON> (termasuk persed<PERSON>), bin<PERSON><PERSON>, per<PERSON>, dll.", "gamerule.doFireTick": "<PERSON><PERSON><PERSON><PERSON> api", "gamerule.doImmediateRespawn": "Bangkit kembali seketika", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doLimitedCrafting": "<PERSON><PERSON><PERSON><PERSON> resep untuk membuat kerajinan", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON>, pemain hanya dapat membuat benda dengan resep telah terbuka.", "gamerule.doMobLoot": "Jatuhkan jarahan makhluk", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> jatuhan sumber daya dari makhluk, term<PERSON><PERSON> bola pengalaman.", "gamerule.doMobSpawning": "Bangkitkan makhluk", "gamerule.doMobSpawning.description": "Beberapa entitas mungkin memiliki aturan terpisah.", "gamerule.doPatrolSpawning": "Bangkitkan ronda <PERSON>ah", "gamerule.doTileDrops": "Jatuhkan balok", "gamerule.doTileDrops.description": "Mengen<PERSON><PERSON><PERSON> jatuhan sumber daya dari balok, term<PERSON><PERSON> bola pengal<PERSON>.", "gamerule.doTraderSpawning": "Bangkitkan Pedagang <PERSON>ling", "gamerule.doVinesSpread": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doVinesSpread.description": "Mengendalikan apakah balok Tumbuhan Merambat menyebar secara acak ke balok yang bersebelahan. Tidak berpengaruh pada balok tumbuhan merambat lainnya seperti Per<PERSON>bat <PERSON>, <PERSON><PERSON><PERSON>, dan se<PERSON><PERSON><PERSON>.", "gamerule.doWardenSpawning": "Bangkit<PERSON>", "gamerule.doWeatherCycle": "Memperbarui cuaca", "gamerule.drowningDamage": "<PERSON>i luka tenggelam", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> yang di<PERSON>par akan hilang saat mati", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON><PERSON> yang di<PERSON> oleh pemain hilang ketika pemain mati.", "gamerule.entitiesWithPassengersCanUsePortals": "Entitas dengan penumpang dapat menggunakan gerbang", "gamerule.entitiesWithPassengersCanUsePortals.description": "Memungkinkan entitas dengan penumpang untuk melakukan teleportasi melalui Gerbang Nether, Gerbang End, dan <PERSON>.", "gamerule.fallDamage": "<PERSON>i luka jatuh", "gamerule.fireDamage": "<PERSON><PERSON> luka bakar", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON> pemain yang sudah mati", "gamerule.forgiveDeadPlayers.description": "Makhluk netral yang terprovokasi berhenti mengejar ketika pemain incaran mati di dekatnya.", "gamerule.freezeDamage": "<PERSON><PERSON> luka beku", "gamerule.globalSoundEvents": "<PERSON><PERSON> semesta", "gamerule.globalSoundEvents.description": "<PERSON><PERSON><PERSON> peristiwa permainan tertentu ter<PERSON>, se<PERSON>i pembang<PERSON> bos, suaranya terdengar di mana pun.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON><PERSON> persed<PERSON>an setelah mati", "gamerule.lavaSourceConversion": "<PERSON><PERSON> be<PERSON>h menjadi sumber", "gamerule.lavaSourceConversion.description": "Ketika lava mengalir dikelilingi dua sisi oleh sumber lava, ia berubah menjadi sumber.", "gamerule.locatorBar": "Aktifkan Bilah Pelacak pemain", "gamerule.locatorBar.description": "<PERSON><PERSON><PERSON>, akan menamp<PERSON>kan bilah yang menun<PERSON>kkan arah pemain.", "gamerule.logAdminCommands": "<PERSON>ark<PERSON> perintah pengelola", "gamerule.maxCommandChainLength": "Batas ukuran rantai perintah", "gamerule.maxCommandChainLength.description": "Diterapkan pada rantai dan fungsi balok perintah.", "gamerule.maxCommandForkCount": "Batas konteks perintah", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON> maksimum konteks yang dapat digunakan oleh perintah seperti 'execute as'.", "gamerule.maxEntityCramming": "Batas entitas tergencet", "gamerule.minecartMaxSpeed": "Kecepatan maksimum Kereta <PERSON>", "gamerule.minecartMaxSpeed.description": "Kecepa<PERSON> maksimum asali untuk Kereta Tambang yang bergerak di atas tanah.", "gamerule.mobExplosionDropDecay": "Beberapa balok tidak akan menjatuhkan jarahan ledakan makhluk", "gamerule.mobExplosionDropDecay.description": "Beberapa jatuhan dari balok yang dihancurkan oleh ledakan yang disebabkan oleh makhluk hilang dalam ledakan.", "gamerule.mobGriefing": "Izinkan tindakan makhluk perusak", "gamerule.naturalRegeneration": "Regenerasi k<PERSON>an", "gamerule.playersNetherPortalCreativeDelay": "Ke<PERSON>lambatan gerbang <PERSON>her pemain mode kreatif", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON><PERSON> (dalam tik) yang diperlukan pemain mode kreatif untuk berdiri di gerbang Nether sebelum berpindah dimensi.", "gamerule.playersNetherPortalDefaultDelay": "Keterlambatan gerbang Nether pemain mode nonkreatif", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON><PERSON> (dalam tik) yang diperlukan oleh pemain mode nonkreatif untuk berdiri di gerbang Nether sebelum berpindah dimensi.", "gamerule.playersSleepingPercentage": "Persentase tidur", "gamerule.playersSleepingPercentage.description": "Persentase pemain yang harus tidur untuk melewati malam.", "gamerule.projectilesCanBreakBlocks": "Proyektil dapat menghancurkan balok", "gamerule.projectilesCanBreakBlocks.description": "Mengendalikan apakah proyektil dampak akan menghancurkan balok yang dapat dihancurkan oleh proyektil tersebut.", "gamerule.randomTickSpeed": "Tingkat kecepatan tik acak", "gamerule.reducedDebugInfo": "Kurangi informasi awakutu", "gamerule.reducedDebugInfo.description": "Memba<PERSON><PERSON> konten dari layar awakutu.", "gamerule.sendCommandFeedback": "<PERSON><PERSON> umpan balik perintah", "gamerule.showDeathMessages": "<PERSON><PERSON><PERSON><PERSON> pesan kematian", "gamerule.snowAccumulationHeight": "Ketinggian tumpukan salju", "gamerule.snowAccumulationHeight.description": "Saat turun salju, lapisan salju terbentuk di tanah hingga sebanyak ini.", "gamerule.spawnChunkRadius": "Ra<PERSON> bin<PERSON><PERSON><PERSON>", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON> bing<PERSON>h yang tetap dimuat di sekitar posisi bangkit di Permukaan.", "gamerule.spawnRadius": "Radius lokasi pembang<PERSON>", "gamerule.spawnRadius.description": "Mengendalikan ukuran tempat di sekitar titik bangkit di mana pemain bisa muncul.", "gamerule.spectatorsGenerateChunks": "<PERSON>zinkan penonton untuk menghasilkan medan", "gamerule.tntExplodes": "Izinkan penyalaan dan peledakan TNT", "gamerule.tntExplosionDropDecay": "Beberapa balok tidak akan menjatuhkan jarahan ledakan TNT", "gamerule.tntExplosionDropDecay.description": "Beberapa jatuhan dari balok yang dihancurkan oleh ledakan yang disebabkan oleh TNT hilang dalam ledakan.", "gamerule.universalAnger": "<PERSON><PERSON>", "gamerule.universalAnger.description": "Makhluk netral yang terprovokasi menyerang pemain terdekat siapa saja, bukan hanya pemain yang memprovokasi mereka. Fitur ini bekerja lebih baik ketika forgiveDeadPlayers dinonaktifkan.", "gamerule.waterSourceConversion": "Air berubah menjadi sumber", "gamerule.waterSourceConversion.description": "Ketika air mengalir dikelilingi dua sisi oleh sumber air, ia berubah menjadi sumber.", "generator.custom": "Ubah Suaian", "generator.customized": "<PERSON><PERSON>", "generator.minecraft.amplified": "DIPERKUAT", "generator.minecraft.amplified.info": "Perhatian: <PERSON>ya untuk kesenangan! Memerlukan komputer kuat.", "generator.minecraft.debug_all_block_states": "Mode Awakutu", "generator.minecraft.flat": "Super Rata", "generator.minecraft.large_biomes": "Bioma Besar", "generator.minecraft.normal": "<PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Bioma <PERSON>", "generator.single_biome_caves": "<PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "<PERSON>gan men<PERSON>kan laporan ini, <PERSON><PERSON> bahwa informasi yang telah Anda berikan adalah akurat dan lengkap sesuai dengan pengetahuan <PERSON>a.", "gui.abuseReport.comments": "Komentar", "gui.abuseReport.describe": "<PERSON><PERSON> tahu perincian akan membantu kami membuat keputusan yang baik.", "gui.abuseReport.discard.content": "<PERSON><PERSON> <PERSON> per<PERSON>, <PERSON><PERSON> akan kehilangan laporan ini dan komentar Anda.\nAnda yakin ingin pergi?", "gui.abuseReport.discard.discard": "Tingg<PERSON><PERSON> dan <PERSON>", "gui.abuseReport.discard.draft": "Simpan sebagai Draf", "gui.abuseReport.discard.return": "Lanjutkan Menyunting", "gui.abuseReport.discard.title": "<PERSON><PERSON> laporan dan komentar?", "gui.abuseReport.draft.content": "<PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan menyunting laporan yang ada atau membuangnya dan membuat yang baru?", "gui.abuseReport.draft.discard": "<PERSON><PERSON>", "gui.abuseReport.draft.edit": "Lanjutkan Menyunting", "gui.abuseReport.draft.quittotitle.content": "<PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan menyuntingnya atau membuangnya?", "gui.abuseReport.draft.quittotitle.title": "Anda memiliki draf laporan obrolan yang akan hilang bila <PERSON>a keluar", "gui.abuseReport.draft.title": "Sunting draf laporan o<PERSON>?", "gui.abuseReport.error.title": "<PERSON><PERSON><PERSON> dalam mengirim laporan Anda", "gui.abuseReport.message": "Di mana Anda mengamati perilaku buruk tersebut?\nIni akan membantu kami dalam meneliti kasus <PERSON>a.", "gui.abuseReport.more_comments": "<PERSON><PERSON> jelaskan apa yang terjadi:", "gui.abuseReport.name.comment_box_label": "<PERSON>hon jelaskan mengapa Anda ingin melaporkan nama ini:", "gui.abuseReport.name.reporting": "<PERSON><PERSON> me<PERSON> \"%s\".", "gui.abuseReport.name.title": "Lapor<PERSON>", "gui.abuseReport.observed_what": "Mengapa Anda melaporkan ini?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkoba atau alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Seseorang mendorong orang lain untuk ikut serta dalam kegiatan terkait obat-obatan terlarang atau mendorong konsumsi alkohol pada usia bawah umur.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Eksploitasi atau pelecehan seksual terhadap anak", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Seseorang berbicara tentang atau mempromosikan perilaku tidak senonoh yang melibatkan anak-anak.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON> nama baik", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Seseorang merusak reputasi orang lain, seperti berbagi informasi palsu dengan tujuan untuk mengeksploitasi atau memperdaya orang lain.", "gui.abuseReport.reason.description": "Keterangan:", "gui.abuseReport.reason.false_reporting": "Pelapor<PERSON> Pa<PERSON>u", "gui.abuseReport.reason.generic": "<PERSON>a ingin me<PERSON>", "gui.abuseReport.reason.generic.description": "Saya terganggu oleh orang ini / orang ini telah melakukan sesuatu yang tidak saya suka.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON> atau perundungan", "gui.abuseReport.reason.harassment_or_bullying.description": "Seseorang memper<PERSON>n, men<PERSON><PERSON>, atau merundung Anda atau orang lain. Ini termasuk ketika seseorang berulang kali mencoba menghubungi Anda atau orang lain tanpa persetujuan atau mengunggah informasi pribadi tentang Anda atau orang lain tanpa persetujuan (\"doksing\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON> keb<PERSON>", "gui.abuseReport.reason.hate_speech.description": "Seseorang menyerang Anda atau pemain lain berdasarkan karakteristik identitas mereka, seperti agama, ras, atau seksualitas.", "gui.abuseReport.reason.imminent_harm": "Ancaman untuk menyakiti orang lain", "gui.abuseReport.reason.imminent_harm.description": "Seseorang mengancam untuk menyakiti Anda atau orang lain dalam kehidupan nyata.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "G<PERSON><PERSON> intim tanpa izin", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Seseorang berbicara tentang, berb<PERSON>, atau mempromosikan gambar pribadi dan intim.", "gui.abuseReport.reason.self_harm_or_suicide": "Melukai diri sendiri atau bunuh diri", "gui.abuseReport.reason.self_harm_or_suicide.description": "Seseorang mengancam untuk melukai diri mereka sendiri dalam kehidupan nyata, atau berbicara tentang melukai diri mereka sendiri dalam kehidupan nyata.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON>n seksual", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON><PERSON><PERSON> yang mengandung unsur aksi, organ, dan kek<PERSON>san seksual.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terorisme atau kekerasan ekstremis", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Seseorang berbicara tentang, memp<PERSON><PERSON><PERSON>n, atau mengancam melakukan tindakan terorisme atau kekerasan ekstremis karena alasan politik, agama, ideologis, atau lainnya.", "gui.abuseReport.reason.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.report_sent_msg": "<PERSON><PERSON> ber<PERSON><PERSON> men<PERSON>ma <PERSON>. <PERSON><PERSON> kasih!\n\n<PERSON> kami akan meninjaunya sesegera mungkin.", "gui.abuseReport.select_reason": "<PERSON><PERSON><PERSON>", "gui.abuseReport.send": "<PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON> memper<PERSON>kat komentar", "gui.abuseReport.send.error_message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengirim laporan Anda: '%s'", "gui.abuseReport.send.generic_error": "<PERSON><PERSON><PERSON>n kesalahan tak terduga saat mengirim laporan Anda.", "gui.abuseReport.send.http_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han HTTP tak terduga saat mengirim laporan Anda.", "gui.abuseReport.send.json_error": "Menemukan muatan cacat saat mengirim laporan Anda.", "gui.abuseReport.send.no_reason": "<PERSON><PERSON><PERSON> pilih kategori laporan", "gui.abuseReport.send.not_attested": "Harap baca teks di atas dan centang kotak untuk dapat mengirimkan laporan", "gui.abuseReport.send.service_unavailable": "Tidak dapat mencapai layanan Pelaporan Penyalahgunaan. Pastikan Anda tersambung ke internet dan coba lagi.", "gui.abuseReport.sending.title": "Mengirim laporan Anda...", "gui.abuseReport.sent.title": "Laporan terkirim", "gui.abuseReport.skin.title": "Laporkan Rup<PERSON>", "gui.abuseReport.title": "Lapor<PERSON>", "gui.abuseReport.type.chat": "<PERSON><PERSON>", "gui.abuseReport.type.name": "<PERSON><PERSON>", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON>", "gui.acknowledge": "Mengakui", "gui.advancements": "<PERSON><PERSON><PERSON><PERSON>", "gui.all": "<PERSON><PERSON><PERSON>", "gui.back": "Kembali", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON><PERSON> lebih lanjut di tautan berikut: %s", "gui.banned.description.permanent": "<PERSON><PERSON>n <PERSON>a dicekal secara permanen, yang berarti Anda tidak dapat bermain daring atau bergabung dengan Realms.", "gui.banned.description.reason": "Kami baru-baru ini menerima laporan atas perilaku buruk dari akun <PERSON>. Moderator kami sekarang telah meninjau kasus Anda dan mengidentifikasinya sebagai %s, yang bertentangan dengan Standar Komunitas Minecraft.", "gui.banned.description.reason_id": "Kode: %s", "gui.banned.description.reason_id_message": "Kode: %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON><PERSON> saat itu, <PERSON><PERSON> tidak dapat bermain daring atau bergabung dengan Realms.", "gui.banned.description.temporary.duration": "Akun Anda ditangguhkan sementara dan akan diaktifkan kembali dalam %s.", "gui.banned.description.unknownreason": "Kami baru-baru ini menerima laporan atas perilaku buruk dari akun <PERSON>. Moderator kami sekarang telah meninjau kasus Anda dan mengidentifikasi bahwa itu bertentangan dengan Standar Komunitas Minecraft.", "gui.banned.name.description": "<PERSON>a Anda saat ini—\"%s\"—telah melanggar Standar Komunitas kami. <PERSON>a dapat bermain sendiri, tetapi harus mengubah nama Anda untuk bermain daring.\n\nPelajari lebih lanjut atau kirimkan tinjauan kasus di tautan berikut: %s", "gui.banned.name.title": "<PERSON><PERSON> Diiz<PERSON> da<PERSON> <PERSON><PERSON>", "gui.banned.reason.defamation_impersonation_false_information": "<PERSON>iru atau berbagi informasi untuk mengeksploitasi atau memperdaya orang lain", "gui.banned.reason.drugs": "Rujukan terhadap obat-obatan terlarang", "gui.banned.reason.extreme_violence_or_gore": "<PERSON><PERSON><PERSON><PERSON> kek<PERSON>san be<PERSON>han atau pertumpahan darah di dunia nyata", "gui.banned.reason.false_reporting": "<PERSON><PERSON>an palsu atau tak akurat yang berlebih", "gui.banned.reason.fraud": "<PERSON><PERSON><PERSON><PERSON> atau penggunaan konten yang bersifat menipu", "gui.banned.reason.generic_violation": "Melanggar Standar Komunitas", "gui.banned.reason.harassment_or_bullying": "Bahasa kasar yang ditujukan pada seseorang secara berbahaya", "gui.banned.reason.hate_speech": "Ujaran keben<PERSON> atau diskrim<PERSON>i", "gui.banned.reason.hate_terrorism_notorious_figure": "Rujukan terhadap kelompo<PERSON> keben<PERSON>, organisasi teroris, atau orang jahat", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON><PERSON><PERSON> untuk membahayakan orang atau properti dunia nyata", "gui.banned.reason.nudity_or_pornography": "Menampilkan material porno atau cabul", "gui.banned.reason.sexually_inappropriate": "Topik atau konten seksual", "gui.banned.reason.spam_or_advertising": "Spam atau iklan", "gui.banned.skin.description": "Rupa Anda saat ini melanggar Standar Komunitas kami. <PERSON>a masih dapat bermain dengan rupa asali, atau memilih rupa baru.\n\n<PERSON>elajari lebih lanjut atau kirimkan tinjauan kasus di tautan berikut: %s", "gui.banned.skin.title": "Rupa Tidak Diizinkan", "gui.banned.title.permanent": "Akun dicekal permanen", "gui.banned.title.temporary": "<PERSON>kun ditangg<PERSON>kan sementara", "gui.cancel": "<PERSON><PERSON>", "gui.chatReport.comments": "Komentar", "gui.chatReport.describe": "<PERSON><PERSON> tahu perincian akan membantu kami membuat keputusan yang baik.", "gui.chatReport.discard.content": "<PERSON><PERSON> <PERSON> per<PERSON>, <PERSON><PERSON> akan kehilangan laporan ini dan komentar Anda.\nAnda yakin ingin pergi?", "gui.chatReport.discard.discard": "Tingg<PERSON><PERSON> dan <PERSON>", "gui.chatReport.discard.draft": "Simpan sebagai Draf", "gui.chatReport.discard.return": "Lanjutkan Menyunting", "gui.chatReport.discard.title": "<PERSON><PERSON> laporan dan komentar?", "gui.chatReport.draft.content": "<PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan menyunting laporan yang ada atau membuangnya dan membuat yang baru?", "gui.chatReport.draft.discard": "<PERSON><PERSON>", "gui.chatReport.draft.edit": "Lanjutkan Menyunting", "gui.chatReport.draft.quittotitle.content": "<PERSON><PERSON><PERSON><PERSON> Anda ingin melanjutkan menyuntingnya atau membuangnya?", "gui.chatReport.draft.quittotitle.title": "Anda memiliki draf laporan obrolan yang akan hilang bila <PERSON>a keluar", "gui.chatReport.draft.title": "Sunting draf laporan o<PERSON>?", "gui.chatReport.more_comments": "<PERSON><PERSON> jelaskan apa yang terjadi:", "gui.chatReport.observed_what": "Mengapa Anda melaporkan ini?", "gui.chatReport.read_info": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.report_sent_msg": "<PERSON><PERSON> ber<PERSON><PERSON> men<PERSON>ma <PERSON>. <PERSON><PERSON> kasih!\n\n<PERSON> kami akan meninjaunya sesegera mungkin.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON> Obrolan untuk Dilaporkan", "gui.chatReport.select_reason": "<PERSON><PERSON><PERSON>", "gui.chatReport.selected_chat": "%s Pesan Obrolan Dipilih untuk Dilaporkan", "gui.chatReport.send": "<PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON> memper<PERSON>kat komentar", "gui.chatReport.send.no_reason": "<PERSON><PERSON><PERSON> pilih kategori laporan", "gui.chatReport.send.no_reported_messages": "<PERSON><PERSON>an pilih setidaknya satu pesan obrolan untuk dilaporkan", "gui.chatReport.send.too_many_messages": "<PERSON><PERSON><PERSON>u banyak pesan dalam laporan", "gui.chatReport.title": "Laporkan Obrolan Pemain", "gui.chatSelection.context": "<PERSON>esan seputar pilihan ini akan disertakan untuk memberikan konteks tambahan", "gui.chatSelection.fold": "%s pesan disembunyikan", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s bergabung dalam obrolan", "gui.chatSelection.message.narrate": "%s berkata: %s di %s", "gui.chatSelection.selected": "%s/%s pesan dipilih", "gui.chatSelection.title": "<PERSON><PERSON><PERSON> Obrolan untuk Dilaporkan", "gui.continue": "Lanjutkan", "gui.copy_link_to_clipboard": "<PERSON><PERSON> ke Papa<PERSON>", "gui.days": "%s hari", "gui.done": "Se<PERSON><PERSON>", "gui.down": "<PERSON>wa<PERSON>", "gui.entity_tooltip.type": "Jenis: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s berkas ditolak", "gui.fileDropFailure.title": "<PERSON>l menambahkan berkas", "gui.hours": "%s jam", "gui.loadingMinecraft": "Memuat Minecraft", "gui.minutes": "%s menit", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Tombol %s", "gui.narrate.editBox": "Kotak sunting %s: %s", "gui.narrate.slider": "Penggeser %s", "gui.narrate.tab": "Tab %s", "gui.no": "Tidak", "gui.none": "Tidak ada", "gui.ok": "Baik", "gui.open_report_dir": "<PERSON><PERSON> Direk<PERSON>i <PERSON>", "gui.proceed": "Lanjutkan", "gui.recipebook.moreRecipes": "Klik Kanan untuk Selengkapnya", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Cari...", "gui.recipebook.toggleRecipes.all": "Menampilkan Semu<PERSON>", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "Dapat Dibuat", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "Da<PERSON><PERSON>", "gui.report_to_server": "Laporkan ke Peladen", "gui.socialInteractions.blocking_hint": "<PERSON><PERSON><PERSON> dengan akun <PERSON>", "gui.socialInteractions.empty_blocked": "Tidak ada pemain terblokir di obrolan", "gui.socialInteractions.empty_hidden": "Tidak ada pemain tersembunyi di obrolan", "gui.socialInteractions.hidden_in_chat": "Pesan obrolan dari %s akan disembunyikan", "gui.socialInteractions.hide": "Sembunyikan di Obrolan", "gui.socialInteractions.narration.hide": "Sembunyikan pesan dari %s", "gui.socialInteractions.narration.report": "Laporkan pemain %s", "gui.socialInteractions.narration.show": "Tampilkan pesan dari %s", "gui.socialInteractions.report": "Laporkan", "gui.socialInteractions.search_empty": "Tidak dapat menemukan pemain dengan nama tersebut", "gui.socialInteractions.search_hint": "Cari...", "gui.socialInteractions.server_label.multiple": "%s - %s pemain", "gui.socialInteractions.server_label.single": "%s - %s pemain", "gui.socialInteractions.show": "Tampilkan di Obrolan", "gui.socialInteractions.shown_in_chat": "Pesan obrolan dari %s akan ditampilkan", "gui.socialInteractions.status_blocked": "Diblokir", "gui.socialInteractions.status_blocked_offline": "Diblokir - Luring", "gui.socialInteractions.status_hidden": "Tersemb<PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Tersembunyi - Luring", "gui.socialInteractions.status_offline": "Luring", "gui.socialInteractions.tab_all": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "Diblokir", "gui.socialInteractions.tab_hidden": "Tersemb<PERSON><PERSON>", "gui.socialInteractions.title": "Interaksi Sosial", "gui.socialInteractions.tooltip.hide": "Sembunyikan pesan", "gui.socialInteractions.tooltip.report": "Lapor<PERSON> pemain", "gui.socialInteractions.tooltip.report.disabled": "Layanan pelaporan tidak tersedia", "gui.socialInteractions.tooltip.report.no_messages": "Tidak ada pesan yang dapat dilaporkan dari pemain %s", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON><PERSON><PERSON> ini tidak bisa dilap<PERSON>kan, karena pesan obrolannya tidak dapat diverifikasi di peladen ini", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON><PERSON><PERSON> pesan", "gui.stats": "Statistik", "gui.toMenu": "Ke<PERSON>li ke Daftar Peladen", "gui.toRealms": "Ke<PERSON>li ke Daftar Realm", "gui.toTitle": "<PERSON><PERSON><PERSON> ke Menu U<PERSON>a", "gui.toWorld": "Kembali ke Daftar Dunia", "gui.togglable_slot": "Klik untuk mematikan petak", "gui.up": "Atas", "gui.waitingForResponse.button.inactive": "Kembali (%sd)", "gui.waitingForResponse.title": "<PERSON><PERSON>gu <PERSON>", "gui.yes": "Ya", "hanging_sign.edit": "<PERSON><PERSON> Pesan <PERSON>", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Seruan", "instrument.minecraft.dream_goat_horn": "Mimpi", "instrument.minecraft.feel_goat_horn": "Rasa", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Damba", "inventory.binSlot": "Hancurkan Benda", "inventory.hotbarInfo": "Simpan bilah benda dengan %1$s+%2$s", "inventory.hotbarSaved": "<PERSON><PERSON>h benda tersimpan (pulihkan dengan %1$s+%2$s)", "item.canBreak": "Dapat menghancurkan:", "item.canPlace": "Dapat ditempatkan pada:", "item.canUse.unknown": "Tidak diketahui", "item.color": "Warna: %s", "item.components": "%s komponen", "item.disabled": "<PERSON><PERSON>", "item.durability": "Durabilitas: %s / %s", "item.dyed": "Diwarnai", "item.minecraft.acacia_boat": "<PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON>", "item.minecraft.allay_spawn_egg": "Telur Pewu<PERSON>aru", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.apple": "Apel", "item.minecraft.archer_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Telur Pewujudan <PERSON>adilo", "item.minecraft.armor_stand": "Cagak <PERSON>", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.arrow": "Panah", "item.minecraft.axolotl_bucket": "Ember Axolotl", "item.minecraft.axolotl_spawn_egg": "Telur Pewujudan Axolotl", "item.minecraft.baked_potato": "Kentang Pan<PERSON>", "item.minecraft.bamboo_chest_raft": "Ra<PERSON>t Ba<PERSON> den<PERSON>", "item.minecraft.bamboo_raft": "Rakit Bambu", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.beef": "Daging <PERSON>", "item.minecraft.beetroot": "Bit", "item.minecraft.beetroot_seeds": "<PERSON><PERSON>", "item.minecraft.beetroot_soup": "Sup Bit", "item.minecraft.birch_boat": "<PERSON><PERSON>", "item.minecraft.birch_chest_boat": "<PERSON><PERSON>", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON>g<PERSON><PERSON>", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "Tong<PERSON> Ke<PERSON>", "item.minecraft.blaze_spawn_egg": "Telur P<PERSON>", "item.minecraft.blue_bundle": "Kantong Biru", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "Telur Biru", "item.minecraft.blue_harness": "Rungkup Biru", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.bone": "Tulang", "item.minecraft.bone_meal": "Tepung Tulang", "item.minecraft.book": "Buku", "item.minecraft.bordure_indented_banner_pattern": "Pola Spanduk Tepi <PERSON>", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "Mangkuk", "item.minecraft.bread": "R<PERSON><PERSON>", "item.minecraft.breeze_rod": "Tong<PERSON>", "item.minecraft.breeze_spawn_egg": "Telur Pew<PERSON>", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.brewing_stand": "Alat Peramu", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brush": "Sikat", "item.minecraft.bucket": "Ember", "item.minecraft.bundle": "Kantong", "item.minecraft.bundle.empty": "Kosong", "item.minecraft.bundle.empty.description": "Dapat menyimpan tumpukan benda campur", "item.minecraft.bundle.full": "<PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.camel_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON>", "item.minecraft.cat_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Telur Pewujudan <PERSON>-Lab<PERSON>", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON>", "item.minecraft.chest_minecart": "<PERSON><PERSON>", "item.minecraft.chicken": "<PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Telur Pewuju<PERSON>", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "<PERSON><PERSON>", "item.minecraft.clock": "Jam", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.cocoa_beans": "<PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON>", "item.minecraft.cod_bucket": "Ember Kod", "item.minecraft.cod_spawn_egg": "Telur Pewujudan Kod", "item.minecraft.command_block_minecart": "<PERSON><PERSON> dengan Balok Perintah", "item.minecraft.compass": "Kompas", "item.minecraft.cooked_beef": "<PERSON><PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON>", "item.minecraft.cooked_mutton": "Daging Domba Matang", "item.minecraft.cooked_porkchop": "<PERSON><PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "Salem Matang", "item.minecraft.cookie": "Biskuit", "item.minecraft.copper_ingot": "Tembaga Batangan", "item.minecraft.cow_spawn_egg": "Telur <PERSON>", "item.minecraft.creaking_spawn_egg": "Telur Pewuju<PERSON>", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.new": "Pola Spanduk Wajah Creeper", "item.minecraft.creeper_spawn_egg": "Telur Pewujudan <PERSON>reeper", "item.minecraft.crossbow": "Busur Silang", "item.minecraft.crossbow.projectile": "Proyektil:", "item.minecraft.crossbow.projectile.multiple": "Proyektil: %s x %s", "item.minecraft.crossbow.projectile.single": "Proyektil: %s", "item.minecraft.cyan_bundle": "Ka<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.dark_oak_boat": "Perahu E<PERSON>", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON> E<PERSON>", "item.minecraft.debug_stick": "Batang <PERSON>", "item.minecraft.debug_stick.empty": "%s tidak memiliki properti", "item.minecraft.debug_stick.select": "dipilih \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" -> %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Ka<PERSON><PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "Sekop Berl<PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "Fragmen <PERSON>", "item.minecraft.disc_fragment_5.desc": "<PERSON><PERSON><PERSON> - 5", "item.minecraft.dolphin_spawn_egg": "Telur Pewujudan <PERSON>-Lumba", "item.minecraft.donkey_spawn_egg": "Telur Pew<PERSON>", "item.minecraft.dragon_breath": "Napas Naga", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Telur <PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.echo_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.egg": "Telur", "item.minecraft.elder_guardian_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.elytra": "Elitron", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "<PERSON><PERSON>", "item.minecraft.enchanted_golden_apple": "Apel Emas Terpesona", "item.minecraft.end_crystal": "Kristal End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON>", "item.minecraft.endermite_spawn_egg": "Telur Pewujudan Endermite", "item.minecraft.evoker_spawn_egg": "Telur Pew<PERSON>", "item.minecraft.experience_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.explorer_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.explorer_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.feather": "Bulu", "item.minecraft.fermented_spider_eye": "<PERSON>-<PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "Pola Spanduk Kotak-Kotak", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON>", "item.minecraft.firework_rocket": "Roket Ke<PERSON>ng A<PERSON>", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON><PERSON>:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Bintang Kembang Api", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "Biru", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Ubah Suaian", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "P<PERSON><PERSON> ke", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Jingga", "item.minecraft.firework_star.pink": "<PERSON><PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "Bentuk Tidak Dikenal", "item.minecraft.firework_star.shape.burst": "Meledak", "item.minecraft.firework_star.shape.creeper": "Berbentuk Creeper", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Berbentuk Bintang", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "Tongkat Pancing", "item.minecraft.flint": "<PERSON><PERSON>", "item.minecraft.flint_and_steel": "Pemantik Api", "item.minecraft.flow_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "Pola Spanduk Pilin", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Pola Spanduk Rupa Bunga", "item.minecraft.flower_pot": "Pot Bunga", "item.minecraft.fox_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.frog_spawn_egg": "Telur Pewuju<PERSON>", "item.minecraft.furnace_minecart": "<PERSON><PERSON> dengan <PERSON>", "item.minecraft.ghast_spawn_egg": "Tel<PERSON>", "item.minecraft.ghast_tear": "Air Mata Ghast", "item.minecraft.glass_bottle": "Botol <PERSON>", "item.minecraft.glistering_melon_slice": "Seman<PERSON><PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.globe_banner_pattern.desc": "Globe", "item.minecraft.globe_banner_pattern.new": "Pola Spanduk Globe", "item.minecraft.glow_berries": "Buni Sinar", "item.minecraft.glow_ink_sac": "Kantong Tinta Sinar", "item.minecraft.glow_item_frame": "Bingkai Benda Sinar", "item.minecraft.glow_squid_spawn_egg": "Telur Pewujudan <PERSON>-<PERSON>umi <PERSON>ar", "item.minecraft.glowstone_dust": "<PERSON><PERSON>", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Telur Pew<PERSON>", "item.minecraft.gold_ingot": "<PERSON>as <PERSON>", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "Apel Emas", "item.minecraft.golden_axe": "Kapak Emas", "item.minecraft.golden_boots": "Sepatu Emas", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "<PERSON><PERSON>", "item.minecraft.golden_helmet": "<PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON>g<PERSON><PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_leggings": "<PERSON><PERSON>", "item.minecraft.golden_pickaxe": "Beliung Emas", "item.minecraft.golden_shovel": "Sekop Emas", "item.minecraft.golden_sword": "Pedang Em<PERSON>", "item.minecraft.gray_bundle": "Kantong Kelabu", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "Rungku<PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "Rungku<PERSON>", "item.minecraft.guardian_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Pola Spanduk Prahara", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.heartbreak_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.heartbreak_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.hoglin_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON>", "item.minecraft.hopper_minecart": "<PERSON><PERSON> dengan Corong", "item.minecraft.horse_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.host_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.host_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.husk_spawn_egg": "Telur <PERSON>", "item.minecraft.ink_sac": "Kantong Tinta", "item.minecraft.iron_axe": "Kapak <PERSON>", "item.minecraft.iron_boots": "<PERSON>at<PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Telur Pewujudan Golem Besi", "item.minecraft.iron_helmet": "<PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON>g<PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_ingot": "<PERSON><PERSON>", "item.minecraft.iron_leggings": "<PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Beliung <PERSON>", "item.minecraft.iron_shovel": "Sekop Besi", "item.minecraft.iron_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.item_frame": "Bingkai Benda", "item.minecraft.jungle_boat": "<PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON>", "item.minecraft.knowledge_book": "<PERSON><PERSON>", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Ember La<PERSON>", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON>", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_leggings": "<PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Kantong Biru <PERSON>", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_harness": "Rungkup B<PERSON>", "item.minecraft.light_gray_bundle": "Kantong Kelabu <PERSON>", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "Rungkup <PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "Rungkup <PERSON>", "item.minecraft.lingering_potion": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON>k Bisa Dibuat", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON>festasi", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON>mbang Lamb<PERSON>", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water": "Botol Air Bertahan", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON>asan Air", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON>", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON>", "item.minecraft.lodestone_compass": "Kompas Batu Ma<PERSON>", "item.minecraft.mace": "Gada", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON> Ma<PERSON>", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON>g<PERSON><PERSON>", "item.minecraft.magma_cream": "Krim Magma", "item.minecraft.magma_cube_spawn_egg": "Telur Pewujudan Kubus Magma", "item.minecraft.mangrove_boat": "<PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON>", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "<PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON>", "item.minecraft.milk_bucket": "Ember Susu", "item.minecraft.minecart": "<PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "Pola Spanduk Rupa Logo", "item.minecraft.mooshroom_spawn_egg": "Telur Pewujudan Mooshroom", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.mule_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.mushroom_stew": "<PERSON><PERSON>", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> (Kotak Musik)", "item.minecraft.music_disc_far": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Daging Domba Mentah", "item.minecraft.name_tag": "Label Nama", "item.minecraft.nautilus_shell": "Cangkang Nautilus", "item.minecraft.nether_brick": "<PERSON><PERSON>", "item.minecraft.nether_star": "<PERSON><PERSON><PERSON>", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_axe": "Kapak Netherit", "item.minecraft.netherite_boots": "Sepatu Netherit", "item.minecraft.netherite_chestplate": "Baju <PERSON>herit", "item.minecraft.netherite_helmet": "<PERSON><PERSON>", "item.minecraft.netherite_hoe": "Cangkul Netherit", "item.minecraft.netherite_ingot": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_leggings": "<PERSON><PERSON>", "item.minecraft.netherite_pickaxe": "Beliung Netherit", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_shovel": "Sekop Netherit", "item.minecraft.netherite_sword": "Pedang Netherit", "item.minecraft.netherite_upgrade_smithing_template": "Templat Tempa", "item.minecraft.netherite_upgrade_smithing_template.new": "Peningkatan Netherit", "item.minecraft.oak_boat": "<PERSON><PERSON>", "item.minecraft.oak_chest_boat": "Perahu <PERSON>", "item.minecraft.ocelot_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON>", "item.minecraft.orange_bundle": "Kantong Jingga", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "Rungkup <PERSON>", "item.minecraft.painting": "<PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Perahu E<PERSON>", "item.minecraft.pale_oak_chest_boat": "Perahu Ek P<PERSON>", "item.minecraft.panda_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON>", "item.minecraft.pig_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.piglin_banner_pattern.desc": "Moncong", "item.minecraft.piglin_banner_pattern.new": "Pola Spanduk Moncong", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON>ewu<PERSON>", "item.minecraft.piglin_spawn_egg": "Telur <PERSON>ew<PERSON>", "item.minecraft.pillager_spawn_egg": "Tel<PERSON>", "item.minecraft.pink_bundle": "Kantong Merah Jambu", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pink_harness": "Rungkup Merah Jambu", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Polong Periuk Kera", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.poisonous_potato": "<PERSON><PERSON>", "item.minecraft.polar_bear_spawn_egg": "Telur Pewujudan Beruang Ku<PERSON>", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON>", "item.minecraft.porkchop": "Daging <PERSON>", "item.minecraft.potato": "<PERSON><PERSON>", "item.minecraft.potion": "<PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON>", "item.minecraft.potion.effect.empty": "Ramuan Tak Bisa Dibuat", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON>", "item.minecraft.potion.effect.infested": "<PERSON><PERSON>", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON>", "item.minecraft.potion.effect.luck": "<PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON>", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON>", "item.minecraft.potion.effect.thick": "<PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON>", "item.minecraft.potion.effect.water": "Botol Air", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON>", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON>", "item.minecraft.pottery_shard_archer": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON><PERSON>", "item.minecraft.powder_snow_bucket": "Ember Bubuk <PERSON>", "item.minecraft.prismarine_crystals": "<PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.pufferfish": "<PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "Ember Ikan Buntal", "item.minecraft.pufferfish_spawn_egg": "Telur Pewujudan Ikan <PERSON>l", "item.minecraft.pumpkin_pie": "<PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON>", "item.minecraft.purple_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>g<PERSON><PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "Daging <PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.raiser_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.ravager_spawn_egg": "Telur Pewuju<PERSON>", "item.minecraft.raw_copper": "Tembaga Mentah", "item.minecraft.raw_gold": "Emas Mentah", "item.minecraft.raw_iron": "<PERSON><PERSON>", "item.minecraft.recovery_compass": "<PERSON><PERSON><PERSON>", "item.minecraft.red_bundle": "Kantong Merah", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "Rungkup <PERSON>", "item.minecraft.redstone": "Serbuk Redstone", "item.minecraft.resin_brick": "<PERSON><PERSON>", "item.minecraft.resin_clump": "Gumpalan Resin", "item.minecraft.rib_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON>", "item.minecraft.saddle": "Pelana", "item.minecraft.salmon": "Salem Mentah", "item.minecraft.salmon_bucket": "Ember Salem", "item.minecraft.salmon_spawn_egg": "Telur Pewujudan Salem", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.sentry_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.shears": "Gunting", "item.minecraft.sheep_spawn_egg": "Telur Pew<PERSON>", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.purple": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.silverfish_spawn_egg": "Telur Pewujudan Gegat", "item.minecraft.skeleton_horse_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.skeleton_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON> Spanduk", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Pola Spanduk Wajah <PERSON>g<PERSON>ak", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.slime_ball": "<PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "Telur Pewuju<PERSON>", "item.minecraft.smithing_template": "Templat Tempa", "item.minecraft.smithing_template.applies_to": "Diterapkan pada:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Tambah batangan atau kristal", "item.minecraft.smithing_template.armor_trim.applies_to": "Zira<PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Tambah sepotong zirah", "item.minecraft.smithing_template.armor_trim.ingredients": "Batangan & Kristal", "item.minecraft.smithing_template.ingredients": "Bahan:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Tambah Netherit Batangan", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Tambah zirah, senjata, atau alat berlian", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.upgrade": "Tingkatan: ", "item.minecraft.sniffer_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.snout_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.snow_golem_spawn_egg": "Telur Pewujudan Golem Salju", "item.minecraft.snowball": "<PERSON><PERSON>", "item.minecraft.spectral_arrow": "<PERSON><PERSON>", "item.minecraft.spider_eye": "<PERSON>", "item.minecraft.spider_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON> Tak Bisa Dibuat", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON> Infestasi", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.luck": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.water": "Botol Air Ciprat", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON> Air", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.wind_charged": "<PERSON><PERSON><PERSON>", "item.minecraft.spruce_boat": "<PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON>", "item.minecraft.spyglass": "Teropong", "item.minecraft.squid_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON>ng <PERSON>", "item.minecraft.stone_shovel": "Sekop Batu", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.strider_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON>", "item.minecraft.sweet_berries": "B<PERSON> Man<PERSON>", "item.minecraft.tadpole_bucket": "<PERSON><PERSON>", "item.minecraft.tadpole_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.tipped_arrow": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.empty": "Panah Celupan Tak Bisa Dibuat", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.harming": "Panah <PERSON>", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Panah Infestasi", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.leaping": "Panah <PERSON>", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "Panah Regenerasi", "item.minecraft.tipped_arrow.effect.slow_falling": "Panah Tumbang Lambat", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "Panah <PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "Panah <PERSON>", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "Panah Guru Penyu", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "Panah Pernapasan Air", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON>", "item.minecraft.tnt_minecart": "Kereta Tambang dengan TNT", "item.minecraft.torchflower_seeds": "<PERSON><PERSON>", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON>", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON>g", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON>", "item.minecraft.trident": "Trisula", "item.minecraft.tropical_fish": "<PERSON><PERSON>", "item.minecraft.tropical_fish_bucket": "Ember Ikan Tropis", "item.minecraft.tropical_fish_spawn_egg": "Telur Pewujudan I<PERSON>", "item.minecraft.turtle_helmet": "Cangkang Penyu", "item.minecraft.turtle_scute": "<PERSON><PERSON><PERSON>", "item.minecraft.turtle_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.vex_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.vex_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.vex_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.villager_spawn_egg": "Telur Pewujudan <PERSON>du<PERSON>", "item.minecraft.vindicator_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.wandering_trader_spawn_egg": "Telur Pewujudan Pedagang <PERSON>", "item.minecraft.ward_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.warden_spawn_egg": "Telur <PERSON>", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON>", "item.minecraft.water_bucket": "Ember Air", "item.minecraft.wayfinder_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.wheat": "G<PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON>", "item.minecraft.white_bundle": "Kantong Putih", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "Rungkup <PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "Templat Tempa", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.wind_charge": "<PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wither_skeleton_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Telur Pewujudan <PERSON>", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "Sekop Kayu", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "<PERSON><PERSON> dan <PERSON>a", "item.minecraft.written_book": "<PERSON><PERSON>", "item.minecraft.yellow_bundle": "Kantong Kuning", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.yellow_harness": "Rungkup <PERSON>", "item.minecraft.zoglin_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.zombie_horse_spawn_egg": "Telur Pewu<PERSON>", "item.minecraft.zombie_spawn_egg": "Tel<PERSON> P<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Telur Pewujudan <PERSON>dudu<PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "Tel<PERSON> Pew<PERSON>", "item.modifiers.any": "<PERSON><PERSON><PERSON> dipasang:", "item.modifiers.armor": "<PERSON><PERSON><PERSON>:", "item.modifiers.body": "<PERSON><PERSON><PERSON> dipasang:", "item.modifiers.chest": "Saat di Badan:", "item.modifiers.feet": "Saat di Kaki:", "item.modifiers.hand": "<PERSON><PERSON><PERSON>g:", "item.modifiers.head": "Saat di Kepala:", "item.modifiers.legs": "Saat di Tungkai:", "item.modifiers.mainhand": "Saat di Tangan <PERSON>:", "item.modifiers.offhand": "Saat di Tang<PERSON>:", "item.modifiers.saddle": "Ke<PERSON>ka dipelana:", "item.nbt_tags": "NBT: %s etiket", "item.op_block_warning.line1": "Peringatan:", "item.op_block_warning.line2": "<PERSON><PERSON><PERSON><PERSON> benda ini dapat menjalankan perintah", "item.op_block_warning.line3": "<PERSON><PERSON> gunakan kecuali Anda mengetahui isinya secara pasti!", "item.unbreakable": "Tak Dapat Rusak", "itemGroup.buildingBlocks": "Balok <PERSON>n", "itemGroup.coloredBlocks": "Balok Aneka Warna", "itemGroup.combat": "Tempur", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.foodAndDrink": "Makanan & Minuman", "itemGroup.functional": "Balok Berkegunaan", "itemGroup.hotbar": "<PERSON><PERSON><PERSON>", "itemGroup.ingredients": "<PERSON><PERSON>", "itemGroup.inventory": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.natural": "Balok Alami", "itemGroup.op": "Utilitas Operator", "itemGroup.redstone": "Balok Redstone", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "Telur <PERSON>", "itemGroup.tools": "<PERSON><PERSON><PERSON>", "item_modifier.unknown": "Pengubah benda tidak dikenal: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON>:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "<PERSON><PERSON>:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON>", "jigsaw_block.levels": "Tingkat: %s", "jigsaw_block.name": "Nama:", "jigsaw_block.placement_priority": "Prioritas Penempatan:", "jigsaw_block.placement_priority.tooltip": "Ketika balok Keping ini menyambung ke sebuah potongan, ini adalah urutan di mana potongan itu diproses untuk sambungan dalam struktur yang lebih luas.\n\nPotongan akan diproses berdasarkan prioritas menurun dengan urutan penyisipan memecah prioritas setara.", "jigsaw_block.pool": "<PERSON><PERSON><PERSON><PERSON>:", "jigsaw_block.selection_priority": "Prioritas Pemilihan:", "jigsaw_block.selection_priority.tooltip": "<PERSON><PERSON><PERSON> potongan induk sedang diproses untuk sambungan, ini adalah urutan di mana balok Keping ini berusaha untuk menyambung ke potongan sasarannya.\n\nKepingan akan diproses berdasarkan prioritas menurun dengan urutan acak memecah prioritas setara.", "jigsaw_block.target": "<PERSON><PERSON>:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (Kotak Musik)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON>", "key.attack": "Serang/Hancurkan", "key.back": "Mundur", "key.categories.creative": "Mode Kreatif", "key.categories.gameplay": "Permainan", "key.categories.inventory": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.misc": "Serb<PERSON>", "key.categories.movement": "Gerakan", "key.categories.multiplayer": "<PERSON><PERSON><PERSON>", "key.categories.ui": "Antarmuka <PERSON>", "key.chat": "<PERSON><PERSON>", "key.command": "<PERSON><PERSON>", "key.drop": "Jatuhkan Bend<PERSON>", "key.forward": "<PERSON><PERSON>", "key.fullscreen": "Aktifkan <PERSON>", "key.hotbar.1": "Bilah Benda Petak 1", "key.hotbar.2": "Bilah Benda Petak 2", "key.hotbar.3": "Bilah Benda Petak 3", "key.hotbar.4": "Bilah Benda Petak 4", "key.hotbar.5": "Bilah Benda Petak 5", "key.hotbar.6": "Bilah Benda Petak 6", "key.hotbar.7": "Bilah Benda Petak 7", "key.hotbar.8": "Bilah Benda Petak 8", "key.hotbar.9": "Bilah Benda Petak 9", "key.inventory": "Buka/Tu<PERSON>", "key.jump": "Lompat", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "Panah Bawa<PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Tombol 0", "key.keyboard.keypad.1": "Tombol 1", "key.keyboard.keypad.2": "Tombol 2", "key.keyboard.keypad.3": "Tombol 3", "key.keyboard.keypad.4": "Tombol 4", "key.keyboard.keypad.5": "Tombol 5", "key.keyboard.keypad.6": "Tombol 6", "key.keyboard.keypad.7": "Tombol 7", "key.keyboard.keypad.8": "Tombol 8", "key.keyboard.keypad.9": "Tombol 9", "key.keyboard.keypad.add": "Tombol +", "key.keyboard.keypad.decimal": "Tombol Desimal", "key.keyboard.keypad.divide": "Tombol /", "key.keyboard.keypad.enter": "<PERSON>ol Enter", "key.keyboard.keypad.equal": "Tombol =", "key.keyboard.keypad.multiply": "Tombol *", "key.keyboard.keypad.subtract": "Tombol -", "key.keyboard.left": "<PERSON><PERSON>", "key.keyboard.left.alt": "Alt <PERSON>", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl Kiri", "key.keyboard.left.shift": "Shift Kiri", "key.keyboard.left.win": "<PERSON>", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "<PERSON><PERSON>", "key.keyboard.right.alt": "Alt <PERSON>", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl Kanan", "key.keyboard.right.shift": "Shift Kanan", "key.keyboard.right.win": "<PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Tak <PERSON>", "key.keyboard.up": "<PERSON><PERSON>", "key.keyboard.world.1": "Dunia 1", "key.keyboard.world.2": "Dunia 2", "key.left": "<PERSON><PERSON>", "key.loadToolbarActivator": "<PERSON><PERSON>", "key.mouse": "Tombol %1$s", "key.mouse.left": "<PERSON><PERSON>", "key.mouse.middle": "Tombol Tengah", "key.mouse.right": "<PERSON><PERSON>", "key.pickItem": "Ambil Balok", "key.playerlist": "<PERSON><PERSON><PERSON>", "key.quickActions": "<PERSON><PERSON><PERSON>", "key.right": "Belok <PERSON>", "key.saveToolbarActivator": "<PERSON><PERSON><PERSON>", "key.screenshot": "<PERSON><PERSON>", "key.smoothCamera": "Aktifkan Kamera Sinematik", "key.sneak": "Jongkok", "key.socialInteractions": "Layar Interaksi Sosial", "key.spectatorOutlines": "<PERSON><PERSON> (Penonton)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "<PERSON><PERSON>", "key.togglePerspective": "Ubah Sudut Pandang", "key.use": "Gunakan <PERSON>/Tempatkan Balok", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "Komunitas", "known_server_link.community_guidelines": "<PERSON><PERSON><PERSON>", "known_server_link.feedback": "<PERSON><PERSON>", "known_server_link.forums": "Forum", "known_server_link.news": "<PERSON><PERSON>", "known_server_link.report_bug": "Laporkan Kekutu Peladen", "known_server_link.status": "Status", "known_server_link.support": "Dukungan", "known_server_link.website": "Situs", "lanServer.otherPlayers": "<PERSON><PERSON><PERSON><PERSON> untuk <PERSON>", "lanServer.port": "Nomor Porta", "lanServer.port.invalid": "Bukan porta yang sah.\nKosongkan kotak sunting atau masukkan angka antara 1024 dan 65535.", "lanServer.port.invalid.new": "Bukan porta yang sah.\nKosongkan kotak sunting atau masukkan angka antara %s dan %s.", "lanServer.port.unavailable": "Porta tidak tersedia.\nKosongkan kotak sunting atau masukkan angka berbeda antara 1024 dan 65535.", "lanServer.port.unavailable.new": "Porta tidak tersedia.\nKosongkan kotak sunting atau masukkan angka berbeda antara %s dan %s.", "lanServer.scanning": "Mencari permainan di jaringan lokal Anda", "lanServer.start": "<PERSON><PERSON>", "lanServer.title": "Dunia LAN", "language.code": "ind_ID", "language.name": "Bahasa Indonesia", "language.region": "Indonesia", "lectern.take_book": "Ambil Buku", "loading.progress": "%s%%", "mco.account.privacy.info": "Baca lebih lanjut mengenai Mo<PERSON>g dan hukum privasi", "mco.account.privacy.info.button": "Baca lebih lanjut mengenai GDPR", "mco.account.privacy.information": "Mojang menerapkan tahapan tertentu untuk membantu melindungi anak-anak dan privasi mereka, termasuk mematuhi Undang-Undang Perlindungan Privasi Daring Anak-Anak (COPPA) dan Peraturan Perlindungan Data Umum (GDPR).\n\n<PERSON>a mungkin perlu memperoleh persetujuan dari orang tua sebelum mengakses akun Realms Anda.", "mco.account.privacyinfo": "Mojang melaksanakan tahapan tertentu dalam menjaga anak-anak dan privasi mereka termasuk mematuhi Undang-Undang Perlindungan Privasi Daring Anak-Anak (COPPA) dan <PERSON><PERSON><PERSON>lindungan Data Umum (GDPR).\n\nAnda mungkin memerlukan izin dari orang tua Anda sebelum mengakses akun Realms.\n\nBila Anda memiliki akun Minecraft lama (Anda log masuk dengan nama pengguna), <PERSON>a harus mengalihkan Akun tersebut ke akun Mojang agar dapat mengakses Realms.", "mco.account.update": "<PERSON><PERSON><PERSON> akun", "mco.activity.noactivity": "Tidak ada aktivitas selama %s hari", "mco.activity.title": "Kegiatan pemain", "mco.backup.button.download": "<PERSON><PERSON><PERSON>", "mco.backup.button.reset": "Atur Ulang Dunia", "mco.backup.button.restore": "Pulihkan", "mco.backup.button.upload": "<PERSON><PERSON><PERSON>", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "Cadangan (%s)", "mco.backup.entry.description": "Keterangan", "mco.backup.entry.enabledPack": "<PERSON><PERSON> yang <PERSON>", "mco.backup.entry.gameDifficulty": "Kesulitan Per<PERSON>inan", "mco.backup.entry.gameMode": "Mode Permainan", "mco.backup.entry.gameServerVersion": "Versi Peladen Per<PERSON>inan", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "<PERSON><PERSON>", "mco.backup.entry.undefined": "Perubahan Tidak Ditentukan", "mco.backup.entry.uploaded": "Diunggah", "mco.backup.entry.worldType": "<PERSON><PERSON>", "mco.backup.generate.world": "<PERSON><PERSON><PERSON> dunia", "mco.backup.info.title": "<PERSON>bahan dari <PERSON>", "mco.backup.narration": "Cadangan dari %s", "mco.backup.nobackups": "Realm ini tidak memiliki cadangan saat ini.", "mco.backup.restoring": "Memulihkan Realm Anda", "mco.backup.unknown": "TIDAK DIKETAHUI", "mco.brokenworld.download": "<PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "<PERSON><PERSON> atur ulang atau pilih dunia lain.", "mco.brokenworld.message.line2": "Anda juga dapat memilih mengunduh dunia untuk bermain sendiri.", "mco.brokenworld.minigame.title": "Permainan <PERSON> ini tidak lagi didukung", "mco.brokenworld.nonowner.error": "<PERSON><PERSON>, pemilik Realm sedang mengatur ulang dunia", "mco.brokenworld.nonowner.title": "Dunia ked<PERSON>", "mco.brokenworld.play": "Mainkan", "mco.brokenworld.reset": "<PERSON><PERSON>", "mco.brokenworld.title": "Dunia Anda saat ini tidak lagi didukung", "mco.client.incompatible.msg.line1": "<PERSON><PERSON><PERSON> tidak cocok dengan Realms.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON> gunakan versi terbaru Minecraft.", "mco.client.incompatible.msg.line3": "Realms tidak cocok dengan versi citra.", "mco.client.incompatible.title": "<PERSON><PERSON>n tidak cocok!", "mco.client.outdated.stable.version": "Versi klien Anda (%s) tidak cocok dengnan Realms.\n\nMohon gunakan versi terbaru Minecraft.", "mco.client.unsupported.snapshot.version": "Versi klien Anda (%s) tidak cocok dengnan Realms.\n\nRealms tidak tersedia untuk versi citra ini.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON>", "mco.compatibility.downgrade.description": "Dunia ini terakhir dimainkan di versi %s; <PERSON><PERSON> mengg<PERSON>kan versi %s.\nMenurunkan versi dunia dapat menyebabkan kerusakan dan kami tidak dapat menjamin bahwa itu dapat memuat atau bekerja.\n\nCadangan dunia Anda akan disimpan di bawah \"Cadangan Dunia\". <PERSON>lakan pulihkan dunia Anda bila diperlukan.", "mco.compatibility.incompatible.popup.title": "Versi tidak cocok", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON> yang Anda mencoba bergabung tidak cocok dengan versi Anda se<PERSON>.", "mco.compatibility.incompatible.series.popup.message": "Dunia ini terakhir dimainkan di versi %s; <PERSON><PERSON> men<PERSON>kan versi %s.\n\nSeri ini tidak cocok dengan sesama. Dunia baru diperlukan untuk bermain di versi ini.", "mco.compatibility.unverifiable.message": "Versi terakhir di mana dunia ini dimainkan tidak dapat diverifikasi. Bila dunia diubah menjadi versi yang lebih baru atau lebih lama, cadangan akan dibuat secara otomatis dan disimpan di bawah \"Cadangan Dunia\".", "mco.compatibility.unverifiable.title": "Kompatibilitas tidak dapat diverifikasi", "mco.compatibility.upgrade": "Tingkatkan", "mco.compatibility.upgrade.description": "Dunia ini terakhir dimainkan di versi %s; <PERSON><PERSON>kan versi %s.\n\nCadangan dunia Anda akan disimpan di bawah \"Cadangan Dunia\".\n\n<PERSON><PERSON><PERSON> pulihkan dunia Anda bila diperlukan.", "mco.compatibility.upgrade.friend.description": "Dunia ini terakhir dimainkan di versi %s; <PERSON><PERSON>kan versi %s.\n\nCadangan dunia akan disimpan di bawah \"Cadangan Dunia\".\n\nPemilik Realm dapat memulihkan dunia bila diperlukan.", "mco.compatibility.upgrade.title": "<PERSON><PERSON><PERSON><PERSON> Anda benar-benar ingin meningkatkan dunia Anda?", "mco.configure.current.minigame": "Saat ini", "mco.configure.world.activityfeed.disabled": "Umpan aktivitas pemain untuk sementara dimatikan", "mco.configure.world.backup": "Cadangan Dunia", "mco.configure.world.buttons.activity": "Kegiatan Pemain", "mco.configure.world.buttons.close": "Tutup Sementara Realm", "mco.configure.world.buttons.delete": "Hapus", "mco.configure.world.buttons.done": "Se<PERSON><PERSON>", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON> le<PERSON>h lan<PERSON>", "mco.configure.world.buttons.newworld": "<PERSON><PERSON>", "mco.configure.world.buttons.open": "<PERSON><PERSON> Ulang <PERSON>", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "<PERSON><PERSON><PERSON>...", "mco.configure.world.buttons.resetworld": "Atur Ulang Dunia", "mco.configure.world.buttons.save": "Simpan", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON>", "mco.configure.world.close.question.line1": "Anda dapat menutup Realm untuk sementara, agar tidak dapat dimainkan selama Anda membuat penyesuaian. Buka lagi saat Anda telah siap. \n\nIni tidak akan membatalkan berlangganan Realms Anda.", "mco.configure.world.close.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.configure.world.close.question.title": "Perlu membuat perubahan tanpa gangguan?", "mco.configure.world.closing": "Menutup Realm untuk sementara...", "mco.configure.world.commandBlocks": "Balok <PERSON>", "mco.configure.world.delete.button": "Hapus Realm", "mco.configure.world.delete.question.line1": "Realm Anda akan dihapus permanen", "mco.configure.world.delete.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.configure.world.description": "Keterangan Realm", "mco.configure.world.edit.slot.name": "<PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "Beberapa pengaturan dimatikan sebab dunia Anda adalah pet<PERSON>n", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON>apa pengaturan dimatikan sebab dunia Anda adalah pengalaman", "mco.configure.world.edit.subscreen.inspiration": "Beberapa pengaturan dimatikan sebab dunia Anda adalah dunia inspirasi", "mco.configure.world.forceGameMode": "Paksa Mode Permainan", "mco.configure.world.invite.narration": "Anda memiliki %s undangan baru", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "<PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Diundang (%s)", "mco.configure.world.invites.normal.tooltip": "Pengguna Biasa", "mco.configure.world.invites.ops.tooltip": "Operator", "mco.configure.world.invites.remove.tooltip": "Hapus", "mco.configure.world.leave.question.line1": "Bila meninggalkan Realm ini, Anda tak dapat melihatnya lagi kecuali diundang kembali", "mco.configure.world.leave.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.configure.world.loading": "Memuat Realm", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Saat ini: %s", "mco.configure.world.name": "Nama Realm", "mco.configure.world.opening": "Membuka Realm...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON> dengan nama tersebut tidak ada", "mco.configure.world.players.inviting": "Mengundang pemain...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "Tarung", "mco.configure.world.region_preference": "Preferensi Wilayah", "mco.configure.world.region_preference.title": "Pemilihan Preferensi Wilayah", "mco.configure.world.reset.question.line1": "Dunia Anda akan dihasilkan ulang dan dunia Anda saat ini akan hilang", "mco.configure.world.reset.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.configure.world.resourcepack.question": "Anda me<PERSON><PERSON>an paket sumber ubah suaian untuk bermain di <PERSON> ini.\n\nA<PERSON>kah Anda ingin mengunduhnya dan main?", "mco.configure.world.resourcepack.question.line1": "Realm ini memerlukan paket sumber ubah suaian", "mco.configure.world.resourcepack.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mengunduhnya dan bermain?", "mco.configure.world.restore.download.question.line1": "Dunia akan diunduh dan ditambahkan ke dunia bermain sendiri Anda.", "mco.configure.world.restore.download.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda ingin melanju<PERSON>kan?", "mco.configure.world.restore.question.line1": "Dunia Anda akan dipulihkan pada tanggal '%s' (%s)", "mco.configure.world.restore.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.configure.world.settings.expired": "Anda tidak dapat mengubah pengaturan Realm kedaluwarsa", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Dunia %s", "mco.configure.world.slot.empty": "Kosong", "mco.configure.world.slot.switch.question.line1": "Realm Anda akan dialihkan ke dunia lain", "mco.configure.world.slot.switch.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.configure.world.slot.tooltip": "Beralih ke dunia", "mco.configure.world.slot.tooltip.active": "Bergabung", "mco.configure.world.slot.tooltip.minigame": "Beralih ke Permainan Kecil", "mco.configure.world.spawnAnimals": "Bangkit<PERSON>", "mco.configure.world.spawnMonsters": "Bangkitkan Monster", "mco.configure.world.spawnNPCs": "Bangkitkan Tokoh Nonpemain", "mco.configure.world.spawnProtection": "Perlindungan Wilayah Bangkit", "mco.configure.world.spawn_toggle.message": "Me<PERSON>ikan pilihan ini akan menghapus seluruh entitas yang ada dari jenis itu", "mco.configure.world.spawn_toggle.message.npc": "<PERSON><PERSON>ikan pilihan ini akan menghapus seluruh entitas yang ada dari jenis itu, mi<PERSON><PERSON>", "mco.configure.world.spawn_toggle.title": "Peringatan!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "hari", "mco.configure.world.subscription.days": "hari", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> dari sehari", "mco.configure.world.subscription.month": "bulan", "mco.configure.world.subscription.months": "bulan", "mco.configure.world.subscription.recurring.daysleft": "<PERSON><PERSON><PERSON><PERSON> otomati<PERSON>", "mco.configure.world.subscription.recurring.info": "Perubahan yang dibuat pada berlangganan Realms Anda seperti mengulur waktu ataupun mematikan pembayaran berulang tidak akan terlihat hingga tanggal pembayaran berikutnya.", "mco.configure.world.subscription.remaining.days": "%1$s hari", "mco.configure.world.subscription.remaining.months": "%1$s bulan", "mco.configure.world.subscription.remaining.months.days": "%1$s bulan, %2$s hari", "mco.configure.world.subscription.start": "<PERSON><PERSON>", "mco.configure.world.subscription.tab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.unknown": "Tidak diketahui", "mco.configure.world.switch.slot": "Buat Dunia", "mco.configure.world.switch.slot.subtitle": "Dunia ini kosong, pilih bagaimana dunia Anda akan dibuat", "mco.configure.world.title": "Atur Realm:", "mco.configure.world.uninvite.player": "<PERSON><PERSON><PERSON><PERSON> Anda yakin untuk membatalkan undangan '%s'?", "mco.configure.world.uninvite.question": "<PERSON><PERSON><PERSON><PERSON> Anda yakin membatalkan undangan", "mco.configure.worlds.title": "Dunia", "mco.connect.authorizing": "<PERSON><PERSON><PERSON>...", "mco.connect.connecting": "Menghubungkan ke Realm...", "mco.connect.failed": "<PERSON><PERSON> ke Realm", "mco.connect.region": "Wilayah peladen: %s", "mco.connect.success": "Se<PERSON><PERSON>", "mco.create.world": "Buat", "mco.create.world.error": "Anda harus memasukkan nama!", "mco.create.world.failed": "Gagal membuat dunia!", "mco.create.world.reset.title": "Membuat dunia...", "mco.create.world.skip": "<PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON> pilihan, <PERSON>ukan dunia yang akan diletakkan pada Realm baru Anda", "mco.create.world.wait": "Sedang membuat Realm...", "mco.download.cancelled": "<PERSON><PERSON><PERSON><PERSON> ", "mco.download.confirmation.line1": "Dunia yang akan Anda unduh lebih besar dari %s", "mco.download.confirmation.line2": "Anda tidak akan dapat mengunggah dunia ini ke Realm lagi", "mco.download.confirmation.oversized": "Dunia yang akan Anda unduh lebih besar dari %s\n\nAnda tidak akan bisa mengunggah dunia ini ke Realm Anda lagi", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON>", "mco.download.downloading": "<PERSON><PERSON><PERSON><PERSON>", "mco.download.extracting": "Membongkar", "mco.download.failed": "<PERSON><PERSON><PERSON><PERSON> gagal", "mco.download.percent": "%s %%", "mco.download.preparing": "Mempersia<PERSON><PERSON>", "mco.download.resourcePack.fail": "<PERSON>l mengunduh paket sumber!", "mco.download.speed": "(%s/d)", "mco.download.speed.narration": "%s per detik", "mco.download.title": "<PERSON><PERSON><PERSON><PERSON> Te<PERSON>aru", "mco.error.invalid.session.message": "<PERSON><PERSON> ulang Minecraft", "mco.error.invalid.session.title": "Sesi Tidak Sah", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "Ketentuan Layanan tidak diterima", "mco.errorMessage.6003": "Batas pengunduhan di<PERSON>", "mco.errorMessage.6004": "Batas pengunggahan dicapai", "mco.errorMessage.6005": "Dunia terkunci", "mco.errorMessage.6006": "Dunia ked<PERSON>", "mco.errorMessage.6007": "Pengguna ada di terlalu banyak Realm", "mco.errorMessage.6008": "Nama Realm tidak sah", "mco.errorMessage.6009": "Keterangan Realm tidak sah", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON><PERSON>, silakan coba lagi nanti.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>: ", "mco.errorMessage.initialize.failed": "<PERSON><PERSON>", "mco.errorMessage.noDetails": "Tidak ada perincian kesalahan yang diberikan", "mco.errorMessage.realmsService": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> (%s):", "mco.errorMessage.realmsService.configurationError": "Kesalahan tak diharapkan terjadi saat mengubah pilihan dunia", "mco.errorMessage.realmsService.connectivity": "Tidak dapat terhubung ke Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Tidak dapat memeriksa versi yang cocok, respons yang didapat: %s", "mco.errorMessage.retry": "<PERSON><PERSON> ul<PERSON>i", "mco.errorMessage.serviceBusy": "Realms saat ini sedang sibuk.\nMohon coba menghubungkan ke Realm Anda dalam beberapa menit.", "mco.gui.button": "Tombol", "mco.gui.ok": "Baik", "mco.info": "Info!", "mco.invited.player.narration": "Mengundang pemain %s", "mco.invites.button.accept": "Terima", "mco.invites.button.reject": "<PERSON><PERSON>", "mco.invites.nopending": "Tidak ada undangan tertunda!", "mco.invites.pending": "Undangan baru!", "mco.invites.title": "Undangan Tertunda", "mco.minigame.world.changeButton": "<PERSON><PERSON><PERSON>", "mco.minigame.world.info.line1": "Ini akan mengganti dunia Anda dengan permainan kecil untuk sementara!", "mco.minigame.world.info.line2": "<PERSON>a dapat kembali ke dunia asli tanpa kehilangan apa pun nanti.", "mco.minigame.world.noSelection": "<PERSON><PERSON><PERSON> membuat pilihan", "mco.minigame.world.restore": "Mengakh<PERSON>...", "mco.minigame.world.restore.question.line1": "Permainan kecil akan berakhir dan <PERSON> Anda akan dipulihkan.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "mco.minigame.world.selected": "Permaina<PERSON> te<PERSON>:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON>...", "mco.minigame.world.startButton": "Beral<PERSON>", "mco.minigame.world.starting.screen.title": "<PERSON><PERSON><PERSON>...", "mco.minigame.world.stopButton": "<PERSON><PERSON><PERSON>", "mco.minigame.world.switch.new": "<PERSON><PERSON><PERSON> lain?", "mco.minigame.world.switch.title": "<PERSON><PERSON>", "mco.minigame.world.title": "Alih Realm ke Permainan Ke<PERSON>l", "mco.news": "Kabar Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Sekarang", "mco.notification.transferSubscription.message": "Berlangganan Realms Java akan dipindahkan ke Microsoft Store. Jangan biarkan berlangganan Anda kedaluwarsa! Transfer sekarang dan dapatkan 30 hari Realms secara gratis. Kunjungi Profil di minecraft.net untuk mentransfer berlangganan Anda.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON>", "mco.notification.visitUrl.message.default": "<PERSON><PERSON> kun<PERSON>gi tautan di bawah", "mco.onlinePlayers": "<PERSON><PERSON><PERSON>", "mco.play.button.realm.closed": "Realm ditutup", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Petualangan", "mco.reset.world.experience": "Pengalaman", "mco.reset.world.generate": "<PERSON><PERSON>", "mco.reset.world.inspiration": "Inspirasi", "mco.reset.world.resetting.screen.title": "<PERSON><PERSON>ur ulang dunia...", "mco.reset.world.seed": "<PERSON><PERSON> (Opsional)", "mco.reset.world.template": "Pola Acu Dunia", "mco.reset.world.title": "Atur Ulang Dunia", "mco.reset.world.upload": "<PERSON><PERSON><PERSON> dunia", "mco.reset.world.warning": "Ini akan menggantikan Realm Anda saat ini", "mco.selectServer.buy": "Beli Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Realm Tak Aktif", "mco.selectServer.closeserver": "Tutup Realm", "mco.selectServer.configure": "Atur Realm", "mco.selectServer.configureRealm": "Konfigurasi Realm", "mco.selectServer.create": "Buat Realm", "mco.selectServer.create.subtitle": "<PERSON><PERSON>h dunia mana yang dipakai untuk Realm baru Anda", "mco.selectServer.expired": "Realm kedaluwarsa", "mco.selectServer.expiredList": "Realm Anda telah kedal<PERSON>", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "<PERSON><PERSON>a telah be<PERSON>", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON><PERSON><PERSON> dalam satu hari", "mco.selectServer.expires.days": "Ke<PERSON><PERSON><PERSON><PERSON> dalam %s hari", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>", "mco.selectServer.leave": "Tinggalkan Realm", "mco.selectServer.loading": "Memuat Daftar Realm", "mco.selectServer.mapOnlySupportedForVersion": "Peta ini tidak didukung dalam %s", "mco.selectServer.minigame": "Permainan <PERSON>:", "mco.selectServer.minigameName": "Permainan Kecil: %s", "mco.selectServer.minigameNotSupportedInVersion": "Tidak dapat bermain <PERSON> %s", "mco.selectServer.noRealms": "Anda se<PERSON>inya tidak memiliki Realm. Tambahkan Realm untuk bermain bersama dengan teman-teman Anda.", "mco.selectServer.note": "Catatan:", "mco.selectServer.open": "Realm terbuka", "mco.selectServer.openserver": "Buka Realm", "mco.selectServer.play": "Main", "mco.selectServer.popup": "Realms adalah cara aman dan sederhana untuk menikmati dunia Minecraft daring dengan hingga sepuluh teman bersamaan. Realms mendukung banyak permainan kecil dan dunia ubah suaian! Hanya pemilik Realm yang harus membayar.", "mco.selectServer.purchase": "Tambah Realm", "mco.selectServer.trial": "<PERSON>ba gratis!", "mco.selectServer.uninitialized": "<PERSON>lik untuk memulai Realm baru Anda!", "mco.snapshot.createSnapshotPopup.text": "Anda akan membuat Realm Citra gratis yang akan dipasangkan dengan berlangganan Realms berbayar Anda. Realm Citra baru ini akan dapat diakses selama berlangganan berbayar aktif. Realm berbayar Anda tidak akan terpengaruh.", "mco.snapshot.createSnapshotPopup.title": "Buat Realm Citra?", "mco.snapshot.creating": "Membuat Realm Citra...", "mco.snapshot.description": "Dipasangkan dengan \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Anda perlu berada pada versi %s untuk bergabung dengan Realm ini", "mco.snapshot.friendsRealm.upgrade": "%s perlu meningkatkan Realmnya sebelum Anda dapat bermain dari versi ini", "mco.snapshot.paired": "Realm Citra ini dipasangkan dengan \"%s\"", "mco.snapshot.parent.tooltip": "Gunakan rilisan terbaru Minecraft untuk bermain di Realm ini", "mco.snapshot.start": "<PERSON><PERSON> Citra gratis", "mco.snapshot.subscription.info": "Ini adalah Realm Citra yang dipasangkan dengan berlangganan Realm Anda '%s'. Ini akan tetap aktif selama Realm yang dipasangkan tersebut aktif.", "mco.snapshot.tooltip": "Gunakan Realms Citra untuk melihat sekilas versi mendatang dari <PERSON>craft, yang mungkin mencakup fitur-fitur baru dan perubahan lainnya.\n\nAnda dapat menemukan Realms biasa Anda dalam versi rilisan permainan.", "mco.snapshotRealmsPopup.message": "Realms sekarang mulai tersedia dalam Citra mulai dari Citra 23w41a. Setiap berlangganan Realms hadir dengan sebuah Realm Citra gratis yang terpisah dari Realm Java biasa Anda!", "mco.snapshotRealmsPopup.title": "Realms sekarang tersedia di Citra", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.button.publisher": "Penerbit", "mco.template.button.select": "<PERSON><PERSON><PERSON>", "mco.template.button.trailer": "Cuplikan", "mco.template.default.name": "Pola Acu Dunia", "mco.template.info.tooltip": "Situs Penerbit", "mco.template.name": "Pol<PERSON> Acu", "mco.template.select.failure": "<PERSON>mi tidak dapat mengambil daftar isi dari kategori ini.\nMohon periksa kembali sambungan internet Anda, atau coba lagi nanti.", "mco.template.select.narrate.authors": "Pengarang: %s", "mco.template.select.narrate.version": "versi %s", "mco.template.select.none": "Ups, tampaknya kategori ini kosong.\n<PERSON>hon periksa kembali nanti untuk mendapatkan konten baru, atau bila <PERSON>a adalah pencipta,\n%s.", "mco.template.select.none.linkTitle": "mungkin Anda dapat mengunggah sesuatu sendiri", "mco.template.title": "Pola Acu Dunia", "mco.template.title.minigame": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.trailer.tooltip": "Cuplikan peta", "mco.terms.buttons.agree": "<PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "Tidak setuju", "mco.terms.sentence.1": "<PERSON><PERSON>", "mco.terms.sentence.2": "Minecraft Realms", "mco.terms.title": "Ketentuan Layanan Realms", "mco.time.daysAgo": "%1$s hari yang lalu", "mco.time.hoursAgo": "%1$s jam yang lalu", "mco.time.minutesAgo": "%1$s menit yang lalu", "mco.time.now": "<PERSON><PERSON><PERSON>", "mco.time.secondsAgo": "%1$s detik yang lalu", "mco.trial.message.line1": "Ingin mendapatkan Realm Anda sendiri?", "mco.trial.message.line2": "Klik di sini untuk infomasi lebih lanjut!", "mco.upload.button.name": "<PERSON><PERSON><PERSON>", "mco.upload.cancelled": "<PERSON><PERSON><PERSON><PERSON> dibatalkan", "mco.upload.close.failure": "Tidak dapat menutup <PERSON>, silakan coba lagi nanti", "mco.upload.done": "Pengungg<PERSON> se<PERSON>ai", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Pengunggahan gagal! (%s)", "mco.upload.failed.too_big.description": "Dunia yang dipilih terlalu besar. Ukuran maksimum yang diperbolehkan adalah %s.", "mco.upload.failed.too_big.title": "Dunia terlalu besar", "mco.upload.hardcore": "Tidak dapat mengunggah Dunia Menantang!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Mempersiapkan dunia Anda", "mco.upload.select.world.none": "Dunia Bermain Sendiri tidak ditemukan!", "mco.upload.select.world.subtitle": "<PERSON><PERSON><PERSON> pilih dunia Bermain <PERSON> untuk diunggah", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON>", "mco.upload.size.failure.line1": "'%s' terlalu besar!", "mco.upload.size.failure.line2": "Ukurannya saat ini %s. <PERSON><PERSON><PERSON><PERSON> yang diizinkan adalah %s.", "mco.upload.uploading": "Mengunggah '%s'", "mco.upload.verifying": "Memverifikasi dunia And<PERSON>", "mco.version": "Versi: %s", "mco.warning": "Peringatan!", "mco.worldSlot.minigame": "<PERSON><PERSON><PERSON><PERSON>", "menu.custom_options": "<PERSON><PERSON><PERSON> Suaian...", "menu.custom_options.title": "<PERSON><PERSON><PERSON>", "menu.custom_options.tooltip": "Perhatian: <PERSON><PERSON><PERSON> ubah suaian disediakan oleh peladen dan/atau konten pihak ketiga.\n<PERSON><PERSON> dengan hati-hati!", "menu.custom_screen_info.button_narration": "<PERSON>i adalah layar ubah suaian. Pelajari lebih lanjut.", "menu.custom_screen_info.contents": "Konten dari layar ini dikendalikan oleh peladen dan peta pihak ketiga yang tidak dimiliki, dioperasikan, atau diawasi oleh Mojang Studios atau Microsoft.\n\nTangani dengan hati-hati! Selalu berhati-hati ketika menelusuri tautan dan jangan pernah memberi informasi pribadi Anda, termasuk perincian log masuk.\n\nBila layar ini menghalang Anda dari bermain, Anda dapat memutuskan sambungan dari peladen saat ini menggunakan tombol di bawah.", "menu.custom_screen_info.disconnect": "<PERSON><PERSON> ubah su<PERSON>n di<PERSON>", "menu.custom_screen_info.title": "Catatan mengenai layar ubah suaian", "menu.custom_screen_info.tooltip": "Ini adalah layar ubah suaian. Klik di sini untuk mempelajari lebih lanjut.", "menu.disconnect": "<PERSON><PERSON>", "menu.feedback": "Umpan Balik...", "menu.feedback.title": "<PERSON><PERSON>", "menu.game": "<PERSON><PERSON>", "menu.modded": " (Dimodifikasi)", "menu.multiplayer": "<PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "<PERSON><PERSON><PERSON>...", "menu.paused": "<PERSON><PERSON><PERSON><PERSON>", "menu.playdemo": "<PERSON><PERSON> Du<PERSON> De<PERSON>", "menu.playerReporting": "<PERSON>ela<PERSON><PERSON>", "menu.preparingSpawn": "Menyiapkan titik bangkit: %s%%", "menu.quick_actions": "<PERSON><PERSON><PERSON>...", "menu.quick_actions.title": "<PERSON><PERSON><PERSON>", "menu.quit": "<PERSON><PERSON><PERSON>", "menu.reportBugs": "Laporkan Kekutu", "menu.resetdemo": "Atur Ulang Dunia Demo", "menu.returnToGame": "Kembali ke Permainan", "menu.returnToMenu": "Simpan dan <PERSON> ke Menu", "menu.savingChunks": "<PERSON><PERSON><PERSON><PERSON>", "menu.savingLevel": "Menyimpan dunia", "menu.sendFeedback": "<PERSON><PERSON>", "menu.server_links": "<PERSON><PERSON>...", "menu.server_links.title": "<PERSON><PERSON>", "menu.shareToLan": "Buka ke LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON>", "menu.working": "Sedang bekerja...", "merchant.deprecated": "<PERSON>duduk isi kembali persediaan hingga dua kali sehari.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.4": "<PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Dagangan", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Tekan %1$s untuk Turun", "multiplayer.applyingPack": "Menerapkan paket sumber", "multiplayer.confirm_command.parse_errors": "Anda sedang mencoba menjalankan perintah yang tidak dikenal atau tidak sah. Anda yakin?\nPerintah: %s", "multiplayer.confirm_command.permissions_required": "Anda sedang mencoba menjalankan perintah yang memerlukan izin yang lebih tinggi.\nIni mungkin berdampak buruk pada permainan Anda.\nAnda yakin?\nPerintah: %s", "multiplayer.confirm_command.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.authservers_down": "<PERSON><PERSON>den otentikasi mati. <PERSON><PERSON> ma<PERSON>, silakan coba lagi nanti!", "multiplayer.disconnect.bad_chat_index": "Ditemukan pesan obrolan terlewat atau terurut ulang dari peladen", "multiplayer.disconnect.banned": "Anda dicekal dari peladen ini", "multiplayer.disconnect.banned.expiration": "\nPencekalan Anda akan dicabut pada %s", "multiplayer.disconnect.banned.reason": "Anda dicekal dari peladen ini.\nAlasan: %s", "multiplayer.disconnect.banned_ip.expiration": "\nPencekalan Anda akan di<PERSON>pus pada %s", "multiplayer.disconnect.banned_ip.reason": "Alamat IP Anda dicekal dari peladen ini.\nAlasan: %s", "multiplayer.disconnect.chat_validation_failed": "Kegagalan validasi pesan obrolan", "multiplayer.disconnect.duplicate_login": "Anda log masuk dari lokasi lain", "multiplayer.disconnect.expired_public_key": "Kunci publik profil kedaluwarsa. <PERSON>iksa apakah waktu sistem Anda sudah disinkronkan, dan coba jalankan ulang permainan Anda.", "multiplayer.disconnect.flying": "Terbang tidak diaktifkan di peladen ini", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.idling": "Anda menganggur terlalu lama!", "multiplayer.disconnect.illegal_characters": "Karakter tak sah di obrolan", "multiplayer.disconnect.incompatible": "Klien tidak cocok! Harap gunakan %s", "multiplayer.disconnect.invalid_entity_attacked": "Mencoba menyerang entitas tak sah", "multiplayer.disconnect.invalid_packet": "Peladen mengirimkan paket yang tidak sah", "multiplayer.disconnect.invalid_player_data": "Data pemain tidak sah", "multiplayer.disconnect.invalid_player_movement": "<PERSON><PERSON><PERSON> paket pergerakan tak sah pemain", "multiplayer.disconnect.invalid_public_key_signature": "Tanda tangan tidak sah untuk kunci publik profil.\nCoba jalankan ulang permainan Anda.", "multiplayer.disconnect.invalid_public_key_signature.new": "Tanda tangan tidak sah untuk kunci publik profil.\nCoba jalankan ulang permainan Anda.", "multiplayer.disconnect.invalid_vehicle_movement": "<PERSON><PERSON><PERSON> paket pergerakan tak sah kendaraan", "multiplayer.disconnect.ip_banned": "Alamat IP Anda dicekal dari peladen ini", "multiplayer.disconnect.kicked": "Dikeluarkan oleh operator", "multiplayer.disconnect.missing_tags": "Kumpulan etiket tidak lengkap diterima dari peladen.\nMohon hubungi operator peladen.", "multiplayer.disconnect.name_taken": "<PERSON><PERSON> tersebut telah diambil", "multiplayer.disconnect.not_whitelisted": "Anda tidak dalam daftar putih peladen ini!", "multiplayer.disconnect.out_of_order_chat": "<PERSON>et obrolan rusak diterima. Apakah waktu sistem Anda diubah?", "multiplayer.disconnect.outdated_client": "Klien tidak cocok! Harap gunakan %s", "multiplayer.disconnect.outdated_server": "Klien tidak cocok! Harap gunakan %s", "multiplayer.disconnect.server_full": "P<PERSON>den penuh!", "multiplayer.disconnect.server_shutdown": "Peladen tertutup", "multiplayer.disconnect.slow_login": "Waktu log masuk terlalu lama", "multiplayer.disconnect.too_many_pending_chats": "Te<PERSON><PERSON>u banyak pesan yang tidak diakui", "multiplayer.disconnect.transfers_disabled": "Peladen tidak menerima transfer", "multiplayer.disconnect.unexpected_query_response": "Data ubah suaian tak diharapkan oleh klien", "multiplayer.disconnect.unsigned_chat": "<PERSON><PERSON><PERSON> paket obrolan dengan tanda tangan yang hilang atau tidak sah.", "multiplayer.disconnect.unverified_username": "Gagal verifikasi <PERSON>!", "multiplayer.downloadingStats": "Mengambil statistik...", "multiplayer.downloadingTerrain": "Memuat medan...", "multiplayer.lan.server_found": "Peladen baru ditemukan: %s", "multiplayer.message_not_delivered": "Tidak dapat mengirim pesan, simak log peladen: %s", "multiplayer.player.joined": "%s bergabung dalam permainan", "multiplayer.player.joined.renamed": "%s (sebelumnya dikenal sebagai %s) bergabung dalam permainan", "multiplayer.player.left": "%s meninggalkan permainan", "multiplayer.player.list.hp": "%stk", "multiplayer.player.list.narration": "P<PERSON>in daring: %s", "multiplayer.requiredTexturePrompt.disconnect": "P<PERSON>den memerlukan paket sumber ubah suaian", "multiplayer.requiredTexturePrompt.line1": "Peladen ini mengharuskan penggunaan paket sumber ubah suaian.", "multiplayer.requiredTexturePrompt.line2": "Menolak paket sumber ubah suaian akan memutuskan Anda dari peladen ini.", "multiplayer.socialInteractions.not_available": "Interaksi Sosial hanya tersedia di dunia <PERSON><PERSON>", "multiplayer.status.and_more": "... dan %s lainnya ...", "multiplayer.status.cancelled": "Di<PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Tidak dapat menghubungkan ke peladen", "multiplayer.status.cannot_resolve": "Tidak dapat menemukan nama hos", "multiplayer.status.finished": "Se<PERSON><PERSON>", "multiplayer.status.incompatible": "Versi tidak cocok!", "multiplayer.status.motd.narration": "Pesan hari ini: %s", "multiplayer.status.no_connection": "(tiada sambungan)", "multiplayer.status.old": "<PERSON><PERSON>", "multiplayer.status.online": "Daring", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milidetik", "multiplayer.status.pinging": "Memeriksa...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s dari %s pemain daring", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "Permintaan status telah ditangani", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Menerima status tak diminta", "multiplayer.status.version.narration": "Versi peladen: %s", "multiplayer.stopSleeping": "<PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "Paket sumber peladen tidak dapat diterapkan", "multiplayer.texturePrompt.failure.line2": "Fungsionalitas apa pun yang memerlukan sumber daya ubah suaian mungkin tidak dapat bekerja sesuai harapan", "multiplayer.texturePrompt.line1": "Peladen ini menyarankan penggunaan paket sumber khusus.", "multiplayer.texturePrompt.line2": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mengunduh dan memasang secara otomatis?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nPesan dari peladen:\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON>", "multiplayer.unsecureserver.toast": "Pesan yang dikirim di peladen ini mungkin telah diubah dan mungkin tidak sesuai dengan pesan asli", "multiplayer.unsecureserver.toast.title": "Pesan obrolan tidak dapat diverifikasi", "multiplayerWarning.check": "<PERSON>an tampilkan layar ini lagi", "multiplayerWarning.header": "Perhatian: <PERSON><PERSON><PERSON><PERSON>", "multiplayerWarning.message": "Perhatian: <PERSON><PERSON><PERSON><PERSON> daring dita<PERSON>an oleh peladen pihak ketiga yang tidak dimiliki, dioperasikan, atau diawasi oleh Mojang Studios atau Microsoft. <PERSON><PERSON>a permainan daring, <PERSON><PERSON> mungkin terpapar pesan obrolan yang tidak termoderasi atau jenis lain konten buatan pengguna yang mungkin tidak cocok untuk semua orang.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Tombol: %s", "narration.button.usage.focused": "Tekan Enter untuk mengaktifkan", "narration.button.usage.hovered": "Klik kiri untuk mengaktifkan", "narration.checkbox": "Kotak centang: %s", "narration.checkbox.usage.focused": "Tekan Enter untuk beralih", "narration.checkbox.usage.hovered": "Klik kiri untuk beralih", "narration.component_list.usage": "<PERSON><PERSON> untuk menavigasi ke elemen selanjutnya", "narration.cycle_button.usage.focused": "Tekan Enter untuk beralih ke %s", "narration.cycle_button.usage.hovered": "Klik kiri untuk beralih ke %s", "narration.edit_box": "Kotak sunting: %s", "narration.item": "Benda: %s", "narration.recipe": "Resep untuk %s", "narration.recipe.usage": "Klik kiri untuk memilih", "narration.recipe.usage.more": "<PERSON>lik kanan untuk memperlihatkan lebih banyak resep", "narration.selection.usage": "<PERSON><PERSON> tombol atas dan bawah untuk berpindah ke entri lain", "narration.slider.usage.focused": "Tekan tombol kiri atau kanan kibor untuk mengganti nilai", "narration.slider.usage.hovered": "<PERSON><PERSON> penggeser untuk mengganti nilai", "narration.suggestion": "Memilih saran %s dari %s: %s", "narration.suggestion.tooltip": "Memilih saran %s dari %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Tekan Ta<PERSON> untuk beralih ke saran berikutnya", "narration.suggestion.usage.cycle.hidable": "Tekan Tab untuk beralih ke saran berikutnya, atau Escape untuk meninggalkan saran", "narration.suggestion.usage.fill.fixed": "Tekan Tab untuk menggunakan saran", "narration.suggestion.usage.fill.hidable": "Tekan Tab untuk menggunakan saran, atau Escape untuk meninggalkan saran", "narration.tab_navigation.usage": "<PERSON><PERSON>l dan Tab untuk beralih antara tab", "narrator.button.accessibility": "Aksesibilitas", "narrator.button.difficulty_lock": "<PERSON><PERSON><PERSON> kesulitan", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Tidak dikunci", "narrator.button.language": "Bahasa", "narrator.controls.bound": "%s terikat dengan %s", "narrator.controls.reset": "Atur ulang tombol %s", "narrator.controls.unbound": "%s tidak terikat", "narrator.joining": "Bergabung", "narrator.loading": "Memuat: %s", "narrator.loading.done": "Se<PERSON><PERSON>", "narrator.position.list": "Memilih daftar baris %s dari %s", "narrator.position.object_list": "Memilih baris elemen %s dari %s", "narrator.position.screen": "Elemen layar %s dari %s", "narrator.position.tab": "Memilih tab %s dari %s", "narrator.ready_to_play": "Siap untuk bermain", "narrator.screen.title": "<PERSON><PERSON>", "narrator.screen.usage": "<PERSON><PERSON><PERSON> kursor tetikus atau tombol Tab untuk memilih elemen", "narrator.select": "Dipilih: %s", "narrator.select.world": "Memilih %s, terakhir bermain: %s, %s, %s, versi: %s", "narrator.select.world_info": "Memilih %s, terakhir bermain: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON>", "optimizeWorld.confirm.description": "Tindakan ini akan mengoptimalkan dunia Anda dengan memastikan bahwa data tersimpan merupakan format terbaru. Ini akan memakan waktu yang cukup lama, tergantung pada dunia Anda. <PERSON><PERSON> se<PERSON>, dunia Anda mungkin akan ber<PERSON>lan lebih cepat, tetapi tidak akan cocok dengan versi lama permainan. Anda yakin ingin melanjutkan?", "optimizeWorld.confirm.proceed": "<PERSON><PERSON>t Cadangan dan <PERSON>", "optimizeWorld.confirm.title": "Optimasi Dunia", "optimizeWorld.info.converted": "<PERSON><PERSON><PERSON> yang telah ditingkatkan: %s", "optimizeWorld.info.skipped": "<PERSON><PERSON><PERSON> yang di<PERSON>: %s", "optimizeWorld.info.total": "<PERSON><PERSON><PERSON>: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "<PERSON>gh<PERSON><PERSON> bingkah...", "optimizeWorld.stage.failed": "Gagal! :(", "optimizeWorld.stage.finished": "Menyelesaikan...", "optimizeWorld.stage.finished.chunks": "Menyelesaikan peningkatan bingkah...", "optimizeWorld.stage.finished.entities": "Menyelesaikan peningkatan entitas...", "optimizeWorld.stage.finished.poi": "Menyelesaikan peningkatan titik perhatian...", "optimizeWorld.stage.upgrading": "Meningkatkan seluruh bingkah...", "optimizeWorld.stage.upgrading.chunks": "Meningkatkan seluruh bingkah...", "optimizeWorld.stage.upgrading.entities": "Meningkatkan seluruh entitas...", "optimizeWorld.stage.upgrading.poi": "Meningkatkan seluruh titik perhatian...", "optimizeWorld.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '%s'", "options.accessibility": "Pengaturan Aksesibilitas...", "options.accessibility.high_contrast": "Ko<PERSON>ras <PERSON>", "options.accessibility.high_contrast.error.tooltip": "Paket sumber Kontras Tinggi tidak tersedia.", "options.accessibility.high_contrast.tooltip": "Meningkatkan kontras dari elemen antarmuka pengguna.", "options.accessibility.high_contrast_block_outline": "<PERSON><PERSON> <PERSON> Balok Kontras Tinggi", "options.accessibility.high_contrast_block_outline.tooltip": "Meningkatkan kontras garis luar balok sasaran.", "options.accessibility.link": "Petunjuk Aksesibilitas", "options.accessibility.menu_background_blurriness": "Kebur<PERSON>u", "options.accessibility.menu_background_blurriness.tooltip": "Mengubah keburaman latar belakang menu", "options.accessibility.narrator_hotkey": "Tombol Pintas Narator", "options.accessibility.narrator_hotkey.mac.tooltip": "Memungkinkan untuk menyalakan dan mematikan Narator dengan 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Memungkinkan untuk menyalakan dan mematikan Narator dengan 'Ctrl+B'.", "options.accessibility.panorama_speed": "Laju Gulir Panorama", "options.accessibility.text_background": "Lat. Bel. Teks", "options.accessibility.text_background.chat": "<PERSON><PERSON>lan", "options.accessibility.text_background.everywhere": "Di mana pun", "options.accessibility.text_background_opacity": "Keburaman Lat. Bel. Teks", "options.accessibility.title": "Pengaturan Aksesibilitas", "options.allowServerListing": "<PERSON>zinkan Daftar <PERSON>", "options.allowServerListing.tooltip": "Peladen mungkin mencantumkan pemain daring sebagai bagian status publik.\n<PERSON>gan pilihan ini dimatikan, nama <PERSON>a tidak akan muncul dalam daftar.", "options.ao": "Pencahayaan Hal<PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minimal", "options.ao.off": "<PERSON><PERSON>", "options.attack.crosshair": "Silang Pembidik", "options.attack.hotbar": "<PERSON><PERSON><PERSON>", "options.attackIndicator": "Tanda <PERSON>", "options.audioDevice": "<PERSON><PERSON><PERSON>", "options.audioDevice.default": "<PERSON><PERSON>", "options.autoJump": "Lompat Otomatis", "options.autoSuggestCommands": "<PERSON><PERSON>", "options.autosaveIndicator": "<PERSON><PERSON> Si<PERSON>", "options.biomeBlendRadius": "Transisi Bioma", "options.biomeBlendRadius.1": "<PERSON><PERSON> (Tercepat)", "options.biomeBlendRadius.11": "11x11 (Ekstrem)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (Cepat)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON> Tinggi)", "options.chat": "Pengatura<PERSON>...", "options.chat.color": "<PERSON><PERSON>", "options.chat.delay": "Jeda Obrolan: %s detik", "options.chat.delay_none": "Jeda Obrolan: Tidak Ada", "options.chat.height.focused": "Tinggi Terfokus", "options.chat.height.unfocused": "Tinggi Tak Terfokus", "options.chat.line_spacing": "Jarak <PERSON>", "options.chat.links": "Tautan Web", "options.chat.links.prompt": "Konfirmasi <PERSON>", "options.chat.opacity": "Keburaman Teks Obrolan", "options.chat.scale": "Ukuran Teks Obrolan", "options.chat.title": "Pengatura<PERSON>...", "options.chat.visibility": "<PERSON><PERSON>lan", "options.chat.visibility.full": "Tampak", "options.chat.visibility.hidden": "Tersemb<PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON>", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s bingkah", "options.clouds.fancy": "<PERSON><PERSON><PERSON>", "options.clouds.fast": "Cepat", "options.controls": "Kendali...", "options.credits_and_attribution": "Kredit & Atribusi...", "options.damageTiltStrength": "<PERSON><PERSON><PERSON>", "options.damageTiltStrength.tooltip": "<PERSON><PERSON><PERSON> goyangan kamera yang disebabkan karena terluka.", "options.darkMojangStudiosBackgroundColor": "Logo Monokrom", "options.darkMojangStudiosBackgroundColor.tooltip": "Mengganti latar belakang layar memuat Mojang Studios menjadi hitam.", "options.darknessEffectScale": "Denyut <PERSON>", "options.darknessEffectScale.tooltip": "Mengendalikan seberapa efek Kegelapan berdenyut ketika Penunggu atau Sculk Penjerit memberikan efek tersebut kepada Anda.", "options.difficulty": "Kesulitan", "options.difficulty.easy": "Mudah", "options.difficulty.easy.info": "Makhluk jahat muncul tetapi memberikan luka lebih kecil. <PERSON><PERSON> bilah kela<PERSON>an habis, nyawa berkurang hingga 5 hati.", "options.difficulty.hard": "Sulit", "options.difficulty.hard.info": "Makh<PERSON> jahat muncul dan memberikan luka lebih besar. Dapat mati kelaparan bila bilah kelaparan habis.", "options.difficulty.hardcore": "<PERSON><PERSON><PERSON>", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Makh<PERSON> jahat muncul dan member<PERSON>n luka <PERSON>a. <PERSON><PERSON> bilah kela<PERSON>an habis, nyawa berkurang hingga set<PERSON>ah hati.", "options.difficulty.online": "Kesulitan Peladen", "options.difficulty.peaceful": "Damai", "options.difficulty.peaceful.info": "Tidak ada makhluk jahat dan hanya beberapa makhluk netral muncul. <PERSON><PERSON>h kelaparan tidak berkurang dan nyawa selalu mengisi sepanjang waktu.", "options.directionalAudio": "Suara Direksional", "options.directionalAudio.off.tooltip": "Suara Stereo Klasik", "options.directionalAudio.on.tooltip": "Menggunakan suara direksional berbasis HRTF untuk meningkatkan simulasi suara 3D. Memerlukan perangkat keras suara yang kompatibel dengan HTRF, dan sangat dianjurkan mengenakan pelantang telinga.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON><PERSON>", "options.entityDistanceScaling": "Jarak <PERSON>", "options.entityShadows": "Bayangan Entitas", "options.font": "Pengaturan Fon...", "options.font.title": "Pengaturan F<PERSON>", "options.forceUnicodeFont": "Paksa Fon Unicode", "options.fov": "Jarak <PERSON>", "options.fov.max": "<PERSON><PERSON>", "options.fov.min": "Normal", "options.fovEffectScale": "Efek Jarak Pandang", "options.fovEffectScale.tooltip": "Mengendalikan seberapa banyak jarak pandang dapat berubah oleh efek permainan.", "options.framerate": "%s bpd", "options.framerateLimit": "Laju Bingkai Maks.", "options.framerateLimit.max": "Takterbatas", "options.fullscreen": "<PERSON><PERSON>", "options.fullscreen.current": "Saat ini", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "<PERSON><PERSON><PERSON><PERSON> layar penuh", "options.fullscreen.unavailable": "Pengaturan tidak tersedia", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "<PERSON><PERSON>", "options.gamma.max": "Terang", "options.gamma.min": "<PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Kecepatan Kilau", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON> kecepatan dari tampilan kilau yang berkilau pada benda terpesona.", "options.glintStrength": "Ke<PERSON><PERSON><PERSON>", "options.glintStrength.tooltip": "Mengatur tingkat transparansi dari tampilan kilau pada benda terpesona.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "Menakjubkan!", "options.graphics.fabulous.tooltip": "Grafika \"%s\" menggunakan pencorak layar untuk menampilkan cuaca, awan, dan partikel di balik balok tembus cahaya dan air.\nIni dapat memengaruhi kinerja untuk perangkat portabel dan perangkat dengan tampilan 4K.", "options.graphics.fancy": "<PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "<PERSON><PERSON> \"<PERSON>wah\" menyeimbangkan kinerja dan kualitas untuk sebagian besar perangkat.\n<PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan partikel mungkin tidak muncul di belakang balok tembus cahaya atau air.", "options.graphics.fast": "Cepat", "options.graphics.fast.tooltip": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" men<PERSON><PERSON>i jumlah hujan dan salju yang dapat dilihat.\nEfek tembus pandang dimatikan untuk beberapa balok seperti dedaunan.", "options.graphics.warning.accept": "Lan<PERSON><PERSON><PERSON>", "options.graphics.warning.cancel": "Kembali", "options.graphics.warning.message": "Perangkat grafika Anda terdeteksi tidak mendukung untuk pilihan grafika %s.\n\n<PERSON><PERSON> boleh mengabaikannya dan lan<PERSON>t, tetapi tidak akan diberikan dukungan kepada perangkat Anda bila memilih menggunakan grafika %s.", "options.graphics.warning.renderer": "Mesin render terdeteksi: [%s]", "options.graphics.warning.title": "Perangkat Grafika Tidak <PERSON>", "options.graphics.warning.vendor": "Vendor terdeteksi: [%s]", "options.graphics.warning.version": "Versi OpenGL terdeteksi: [%s]", "options.guiScale": "<PERSON><PERSON><PERSON>", "options.guiScale.auto": "<PERSON><PERSON><PERSON><PERSON>", "options.hidden": "Tersemb<PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON><PERSON>r membuat langit berdenyar. <PERSON><PERSON> akan tetap terlihat.", "options.hideMatchedNames": "Nama Cocok Taktampak", "options.hideMatchedNames.tooltip": "Peladen pihak ketiga dapat mengirim pesan obrolan dalam format nonstandar.\n<PERSON>gan menyalakan pilihan ini, pemain tersembunyi akan dicocokkan berdasarkan nama pengirim obrolan.", "options.hideSplashTexts": "Sembunyikan Teks Kuning", "options.hideSplashTexts.tooltip": "Menyembunyikan teks kuning pada menu utama.", "options.inactivityFpsLimit": "Kurangi BPD ketika", "options.inactivityFpsLimit.afk": "<PERSON><PERSON>", "options.inactivityFpsLimit.afk.tooltip": "Membatasi laju bingkai ke 30 ketika permainan tidak mendapat masukan pemain selama lebih dari semenit, lalu dibatasi menjadi 10 setelah 9 menit kemudian.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Membatasi kecepatan bingkai hanya ketika jendela permainan di<PERSON>.", "options.invertMouse": "Balik Tetikus", "options.japaneseGlyphVariants": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "options.japaneseGlyphVariants.tooltip": "Menggunakan varian Jepang untuk aksara CJK di dalam fon asali.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "<PERSON><PERSON>", "options.language": "Bahasa...", "options.language.title": "Bahasa", "options.languageAccuracyWarning": "(Penerjemahan mungkin tidak 100%% akurat)", "options.languageWarning": "Penerjemahan mungkin tidak 100%% akurat", "options.mainHand": "<PERSON><PERSON>", "options.mainHand.left": "<PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON>", "options.mipmapLevels": "Ting<PERSON> Mi<PERSON>ap", "options.modelPart.cape": "Jubah", "options.modelPart.hat": "<PERSON>i", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON>", "options.mouseWheelSensitivity": "Sensitivi<PERSON>", "options.mouse_settings": "Pengaturan Tetikus...", "options.mouse_settings.title": "Pengat<PERSON><PERSON>", "options.multiplayer.title": "<PERSON><PERSON><PERSON><PERSON>...", "options.multiplier": "%sx", "options.music_frequency": "Kekerapan Musik", "options.music_frequency.constant": "<PERSON><PERSON><PERSON>", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Sering", "options.music_frequency.tooltip": "Mengubah seberapa sering musik dimainkan ketika di dalam dunia permainan.", "options.narrator": "Narator", "options.narrator.all": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.chat": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.notavailable": "Tidak Tersedia", "options.narrator.off": "<PERSON><PERSON>", "options.narrator.system": "<PERSON><PERSON><PERSON><PERSON>", "options.notifications.display_time": "Waktu <PERSON>", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON> lama waktu seluruh pemberitahuan tampak pada layar.", "options.off": "<PERSON><PERSON>", "options.off.composed": "%s: <PERSON><PERSON>", "options.on": "<PERSON><PERSON><PERSON>", "options.on.composed": "%s: <PERSON><PERSON><PERSON>", "options.online": "Daring...", "options.online.title": "<PERSON><PERSON><PERSON><PERSON>", "options.onlyShowSecureChat": "<PERSON><PERSON><PERSON>", "options.onlyShowSecureChat.tooltip": "<PERSON>ya menampilkan pesan dari pemain lain yang dapat diverifikasi bahwa pesan tersebut dikirim oleh pemain tersebut, dan belum dimodifikasi.", "options.operatorItemsTab": "Tab Benda Operator", "options.particles": "Partikel", "options.particles.all": "<PERSON><PERSON><PERSON>", "options.particles.decreased": "Dikurangi", "options.particles.minimal": "Sedikit", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Beberapa tindakan di dalam bingkah akan langsung menghimpun ulang bingkah. Ini termasuk menempatkan dan menghancurkan balok.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Bingkah terdekat selalu terhimpun langsung. Ini dapat berdampak pada kinerja permainan saat balok ditempatkan atau dihancurkan.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Bingkah terdekat dihimpun dalam benang paralel. Ini dapat mengakibatkan lubang visual yang singkat saat balok dihancurkan.", "options.rawMouseInput": "<PERSON><PERSON><PERSON>", "options.realmsNotifications": "Kabar Realms & Undangan", "options.realmsNotifications.tooltip": "Mengambil kabar dan undangan Realms di layar judul dan menampilkan ikonnya masing-masing pada tombol Realms.", "options.reducedDebugInfo": "Info Awakutu Minimal", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "<PERSON><PERSON><PERSON>", "options.renderDistance": "Jarak <PERSON>", "options.resourcepack": "<PERSON><PERSON>...", "options.rotateWithMinecart": "<PERSON><PERSON><PERSON><PERSON>gan <PERSON>", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON> apakah pandangan pemain berputar bersamaan dengan Kereta Tambang yang berbelok. Hanya tersedia di dunia dengan pengaturan 'Peningkatan Kereta Tambang' din<PERSON>n.", "options.screenEffectScale": "Efek Distorsi", "options.screenEffectScale.tooltip": "Kekuatan efek distorsi pada layar mual dan layar gerbang <PERSON>.\nPada nilai yang lebih kecil, efek mual digantikan dengan paparan hijau.", "options.sensitivity": "Sensitivitas", "options.sensitivity.max": "SUPERCEPAT!!!", "options.sensitivity.min": "*kuap*", "options.showNowPlayingToast": "Pemberitahuan Musik", "options.showNowPlayingToast.tooltip": "Menampilkan pemberitahuan judul musik ketika musik mulai dimainkan. Pemberitahuan yang sama akan ditampilkan secara terus-menerus di menu jeda ketika musik sedang dimainkan.", "options.showSubtitles": "<PERSON><PERSON><PERSON>", "options.simulationDistance": "<PERSON><PERSON><PERSON>", "options.skinCustomisation": "Pengubahsuaian Rupa...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sounds": "Musik & Suara...", "options.sounds.title": "Pilihan Musik & Suara", "options.telemetry": "Data Telemetri...", "options.telemetry.button": "Pengumpulan Data", "options.telemetry.button.tooltip": "\"%s\" mencakup data yang diperlukan saja.\n\"%s\" mencakup data yang diperlukan serta data opsional.", "options.telemetry.disabled": "Telemetri dimatikan.", "options.telemetry.state.all": "<PERSON><PERSON><PERSON>", "options.telemetry.state.minimal": "Sedikit", "options.telemetry.state.none": "Tidak ada", "options.title": "<PERSON><PERSON><PERSON>", "options.touchscreen": "<PERSON> <PERSON>", "options.video": "Pengaturan Video...", "options.videoTitle": "Pengaturan Video", "options.viewBobbing": "Jalan Realistis", "options.visible": "Tampak", "options.vsync": "Sinkronisasi Vertikal", "outOfMemory.message": "Minecraft telah kehabisan memori.\n\nIni bisa jadi disebabkan oleh kekutu dalam permainan atau oleh Java Virtual Machine yang tidak diakolasikan ruang yang cukup.\n\nUntuk menghindari kerusakan dunia, permainan telah ditutup. Kami telah mencoba untuk menambah ruang agar Anda dapat kembali ke menu utama dan kembali bermain, tetapi ini belum tentu dapat bekerja.\n\nMohon muat ulang permainan bila Anda melihat pesan ini lagi.", "outOfMemory.title": "Kehabisan memori!", "pack.available.title": "Tersedia", "pack.copyFailure": "<PERSON><PERSON> menyalin paket", "pack.dropConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menambahkan paket-paket berikut ke Minecraft?", "pack.dropInfo": "Seret dan lepas berkas pada jendela ini untuk menambahkan paket", "pack.dropRejected.message": "<PERSON>tri berikut merupakan paket yang tidak sah dan tidak tersalin:\n %s", "pack.dropRejected.title": "Entri nonpaket", "pack.folderInfo": "(Letakkan berkas paket di sini)", "pack.incompatible": "Tidak cocok", "pack.incompatible.confirm.new": "Paket ini dibuat untuk versi Minecraft yang lebih baru dan mungkin tidak dapat bekerja dengan baik.", "pack.incompatible.confirm.old": "Paket ini dibuat untuk versi Minecraft yang lebih lama dan mungkin sudah tidak dapat bekerja dengan baik.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin memuat paket ini?", "pack.incompatible.new": "(Dibuat untuk versi Minecraft yang lebih baru)", "pack.incompatible.old": "(Dibuat untuk versi Minecraft yang lebih lama)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Buka Map Paket", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "bawaan", "pack.source.feature": "fitur", "pack.source.local": "lokal", "pack.source.server": "peladen", "pack.source.world": "dunia", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albania", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Buket", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Tengkorak Bernyala", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Ganti", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Selamat Siang Tuan Courbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Api", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab dengan Tiga <PERSON>oni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatif", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Lorong", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Tanaman Giok", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Telunjuk", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Telaga", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Kolam", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Panggung Telah Siap", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Kanigara", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Awapaket", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Pengembara", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "<PERSON><PERSON>", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Air", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON><PERSON>, <PERSON>", "painting.random": "Varian acak", "parsing.bool.expected": "Mengharap<PERSON>", "parsing.bool.invalid": "<PERSON><PERSON> tidak sah, men<PERSON><PERSON><PERSON><PERSON> 'true' atau 'false', tetapi didapa<PERSON> '%s'", "parsing.double.expected": "Mengharapkan angka ganda", "parsing.double.invalid": "Angka ganda '%s' tidak sah", "parsing.expected": "Mengharapkan '%s'", "parsing.float.expected": "Mengharapkan angka mengambang", "parsing.float.invalid": "Angka mengambang '%s' tidak sah", "parsing.int.expected": "Mengharapkan angka bulat", "parsing.int.invalid": "Angka bulat '%s' tidak sah", "parsing.long.expected": "Mengharapkan angka besar", "parsing.long.invalid": "Angka besar '%s' tidak sah", "parsing.quote.escape": "Urutan kunci keluar tidak sah '\\%s' dalam untaian berkutip", "parsing.quote.expected.end": "<PERSON><PERSON><PERSON> tanpa tanda kutip penutup", "parsing.quote.expected.start": "Mengharapkan tanda kutip untuk memulai untaian", "particle.invalidOptions": "Tidak dapat menguraikan pilihan partikel: %s", "particle.notFound": "Partikel tidak dikenal: %s", "permissions.requires.entity": "Memerlukan entitas agar dapat menjalankan perintah di sini", "permissions.requires.player": "<PERSON><PERSON><PERSON><PERSON> pemain agar dapat menjalankan perintah di sini", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Ketika Diterapkan:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predikat tidak dikenal: %s", "quickplay.error.invalid_identifier": "Tidak dapat menemukan dunia dengan pengenal yang diberikan", "quickplay.error.realm_connect": "Tidak dapat terhubung ke Realm", "quickplay.error.realm_permission": "Tidak ada izin untuk terhubung ke Realm ini", "quickplay.error.title": "Main Se<PERSON><PERSON>", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, AS", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, AS", "realms.configuration.region.east_us_2": "North Carolina, AS", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_west": "<PERSON><PERSON><PERSON>", "realms.configuration.region.korea_central": "Korea Selatan", "realms.configuration.region.north_central_us": "Illinois, AS", "realms.configuration.region.north_europe": "Irlandia", "realms.configuration.region.south_central_us": "Texas, AS", "realms.configuration.region.southeast_asia": "Singapura", "realms.configuration.region.sweden_central": "Swedia", "realms.configuration.region.uae_north": "Uni Emirat Arab (UEA)", "realms.configuration.region.uk_south": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_central_us": "Utah, AS", "realms.configuration.region.west_europe": "<PERSON><PERSON>", "realms.configuration.region.west_us": "California, AS", "realms.configuration.region.west_us_2": "Washington, AS", "realms.configuration.region_preference.automatic_owner": "Otomatis (latensi pemilik Realm)", "realms.configuration.region_preference.automatic_player": "Otomatis (pertama bergabung ke sesi)", "realms.missing.snapshot.error.text": "Realms saat ini tidak didukung dalam versi citra", "recipe.notFound": "Resep tidak dikenal: %s", "recipe.toast.description": "<PERSON><PERSON><PERSON> buku resep <PERSON>", "recipe.toast.title": "Resep Baru Terbuka!", "record.nowPlaying": "Sekarang Memutar: %s", "recover_world.bug_tracker": "Laporkan Kekutu", "recover_world.button": "Coba Pulihkan", "recover_world.done.failed": "<PERSON>l pulih dari keadaan sebelumnya.", "recover_world.done.success": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON>", "recover_world.issue.none": "Tidak ada masalah", "recover_world.message": "Beberapa masalah terjadi saat mencoba membaca map dunia \"%s\".\nDunia dapat dipulihkan ke keadaan sebelumnya atau Anda dapat melaporkan masalah ini pada pelacak kekutu.", "recover_world.no_fallback": "Tidak ada keadaan pemulihan yang tersedia", "recover_world.restore": "Mencoba Memulihkan", "recover_world.restoring": "Mencoba memulihkan dunia...", "recover_world.state_entry": "Keadaan dari %s: ", "recover_world.state_entry.unknown": "tidak <PERSON>i", "recover_world.title": "Gagal memuat dunia", "recover_world.warning": "<PERSON><PERSON> memuat ring<PERSON>an dunia", "resourcePack.broken_assets": "ASET RUSAK DITEMUKAN", "resourcePack.high_contrast.name": "Ko<PERSON>ras <PERSON>", "resourcePack.load_fail": "<PERSON>l memuat ulang sumber daya", "resourcePack.programmer_art.name": "<PERSON><PERSON>", "resourcePack.runtime_failure": "Kesalahan paket sumber terdeteksi", "resourcePack.server.name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "resourcePack.title": "<PERSON><PERSON><PERSON>", "resourcePack.vanilla.description": "<PERSON><PERSON><PERSON> dan sensasi asali Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON>", "resourcepack.downloading": "<PERSON><PERSON><PERSON><PERSON>", "resourcepack.progress": "<PERSON><PERSON><PERSON><PERSON> berka<PERSON> (%s MB)...", "resourcepack.requesting": "Membuat Permin<PERSON>an...", "screenshot.failure": "Gagal menyimpan Tangkapan <PERSON>: %s", "screenshot.success": "Tangkapan layar disimpan sebagai %s", "selectServer.add": "Tambah Peladen", "selectServer.defaultName": "Peladen Minecraft", "selectServer.delete": "Hapus", "selectServer.deleteButton": "Hapus", "selectServer.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus peladen ini?", "selectServer.deleteWarning": "'%s' akan hilang selamanya! (Permanen!)", "selectServer.direct": "Sambungan Langsung", "selectServer.edit": "Sunting", "selectServer.hiddenAddress": "(Tersemb<PERSON><PERSON>)", "selectServer.refresh": "Segarkan", "selectServer.select": "Bergabung", "selectWorld.access_failure": "<PERSON><PERSON> mengakses dunia", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON>", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON> /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "Hapus Data Tembolok", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON><PERSON> dan <PERSON>", "selectWorld.backupJoinSkipButton": "<PERSON><PERSON>, <PERSON><PERSON>!", "selectWorld.backupQuestion.customized": "Dunia ubah suaian sudah tidak didukung", "selectWorld.backupQuestion.downgrade": "Penurunan versi dunia tidak didukung", "selectWorld.backupQuestion.experimental": "Dunia yang menggunakan Pengaturan Percobaan tidak didukung", "selectWorld.backupQuestion.snapshot": "<PERSON><PERSON><PERSON><PERSON> Anda benar-benar ingin memuat dunia ini?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON>, kami sudah tidak mendukung dunia ubah suaian dalam versi Minecraft ini. Kami masih dapat memuat dunia ini dan menjaga semua seperti apa adanya, tetapi medan baru yang dihasilkan tidak akan diubahsuai lagi. Mohon maaf atas ketidaknyamanan yang terjadi!", "selectWorld.backupWarning.downgrade": "Dunia ini terakhir dimainkan di versi %s; <PERSON><PERSON> men<PERSON> versi %s.\nMenurunkan versi dunia dapat menyebabkan kerusakan - kami tidak dapat menjamin bahwa itu dapat memuat atau bekerja. B<PERSON>a ingin melan<PERSON>, harap buat cadangan.", "selectWorld.backupWarning.experimental": "Dunia ini menggunakan pengaturan percobaan yang bisa berhenti kapan saja. Kami tidak menjamin bahwa itu dapat memuat atau bekerja. <PERSON>r<PERSON><PERSON>-hatilah!", "selectWorld.backupWarning.snapshot": "Dunia ini terakhir dimainkan di versi %s; <PERSON><PERSON> mengg<PERSON>kan versi %s.\n<PERSON><PERSON> membuat cadangan bila Anda mengalami kerusakan dunia.", "selectWorld.bonusItems": "<PERSON><PERSON>", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "Harus dikonversi!", "selectWorld.conversion.tooltip": "Dunia ini harus di buka di versi lama (seperti 1.6.4) agar dapat dikonversi dengan aman", "selectWorld.create": "Buat Dunia Baru", "selectWorld.customizeType": "Ubah Suaikan", "selectWorld.dataPacks": "Paket Data", "selectWorld.data_read": "Membaca data dunia...", "selectWorld.delete": "Hapus", "selectWorld.deleteButton": "Hapus", "selectWorld.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus dunia ini?", "selectWorld.deleteWarning": "'%s' akan hilang selamanya! (Permanen!)", "selectWorld.delete_failure": "<PERSON><PERSON>us dunia", "selectWorld.edit": "Sunting", "selectWorld.edit.backup": "Buat Cadangan", "selectWorld.edit.backupCreated": "Mencadangkan: %s", "selectWorld.edit.backupFailed": "Pencadangan gagal", "selectWorld.edit.backupFolder": "Buka Map Cadangan", "selectWorld.edit.backupSize": "ukuran: %s MB", "selectWorld.edit.export_worldgen_settings": "Ekspor Pengaturan Penghasilan Dunia", "selectWorld.edit.export_worldgen_settings.failure": "Ekspor gagal", "selectWorld.edit.export_worldgen_settings.success": "Ekspor berhasil", "selectWorld.edit.openFolder": "Buka Map Dunia", "selectWorld.edit.optimize": "Optimalkan Dunia", "selectWorld.edit.resetIcon": "Atur Ulang Ikon", "selectWorld.edit.save": "Simpan", "selectWorld.edit.title": "Sunting Dunia", "selectWorld.enterName": "<PERSON><PERSON>", "selectWorld.enterSeed": "<PERSON><PERSON> untuk penghasil dunia", "selectWorld.experimental": "Per<PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Fitur percobaan yang diperlukan: %s", "selectWorld.experimental.details.title": "Persyaratan <PERSON>", "selectWorld.experimental.message": "Berhati-hatilah!\nPengaturan ini memerlukan fitur yang masih dalam pengembangan. <PERSON>nia Anda dapat ber<PERSON>, rusak, atau tidak bekerja pada pembaruan berikutnya.", "selectWorld.experimental.title": "<PERSON><PERSON><PERSON>", "selectWorld.experiments": "Per<PERSON><PERSON><PERSON>", "selectWorld.experiments.info": "Percobaan adalah fitur-fitur baru potensial. Berhati-hatilah karena beberapa hal dapat rusak. Percobaan tidak bisa dimatikan setelah pembuatan dunia.", "selectWorld.futureworld.error.text": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat mencoba untuk memuat dunia dari versi terbaru. Ini sebuah tindakan berbahaya sejak awal; mohon maaf ini tidak berhasil.", "selectWorld.futureworld.error.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>han!", "selectWorld.gameMode": "Mode Permainan", "selectWorld.gameMode.adventure": "Petualangan", "selectWorld.gameMode.adventure.info": "Sama seperti Mode Sintas, tetapi balok tidak bisa ditambahkan atau dihapus.", "selectWorld.gameMode.adventure.line1": "Sama seperti Mode Sintas, tetapi balok tidak bisa", "selectWorld.gameMode.adventure.line2": "ditambah<PERSON> atau dihapus", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, bang<PERSON>, dan j<PERSON><PERSON>i tanpa batas. <PERSON><PERSON> bisa terbang, memiliki material yang tak terbatas, dan tidak dapat dilukai oleh monster.", "selectWorld.gameMode.creative.line1": "Sumber daya tak terbatas, be<PERSON> terbang, dan", "selectWorld.gameMode.creative.line2": "menghancurkan balok secara langsung", "selectWorld.gameMode.hardcore": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "Mode Sintas terkunci pada tingkat kesulitan 'Sulit'. <PERSON>a tidak dapat bangkit kembali bila anda mati.", "selectWorld.gameMode.hardcore.line1": "Sama seperti Mode Sintas, terkunci di kesulitan", "selectWorld.gameMode.hardcore.line2": "te<PERSON><PERSON>, dan hanya satu nyawa", "selectWorld.gameMode.spectator": "Penonton", "selectWorld.gameMode.spectator.info": "Anda dapat melihat tapi tidak dapat menyentuh.", "selectWorld.gameMode.spectator.line1": "Anda dapat melihat tapi tidak dapat menyentuh", "selectWorld.gameMode.survival": "Sintas", "selectWorld.gameMode.survival.info": "<PERSON><PERSON><PERSON><PERSON> dunia misterius di mana Anda memba<PERSON>, men<PERSON><PERSON><PERSON><PERSON>, memb<PERSON><PERSON>, dan melawan monster.", "selectWorld.gameMode.survival.line1": "<PERSON>i sumber daya, buat kera<PERSON>, raih", "selectWorld.gameMode.survival.line2": "tingkatan, kese<PERSON>an, dan makan", "selectWorld.gameRules": "<PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON>", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON><PERSON> (.json)", "selectWorld.incompatible.description": "Dunia ini tidak dapat dibuka dalam versi ini.\nDunia terakhir dimainkan di versi %s.", "selectWorld.incompatible.info": "Versi tidak cocok: %s", "selectWorld.incompatible.title": "Versi tidak cocok", "selectWorld.incompatible.tooltip": "Dunia ini tidak dapat dibuka karena dibuat oleh versi yang tidak cocok.", "selectWorld.incompatible_series": "Dibuat oleh versi yang tidak cocok", "selectWorld.load_folder_access": "Tidak dapat membaca atau mengakses map dunia permainan!", "selectWorld.loading_list": "Memuat Daftar Dunia", "selectWorld.locked": "Terkunci oleh program Minecraft lain yang sedang berjalan", "selectWorld.mapFeatures": "Hasilkan Struktur", "selectWorld.mapFeatures.info": "<PERSON><PERSON>, <PERSON><PERSON>, dll.", "selectWorld.mapType": "<PERSON><PERSON>", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "<PERSON><PERSON><PERSON>...", "selectWorld.newWorld": "<PERSON><PERSON>", "selectWorld.recreate": "B<PERSON>t <PERSON>", "selectWorld.recreate.customized.text": "Dunia ubah suaian sudah tidak didukung dalam versi Minecraft ini. <PERSON><PERSON> masih dapat membuat ulang lagi dunia ini menggunakan benih yang sama, tetapi pengubahsuaian medan apa pun akan hilang. Mohon maaf atas ketidaknyamanan yang terjadi!", "selectWorld.recreate.customized.title": "Dunia ubah suaian sudah tidak didukung", "selectWorld.recreate.error.text": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba membuat ulang dunia.", "selectWorld.recreate.error.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>han!", "selectWorld.resource_load": "Menyiapkan Sumber Daya...", "selectWorld.resultFolder": "<PERSON><PERSON> disimpan dalam:", "selectWorld.search": "cari dunia", "selectWorld.seedInfo": "Biarkan kosong untuk benih acak", "selectWorld.select": "Mainkan Dunia Terpilih", "selectWorld.targetFolder": "Simpan map: %s", "selectWorld.title": "<PERSON><PERSON><PERSON>", "selectWorld.tooltip.fromNewerVersion1": "Dunia telah disimpan dalam versi yang lebih baru,", "selectWorld.tooltip.fromNewerVersion2": "pemuatan dunia ini dapat menyebabkan masalah!", "selectWorld.tooltip.snapshot1": "<PERSON>an lupa membuat cadangan dunia ini", "selectWorld.tooltip.snapshot2": "sebelum Anda muat di citra ini.", "selectWorld.unable_to_load": "Tidak dapat memuat dunia", "selectWorld.version": "Versi:", "selectWorld.versionJoinButton": "Tetap <PERSON>", "selectWorld.versionQuestion": "<PERSON><PERSON><PERSON><PERSON> Anda benar-benar ingin memuat dunia ini?", "selectWorld.versionUnknown": "tidak <PERSON>i", "selectWorld.versionWarning": "Dunia ini terakhir dimainkan di versi %s dan pemuatan ke dalam versi ini dapat menyebabkan kerusakan!", "selectWorld.warning.deprecated.question": "Beberapa fitur yang digunakan sudah usang dan akan berhenti berfungsi di masa mendatang. A<PERSON><PERSON>h Anda ingin melanjutkan?", "selectWorld.warning.deprecated.title": "Peringatan! Pengaturan ini menggunakan fitur usang", "selectWorld.warning.experimental.question": "Pengaturan ini bersifat eksperimental dan suatu hari nanti bisa berhenti berfungsi. <PERSON><PERSON><PERSON><PERSON> Anda ingin melanju<PERSON>kan?", "selectWorld.warning.experimental.title": "Peringatan! Pengaturan ini menggunakan fitur percobaan", "selectWorld.warning.lowDiskSpace.description": "Ruangan di dalam perangkat Anda tidak mencukupi.\nKehab<PERSON> ruangan diska saat berada di dalam permainan dapat merusak dunia Anda.", "selectWorld.warning.lowDiskSpace.title": "Peringatan! <PERSON>uang diska rendah!", "selectWorld.world": "Dunia", "sign.edit": "Ubah Pesan <PERSON>", "sleep.not_possible": "Tidak bisa melewati malam dengan beristirahat", "sleep.players_sleeping": "%s/%s pemain tidur", "sleep.skipping_night": "<PERSON><PERSON><PERSON> sepanjang malam ini", "slot.only_single_allowed": "<PERSON>ya diperbolehkan petak tunggal, terdapat '%s'", "slot.unknown": "Petak '%s' tidak dikenal", "snbt.parser.empty_key": "Kunci tidak dapat kosong", "snbt.parser.expected_binary_numeral": "Mengharapkan bilangan biner", "snbt.parser.expected_decimal_numeral": "Mengharapkan bilangan desimal", "snbt.parser.expected_float_type": "Mengharapkan bilangan mengambang", "snbt.parser.expected_hex_escape": "Mengharapkan karakter literal dengan panjang %s", "snbt.parser.expected_hex_numeral": "Mengharapkan bilangan he<PERSON>l", "snbt.parser.expected_integer_type": "Mengharapkan bilangan bulat", "snbt.parser.expected_non_negative_number": "Mengharapkan bilangan nonnegatif", "snbt.parser.expected_number_or_boolean": "Mengharapkan bilangan atau Boole", "snbt.parser.expected_string_uuid": "Mengharapkan untaian yang mewakili UUID sah", "snbt.parser.expected_unquoted_string": "Mengharapkan untaian tak dikutip yang sah", "snbt.parser.infinity_not_allowed": "Hanya diperbolehkan bilangan berbatas", "snbt.parser.invalid_array_element_type": "<PERSON><PERSON> elemen larik tidak sah", "snbt.parser.invalid_character_name": "Nama karakter Unicode tidak sah", "snbt.parser.invalid_codepoint": "<PERSON>lai karakter Unicode tidak sah: %s", "snbt.parser.invalid_string_contents": "<PERSON>i untaian tidak sah", "snbt.parser.invalid_unquoted_start": "Untaian tak dikutip tidak dapat dimulai dengan angka 0—9, +, atau -", "snbt.parser.leading_zero_not_allowed": "Bilangan desimal tidak dapat diawali dengan 0", "snbt.parser.no_such_operation": "Operasi tersebut tidak ada: %s", "snbt.parser.number_parse_failure": "Gagal menguraikan bilangan: %s", "snbt.parser.undescore_not_allowed": "<PERSON><PERSON><PERSON> garis bawah tidak diperbolehkan pada awal atau akhir bilangan", "soundCategory.ambient": "Sekitar/Lingkungan", "soundCategory.block": "Balok", "soundCategory.hostile": "Makhluk Jahat", "soundCategory.master": "Volume Utama", "soundCategory.music": "Mu<PERSON>", "soundCategory.neutral": "Makhluk Ramah", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Kotak Lagu/Nada", "soundCategory.ui": "Antarmuka", "soundCategory.voice": "Suara/Ucapan", "soundCategory.weather": "Cuaca", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "Halaman Be<PERSON>", "spectatorMenu.previous_page": "Halaman Sebelumnya", "spectatorMenu.root.prompt": "<PERSON>kan tombol untuk memilih perintah, dan tekan lagi untuk menggunakannya.", "spectatorMenu.team_teleport": "Teleportasi ke Anggota Regu", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON>h regu tujuan teleportasi", "spectatorMenu.teleport": "Teleportasi ke Pemain", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON> pemain tujuan teleportasi", "stat.generalButton": "<PERSON><PERSON>", "stat.itemsButton": "<PERSON><PERSON>", "stat.minecraft.animals_bred": "<PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.bell_ring": "Lonceng Dibunyikan", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.clean_armor": "Potongan Zirah <PERSON>", "stat.minecraft.clean_banner": "Spanduk Dibersihkan", "stat.minecraft.clean_shulker_box": "Kotak Shulker Dibersihkan", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "Jarak <PERSON>k", "stat.minecraft.damage_absorbed": "Serangan Diserap", "stat.minecraft.damage_blocked_by_shield": "Serangan <PERSON>", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> (Diserap)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> (Ditahan)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "Serangan Di<PERSON>", "stat.minecraft.deaths": "<PERSON>", "stat.minecraft.drop": "<PERSON><PERSON>", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON>", "stat.minecraft.enchant_item": "<PERSON><PERSON>", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "<PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON>", "stat.minecraft.fly_one_cm": "Jarak <PERSON>", "stat.minecraft.happy_ghast_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_beacon": "Interaksi <PERSON>", "stat.minecraft.interact_with_blast_furnace": "Interaksi den<PERSON>", "stat.minecraft.interact_with_brewingstand": "Interaksi dengan Alat Peramu", "stat.minecraft.interact_with_campfire": "Interaksi den<PERSON>", "stat.minecraft.interact_with_cartography_table": "Interaksi <PERSON>", "stat.minecraft.interact_with_crafting_table": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_furnace": "Interaksi den<PERSON>", "stat.minecraft.interact_with_grindstone": "Interaks<PERSON>", "stat.minecraft.interact_with_lectern": "Interaksi dengan Podium", "stat.minecraft.interact_with_loom": "Interaksi dengan Al<PERSON>", "stat.minecraft.interact_with_smithing_table": "Interaksi den<PERSON>", "stat.minecraft.interact_with_smoker": "Interaksi <PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON>", "stat.minecraft.jump": "Kali Lo<PERSON>at", "stat.minecraft.leave_game": "Permainan <PERSON>", "stat.minecraft.minecart_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.mob_kills": "Makhluk Dibunuh", "stat.minecraft.open_barrel": "Tong Dibuka", "stat.minecraft.open_chest": "<PERSON><PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON>", "stat.minecraft.open_shulker_box": "Kotak Shulker Di<PERSON>", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.play_noteblock": "Kotak Nada Dimainkan", "stat.minecraft.play_record": "<PERSON><PERSON><PERSON>", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.raid_trigger": "<PERSON><PERSON>", "stat.minecraft.raid_win": "<PERSON><PERSON>", "stat.minecraft.sleep_in_bed": "Kali Tidur di Tempat Tidur", "stat.minecraft.sneak_time": "Waktu <PERSON>", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.talked_to_villager": "<PERSON><PERSON><PERSON>", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_death": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_rest": "<PERSON><PERSON><PERSON>", "stat.minecraft.total_world_time": "<PERSON><PERSON><PERSON> dengan Dunia Terbuka", "stat.minecraft.traded_with_villager": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON>", "stat.minecraft.tune_noteblock": "Kotak Nada Disetel", "stat.minecraft.use_cauldron": "Air Diambil da<PERSON>", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "Jarak <PERSON>", "stat.mobsButton": "Makhluk", "stat_type.minecraft.broken": "<PERSON>", "stat_type.minecraft.crafted": "Kali Dibuat", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.killed": "Anda membunuh %s %s", "stat_type.minecraft.killed.none": "Anda tidak pernah membunuh %s", "stat_type.minecraft.killed_by": "%s telah membunuh Anda %s kali", "stat_type.minecraft.killed_by.none": "Anda tidak pernah dibunuh %s", "stat_type.minecraft.mined": "Kali Ditambang", "stat_type.minecraft.picked_up": "Dipungut", "stat_type.minecraft.used": "<PERSON>", "stats.none": "-", "structure_block.button.detect_size": "PERIKSA", "structure_block.button.load": "MUAT", "structure_block.button.save": "SIMPAN", "structure_block.custom_data": "Nama Etiket Data Ubah Suaian", "structure_block.detect_size": "<PERSON><PERSON><PERSON> dan Posisi Struktur:", "structure_block.hover.corner": "Sudut: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Muat: %s", "structure_block.hover.save": "Simpan: %s", "structure_block.include_entities": "Sertakan Entitas:", "structure_block.integrity": "<PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "structure_block.integrity.integrity": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.integrity.seed": "<PERSON><PERSON>", "structure_block.invalid_structure_name": "<PERSON>a struktur '%s' tidak sah", "structure_block.load_not_found": "Struktur '%s' tidak tersedia ", "structure_block.load_prepare": "Menyiapkan posisi struktur '%s'", "structure_block.load_success": "Memuat struktur dari '%s'", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "Data", "structure_block.mode.load": "Muat", "structure_block.mode.save": "Simpan", "structure_block.mode_info.corner": "Mode Sudut - <PERSON><PERSON> letak dan ukuran", "structure_block.mode_info.data": "Mode Data - Penanda logika permainan", "structure_block.mode_info.load": "Mode Muat - Me<PERSON>at dari berkas", "structure_block.mode_info.save": "Mode Simpan - <PERSON><PERSON><PERSON> ke berkas", "structure_block.position": "Posisi Relatif", "structure_block.position.x": "posisi x relatif", "structure_block.position.y": "posisi y relatif", "structure_block.position.z": "posisi z relatif", "structure_block.save_failure": "Tak dapat menyimpan struktur '%s'", "structure_block.save_success": "Struktur ters<PERSON><PERSON> se<PERSON> '%s'", "structure_block.show_air": "Tampakkan Balok Tak Terlihat:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON>:", "structure_block.size": "Ukuran Struktur", "structure_block.size.x": "ukuran x struktur", "structure_block.size.y": "ukuran y struktur", "structure_block.size.z": "ukuran z struktur", "structure_block.size_failure": "Tidak dapat memeriksa ukuran struktur. Tambahkan sudut lain yang sesuai dengan nama struktur", "structure_block.size_success": "Ukuran '%s' ber<PERSON>il diperiksa", "structure_block.strict": "Penempatan Ketat:", "structure_block.structure_name": "<PERSON><PERSON>", "subtitles.ambient.cave": "<PERSON><PERSON>n", "subtitles.ambient.sound": "<PERSON><PERSON>n", "subtitles.block.amethyst_block.chime": "Kecubung berbunyi", "subtitles.block.amethyst_block.resonate": "Kecubung bergema", "subtitles.block.anvil.destroy": "<PERSON><PERSON> hancur", "subtitles.block.anvil.land": "<PERSON><PERSON> mendarat", "subtitles.block.anvil.use": "<PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON>", "subtitles.block.barrel.open": "<PERSON> membuka", "subtitles.block.beacon.activate": "<PERSON><PERSON>", "subtitles.block.beacon.ambient": "<PERSON><PERSON>", "subtitles.block.beacon.deactivate": "<PERSON>ar mati", "subtitles.block.beacon.power_select": "<PERSON><PERSON> dipilih", "subtitles.block.beehive.drip": "<PERSON><PERSON> menetes", "subtitles.block.beehive.enter": "<PERSON><PERSON> memasuki sarang", "subtitles.block.beehive.exit": "Lebah meninggalkan sarang", "subtitles.block.beehive.shear": "Gunting men<PERSON>kis", "subtitles.block.beehive.work": "<PERSON><PERSON> be<PERSON>", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON> menggema", "subtitles.block.bell.use": "Lonceng berdenting", "subtitles.block.big_dripleaf.tilt_down": "<PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_up": "<PERSON><PERSON>", "subtitles.block.blastfurnace.fire_crackle": "Gemeretak Tanur Tiup", "subtitles.block.brewing_stand.brew": "Alat <PERSON> be<PERSON>", "subtitles.block.bubble_column.bubble_pop": "Gelembung meletus", "subtitles.block.bubble_column.upwards_ambient": "Gelembung mengalir", "subtitles.block.bubble_column.upwards_inside": "Gelembung berdesis", "subtitles.block.bubble_column.whirlpool_ambient": "Gelembung berputar", "subtitles.block.bubble_column.whirlpool_inside": "Gelembung bertiup", "subtitles.block.button.click": "Tombol mengeklik", "subtitles.block.cake.add_candle": "<PERSON><PERSON> ditekan", "subtitles.block.campfire.crackle": "Gemeretak Api Unggun", "subtitles.block.candle.crackle": "Gemeretak Lilin", "subtitles.block.candle.extinguish": "<PERSON><PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON>p", "subtitles.block.chest.locked": "<PERSON><PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON> me<PERSON>", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON> layu", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON> Ko<PERSON> tumbuh", "subtitles.block.comparator.click": "Pembanding mengeklik", "subtitles.block.composter.empty": "Komposter dikosongkan", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON>", "subtitles.block.composter.ready": "<PERSON><PERSON><PERSON> berkompos", "subtitles.block.conduit.activate": "Talang Salur menyala", "subtitles.block.conduit.ambient": "Talang Salur berdenyut", "subtitles.block.conduit.attack.target": "Talang Salur menyerang", "subtitles.block.conduit.deactivate": "Talang Salur mati", "subtitles.block.copper_bulb.turn_off": "<PERSON><PERSON>ga padam", "subtitles.block.copper_bulb.turn_on": "Lam<PERSON>a", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON>", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON><PERSON>mbuka", "subtitles.block.crafter.craft": "<PERSON><PERSON>", "subtitles.block.crafter.fail": "<PERSON><PERSON> gagal merajin", "subtitles.block.creaking_heart.hurt": "<PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON>n", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON><PERSON>", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON>-b<PERSON><PERSON><PERSON> kering", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON>", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON> pecah", "subtitles.block.dispenser.dispense": "<PERSON><PERSON>", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON> gagal", "subtitles.block.door.toggle": "Pintu menderit", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON>", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON>", "subtitles.block.enchantment_table.use": "<PERSON><PERSON>", "subtitles.block.end_portal.spawn": "Gerbang <PERSON> membuka", "subtitles.block.end_portal_frame.fill": "<PERSON>", "subtitles.block.eyeblossom.close": "Bunga Mata menutup", "subtitles.block.eyeblossom.idle": "Bunga Mata berbisik", "subtitles.block.eyeblossom.open": "Bunga Mata membuka", "subtitles.block.fence_gate.toggle": "Pintu <PERSON> menderit", "subtitles.block.fire.ambient": "Gemeretak Api", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Kunang-<PERSON>nang berdengung", "subtitles.block.frogspawn.hatch": "Kecebong menetas", "subtitles.block.furnace.fire_crackle": "Gemeretak Tungku", "subtitles.block.generic.break": "Balok hancur", "subtitles.block.generic.fall": "Sesuatu terjatuh di atas balok", "subtitles.block.generic.footsteps": "<PERSON><PERSON> kaki", "subtitles.block.generic.hit": "Menghancurkan balok", "subtitles.block.generic.place": "Balok ditempatkan", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "Tanaman dipotong", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON>", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON><PERSON> turun balok madu", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON>", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON>mbuka", "subtitles.block.lava.ambient": "<PERSON>va meletup", "subtitles.block.lava.extinguish": "<PERSON><PERSON> mendesis", "subtitles.block.lever.click": "<PERSON><PERSON>", "subtitles.block.note_block.note": "Kotak Nada dimainkan", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON>n", "subtitles.block.piston.move": "<PERSON><PERSON> bergerak", "subtitles.block.pointed_dripstone.drip_lava": "Lava menetes", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava menetes ke dalam <PERSON>ali", "subtitles.block.pointed_dripstone.drip_water": "Air menetes", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Air menetes ke dalam <PERSON>ali", "subtitles.block.pointed_dripstone.land": "Stalaktit jatuh", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.portal.travel": "<PERSON><PERSON>", "subtitles.block.portal.trigger": "Suara Gerbang menguat", "subtitles.block.pressure_plate.click": "Pelat Penekan mengeklik", "subtitles.block.pumpkin.carve": "Gunting men<PERSON>kir", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON> mendesis", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON>", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON>", "subtitles.block.respawn_anchor.set_spawn": "<PERSON><PERSON> Pembangkit mengatur titik bangkit", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON>-b<PERSON><PERSON><PERSON> pasir", "subtitles.block.sand.wind": "<PERSON><PERSON>", "subtitles.block.sculk.charge": "<PERSON><PERSON><PERSON> berb<PERSON>h", "subtitles.block.sculk.spread": "Sculk menyebar", "subtitles.block.sculk_catalyst.bloom": "Katalisator Sc<PERSON>k mekar", "subtitles.block.sculk_sensor.clicking": "Sensor Sculk mengeklik", "subtitles.block.sculk_sensor.clicking_stop": "Sensor Sculk berhenti mengeklik", "subtitles.block.sculk_shrieker.shriek": "Sculk Penjerit menjerit", "subtitles.block.shulker_box.close": "Kotak Shulker menutup", "subtitles.block.shulker_box.open": "<PERSON><PERSON> membuka", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON>", "subtitles.block.smithing_table.use": "<PERSON><PERSON>", "subtitles.block.smoker.smoke": "Pengasap berasap", "subtitles.block.sniffer_egg.crack": "Telur Pengendus retak", "subtitles.block.sniffer_egg.hatch": "Telur Pengendus menetas", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON> bertelur", "subtitles.block.sponge.absorb": "Spons mengisap", "subtitles.block.sweet_berry_bush.pick_berries": "Buni dipetik", "subtitles.block.trapdoor.close": "<PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.open": "<PERSON><PERSON><PERSON>mbuka", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON> menderit", "subtitles.block.trial_spawner.about_to_spawn_item": "<PERSON><PERSON> men<PERSON>ikan disia<PERSON>kan", "subtitles.block.trial_spawner.ambient": "Gemeretak Pembangkit Cobaan", "subtitles.block.trial_spawner.ambient_charged": "Gemeretak mengerikan", "subtitles.block.trial_spawner.ambient_ominous": "Gemeretak mengerikan", "subtitles.block.trial_spawner.charge_activate": "Pertanda buruk melanda Pembangkit Cobaan", "subtitles.block.trial_spawner.close_shutter": "Pembangkit Cobaan menutup", "subtitles.block.trial_spawner.detect_player": "Pembangkit Cobaan memuat", "subtitles.block.trial_spawner.eject_item": "Pembangkit Cobaan mengeluarkan benda", "subtitles.block.trial_spawner.ominous_activate": "Pertanda buruk melanda Pembangkit Cobaan", "subtitles.block.trial_spawner.open_shutter": "Pembangkit Cobaan membuka", "subtitles.block.trial_spawner.spawn_item": "<PERSON><PERSON> men<PERSON>ikan muncul", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON><PERSON>", "subtitles.block.trial_spawner.spawn_mob": "Pembangkit Cobaan membangkitkan makhluk", "subtitles.block.tripwire.attach": "Kawat <PERSON> terpasang", "subtitles.block.tripwire.click": "Kawat Senggol mengeklik", "subtitles.block.tripwire.detach": "Kawat <PERSON> terlepas", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "Gemeretak Brankas", "subtitles.block.vault.close_shutter": "Brankas menutup", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON> padam", "subtitles.block.vault.eject_item": "Brankas mengeluarkan benda", "subtitles.block.vault.insert_item": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON> gagal dibuka", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON> membuka", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON> men<PERSON>k pemain", "subtitles.block.water.ambient": "Air mengalir", "subtitles.block.wet_sponge.dries": "Spons mengering", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON> ditempatkan", "subtitles.chiseled_bookshelf.insert_enchanted": "Buku Pesona ditempatkan", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON>", "subtitles.chiseled_bookshelf.take_enchanted": "<PERSON><PERSON>a diam<PERSON>", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON> duri", "subtitles.entity.allay.ambient_with_item": "Andaru mencari", "subtitles.entity.allay.ambient_without_item": "Andaru mendambakan", "subtitles.entity.allay.death": "And<PERSON> mati", "subtitles.entity.allay.hurt": "Andaru terluka", "subtitles.entity.allay.item_given": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.allay.item_taken": "Andaru merasa tenang", "subtitles.entity.allay.item_thrown": "Andaru melempar", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> men<PERSON>", "subtitles.entity.armadillo.brush": "Skutum disikat", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON> berlin<PERSON>", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.scute_drop": "Armadilo merontokkan skutum", "subtitles.entity.armadillo.unroll_finish": "Armadilo membuka gulungan", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.arrow.hit": "<PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> terk<PERSON>ah", "subtitles.entity.arrow.shoot": "<PERSON><PERSON>", "subtitles.entity.axolotl.attack": "Axolotl <PERSON>", "subtitles.entity.axolotl.death": "Axolotl mati", "subtitles.entity.axolotl.hurt": "Axolotl terluka", "subtitles.entity.axolotl.idle_air": "Cicitan Axolotl", "subtitles.entity.axolotl.idle_water": "Cicitan Axolotl", "subtitles.entity.axolotl.splash": "Axolotl <PERSON>", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> berenang", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON> meleng<PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON> terluka", "subtitles.entity.bat.takeoff": "<PERSON><PERSON>law<PERSON> terbang", "subtitles.entity.bee.ambient": "Lebah berdengung", "subtitles.entity.bee.death": "Le<PERSON> mati", "subtitles.entity.bee.hurt": "Lebah terluka", "subtitles.entity.bee.loop": "Lebah berdengung", "subtitles.entity.bee.loop_aggressive": "Lebah berdengung marah", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> berdengung bahagia", "subtitles.entity.bee.sting": "Sengatan Lebah", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON><PERSON> berna<PERSON>", "subtitles.entity.blaze.burn": "Gemeretak Ke<PERSON>mang", "subtitles.entity.blaze.death": "Kemamang mati", "subtitles.entity.blaze.hurt": "Ke<PERSON>mang terluka", "subtitles.entity.blaze.shoot": "Ke<PERSON><PERSON>g <PERSON>", "subtitles.entity.boat.paddle_land": "Men<PERSON>ung", "subtitles.entity.boat.paddle_water": "Men<PERSON>ung", "subtitles.entity.bogged.ambient": "Bengkarawa menggemeretuk", "subtitles.entity.bogged.death": "Bengkara<PERSON> mati", "subtitles.entity.bogged.hurt": "Bengkarawa terluka", "subtitles.entity.breeze.charge": "<PERSON><PERSON> men<PERSON> serangan", "subtitles.entity.breeze.death": "<PERSON><PERSON> mati", "subtitles.entity.breeze.deflect": "<PERSON><PERSON>", "subtitles.entity.breeze.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.breeze.idle_air": "Pawana terbang", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON> menderu", "subtitles.entity.breeze.inhale": "<PERSON><PERSON>", "subtitles.entity.breeze.jump": "Pawana melompat", "subtitles.entity.breeze.land": "<PERSON><PERSON> mendarat", "subtitles.entity.breeze.shoot": "Pawana <PERSON>", "subtitles.entity.breeze.slide": "Pa<PERSON> meluncur", "subtitles.entity.breeze.whirl": "<PERSON><PERSON> berputar", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON>", "subtitles.entity.camel.ambient": "<PERSON>ta mendengus", "subtitles.entity.camel.dash": "Lompatan Unta", "subtitles.entity.camel.dash_ready": "<PERSON>ta pulih", "subtitles.entity.camel.death": "Unta mati", "subtitles.entity.camel.eat": "<PERSON>ta makan", "subtitles.entity.camel.hurt": "<PERSON>ta terluka", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON><PERSON> du<PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.camel.step": "<PERSON>ta melangkah", "subtitles.entity.camel.step_sand": "Unta melangkah di pasir", "subtitles.entity.cat.ambient": "<PERSON><PERSON> men<PERSON>g", "subtitles.entity.cat.beg_for_food": "Kucing meminta", "subtitles.entity.cat.death": "<PERSON><PERSON> mati", "subtitles.entity.cat.eat": "<PERSON><PERSON> makan", "subtitles.entity.cat.hiss": "<PERSON><PERSON> mendesis", "subtitles.entity.cat.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.cat.purr": "<PERSON><PERSON> mendengkur", "subtitles.entity.chicken.ambient": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON> mati", "subtitles.entity.chicken.egg": "<PERSON><PERSON>", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> te<PERSON>", "subtitles.entity.cod.death": "Kod mati", "subtitles.entity.cod.flop": "Kod melompat", "subtitles.entity.cod.hurt": "<PERSON><PERSON> te<PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON> melen<PERSON>", "subtitles.entity.cow.death": "<PERSON>pi mati", "subtitles.entity.cow.hurt": "<PERSON><PERSON> te<PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON> diperah", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON> melihat", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.creaking.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON> roboh", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON> di<PERSON>", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.creeper.death": "C<PERSON>per mati", "subtitles.entity.creeper.hurt": "Creeper terluka", "subtitles.entity.creeper.primed": "Creeper mendesis", "subtitles.entity.dolphin.ambient": "Cicitan Lu<PERSON>-Lumba", "subtitles.entity.dolphin.ambient_water": "Lumba-Lumba berdebus", "subtitles.entity.dolphin.attack": "Lumba-Lumba menyerang", "subtitles.entity.dolphin.death": "Lumba-Lumba mati", "subtitles.entity.dolphin.eat": "Lumba-Lumba makan", "subtitles.entity.dolphin.hurt": "Lumba-Lumba terluka", "subtitles.entity.dolphin.jump": "Lumba-Lumba melompat", "subtitles.entity.dolphin.play": "Lumba-Lu<PERSON> bermain", "subtitles.entity.dolphin.splash": "Lumba-<PERSON><PERSON> menyiprat", "subtitles.entity.dolphin.swim": "Lumba-Lumba berenang", "subtitles.entity.donkey.ambient": "Keledai me<PERSON>ih", "subtitles.entity.donkey.angry": "Keledai meringkik", "subtitles.entity.donkey.chest": "Peti dipasangkan ke Keledai", "subtitles.entity.donkey.death": "Keledai mati", "subtitles.entity.donkey.eat": "Keledai makan", "subtitles.entity.donkey.hurt": "Keledai terluka", "subtitles.entity.donkey.jump": "Keledai melompat", "subtitles.entity.drowned.ambient": "<PERSON>ong<PERSON>am <PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON>ong<PERSON>am <PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.drowned.hurt": "Zonggelam terluka", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.drowned.step": "Zonggelam melang<PERSON>h", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.egg.throw": "Telur di<PERSON>", "subtitles.entity.elder_guardian.ambient": "Pamong Tetua mengerang", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.elder_guardian.flop": "Tetua Pen<PERSON>ga melompat", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.entity.ender_dragon.ambient": "Naga mengaum", "subtitles.entity.ender_dragon.death": "Naga mati", "subtitles.entity.ender_dragon.flap": "Kelepak Naga", "subtitles.entity.ender_dragon.growl": "<PERSON>ga men<PERSON>am", "subtitles.entity.ender_dragon.hurt": "Naga terluka", "subtitles.entity.ender_dragon.shoot": "Naga <PERSON>", "subtitles.entity.ender_eye.death": "<PERSON> j<PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON> <PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> men<PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> mati", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.enderman.scream": "<PERSON><PERSON> menjerit", "subtitles.entity.enderman.stare": "Enderman member<PERSON>", "subtitles.entity.enderman.teleport": "Enderman melakukan teleportasi", "subtitles.entity.endermite.ambient": "Ender<PERSON> berdecit", "subtitles.entity.endermite.death": "Endermite mati", "subtitles.entity.endermite.hurt": "Endermite terluka", "subtitles.entity.evoker.ambient": "<PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON> me<PERSON>i", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON> mati", "subtitles.entity.evoker.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON> serangan", "subtitles.entity.evoker.prepare_summon": "Pawang men<PERSON> pemanggilan", "subtitles.entity.evoker.prepare_wololo": "Pawang membaca mantra", "subtitles.entity.evoker_fangs.attack": "Taring menggigit", "subtitles.entity.experience_orb.pickup": "Pengalaman diperoleh", "subtitles.entity.firework_rocket.blast": "Ledakan kembang api", "subtitles.entity.firework_rocket.launch": "Kembang api diluncurkan", "subtitles.entity.firework_rocket.twinkle": "Kembang api berk<PERSON>p", "subtitles.entity.fish.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "Pelampung dikembalikan", "subtitles.entity.fishing_bobber.splash": "Percikan Pelampung Pancingan", "subtitles.entity.fishing_bobber.throw": "Pelampung dilempar", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> marah", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.teleport": "<PERSON><PERSON><PERSON> me<PERSON>n teleportasi", "subtitles.entity.frog.ambient": "Katak berketur", "subtitles.entity.frog.death": "Katak mati", "subtitles.entity.frog.eat": "<PERSON>ak makan", "subtitles.entity.frog.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.frog.long_jump": "Katak melompat", "subtitles.entity.generic.big_fall": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Sekarat", "subtitles.entity.generic.drink": "Menyeruput", "subtitles.entity.generic.eat": "Memakan", "subtitles.entity.generic.explode": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON><PERSON> api", "subtitles.entity.generic.hurt": "Sesuatu terluka", "subtitles.entity.generic.small_fall": "Sesuatu tersandung", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "Berenang", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON> angin", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "Ghast Cilik berdekut", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> Cili<PERSON> mati", "subtitles.entity.ghastling.hurt": "G<PERSON>t Cilik terluka", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> muncul", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.break": "Bingkai Benda Sinar dihancurkan", "subtitles.entity.glow_item_frame.place": "Bingkai Benda Sinar ditempatkan", "subtitles.entity.glow_item_frame.remove_item": "Mengosongkan Bingkai Benda Sinar", "subtitles.entity.glow_item_frame.rotate_item": "Bingkai Benda Sinar mengeklik", "subtitles.entity.glow_squid.ambient": "Cumi-Cumi <PERSON> be<PERSON>", "subtitles.entity.glow_squid.death": "Cumi-<PERSON><PERSON> mati", "subtitles.entity.glow_squid.hurt": "Cumi-Cumi <PERSON> terluka", "subtitles.entity.glow_squid.squirt": "Cumi-Cumi Sinar menembak tinta", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> men<PERSON>mb<PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> makan", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON> lepas", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.entity.goat.long_jump": "Kambing melompat", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> diperah", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> melang<PERSON>h", "subtitles.entity.guardian.ambient": "Penjaga men<PERSON>ang", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.guardian.flop": "Penjaga melompat", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.happy_ghast.equip": "Rungkup dipasang", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.happy_ghast.unequip": "Rungkup dilepas", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> men<PERSON>am marah", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON>n mati", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> mundur", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> me<PERSON>h", "subtitles.entity.horse.ambient": "<PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON> kuda dipasang", "subtitles.entity.horse.breathe": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.horse.death": "<PERSON>da mati", "subtitles.entity.horse.eat": "<PERSON>da makan", "subtitles.entity.horse.gallop": "<PERSON>da berlari kencang", "subtitles.entity.horse.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.horse.jump": "Kuda melompat", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.ambient": "Zongkering mengerang", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>h men<PERSON>", "subtitles.entity.husk.death": "Zongkering mati", "subtitles.entity.husk.hurt": "Zongkering terluka", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON>i", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "<PERSON><PERSON><PERSON> kebutaan", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON> bayangan", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON>", "subtitles.entity.iron_golem.damage": "Golem <PERSON> hancur", "subtitles.entity.iron_golem.death": "Golem <PERSON> mati", "subtitles.entity.iron_golem.hurt": "Golem <PERSON> terluka", "subtitles.entity.iron_golem.repair": "Golem Besi diperbaiki", "subtitles.entity.item.break": "<PERSON><PERSON> hanc<PERSON>", "subtitles.entity.item.pickup": "<PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "Bingkai Benda dihancurkan", "subtitles.entity.item_frame.place": "Bingkai Benda ditempatkan", "subtitles.entity.item_frame.remove_item": "Mengosongkan Bingkai Benda", "subtitles.entity.item_frame.rotate_item": "Bingkai Benda mengeklik", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON> Tali putus", "subtitles.entity.leash_knot.place": "Simpul Tali terikat", "subtitles.entity.lightning_bolt.impact": "<PERSON>baran petir", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON><PERSON> petir", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "Lama mengembik marah", "subtitles.entity.llama.chest": "<PERSON>i dipasa<PERSON>kan ke Lama", "subtitles.entity.llama.death": "<PERSON> mati", "subtitles.entity.llama.eat": "<PERSON>", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Kubus Magma mati", "subtitles.entity.magma_cube.hurt": "Kubus Magma terluka", "subtitles.entity.magma_cube.squish": "Kubus Magma berdecit", "subtitles.entity.minecart.inside": "<PERSON><PERSON> berdentang", "subtitles.entity.minecart.inside_underwater": "Kereta Tambang berdentang di dalam air", "subtitles.entity.minecart.riding": "<PERSON><PERSON>", "subtitles.entity.mooshroom.convert": "Moosh<PERSON> berubah", "subtitles.entity.mooshroom.eat": "Mooshroom makan", "subtitles.entity.mooshroom.milk": "Mooshroom diperah", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom diperah curiga", "subtitles.entity.mule.ambient": "<PERSON><PERSON> meringih", "subtitles.entity.mule.angry": "Bagal mering<PERSON>k", "subtitles.entity.mule.chest": "Peti dipasangkan ke Bagal", "subtitles.entity.mule.death": "<PERSON>gal mati", "subtitles.entity.mule.eat": "<PERSON>gal makan", "subtitles.entity.mule.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.mule.jump": "Bagal melompat", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.painting.place": "Lukisan ditempatkan", "subtitles.entity.panda.aggressive_ambient": "Panda gusar", "subtitles.entity.panda.ambient": "Panda terengah-engah", "subtitles.entity.panda.bite": "<PERSON>da mengg<PERSON>t", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> menciut", "subtitles.entity.panda.death": "Panda mati", "subtitles.entity.panda.eat": "Panda makan", "subtitles.entity.panda.hurt": "Panda terluka", "subtitles.entity.panda.pre_sneeze": "Hidung Panda gatal", "subtitles.entity.panda.sneeze": "Panda bersin", "subtitles.entity.panda.step": "Panda melang<PERSON>h", "subtitles.entity.panda.worried_ambient": "<PERSON>da mere<PERSON>", "subtitles.entity.parrot.ambient": "<PERSON><PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON> mati", "subtitles.entity.parrot.eats": "<PERSON><PERSON> makan", "subtitles.entity.parrot.fly": "<PERSON><PERSON>", "subtitles.entity.parrot.hurts": "<PERSON><PERSON> te<PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON> men<PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON> men<PERSON>u <PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "Bayan meniru <PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON> men<PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "Bayan meniru Kubus Magma", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON>an men<PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON>an men<PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON> men<PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON> men<PERSON>", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON>", "subtitles.entity.phantom.ambient": "<PERSON><PERSON>-<PERSON><PERSON> me<PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.phantom.death": "<PERSON><PERSON>-<PERSON><PERSON> mati", "subtitles.entity.phantom.flap": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.phantom.hurt": "<PERSON><PERSON>-<PERSON><PERSON> te<PERSON>", "subtitles.entity.phantom.swoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.ambient": "<PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON> mati", "subtitles.entity.pig.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.pig.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> mengagumi benda", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> mendengus", "subtitles.entity.piglin.angry": "<PERSON><PERSON> mendengus marah", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> be<PERSON>h men<PERSON><PERSON>", "subtitles.entity.piglin.death": "<PERSON><PERSON> mati", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> mendengus iri", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> mundur", "subtitles.entity.piglin.step": "<PERSON><PERSON> me<PERSON>h", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> G<PERSON> mendengus", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> Ganas mendengus marah", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> Gana<PERSON> be<PERSON>h men<PERSON>", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> G<PERSON> mati", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> G<PERSON> terl<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> G<PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.player.attack.crit": "Serangan kritis", "subtitles.entity.player.attack.knockback": "Serangan pukul mundur", "subtitles.entity.player.attack.strong": "Serangan kuat", "subtitles.entity.player.attack.sweep": "Seranga<PERSON> men<PERSON>", "subtitles.entity.player.attack.weak": "Serangan lemah", "subtitles.entity.player.burp": "Bersendawa", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.levelup": "<PERSON><PERSON> tin<PERSON>", "subtitles.entity.player.teleport": "<PERSON><PERSON><PERSON> teleportasi", "subtitles.entity.polar_bear.ambient": "Beruang Kutub mengerang", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON> berdengung", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.polar_bear.hurt": "Beruang Ku<PERSON> terluka", "subtitles.entity.polar_bear.warning": "Beruang Kutub mengaum", "subtitles.entity.potion.splash": "Botol dibanting", "subtitles.entity.potion.throw": "Bo<PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_up": "<PERSON><PERSON>ggelembung", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON> mati", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON> melo<PERSON>", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON>", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON>", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> melo<PERSON>", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON> mendengus", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON> mengg<PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.ravager.hurt": "Pem<PERSON><PERSON> terluka", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON> meraung", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>h", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.salmon.death": "Salem mati", "subtitles.entity.salmon.flop": "Salem melompat", "subtitles.entity.salmon.hurt": "Salem terluka", "subtitles.entity.sheep.ambient": "Dom<PERSON> mengembik", "subtitles.entity.sheep.death": "Dom<PERSON> mati", "subtitles.entity.sheep.hurt": "Domba terluka", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>p", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> memb<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> me<PERSON> teleportasi", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.silverfish.ambient": "Gegat mendesis", "subtitles.entity.silverfish.death": "Gegat mati", "subtitles.entity.silverfish.hurt": "Gegat terluka", "subtitles.entity.skeleton.ambient": "Bengkarak menggemeretuk", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>h men<PERSON>di <PERSON>", "subtitles.entity.skeleton.death": "Bengkarak mati", "subtitles.entity.skeleton.hurt": "Bengkarak terluka", "subtitles.entity.skeleton.shoot": "Bengkarak menembak", "subtitles.entity.skeleton_horse.ambient": "Kuda Bengkarak meringkik", "subtitles.entity.skeleton_horse.death": "<PERSON>da <PERSON> mati", "subtitles.entity.skeleton_horse.hurt": "Kuda Bengkarak terluka", "subtitles.entity.skeleton_horse.jump_water": "Kuda Bengkarak melompat", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON>gkara<PERSON> berenang", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON>ali", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.sniffer.drop_seed": "Pengendus menja<PERSON> benih", "subtitles.entity.sniffer.eat": "Pengendus makan", "subtitles.entity.sniffer.egg_crack": "Telur Pengendus retak", "subtitles.entity.sniffer.egg_hatch": "Telur Pengendus menetas", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> se<PERSON>g", "subtitles.entity.sniffer.hurt": "Pen<PERSON><PERSON> terluka", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON> mendengus", "subtitles.entity.sniffer.scenting": "Pengendus mencium", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON>i", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON> mengendus", "subtitles.entity.sniffer.step": "Pengendus melangkah", "subtitles.entity.snow_golem.death": "Golem Salju mati", "subtitles.entity.snow_golem.hurt": "Golem Salju terluka", "subtitles.entity.snowball.throw": "Bola Salju dilempar", "subtitles.entity.spider.ambient": "Laba-Laba mendesis", "subtitles.entity.spider.death": "Laba-Lab<PERSON> mati", "subtitles.entity.spider.hurt": "Laba-Lab<PERSON> te<PERSON>", "subtitles.entity.squid.ambient": "<PERSON>umi-<PERSON><PERSON> berenang", "subtitles.entity.squid.death": "Cumi-C<PERSON> mati", "subtitles.entity.squid.hurt": "Cumi-Cumi terluka", "subtitles.entity.squid.squirt": "Cumi-Cumi menembak tinta", "subtitles.entity.stray.ambient": "Bengkaku menggemeretuk", "subtitles.entity.stray.death": "Beng<PERSON><PERSON> mati", "subtitles.entity.stray.hurt": "Bengkaku terluka", "subtitles.entity.strider.death": "Pengarung mati", "subtitles.entity.strider.eat": "Pengarung makan", "subtitles.entity.strider.happy": "Pengarung berkicau", "subtitles.entity.strider.hurt": "Pengarung terluka", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "Pengarung mundur", "subtitles.entity.tadpole.death": "<PERSON><PERSON>bong mati", "subtitles.entity.tadpole.flop": "Kecebong melompat", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON><PERSON> tumbuh", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON>bong terluka", "subtitles.entity.tnt.primed": "TNT mendesis", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON> mati", "subtitles.entity.tropical_fish.flop": "<PERSON><PERSON> melo<PERSON>", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON>", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.death": "<PERSON><PERSON> mati", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON> mati", "subtitles.entity.turtle.egg_break": "Telur Penyu pecah", "subtitles.entity.turtle.egg_crack": "Telur Penyu retak", "subtitles.entity.turtle.egg_hatch": "Telur Penyu menetas", "subtitles.entity.turtle.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON> terluka", "subtitles.entity.turtle.lay_egg": "<PERSON><PERSON>", "subtitles.entity.turtle.shamble": "Penyu menyeret", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON> menyeret", "subtitles.entity.turtle.swim": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> melen<PERSON>", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON>k berguma<PERSON>", "subtitles.entity.villager.celebrate": "Penduduk bersorak", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON><PERSON> tewas", "subtitles.entity.villager.hurt": "Penduduk terluka", "subtitles.entity.villager.no": "Penduduk tidak setuju", "subtitles.entity.villager.trade": "Penduduk be<PERSON>gang", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_butcher": "Tukang <PERSON>", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON>", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "Tukang Panah be<PERSON>ja", "subtitles.entity.villager.work_leatherworker": "<PERSON>kan<PERSON>", "subtitles.entity.villager.work_librarian": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_mason": "Tukang <PERSON> be<PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.villager.work_toolsmith": "Tukang Alat bekerja", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON>k <PERSON>", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON> berguma<PERSON>", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON> terluka", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON>gan<PERSON> berg<PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON>gan<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON><PERSON> minum susu", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON>gan<PERSON> minum ramuan", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON>gan<PERSON> terluka", "subtitles.entity.wandering_trader.no": "Pedagang <PERSON> tidak setuju", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON><PERSON> muncul", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>gang", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.agitated": "Penunggu mengerang marah", "subtitles.entity.warden.ambient": "Penunggu berdengking", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.attack_impact": "<PERSON>ung<PERSON>", "subtitles.entity.warden.death": "<PERSON>ung<PERSON> mati", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON>ali", "subtitles.entity.warden.emerge": "Penunggu bangkit", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> berdetak", "subtitles.entity.warden.hurt": "<PERSON>ung<PERSON> terluka", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> menyadari dengan amarah", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON> maju", "subtitles.entity.warden.nearby_closest": "<PERSON><PERSON><PERSON> mendekat", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON> meraung", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON> mengendus", "subtitles.entity.warden.sonic_boom": "<PERSON>ung<PERSON>", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> serangan", "subtitles.entity.warden.step": "Penunggu melangkah", "subtitles.entity.warden.tendril_clicks": "Sulur Penunggu mengeklik", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON>", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON>", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> minum", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.wither.ambient": "Wither murka", "subtitles.entity.wither.death": "Wither mati", "subtitles.entity.wither.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON>er te<PERSON>", "subtitles.entity.wither_skeleton.ambient": "Bengkarak Wither menggemeretuk", "subtitles.entity.wither_skeleton.death": "Bengkarak Wither mati", "subtitles.entity.wither_skeleton.hurt": "Bengkarak Wither terluka", "subtitles.entity.wolf.ambient": "Ser<PERSON>la terengah-engah", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.entity.wolf.pant": "Ser<PERSON>la terengah-engah", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON> menggoya<PERSON> badan", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> men<PERSON>am", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> men<PERSON>am marah", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> men<PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> hancur", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON> be<PERSON> men<PERSON>", "subtitles.entity.zombie.death": "<PERSON><PERSON>i mati", "subtitles.entity.zombie.destroy_egg": "Telur Penyu terinjak", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON>", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON>i mati", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON> terluka", "subtitles.entity.zombie_villager.ambient": "Zombi Penduduk men<PERSON>ang", "subtitles.entity.zombie_villager.converted": "<PERSON><PERSON><PERSON>duduk meleng<PERSON>", "subtitles.entity.zombie_villager.cure": "<PERSON>omb<PERSON>", "subtitles.entity.zombie_villager.death": "<PERSON><PERSON><PERSON> tewas", "subtitles.entity.zombie_villager.hurt": "Zombi Penduduk terluka", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON> mendengus", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON> mendengus marah", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON><PERSON> terluka", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON><PERSON> buruk", "subtitles.event.mob_effect.raid_omen": "<PERSON><PERSON> mendek<PERSON>", "subtitles.event.mob_effect.trial_omen": "<PERSON><PERSON>an mengerikan mendekat", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON>n", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON>", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON> berden<PERSON>", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_netherite": "<PERSON>irah Nether<PERSON> berden<PERSON>g", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_wolf": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.unequip_wolf": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.scrape": "Kapak men<PERSON>", "subtitles.item.axe.strip": "Kapak mengu<PERSON>", "subtitles.item.axe.wax_off": "<PERSON><PERSON>", "subtitles.item.bone_meal.use": "Kerutan Tepung Tulang", "subtitles.item.book.page_turn": "<PERSON><PERSON>", "subtitles.item.book.put": "<PERSON><PERSON> be<PERSON>bu<PERSON>", "subtitles.item.bottle.empty": "Mengosongkan Botol", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "Menyikat", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand.complete": "<PERSON><PERSON><PERSON>", "subtitles.item.bucket.empty": "Mengosongkan Ember", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl diambil", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bundle.drop_contents": "Mengosongkan Kantong", "subtitles.item.bundle.insert": "<PERSON><PERSON> di<PERSON>", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON> penuh", "subtitles.item.bundle.remove_one": "<PERSON><PERSON>", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON> teleportasi", "subtitles.item.crop.plant": "<PERSON><PERSON> ditanam", "subtitles.item.crossbow.charge": "Busur Silang memuat", "subtitles.item.crossbow.hit": "<PERSON><PERSON>", "subtitles.item.crossbow.load": "Busur Silang termuat", "subtitles.item.crossbow.shoot": "Busur Silang menembak", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON>", "subtitles.item.flintandsteel.use": "Pemantik Api mengeklik", "subtitles.item.glow_ink_sac.use": "Kantong Tinta Sinar dilumuri", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON>", "subtitles.item.hoe.till": "Cangkul memacul", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "<PERSON><PERSON><PERSON>", "subtitles.item.ink_sac.use": "Kantong Tinta <PERSON>", "subtitles.item.lead.break": "<PERSON><PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON> mengikat", "subtitles.item.lead.untied": "<PERSON><PERSON>", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON><PERSON>", "subtitles.item.lodestone_compass.lock": "Kompas Batu Magnet terkunci pada Batu Magnet", "subtitles.item.mace.smash_air": "<PERSON><PERSON> membanting", "subtitles.item.mace.smash_ground": "<PERSON><PERSON> membanting", "subtitles.item.nether_wart.plant": "<PERSON><PERSON> ditanam", "subtitles.item.ominous_bottle.dispose": "Botol pecah", "subtitles.item.saddle.unequip": "<PERSON><PERSON><PERSON>", "subtitles.item.shears.shear": "Gunting memotong", "subtitles.item.shears.snip": "Gunting menggunting", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "Sekop meratakan", "subtitles.item.spyglass.stop_using": "Teropong <PERSON>", "subtitles.item.spyglass.use": "Teropong membesar", "subtitles.item.totem.use": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.hit": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON> bergetar", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON> kem<PERSON>i", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON> men<PERSON>", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.item.trident.thunder": "<PERSON>sula memanggil petir", "subtitles.item.wolf_armor.break": "<PERSON><PERSON><PERSON>", "subtitles.item.wolf_armor.crack": "<PERSON><PERSON><PERSON> retak", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON>", "subtitles.item.wolf_armor.repair": "<PERSON><PERSON><PERSON>", "subtitles.particle.soul_escape": "Jiwa terle<PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON> digambar", "subtitles.ui.hud.bubble_pop": "Napas berk<PERSON>ng", "subtitles.ui.loom.take_result": "Alat Tenun digunakan", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON>", "subtitles.weather.rain": "<PERSON><PERSON>", "symlink_warning.message": "Memuat dunia dari map dengan tautan simbolis mungkin tidak aman bila Anda tidak tahu pasti apa yang Anda lakukan. Mohon kunjungi %s untuk mempelajari lebih lanjut.", "symlink_warning.message.pack": "Memuat paket dengan tautan simbolis mungkin tidak aman bila Anda tidak tahu pasti apa yang Anda lakukan. Mohon kunjungi %s untuk mempelajari lebih lanjut.", "symlink_warning.message.world": "Memuat dunia dari map dengan tautan simbolis mungkin tidak aman bila Anda tidak tahu pasti apa yang Anda lakukan. Mohon kunjungi %s untuk mempelajari lebih lanjut.", "symlink_warning.more_info": "Informasi Lebih <PERSON>", "symlink_warning.title": "Map dunia mengandung tautan simbolis", "symlink_warning.title.pack": "<PERSON>et yang ditambahkan mengandung tautan simbolis", "symlink_warning.title.world": "Map dunia mengandung tautan simbolis", "team.collision.always": "<PERSON><PERSON><PERSON>", "team.collision.never": "Tak <PERSON>", "team.collision.pushOtherTeams": "Mendorong regu lain", "team.collision.pushOwnTeam": "Mendorong regu sendiri", "team.notFound": "Regu '%s' tidak dikenal", "team.visibility.always": "<PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Sembunyikan dari regu lain", "team.visibility.hideForOwnTeam": "Sembunyikan dari regu sendiri", "team.visibility.never": "Tak <PERSON>", "telemetry.event.advancement_made.description": "Memahami konteks di balik menerima kemajuan dapat membantu kami lebih memahami dan meningkatkan progres permainan.", "telemetry.event.advancement_made.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.game_load_times.description": "Peristiwa ini dapat membantu kami mengetahui di mana peningkatan kinerja pengaktifan diperlukan dengan mengukur waktu eksekusi dari fase pengaktifan.", "telemetry.event.game_load_times.title": "<PERSON><PERSON><PERSON>", "telemetry.event.optional": "%s (Opsional)", "telemetry.event.optional.disabled": "%s (Opsional) - <PERSON><PERSON><PERSON><PERSON>", "telemetry.event.performance_metrics.description": "Mengetahui ikhtisar kinerja Minecraft secara keseluruhan membantu kami dalam menyetel dan mengoptimalkan permainan untuk berbagai spesifikasi mesin dan sistem operasi. Versi permainan disertakan untuk membantu kami dalam membandingkan ikhtisar kinerja dari versi baru Minecraft.", "telemetry.event.performance_metrics.title": "<PERSON><PERSON>", "telemetry.event.required": "%s (Diperlukan)", "telemetry.event.world_load_times.description": "Penting bagi kami untuk memahami berapa lama waktu yang diperlukan untuk bergabung ke sebuah dunia, dan bagaimana hal itu berubah dari waktu ke waktu. <PERSON><PERSON><PERSON>, saat kami menambahkan fitur baru atau melakukan perubahan teknis yang lebih besar, kami perlu melihat pengaruhnya terhadap waktu memuat.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "Mengetahui cara pemain bermain Minecraft (seperti Mode Permainan, klien atau peladen modifikasi, dan versi permainan) memungkinkan kami memfokuskan pembaruan permainan untuk meningkatkan area yang paling diperhatikan pemain.\nPeristiwa Dunia Dimuat dipasangkan dengan peristiwa Dunia Ditutup untuk menghitung berapa lama sesi bermain telah berlangsung.", "telemetry.event.world_loaded.title": "Dunia Dimuat", "telemetry.event.world_unloaded.description": "Peristiwa ini dipasangkan dengan peristiwa Dunia Dimuat untuk menghitung berapa lama sesi dunia telah berlangsung.\n<PERSON><PERSON><PERSON> (dalam detik dan tik) diukur ketika sesi dunia telah berakhir (kembali ke judul, memutuskan sambungan dari peladen).", "telemetry.event.world_unloaded.title": "Dunia Ditutup", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON> (Tik)", "telemetry.property.advancement_id.title": "<PERSON><PERSON><PERSON>", "telemetry.property.client_id.title": "Pengenal Klien", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON><PERSON> (kB)", "telemetry.property.event_timestamp_utc.title": "Stempel Waktu <PERSON> (UTC)", "telemetry.property.frame_rate_samples.title": "Sampel Laju Bingkai (BPD)", "telemetry.property.game_mode.title": "Mode Permainan", "telemetry.property.game_version.title": "<PERSON><PERSON><PERSON>", "telemetry.property.launcher_name.title": "<PERSON><PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "<PERSON><PERSON><PERSON> (Milidetik)", "telemetry.property.load_time_loading_overlay_ms.title": "<PERSON><PERSON><PERSON> (Milidetik)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON><PERSON><PERSON> Jendela Terbuka (Milidetik)", "telemetry.property.load_time_total_time_ms.title": "<PERSON><PERSON><PERSON> (Milidetik)", "telemetry.property.minecraft_session_id.title": "Pengenal Sesi Minecraft", "telemetry.property.new_world.title": "<PERSON><PERSON>", "telemetry.property.number_of_samples.title": "<PERSON><PERSON><PERSON>", "telemetry.property.operating_system.title": "Sistem Operasi", "telemetry.property.opt_in.title": "Keikutsertaan", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Konten Peta Realms (Nama Permainan Kecil)", "telemetry.property.render_distance.title": "Jarak <PERSON>", "telemetry.property.render_time_samples.title": "Sampel Waktu Render", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON><PERSON> (Detik)", "telemetry.property.server_modded.title": "Peladen Modifikasi", "telemetry.property.server_type.title": "<PERSON><PERSON>", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON><PERSON> (Tik)", "telemetry.property.used_memory_samples.title": "Memor<PERSON> Akses A<PERSON> yang Terpakai", "telemetry.property.user_id.title": "Pengenal Pengguna", "telemetry.property.world_load_time_ms.title": "Waktu Pemuatan Dunia (Milidetik)", "telemetry.property.world_session_id.title": "Pengenal Sesi Dunia", "telemetry_info.button.give_feedback": "<PERSON><PERSON>", "telemetry_info.button.privacy_statement": "<PERSON><PERSON><PERSON><PERSON>", "telemetry_info.button.show_data": "<PERSON><PERSON>", "telemetry_info.opt_in.description": "Saya setuju untuk mengirimkan data telemetri opsional", "telemetry_info.property_title": "Data yang Disertakan", "telemetry_info.screen.description": "Mengumpulkan data ini membantu kami meningkatkan Minecraft dengan memandu kami ke arah yang relevan dengan pemain kami.\nAnda juga dapat mengirimkan umpan balik tambahan untuk membantu kami terus meningkatkan Minecraft.", "telemetry_info.screen.title": "Pengumpulan Data Telemetri", "test.error.block_property_mismatch": "Mengharapkan sifat %s sebagai %s, didapati %s", "test.error.block_property_missing": "Sifat balok hilang, sifat %s diharapkan %s", "test.error.entity_property": "Entitas %s gagal uji: %s", "test.error.entity_property_details": "Entitas %s gagal uji: %s, diharap<PERSON>: %s, did<PERSON><PERSON>: %s", "test.error.expected_block": "Mengharapkan balok %s, mendapatkan %s", "test.error.expected_block_tag": "Mengharapkan balok dalam #%s, mendapatkan %s", "test.error.expected_container_contents": "Wadah se<PERSON>nya mengandung: %s", "test.error.expected_container_contents_single": "Wadah se<PERSON>nya mengandung satu: %s", "test.error.expected_empty_container": "<PERSON><PERSON>h <PERSON> kosong", "test.error.expected_entity": "Mengharapkan %s", "test.error.expected_entity_around": "Mengharapkan %s ada di sekitar %s, %s, %s", "test.error.expected_entity_count": "Mengharapkan %s entitas jenis %s, ditemukan %s", "test.error.expected_entity_data": "Mengharapkan data entitas sebagai: %s, didapati: %s", "test.error.expected_entity_data_predicate": "Data entitas tidak sama untuk %s", "test.error.expected_entity_effect": "Mengharapkan %s memiliki efek %s %s", "test.error.expected_entity_having": "Persediaan entitas seharusnya mengandung %s", "test.error.expected_entity_holding": "Entitas seharusnya memegang %s", "test.error.expected_entity_in_test": "%s diharapkan ada pada uji", "test.error.expected_entity_not_touching": "%s tidak diharapkan menyentuh %s, %s, %s (relatif: %s, %s, %s)", "test.error.expected_entity_touching": "%s diharapkan menyentuh %s, %s, %s (relatif: %s, %s, %s)", "test.error.expected_item": "Mengharapkan benda jenis %s", "test.error.expected_items_count": "Mengharapkan %s benda jenis %s, ditemukan %s", "test.error.fail": "Persyaratan kegagalan dipenuhi", "test.error.invalid_block_type": "Men<PERSON>ukan jenis balok yang tidak diharapkan: %s", "test.error.missing_block_entity": "Entitas balok hilang", "test.error.position": "%s pada %s, %s, %s (relatif: %s, %s, %s) pada tik %s", "test.error.sequence.condition_already_triggered": "Persyaratan telah dipicu pada %s", "test.error.sequence.condition_not_triggered": "Persyaratan tidak dipicu", "test.error.sequence.invalid_tick": "Be<PERSON><PERSON><PERSON> dalam tik tidak sah: mengharapkan %s", "test.error.sequence.not_completed": "<PERSON><PERSON><PERSON> uji habis sebelum urutan selesai", "test.error.set_biome": "Gagal mengatur bioma untuk uji", "test.error.spawn_failure": "Gagal membuat entitas %s", "test.error.state_not_equal": "<PERSON><PERSON>an tidak benar. Mengharapkan %s, didapati %s", "test.error.structure.failure": "Gagal menempatkan struktur uji untuk %s", "test.error.tick": "%s pada tik %s", "test.error.ticking_without_structure": "Uji berdetik sebelum menempatkan struktur", "test.error.timeout.no_result": "Tidak berhasil atau gagal dalam %s tik", "test.error.timeout.no_sequences_finished": "Tidak ada urutan yang selesai dalam %s tik", "test.error.too_many_entities": "Mengharapkan hanya satu %s ada di sekitar %s, %s, %s, tetapi ditemukan %s", "test.error.unexpected_block": "Tidak mengharapkan balok berupa %s", "test.error.unexpected_entity": "%s tidak diharapkan ada", "test.error.unexpected_item": "Tidak mengharapkan benda jenis %s", "test.error.unknown": "Kesalahan internal tidak diketahui: %s", "test.error.value_not_equal": "Mengharapkan %s sebagai %s, didapati %s", "test.error.wrong_block_entity": "Jenis entitas balok salah: %s", "test_block.error.missing": "Balok %s hilang pada struktur uji", "test_block.error.too_many": "Terlalu banyak balok %s", "test_block.invalid_timeout": "<PERSON><PERSON><PERSON> tunggu (%s) tidak sah - jumlah tik harus positif", "test_block.message": "Pesan:", "test_block.mode.accept": "Terima", "test_block.mode.fail": "Gaga<PERSON>", "test_block.mode.log": "Log", "test_block.mode.start": "<PERSON><PERSON>", "test_block.mode_info.accept": "Mode Terima - <PERSON><PERSON><PERSON> k<PERSON> un<PERSON> (sebagian dari) uji", "test_block.mode_info.fail": "Mode Gagal - <PERSON><PERSON><PERSON><PERSON><PERSON> uji", "test_block.mode_info.log": "Mode Log - Mencatat pesan", "test_block.mode_info.start": "Mode Mulai - <PERSON><PERSON><PERSON> awal uji", "test_instance.action.reset": "<PERSON>ur ulang dan muat", "test_instance.action.run": "Muat dan jalankan", "test_instance.action.save": "<PERSON>mp<PERSON> struktur", "test_instance.description.batch": "Tumpak: %s", "test_instance.description.failed": "Gagal: %s", "test_instance.description.function": "Fungsi: %s", "test_instance.description.invalid_id": "Pengenal uji tidak sah", "test_instance.description.no_test": "Uji tersebut tidak ada", "test_instance.description.structure": "Struktur: %s", "test_instance.description.type": "Jenis: %s", "test_instance.type.block_based": "Uji Berbasis Balok", "test_instance.type.function": "<PERSON><PERSON>", "test_instance_block.entities": "Entitas:", "test_instance_block.error.no_test": "Tidak dapat menjalankan peristiwa uji pada %s, %s, %s sebab ada uji yang tidak terdefinisi", "test_instance_block.error.no_test_structure": "Tidak dapat menjalankan peristiwa uji pada %s, %s, %s sebab tidak ada struktur uji", "test_instance_block.error.unable_to_save": "Tidak dapat menyimpan pola acu struktur uji untuk peristiwa uji pada %s, %s, %s", "test_instance_block.invalid": "[tidak sah]", "test_instance_block.reset_success": "<PERSON><PERSON><PERSON><PERSON> mengatur ulang uji: %s", "test_instance_block.rotation": "Rotasi:", "test_instance_block.size": "Ukuran Struktur Uji", "test_instance_block.starting": "<PERSON><PERSON><PERSON> uji %s", "test_instance_block.test_id": "Pengen<PERSON><PERSON>", "title.32bit.deprecation": "Sistem 32-bit terdeteksi: ini dapat mencegah Anda bermain di masa mendatang karena sistem 64-bit akan diperlukan!", "title.32bit.deprecation.realms": "Minecraft akan segera mewajibkan sistem 64-bit, yang akan mencegah Anda bermain atau menggunakan Realms pada perangkat ini. Anda harus membatalkan semua berlangganan Realms secara manual.", "title.32bit.deprecation.realms.check": "<PERSON>an tampilkan layar ini lagi", "title.32bit.deprecation.realms.header": "Sistem 32-bit terdeteksi", "title.credits": "Hak cipta Mojang AB. Jang<PERSON> disebarkan!", "title.multiplayer.disabled": "<PERSON><PERSON><PERSON>. Harap periksa pengaturan akun Microsoft Anda.", "title.multiplayer.disabled.banned.name": "Anda harus mengubah nama Anda sebelum Anda dapat bermain daring", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON><PERSON>a ditangguhkan selamanya dari permainan daring", "title.multiplayer.disabled.banned.temporary": "<PERSON><PERSON><PERSON>a ditangguhkan sementara dari permainan daring", "title.multiplayer.lan": "<PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON> (Peladen Pihak <PERSON>)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "<PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Awalan, %s%2$s kembali %s dan %1$s terakhir %s dan juga %1$s lagi!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hai %", "translation.test.invalid2": "hai %s", "translation.test.none": "Halo, dunia!", "translation.test.world": "dunia", "trim_material.minecraft.amethyst": "<PERSON>han <PERSON>", "trim_material.minecraft.copper": "<PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON>", "trim_material.minecraft.redstone": "Bahan Redstone", "trim_material.minecraft.resin": "<PERSON><PERSON>", "trim_pattern.minecraft.bolt": "<PERSON><PERSON>", "trim_pattern.minecraft.coast": "<PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON>", "trim_pattern.minecraft.flow": "<PERSON><PERSON>", "trim_pattern.minecraft.host": "<PERSON><PERSON>", "trim_pattern.minecraft.raiser": "<PERSON><PERSON>", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "<PERSON><PERSON>", "trim_pattern.minecraft.shaper": "<PERSON><PERSON>", "trim_pattern.minecraft.silence": "<PERSON><PERSON>", "trim_pattern.minecraft.snout": "<PERSON><PERSON>", "trim_pattern.minecraft.spire": "<PERSON><PERSON>", "trim_pattern.minecraft.tide": "<PERSON><PERSON>", "trim_pattern.minecraft.vex": "<PERSON><PERSON>", "trim_pattern.minecraft.ward": "<PERSON><PERSON>", "trim_pattern.minecraft.wayfinder": "<PERSON><PERSON>", "trim_pattern.minecraft.wild": "<PERSON><PERSON>", "tutorial.bundleInsert.description": "Klik Kanan untuk menambahkan benda", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON>", "tutorial.craft_planks.description": "<PERSON><PERSON> resep dapat membantu", "tutorial.craft_planks.title": "Buat papan kayu", "tutorial.find_tree.description": "<PERSON><PERSON>l untuk mendapatkan kayu", "tutorial.find_tree.title": "Cari satu pohon", "tutorial.look.description": "Gunakan tetikus Anda untuk berbelok", "tutorial.look.title": "<PERSON><PERSON>", "tutorial.move.description": "Lompat dengan %s", "tutorial.move.title": "Gerak dengan %s, %s, %s, dan %s", "tutorial.open_inventory.description": "Tekan %s", "tutorial.open_inventory.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "tutorial.punch_tree.description": "Tahan %s", "tutorial.punch_tree.title": "Hancurkan pohon", "tutorial.socialInteractions.description": "Tekan %s untuk membuka", "tutorial.socialInteractions.title": "Interaksi Sosial", "upgrade.minecraft.netherite_upgrade": "Peningkatan Netherit"}