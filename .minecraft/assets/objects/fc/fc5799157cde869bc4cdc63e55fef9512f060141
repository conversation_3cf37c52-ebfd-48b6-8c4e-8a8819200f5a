{"accessibility.onboarding.accessibility.button": "Configuracion d'Accessibilitat...", "accessibility.onboarding.screen.narrator": "Picatz Entrar per activar lo narrador", "accessibility.onboarding.screen.title": "Benvengut a Minecraft!\n\nVòles activar lo Narrador o veire la Configuracion d'Accessibilitat?", "addServer.add": "Acabat", "addServer.enterIp": "<PERSON><PERSON><PERSON> dau servidor", "addServer.enterName": "Nom dau servidor", "addServer.resourcePack": "Paquets de ressorsa dau servidor", "addServer.resourcePack.disabled": "Desactivat", "addServer.resourcePack.enabled": "Activat", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Modificar las informacions dau servidor", "advMode.command": "Comanda de la Consòla", "advMode.mode": "Mòde", "advMode.mode.auto": "Repeticion", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.conditional": "Condicionau", "advMode.mode.redstone": "Impulsion", "advMode.mode.redstoneTriggered": "Demanda de redstone", "advMode.mode.sequence": "<PERSON><PERSON>", "advMode.mode.unconditional": "Incondicionau", "advMode.notAllowed": "Vos cau èsser un operator en mòde creatiu", "advMode.notEnabled": "Los blòcs de comandas son pas activats sus aquel servidor", "advMode.previousOutput": "Sortida <PERSON>a", "advMode.setCommand": "Definir una comanda de la consòla pel blòc", "advMode.setCommand.success": "Comanda definida: %s", "advMode.trackOutput": "Afichar lo resultat", "advMode.triggering": "Activacion", "advMode.type": "Tipe", "advancement.advancementNotFound": "Progrès desconegut: '%s'", "advancements.adventure.adventuring_time.description": "Descobrir totes los biòmas", "advancements.adventure.adventuring_time.title": "Cap a l'aventura", "advancements.adventure.arbalistic.description": "Aucís cinc creaturas amb un sol tiri de suspension", "advancements.adventure.arbalistic.title": "Arcbalestaca", "advancements.adventure.avoid_vibration.description": "S'escond près d'un Captaire Sculk o Warden per l'empachar de vos detectar", "advancements.adventure.avoid_vibration.title": "Sens un bruch", "advancements.adventure.blowback.description": "Aucís un ventolet amb siá pròpria carga de vent.", "advancements.adventure.blowback.title": "Retorn de flama", "advancements.adventure.brush_armadillo.description": "Brossatz un tató per obténer de placas.", "advancements.adventure.brush_armadillo.title": "Placat de placas", "advancements.adventure.bullseye.description": "Tirar una sageta al centre d'una buta a almens 30 mètres de distància", "advancements.adventure.bullseye.title": "Al centre", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fasètz un vas ondrat ambe 4 tèstes de terralha", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restau<PERSON><PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Siatz près d'un fabricator quand lo fabricator fabrica un fabricator", "advancements.adventure.crafters_crafting_crafters.title": "Fabricators fabricant de fabricators", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON><PERSON>i lo bèc del mond (limit de blòcs) fins al fons del mond e subreviu", "advancements.adventure.fall_from_world_height.title": "Baumas e bauces", "advancements.adventure.heart_transplanter.description": "Plaçatz un còr de cruisseire amb lo bon alinhament entre dos buscas d'euse palle", "advancements.adventure.heart_transplanter.title": "Transplantacion cardiaca", "advancements.adventure.hero_of_the_village.description": "Protegissètz un vilatge d'una invasion", "advancements.adventure.hero_of_the_village.title": "Eròi del vilatge", "advancements.adventure.honey_block_slide.description": "Sauta en un blòc de mèu per facilitar ta casuda", "advancements.adventure.honey_block_slide.title": "Situacion pegosa", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON><PERSON> quin monstre que siá", "advancements.adventure.kill_a_mob.title": "Caçaire de mostres", "advancements.adventure.kill_all_mobs.description": "<PERSON><PERSON>ís <PERSON> de cada monstre", "advancements.adventure.kill_all_mobs.title": "Caçaire de monstres", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Aucís una creatura près d'un catalisaire de Sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "S'Espandís", "advancements.adventure.lighten_up.description": "Desruscatz una ampola en coire amb una pigassa per la far venir mai lusenta.", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON> br<PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Aparatz un vilatgés del pericle sens que res s'embrase", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Nauta tension", "advancements.adventure.minecraft_trials_edition.description": "Entratz un membre d'espròva.", "advancements.adventure.minecraft_trials_edition.title": "Mesa a l'espròva", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> amb <PERSON>", "advancements.adventure.ol_betsy.title": "La vièlha Bèrta", "advancements.adventure.overoverkill.description": "Infligissètz 50 còrs de degalhs en un sol còp de maça", "advancements.adventure.overoverkill.title": "<PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Fasètz que las pradariás viscan amb lo bruch de la musica d'un tocadisque.", "advancements.adventure.play_jukebox_in_meadows.title": "El bruch de la musica", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Captatz lo senhal de poténcia d'una bibliotèca escultada en utilizant un comparator", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Lo poder dels libres", "advancements.adventure.revaulting.description": "Desverrolha un còfrefòrt funèst amb un clau d'espròvas funèst.", "advancements.adventure.revaulting.title": "Còfrefòrt plan fòrt", "advancements.adventure.root.description": "Aventura, exploracion e combat", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Brossatz un blòc suspècte per obténer un tèst de terralha", "advancements.adventure.salvage_sherd.title": "Respecta las rèstas", "advancements.adventure.shoot_arrow.description": "Tirar sus quicòm amb una sageta", "advancements.adventure.shoot_arrow.title": "<PERSON>s la linha de mira", "advancements.adventure.sleep_in_bed.description": "Dormissetz dins un lièch per cambiar lo vòstre punt de reaparicion", "advancements.adventure.sleep_in_bed.title": "De polits sòmis", "advancements.adventure.sniper_duel.description": "Aucís una esqueleta a mai de 50 mètres luènh", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON> de snipers", "advancements.adventure.spyglass_at_dragon.description": "Espiatz lo Dragon de l'End a travèrs una pòrtavista", "advancements.adventure.spyglass_at_dragon.title": "Es un avion?", "advancements.adventure.spyglass_at_ghast.description": "Espiatz un Ghast a travèrs una pòrtavista", "advancements.adventure.spyglass_at_ghast.title": "Es un balon ?", "advancements.adventure.spyglass_at_parrot.description": "Espiatz un papagai a travèrs una pòrtavista", "advancements.adventure.spyglass_at_parrot.title": "Es un aucèl?", "advancements.adventure.summon_iron_golem.description": "Invocatz un golem de fèrre per aparar un vilatge", "advancements.adventure.summon_iron_golem.title": "Agent de securitat", "advancements.adventure.throw_trident.description": "Lançatz un trident a quicòm.\nNòta : Lançar vòstra unica arma es pas una bona idèa.", "advancements.adventure.throw_trident.title": "Una galejada plan lançada", "advancements.adventure.totem_of_undying.description": "Utilizatz un totèm d'immortalitat per escapar a la mòrt.", "advancements.adventure.totem_of_undying.title": "A l’article de la mòrt", "advancements.adventure.trade.description": "Mercandejatz amb un vilatgés", "advancements.adventure.trade.title": "Quin afar !", "advancements.adventure.trade_at_world_height.description": "Mercandejatz amb un vilatgés al limit de blòcs.", "advancements.adventure.trade_at_world_height.title": "Mercad<PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplicatz aquelas ondraduras almens un còp : de las torres, dels morres, de las còstelas, dels abisses, del silenci, dels vex, de las marèas e dels correires.", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Un fòrt faure", "advancements.adventure.trim_with_any_armor_pattern.description": "Ondratz una armadura sus una taula de fargatge.", "advancements.adventure.trim_with_any_armor_pattern.title": "Faure en aparéncia", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON><PERSON> dos fantaumas amb una sageta perforanta", "advancements.adventure.two_birds_one_arrow.title": "D'una sageta dos còps", "advancements.adventure.under_lock_and_key.description": "Desverrolha un còfrefòrt amb un clau d'espròvas.", "advancements.adventure.under_lock_and_key.title": "La clau del succès", "advancements.adventure.use_lodestone.description": "Utilizatz una bossòla sus una magnetita", "advancements.adventure.use_lodestone.title": "M’èm a casa, magnetita del país", "advancements.adventure.very_very_frightening.description": "Tustatz un vilatgés amb lo fólzer", "advancements.adventure.very_very_frightening.title": "<PERSON>òrça fò<PERSON>", "advancements.adventure.voluntary_exile.description": "Tuatz un capitani de pilhards.\n<PERSON><PERSON> vau restar alunhat daus vilatges pel moment...", "advancements.adventure.voluntary_exile.title": "Exilh volontari", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Caminatz sus de nèu polverosa... sens s'i afonsar", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Leugièr com un conilh", "advancements.adventure.who_needs_rockets.description": "Utilizatz una carga de vent per vos propulsar a 8 blòcs de nautor", "advancements.adventure.who_needs_rockets.title": "Qui a besonh de fusada?", "advancements.adventure.whos_the_pillager_now.description": "Tornar a un pilhard la moneda de la sieuna peça", "advancements.adventure.whos_the_pillager_now.title": "Es qui lo pilhard ara ?", "advancements.empty": "Sembla i aver pas res aquí...", "advancements.end.dragon_breath.description": "Re<PERSON>l<PERSON><PERSON><PERSON><PERSON> lo buf de Dragon dins una fiòla", "advancements.end.dragon_breath.title": "Sens alen", "advancements.end.dragon_egg.description": "Ten la uòu del dragon", "advancements.end.dragon_egg.title": "La nòva generacion", "advancements.end.elytra.description": "Trobatz d'Elitres", "advancements.end.elytra.title": "Cap a l'infinit e en delà", "advancements.end.enter_end_gateway.description": "Escapar de l'isla", "advancements.end.enter_end_gateway.title": "Escapada a distància", "advancements.end.find_end_city.description": "Vas al dedins, que pòt se passar ?", "advancements.end.find_end_city.title": "La vila a la fin dau juòc", "advancements.end.kill_dragon.description": "<PERSON>", "advancements.end.kill_dragon.title": "Des<PERSON>uratz l'End", "advancements.end.levitate.description": "Levitatz a un nautor de 50 blòcs après l'ataca d'un shulker.", "advancements.end.levitate.title": "Mirador <PERSON>", "advancements.end.respawn_dragon.description": "Fasètz reaparéisser lo Dragon de l'End.", "advancements.end.respawn_dragon.title": "La fin... Encara...", "advancements.end.root.description": "O lo començament?", "advancements.end.root.title": "La fin", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fasetz en sòrta qu'un allay escampa un pastís sus un blòc musicau", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Cançon d'anniversari", "advancements.husbandry.allay_deliver_item_to_player.description": "Recebètz un objècte portat per un Allay.", "advancements.husbandry.allay_deliver_item_to_player.title": "Ieu soi lo tieu amic", "advancements.husbandry.axolotl_in_a_bucket.description": "Capturatz un axolòtl dins un ferrat", "advancements.husbandry.axolotl_in_a_bucket.title": "Lo predador lo mai mistoflet", "advancements.husbandry.balanced_diet.description": "Manjatz tot çò que se pòt manjar, quitament çò qu'es pas bon per vos", "advancements.husbandry.balanced_diet.title": "Una alimentacion equilibrada", "advancements.husbandry.breed_all_animals.description": "Fasètz se reprodusir tot los animals almens un còp", "advancements.husbandry.breed_all_animals.title": "Dos per dos", "advancements.husbandry.breed_an_animal.description": "Fasètz se reprodusir dos animals ensem", "advancements.husbandry.breed_an_animal.title": "I a de l'amor dins l'aire", "advancements.husbandry.complete_catalogue.description": "Amansissètz totas las variantas de cats !", "advancements.husbandry.complete_catalogue.title": "Un catalòg complèt", "advancements.husbandry.feed_snifflet.description": "Apasturatz un niflaire", "advancements.husbandry.feed_snifflet.title": "Pichòtas niflas", "advancements.husbandry.fishy_business.description": "Aganta un peis", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON> pel peis", "advancements.husbandry.froglights.description": "Obt<PERSON><PERSON><PERSON> totes los tipes de granolum dins vòstre inventari", "advancements.husbandry.froglights.title": "Amb los nòstres poders combinats!", "advancements.husbandry.kill_axolotl_target.description": "Fasètz còla amb un axolòtl e ganhatz un combat", "advancements.husbandry.kill_axolotl_target.title": "L'union que da fòrça !", "advancements.husbandry.leash_all_frog_variants.description": "Tenètz cada varianta de granolha en laç", "advancements.husbandry.leash_all_frog_variants.title": "Las tres mosquetièras", "advancements.husbandry.make_a_sign_glow.description": "<PERSON><PERSON><PERSON> lo tèxt de qualque pancarta lusent", "advancements.husbandry.make_a_sign_glow.title": "Una idèa lusenta !", "advancements.husbandry.netherite_hoe.description": "Apondètz un lingòt de netherita sus un bigòs, e puèi avaloratz vòstras causidas de vida.", "advancements.husbandry.netherite_hoe.title": "Devocion seriosa", "advancements.husbandry.obtain_sniffer_egg.description": "Obtenètz un uòu de niflaire", "advancements.husbandry.obtain_sniffer_egg.title": "Descobèrta olfactiva", "advancements.husbandry.place_dried_ghast_in_water.description": "Plaçatz un Ghast secat dins l'aiga", "advancements.husbandry.place_dried_ghast_in_water.title": "Demoratz idratat !", "advancements.husbandry.plant_any_sniffer_seed.description": "Plantatz una grana trobada per un niflaire", "advancements.husbandry.plant_any_sniffer_seed.title": "Plantant lo passat", "advancements.husbandry.plant_seed.description": "Plantatz una grana e véser-la créisser.", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON> tan rapidament...", "advancements.husbandry.remove_wolf_armor.description": "Quitatz l'armadura d'un lop amb de cisalhas.", "advancements.husbandry.remove_wolf_armor.title": "Un bèl pel", "advancements.husbandry.repair_wolf_armor.description": "Reparatz cap e tot una armadura per lop damatjada amb placas de tató.", "advancements.husbandry.repair_wolf_armor.title": "Coma nòu", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Montatz en batèl e navigatz amb la vòstra cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Capitani cabra !", "advancements.husbandry.root.description": "Lo mond es plen d'amics e de noiridura", "advancements.husbandry.root.title": "Agricultura", "advancements.husbandry.safely_harvest_honey.description": "Utilizatz un fuòc de camp per reculhir lo mèu d'un nis en utilizant una fiòla sens provocar las abelhas", "advancements.husbandry.safely_harvest_honey.title": "<PERSON>ás mèu convidat", "advancements.husbandry.silk_touch_nest.description": "<PERSON>da<PERSON> un Nis o un bornhon, amb 3 a<PERSON><PERSON> de<PERSON>, en utilizant <PERSON><PERSON> de seda.", "advancements.husbandry.silk_touch_nest.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tactical_fishing.description": "Pescatz un peis... sens cana de pesca !", "advancements.husbandry.tactical_fishing.title": "Pesca tactica", "advancements.husbandry.tadpole_in_a_bucket.description": "Capturatz un capmartèu dins un ferrat", "advancements.husbandry.tadpole_in_a_bucket.title": "La p'tita granolha", "advancements.husbandry.tame_an_animal.description": "Amansissètz un animal", "advancements.husbandry.tame_an_animal.title": "Melhors amics per la vida", "advancements.husbandry.wax_off.description": "Raspatz la bresca d'un blòc de coire !", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Enceratz un blòc de coire amb de bresca !", "advancements.husbandry.wax_on.title": "Lustrar", "advancements.husbandry.whole_pack.description": "Amansissètz totas las variantas de lòp.", "advancements.husbandry.whole_pack.title": "Cap de la canhada", "advancements.nether.all_effects.description": "<PERSON><PERSON><PERSON> totes los efièches a l'encòp", "advancements.nether.all_effects.title": "Cossí sèm arribatz aicí?", "advancements.nether.all_potions.description": "<PERSON><PERSON><PERSON> totes los efièches de pocion a l'encòp", "advancements.nether.all_potions.title": "Mesclas perilhoses", "advancements.nether.brew_potion.description": "Preparatz una pocion", "advancements.nether.brew_potion.title": "Distillariá locala", "advancements.nether.charge_respawn_anchor.description": "Cargatz una acora de reaparicion al sieu maximum", "advancements.nether.charge_respawn_anchor.title": "Pas d'a fons \"nòu\" vidas", "advancements.nether.create_beacon.description": "Construsètz e plaçatz una balisa", "advancements.nether.create_beacon.title": "Fai la tiá balisa", "advancements.nether.create_full_beacon.description": "Auçatz una balisa al sieu plen poder.", "advancements.nether.create_full_beacon.title": "Balisaire", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> de piglins amb d'aur", "advancements.nether.distract_piglin.title": "Mon tresaur...", "advancements.nether.explore_nether.description": "Des<PERSON><PERSON>r totes los biòmas del Nether.", "advancements.nether.explore_nether.title": "Viatge au cap de l'infèrn", "advancements.nether.fast_travel.description": "Viatjatz sus 7 quilomètres a la susfàcia pel Nether", "advancements.nether.fast_travel.title": "Botiòla sos-espaciau", "advancements.nether.find_bastion.description": "Entratz dins las roïnas d'un bastion", "advancements.nether.find_bastion.title": "Los bels jorns", "advancements.nether.find_fortress.description": "Descobrissètz una fortalesa del Nether", "advancements.nether.find_fortress.title": "Una terribla fortalesa", "advancements.nether.get_wither_skull.description": "Obtenètz un cran d'esqueleta Wither.", "advancements.nether.get_wither_skull.title": "Esglasianta, espantosa esqueleta", "advancements.nether.loot_bastion.description": "Pilhatz un còfre dins las roïnas d'un bastion", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Obtenètz una armadura en netherita complèta.", "advancements.nether.netherite_armor.title": "Cobrissètz me de brigalhs", "advancements.nether.obtain_ancient_debris.description": "Obtenètz de brigalhs antics", "advancements.nether.obtain_ancient_debris.title": "Amagat dins las prigondors", "advancements.nether.obtain_blaze_rod.description": "Recuperatz un baston de Blaze", "advancements.nether.obtain_blaze_rod.title": "Dins las flamas de l'infèrn", "advancements.nether.obtain_crying_obsidian.description": "Obtenètz de obsidiana plorosa", "advancements.nether.obtain_crying_obsidian.title": "Qual copa de ceba ?", "advancements.nether.return_to_sender.description": "Tuatz un ghast amb una pelòta de fuòc", "advancements.nether.return_to_sender.title": "Retorn au mandaire", "advancements.nether.ride_strider.description": "Cavaucatz e dirigissètz un arpentaire amb un fonge bescornut sus un baston", "advancements.nether.ride_strider.title": "<PERSON><PERSON>e bat<PERSON> a de patas", "advancements.nether.ride_strider_in_overworld_lava.description": "Prenetz un arpentaire far una loooonga passejada sus un lac de lava a la susfàcia.", "advancements.nether.ride_strider_in_overworld_lava.title": "Com a casa", "advancements.nether.root.description": "Benvengut a l'infèrn", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Fasètz aparéisser lo <PERSON>", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Prenètz un ghast del Nether, tornatz-o en tota securitat a la Susfacia... e tuatz-o", "advancements.nether.uneasy_alliance.title": "Aliança instabla", "advancements.nether.use_lodestone.description": "Utilizatz una bossòla sus una magnetita", "advancements.nether.use_lodestone.title": "M’èm a casa, magnetita del país", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Aflaquissètz e sonhatz un zòmbi vilatgés.", "advancements.story.cure_zombie_villager.title": "Zòmbilòg", "advancements.story.deflect_arrow.description": "Paratz un projectil amb un bloquièr.", "advancements.story.deflect_arrow.title": "<PERSON><PERSON>, merc<PERSON>", "advancements.story.enchant_item.description": "Encantar un objècte dins una taula d'encantament", "advancements.story.enchant_item.title": "Emmascaire", "advancements.story.enter_the_end.description": "Dintratz dins lo portau de l'End", "advancements.story.enter_the_end.title": "La fin ?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, al<PERSON><PERSON> e dintratz dins un portau dau Nether", "advancements.story.enter_the_nether.title": "<PERSON><PERSON> con<PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> un uèlh d'Ender", "advancements.story.follow_ender_eye.title": "Se<PERSON><PERSON><PERSON>...", "advancements.story.form_obsidian.description": "Obtenètz de obsidiana", "advancements.story.form_obsidian.title": "Menta fresca", "advancements.story.iron_tools.description": "Fasètz una trenca en fèrre.", "advancements.story.iron_tools.title": "<PERSON><PERSON><PERSON><PERSON>'s pas una trenca de fèrre", "advancements.story.lava_bucket.description": "Emplissètz un ferrat de lava", "advancements.story.lava_bucket.title": "Causa cauda", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "Diamants!", "advancements.story.mine_stone.description": "Minatz de ròca amb vòstra nòva trenca.", "advancements.story.mine_stone.title": "Edat de la Pèira", "advancements.story.obtain_armor.description": "Fasètz una pèça d'armadura en fèrre.", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON>'<PERSON>", "advancements.story.root.description": "Lo còr e l'istòria del jòc", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "L'armadura en diamant sauva de vidas", "advancements.story.shiny_gear.title": "Cobrissètz-me de diamants", "advancements.story.smelt_iron.description": "Fasètz fondre de fèrre", "advancements.story.smelt_iron.title": "L'edat de fèrre", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON> una trenca melhora", "advancements.story.upgrade_tools.title": "Qualitat superiora", "advancements.toast.challenge": "Desfís Co<PERSON>tat!", "advancements.toast.goal": "Tòca Atenguda!", "advancements.toast.task": "Progrès realizat!", "argument.anchor.invalid": "Posicion d'ancoratge del entitat pas valida : %s", "argument.angle.incomplete": "Incomplet (1 angle esperat)", "argument.angle.invalid": "<PERSON><PERSON> pas valid", "argument.block.id.invalid": "Tipe de blòc desconegut '%s'", "argument.block.property.duplicate": "La proprietat \"%s\" pòt èsser definida sonque un còp pel bloc %s", "argument.block.property.invalid": "Lo bloc %s accepta pas \"%s\" per la proprietat %s", "argument.block.property.novalue": "Valor esperada per la proprietat \"%s\" sus lo bloc %s", "argument.block.property.unclosed": "Un \"]\" de barradura es esperat per las proprietats d'estat del bloc", "argument.block.property.unknown": "El bloc %s a pas la proprietat \"%s\"", "argument.block.tag.disallowed": "Los tags son pas autorizats aicí, solament de vertadièrs blocs", "argument.color.invalid": "Color desconeguda '%s'", "argument.component.invalid": "Compousant de chat pas valida: %s", "argument.criteria.invalid": "C<PERSON><PERSON>ri descon<PERSON>ut '%s'", "argument.dimension.invalid": "Dimension desconeguda '%s'", "argument.double.big": "Un double deu pas èsser superior a %s, valor trobada: %s", "argument.double.low": "Un double deu pas èsser inferior a %s, valor trobada: %s", "argument.entity.invalid": "Nom o UUID pas valid", "argument.entity.notfound.entity": "Pas cap de entitat trobada", "argument.entity.notfound.player": "Cap de jogaire pas trobat", "argument.entity.options.advancements.description": "Jo<PERSON>res amb dels progrèsses", "argument.entity.options.distance.description": "Distància fins a l'entitat", "argument.entity.options.distance.negative": "La distancia pòt pas èsser negativa", "argument.entity.options.dx.description": "Entitas entre x e x + dx", "argument.entity.options.dy.description": "Entitas entre y e y + dy", "argument.entity.options.dz.description": "Entitas entre z e z + dz", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON> dins un mòde de juòc", "argument.entity.options.inapplicable": "Causida '%s' es inaplicable aicí", "argument.entity.options.level.description": "Nivèl d'experiéncia", "argument.entity.options.level.negative": "Lo nivèl d'experiéncia pòt pas èsser negatiu", "argument.entity.options.limit.description": "Nombre d'entitats maximal a retornar", "argument.entity.options.limit.toosmall": "Lo limit deu èsser d'almens 1", "argument.entity.options.mode.invalid": "Mòde de juòc desconegut o pas valid: %s", "argument.entity.options.name.description": "Nom de l'entitat", "argument.entity.options.nbt.description": "Entitats amb NBT", "argument.entity.options.predicate.description": "Predicat personalitzat", "argument.entity.options.scores.description": "Entitat amb marca", "argument.entity.options.sort.description": "Triar las entitats", "argument.entity.options.sort.irreversible": "Tipe de tri desconegut o pas valid: %s", "argument.entity.options.tag.description": "Entitats amb tag", "argument.entity.options.team.description": "Entitats en una còla", "argument.entity.options.type.description": "Entitats de tipe", "argument.entity.options.type.invalid": "Tipe d'entitat desconegut o pas valid: %s", "argument.entity.options.unknown": "Opcion desconegut '%s'", "argument.entity.options.unterminated": "Fin de las causidas esperada", "argument.entity.options.valueless": "Valor esperada per la causida \"%s\"", "argument.entity.options.x.description": "Posicion x", "argument.entity.options.x_rotation.description": "Rotacion x de l'entitat", "argument.entity.options.y.description": "Posicion y", "argument.entity.options.y_rotation.description": "Rotacion y de l'entitat", "argument.entity.options.z.description": "Posicion z", "argument.entity.selector.allEntities": "Totes les entitats", "argument.entity.selector.allPlayers": "Totes los jogaires", "argument.entity.selector.missing": "Tipe de seleccionaire macant", "argument.entity.selector.nearestEntity": "Entitat la mai pròcha", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON> lo mai pròche", "argument.entity.selector.not_allowed": "Seleccionaire pas autorizat", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON>", "argument.entity.selector.self": "Entitat actuala", "argument.entity.selector.unknown": "Tipe de selector desconegut '%s'", "argument.entity.toomany": "Sonque una entitat es autorizada, mas lo seleccionaire provesida ne autoriza mai de una", "argument.enum.invalid": "Valor pas valida : %s", "argument.float.big": "Un float deu pas èsser superior a %s, valor trobada: %s", "argument.float.low": "Un float deu pas èsser inferior a %s, valor trobada: %s", "argument.gamemode.invalid": "Mòde de juòc desconegut: %s", "argument.hexcolor.invalid": "Còdi color exadecimal pas valid : %s", "argument.id.invalid": "ID pas valid", "argument.id.unknown": "ID desconegut: '%s'", "argument.integer.big": "Un entièr deu pas èsser superior a %s, valor trobada: %s", "argument.integer.low": "Un entièr deu pas èsser inferior a %s, valor trobada: %s", "argument.item.id.invalid": "Objècte desconegut '%s'", "argument.item.tag.disallowed": "Los tags son pas autorizats aicí, solament de vertadièrs objèctes", "argument.literal.incorrect": "Literal \"%s\" esperat", "argument.long.big": "Un long deu pas èsser superior a %s, valor trobada: %s", "argument.long.low": "Un long deu pas èsser inferior a %s, valor trobada: %s", "argument.message.too_long": "Lo messatge dins lo chat es tròp long (%s > maximum de %s caractèrs)", "argument.nbt.array.invalid": "Tipe de vector pas valid: %s", "argument.nbt.array.mixed": "Impossible d'inserir %s dins %s", "argument.nbt.expected.compound": "Tag compausat esperat", "argument.nbt.expected.key": "Clau esperada", "argument.nbt.expected.value": "Valor esperada", "argument.nbt.list.mixed": "Impossible d'inserir %s dins la lista de %s", "argument.nbt.trailing": "Donadas de fin inesperadas", "argument.player.entities": "Sonque los jogaires pòdon èsser afectats per aquela comanda, mas lo seleccionaire provesida enclau las entitats", "argument.player.toomany": "Sonque un jogaire es autorizada, mas lo seleccionaire provesida ne autoriza mai de una", "argument.player.unknown": "<PERSON><PERSON> j<PERSON> pas", "argument.pos.missing.double": "Coordenadas esperadas", "argument.pos.missing.int": "Posicion d'un bloc esperada", "argument.pos.mixed": "Impossible de mesclar de las coordenadas localas e de mond (tot deu utilizar ^ o non)", "argument.pos.outofbounds": "Aquela posicion es al defòra dels limites autorizats", "argument.pos.outofworld": "Aquela posicion es al defòra d'aquel mond !", "argument.pos.unloaded": "Aquela posicion es pas cargada", "argument.pos2d.incomplete": "Incomplet (2 coordenadas esperadas)", "argument.pos3d.incomplete": "Incomplet (3 coordenadas esperadas)", "argument.range.empty": "Valor o interval de valor esperat", "argument.range.ints": "Sonque los nombres entièrs son autorizats, pas los decimals", "argument.range.swapped": "Lo minimum pòt pas èsser mai grand que la maximum", "argument.resource.invalid_type": "L'element \"%s\" a lo mal tipe \"%s\" (\"%s\" esperat)", "argument.resource.not_found": "Impossible de trobar l'element \"%s\" de tipe \"%s\"", "argument.resource_or_id.failed_to_parse": "Mèuca de l'analisi sintaxica de la estructura : %s", "argument.resource_or_id.invalid": "ID o tag pas valid", "argument.resource_or_id.no_such_element": "Se pòt pas trobar l'element \"%s\" dins lo registre \"%s\"", "argument.resource_selector.not_found": "Cap de correspondéncia pel seleccionaire \"%s\" de tipe \"%s\"", "argument.resource_tag.invalid_type": "Lo tag \"%s\" a lo mal tipe \"%s\" (\"%s\" esperat)", "argument.resource_tag.not_found": "Impossible de trobar lo tag \"%s\" de tipe \"%s\"", "argument.rotation.incomplete": "Incomplet (2 coordenadas esperadas)", "argument.scoreHolder.empty": "Cap de detentor de marca pertinent pas pogut èsser trobat", "argument.scoreboardDisplaySlot.invalid": "Emplaçament d'afichatge desconegut : %s", "argument.style.invalid": "Estil pas valid : %s", "argument.time.invalid_tick_count": "Lo nombre de cicles a d'èsser non-negatiu", "argument.time.invalid_unit": "Unitat invalida", "argument.time.tick_count_too_low": "Lo nombre de cicles deu pas èsser inferior a %s, valor trobada: %s", "argument.uuid.invalid": "UUID invalide", "argument.waypoint.invalid": "L'entitat causida es pas una balisa", "arguments.block.tag.unknown": "Tag de blòc desconegut '%s'", "arguments.function.tag.unknown": "Tag de foncion desconegut '%s'", "arguments.function.unknown": "Foncion desconeguda '%s'", "arguments.item.component.expected": "Compausant d'objècte esperat", "arguments.item.component.malformed": "Compausant \"%s\" incorrèct : %s", "arguments.item.component.repeated": "Lo compausant d'objècte \"%s\" es estat repetit, mas sonque una valor pòt èsser provesida", "arguments.item.component.unknown": "Compausant d'objècte desconegut : %s", "arguments.item.malformed": "Objècte incorrèct : %s", "arguments.item.overstacked": "%s n'es pòt apilar que fins a %s", "arguments.item.predicate.malformed": "Predicat \"%s\" incorrèct : %s", "arguments.item.predicate.unknown": "Predicat d'objècte desconegut: %s'", "arguments.item.tag.unknown": "Tag de objècte desconegut '%s'", "arguments.nbtpath.node.invalid": "Element de camin NBT pas valid", "arguments.nbtpath.nothing_found": "Cap d'element correspondent a %s es pas estat trobat", "arguments.nbtpath.too_deep": "Lo NBT s'enseguissent es tròp prigondament imbricat", "arguments.nbtpath.too_large": "Lo NBT s'enseguissent es tròp grand", "arguments.objective.notFound": "Objectiu del tablèu de marca desconegut : %s", "arguments.objective.readonly": "L'objectiu \"%s\" del tablèu de marca es en lectura sola", "arguments.operation.div0": "Pòt pas divisir per 0", "arguments.operation.invalid": "Operacion pas valida", "arguments.swizzle.invalid": "Combinacion dels axes pas valid, combinacion de \"x\", \"y\" e \"z\" esperada", "attribute.modifier.equals.0": "%s en %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s %% en %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s %% en %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s %% en %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "<PERSON>ust<PERSON><PERSON>", "attribute.name.attack_damage": "Degalhs d'Ataca", "attribute.name.attack_knockback": "Reculada d'Ataca", "attribute.name.attack_speed": "Velocitat d'Ataca", "attribute.name.block_break_speed": "Velocitat de trencat de los blocs", "attribute.name.block_interaction_range": "Portada d'interaccion amb los blocs", "attribute.name.burning_time": "Temps enfuocat", "attribute.name.camera_distance": "Distància de la camerà", "attribute.name.entity_interaction_range": "Portada d'interaccion amb las entitats", "attribute.name.explosion_knockback_resistance": "Resistància a la reculada de las explosions", "attribute.name.fall_damage_multiplier": "Multiplicador de degalh de casuda", "attribute.name.flying_speed": "Velocitat de vòl", "attribute.name.follow_range": "Rai de deteccion de las creaturas", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Robust<PERSON>a de l'Armadura", "attribute.name.generic.attack_damage": "Degalhs d'ataca", "attribute.name.generic.attack_knockback": "Reculada d'Ataca", "attribute.name.generic.attack_speed": "Velocitat d'ataca", "attribute.name.generic.block_interaction_range": "Portada d'interaccion amb los blocs", "attribute.name.generic.burning_time": "Temps enfuocat", "attribute.name.generic.entity_interaction_range": "Portada d'interaccion amb las entitats", "attribute.name.generic.explosion_knockback_resistance": "Resistància a la reculada de las explosions", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de degalh de casuda", "attribute.name.generic.flying_speed": "Velocitat de vòl", "attribute.name.generic.follow_range": "Rai de Deteccion de las Creaturas", "attribute.name.generic.gravity": "Gravetat", "attribute.name.generic.jump_strength": "Poténcia de saut", "attribute.name.generic.knockback_resistance": "Resistància a la reculada", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Absorpcion maximala", "attribute.name.generic.max_health": "Vida maximala", "attribute.name.generic.movement_efficiency": "Eficacitat de movement", "attribute.name.generic.movement_speed": "Velocitat", "attribute.name.generic.oxygen_bonus": "Bonus d'oxigèn", "attribute.name.generic.safe_fall_distance": "Distància de casuda segura", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Nautor dels pas", "attribute.name.generic.water_movement_efficiency": "Eficacitat de movement aigassièr", "attribute.name.gravity": "Gravetat", "attribute.name.horse.jump_strength": "Poténcia de saut dau cavau", "attribute.name.jump_strength": "Poténcia de saut", "attribute.name.knockback_resistance": "Resistància a la Reculada", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Absorpcion maximala", "attribute.name.max_health": "Vida maximala", "attribute.name.mining_efficiency": "Eficacitat de minatge", "attribute.name.movement_efficiency": "Eficacitat de movement", "attribute.name.movement_speed": "Velocitat", "attribute.name.oxygen_bonus": "Bonus d'oxigèn", "attribute.name.player.block_break_speed": "Velocitat de trencat de los blocs", "attribute.name.player.block_interaction_range": "Portada d'interaccion amb los blocs", "attribute.name.player.entity_interaction_range": "Portada d'interaccion amb las entitats", "attribute.name.player.mining_efficiency": "Eficacitat de minatge", "attribute.name.player.sneaking_speed": "Velocitat agrovat", "attribute.name.player.submerged_mining_speed": "Velocitat de minatge jos l'aiga", "attribute.name.player.sweeping_damage_ratio": "Ratio de degalh per escobatge", "attribute.name.safe_fall_distance": "Distància de casuda segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocitat agrovat", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.step_height": "Nautor dels pas", "attribute.name.submerged_mining_speed": "Velocitat de minatge jos l'aiga", "attribute.name.sweeping_damage_ratio": "Ratio de degalh per escobatge", "attribute.name.tempt_range": "Portada de temptacion de las creaturas", "attribute.name.water_movement_efficiency": "Eficacitat de movement aigassièr", "attribute.name.waypoint_receive_range": "Portada de recepcion de las balisas", "attribute.name.waypoint_transmit_range": "Portada de transmission de las balisas", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Dèltas de basalte", "biome.minecraft.beach": "<PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Bòsc de <PERSON>èç", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON> de <PERSON>", "biome.minecraft.cold_ocean": "Ocean freg", "biome.minecraft.crimson_forest": "Bòsc cremesin", "biome.minecraft.dark_forest": "Bòsc escur", "biome.minecraft.deep_cold_ocean": "Ocean freg prigond", "biome.minecraft.deep_dark": "Escuritat prigonda", "biome.minecraft.deep_frozen_ocean": "Ocean glaçat prigond", "biome.minecraft.deep_lukewarm_ocean": "Ocean tebés prigond", "biome.minecraft.deep_ocean": "Ocean prigond", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "<PERSON>umas <PERSON>espeleo<PERSON>", "biome.minecraft.end_barrens": "Tèrras aridas de l'End", "biome.minecraft.end_highlands": "<PERSON><PERSON><PERSON>s nauta<PERSON> de l'End", "biome.minecraft.end_midlands": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> de l'End", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON> eroda<PERSON>", "biome.minecraft.flower_forest": "Bòsc de flors", "biome.minecraft.forest": "Bòsc", "biome.minecraft.frozen_ocean": "Ocean glaçat", "biome.minecraft.frozen_peaks": "Cimas geladas", "biome.minecraft.frozen_river": "<PERSON><PERSON>", "biome.minecraft.grove": "Bosquet", "biome.minecraft.ice_spikes": "Pics de glaç", "biome.minecraft.jagged_peaks": "Cimas denteladas", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Ocean tebés", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON> luxuriantas", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "Pradariá", "biome.minecraft.mushroom_fields": "Camp de camparòus", "biome.minecraft.nether_wastes": "<PERSON><PERSON><PERSON>", "biome.minecraft.ocean": "Ocean", "biome.minecraft.old_growth_birch_forest": "Bòsc de bèces ancians", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON> ancians", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON> d'avets ancians", "biome.minecraft.pale_garden": "Jardin palle", "biome.minecraft.plains": "Planas", "biome.minecraft.river": "Riu", "biome.minecraft.savanna": "<PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Plan de savana", "biome.minecraft.small_end_islands": "<PERSON>las p<PERSON> de l'End", "biome.minecraft.snowy_beach": "<PERSON><PERSON><PERSON> ne<PERSON>da", "biome.minecraft.snowy_plains": "Planas nevadas", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON><PERSON> en<PERSON>", "biome.minecraft.snowy_taiga": "Taiga nevada", "biome.minecraft.soul_sand_valley": "<PERSON>au <PERSON>", "biome.minecraft.sparse_jungle": "<PERSON><PERSON> c<PERSON>", "biome.minecraft.stony_peaks": "Cimas rocosas", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON> rocosa", "biome.minecraft.sunflower_plains": "Planas de virasolelhs", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "L'End", "biome.minecraft.the_void": "Lo void", "biome.minecraft.warm_ocean": "Ocean caud", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>ut", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON> gravelosas ventosas", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON> vent<PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON> ventosa", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON> bos<PERSON>es", "block.minecraft.acacia_button": "Boton en cacièr", "block.minecraft.acacia_door": "Pòrta en cacièr", "block.minecraft.acacia_fence": "Barralha en cacièr", "block.minecraft.acacia_fence_gate": "Pòrta de barralha en cacièr", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en cacièr", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Busca de cacièr", "block.minecraft.acacia_planks": "Plancas de cacièr", "block.minecraft.acacia_pressure_plate": "Placa de pression en cacièr", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_sign": "Pancarta en cacièr", "block.minecraft.acacia_slab": "<PERSON><PERSON>", "block.minecraft.acacia_stairs": "Escalièrs en cacièr", "block.minecraft.acacia_trapdoor": "Trapa en cacièr", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en cacièr murala", "block.minecraft.acacia_wall_sign": "Pancarta en cacièr murala", "block.minecraft.acacia_wood": "<PERSON><PERSON>", "block.minecraft.activator_rail": "Ralh activator", "block.minecraft.air": "Aire", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Blòc d'ametista", "block.minecraft.amethyst_cluster": "Amàs d'ametista", "block.minecraft.ancient_debris": "Brigalhs antics", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Lausa d'and<PERSON>", "block.minecraft.andesite_stairs": "Escalièrs d'andesita", "block.minecraft.andesite_wall": "<PERSON><PERSON>'and<PERSON>", "block.minecraft.anvil": "Enclutge", "block.minecraft.attached_melon_stem": "Tija de pastèca estacada", "block.minecraft.attached_pumpkin_stem": "Tija de coja estacada", "block.minecraft.azalea": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "Fuè<PERSON><PERSON>", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "Bambó", "block.minecraft.bamboo_block": "<PERSON><PERSON><PERSON><PERSON>ó", "block.minecraft.bamboo_button": "Boton en bambó", "block.minecraft.bamboo_door": "Pòrta en bambó", "block.minecraft.bamboo_fence": "Barralha en bambó", "block.minecraft.bamboo_fence_gate": "Pòrta de <PERSON>ha en bambó", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en bambó", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic_slab": "Lausa en mosaï<PERSON> de bam<PERSON>ó", "block.minecraft.bamboo_mosaic_stairs": "Escalièrs en mosaïc de bam<PERSON>ó", "block.minecraft.bamboo_planks": "Plancas de bambó", "block.minecraft.bamboo_pressure_plate": "Placa de pression en bambó", "block.minecraft.bamboo_sapling": "<PERSON><PERSON>", "block.minecraft.bamboo_sign": "Pancarta en bambó", "block.minecraft.bamboo_slab": "Lausa en bambó", "block.minecraft.bamboo_stairs": "Escalièrs en bambó", "block.minecraft.bamboo_trapdoor": "Trapa en bambó", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en bambó murala", "block.minecraft.bamboo_wall_sign": "Pancarta en bambó murala", "block.minecraft.banner.base.black": "Camp negre plan", "block.minecraft.banner.base.blue": "Camp blau plan", "block.minecraft.banner.base.brown": "Camp brun plan", "block.minecraft.banner.base.cyan": "Camp cian plan", "block.minecraft.banner.base.gray": "Camp gris plan", "block.minecraft.banner.base.green": "Camp verd plan", "block.minecraft.banner.base.light_blue": "Camp blau clar plan", "block.minecraft.banner.base.light_gray": "Camp gris clar plan", "block.minecraft.banner.base.lime": "Camp lima plan", "block.minecraft.banner.base.magenta": "Camp magenta plan", "block.minecraft.banner.base.orange": "Camp irange plan", "block.minecraft.banner.base.pink": "Camp ròse plan", "block.minecraft.banner.base.purple": "Camp violet plan", "block.minecraft.banner.base.red": "Camp roge plan", "block.minecraft.banner.base.white": "Camp blanc plan", "block.minecraft.banner.base.yellow": "Camp jaune plan", "block.minecraft.banner.border.black": "Bordadura negra", "block.minecraft.banner.border.blue": "Bordadura blava", "block.minecraft.banner.border.brown": "Bordadura bruna", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.border.light_gray": "Bordadura gris clar", "block.minecraft.banner.border.lime": "Bordadura lima", "block.minecraft.banner.border.magenta": "Bordadura magenta", "block.minecraft.banner.border.orange": "Bordadura iranja", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON> ròsa", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.border.red": "Bordadura roja", "block.minecraft.banner.border.white": "Bordadu<PERSON> blanca", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON><PERSON> jauna", "block.minecraft.banner.bricks.black": "Camp maçonat negre", "block.minecraft.banner.bricks.blue": "Camp maçonat blau", "block.minecraft.banner.bricks.brown": "Camp maçonat brun", "block.minecraft.banner.bricks.cyan": "Camp maçonat cian", "block.minecraft.banner.bricks.gray": "Camp maçonat gris", "block.minecraft.banner.bricks.green": "Camp maçonat verd", "block.minecraft.banner.bricks.light_blue": "Camp ma<PERSON>onat blau clar", "block.minecraft.banner.bricks.light_gray": "Camp maçonat gris clar", "block.minecraft.banner.bricks.lime": "Camp maçonat lima", "block.minecraft.banner.bricks.magenta": "Camp maçonat magenta", "block.minecraft.banner.bricks.orange": "Camp maçonat irange", "block.minecraft.banner.bricks.pink": "Camp maçonat ròse", "block.minecraft.banner.bricks.purple": "Camp maçonat violet", "block.minecraft.banner.bricks.red": "Camp maçonat roge", "block.minecraft.banner.bricks.white": "Camp maçonat blanc", "block.minecraft.banner.bricks.yellow": "Camp maçonat jaune", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON><PERSON> negre", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON><PERSON> blau", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON><PERSON> brun", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON> verd", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON><PERSON> gris clar", "block.minecraft.banner.circle.lime": "Tortèu lima", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>u magenta", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "Tortèu violet", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON><PERSON> roge", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON><PERSON> jaune", "block.minecraft.banner.creeper.black": "Fàcia de creeper negra", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON>cia de creeper blava", "block.minecraft.banner.creeper.brown": "Fàcia de creeper bruna", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON> de creeper cian", "block.minecraft.banner.creeper.gray": "Fàcia de creeper grisa", "block.minecraft.banner.creeper.green": "Fàcia de creeper verda", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON> de creeper blau clar", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON> de creeper gris clar", "block.minecraft.banner.creeper.lime": "Fàcia de creeper lima", "block.minecraft.banner.creeper.magenta": "Fàcia de creeper magenta", "block.minecraft.banner.creeper.orange": "Fàcia de creeper iranja", "block.minecraft.banner.creeper.pink": "Fàcia de creeper ròsa", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON> de creeper violeta", "block.minecraft.banner.creeper.red": "Fàcia de creeper roja", "block.minecraft.banner.creeper.white": "Fàcia de creeper blanca", "block.minecraft.banner.creeper.yellow": "Fàcia de creeper jauna", "block.minecraft.banner.cross.black": "Sautador negre", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON> blau", "block.minecraft.banner.cross.brown": "Sautador brun", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON>ian", "block.minecraft.banner.cross.gray": "Sautador gris", "block.minecraft.banner.cross.green": "Sautador verd", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.cross.light_gray": "Sautador gris car", "block.minecraft.banner.cross.lime": "Sautador lima", "block.minecraft.banner.cross.magenta": "Sautador magenta", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.pink": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.cross.purple": "Sautador violet", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON> roge", "block.minecraft.banner.cross.white": "<PERSON>uta<PERSON> blanc", "block.minecraft.banner.cross.yellow": "<PERSON>utador jaune", "block.minecraft.banner.curly_border.black": "Bordadura endentada negra", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON><PERSON> endentada blava", "block.minecraft.banner.curly_border.brown": "Bordadura endentada bruna", "block.minecraft.banner.curly_border.cyan": "Borda<PERSON>ra endentada cian", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON>ra endentada grisa", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON>ra endentada verda", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON><PERSON> endentada blau clar", "block.minecraft.banner.curly_border.light_gray": "Borda<PERSON>ra endentada gris clar", "block.minecraft.banner.curly_border.lime": "Bordadura endentada lima", "block.minecraft.banner.curly_border.magenta": "Bordadura endentada magenta", "block.minecraft.banner.curly_border.orange": "Bordadura endentada iranja", "block.minecraft.banner.curly_border.pink": "Bordadura endentada ròsa", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON><PERSON><PERSON> endentada violeta", "block.minecraft.banner.curly_border.red": "Bordadura endentada roja", "block.minecraft.banner.curly_border.white": "Bordadura endentada blanca", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON>ra endentada jauna", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> negre", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> blau", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> brun", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> <PERSON>ian", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> gris", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> verd", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> blau clar", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> gris clar", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> lima", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON>, 1èr magenta", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON>, 1èr violet", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> roge", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> blanc", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> jaune", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> negre", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> blau", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> brun", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> <PERSON>ian", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> gris", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> verd", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> blau clar", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> gris clar", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON><PERSON>, 1<PERSON>r lima", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON>, 1<PERSON>r magenta", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> <PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON>, 1<PERSON>r violet", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> roge", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> blanc", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON>, 1<PERSON><PERSON> jaune", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON>, 2nd negre", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON><PERSON>, 2nd blau", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON>, 2nd brun", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON>, 2nd cian", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON>, 2nd gris", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON>, 2nd verd", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON>, 2nd blau clar", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON><PERSON><PERSON>, 2nd gris clar", "block.minecraft.banner.diagonal_up_left.lime": "<PERSON><PERSON><PERSON>, 2nd lima", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON>, 2nd magenta", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON>, 2nd irange", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON>, 2nd ròse", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON><PERSON>, 2nd violet", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON>, 2nd roge", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON>, 2nd blanc", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON>, 2nd jaune", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON>, 2nd negre", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON>, 2nd blau", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON>, 2nd brun", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON>, 2nd cian", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON>, 2nd gris", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON>, 2nd verd", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON>, 2nd blau clar", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON><PERSON>, 2nd gris clar", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON><PERSON>, 2nd lima", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON>, 2nd magenta", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON><PERSON>, 2nd irange", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON>, 2nd ròse", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON>, 2nd violet", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON>, 2nd roge", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON>, 2nd blanc", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON>, 2nd jaune", "block.minecraft.banner.flow.black": "Revolum negre", "block.minecraft.banner.flow.blue": "Revolum blau", "block.minecraft.banner.flow.brown": "Revolum brun", "block.minecraft.banner.flow.cyan": "Revolum cian", "block.minecraft.banner.flow.gray": "Revolum gris", "block.minecraft.banner.flow.green": "Revolum verd", "block.minecraft.banner.flow.light_blue": "Revolum blau clar", "block.minecraft.banner.flow.light_gray": "Revolum gris clar", "block.minecraft.banner.flow.lime": "Revolum lima", "block.minecraft.banner.flow.magenta": "Revolum magenta", "block.minecraft.banner.flow.orange": "Revolum irange", "block.minecraft.banner.flow.pink": "Revolum ròse", "block.minecraft.banner.flow.purple": "Revolum violet", "block.minecraft.banner.flow.red": "Revolum roge", "block.minecraft.banner.flow.white": "Revolum blanc", "block.minecraft.banner.flow.yellow": "Revolum jaune", "block.minecraft.banner.flower.black": "Flor negra", "block.minecraft.banner.flower.blue": "Flor blava", "block.minecraft.banner.flower.brown": "Flor bruna", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.flower.gray": "Flor grisa", "block.minecraft.banner.flower.green": "Flor verda", "block.minecraft.banner.flower.light_blue": "Flor blau clar", "block.minecraft.banner.flower.light_gray": "Flor gris clar", "block.minecraft.banner.flower.lime": "Flor lima", "block.minecraft.banner.flower.magenta": "Flor magenta", "block.minecraft.banner.flower.orange": "Flor <PERSON>", "block.minecraft.banner.flower.pink": "<PERSON>lor ròsa", "block.minecraft.banner.flower.purple": "Flor violeta", "block.minecraft.banner.flower.red": "<PERSON><PERSON> roja", "block.minecraft.banner.flower.white": "Flor blanca", "block.minecraft.banner.flower.yellow": "Flor jauna", "block.minecraft.banner.globe.black": "Glòbe negre", "block.minecraft.banner.globe.blue": "<PERSON><PERSON>ò<PERSON> blau", "block.minecraft.banner.globe.brown": "Glòbe brun", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.globe.gray": "Glòbe gris", "block.minecraft.banner.globe.green": "Glòbe verd", "block.minecraft.banner.globe.light_blue": "<PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.globe.light_gray": "Glòbe gris clar", "block.minecraft.banner.globe.lime": "Glòbe lima", "block.minecraft.banner.globe.magenta": "Glòbe magenta", "block.minecraft.banner.globe.orange": "Glòbe i<PERSON>e", "block.minecraft.banner.globe.pink": "Glòbe ròse", "block.minecraft.banner.globe.purple": "Glòbe violet", "block.minecraft.banner.globe.red": "Glòbe roge", "block.minecraft.banner.globe.white": "Glòbe blanc", "block.minecraft.banner.globe.yellow": "Glòbe jaune", "block.minecraft.banner.gradient.black": "Degradat negre", "block.minecraft.banner.gradient.blue": "Degradat blau", "block.minecraft.banner.gradient.brown": "Degradat brun", "block.minecraft.banner.gradient.cyan": "Degradat cian", "block.minecraft.banner.gradient.gray": "Degradat gris", "block.minecraft.banner.gradient.green": "Degradat verd", "block.minecraft.banner.gradient.light_blue": "Degradat blau clar", "block.minecraft.banner.gradient.light_gray": "Degradat gris clar", "block.minecraft.banner.gradient.lime": "Degradat lima", "block.minecraft.banner.gradient.magenta": "Degradat magenta", "block.minecraft.banner.gradient.orange": "Degradat irange", "block.minecraft.banner.gradient.pink": "Degradat ròse", "block.minecraft.banner.gradient.purple": "Degradat violet", "block.minecraft.banner.gradient.red": "Degradat roge", "block.minecraft.banner.gradient.white": "Degradat blanc", "block.minecraft.banner.gradient.yellow": "Degradat jaune", "block.minecraft.banner.gradient_up.black": "Degradat negre en poncha", "block.minecraft.banner.gradient_up.blue": "Degradat blau en poncha", "block.minecraft.banner.gradient_up.brown": "Degradat brun en poncha", "block.minecraft.banner.gradient_up.cyan": "Degradat cian en poncha", "block.minecraft.banner.gradient_up.gray": "Degradat gris en poncha", "block.minecraft.banner.gradient_up.green": "Degradat verd en poncha", "block.minecraft.banner.gradient_up.light_blue": "Degradat blau clar en poncha", "block.minecraft.banner.gradient_up.light_gray": "Degradat gris clar en poncha", "block.minecraft.banner.gradient_up.lime": "Degradat lima en poncha", "block.minecraft.banner.gradient_up.magenta": "Degradat magenta en poncha", "block.minecraft.banner.gradient_up.orange": "Degradat irange en poncha", "block.minecraft.banner.gradient_up.pink": "Degradat ròse en poncha", "block.minecraft.banner.gradient_up.purple": "Degradat violet en poncha", "block.minecraft.banner.gradient_up.red": "Degradat roge en poncha", "block.minecraft.banner.gradient_up.white": "Degradat blanc en poncha", "block.minecraft.banner.gradient_up.yellow": "Degradat jaune en poncha", "block.minecraft.banner.guster.black": "Ventorià negra", "block.minecraft.banner.guster.blue": "Ventorià blava", "block.minecraft.banner.guster.brown": "Ventorià bruna", "block.minecraft.banner.guster.cyan": "Ventorià cian", "block.minecraft.banner.guster.gray": "Ventorià grisa", "block.minecraft.banner.guster.green": "Ventorià verda", "block.minecraft.banner.guster.light_blue": "Ventorià blau clar", "block.minecraft.banner.guster.light_gray": "Ventorià gris clar", "block.minecraft.banner.guster.lime": "Ventorià lima", "block.minecraft.banner.guster.magenta": "Ventorià magenta", "block.minecraft.banner.guster.orange": "Ventorià iranja", "block.minecraft.banner.guster.pink": "Ventorià ròsa", "block.minecraft.banner.guster.purple": "Ventorià violeta", "block.minecraft.banner.guster.red": "Ventorià roja", "block.minecraft.banner.guster.white": "Ventorià blanca", "block.minecraft.banner.guster.yellow": "Ventorià jauna", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON>, 1<PERSON><PERSON> negre", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON>, 1<PERSON><PERSON> blau", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON>, 1<PERSON><PERSON> brun", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON>, 1<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON>, 1<PERSON><PERSON> gris", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON>, 1<PERSON><PERSON> verd", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON>, 1<PERSON><PERSON> blau clar", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON>, 1<PERSON><PERSON> gris clar", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON>, 1<PERSON>r lima", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON>, 1<PERSON>r magenta", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON>, 1<PERSON>r violet", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> roge", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON>, 1<PERSON><PERSON> jaune", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON>, 2nd negre", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON>, 2nd blau", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON>, 2nd brun", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON>, 2nd cian", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON>, 2nd gris", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON>, 2nd verd", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON>, 2nd blau clar", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON>, 2nd gris clar", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON>, 2nd lima", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON>, 2nd magenta", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON>, 2nd irange", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON>, 2nd ròse", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON>, 2nd violet", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON>, 2nd roge", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON>, 2nd blanc", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON>, 2nd jaune", "block.minecraft.banner.half_vertical.black": "Partit, 1<PERSON><PERSON> negre", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON>, 1<PERSON><PERSON> blau", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>, 1<PERSON>r brun", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON>, 1<PERSON><PERSON> <PERSON>ian", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON>, 1<PERSON><PERSON> gris", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON>, 1<PERSON><PERSON> verd", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON>, 1<PERSON><PERSON> blau clar", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON>, 1<PERSON>r gris clar", "block.minecraft.banner.half_vertical.lime": "Partit, 1<PERSON>r lima", "block.minecraft.banner.half_vertical.magenta": "<PERSON>it, 1èr magenta", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON>, 1<PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "<PERSON>it, 1<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.half_vertical.purple": "<PERSON>it, 1èr violet", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>, 1<PERSON><PERSON> roge", "block.minecraft.banner.half_vertical.white": "<PERSON>it, 1<PERSON><PERSON> blanc", "block.minecraft.banner.half_vertical.yellow": "<PERSON>it, 1<PERSON><PERSON> jaune", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON>, 2nd negre", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON>, 2nd blau", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>, 2nd brun", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON>, 2nd cian", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON>, 2nd gris", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON>, 2nd verd", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON>, 2nd blau clar", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON>, 2nd blau clar", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON>, 2nd lima", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON>, 2nd magenta", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON>, 2nd irange", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON>, 2nd ròse", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON>, 2nd violet", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON>, 2nd roge", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON>, 2nd blanc", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>, 2nd jaune", "block.minecraft.banner.mojang.black": "Emblèma negra", "block.minecraft.banner.mojang.blue": "Emblèma blava", "block.minecraft.banner.mojang.brown": "Emblèma bruna", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.mojang.gray": "Emblè<PERSON> grisa", "block.minecraft.banner.mojang.green": "Emblè<PERSON> verda", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.mojang.light_gray": "Emblèma gris clar", "block.minecraft.banner.mojang.lime": "Emblèma lima", "block.minecraft.banner.mojang.magenta": "Emblèma magenta", "block.minecraft.banner.mojang.orange": "Emblèma i<PERSON>", "block.minecraft.banner.mojang.pink": "Emblè<PERSON> ròsa", "block.minecraft.banner.mojang.purple": "Emblè<PERSON> violeta", "block.minecraft.banner.mojang.red": "Emblè<PERSON> roja", "block.minecraft.banner.mojang.white": "Emblè<PERSON> blanca", "block.minecraft.banner.mojang.yellow": "Emblè<PERSON> jauna", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> negre", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> blau", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON> brun", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.piglin.green": "<PERSON><PERSON> verd", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON> blau clar", "block.minecraft.banner.piglin.light_gray": "<PERSON>rre gris clar", "block.minecraft.banner.piglin.lime": "Morre lima", "block.minecraft.banner.piglin.magenta": "Morre magenta", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON> i<PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON> rò<PERSON>", "block.minecraft.banner.piglin.purple": "Morre violet", "block.minecraft.banner.piglin.red": "<PERSON><PERSON> roge", "block.minecraft.banner.piglin.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON> jaune", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON> negre", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON> blau", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>e brun", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON> verd", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.rhombus.light_gray": "<PERSON>sange gris clar", "block.minecraft.banner.rhombus.lime": "Lausange lima", "block.minecraft.banner.rhombus.magenta": "Lausange magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON> i<PERSON>e", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rò<PERSON>", "block.minecraft.banner.rhombus.purple": "Lausange violet", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON> roge", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> jaune", "block.minecraft.banner.skull.black": "<PERSON><PERSON> negre", "block.minecraft.banner.skull.blue": "<PERSON><PERSON> blau", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> brun", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.skull.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.skull.green": "<PERSON><PERSON> verd", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON> blau clar", "block.minecraft.banner.skull.light_gray": "<PERSON>rani gris clar", "block.minecraft.banner.skull.lime": "Crani lima", "block.minecraft.banner.skull.magenta": "Crani magenta", "block.minecraft.banner.skull.orange": "Crani i<PERSON>e", "block.minecraft.banner.skull.pink": "<PERSON><PERSON> ròse", "block.minecraft.banner.skull.purple": "Crani violet", "block.minecraft.banner.skull.red": "<PERSON><PERSON> roge", "block.minecraft.banner.skull.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.skull.yellow": "<PERSON>rani jaune", "block.minecraft.banner.small_stripes.black": "Quatre paus negres", "block.minecraft.banner.small_stripes.blue": "<PERSON>uatre paus blaus", "block.minecraft.banner.small_stripes.brown": "Quatre paus bruns", "block.minecraft.banner.small_stripes.cyan": "Quatre paus cian", "block.minecraft.banner.small_stripes.gray": "Quatre paus grises", "block.minecraft.banner.small_stripes.green": "Quatre paus verds", "block.minecraft.banner.small_stripes.light_blue": "Quatre paus blau clar", "block.minecraft.banner.small_stripes.light_gray": "Quatre paus gris clar", "block.minecraft.banner.small_stripes.lime": "Quatre paus lima", "block.minecraft.banner.small_stripes.magenta": "Quatre paus magenta", "block.minecraft.banner.small_stripes.orange": "Quatre paus iranges", "block.minecraft.banner.small_stripes.pink": "Quatre paus ròses", "block.minecraft.banner.small_stripes.purple": "Quatre paus violets", "block.minecraft.banner.small_stripes.red": "Quatre paus roges", "block.minecraft.banner.small_stripes.white": "Quatre paus blancs", "block.minecraft.banner.small_stripes.yellow": "Quatre paus jaunes", "block.minecraft.banner.square_bottom_left.black": "Canton dèstre en poncha negre", "block.minecraft.banner.square_bottom_left.blue": "Canton dèstre en poncha blau", "block.minecraft.banner.square_bottom_left.brown": "Canton brun en basa a drecha", "block.minecraft.banner.square_bottom_left.cyan": "Canton dèstre en poncha cian", "block.minecraft.banner.square_bottom_left.gray": "Canton dèstre en poncha gris", "block.minecraft.banner.square_bottom_left.green": "Canton dèstre en poncha verd", "block.minecraft.banner.square_bottom_left.light_blue": "Canton dèstre en poncha blau clar", "block.minecraft.banner.square_bottom_left.light_gray": "Canton dèstre en poncha gris clar", "block.minecraft.banner.square_bottom_left.lime": "Canton dèstre en poncha lima", "block.minecraft.banner.square_bottom_left.magenta": "Canton dèstre en poncha magenta", "block.minecraft.banner.square_bottom_left.orange": "Canton dèstre en poncha irange", "block.minecraft.banner.square_bottom_left.pink": "Canton dèstre en poncha ròse", "block.minecraft.banner.square_bottom_left.purple": "Canton dèstre en poncha violet", "block.minecraft.banner.square_bottom_left.red": "Canton dèstre en poncha roge", "block.minecraft.banner.square_bottom_left.white": "Canton dèstre en poncha blanc", "block.minecraft.banner.square_bottom_left.yellow": "Canton dèstre en poncha jaune", "block.minecraft.banner.square_bottom_right.black": "Canton senèstre en poncha negre", "block.minecraft.banner.square_bottom_right.blue": "Canton senèstre en poncha blau", "block.minecraft.banner.square_bottom_right.brown": "Canton senèstre en poncha brun", "block.minecraft.banner.square_bottom_right.cyan": "Canton senèstre en poncha cian", "block.minecraft.banner.square_bottom_right.gray": "Canton senèstre en poncha gris", "block.minecraft.banner.square_bottom_right.green": "Canton senèstre en poncha verd", "block.minecraft.banner.square_bottom_right.light_blue": "Canton senèstre en poncha blau clar", "block.minecraft.banner.square_bottom_right.light_gray": "Canton senèstre en poncha gris clar", "block.minecraft.banner.square_bottom_right.lime": "Canton senèstre en poncha lima", "block.minecraft.banner.square_bottom_right.magenta": "Canton senèstre en poncha magenta", "block.minecraft.banner.square_bottom_right.orange": "Canton senèstre en poncha irange", "block.minecraft.banner.square_bottom_right.pink": "Canton senèstre en poncha ròse", "block.minecraft.banner.square_bottom_right.purple": "Canton senèstre en poncha violet", "block.minecraft.banner.square_bottom_right.red": "Canton senèstre en poncha roge", "block.minecraft.banner.square_bottom_right.white": "Canton senèstre en poncha blanc", "block.minecraft.banner.square_bottom_right.yellow": "Canton senèstre en poncha jaune", "block.minecraft.banner.square_top_left.black": "Canton dèstre en cap negre", "block.minecraft.banner.square_top_left.blue": "Canton dèstre en cap blau", "block.minecraft.banner.square_top_left.brown": "Canton dèstre en cap brun", "block.minecraft.banner.square_top_left.cyan": "Canton dèstre en cap cian", "block.minecraft.banner.square_top_left.gray": "Canton dèstre en cap gris", "block.minecraft.banner.square_top_left.green": "Canton dèstre en cap verd", "block.minecraft.banner.square_top_left.light_blue": "Canton dèstre en cap blau clar", "block.minecraft.banner.square_top_left.light_gray": "Canton dèstre en cap gris clar", "block.minecraft.banner.square_top_left.lime": "Canton dèstre en cap lima", "block.minecraft.banner.square_top_left.magenta": "Canton dèstre en cap magenta", "block.minecraft.banner.square_top_left.orange": "Canton dèstre en cap irange", "block.minecraft.banner.square_top_left.pink": "Canton dèstre en cap ròse", "block.minecraft.banner.square_top_left.purple": "Canton dèstre en cap violet", "block.minecraft.banner.square_top_left.red": "Canton dèstre en cap roge", "block.minecraft.banner.square_top_left.white": "Canton dèstre en cap blanc", "block.minecraft.banner.square_top_left.yellow": "Canton dèstre en cap jaune", "block.minecraft.banner.square_top_right.black": "Canton senèstre en cap blanc", "block.minecraft.banner.square_top_right.blue": "Canton senèstre en cap blau", "block.minecraft.banner.square_top_right.brown": "Canton senèstre en cap brun", "block.minecraft.banner.square_top_right.cyan": "Canton senèstre en cap cian", "block.minecraft.banner.square_top_right.gray": "Canton senèstre en cap gris", "block.minecraft.banner.square_top_right.green": "Canton senèstre en cap verd", "block.minecraft.banner.square_top_right.light_blue": "Canton senèstre en cap blau clar", "block.minecraft.banner.square_top_right.light_gray": "Canton senèstre en cap gris clar", "block.minecraft.banner.square_top_right.lime": "Canton senèstre en cap lima", "block.minecraft.banner.square_top_right.magenta": "Canton senèstre en cap magenta", "block.minecraft.banner.square_top_right.orange": "Canton senèstre en cap irange", "block.minecraft.banner.square_top_right.pink": "Canton senèstre en cap ròse", "block.minecraft.banner.square_top_right.purple": "Canton senèstre en cap violet", "block.minecraft.banner.square_top_right.red": "Canton senèstre en cap roge", "block.minecraft.banner.square_top_right.white": "Canton senèstre en cap blanc", "block.minecraft.banner.square_top_right.yellow": "Canton senèstre en cap jaune", "block.minecraft.banner.straight_cross.black": "Crotz negra", "block.minecraft.banner.straight_cross.blue": "Crotz blava", "block.minecraft.banner.straight_cross.brown": "Crotz bruna", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON> gris clar", "block.minecraft.banner.straight_cross.lime": "Crotz lima", "block.minecraft.banner.straight_cross.magenta": "Crotz magenta", "block.minecraft.banner.straight_cross.orange": "Crotz iranja", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON> roja", "block.minecraft.banner.straight_cross.white": "<PERSON>rotz blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON> jauna", "block.minecraft.banner.stripe_bottom.black": "Campan<PERSON> negra", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON> blava", "block.minecraft.banner.stripe_bottom.brown": "<PERSON>an<PERSON> bruna", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON> gris clar", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON> lima", "block.minecraft.banner.stripe_bottom.magenta": "Campanha magenta", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON><PERSON> i<PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON> roja", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON> jauna", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON> negre", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> blau", "block.minecraft.banner.stripe_center.brown": "<PERSON>u brun", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.stripe_center.gray": "<PERSON>u gris", "block.minecraft.banner.stripe_center.green": "<PERSON>u verd", "block.minecraft.banner.stripe_center.light_blue": "<PERSON>u blau clar", "block.minecraft.banner.stripe_center.light_gray": "Pau gris clar", "block.minecraft.banner.stripe_center.lime": "<PERSON>u lima", "block.minecraft.banner.stripe_center.magenta": "Pau magenta", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "Pau violet", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON> roge", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> jaune", "block.minecraft.banner.stripe_downleft.black": "Barra negra", "block.minecraft.banner.stripe_downleft.blue": "Barra blava", "block.minecraft.banner.stripe_downleft.brown": "Barra bruna", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.stripe_downleft.gray": "<PERSON>a grisa", "block.minecraft.banner.stripe_downleft.green": "Barra verda", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON>a blau clar", "block.minecraft.banner.stripe_downleft.light_gray": "Barra gris clar", "block.minecraft.banner.stripe_downleft.lime": "Barra lima", "block.minecraft.banner.stripe_downleft.magenta": "Barra magenta", "block.minecraft.banner.stripe_downleft.orange": "Barra iranja", "block.minecraft.banner.stripe_downleft.pink": "Barra ròsa", "block.minecraft.banner.stripe_downleft.purple": "<PERSON>a violeta", "block.minecraft.banner.stripe_downleft.red": "Barra roja", "block.minecraft.banner.stripe_downleft.white": "Barra blanca", "block.minecraft.banner.stripe_downleft.yellow": "Barra jauna", "block.minecraft.banner.stripe_downright.black": "<PERSON>a negra", "block.minecraft.banner.stripe_downright.blue": "Benda blava", "block.minecraft.banner.stripe_downright.brown": "<PERSON>a bruna", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON> g<PERSON>a", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON> verda", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON> blau clar", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON>a gris clar", "block.minecraft.banner.stripe_downright.lime": "<PERSON>a lima", "block.minecraft.banner.stripe_downright.magenta": "Benda magenta", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON> violeta", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON> blanca", "block.minecraft.banner.stripe_downright.yellow": "<PERSON>a jauna", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON> d<PERSON> negre", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON> blau", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON> d<PERSON>tre brun", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON> d<PERSON> verd", "block.minecraft.banner.stripe_left.light_blue": "<PERSON><PERSON> d<PERSON><PERSON> blau clar", "block.minecraft.banner.stripe_left.light_gray": "<PERSON><PERSON> d<PERSON>tre gris clar", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON> d<PERSON>tre lima", "block.minecraft.banner.stripe_left.magenta": "<PERSON>u dèstre magenta", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON> rò<PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON> d<PERSON> violet", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON> roge", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON> jaune", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON>sa negra", "block.minecraft.banner.stripe_middle.blue": "F<PERSON>sa blava", "block.minecraft.banner.stripe_middle.brown": "Faissa bruna", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.stripe_middle.light_gray": "Faissa gris clar", "block.minecraft.banner.stripe_middle.lime": "Faissa lima", "block.minecraft.banner.stripe_middle.magenta": "Faissa magenta", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> ròsa", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON> roja", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON>sa blanca", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> jauna", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON> sen<PERSON> negre", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON> blau", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> sen<PERSON><PERSON> brun", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON> se<PERSON><PERSON> cian", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON> sen<PERSON> gris", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON> sen<PERSON><PERSON> verd", "block.minecraft.banner.stripe_right.light_blue": "<PERSON><PERSON> sen<PERSON><PERSON> blau clar", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON> sen<PERSON><PERSON> gris clar", "block.minecraft.banner.stripe_right.lime": "<PERSON><PERSON> sen<PERSON><PERSON> lima", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON> sen<PERSON>tre magenta", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON> se<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON> sen<PERSON><PERSON> rò<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON> sen<PERSON><PERSON> violet", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON> sen<PERSON><PERSON> roge", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON> se<PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> sen<PERSON><PERSON> jaune", "block.minecraft.banner.stripe_top.black": "Cap negre", "block.minecraft.banner.stripe_top.blue": "Cap blau", "block.minecraft.banner.stripe_top.brown": "Cap brun", "block.minecraft.banner.stripe_top.cyan": "Cap cian", "block.minecraft.banner.stripe_top.gray": "Cap gris", "block.minecraft.banner.stripe_top.green": "Cap verd", "block.minecraft.banner.stripe_top.light_blue": "Cap blau clar", "block.minecraft.banner.stripe_top.light_gray": "Cap gris clar", "block.minecraft.banner.stripe_top.lime": "Cap lima", "block.minecraft.banner.stripe_top.magenta": "Cap magenta", "block.minecraft.banner.stripe_top.orange": "Cap irange", "block.minecraft.banner.stripe_top.pink": "Cap ròse", "block.minecraft.banner.stripe_top.purple": "Cap violet", "block.minecraft.banner.stripe_top.red": "Cap roge", "block.minecraft.banner.stripe_top.white": "Cap blanc", "block.minecraft.banner.stripe_top.yellow": "Cap jaune", "block.minecraft.banner.triangle_bottom.black": "Mantelat negre", "block.minecraft.banner.triangle_bottom.blue": "Mantelat blau", "block.minecraft.banner.triangle_bottom.brown": "Mantelat brun", "block.minecraft.banner.triangle_bottom.cyan": "Mantelat cian", "block.minecraft.banner.triangle_bottom.gray": "Mantelat gris", "block.minecraft.banner.triangle_bottom.green": "Mantelat verd", "block.minecraft.banner.triangle_bottom.light_blue": "Mantelat gris clar", "block.minecraft.banner.triangle_bottom.light_gray": "Mantelat gris clar", "block.minecraft.banner.triangle_bottom.lime": "Mantelat lima", "block.minecraft.banner.triangle_bottom.magenta": "Mantelat magenta", "block.minecraft.banner.triangle_bottom.orange": "Mantelat irange", "block.minecraft.banner.triangle_bottom.pink": "Mantelat ròse", "block.minecraft.banner.triangle_bottom.purple": "Mantelat violet", "block.minecraft.banner.triangle_bottom.red": "Mantelat roge", "block.minecraft.banner.triangle_bottom.white": "Mantelat blanc", "block.minecraft.banner.triangle_bottom.yellow": "Mantelat jaune", "block.minecraft.banner.triangle_top.black": "Mantelat reversat negre", "block.minecraft.banner.triangle_top.blue": "Mantelat reversat blau", "block.minecraft.banner.triangle_top.brown": "Mantelat reversat brun", "block.minecraft.banner.triangle_top.cyan": "Mantelat reversat cian", "block.minecraft.banner.triangle_top.gray": "Mantelat reversat gris", "block.minecraft.banner.triangle_top.green": "Mantelat reversat verd", "block.minecraft.banner.triangle_top.light_blue": "Mantelat reversat blau clar", "block.minecraft.banner.triangle_top.light_gray": "Mantelat reversat gris clar", "block.minecraft.banner.triangle_top.lime": "Mantelat reversat lima", "block.minecraft.banner.triangle_top.magenta": "Mantelat reversat magenta", "block.minecraft.banner.triangle_top.orange": "Mantelat reversat irange", "block.minecraft.banner.triangle_top.pink": "Mantelat reversat ròse", "block.minecraft.banner.triangle_top.purple": "Mantelat reversat violet", "block.minecraft.banner.triangle_top.red": "Mantelat reversat roge", "block.minecraft.banner.triangle_top.white": "Mantelat reversat blanc", "block.minecraft.banner.triangle_top.yellow": "Mantelat reversat jaune", "block.minecraft.banner.triangles_bottom.black": "Encaissadura negra en poncha", "block.minecraft.banner.triangles_bottom.blue": "Encaissadura blava en poncha", "block.minecraft.banner.triangles_bottom.brown": "Encaissadura bruna en poncha", "block.minecraft.banner.triangles_bottom.cyan": "Encaissadura cian en poncha", "block.minecraft.banner.triangles_bottom.gray": "Encaissadura grisa en poncha", "block.minecraft.banner.triangles_bottom.green": "Encaissadura verda en poncha", "block.minecraft.banner.triangles_bottom.light_blue": "Encaissadura blau clar en poncha", "block.minecraft.banner.triangles_bottom.light_gray": "Encaissadura gris clar en poncha", "block.minecraft.banner.triangles_bottom.lime": "Encaissadura lima en poncha", "block.minecraft.banner.triangles_bottom.magenta": "Encaissadura magenta en poncha", "block.minecraft.banner.triangles_bottom.orange": "Encaissadura iranja en poncha", "block.minecraft.banner.triangles_bottom.pink": "Encaissadura ròsa en poncha", "block.minecraft.banner.triangles_bottom.purple": "Encaissadura violeta en poncha", "block.minecraft.banner.triangles_bottom.red": "Encaissadura roja en poncha", "block.minecraft.banner.triangles_bottom.white": "Encaissadura blanca en poncha", "block.minecraft.banner.triangles_bottom.yellow": "Encaissadura jauna en poncha", "block.minecraft.banner.triangles_top.black": "Encaissadura negra en cap", "block.minecraft.banner.triangles_top.blue": "Encaissadura blava en cap", "block.minecraft.banner.triangles_top.brown": "Encaissadura bruna en cap", "block.minecraft.banner.triangles_top.cyan": "Encaissadura cian en cap", "block.minecraft.banner.triangles_top.gray": "Encaissadura grisa en cap", "block.minecraft.banner.triangles_top.green": "Encaissadura verda en cap", "block.minecraft.banner.triangles_top.light_blue": "Encaissadura blau clar en cap", "block.minecraft.banner.triangles_top.light_gray": "Encaissadura gris clar en cap", "block.minecraft.banner.triangles_top.lime": "Encaissadura lima en cap", "block.minecraft.banner.triangles_top.magenta": "Encaissadura magenta en cap", "block.minecraft.banner.triangles_top.orange": "Encaissadura iranja en cap", "block.minecraft.banner.triangles_top.pink": "Encaissadura ròsa en cap", "block.minecraft.banner.triangles_top.purple": "Encaissadura violeta en cap", "block.minecraft.banner.triangles_top.red": "Encaissadura roja en cap", "block.minecraft.banner.triangles_top.white": "Encaissadura blanca en cap", "block.minecraft.banner.triangles_top.yellow": "Encaissadura jauna en cap", "block.minecraft.barrel": "Barrica", "block.minecraft.barrier": "<PERSON><PERSON><PERSON> invisibla", "block.minecraft.basalt": "Basalte", "block.minecraft.beacon": "Balisa", "block.minecraft.beacon.primary": "<PERSON><PERSON>", "block.minecraft.beacon.secondary": "Poder Segond", "block.minecraft.bed.no_sleep": "Podètz dormir sonque la nuèch o pendent los auratges", "block.minecraft.bed.not_safe": "Podètz pas dormir ara; i a de monstres a proximitat", "block.minecraft.bed.obstructed": "Aquest lièch es obstruït", "block.minecraft.bed.occupied": "Aqueste lièch es ocupat", "block.minecraft.bed.too_far_away": "Podètz pas dormir ara; lo lièch es tròp luènh", "block.minecraft.bedrock": "Ròca de basa", "block.minecraft.bee_nest": "<PERSON><PERSON>", "block.minecraft.beehive": "Bornhon artificiau", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> granda", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON> de <PERSON><PERSON><PERSON><PERSON><PERSON> granda", "block.minecraft.birch_button": "Boton en bèç", "block.minecraft.birch_door": "Pòrta en bèç", "block.minecraft.birch_fence": "Barralha en bèç", "block.minecraft.birch_fence_gate": "Pòrta de <PERSON>ha en bèç", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en bèç", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Busca de bèç", "block.minecraft.birch_planks": "Plancas de bèç", "block.minecraft.birch_pressure_plate": "Placa de pression en bèç", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "Pancarta en bèç", "block.minecraft.birch_slab": "Lausa en b<PERSON>ç", "block.minecraft.birch_stairs": "Escalièrs en bèç", "block.minecraft.birch_trapdoor": "Trapa en bèç", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en b<PERSON><PERSON>a", "block.minecraft.birch_wall_sign": "Pancarta en b<PERSON><PERSON> murala", "block.minecraft.birch_wood": "<PERSON><PERSON>", "block.minecraft.black_banner": "Bandièra negra", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON> negre", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON><PERSON> negra", "block.minecraft.black_candle_cake": "<PERSON><PERSON> amb candèla negra", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON> negra", "block.minecraft.black_concrete": "<PERSON><PERSON> negre", "block.minecraft.black_concrete_powder": "Polsa de beton negra", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON><PERSON> cuècha esmautada negra", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON> negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON> negre", "block.minecraft.black_stained_glass_pane": "Panèu de veire negre", "block.minecraft.black_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha negra", "block.minecraft.black_wool": "<PERSON> negra", "block.minecraft.blackstone": "Ròca negra", "block.minecraft.blackstone_slab": "Lausa de rò<PERSON> negra", "block.minecraft.blackstone_stairs": "Escalièrs de ròca negra", "block.minecraft.blackstone_wall": "<PERSON><PERSON> de <PERSON> negra", "block.minecraft.blast_furnace": "Fornèl de fusion", "block.minecraft.blue_banner": "Bandièra blava", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON> blau", "block.minecraft.blue_candle": "Cand<PERSON>la blava", "block.minecraft.blue_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la blava", "block.minecraft.blue_carpet": "Moqueta blava", "block.minecraft.blue_concrete": "<PERSON><PERSON> blau", "block.minecraft.blue_concrete_powder": "Polsa de beton blava", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON> cuècha esmautada blava", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> blau", "block.minecraft.blue_orchid": "Orquidèa blava", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON> blava", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON> blau", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON> de veire blau", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha blava", "block.minecraft.blue_wool": "Lana blava", "block.minecraft.bone_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bookshelf": "Bibliotèca", "block.minecraft.brain_coral": "<PERSON><PERSON>", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON><PERSON> <PERSON>er<PERSON>èu", "block.minecraft.brain_coral_fan": "Gorgònia de coralh de cervèu", "block.minecraft.brain_coral_wall_fan": "Gorgònia de coralh de cervèu murala", "block.minecraft.brewing_stand": "Alambic", "block.minecraft.brick_slab": "Lausa en bricas", "block.minecraft.brick_stairs": "Escalièrs en bricas", "block.minecraft.brick_wall": "<PERSON><PERSON> de bricas", "block.minecraft.bricks": "Bricas", "block.minecraft.brown_banner": "Bandièra bruna", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON> brun", "block.minecraft.brown_candle": "<PERSON>d<PERSON><PERSON> bruna", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> amb candèla bruna", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> bruna", "block.minecraft.brown_concrete": "<PERSON>on brun", "block.minecraft.brown_concrete_powder": "Polsa de beton bruna", "block.minecraft.brown_glazed_terracotta": "Tèrra cuècha esmautada bruna", "block.minecraft.brown_mushroom": "Campar<PERSON>u brun", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>òc de camparòu brun", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>er bruna", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON> brun", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON>u de veire brun", "block.minecraft.brown_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha bruna", "block.minecraft.brown_wool": "<PERSON> bruna", "block.minecraft.bubble_column": "Colomna de boti<PERSON>las", "block.minecraft.bubble_coral": "<PERSON><PERSON>", "block.minecraft.bubble_coral_block": "<PERSON><PERSON><PERSON><PERSON> <PERSON> de bot<PERSON>", "block.minecraft.bubble_coral_fan": "Gorgònia de coralh de botiòla", "block.minecraft.bubble_coral_wall_fan": "Gorgònia de coralh de botiòla murala", "block.minecraft.budding_amethyst": "Drusa d'ametista", "block.minecraft.bush": "<PERSON><PERSON>", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de cactus", "block.minecraft.cake": "Pastís", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Captaire <PERSON><PERSON><PERSON> calibrat", "block.minecraft.campfire": "<PERSON><PERSON><PERSON> de camp", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> amb cand<PERSON>la", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Taula de cartografia", "block.minecraft.carved_pumpkin": "Coja escultada", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "Aire de bauma", "block.minecraft.cave_vines": "<PERSON>nas de <PERSON>", "block.minecraft.cave_vines_plant": "Planta de lianas de bauma", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Blòc de comandas en cadena", "block.minecraft.cherry_button": "Boton en cerièis", "block.minecraft.cherry_door": "Pòrta en cerièis", "block.minecraft.cherry_fence": "Barralha en cerièis", "block.minecraft.cherry_fence_gate": "Pòrta de barralha en cerièis", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en cerièis", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "Busca de cerièis", "block.minecraft.cherry_planks": "Plancas en cerièis", "block.minecraft.cherry_pressure_plate": "Placa de pression en cerièis", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Pancarta en cerièis", "block.minecraft.cherry_slab": "Lausa en cerièis", "block.minecraft.cherry_stairs": "Escalièrs en cerièis", "block.minecraft.cherry_trapdoor": "Trapa en cerièis", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en cerièis murala", "block.minecraft.cherry_wall_sign": "Pancarta en cerièis murala", "block.minecraft.cherry_wood": "Fusta en cerièis", "block.minecraft.chest": "Còfre", "block.minecraft.chipped_anvil": "Enclutge bercat", "block.minecraft.chiseled_bookshelf": "Bibliotèca escultada", "block.minecraft.chiseled_copper": "Coire escultat", "block.minecraft.chiseled_deepslate": "Ardesa prigonda escultada", "block.minecraft.chiseled_nether_bricks": "Bricas dau Nether escultadas", "block.minecraft.chiseled_polished_blackstone": "Ròca negra escultada", "block.minecraft.chiseled_quartz_block": "Blòc de qüars escultat", "block.minecraft.chiseled_red_sandstone": "Gres roge escultat", "block.minecraft.chiseled_resin_bricks": "Bricas de resina escultadas", "block.minecraft.chiseled_sandstone": "Gres escultat", "block.minecraft.chiseled_stone_bricks": "Bricas de pèira escultadas", "block.minecraft.chiseled_tuff": "<PERSON><PERSON> es<PERSON>ltat", "block.minecraft.chiseled_tuff_bricks": "Bricas <PERSON>", "block.minecraft.chorus_flower": "Flor de <PERSON>", "block.minecraft.chorus_plant": "Planta de <PERSON>òrus", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Uèlh<PERSON><PERSON><PERSON> barrata", "block.minecraft.coal_block": "Blòc de carbon", "block.minecraft.coal_ore": "Minièra de carbon", "block.minecraft.coarse_dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "Pèiras d'ardesa prigonda", "block.minecraft.cobbled_deepslate_slab": "Lausa en pèiras d'ardesa prigonda", "block.minecraft.cobbled_deepslate_stairs": "Escalièrs en pèiras d'ardesa prigonda", "block.minecraft.cobbled_deepslate_wall": "Muret en pèiras d'ardesa prigonda", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Lausa <PERSON>", "block.minecraft.cobblestone_stairs": "Escalièrs de <PERSON>", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.cobweb": "Telaranha", "block.minecraft.cocoa": "Cacau", "block.minecraft.command_block": "Blòc de comandas", "block.minecraft.comparator": "Comparator de redstone", "block.minecraft.composter": "Compostador", "block.minecraft.conduit": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Blòc de coire", "block.minecraft.copper_bulb": "Ampola en coire", "block.minecraft.copper_door": "Pòrta en coire", "block.minecraft.copper_grate": "Grasilha en coire", "block.minecraft.copper_ore": "Minièra de coire", "block.minecraft.copper_trapdoor": "Trapa en coire", "block.minecraft.cornflower": "Blavet", "block.minecraft.cracked_deepslate_bricks": "Bricas d'ardesa prigonda fendilhadas", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON> d'ardesa prigonda fendilhadas", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON> dau <PERSON>her fendil<PERSON>as", "block.minecraft.cracked_polished_blackstone_bricks": "Bricas de ròca negra fendilhadas", "block.minecraft.cracked_stone_bricks": "Bricas de pèira fendilhadas", "block.minecraft.crafter": "Fabricator", "block.minecraft.crafting_table": "<PERSON><PERSON>", "block.minecraft.creaking_heart": "<PERSON>òr de c<PERSON>isseire", "block.minecraft.creeper_head": "Cap de creeper", "block.minecraft.creeper_wall_head": "Cap de creeper murau", "block.minecraft.crimson_button": "Boton cremesin", "block.minecraft.crimson_door": "Pòrta cremesina", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON> cremesina", "block.minecraft.crimson_fence_gate": "Pòrta de barralha cremesina", "block.minecraft.crimson_fungus": "Fonge cremesin", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta cremesina", "block.minecraft.crimson_hyphae": "Ifas cremesinas", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON> crem<PERSON>", "block.minecraft.crimson_planks": "Plancas cremesinas", "block.minecraft.crimson_pressure_plate": "Placa de pression cremesina", "block.minecraft.crimson_roots": "Raices cremesins", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON> cremesina", "block.minecraft.crimson_slab": "<PERSON><PERSON> cremesina", "block.minecraft.crimson_stairs": "Escaliè<PERSON> cremesi<PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON> cremesina", "block.minecraft.crimson_trapdoor": "Trapa cremesina", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjanta cremesina murala", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON> cremesina murala", "block.minecraft.crying_obsidian": "Obsidiana plorosa", "block.minecraft.cut_copper": "Coire talhat", "block.minecraft.cut_copper_slab": "Lausa en coire talhada", "block.minecraft.cut_copper_stairs": "Escalièrs en coire talhat", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON> roge talhat", "block.minecraft.cut_red_sandstone_slab": "<PERSON>sa en gres roge talhat", "block.minecraft.cut_sandstone": "<PERSON><PERSON> talhat", "block.minecraft.cut_sandstone_slab": "Lausa en gres talhat", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON>ian", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON>ian", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la cian", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_concrete": "<PERSON><PERSON> cian", "block.minecraft.cyan_concrete_powder": "Polsa de beton cian", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha es<PERSON>utada cian", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON>u de veire cian", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_wool": "<PERSON> c<PERSON>", "block.minecraft.damaged_anvil": "Enclutge dam<PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "<PERSON><PERSON> en euse escur", "block.minecraft.dark_oak_door": "Pòrta en euse escur", "block.minecraft.dark_oak_fence": "<PERSON>al<PERSON> en euse escur", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON> en euse escur", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en euse escur", "block.minecraft.dark_oak_leaves": "Fuèl<PERSON> d'euse escur", "block.minecraft.dark_oak_log": "Busca d'euse escur", "block.minecraft.dark_oak_planks": "Plancas d'euse escur", "block.minecraft.dark_oak_pressure_plate": "Placa de pression en euse escur", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON> d'euse escur", "block.minecraft.dark_oak_sign": "Pancarta en euse escur", "block.minecraft.dark_oak_slab": "<PERSON><PERSON> en euse escur", "block.minecraft.dark_oak_stairs": "Escalièrs en euse escur", "block.minecraft.dark_oak_trapdoor": "Trapa en euse escur", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en euse escur murala", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON> en euse escur murala", "block.minecraft.dark_oak_wood": "Fusta d'euse escur", "block.minecraft.dark_prismarine": "Prismarina escura", "block.minecraft.dark_prismarine_slab": "Lausa de prismarina escura", "block.minecraft.dark_prismarine_stairs": "Escalièrs en prismarina escura", "block.minecraft.daylight_detector": "Detector de lutz solara", "block.minecraft.dead_brain_coral": "<PERSON><PERSON> <PERSON>er<PERSON><PERSON><PERSON> mòrt", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON><PERSON> <PERSON> de cervèu mòrt", "block.minecraft.dead_brain_coral_fan": "Gorgònia de coralh de cervèu mòrt", "block.minecraft.dead_brain_coral_wall_fan": "Gorgònia de coralh de cervèu mòrt murala", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> <PERSON> bot<PERSON><PERSON><PERSON> mòrt", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> de boti<PERSON>la mòrt", "block.minecraft.dead_bubble_coral_fan": "Gorgònia de coralh de botiòla mòrt", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgònia de coralh de botiòla mòrt murala", "block.minecraft.dead_bush": "<PERSON>rb<PERSON> mòrt", "block.minecraft.dead_fire_coral": "<PERSON><PERSON> <PERSON> fu<PERSON> mòrt", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON><PERSON> <PERSON> de fuòc mòrt", "block.minecraft.dead_fire_coral_fan": "Gorgònia de coralh de fuòc mòrt", "block.minecraft.dead_fire_coral_wall_fan": "Gorgònia de coralh de fuòc mòrt murala", "block.minecraft.dead_horn_coral": "<PERSON><PERSON> cornut mòrt", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>h cornut mòrt", "block.minecraft.dead_horn_coral_fan": "Gorgònia de coralh cornut mòrt", "block.minecraft.dead_horn_coral_wall_fan": "Gorgònia de coralh cornut mòrt murala", "block.minecraft.dead_tube_coral": "Coralh tubular mòrt", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON><PERSON> <PERSON>h tubular mòrt", "block.minecraft.dead_tube_coral_fan": "Gorgònia de coralh tubular mòrt", "block.minecraft.dead_tube_coral_wall_fan": "Gorgònia de coralh tubular mòrt murala", "block.minecraft.decorated_pot": "<PERSON><PERSON> ondrat", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON> prigonda", "block.minecraft.deepslate_brick_slab": "Lausa en bricas d'ardesa prigonda", "block.minecraft.deepslate_brick_stairs": "Escalièrs en bricas d'ardesa prigonda", "block.minecraft.deepslate_brick_wall": "Muret en bricas d'ardesa prigonda", "block.minecraft.deepslate_bricks": "Bricas d'ardesa prigonda", "block.minecraft.deepslate_coal_ore": "Minièra de carbon en ardesa prigonda", "block.minecraft.deepslate_copper_ore": "Minièra de coire en ardesa prigonda", "block.minecraft.deepslate_diamond_ore": "Minièra de diamant en ardesa prigonda", "block.minecraft.deepslate_emerald_ore": "Minièra d'esmerauda en ardesa prigonda", "block.minecraft.deepslate_gold_ore": "Minièra d'aur en ardesa prigonda", "block.minecraft.deepslate_iron_ore": "Minièra de fèrre en ardesa prigonda", "block.minecraft.deepslate_lapis_ore": "Minièra de lapislazuli en ardesa prigonda", "block.minecraft.deepslate_redstone_ore": "Minièra de redstone en ardesa prigonda", "block.minecraft.deepslate_tile_slab": "Lausa en rajòlas d'ardesa prigonda", "block.minecraft.deepslate_tile_stairs": "Escalièrs en rajòlas d'ardesa prigonda", "block.minecraft.deepslate_tile_wall": "Muret en rajòlas d'ardesa prigonda", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON>'ardes<PERSON> prigonda", "block.minecraft.detector_rail": "Ralh detector", "block.minecraft.diamond_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_ore": "Minièra <PERSON>", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Lausa de diorita", "block.minecraft.diorite_stairs": "Escalièrs de diorita", "block.minecraft.diorite_wall": "<PERSON><PERSON>", "block.minecraft.dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON>", "block.minecraft.dispenser": "Distribuïdor", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON> de dragon", "block.minecraft.dragon_head": "Cap de dragon", "block.minecraft.dragon_wall_head": "Cap de dragon murau", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON> se<PERSON>", "block.minecraft.dried_kelp_block": "<PERSON><PERSON>ò<PERSON> d'augas secadas", "block.minecraft.dripstone_block": "Blòc d'espeleotèma", "block.minecraft.dropper": "Donaire", "block.minecraft.emerald_block": "<PERSON><PERSON><PERSON><PERSON>'<PERSON>auda", "block.minecraft.emerald_ore": "Minièra d'es<PERSON>auda", "block.minecraft.enchanting_table": "<PERSON><PERSON> d'encantament", "block.minecraft.end_gateway": "Palanca de l'End", "block.minecraft.end_portal": "Portau de l'End", "block.minecraft.end_portal_frame": "Quadre dau portau de l'End", "block.minecraft.end_rod": "Barra de l'End", "block.minecraft.end_stone": "Ròca de l'End", "block.minecraft.end_stone_brick_slab": "Lausa en bricas de pèira de l'End", "block.minecraft.end_stone_brick_stairs": "Escalièrs en bricas de pèira de l'End", "block.minecraft.end_stone_brick_wall": "Muret en bricas de pèira de l'End", "block.minecraft.end_stone_bricks": "Bricas de pèira de l'End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON> d'ender", "block.minecraft.exposed_chiseled_copper": "Coire escultat expausat", "block.minecraft.exposed_copper": "Coire expausat", "block.minecraft.exposed_copper_bulb": "Ampola en coire expausada", "block.minecraft.exposed_copper_door": "Pòrta en coire expausada", "block.minecraft.exposed_copper_grate": "Grasilha en coire expausada", "block.minecraft.exposed_copper_trapdoor": "Trapa en coire expausada", "block.minecraft.exposed_cut_copper": "Coire talhat expausat", "block.minecraft.exposed_cut_copper_slab": "Lausa en coire talhada expausada", "block.minecraft.exposed_cut_copper_stairs": "Escalièrs en coire talhat expausat", "block.minecraft.farmland": "<PERSON><PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> fu<PERSON>", "block.minecraft.fire_coral_fan": "Gorgònia de coralh de fuòc", "block.minecraft.fire_coral_wall_fan": "Gorgònia de coralh de fuòc murala", "block.minecraft.firefly_bush": "<PERSON><PERSON> a lusetas", "block.minecraft.fletching_table": "<PERSON><PERSON> d'arqueria", "block.minecraft.flower_pot": "<PERSON><PERSON>", "block.minecraft.flowering_azalea": "Azalèa florida", "block.minecraft.flowering_azalea_leaves": "Fuèlhas d'azalèa florida", "block.minecraft.frogspawn": "Uòus de grano<PERSON>ha", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.furnace": "Forn", "block.minecraft.gilded_blackstone": "Ròca negra daurada", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.glow_lichen": "Liquèn lumin<PERSON>", "block.minecraft.glowstone": "<PERSON>èira luminosa", "block.minecraft.gold_block": "<PERSON><PERSON><PERSON><PERSON>'aur", "block.minecraft.gold_ore": "Minièra <PERSON>'aur", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Lausa en granit", "block.minecraft.granite_stairs": "Escalièrs en granit", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON><PERSON> d'èrba", "block.minecraft.gravel": "Grava", "block.minecraft.gray_banner": "Band<PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la g<PERSON>a", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.gray_concrete": "<PERSON><PERSON> gris", "block.minecraft.gray_concrete_powder": "Polsa de beton grisa", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha esmautada grisa", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_stained_glass_pane": "Panèu de veire gris", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON> grisa", "block.minecraft.gray_wool": "<PERSON> g<PERSON>", "block.minecraft.green_banner": "Bandièra verda", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON> verd", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.green_candle_cake": "<PERSON><PERSON> amb candèla verda", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON> verda", "block.minecraft.green_concrete": "<PERSON><PERSON> verd", "block.minecraft.green_concrete_powder": "Polsa de beton verda", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha esmautada verda", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON> verda", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> verd", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON>u de veire verd", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha verda", "block.minecraft.green_wool": "<PERSON> verda", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "Raices penjants", "block.minecraft.hay_block": "Bala de fen", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON><PERSON> pesuc", "block.minecraft.heavy_weighted_pressure_plate": "Placa de pression per pes elevat", "block.minecraft.honey_block": "<PERSON><PERSON><PERSON><PERSON> de mèu", "block.minecraft.honeycomb_block": "Blòc de bresca", "block.minecraft.hopper": "Embut", "block.minecraft.horn_coral": "<PERSON>h cornut", "block.minecraft.horn_coral_block": "<PERSON><PERSON><PERSON><PERSON> <PERSON> cornut", "block.minecraft.horn_coral_fan": "Gorgònia de coralh cornut", "block.minecraft.horn_coral_wall_fan": "Gorgònia de coralh cornut murala", "block.minecraft.ice": "Glaç", "block.minecraft.infested_chiseled_stone_bricks": "Bricas de pèira escultada infestadas", "block.minecraft.infested_cobblestone": "Pèiras infestadas", "block.minecraft.infested_cracked_stone_bricks": "Bricas de pèira fendilhada infestadas", "block.minecraft.infested_deepslate": "Ardesa prigonda infestada", "block.minecraft.infested_mossy_stone_bricks": "Bricas de pèira mofuda infestadas", "block.minecraft.infested_stone": "Ròca infestada", "block.minecraft.infested_stone_bricks": "Bricas de pèira infestadas", "block.minecraft.iron_bars": "Barrets de fèrre", "block.minecraft.iron_block": "<PERSON>lòc de fèrre", "block.minecraft.iron_door": "Pòrta en fèrre", "block.minecraft.iron_ore": "Minièra de fèrre", "block.minecraft.iron_trapdoor": "Trapa en fèrre", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON> coja", "block.minecraft.jigsaw": "<PERSON><PERSON>ò<PERSON>", "block.minecraft.jukebox": "Tocadisques", "block.minecraft.jungle_button": "<PERSON><PERSON>", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON> de j<PERSON>", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON> de jungla", "block.minecraft.jungle_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de <PERSON>", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "Busca de jungla", "block.minecraft.jungle_planks": "Plancas de jungla", "block.minecraft.jungle_pressure_plate": "Placa de pression de jungla", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_slab": "<PERSON><PERSON>", "block.minecraft.jungle_stairs": "Esca<PERSON><PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de jungla murala", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON><PERSON> de j<PERSON>la murala", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "<PERSON>a", "block.minecraft.kelp_plant": "<PERSON><PERSON> d'auga", "block.minecraft.ladder": "Escala", "block.minecraft.lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lapis_block": "Blòc de lapislazuli", "block.minecraft.lapis_ore": "Minièra de lapislazuli", "block.minecraft.large_amethyst_bud": "Cristau d'ametista grand", "block.minecraft.large_fern": "Faugu<PERSON><PERSON><PERSON> granda", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Pairòla de lava", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON> mò<PERSON>", "block.minecraft.lectern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lever": "Agre", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "Bandièra blau clar", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la blau clar", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_concrete": "<PERSON><PERSON> blau clar", "block.minecraft.light_blue_concrete_powder": "Polsa de beton blau clar", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON> cuècha esmautada blau clar", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_stained_glass_pane": "<PERSON><PERSON><PERSON> de veire blau clar", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha blau clar", "block.minecraft.light_blue_wool": "<PERSON> blau clar", "block.minecraft.light_gray_banner": "Bandièra gris clar", "block.minecraft.light_gray_bed": "<PERSON>èch gris clar", "block.minecraft.light_gray_candle": "Cand<PERSON>la gris clar", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON> amb candèla gris clar", "block.minecraft.light_gray_carpet": "<PERSON>queta gris clar", "block.minecraft.light_gray_concrete": "<PERSON>on gris clar", "block.minecraft.light_gray_concrete_powder": "Polsa de beton gris clar", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON> cuècha esmautada gris clar", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON> gris clar", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> gris clar", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON>u de veire gris clar", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha gris clar", "block.minecraft.light_gray_wool": "<PERSON> gris clar", "block.minecraft.light_weighted_pressure_plate": "Placa de pression per pes leugièr", "block.minecraft.lightning_rod": "Paratron", "block.minecraft.lilac": "<PERSON><PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "Nimfèia", "block.minecraft.lime_banner": "Bandièra lima", "block.minecraft.lime_bed": "Lièch lima", "block.minecraft.lime_candle": "Candèla lima", "block.minecraft.lime_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la blanca", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON> lima", "block.minecraft.lime_concrete": "<PERSON><PERSON> lima", "block.minecraft.lime_concrete_powder": "Polsa de beton lima", "block.minecraft.lime_glazed_terracotta": "Tèrra cuècha esmautada lima", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON> verd clar", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON> lima", "block.minecraft.lime_stained_glass_pane": "Panèu de veire lima", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON> cuècha verd clar", "block.minecraft.lime_wool": "Lana lima", "block.minecraft.lodestone": "Ma<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Telièr", "block.minecraft.magenta_banner": "Bandièra magenta", "block.minecraft.magenta_bed": "Lièch magenta", "block.minecraft.magenta_candle": "Candèla magenta", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON> amb candèla magenta", "block.minecraft.magenta_carpet": "Moqueta magenta", "block.minecraft.magenta_concrete": "Beton magenta", "block.minecraft.magenta_concrete_powder": "Polsa de beton magenta", "block.minecraft.magenta_glazed_terracotta": "Tèrra cuècha esmautada magenta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>lker magenta", "block.minecraft.magenta_stained_glass": "Veire magenta", "block.minecraft.magenta_stained_glass_pane": "Panèu de veire magenta", "block.minecraft.magenta_terracotta": "Tèrra cuècha magenta", "block.minecraft.magenta_wool": "Lana magenta", "block.minecraft.magma_block": "Blòc de magma", "block.minecraft.mangrove_button": "Boton en manglièr", "block.minecraft.mangrove_door": "Pòrta en manglièr", "block.minecraft.mangrove_fence": "Barralha en manglièr", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON>ha en manglièr", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en manglièr", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_log": "Busca de manglièr", "block.minecraft.mangrove_planks": "Plancas de manglièr", "block.minecraft.mangrove_pressure_plate": "Placa de pression en manglièr", "block.minecraft.mangrove_propagule": "Propagula de manglièr", "block.minecraft.mangrove_roots": "Raices de manglièr", "block.minecraft.mangrove_sign": "Pancarta en manglièr", "block.minecraft.mangrove_slab": "Lausa en manglièr", "block.minecraft.mangrove_stairs": "Escalièrs en manglièr", "block.minecraft.mangrove_trapdoor": "Trapa en manglièr", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta en manglièr murala", "block.minecraft.mangrove_wall_sign": "Pancarta en manglièr murala", "block.minecraft.mangrove_wood": "<PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "Cristau d'ametista mejan", "block.minecraft.melon": "Pastèca", "block.minecraft.melon_stem": "Tija de pastèca", "block.minecraft.moss_block": "<PERSON><PERSON><PERSON><PERSON> de mofa", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON> de mofa", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON> mofuda<PERSON>", "block.minecraft.mossy_cobblestone_slab": "Lausa de <PERSON> mofudas", "block.minecraft.mossy_cobblestone_stairs": "Escalièrs en pèiras mofudas", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> <PERSON><PERSON><PERSON> mofudas", "block.minecraft.mossy_stone_brick_slab": "Lausa en bricas de pèiras mofudas", "block.minecraft.mossy_stone_brick_stairs": "Escalièrs en bricas de pèiras mofudas", "block.minecraft.mossy_stone_brick_wall": "Muret en bricas de pèiras mofudas", "block.minecraft.mossy_stone_bricks": "Bricas de pèira mofudas", "block.minecraft.moving_piston": "Piston en moviment", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Lausa en bricas de fanga", "block.minecraft.mud_brick_stairs": "Escalièrs en bricas de fanga", "block.minecraft.mud_brick_wall": "Muret en bricas de fanga", "block.minecraft.mud_bricks": "Bricas de fanga", "block.minecraft.muddy_mangrove_roots": "Raices de manglièr fangoses", "block.minecraft.mushroom_stem": "<PERSON><PERSON>ò<PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Barralha en bricas dau Nether", "block.minecraft.nether_brick_slab": "Lausa en bricas dau Net<PERSON>", "block.minecraft.nether_brick_stairs": "Escalièrs en bricas dau Nether", "block.minecraft.nether_brick_wall": "Muret en bricas dau Nether", "block.minecraft.nether_bricks": "<PERSON><PERSON><PERSON> da<PERSON>", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON> d'aur dau <PERSON>", "block.minecraft.nether_portal": "Portau dau Net<PERSON>", "block.minecraft.nether_quartz_ore": "Minièra de qüars dau Net<PERSON>", "block.minecraft.nether_sprouts": "Brots dau Nether", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "<PERSON><PERSON><PERSON>c de botiòlas dau <PERSON>", "block.minecraft.netherite_block": "Blòc de netherita", "block.minecraft.netherrack": "<PERSON><PERSON><PERSON>", "block.minecraft.note_block": "<PERSON><PERSON><PERSON><PERSON> musicau", "block.minecraft.oak_button": "Boton en euse", "block.minecraft.oak_door": "Pòrta en euse", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON> en euse", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON> en euse", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en euse", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON>'<PERSON>", "block.minecraft.oak_log": "Busca d'euse", "block.minecraft.oak_planks": "Plancas d'euse", "block.minecraft.oak_pressure_plate": "Placa de pression en euse", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON> d'euse", "block.minecraft.oak_sign": "Pan<PERSON><PERSON> en euse", "block.minecraft.oak_slab": "<PERSON><PERSON> en euse", "block.minecraft.oak_stairs": "Escalièrs en euse", "block.minecraft.oak_trapdoor": "Trapa en euse", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en euse murala", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON> en euse murala", "block.minecraft.oak_wood": "<PERSON><PERSON> d'euse", "block.minecraft.observer": "Observator", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "Granolum <PERSON>cre", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON><PERSON> ominosa", "block.minecraft.open_eyeblossom": "Uèlhquidèa obèrta", "block.minecraft.orange_banner": "Bandièra i<PERSON>", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> amb cand<PERSON>", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON> i<PERSON>e", "block.minecraft.orange_concrete_powder": "Polsa de beton iranja", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha es<PERSON>utada iranja", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON>lker i<PERSON>", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panèu de veire i<PERSON>e", "block.minecraft.orange_terracotta": "<PERSON><PERSON><PERSON> i<PERSON>", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_wool": "<PERSON>", "block.minecraft.oxeye_daisy": "Margarida", "block.minecraft.oxidized_chiseled_copper": "Coire escultat oxidat", "block.minecraft.oxidized_copper": "Coire oxidat", "block.minecraft.oxidized_copper_bulb": "Ampola en coire oxidada", "block.minecraft.oxidized_copper_door": "Pòrta en coire oxidada", "block.minecraft.oxidized_copper_grate": "Grasilha en coire oxidada", "block.minecraft.oxidized_copper_trapdoor": "Trapa en coire oxidada", "block.minecraft.oxidized_cut_copper": "Coire talhat oxidat", "block.minecraft.oxidized_cut_copper_slab": "Lausa en coire talhada oxidada", "block.minecraft.oxidized_cut_copper_stairs": "Escalièrs en coire talhat oxidat", "block.minecraft.packed_ice": "Glaç compactat", "block.minecraft.packed_mud": "Fanga compactada", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON> palla pen<PERSON>ta", "block.minecraft.pale_moss_block": "<PERSON><PERSON><PERSON><PERSON> de mofa palla", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON> de mofa palla", "block.minecraft.pale_oak_button": "Boton en euse palle", "block.minecraft.pale_oak_door": "Pòrta en euse palle", "block.minecraft.pale_oak_fence": "Barralha en euse palle", "block.minecraft.pale_oak_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON>ha en euse palle", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en euse palle", "block.minecraft.pale_oak_leaves": "Fuèlhas d'euse palle", "block.minecraft.pale_oak_log": "Busca d'euse palle", "block.minecraft.pale_oak_planks": "Plancas d'euse palle", "block.minecraft.pale_oak_pressure_plate": "Placa de pression en euse palle", "block.minecraft.pale_oak_sapling": "<PERSON>rel<PERSON> d'euse palle", "block.minecraft.pale_oak_sign": "Pancarta en euse palle", "block.minecraft.pale_oak_slab": "Lausa en euse palle", "block.minecraft.pale_oak_stairs": "Escalièrs en euse palle", "block.minecraft.pale_oak_trapdoor": "Trapa en euse palle", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en euse palle murala", "block.minecraft.pale_oak_wall_sign": "Pancar<PERSON> en euse palle murala", "block.minecraft.pale_oak_wood": "Fusta d'euse palle", "block.minecraft.pearlescent_froglight": "Granolum nacrat", "block.minecraft.peony": "<PERSON><PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "Lausa en euse petrificada", "block.minecraft.piglin_head": "Cap de piglin", "block.minecraft.piglin_wall_head": "Cap de piglin mural", "block.minecraft.pink_banner": "Bandiè<PERSON> r<PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> amb cand<PERSON><PERSON> ròsa", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON> rò<PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON> rò<PERSON>", "block.minecraft.pink_concrete_powder": "Polsa de beton ròsa", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha esmautada ròsa", "block.minecraft.pink_petals": "Petals ròses", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "Panèu de veire ròse", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON> ròsa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_wool": "<PERSON>", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Cap de piston", "block.minecraft.pitcher_crop": "Plançon de planta jarra", "block.minecraft.pitcher_plant": "<PERSON>a jarra", "block.minecraft.player_head": "Cap de jogaire", "block.minecraft.player_head.named": "Cap de %s", "block.minecraft.player_wall_head": "Cap de jogaire murau", "block.minecraft.podzol": "Podzòl", "block.minecraft.pointed_dripstone": "Espeleotèma ponchuda", "block.minecraft.polished_andesite": "<PERSON><PERSON> polida", "block.minecraft.polished_andesite_slab": "Lausa d'andesita polida", "block.minecraft.polished_andesite_stairs": "Escalièrs d'andesita polida", "block.minecraft.polished_basalt": "Basalte polit", "block.minecraft.polished_blackstone": "Ròca negra polida", "block.minecraft.polished_blackstone_brick_slab": "Lausa en bricas de ròca negra polida", "block.minecraft.polished_blackstone_brick_stairs": "Escalièrs en bricas de ròca negra polida", "block.minecraft.polished_blackstone_brick_wall": "Muret en bricas de ròca negra polida", "block.minecraft.polished_blackstone_bricks": "Bricas de ròca negra polida", "block.minecraft.polished_blackstone_button": "Boton de ròca negra polida", "block.minecraft.polished_blackstone_pressure_plate": "Placa de pression en ròca negra polida", "block.minecraft.polished_blackstone_slab": "Lausa de ròca negra polida", "block.minecraft.polished_blackstone_stairs": "Escalièrs de ròca negra polida", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> de ròca negra polida", "block.minecraft.polished_deepslate": "Ardesa prigonda polida", "block.minecraft.polished_deepslate_slab": "Lausa en ardesa prigonda polida", "block.minecraft.polished_deepslate_stairs": "Escalièrs en ardesa prigonda polida", "block.minecraft.polished_deepslate_wall": "<PERSON>ret en ardesa prigonda polida", "block.minecraft.polished_diorite": "<PERSON><PERSON>ta polida", "block.minecraft.polished_diorite_slab": "Lausa en diorita polida", "block.minecraft.polished_diorite_stairs": "Escalièrs en diorit polida", "block.minecraft.polished_granite": "Granit polit", "block.minecraft.polished_granite_slab": "Lausa en granit polit", "block.minecraft.polished_granite_stairs": "Escalièrs en granit polit", "block.minecraft.polished_tuff": "<PERSON><PERSON> polit", "block.minecraft.polished_tuff_slab": "Lausa de tuf polit", "block.minecraft.polished_tuff_stairs": "Escalièrs de tuf polit", "block.minecraft.polished_tuff_wall": "<PERSON>r de tuf polit", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "<PERSON><PERSON><PERSON> de cacièr en pòt", "block.minecraft.potted_allium": "Allium en pòt", "block.minecraft.potted_azalea_bush": "Azalèa en pòt", "block.minecraft.potted_azure_bluet": "Houstonia caerulea en pòt", "block.minecraft.potted_bamboo": "Bambó en pòt", "block.minecraft.potted_birch_sapling": "<PERSON>relh de bèç en pòt", "block.minecraft.potted_blue_orchid": "Orquidèa blava en pòt", "block.minecraft.potted_brown_mushroom": "Camparòu brun en pòt", "block.minecraft.potted_cactus": "Cactus en pòt", "block.minecraft.potted_cherry_sapling": "Grelh de cerièis en pòt", "block.minecraft.potted_closed_eyeblossom": "Uèlhquidèa obèrta en pot", "block.minecraft.potted_cornflower": "Blavet en pòt", "block.minecraft.potted_crimson_fungus": "Fonge cremesin en pòt", "block.minecraft.potted_crimson_roots": "Raices cremesins en pòt", "block.minecraft.potted_dandelion": "Pissalach en pòt", "block.minecraft.potted_dark_oak_sapling": "Euse escur en pòt", "block.minecraft.potted_dead_bush": "Arbust mòrt en pòt", "block.minecraft.potted_fern": "Fauguièra en pòt", "block.minecraft.potted_flowering_azalea_bush": "Azalèa florida en pòt", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON><PERSON> de jungla en pòt", "block.minecraft.potted_lily_of_the_valley": "Muguet en pòt", "block.minecraft.potted_mangrove_propagule": "Propagula de manglièr en pòt", "block.minecraft.potted_oak_sapling": "Euse en pòt", "block.minecraft.potted_open_eyeblossom": "Uèlhquidèa barrata en pot", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON>a iranja en pòt", "block.minecraft.potted_oxeye_daisy": "Margarida en pòt", "block.minecraft.potted_pale_oak_sapling": "Euse palle en pòt", "block.minecraft.potted_pink_tulip": "<PERSON><PERSON><PERSON> rò<PERSON> en pòt", "block.minecraft.potted_poppy": "Rosèla en pòt", "block.minecraft.potted_red_mushroom": "Camparòu roge en pòt", "block.minecraft.potted_red_tulip": "<PERSON><PERSON><PERSON> roja en pòt", "block.minecraft.potted_spruce_sapling": "Grelh d'avet en pòt", "block.minecraft.potted_torchflower": "Entòrcha-flor en pot", "block.minecraft.potted_warped_fungus": "Fonge bescornut en pòt", "block.minecraft.potted_warped_roots": "Raices bescornuts en pòt", "block.minecraft.potted_white_tulip": "Tulipa blanca en pòt", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON> de wither en pòt", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "Pairòla de nèu pol<PERSON>osa", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON> propulsor", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Lausa en bricas de prismarina", "block.minecraft.prismarine_brick_stairs": "Escalièrs en bricas de prismarina", "block.minecraft.prismarine_bricks": "Bricas de prismarina", "block.minecraft.prismarine_slab": "Lausa de prismarina", "block.minecraft.prismarine_stairs": "Escalièrs en prismarina", "block.minecraft.prismarine_wall": "<PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Tija de coja", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON><PERSON> violeta", "block.minecraft.purple_bed": "Lièch violet", "block.minecraft.purple_candle": "<PERSON><PERSON><PERSON><PERSON> violeta", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la violeta", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.purple_concrete": "Beton violet", "block.minecraft.purple_concrete_powder": "Polsa de beton violeta", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha esmautada violeta", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Veire violet", "block.minecraft.purple_stained_glass_pane": "Panèu de veire violet", "block.minecraft.purple_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON> violeta", "block.minecraft.purple_wool": "<PERSON>", "block.minecraft.purpur_block": "<PERSON><PERSON><PERSON><PERSON>pra", "block.minecraft.purpur_pillar": "<PERSON><PERSON>", "block.minecraft.purpur_slab": "<PERSON><PERSON>", "block.minecraft.purpur_stairs": "Escaliè<PERSON>", "block.minecraft.quartz_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "<PERSON><PERSON>", "block.minecraft.quartz_slab": "Lausa en qüars", "block.minecraft.quartz_stairs": "Escalièrs en qüars", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Blòc de coire brut", "block.minecraft.raw_gold_block": "<PERSON><PERSON><PERSON><PERSON> d'aur brut", "block.minecraft.raw_iron_block": "Blòc de fèrre brut", "block.minecraft.red_banner": "Bandièra roja", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON> roge", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON> amb cand<PERSON><PERSON> roja", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON> roge", "block.minecraft.red_concrete_powder": "Polsa de beton roja", "block.minecraft.red_glazed_terracotta": "<PERSON>èrra cu<PERSON>cha esmautada roja", "block.minecraft.red_mushroom": "<PERSON>ar<PERSON><PERSON> roge", "block.minecraft.red_mushroom_block": "Blòc de camparòu roge", "block.minecraft.red_nether_brick_slab": "Lausa en bricas dau <PERSON> rojas", "block.minecraft.red_nether_brick_stairs": "Escalièrs en bricas dau <PERSON> rojas", "block.minecraft.red_nether_brick_wall": "<PERSON>ret en bricas dau <PERSON> rojas", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON> dau <PERSON>", "block.minecraft.red_sand": "Sabla roja", "block.minecraft.red_sandstone": "<PERSON><PERSON> roge", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON> de gres roge", "block.minecraft.red_sandstone_stairs": "Escalièrs en gres roge", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> de gres roge", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> roge", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON>u de veire roge", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON> roja", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON>", "block.minecraft.redstone_block": "Blòc de redstone", "block.minecraft.redstone_lamp": "Lum de redstone", "block.minecraft.redstone_ore": "Minièra de redstone", "block.minecraft.redstone_torch": "Entòrcha de redstone", "block.minecraft.redstone_wall_torch": "Entòrcha de redstone murala", "block.minecraft.redstone_wire": "Fiau de redstone", "block.minecraft.reinforced_deepslate": "Ardesa prigonda renforçada", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Blòc de comandas a repeticion", "block.minecraft.resin_block": "Blòc de resina", "block.minecraft.resin_brick_slab": "Lausa en bricas de resina", "block.minecraft.resin_brick_stairs": "Escalièrs en bricas de resina", "block.minecraft.resin_brick_wall": "Muret en bricas de resina", "block.minecraft.resin_bricks": "Bricas de resina", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> resin<PERSON>", "block.minecraft.respawn_anchor": "Ancora de reaparicion", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON> racino<PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sabla", "block.minecraft.sandstone": "Gres", "block.minecraft.sandstone_slab": "Lausa de <PERSON>", "block.minecraft.sandstone_stairs": "Escalièrs en gres", "block.minecraft.sandstone_wall": "<PERSON><PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalisaire de sculk", "block.minecraft.sculk_sensor": "Captaire sculk", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON><PERSON> sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Lantèrna marina", "block.minecraft.sea_pickle": "Cornisson de mar", "block.minecraft.seagrass": "Èrba marina", "block.minecraft.set_spawn": "Ponch de reaparicion definit", "block.minecraft.short_dry_grass": "<PERSON>rba corta seca", "block.minecraft.short_grass": "<PERSON><PERSON>a corta", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Cran d'esqueleta", "block.minecraft.skeleton_wall_skull": "Cran d'esqueleta murau", "block.minecraft.slime_block": "Blòc de Slime", "block.minecraft.small_amethyst_bud": "Cristau d'ametista pichon", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ona", "block.minecraft.smithing_table": "<PERSON><PERSON>", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Basalte lis", "block.minecraft.smooth_quartz": "<PERSON><PERSON><PERSON><PERSON> de qüars lis", "block.minecraft.smooth_quartz_slab": "Lausa en qüars lis", "block.minecraft.smooth_quartz_stairs": "Escalièrs en qüars lis", "block.minecraft.smooth_red_sandstone": "<PERSON>res roge lis", "block.minecraft.smooth_red_sandstone_slab": "Lausa en gres roge lis", "block.minecraft.smooth_red_sandstone_stairs": "Escalièrs en gres roge lis", "block.minecraft.smooth_sandstone": "Gres lis", "block.minecraft.smooth_sandstone_slab": "Lausa en gres lis", "block.minecraft.smooth_sandstone_stairs": "Escalièrs en gres lis", "block.minecraft.smooth_stone": "Ròca lisa", "block.minecraft.smooth_stone_slab": "Lausa de ròca lisa", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.snow": "<PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "Blòc de nèu", "block.minecraft.soul_campfire": "Fuòc de camp d'anmas", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "Sabla d<PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "Entòrcha d<PERSON>anmas", "block.minecraft.soul_wall_torch": "Entòrch<PERSON> d'anmas murala", "block.minecraft.spawn.not_valid": "Avètz pas de lièch o d'ancora de reaparicion cargada, o lo blòc es estat obstruït", "block.minecraft.spawner": "Generator de Mostres", "block.minecraft.spawner.desc1": "Interaccion amb un uòu d'aparicion :", "block.minecraft.spawner.desc2": "Definís lo tipe de creatura", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Flor sporifèra", "block.minecraft.spruce_button": "Boton en avet", "block.minecraft.spruce_door": "Pòrta en avet", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON> en avet", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON> en avet", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en avet", "block.minecraft.spruce_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_log": "Busca d'avet", "block.minecraft.spruce_planks": "Plancas d'avet", "block.minecraft.spruce_pressure_plate": "Placa de pression en avet", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON> d'avet", "block.minecraft.spruce_sign": "Pancarta en avet", "block.minecraft.spruce_slab": "<PERSON><PERSON> en avet", "block.minecraft.spruce_stairs": "Escalièrs en avet", "block.minecraft.spruce_trapdoor": "<PERSON>rapa en avet", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> en avet murala", "block.minecraft.spruce_wall_sign": "Pancar<PERSON> en avet murala", "block.minecraft.spruce_wood": "<PERSON><PERSON> d'avet", "block.minecraft.sticky_piston": "<PERSON><PERSON> peg<PERSON>", "block.minecraft.stone": "Ròca", "block.minecraft.stone_brick_slab": "Lausa en bricas de pèira", "block.minecraft.stone_brick_stairs": "Escalièrs en bricas de pèira", "block.minecraft.stone_brick_wall": "Muret en bricas de pèira", "block.minecraft.stone_bricks": "Bricas de pèira", "block.minecraft.stone_button": "Boton en pèira", "block.minecraft.stone_pressure_plate": "Placa de pression en ròca", "block.minecraft.stone_slab": "Lausa <PERSON>", "block.minecraft.stone_stairs": "Escalièrs de rò<PERSON>", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Busca de cacièr <PERSON>", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON> de caci<PERSON>", "block.minecraft.stripped_bamboo_block": "Blòc de bambó desruscat", "block.minecraft.stripped_birch_log": "Busca de bè<PERSON>", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "Busca de cerièis desruscada", "block.minecraft.stripped_cherry_wood": "Fusta de cerièis desruscada", "block.minecraft.stripped_crimson_hyphae": "Ifas cremesinas der<PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON> cremesi<PERSON>", "block.minecraft.stripped_dark_oak_log": "Busca d'euse escur deruschada", "block.minecraft.stripped_dark_oak_wood": "Fusta d'euse escur deruschada", "block.minecraft.stripped_jungle_log": "Busca de jungla derus<PERSON>", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON> de jungla der<PERSON>", "block.minecraft.stripped_mangrove_log": "Busca de manglièr <PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON> de mangliè<PERSON>", "block.minecraft.stripped_oak_log": "Busca d'euse deruschada", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON> d'euse deruschada", "block.minecraft.stripped_pale_oak_log": "Busca d'euse palle deruschada", "block.minecraft.stripped_pale_oak_wood": "Fusta d'euse palle deruschada", "block.minecraft.stripped_spruce_log": "Busca d'avet deruschada", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON> d'avet deruschada", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON> be<PERSON>", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON> be<PERSON> derus<PERSON>da", "block.minecraft.structure_block": "Blòc d'Estructura", "block.minecraft.structure_void": "Void d'estructura", "block.minecraft.sugar_cane": "Cana de sucre", "block.minecraft.sunflower": "Virasolelh", "block.minecraft.suspicious_gravel": "Grava suspècta", "block.minecraft.suspicious_sand": "Sabla suspècta", "block.minecraft.sweet_berry_bush": "Arbust de bagas doças", "block.minecraft.tall_dry_grass": "Èrba nauta seca", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON> nauta", "block.minecraft.tall_seagrass": "Èrba marina nauta", "block.minecraft.target": "Buta", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.test_block": "<PERSON> de tèst", "block.minecraft.test_instance_block": "Bloc d'instància de tèst", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Las explosions de TNT son pas activadas", "block.minecraft.torch": "Entòrcha", "block.minecraft.torchflower": "Entòrcha-flor", "block.minecraft.torchflower_crop": "Plançon d'entòrcha-flor", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON><PERSON> trapa", "block.minecraft.trial_spawner": "Generator de las espròvas", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "Coralh tubular", "block.minecraft.tube_coral_block": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>h tubular", "block.minecraft.tube_coral_fan": "Gorgònia de coralh tubular", "block.minecraft.tube_coral_wall_fan": "Gorgònia de coralh tubular murala", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Lausa en bricas de tuf", "block.minecraft.tuff_brick_stairs": "Escalièrs en bricas de tuf", "block.minecraft.tuff_brick_wall": "Muret en bricas de tuf", "block.minecraft.tuff_bricks": "Bricas de tuf", "block.minecraft.tuff_slab": "<PERSON><PERSON> de tuf", "block.minecraft.tuff_stairs": "Esca<PERSON><PERSON><PERSON> de tuf", "block.minecraft.tuff_wall": "<PERSON><PERSON> de tuf", "block.minecraft.turtle_egg": "Uòu de tartuga", "block.minecraft.twisting_vines": "<PERSON><PERSON>", "block.minecraft.twisting_vines_plant": "Planta de lianas torsadas", "block.minecraft.vault": "Còfrefòrt", "block.minecraft.verdant_froglight": "Granolum verdós", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "<PERSON>e dau void", "block.minecraft.wall_torch": "Entò<PERSON>a murala", "block.minecraft.warped_button": "<PERSON><PERSON> bescornut", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON> be<PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON> be<PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON> de <PERSON> besco<PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON> be<PERSON>", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta be<PERSON>", "block.minecraft.warped_hyphae": "<PERSON><PERSON> be<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "Plancas bescornudas", "block.minecraft.warped_pressure_plate": "Placa de pression bescornuda", "block.minecraft.warped_roots": "Raices bescornuts", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> be<PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON>", "block.minecraft.warped_stairs": "Escalièrs <PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON> be<PERSON>", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>ta bescorn<PERSON> murala", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> bescorn<PERSON> murala", "block.minecraft.warped_wart_block": "Blòc de botiòlas bescornudas dau <PERSON>", "block.minecraft.water": "Aiga", "block.minecraft.water_cauldron": "Pairòla d'aiga", "block.minecraft.waxed_chiseled_copper": "Coire escultat encerat", "block.minecraft.waxed_copper_block": "Blòc de coire encerat", "block.minecraft.waxed_copper_bulb": "Ampola en coire encerada", "block.minecraft.waxed_copper_door": "Pòrta en coire encerada", "block.minecraft.waxed_copper_grate": "Grasilha en coire encerada", "block.minecraft.waxed_copper_trapdoor": "Trapa en coire encerada", "block.minecraft.waxed_cut_copper": "Coire talhat encerat", "block.minecraft.waxed_cut_copper_slab": "Lausa en coire talhada encerada", "block.minecraft.waxed_cut_copper_stairs": "Escalièrs en coire talhat encerat", "block.minecraft.waxed_exposed_chiseled_copper": "Coire escultat expausat encerat", "block.minecraft.waxed_exposed_copper": "Coire expausat encerat", "block.minecraft.waxed_exposed_copper_bulb": "Ampola en coire expausada encerada", "block.minecraft.waxed_exposed_copper_door": "Pòrta en coire expausada encerada", "block.minecraft.waxed_exposed_copper_grate": "Grasilha en coire expausada encerada", "block.minecraft.waxed_exposed_copper_trapdoor": "Trapa en coire expausada encerada", "block.minecraft.waxed_exposed_cut_copper": "Coire talhat expausat encerat", "block.minecraft.waxed_exposed_cut_copper_slab": "Lausa en coire talhada expausada encerada", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escalièrs en coire talhat expausat encerat", "block.minecraft.waxed_oxidized_chiseled_copper": "Coire escultat oxidat encerat", "block.minecraft.waxed_oxidized_copper": "Coire oxidat encerat", "block.minecraft.waxed_oxidized_copper_bulb": "Ampola en coire oxidada encerada", "block.minecraft.waxed_oxidized_copper_door": "Pòrta en coire oxidada encerada", "block.minecraft.waxed_oxidized_copper_grate": "Grasilha en coire oxidada encerada", "block.minecraft.waxed_oxidized_copper_trapdoor": "Trapa en coire oxidada encerada", "block.minecraft.waxed_oxidized_cut_copper": "Coire talhat oxidat encerat", "block.minecraft.waxed_oxidized_cut_copper_slab": "Lausa en coire talhada oxidada encerada", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escalièrs en coire talhat oxidat encerat", "block.minecraft.waxed_weathered_chiseled_copper": "Coire escultat corroït encerat", "block.minecraft.waxed_weathered_copper": "Coire corroït encerat", "block.minecraft.waxed_weathered_copper_bulb": "Ampola en coire corroïda encerada", "block.minecraft.waxed_weathered_copper_door": "Pòrta en coire corroïda encerada", "block.minecraft.waxed_weathered_copper_grate": "Grasilha en coire corroïda encerada", "block.minecraft.waxed_weathered_copper_trapdoor": "Trapa en coire corroïda encerada", "block.minecraft.waxed_weathered_cut_copper": "Coire talhat corroït encerat", "block.minecraft.waxed_weathered_cut_copper_slab": "Lausa en coire talhada corroïda encerada", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escalièrs en coire talhat corroït encerat", "block.minecraft.weathered_chiseled_copper": "Coire escultat corroït", "block.minecraft.weathered_copper": "Coire corroït", "block.minecraft.weathered_copper_bulb": "Ampola en coire corroïda", "block.minecraft.weathered_copper_door": "Pòrta en coire corroïda", "block.minecraft.weathered_copper_grate": "Grasilha en coire corroïda", "block.minecraft.weathered_copper_trapdoor": "Trapa en coire corroïda", "block.minecraft.weathered_cut_copper": "Coire talhat corroït", "block.minecraft.weathered_cut_copper_slab": "Lausa en coire talhada corroïda", "block.minecraft.weathered_cut_copper_stairs": "Escalièrs en coire talhat corroït", "block.minecraft.weeping_vines": "<PERSON><PERSON> plorosas", "block.minecraft.weeping_vines_plant": "Planta de lianas plorosas", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON><PERSON> trempada", "block.minecraft.wheat": "Culturas de blat", "block.minecraft.white_banner": "Bandièra blanca", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON><PERSON> blanca", "block.minecraft.white_candle_cake": "<PERSON><PERSON> amb cand<PERSON>la blanca", "block.minecraft.white_carpet": "Moqueta blanca", "block.minecraft.white_concrete": "<PERSON><PERSON> blanc", "block.minecraft.white_concrete_powder": "Polsa de beton blanca", "block.minecraft.white_glazed_terracotta": "Tèrra cu<PERSON>cha esmautada blanca", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_stained_glass_pane": "Pan<PERSON>u de veire blanc", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON> blanca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.white_wool": "<PERSON> blan<PERSON>", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON> sal<PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON> de wither", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON> d'esqueleta wither", "block.minecraft.wither_skeleton_wall_skull": "<PERSON>ran d'esqueleta wither murau", "block.minecraft.yellow_banner": "Bandièra jauna", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON> jaune", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON><PERSON> jauna", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> amb candèla jauna", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON> jauna", "block.minecraft.yellow_concrete": "<PERSON><PERSON> jaune", "block.minecraft.yellow_concrete_powder": "Polsa de beton jauna", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha esmautada jauna", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> jauna", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> jaune", "block.minecraft.yellow_stained_glass_pane": "Panèu de veire jaune", "block.minecraft.yellow_terracotta": "<PERSON><PERSON><PERSON> cu<PERSON>cha jauna", "block.minecraft.yellow_wool": "<PERSON> jauna", "block.minecraft.zombie_head": "Cap de zòmbi", "block.minecraft.zombie_wall_head": "Cap de zòmbi murau", "book.byAuthor": "per %1$s", "book.edit.title": "Ecran de modificacion del libre", "book.editTitle": "Títol dau libre:", "book.finalizeButton": "Signar e barrar", "book.finalizeWarning": "Mèfi! Un còp signat, lo libre serà pas pus modificable.", "book.generation.0": "<PERSON><PERSON>", "book.generation.1": "Còpia de l'originau", "book.generation.2": "Còpia de la còpia", "book.generation.3": "Estraçat", "book.invalid.tag": "* Tag de libre pas valid *", "book.pageIndicator": "Pagina %1$s sus %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON>", "book.page_button.previous": "Pagina Precedenta", "book.sign.title": "Ecran de signatura del libre", "book.sign.titlebox": "Títol", "book.signButton": "Signar", "book.view.title": "Ecran de lectura del libre", "build.tooHigh": "La nautor maximala per bastir es de %s", "chat.cannotSend": "Se pòt pas enviar de messatge dins lo chat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Picatz per i vos teleportar", "chat.copy": "<PERSON><PERSON><PERSON> dins lo cachapapièrs", "chat.copy.click": "Clicatz per copiar dins lo cachapapièrs", "chat.deleted_marker": "Aquel messatge es estat suprimit pel servidor.", "chat.disabled.chain_broken": "Chat desactivat en causa d'una cadena rompuda. <PERSON><PERSON><PERSON> pregat d'ensajar de vos tornar connectar.", "chat.disabled.expiredProfileKey": "Chat desactivat en causa d'un perfil de clau publica expirat. <PERSON><PERSON>tz pregat d'ensajar de vos tornar connectar.", "chat.disabled.invalid_command_signature": "La comanda conteniá de signaturas d'argument de comanda pas esperadas o mancantas.", "chat.disabled.invalid_signature": "Signatura pas valida per lo chat. S<PERSON>tz pregat d'ensajar de vos tornar connectar.", "chat.disabled.launcher": "Chat desactivat dins las opcions del lançador. Se pòt pas enviar de messatge.", "chat.disabled.missingProfileKey": "Chat desactivat en causa d'un perfil de clau publica mancant. <PERSON><PERSON><PERSON> pregat d'ensajar de vos tornar connectar.", "chat.disabled.options": "Chat desactivat dins las opcions del client.", "chat.disabled.out_of_order_chat": "Chat recebut desordonat. L'òra del sistèma es cambiada ?", "chat.disabled.profile": "Chat es pas autorizat pels paramètres del compte. Tornatz picar \"%s\" per mai d'informacions.", "chat.disabled.profile.moreInfo": "Chat es pas autorizat pels paramètres del compte. Se pòt pas enviar o véser de messatge.", "chat.editBox": "chat", "chat.filtered": "Filtrat per lo servidor.", "chat.filtered_full": "Lo servidor a amagat los vòstres messatges per d'uns jogaires.", "chat.link.confirm": "<PERSON><PERSON><PERSON> segur de voler dobrir aqueste siti web?", "chat.link.confirmTrusted": "Volètz dobrir aqueste ligam o lo pegar sus vòstre cachapapièrs?", "chat.link.open": "Dobrir dins lo navigator", "chat.link.warning": "Dobriscatz pas jamais de ligams de persona en que vos fisatz pas!", "chat.queue": "[+%s messatge ne espèra]", "chat.square_brackets": "[%s]\n", "chat.tag.error": "Lo servidor envièt un messatge pas valid.", "chat.tag.modified": "Messatge modificat per lo servidor. Messatge original :", "chat.tag.not_secure": "Messatge pas verificat. Se pòt pas èsser senhalat.", "chat.tag.system": "Messatge del servidor. Se pòt pas èsser senhalat.", "chat.tag.system_single_player": "Messatge del servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s a fait lo desfis %s", "chat.type.advancement.goal": "%s a aténhat la tòca %s", "chat.type.advancement.task": "%s a fach lo progrès %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Mandar un messatge l'equipa", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s ditz %s", "chat.validation_error": "Error de la validacion del chat", "chat_screen.message": "Messatge d'enviar: %s", "chat_screen.title": "Fenestra del tchat", "chat_screen.usage": "Escrivètz lo tèxt e picatz Enter per o mandar", "chunk.toast.checkLog": "Consultatz lo registre per mai de detalhs", "chunk.toast.loadFailure": "Mèuca del cargament del tròç en %s", "chunk.toast.lowDiskSpace": "Espaci de disc feble !", "chunk.toast.lowDiskSpace.description": "La sauvagarda del mond poiriá pas èsser possible.", "chunk.toast.saveFailure": "Mèuca de la sauvagarda del tròç en %s", "clear.failed.multiple": "Cap d'objècte es estat trobat sus %s jogaires", "clear.failed.single": "Cap d'objècte es estat trobat sus lo jogaire %s", "color.minecraft.black": "Negre", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verd", "color.minecraft.light_blue": "<PERSON><PERSON> clar", "color.minecraft.light_gray": "<PERSON><PERSON> clar", "color.minecraft.lime": "Lima", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Irange", "color.minecraft.pink": "<PERSON><PERSON><PERSON>", "color.minecraft.purple": "Violet", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "Jaune", "command.context.here": "<--[AICÍ]", "command.context.parse_error": "%s a la posicion %s: %s", "command.exception": "Mèuca de l'analisi sintaxica de la comanda : %s", "command.expected.separator": "Un espaci blanc es esperat per acabar un argument, mas de donadas de fin son estadas trobadas", "command.failed": "Una mèuca inesperada se produsís en assajant de complir aquela comanda", "command.forkLimit": "Nombre maximal de contèxts (%s) atenh", "command.unknown.argument": "Argument pas valid per la comanda", "command.unknown.command": "Comanda desconeguda o pas complèta, veire la mèuca <PERSON>-jos", "commands.advancement.criterionNotFound": "Lo progrès %1$s conten pas lo critèri '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Se pòt pas acordar lo critèri \"%s\" del progrès %s a %s jogaires perque ja l'an", "commands.advancement.grant.criterion.to.many.success": "Lo critèri \"%s\" del progrès %s es estat acordat a %s jogaires", "commands.advancement.grant.criterion.to.one.failure": "Se pòt pas acordar lo critèri \"%s\" del progrès %s al jogaire %s perque ja l'a", "commands.advancement.grant.criterion.to.one.success": "Lo critèri \"%s\" del progrès %s es estat acordat a %s", "commands.advancement.grant.many.to.many.failure": "Se pòt pas acordar %s progrès a %s jogaires perque ja los an", "commands.advancement.grant.many.to.many.success": "%s progrès son estats acordat a %s jogaires", "commands.advancement.grant.many.to.one.failure": "Se pòt pas acordar %s progrès a %s perque aquel jogaire ja los a", "commands.advancement.grant.many.to.one.success": "%s progrès son estats acordat a %s", "commands.advancement.grant.one.to.many.failure": "Se pòt pas acordar lo progrès %s a %s jogaires perque ja l'an", "commands.advancement.grant.one.to.many.success": "Lo progrès %s es estat acordat a %s jogaires", "commands.advancement.grant.one.to.one.failure": "Impossibla d'octroyar lo progrès  %s à  %s perdequé lo jogador lo possedis jà", "commands.advancement.grant.one.to.one.success": "Lo progrès %s es estat acordat a %s", "commands.advancement.revoke.criterion.to.many.failure": "Se pòt pas levar lo critèri \"%s\" del progrès %s a %s jogaires perque l'an pas", "commands.advancement.revoke.criterion.to.many.success": "Lo critèri \"%s\" del progrès %s es estat levat a %s jogaires", "commands.advancement.revoke.criterion.to.one.failure": "Impossible de levar le critèri \"%s\" del progrès %s a %s perque lo jogaire l'a pas", "commands.advancement.revoke.criterion.to.one.success": "Lo critèri \"%s\" del progrès %s es estat levat al jogaire %s", "commands.advancement.revoke.many.to.many.failure": "Se pòt pas levar %s progrès a %s jogaires perque los an pas", "commands.advancement.revoke.many.to.many.success": "%s progrès son estats levat a %s jogaires", "commands.advancement.revoke.many.to.one.failure": "Se pòt pas levar %s progrès al jogaire %s perque los a pas", "commands.advancement.revoke.many.to.one.success": "%s progrès son estats levat al jogaire %s", "commands.advancement.revoke.one.to.many.failure": "Se pòt pas levar lo progrès %s a %s jogaires perque l'an pas", "commands.advancement.revoke.one.to.many.success": "Lo progrès %s es estat levat a %s jogaires", "commands.advancement.revoke.one.to.one.failure": "Se pòt pas levar lo progrès %s al jogaire %s perque l'a pas", "commands.advancement.revoke.one.to.one.success": "Lo progrès %s es estat levat al jogaire %s", "commands.attribute.base_value.get.success": "La valor de basa del atribut \"%s\" de l'entitat \"%s\" es de %s", "commands.attribute.base_value.reset.success": "La valor de basa del atribut \"%s\" de l'entitat \"%s\" es estada reïnicializada a %s", "commands.attribute.base_value.set.success": "La valor de basa del atribut \"%s\" de l'entitat \"%s\" es definida a %s", "commands.attribute.failed.entity": "%s es pas una entitat valida per aquela comanda", "commands.attribute.failed.modifier_already_present": "Lo modificaire %s es ja aplicat al atribut \"%s\" de l'entitat \"%s\"", "commands.attribute.failed.no_attribute": "L'entitat \"%s\" a pas l'atribut \"%s\"", "commands.attribute.failed.no_modifier": "L'atribut \"%s\" de l'entitat \"%s\" a pas de modificaire %s", "commands.attribute.modifier.add.success": "Lo modificaire %s es estat apondut al atribut \"%s\" de l'entitat \"%s\"", "commands.attribute.modifier.remove.success": "Lo modificaire %s es estat retirat del atribut \"%s\" de l'entitat \"%s\"", "commands.attribute.modifier.value.get.success": "La valor del modificaire %s del atribut \"%s\" de l'entitat \"%s\" es de %s", "commands.attribute.value.get.success": "La valor del atribut \"%s\" del entitat \"%s\" es de %s", "commands.ban.failed": "Res es cambiat. Aquel jogaire es ja fòrabandit", "commands.ban.success": "Lo jogaire %s es estat fòrabandit : %s", "commands.banip.failed": "Res es cambiat. Aquel IP es ja fòrabandida", "commands.banip.info": "Aquel fò<PERSON>iment toca %s jogaire(s) : %s", "commands.banip.invalid": "Adreça IP pas valid o jogaire desconegut", "commands.banip.success": "IP fòrabandida %s: %s", "commands.banlist.entry": "%s es estat fòrabandit per %s: %s", "commands.banlist.entry.unknown": "(Desconegut)", "commands.banlist.list": "I a %s fòrabandiment(s) :", "commands.banlist.none": "I a pas cap de bans", "commands.bossbar.create.failed": "Una barra de boss amb l'ID \"%s\" existís ja", "commands.bossbar.create.success": "Barra de boss personalizada creada: %s", "commands.bossbar.get.max": "La barra de boss personalizada %s a un maximum de %s", "commands.bossbar.get.players.none": "La barra de boss personalizada %s a pas de jogaires connectats ara", "commands.bossbar.get.players.some": "La barra de boss personalizada %s a %s jogaire(s) connectat(s) ara : %s", "commands.bossbar.get.value": "La barra de boss personalizada %s a una valor de %s", "commands.bossbar.get.visible.hidden": "La barra de boss personalizada %s es ara pas visibla", "commands.bossbar.get.visible.visible": "La barra de boss personalizada %s es ara visibla", "commands.bossbar.list.bars.none": "I a pas cap de barra de boss personalizada activa", "commands.bossbar.list.bars.some": "I a %s barra(s) de boss personalizada(s) : %s", "commands.bossbar.remove.success": "Barra de boss personalizada retirada: %s", "commands.bossbar.set.color.success": "La barra de boss personalizada %s es cambiat de color", "commands.bossbar.set.color.unchanged": "Res es cambiat. La barra de boss es ja d'aquela color", "commands.bossbar.set.max.success": "La barra de boss personalizada %s es cambiat de maximum a %s", "commands.bossbar.set.max.unchanged": "Res es cambiat. La barra de boss a ja aquel maximum", "commands.bossbar.set.name.success": "La barra de boss personalizada %s es estada tornar-nomada", "commands.bossbar.set.name.unchanged": "Res es cambiat. La barra de boss a ja aquel nom", "commands.bossbar.set.players.success.none": "La barra de boss personalizada %s a pas de jogaire mai", "commands.bossbar.set.players.success.some": "La barra de boss personalizada %s a ara %s jogaire(s) : %s", "commands.bossbar.set.players.unchanged": "Res es cambiat. Aquelos jogaires son ja sus la barra de boss amb degun a apondre o a retirar", "commands.bossbar.set.style.success": "La barra de boss personalizada %s es cambiat d'estil", "commands.bossbar.set.style.unchanged": "Res es cambiat. La barra de boss a ja aquel estil", "commands.bossbar.set.value.success": "La barra de boss personalizada %s es cambiat de valor per %s", "commands.bossbar.set.value.unchanged": "Res es cambiat. La barra de boss a ja aquela valor", "commands.bossbar.set.visibility.unchanged.hidden": "Res es cambiat. La barra de boss es ja pas visibla", "commands.bossbar.set.visibility.unchanged.visible": "Res es cambiat. La barra de boss es ja visibla", "commands.bossbar.set.visible.success.hidden": "La barra de boss personalizada %s es ara amagada", "commands.bossbar.set.visible.success.visible": "La barra de boss personalizada %s es ara visibla", "commands.bossbar.unknown": "I a pas de barra de boss amb aquel ID \"%s\"", "commands.clear.success.multiple": "Retirada de %s objècte(s) del inventari de %s jogaires", "commands.clear.success.single": "Retirada de %s objècte(s) del inventari de %s", "commands.clear.test.multiple": "%s objècte(s) correspondent(s) trobat(s) dins l'inventari de %s jogaires", "commands.clear.test.single": "%s objècte(s) correspondent(s) trobat(s) dins l'inventari de %s", "commands.clone.failed": "Cap de blòc es estat copiat", "commands.clone.overlap": "La sorsa e la destinacion se pòdon pas cavalgar", "commands.clone.success": "Còpia de %s blòc(s) amb succès", "commands.clone.toobig": "Tròp de blòcs dins la zona especificada (%s al maximum, %s espicificats)", "commands.damage.invulnerable": "La cibla es pas sensibla al tipe de degalh provesit", "commands.damage.success": "%2$s patís %1$s punt(s) de degalh", "commands.data.block.get": "La proprietat %s del blòc en %s, %s, %s, après un factor d'escala de %s, es de %s", "commands.data.block.invalid": "Lo blòc ciblat es pas una entitat de blòc", "commands.data.block.modified": "Las donadas del blòc en %s, %s, %s son estadas modificadas", "commands.data.block.query": "Lo blòc en %s, %s, %s a las donadas seguentas : %s", "commands.data.entity.get": "La propiretat %s de %s, après un factor d'escala de %s, es de %s", "commands.data.entity.invalid": "Se pòt pas modificar las donadas del jogaire", "commands.data.entity.modified": "Donadas de l'entitat modificadas per %s", "commands.data.entity.query": "%s a las donadas d'entitat seguentas : %s", "commands.data.get.invalid": "Se pòt pas obténer %s, sonque de tags numerics son autorizats", "commands.data.get.multiple": "Aqueste argument accèpta una sola valor NBT", "commands.data.get.unknown": "Se pòt pas obténer %s, lo tag existís pas", "commands.data.merge.failed": "Res es cambiat. Las proprietats especificadas an ja aquelas valors", "commands.data.modify.expected_list": "S'esperava una lista, valor recebuda: %s", "commands.data.modify.expected_object": "S'esperava un objècte, valor recebuda: %s", "commands.data.modify.expected_value": "Valor esperada, recebut : %s", "commands.data.modify.invalid_index": "Indèxe de lista invalida: %s", "commands.data.modify.invalid_substring": "Indèxes de soscadena pas valids : de %s a %s", "commands.data.storage.get": "La proprietat %s dins l'emmagatzematge %s, après un factor d'escala de %s, es de %s", "commands.data.storage.modified": "Emmagatzematge cambiat %s", "commands.data.storage.query": "Lo emmagatzematge %s ten: %s", "commands.datapack.create.already_exists": "Un pack amb lo nom \"%s\" existís ja", "commands.datapack.create.invalid_full_name": "Novèl nom de pack '%s' non valid", "commands.datapack.create.invalid_name": "Caractèrs pas valids dins lo nom del novèl pack \"%s\"", "commands.datapack.create.io_failure": "Se pòt pas crear un pack amb lo nom \"%s\", consultatz lo registre", "commands.datapack.create.metadata_encode_failure": "Mèuca al temps del encodatge pel pack amb lo nom \"%s\" : %s", "commands.datapack.create.success": "Creacion d'un novèl pack amb lo nom \"%s\"", "commands.datapack.disable.failed": "Lo pack de donada \"%s\" es pas activat !", "commands.datapack.disable.failed.feature": "Lo pack de donada \"%s\" pòt pas èsser desactivat, perque fa partit d'un variable actiu !", "commands.datapack.enable.failed": "Lo pack de donada \"%s\" es ja activat !", "commands.datapack.enable.failed.no_flags": "lo pack  \"%s\"  pot pas èsser sculptat, perqué los variables requisas son pas activadas dins lo monda :  \"%s\" !", "commands.datapack.list.available.none": "I a pas cap d'autre pack de donada disponibles", "commands.datapack.list.available.success": "I a %s pack(s) de donadas disponible(s) : %s", "commands.datapack.list.enabled.none": "I a pas cap de packs de donadas activats", "commands.datapack.list.enabled.success": "I a %s pack(s) de donadas activat(s) : %s", "commands.datapack.modify.disable": "Desactivacion del pack de donadas %s", "commands.datapack.modify.enable": "Activacion del pack de donadas %s", "commands.datapack.unknown": "Pack de donada desconegut '%s'", "commands.debug.alreadyRunning": "Lo profilador de cicles es ja amodat", "commands.debug.function.noRecursion": "Se pòt pas traçar de dins de l'interior d'una foncion", "commands.debug.function.noReturnRun": "Lo traçat se pòt pas èsser utilizat amb /return run", "commands.debug.function.success.multiple": "Traçat de %s comanda(s) de %s foncions cap al fichièr de sortida %s", "commands.debug.function.success.single": "Traçat de %s comanda(s) de la foncion \"%s\" cap al fichièr de sortida %s", "commands.debug.function.traceFailed": "Mèuca del traçat de la foncion", "commands.debug.notRunning": "Lo profilador de cicles es pas amodat", "commands.debug.started": "Desmarrat<PERSON> del profilador de cicles", "commands.debug.stopped": "Arrèst del profilador de cicles après %s segonda(s) et %s cicle(s) (%s cicle(s) per segonda)", "commands.defaultgamemode.success": "Lo mòde de juòc per defaut es ara %s", "commands.deop.failed": "Res es cambiat. Aquel jogaire es pas un operator", "commands.deop.success": "%s es pas mai un operator del servidor", "commands.dialog.clear.multiple": "Los dialògs son estats esfaçats per %s jogaires", "commands.dialog.clear.single": "Los dialògs son estats esfaçats per %s", "commands.dialog.show.multiple": "Lo dialòg es estat afichat per %s jogaires", "commands.dialog.show.single": "Lo dialòg es estat afichat per %s", "commands.difficulty.failure": "Res es cambiat. La dificultat es ja definit coma %s", "commands.difficulty.query": "La difficutat es %s", "commands.difficulty.success": "La dificultat es estada definida coma %s", "commands.drop.no_held_items": "L'entitat pòt pas téner cap d'objècte", "commands.drop.no_loot_table": "L'entitat \"%s\" a pas de taulà de butin", "commands.drop.no_loot_table.block": "Lo blòc %s a pas de taulà de butin", "commands.drop.success.multiple": "%s objèctes largats", "commands.drop.success.multiple_with_table": "Largat %s objectès de la taulà de butin %s", "commands.drop.success.single": "%s %s largat(adas)", "commands.drop.success.single_with_table": "%s %s largat(adas) de la taulà de butin", "commands.effect.clear.everything.failed": "La cibla a pas d'efièch a retirar", "commands.effect.clear.everything.success.multiple": "Totes les efèits an èsser retiradas de %s ciblas", "commands.effect.clear.everything.success.single": "Totes les efèits an èsser retiradas de %s", "commands.effect.clear.specific.failed": "La cibla a pas l'efièch volgut", "commands.effect.clear.specific.success.multiple": "L'efèits %s forguèt retirada de %s ciblas", "commands.effect.clear.specific.success.single": "L'efèits %s forguèt retirada de %s", "commands.effect.give.failed": "Impossible d'aplicar aqueste efièch (la cibla es immunizat als efièches o a quicòm mai fort)", "commands.effect.give.success.multiple": "L'efèit %s es aplicat a %s ciblas", "commands.effect.give.success.single": "L'efèit %s es aplicat a %s", "commands.enchant.failed": "Res es cambiat. Las ciblas an pas d'objècte dins lor man o l'encantament pòt pas èsser aplicat", "commands.enchant.failed.entity": "%s es pas una entitat valida per aquela comanda", "commands.enchant.failed.incompatible": "%s pòt pas aver aquela encantament", "commands.enchant.failed.itemless": "%s ten pas cap d'objècte", "commands.enchant.failed.level": "%s es mai quel nivèl maximal (%s) que pòt téner aquel encantament", "commands.enchant.success.multiple": "L'encantament %s es aplicat a %s entitats", "commands.enchant.success.single": "L'encantament %s es aplicat al objècte de %s", "commands.execute.blocks.toobig": "Tròp de blòcs dins la zona especificada (%s al maximum, %s especificats)", "commands.execute.conditional.fail": "Tèst mal escaire", "commands.execute.conditional.fail_count": "Tèst mal escaire, quantitat : %s", "commands.execute.conditional.pass": "<PERSON><PERSON><PERSON>", "commands.execute.conditional.pass_count": "T<PERSON>t reüssit, quantitat: %s", "commands.execute.function.instantiationFailure": "Mèuca de l'instanciacion de la foncion \"%s\" : %s", "commands.experience.add.levels.success.multiple": "Donèt %s nivèl(s) d'experiéncia a %s jogaires", "commands.experience.add.levels.success.single": "Donèt %s nivèl(s) d'experiéncia a %s", "commands.experience.add.points.success.multiple": "Donèt %s ponch(es) d'experiéncia a %s jogaires", "commands.experience.add.points.success.single": "Donèt %s ponch(es) d'experiéncia a %s", "commands.experience.query.levels": "%s a %s nivèus d'experiéncia", "commands.experience.query.points": "%s a %s ponches d'experiéncia", "commands.experience.set.levels.success.multiple": "Lo nombre de nivèl d'experiéncia es estat definit a %s per %s jogaires", "commands.experience.set.levels.success.single": "Lo nombre de nivèl d'experiéncia es estat definit a %s per %s", "commands.experience.set.points.invalid": "Se pòt pas definir lo nombre de ponches d'experiéncia en delà del maximum de ponches del nivèl actual del jogaire", "commands.experience.set.points.success.multiple": "Lo nombre de ponches d'expériencia es estat definit a %s per %s jogaires", "commands.experience.set.points.success.single": "Lo nombre de ponches d'expériencia es estat definit a %s per %s", "commands.fill.failed": "Cap de blòc es estat emplit", "commands.fill.success": "Empliment de %s blòc(s) amb succès", "commands.fill.toobig": "Tròp de blòcs dins la zona especificada (%s al maximum, %s especificats)", "commands.fillbiome.success": "Los biòmas son estats definits entre %s, %s, %s e %s, %s, %s", "commands.fillbiome.success.count": "%s entrada(s) de biòma definida(s) entre %s, %s, %s e %s, %s, %s", "commands.fillbiome.toobig": "Tròp de blòcs dins lo volum especificat (%s al maximum, %s especificats)", "commands.forceload.added.failure": "Cap de tròces es estat marcat coma forçat a cargar", "commands.forceload.added.multiple": "Los %s tròces dins la dimension %s de %s a %s son ara marcats coma forçats a cargar", "commands.forceload.added.none": "Cap de tròç forçat a cargar es pas estat trobat dins la dimension %s", "commands.forceload.added.single": "Lo tròç %s dins la dimension %s es ara marcat coma forçat a cargar", "commands.forceload.list.multiple": "%s tròces forçats a cargar son estats trobats dins la dimension %s en : %s", "commands.forceload.list.single": "Un tròç forçat a cargar es estat trobat dins la dimension %s en : %s", "commands.forceload.query.failure": "Lo tròç %s dins la dimension %s es pas marcat coma forçat a cargar", "commands.forceload.query.success": "Lo tròç %s dins la dimension %s es marcat coma forçat a cargar", "commands.forceload.removed.all": "Tots los tròces dins la dimension %s son ara pas mai marcats coma forçats a cargar", "commands.forceload.removed.failure": "Cap de tròç es pas mai marcat coma forçat a cargar", "commands.forceload.removed.multiple": "Los %s tròces dins la dimension %s de %s a %s son ara pas mai marcats coma forçats a cargar", "commands.forceload.removed.single": "Lo tròç %s dins la dimension %s es ara pas mai marcat coma forçat a cargar", "commands.forceload.toobig": "Tròp de tròces dins la zona especificada (%s al maximum, %s especificats)", "commands.function.error.argument_not_compound": "Tipe d'argument pas valid : %s, compausant esperat", "commands.function.error.missing_argument": "Argument %2$s mancant per la foncion \"%1$s\"", "commands.function.error.missing_arguments": "Arguments mancants per la foncion \"%s\"", "commands.function.error.parse": "Al moment de l'instanciacion de la macro %s : la comanda \"%s\" provocava l'error : %s", "commands.function.instantiationFailure": "Mèuca de l'instanciacion de la foncion \"%s\" : %s", "commands.function.result": "La foncion \"%s\" tornava %s", "commands.function.scheduled.multiple": "Execucion de las foncions \"%s\"", "commands.function.scheduled.no_functions": "Se pòt pas trobar una foncion amb el nom %s", "commands.function.scheduled.single": "Execucion de la foncion \"%s\"", "commands.function.success.multiple": "Execucion de %s comanda(s) dins de %s foncions", "commands.function.success.multiple.result": "%s foncions executadas", "commands.function.success.single": "Execucion de %s comanda(s) dins de la foncion \"%s\"", "commands.function.success.single.result": "La foncion \"%2$s\" retornava %1$s", "commands.gamemode.success.other": "Cambiament dau mòde de juòc de %s en %s", "commands.gamemode.success.self": "Cambiament de vòstre pròpri mòde de juòc en %s", "commands.gamerule.query": "La règla de juòc %s es botada a : %s", "commands.gamerule.set": "La règla de juòc %s es ara botada a: %s", "commands.give.failed.toomanyitems": "Se pòt pas donar mai de %s %s", "commands.give.success.multiple": "Donèt %s %s a %s jogaire", "commands.give.success.single": "Donèt %s %s a %s", "commands.help.failed": "Comanda desconeguda o permissions pas sufisentas", "commands.item.block.set.success": "S'es remplaçat un espaci a %s, %s, %s amb %s", "commands.item.entity.set.success.multiple": "S'es remplaçat un espaci sus %s entitats amb %s", "commands.item.entity.set.success.single": "S'es remplaçat un espaci sus %s amb %s", "commands.item.source.no_such_slot": "La sorsa a pas d'espaci %s", "commands.item.source.not_a_container": "La posicion sorsa en %s, %s, %s es pas un contenador", "commands.item.target.no_changed.known_item": "I a pas cap de ciblas acceptant l'objècte %s dins l'espaci %s", "commands.item.target.no_changes": "I a pas cap de ciblas acceptant l'objècte dins l'espaci %s", "commands.item.target.no_such_slot": "La cibla a pas d'espaci %s", "commands.item.target.not_a_container": "La posicion ciblada en %s, %s, %s es pas un contenador", "commands.jfr.dump.failed": "Error de l'enregistrament dins %s", "commands.jfr.start.failed": "Fracàs de desmarratge del perfilatge JFR", "commands.jfr.started": "Desmarratge dau perfilatge JFR", "commands.jfr.stopped": "Arrèst de la perfilada JFR e enregistrament dins %s", "commands.kick.owner.failed": "Se pòt pas expulsar lo proprietari del sevidor d'una partida LAN", "commands.kick.singleplayer.failed": "Se pòt pas expulsar un jogador d'una partida pas en linha", "commands.kick.success": "%s es estat(ada) expulsat(ada) : %s", "commands.kill.success.multiple": "%s entitats son estadas tuadas", "commands.kill.success.single": "%s tuat", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "I a %s / %s jogaires en linha : %s", "commands.locate.biome.not_found": "Se pòt pas trobar un biòme \"%s\" a una distància rasonabla", "commands.locate.biome.success": "Lo biòma \"%s\" lo mai prèp es situat en %s (%s blòcs de distància)", "commands.locate.poi.not_found": "Se pòt pas trobar un ponch d'interès \"%s\" a una distància rasonabla", "commands.locate.poi.success": "Lo ponch d'interès \"%s\" lo mai prèp es situat en %s (%s blòcs de distància)", "commands.locate.structure.invalid": "L'estructura \"%s\" existís pas", "commands.locate.structure.not_found": "Se pòt pas trobar una estructura \"%s\" a proximitat", "commands.locate.structure.success": "L'estructura \"%s\" la mai prèpa es situada en %s (%s blòcs de distància)", "commands.message.display.incoming": "%s vos chita: %s", "commands.message.display.outgoing": "Chitas a %s: %s", "commands.op.failed": "Res es cambiat. Aquel jogaire es ja un operator", "commands.op.success": "%s es ara un operator del servidor", "commands.pardon.failed": "Res es cambiat. Aquel jogaire es pas fòrabandit", "commands.pardon.success": "Lo jogaire %s es pas mai fòrabandit", "commands.pardonip.failed": "Res es cambiat. Aquela adreça IP es pas fòrabandida", "commands.pardonip.invalid": "Adreça IP pas valid", "commands.pardonip.success": "IP autorizada %s", "commands.particle.failed": "La particula èra pas visibla per degun", "commands.particle.success": "Afichatge de las particulas %s", "commands.perf.alreadyRunning": "Lo profilador tecnic es ja amodat", "commands.perf.notRunning": "Lo profilador tecnic es pas amodat", "commands.perf.reportFailed": "Mèuca de la creacion del rapòrt de desbugatge", "commands.perf.reportSaved": "Rapòrt de desbugatge creat dins %s", "commands.perf.started": "Desmarratge del profilador tecnic pendent 10 segondas (utilizatz \"/perf stop\" per copar)", "commands.perf.stopped": "Arrèst del profilador tecnic après %s segonda(s) e %s cicle(s) (%s cicle(s) per segonda)", "commands.place.feature.failed": "Mèuca del plaçament del element", "commands.place.feature.invalid": "L'element \"%s\" existís pas", "commands.place.feature.success": "L'element \"%s\" es estat plaçat en %s, %s, %s", "commands.place.jigsaw.failed": "Mèuca de la generacion del puzzle", "commands.place.jigsaw.invalid": "L'ensemble de modèl \"%s\" existís pas", "commands.place.jigsaw.success": "Lo puzzle es estat generat en %s, %s, %s", "commands.place.structure.failed": "Mèuca del plaçament de l'estructura", "commands.place.structure.invalid": "L'estructura \"%s\" existís pas", "commands.place.structure.success": "L'estructura \"%s\" es estada generada en %s, %s, %s", "commands.place.template.failed": "Mèuca del plaçament del modèl", "commands.place.template.invalid": "Lo modèl amb l'ID \"%s\" existís pas", "commands.place.template.success": "Lo modèl \"%s\" es estat cargat en %s, %s, %s", "commands.playsound.failed": "Lo son es tròp luènh per èsser ausit", "commands.playsound.success.multiple": "Lo son \"%s\" es estat jogat a %s jogaires", "commands.playsound.success.single": "Lo son \"%s\" es estat jogat a %s", "commands.publish.alreadyPublished": "Lo jòc multijogaire es ja albergat sul pòrt %s", "commands.publish.failed": "Impossible d'aubergar lo juòc locau", "commands.publish.started": "Juòc locau aubergat sul pòrt %s", "commands.publish.success": "Lo jòc multijogaires es ara albergat sul pòrt %s", "commands.random.error.range_too_large": "L'interval de valors aleatòris cal pas èsser superior a 2147483646", "commands.random.error.range_too_small": "L'interval de valors aleatòris cal pas èsser inferior a 2", "commands.random.reset.all.success": "Reïnicializacion de %s sequéncia(s) aleatòria(s)", "commands.random.reset.success": "Reïnicializacion de la sequéncia aleatòria %s", "commands.random.roll": "%s obten %s (entre %s e %s)", "commands.random.sample.success": "Valor aleatòria : %s", "commands.recipe.give.failed": "Pas de nòvas recèptas son estadas apresas", "commands.recipe.give.success.multiple": "Desblocatge de %s recèpta(s) per %s jogaires", "commands.recipe.give.success.single": "Desblocatge de %s recèpta(s) per %s", "commands.recipe.take.failed": "I a pas cap de recèpta que pòsca èsser oblidadas", "commands.recipe.take.success.multiple": "Oblia de %s recèpta(s) de %s jogaires", "commands.recipe.take.success.single": "Oblia de %s recèpta(s) de %s", "commands.reload.failure": "Malescaduda del recargament; guardar las donadas inicialas", "commands.reload.success": "Recargament!", "commands.ride.already_riding": "%s cavalga ja un(a) %s", "commands.ride.dismount.success": "%s cavalga pas mai un(a) %s", "commands.ride.mount.failure.cant_ride_players": "Los jogaires pon pas èsser cavalgats", "commands.ride.mount.failure.generic": "%s podiá pas comnçar a cavalgar un(a) %s", "commands.ride.mount.failure.loop": "Se pòt pas far cavalgar l'entitat sus ela meteis o un dels sieus passatgièrs", "commands.ride.mount.failure.wrong_dimension": "Se pòt pas cavalgar una entitat dins una dimension diferenta", "commands.ride.mount.success": "%s comença a cavalgar un(a) %s", "commands.ride.not_riding": "%s es cavalgant pas cap de veïcul", "commands.rotate.success": "%s es estat virat(ada)", "commands.save.alreadyOff": "La sauvagarda es ja desactivada", "commands.save.alreadyOn": "La sauvagarda es ja activada", "commands.save.disabled": "La sauvagarda automatica es ara desactivada", "commands.save.enabled": "La sauvagarda automatica es ara activada", "commands.save.failed": "Se pòt pas sauvagardar la partida (I a pro d'espaci disc ?)", "commands.save.saving": "Sauvagarda de la partida... (Aquò pòt prendre del temps !)", "commands.save.success": "Partida sauvagardada", "commands.schedule.cleared.failure": "Cap de prevision existís amb l'ID \"%s\"", "commands.schedule.cleared.success": "Retirada de %s prevision(s) amb l'Id \"%s\"", "commands.schedule.created.function": "La foncion \"%s\" es prevista dins %s cicle(s) a l'òra del jòc %s", "commands.schedule.created.tag": "Lo tag \"%s\" es previst dins %s cilce(s) a l'òra del jòc %s", "commands.schedule.macro": "Se pòt pas programar una macro", "commands.schedule.same_tick": "Se pòt pas prevéser per lo cicle actual", "commands.scoreboard.objectives.add.duplicate": "Un objectiu amb aquel nom existís ja", "commands.scoreboard.objectives.add.success": "Novel objectiu creat %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Res es cambiat. Aquel emplaçament d'afichatge es ja vuèg", "commands.scoreboard.objectives.display.alreadySet": "Res es cambiat. Aquel emplaçament d'afichatge es ja mostrant aquel objectiu", "commands.scoreboard.objectives.display.cleared": "Tots los objectius dins l'emplaçament d'afichatge %s son estats escafats", "commands.scoreboard.objectives.display.set": "L'emplaçament d'afichatge %s mostra ara l'objectiu %s", "commands.scoreboard.objectives.list.empty": "I a pas cap d'objectius", "commands.scoreboard.objectives.list.success": "I a %s objectiu(s) : %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "L'actualizacion automatica del afichatge del objectiu %s es estada desactivada", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "L'actualizacion automatica del afichatge del objectiu %s es estada activada", "commands.scoreboard.objectives.modify.displayname": "Lo nom afichat del objectiu %s es estat cambiat per %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Lo format numeric per defaut %s es estat esfaçat", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Lo format numeric per defaut %s es cambiat", "commands.scoreboard.objectives.modify.rendertype": "Lo tipe de rendut del objectiu %s es estat cambiat", "commands.scoreboard.objectives.remove.success": "El objectiu %s forguèt retirèt", "commands.scoreboard.players.add.success.multiple": "%s es apondut a la marca del objectiu %s per %s entitats", "commands.scoreboard.players.add.success.single": "%s es apondut a la marca del objectiu %s per %s (ara a %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Lo nom d'afichatge de %s entitats es estat esfaçat dins %s", "commands.scoreboard.players.display.name.clear.success.single": "Lo nom d'afichatge de %s es estat esfaçat dins %s", "commands.scoreboard.players.display.name.set.success.multiple": "Lo nom d'afichatge de %2$s entitats es estat cambiat en %1$s dins %3$s", "commands.scoreboard.players.display.name.set.success.single": "Lo nom d'afichatge de %2$s es estat cambiat en %1$s dins %3$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Lo format numeric de %s entitats es estat esfaçat dins %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Lo format numeric de %s es estat esfaçat dins %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Lo format numeric de %s entitats es estat cambiat dins %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Lo format numeric de %s es estat cambiat dins %s", "commands.scoreboard.players.enable.failed": "Res es cambiat. Aquel desenclavador es ja activat", "commands.scoreboard.players.enable.invalid": "\"Enable\" fonciona sonque suls objectius de tipe \"trigger\"", "commands.scoreboard.players.enable.success.multiple": "Lo desenclavador %s es estat activat per %s entitats", "commands.scoreboard.players.enable.success.single": "Lo desclavador %s es estat activat per %s", "commands.scoreboard.players.get.null": "Se pòt pas obténer la valor de %s per %s, es pas definit", "commands.scoreboard.players.get.success": "%s a una marca de %s per l'objectiu %s", "commands.scoreboard.players.list.empty": "I a pas cap d'entitats seguidas", "commands.scoreboard.players.list.entity.empty": "%s a pas de marca a afichar", "commands.scoreboard.players.list.entity.entry": "%s : %s", "commands.scoreboard.players.list.entity.success": "%s a %s marca(s) :", "commands.scoreboard.players.list.success": "I a %s entitat(s) seguida(s) : %s", "commands.scoreboard.players.operation.success.multiple": "La marca del objectiu %s es estada actualizada per %s entitats", "commands.scoreboard.players.operation.success.single": "La marca del objectiu %s per %s es estada definida a %s", "commands.scoreboard.players.remove.success.multiple": "Retirada de %s de la marca del objectiu %s per %s entitats", "commands.scoreboard.players.remove.success.single": "Retirada de %s de la marca del objectiu %s per %s (ara de %s)", "commands.scoreboard.players.reset.all.multiple": "Totas las marcas de %s entitats son estadas reïnicializadas", "commands.scoreboard.players.reset.all.single": "Totas las marcas de %s son estadas reïnicializadas", "commands.scoreboard.players.reset.specific.multiple": "La marca del objectiu %s es estada reïnicializada per %s entitats", "commands.scoreboard.players.reset.specific.single": "La marca del objectiu %s es estada reïnicializada per %s", "commands.scoreboard.players.set.success.multiple": "La marca del objectiu %s per %s entitats es estada definida a %s", "commands.scoreboard.players.set.success.single": "La marca del objectiu %s per %s es estada definida a %s", "commands.seed.success": "Grana: %s", "commands.setblock.failed": "Se pòt pas plaçar lo blòc", "commands.setblock.success": "Lo blòc en %s, %s, %s es estat cambiat", "commands.setidletimeout.success": "Lo temps d'inactivitat abans percaça es ara a %s minuta(s)", "commands.setidletimeout.success.disabled": "Lo temps d'inactivitat abans percaça es ara desactivat", "commands.setworldspawn.failure.not_overworld": "Se pòt pas definir lo punt d'aparicion del mond fòra de la susfàcia", "commands.setworldspawn.success": "Lo punch d'aparicion del mond es estat definit en %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Lo punch d'aparicion es estat definit en %s, %s, %s [%s] dins la dimension %s per %s jogaires", "commands.spawnpoint.success.single": "Lo punch d'aparicion es estat definit en %s, %s, %s [%s] dins la dimension %s per %s", "commands.spectate.not_spectator": "%s ne està pas en mode observant", "commands.spectate.self": "Non te pòdes veire", "commands.spectate.success.started": "Ara observant: %s", "commands.spectate.success.stopped": "Pas mai observant una entitat", "commands.spreadplayers.failed.entities": "Se pòt pas propagar %s entitat(s) al etorn de %s, %s (tròp d'entitats per l'espaci disponible - ensajatz de despassar pas %s)", "commands.spreadplayers.failed.invalid.height": "Valor %s del argument maxHeight pas valid, valor superiora a la nautor minimala del mond de %s esperat", "commands.spreadplayers.failed.teams": "Propagacion de %s còla(s) a l'entorn de %s, %s impossibla (tròp d'entitats per l'espaci - ensajatz de despassar s %s)", "commands.spreadplayers.success.entities": "Propagacion de %s entitat(s) a l'entorn de %s, %s, amb una mejana de %s blòc(s) de distància", "commands.spreadplayers.success.teams": "Propagacion de %s còla(s) a l'entorn de %s, %s, amb una mejana de %s blòc(s) de distància", "commands.stop.stopping": "<PERSON><PERSON><PERSON><PERSON> del servidor", "commands.stopsound.success.source.any": "<PERSON><PERSON><PERSON><PERSON> de tots los sons \"%s\"", "commands.stopsound.success.source.sound": "Arrèst del son \"%s\" sus la sorsa \"%s\"", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON><PERSON> de tots los sons", "commands.stopsound.success.sourceless.sound": "<PERSON><PERSON><PERSON><PERSON> del son \"%s\"", "commands.summon.failed": "Se pòt pas invocar l'entitat", "commands.summon.failed.uuid": "Se pòt pas invocar l'entitat perque l'UUID es ja utilizat", "commands.summon.invalidPosition": "Posicion pas valida per invocar", "commands.summon.success": "L'entitat \"%s\" es estada invocada", "commands.tag.add.failed": "La cibla a ja lo tag o n'a tròp", "commands.tag.add.success.multiple": "Lo tag \"%s\" es apondut a %s entitats", "commands.tag.add.success.single": "Lo tag \"%s\" es apondut a %s", "commands.tag.list.multiple.empty": "I a pas cap de tag sus las %s entitats", "commands.tag.list.multiple.success": "Las %s entitats an %s tag(s) al total : %s", "commands.tag.list.single.empty": "%s a pas cap de tags", "commands.tag.list.single.success": "%s a %s tags: %s", "commands.tag.remove.failed": "La cibla a pas aquel tag", "commands.tag.remove.success.multiple": "Lo tag \"%s\" es estat retirat de %s entitats", "commands.tag.remove.success.single": "Tag retirat '%s' de %s", "commands.team.add.duplicate": "Una còla amb aquel nom existís ja", "commands.team.add.success": "La còla %s es creada", "commands.team.empty.success": "Retirada de %s membre(s) de la còla %s", "commands.team.empty.unchanged": "Res es cambiat. Aquela còla es ja vuèga", "commands.team.join.success.multiple": "S'es apondut %s membres a la còla %s", "commands.team.join.success.single": "Ajust de %s a l'equipa %s", "commands.team.leave.success.multiple": "Retirada de %s membres de lor còla", "commands.team.leave.success.single": "%s es partit de sa còla", "commands.team.list.members.empty": "I a pas cap de membres dins la còla %s", "commands.team.list.members.success": "La còla %s ten %s membre(s) : %s", "commands.team.list.teams.empty": "I a pas cap de còlas", "commands.team.list.teams.success": "I a %s còla(s) : %s", "commands.team.option.collisionRule.success": "La règla de tust per la còla %s es ara \"%s\"", "commands.team.option.collisionRule.unchanged": "Res es cambiat. La règla de tust es ja a aquela valor", "commands.team.option.color.success": "La color de la còla %s es ara %s", "commands.team.option.color.unchanged": "Res es cambiat. Es ja la color d'aquela còla", "commands.team.option.deathMessageVisibility.success": "La visibilitat dels messatges de mòrt per la còla %s es ara \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Res es cambiat. La visibilitat dels messatges de mòrt es ja a aquela valor", "commands.team.option.friendlyfire.alreadyDisabled": "Res es cambiat. Lo tir aliat es ja desactivat per aquela còla", "commands.team.option.friendlyfire.alreadyEnabled": "Res es cambiat. Lo tir aliat es ja activat per aquela còla", "commands.team.option.friendlyfire.disabled": "Lo tir aliat es estat desactivat per la còla %s", "commands.team.option.friendlyfire.enabled": "Lo tir aliat es estat activat per la còla %s", "commands.team.option.name.success": "Lo nom de la còla %s es estat actualizat", "commands.team.option.name.unchanged": "Res es cambiat. Es ja lo nom d'aquela còla", "commands.team.option.nametagVisibility.success": "La visibilitat dels noms per la còla %s es ara \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Res es cambiat. La visibilitat dels noms es ja a aquela valor", "commands.team.option.prefix.success": "Lo prefix de còla es definit a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Res es cambiat. <PERSON><PERSON>a c<PERSON>la pòt ja pas véser sieus coequipièr invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Res es cambiat. Aquela còla pòt ja véser sieus coequipièr invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "La còla %s pòt pas mai véser los coequipièr invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "La còla %s pòt ara véser los coequipièr invisibles", "commands.team.option.suffix.success": "Lo sufix de còla es definit a %s", "commands.team.remove.success": "La còla %s es estada suprimida", "commands.teammsg.failed.noteam": "Vos cal èsser dins una còla per mandar un messatge de còla", "commands.teleport.invalidPosition": "Posicion pas valida per la teleportacion", "commands.teleport.success.entity.multiple": "%s entitats son estadas teleportadas cap a %s", "commands.teleport.success.entity.single": "%s es estat teleportat cap a %s", "commands.teleport.success.location.multiple": "%s entitats son estadas teleportadas en %s, %s, %s", "commands.teleport.success.location.single": "%s es estat teleportat cap a %s,%s,%s", "commands.test.batch.starting": "Desmaratge del lòt %s del environament %s", "commands.test.clear.error.no_tests": "Se pòt pas trobar un tèst a esfaçar", "commands.test.clear.success": "Escafada de %s estructura(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Clicatz per copiar dins lo cachapapièrs", "commands.test.create.success": "Configuracion creada per lo tèst \"%s\"", "commands.test.error.no_test_containing_pos": "Se pòt pas trobar una instància de tèst que contenga %s, %s, %s", "commands.test.error.no_test_instances": "Cap d'instància de tèst es estada trobada", "commands.test.error.non_existant_test": "Se pòt pas trobar lo tèst \"%s\"", "commands.test.error.structure_not_found": "Se pòt pas trobar l'estructura de tèst \"%s\"", "commands.test.error.test_instance_not_found": "Se pòt pas trobar l'entitat de blòc d'instància de tèst", "commands.test.error.test_instance_not_found.position": "Se pòt pas trobar l'entitat de blòc d'instància de tèst en %s, %s, %s", "commands.test.error.too_large": "La talha deu èsser inferior a %s sus cada axe", "commands.test.locate.done": "Localizacion teminada, %s estructura(s) trobada(s)", "commands.test.locate.found": "Estructura trobada en %s (distància : %s)", "commands.test.locate.started": "Desmarratge de la localizacion de las estructuras de tèst, çò poiriá prene de temps...", "commands.test.no_tests": "Cap de tèst a far", "commands.test.relative_position": "Posicion relatiu a %s : %s", "commands.test.reset.error.no_tests": "Se pòt pas trobar un tèst a reïnicializar", "commands.test.reset.success": "Reïnicializacion de %s estructura(s)", "commands.test.run.no_tests": "Cap de tèst es estat trobat", "commands.test.run.running": "Execucion de %s tèst(s)...", "commands.test.summary": "Tèst del jòc terminat ! %s tèst(s) fach(es)", "commands.test.summary.all_required_passed": "Tots los tèsts requerits capitavan :)", "commands.test.summary.failed": "%s tèst(s) requerit(s) capitava(n) pas :(", "commands.test.summary.optional_failed": "%s tèst(s) facultatiu(s) capitava(n) pas", "commands.tick.query.percentiles": "Percentils : P50 : %s ms, P95 : %s ms, P99 : %s ms, escapolon : %s", "commands.tick.query.rate.running": "Velocitat dels cicles ciblada : %s per segonda\nTemps mejan per cicle : %s ms (cibla : %s ms)", "commands.tick.query.rate.sprinting": "Velocitat dels cicles ciblada : %s per segonda (i<PERSON><PERSON>, sonque referén<PERSON>)\nTemps mejan per cicle : %s ms", "commands.tick.rate.success": "La velocitat dels cicles es estada definida a %s per segonda", "commands.tick.sprint.report": "Abrivada terminada amb %s cicles per segonda o %s ms per cicle", "commands.tick.sprint.stop.fail": "Cap d'abrivada de cicles en cors", "commands.tick.sprint.stop.success": "Abrivada de cicles copada", "commands.tick.status.frozen": "Lo jòc se palfica", "commands.tick.status.lagging": "<PERSON> jòc fonciona, mas capita pas a seguir la velocitat de cicles ciblada", "commands.tick.status.running": "Lo jòc fonciona normalament", "commands.tick.status.sprinting": "Lo jòc es abrivat", "commands.tick.step.fail": "Se pòt pas avançar lo jòc, el deu primièr se palficar", "commands.tick.step.stop.fail": "Cap d'avançada de cicles en cors", "commands.tick.step.stop.success": "Avançada de cicles copada", "commands.tick.step.success": "Avançada de %s cicle(s)", "commands.time.query": "Lo temps es %s", "commands.time.set": "Temps fixat a %s", "commands.title.cleared.multiple": "Los títols son estats esfaçats per %s jogaires", "commands.title.cleared.single": "Los títols son estats esfaçats per %s", "commands.title.reset.multiple": "Las opcions de títols son estadas reïnicializadas per %s jogaires", "commands.title.reset.single": "Las opcions de títols son estadas reïnicializadas per %s", "commands.title.show.actionbar.multiple": "Afichatge d'un nòu títol de barra d'acion per %s jogaires", "commands.title.show.actionbar.single": "Afichatge d'un nòu títol de barra d'acion per %s", "commands.title.show.subtitle.multiple": "Afichatge d'un nòu sostitol per %s jogaires", "commands.title.show.subtitle.single": "Afichatge d'un nòu sostitol per %s", "commands.title.show.title.multiple": "Afichatge d'un nòu títol per %s jogaires", "commands.title.show.title.single": "Afichatge d'un nòu títol per %s", "commands.title.times.multiple": "Los temps d'afichatge del títol cambian per %s jogaires", "commands.title.times.single": "Los temps d'afichatge del títol cambian per %s", "commands.transfer.error.no_players": "Devètz especificar almens un jogaire a transferir", "commands.transfer.success.multiple": "Transferiment de %s jogaires vèrs %s : %s", "commands.transfer.success.single": "Transferiment de %s vèrs %s : %s", "commands.trigger.add.success": "Activat %s (%s ajustat a valor)", "commands.trigger.failed.invalid": "Podètz sonque amodar los objectius de tipe \"trigger\"", "commands.trigger.failed.unprimed": "Podètz pas ja amodar aquel objectiu", "commands.trigger.set.success": "Activat %s (valor definida a %s)", "commands.trigger.simple.success": "Activat %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Informacions sus la version del servidor :", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = non", "commands.version.stable.yes": "stable = òc", "commands.waypoint.list.empty": "I a pas cap de balisas en %s", "commands.waypoint.list.success": "I a %s balisa(s) en %s : %s", "commands.waypoint.modify.color": "La color de la balisa es ara : %s", "commands.waypoint.modify.color.reset": "Reïnicializar la color de la balisa", "commands.waypoint.modify.style": "Estile del punt de repèr", "commands.weather.set.clear": "Lo temps se lèva", "commands.weather.set.rain": "Lo temps es de pluèja", "commands.weather.set.thunder": "Lo temps ven auratjós", "commands.whitelist.add.failed": "Lo jogaire es ja dins la lista blanca", "commands.whitelist.add.success": "%s es estat apondut dins la tièira blanca", "commands.whitelist.alreadyOff": "La lista blanca es ja desactivada", "commands.whitelist.alreadyOn": "La lista blanca es ja activada", "commands.whitelist.disabled": "La lista blanca es ara desactivada", "commands.whitelist.enabled": "La lista blanca es ara activada", "commands.whitelist.list": "I a %s jogaire(s) sus la lista blanca : %s", "commands.whitelist.none": "I a pas cap de jogaire sus la lista blanca", "commands.whitelist.reloaded": "Tièira blanca tornada cargar", "commands.whitelist.remove.failed": "Lo jogaire es pas sus la lista blanca", "commands.whitelist.remove.success": "%s es pas mai dins la tièira blanca", "commands.worldborder.center.failed": "Res es cambiat. La bordadura del mond es ja centrada aicí", "commands.worldborder.center.success": "Lo centre de la bordadura del mond es ara en %s, %s", "commands.worldborder.damage.amount.failed": "Res es cambiat. La bordadura del mond ja infligís aquel nombre de degalh", "commands.worldborder.damage.amount.success": "La bordadura del mond infligís ara %s degalh(s) per blòc per segonda", "commands.worldborder.damage.buffer.failed": "Res es cambiat. Lo tampon de degalh de la bordadura del mond es ja d'aquela distància", "commands.worldborder.damage.buffer.success": "La zòna tampon de degalhs de la bordadura del mond es definida a %s blòc(s)", "commands.worldborder.get": "La bordadura del mond es actualament de %s blòc(s) de largor", "commands.worldborder.set.failed.big": "La bordadura del mond pòt pas èsser mai grana que %s blòcs de largor", "commands.worldborder.set.failed.far": "La bordadura del mond pòt pas èsser a mai de %s blòcs", "commands.worldborder.set.failed.nochange": "Res es cambiat. La bordadura del mond es ja d'aquela talha", "commands.worldborder.set.failed.small": "La bordadura del mond pòt pas èsser mai petit qu'un blòc de largor", "commands.worldborder.set.grow": "Espandiment de la bordadura del mond a %s blòcs de larg en %s segondas", "commands.worldborder.set.immediate": "La bordadura del mond es estat reglada sus %s blòc(s) de larg", "commands.worldborder.set.shrink": "Estrechiment de la bordadura del mond a %s blòc(s) de larg en %s segonda(s)", "commands.worldborder.warning.distance.failed": "Res es cambiat. L'avís de la bordadura del mond es ja a aquela distància", "commands.worldborder.warning.distance.success": "La distància d'avís de la bordadura del mond es ara definida a %s blòc(s)", "commands.worldborder.warning.time.failed": "Res es cambiat. L'avís de la bordadura del mond es ja d'aquela durada", "commands.worldborder.warning.time.success": "Lo temps d'avís de la bordadura del mond es ara defiinit a %s segonda(s)", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON><PERSON> dem<PERSON>i mai de 24 òras", "compliance.playtime.hours": "Jogatz dempuèi %s òra(s)", "compliance.playtime.message": "<PERSON><PERSON> trò<PERSON> longtemps pòt interferir amb la vòstra vida de cada jorn", "connect.aborted": "Connexion interrompuda", "connect.authorizing": "Autentificacion...", "connect.connecting": "Connexion au servidor...", "connect.encrypting": "Chiframent en cors...", "connect.failed": "La connexion au servidor a pas capitat", "connect.failed.transfer": "Mèuca de connexion al moment del cambiament de servidor", "connect.joining": "Entrada dins lo monde...", "connect.negotiating": "Negociacion en cors...", "connect.reconfiging": "Reconfiguracion en cors...", "connect.reconfiguring": "Reconfiguracion en cors...", "connect.transferring": "Transferiment vèrs un nòu servidor...", "container.barrel": "Barrica", "container.beacon": "Balisa", "container.beehive.bees": "Abelhas : %s / %s", "container.beehive.honey": "Mèu : %s / %s", "container.blast_furnace": "Fornèl de fusion", "container.brewing": "Alambic", "container.cartography_table": "Taula de cartografia", "container.chest": "Còfre", "container.chestDouble": "Còfre Grand", "container.crafter": "Fabricator", "container.crafting": "Fabricacion", "container.creative": "Seleccion de l'objècte", "container.dispenser": "Distribuidor", "container.dropper": "Dropper", "container.enchant": "Encantar", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lapislazuli", "container.enchant.lapis.one": "1 lapislazuli", "container.enchant.level.many": "%s nivèus d'encantament", "container.enchant.level.one": "1 nivèu d'encantament", "container.enchant.level.requirement": "Nivèu requesit: %s", "container.enderchest": "Còfre de l'End", "container.furnace": "Forn", "container.grindstone_title": "Reparer e desembreissar", "container.hopper": "Embut", "container.inventory": "Inventari", "container.isLocked": "%s es varrolhat!", "container.lectern": "<PERSON><PERSON><PERSON><PERSON>", "container.loom": "Telièr", "container.repair": "Reparar e Nomenar", "container.repair.cost": "Còst : %1$s", "container.repair.expensive": "Tròp Car!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s × %s", "container.shulkerBox.more": "e %s mai...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Impossible de dobrir. Los objèctes son pas estats encara generats.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "Malhorar l'equipament", "container.upgrade.error_tooltip": "Aquel objècte pòt pas èsser melhorat d'aquela faiçon", "container.upgrade.missing_template_tooltip": "Apondètz un modèl de fargatge", "controls.keybinds": "Assignacion de las tòcas...", "controls.keybinds.duplicateKeybinds": "Aquela tòca es tanben utilizada per :\n%s", "controls.keybinds.title": "Assignac<PERSON> de las tòcas", "controls.reset": "Reï<PERSON>.", "controls.resetAll": "Reïnicializar las tòcas", "controls.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Causissètz un biòma", "createWorld.customize.buffet.title": "Personalizacion del monde bufet", "createWorld.customize.flat.height": "Nautor", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Fons - %s", "createWorld.customize.flat.layer.top": "Surfàcia - %s", "createWorld.customize.flat.removeLayer": "Enlevar la Jaça", "createWorld.customize.flat.tile": "Materiau de la jaça", "createWorld.customize.flat.title": "Personalizacion dau monde subreplanièr", "createWorld.customize.presets": "Prereglatges", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON>, ne vaquí quauques unes qu'avèm creat per vos!", "createWorld.customize.presets.select": "<PERSON><PERSON>gar aqueste prereglatge", "createWorld.customize.presets.share": "Volètz partejar vòstre prereglatge amb qualqu'un? Emplegatz la bóstia çai-jos!", "createWorld.customize.presets.title": "Seleccionatz un paramètre predefinit", "createWorld.preparing": "Preparacion de la creacion dau monde...", "createWorld.tab.game.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.more.title": "<PERSON>", "createWorld.tab.world.title": "Mond", "credits_and_attribution.button.attribution": "Atribucions", "credits_and_attribution.button.credits": "Crè<PERSON><PERSON>", "credits_and_attribution.button.licenses": "Licéncias", "credits_and_attribution.screen.title": "Crèdits e atribucions", "dataPack.bundle.description": "Activa l'objècte experimentau \"Sac\"", "dataPack.bundle.name": "Sacs", "dataPack.locator_bar.description": "Mostrar la direccion dels autres jogaires en multijogaires", "dataPack.locator_bar.name": "Barra de localisacion", "dataPack.minecart_improvements.description": "Movement dels vagonets melhorats", "dataPack.minecart_improvements.name": "Melhorança dels vagonnets", "dataPack.redstone_experiments.description": "Cambiaments experimentals sus la redstone", "dataPack.redstone_experiments.name": "Experimentacions sus la redstone", "dataPack.title": "Seleccionar de paquets de donadas", "dataPack.trade_rebalance.description": "Escambis de vilatgés renovelats", "dataPack.trade_rebalance.name": "Equilibri dels escambis", "dataPack.update_1_20.description": "Novetats per Minecraft 1.20", "dataPack.update_1_20.name": "Mesa a jorno 1.20", "dataPack.update_1_21.description": "Novetats per Minecraft 1.21", "dataPack.update_1_21.name": "Mesa a jorno 1.21", "dataPack.validation.back": "<PERSON><PERSON>", "dataPack.validation.failed": "La validacion dels packs de donadas capitava pas !", "dataPack.validation.reset": "Restab<PERSON>r lo paquet per defaut", "dataPack.validation.working": "Validacion dels packs de donadas causits...", "dataPack.vanilla.description": "Las donadas per defaut de Minecraft", "dataPack.vanilla.name": "<PERSON> defaut", "dataPack.winter_drop.description": "Novetats pel drop ivernal", "dataPack.winter_drop.name": "Drop ivernal", "datapackFailure.safeMode": "Mòde sens fracàs", "datapackFailure.safeMode.failed.description": "Aquel mond conten de donadas de sauvagarda pas validas o corrompudas.", "datapackFailure.safeMode.failed.title": "Mèuca del cargament del mond en mòde sens mèuca.", "datapackFailure.title": "Mèucas dins los packs de donadas causits empachan lo cargament del mond.\nPodètz siá ensajar de cargar lo mond amb solament lo pack de donadas vanilla (\"Mòde sens mèucas\"), siá tornar al menut principal e corregir lo problèma manualament.", "death.attack.anvil": "%1$s foguèt esclafat per un enclutge", "death.attack.anvil.player": "%1$s foguèt esclafat per un enclutge mentre que combatiá (un(a)) %2$s", "death.attack.arrow": "%1$s foguèt tuat pel tir de %2$s", "death.attack.arrow.item": "%1$s foguèt tuat pel tir de %2$s en emplegant %3$s", "death.attack.badRespawnPoint.link": "una fonctionalitat intencionala", "death.attack.badRespawnPoint.message": "%1$s foguèt tuat per %2$s", "death.attack.cactus": "%1$s foguèt ponch fins la mòrt", "death.attack.cactus.player": "%1$s caminèt dins un cactus mentre qu'ensajava d'escapar a %2$s", "death.attack.cramming": "%1$s es estat aplatit", "death.attack.cramming.player": "%1$s es estat aplatit per %2$s", "death.attack.dragonBreath": "%1$s s'es fach rostir pel buf d'un dragon", "death.attack.dragonBreath.player": "%1$s s'es fach rostir pel buf d'un dragon per %2$s", "death.attack.drown": "%1$s se neguèt", "death.attack.drown.player": "%1$s se neguèt mentre qu'ensajava d'escapar a %2$s", "death.attack.dryout": "%1$s es mòrt de desitratacion", "death.attack.dryout.player": "%1$s es mòrt de desitratacion en ensajant de fugir (un(a)) %2$s", "death.attack.even_more_magic": "%1$s foguèt tuat amb encara mai de magia", "death.attack.explosion": "%1$s espetèt", "death.attack.explosion.player": "%1$s espetèt en causa de %2$s", "death.attack.explosion.player.item": "%1$s a explosat per %2$s en utilizant %3$s", "death.attack.fall": "%1$s s'esclafèt au sòu", "death.attack.fall.player": "%1$s s'esclafèt au sòu en ensajant de fugir (un(a)) %2$s", "death.attack.fallingBlock": "%1$s foguèt esclafat per un blòc", "death.attack.fallingBlock.player": "%1$s foguèt esclafat per un blòc en comabtent (un(a)) %2$s", "death.attack.fallingStalactite": "%1$s foguèt trasforat per una estalactita", "death.attack.fallingStalactite.player": "%1$s foguèt trasforat per una estalactita mentre que combatiá (un(a)) %2$s", "death.attack.fireball": "%1$s foguèt tuat per la bola de fuòc de %2$s", "death.attack.fireball.item": "%1$s foguèt tuat per la bola de fuòc de %2$s en emplegant %3$s", "death.attack.fireworks": "%1$s s'es fach petar", "death.attack.fireworks.item": "%1$s s'es fach petar dins un fuòc d'artifici tirat per %2$s amb %3$s", "death.attack.fireworks.player": "%1$s s'es fach petar mentre que combatiá %2$s", "death.attack.flyIntoWall": "%1$s a descobèrt l'energia cinetica", "death.attack.flyIntoWall.player": "%1$s a descobèrt l'energia cinetica en ensajant de fugir (un(a)) %2$s", "death.attack.freeze": "%1$s es mòrt(a) de freg", "death.attack.freeze.player": "%1$s foguèt tuat amb de freg per %2$s", "death.attack.generic": "%1$s es mòrt(a)", "death.attack.generic.player": "%1$s es mòrt per %2$s", "death.attack.genericKill": "%1$s foguèt tuat", "death.attack.genericKill.player": "%1$s foguèt tuat mentre que combatiá (un(a)) %2$s", "death.attack.hotFloor": "%1$s a descobèrt que lo sòu èra en lava", "death.attack.hotFloor.player": "%1$s a caminat dins una zòna dangierosa en causa de/d'un(a) %2$s", "death.attack.inFire": "%1$s caminèt dins de fuòc", "death.attack.inFire.player": "%1$s caminèt dins de fuòc mentre que combatiá %2$s", "death.attack.inWall": "%1$s s'asfixièt dins un mur", "death.attack.inWall.player": "%1$s s'asfixièt dins un mur mentre que combatiá (un(a)) %2$s", "death.attack.indirectMagic": "%1$s foguèt chaplat per %2$s en emplegant de magia", "death.attack.indirectMagic.item": "%1$s foguèt tuat per %2$s en emplegant %3$s", "death.attack.lava": "%1$s ensagèt de nadar dins la lava", "death.attack.lava.player": "%1$s ensagèt de nadar dins la lava per escapar a %2$s", "death.attack.lightningBolt": "%1$s es estat folzejat", "death.attack.lightningBolt.player": "%1$s es estat folzejat mentre que combatiá (un(a)) %2$s", "death.attack.mace_smash": "%1$s es maçat per (un(a)) %2$s", "death.attack.mace_smash.item": "%1$s es maçat per (un(a)) %2$s amb %3$s", "death.attack.magic": "%1$s foguèt tuat amb de magia", "death.attack.magic.player": "%1$s foguèt tuat amb magia en ensajant de fugir (un(a)) %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON><PERSON>, lo messatge es tròp long per èsser afichat completament. Aicí i a una version reducha : %s", "death.attack.mob": "%1$s foguèt tuat per %2$s", "death.attack.mob.item": "%1$s foguèt tuat per %2$s en emplegant %3$s", "death.attack.onFire": "%1$s es mòrt cremat", "death.attack.onFire.item": "%1$s es mòrt cremat mentre que combatiá (un(a)) %2$s brandissent %3$s", "death.attack.onFire.player": "%1$s es mòrt cremat mentre que combatiá %2$s", "death.attack.outOfWorld": "%1$s tombèt fòra lo mond", "death.attack.outOfWorld.player": "%1$s volguèt pas viure dins lo meteis monde que %2$s", "death.attack.outsideBorder": "%1$s a quitat las raras d'aquel mond", "death.attack.outsideBorder.player": "%1$s a quitat las raras d'aquel mond mentre que combatiá (un(a)) %2$s", "death.attack.player": "%1$s foguèt tuat per %2$s", "death.attack.player.item": "%1$s foguèt tuat per %2$s en emplegant %3$s", "death.attack.sonic_boom": "%1$s foguèt anientar per un udolament sonic", "death.attack.sonic_boom.item": "%1$s foguèt anientar per un udolament sonic en ensajant de fugir (un(a)) %2$s brandissent %3$s", "death.attack.sonic_boom.player": "%1$s foguèt anientar per un udolament sonic en ensajant de fugir (un(a)) %2$s", "death.attack.stalagmite": "%1$s es estat empalat sus un estalagmita", "death.attack.stalagmite.player": "%1$s es estat empalat sus un estalagmita mentre que combatiá (un(a)) %2$s", "death.attack.starve": "%1$s es mòrt de fam", "death.attack.starve.player": "%1$s es mòrt de fam mentre que combatiá %2$s", "death.attack.sting": "%1$s foguèt picat fins la mòrt", "death.attack.sting.item": "%1$s foguèt picat fins la mòrt per %2$s en emplegant %3$s", "death.attack.sting.player": "%1$s foguèt picat fins la mòrt per %2$s", "death.attack.sweetBerryBush": "%1$s foguèt ponch fins la mòrt per un arbust de bagas doças", "death.attack.sweetBerryBush.player": "%1$s foguèt ponch fins la mòrt per un arbust de bagas doças en ensajant de fugir (un(a)) %2$s", "death.attack.thorns": "%1$s foguèt tuat mentre qu'ensajava de nafrar %2$s", "death.attack.thorns.item": "%1$s foguèt tuat per %3$s en ensajant de nafrar (un(a)) %2$s", "death.attack.thrown": "%1$s fogu<PERSON>t chaplat per %2$s", "death.attack.thrown.item": "%1$s foguèt chaplat per %2$s en emplegant %3$s", "death.attack.trident": "%1$s es estat empalat per (un(a)) %2$s", "death.attack.trident.item": "%1$s es estat empalat per (un(a)) %2$s amb %3$s", "death.attack.wither": "%1$s foguèt tuat per un wither", "death.attack.wither.player": "%1$s es mòrt(a) de marciment mentre que combatiá %2$s", "death.attack.witherSkull": "%1$s foguèt tuat per un cran de Wither tirat per (un(a)) %2$s", "death.attack.witherSkull.item": "%1$s foguèt tuat per un cran de Wither tirat per (un(a)) %2$s amb %3$s", "death.fell.accident.generic": "%1$s tombèt d'un luòc naut", "death.fell.accident.ladder": "%1$s tombèt d'una escala", "death.fell.accident.other_climbable": "%1$s tombèt mentre escalava", "death.fell.accident.scaffolding": "%1$s tombèt d'un enart", "death.fell.accident.twisting_vines": "%1$s tombèt d'unas lianas torsadas", "death.fell.accident.vines": "%1$s tombèt d'unas lianas", "death.fell.accident.weeping_vines": "%1$s tombèt d'unas lianas plorosas", "death.fell.assist": "%1$s foguèt condemnat de tombar per %2$s", "death.fell.assist.item": "%1$s foguèt condemnat de tombar per %2$s en emplegant %3$s", "death.fell.finish": "%1$s se tombèt e foguèt acabat per %2$s", "death.fell.finish.item": "%1$s se tombèt e foguèt acabat per %2$s en emplegant %3$s", "death.fell.killer": "%1$s foguèt condemnat de tombar", "deathScreen.quit.confirm": "<PERSON><PERSON><PERSON> segur de voler quitar?", "deathScreen.respawn": "<PERSON><PERSON>", "deathScreen.score": "<PERSON><PERSON>", "deathScreen.score.value": "Marca : %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON> lo monde", "deathScreen.title": "<PERSON><PERSON><PERSON> mòrt(a)!", "deathScreen.title.hardcore": "Partida acabada!", "deathScreen.titleScreen": "<PERSON>ut principau", "debug.advanced_tooltips.help": "F3 + H = Avançar las botiòlas-conselh", "debug.advanced_tooltips.off": "Botiòlas-con<PERSON><PERSON> a<PERSON>: amagadas", "debug.advanced_tooltips.on": "Botiòlas-conselh a<PERSON>das: visiblas", "debug.chunk_boundaries.help": "F3 + G = Mostrar los limites de tròces", "debug.chunk_boundaries.off": "Limites de tròces: amagats", "debug.chunk_boundaries.on": "Limites de tròces: visibles", "debug.clear_chat.help": "F3 + D = Netejar la charrada", "debug.copy_location.help": "F3 + C = Copiar la posicion coma /tp comanda, manténer F3 + C per crash<PERSON>r el juòc", "debug.copy_location.message": "Posicion copiada dins lo cachapapièrs", "debug.crash.message": "F3 + C èst mantengut. Aquò harà crachèr el juòc.", "debug.crash.warning": "Crash en %s...", "debug.creative_spectator.error": "Avètz pas la permission de cambiar de mòde de jòc", "debug.creative_spectator.help": "F3 + N = Alterna mòde de juòc precedent <-> espectator", "debug.dump_dynamic_textures": "Texturas dinamicas enregistradas dins %s", "debug.dump_dynamic_textures.help": "F3 + S = Exportar las texturas dinamicas", "debug.gamemodes.error": "<PERSON><PERSON><PERSON><PERSON> pas permission de dobrir lo selector de mòde de juòc", "debug.gamemodes.help": "F3 + F4 = Dobrir lo selector de mòde de juòc", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s seguent", "debug.help.help": "F3 + Q = Mostrar aquesta lista", "debug.help.message": "<PERSON>rch<PERSON> clavi<PERSON>:", "debug.inspect.client.block": "Las donadas dau blòc dau client son estats copiats dins lo cachapapièrs", "debug.inspect.client.entity": "Las donadas de l'entitat dau client son estats copiats dins lo cachapapièrs", "debug.inspect.help": "F3 + I = Copiar las donadas d'entitat o de blòc dins lo cachapapièrs", "debug.inspect.server.block": "Las donadas dau blòc dau servidor son estats copiats dins lo cachapapièrs", "debug.inspect.server.entity": "Las donadas de l'entitat dau servidor son estats copiats dins lo cachapapièrs", "debug.pause.help": "F3 + Esc = Pausa sens lo menut de pausa (se la pausa es possibla)", "debug.pause_focus.help": "F3 + P = Pausar au moment de pèrda de focus", "debug.pause_focus.off": "Pausa au moment de pèrda de focus: desactivada", "debug.pause_focus.on": "Pausa au moment de pèrda de focus: activada", "debug.prefix": "[Desbogatge]:", "debug.profiling.help": "F3 + L = Amodar/Arrestar lo perfilatge", "debug.profiling.start": "Desmarratge del perfilatge pendent %s segondas. Utilizatz F3 + L per copar", "debug.profiling.stop": "Fin del perfilatge. Resultats enregistrats dins %s", "debug.reload_chunks.help": "F3 + A = Recargar los tròces", "debug.reload_chunks.message": "Recargament de totes los tròces", "debug.reload_resourcepacks.help": "F3 + T = <PERSON>nar cargar los paquets de ressorsa", "debug.reload_resourcepacks.message": "Paquets de ressorsa tornat cargar", "debug.show_hitboxes.help": "F3 + B = Mostrar las bóstias de collision", "debug.show_hitboxes.off": "Bóstias de collision: amagadas", "debug.show_hitboxes.on": "Bóstias de collision: visiblas", "debug.version.header": "Informacions sus la version del client :", "debug.version.help": "F3 + V = Informacions sus la version del client", "demo.day.1": "Aquesta demonstracion durarà cinc jorns de juòc. Fasètz çò melhor que podètz!", "demo.day.2": "<PERSON><PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON>", "demo.day.4": "Quatren Jo<PERSON>", "demo.day.5": "Aquò es lo vòstre darri<PERSON>r jorn!", "demo.day.6": "Vòstres cinc jorns sont passats. Emplegatz %s per gardar una captura d'ecran de vòstra creacion.", "demo.day.warning": "Vòstre temps es gaireben acabat!", "demo.demoExpired": "Lo temps de demonstracion es acabat!", "demo.help.buy": "<PERSON><PERSON><PERSON> Ara!", "demo.help.fullWrapped": "Aquesta demostracion durarà cinc jorns dins lo juòc (aperaquí 1 ora e 40 minutas realas). Agachatz los progrèsses per vos ajudar! Bon juòc!", "demo.help.inventory": "Utilisatz %1$s per dobrir vòstre inventari", "demo.help.jump": "Sautatz amb %1$s", "demo.help.later": "<PERSON><PERSON><PERSON> de jogar!", "demo.help.movement": "Utilizatz %1$s, %2$s, %3$s, %4$s e la mirga per bolegar", "demo.help.movementMouse": "Agachatz a l'entorn de vosautres amb la mirga", "demo.help.movementShort": "Bolegatz amb %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft Mòde Demonstracion", "demo.remainingTime": "Temps restant: %s", "demo.reminder": "Lo temps de demonstracion es passat. Crompatz lo juòc per contunhar o creatz un monde nòu!", "difficulty.lock.question": "Sètz segur que volètz varrolhar la dificultat d'aqueste mond? Lo mond serà totjorn en %1$s, e poiretz pas mai jamai cambiar aquò.", "difficulty.lock.title": "Varrolhar la dificultat del mond", "disconnect.endOfStream": "Fin dau flus", "disconnect.exceeded_packet_rate": "Expulsat(ada) per aver despassar la limita de debit de paquets", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Requèsta d'estatut ignorada", "disconnect.loginFailedInfo": "L'autentificacian a pas capitat: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Lo mòde multijogaires es desactivat. Se vos plai, verificatz los paramètres del sieu compte Microsoft.", "disconnect.loginFailedInfo.invalidSession": "Session pas valida (Ensajatz de tornar iniciar vòstre juòc e lo lançador)", "disconnect.loginFailedInfo.serversUnavailable": "Lo servici d'autentification es per ara pas accessibles. Se vos plai, tornatz ensajar.", "disconnect.loginFailedInfo.userBanned": "Estàs fòrabandit(ida) del jòc en linha", "disconnect.lost": "Connexion perduda", "disconnect.packetError": "Mèuca del protocòl ret", "disconnect.spam": "Expulsat per spam", "disconnect.timeout": "Temps d'espèra passat", "disconnect.transfer": "Transferit(ida) vèrs un aute servidor", "disconnect.unknownHost": "Òste desconegut", "download.pack.failed": "Mèuca del telecargament de %s pack(s) sus %s", "download.pack.progress.bytes": "Progression : %s (Talha totala desconeguda)", "download.pack.progress.percent": "Progression : %s %%", "download.pack.title": "Telecargament del pack de ressorsa %s/%s", "editGamerule.default": "Per defaut: %s", "editGamerule.title": "Modificar las règlas dau juòc", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorpcion", "effect.minecraft.bad_omen": "<PERSON><PERSON>", "effect.minecraft.blindness": "Cecitat", "effect.minecraft.conduit_power": "Fòrça de conduch", "effect.minecraft.darkness": "Foscor", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON><PERSON> dau daufin", "effect.minecraft.fire_resistance": "Resisténcia au fuòc", "effect.minecraft.glowing": "Subrebrilhança", "effect.minecraft.haste": "Preissa", "effect.minecraft.health_boost": "Bonus de vida", "effect.minecraft.hero_of_the_village": "Eròi del vilatge", "effect.minecraft.hunger": "Fam", "effect.minecraft.infested": "Enfeciment", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON> instant<PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON>", "effect.minecraft.invisibility": "Invisibilitat", "effect.minecraft.jump_boost": "Melhorança daus sauts", "effect.minecraft.levitation": "Levitacion", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Fatiga per minar", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Vision nocturna", "effect.minecraft.oozing": "Viscositat", "effect.minecraft.poison": "Poison", "effect.minecraft.raid_omen": "Presagi d'invasion", "effect.minecraft.regeneration": "Regeneracion", "effect.minecraft.resistance": "Resisténcia", "effect.minecraft.saturation": "Saturacion", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "<PERSON><PERSON>", "effect.minecraft.speed": "Velocitat", "effect.minecraft.strength": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "Presagi <PERSON>'es<PERSON>", "effect.minecraft.unluck": "Malastre", "effect.minecraft.water_breathing": "Respiracion aqüatica", "effect.minecraft.weakness": "Flaquesa", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "<PERSON><PERSON>", "effect.minecraft.wither": "Descomposicion", "effect.none": "Cap d'efièch", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinitat aqüatica", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON><PERSON> daus artropò<PERSON>", "enchantment.minecraft.binding_curse": "Malediccion dau ligam eternau", "enchantment.minecraft.blast_protection": "Resisténcia a las explosions", "enchantment.minecraft.breach": "Bèrca", "enchantment.minecraft.channeling": "Conductivitat", "enchantment.minecraft.density": "Densitat", "enchantment.minecraft.depth_strider": "Agilitat aqüatica", "enchantment.minecraft.efficiency": "Eficacitat", "enchantment.minecraft.feather_falling": "<PERSON><PERSON> pluma", "enchantment.minecraft.fire_aspect": "As<PERSON><PERSON><PERSON><PERSON> de fu<PERSON>", "enchantment.minecraft.fire_protection": "Resisténcia au fuòc", "enchantment.minecraft.flame": "Flama", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Caminaire de glaç", "enchantment.minecraft.impaling": "Empalament", "enchantment.minecraft.infinity": "Infinitat", "enchantment.minecraft.knockback": "Reculada", "enchantment.minecraft.looting": "Butin", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Astre de la <PERSON>r", "enchantment.minecraft.lure": "Esca", "enchantment.minecraft.mending": "Reparacion", "enchantment.minecraft.multishot": "Tir multiple", "enchantment.minecraft.piercing": "Perforacion", "enchantment.minecraft.power": "Poténcia", "enchantment.minecraft.projectile_protection": "Proteccion contra los projectiles", "enchantment.minecraft.protection": "Proteccion", "enchantment.minecraft.punch": "<PERSON><PERSON>", "enchantment.minecraft.quick_charge": "Carga rapida", "enchantment.minecraft.respiration": "Respiracion", "enchantment.minecraft.riptide": "Impulsion", "enchantment.minecraft.sharpness": "Ta<PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON> de seda", "enchantment.minecraft.smite": "Castig", "enchantment.minecraft.soul_speed": "Velocitat de las armas", "enchantment.minecraft.sweeping": "Talh desolator", "enchantment.minecraft.sweeping_edge": "Talh desolator", "enchantment.minecraft.swift_sneak": "Discrecion rapid", "enchantment.minecraft.thorns": "Espinas", "enchantment.minecraft.unbreaking": "Soliditat", "enchantment.minecraft.vanishing_curse": "Malediccion de desaparicion", "enchantment.minecraft.wind_burst": "Ventòria", "entity.minecraft.acacia_boat": "Batèu en cacièr", "entity.minecraft.acacia_chest_boat": "Batèu en cacièr amb còfre", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nivol d'efèit persistant", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "Pòrta-armadura", "entity.minecraft.arrow": "Sageta", "entity.minecraft.axolotl": "Axolòtl", "entity.minecraft.bamboo_chest_raft": "Rad<PERSON><PERSON> en bambó amb còfre", "entity.minecraft.bamboo_raft": "<PERSON>d<PERSON><PERSON> en bambó", "entity.minecraft.bat": "Ratapenada", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "Batèu en bèç", "entity.minecraft.birch_chest_boat": "Bat<PERSON>u en bèç amb còfre", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Visual de bloc", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "Enfagat", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Carga de vent", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "Cat", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "Batèu en cerièis", "entity.minecraft.cherry_chest_boat": "Bat<PERSON>u amb còfre en cerièis", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON> amb c<PERSON><PERSON>re", "entity.minecraft.chest_minecart": "<PERSON>agon<PERSON> amb còfre", "entity.minecraft.chicken": "Polastre", "entity.minecraft.cod": "Merluça", "entity.minecraft.command_block_minecart": "Vagonet amb bl<PERSON><PERSON> de comandas", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "Cruisseire", "entity.minecraft.creaking_transient": "Cruisseire", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Bat<PERSON>u en euse escur", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> en euse escur amb còfre", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "<PERSON><PERSON> de fuòc de dragon", "entity.minecraft.drowned": "Negat", "entity.minecraft.egg": "<PERSON><PERSON><PERSON>", "entity.minecraft.elder_guardian": "Grand gardian", "entity.minecraft.end_crystal": "Cristau de l'End", "entity.minecraft.ender_dragon": "<PERSON> d'Ender", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'ender <PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "<PERSON><PERSON><PERSON>", "entity.minecraft.evoker": "Evocator", "entity.minecraft.evoker_fangs": "Cròcs d'evocator", "entity.minecraft.experience_bottle": "Fiòla d'experiéncia lançada", "entity.minecraft.experience_orb": "Òrbe d'experiéncia", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Blòc en casuda", "entity.minecraft.falling_block_type": "%s casent", "entity.minecraft.fireball": "<PERSON><PERSON>", "entity.minecraft.firework_rocket": "Fuòc d'artifici", "entity.minecraft.fishing_bobber": "Flotador", "entity.minecraft.fox": "<PERSON><PERSON>", "entity.minecraft.frog": "Granolha", "entity.minecraft.furnace_minecart": "Vagonet amb forn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Gigant", "entity.minecraft.glow_item_frame": "Quadre lusent", "entity.minecraft.glow_squid": "<PERSON>tena lusenta", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> amb embut", "entity.minecraft.horse": "Cavau", "entity.minecraft.husk": "Zòmbi momificat", "entity.minecraft.illusioner": "Illusionista", "entity.minecraft.interaction": "Interaccion", "entity.minecraft.iron_golem": "<PERSON><PERSON>", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Visual d'objècte", "entity.minecraft.item_frame": "Quadre", "entity.minecraft.jungle_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON><PERSON> j<PERSON>la amb c<PERSON>fre", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Nos de estaca", "entity.minecraft.lightning_bolt": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.lingering_potion": "Pocion persistenta", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Escopit de lama", "entity.minecraft.magma_cube": "Cube de magma", "entity.minecraft.mangrove_boat": "Batèu en manglièr", "entity.minecraft.mangrove_chest_boat": "Bat<PERSON>u en manglièr amb còfre", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Vagonet", "entity.minecraft.mooshroom": "Vacamparòu", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Bat<PERSON><PERSON> en euse", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON> en euse amb còfre", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Generator d'objè<PERSON><PERSON> fun<PERSON>t", "entity.minecraft.painting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Batèu en euse palle", "entity.minecraft.pale_oak_chest_boat": "Bat<PERSON><PERSON> en euse palle amb còfre", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Papagai", "entity.minecraft.phantom": "Fantauma", "entity.minecraft.pig": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON>lin ferotge", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Ors polar", "entity.minecraft.potion": "Pocion", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Devastator", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "Moton", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON> d'argent", "entity.minecraft.skeleton": "Esqueleta", "entity.minecraft.skeleton_horse": "Cavau esqueleta", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "<PERSON><PERSON> <PERSON> fu<PERSON><PERSON> p<PERSON>ona", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON>", "entity.minecraft.snowball": "<PERSON><PERSON>", "entity.minecraft.spawner_minecart": "Vagonet amb generator de creaturas", "entity.minecraft.spectral_arrow": "Sageta espectrala", "entity.minecraft.spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Pocion getabla", "entity.minecraft.spruce_boat": "Bat<PERSON>u en avet", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON> en avet amb còfre", "entity.minecraft.squid": "Tautena", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "Arpentaire", "entity.minecraft.tadpole": "Cap<PERSON>è<PERSON>", "entity.minecraft.text_display": "Visual de tèxt", "entity.minecraft.tnt": "TNT amorcada", "entity.minecraft.tnt_minecart": "Vagonet amb de TNT", "entity.minecraft.trader_llama": "<PERSON> ambulanta", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> t<PERSON>", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Cirurgian negre", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON> morisca", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON> ornat", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON> papa<PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON> roge", "entity.minecraft.tropical_fish.predefined.15": "Bavarèla a pot roja", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON> roge", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON> p<PERSON> tomata", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "Cirurgian blau", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON><PERSON> papagai de coa jauna", "entity.minecraft.tropical_fish.predefined.21": "Cirurgian jaune", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Ciclid", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Peis combatent", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON> ponchut", "entity.minecraft.tropical_fish.predefined.8": "Pagre emperaire roge", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON> a<PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Sautarin", "entity.minecraft.tropical_fish.type.glitter": "Pa<PERSON>he<PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Faïna", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Eliora<PERSON>", "entity.minecraft.turtle": "Tartuga", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Cartografe", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "Agricultor", "entity.minecraft.villager.fisherman": "Pescaire", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotecari", "entity.minecraft.villager.mason": "Maçon", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "Pastre", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON> d'a<PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON> d'armas", "entity.minecraft.vindicator": "Vindicator", "entity.minecraft.wandering_trader": "Mercant ambulant", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Carga de vent", "entity.minecraft.witch": "<PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON> wither", "entity.minecraft.wither_skull": "<PERSON><PERSON> wither", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zòmbi", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Zòmbi vilatgés", "entity.minecraft.zombified_piglin": "<PERSON>lin zombificat", "entity.not_summonable": "Pòt pas invocar a l'entitat de tipe \"%s\"", "event.minecraft.raid": "Invasion", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Invasion - <PERSON><PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Envasidors restants: %s", "event.minecraft.raid.victory": "Victòria", "event.minecraft.raid.victory.full": "Invasion - Victòria", "filled_map.buried_treasure": "Mapa del tresaur enterrat", "filled_map.explorer_jungle": "Mapa d'exploracion de las junglas", "filled_map.explorer_swamp": "Mapa d'exploracion dels paluns", "filled_map.id": "ID #%s", "filled_map.level": "(Nivèu %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.mansion": "Mapa d'exploracion dels bòsques", "filled_map.monument": "Mapa d'exploracion daus oceans", "filled_map.scale": "Escala 1 :%s", "filled_map.trial_chambers": "Mapa d'exploracion de las espròvas", "filled_map.unknown": "Mapa desconeguda", "filled_map.village_desert": "Mapa d'exploracion de vilatge del desèrt", "filled_map.village_plains": "Mapa d'exploracion de vilatge de las planas", "filled_map.village_savanna": "Mapa d'exploracion de vilatge de la savana", "filled_map.village_snowy": "Mapa d'exploracion de vilatge ennevat", "filled_map.village_taiga": "Mapa d'exploracion de vilatge de la taiga", "flat_world_preset.minecraft.bottomless_pit": "Potz sens fons", "flat_world_preset.minecraft.classic_flat": "Classic", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Overworld", "flat_world_preset.minecraft.redstone_ready": "Prèst per la redstone", "flat_world_preset.minecraft.snowy_kingdom": "Re<PERSON><PERSON> en<PERSON>", "flat_world_preset.minecraft.the_void": "Lo void", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON> daus trogloditas", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "Mòde aventura", "gameMode.changed": "Vòstre mòde de juòc es ara %s", "gameMode.creative": "<PERSON><PERSON><PERSON> creatiu", "gameMode.hardcore": "Mòde Extrèm!", "gameMode.spectator": "Mòde espectator", "gameMode.survival": "Mòde subrevida", "gamerule.allowFireTicksAwayFromPlayer": "Actualizacion del fuòc luènh dels jogaires", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla se lo fuòc e la lava pòdon s'actualizar a mai de 8 tròces de quin jogaire que siá", "gamerule.announceAdvancements": "<PERSON>gr<PERSON><PERSON>", "gamerule.blockExplosionDropDecay": "Pèrdia de butin dins las explosions de bl<PERSON>", "gamerule.blockExplosionDropDecay.description": "D'unes butins de blocs copats per explosions a causa d'interaccion entre de blòcs son perduts dins l'explosion.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Butins", "gamerule.category.misc": "<PERSON>v<PERSON><PERSON>", "gamerule.category.mobs": "C<PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Aparicion", "gamerule.category.updates": "Actualizacions del mond", "gamerule.commandBlockOutput": "Sortida dels blòcs de comanda", "gamerule.commandModificationBlockLimit": "Limita de blòcs modificables per comanda", "gamerule.commandModificationBlockLimit.description": "Lo nombre de blòcs que pòdon èsser modificats a l'encòp per una comanda, tal que /fill o /clone.", "gamerule.disableElytraMovementCheck": "Verificacion desactivada del vòl en elitras", "gamerule.disablePlayerMovementCheck": "Verificacion desactivada de la mudança del jogaire", "gamerule.disableRaids": "Desactivar invasions", "gamerule.doDaylightCycle": "Avançar l'ora dau jorn", "gamerule.doEntityDrops": "Butin de las entitats", "gamerule.doEntityDrops.description": "Controla los butins dels vagonets (Lo lor inventari enclaus), dels quadres, dels batèus, etc.", "gamerule.doFireTick": "Propagacion del fuòc", "gamerule.doImmediateRespawn": "Reaparéisser immediatament", "gamerule.doInsomnia": "<PERSON><PERSON>", "gamerule.doLimitedCrafting": "Recèpta necessària per fabricar", "gamerule.doLimitedCrafting.description": "Se activada, los jogaires poiràn fabricar solament a comptar de las recèptas desblocadas.", "gamerule.doMobLoot": "Butin de las creaturas", "gamerule.doMobLoot.description": "Controla los butin de las creaturas, òrbes d'experiéncia enclausa.", "gamerule.doMobSpawning": "Generar de creaturas", "gamerule.doMobSpawning.description": "D'unas entitats pòdon aver de règlas diferentas.", "gamerule.doPatrolSpawning": "<PERSON><PERSON> de <PERSON> de p<PERSON>hards", "gamerule.doTileDrops": "<PERSON><PERSON> del<PERSON> bl<PERSON>cs", "gamerule.doTileDrops.description": "Controla los butin dels blòcs, òrbes d'experiéncia enclausa.", "gamerule.doTraderSpawning": "Generar de mercants ambulants", "gamerule.doVinesSpread": "Propagacion de las lianas", "gamerule.doVinesSpread.description": "Controla la propagacion aleatòria de las lianas suls blòcs vesins. Afecta pas los autes tipes de lianas tal que las lianas plorosas, las lianas torsadas, etc.", "gamerule.doWardenSpawning": "<PERSON><PERSON> de wardens", "gamerule.doWeatherCycle": "Cicle meteorologic", "gamerule.drowningDamage": "Degalhs de negada", "gamerule.enderPearlsVanishOnDeath": "Disparicion de las pèrlas d'ender lançadas a la mòrt", "gamerule.enderPearlsVanishOnDeath.description": "Controla se las pèrlas d'ender lançadas per un jogaire desapareisson quand aqueste morís.", "gamerule.entitiesWithPassengersCanUsePortals": "Teleportacion de las entitats amb passatgièrs a travèrs los portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permet a las entitats amb passatgièrs de se telepòrtar a travèrs dels portals de Nether e de l'End, e de palancas de l'End.", "gamerule.fallDamage": "Degalhs de casuda", "gamerule.fireDamage": "Degalhs de fu<PERSON>", "gamerule.forgiveDeadPlayers": "Perdonar los jogaires mòrts", "gamerule.forgiveDeadPlayers.description": "Las creaturas neutras encoleradas quitan de l'èsser quand lo jogaire ciblat morís a proximitat.", "gamerule.freezeDamage": "Degalhs de freg", "gamerule.globalSoundEvents": "Eveniments sonòrs globals", "gamerule.globalSoundEvents.description": "Quand d'uns eveniments se produsisson dins lo jòc, coma l'aparicion d'un boss, lo son es ausit pertot.", "gamerule.keepInventory": "Conservacion del inventari aprèp la mòrt", "gamerule.lavaSourceConversion": "Conversion de la lava en sorsa", "gamerule.lavaSourceConversion.description": "Quand un escorriment enrodat de dos sorsas de lava, se convertís en sorsa.", "gamerule.locatorBar": "Activar la barra de posicion dels jogaires", "gamerule.locatorBar.description": "Quand es activat, una barra s'aficha sus l'ecran per indicar la direccion dels jogaires.", "gamerule.logAdminCommands": "Comandas d'administrador afichadas", "gamerule.maxCommandChainLength": "Limita de las cadenas de comandas", "gamerule.maxCommandChainLength.description": "S'aplica als blòcs de comandas en cadena e a las foncions.", "gamerule.maxCommandForkCount": "Limita de contèxts de las comandas", "gamerule.maxCommandForkCount.description": "Lo nombre maximal de contèxts que pòdon èsser utilizats per comandas tal que \"execute as\".", "gamerule.maxEntityCramming": "Limita d'amolonament de las entitats", "gamerule.minecartMaxSpeed": "Velocitat maximala dels vagonets", "gamerule.minecartMaxSpeed.description": "La velocitat maximala per defaut de vagonets en movement sul sòl.", "gamerule.mobExplosionDropDecay": "Pèrdia de butin dins las explosions de creaturas", "gamerule.mobExplosionDropDecay.description": "D'unes butins de blòcs copats per explosions de creaturas son perduts dins l'explosion.", "gamerule.mobGriefing": "Accions copant de las creaturas", "gamerule.naturalRegeneration": "Regenerar vida", "gamerule.playersNetherPortalCreativeDelay": "Relambi del portal del Nether en mòde creatiu", "gamerule.playersNetherPortalCreativeDelay.description": "Lo temps (en cicles) pendent lo qual un jogaire en mòde Creatiu deu esperar en se téner dins un portal del Nether abans de cambiar de dimension.", "gamerule.playersNetherPortalDefaultDelay": "Relambi del portal del Nether en mòde pas creatiu", "gamerule.playersNetherPortalDefaultDelay.description": "Lo temps (en cicles) pendent lo qual un jogaire en mòde pas Creatiu deu esperar en se téner dins un portal del Nether abans de cambiar de dimension.", "gamerule.playersSleepingPercentage": "Percentatge de jogaires endormits", "gamerule.playersSleepingPercentage.description": "Lo percentatge de jogaires que devon dormir per passar la nuech.", "gamerule.projectilesCanBreakBlocks": "Copada de blòcs pels projectils", "gamerule.projectilesCanBreakBlocks.description": "Controla se los projectils copan los blòcs copables per aqueste al impacte.", "gamerule.randomTickSpeed": "Velocitat dels cicles aleatòri", "gamerule.reducedDebugInfo": "Informacions de debugatge reduchas", "gamerule.reducedDebugInfo.description": "Limita lo contengut del ecran de debugatge.", "gamerule.sendCommandFeedback": "Resultats de las comandas", "gamerule.showDeathMessages": "Messatges de mòrt", "gamerule.snowAccumulationHeight": "Nautor d'acumulacion de la nèu", "gamerule.snowAccumulationHeight.description": "Quand nèva, los estrats de nèu que se forman sul sòl pòdon aténher aqueste nautor.", "gamerule.spawnChunkRadius": "Rai de tròces d'aparicion", "gamerule.spawnChunkRadius.description": "Lo nombre de tròces que demore cargats al entorn del punch d'aparicion de la susfàcia.", "gamerule.spawnRadius": "Rai de la zòna de reaparicion", "gamerule.spawnRadius.description": "Controla la talha de la zòna dins la quina los jogaires pòdon reapareisser.", "gamerule.spectatorsGenerateChunks": "Generacion del terren pels espectators", "gamerule.tntExplodes": "Explosion de TNT", "gamerule.tntExplosionDropDecay": "Pèrdia de butin dins las explosions de TNT", "gamerule.tntExplosionDropDecay.description": "D'unes butins de blocs copats per explosions de TNT son perduts dins l'explosion.", "gamerule.universalAnger": "<PERSON><PERSON><PERSON>", "gamerule.universalAnger.description": "Las creaturas neutras encoleradas atacan tots los jogaires a proximitat, pas solament lo jogaire que l'an encoleradas. Fonciona melhor quand la règla \"forgiveDeadPlayers\" es desactivada.", "gamerule.waterSourceConversion": "Conversion de l'aiga en sorsa", "gamerule.waterSourceConversion.description": "Quand un escorriment enrodat de dos sorsas d'aiga, se convertís en sorsa.", "generator.custom": "Personalizat", "generator.customized": "<PERSON><PERSON><PERSON> (ancian)", "generator.minecraft.amplified": "AMPLIFICAT", "generator.minecraft.amplified.info": "Notatz: Per mai de plaser! Cau aver un ordinator poderós.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON><PERSON>", "generator.minecraft.flat": "Subreplanièr", "generator.minecraft.large_biomes": "Biòmas grands", "generator.minecraft.normal": "<PERSON> defaut", "generator.minecraft.single_biome_surface": "Biòma unic", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON> flotantas", "gui.abuseReport.attestation": "En sometent aquel senhalament, confirmatz que las informacions qu'avètz provesidas son, a vòstre cone<PERSON>, exactas e corrèctas.", "gui.abuseReport.comments": "Comentari", "gui.abuseReport.describe": "Partejar de detalhs nos ajudarà a prene una decision esclairada.", "gui.abuseReport.discard.content": "Se quitatz, perdretz aquel senhalament e los vòstres comentaris.\n<PERSON><PERSON><PERSON> segur de voler quitar ?", "gui.abuseReport.discard.discard": "<PERSON><PERSON><PERSON> e <PERSON>ar", "gui.abuseReport.discard.draft": "Salvagardar un brolhon", "gui.abuseReport.discard.return": "Contunhar de modificar", "gui.abuseReport.discard.title": "Abandonar lo senhalament e los comentaris ?", "gui.abuseReport.draft.content": "Volètz contunhar de modificar lo senhalament existent o l'abandonar e ne crear un novèl ?", "gui.abuseReport.draft.discard": "Abandonar", "gui.abuseReport.draft.edit": "Contunhar de modificar", "gui.abuseReport.draft.quittotitle.content": "<PERSON><PERSON><PERSON> contunhar de lo modificar o l'abandonar ?", "gui.abuseReport.draft.quittotitle.title": "Un brolhon de senhalament serà perdut se quitatz", "gui.abuseReport.draft.title": "Modificar lo brolhon del senhalament ?", "gui.abuseReport.error.title": "Problèma en mandant lo vòstre senhalament", "gui.abuseReport.message": "Ont avètz remarcat un compòrtament pas adequat ?\nAquò nos ajudarà a estudiar lo vòstre cas.", "gui.abuseReport.more_comments": "Se vos plai, descrivètz çò qu'es passat :", "gui.abuseReport.name.comment_box_label": "Se vos plai, descrivètz perqué volètz senhalar aquel nom :", "gui.abuseReport.name.reporting": "Senhalatz \"%s\".", "gui.abuseReport.name.title": "Senhalament del nom del jogaire", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON> se<PERSON> a<PERSON> ?", "gui.abuseReport.read_info": "Ne saber mai", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drògas o alcoòl", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Qualqu'un encoratja d'autras personas a participar a activitats illegalas ligadas a las drògas o encoratja la consomacion d'alcoòl per de minors.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Espleitacion o abús sexual de minors", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Qualqu'un parla de o encoratge un comportament vergonhós implicant de minors.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamacion", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Qualqu'un prejudicia a la vòstra reputacion o la d'una autra persona, per exemple, en difusant de falsas informacions dins la finalitat d'espleitar o de decebre los autres.", "gui.abuseReport.reason.description": "Descripcion :", "gui.abuseReport.reason.false_reporting": "Fals senhalament", "gui.abuseReport.reason.generic": "<PERSON><PERSON><PERSON> se<PERSON> aquel j<PERSON>", "gui.abuseReport.reason.generic.description": "Aquel jogaire me fa aissa o a fach quicòm que m'agrada pas.", "gui.abuseReport.reason.harassment_or_bullying": "Agarriment o intimidacion", "gui.abuseReport.reason.harassment_or_bullying.description": "Qualqu'un vos abaissa, vos ataca o vos agarrís, vos o una autra persona. Aquò inclutz los ensags de contacte repetits e pas consentits o la publicacion pas consentida d'informacions personalas privadas (\"doxing\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON> asirosa", "gui.abuseReport.reason.hate_speech.description": "Qualqu'un vos ataca, vos o una autra persona, en se basant sus de caracteristicas ligadas a l'identitat, com la religion, l'etnia, o la sexualitat.", "gui.abuseReport.reason.imminent_harm": "Menaças de portar prejudici als autres", "gui.abuseReport.reason.imminent_harm.description": "Qualqu'un menaça de vos far de mal o de far de mal a una autra persona dins la vida reala.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imatges intimas pas consentidas", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Qualqu'un parla de, encoratja o parteja d'imatges privadas e intimas.", "gui.abuseReport.reason.self_harm_or_suicide": "Auto-mutilacion o suicidi", "gui.abuseReport.reason.self_harm_or_suicide.description": "Qualqu'un parla de o menaça de se far de mal dins la vida reala.", "gui.abuseReport.reason.sexually_inappropriate": "Sexualament explicit", "gui.abuseReport.reason.sexually_inappropriate.description": "Aquel jogaire pòrta una aparéncia explicida relativa als actes sexuals, als organs sexuals o a la violéncia sexuala.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme o extremisme violent", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Qualq<PERSON>'un parla, encortja o menaça de perpetrar d'actes de terrorisme o d'extremisme violent per de rasons politicas, religiosas, ideologicas o autres.", "gui.abuseReport.reason.title": "Causir una categoria de senhalament", "gui.abuseReport.report_sent_msg": "Avèm plan recebut lo vòstre senhalament. Mercé !\n\nNòstra equipa l'examinarà tanlèu que possible.", "gui.abuseReport.select_reason": "Causir una categoria de senhalament", "gui.abuseReport.send": "Mandar", "gui.abuseReport.send.comment_too_long": "Se vos plai, abracatz lo comentari", "gui.abuseReport.send.error_message": "Mèuca al moment del mandadís del vòstre senhalament :\n\"%s\"", "gui.abuseReport.send.generic_error": "<PERSON><PERSON>uca inesperada al moment del mandadís del vòstre senhalament.", "gui.abuseReport.send.http_error": "Mèuca HTTP inesperada al moment del mandadís del vòstre senhalament.", "gui.abuseReport.send.json_error": "Carga utila pas corrècta encontrada al temps del mandadís del vòstre senhalament.", "gui.abuseReport.send.no_reason": "Se vos plai, causissètz una categoria", "gui.abuseReport.send.not_attested": "Se vos plai, legissètz lo tèxt çai-sus e entalhatz la caseta per mandar lo senhalament", "gui.abuseReport.send.service_unavailable": "Se pòt pas dintrar en contact amb lo servici de senhalaments dels abuses. Se vos plai, acertatz-vos d'èsser conectat(ada) a Internet e tornatz ensajar.", "gui.abuseReport.sending.title": "Mandadís del vòstre senhalament...", "gui.abuseReport.sent.title": "Sen<PERSON><PERSON> mandat", "gui.abuseReport.skin.title": "Senhalament del aparéncia del jogaire", "gui.abuseReport.title": "Senhalament del jogaire", "gui.abuseReport.type.chat": "Messatges del chat", "gui.abuseReport.type.name": "Nom del jogaire", "gui.abuseReport.type.skin": "Aparéncia del jogaire", "gui.acknowledge": "Ai comprés", "gui.advancements": "<PERSON>gr<PERSON><PERSON>", "gui.all": "To<PERSON>", "gui.back": "Retorn", "gui.banned.description": "%s\n\n%s\n\nPer ne saber mai, consultatz lo ligam seguent : %s", "gui.banned.description.permanent": "Lo vòstre compte es fòrabandit definitivament, aquò significa que podètz pas jogar en linha o rejónher de Realms.", "gui.banned.description.reason": "I a pas gaire, avèm recebut un senhalament per compòrtament pas adequat sus vòstre compte. Nòstres moderators an examinat lo vòstre cas e an determinat qu'es un cas de \"%s\", aquò es contra los Estandards de la comunautat de Minecraft.", "gui.banned.description.reason_id": "Còdi : %s", "gui.banned.description.reason_id_message": "Còdi : %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, podètz pas mai jogar en linha o rej<PERSON>her de Realms.", "gui.banned.description.temporary.duration": "Lo vòstre compte es temporàriament fòrabandit e serà reactivat dins %s.", "gui.banned.description.unknownreason": "I a pas gaire, avèm recebut un senhalament per compòrtament pas adequat sus vòstre compte. Nòstres moderators an examinat lo vòstre cas e an determinat qu'es contra los Estandards de la comunautat de Minecraft.", "gui.banned.name.description": "Lo vòstre nom actual, \"%s\", enfranh nòstres Estandards de la comunautat. Podètz jogar sol, mas deurà cambiar de nom per poder jogar en linha.\n\nPer ne saber mai o per demandar-nos de tornar examinar lo vòstre cas, consultatz lo ligam seguent : %s", "gui.banned.name.title": "Nom pas autorizat pel multijogaire", "gui.banned.reason.defamation_impersonation_false_information": "Usurpacion d'identitat o difusion d'informacions per espleitar o decebre los autres", "gui.banned.reason.drugs": "Referéncias a de drògas illegalas", "gui.banned.reason.extreme_violence_or_gore": "Representacion realas de violéncia demasiada o de contengut sagnós", "gui.banned.reason.false_reporting": "Tròp de senhalament fals o pas corrects", "gui.banned.reason.fraud": "Aquisicion o utilizacion fraudulosa de contengut", "gui.banned.reason.generic_violation": "Violacion dels Estandards de la comunautat", "gui.banned.reason.harassment_or_bullying": "Lengatge ofensant utilizat de manièra dirigida e nosibla", "gui.banned.reason.hate_speech": "Paraula asirosa o discriminatòria", "gui.banned.reason.hate_terrorism_notorious_figure": "Referéncias a de grops d'asir, d'organizacion terroristas o de figuras notòrias", "gui.banned.reason.imminent_harm_to_person_or_property": "Intencion de portar prejudici a una persona, o a de bens dins la vida reala", "gui.banned.reason.nudity_or_pornography": "Afichatge de ressorsas obscènas o pornograficas", "gui.banned.reason.sexually_inappropriate": "Subjècte o contenguts a caractèr sexual", "gui.banned.reason.spam_or_advertising": "Spam o publicitat", "gui.banned.skin.description": "La vòstra aparéncia enfranh nòstres Estandards de la comunautat. Podètz encara jogar amb una aparéncia per defaut, o ne causir una autra.\n\nPer ne saber mai o per demandar-nos de tornar examinar lo vòstre cas, consultatz lo ligam seguent : %s", "gui.banned.skin.title": "Aparéncia pas autorizada", "gui.banned.title.permanent": "Compte fòrabandit definitivament", "gui.banned.title.temporary": "Compte fòrabandit temporàriament", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Comentari", "gui.chatReport.describe": "Partejar de detalhs nos ajudarà a prene una decision esclairada.", "gui.chatReport.discard.content": "Se quitatz, perdretz aquel senhalament e los vòstres comentaris.\n<PERSON><PERSON><PERSON> segur de voler quitar ?", "gui.chatReport.discard.discard": "<PERSON><PERSON><PERSON> e <PERSON>ar", "gui.chatReport.discard.draft": "Salvagardar un brolhon", "gui.chatReport.discard.return": "Contunhar de modificar", "gui.chatReport.discard.title": "Abandonar lo senhalament e los comentaris ?", "gui.chatReport.draft.content": "Volètz contunhar de modificar lo senhalament existent o l'abandonar e ne crear un novèl ?", "gui.chatReport.draft.discard": "Abandonar", "gui.chatReport.draft.edit": "Contunhar de modificar", "gui.chatReport.draft.quittotitle.content": "<PERSON><PERSON><PERSON> contunhar de lo modificar o l'abandonar ?", "gui.chatReport.draft.quittotitle.title": "Un brolhon de senhalament serà perdut se quitatz", "gui.chatReport.draft.title": "Modificar lo brolhon del senhalament ?", "gui.chatReport.more_comments": "Se vos plai, descrivètz çò qu'es passat :", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON> se<PERSON> a<PERSON> ?", "gui.chatReport.read_info": "Ne saber mai", "gui.chatReport.report_sent_msg": "Avèm plan recebut lo vòstre senhalament. Mercé !\n\nNòstra equipa l'examinarà tanlèu que possible.", "gui.chatReport.select_chat": "Causir los messatges a senhalar", "gui.chatReport.select_reason": "Causir una categoria de senhalament", "gui.chatReport.selected_chat": "Messatge(s) causit(s) a senhalar : %s", "gui.chatReport.send": "Mandar", "gui.chatReport.send.comments_too_long": "Se vos plai, abracatz lo comentari", "gui.chatReport.send.no_reason": "Se vos plai, causissètz una categoria", "gui.chatReport.send.no_reported_messages": "Se vos plai, causissètz almens un messatge dins lo chat a senhalar", "gui.chatReport.send.too_many_messages": "I a tròp de messatges dins lo senhalament", "gui.chatReport.title": "Senhalament de messatges del jogaire", "gui.chatSelection.context": "Los messatges environants aquela causida seràn enclús coma contèxt suplementari", "gui.chatSelection.fold": "%s messatge(s) amagat(s)", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s a jongut lo tchat", "gui.chatSelection.message.narrate": "%s a dich : %s a %s", "gui.chatSelection.selected": "%s/%s messatge(s) causit(s)", "gui.chatSelection.title": "Causir los messatges a senhalar", "gui.continue": "<PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copiar lo ligam dins lo cachapapièrs", "gui.days": "%s jorn(s)", "gui.done": "Acabat", "gui.down": "<PERSON><PERSON>", "gui.entity_tooltip.type": "Tipe: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s fichièrs an estats fòragetats", "gui.fileDropFailure.title": "Mèuca de l'apondi dels fichièrs", "gui.hours": "%s òra(s)", "gui.loadingMinecraft": "Cargament de Minecraft", "gui.minutes": "%s minuta(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Boton %s", "gui.narrate.editBox": "Camp de sasida %s : %s", "gui.narrate.slider": "Cursor %s", "gui.narrate.tab": "Ongleta %s", "gui.no": "Non", "gui.none": "Cap", "gui.ok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.open_report_dir": "Dobrir lo repertòri dels senhalaments", "gui.proceed": "<PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "Clic drech per mai", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Cercar...", "gui.recipebook.toggleRecipes.all": "Totas las recèptas", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "gui.report_to_server": "<PERSON><PERSON><PERSON> al servidor", "gui.socialInteractions.blocking_hint": "<PERSON><PERSON>r amb un compte de Microsoft", "gui.socialInteractions.empty_blocked": "Pas de jogaires blocats dins lo chat", "gui.socialInteractions.empty_hidden": "Pas de jogaires amagats dins lo chat", "gui.socialInteractions.hidden_in_chat": "Los messatges de %s seràn amagats", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON> dins lo chat", "gui.socialInteractions.narration.hide": "Amagar los messatges de %s", "gui.socialInteractions.narration.report": "Senhalar lo jogaire %s", "gui.socialInteractions.narration.show": "Mostrar los messages de %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "Impossible de trobar de jogaires amb aquest nom", "gui.socialInteractions.search_hint": "Cercar...", "gui.socialInteractions.server_label.multiple": "%s - %s jogaires", "gui.socialInteractions.server_label.single": "%s - %s jogaire", "gui.socialInteractions.show": "Mostrar dins lo chat", "gui.socialInteractions.shown_in_chat": "Los messatges de %s seràn mostrats", "gui.socialInteractions.status_blocked": "Blocat", "gui.socialInteractions.status_blocked_offline": "Blocat - Fòra de linha", "gui.socialInteractions.status_hidden": "Amagat", "gui.socialInteractions.status_hidden_offline": "Amagat - Fòra de l<PERSON>ha", "gui.socialInteractions.status_offline": "<PERSON><PERSON><PERSON> l<PERSON>", "gui.socialInteractions.tab_all": "Totes", "gui.socialInteractions.tab_blocked": "Blocat", "gui.socialInteractions.tab_hidden": "Amagat", "gui.socialInteractions.title": "Interaccions socialas", "gui.socialInteractions.tooltip.hide": "Amagar los messatges", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON> lo jogaire", "gui.socialInteractions.tooltip.report.disabled": "Lo servici de senhalament es pas disponible", "gui.socialInteractions.tooltip.report.no_messages": "I a pas cap de messatge del jogaire %s a senhalar", "gui.socialInteractions.tooltip.report.not_reportable": "Aquel jogaire pòt pas èsser senhalat, perque sos messatges dins lo chat pòdon pas èsser verificats sus aquel servidor", "gui.socialInteractions.tooltip.show": "Mostrar los messages", "gui.stats": "Estatisticas", "gui.toMenu": "Tornar a la lista de servidors", "gui.toRealms": "Tornar a la lista dels Realms", "gui.toTitle": "Tornar au menut principau", "gui.toWorld": "Torna a la lista dels monds", "gui.togglable_slot": "Clicatz per desactivar l'espaci", "gui.up": "Amont", "gui.waitingForResponse.button.inactive": "<PERSON><PERSON> (%ss)", "gui.waitingForResponse.title": "En espèra del Servidor", "gui.yes": "Òc", "hanging_sign.edit": "Messatge de la pancarta penjanta", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Cridar", "instrument.minecraft.dream_goat_horn": "Somia<PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Reflexionar", "instrument.minecraft.seek_goat_horn": "Buscar", "instrument.minecraft.sing_goat_horn": "Cantar", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Destruire l'Objècte", "inventory.hotbarInfo": "Sauvagardar la barra rapida amb %1$s+%2$s", "inventory.hotbarSaved": "Barra rapida sauvagardada (la restaura amb %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON><PERSON> trencar:", "item.canPlace": "Pòt èsser plaçat sus:", "item.canUse.unknown": "Desconegut", "item.color": "Color : %s", "item.components": "%s compausant(s)", "item.disabled": "Objècte desactivat", "item.durability": "Durabilitat : %s / %s", "item.dyed": "Tenchat", "item.minecraft.acacia_boat": "Batèu en cacièr", "item.minecraft.acacia_chest_boat": "Batèu en cacièr amb còfre", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON>u d'aparicion d'allay", "item.minecraft.amethyst_shard": "Fragment d'ametista", "item.minecraft.angler_pottery_shard": "Tèst de terralha de pescaire", "item.minecraft.angler_pottery_sherd": "Tèst de terralha de pescaire", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "<PERSON><PERSON>t de terralha d'a<PERSON>quièr", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON>t de terralha d'a<PERSON>quièr", "item.minecraft.armadillo_scute": "Placa de tató", "item.minecraft.armadillo_spawn_egg": "Uòu d'aparicion de tató", "item.minecraft.armor_stand": "Pòrta-armadura", "item.minecraft.arms_up_pottery_shard": "Tèst de terralha de braçes alçats", "item.minecraft.arms_up_pottery_sherd": "Tèst de terralha de braçes alçats", "item.minecraft.arrow": "Sageta", "item.minecraft.axolotl_bucket": "Axolòtl dins un ferrat", "item.minecraft.axolotl_spawn_egg": "Uòu d'aparcion d'axolòtl", "item.minecraft.baked_potato": "<PERSON><PERSON>", "item.minecraft.bamboo_chest_raft": "Rad<PERSON><PERSON> en bambó amb còfre", "item.minecraft.bamboo_raft": "<PERSON>d<PERSON><PERSON> en bambó", "item.minecraft.bat_spawn_egg": "Uòu d'aparicion de ratapenada", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON><PERSON> d'apar<PERSON><PERSON> d'a<PERSON>", "item.minecraft.beef": "Buòu crus", "item.minecraft.beetroot": "<PERSON><PERSON>arava", "item.minecraft.beetroot_seeds": "Granas de bledarava", "item.minecraft.beetroot_soup": "Sopa de b<PERSON>", "item.minecraft.birch_boat": "Batèu en bèç", "item.minecraft.birch_chest_boat": "Bat<PERSON>u en bèç amb còfre", "item.minecraft.black_bundle": "Sac negre", "item.minecraft.black_dye": "<PERSON><PERSON> negra", "item.minecraft.black_harness": "Arnesc negre", "item.minecraft.blade_pottery_shard": "Tèst de terralha de <PERSON>", "item.minecraft.blade_pottery_sherd": "Tèst de terralha de <PERSON>", "item.minecraft.blaze_powder": "Polsa de blaze", "item.minecraft.blaze_rod": "Baston de blaze", "item.minecraft.blaze_spawn_egg": "Uòu d'aparicion de blaze", "item.minecraft.blue_bundle": "Sac blau", "item.minecraft.blue_dye": "<PERSON><PERSON> blava", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON> blau", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON><PERSON> blau", "item.minecraft.bogged_spawn_egg": "Uòu d'aparicion d'enfagat", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ondradura dels bolons", "item.minecraft.bone": "Òs", "item.minecraft.bone_meal": "Polsa d'òs", "item.minecraft.book": "Libre", "item.minecraft.bordure_indented_banner_pattern": "<PERSON><PERSON>u de bordadura endentada per bandièra", "item.minecraft.bow": "Arc", "item.minecraft.bowl": "Bòl", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "<PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Uòu d'aparicion de ventole<PERSON>", "item.minecraft.brewer_pottery_shard": "Tèst de terralha d'alquimista", "item.minecraft.brewer_pottery_sherd": "Tèst de terralha d'alquimista", "item.minecraft.brewing_stand": "Alambic", "item.minecraft.brick": "Brica", "item.minecraft.brown_bundle": "Sac brun", "item.minecraft.brown_dye": "<PERSON><PERSON> bruna", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON> brun", "item.minecraft.brown_harness": "Arnesc brun", "item.minecraft.brush": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Sac", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "<PERSON><PERSON><PERSON> conténer una pila de mai d'un objècte", "item.minecraft.bundle.full": "Plen", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Tèst de terralha de f<PERSON>c", "item.minecraft.burn_pottery_sherd": "Tèst de terralha de f<PERSON>c", "item.minecraft.camel_spawn_egg": "Uòu d'aparicion de dromadari", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Pastenaga sus un baston", "item.minecraft.cat_spawn_egg": "Uòu d'aparicion de cat", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "<PERSON>òu d'aparicion d'a<PERSON>ha de b<PERSON>", "item.minecraft.chainmail_boots": "Bòtas en còta de malhas", "item.minecraft.chainmail_chestplate": "Plastron en còta de malhas", "item.minecraft.chainmail_helmet": "Casco en còta de malhas", "item.minecraft.chainmail_leggings": "Cambièras en còta de malhas", "item.minecraft.charcoal": "Carbon de fusta", "item.minecraft.cherry_boat": "Batèu en cerièis", "item.minecraft.cherry_chest_boat": "Bat<PERSON>u amb còfre en cerièis", "item.minecraft.chest_minecart": "<PERSON>agon<PERSON> amb còfre", "item.minecraft.chicken": "Polastre crus", "item.minecraft.chicken_spawn_egg": "Uòu d'aparicion de polastre", "item.minecraft.chorus_fruit": "<PERSON><PERSON> de <PERSON>ò<PERSON>", "item.minecraft.clay_ball": "Bola d'arg<PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbon", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "Ondradura dels litorales", "item.minecraft.cocoa_beans": "Cacau", "item.minecraft.cod": "Merluça crusa", "item.minecraft.cod_bucket": "<PERSON><PERSON><PERSON><PERSON> dins un ferrat", "item.minecraft.cod_spawn_egg": "Uòu d'aparicion de merluça", "item.minecraft.command_block_minecart": "Vagonet amb bl<PERSON><PERSON> de comandas", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON>", "item.minecraft.cooked_porkchop": "Costeleta de pò<PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Lingòt de coire", "item.minecraft.cow_spawn_egg": "Uòu d'aparicion de vaca", "item.minecraft.creaking_spawn_egg": "Uòu d'aparicion de cruisseire", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON> de creeper", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON><PERSON> de fàcia de creeper per bandièra", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON> d'aparicion de creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Projectil :", "item.minecraft.crossbow.projectile.multiple": "Projectil : %s × %s", "item.minecraft.crossbow.projectile.single": "Projectil : %s", "item.minecraft.cyan_bundle": "Sac cian", "item.minecraft.cyan_dye": "<PERSON><PERSON> cian", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON><PERSON> cian", "item.minecraft.danger_pottery_shard": "Tèst de terralha de perilh", "item.minecraft.danger_pottery_sherd": "Tèst de terralha de perilh", "item.minecraft.dark_oak_boat": "Bat<PERSON>u en euse escur", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> en euse escur amb còfre", "item.minecraft.debug_stick": "<PERSON>ston de des<PERSON>gatge", "item.minecraft.debug_stick.empty": "%s n'a pas de proprietat", "item.minecraft.debug_stick.select": "\"%s\" seleccionat (%s)", "item.minecraft.debug_stick.update": "\"%s\" a %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Pigassa en diamant", "item.minecraft.diamond_boots": "Bòtas en diamant", "item.minecraft.diamond_chestplate": "Plastron en diamant", "item.minecraft.diamond_helmet": "Casco en diamant", "item.minecraft.diamond_hoe": "Bigòs en diamant", "item.minecraft.diamond_horse_armor": "Armadura en diamant per cavau", "item.minecraft.diamond_leggings": "Cambièras en diamant", "item.minecraft.diamond_pickaxe": "Trenca en diamant", "item.minecraft.diamond_shovel": "Pala en diamant", "item.minecraft.diamond_sword": "Espasa en diamant", "item.minecraft.disc_fragment_5": "Fragment de disc", "item.minecraft.disc_fragment_5.desc": "Disc de musica - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON>u d'apar<PERSON><PERSON> de da<PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON>u d'aparicion d'ase", "item.minecraft.dragon_breath": "<PERSON><PERSON> de dragon", "item.minecraft.dried_kelp": "Augas secadas", "item.minecraft.drowned_spawn_egg": "Uòu d'aparicion de negat", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "Ondradura de las dunas", "item.minecraft.echo_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.egg": "<PERSON><PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "Uòu d'aparicion de grand gardian", "item.minecraft.elytra": "Elitres", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Libre encantat", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON> da<PERSON>da encantada", "item.minecraft.end_crystal": "Cristau de l'End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON>òu d'aparicion de Dragon d'Ender", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'ender", "item.minecraft.enderman_spawn_egg": "<PERSON>òu d'aparicion d'enderòme", "item.minecraft.endermite_spawn_egg": "Uòu d'aparicion d'enderarna", "item.minecraft.evoker_spawn_egg": "Uòu d'aparicion d'evocator", "item.minecraft.experience_bottle": "Fiòla d'experiéncia", "item.minecraft.explorer_pottery_shard": "Tèst de terralha de descobèrta", "item.minecraft.explorer_pottery_sherd": "Tèst de terralha de descobèrta", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "Ondradura del uèlh", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Uèlh d'aranha fermentat", "item.minecraft.field_masoned_banner_pattern": "Motiu de camp maçonat per bandièra", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Pelòta de fuòc", "item.minecraft.firework_rocket": "Fuòc d'Artifici", "item.minecraft.firework_rocket.flight": "Temps de vòl:", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Estela de Fuòc d'Artifici", "item.minecraft.firework_star.black": "Negre", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Personalizat", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Fondut en", "item.minecraft.firework_star.flicker": "Belugas", "item.minecraft.firework_star.gray": "<PERSON><PERSON>", "item.minecraft.firework_star.green": "Verde", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON> clar", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON> clar", "item.minecraft.firework_star.lime": "Lima", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Irange", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "Violet", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.burst": "Espetar", "item.minecraft.firework_star.shape.creeper": "En fòrma de creeper", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON> granda", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON> pichona", "item.minecraft.firework_star.shape.star": "En fòrma d'estela", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON>", "item.minecraft.firework_star.yellow": "Jaune", "item.minecraft.fishing_rod": "Cana de pesca", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "Ondradura dels revolums", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.flow_banner_pattern.desc": "Revolum", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON><PERSON> de revolum per bandièra", "item.minecraft.flow_pottery_sherd": "Tèst de terralha de revolum", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON><PERSON> de flor per bandièra", "item.minecraft.flower_pot": "<PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "Uòu d'aparicion de rainard", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON><PERSON> de terralha d'amic", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON><PERSON> de terralha d'amic", "item.minecraft.frog_spawn_egg": "Uòu d'aparicion de granolha", "item.minecraft.furnace_minecart": "Vagonet amb forn", "item.minecraft.ghast_spawn_egg": "<PERSON>òu d'aparicion de g<PERSON>t", "item.minecraft.ghast_tear": "Lagrema de <PERSON>t", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON> de pastèca <PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.globe_banner_pattern.desc": "Glòbe", "item.minecraft.globe_banner_pattern.new": "<PERSON><PERSON><PERSON> de gl<PERSON> per bandièra", "item.minecraft.glow_berries": "Bagas lusentas", "item.minecraft.glow_ink_sac": "Sac de tencha lusent", "item.minecraft.glow_item_frame": "Quadre lusent", "item.minecraft.glow_squid_spawn_egg": "Uòu d'aparicion de tautena lusenta", "item.minecraft.glowstone_dust": "Polsa de pèira luminosa", "item.minecraft.goat_horn": "<PERSON><PERSON>rn <PERSON>", "item.minecraft.goat_spawn_egg": "Uòu d'aparicion de cabra", "item.minecraft.gold_ingot": "Lingò<PERSON> d'aur", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON>'<PERSON>r", "item.minecraft.golden_apple": "<PERSON><PERSON>", "item.minecraft.golden_axe": "Pigassa en aur", "item.minecraft.golden_boots": "Bòtas en aur", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Plastron en aur", "item.minecraft.golden_helmet": "<PERSON><PERSON> en aur", "item.minecraft.golden_hoe": "Bigòs en aur", "item.minecraft.golden_horse_armor": "Armadura en aur per cavau", "item.minecraft.golden_leggings": "Cambièras en aur", "item.minecraft.golden_pickaxe": "Trenca en aur", "item.minecraft.golden_shovel": "Pala en aur", "item.minecraft.golden_sword": "Espasa en aur", "item.minecraft.gray_bundle": "Sac gris", "item.minecraft.gray_dye": "<PERSON><PERSON> gris", "item.minecraft.gray_harness": "Arnesc gris", "item.minecraft.green_bundle": "Sac verd", "item.minecraft.green_dye": "<PERSON><PERSON> verda", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON> verd", "item.minecraft.guardian_spawn_egg": "<PERSON>òu d'aparicion de gardian", "item.minecraft.gunpowder": "Pouvera", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.guster_banner_pattern.desc": "Ventorià", "item.minecraft.guster_banner_pattern.new": "Motiu de ventorià per bandièra", "item.minecraft.guster_pottery_sherd": "Tèst de terralha de ventòria", "item.minecraft.happy_ghast_spawn_egg": "Uòu d'aparicion de ghast urós", "item.minecraft.harness": "Arnesc", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "Tèst de terralha de <PERSON>", "item.minecraft.heart_pottery_sherd": "Tèst de terralha de <PERSON>", "item.minecraft.heartbreak_pottery_shard": "Tèst de terralha de c<PERSON>r trencat", "item.minecraft.heartbreak_pottery_sherd": "Tèst de terralha de c<PERSON>r trencat", "item.minecraft.hoglin_spawn_egg": "Uòu d'aparicion de hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "Bresca", "item.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> amb embut", "item.minecraft.horse_spawn_egg": "Uòu d'aparicion de cavau", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Ondradura dels òstes", "item.minecraft.howl_pottery_shard": "Tèst de terralha de lop", "item.minecraft.howl_pottery_sherd": "Tèst de terralha de lop", "item.minecraft.husk_spawn_egg": "Uòu d'aparicion de zòmbi momificat", "item.minecraft.ink_sac": "Sac de tencha", "item.minecraft.iron_axe": "Pigassa en fèrre", "item.minecraft.iron_boots": "Bòtas en fèrre", "item.minecraft.iron_chestplate": "Plastron en fèrre", "item.minecraft.iron_golem_spawn_egg": "Uòu d'aparicion de golem de fèrre", "item.minecraft.iron_helmet": "Casco en fèrre", "item.minecraft.iron_hoe": "Bigòs en fèrre", "item.minecraft.iron_horse_armor": "Armadura en fèrre per cavau", "item.minecraft.iron_ingot": "Lingòt de fèrre", "item.minecraft.iron_leggings": "Cambièras en fèrre", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Trenca en fèrre", "item.minecraft.iron_shovel": "Pala en fèrre", "item.minecraft.iron_sword": "Espasa en fèrre", "item.minecraft.item_frame": "Quadre", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON><PERSON> j<PERSON>la amb c<PERSON>fre", "item.minecraft.knowledge_book": "Libre de las coneissénças", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Ferrat de lava", "item.minecraft.lead": "<PERSON>ç", "item.minecraft.leather": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Bòtas en cuèr", "item.minecraft.leather_chestplate": "Tunica en cuèr", "item.minecraft.leather_helmet": "Capèu en cuèr", "item.minecraft.leather_horse_armor": "Armadura en cuèr per cavau", "item.minecraft.leather_leggings": "Pantalons en cuèr", "item.minecraft.light_blue_bundle": "Sac blau clar", "item.minecraft.light_blue_dye": "<PERSON><PERSON> blau clar", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON><PERSON> blau clar", "item.minecraft.light_gray_bundle": "Sac gris clar", "item.minecraft.light_gray_dye": "<PERSON><PERSON> gris clar", "item.minecraft.light_gray_harness": "Arnesc gris clar", "item.minecraft.lime_bundle": "Sac lima", "item.minecraft.lime_dye": "<PERSON><PERSON> lima", "item.minecraft.lime_harness": "Arnesc verd clar", "item.minecraft.lingering_potion": "Pocion persistenta", "item.minecraft.lingering_potion.effect.awkward": "Pocion estranha persistenta", "item.minecraft.lingering_potion.effect.empty": "Pocion de persisténcia non fabricabla", "item.minecraft.lingering_potion.effect.fire_resistance": "Pocion de resisténcia au fuòc persistenta", "item.minecraft.lingering_potion.effect.harming": "Pocion de degalh persistenta", "item.minecraft.lingering_potion.effect.healing": "Pocion de suènh persistenta", "item.minecraft.lingering_potion.effect.infested": "Pocion persistenta d'enfeciment", "item.minecraft.lingering_potion.effect.invisibility": "Pocion d'invisibilitat persistenta", "item.minecraft.lingering_potion.effect.leaping": "Pocion de saut persistenta", "item.minecraft.lingering_potion.effect.levitation": "Pocion de levitacion persistenta", "item.minecraft.lingering_potion.effect.luck": "Pocion d'astre persistenta", "item.minecraft.lingering_potion.effect.mundane": "Pocion banala persistenta", "item.minecraft.lingering_potion.effect.night_vision": "Pocion de vision nocturna persistenta", "item.minecraft.lingering_potion.effect.oozing": "Pocion persistenta de viscositat", "item.minecraft.lingering_potion.effect.poison": "Pocion de poison persistenta", "item.minecraft.lingering_potion.effect.regeneration": "Pocion de regeneracion persistenta", "item.minecraft.lingering_potion.effect.slow_falling": "Pocion de casuda lenta persistenta", "item.minecraft.lingering_potion.effect.slowness": "Pocion de lentor persistenta", "item.minecraft.lingering_potion.effect.strength": "Pocion de fòrça persistenta", "item.minecraft.lingering_potion.effect.swiftness": "Pocion de velocitat persistenta", "item.minecraft.lingering_potion.effect.thick": "Pocion espessa persistenta", "item.minecraft.lingering_potion.effect.turtle_master": "Pocion dau mèstre tartuga persistenta", "item.minecraft.lingering_potion.effect.water": "Fiòla d'aiga persistenta", "item.minecraft.lingering_potion.effect.water_breathing": "Pocion de respiracion aqüatica persistenta", "item.minecraft.lingering_potion.effect.weakness": "Pocion de flaquesa persistenta", "item.minecraft.lingering_potion.effect.weaving": "Pocion persistenta de teisseire", "item.minecraft.lingering_potion.effect.wind_charged": "Pocion persistenta de Buf", "item.minecraft.llama_spawn_egg": "Uòu d'aparicion de lama", "item.minecraft.lodestone_compass": "Bossòla magnetizada", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Sac magenta", "item.minecraft.magenta_dye": "Tencha magenta", "item.minecraft.magenta_harness": "Arnesc magenta", "item.minecraft.magma_cream": "Crèma de magma", "item.minecraft.magma_cube_spawn_egg": "Uòu d'aparicion de cube de magma", "item.minecraft.mangrove_boat": "Batèu en manglièr", "item.minecraft.mangrove_chest_boat": "Bat<PERSON>u en manglièr amb còfre", "item.minecraft.map": "Mapa verge", "item.minecraft.melon_seeds": "Granas de pastèca", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON> de <PERSON>", "item.minecraft.minecart": "Vagonet", "item.minecraft.miner_pottery_shard": "Tèst de terralha de minaire", "item.minecraft.miner_pottery_sherd": "Tèst de terralha de minaire", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.mojang_banner_pattern.desc": "Simb<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON><PERSON> d'emblèma per bandièra", "item.minecraft.mooshroom_spawn_egg": "Uòu d'aparicion de vacamparòu", "item.minecraft.mourner_pottery_shard": "Tèst de terralha de ploraire", "item.minecraft.mourner_pottery_sherd": "Tèst de terralha de ploraire", "item.minecraft.mule_spawn_egg": "Uòu d'aparicion de mula", "item.minecraft.mushroom_stew": "Sopa de camparòu", "item.minecraft.music_disc_11": "Disc de musica", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disc de musica", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disc de Musica", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disc de musica", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disc de musica", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disc de musica", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disc", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - Creator (<PERSON><PERSON> a musica)", "item.minecraft.music_disc_far": "Disc de musica", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disc de musica", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disc de musica", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disc de musica", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disc de musica", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disc de musica", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disc de musica", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disc de musica", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disc de musica", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disc de musica", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disc de musica", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disc de musica", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Moton crus", "item.minecraft.name_tag": "Etiqueta", "item.minecraft.nautilus_shell": "Clòsca de nautile", "item.minecraft.nether_brick": "Brica dau <PERSON>", "item.minecraft.nether_star": "<PERSON><PERSON><PERSON>", "item.minecraft.nether_wart": "Verruga del Nether", "item.minecraft.netherite_axe": "Pigassa en netherita", "item.minecraft.netherite_boots": "Bòtas en netherita", "item.minecraft.netherite_chestplate": "Plastron en netherita", "item.minecraft.netherite_helmet": "Casco en netherita", "item.minecraft.netherite_hoe": "Bigòs en netherita", "item.minecraft.netherite_ingot": "Lingòt de netherita", "item.minecraft.netherite_leggings": "Cambièras en netherita", "item.minecraft.netherite_pickaxe": "Trenca en netherita", "item.minecraft.netherite_scrap": "Fragment de netherita", "item.minecraft.netherite_shovel": "Pala en netherita", "item.minecraft.netherite_sword": "Espasa en netherita", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Melhorament en netherita", "item.minecraft.oak_boat": "Bat<PERSON><PERSON> en euse", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON> en euse amb còfre", "item.minecraft.ocelot_spawn_egg": "Uòu d'aparcion d'ocelòt", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.ominous_trial_key": "Clau de las espròvas funèst", "item.minecraft.orange_bundle": "Sac irange", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "Arnesc irange", "item.minecraft.painting": "Pintura", "item.minecraft.pale_oak_boat": "Batèu en euse palle", "item.minecraft.pale_oak_chest_boat": "Bat<PERSON><PERSON> en euse palle amb còfre", "item.minecraft.panda_spawn_egg": "Uòu d'aparicion de panda", "item.minecraft.paper": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Uòu d'aparcion de papagai", "item.minecraft.phantom_membrane": "Membrana de fan<PERSON>uma", "item.minecraft.phantom_spawn_egg": "Uòu d'aparicion de fantauma", "item.minecraft.pig_spawn_egg": "Uòu d'aparicion de pòrc", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON><PERSON> de morre per bandièra", "item.minecraft.piglin_brute_spawn_egg": "Uòu d'aparicion de piglin ferotge", "item.minecraft.piglin_spawn_egg": "Uòu d'aparicion de piglin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON>u d'apar<PERSON>on de p<PERSON>", "item.minecraft.pink_bundle": "Sac ròse", "item.minecraft.pink_dye": "<PERSON><PERSON> r<PERSON>", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON><PERSON> rò<PERSON>", "item.minecraft.pitcher_plant": "<PERSON>a jarra", "item.minecraft.pitcher_pod": "Gran de planta jarra", "item.minecraft.plenty_pottery_shard": "Tèst de terralha de riquesa", "item.minecraft.plenty_pottery_sherd": "Tèst de terralha de riquesa", "item.minecraft.poisonous_potato": "Patana empoisonada", "item.minecraft.polar_bear_spawn_egg": "Uòu d'aparicion d'ors polar", "item.minecraft.popped_chorus_fruit": "Fruch de còrus esclatada", "item.minecraft.porkchop": "Costeleta de pòrc crusa", "item.minecraft.potato": "<PERSON><PERSON>", "item.minecraft.potion": "Pocion", "item.minecraft.potion.effect.awkward": "Pocion <PERSON>", "item.minecraft.potion.effect.empty": "Pocion non fabricabla", "item.minecraft.potion.effect.fire_resistance": "Pocion de resisténcia au fuòc", "item.minecraft.potion.effect.harming": "Pocion de degalhs", "item.minecraft.potion.effect.healing": "Pocion de suènh", "item.minecraft.potion.effect.infested": "Pocion d'enfeciment", "item.minecraft.potion.effect.invisibility": "Pocion d'invisibilitat", "item.minecraft.potion.effect.leaping": "Pocion de saut", "item.minecraft.potion.effect.levitation": "Pocion de levitacion", "item.minecraft.potion.effect.luck": "Pocion d'astre", "item.minecraft.potion.effect.mundane": "Pocion ban<PERSON>", "item.minecraft.potion.effect.night_vision": "Pocion de vision nocturna", "item.minecraft.potion.effect.oozing": "Pocion de viscositat", "item.minecraft.potion.effect.poison": "Pocion de poison", "item.minecraft.potion.effect.regeneration": "Pocion de regeneracion", "item.minecraft.potion.effect.slow_falling": "Pocion de casuda lenta", "item.minecraft.potion.effect.slowness": "Pocion de lentor", "item.minecraft.potion.effect.strength": "Pocion de fòrça", "item.minecraft.potion.effect.swiftness": "Pocion de velocitat", "item.minecraft.potion.effect.thick": "Pocion Espesa", "item.minecraft.potion.effect.turtle_master": "Pocion dau mèstre tartuga", "item.minecraft.potion.effect.water": "Fiòla d'aiga", "item.minecraft.potion.effect.water_breathing": "Pocion de respiracion aqüatica", "item.minecraft.potion.effect.weakness": "Pocion de flaquesa", "item.minecraft.potion.effect.weaving": "Pocion de teisseire", "item.minecraft.potion.effect.wind_charged": "Pocion de Buf", "item.minecraft.pottery_shard_archer": "<PERSON><PERSON>t de terralha d'a<PERSON>quièr", "item.minecraft.pottery_shard_arms_up": "Tèst de terralha de braçes alçadas", "item.minecraft.pottery_shard_prize": "Tèst de terralha de j<PERSON>", "item.minecraft.pottery_shard_skull": "Tèst de terralha de cran", "item.minecraft.powder_snow_bucket": "<PERSON><PERSON><PERSON> de n<PERSON> p<PERSON>", "item.minecraft.prismarine_crystals": "Cristaus de prismarina", "item.minecraft.prismarine_shard": "Tròç de prismarina", "item.minecraft.prize_pottery_shard": "Tèst de terralha de j<PERSON>", "item.minecraft.prize_pottery_sherd": "Tèst de terralha de j<PERSON>", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "Peis-gl<PERSON>be dins un ferrat", "item.minecraft.pufferfish_spawn_egg": "Uòu d'aparicion de peis glòbe", "item.minecraft.pumpkin_pie": "Tarta de coja", "item.minecraft.pumpkin_seeds": "Granas de coja", "item.minecraft.purple_bundle": "Sac violet", "item.minecraft.purple_dye": "<PERSON><PERSON> violeta", "item.minecraft.purple_harness": "Arnesc violet", "item.minecraft.quartz": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> crus", "item.minecraft.rabbit_foot": "<PERSON><PERSON> de con<PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Uòu d'aparicion de conilh", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ondradura dels elevaires", "item.minecraft.ravager_spawn_egg": "Uòu d'aparicion de devastator", "item.minecraft.raw_copper": "Coire brut", "item.minecraft.raw_gold": "Aur brut", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON> brut", "item.minecraft.recovery_compass": "Bossòla de recuperacion", "item.minecraft.red_bundle": "Sac roge", "item.minecraft.red_dye": "<PERSON><PERSON>", "item.minecraft.red_harness": "Arnesc roge", "item.minecraft.redstone": "Polsa de redstone", "item.minecraft.resin_brick": "Brica de resina", "item.minecraft.resin_clump": "Amàs de resina", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "Ondradura de las còstelas", "item.minecraft.rotten_flesh": "Carn poirida", "item.minecraft.saddle": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon": "Saumon crus", "item.minecraft.salmon_bucket": "<PERSON><PERSON>on dins un ferrat", "item.minecraft.salmon_spawn_egg": "Uòu d'aparcion de saumon", "item.minecraft.scrape_pottery_sherd": "Tèst de terralha de destral", "item.minecraft.scute": "Placa", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ondradura de las sentinèlas", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ondradura dels modelaires", "item.minecraft.sheaf_pottery_shard": "Tèst de terralha de bòta de blat", "item.minecraft.sheaf_pottery_sherd": "Tèst de terralha de bòta de blat", "item.minecraft.shears": "Cisalhas", "item.minecraft.sheep_spawn_egg": "Uòu d'aparicion de moton", "item.minecraft.shelter_pottery_shard": "Tèst de terralha d'abric", "item.minecraft.shelter_pottery_sherd": "Tèst de terralha d'abric", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> negre", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> blau", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> brun", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON>ris", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> blau clar", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON>r gris clar", "item.minecraft.shield.lime": "Blo<PERSON><PERSON><PERSON> lima", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> magenta", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.purple": "Bloquièr violet", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> roge", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON> d'a<PERSON><PERSON><PERSON>", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "Ondradura del silenci", "item.minecraft.silverfish_spawn_egg": "Uòu d'aparicion de peis d'argent", "item.minecraft.skeleton_horse_spawn_egg": "Uòu d'aparicion de cavau esqueleta", "item.minecraft.skeleton_spawn_egg": "Uòu d'aparicion d'esqueleta", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON> de bandièra", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "<PERSON><PERSON><PERSON> de crani per bandièra", "item.minecraft.skull_pottery_shard": "Tèst de terralha de cran", "item.minecraft.skull_pottery_sherd": "Tèst de terralha de cran", "item.minecraft.slime_ball": "Bola de slime", "item.minecraft.slime_spawn_egg": "Uòu d'aparicion de slime", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "S'aplica sus :", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Apondètz un lingòt o un cristau", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Apond<PERSON>tz una armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingòts e cristaus", "item.minecraft.smithing_template.ingredients": "Ingredients :", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Apondètz un lingòt de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipament en diamant", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Apond<PERSON><PERSON> una armadura, una arma o una aisina en diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingòt de netherita", "item.minecraft.smithing_template.upgrade": "Melhorament : ", "item.minecraft.sniffer_spawn_egg": "<PERSON>òu d'aparicion de niflaire", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON> de terra<PERSON><PERSON> de <PERSON>flaire", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> de terra<PERSON><PERSON> de <PERSON>flaire", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Ondradura dels morres", "item.minecraft.snow_golem_spawn_egg": "Uòu d'aparicion de golem de nèu", "item.minecraft.snowball": "<PERSON><PERSON>", "item.minecraft.spectral_arrow": "Sageta espectrala", "item.minecraft.spider_eye": "Uèl<PERSON>'<PERSON>", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON>u d'aparicion d'a<PERSON>ha", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "Ondradura de las torres", "item.minecraft.splash_potion": "Pocion getabla", "item.minecraft.splash_potion.effect.awkward": "Pocion Getabla <PERSON>nha", "item.minecraft.splash_potion.effect.empty": "Pocion getabla non fabricabla", "item.minecraft.splash_potion.effect.fire_resistance": "Pocion de resisténcia al fuòc getabla", "item.minecraft.splash_potion.effect.harming": "Pocion de degalhs getabla", "item.minecraft.splash_potion.effect.healing": "Pocion de suènh getabla", "item.minecraft.splash_potion.effect.infested": "Pocion getabla d'enfeciment", "item.minecraft.splash_potion.effect.invisibility": "Pocion d'invisibilitat getabla", "item.minecraft.splash_potion.effect.leaping": "Pocion de saut getabla", "item.minecraft.splash_potion.effect.levitation": "Pocion de levitacion getabla", "item.minecraft.splash_potion.effect.luck": "Pocion d'astre getabla", "item.minecraft.splash_potion.effect.mundane": "Pocion banala getabla", "item.minecraft.splash_potion.effect.night_vision": "Pocion de vision nocturna getabla", "item.minecraft.splash_potion.effect.oozing": "Pocion getabla de viscositat", "item.minecraft.splash_potion.effect.poison": "Pocion de poison getabla", "item.minecraft.splash_potion.effect.regeneration": "Pocion de regeneracion getabla", "item.minecraft.splash_potion.effect.slow_falling": "Pocion de casuda lenta getabla", "item.minecraft.splash_potion.effect.slowness": "Pocion de lentor getabla", "item.minecraft.splash_potion.effect.strength": "Pocion de fòrça getabla", "item.minecraft.splash_potion.effect.swiftness": "Pocion de velocitat getabla", "item.minecraft.splash_potion.effect.thick": "Pocion Getabla Espessa", "item.minecraft.splash_potion.effect.turtle_master": "Pocion dau mèstre tartuga getabla", "item.minecraft.splash_potion.effect.water": "Fiòla getabla d'aiga", "item.minecraft.splash_potion.effect.water_breathing": "Pocion de respiracion aqüatica getabla", "item.minecraft.splash_potion.effect.weakness": "Pocion de flaquesa getabla", "item.minecraft.splash_potion.effect.weaving": "Pocion getabla de teisseire", "item.minecraft.splash_potion.effect.wind_charged": "Pocion getabla de <PERSON>uf", "item.minecraft.spruce_boat": "Bat<PERSON>u en avet", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON> en avet amb còfre", "item.minecraft.spyglass": "Pòrtavista", "item.minecraft.squid_spawn_egg": "Uòu d'aparicion de tautena", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "Pigassa en pèira", "item.minecraft.stone_hoe": "Bigòs en pèira", "item.minecraft.stone_pickaxe": "Trenca en pèira", "item.minecraft.stone_shovel": "Pala en pèira", "item.minecraft.stone_sword": "Espasa en pèira", "item.minecraft.stray_spawn_egg": "Uòu d'aparicion de barrutlaire", "item.minecraft.strider_spawn_egg": "Uòu d'aparicion d'arpentaire", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "Sopa suspècta", "item.minecraft.sweet_berries": "Bagas doças", "item.minecraft.tadpole_bucket": "Cap<PERSON><PERSON><PERSON> dins un ferrat", "item.minecraft.tadpole_spawn_egg": "Uòu d'aparicion de capmartèu", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "Ondradura de las marèas", "item.minecraft.tipped_arrow": "Sageta d'efièch", "item.minecraft.tipped_arrow.effect.awkward": "Sageta a efièch", "item.minecraft.tipped_arrow.effect.empty": "Sageta especiala non fabricabla", "item.minecraft.tipped_arrow.effect.fire_resistance": "Sageta de resisténcia au fuòc", "item.minecraft.tipped_arrow.effect.harming": "Sage<PERSON> de <PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Sageta de suènh", "item.minecraft.tipped_arrow.effect.infested": "Sageta d'enfeciment", "item.minecraft.tipped_arrow.effect.invisibility": "Sageta d'invisibilitat", "item.minecraft.tipped_arrow.effect.leaping": "Sageta de saut", "item.minecraft.tipped_arrow.effect.levitation": "Sageta de levitacion", "item.minecraft.tipped_arrow.effect.luck": "Sageta d'astre", "item.minecraft.tipped_arrow.effect.mundane": "Sageta a efièch", "item.minecraft.tipped_arrow.effect.night_vision": "Sageta de vision nocturna", "item.minecraft.tipped_arrow.effect.oozing": "Sageta de viscositat", "item.minecraft.tipped_arrow.effect.poison": "Sageta de poison", "item.minecraft.tipped_arrow.effect.regeneration": "Sageta de regeneracion", "item.minecraft.tipped_arrow.effect.slow_falling": "Sageta de casuda lenta", "item.minecraft.tipped_arrow.effect.slowness": "Sageta de lentor", "item.minecraft.tipped_arrow.effect.strength": "Sageta de fòrça", "item.minecraft.tipped_arrow.effect.swiftness": "Sageta de velocitat", "item.minecraft.tipped_arrow.effect.thick": "Sageta a efièch", "item.minecraft.tipped_arrow.effect.turtle_master": "Sageta dau mèstre tartuga", "item.minecraft.tipped_arrow.effect.water": "Sageta d'aiga", "item.minecraft.tipped_arrow.effect.water_breathing": "Sageta de respiracion aqüatica", "item.minecraft.tipped_arrow.effect.weakness": "Sageta de flaquesa", "item.minecraft.tipped_arrow.effect.weaving": "Sageta de Téisser", "item.minecraft.tipped_arrow.effect.wind_charged": "Sageta de Buf", "item.minecraft.tnt_minecart": "Vagonet amb de TNT", "item.minecraft.torchflower_seeds": "Granas d'entòrcha-flor", "item.minecraft.totem_of_undying": "Totèm d'immortalita<PERSON>", "item.minecraft.trader_llama_spawn_egg": "Uòu d'aparicion de lama ambulanta", "item.minecraft.trial_key": "<PERSON>lau <PERSON> las espròvas", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> t<PERSON>", "item.minecraft.tropical_fish_bucket": "<PERSON><PERSON>s tropicau dins un ferrat", "item.minecraft.tropical_fish_spawn_egg": "Uòu d'aparicion de peis tropicau", "item.minecraft.turtle_helmet": "Clòsca de tartuga", "item.minecraft.turtle_scute": "Placa de tartuga", "item.minecraft.turtle_spawn_egg": "Uòu d'aparicion de tartuga", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Ondradura dels vex", "item.minecraft.vex_spawn_egg": "Uòu d'aparicion de vex", "item.minecraft.villager_spawn_egg": "Uòu d'aparicion de vilatgés", "item.minecraft.vindicator_spawn_egg": "Uòu d'aparicion de vindicator", "item.minecraft.wandering_trader_spawn_egg": "Uòu d'aparicion de mercant ambulant", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "Ondradura dels abisses", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON><PERSON> d'aparicion de warden", "item.minecraft.warped_fungus_on_a_stick": "Fonge bescornut sus un baston", "item.minecraft.water_bucket": "Ferrat d'aiga", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ondradura dels correires", "item.minecraft.wheat": "<PERSON><PERSON>", "item.minecraft.wheat_seeds": "Granas de blat", "item.minecraft.white_bundle": "Sac blanc", "item.minecraft.white_dye": "<PERSON><PERSON> blanca", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON><PERSON> blanc", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> salvatja", "item.minecraft.wind_charge": "Carga de vent", "item.minecraft.witch_spawn_egg": "Uòu d'aparicion de masca", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON>u d'aparicion d'esqueleta wither", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON><PERSON> d'aparicion de wither", "item.minecraft.wolf_armor": "Amadura per lop", "item.minecraft.wolf_spawn_egg": "Uòu d'aparicion de lop", "item.minecraft.wooden_axe": "Pigassa en fusta", "item.minecraft.wooden_hoe": "Bigòs en fusta", "item.minecraft.wooden_pickaxe": "Trenca en fusta", "item.minecraft.wooden_shovel": "Pala en fusta", "item.minecraft.wooden_sword": "Espasa en fusta", "item.minecraft.writable_book": "Libre e pluma", "item.minecraft.written_book": "Libre Escrich", "item.minecraft.yellow_bundle": "Sac jauna", "item.minecraft.yellow_dye": "<PERSON><PERSON> jauna", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON><PERSON> jaun", "item.minecraft.zoglin_spawn_egg": "Uòu d'aparicion de <PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "Uòu d'aparicion de cavau zòmbi", "item.minecraft.zombie_spawn_egg": "Uòu d'aparicion de zòmbi", "item.minecraft.zombie_villager_spawn_egg": "Uòu d'aparicion de zòmbi vilatgés", "item.minecraft.zombified_piglin_spawn_egg": "Uòu d'aparicion de piglin zombificat", "item.modifiers.any": "Quand equipat :", "item.modifiers.armor": "Sul còrs :", "item.modifiers.body": "Quand equipat :", "item.modifiers.chest": "Al còs:", "item.modifiers.feet": "Aus pès:", "item.modifiers.hand": "Quand tengut :", "item.modifiers.head": "Al cap:", "item.modifiers.legs": "A las cambas:", "item.modifiers.mainhand": "A la man principala:", "item.modifiers.offhand": "A la man segondària:", "item.modifiers.saddle": "En sèla :", "item.nbt_tags": "NBT : %s etiqueta(s)", "item.op_block_warning.line1": "Atencion :", "item.op_block_warning.line2": "U<PERSON><PERSON>r aquel objècte poiriá menar a l'execucion de comandas", "item.op_block_warning.line3": "Utilizatz lo pas fòra se sabètz exactament çò qu'el conten !", "item.unbreakable": "Intrencable", "itemGroup.buildingBlocks": "Blòcs de construccion", "itemGroup.coloredBlocks": "Blòcs colorats", "itemGroup.combat": "Combat", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Fabricacion", "itemGroup.foodAndDrink": "Noiridura e beures", "itemGroup.functional": "Blòcs foncionaus", "itemGroup.hotbar": "Barras rapidas sauvagardadas", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Inventari dau mòde subrevida", "itemGroup.natural": "Blòcs naturaus", "itemGroup.op": "Utilitaris d'operator", "itemGroup.redstone": "Redstone", "itemGroup.search": "<PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "Uòu d'aparicion", "itemGroup.tools": "Aisinas e utilitaris", "item_modifier.unknown": "Modificaire d'objècte desconegut : %s", "jigsaw_block.final_state": "Se cambia en :", "jigsaw_block.generate": "Generar", "jigsaw_block.joint.aligned": "Alinhat", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "Tipe d'assemblatge :", "jigsaw_block.keep_jigsaws": "Gardar los puzzles", "jigsaw_block.levels": "Nivèu: %s", "jigsaw_block.name": "Nom:", "jigsaw_block.placement_priority": "Prioritat de plaçament :", "jigsaw_block.placement_priority.tooltip": "Òrdre dins lo qual la pèça sus la quala se connecta aquel bloc de puzzle es tractada demest los ligams d'una estructura mai larga.\n\nLo processús se desròtla dins l'òrdre descreissent, en seguent l'òrdre d'insercion se i a egalitat.", "jigsaw_block.pool": "Lista de cèls:", "jigsaw_block.selection_priority": "Prioritat de seleccion:", "jigsaw_block.selection_priority.tooltip": "Òrdre dins lo qual aquel bloc de puzzle ensaja de se connectar a sa pèça cibla quand la pèça parenta es tractada pels connexions.\n\nLo processús se desròtla dins l'òrdre descreissent, o de biais aleatòri se i a egalitat.", "jigsaw_block.target": "Nom de buta:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - Creator (<PERSON><PERSON> a musica)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON>gr<PERSON><PERSON>", "key.attack": "Atacar/Destrusir", "key.back": "<PERSON><PERSON><PERSON>", "key.categories.creative": "<PERSON><PERSON><PERSON>", "key.categories.gameplay": "Jogabilitat", "key.categories.inventory": "Inventari", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "Movement", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "Interfàcia dau juòc", "key.chat": "Dobrir lo chat", "key.command": "Dobrir la comanda", "key.drop": "Escampar l'Objècte Seleccionat", "key.forward": "Caminar <PERSON>", "key.fullscreen": "(Des)activar plen ecran", "key.hotbar.1": "Barra rapida 1", "key.hotbar.2": "Barra rapida 2", "key.hotbar.3": "Barra rapida 3", "key.hotbar.4": "Barra rapida 4", "key.hotbar.5": "Barra rapida 5", "key.hotbar.6": "Barra rapida 6", "key.hotbar.7": "Barra rapida 7", "key.hotbar.8": "Barra rapida 8", "key.hotbar.9": "Barra rapida 9", "key.inventory": "Dobrir/barrar l'inventari", "key.jump": "Sautar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Escafar", "key.keyboard.caps.lock": "Ver. majuscula", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.down": "Flecha de bas", "key.keyboard.end": "Fin", "key.keyboard.enter": "Entrada", "key.keyboard.equal": "=", "key.keyboard.escape": "<PERSON><PERSON><PERSON>", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON><PERSON>", "key.keyboard.insert": "<PERSON><PERSON>", "key.keyboard.keypad.0": "Tòca num. 0", "key.keyboard.keypad.1": "Tòca num. 1", "key.keyboard.keypad.2": "Tòca num. 2", "key.keyboard.keypad.3": "Tòca num. 3", "key.keyboard.keypad.4": "Tòca num. 4", "key.keyboard.keypad.5": "Tòca num. 5", "key.keyboard.keypad.6": "Tòca num. 6", "key.keyboard.keypad.7": "Tòca num. 7", "key.keyboard.keypad.8": "Tòca num. 8", "key.keyboard.keypad.9": "Tòca num. 9", "key.keyboard.keypad.add": "Tòca num. +", "key.keyboard.keypad.decimal": "Calada numeric", "key.keyboard.keypad.divide": "Tòca num. /", "key.keyboard.keypad.enter": "Tòca num. Entrada", "key.keyboard.keypad.equal": "Tòca num. =", "key.keyboard.keypad.multiply": "Tòca num. *", "key.keyboard.keypad.subtract": "Tòca num. -", "key.keyboard.left": "<PERSON><PERSON><PERSON> es<PERSON>u<PERSON>", "key.keyboard.left.alt": "Alt esquèr", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl esquèr", "key.keyboard.left.shift": "<PERSON> d'esqu<PERSON><PERSON>", "key.keyboard.left.win": "Windows d'esquèr", "key.keyboard.menu": "<PERSON>ut", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Ver. num.", "key.keyboard.page.down": "Pag. seguenta", "key.keyboard.page.up": "Pag. precedenta", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "Estampar ecran", "key.keyboard.right": "Flecha de dreita", "key.keyboard.right.alt": "Alt de dreita", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl drecha", "key.keyboard.right.shift": "<PERSON>", "key.keyboard.right.win": "Windows de drecha", "key.keyboard.scroll.lock": "<PERSON><PERSON><PERSON><PERSON> despl.", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON>", "key.keyboard.up": "<PERSON><PERSON><PERSON> de naut", "key.keyboard.world.1": "Monde 1", "key.keyboard.world.2": "Monde 2", "key.left": "Se desplaçar a esquèrra", "key.loadToolbarActivator": "Cargar una barra rapida", "key.mouse": "Boton %1$s", "key.mouse.left": "<PERSON><PERSON>", "key.mouse.middle": "Boton del mitan", "key.mouse.right": "Bo<PERSON>", "key.pickItem": "Causir un Blòc", "key.playerlist": "Lista daus jogaires", "key.quickActions": "Accions Rapidas", "key.right": "Se Desplaçar a Drecha", "key.saveToolbarActivator": "Sauvagardar la barra rapida", "key.screenshot": "Captura d'Ecran", "key.smoothCamera": "Activar/Desactivar lo mòde cimematic per la camèra", "key.sneak": "S'agrovar", "key.socialInteractions": "Interaccion social", "key.spectatorOutlines": "Metre en Evidéncia los Jogaires (Espectators)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Escambiar amb la man segondària", "key.togglePerspective": "Cambiar Perspectiva", "key.use": "Emplegar un Objècte/Pausar un Blòc", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Comunautat", "known_server_link.community_guidelines": "Directivas de la comunitat", "known_server_link.feedback": "Comentari", "known_server_link.forums": "Forums", "known_server_link.news": "<PERSON><PERSON><PERSON>", "known_server_link.report_bug": "Senhalar un bug del servidor", "known_server_link.status": "Estat", "known_server_link.support": "Assistància", "known_server_link.website": "Siti web", "lanServer.otherPlayers": "Opcions pels autres jogaires", "lanServer.port": "Numero del pòrt", "lanServer.port.invalid": "Pas un pòrt valid.\nDaissatz lo camp de sasida vuèg o entratz un nombre entre 1024 e 65535.", "lanServer.port.invalid.new": "Pas un pòrt valid.\nDaissatz lo camp de sasida vuèg o entratz un nombre entre %s e %s.", "lanServer.port.unavailable": "Pas un pòrt disponible.\nDaissatz lo camp de sasida vuèg o entratz un autre nombre entre 1024 e 65535.", "lanServer.port.unavailable.new": "Pas un pòrt disponible.\nDaissatz lo camp de sasida vuèg o entratz un autre nombre entre %s e %s.", "lanServer.scanning": "Recèrca de partidas sus vòstra ret locala", "lanServer.start": "Començar un mond LAN", "lanServer.title": "Mond LAN", "language.code": "oci_FR", "language.name": "Occitan", "language.region": "Occitània", "lectern.take_book": "Prene lo libre", "loading.progress": "%s %%", "mco.account.privacy.info": "Legir mai sus Mojang e las leis sus la confidencialitat", "mco.account.privacy.info.button": "Legir mai sus lo RGPD", "mco.account.privacy.information": "Mojang implementa cèrtas proceduras per ajudar de protegir los menors e lor vida privada confòrmament al Children’s Online Privacy Protection Act (COPPA) e a la General Data Protection Regulation (GDPR).\n\nSe pòtz que vos cal obténer lo consentiment parental abans d'accedir a vòstre compte Realms.", "mco.account.privacyinfo": "Mojang implementa cèrtas proceduras per ajudar de protegir los menors e lor vida privada confòrmament al Children’s Online Privacy Protection Act (COPPA) e a la General Data Protection Regulation (GDPR).\n\nSe pòtz que vos cal obténer lo consentiment parental abans d'accedir a vòstre compte Realms.\n\nS'avètz un compte Minecraft vièlh (vos connectatz amb vòstre nom d'utilizaire), vos cal migrar a un compte Mojang per accedir a Realms.", "mco.account.update": "<PERSON><PERSON><PERSON> lo compte", "mco.activity.noactivity": "Cap d'activitat dempuèi %s jorn(s)", "mco.activity.title": "Activitat dels jogaires", "mco.backup.button.download": "Telecargar lo da<PERSON>r", "mco.backup.button.reset": "Reïnicializar", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "Televersar lo mond", "mco.backup.changes.tooltip": "Cambiaments", "mco.backup.entry": "Salvagarda (%s)", "mco.backup.entry.description": "Descripcion", "mco.backup.entry.enabledPack": "Paquet(s) activat(s)", "mco.backup.entry.gameDifficulty": "Dificultat del jòc", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Version del servidor de jòc", "mco.backup.entry.name": "Nom", "mco.backup.entry.seed": "Grana", "mco.backup.entry.templateName": "Nom del modèl", "mco.backup.entry.undefined": "Cambiament indefinit", "mco.backup.entry.uploaded": "Televersat", "mco.backup.entry.worldType": "Tipe de monde", "mco.backup.generate.world": "Generar un monde", "mco.backup.info.title": "Cambiaments dempuèi lo darrièr rescat", "mco.backup.narration": "Salvagarda del %s", "mco.backup.nobackups": "Aqueste realm a pas cap de sauvagarda.", "mco.backup.restoring": "Restabliment de vòstre realm", "mco.backup.unknown": "DESCONEGUT", "mco.brokenworld.download": "Telecargar", "mco.brokenworld.downloaded": "Telecargat", "mco.brokenworld.message.line1": "Se vos plai, reïnicializar o causissètz un autre monde.", "mco.brokenworld.message.line2": "<PERSON>d<PERSON>tz tanben causir de telecargar un monde un jogaire.", "mco.brokenworld.minigame.title": "Aqueste minijuòc es pas mai suportat", "mco.brokenworld.nonowner.error": "Se vos plai, esperatz que lo proprietari torne inicializar lo mond", "mco.brokenworld.nonowner.title": "Lo monde es pas a jorn", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Reïnicializar", "mco.brokenworld.title": "Vòstre monde actuau es pas mai suportat", "mco.client.incompatible.msg.line1": "Vòstre client es pas compatible amb Realms.", "mco.client.incompatible.msg.line2": "Se vos plai, utilizatz la darrèra version de Minecraft.", "mco.client.incompatible.msg.line3": "Realms es pas compatible amb las versions de desvolopament.", "mco.client.incompatible.title": "Client Incompatible!", "mco.client.outdated.stable.version": "La version de vòstre client (%s) es pas compatibla amb Realms.\nMercé d'utilizar la version mai recente de Minecraft.", "mco.client.unsupported.snapshot.version": "La version de vòstre client (%s) es pas compatibla amb Realms.\n\nRealms es pas disponible per aquesta version de Snapshot.", "mco.compatibility.downgrade": "Retrogradation", "mco.compatibility.downgrade.description": "Aqueste mond foguèt jogat per darrièra còp amb la version %s; sètz en version %s. Retrogradar un mond pòt causat de corrupcion - nos pòdem pas garantir qu'el se cargarà o funcionarà.\nUna còpia de seguretat de vòstre mond serà enregistrada dins \"Còpias de seguretat del mond\". Mercé de restaurar vòstre mond se es necessari.", "mco.compatibility.incompatible.popup.title": "Version incompatibla", "mco.compatibility.incompatible.releaseType.popup.message": "Lo mond al qual volètz participar es incompatibla amb la version que sètz.", "mco.compatibility.incompatible.series.popup.message": "Aqueste mond foguèt jogat l'ultima vegada amb la version %s; vos sètz sus la version %s.\n\nAquestas sèrias son pas compatiblas entre elas. Cal un nau mond per jogar amb aquesta version.", "mco.compatibility.unverifiable.message": "La version amb la quala aqueste mond foguèt jogat l'ultima vegada pòt pas èsser verificada. Se lo mond es actualizat o retrogradat, una còpia de seguretat serà creada automaticament e guardada dins « Còpias de seguretat dels monds ».", "mco.compatibility.unverifiable.title": "La compatibilitat pòt pas èsser verificada", "mco.compatibility.upgrade": "Melhorament", "mco.compatibility.upgrade.description": "Aqueste mond foguèt jogat per darrièra còp amb la version %s, sètz en version %s.\n\nUna còpia de seguretat de vòstre mond serà enregistrada dins \"Còpias de seguretat del mond\".\n\nMercé de restaurar vòstre mond se es necessari.", "mco.compatibility.upgrade.friend.description": "Aqueste mond foguèt jogat per darrièra còp amb la version %s, sètz en version %s.\n\nUna còpia de seguretat de vòstre mond serà enregistrada dins \"Còpias de seguretat del mond\".\n\nLo proprietari del Realm pòt restaurar lo mond se es necessari.", "mco.compatibility.upgrade.title": "Volètz realament actualizar aqueste mond ?", "mco.configure.current.minigame": "Actuau", "mco.configure.world.activityfeed.disabled": "L'activitat daus jogaires es temporàriament desactivada", "mco.configure.world.backup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.activity": "Activitat dau jogaire", "mco.configure.world.buttons.close": "Barrar lo realm", "mco.configure.world.buttons.delete": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "Acabat", "mco.configure.world.buttons.edit": "Paramètres", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "Mai d'opcions", "mco.configure.world.buttons.newworld": "<PERSON><PERSON>", "mco.configure.world.buttons.open": "Dobrir lo Realm", "mco.configure.world.buttons.options": "Opcions del monde", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Seleccionar la region...", "mco.configure.world.buttons.resetworld": "Reinicializar lo monde", "mco.configure.world.buttons.save": "Enregistrar", "mco.configure.world.buttons.settings": "Paramètres", "mco.configure.world.buttons.subscription": "Abonament", "mco.configure.world.buttons.switchminigame": "Cambiar de minijuòc", "mco.configure.world.close.question.line1": "Pòdes barrar temporàriament ton Realm per empachar los jogaires d'i jogar del temps que fas d'ajustaments. Torna-o obrir quand ès prèst.\n\nAquò anulla pas ton abonament a Realms.", "mco.configure.world.close.question.line2": "<PERSON><PERSON><PERSON> segur de voler far aquò ?", "mco.configure.world.close.question.title": "Cal modificar sens interrompre ?", "mco.configure.world.closing": "Barradura del realm...", "mco.configure.world.commandBlocks": "Blòcs de comandas", "mco.configure.world.delete.button": "<PERSON><PERSON><PERSON><PERSON> lo <PERSON>", "mco.configure.world.delete.question.line1": "Vòstre realm serà suprimit per totjorn", "mco.configure.world.delete.question.line2": "<PERSON><PERSON>tz segur que volètz contunhar ?", "mco.configure.world.description": "Descripcion del realm", "mco.configure.world.edit.slot.name": "Nom del mond", "mco.configure.world.edit.subscreen.adventuremap": "Quauques paramètres son desactivats perque vòstre monde actuau es en mòde aventura", "mco.configure.world.edit.subscreen.experience": "Quauques paramètres son desactivats perque vòstre monde actuau es una experiéncia", "mco.configure.world.edit.subscreen.inspiration": "Quauques paramètres son desactivats perque vòstre monde actuau es una inspiracion", "mco.configure.world.forceGameMode": "Forçar lo cambi de mòde de juòc", "mco.configure.world.invite.narration": "Avètz %s invitacion(s) nòva(s)", "mco.configure.world.invite.profile.name": "Nom", "mco.configure.world.invited": "Convidat", "mco.configure.world.invited.number": "Convidat (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Operator", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Se quitatz aqueste realm, i poiretz pas mai accedir tant que seretz pas convidat tornamai", "mco.configure.world.leave.question.line2": "<PERSON><PERSON>tz segur que volètz contunhar ?", "mco.configure.world.loading": "Cargament del Realm", "mco.configure.world.location": "Emplaçament", "mco.configure.world.minigame": "Actual : %s", "mco.configure.world.name": "Nom del realm", "mco.configure.world.opening": "Dobertura del realm...", "mco.configure.world.players.error": "Un jogaire amb lo nom provesís existís pas", "mco.configure.world.players.inviting": "Convidant jogador...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Preferéncia de region", "mco.configure.world.region_preference.title": "Seleccion de region", "mco.configure.world.reset.question.line1": "Vòstre monde serà regenerat e vòstre monde actuau serà perdut", "mco.configure.world.reset.question.line2": "<PERSON><PERSON>tz segur que volètz contunhar ?", "mco.configure.world.resourcepack.question": "A<PERSON><PERSON><PERSON> besonh d'un paquet de ressorsas personalizat per jogar sus aqueste Reialme.\n\nVòlez telecargar-lo e jogar ?", "mco.configure.world.resourcepack.question.line1": "Vos cau un paquet de ressorsa personalizat per jogar sus aquest realm", "mco.configure.world.resourcepack.question.line2": "Lo volètz telegar e jogar?", "mco.configure.world.restore.download.question.line1": "Lo monde serà telecargat e apondut a vòstres mondes un jogaire.", "mco.configure.world.restore.download.question.line2": "<PERSON><PERSON><PERSON>?", "mco.configure.world.restore.question.line1": "Vòstre monde serà restaurat a la data dau '%s' (%s)", "mco.configure.world.restore.question.line2": "<PERSON><PERSON>tz segur que volètz contunhar ?", "mco.configure.world.settings.expired": "Podètz pas modificar los paramètres d'un Realm expirat", "mco.configure.world.settings.title": "Paramètres", "mco.configure.world.slot": "Monde %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Vòstre realm es per cambiar per un autre monde", "mco.configure.world.slot.switch.question.line2": "<PERSON><PERSON>tz segur que volètz contunhar ?", "mco.configure.world.slot.tooltip": "Cambiar de monde", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "<PERSON>r dins los minijuòcs", "mco.configure.world.spawnAnimals": "Generar los animaus", "mco.configure.world.spawnMonsters": "Generar los mostres", "mco.configure.world.spawnNPCs": "Generar los PNJ", "mco.configure.world.spawnProtection": "Proteccion de la zòna iniciala", "mco.configure.world.spawn_toggle.message": "Desactivar aquesta opció supprimirà totes las entitats existentas d'aquel tipe.", "mco.configure.world.spawn_toggle.message.npc": "Desactivar aquesta opcion suprimirà totes las entitats existentas d'aquel tipe, còma les vilatgers.", "mco.configure.world.spawn_toggle.title": "Atencion !", "mco.configure.world.status": "Estat", "mco.configure.world.subscription.day": "jorn", "mco.configure.world.subscription.days": "jorns", "mco.configure.world.subscription.expired": "Expirat", "mco.configure.world.subscription.extend": "Perlongar l'Abonament", "mco.configure.world.subscription.less_than_a_day": "Mens d'un jorn", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "meses", "mco.configure.world.subscription.recurring.daysleft": "Renovelat automaticament dins", "mco.configure.world.subscription.recurring.info": "Los cambiaments fachs al vòstre abonament a Realms, coma la prolongacion o la desactivacion de la facturacion periodica, seràn pas tenguts en compte abans la data de facturacion que ven.", "mco.configure.world.subscription.remaining.days": "%1$s jorn(s)", "mco.configure.world.subscription.remaining.months": "%1$s mesada(s)", "mco.configure.world.subscription.remaining.months.days": "%1$s mesada(s) e %2$s jorn(s)", "mco.configure.world.subscription.start": "Data de Debuta", "mco.configure.world.subscription.tab": "Abonament", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON> demorant", "mco.configure.world.subscription.title": "Informacions sus l'abonament", "mco.configure.world.subscription.unknown": "Desconegut", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> un Mond", "mco.configure.world.switch.slot.subtitle": "Aquèste mond es vuèg, causissètz cossí crear vòstre monde", "mco.configure.world.title": "Configurar realm:", "mco.configure.world.uninvite.player": "<PERSON><PERSON><PERSON> segur que volètz anullar la convidacion de \"%s\" ?", "mco.configure.world.uninvite.question": "<PERSON><PERSON><PERSON> segur que volètz anullar la convidacion", "mco.configure.worlds.title": "<PERSON><PERSON>", "mco.connect.authorizing": "Autentificacion...", "mco.connect.connecting": "Connexion au realm...", "mco.connect.failed": "La connexion al realm a pas capitat", "mco.connect.region": "Region del servidor : %s", "mco.connect.success": "Acabat", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "Vos cau dintrar un nom!", "mco.create.world.failed": "Mèuca de la creacion del mond !", "mco.create.world.reset.title": "Creacion dau monde...", "mco.create.world.skip": "Passar", "mco.create.world.subtitle": "<PERSON>'o volètz, causissètz quin monde voldriatz metre sus vòstre realm novèu", "mco.create.world.wait": "Creacion del realm...", "mco.download.cancelled": "Telecarga<PERSON>", "mco.download.confirmation.line1": "Lo monde que sètz per telecargar is mai grand que %s", "mco.download.confirmation.line2": "Poiretz pas mai metre lo vòstre monde en linha suls vòstres realms", "mco.download.confirmation.oversized": "Lo mond que volètz telecargar a una talha mai granda que %s\n\nPoiretz pas mai mandar aquel mond sul vòstre Realm", "mco.download.done": "Telecargament Acabat", "mco.download.downloading": "Telecargament", "mco.download.extracting": "T<PERSON><PERSON>", "mco.download.failed": "Telecargament Pas Capitat", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparacion dau telecargament", "mco.download.resourcePack.fail": "Mèuca del telecargament del paquet de ressorsa !", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Telecargament del darrèr mond", "mco.error.invalid.session.message": "Se vos plai, ensajatz de tornar amodar Minecraft", "mco.error.invalid.session.title": "Session pas valida", "mco.errorMessage.6001": "<PERSON><PERSON>", "mco.errorMessage.6002": "ToS pas acceptat", "mco.errorMessage.6003": "Limit de Telecargament Atengut", "mco.errorMessage.6004": "Limit de televersament atengut", "mco.errorMessage.6005": "Mond blocat", "mco.errorMessage.6006": "Lo mond es pas a jorn", "mco.errorMessage.6007": "Utilizaire dins tròp de Realms", "mco.errorMessage.6008": "Nom del Realm pas vaild", "mco.errorMessage.6009": "Descripcion del Realm pas valid", "mco.errorMessage.connectionFailure": "Una error s'es produsit, se vos plai ensajatz mai tard.", "mco.errorMessage.generic": "Se a produch un error : ", "mco.errorMessage.initialize.failed": "Impossible d’inicializar lo <PERSON>", "mco.errorMessage.noDetails": "Detalhs de l’error pas provesits", "mco.errorMessage.realmsService": "Se a produch un error (%s) :", "mco.errorMessage.realmsService.configurationError": "Edicion d’un emplaçament de Realm pas actiu e imprevist", "mco.errorMessage.realmsService.connectivity": "Se pòt pas connectar al Realm : %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s) :", "mco.errorMessage.realmsService.unknownCompatibility": "Se pòt pas verificar la version compatible, responsa obtenguda : %s", "mco.errorMessage.retry": "Tornar ensajar l'operacion", "mco.errorMessage.serviceBusy": "Realms es ocupat d'aquesta passa.\nEnsaja de te reconnecter a ton Realm dins qualques minutas.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.info": "Informacions !", "mco.invited.player.narration": "Convit enviat a %s", "mco.invites.button.accept": "Acceptar", "mco.invites.button.reject": "Refusar", "mco.invites.nopending": "Cap d'invitacion pendent!", "mco.invites.pending": "Convidacion(s) nòva(s) !", "mco.invites.title": "Convidacion en Espèra", "mco.minigame.world.changeButton": "Causir un autre minijuòc", "mco.minigame.world.info.line1": "Aquò va remplaçar temporàriament vòstre monde amb un minijuòc!", "mco.minigame.world.info.line2": "Poiretz tornar a vòstre monde original sens pèrdre res.", "mco.minigame.world.noSelection": "Se vos plai, fasètz una seleccion", "mco.minigame.world.restore": "Fin del minijuòc...", "mco.minigame.world.restore.question.line1": "Lo minijuòc es per s'acabar e vòstre realm es per èsser restaurat.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON><PERSON> segur de voler far aquò?", "mco.minigame.world.selected": "Causir lo minijuòc:", "mco.minigame.world.slot.screen.title": "Cambiament de monde...", "mco.minigame.world.startButton": "Cambiar", "mco.minigame.world.starting.screen.title": "Lançament del minijòc...", "mco.minigame.world.stopButton": "Acabar lo minijuòc", "mco.minigame.world.switch.new": "Causir un autre minijuòc?", "mco.minigame.world.switch.title": "Cambiar de minijuòc", "mco.minigame.world.title": "Cambiar lo realm en un minijuòc", "mco.news": "Nòvas de Realms", "mco.notification.dismiss": "Regetar", "mco.notification.transferSubscription.buttonText": "Transferir", "mco.notification.transferSubscription.message": "Los subrescrits a Java Realms son transferidas cap al Microsoft Store. Daisses pas ton subrescrit expirar !\nTransferís-la ara e profiècha de 30 jorns gratuits sus Realms.\nTorna-te sus ton Perfil sus minecraft.net per transferir ton subrescrit.", "mco.notification.visitUrl.buttonText.default": "Dobrir lo ligam", "mco.notification.visitUrl.message.default": "Se vos plai, clicatz lo ligam çai-jos", "mco.onlinePlayers": "Jo<PERSON>res en linha", "mco.play.button.realm.closed": "Lo Realm es estat barrat", "mco.question": "Question", "mco.reset.world.adventure": "Aventuras", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "<PERSON><PERSON>", "mco.reset.world.inspiration": "Inspiracion", "mco.reset.world.resetting.screen.title": "Reïnicializacion dau monde...", "mco.reset.world.seed": "Grana (opcional)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON> mondes", "mco.reset.world.title": "Reïnicializar lo mond", "mco.reset.world.upload": "Televersar lo mond", "mco.reset.world.warning": "Vòstre reialme serà suprimit definitivament !", "mco.selectServer.buy": "Crompar un realm", "mco.selectServer.close": "<PERSON><PERSON>", "mco.selectServer.closed": "Realm barrat", "mco.selectServer.closeserver": "Barrar lo realm", "mco.selectServer.configure": "Configurar realm", "mco.selectServer.configureRealm": "Configurar lo <PERSON>", "mco.selectServer.create": "Crear un realm", "mco.selectServer.create.subtitle": "Selecciona qual mond metre dins ton novèl Realm.", "mco.selectServer.expired": "Realm expirat", "mco.selectServer.expiredList": "Vòstra abonament a expirat", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "<PERSON>òstre periòde d'assag a acabat", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON> dins un jorn", "mco.selectServer.expires.days": "Expira dins %s jorns", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>", "mco.selectServer.leave": "<PERSON><PERSON><PERSON> lo <PERSON>", "mco.selectServer.loading": "Cargament de la lista dels Realms", "mco.selectServer.mapOnlySupportedForVersion": "Aquesta mapa es pas suportada en %s", "mco.selectServer.minigame": "Minijuòc:", "mco.selectServer.minigameName": "Minijuòc : %s", "mco.selectServer.minigameNotSupportedInVersion": "Impossible de jogar a aqueste minijuòc en %s", "mco.selectServer.noRealms": "As pas l'aire d'aver de Realm. Ajusta un Realm per jogar amb tos amics.", "mco.selectServer.note": "Nòta :", "mco.selectServer.open": "<PERSON> dobèrt", "mco.selectServer.openserver": "Dobrir lo Realm", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Realms es un biais simple et segur de jogar a un monde Minecraft amb fins a dètz amics. Se pòt jogar a un molon de minijuòcs, e a un molon de mondes personalizats. Sonque au proprietari lo cau pagar.", "mco.selectServer.purchase": "Apondre un realm", "mco.selectServer.trial": "Assajatz!", "mco.selectServer.uninitialized": "Clicatz per crear un realm!", "mco.snapshot.createSnapshotPopup.text": "Ès a mand de crear un Realm del Snapshot gratuit que serà associat a ton subrescrit Realms pagant. Aquel novèl Realm del Snapshot serà accessible tant que lo subrescrit pagant es actiu. Ton Realm pagant serà pas afectat.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON><PERSON> crear un Realm del Snapshot ?", "mco.snapshot.creating": "Creacion del Realm del Snapshot...", "mco.snapshot.description": "Associat a \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Deves èsser sus la version %s per rej<PERSON>her aiceste Realm", "mco.snapshot.friendsRealm.upgrade": "%s deu actualizar son Realm abans que pòscas jogar a partir d'aquela version", "mco.snapshot.paired": "Aiceste Realm del Snapshot es associat a \"%s\"", "mco.snapshot.parent.tooltip": "Utiliza la darrièra version de Minecraft per jogar sus aiceste Realm", "mco.snapshot.start": "Comença un Realm del Snapshot gratuit", "mco.snapshot.subscription.info": "Aiçò es un Realm del Snapshot associat a lo subrescrit de ton Realm '%s'. Demorarà actiu tant que son Realm associat o serà.", "mco.snapshot.tooltip": "Utiliza los Realms del Snapshot per aver un apercebut de las versions venentas de Minecraft, que poirián inclure de novèlas foncionalitats e d'autres cambiaments.\n\nPòdes trobar tos Realms normals dins la version oficiala del jòc.", "mco.snapshotRealmsPopup.message": "Los Realms son d'ara enlà disponibles dins los Snapshots a partir del Snapshot 23w41a. Cada subrescrit Realms compren un Realms del Snapshot gratuit, distint de tos Realms Java abituals !", "mco.snapshotRealmsPopup.title": "Los Realms son d'ara enlà disponibles dins las versions de desvolopament", "mco.snapshotRealmsPopup.urlText": "Ne saber mai", "mco.template.button.publisher": "Creator", "mco.template.button.select": "<PERSON><PERSON><PERSON>", "mco.template.button.trailer": "Banda-Annóncia", "mco.template.default.name": "<PERSON><PERSON><PERSON><PERSON> dau monde", "mco.template.info.tooltip": "Siti de l'editor", "mco.template.name": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.select.failure": "Avèm pas pogut recuperar la lista del contengut per aquèsta categoria.\nSe vos plai, verificatz vòstra connexion internet, o tornar ensajar mai tard.", "mco.template.select.narrate.authors": "Autors: %s", "mco.template.select.narrate.version": "version %s", "mco.template.select.none": "Ops, sembla que lo contengut d'aquèsta categoria es actualament vuèg.\nSe vos plai, verificatz mai tard per de contengut novèl, o se sètz un creator, %s.", "mco.template.select.none.linkTitle": "pensatz de sometre quicòm vosautres-meteisses", "mco.template.title": "<PERSON><PERSON><PERSON><PERSON>d", "mco.template.title.minigame": "Minijuòcs", "mco.template.trailer.tooltip": "Apercebut de la mapa", "mco.terms.buttons.agree": "Acceptar", "mco.terms.buttons.disagree": "Acceptar pas", "mco.terms.sentence.1": "Accèpti", "mco.terms.sentence.2": "Condicions d'utilizacion", "mco.terms.title": "Condicions d'utilizacion de Minecraft Realms", "mco.time.daysAgo": "i a %1$s jorn(s)", "mco.time.hoursAgo": "i a %1$s òra(s)", "mco.time.minutesAgo": "i a %1$s minuta(s)", "mco.time.now": "ara", "mco.time.secondsAgo": "i a %1$s segonda(s)", "mco.trial.message.line1": "Volètz aver vòstre pròpri realm?", "mco.trial.message.line2": "Picatz aicí per mai d'informacions!", "mco.upload.button.name": "Televersar", "mco.upload.cancelled": "Televersament anullat", "mco.upload.close.failure": "S'es pas pogut barrar vòstre realm, ensaja<PERSON> mai tard, se vos plai", "mco.upload.done": "Televersament acabat", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Televersament pas capitat! (%s)", "mco.upload.failed.too_big.description": "Lo mond causit es tròp voluminós. La talha maximala autorisada es %s.", "mco.upload.failed.too_big.title": "Mond tròp grand", "mco.upload.hardcore": "Los mondes extrèmes pòdon pas èsser meses en linha!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparacion de las donadas de vòstre mond", "mco.upload.select.world.none": "Cap de monde un jogaire trobat!", "mco.upload.select.world.subtitle": "Se vos plai, causissètz un monde un jogaire de televersar", "mco.upload.select.world.title": "Televersar lo mond", "mco.upload.size.failure.line1": "'%s' es tròp grand!", "mco.upload.size.failure.line2": "Fa %s. La talha maximala autorizada es %s.", "mco.upload.uploading": "Televersament de \"%s\"", "mco.upload.verifying": "Verificacion de vòstra mapa", "mco.version": "Version : %s", "mco.warning": "Atencion !", "mco.worldSlot.minigame": "Minijuò<PERSON>", "menu.custom_options": "Opcions personalisadas...", "menu.custom_options.title": "Opcions personalisadas", "menu.custom_options.tooltip": "Nòta : Las opcions personalizadas son provesidas per servidors et/o contengut tèrç.\nFatz atencion !", "menu.custom_screen_info.button_narration": "Aquò es un ecran personalizat. Ne saber mai.", "menu.custom_screen_info.contents": "Lo contengut d'aquel ecran es controlat per de servidors e de cartas tèrças que son pas detenguts, espleitats o supervisats per Mojang Studios o Microsoft.\n\nAtencion ! Fasètz mòstra de prudéncia quand seguissètz de ligams et partejatz pas las vòstras informacions personalas, informacions de connexion compresas.\n\nSe aquel ecran vos empacha de jogar, podètz vos desconnectar del servidor actual en clicant lo boton çai-jos.", "menu.custom_screen_info.disconnect": "Ecran personalizat fòragetat", "menu.custom_screen_info.title": "Nòta a prepaus dels ecrans personalizats", "menu.custom_screen_info.tooltip": "Aquò es un ecran personalizat. Clicatz aicí per ne saber mai.", "menu.disconnect": "Se desconnectar", "menu.feedback": "Comentari...", "menu.feedback.title": "Comentari", "menu.game": "Menut dau juòc", "menu.modded": " (<PERSON><PERSON><PERSON>)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Opcions...", "menu.paused": "Juòc en pausa", "menu.playdemo": "Jogar sul monde de demonstracion", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Preparacion de la zòna d'aparicion : %s%%", "menu.quick_actions": "Accions Rapidas...", "menu.quick_actions.title": "Accions Rapidas", "menu.quit": "<PERSON><PERSON><PERSON> lo juòc", "menu.reportBugs": "<PERSON><PERSON><PERSON> un <PERSON>", "menu.resetdemo": "<PERSON><PERSON> comen<PERSON>r lo mond de demonstracion", "menu.returnToGame": "Tornar au juòc", "menu.returnToMenu": "Sauvagardar e quitar la partida", "menu.savingChunks": "Sauvagarda daus tròces", "menu.savingLevel": "Sauvagarda dau monde", "menu.sendFeedback": "Donar una opinion", "menu.server_links": "Ligams del servidor...", "menu.server_links.title": "Ligams del servidor", "menu.shareToLan": "Dobrir au LAN", "menu.singleplayer": "Un jogaire", "menu.working": "Tractament en cors...", "merchant.deprecated": "Los vilatgés s'aprovesisson dusca a dos vegadas per jorn.", "merchant.level.1": "<PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "Companhon", "merchant.level.4": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Escambis", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Picatz %1$s per davalar", "multiplayer.applyingPack": "Aplicacion del pack de ressorsas", "multiplayer.confirm_command.parse_errors": "Ensajatz d’executar una comanda desconeguda o invalida.\nSètz segur  ?\nComanda : %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Authentication servers are down. Please try again later, sorry!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Tu estàs destèrrat d'èst servidor", "multiplayer.disconnect.banned.expiration": "\n<PERSON><PERSON> vai èsser retirat en %s", "multiplayer.disconnect.banned.reason": "Tu estàs destèrrat d'est servidor.\nRason: %s", "multiplayer.disconnect.banned_ip.expiration": "\nVòstre destèrro vai èsser retirar en %s", "multiplayer.disconnect.banned_ip.reason": "Teu adreça IP està destèrrat d'est servidor.\nRason: %s", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "You logged in from another location", "multiplayer.disconnect.expired_public_key": "Perfil de clau publica expirat. Verificatz que la data e l'ora de vòstre sistèma son sincronizats e ensajatz de tornar lançar lo jòc.", "multiplayer.disconnect.flying": "Volar non es permetent en aqueth servidor", "multiplayer.disconnect.generic": "Desligat", "multiplayer.disconnect.idling": "You have been idle for too long!", "multiplayer.disconnect.illegal_characters": "Caractèrs illegaus dins lo chat", "multiplayer.disconnect.incompatible": "Client incompatible! Se vos plai, utilizatz %s", "multiplayer.disconnect.invalid_entity_attacked": "Attempting to attack an invalid entity", "multiplayer.disconnect.invalid_packet": "Lo servidor envièt un paquet pas valid", "multiplayer.disconnect.invalid_player_data": "Donadas de jogaire pas validas", "multiplayer.disconnect.invalid_player_movement": "Invalid move player packet received", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Invalid move vehicle packet received", "multiplayer.disconnect.ip_banned": "Vòstra adreça IP es bandida d'aqueste servidor", "multiplayer.disconnect.kicked": "Expulsat(ada) pel operator", "multiplayer.disconnect.missing_tags": "Incomplete set of tags received from server.\nPlease contact server operator.", "multiplayer.disconnect.name_taken": "Aqueste nom èst dejà pres", "multiplayer.disconnect.not_whitelisted": "Sètz pas sus la lista blanca d'aquel servidor!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Client incompatible! Se vos plai, utilizatz %s", "multiplayer.disconnect.outdated_server": "Client incompatible! Se vos plai, utilizatz %s", "multiplayer.disconnect.server_full": "Lo servidor està plen!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> fermat", "multiplayer.disconnect.slow_login": "La connexion a pres tròp de temps", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Unexpected custom data from client", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Impossible de verificar vòstre nom d'utilizaire!", "multiplayer.downloadingStats": "Recuperant las estatisticas...", "multiplayer.downloadingTerrain": "Cargament dau terren...", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": "Can't deliver chat message, check server logs: %s", "multiplayer.player.joined": "%s a rejonch lo juòc", "multiplayer.player.joined.renamed": "%s (ancianament conegut/da jol nom de %s) a rejonch lo juòc", "multiplayer.player.left": "%s a quitat lo juòc", "multiplayer.player.list.hp": "%s PV", "multiplayer.player.list.narration": "Jogaires en linha : %s", "multiplayer.requiredTexturePrompt.disconnect": "Aquest servidor requerís un paquet de ressorsas personalizat", "multiplayer.requiredTexturePrompt.line1": "This server requires the use of a custom resource pack.", "multiplayer.requiredTexturePrompt.line2": "Fòragetar aquel paquet de ressorsa personalizat vos desconnectarà d'aquel servidor.", "multiplayer.socialInteractions.not_available": "Social Interactions are only available in Multiplayer worlds", "multiplayer.status.and_more": "... e %s mai... ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Impossible de se connectar au servidor", "multiplayer.status.cannot_resolve": "Impossible de resòlvre lo nom de l'òste", "multiplayer.status.finished": "Acabat", "multiplayer.status.incompatible": "Version incompatibla!", "multiplayer.status.motd.narration": "Messatge del jorn : %s", "multiplayer.status.no_connection": "(sens connexion)", "multiplayer.status.old": "vielh\n", "multiplayer.status.online": "En linha", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "Ping en cors...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s jogaires sus %s en linha", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "Status request has been handled", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Received unrequested status", "multiplayer.status.version.narration": "Version del servidor : %s", "multiplayer.stopSleeping": "Sortir dau lièch", "multiplayer.texturePrompt.failure.line1": "Se pòt pas aplicar lo paquet de ressorsas del servidor.", "multiplayer.texturePrompt.failure.line2": "Any functionality that requires custom resources might not work as expected", "multiplayer.texturePrompt.line1": "Aqueste servidor recomanda l'emplec d'un paquet de ressorças personalizat.", "multiplayer.texturePrompt.line2": "Lo volètz telecargar e l'installar automagicament?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMessatge del servidor:\n%s", "multiplayer.title": "<PERSON><PERSON> en Multijogaire", "multiplayer.unsecureserver.toast": "Messages sent on this server may be modified and might not reflect the original message", "multiplayer.unsecureserver.toast.title": "Chat messages can't be verified", "multiplayerWarning.check": "Do not show this screen again", "multiplayerWarning.header": "Caution: Third-Party Online Play", "multiplayerWarning.message": "Caution: Online play is offered by third-party servers that are not owned, operated, or supervised by Mojang Studios or Microsoft. During online play, you may be exposed to unmoderated chat messages or other types of user-generated content that may not be suitable for everyone.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygene", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Boton : %s", "narration.button.usage.focused": "Picatz Entrada per activar", "narration.button.usage.hovered": "Clic esquèr per activar", "narration.checkbox": "Casa : %s", "narration.checkbox.usage.focused": "Picatz Entrar per Activar/Desactivar", "narration.checkbox.usage.hovered": "Clic esquèr per activar/descativar", "narration.component_list.usage": "Picatz Tab per navigar a l'element seguent", "narration.cycle_button.usage.focused": "Picatz Entrar per cambiar per %s", "narration.cycle_button.usage.hovered": "Clic esquèr per cambiar a %s", "narration.edit_box": "Camp de sasida: %s", "narration.item": "Objècte : %s", "narration.recipe": "Recèpta de %s", "narration.recipe.usage": "<PERSON>lic esquèr per seleccionar", "narration.recipe.usage.more": "Clic drech per mostrar mai de recèptas", "narration.selection.usage": "Picatz las tòcas naut o bas per cambiar la seleccion", "narration.slider.usage.focused": "Picatz las tòcas esquèrra o drecha per cambiar la valor", "narration.slider.usage.hovered": "Drag slider to change value", "narration.suggestion": "La suggeston %s de %s anèt seleccionat: %s", "narration.suggestion.tooltip": "La suggeston %s de %s anèt seleccionat: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Picatz Tab per passar a la suggestion segenta", "narration.suggestion.usage.cycle.hidable": "Picatz Tab per passar a la suggestion segenta, o Esc per sortir de las suggestions", "narration.suggestion.usage.fill.fixed": "Picatz Tab per utilizar la suggestion", "narration.suggestion.usage.fill.hidable": "Picatz Tab per utilizar la suggestion, o Esc per sortir de las suggestions", "narration.tab_navigation.usage": "Picatz Ctr e Tab per navigar entre las ongletas", "narrator.button.accessibility": "Accessibilitat", "narrator.button.difficulty_lock": "Varrolhatge", "narrator.button.difficulty_lock.locked": "Blocat", "narrator.button.difficulty_lock.unlocked": "Desblocat", "narrator.button.language": "Lenga", "narrator.controls.bound": "Lo boton %s es ligat a %s", "narrator.controls.reset": "Tornar botar lo boton %s", "narrator.controls.unbound": "%s es pas ligat", "narrator.joining": "Dintrada", "narrator.loading": "Cargament : %s", "narrator.loading.done": "Acabat", "narrator.position.list": "Linha %s sus %s de la tièra", "narrator.position.object_list": "Element %s sus %s de la linha", "narrator.position.screen": "Element d'ecran %s sus %s", "narrator.position.tab": "Onglet %s sus %s", "narrator.ready_to_play": "<PERSON><PERSON><PERSON><PERSON>", "narrator.screen.title": "<PERSON>ut principau", "narrator.screen.usage": "Utilizatz lo cursor de la mirga o la tòca Tab per causir un element", "narrator.select": "Seleccion : %s", "narrator.select.world": "%s seleccionat, jogat pel da<PERSON><PERSON>r còp lo %s, en %s, %s, en version : %s", "narrator.select.world_info": "%s seleccionat, jogat pel da<PERSON><PERSON>r c<PERSON>p lo : %s, %s", "narrator.toast.disabled": "Na<PERSON>dor <PERSON>act<PERSON>", "narrator.toast.enabled": "<PERSON><PERSON>dor activat", "optimizeWorld.confirm.description": "Aquesta operacion va ensajar d'optimisar lo vòstre monde en s'assegurant que totas las donadas siagan estocadas dins lo format de juòc lo mai recent. Aquò pòt prendre mai o mens de temps en fonccion de la talha dau vòstre monde. Un còp fach, vòstre monde deuriá fonccionar mai lèu, mas serà pas pus compatible amb las ancianas versions dau juòc. Volètz vertadièrament contunhar?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Optimizar lo monde", "optimizeWorld.info.converted": "<PERSON><PERSON><PERSON><PERSON> meses a jorn: %s", "optimizeWorld.info.skipped": "Tròces omeses: %s", "optimizeWorld.info.total": "Tròces en totau: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Comptant los tròces...", "optimizeWorld.stage.failed": "Fracàs! :(", "optimizeWorld.stage.finished": "Finalizacion...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Mesa a jorn de totes los tròces...", "optimizeWorld.stage.upgrading.chunks": "Mesa a jorn de totes los tròces...", "optimizeWorld.stage.upgrading.entities": "Mesa a jorn de totes las entitats...", "optimizeWorld.stage.upgrading.poi": "Mesa a jorn de totes los ponches d'interès...", "optimizeWorld.title": "Optimizant lo monde \"%s\"", "options.accessibility": "Configuracion d'Accessibilitat...", "options.accessibility.high_contrast": "Contrast naut", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available.", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "Contrast naut del contorn dels blòcs", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Guida d'accessibilitat", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Permet al narrador d'èsser (des)activat amb \"Cmd + B\".", "options.accessibility.narrator_hotkey.tooltip": "Permet al narrador d'èsser (des)activat amb \"Ctrl + B\".", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "Fons del tèxt", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON><PERSON>", "options.accessibility.text_background_opacity": "Opacitat del fons del tèxt", "options.accessibility.title": "Paramètres d'accessibilitat...", "options.allowServerListing": "Allow Server Listings", "options.allowServerListing.tooltip": "Servers may list online players as part of their public status.\nWith this option off your name will not show up in such lists.", "options.ao": "Esclairatge suau", "options.ao.max": "<PERSON><PERSON>", "options.ao.min": "Minimom", "options.ao.off": "Desactivat", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "Barra rapida", "options.attackIndicator": "Indicator d'ataca", "options.audioDevice": "Aparelh", "options.audioDevice.default": "De<PERSON>ut dau sistèma", "options.autoJump": "Saut automatic", "options.autoSuggestCommands": "Suggestions de commanda", "options.autosaveIndicator": "Indicator de sauv. auto.", "options.biomeBlendRadius": "Transicion de biòmas", "options.biomeBlendRadius.1": "Non (plus rapid)", "options.biomeBlendRadius.11": "11x11 (Extrem)", "options.biomeBlendRadius.13": "13x13 (Exhibicionista)", "options.biomeBlendRadius.15": "15x15 (maximal)", "options.biomeBlendRadius.3": "3x3 (Rapid)", "options.biomeBlendRadius.5": "5x5 (normau)", "options.biomeBlendRadius.7": "7x7 (Aut)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON><PERSON>)", "options.chat": "Opcions del chat...", "options.chat.color": "Colors", "options.chat.delay": "Delai de la discussion: %s segonda", "options.chat.delay_none": "Delai de la discussion: pas cap", "options.chat.height.focused": "Nautor <PERSON>", "options.chat.height.unfocused": "Nautor Trebola", "options.chat.line_spacing": "Interlinha", "options.chat.links": "Ligams Web", "options.chat.links.prompt": "Avisar abans d'obrir un ligam", "options.chat.opacity": "Opacitat del tèxt de la discussion", "options.chat.scale": "<PERSON><PERSON><PERSON> del tèxt de la discussion", "options.chat.title": "Opcions del chat...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Visible", "options.chat.visibility.hidden": "Amagat", "options.chat.visibility.system": "Solament las comandas", "options.chat.width": "Largor", "options.chunks": "%s tròces", "options.clouds.fancy": "Detalhats", "options.clouds.fast": "Rapides", "options.controls": "Contraròtles...", "options.credits_and_attribution": "Crèdits e atribucions...", "options.damageTiltStrength": "Damage Tilt", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "<PERSON><PERSON><PERSON> monocratic", "options.darkMojangStudiosBackgroundColor.tooltip": "Changes the Mojang Studios loading screen background color to black.", "options.darknessEffectScale": "Pulsacion de foscor", "options.darknessEffectScale.tooltip": "Regula la quantitat de pulsacion de foscor recebudas de la part de un Warden o de un udolaire sculk.", "options.difficulty": "Dificultat", "options.difficulty.easy": "Aisida", "options.difficulty.easy.info": "Hostile mobs spawn but deal less damage. Hunger bar depletes and drains health down to 5 hearts.", "options.difficulty.hard": "Malaisida", "options.difficulty.hard.info": "Hostile mobs spawn and deal more damage. Hunger bar depletes and drains all health.", "options.difficulty.hardcore": "Extrèm", "options.difficulty.normal": "<PERSON><PERSON>", "options.difficulty.normal.info": "Hostile mobs spawn and deal standard damage. Hunger bar depletes and drains health down to half a heart.", "options.difficulty.online": "Dificultat del servidor", "options.difficulty.peaceful": "Pacifica", "options.difficulty.peaceful.info": "No hostile mobs and only some neutral mobs spawn. Hunger bar doesn't deplete and health replenishes over time.", "options.directionalAudio": "Directional Audio", "options.directionalAudio.off.tooltip": "Classic Stereo sound.", "options.directionalAudio.on.tooltip": "Uses HRTF-based directional audio to improve the simulation of 3D sound. Requires HRTF compatible audio hardware, and is best experienced with headphones.", "options.discrete_mouse_scroll": "Desplaça<PERSON> discrèt", "options.entityDistanceScaling": "Distància d'entitats", "options.entityShadows": "Ombras d'entitats", "options.font": "Font Settings...", "options.font.title": "Font Settings", "options.forceUnicodeFont": "Forçar la Poliça Unicode", "options.fov": "Camp de vision", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON>", "options.fovEffectScale": "Efièches del camp de vision", "options.fovEffectScale.tooltip": "Regula la amplitud de las variacions del camp de vision en foncion dels efièches en jòc.", "options.framerate": "%s fps", "options.framerateLimit": "FPS maximom", "options.framerateLimit.max": "Illimitat", "options.fullscreen": "<PERSON>len ecran", "options.fullscreen.current": "Actuala", "options.fullscreen.entry": "%s × %s (%s Hz, %s bits)", "options.fullscreen.resolution": "Resocution de plen ecran", "options.fullscreen.unavailable": "Paramètre indisponible", "options.gamma": "Luminositat", "options.gamma.default": "<PERSON> defaut", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Escur", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON>", "options.glintSpeed.tooltip": "Controls how fast the visual glint shimmers across enchanted items.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Controls how transparent the visual glint is on enchanted items.", "options.graphics": "Grafics", "options.graphics.fabulous": "Fabuloses!", "options.graphics.fabulous.tooltip": "%s graphics uses screen shaders for drawing weather, clouds, and particles behind translucent blocks and water.\nThis may severely impact performance for portable devices and 4K displays.", "options.graphics.fancy": "Detalhats", "options.graphics.fancy.tooltip": "Fancy graphics balances performance and quality for the majority of machines.\nWeather, clouds, and particles may not appear behind translucent blocks or water.", "options.graphics.fast": "Rapides", "options.graphics.fast.tooltip": "Los grafismes \"rapids\" redusisson la qualitat de la pluèja e de la nèu visibles al ecran.\nLos efièches de tranparéncia son desactivats per d'unes blocs, tal coma las fuèlhas.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Revenir en arrièra", "options.graphics.warning.message": "Your graphics device is detected as unsupported for the %s graphics option.\n\nYou may ignore this and continue, however support will not be provided for your device if you choose to use %s graphics.", "options.graphics.warning.renderer": "Renderer detected: [%s]", "options.graphics.warning.title": "Graphics Device Unsupported", "options.graphics.warning.vendor": "Vendor detected: [%s]", "options.graphics.warning.version": "Version d'OpenGL detectada: [%s]", "options.guiScale": "Talha de l'interfàcia", "options.guiScale.auto": "Automatica", "options.hidden": "Amagat", "options.hideLightningFlashes": "Amagar la lutz daus liuç", "options.hideLightningFlashes.tooltip": "Empacha los ulhauces luminoses faches pel fólzer dins lo cèl. Lo fólzer serà encara visible.", "options.hideMatchedNames": "Amagar los noms coïncident", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "Inactiu", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "Inversar la Mirga", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "<PERSON><PERSON>", "options.language": "Lenga...", "options.language.title": "Lenga", "options.languageAccuracyWarning": "(Las reviradas pòdon èsser pas fisablas a 100%%)", "options.languageWarning": "Las reviradas pòdon èsser pas fisablas a 100%%", "options.mainHand": "<PERSON>a", "options.mainHand.left": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.cape": "Capa", "options.modelPart.hat": "<PERSON><PERSON><PERSON>", "options.modelPart.jacket": "Gil<PERSON>", "options.modelPart.left_pants_leg": "Camba esquèrra", "options.modelPart.left_sleeve": "<PERSON><PERSON>", "options.modelPart.right_pants_leg": "Camba drecha", "options.modelPart.right_sleeve": "<PERSON><PERSON>", "options.mouseWheelSensitivity": "Sensibilitat dau desplaçament", "options.mouse_settings": "Paramètres de Minèria...", "options.mouse_settings.title": "Paramètres de Minèria", "options.multiplayer.title": "Opcions Multijogaire...", "options.multiplier": "%sx", "options.music_frequency": "Frequéncia de la musica", "options.music_frequency.constant": "En continú", "options.music_frequency.default": "<PERSON> defaut", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "Cambia la frequéncia a laquala la musica es jogada dins un mond.", "options.narrator": "Narrator", "options.narrator.all": "Conta tot", "options.narrator.chat": "Conta la discussion", "options.narrator.notavailable": "Disponible pas", "options.narrator.off": "Desactivat", "options.narrator.system": "Conta lo sistèma", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "Desactivat", "options.off.composed": "%s: Desactivat", "options.on": "Activat", "options.on.composed": "%s:  Activat", "options.online": "En linha...", "options.online.title": "Opcions en linha", "options.onlyShowSecureChat": "Only Show Secure Chat", "options.onlyShowSecureChat.tooltip": "Only display messages from other players that can be verified to have been sent by that player, and have not been modified.", "options.operatorItemsTab": "Inventari d'operator", "options.particles": "Particulas", "options.particles.all": "To<PERSON>", "options.particles.decreased": "Redusidas", "options.particles.minimal": "Minimalas", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON> de tr<PERSON>", "options.prioritizeChunkUpdates.byPlayer": "Per accion", "options.prioritizeChunkUpdates.byPlayer.tooltip": "D'unas accions al dintre d'un tròç lo faràn recompilar sul pic. Aquò compren lo plaçament e la destrucion dels blòcs.", "options.prioritizeChunkUpdates.nearby": "Constant", "options.prioritizeChunkUpdates.nearby.tooltip": "Nearby chunks are always compiled immediately. This may impact game performance when blocks are placed or destroyed.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Los tròces pròches son compilats dins de fials d'execucion parallèls. Pòt causar de traucs visuals bracs quand de blòcs son destrusits.", "options.rawMouseInput": "Dintrada dirècta", "options.realmsNotifications": "Realms News & Invites", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "Informacions de desbogatge reduchas", "options.renderClouds": "Nívols", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Dist. d'afichatge", "options.resourcepack": "Paquets de ressorsa...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Efièches de distorsion", "options.screenEffectScale.tooltip": "Intensitat dels efièches de distorsion del ecran provocats pel souslèu e los portaus dau Nether.\nSe la valor es flaca, l'efièch de souslèu es remplaçat per un contorn verd.", "options.sensitivity": "Sensitivitat", "options.sensitivity.max": "IPERVELOCITAT!!!", "options.sensitivity.min": "*badalh*", "options.showNowPlayingToast": "Notificacion musicala", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.simulationDistance": "Dist. de simulacion", "options.skinCustomisation": "Personalizar mon aparéncia...", "options.skinCustomisation.title": "Personalizar mon a<PERSON>én<PERSON>", "options.sounds": "Musica e sons...", "options.sounds.title": "Opcions de musica e sons", "options.telemetry": "Telemetry Data...", "options.telemetry.button": "Data Collection", "options.telemetry.button.tooltip": "\"%s\" includes only the required data.\n\"%s\" includes optional, as well as the required data.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "<PERSON><PERSON>", "options.telemetry.state.minimal": "Minimala", "options.telemetry.state.none": "Pas cap", "options.title": "Opcions", "options.touchscreen": "Mòde Fenestron Tactil", "options.video": "Opcions graficas...", "options.videoTitle": "Opcions graficas", "options.viewBobbing": "Movement de vision", "options.visible": "Vesible", "options.vsync": "VSync", "outOfMemory.message": "Minecraft has run out of memory.\n\nThis could be caused by a bug in the game or by the Java Virtual Machine not being allocated enough memory.\n\nTo prevent world corruption, the current game has quit. We've tried to free up enough memory to let you go back to the main menu and back to playing, but this may not have worked.\n\nPlease restart the game if you see this message again.", "outOfMemory.title": "Out of memory!", "pack.available.title": "Disponible", "pack.copyFailure": "Failed to copy packs", "pack.dropConfirm": "Do you want to add the following packs to Minecraft?", "pack.dropInfo": "Drag and drop files into this window to add packs", "pack.dropRejected.message": "Las entradas seguentas èran pas de paquets valids e son pas estats copiats : %s", "pack.dropRejected.title": "Entradas fòra de p<PERSON>", "pack.folderInfo": "(Plaçatz los paquets aicí)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "This pack was made for a newer version of Minecraft and may not work correctly.", "pack.incompatible.confirm.old": "This pack was made for an older version of Minecraft and may no longer work correctly.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON> segur de voler cargar aquèste paquet?", "pack.incompatible.new": "(Fait per une version mai nòva de Minecraft)", "pack.incompatible.old": "(Fait per una version mai anciana de Minecraft)", "pack.nameAndSource": "%s (%s)\n", "pack.openFolder": "<PERSON><PERSON><PERSON> lo dossièr daus paquets", "pack.selected.title": "Seleccionat", "pack.source.builtin": "intègre", "pack.source.feature": "feature", "pack.source.local": "local", "pack.source.server": "servidor", "pack.source.world": "monde", "painting.dimensions": "%s x %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "L'Albanés", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Cibla bombardada amb capitada", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Cran en fuòc", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Lo bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON>'au<PERSON><PERSON><PERSON> de b<PERSON>", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "La cambiament", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Lo boss final", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "La fauguièra", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Los combatents", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Los escavaments", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Lo quebab als tres pebròt", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "La bruma bassa", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Lo luquet", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "L'òrbe", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Chòtas limones", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Lo camin", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Los arbres de Jade", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON> guin<PERSON>re", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "L'estanh", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La piscina", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "La cavalgada dins la pradariá", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Lo litoral", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "L'estrop mortal", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Cran et Ròsas", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "La scèna es prèsta", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Los virasolelhs", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "Lo solelh colc", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Las marèas", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Lo void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Las tèrras en bosiga", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Aiga", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Air", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "<PERSON><PERSON><PERSON>", "parsing.bool.expected": "Boolean esperat", "parsing.bool.invalid": "Boolean pas valid, \"true\" o \"false\" esperat, valor trobada : %s", "parsing.double.expected": "Double esperat", "parsing.double.invalid": "Double pas valid : %s", "parsing.expected": "\"%s\" esperat", "parsing.float.expected": "Float esperat", "parsing.float.invalid": "Float pas valid : %s", "parsing.int.expected": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>at", "parsing.int.invalid": "Entièr pas valid : %s", "parsing.long.expected": "S'esperava un long", "parsing.long.invalid": "Long pas valid : %s", "parsing.quote.escape": "Invalid escape sequence '\\%s' in quoted string", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.invalidOptions": "Mèuca de l'analisi sintaxi de las opcions de particulas : %s", "particle.notFound": "Particula desconeguda '%s'", "permissions.requires.entity": "An entity is required to run this command here", "permissions.requires.player": "A player is required to run this command here", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Quand aplicada:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicat desconegut: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "Australia (Nòva Galas del Sud)", "realms.configuration.region.australia_southeast": "Australia (Victòria)", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "Índia", "realms.configuration.region.central_us": "Estats Units (Iowa)", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Estats Units (Virgínia)", "realms.configuration.region.east_us_2": "Estats Units (Carolina del Nòrd)", "realms.configuration.region.france_central": "França", "realms.configuration.region.japan_east": "Japon oriental", "realms.configuration.region.japan_west": "Japon occidental", "realms.configuration.region.korea_central": "Corèa del Sud", "realms.configuration.region.north_central_us": "Estats Units (Illinois)", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Estats Units (Tèxas)", "realms.configuration.region.southeast_asia": "Singapor", "realms.configuration.region.sweden_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.uae_north": "Emirats Arabis Units", "realms.configuration.region.uk_south": "Anglatèrra meridionala", "realms.configuration.region.west_central_us": "Estats Units (Utah)", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "Estats Units (Califòrnia)", "realms.configuration.region.west_us_2": "Estats Units (Washington)", "realms.configuration.region_preference.automatic_owner": "Automatic (ping del proprietari del servidor)", "realms.configuration.region_preference.automatic_player": "Automatic (primi<PERSON>r a rej<PERSON>her lo servidor)", "realms.missing.snapshot.error.text": "Los Realms son pas pres en carga per ara dins las versions de desvolopament", "recipe.notFound": "Recèpta desconeguda: '%s'", "recipe.toast.description": "Verificatz lo receptari", "recipe.toast.title": "Nòvas recèptas !", "record.nowPlaying": "Lectura en cors: %s", "recover_world.bug_tracker": "<PERSON><PERSON><PERSON> un <PERSON>", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "desconegut", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "BROKEN ASSETS DETECTED", "resourcePack.high_contrast.name": "Contrast naut", "resourcePack.load_fail": "Error en recargar los recorsos", "resourcePack.programmer_art.name": "art del programmador ", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "Ressorças especificas au monde", "resourcePack.title": "Seleccionar de paquets de ressorsa", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "Per deca", "resourcepack.downloading": "Telecargament dau paquet de ressorsa", "resourcepack.progress": "Telecargament dau fichièr (%s MB)...", "resourcepack.requesting": "Req<PERSON><PERSON><PERSON> en cors...", "screenshot.failure": "Sauvagarda de la captura d'ecran impossibla: %s", "screenshot.success": "Captura d'ecran sauvagardada jol nom de %s", "selectServer.add": "<PERSON><PERSON><PERSON><PERSON> servidor", "selectServer.defaultName": "Servidor Minecraft", "selectServer.delete": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "Sètz segur que volètz suprimir aqueste servidor?", "selectServer.deleteWarning": "\"%s\" serà perdut per totjorn ! (Fòrca longtemps)", "selectServer.direct": "Connexion dirècta", "selectServer.edit": "Modificar", "selectServer.hiddenAddress": "(Amagada)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.access_failure": "Fracàs de l'accès au monde", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON>", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON> coma /gamemode, /experience", "selectWorld.allowCommands.new": "Autorizar comandas", "selectWorld.backupEraseCache": "Esfaçar les informacions amagada", "selectWorld.backupJoinConfirmButton": "Crear sauvagarda e cargar", "selectWorld.backupJoinSkipButton": "Sabi çò que fau!", "selectWorld.backupQuestion.customized": "Los mondes personalizats son pas mai supportats", "selectWorld.backupQuestion.downgrade": "Downgrading a world is not supported", "selectWorld.backupQuestion.experimental": "Los mondes qu'utilizan de paramètres experimentaus son pas suportats", "selectWorld.backupQuestion.snapshot": "Volètz de verai cargar aquèste monde ?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON>, los mondes personalizats son pas suportats in aquela version de Minecraft. Podèm ensajar de cargar aquel mond e gardar tot coma èra, mas cada novèl terren generat podrà pas èsser personalizat. Desencusatz-nos per aquela inconveniéncia !", "selectWorld.backupWarning.downgrade": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work. If you still want to continue, please make a backup.", "selectWorld.backupWarning.experimental": "Aquest monde utiliza de paramètres experimentaus que pòdan arrestar de foncionar a cada instant. Pòdem pas assegurar que va cargar o capitar.", "selectWorld.backupWarning.snapshot": "Aqueste monde foguèt jogat en darrièr en version %s, sètz sus la snapshot %s. Mercé de faire una sauvagarda en cas que lo monde serià corromput!", "selectWorld.bonusItems": "Còfre bonus", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "A d'èsser convertit!", "selectWorld.conversion.tooltip": "Aqueste monde dèu èsser dobèrt dins una version mai anciana (tau que la 1.6.4) per èsser convertit en tota securitat", "selectWorld.create": "<PERSON><PERSON><PERSON> un monde nòu", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "Paquets de donadas", "selectWorld.data_read": "Lectura de las donadas dau monde...", "selectWorld.delete": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "Sètz segur que volètz suprimir aqueste mond?", "selectWorld.deleteWarning": "'serà perdut per totjorn! (<PERSON>òrça longtem<PERSON>!)", "selectWorld.delete_failure": "Pas capitat a suprimir lo monde", "selectWorld.edit": "Modificar", "selectWorld.edit.backup": "Faire una sauvagarda", "selectWorld.edit.backupCreated": "Sauvagardat: %s", "selectWorld.edit.backupFailed": "Fracàs de sauvagarda", "selectWorld.edit.backupFolder": "Do<PERSON>r lo dossièr de sauvagardas", "selectWorld.edit.backupSize": "pes: %s MB", "selectWorld.edit.export_worldgen_settings": "Exportar los paramètres de generacion", "selectWorld.edit.export_worldgen_settings.failure": "Fracàs de l'exportacion", "selectWorld.edit.export_worldgen_settings.success": "Exportat", "selectWorld.edit.openFolder": "Do<PERSON>r lo dossièr dau monde", "selectWorld.edit.optimize": "Optimizar lo monde", "selectWorld.edit.resetIcon": "Reïnicializar l'icòna", "selectWorld.edit.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.title": "Modificar lo monde", "selectWorld.enterName": "Nom dau monde", "selectWorld.enterSeed": "Grana pel generator de mondes", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalhs", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experimentacions", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "Something went wrong while trying to load a world from a future version. This was a risky operation to begin with; sorry it didn't work.", "selectWorld.futureworld.error.title": "Se a produch un error!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": "Identic au mòde subrevida, mas los blòcs", "selectWorld.gameMode.adventure.line2": "pòdon pas èsser plaçats o destruches", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "Create, build, and explore without limits. You can fly, have endless materials, and can't be hurt by monsters.", "selectWorld.gameMode.creative.line1": "Ressorsas illimitadas, possibilitat de volar e", "selectWorld.gameMode.creative.line2": "destruccion de blòcs instantanèa", "selectWorld.gameMode.hardcore": "Extrèm", "selectWorld.gameMode.hardcore.info": "Coma lo mòde subrevida, mai verrolhat sus la dificultat \"malaisida\". Podètz pas reaparéisser se morissètz.", "selectWorld.gameMode.hardcore.line1": "<PERSON>ò meteis que lo mòde subrevida, blocat a la", "selectWorld.gameMode.hardcore.line2": "dificultat maximala, e amb una sola vida", "selectWorld.gameMode.spectator": "Espectator", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON><PERSON> a<PERSON>, mas pas tocar.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON><PERSON> a<PERSON>, mas pas tocar", "selectWorld.gameMode.survival": "Subrevida", "selectWorld.gameMode.survival.info": "Explore a mysterious world where you build, collect, craft, and fight monsters.", "selectWorld.gameMode.survival.line1": "Cercatz de ressorças, fabricatz,", "selectWorld.gameMode.survival.line2": "gan<PERSON>z de nivèus, vida e fam", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON> dau ju<PERSON>c", "selectWorld.import_worldgen_settings": "Importar de paramètres", "selectWorld.import_worldgen_settings.failure": "Error d'importacion de paramètres", "selectWorld.import_worldgen_settings.select_file": "Se<PERSON><PERSON><PERSON>r lo fichièr de paramètres (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Version incompatibla : %s", "selectWorld.incompatible.title": "Version incompatibla", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "Creat per una version incompatibla", "selectWorld.load_folder_access": "Impossible de legir o d'accedir au dossièr de sauvagardas!", "selectWorld.loading_list": "Cargament de la lista dels monds", "selectWorld.locked": "Ja obèrt dins una autra instància de Minecraft", "selectWorld.mapFeatures": "Estructuras", "selectWorld.mapFeatures.info": "Vilatges, varatges, etc.", "selectWorld.mapType": "Tipe de monde", "selectWorld.mapType.normal": "<PERSON><PERSON>", "selectWorld.moreWorldOptions": "Mai d'opcions dau monde...", "selectWorld.newWorld": "<PERSON><PERSON>", "selectWorld.recreate": "Recrear", "selectWorld.recreate.customized.text": "Customized worlds are no longer supported in this version of Minecraft. We can try to recreate it with the same seed and properties, but any terrain customizations will be lost. We're sorry for the inconvenience!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "Something went wrong while trying to recreate a world.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "Serà enregistrat dins:", "selectWorld.search": "cèrca un monde", "selectWorld.seedInfo": "Daissar un espaci blanc per una grana aleatòria", "selectWorld.select": "<PERSON><PERSON> au monde causit", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": "Causir un monde", "selectWorld.tooltip.fromNewerVersion1": "Lo monde es estat sauvagardat dins una version novèla,", "selectWorld.tooltip.fromNewerVersion2": "cargar aquèste mond pòt pausar problèma!", "selectWorld.tooltip.snapshot1": "Oblidatz pas de far una còpia d'aqueste monde", "selectWorld.tooltip.snapshot2": "abans que lo cargetz dins aquèla version de desvolopament.", "selectWorld.unable_to_load": "Impossible de cargar los mondes", "selectWorld.version": "Version :", "selectWorld.versionJoinButton": "<PERSON><PERSON>", "selectWorld.versionQuestion": "Volètz de verai cargar aqueste monde?", "selectWorld.versionUnknown": "Desconegut", "selectWorld.versionWarning": "Aqueste monde foguèt jogat en darrièr dins la version %s e lo cargar dins aquesta version podrià causar sa corrupcion !", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Atencion! Aquestes paramètres utilizan de fonccionalitats experimentalas", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Mond", "sign.edit": "Modificar lo tèxte de la pancarta", "sleep.not_possible": "<PERSON>òt pas passar la nuèch", "sleep.players_sleeping": "%s/%s jogaires dormissent", "sleep.skipping_night": "Paasatz la nuèch", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Emplaçament desconegut '%s'", "snbt.parser.empty_key": "La clau pòt pas èsser vueg", "snbt.parser.expected_binary_numeral": "Nombre binari esperat", "snbt.parser.expected_decimal_numeral": "Nombre decimal esperat", "snbt.parser.expected_float_type": "Nombre a virgula flotanta esperat", "snbt.parser.expected_hex_escape": "Literal de caractèr d'une longor de %s esperat", "snbt.parser.expected_hex_numeral": "Nombre exadecimal esperat", "snbt.parser.expected_integer_type": "Nombre entièr esperat", "snbt.parser.expected_non_negative_number": "Nombre pas negatiu esperat", "snbt.parser.expected_number_or_boolean": "Nombre o boolean esperat", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Tipe d'element de vector pas valid", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Ambient/Environament", "soundCategory.block": "Blòcs", "soundCategory.hostile": "Creaturas ostilas", "soundCategory.master": "Volume principau", "soundCategory.music": "Musica", "soundCategory.neutral": "Creaturas passivas", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Tocadisques/Blòcs de nòtas", "soundCategory.ui": "Interfàcia", "soundCategory.voice": "Votz/Paraula", "soundCategory.weather": "<PERSON><PERSON>", "spectatorMenu.close": "<PERSON>r lo menut", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON>a", "spectatorMenu.root.prompt": "Picatz una clau per causir una comanda, e tornatz picar per l'utilizar.", "spectatorMenu.team_teleport": "Se teleportat a un membre de l'equipa", "spectatorMenu.team_teleport.prompt": "Causir una equipa cap a la quala se teleportar", "spectatorMenu.teleport": "Se teleportar a un jogaire", "spectatorMenu.teleport.prompt": "Causir un jogaire cap au quau se teleportar", "stat.generalButton": "Generau", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Animals abalits", "stat.minecraft.aviate_one_cm": "Distància amb elitres", "stat.minecraft.bell_ring": "Campanas sonadas", "stat.minecraft.boat_one_cm": "Distància en batèu", "stat.minecraft.clean_armor": "Pèças d'Armaduras Netejadas", "stat.minecraft.clean_banner": "Bandièras netejadas", "stat.minecraft.clean_shulker_box": "Boitas de Shulker netejadas", "stat.minecraft.climb_one_cm": "Distància escalada", "stat.minecraft.crouch_one_cm": "Distància percorruda acocolat", "stat.minecraft.damage_absorbed": "Degalhs absorbits", "stat.minecraft.damage_blocked_by_shield": "Degalhs blocats per bloquièr", "stat.minecraft.damage_dealt": "Degalhs infligits", "stat.minecraft.damage_dealt_absorbed": "Degalhs infligits (absorbits)", "stat.minecraft.damage_dealt_resisted": "Degalhs infligits (resistits)", "stat.minecraft.damage_resisted": "Degalhs resistits", "stat.minecraft.damage_taken": "<PERSON>galhs recebuts", "stat.minecraft.deaths": "Nombre de mòrts", "stat.minecraft.drop": "Objèctes escampats", "stat.minecraft.eat_cake_slice": "Parts de pastís manjadas", "stat.minecraft.enchant_item": "Objèctes encantats", "stat.minecraft.fall_one_cm": "Distància tombada", "stat.minecraft.fill_cauldron": "Pa<PERSON>ò<PERSON> emplid<PERSON>", "stat.minecraft.fish_caught": "Peis Pescats", "stat.minecraft.fly_one_cm": "Distància volada", "stat.minecraft.happy_ghast_one_cm": "Distància en Ghast urós", "stat.minecraft.horse_one_cm": "Distància a cavau", "stat.minecraft.inspect_dispenser": "Distribuïdors inspectats", "stat.minecraft.inspect_dropper": "Donaires inspectats", "stat.minecraft.inspect_hopper": "Embuts inspectats", "stat.minecraft.interact_with_anvil": "Interacciones amb un Enclutge", "stat.minecraft.interact_with_beacon": "Interaccions amb una Balisa", "stat.minecraft.interact_with_blast_furnace": "Interaccions amb un fornèu de fusion", "stat.minecraft.interact_with_brewingstand": "Interaccions amb un alambic", "stat.minecraft.interact_with_campfire": "Interaccions amb fuòcs de camp", "stat.minecraft.interact_with_cartography_table": "Interaccions amb una Taula de cartografia", "stat.minecraft.interact_with_crafting_table": "Interaccions amb una taula de trabalh", "stat.minecraft.interact_with_furnace": "Interaccions amb un Forn", "stat.minecraft.interact_with_grindstone": "Interaccions amb una mòla", "stat.minecraft.interact_with_lectern": "Interaccions amb un pupitre", "stat.minecraft.interact_with_loom": "Interaccions amb telièrs", "stat.minecraft.interact_with_smithing_table": "Interracions amb una taula de fargatge", "stat.minecraft.interact_with_smoker": "Interaccions amb un fumador", "stat.minecraft.interact_with_stonecutter": "Interaccions amb un peirièr", "stat.minecraft.jump": "Sauts", "stat.minecraft.leave_game": "Partidas abandonadas", "stat.minecraft.minecart_one_cm": "Distància en vagonet", "stat.minecraft.mob_kills": "Creaturas Tuadas", "stat.minecraft.open_barrel": "Barricas <PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON> d'ender dobèrts", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "stat.minecraft.pig_one_cm": "Distància a esquina de pòrc", "stat.minecraft.play_noteblock": "Blòcs musicaus jogats", "stat.minecraft.play_record": "Discs de musica jogat", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "Plantas botadas en pòt", "stat.minecraft.raid_trigger": "Invasions desclavadas", "stat.minecraft.raid_win": "Invasions vencudas", "stat.minecraft.sleep_in_bed": "Nuèches passadas dins un lièch", "stat.minecraft.sneak_time": "Temps Agrovat", "stat.minecraft.sprint_one_cm": "Distància corruda", "stat.minecraft.strider_one_cm": "Distància a arpentaire", "stat.minecraft.swim_one_cm": "Distància nadada", "stat.minecraft.talked_to_villager": "Convèrsa amb los Vilatgeses", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_death": "Temps dempuèi la darrièra mòrt", "stat.minecraft.time_since_rest": "Temps dempuèi lo darrièr repaus", "stat.minecraft.total_world_time": "Temps amb lo mond dobèrt", "stat.minecraft.traded_with_villager": "Escambis amb los Vilatgeses", "stat.minecraft.trigger_trapped_chest": "<PERSON>ò<PERSON><PERSON> trapa desclavats", "stat.minecraft.tune_noteblock": "Blòcs musicaus acordats", "stat.minecraft.use_cauldron": "Aiga presa d'una pairòla", "stat.minecraft.walk_on_water_one_cm": "Distància caminada sus l'aiga", "stat.minecraft.walk_one_cm": "Distància caminada", "stat.minecraft.walk_under_water_one_cm": "Distància caminada jota l'aiga", "stat.mobsButton": "C<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Quantitat copada", "stat_type.minecraft.crafted": "Quantitat Fabricada", "stat_type.minecraft.dropped": "Escampat", "stat_type.minecraft.killed": "Avètz tuat %s %s", "stat_type.minecraft.killed.none": "Avètz pas jamai tuat %s", "stat_type.minecraft.killed_by": "%s vos a tuat %s còp(s)", "stat_type.minecraft.killed_by.none": "Sètz pas jamai estat tuat per %s", "stat_type.minecraft.mined": "Quantitat Minada", "stat_type.minecraft.picked_up": "Quantitat Amassada", "stat_type.minecraft.used": "Nombre d'utilizacions", "stats.none": "-", "structure_block.button.detect_size": "DETECTAR", "structure_block.button.load": "CARGAR", "structure_block.button.save": "SAUVA", "structure_block.custom_data": "Nom del Data Tag empersonat", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Canton: %s", "structure_block.hover.data": "Donadas : %s", "structure_block.hover.load": "Cargament : %s", "structure_block.hover.save": "Sauvagardar: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Plenor e grana de la structura", "structure_block.integrity.integrity": "Integritat de l'estructura", "structure_block.integrity.seed": "Grana de l'estructura", "structure_block.invalid_structure_name": "Nom d'èstructura pas valid \"%s\"", "structure_block.load_not_found": "Structura '%s' es pas dispausidís", "structure_block.load_prepare": "Structure '%s' position preparada", "structure_block.load_success": "Structura caigarda de '%s'", "structure_block.mode.corner": "Canton", "structure_block.mode.data": "Donadas", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "<PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Corner Mode - Placement and size marker", "structure_block.mode_info.data": "Data Mode - Game logic marker", "structure_block.mode_info.load": "<PERSON><PERSON>de cargar - cargar venedor del fiquìer", "structure_block.mode_info.save": "Mòde salve<PERSON>ar - escríver dins lo fiquièr", "structure_block.position": "Position relativa", "structure_block.position.x": "posicion relativa x", "structure_block.position.y": "posicion relativa y", "structure_block.position.z": "posicion relativa z", "structure_block.save_failure": "Impossibla de sauvagardar l'estructura '%s'", "structure_block.save_success": "Estructura sauvagardat amb '%s'", "structure_block.show_air": "Show Invisible Blocks:", "structure_block.show_boundingbox": "Show Bounding Box:", "structure_block.size": "Talha de l'estructura", "structure_block.size.x": "talha de l'estructura x", "structure_block.size.y": "talha de l'estructura y", "structure_block.size.z": "talha de l'estructura z", "structure_block.size_failure": "Impossibla de detectar la tailha de la strutura. Ajust cantons amb las structuras amb tanplan nom que fonctionnada", "structure_block.size_success": "Tailha detectada amb escasuda pòr '%s'", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Nom de l'estructura", "subtitles.ambient.cave": "Bruch inquietant", "subtitles.ambient.sound": "Bruch inquietant", "subtitles.block.amethyst_block.chime": "Tindadís d'ametista", "subtitles.block.amethyst_block.resonate": "Ametista clantís", "subtitles.block.anvil.destroy": "Enclutge copat", "subtitles.block.anvil.land": "Enclutge Tombat", "subtitles.block.anvil.use": "Enclutge Emplegat", "subtitles.block.barrel.close": "Barrica se barra", "subtitles.block.barrel.open": "Barrica se dobrís", "subtitles.block.beacon.activate": "Balisa activada", "subtitles.block.beacon.ambient": "Bronzin de balisa", "subtitles.block.beacon.deactivate": "Balisa desactivada", "subtitles.block.beacon.power_select": "Poder de la balisa selectionat", "subtitles.block.beehive.drip": "Pingos de mèu", "subtitles.block.beehive.enter": "<PERSON><PERSON> dintra dins un bornhon", "subtitles.block.beehive.exit": "<PERSON><PERSON>", "subtitles.block.beehive.shear": "Brasca reculhita", "subtitles.block.beehive.work": "<PERSON><PERSON>", "subtitles.block.bell.resonate": "<PERSON><PERSON> re<PERSON>a", "subtitles.block.bell.use": "<PERSON>ana sona", "subtitles.block.big_dripleaf.tilt_down": "Got<PERSON><PERSON><PERSON><PERSON><PERSON> plega", "subtitles.block.big_dripleaf.tilt_up": "Gotafuèlha se redreçar", "subtitles.block.blastfurnace.fire_crackle": "Fornèu de fusion peteja", "subtitles.block.brewing_stand.brew": "Alambic bolhona", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON><PERSON> esclatan", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON><PERSON> fl<PERSON>", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON> ascendan", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON><PERSON> remolina<PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.button.click": "<PERSON>lic de boton", "subtitles.block.cake.add_candle": "Pastís es<PERSON>", "subtitles.block.campfire.crackle": "Fuòc de camp peteja", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON><PERSON>a", "subtitles.block.candle.extinguish": "Candèla s'escantís", "subtitles.block.chest.close": "<PERSON>ò<PERSON><PERSON> se barra", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.death": "Flor de còrus que fena", "subtitles.block.chorus_flower.grow": "Flor de còrus creis", "subtitles.block.comparator.click": "Comparator acciona<PERSON>", "subtitles.block.composter.empty": "Compostator voidat", "subtitles.block.composter.fill": "Compostator emplit", "subtitles.block.composter.ready": "Compostador compòsta", "subtitles.block.conduit.activate": "Conduch activat", "subtitles.block.conduit.ambient": "Pulsacion de conduch", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON> ataca", "subtitles.block.conduit.deactivate": "Conduch desactivat", "subtitles.block.copper_bulb.turn_off": "Ampola en coire s'atuda", "subtitles.block.copper_bulb.turn_on": "Ampola en coire s'aluca", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON> se barra", "subtitles.block.copper_trapdoor.open": "Trapa se dob<PERSON>ís", "subtitles.block.crafter.craft": "Fabricator fabrica", "subtitles.block.crafter.fail": "Fabricator fa mèuca", "subtitles.block.creaking_heart.hurt": "Renada de còr de cruisseire", "subtitles.block.creaking_heart.idle": "Bruch inquietant", "subtitles.block.creaking_heart.spawn": "Còr de cruisseire se desvelha", "subtitles.block.deadbush.idle": "Bruches secs", "subtitles.block.decorated_pot.insert": "Empliment d'un vas ondrat", "subtitles.block.decorated_pot.insert_fail": "Vas ondrat oscilla", "subtitles.block.decorated_pot.shatter": "Trencament de vas ondrat", "subtitles.block.dispenser.dispense": "Objècte Distribuït", "subtitles.block.dispenser.fail": "Distribuïdor que capita pas", "subtitles.block.door.toggle": "Pòrta que cruss<PERSON>", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Taula d'encantament utilizada", "subtitles.block.end_portal.spawn": "Portau de l'End se dobrís", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.close": "Uèlhquidèa se barra", "subtitles.block.eyeblossom.idle": "Mormolh d'uèlhquidèa", "subtitles.block.eyeblossom.open": "Uèlhquidèa s'obrís", "subtitles.block.fence_gate.toggle": "Pòrta de bar<PERSON> que c<PERSON>", "subtitles.block.fire.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.fire.extinguish": "Extincion d'un fuòc", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.frogspawn.hatch": "Cap<PERSON><PERSON><PERSON> nais", "subtitles.block.furnace.fire_crackle": "<PERSON>n peteja", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON><PERSON> trencat", "subtitles.block.generic.fall": "Quicòm es tombat sus un blòc", "subtitles.block.generic.footsteps": "Bruches de pas", "subtitles.block.generic.hit": "<PERSON><PERSON><PERSON> d'un blòc", "subtitles.block.generic.place": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.grindstone.use": "Mòla utilizada", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> talhada", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> oscilla", "subtitles.block.honey_block.slide": "Lisant en un blòc de mèu", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON> se barra", "subtitles.block.iron_trapdoor.open": "Trapa se dob<PERSON>ís", "subtitles.block.lava.ambient": "Botiòlas de lava petan", "subtitles.block.lava.extinguish": "<PERSON><PERSON> gras<PERSON>ha", "subtitles.block.lever.click": "Agre accionat", "subtitles.block.note_block.note": "Blòc musicau que jòga", "subtitles.block.pale_hanging_moss.idle": "Bruch inquietant", "subtitles.block.piston.move": "<PERSON>ston que bolega", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON>va gota", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava gota dins una pairòla", "subtitles.block.pointed_dripstone.drip_water": "<PERSON>ga gota", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON>ga gota dins una pairòla", "subtitles.block.pointed_dripstone.land": "Estalactita s'esclafa", "subtitles.block.portal.ambient": "Brusiment de portau", "subtitles.block.portal.travel": "Brusiment de portal s'atenua", "subtitles.block.portal.trigger": "Brusiment de portal s'intensifica", "subtitles.block.pressure_plate.click": "Clics de Placa de Pression", "subtitles.block.pumpkin.carve": "Cisalhas escultan", "subtitles.block.redstone_torch.burnout": "Entòrcha que grasilha", "subtitles.block.respawn_anchor.ambient": "Acora de reaparicion brusís", "subtitles.block.respawn_anchor.charge": "Acora de reaparicion cargada", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON><PERSON> depletes", "subtitles.block.respawn_anchor.set_spawn": "Ancora de reaparicion activada", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Sculk bolhona", "subtitles.block.sculk.spread": "Sculk s'espandissent", "subtitles.block.sculk_catalyst.bloom": "Catalisaire de Sculk espandissent", "subtitles.block.sculk_sensor.clicking": "Captaire sculk comen<PERSON> a clicar", "subtitles.block.sculk_sensor.clicking_stop": "Captaire sculk s'a<PERSON><PERSON><PERSON>", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON><PERSON> d'u<PERSON><PERSON><PERSON> sculk", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> se barra", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> se dobr<PERSON>", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> oscilla", "subtitles.block.smithing_table.use": "<PERSON><PERSON> de fargatge utilizada", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON> fuma", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON><PERSON> de niflaire se fend", "subtitles.block.sniffer_egg.hatch": "Uòu de niflaire espel<PERSON>", "subtitles.block.sniffer_egg.plop": "Niflaire pond", "subtitles.block.sponge.absorb": "Sponge sucks", "subtitles.block.sweet_berry_bush.pick_berries": "Bagas culhidas", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "Trapa peta", "subtitles.block.trial_spawner.about_to_spawn_item": "Obj<PERSON><PERSON><PERSON> fun<PERSON> s'anóncia", "subtitles.block.trial_spawner.ambient": "Generator de las espròvas peteja", "subtitles.block.trial_spawner.ambient_charged": "<PERSON><PERSON><PERSON>", "subtitles.block.trial_spawner.ambient_ominous": "<PERSON><PERSON><PERSON>", "subtitles.block.trial_spawner.charge_activate": "Presagi afecta un generator de las espròvas", "subtitles.block.trial_spawner.close_shutter": "Generator de las espròvas se barra", "subtitles.block.trial_spawner.detect_player": "Generator de las espròvas se carga", "subtitles.block.trial_spawner.eject_item": "Generator de las espròvas ejècta un objècte", "subtitles.block.trial_spawner.ominous_activate": "Presagi afecta un generator de las espròvas", "subtitles.block.trial_spawner.open_shutter": "Generator de las espròvas se dobrís", "subtitles.block.trial_spawner.spawn_item": "Objècte funèst ejectat", "subtitles.block.trial_spawner.spawn_item_begin": "Obj<PERSON><PERSON><PERSON> funèst apareis", "subtitles.block.trial_spawner.spawn_mob": "Creatura generada", "subtitles.block.tripwire.attach": "Còrda que se liga", "subtitles.block.tripwire.click": "<PERSON><PERSON> d'una còrda", "subtitles.block.tripwire.detach": "Còrda que se desliga", "subtitles.block.vault.activate": "Còfrefòrt s'activa", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> peteja", "subtitles.block.vault.close_shutter": "Còfrefòrt se barra", "subtitles.block.vault.deactivate": "Còfrefòrt se desactiva", "subtitles.block.vault.eject_item": "Còfrefòrt ejècta un objècte", "subtitles.block.vault.insert_item": "Còfrefòrt se desverrolha", "subtitles.block.vault.insert_item_fail": "Còfrefòrt foragèta un objècte", "subtitles.block.vault.open_shutter": "Còfrefòrt se dobrís", "subtitles.block.vault.reject_rewarded_player": "Còfrefòrt foragèta un jogaire", "subtitles.block.water.ambient": "Aiga <PERSON>", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "Libre plaçat", "subtitles.chiseled_bookshelf.insert_enchanted": "Libre encantat plaçat", "subtitles.chiseled_bookshelf.take": "Libre tirat", "subtitles.chiseled_bookshelf.take_enchanted": "Libre encantat tirat", "subtitles.enchant.thorns.hit": "Picada d'espinas", "subtitles.entity.allay.ambient_with_item": "Allay cerca", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON> la<PERSON>", "subtitles.entity.allay.death": "<PERSON>ay mor<PERSON>", "subtitles.entity.allay.hurt": "<PERSON>ay nafrat", "subtitles.entity.allay.item_given": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.allay.item_taken": "Allay aleuja", "subtitles.entity.allay.item_thrown": "Allay <PERSON>", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON>na", "subtitles.entity.armadillo.brush": "Brossatge d'un tató", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON>a<PERSON>a", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "Tató espia", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON>'en<PERSON>a", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON><PERSON> pè<PERSON> una placa", "subtitles.entity.armadillo.unroll_finish": "Tató se desenròtla", "subtitles.entity.armadillo.unroll_start": "Tató espia", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON><PERSON><PERSON><PERSON> tombat", "subtitles.entity.arrow.hit": "Sageta plantada", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> tocat", "subtitles.entity.arrow.shoot": "Sageta tirada", "subtitles.entity.axolotl.attack": "Axolòtl ataca", "subtitles.entity.axolotl.death": "Axolòtl morís", "subtitles.entity.axolotl.hurt": "Axolòtl nafrat", "subtitles.entity.axolotl.idle_air": "Axolòtl bresilha", "subtitles.entity.axolotl.idle_water": "Axolòtl bresilha", "subtitles.entity.axolotl.splash": "Axolòtl esposca", "subtitles.entity.axolotl.swim": "Axolòtl nada", "subtitles.entity.bat.ambient": "Ratapenada siscla", "subtitles.entity.bat.death": "Ratapenada morís", "subtitles.entity.bat.hurt": "Ratapenada nafrada", "subtitles.entity.bat.takeoff": "Ratapenada s'envola", "subtitles.entity.bee.ambient": "<PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON>", "subtitles.entity.bee.hurt": "<PERSON><PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON> bronzina furios<PERSON>", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> bron<PERSON>a u<PERSON>", "subtitles.entity.bee.sting": "Abelha pica", "subtitles.entity.blaze.ambient": "Blaze respira", "subtitles.entity.blaze.burn": "<PERSON> peteja", "subtitles.entity.blaze.death": "<PERSON> mor<PERSON>", "subtitles.entity.blaze.hurt": "<PERSON> nafrat", "subtitles.entity.blaze.shoot": "Blaze que tira", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON>fagat cla<PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON> morís", "subtitles.entity.bogged.hurt": "Enfagat nafrat", "subtitles.entity.breeze.charge": "Ventolet carga", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON><PERSON> morís", "subtitles.entity.breeze.deflect": "Ventolet desvia", "subtitles.entity.breeze.hurt": "<PERSON>ent<PERSON>t nafrat", "subtitles.entity.breeze.idle_air": "Ventolet vòla", "subtitles.entity.breeze.idle_ground": "Bronzin de Ventolet", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON>t aspira", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON>t sauta", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON><PERSON> ate<PERSON>", "subtitles.entity.breeze.shoot": "Vent<PERSON>t tira", "subtitles.entity.breeze.slide": "Ventolet lisa", "subtitles.entity.breeze.whirl": "Ventolet revoluma", "subtitles.entity.breeze.wind_burst": "Carga de vent peta", "subtitles.entity.camel.ambient": "Camel grunts", "subtitles.entity.camel.dash": "Camel yeets", "subtitles.entity.camel.dash_ready": "Camel recovers", "subtitles.entity.camel.death": "Camel dies", "subtitles.entity.camel.eat": "Camel eats", "subtitles.entity.camel.hurt": "Camel hurts", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON> equipada", "subtitles.entity.camel.sit": "Camel sits down", "subtitles.entity.camel.stand": "Camel stands up", "subtitles.entity.camel.step": "Camel steps", "subtitles.entity.camel.step_sand": "Camel sands", "subtitles.entity.cat.ambient": "<PERSON> miaula", "subtitles.entity.cat.beg_for_food": "Gat demanda", "subtitles.entity.cat.death": "<PERSON> mor<PERSON>", "subtitles.entity.cat.eat": "Cat manja", "subtitles.entity.cat.hiss": "Cat siula", "subtitles.entity.cat.hurt": "<PERSON> nafrat", "subtitles.entity.cat.purr": "Gat ronrona", "subtitles.entity.chicken.ambient": "Pol<PERSON>re cloqua", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.chicken.egg": "Polastre fa l'uòu", "subtitles.entity.chicken.hurt": "Polastre nafrat", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.cod.flop": "Merluça sauteja", "subtitles.entity.cod.hurt": "Merluça nafrada", "subtitles.entity.cow.ambient": "Vaca brama", "subtitles.entity.cow.death": "Vaca morís", "subtitles.entity.cow.hurt": "Vaca nafrada", "subtitles.entity.cow.milk": "Mol<PERSON>nt d'una vaca", "subtitles.entity.creaking.activate": "Cruisseire obsèrva", "subtitles.entity.creaking.ambient": "Cruisseire c<PERSON>issís", "subtitles.entity.creaking.attack": "Cruisseire ataca", "subtitles.entity.creaking.deactivate": "Cruisseire se calma", "subtitles.entity.creaking.death": "Cruisseire s'afondra", "subtitles.entity.creaking.freeze": "Cruisse<PERSON> s'arresta", "subtitles.entity.creaking.spawn": "Cruisseire se manifèsta", "subtitles.entity.creaking.sway": "Cruisseire atacat", "subtitles.entity.creaking.twitch": "Cruisseire tremòla", "subtitles.entity.creaking.unfreeze": "Cruisseire se desplaça", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> morís", "subtitles.entity.creeper.hurt": "<PERSON><PERSON>per nafrat", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> siula", "subtitles.entity.dolphin.ambient": "Dal<PERSON> carrinca", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON>", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> manja", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON> sauta", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> esposca", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON> nada", "subtitles.entity.donkey.ambient": "<PERSON>e", "subtitles.entity.donkey.angry": "<PERSON><PERSON> end<PERSON>ha", "subtitles.entity.donkey.chest": "Còfre d'ase equipat", "subtitles.entity.donkey.death": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON> manja", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> na<PERSON>t", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Negat gargolha", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "Negat morís", "subtitles.entity.drowned.hurt": "Negat nafrat", "subtitles.entity.drowned.shoot": "Negat lança un trident", "subtitles.entity.drowned.step": "Negat camina", "subtitles.entity.drowned.swim": "Negat nada", "subtitles.entity.egg.throw": "<PERSON><PERSON><PERSON> mandat", "subtitles.entity.elder_guardian.ambient": "Grand gardian gemega", "subtitles.entity.elder_guardian.ambient_land": "Grand gardian aleteja", "subtitles.entity.elder_guardian.curse": "Malediccion d'un grand gardian", "subtitles.entity.elder_guardian.death": "Grand gardian morís", "subtitles.entity.elder_guardian.flop": "Grand gardian sauta", "subtitles.entity.elder_guardian.hurt": "Grand gardian na<PERSON>t", "subtitles.entity.ender_dragon.ambient": "<PERSON>", "subtitles.entity.ender_dragon.death": "Dragon morís", "subtitles.entity.ender_dragon.flap": "Dragon aleteja", "subtitles.entity.ender_dragon.growl": "Dragon rena", "subtitles.entity.ender_dragon.hurt": "Dragon nafrat", "subtitles.entity.ender_dragon.shoot": "Dragon qu'escopís", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tomba", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON><PERSON> d'ender mandat", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'ender <PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON> crida", "subtitles.entity.enderman.teleport": "Enderòme se telepòrta", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON> form<PERSON>", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.ambient": "Evocator mormolha", "subtitles.entity.evoker.cast_spell": "Evocator qu'emmasca", "subtitles.entity.evoker.celebrate": "Evocator celèbra", "subtitles.entity.evoker.death": "Evocator morís", "subtitles.entity.evoker.hurt": "Evocator nafrat", "subtitles.entity.evoker.prepare_attack": "Evocator que prepara una ataca", "subtitles.entity.evoker.prepare_summon": "Evocator que prepara una invocacion", "subtitles.entity.evoker.prepare_wololo": "Evocator que prepara un encantament", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON> que mòrd", "subtitles.entity.experience_orb.pickup": "Experi<PERSON><PERSON> obtenguda", "subtitles.entity.firework_rocket.blast": "Fuòc d'artifici qu'espeta", "subtitles.entity.firework_rocket.launch": "Fuòc d'artifici amodat", "subtitles.entity.firework_rocket.twinkle": "Beluga de fuòc d'artifici", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON> retrieved", "subtitles.entity.fishing_bobber.splash": "Fishing Bobber splashes", "subtitles.entity.fishing_bobber.throw": "Cana de pesca mandada", "subtitles.entity.fox.aggro": "<PERSON><PERSON> s'<PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON> plipa", "subtitles.entity.fox.bite": "<PERSON><PERSON> mò<PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.fox.eat": "<PERSON>ard manja", "subtitles.entity.fox.hurt": "<PERSON><PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON>", "subtitles.entity.fox.sleep": "<PERSON>ard ronca", "subtitles.entity.fox.sniff": "<PERSON><PERSON> nifla", "subtitles.entity.fox.spit": "<PERSON><PERSON>", "subtitles.entity.fox.teleport": "<PERSON><PERSON> se telepòrta", "subtitles.entity.frog.ambient": "Granolha rena", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON> manja", "subtitles.entity.frog.hurt": "<PERSON>ol<PERSON> nafrada", "subtitles.entity.frog.lay_spawn": "Granolha pond", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON> sauta", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Es a cremar", "subtitles.entity.generic.death": "<PERSON>s a morir", "subtitles.entity.generic.drink": "Es a beure", "subtitles.entity.generic.eat": "Es a manjar", "subtitles.entity.generic.explode": "Explosion", "subtitles.entity.generic.extinguish_fire": "Extincion de fuòc", "subtitles.entity.generic.hurt": "Quicòm es nafrat", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON><PERSON><PERSON> tombat", "subtitles.entity.generic.splash": "Esposques", "subtitles.entity.generic.swim": "<PERSON><PERSON> a nadar", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "Ghast plora", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> qu'escop<PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Glow Item <PERSON> fills", "subtitles.entity.glow_item_frame.break": "Quadre lusent trenca", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON> <PERSON><PERSON> placed", "subtitles.entity.glow_item_frame.remove_item": "Glow Item Frame empties", "subtitles.entity.glow_item_frame.rotate_item": "Glow Item Frame clicks", "subtitles.entity.glow_squid.ambient": "Tautena lusenta nada", "subtitles.entity.glow_squid.death": "Tautena lusenta morís", "subtitles.entity.glow_squid.hurt": "Tautena lusenta nafrada", "subtitles.entity.glow_squid.squirt": "Tautena lusenta que tira tencha", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> manja", "subtitles.entity.goat.horn_break": "Còrn de cabra se trenca", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> na<PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> sauta", "subtitles.entity.goat.milk": "Molzuda d'una cabra", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stomps", "subtitles.entity.goat.ram_impact": "Goat rams", "subtitles.entity.goat.screaming.ambient": "Goat bellows", "subtitles.entity.goat.step": "Cabra camina", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> gem<PERSON>a", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON> que tira", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> rena", "subtitles.entity.hoglin.angry": "Hoglin rena furiosament", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> converts to <PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> na<PERSON>t", "subtitles.entity.hoglin.retreat": "Hoglin retreats", "subtitles.entity.hoglin.step": "Hoglin camina", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> end<PERSON>", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Armadura de cavau equipada", "subtitles.entity.horse.breathe": "Cavau respira", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.horse.eat": "Cavau manja", "subtitles.entity.horse.gallop": "Cavau que galopa", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> na<PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> sauta", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON> equipada", "subtitles.entity.husk.ambient": "Zòmbi momificat rona", "subtitles.entity.husk.converted_to_zombie": "Zòmbi momificat convertit en zòmbi", "subtitles.entity.husk.death": "Zòmbi momificat morís", "subtitles.entity.husk.hurt": "Zòmbi momificat nafrat", "subtitles.entity.illusioner.ambient": "Illusionista mormolha", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON> casts spell", "subtitles.entity.illusioner.death": "Illusionista morís", "subtitles.entity.illusioner.hurt": "Il<PERSON><PERSON><PERSON> na<PERSON>", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON> displaces", "subtitles.entity.illusioner.prepare_blindness": "Il<PERSON><PERSON> prepares blindness", "subtitles.entity.illusioner.prepare_mirror": "Il<PERSON><PERSON> prepares mirror image", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON> de <PERSON> qu'ataca", "subtitles.entity.iron_golem.damage": "Golem de fèrre se trenca", "subtitles.entity.iron_golem.death": "Golem de f<PERSON>rre mor<PERSON>", "subtitles.entity.iron_golem.hurt": "Golem de f<PERSON>rre na<PERSON>", "subtitles.entity.iron_golem.repair": "Golem de fèrre reparat", "subtitles.entity.item.break": "Objècte trenca", "subtitles.entity.item.pickup": "Obj<PERSON><PERSON><PERSON> recuperat", "subtitles.entity.item_frame.add_item": "Quadre emplit", "subtitles.entity.item_frame.break": "Quadre trenca", "subtitles.entity.item_frame.place": "Quadre plaçat", "subtitles.entity.item_frame.remove_item": "Quadre vuejat", "subtitles.entity.item_frame.rotate_item": "Clic sus un quadre", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.thunder": "Tron", "subtitles.entity.llama.ambient": "<PERSON> b<PERSON>", "subtitles.entity.llama.angry": "<PERSON> b<PERSON>la furios<PERSON>", "subtitles.entity.llama.chest": "Còfre equipat sus un lama", "subtitles.entity.llama.death": "<PERSON> m<PERSON>", "subtitles.entity.llama.eat": "Lama manja", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON> camina", "subtitles.entity.llama.swag": "Lama decorat", "subtitles.entity.magma_cube.death": "Cube de magma morís", "subtitles.entity.magma_cube.hurt": "Cube de magma nafrat", "subtitles.entity.magma_cube.squish": "Cube de magma sauta", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Vagonet que rotla", "subtitles.entity.mooshroom.convert": "Vacamparòu se tresmuda", "subtitles.entity.mooshroom.eat": "Vacamparòu manja", "subtitles.entity.mooshroom.milk": "Molzuda d'una vacamparòu", "subtitles.entity.mooshroom.suspicious_milk": "Molzuda suspècta d'una vacamparòu", "subtitles.entity.mule.ambient": "<PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON>", "subtitles.entity.mule.chest": "Còfre de mula equipat", "subtitles.entity.mule.death": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON> manja", "subtitles.entity.mule.hurt": "<PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "Pintura despenja", "subtitles.entity.painting.place": "Pintura plaçada", "subtitles.entity.panda.aggressive_ambient": "Panda huffs", "subtitles.entity.panda.ambient": "Panda pantuga", "subtitles.entity.panda.bite": "<PERSON><PERSON> mòrd", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> b<PERSON>la", "subtitles.entity.panda.death": "Panda morís", "subtitles.entity.panda.eat": "Panda manja", "subtitles.entity.panda.hurt": "Panda nafrat", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON>'s nose tickles", "subtitles.entity.panda.sneeze": "<PERSON><PERSON> sneezes", "subtitles.entity.panda.step": "Panda camina", "subtitles.entity.panda.worried_ambient": "Panda whimpers", "subtitles.entity.parrot.ambient": "<PERSON><PERSON> parla", "subtitles.entity.parrot.death": "<PERSON><PERSON> mor<PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON> manja", "subtitles.entity.parrot.fly": "<PERSON><PERSON>", "subtitles.entity.parrot.hurts": "<PERSON><PERSON> na<PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON> respira", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON> bron<PERSON>a", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON> siula", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON> gar<PERSON>ha", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON> gemega", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON> morm<PERSON>ha", "subtitles.entity.parrot.imitate.ghast": "Papagai plora", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON> gemega", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON> rena", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON> rona", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON> morm<PERSON>ha", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON> sauta", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON> sis<PERSON>la", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON> bufa", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON> bufa", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON> morm<PERSON>ha", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON> rena", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON> gacha", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON> siula", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON> sauta", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON> siula", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.parrot.imitate.vex": "Papagai vexa", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON> marmolha", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> gem<PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON> rif<PERSON>ha", "subtitles.entity.parrot.imitate.wither": "Papagai s'enràbia", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON> rena", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON> rona", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON> rona", "subtitles.entity.phantom.ambient": "Fantauma siscla", "subtitles.entity.phantom.bite": "Fantauma mòrd", "subtitles.entity.phantom.death": "Fantauma morís", "subtitles.entity.phantom.flap": "Fantauma ale<PERSON>ja", "subtitles.entity.phantom.hurt": "Fantauma nafrada", "subtitles.entity.phantom.swoop": "Phantom swoops", "subtitles.entity.pig.ambient": "Crit d'un pòrc", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.pig.saddle": "<PERSON><PERSON><PERSON> equipada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> remira un objècte", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> bufa", "subtitles.entity.piglin.angry": "<PERSON><PERSON> bufa furiosament", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> celèbra", "subtitles.entity.piglin.converted_to_zombified": "Piglin convertit en Piglin zombificat", "subtitles.entity.piglin.death": "<PERSON><PERSON> mor<PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> nafrat", "subtitles.entity.piglin.jealous": "Piglin bufa gelosament", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> fugís", "subtitles.entity.piglin.step": "Piglin camina", "subtitles.entity.piglin_brute.ambient": "<PERSON>lin ferotge bufa", "subtitles.entity.piglin_brute.angry": "<PERSON>lin ferotge bufa furiosament", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin ferotge convertit en Piglin zombificat", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> ferotge morís", "subtitles.entity.piglin_brute.hurt": "<PERSON>lin ferotge nafrat", "subtitles.entity.piglin_brute.step": "Piglin ferotge camina", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.crit": "Ataca critica", "subtitles.entity.player.attack.knockback": "Ataca rebutissenta", "subtitles.entity.player.attack.strong": "Ataca fòrta", "subtitles.entity.player.attack.sweep": "Ataca escobant", "subtitles.entity.player.attack.weak": "Ataca febla", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "Player drowning", "subtitles.entity.player.hurt_on_fire": "Player burns", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> que monta de nivèl", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "Ors polar rona", "subtitles.entity.polar_bear.ambient_baby": "Orson polar que rona", "subtitles.entity.polar_bear.death": "Ors polar morís", "subtitles.entity.polar_bear.hurt": "Ors polar nafrat", "subtitles.entity.polar_bear.warning": "<PERSON>s polar rugís", "subtitles.entity.potion.splash": "Fiòla copada", "subtitles.entity.potion.throw": "Fiòla <PERSON>", "subtitles.entity.puffer_fish.blow_out": "Pufferfish deflates", "subtitles.entity.puffer_fish.blow_up": "Pufferfish inflates", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON> g<PERSON> morís", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON><PERSON> sauteja", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON> g<PERSON> nafrat", "subtitles.entity.puffer_fish.sting": "Pufferfish stings", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> qu'ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> sauta", "subtitles.entity.ravager.ambient": "Devastator rena", "subtitles.entity.ravager.attack": "Devastator mòrd", "subtitles.entity.ravager.celebrate": "Devastator celèbra", "subtitles.entity.ravager.death": "Devastator morís", "subtitles.entity.ravager.hurt": "Devastator nafrat", "subtitles.entity.ravager.roar": "Devastator rugís", "subtitles.entity.ravager.step": "Devastator camina", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON> stunned", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON> saute<PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> que bèla", "subtitles.entity.sheep.death": "<PERSON><PERSON> mor<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON>ton nafrat", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> gacha", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> se barra", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> se dobr<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> qu'esco<PERSON><PERSON>", "subtitles.entity.shulker.teleport": "<PERSON>lk<PERSON> se telepòrta", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON> es<PERSON>a", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON> trenca", "subtitles.entity.silverfish.ambient": "Peis d'argent siula", "subtitles.entity.silverfish.death": "<PERSON><PERSON>s d'argent morís", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON>s d'argent nafrat", "subtitles.entity.skeleton.ambient": "Esqueleta claqueja", "subtitles.entity.skeleton.converted_to_stray": "Skeleton converts to Stray", "subtitles.entity.skeleton.death": "Esqueleta morís", "subtitles.entity.skeleton.hurt": "Esqueleta nafrada", "subtitles.entity.skeleton.shoot": "Esqueleta qu'escopís", "subtitles.entity.skeleton_horse.ambient": "Cavau esqueleta crida", "subtitles.entity.skeleton_horse.death": "Cavau esqueleta morís", "subtitles.entity.skeleton_horse.hurt": "Cavau esqueleta nafrat", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Cavau esqueleta nada", "subtitles.entity.slime.attack": "Slime qu'ataca", "subtitles.entity.slime.death": "Slime morís", "subtitles.entity.slime.hurt": "Slime nafrat", "subtitles.entity.slime.squish": "Slime sauta", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON><PERSON> cava", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON> se quilha", "subtitles.entity.sniffer.drop_seed": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON> de granas", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON> manja", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON><PERSON> de niflaire se fend", "subtitles.entity.sniffer.egg_hatch": "Uòu de niflaire espel<PERSON>", "subtitles.entity.sniffer.happy": "Niflaire es alègre", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON> rena", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON> flaira", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON> cerca", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON> nifla", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON> camina", "subtitles.entity.snow_golem.death": "Golem de n<PERSON>u mor<PERSON>", "subtitles.entity.snow_golem.hurt": "Golem de n<PERSON>", "subtitles.entity.snowball.throw": "Bola de nèu <PERSON>", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.ambient": "Tautena nada", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> morís", "subtitles.entity.squid.hurt": "Tautena nafrada", "subtitles.entity.squid.squirt": "Tautena que tira tencha", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "Arpentaire morís", "subtitles.entity.strider.eat": "Arpentaire manja", "subtitles.entity.strider.happy": "Strider warbles", "subtitles.entity.strider.hurt": "Arpentaire nafrat", "subtitles.entity.strider.idle": "Strider chirps", "subtitles.entity.strider.retreat": "Strider retreats", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.tadpole.flop": "Cap<PERSON><PERSON><PERSON> saute<PERSON>", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tnt.primed": "TNT amodat", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON> t<PERSON> morís", "subtitles.entity.tropical_fish.flop": "<PERSON><PERSON><PERSON> t<PERSON> sauteja", "subtitles.entity.tropical_fish.hurt": "<PERSON>eis t<PERSON>au nafrat", "subtitles.entity.turtle.ambient_land": "Turtle chirps", "subtitles.entity.turtle.death": "Tartuga morís", "subtitles.entity.turtle.death_baby": "Nenet de tartuga morís", "subtitles.entity.turtle.egg_break": "Uòu de tartuga se trenca", "subtitles.entity.turtle.egg_crack": "Uòu de tartuga se fend", "subtitles.entity.turtle.egg_hatch": "Turtle Egg hatches", "subtitles.entity.turtle.hurt": "Tartuga nafrada", "subtitles.entity.turtle.hurt_baby": "Nenet de tartuga nafrat", "subtitles.entity.turtle.lay_egg": "Turtle lays egg", "subtitles.entity.turtle.shamble": "Turtle shambles", "subtitles.entity.turtle.shamble_baby": "Baby <PERSON> shambles", "subtitles.entity.turtle.swim": "Tartuga nada", "subtitles.entity.vex.ambient": "Vex vexa", "subtitles.entity.vex.charge": "Vex que crida", "subtitles.entity.vex.death": "Vex morís", "subtitles.entity.vex.hurt": "Vex nafrat", "subtitles.entity.villager.ambient": "Vilat<PERSON>s ma<PERSON>ota", "subtitles.entity.villager.celebrate": "Vilatgés celèbra", "subtitles.entity.villager.death": "Vilat<PERSON><PERSON> mor<PERSON>", "subtitles.entity.villager.hurt": "Vilatgés nafrat", "subtitles.entity.villager.no": "Vilatgés refusa", "subtitles.entity.villager.trade": "Vilatgés escàmbia", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cartographer": "Cartografe trabalha", "subtitles.entity.villager.work_cleric": "Clergue trabalha", "subtitles.entity.villager.work_farmer": "Agricultor trab<PERSON>", "subtitles.entity.villager.work_fisherman": "Pescaire trabalha", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_librarian": "Bibliotecari trabalha", "subtitles.entity.villager.work_mason": "Maçon trabalha", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON> trabalha", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON><PERSON> d'aisinas trabalha", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON>re d'armas trabalha", "subtitles.entity.villager.yes": "Vilatgés accepta", "subtitles.entity.vindicator.ambient": "Vindicator marmolha", "subtitles.entity.vindicator.celebrate": "Vindicator celèbra", "subtitles.entity.vindicator.death": "Vindicator morís", "subtitles.entity.vindicator.hurt": "Vindicator nafrat", "subtitles.entity.wandering_trader.ambient": "Mercant ambulant marmota", "subtitles.entity.wandering_trader.death": "Mercant ambulant morís", "subtitles.entity.wandering_trader.disappeared": "Mercant ambulant desapareis", "subtitles.entity.wandering_trader.drink_milk": "Mercant ambulant beu lach", "subtitles.entity.wandering_trader.drink_potion": "Mercant ambulant beu pocion", "subtitles.entity.wandering_trader.hurt": "Mercant ambulant nafrat", "subtitles.entity.wandering_trader.no": "Mercant ambulant refusa", "subtitles.entity.wandering_trader.reappeared": "Mercant ambulant apareis", "subtitles.entity.wandering_trader.trade": "Mercant ambulant escàmbia", "subtitles.entity.wandering_trader.yes": "Mercant ambulant accepta", "subtitles.entity.warden.agitated": "Papagai rona furiosament", "subtitles.entity.warden.ambient": "Warden gem<PERSON>", "subtitles.entity.warden.angry": "Warden rages", "subtitles.entity.warden.attack_impact": "Warden lands hit", "subtitles.entity.warden.death": "Warden morís", "subtitles.entity.warden.dig": "Warden digs", "subtitles.entity.warden.emerge": "Warden emerges", "subtitles.entity.warden.heartbeat": "Warden's heart beats", "subtitles.entity.warden.hurt": "<PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.warden.listening": "Warden takes notice", "subtitles.entity.warden.listening_angry": "Warden takes notice angrily", "subtitles.entity.warden.nearby_close": "Warden approaches", "subtitles.entity.warden.nearby_closer": "Warden <PERSON>", "subtitles.entity.warden.nearby_closest": "<PERSON> draws close", "subtitles.entity.warden.roar": "<PERSON>", "subtitles.entity.warden.sniff": "<PERSON> nifla", "subtitles.entity.warden.sonic_boom": "Warden booms", "subtitles.entity.warden.sonic_charge": "Warden carga", "subtitles.entity.warden.step": "Warden camina", "subtitles.entity.warden.tendril_clicks": "Warden's tendrils click", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON> rif<PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON> mor<PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON> beu", "subtitles.entity.witch.hurt": "<PERSON><PERSON> na<PERSON>da", "subtitles.entity.witch.throw": "Masca que lança una pocion", "subtitles.entity.wither.ambient": "Wither s'enr<PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON> mor<PERSON>", "subtitles.entity.wither.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wither.shoot": "Wither qu'ataca", "subtitles.entity.wither.spawn": "Wither invocat", "subtitles.entity.wither_skeleton.ambient": "Esquelet<PERSON> wither claqueja", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON><PERSON> wither morís", "subtitles.entity.wither_skeleton.hurt": "Esquelet<PERSON> wither nafrada", "subtitles.entity.wolf.ambient": "Lop pantuga", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON><PERSON> mor<PERSON>", "subtitles.entity.wolf.growl": "Lop rena", "subtitles.entity.wolf.hurt": "<PERSON><PERSON> na<PERSON>t", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "Lop que s'esbrofa", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> rena", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> rena furiosament", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> mor<PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> nafrat", "subtitles.entity.zoglin.step": "Zoglin camina", "subtitles.entity.zombie.ambient": "Zòmbi rona", "subtitles.entity.zombie.attack_wooden_door": "Porta trembla", "subtitles.entity.zombie.break_wooden_door": "Pòrta trenca", "subtitles.entity.zombie.converted_to_drowned": "Zombie converts to Drowned", "subtitles.entity.zombie.death": "Zòmbi morís", "subtitles.entity.zombie.destroy_egg": "<PERSON> stomped", "subtitles.entity.zombie.hurt": "<PERSON>òmb<PERSON> nafrat", "subtitles.entity.zombie.infect": "<PERSON>òmbi qu'infecta", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> z<PERSON> crida", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> z<PERSON> morís", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> z<PERSON> na<PERSON>t", "subtitles.entity.zombie_villager.ambient": "Zòmbi vilatgés rona", "subtitles.entity.zombie_villager.converted": "Zombie Villager vociferates", "subtitles.entity.zombie_villager.cure": "Zombie Villager snuffles", "subtitles.entity.zombie_villager.death": "Zòmbi vilatgés morís", "subtitles.entity.zombie_villager.hurt": "Zòmbi vilatgés nafrat", "subtitles.entity.zombified_piglin.ambient": "Piglin zombificat rena", "subtitles.entity.zombified_piglin.angry": "Piglin zombificat rena furiosament", "subtitles.entity.zombified_piglin.death": "<PERSON>lin zombificat morís", "subtitles.entity.zombified_piglin.hurt": "Piglin zombificat nafrat", "subtitles.event.mob_effect.bad_omen": "Lo presagi pren fòrma", "subtitles.event.mob_effect.raid_omen": "Invasion que s'apròche", "subtitles.event.mob_effect.trial_omen": "Espròva funèsta s'apròcha", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON> bra<PERSON>", "subtitles.item.armor.equip": "Objècte equipat", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON> de malha claqueja", "subtitles.item.armor.equip_diamond": "Armadura de diamant que claca", "subtitles.item.armor.equip_elytra": "Elitres que brusisson", "subtitles.item.armor.equip_gold": "Armad<PERSON> d'aur claqueja", "subtitles.item.armor.equip_iron": "Armadura de fèrre que claca", "subtitles.item.armor.equip_leather": "Armadura de cuèr que brusís", "subtitles.item.armor.equip_netherite": "Tindadís d'armadura en netherita", "subtitles.item.armor.equip_turtle": "Clòsca de tartuga equipada", "subtitles.item.armor.equip_wolf": "Armadura per lop ligada", "subtitles.item.armor.unequip_wolf": "Armadura per lop quitada", "subtitles.item.axe.scrape": "Axe scrapes", "subtitles.item.axe.strip": "Axe strips", "subtitles.item.axe.wax_off": "Bresca raspada", "subtitles.item.bone_meal.use": "Bone Meal crinkles", "subtitles.item.book.page_turn": "<PERSON><PERSON>a brusissa", "subtitles.item.book.put": "Libre pausat", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.fill": "Fiòla que s'emplís", "subtitles.item.brush.brushing.generic": "Brushing", "subtitles.item.brush.brushing.gravel": "Brossatge de grava", "subtitles.item.brush.brushing.gravel.complete": "Brossatge de grava completat", "subtitles.item.brush.brushing.sand": "Brossatge de sabla", "subtitles.item.brush.brushing.sand.complete": "Brossatge de sabla completat", "subtitles.item.bucket.empty": "Ferrat que se vuèja", "subtitles.item.bucket.fill": "<PERSON>rrat que s'emplís", "subtitles.item.bucket.fill_axolotl": "Axolòtl capturat", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> cap<PERSON>", "subtitles.item.bucket.fill_tadpole": "Capmart<PERSON><PERSON> capturat", "subtitles.item.bundle.drop_contents": "Sac voidat", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rengat", "subtitles.item.bundle.insert_fail": "Sac plen", "subtitles.item.bundle.remove_one": "Objècte sortit", "subtitles.item.chorus_fruit.teleport": "Jo<PERSON>re se telepòrta", "subtitles.item.crop.plant": "Cultura plantada", "subtitles.item.crossbow.charge": "Crossbow charges up", "subtitles.item.crossbow.hit": "Arrow hits", "subtitles.item.crossbow.load": "Crossbow loads", "subtitles.item.crossbow.shoot": "Crossbow fires", "subtitles.item.dye.use": "Dye stains", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Bola de f<PERSON><PERSON>", "subtitles.item.flintandsteel.use": "Bat<PERSON>uòc emplegat", "subtitles.item.glow_ink_sac.use": "Glow Ink Sac splotches", "subtitles.item.goat_horn.play": "<PERSON><PERSON> plays", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON>", "subtitles.item.honey_bottle.drink": "Es a avalar", "subtitles.item.honeycomb.wax_on": "Bresca aplicada", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Ink Sac splotches", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Bossòla magnetizada se fixa", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Crop planted", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Pinçamet de cisalhas", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Parada de bloquièr", "subtitles.item.shovel.flatten": "<PERSON>la qu'a<PERSON>", "subtitles.item.spyglass.stop_using": "La pòrtavista se retracta", "subtitles.item.spyglass.use": "La pòrtavista s'estend", "subtitles.item.totem.use": "Totèm activat", "subtitles.item.trident.hit": "Trident empala", "subtitles.item.trident.hit_ground": "Trident vibra", "subtitles.item.trident.return": "Trident torna", "subtitles.item.trident.riptide": "Trident propulsa", "subtitles.item.trident.throw": "Trident es lançat", "subtitles.item.trident.thunder": "Tron de trident", "subtitles.item.wolf_armor.break": "Armadura per lop trenca", "subtitles.item.wolf_armor.crack": "Armadura per lop se fend", "subtitles.item.wolf_armor.damage": "Arm<PERSON><PERSON> per lop se degalha", "subtitles.item.wolf_armor.repair": "Armadura per lop reparada", "subtitles.particle.soul_escape": "Arma escapa", "subtitles.ui.cartography_table.take_result": "Mapa dessenhada", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "Telièr util<PERSON>", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.weather.rain": "<PERSON><PERSON><PERSON><PERSON> tomba", "symlink_warning.message": "Cargar dels monds a comptar de dossièrs contenant dels ligams simbolics pòt presentar un risc se sabètz pas exactament çò que fasètz. Se vos plai, consultatz %s per ne saber mai.", "symlink_warning.message.pack": "Cargar dels packs amb ligams simbolics pòt presentar un risc se sabètz pas exactament çò que fasètz. Se vos plai, consultatz %s per ne saber mai.", "symlink_warning.message.world": "Cargar dels monds a comptar de dossièrs contenant dels ligams simbolics pòt presentar un risc se sabètz pas exactament çò que fasètz. Se vos plai, consultatz %s per ne saber mai.", "symlink_warning.more_info": "Mai d'informacions", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "<PERSON><PERSON><PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON>", "team.collision.pushOtherTeams": "Push other teams", "team.collision.pushOwnTeam": "<PERSON><PERSON> own team", "team.notFound": "Còla desconeguda : %s", "team.visibility.always": "<PERSON><PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Hide for other teams", "team.visibility.hideForOwnTeam": "Hide for own team", "team.visibility.never": "<PERSON><PERSON>", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Optional)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "temps de cargament", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Advancement ID", "telemetry.property.client_id.title": "Client ID", "telemetry.property.client_modded.title": "<PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "Version de jòc", "telemetry.property.launcher_name.title": "Nom del lançador", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "<PERSON><PERSON>", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Operating System", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Render Distance", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Tipe del servidor", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "<PERSON>", "telemetry_info.button.privacy_statement": "Privacy Statement", "telemetry_info.button.show_data": "View My Data", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Included Data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "S'esperam que %s aja un efièch %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Mèuca de la definicion del biòme pel tèst", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit system detected: this may prevent you from playing in the future as a 64-bit system will be required!", "title.32bit.deprecation.realms": "Minecraft will soon require a 64-bit system, which will prevent you from playing or using Realms on this device. You will need to manually cancel any Realms subscription.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "32-bit system detected", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "<PERSON>jo<PERSON>re (LAN)", "title.multiplayer.other": "Multijo<PERSON>re (servidor de terces)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Un jogaire", "translation.test.args": "%s %s", "translation.test.complex": "Prefixe, %s%2$s encara %s e %1$s enfin %s e tamben %1$s encara !", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "adieu %", "translation.test.invalid2": "adiu %s", "translation.test.none": "Adieu, monde!", "translation.test.world": "monde", "trim_material.minecraft.amethyst": "En ametista", "trim_material.minecraft.copper": "En coire", "trim_material.minecraft.diamond": "<PERSON> diamant", "trim_material.minecraft.emerald": "En esmerauda", "trim_material.minecraft.gold": "En aur", "trim_material.minecraft.iron": "En fèrre", "trim_material.minecraft.lapis": "En lapislazuli", "trim_material.minecraft.netherite": "En netherita", "trim_material.minecraft.quartz": "En qüars", "trim_material.minecraft.redstone": "En redstone", "trim_material.minecraft.resin": "En resina", "trim_pattern.minecraft.bolt": "Ondradura dels bolons", "trim_pattern.minecraft.coast": "Ondradura dels litorales", "trim_pattern.minecraft.dune": "Ondradura de las dunas", "trim_pattern.minecraft.eye": "Ondradura del uèlh", "trim_pattern.minecraft.flow": "Ondradura dels revolums", "trim_pattern.minecraft.host": "Ondradura dels òstes", "trim_pattern.minecraft.raiser": "Ondradura dels elevaires", "trim_pattern.minecraft.rib": "Ondradura de las còstelas", "trim_pattern.minecraft.sentry": "Ondradura de las sentinèlas", "trim_pattern.minecraft.shaper": "Ondradura dels modelaires", "trim_pattern.minecraft.silence": "Ondradura del silenci", "trim_pattern.minecraft.snout": "Ondradura dels morres", "trim_pattern.minecraft.spire": "Ondradura de las torres", "trim_pattern.minecraft.tide": "Ondradura de las marèas", "trim_pattern.minecraft.vex": "Ondradura dels vex", "trim_pattern.minecraft.ward": "Ondradura dels abisses", "trim_pattern.minecraft.wayfinder": "Ondradura dels correires", "trim_pattern.minecraft.wild": "<PERSON><PERSON><PERSON><PERSON> salvatja", "tutorial.bundleInsert.description": "Clicatz drech per apondre objèctes", "tutorial.bundleInsert.title": "Utilizar un sac", "tutorial.craft_planks.description": "Lo receptari pòt ajudar", "tutorial.craft_planks.title": "Craft wooden planks", "tutorial.find_tree.description": "Punch it to collect wood", "tutorial.find_tree.title": "Trobatz un arbre", "tutorial.look.description": "Use your mouse to turn", "tutorial.look.title": "Look around", "tutorial.move.description": "Sautatz amb %s", "tutorial.move.title": "Desplaçatz amb %s, %s, %s e %s", "tutorial.open_inventory.description": "Picatz %s", "tutorial.open_inventory.title": "Dobrissètz vòstre inventari", "tutorial.punch_tree.description": "Hold down %s", "tutorial.punch_tree.title": "Destroy the tree", "tutorial.socialInteractions.description": "Picatz %s per obrir", "tutorial.socialInteractions.title": "Social Interactions", "upgrade.minecraft.netherite_upgrade": "Melhorament en netherita"}