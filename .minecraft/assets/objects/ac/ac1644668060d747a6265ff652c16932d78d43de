{"accessibility.onboarding.accessibility.button": "Configurazzion d'acessibilità...", "accessibility.onboarding.screen.narrator": "Schiscia \"Invio\" per pizzà el narrador", "accessibility.onboarding.screen.title": "Benvegnud in sul Minecraft!\n\nVoeulet-to pizzà el narrador o andà dent i impostazzion d'acessibilità?", "addServer.add": "Fad", "addServer.enterIp": "Indirizz del server", "addServer.enterName": "Nom del server", "addServer.resourcePack": "Pachet de resorse del server", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "Pizzad", "addServer.resourcePack.prompt": "<PERSON><PERSON>", "addServer.title": "Modifega i info del server", "advMode.command": "Comand", "advMode.mode": "Modalità", "advMode.mode.auto": "Repetizzion", "advMode.mode.autoexec.bat": "Semper pizz", "advMode.mode.conditional": "Condizzional", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Ghe voeul prejarossa", "advMode.mode.sequence": "Cadena", "advMode.mode.unconditional": "Incondizzional", "advMode.notAllowed": "Te gh'heet de vesser un operador in modalità creativa", "advMode.notEnabled": "I bloch di comand inn mìa pizz in quell server chì", "advMode.previousOutput": "Resultad de prima", "advMode.setCommand": "Imposta un comand per el bloch", "advMode.setCommand.success": "Comand impostad: %s", "advMode.trackOutput": "Vardegh adree al risultad", "advMode.triggering": "Acension", "advMode.type": "Tip", "advancement.advancementNotFound": "Progress minga conossud: %s", "advancements.adventure.adventuring_time.description": "Descovriss tucc i biom", "advancements.adventure.adventuring_time.title": "Temp de l'aventura!", "advancements.adventure.arbalistic.description": "Copa cinch creadure diferente con domà un colp de balestra", "advancements.adventure.arbalistic.title": "<PERSON> bales<PERSON>r", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON><PERSON> via vesin a un sensor sculk o a un warden per fàss minga sentì", "advancements.adventure.avoid_vibration.title": "Discrezzion 100", "advancements.adventure.blowback.description": "Copa on breeze cond on so colp de vent deviad", "advancements.adventure.blowback.title": "Contravent", "advancements.adventure.brush_armadillo.description": "O<PERSON><PERSON> le squame di un armadillo con una spazzola", "advancements.adventure.brush_armadillo.title": "Non è squarino?", "advancements.adventure.bullseye.description": "Colpiss el center de un bersai de almanca 30 meter de distanza", "advancements.adventure.bullseye.title": "Bell e ciapad!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fà su un vas decorad con quater toch de terra-cota", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON> complet", "advancements.adventure.crafters_crafting_crafters.description": "Trovati vicino a un fabbricatore mentre fabbrica un fabbricatore", "advancements.adventure.crafters_crafting_crafters.title": "Fabbricatore fabbrica fabbricatori", "advancements.adventure.fall_from_world_height.description": "Salta sgiò del pont pussee alt del mond (el limet de altezza di costruzzion) fina al fond del mond senza morì", "advancements.adventure.fall_from_world_height.title": "Grote & Scoeui", "advancements.adventure.heart_transplanter.description": "Piaza on cœr de creaking cond el liniament polid in fra dò bloc de bora de cerr slavi", "advancements.adventure.heart_transplanter.title": "Trapianto cardiaco", "advancements.adventure.hero_of_the_village.description": "Defend un paes de un'incursion", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON> del <PERSON>s", "advancements.adventure.honey_block_slide.description": "Sguja in su un bloch de mel per fà la crodada pussee moresina", "advancements.adventure.honey_block_slide.title": "Situazzion impatachenta", "advancements.adventure.kill_a_mob.description": "Copa un moster", "advancements.adventure.kill_a_mob.title": "Casciador de <PERSON>", "advancements.adventure.kill_all_mobs.description": "Copa almanca una voeulta ogne tip de moster", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Copa na creadura arent a’n catalizador de sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "El se spantega", "advancements.adventure.lighten_up.description": "Raschia una lampada di rame con un'ascia per renderla più luminosa", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protensg un paisan d'una scossa minga vorsuda senza fà pizzà un incendi", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Protetor di sora-tension", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON> piede in una camera della sfida", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Edizione della Sfida(e)", "advancements.adventure.ol_betsy.description": "Tira cont una balestra", "advancements.adventure.ol_betsy.title": "La veggia Betsy", "advancements.adventure.overoverkill.description": "Infliggi 50 cuori di danno con un colpo solo usando la mazza", "advancements.adventure.overoverkill.title": "Ammazza che mazza", "advancements.adventure.play_jukebox_in_meadows.description": "Dà vita ai prad con la musega de un sgira-disch", "advancements.adventure.play_jukebox_in_meadows.title": "El son de la musega", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Controlla la potenza del segnale di potenza di una libreria cesellata usando un comparatore", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "El poder di liber", "advancements.adventure.revaulting.description": "Sblocca una cassaforte nefasta con una chiave della sfida nefasta", "advancements.adventure.revaulting.title": "Scassaforte", "advancements.adventure.root.description": "Aventura, esplorazzion e combatiment", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Neta un bloch sospet per otegnì un toch de terra-cota", "advancements.adventure.salvage_sherd.title": "Reperti antichi", "advancements.adventure.shoot_arrow.description": "Colpiss un quaicoss cont una flizza", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON>ma mira!", "advancements.adventure.sleep_in_bed.description": "Dòrm in d'un lecc per cambià el to pont de renassida", "advancements.adventure.sleep_in_bed.title": "Bona nocc", "advancements.adventure.sniper_duel.description": "Copa un schelter de almanca 50 bloch de distanza", "advancements.adventure.sniper_duel.title": "Duell in tra cechit", "advancements.adventure.spyglass_at_dragon.description": "Varda el dragon de l'End travers de un canoccial", "advancements.adventure.spyglass_at_dragon.title": "A l'è 'n reoplan?", "advancements.adventure.spyglass_at_ghast.description": "Varda un ghast travers de un canoccial", "advancements.adventure.spyglass_at_ghast.title": "A l'è un balon?", "advancements.adventure.spyglass_at_parrot.description": "Varda un papagall travers de un canoccial", "advancements.adventure.spyglass_at_parrot.title": "A l'è un olcell?", "advancements.adventure.summon_iron_golem.description": "Fà su un golem de ferr per defender un paes", "advancements.adventure.summon_iron_golem.title": "Prima legg de la robotega", "advancements.adventure.throw_trident.description": "Tira un trident contra a un quaicoss.\nNota: trà via la toa unega arma l'è minga una bona idea.", "advancements.adventure.throw_trident.title": "Ciapa su e porta a cà", "advancements.adventure.totem_of_undying.description": "Do<PERSON>ra un totem de l'imortalità per schivà la mort", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Scambia con sucess cont un paisan", "advancements.adventure.trade.title": "Che afari!", "advancements.adventure.trade_at_world_height.description": "Scambia cont un paesan al limit de al<PERSON>zza", "advancements.adventure.trade_at_world_height.title": "Mercant de stelle", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Applica almeno una volta questi schemi di forgiatura: guglie, grugno, costole, sorvegliante, silenzio, vessante, marea, ricognitore", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON> con stil", "advancements.adventure.trim_with_any_armor_pattern.description": "Decora un'armadura in d'un tavol del ferrer", "advancements.adventure.trim_with_any_armor_pattern.title": "Un noeuv stil", "advancements.adventure.two_birds_one_arrow.description": "Copa du fantasm cont una flizza perforanta", "advancements.adventure.two_birds_one_arrow.title": "<PERSON> <PERSON>, una flizza", "advancements.adventure.under_lock_and_key.description": "Apri una cassaforte con una chiave della prova", "advancements.adventure.under_lock_and_key.title": "Sottochiave", "advancements.adventure.use_lodestone.description": "Usa una bussola su un blocco di magnetite", "advancements.adventure.use_lodestone.title": "La diritta via non è smarrita", "advancements.adventure.very_very_frightening.description": "Colpiss un paisan cont una saeta", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON> spaventos", "advancements.adventure.voluntary_exile.description": "Copa el capitan de un'incursion.\n<PERSON>si, per intanta, l'è mei stà lontan di paes...", "advancements.adventure.voluntary_exile.title": "Esili volontari", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Cammina sulla neve polverosa... senza sprofondare", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Leger 'me una piuma", "advancements.adventure.who_needs_rockets.description": "Usa una carica di vento per lanciarti in alto di 8 blocchi", "advancements.adventure.who_needs_rockets.title": "Chi ha bisogno di razzi?", "advancements.adventure.whos_the_pillager_now.description": "Dà a un botinador el savor de la so istessa medesina", "advancements.adventure.whos_the_pillager_now.title": "Chi l'è el botinador adess?", "advancements.empty": "Par che ghe sia nagot chichins<PERSON>...", "advancements.end.dragon_breath.description": "Cata su la bofada de dragon in d'una botilia", "advancements.end.dragon_breath.title": "<PERSON><PERSON> s<PERSON> i dent", "advancements.end.dragon_egg.description": "Otegn l'oeuv de dragon", "advancements.end.dragon_egg.title": "La noeuva generazzion", "advancements.end.elytra.description": "Troeuva i eliter", "advancements.end.elytra.title": "In del cel senza confin", "advancements.end.enter_end_gateway.description": "Scapa de l'isola principala", "advancements.end.enter_end_gateway.title": "Fuga a distanza", "advancements.end.find_end_city.description": "Và pur denter, cossa el podaria mai capità?", "advancements.end.find_end_city.title": "Cità a la fin del sgioeugh", "advancements.end.kill_dragon.description": "Bona fortuna", "advancements.end.kill_dragon.title": "Libera l'End", "advancements.end.levitate.description": "Levita su a 50 bloch in volt per via di atach d'un shulker", "advancements.end.levitate.title": "Bella veduda de chì su", "advancements.end.respawn_dragon.description": "Fà renass ancamò el dragon", "advancements.end.respawn_dragon.title": "L'End... ancamò...", "advancements.end.root.description": "O el principi?", "advancements.end.root.title": "L'End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fà portà a un allay una torta in su un bloch sonador", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Canzon de compleann", "advancements.husbandry.allay_deliver_item_to_player.description": "Fàt portà un lavorà de un allay", "advancements.husbandry.allay_deliver_item_to_player.title": "Te gh’heet un amis in mi", "advancements.husbandry.axolotl_in_a_bucket.description": "Branca un axolotl cont on sidell", "advancements.husbandry.axolotl_in_a_bucket.title": "El rapinador pussee atraent", "advancements.husbandry.balanced_diet.description": "Mangia su qualsessia roba che la sia comestibel, anca se la te fa minga ben", "advancements.husbandry.balanced_diet.title": "Una dieta equilibrada", "advancements.husbandry.breed_all_animals.description": "Fà cobià du animai de ogne spece!", "advancements.husbandry.breed_all_animals.title": "De du in du", "advancements.husbandry.breed_an_animal.description": "Fà cobià du animai de l'istessa spece", "advancements.husbandry.breed_an_animal.title": "L'amor a l'è in de l'aria stasira", "advancements.husbandry.complete_catalogue.description": "Domestega tute i razze de gat!", "advancements.husbandry.complete_catalogue.title": "Un gatalogh complet", "advancements.husbandry.feed_snifflet.description": "Nutri un cucciolo di fiutatore", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON> respiri", "advancements.husbandry.fishy_business.description": "Pesca un pess", "advancements.husbandry.fishy_business.title": "Grazzia per el pess", "advancements.husbandry.froglights.description": "Riva a havégh tucc i rana-lus in del to inventari", "advancements.husbandry.froglights.title": "I nost poder insema!", "advancements.husbandry.kill_axolotl_target.description": "Fà squadra con un axolotl e vensg una bataja", "advancements.husbandry.kill_axolotl_target.title": "El podè sanador de l'amicizzia!", "advancements.husbandry.leash_all_frog_variants.description": "Tagn tucc i tipi de rana cont un sguinzal", "advancements.husbandry.leash_all_frog_variants.title": "Tri salt in paes", "advancements.husbandry.make_a_sign_glow.description": "Fà sberlusì el test d'un quaisessia placard", "advancements.husbandry.make_a_sign_glow.title": "Varda che stralusc!", "advancements.husbandry.netherite_hoe.description": "Do<PERSON>ra un lingot de neterit per mejorà una zapa e poeu valuta an'mò i to scerne de vita", "advancements.husbandry.netherite_hoe.title": "Passion seria", "advancements.husbandry.obtain_sniffer_egg.description": "Otegn un oeuv de nasador", "advancements.husbandry.obtain_sniffer_egg.title": "Odora interessante", "advancements.husbandry.place_dried_ghast_in_water.description": "Piazza un ghast essiccato in acqua", "advancements.husbandry.place_dried_ghast_in_water.title": "La corretta idratazione", "advancements.husbandry.plant_any_sniffer_seed.description": "Pianta una somenza trovada del nasador", "advancements.husbandry.plant_any_sniffer_seed.title": "Piantà el passad", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON> s<PERSON> di somenze e vardai cresser", "advancements.husbandry.plant_seed.title": "I cressen inscì in pressa...", "advancements.husbandry.remove_wolf_armor.description": "Rimuovi l'armatura a un lupo con le cesoie", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON> brillante", "advancements.husbandry.repair_wolf_armor.description": "Ripara completamente un'armatura per lupi danneggiata usando le squame di armadillo", "advancements.husbandry.repair_wolf_armor.title": "Come nuovo", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Monta in barca cont una cavra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "La cavra la sa mìa nodà!", "advancements.husbandry.root.description": "El mond a l'è pien de amis e de mangià", "advancements.husbandry.root.title": "E terra... e aqua", "advancements.husbandry.safely_harvest_honey.description": "Droeuva on falò per catà mel de on rusc cont el drovà na boteja de veder senza fà inrabì i ave", "advancements.husbandry.safely_harvest_honey.title": "Te zzeet el benvegnud!", "advancements.husbandry.silk_touch_nest.description": "Sposta un bisoeul con 3 ave denter con Toch de seda", "advancements.husbandry.silk_touch_nest.title": "Bisoeul de sacocia", "advancements.husbandry.tactical_fishing.description": "Pesca un pess... senza cana de pescà!", "advancements.husbandry.tactical_fishing.title": "Pesca tatega", "advancements.husbandry.tadpole_in_a_bucket.description": "Branca una botarana cont un sidell", "advancements.husbandry.tadpole_in_a_bucket.title": "Se la rana la gh'heva la cova...", "advancements.husbandry.tame_an_animal.description": "Domestega un animal", "advancements.husbandry.tame_an_animal.title": "Amis per semper", "advancements.husbandry.wax_off.description": "Cava via la cera de un bloch de ram!", "advancements.husbandry.wax_off.title": "Cava la cera", "advancements.husbandry.wax_on.description": "Aplica un favo a un bloch de ram!", "advancements.husbandry.wax_on.title": "Met la cera", "advancements.husbandry.whole_pack.description": "Addomestica tutti i tipi di lupo", "advancements.husbandry.whole_pack.title": "Branco al completo", "advancements.nether.all_effects.description": "Apliches a ti medem tucc i efet insema", "advancements.nether.all_effects.title": "'Me che sem rivad in fina a chì?", "advancements.nether.all_potions.description": "Apliches a ti medem i efet di pozzion tucc insema", "advancements.nether.all_potions.title": "Mes'ciot esplosiv", "advancements.nether.brew_potion.description": "Pronta una pozzion", "advancements.nether.brew_potion.title": "Distilleria locala", "advancements.nether.charge_respawn_anchor.description": "Carega un'ancora de la renassida al nivell massim", "advancements.nether.charge_respawn_anchor.title": "Squas come un gat", "advancements.nether.create_beacon.description": "Fà su e piazza un far", "advancements.nether.create_beacon.title": "Pizza el ciar", "advancements.nether.create_full_beacon.description": "Dà al far piena potenza", "advancements.nether.create_full_beacon.title": "Guardian del far", "advancements.nether.distract_piglin.description": "Distrai un piglin con l'or", "advancements.nether.distract_piglin.title": "Fever de l'or", "advancements.nether.explore_nether.description": "Esplora tucc i biom del Nether", "advancements.nether.explore_nether.title": "Turism infernal", "advancements.nether.fast_travel.description": "Dopera l'infern per spostàt de 7 km in de la superfis", "advancements.nether.fast_travel.title": "Balla <PERSON>la", "advancements.nether.find_bastion.description": "Va dent in di rest d'un bastion", "advancements.nether.find_bastion.title": "Ai quei temp", "advancements.nether.find_fortress.description": "Descovriss una fortezza del Nether", "advancements.nether.find_fortress.title": "Una fortezza terribela", "advancements.nether.get_wither_skull.description": "Otegn el coo de un schelter de l'infern", "advancements.nether.get_wither_skull.title": "Schelter in de l'armadi", "advancements.nether.loot_bastion.description": "Sachegia una cesta in di rest d'un bastion", "advancements.nether.loot_bastion.title": "Porscei in guerra", "advancements.nether.netherite_armor.description": "Otegn un'armadura intrega de nederit", "advancements.nether.netherite_armor.title": "Quateem de rest antigh", "advancements.nether.obtain_ancient_debris.description": "Otegn di rest antigh", "advancements.nether.obtain_ancient_debris.title": "Scondud in profondità", "advancements.nether.obtain_blaze_rod.description": "Otegn un baston de foeugh", "advancements.nether.obtain_blaze_rod.title": "Scherzà cont el foeugh", "advancements.nether.obtain_crying_obsidian.description": "Otegn un bloch de ossidiana caragnanta", "advancements.nether.obtain_crying_obsidian.title": "Chi l'è 'dree a tajà i scigolle?", "advancements.nether.return_to_sender.description": "Destruga un ghast cont una balla de foeugh", "advancements.nether.return_to_sender.title": "Retorn al mitent", "advancements.nether.ride_strider.description": "Cavalca un strider cont un baston con fonsg desformad", "advancements.nether.ride_strider.title": "Quella barca chì la gh'ha i gambe!", "advancements.nether.ride_strider_in_overworld_lava.description": "Fà un loooongh sgir cont un strider sora un lagh de lava in de la superfiss", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON><PERSON> a cà", "advancements.nether.root.description": "Portes di vestid de estad", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Recupera un ghast in de l'infern, menel san e salv a la superfis... e poeu copel", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON> dificil", "advancements.nether.use_lodestone.description": "Dopera una bussola in su un bloch de magnetit", "advancements.nether.use_lodestone.title": "Ciapam su e portam a cà", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Indeboliss e poeu cura un paisan zombi", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Fà cambià direzzion a una balla cont el scud", "advancements.story.deflect_arrow.title": "Minga incoeu, grazzia", "advancements.story.enchant_item.description": "Instria un lavorà cont un banch de instriament", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Va denter in del portal de l'End", "advancements.story.enter_the_end.title": "La fin?", "advancements.story.enter_the_nether.description": "Fà su, pizza e traversa un portal del Nether", "advancements.story.enter_the_nether.title": "<PERSON><PERSON>ee no, chi ghe va ghe resta", "advancements.story.follow_ender_eye.description": "V<PERSON> 'dree a un oeugg de ender", "advancements.story.follow_ender_eye.title": "A la prima oggiada", "advancements.story.form_obsidian.description": "Otegn un bloch de ossidiana", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON> de dragon", "advancements.story.iron_tools.description": "Mejora el to picozz", "advancements.story.iron_tools.title": "A l'è minga on picozz de ferr?", "advancements.story.lava_bucket.description": "Impieniss un sidell con la lava", "advancements.story.lava_bucket.title": "<PERSON><PERSON> colda", "advancements.story.mine_diamond.description": "Otegn di diamant", "advancements.story.mine_diamond.title": "Diamant!", "advancements.story.mine_stone.description": "Destruga de la preja cont el to noeuv picozz", "advancements.story.mine_stone.title": "Età de la preja", "advancements.story.obtain_armor.description": "Proteges cont una part de armadura de ferr", "advancements.story.obtain_armor.title": "Un corp d'azzal", "advancements.story.root.description": "El coeur e la storia del sgioeugh", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "L'armadura de diamant la salva la vita", "advancements.story.shiny_gear.title": "<PERSON><PERSON><PERSON> de di<PERSON>", "advancements.story.smelt_iron.description": "Fònd un lingot de ferr", "advancements.story.smelt_iron.title": "Otegn i ferr del mester", "advancements.story.upgrade_tools.description": "Fà su un picozz pussee bon", "advancements.story.upgrade_tools.title": "Andà su d'un basell", "advancements.toast.challenge": "<PERSON><PERSON>da completada!", "advancements.toast.goal": "Rivad a l'obietiv!", "advancements.toast.task": "Progress fad!", "argument.anchor.invalid": "Posizzion de rampinada de l'enitità minga bona: %s", "argument.angle.incomplete": "Incomplet (ghe cala 1 canton)", "argument.angle.invalid": "Canton minga valid", "argument.block.id.invalid": "Bloch minga conossud: %s", "argument.block.property.duplicate": "La proprietà \"%s\" la po vesser impostada domà una volta per el bloch %s", "argument.block.property.invalid": "Al bloch %s a ghe va minga ben %s per la proprietà \"%s\"", "argument.block.property.novalue": "Previst un valor per la proprietà \"%s\" del bloch %s", "argument.block.property.unclosed": "A gh'è bisogn de ] a la saradura di proprietà del bloch", "argument.block.property.unknown": "El bloch %s el gh'ha minga la proprietà \"%s\"", "argument.block.tag.disallowed": "Chichinscì a inn minga permetude i etichete, ma domà di bloch ver", "argument.color.invalid": "Color minga conossud: %s", "argument.component.invalid": "Component de la ciciarada minga bon: %s", "argument.criteria.invalid": "Criteri '%s' descognossud", "argument.dimension.invalid": "Grandezza minga conossuda: %s", "argument.double.big": "El valor dobi el gh'ha mìa de vesser sora de %s (trovad %s)", "argument.double.low": "El valor dobi el gh'ha de vesser almanca de %s (trovad %s)", "argument.entity.invalid": "Nom o UUID minga bon", "argument.entity.notfound.entity": "Nanca un'entità trovada", "argument.entity.notfound.player": "Nissun sgiugador trovad", "argument.entity.options.advancements.description": "Sgiugador cont un progress determinad", "argument.entity.options.distance.description": "Distanza de l'entità", "argument.entity.options.distance.negative": "La distanza la po minga vesser negativa", "argument.entity.options.dx.description": "Entità in tra x e x + dx", "argument.entity.options.dy.description": "Entità in tra y e y + dy", "argument.entity.options.dz.description": "Entità in tra z e z + dz", "argument.entity.options.gamemode.description": "Sgiugador con modalità de sgioeugh", "argument.entity.options.inapplicable": "L'opzion \"%s\" l'è minga aplicabil chichinscì", "argument.entity.options.level.description": "Nivell de esperienza", "argument.entity.options.level.negative": "El nivell el po minga vesser negativ", "argument.entity.options.limit.description": "Numer massim de entità de dà indree", "argument.entity.options.limit.toosmall": "El valor minim del limit el gh'ha de vesser 1", "argument.entity.options.mode.invalid": "Modalità de sgioeugh minga conossuda o minga bona: %s", "argument.entity.options.name.description": "Nom de l'entità", "argument.entity.options.nbt.description": "Entità con NBT", "argument.entity.options.predicate.description": "Predicad personalizad", "argument.entity.options.scores.description": "Entità cont un pontegg", "argument.entity.options.sort.description": "Ordena i entità", "argument.entity.options.sort.irreversible": "Tipo de ordinament minga conossud o minga bon: %s", "argument.entity.options.tag.description": "Entità con eticheta", "argument.entity.options.team.description": "Entità in d'una squadra", "argument.entity.options.type.description": "Entità de un tip", "argument.entity.options.type.invalid": "Tipo di entità minga conossud o minga bon: %s", "argument.entity.options.unknown": "Opzion minga conossuda: %s", "argument.entity.options.unterminated": "Prevista la fin di opzion", "argument.entity.options.valueless": "Previst un valor per l'opzion \"%s\"", "argument.entity.options.x.description": "Posizzion x", "argument.entity.options.x_rotation.description": "Rotazzion x de l'enitità", "argument.entity.options.y.description": "Posizzion y", "argument.entity.options.y_rotation.description": "Rotazzion y de l'enitità", "argument.entity.options.z.description": "Posizzion z", "argument.entity.selector.allEntities": "Tute i entità", "argument.entity.selector.allPlayers": "Tucc i sgiugador", "argument.entity.selector.missing": "Ghe cala el tipo de seletor", "argument.entity.selector.nearestEntity": "Entità pussee arenta", "argument.entity.selector.nearestPlayer": "<PERSON>gi<PERSON><PERSON> pussee arent", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON> minga permetud", "argument.entity.selector.randomPlayer": "Sgiugador a cas", "argument.entity.selector.self": "Entità atuala", "argument.entity.selector.unknown": "Tipo de seletor minga conossud: %s", "argument.entity.toomany": "A l'è permetuda domà un'entità, ma el seletor drovad l'è bon de selezionànn pussee de voeuna", "argument.enum.invalid": "Valor \"%s\" mìa valid", "argument.float.big": "El valor float el gh'ha de vesser al massim %s, trovad%s", "argument.float.low": "El valor float el gh'hà de vesser almanch de %s, trovad %s", "argument.gamemode.invalid": "Modalità de sgioeugh mìa conossuda: %s", "argument.hexcolor.invalid": "Codice colore esadecimale '%s' non valido", "argument.id.invalid": "ID minga bon", "argument.id.unknown": "ID minga conossud: %s", "argument.integer.big": "El numer intregh el gh'ha de vesser minga magior de %s, trovad %s", "argument.integer.low": "El numer intregh el gh'hà de vesser minga minor de %s, trovad %s", "argument.item.id.invalid": "Roba minga conossuda: %s", "argument.item.tag.disallowed": "I etichete a inn minga permetude chichinscì, ma domà di oget ver", "argument.literal.incorrect": "Se speta el valor leteral \"%s\"", "argument.long.big": "El valor long el gh'ha de vesser sota de %s (trovad %s)", "argument.long.low": "El valor long el gh'ha de vesser almanca de %s (trovad %s)", "argument.message.too_long": "Messaggio in chat troppo lungo (%s > massimo %s caratteri)", "argument.nbt.array.invalid": "Tipo de vetor minga bon: %s", "argument.nbt.array.mixed": "Impossibel meter dent %s in %s", "argument.nbt.expected.compound": "Previsto un tag composto", "argument.nbt.expected.key": "Prevista ona ciav", "argument.nbt.expected.value": "<PERSON><PERSON> on valor", "argument.nbt.list.mixed": "Impossibel meter dent %s in la lista de %s", "argument.nbt.trailing": "<PERSON>t finai minga previst", "argument.player.entities": "Quell comand chì el va domà in sui sgiugador, ma el seletor fornid el po selezionà di alter entità", "argument.player.toomany": "A l'è permetud domà un sgiugador, ma el seletor dovrad en permet pussee de vun", "argument.player.unknown": "El sgiugador l'esist no", "argument.pos.missing.double": "Prevista una coordinada", "argument.pos.missing.int": "A ghe voeur la posizzion d'un bloch", "argument.pos.mixed": "Impossibel mes'cià i cooridinate locai e quei globai (tucce i gh'hann de drovà ^ o nagot)", "argument.pos.outofbounds": "Quella posizzion chì a l'è foeura di confin permetud.", "argument.pos.outofworld": "Questa posizzion chì l'è foeura de quell mond chì!", "argument.pos.unloaded": "Questa posizzion chì l'è minga caregada", "argument.pos2d.incomplete": "Incomplet (a gh'è bisogn de 2 coordinade)", "argument.pos3d.incomplete": "Incomplet (a gh'è bisgon de 3 coordinade)", "argument.range.empty": "A l'è previst on valor o un intervall de valor", "argument.range.ints": "Domà i numer intregh a inn permetud, minga quei decimai", "argument.range.swapped": "El minim el po minga vesser magior del massim", "argument.resource.invalid_type": "L'elemento «%s» è di tipo sbagliato: %s (previsto «%s»)", "argument.resource.not_found": "Impossibile trovare l'elemento «%s» di tipo «%s»", "argument.resource_or_id.failed_to_parse": "Impossibile analizzare la struttura: %s", "argument.resource_or_id.invalid": "Id o tag non valido", "argument.resource_or_id.no_such_element": "Impossibile trovare l'elemento «%s» nel registro «%s»", "argument.resource_selector.not_found": "Nessuna corrispondenza per il selettore '%s' di tipo '%s'", "argument.resource_tag.invalid_type": "Il tag «%s» è di tipo sbagliato: %s (previsto «%s»)", "argument.resource_tag.not_found": "Impossibile trovare il tag «%s» di tipo «%s»", "argument.rotation.incomplete": "Incomplet (se specen do coordinade)", "argument.scoreHolder.empty": "S'è trovad nissun marcador de pont relevant", "argument.scoreboardDisplaySlot.invalid": "Posizzion descognossuda: %s", "argument.style.invalid": "Stil mìa valid: %s", "argument.time.invalid_tick_count": "El numer de tick el gh'ha de vesser minga negativ", "argument.time.invalid_unit": "Unità minga bona", "argument.time.tick_count_too_low": "Il numero dei tick non deve essere minore di %s, ricevuto %s", "argument.uuid.invalid": "UUID minga valid", "argument.waypoint.invalid": "L'entità selezionata non è un punto di riferimento", "arguments.block.tag.unknown": "Eticheta del bloch minga conossuda: %s", "arguments.function.tag.unknown": "Eticheta de fonzion minga conossud: %s", "arguments.function.unknown": "Fonzion minga conossuda: %s", "arguments.item.component.expected": "Previsto un componente dell'oggetto", "arguments.item.component.malformed": "Componente «%s» non corretto: %s", "arguments.item.component.repeated": "Il componente dell'oggetto «%s» è stato ripetuto, ma è possibile specificare un solo valore", "arguments.item.component.unknown": "Componente dell'oggetto sconosciuto: %s", "arguments.item.malformed": "Oggetto non corretto: %s", "arguments.item.overstacked": "L'oget %s el po vesser mucciad su fina a %s", "arguments.item.predicate.malformed": "Predicato «%s» non corretto: %s", "arguments.item.predicate.unknown": "Predicato dell'oggetto sconosciuto: %s", "arguments.item.tag.unknown": "Eticheta di element minga conossuda: %s", "arguments.nbtpath.node.invalid": "Element minga bon in del percors NBT", "arguments.nbtpath.nothing_found": "A l'è stad trovad nanca un element che 'l correspond a %s", "arguments.nbtpath.too_deep": "NBT risultante troppo profondamente annidato", "arguments.nbtpath.too_large": "NBT risultante troppo grande", "arguments.objective.notFound": "Obietiv descognossud: %s", "arguments.objective.readonly": "L'obietiv %s a l'è de domà letura", "arguments.operation.div0": "Se poeul no spartì per zer", "arguments.operation.invalid": "Operazzion minga bona", "arguments.swizzle.invalid": "Ass mìa valid, gh'è bisogn de la combinazzion de \"x\", \"y\" e \"z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Dureza de l'armadura", "attribute.name.attack_damage": "<PERSON><PERSON> per colpo", "attribute.name.attack_knockback": "Contraccolpo at<PERSON>cco", "attribute.name.attack_speed": "Velocità de atach", "attribute.name.block_break_speed": "Velocità di distruzione dei blocchi", "attribute.name.block_interaction_range": "Raggio d'interazione con i blocchi", "attribute.name.burning_time": "Temp de brusà", "attribute.name.camera_distance": "Distanza dell'inquadratura", "attribute.name.entity_interaction_range": "Raggio d'interazione con le entità", "attribute.name.explosion_knockback_resistance": "Resistenza al contraccolpo delle esplosioni", "attribute.name.fall_damage_multiplier": "Moltiplicatore del danno da caduta", "attribute.name.flying_speed": "Velocità de sgol", "attribute.name.follow_range": "Raggio di rilevamento delle creature", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Durezza de l'armadura", "attribute.name.generic.attack_damage": "Dagn per colp", "attribute.name.generic.attack_knockback": "Contra-colp", "attribute.name.generic.attack_speed": "Velocità de atach", "attribute.name.generic.block_interaction_range": "Raggio d'interazione con i blocchi", "attribute.name.generic.burning_time": "Temp de brusà", "attribute.name.generic.entity_interaction_range": "Raggio d'interazione con le entità", "attribute.name.generic.explosion_knockback_resistance": "Resistenza al contraccolpo delle esplosioni", "attribute.name.generic.fall_damage_multiplier": "Moltiplicatore del danno da caduta", "attribute.name.generic.flying_speed": "Velocità de sgol", "attribute.name.generic.follow_range": "Distanza rilevament creadure", "attribute.name.generic.gravity": "Gravità", "attribute.name.generic.jump_strength": "Forza del salt", "attribute.name.generic.knockback_resistance": "Resistenza al contra-colp", "attribute.name.generic.luck": "Fortuna", "attribute.name.generic.max_absorption": "Assorbimento massimo", "attribute.name.generic.max_health": "Salut massima", "attribute.name.generic.movement_efficiency": "Efficienza del movimento", "attribute.name.generic.movement_speed": "Velocità", "attribute.name.generic.oxygen_bonus": "Ossigeno aggiuntivo", "attribute.name.generic.safe_fall_distance": "Distanza di caduta sicura", "attribute.name.generic.scale": "Scala", "attribute.name.generic.step_height": "Altezza del passo", "attribute.name.generic.water_movement_efficiency": "Efficienza del movimento in acqua", "attribute.name.gravity": "Gravitaa", "attribute.name.horse.jump_strength": "Forza de salt del cavall", "attribute.name.jump_strength": "Forza del salto", "attribute.name.knockback_resistance": "Resistenza al contraccolpo", "attribute.name.luck": "Fortuna", "attribute.name.max_absorption": "Assorbimento massimo", "attribute.name.max_health": "Salut massima", "attribute.name.mining_efficiency": "Efficienza d'estrazione", "attribute.name.movement_efficiency": "Efficienza del movimento", "attribute.name.movement_speed": "Velocità", "attribute.name.oxygen_bonus": "Ossigeno aggiuntivo", "attribute.name.player.block_break_speed": "Velocità di distruzione dei blocchi", "attribute.name.player.block_interaction_range": "Raggio d'interazione con i blocchi", "attribute.name.player.entity_interaction_range": "Raggio d'interazione con le entità", "attribute.name.player.mining_efficiency": "Efficienza d'estrazione", "attribute.name.player.sneaking_speed": "Velocità furtiva", "attribute.name.player.submerged_mining_speed": "Velocità d'estrazione sott'acqua", "attribute.name.player.sweeping_damage_ratio": "Rapporto del danno sferzante", "attribute.name.safe_fall_distance": "Distanza di caduta sicura", "attribute.name.scale": "Scala", "attribute.name.sneaking_speed": "Velocità furtiva", "attribute.name.spawn_reinforcements": "Rinforzi zombi", "attribute.name.step_height": "Altezza del passo", "attribute.name.submerged_mining_speed": "Velocità d'estrazione sott'acqua", "attribute.name.sweeping_damage_ratio": "Rapporto del danno sferzante", "attribute.name.tempt_range": "Raggio di tentazione delle creature", "attribute.name.water_movement_efficiency": "Efficienza del movimento in acqua", "attribute.name.waypoint_receive_range": "Portata di ricezione del punto di riferimento", "attribute.name.waypoint_transmit_range": "Portata di trasmissione del punto di riferimento", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON>", "biome.minecraft.badlands": "Desert rocios", "biome.minecraft.bamboo_jungle": "Sgiungla de bamboo", "biome.minecraft.basalt_deltas": "Delta de basalt", "biome.minecraft.beach": "Spiasgia", "biome.minecraft.birch_forest": "Bosch de biolle", "biome.minecraft.cherry_grove": "<PERSON><PERSON>", "biome.minecraft.cold_ocean": "Ocean fregg", "biome.minecraft.crimson_forest": "Bosch cremes", "biome.minecraft.dark_forest": "<PERSON><PERSON> negher", "biome.minecraft.deep_cold_ocean": "Ocean fregg fond", "biome.minecraft.deep_dark": "<PERSON><PERSON> scurent", "biome.minecraft.deep_frozen_ocean": "<PERSON> sgelad fond", "biome.minecraft.deep_lukewarm_ocean": "Ocean teved fond", "biome.minecraft.deep_ocean": "Ocean fond", "biome.minecraft.desert": "Desert", "biome.minecraft.dripstone_caves": "Caverne di speleotema", "biome.minecraft.end_barrens": "Desert de l'End", "biome.minecraft.end_highlands": "Zona volta de l'End", "biome.minecraft.end_midlands": "Zona media de l'End", "biome.minecraft.eroded_badlands": "Desert de rocia rosegad", "biome.minecraft.flower_forest": "Bosch di fior", "biome.minecraft.forest": "<PERSON><PERSON>", "biome.minecraft.frozen_ocean": "Ocean sgelad", "biome.minecraft.frozen_peaks": "<PERSON><PERSON> giazzade", "biome.minecraft.frozen_river": "<PERSON>um s<PERSON>ad", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Scime de giazz", "biome.minecraft.jagged_peaks": "Cime resegade", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Ocean teved", "biome.minecraft.lush_caves": "Caverne coi piante", "biome.minecraft.mangrove_swamp": "Sciatera de mangrovie", "biome.minecraft.meadow": "Brugher<PERSON>", "biome.minecraft.mushroom_fields": "Camp de fonsg", "biome.minecraft.nether_wastes": "Desert del Nether", "biome.minecraft.ocean": "Ocean", "biome.minecraft.old_growth_birch_forest": "Bosch di biolle vegge veggente", "biome.minecraft.old_growth_pine_taiga": "Taiga de pin vegg veggent", "biome.minecraft.old_growth_spruce_taiga": "Taiga de abiezz vegg veggent", "biome.minecraft.pale_garden": "Giardino pallido", "biome.minecraft.plains": "<PERSON><PERSON>", "biome.minecraft.river": "<PERSON><PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "Isole piscinine de l'End", "biome.minecraft.snowy_beach": "Spiasgia con nev", "biome.minecraft.snowy_plains": "Pianura de nev", "biome.minecraft.snowy_slopes": "Calada de nev", "biome.minecraft.snowy_taiga": "Taiga nevosa", "biome.minecraft.soul_sand_valley": "Vall di anime", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON><PERSON> rara", "biome.minecraft.stony_peaks": "Cime de preja", "biome.minecraft.stony_shore": "Costa de preja", "biome.minecraft.sunflower_plains": "Piana di sgira-sol", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "L'End", "biome.minecraft.the_void": "El voeui", "biome.minecraft.warm_ocean": "Ocean cold", "biome.minecraft.warped_forest": "Bosch desformad", "biome.minecraft.windswept_forest": "<PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "Colline de gera vent<PERSON>", "biome.minecraft.windswept_hills": "<PERSON><PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON> ventosa", "biome.minecraft.wooded_badlands": "Desert de rocia boschent", "block.minecraft.acacia_button": "<PERSON><PERSON> de rubinia", "block.minecraft.acacia_door": "Porta de rubinia", "block.minecraft.acacia_fence": "Stongarda de rubinia", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON> de rub<PERSON>", "block.minecraft.acacia_hanging_sign": "Insegna de rubinia", "block.minecraft.acacia_leaves": "Foeuje de rubinia", "block.minecraft.acacia_log": "Bora de rubinia", "block.minecraft.acacia_planks": "Ass de rubinia", "block.minecraft.acacia_pressure_plate": "Pian a pression de rubinia", "block.minecraft.acacia_sapling": "But de rubinia", "block.minecraft.acacia_sign": "<PERSON>tell de rubinia", "block.minecraft.acacia_slab": "Pioda de rubinia", "block.minecraft.acacia_stairs": "Basei de rubinia", "block.minecraft.acacia_trapdoor": "Bussera de rubinia", "block.minecraft.acacia_wall_hanging_sign": "Insegna a mur de rubinia", "block.minecraft.acacia_wall_sign": "<PERSON><PERSON><PERSON> de mur de rubinia", "block.minecraft.acacia_wood": "Legn de rubinia", "block.minecraft.activator_rail": "Binari pizzador", "block.minecraft.air": "Aria", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Bloch de ametista", "block.minecraft.amethyst_cluster": "G<PERSON>ell de ametista", "block.minecraft.ancient_debris": "Rest antigh", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Pioda de andesit", "block.minecraft.andesite_stairs": "Basei de andesit", "block.minecraft.andesite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.anvil": "Incusgen", "block.minecraft.attached_melon_stem": "Pianta de inguria madura", "block.minecraft.attached_pumpkin_stem": "Pianta de zuca madura", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Foeuje de azalea", "block.minecraft.azure_bluet": "Houstonia", "block.minecraft.bamboo": "Bamboo", "block.minecraft.bamboo_block": "<PERSON>h de <PERSON>", "block.minecraft.bamboo_button": "Boton de bamboo", "block.minecraft.bamboo_door": "Porta de bamboo", "block.minecraft.bamboo_fence": "Stongarda de bamboo", "block.minecraft.bamboo_fence_gate": "<PERSON>ell de <PERSON>", "block.minecraft.bamboo_hanging_sign": "Insegna de bamboo", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic_slab": "Pioda de mosaegh de bamboo", "block.minecraft.bamboo_mosaic_stairs": "Basei de mosaegh de bamboo", "block.minecraft.bamboo_planks": "Ass de bamboo", "block.minecraft.bamboo_pressure_plate": "Pian a pression de bamboo", "block.minecraft.bamboo_sapling": "But de bamboo", "block.minecraft.bamboo_sign": "Cartell de bamboo", "block.minecraft.bamboo_slab": "Pioda de bamboo", "block.minecraft.bamboo_stairs": "Basei de bamboo", "block.minecraft.bamboo_trapdoor": "Bussera de bamboo", "block.minecraft.bamboo_wall_hanging_sign": "Insegna a mur de bamboo", "block.minecraft.bamboo_wall_sign": "Cartell de mur de bamboo", "block.minecraft.banner.base.black": "Camp negher", "block.minecraft.banner.base.blue": "Camp bleou", "block.minecraft.banner.base.brown": "Camp marron", "block.minecraft.banner.base.cyan": "Camp cian", "block.minecraft.banner.base.gray": "Camp gris", "block.minecraft.banner.base.green": "Camp verd", "block.minecraft.banner.base.light_blue": "Camp bloeu ciar", "block.minecraft.banner.base.light_gray": "Camp gris ciar", "block.minecraft.banner.base.lime": "Camp lime", "block.minecraft.banner.base.magenta": "Camp magenta", "block.minecraft.banner.base.orange": "Camp naranz", "block.minecraft.banner.base.pink": "Camp roeusa", "block.minecraft.banner.base.purple": "Camp vioeula", "block.minecraft.banner.base.red": "Camp ross", "block.minecraft.banner.base.white": "Camp bianch", "block.minecraft.banner.base.yellow": "Camp sgiald", "block.minecraft.banner.border.black": "Bordura negra", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> marrona", "block.minecraft.banner.border.cyan": "Bordura ciana", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON> gris ciar", "block.minecraft.banner.border.lime": "Bordura lime", "block.minecraft.banner.border.magenta": "Bordura magenta", "block.minecraft.banner.border.orange": "Bordura naranza", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.border.purple": "Bordura vioeula", "block.minecraft.banner.border.red": "Bordura rossa", "block.minecraft.banner.border.white": "Bordura bianca", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON> s<PERSON>lda", "block.minecraft.banner.bricks.black": "Camp de quadrei negher", "block.minecraft.banner.bricks.blue": "Camp de quadrei bloeu", "block.minecraft.banner.bricks.brown": "Camp de quadrei marron", "block.minecraft.banner.bricks.cyan": "Camp de quadrei celest", "block.minecraft.banner.bricks.gray": "Camp de quadrei gris", "block.minecraft.banner.bricks.green": "Camp de quadrei verd", "block.minecraft.banner.bricks.light_blue": "Camp de quadrei bloeu ciar", "block.minecraft.banner.bricks.light_gray": "Camp de quadrei gris ciar", "block.minecraft.banner.bricks.lime": "Camp de quadrei lime", "block.minecraft.banner.bricks.magenta": "Camp de quadrei magenta", "block.minecraft.banner.bricks.orange": "Camp de quadrei naranz", "block.minecraft.banner.bricks.pink": "Camp de quadrei roeusa", "block.minecraft.banner.bricks.purple": "Camp de quadrei vioeula", "block.minecraft.banner.bricks.red": "Camp de quadrei ross", "block.minecraft.banner.bricks.white": "Camp de quadrei bianch", "block.minecraft.banner.bricks.yellow": "Camp de quadrei sgiald", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON> negher", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON> marron", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>t celest", "block.minecraft.banner.circle.gray": "Sercet gris", "block.minecraft.banner.circle.green": "Sercet verd", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar", "block.minecraft.banner.circle.light_gray": "Sercet gris ciar", "block.minecraft.banner.circle.lime": "Sercet lime", "block.minecraft.banner.circle.magenta": "Sercet magenta", "block.minecraft.banner.circle.orange": "Sercet naranz", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.circle.purple": "Sercet vioeula", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON> ross", "block.minecraft.banner.circle.white": "<PERSON>cet bianch", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON> s<PERSON>ld", "block.minecraft.banner.creeper.black": "Faza de creeper negra", "block.minecraft.banner.creeper.blue": "Faza de creeper bloeu", "block.minecraft.banner.creeper.brown": "Faza de creeper marrona", "block.minecraft.banner.creeper.cyan": "Faza de creeper celest", "block.minecraft.banner.creeper.gray": "<PERSON>aza de creeper grisa", "block.minecraft.banner.creeper.green": "Faza de creeper verda", "block.minecraft.banner.creeper.light_blue": "Faza de creeper bloeu ciar", "block.minecraft.banner.creeper.light_gray": "<PERSON>aza de creeper grisa ciar", "block.minecraft.banner.creeper.lime": "Faza de creeper lime", "block.minecraft.banner.creeper.magenta": "Faza de creeper magenta", "block.minecraft.banner.creeper.orange": "Faza de creeper naranza", "block.minecraft.banner.creeper.pink": "Faza de creeper roeusa", "block.minecraft.banner.creeper.purple": "Faza de creeper vioeula", "block.minecraft.banner.creeper.red": "Faza de creeper rossa", "block.minecraft.banner.creeper.white": "Faza da creeper bianca", "block.minecraft.banner.creeper.yellow": "<PERSON>aza de creeper sgialda", "block.minecraft.banner.cross.black": "Cros de Sant Andrea negra", "block.minecraft.banner.cross.blue": "Cros de Sant <PERSON> b<PERSON>", "block.minecraft.banner.cross.brown": "Cros de <PERSON>", "block.minecraft.banner.cross.cyan": "Cros de Sant Andrea celesta", "block.minecraft.banner.cross.gray": "Cros de Sant Andrea g<PERSON>", "block.minecraft.banner.cross.green": "<PERSON>ros de Andrea verda", "block.minecraft.banner.cross.light_blue": "<PERSON>ros de Sant <PERSON> blo<PERSON> ciar", "block.minecraft.banner.cross.light_gray": "Cros de Sant Andrea grisa ciar", "block.minecraft.banner.cross.lime": "Cros de Sant Andrea lime", "block.minecraft.banner.cross.magenta": "Cros de Sant Andrea magenta", "block.minecraft.banner.cross.orange": "Cros de Sant Andrea <PERSON>", "block.minecraft.banner.cross.pink": "Cros de Sant Andrea roeusa", "block.minecraft.banner.cross.purple": "Cros de Sant Andrea vio<PERSON>la", "block.minecraft.banner.cross.red": "Cros de Sant Andrea rossa", "block.minecraft.banner.cross.white": "Cros de Sant Andrea bi<PERSON>ca", "block.minecraft.banner.cross.yellow": "Cros de Sant Andrea s<PERSON>lda", "block.minecraft.banner.curly_border.black": "Bordura negra dentada", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> blo<PERSON> den<PERSON>a", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> marrona dentada", "block.minecraft.banner.curly_border.cyan": "Bordura ciana dentada", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> grisa dentada", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> verda dentada", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar dentada", "block.minecraft.banner.curly_border.light_gray": "Bordura gris ciar dentada", "block.minecraft.banner.curly_border.lime": "Bordura lime dentada", "block.minecraft.banner.curly_border.magenta": "Bordura magenta dentada", "block.minecraft.banner.curly_border.orange": "Bordura naranza dentada", "block.minecraft.banner.curly_border.pink": "<PERSON>rd<PERSON> roeusa dentada", "block.minecraft.banner.curly_border.purple": "Bordura vioeula dentada", "block.minecraft.banner.curly_border.red": "Bordura rossa dentada", "block.minecraft.banner.curly_border.white": "Bordura bianca dentada", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> s<PERSON>lda dentada", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON> celest", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> verd", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON> gris ciar", "block.minecraft.banner.diagonal_left.lime": "Tajad lime", "block.minecraft.banner.diagonal_left.magenta": "Tajad magenta", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON> ross", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> bi<PERSON>ch", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON><PERSON>gher", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON> marron", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON><PERSON> celest", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON> verd", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> gris ciar", "block.minecraft.banner.diagonal_right.lime": "Stretajad lime", "block.minecraft.banner.diagonal_right.magenta": "Stretajad magenta", "block.minecraft.banner.diagonal_right.orange": "Stretajad <PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON> roe<PERSON>", "block.minecraft.banner.diagonal_right.purple": "Stretajad v<PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON><PERSON> ross", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON><PERSON> bianch", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON><PERSON> negher a l'invers", "block.minecraft.banner.diagonal_up_left.blue": "Stretajad blo<PERSON> a l'invers", "block.minecraft.banner.diagonal_up_left.brown": "Stretajad marron a l'invers", "block.minecraft.banner.diagonal_up_left.cyan": "Stretajad celest a l'invers", "block.minecraft.banner.diagonal_up_left.gray": "Stretajad gris a l'invers", "block.minecraft.banner.diagonal_up_left.green": "Stretajad verd a l'invers", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON><PERSON> bloeu ciar a l'invers", "block.minecraft.banner.diagonal_up_left.light_gray": "Stretajad gris ciar a l'invers", "block.minecraft.banner.diagonal_up_left.lime": "Stretajad lime a l'invers", "block.minecraft.banner.diagonal_up_left.magenta": "Stretajad magenta a l'invers", "block.minecraft.banner.diagonal_up_left.orange": "Stretajad naranz a l'invers", "block.minecraft.banner.diagonal_up_left.pink": "Stretajad roeusa a l'invers", "block.minecraft.banner.diagonal_up_left.purple": "Stretajad vioeula a l'invers", "block.minecraft.banner.diagonal_up_left.red": "St<PERSON><PERSON><PERSON> ross a l'invers", "block.minecraft.banner.diagonal_up_left.white": "Stretajad bianch a l'invers", "block.minecraft.banner.diagonal_up_left.yellow": "Stretajad sgiald a l'invers", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON> negher a l'invers", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON> blo<PERSON> a l'invers", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> marron a l'invers", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON> celest a l'invers", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON>d gris a l'invers", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON> verd a l'invers", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON> bloeu c<PERSON> a l'invers", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON><PERSON> gris ciar a l'invers", "block.minecraft.banner.diagonal_up_right.lime": "Tajad lime a l'invers", "block.minecraft.banner.diagonal_up_right.magenta": "Tajad magenta a l'invers", "block.minecraft.banner.diagonal_up_right.orange": "Tajad na<PERSON> a l'invers", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON> roe<PERSON> a l'invers", "block.minecraft.banner.diagonal_up_right.purple": "Tajad vio<PERSON>la a l'invers", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON> ross a l'invers", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> bianch a l'invers", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> s<PERSON>ld a l'invers", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> nero", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON> blu", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON> marrone", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON> ciano", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> g<PERSON>io", "block.minecraft.banner.flow.green": "Flusso verde", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> grigio chiaro", "block.minecraft.banner.flow.lime": "Flusso lime", "block.minecraft.banner.flow.magenta": "Flusso magenta", "block.minecraft.banner.flow.orange": "Flusso a<PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.flow.purple": "<PERSON><PERSON><PERSON> viola", "block.minecraft.banner.flow.red": "Flusso rosso", "block.minecraft.banner.flow.white": "Flusso bianco", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.flower.black": "<PERSON><PERSON>", "block.minecraft.banner.flower.blue": "<PERSON><PERSON> bloeu", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> ma<PERSON>n", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.flower.green": "<PERSON><PERSON> verd", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON> bloeu ciar", "block.minecraft.banner.flower.light_gray": "<PERSON>or gris ciar", "block.minecraft.banner.flower.lime": "Fior lime", "block.minecraft.banner.flower.magenta": "Fior magenta", "block.minecraft.banner.flower.orange": "<PERSON><PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flower.red": "<PERSON>or ross", "block.minecraft.banner.flower.white": "<PERSON><PERSON> bianch", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON>", "block.minecraft.banner.globe.black": "<PERSON><PERSON><PERSON> negher", "block.minecraft.banner.globe.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.globe.brown": "<PERSON><PERSON><PERSON> marron", "block.minecraft.banner.globe.cyan": "Pianeda cian", "block.minecraft.banner.globe.gray": "Pianeda gris", "block.minecraft.banner.globe.green": "Pianeda verd", "block.minecraft.banner.globe.light_blue": "Pi<PERSON><PERSON> bloeu ciar", "block.minecraft.banner.globe.light_gray": "Pianeda gris ciar", "block.minecraft.banner.globe.lime": "Pianeda lime", "block.minecraft.banner.globe.magenta": "Pianeda magenta", "block.minecraft.banner.globe.orange": "Pianeda naranz", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.globe.purple": "Pianeda vioeula", "block.minecraft.banner.globe.red": "Pi<PERSON><PERSON> ross", "block.minecraft.banner.globe.white": "Pianeda bianch", "block.minecraft.banner.globe.yellow": "Pi<PERSON><PERSON> s<PERSON>ld", "block.minecraft.banner.gradient.black": "Sfumad<PERSON> negra", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON> marrona", "block.minecraft.banner.gradient.cyan": "Sfumadura celesta", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON> c<PERSON>", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON><PERSON> gris ciar", "block.minecraft.banner.gradient.lime": "Sfumadura lime", "block.minecraft.banner.gradient.magenta": "Sfumadura magenta", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.gradient.purple": "Sfumad<PERSON> vioeula", "block.minecraft.banner.gradient.red": "Sfumadura rossa", "block.minecraft.banner.gradient.white": "Sfumadura bianca", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>lda", "block.minecraft.banner.gradient_up.black": "Sfumadura negra a la bas", "block.minecraft.banner.gradient_up.blue": "Sfumadura bloeu a la bas", "block.minecraft.banner.gradient_up.brown": "Sfumadura marrona a la bas", "block.minecraft.banner.gradient_up.cyan": "Sfumadura cian a la bas", "block.minecraft.banner.gradient_up.gray": "Sfumadura grisa a la bas", "block.minecraft.banner.gradient_up.green": "Sfumadura verda a la bas", "block.minecraft.banner.gradient_up.light_blue": "Sfumadura bloeu ciar a la bas", "block.minecraft.banner.gradient_up.light_gray": "Sfumadura gris ciar a la bas", "block.minecraft.banner.gradient_up.lime": "Sfumadura lime a la bas", "block.minecraft.banner.gradient_up.magenta": "Sfumadura magenta a la bas", "block.minecraft.banner.gradient_up.orange": "Sfumadura naranza a la bas", "block.minecraft.banner.gradient_up.pink": "Sfumadura roeusa a la bas", "block.minecraft.banner.gradient_up.purple": "Sfumadura vioeula a la bas", "block.minecraft.banner.gradient_up.red": "Sfumadura rossa a la bas", "block.minecraft.banner.gradient_up.white": "Sfumadura bianca a la bas", "block.minecraft.banner.gradient_up.yellow": "Sfumadura sgialda a la bas", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON>ra", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON> blu", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> marrone", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON> grigia", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON> grigio chiaro", "block.minecraft.banner.guster.lime": "Raffica lime", "block.minecraft.banner.guster.magenta": "Raffica magenta", "block.minecraft.banner.guster.orange": "Raffica a<PERSON>", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON> viola", "block.minecraft.banner.guster.red": "Raffica rossa", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> bianca", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> gialla", "block.minecraft.banner.half_horizontal.black": "S'cioncad negher", "block.minecraft.banner.half_horizontal.blue": "S'cioncad bloeu", "block.minecraft.banner.half_horizontal.brown": "S'cioncad marron", "block.minecraft.banner.half_horizontal.cyan": "S'cioncad celest", "block.minecraft.banner.half_horizontal.gray": "S'cioncad gris", "block.minecraft.banner.half_horizontal.green": "S'cioncad verd", "block.minecraft.banner.half_horizontal.light_blue": "S'cioncad bloeu ciar", "block.minecraft.banner.half_horizontal.light_gray": "S'cioncad gris ciar", "block.minecraft.banner.half_horizontal.lime": "S'cioncad lime", "block.minecraft.banner.half_horizontal.magenta": "S'cioncad magenta", "block.minecraft.banner.half_horizontal.orange": "S'cioncad naranz", "block.minecraft.banner.half_horizontal.pink": "S'cioncad roeusa", "block.minecraft.banner.half_horizontal.purple": "S'cioncad vioeula", "block.minecraft.banner.half_horizontal.red": "S'cioncad ross", "block.minecraft.banner.half_horizontal.white": "S'cioncad bianch", "block.minecraft.banner.half_horizontal.yellow": "S'cioncad sgiald", "block.minecraft.banner.half_horizontal_bottom.black": "S'cioncad negher a l'invers", "block.minecraft.banner.half_horizontal_bottom.blue": "S'cioncad bloeu a l'invers", "block.minecraft.banner.half_horizontal_bottom.brown": "S'cioncad marron a l'invers", "block.minecraft.banner.half_horizontal_bottom.cyan": "S'cioncad celest a l'invers", "block.minecraft.banner.half_horizontal_bottom.gray": "S'cioncad gris a l'invers", "block.minecraft.banner.half_horizontal_bottom.green": "S'cioncad verd a l'invers", "block.minecraft.banner.half_horizontal_bottom.light_blue": "S'cioncad bloeu ciar a l'invers", "block.minecraft.banner.half_horizontal_bottom.light_gray": "S'cioncad gris ciar a l'invers", "block.minecraft.banner.half_horizontal_bottom.lime": "S'cioncad lime a l'invers", "block.minecraft.banner.half_horizontal_bottom.magenta": "S'cioncad magenta a l'invers", "block.minecraft.banner.half_horizontal_bottom.orange": "S'cioncad naranz a l'invers", "block.minecraft.banner.half_horizontal_bottom.pink": "S'cioncad roeusa a l'invers", "block.minecraft.banner.half_horizontal_bottom.purple": "S'cioncad vioeula a l'invers", "block.minecraft.banner.half_horizontal_bottom.red": "S'cioncad ross a l'invers", "block.minecraft.banner.half_horizontal_bottom.white": "S'cioncad bianch a l'invers", "block.minecraft.banner.half_horizontal_bottom.yellow": "S'cioncad sgiald a l'invers", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON> negher", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON> marron", "block.minecraft.banner.half_vertical.cyan": "Partid celest", "block.minecraft.banner.half_vertical.gray": "Partid gris", "block.minecraft.banner.half_vertical.green": "Partid verd", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON> bloeu ciar", "block.minecraft.banner.half_vertical.light_gray": "Partid gris ciar", "block.minecraft.banner.half_vertical.lime": "Partid lime", "block.minecraft.banner.half_vertical.magenta": "Partid magenta", "block.minecraft.banner.half_vertical.orange": "Partid naranz", "block.minecraft.banner.half_vertical.pink": "Partid roeusa", "block.minecraft.banner.half_vertical.purple": "Partid vioeula", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON> ross", "block.minecraft.banner.half_vertical.white": "<PERSON>id bianch", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON> s<PERSON>ld", "block.minecraft.banner.half_vertical_right.black": "Partid negher a l'invers", "block.minecraft.banner.half_vertical_right.blue": "Partid bloeu a l'invers", "block.minecraft.banner.half_vertical_right.brown": "Partid marron a l'invers", "block.minecraft.banner.half_vertical_right.cyan": "Partid celest a l'invers", "block.minecraft.banner.half_vertical_right.gray": "Partid gris a l'invers", "block.minecraft.banner.half_vertical_right.green": "Partid verd a l'invers", "block.minecraft.banner.half_vertical_right.light_blue": "Partid bloeu a l'invers", "block.minecraft.banner.half_vertical_right.light_gray": "Partid gris ciar a l'invers", "block.minecraft.banner.half_vertical_right.lime": "Partid lime a l'invers", "block.minecraft.banner.half_vertical_right.magenta": "Partid magenta a l'invers", "block.minecraft.banner.half_vertical_right.orange": "Partid naranz a l'invers", "block.minecraft.banner.half_vertical_right.pink": "Partid roeusa a l'invers", "block.minecraft.banner.half_vertical_right.purple": "Partid vioeula a l'invers", "block.minecraft.banner.half_vertical_right.red": "Partid ross a l'invers", "block.minecraft.banner.half_vertical_right.white": "Partid bianch a l'invers", "block.minecraft.banner.half_vertical_right.yellow": "Partid sgiald a l'invers", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON> negher", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.mojang.brown": "<PERSON>eugh marron", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.mojang.gray": "Loeugh gris", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON> verd", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar", "block.minecraft.banner.mojang.light_gray": "Loeugh gris ciar", "block.minecraft.banner.mojang.lime": "Loeugh lime", "block.minecraft.banner.mojang.magenta": "Loeugh magenta", "block.minecraft.banner.mojang.orange": "Loeugh naranz", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.mojang.purple": "Loeugh vioeula", "block.minecraft.banner.mojang.red": "Loeugh ross", "block.minecraft.banner.mojang.white": "Loeugh bianch", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON> s<PERSON>ld", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON> negher", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON> marron", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON>n gris", "block.minecraft.banner.piglin.green": "<PERSON><PERSON>n verd", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar", "block.minecraft.banner.piglin.light_gray": "<PERSON>rugn gris ciar", "block.minecraft.banner.piglin.lime": "Grugn lime", "block.minecraft.banner.piglin.magenta": "Grugn magenta", "block.minecraft.banner.piglin.orange": "Grugn naranz", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.piglin.purple": "Grugn vioeula", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON> ross", "block.minecraft.banner.piglin.white": "Grugn bianch", "block.minecraft.banner.piglin.yellow": "Grugn s<PERSON>ld", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON> negher", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON> bloeu", "block.minecraft.banner.rhombus.brown": "Romb marron", "block.minecraft.banner.rhombus.cyan": "Romb celest", "block.minecraft.banner.rhombus.gray": "Romb gris", "block.minecraft.banner.rhombus.green": "Romb verd", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON> bloeu ciar", "block.minecraft.banner.rhombus.light_gray": "Romb gris ciar", "block.minecraft.banner.rhombus.lime": "Romb lime", "block.minecraft.banner.rhombus.magenta": "Romb magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON> naranz", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON> roeusa", "block.minecraft.banner.rhombus.purple": "<PERSON>omb vioeula", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON> ross", "block.minecraft.banner.rhombus.white": "<PERSON>omb bianch", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON> sgiald", "block.minecraft.banner.skull.black": "<PERSON><PERSON> negher", "block.minecraft.banner.skull.blue": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> marron", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.skull.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.skull.green": "<PERSON><PERSON> verd", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON> bloeu ciar", "block.minecraft.banner.skull.light_gray": "<PERSON>rani gris ciar", "block.minecraft.banner.skull.lime": "Crani lime", "block.minecraft.banner.skull.magenta": "Crani magenta", "block.minecraft.banner.skull.orange": "Crani naranz", "block.minecraft.banner.skull.pink": "<PERSON><PERSON> roeusa", "block.minecraft.banner.skull.purple": "<PERSON><PERSON> vioeula", "block.minecraft.banner.skull.red": "<PERSON><PERSON> ross", "block.minecraft.banner.skull.white": "<PERSON><PERSON> bianch", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON> negher", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON> marron", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON> celest", "block.minecraft.banner.small_stripes.gray": "<PERSON>lad gris", "block.minecraft.banner.small_stripes.green": "Palad verd", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON> bloeu ciar", "block.minecraft.banner.small_stripes.light_gray": "Palad gris ciar", "block.minecraft.banner.small_stripes.lime": "Palad lime", "block.minecraft.banner.small_stripes.magenta": "Palad magenta", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON> roeusa", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON> ross", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON> bianch", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON>ld", "block.minecraft.banner.square_bottom_left.black": "Canton drizz negher de la guzza", "block.minecraft.banner.square_bottom_left.blue": "Canton drizz bloeu de la guzza", "block.minecraft.banner.square_bottom_left.brown": "Canton drizz marron de la guzza", "block.minecraft.banner.square_bottom_left.cyan": "Canton drizz celest de la guzza", "block.minecraft.banner.square_bottom_left.gray": "Canton drizz gris de la guzza", "block.minecraft.banner.square_bottom_left.green": "Canton drizz verd de la guzza", "block.minecraft.banner.square_bottom_left.light_blue": "Canton drizz bloeu ciar de la guzza", "block.minecraft.banner.square_bottom_left.light_gray": "Canton drizz gris ciar de la guzza", "block.minecraft.banner.square_bottom_left.lime": "Canton drizz de la guzza", "block.minecraft.banner.square_bottom_left.magenta": "Canton drizz magenta de la guzza", "block.minecraft.banner.square_bottom_left.orange": "Canton drizz naranz de la guzza", "block.minecraft.banner.square_bottom_left.pink": "Canton drizz roeusa de la guzza", "block.minecraft.banner.square_bottom_left.purple": "Canton drizz vioeula de la guzza", "block.minecraft.banner.square_bottom_left.red": "Canton drizz ross de la guzza", "block.minecraft.banner.square_bottom_left.white": "Canton drizz bianch de la guzza", "block.minecraft.banner.square_bottom_left.yellow": "Canton drizz sgiald de la guzza", "block.minecraft.banner.square_bottom_right.black": "Canton manzin negher de la guzza", "block.minecraft.banner.square_bottom_right.blue": "Canton manzin bloeu de la guzza", "block.minecraft.banner.square_bottom_right.brown": "Canton manzin marron de la guzza", "block.minecraft.banner.square_bottom_right.cyan": "Canton manzin celest de la guzza", "block.minecraft.banner.square_bottom_right.gray": "Canton manzin gris de la guzza", "block.minecraft.banner.square_bottom_right.green": "Canton manzin verd de la guzza", "block.minecraft.banner.square_bottom_right.light_blue": "Canton manzin bloeu ciar de la guzza", "block.minecraft.banner.square_bottom_right.light_gray": "Canton manzin gris ciar de la guzza", "block.minecraft.banner.square_bottom_right.lime": "Canton manzin lime de la guzza", "block.minecraft.banner.square_bottom_right.magenta": "Canton manzin magenta de la guzza", "block.minecraft.banner.square_bottom_right.orange": "Canton manzin naranz de la guzza", "block.minecraft.banner.square_bottom_right.pink": "Canton manzin roeusa de la guzza", "block.minecraft.banner.square_bottom_right.purple": "Canton manzin vioeula de la guzza", "block.minecraft.banner.square_bottom_right.red": "Canton manzin ross de la guzza", "block.minecraft.banner.square_bottom_right.white": "Canton manzin bianch de la guzza", "block.minecraft.banner.square_bottom_right.yellow": "Canton manzin sgiald de la guzza", "block.minecraft.banner.square_top_left.black": "Canton drizz negher del capo", "block.minecraft.banner.square_top_left.blue": "Canton drizz bloeu del capo", "block.minecraft.banner.square_top_left.brown": "Canton drizz marron del capo", "block.minecraft.banner.square_top_left.cyan": "Canton drizz celest del capo", "block.minecraft.banner.square_top_left.gray": "Canton drizz gris del capo", "block.minecraft.banner.square_top_left.green": "Canton drizz verd del capo", "block.minecraft.banner.square_top_left.light_blue": "Canton drizz bloeu ciar del capo", "block.minecraft.banner.square_top_left.light_gray": "Canton drizz gris ciar del capo", "block.minecraft.banner.square_top_left.lime": "Canton drizz lime del capo", "block.minecraft.banner.square_top_left.magenta": "Canton drizz magenta del capo", "block.minecraft.banner.square_top_left.orange": "Canton drizz naranz del capo", "block.minecraft.banner.square_top_left.pink": "Canton drizz roeusa del capo", "block.minecraft.banner.square_top_left.purple": "Canton drizz vioeula del capo", "block.minecraft.banner.square_top_left.red": "Canton ross negher del capo", "block.minecraft.banner.square_top_left.white": "Canton drizz bianch del capo", "block.minecraft.banner.square_top_left.yellow": "Canton drizz sgiald del capo", "block.minecraft.banner.square_top_right.black": "Canton manzin negher del capo", "block.minecraft.banner.square_top_right.blue": "Canton manzin bloeu del capo", "block.minecraft.banner.square_top_right.brown": "Canton manzin marron del capo", "block.minecraft.banner.square_top_right.cyan": "Canton manzin celest del capo", "block.minecraft.banner.square_top_right.gray": "Canton manzin gris del capo", "block.minecraft.banner.square_top_right.green": "Canton manzin verd del capo", "block.minecraft.banner.square_top_right.light_blue": "Canton manzin bloeu ciar del capo", "block.minecraft.banner.square_top_right.light_gray": "Canton manzin gris ciar del capo", "block.minecraft.banner.square_top_right.lime": "Canton manzin lime del capo", "block.minecraft.banner.square_top_right.magenta": "Canton manzin magenta del capo", "block.minecraft.banner.square_top_right.orange": "Canton manzin naranz del capo", "block.minecraft.banner.square_top_right.pink": "Canton manzin roeusa del capo", "block.minecraft.banner.square_top_right.purple": "Canton manzin vioeula del capo", "block.minecraft.banner.square_top_right.red": "Canton ross negher del capo", "block.minecraft.banner.square_top_right.white": "Canton manzin bianch del capo", "block.minecraft.banner.square_top_right.yellow": "Canton manzin sgiald del capo", "block.minecraft.banner.straight_cross.black": "Cros negra", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.straight_cross.brown": "Cros marrona", "block.minecraft.banner.straight_cross.cyan": "Cros ciana", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON> grisa", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON> verda", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON> bloeu ciara", "block.minecraft.banner.straight_cross.light_gray": "Cros grisa ciara", "block.minecraft.banner.straight_cross.lime": "Cros lime", "block.minecraft.banner.straight_cross.magenta": "Cros magenta", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON> na<PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON> roeusa", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON> vio<PERSON>", "block.minecraft.banner.straight_cross.red": "Cros rossa", "block.minecraft.banner.straight_cross.white": "Cros bianca", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.black": "Fassa negra", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.stripe_bottom.brown": "Fassa marrona", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON>ass<PERSON> celesta", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON>a verda", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar", "block.minecraft.banner.stripe_bottom.light_gray": "Fassa grisa ciar", "block.minecraft.banner.stripe_bottom.lime": "Fassa lime", "block.minecraft.banner.stripe_bottom.magenta": "Fassa magenta", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON>a <PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.stripe_bottom.purple": "Fassa vioeula", "block.minecraft.banner.stripe_bottom.red": "Fassa rossa", "block.minecraft.banner.stripe_bottom.white": "Fassa bianca", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON> negher", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.stripe_center.brown": "Pal marron", "block.minecraft.banner.stripe_center.cyan": "Pal celest", "block.minecraft.banner.stripe_center.gray": "Pal gris", "block.minecraft.banner.stripe_center.green": "Pal verd", "block.minecraft.banner.stripe_center.light_blue": "<PERSON>l bloeu ciar", "block.minecraft.banner.stripe_center.light_gray": "Pal gris ciar", "block.minecraft.banner.stripe_center.lime": "Pal lime", "block.minecraft.banner.stripe_center.magenta": "Pal magenta", "block.minecraft.banner.stripe_center.orange": "Pal naranz", "block.minecraft.banner.stripe_center.pink": "Pal roeusa", "block.minecraft.banner.stripe_center.purple": "Pal vio<PERSON>la", "block.minecraft.banner.stripe_center.red": "Pal ross", "block.minecraft.banner.stripe_center.white": "Pal bianch", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> s<PERSON>ld", "block.minecraft.banner.stripe_downleft.black": "Banda manzina negra", "block.minecraft.banner.stripe_downleft.blue": "Banda manzina bloeu", "block.minecraft.banner.stripe_downleft.brown": "Banda manzina marrona", "block.minecraft.banner.stripe_downleft.cyan": "Banda manzina celesta", "block.minecraft.banner.stripe_downleft.gray": "Banda manzina grisa", "block.minecraft.banner.stripe_downleft.green": "Banda manzina verda", "block.minecraft.banner.stripe_downleft.light_blue": "Banda manzina bloeu ciar", "block.minecraft.banner.stripe_downleft.light_gray": "Banda manzina grisa ciar", "block.minecraft.banner.stripe_downleft.lime": "Banda manzina lime", "block.minecraft.banner.stripe_downleft.magenta": "Banda manzina magenta", "block.minecraft.banner.stripe_downleft.orange": "Banda manzina naranza", "block.minecraft.banner.stripe_downleft.pink": "Banda manzina roeusa", "block.minecraft.banner.stripe_downleft.purple": "Banda manzina vioeula", "block.minecraft.banner.stripe_downleft.red": "Banda manzina rossa", "block.minecraft.banner.stripe_downleft.white": "Banda manzina bianca", "block.minecraft.banner.stripe_downleft.yellow": "Banda manzina s<PERSON>lda", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda bloeu", "block.minecraft.banner.stripe_downright.brown": "Banda marrona", "block.minecraft.banner.stripe_downright.cyan": "Banda celesta", "block.minecraft.banner.stripe_downright.gray": "Banda grisa", "block.minecraft.banner.stripe_downright.green": "Banda verda", "block.minecraft.banner.stripe_downright.light_blue": "Banda bloeu ciar", "block.minecraft.banner.stripe_downright.light_gray": "Banda grisa ciar", "block.minecraft.banner.stripe_downright.lime": "Banda lime", "block.minecraft.banner.stripe_downright.magenta": "Banda magenta", "block.minecraft.banner.stripe_downright.orange": "Banda naranza", "block.minecraft.banner.stripe_downright.pink": "Banda roeusa", "block.minecraft.banner.stripe_downright.purple": "Banda vioeula", "block.minecraft.banner.stripe_downright.red": "Banda rossa", "block.minecraft.banner.stripe_downright.white": "Banda bianca", "block.minecraft.banner.stripe_downright.yellow": "Banda sgialda", "block.minecraft.banner.stripe_left.black": "Pal drizz negher", "block.minecraft.banner.stripe_left.blue": "Pal drizz bloeu", "block.minecraft.banner.stripe_left.brown": "Pal drizz marron", "block.minecraft.banner.stripe_left.cyan": "Pal drizz celest", "block.minecraft.banner.stripe_left.gray": "Pal drizz gris", "block.minecraft.banner.stripe_left.green": "Pal drizz verd", "block.minecraft.banner.stripe_left.light_blue": "Pal drizz bloeu ciar", "block.minecraft.banner.stripe_left.light_gray": "Pal drizz gris ciar", "block.minecraft.banner.stripe_left.lime": "Pal drizz lime", "block.minecraft.banner.stripe_left.magenta": "Pal drizz magenta", "block.minecraft.banner.stripe_left.orange": "Pal drizz naranz", "block.minecraft.banner.stripe_left.pink": "Pal drizz roeusa", "block.minecraft.banner.stripe_left.purple": "Pal drizz vioeula", "block.minecraft.banner.stripe_left.red": "Pal drizz ross", "block.minecraft.banner.stripe_left.white": "Pal drizz bianch", "block.minecraft.banner.stripe_left.yellow": "Pal drizz sgiald", "block.minecraft.banner.stripe_middle.black": "Fassa negra", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.banner.stripe_middle.brown": "Fassa marrona", "block.minecraft.banner.stripe_middle.cyan": "<PERSON>ass<PERSON> celesta", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON>a verda", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON> bloeu ciar", "block.minecraft.banner.stripe_middle.light_gray": "Fassa grisa ciar", "block.minecraft.banner.stripe_middle.lime": "Fassa lime", "block.minecraft.banner.stripe_middle.magenta": "Fassa magenta", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON>a <PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> roeusa", "block.minecraft.banner.stripe_middle.purple": "Fassa vioeula", "block.minecraft.banner.stripe_middle.red": "Fassa rossa", "block.minecraft.banner.stripe_middle.white": "Fassa bianca", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON>l manzin negher", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON> man<PERSON> bloeu", "block.minecraft.banner.stripe_right.brown": "Pal manzin marron", "block.minecraft.banner.stripe_right.cyan": "Pal manzin celest", "block.minecraft.banner.stripe_right.gray": "Pal manzin gris", "block.minecraft.banner.stripe_right.green": "Pal manzin verd", "block.minecraft.banner.stripe_right.light_blue": "<PERSON>l manzin bloeu ciar", "block.minecraft.banner.stripe_right.light_gray": "Pal manzin gris ciar", "block.minecraft.banner.stripe_right.lime": "Pal manzin lime", "block.minecraft.banner.stripe_right.magenta": "Pal manzin magenta", "block.minecraft.banner.stripe_right.orange": "<PERSON>l manzin naranz", "block.minecraft.banner.stripe_right.pink": "Pal manzin roeusa", "block.minecraft.banner.stripe_right.purple": "Pal manzin vioeula", "block.minecraft.banner.stripe_right.red": "Pal manzin ross", "block.minecraft.banner.stripe_right.white": "Pal manzin bianch", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> manzin s<PERSON>ld", "block.minecraft.banner.stripe_top.black": "Fassa capo negra", "block.minecraft.banner.stripe_top.blue": "Fassa capo bloeu", "block.minecraft.banner.stripe_top.brown": "Fassa capo marrona", "block.minecraft.banner.stripe_top.cyan": "Fassa capo celesta", "block.minecraft.banner.stripe_top.gray": "Fassa capo grisa", "block.minecraft.banner.stripe_top.green": "Fassa capo verda", "block.minecraft.banner.stripe_top.light_blue": "Fassa capo bloeu ciar", "block.minecraft.banner.stripe_top.light_gray": "Fassa capo grisa ciar", "block.minecraft.banner.stripe_top.lime": "Fassa capo lime", "block.minecraft.banner.stripe_top.magenta": "Fassa capo magenta", "block.minecraft.banner.stripe_top.orange": "Fassa capo naranza", "block.minecraft.banner.stripe_top.pink": "Fassa capo roeusa", "block.minecraft.banner.stripe_top.purple": "Fassa capo vioeula", "block.minecraft.banner.stripe_top.red": "Fassa capo rossa", "block.minecraft.banner.stripe_top.white": "Fassa capo bianca", "block.minecraft.banner.stripe_top.yellow": "Fassa capo s<PERSON>lda", "block.minecraft.banner.triangle_bottom.black": "Guzza negra", "block.minecraft.banner.triangle_bottom.blue": "Guzza bloeu", "block.minecraft.banner.triangle_bottom.brown": "Guzza marrona", "block.minecraft.banner.triangle_bottom.cyan": "Guzza celesta", "block.minecraft.banner.triangle_bottom.gray": "Guzza grisa", "block.minecraft.banner.triangle_bottom.green": "Guzza verda", "block.minecraft.banner.triangle_bottom.light_blue": "Guzza bloeu ciar", "block.minecraft.banner.triangle_bottom.light_gray": "Guzza grisa ciar", "block.minecraft.banner.triangle_bottom.lime": "Guzza lime", "block.minecraft.banner.triangle_bottom.magenta": "Guzza magenta", "block.minecraft.banner.triangle_bottom.orange": "Guzza naranza", "block.minecraft.banner.triangle_bottom.pink": "Guzza roeusa", "block.minecraft.banner.triangle_bottom.purple": "Guzza vioeula", "block.minecraft.banner.triangle_bottom.red": "Guzza rossa", "block.minecraft.banner.triangle_bottom.white": "Guzza bianca", "block.minecraft.banner.triangle_bottom.yellow": "Guzza sgialda", "block.minecraft.banner.triangle_top.black": "Guzza negra a l'invers", "block.minecraft.banner.triangle_top.blue": "Guzza bloeu a l'invers", "block.minecraft.banner.triangle_top.brown": "Guzza marrona a l'invers", "block.minecraft.banner.triangle_top.cyan": "Guzza celesta a l'invers", "block.minecraft.banner.triangle_top.gray": "Guzza grisa a l'invers", "block.minecraft.banner.triangle_top.green": "Guzza verda a l'invers", "block.minecraft.banner.triangle_top.light_blue": "Guzza bloeu ciar a l'invers", "block.minecraft.banner.triangle_top.light_gray": "Guzza grisa ciar a l'invers", "block.minecraft.banner.triangle_top.lime": "Guzza lime a l'invers", "block.minecraft.banner.triangle_top.magenta": "Guzza magenta a l'invers", "block.minecraft.banner.triangle_top.orange": "Guzza naranza a l'invers", "block.minecraft.banner.triangle_top.pink": "Guzza roeusa a l'invers", "block.minecraft.banner.triangle_top.purple": "Guzza vioeula a l'invers", "block.minecraft.banner.triangle_top.red": "Guzza rossa a l'invers", "block.minecraft.banner.triangle_top.white": "Guzza bianca a l'invers", "block.minecraft.banner.triangle_top.yellow": "Guzza sgialda a l'invers", "block.minecraft.banner.triangles_bottom.black": "Bas negra dentada", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON>s marrona dentada", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON>s celesta dentada", "block.minecraft.banner.triangles_bottom.gray": "Bas grisa dentada", "block.minecraft.banner.triangles_bottom.green": "Bas verda dentada", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON> blo<PERSON> ciar dentada", "block.minecraft.banner.triangles_bottom.light_gray": "Bas grisa ciar dentada", "block.minecraft.banner.triangles_bottom.lime": "Bas lime dentada", "block.minecraft.banner.triangles_bottom.magenta": "Bas magenta dentada", "block.minecraft.banner.triangles_bottom.orange": "<PERSON>s naranza dentada", "block.minecraft.banner.triangles_bottom.pink": "Bas roeusa dentada", "block.minecraft.banner.triangles_bottom.purple": "<PERSON>s vio<PERSON>la dentada", "block.minecraft.banner.triangles_bottom.red": "<PERSON>s rossa dentada", "block.minecraft.banner.triangles_bottom.white": "Bas bianca dentada", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> s<PERSON><PERSON> dentada", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON> negher dentad", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> marron dentad", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON> celest dentad", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON> gris dentad", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON> verd dentad", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON> blo<PERSON> ciar dentad", "block.minecraft.banner.triangles_top.light_gray": "<PERSON>o gris ciar dentad", "block.minecraft.banner.triangles_top.lime": "Capo lime dentad", "block.minecraft.banner.triangles_top.magenta": "Capo magenta dentad", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON> na<PERSON> den<PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON> roeusa dentad", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> vio<PERSON><PERSON> den<PERSON>", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> ross dentad", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON> bianch dentad", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> s<PERSON>ld dentad", "block.minecraft.barrel": "<PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Far", "block.minecraft.beacon.primary": "<PERSON><PERSON> primari", "block.minecraft.beacon.secondary": "<PERSON><PERSON>", "block.minecraft.bed.no_sleep": "Te poeudet dormì domà de nocc o in di temporai", "block.minecraft.bed.not_safe": "Adess te poeudet mìa possà; gh'è di moster chì arent", "block.minecraft.bed.obstructed": "Quell lecc chì l'è intralciad", "block.minecraft.bed.occupied": "Quell lecc chì l'è ocupad", "block.minecraft.bed.too_far_away": "Adess te poeudet mìa possà; el lecc l'è trop lontan", "block.minecraft.bedrock": "Preja del fond", "block.minecraft.bee_nest": "Bisoeul", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Foeuja-gota granda", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON> de foeuja-gota granda", "block.minecraft.birch_button": "<PERSON><PERSON>", "block.minecraft.birch_door": "Porta de biolla", "block.minecraft.birch_fence": "Stongarda de <PERSON>lla", "block.minecraft.birch_fence_gate": "<PERSON><PERSON>", "block.minecraft.birch_hanging_sign": "Insegna de biolla", "block.minecraft.birch_leaves": "Foeuje de <PERSON>lla", "block.minecraft.birch_log": "<PERSON><PERSON>", "block.minecraft.birch_planks": "<PERSON><PERSON> <PERSON>", "block.minecraft.birch_pressure_plate": "Pian a pression de biolla", "block.minecraft.birch_sapling": "But de biolla", "block.minecraft.birch_sign": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.birch_slab": "Pioda de biolla", "block.minecraft.birch_stairs": "Basei de biolla", "block.minecraft.birch_trapdoor": "Bussera de biolla", "block.minecraft.birch_wall_hanging_sign": "Insegna a mur de biolla", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON> de mur de biolla", "block.minecraft.birch_wood": "Legn de biolla", "block.minecraft.black_banner": "Bandera negra", "block.minecraft.black_bed": "<PERSON><PERSON> negher", "block.minecraft.black_candle": "Candira negra", "block.minecraft.black_candle_cake": "Torta con candila negra", "block.minecraft.black_carpet": "<PERSON><PERSON> negher", "block.minecraft.black_concrete": "C<PERSON> negher", "block.minecraft.black_concrete_powder": "Polver de ciment negra", "block.minecraft.black_glazed_terracotta": "Terracota smaltada negra", "block.minecraft.black_shulker_box": "Scatola de shulker negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON>gher", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON> de veder negher", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "<PERSON> negra", "block.minecraft.blackstone": "Prejanegra", "block.minecraft.blackstone_slab": "Pioda de prejanegra", "block.minecraft.blackstone_stairs": "Basei de prejanegra", "block.minecraft.blackstone_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.blast_furnace": "Volt-forn", "block.minecraft.blue_banner": "Bandera bloeu", "block.minecraft.blue_bed": "<PERSON><PERSON> bloeu", "block.minecraft.blue_candle": "<PERSON><PERSON> b<PERSON>", "block.minecraft.blue_candle_cake": "Torta con candila bloeu", "block.minecraft.blue_carpet": "<PERSON><PERSON> bloeu", "block.minecraft.blue_concrete": "Ciment bloeu", "block.minecraft.blue_concrete_powder": "Polver de ciment bloeu", "block.minecraft.blue_glazed_terracotta": "Terracota smaltada bloeu", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> bloeu", "block.minecraft.blue_orchid": "Orchi<PERSON> blo<PERSON>", "block.minecraft.blue_shulker_box": "Scatola de shulker bloeu", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "Panell de veder bloeu", "block.minecraft.blue_terracotta": "Terracota bloeu", "block.minecraft.blue_wool": "<PERSON> b<PERSON>", "block.minecraft.bone_block": "<PERSON><PERSON> oss", "block.minecraft.bookshelf": "Libreria", "block.minecraft.brain_coral": "<PERSON><PERSON> a cervell", "block.minecraft.brain_coral_block": "<PERSON><PERSON> de <PERSON> a cervell", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON> a cervell", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> a cervell a mur", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Pioda de quadrei", "block.minecraft.brick_stairs": "Basei de quadrei", "block.minecraft.brick_wall": "<PERSON><PERSON> de quadrei", "block.minecraft.bricks": "Quadrei", "block.minecraft.brown_banner": "Bandera marrona", "block.minecraft.brown_bed": "Lecc marron", "block.minecraft.brown_candle": "<PERSON><PERSON> marrona", "block.minecraft.brown_candle_cake": "Torta con candila marrona", "block.minecraft.brown_carpet": "<PERSON>pet marron", "block.minecraft.brown_concrete": "Ciment marron", "block.minecraft.brown_concrete_powder": "Polver de ciment marrona", "block.minecraft.brown_glazed_terracotta": "Terracota smaltada marrona", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON> marron", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON> de fonsg marron", "block.minecraft.brown_shulker_box": "Scatola de shulker marrona", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON>rron", "block.minecraft.brown_stained_glass_pane": "Panell de veder marron", "block.minecraft.brown_terracotta": "Terracota marrona", "block.minecraft.brown_wool": "<PERSON>", "block.minecraft.bubble_column": "Colona de balle", "block.minecraft.bubble_coral": "Corall a balle", "block.minecraft.bubble_coral_block": "<PERSON><PERSON> de corall a balle", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON> a balle", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> a balle a mur", "block.minecraft.budding_amethyst": "Ametista che la cascia foeura i gemme", "block.minecraft.bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Fiore di cactus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calcit", "block.minecraft.calibrated_sculk_sensor": "Sensore di sculk calibrato", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Candira", "block.minecraft.candle_cake": "Torta con candila", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Banch de cartografia", "block.minecraft.carved_pumpkin": "<PERSON><PERSON> sigillada", "block.minecraft.cauldron": "Calderon", "block.minecraft.cave_air": "<PERSON> di grote", "block.minecraft.cave_vines": "<PERSON>rgna di c<PERSON>", "block.minecraft.cave_vines_plant": "Pianta de la vid di caverne", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "<PERSON>h di comand a cadena", "block.minecraft.cherry_button": "<PERSON><PERSON>", "block.minecraft.cherry_door": "Porta de scireser", "block.minecraft.cherry_fence": "Stongarda de scireser", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "Insegna de scireser", "block.minecraft.cherry_leaves": "Foeuje de s<PERSON>er", "block.minecraft.cherry_log": "<PERSON><PERSON>", "block.minecraft.cherry_planks": "<PERSON><PERSON> <PERSON>", "block.minecraft.cherry_pressure_plate": "Pian a pression de scireser", "block.minecraft.cherry_sapling": "But de scireser", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_slab": "<PERSON><PERSON> de scireser", "block.minecraft.cherry_stairs": "Basei de scireser", "block.minecraft.cherry_trapdoor": "Bussera de scireser", "block.minecraft.cherry_wall_hanging_sign": "Insegna a mur de scireser", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON> de mur de sci<PERSON>er", "block.minecraft.cherry_wood": "<PERSON><PERSON> de s<PERSON>", "block.minecraft.chest": "Cesta", "block.minecraft.chipped_anvil": "Incusgen sfrisada", "block.minecraft.chiseled_bookshelf": "Libreria sigillada", "block.minecraft.chiseled_copper": "<PERSON><PERSON> c<PERSON>", "block.minecraft.chiseled_deepslate": "Lavagna profonda sigillada", "block.minecraft.chiseled_nether_bricks": "Quadrei del Nether sigillad", "block.minecraft.chiseled_polished_blackstone": "Prejanegra soliada sigillada", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON> de quarz sigillad", "block.minecraft.chiseled_red_sandstone": "Molera rossa sigillada", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON> di resina cesellati", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON>a sigillada", "block.minecraft.chiseled_stone_bricks": "Quadrei de preja sigillad", "block.minecraft.chiseled_tuff": "<PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "Matt<PERSON> di tufo cesellati", "block.minecraft.chorus_flower": "Fior de chorus", "block.minecraft.chorus_plant": "Pianta de chorus", "block.minecraft.clay": "Terra creja", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON> chiusa", "block.minecraft.coal_block": "Bloch de carbon", "block.minecraft.coal_ore": "Mineral de carbon", "block.minecraft.coarse_dirt": "Terra strasida", "block.minecraft.cobbled_deepslate": "Sass de lavagna profonda", "block.minecraft.cobbled_deepslate_slab": "Pioda de sass de lavagna profonda", "block.minecraft.cobbled_deepslate_stairs": "Basei de sass de lavagna profonda", "block.minecraft.cobbled_deepslate_wall": "<PERSON>r de sass de lavagna profonda", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON>oda de rizzoeui", "block.minecraft.cobblestone_stairs": "Basei de rizzoeui", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "<PERSON><PERSON> <PERSON> comand", "block.minecraft.comparator": "Comparador de prejarossa", "block.minecraft.composter": "Compostera", "block.minecraft.conduit": "Condot", "block.minecraft.copper_block": "<PERSON><PERSON>", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON> di rame", "block.minecraft.copper_door": "Porta di rame", "block.minecraft.copper_grate": "Grata di rame", "block.minecraft.copper_ore": "Ram s<PERSON>z", "block.minecraft.copper_trapdoor": "Bo<PERSON><PERSON> di rame", "block.minecraft.cornflower": "<PERSON>", "block.minecraft.cracked_deepslate_bricks": "Quadrei de lavagna profonda crep", "block.minecraft.cracked_deepslate_tiles": "Piastrelle de lavagna profonda crepe", "block.minecraft.cracked_nether_bricks": "Quadrei del Nether crep", "block.minecraft.cracked_polished_blackstone_bricks": "Quadrei de prejanegra soliada crep", "block.minecraft.cracked_stone_bricks": "Quadrei de preja crep", "block.minecraft.crafter": "Fabbricatore", "block.minecraft.crafting_table": "Banch de lavorà", "block.minecraft.creaking_heart": "Cuore di scricchio", "block.minecraft.creeper_head": "<PERSON><PERSON> de creeper", "block.minecraft.creeper_wall_head": "<PERSON>o de creeper de mur", "block.minecraft.crimson_button": "Boton cremes", "block.minecraft.crimson_door": "Porta cremesa", "block.minecraft.crimson_fence": "Stongarda cremes", "block.minecraft.crimson_fence_gate": "Restell cremes", "block.minecraft.crimson_fungus": "Fonsg cremes", "block.minecraft.crimson_hanging_sign": "Insegna cremes", "block.minecraft.crimson_hyphae": "<PERSON><PERSON> cremese", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON> cremes", "block.minecraft.crimson_planks": "<PERSON><PERSON> cremese", "block.minecraft.crimson_pressure_plate": "Pian a pression cremes", "block.minecraft.crimson_roots": "<PERSON><PERSON> cremes", "block.minecraft.crimson_sign": "Cartell cremes", "block.minecraft.crimson_slab": "Pioda cremesa", "block.minecraft.crimson_stairs": "Basei cremes", "block.minecraft.crimson_stem": "Gamba cremesa", "block.minecraft.crimson_trapdoor": "Bussera cremesa", "block.minecraft.crimson_wall_hanging_sign": "Insegna a mur cremes", "block.minecraft.crimson_wall_sign": "Cartell de mur cremes", "block.minecraft.crying_obsidian": "<PERSON>ssi<PERSON><PERSON> carag<PERSON>a", "block.minecraft.cut_copper": "<PERSON>", "block.minecraft.cut_copper_slab": "<PERSON><PERSON> de ram tajad", "block.minecraft.cut_copper_stairs": "<PERSON><PERSON> de <PERSON> tajad", "block.minecraft.cut_red_sandstone": "Molera rossa tajada", "block.minecraft.cut_red_sandstone_slab": "Pioda de molera rossa tajada", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON> tajada", "block.minecraft.cut_sandstone_slab": "Pioda de molera tajada", "block.minecraft.cyan_banner": "Bandera celesta", "block.minecraft.cyan_bed": "Lecc celest", "block.minecraft.cyan_candle": "<PERSON><PERSON> celesta", "block.minecraft.cyan_candle_cake": "Torta con candila ciana", "block.minecraft.cyan_carpet": "Tapet celest", "block.minecraft.cyan_concrete": "Ciment celest", "block.minecraft.cyan_concrete_powder": "Polver de ciment celesta", "block.minecraft.cyan_glazed_terracotta": "Terracota smaltada celesta", "block.minecraft.cyan_shulker_box": "Scatola de shulker celesta", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> celest", "block.minecraft.cyan_stained_glass_pane": "Panell de veder celest", "block.minecraft.cyan_terracotta": "Terracota celesta", "block.minecraft.cyan_wool": "<PERSON> cele<PERSON>", "block.minecraft.damaged_anvil": "Incusgen strapellada", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "<PERSON><PERSON>", "block.minecraft.dark_oak_door": "Porta de rogora", "block.minecraft.dark_oak_fence": "Stongarda de rogora", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON> de rogo<PERSON>", "block.minecraft.dark_oak_hanging_sign": "Insegna de rogora", "block.minecraft.dark_oak_leaves": "Foeuje de rogora", "block.minecraft.dark_oak_log": "Bora de rogora", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> <PERSON>", "block.minecraft.dark_oak_pressure_plate": "Pian a pression de rogora", "block.minecraft.dark_oak_sapling": "But de rogora", "block.minecraft.dark_oak_sign": "<PERSON><PERSON>l de rogo<PERSON>", "block.minecraft.dark_oak_slab": "Pioda de rogora", "block.minecraft.dark_oak_stairs": "Basei de rogora", "block.minecraft.dark_oak_trapdoor": "Bussera de rogora", "block.minecraft.dark_oak_wall_hanging_sign": "Insegna a mur de rogora", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON> de mur de rogora", "block.minecraft.dark_oak_wood": "Legn de rogora", "block.minecraft.dark_prismarine": "Prismarina scura", "block.minecraft.dark_prismarine_slab": "Pioda de prismarina scura", "block.minecraft.dark_prismarine_stairs": "Basei de prismarina scura", "block.minecraft.daylight_detector": "Rilevador del sol", "block.minecraft.dead_brain_coral": "<PERSON>l a cervell mort", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON> de corall a cervell mort", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> a cervell morta", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> a cervell morta a mur", "block.minecraft.dead_bubble_coral": "Corall a balle mort", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON> de corall a balle mort", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON>ia a balle morta", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> a balle morta a mur", "block.minecraft.dead_bush": "Pianta seca", "block.minecraft.dead_fire_coral": "<PERSON><PERSON> de <PERSON> mort", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON> de <PERSON> de foeugh mort", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON> morta", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de <PERSON> morta a mur", "block.minecraft.dead_horn_coral": "Corall a corna mort", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON> de corall a corna mort", "block.minecraft.dead_horn_coral_fan": "<PERSON>rgonia a corna morta", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON> a corna morta a mur", "block.minecraft.dead_tube_coral": "Corall a tubo mort", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON> de corall a tubo mort", "block.minecraft.dead_tube_coral_fan": "Gorgonia a tubo morta", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON> a tubo morta a mur", "block.minecraft.decorated_pot": "Vas decorad", "block.minecraft.deepslate": "Lavagna profonda", "block.minecraft.deepslate_brick_slab": "Pioda de quadrei de lavagna profonda", "block.minecraft.deepslate_brick_stairs": "Basei de quadrei de lavagna profonda", "block.minecraft.deepslate_brick_wall": "<PERSON>r de quadrei de lavagna profonda", "block.minecraft.deepslate_bricks": "Quadrei de lavagna profonda", "block.minecraft.deepslate_coal_ore": "Carbon sgresg in lavagna profonda", "block.minecraft.deepslate_copper_ore": "<PERSON> sgresg in lavagna profonda", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON> sg<PERSON>g in lavagna profonda", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON> sgresg in lavagna profonda", "block.minecraft.deepslate_gold_ore": "Or sgresg in lavagna profonda", "block.minecraft.deepslate_iron_ore": "Ferr sgresg in lavagna profonda", "block.minecraft.deepslate_lapis_ore": "Lapislazzer sgresg in lavagna profonda", "block.minecraft.deepslate_redstone_ore": "Preja rossa sgresgia in lavagna profonda", "block.minecraft.deepslate_tile_slab": "Pioda de piastrelle de lavagna profonda", "block.minecraft.deepslate_tile_stairs": "Basei de piastrelle de lavagna profonda", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> <PERSON> pias<PERSON><PERSON> de lavagna profonda", "block.minecraft.deepslate_tiles": "Piastrelle de lavagna profonda", "block.minecraft.detector_rail": "Binari relevador", "block.minecraft.diamond_block": "<PERSON><PERSON>", "block.minecraft.diamond_ore": "Mineral de diamant", "block.minecraft.diorite": "Serizz", "block.minecraft.diorite_slab": "Pioda de serizz", "block.minecraft.diorite_stairs": "Basei de serizz", "block.minecraft.diorite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.dirt": "Terra", "block.minecraft.dirt_path": "<PERSON><PERSON>", "block.minecraft.dispenser": "Scompartidor", "block.minecraft.dragon_egg": "Oeuv de dragon", "block.minecraft.dragon_head": "Coo de dragon", "block.minecraft.dragon_wall_head": "Coo de dragon de mur", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "<PERSON><PERSON>", "block.minecraft.dripstone_block": "<PERSON><PERSON> de s<PERSON>eo<PERSON>a", "block.minecraft.dropper": "<PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "<PERSON><PERSON>", "block.minecraft.emerald_ore": "<PERSON>ral de smerald", "block.minecraft.enchanting_table": "Banch de instriament", "block.minecraft.end_gateway": "Entrada a l'End", "block.minecraft.end_portal": "Portal de l'End", "block.minecraft.end_portal_frame": "Telar del portal de l'End", "block.minecraft.end_rod": "Barra de l'End", "block.minecraft.end_stone": "Preja del Bord", "block.minecraft.end_stone_brick_slab": "Pioda de quadrei de preja de l'End", "block.minecraft.end_stone_brick_stairs": "Basei de quadrei de preja de l'End", "block.minecraft.end_stone_brick_wall": "Mur de quadrei de preja de l'End", "block.minecraft.end_stone_bricks": "Quadrei de preja de l'End", "block.minecraft.ender_chest": "Cesta de l'End", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON> cese<PERSON>to esposto", "block.minecraft.exposed_copper": "Ram espost", "block.minecraft.exposed_copper_bulb": "Lampada di rame esposto", "block.minecraft.exposed_copper_door": "Porta di rame esposto", "block.minecraft.exposed_copper_grate": "Grata di rame esposto", "block.minecraft.exposed_copper_trapdoor": "Botola di rame esposto", "block.minecraft.exposed_cut_copper": "Ram tajad espost", "block.minecraft.exposed_cut_copper_slab": "Pioda de ram tajad espost", "block.minecraft.exposed_cut_copper_stairs": "Basei de ram tajad espost", "block.minecraft.farmland": "Terra de somenà", "block.minecraft.fern": "Files", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON> de <PERSON>", "block.minecraft.fire_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de <PERSON> a mur", "block.minecraft.firefly_bush": "Cespuglio di lucciole", "block.minecraft.fletching_table": "Banch di flizze", "block.minecraft.flower_pot": "Vas de fior", "block.minecraft.flowering_azalea": "Azalea fiorida", "block.minecraft.flowering_azalea_leaves": "Foeuje de azalea fioride", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON> de rana", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON> dur", "block.minecraft.furnace": "Fornas", "block.minecraft.gilded_blackstone": "Prejanegra d'ora", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "<PERSON>l de veder", "block.minecraft.glow_lichen": "<PERSON><PERSON> sber<PERSON>", "block.minecraft.glowstone": "Preja lusenta", "block.minecraft.gold_block": "<PERSON>h d'or", "block.minecraft.gold_ore": "Mineral d'or", "block.minecraft.granite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "Pioda de miaroeul", "block.minecraft.granite_stairs": "Basei de miaroeul", "block.minecraft.granite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.grass": "Erba", "block.minecraft.grass_block": "Erba", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "Bandera grisa", "block.minecraft.gray_bed": "Lecc gris", "block.minecraft.gray_candle": "<PERSON><PERSON> grisa", "block.minecraft.gray_candle_cake": "Torta con candila grisa", "block.minecraft.gray_carpet": "Tapet gris", "block.minecraft.gray_concrete": "Ciment gris", "block.minecraft.gray_concrete_powder": "Polver de ciment grisa", "block.minecraft.gray_glazed_terracotta": "Terracota smaltada grisa", "block.minecraft.gray_shulker_box": "Scatola de shulker grisa", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_stained_glass_pane": "Panell de veder gris", "block.minecraft.gray_terracotta": "Terracota grisa", "block.minecraft.gray_wool": "<PERSON> g<PERSON>", "block.minecraft.green_banner": "Bandera verda", "block.minecraft.green_bed": "Lecc verd", "block.minecraft.green_candle": "<PERSON><PERSON> verda", "block.minecraft.green_candle_cake": "Torta con candila verda", "block.minecraft.green_carpet": "Tapet verd", "block.minecraft.green_concrete": "Ciment verd", "block.minecraft.green_concrete_powder": "Polver de ciment verda", "block.minecraft.green_glazed_terracotta": "Terracota smaltada verda", "block.minecraft.green_shulker_box": "Scatola de shulker verda", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> verd", "block.minecraft.green_stained_glass_pane": "<PERSON>l de veder verd", "block.minecraft.green_terracotta": "Terracota verda", "block.minecraft.green_wool": "<PERSON> verda", "block.minecraft.grindstone": "<PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON> a bandolera", "block.minecraft.hay_block": "Balla de fen", "block.minecraft.heavy_core": "Nucleo pesante", "block.minecraft.heavy_weighted_pressure_plate": "Pian a pression per pes grand", "block.minecraft.honey_block": "<PERSON><PERSON> de <PERSON>l", "block.minecraft.honeycomb_block": "<PERSON><PERSON>", "block.minecraft.hopper": "Tremoeusgia", "block.minecraft.horn_coral": "<PERSON>l a corna", "block.minecraft.horn_coral_block": "<PERSON><PERSON> de corall a corna", "block.minecraft.horn_coral_fan": "Gorgonia a corna", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON> a corna a mur", "block.minecraft.ice": "Giazz", "block.minecraft.infested_chiseled_stone_bricks": "Quadrei de preja sigillad infestad", "block.minecraft.infested_cobblestone": "Rizzoeui infestad", "block.minecraft.infested_cracked_stone_bricks": "Quadrei de preja crep infestad", "block.minecraft.infested_deepslate": "Lavagna profonda infestada", "block.minecraft.infested_mossy_stone_bricks": "Quadrei de preja tepos infestad", "block.minecraft.infested_stone": "Preja infestada", "block.minecraft.infested_stone_bricks": "Quadrei de preja infestad", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON> de ferr", "block.minecraft.iron_block": "<PERSON><PERSON>", "block.minecraft.iron_door": "Porta de ferr", "block.minecraft.iron_ore": "Mineral de ferr", "block.minecraft.iron_trapdoor": "Bussera de ferr", "block.minecraft.jack_o_lantern": "Lanterna de zuca", "block.minecraft.jigsaw": "Bloch puzzle", "block.minecraft.jukebox": "Sgira-disch", "block.minecraft.jungle_button": "<PERSON><PERSON>ungla", "block.minecraft.jungle_door": "Porta de la sgiungla", "block.minecraft.jungle_fence": "Stongarda de la sgiungla", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON> de <PERSON> s<PERSON>ungla", "block.minecraft.jungle_hanging_sign": "Insegna de la sgiungla", "block.minecraft.jungle_leaves": "Foeuje de la sgiungla", "block.minecraft.jungle_log": "Bora de la giungla", "block.minecraft.jungle_planks": "<PERSON><PERSON> <PERSON>", "block.minecraft.jungle_pressure_plate": "Pian a pression de la sgiungla", "block.minecraft.jungle_sapling": "But de la sgiungla", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON> de <PERSON>ungla", "block.minecraft.jungle_slab": "Pioda de la sgiungla", "block.minecraft.jungle_stairs": "Basei de la sgiungla", "block.minecraft.jungle_trapdoor": "Bussera de la sgiungla", "block.minecraft.jungle_wall_hanging_sign": "Insegna a mur de la sgiungla", "block.minecraft.jungle_wall_sign": "Cartell de mur de la s<PERSON>ungla", "block.minecraft.jungle_wood": "Legn de la giungla", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "Picol d'alga", "block.minecraft.ladder": "<PERSON><PERSON> a man", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "<PERSON><PERSON> de lapis lazuli", "block.minecraft.lapis_ore": "Mineral de lapislazzer", "block.minecraft.large_amethyst_bud": "Gemma de ametista granda", "block.minecraft.large_fern": "Files gigant", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Calderon de lava", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "<PERSON><PERSON>", "block.minecraft.lever": "Leva", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "Bandera bloeu ciar", "block.minecraft.light_blue_bed": "Lecc bloeu ciar", "block.minecraft.light_blue_candle": "<PERSON><PERSON> bloeu ciara", "block.minecraft.light_blue_candle_cake": "Torta con candila celesta", "block.minecraft.light_blue_carpet": "<PERSON><PERSON> bloeu ciar", "block.minecraft.light_blue_concrete": "Ciment bloeu ciar", "block.minecraft.light_blue_concrete_powder": "Polver de ciment bloeu ciar", "block.minecraft.light_blue_glazed_terracotta": "Terracota smaltada bloeu ciar", "block.minecraft.light_blue_shulker_box": "Scatola de shulker bloeu ciar", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> blo<PERSON> c<PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Panell de veder bloeu ciar", "block.minecraft.light_blue_terracotta": "Terracota bloeu ciar", "block.minecraft.light_blue_wool": "<PERSON> blo<PERSON> c<PERSON>", "block.minecraft.light_gray_banner": "Bandera grisa ciar", "block.minecraft.light_gray_bed": "Lecc gris ciar", "block.minecraft.light_gray_candle": "Candira grisa ciara", "block.minecraft.light_gray_candle_cake": "Torta con candile grisa ciara", "block.minecraft.light_gray_carpet": "Tapet gris ciar", "block.minecraft.light_gray_concrete": "Ciment gris ciar", "block.minecraft.light_gray_concrete_powder": "Polver de ciment grisa ciar", "block.minecraft.light_gray_glazed_terracotta": "Terracota smaltada grisa ciar", "block.minecraft.light_gray_shulker_box": "Scatola de shulker grisa ciar", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> gris ciar", "block.minecraft.light_gray_stained_glass_pane": "Panell de veder gris ciar", "block.minecraft.light_gray_terracotta": "Terracota grisa ciar", "block.minecraft.light_gray_wool": "<PERSON> grisa ciar", "block.minecraft.light_weighted_pressure_plate": "Pian a pression per pes piscinin", "block.minecraft.lightning_rod": "Parasaete", "block.minecraft.lilac": "Lilà", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Bandera lime", "block.minecraft.lime_bed": "Lecc lime", "block.minecraft.lime_candle": "Candira lime", "block.minecraft.lime_candle_cake": "Torta con candila lime", "block.minecraft.lime_carpet": "Tapet lime", "block.minecraft.lime_concrete": "Ciment lime", "block.minecraft.lime_concrete_powder": "Polver de ciment lime", "block.minecraft.lime_glazed_terracotta": "Terracota smaltada lime", "block.minecraft.lime_shulker_box": "Scatola de shulker lime", "block.minecraft.lime_stained_glass": "Veder lime", "block.minecraft.lime_stained_glass_pane": "Panell de veder lime", "block.minecraft.lime_terracotta": "Terracota lime", "block.minecraft.lime_wool": "Lana lime", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Telar", "block.minecraft.magenta_banner": "Bandera magenta", "block.minecraft.magenta_bed": "Lecc magenta", "block.minecraft.magenta_candle": "Candira magenta", "block.minecraft.magenta_candle_cake": "Torta con candila magenta", "block.minecraft.magenta_carpet": "Tapet magenta", "block.minecraft.magenta_concrete": "Ciment magenta", "block.minecraft.magenta_concrete_powder": "Polver de ciment magenta", "block.minecraft.magenta_glazed_terracotta": "Terracota smaltada magenta", "block.minecraft.magenta_shulker_box": "Scatola de shulker magenta", "block.minecraft.magenta_stained_glass": "Veder magenta", "block.minecraft.magenta_stained_glass_pane": "Panell de veder magenta", "block.minecraft.magenta_terracotta": "Terracota magenta", "block.minecraft.magenta_wool": "Lana magenta", "block.minecraft.magma_block": "Bloch de magma", "block.minecraft.mangrove_button": "Boton de mangrovia", "block.minecraft.mangrove_door": "Porta de mangrovia", "block.minecraft.mangrove_fence": "Stongarda de mangrovia", "block.minecraft.mangrove_fence_gate": "Restell de mangrovia", "block.minecraft.mangrove_hanging_sign": "Insegna de mangrovia", "block.minecraft.mangrove_leaves": "Foeuje de mangrovia", "block.minecraft.mangrove_log": "Bora de mangrovia", "block.minecraft.mangrove_planks": "Ass de mangrovia", "block.minecraft.mangrove_pressure_plate": "Pian a pression de mangrovia", "block.minecraft.mangrove_propagule": "Propapagul de mangrovia", "block.minecraft.mangrove_roots": "<PERSON><PERSON>", "block.minecraft.mangrove_sign": "Cartell de mangrovia", "block.minecraft.mangrove_slab": "Pioda de mangrovia", "block.minecraft.mangrove_stairs": "Basei de mangrovia", "block.minecraft.mangrove_trapdoor": "Bussera de mangrovia", "block.minecraft.mangrove_wall_hanging_sign": "Insegna a mur de mangrovia", "block.minecraft.mangrove_wall_sign": "<PERSON>tel<PERSON> de mur de mangrovia", "block.minecraft.mangrove_wood": "Legn de mangrovia", "block.minecraft.medium_amethyst_bud": "Gemma de ametista media", "block.minecraft.melon": "Inguria", "block.minecraft.melon_stem": "Pianta de inguria", "block.minecraft.moss_block": "<PERSON><PERSON>", "block.minecraft.moss_carpet": "Ta<PERSON> de tepa", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON> de rizzoeui tepos", "block.minecraft.mossy_cobblestone_slab": "Pioda de rizzoeui tepos", "block.minecraft.mossy_cobblestone_stairs": "Basei de rizzoeui tepos", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> <PERSON> riz<PERSON><PERSON>i tepos", "block.minecraft.mossy_stone_brick_slab": "Pioda de quadrei de preja tepos", "block.minecraft.mossy_stone_brick_stairs": "Basei de quadrei de preja tepos", "block.minecraft.mossy_stone_brick_wall": "Mur de quadrei de preja tepos", "block.minecraft.mossy_stone_bricks": "Quadrei de preja tepos", "block.minecraft.moving_piston": "Piston in moviment", "block.minecraft.mud": "Palta", "block.minecraft.mud_brick_slab": "Pioda de quadrei de palta", "block.minecraft.mud_brick_stairs": "Basei de quadrei de palta", "block.minecraft.mud_brick_wall": "<PERSON>r de quadrei de palta", "block.minecraft.mud_bricks": "Quadrei de palta", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON> paltus<PERSON> de mangrovia", "block.minecraft.mushroom_stem": "Picol de fonsg", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Stongarda de quadrei del Nether", "block.minecraft.nether_brick_slab": "Pioda de quadrei del Nether", "block.minecraft.nether_brick_stairs": "Basei de quadrei del Nether", "block.minecraft.nether_brick_wall": "<PERSON>r de quadrei del Nether", "block.minecraft.nether_bricks": "Quadrei del Nether", "block.minecraft.nether_gold_ore": "Mineral d'or del Nether", "block.minecraft.nether_portal": "Porta del Nether", "block.minecraft.nether_quartz_ore": "Mineral de quarz del Nether", "block.minecraft.nether_sprouts": "But del Nether", "block.minecraft.nether_wart": "Brossera del Nether", "block.minecraft.nether_wart_block": "<PERSON><PERSON> br<PERSON> del Nether", "block.minecraft.netherite_block": "<PERSON><PERSON> de netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloch sonador", "block.minecraft.oak_button": "<PERSON><PERSON>", "block.minecraft.oak_door": "Porta de cerr", "block.minecraft.oak_fence": "Stongarda de cerr", "block.minecraft.oak_fence_gate": "<PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "Insegna de cerr", "block.minecraft.oak_leaves": "Foeuje de c<PERSON>r", "block.minecraft.oak_log": "<PERSON><PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON> <PERSON>", "block.minecraft.oak_pressure_plate": "Pian a pression de cerr", "block.minecraft.oak_sapling": "But de cerr", "block.minecraft.oak_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_slab": "<PERSON><PERSON> de c<PERSON>", "block.minecraft.oak_stairs": "Basei de cerr", "block.minecraft.oak_trapdoor": "Bussera de cerr", "block.minecraft.oak_wall_hanging_sign": "Insegna a mur de cerr", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON> de mur de cerr", "block.minecraft.oak_wood": "<PERSON><PERSON> de cerr", "block.minecraft.observer": "Osservador", "block.minecraft.obsidian": "Ossidian<PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON>lus ocra", "block.minecraft.ominous_banner": "Bandera de la deslipa", "block.minecraft.open_eyeblossom": "Occhidea aperta", "block.minecraft.orange_banner": "Bandera naranza", "block.minecraft.orange_bed": "Lecc naranz", "block.minecraft.orange_candle": "<PERSON><PERSON>", "block.minecraft.orange_candle_cake": "Torta con candila naranza", "block.minecraft.orange_carpet": "Tapet naranz", "block.minecraft.orange_concrete": "Ciment naranz", "block.minecraft.orange_concrete_powder": "Polver de ciment naranz", "block.minecraft.orange_glazed_terracotta": "Terracota smaltada naranza", "block.minecraft.orange_shulker_box": "Scatola de shulker naranza", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panell de veder naranz", "block.minecraft.orange_terracotta": "Terracota naranza", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> naranz", "block.minecraft.orange_wool": "<PERSON>", "block.minecraft.oxeye_daisy": "Margh<PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON> cese<PERSON>to ossi<PERSON>to", "block.minecraft.oxidized_copper": "Ram o<PERSON>", "block.minecraft.oxidized_copper_bulb": "Lampada di rame ossidato", "block.minecraft.oxidized_copper_door": "Porta di rame ossidato", "block.minecraft.oxidized_copper_grate": "Grata di rame ossidato", "block.minecraft.oxidized_copper_trapdoor": "Botola di rame ossidato", "block.minecraft.oxidized_cut_copper": "<PERSON>", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON> de ram tajad o<PERSON>", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON><PERSON> de ram tajad o<PERSON>", "block.minecraft.packed_ice": "Giazz compat", "block.minecraft.packed_mud": "Palta compata", "block.minecraft.pale_hanging_moss": "Muschio pendente pallido", "block.minecraft.pale_moss_block": "Blocco di muschio pallido", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON> di muschio pallido", "block.minecraft.pale_oak_button": "Pulsante di quercia pallida", "block.minecraft.pale_oak_door": "Porta di quercia pallida", "block.minecraft.pale_oak_fence": "Staccionata di quercia pallida", "block.minecraft.pale_oak_fence_gate": "Cancelletto di quercia pallida", "block.minecraft.pale_oak_hanging_sign": "Insegna di quercia pallida", "block.minecraft.pale_oak_leaves": "Foglie di quercia pallida", "block.minecraft.pale_oak_log": "<PERSON>ra de cerr slavi", "block.minecraft.pale_oak_planks": "Assi di quercia pallida", "block.minecraft.pale_oak_pressure_plate": "Pedana a pressione di quercia pallida", "block.minecraft.pale_oak_sapling": "Arboscello di quercia pallida", "block.minecraft.pale_oak_sign": "Cartello di quercia pallida", "block.minecraft.pale_oak_slab": "Lastra di quercia pallida", "block.minecraft.pale_oak_stairs": "Scalini di quercia pallida", "block.minecraft.pale_oak_trapdoor": "Botola di quercia pallida", "block.minecraft.pale_oak_wall_hanging_sign": "Insegna di quercia pallida a parete", "block.minecraft.pale_oak_wall_sign": "Cartello di quercia pallida a parete", "block.minecraft.pale_oak_wood": "Legno di quercia pallida", "block.minecraft.pearlescent_froglight": "Rana-lus color perla", "block.minecraft.peony": "Peonia", "block.minecraft.petrified_oak_slab": "Pioda de cerr de preja", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON> de piglin de mur", "block.minecraft.pink_banner": "Bandera roeusa", "block.minecraft.pink_bed": "Lecc roeusa", "block.minecraft.pink_candle": "<PERSON><PERSON> roe<PERSON>", "block.minecraft.pink_candle_cake": "Torta con candila roeusa", "block.minecraft.pink_carpet": "<PERSON><PERSON> roeusa", "block.minecraft.pink_concrete": "Ciment roeusa", "block.minecraft.pink_concrete_powder": "Polver de ciment roeusa", "block.minecraft.pink_glazed_terracotta": "Terracota smaltada roeusa", "block.minecraft.pink_petals": "<PERSON><PERSON> rosa", "block.minecraft.pink_shulker_box": "Scatola de shulker roeusa", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> roe<PERSON>", "block.minecraft.pink_stained_glass_pane": "Panell de veder roeusa", "block.minecraft.pink_terracotta": "Terracota roeusa", "block.minecraft.pink_tulip": "Tolipan roeusa", "block.minecraft.pink_wool": "<PERSON> roe<PERSON>", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Coo de piston", "block.minecraft.pitcher_crop": "Coltura di pianta carnivora", "block.minecraft.pitcher_plant": "Pianta carnivora", "block.minecraft.player_head": "Coo de sgiugador", "block.minecraft.player_head.named": "Coo del %s", "block.minecraft.player_wall_head": "Coo de sgiugador de mur", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "Speleotema guzz", "block.minecraft.polished_andesite": "Andesit soliada", "block.minecraft.polished_andesite_slab": "Pioda de andesit soliada", "block.minecraft.polished_andesite_stairs": "Basei de andesit soliada", "block.minecraft.polished_basalt": "Basalt soliad", "block.minecraft.polished_blackstone": "Prejanegra soliada", "block.minecraft.polished_blackstone_brick_slab": "Pioda de quadrei de prejanegra soliada", "block.minecraft.polished_blackstone_brick_stairs": "Basei de quadrei de prejanegra soliada", "block.minecraft.polished_blackstone_brick_wall": "Mur de quadrei de prejanegra soliada", "block.minecraft.polished_blackstone_bricks": "Quadrei de prejanegra soliada", "block.minecraft.polished_blackstone_button": "Boton de prejanegra soliada", "block.minecraft.polished_blackstone_pressure_plate": "Pian a pression de prejanegra soliada", "block.minecraft.polished_blackstone_slab": "Pioda de prejanegra soliada", "block.minecraft.polished_blackstone_stairs": "Basei de prejanegra soliada", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> <PERSON> prejan<PERSON>ra soliada", "block.minecraft.polished_deepslate": "Lavagna profonda soliada", "block.minecraft.polished_deepslate_slab": "Pioda de lavagna profonda soliada", "block.minecraft.polished_deepslate_stairs": "Basei de lavagna profonda soliada", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> de lavagna profonda soliada", "block.minecraft.polished_diorite": "Serizz soliad", "block.minecraft.polished_diorite_slab": "Pioda de serizz soliad", "block.minecraft.polished_diorite_stairs": "Basei de serizz soliad", "block.minecraft.polished_granite": "<PERSON><PERSON>eul soliad", "block.minecraft.polished_granite_slab": "Pioda de miaroeul soliad", "block.minecraft.polished_granite_stairs": "Basei de miaroeul soliad", "block.minecraft.polished_tuff": "<PERSON><PERSON> le<PERSON>", "block.minecraft.polished_tuff_slab": "Lastra di tufo levigato", "block.minecraft.polished_tuff_stairs": "Scalini di tufo levigato", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON> di tufo levigato", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Pom de terra", "block.minecraft.potted_acacia_sapling": "Vas con but de rubinia", "block.minecraft.potted_allium": "Vas con fior de ai", "block.minecraft.potted_azalea_bush": "Vas con azalea", "block.minecraft.potted_azure_bluet": "Vas con houstonia", "block.minecraft.potted_bamboo": "Vas con bamboo", "block.minecraft.potted_birch_sapling": "Vas con but de biolla", "block.minecraft.potted_blue_orchid": "Vas cont orchidea bloeu", "block.minecraft.potted_brown_mushroom": "Vas con fonsg marron", "block.minecraft.potted_cactus": "Vas con cactus", "block.minecraft.potted_cherry_sapling": "Vas con but de scireser", "block.minecraft.potted_closed_eyeblossom": "Vaso con occhidea chiusa", "block.minecraft.potted_cornflower": "Vas con lori", "block.minecraft.potted_crimson_fungus": "Vas con fonsg cremes", "block.minecraft.potted_crimson_roots": "Vas con radis cremese", "block.minecraft.potted_dandelion": "Vas con bofanella", "block.minecraft.potted_dark_oak_sapling": "Vas con but de rogora", "block.minecraft.potted_dead_bush": "Vas con pianta seca", "block.minecraft.potted_fern": "Vas con files", "block.minecraft.potted_flowering_azalea_bush": "Vas con azalea fiorida", "block.minecraft.potted_jungle_sapling": "Vas con but de la sgiungla", "block.minecraft.potted_lily_of_the_valley": "Vas con moneghin", "block.minecraft.potted_mangrove_propagule": "Vas con propagul de mangrovia", "block.minecraft.potted_oak_sapling": "Vas con but de cerr", "block.minecraft.potted_open_eyeblossom": "Vaso con occhidea aperta", "block.minecraft.potted_orange_tulip": "Vas con tolipan naranz", "block.minecraft.potted_oxeye_daisy": "Vas con margherita", "block.minecraft.potted_pale_oak_sapling": "Vaso con arboscello di quercia pallida", "block.minecraft.potted_pink_tulip": "Vas con tolipan roeusa", "block.minecraft.potted_poppy": "Vas con popolana", "block.minecraft.potted_red_mushroom": "Vas con fonsg ross", "block.minecraft.potted_red_tulip": "Vas con to<PERSON>an ross", "block.minecraft.potted_spruce_sapling": "Vas con but de abiezz", "block.minecraft.potted_torchflower": "Vas con torcia-fior", "block.minecraft.potted_warped_fungus": "Vas con fonsg desformad", "block.minecraft.potted_warped_roots": "Vas con radis desformade", "block.minecraft.potted_white_tulip": "Vas con tolipan bianch", "block.minecraft.potted_wither_rose": "Vas con roeusa del Wither", "block.minecraft.powder_snow": "Fioca in polver", "block.minecraft.powder_snow_cauldron": "Calderon de fioca in polver", "block.minecraft.powered_rail": "Binari motorizad", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Pioda de quadrei de prismarina", "block.minecraft.prismarine_brick_stairs": "Basei de quadrei de prismarina", "block.minecraft.prismarine_bricks": "Quadrei de prismarina", "block.minecraft.prismarine_slab": "Pioda de prismarina", "block.minecraft.prismarine_stairs": "Basei de prismarina", "block.minecraft.prismarine_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Pianta de zuca", "block.minecraft.purple_banner": "Bandera vioeula", "block.minecraft.purple_bed": "Lecc vioeula", "block.minecraft.purple_candle": "<PERSON><PERSON> v<PERSON>", "block.minecraft.purple_candle_cake": "Torta con candila vioeula", "block.minecraft.purple_carpet": "<PERSON><PERSON> vioeula", "block.minecraft.purple_concrete": "Ciment vioeula", "block.minecraft.purple_concrete_powder": "Polver de ciment vioeula", "block.minecraft.purple_glazed_terracotta": "Terracota smaltada vioeula", "block.minecraft.purple_shulker_box": "Scatola de shulker vioeula", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "Panell de veder vioeula", "block.minecraft.purple_terracotta": "Terracota vioeula", "block.minecraft.purple_wool": "<PERSON> vio<PERSON>", "block.minecraft.purpur_block": "<PERSON><PERSON> purpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON> de purpur", "block.minecraft.purpur_slab": "Pioda de purpur", "block.minecraft.purpur_stairs": "Basei de purpur", "block.minecraft.quartz_block": "<PERSON><PERSON>", "block.minecraft.quartz_bricks": "Quadrei de quarz", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "<PERSON>oda de quarz", "block.minecraft.quartz_stairs": "Basei de quarz", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Bloch de mineral de ram", "block.minecraft.raw_gold_block": "Bloch de mineral d'or", "block.minecraft.raw_iron_block": "Bloch de mineral de ferr", "block.minecraft.red_banner": "Bandera rossa", "block.minecraft.red_bed": "Le<PERSON> ross", "block.minecraft.red_candle": "Candira rossa", "block.minecraft.red_candle_cake": "Torta con candila rossa", "block.minecraft.red_carpet": "<PERSON><PERSON> ross", "block.minecraft.red_concrete": "Ciment ross", "block.minecraft.red_concrete_powder": "Polver de ciment rossa", "block.minecraft.red_glazed_terracotta": "Terracota smaltada rossa", "block.minecraft.red_mushroom": "Fons<PERSON> ross", "block.minecraft.red_mushroom_block": "<PERSON><PERSON> de fonsg ross", "block.minecraft.red_nether_brick_slab": "Pioda de quadrei ross del Nether", "block.minecraft.red_nether_brick_stairs": "Basei de quadrei ross del Nether", "block.minecraft.red_nether_brick_wall": "<PERSON>r de quadrei ross del Nether", "block.minecraft.red_nether_bricks": "Quadrei ross del <PERSON>her", "block.minecraft.red_sand": "Sabia rossa", "block.minecraft.red_sandstone": "Molera rossa", "block.minecraft.red_sandstone_slab": "Pioda de molera rossa", "block.minecraft.red_sandstone_stairs": "Basei de molera rossa", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> de molera rossa", "block.minecraft.red_shulker_box": "Scatola de shulker rossa", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> ross", "block.minecraft.red_stained_glass_pane": "<PERSON>l de veder ross", "block.minecraft.red_terracotta": "Terracota rossa", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> ross", "block.minecraft.red_wool": "<PERSON> rossa", "block.minecraft.redstone_block": "Bloch de preja rossa", "block.minecraft.redstone_lamp": "Lampeda de prejarossa", "block.minecraft.redstone_ore": "Mineral de prejarossa", "block.minecraft.redstone_torch": "Torcia de prejarossa", "block.minecraft.redstone_wall_torch": "Torcia de prejarossa de mur", "block.minecraft.redstone_wire": "Fil de prejarossa", "block.minecraft.reinforced_deepslate": "Lavagna profonda renforzada", "block.minecraft.repeater": "<PERSON>etidor <PERSON>", "block.minecraft.repeating_command_block": "Bloch di comand a repetizzion", "block.minecraft.resin_block": "Blocco di resina", "block.minecraft.resin_brick_slab": "Lastra di mattoni di resina", "block.minecraft.resin_brick_stairs": "Scalini di mattoni di resina", "block.minecraft.resin_brick_wall": "<PERSON><PERSON><PERSON> di mattoni di resina", "block.minecraft.resin_bricks": "<PERSON><PERSON> di resina", "block.minecraft.resin_clump": "Grumo di resina", "block.minecraft.respawn_anchor": "Ancora de la renassida", "block.minecraft.rooted_dirt": "Terra con radis", "block.minecraft.rose_bush": "Boscal de roeuse", "block.minecraft.sand": "Sabia", "block.minecraft.sandstone": "Molera", "block.minecraft.sandstone_slab": "<PERSON>oda de molera", "block.minecraft.sandstone_stairs": "Basei de molera", "block.minecraft.sandstone_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador de sculk", "block.minecraft.sculk_sensor": "Sensor sculk", "block.minecraft.sculk_shrieker": "Sculk vosador", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Lanterna del mar", "block.minecraft.sea_pickle": "Cocumer del mar", "block.minecraft.seagrass": "Erba del mar", "block.minecraft.set_spawn": "Pont de renassida configurad", "block.minecraft.short_dry_grass": "<PERSON><PERSON>a secca bassa", "block.minecraft.short_grass": "<PERSON><PERSON>a bassa", "block.minecraft.shroomlight": "Sberlus-fonsg", "block.minecraft.shulker_box": "Scatola <PERSON>", "block.minecraft.skeleton_skull": "<PERSON><PERSON> <PERSON>ter", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON> de schelter de mur", "block.minecraft.slime_block": "Bloch de slime", "block.minecraft.small_amethyst_bud": "Gemma de ametista piscinina", "block.minecraft.small_dripleaf": "Foeuja-gota picola", "block.minecraft.smithing_table": "Tavol del ferrer", "block.minecraft.smoker": "Afumegador", "block.minecraft.smooth_basalt": "Basalt soli", "block.minecraft.smooth_quartz": "<PERSON><PERSON> de quarz liss", "block.minecraft.smooth_quartz_slab": "Pioda de quarz liss", "block.minecraft.smooth_quartz_stairs": "Basei de quarz liss", "block.minecraft.smooth_red_sandstone": "Molera rossa lissa", "block.minecraft.smooth_red_sandstone_slab": "Pioda de molera rossa lissa", "block.minecraft.smooth_red_sandstone_stairs": "Basei de molera rossa soliada", "block.minecraft.smooth_sandstone": "<PERSON><PERSON>a lissa", "block.minecraft.smooth_sandstone_slab": "Pioda de molera lissa", "block.minecraft.smooth_sandstone_stairs": "Basei de molera lissa", "block.minecraft.smooth_stone": "Preja soliada", "block.minecraft.smooth_stone_slab": "Pioda de preja lissa", "block.minecraft.sniffer_egg": "Oeuv de nasador", "block.minecraft.snow": "Fioca", "block.minecraft.snow_block": "<PERSON><PERSON> de fi<PERSON>", "block.minecraft.soul_campfire": "Falò de l'anima", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON> di anime", "block.minecraft.soul_lantern": "Lanterna di anime", "block.minecraft.soul_sand": "Sabia di anime", "block.minecraft.soul_soil": "Terra di anime", "block.minecraft.soul_torch": "Torcia di anime", "block.minecraft.soul_wall_torch": "Torcia di anime de mur", "block.minecraft.spawn.not_valid": "Te gh'heet nissun lecc o ancora de la renassida cargada, o inn intrlciad", "block.minecraft.spawner": "Generatore di mostri", "block.minecraft.spawner.desc1": "Interazione con uovo generatore:", "block.minecraft.spawner.desc2": "Imposta il tipo di creatura", "block.minecraft.sponge": "S<PERSON>nga", "block.minecraft.spore_blossom": "Fior produtor de spore", "block.minecraft.spruce_button": "Boton de abiezz", "block.minecraft.spruce_door": "Porta de abiezz", "block.minecraft.spruce_fence": "Stongarda de abiezz", "block.minecraft.spruce_fence_gate": "Restell de abiezz", "block.minecraft.spruce_hanging_sign": "Insegna de abiezz", "block.minecraft.spruce_leaves": "Foeuje de abiezz", "block.minecraft.spruce_log": "Bora de a<PERSON>zz", "block.minecraft.spruce_planks": "<PERSON><PERSON> de a<PERSON>", "block.minecraft.spruce_pressure_plate": "Pian a pression de abiezz", "block.minecraft.spruce_sapling": "But de abiezz", "block.minecraft.spruce_sign": "Cartell de abiezz", "block.minecraft.spruce_slab": "Pioda de abiezz", "block.minecraft.spruce_stairs": "Basei de abiezz", "block.minecraft.spruce_trapdoor": "Bussera de abiezz", "block.minecraft.spruce_wall_hanging_sign": "Insegna a mur de abiezz", "block.minecraft.spruce_wall_sign": "<PERSON>tel<PERSON> de mur de abiezz", "block.minecraft.spruce_wood": "Legn de abiezz", "block.minecraft.sticky_piston": "<PERSON><PERSON> imp<PERSON>", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Pioda de quadrei de preja", "block.minecraft.stone_brick_stairs": "Basei de quadrei de preja", "block.minecraft.stone_brick_wall": "<PERSON>r de quadrei de preja", "block.minecraft.stone_bricks": "Quadrei de preja", "block.minecraft.stone_button": "<PERSON><PERSON> de pre<PERSON>", "block.minecraft.stone_pressure_plate": "Pian a pression de preja", "block.minecraft.stone_slab": "Pioda de preja", "block.minecraft.stone_stairs": "Basei de preja", "block.minecraft.stonecutter": "Taja-preje", "block.minecraft.stripped_acacia_log": "Bora de rubinia scarpada", "block.minecraft.stripped_acacia_wood": "Legn de rubinia scarpad", "block.minecraft.stripped_bamboo_block": "Bloch de bamboo scarpad", "block.minecraft.stripped_birch_log": "<PERSON>ra de biolla scarpada", "block.minecraft.stripped_birch_wood": "Legn de biolla scarpad", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON> de cireser scarpad", "block.minecraft.stripped_cherry_wood": "Legn de scireser scarpad", "block.minecraft.stripped_crimson_hyphae": "Ife cremese scarpade", "block.minecraft.stripped_crimson_stem": "Gamba cremesa scarpada", "block.minecraft.stripped_dark_oak_log": "Bora de rogora scarpada", "block.minecraft.stripped_dark_oak_wood": "Legn de rogora scarpad", "block.minecraft.stripped_jungle_log": "Bora de la giungla scarpada", "block.minecraft.stripped_jungle_wood": "Legn de la giungla scarpad", "block.minecraft.stripped_mangrove_log": "Bora de mangrovia scarpada", "block.minecraft.stripped_mangrove_wood": "Legn de mangrovia scarpad", "block.minecraft.stripped_oak_log": "<PERSON><PERSON> de cerr scarpada", "block.minecraft.stripped_oak_wood": "Legn de cerr scarpad", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON> de cerr slavi scarpada", "block.minecraft.stripped_pale_oak_wood": "Legno di quercia pallida scortecciato", "block.minecraft.stripped_spruce_log": "Bora de abiezz scarpada", "block.minecraft.stripped_spruce_wood": "Legn de abiezz scarpad", "block.minecraft.stripped_warped_hyphae": "Ife desformade scarpade", "block.minecraft.stripped_warped_stem": "Gamba desformada scarpada", "block.minecraft.structure_block": "<PERSON><PERSON> strutura", "block.minecraft.structure_void": "Voeui de la strutura", "block.minecraft.sugar_cane": "<PERSON><PERSON>", "block.minecraft.sunflower": "Girasol", "block.minecraft.suspicious_gravel": "Gera sospeta", "block.minecraft.suspicious_sand": "Sabia sospeta", "block.minecraft.sweet_berry_bush": "Boscal de frut dolz", "block.minecraft.tall_dry_grass": "Erba secca alta", "block.minecraft.tall_grass": "Erba volta", "block.minecraft.tall_seagrass": "Erba volta del mar", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "Blocco di test", "block.minecraft.test_instance_block": "<PERSON><PERSON> di collaudo", "block.minecraft.tinted_glass": "Veder colorad", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Le esplosioni di TNT sono disattivate", "block.minecraft.torch": "Torcia", "block.minecraft.torchflower": "Torcia-fior", "block.minecraft.torchflower_crop": "Pianta de torcia-fior", "block.minecraft.trapped_chest": "Cesta-trapola", "block.minecraft.trial_spawner": "Generatore della sfida", "block.minecraft.tripwire": "Fil trapola", "block.minecraft.tripwire_hook": "Rampin de fil trapola", "block.minecraft.tube_coral": "<PERSON>l a tubo", "block.minecraft.tube_coral_block": "<PERSON><PERSON> de corall a tubo", "block.minecraft.tube_coral_fan": "Gorgonia a tubo", "block.minecraft.tube_coral_wall_fan": "Go<PERSON><PERSON> a tubo a mur", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Lastra di mattoni di tufo", "block.minecraft.tuff_brick_stairs": "Scalini di mattoni di tufo", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON> di mattoni di tufo", "block.minecraft.tuff_bricks": "Matt<PERSON> di tufo", "block.minecraft.tuff_slab": "Lastra di tufo", "block.minecraft.tuff_stairs": "Scalini di tufo", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON> di tufo", "block.minecraft.turtle_egg": "Oeuv de bissa scudellera", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON> intortia", "block.minecraft.twisting_vines_plant": "Pianta de ergna intortia", "block.minecraft.vault": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON> verden<PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Aria del voeui", "block.minecraft.wall_torch": "<PERSON><PERSON> de mur", "block.minecraft.warped_button": "<PERSON>ton desformad", "block.minecraft.warped_door": "Porta desformada", "block.minecraft.warped_fence": "Stongarda desformada", "block.minecraft.warped_fence_gate": "Restell desformad", "block.minecraft.warped_fungus": "Fonsg desformad", "block.minecraft.warped_hanging_sign": "Insegna desformada", "block.minecraft.warped_hyphae": "Ife desformade", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "Pian a pression desformad", "block.minecraft.warped_roots": "<PERSON><PERSON>", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> desform<PERSON>", "block.minecraft.warped_slab": "Pioda desformada", "block.minecraft.warped_stairs": "Basei desformad", "block.minecraft.warped_stem": "Gamba desformada", "block.minecraft.warped_trapdoor": "Bussera desformada", "block.minecraft.warped_wall_hanging_sign": "Insegna a mur desformada", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> de mur desformad", "block.minecraft.warped_wart_block": "Bloch de brossera desformada", "block.minecraft.water": "Aqua", "block.minecraft.water_cauldron": "Calderon de aqua", "block.minecraft.waxed_chiseled_copper": "Ram<PERSON> cesellato cerato", "block.minecraft.waxed_copper_block": "<PERSON><PERSON> incerad", "block.minecraft.waxed_copper_bulb": "Lampada di rame cerato", "block.minecraft.waxed_copper_door": "Porta di rame cerato", "block.minecraft.waxed_copper_grate": "Grata di rame cerato", "block.minecraft.waxed_copper_trapdoor": "Botola di rame cerato", "block.minecraft.waxed_cut_copper": "<PERSON> tajad incerad", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON> de ram tajad incerad", "block.minecraft.waxed_cut_copper_stairs": "<PERSON><PERSON> de ram tajad incerad", "block.minecraft.waxed_exposed_chiseled_copper": "Rame cesellato esposto cerato", "block.minecraft.waxed_exposed_copper": "Ram espost incerad", "block.minecraft.waxed_exposed_copper_bulb": "Lampada di rame esposto cerato", "block.minecraft.waxed_exposed_copper_door": "Porta di rame esposto cerato", "block.minecraft.waxed_exposed_copper_grate": "Grata di rame esposto cerato", "block.minecraft.waxed_exposed_copper_trapdoor": "Botola di rame esposto cerato", "block.minecraft.waxed_exposed_cut_copper": "Ram tajad espost incerad", "block.minecraft.waxed_exposed_cut_copper_slab": "Pioda de ram tajad espost incerad", "block.minecraft.waxed_exposed_cut_copper_stairs": "Basei de ram tajad espost incerad", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON> cesellato ossidato cerato", "block.minecraft.waxed_oxidized_copper": "Ram ossidad incerad", "block.minecraft.waxed_oxidized_copper_bulb": "Lampada di rame ossidato cerato", "block.minecraft.waxed_oxidized_copper_door": "Porta di rame ossidato cerato", "block.minecraft.waxed_oxidized_copper_grate": "Grata di rame ossidato cerato", "block.minecraft.waxed_oxidized_copper_trapdoor": "Botola di rame ossidato cerato", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON> tajad o<PERSON> incerad", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON><PERSON> de ram tajad ossidad incerad", "block.minecraft.waxed_oxidized_cut_copper_stairs": "<PERSON>i de ram tajad ossidad incerad", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON> cesellato corroso cerato", "block.minecraft.waxed_weathered_copper": "<PERSON> smangiad incerad", "block.minecraft.waxed_weathered_copper_bulb": "Lampada di rame corroso cerato", "block.minecraft.waxed_weathered_copper_door": "Porta di rame corroso cerato", "block.minecraft.waxed_weathered_copper_grate": "Grata di rame corroso cerato", "block.minecraft.waxed_weathered_copper_trapdoor": "Botola di rame corroso cerato", "block.minecraft.waxed_weathered_cut_copper": "<PERSON> tajad smangiad incerad", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON> de ram tajad smangiad incerad", "block.minecraft.waxed_weathered_cut_copper_stairs": "<PERSON><PERSON> de ram tajad smangiad incerad", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON> cese<PERSON>to corroso", "block.minecraft.weathered_copper": "<PERSON> r<PERSON>", "block.minecraft.weathered_copper_bulb": "Lampada di rame corroso", "block.minecraft.weathered_copper_door": "Porta di rame corroso", "block.minecraft.weathered_copper_grate": "Grata di rame corroso", "block.minecraft.weathered_copper_trapdoor": "Botola di rame corroso", "block.minecraft.weathered_cut_copper": "<PERSON> ta<PERSON> s<PERSON>d", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON> de ram tajad smangiad", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON> de ram tajad smangiad", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "Pianta de ergna caragnenta", "block.minecraft.wet_sponge": "Sponga masarada", "block.minecraft.wheat": "Forment", "block.minecraft.white_banner": "Bandera bianca", "block.minecraft.white_bed": "Lecc bianch", "block.minecraft.white_candle": "Candira bianca", "block.minecraft.white_candle_cake": "Torta con candila bianca", "block.minecraft.white_carpet": "<PERSON><PERSON> bianch", "block.minecraft.white_concrete": "Ciment bianch", "block.minecraft.white_concrete_powder": "Polver de ciment bianca", "block.minecraft.white_glazed_terracotta": "Terracota smaltada bianca", "block.minecraft.white_shulker_box": "Scatola de shulker bianca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> bianch", "block.minecraft.white_stained_glass_pane": "Panell de veder bianch", "block.minecraft.white_terracotta": "Terracota bianca", "block.minecraft.white_tulip": "Tolip<PERSON> bianch", "block.minecraft.white_wool": "Lana <PERSON>", "block.minecraft.wildflowers": "Fiori di campo", "block.minecraft.wither_rose": "Roeusa del Wither", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON> de schelter wither", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON> de schelter wither a mur", "block.minecraft.yellow_banner": "Bandera sgialda", "block.minecraft.yellow_bed": "Lecc sgiald", "block.minecraft.yellow_candle": "<PERSON><PERSON> s<PERSON>", "block.minecraft.yellow_candle_cake": "Torta con candila sgialda", "block.minecraft.yellow_carpet": "<PERSON><PERSON> s<PERSON>ld", "block.minecraft.yellow_concrete": "Ciment sgiald", "block.minecraft.yellow_concrete_powder": "Polver de ciment sgialda", "block.minecraft.yellow_glazed_terracotta": "Terracota smaltada sgialda", "block.minecraft.yellow_shulker_box": "Scatola de shulker sgialda", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Panell de veder sgiald", "block.minecraft.yellow_terracotta": "Terracota sgialda", "block.minecraft.yellow_wool": "<PERSON> s<PERSON>", "block.minecraft.zombie_head": "<PERSON>o de zombie", "block.minecraft.zombie_wall_head": "Coo de zombie de mur", "book.byAuthor": "de %1$s", "book.edit.title": "Finestra di modifica del libro", "book.editTitle": "Titol del liber:", "book.finalizeButton": "Frima e sarra su", "book.finalizeWarning": "Ocio! Quand che te firmet el liber, te podareet pu modefegàll.", "book.generation.0": "Original", "book.generation.1": "Copia de l'original", "book.generation.2": "Copia de una copia", "book.generation.3": "Sbregad", "book.invalid.tag": "* Eticheta liber mìa valida *", "book.pageIndicator": "Pagina %1$s de %2$s", "book.page_button.next": "Pagina successiva", "book.page_button.previous": "<PERSON><PERSON><PERSON>e", "book.sign.title": "Finestra della firma del libro", "book.sign.titlebox": "<PERSON><PERSON>", "book.signButton": "Firma", "book.view.title": "Finestra di lettura del libro", "build.tooHigh": "El limet de altezza per i costruzzion l'è %s", "chat.cannotSend": "Se po mìa invià el messagg in de la ciciarada", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Schiscia per teleportàss", "chat.copy": "Copia in di apunt", "chat.copy.click": "Schiscia per copià in di apunt", "chat.deleted_marker": "Quell messagg chì in de la ciciarada l'è stad ranzad via del server.", "chat.disabled.chain_broken": "Chat disattivata a causa di una catena spezzata. Prova a riconnetterti.", "chat.disabled.expiredProfileKey": "Chat disattivata a causa della crittografia asimmetrica del profilo scaduta. Prova a riconnetterti.", "chat.disabled.invalid_command_signature": "Firme degli argomenti del comando non previste o mancanti.", "chat.disabled.invalid_signature": "Firma della chat non valida. Prova a riconnetterti.", "chat.disabled.launcher": "Ciciarada smorzada in di opzion del launcher. Se poeu mìa mandà el messagg.", "chat.disabled.missingProfileKey": "Chat disattivata a causa della crittografia asimmetrica del profilo mancante. Prova a riconnetterti.", "chat.disabled.options": "Chat disattivata nelle opzioni del client.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON><PERSON> chat non funzionanti. L'orario del tuo computer è stato modificato?", "chat.disabled.profile": "Chat disattivata nelle impostazioni dell'account. Premi di nuovo «%s» per maggiori informazioni.", "chat.disabled.profile.moreInfo": "Chat disattivata nelle impostazioni dell'account. Impossibile mandare o leggere i messaggi.", "chat.editBox": "ciciarada", "chat.filtered": "Filtrad del server.", "chat.filtered_full": "Il server ha nascosto il tuo messaggio per alcuni giocatori.", "chat.link.confirm": "Te seet segur de dervì el sit?", "chat.link.confirmTrusted": "Te voeulet dervì quell ligam chì o copiàll in di apunt?", "chat.link.open": "<PERSON>va el browser", "chat.link.warning": "Derva mai ligam de sgent che te cognosset mìa!", "chat.queue": "[+ %s linee che calen]", "chat.square_brackets": "[%s]", "chat.tag.error": "Messaggio del server non valido.", "chat.tag.modified": "Messagg modifegad del server. Original:", "chat.tag.not_secure": "Messagg mìa verifegad. El se poeu mìa denonzià.", "chat.tag.system": "Messagg del server. Se poeu mìa segnalà.", "chat.tag.system_single_player": "Messagg del server.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s l'ha finid la sfida %s", "chat.type.advancement.goal": "%s l'è rivad a l'obietiv %s", "chat.type.advancement.task": "%s l'ha fad el progress %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Scrìv a la squadra", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s el dis %s", "chat.validation_error": "Errore di convalida della chat", "chat_screen.message": "Messagg de mandà: %s", "chat_screen.title": "<PERSON><PERSON><PERSON> ciciarada", "chat_screen.usage": "Scrìv un messagg e schiscia \"Invio\" per mandàll", "chunk.toast.checkLog": "Controlla il registro per maggiori dettagli", "chunk.toast.loadFailure": "Impossibile caricare il chunk in %s", "chunk.toast.lowDiskSpace": "Poco spazio su disco!", "chunk.toast.lowDiskSpace.description": "Il salvataggio del mondo potrebbe fallire.", "chunk.toast.saveFailure": "Impossibile salvare il chunk in %s", "clear.failed.multiple": "Gh'è stad trovad nagot in %s sgiugador", "clear.failed.single": "Gh'è stad trovad nagot in %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "<PERSON><PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verd", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON> c<PERSON>", "color.minecraft.lime": "Lime", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Naranz", "color.minecraft.pink": "Roe<PERSON>", "color.minecraft.purple": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.red": "<PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "Sgiald", "command.context.here": "<--[CHÌ]", "command.context.parse_error": "%s in de la posizzion %s: %s", "command.exception": "A se po minga analizà el comand: %s", "command.expected.separator": "Previst un spazzi bianch prima de un noeuv argoment", "command.failed": "A l'è capitad un error imprevist in del tentà de eseguì quell comand chì", "command.forkLimit": "Numero massimo di contesti (%s) raggiunto", "command.unknown.argument": "Argoment del comand minga bon", "command.unknown.command": "Comand minga conossud o minga complet, varda de sota per l'eror", "commands.advancement.criterionNotFound": "El progress %1$s el contegn minga el criteri \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "Impossibil assegnàgh el criteri \"%s\" del progress %s a %s sgi<PERSON>dor perchè i ghe l'hann sgiamò", "commands.advancement.grant.criterion.to.many.success": "El criteri \"%s\" del progress %s el gh'è stad assegnad %s sgiugador", "commands.advancement.grant.criterion.to.one.failure": "Impossibil assegnàgh el criteri \"%s\" del progress %s al / a la %s perchè el / la ghe l'hà sgiamò", "commands.advancement.grant.criterion.to.one.success": "El criteri \"%s\" del progress %s el gh'è stad a assegnad al / a la %s", "commands.advancement.grant.many.to.many.failure": "Impossibil assegnàgh %s progress a %s sgiusgador perchè i gh'ai hann sgiamò", "commands.advancement.grant.many.to.many.success": "%s progress a inn stad assegnad a %s sgiugador", "commands.advancement.grant.many.to.one.failure": "Impossibel assegnà %s progress a %s perchè el/la ghe i ha sgiamò", "commands.advancement.grant.many.to.one.success": "%s progress a inn stad assegnad a %s", "commands.advancement.grant.one.to.many.failure": "Impossibel assegnà el progress %s a %s sgiugador perchè ghe l'hann sgiamò", "commands.advancement.grant.one.to.many.success": "El progress %s a l'è stad assegnad a %s sgiugador", "commands.advancement.grant.one.to.one.failure": "Impossibel assegnà el progress %s a %s perchè el ghe l'ha sgiamò", "commands.advancement.grant.one.to.one.success": "El progress %s a l'è stad assegnad a %s", "commands.advancement.revoke.criterion.to.many.failure": "Impossibil toeugh via el criteri \"%s\" del progress %s a %s sgiugador perch<PERSON> i ghe l'hann minga", "commands.advancement.revoke.criterion.to.many.success": "El criteri \"%s\" del progress %s el gh'è stad tolt via a %s sgiugador", "commands.advancement.revoke.criterion.to.one.failure": "Impossibil toeugh via el criteru \"%s\" del progress %s a %s perchè el / la ghe l'ha minga", "commands.advancement.revoke.criterion.to.one.success": "El criteri \"%s\" del progress %sel gh'è stad tolt via al / a la %s", "commands.advancement.revoke.many.to.many.failure": "Impossibil toeugh via %s progress a %s sgiugador perch<PERSON> i gh'ai hann minga", "commands.advancement.revoke.many.to.many.success": "%s progress a gh' inn stad tolt via a %s sgiugador", "commands.advancement.revoke.many.to.one.failure": "Impossibil toeugh via %s progress al / a la %s perchè el / la gh'ai ha minga", "commands.advancement.revoke.many.to.one.success": "%s progress a gh' inn stad tolt via al / a la %s", "commands.advancement.revoke.one.to.many.failure": "Impossibil toeugh via el progress %s a %s sgiugador perchè i ghe l'hann minga", "commands.advancement.revoke.one.to.many.success": "El progress %s el gh'è stad tolt via a %s sgiusgador", "commands.advancement.revoke.one.to.one.failure": "Impossibil toeugh via el progress %s al / a la %s perchè el / la ghe l'hà minga", "commands.advancement.revoke.one.to.one.success": "El progress %s el gh'è stad tolt via al / a la %s", "commands.attribute.base_value.get.success": "El valor de partenza de l'atribut %s de l'entità %s l'è %s", "commands.attribute.base_value.reset.success": "Il valore base dell'attributo %s dell'entità %s è stato impostato al valore predefinito %s", "commands.attribute.base_value.set.success": "El valor de partenza %s de l'entità %s l'è stad impostad per el / la %s", "commands.attribute.failed.entity": "%s a l'è ona entità minga bona per quell comand chì", "commands.attribute.failed.modifier_already_present": "El modifegador %s l'è sgiamò aplicad a l'atribud %s de l'entità %s", "commands.attribute.failed.no_attribute": "L'entità %s la gh'hà minga l'atribut %s", "commands.attribute.failed.no_modifier": "L'atribut %s de l'entità %s el gh'ha minga el modifegador %s", "commands.attribute.modifier.add.success": "El modifegador %s l'è stad aplicad a l'atribut %s de l'entità %s", "commands.attribute.modifier.remove.success": "El modifegador %s a l'è stad cavad via de l'atribut %s de l'entità %s", "commands.attribute.modifier.value.get.success": "El valor del modifegador %s de l'atribut %s de l'entità %s a l'è %s", "commands.attribute.value.get.success": "El valor de l'atribut %s de l'entità %s l'è %s", "commands.ban.failed": "Nient a l'è cambiad. El sgiugador a l'è sgiamò stad bandid", "commands.ban.success": "%s a l'è stad/a bandid/a: %s", "commands.banip.failed": "Nient a l'è cambiad. Quell indirizz IP chì a l'è stad sgiamò bandid", "commands.banip.info": "Questo ban si applica a %s giocatore/i: %s", "commands.banip.invalid": "Indirizz IP invalid o sgiugador descognossud", "commands.banip.success": "L'indirizz IP %s a l'è stad bandid: %s", "commands.banlist.entry": "%s a l'è stad/a bandid/a del/a %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "commands.banlist.list": "%s ban:", "commands.banlist.none": "A gh'inn no cunt bandid", "commands.bossbar.create.failed": "L'esist sgiamò una barra boss con ID \"%s\"", "commands.bossbar.create.success": "La barra boss personalizada %s a l'è stada fada su", "commands.bossbar.get.max": "El valor massim de la barra boss personalizada %s a l'è de %s", "commands.bossbar.get.players.none": "La barra capo %s la gh'ha nissun sgiugador in linea", "commands.bossbar.get.players.some": "La barra boss %s ha al momento %s giocatore/i associato/i online: %3$s", "commands.bossbar.get.value": "El valor de la barra boss personalizada %s a l'è de %s", "commands.bossbar.get.visible.hidden": "La barra boss personalizada %s a l'è sconduda", "commands.bossbar.get.visible.visible": "La barra boss personalizada %s a l'è visibel", "commands.bossbar.list.bars.none": "A gh'inn minga di barre boss personalizade pizzade", "commands.bossbar.list.bars.some": "%s barra/e boss attive: %s", "commands.bossbar.remove.success": "La barra boss personalizada %s a l'è stada ranzada via", "commands.bossbar.set.color.success": "A l'è stad cambiad el color de la barra boss personalizada %s", "commands.bossbar.set.color.unchanged": "Nient a l'è cambiad. A l'è sgiamò el color de quella barra boss chì", "commands.bossbar.set.max.success": "A l'è stadcambiad el valor massim de la barra boss personalizada %s a %s", "commands.bossbar.set.max.unchanged": "Nient a l'è cambiad. A l'è sgiamò el valor massim de quella barra bos chì", "commands.bossbar.set.name.success": "A l'è stad cambiad el nom de la barra boss personalizada %s", "commands.bossbar.set.name.unchanged": "Nient a l'è cambiad. A l'è sgiamò el nom de quella barra boss chì", "commands.bossbar.set.players.success.none": "La barra capo %s la gh'ha pu di sgiugador", "commands.bossbar.set.players.success.some": "La barra boss %s è ora associata a %s giocatore/i: %s", "commands.bossbar.set.players.unchanged": "L'è cambiad nagot: 'sti sgiugador inn associad a la barra capo e nissun l'è de sgiontà o ranzà", "commands.bossbar.set.style.success": "A l'è stad cambiad el stil de la barra boss personalizada %s", "commands.bossbar.set.style.unchanged": "Nient a l'è cambiad. A l'è sgiamò el stil de quella barra boss chì", "commands.bossbar.set.value.success": "A l'è stad cambiad el valor de la barra boss personalizada %s a %s", "commands.bossbar.set.value.unchanged": "Nient a l'è cambiad. A l'è sgiamò el valor de quella barra boss chì", "commands.bossbar.set.visibility.unchanged.hidden": "Nient a l'è cambiad. Quella barra boss chì a l'è sgiamò sconduda", "commands.bossbar.set.visibility.unchanged.visible": "Nient a l'è cambiad. Quella barra boss chì a l'è sgiamò visibel", "commands.bossbar.set.visible.success.hidden": "La barra boss personalizada %s a l'è adess sconduda", "commands.bossbar.set.visible.success.visible": "La barra boss personalizada %s a l'è adess visibel", "commands.bossbar.unknown": "L'esist nissuna barra boss con ID \"%s\"", "commands.clear.success.multiple": "Inn stad cavad via %s lavorà de %s sgiugador", "commands.clear.success.single": "Inn stad cavad via %s lavorà de %s", "commands.clear.test.multiple": "Trovata una corrispondenza di %s oggetto/i su %s giocatori", "commands.clear.test.single": "Trovata una corrispondenza di %s oggetto/i su %s", "commands.clone.failed": "Nissun bloch l'è stad fà su de noeuv istess spetasciad", "commands.clone.overlap": "Le aree di origin e di destinazzion i poden minga vesser uguai", "commands.clone.success": "Blocchi clonati: %s", "commands.clone.toobig": "Trop tanti bloch in de l'area (massim: %s, specifegad: %s)", "commands.damage.invulnerable": "Il bersaglio è invulnerabile al tipo di danno selezionato", "commands.damage.success": "Inflitti %s danni a %s", "commands.data.block.get": "%s del bloch in %s, %s, %s, cont un fator de scala de %s, l'è %s", "commands.data.block.invalid": "El bloch de destinazzion l'è minga un'entità-bloch", "commands.data.block.modified": "I dat bloch in %s, %s, %s a inn stad modifegad", "commands.data.block.query": "El bloch in %s, %s, %s el gh'ha quei dat bloch chì: %s", "commands.data.entity.get": "%s de %s, cont un fator de scala de %s, l'è %s", "commands.data.entity.invalid": "Impossibel modifegà i dat del sgiugador", "commands.data.entity.modified": "I dat entità de %s a inn stad modifegad", "commands.data.entity.query": "%s el gh'ha quei dat entità chì: %s", "commands.data.get.invalid": "Impossibil otegnì %s: domà i etichete numeriche inn permetude", "commands.data.get.multiple": "Quell'argoment chì l'aceta un'unegh valor NBT", "commands.data.get.unknown": "Impossibil otegnì %s: el tag l'esista minga", "commands.data.merge.failed": "A l'è cambiad nagot. I proprietà specifegade i gh'hann sgiamò 'sti valor chì", "commands.data.modify.expected_list": "Prevista una lista (otegnud %s)", "commands.data.modify.expected_object": "Previst un oget (otegnud %s)", "commands.data.modify.expected_value": "Previsto un valore (ricevuto %s)", "commands.data.modify.invalid_index": "Indes de la lista minga bon: %s", "commands.data.modify.invalid_substring": "Indici di sottostringa non validi: da %s a %s", "commands.data.storage.get": "%s in del contenidor %s, cont on fator de scala de %s, l'è %s", "commands.data.storage.modified": "Contenidor %s modifegad", "commands.data.storage.query": "El contenidor %s el gh'ha quei contegnud chì: %s", "commands.datapack.create.already_exists": "Esiste già un pacchetto con il nome «%s»", "commands.datapack.create.invalid_full_name": "Nome del nuovo pacchetto non valido: «%s»", "commands.datapack.create.invalid_name": "Caratteri non validi nel nome del nuovo pacchetto: «%s»", "commands.datapack.create.io_failure": "Impossibile creare un pacchetto con il nome «%s». Controlla nei log", "commands.datapack.create.metadata_encode_failure": "Impossibile codificare i metadati per il pacchetto «%s»: %s", "commands.datapack.create.success": "Creato un nuovo pacchetto vuoto con il nome «%s»", "commands.datapack.disable.failed": "El pachet de dat %s a l'è minga pizzad!", "commands.datapack.disable.failed.feature": "Il pacchetto «%s» non può essere disattivato perché fa parte di una variabile attiva!", "commands.datapack.enable.failed": "El pachet de dat %s a l'è sgiamò pizzad!", "commands.datapack.enable.failed.no_flags": "Impossibile attivare il pacchetto «%s», poiché le variabili richieste non sono attive in questo mondo: %s!", "commands.datapack.list.available.none": "Nissun pachet de dat l'è disponibel", "commands.datapack.list.available.success": "%s pacchetto/i di dati disponibili: %s", "commands.datapack.list.enabled.none": "Nissun pachet de dat l'è pizzad", "commands.datapack.list.enabled.success": "%s pacchetto/i di dati attivi: %s", "commands.datapack.modify.disable": "'Dree a smorzà el pachet de dat %s", "commands.datapack.modify.enable": "'Dree a pizzà el pachet de dat %s", "commands.datapack.unknown": "Pachet de dat descognossud: %s", "commands.debug.alreadyRunning": "La profiladura di tick l'è sgiamò partida", "commands.debug.function.noRecursion": "Se poeu mìa rilevà del dedenter d'una fonzion", "commands.debug.function.noReturnRun": "Impossibile usare la tracciatura con «return run»", "commands.debug.function.success.multiple": "Tracciato %s comando/i di %s funzioni nel documento d'uscita %s", "commands.debug.function.success.single": "Tracciato %s comando/i della funzione «%s» nel documento d'uscita %s", "commands.debug.function.traceFailed": "Se poeu mìa rilevà la fonzion", "commands.debug.notRunning": "La profiladura di tick l'è minga partida", "commands.debug.started": "La profiladura di tick l'è partida", "commands.debug.stopped": "La profiladura di tick l'è fermada de poeu de %s segondi e %s tick (%s tick al segondo)", "commands.defaultgamemode.success": "De chì inanz la modalità de sgioeugh predefinida la sarà %s", "commands.deop.failed": "Nient a l'è cambiad. El sgiugador a l'è minga un operador", "commands.deop.success": "%s a l'è pu un operador del server", "commands.dialog.clear.multiple": "I dialoghi per %s giocatori sono stati cancellati", "commands.dialog.clear.single": "I dialoghi per %s sono stati cancellati", "commands.dialog.show.multiple": "Dialogo mostrato a %s giocatori", "commands.dialog.show.single": "Dialogo mostrato a %s", "commands.difficulty.failure": "La dificoltà l'è minga cambiada: l'è sgiamò impostada come %s", "commands.difficulty.query": "La dificoltà a l'è %s", "commands.difficulty.success": "La dificoltà l'è stada impostada a %s", "commands.drop.no_held_items": "I entità poeuden mìa tegnì in man di lavorà", "commands.drop.no_loot_table": "L'entità %s la gh'ha mìa la tabella di lavorà", "commands.drop.no_loot_table.block": "Il blocco %s non ha una tabella del bottino", "commands.drop.success.multiple": "Lavorà lassad: %s", "commands.drop.success.multiple_with_table": "S'inn liberad %s lavorà de la tabella %s", "commands.drop.success.single": "Trad foeura %s %s", "commands.drop.success.single_with_table": "S'inn liberad %s de %s de la tabella %s", "commands.effect.clear.everything.failed": "L'obietiv el gh'ha nissun efet de cavà", "commands.effect.clear.everything.success.multiple": "Ranzad tucc i efet de %s entità", "commands.effect.clear.everything.success.single": "Ranzad tucc i efet de %s", "commands.effect.clear.specific.failed": "L'obietiv el gh'ha mìa l'efet ciamad", "commands.effect.clear.specific.success.multiple": "Ranzad i efet %s de %s entità", "commands.effect.clear.specific.success.single": "S'è cavad l'efet %s de %s", "commands.effect.give.failed": "Se poeu mìa meter l'efet (l'obietiv l'è imun ai efet o el gh'ha un vergot pussee fort)", "commands.effect.give.success.multiple": "L'efet %s è stad aplicad a %s entità", "commands.effect.give.success.single": "L'efet %s l'è stad aplicad a %s", "commands.enchant.failed": "A l'è cambiad nagot: i obietiv gh'hann mìa un lavorà in di man o se poeu mìa meter l'instriament", "commands.enchant.failed.entity": "%s non è un'entità valida per questo comando", "commands.enchant.failed.incompatible": "El lavorà %s el suporta minga quell instriament chì", "commands.enchant.failed.itemless": "%s el ten nanca un oget", "commands.enchant.failed.level": "%s l'è magior del nivell massim (%s) suportad de 'sto instriament chì", "commands.enchant.success.multiple": "L'instriament %s l'è stad aplicad a %s entità", "commands.enchant.success.single": "L'instriament %s l'è stad aplicad a l'oget de %s", "commands.execute.blocks.toobig": "Troppi blocchi nell'area (massimo: %s, specificati: %s)", "commands.execute.conditional.fail": "La proeuva l'è fallada", "commands.execute.conditional.fail_count": "La proeuva l'è fallada; cunt: %s", "commands.execute.conditional.pass": "La proeuva l'è stada superada", "commands.execute.conditional.pass_count": "La proeuva l'è stada superada; cunt: %s", "commands.execute.function.instantiationFailure": "Impossibile creare un'istanza della funzione %s: %s", "commands.experience.add.levels.success.multiple": "A gh'inn stad assegnad %s nivei de esperienza a %s giocatori", "commands.experience.add.levels.success.single": "%s nivei de esperienza a gh'inn stad assegnad al/a la %s", "commands.experience.add.points.success.multiple": "A gh'inn stad assegnad %s ponti esperienza a %s sgiugador", "commands.experience.add.points.success.single": "A gh'inn stad assegnad %s ponti esperienza al/a la %s", "commands.experience.query.levels": "El/La %s el/la gh'ha %s nivei de esperienza", "commands.experience.query.points": "El/la %s el/la gh'ha %s ponti esperienza", "commands.experience.set.levels.success.multiple": "A l'è stad impostad el nivell de esperienza %2$s per %1$s sgiugador", "commands.experience.set.levels.success.single": "A l'è stad impostad el nivell de sperienza %2$s per el/la %1$s", "commands.experience.set.points.invalid": "Se poeu mìa meter i pont esperienza sora i pont massim del livell de adess del sgiugador", "commands.experience.set.points.success.multiple": "I ponti esperienza de %2$s sgiugador a inn stad impostad per el/la %1$s", "commands.experience.set.points.success.single": "I ponti esperienza de %2$s a inn stad inpostad per %1$s", "commands.fill.failed": "Nissun bloch a l'è stad piazzad", "commands.fill.success": "<PERSON><PERSON> p<PERSON>: %s", "commands.fill.toobig": "Troppi blocchi nell'area (massimo: %s, specificati: %s)", "commands.fillbiome.success": "Biomi impostati tra %s, %s, %s e %s, %s, %s", "commands.fillbiome.success.count": "Impostato %s bioma/i tra %s, %s, %s e %s, %s, %s", "commands.fillbiome.toobig": "Troppi blocchi nell'area (massimo: %s, specificati: %s)", "commands.forceload.added.failure": "Nissun toch a l'è stad segnalad per el caregament forzad", "commands.forceload.added.multiple": "%s toch a inn stad marcad in %s de %s a %s per el caregament forzad", "commands.forceload.added.none": "In %s a l'è stad trovad nanca vun toch sotopost al caregament forzad", "commands.forceload.added.single": "El toch %s in %s l'è stad marcad per el caregament forzad", "commands.forceload.list.multiple": "%s sotopost al caregament forzad a inn stad trovad in %s a: %s", "commands.forceload.list.single": "A l'è stad trovad on toch per el caregament forzad in %s a: %s", "commands.forceload.query.failure": "El toch %s in %s a l'è stad no marcad per el caregament forzad", "commands.forceload.query.success": "El toch %s in %s a l'è stad marcad per el caregament forzad", "commands.forceload.removed.all": "In %s adesedess nissun toch l'è pù marcad per el caregament forzad", "commands.forceload.removed.failure": "S'è mìa cavad el caregament forzad de nissun chunk", "commands.forceload.removed.multiple": "%s toch in %s de %s a %s no a inn pù marcad per el caregament forzad", "commands.forceload.removed.single": "El toch %s in %s no l'è pù marcad per el caregament forzad", "commands.forceload.toobig": "Trop tanti toch in de l'area specifegada (massim: %s, specifegad: %s)", "commands.function.error.argument_not_compound": "Tipo di argomento non valido: %s (previsto «compound»)", "commands.function.error.missing_argument": "Argomento %2$s mancante per la funzione %1$s", "commands.function.error.missing_arguments": "Argomenti mancanti per la funzione %s", "commands.function.error.parse": "Durante l'esecuzione della macro %s, il comando «%s» ha causato un errore: %s", "commands.function.instantiationFailure": "Impossibile creare un'istanza della funzione %s: %s", "commands.function.result": "La funzione «%s» ha restituito %s", "commands.function.scheduled.multiple": "Esecuzione delle funzioni: %s", "commands.function.scheduled.no_functions": "Non è stata trovata alcuna funzione denominata «%s»", "commands.function.scheduled.single": "Esecuzione della funzione: %s", "commands.function.success.multiple": "Eseguito %s comando/i da %s funzioni", "commands.function.success.multiple.result": "Eseguite %s funzioni", "commands.function.success.single": "Eseguito %s comando/i dalla funzione «%s»", "commands.function.success.single.result": "La funzione «%2$s» ha restituito %1$s", "commands.gamemode.success.other": "La modalità de sgioeugh de %s a l'è stada cambiada in %s", "commands.gamemode.success.self": "De chì inanz la modalità de sgioeugh l'è impostada come %s", "commands.gamerule.query": "La regola de gioco %s adess l'è impostada 'me: %s", "commands.gamerule.set": "La regola de sgioeugh %s l'è stada impostada come: %s", "commands.give.failed.toomanyitems": "Minga pussee de %s %s", "commands.give.success.multiple": "%3$s sgiugador i hann ciapad %2$s × %1$s", "commands.give.success.single": "%3$s l'ha ciapad %2$s × %1$s", "commands.help.failed": "Comand descognossud o mìa assee permess", "commands.item.block.set.success": "Scambiad on ispazzi in %s, %s, %s con %s", "commands.item.entity.set.success.multiple": "Scambiad on ispazzi in %s entità con %s", "commands.item.entity.set.success.single": "Scambiad on ispazzi in %s con %s", "commands.item.source.no_such_slot": "La font la gh'ha mìa el slot %s", "commands.item.source.not_a_container": "La posizzion inizzial %s, %s, %s l'è minga on recipient", "commands.item.target.no_changed.known_item": "Nissun obietiv l'ha acetad el lavor %s in del slot %s", "commands.item.target.no_changes": "Nissun obietiv l'ha acetad el lavor in del slot %s", "commands.item.target.no_such_slot": "L'obietiv el gh'ha mìa un slot %s", "commands.item.target.not_a_container": "La posizzion de l'oget %s, %s, %s l'è minga on recipient", "commands.jfr.dump.failed": "Impossibel descaregà la registrazzion JFR: %s", "commands.jfr.start.failed": "L'inviada de la profiladura JFR l'è fallada", "commands.jfr.started": "La profiladura JFR l'è tacada", "commands.jfr.stopped": "La profiladura JFR l'è stada fermada, e i risultad a inn stad descaregad in %s", "commands.kick.owner.failed": "Impossibile espellere il proprietario del server in una sessione LAN", "commands.kick.singleplayer.failed": "Impossibile espellere in un mondo offline in giocatore singolo", "commands.kick.success": "%s a l'è stad/a casciad/a: %s", "commands.kill.success.multiple": "%s entità a inn stade copade", "commands.kill.success.single": "%s a l'è stad/a copad/a", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "%s sgiugador (de %s al massim) a inn coness adess: %s", "commands.locate.biome.not_found": "Se poeu mìa trovà un bioma \"%s\" dent de una distanza resonevol", "commands.locate.biome.success": "Il bioma %s più vicino è in %s (%s blocchi di distanza)", "commands.locate.poi.not_found": "Impossibile trovare un punto d'interesse di tipo «%s» entro una distanza ragionevole", "commands.locate.poi.success": "Il punto «%s» più vicino è in %s (%s blocchi di distanza)", "commands.locate.structure.invalid": "Gh'è nissuna strutura del tipo \"%s\"", "commands.locate.structure.not_found": "S'è trovada mìa la strutura del tipo %s chì arent", "commands.locate.structure.success": "La strutura de tipo %s pussee arenta l'è in %s (%s blocch de distanza)", "commands.message.display.incoming": "%s el/la t'ha did sot-vos: %s", "commands.message.display.outgoing": "T'hee did sot-vos al/a la %s: %s", "commands.op.failed": "Nient a l'è cambiad. El sgiugador a l'è sgiamò un operador", "commands.op.success": "%s a l'è adess un operador del server", "commands.pardon.failed": "Nient a l'è cambiad. El sgiugador a l'è minga bandid", "commands.pardon.success": "%s a l'è pu bandid/a", "commands.pardonip.failed": "Nient a l'è cambiad. L'indirizz IP a l'è minga bandid", "commands.pardonip.invalid": "Indirizz IP invalid", "commands.pardonip.success": "L'indirizz IP %s a l'è pu bandid", "commands.particle.failed": "La partizella l'era visibil per nissun", "commands.particle.success": "Visualizzazion de la particola %s", "commands.perf.alreadyRunning": "La profiladura de la resa l'è sgiamò partida", "commands.perf.notRunning": "A l'è stada minga dada l'inviada la profiladura di prestazzion", "commands.perf.reportFailed": "Se poeu mìa fà su el raport de debug", "commands.perf.reportSaved": "El raport de debug l'è stad metud in pè in %s", "commands.perf.started": "Partida una profiladura de la resa de 10 segondi (drova \"/perf stop\" per fermala prima)", "commands.perf.stopped": "La profilatura delle prestazioni è stata interrotta dopo %s secondo/i e %s tick (%s tick al secondo)", "commands.place.feature.failed": "Se poeu mìa meter la decorazzion", "commands.place.feature.invalid": "Gh'è nissuna decorazzion del tipo \"%s\"", "commands.place.feature.success": "\"%s\" l'è stad metud in %s, %s, %s", "commands.place.jigsaw.failed": "Eror al fà su el puzzle", "commands.place.jigsaw.invalid": "Gh'è nissun grup de meder de tipo \"%s\"", "commands.place.jigsaw.success": "Puzzle fad su in %s, %s, %s", "commands.place.structure.failed": "Se poeu mìa piazzà la strutura", "commands.place.structure.invalid": "Non esiste una struttura di tipo «%s»", "commands.place.structure.success": "Strutura \"%s\" fada su in %s, %s, %s", "commands.place.template.failed": "Se poeu mìa piazzà el modell", "commands.place.template.invalid": "Gh'è nissun meder cont l'Id \"%s\"", "commands.place.template.success": "Cargad el meder \"%s\" in %s, %s, %s", "commands.playsound.failed": "El son a l'è trop de lonsg per sentìss", "commands.playsound.success.multiple": "El son %s l'è stad sonad per %s sgiugador", "commands.playsound.success.single": "El son %s l'è stad sonad %s", "commands.publish.alreadyPublished": "La partida multi-sgiugador l'è sgiamò ospitada in su la porta %s", "commands.publish.failed": "Impossibel ospità la partida locala", "commands.publish.started": "La partida locala a l'è ospitada travers de la porta %s", "commands.publish.success": "La partida multisgiugador adsedess l'è ospatada travers de la porta %s", "commands.random.error.range_too_large": "L'intervallo del valore casuale dev'essere al massimo 2147483646", "commands.random.error.range_too_small": "L'intervallo del valore casuale dev'essere almeno 2", "commands.random.reset.all.success": "%s sequenze casuali sono state reimpostate", "commands.random.reset.success": "La sequenza casuale %s è stata reimpostata", "commands.random.roll": "%s ha estratto %s (tra %s e %s)", "commands.random.sample.success": "Valore casuale: %s", "commands.recipe.give.failed": "Nissuna noeuva riceta a l'è stada desblocada", "commands.recipe.give.success.multiple": "%s ricete a inn stade desblocade per %s sgiugador", "commands.recipe.give.success.single": "%s ricete a inn stade desblocade per %s", "commands.recipe.take.failed": "Nissusa riceta la poeul vesser ranzada via", "commands.recipe.take.success.multiple": "%s ricete a inn stade ranzade via de %s sgiugador", "commands.recipe.take.success.single": "%s ricete a inn stade ranzade via de %s", "commands.reload.failure": "Impossibel caregà an'mò, vegnarann mentugnud i vegg dat", "commands.reload.success": "'Dree a caregà su de noeuv!", "commands.ride.already_riding": "%s è già su %s", "commands.ride.dismount.success": "%s è sceso/a da %s", "commands.ride.mount.failure.cant_ride_players": "I giocatori non possono essere cavalcati", "commands.ride.mount.failure.generic": "%s non può salire su %s", "commands.ride.mount.failure.loop": "Un'entità non può salire su se stessa o sui propri passeggeri", "commands.ride.mount.failure.wrong_dimension": "Impossibile salire su un'entità in una dimensione diversa", "commands.ride.mount.success": "%s è salito/a su %s", "commands.ride.not_riding": "%s non è su nessun veicolo", "commands.rotate.success": "Ruotato %s", "commands.save.alreadyOff": "El salvament l'è sgiamò smorzad", "commands.save.alreadyOn": "El salvament l'è sgiamò pizzad", "commands.save.disabled": "El salvament automategh a l'è stad smorzad", "commands.save.enabled": "El salvament automategh a l'è stad pizzad", "commands.save.failed": "Impossibil salvà el sgioeugh (a ghe n'è assee de spazzi in del disco?)", "commands.save.saving": "'Dree a salvà la partida... (ghe podaria volé un poo!)", "commands.save.success": "Partida salvada", "commands.schedule.cleared.failure": "Nanca un programa con ID %s", "commands.schedule.cleared.success": "Rimosso %s programma/i con ID %s", "commands.schedule.created.function": "La funzione «%s» verrà eseguita tra %s tick (tempo di gioco: %s)", "commands.schedule.created.tag": "L'eticheta \"%s\" la sarà eseguida tra %s tick (temp de sgieuogh: %s)", "commands.schedule.macro": "Impossibile programmare una macro", "commands.schedule.same_tick": "Impossibil programà la corsa in del tick de adess", "commands.scoreboard.objectives.add.duplicate": "L'esist sgiamò un obietiv con quell nom chì", "commands.scoreboard.objectives.add.success": "L'obietiv %s a l'è stad fad su", "commands.scoreboard.objectives.display.alreadyEmpty": "Nient a l'è cambiad. La posizzion a l'è sgiamò voeuda", "commands.scoreboard.objectives.display.alreadySet": "Nient a l'è cambiad. La posizzion a l'è sgiamò 'dree a mostrà l'obietiv", "commands.scoreboard.objectives.display.cleared": "Tucc i obietiv a inn stad ranzad via de la posizzion %s", "commands.scoreboard.objectives.display.set": "Impostada in la posizzion %s per mostrà l'obietiv %s", "commands.scoreboard.objectives.list.empty": "A gh'inn minga obietiv", "commands.scoreboard.objectives.list.success": "%s obiettivo/i: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disattivato l'aggiornamento visuale automatico per l'obiettivo: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Attivato l'aggiornamento visuale automatico per l'obiettivo: %s", "commands.scoreboard.objectives.modify.displayname": "Cambiad el nom de la schermade del %s in %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cancellato il formato numerico predefinito dell'obiettivo: %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Cambiato il formato numerico predefinito dell'obiettivo: %s", "commands.scoreboard.objectives.modify.rendertype": "Tipo de restituzzion de l'obietiv %s l'è stad cambiad", "commands.scoreboard.objectives.remove.success": "L'obietiv %s a l'è stad ranzad via", "commands.scoreboard.players.add.success.multiple": "%s pont inn stad sgiontad a l'obietiv %s per %s entità", "commands.scoreboard.players.add.success.single": "%s pont inn stad sgiontad a l'obietiv %s per %s (adess %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cancellato il nome visualizzato in %2$s per %1$s entità", "commands.scoreboard.players.display.name.clear.success.single": "Cancellato il nome visualizzato in %2$s per %1$s", "commands.scoreboard.players.display.name.set.success.multiple": "Cambiato il nome visualizzato in %3$s per %2$s entità in «%1$s»", "commands.scoreboard.players.display.name.set.success.single": "Cambiato il nome visualizzato in %3$s per %2$s in «%1$s»", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cancellato il formato numerico per %s entità in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cancellato il formato numerico per %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Cambiato il formato numerico per %s entità in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Cambiato il formato numerico per %s in %s", "commands.scoreboard.players.enable.failed": "A l'è cambiad nagot: el pizzador l'è sgiamò pizzad", "commands.scoreboard.players.enable.invalid": "\"enable\" la va domà con obietiv pizzador", "commands.scoreboard.players.enable.success.multiple": "El trigger %s l'è stad pizzad per %s entità", "commands.scoreboard.players.enable.success.single": "El pizzador %s l'è stad pizzad per %s", "commands.scoreboard.players.get.null": "Se poeu mìa havégh i pont de l'obietiv %s per %s: l'è mìa configurad", "commands.scoreboard.players.get.success": "%s el gh'ha %s pont in %s", "commands.scoreboard.players.list.empty": "Gh'è minga entità registrade", "commands.scoreboard.players.list.entity.empty": "%s el/la gh'ha minga de pontegg de fà vedè", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s ha %s punteggio/i:", "commands.scoreboard.players.list.success": "%s entità registrata/e: %s", "commands.scoreboard.players.operation.success.multiple": "Atualizad i pont de %s per %s entità", "commands.scoreboard.players.operation.success.single": "Cambiad pont obietiv %s de %s a %s", "commands.scoreboard.players.remove.success.multiple": "S'inn ranzad %s pont de l'obietiv %s per %s entità", "commands.scoreboard.players.remove.success.single": "S'inn ranzad %s pont de l'obietiv %s per %s (adess %s)", "commands.scoreboard.players.reset.all.multiple": "Tucc i pontegg per %s entità a inn stad metud a zero", "commands.scoreboard.players.reset.all.single": "Tucc i pontegg per %s a inn stad metud de noeuv a zero", "commands.scoreboard.players.reset.specific.multiple": "Retacà i pont de %s per %s entità", "commands.scoreboard.players.reset.specific.single": "Retacà i pont de %s per %s", "commands.scoreboard.players.set.success.multiple": "Imposta i pont obietiv %s per %s entità a %s", "commands.scoreboard.players.set.success.single": "Cambiad pont obietiv %s de %s a %s", "commands.seed.success": "Somenza: %s", "commands.setblock.failed": "Se poeul no piazzà el bloch", "commands.setblock.success": "El bloch in %s, %s, %s a l'è stad cambiad", "commands.setidletimeout.success": "Il tempo d'inattività massimo dei giocatori è stato impostato a %s minuto/i", "commands.setidletimeout.success.disabled": "Il tempo d'inattività massimo dei giocatori è stato disattivato", "commands.setworldspawn.failure.not_overworld": "Impossibile impostare il punto di generazione globale fuori dall'Overworld", "commands.setworldspawn.success": "El pont de sgenerazzion global l'è stad impostad come %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "El pont de sgenerazzion de %6$s sgiugador l'è sta impostad %1$s, %2$s, %3$s [%4$s] in %5$s", "commands.spawnpoint.success.single": "Impostad el pont de generazzion in %s, %s, %s [%s] in %s per %s", "commands.spectate.not_spectator": "%s a l'è minga in modalità spetador", "commands.spectate.self": "Te poeudet vardàss indepertì", "commands.spectate.success.started": "Sgiugador el varda de %s", "commands.spectate.success.stopped": "Te vardet pu un'entità", "commands.spreadplayers.failed.entities": "Impossibile distribuire %s entità attorno a %s, %s (troppe entità per l'area: prova a distribuire al massimo %s entità)", "commands.spreadplayers.failed.invalid.height": "maxHeight %s mìa valid; previst un valor pussee volt de altezza minima del mond %s", "commands.spreadplayers.failed.teams": "Impossibile distribuire %s squadra/e attorno a %s, %s (troppe entità per l'area: prova a distribuire al massimo %s squadra/e)", "commands.spreadplayers.success.entities": "Distribuito %s entità attorno a %s, %s con una separazione media in blocchi pari a %s", "commands.spreadplayers.success.teams": "Distribuito %s squadra/e attorno a %s, %s con una separazione media in blocchi pari a %s", "commands.stop.stopping": "'Dree a fermà el server", "commands.stopsound.success.source.any": "Tucc i son de la sorgent %s a inn stad smorzad", "commands.stopsound.success.source.sound": "El son %s de la sorgent %s è stad smorzad", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON> tucc i son", "commands.stopsound.success.sourceless.sound": "El son %s l'è stad smorzad", "commands.summon.failed": "Se poeu mìa ciamà l'entità", "commands.summon.failed.uuid": "Impossibil ciamà l'entità per via d'ona duplicazzion di UUID", "commands.summon.invalidPosition": "Posizzion mìa bona per la ciamada", "commands.summon.success": "L'entità %s l'è stada tirada a man", "commands.tag.add.failed": "L'obietiv el gh'ha sgiamò l'eticheta o el ne gh'ha trope", "commands.tag.add.success.multiple": "L'eticheta \"%s\" l'è stada sgiontada a %s entità", "commands.tag.add.success.single": "L'eticheta \"%s\" l'è stada sgiontada a %s", "commands.tag.list.multiple.empty": "I %s entità a gh'hann minga tag", "commands.tag.list.multiple.success": "I %s entità i gh'hann %s tag complessiv: %s", "commands.tag.list.single.empty": "%s el gh'ha minga etichete", "commands.tag.list.single.success": "%s el gh'ha %s etichete: %s", "commands.tag.remove.failed": "L'obietiv el gh'ha mìa quest'eticheta chì", "commands.tag.remove.success.multiple": "L'eticheta \"%s\" l'è stada tolta via de %s entità", "commands.tag.remove.success.single": "L'eticheta \"%s\" l'è stada tolta via de %s", "commands.team.add.duplicate": "L'esist sgiamò una squadra con quell nom chì", "commands.team.add.success": "La squadra %s a l'è stada fada su", "commands.team.empty.success": "Rimosso %s membro/i dalla squadra %s", "commands.team.empty.unchanged": "Nient a l'è cambiad. La squadra a l'è sgiamò voeuda", "commands.team.join.success.multiple": "%s member a inn stad sgiontad a la squadra %s", "commands.team.join.success.single": "%s a l'è stad/a sgiontad/a a la squadra %s", "commands.team.leave.success.multiple": "%s member a inn stad ranzad via di so squader", "commands.team.leave.success.single": "%s a l'è stad/a ranzad/a via de la so squadra", "commands.team.list.members.empty": "La squadra %s la gh'ha minga member", "commands.team.list.members.success": "La squadra %s ha %s membro/i: %s", "commands.team.list.teams.empty": "A gh'inn minga squader", "commands.team.list.teams.success": "%s squadra/e: %s", "commands.team.option.collisionRule.success": "La regola de s'centrada per la squadra %s adess l'è \"%s\"", "commands.team.option.collisionRule.unchanged": "A l'è cambiad nagot. La regola de s'centrada la gh'ha sgiamò quell valor chì", "commands.team.option.color.success": "El color de la squadra %s a l'è stad cambiad in %s", "commands.team.option.color.unchanged": "Nient a l'è cambiad. A l'è sgiamò el color de la squadra", "commands.team.option.deathMessageVisibility.success": "La visibilità di messagg de mort per la squadra %s adess l'è \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "A l'è cambiad nagot. La visibilità di messagg de mort la gh'ha sgiamò quell valor chì", "commands.team.option.friendlyfire.alreadyDisabled": "Nient a l'è cambiad. El foeugh amis a l'è sgiamò smorzad per la squadra", "commands.team.option.friendlyfire.alreadyEnabled": "Nient a l'è cambiad. El foeugh amis a l'è sgiamò pizzad per la squadra", "commands.team.option.friendlyfire.disabled": "El foeugh amis per la squadra %s a l'è stad smorzad", "commands.team.option.friendlyfire.enabled": "El foeugh amis per la squadra %s a l'è stad pizzad", "commands.team.option.name.success": "Atualiza el nom de la squadra %s", "commands.team.option.name.unchanged": "Nient a l'è cambiad. A l'è sgiamò el nom de la squadra", "commands.team.option.nametagVisibility.success": "La visibilità di nom di sgiugador per la squadra %s adess l'è \"%s\"", "commands.team.option.nametagVisibility.unchanged": "A l'è cambiad nagot. La visibilità di nom di sgiucador l'ha sgiamò quell valor chì", "commands.team.option.prefix.success": "El prefiss de la squadra a l'è stad cambiad in %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nient a l'è cambiad. I member de la squadra a poeuden minga vedé i compagn invisibel", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nient a l'è cambiad. I member de la squadra a poeuden sgiamò vedé i compagn invisibel", "commands.team.option.seeFriendlyInvisibles.disabled": "I member de la squadra %s a poeuden pu vedé i compagn invisibel", "commands.team.option.seeFriendlyInvisibles.enabled": "I member de la squadra %s a poeuden vedé i compagn invisibel", "commands.team.option.suffix.success": "El sufiss de la squadra a l'è stad cambiad in %s", "commands.team.remove.success": "La squadra %s a l'è stada ranzada via", "commands.teammsg.failed.noteam": "Te gh'heet de vesser in d'una squadra per mandà un messagg de squadra", "commands.teleport.invalidPosition": "Posizzion minga valida per teleportà", "commands.teleport.success.entity.multiple": "%s entità a inn stade teleportade a %s", "commands.teleport.success.entity.single": "%s a l'è stad teleportad a %s", "commands.teleport.success.location.multiple": "%s entità a inn stade teleportade in %s, %s, %s", "commands.teleport.success.location.single": "%s a l'è stad teleportad in %s, %s, %s", "commands.test.batch.starting": "Avvio dell'ambiente %s del lotto %s", "commands.test.clear.error.no_tests": "Impossibile trovare un test da cancellare", "commands.test.clear.success": "Sono state eliminate %s strutture", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Fai clic per copiare negli appunti", "commands.test.create.success": "L'impostazione è stata creata per il test %s", "commands.test.error.no_test_containing_pos": "Impossibile trovare un'istanza di test che contenga %s, %s, %s", "commands.test.error.no_test_instances": "Nessuna istanza di test trovata", "commands.test.error.non_existant_test": "Il test %s non è stato trovato", "commands.test.error.structure_not_found": "La struttura di test %s non è stata trovata", "commands.test.error.test_instance_not_found": "Impossibile trovare l'entità del blocco di collaudo", "commands.test.error.test_instance_not_found.position": "Impossibile trovare l'entità del blocco di collaudo presso %s, %s, %s", "commands.test.error.too_large": "Le dimensioni della struttura devono essere inferiori a %s blocchi su ogni asse", "commands.test.locate.done": "Localizzazione terminata, sono state trovate %s strutture", "commands.test.locate.found": "Struttura trovata a: %s (distanza: %s)", "commands.test.locate.started": "Avviata la localizzazione delle strutture di test. Potrebbe volerci un po' di tempo...", "commands.test.no_tests": "N<PERSON>un test da eseguire", "commands.test.relative_position": "Posizione relativa a %s: %s", "commands.test.reset.error.no_tests": "Impossibile trovare un test da ripristinare", "commands.test.reset.success": "Strutture ripristinate: %s", "commands.test.run.no_tests": "Nessun test trovato", "commands.test.run.running": "Esecuzione di %s test...", "commands.test.summary": "Test del gioco completato! %s test eseguiti", "commands.test.summary.all_required_passed": "Tutti i test necessari sono stati superati :)", "commands.test.summary.failed": "Test necessari non riusciti: %s :(", "commands.test.summary.optional_failed": "Test facoltativi non riusciti: %s", "commands.tick.query.percentiles": "Percentili: P50: %s ms, P95: %s ms, P99: %s ms; campione: %s", "commands.tick.query.rate.running": "Frequenza di tick indicata: %s al secondo.\nTempo medio per tick: %s ms (indicato: %s ms)", "commands.tick.query.rate.sprinting": "Frequenza di tick indicata: %s al secondo (ignorata, solo per riferimento).\nTempo medio per tick: %s ms", "commands.tick.rate.success": "Frequenza indicata impostata su %s tick al secondo", "commands.tick.sprint.report": "Accelerazione completata con %s tick al secondo, o %s ms per tick", "commands.tick.sprint.stop.fail": "Nessuna accelerazione di tick in corso", "commands.tick.sprint.stop.success": "Accelerazione di tick interrotta", "commands.tick.status.frozen": "Il gioco è stato fermato", "commands.tick.status.lagging": "Il gioco è in esecuzione, ma non riesce a tenere il passo con la frequenza di tick indicata", "commands.tick.status.running": "Il gioco funziona normalmente", "commands.tick.status.sprinting": "Il gioco è accelerato", "commands.tick.step.fail": "Impossibile effettuare l'avanzamento - il gioco deve prima essere fermato", "commands.tick.step.stop.fail": "<PERSON><PERSON><PERSON> a<PERSON> di tick in corso", "commands.tick.step.stop.success": "Avanzamento di tick interrotto", "commands.tick.step.success": "Avanzamento di %s tick", "commands.time.query": "Temp: %s", "commands.time.set": "El temp l'è stad impostad a %s", "commands.title.cleared.multiple": "I titol per %s sgiugador a inn stad scancellad", "commands.title.cleared.single": "I titol %s a inn stad scancellad", "commands.title.reset.multiple": "I opzion di titoi per %s sgiugador a inn stade stabilide an'mò", "commands.title.reset.single": "I opzion di titoi per %s a inn stade stabilide an'mò", "commands.title.show.actionbar.multiple": "Visualizazzion titol noeuv in la barra per %s sgiugador", "commands.title.show.actionbar.single": "Visualizazzion titol noeuv in de la barra di lavorà per %s", "commands.title.show.subtitle.multiple": "Visualizazzion d'un sottitol noeuv per %s sgiugador", "commands.title.show.subtitle.single": "Visualizazzion d'un sottitol noeuv per %s", "commands.title.show.title.multiple": "Visualizazzion d'un titol noeuv per %s sgiugador", "commands.title.show.title.single": "Visualizazzion d'un titol noeuv per %s", "commands.title.times.multiple": "I temp de visualizazzion di titol a inn stad cambiad per %s sgiudador", "commands.title.times.single": "I temp de visualizazzion di titol a inn stad cambiad per %s", "commands.transfer.error.no_players": "Va specificato almeno un giocatore da trasferire", "commands.transfer.success.multiple": "Trasferimento di %s giocatori a %s:%s", "commands.transfer.success.single": "Trasferimento di %s a %s:%s", "commands.trigger.add.success": "%s l'è stad pizzad (sgiontad %s al valor)", "commands.trigger.failed.invalid": "Te poeudet pizzà domà obietiv de tipo \"pizzador\"", "commands.trigger.failed.unprimed": "Te poeudet gna'mò pizzà quest'obietiv pizzador chì", "commands.trigger.set.success": "%s l'è stad pizzad (valor impostad %s)", "commands.trigger.simple.success": "%s l'è stad pizzad", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Nessun punto di riferimento in %s", "commands.waypoint.list.success": "%s punto/i di riferimento in %s: %s", "commands.waypoint.modify.color": "Il colore del punto di riferimento è stato impostato su %s", "commands.waypoint.modify.color.reset": "Ripristinato il colore del punto di riferimento", "commands.waypoint.modify.style": "Lo stile del punto di riferimento è stato cambiato", "commands.weather.set.clear": "El temp meteorologich l'è stad impostad come \"bell temp\"", "commands.weather.set.rain": "El temp meteorologich l'è stad impostad come \"pioeuva\"", "commands.weather.set.thunder": "El temp meteorologich l'è stad impostad come \"temporal\"", "commands.whitelist.add.failed": "El sgiugador a l'è sgiamò in de la lista bianca", "commands.whitelist.add.success": "%s l'è stad/stada sgiontad/sgiontada a la lista bianca", "commands.whitelist.alreadyOff": "La lista bianca l'è sgiamò smorzada", "commands.whitelist.alreadyOn": "La lista bianca l'è sgiamò pizzada", "commands.whitelist.disabled": "La lista bianca l'è stada smorzada dessadess", "commands.whitelist.enabled": "La lista bianca l'è stada pizzada dessadess", "commands.whitelist.list": "%s giocatore/i nella lista whitelist: %s", "commands.whitelist.none": "A gh'inn no sgiugador in de la lista bianca", "commands.whitelist.reloaded": "La lista bianca l'è stada caregada de noeuv", "commands.whitelist.remove.failed": "El sgiugador a l'è minga in de la lista bianca", "commands.whitelist.remove.success": "%s l'è stad/stada tolt/tolta via de la lista bianca", "commands.worldborder.center.failed": "Nient a l'è cambiad. El center del confin del mond a l'è sgiamò in quella posizzion chì", "commands.worldborder.center.success": "El center del confin del mond a l'è stad cambiad in %s, %s", "commands.worldborder.damage.amount.failed": "A l'è cambiad nagot. I dagn subid al confin del mond a inn sgiamò in quella quantità chì", "commands.worldborder.damage.amount.success": "La quantità de dagn foeura del confin del mond l'è stada impostada a %s per bloch per ogni segond", "commands.worldborder.damage.buffer.failed": "Nient a l'è cambiad. La zona segura foeura del confin del mond a l'è sgiamò a quella distanza chì", "commands.worldborder.damage.buffer.success": "La zona sicura fuori dal confine del mondo è stata impostata a %s blocco/i", "commands.worldborder.get": "Al momento il confine del mondo è largo %s blocco/i", "commands.worldborder.set.failed.big": "El bord del mond el poeul minga vesser pussee grand de %s bloch", "commands.worldborder.set.failed.far": "El bord del mond el poeu mìa havégh pussee de %s bloch", "commands.worldborder.set.failed.nochange": "Nient a l'è cambiad. El center del confin del mond el gh'ha sgiamò quella larghezza chì", "commands.worldborder.set.failed.small": "I confin del mond poeuden mìa vesser pussee piscinit de 1 bloch", "commands.worldborder.set.grow": "El confin del mond l'è 'dree a slargàss foeura fina a vegnì %s bloch pussee largh in %s segond", "commands.worldborder.set.immediate": "Il confine del mondo è stato impostato a %s blocco/i di larghezza", "commands.worldborder.set.shrink": "Restrizione del confine del mondo a %s blocco/i di larghezza in %s secondo/i", "commands.worldborder.warning.distance.failed": "A l''è cambiad nagot. L'avis del confin del mond l'è sgiamò a quella distanza chì", "commands.worldborder.warning.distance.success": "La distanza di avvertimento del confine del mondo è stata impostata a %s blocco/i", "commands.worldborder.warning.time.failed": "A l'è cambiad nagot. El temp de avertiment del confin del mond l'è sgiamò quell temp chì", "commands.worldborder.warning.time.success": "Il tempo di avvertimento del confine del mondo è stato impostato a %s secondo/i", "compliance.playtime.greaterThan24Hours": "Te seet 'dree a sgiugà de pussee de 24 ore", "compliance.playtime.hours": "Te seet 'dree a sgiugà de %s ora/e", "compliance.playtime.message": "El trop sgiugà el podaria interferì con la vita de tucc i dì", "connect.aborted": "<PERSON><PERSON><PERSON>", "connect.authorizing": "'Dree a andà dent...", "connect.connecting": "Conession al server...", "connect.encrypting": "'Dree a critografà...", "connect.failed": "Eror a conetes al server", "connect.failed.transfer": "Connessione non riuscita durante il trasferimento al server", "connect.joining": "'Dree a sgiontàss al mond...", "connect.negotiating": "'Dree a negozzi<PERSON>...", "connect.reconfiging": "Riconfigurazione in corso...", "connect.reconfiguring": "Riconfigurazione in corso...", "connect.transferring": "Trasferimento a un nuovo server...", "container.barrel": "<PERSON><PERSON>", "container.beacon": "Far", "container.beehive.bees": "Api: %s/%s", "container.beehive.honey": "Miele: %s/%s", "container.blast_furnace": "Volt-forn", "container.brewing": "<PERSON><PERSON>", "container.cartography_table": "Tavol de cartografia", "container.chest": "Cesta", "container.chestDouble": "Cesta granda", "container.crafter": "Fabbricatore", "container.crafting": "Fabricazzion", "container.creative": "Scerna de lavorà", "container.dispenser": "Scompartidor", "container.dropper": "<PERSON><PERSON><PERSON>", "container.enchant": "Instria", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lapislazzer", "container.enchant.lapis.one": "1 lapislazzer", "container.enchant.level.many": "%s nivell d'instriament", "container.enchant.level.one": "1 nivell de esperienza", "container.enchant.level.requirement": "Livei necessari: %s", "container.enderchest": "<PERSON><PERSON> de ender", "container.furnace": "Fornas", "container.grindstone_title": "Giusta e desencanta", "container.hopper": "Tramoeugia", "container.inventory": "Inventari", "container.isLocked": "%s l'è blocad!", "container.lectern": "<PERSON><PERSON>", "container.loom": "Telar", "container.repair": "<PERSON><PERSON><PERSON> e dà un nom", "container.repair.cost": "Cost: %1$s", "container.repair.expensive": "Trop car!", "container.shulkerBox": "Scatola <PERSON>", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "e %s olter...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Fumegador", "container.spectatorCantOpen": "Se poeu mìa dervì. El contegnud a l'è stad gnan<PERSON>ò fad su.", "container.stonecutter": "Taja-preje", "container.upgrade": "Mejora un lavorà", "container.upgrade.error_tooltip": "L'oggetto non può essere migliorato in questo modo", "container.upgrade.missing_template_tooltip": "Sgionta un model del ferrer", "controls.keybinds": "Configurazzion boton...", "controls.keybinds.duplicateKeybinds": "Questo tasto viene usato anche per:\n%s", "controls.keybinds.title": "Configurazzion boton", "controls.reset": "Retaca", "controls.resetAll": "Retaca tast", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Cata foeura un bioma", "createWorld.customize.buffet.title": "Personaliza<PERSON>on mond buffet", "createWorld.customize.flat.height": "Altezza", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Fond - %s", "createWorld.customize.flat.layer.top": "Scima - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON> via livell", "createWorld.customize.flat.tile": "Material livei", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets": "<PERSON><PERSON>", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON>, te el chì un quai che hem fad inanz!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON> meder", "createWorld.customize.presets.share": "Te voeulet scompartì el meder cont un quaivun? <PERSON>vra el quader chì de sota!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON> un meder", "createWorld.preparing": "'Dree a prontà la creazzion del mond...", "createWorld.tab.game.title": "Partida", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Mond", "credits_and_attribution.button.attribution": "Attribuzioni", "credits_and_attribution.button.credits": "Recognossiment", "credits_and_attribution.button.licenses": "Licenze", "credits_and_attribution.screen.title": "Riconoscimenti e attribuzioni", "dataPack.bundle.description": "Attiva il sacchetto come oggetto sperimentale", "dataPack.bundle.name": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Mostra la posizione degli altri giocatori in multigiocatore", "dataPack.locator_bar.name": "Barra di localizzazione", "dataPack.minecart_improvements.description": "Movimento dei vagonetti migliorato", "dataPack.minecart_improvements.name": "Miglioramenti dei vagonetti", "dataPack.redstone_experiments.description": "Modifiche sperimentali alla redstone", "dataPack.redstone_experiments.name": "Redstone sperimentale", "dataPack.title": "Scerniss i pachet dacc", "dataPack.trade_rebalance.description": "Scambi con i villici aggiornati", "dataPack.trade_rebalance.name": "Riequilibrio degli scambi con i villici", "dataPack.update_1_20.description": "Nuovi contenuti e funzionalità per Minecraft 1.20", "dataPack.update_1_20.name": "Atualizazzion 1.20", "dataPack.update_1_21.description": "Nuovi contenuti e funzionalità per Minecraft 1.21", "dataPack.update_1_21.name": "Aggiornamento 1.21", "dataPack.validation.back": "<PERSON>à indree", "dataPack.validation.failed": "Se poeu mìa validà i pachet dacc!", "dataPack.validation.reset": "Restabiliss", "dataPack.validation.working": "'Dree a validà i pachet de dacc scernid...", "dataPack.vanilla.description": "I dat predefinid del Minecraft", "dataPack.vanilla.name": "De bas", "dataPack.winter_drop.description": "Novità del rilascio invernale", "dataPack.winter_drop.name": "Rilascio invernale", "datapackFailure.safeMode": "<PERSON><PERSON> segura", "datapackFailure.safeMode.failed.description": "Questo mondo contiene dati di salvataggio non validi o danneggiati.", "datapackFailure.safeMode.failed.title": "Impossibile caricare il mondo in modalità provvisoria.", "datapackFailure.title": "Alcuni errori nei pacchetti di dati selezionati hanno impedito il caricamento del mondo.\nPuoi provare a caricarlo utilizzando solo il pacchetto di dati vanilla («modalità provvisoria») oppure tornare al menu principale e risolvere il problema manualmente.", "death.attack.anvil": "Un'incusgen l'ha schisciad %1$s", "death.attack.anvil.player": "Un'incusgen l'ha schisciad %1$s in del combater contra %2$s", "death.attack.arrow": "%2$s l'ha colpid %1$s", "death.attack.arrow.item": "%2$s l'ha colpid %1$s con %3$s", "death.attack.badRespawnPoint.link": "Dissegn vorsud del sgioeugh", "death.attack.badRespawnPoint.message": "%1$s a l'è stad/a copad/a de %2$s", "death.attack.cactus": "%1$s l'è mort besiad", "death.attack.cactus.player": "%1$s l'è andad/a contra un cactus in del scapà de %2$s", "death.attack.cramming": "<PERSON><PERSON>'hann dad tropa pression a %1$s", "death.attack.cramming.player": "%2$s el/la gh'ha dad tropa pression a %1$s", "death.attack.dragonBreath": "%1$s è stato/a arrostito/a nel soffio di drago", "death.attack.dragonBreath.player": "%1$s è stato/a arrostito/a nel soffio di drago da %2$s", "death.attack.drown": "%1$s l'è negad/a", "death.attack.drown.player": "%1$s l'è negad/a in del scapà de %2$s", "death.attack.dryout": "%1$s a l'è mort/a de desidratazzion", "death.attack.dryout.player": "%1$s a l'è mort/a de desidratazzion in del scapà del/la %2$s", "death.attack.even_more_magic": "An'mò pussee magia l'ha copad %1$s", "death.attack.explosion": "%1$s l'è s'ciopad", "death.attack.explosion.player": "%2$s l'ha fad s'ciopà %1$s", "death.attack.explosion.player.item": "%2$s l'ha fad s'ciopà %1$s con %3$s", "death.attack.fall": "%1$s a l'è borlad sgiò in terra", "death.attack.fall.player": "%1$s l'è borlad/a sgiò in terra in del scapà de %2$s", "death.attack.fallingBlock": "%1$s l'è stad/a schisciad/a d'un bloch", "death.attack.fallingBlock.player": "Un bloch l'ha schisciad %1$s in del combater contra %2$s", "death.attack.fallingStalactite": "%1$s l'è stad infilzad d'una stalatite che l'era 'dree a borlà sgiò", "death.attack.fallingStalactite.player": "In del combater con  %2$s, %1$s l'è stad infilzad d'una stalatite che l'era 'dree a borlà sgiò", "death.attack.fireball": "%2$s l'ha ciapad a balle de foeugh %1$s", "death.attack.fireball.item": "%2$s l'ha ciapad a balle de foeugh %1$s con %3$s", "death.attack.fireworks": "%1$s l'è andad/a via cont el bot", "death.attack.fireworks.item": "%1$s se n'è andad/a cont el bot per via de un razz pirotecnegh tirad de %2$s con %3$s", "death.attack.fireworks.player": "%1$s l'è andad/a via cont el bot in del combater contra %2$s", "death.attack.flyIntoWall": "%1$s l'ha sperimentad l'energia cinetega", "death.attack.flyIntoWall.player": "%1$s l'ha sperimentad l'energia cinetega in del scapà de %2$s", "death.attack.freeze": "%1$s gh'è mort de fregg", "death.attack.freeze.player": "%1$s l'è mort sgelad per via de %2$s", "death.attack.generic": "%1$s l'è mort/a", "death.attack.generic.player": "%1$s l'è mort/a per via de %2$s", "death.attack.genericKill": "%1$s a l'è stad/a copad/a", "death.attack.genericKill.player": "%1$s a l'è stad/a copad/a in del combater contra %2$s", "death.attack.hotFloor": "%1$s l'ha descovert che el soeul l'era lava", "death.attack.hotFloor.player": "%1$s è finito/a in una zona pericolosa a causa di %2$s", "death.attack.inFire": "Hann tacad foeugh a %1$s", "death.attack.inFire.player": "%1$s l'è brusad in del foeugh al combater contra %2$s", "death.attack.inWall": "%1$s l'è sofegad/a in d'un mur", "death.attack.inWall.player": "%1$s l'è sofegad/a in d'un mur in del combater contra %2$s", "death.attack.indirectMagic": "%1$s a l'è stad/a copad/a de %2$s con la magia", "death.attack.indirectMagic.item": "%1$s l'è stad/a copad/a de %2$s con %3$s", "death.attack.lava": "%1$s l'ha provad a nodà in de la lava", "death.attack.lava.player": "%1$s l'ha provad a nodà in de la lava in del scapà de %2$s", "death.attack.lightningBolt": "Una saeta l'ha centrad %1$s", "death.attack.lightningBolt.player": "Una saeta l'ha centrad %1$s al combater contra %2$s", "death.attack.mace_smash": "%1$s è stato/a fracassato/a da %2$s", "death.attack.mace_smash.item": "%1$s è stato/a fracassato/a da %2$s con %3$s", "death.attack.magic": "La magia l'ha copad %1$s", "death.attack.magic.player": "La magia l'ha copad %1$s al scapà de %2$s", "death.attack.message_too_long": "Siamo spiacenti, ma il messaggio era troppo lungo per essere inviato in forma completa. Ecco la versione accorciata: %s", "death.attack.mob": "%2$s l'ha copad %1$s", "death.attack.mob.item": "%2$s l'ha copad %1$s con %3$s", "death.attack.onFire": "%1$s l'è mort/a brusad/a", "death.attack.onFire.item": "%1$s è stato/a ridotto/a in cenere lottando con %2$s che impugnava %3$s", "death.attack.onFire.player": "%1$s l'è brusad/a al combater contra %2$s", "death.attack.outOfWorld": "%1$s l'è borlad/a foeura del mond", "death.attack.outOfWorld.player": "%1$s el voleva pu viver in de l'istess mond de %2$s", "death.attack.outsideBorder": "%1$s ha oltrepassato i confini del mondo", "death.attack.outsideBorder.player": "%1$s ha oltrepassato i confini del mondo lottando con %2$s", "death.attack.player": "%1$s è stato/a ucciso/a da %2$s", "death.attack.player.item": "%1$s è stato/a ucciso/a da %2$s con %3$s", "death.attack.sonic_boom": "%1$s l'è stad obliterad de un sgar cargad a son", "death.attack.sonic_boom.item": "%1$s è stato/a obliterato/a da un urlo caricato sonicamente fuggendo da %2$s che impugnava %3$s", "death.attack.sonic_boom.player": "%1$s l'è stad obliterad de un sgar cargad a son intratant che el scapava de %2$s", "death.attack.stalagmite": "Una stalagmite l'ha impalad el/la %1$s", "death.attack.stalagmite.player": "%1$s l'è stad/stada impalada d'una stalagmite in del scombater con %2$s", "death.attack.starve": "%1$s l'è mort/a de fam", "death.attack.starve.player": "%1$s l'è mort/a de fam in del combater contra %2$s", "death.attack.sting": "%1$s l'è mort/a sponsgiud/a", "death.attack.sting.item": "%1$s è stato/a punto/a a morte da %2$s con %3$s", "death.attack.sting.player": "%1$s l'è mort/a sponsgiud/a de %2$s", "death.attack.sweetBerryBush": "%1$s l'è mort besiad d'un boscal de frut dolz", "death.attack.sweetBerryBush.player": "%1$s a l'è mort besiad d'un boscal de frut dolz in del scapà de %2$s", "death.attack.thorns": "%1$s l'è mort in del provà a ferì %2$s", "death.attack.thorns.item": "%1$s l'è stad/a copad/a de %3$s al provà a ferì %2$s", "death.attack.thrown": "%2$s l'ha colpid el/la %1$s", "death.attack.thrown.item": "%2$s l'ha segutad a colpì %1$s con %3$s", "death.attack.trident": "%2$s l'ha impalad %1$s", "death.attack.trident.item": "%2$s l'ha impalad %1$s con %3$s", "death.attack.wither": "%1$s l'è impassid/a", "death.attack.wither.player": "%1$s l'è impassid/a in del combater contra %2$s", "death.attack.witherSkull": "Un crani de %2$s l'ha colpid %1$s", "death.attack.witherSkull.item": "%1$s è stato/a colpito/a da un teschio di %2$s con %3$s", "death.fell.accident.generic": "%1$s l'è borlad/a sgiò de un sit volt", "death.fell.accident.ladder": "%1$s l'è borlad/a sgiò de una scala a man", "death.fell.accident.other_climbable": "%1$s l'è borlad/a sgiò al rampegàss", "death.fell.accident.scaffolding": "%1$s l'è borlad/a sgiò d'un pontegg", "death.fell.accident.twisting_vines": "%1$s l'è borlad/a sgiò de di ergne intortie", "death.fell.accident.vines": "%1$s l'è borlad/a sgiò de di ergne", "death.fell.accident.weeping_vines": "%1$s l'è borlad/a sgiò de di ergne caragnente", "death.fell.assist": "%2$s l'ha ruzad sgiò %1$s", "death.fell.assist.item": "%2$s l'ha ruzad sgiò %1$s con %3$s", "death.fell.finish": "%1$s l'è borlad/a sgiò e l'è stad finid/a de %2$s", "death.fell.finish.item": "%1$s l'è borlad/a sgiò e l'è stad finid/a de %2$s con %3$s", "death.fell.killer": "%1$s l'è stad/a ruzad/a sgiò", "deathScreen.quit.confirm": "Te seet segur de volé andà foeura?", "deathScreen.respawn": "<PERSON><PERSON>", "deathScreen.score": "<PERSON>", "deathScreen.score.value": "Punteggio: %s", "deathScreen.spectate": "Renass 'me spetador", "deathScreen.title": "Te seet mort!", "deathScreen.title.hardcore": "Fin del sgioeugh!", "deathScreen.titleScreen": "Schermada del titol", "debug.advanced_tooltips.help": "F3 + H = Descriz<PERSON><PERSON> vanzade", "debug.advanced_tooltips.off": "Descrizzion vanzada: scondude", "debug.advanced_tooltips.on": "Descrizzion avanzade: visibel", "debug.chunk_boundaries.help": "F3 + G = Mostra i confin di toch", "debug.chunk_boundaries.off": "Confin di toch: scondud", "debug.chunk_boundaries.on": "Confin di toch: visibel", "debug.clear_chat.help": "F3 + D = Neta la ciciarada", "debug.copy_location.help": "F3 + C = Copia la posizzion 'me comand /tp; mantegnii schischiad sgiò per fermà el sgioeugh", "debug.copy_location.message": "Sit copiad in di apunt", "debug.crash.message": "Te seet 'dree a tegnì schisciad F3 + C. Se te i lasset minga el sgioeugh el desmetarà de fonzionà.", "debug.crash.warning": "El sgioeugh el desmetarà de fonzionà in %s...", "debug.creative_spectator.error": "Non hai il permesso di cambiare modalità di gioco", "debug.creative_spectator.help": "F3 + N = Cambia tra la modalità di gioco precedente e la modalità spettatore", "debug.dump_dynamic_textures": "Grafiche dinamiche salvate in %s", "debug.dump_dynamic_textures.help": "F3 + S = Esporta le grafiche dinamiche", "debug.gamemodes.error": "Te gh'heet minga el permess de cambià la modalità de sgioeugh", "debug.gamemodes.help": "F3 + F4 = Derva el menu per cambià la modalità de sgioeugh", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Dopo", "debug.help.help": "F3 + Q = Mostra quella lista chì", "debug.help.message": "Boton de schiscià:", "debug.inspect.client.block": "<PERSON><PERSON> b<PERSON><PERSON> (banda client) copiad in di apont", "debug.inspect.client.entity": "<PERSON>t entità (banda client) copiad in di apont", "debug.inspect.help": "F3 + I = Copia in di apont i dat bloch o entità", "debug.inspect.server.block": "<PERSON><PERSON> bloc<PERSON> (banda server) copiad in di apont", "debug.inspect.server.entity": "<PERSON><PERSON> entit<PERSON> (banda server) copiad in di apont", "debug.pause.help": "F3 + Esc = Sospend senza dervì el menù de pausa (domà se el se poeul sospender)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON>t in pausa quand che la finestra la se smorza", "debug.pause_focus.off": "Pausa al sarrà el scherm: no", "debug.pause_focus.on": "Pausa al sarrà el scherm: sì", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Dàgh l'inviada o ferma la profiladura", "debug.profiling.start": "Profiladura di %s second partida. Schiscia F3 + L per fermala in anticip", "debug.profiling.stop": "Profiladura ultimada. Risultad salvad in %s", "debug.reload_chunks.help": "F3 + A = <PERSON>ga an'mò i toch", "debug.reload_chunks.message": "'Dree a caregà an'mò tucc i toch", "debug.reload_resourcepacks.help": "F3 + T = Carega de noeuv i pachet de risorse", "debug.reload_resourcepacks.message": "<PERSON>het de resorse caregad an'mò", "debug.show_hitboxes.help": "F3 + B = Mostra i hitbox", "debug.show_hitboxes.off": "Hitbox: scondud", "debug.show_hitboxes.on": "Hitbox: mostrad", "debug.version.header": "Informazioni sulla versione del client:", "debug.version.help": "F3 + V = Informazioni sulla versione del client", "demo.day.1": "La proeuva la durarà cinch dì de sgioeugh. Forza!", "demo.day.2": "<PERSON><PERSON> du", "demo.day.3": "<PERSON>ì tri", "demo.day.4": "<PERSON><PERSON> quater", "demo.day.5": "Ultim dì!", "demo.day.6": "L'è passad el to quint di. Dovra %s per salvà una schermada de la toa creazzion.", "demo.day.warning": "El to temp l'è 'dree a finì!", "demo.demoExpired": "La proeuva l'è finida!", "demo.help.buy": "Crompa adess!", "demo.help.fullWrapped": "Quella proeuva chì la durarà 5 dì de sgioeugh (circa 1 ora e 40 minut del temp real). Dà un'oggiada ai progress per havégh consili. Divertes!", "demo.help.inventory": "Schiscia %1$s per dervì el to inventari", "demo.help.jump": "Schiscia %1$s per saltà", "demo.help.later": "Se<PERSON>ta a s<PERSON>!", "demo.help.movement": "Dovra i tast %1$s, %2$s, %3$s, %4$s e el ratin per moeuves", "demo.help.movementMouse": "<PERSON><PERSON>ra el ratin per vardàss intorna", "demo.help.movementShort": "Schiscia %1$s, %2$s, %3$s, %4$s per moeuves", "demo.help.title": "Modalità proeuva del Minecraft", "demo.remainingTime": "Temp che resta: %s", "demo.reminder": "La proeuva l'è finida. Crompa el sgioeugh per andà inanz o fà su un mond noeuv!", "difficulty.lock.question": "Te seet segur de volé blocà la dificoltà del mond? La sarà blocada per semper a %1$s e te podareet pu cambiàlla.", "difficulty.lock.title": "Bloca la dificoltà del mond", "disconnect.endOfStream": "Fin de la conession", "disconnect.exceeded_packet_rate": "Casciad per havé superad el limit de pachet", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Richiesta di stato ignorata", "disconnect.loginFailedInfo": "Eror a vegnì dent: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El multisgiugador a l'è smorzad. Controlla i configurazzion del to cunt del Minecraft.", "disconnect.loginFailedInfo.invalidSession": "Session mìa valida (proeuva a retacà el sgioeugh e el launcher)", "disconnect.loginFailedInfo.serversUnavailable": "I server de autentegazzion a inn minga acessibel al moment. Per p<PERSON>, proeuva ancam<PERSON>.", "disconnect.loginFailedInfo.userBanned": "Te seet bandid del s<PERSON>ugà in linea", "disconnect.lost": "Conession perduda", "disconnect.packetError": "Errore del protocollo di rete", "disconnect.spam": "Casciad per spam", "disconnect.timeout": "Temp scadud", "disconnect.transfer": "Trasferito a un altro server", "disconnect.unknownHost": "Ospit <PERSON>", "download.pack.failed": "Impossibile scaricare %s pacchetti su %s", "download.pack.progress.bytes": "Avanzamento: %s (dimensioni totali sconosciute)", "download.pack.progress.percent": "Avanzamento: %s%%", "download.pack.title": "Download del pacchetto di risorse: %s/%s", "editGamerule.default": "De bas: %s", "editGamerule.title": "Modifega i regolle de sgioeugh", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Surbiment", "effect.minecraft.bad_omen": "Segn de pobia", "effect.minecraft.blindness": "Inorbiment", "effect.minecraft.conduit_power": "Canalizazzion", "effect.minecraft.darkness": "<PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Grazzia del delfin", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> al <PERSON>", "effect.minecraft.glowing": "Luminiscenza", "effect.minecraft.haste": "<PERSON><PERSON> minoeura", "effect.minecraft.health_boost": "Salut cressuda", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON> del <PERSON>s", "effect.minecraft.hunger": "Fam", "effect.minecraft.infested": "Infestazione", "effect.minecraft.instant_damage": "Dagn instantaneo", "effect.minecraft.instant_health": "Cura instantanea", "effect.minecraft.invisibility": "Invisibilità", "effect.minecraft.jump_boost": "Supersalt", "effect.minecraft.levitation": "Levitazzion", "effect.minecraft.luck": "Fortuna", "effect.minecraft.mining_fatigue": "Fadi<PERSON>", "effect.minecraft.nausea": "Ingossa", "effect.minecraft.night_vision": "Vision per la nocc", "effect.minecraft.oozing": "Trasudazione", "effect.minecraft.poison": "<PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Presag. incursivo", "effect.minecraft.regeneration": "Regenerazzion", "effect.minecraft.resistance": "Resistenza", "effect.minecraft.saturation": "Saturazzion", "effect.minecraft.slow_falling": "Bo<PERSON><PERSON> lenta", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON>", "effect.minecraft.speed": "Velocità", "effect.minecraft.strength": "Forza", "effect.minecraft.trial_omen": "Presag. sfidante", "effect.minecraft.unluck": "<PERSON><PERSON><PERSON>", "effect.minecraft.water_breathing": "Respirazzion aquatega", "effect.minecraft.weakness": "Debolezza", "effect.minecraft.weaving": "Tessitura", "effect.minecraft.wind_charged": "<PERSON><PERSON> ventosa", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON><PERSON><PERSON> efet", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Respirazzion sotamarina", "enchantment.minecraft.bane_of_arthropods": "Desgrazzia di artropod", "enchantment.minecraft.binding_curse": "Maledizzion del ligam", "enchantment.minecraft.blast_protection": "Protezzion dei s'ciop", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Canalizazzion", "enchantment.minecraft.density": "Densità", "enchantment.minecraft.depth_strider": "Pass sotamarin", "enchantment.minecraft.efficiency": "Eficenza", "enchantment.minecraft.feather_falling": "Bo<PERSON>ada morbida", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Protezzion del foeugh", "enchantment.minecraft.flame": "Fiama", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Pass giazzad", "enchantment.minecraft.impaling": "Impalament", "enchantment.minecraft.infinity": "Infinit", "enchantment.minecraft.knockback": "Contracolp", "enchantment.minecraft.looting": "<PERSON><PERSON>", "enchantment.minecraft.loyalty": "Fedeltà", "enchantment.minecraft.luck_of_the_sea": "Fortuna del mar", "enchantment.minecraft.lure": "Lisca", "enchantment.minecraft.mending": "Reparazzion", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON> tir insema", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Forza", "enchantment.minecraft.projectile_protection": "Protezzion di balle", "enchantment.minecraft.protection": "Protezzion", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "Carega svelta", "enchantment.minecraft.respiration": "Respirazzion", "enchantment.minecraft.riptide": "Corrent de ritorna", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Toch de seda", "enchantment.minecraft.smite": "<PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Velocità di anime", "enchantment.minecraft.sweeping": "<PERSON>", "enchantment.minecraft.sweeping_edge": "<PERSON>", "enchantment.minecraft.swift_sneak": "Pass lesger", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Ins'cepabilità", "enchantment.minecraft.vanishing_curse": "Maledizzion del spariment", "enchantment.minecraft.wind_burst": "Colpo di vento", "entity.minecraft.acacia_boat": "Barca di acacia", "entity.minecraft.acacia_chest_boat": "Barca di acacia con baule", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nivola de efet a area", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Suport per armadure", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Zattera di bambù con baule", "entity.minecraft.bamboo_raft": "Zattera di bambù", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Ava", "entity.minecraft.birch_boat": "Barca di betulla", "entity.minecraft.birch_chest_boat": "Barca di betulla con baule", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.boat": "Barca", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Brezza", "entity.minecraft.breeze_wind_charge": "Carica di vento", "entity.minecraft.camel": "Camell", "entity.minecraft.cat": "Gat", "entity.minecraft.cave_spider": "Ragn di grote", "entity.minecraft.cherry_boat": "Barca di ciliegio", "entity.minecraft.cherry_chest_boat": "Barca di ciliegio con baule", "entity.minecraft.chest_boat": "Barca con baule", "entity.minecraft.chest_minecart": "<PERSON>ell con cesta", "entity.minecraft.chicken": "Pollaster", "entity.minecraft.cod": "Merluzz", "entity.minecraft.command_block_minecart": "Carrell con bloch di comand", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Barca di quercia scura", "entity.minecraft.dark_oak_chest_boat": "Barca di quercia scura con baule", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "<PERSON><PERSON> de <PERSON>ugh de dragon", "entity.minecraft.drowned": "Negad", "entity.minecraft.egg": "Oeuv tirad", "entity.minecraft.elder_guardian": "Guardian antigh", "entity.minecraft.end_crystal": "Cristall de l'End", "entity.minecraft.ender_dragon": "Dragon de l'End", "entity.minecraft.ender_pearl": "Perla de l'ender tirada", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evocador", "entity.minecraft.evoker_fangs": "Dentasc de evocador", "entity.minecraft.experience_bottle": "Botilia d'esperienza tirada", "entity.minecraft.experience_orb": "Sfera d'esperienza", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON> de l'ender", "entity.minecraft.falling_block": "<PERSON><PERSON> 'dree a borlà sgiò", "entity.minecraft.falling_block_type": "%s l'è 'dree a borlà sgiò", "entity.minecraft.fireball": "<PERSON><PERSON>", "entity.minecraft.firework_rocket": "Razz pirotecnegh", "entity.minecraft.fishing_bobber": "Gallegiant", "entity.minecraft.fox": "Volp", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "<PERSON>ell con fornas", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Gigant", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON> s<PERSON>a", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardian", "entity.minecraft.happy_ghast": "Ghast allegro", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Carrell de minera con tramoeugia", "entity.minecraft.horse": "Cavall", "entity.minecraft.husk": "Zombie del desert", "entity.minecraft.illusioner": "Illusionista", "entity.minecraft.interaction": "Interazione", "entity.minecraft.iron_golem": "<PERSON><PERSON> de <PERSON>", "entity.minecraft.item": "Lavorà", "entity.minecraft.item_display": "Visualizzatore di oggetti", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Barca della giungla", "entity.minecraft.jungle_chest_boat": "Barca della giungla con baule", "entity.minecraft.killer_bunny": "<PERSON> conili assassin", "entity.minecraft.leash_knot": "Grop del sguinzal", "entity.minecraft.lightning_bolt": "Saeta", "entity.minecraft.lingering_potion": "Pozione persistente", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Spuva de lama", "entity.minecraft.magma_cube": "Cub de magma", "entity.minecraft.mangrove_boat": "Barca di mangrovia", "entity.minecraft.mangrove_chest_boat": "Barca di mangrovia con baule", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "<PERSON><PERSON> de minera", "entity.minecraft.mooshroom": "Vaca-fonsg", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Barca di quercia", "entity.minecraft.oak_chest_boat": "Barca di quercia con baule", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Generatore di oggetti infausti", "entity.minecraft.painting": "<PERSON>uader", "entity.minecraft.pale_oak_boat": "Barca di quercia pallida", "entity.minecraft.pale_oak_chest_boat": "Barca di quercia pallida con baule", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "Fantasma", "entity.minecraft.pig": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin brut", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Ors polar", "entity.minecraft.potion": "Pozzion", "entity.minecraft.pufferfish": "<PERSON><PERSON>-balla", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "S'cepa-tut", "entity.minecraft.salmon": "Salmon", "entity.minecraft.sheep": "Pegora", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON> d'<PERSON>", "entity.minecraft.skeleton": "Schel<PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON> schelter", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "<PERSON><PERSON> piscinina", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Golem de fioca", "entity.minecraft.snowball": "Balla de fioca", "entity.minecraft.spawner_minecart": "<PERSON>ell con sgenerador de creadure", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> s<PERSON>", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Pozione da lancio", "entity.minecraft.spruce_boat": "Barca di abete", "entity.minecraft.spruce_chest_boat": "Barca di abete con baule", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "<PERSON><PERSON><PERSON> fi<PERSON>", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Esposidor de <PERSON>", "entity.minecraft.tnt": "TNT pizzada", "entity.minecraft.tnt_minecart": "Carrell con TNT", "entity.minecraft.trader_llama": "<PERSON>", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Pess tropegal", "entity.minecraft.tropical_fish.predefined.0": "<PERSON>ess anemon", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON> cerus<PERSON>h negher", "entity.minecraft.tropical_fish.predefined.10": "Idol moresch", "entity.minecraft.tropical_fish.predefined.11": "<PERSON>ess parpai ornad", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> papa<PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON> ansgiol regina", "entity.minecraft.tropical_fish.predefined.14": "Ciclid ross", "entity.minecraft.tropical_fish.predefined.15": "Pess-serpent ross", "entity.minecraft.tropical_fish.predefined.16": "Dentisc ross", "entity.minecraft.tropical_fish.predefined.17": "Pess capitan", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON> pajasc tomates", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON> balestra", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON> c<PERSON><PERSON>h bloeu", "entity.minecraft.tropical_fish.predefined.20": "<PERSON>ess papagall cont alete sgialde", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON> cerus<PERSON>h s<PERSON>ld", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON> parpai", "entity.minecraft.tropical_fish.predefined.4": "Ciclid", "entity.minecraft.tropical_fish.predefined.5": "Pess-pajasc", "entity.minecraft.tropical_fish.predefined.6": "Pess scombatent roeusa", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Sgagnador ross", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON> bloch", "entity.minecraft.tropical_fish.type.brinely": "<PERSON>ess pelagegh", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON> terracrea", "entity.minecraft.tropical_fish.type.dasher": "Pess scat", "entity.minecraft.tropical_fish.type.flopper": "Pess saltador", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON> s<PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON> smaggia", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON>ess ragg de sol", "entity.minecraft.turtle": "<PERSON><PERSON> scudellera", "entity.minecraft.vex": "Spirit", "entity.minecraft.villager": "Villan", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "Prevost", "entity.minecraft.villager.farmer": "Paisan", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "<PERSON><PERSON> de flizze", "entity.minecraft.villager.leatherworker": "Conficior", "entity.minecraft.villager.librarian": "Bibliotecari", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Bambo", "entity.minecraft.villager.none": "Villan", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Armaroeul", "entity.minecraft.vindicator": "Vendegador", "entity.minecraft.wandering_trader": "Mercant ramingh", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Carica di vento", "entity.minecraft.witch": "Stria", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON> wither", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>v", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "C<PERSON>ll zombie", "entity.minecraft.zombie_villager": "<PERSON>n z<PERSON>i", "entity.minecraft.zombified_piglin": "<PERSON><PERSON>", "entity.not_summonable": "Impossibile evocare l'entità di tipo %s", "event.minecraft.raid": "Incursion", "event.minecraft.raid.defeat": "Batuda", "event.minecraft.raid.defeat.full": "Incursione - Sconfitta", "event.minecraft.raid.raiders_remaining": "Incursor che resten: %s", "event.minecraft.raid.victory": "Vitoria", "event.minecraft.raid.victory.full": "Incursione - Vittoria", "filled_map.buried_treasure": "Mapa del tesor", "filled_map.explorer_jungle": "Mappa dell'esploratore di giungle", "filled_map.explorer_swamp": "Mappa dell'esploratore di paludi", "filled_map.id": "ID #%s", "filled_map.level": "(Nivell %s/%s)", "filled_map.locked": "Blocada", "filled_map.mansion": "Mapa de l'esplorador di bosch", "filled_map.monument": "Mapa de l'esplorador de l'ocean", "filled_map.scale": "Scala 1:%s", "filled_map.trial_chambers": "Mappa dell'esploratore della sfida", "filled_map.unknown": "Mapa descognossuda", "filled_map.village_desert": "Mappa del villaggio del deserto", "filled_map.village_plains": "Mappa del villaggio della pianura", "filled_map.village_savanna": "Mappa del villaggio della savana", "filled_map.village_snowy": "Mappa del villaggio innevato", "filled_map.village_taiga": "Mappa del villaggio della taiga", "flat_world_preset.minecraft.bottomless_pit": "Pozz senza fond", "flat_world_preset.minecraft.classic_flat": "<PERSON><PERSON>", "flat_world_preset.minecraft.desert": "Desert", "flat_world_preset.minecraft.overworld": "Superfiss", "flat_world_preset.minecraft.redstone_ready": "Prontad per prejarossa", "flat_world_preset.minecraft.snowy_kingdom": "Regn de nev", "flat_world_preset.minecraft.the_void": "El Voeui", "flat_world_preset.minecraft.tunnelers_dream": "Sogn del minoeur", "flat_world_preset.minecraft.water_world": "Mond d'aqua", "flat_world_preset.unknown": "???", "gameMode.adventure": "Modalità aventura", "gameMode.changed": "La modalità de sgioeugh l'è cambiada in %s", "gameMode.creative": "Modalità crea", "gameMode.hardcore": "Modalità estrema!", "gameMode.spectator": "Modalità spetador", "gameMode.survival": "Modalità soraviv", "gamerule.allowFireTicksAwayFromPlayer": "Aggiorna il fuoco lontano dai gio<PERSON>ori", "gamerule.allowFireTicksAwayFromPlayer.description": "Controlla se il fuoco e la lava sono attivi anche a una distanza maggiore di 8 chunk da ogni giocatore", "gamerule.announceAdvancements": "Avisa di progress", "gamerule.blockExplosionDropDecay": "L'esplosione di blocchi distrugge oggetti rilasciabili", "gamerule.blockExplosionDropDecay.description": "Quando l'interazione con blocchi causa esplosioni che distruggono blocchi, viene perduta parte degli oggetti normalmente rilasciabili.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "<PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON>", "gamerule.category.mobs": "Creadure", "gamerule.category.player": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Creazzion", "gamerule.category.updates": "Atualizazzion del mond", "gamerule.commandBlockOutput": "Fà vedé risultad di bloch de comand", "gamerule.commandModificationBlockLimit": "Limite dei blocchi modificabili per comando", "gamerule.commandModificationBlockLimit.description": "Numero massimo di blocchi che possono essere cambiati contemporaneamente da un solo comando, come «/fill» o «/clone».", "gamerule.disableElytraMovementCheck": "Smorza controll de moviment di eliter", "gamerule.disablePlayerMovementCheck": "Disattiva il controllo del movimento dei giocatori", "gamerule.disableRaids": "Smorza invasion", "gamerule.doDaylightCycle": "Ciclo dì e nocc", "gamerule.doEntityDrops": "Lassa lavorà al s'cepà i entità", "gamerule.doEntityDrops.description": "Controlla i lavorà lassad di carrei minerari (insema ai inventari), di cornis, di barche e inscì inanz.", "gamerule.doFireTick": "<PERSON><PERSON><PERSON>", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON> is<PERSON>a", "gamerule.doInsomnia": "Fà su giubiane", "gamerule.doLimitedCrafting": "Ciama i ricete per fà su", "gamerule.doLimitedCrafting.description": "Se attivata, i giocatori possono fabbricare oggetti solo utilizzando le ricette sbloccate.", "gamerule.doMobLoot": "Lassa lavorà di creadure", "gamerule.doMobLoot.description": "Controlla le risorse che vengono rilasciate dalle creature, comprese le sfere di esperienza.", "gamerule.doMobSpawning": "Fà su di creadure", "gamerule.doMobSpawning.description": "Alcune entità potrebbero avere regole separate.", "gamerule.doPatrolSpawning": "Fà su ronde de lader", "gamerule.doTileDrops": "Lassa i bloch", "gamerule.doTileDrops.description": "Controlla le risorse che vengono rilasciate dai blocchi, comprese le sfere di esperienza.", "gamerule.doTraderSpawning": "Fà su i mercant ramingh", "gamerule.doVinesSpread": "Propagazione dei rampicanti", "gamerule.doVinesSpread.description": "Controlla se i rampicanti possono propagarsi in modo autonomo e casuale verso i blocchi adiacenti. Non ha effetto sugli altri tipi di rampicanti come i rampicanti piangenti, i rampicanti attorcigliati ecc.", "gamerule.doWardenSpawning": "Fà su di warden", "gamerule.doWeatherCycle": "Atualiza temp", "gamerule.drowningDamage": "Dagn per negament", "gamerule.enderPearlsVanishOnDeath": "Le perle di ender lanciate scompaiono dopo la morte", "gamerule.enderPearlsVanishOnDeath.description": "Stabilisce se le perle di ender lanciate da un giocatore scompaiono alla morte di quest'ultimo.", "gamerule.entitiesWithPassengersCanUsePortals": "Le entità con passeggeri possono attraversare i portali", "gamerule.entitiesWithPassengersCanUsePortals.description": "Consenti alle entità con passeggeri di teletrasportarsi nei portali del Nether, portali dell'End e varchi dell'End.", "gamerule.fallDamage": "Dagn per borlada", "gamerule.fireDamage": "Dagn per foeugh", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON> sgiugador mort", "gamerule.forgiveDeadPlayers.description": "I creadure inverse neutrai desmeten de vess invers quand che el sgiugador a l'è mort lì arent.", "gamerule.freezeDamage": "Dagn per via del gel", "gamerule.globalSoundEvents": "Eventi sonori globali", "gamerule.globalSoundEvents.description": "Al verificarsi di determinati eventi, come la generazione di un boss, il suono verrà udito globalmente.", "gamerule.keepInventory": "Salva inventari dopo de la mort", "gamerule.lavaSourceConversion": "Trasformazione della lava in sorgente", "gamerule.lavaSourceConversion.description": "Quando la lava colante è circondata da sorgenti di lava su due lati, si trasformerà in sorgente.", "gamerule.locatorBar": "Attiva la barra di localizzazione", "gamerule.locatorBar.description": "Quando attiva, viene mostrata una barra sullo schermo per indicare la posizione dei giocatori.", "gamerule.logAdminCommands": "Fà savé i comand di aministrador", "gamerule.maxCommandChainLength": "Limit di caden de comand", "gamerule.maxCommandChainLength.description": "Si applica a blocchi comandi a catena e funzioni.", "gamerule.maxCommandForkCount": "Limite dei contesti dei comandi", "gamerule.maxCommandForkCount.description": "Numero massimo dei contesti che possono essere usati da comandi come 'execute as'.", "gamerule.maxEntityCramming": "Limit entità per bloch", "gamerule.minecartMaxSpeed": "Velocità massima dei vagonetti", "gamerule.minecartMaxSpeed.description": "Velocità massima predefinita di un vagonetto in movimento.", "gamerule.mobExplosionDropDecay": "L'esplosione delle creature distrugge oggetti rilasciabili", "gamerule.mobExplosionDropDecay.description": "Quando le creature causano esplosioni che distruggono blocchi, viene perduta parte degli oggetti normalmente rilasciabili.", "gamerule.mobGriefing": "Permet ai mob de destrugà i robe", "gamerule.naturalRegeneration": "Restabiliss salut", "gamerule.playersNetherPortalCreativeDelay": "<PERSON><PERSON> dei portali del Nether (solo modalità creativa)", "gamerule.playersNetherPortalCreativeDelay.description": "Tempo (in tick) che un giocatore in modalità creativa deve trascorrere in un portale del Nether prima di cambiare dimensione.", "gamerule.playersNetherPortalDefaultDelay": "<PERSON><PERSON> dei portali del Nether (esclusa modalità creativa)", "gamerule.playersNetherPortalDefaultDelay.description": "Tempo (in tick) che un giocatore in modalità diverse dalla creativa deve trascorrere in un portale del Nether prima di cambiare dimensione.", "gamerule.playersSleepingPercentage": "Percentual de s<PERSON><PERSON>dor 'dree a dormì", "gamerule.playersSleepingPercentage.description": "La percentual de sgiugador che gh'hann de dormì per passà la noeucc.", "gamerule.projectilesCanBreakBlocks": "Distruzione dei blocchi tramite proiettili", "gamerule.projectilesCanBreakBlocks.description": "Controlla se l'impatto dei proiettili può distruggere i blocchi vulnerabili.", "gamerule.randomTickSpeed": "Velocità random di tick", "gamerule.reducedDebugInfo": "Dà sgiò i informazzion debug", "gamerule.reducedDebugInfo.description": "Limita il contenuto della schermata di debug.", "gamerule.sendCommandFeedback": "Manda i resposte di comand", "gamerule.showDeathMessages": "Mostra i messagg de mort", "gamerule.snowAccumulationHeight": "Altezza di accumulo della neve", "gamerule.snowAccumulationHeight.description": "<PERSON>uando nevica, la neve si accumulerà a terra fino al numero massimo di strati specificato.", "gamerule.spawnChunkRadius": "Raggio dei chunk di generazione", "gamerule.spawnChunkRadius.description": "Numero di chunk che rimangono caricati intorno al punto di generazione globale.", "gamerule.spawnRadius": "Ragg zona de renassida", "gamerule.spawnRadius.description": "Controlla la dimensione dell'area dove i giocatori possono generarsi, centrata intorno al punto di generazione.", "gamerule.spectatorsGenerateChunks": "Permet ai spetador de fà su terren", "gamerule.tntExplodes": "Consente l'innesco e l'esplosione di TNT", "gamerule.tntExplosionDropDecay": "L'esplosione di TNT distrugge oggetti rilasciabili", "gamerule.tntExplosionDropDecay.description": "Quando il TNT causa esplosioni che distruggono blocchi, viene perduta parte degli oggetti normalmente rilasciabili.", "gamerule.universalAnger": "Tucc inrabid", "gamerule.universalAnger.description": "I creadure neutrai inverse i atachen qualsessia sgiugador, mìa nomà i sgiugador che ie fann inversà. El fonziona mei se «Perdonà sgiugador mort» l'è smorzad.", "gamerule.waterSourceConversion": "Trasformazione dell'acqua in sorgente", "gamerule.waterSourceConversion.description": "Quando l'acqua corrente è circondata da sorgenti d'acqua su due lati, si trasformerà in sorgente.", "generator.custom": "Personalizad", "generator.customized": "Personalizad (vegg)", "generator.minecraft.amplified": "AMPLIFICAD", "generator.minecraft.amplified.info": "<PERSON><PERSON>: domà per divertìss! Gh'è besogn de un bon computer.", "generator.minecraft.debug_all_block_states": "Modalità debug", "generator.minecraft.flat": "<PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "Bioma largh", "generator.minecraft.normal": "Normal", "generator.minecraft.single_biome_surface": "Domà un bioma", "generator.single_biome_caves": "<PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON> s<PERSON>", "gui.abuseReport.attestation": "Inviando questa segnalazione, confermi che le informazioni che hai fornito sono accurate e complete al meglio delle tue conoscenze.", "gui.abuseReport.comments": "Coment", "gui.abuseReport.describe": "Scompartì i detai el ne ajutarà a ciapà una decision ben informada.", "gui.abuseReport.discard.content": "Se te veet foeura, te perdareet la segnalazzion e i to coment.\nTe seet segur che te voeulet andà foeura?", "gui.abuseReport.discard.discard": "Và foeura e trà via la segnalazzion", "gui.abuseReport.discard.draft": "Salva 'me sbozz", "gui.abuseReport.discard.return": "Segutà a modifegà", "gui.abuseReport.discard.title": "Trà via la segnalazzion e i coment?", "gui.abuseReport.draft.content": "Vuoi continuare a modificare la segnalazione in corso o annullarla e crearne una nuova?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Seguta a modifegà", "gui.abuseReport.draft.quittotitle.content": "Vuoi continuare a modificarla o annullarla?", "gui.abuseReport.draft.quittotitle.title": "Hai una bozza di segnalazione che andrà persa se abbandoni", "gui.abuseReport.draft.title": "Modificare la bozza della segnalazione?", "gui.abuseReport.error.title": "Gh'è un problema a mandà la to segnalazzion", "gui.abuseReport.message": "Dove hai notato un comportamento scorretto?\nQuesto ci aiuterà ad analizzare il caso.", "gui.abuseReport.more_comments": "Des<PERSON>rivi quello che è successo:", "gui.abuseReport.name.comment_box_label": "Descrivi perché vuoi segnalare questo nome:", "gui.abuseReport.name.reporting": "Stai segnalando «%s».", "gui.abuseReport.name.title": "Segnala el nom del sgiugador", "gui.abuseReport.observed_what": "Perchè te seet 'dree a segnalà quella roba chì?", "gui.abuseReport.read_info": "Descov<PERSON>s pussee in sui segnalazzion", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON><PERSON><PERSON> o alcol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Vergun l'anima i olter a partecipà a atività ilegai ligade a la droga o l'alcolism de minorenn.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Sfrutament o abus sessual de minor", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Un quaivun l’è 'dree a parlà o a promoeuver atività indecent cont bagai.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON>, impersonificazzion o informazzion false", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Vergun l'è 'dree a dagnà la reputazzion de un vergun d'olter cont el fà finta de vesser un vergun d'olter o cont el scompartì informazzion cont el fin de servìss o fà su i alter.", "gui.abuseReport.reason.description": "Descrizzion:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON><PERSON><PERSON><PERSON> falsa", "gui.abuseReport.reason.generic": "Voeuli segnalà quell sgiugador chì", "gui.abuseReport.reason.generic.description": "Questo giocatore m'infastidisce o ha fatto qualcosa che non mi piace.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON> o bullism", "gui.abuseReport.reason.harassment_or_bullying.description": "Un quaivun l’è 'dree atacàt o vesser prepotent con ti o un olter, che l'è anca quand che vun el seguta a contatà ti o un olter senza consens, o publegà i to informazzion privade personai o de olter senza consens (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Descors d’odi", "gui.abuseReport.reason.hate_speech.description": "Un quaivun l’è 'dree a tacà ti o un olter sgiugador per carateristeghe d’identità, compagn de religion, etnia o sessualità.", "gui.abuseReport.reason.imminent_harm": "Pericol iminent - Minacia de fà dagn a olter in la vita real", "gui.abuseReport.reason.imminent_harm.description": "Un quaivun el te dis che el farà dagn a ti o a un olter in la vita vera.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imagin intime senza consens", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON> l'è 'dree a tirà man, a scompartì o promoeuver imagin privade e intime.", "gui.abuseReport.reason.self_harm_or_suicide": "Pericol arent - Autolesionism o suicidi", "gui.abuseReport.reason.self_harm_or_suicide.description": "Vergun el minacia de fàss del mal in la vita vera o el tira man de fàll.", "gui.abuseReport.reason.sexually_inappropriate": "Contenuti sessuali non appropriati", "gui.abuseReport.reason.sexually_inappropriate.description": "Skin esplicite con riferimenti ad atti sessuali, organi sessuali o violenza sessuale.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism o estremism violent", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "On quaivun l’è dree a parlà, promoeuver o menascià de fà azzion terroristeghe o d’estremism violent per rason politeghe, de religion, ideologeghe o olter.", "gui.abuseReport.reason.title": "Cata foeura la categoria de segnalazzion", "gui.abuseReport.report_sent_msg": "Abbiamo ricevuto correttamente la tua segnalazione. Grazie!\n\nIl nostro team la revisionerà il prima possibile.", "gui.abuseReport.select_reason": "Seleziona una categoria per la segnalazione", "gui.abuseReport.send": "<PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "Abbrevia il commento", "gui.abuseReport.send.error_message": "Gh'è stad un eror a mandà la to segnalazzion:\n'%s'", "gui.abuseReport.send.generic_error": "<PERSON><PERSON>'è stad un eror a mandà la to segnalazzion.", "gui.abuseReport.send.http_error": "Al mandà la to segnalazzion, gh'è stad un eror HTTP imprevist.", "gui.abuseReport.send.json_error": "<PERSON>h'è stad un pachet mal-fad a mandà la to segnalazzion.", "gui.abuseReport.send.no_reason": "Cata foeura una categoria de segnalazzion", "gui.abuseReport.send.not_attested": "Si prega di leggere il testo sopra e selezionare la casella di controllo per poter inviare la segnalazione", "gui.abuseReport.send.service_unavailable": "Se po mìa contatà el servizzi de segnalazzion abus. Segures de vesser conetud a l'internet e proeuva an'mò.", "gui.abuseReport.sending.title": "'Dree a mandà la segnalazzion...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mandada", "gui.abuseReport.skin.title": "Segnala l'aspet del sgiugador", "gui.abuseReport.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Messagg in de la ciciarada", "gui.abuseReport.type.name": "Nom del sgiugador", "gui.abuseReport.type.skin": "Aspet del sgiugador", "gui.acknowledge": "<PERSON><PERSON>", "gui.advancements": "Progress", "gui.all": "Tucc", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nDescovriss pussee al ligam chichinscì: %s", "gui.banned.description.permanent": "El to cunt l'è bandid per semper, che voeul dì che te poeudet mìa sgiugà in linea o sgiontàss a Realms.", "gui.banned.description.reason": "Hem ricevud una segnalazzion per mala creanza del to cunt. I nost moderador hann revist il to cas e l'hann identificad 'me %s, che va contra i standard de la comunità del Minecraft.", "gui.banned.description.reason_id": "Codes: %s", "gui.banned.description.reason_id_message": "Codes: %s - %s", "gui.banned.description.temporary": "%s Fin a quel moment, te poeudet mìa sgiugà in linea o sgiontàss a Realms.", "gui.banned.description.temporary.duration": "El to cunt l'è per adess sospendud e el sarà pizzad an'mò tra %s.", "gui.banned.description.unknownreason": "Hem ricevud una segnalazzion per mala creanza del to cunt. I nost moderador hann revist il to cas e l'hann identificad che va contra i standard de la comunità del Minecraft.", "gui.banned.name.description": "El to nom de adess - \"%s\" - el va contra ai Standard de la comunità. Te poeudet sgiugà in modalità Un sgiugador, ma te gh'heet de cambià el to nom per sgiugà in linea.\n\nDescovriss pussee o ciama un'analisi del cas al ligam chichinscì: %s", "gui.banned.name.title": "Nome non consentito in multigiocatore", "gui.banned.reason.defamation_impersonation_false_information": "Sostituzione di persona o diffusione d'informazioni al fine d'ingannare o approfittarsi di altri", "gui.banned.reason.drugs": "Riferimenti a droghe illegali", "gui.banned.reason.extreme_violence_or_gore": "Raffigurazioni di scene reali eccessivamente violente o cruente", "gui.banned.reason.false_reporting": "Numero eccessivo di segnalazioni false o imprecise", "gui.banned.reason.fraud": "Acquisizione o uso fraudolento di contenuti", "gui.banned.reason.generic_violation": "Violazione degli Standard della community", "gui.banned.reason.harassment_or_bullying": "Linguaggio offensivo usato in maniera diretta e dannosa", "gui.banned.reason.hate_speech": "Incitamento all'odio o discriminazione", "gui.banned.reason.hate_terrorism_notorious_figure": "Riferimenti a gruppi d'odio, organizzazioni terroristiche o figure malfamate", "gui.banned.reason.imminent_harm_to_person_or_property": "Intento di causare danno nella vita reale a persone o beni", "gui.banned.reason.nudity_or_pornography": "Condivisione di materiali osceni o pornografici", "gui.banned.reason.sexually_inappropriate": "Argomenti o contenuti di natura sessuale", "gui.banned.reason.spam_or_advertising": "Spam o pubblicità", "gui.banned.skin.description": "El to aspet de adess el va contra ai Standard de la comunità. Te poeudet segutà a sgiugà cont un aspet normal, sedenò te gh'heet de catàn foeura vun noeuv.\n\nDescovriss pussee o ciama un'analisi del cas al ligam chichinscì: %s", "gui.banned.skin.title": "<PERSON><PERSON> minga consentid", "gui.banned.title.permanent": "Cunt bandid per semper", "gui.banned.title.temporary": "Cunt sospendud per adess", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Coment", "gui.chatReport.describe": "Scompartì i detai el ne ajutarà a ciapà una decision ben informada.", "gui.chatReport.discard.content": "Se te veet foeura, te perdareet la segnalazzion e i to coment.\nTe seet segur che te voeulet andà foeura?", "gui.chatReport.discard.discard": "Và foeura e trà via la segnalazzion", "gui.chatReport.discard.draft": "Salva 'me sbozz", "gui.chatReport.discard.return": "Segutà a modifegà", "gui.chatReport.discard.title": "Trà via la segnalazzion e i coment?", "gui.chatReport.draft.content": "Vuoi continuare a modificare la segnalazione in corso o annullarla e crearne una nuova?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Seguta a modifegà", "gui.chatReport.draft.quittotitle.content": "Vuoi continuare a modificarla o annullarla?", "gui.chatReport.draft.quittotitle.title": "Hai una bozza di segnalazione che andrà persa se abbandoni", "gui.chatReport.draft.title": "Modifegà el sbozz de la segnalazzion?", "gui.chatReport.more_comments": "Scrìv sgiò 'se l'è capitad:", "gui.chatReport.observed_what": "Perchè te 'l segnalet?", "gui.chatReport.read_info": "Descovr pussee sora i segnalazzion", "gui.chatReport.report_sent_msg": "La to segnalazzion l'è rivada polida. Grazzia!\n\nEl nost team la revisionarà prest.", "gui.chatReport.select_chat": "Scerniss i messagg in ciciarada de segnalà", "gui.chatReport.select_reason": "Cata foeura una categoria de segnalazzion", "gui.chatReport.selected_chat": "%s messagg in ciciarada scernid per la segnalazzion", "gui.chatReport.send": "<PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON>a el coment, per piasé", "gui.chatReport.send.no_reason": "Cata foeura una categoria de segnalazzion", "gui.chatReport.send.no_reported_messages": "Scerniss almanca un messagg in ciciarada de segnalà", "gui.chatReport.send.too_many_messages": "Trop messagg in la segnalazzion", "gui.chatReport.title": "<PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Se metarann anca i messagg arent a la selezzion per dà pussee contest", "gui.chatSelection.fold": "%s messagg scondud", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s el/la s'è sgiontad/a", "gui.chatSelection.message.narrate": "%s l’ha did: %s ai %s", "gui.chatSelection.selected": "%s/%s messagg scernid", "gui.chatSelection.title": "Scerniss i messagg de la ciciarada de segnalà", "gui.continue": "Continua", "gui.copy_link_to_clipboard": "Copia negli appunti", "gui.days": "%s dì", "gui.done": "Fad", "gui.down": "<PERSON><PERSON><PERSON>", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s file rifiutati", "gui.fileDropFailure.title": "Impossibile aggiungere i file", "gui.hours": "%s ora/e", "gui.loadingMinecraft": "Caricamento di Minecraft", "gui.minutes": "%s minut", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Boton %s", "gui.narrate.editBox": "Casella de test %s: %s", "gui.narrate.slider": "%s corsor", "gui.narrate.tab": "Scheda %s", "gui.no": "No", "gui.none": "Nissun", "gui.ok": "<PERSON>", "gui.open_report_dir": "Apri la cartella dei rapporti", "gui.proceed": "Và inanz", "gui.recipebook.moreRecipes": "Boton drizz per alter", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Cerca...", "gui.recipebook.toggleRecipes.all": "<PERSON>ra tuscoss", "gui.recipebook.toggleRecipes.blastable": "'Dree a mostrà s'cepabil", "gui.recipebook.toggleRecipes.craftable": "'Dree a mostrà realizabil", "gui.recipebook.toggleRecipes.smeltable": "'Dree a mostrà deslenguabil", "gui.recipebook.toggleRecipes.smokable": "'Dree a mostrà fumabil", "gui.report_to_server": "Segnala al server", "gui.socialInteractions.blocking_hint": "Ransgia cont un cunt Microsoft", "gui.socialInteractions.empty_blocked": "Nissun sgiugador blocad in la ciciarada", "gui.socialInteractions.empty_hidden": "Nissun sgiugador scondud in la ciciarada", "gui.socialInteractions.hidden_in_chat": "I messagg de %s sarann scondud", "gui.socialInteractions.hide": "Scònd in ciciarada", "gui.socialInteractions.narration.hide": "Scònd messagg del %s", "gui.socialInteractions.narration.report": "Segnala sgiugador %s", "gui.socialInteractions.narration.show": "Mostra messagg del %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "S'è trovad nissun sgiugador con quell nom chì", "gui.socialInteractions.search_hint": "Cerca...", "gui.socialInteractions.server_label.multiple": "%s - %s sgiugador", "gui.socialInteractions.server_label.single": "%s - %s sgiugador", "gui.socialInteractions.show": "Mostra in ciciarada", "gui.socialInteractions.shown_in_chat": "I messagg de %s sarann mostrad", "gui.socialInteractions.status_blocked": "Blocad", "gui.socialInteractions.status_blocked_offline": "Blocad - Foeura linea", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Scondud - Foeura linea", "gui.socialInteractions.status_offline": "Foeura linea", "gui.socialInteractions.tab_all": "Tucc", "gui.socialInteractions.tab_blocked": "Blocad", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Interazzion sociai", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> messagg", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "El servizzi de segnalazzion l'è mìa disponibil", "gui.socialInteractions.tooltip.report.no_messages": "Nissun messagg de %s de segnalà", "gui.socialInteractions.tooltip.report.not_reportable": "Quell sgiugagor chì el se po mìa segnalà perchè i so messagg in la ciciarada se poden mìa verifegà in quest server chì", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> messagg", "gui.stats": "Statisteghe", "gui.toMenu": "Indree a la lista di server", "gui.toRealms": "Torna alla lista dei Realm", "gui.toTitle": "Indree a la schermada del titol", "gui.toWorld": "Torna alla lista dei mondi", "gui.togglable_slot": "Fai clic per disattivare lo slot", "gui.up": "Su", "gui.waitingForResponse.button.inactive": "Indietro (%s s)", "gui.waitingForResponse.title": "In attesa del server", "gui.yes": "Sì", "hanging_sign.edit": "Messagg de l'insegna", "instrument.minecraft.admire_goat_horn": "Amirazzion", "instrument.minecraft.call_goat_horn": "Ciamada", "instrument.minecraft.dream_goat_horn": "Sogn", "instrument.minecraft.feel_goat_horn": "Sentiment", "instrument.minecraft.ponder_goat_horn": "Riflession", "instrument.minecraft.seek_goat_horn": "Ricerca", "instrument.minecraft.sing_goat_horn": "Cant", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "S'cepa lavorà", "inventory.hotbarInfo": "Salva barra svelta con %1$s + %2$s", "inventory.hotbarSaved": "Barra svelta salvada (retacala con %1$s + %2$s)", "item.canBreak": "El poeu s'cepà:", "item.canPlace": "Se poeu meter sora:", "item.canUse.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.color": "Color: %s", "item.components": "%s componente/i", "item.disabled": "<PERSON>gg<PERSON> disattivato", "item.durability": "Durabilità: %s / %s", "item.dyed": "Tenc", "item.minecraft.acacia_boat": "Barca de rubinia", "item.minecraft.acacia_chest_boat": "Barca de rubinia con cesta", "item.minecraft.allay_spawn_egg": "Oeuv de allay", "item.minecraft.amethyst_shard": "Scheja de ametista", "item.minecraft.angler_pottery_shard": "Toch de terra-cota con cana de pesca", "item.minecraft.angler_pottery_sherd": "Toch de terra-cota con cana de pesca", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Toch de terra-cota con arch", "item.minecraft.archer_pottery_sherd": "Toch de terra-cota con arch", "item.minecraft.armadillo_scute": "Squama di armadillo", "item.minecraft.armadillo_spawn_egg": "Uovo generatore di armadillo", "item.minecraft.armor_stand": "Suport per armadure", "item.minecraft.arms_up_pottery_shard": "Toch de terra-cota con figura", "item.minecraft.arms_up_pottery_sherd": "Toch de terra-cota con figura", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "<PERSON><PERSON> con assolot", "item.minecraft.axolotl_spawn_egg": "Oeuv de axolotl", "item.minecraft.baked_potato": "Pom de terra cot", "item.minecraft.bamboo_chest_raft": "Barchin de bamboo con cesta", "item.minecraft.bamboo_raft": "Barchin de bamboo", "item.minecraft.bat_spawn_egg": "Oeuv de tegnoeula", "item.minecraft.bee_spawn_egg": "Oeuv de ava", "item.minecraft.beef": "<PERSON><PERSON> crud", "item.minecraft.beetroot": "Biedrava", "item.minecraft.beetroot_seeds": "Somenza de biedrave", "item.minecraft.beetroot_soup": "Supa de biedrave", "item.minecraft.birch_boat": "Barca de <PERSON>lla", "item.minecraft.birch_chest_boat": "Barca de biolla con cesta", "item.minecraft.black_bundle": "Sacchetto nero", "item.minecraft.black_dye": "Colorant negher", "item.minecraft.black_harness": "Imbracatura nera", "item.minecraft.blade_pottery_shard": "Toch de terra-cota con spada", "item.minecraft.blade_pottery_sherd": "Toch de terra-cota con spada", "item.minecraft.blaze_powder": "Polver de blaze", "item.minecraft.blaze_rod": "Verga del blaze", "item.minecraft.blaze_spawn_egg": "Oeuv de blaze", "item.minecraft.blue_bundle": "Sacchetto blu", "item.minecraft.blue_dye": "Colorant bloeu", "item.minecraft.blue_egg": "Uovo blu", "item.minecraft.blue_harness": "Imbracatura blu", "item.minecraft.bogged_spawn_egg": "Uovo generatore di paludoso", "item.minecraft.bolt_armor_trim_smithing_template": "Schema di forgiatura", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ornamento stile bullone", "item.minecraft.bone": "Oss", "item.minecraft.bone_meal": "Polver de oss", "item.minecraft.book": "Liber", "item.minecraft.bordure_indented_banner_pattern": "Motivo con bordatura dentata", "item.minecraft.bow": "Arch", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Verga di brezza", "item.minecraft.breeze_spawn_egg": "Uovo generatore di brezza", "item.minecraft.brewer_pottery_shard": "Toch de terra-cota con pozzion", "item.minecraft.brewer_pottery_sherd": "Toch de terra-cota con pozzion", "item.minecraft.brewing_stand": "<PERSON><PERSON>", "item.minecraft.brick": "<PERSON><PERSON>rell", "item.minecraft.brown_bundle": "Sacchetto marrone", "item.minecraft.brown_dye": "Colorant marron", "item.minecraft.brown_egg": "Uovo marrone", "item.minecraft.brown_harness": "Imbracatura marrone", "item.minecraft.brush": "<PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "Sachet", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "<PERSON><PERSON>ò contenere una pila mista di oggetti", "item.minecraft.bundle.full": "Pieno", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Toch de terra-cota con foeugh", "item.minecraft.burn_pottery_sherd": "Toch de terra-cota con foeugh", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON> de camell", "item.minecraft.carrot": "Car<PERSON>la", "item.minecraft.carrot_on_a_stick": "Baston e carotola", "item.minecraft.cat_spawn_egg": "Oeuv de gat", "item.minecraft.cauldron": "Calderon", "item.minecraft.cave_spider_spawn_egg": "Oeuv de ragn di grote", "item.minecraft.chainmail_boots": "Strivai de maja", "item.minecraft.chainmail_chestplate": "Cota de maja", "item.minecraft.chainmail_helmet": "<PERSON> de maja", "item.minecraft.chainmail_leggings": "Gambai de maja", "item.minecraft.charcoal": "Carbonella", "item.minecraft.cherry_boat": "Barca de scireser", "item.minecraft.cherry_chest_boat": "Barca de scireser con cesta", "item.minecraft.chest_minecart": "Carrell de minera con cesta", "item.minecraft.chicken": "Pollaster crud", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.chorus_fruit": "Frut de chorus", "item.minecraft.clay_ball": "Balla de argilla", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbon", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.coast_armor_trim_smithing_template.new": "Ornamento stile costiero", "item.minecraft.cocoa_beans": "Fave de cacao", "item.minecraft.cod": "<PERSON><PERSON><PERSON>z crud", "item.minecraft.cod_bucket": "<PERSON><PERSON> con merluzz", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON>v de merluzz", "item.minecraft.command_block_minecart": "Carrell de minera con bloch di comand", "item.minecraft.compass": "Bussola", "item.minecraft.cooked_beef": "Bisteca", "item.minecraft.cooked_chicken": "Pollaster cot", "item.minecraft.cooked_cod": "Merluzz cot", "item.minecraft.cooked_mutton": "Bar cot", "item.minecraft.cooked_porkchop": "Brassoeula de porcell cota", "item.minecraft.cooked_rabbit": "<PERSON><PERSON> cot", "item.minecraft.cooked_salmon": "Salmon cot", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "Oeuv de vaca", "item.minecraft.creaking_spawn_egg": "Uovo generatore di scricchio", "item.minecraft.creeper_banner_pattern": "Motiv de la bandera", "item.minecraft.creeper_banner_pattern.desc": "<PERSON>aza de creeper", "item.minecraft.creeper_banner_pattern.new": "Motivo con volto di creeper", "item.minecraft.creeper_spawn_egg": "Oeuv de creeper", "item.minecraft.crossbow": "Balestra", "item.minecraft.crossbow.projectile": "Balla:", "item.minecraft.crossbow.projectile.multiple": "Proiettile: %s x %s", "item.minecraft.crossbow.projectile.single": "Proiettile: %s", "item.minecraft.cyan_bundle": "Sacchetto ciano", "item.minecraft.cyan_dye": "Colorant celest", "item.minecraft.cyan_harness": "Imbracatura ciano", "item.minecraft.danger_pottery_shard": "Toch de terra-cota con creeper", "item.minecraft.danger_pottery_sherd": "Toch de terra-cota con creeper", "item.minecraft.dark_oak_boat": "Barca de rogora", "item.minecraft.dark_oak_chest_boat": "Barca de rogora con cesta", "item.minecraft.debug_stick": "Baston de debug", "item.minecraft.debug_stick.empty": "%s el gh'ha mìa proprietà", "item.minecraft.debug_stick.select": "scernid \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" adess l'è %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON>", "item.minecraft.diamond_hoe": "Zapa de diamant", "item.minecraft.diamond_horse_armor": "Armadura de diamant per cavall", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "Picozz de diamant", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Spada de diamant", "item.minecraft.disc_fragment_5": "<PERSON><PERSON> de disch", "item.minecraft.disc_fragment_5.desc": "Disch de musega - 5", "item.minecraft.dolphin_spawn_egg": "Oeuv de delfin", "item.minecraft.donkey_spawn_egg": "<PERSON>eu<PERSON> de asen", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON> de dragon", "item.minecraft.dried_kelp": "Alga seca", "item.minecraft.drowned_spawn_egg": "Oeuv de negad", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.dune_armor_trim_smithing_template.new": "Ornamento stile duna", "item.minecraft.echo_shard": "<PERSON><PERSON><PERSON> resonanta", "item.minecraft.egg": "Oeuv", "item.minecraft.elder_guardian_spawn_egg": "Oeuv de guardian antigh", "item.minecraft.elytra": "Eliter", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Liber instriad", "item.minecraft.enchanted_golden_apple": "Pom d'or instriad", "item.minecraft.end_crystal": "Cristall de l'End", "item.minecraft.ender_dragon_spawn_egg": "Oeuv de l'Enderdragon", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON> de l'ender", "item.minecraft.ender_pearl": "<PERSON><PERSON> l'ender", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.endermite_spawn_egg": "Oeuv de endermite", "item.minecraft.evoker_spawn_egg": "Oeuv de evocador", "item.minecraft.experience_bottle": "Botilia d'esperienza", "item.minecraft.explorer_pottery_shard": "Toch de terra-cota con mapa", "item.minecraft.explorer_pottery_sherd": "Toch de terra-cota con mapa", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.eye_armor_trim_smithing_template.new": "Ornamento stile occhio", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON> de ragn surbuid", "item.minecraft.field_masoned_banner_pattern": "Motivo con seminato di mattoni", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "<PERSON><PERSON> de <PERSON>", "item.minecraft.firework_rocket": "Razz pirotecnegh", "item.minecraft.firework_rocket.flight": "<PERSON>rada del sgol:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "<PERSON>", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Personalizad", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON> in", "item.minecraft.firework_star.flicker": "Lu<PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON>", "item.minecraft.firework_star.green": "Verd", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON> c<PERSON>", "item.minecraft.firework_star.lime": "Lime", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Naranz", "item.minecraft.firework_star.pink": "Roe<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON>", "item.minecraft.firework_star.shape": "Forma descognossuda", "item.minecraft.firework_star.shape.burst": "S'ciop", "item.minecraft.firework_star.shape.creeper": "A forma de creeper", "item.minecraft.firework_star.shape.large_ball": "<PERSON>a granda", "item.minecraft.firework_star.shape.small_ball": "Balla piscinina", "item.minecraft.firework_star.shape.star": "A forma de stella", "item.minecraft.firework_star.trail": "Segn", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Sgiald", "item.minecraft.fishing_rod": "Cana de pescà", "item.minecraft.flint": "Serizz", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "Schema di forgiatura", "item.minecraft.flow_armor_trim_smithing_template.new": "Ornamento stile flusso", "item.minecraft.flow_banner_pattern": "Motivo per stendardo", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "Motivo con flusso", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON><PERSON> con flusso", "item.minecraft.flower_banner_pattern": "Motiv de la bandera", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Motivo con fiore", "item.minecraft.flower_pot": "Vas de fior", "item.minecraft.fox_spawn_egg": "Oeuv de volp", "item.minecraft.friend_pottery_shard": "Toch de terra-cota con amis", "item.minecraft.friend_pottery_sherd": "Toch de terra-cota con amis", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON><PERSON> de rana", "item.minecraft.furnace_minecart": "<PERSON>ell de minera con prestin", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON>t", "item.minecraft.ghast_tear": "Lagrim<PERSON> de g<PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>der", "item.minecraft.glistering_melon_slice": "Feta de inguria lusenta", "item.minecraft.globe_banner_pattern": "Motiv de la bandera", "item.minecraft.globe_banner_pattern.desc": "Pi<PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "Motivo con globo", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.glow_ink_sac": "Saca de incioster sberlusenta", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON> s<PERSON>a", "item.minecraft.glow_squid_spawn_egg": "Oeuv de calamar sber<PERSON>ent", "item.minecraft.glowstone_dust": "Polver de preja lusenta", "item.minecraft.goat_horn": "<PERSON><PERSON> de cavra", "item.minecraft.goat_spawn_egg": "Oeuv de cavra", "item.minecraft.gold_ingot": "<PERSON><PERSON> d'or", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON>'<PERSON>a", "item.minecraft.golden_apple": "Pom d'or", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON> d'or", "item.minecraft.golden_boots": "Strivai d'or", "item.minecraft.golden_carrot": "Carotola d'ora", "item.minecraft.golden_chestplate": "Corazza d'ora", "item.minecraft.golden_helmet": "Elm d'or", "item.minecraft.golden_hoe": "Zapa d'or", "item.minecraft.golden_horse_armor": "Armadura d'ora per cavall", "item.minecraft.golden_leggings": "Gambai d'or", "item.minecraft.golden_pickaxe": "Picozz d'or", "item.minecraft.golden_shovel": "Baira d'ora", "item.minecraft.golden_sword": "Spada d'or", "item.minecraft.gray_bundle": "Sacchetto grigio", "item.minecraft.gray_dye": "Colorant gris", "item.minecraft.gray_harness": "Imbracatura grigia", "item.minecraft.green_bundle": "Sacchetto verde", "item.minecraft.green_dye": "Colorant verd", "item.minecraft.green_harness": "Imbracatura verde", "item.minecraft.guardian_spawn_egg": "Oeuv de guardian", "item.minecraft.gunpowder": "Polver de s'ciop", "item.minecraft.guster_banner_pattern": "Motivo per stendardo", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Motivo con raffica", "item.minecraft.guster_pottery_sherd": "Coccio con raffica", "item.minecraft.happy_ghast_spawn_egg": "Uovo generatore di ghast allegro", "item.minecraft.harness": "Imbracatura", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON> del mar", "item.minecraft.heart_pottery_shard": "Toch de terra-cota con coeur", "item.minecraft.heart_pottery_sherd": "Toch de terra-cota con coeur", "item.minecraft.heartbreak_pottery_shard": "Toch de terra-cota con coeur s'cepad", "item.minecraft.heartbreak_pottery_sherd": "Toch de terra-cota con coeur s'cepad", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON> de ho<PERSON>n", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON>l", "item.minecraft.honeycomb": "<PERSON><PERSON>", "item.minecraft.hopper_minecart": "Carrell de minera con tramoeugia", "item.minecraft.horse_spawn_egg": "Oeuv de cavall", "item.minecraft.host_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.host_armor_trim_smithing_template.new": "Ornamento stile ospite", "item.minecraft.howl_pottery_shard": "Toch de terra-cota con lov", "item.minecraft.howl_pottery_sherd": "Toch de terra-cota con lov", "item.minecraft.husk_spawn_egg": "Oeuv de zombie del desert", "item.minecraft.ink_sac": "Saca de incioster", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON>err", "item.minecraft.iron_boots": "Strivai de ferr", "item.minecraft.iron_chestplate": "<PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Oeuv de golem de ferr", "item.minecraft.iron_helmet": "<PERSON>r", "item.minecraft.iron_hoe": "Zapa de ferr", "item.minecraft.iron_horse_armor": "Armadura de ferr per cavall", "item.minecraft.iron_ingot": "<PERSON><PERSON>err", "item.minecraft.iron_leggings": "Gambai de ferr", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON> de ferr", "item.minecraft.iron_pickaxe": "Picozz de ferr", "item.minecraft.iron_shovel": "Baira de ferr", "item.minecraft.iron_sword": "Spada de ferr", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Barca de la sgiungla", "item.minecraft.jungle_chest_boat": "Barca de la sgiungla con cesta", "item.minecraft.knowledge_book": "Liber de la cognossenza", "item.minecraft.lapis_lazuli": "Lapis<PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON> de lava", "item.minecraft.lead": "Sguinzal", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "Strivai de coram", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Armadura de coram per cavall", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Sacchetto azzurro", "item.minecraft.light_blue_dye": "Colorant bloeu ciar", "item.minecraft.light_blue_harness": "Imbracatura azzurra", "item.minecraft.light_gray_bundle": "Sacchetto grigio chiaro", "item.minecraft.light_gray_dye": "Colorant gris ciar", "item.minecraft.light_gray_harness": "Imbracatura grigio chiaro", "item.minecraft.lime_bundle": "Sacchetto lime", "item.minecraft.lime_dye": "Colorant lime", "item.minecraft.lime_harness": "Imbracatura lime", "item.minecraft.lingering_potion": "Pozzion persistenta", "item.minecraft.lingering_potion.effect.awkward": "Pozzion strana persistenta", "item.minecraft.lingering_potion.effect.empty": "Pozzion persistenta minga fabricabil", "item.minecraft.lingering_potion.effect.fire_resistance": "Pozzion persistenta de resistenza al <PERSON>ugh", "item.minecraft.lingering_potion.effect.harming": "Pozzion persistenta de dagn", "item.minecraft.lingering_potion.effect.healing": "Pozzion persistenta de cura", "item.minecraft.lingering_potion.effect.infested": "Pozione persistente d'infestazione", "item.minecraft.lingering_potion.effect.invisibility": "Pozzion persistenta d'invisibilità", "item.minecraft.lingering_potion.effect.leaping": "Pozzion persistenta de salt", "item.minecraft.lingering_potion.effect.levitation": "Pozzion persistenta de levitazzion", "item.minecraft.lingering_potion.effect.luck": "Pozzion persistenta de fortuna", "item.minecraft.lingering_potion.effect.mundane": "Pozzion normala persistenta", "item.minecraft.lingering_potion.effect.night_vision": "Pozzion persistenta de la vision per la nocc", "item.minecraft.lingering_potion.effect.oozing": "Pozione persistente di trasudazione", "item.minecraft.lingering_potion.effect.poison": "Pozzion persistenta de velen", "item.minecraft.lingering_potion.effect.regeneration": "Pozzion persistenta de regenerazzion", "item.minecraft.lingering_potion.effect.slow_falling": "Pozzion persistenta de borlada lenta", "item.minecraft.lingering_potion.effect.slowness": "Pozzion persistenta <PERSON>", "item.minecraft.lingering_potion.effect.strength": "Pozzion persistenta de forza", "item.minecraft.lingering_potion.effect.swiftness": "Pozzion persistenta <PERSON>", "item.minecraft.lingering_potion.effect.thick": "<PERSON>zzion densa persistenta", "item.minecraft.lingering_potion.effect.turtle_master": "Pozzion persistenta del maester bissa scudellera", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON> d'aqua persistenta", "item.minecraft.lingering_potion.effect.water_breathing": "Pozzion persistenta de respirazzion aquatega", "item.minecraft.lingering_potion.effect.weakness": "Pozzion persistenta de debolezza", "item.minecraft.lingering_potion.effect.weaving": "Pozione persistente di tessitura", "item.minecraft.lingering_potion.effect.wind_charged": "Pozione persistente di carica ventosa", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON> lama", "item.minecraft.lodestone_compass": "Bussola magnetizada", "item.minecraft.mace": "Mazza", "item.minecraft.magenta_bundle": "Sacchetto magenta", "item.minecraft.magenta_dye": "Colorant magenta", "item.minecraft.magenta_harness": "Imbracatura magenta", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Oeuv de cub de magma", "item.minecraft.mangrove_boat": "Barca de mangrovia", "item.minecraft.mangrove_chest_boat": "Barca de mangrovia con cesta", "item.minecraft.map": "Mapa voeuda", "item.minecraft.melon_seeds": "Somenza de inguria", "item.minecraft.melon_slice": "Feta de inguria", "item.minecraft.milk_bucket": "<PERSON><PERSON>", "item.minecraft.minecart": "<PERSON><PERSON> de minera", "item.minecraft.miner_pottery_shard": "Toch de terra-cota con picozz", "item.minecraft.miner_pottery_sherd": "Toch de terra-cota con picozz", "item.minecraft.mojang_banner_pattern": "Motiv de la bandera", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "Motivo con logo", "item.minecraft.mooshroom_spawn_egg": "Oeuv de vaca-fonsg", "item.minecraft.mourner_pottery_shard": "Toch de terra-cota con warden", "item.minecraft.mourner_pottery_sherd": "Toch de terra-cota con warden", "item.minecraft.mule_spawn_egg": "Oeu<PERSON> de mull", "item.minecraft.mushroom_stew": "Stracot de fonsg", "item.minecraft.music_disc_11": "<PERSON><PERSON> musega", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON> musega", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON> musega", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON> musega", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON> musega", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON> musega", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disco musicale", "item.minecraft.music_disc_creator.desc": "<PERSON> — C<PERSON>", "item.minecraft.music_disc_creator_music_box": "Disco musicale", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> <PERSON><PERSON> (carillon)", "item.minecraft.music_disc_far": "<PERSON><PERSON> musega", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disco musicale", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions — Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON> musega", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON> musega", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON> musega", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON> musega", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disco musicale", "item.minecraft.music_disc_precipice.desc": "<PERSON> — Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON> musega", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON> musega", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON> musega", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disco musicale", "item.minecraft.music_disc_tears.desc": "<PERSON> — Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON> musega", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON> musega", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Bar crud", "item.minecraft.name_tag": "Etiche<PERSON>", "item.minecraft.nautilus_shell": "Guss de nautilus", "item.minecraft.nether_brick": "Quadrell del Nether", "item.minecraft.nether_star": "<PERSON>", "item.minecraft.nether_wart": "Brossera del Nether", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON> de netherite", "item.minecraft.netherite_boots": "Strivai de netherite", "item.minecraft.netherite_chestplate": "Corazza de netherite", "item.minecraft.netherite_helmet": "<PERSON> de netherite", "item.minecraft.netherite_hoe": "Zapa de netherite", "item.minecraft.netherite_ingot": "Lingot de netherite", "item.minecraft.netherite_leggings": "Gambai de netherite", "item.minecraft.netherite_pickaxe": "Picozz de netherite", "item.minecraft.netherite_scrap": "To<PERSON><PERSON> de netherite", "item.minecraft.netherite_shovel": "Baira de netherite", "item.minecraft.netherite_sword": "Spada de netherite", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON> del ferrer", "item.minecraft.netherite_upgrade_smithing_template.new": "Miglioramento di netherite", "item.minecraft.oak_boat": "Barca de c<PERSON>r", "item.minecraft.oak_chest_boat": "Barca de rogora con cesta", "item.minecraft.ocelot_spawn_egg": "Oeuv de gatopard", "item.minecraft.ominous_bottle": "Ampolla infausta", "item.minecraft.ominous_trial_key": "Chiave della sfida infausta", "item.minecraft.orange_bundle": "Sacchetto arancione", "item.minecraft.orange_dye": "Colorant naranz", "item.minecraft.orange_harness": "Imbracatura arancione", "item.minecraft.painting": "<PERSON>uader", "item.minecraft.pale_oak_boat": "Barca di quercia pallida", "item.minecraft.pale_oak_chest_boat": "Barca di quercia pallida con baule", "item.minecraft.panda_spawn_egg": "Oeuv de panda", "item.minecraft.paper": "Carta", "item.minecraft.parrot_spawn_egg": "<PERSON>euv de p<PERSON>", "item.minecraft.phantom_membrane": "Membrana de fantasma", "item.minecraft.phantom_spawn_egg": "Oeuv de fantasma", "item.minecraft.pig_spawn_egg": "Oeuv de porscell", "item.minecraft.piglin_banner_pattern": "Motiv de la bandera", "item.minecraft.piglin_banner_pattern.desc": "Grugn", "item.minecraft.piglin_banner_pattern.new": "Motivo con grugno", "item.minecraft.piglin_brute_spawn_egg": "Oeuv de piglin brut", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON><PERSON> de <PERSON>lin", "item.minecraft.pillager_spawn_egg": "Oeuv de lader", "item.minecraft.pink_bundle": "Sacchetto rosa", "item.minecraft.pink_dye": "Colorant roeusa", "item.minecraft.pink_harness": "Imbracatura rosa", "item.minecraft.pitcher_plant": "Pianta carnivora", "item.minecraft.pitcher_pod": "Baccello di pianta carnivora", "item.minecraft.plenty_pottery_shard": "Toch de terra-cota con cesta", "item.minecraft.plenty_pottery_sherd": "Toch de terra-cota con cesta", "item.minecraft.poisonous_potato": "Pom de terra toeussega", "item.minecraft.polar_bear_spawn_egg": "Oeuv de ors polar", "item.minecraft.popped_chorus_fruit": "Frut de chorus s'ciopad", "item.minecraft.porkchop": "Brassoeula de porcell cruda", "item.minecraft.potato": "Pom de terra", "item.minecraft.potion": "Pozzion", "item.minecraft.potion.effect.awkward": "Pozzion strana", "item.minecraft.potion.effect.empty": "Pozzion che se poeu mìa fà su", "item.minecraft.potion.effect.fire_resistance": "Pozzion de resistenza al <PERSON>ugh", "item.minecraft.potion.effect.harming": "<PERSON>zzion de dagn", "item.minecraft.potion.effect.healing": "Pozzion de cura", "item.minecraft.potion.effect.infested": "Pozione d'infestazione", "item.minecraft.potion.effect.invisibility": "Pozzion d'invisibilità", "item.minecraft.potion.effect.leaping": "Pozzion de salt", "item.minecraft.potion.effect.levitation": "Pozzion de levitazzion", "item.minecraft.potion.effect.luck": "Pozzion de fortuna", "item.minecraft.potion.effect.mundane": "Pozzion normala", "item.minecraft.potion.effect.night_vision": "Pozzion de la vision per la nocc", "item.minecraft.potion.effect.oozing": "Pozione di trasudazione", "item.minecraft.potion.effect.poison": "Pozzion de velen", "item.minecraft.potion.effect.regeneration": "Pozzion de regenerazzion", "item.minecraft.potion.effect.slow_falling": "Pozzion de borlada lenta", "item.minecraft.potion.effect.slowness": "Pozzion de lentezza", "item.minecraft.potion.effect.strength": "Pozzion de forza", "item.minecraft.potion.effect.swiftness": "Pozzion de <PERSON>zza", "item.minecraft.potion.effect.thick": "Pozzion densa", "item.minecraft.potion.effect.turtle_master": "Pozzion del maester bissa scudellera", "item.minecraft.potion.effect.water": "Bo<PERSON>ia d'aqua", "item.minecraft.potion.effect.water_breathing": "Pozzion de respirazzion aquatega", "item.minecraft.potion.effect.weakness": "Pozzion de debolezza", "item.minecraft.potion.effect.weaving": "Pozione di tessitura", "item.minecraft.potion.effect.wind_charged": "Pozione di carica ventosa", "item.minecraft.pottery_shard_archer": "Toch de terra-cota con arch", "item.minecraft.pottery_shard_arms_up": "Toch de terra-cota con figura", "item.minecraft.pottery_shard_prize": "Toch de terra-cota con bisgiô", "item.minecraft.pottery_shard_skull": "Toch de terra-cota con coo", "item.minecraft.powder_snow_bucket": "<PERSON><PERSON> de fioca in polver", "item.minecraft.prismarine_crystals": "Cristai de prismarina", "item.minecraft.prismarine_shard": "Scheja de prismarina", "item.minecraft.prize_pottery_shard": "Toch de terra-cota con bisgiô", "item.minecraft.prize_pottery_sherd": "Toch de terra-cota con bisgiô", "item.minecraft.pufferfish": "<PERSON><PERSON>-balla", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON> con pess-balla", "item.minecraft.pufferfish_spawn_egg": "Oeuv de pess-balla", "item.minecraft.pumpkin_pie": "Torta de zu<PERSON>", "item.minecraft.pumpkin_seeds": "Somenza de zuca", "item.minecraft.purple_bundle": "Sacchetto viola", "item.minecraft.purple_dye": "Colorant vioeula", "item.minecraft.purple_harness": "Imbracatura viola", "item.minecraft.quartz": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON> crud", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON> de <PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON> de con<PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ornamento stile allevatore", "item.minecraft.ravager_spawn_egg": "Oeuv de s'cepa-tut", "item.minecraft.raw_copper": "<PERSON> crud", "item.minecraft.raw_gold": "Or crud", "item.minecraft.raw_iron": "<PERSON>rr crud", "item.minecraft.recovery_compass": "Bussola del retrovà", "item.minecraft.red_bundle": "Sacchetto rosso", "item.minecraft.red_dye": "Colorant ross", "item.minecraft.red_harness": "Imbracatura rossa", "item.minecraft.redstone": "Polver de prejarossa", "item.minecraft.resin_brick": "<PERSON><PERSON> di resina", "item.minecraft.resin_clump": "Grumo di resina", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.rib_armor_trim_smithing_template.new": "Ornamento stile costole", "item.minecraft.rotten_flesh": "Carna marscia", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "Salmon crud", "item.minecraft.salmon_bucket": "<PERSON>ll con salmon", "item.minecraft.salmon_spawn_egg": "Oeuv de salmon", "item.minecraft.scrape_pottery_sherd": "Coccio con raschiatura", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ornamento stile sentinella", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ornamento stile modellatore", "item.minecraft.sheaf_pottery_shard": "Toch de terra-cota con fen", "item.minecraft.sheaf_pottery_sherd": "Toch de terra-cota con fen", "item.minecraft.shears": "S'cesora", "item.minecraft.sheep_spawn_egg": "Oeuv de pegora", "item.minecraft.shelter_pottery_shard": "Toch de terra-cota con refusg", "item.minecraft.shelter_pottery_sherd": "Toch de terra-cota con refusg", "item.minecraft.shield": "<PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON> negher", "item.minecraft.shield.blue": "<PERSON><PERSON> blo<PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON> marron", "item.minecraft.shield.cyan": "<PERSON><PERSON> celest", "item.minecraft.shield.gray": "<PERSON><PERSON> gris", "item.minecraft.shield.green": "<PERSON><PERSON> verd", "item.minecraft.shield.light_blue": "<PERSON><PERSON> bloeu ciar", "item.minecraft.shield.light_gray": "<PERSON>ud gris ciar", "item.minecraft.shield.lime": "Scud lime", "item.minecraft.shield.magenta": "Scud magenta", "item.minecraft.shield.orange": "<PERSON><PERSON> naranz", "item.minecraft.shield.pink": "<PERSON><PERSON> roeusa", "item.minecraft.shield.purple": "<PERSON><PERSON> vioeula", "item.minecraft.shield.red": "<PERSON><PERSON> ross", "item.minecraft.shield.white": "<PERSON><PERSON> bianch", "item.minecraft.shield.yellow": "<PERSON><PERSON> s<PERSON>ld", "item.minecraft.shulker_shell": "<PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.silence_armor_trim_smithing_template.new": "Ornamento stile silenzio", "item.minecraft.silverfish_spawn_egg": "Oeuv de pess d'arsgent", "item.minecraft.skeleton_horse_spawn_egg": "Oeu<PERSON> de cavall schelter", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON><PERSON> de schelter", "item.minecraft.skull_banner_pattern": "Motiv de la bandera", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Motivo con teschio", "item.minecraft.skull_pottery_shard": "Toch de terra-cota con coo", "item.minecraft.skull_pottery_sherd": "Toch de terra-cota con coo", "item.minecraft.slime_ball": "Balla de slime", "item.minecraft.slime_spawn_egg": "Oeuv de slime", "item.minecraft.smithing_template": "<PERSON> del ferrer", "item.minecraft.smithing_template.applies_to": "El se aplica a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Sgionta un lingot o un cristall", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Sgionta una part de armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingot e cristall", "item.minecraft.smithing_template.ingredients": "Ingredient:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Sgionta un lingot de netherite", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Fornitù <PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Sgionta un armadura, un'arma o un arnes de diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingot de netherite", "item.minecraft.smithing_template.upgrade": "<PERSON><PERSON><PERSON>: ", "item.minecraft.sniffer_spawn_egg": "Oeuv de nasador", "item.minecraft.snort_pottery_shard": "Toch de terra-cota con nasador", "item.minecraft.snort_pottery_sherd": "Toch de terra-cota con nasador", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.snout_armor_trim_smithing_template.new": "Ornamento stile grugno", "item.minecraft.snow_golem_spawn_egg": "Oeuv de golem de nev", "item.minecraft.snowball": "Balla de fioca", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.spider_eye": "<PERSON><PERSON><PERSON> de <PERSON>n", "item.minecraft.spider_spawn_egg": "<PERSON>eu<PERSON> de ragn", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.spire_armor_trim_smithing_template.new": "Ornamento stile guglie", "item.minecraft.splash_potion": "Pozzion de tirà", "item.minecraft.splash_potion.effect.awkward": "Pozzion strana de tirà", "item.minecraft.splash_potion.effect.empty": "Pozzion de tirà che se poeu mìa fà su", "item.minecraft.splash_potion.effect.fire_resistance": "Pozzion de tirà de resistenza al foeugh", "item.minecraft.splash_potion.effect.harming": "Pozzion de tirà de dagn", "item.minecraft.splash_potion.effect.healing": "Pozzion de tirà de cura", "item.minecraft.splash_potion.effect.infested": "Pozione da lancio d'infestazione", "item.minecraft.splash_potion.effect.invisibility": "Pozzion de tirà d'invisibilità", "item.minecraft.splash_potion.effect.leaping": "Pozzion de tirà de salt", "item.minecraft.splash_potion.effect.levitation": "Pozzion de tirà de levitazzion", "item.minecraft.splash_potion.effect.luck": "Pozzion de tirà de fortuna", "item.minecraft.splash_potion.effect.mundane": "Pozzion normala de tirà", "item.minecraft.splash_potion.effect.night_vision": "Pozzion de tirà de vision per la nocc", "item.minecraft.splash_potion.effect.oozing": "Pozione da lancio di trasudazione", "item.minecraft.splash_potion.effect.poison": "Pozzion de tirà de velen", "item.minecraft.splash_potion.effect.regeneration": "Pozzion de tirà de regenerazzion", "item.minecraft.splash_potion.effect.slow_falling": "Pozzion de tirà de borlada lenta", "item.minecraft.splash_potion.effect.slowness": "Pozzion de tirà de lentezza", "item.minecraft.splash_potion.effect.strength": "Pozzion de tirà de forza", "item.minecraft.splash_potion.effect.swiftness": "Pozzion de tirà de sveltezza", "item.minecraft.splash_potion.effect.thick": "Pozzion densa de tirà", "item.minecraft.splash_potion.effect.turtle_master": "Pozzion de tirà del maester bissa scudellera", "item.minecraft.splash_potion.effect.water": "Botilia d'aqua de tirà", "item.minecraft.splash_potion.effect.water_breathing": "Pozzion de tirà de respirazzion aquatega", "item.minecraft.splash_potion.effect.weakness": "Pozzion de tirà de debolezza", "item.minecraft.splash_potion.effect.weaving": "Pozione da lancio di tessitura", "item.minecraft.splash_potion.effect.wind_charged": "Pozione da lancio di carica ventosa", "item.minecraft.spruce_boat": "Barca de abiezz", "item.minecraft.spruce_chest_boat": "Barca de abiezz con cesta", "item.minecraft.spyglass": "Canoccial", "item.minecraft.squid_spawn_egg": "Oeuv de calamar", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "Zapa de preja", "item.minecraft.stone_pickaxe": "Picozz de preja", "item.minecraft.stone_shovel": "Baira de preja", "item.minecraft.stone_sword": "Spada de preja", "item.minecraft.stray_spawn_egg": "Oeuv de schelter di nev", "item.minecraft.strider_spawn_egg": "Oeu<PERSON> de strider", "item.minecraft.string": "Fil", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON>pa sospeta", "item.minecraft.sweet_berries": "<PERSON><PERSON> do<PERSON>z", "item.minecraft.tadpole_bucket": "<PERSON><PERSON> con botarana", "item.minecraft.tadpole_spawn_egg": "Oeuv de botarana", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.tide_armor_trim_smithing_template.new": "Ornamento stile marea", "item.minecraft.tipped_arrow": "Flizza guzza", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON> imbi<PERSON>", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON><PERSON> imbibida che se poeu mìa fà su", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON><PERSON> <PERSON> resist<PERSON> al <PERSON>", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON>zza de cura", "item.minecraft.tipped_arrow.effect.infested": "Freccia d'infestazione", "item.minecraft.tipped_arrow.effect.invisibility": "Flizza de invisibilità", "item.minecraft.tipped_arrow.effect.leaping": "Flizza de salt", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.luck": "Flizza de fortuna", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON> imbi<PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "Flizza de vision per la nocc", "item.minecraft.tipped_arrow.effect.oozing": "Freccia di trasudazione", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> de <PERSON>len", "item.minecraft.tipped_arrow.effect.regeneration": "Flizza de regenerazzion", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON><PERSON> de borlada lenta", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON> imbi<PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "Flizza del maester bissa scudellera", "item.minecraft.tipped_arrow.effect.water": "Flizza d'aqua", "item.minecraft.tipped_arrow.effect.water_breathing": "Flizza de respirazzion aquatega", "item.minecraft.tipped_arrow.effect.weakness": "Flizza de debolezza", "item.minecraft.tipped_arrow.effect.weaving": "Freccia di tessitura", "item.minecraft.tipped_arrow.effect.wind_charged": "Freccia di carica ventosa", "item.minecraft.tnt_minecart": "<PERSON>ell de minera con dimanit", "item.minecraft.torchflower_seeds": "Somenza de torcia-fior", "item.minecraft.totem_of_undying": "Totem de l'imortalità", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON> de lama ram<PERSON>", "item.minecraft.trial_key": "<PERSON>ave della sfida", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "Pess tropegal", "item.minecraft.tropical_fish_bucket": "<PERSON><PERSON> de pess tropegal", "item.minecraft.tropical_fish_spawn_egg": "Oeuv de pess tropegal", "item.minecraft.turtle_helmet": "Guss de bissa scudellera", "item.minecraft.turtle_scute": "Scuto di tartaruga", "item.minecraft.turtle_spawn_egg": "Oeuv de bissa scudellera", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.vex_armor_trim_smithing_template.new": "Ornamento stile vessante", "item.minecraft.vex_spawn_egg": "Oeuv de spirit", "item.minecraft.villager_spawn_egg": "Oeuv de paisan", "item.minecraft.vindicator_spawn_egg": "Oeuv de vendegador", "item.minecraft.wandering_trader_spawn_egg": "Oeuv de mercant ramingh", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.ward_armor_trim_smithing_template.new": "Ornamento stile sorvegliante", "item.minecraft.warden_spawn_egg": "Oeu<PERSON> de warden", "item.minecraft.warped_fungus_on_a_stick": "Baston con fonsg desformad", "item.minecraft.water_bucket": "<PERSON><PERSON>'<PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ornamento stile ricognitore", "item.minecraft.wheat": "Forment", "item.minecraft.wheat_seeds": "Somenza de forment", "item.minecraft.white_bundle": "Sacchetto bianco", "item.minecraft.white_dye": "Colorant bianch", "item.minecraft.white_harness": "Imbracatura bianca", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON> del ferrer", "item.minecraft.wild_armor_trim_smithing_template.new": "Ornamento stile selvaggio", "item.minecraft.wind_charge": "Carica di vento", "item.minecraft.witch_spawn_egg": "Oeuv de stria", "item.minecraft.wither_skeleton_spawn_egg": "Oeu<PERSON> de schelter wither", "item.minecraft.wither_spawn_egg": "Oeuv de wither", "item.minecraft.wolf_armor": "Armatura per lupi", "item.minecraft.wolf_spawn_egg": "Oeuv de lov", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "Zapa de legn", "item.minecraft.wooden_pickaxe": "Picozz de legn", "item.minecraft.wooden_shovel": "<PERSON>ra de <PERSON>n", "item.minecraft.wooden_sword": "Spada de legn", "item.minecraft.writable_book": "Liber e pena", "item.minecraft.written_book": "Liber scrit", "item.minecraft.yellow_bundle": "Sacchetto giallo", "item.minecraft.yellow_dye": "Colorant sgiald", "item.minecraft.yellow_harness": "Imbracatura gialla", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON> z<PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "Oeuv de cavall zombie", "item.minecraft.zombie_spawn_egg": "Oeuv de zombie", "item.minecraft.zombie_villager_spawn_egg": "Oeuv de paisan zombie", "item.minecraft.zombified_piglin_spawn_egg": "Oeuv de piglin zombifegad", "item.modifiers.any": "Sul corpo:", "item.modifiers.armor": "Addosso:", "item.modifiers.body": "Sul corpo:", "item.modifiers.chest": "In sul corp:", "item.modifiers.feet": "In sui pè:", "item.modifiers.hand": "In mano:", "item.modifiers.head": "In sul coo:", "item.modifiers.legs": "In sui gambe:", "item.modifiers.mainhand": "In la man principala:", "item.modifiers.offhand": "In la man segondaria:", "item.modifiers.saddle": "Con sella:", "item.nbt_tags": "NBT: %s tag", "item.op_block_warning.line1": "Attenzione!", "item.op_block_warning.line2": "<PERSON>'oggetto potrebbe eseguire comandi,", "item.op_block_warning.line3": "utilizzalo soltanto se sai a che cosa serve!", "item.unbreakable": "Ins'cepabil", "itemGroup.buildingBlocks": "<PERSON><PERSON>", "itemGroup.coloredBlocks": "Bloch colorad", "itemGroup.combat": "Combatiment", "itemGroup.consumables": "Con<PERSON><PERSON><PERSON>", "itemGroup.crafting": "Fab<PERSON>gazzi<PERSON>", "itemGroup.foodAndDrink": "Magnà e bebide", "itemGroup.functional": "Bloch fonzionai", "itemGroup.hotbar": "Barre svelte salvade", "itemGroup.ingredients": "Ingredient", "itemGroup.inventory": "Inventari de soravivenza", "itemGroup.natural": "Bloch naturai", "itemGroup.op": "Utilità per operatori", "itemGroup.redstone": "<PERSON><PERSON>", "itemGroup.search": "Cerca", "itemGroup.spawnEggs": "Oeuv sgenerador", "itemGroup.tools": "Arnea e utilità", "item_modifier.unknown": "Modifegador de oget minga cognossud: %s", "jigsaw_block.final_state": "<PERSON>à vegnì:", "jigsaw_block.generate": "Fà su", "jigsaw_block.joint.aligned": "In linea", "jigsaw_block.joint.rollable": "Pirlabil", "jigsaw_block.joint_label": "Tipo de sgiontament:", "jigsaw_block.keep_jigsaws": "Tègn rompa-coo", "jigsaw_block.levels": "Nivei: %s", "jigsaw_block.name": "Nom:", "jigsaw_block.placement_priority": "Priorità di piazzamento:", "jigsaw_block.placement_priority.tooltip": "Quando questo blocco puzzle si connette a un pezzo, questo sarà l'ordine in cui il pezzo viene elaborato per le connessioni alla struttura complessiva.\n\nI pezzi verranno elaborati in ordine di priorità decrescente, seguendo l'ordine d'inserimento in caso di parità.", "jigsaw_block.pool": "Grup obietiv:", "jigsaw_block.selection_priority": "Priorità di selezione:", "jigsaw_block.selection_priority.tooltip": "Quando il pezzo associato viene elaborato per le connessioni, questo sarà l'ordine in cui questo blocco puzzle tenterà di connettersi al suo pezzo destinazione.\n\nI pezzi verranno elaborati in ordine di priorità decrescente, casualmente in caso di parità.", "jigsaw_block.target": "Nom obietiv:", "jukebox_song.minecraft.11": "C418 — 11", "jukebox_song.minecraft.13": "C418 — 13", "jukebox_song.minecraft.5": "<PERSON> — 5", "jukebox_song.minecraft.blocks": "C418 — blocks", "jukebox_song.minecraft.cat": "C418 — cat", "jukebox_song.minecraft.chirp": "C418 — chirp", "jukebox_song.minecraft.creator": "<PERSON> — C<PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> <PERSON><PERSON> (carillon)", "jukebox_song.minecraft.far": "C418 — far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions — Lava Chicken", "jukebox_song.minecraft.mall": "C418 — mall", "jukebox_song.minecraft.mellohi": "C418 — mellohi", "jukebox_song.minecraft.otherside": "<PERSON> — otherside", "jukebox_song.minecraft.pigstep": "<PERSON> Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> — Precipice", "jukebox_song.minecraft.relic": "<PERSON> <PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 — stal", "jukebox_song.minecraft.strad": "C418 — strad", "jukebox_song.minecraft.tears": "<PERSON> — Tears", "jukebox_song.minecraft.wait": "C418 — wait", "jukebox_song.minecraft.ward": "C418 — ward", "key.advancements": "Progress", "key.attack": "Ataca/S'cepa", "key.back": "<PERSON><PERSON> indree", "key.categories.creative": "Modalità crea", "key.categories.gameplay": "<PERSON><PERSON><PERSON> s<PERSON>", "key.categories.inventory": "Inventari", "key.categories.misc": "<PERSON><PERSON>", "key.categories.movement": "Moviment", "key.categories.multiplayer": "Multisgiugador", "key.categories.ui": "Interfacia de sgioeugh", "key.chat": "<PERSON><PERSON> ciciarada", "key.command": "Derva i comand", "key.drop": "Lassa el lavorà catad foeura", "key.forward": "Camina inanz", "key.fullscreen": "Scher<PERSON> intregh", "key.hotbar.1": "Barra svelta 1", "key.hotbar.2": "Barra svelta 2", "key.hotbar.3": "Barra svelta 3", "key.hotbar.4": "Barra svelta 4", "key.hotbar.5": "Barra svelta 5", "key.hotbar.6": "Barra svelta 6", "key.hotbar.7": "Barra svelta 7", "key.hotbar.8": "Barra svelta 8", "key.hotbar.9": "Barra svelta 9", "key.inventory": "Derva/Sarra su l'inventari", "key.jump": "Salta", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "<PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Canc", "key.keyboard.down": "<PERSON><PERSON><PERSON>", "key.keyboard.end": "Fin", "key.keyboard.enter": "Tast manda", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Enter (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "<PERSON>lica manzina", "key.keyboard.left.alt": "<PERSON>", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "C<PERSON>l manzin", "key.keyboard.left.shift": "Shift manzin", "key.keyboard.left.win": "<PERSON> manzin", "key.keyboard.menu": "<PERSON>ù", "key.keyboard.minus": "-", "key.keyboard.num.lock": "<PERSON>h num", "key.keyboard.page.down": "<PERSON><PERSON> s<PERSON>", "key.keyboard.page.up": "Pag su", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "Stamp", "key.keyboard.right": "<PERSON><PERSON><PERSON> drizza", "key.keyboard.right.alt": "Alt drizz", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl drizz", "key.keyboard.right.shift": "Shift drizz", "key.keyboard.right.win": "Win drizz", "key.keyboard.scroll.lock": "<PERSON><PERSON> scarl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Mìa ligad", "key.keyboard.up": "Flicia su", "key.keyboard.world.1": "Mond 1", "key.keyboard.world.2": "Mond 2", "key.left": "<PERSON><PERSON> a manzina", "key.loadToolbarActivator": "Carga una barra svelta", "key.mouse": "Boton %1$s", "key.mouse.left": "<PERSON><PERSON> manzin", "key.mouse.middle": "Boton central", "key.mouse.right": "Boton drizz", "key.pickItem": "<PERSON>eu un bloch", "key.playerlist": "Lista sgiugador", "key.quickActions": "Azioni rapide", "key.right": "Camina a drizza", "key.saveToolbarActivator": "<PERSON>va barra svelta", "key.screenshot": "Ciapa una schermada", "key.smoothCamera": "Vision cinematega", "key.sneak": "Strusa via", "key.socialInteractions": "Interazzion sociai", "key.spectatorOutlines": "<PERSON><PERSON> i s<PERSON>dor (spetador)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Scambia de man i lavorà", "key.togglePerspective": "Cambia prospetiva", "key.use": "Do<PERSON>ra un lavorà/Mèt un bloch", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Linee guida della community", "known_server_link.feedback": "Riscontro", "known_server_link.forums": "Forum", "known_server_link.news": "Notizie", "known_server_link.report_bug": "Segnala un bug del server", "known_server_link.status": "Stato", "known_server_link.support": "Supporto", "known_server_link.website": "<PERSON><PERSON>", "lanServer.otherPlayers": "Configurazzion per i olter s<PERSON>ugador", "lanServer.port": "Numer porta", "lanServer.port.invalid": "Porta mìa valida.\n<PERSON><PERSON> el quader voeui o mèt un numer divers in tra 1024 e 65535.", "lanServer.port.invalid.new": "Porta non valida.\nLascia il riquadro vuoto o inserisci un numero tra %s e %s.", "lanServer.port.unavailable": "Porta mìa disponibil.\nLassa el quader voeui o mèt un numer divers in tra 1024 e 65535.", "lanServer.port.unavailable.new": "Porta non disponibile.\nLascia il riquadro vuoto o inserisci un numero diverso compreso tra %s e %s.", "lanServer.scanning": "'Dree a cercà sgioeugh in la to red locala", "lanServer.start": "Taca un mond in LAN", "lanServer.title": "Mond in LAN", "language.code": "lmo_IT", "language.name": "Lombard", "language.region": "Lombardia", "lectern.take_book": "Toeu el liber", "loading.progress": "%s%%", "mco.account.privacy.info": "Lènsg pussee sora el Mojang e i legg de confidenzialità", "mco.account.privacy.info.button": "Scopri di più sull'RGPD", "mco.account.privacy.information": "Mojang applica alcune misure per assicurare la protezione dei bambini e della loro riservatezza, in conformità del Children's Online Privacy Protection Act (COPPA) e del regolamento generale sulla protezione dei dati (RGPD).\n\nPotrebbe essere necessario il consenso dei genitori per accedere al tuo account su Realms.", "mco.account.privacyinfo": "La Mojang la dovra di mesure per aiutà a protegger i bagai e la so riservatezza segond la Children’s Online Privacy Protection Act (COPPA) e la General Data Protection Regulation (GDPR).\n\nSe podaria havégh besogn che i sgent confermen prima de fàss un cunt de Realms.\n\nSe te gh'heet un cunt vegg del Minecraft (te vegnet dent con el to nom del sgiugador), te gh'heet besogn de spostà el to cunt in d'un cunt de la Mojang per vegnì dent.", "mco.account.update": "<PERSON><PERSON><PERSON> cunt", "mco.activity.noactivity": "Nessuna attività da %s giorno/i", "mco.activity.title": "Atività di sgiugador", "mco.backup.button.download": "Descarega l'ultima version", "mco.backup.button.reset": "Retaca mond", "mco.backup.button.restore": "Reimposta", "mco.backup.button.upload": "Carega mond", "mco.backup.changes.tooltip": "Cambi", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Descrizione", "mco.backup.entry.enabledPack": "Pacchetto attivo", "mco.backup.entry.gameDifficulty": "Difficoltà di gioco", "mco.backup.entry.gameMode": "Modalità de sgioeugh", "mco.backup.entry.gameServerVersion": "Version del server", "mco.backup.entry.name": "Nom", "mco.backup.entry.seed": "Somenza", "mco.backup.entry.templateName": "Nome del modello", "mco.backup.entry.undefined": "Modifica non definita", "mco.backup.entry.uploaded": "Caregad", "mco.backup.entry.worldType": "Tipo de mond", "mco.backup.generate.world": "Fà su el mond", "mco.backup.info.title": "Modifiche dall'ultimo backup", "mco.backup.narration": "Backup del %s", "mco.backup.nobackups": "Quell realm el gh'ha nissun salvament de reserva.", "mco.backup.restoring": "'Dree a retacà el to realm", "mco.backup.unknown": "DESCOGNOSSUD", "mco.brokenworld.download": "Descarega", "mco.brokenworld.downloaded": "Descaregad", "mco.brokenworld.message.line1": "Retaca o scerniss un olter mond.", "mco.brokenworld.message.line2": "Te poeudet anca descaregà el mond in la modalità \"un sgiugador\"", "mco.brokenworld.minigame.title": "Quell minisgioeugh l'è pu suportad", "mco.brokenworld.nonowner.error": "Speta che el proprietari del real el retaca el mond", "mco.brokenworld.nonowner.title": "El mond l'è mìa atualizad", "mco.brokenworld.play": "Sgiuga", "mco.brokenworld.reset": "Retaca", "mco.brokenworld.title": "El to mond de adess l'è pu suportad", "mco.client.incompatible.msg.line1": "El to client l'è mìa compatibil con i Realms.", "mco.client.incompatible.msg.line2": "Dovra la version pussee noeuva del Minecraft, per piasé.", "mco.client.incompatible.msg.line3": "I Realms inn mìa compatibil con i version de proeuve (snapshots).", "mco.client.incompatible.title": "Client mìa compatibil!", "mco.client.outdated.stable.version": "La versione del client (%s) non è compatibile con Realms.\n\nUsa la versione più recente di Minecraft.", "mco.client.unsupported.snapshot.version": "La versione del client (%s) non è compatibile con Realms.\n\nRealms non è disponibile per questa versione sperimentale.", "mco.compatibility.downgrade": "Carica in una versione precedente", "mco.compatibility.downgrade.description": "L'ultima volta questo mondo è stato caricato nella versione %s, mentre tu stai usando la versione %s. <PERSON><PERSON>re il mondo in una versione precedente potrebbe danneggiarlo: non possiamo garantire il suo corretto caricamento o funzionamento.\n\nUna copia del tuo mondo verrà salvata in «Backup». Usala per il ripristino se necessario.", "mco.compatibility.incompatible.popup.title": "Versione incompatibile", "mco.compatibility.incompatible.releaseType.popup.message": "Il mondo a cui stai cercando di unirti non è compatibile con la versione in uso.", "mco.compatibility.incompatible.series.popup.message": "L'ultima volta questo mondo è stato caricato nella versione %s, mentre tu stai usando la versione %s.\n\nLe due versioni non sono compatibili tra di loro. Per giocare in questa versione, è necessario un nuovo mondo.", "mco.compatibility.unverifiable.message": "Impossibile verificare la versione in cui il mondo è stato caricato l'ultima volta. Se sarà caricato in una versione del gioco successiva o precedente, ne verrà creata e salvata automaticamente una copia in «Backup».", "mco.compatibility.unverifiable.title": "Compatibilità non verificabile", "mco.compatibility.upgrade": "Aggiorna", "mco.compatibility.upgrade.description": "L'ultima volta questo mondo è stato caricato nella versione %s, mentre tu stai usando la versione %s. \n\nUna copia del tuo mondo verrà salvata in «Backup». Usala per il ripristino se necessario.", "mco.compatibility.upgrade.friend.description": "L'ultima volta questo mondo è stato caricato nella versione %s, mentre tu stai usando la versione %s. \n\nUna copia del tuo mondo verrà salvata in «Backup».\n\nIl proprietario del Realm potrà ripristinare il mondo se necessario.", "mco.compatibility.upgrade.title": "Vuoi davvero aggiornare il tuo mondo?", "mco.configure.current.minigame": "De adess", "mco.configure.world.activityfeed.disabled": "L'atività di sgiugador l'è smorzad per adess", "mco.configure.world.backup": "Salvament de reserva mond", "mco.configure.world.buttons.activity": "Atività di sgiugador", "mco.configure.world.buttons.close": "Serra realm", "mco.configure.world.buttons.delete": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "Fad", "mco.configure.world.buttons.edit": "Configurazzion", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "Pussee opzion", "mco.configure.world.buttons.newworld": "Nuovo mondo", "mco.configure.world.buttons.open": "Derva realm", "mco.configure.world.buttons.options": "Opzion del mond", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Seleziona la regione...", "mco.configure.world.buttons.resetworld": "Retaca el mond", "mco.configure.world.buttons.save": "<PERSON><PERSON>", "mco.configure.world.buttons.settings": "Configurazzion", "mco.configure.world.buttons.subscription": "Iscrizzion", "mco.configure.world.buttons.switchminigame": "Scambia el minisgioeugh", "mco.configure.world.close.question.line1": "El to real se podarà pu dovrà.", "mco.configure.world.close.question.line2": "Seet segur de andà inanz?", "mco.configure.world.close.question.title": "Hai bisogno di apportare modifiche senza interruzioni?", "mco.configure.world.closing": "'Dree a serrà el realm...", "mco.configure.world.commandBlocks": "<PERSON><PERSON>nd", "mco.configure.world.delete.button": "Scassa via realm", "mco.configure.world.delete.question.line1": "El to realm el sarà scancellad per semper", "mco.configure.world.delete.question.line2": "Seet segur de andà inanz?", "mco.configure.world.description": "Descrizzion del realm", "mco.configure.world.edit.slot.name": "Nom del mond", "mco.configure.world.edit.subscreen.adventuremap": "Un quai configurazzion l'è smorzad perchè el to mond de adess l'è un'aventura", "mco.configure.world.edit.subscreen.experience": "Un quai configurazzion l'è smorzad perchè el to mond de adess l'è un'esperienza", "mco.configure.world.edit.subscreen.inspiration": "Un quai configurazzion l'è smorzad perchè el to mond de adess l'è un'ispirazzion", "mco.configure.world.forceGameMode": "Forza la modalità de sgioeugh", "mco.configure.world.invite.narration": "Hai %s nuovo/i invito/i", "mco.configure.world.invite.profile.name": "Nom", "mco.configure.world.invited": "Invitad", "mco.configure.world.invited.number": "Invitati (%s)", "mco.configure.world.invites.normal.tooltip": "Dovrador normal", "mco.configure.world.invites.ops.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "Cava via", "mco.configure.world.leave.question.line1": "Se te lasset quell real chì te 'l vedareet pu foeura che se te invitarann an'mò", "mco.configure.world.leave.question.line2": "Seet segur de andà inanz?", "mco.configure.world.loading": "Caricamento del Realm", "mco.configure.world.location": "Sit", "mco.configure.world.minigame": "Attuale: %s", "mco.configure.world.name": "Nom del realm", "mco.configure.world.opening": "'Dree a dervì el real...", "mco.configure.world.players.error": "On sgiugador con quell nom lì el gh'è no", "mco.configure.world.players.inviting": "Invio dell'invito...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PvsP", "mco.configure.world.region_preference": "Regione preferita", "mco.configure.world.region_preference.title": "Selezione della preferenza regionale", "mco.configure.world.reset.question.line1": "El to mond el sarà fad su ancamò e quell chì de adess el se perdarà", "mco.configure.world.reset.question.line2": "Seet segur de andà inanz?", "mco.configure.world.resourcepack.question": "Questo Realm richiede un pacchetto di risorse personalizzato.\n\nVuoi scaricarlo per giocare?", "mco.configure.world.resourcepack.question.line1": "Te gh'heet besogn de un pachet de resorse personalizad per sgiugà", "mco.configure.world.resourcepack.question.line2": "Voeuret descaregàll e sgiugàll?", "mco.configure.world.restore.download.question.line1": "El mond el sarà descaregad e sgiontad ai mond \"un sgiugador\".", "mco.configure.world.restore.download.question.line2": "V<PERSON><PERSON>t andà inanz?", "mco.configure.world.restore.question.line1": "El to mond el sarà reimpostad a la data '%s' (%s)", "mco.configure.world.restore.question.line2": "Seet segur de andà inanz?", "mco.configure.world.settings.expired": "Non puoi modificare le impostazioni di un Realm scaduto", "mco.configure.world.settings.title": "Configurazzion", "mco.configure.world.slot": "Mond %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "El to realm el sarà scambiad con un olter mond", "mco.configure.world.slot.switch.question.line2": "Seet segur de andà inanz?", "mco.configure.world.slot.tooltip": "Passa al mond", "mco.configure.world.slot.tooltip.active": "Sgiontes", "mco.configure.world.slot.tooltip.minigame": "Passa al minisgioeugh", "mco.configure.world.spawnAnimals": "Fà su di bestie", "mco.configure.world.spawnMonsters": "Fà su di moster", "mco.configure.world.spawnNPCs": "Fà su di paesan", "mco.configure.world.spawnProtection": "Protezzion pont de inizzi", "mco.configure.world.spawn_toggle.message": "Smorzà questa opzion chì el RANZARÀ TUCC i entità de quell tipo lì", "mco.configure.world.spawn_toggle.message.npc": "Smorzà questa opzion chì el RANZARÀ TUCC i entità de quell tipo lì, compagn di paesan", "mco.configure.world.spawn_toggle.title": "<PERSON><PERSON>!", "mco.configure.world.status": "Stat", "mco.configure.world.subscription.day": "dì", "mco.configure.world.subscription.days": "dì", "mco.configure.world.subscription.expired": "<PERSON><PERSON>", "mco.configure.world.subscription.extend": "Slarga l'iscrizzion", "mco.configure.world.subscription.less_than_a_day": "Manch che un dì", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "mes", "mco.configure.world.subscription.recurring.daysleft": "Renovad inde<PERSON>ù tra", "mco.configure.world.subscription.recurring.info": "Le modifiche effettuate al tuo abbonamento a Realms, come l'estensione del termine o la disattivazione della fatturazione ricorrente, non verranno rese effettive fino alla prossima data di fatturazione.", "mco.configure.world.subscription.remaining.days": "%1$s giorno/i", "mco.configure.world.subscription.remaining.months": "%1$s mese/i", "mco.configure.world.subscription.remaining.months.days": "%1$s mese/i, %2$s giorno/i", "mco.configure.world.subscription.start": "Data de inizzi", "mco.configure.world.subscription.tab": "Abbonamento", "mco.configure.world.subscription.timeleft": "Temp che resta", "mco.configure.world.subscription.title": "La to iscrizzion", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "Fà su el mond", "mco.configure.world.switch.slot.subtitle": "El mond l'è voeui, scerniss 'me fà su el to mond", "mco.configure.world.title": "Configura realm:", "mco.configure.world.uninvite.player": "Vuoi davvero annullare l'invito di «%s»?", "mco.configure.world.uninvite.question": "Seet segur de trà a mont l'invit", "mco.configure.worlds.title": "Mond", "mco.connect.authorizing": "'Dree a andà dent...", "mco.connect.connecting": "'Dree a conetess al realm...", "mco.connect.failed": "Conession al realm fallida", "mco.connect.region": "Regione del server: %s", "mco.connect.success": "Fad", "mco.create.world": "Fà su", "mco.create.world.error": "Te gh'heet de scriver un nom!", "mco.create.world.failed": "Impossibile creare il mondo!", "mco.create.world.reset.title": "'Dree a fà su el mond...", "mco.create.world.skip": "Salta", "mco.create.world.subtitle": "Se te voeulet, cata foeura el mond de meter in del to noeuv realm", "mco.create.world.wait": "'Dree a fà su el real...", "mco.download.cancelled": "Descaregament anullad", "mco.download.confirmation.line1": "El mond che te descaregareet l'è pussee grand che %s", "mco.download.confirmation.line2": "Te podareet pu caregà quell mond chì in del to realm", "mco.download.confirmation.oversized": "Il mondo che stai per scaricare è più grande di %s\n\nNon potrai più caricare questo mondo nel tuo Realm", "mco.download.done": "Descaregament finid", "mco.download.downloading": "'<PERSON><PERSON> a <PERSON>", "mco.download.extracting": "'Dree a tirà foeura", "mco.download.failed": "Descaregament fallid", "mco.download.percent": "%s %%", "mco.download.preparing": "'Dree a preparà el descaregament", "mco.download.resourcePack.fail": "Impossibile scaricare il pacchetto di risorse!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "'Dree a descaregà l'ultima version del mond", "mco.error.invalid.session.message": "Proeuva a retacà el Minecraft", "mco.error.invalid.session.title": "Session invalida", "mco.errorMessage.6001": "Client mìa atualizad", "mco.errorMessage.6002": "<PERSON><PERSON><PERSON> <PERSON>r<PERSON>zzi mìa confermad", "mco.errorMessage.6003": "Rivad al limit de descaregament", "mco.errorMessage.6004": "Rivad al limit de caregament", "mco.errorMessage.6005": "<PERSON><PERSON> b<PERSON>o", "mco.errorMessage.6006": "Mondo non aggiornato", "mco.errorMessage.6007": "Utente in troppi Realm", "mco.errorMessage.6008": "Nome del Realm non valido", "mco.errorMessage.6009": "Descrizione del Realm non valida", "mco.errorMessage.connectionFailure": "<PERSON><PERSON>'è stad un eror, proeuva an'mò in seguit.", "mco.errorMessage.generic": "Si è verificato un errore: ", "mco.errorMessage.initialize.failed": "Inizializzazione del Realm non riuscita", "mco.errorMessage.noDetails": "Non sono stati forniti dettagli sull'errore", "mco.errorMessage.realmsService": "Si è verificato un errore (%s):", "mco.errorMessage.realmsService.configurationError": "Si è verificato un errore non previsto durante la modifica delle opzioni del mondo", "mco.errorMessage.realmsService.connectivity": "Impossibile connettersi a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Impossibile verificare la versione compatibile (risposta ricevuta: %s)", "mco.errorMessage.retry": "Riprova operazione", "mco.errorMessage.serviceBusy": "El realms a l'è ucupad adess.\nProeuva a conetess an'mò al tò realm in d'un para de menut.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Infomazioni!", "mco.invited.player.narration": "Invitato il giocatore %s", "mco.invites.button.accept": "Conferma", "mco.invites.button.reject": "Refuda", "mco.invites.nopending": "Nissun invit lassad lì!", "mco.invites.pending": "Nuovo/i invito/i!", "mco.invites.title": "<PERSON><PERSON>t lassad lì", "mco.minigame.world.changeButton": "Cata foeura un olter minisgioeugh", "mco.minigame.world.info.line1": "Quell chì el scambiarà per adess el to mond cont un minisgioeugh!", "mco.minigame.world.info.line2": "Te podareet tornà indree al to mond orisginal senza perder nagot.", "mco.minigame.world.noSelection": "<PERSON>a foeura, per piasé", "mco.minigame.world.restore": "'Dree a serrà el minisgioeugh...", "mco.minigame.world.restore.question.line1": "El minisgioeugh el finarà e el to realm el sarà recargad.", "mco.minigame.world.restore.question.line2": "Seet segur de andà inanz?", "mco.minigame.world.selected": "Minisgioeugh scernid:", "mco.minigame.world.slot.screen.title": "'Dree a scambià el mond...", "mco.minigame.world.startButton": "Scambia", "mco.minigame.world.starting.screen.title": "'Dree a tacà el minisgioeugh...", "mco.minigame.world.stopButton": "<PERSON><PERSON> minis<PERSON>", "mco.minigame.world.switch.new": "<PERSON><PERSON><PERSON> un olter minisgioeugh?", "mco.minigame.world.switch.title": "Cambia minisgioeugh", "mco.minigame.world.title": "Passa del realm al minisgioeugh", "mco.news": "Noeuve di Realms", "mco.notification.dismiss": "Ignora", "mco.notification.transferSubscription.buttonText": "Trasfer<PERSON><PERSON><PERSON> ora", "mco.notification.transferSubscription.message": "Gli abbonamenti a Realms per Java si trasferiscono su Microsoft Store. Non lasciare che il tuo abbonamento scada!\nTrasferisciti adesso e ottieni 30 giorni di Realms gratuitamente.\nVai sul tuo profilo su minecraft.net per trasferire il tuo abbonamento.", "mco.notification.visitUrl.buttonText.default": "Apri link", "mco.notification.visitUrl.message.default": "Visita il link qui sotto", "mco.onlinePlayers": "Giocatori online", "mco.play.button.realm.closed": "Il Realm è chiuso", "mco.question": "<PERSON><PERSON>", "mco.reset.world.adventure": "Aventure", "mco.reset.world.experience": "Esperienze", "mco.reset.world.generate": "Noeuv mond", "mco.reset.world.inspiration": "Ispirazzion", "mco.reset.world.resetting.screen.title": "'Dree a retacà el mond...", "mco.reset.world.seed": "Somenza (opzional)", "mco.reset.world.template": "Modei del mond", "mco.reset.world.title": "Retaca el mond", "mco.reset.world.upload": "Carega mond", "mco.reset.world.warning": "Quest chì el sostituarà el mond de adess del to realm", "mco.selectServer.buy": "Compra un realm!", "mco.selectServer.close": "Sarra su", "mco.selectServer.closed": "Realm serrad", "mco.selectServer.closeserver": "Serra realm", "mco.selectServer.configure": "Configura realm", "mco.selectServer.configureRealm": "Configura el realm", "mco.selectServer.create": "Fà su realm", "mco.selectServer.create.subtitle": "Seleziona quale mondo mettere nel tuo nuovo Realm", "mco.selectServer.expired": "Realm scadud", "mco.selectServer.expiredList": "La to iscrizzion l'è finida", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "La toa proeuva l'è finida", "mco.selectServer.expires.day": "El finiss tra un dì", "mco.selectServer.expires.days": "El finiss tra %s dì", "mco.selectServer.expires.soon": "El finiss prest", "mco.selectServer.leave": "Lassa realm", "mco.selectServer.loading": "Caricamento della lista dei Realm...", "mco.selectServer.mapOnlySupportedForVersion": "La mapa l'è mìa suportada in la %s", "mco.selectServer.minigame": "Minisgioeugh:", "mco.selectServer.minigameName": "Minigioco: %s", "mco.selectServer.minigameNotSupportedInVersion": "Se poeu mìa sgiugà quell minisgioeugh chì in la %s", "mco.selectServer.noRealms": "Se<PERSON>ra che tu non abbia un Realm. Aggiungi un Realm per giocare insieme ai tuoi amici.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Realm avert", "mco.selectServer.openserver": "Derva el realm", "mco.selectServer.play": "Sgiuga", "mco.selectServer.popup": "I Realm inn una manera segura e fazzila per godess un mond in linea del Minecraft con fina des amis. El soportà tant minisgioeugh e mond personalizad! Domà el proprietari del realm el gh'ha de pagà.", "mco.selectServer.purchase": "Sgionta Realm", "mco.selectServer.trial": "Proeuval!", "mco.selectServer.uninitialized": "Schiscia per tacà con un realm noeuv!", "mco.snapshot.createSnapshotPopup.text": "Stai per creare un Realm gratuito su versione sperimentale che verrà associato al tuo abbonamento a Realms. Questo nuovo Realm sperimentale sarà accessibile finché il tuo abbonamento a pagamento rimarrà attivo. Il tuo Realm a pagamento non sarà influenzato.", "mco.snapshot.createSnapshotPopup.title": "Creare un Realm sperimentare?", "mco.snapshot.creating": "Creazione del Realm sperimentale...", "mco.snapshot.description": "Associato a %s", "mco.snapshot.friendsRealm.downgrade": "Devi usare la versione %s per entrare in questo Realm", "mco.snapshot.friendsRealm.upgrade": "%s deve aggiornare il Realm perché tu possa giocare in questa versione", "mco.snapshot.paired": "Questo Realm sperimentale è associato a «%s»", "mco.snapshot.parent.tooltip": "Usa l'ultima versione stabile di Minecraft per giocare in questo Realm", "mco.snapshot.start": "Avvia un Realm sperimentale gratuito", "mco.snapshot.subscription.info": "Questo è un Realm su versione sperimentale associato all'abbonamento del tuo Realm «%s». Rimarrà attivo per tutto il tempo in cui lo sarà il Realm associato.", "mco.snapshot.tooltip": "Usa i Realm sperimentali per avere un'anteprima delle versioni future di Minecraft, che potrebbero includere nuove funzionalità e altri cambiamenti.\n\nPuoi trovare i tuoi Realm normali nella versione stabile del gioco.", "mco.snapshotRealmsPopup.message": "A partire dalla versione 23w41a, i Realm diventano disponibili anche nelle versioni sperimentali. Ogni abbonamento a Realms include un Realm sperimentale gratuito, separato dal tuo Realm per Java normale!", "mco.snapshotRealmsPopup.title": "I Realm sono ora disponibili nelle versioni sperimentali", "mco.snapshotRealmsPopup.urlText": "Scopri di più", "mco.template.button.publisher": "Autor", "mco.template.button.select": "Cata foeura", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "Modell del mond", "mco.template.info.tooltip": "Sit de l'autor", "mco.template.name": "<PERSON><PERSON>", "mco.template.select.failure": "S'è mìa podud restabilì la lista de contegnud per quella categoria chì.\nDà un'oggiada a la to conession internet, o proeuva ancamò in seguit.", "mco.template.select.narrate.authors": "Autor: %s", "mco.template.select.narrate.version": "version %s", "mco.template.select.none": "Ops, soeumeja che quella categoria de contegnud chì l'è voeui.\nDà un'oggiada pussee tardi per noeuv contegnud, o se te seet un creador,\n%s.", "mco.template.select.none.linkTitle": "resona se insubì propi ti un vergot", "mco.template.title": "Meder del mond", "mco.template.title.minigame": "Minisgioeugh", "mco.template.trailer.tooltip": "Trail<PERSON> de la mapa", "mco.terms.buttons.agree": "<PERSON><PERSON> ben", "mco.terms.buttons.disagree": "Va mìa ben", "mco.terms.sentence.1": "Son d'acordi cont i termin de servizzi", "mco.terms.sentence.2": "del Minecraft Realms", "mco.terms.title": "<PERSON><PERSON><PERSON> de <PERSON> realms", "mco.time.daysAgo": "%1$s giorno/i fa", "mco.time.hoursAgo": "%1$s ora/e fa", "mco.time.minutesAgo": "%1$s minuto/i fa", "mco.time.now": "adess", "mco.time.secondsAgo": "%1$s secondo/i fa", "mco.trial.message.line1": "Voeuret el to realm personal?", "mco.trial.message.line2": "Schiscia chì per pussee info!", "mco.upload.button.name": "Carega", "mco.upload.cancelled": "Caregament anullad", "mco.upload.close.failure": "S'è mìa podud serrà su el to realm, proeuva an'mò in seguit", "mco.upload.done": "Caregament finid", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Caregament fallid! (%s)", "mco.upload.failed.too_big.description": "Il mondo selezionato è troppo grande. La dimensione massima consentita è di %s.", "mco.upload.failed.too_big.title": "Mondo troppo grande", "mco.upload.hardcore": "Se poeuden mìa caregà i mond in modalità estrema!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "'Dree a preparà el to mond", "mco.upload.select.world.none": "<PERSON>ssun mond a un sgiugador trovad!", "mco.upload.select.world.subtitle": "Cata foeura un mond a un sgiugador per caregà", "mco.upload.select.world.title": "Carega mond", "mco.upload.size.failure.line1": "%s l'è trop grand!", "mco.upload.size.failure.line2": "A l'è %s. La dimension massima permetuda a l'è %s.", "mco.upload.uploading": "'Dree a caregà '%s'", "mco.upload.verifying": "'Dree a verifegà el to mond", "mco.version": "Versione: %s", "mco.warning": "<PERSON><PERSON>!", "mco.worldSlot.minigame": "Minisgioeugh", "menu.custom_options": "Opzioni personalizzate...", "menu.custom_options.title": "Opzioni personalizzate", "menu.custom_options.tooltip": "Nota: le opzioni personalizzate sono fornite da server o contenuti di terze parti.\nUsare con cautela!", "menu.custom_screen_info.button_narration": "Questa è una schermata personalizzata. Scopri di più.", "menu.custom_screen_info.contents": "I contenuti di questa schermata sono controllati da server e mappe di terze parti che non sono posseduti, gestiti o supervisionati da Mojang Studios o Microsoft.\n\nUsare con cautela! Fa' sempre attenzione nel seguire i link e non fornire mai dati personali, inclusi i dettagli di login.\n\nSe questa schermata ti impedisce di giocare, puoi disconnetterti dal server corrente usando il pulsante qui sotto.", "menu.custom_screen_info.disconnect": "Schermata personalizzata rifiutata", "menu.custom_screen_info.title": "Nota riguardante le schermate personalizzate", "menu.custom_screen_info.tooltip": "Questa è una schermata personalizzata. Clicca qui per saperne di più.", "menu.disconnect": "Desconetes", "menu.feedback": "Riscontro...", "menu.feedback.title": "Riscontro", "menu.game": "<PERSON><PERSON>", "menu.modded": " (Modifegad)", "menu.multiplayer": "Multisgiugador", "menu.online": "Minecraft Realms", "menu.options": "Configurazzion...", "menu.paused": "Sgioeugh in pausa", "menu.playdemo": "Sgiuga mond proeuva", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "'Dree a prontà l'area de nassida: %s%%", "menu.quick_actions": "Azioni rapide...", "menu.quick_actions.title": "Azioni rapide", "menu.quit": "Và foe<PERSON>", "menu.reportBugs": "<PERSON><PERSON><PERSON> eror", "menu.resetdemo": "Retaca mond proeuva", "menu.returnToGame": "Torna al <PERSON>eugh", "menu.returnToMenu": "Salva e torna indree", "menu.savingChunks": "'Dree a salvà i chunk", "menu.savingLevel": "'Dree a salvà el mond", "menu.sendFeedback": "Lassa un'opinion", "menu.server_links": "Link del server...", "menu.server_links.title": "Link del server", "menu.shareToLan": "Derva in LAN", "menu.singleplayer": "Un s<PERSON>ugador", "menu.working": "'Dree a elaborà...", "merchant.deprecated": "I paisan fann proved fina a do voeulte al dì.", "merchant.level.1": "Novell", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "Specializad", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Sc<PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Schiscia %1$s per desmontà", "multiplayer.applyingPack": "'Dree a aplicà el pachet de resorse", "multiplayer.confirm_command.parse_errors": "Stai cercando di eseguire un comando non riconosciuto o invalido.\nNe sei sicuro?\nComando: %s", "multiplayer.confirm_command.permissions_required": "Stai cercando di eseguire un comando che richiede permessi elevati.\n<PERSON><PERSON>ò potrebbe influire negativamente sulla tua esperienza di gioco.\nNe sei sicuro?\nComando: %s", "multiplayer.confirm_command.title": "Conferma l'esecuzione del comando", "multiplayer.disconnect.authservers_down": "I server de autenticazzion i fonzionen mìa. Proeuva an'mò de poeu!", "multiplayer.disconnect.bad_chat_index": "R<PERSON>vato un messaggio in chat mancante o riordinato dal server", "multiplayer.disconnect.banned": "Te seet bandid de quell server chì", "multiplayer.disconnect.banned.expiration": "\nSe cavarà el to bandiment el %s", "multiplayer.disconnect.banned.reason": "Te seet bandid de quell server chì.\nReson: %s", "multiplayer.disconnect.banned_ip.expiration": "\nEl to bandiment el sarà cavad in tra %s", "multiplayer.disconnect.banned_ip.reason": "El to indirizz IP l'è bandid del server.\nReson: %s", "multiplayer.disconnect.chat_validation_failed": "Eror al validà el messagg in la ciciarada", "multiplayer.disconnect.duplicate_login": "Te seet vegnud dent de un olter sit", "multiplayer.disconnect.expired_public_key": "La ciav publega del profil l'è scaduda. Dà un'oggiada che l'ora del to sistema l'è sincronizada, e retacael to s<PERSON><PERSON>gh.", "multiplayer.disconnect.flying": "Se poeu mìa sgolà in quell server chì", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.idling": "Te seet stad inativ per trop temp!", "multiplayer.disconnect.illegal_characters": "Carater illegal in la ciciarada", "multiplayer.disconnect.incompatible": "Client incompatibil! Dovra la version %s", "multiplayer.disconnect.invalid_entity_attacked": "Te seet 'dree a provà a atacà un'entità minga valida", "multiplayer.disconnect.invalid_packet": "El server l'ha mandad on pachet invalid", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON> <PERSON> sgiugador invalid", "multiplayer.disconnect.invalid_player_movement": "Rivad un moviment del sgiugador mìa valid", "multiplayer.disconnect.invalid_public_key_signature": "Firma de la ciav publega del profil l'è mìa valida.\nProeuva a retacà el sgioeugh.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firma de la ciav publega del profil l'è mìa valida.\nProeuva a retacà el sgioeugh.", "multiplayer.disconnect.invalid_vehicle_movement": "Rivad un veicol del sgiugador mìa valid", "multiplayer.disconnect.ip_banned": "El to indirizz IP l'è stad bandid del server", "multiplayer.disconnect.kicked": "Casciad de un operari", "multiplayer.disconnect.missing_tags": "Grup d'etichete mìa complet rivade al server. \nContata un operari del server.", "multiplayer.disconnect.name_taken": "Quell nom chì l'è sgiamò ciapad", "multiplayer.disconnect.not_whitelisted": "Te seet mìa in la lista bianca del server!", "multiplayer.disconnect.out_of_order_chat": "Hem ricevud di pachet de ciaciarade che fonzionen no. L'ora del to sistema l'è cambiada?", "multiplayer.disconnect.outdated_client": "Client incompatibile! Usa la versione %s", "multiplayer.disconnect.outdated_server": "Client incompatibile! Usa la versione %s", "multiplayer.disconnect.server_full": "El server l'è pien!", "multiplayer.disconnect.server_shutdown": "Server serrad su", "multiplayer.disconnect.slow_login": "Trop temp per andà dent", "multiplayer.disconnect.too_many_pending_chats": "Trop messagg in ciciarada mìa recognossud", "multiplayer.disconnect.transfers_disabled": "Il server non accetta i trasferimenti", "multiplayer.disconnect.unexpected_query_response": "Rivad al client dacc personalizad mìa previst", "multiplayer.disconnect.unsigned_chat": "L'è rivad un pachet de ciciarada senza firma o mìa valida.", "multiplayer.disconnect.unverified_username": "Se po mìa verifegà el nom del sgiugador!", "multiplayer.downloadingStats": "'Dree a recuperà i statisteghe...", "multiplayer.downloadingTerrain": "'Dree a cargà el terren...", "multiplayer.lan.server_found": "Noeuv server trovad: %s", "multiplayer.message_not_delivered": "Se po mìa mandà el mesagg in de la ciciarada, controlla in di log del server: %s", "multiplayer.player.joined": "%s l'è dent al sgioeugh", "multiplayer.player.joined.renamed": "%s (prima %s) l'è dent al sgioeugh", "multiplayer.player.left": "%s l'ha lassad el sgioeugh", "multiplayer.player.list.hp": "%s PS", "multiplayer.player.list.narration": "Sgiugador in linea: %s", "multiplayer.requiredTexturePrompt.disconnect": "El server el gh'ha besogn de on pachet de resorse personalizad", "multiplayer.requiredTexturePrompt.line1": "El server el gh'ha de besogn de 'n pachet de resorse aposta.", "multiplayer.requiredTexturePrompt.line2": "Refudà quell pachet de resorse personalizad chì el te desconetarà del server.", "multiplayer.socialInteractions.not_available": "I interazzion sociai se poeuden dovrà domà in di mond multisgiugador", "multiplayer.status.and_more": "... e %s olter ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Se poeu mìa conetess al server", "multiplayer.status.cannot_resolve": "Se po mìa resolver el nom del host", "multiplayer.status.finished": "<PERSON><PERSON>", "multiplayer.status.incompatible": "Version incompatibil!", "multiplayer.status.motd.narration": "Messaggio del giorno: %s", "multiplayer.status.no_connection": "(nissuna conession)", "multiplayer.status.old": "Vegg", "multiplayer.status.online": "In linea", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisecondi", "multiplayer.status.pinging": "'Dree a conetess...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s sgiugador in su %s inn in linea", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "La domanda de stat l'è rivada", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Rivad un stat mìa ciamad", "multiplayer.status.version.narration": "Version del server: %s", "multiplayer.stopSleeping": "Lassa let", "multiplayer.texturePrompt.failure.line1": "El pachet de resorse del server el se poeul mìa aplicà", "multiplayer.texturePrompt.failure.line2": "Qualsessia fonzionalità che gh'ha besogn de resorse personalizade la podaria fonzionà 'me che se spera", "multiplayer.texturePrompt.line1": "Quell server chì el consilia de dovrà un pachet de resorse personalizad.", "multiplayer.texturePrompt.line2": "Te voeulet descargàll e installàll indeperlù per magia?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMessagg del server:\n%s", "multiplayer.title": "Multisgiugador", "multiplayer.unsecureserver.toast": "I messaggi inviati su questo server possono essere modificati e potrebbero non riflettere i messaggi originali", "multiplayer.unsecureserver.toast.title": "I messagg de la ciciarada se poeuden mìa verifegà", "multiplayerWarning.check": "<PERSON>ra pu quella schermada chì", "multiplayerWarning.header": "Ocio: s<PERSON><PERSON><PERSON> in linea de terz", "multiplayerWarning.message": "Ocio: el sgioeugh in linea a l'è insubid de server de terz che inn mìa de propietà, ransgiad o soravisionad de la Mojang o del Microsoft. In del sgioeugh in linea, te podarisset trovà di mesagg in ciciarada mìa moderad o olter tipi de contegnud mìa bon per tucc fad su di dovrador.", "music.game.a_familiar_room": "<PERSON> — A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> — An Ordinary Day", "music.game.ancestry": "<PERSON> — Ancestry", "music.game.below_and_above": "<PERSON> — Below and Above", "music.game.broken_clocks": "<PERSON> — Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> — Comforting Memories", "music.game.creative.aria_math": "C418 — Aria Math", "music.game.creative.biome_fest": "C418 — Biome Fest", "music.game.creative.blind_spots": "C418 — Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 — Taswell", "music.game.crescent_dunes": "<PERSON> — Crescent Dunes", "music.game.danny": "C418 <PERSON> <PERSON>", "music.game.deeper": "<PERSON> — <PERSON>er", "music.game.dry_hands": "C418 — Dry Hands", "music.game.echo_in_the_wind": "<PERSON> — Echo in the Wind", "music.game.eld_unknown": "<PERSON> — <PERSON><PERSON>", "music.game.end.alpha": "C418 — Alpha", "music.game.end.boss": "C418 <PERSON> <PERSON>", "music.game.end.the_end": "C418 — The End", "music.game.endless": "<PERSON> — <PERSON><PERSON>", "music.game.featherfall": "<PERSON> — Featherfall", "music.game.fireflies": "<PERSON> Fireflies", "music.game.floating_dream": "<PERSON><PERSON> — Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> — Infinite Amethyst", "music.game.key": "C418 — <PERSON>", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> — Left to Bloom", "music.game.lilypad": "<PERSON> — Lilypad", "music.game.living_mice": "C418 — Living Mice", "music.game.mice_on_venus": "C418 — <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 — Minecraft", "music.game.nether.ballad_of_the_cats": "C418 — Ballad of the Cats", "music.game.nether.concrete_halls": "C418 — Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 — <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> — <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> — So Below", "music.game.nether.warmth": "C418 — Warmth", "music.game.one_more_day": "<PERSON> — One More Day", "music.game.os_piano": "<PERSON> — O's Piano", "music.game.oxygene": "C418 — Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> — Puzzlebox", "music.game.stand_tall": "<PERSON> — Stand Tall", "music.game.subwoofer_lullaby": "C418 — Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> — Firebugs", "music.game.swamp.labyrinthine": "<PERSON> — Labyrinthine", "music.game.sweden": "C418 — Sweden", "music.game.watcher": "<PERSON> — Watcher", "music.game.water.axolotl": "C418 — Axolotl", "music.game.water.dragon_fish": "C418 — Dragon Fish", "music.game.water.shuniji": "C418 — Shuni<PERSON>", "music.game.wending": "<PERSON> — Wending", "music.game.wet_hands": "C418 — <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> ya<PERSON><PERSON>", "music.menu.beginning_2": "C418 — Beginning 2", "music.menu.floating_trees": "C418 — Floating Trees", "music.menu.moog_city_2": "C418 — Moog City 2", "music.menu.mutation": "C418 — Mutation", "narration.button": "Boton: %s", "narration.button.usage.focused": "Schiscia \"Invio\" per pizzà", "narration.button.usage.hovered": "Clica col boton manzin per pizzà", "narration.checkbox": "Casella de controll: %s", "narration.checkbox.usage.focused": "Schiscia enter per alternà", "narration.checkbox.usage.hovered": "Schiscia el boton manzin per alternà", "narration.component_list.usage": "<PERSON><PERSON><PERSON><PERSON> sgiò Tab per andà a l'element che 'l ven", "narration.cycle_button.usage.focused": "Schiscia \"Invio\" per passà a %s", "narration.cycle_button.usage.hovered": "Clica cont el boton manzin per passà a %s", "narration.edit_box": "Casella de modifega: %s", "narration.item": "Oggetto: %s", "narration.recipe": "Riceta per %s", "narration.recipe.usage": "Clica cont el boton manzin per scernì foeura", "narration.recipe.usage.more": "Clica cont el boton drizz per fà vedè pussee de rezipe", "narration.selection.usage": "Schiscià i boton cont i sflizze su e sgiò per moeuves d'on element a l'alter", "narration.slider.usage.focused": "Schiscia el boton con la sflizze drizza o quell con quella manzina per cambià el valor", "narration.slider.usage.hovered": "Tira adree el regolador che cambià el valor", "narration.suggestion": "Consili scernid %s de %s: %s", "narration.suggestion.tooltip": "Consili scernid %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Premi Tab per passare al prossimo suggerimento", "narration.suggestion.usage.cycle.hidable": "Premi Tab per passare al prossimo suggerimento o Esc per chiudere i suggerimenti", "narration.suggestion.usage.fill.fixed": "Premi Tab per usare il suggerimento", "narration.suggestion.usage.fill.hidable": "Premi Tab per usare il suggerimento o Esc per chiudere i suggerimenti", "narration.tab_navigation.usage": "Premi Ctrl e Tab per cambiare scheda", "narrator.button.accessibility": "Acessibilità", "narrator.button.difficulty_lock": "<PERSON><PERSON>", "narrator.button.difficulty_lock.locked": "Blocada", "narrator.button.difficulty_lock.unlocked": "Desblocada", "narrator.button.language": "Lengua", "narrator.controls.bound": "%s a l'è ligad a %s", "narrator.controls.reset": "Retaca el boton de %s", "narrator.controls.unbound": "%s a l'è mìa ligad", "narrator.joining": "'Dree a s<PERSON>t<PERSON>ss", "narrator.loading": "'Dree a cargà: %s", "narrator.loading.done": "Fad", "narrator.position.list": "Scernida foeura la riga de la lista %s de %s", "narrator.position.object_list": "Scernid foeura l'element de la riga %s de %s", "narrator.position.screen": "Element del schermo %s de %s", "narrator.position.tab": "Selezionata la scheda %s di %s", "narrator.ready_to_play": "Pronto a giocare", "narrator.screen.title": "Schermada titol", "narrator.screen.usage": "Drova el boton de la maneta o 'l boton Tab per scernì foeura un element", "narrator.select": "Scernid: %s", "narrator.select.world": "Scernid %s, ultima voeulta %s, %s, %s, version: %s", "narrator.select.world_info": "Selezionato %s, ultima partita: %s, %s", "narrator.toast.disabled": "Sintesi vos smorzada", "narrator.toast.enabled": "Sintesi vos pizzada", "optimizeWorld.confirm.description": "Quell chì el provarà a otimizà el to mond cont el seguràss che tucc i dacc inn guarnad in del format del sgioeugh pussee noeuv. El podarà ciapà via tant temp, a segonda del to mond. Una voeulta finid, el to mond el sarà pussee svelt ma el sarà pu compatibil con vegge version del sgioeugh. Te seet segur de andà inanz?", "optimizeWorld.confirm.proceed": "Esegui un backup e ottimizza", "optimizeWorld.confirm.title": "O<PERSON><PERSON><PERSON>d", "optimizeWorld.info.converted": "Chunk atualizad: %s", "optimizeWorld.info.skipped": "Chunk soltad: %s", "optimizeWorld.info.total": "Biolche totai: %s", "optimizeWorld.progress.counter": "%s/%s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "'Dree a cuntà i chunk...", "optimizeWorld.stage.failed": "Fallid! :(", "optimizeWorld.stage.finished": "Completamento in corso...", "optimizeWorld.stage.finished.chunks": "Fine dell'aggiornamento dei chunk...", "optimizeWorld.stage.finished.entities": "Fine dell'aggiornamento delle entità...", "optimizeWorld.stage.finished.poi": "Fine dell'aggiornamento dei punti d'interesse...", "optimizeWorld.stage.upgrading": "Aggiornamento di tutti i chunk...", "optimizeWorld.stage.upgrading.chunks": "Aggiornamento di tutti i chunk...", "optimizeWorld.stage.upgrading.entities": "Aggiornamento di tutte le entità...", "optimizeWorld.stage.upgrading.poi": "Aggiornamento di tutti i punti d'interesse...", "optimizeWorld.title": "'Dree a otimizà el mond \"%s\"", "options.accessibility": "Accessibilità...", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON> elevato", "options.accessibility.high_contrast.error.tooltip": "Il pacchetto di risorse «Contrasto elevato» non è disponibile.", "options.accessibility.high_contrast.tooltip": "Aumenta il contrasto tra gli elementi dell'interfaccia.", "options.accessibility.high_contrast_block_outline": "Con<PERSON><PERSON>i", "options.accessibility.high_contrast_block_outline.tooltip": "Aumenta il contrasto del contorno del blocco puntato.", "options.accessibility.link": "Guida a l'acessibilità", "options.accessibility.menu_background_blurriness": "Sfocatura sfondo menu", "options.accessibility.menu_background_blurriness.tooltip": "Regola la sfocatura degli sfondi dei menu.", "options.accessibility.narrator_hotkey": "Scorciatoia assist. vocale", "options.accessibility.narrator_hotkey.mac.tooltip": "Consente di attivare o disattivare l'assistente vocale premendo Cmd + B.", "options.accessibility.narrator_hotkey.tooltip": "Consente di attivare o disattivare l'assistente vocale con la combinazione di tasti Ctrl + B.", "options.accessibility.panorama_speed": "Velocità del panorama", "options.accessibility.text_background": "Fond test", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Indepertut", "options.accessibility.text_background_opacity": "Opacità sfond test", "options.accessibility.title": "Acessibilità...", "options.allowServerListing": "Autoriza i liste di server", "options.allowServerListing.tooltip": "Po vesser che un quai server el faga la lista di sgiugador in linea in del so stat publich de lor.\nCon l'opzion smorzada, el to nom el sarà mìa mostrad in queste liste chì.", "options.ao": "<PERSON><PERSON> l<PERSON>ger", "options.ao.max": "<PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON>", "options.ao.off": "NO", "options.attack.crosshair": "<PERSON><PERSON>", "options.attack.hotbar": "Barra svelta", "options.attackIndicator": "Indicador de atach", "options.audioDevice": "Dispositiv", "options.audioDevice.default": "Configurazzion del sistema", "options.autoJump": "Salt automategh", "options.autoSuggestCommands": "Consili comand", "options.autosaveIndicator": "Indicator de autosalvament", "options.biomeBlendRadius": "Transizzion bioma", "options.biomeBlendRadius.1": "NO (pussee svelt)", "options.biomeBlendRadius.11": "11x11 (estrem)", "options.biomeBlendRadius.13": "13x13 (esagerad)", "options.biomeBlendRadius.15": "15x15 (massim)", "options.biomeBlendRadius.3": "3x3 (svelt)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (volt)", "options.biomeBlendRadius.9": "9x9 (volt fiss)", "options.chat": "Chat...", "options.chat.color": "Color", "options.chat.delay": "Ritard ciciarada: %s segond", "options.chat.delay_none": "Ritard ciciarada: <PERSON><PERSON><PERSON>", "options.chat.height.focused": "Altezza a foeugh", "options.chat.height.unfocused": "Altezza mìa a foeugh", "options.chat.line_spacing": "Interlinea", "options.chat.links": "Ligam web", "options.chat.links.prompt": "Conferma in sui ligam", "options.chat.opacity": "Opacità test ciciarada", "options.chat.scale": "Dimension test", "options.chat.title": "Ciciarada...", "options.chat.visibility": "Ciciarada", "options.chat.visibility.full": "Mostrada", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON> comand", "options.chat.width": "<PERSON><PERSON><PERSON><PERSON>", "options.chunks": "%s chunk", "options.clouds.fancy": "<PERSON><PERSON><PERSON>", "options.clouds.fast": "Svelte", "options.controls": "Controi...", "options.credits_and_attribution": "Riconoscimenti e attribuzioni...", "options.damageTiltStrength": "Oscillazione al colpo", "options.damageTiltStrength.tooltip": "Il livello di oscillazione della visuale quando si riceve danno.", "options.darkMojangStudiosBackgroundColor": "Loeugh monocromategh", "options.darkMojangStudiosBackgroundColor.tooltip": "Cambia el color del fond de cargament de Mojang Studios in negher.", "options.darknessEffectScale": "<PERSON>ul<PERSON><PERSON><PERSON> de scur", "options.darknessEffectScale.tooltip": "Controlla la forza de la pulsazzion de l'efet del scur de un warden o un sculk vosador.", "options.difficulty": "Dificoltà", "options.difficulty.easy": "Fazzil", "options.difficulty.easy.info": "Permette la generazione di creature ostili, ma ne riduce l'attacco. La barra della fame può esaurirsi e abbassarti la salute fino a 5 cuori.", "options.difficulty.hard": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.hard.info": "Permette la generazione di creature ostili con attacco maggiore. La barra della fame può esaurirsi e azzerarti la salute.", "options.difficulty.hardcore": "Estrema", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Permette la generazione di creature ostili con attacco normale. La barra della fame può esaurirsi e abbassarti la salute fino a mezzo cuore.", "options.difficulty.online": "Dificoltà del server", "options.difficulty.peaceful": "Pacifega", "options.difficulty.peaceful.info": "Blocca la generazione di tutte le creature ostili e di alcune creature neutrali. La barra della fame non si esaurisce e la salute viene ripristinata col passare del tempo.", "options.directionalAudio": "Audio direzzional", "options.directionalAudio.off.tooltip": "Son stereo classegh", "options.directionalAudio.on.tooltip": "El dopra un audio direzzional con tecnologia HRTF per fà mei la simulazzion del son 3D. Gh'è besogn del hardware audio compatibel cont el HRTF e l'è mei dovrà di scufie.", "options.discrete_mouse_scroll": "Scarligada discreta", "options.entityDistanceScaling": "Distanza di entità", "options.entityShadows": "Ombrie di entità", "options.font": "Impostazioni del carattere...", "options.font.title": "Impostazioni del tipo di carattere", "options.forceUnicodeFont": "Font unicode", "options.fov": "FOV", "options.fov.max": "Pro a Quake", "options.fov.min": "Normal", "options.fovEffectScale": "Efet camp de vision", "options.fovEffectScale.tooltip": "Controlla quanto il campo visivo può cambiare con gli effetti di gioco.", "options.framerate": "%s fps", "options.framerateLimit": "Limit de FPS", "options.framerateLimit.max": "Senza limit", "options.fullscreen": "Scher<PERSON> intregh", "options.fullscreen.current": "De adess", "options.fullscreen.entry": "%s × %s (%s Hz, %s bit)", "options.fullscreen.resolution": "Resoluzzion a scherm intregh", "options.fullscreen.unavailable": "Configurazzion mìa disponibil", "options.gamma": "Ciar", "options.gamma.default": "De bas", "options.gamma.max": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.min": "<PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocità del luccichio", "options.glintSpeed.tooltip": "Regola la velocità del luccichio che avvolge gli oggetti incantati.", "options.glintStrength": "Intensità del luccichio", "options.glintStrength.tooltip": "Regola l'opacità del luccichio che avvolge gli oggetti incantati.", "options.graphics": "Grafega", "options.graphics.fabulous": "Favolosa!", "options.graphics.fabulous.tooltip": "La grafega %s la dovra i sgionta-ombrie per dissegnà el temp atmosferegh, i nivole e i particelle travers de bloch semitrasparent e l'aqua.\nLa podaria influenzà i rese di dispositiv portatel e di scherm 4K.", "options.graphics.fancy": "<PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "La grafega detajada la balanza prestazzion e qualità per la pupart di computer.\nTemp atmosferegh, nivole e particelle se podarien mìa veder travers de l'aqua e di bloch semitrasparent.", "options.graphics.fast": "S<PERSON><PERSON>", "options.graphics.fast.tooltip": "La grafega svelta la sbassa la quantità de pioeuva e fioca che se ved. \nI efet de trasparenza inn smorzad per i bloch 'me i foeuje.", "options.graphics.warning.accept": "Continua senza supporto", "options.graphics.warning.cancel": "Indietro", "options.graphics.warning.message": "L'è stad rilevad che el to dispotiv grafegh a l'è mìa suportad per l'opzion grafega %s.\n\nTe poeudet ignorà e andà inanz, ma el suport el sarà mìa fornid per el to dispotiv se te scernisset de dovrà quell'opzion grafega chì.", "options.graphics.warning.renderer": "Render rilevad: [%s]", "options.graphics.warning.title": "Dispositiv grafegh mìa suportad", "options.graphics.warning.vendor": "Fornidor rilevad: [%s]", "options.graphics.warning.version": "Version del OpenGL relevada: [%s]", "options.guiScale": "Dimension GUI", "options.guiScale.auto": "Auto", "options.hidden": "<PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "Scònd i stralusc", "options.hideLightningFlashes.tooltip": "Disattiva i lampi di luce prodotti dai fulmini nel cielo. I fulmini saranno ancora visibili.", "options.hideMatchedNames": "Scònd i nom compagn", "options.hideMatchedNames.tooltip": "I server di terze parti potrebbero inviare messaggi in chat in un formato insolito.\nAttivando quest'opzione, i giocatori nascosti verranno identificati in base al nome del mittente dei messaggi.", "options.hideSplashTexts": "Nascondi testi iniziali", "options.hideSplashTexts.tooltip": "Nasconde i testi iniziali gialli mostrati nel menu principale.", "options.inactivityFpsLimit": "<PERSON><PERSON><PERSON><PERSON> frequenza", "options.inactivityFpsLimit.afk": "Se inattivo", "options.inactivityFpsLimit.afk.tooltip": "Limita la frequenza dei fotogrammi a 30 quando il gioco non riceve alcun comando dal giocatore per più di un minuto, dunque a 10 dopo altri 9 minuti.", "options.inactivityFpsLimit.minimized": "Se ridotto", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON>ita la frequenza dei fotogrammi solo quando la finestra di gioco è ridotta a icona.", "options.invertMouse": "Invert ratin", "options.japaneseGlyphVariants": "<PERSON><PERSON><PERSON> g<PERSON>i", "options.japaneseGlyphVariants.tooltip": "Applica le varianti giapponesi dei caratteri CJK al font predefinito.", "options.key.hold": "Tègn", "options.key.toggle": "Alterna", "options.language": "Lengua...", "options.language.title": "<PERSON><PERSON>", "options.languageAccuracyWarning": "(Le traduzioni potrebbero non essere accurate al 100%%)", "options.languageWarning": "I traduzzion podarien vesser mìa al 100%% giuste", "options.mainHand": "Man principal", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "Drizza", "options.mipmapLevels": "<PERSON><PERSON>", "options.modelPart.cape": "Tabarr", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "Gipon", "options.modelPart.left_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON> man<PERSON>a", "options.modelPart.right_pants_leg": "Gamba drizza", "options.modelPart.right_sleeve": "Manega drizza", "options.mouseWheelSensitivity": "Sensibilità scarligada", "options.mouse_settings": "Configurazzion ratin...", "options.mouse_settings.title": "Configurazzion ratin", "options.multiplayer.title": "Multisgiugador...", "options.multiplier": "%sx", "options.music_frequency": "Frequenza musicale", "options.music_frequency.constant": "Costante", "options.music_frequency.default": "Predefinita", "options.music_frequency.frequent": "Frequente", "options.music_frequency.tooltip": "Modifica la frequenza di riproduzione della musica nel mondo di gioco.", "options.narrator": "Sintesi vos", "options.narrator.all": "Tusscoss", "options.narrator.chat": "Ciciarada", "options.narrator.notavailable": "<PERSON><PERSON>a disponibel", "options.narrator.off": "NO", "options.narrator.system": "Sistema", "options.notifications.display_time": "Durata delle notifiche", "options.notifications.display_time.tooltip": "Regola per quanto tempo le notifiche rimarranno sullo schermo.", "options.off": "NO", "options.off.composed": "%s: NO", "options.on": "SÌ", "options.on.composed": "%s: SÌ", "options.online": "In linea...", "options.online.title": "Opzion in linea", "options.onlyShowSecureChat": "Mostra domà la ciciarada segura", "options.onlyShowSecureChat.tooltip": "Mostra domà messagg di olter sgiugador che poeuden vesser verifegad ’me inviad de quell sgiugador chì, e che inn stad mìa modifegad.", "options.operatorItemsTab": "Schema de lavorà de l'operador", "options.particles": "Particelle", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "<PERSON><PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s:%s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Cargador de chunk", "options.prioritizeChunkUpdates.byPlayer": "Cargament parzial", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Una quai azzion dent di chunk la compilarà el chunk subit, compagn de meter sgiò i bloch o s'cepài.", "options.prioritizeChunkUpdates.nearby": "Cargament total", "options.prioritizeChunkUpdates.nearby.tooltip": "I chunk arent inn semper compilad subit, cont el ris'c de problema de rendiment del sgioeugh quand che se meten sgiò o se s'cepen i bloch.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "I chunk arent inn compilad in parallel. Quella roba chì la podaria menà a brev bus de visual al s'cepà i bloch.", "options.rawMouseInput": "Input diret", "options.realmsNotifications": "Notizie e inviti di Realms", "options.realmsNotifications.tooltip": "Recupera notizie e inviti di Realms nel menu principale e mostra le loro rispettive icone sul pulsante Realms.", "options.reducedDebugInfo": "Dacc de debug sbassad", "options.renderClouds": "Nigole", "options.renderCloudsDistance": "Distanza delle nuvole", "options.renderDistance": "Visibilità", "options.resourcepack": "Pachet de resorse...", "options.rotateWithMinecart": "<PERSON>uota con vagonetto", "options.rotateWithMinecart.tooltip": "Stabilisce se la visuale del giocatore deve ruotare quando un vagonetto svolta. Disponibile solo nei mondi con il parametro sperimentale «Miglioramenti dei vagonetti» attivo.", "options.screenEffectScale": "Efet desformazzion", "options.screenEffectScale.tooltip": "Controlla l'intensità de desformazzion del scherm causada de l'ingossa e di portai del Nether.\nCon valor pussee bass, una quatadura verda la ciapa el sit de l'efet nausea.", "options.sensitivity": "Sensibilità", "options.sensitivity.max": "SUPERSVELT!!!", "options.sensitivity.min": "*sbagg*", "options.showNowPlayingToast": "Mostra notifiche musicali", "options.showNowPlayingToast.tooltip": "Mostra un messaggio a comparsa ogni volta che inizia una canzone. Lo stesso messaggio viene costantemente mostrato nel menu di pausa del gioco mentre la canzone è in riproduzione.", "options.showSubtitles": "Mostra sotatitoi", "options.simulationDistance": "<PERSON><PERSON><PERSON> de simulazzion", "options.skinCustomisation": "Personalizazzion aspet...", "options.skinCustomisation.title": "Personalizazzion aspet", "options.sounds": "<PERSON><PERSON> e son...", "options.sounds.title": "Opzion musega e son", "options.telemetry": "Dati di telemetria...", "options.telemetry.button": "Catada de dacc", "options.telemetry.button.tooltip": "«%s» include solo i dati necessari.\n«%s» include dati facoltativi e necessari.", "options.telemetry.disabled": "La telemetria è disattivata.", "options.telemetry.state.all": "Tucc", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "Nissun", "options.title": "Opzion", "options.touchscreen": "Modalità toca-scherm", "options.video": "Grafega...", "options.videoTitle": "Configurazzion video", "options.viewBobbing": "Vision dondanta", "options.visible": "Mostrad", "options.vsync": "VSync", "outOfMemory.message": "El Minecraft el gh'ha pu memoria.\n\nA l'è inscì forsi per un bug in del sgioeugh o gh'è mìa assee memoria dada a la machina virtual Java. \n\nTe seet andad foeura del sgioegh per schivà la coruzzion del nivell. Hem provad a liberà assee memoria per tornà indree al menù principal e tornà a sgiugà an'mò, ma l'è mìa andada ben.\n\nRetaca el sgioeugh se te vedet ancamò el messagg.", "outOfMemory.title": "Memoria finida!", "pack.available.title": "Disponibil", "pack.copyFailure": "Se ha minga podud copià i pachet", "pack.dropConfirm": "Te voeulet sgiontà quei pachet chì al Minecraft?", "pack.dropInfo": "Tira adree i file in questa fenestra per sgiontà i pachet", "pack.dropRejected.message": "Gli elementi seguenti non sono pacchetti validi e non sono stati copiati:\n %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON> non validi", "pack.folderInfo": "(<PERSON><PERSON><PERSON> chichi<PERSON> i pachet)", "pack.incompatible": "Mìa compatibil", "pack.incompatible.confirm.new": "Quell pachet de resors chì a l'è stad fad su per una version pussee noeuva del Minecraft e el podaria fonzionà mìa ben.", "pack.incompatible.confirm.old": "Quell pachet de resors chì a l'è stad fad su per una version pussee veggia del Minecraft e el podaria fonzionà mìa ben.", "pack.incompatible.confirm.title": "Te seet segur de volé cargà quell pachet chì?", "pack.incompatible.new": "(Fad su per una version del Minecraft pussee noeuva)", "pack.incompatible.old": "(Fad su per una version del Minecraft pussee veggia)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Derva la cartella di pachet", "pack.selected.title": "<PERSON><PERSON><PERSON>", "pack.source.builtin": "normal", "pack.source.feature": "fonzionalità", "pack.source.local": "local", "pack.source.server": "server", "pack.source.world": "mond", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanes", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Cortile", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Barocco", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Bombardament del bersai riussid", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Mazzo di fiori", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Crani in fiame", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Uccello delle caverne", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Cambio", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON>elà, sior Courbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Terra", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Nemico finale", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Combatent", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Reperto", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab con tri piarei", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Nebbia", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativa", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Sfera", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Gufo con limoni", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passaggio", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Porc su tela", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Arbor del Paradis", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Stagno", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La pissina", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Cavalcata in prateria", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Rest mortai", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Crani e roeuse", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Tuscoss a la via", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "El voeui", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Desert", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Aqua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vent", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Variante casuale", "parsing.bool.expected": "Se speta un valor boolean", "parsing.bool.invalid": "Valor boolean minga bon, el gh'avariss de vesser 'true' o 'false' inveci l'è '%s'", "parsing.double.expected": "<PERSON><PERSON>ess<PERSON> un valor dobi", "parsing.double.invalid": "Valor dobi mìa valid: %s", "parsing.expected": "Se preved \"%s\"", "parsing.float.expected": "Se speta un float", "parsing.float.invalid": "Valor float minga bon: %s", "parsing.int.expected": "Se speta un numer intregh", "parsing.int.invalid": "Numer intregh minga bon: %s", "parsing.long.expected": "<PERSON><PERSON><PERSON><PERSON> un valor long", "parsing.long.invalid": "Valor long mìa valid: %s", "parsing.quote.escape": "Sequenza de escape \"\\%s\" mìa valida in la stringa con virgolete", "parsing.quote.expected.end": "A gh'è bisogn di virgolete a la fin de la stringa per sarala su", "parsing.quote.expected.start": "A gh'è bisogn di virgolete al principi de la stringa", "particle.invalidOptions": "Impossibile analizzare le opzioni della particella: %s", "particle.notFound": "Particola minga conossuda: %s", "permissions.requires.entity": "A gh'è bisogn d'un'entità de fà andà quell comand chì", "permissions.requires.player": "A gh'è besogn de on sgiugador de fà andà quell comand chì", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Efet aplicad:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicad minga conossud: %s", "quickplay.error.invalid_identifier": "Impossibile trovare un mondo con quell'identificatore", "quickplay.error.realm_connect": "Impossibile connettersi al Realm", "quickplay.error.realm_permission": "Non hai l'autorizzazione per connetterti a questo Realm", "quickplay.error.title": "Avvio rapido fallito", "realms.configuration.region.australia_east": "Australia (Nuovo Galles del Sud)", "realms.configuration.region.australia_southeast": "Australia (Victoria)", "realms.configuration.region.brazil_south": "Brasile", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Stati Uniti d'America (Iowa)", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Stati Uniti d'America (Virginia)", "realms.configuration.region.east_us_2": "Stati Uniti d'America (Carolina del Nord)", "realms.configuration.region.france_central": "Francia", "realms.configuration.region.japan_east": "Giappone (orientale)", "realms.configuration.region.japan_west": "Giappone (occidentale)", "realms.configuration.region.korea_central": "Corea del Sud", "realms.configuration.region.north_central_us": "Stati Uniti d'America (Illinois)", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Stati Uniti d'America (Texas)", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Svezia", "realms.configuration.region.uae_north": "Emirati Arabi Uniti", "realms.configuration.region.uk_south": "Inghilterra (meridionale)", "realms.configuration.region.west_central_us": "Stati Uniti d'America (Utah)", "realms.configuration.region.west_europe": "<PERSON><PERSON>", "realms.configuration.region.west_us": "Stati Uniti d'America (California)", "realms.configuration.region.west_us_2": "Stati Uniti d'America (Washington)", "realms.configuration.region_preference.automatic_owner": "Automatico (ping del proprietario del Realm)", "realms.configuration.region_preference.automatic_player": "Automatico (primo a unirsi alla sessione)", "realms.missing.snapshot.error.text": "Realms a l'è minga suportad in di version de proeuva", "recipe.notFound": "Riceta descognossuda: %s", "recipe.toast.description": "Varda el liber di ricete", "recipe.toast.title": "Noeuve ricete!", "record.nowPlaying": "'Dree a scoltà: %s", "recover_world.bug_tracker": "<PERSON><PERSON><PERSON> un <PERSON>", "recover_world.button": "Tenta il recupero", "recover_world.done.failed": "Impossibile recuperare lo stato precedente.", "recover_world.done.success": "<PERSON><PERSON>ero rius<PERSON>to!", "recover_world.done.title": "Recupero completato", "recover_world.issue.missing_file": "File mancante", "recover_world.issue.none": "<PERSON><PERSON><PERSON> errore", "recover_world.message": "Durante il tentativo di lettura della cartella del mondo «%s», si sono verificati i seguenti errori.\nÈ possibile tentare il ripristino del mondo a uno stato precedente oppure puoi segnalare quest'errore nel registro dei bug.", "recover_world.no_fallback": "Non sono disponibili stati recuperabili", "recover_world.restore": "Tenta il ripristino", "recover_world.restoring": "Tentativo di ripristino del mondo...", "recover_world.state_entry": "Stato in data %s: ", "recover_world.state_entry.unknown": "scon<PERSON><PERSON><PERSON>", "recover_world.title": "Impossibile caricare il mondo", "recover_world.warning": "Impossibile caricare il riepilogo del mondo", "resourcePack.broken_assets": "TROVADE RESORSE MALFADE", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON> elevato", "resourcePack.load_fail": "Recargament resorse fallid", "resourcePack.programmer_art.name": "Art del programador", "resourcePack.runtime_failure": "Errore del pacchetto di risorse", "resourcePack.server.name": "Resorse specifeghe del mond", "resourcePack.title": "<PERSON><PERSON><PERSON> pachet de resorsa", "resourcePack.vanilla.description": "L’aspet de bas del Minecraft", "resourcePack.vanilla.name": "De bas", "resourcepack.downloading": "'Dree a descaregà el pachet de resorse", "resourcepack.progress": "'Dree a descaregà el file (%s MB)...", "resourcepack.requesting": "'Dree a mandà la domanda...", "screenshot.failure": "Se poeu mìa salvà la schermada: %s", "screenshot.success": "La schermada l'è stada salvada 'me %s", "selectServer.add": "Sgionta server", "selectServer.defaultName": "Server del Minecraft", "selectServer.delete": "<PERSON><PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "Ta voeulet ranzà quell server chì?", "selectServer.deleteWarning": "%s el sarà perdud per semper! (A l'è tant temp!)", "selectServer.direct": "Conession direta", "selectServer.edit": "Modifega", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON>)", "selectServer.refresh": "Carga an'mò", "selectServer.select": "Sgiontes al server", "selectWorld.access_failure": "Se poeu mìa and<PERSON> dent al mond", "selectWorld.allowCommands": "<PERSON><PERSON> gabole", "selectWorld.allowCommands.info": "Comand 'me /gamemode, /experience", "selectWorld.allowCommands.new": "Abilita comandi", "selectWorld.backupEraseCache": "Voda i dacc de la cache", "selectWorld.backupJoinConfirmButton": "Fà su backup e carga", "selectWorld.backupJoinSkipButton": "El soo 'se son 'dree a fà!", "selectWorld.backupQuestion.customized": "I mond personalizad inn pu suportad", "selectWorld.backupQuestion.downgrade": "Se poeu no sbassà la version de 'n mond", "selectWorld.backupQuestion.experimental": "I mond che dovren di configurazzion sperimentai inn mìa suportad", "selectWorld.backupQuestion.snapshot": "Te voeulet debon cargà quell mond chì?", "selectWorld.backupWarning.customized": "Per sfortuna suportom mìa i mond personalizad in quella version chì del Minecraft. Poeudom de tute i manere cargà el mond e tegnì tuscoss come l'era ma qualsessia terren noeuv fad su al sarà pu personalizad. Ne despias!", "selectWorld.backupWarning.downgrade": "Quell mond chì l'è stad doperad in la version %s; ti te seet in la version %s. Sbassà de version un mond el po s'cepàll - podom no garantì che 'l caregarà o 'l fonzionarà. Se te voeuret debon andà inanz, fà un backup, per piasé!", "selectWorld.backupWarning.experimental": "Quell mond chì el dovra di configurazzion sperimentai che podarien desmeter de fonzionà in qualsessia moment. A poeudom minga segurà che el se cargarà o el fonzionarà. Fà ballà l'oeugg!", "selectWorld.backupWarning.snapshot": "Quell mond chì l'è stad sgiugad l'ultima voeulta in la version %s; adess te seet a la %s. Fà su un salvament de resèrva in cas de coruzzion!", "selectWorld.bonusItems": "Cassa bonus", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "comandi", "selectWorld.conversion": "El gh'ha de vesser convertid!", "selectWorld.conversion.tooltip": "Quell mond chì el gh'ha de vesser dervid in d'una version pussee veggia (compagna de la 1.6.4) per vesser convertì senza problema", "selectWorld.create": "Fà su un mond noeuv", "selectWorld.customizeType": "Personaliza", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON>", "selectWorld.data_read": "'Dree a lensger i dacc...", "selectWorld.delete": "<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "Te seet segur de scancellà quell mond chì?", "selectWorld.deleteWarning": "%s el sarà perdud per semper! (A l'è tant temp!)", "selectWorld.delete_failure": "Eror al cavà el mond", "selectWorld.edit": "Modifega", "selectWorld.edit.backup": "Fà un backup", "selectWorld.edit.backupCreated": "Backup fad: %s", "selectWorld.edit.backupFailed": "Backup fallid", "selectWorld.edit.backupFolder": "Derva cartella di backup", "selectWorld.edit.backupSize": "dimension: %s MB", "selectWorld.edit.export_worldgen_settings": "Esporta i configurazzion de fà su del mond", "selectWorld.edit.export_worldgen_settings.failure": "Esportazzion fallida", "selectWorld.edit.export_worldgen_settings.success": "Esportad", "selectWorld.edit.openFolder": "Derva cartella del mond", "selectWorld.edit.optimize": "Otimiza el mond", "selectWorld.edit.resetIcon": "Retaca l'icona", "selectWorld.edit.save": "<PERSON><PERSON>", "selectWorld.edit.title": "Modifega mond", "selectWorld.enterName": "Nom del mond", "selectWorld.enterSeed": "Somenza de fà su del mond", "selectWorld.experimental": "Sperimental", "selectWorld.experimental.details": "Detai", "selectWorld.experimental.details.entry": "Fonzion sperimentai necessarie: %s", "selectWorld.experimental.details.title": "Requisit: fonzionalità sperimentai", "selectWorld.experimental.message": "Attenzione!\nQuesta configurazione richiede funzionalità che sono ancora in fase di sviluppo. Il tuo mondo potrebbe arrestarsi, corrompersi o smettere di funzionare nelle versioni future.", "selectWorld.experimental.title": "Ocio: fonzion sperimentai", "selectWorld.experiments": "Esperimenti", "selectWorld.experiments.info": "Gli esperimenti sono nuove funzionalità che in futuro potrebbero essere aggiunte alla versione completa del gioco. Attenzione: gli esperimenti possono causare errori e non possono essere disattivati dopo la creazione del mondo.", "selectWorld.futureworld.error.text": "Un quaicoss a l'è andad mal in del cargà el mond de una varsion futura. Però l'era un'operazzion a ris'c, ne despias che la sia mìa fonzionada.", "selectWorld.futureworld.error.title": "Gh'è capitad un eror!", "selectWorld.gameMode": "Modalità", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Come la modalità sopravvivenza, ma non puoi piazzare o distruggere blocchi.", "selectWorld.gameMode.adventure.line1": "Compagna de la modalità soraviv, ma i bloch", "selectWorld.gameMode.adventure.line2": "se poeuden mìa sgiontà o cavà", "selectWorld.gameMode.creative": "Creativa", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, cost<PERSON><PERSON><PERSON> ed esplora senza limitazioni. Puoi volare, disponi di risorse infinite e i mostri non possono ferirti.", "selectWorld.gameMode.creative.line1": "Resorse senza fin, sgola liber e", "selectWorld.gameMode.creative.line2": "s'cepa i bloch a vista", "selectWorld.gameMode.hardcore": "Estrema", "selectWorld.gameMode.hardcore.info": "Modalità sopravvivenza bloccata in difficoltà «Difficile». Se muori non puoi più rinascere.", "selectWorld.gameMode.hardcore.line1": "Compagna de la modalità soraviv, ma con la", "selectWorld.gameMode.hardcore.line2": "dificoltà pussee volta, e apena una vita", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON>i guardare, ma non toccare.", "selectWorld.gameMode.spectator.line1": "Te poeudet vardà ma mìa tocà", "selectWorld.gameMode.survival": "Soravivenza", "selectWorld.gameMode.survival.info": "Esplora un mondo misterioso dove puoi costruire, raccogliere risorse, fabbricare oggetti e combattere i mostri.", "selectWorld.gameMode.survival.line1": "Cerca i resorse, fà su lavorà,", "selectWorld.gameMode.survival.line2": "và su de livell, salut e fam", "selectWorld.gameRules": "<PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Importa configurazzion", "selectWorld.import_worldgen_settings.failure": "Eror a l'importà configurazzion", "selectWorld.import_worldgen_settings.select_file": "Scerniss configurazzion file (.json)", "selectWorld.incompatible.description": "Questo mondo non può essere aperto nella versione attuale.\nL'ultima volta è stato caricato nella versione %s.", "selectWorld.incompatible.info": "Versione incompatibile: %s", "selectWorld.incompatible.title": "Versione incompatibile", "selectWorld.incompatible.tooltip": "<PERSON>o mondo non può essere aperto perché è stato creato da una versione incompatibile.", "selectWorld.incompatible_series": "Fad su cont una version incompatibel", "selectWorld.load_folder_access": "Se poeu mìa lensger o andà dent la cartella indove che i mond del sgioeugh inn salvad!", "selectWorld.loading_list": "’Dree a cargà la lista di mond", "selectWorld.locked": "<PERSON><PERSON> avert in d'una altra session del Minecraft", "selectWorld.mapFeatures": "Fà su struture", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, relitti ecc.", "selectWorld.mapType": "Tipo de mond", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Alter opzion del mond...", "selectWorld.newWorld": "Mond noeuv", "selectWorld.recreate": "Fà su an'mò", "selectWorld.recreate.customized.text": "I mond personalizad inn pu suportad in questa version ch<PERSON> del Minecraft. A poeudom provà a fàll su ancamò con l'istessa somenza e propietà, ma qualsessia personalizazzion al terren la sarà perduda. Se scusom per l'inconvenient!", "selectWorld.recreate.customized.title": "I mond personalizad inn pu suportad", "selectWorld.recreate.error.text": "Un quaicoss l'è andad mal quand s'era 'dree a provà a fà su un mond.", "selectWorld.recreate.error.title": "Gh'è capitad un eror!", "selectWorld.resource_load": "Preparazione delle risorse...", "selectWorld.resultFolder": "El sarà salvad in:", "selectWorld.search": "cerca un mond", "selectWorld.seedInfo": "Lassa voeudi una somenza a cas", "selectWorld.select": "Sgiuga el mond scernid", "selectWorld.targetFolder": "Cartella di salvataggio: %s", "selectWorld.title": "<PERSON>erniss mond", "selectWorld.tooltip.fromNewerVersion1": "El mond l'è stad salvad in d'una noeuva version,", "selectWorld.tooltip.fromNewerVersion2": "cargà el mond el podariss dà problema!", "selectWorld.tooltip.snapshot1": "Desmenteghes mìa de fà el backup del mond", "selectWorld.tooltip.snapshot2": "in<PERSON><PERSON> de carg<PERSON>ll in quell snapshot chì.", "selectWorld.unable_to_load": "Se poeu mìa cargà i mond", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON> is<PERSON><PERSON>", "selectWorld.versionQuestion": "Te seet segur de volé cargà quell mond chì?", "selectWorld.versionUnknown": "descogno<PERSON><PERSON>", "selectWorld.versionWarning": "Quell mond chì l'è stad sgiugad l'ultima voeulta con la version %s; cargàll adess el poeu corrompell!", "selectWorld.warning.deprecated.question": "Una quai configurazzion l'è obsoleta e la desmetarà de fonzionà in del futur. Te voeulet andà inanz?", "selectWorld.warning.deprecated.title": "Ocio! Quei configurazzion chì doperen di fonzion obsolete", "selectWorld.warning.experimental.question": "I configurazzion inn sperimentai e podarien desmeter de fonzionà in futur. Te voeulet andà inanz?", "selectWorld.warning.experimental.title": "Ocio! Quei configurazzion chì doperen di fonzion sperimentai", "selectWorld.warning.lowDiskSpace.description": "C'è poco spazio libero sul dispositivo.\nEsaurire lo spazio su disco durante una sessione di gioco può danneggiare il tuo mondo.", "selectWorld.warning.lowDiskSpace.title": "Attenzione! Poco spazio sul disco rimasto!", "selectWorld.world": "Mond", "sign.edit": "Messagg del cartell", "sleep.not_possible": "Se po no dormì in quella nocc chì", "sleep.players_sleeping": "%s/%s sgiugador inn 'dree a dormì", "sleep.skipping_night": "'Dree a dormì tuta la noeucc", "slot.only_single_allowed": "Sono consentiti solo slot singoli (ricevuto %s)", "slot.unknown": "Spazzi descognossud: %s", "snbt.parser.empty_key": "La chiave non può essere vuota", "snbt.parser.expected_binary_numeral": "Previsto un numero binario", "snbt.parser.expected_decimal_numeral": "Previsto un numero decimale", "snbt.parser.expected_float_type": "Previsto un numero in virgola mobile", "snbt.parser.expected_hex_escape": "Previsto un carattere letterale di lunghezza %s", "snbt.parser.expected_hex_numeral": "Previsto un numero esadecimale", "snbt.parser.expected_integer_type": "Previsto un numero intero", "snbt.parser.expected_non_negative_number": "Previsto un numero non negativo", "snbt.parser.expected_number_or_boolean": "Previsto un numero o valore booleano", "snbt.parser.expected_string_uuid": "Prevista una stringa con un UUID valido", "snbt.parser.expected_unquoted_string": "Prevista una stringa valida senza virgolette", "snbt.parser.infinity_not_allowed": "I numeri non finiti non sono consentiti", "snbt.parser.invalid_array_element_type": "Tipo di elemento di matrice non valido", "snbt.parser.invalid_character_name": "Nome del carattere Unicode non valido", "snbt.parser.invalid_codepoint": "Valore del carattere Unicode non valido: %s", "snbt.parser.invalid_string_contents": "Contenuti della stringa non validi", "snbt.parser.invalid_unquoted_start": "Le stringhe senza virgolette non possono iniziare con le cifre da 0 a 9, + o -", "snbt.parser.leading_zero_not_allowed": "I numeri decimali non possono iniziare con 0", "snbt.parser.no_such_operation": "Nessuna operazione: %s", "snbt.parser.number_parse_failure": "Impossibile analizzare il numero: %s", "snbt.parser.undescore_not_allowed": "I trattini bassi non sono consentiti all'inizio o alla fine di un numero", "soundCategory.ambient": "Ambient", "soundCategory.block": "Bloch", "soundCategory.hostile": "Creadure nemise", "soundCategory.master": "Volum general", "soundCategory.music": "Musega", "soundCategory.neutral": "Creadure amise", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "<PERSON><PERSON> m<PERSON>ga", "soundCategory.ui": "Interfaccia", "soundCategory.voice": "Vos e dialogh", "soundCategory.weather": "Clima", "spectatorMenu.close": "Serra su el menù", "spectatorMenu.next_page": "Pagina dopo", "spectatorMenu.previous_page": "Pagina prima", "spectatorMenu.root.prompt": "Schiscia una ciav per scernì un comand, e an'mò per dovràll.", "spectatorMenu.team_teleport": "Teleporta a un member de squadra", "spectatorMenu.team_teleport.prompt": "Cata foeura che squadra teleportàss", "spectatorMenu.teleport": "Teleporta al sgiugador", "spectatorMenu.teleport.prompt": "Scerniss un sgiudador de teleportà", "stat.generalButton": "General", "stat.itemsButton": "Lavorà", "stat.minecraft.animals_bred": "<PERSON><PERSON><PERSON> levad", "stat.minecraft.aviate_one_cm": "Distanza in eliter", "stat.minecraft.bell_ring": "Campane sonade", "stat.minecraft.boat_one_cm": "Distanza in barca", "stat.minecraft.clean_armor": "Part de armadura netade", "stat.minecraft.clean_banner": "Bandere netade", "stat.minecraft.clean_shulker_box": "Scatole de shulker netade", "stat.minecraft.climb_one_cm": "Di<PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "Distanza in scroscion", "stat.minecraft.damage_absorbed": "<PERSON><PERSON> surbid", "stat.minecraft.damage_blocked_by_shield": "Dagn blocad cont el scud", "stat.minecraft.damage_dealt": "Dagn causad", "stat.minecraft.damage_dealt_absorbed": "Dagn causad (surbid)", "stat.minecraft.damage_dealt_resisted": "Dagn causad (resistid)", "stat.minecraft.damage_resisted": "Dagn resistid", "stat.minecraft.damage_taken": "Dagn ricevud", "stat.minecraft.deaths": "Numer de mort", "stat.minecraft.drop": "<PERSON><PERSON><PERSON> lassad", "stat.minecraft.eat_cake_slice": "Fete de torta mangiade", "stat.minecraft.enchant_item": "Lavorà instriad", "stat.minecraft.fall_one_cm": "Distanza in crodada", "stat.minecraft.fill_cauldron": "<PERSON><PERSON> impienid", "stat.minecraft.fish_caught": "Pess pescad", "stat.minecraft.fly_one_cm": "Distanza in sgol", "stat.minecraft.happy_ghast_one_cm": "Distanza su ghast allegro", "stat.minecraft.horse_one_cm": "Distanza a cavall", "stat.minecraft.inspect_dispenser": "Scompartidor esaminad", "stat.minecraft.inspect_dropper": "Sgetador esaminad", "stat.minecraft.inspect_hopper": "Tramoeusge esaminade", "stat.minecraft.interact_with_anvil": "Interazzion cont i incusgen", "stat.minecraft.interact_with_beacon": "Interazzion cont i far", "stat.minecraft.interact_with_blast_furnace": "Interazzion cont i volt-forn", "stat.minecraft.interact_with_brewingstand": "Interazzion cont i lambich", "stat.minecraft.interact_with_campfire": "Interazzion cont i falò", "stat.minecraft.interact_with_cartography_table": "Interazzion cont i banch de cartografia", "stat.minecraft.interact_with_crafting_table": "Interazzion cont i banch de lavorà", "stat.minecraft.interact_with_furnace": "Interazzion coi fornas", "stat.minecraft.interact_with_grindstone": "Interazzion cont i mole", "stat.minecraft.interact_with_lectern": "Interazzion cont i letorin", "stat.minecraft.interact_with_loom": "Interazzion cont i telar", "stat.minecraft.interact_with_smithing_table": "Interazzion cont i tavol del ferrer", "stat.minecraft.interact_with_smoker": "Interazzion cont i afumegador", "stat.minecraft.interact_with_stonecutter": "Interazzion cont i taja-preje", "stat.minecraft.jump": "Salt", "stat.minecraft.leave_game": "Session lassade", "stat.minecraft.minecart_one_cm": "Distanza in su carrell de minera", "stat.minecraft.mob_kills": "Creadure copade", "stat.minecraft.open_barrel": "<PERSON><PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON> der<PERSON>", "stat.minecraft.open_enderchest": "Ceste de l'ender dervide", "stat.minecraft.open_shulker_box": "Scatole de shulker dervide", "stat.minecraft.pig_one_cm": "Distanza in su porscell", "stat.minecraft.play_noteblock": "<PERSON><PERSON> de musega sonad", "stat.minecraft.play_record": "<PERSON><PERSON> de musega sonad", "stat.minecraft.play_time": "Temp sgiugad", "stat.minecraft.player_kills": "Sgiugador <PERSON>", "stat.minecraft.pot_flower": "Piante in vas", "stat.minecraft.raid_trigger": "Incursion pizzade", "stat.minecraft.raid_win": "Incursion vensgiude", "stat.minecraft.sleep_in_bed": "Nocc dormide in d'un lecc", "stat.minecraft.sneak_time": "Temp in scruscion", "stat.minecraft.sprint_one_cm": "Distanza corsa", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON> in su strider", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> nodada", "stat.minecraft.talked_to_villager": "Ciciarade cont i paisan", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON> colpid", "stat.minecraft.time_since_death": "Temp de l'ultima mort", "stat.minecraft.time_since_rest": "Temp de l'ultima possada", "stat.minecraft.total_world_time": "Temp cont el mond avert", "stat.minecraft.traded_with_villager": "Scambi cont i paisan", "stat.minecraft.trigger_trapped_chest": "Ceste trapola pizzade", "stat.minecraft.tune_noteblock": "Bloch de musega incordad", "stat.minecraft.use_cauldron": "Aqua ciapada di calderon", "stat.minecraft.walk_on_water_one_cm": "Distanza a pè in aqua bassa", "stat.minecraft.walk_one_cm": "Distanza a pè", "stat.minecraft.walk_under_water_one_cm": "Distanza a pè sota l'aqua", "stat.mobsButton": "Creadure", "stat_type.minecraft.broken": "Voeulte a s'cepà", "stat_type.minecraft.crafted": "Voeulte a fà su", "stat_type.minecraft.dropped": "<PERSON><PERSON>", "stat_type.minecraft.killed": "T'heet copad %s %s", "stat_type.minecraft.killed.none": "T'heet mai copad %s", "stat_type.minecraft.killed_by": "%s el t'ha copad %s voeulte", "stat_type.minecraft.killed_by.none": "Te seet mai stad copad de %s", "stat_type.minecraft.mined": "Voeulte a sgarlà", "stat_type.minecraft.picked_up": "Catad su", "stat_type.minecraft.used": "Voeulte a dovrà", "stats.none": "-", "structure_block.button.detect_size": "RILEVA", "structure_block.button.load": "CARGA", "structure_block.button.save": "SALVA", "structure_block.custom_data": "Nom de l'eticheta de dacc personalizad", "structure_block.detect_size": "Rileva dimensione e posizione:", "structure_block.hover.corner": "Canton: %s", "structure_block.hover.data": "Dacc: %s", "structure_block.hover.load": "Carga: %s", "structure_block.hover.save": "Salva: %s", "structure_block.include_entities": "Includi entità:", "structure_block.integrity": "Integrità e somenza de la strutura", "structure_block.integrity.integrity": "Integrità de la strutura", "structure_block.integrity.seed": "Somenza de la strutura", "structure_block.invalid_structure_name": "Nom de la strutura \"%s\" mìa valid", "structure_block.load_not_found": "Strutura %s mìa disponibil", "structure_block.load_prepare": "Posizzion de la strutura %s prontada", "structure_block.load_success": "Strutura cargada de \"%s\"", "structure_block.mode.corner": "Canton", "structure_block.mode.data": "Dacc", "structure_block.mode.load": "Carga", "structure_block.mode.save": "<PERSON><PERSON>", "structure_block.mode_info.corner": "Canton - Marca posizzion e dimension", "structure_block.mode_info.data": "Dacc - Dovra logica del sgioeugh", "structure_block.mode_info.load": "Cargament - Carga de un file", "structure_block.mode_info.save": "Salvament - Scrìv su file", "structure_block.position": "Posizzion relativa", "structure_block.position.x": "posizzion relativa in x", "structure_block.position.y": "posizzion relativa in y", "structure_block.position.z": "posizzion relativa in z", "structure_block.save_failure": "Se poeu mìa salvà la strutura '%s'", "structure_block.save_success": "Strutura salvada 'me \"%s\"", "structure_block.show_air": "Mostra bloch invisibel:", "structure_block.show_boundingbox": "Mostra i confin:", "structure_block.size": "Dimension de la strutura", "structure_block.size.x": "dimension de la strutura in x", "structure_block.size.y": "dimension de la strutura in y", "structure_block.size.z": "dimension de la strutura in z", "structure_block.size_failure": "Se poeu mìa rilevà la dimension de la strutura. Sgionta di canton cont el nom di struture corrispondente", "structure_block.size_success": "Dimension strutura rilevada per %s", "structure_block.strict": "Piazzamento statico:", "structure_block.structure_name": "Nom de la strutura", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON> in<PERSON>", "subtitles.block.amethyst_block.chime": "Ametista la cioca", "subtitles.block.amethyst_block.resonate": "Ametista risuona", "subtitles.block.anvil.destroy": "Incusgen destrugada", "subtitles.block.anvil.land": "<PERSON>us<PERSON> borlada", "subtitles.block.anvil.use": "Incusgen doperada", "subtitles.block.barrel.close": "<PERSON><PERSON> el se sarra", "subtitles.block.barrel.open": "<PERSON>l el se derva", "subtitles.block.beacon.activate": "<PERSON>iz<PERSON>", "subtitles.block.beacon.ambient": "Far el zonzona", "subtitles.block.beacon.deactivate": "<PERSON> smorzad", "subtitles.block.beacon.power_select": "Poder del far scernid", "subtitles.block.beehive.drip": "<PERSON> la gota", "subtitles.block.beehive.enter": "Ava la va denter al bisoeul", "subtitles.block.beehive.exit": "Ava la sortiss del bisoeul", "subtitles.block.beehive.shear": "S'cesore i sgrafignen", "subtitles.block.beehive.work": "Ave lavoren", "subtitles.block.bell.resonate": "Campana la resona", "subtitles.block.bell.use": "Campana la sona", "subtitles.block.big_dripleaf.tilt_down": "Foeuja-gota la se inarca", "subtitles.block.big_dripleaf.tilt_up": "Foeuja-gota la se drizza", "subtitles.block.blastfurnace.fire_crackle": "Volt- forn el s'ciopeta", "subtitles.block.brewing_stand.brew": "Lambich el buiss", "subtitles.block.bubble_column.bubble_pop": "Balle s'ciopen", "subtitles.block.bubble_column.upwards_ambient": "Corrent de balle", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON> vann su", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON> de balle", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON> s<PERSON>", "subtitles.block.button.click": "<PERSON><PERSON> schisciad", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON>", "subtitles.block.campfire.crackle": "Falò el s'ciopeta", "subtitles.block.candle.crackle": "Candila la s'ciopeta", "subtitles.block.candle.extinguish": "Candela si spegne", "subtitles.block.chest.close": "<PERSON>ull el se sarra", "subtitles.block.chest.locked": "<PERSON><PERSON> blocad", "subtitles.block.chest.open": "<PERSON>ull el se derva", "subtitles.block.chorus_flower.death": "Fior de chorus marsc", "subtitles.block.chorus_flower.grow": "Fior de chorus el cress", "subtitles.block.comparator.click": "Comparador pizzad", "subtitles.block.composter.empty": "Compostera vojada", "subtitles.block.composter.fill": "Compostera impienida", "subtitles.block.composter.ready": "Compostera la composta", "subtitles.block.conduit.activate": "Condot pizzad", "subtitles.block.conduit.ambient": "Condot el pulsa", "subtitles.block.conduit.attack.target": "Condot l'ataca", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Lam<PERSON>a di rame si spegne", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON>a di rame si accende", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON> si chiude", "subtitles.block.copper_trapdoor.open": "Bo<PERSON>la si apre", "subtitles.block.crafter.craft": "Fabbricatore in azione", "subtitles.block.crafter.fail": "Errore del fabbricatore", "subtitles.block.creaking_heart.hurt": "Cuore di scricchio b<PERSON><PERSON>", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON> in<PERSON>", "subtitles.block.creaking_heart.spawn": "Cuore di scricchio risvegliato", "subtitles.block.deadbush.idle": "<PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "Vaso decorato riempito", "subtitles.block.decorated_pot.insert_fail": "Vaso decorato oscilla", "subtitles.block.decorated_pot.shatter": "Vas el se s'cepa", "subtitles.block.dispenser.dispense": "Lavorà scompartid", "subtitles.block.dispenser.fail": "Error del scompartidor", "subtitles.block.door.toggle": "Porta la scrizza", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON> di <PERSON>zza", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> essiccato si reidrata", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> essi<PERSON><PERSON> inzuppato", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> essiccato si sente meglio", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON>", "subtitles.block.enchantment_table.use": "Banch de instriament dovrad", "subtitles.block.end_portal.spawn": "Portal de l'End avert", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON> de ender piazzad", "subtitles.block.eyeblossom.close": "Occhidea si chiude", "subtitles.block.eyeblossom.idle": "Occhidea bisbiglia", "subtitles.block.eyeblossom.open": "Occhidea si apre", "subtitles.block.fence_gate.toggle": "Restell el scrizza", "subtitles.block.fire.ambient": "Foeugh el s'ciopeta", "subtitles.block.fire.extinguish": "<PERSON><PERSON><PERSON> smorzad", "subtitles.block.firefly_bush.idle": "Luc<PERSON>le ronzano", "subtitles.block.frogspawn.hatch": "Botarana la cress", "subtitles.block.furnace.fire_crackle": "Fornas la s'ciopeta", "subtitles.block.generic.break": "<PERSON><PERSON> destrugad", "subtitles.block.generic.fall": "Caduta su blocco", "subtitles.block.generic.footsteps": "Pass", "subtitles.block.generic.hit": "Bloch el se s'cepa", "subtitles.block.generic.place": "<PERSON><PERSON> piazzad", "subtitles.block.grindstone.use": "<PERSON><PERSON>", "subtitles.block.growing_plant.crop": "Pianta podada", "subtitles.block.hanging_sign.waxed_interact_fail": "Insegna oscilla", "subtitles.block.honey_block.slide": "'Dree a sgujà in su un bloch de mel", "subtitles.block.iron_trapdoor.close": "Bussera la se sarra su", "subtitles.block.iron_trapdoor.open": "Bussera la se derva", "subtitles.block.lava.ambient": "Lava la s'ciopeta", "subtitles.block.lava.extinguish": "Zifol de lava", "subtitles.block.lever.click": "Leva <PERSON>da", "subtitles.block.note_block.note": "Bloch sonador el sona", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON> in<PERSON>", "subtitles.block.piston.move": "Piston el se moeuv", "subtitles.block.pointed_dripstone.drip_lava": "Lava la gota", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava la gota in del calderon", "subtitles.block.pointed_dripstone.drip_water": "Aqua la gota", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Aqua la gota in del calderon", "subtitles.block.pointed_dripstone.land": "Stalatite la va in toch", "subtitles.block.portal.ambient": "Frecass de portal", "subtitles.block.portal.travel": "Frecass del portal el svaniss", "subtitles.block.portal.trigger": "Frecass del portal el ven pussee fort", "subtitles.block.pressure_plate.click": "Pian a pression schisciad", "subtitles.block.pumpkin.carve": "S'cesore i tajen", "subtitles.block.redstone_torch.burnout": "Torcia de preja rossa brusada", "subtitles.block.respawn_anchor.ambient": "Frastuono dell'ancora della rinascita", "subtitles.block.respawn_anchor.charge": "Ancora de la renassida caregada", "subtitles.block.respawn_anchor.deplete": "Ancora de la renassida la se descarega", "subtitles.block.respawn_anchor.set_spawn": "Ancora de la renassida ativada", "subtitles.block.sand.idle": "<PERSON><PERSON> sa<PERSON>i", "subtitles.block.sand.wind": "<PERSON><PERSON>", "subtitles.block.sculk.charge": "Sculk el buiss", "subtitles.block.sculk.spread": "Sculk el se spantega", "subtitles.block.sculk_catalyst.bloom": "Catalizador de sculk el spantega", "subtitles.block.sculk_sensor.clicking": "Sensor sculk el se pizza", "subtitles.block.sculk_sensor.clicking_stop": "Sensor sculk el se smorza", "subtitles.block.sculk_shrieker.shriek": "Sculk vosador el vosa", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> el se sarra su", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> el se derva", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> oscilla", "subtitles.block.smithing_table.use": "Tavol del ferrer doperad", "subtitles.block.smoker.smoke": "Afumegador l'afumega", "subtitles.block.sniffer_egg.crack": "Oeuv de nasador el se crena", "subtitles.block.sniffer_egg.hatch": "Oeuv de nasador el se derva", "subtitles.block.sniffer_egg.plop": "Fiutatore depone uovo", "subtitles.block.sponge.absorb": "Sponga la ciucia", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON> borlen", "subtitles.block.trapdoor.close": "<PERSON><PERSON><PERSON> si chiude", "subtitles.block.trapdoor.open": "Bo<PERSON>la si apre", "subtitles.block.trapdoor.toggle": "Bussera la scrizza", "subtitles.block.trial_spawner.about_to_spawn_item": "Oggetto infausto si prepara", "subtitles.block.trial_spawner.ambient": "Generatore della sfida crepita", "subtitles.block.trial_spawner.ambient_charged": "Crepito infausto", "subtitles.block.trial_spawner.ambient_ominous": "Crepito infausto", "subtitles.block.trial_spawner.charge_activate": "Presagio travolge generatore della sfida", "subtitles.block.trial_spawner.close_shutter": "Generatore della sfida si chiude", "subtitles.block.trial_spawner.detect_player": "Generatore della sfida caricato", "subtitles.block.trial_spawner.eject_item": "Oggetto della sfida espulso", "subtitles.block.trial_spawner.ominous_activate": "Generatore della sfida reso infausto", "subtitles.block.trial_spawner.open_shutter": "Generatore della sfida si apre", "subtitles.block.trial_spawner.spawn_item": "Oggetto infausto rila<PERSON>to", "subtitles.block.trial_spawner.spawn_item_begin": "Oggetto infausto appare", "subtitles.block.trial_spawner.spawn_mob": "Creatura della sfida generata", "subtitles.block.tripwire.attach": "Fil trapola el se liga", "subtitles.block.tripwire.click": "Fil trapola el se pizza", "subtitles.block.tripwire.detach": "Fil trapola el se destaca", "subtitles.block.vault.activate": "Cassaforte si accende", "subtitles.block.vault.ambient": "Cassaforte crepita", "subtitles.block.vault.close_shutter": "Cassaforte si chiude", "subtitles.block.vault.deactivate": "Cassaforte si spegne", "subtitles.block.vault.eject_item": "Cassaforte espelle un oggetto", "subtitles.block.vault.insert_item": "Cassaforte si sblocca", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON><PERSON> respinge og<PERSON>to", "subtitles.block.vault.open_shutter": "Cassaforte si apre", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON><PERSON> respinge giocatore", "subtitles.block.water.ambient": "Aqua la corr via", "subtitles.block.wet_sponge.dries": "Spugna si asciuga", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "Liber instriad metud", "subtitles.chiseled_bookshelf.take": "Liber ciapad su", "subtitles.chiseled_bookshelf.take_enchanted": "Liber instriad ciapad su", "subtitles.enchant.thorns.hit": "<PERSON><PERSON>po<PERSON>", "subtitles.entity.allay.ambient_with_item": "Allay el cacia", "subtitles.entity.allay.ambient_without_item": "Allay el desidera", "subtitles.entity.allay.death": "Allay el moeur", "subtitles.entity.allay.hurt": "Allay ferid", "subtitles.entity.allay.item_given": "<PERSON>ay el <PERSON>", "subtitles.entity.allay.item_taken": "Allay el cata su", "subtitles.entity.allay.item_thrown": "Allay el tira", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.armadillo.brush": "S<PERSON><PERSON> spazzolata", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> muore", "subtitles.entity.armadillo.eat": "Armadillo mangia", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> ferito", "subtitles.entity.armadillo.hurt_reduced": "Armadillo si arrotola", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON> at<PERSON>ra", "subtitles.entity.armadillo.peek": "Armadillo sbircia", "subtitles.entity.armadillo.roll": "Armadillo si arrotola", "subtitles.entity.armadillo.scute_drop": "Armadillo perde una squama", "subtitles.entity.armadillo.unroll_finish": "Armadillo si srotola", "subtitles.entity.armadillo.unroll_start": "Armadillo sbircia", "subtitles.entity.armor_stand.fall": "Suport per armadure el borla", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON> la colpiss", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON><PERSON> colpid", "subtitles.entity.arrow.shoot": "<PERSON><PERSON>zza tira<PERSON>", "subtitles.entity.axolotl.attack": "Assolot l'ataca", "subtitles.entity.axolotl.death": "Assolot el moeur", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON>ot ferid", "subtitles.entity.axolotl.idle_air": "Assolot el canta", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON> canta", "subtitles.entity.axolotl.splash": "Assolot el sguazza", "subtitles.entity.axolotl.swim": "Assolot el noda", "subtitles.entity.bat.ambient": "Tegnoeula la scrizza", "subtitles.entity.bat.death": "Tegnoeura la moeur", "subtitles.entity.bat.hurt": "<PERSON>gnoeur<PERSON> ferida", "subtitles.entity.bat.takeoff": "Tegnoeura la sgola", "subtitles.entity.bee.ambient": "Ava la zonzona", "subtitles.entity.bee.death": "Ava la moeur", "subtitles.entity.bee.hurt": "Ava ferida", "subtitles.entity.bee.loop": "<PERSON><PERSON> ronza", "subtitles.entity.bee.loop_aggressive": "Ava la zonzona inversada", "subtitles.entity.bee.pollinate": "Ava la zonzona contenta", "subtitles.entity.bee.sting": "Ava la sponsg", "subtitles.entity.blaze.ambient": "Blaze el respira", "subtitles.entity.blaze.burn": "Blaze el s'ciopeta", "subtitles.entity.blaze.death": "Blaze el moeur", "subtitles.entity.blaze.hurt": "Blaze ferid", "subtitles.entity.blaze.shoot": "Blaze l'ataca", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON>à", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON><PERSON> scric<PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON> muore", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON><PERSON> ferito", "subtitles.entity.breeze.charge": "Brezza carica", "subtitles.entity.breeze.death": "Brezza muore", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> deflette", "subtitles.entity.breeze.hurt": "Brezza ferita", "subtitles.entity.breeze.idle_air": "Brezza vola", "subtitles.entity.breeze.idle_ground": "Brezza frulla", "subtitles.entity.breeze.inhale": "Brezza inspira", "subtitles.entity.breeze.jump": "Brezza salta", "subtitles.entity.breeze.land": "Brezza atterra", "subtitles.entity.breeze.shoot": "Brezza spara", "subtitles.entity.breeze.slide": "Brezza scorre", "subtitles.entity.breeze.whirl": "Brezza turbina", "subtitles.entity.breeze.wind_burst": "Carica di vento scoppia", "subtitles.entity.camel.ambient": "Camell el rogna", "subtitles.entity.camel.dash": "Camell el corr", "subtitles.entity.camel.dash_ready": "Dromedario si riprende", "subtitles.entity.camel.death": "Camel el moeur", "subtitles.entity.camel.eat": "Camell el mangia", "subtitles.entity.camel.hurt": "Camell ferid", "subtitles.entity.camel.saddle": "Sella equipagiada", "subtitles.entity.camel.sit": "Camell el se seta sgiò", "subtitles.entity.camel.stand": "Camell el se leva su", "subtitles.entity.camel.step": "Camell el camina", "subtitles.entity.camel.step_sand": "Camell el camina in su la sabia", "subtitles.entity.cat.ambient": "Gat el mogna", "subtitles.entity.cat.beg_for_food": "Gat el ciama de mangià", "subtitles.entity.cat.death": "Gat el moeur", "subtitles.entity.cat.eat": "Gat el mangia", "subtitles.entity.cat.hiss": "Gat el bofa", "subtitles.entity.cat.hurt": "Gat ferid", "subtitles.entity.cat.purr": "Gat el fronfrona", "subtitles.entity.chicken.ambient": "Pollaster el rogna", "subtitles.entity.chicken.death": "Pollaster el moeur", "subtitles.entity.chicken.egg": "Pollaster el fa l'oeuv", "subtitles.entity.chicken.hurt": "Pollaster ferid", "subtitles.entity.cod.death": "Merluzz el moeur", "subtitles.entity.cod.flop": "Merluzz el solta", "subtitles.entity.cod.hurt": "Merluzz ferid", "subtitles.entity.cow.ambient": "Vaca la mugiss", "subtitles.entity.cow.death": "Vaca la moeur", "subtitles.entity.cow.hurt": "Vaca ferida", "subtitles.entity.cow.milk": "Vaca a l'è molgiada", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON><PERSON> osserva", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON><PERSON> scricchiola", "subtitles.entity.creaking.attack": "Scricchio attacca", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON> si calma", "subtitles.entity.creaking.death": "Scricchio si sgretola", "subtitles.entity.creaking.freeze": "Sc<PERSON>chio si ferma", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON>o appare", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON> colpito", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON>o si contorce", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON>o si muove", "subtitles.entity.creeper.death": "Creeper el moeur", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.creeper.primed": "Creeper el zifola", "subtitles.entity.dolphin.ambient": "Delfin el cria", "subtitles.entity.dolphin.ambient_water": "Delfin el zifola", "subtitles.entity.dolphin.attack": "Delfin l'ataca", "subtitles.entity.dolphin.death": "Delfin el moeur", "subtitles.entity.dolphin.eat": "Delfin el mangia", "subtitles.entity.dolphin.hurt": "Delfin ferid", "subtitles.entity.dolphin.jump": "Delfin el salta", "subtitles.entity.dolphin.play": "Delfin el sgioeuga", "subtitles.entity.dolphin.splash": "Delfin el sguazza", "subtitles.entity.dolphin.swim": "Delfin el noda", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> el raggia", "subtitles.entity.donkey.angry": "<PERSON><PERSON> el raggia", "subtitles.entity.donkey.chest": "Baull de l'asen equipagiad", "subtitles.entity.donkey.death": "<PERSON><PERSON> el moeur", "subtitles.entity.donkey.eat": "<PERSON><PERSON> el mangia", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> ferid", "subtitles.entity.donkey.jump": "<PERSON><PERSON> salta", "subtitles.entity.drowned.ambient": "Negad el surbuiss", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "Negad el moeur", "subtitles.entity.drowned.hurt": "<PERSON><PERSON>ad ferid", "subtitles.entity.drowned.shoot": "Negad el tira el trident", "subtitles.entity.drowned.step": "Negad el camina", "subtitles.entity.drowned.swim": "Negad el noda", "subtitles.entity.egg.throw": "Oeuv tirad", "subtitles.entity.elder_guardian.ambient": "Guardian antigh el se lamenta", "subtitles.entity.elder_guardian.ambient_land": "Guardian antigh el cria", "subtitles.entity.elder_guardian.curse": "Guardian pussee vegg el malediss", "subtitles.entity.elder_guardian.death": "Guardian antigh el moeur", "subtitles.entity.elder_guardian.flop": "Guardian antigh el solta", "subtitles.entity.elder_guardian.hurt": "Guardian antigh ferid", "subtitles.entity.ender_dragon.ambient": "Dragon el rusgiss", "subtitles.entity.ender_dragon.death": "Dragon el moeur", "subtitles.entity.ender_dragon.flap": "Dragon el sgola", "subtitles.entity.ender_dragon.growl": "Dragon el rogna", "subtitles.entity.ender_dragon.hurt": "Dragon ferid", "subtitles.entity.ender_dragon.shoot": "Dragon l'ataca", "subtitles.entity.ender_eye.death": "Oeu<PERSON> de ender el borla sgiò", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON> de ender tirad", "subtitles.entity.ender_pearl.throw": "Perla del Bord tirada", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> el fa \"vwoop\"", "subtitles.entity.enderman.death": "<PERSON><PERSON> el moeur", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> ferid", "subtitles.entity.enderman.scream": "<PERSON><PERSON> urla", "subtitles.entity.enderman.stare": "<PERSON><PERSON> el cria", "subtitles.entity.enderman.teleport": "<PERSON><PERSON> el se teleporta", "subtitles.entity.endermite.ambient": "Endermite la strusa", "subtitles.entity.endermite.death": "Endermite la moeur", "subtitles.entity.endermite.hurt": "Endermite ferid", "subtitles.entity.evoker.ambient": "Evocador el mormora", "subtitles.entity.evoker.cast_spell": "Evocador el tira striozz", "subtitles.entity.evoker.celebrate": "Evocador el fa festa", "subtitles.entity.evoker.death": "Evocador el moeur", "subtitles.entity.evoker.hurt": "Evocador ferid", "subtitles.entity.evoker.prepare_attack": "Evocador el prepara un atach", "subtitles.entity.evoker.prepare_summon": "Evocador el prepara un'evocazzion", "subtitles.entity.evoker.prepare_wololo": "Evocador el prepara un striozz", "subtitles.entity.evoker_fangs.attack": "Sgagnada di dent de presa", "subtitles.entity.experience_orb.pickup": "Esperienza guadagnada", "subtitles.entity.firework_rocket.blast": "Razz pirotecnegh el s'ciopa", "subtitles.entity.firework_rocket.launch": "Razz pirotecnegh tirad", "subtitles.entity.firework_rocket.twinkle": "Razz pirotecnegh el sberlusa", "subtitles.entity.fish.swim": "<PERSON><PERSON><PERSON> d'<PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "Galegiant recuperad", "subtitles.entity.fishing_bobber.splash": "Galegiant el sbrofa", "subtitles.entity.fishing_bobber.throw": "Galegiant tirad", "subtitles.entity.fox.aggro": "Golp la se inversa", "subtitles.entity.fox.ambient": "Golp la sguagniss", "subtitles.entity.fox.bite": "Golp la sgagna", "subtitles.entity.fox.death": "Golp la moeur", "subtitles.entity.fox.eat": "Golp la mangia", "subtitles.entity.fox.hurt": "Golp ferida", "subtitles.entity.fox.screech": "Golp la sguagniss", "subtitles.entity.fox.sleep": "Golp la ronfa", "subtitles.entity.fox.sniff": "Gol<PERSON> la usma", "subtitles.entity.fox.spit": "Golp la spuva", "subtitles.entity.fox.teleport": "Golp la se teleporta", "subtitles.entity.frog.ambient": "Rana la canta", "subtitles.entity.frog.death": "<PERSON> la moeur", "subtitles.entity.frog.eat": "Rana la mangia", "subtitles.entity.frog.hurt": "Rana ferida", "subtitles.entity.frog.lay_spawn": "Rana la fa oeuv", "subtitles.entity.frog.long_jump": "<PERSON> la salta", "subtitles.entity.generic.big_fall": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.burn": "'Dree a brusà", "subtitles.entity.generic.death": "'Dree a morì", "subtitles.entity.generic.drink": "'Dree a bever", "subtitles.entity.generic.eat": "'Dree a mangià", "subtitles.entity.generic.explode": "Esplosion", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON> smorzad", "subtitles.entity.generic.hurt": "Un quaicoss ferid", "subtitles.entity.generic.small_fall": "Crodada piscinina", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON> d'aqua", "subtitles.entity.generic.swim": "'Dree a nodà", "subtitles.entity.generic.wind_burst": "Carica di vento scoppia", "subtitles.entity.ghast.ambient": "Ghast el caragna", "subtitles.entity.ghast.death": "G<PERSON><PERSON> el moeur", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.ghast.shoot": "Ghast l'ataca", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> tuba", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> muore", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> ferito", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON> appare", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON> s<PERSON>a impienida", "subtitles.entity.glow_item_frame.break": "<PERSON><PERSON><PERSON> s<PERSON>a destrugada", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON> s<PERSON>a piazzada", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON> s<PERSON> vojada", "subtitles.entity.glow_item_frame.rotate_item": "Oget in cornis sberlusenta sgirad", "subtitles.entity.glow_squid.ambient": "Calamar sber<PERSON>ent el noda", "subtitles.entity.glow_squid.death": "Calamar sberlusent el moeur", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON> sber<PERSON> ferid", "subtitles.entity.glow_squid.squirt": "Calamar sberlusent el sbrofa incioster", "subtitles.entity.goat.ambient": "Cavra la bela", "subtitles.entity.goat.death": "Cavra la moeur", "subtitles.entity.goat.eat": "Cavra la maja", "subtitles.entity.goat.horn_break": "Corn de cavra de destaca", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> ferida", "subtitles.entity.goat.long_jump": "Cavra la salta", "subtitles.entity.goat.milk": "Cavra l'è molgiada", "subtitles.entity.goat.prepare_ram": "Cavra la scalza", "subtitles.entity.goat.ram_impact": "Cavra la carega", "subtitles.entity.goat.screaming.ambient": "Cavra la vosa", "subtitles.entity.goat.step": "Cavra la camina", "subtitles.entity.guardian.ambient": "Guardian el se lamenta", "subtitles.entity.guardian.ambient_land": "Guardian el cria", "subtitles.entity.guardian.attack": "Guardian l'ataca", "subtitles.entity.guardian.death": "Guardian el moeur", "subtitles.entity.guardian.flop": "Guardian el solta", "subtitles.entity.guardian.hurt": "Guardian ferid", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON>t allegro canticchia", "subtitles.entity.happy_ghast.death": "Ghast allegro muore", "subtitles.entity.happy_ghast.equip": "Imbracatura allacciata", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON>t allegro è pronto", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON>t allegro si arresta", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON>t allegro ferito", "subtitles.entity.happy_ghast.unequip": "Imbracatura slacciata", "subtitles.entity.hoglin.ambient": "Hoglin el rogna", "subtitles.entity.hoglin.angry": "Hoglin el rogna inversad", "subtitles.entity.hoglin.attack": "Ho<PERSON>n l'ataca", "subtitles.entity.hoglin.converted_to_zombified": "Hoglin el se trasforma in zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON>n el moeur", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n ferid", "subtitles.entity.hoglin.retreat": "Ho<PERSON>n el va indree", "subtitles.entity.hoglin.step": "Hoglin el camina", "subtitles.entity.horse.ambient": "Cavall el ronscina", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> nitri<PERSON>", "subtitles.entity.horse.armor": "Bardadura fornida del necessari", "subtitles.entity.horse.breathe": "Cavall el respira", "subtitles.entity.horse.death": "Cavall el moeur", "subtitles.entity.horse.eat": "Cavall el mangia", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.horse.jump": "Cavall el salta", "subtitles.entity.horse.saddle": "Sella fornida del necessari", "subtitles.entity.husk.ambient": "Zombi del desert el se lamenta", "subtitles.entity.husk.converted_to_zombie": "Zombi del desert el se trasforma in zombi", "subtitles.entity.husk.death": "Zombi del desert el moeur", "subtitles.entity.husk.hurt": "Zombi del desert ferid", "subtitles.entity.illusioner.ambient": "Illosor el mormora", "subtitles.entity.illusioner.cast_spell": "Illusionista el tira striozz", "subtitles.entity.illusioner.death": "Illusionista el moeur", "subtitles.entity.illusioner.hurt": "Illusionista ferid", "subtitles.entity.illusioner.mirror_move": "Illusionista el se teleporta", "subtitles.entity.illusioner.prepare_blindness": "Illusionista el prepara l'inorbiment", "subtitles.entity.illusioner.prepare_mirror": "Illusionista el prepara illusion", "subtitles.entity.iron_golem.attack": "Golem de ferr l'ataca", "subtitles.entity.iron_golem.damage": "Golem de ferr el se s'cepa", "subtitles.entity.iron_golem.death": "Golem de ferr el moeur", "subtitles.entity.iron_golem.hurt": "Go<PERSON> de ferr ferid", "subtitles.entity.iron_golem.repair": "Golem de ferr giustad", "subtitles.entity.item.break": "Lavorà destrugad", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON> impienida", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON> des<PERSON>uga<PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.item_frame.rotate_item": "Oget incornisad sgirad", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.place": "Guinzaglio legato", "subtitles.entity.lightning_bolt.impact": "Fulmen vegnud sgiò", "subtitles.entity.lightning_bolt.thunder": "Tronn el rintrona", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON> el bela inversad", "subtitles.entity.llama.chest": "Baull del lama fornid del necessari", "subtitles.entity.llama.death": "<PERSON> el moeur", "subtitles.entity.llama.eat": "<PERSON> el mangia", "subtitles.entity.llama.hurt": "<PERSON> ferid", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON> el camina", "subtitles.entity.llama.swag": "Lama l'è decorad", "subtitles.entity.magma_cube.death": "Cub de magma el moeur", "subtitles.entity.magma_cube.hurt": "Cub de magma ferid", "subtitles.entity.magma_cube.squish": "Cub de magma el salta", "subtitles.entity.minecart.inside": "Vagonetto stride", "subtitles.entity.minecart.inside_underwater": "Vagonetto stride sott'acqua", "subtitles.entity.minecart.riding": "Carrell minerari el se moeuv", "subtitles.entity.mooshroom.convert": "Vaca-fonsg la se trasforma", "subtitles.entity.mooshroom.eat": "Vaca-fonsg la mangia", "subtitles.entity.mooshroom.milk": "Vaca-fonsg a l'è molgiada", "subtitles.entity.mooshroom.suspicious_milk": "Vaca-fonsg a l'è molgiuda con sospet", "subtitles.entity.mule.ambient": "Mull el ragia", "subtitles.entity.mule.angry": "Mul el ronscina", "subtitles.entity.mule.chest": "Baull del mull fornid del necessari", "subtitles.entity.mule.death": "Mul el moeur", "subtitles.entity.mule.eat": "Mul el mangia", "subtitles.entity.mule.hurt": "Mul ferid", "subtitles.entity.mule.jump": "<PERSON><PERSON>a", "subtitles.entity.painting.break": "Quader destrugad", "subtitles.entity.painting.place": "<PERSON>uader piazzad", "subtitles.entity.panda.aggressive_ambient": "Panda el bofa", "subtitles.entity.panda.ambient": "Panda el sbanfa", "subtitles.entity.panda.bite": "Panda el sgagna", "subtitles.entity.panda.cant_breed": "Panda el sguagniss", "subtitles.entity.panda.death": "Panda el moeur", "subtitles.entity.panda.eat": "Panda el mangia", "subtitles.entity.panda.hurt": "Panda ferid", "subtitles.entity.panda.pre_sneeze": "Nas del panda el prurisna", "subtitles.entity.panda.sneeze": "Panda el sternuda", "subtitles.entity.panda.step": "Panda el camina", "subtitles.entity.panda.worried_ambient": "Panda el mogna", "subtitles.entity.parrot.ambient": "Papagall el parla", "subtitles.entity.parrot.death": "Papa<PERSON> el moeur", "subtitles.entity.parrot.eats": "Papagall el mangia", "subtitles.entity.parrot.fly": "Papagall el sgorata", "subtitles.entity.parrot.hurts": "<PERSON><PERSON> ferid", "subtitles.entity.parrot.imitate.blaze": "Papagall el respira", "subtitles.entity.parrot.imitate.bogged": "Pappagallo scricchiola", "subtitles.entity.parrot.imitate.breeze": "Pappagallo frulla", "subtitles.entity.parrot.imitate.creaking": "Pappagallo scricchiola", "subtitles.entity.parrot.imitate.creeper": "Papagall el zifola", "subtitles.entity.parrot.imitate.drowned": "Papagall el surbuiss", "subtitles.entity.parrot.imitate.elder_guardian": "Papagall el se lamenta", "subtitles.entity.parrot.imitate.ender_dragon": "Papagall el rusgiss", "subtitles.entity.parrot.imitate.endermite": "Papagall el strusa", "subtitles.entity.parrot.imitate.evoker": "Papagall el mormora", "subtitles.entity.parrot.imitate.ghast": "Papagall el caragna", "subtitles.entity.parrot.imitate.guardian": "Papagall el se lamenta", "subtitles.entity.parrot.imitate.hoglin": "Papagall el rogna", "subtitles.entity.parrot.imitate.husk": "Papagall el sguagniss", "subtitles.entity.parrot.imitate.illusioner": "Papagall el mormora", "subtitles.entity.parrot.imitate.magma_cube": "Papa<PERSON> el salta", "subtitles.entity.parrot.imitate.phantom": "Papagall el scrizza", "subtitles.entity.parrot.imitate.piglin": "Papagall el rogna", "subtitles.entity.parrot.imitate.piglin_brute": "Papagall el rogna", "subtitles.entity.parrot.imitate.pillager": "Papagall el mormora", "subtitles.entity.parrot.imitate.ravager": "Papagall el rogna", "subtitles.entity.parrot.imitate.shulker": "Papagall el varda", "subtitles.entity.parrot.imitate.silverfish": "Papagall el zifola", "subtitles.entity.parrot.imitate.skeleton": "Papagall el donda", "subtitles.entity.parrot.imitate.slime": "Papa<PERSON> el salta", "subtitles.entity.parrot.imitate.spider": "Papagall el zifola", "subtitles.entity.parrot.imitate.stray": "Papagall el donda", "subtitles.entity.parrot.imitate.vex": "Papagall el mal-trata", "subtitles.entity.parrot.imitate.vindicator": "Papagall el barbota", "subtitles.entity.parrot.imitate.warden": "Papagall el se lamenta", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON> el rid sot-via", "subtitles.entity.parrot.imitate.wither": "Papa<PERSON> el va in su tute i furie", "subtitles.entity.parrot.imitate.wither_skeleton": "Papagall el donda", "subtitles.entity.parrot.imitate.zoglin": "Papagall el rogna", "subtitles.entity.parrot.imitate.zombie": "Papagall el sguagniss", "subtitles.entity.parrot.imitate.zombie_villager": "Papagall el sguagniss", "subtitles.entity.phantom.ambient": "Fantasma el scrizza", "subtitles.entity.phantom.bite": "Fastasma el mordigna", "subtitles.entity.phantom.death": "Fantasma el moeur", "subtitles.entity.phantom.flap": "Fantasma el sbat i al", "subtitles.entity.phantom.hurt": "Fantasma ferid", "subtitles.entity.phantom.swoop": "Fantasma in picada", "subtitles.entity.pig.ambient": "Porcell el rogna", "subtitles.entity.pig.death": "Po<PERSON>ell el moeur", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.pig.saddle": "Sella equipagiada", "subtitles.entity.piglin.admiring_item": "<PERSON>lin el contempla un lavorà", "subtitles.entity.piglin.ambient": "<PERSON>lin el rogna", "subtitles.entity.piglin.angry": "Piglin el rogna inversad", "subtitles.entity.piglin.celebrate": "<PERSON>lin el salta de contentezza", "subtitles.entity.piglin.converted_to_zombified": "<PERSON>lin el se zombifega", "subtitles.entity.piglin.death": "<PERSON>lin el moeur", "subtitles.entity.piglin.hurt": "<PERSON>lin ferid", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> el rogna con invidia", "subtitles.entity.piglin.retreat": "Piglin el va indree", "subtitles.entity.piglin.step": "Piglin el camina", "subtitles.entity.piglin_brute.ambient": "<PERSON>lin brut el rogna", "subtitles.entity.piglin_brute.angry": "Piglin brut el rogna inversad", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin brut el se zombifega", "subtitles.entity.piglin_brute.death": "<PERSON>lin brut el moeur", "subtitles.entity.piglin_brute.hurt": "Piglin brut ferid", "subtitles.entity.piglin_brute.step": "Piglin brut el camina", "subtitles.entity.pillager.ambient": "Botinador el mormora", "subtitles.entity.pillager.celebrate": "Botinador el salta de contentezza", "subtitles.entity.pillager.death": "Botinador el moeur", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.player.attack.crit": "Atach critegh", "subtitles.entity.player.attack.knockback": "Atach con contra-colp", "subtitles.entity.player.attack.strong": "Atach fort", "subtitles.entity.player.attack.sweep": "<PERSON><PERSON> destrugador", "subtitles.entity.player.attack.weak": "Atach fiap", "subtitles.entity.player.burp": "<PERSON><PERSON>", "subtitles.entity.player.death": "Sgiugador el moeur", "subtitles.entity.player.freeze_hurt": "Sgi<PERSON><PERSON> s<PERSON>", "subtitles.entity.player.hurt": "Sgiugador ferid", "subtitles.entity.player.hurt_drown": "'Dree a negà", "subtitles.entity.player.hurt_on_fire": "'Dree a brusà", "subtitles.entity.player.levelup": "El sgioeugador el va in alt de nivell", "subtitles.entity.player.teleport": "Giocatore si teletrasporta", "subtitles.entity.polar_bear.ambient": "Ors polar el se lamenta", "subtitles.entity.polar_bear.ambient_baby": "Fiolin de ors polar el se lamenta", "subtitles.entity.polar_bear.death": "Ors polar el moeur", "subtitles.entity.polar_bear.hurt": "Ors polar ferid", "subtitles.entity.polar_bear.warning": "Ors polar el rusgiss", "subtitles.entity.potion.splash": "<PERSON><PERSON>ia trada a toch", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON> tirada", "subtitles.entity.puffer_fish.blow_out": "Pess-balla el se disgonfia", "subtitles.entity.puffer_fish.blow_up": "Pess-balla el se s'cionfa", "subtitles.entity.puffer_fish.death": "P<PERSON>-balla el moeur", "subtitles.entity.puffer_fish.flop": "Pess-balla el se trà de chì e de là", "subtitles.entity.puffer_fish.hurt": "Pess-balla ferid", "subtitles.entity.puffer_fish.sting": "Pess-balla el spong", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON> el vosata", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>'at<PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON> el moeur", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> ferid", "subtitles.entity.rabbit.jump": "<PERSON><PERSON> el salta", "subtitles.entity.ravager.ambient": "S'cepa-tut el rogna", "subtitles.entity.ravager.attack": "S'cepa-tut el sgagna", "subtitles.entity.ravager.celebrate": "S'cepa-tut el salta de contentezza", "subtitles.entity.ravager.death": "S'cepa-tut el moeur", "subtitles.entity.ravager.hurt": "S'cepa-tut ferid", "subtitles.entity.ravager.roar": "S'cepa-tut el rusgiss", "subtitles.entity.ravager.step": "S'cepa-tut el camina", "subtitles.entity.ravager.stunned": "S'cepa-tut instornid", "subtitles.entity.salmon.death": "Salmon el moeur", "subtitles.entity.salmon.flop": "Salmon el se trà de chì e de là", "subtitles.entity.salmon.hurt": "Salmon ferid", "subtitles.entity.sheep.ambient": "Pegora la bela", "subtitles.entity.sheep.death": "Pegora la moeur", "subtitles.entity.sheep.hurt": "Pegora ferida", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> el varda", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> el se sarra su", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> el moeur", "subtitles.entity.shulker.hurt": "<PERSON>lk<PERSON> ferid", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> el se derva", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> l'ataca", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> el se teleporta", "subtitles.entity.shulker_bullet.hit": "Projetil del shulker el s'ciopa", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON>ker distrut", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON> d'<PERSON><PERSON> el zifola", "subtitles.entity.silverfish.death": "<PERSON><PERSON> d'ars<PERSON> el moeur", "subtitles.entity.silverfish.hurt": "<PERSON>ess d'ars<PERSON> ferid", "subtitles.entity.skeleton.ambient": "Scheleter el scrizza", "subtitles.entity.skeleton.converted_to_stray": "Schelter el se trasforma in schelter di nev", "subtitles.entity.skeleton.death": "Sc<PERSON><PERSON> el moeur", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.skeleton.shoot": "Schelter l'ataca", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> schelter el cria", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON> schelter el moeur", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON> schelter ferid", "subtitles.entity.skeleton_horse.jump_water": "<PERSON><PERSON><PERSON> scheletro salta", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON><PERSON> schelter el noda", "subtitles.entity.slime.attack": "Slime l'ataca", "subtitles.entity.slime.death": "Slime el moeur", "subtitles.entity.slime.hurt": "Slime ferid", "subtitles.entity.slime.squish": "Slime la salta", "subtitles.entity.sniffer.death": "Na<PERSON><PERSON> el moeur", "subtitles.entity.sniffer.digging": "Na<PERSON>or el sgarla", "subtitles.entity.sniffer.digging_stop": "Na<PERSON>or el se leva su", "subtitles.entity.sniffer.drop_seed": "Nasador el troeuva una somenza", "subtitles.entity.sniffer.eat": "Nasador el mangia", "subtitles.entity.sniffer.egg_crack": "Oeuv de nasador el se crena", "subtitles.entity.sniffer.egg_hatch": "Oeuv de nasador el se derva", "subtitles.entity.sniffer.happy": "Fiutatore si rallegra", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.sniffer.idle": "Na<PERSON>or el rogna", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> fi<PERSON>", "subtitles.entity.sniffer.searching": "Fiutatore cerca", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "Nasador el camina", "subtitles.entity.snow_golem.death": "Golem de fioca el moeur", "subtitles.entity.snow_golem.hurt": "Golem de fioca ferid", "subtitles.entity.snowball.throw": "Balla de fioca tirada", "subtitles.entity.spider.ambient": "Ragn el zifola", "subtitles.entity.spider.death": "Ragn el moeur", "subtitles.entity.spider.hurt": "<PERSON>gn ferid", "subtitles.entity.squid.ambient": "Calamar el noda", "subtitles.entity.squid.death": "Calamar el moeur", "subtitles.entity.squid.hurt": "Calamar ferid", "subtitles.entity.squid.squirt": "Calamar el sbrofa incioster", "subtitles.entity.stray.ambient": "Schelter di nev el scrizza", "subtitles.entity.stray.death": "Schelter di nev el moeur", "subtitles.entity.stray.hurt": "Sc<PERSON><PERSON> di nev ferid", "subtitles.entity.strider.death": "Strider el moeur", "subtitles.entity.strider.eat": "Strider el mangia", "subtitles.entity.strider.happy": "Strider el cria", "subtitles.entity.strider.hurt": "Strider ferid", "subtitles.entity.strider.idle": "Strider el cipa", "subtitles.entity.strider.retreat": "Strider el va indree", "subtitles.entity.tadpole.death": "Botarana la moeur", "subtitles.entity.tadpole.flop": "Botarana la cioca", "subtitles.entity.tadpole.grow_up": "Botarana la cress", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON> ferida", "subtitles.entity.tnt.primed": "<PERSON><PERSON>t pizzada", "subtitles.entity.tropical_fish.death": "Pess tropegal el moeur", "subtitles.entity.tropical_fish.flop": "Pess tropegal el se trà de chì e de là", "subtitles.entity.tropical_fish.hurt": "Pess tropegal ferid", "subtitles.entity.turtle.ambient_land": "Bissa scudellera la cipa", "subtitles.entity.turtle.death": "Bissa scudellera la moeur", "subtitles.entity.turtle.death_baby": "Bissa scudellera piscinina la moeur", "subtitles.entity.turtle.egg_break": "Oeuv de bissa-scullera 'l se s'cepa", "subtitles.entity.turtle.egg_crack": "Oeuv de bissa-scullera 'l se crena", "subtitles.entity.turtle.egg_hatch": "Oeuv de bissa-scullera 'l se derva", "subtitles.entity.turtle.hurt": "Bissa scudellera ferida", "subtitles.entity.turtle.hurt_baby": "Bissa scudellera piscinina ferida", "subtitles.entity.turtle.lay_egg": "Bissa scudellera la fa l'oeuv", "subtitles.entity.turtle.shamble": "Bissa scudellera la squanquana", "subtitles.entity.turtle.shamble_baby": "Bissa scudellera piscinina la squanquana", "subtitles.entity.turtle.swim": "Bissa scudellera la noda", "subtitles.entity.vex.ambient": "Spirit el tormenta", "subtitles.entity.vex.charge": "Spirit el cria", "subtitles.entity.vex.death": "Spirit el moeur", "subtitles.entity.vex.hurt": "Spirit ferid", "subtitles.entity.villager.ambient": "Paisan el mormora", "subtitles.entity.villager.celebrate": "Paisan el fa festa", "subtitles.entity.villager.death": "Paisan el moeur", "subtitles.entity.villager.hurt": "Paisan ferid", "subtitles.entity.villager.no": "Paisan el refuda", "subtitles.entity.villager.trade": "Paisan el scambia", "subtitles.entity.villager.work_armorer": "Corazzer el lavora", "subtitles.entity.villager.work_butcher": "Becher el lavora", "subtitles.entity.villager.work_cartographer": "Cartograf el lavora", "subtitles.entity.villager.work_cleric": "Ceregh el lavora", "subtitles.entity.villager.work_farmer": "Paisan el lavora", "subtitles.entity.villager.work_fisherman": "Pescador el lavora", "subtitles.entity.villager.work_fletcher": "Ferrer de flizze el lavora", "subtitles.entity.villager.work_leatherworker": "Lavorador de coram el lavora", "subtitles.entity.villager.work_librarian": "Bibliotecari el lavora", "subtitles.entity.villager.work_mason": "Magut el lavora", "subtitles.entity.villager.work_shepherd": "<PERSON> el lavora", "subtitles.entity.villager.work_toolsmith": "Ferrer el lavora", "subtitles.entity.villager.work_weaponsmith": "Armaroeul el lavora", "subtitles.entity.villager.yes": "Paisan l'aceta", "subtitles.entity.vindicator.ambient": "Vendegador el barbota", "subtitles.entity.vindicator.celebrate": "Vendegador el fa festa", "subtitles.entity.vindicator.death": "Vendegador el moeur", "subtitles.entity.vindicator.hurt": "Vendegador ferid", "subtitles.entity.wandering_trader.ambient": "Mercant ramingh el mormora", "subtitles.entity.wandering_trader.death": "Mercant ramingh el moeur", "subtitles.entity.wandering_trader.disappeared": "Mercant ramingh el descompar", "subtitles.entity.wandering_trader.drink_milk": "Mercant ramingh el bev lacc", "subtitles.entity.wandering_trader.drink_potion": "Mercant ramingh el bev una pozzion", "subtitles.entity.wandering_trader.hurt": "Mercant ramingh ferid", "subtitles.entity.wandering_trader.no": "Mercant ramingh el refuda", "subtitles.entity.wandering_trader.reappeared": "Mercant ramingh el compar", "subtitles.entity.wandering_trader.trade": "Mercant ramingh el scambia", "subtitles.entity.wandering_trader.yes": "Mercant ramingh l'aceta", "subtitles.entity.warden.agitated": "Warden el se lamenta inversad", "subtitles.entity.warden.ambient": "Warden el se lamenta", "subtitles.entity.warden.angry": "Warden el se inversa", "subtitles.entity.warden.attack_impact": "<PERSON> el colpiss", "subtitles.entity.warden.death": "Warden el moeur", "subtitles.entity.warden.dig": "<PERSON> el s<PERSON>", "subtitles.entity.warden.emerge": "Warden el vegn foeura", "subtitles.entity.warden.heartbeat": "Coeur de <PERSON> el bat", "subtitles.entity.warden.hurt": "Warden ferid", "subtitles.entity.warden.listening": "Warden el se incorsg", "subtitles.entity.warden.listening_angry": "Warden el se incorsg inversad", "subtitles.entity.warden.nearby_close": "Warden el vegn arent", "subtitles.entity.warden.nearby_closer": "Warden el vegn inanz", "subtitles.entity.warden.nearby_closest": "Warden a l'è propi vesin", "subtitles.entity.warden.roar": "<PERSON> el rus<PERSON>s", "subtitles.entity.warden.sniff": "Warden l'usma", "subtitles.entity.warden.sonic_boom": "Warden el taca", "subtitles.entity.warden.sonic_charge": "Warden el carega", "subtitles.entity.warden.step": "Warden el camina", "subtitles.entity.warden.tendril_clicks": "Antenne del sorvegliante schioccano", "subtitles.entity.wind_charge.throw": "Carica di vento vola", "subtitles.entity.wind_charge.wind_burst": "Carica di vento scoppia", "subtitles.entity.witch.ambient": "Stria la sgavasgia", "subtitles.entity.witch.celebrate": "Stria la fa festa", "subtitles.entity.witch.death": "Stria la moeur", "subtitles.entity.witch.drink": "Stria la bev", "subtitles.entity.witch.hurt": "Stria ferida", "subtitles.entity.witch.throw": "Stria la tira pozzion", "subtitles.entity.wither.ambient": "Wither el se inversa", "subtitles.entity.wither.death": "Wither el moeur", "subtitles.entity.wither.hurt": "Wither ferid", "subtitles.entity.wither.shoot": "<PERSON>er l'ataca", "subtitles.entity.wither.spawn": "Wither el se libera", "subtitles.entity.wither_skeleton.ambient": "Schelter de l'infern el scrizza", "subtitles.entity.wither_skeleton.death": "Schelter de l'infern el moeur", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON> de l'infern ferid", "subtitles.entity.wolf.ambient": "Lov el bofa", "subtitles.entity.wolf.bark": "<PERSON><PERSON> a<PERSON>", "subtitles.entity.wolf.death": "Lov el moeur", "subtitles.entity.wolf.growl": "Lov el rogna", "subtitles.entity.wolf.hurt": "<PERSON>v ferid", "subtitles.entity.wolf.pant": "<PERSON><PERSON>", "subtitles.entity.wolf.shake": "Lov el se scorliss", "subtitles.entity.wolf.whine": "<PERSON>po si lamenta", "subtitles.entity.zoglin.ambient": "Zoglin el rogna", "subtitles.entity.zoglin.angry": "Zoglin el rogna inversad", "subtitles.entity.zoglin.attack": "Zoglin l'ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> el moeur", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> ferid", "subtitles.entity.zoglin.step": "Zoglin el camina", "subtitles.entity.zombie.ambient": "Zombi el se lamenta", "subtitles.entity.zombie.attack_wooden_door": "Porta la donda", "subtitles.entity.zombie.break_wooden_door": "Porta la se romp", "subtitles.entity.zombie.converted_to_drowned": "Zombi el se trasforma in negad", "subtitles.entity.zombie.death": "Zombi el moeur", "subtitles.entity.zombie.destroy_egg": "Oeuv de bissa scudellera pestad sgiò", "subtitles.entity.zombie.hurt": "Zombi ferid", "subtitles.entity.zombie.infect": "Zombi l'infeta", "subtitles.entity.zombie_horse.ambient": "Cavall zombi el cria", "subtitles.entity.zombie_horse.death": "Cavall zombi el moeur", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> zombi ferid", "subtitles.entity.zombie_villager.ambient": "Paisan zombi el se lamenta", "subtitles.entity.zombie_villager.converted": "Paisan zombi curad", "subtitles.entity.zombie_villager.cure": "Paisan zombi el se scalmana", "subtitles.entity.zombie_villager.death": "Paisan zombi el moeur", "subtitles.entity.zombie_villager.hurt": "Paisan zombi ferid", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> zombifegad el rogna", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON> zombifegad el rogna con rabia", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombifegad el moeur", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> zombifegad ferid", "subtitles.event.mob_effect.bad_omen": "Presagio s'insedia", "subtitles.event.mob_effect.raid_omen": "Incursione nelle vicinanze", "subtitles.event.mob_effect.trial_omen": "Sfida infausta nelle vicinanze", "subtitles.event.raid.horn": "Coren el resona", "subtitles.item.armor.equip": "Lavorà dovrad", "subtitles.item.armor.equip_chain": "Armadura de maja la cioca", "subtitles.item.armor.equip_diamond": "Armadura de diamant la cioca", "subtitles.item.armor.equip_elytra": "Eliter fluscen", "subtitles.item.armor.equip_gold": "Armadura d'or la fa frecass", "subtitles.item.armor.equip_iron": "Armadura de ferr la fa frecass", "subtitles.item.armor.equip_leather": "Armadura de coram la fluscia", "subtitles.item.armor.equip_netherite": "Armadura de netherite la fa frecass", "subtitles.item.armor.equip_turtle": "Guss de bissa scudellera el resona", "subtitles.item.armor.equip_wolf": "Armatura per lupi allacciata", "subtitles.item.armor.unequip_wolf": "Armatura per lupi slacciata", "subtitles.item.axe.scrape": "Se<PERSON>rin el sgrafigna", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> el scarpa", "subtitles.item.axe.wax_off": "Cera cavada", "subtitles.item.bone_meal.use": "<PERSON><PERSON> de oss la fluscia", "subtitles.item.book.page_turn": "Pagina la fluscia", "subtitles.item.book.put": "Liber el fa plof", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON> impienida", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Netadura de la gera", "subtitles.item.brush.brushing.gravel.complete": "<PERSON>era <PERSON>ada", "subtitles.item.brush.brushing.sand": "Netadura de la sabia", "subtitles.item.brush.brushing.sand.complete": "Sabia netada", "subtitles.item.bucket.empty": "<PERSON><PERSON> v<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON> imp<PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl brancad", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> <PERSON><PERSON> su", "subtitles.item.bucket.fill_tadpole": "Botarana catada su", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON> vojad", "subtitles.item.bundle.insert": "Lavorà sgiontad", "subtitles.item.bundle.insert_fail": "Sacchetto pieno", "subtitles.item.bundle.remove_one": "Lavorà cavad via", "subtitles.item.chorus_fruit.teleport": "Sgiugador el se teleporta", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON> some<PERSON>", "subtitles.item.crossbow.charge": "Caregament de la balestra", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON> la colpiss", "subtitles.item.crossbow.load": "Balestra caregada", "subtitles.item.crossbow.shoot": "Tir de balestra", "subtitles.item.dye.use": "Tinta la teng", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON> de <PERSON> tirad", "subtitles.item.flintandsteel.use": "Sparafoeugh dovrad", "subtitles.item.glow_ink_sac.use": "Saca de incoster sbarlusent la smagia", "subtitles.item.goat_horn.play": "Coren de cavra el sona", "subtitles.item.hoe.till": "Zapa l'ara", "subtitles.item.honey_bottle.drink": "'Dree a bever", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "Bardatura rimossa", "subtitles.item.ink_sac.use": "Saca de incoster la smagia", "subtitles.item.lead.break": "Guinzaglio si spezza", "subtitles.item.lead.tied": "Guinzaglio legato", "subtitles.item.lead.untied": "Guinzaglio slegato", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON><PERSON>o", "subtitles.item.lodestone_compass.lock": "Bussola magnetizada fissada in su magnetit", "subtitles.item.mace.smash_air": "<PERSON><PERSON> frac<PERSON>a", "subtitles.item.mace.smash_ground": "<PERSON><PERSON> frac<PERSON>a", "subtitles.item.nether_wart.plant": "<PERSON><PERSON><PERSON> some<PERSON>", "subtitles.item.ominous_bottle.dispose": "Ampolla distrutta", "subtitles.item.saddle.unequip": "<PERSON><PERSON> rim<PERSON>", "subtitles.item.shears.shear": "<PERSON>'cesore drovade", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.item.shield.block": "Scud el bloca", "subtitles.item.shovel.flatten": "Baira la spiana", "subtitles.item.spyglass.stop_using": "Canoccial el se tra indree", "subtitles.item.spyglass.use": "Canoccial el se slonga", "subtitles.item.totem.use": "Totem pizzad", "subtitles.item.trident.hit": "Trident l'infilza", "subtitles.item.trident.hit_ground": "Trident el vibra", "subtitles.item.trident.return": "Trident el torna indree", "subtitles.item.trident.riptide": "Trident el part a l'improvista", "subtitles.item.trident.throw": "Trident el scrizza", "subtitles.item.trident.thunder": "Trident el trona", "subtitles.item.wolf_armor.break": "Armatura per lupi si rompe", "subtitles.item.wolf_armor.crack": "Armatura per lupi s'incrina", "subtitles.item.wolf_armor.damage": "Armatura per lupi danneggiata", "subtitles.item.wolf_armor.repair": "Armatura per lupi riparata", "subtitles.particle.soul_escape": "Anima la scapa", "subtitles.ui.cartography_table.take_result": "Mapa dissegnada", "subtitles.ui.hud.bubble_pop": "Ossigeno in calo", "subtitles.ui.loom.take_result": "Telar dovrad", "subtitles.ui.stonecutter.take_result": "Taja-<PERSON><PERSON> do<PERSON>ad", "subtitles.weather.rain": "<PERSON><PERSON><PERSON><PERSON>", "symlink_warning.message": "Car<PERSON>re mondi da cartelle che contengono collegamenti simbolici può presentare rischi se non sai con piena certezza quello che stai facendo. Visita %s per saperne di più.", "symlink_warning.message.pack": "Caricare pacchetti che contengono collegamenti simbolici può presentare rischi se non sai con piena certezza quello che stai facendo. Visita %s per saperne di più.", "symlink_warning.message.world": "Car<PERSON>re mondi da cartelle che contengono collegamenti simbolici può presentare rischi se non sai con piena certezza quello che stai facendo. Visita %s per saperne di più.", "symlink_warning.more_info": "Ulteriori informazioni", "symlink_warning.title": "La cartella del mondo contiene collegamenti simbolici", "symlink_warning.title.pack": "I pacchetti aggiunti contengono collegamenti simbolici", "symlink_warning.title.world": "La cartella del mondo contiene collegamenti simbolici", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON>", "team.collision.pushOtherTeams": "Ruza member de olter squader", "team.collision.pushOwnTeam": "Ruza member de la propia squadra", "team.notFound": "Squadra descognossuda: %s", "team.visibility.always": "<PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Sconduda per i olter squader", "team.visibility.hideForOwnTeam": "Sconduda per la propia squadra", "team.visibility.never": "<PERSON>", "telemetry.event.advancement_made.description": "Capire il contesto relativo al compimento di un progresso può aiutarci a comprendere meglio e a perfezionare la sequenza di gioco.", "telemetry.event.advancement_made.title": "Progresso compiuto", "telemetry.event.game_load_times.description": "Misurando il tempo necessario a eseguire le fasi di avvio, quest'evento può aiutarci a capire dove c'è bisogno di migliorie.", "telemetry.event.game_load_times.title": "Tempi di caricamento del gioco", "telemetry.event.optional": "%s (facoltativo)", "telemetry.event.optional.disabled": "%s (facoltativo) - disattivato", "telemetry.event.performance_metrics.description": "Conoscere il profilo delle prestazioni di Minecraft ci aiuta ad adattare e a ottimizzare il gioco per un'ampia gamma di dispositivi e sistemi operativi.\nLa versione del gioco è inclusa per aiutarci a effettuare il confronto con il profilo delle prestazioni delle nuove versioni di Minecraft.", "telemetry.event.performance_metrics.title": "Metriche di prestazione", "telemetry.event.required": "%s (necessario)", "telemetry.event.world_load_times.description": "Per noi è importante capire il tempo necessario per entrare in un mondo e come questo dato può cambiare. Ad esempio, quando aggiungiamo nuove funzionalità o apportiamo modifiche tecniche significative, ci serve sapere in che modo ciò influisce sui tempi di caricamento.", "telemetry.event.world_load_times.title": "Tempo di caricamento dei mondi", "telemetry.event.world_loaded.description": "Sapere come i giocatori giocano a Minecraft (per esempio la modalità di gioco, client o server modificati e la versione di gioco) ci consente di focalizzare gli aggiornamenti del gioco al fine di migliorare le aree a cui i giocatori tengono di più.\nL'evento «Mondo caricato» è usato assieme all'evento «Mondo scaricato» per calcolare la durata delle sessioni di gioco.", "telemetry.event.world_loaded.title": "Mond caregad", "telemetry.event.world_unloaded.description": "Quest'evento viene usato insieme a «Mondo caricato» per calcolare la durata della sessione del mondo.\nLa durata (in secondi e in tick) viene misurata al termine della sessione di un mondo (cioè quando si torna al menu principale o ci si disconnette da un server).", "telemetry.event.world_unloaded.title": "Mond descaregad", "telemetry.property.advancement_game_time.title": "Tempo di gioco (tick)", "telemetry.property.advancement_id.title": "ID progresso", "telemetry.property.client_id.title": "ID client", "telemetry.property.client_modded.title": "Client modificato", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicata (kB)", "telemetry.property.event_timestamp_utc.title": "Orario dell'evento (UTC)", "telemetry.property.frame_rate_samples.title": "Campioni di frequenza dei fotogrammi (fps)", "telemetry.property.game_mode.title": "Modalità de sgioeugh", "telemetry.property.game_version.title": "Version del sgioeugh", "telemetry.property.launcher_name.title": "Nom del launcher", "telemetry.property.load_time_bootstrap_ms.title": "Tempo di avvio (millisecondi)", "telemetry.property.load_time_loading_overlay_ms.title": "Tempo nella schermata di caricamento (millisecondi)", "telemetry.property.load_time_pre_window_ms.title": "Tempo prima dell'apertura della finestra (millisecondi)", "telemetry.property.load_time_total_time_ms.title": "Tempo totale di caricamento (millisecondi)", "telemetry.property.minecraft_session_id.title": "ID sessione di <PERSON>craft", "telemetry.property.new_world.title": "Mond noeuv", "telemetry.property.number_of_samples.title": "Numero di campioni", "telemetry.property.operating_system.title": "Sistema operativo", "telemetry.property.opt_in.title": "Evento necessario/facoltativo", "telemetry.property.platform.title": "Piattaforma", "telemetry.property.realms_map_content.title": "Contenuto della mappa di Realms (nome del minigioco)", "telemetry.property.render_distance.title": "Di<PERSON><PERSON> di <PERSON>", "telemetry.property.render_time_samples.title": "Campioni del tempo di rendering", "telemetry.property.seconds_since_load.title": "Tempo dal caricamento del mondo (secondi)", "telemetry.property.server_modded.title": "Server modificato", "telemetry.property.server_type.title": "Tip de server", "telemetry.property.ticks_since_load.title": "Tempo dal caricamento del mondo (tick)", "telemetry.property.used_memory_samples.title": "Utilizzo di memoria RAM", "telemetry.property.user_id.title": "ID del dovrador", "telemetry.property.world_load_time_ms.title": "Tempo di caricamento del mondo (millisecondi)", "telemetry.property.world_session_id.title": "ID sessione del mondo", "telemetry_info.button.give_feedback": "Lassa un'opinion", "telemetry_info.button.privacy_statement": "Dichiarazione sulla privacy", "telemetry_info.button.show_data": "<PERSON>va i mè dat", "telemetry_info.opt_in.description": "Acconsento all'invio di dati di telemetria facoltativi", "telemetry_info.property_title": "<PERSON><PERSON> in<PERSON>i", "telemetry_info.screen.description": "Catà su quei dat chì el ne juta a fà pussee bon el Minecraft, desgià che inscì a podom savé quai inn i scerne pussee giuste per i noster sgiugador.\nDe sorapu te podet mandà anca di opinion per jutànn a fà el Minecraft pussee bon an'mò.", "telemetry_info.screen.title": "Raccolta dati di telemetria", "test.error.block_property_mismatch": "Proprietà %s prevista: %s, risultata: %s", "test.error.block_property_missing": "Proprietà di blocco mancante. Proprietà %s prevista: %s", "test.error.entity_property": "L'entità %s ha fallito il test: %s", "test.error.entity_property_details": "L'entità %s ha fallito il test: %s, previsto: %s, risultato: %s", "test.error.expected_block": "Previsto il blocco %s, ottenuto %s", "test.error.expected_block_tag": "Previsto blocco entro #%s, ottenuto %s", "test.error.expected_container_contents": "Il contenitore dovrebbe avere: %s", "test.error.expected_container_contents_single": "Il contenitore dovrebbe avere un solo: %s", "test.error.expected_empty_container": "Il contenitore dovrebbe essere vuoto", "test.error.expected_entity": "Previsto %s", "test.error.expected_entity_around": "Prevista l'esistenza di %s intorno a %s, %s, %s", "test.error.expected_entity_count": "Previste %s entità di tipo %s, trovate %s", "test.error.expected_entity_data": "Dati dell'entità previsti: %s, trovati: %s", "test.error.expected_entity_data_predicate": "I dati dell'entità non corrispondono per %s", "test.error.expected_entity_effect": "Previsto che %s abbia l'effetto %s %s", "test.error.expected_entity_having": "L'inventario dell'entità dovrebbe avere %s", "test.error.expected_entity_holding": "L'entità dovrebbe impugnare %s", "test.error.expected_entity_in_test": "Prevista l'esistenza di %s nel test", "test.error.expected_entity_not_touching": "Non previsto %s adiacente a %s, %s, %s (relativa: %s, %s, %s)", "test.error.expected_entity_touching": "Previsto %s adiacente a %s, %s, %s (relativa: %s, %s, %s)", "test.error.expected_item": "Previsto oggetto di tipo %s", "test.error.expected_items_count": "Previsti %s oggetti di tipo %s, trovati %s", "test.error.fail": "Condizioni di fallimento soddisfatte", "test.error.invalid_block_type": "Trovato tipo di blocco non previsto: %s", "test.error.missing_block_entity": "Entità di blocco mancante", "test.error.position": "%s presso %s, %s, %s (relativa: %s, %s, %s) al tick %s", "test.error.sequence.condition_already_triggered": "Condizione già attivata a %s", "test.error.sequence.condition_not_triggered": "Condizione non attivata", "test.error.sequence.invalid_tick": "<PERSON><PERSON><PERSON><PERSON> ottenuta durante tick non valido: previsto %s", "test.error.sequence.not_completed": "Il test è scaduto prima del completamento della sequenza", "test.error.set_biome": "Impossibile impostare il bioma per il test", "test.error.spawn_failure": "Impossibile creare l'entità %s", "test.error.state_not_equal": "Stato non corretto. Previsto %s, ricevuto %s", "test.error.structure.failure": "Impossibile piazzare la struttura di test per %s", "test.error.tick": "%s al tick %s", "test.error.ticking_without_structure": "Test in corso prima di piazzare la struttura", "test.error.timeout.no_result": "Non è riuscito né fallito entro %s tick", "test.error.timeout.no_sequences_finished": "Nessuna sequenza finita entro %s tick", "test.error.too_many_entities": "Prevista l'esistenza di un solo %s intorno a %s, %s, %s ma è stato trovato %s", "test.error.unexpected_block": "Non era previsto che il blocco fosse %s", "test.error.unexpected_entity": "Non era prevista l'esistenza di %s", "test.error.unexpected_item": "Non era previsto un oggetto di tipo %s", "test.error.unknown": "Errore interno sconosciuto: %s", "test.error.value_not_equal": "%s previsto: %s, risultato: %s", "test.error.wrong_block_entity": "Tipo di entità di blocco sbagliato: %s", "test_block.error.missing": "Blocco %s mancante dalla struttura di test", "test_block.error.too_many": "<PERSON><PERSON><PERSON> b<PERSON> (%s)", "test_block.invalid_timeout": "Timeout non valido (%s): deve essere un numero positivo di tick", "test_block.message": "Messaggio:", "test_block.mode.accept": "Accetta", "test_block.mode.fail": "Fallimento", "test_block.mode.log": "Registro", "test_block.mode.start": "<PERSON><PERSON><PERSON>", "test_block.mode_info.accept": "Modalità accettazione: accetta la riuscita di (una parte di) un test", "test_block.mode_info.fail": "Modalità fallimento: il test fallisce", "test_block.mode_info.log": "Modalità registro: registra un messaggio", "test_block.mode_info.start": "Modalità avvio: il punto di partenza di un test", "test_instance.action.reset": "Ripristina e carica", "test_instance.action.run": "Carica ed esegui", "test_instance.action.save": "<PERSON><PERSON> struttura", "test_instance.description.batch": "Lotto: %s", "test_instance.description.failed": "Non riuscito: %s", "test_instance.description.function": "Funzione: %s", "test_instance.description.invalid_id": "ID di test non valido", "test_instance.description.no_test": "Nessun test trovato", "test_instance.description.structure": "Struttura: %s", "test_instance.description.type": "Tipo: %s", "test_instance.type.block_based": "Test basato su blocchi", "test_instance.type.function": "Test di funzione integrato", "test_instance_block.entities": "Entità:", "test_instance_block.error.no_test": "Impossibile eseguire l'istanza di test presso %s, %s, %s perché non è stato definito alcun test", "test_instance_block.error.no_test_structure": "Impossibile eseguire l'istanza di test presso %s, %s, %s perché non ha una struttura di test", "test_instance_block.error.unable_to_save": "Impossibile salvare il modello di struttura per l'istanza di test presso %s, %s, %s", "test_instance_block.invalid": "[non valido]", "test_instance_block.reset_success": "<PERSON><PERSON><PERSON><PERSON> rius<PERSON> per il test: %s", "test_instance_block.rotation": "Rotazione:", "test_instance_block.size": "Dimensione della struttura di test", "test_instance_block.starting": "Avvio del test %s", "test_instance_block.test_id": "ID istanza di test", "title.32bit.deprecation": "L'è stad relevad un sistema a 32 bit: quell chì el podaria impedìt de sgiugà in futur desgià che ghe sarà besogn del sistema a 64 bit!", "title.32bit.deprecation.realms": "Prest el Minecraft el gh'havarà besogn de un sistema a 64 bit. Quella roba chì la te impedarà de sgiugà o dovrà  Realms in su quell dispositiv chì. Te gh'havareet de scancellà a man tucc i iscrizion a Realms.", "title.32bit.deprecation.realms.check": "<PERSON>ra pu quella schermada chì", "title.32bit.deprecation.realms.header": "Sistema a 32 bit relevad", "title.credits": "Copyright Mojang AB. Vietata la distribuzione!", "title.multiplayer.disabled": "La modalità multigiocatore è disattivata. Controlla le impostazioni del tuo account Microsoft.", "title.multiplayer.disabled.banned.name": "Te gh'heet de cambià el to nom prima de podé sgiugà in linea", "title.multiplayer.disabled.banned.permanent": "Il tuo account è permanentemente sospeso dal gioco online", "title.multiplayer.disabled.banned.temporary": "Il tuo account è momentaneamente sospeso dal gioco online", "title.multiplayer.lan": "Multisgi<PERSON>dor (LAN)", "title.multiplayer.other": "Multisgi<PERSON><PERSON> (server de terz)", "title.multiplayer.realms": "<PERSON>s<PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Un s<PERSON>ugador", "translation.test.args": "%s %s", "translation.test.complex": "Prefiss, %s%2$s an'mò %s e %1$s e a la fin %s e anca %1$s ancamò!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "ciao %", "translation.test.invalid2": "ciao %s", "translation.test.none": "Ciao, mond!", "translation.test.world": "mond", "trim_material.minecraft.amethyst": "In ametista", "trim_material.minecraft.copper": "In ram", "trim_material.minecraft.diamond": "In diamant", "trim_material.minecraft.emerald": "In ismerald", "trim_material.minecraft.gold": "In or", "trim_material.minecraft.iron": "In ferr", "trim_material.minecraft.lapis": "In lapislazzuli", "trim_material.minecraft.netherite": "In netherite", "trim_material.minecraft.quartz": "In quarz", "trim_material.minecraft.redstone": "In prejarossa", "trim_material.minecraft.resin": "<PERSON> resina", "trim_pattern.minecraft.bolt": "Ornamento stile bullone", "trim_pattern.minecraft.coast": "Dissegn d'armadura: costa", "trim_pattern.minecraft.dune": "Dissegn d'armadura: duna", "trim_pattern.minecraft.eye": "Dissegn d'armadura: oeugg", "trim_pattern.minecraft.flow": "Ornamento stile flusso", "trim_pattern.minecraft.host": "Dissegn d'armadura: ospit", "trim_pattern.minecraft.raiser": "Ornamento stile allevatore", "trim_pattern.minecraft.rib": "Ornamento stile costole", "trim_pattern.minecraft.sentry": "Ornamento stile sentinella", "trim_pattern.minecraft.shaper": "Ornamento stile modellatore", "trim_pattern.minecraft.silence": "Dissegn d'armadura: silenzi", "trim_pattern.minecraft.snout": "Ornamento stile grugno", "trim_pattern.minecraft.spire": "Ornamento stile guglie", "trim_pattern.minecraft.tide": "Ornamento stile marea", "trim_pattern.minecraft.vex": "Ornamento stile vessante", "trim_pattern.minecraft.ward": "Ornamento stile sorvegliante", "trim_pattern.minecraft.wayfinder": "Ornamento stile ricognitore", "trim_pattern.minecraft.wild": "Dissegn d'armadura: selvagg", "tutorial.bundleInsert.description": "Schiscia el boton de drizza per sgiontà di element", "tutorial.bundleInsert.title": "Impieniss un fagot", "tutorial.craft_planks.description": "El liber di ricete el poeu dà una man", "tutorial.craft_planks.title": "Fabrega ass de legn", "tutorial.find_tree.description": "Colpiss la bora", "tutorial.find_tree.title": "Troeuva una pianta", "tutorial.look.description": "Moeuv el ratin per giràss", "tutorial.look.title": "Vardes intorna", "tutorial.move.description": "Salta con %s", "tutorial.move.title": "Moeuvess con %s, %s, %s e %s", "tutorial.open_inventory.description": "Schiscia %s", "tutorial.open_inventory.title": "Derva el to inventari", "tutorial.punch_tree.description": "Tegn schisciad %s", "tutorial.punch_tree.title": "Destruga la pianta", "tutorial.socialInteractions.description": "Schiscia %s per dervì", "tutorial.socialInteractions.title": "Interazzion sociai", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON><PERSON> de netherite"}