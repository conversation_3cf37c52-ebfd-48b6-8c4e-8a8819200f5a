{"accessibility.onboarding.accessibility.button": "Barrierefreiheit …", "accessibility.onboarding.screen.narrator": "<PERSON>ücke die Eingabetaste, um die Sprachausgabe zu aktivieren.", "accessibility.onboarding.screen.title": "Willkommen in Minecraft!\n\nMöchtest du die Sprachausgabe aktivieren oder die Barrierefreiheitseinstellungen besuchen?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "Server<PERSON><PERSON><PERSON>", "addServer.enterName": "Servername", "addServer.resourcePack": "Server‐Ressourcenpakete", "addServer.resourcePack.disabled": "Deaktiviert", "addServer.resourcePack.enabled": "Aktiviert", "addServer.resourcePack.prompt": "Abfrage", "addServer.title": "Serverinformationen bearbeiten", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Immer aktiv", "advMode.mode.conditional": "<PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.sequence": "Verketten", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "<PERSON><PERSON> ein Operator im Kreativ-Modus kann Befehle eingeben", "advMode.notEnabled": "Befehlsblöcke sind auf diesem Server nicht eingeschaltet", "advMode.previousOutput": "Letzte Ausgabe", "advMode.setCommand": "Be<PERSON><PERSON> e<PERSON>ben", "advMode.setCommand.success": "Befehl gesetzt: %s", "advMode.trackOutput": "Ausgabe verfolgen", "advMode.triggering": "Auslösen", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Unbekannter Fortschritt ‚%s‘", "advancements.adventure.adventuring_time.description": "Entdecke alle Biome", "advancements.adventure.adventuring_time.title": "Abenteuerzeit", "advancements.adventure.arbalistic.description": "E<PERSON><PERSON> fünf unterschiedliche Kreaturen mit einem Armbrustschuss", "advancements.adventure.arbalistic.title": "Armbrustschütze", "advancements.adventure.avoid_vibration.description": "Schlei<PERSON> in der Nähe eines Sculk‐Sensors oder Wärters, um zu verhindern, dass du bemerkt wirst", "advancements.adventure.avoid_vibration.title": "Schleichen 100", "advancements.adventure.blowback.description": "Erlege eine Böe mit einer abgewehrten Böen‐Windkugel", "advancements.adventure.blowback.title": "Gegenwind", "advancements.adventure.brush_armadillo.description": "<PERSON>utze e<PERSON> Pinsel, um ein Hornschild vom Panzer eines Gürteltiers zu lösen", "advancements.adventure.brush_armadillo.title": "Ist das nicht putzig?", "advancements.adventure.bullseye.description": "Triff die Mitte eines Zielblocks aus mindestens 30 Blöcken Entfernung", "advancements.adventure.bullseye.title": "Volltreffer!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Stelle aus vier Töpferscherben einen verzierten Krug her", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Be<PERSON>tsame Restaurierung", "advancements.adventure.crafters_crafting_crafters.description": "Befinde dich in der Nähe eines Werkers, wenn dieser einen Werker herstellt", "advancements.adventure.crafters_crafting_crafters.title": "Werker werkeln Werker", "advancements.adventure.fall_from_world_height.description": "Stürze im freien Fall von der Weltobergrenze (die maximale Bauhöhe) bis zur Weltuntergrenze und überlebe", "advancements.adventure.fall_from_world_height.title": "Höhlen & Klippen", "advancements.adventure.heart_transplanter.description": "Setze ein Knarzherz in der richtigen Ausrichtung zwischen zwei Blasseichenstämme", "advancements.adventure.heart_transplanter.title": "Herzverpflanzer", "advancements.adventure.hero_of_the_village.description": "Verteidige ein Dorf erfolgreich vor einem Überfall", "advancements.adventure.hero_of_the_village.title": "Do<PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Springe an einen Honigblock, um deinen Sturz abzufangen", "advancements.adventure.honey_block_slide.title": "Eine klebrige Angelegenheit", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON><PERSON> ein <PERSON>", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "<PERSON><PERSON><PERSON> jedes Monster mindestens ein <PERSON>", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON>j<PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Erlege eine Kreatur in der Nähe eines Sculk‐Katalysators", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Es verbreitet sich", "advancements.adventure.lighten_up.description": "<PERSON><PERSON>e eine Kupferleuchte mit einer Axt, um ihre Leuchtkraft zu verbessern", "advancements.adventure.lighten_up.title": "Aufhellen", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "<PERSON>h<PERSON>tze einen Dorfbewohner vor einem unerwünschten <PERSON>, ohne dabei einen Brand auszulösen", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Überspannungsschutz", "advancements.adventure.minecraft_trials_edition.description": "Wage dich in eine Prüfungskammer", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Prüfungs‐Edition", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> eine Armbrust ab", "advancements.adventure.ol_betsy.title": "Ich habe die Knarre gesichert", "advancements.adventure.overoverkill.description": "Verursache mit dem Streitkolben 50 Herzen Schaden in einem einzigen Hieb", "advancements.adventure.overoverkill.title": "Großmeister des Kampfes", "advancements.adventure.play_jukebox_in_meadows.description": "Belebe die Alm mit dem Klang der Musik aus einem Plattenspieler", "advancements.adventure.play_jukebox_in_meadows.title": "Deine Welt sind die Berge", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Lies mit einem Komparator die Signalstärke eines gearbeiteten Bücherregals aus", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Spannung in jedem Band", "advancements.adventure.revaulting.description": "Schließe einen unheilvollen Tresor mit einem unheilvollen Prüfungsschlüssel auf", "advancements.adventure.revaulting.title": "Panzerknacker", "advancements.adventure.root.description": "<PERSON><PERSON>uer, Entdeckung und Kampf", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "Pinsel einen seltsamen Block ab, um eine Töpferscherbe zu finden", "advancements.adventure.salvage_sherd.title": "Achtung vor dem Alten", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON> ein <PERSON> mit einem Pfeil", "advancements.adventure.shoot_arrow.title": "Zielübungen", "advancements.adventure.sleep_in_bed.description": "Schl<PERSON>e in einem Bett, um deinen Wiedereinstiegspunkt zu ändern", "advancements.adventure.sleep_in_bed.title": "Träum was <PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Erlege ein Skelett aus mindestens 50 Blöcken Entfernung", "advancements.adventure.sniper_duel.title": "Scharfschützenduell", "advancements.adventure.spyglass_at_dragon.description": "Betrachte den Enderdrachen durch ein Fernrohr", "advancements.adventure.spyglass_at_dragon.title": "Ist es ein Flugzeug?", "advancements.adventure.spyglass_at_ghast.description": "Betrachte einen Ghast durch ein Fernrohr", "advancements.adventure.spyglass_at_ghast.title": "Ist es ein <PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "Betrachte einen Papagei durch ein Fernrohr", "advancements.adventure.spyglass_at_parrot.title": "Ist es ein Vogel?", "advancements.adventure.summon_iron_golem.description": "Baue einen Eisengolem, um bei der Verteidigung eines Dorfes mitzuhelfen", "advancements.adventure.summon_iron_golem.title": "Das 1. Gesetz der Robotik", "advancements.adventure.throw_trident.description": "Wirf deinen Dreizack auf irgendetwas.\nHinweis: Es ist keine gute Idee, deine einzige Waffe wegzuwerfen.", "advancements.adventure.throw_trident.title": "Ein Witz zum Wegwerfen", "advancements.adventure.totem_of_undying.description": "Benutze ein Totem der Unsterblichkeit, um dem Tod von der Schippe zu springen", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Schl<PERSON>ße einen Handel mit einem Dorfbewohner ab", "advancements.adventure.trade.title": "Was für ein Geschäft!", "advancements.adventure.trade_at_world_height.description": "Handle mit einem Dorfbewohner auf der maximalen Bauhöhe", "advancements.adventure.trade_at_world_height.title": "<PERSON>enh<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON> jede dieser Schmiedevorlagen mindestens einmal an: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ge<PERSON>, Gezeiten und Wegfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Schmieden mit Stil", "advancements.adventure.trim_with_any_armor_pattern.description": "Versieh ein Rüstungsteil an einem Schmiedetisch mit einem Rüstungsbesatz", "advancements.adventure.trim_with_any_armor_pattern.title": "Ein neues Aussehen", "advancements.adventure.two_birds_one_arrow.description": "Erlege zwei Phantome mit einem Durchschuss", "advancements.adventure.two_birds_one_arrow.title": "Zwei auf einen Streich", "advancements.adventure.under_lock_and_key.description": "Entri<PERSON>le einen Tresor mit einem Prüfungsschlüssel", "advancements.adventure.under_lock_and_key.title": "<PERSON>nter Schloss und Riegel", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON><PERSON> einen Kompass an einem Leitstein", "advancements.adventure.use_lodestone.title": "Weißt du, wo die Leitstein<PERSON> stehen?", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON> einen Dorfbewohner mit einem Blitz", "advancements.adventure.very_very_frightening.title": "B<PERSON>ezucken, Donnerkrach", "advancements.adventure.voluntary_exile.description": "Besiege einen Räuberhauptmann.\nZiehe möglicherweise in Betracht, dich vore<PERSON> von <PERSON>örfern fernzuhalten …", "advancements.adventure.voluntary_exile.title": "Freiwilliges Exil", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Gehe auf Pulverschnee … ohne darin zu versinken", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON>t wie ein Kaninchen", "advancements.adventure.who_needs_rockets.description": "<PERSON>utze eine Windkugel, um dich 8 <PERSON>löcke in die Höhe zu schleudern", "advancements.adventure.who_needs_rockets.title": "Wer braucht schon Raketen?", "advancements.adventure.whos_the_pillager_now.description": "Gib einem Plünderer eine Kostprobe seiner eigenen Medizin", "advancements.adventure.whos_the_pillager_now.title": "Wer ist jetzt der Plünderer?", "advancements.empty": "Hier scheint es nichts zu geben …", "advancements.end.dragon_breath.description": "Sammle Drachenatem in einer Glasflasche", "advancements.end.dragon_breath.title": "Du brauchst ein Pfefferminz", "advancements.end.dragon_egg.description": "Halte das Drachenei", "advancements.end.dragon_egg.title": "Die nächste Generation", "advancements.end.elytra.description": "<PERSON><PERSON>", "advancements.end.elytra.title": "<PERSON><PERSON><PERSON> geht’s weiter", "advancements.end.enter_end_gateway.description": "Entkomme der Insel", "advancements.end.enter_end_gateway.title": "Im Transit", "advancements.end.find_end_city.description": "Geh doch rein, was kann schon passieren?", "advancements.end.find_end_city.title": "Die Stadt am Ende des Spiels", "advancements.end.kill_dragon.description": "Viel Glück", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Schwebe durch Shulker‐Geschosse 50 Blöcke aufwärts", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON> von hier oben", "advancements.end.respawn_dragon.description": "Rufe den Enderdrachen ins Leben zurück", "advancements.end.respawn_dragon.title": "<PERSON> … <PERSON><PERSON> wieder …", "advancements.end.root.description": "… oder der <PERSON>?", "advancements.end.root.title": "<PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Lass einen Hilfsgeist einen Kuchen bei einem Notenblock ablegen", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Geburtstagslied", "advancements.husbandry.allay_deliver_item_to_player.description": "Lass dir einen Gegenstand von einem Hilfsgeist liefern", "advancements.husbandry.allay_deliver_item_to_player.title": "Du hast ’n Freund in mir", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON> einen Axolotl mit einem Eimer", "advancements.husbandry.axolotl_in_a_bucket.title": "Das niedlichste <PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> all<PERSON>, selbst wenn es nicht gut für dich ist", "advancements.husbandry.balanced_diet.title": "Ausgewogene Ernährung", "advancements.husbandry.breed_all_animals.description": "Vermehre alle Tierarten!", "advancements.husbandry.breed_all_animals.title": "Paarweise", "advancements.husbandry.breed_an_animal.description": "Vermehre zwei Tiere", "advancements.husbandry.breed_an_animal.title": "Die Hühnchen und die Blümchen", "advancements.husbandry.complete_catalogue.description": "<PERSON>ähme alle Katzenarten!", "advancements.husbandry.complete_catalogue.title": "Ein kompletter Katerlog", "advancements.husbandry.feed_snifflet.description": "Füttere ein Schnüfflerjunges", "advancements.husbandry.feed_snifflet.title": "Schnupperkurs", "advancements.husbandry.fishy_business.description": "<PERSON>", "advancements.husbandry.fishy_business.title": "Anglerglück", "advancements.husbandry.froglights.description": "Habe alle Froschlichter im Inventar", "advancements.husbandry.froglights.title": "Mit vereinten Kräften!", "advancements.husbandry.kill_axolotl_target.description": "Verbünde dich mit einem Axolotl und gewinne einen Kampf", "advancements.husbandry.kill_axolotl_target.title": "Die heilende Kraft der Freundschaft!", "advancements.husbandry.leash_all_frog_variants.description": "Habe jede Froschart an einer <PERSON>", "advancements.husbandry.leash_all_frog_variants.title": "Wenn die Kerle um die Häuser ziehen", "advancements.husbandry.make_a_sign_glow.description": "Lass den Text einer beliebigen Art von <PERSON>", "advancements.husbandry.make_a_sign_glow.title": "<PERSON>uchte und staune!", "advancements.husbandry.netherite_hoe.description": "<PERSON>utze einen Netheritbarren, um eine Hacke aufzuwerten, und denke dann über deine Lebensentscheidungen nach", "advancements.husbandry.netherite_hoe.title": "Landwirt aus Leidenschaft", "advancements.husbandry.obtain_sniffer_egg.description": "Beschaffe ein Schnüffler‐Ei", "advancements.husbandry.obtain_sniffer_egg.title": "Ein ungewöhnlicher Geruch", "advancements.husbandry.place_dried_ghast_in_water.description": "Setze einen ausgetrockneten Ghast in Wasser", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON> hydriert!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON><PERSON> von einem Schnüffler ausgegrabene Samen", "advancements.husbandry.plant_any_sniffer_seed.title": "Wir säen Geschichte", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON> und sieh ihnen beim W<PERSON> zu", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "<PERSON><PERSON><PERSON> mit einer Schere den Wolfspanzer von e<PERSON>m Wolf", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON><PERSON>‐schnapp!", "advancements.husbandry.repair_wolf_armor.description": "Repariere einen beschädigten Wolfspanzer mit Gürteltier‐Hornschilden vollständig", "advancements.husbandry.repair_wolf_armor.title": "So gut wie neu", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Steige in ein Boot und mach mit einer Ziege die Fliege", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Ohne G<PERSON>ß kein Pre<PERSON>!", "advancements.husbandry.root.description": "Die Welt ist voller Freunde und Essen", "advancements.husbandry.root.title": "Landwirtschaft", "advancements.husbandry.safely_harvest_honey.description": "<PERSON><PERSON>e ein <PERSON>ger<PERSON>uer, um mit einer Glasflasche Honig aus einem Bienenstock zu sammeln, ohne die Bienen zu reizen", "advancements.husbandry.safely_harvest_honey.title": "Sssei unser Gast", "advancements.husbandry.silk_touch_nest.description": "Ziehe ein Bienennest oder einen Bienenstock, in dem drei Bienen sind, mit Behutsamkeit um", "advancements.husbandry.silk_touch_nest.title": "Bienenwanderung", "advancements.husbandry.tactical_fishing.description": "<PERSON> einen Fisch … ohne Angel!", "advancements.husbandry.tactical_fishing.title": "Taktisches Fischen", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON> eine <PERSON> in einem Eimer", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON><PERSON> ein <PERSON>", "advancements.husbandry.tame_an_animal.title": "Allerbeste Freunde", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON> Wach<PERSON> von einem Kupferblock ab!", "advancements.husbandry.wax_off.title": "Polieren", "advancements.husbandry.wax_on.description": "Wachse einen Kupferblock mit einer Honigwabe!", "advancements.husbandry.wax_on.title": "Auftragen", "advancements.husbandry.whole_pack.description": "Zähme alle Wolfsarten", "advancements.husbandry.whole_pack.title": "Das ganze <PERSON>", "advancements.nether.all_effects.description": "Habe jeden Statuseffekt gleichzeitig", "advancements.nether.all_effects.title": "Wie haben wir das geschafft?", "advancements.nether.all_potions.description": "Habe jeden Trankeffekt gleichzeitig", "advancements.nether.all_potions.title": "Eine gefährliche Mischung", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> e<PERSON> T<PERSON>", "advancements.nether.brew_potion.title": "Alchemie", "advancements.nether.charge_respawn_anchor.description": "Lade einen Seelenanker auf die höchste Stufe auf", "advancements.nether.charge_respawn_anchor.title": "<PERSON>cht ganz „sieben“ Leben", "advancements.nether.create_beacon.description": "Baue und aktiviere ein Leuchtfeuer", "advancements.nether.create_beacon.title": "<PERSON> Nachbarn heim<PERSON>ten", "advancements.nether.create_full_beacon.description": "Bring ein Leuchtfeuer auf volle Stärke", "advancements.nether.create_full_beacon.title": "Leuchtturmwärter", "advancements.nether.distract_piglin.description": "<PERSON><PERSON> mit Gold ab", "advancements.nether.distract_piglin.title": "Oh, wie glänzend!", "advancements.nether.explore_nether.description": "Entdecke alle Netherbiome", "advancements.nether.explore_nether.title": "Heißbegehrte Reiseziele", "advancements.nether.fast_travel.description": "<PERSON><PERSON><PERSON> den Nether, um 7 km in der Oberwelt zu reisen", "advancements.nether.fast_travel.title": "Subraumtransport", "advancements.nether.find_bastion.description": "<PERSON>ritt eine Bastionsruine", "advancements.nether.find_bastion.title": "Das waren noch Zeiten …", "advancements.nether.find_fortress.description": "<PERSON><PERSON> in eine Netherfestung ein", "advancements.nether.find_fortress.title": "Eine schreckliche Festung", "advancements.nether.get_wither_skull.description": "Erbeute einen Wither‐Skelettschädel", "advancements.nether.get_wither_skull.title": "Nicht den Kopf verlieren", "advancements.nether.loot_bastion.description": "Plündere eine Truhe in einer Bastionsruine", "advancements.nether.loot_bastion.title": "Kampfschweine", "advancements.nether.netherite_armor.description": "Besorge dir eine komplette Netheritrüstung", "advancements.nether.netherite_armor.title": "<PERSON><PERSON> mich mit Schrott", "advancements.nether.obtain_ancient_debris.description": "Beschaffe antiken Schrott", "advancements.nether.obtain_ancient_debris.title": "Verborgen in den Tiefen", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON><PERSON><PERSON>e eine Lohe um ihre Rute", "advancements.nether.obtain_blaze_rod.title": "Spiel mit dem Feuer", "advancements.nether.obtain_crying_obsidian.description": "Beschaffe weinenden Obsidian", "advancements.nether.obtain_crying_obsidian.title": "Wer schneidet hier Zwi<PERSON>n?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> einen Ghast mit seinen eigenen Waffen", "advancements.nether.return_to_sender.title": "Zurück zum Absender", "advancements.nether.ride_strider.description": "Reite einen Schreiter mit einer Wirrpilzrute", "advancements.nether.ride_strider.title": "<PERSON><PERSON> diesem „<PERSON>ot“ Be<PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Unternimm mit einem Schreiter einen laaaangen Ritt auf einem Lavasee in der Oberwelt", "advancements.nether.ride_strider_in_overworld_lava.title": "Ganz wie zu <PERSON>", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> dir was <PERSON><PERSON><PERSON> an", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Erschaffe den Wither", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Befreie einen Ghast aus dem Nether, bring ihn sicher in die Oberwelt … und erlege ihn dann", "advancements.nether.uneasy_alliance.title": "Unheilvolle Allianz", "advancements.nether.use_lodestone.description": "<PERSON><PERSON><PERSON> einen Kompass an einem Leitstein", "advancements.nether.use_lodestone.title": "Weißt du, wo die Leitstein<PERSON> stehen?", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Schwäche einen Zombiedorfbe‐ wohner und heile ihn dann", "advancements.story.cure_zombie_villager.title": "Zombiearzt", "advancements.story.deflect_arrow.description": "<PERSON><PERSON> ein Geschoss mit einem Schild ab", "advancements.story.deflect_arrow.title": "Heute nicht, danke", "advancements.story.enchant_item.description": "Verzaubere einen Gegenstand an einem Zaubertisch", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "<PERSON><PERSON><PERSON> das Endportal", "advancements.story.enter_the_end.title": "<PERSON> Ende?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, entzü<PERSON> und betritt ein Netherportal", "advancements.story.enter_the_nether.title": "Wir müssen noch tiefer", "advancements.story.follow_ender_eye.description": "<PERSON>olge einem Enderauge", "advancements.story.follow_ender_eye.title": "Das fliegende Auge", "advancements.story.form_obsidian.description": "Beschaffe einen Obsidianblock", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Verbessere deine Spitzhacke", "advancements.story.iron_tools.title": "Zu viel Eisen im Blut", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON> einen Eimer mit Lava", "advancements.story.lava_bucket.title": "Manche mögen’s heiß", "advancements.story.mine_diamond.description": "Beschaffe <PERSON>", "advancements.story.mine_diamond.title": "Diamanten!", "advancements.story.mine_stone.description": "<PERSON>ue Stein mit deiner neuen Spitzhacke ab", "advancements.story.mine_stone.title": "Steinzeit", "advancements.story.obtain_armor.description": "<PERSON>h<PERSON>tze dich mit einem Rüstungsteil aus Eisen", "advancements.story.obtain_armor.title": "<PERSON>h dich fein", "advancements.story.root.description": "Der Kern und die Geschichte des Spiels", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamantrüstung rettet Leben", "advancements.story.shiny_gear.title": "<PERSON><PERSON> mich mit Diamanten", "advancements.story.smelt_iron.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> im Ofen zu einem Eisenbarren", "advancements.story.smelt_iron.title": "Metallurgie", "advancements.story.upgrade_tools.description": "<PERSON>elle eine bessere <PERSON>hacke her", "advancements.story.upgrade_tools.title": "Technischer Fortschritt", "advancements.toast.challenge": "Aufgabe erledigt!", "advancements.toast.goal": "<PERSON><PERSON> erreicht!", "advancements.toast.task": "Fortsch<PERSON>t erzielt!", "argument.anchor.invalid": "Ungültige Objektankerposition ‚%s‘", "argument.angle.incomplete": "Unvollständig (1 Blickwinkel erwartet)", "argument.angle.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.block.id.invalid": "Unbekannter Block ‚%s‘", "argument.block.property.duplicate": "Zustand ‚%s‘ kann nur einmal für den Block %s gesetzt werden", "argument.block.property.invalid": "Block %s ak<PERSON>ptiert ‚%s‘ für Blockzustand ‚%s‘ nicht", "argument.block.property.novalue": "Fehlender Wert für Zustand ‚%s‘ des Blocks %s", "argument.block.property.unclosed": "Schließende Klammer ] für Blockzustand erwartet", "argument.block.property.unknown": "Block %s besitzt den Zustand ‚%s‘ nicht", "argument.block.tag.disallowed": "<PERSON><PERSON> sind hier nicht erlaubt, nur tatsächliche Blöcke", "argument.color.invalid": "Unbekannte Farbe ‚%s‘", "argument.component.invalid": "Ungültige Textkomponente ‚%s‘", "argument.criteria.invalid": "Unbekanntes Kriterium ‚%s‘", "argument.dimension.invalid": "Unbekannte Dimension ‚%s‘", "argument.double.big": "Kommazahl darf nicht größer als %s sein, %s ist zu groß", "argument.double.low": "<PERSON>mmaza<PERSON> darf nicht kleiner als %s sein, %s ist zu klein", "argument.entity.invalid": "Ungültiger Name oder ungültige UUID", "argument.entity.notfound.entity": "<PERSON>s wurde kein Objekt gefunden", "argument.entity.notfound.player": "<PERSON>s wurde kein Spieler gefunden", "argument.entity.options.advancements.description": "Spieler mit Fortschritten", "argument.entity.options.distance.description": "Entfernung zum Objekt", "argument.entity.options.distance.negative": "Entfernung darf nicht negativ sein", "argument.entity.options.dx.description": "Objekte zwischen X und X + dX", "argument.entity.options.dy.description": "Objekte zwischen Y und Y + dY", "argument.entity.options.dz.description": "Objekte zwischen Z und Z + dZ", "argument.entity.options.gamemode.description": "Spieler mit Spielmodus", "argument.entity.options.inapplicable": "Filter ‚%s‘ ist hier nicht anwendbar", "argument.entity.options.level.description": "Erfahrungsstufe", "argument.entity.options.level.negative": "Erfahrungsstufe darf nicht negativ sein", "argument.entity.options.limit.description": "Maximale Anzahl zurückzuliefernder Objekte", "argument.entity.options.limit.toosmall": "Limit muss mindestens 1 betragen", "argument.entity.options.mode.invalid": "Ungültiger oder unbekannter Spielmodus ‚%s‘", "argument.entity.options.name.description": "Objektname", "argument.entity.options.nbt.description": "Objekte mit NBT", "argument.entity.options.predicate.description": "Benutzerdefiniertes Prädikat", "argument.entity.options.scores.description": "Objekte mit Punkteständen", "argument.entity.options.sort.description": "Objekte sortieren", "argument.entity.options.sort.irreversible": "Ungültige oder unbekannte Sortierung ‚%s‘", "argument.entity.options.tag.description": "Objekte mit Etikett", "argument.entity.options.team.description": "Objekte im Team", "argument.entity.options.type.description": "Objekte des Typs", "argument.entity.options.type.invalid": "Ungültiger oder unbekannter Objekttyp ‚%s‘", "argument.entity.options.unknown": "Unbekannter Filter ‚%s‘", "argument.entity.options.unterminated": "Schließende Klammer ] für Filter erwartet", "argument.entity.options.valueless": "Wert für Filter ‚%s‘ erwartet", "argument.entity.options.x.description": "X-Position", "argument.entity.options.x_rotation.description": "Kopfneigung des Objekts", "argument.entity.options.y.description": "Y-Position", "argument.entity.options.y_rotation.description": "Blickrichtung des Objekts", "argument.entity.options.z.description": "Z-Position", "argument.entity.selector.allEntities": "Alle Objekte", "argument.entity.selector.allPlayers": "<PERSON><PERSON> S<PERSON>ler", "argument.entity.selector.missing": "Fehlender Selektor", "argument.entity.selector.nearestEntity": "Nächstes Objekt", "argument.entity.selector.nearestPlayer": "Nächster Spieler", "argument.entity.selector.not_allowed": "Selektor nicht erlaubt", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.self": "Aktuelles Objekt", "argument.entity.selector.unknown": "Unbekannter Selektor ‚%s‘", "argument.entity.toomany": "Nur ein Objekt ist erlaubt, aber der angegebene Selektor könnte mehrere liefern", "argument.enum.invalid": "Ungültiger Wert „%s“", "argument.float.big": "Kommazahl darf nicht größer als %s sein, %s ist zu groß", "argument.float.low": "<PERSON>mmaza<PERSON> darf nicht kleiner als %s sein, %s ist zu klein", "argument.gamemode.invalid": "Unbekannter Spielmodus ‚%s‘", "argument.hexcolor.invalid": "Ungültiger Hex‐Farbcode ‚%s‘", "argument.id.invalid": "Ungültige ID", "argument.id.unknown": "Unbekannte ID ‚%s‘", "argument.integer.big": "G<PERSON><PERSON><PERSON> darf nicht größer als %s sein, %s ist zu groß", "argument.integer.low": "<PERSON><PERSON><PERSON><PERSON> darf nicht kleiner als %s sein, %s ist zu klein", "argument.item.id.invalid": "Unbekannter Gegenstand ‚%s‘", "argument.item.tag.disallowed": "<PERSON><PERSON> sind hier nicht erlaubt, nur tatsächliche Gegenstände", "argument.literal.incorrect": "Zeichenfolge ‚%s‘ erwartet", "argument.long.big": "Lange Ganzzahl darf nicht größer als %s sein, %s ist zu groß", "argument.long.low": "<PERSON> darf nicht kleiner als %s sein, %s ist zu klein", "argument.message.too_long": "Chatnachricht war zu lang (%s > höchstens %s Zeichen)", "argument.nbt.array.invalid": "Ungültiger Array-Typ ‚%s‘", "argument.nbt.array.mixed": "%s kann nicht in %s e<PERSON>fügt werden", "argument.nbt.expected.compound": "Verbunddaten erwartet", "argument.nbt.expected.key": "Eigenschaft erwartet", "argument.nbt.expected.value": "<PERSON><PERSON> erwartet", "argument.nbt.list.mixed": "%s kann nicht in Liste von %s eingefügt werden", "argument.nbt.trailing": "Unerwartete nachfolgende Daten", "argument.player.entities": "Dieser Befehl kann nur auf Spieler angewandt werden, der angegebene Selektor schließt aber auch Objekte mit ein", "argument.player.toomany": "Nur ein Spieler ist erlaubt, aber der angegebene Selektor könnte mehrere liefern", "argument.player.unknown": "Dieser Spieler existiert nicht", "argument.pos.missing.double": "Koordinate erwartet", "argument.pos.missing.int": "Blockposition erwartet", "argument.pos.mixed": "Lokale und globale Koordinaten dürfen nicht gemischt werden (entweder alles mit ^ oder ohne)", "argument.pos.outofbounds": "Diese Position liegt außerhalb des erlaubten Bereichs.", "argument.pos.outofworld": "Diese Position befindet sich außerhalb der Welt", "argument.pos.unloaded": "Diese Position ist nicht geladen", "argument.pos2d.incomplete": "Unvollständig (2 Koordinaten erwartet)", "argument.pos3d.incomplete": "Unvollständig (3 Koordinaten erwartet)", "argument.range.empty": "Wert oder Wertebereich erwartet", "argument.range.ints": "Nur Ganzzahlen ohne Nachkommastellen erlaubt", "argument.range.swapped": "Minimalwert darf nicht größer als der Maximalwert sein", "argument.resource.invalid_type": "Element ‚%s‘ hat einen falschen Typ ‚%s‘ (‚%s‘ erwartet)", "argument.resource.not_found": "Element ‚%s‘ des Typs ‚%s‘ nicht gefunden", "argument.resource_or_id.failed_to_parse": "Datenstruktur konnte nicht ausgewertet werden: %s", "argument.resource_or_id.invalid": "Ungültige ID oder ungültiges Etikett", "argument.resource_or_id.no_such_element": "Element ‚%s‘ in Register ‚%s‘ nicht gefunden", "argument.resource_selector.not_found": "<PERSON>ine Übereinstimmungen für Selektor ‚%s‘ vom Typ ‚%s‘", "argument.resource_tag.invalid_type": "<PERSON><PERSON>kett ‚%s‘ hat einen falschen Typ ‚%s‘ (‚%s‘ erwartet)", "argument.resource_tag.not_found": "Etikett ‚%s‘ des Typs ‚%s‘ nicht gefunden", "argument.rotation.incomplete": "Unvollständig (2 Rotationsangaben erwartet)", "argument.scoreHolder.empty": "<PERSON>s wurden keine relevanten Punktehalter gefunden", "argument.scoreboardDisplaySlot.invalid": "Unbekannte Anzeigeposition ‚%s‘", "argument.style.invalid": "Ungültige Schriftauszeichnung: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON><PERSON> der Ticks darf nicht negativ sein", "argument.time.invalid_unit": "Ungültige Einheit", "argument.time.tick_count_too_low": "<PERSON><PERSON><PERSON> der Ticks darf nicht kleiner als %s sein, %s ist zu klein", "argument.uuid.invalid": "Ungültige UUID", "argument.waypoint.invalid": "Das ausgewählte Objekt ist kein Wegpunkt", "arguments.block.tag.unknown": "Unbekannter Blockalias ‚%s‘", "arguments.function.tag.unknown": "Unbekannter Funktionsalias ‚%s‘", "arguments.function.unknown": "Unbekannte Funktion ‚%s‘", "arguments.item.component.expected": "Gegenstandskomponente erwartet", "arguments.item.component.malformed": "Fehlerhafte ‚%s‘‐Komponente: ‚%s‘", "arguments.item.component.repeated": "Gegenstandskomponente ‚%s‘ wurde wied<PERSON>, es darf jedoch nur ein Wert angegeben werden", "arguments.item.component.unknown": "Unbekannte Gegenstandskomponente ‚%s‘", "arguments.item.malformed": "Fehlerhafter Gegenstand: ‚%s‘", "arguments.item.overstacked": "Maximale Stapelgröße von %s ist %s", "arguments.item.predicate.malformed": "Fehlerhaftes ‚%s‘‐Prädikat: ‚%s‘", "arguments.item.predicate.unknown": "Unbekanntes Gegenstandsprädikat ‚%s‘", "arguments.item.tag.unknown": "Unbekannter Gegenstandsalias ‚%s‘", "arguments.nbtpath.node.invalid": "Ungültiges Element im NBT-Pfad", "arguments.nbtpath.nothing_found": "<PERSON>s wurden keine Elemente gefunden, die %s entsprechen", "arguments.nbtpath.too_deep": "Resultierender NBT zu tief verschachtelt", "arguments.nbtpath.too_large": "Resultierender NBT zu groß", "arguments.objective.notFound": "Unbekanntes Punkte‐Ziel ‚%s‘", "arguments.objective.readonly": "Das Punkte‐Ziel %s kann nur ausgelesen werden", "arguments.operation.div0": "Durch <PERSON>ull kann nicht geteilt werden", "arguments.operation.invalid": "Ungültige Operation", "arguments.swizzle.invalid": "Ungültige Achsenkombination von X, <PERSON> und Z", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s %% %s", "attribute.modifier.equals.2": "%s %% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s %% %s", "attribute.modifier.plus.2": "+%s %% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s %% %s", "attribute.modifier.take.2": "-%s %% %s", "attribute.name.armor": "Rüstung", "attribute.name.armor_toughness": "Rüstungshärte", "attribute.name.attack_damage": "Angriffsschaden", "attribute.name.attack_knockback": "Angriffs<PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_speed": "Angriffsgeschwindigkeit", "attribute.name.block_break_speed": "Blockabbaugeschwindigkeit", "attribute.name.block_interaction_range": "Block‐Interaktionsweite", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "Kameraabstand", "attribute.name.entity_interaction_range": "Objekt‐Interaktionsweite", "attribute.name.explosion_knockback_resistance": "Explosionsstandfestigkeit", "attribute.name.fall_damage_multiplier": "Fallschaden‐Multiplikator", "attribute.name.flying_speed": "Fluggeschwindigkeit", "attribute.name.follow_range": "Kreaturen‐Verfolgedistanz", "attribute.name.generic.armor": "Rüstung", "attribute.name.generic.armor_toughness": "Rüstungshärte", "attribute.name.generic.attack_damage": "Angriffsschaden", "attribute.name.generic.attack_knockback": "Angriffs<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_speed": "Angriffsgeschwindigkeit", "attribute.name.generic.block_interaction_range": "Block‐Interaktionsweite", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Objekt‐Interaktionsweite", "attribute.name.generic.explosion_knockback_resistance": "Explosionsstandfestigkeit", "attribute.name.generic.fall_damage_multiplier": "Fallschaden‐Multiplikator", "attribute.name.generic.flying_speed": "Fluggeschwindigkeit", "attribute.name.generic.follow_range": "Kreaturen‐Verfolgedistanz", "attribute.name.generic.gravity": "Schwerkraft", "attribute.name.generic.jump_strength": "Sprungstärke", "attribute.name.generic.knockback_resistance": "Standfestigkeit", "attribute.name.generic.luck": "Glück", "attribute.name.generic.max_absorption": "Maximale Absorption", "attribute.name.generic.max_health": "Maximale Gesundheit", "attribute.name.generic.movement_efficiency": "Bewegungseffizienz", "attribute.name.generic.movement_speed": "Geschwindigkeit", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.generic.scale": "Größe", "attribute.name.generic.step_height": "Schritthöhe", "attribute.name.generic.water_movement_efficiency": "Wasser‐Bewegungseffizienz", "attribute.name.gravity": "Schwerkraft", "attribute.name.horse.jump_strength": "Pferdesprungstärke", "attribute.name.jump_strength": "Sprungstärke", "attribute.name.knockback_resistance": "Standfestigkeit", "attribute.name.luck": "Glück", "attribute.name.max_absorption": "Maximale Absorption", "attribute.name.max_health": "Maximale Gesundheit", "attribute.name.mining_efficiency": "A<PERSON>ueff<PERSON><PERSON>z", "attribute.name.movement_efficiency": "Bewegungseffizienz", "attribute.name.movement_speed": "Geschwindigkeit", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blockabbaugeschwindigkeit", "attribute.name.player.block_interaction_range": "Block‐Interaktionsweite", "attribute.name.player.entity_interaction_range": "Objekt‐Interaktionsweite", "attribute.name.player.mining_efficiency": "A<PERSON>ueff<PERSON><PERSON>z", "attribute.name.player.sneaking_speed": "Schleichgeschwindigkeit", "attribute.name.player.submerged_mining_speed": "Unterwasser‐Abbaugeschwindigkeit", "attribute.name.player.sweeping_damage_ratio": "Schwungschadensverhältnis", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.scale": "Größe", "attribute.name.sneaking_speed": "Schleichgeschwindigkeit", "attribute.name.spawn_reinforcements": "Zombie‐Verstärkung", "attribute.name.step_height": "Schritthöhe", "attribute.name.submerged_mining_speed": "Unterwasser‐Abbaugeschwindigkeit", "attribute.name.sweeping_damage_ratio": "Schwungschadensverhältnis", "attribute.name.tempt_range": "Kreaturen‐Anlockdistanz", "attribute.name.water_movement_efficiency": "Wasser‐Bewegungseffizienz", "attribute.name.waypoint_receive_range": "Wegpunkt‐Empfangsreichweite", "attribute.name.waypoint_transmit_range": "Wegpunkt‐Sendereichweite", "attribute.name.zombie.spawn_reinforcements": "Zombie-Verstärkung", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Bambusdschungel", "biome.minecraft.basalt_deltas": "Basaltdeltas", "biome.minecraft.beach": "Strand", "biome.minecraft.birch_forest": "Birkenwald", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON>", "biome.minecraft.crimson_forest": "Karmesinwald", "biome.minecraft.dark_forest": "Dunkler Wald", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Tropfsteinhöhlen", "biome.minecraft.end_barrens": "End-Kargland", "biome.minecraft.end_highlands": "End-Hochland", "biome.minecraft.end_midlands": "End-<PERSON><PERSON><PERSON>", "biome.minecraft.eroded_badlands": "Abgetragene Tafelberge", "biome.minecraft.flower_forest": "Blumenwald", "biome.minecraft.forest": "<PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON>ereist<PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Eiszapfentundra", "biome.minecraft.jagged_peaks": "Zerklüftete Gipfel", "biome.minecraft.jungle": "D<PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "Üppige Höhlen", "biome.minecraft.mangrove_swamp": "Mangrovensumpf", "biome.minecraft.meadow": "Alm", "biome.minecraft.mushroom_fields": "Pilzland", "biome.minecraft.nether_wastes": "Nether-Ödland", "biome.minecraft.ocean": "<PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Birken-Urwald", "biome.minecraft.old_growth_pine_taiga": "Kiefern-Urtaiga", "biome.minecraft.old_growth_spruce_taiga": "Fichten-Urtaiga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Fluss", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Savannenhochebene", "biome.minecraft.small_end_islands": "Kleine Endinseln", "biome.minecraft.snowy_beach": "Verschneiter Strand", "biome.minecraft.snowy_plains": "Verschneite Ebene", "biome.minecraft.snowy_slopes": "Verschneite Hänge", "biome.minecraft.snowy_taiga": "Verschneite Taiga", "biome.minecraft.soul_sand_valley": "Seelensandtal", "biome.minecraft.sparse_jungle": "<PERSON>cht<PERSON> D<PERSON>ungel", "biome.minecraft.stony_peaks": "<PERSON><PERSON>", "biome.minecraft.stony_shore": "<PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Sonnenblumenebene", "biome.minecraft.swamp": "Sumpf", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON>", "biome.minecraft.the_void": "<PERSON>", "biome.minecraft.warm_ocean": "Warmer <PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "Zerzauste Geröllhügel", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "Bewaldete Ta<PERSON>lberge", "block.minecraft.acacia_button": "Akazienholzknopf", "block.minecraft.acacia_door": "Akazienholztür", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "Akazienholzza<PERSON>or", "block.minecraft.acacia_hanging_sign": "Akazienholzhängeschild", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Akazienstamm", "block.minecraft.acacia_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "Akazienholzdruckplatte", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_sign": "Akazienholzschild", "block.minecraft.acacia_slab": "Akazienholzstufe", "block.minecraft.acacia_stairs": "Akazienholztreppe", "block.minecraft.acacia_trapdoor": "Akazienholzfalltür", "block.minecraft.acacia_wall_hanging_sign": "Akazienholzwandhängeschild", "block.minecraft.acacia_wall_sign": "Akazienholzwan<PERSON>child", "block.minecraft.acacia_wood": "Akazienholz", "block.minecraft.activator_rail": "Aktivierungsschiene", "block.minecraft.air": "Luft", "block.minecraft.allium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Amethystblock", "block.minecraft.amethyst_cluster": "Amethys<PERSON><PERSON><PERSON>", "block.minecraft.ancient_debris": "<PERSON><PERSON>", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Andesitstufe", "block.minecraft.andesite_stairs": "Andesittreppe", "block.minecraft.andesite_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Melonenranke", "block.minecraft.attached_pumpkin_stem": "Kürbisranke", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "Porz<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblock", "block.minecraft.bamboo_button": "Bambusknopf", "block.minecraft.bamboo_door": "Bambustür", "block.minecraft.bamboo_fence": "Bambuszaun", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_hanging_sign": "Bambush<PERSON><PERSON>child", "block.minecraft.bamboo_mosaic": "Bambusmosaik", "block.minecraft.bamboo_mosaic_slab": "Bambusmosaikstufe", "block.minecraft.bamboo_mosaic_stairs": "Bambusmosaiktreppe", "block.minecraft.bamboo_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_pressure_plate": "Bambusdruckplatte", "block.minecraft.bamboo_sapling": "Bambussprössling", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_slab": "Bambusstufe", "block.minecraft.bamboo_stairs": "Bambustreppe", "block.minecraft.bamboo_trapdoor": "Bambusfalltür", "block.minecraft.bamboo_wall_hanging_sign": "Bambuswandh<PERSON><PERSON>child", "block.minecraft.bamboo_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.black": "Vollständig schwarzes Feld", "block.minecraft.banner.base.blue": "Vollständig blau<PERSON>", "block.minecraft.banner.base.brown": "Vollständig braunes Feld", "block.minecraft.banner.base.cyan": "Vollständig türkises Feld", "block.minecraft.banner.base.gray": "Vollständig gra<PERSON>", "block.minecraft.banner.base.green": "Vollständig grü<PERSON> Feld", "block.minecraft.banner.base.light_blue": "Vollständig hellbla<PERSON> Feld", "block.minecraft.banner.base.light_gray": "Vollständig <PERSON>", "block.minecraft.banner.base.lime": "Vollständig hellgrünes Feld", "block.minecraft.banner.base.magenta": "Vollständig magenta Feld", "block.minecraft.banner.base.orange": "Vollständig oranges Feld", "block.minecraft.banner.base.pink": "Vollständig rosa <PERSON>", "block.minecraft.banner.base.purple": "Vollständig violettes Feld", "block.minecraft.banner.base.red": "Vollständig rotes Feld", "block.minecraft.banner.base.white": "Vollständig weißes Feld", "block.minecraft.banner.base.yellow": "Vollständig gelbes Feld", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON>", "block.minecraft.banner.border.cyan": "Türkiser Bord", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON> Bord", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.lime": "Hellgr<PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON>", "block.minecraft.banner.border.pink": "<PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON>ld schwarz gemauert", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> blau gema<PERSON>t", "block.minecraft.banner.bricks.brown": "<PERSON>ld braun gemauert", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON> türk<PERSON> g<PERSON>t", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON> grau gemauert", "block.minecraft.banner.bricks.green": "<PERSON><PERSON> grün g<PERSON>t", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON> hellblau gemauert", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON> g<PERSON>t", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON> g<PERSON>t", "block.minecraft.banner.bricks.magenta": "Feld magenta gemauert", "block.minecraft.banner.bricks.orange": "Feld orange gemauert", "block.minecraft.banner.bricks.pink": "<PERSON>ld rosa gemauert", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON> violett gema<PERSON>t", "block.minecraft.banner.bricks.red": "<PERSON>ld rot gemauert", "block.minecraft.banner.bricks.white": "<PERSON><PERSON> weiß gemauert", "block.minecraft.banner.bricks.yellow": "<PERSON>ld gelb gemauert", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Türkise Ku<PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Hellblaue Kugel", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Orange Kugel", "block.minecraft.banner.circle.pink": "<PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON> Kugel", "block.minecraft.banner.circle.white": "Weiße Kugel", "block.minecraft.banner.circle.yellow": "Gelbe Kugel", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Türkiser Creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "Hellblauer Creeper", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON> Creeper", "block.minecraft.banner.creeper.lime": "Hellgrüner Creeper", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON>", "block.minecraft.banner.creeper.pink": "<PERSON>", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Schwarz<PERSON>", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Türkises Andreaskreuz", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "Hellgrünes <PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.orange": "Oranges <PERSON>uz", "block.minecraft.banner.cross.pink": "<PERSON>", "block.minecraft.banner.cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.cross.red": "Rotes Andreaskreuz", "block.minecraft.banner.cross.white": "Weißes Andreaskreuz", "block.minecraft.banner.cross.yellow": "Gelbes Andreaskreuz", "block.minecraft.banner.curly_border.black": "Sc<PERSON><PERSON><PERSON>d", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.cyan": "Türkiser Spickelbord", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON>pic<PERSON>bord", "block.minecraft.banner.curly_border.light_blue": "Hellblauer Spickelbord", "block.minecraft.banner.curly_border.light_gray": "Hellgrauer Spickelbord", "block.minecraft.banner.curly_border.lime": "Hellgrüner Spickelbord", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.pink": "<PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON> schräglinks geteilt", "block.minecraft.banner.diagonal_left.brown": "<PERSON> schräglinks geteilt", "block.minecraft.banner.diagonal_left.cyan": "T<PERSON><PERSON>is schrä<PERSON>nks geteilt", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_left.light_blue": "Hellblau schräglinks geteilt", "block.minecraft.banner.diagonal_left.light_gray": "Hellgrau schräglinks geteilt", "block.minecraft.banner.diagonal_left.lime": "Hellgrün schräglinks geteilt", "block.minecraft.banner.diagonal_left.magenta": "Ma<PERSON>a schr<PERSON><PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_left.orange": "Orange schräglinks geteilt", "block.minecraft.banner.diagonal_left.pink": "<PERSON> s<PERSON> get<PERSON>t", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON> s<PERSON><PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_left.red": "<PERSON>ot schräglinks geteilt", "block.minecraft.banner.diagonal_left.white": "<PERSON>ß schräglinks geteilt", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON> schr<PERSON>g<PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_right.blue": "Blau schrägrechts geteilt", "block.minecraft.banner.diagonal_right.brown": "<PERSON> schrägrechts geteilt", "block.minecraft.banner.diagonal_right.cyan": "Türkis schrägrechts geteilt", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON> schr<PERSON>g<PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON> schr<PERSON>g<PERSON>s geteilt", "block.minecraft.banner.diagonal_right.light_blue": "Hellblau schrägrechts geteilt", "block.minecraft.banner.diagonal_right.light_gray": "Hellgrau schrägrechts geteilt", "block.minecraft.banner.diagonal_right.lime": "Hellgrün schrägrechts geteilt", "block.minecraft.banner.diagonal_right.magenta": "Magenta schrägrechts geteilt", "block.minecraft.banner.diagonal_right.orange": "Orange schrägrechts geteilt", "block.minecraft.banner.diagonal_right.pink": "<PERSON> s<PERSON><PERSON> get<PERSON>t", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON> s<PERSON><PERSON>g<PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_right.red": "Rot schrägrechts geteilt", "block.minecraft.banner.diagonal_right.white": "<PERSON>ß schrägrechts geteilt", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON>g<PERSON><PERSON> geteilt", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON> schr<PERSON>g<PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.blue": "Blau schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON> schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.cyan": "Türkis schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON> schräg<PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> schr<PERSON>g<PERSON>s geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.light_blue": "Hellblau schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.light_gray": "Hellgrau schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.lime": "Hellgrün schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.orange": "Orange schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON> s<PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON> s<PERSON>r<PERSON>g<PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.red": "Rot schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.white": "<PERSON>ß schrägrechts geteilt (invertiert)", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON> schräg<PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.blue": "Blau schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON> schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.cyan": "Türkis schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.light_blue": "Hellblau schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.light_gray": "Hellgrau schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.lime": "Hellgrün schrä<PERSON>nks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta schrä<PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.orange": "Orange schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON> s<PERSON> get<PERSON>t (invertiert)", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON> s<PERSON>r<PERSON> geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.red": "<PERSON>ot schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.white": "<PERSON>ß schräglinks geteilt (invertiert)", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> geteilt (invertiert)", "block.minecraft.banner.flow.black": "Schwarzer Fluss", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.brown": "Brauner Fluss", "block.minecraft.banner.flow.cyan": "Türkiser Fluss", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> Flus<PERSON>", "block.minecraft.banner.flow.green": "<PERSON>r<PERSON><PERSON> Fluss", "block.minecraft.banner.flow.light_blue": "Hellblauer Fluss", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> Fluss", "block.minecraft.banner.flow.lime": "Hellgrüner Fluss", "block.minecraft.banner.flow.magenta": "Magenta Fluss", "block.minecraft.banner.flow.orange": "Oranger Fluss", "block.minecraft.banner.flow.pink": "<PERSON>", "block.minecraft.banner.flow.purple": "<PERSON><PERSON> Fluss", "block.minecraft.banner.flow.red": "<PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> Fluss", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON> F<PERSON>", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON>ü<PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON> Blu<PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.orange": "Orange Blume", "block.minecraft.banner.flower.pink": "<PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON>", "block.minecraft.banner.flower.white": "Weiße Blume", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "Schwarzer Globus", "block.minecraft.banner.globe.blue": "Blauer Globus", "block.minecraft.banner.globe.brown": "Brauner Globus", "block.minecraft.banner.globe.cyan": "Türkiser Globus", "block.minecraft.banner.globe.gray": "Grauer Globus", "block.minecraft.banner.globe.green": "Grüner Globus", "block.minecraft.banner.globe.light_blue": "Hellblauer Globus", "block.minecraft.banner.globe.light_gray": "Hellgrauer Globus", "block.minecraft.banner.globe.lime": "Hellgrüner Globus", "block.minecraft.banner.globe.magenta": "Magenta Globus", "block.minecraft.banner.globe.orange": "Oranger Globus", "block.minecraft.banner.globe.pink": "Rosa Globus", "block.minecraft.banner.globe.purple": "Violetter Globus", "block.minecraft.banner.globe.red": "Roter Globus", "block.minecraft.banner.globe.white": "Weißer Globus", "block.minecraft.banner.globe.yellow": "Gelber Globus", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Türkiser Farbverlauf", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Hellblauer Farbverlauf", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON> Farbverlauf", "block.minecraft.banner.gradient.lime": "Hellgr<PERSON><PERSON>lau<PERSON>", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "Sc<PERSON><PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON>lau<PERSON> (invertiert)", "block.minecraft.banner.gradient_up.cyan": "Türkiser Farbverlauf (invertiert)", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON>lau<PERSON> (invertiert)", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.light_blue": "Hellblauer Farbverlauf (invertiert)", "block.minecraft.banner.gradient_up.light_gray": "Hell<PERSON>uer Farbverlauf (invertiert)", "block.minecraft.banner.gradient_up.lime": "Hellgrü<PERSON>lau<PERSON> (invertiert)", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.pink": "<PERSON> (invertiert)", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON> (invertiert)", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON> (invertiert)", "block.minecraft.banner.guster.black": "Schwarzer Windstoßer", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON> Windstoßer", "block.minecraft.banner.guster.cyan": "Türkiser Windstoßer", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON> Windsto<PERSON>", "block.minecraft.banner.guster.light_blue": "Hellblauer Windstoßer", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON>uer Windstoßer", "block.minecraft.banner.guster.lime": "Hellgrüner Windstoßer", "block.minecraft.banner.guster.magenta": "Magenta Windstoßer", "block.minecraft.banner.guster.orange": "<PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> Windstoßer", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON> schwarz get<PERSON>t", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON> blau <PERSON>t", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON> braun geteilt", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON> türk<PERSON> get<PERSON>t", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON> grau get<PERSON>t", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON> grün <PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON> hell<PERSON> get<PERSON>t", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON> hell<PERSON> get<PERSON>t", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON>t", "block.minecraft.banner.half_horizontal.magenta": "Oben magenta geteilt", "block.minecraft.banner.half_horizontal.orange": "<PERSON>ben orange geteilt", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON> rosa get<PERSON>t", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON> violett geteilt", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON> rot <PERSON>t", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON> weiß get<PERSON>t", "block.minecraft.banner.half_horizontal.yellow": "<PERSON>ben gelb geteilt", "block.minecraft.banner.half_horizontal_bottom.black": "Unten schwarz geteilt", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON>ten blau get<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.brown": "Unten braun geteilt", "block.minecraft.banner.half_horizontal_bottom.cyan": "Unten türkis geteilt", "block.minecraft.banner.half_horizontal_bottom.gray": "Unten grau get<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON>ten grün get<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Unten hellblau geteilt", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Unten hellgrau geteilt", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON>ten hell<PERSON>ün get<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.magenta": "Unten magenta geteilt", "block.minecraft.banner.half_horizontal_bottom.orange": "Unten orange geteilt", "block.minecraft.banner.half_horizontal_bottom.pink": "Unten rosa geteilt", "block.minecraft.banner.half_horizontal_bottom.purple": "Unten violett geteilt", "block.minecraft.banner.half_horizontal_bottom.red": "Unten rot geteilt", "block.minecraft.banner.half_horizontal_bottom.white": "Unten weiß geteilt", "block.minecraft.banner.half_horizontal_bottom.yellow": "Unten gelb geteilt", "block.minecraft.banner.half_vertical.black": "Rechts schwarz gespalten", "block.minecraft.banner.half_vertical.blue": "Rechts blau gespalten", "block.minecraft.banner.half_vertical.brown": "Rechts braun gespalten", "block.minecraft.banner.half_vertical.cyan": "Rechts türkis gespalten", "block.minecraft.banner.half_vertical.gray": "Rechts grau gespalten", "block.minecraft.banner.half_vertical.green": "Rechts grün ges<PERSON>ten", "block.minecraft.banner.half_vertical.light_blue": "Rechts hellblau gespalten", "block.minecraft.banner.half_vertical.light_gray": "Rechts hellgrau gespalten", "block.minecraft.banner.half_vertical.lime": "Rechts hellgrün ges<PERSON>ten", "block.minecraft.banner.half_vertical.magenta": "Rechts magenta gespalten", "block.minecraft.banner.half_vertical.orange": "Rechts orange gespalten", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> rosa gespalten", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON>s violett ges<PERSON>ten", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>s rot gespalten", "block.minecraft.banner.half_vertical.white": "Rechts weiß gespalten", "block.minecraft.banner.half_vertical.yellow": "Rechts gelb gespalten", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON> schwarz gespalten", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> blau ges<PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON> braun gespalten", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON> türkis g<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON> grau ges<PERSON>", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON> gr<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ges<PERSON>", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON> <PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "Links magenta gespalten", "block.minecraft.banner.half_vertical_right.orange": "<PERSON>s orange gespalten", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON> rosa g<PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> violet<PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON>s rot gespalten", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON> weiß ges<PERSON>ten", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> gelb ges<PERSON>ten", "block.minecraft.banner.mojang.black": "Schwarz<PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Türkises Mojang-Logo", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON>ues Mojang-Logo", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "Hellgrünes Mo<PERSON>g-Logo", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "Oranges Mojang-Logo", "block.minecraft.banner.mojang.pink": "<PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>", "block.minecraft.banner.mojang.red": "Rotes Mojang-Logo", "block.minecraft.banner.mojang.white": "Weißes Mojang-Logo", "block.minecraft.banner.mojang.yellow": "Gelbes Mojang-Logo", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "Türk<PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Hellblaue Sc<PERSON>auze", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.orange": "Orange Schnauze", "block.minecraft.banner.piglin.pink": "<PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>", "block.minecraft.banner.piglin.red": "Rote <PERSON>ze", "block.minecraft.banner.piglin.white": "Weiße Schnauze", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "Hell<PERSON><PERSON> Ra<PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Orange Raute", "block.minecraft.banner.rhombus.pink": "<PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "Weiße Raute", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "Türkiser Schädel", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "Hellblauer Schädel", "block.minecraft.banner.skull.light_gray": "Hell<PERSON>uer Schädel", "block.minecraft.banner.skull.lime": "Hellgrüner Schädel", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON>", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "Vier schwarze Pfähle", "block.minecraft.banner.small_stripes.blue": "<PERSON>ier blaue <PERSON>", "block.minecraft.banner.small_stripes.brown": "Vier braune Pfähle", "block.minecraft.banner.small_stripes.cyan": "Vier türkise Pfähle", "block.minecraft.banner.small_stripes.gray": "Vier graue Pfähle", "block.minecraft.banner.small_stripes.green": "Vier grüne <PERSON>e", "block.minecraft.banner.small_stripes.light_blue": "<PERSON>ier hellblaue Pfähle", "block.minecraft.banner.small_stripes.light_gray": "<PERSON>ier hellgraue Pfähle", "block.minecraft.banner.small_stripes.lime": "Vier hellgrüne <PERSON>e", "block.minecraft.banner.small_stripes.magenta": "Vier magenta Pfähle", "block.minecraft.banner.small_stripes.orange": "Vier orange Pfähle", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON> rosa <PERSON>", "block.minecraft.banner.small_stripes.purple": "Vier violette Pfähle", "block.minecraft.banner.small_stripes.red": "Vier rote Pfähle", "block.minecraft.banner.small_stripes.white": "Vier weiße Pfähle", "block.minecraft.banner.small_stripes.yellow": "Vier gelbe Pfähle", "block.minecraft.banner.square_bottom_left.black": "Schwa<PERSON>es rechtes Untereck", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON> rechtes Untereck", "block.minecraft.banner.square_bottom_left.brown": "Braunes rechtes Untereck", "block.minecraft.banner.square_bottom_left.cyan": "Türkises rechtes Untereck", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> recht<PERSON> Untereck", "block.minecraft.banner.square_bottom_left.green": "G<PERSON><PERSON><PERSON> rechtes Untereck", "block.minecraft.banner.square_bottom_left.light_blue": "Hell<PERSON>ues rechtes Untereck", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON> rechtes Untereck", "block.minecraft.banner.square_bottom_left.lime": "Hellgrünes rechtes Untereck", "block.minecraft.banner.square_bottom_left.magenta": "Magenta rechtes Untereck", "block.minecraft.banner.square_bottom_left.orange": "Oranges rechtes Untereck", "block.minecraft.banner.square_bottom_left.pink": "<PERSON> re<PERSON>", "block.minecraft.banner.square_bottom_left.purple": "<PERSON>tes rechtes Untereck", "block.minecraft.banner.square_bottom_left.red": "Rotes rechtes Untereck", "block.minecraft.banner.square_bottom_left.white": "Weißes rechtes Untereck", "block.minecraft.banner.square_bottom_left.yellow": "Gelbes rechtes Untereck", "block.minecraft.banner.square_bottom_right.black": "Schwarz<PERSON> linkes Untereck", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON><PERSON> link<PERSON>", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON> link<PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "Türkises linkes Untereck", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON> <PERSON><PERSON>eck", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON> linkes Untereck", "block.minecraft.banner.square_bottom_right.light_blue": "Hell<PERSON>ues linkes Untereck", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON><PERSON> linkes Untereck", "block.minecraft.banner.square_bottom_right.lime": "Hellgrünes linkes Untereck", "block.minecraft.banner.square_bottom_right.magenta": "Magenta linkes Untereck", "block.minecraft.banner.square_bottom_right.orange": "Oranges linkes <PERSON>", "block.minecraft.banner.square_bottom_right.pink": "<PERSON> linkes <PERSON>", "block.minecraft.banner.square_bottom_right.purple": "<PERSON><PERSON> linkes <PERSON>", "block.minecraft.banner.square_bottom_right.red": "Rotes linkes Untereck", "block.minecraft.banner.square_bottom_right.white": "Weißes linkes Untereck", "block.minecraft.banner.square_bottom_right.yellow": "Gelbes linkes Untereck", "block.minecraft.banner.square_top_left.black": "Schwarzes rechtes Obereck", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON><PERSON> rechtes Obereck", "block.minecraft.banner.square_top_left.brown": "Braunes rechtes Obereck", "block.minecraft.banner.square_top_left.cyan": "Türkises rechtes Obereck", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> rechtes Obereck", "block.minecraft.banner.square_top_left.green": "Gr<PERSON><PERSON> rechtes Obereck", "block.minecraft.banner.square_top_left.light_blue": "Hell<PERSON>ues rechtes Obereck", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON> rechtes Obereck", "block.minecraft.banner.square_top_left.lime": "Hellgrünes rechtes Obereck", "block.minecraft.banner.square_top_left.magenta": "Magenta rechtes Obereck", "block.minecraft.banner.square_top_left.orange": "Oranges rechtes Obereck", "block.minecraft.banner.square_top_left.pink": "<PERSON> re<PERSON> O<PERSON>eck", "block.minecraft.banner.square_top_left.purple": "Violettes rechtes Obereck", "block.minecraft.banner.square_top_left.red": "Rotes rechtes Obereck", "block.minecraft.banner.square_top_left.white": "Weißes rechtes Obereck", "block.minecraft.banner.square_top_left.yellow": "Gelbes rechtes Obereck", "block.minecraft.banner.square_top_right.black": "Schwarzes linkes Obereck", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON><PERSON> linkes <PERSON>", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON> linkes Obereck", "block.minecraft.banner.square_top_right.cyan": "Türkises linkes Obereck", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> linkes O<PERSON>eck", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON><PERSON> linkes Obereck", "block.minecraft.banner.square_top_right.light_blue": "Hellblaues linkes Obereck", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON> linkes Obereck", "block.minecraft.banner.square_top_right.lime": "Hellgrünes linkes Obereck", "block.minecraft.banner.square_top_right.magenta": "Magenta linkes Obereck", "block.minecraft.banner.square_top_right.orange": "Oranges linkes <PERSON>", "block.minecraft.banner.square_top_right.pink": "<PERSON> linkes <PERSON>", "block.minecraft.banner.square_top_right.purple": "<PERSON><PERSON> linkes <PERSON>eck", "block.minecraft.banner.square_top_right.red": "Rotes linkes Obereck", "block.minecraft.banner.square_top_right.white": "Weißes linkes Obereck", "block.minecraft.banner.square_top_right.yellow": "Gelbes linkes Obereck", "block.minecraft.banner.straight_cross.black": "Schwarzes Kreuz", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "Türkises Kreuz", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON>r<PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Hell<PERSON>ues Kreuz", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "Hellgrünes Kreuz", "block.minecraft.banner.straight_cross.magenta": "Magenta Kreuz", "block.minecraft.banner.straight_cross.orange": "Oranges Kreuz", "block.minecraft.banner.straight_cross.pink": "<PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON> K<PERSON>uz", "block.minecraft.banner.straight_cross.red": "Rotes Kreuz", "block.minecraft.banner.straight_cross.white": "Weißes Kreuz", "block.minecraft.banner.straight_cross.yellow": "Gelbes Kreuz", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Türkiser Bannerfuß", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Hellblauer Bannerfuß", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "Hellgr<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "Türkiser Pfahl", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON>hl", "block.minecraft.banner.stripe_center.light_blue": "Hellblauer Pfahl", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON> Pfahl", "block.minecraft.banner.stripe_center.lime": "Hellgrüner Pfahl", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Schwarzer Schräglinksbalken", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON>äglinksbal<PERSON>", "block.minecraft.banner.stripe_downleft.brown": "Brauner Schräglinksbalken", "block.minecraft.banner.stripe_downleft.cyan": "Türkiser Schräglinksbalken", "block.minecraft.banner.stripe_downleft.gray": "G<PERSON>uer Schräglinksbalken", "block.minecraft.banner.stripe_downleft.green": "Grüner Schräglinksbalken", "block.minecraft.banner.stripe_downleft.light_blue": "Hellblauer Schräglinksbalken", "block.minecraft.banner.stripe_downleft.light_gray": "Hellgrauer Schräglinksbalken", "block.minecraft.banner.stripe_downleft.lime": "Hellgrüner Schräglinksbalken", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON>hräglinksbalken", "block.minecraft.banner.stripe_downleft.orange": "Oranger <PERSON>äglinksbal<PERSON>", "block.minecraft.banner.stripe_downleft.pink": "<PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "Roter <PERSON>äglinksbalken", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON>r Schräglinksbalken", "block.minecraft.banner.stripe_downleft.yellow": "Gelber Schräglinksbalken", "block.minecraft.banner.stripe_downright.black": "Schwarzer Schrägbalken", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON>hräg<PERSON>", "block.minecraft.banner.stripe_downright.brown": "Brauner Schrägbalken", "block.minecraft.banner.stripe_downright.cyan": "Türkiser Schrägbalken", "block.minecraft.banner.stripe_downright.gray": "G<PERSON>uer Schrägbalken", "block.minecraft.banner.stripe_downright.green": "Grüner Schrägbalken", "block.minecraft.banner.stripe_downright.light_blue": "Hellblauer Schrägbalken", "block.minecraft.banner.stripe_downright.light_gray": "Hellgrauer Schrägbalken", "block.minecraft.banner.stripe_downright.lime": "Hellgrüner Schrägbalken", "block.minecraft.banner.stripe_downright.magenta": "Magenta Schrägbalken", "block.minecraft.banner.stripe_downright.orange": "Oranger Schrägbal<PERSON>", "block.minecraft.banner.stripe_downright.pink": "<PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "Roter <PERSON>ägbal<PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON>r Schrägbalken", "block.minecraft.banner.stripe_downright.yellow": "Gelber Schrägbalken", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON><PERSON><PERSON> rechte Flanke", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON> rechte <PERSON>", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON> rechte Flanke", "block.minecraft.banner.stripe_left.cyan": "Türkise rechte Flanke", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON> rechte <PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON><PERSON> recht<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Hellblaue rechte Flanke", "block.minecraft.banner.stripe_left.light_gray": "<PERSON><PERSON><PERSON> rechte F<PERSON>e", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> rechte <PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON> rechte F<PERSON>", "block.minecraft.banner.stripe_left.orange": "Orange rechte Flanke", "block.minecraft.banner.stripe_left.pink": "<PERSON> re<PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON> rechte <PERSON>", "block.minecraft.banner.stripe_left.red": "Rote rechte Flanke", "block.minecraft.banner.stripe_left.white": "Weiße rechte Flanke", "block.minecraft.banner.stripe_left.yellow": "Gel<PERSON> rechte Flanke", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON> Balken", "block.minecraft.banner.stripe_middle.cyan": "Türkiser Balken", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Hellblauer Balken", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON> Balken", "block.minecraft.banner.stripe_middle.lime": "Hellgr<PERSON><PERSON> Balken", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Schwarze linke Flanke", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON> linke Flank<PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> linke Flanke", "block.minecraft.banner.stripe_right.cyan": "Türkise linke Flanke", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON> linke Flank<PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON><PERSON> F<PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Hellblaue linke Flanke", "block.minecraft.banner.stripe_right.light_gray": "Hellgraue linke Flanke", "block.minecraft.banner.stripe_right.lime": "Hellgr<PERSON><PERSON> linke Flanke", "block.minecraft.banner.stripe_right.magenta": "Magenta linke Flanke", "block.minecraft.banner.stripe_right.orange": "Orange linke Flanke", "block.minecraft.banner.stripe_right.pink": "<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON> link<PERSON>", "block.minecraft.banner.stripe_right.red": "Rote linke Flanke", "block.minecraft.banner.stripe_right.white": "Weiße linke Flanke", "block.minecraft.banner.stripe_right.yellow": "Gelbe linke Flanke", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "Türkises <PERSON>up<PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "Hellgr<PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Oranges Bannerhaupt", "block.minecraft.banner.stripe_top.pink": "<PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "Weißes Bannerhaupt", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "Schwarze halbe <PERSON>ze", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON> halbe <PERSON>", "block.minecraft.banner.triangle_bottom.brown": "Braune halbe S<PERSON>ze", "block.minecraft.banner.triangle_bottom.cyan": "Türkise halbe Spitze", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON> halbe <PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Hellblaue halbe <PERSON>ze", "block.minecraft.banner.triangle_bottom.light_gray": "Hellgraue halbe <PERSON>ze", "block.minecraft.banner.triangle_bottom.lime": "Hellg<PERSON><PERSON><PERSON> halbe <PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "Magenta halbe <PERSON>", "block.minecraft.banner.triangle_bottom.orange": "Orange halbe Spitze", "block.minecraft.banner.triangle_bottom.pink": "<PERSON> ha<PERSON>", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON> halbe <PERSON>", "block.minecraft.banner.triangle_bottom.red": "Rote halbe S<PERSON>ze", "block.minecraft.banner.triangle_bottom.white": "Weiße halbe Spitze", "block.minecraft.banner.triangle_bottom.yellow": "Gelbe halbe <PERSON>ze", "block.minecraft.banner.triangle_top.black": "Schwarze gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.blue": "Blaue gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.brown": "Braune gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.cyan": "Türkise gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.gray": "Graue gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.green": "Grüne gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.light_blue": "Hellblaue gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.light_gray": "Hellgraue gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.lime": "Hellgrüne gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.magenta": "Magenta gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.orange": "Orange gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.pink": "Rosa gestürzte halbe <PERSON>ze", "block.minecraft.banner.triangle_top.purple": "Violette gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.red": "Rote gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.white": "Weiße gestürzte halbe Spitze", "block.minecraft.banner.triangle_top.yellow": "Gelbe gestürzte halbe Spitze", "block.minecraft.banner.triangles_bottom.black": "Schwarzer gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON>er gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.brown": "Brauner gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.cyan": "Türkiser gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.gray": "Grauer gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.green": "Grüner gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.light_blue": "Hellblauer gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.light_gray": "Hellgrauer gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.lime": "Hellgrüner gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.magenta": "Magenta gespic<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON>", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON> gespickelter <PERSON>", "block.minecraft.banner.triangles_bottom.red": "Roter gespickelter Bannerfuß", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.yellow": "Gelber gespickelter Bannerfuß", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.cyan": "Türkises gespickeltes Bannerhaupt", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>haupt", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_top.lime": "Hellgr<PERSON><PERSON> gespickel<PERSON> Bannerhaupt", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>", "block.minecraft.banner.triangles_top.orange": "Oranges ges<PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON>", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_top.red": "Rotes g<PERSON>", "block.minecraft.banner.triangles_top.white": "Weißes gespickeltes Bannerhaupt", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.barrel": "Fass", "block.minecraft.barrier": "Barriere", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "Primäre Kraft", "block.minecraft.beacon.secondary": "Sekundäre Kraft", "block.minecraft.bed.no_sleep": "Du kannst nur nachts oder während eines Gewitters schlafen", "block.minecraft.bed.not_safe": "Du kannst jetzt nicht schlafen, es sind Monster in der Nähe", "block.minecraft.bed.obstructed": "Dieses <PERSON> ist blockiert", "block.minecraft.bed.occupied": "<PERSON><PERSON> ist belegt", "block.minecraft.bed.too_far_away": "Du kannst jetzt nicht schlafen, das Bett ist zu weit entfernt", "block.minecraft.bedrock": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bee_nest": "B<PERSON>ennest", "block.minecraft.beehive": "Bienenstock", "block.minecraft.beetroots": "Rote Bete", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Großes Tropfblatt", "block.minecraft.big_dripleaf_stem": "Großer Tropfblattstiel", "block.minecraft.birch_button": "Birkenholzknopf", "block.minecraft.birch_door": "Birkenholztür", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence_gate": "Birkenholzzauntor", "block.minecraft.birch_hanging_sign": "Birkenholzh<PERSON><PERSON>child", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Birkenstamm", "block.minecraft.birch_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Birkenholzdruckplatte", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "Birkenholzschild", "block.minecraft.birch_slab": "Birkenholzstufe", "block.minecraft.birch_stairs": "Birkenholztreppe", "block.minecraft.birch_trapdoor": "Birkenholzfalltür", "block.minecraft.birch_wall_hanging_sign": "Birkenholzwandh<PERSON><PERSON>child", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_wood": "Birkenholz", "block.minecraft.black_banner": "Schwarzes Banner", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_candle": "Schwarze Kerze", "block.minecraft.black_candle_cake": "<PERSON>chen mit schwarzer Kerze", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "Schwarze glasierte Keramik", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass_pane": "Schwarz<PERSON>ibe", "block.minecraft.black_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_wool": "Schwa<PERSON><PERSON> W<PERSON>e", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Schwarzsteinstufe", "block.minecraft.blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blast_furnace": "Schmelzofen", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON>chen mit blauer Kerze", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bone_block": "Knochenblock", "block.minecraft.bookshelf": "Bücherregal", "block.minecraft.brain_coral": "Hirnkoralle", "block.minecraft.brain_coral_block": "Hirnkorallenblock", "block.minecraft.brain_coral_fan": "Hirnkorallenfä<PERSON>", "block.minecraft.brain_coral_wall_fan": "<PERSON>rnkorallenwandfächer", "block.minecraft.brewing_stand": "Braustand", "block.minecraft.brick_slab": "Ziegelstufe", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Ziegelsteine", "block.minecraft.brown_banner": "<PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "<PERSON>chen mit brauner Kerze", "block.minecraft.brown_carpet": "<PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "Braune glasierte Keramik", "block.minecraft.brown_mushroom": "<PERSON><PERSON> Pilz", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.brown_terracotta": "<PERSON><PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Blasensäule", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Blasenkorallenblock", "block.minecraft.bubble_coral_fan": "Blasenkorallenfächer", "block.minecraft.bubble_coral_wall_fan": "Blasenkorallenwandfächer", "block.minecraft.budding_amethyst": "Amethystknospenblock", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Kaktusblüte", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Kalibriert<PERSON>k-Sensor", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> mit Kerze", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Geschnitzter Kürbis", "block.minecraft.cauldron": "Kessel", "block.minecraft.cave_air": "Höhlenluft", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON><PERSON>rank<PERSON>", "block.minecraft.cave_vines_plant": "H<PERSON>hlenrankenpflanze", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "Ketten-Befehlsblock", "block.minecraft.cherry_button": "Kirschholzknopf", "block.minecraft.cherry_door": "Kirschholztür", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "Kirschholzdruckplatte", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Kirschholzschild", "block.minecraft.cherry_slab": "Kirschholzstufe", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "Kirschholzfalltür", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "Truhe", "block.minecraft.chipped_anvil": "Angeschlagener Amboss", "block.minecraft.chiseled_bookshelf": "Gearbeitetes Bücherregal", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "Gemeißelte Netherziegel", "block.minecraft.chiseled_polished_blackstone": "Gemeißelter polierter <PERSON>", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON> rot<PERSON>", "block.minecraft.chiseled_resin_bricks": "Gemeißelte Harzziegel", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "Gemeißelte Steinziegel", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "Gemeißelte Tuffsteinziegel", "block.minecraft.chorus_flower": "Chorusblüte", "block.minecraft.chorus_plant": "Choruspflanze", "block.minecraft.clay": "Ton", "block.minecraft.closed_eyeblossom": "Geschlossene Augenblüte", "block.minecraft.coal_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "Bruchtiefenschiefer", "block.minecraft.cobbled_deepslate_slab": "Bruchtiefenschieferstufe", "block.minecraft.cobbled_deepslate_stairs": "Bruchtiefenschiefertreppe", "block.minecraft.cobbled_deepslate_wall": "Bruchtiefenschiefermauer", "block.minecraft.cobblestone": "Bruchstein", "block.minecraft.cobblestone_slab": "Bruchsteinstufe", "block.minecraft.cobblestone_stairs": "Bruchsteintreppe", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobweb": "Spinnennetz", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Befehlsblock", "block.minecraft.comparator": "Redstone-Komparator", "block.minecraft.composter": "Ko<PERSON>ster", "block.minecraft.conduit": "Aquisator", "block.minecraft.copper_block": "<PERSON><PERSON>er<PERSON>", "block.minecraft.copper_bulb": "Kupferleuchte", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON>fall<PERSON>ü<PERSON>", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Rissige Tiefenschieferziegel", "block.minecraft.cracked_deepslate_tiles": "Rissige Tiefenschieferfliesen", "block.minecraft.cracked_nether_bricks": "Rissige Netherziegel", "block.minecraft.cracked_polished_blackstone_bricks": "Rissige polierte Schwarzsteinziegel", "block.minecraft.cracked_stone_bricks": "Rissige Steinziegel", "block.minecraft.crafter": "<PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "Werkbank", "block.minecraft.creaking_heart": "Knarzherz", "block.minecraft.creeper_head": "Creeperkopf", "block.minecraft.creeper_wall_head": "Creeper-Wandkopf", "block.minecraft.crimson_button": "Ka<PERSON><PERSON>nknopf", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "Karmesinpilz", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hyphae": "Karmesinhyphen", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Karmesindruckplatte", "block.minecraft.crimson_roots": "Karmesinwurzeln", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "Karmesinstufe", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Weinender Obsidian", "block.minecraft.cut_copper": "Geschnittener Kupferblock", "block.minecraft.cut_copper_slab": "Geschnittene Kupferstufe", "block.minecraft.cut_copper_stairs": "Geschnittene Kupfertreppe", "block.minecraft.cut_red_sandstone": "Geschnittener roter Sandstein", "block.minecraft.cut_red_sandstone_slab": "Geschnittene rote Sandsteinstufe", "block.minecraft.cut_sandstone": "Geschnittener Sandstein", "block.minecraft.cut_sandstone_slab": "Geschnittene Sandsteinstufe", "block.minecraft.cyan_banner": "Türkises Banner", "block.minecraft.cyan_bed": "Türkises Bett", "block.minecraft.cyan_candle": "Türkise Kerze", "block.minecraft.cyan_candle_cake": "<PERSON>chen mit türkiser Kerze", "block.minecraft.cyan_carpet": "Türkiser Teppich", "block.minecraft.cyan_concrete": "Türkiser <PERSON>", "block.minecraft.cyan_concrete_powder": "Türkiser Trockenbeton", "block.minecraft.cyan_glazed_terracotta": "Türkise glasierte Keramik", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "Türkises Glas", "block.minecraft.cyan_stained_glass_pane": "Türkise Glasscheibe", "block.minecraft.cyan_terracotta": "Türkise Keramik", "block.minecraft.cyan_wool": "Türkise W<PERSON>", "block.minecraft.damaged_anvil": "Beschädigter Amboss", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Schwarzeichenholzknopf", "block.minecraft.dark_oak_door": "Schwarzeichenholztür", "block.minecraft.dark_oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>hol<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_hanging_sign": "Schwarzeichenholzhä<PERSON>child", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "Schwarzeichenstamm", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_pressure_plate": "Schwarzeichenholzdruckplatte", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sign": "Schwarzeichenholzschild", "block.minecraft.dark_oak_slab": "Schwarzeichenholzstufe", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_trapdoor": "Schwarzeichenholzfalltür", "block.minecraft.dark_oak_wall_hanging_sign": "Schwarzeichenholzwandhängeschild", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON> P<PERSON>arin", "block.minecraft.dark_prismarine_slab": "Dunkle Prismarinstufe", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON><PERSON> P<PERSON>arint<PERSON>ppe", "block.minecraft.daylight_detector": "Tageslichtsensor", "block.minecraft.dead_brain_coral": "Abgestorbene Hirnkoralle", "block.minecraft.dead_brain_coral_block": "Abgestorbener Hirnkorallenblock", "block.minecraft.dead_brain_coral_fan": "Abgestorbener Hirnkorallenfächer", "block.minecraft.dead_brain_coral_wall_fan": "Abgestorbener Hirnkorallenwandfächer", "block.minecraft.dead_bubble_coral": "Abgestorbene Blasenkoralle", "block.minecraft.dead_bubble_coral_block": "Abgestorbener Blasenkorallenblock", "block.minecraft.dead_bubble_coral_fan": "Abgestorbener Blasenkorallenfächer", "block.minecraft.dead_bubble_coral_wall_fan": "Abgestorbener Blasenkorallenwandfächer", "block.minecraft.dead_bush": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral": "Abgestorben<PERSON>", "block.minecraft.dead_fire_coral_block": "Abgestorbener Feuerkorallenblock", "block.minecraft.dead_fire_coral_fan": "Abgestorbener Feuerkorallenfächer", "block.minecraft.dead_fire_coral_wall_fan": "Abgestorbener Feuerkorallenwandfächer", "block.minecraft.dead_horn_coral": "Abgestorbene Geweihkoralle", "block.minecraft.dead_horn_coral_block": "Abgestorbener Geweihkorallenblock", "block.minecraft.dead_horn_coral_fan": "Abgestorbener Geweihkorallenfächer", "block.minecraft.dead_horn_coral_wall_fan": "Abgestorbener Geweihkorallenwandfächer", "block.minecraft.dead_tube_coral": "Abgestorbene Orgelkoralle", "block.minecraft.dead_tube_coral_block": "Abgestorbener Orgelkorallenblock", "block.minecraft.dead_tube_coral_fan": "Abgestorbener Orgelkorallenfächer", "block.minecraft.dead_tube_coral_wall_fan": "Abgestorbener Orgelkorallenwandfächer", "block.minecraft.decorated_pot": "Verzierter Krug", "block.minecraft.deepslate": "Tiefenschiefer", "block.minecraft.deepslate_brick_slab": "Tiefenschieferziegelstufe", "block.minecraft.deepslate_brick_stairs": "Tiefenschieferziegeltreppe", "block.minecraft.deepslate_brick_wall": "Tiefenschieferziegelmauer", "block.minecraft.deepslate_bricks": "Tiefenschieferziegel", "block.minecraft.deepslate_coal_ore": "Tiefenschiefer-Steinkohle", "block.minecraft.deepslate_copper_ore": "Tiefenschiefer-Kupfererz", "block.minecraft.deepslate_diamond_ore": "Tiefenschiefer-Diamanterz", "block.minecraft.deepslate_emerald_ore": "Tiefenschiefer-Smaragderz", "block.minecraft.deepslate_gold_ore": "Tiefenschiefer-Golderz", "block.minecraft.deepslate_iron_ore": "Tiefenschiefer-Eisenerz", "block.minecraft.deepslate_lapis_ore": "Tiefenschiefer-Lapislazulierz", "block.minecraft.deepslate_redstone_ore": "Tiefenschiefer-Redstone-Erz", "block.minecraft.deepslate_tile_slab": "Tiefenschieferfliesenstufe", "block.minecraft.deepslate_tile_stairs": "Tiefenschieferfliesentreppe", "block.minecraft.deepslate_tile_wall": "Tiefenschieferfliesenmauer", "block.minecraft.deepslate_tiles": "Tiefenschieferfliesen", "block.minecraft.detector_rail": "Sensorschiene", "block.minecraft.diamond_block": "Diamantblock", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioritstufe", "block.minecraft.diorite_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dirt": "Erde", "block.minecraft.dirt_path": "Trampelpfad", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "Dr<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Drachen-Wandkopf", "block.minecraft.dried_ghast": "Ausgetrockneter Ghast", "block.minecraft.dried_kelp_block": "Getrockneter Seetangblock", "block.minecraft.dripstone_block": "T<PERSON>fsteinblock", "block.minecraft.dropper": "<PERSON>pender", "block.minecraft.emerald_block": "Smaragdblock", "block.minecraft.emerald_ore": "Smaragderz", "block.minecraft.enchanting_table": "Z<PERSON><PERSON><PERSON>", "block.minecraft.end_gateway": "Endtransitportal", "block.minecraft.end_portal": "Endportal", "block.minecraft.end_portal_frame": "Endportalrah<PERSON>", "block.minecraft.end_rod": "Endstab", "block.minecraft.end_stone": "<PERSON>stein", "block.minecraft.end_stone_brick_slab": "Endsteinziegelstufe", "block.minecraft.end_stone_brick_stairs": "Endsteinziegeltreppe", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ender_chest": "Endertruhe", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON> gemeißelter <PERSON>", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_cut_copper": "Angelaufener geschnittener Kupferblock", "block.minecraft.exposed_cut_copper_slab": "Angelaufene geschnittene Kupferstufe", "block.minecraft.exposed_cut_copper_stairs": "Angelaufene geschnittene Kupfertreppe", "block.minecraft.farmland": "Ackerboden", "block.minecraft.fern": "Farn", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "Feuerkoralle", "block.minecraft.fire_coral_block": "Feuerkorallenblock", "block.minecraft.fire_coral_fan": "Feuerkorallenfächer", "block.minecraft.fire_coral_wall_fan": "Feuerkorallenwandfächer", "block.minecraft.firefly_bush": "Glühwürmchenbusch", "block.minecraft.fletching_table": "Bognertisch", "block.minecraft.flower_pot": "Blu<PERSON><PERSON>", "block.minecraft.flowering_azalea": "B<PERSON>ü<PERSON><PERSON> Azalee", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "Brüchiges Eis", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "Golddu<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glasscheibe", "block.minecraft.glow_lichen": "Leuchtflechte", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Goldblock", "block.minecraft.gold_ore": "Gold<PERSON><PERSON>", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Granitstufe", "block.minecraft.granite_stairs": "Granittreppe", "block.minecraft.granite_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Grasblock", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON>chen mit grauer Kerze", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON> Banner", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON>chen mit grüner Kerze", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> Trocken<PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "Hängende Wurzeln", "block.minecraft.hay_block": "Strohballen", "block.minecraft.heavy_core": "Schwerer Kern", "block.minecraft.heavy_weighted_pressure_plate": "Grobwägeplatte", "block.minecraft.honey_block": "Honigblock", "block.minecraft.honeycomb_block": "Honigwabenblock", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Geweihkoralle", "block.minecraft.horn_coral_block": "Geweihkorallenblock", "block.minecraft.horn_coral_fan": "Geweihkorallenfächer", "block.minecraft.horn_coral_wall_fan": "Geweihkorallenwandfächer", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Befallene gemeißelte Steinziegel", "block.minecraft.infested_cobblestone": "Befallener Bruchstein", "block.minecraft.infested_cracked_stone_bricks": "Befallene rissige Steinziegel", "block.minecraft.infested_deepslate": "Befallener Tiefenschiefer", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON><PERSON> bemooste <PERSON>", "block.minecraft.infested_stone": "Befallener Stein", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_bars": "Eisengitter", "block.minecraft.iron_block": "Eisenblock", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Eisenfalltür", "block.minecraft.jack_o_lantern": "Kürbislaterne", "block.minecraft.jigsaw": "Verbundblock", "block.minecraft.jukebox": "Plattenspieler", "block.minecraft.jungle_button": "Tropenholzknopf", "block.minecraft.jungle_door": "Tropenholztür", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Tropenhol<PERSON><PERSON><PERSON>", "block.minecraft.jungle_hanging_sign": "Tropenholzh<PERSON><PERSON>child", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "Tropenbaumstamm", "block.minecraft.jungle_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Tropenholzdruckplatte", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ling", "block.minecraft.jungle_sign": "Tropenholzschild", "block.minecraft.jungle_slab": "Tropenholzstufe", "block.minecraft.jungle_stairs": "Tropenholztreppe", "block.minecraft.jungle_trapdoor": "Tropenholzfalltür", "block.minecraft.jungle_wall_hanging_sign": "Tropenholzwandhängeschild", "block.minecraft.jungle_wall_sign": "T<PERSON>en<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_wood": "T<PERSON>en<PERSON><PERSON>", "block.minecraft.kelp": "<PERSON>tang", "block.minecraft.kelp_plant": "Seetangstängel", "block.minecraft.ladder": "<PERSON><PERSON>", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "Lapislazuliblock", "block.minecraft.lapis_ore": "La<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "Große Amethystknospe", "block.minecraft.large_fern": "Großer Farn", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavakessel", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "Lichtblock", "block.minecraft.light_blue_banner": "Hell<PERSON>ues Banner", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle": "Hellblaue Kerze", "block.minecraft.light_blue_candle_cake": "<PERSON>chen mit hellblauer Kerze", "block.minecraft.light_blue_carpet": "Hell<PERSON>uer Teppich", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> Beton", "block.minecraft.light_blue_concrete_powder": "Hellblauer Trockenbeton", "block.minecraft.light_blue_glazed_terracotta": "Hellblaue glasierte Keramik", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON>-<PERSON>", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Hellblaue Glasscheibe", "block.minecraft.light_blue_terracotta": "Hellblaue Keramik", "block.minecraft.light_blue_wool": "Hellblaue Wolle", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON>chen mit hellgrauer Kerze", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON> Trockenbeton", "block.minecraft.light_gray_glazed_terracotta": "Hell<PERSON>ue glasierte <PERSON>ramik", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON>-<PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.light_weighted_pressure_plate": "Feinwägeplatte", "block.minecraft.lightning_rod": "Blitzableiter", "block.minecraft.lilac": "Flieder", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "Seerosenblatt", "block.minecraft.lime_banner": "Hellgrünes Banner", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON>chen mit hellgrüner Kerze", "block.minecraft.lime_carpet": "Hellgr<PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "Hellgrüner Trockenbeton", "block.minecraft.lime_glazed_terracotta": "Hellgrüne g<PERSON>te Keramik", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "Hellgr<PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "Hellg<PERSON><PERSON><PERSON>", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Webstuhl", "block.minecraft.magenta_banner": "Magenta Banner", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "Ma<PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "<PERSON>chen mit magenta Kerze", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_glazed_terracotta": "Magenta glas<PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.magma_block": "Magmablock", "block.minecraft.mangrove_button": "Mangrovenholzknopf", "block.minecraft.mangrove_door": "Mangrovenholztür", "block.minecraft.mangrove_fence": "Mangrovenholzzaun", "block.minecraft.mangrove_fence_gate": "Mangrovenholzzauntor", "block.minecraft.mangrove_hanging_sign": "Mangrovenholzhä<PERSON>child", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_log": "Mangrovenstamm", "block.minecraft.mangrove_planks": "Man<PERSON>nholzbretter", "block.minecraft.mangrove_pressure_plate": "Mangrovenholzdruckplatte", "block.minecraft.mangrove_propagule": "Mangroven-Ke<PERSON>ling", "block.minecraft.mangrove_roots": "Mangrovenwurzeln", "block.minecraft.mangrove_sign": "Mangrovenholzschild", "block.minecraft.mangrove_slab": "Mangrovenholzstufe", "block.minecraft.mangrove_stairs": "Mangrovenholztreppe", "block.minecraft.mangrove_trapdoor": "Mangrovenholzfalltür", "block.minecraft.mangrove_wall_hanging_sign": "Mangrovenholzwandh<PERSON><PERSON>child", "block.minecraft.mangrove_wall_sign": "Man<PERSON>nh<PERSON><PERSON><PERSON><PERSON>child", "block.minecraft.mangrove_wood": "Mangrovenholz", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON>", "block.minecraft.melon": "<PERSON><PERSON>", "block.minecraft.melon_stem": "Melonenpflanze", "block.minecraft.moss_block": "Moosblock", "block.minecraft.moss_carpet": "Moosteppich", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON> Bruchstein", "block.minecraft.mossy_cobblestone_slab": "Bemooste Bruchsteinstufe", "block.minecraft.mossy_cobblestone_stairs": "Bemooste Bruchsteintreppe", "block.minecraft.mossy_cobblestone_wall": "Be<PERSON><PERSON><PERSON>uer", "block.minecraft.mossy_stone_brick_slab": "Bemooste Steinziegelstufe", "block.minecraft.mossy_stone_brick_stairs": "Bemoost<PERSON>", "block.minecraft.mossy_stone_brick_wall": "Be<PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moving_piston": "Bewegter Block", "block.minecraft.mud": "Schlamm", "block.minecraft.mud_brick_slab": "Schlammziegelstufe", "block.minecraft.mud_brick_stairs": "Schlammziegeltreppe", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mauer", "block.minecraft.mud_bricks": "Schlammziegel", "block.minecraft.muddy_mangrove_roots": "Schlammige Mangrovenwurzeln", "block.minecraft.mushroom_stem": "Pilzstiel", "block.minecraft.mycelium": "<PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Netherziegelzaun", "block.minecraft.nether_brick_slab": "Netherziegelstufe", "block.minecraft.nether_brick_stairs": "Netherziegeltreppe", "block.minecraft.nether_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_gold_ore": "Nethergolderz", "block.minecraft.nether_portal": "Netherportal", "block.minecraft.nether_quartz_ore": "Netherquarzerz", "block.minecraft.nether_sprouts": "Nethersprossen", "block.minecraft.nether_wart": "Netherwarzen", "block.minecraft.nether_wart_block": "Netherwarzenblock", "block.minecraft.netherite_block": "Netheritblock", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Notenblock", "block.minecraft.oak_button": "Eichenholzknopf", "block.minecraft.oak_door": "Eichenholztür", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON>nh<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "Eichenholzhä<PERSON>child", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Eichenstamm", "block.minecraft.oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Eichenholzdruckplatte", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sign": "Eichenholzschild", "block.minecraft.oak_slab": "Eichenholzstufe", "block.minecraft.oak_stairs": "Eichenholztreppe", "block.minecraft.oak_trapdoor": "Eichenholzfalltür", "block.minecraft.oak_wall_hanging_sign": "Eichenholzwandh<PERSON>ngeschild", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>child", "block.minecraft.oak_wood": "Eichenholz", "block.minecraft.observer": "Beobachter", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.ominous_banner": "Unheilvolles Banner", "block.minecraft.open_eyeblossom": "Geöffnete Augenblüte", "block.minecraft.orange_banner": "Oranges Banner", "block.minecraft.orange_bed": "Oranges Bett", "block.minecraft.orange_candle": "Orange Kerze", "block.minecraft.orange_candle_cake": "<PERSON>chen mit oranger Kerze", "block.minecraft.orange_carpet": "<PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "Orange glasierte Keramik", "block.minecraft.orange_shulker_box": "Orange Shulker-Kiste", "block.minecraft.orange_stained_glass": "Oranges Glas", "block.minecraft.orange_stained_glass_pane": "Orange Glasscheibe", "block.minecraft.orange_terracotta": "Orange Keramik", "block.minecraft.orange_tulip": "Orange Tulpe", "block.minecraft.orange_wool": "Orange Wolle", "block.minecraft.oxeye_daisy": "Margerite", "block.minecraft.oxidized_chiseled_copper": "Oxidierter gemeißelter <PERSON>", "block.minecraft.oxidized_copper": "Oxi<PERSON><PERSON>", "block.minecraft.oxidized_copper_bulb": "Oxidierte Kupferleuchte", "block.minecraft.oxidized_copper_door": "Oxidierte Kupfertür", "block.minecraft.oxidized_copper_grate": "<PERSON>xi<PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Oxidierte Kupferfalltür", "block.minecraft.oxidized_cut_copper": "Oxidierter geschnittener Kupferblock", "block.minecraft.oxidized_cut_copper_slab": "Oxidierte geschnittene Kupferstufe", "block.minecraft.oxidized_cut_copper_stairs": "Oxidierte geschnittene Kupfertreppe", "block.minecraft.packed_ice": "Packeis", "block.minecraft.packed_mud": "<PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON> Blassmo<PERSON>", "block.minecraft.pale_moss_block": "Blassmoosblock", "block.minecraft.pale_moss_carpet": "<PERSON>lassmoostep<PERSON><PERSON>", "block.minecraft.pale_oak_button": "Blasseichenholzknopf", "block.minecraft.pale_oak_door": "Blasseichenholztür", "block.minecraft.pale_oak_fence": "Blasseichen<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "Blasseichenholzzauntor", "block.minecraft.pale_oak_hanging_sign": "Blasseichenholzhängeschild", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_log": "Blasseichenstamm", "block.minecraft.pale_oak_planks": "<PERSON><PERSON>ei<PERSON>hol<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_pressure_plate": "Blasseichenholzdruckplatte", "block.minecraft.pale_oak_sapling": "Blasseichensetzling", "block.minecraft.pale_oak_sign": "Blasseichenholzschild", "block.minecraft.pale_oak_slab": "Blasseichenholzstufe", "block.minecraft.pale_oak_stairs": "Blasseichenholztreppe", "block.minecraft.pale_oak_trapdoor": "Blasseichenholzfalltür", "block.minecraft.pale_oak_wall_hanging_sign": "Blasseichenholzwandhängeschild", "block.minecraft.pale_oak_wall_sign": "Blasseichenhol<PERSON><PERSON><PERSON>child", "block.minecraft.pale_oak_wood": "Blasseichenholz", "block.minecraft.pearlescent_froglight": "Perlmutternes Fr<PERSON>t", "block.minecraft.peony": "Pfingstrose", "block.minecraft.petrified_oak_slab": "Versteinerte Eichenholzstufe", "block.minecraft.piglin_head": "Piglinkopf", "block.minecraft.piglin_wall_head": "Piglin-Wandkopf", "block.minecraft.pink_banner": "<PERSON>", "block.minecraft.pink_bed": "<PERSON>", "block.minecraft.pink_candle": "<PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> mit rosa <PERSON>rz<PERSON>", "block.minecraft.pink_carpet": "<PERSON>", "block.minecraft.pink_concrete": "<PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON> g<PERSON>", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "<PERSON>-<PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON>", "block.minecraft.pink_stained_glass_pane": "<PERSON>", "block.minecraft.pink_terracotta": "<PERSON>", "block.minecraft.pink_tulip": "<PERSON>", "block.minecraft.pink_wool": "<PERSON>", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON>benkopf", "block.minecraft.pitcher_crop": "Kannenpflanzentrieb", "block.minecraft.pitcher_plant": "Kannenpflanze", "block.minecraft.player_head": "Spielerkopf", "block.minecraft.player_head.named": "<PERSON><PERSON> von %s", "block.minecraft.player_wall_head": "Spieler-Wandkopf", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Spitzer Tropfstein", "block.minecraft.polished_andesite": "Polierter Andesit", "block.minecraft.polished_andesite_slab": "Polierte Andesitstufe", "block.minecraft.polished_andesite_stairs": "Polierte <PERSON>", "block.minecraft.polished_basalt": "Polierter Basalt", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Polierte Schwarzsteinziegelstufe", "block.minecraft.polished_blackstone_brick_stairs": "Polier<PERSON>ppe", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON>uer", "block.minecraft.polished_blackstone_bricks": "<PERSON>ier<PERSON>", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_pressure_plate": "Polierte Schwarzsteindruckplatte", "block.minecraft.polished_blackstone_slab": "Polierte <PERSON>e", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Polierte Tiefenschieferstufe", "block.minecraft.polished_deepslate_stairs": "Polierte Tiefenschiefertreppe", "block.minecraft.polished_deepslate_wall": "Polierte Tief<PERSON>efermauer", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Polierte Dioritstufe", "block.minecraft.polished_diorite_stairs": "Polierte <PERSON>", "block.minecraft.polished_granite": "Polierter Granit", "block.minecraft.polished_granite_slab": "Polierte Granitstufe", "block.minecraft.polished_granite_stairs": "Polierte Granitt<PERSON>ppe", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "Polierte <PERSON>e", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Eingetopfter Akaziensetzling", "block.minecraft.potted_allium": "Einget<PERSON><PERSON>", "block.minecraft.potted_azalea_bush": "Eingetopfte Azalee", "block.minecraft.potted_azure_bluet": "Eingetopftes Porzellansternchen", "block.minecraft.potted_bamboo": "Eingetopfter Bambus", "block.minecraft.potted_birch_sapling": "Eingetopfter Birkensetzling", "block.minecraft.potted_blue_orchid": "Eingetopfte blaue Orchidee", "block.minecraft.potted_brown_mushroom": "Eingetopfter brauner Pilz", "block.minecraft.potted_cactus": "Eingetopfter Kaktus", "block.minecraft.potted_cherry_sapling": "Einget<PERSON><PERSON>", "block.minecraft.potted_closed_eyeblossom": "Eingetopfte geschlossene Augenblüte", "block.minecraft.potted_cornflower": "Eingetopfte Kornblume", "block.minecraft.potted_crimson_fungus": "Eingetopfter Karmesinpilz", "block.minecraft.potted_crimson_roots": "Eingetopfte Karmesinwurzeln", "block.minecraft.potted_dandelion": "Eingetop<PERSON>", "block.minecraft.potted_dark_oak_sapling": "Eingetopfter Schwarzei<PERSON>setzling", "block.minecraft.potted_dead_bush": "Einget<PERSON><PERSON> toter Busch", "block.minecraft.potted_fern": "Eingetopfter Farn", "block.minecraft.potted_flowering_azalea_bush": "Eingetopfte blühende Azalee", "block.minecraft.potted_jungle_sapling": "Eingetopfter T<PERSON>baumsetzling", "block.minecraft.potted_lily_of_the_valley": "Eingetopftes Maiglöckchen", "block.minecraft.potted_mangrove_propagule": "Eingetop<PERSON>n-<PERSON><PERSON><PERSON>", "block.minecraft.potted_oak_sapling": "Eingetopfter <PERSON>", "block.minecraft.potted_open_eyeblossom": "Eingetopfte geöffnete Augenblüte", "block.minecraft.potted_orange_tulip": "Eingetopfte orange Tulpe", "block.minecraft.potted_oxeye_daisy": "Eingetopfte Margerite", "block.minecraft.potted_pale_oak_sapling": "Eingetopfter Blasseichensetzling", "block.minecraft.potted_pink_tulip": "Eingetopft<PERSON> rosa <PERSON>", "block.minecraft.potted_poppy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_red_mushroom": "Eingetopfter roter Pilz", "block.minecraft.potted_red_tulip": "Eingetopfte rote Tulpe", "block.minecraft.potted_spruce_sapling": "Eingetop<PERSON>", "block.minecraft.potted_torchflower": "Eingetopfte Fackellilie", "block.minecraft.potted_warped_fungus": "Eingetopfter Wirrpilz", "block.minecraft.potted_warped_roots": "Eingetopfte Wirrwurzeln", "block.minecraft.potted_white_tulip": "Eingetopfte weiße Tulpe", "block.minecraft.potted_wither_rose": "Eingetopfte Wither-Rose", "block.minecraft.powder_snow": "Pulverschnee", "block.minecraft.powder_snow_cauldron": "Pulverschneekessel", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarinziegelstufe", "block.minecraft.prismarine_brick_stairs": "Prismarinz<PERSON>geltreppe", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "Prismarinstufe", "block.minecraft.prismarine_stairs": "Prismarintreppe", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "<PERSON><PERSON>", "block.minecraft.purple_bed": "<PERSON><PERSON>", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> mit violetter Kerze", "block.minecraft.purple_carpet": "<PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> g<PERSON>", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON>", "block.minecraft.purpur_block": "Purpurblock", "block.minecraft.purpur_pillar": "Purpursäule", "block.minecraft.purpur_slab": "Purpurstufe", "block.minecraft.purpur_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_block": "Quarzblock", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "Quarzsäule", "block.minecraft.quartz_slab": "Quarzstufe", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "<PERSON><PERSON><PERSON>pferblock", "block.minecraft.raw_gold_block": "<PERSON><PERSON><PERSON>block", "block.minecraft.raw_iron_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_banner": "Rotes Banner", "block.minecraft.red_bed": "Rotes Bett", "block.minecraft.red_candle": "Rote Kerze", "block.minecraft.red_candle_cake": "<PERSON>chen mit roter Kerze", "block.minecraft.red_carpet": "<PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "Rote glasierte Keramik", "block.minecraft.red_mushroom": "<PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "Rote Netherziegelstufe", "block.minecraft.red_nether_brick_stairs": "Rote Netherziegeltreppe", "block.minecraft.red_nether_brick_wall": "Rote Netherziegelmauer", "block.minecraft.red_nether_bricks": "Rote Netherziegel", "block.minecraft.red_sand": "<PERSON><PERSON>", "block.minecraft.red_sandstone": "<PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Rote Sandsteinstufe", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON>-<PERSON>", "block.minecraft.red_stained_glass": "Rotes Glas", "block.minecraft.red_stained_glass_pane": "Rote Glasscheibe", "block.minecraft.red_terracotta": "Rote Keramik", "block.minecraft.red_tulip": "<PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Redstone-Block", "block.minecraft.redstone_lamp": "Redstone-Lampe", "block.minecraft.redstone_ore": "Redstone-Erz", "block.minecraft.redstone_torch": "Redstone-Fackel", "block.minecraft.redstone_wall_torch": "Redstone-Wandfackel", "block.minecraft.redstone_wire": "Redstone-Leitung", "block.minecraft.reinforced_deepslate": "Verstärkter Tiefenschiefer", "block.minecraft.repeater": "Redstone-Verstärker", "block.minecraft.repeating_command_block": "Wiederhol-Be<PERSON>hlsblock", "block.minecraft.resin_block": "Harzblock", "block.minecraft.resin_brick_slab": "Harzziegelstufe", "block.minecraft.resin_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_clump": "Harz<PERSON>lump<PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "Rosenstrauch", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Sandsteinstufe", "block.minecraft.sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-Katalysator", "block.minecraft.sculk_sensor": "Sculk-Sensor", "block.minecraft.sculk_shrieker": "Sculk-Kreischer", "block.minecraft.sculk_vein": "Sculk-Ader", "block.minecraft.sea_lantern": "Seelaterne", "block.minecraft.sea_pickle": "Meeresgurke", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Wiedereinstiegspunkt gesetzt", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Skelettschädel", "block.minecraft.skeleton_wall_skull": "Skelett-Wandschädel", "block.minecraft.slime_block": "Schleimblock", "block.minecraft.small_amethyst_bud": "Kleine Amethystknospe", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "Schmiedetisch", "block.minecraft.smoker": "Räucherofen", "block.minecraft.smooth_basalt": "<PERSON><PERSON>", "block.minecraft.smooth_quartz": "<PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "Glatte Quarzstufe", "block.minecraft.smooth_quartz_stairs": "Glatte Quarztreppe", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_slab": "Glatte rote Sandsteinstufe", "block.minecraft.smooth_red_sandstone_stairs": "Glatte rote Sandsteintreppe", "block.minecraft.smooth_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "Glatte Sandsteinstufe", "block.minecraft.smooth_sandstone_stairs": "Glatte <PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "Glatte Steinstufe", "block.minecraft.sniffer_egg": "Schnüffler-Ei", "block.minecraft.snow": "Sc<PERSON><PERSON>", "block.minecraft.snow_block": "Schneeblock", "block.minecraft.soul_campfire": "Seelenlagerfeuer", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "Seelenlaterne", "block.minecraft.soul_sand": "Seelensand", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawn.not_valid": "Du hast kein <PERSON>t und keinen aufgeladenen Seelenanker, oder dein Wiedereinstiegspunkt wurde blockiert", "block.minecraft.spawner": "Spawner", "block.minecraft.spawner.desc1": "Benutze Spawn‐Ei:", "block.minecraft.spawner.desc2": "<PERSON>gt <PERSON>yp fest", "block.minecraft.sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "Sporenblüte", "block.minecraft.spruce_button": "Fichtenholzknopf", "block.minecraft.spruce_door": "Fichtenholztür", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "Fichtenholzh<PERSON><PERSON><PERSON>", "block.minecraft.spruce_leaves": "Fichtennadeln", "block.minecraft.spruce_log": "Fichtenstamm", "block.minecraft.spruce_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "Fichtenholzdruckplatte", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "Fichtenholzschild", "block.minecraft.spruce_slab": "Fichtenholzstufe", "block.minecraft.spruce_stairs": "<PERSON>chtenholztre<PERSON>", "block.minecraft.spruce_trapdoor": "Fichtenholzfalltür", "block.minecraft.spruce_wall_hanging_sign": "Fichtenholzwandh<PERSON><PERSON>child", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "Klebriger Kolben", "block.minecraft.stone": "<PERSON>", "block.minecraft.stone_brick_slab": "Steinziegelstufe", "block.minecraft.stone_brick_stairs": "Stein<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "Steinknopf", "block.minecraft.stone_pressure_plate": "Steindruckplatte", "block.minecraft.stone_slab": "Steinstufe", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "Steinsäge", "block.minecraft.stripped_acacia_log": "Entrindeter Akazienstamm", "block.minecraft.stripped_acacia_wood": "Entrindetes Akazienholz", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_log": "Entrindeter Birkenstamm", "block.minecraft.stripped_birch_wood": "Entrindetes Birkenholz", "block.minecraft.stripped_cherry_log": "Entrindeter Kirschstamm", "block.minecraft.stripped_cherry_wood": "Entrindetes Kirschholz", "block.minecraft.stripped_crimson_hyphae": "Geschälte Karmesinhyphen", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "Entrindeter Schwarzeichenstamm", "block.minecraft.stripped_dark_oak_wood": "Entrindetes Schwarzeichenholz", "block.minecraft.stripped_jungle_log": "Entrindeter Tropenbaumstamm", "block.minecraft.stripped_jungle_wood": "Entrindetes Tropenholz", "block.minecraft.stripped_mangrove_log": "Entrindeter Mangrovenstamm", "block.minecraft.stripped_mangrove_wood": "Entrindetes Mangrovenholz", "block.minecraft.stripped_oak_log": "Entrindeter Eichenstamm", "block.minecraft.stripped_oak_wood": "Entrindetes Eichenholz", "block.minecraft.stripped_pale_oak_log": "Entrindeter Blasseichenstamm", "block.minecraft.stripped_pale_oak_wood": "Entrindetes Blasseichenholz", "block.minecraft.stripped_spruce_log": "Entrindeter Fichtenstamm", "block.minecraft.stripped_spruce_wood": "Entrindetes Fichtenholz", "block.minecraft.stripped_warped_hyphae": "Geschälte Wirrhyphen", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.structure_block": "Konstruktionsblock", "block.minecraft.structure_void": "Konstruktionsleere", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Sonnenblume", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "Seltsamer Sand", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON>ßbeerstrauch", "block.minecraft.tall_dry_grass": "<PERSON><PERSON> Trockengras", "block.minecraft.tall_grass": "Hohes Gras", "block.minecraft.tall_seagrass": "<PERSON><PERSON> Seegras", "block.minecraft.target": "<PERSON><PERSON>", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Testblock", "block.minecraft.test_instance_block": "Testinstanzblock", "block.minecraft.tinted_glass": "Getöntes Glas", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT‐Explosionen sind deaktiviert", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Fackellilienpflanze", "block.minecraft.trapped_chest": "Redstone-Truhe", "block.minecraft.trial_spawner": "<PERSON><PERSON>ü<PERSON><PERSON><PERSON>-Spawner", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON>ken", "block.minecraft.tube_coral": "Orgelkoralle", "block.minecraft.tube_coral_block": "Orgelkorallenblock", "block.minecraft.tube_coral_fan": "Orgelkorallenfächer", "block.minecraft.tube_coral_wall_fan": "Orgelkorallenwandfächer", "block.minecraft.tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tuffsteinziegelstufe", "block.minecraft.tuff_brick_stairs": "Tuffsteinziegeltreppe", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_bricks": "<PERSON>ffs<PERSON>ziegel", "block.minecraft.tuff_slab": "Tuffsteinstufe", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Schildkrötenei", "block.minecraft.twisting_vines": "Zwirbelranken", "block.minecraft.twisting_vines_plant": "Zwirbelrankenpflanze", "block.minecraft.vault": "Tresor", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.vine": "<PERSON>en", "block.minecraft.void_air": "Leerenluft", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "Wirrknopf", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "Wirrzaun", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "W<PERSON>rpilz", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hyphae": "Wirrhyphen", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "Wirrdruckplatte", "block.minecraft.warped_roots": "Wirrwurzeln", "block.minecraft.warped_sign": "Wirrschild", "block.minecraft.warped_slab": "Wirrstufe", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "Wirrfalltür", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wart_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "Wasserkessel", "block.minecraft.waxed_chiseled_copper": "Gewachster gemeißelter <PERSON>", "block.minecraft.waxed_copper_block": "Gewachster Kupferblock", "block.minecraft.waxed_copper_bulb": "Gewachste Kupferleuchte", "block.minecraft.waxed_copper_door": "Gewachste Kupfertür", "block.minecraft.waxed_copper_grate": "Gewachster Kupferrost", "block.minecraft.waxed_copper_trapdoor": "Gewachste Kupferfalltür", "block.minecraft.waxed_cut_copper": "Gewachster geschnittener Kupferblock", "block.minecraft.waxed_cut_copper_slab": "Gewachste geschnittene Kupferstufe", "block.minecraft.waxed_cut_copper_stairs": "Gewachste geschnittene Kupfertreppe", "block.minecraft.waxed_exposed_chiseled_copper": "Gewachster angelaufener gemeißelter Ku<PERSON>block", "block.minecraft.waxed_exposed_copper": "Gewachster angelaufener Kupferblock", "block.minecraft.waxed_exposed_copper_bulb": "Gewachste angelaufene Kupferleuchte", "block.minecraft.waxed_exposed_copper_door": "Gewachste angelaufene Kupfertür", "block.minecraft.waxed_exposed_copper_grate": "Gewachster angelaufener Kupferrost", "block.minecraft.waxed_exposed_copper_trapdoor": "Gewachste angelaufene Kupferfalltür", "block.minecraft.waxed_exposed_cut_copper": "Gewachster angelaufener geschnittener Kupferblock", "block.minecraft.waxed_exposed_cut_copper_slab": "Gewachste angelaufene geschnittene Kupferstufe", "block.minecraft.waxed_exposed_cut_copper_stairs": "Gewachste angelaufene geschnittene Kupfertreppe", "block.minecraft.waxed_oxidized_chiseled_copper": "Gewachster oxidierter gemeißelter Ku<PERSON>block", "block.minecraft.waxed_oxidized_copper": "Gewachster oxidierter <PERSON>", "block.minecraft.waxed_oxidized_copper_bulb": "Gewachste oxidierte Kupferleuchte", "block.minecraft.waxed_oxidized_copper_door": "Gewachste oxidierte Kupfertür", "block.minecraft.waxed_oxidized_copper_grate": "Gewachster oxidierter <PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "Gewachste oxidierte Kupferfalltür", "block.minecraft.waxed_oxidized_cut_copper": "Gewachster oxidierter geschnittener Kupferblock", "block.minecraft.waxed_oxidized_cut_copper_slab": "Gewachste oxidierte geschnittene Kupferstufe", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Gewachste oxidierte geschnittene Kupfertreppe", "block.minecraft.waxed_weathered_chiseled_copper": "Gewachster verwitterter gemeißelter Ku<PERSON>block", "block.minecraft.waxed_weathered_copper": "Gewachster verwitterter <PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "Gewachste verwitterte Kupferleuchte", "block.minecraft.waxed_weathered_copper_door": "Gewachste verwitterte Kupfertür", "block.minecraft.waxed_weathered_copper_grate": "Gewachster verwitterter Kupf<PERSON>rost", "block.minecraft.waxed_weathered_copper_trapdoor": "Gewachste verwitterte Kupferfalltür", "block.minecraft.waxed_weathered_cut_copper": "Gewachster verwitterter geschnittener Kupferblock", "block.minecraft.waxed_weathered_cut_copper_slab": "Gewachste verwitterte geschnittene Kupferstufe", "block.minecraft.waxed_weathered_cut_copper_stairs": "Gewachste verwitterte geschnittene Kupfertreppe", "block.minecraft.weathered_chiseled_copper": "Verwitterter gemeißelter <PERSON>", "block.minecraft.weathered_copper": "<PERSON>erwi<PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "Verwitterte Kupferleuchte", "block.minecraft.weathered_copper_door": "Verwitterte Kupfertür", "block.minecraft.weathered_copper_grate": "Verwitterter <PERSON>", "block.minecraft.weathered_copper_trapdoor": "Verwitterte Kupferfalltür", "block.minecraft.weathered_cut_copper": "Verwitterter geschnittener Kupferblock", "block.minecraft.weathered_cut_copper_slab": "Verwitterte geschnittene Kupferstufe", "block.minecraft.weathered_cut_copper_stairs": "Verwitterte geschnittene Kupfertreppe", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "Trauerrankenpflanze", "block.minecraft.wet_sponge": "<PERSON><PERSON>", "block.minecraft.wheat": "Weizenpflanze", "block.minecraft.white_banner": "Weißes Banner", "block.minecraft.white_bed": "Weißes Bett", "block.minecraft.white_candle": "Weiße Kerze", "block.minecraft.white_candle_cake": "<PERSON>chen mit weißer <PERSON>rze", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "Weiße glasierte Keramik", "block.minecraft.white_shulker_box": "Weiße Shulker-Kiste", "block.minecraft.white_stained_glass": "Weißes Glas", "block.minecraft.white_stained_glass_pane": "Weiße Glasscheibe", "block.minecraft.white_terracotta": "Weiße Keramik", "block.minecraft.white_tulip": "Weiße Tulpe", "block.minecraft.white_wool": "Weiße Wolle", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Wither-Skelettschädel", "block.minecraft.wither_skeleton_wall_skull": "Wither-Skelett-Wandschädel", "block.minecraft.yellow_banner": "Gelbes Banner", "block.minecraft.yellow_bed": "Gelbes Bett", "block.minecraft.yellow_candle": "Gelbe Kerze", "block.minecraft.yellow_candle_cake": "<PERSON>chen mit gelber Kerze", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Gelbe glasierte Keramik", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Gelbe Glasscheibe", "block.minecraft.yellow_terracotta": "Gelbe Keramik", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> W<PERSON>e", "block.minecraft.zombie_head": "Zombiekopf", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.byAuthor": "von %1$s", "book.edit.title": "Buchbearbeitungsfenster", "book.editTitle": "Buchtitel eingeben:", "book.finalizeButton": "Endgültig signieren", "book.finalizeWarning": "Hinweis! Wenn du das Buch signierst, kann es nicht mehr bearbeitet werden.", "book.generation.0": "Original", "book.generation.1": "Kopie des Originals", "book.generation.2": "<PERSON><PERSON> einer <PERSON>", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Ungültiges Buchetikett *", "book.pageIndicator": "Seite %1$s von %2$s", "book.page_button.next": "Nächste Seite", "book.page_button.previous": "Vorherige Seite", "book.sign.title": "Buchsignierfenster", "book.sign.titlebox": "Titel", "book.signButton": "Signieren", "book.view.title": "Buchansichtsfenster", "build.tooHigh": "Die maximale Bauhöhe beträgt %s", "chat.cannotSend": "Chatnachricht kann nicht gesendet werden", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klicke zum Teleportieren", "chat.copy": "<PERSON>", "chat.copy.click": "<PERSON><PERSON><PERSON>, um in die Zwischenablage zu kopieren", "chat.deleted_marker": "Diese Cha<PERSON>nachricht wurde vom Server gelöscht.", "chat.disabled.chain_broken": "Der Chat wurde aufgrund einer unterbrochenen Kette deaktiviert. <PERSON><PERSON> versuche, dich erneut zu verbinden.", "chat.disabled.expiredProfileKey": "Der Chat wurde aufgrund eines abgelaufenen öffentlichen Profilschlüssels deaktiviert. <PERSON><PERSON> versuche, dich erneut zu verbinden.", "chat.disabled.invalid_command_signature": "Der Befehl hatte unerwartete oder fehlende Befehlsargument‐Signaturen.", "chat.disabled.invalid_signature": "Der Chat wies eine ungültige Signatur auf. <PERSON><PERSON> versuche, dich erneut zu verbinden.", "chat.disabled.launcher": "Der Chat wurde durch Launcher‐Einstellung deaktiviert. Nachricht kann nicht gesendet werden.", "chat.disabled.missingProfileKey": "Der Chat wurde aufgrund eines fehlenden öffentlichen Profilschlüssels deaktiviert. <PERSON><PERSON> versuche, dich erneut zu verbinden.", "chat.disabled.options": "Der Chat wurde in den Client‐Einstellungen deaktiviert.", "chat.disabled.out_of_order_chat": "Der Chat wurde in falscher Reihenfolge empfangen. Hat sich deine Systemzeit geändert?", "chat.disabled.profile": "Der Chat wird durch die Kontoeinstellungen nicht erlaubt. Drücke %s erneut, um weitere Informationen zu erhalten.", "chat.disabled.profile.moreInfo": "Der Chat wird durch die Kontoeinstellungen nicht erlaubt. Nachrichten können nicht gesendet oder angezeigt werden.", "chat.editBox": "Cha<PERSON>", "chat.filtered": "Gefiltert vom Server.", "chat.filtered_full": "Der Server hat deine Nachricht für einige Spieler versteckt.", "chat.link.confirm": "Bist du sicher, dass du die folgende Webseite öffnen möchtest?", "chat.link.confirmTrusted": "Möchtest du diesen Link öffnen oder in die Zwischenablage kopieren?", "chat.link.open": "<PERSON><PERSON>", "chat.link.warning": "<PERSON><PERSON><PERSON> keine <PERSON> von <PERSON>, denen du nicht vertraust!", "chat.queue": "[+%s auss<PERSON><PERSON><PERSON>(n)]", "chat.square_brackets": "[%s]", "chat.tag.error": "Der Server hat eine ungültige Nachricht gesendet.", "chat.tag.modified": "Die Nachricht wurde vom Server verändert. Original:", "chat.tag.not_secure": "Nicht überprüfte Nachricht. Kann nicht gemeldet werden.", "chat.tag.system": "Servernachricht. Kann nicht gemeldet werden.", "chat.tag.system_single_player": "Servernachricht.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s hat die Aufgabe %s erledigt", "chat.type.advancement.goal": "%s hat das Ziel %s erreicht", "chat.type.advancement.task": "%s hat den Fortschritt %s erzielt", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Team benachrichtigen", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sagt %s", "chat.validation_error": "Chatüberprüfungsfehler", "chat_screen.message": "<PERSON>u sendende Nachricht: %s", "chat_screen.title": "Chatfenster", "chat_screen.usage": "Gib eine Nachricht ein und drücke die Eingabetaste, um sie abzusenden", "chunk.toast.checkLog": "Siehe Ausgabeprotokoll für Einzelheiten", "chunk.toast.loadFailure": "Chunk bei %s konnte nicht geladen werden", "chunk.toast.lowDiskSpace": "<PERSON>ig freier Speicherplatz!", "chunk.toast.lowDiskSpace.description": "Die Welt kann unter Umständen nicht gespeichert werden.", "chunk.toast.saveFailure": "Chunk bei %s konnte nicht gespeichert werden", "clear.failed.multiple": "Bei %s Spielern wurden keine Gegenstände gefunden", "clear.failed.single": "Bei %s wurden keine Gegenstände gefunden", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.gray": "G<PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Hellblau", "color.minecraft.light_gray": "Hellgrau", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Orange", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "Rot", "color.minecraft.white": "<PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[HIER]", "command.context.parse_error": "%s an Position %s: %s", "command.exception": "Befehl fehlerhaft: %s", "command.expected.separator": "Leerzeichen vor neuem Argument erwartet", "command.failed": "Während der Ausführung dieses Befehls ist ein Fehler aufgetreten", "command.forkLimit": "Maximale Anzahl an Befehlsrahmen (%s) erreicht", "command.unknown.argument": "Ungültiges Befehlsargument", "command.unknown.command": "Unbekannter oder unvollständiger Be<PERSON>hl, siehe unten für Fehler", "commands.advancement.criterionNotFound": "Kriterium ‚%2$s‘ gehört nicht zum Fortschritt %1$s", "commands.advancement.grant.criterion.to.many.failure": "Kriterium ‚%s‘ des Fortschritts %s konnte %s S<PERSON>lern nicht gewährt werden, da es bereits erfüllt ist", "commands.advancement.grant.criterion.to.many.success": "Kriterium ‚%s‘ des Fortschritts %s wurde %s Spielern gewährt", "commands.advancement.grant.criterion.to.one.failure": "Kriterium ‚%s‘ des Fortschritts %s konnte %s nicht gewährt werden, da es bereits erfüllt ist", "commands.advancement.grant.criterion.to.one.success": "Kriterium ‚%s‘ des Fortschritts %s wurde %s gewährt", "commands.advancement.grant.many.to.many.failure": "%s Fortschritte konnten %s S<PERSON>lern nicht gewährt werden, da sie bereits erreicht wurden", "commands.advancement.grant.many.to.many.success": "%s Fortschritte wurden %s Spielern gewährt", "commands.advancement.grant.many.to.one.failure": "%s Fortschritte konnten %s nicht gewährt werden, da sie bereits erreicht wurden", "commands.advancement.grant.many.to.one.success": "%s Fortschritte wurden %s gewährt", "commands.advancement.grant.one.to.many.failure": "Fortschritt %s konnte %s Spielern nicht gewährt werden, da er bereits erreicht wurde", "commands.advancement.grant.one.to.many.success": "Fortschritt %s wurde %s Spielern gewährt", "commands.advancement.grant.one.to.one.failure": "Fortschritt %s konnte %s nicht gewährt werden, da er bereits erreicht wurde", "commands.advancement.grant.one.to.one.success": "Fortschritt %s wurde %s gewährt", "commands.advancement.revoke.criterion.to.many.failure": "Kriterium ‚%s‘ des Fortschritts %s konnte %s S<PERSON><PERSON>n nicht entz<PERSON> werden, da es nicht erfüllt war", "commands.advancement.revoke.criterion.to.many.success": "Kriterium ‚%s‘ des Fortschritts %s wurde %s Spielern entzogen", "commands.advancement.revoke.criterion.to.one.failure": "Kriterium ‚%s‘ des Fortschritts %s konnte %s nicht entzogen werden, da es nicht erfüllt war", "commands.advancement.revoke.criterion.to.one.success": "Kriterium ‚%s‘ des Fortschritts %s wurde %s entzogen", "commands.advancement.revoke.many.to.many.failure": "%s Fortschritte konnten %s <PERSON><PERSON><PERSON>n nicht entzogen werden, da sie nicht erreicht wurden", "commands.advancement.revoke.many.to.many.success": "%s Fortschritte wurden %s Spielern entzogen", "commands.advancement.revoke.many.to.one.failure": "%s Fortschritte konnten %s nicht entzogen werden, da sie nicht erreicht wurden", "commands.advancement.revoke.many.to.one.success": "%s Fortschritte wurden %s entzogen", "commands.advancement.revoke.one.to.many.failure": "Fortschritt %s konnte %s Spielern nicht entzogen werden, da er nicht erreicht wurde", "commands.advancement.revoke.one.to.many.success": "Fortschritt %s wurde %s Spielern entzogen", "commands.advancement.revoke.one.to.one.failure": "Fortschritt %s konnte %s nicht entzogen werden, da er nicht erreicht wurde", "commands.advancement.revoke.one.to.one.success": "Fortschritt %s wurde %s entzogen", "commands.attribute.base_value.get.success": "Grundwert des Attributs %s des Objekts %s ist %s", "commands.attribute.base_value.reset.success": "Grundwert für das Attribut %s von Objekt %s wurde auf den Standardwert %s zurückgesetzt", "commands.attribute.base_value.set.success": "Grundwert des Attributs %s des Objekts %s wurde auf %s gesetzt", "commands.attribute.failed.entity": "%s ist kein gültiges Objekt für diesen Befehl", "commands.attribute.failed.modifier_already_present": "Modifikator %s ist bei Attribut %s des Objekts %s bereits vorhanden", "commands.attribute.failed.no_attribute": "Objekt %s hat kein Attribut %s", "commands.attribute.failed.no_modifier": "Attribut %s des Objekts %s hat keinen Modifikator %s", "commands.attribute.modifier.add.success": "Modifikator %s wurde zum Attribut %s des Objekts %s hinzugefügt", "commands.attribute.modifier.remove.success": "Modifikator %s wurde von Attribut %s des Objekts %s entfernt", "commands.attribute.modifier.value.get.success": "Wert des Modifikators %s des Attributs %s des Objekts %s ist %s", "commands.attribute.value.get.success": "Wert des Attributs %s des Objekts %s ist %s", "commands.ban.failed": "Nichts hat sich geändert, der Spieler ist bereits gesperrt", "commands.ban.success": "%s wurde gesperrt: %s", "commands.banip.failed": "Nichts hat sich geändert, die IP-Adresse ist bereits gesperrt", "commands.banip.info": "Diese Sperre betrifft %s <PERSON><PERSON><PERSON>: %s", "commands.banip.invalid": "Ungültige IP-Adresse oder unbekannter Spieler", "commands.banip.success": "IP-Adresse %s wurde gesperrt: %s", "commands.banlist.entry": "%s wurde von %s gesperrt: %s", "commands.banlist.entry.unknown": "(unbekannt)", "commands.banlist.list": "Es gibt %s Sperre(n):", "commands.banlist.none": "<PERSON><PERSON> gibt keine <PERSON>", "commands.bossbar.create.failed": "Eine Bossleiste mit der ID ‚%s‘ existiert bereits", "commands.bossbar.create.success": "Benutzerdefinierte Bossleiste %s wurde erstellt", "commands.bossbar.get.max": "Der Maximalwert der benutzerdefinierten Bossleiste %s beträgt %s", "commands.bossbar.get.players.none": "Benutzerdefinierte Bossleiste %s hat aktuell keine Spieler online", "commands.bossbar.get.players.some": "Benutzerdefinierte Bossleiste %s hat aktuell %s Spieler online: %s", "commands.bossbar.get.value": "Der aktuelle Wert der benutzerdefinierten Bossleiste %s beträgt %s", "commands.bossbar.get.visible.hidden": "Benutzerdefinierte Bossleiste %s ist aktuell unsichtbar", "commands.bossbar.get.visible.visible": "Benutzerdefinierte Bossleiste %s ist aktuell sichtbar", "commands.bossbar.list.bars.none": "Es sind keine benutzerdefinierten Bossleisten aktiv", "commands.bossbar.list.bars.some": "Es ist/sind %s benutzerdefinierte Bossleiste(n) aktiv: %s", "commands.bossbar.remove.success": "Benutzerdefinierte Bossleiste %s wurde entfernt", "commands.bossbar.set.color.success": "Farbe der benutzerdefinierten Bossleiste %s wurde geändert", "commands.bossbar.set.color.unchanged": "Nichts hat sich ge<PERSON>ndert, die Bossleiste hat bereits diese Farbe", "commands.bossbar.set.max.success": "Maximalwert der benutzerdefinierten Bossleiste %s wurde auf %s geändert", "commands.bossbar.set.max.unchanged": "Nichts hat sich geändert, die Bossleiste hat bereits dieses Maximum", "commands.bossbar.set.name.success": "Benutzerdefinierte Bossleiste %s wurde umbenannt", "commands.bossbar.set.name.unchanged": "Nichts hat sich geändert, die Bossleiste hat bereits diesen Namen", "commands.bossbar.set.players.success.none": "Benutzerdefinierte Bossleiste %s hat keine Spieler mehr", "commands.bossbar.set.players.success.some": "Benutzerdefinierte Bossleiste %s hat aktuell %s <PERSON><PERSON><PERSON>: %s", "commands.bossbar.set.players.unchanged": "Nichts hat sich ge<PERSON><PERSON><PERSON>, diese Spieler sind bereits der Bossleiste zugeordnet, es wurde niemand hinzugefügt oder entfernt", "commands.bossbar.set.style.success": "Einteilung der benutzerdefinierten Bossleiste %s wurde geändert", "commands.bossbar.set.style.unchanged": "Nichts hat sich ge<PERSON><PERSON>t, die Bossleiste hat bereits diese Einteilung", "commands.bossbar.set.value.success": "Wert der benutzerdefinierten Bossleiste %s wurde auf %s geändert", "commands.bossbar.set.value.unchanged": "Nichts hat sich geändert, die Bossleiste hat bereits diesen Wert", "commands.bossbar.set.visibility.unchanged.hidden": "Nichts hat sich geändert, die Bossleiste ist bereits unsichtbar", "commands.bossbar.set.visibility.unchanged.visible": "Nichts hat sich geändert, die Bossleiste ist bereits sichtbar", "commands.bossbar.set.visible.success.hidden": "Benutzerdefinierte Bossleiste %s ist nun unsichtbar", "commands.bossbar.set.visible.success.visible": "Benutzerdefinierte Bossleiste %s ist nun sichtbar", "commands.bossbar.unknown": "Es existiert keine Bossleiste mit der ID ‚%s‘", "commands.clear.success.multiple": "%s Gegenstand/‐stände wurde(n) von %s <PERSON><PERSON><PERSON>n entfernt", "commands.clear.success.single": "%s Gegenstand/‐stände wurde(n) von %s entfernt", "commands.clear.test.multiple": "%s übereinstimmende(r) Gegenstand/‐stände wurde(n) bei %s Spielern gefunden", "commands.clear.test.single": "%s übereinstimmende(r) Gegenstand/‐stände wurde(n) bei %s gefunden", "commands.clone.failed": "<PERSON><PERSON> wurden keine Blöcke kopiert", "commands.clone.overlap": "Quell- und Zielbereich dürfen sich nicht überschneiden", "commands.clone.success": "%s Block/Blöcke wurde(n) erfolgreich kopiert", "commands.clone.toobig": "Zu viele Blöcke im angegebenen Bereich (maximal %s, angegeben %s)", "commands.damage.invulnerable": "<PERSON><PERSON> kann durch den angegebenen Schadenstyp nicht verletzt werden", "commands.damage.success": "%s <PERSON><PERSON><PERSON> wurde auf %s angewandt", "commands.data.block.get": "%s von Block bei (%s, %s, %s) multipliziert mit %s ist %s", "commands.data.block.invalid": "<PERSON><PERSON><PERSON> hat keine Blockdaten", "commands.data.block.modified": "Blockdaten bei (%s, %s, %s) wurden geändert", "commands.data.block.query": "Block bei (%s, %s, %s) besitzt die folgenden Blockdaten: %s", "commands.data.entity.get": "%s von %s multipliziert mit %s ist %s", "commands.data.entity.invalid": "Spielerdaten können nicht geändert werden", "commands.data.entity.modified": "Objektdaten von %s wurden geändert", "commands.data.entity.query": "%s besitzt die folgenden Objektdaten: %s", "commands.data.get.invalid": "‚%s‘ kann nicht abgefragt werden, die Eigenschaft ist keine Zahl", "commands.data.get.multiple": "Dieses Argument akzeptiert nur einen einzelnen NBT-Wert", "commands.data.get.unknown": "‚%s‘ kann nicht abgefragt werden, die Eigenschaft existiert nicht", "commands.data.merge.failed": "Nichts hat sich geändert, die angegebenen Eigenschaften haben bereits diese Werte", "commands.data.modify.expected_list": "<PERSON>e erwartet, ‚%s‘ erhalten", "commands.data.modify.expected_object": "Ob<PERSON><PERSON> er<PERSON><PERSON>, ‚%s‘ erhalten", "commands.data.modify.expected_value": "<PERSON>rt erwartet, ‚%s‘ erhalten", "commands.data.modify.invalid_index": "Ungültiger Listenindex: %s", "commands.data.modify.invalid_substring": "Ungültige Abschnittsindizes: %s bis %s", "commands.data.storage.get": "%s im Datenspeicher %s mit %s multipliziert ist %s", "commands.data.storage.modified": "Datenspeicher %s wurde geändert", "commands.data.storage.query": "Datenspeicher %s besitzt die folgenden Daten: %s", "commands.datapack.create.already_exists": "Ein Datenpaket mit dem Namen ‚%s‘ existiert bereits", "commands.datapack.create.invalid_full_name": "Ungültiger Name ‚%s‘ für neues Datenpaket", "commands.datapack.create.invalid_name": "Der Datenpaketname ‚%s‘ enthält ungültige Zeichen", "commands.datapack.create.io_failure": "Datenpaket ‚%s‘ konnte nicht erstellt werden; siehe <PERSON>", "commands.datapack.create.metadata_encode_failure": "Metadaten für Datenpaket ‚%s‘ konnten nicht kodiert werden: %s", "commands.datapack.create.success": "Neues leeres Datenpaket ‚%s‘ wurde erstellt", "commands.datapack.disable.failed": "Datenpaket ‚%s‘ ist bereits deaktiviert", "commands.datapack.disable.failed.feature": "Datenpaket ‚%s‘ kann nicht deaktiviert werden, da es Bestandteil eines aktiven Umschalters ist!", "commands.datapack.enable.failed": "Datenpaket ‚%s‘ ist bereits aktiviert", "commands.datapack.enable.failed.no_flags": "Das Datenpaket ‚%s‘ kann nicht aktiviert werden, weil die benötigten Umschalter für diese Welt nicht aktiviert sind: %s!", "commands.datapack.list.available.none": "Es sind keine weiteren Datenpakete verfügbar", "commands.datapack.list.available.success": "Es ist/sind %s Datenpaket(e) verfügbar: %s", "commands.datapack.list.enabled.none": "Es sind keine Datenpakete aktiviert", "commands.datapack.list.enabled.success": "Es ist/sind %s Datenpaket(e) aktiviert: %s", "commands.datapack.modify.disable": "Datenpaket %s wird deaktiviert", "commands.datapack.modify.enable": "Datenpaket %s wird aktiviert", "commands.datapack.unknown": "Unbekanntes Datenpaket ‚%s‘", "commands.debug.alreadyRunning": "Tick-Aufzeichnung wurde bereits gestartet", "commands.debug.function.noRecursion": "Es kann nicht aus einer Funktion heraus aufgezeichnet werden", "commands.debug.function.noReturnRun": "Ablaufaufzeichnung kann nicht mit „return run“ verwendet werden", "commands.debug.function.success.multiple": "%s Be<PERSON>hl(e) von %s Funktionen wurde(n) in die Ausgabedatei %s aufgezeichnet", "commands.debug.function.success.single": "%s Be<PERSON>hl(e) der Funktion ‚%s‘ wurde(n) in die Ausgabedatei %s aufgezeichnet", "commands.debug.function.traceFailed": "Funktion konnte nicht aufgezeichnet werden", "commands.debug.notRunning": "Tick-Aufzeichnung ist nicht aktiv", "commands.debug.started": "<PERSON>ick<PERSON>Aufzeichnung gestartet", "commands.debug.stopped": "Tick‐Aufzeichnung wurde nach %s Sekunde(n) und %s Tick(s) beendet (%s Tick(s) pro Sekunde)", "commands.defaultgamemode.success": "Standardspielmodus wurde auf %s gesetzt", "commands.deop.failed": "Nichts hat sich geändert, der Spieler ist bereits kein Operator", "commands.deop.success": "%s ist kein Serveroperator mehr", "commands.dialog.clear.multiple": "Dialog wird für %s Spieler geschlossen", "commands.dialog.clear.single": "Dialog wird für %s geschlossen", "commands.dialog.show.multiple": "Dialog wird %s Spielern angezeigt", "commands.dialog.show.single": "Dialog wird %s angezeigt", "commands.difficulty.failure": "Nichts hat sich geändert, die Schwierigkeit liegt bereits bei %s", "commands.difficulty.query": "Die Schwierigkeit ist %s", "commands.difficulty.success": "Schwierigkeit wurde auf %s gesetzt", "commands.drop.no_held_items": "Objekt kann keine Gegenstände tragen", "commands.drop.no_loot_table": "Objekt %s hat keine Beutetabelle", "commands.drop.no_loot_table.block": "Block %s hat keine Be<PERSON>belle", "commands.drop.success.multiple": "%s Gegenstände fallen gelassen", "commands.drop.success.multiple_with_table": "%s Gegenstände aus Beutetabelle %s fallen gelassen", "commands.drop.success.single": "%s %s fallen gelassen", "commands.drop.success.single_with_table": "%s %s aus Beutetabelle %s fallen gelassen", "commands.effect.clear.everything.failed": "<PERSON><PERSON> besitzt keine zu entfernenden Effekte", "commands.effect.clear.everything.success.multiple": "Alle Effekte wurden von %s Zielen entfernt", "commands.effect.clear.everything.success.single": "Alle Effekte wurden von %s entfernt", "commands.effect.clear.specific.failed": "<PERSON><PERSON> besitzt den zu entfernenden Effekt nicht", "commands.effect.clear.specific.success.multiple": "Effekt %s wurde von %s Zielen entfernt", "commands.effect.clear.specific.success.single": "Effekt %s wurde von %s entfernt", "commands.effect.give.failed": "Effekt konnte nicht angewandt werden (das Ziel ist entweder resistent oder besitzt eine stärkere Stufe)", "commands.effect.give.success.multiple": "Effekt %s wurde auf %s Ziele angewandt", "commands.effect.give.success.single": "Effekt %s wurde auf %s angewandt", "commands.enchant.failed": "Nichts hat sich geändert, die Ziele halten entweder keinen Gegenstand in ihrer Haupthand oder die Verzauberung konnte nicht angewandt werden", "commands.enchant.failed.entity": "%s ist kein gültiges Objekt für diesen Befehl", "commands.enchant.failed.incompatible": "%s kann damit nicht verz<PERSON>bert werden", "commands.enchant.failed.itemless": "%s hält keinen Gegenstand in der Haupthand", "commands.enchant.failed.level": "Stufe %s ist zu hoch, die maximale Stufe dieser Verzauberung beträgt %s", "commands.enchant.success.multiple": "Verzauberung %s wurde auf %s Objekte angewandt", "commands.enchant.success.single": "Verzauberung %s wurde auf den Gegenstand von %s angewandt", "commands.execute.blocks.toobig": "Zu viele Blöcke im angegebenen Bereich (maximal %s, angegeben %s)", "commands.execute.conditional.fail": "Test fehlgeschlagen", "commands.execute.conditional.fail_count": "Test fehlgeschlagen, Anzahl: %s", "commands.execute.conditional.pass": "Test erfol<PERSON><PERSON>ich", "commands.execute.conditional.pass_count": "Test er<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>: %s", "commands.execute.function.instantiationFailure": "Funktion ‚%s‘ konnte nicht ausgewertet werden: %s", "commands.experience.add.levels.success.multiple": "%s Erfahrungstufen wurden an %s Spieler gegeben", "commands.experience.add.levels.success.single": "%s Erfahrungstufen wurden an %s gegeben", "commands.experience.add.points.success.multiple": "%s Erfahrungspunkte wurden an %s Spieler gegeben", "commands.experience.add.points.success.single": "%s Erfahrungspunkte wurden an %s gegeben", "commands.experience.query.levels": "%s hat Erfahrungsstufe %s", "commands.experience.query.points": "%s hat %s Erfahrungspunkte", "commands.experience.set.levels.success.multiple": "Erfahrungsstufen von %2$s Spielern wurden auf %1$s gesetzt", "commands.experience.set.levels.success.single": "Erfahrungsstufe von %2$s wurde auf %1$s gesetzt", "commands.experience.set.points.invalid": "Erfahrungspunkte dürfen nicht höher gesetzt werden als das Maximum der aktuellen Erfahrungsstufe des Spielers", "commands.experience.set.points.success.multiple": "Erfahrungspunkte von %2$s Spielern wurden auf %1$s gesetzt", "commands.experience.set.points.success.single": "Erfahrungspunkte von %2$s wurden auf %1$s gesetzt", "commands.fill.failed": "<PERSON><PERSON> wurden keine Blöcke platziert", "commands.fill.success": "%s Block/Blöcke wurde(n) erfolgreich platziert", "commands.fill.toobig": "Zu viele Blöcke im angegebenen Bereich (maximal %s, angegeben %s)", "commands.fillbiome.success": "Biome zwischen %s, %s, %s und %s, %s, %s festgelegt", "commands.fillbiome.success.count": "%s Biom‐Eintrag/Einträge zwischen %s, %s, %s und %s, %s, %s festgelegt", "commands.fillbiome.toobig": "Zu viele Blöcke im angegebenen Raum (maximal %s, angegeben %s)", "commands.forceload.added.failure": "Nichts hat sich ge<PERSON>ndert, diese Chunks werden bereits dauerhaft geladen", "commands.forceload.added.multiple": "%s Chunks in %s von %s bis %s werden nun dauerhaft geladen", "commands.forceload.added.none": "In %s werden keine Chunks dauerhaft geladen", "commands.forceload.added.single": "Chunk %s in %s wird nun dauerhaft geladen", "commands.forceload.list.multiple": "In %2$s werden %1$s Chunks dauerhaft geladen: %3$s", "commands.forceload.list.single": "In %s wird ein Chunk dauerhaft geladen: %s", "commands.forceload.query.failure": "Chunk %s in %s wird nicht dauerhaft geladen", "commands.forceload.query.success": "Chunk %s in %s wird dauerhaft geladen", "commands.forceload.removed.all": "In %s werden nun keine Chunks mehr dauerhaft geladen", "commands.forceload.removed.failure": "Nichts hat sich ge<PERSON><PERSON>t, diese Chunks wurden auch bisher nicht dauerhaft geladen", "commands.forceload.removed.multiple": "%s Chunks in %s von %s bis %s werden nun nicht mehr dauerhaft geladen", "commands.forceload.removed.single": "Chunk %s in %s wird nun nicht mehr dauerhaft geladen", "commands.forceload.toobig": "Zu viele Chunks im angegebenen Bereich (maximal %s, angegeben %s)", "commands.function.error.argument_not_compound": "Ungültiger Argumenttyp: ‚%s‘ ist kein Verbund", "commands.function.error.missing_argument": "Fehlendes Argument ‚%2$s‘ für die Funktion ‚%1$s‘", "commands.function.error.missing_arguments": "Fehlende Argumente für die Funktion ‚%s‘", "commands.function.error.parse": "Bei der Auswertung des Makros ‚%s‘ hat der Befehl ‚%s‘ einen Fehler verursacht: %s", "commands.function.instantiationFailure": "Funktion ‚%s‘ konnte nicht ausgewertet werden: %s", "commands.function.result": "Funktion ‚%s‘ lieferte %s zurück", "commands.function.scheduled.multiple": "Funktionen (%s) werden ausgeführt", "commands.function.scheduled.no_functions": "Es wurde keine Funktion mit dem Namen ‚%s‘ gefunden", "commands.function.scheduled.single": "Funktion ‚%s‘ wird ausgeführt", "commands.function.success.multiple": "%s Befehl(e) von %s Funktionen wurde(n) ausgeführt", "commands.function.success.multiple.result": "%s Funktionen ausgeführt", "commands.function.success.single": "%s Be<PERSON>hl(e) der Funktion ‚%s‘ wurde(n) ausgeführt", "commands.function.success.single.result": "Funktion ‚%2$s‘ lieferte %1$s zurück", "commands.gamemode.success.other": "Der Spielmodus von %s wurde auf %s gesetzt", "commands.gamemode.success.self": "<PERSON><PERSON> wurde auf %s gesetzt", "commands.gamerule.query": "Die Spielregel %s ist derzeit ‚%s‘", "commands.gamerule.set": "Spielregel %s wurde auf ‚%s‘ gesetzt", "commands.give.failed.toomanyitems": "Kann nicht mehr als %s %s geben", "commands.give.success.multiple": "%s %s an %s Spieler gegeben", "commands.give.success.single": "%s %s an %s gegeben", "commands.help.failed": "Unbekannter Befehl oder unzureichende Berechtigung", "commands.item.block.set.success": "Inhalt eines Inventarplatzes bei (%s, %s, %s) wurde durch %s ersetzt", "commands.item.entity.set.success.multiple": "Inhalt eines Inventarplatzes von %s Objekten wurde durch %s ersetzt", "commands.item.entity.set.success.single": "Inhalt eines Inventarplatzes von %s wurde durch %s ersetzt", "commands.item.source.no_such_slot": "Quelle besitzt Inventarplatz %s nicht", "commands.item.source.not_a_container": "Quellposition %s, %s, %s besitzt kein Inventar", "commands.item.target.no_changed.known_item": "Kein Ziel lässt %s bei Inventarplatz %s zu", "commands.item.target.no_changes": "Kein Ziel lässt Gegenstände bei Inventarplatz %s zu", "commands.item.target.no_such_slot": "Ziel besitzt Inventarplatz %s nicht", "commands.item.target.not_a_container": "Zielposition %s, %s, %s besitzt kein Inventar", "commands.jfr.dump.failed": "Fehler beim Speichern der JFR‐Aufzeichnung: %s", "commands.jfr.start.failed": "Fehler beim Starten der JFR‐Aufzeichnung", "commands.jfr.started": "JFR‐Aufzeichnung gestartet", "commands.jfr.stopped": "JFR‐Aufzeichnung gestoppt und in %s gespeichert", "commands.kick.owner.failed": "Der Betreiber eines LAN‐Spiels kann nicht hinausgeworfen werden", "commands.kick.singleplayer.failed": "In nicht freigegebenen Einzelspielen kann nicht hinausgeworfen werden", "commands.kick.success": "%s wurde hinausgeworfen: %s", "commands.kill.success.multiple": "%s Objekte wurden beseitigt", "commands.kill.success.single": "%s wurde beseitigt", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Es sind %s von maximal %s S<PERSON>lern online: %s", "commands.locate.biome.not_found": "Es konnte in angemessener Entfernung kein Biom des Typs „%s“ gefunden werden", "commands.locate.biome.success": "%s ist am nächsten bei %s (%s Blöcke entfernt)", "commands.locate.poi.not_found": "Es konnte in angemessener Entfernung kein Zielpunkt des Typs „%s“ gefunden werden", "commands.locate.poi.success": "Zielpunkt %s ist am nächsten bei %s (%s Blöcke entfernt)", "commands.locate.structure.invalid": "Es gibt kein Bauwerk des Typs „%s“", "commands.locate.structure.not_found": "Es konnte in der Nähe kein Bauwerk des Typs „%s“ gefunden werden", "commands.locate.structure.success": "Nächstgelegene Bauwerk %s ist bei %s (%s Blöcke entfernt)", "commands.message.display.incoming": "%s flüstert dir zu: %s", "commands.message.display.outgoing": "Du flüsterst %s zu: %s", "commands.op.failed": "Nichts hat sich geändert, der Spieler ist bereits ein Operator", "commands.op.success": "%s wurde zum Serveroperator ernannt", "commands.pardon.failed": "Nichts hat sich geändert, der Spieler ist nicht gesperrt", "commands.pardon.success": "%s wurde entsperrt", "commands.pardonip.failed": "Nichts hat sich geändert, diese IP-Adresse ist nicht gesperrt", "commands.pardonip.invalid": "Ungültige IP-Adresse", "commands.pardonip.success": "IP-Adresse %s wurde entsperrt", "commands.particle.failed": "Partikel war für niemanden sichtbar", "commands.particle.success": "Partikel %s wird gezeigt", "commands.perf.alreadyRunning": "Leistungsaufzeichnung wurde bereits gestartet", "commands.perf.notRunning": "Leistungsaufzeichnung ist nicht aktiv", "commands.perf.reportFailed": "Debug‐Bericht konnte nicht erstellt werden", "commands.perf.reportSaved": "Debug‐Bericht wurde erstellt in %s", "commands.perf.started": "10‐sekündige Leistungsaufzeichnung wurde gestartet (benutze ‚/perf stop‘, um sie vorzeitig zu beenden)", "commands.perf.stopped": "Leistungsaufzeichnung wurde nach %s Sekunde(n) und %s Tick(s) beendet (%s Tick(s) pro Sekunde)", "commands.place.feature.failed": "Merkmal konnte nicht platziert werden", "commands.place.feature.invalid": "Es gibt kein Merkmal des Typs „%s“", "commands.place.feature.success": "„%s“ wurde bei %s, %s, %s platziert", "commands.place.jigsaw.failed": "Verbund konnte nicht generiert werden", "commands.place.jigsaw.invalid": "Es gibt keine Konstruktionsvorlagenquelle des Typs „%s“", "commands.place.jigsaw.success": "Verbund wurde bei %s, %s, %s generiert", "commands.place.structure.failed": "Bauwerk konnte nicht platziert werden", "commands.place.structure.invalid": "Es gibt kein Bauwerk des Typs „%s“", "commands.place.structure.success": "Bauwerk „%s“ wurde bei %s, %s, %s generiert", "commands.place.template.failed": "Konstruktionsvorlage konnte nicht platziert werden", "commands.place.template.invalid": "Es existiert keine Konstruktionsvorlage mit der ID „%s“", "commands.place.template.success": "Konstruktionsvorlage „%s“ bei %s, %s, %s geladen", "commands.playsound.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> ist zu weit entfernt, um gehört zu werden", "commands.playsound.success.multiple": "Geräusch %s wurde für %s Spieler abgespielt", "commands.playsound.success.single": "Geräusch %s wurde für %s abgespielt", "commands.publish.alreadyPublished": "LAN-Spiel ist bereits unter Port %s erreichbar", "commands.publish.failed": "Es konnte kein LAN-Spiel erstellt werden", "commands.publish.started": "LAN-Spiel unter Port %s erreichbar", "commands.publish.success": "LAN-Spiel ist jetzt unter Port %s erreichbar", "commands.random.error.range_too_large": "Zahlenbereich der Zufallszahl darf höchstens 2147483646 Werte umfassen", "commands.random.error.range_too_small": "Zahlenbereich der Zufallszahl muss mindestens 2 Werte umfassen", "commands.random.reset.all.success": "%s Zufallsfolge(n) wurde(n) zurückgesetzt", "commands.random.reset.success": "Zufallsfolge ‚%s‘ wurde zurückgesetzt", "commands.random.roll": "%s hat %s gewürfelt (zwischen %s und %s einschließlich)", "commands.random.sample.success": "Zufallswert: %s", "commands.recipe.give.failed": "<PERSON><PERSON> wurden keine neuen Rezepte freigeschaltet", "commands.recipe.give.success.multiple": "%s Rezept(e) wurde(n) für %s Spieler freigeschaltet", "commands.recipe.give.success.single": "%s Rezept(e) wurde(n) für %s freigeschaltet", "commands.recipe.take.failed": "Es konnten keine Rezepte entfernt werden", "commands.recipe.take.success.multiple": "%s Rezept(e) wurde(n) von %s <PERSON><PERSON>lern entfernt", "commands.recipe.take.success.single": "%s Rezept(e) wurde(n) von %s entfernt", "commands.reload.failure": "Neuladen fehlgeschlagen, alte Daten werden beibehalten", "commands.reload.success": "Datenpakete werden neu geladen", "commands.ride.already_riding": "%s ist bereits auf %s aufgestiegen", "commands.ride.dismount.success": "%s ist von %s abgestiegen", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON> kann nichts aufsteigen", "commands.ride.mount.failure.generic": "%s konnte nicht auf %s aufsteigen", "commands.ride.mount.failure.loop": "Kann das Objekt nicht auf sich selbst oder einen seiner Passagiere aufsteigen lassen", "commands.ride.mount.failure.wrong_dimension": "<PERSON><PERSON> in anderer Dimension kann nicht aufgestiegen werden", "commands.ride.mount.success": "%s ist auf %s aufgestiegen", "commands.ride.not_riding": "%s ist auf kein Fahrmittel aufgestiegen", "commands.rotate.success": "%s wurde gedreht", "commands.save.alreadyOff": "Automatisches Speichern ist bereits deaktiviert", "commands.save.alreadyOn": "Automatisches Speichern ist bereits aktiviert", "commands.save.disabled": "Automatisches Speichern ist jetzt deaktiviert", "commands.save.enabled": "Automatisches Speichern ist jetzt aktiviert", "commands.save.failed": "Spiel konnte nicht gespeichert werden (ist genug freier Speicherplatz vorhanden?)", "commands.save.saving": "Spiel wird gespeichert (das kann einen Moment dauern!)", "commands.save.success": "Spiel wurde ges<PERSON>rt", "commands.schedule.cleared.failure": "Es gibt keine Planungen mit der ID %s", "commands.schedule.cleared.success": "<PERSON>s wurde(n) %s Planung(en) mit der ID %s entfernt", "commands.schedule.created.function": "Funktion ‚%s‘ wurde in %s Tick(s) zur Spielzeit %s eingeplant", "commands.schedule.created.tag": "Etikett ‚%s‘ wurde in %s Tick(s) zur Spielzeit %s eingeplant", "commands.schedule.macro": "<PERSON><PERSON><PERSON> kö<PERSON>n nicht geplant werden", "commands.schedule.same_tick": "<PERSON><PERSON>r den aktuellen Tick kann nichts eingeplant werden", "commands.scoreboard.objectives.add.duplicate": "Ein Punkte‐Ziel mit diesem Namen existiert bereits", "commands.scoreboard.objectives.add.success": "Neues Punkte‐Ziel %s wurde erstellt", "commands.scoreboard.objectives.display.alreadyEmpty": "Nichts hat sich geändert, die Anzeigeposition ist bereits leer", "commands.scoreboard.objectives.display.alreadySet": "Nichts hat sich geändert, die Anzeigeposition zeigt bereits das Punkte‐Ziel", "commands.scoreboard.objectives.display.cleared": "Alle Punkte‐Ziele in Anzeigeposition %s wurden zurückgesetzt", "commands.scoreboard.objectives.display.set": "Anzeigeposition %s zeigt nun das Punkte‐Ziel %s", "commands.scoreboard.objectives.list.empty": "<PERSON>s gibt keine Punkte‐Ziele", "commands.scoreboard.objectives.list.success": "Es gibt %s Punkte‐Ziel(e): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Automatische Aktualisierung des Anzeigenamens wurde für Punkte‐Ziel ‚%s‘ deaktiviert", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Automatische Aktualisierung des Anzeigenamens wurde für Punkte‐Ziel ‚%s‘ aktiviert", "commands.scoreboard.objectives.modify.displayname": "Anzeigename des Punkte‐Ziels %s wurde zu %s geändert", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Standard‐Zahlendarstellung von Punkte‐Ziel ‚%s‘ wurde zurückgesetzt", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Standard‐Zahlendarstellung von Punkte‐Ziel ‚%s‘ wurde geändert", "commands.scoreboard.objectives.modify.rendertype": "Darstellungsart von Punkte‐Ziel %s wurde geändert", "commands.scoreboard.objectives.remove.success": "Punkte‐Ziel %s wurde entfernt", "commands.scoreboard.players.add.success.multiple": "%2$s wurde für %3$s Objekte um %1$s erhöht", "commands.scoreboard.players.add.success.single": "%2$s wurde für %3$s um %1$s erhöht (jetzt %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Anzeigename in %2$s wurde für %1$s Objekte zurückgesetzt", "commands.scoreboard.players.display.name.clear.success.single": "Anzeigename in %2$s wurde für %1$s zurückgesetzt", "commands.scoreboard.players.display.name.set.success.multiple": "Anzeigename in %3$s wurde für %2$s Objekte zu %1$s geändert", "commands.scoreboard.players.display.name.set.success.single": "Anzeigename in %3$s wurde für %2$s zu %1$s geändert", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Zahlendarstellung in %2$s wurde für %1$s Objekte zurückgesetzt", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Zahlendarstellung in %2$s wurde für %1$s zurückgesetzt", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Zahlendarstellung in %2$s wurde für %1$s Objekte geändert", "commands.scoreboard.players.display.numberFormat.set.success.single": "Zahlendarstellung in %2$s wurde für %1$s geändert", "commands.scoreboard.players.enable.failed": "Nichts hat sich geändert, der Auslöser ist bereits aktiviert", "commands.scoreboard.players.enable.invalid": "Dieser Befehl funktioniert nur für Auslöser (trigger‐Punkte‐Ziele)", "commands.scoreboard.players.enable.success.multiple": "Auslöser %s wurde für %s Objekte aktiviert", "commands.scoreboard.players.enable.success.single": "Auslöser %s wurde für %s aktiviert", "commands.scoreboard.players.get.null": "<PERSON>rt von %s für %s kann nicht abgefragt werden, da keiner gesetzt ist", "commands.scoreboard.players.get.success": "%s hat %s %s", "commands.scoreboard.players.list.empty": "Es gibt keine überwachten Objekte", "commands.scoreboard.players.list.entity.empty": "%s hat keine Punktestände", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s hat %s Punktestand/‐stände:", "commands.scoreboard.players.list.success": "Es gibt %s überwachte(s) Objekt(e): %s", "commands.scoreboard.players.operation.success.multiple": "%s wurde für %s Objekte geändert", "commands.scoreboard.players.operation.success.single": "%s wurde für %s auf %s geändert", "commands.scoreboard.players.remove.success.multiple": "%2$s wurde für %3$s Objekte um %1$s verringert", "commands.scoreboard.players.remove.success.single": "%2$s wurde für %3$s um %1$s verringert (jetzt %4$s)", "commands.scoreboard.players.reset.all.multiple": "Alle Punktestände wurden für %s Objekte gelöscht", "commands.scoreboard.players.reset.all.single": "Alle Punktestände wurden für %s gelöscht", "commands.scoreboard.players.reset.specific.multiple": "%s wurde für %s Objekte gelöscht", "commands.scoreboard.players.reset.specific.single": "%s wurde für %s gelöscht", "commands.scoreboard.players.set.success.multiple": "%s wurde für %s Objekte auf %s gesetzt", "commands.scoreboard.players.set.success.single": "%s wurde für %s auf %s gesetzt", "commands.seed.success": "Der Startwert ist %s", "commands.setblock.failed": "Block konnte nicht platziert werden", "commands.setblock.success": "Block bei (%s, %s, %s) wurde geändert", "commands.setidletimeout.success": "Spieler werden nun nach %s Minute(n) Untätigkeit vom Server getrennt", "commands.setidletimeout.success.disabled": "Spieler werden nun bei Untätigkeit nicht vom Server getrennt", "commands.setworldspawn.failure.not_overworld": "Welteinstiegspunkt kann nur für die Oberwelt gesetzt werden", "commands.setworldspawn.success": "Welteinstiegspunkt wurde auf (%s, %s, %s) [%s] gesetzt", "commands.spawnpoint.success.multiple": "Einstiegspunkt von %6$s Spielern wurde auf (%1$s, %2$s, %3$s) [%4$s] in %5$s gesetzt", "commands.spawnpoint.success.single": "Einstiegspunkt von %6$s wurde auf (%1$s, %2$s, %3$s) [%4$s] in %5$s gesetzt", "commands.spectate.not_spectator": "%s ist nicht im Zuschauermodus", "commands.spectate.self": "Du kannst dich nicht selbst beobachten", "commands.spectate.success.started": "Du beobachtest nun %s", "commands.spectate.success.stopped": "Du beobachtest kein Objekt mehr", "commands.spreadplayers.failed.entities": "%s Objekt(e) konnte(n) nicht um (%s, %s) verteilt werden (zu wenig Platz für Objekte – versuche, höchstens %s zu verteilen)", "commands.spreadplayers.failed.invalid.height": "Ungültiger Wert für maxHeight: %s. Er muss höher sein als die Weltuntergrenze %s", "commands.spreadplayers.failed.teams": "%s Team(s) konnte(n) nicht um (%s, %s) verteilt werden (zu wenig Platz für Teams – versuche, höchstens %s zu verteilen)", "commands.spreadplayers.success.entities": "%s Objekt(e) wurde(n) rund um (%s, %s) mit einem Abstand von durchschnittlich %s Block/Blöcken zueinander verteilt", "commands.spreadplayers.success.teams": "%s Team(s) wurde(n) rund um (%s, %s) mit einem Abstand von durchschnittlich %s Block/Blöcken zueinander verteilt", "commands.stop.stopping": "Server wird gestoppt", "commands.stopsound.success.source.any": "Alle %s-Geräusche wurden gestoppt", "commands.stopsound.success.source.sound": "Geräusch %s für die Geräuschart %s wurde gestoppt", "commands.stopsound.success.sourceless.any": "Alle Geräusche wurden gestoppt", "commands.stopsound.success.sourceless.sound": "Geräusch %s wurde gestoppt", "commands.summon.failed": "Objekt konnte nicht erzeugt werden", "commands.summon.failed.uuid": "Objekt konnte nicht erzeugt werden; die UUID existiert bereits", "commands.summon.invalidPosition": "Ungültige Position zum Erzeugen", "commands.summon.success": "%s wurde erzeugt", "commands.tag.add.failed": "Entweder hat das Ziel bereits dieses Etikett oder es hat zu viele Etiketten", "commands.tag.add.success.multiple": "Etikett ‚%s‘ wurde %s Objekten hinzugefügt", "commands.tag.add.success.single": "Etikett ‚%s‘ wurde %s hinzugefügt", "commands.tag.list.multiple.empty": "Die %s ausgewählten Objekte haben kein Etikett", "commands.tag.list.multiple.success": "Die %s ausgewählten Objekte haben insgesamt %s Etiketten: %s", "commands.tag.list.single.empty": "%s hat keine Etiketten", "commands.tag.list.single.success": "%s hat %s Etiketten: %s", "commands.tag.remove.failed": "<PERSON><PERSON> besitzt dieses Etikett nicht", "commands.tag.remove.success.multiple": "Etikett ‚%s‘ wurde von %s Objekten entfernt", "commands.tag.remove.success.single": "Etikett ‚%s‘ wurde von %s entfernt", "commands.team.add.duplicate": "Ein Team mit diesem Namen existiert bereits", "commands.team.add.success": "Team %s wurde erste<PERSON>t", "commands.team.empty.success": "%s Mitglied(er) wurde(n) aus Team %s entfernt", "commands.team.empty.unchanged": "Nichts hat sich geändert, das Team ist bereits leer", "commands.team.join.success.multiple": "%s <PERSON><PERSON><PERSON><PERSON><PERSON> wurden zu Team %s hinzugefügt", "commands.team.join.success.single": "%s wurde zu Team %s hinz<PERSON><PERSON><PERSON>gt", "commands.team.leave.success.multiple": "%s Mitglieder wurden aus Teams entfernt", "commands.team.leave.success.single": "%s wurde aus jedem Team entfernt", "commands.team.list.members.empty": "Team %s hat keine Mitglieder", "commands.team.list.members.success": "Team %s hat %s Mitglied(er): %s", "commands.team.list.teams.empty": "<PERSON>s gibt keine Teams", "commands.team.list.teams.success": "Es gibt %s Team(s): %s", "commands.team.option.collisionRule.success": "Schieberegel für Team %s wurde auf ‚%s‘ gesetzt", "commands.team.option.collisionRule.unchanged": "Nichts hat sich geändert, die Schieberegel hat bereits diesen Wert", "commands.team.option.color.success": "Farbe von Team %s wurde auf %s gesetzt", "commands.team.option.color.unchanged": "Nichts hat sich ge<PERSON><PERSON>t, das Team hat bereits diese Farbe", "commands.team.option.deathMessageVisibility.success": "Sichtbarkeit der Todesmeldungen von Team %s wurde auf ‚%s‘ gesetzt", "commands.team.option.deathMessageVisibility.unchanged": "Nichts hat sich geändert, die Sichtbarkeit der Todesmeldungen hat bereits diesen Wert", "commands.team.option.friendlyfire.alreadyDisabled": "Nichts hat sich ge<PERSON><PERSON><PERSON>, für dieses Team ist Eigenbeschuss bereits deaktiviert", "commands.team.option.friendlyfire.alreadyEnabled": "Nichts hat sich ge<PERSON><PERSON>t, für dieses Team ist Eigenbeschuss bereits aktiviert", "commands.team.option.friendlyfire.disabled": "Eigenbeschuss wurde für Team %s deaktiviert", "commands.team.option.friendlyfire.enabled": "Eigenbeschuss wurde für Team %s aktiviert", "commands.team.option.name.success": "Name von Team %s wurde aktualisiert", "commands.team.option.name.unchanged": "Nichts hat sich ge<PERSON>ndert, das Team hat bereits diesen Namen", "commands.team.option.nametagVisibility.success": "Sichtbarkeit der Spielernamen von Team %s wurde auf ‚%s‘ gesetzt", "commands.team.option.nametagVisibility.unchanged": "Nichts hat sich geändert, die Sichtbarkeit der Spielernamen hat bereits diesen Wert", "commands.team.option.prefix.success": "Team-Präfix wurde auf %s gesetzt", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nichts hat sich ge<PERSON><PERSON><PERSON>, das Team kann bereits unsichtbare Teammitglieder nicht sehen", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nichts hat sich ge<PERSON><PERSON><PERSON>, das Team kann bereits unsichtbare Teammitglieder sehen", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s kann keine unsichtbaren Teammitglieder mehr sehen", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s kann nun unsichtbare Teammitglieder sehen", "commands.team.option.suffix.success": "Team-Suffix wurde auf %s gesetzt", "commands.team.remove.success": "Team %s wurde entfernt", "commands.teammsg.failed.noteam": "<PERSON> musst in einem Team sein, um dein Team zu benachrichtigen", "commands.teleport.invalidPosition": "Ungültige Position zum Teleportieren", "commands.teleport.success.entity.multiple": "%s Objekte wurden zu %s teleportiert", "commands.teleport.success.entity.single": "%s wurde zu %s teleportiert", "commands.teleport.success.location.multiple": "%s Objekte wurden zu (%s, %s, %s) teleportiert", "commands.teleport.success.location.single": "%s wurde zu (%s, %s, %s) teleportiert", "commands.test.batch.starting": "Umgebung %s, Teil %s wird gestartet", "commands.test.clear.error.no_tests": "<PERSON>s wurden keine Tests zum Löschen gefunden", "commands.test.clear.success": "%s Konstruktion(en) wurde(n) gelöscht", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON><PERSON>, um in die Zwischenablage zu kopieren", "commands.test.create.success": "Testaufbau wurde für Test %s erstellt", "commands.test.error.no_test_containing_pos": "Es kann keine Testinstanz gefunden werden, die %s, %s, %s enthält", "commands.test.error.no_test_instances": "<PERSON>s wurden keine Testinstanzen gefunden", "commands.test.error.non_existant_test": "Test %s konnte nicht gefunden werden", "commands.test.error.structure_not_found": "Testkonstruktion %s konnte nicht gefunden werden", "commands.test.error.test_instance_not_found": "Testinstanz‐Blockobjekt konnte nicht gefunden werden", "commands.test.error.test_instance_not_found.position": "Testinstanz‐Blockobjekt konnte nicht für Test bei %s, %s, %s gefunden werden", "commands.test.error.too_large": "Die Konstruktion darf in jeder Richtung höchstens %s Blö<PERSON> groß sein", "commands.test.locate.done": "Suche abgeschlossen, %s Konstruktion(en) gefunden", "commands.test.locate.found": "Konstruktion bei %s gefunden (Entfernung: %s)", "commands.test.locate.started": "Testkonstruktion(en) werden ausfindig gemacht, das könnte ein bisschen dauern ...", "commands.test.no_tests": "Keine Tests zum Ausführen vorhanden", "commands.test.relative_position": "Position relativ zu %s: %s", "commands.test.reset.error.no_tests": "<PERSON><PERSON> wurden keine Tests zum Zurücksetzen gefunden", "commands.test.reset.success": "%s Konstruktion(en) wurde(n) zurückgesetzt", "commands.test.run.no_tests": "Keine Tests gefunden", "commands.test.run.running": "%s Test(s) wird/werden ausgeführt …", "commands.test.summary": "Spielüberprüfung abgeschlossen! %s Test(s) wurde(n) ausgeführt", "commands.test.summary.all_required_passed": "Alle erforderlichen Tests waren erfolgreich :)", "commands.test.summary.failed": "%s erforderliche(r) Test(s) schlug(en) fehl :(", "commands.test.summary.optional_failed": "%s optionale(r) Test(s) schlug(en) fehl", "commands.tick.query.percentiles": "Perzentile: P50: %s ms, P95: %s ms, P99: %s ms; Stichprobe: %s", "commands.tick.query.rate.running": "Soll‐Tickrate: %s pro Sekunde.\nDurchschnittliche Zeit pro Tick: %s ms (Soll: %s ms)", "commands.tick.query.rate.sprinting": "Soll‐Tickrate: %s pro Sekunde (nicht berücksichtigt, nur zur Referenz).\nDurchschnittliche Zeit pro Tick: %s ms", "commands.tick.rate.success": "Soll‐Tickrate wurde auf %s pro Sekunde gesetzt", "commands.tick.sprint.report": "Beschleunigung wurde mit %s Ticks pro Sekunde bzw. %s ms pro Tick abgeschlossen", "commands.tick.sprint.stop.fail": "Es findet derzeit keine Tick‐Beschleunigung statt", "commands.tick.sprint.stop.success": "Tick‐Beschleunigung wurde abgebrochen", "commands.tick.status.frozen": "Das Spiel ist eingefroren", "commands.tick.status.lagging": "Das Spiel läuft, kann jedoch nicht mit der Soll‐Tickrate mithalten", "commands.tick.status.running": "Das Spiel läuft normal", "commands.tick.status.sprinting": "Das Spiel läuft beschleunigt", "commands.tick.step.fail": "Spiel konnte nicht fortgeschritten werden – es muss zunächst eingefroren werden", "commands.tick.step.stop.fail": "Es findet derzeit kein Tick‐Fortschreiten statt", "commands.tick.step.stop.success": "Tick‐Fortschreiten wurde abgebrochen", "commands.tick.step.success": "Es wird um %s Tick(s) fortgeschritten", "commands.time.query": "Die aktuelle Zeit ist %s", "commands.time.set": "Zeit wurde auf %s gesetzt", "commands.title.cleared.multiple": "Alle Titel wurden für %s Spieler entfernt", "commands.title.cleared.single": "Alle Titel wurden für %s entfernt", "commands.title.reset.multiple": "Titel-Anzeigezeiten wurden für %s Spieler zurückgesetzt", "commands.title.reset.single": "Titel-Anzeigezeiten wurden für %s zurückgesetzt", "commands.title.show.actionbar.multiple": "Neuer Aktionsleistentitel wird für %s Spieler angezeigt", "commands.title.show.actionbar.single": "Neuer Aktionsleistentitel wird für %s angezeigt", "commands.title.show.subtitle.multiple": "Neuer Untertitel wird für %s Spieler angezeigt", "commands.title.show.subtitle.single": "Neuer Untertitel wird für %s angezeigt", "commands.title.show.title.multiple": "Neuer Titel wird für %s Spieler angezeigt", "commands.title.show.title.single": "Neuer Titel wird für %s angezeigt", "commands.title.times.multiple": "Titel-Anzeigezeiten wurden für %s Spieler geändert", "commands.title.times.single": "Titel-Anzeigezeiten wurden für %s geändert", "commands.transfer.error.no_players": "Es muss mindestens ein zu übertragender Spieler angegeben werden", "commands.transfer.success.multiple": "%s Spieler werden an %s:%s übertragen", "commands.transfer.success.single": "%s wird an %s:%s übertragen", "commands.trigger.add.success": "%s wurde ausgelöst (Wert wurde um %s erhöht)", "commands.trigger.failed.invalid": "Punkte‐Ziel ist kein Auslöser (trigger‐Punkte‐Ziel)", "commands.trigger.failed.unprimed": "Du kannst dieses Punkte‐Ziel noch nicht auslösen", "commands.trigger.set.success": "%s wurde ausgelöst (Wert wurde auf %s gesetzt)", "commands.trigger.simple.success": "%s wurde ausgelöst", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "<PERSON><PERSON> in %s", "commands.waypoint.list.success": "%s Wegpunkt(e) in %s: %s", "commands.waypoint.modify.color": "Wegpunktfarbe ist nun %s", "commands.waypoint.modify.color.reset": "Wegpunktfarbe wurde zurückgesetzt", "commands.waypoint.modify.style": "Aussehen des Wegpunkts wurde geändert", "commands.weather.set.clear": "Wetter wurde auf Klar ge<PERSON>t", "commands.weather.set.rain": "Wetter wurde auf Regen geändert", "commands.weather.set.thunder": "<PERSON>ter wurde auf Gewitter geändert", "commands.whitelist.add.failed": "Spieler steht bereits auf der Gästeliste", "commands.whitelist.add.success": "%s wurde auf die Gästeliste gesetzt", "commands.whitelist.alreadyOff": "Die Gästeliste ist bereits deaktiviert", "commands.whitelist.alreadyOn": "Die Gästeliste ist bereits aktiviert", "commands.whitelist.disabled": "Die Gästeliste ist jetzt deaktiviert", "commands.whitelist.enabled": "Die Gästeliste ist jetzt aktiviert", "commands.whitelist.list": "Es steht/stehen %s Spieler auf der Gästeliste: %s", "commands.whitelist.none": "<PERSON><PERSON> stehen keine Spieler auf der Gästeliste", "commands.whitelist.reloaded": "Die Gästeliste wurde neu geladen", "commands.whitelist.remove.failed": "<PERSON>pieler steht nicht auf der Gästeliste", "commands.whitelist.remove.success": "%s wurde von der Gästeliste entfernt", "commands.worldborder.center.failed": "Nichts hat sich geändert, die Weltbarriere hat bereits diese Mitte", "commands.worldborder.center.success": "Die Mitte der Weltbarriere wurde auf (%s, %s) gesetzt", "commands.worldborder.damage.amount.failed": "Nichts hat sich ge<PERSON><PERSON><PERSON>, der von der Weltbarriere verursachte Schaden hat bereits diesen Wert", "commands.worldborder.damage.amount.success": "Der Schaden der Weltbarriere wurde auf %s pro Block jede Sekunde gesetzt", "commands.worldborder.damage.buffer.failed": "Nichts hat sich ge<PERSON><PERSON>t, der Unversehrtheitsbereich um die Weltbarriere hat bereits diese Größe", "commands.worldborder.damage.buffer.success": "Der Unversehrtheitsbereich um die Weltbarriere wurde auf %s Block/Blöcke gesetzt", "commands.worldborder.get": "Die Weltbarriere hat derzeit eine Weite von %s Block/Blöcken", "commands.worldborder.set.failed.big": "Die Weite der Weltbarriere darf nicht größer als %s <PERSON>l<PERSON><PERSON> sein", "commands.worldborder.set.failed.far": "Die Weltbarriere darf nicht weiter als %s Blöcke entfernt sein", "commands.worldborder.set.failed.nochange": "Nichts hat sich geändert, die Weltbarriere hat bereits diese Weite", "commands.worldborder.set.failed.small": "Die Weite der Weltbarriere darf nicht kleiner als 1 Block sein", "commands.worldborder.set.grow": "Die Weltbarriere wird innerhalb von %2$s Sekunden auf eine Weite von %1$s Blöcken vergrößert", "commands.worldborder.set.immediate": "Die Weltbarriere wurde auf eine Weite von %s Block/Blöcken gesetzt", "commands.worldborder.set.shrink": "Die Weltbarriere wird innerhalb von %2$s Sekunde(n) auf eine Weite von %1$s Block/Blöcke verkleinert", "commands.worldborder.warning.distance.failed": "Nichts hat sich ge<PERSON><PERSON>t, der Warnbereich um die Weltbarriere hat bereits diese Größe", "commands.worldborder.warning.distance.success": "Der Warnbereich um die Weltbarriere wurde auf %s Block/Blöcke gesetzt", "commands.worldborder.warning.time.failed": "Nichts hat sich ge<PERSON><PERSON>t, die Warnzeit der Weltbarriere hat bereits diesen Wert", "commands.worldborder.warning.time.success": "Die Warnzeit der Weltbarriere wurde auf %s Sekunde(n) gesetzt", "compliance.playtime.greaterThan24Hours": "Du spielst bereits seit mehr als 24 Stunden", "compliance.playtime.hours": "Du spielst bereits seit %s Stunde(n)", "compliance.playtime.message": "Übermäßiges Spielen kann den Alltag beeinträchtigen", "connect.aborted": "Abgebrochen", "connect.authorizing": "Anmelden …", "connect.connecting": "Verbindung mit dem Server wird hergestellt …", "connect.encrypting": "Verschlüsseln …", "connect.failed": "Verbindungsaufbau fehlgeschlagen", "connect.failed.transfer": "Verbindung bei Übertragung an den Server fehlgeschlagen", "connect.joining": "Welt wird betreten …", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON> …", "connect.reconfiging": "<PERSON>eu konfigurier<PERSON> …", "connect.reconfiguring": "<PERSON>eu konfigurier<PERSON> …", "connect.transferring": "Übertrage an neuen Server …", "container.barrel": "Fass", "container.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.beehive.bees": "Bienen: %s/%s", "container.beehive.honey": "Honig: %s/%s", "container.blast_furnace": "Schmelzofen", "container.brewing": "Braustand", "container.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "container.chest": "Truhe", "container.chestDouble": "Große Truhe", "container.crafter": "<PERSON><PERSON><PERSON>", "container.crafting": "Handwerk", "container.creative": "Gegenstände", "container.dispenser": "<PERSON><PERSON><PERSON>", "container.dropper": "<PERSON>pender", "container.enchant": "Verzaubern", "container.enchant.clue": "%s …?", "container.enchant.lapis.many": "%s Lapislazuli", "container.enchant.lapis.one": "1 Lapislazuli", "container.enchant.level.many": "%s Erfahrungsstufen", "container.enchant.level.one": "1 Erfahrungsstufe", "container.enchant.level.requirement": "Erforderliche Erfahrungsstufe: %s", "container.enderchest": "Endertruhe", "container.furnace": "<PERSON><PERSON>", "container.grindstone_title": "Reparieren/Entzaubern", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Inventar", "container.isLocked": "%s ist verschlossen!", "container.lectern": "<PERSON><PERSON><PERSON>", "container.loom": "Webstuhl", "container.repair": "Reparieren/Benennen", "container.repair.cost": "Erfahrungskosten: %1$s", "container.repair.expensive": "Zu teuer!", "container.shulkerBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%2$s× %1$s", "container.shulkerBox.more": "und %s weitere …", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Räucherofen", "container.spectatorCantOpen": "Kann nicht geöffnet werden. Inhalt wurde noch nicht generiert.", "container.stonecutter": "Steinsäge", "container.upgrade": "Ausrüstung aufwerten", "container.upgrade.error_tooltip": "Gegenstand kann so nicht aufgewertet werden", "container.upgrade.missing_template_tooltip": "Schmiedevorlage hinzufügen", "controls.keybinds": "Tastenbelegung …", "controls.keybinds.duplicateKeybinds": "Diese Taste wird auch verwendet für:\n%s", "controls.keybinds.title": "Tastenbelegung", "controls.reset": "Z<PERSON>ücksetzen", "controls.resetAll": "Tasten zurücksetzen", "controls.title": "Steuerung", "createWorld.customize.buffet.biome": "<PERSON>te wähle ein Biom", "createWorld.customize.buffet.title": "Einzelbiomanpassung", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Boden – %s", "createWorld.customize.flat.layer.top": "Oberfläche – %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> entfernen", "createWorld.customize.flat.tile": "Schichtmaterial", "createWorld.customize.flat.title": "Flachlandanpassung", "createWorld.customize.presets": "Voreinstellungen", "createWorld.customize.presets.list": "Alternativ sind hier einige, die wir zuvor erstellt haben!", "createWorld.customize.presets.select": "Voreinstellung benutzen", "createWorld.customize.presets.share": "<PERSON>öchtest du deine Vorlage mit jemandem teilen? Benutze das Eingabefeld!", "createWorld.customize.presets.title": "Voreinstellung auswählen", "createWorld.preparing": "Welterstellung wird vorbereitet …", "createWorld.tab.game.title": "Spiel", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "Welt", "credits_and_attribution.button.attribution": "Namensnennung", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "Mitwirkende und Namensnennung", "dataPack.bundle.description": "Aktiviert das experimentelle Bündel", "dataPack.bundle.name": "Bündel", "dataPack.locator_bar.description": "Zeigt im Mehrspielermodus die Richtung anderer Spieler an", "dataPack.locator_bar.name": "Ortungsleiste", "dataPack.minecart_improvements.description": "Verbesserte Bewegung von <PERSON>", "dataPack.minecart_improvements.name": "Lorenverbesserungen", "dataPack.redstone_experiments.description": "Experimentelle Redstone‐Änderungen", "dataPack.redstone_experiments.name": "Redstone‐Experimente", "dataPack.title": "Datenpakete auswählen", "dataPack.trade_rebalance.description": "Überarbeitete Angebote für Dorfbewohner", "dataPack.trade_rebalance.name": "Neugewichtung des Dorfbewohnerhandels", "dataPack.update_1_20.description": "Neue Spielelemente und Inhalte für Minecraft 1.20", "dataPack.update_1_20.name": "Aktualisierung 1.20", "dataPack.update_1_21.description": "Neue Spielelemente und Inhalte für Minecraft 1.21", "dataPack.update_1_21.name": "Aktualisierung 1.21", "dataPack.validation.back": "Zurück", "dataPack.validation.failed": "Überprüfung der Datenpakete fehlgeschlagen!", "dataPack.validation.reset": "Auf Standard zurücksetzen", "dataPack.validation.working": "Ausgewählte Datenpakete werden überprüft …", "dataPack.vanilla.description": "Die Standarddaten für Minecraft", "dataPack.vanilla.name": "Standard", "dataPack.winter_drop.description": "Neue Spielelemente und Inhalte für den Winter‐Drop", "dataPack.winter_drop.name": "Winter‐Drop", "datapackFailure.safeMode": "Abgesicherter Modus", "datapackFailure.safeMode.failed.description": "Diese Welt enthält ungültige oder beschädigte Speicherdaten.", "datapackFailure.safeMode.failed.title": "Laden der Welt im abgesicherten Modus fehlgeschlagen", "datapackFailure.title": "<PERSON>hler in den derzeit ausgewählten Datenpaketen haben das Laden der Welt verhindert.\nDu kannst entweder versuchen, sie nur mit dem Vanilla‐Datenpaket zu laden („abgesicherter Modus“), oder zum Hauptmenü zurückkehren und es manuell beheben.", "death.attack.anvil": "%1$s wurde von einem fallenden Amboss zerquetscht", "death.attack.anvil.player": "%1$s wurde im Kampf gegen %2$s von einem fallenden Amboss zerquetscht", "death.attack.arrow": "%1$s wurde von %2$s erschossen", "death.attack.arrow.item": "%1$s wurde von %2$s mit %3$s erschossen", "death.attack.badRespawnPoint.link": "beabsichtigtem Spieldesign", "death.attack.badRespawnPoint.message": "%1$s wurde von %2$s getötet", "death.attack.cactus": "%1$s wurde von einem Kaktus zu Tode gestochen", "death.attack.cactus.player": "%1$s lief be<PERSON>, %2$s zu entkommen, in einen Kaktus", "death.attack.cramming": "%1$s wurde zerquetscht", "death.attack.cramming.player": "%1$s wurde von %2$s zerquetscht", "death.attack.dragonBreath": "%1$s wurde in Drachenatem geröstet", "death.attack.dragonBreath.player": "%1$s wurde durch %2$s in Drachenatem geröstet", "death.attack.drown": "%1$s ertrank", "death.attack.drown.player": "%1$s ertrank be<PERSON>, %2$s zu entkommen", "death.attack.dryout": "%1$s starb an Wassermangel", "death.attack.dryout.player": "%1$s starb be<PERSON>, %2$s zu entkommen, an Wassermangel", "death.attack.even_more_magic": "%1$s wurde durch verstärkte Magie getötet", "death.attack.explosion": "%1$s wurde in die Luft gesprengt", "death.attack.explosion.player": "%1$s wurde von %2$s in die Luft gesprengt", "death.attack.explosion.player.item": "%1$s wurde von %2$s mit %3$s in die Luft gesprengt", "death.attack.fall": "%1$s fiel der Schwerkraft zum Opfer", "death.attack.fall.player": "%1$s fiel be<PERSON>, %2$s zu entkommen, der Schwerkraft zum Opfer", "death.attack.fallingBlock": "%1$s wurde von einem fallenden Block zerquetscht", "death.attack.fallingBlock.player": "%1$s wurde im Kampf gegen %2$s von einem fallenden Block zerquetscht", "death.attack.fallingStalactite": "%1$s wurde von einem fallenden Stalaktiten aufgespießt", "death.attack.fallingStalactite.player": "%1$s wurde im Kampf gegen %2$s von einem fallenden Stalaktiten aufgespießt", "death.attack.fireball": "%1$s wurde von %2$s flambiert", "death.attack.fireball.item": "%1$s wurde von %2$s mit %3$s flambiert", "death.attack.fireworks": "%1$s flog mit einem Knall in die Luft", "death.attack.fireworks.item": "%1$s flog aufgrund eines Feuerwerks, das von %2$s mit %3$s abgeschossen wurde, mit einem Knall in die Luft", "death.attack.fireworks.player": "%1$s flog im Kampf gegen %2$s mit einem Knall in die Luft", "death.attack.flyIntoWall": "%1$s erfuhr kinetische Energie", "death.attack.flyIntoWall.player": "%1$s erfuhr beim V<PERSON>, %2$s zu entkommen, kinetische Energie", "death.attack.freeze": "%1$s erfror", "death.attack.freeze.player": "%1$s erfror durch %2$s", "death.attack.generic": "%1$s starb", "death.attack.generic.player": "%1$s starb wegen %2$s", "death.attack.genericKill": "%1$s wurde getötet", "death.attack.genericKill.player": "%1$s wurde im Kampf gegen %2$s getötet", "death.attack.hotFloor": "%1$s wurde der Boden zu heiß", "death.attack.hotFloor.player": "%1$s geriet wegen %2$s in den Gefahrenbereich", "death.attack.inFire": "%1$s ging in Flammen auf", "death.attack.inFire.player": "%1$s lief im Kampf gegen %2$s ins Feuer", "death.attack.inWall": "%1$s wurde lebendig begraben", "death.attack.inWall.player": "%1$s wurde im Kampf gegen %2$s lebendig begraben", "death.attack.indirectMagic": "%1$s wurde von %2$s mit Magie getötet", "death.attack.indirectMagic.item": "%1$s wurde von %2$s mit %3$s getötet", "death.attack.lava": "%1$s versuchte, in <PERSON><PERSON> zu schwimmen", "death.attack.lava.player": "%1$s versuchte, in Lava zu schwimmen, um %2$s zu entkommen", "death.attack.lightningBolt": "%1$s wurde vom Blitz getroffen", "death.attack.lightningBolt.player": "%1$s wurde im Kampf gegen %2$s vom Blitz getroffen", "death.attack.mace_smash": "%1$s wurde von %2$s zersch<PERSON>ert", "death.attack.mace_smash.item": "%1$s wurde von %2$s mit %3$s zersch<PERSON>ert", "death.attack.magic": "%1$s wurde durch Magie getötet", "death.attack.magic.player": "%1$s wurde be<PERSON>, %2$s zu entkommen, durch Magie getötet", "death.attack.message_too_long": "Die eigentliche Meldung ist leider zu lang, um angezeigt werden zu können. Hier ist eine verkürzte Fassung: %s", "death.attack.mob": "%1$s wurde von %2$s erschlagen", "death.attack.mob.item": "%1$s wurde von %2$s mit %3$s erschlagen", "death.attack.onFire": "%1$s verbrannte", "death.attack.onFire.item": "%1$s wurde im Kampf gegen %2$s – %3$s führend – geröstet", "death.attack.onFire.player": "%1$s wurde im Kampf gegen %2$s geröstet", "death.attack.outOfWorld": "%1$s fiel aus der Welt", "death.attack.outOfWorld.player": "%1$s wollte nicht mehr in derselben Welt wie %2$s leben", "death.attack.outsideBorder": "%1$s verließ die Grenzen dieser Welt", "death.attack.outsideBorder.player": "%1$s verließ im Kampf gegen %2$s die Grenzen dieser Welt", "death.attack.player": "%1$s wurde von %2$s erschlagen", "death.attack.player.item": "%1$s wurde von %2$s mit %3$s erschlagen", "death.attack.sonic_boom": "%1$s wurde durch schallgeladenes Kreischen ausgelöscht", "death.attack.sonic_boom.item": "%1$s wurde be<PERSON>, %2$s – %3$s führend – zu entkommen, durch schallgeladenes Kreischen ausgelöscht", "death.attack.sonic_boom.player": "%1$s wurde be<PERSON>, %2$s zu entkommen, durch schallgeladenes Kreischen ausgelöscht", "death.attack.stalagmite": "%1$s wurde von einem Stalagmiten aufgespießt", "death.attack.stalagmite.player": "%1$s wurde im Kampf gegen %2$s von einem Stalagmiten aufgespießt", "death.attack.starve": "%1$s verhungerte", "death.attack.starve.player": "%1$s verhungerte im Kampf gegen %2$s", "death.attack.sting": "%1$s wurde zu Tode gestochen", "death.attack.sting.item": "%1$s wurde von %2$s mit %3$s zu Tode gestochen", "death.attack.sting.player": "%1$s wurde von %2$s zu Tode gestochen", "death.attack.sweetBerryBush": "%1$s stach sich an einem Süßbeerstrauch zu Tode", "death.attack.sweetBerryBush.player": "%1$s stach sich beim Versuch, %2$s zu entkommen, an einem Süßbeerstrauch zu Tode", "death.attack.thorns": "%1$s starb be<PERSON>, %2$s zu verletzen", "death.attack.thorns.item": "%1$s starb be<PERSON>, %2$s zu verletzen, durch %3$s", "death.attack.thrown": "%1$s wurde von %2$s zu Tode geprügelt", "death.attack.thrown.item": "%1$s wurde von %2$s mit %3$s zu Tode geprügelt", "death.attack.trident": "%1$s wurde von %2$s aufgespießt", "death.attack.trident.item": "%1$s wurde von %2$s mit %3$s aufgespießt", "death.attack.wither": "%1$s verdorrte", "death.attack.wither.player": "%1$s verdorrte im Kampf gegen %2$s", "death.attack.witherSkull": "%1$s wurde von %2$s mit einem Schädel erschossen", "death.attack.witherSkull.item": "%1$s wurde von %2$s mit %3$s mit einem Schädel erschossen", "death.fell.accident.generic": "%1$s fiel aus zu großer Höhe", "death.fell.accident.ladder": "%1$s stürz<PERSON> von einer Leiter ab", "death.fell.accident.other_climbable": "%1$s stürzte beim <PERSON> ab", "death.fell.accident.scaffolding": "%1$s stürz<PERSON> von einem Gerüst ab", "death.fell.accident.twisting_vines": "%1$s stü<PERSON><PERSON> von Zwirbelranken ab", "death.fell.accident.vines": "%1$s st<PERSON><PERSON><PERSON> von <PERSON> ab", "death.fell.accident.weeping_vines": "%1$s st<PERSON><PERSON><PERSON> von Trauerranken ab", "death.fell.assist": "%1$s wurde von %2$s zum Absturz verdammt", "death.fell.assist.item": "%1$s wurde von %2$s mit %3$s zum Absturz verdammt", "death.fell.finish": "%1$s fiel zu tief und wurde von %2$s erledigt", "death.fell.finish.item": "%1$s fiel zu tief und wurde von %2$s mit %3$s erledigt", "death.fell.killer": "%1$s wurde zum Absturz verdammt", "deathScreen.quit.confirm": "B<PERSON> du sicher, dass du das Spiel verlassen möchtest?", "deathScreen.respawn": "Wiederbeleben", "deathScreen.score": "Punktestand", "deathScreen.score.value": "Punktestand: %s", "deathScreen.spectate": "Welt beobachten", "deathScreen.title": "Du bist gestorben!", "deathScreen.title.hardcore": "Game over!", "deathScreen.titleScreen": "Hauptmenü", "debug.advanced_tooltips.help": "F3 + H = <PERSON><PERSON><PERSON><PERSON><PERSON>ellinfo<PERSON>", "debug.advanced_tooltips.off": "Erweiterte Schnellinfos: unsichtbar", "debug.advanced_tooltips.on": "Erweiterte Schnellinfos: sichtbar", "debug.chunk_boundaries.help": "F3 + G = Chunk‐Grenzen anzeigen", "debug.chunk_boundaries.off": "Chunk‐Grenzen: unsichtbar", "debug.chunk_boundaries.on": "Chunk‐Grenzen: sichtbar", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON> leeren", "debug.copy_location.help": "F3 + C = Position als /tp-Befehl kopieren oder gedrückt halten, um einen Absturz zu erzwingen", "debug.copy_location.message": "Position wurde in die Zwischenablage kopiert", "debug.crash.message": "F3 + C sind gedrückt. Dies wird das Spiel abstürzen lassen, wenn sie nicht losgelassen werden.", "debug.crash.warning": "Absturz in %s …", "debug.creative_spectator.error": "Spielmodus kann nicht geändert werden; keine Berechtigung", "debug.creative_spectator.help": "F3 + N = Zwischen vorherigem Spielmodus und Zuschauermodus wechseln", "debug.dump_dynamic_textures": "Dynamische Texturen in %s gespeichert", "debug.dump_dynamic_textures.help": "F3 + S = Dynamische Texturen ausgeben", "debug.gamemodes.error": "Spielmodusauswahl kann wegen fehlender Berechtigung nicht geöffnet werden", "debug.gamemodes.help": "F3 + F4 = Spielmodusauswahl öffnen", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Nächster", "debug.help.help": "F3 + Q = Diese Liste anzeigen", "debug.help.message": "Tastenbelegung:", "debug.inspect.client.block": "Clientseitige Blockdaten in die Zwischenablage kopiert", "debug.inspect.client.entity": "Clientseitige Objektdaten in die Zwischenablage kopiert", "debug.inspect.help": "F3 + I = Block- oder Objektdaten in Zwischenablage kopieren", "debug.inspect.server.block": "Serverseitige Blockdaten in Zwischenablage kopiert", "debug.inspect.server.entity": "Serverseitige Objektdaten in Zwischenablage kopiert", "debug.pause.help": "F3 + Esc = Spiel ohne Pausenmenü anhalten (sofern möglich)", "debug.pause_focus.help": "F3 + P = Pausieren bei Fokusverlust", "debug.pause_focus.off": "Pausieren bei Fokusverlust: deaktiviert", "debug.pause_focus.on": "Pausieren bei Fokusverlust: aktiviert", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Profilerstellung starten/beenden", "debug.profiling.start": "Debug‐Aufzeichnung wurde für %s Sekunden gestartet. Benutze F3 + L, um sie vorzeitig zu beenden", "debug.profiling.stop": "Profilerstellung beendet. Ergebnisse wurden in %s gespeichert.", "debug.reload_chunks.help": "F3 + A = Chunks neu laden", "debug.reload_chunks.message": "Alle Chunks werden neu geladen", "debug.reload_resourcepacks.help": "F3 + T = Ressourcenpakete neu laden", "debug.reload_resourcepacks.message": "Ressourcenpakete neu geladen", "debug.show_hitboxes.help": "F3 + B = Hitboxen anzeigen", "debug.show_hitboxes.off": "Hitboxen: unsichtbar", "debug.show_hitboxes.on": "Hitboxen: sichtbar", "debug.version.header": "Clientseitige Versionsinformationen:", "debug.version.help": "F3 + V = Clientseitige Versionsinformationen ausgeben", "demo.day.1": "Diese Demo beinhaltet fünf Spieltage. Gib dein Bestes!", "demo.day.2": "Zweiter Tag", "demo.day.3": "Dritter Tag", "demo.day.4": "Vierter Tag", "demo.day.5": "Fünfter und letzter Tag!", "demo.day.6": "Dein fünfter Spieltag ist zu Ende. Drücke %s, um einen Screenshot deiner Schöpfung abzuspeichern.", "demo.day.warning": "<PERSON><PERSON> Z<PERSON> ist schon fast um!", "demo.demoExpired": "Die Demozeit ist abgelaufen!", "demo.help.buy": "Jetzt kaufen!", "demo.help.fullWrapped": "Diese Demo dauert 5 Spieltage (ca. 1 Stunde und 40 Minuten echte Zeit). Sieh dir die Fortschritte für Hinweise an! Viel Spaß!", "demo.help.inventory": "Mit %1$s öffnest du dein Inventar", "demo.help.jump": "Drücke %1$s, um zu springen", "demo.help.later": "Weiterspielen!", "demo.help.movement": "Benutze %1$s, %2$s, %3$s, %4$s und die Maus, um dich zu bewegen", "demo.help.movementMouse": "<PERSON><PERSON> dich mithilfe der Maus um", "demo.help.movementShort": "Bewege dich mit %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft‐Demo‐Modus", "demo.remainingTime": "Verbleibende Zeit: %s", "demo.reminder": "Die Demozeit ist abgelaufen. <PERSON><PERSON><PERSON> das Spiel, um fortzufahren, oder erstelle eine neue Welt!", "difficulty.lock.question": "Bist du sicher, dass du die Schwierigkeit dieser Welt sperren möchtest? Dies wird die Welt für immer auf %1$s setzen und du wirst diese Einstellung nicht mehr ändern können.", "difficulty.lock.title": "Schwierigkeit dieser Welt sperren", "disconnect.endOfStream": "Ende der Datenübertragung", "disconnect.exceeded_packet_rate": "Wegen Überschreitung des Paketratenlimits hinausgeworfen", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Statusanfrage wurde nicht beantwortet", "disconnect.loginFailedInfo": "Anmeldung fehlgeschlagen: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Der Mehrspielermodus ist deaktiviert. Bitte überprüfe die Einstellungen deines Microsoft‐Kontos.", "disconnect.loginFailedInfo.invalidSession": "Ungültige Sitzung (Versuche, das Spiel und ggf. den Launcher neu zu starten)", "disconnect.loginFailedInfo.serversUnavailable": "Die Authentifizierungsserver sind derzeit nicht erreichbar. Bitte versuche es erneut.", "disconnect.loginFailedInfo.userBanned": "Du bist vom Online‐Spielen ausgeschlossen", "disconnect.lost": "Verbindung abgebrochen", "disconnect.packetError": "Fehler im Netzwerkprotokoll", "disconnect.spam": "Wegen Spamming hinausgeworfen", "disconnect.timeout": "Zeitüberschreitung", "disconnect.transfer": "An einen anderen Server übertragen", "disconnect.unknownHost": "Unbekannter Server", "download.pack.failed": "%s von %s <PERSON><PERSON>(en) konnte(n) nicht heruntergeladen werden", "download.pack.progress.bytes": "Fortschritt: %s (Gesamtgröße unbekannt)", "download.pack.progress.percent": "Fortschritt: %s %%", "download.pack.title": "Ressourcenpaket %s/%s wird heruntergeladen", "editGamerule.default": "Standard: %s", "editGamerule.title": "Spielregeln bearbeiten", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorption", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.blindness": "Blindheit", "effect.minecraft.conduit_power": "Meereskraft", "effect.minecraft.darkness": "Dunkelheit", "effect.minecraft.dolphins_grace": "Gunst des Delfins", "effect.minecraft.fire_resistance": "Feuerresistenz", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Extraenergie", "effect.minecraft.hero_of_the_village": "Held des Dorfes", "effect.minecraft.hunger": "Hunger", "effect.minecraft.infested": "Befallen", "effect.minecraft.instant_damage": "Direktschaden", "effect.minecraft.instant_health": "Direktheilung", "effect.minecraft.invisibility": "Unsichtbarkeit", "effect.minecraft.jump_boost": "Sprungkraft", "effect.minecraft.levitation": "Schwebekraft", "effect.minecraft.luck": "Glück", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Übelkeit", "effect.minecraft.night_vision": "<PERSON>chtsicht", "effect.minecraft.oozing": "Schleimen", "effect.minecraft.poison": "Vergiftung", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Regeneration", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Sättigung", "effect.minecraft.slow_falling": "Sanfter Fall", "effect.minecraft.slowness": "Langsamkeit", "effect.minecraft.speed": "Schnelligkeit", "effect.minecraft.strength": "Stärke", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "Pech", "effect.minecraft.water_breathing": "Unterwasseratmung", "effect.minecraft.weakness": "Schwäche", "effect.minecraft.weaving": "<PERSON>en", "effect.minecraft.wind_charged": "Windgeladen", "effect.minecraft.wither": "Verdorrung", "effect.none": "Wirkungslos", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Wasseraffinität", "enchantment.minecraft.bane_of_arthropods": "Nemesis der Gliederfüßer", "enchantment.minecraft.binding_curse": "Fluch der Bindung", "enchantment.minecraft.blast_protection": "Explosionsschutz", "enchantment.minecraft.breach": "Durchbruch", "enchantment.minecraft.channeling": "Entladung", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Effizienz", "enchantment.minecraft.feather_falling": "Federfall", "enchantment.minecraft.fire_aspect": "Verbrennung", "enchantment.minecraft.fire_protection": "Feuer<PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON>lamme", "enchantment.minecraft.fortune": "Glück", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Unendlichkeit", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Plünderung", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Glück des Meeres", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Reparatur", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Stärke", "enchantment.minecraft.projectile_protection": "Sc<PERSON>ss<PERSON>tz", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Schlag", "enchantment.minecraft.quick_charge": "Schnellladen", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Sog", "enchantment.minecraft.sharpness": "Schärfe", "enchantment.minecraft.silk_touch": "Behutsamkeit", "enchantment.minecraft.smite": "Bann", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "Schwungkraft", "enchantment.minecraft.sweeping_edge": "Schwungkraft", "enchantment.minecraft.swift_sneak": "Huschen", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Haltbarkeit", "enchantment.minecraft.vanishing_curse": "Fluch des Verschwindens", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akazienholzboot", "entity.minecraft.acacia_chest_boat": "Akazienholztruhenboot", "entity.minecraft.allay": "Hilfsgeist", "entity.minecraft.area_effect_cloud": "Partikelwolke", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "Rüstungsständer", "entity.minecraft.arrow": "Pfeil", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambus-Truhenfloß", "entity.minecraft.bamboo_raft": "Bambusfloß", "entity.minecraft.bat": "<PERSON><PERSON>er<PERSON><PERSON>", "entity.minecraft.bee": "Biene", "entity.minecraft.birch_boat": "Birkenholzboot", "entity.minecraft.birch_chest_boat": "Birkenholztruhenboot", "entity.minecraft.blaze": "Lohe", "entity.minecraft.block_display": "Blockdarsteller", "entity.minecraft.boat": "Boot", "entity.minecraft.bogged": "Sumpfskelett", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Windkugel", "entity.minecraft.camel": "Dr<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON><PERSON>pinne", "entity.minecraft.cherry_boat": "<PERSON>rsch<PERSON>zboot", "entity.minecraft.cherry_chest_boat": "Kirschholztruhenboot", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.chest_minecart": "Güterlore", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Befehlsblocklore", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Knarz", "entity.minecraft.creaking_transient": "Knarz", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Schwarzeichenholzboot", "entity.minecraft.dark_oak_chest_boat": "Schwarzeichenholztruhenboot", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Drachen-Feuerkugel", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Geworfenes Ei", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "Geworfene Enderperle", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Magier", "entity.minecraft.evoker_fangs": "Fangzähne", "entity.minecraft.experience_bottle": "Geworfenes Erfahrungsfläschchen", "entity.minecraft.experience_orb": "Erfahrungskugel", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Fallender Block", "entity.minecraft.falling_block_type": "Fallender %s", "entity.minecraft.fireball": "Feuerkugel", "entity.minecraft.firework_rocket": "Feuerwerksrakete", "entity.minecraft.fishing_bobber": "Schwimmer", "entity.minecraft.fox": "Fuchs", "entity.minecraft.frog": "<PERSON><PERSON><PERSON>", "entity.minecraft.furnace_minecart": "Antrieb<PERSON><PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "Le<PERSON>tti<PERSON>nfisch", "entity.minecraft.goat": "Ziege", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "Glücklicher Ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Trichterlore", "entity.minecraft.horse": "<PERSON><PERSON>d", "entity.minecraft.husk": "Wüstenzombie", "entity.minecraft.illusioner": "Illusionist", "entity.minecraft.interaction": "Interaktionsobjekt", "entity.minecraft.iron_golem": "Eisengolem", "entity.minecraft.item": "Gegenstand", "entity.minecraft.item_display": "Gegenstandsdarsteller", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Tropenholzboot", "entity.minecraft.jungle_chest_boat": "Tropenholztruhenboot", "entity.minecraft.killer_bunny": "<PERSON>-<PERSON><PERSON>", "entity.minecraft.leash_knot": "Leinenknoten", "entity.minecraft.lightning_bolt": "Blitz", "entity.minecraft.lingering_potion": "Verweiltrank", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Lamasp<PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmawürfel", "entity.minecraft.mangrove_boat": "Mangrovenholzboot", "entity.minecraft.mangrove_chest_boat": "Mangrovenholztruhenboot", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Lore", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Eichenholzboot", "entity.minecraft.oak_chest_boat": "Eichenholztruhenboot", "entity.minecraft.ocelot": "Ozelot", "entity.minecraft.ominous_item_spawner": "Unheilvoller Gegenstands-Spawner", "entity.minecraft.painting": "G<PERSON>ä<PERSON>", "entity.minecraft.pale_oak_boat": "Blasseichenholzboot", "entity.minecraft.pale_oak_chest_boat": "Blasseichenholztruhenboot", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin-Barbar", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON>rank", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Verwüster", "entity.minecraft.salmon": "Lachs", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>er-Geschoss", "entity.minecraft.silverfish": "Silberfischchen", "entity.minecraft.skeleton": "Skelett", "entity.minecraft.skeleton_horse": "Skelettpferd", "entity.minecraft.slime": "Schleim", "entity.minecraft.small_fireball": "<PERSON><PERSON> Feuerkugel", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Schneegolem", "entity.minecraft.snowball": "Schneeball", "entity.minecraft.spawner_minecart": "Spawner-Lore", "entity.minecraft.spectral_arrow": "Spektralpfeil", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Wurftrank", "entity.minecraft.spruce_boat": "Fichtenholzboot", "entity.minecraft.spruce_chest_boat": "Fichtenholztruhenboot", "entity.minecraft.squid": "Tintenfisch", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Textdarsteller", "entity.minecraft.tnt": "Gezündetes TNT", "entity.minecraft.tnt_minecart": "TNT-Lore", "entity.minecraft.trader_llama": "Händlerlama", "entity.minecraft.trident": "Dreizack", "entity.minecraft.tropical_fish": "Tropenfisch", "entity.minecraft.tropical_fish.predefined.0": "Anemonenfisch", "entity.minecraft.tropical_fish.predefined.1": "Schwarzer Segelflossendoktor", "entity.minecraft.tropical_fish.predefined.10": "Halfterfisch", "entity.minecraft.tropical_fish.predefined.11": "Orangestreifen-Falterfisch", "entity.minecraft.tropical_fish.predefined.12": "Papageifisch", "entity.minecraft.tropical_fish.predefined.13": "Diadem-Prachtkaiserfisch", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Rotlippen-Schleimfisch", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>flossend<PERSON>", "entity.minecraft.tropical_fish.predefined.20": "Gelbschwanz-Papageifisch", "entity.minecraft.tropical_fish.predefined.21": "Gelber Segelflossendoktor", "entity.minecraft.tropical_fish.predefined.3": "Falterfisch", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Zuckerwatte-Kampffisch", "entity.minecraft.tropical_fish.predefined.7": "Zwergbarsch", "entity.minecraft.tropical_fish.predefined.8": "Kaiser-Sc<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Meerbarbe", "entity.minecraft.tropical_fish.type.betty": "Peitschenfisch", "entity.minecraft.tropical_fish.type.blockfish": "Blockfisch", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "Flitzer", "entity.minecraft.tropical_fish.type.flopper": "Zap<PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "Trommlerfisch", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sonnenstrahler", "entity.minecraft.turtle": "Schildkröte", "entity.minecraft.vex": "Plagegeist", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "Panzermacher", "entity.minecraft.villager.butcher": "<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON>", "entity.minecraft.villager.fisherman": "<PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliothekar", "entity.minecraft.villager.mason": "Maurer", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Waffenschmied", "entity.minecraft.vindicator": "<PERSON><PERSON>", "entity.minecraft.wandering_trader": "Fahrender Händler", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Windkugel", "entity.minecraft.witch": "Hexe", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Zombie<PERSON><PERSON>wohn<PERSON>", "entity.minecraft.zombified_piglin": "Zombifizierter <PERSON>", "entity.not_summonable": "Objekt des Typs %s kann nicht erzeugt werden", "event.minecraft.raid": "Überfall", "event.minecraft.raid.defeat": "Niederlage", "event.minecraft.raid.defeat.full": "Überfall – Niederlage", "event.minecraft.raid.raiders_remaining": "Verbleibende Räuber: %s", "event.minecraft.raid.victory": "Sieg", "event.minecraft.raid.victory.full": "Überfall – Sieg", "filled_map.buried_treasure": "Schatzkarte", "filled_map.explorer_jungle": "Dschungel-Entdeckerkarte", "filled_map.explorer_swamp": "Sumpf-Entdeckerkarte", "filled_map.id": "Nr. %s", "filled_map.level": "(Stufe %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.mansion": "Wald-Entdeckerkarte", "filled_map.monument": "Ozean-Ent<PERSON>cker<PERSON><PERSON>", "filled_map.scale": "Maßstab 1:%s", "filled_map.trial_chambers": "Prüfungs-Entdeckerkarte", "filled_map.unknown": "Unbekannte Karte", "filled_map.village_desert": "Wüstendorfkarte", "filled_map.village_plains": "Ebenendorfkarte", "filled_map.village_savanna": "Savannendorfkarte", "filled_map.village_snowy": "Schneedorfkarte", "filled_map.village_taiga": "Taigadorfkar<PERSON>", "flat_world_preset.minecraft.bottomless_pit": "Bodenloser Abgrund", "flat_world_preset.minecraft.classic_flat": "Klassisches Flachland", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Oberwelt", "flat_world_preset.minecraft.redstone_ready": "Redstone, fertig, los", "flat_world_preset.minecraft.snowy_kingdom": "Schneekönigreich", "flat_world_preset.minecraft.the_void": "<PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Traum des Bergarbeiters", "flat_world_preset.minecraft.water_world": "Wasserwelt", "flat_world_preset.unknown": "???", "gameMode.adventure": "Abenteuermodus", "gameMode.changed": "De<PERSON>lm<PERSON> wurde zu %s geändert", "gameMode.creative": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "gameMode.hardcore": "Hardcore‐Modus", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "Überlebensmodus", "gamerule.allowFireTicksAwayFromPlayer": "Von <PERSON>n entferntes Feuer aktualisieren", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON>, o<PERSON> <PERSON><PERSON> und Lava in einer Entfernung von mehr als 8 <PERSON><PERSON> von einem Spieler aktualisiert werden", "gamerule.announceAdvancements": "Fortschritte bekannt geben", "gamerule.blockExplosionDropDecay": "Block‐Explosionsausbeute verschwindet", "gamerule.blockExplosionDropDecay.description": "Einiges von der Ausbeute von <PERSON>, die durch Explosionen aufgrund von Blockinteraktionen zerstört werden, geht dabei verloren.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Ausbeute", "gamerule.category.misc": "Verschiedenes", "gamerule.category.mobs": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Erzeugung", "gamerule.category.updates": "Weltaktualisierungen", "gamerule.commandBlockOutput": "Befehlsblockausgabe anzeigen", "gamerule.commandModificationBlockLimit": "Blockgrenze für Befehlsänderungen", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON> der Blöcke, die mit einem Befehl wie „fill“ oder „clone“ gleichzeitig geändert werden können.", "gamerule.disableElytraMovementCheck": "Elytrenflug nicht überprüfen", "gamerule.disablePlayerMovementCheck": "Spielerbewegung nicht überprüfen", "gamerule.disableRaids": "Überfälle <PERSON>akti<PERSON>", "gamerule.doDaylightCycle": "Voranschreiten der Tageszeit", "gamerule.doEntityDrops": "Ob<PERSON><PERSON><PERSON><PERSON><PERSON> fallen lassen", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> usw. ihre Ausbeute fallen lassen (einschließlich Inventaren).", "gamerule.doFireTick": "Feuerausbreitung", "gamerule.doImmediateRespawn": "Sofortiger Wiedereinstieg", "gamerule.doInsomnia": "<PERSON><PERSON> erzeugen", "gamerule.doLimitedCrafting": "Rezepte zur Herstellung benötigt", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON> a<PERSON>, können Spieler nur bereits freigeschaltete Rezepte nutzen.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fallen lassen", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON>, ob Kreaturen ihre Ausbeute fallen lassen (einschließlich Erfahrungskugeln).", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "gamerule.doMobSpawning.description": "<PERSON><PERSON>r einige Objekte können eigene Regeln gelten.", "gamerule.doPatrolSpawning": "Plünderer‐Patrouillen erzeugen", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON><PERSON> fallen lassen", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON>, ob <PERSON><PERSON><PERSON><PERSON> ihre Ausbeute fallen lassen (einschließlich Erfahrungskugeln).", "gamerule.doTraderSpawning": "Fahrende Händler erzeugen", "gamerule.doVinesSpread": "Rankenausbreitung", "gamerule.doVinesSpread.description": "<PERSON><PERSON><PERSON>, ob sich Ranken zufällig auf benachbarte Blöcke ausbreiten können. Wirkt sich nicht auf andere Rankenarten wie Trauerranken, Zwirbelranken usw. aus.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON> erzeugen", "gamerule.doWeatherCycle": "Wetterwechsel", "gamerule.drowningDamage": "Schaden durch Ertrinken", "gamerule.enderPearlsVanishOnDeath": "Enderperlen‐Mitleidenschaft", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON>, ob vom Spieler geworfene Enderperlen verschwinden, wenn dieser stirbt.", "gamerule.entitiesWithPassengersCanUsePortals": "Portalnutzung durch Objekte mit Passagieren", "gamerule.entitiesWithPassengersCanUsePortals.description": "Ermöglicht es Objekten mit Passagieren, sich durch Nether‐, End‐ und Endtransitportale zu teleportieren.", "gamerule.fallDamage": "Fallschaden", "gamerule.fireDamage": "Feuerschaden", "gamerule.forgiveDeadPlayers": "Gestorbenen Spielern vergeben", "gamerule.forgiveDeadPlayers.description": "Verärgerte neutrale Kreaturen hören auf, wü<PERSON> zu sein, wenn der Zielspieler in der Nähe stirbt.", "gamerule.freezeDamage": "Erfrierungsschaden", "gamerule.globalSoundEvents": "Weltweite Geräuschereignisse", "gamerule.globalSoundEvents.description": "Wenn bestimmte Spielereignisse eintreten, z. B. das Auftauchen eines Endgegners, ist das Geräusch überall zu hören.", "gamerule.keepInventory": "Inventar nach dem Tod behalten", "gamerule.lavaSourceConversion": "Lava‐Erneuerbarkeit", "gamerule.lavaSourceConversion.description": "Wenn fließende Lava auf zwei Seiten von Lavaquellen umgeben ist, wird sie zu einer Quelle.", "gamerule.locatorBar": "Spielerortungsleiste aktivieren", "gamerule.locatorBar.description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, zeigt eine Leiste auf dem Bildschirm die Richtung der Mitspieler an.", "gamerule.logAdminCommands": "Administratorbefehle protokollieren", "gamerule.maxCommandChainLength": "Obergrenze für Befehlsketten", "gamerule.maxCommandChainLength.description": "Wird auf Befehlsblockketten und Funktionen angewandt.", "gamerule.maxCommandForkCount": "Obergrenze für Befehlsrahmen", "gamerule.maxCommandForkCount.description": "Maximale Anzahl an Befehlsrahmen, die von Befehlen wie „execute as“ benutzt werden können.", "gamerule.maxEntityCramming": "Obergrenze für Objektgedränge", "gamerule.minecartMaxSpeed": "Loren‐Höchstgeschwindigkeit", "gamerule.minecartMaxSpeed.description": "Höchstgeschwindigkeit, die eine Lore an Land standardmäßig erreichen kann.", "gamerule.mobExplosionDropDecay": "Kreatur‐Explosionsausbeute verschwindet", "gamerule.mobExplosionDropDecay.description": "Einige<PERSON> von der Ausbeute von <PERSON>, die durch von Kreaturen verursachte Explosionen zerstört werden, geht dabei verloren.", "gamerule.mobGriefing": "Zerstörung durch Kreaturen", "gamerule.naturalRegeneration": "Gesundheit regenerieren", "gamerule.playersNetherPortalCreativeDelay": "Netherportal‐Verzögerung im Kreativmodus", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (in Ticks), die ein Spieler im Kreativmodus in einem Netherportal verbringen muss, bevor dieser die Dimension wechselt.", "gamerule.playersNetherPortalDefaultDelay": "Netherportal‐Verzögerung", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (in Ticks), die ein Spieler außerhalb des Kreativmodus in einem Netherportal verbringen muss, bevor dieser die Dimension wechselt.", "gamerule.playersSleepingPercentage": "Schlafquote", "gamerule.playersSleepingPercentage.description": "Der Prozentsatz an Spielern, die im Bett liegen müssen, um die Nacht durchzuschlafen.", "gamerule.projectilesCanBreakBlocks": "Blockzerstörung durch Geschosse", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON>, ob aufprallende Geschosse Blöcke zerstören, die durch sie zerstörbar wären.", "gamerule.randomTickSpeed": "Häufigkeit von Zufallsereignissen", "gamerule.reducedDebugInfo": "Debug‐Infos reduzieren", "gamerule.reducedDebugInfo.description": "Begrenzt die Inhalte der Debug‐Anzeige.", "gamerule.sendCommandFeedback": "Befehlsausgabe anzeigen", "gamerule.showDeathMessages": "Todesmeldungen anzeigen", "gamerule.snowAccumulationHeight": "Schneedeckenhöhe", "gamerule.snowAccumulationHeight.description": "<PERSON>n es schneit, entsteht auf dem Boden eine Schneedecke, die höchstens diese Anzahl von <PERSON> erreicht.", "gamerule.spawnChunkRadius": "Einstiegs‐Chunk‐Radius", "gamerule.spawnChunkRadius.description": "Anzahl der Chunks um den Einstiegspunkt in der Oberwelt, die dauerhaft geladen bleiben.", "gamerule.spawnRadius": "<PERSON><PERSON> des Wiedereinstiegsbereichs", "gamerule.spawnRadius.description": "Steuert die Größe des Bereichs um den Einstiegspunkt, in dem Spieler in die Welt einsteigen können.", "gamerule.spectatorsGenerateChunks": "Zuschauer generieren Landschaft", "gamerule.tntExplodes": "Entzünden und Explodieren von TNT", "gamerule.tntExplosionDropDecay": "TNT‐Explosionsausbeute verschwindet", "gamerule.tntExplosionDropDecay.description": "Einiges von der Ausbeute von <PERSON>, die durch TNT‐Explosionen zerstört werden, geht dabei verloren.", "gamerule.universalAnger": "Allgemeine Verärgerung", "gamerule.universalAnger.description": "Verärgerte neutrale Kreaturen greifen jeden Spieler in der Nähe an – nicht nur den Spieler, der sie verärgert hat. Funktioniert am besten, wenn forgiveDeadPlayers deaktiviert ist.", "gamerule.waterSourceConversion": "Wasser‐Erneuerbarkeit", "gamerule.waterSourceConversion.description": "<PERSON>n fließendes Wasser auf zwei Seiten von Wasserquellen umgeben ist, wird es zu einer Quelle.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.customized": "Angepasst (alt)", "generator.minecraft.amplified": "ZERKLÜFTET", "generator.minecraft.amplified.info": "Hinweis: <PERSON>ur zum Spaß! Erfordert einen starken Computer.", "generator.minecraft.debug_all_block_states": "Debug‐Modus", "generator.minecraft.flat": "Flachland", "generator.minecraft.large_biomes": "Große Biome", "generator.minecraft.normal": "Standard", "generator.minecraft.single_biome_surface": "Einzelnes Biom", "generator.single_biome_caves": "<PERSON><PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Schwebende Inseln", "gui.abuseReport.attestation": "Mit der Einreichung dieser Meldung versicherst du, dass deine Angaben wahrheitsgetreu und vollständig sind – nach bestem Wissen.", "gui.abuseReport.comments": "Anmerkungen", "gui.abuseReport.describe": "<PERSON> Angabe von Einzelheiten hilft uns dabei, eine fundierte Entscheidung zu treffen.", "gui.abuseReport.discard.content": "Wenn du die Seite verlässt, gehen diese Meldung und deine Anmerkungen verloren.\n<PERSON><PERSON> du sicher, dass du sie verlassen möchtest?", "gui.abuseReport.discard.discard": "Verlassen und Meldung verwerfen", "gui.abuseReport.discard.draft": "Als Entwurf speichern", "gui.abuseReport.discard.return": "Bearbeitung fortsetzen", "gui.abuseReport.discard.title": "Meldung und Anmerkungen verwerfen?", "gui.abuseReport.draft.content": "Möchtest du die vorhandene Meldung weiterbearbeiten oder sie verwerfen und eine neue Meldung erstellen?", "gui.abuseReport.draft.discard": "Verwerfen", "gui.abuseReport.draft.edit": "Bearbeitung fortsetzen", "gui.abuseReport.draft.quittotitle.content": "Möchtest du ihn weiterbearbeiten oder verwerfen?", "gui.abuseReport.draft.quittotitle.title": "Du hast einen Meldeentwurf, der verloren geht, wenn du das Spiel verlässt.", "gui.abuseReport.draft.title": "Meldeentwurf bearbeiten?", "gui.abuseReport.error.title": "Problem beim <PERSON>en deiner Meldung", "gui.abuseReport.message": "Wo hast du das unerwünschte Verhalten wahrgenommen?\nDiese Auswahl erleichtert es uns, deinen Vorgang zu un<PERSON>uchen.", "gui.abuseReport.more_comments": "<PERSON><PERSON> schildere, was vorgefallen ist:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON> schildere, we<PERSON><PERSON><PERSON> du diesen Profilnamen melden möchtest:", "gui.abuseReport.name.reporting": "Du meldest „%s“.", "gui.abuseReport.name.title": "Unangemessenen Profilnamen des Spielers melden", "gui.abuseReport.observed_what": "Was ist der Grund für die Meldung?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON><PERSON> mehr zum Melden", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Dr<PERSON> oder Alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON> ermu<PERSON>gt andere, sich an rechtswidrigen drogenbezogenen Aktivitäten zu beteiligen, oder regt Minderjährige zum Alkoholkonsum an.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Sexuelle Ausbeutung oder Missbrauch von Kindern", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> spricht über anstößiges Verhalten gegenüber Kindern oder fördert es auf andere Weise.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON>er<PERSON><PERSON>dung", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON> schä<PERSON>gt deinen Ruf oder jenen einer anderen Person, zum Beispiel durch die Verbreitung von Falschinformationen mit dem Ziel, andere zu täuschen oder zu betrügen.", "gui.abuseReport.reason.description": "Beschreibung:", "gui.abuseReport.reason.false_reporting": "Falschmeldung", "gui.abuseReport.reason.generic": "Ich möchte diesen Spieler melden", "gui.abuseReport.reason.generic.description": "Ich ärgere mich über diesen Spieler bzw. dieser hat etwas getan, was mir nicht gefällt.", "gui.abuseReport.reason.harassment_or_bullying": "Belästigung oder Mobbing", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, attack<PERSON><PERSON> oder schikaniert dich oder eine andere Person. Dies gilt auch, wenn jemand wied<PERSON><PERSON>t versucht, mit dir oder einer anderen Person ungefragt in Verbindung zu treten oder persönliche Privatinformationen über dich oder eine andere Person ungefragt zu veröffentlichen („Doxen“).", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON> greift dich oder einen anderen Spieler aufgrund von Merkmalen seiner Identität wie Religion, Ethnie oder Sexualität an.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON><PERSON>, anderen Personen Schaden zuzufügen", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON> d<PERSON>ht damit, dir oder jemand anderem im wirklichen Leben Schaden zuzufügen.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Intime Bilder ohne Einverständnis", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON> spricht über private und intime Bilder, gibt sie weiter oder macht sie auf andere Weise zugänglich.", "gui.abuseReport.reason.self_harm_or_suicide": "Selbstverletzung oder Selbstmord", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON> droht damit, sich selbst im wirklichen Leben zu verletzen, oder sp<PERSON>t davon, das zu tun.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins, die sexuelle Handlungen, Geschlechtsorgane und/oder sexuelle Gewalt grafisch darstellen.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismus oder gewalttätiger Extremismus", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> spricht über Terrorismus oder gewalttätigen Extremismus aus politischen, religiösen, ideologischen oder anderen Gründen, fö<PERSON><PERSON> ihn oder droht damit, ihn zu verüben.", "gui.abuseReport.reason.title": "Meldekategorie auswählen", "gui.abuseReport.report_sent_msg": "Wir haben deine Meldung erfolgreich erhalten. Danke!\n\nUnser Team wird sich möglichst zeitnah darum kümmern.", "gui.abuseReport.select_reason": "Meldekategorie auswählen", "gui.abuseReport.send": "<PERSON><PERSON><PERSON> absenden", "gui.abuseReport.send.comment_too_long": "Bitte kürze die Anmerkung.", "gui.abuseReport.send.error_message": "Beim Senden deiner Meldung ist ein Fehler aufgetreten:\n‚%s‘", "gui.abuseReport.send.generic_error": "<PERSON>im <PERSON>en deiner Meldung ist ein unerwarteter Fehler aufgetreten.", "gui.abuseReport.send.http_error": "Beim <PERSON> deiner Meldung ist ein unerwarteter HTTP‐Fehler aufgetreten.", "gui.abuseReport.send.json_error": "Beim <PERSON>en deiner Meldung wurden fehlerhafte Nutzdaten festgestellt.", "gui.abuseReport.send.no_reason": "Bitte wähle eine Meldekategorie.", "gui.abuseReport.send.not_attested": "Bitte lies die obige Aussage und hake das Kontrollkästchen ab, um die Meldung absenden zu können.", "gui.abuseReport.send.service_unavailable": "Der Dienst zur Meldung von Fehlverhalten kann nicht erreicht werden. <PERSON>te stelle sicher, dass du mit dem Internet verbunden bist, und versuche es erneut.", "gui.abuseReport.sending.title": "<PERSON><PERSON> wird gesendet …", "gui.abuseReport.sent.title": "Meldung gesendet", "gui.abuseReport.skin.title": "Skin des Spielers melden", "gui.abuseReport.title": "<PERSON><PERSON><PERSON> melden", "gui.abuseReport.type.chat": "Chatnachrichten", "gui.abuseReport.type.name": "Profilname", "gui.abuseReport.type.skin": "Skin", "gui.acknowledge": "Verstanden", "gui.advancements": "Fortschritte", "gui.all": "Alle", "gui.back": "Zurück", "gui.banned.description": "%s\n\n%s\n\nErfahre mehr unter folgendem Link: %s", "gui.banned.description.permanent": "<PERSON><PERSON> ist dauerhaft gesperrt, was bedeu<PERSON><PERSON>, dass du weder online spielen noch Realms beitreten kannst.", "gui.banned.description.reason": "Wir haben kürzlich eine Meldung über schlechtes Verhalten von deinem Konto erhalten. Unsere Moderatoren haben deinen Fall nun überprüft und als %s erkannt, was gegen die Minecraft‐Community‐Standards verstößt.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s – %s", "gui.banned.description.temporary": "%s Bis dahin kannst du weder online spielen noch Realms beitreten.", "gui.banned.description.temporary.duration": "Dein Ko<PERSON> ist vorübergehend gesperrt und wird in %s wieder aktiviert.", "gui.banned.description.unknownreason": "Wir haben kürzlich eine Meldung über schlechtes Verhalten von deinem Konto erhalten. Unsere Moderatoren haben deinen Fall nun überprüft und festgestellt, dass er gegen die Minecraft‐Community‐Standards verstößt.", "gui.banned.name.description": "Dein derzeitiger Profilname „%s“ verstößt gegen unsere Community‐Standards. Du kannst im Einzelspielermodus spielen, musst aber deinen Profilnamen ändern, um online spielen zu können.\n\nErfahre mehr unter dem folgenden Link oder reiche dort eine Einzelfallprüfung ein: %s", "gui.banned.name.title": "Profilname im Mehrspielermodus nicht erlaubt", "gui.banned.reason.defamation_impersonation_false_information": "Identitätsdiebstahl oder Verbreitung von Informationen, um andere zu täuschen oder zu betrügen", "gui.banned.reason.drugs": "Erwähnung rechtswidriger Drogen", "gui.banned.reason.extreme_violence_or_gore": "Darstellungen von exzessiver Gewalt oder blutigen Szenen aus dem wirklichen Leben", "gui.banned.reason.false_reporting": "Übermäßig viele falsche oder ungenaue Meldungen", "gui.banned.reason.fraud": "Betrügerische Aneignung oder Verwertung von Inhalten", "gui.banned.reason.generic_violation": "Verletzung der Community‐Standards", "gui.banned.reason.harassment_or_bullying": "Beleidigende Sprache, die in gezielt verletzender Weise verwendet wurde", "gui.banned.reason.hate_speech": "Hassrede oder Diskriminierung", "gui.banned.reason.hate_terrorism_notorious_figure": "Erwä<PERSON><PERSON> von Hassgruppen, terroristischen Vereinigungen oder gefährlichen Persönlichkeiten", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON><PERSON><PERSON><PERSON>, Personen oder Eigentum im wirklichen Leben zu schädigen", "gui.banned.reason.nudity_or_pornography": "Darstellung von anzüglichem oder pornografischem Material", "gui.banned.reason.sexually_inappropriate": "Themen oder Inhalte mit sexuellem Bezug", "gui.banned.reason.spam_or_advertising": "Spam oder Werbung", "gui.banned.skin.description": "Dein derzeitiger Skin verstößt gegen unsere Community‐Standards. Du kannst mit einem Standard‐Skin weiterspielen oder einen neuen Skin auswählen.\n\nErfahre mehr unter dem folgenden Link oder reiche dort eine Einzelfallprüfung ein: %s", "gui.banned.skin.title": "<PERSON> nicht erlaubt", "gui.banned.title.permanent": "Konto dauerhaft gesperrt", "gui.banned.title.temporary": "Konto vorübergehend gesperrt", "gui.cancel": "Abbrechen", "gui.chatReport.comments": "Anmerkungen", "gui.chatReport.describe": "<PERSON> Angabe von Einzelheiten hilft uns dabei, eine fundierte Entscheidung zu treffen.", "gui.chatReport.discard.content": "Wenn du die Seite verlässt, gehen diese Meldung und deine Anmerkungen verloren.\n<PERSON><PERSON> du sicher, dass du sie verlassen möchtest?", "gui.chatReport.discard.discard": "Verlassen und Meldung verwerfen", "gui.chatReport.discard.draft": "Als Entwurf speichern", "gui.chatReport.discard.return": "Bearbeitung fortsetzen", "gui.chatReport.discard.title": "Meldung und Anmerkungen verwerfen?", "gui.chatReport.draft.content": "Möchtest du die vorhandene Meldung weiterbearbeiten oder sie verwerfen und eine neue Meldung erstellen?", "gui.chatReport.draft.discard": "Verwerfen", "gui.chatReport.draft.edit": "Bearbeitung fortsetzen", "gui.chatReport.draft.quittotitle.content": "Möchtest du ihn weiterbearbeiten oder verwerfen?", "gui.chatReport.draft.quittotitle.title": "Du hast einen Entwurf einer Chatmeldung, der verloren geht, wenn du das Spiel verlässt.", "gui.chatReport.draft.title": "Entwurf der Chatmeldung bearbeiten?", "gui.chatReport.more_comments": "<PERSON><PERSON> schildere, was vorgefallen ist:", "gui.chatReport.observed_what": "Was ist der Grund für die Meldung?", "gui.chatReport.read_info": "<PERSON><PERSON><PERSON><PERSON> mehr zum Melden", "gui.chatReport.report_sent_msg": "Wir haben deine Meldung erfolgreich erhalten. Danke!\n\nUnser Team wird sich möglichst zeitnah darum kümmern.", "gui.chatReport.select_chat": "Zu meldende Chatnachrichten auswählen", "gui.chatReport.select_reason": "Meldekategorie auswählen", "gui.chatReport.selected_chat": "%s Chatnachricht(en) zum Melden ausgewählt", "gui.chatReport.send": "<PERSON><PERSON><PERSON> absenden", "gui.chatReport.send.comments_too_long": "Bitte kürze die Anmerkung.", "gui.chatReport.send.no_reason": "Bitte wähle eine Meldekategorie.", "gui.chatReport.send.no_reported_messages": "Bitte wähle mindestens eine zu meldende Chatnachricht aus.", "gui.chatReport.send.too_many_messages": "<PERSON> ve<PERSON>, zu viele Nachrichten in die Meldung aufzunehmen.", "gui.chatReport.title": "Chatverhalten des Spielers melden", "gui.chatSelection.context": "Diese Auswahl umgebende Nachrichten werden aufgenommen, um zusätzlichen Kontext zu bieten.", "gui.chatSelection.fold": "%s Nachricht(en) ausgeblendet", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s ist dem Chat beigetreten", "gui.chatSelection.message.narrate": "%s sagte: %s um %s", "gui.chatSelection.selected": "%s von %s Nachrichten ausgewählt", "gui.chatSelection.title": "Zu meldende Chatnachrichten auswählen", "gui.continue": "Fortfahren", "gui.copy_link_to_clipboard": "<PERSON>", "gui.days": "%s Tag(e)", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "<PERSON>ter", "gui.entity_tooltip.type": "Typ: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s <PERSON><PERSON> wurden abgelehnt", "gui.fileDropFailure.title": "Hinzufügen der Dateien fehlgeschlagen", "gui.hours": "%s Stunde(n)", "gui.loadingMinecraft": "Minecraft wird geladen", "gui.minutes": "%s Minute(n)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s‐Schaltfläche", "gui.narrate.editBox": "%s‐Eingabefeld: %s", "gui.narrate.slider": "%s<PERSON>", "gui.narrate.tab": "%s‐Reiter", "gui.no": "<PERSON><PERSON>", "gui.none": "<PERSON><PERSON>", "gui.ok": "OK", "gui.open_report_dir": "Berichtsverzeichnis öffnen", "gui.proceed": "Fortfahren", "gui.recipebook.moreRecipes": "Rechtsklick für mehr", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "<PERSON>e …", "gui.recipebook.toggleRecipes.all": "Alles wird angezeigt", "gui.recipebook.toggleRecipes.blastable": "Schmelzbares wird angezeigt", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON><PERSON><PERSON> wird angezeigt", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wird angezeigt", "gui.recipebook.toggleRecipes.smokable": "Räucherbares wird angezeigt", "gui.report_to_server": "<PERSON> den <PERSON> melden", "gui.socialInteractions.blocking_hint": "Mit <PERSON>‐Konto verwalten", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON> im Chat blockiert", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON> im Chat ausgeblendet", "gui.socialInteractions.hidden_in_chat": "Chatnachrichten von %s werden ausgeblendet", "gui.socialInteractions.hide": "<PERSON><PERSON> au<PERSON>", "gui.socialInteractions.narration.hide": "Nachrichten von %s ausblenden", "gui.socialInteractions.narration.report": "Spieler %s melden", "gui.socialInteractions.narration.show": "Nachrichten von %s anzeigen", "gui.socialInteractions.report": "Melden", "gui.socialInteractions.search_empty": "<PERSON><PERSON> Spieler mit diesem Namen gefunden", "gui.socialInteractions.search_hint": "<PERSON>e …", "gui.socialInteractions.server_label.multiple": "%s – %s <PERSON><PERSON>ler", "gui.socialInteractions.server_label.single": "%s – %s <PERSON><PERSON>ler", "gui.socialInteractions.show": "<PERSON><PERSON>igen", "gui.socialInteractions.shown_in_chat": "Chatnachrichten von %s werden angezeigt", "gui.socialInteractions.status_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "<PERSON><PERSON>t – Offline", "gui.socialInteractions.status_hidden": "Ausgeblendet", "gui.socialInteractions.status_hidden_offline": "Ausgeblendet – Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "Alle", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "Ausgeblendet", "gui.socialInteractions.title": "Soziale Interaktionen", "gui.socialInteractions.tooltip.hide": "Nachrichten ausblenden", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON> melden", "gui.socialInteractions.tooltip.report.disabled": "Der Meldedienst ist nicht verfügbar.", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON><PERSON> Nach<PERSON> von %s, die gemeldet werden könnten", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON>ser Spieler kann nicht gemeldet werden, da seine Chatnachrichten auf diesem Server nicht überprüft werden können.", "gui.socialInteractions.tooltip.show": "Nachrichten anzeigen", "gui.stats": "Statistiken", "gui.toMenu": "Zurück zur Serverauswahl", "gui.toRealms": "Zurück zur Realm‐Auswahl", "gui.toTitle": "Zurück zum Hauptmenü", "gui.toWorld": "Zurück zur Weltauswahl", "gui.togglable_slot": "<PERSON><PERSON><PERSON>, um Inventarplatz zu sperren", "gui.up": "Hoch", "gui.waitingForResponse.button.inactive": "Zurück (%s s)", "gui.waitingForResponse.title": "<PERSON>f <PERSON> warten", "gui.yes": "<PERSON>a", "hanging_sign.edit": "Hängeschildbeschriftung bearbeiten", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "Träumen", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Nachdenken", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Gegenstand zerstören", "inventory.hotbarInfo": "Schnellzugriffsleiste mit %1$s+%2$s speichern", "inventory.hotbarSaved": "Schnellzugriffsleiste gespeichert (mit %1$s+%2$s wiederherstellen)", "item.canBreak": "<PERSON><PERSON> abbauen:", "item.canPlace": "Kann platziert werden auf:", "item.canUse.unknown": "Unbekannt", "item.color": "Farbe: %s", "item.components": "%s Komponente(n)", "item.disabled": "Deaktivierter Gegenstand", "item.durability": "Haltbarkeit: %s/%s", "item.dyed": "Gefärbt", "item.minecraft.acacia_boat": "Akazienholzboot", "item.minecraft.acacia_chest_boat": "Akazienholztruhenboot", "item.minecraft.allay_spawn_egg": "Hilfsgeister-Spawn-Ei", "item.minecraft.amethyst_shard": "Amethystscherbe", "item.minecraft.angler_pottery_shard": "Angler-T<PERSON>pferscherbe", "item.minecraft.angler_pottery_sherd": "Angler-T<PERSON>pferscherbe", "item.minecraft.apple": "<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Schützen-Töpferscherbe", "item.minecraft.archer_pottery_sherd": "Schützen-Töpferscherbe", "item.minecraft.armadillo_scute": "Gürteltier-Hornschild", "item.minecraft.armadillo_spawn_egg": "Gürteltier-Spawn-Ei", "item.minecraft.armor_stand": "Rüstungsständer", "item.minecraft.arms_up_pottery_shard": "Gebärden-Töpferscherbe", "item.minecraft.arms_up_pottery_sherd": "Gebärden-Töpferscherbe", "item.minecraft.arrow": "Pfeil", "item.minecraft.axolotl_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.axolotl_spawn_egg": "Axolotl-Spawn-Ei", "item.minecraft.baked_potato": "Ofenkarto<PERSON>l", "item.minecraft.bamboo_chest_raft": "Bambus-Truhenfloß", "item.minecraft.bamboo_raft": "Bambusfloß", "item.minecraft.bat_spawn_egg": "Fledermaus-Spawn-Ei", "item.minecraft.bee_spawn_egg": "Bienen-Spawn-Ei", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "Rote Bete", "item.minecraft.beetroot_seeds": "Rote-Bete-Samen", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Birkenholzboot", "item.minecraft.birch_chest_boat": "Birkenholztruhenboot", "item.minecraft.black_bundle": "Schwarzes Bündel", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_shard": "Klingen-Töpferscherbe", "item.minecraft.blade_pottery_sherd": "Klingen-Töpferscherbe", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "Lohenrute", "item.minecraft.blaze_spawn_egg": "Lohen-Spawn-Ei", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Sumpfskelett-Spawn-Ei", "item.minecraft.bolt_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolzen-Rüstungsbesatz", "item.minecraft.bone": "K<PERSON>chen", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON>me<PERSON>", "item.minecraft.book": "<PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Spickelbord-Bannervorlage", "item.minecraft.bow": "Bogen", "item.minecraft.bowl": "Schüssel", "item.minecraft.bread": "<PERSON><PERSON>", "item.minecraft.breeze_rod": "Böenrute", "item.minecraft.breeze_spawn_egg": "Böen-Spawn-Ei", "item.minecraft.brewer_pottery_shard": "B<PERSON>uer-Töpferscherbe", "item.minecraft.brewer_pottery_sherd": "B<PERSON>uer-Töpferscherbe", "item.minecraft.brewing_stand": "Braustand", "item.minecraft.brick": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON> Bündel", "item.minecraft.brown_dye": "<PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON>l", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Bündel", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Nimmt einen gemischten Stapel Gegenstände auf", "item.minecraft.bundle.full": "Voll", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Flammen-Töpferscherbe", "item.minecraft.burn_pottery_sherd": "Flammen-Töpferscherbe", "item.minecraft.camel_spawn_egg": "Dromedar-Spawn-Ei", "item.minecraft.carrot": "Ka<PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Karottenrute", "item.minecraft.cat_spawn_egg": "Katzen-Spawn-Ei", "item.minecraft.cauldron": "Kessel", "item.minecraft.cave_spider_spawn_egg": "Höhlenspinnen-Spawn-Ei", "item.minecraft.chainmail_boots": "Kettenstiefel", "item.minecraft.chainmail_chestplate": "Kettenhemd", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "Kettenhose", "item.minecraft.charcoal": "Holzkohle", "item.minecraft.cherry_boat": "<PERSON>rsch<PERSON>zboot", "item.minecraft.cherry_chest_boat": "Kirschholztruhenboot", "item.minecraft.chest_minecart": "Güterlore", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Huhn-Spawn-Ei", "item.minecraft.chorus_fruit": "Chorusfrucht", "item.minecraft.clay_ball": "Tonklumpen", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.coast_armor_trim_smithing_template.new": "Küsten-Rüstungsbesatz", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_spawn_egg": "Kabeljau-Spawn-Ei", "item.minecraft.command_block_minecart": "Befehlsblocklore", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Steak", "item.minecraft.cooked_chicken": "Gebratenes Hühnchen", "item.minecraft.cooked_cod": "Gebratener <PERSON>bel<PERSON>", "item.minecraft.cooked_mutton": "Gebratenes Hammelfleisch", "item.minecraft.cooked_porkchop": "Gebratenes Schweinefleisch", "item.minecraft.cooked_rabbit": "Gebratenes Kaninchen", "item.minecraft.cooked_salmon": "Gebratener Lachs", "item.minecraft.cookie": "Keks", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "Kuh-Spawn-Ei", "item.minecraft.creaking_spawn_egg": "Knarz-Spawn-Ei", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-<PERSON><PERSON>", "item.minecraft.creeper_spawn_egg": "Creeper-Spawn-Ei", "item.minecraft.crossbow": "Armbrust", "item.minecraft.crossbow.projectile": "Geschoss:", "item.minecraft.crossbow.projectile.multiple": "Geschoss: %s× %s", "item.minecraft.crossbow.projectile.single": "Geschoss: %s", "item.minecraft.cyan_bundle": "Türkises Bündel", "item.minecraft.cyan_dye": "Türkiser Farbstoff", "item.minecraft.cyan_harness": "Türkises Geschirr", "item.minecraft.danger_pottery_shard": "Gefahren-Töpferscherbe", "item.minecraft.danger_pottery_sherd": "Gefahren-Töpferscherbe", "item.minecraft.dark_oak_boat": "Schwarzeichenholzboot", "item.minecraft.dark_oak_chest_boat": "Schwarzeichenholztruhenboot", "item.minecraft.debug_stick": "Debug-Stab", "item.minecraft.debug_stick.empty": "%s hat keine Blockzustände", "item.minecraft.debug_stick.select": "‚%s‘ ausgewählt (%s)", "item.minecraft.debug_stick.update": "‚%s‘ ist jetzt %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Diamantaxt", "item.minecraft.diamond_boots": "Diamantstiefel", "item.minecraft.diamond_chestplate": "Diamantharnisch", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Diamanten<PERSON>", "item.minecraft.diamond_leggings": "Diamantbeinschutz", "item.minecraft.diamond_pickaxe": "Diamantspitzhacke", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Diamantschwert", "item.minecraft.disc_fragment_5": "Plattenbruchstück", "item.minecraft.disc_fragment_5.desc": "Schallplatte - 5", "item.minecraft.dolphin_spawn_egg": "Delfin-Spawn-Ei", "item.minecraft.donkey_spawn_egg": "Esel-Spawn-Ei", "item.minecraft.dragon_breath": "Drachenatem", "item.minecraft.dried_kelp": "Getrockneter Seetang", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON>enen-Spawn-Ei", "item.minecraft.dune_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.dune_armor_trim_smithing_template.new": "Dünen-Rüstungsbesatz", "item.minecraft.echo_shard": "Echoscherbe", "item.minecraft.egg": "<PERSON>i", "item.minecraft.elder_guardian_spawn_egg": "Großwächter-Spawn-Ei", "item.minecraft.elytra": "Elytren", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Verzaubertes Buch", "item.minecraft.enchanted_golden_apple": "Verzauberter goldener Apfel", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Enderdrachen-Spawn-Ei", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Enderman-Spawn-Ei", "item.minecraft.endermite_spawn_egg": "Endermiten-Spawn-Ei", "item.minecraft.evoker_spawn_egg": "Magier-Spawn-Ei", "item.minecraft.experience_bottle": "Erfahrungsfläschchen", "item.minecraft.explorer_pottery_shard": "Entdecker-Töpferscherbe", "item.minecraft.explorer_pottery_sherd": "Entdecker-Töpferscherbe", "item.minecraft.eye_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.eye_armor_trim_smithing_template.new": "Augen-Rüstungsbesatz", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Fermentiertes Spinnenauge", "item.minecraft.field_masoned_banner_pattern": "Mauerung-Bannervorlage", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "Feuerkugel", "item.minecraft.firework_rocket": "Feuerwerksrakete", "item.minecraft.firework_rocket.flight": "Flugdauer:", "item.minecraft.firework_rocket.multiple_stars": "%s× %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Feuerwerksstern", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON> zu", "item.minecraft.firework_star.flicker": "Funkeln", "item.minecraft.firework_star.gray": "G<PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Hellblau", "item.minecraft.firework_star.light_gray": "Hellgrau", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Orange", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "Rot", "item.minecraft.firework_star.shape": "Unbekannte Form", "item.minecraft.firework_star.shape.burst": "Explosion", "item.minecraft.firework_star.shape.creeper": "Creeperförmig", "item.minecraft.firework_star.shape.large_ball": "Große Kugel", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Stern<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "Angel", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Feuerzeug", "item.minecraft.flow_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.flow_armor_trim_smithing_template.new": "Fluss-Rüstungsbesatz", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "Fluss", "item.minecraft.flow_banner_pattern.new": "Fluss-<PERSON><PERSON><PERSON>", "item.minecraft.flow_pottery_sherd": "Fluss-Töpferscherbe", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Blumen-<PERSON><PERSON><PERSON>", "item.minecraft.flower_pot": "Blu<PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "Fuchs-Spawn-Ei", "item.minecraft.friend_pottery_shard": "Freundes-Töpferscherbe", "item.minecraft.friend_pottery_sherd": "Freundes-Töpferscherbe", "item.minecraft.frog_spawn_egg": "Frosch-Spawn-Ei", "item.minecraft.furnace_minecart": "Antrieb<PERSON><PERSON>", "item.minecraft.ghast_spawn_egg": "Ghast-Spawn-Ei", "item.minecraft.ghast_tear": "Ghast-Träne", "item.minecraft.glass_bottle": "Glasflasche", "item.minecraft.glistering_melon_slice": "Glitzernde Melonenscheibe", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Globus-Bannervorlage", "item.minecraft.glow_berries": "Leuchtbeeren", "item.minecraft.glow_ink_sac": "Leuchttintenbeutel", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Leuchttintenfisch-Spawn-Ei", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Ziegen-Spawn-Ei", "item.minecraft.gold_ingot": "Gold<PERSON>ren", "item.minecraft.gold_nugget": "Goldklumpen", "item.minecraft.golden_apple": "<PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "Goldstiefel", "item.minecraft.golden_carrot": "<PERSON><PERSON> Karotte", "item.minecraft.golden_chestplate": "Goldharnisch", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON>", "item.minecraft.golden_leggings": "Goldbeinschutz", "item.minecraft.golden_pickaxe": "Goldspitzhacke", "item.minecraft.golden_shovel": "Goldschaufel", "item.minecraft.golden_sword": "Goldschwert", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Wächter-Spawn-Ei", "item.minecraft.gunpowder": "Schwarzpulver", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "Windstoßer", "item.minecraft.guster_banner_pattern.new": "Windstoßer-Bannervorlage", "item.minecraft.guster_pottery_sherd": "Windstoßer-Töpferscherbe", "item.minecraft.happy_ghast_spawn_egg": "Glücklicher-Ghast-Spawn-Ei", "item.minecraft.harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON>z des Meeres", "item.minecraft.heart_pottery_shard": "Herz-Töpferscherbe", "item.minecraft.heart_pottery_sherd": "Herz-Töpferscherbe", "item.minecraft.heartbreak_pottery_shard": "Herzschmerz-Töpferscherbe", "item.minecraft.heartbreak_pottery_sherd": "Herzschmerz-Töpferscherbe", "item.minecraft.hoglin_spawn_egg": "Hoglin-Spawn-Ei", "item.minecraft.honey_bottle": "Honig<PERSON>las<PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hopper_minecart": "Trichterlore", "item.minecraft.horse_spawn_egg": "Pferde-Spawn-Ei", "item.minecraft.host_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.host_armor_trim_smithing_template.new": "Gastwirts-Rüstungsbesatz", "item.minecraft.howl_pottery_shard": "Geheul-Töpferscherbe", "item.minecraft.howl_pottery_sherd": "Geheul-Töpferscherbe", "item.minecraft.husk_spawn_egg": "Wüstenzombie-Spawn-Ei", "item.minecraft.ink_sac": "Tintenbeutel", "item.minecraft.iron_axe": "Eisenaxt", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "Eisenharnisch", "item.minecraft.iron_golem_spawn_egg": "Eisengolem-Spawn-Ei", "item.minecraft.iron_helmet": "Eisenhelm", "item.minecraft.iron_hoe": "Eisenhacke", "item.minecraft.iron_horse_armor": "Eiserner Rossharnisch", "item.minecraft.iron_ingot": "Eisenbarren", "item.minecraft.iron_leggings": "Eisenbeinschutz", "item.minecraft.iron_nugget": "Eisenklumpen", "item.minecraft.iron_pickaxe": "Eisenspitzhacke", "item.minecraft.iron_shovel": "<PERSON><PERSON>ns<PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "Eisenschwert", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Tropenholzboot", "item.minecraft.jungle_chest_boat": "Tropenholztruhenboot", "item.minecraft.knowledge_book": "Buch des Wissens", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_leggings": "Lederhose", "item.minecraft.light_blue_bundle": "<PERSON><PERSON>ues Bündel", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "Hellgrünes Bündel", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "Hellgr<PERSON><PERSON>", "item.minecraft.lingering_potion": "Verweiltrank", "item.minecraft.lingering_potion.effect.awkward": "Seltsamer Verweiltrank", "item.minecraft.lingering_potion.effect.empty": "Nicht braubarer Verweiltrank", "item.minecraft.lingering_potion.effect.fire_resistance": "Verweiltrank der Feuerresistenz", "item.minecraft.lingering_potion.effect.harming": "Verweiltrank des Schadens", "item.minecraft.lingering_potion.effect.healing": "Verweiltrank der Heilung", "item.minecraft.lingering_potion.effect.infested": "Verweiltrank des Befalls", "item.minecraft.lingering_potion.effect.invisibility": "Verweiltrank der Unsichtbarkeit", "item.minecraft.lingering_potion.effect.leaping": "Verweiltrank der Sprungkraft", "item.minecraft.lingering_potion.effect.levitation": "Verweiltrank der Schwebekraft", "item.minecraft.lingering_potion.effect.luck": "Verweiltrank des Glücks", "item.minecraft.lingering_potion.effect.mundane": "Gewöhnlicher Verweiltrank", "item.minecraft.lingering_potion.effect.night_vision": "Verweiltrank der Nachtsicht", "item.minecraft.lingering_potion.effect.oozing": "Verweiltrank des Schleimens", "item.minecraft.lingering_potion.effect.poison": "Verweiltrank der Vergiftung", "item.minecraft.lingering_potion.effect.regeneration": "Verweiltrank der Regeneration", "item.minecraft.lingering_potion.effect.slow_falling": "Verweiltrank des sanften Falls", "item.minecraft.lingering_potion.effect.slowness": "Verweiltrank der Langsamkeit", "item.minecraft.lingering_potion.effect.strength": "Verweiltrank der Stärke", "item.minecraft.lingering_potion.effect.swiftness": "Verweiltrank der Schnelligkeit", "item.minecraft.lingering_potion.effect.thick": "Dick<PERSON><PERSON><PERSON><PERSON><PERSON> Verweiltrank", "item.minecraft.lingering_potion.effect.turtle_master": "Verweiltrank des Schildkrötenmeisters", "item.minecraft.lingering_potion.effect.water": "Verweilende Wasserflasche", "item.minecraft.lingering_potion.effect.water_breathing": "Verweiltrank der Unterwasseratmung", "item.minecraft.lingering_potion.effect.weakness": "Verweiltrank der Schwäche", "item.minecraft.lingering_potion.effect.weaving": "Verweiltrank des Webens", "item.minecraft.lingering_potion.effect.wind_charged": "Verweiltrank der Windladung", "item.minecraft.llama_spawn_egg": "Lama-Spawn-Ei", "item.minecraft.lodestone_compass": "Leitstein-Kompass", "item.minecraft.mace": "Streitkolben", "item.minecraft.magenta_bundle": "Magenta Bündel", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmacreme", "item.minecraft.magma_cube_spawn_egg": "Magmawürfel-Spawn-Ei", "item.minecraft.mangrove_boat": "Mangrovenholzboot", "item.minecraft.mangrove_chest_boat": "Mangrovenholztruhenboot", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "Melonenkerne", "item.minecraft.melon_slice": "Melonenscheibe", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.minecart": "Lore", "item.minecraft.miner_pottery_shard": "Bergarbeiter-Töpferscherbe", "item.minecraft.miner_pottery_sherd": "Bergarbeiter-Töpferscherbe", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Mojang-Logo", "item.minecraft.mojang_banner_pattern.new": "Mojang-Logo-Bannervorlage", "item.minecraft.mooshroom_spawn_egg": "Mooshroom-Spawn-Ei", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON>-Töpferscherbe", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON>-Töpferscherbe", "item.minecraft.mule_spawn_egg": "Maultier-Spawn-Ei", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Schallplatte", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Schallplatte", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Schallplatte", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Schallplatte", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Schallplatte", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Schallplatte", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Schallplatte", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Schallplatte", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (Spieluhr)", "item.minecraft.music_disc_far": "Schallplatte", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Schallplatte", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Schallplatte", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Schallplatte", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Schallplatte", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Schallplatte", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Schallplatte", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Schallplatte", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Schallplatte", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Schallplatte", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Schallplatte", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Schallplatte", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Schallplatte", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Namensschild", "item.minecraft.nautilus_shell": "Nautilusschale", "item.minecraft.nether_brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nether_star": "Netherstern", "item.minecraft.nether_wart": "Netherwarze", "item.minecraft.netherite_axe": "Netheritaxt", "item.minecraft.netherite_boots": "Netheritstiefel", "item.minecraft.netherite_chestplate": "Netheritharnisch", "item.minecraft.netherite_helmet": "Netherithelm", "item.minecraft.netherite_hoe": "Netherithacke", "item.minecraft.netherite_ingot": "Netheritbarren", "item.minecraft.netherite_leggings": "Netheritbeinschutz", "item.minecraft.netherite_pickaxe": "Netheritspitzhacke", "item.minecraft.netherite_scrap": "Netheritplatten", "item.minecraft.netherite_shovel": "Netheritschaufel", "item.minecraft.netherite_sword": "Netheritschwert", "item.minecraft.netherite_upgrade_smithing_template": "Schmiedevorlage", "item.minecraft.netherite_upgrade_smithing_template.new": "Netheritaufwertung", "item.minecraft.oak_boat": "Eichenholzboot", "item.minecraft.oak_chest_boat": "Eichenholztruhenboot", "item.minecraft.ocelot_spawn_egg": "Ozelot-Spawn-Ei", "item.minecraft.ominous_bottle": "Unheilvolle Flasche", "item.minecraft.ominous_trial_key": "Unheilvoller Prüfungsschlüssel", "item.minecraft.orange_bundle": "Oranges Bündel", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "Oranges <PERSON>", "item.minecraft.painting": "G<PERSON>ä<PERSON>", "item.minecraft.pale_oak_boat": "Blasseichenholzboot", "item.minecraft.pale_oak_chest_boat": "Blasseichenholztruhenboot", "item.minecraft.panda_spawn_egg": "Panda-Spawn-Ei", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Papageien-Spawn-Ei", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Phantom-Spawn-Ei", "item.minecraft.pig_spawn_egg": "Schweine-Spawn-Ei", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Schnauzen-Bannervorlage", "item.minecraft.piglin_brute_spawn_egg": "Piglin-Barbaren-Spawn-Ei", "item.minecraft.piglin_spawn_egg": "Piglin-Spawn-Ei", "item.minecraft.pillager_spawn_egg": "Plünderer-Spawn-Ei", "item.minecraft.pink_bundle": "<PERSON>", "item.minecraft.pink_dye": "<PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Kannenpflanze", "item.minecraft.pitcher_pod": "Kannenpflanzenkapsel", "item.minecraft.plenty_pottery_shard": "Reichtums-Töpferscherbe", "item.minecraft.plenty_pottery_sherd": "Reichtums-Töpferscherbe", "item.minecraft.poisonous_potato": "Giftige Kartoffel", "item.minecraft.polar_bear_spawn_egg": "Eisbären-Spawn-Ei", "item.minecraft.popped_chorus_fruit": "Geplatzte Chorusfrucht", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "<PERSON>rank", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> Trank", "item.minecraft.potion.effect.empty": "<PERSON><PERSON> braubarer Trank", "item.minecraft.potion.effect.fire_resistance": "Trank der Feuerresistenz", "item.minecraft.potion.effect.harming": "<PERSON><PERSON> des Schadens", "item.minecraft.potion.effect.healing": "<PERSON>rank der Heilung", "item.minecraft.potion.effect.infested": "Trank des Befalls", "item.minecraft.potion.effect.invisibility": "Trank der Unsichtbarkeit", "item.minecraft.potion.effect.leaping": "Trank der Sprungkraft", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON> der Schwebekraft", "item.minecraft.potion.effect.luck": "<PERSON><PERSON> des Glücks", "item.minecraft.potion.effect.mundane": "Gewöhnlicher Trank", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON> der Nachtsicht", "item.minecraft.potion.effect.oozing": "<PERSON>rank des Schleimens", "item.minecraft.potion.effect.poison": "Trank der Vergiftung", "item.minecraft.potion.effect.regeneration": "Trank der Regeneration", "item.minecraft.potion.effect.slow_falling": "Trank des sanften Falls", "item.minecraft.potion.effect.slowness": "Trank der Langsamkeit", "item.minecraft.potion.effect.strength": "Trank der Stärke", "item.minecraft.potion.effect.swiftness": "Trank der Schnelligkeit", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Trank des Schildkrötenmeisters", "item.minecraft.potion.effect.water": "Wasserflasche", "item.minecraft.potion.effect.water_breathing": "Trank der Unterwasseratmung", "item.minecraft.potion.effect.weakness": "Trank der Schwäche", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON> des Webens", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON> der Windladung", "item.minecraft.pottery_shard_archer": "Schützen-Töpferscherbe", "item.minecraft.pottery_shard_arms_up": "Gebärden-Töpferscherbe", "item.minecraft.pottery_shard_prize": "Juwelen-Töpferscherbe", "item.minecraft.pottery_shard_skull": "Totenkopf-Töpferscherbe", "item.minecraft.powder_snow_bucket": "Pulverschneeeimer", "item.minecraft.prismarine_crystals": "Prismarinkristalle", "item.minecraft.prismarine_shard": "Prismarinscherbe", "item.minecraft.prize_pottery_shard": "Juwelen-Töpferscherbe", "item.minecraft.prize_pottery_sherd": "Juwelen-Töpferscherbe", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "Kugelfi<PERSON><PERSON>r", "item.minecraft.pufferfish_spawn_egg": "Kugelfisch-Spawn-Ei", "item.minecraft.pumpkin_pie": "Kürbiskuchen", "item.minecraft.pumpkin_seeds": "Kürbiskerne", "item.minecraft.purple_bundle": "Violettes Bündel", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON>pf<PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Kaninchen-Spawn-Ei", "item.minecraft.rabbit_stew": "<PERSON><PERSON>chenragout", "item.minecraft.raiser_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.raiser_armor_trim_smithing_template.new": "Aufzieher-Rüstungsbesatz", "item.minecraft.ravager_spawn_egg": "Verwüster-Spawn-Ei", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Bergungskompass", "item.minecraft.red_bundle": "Rotes Bündel", "item.minecraft.red_dye": "<PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON>", "item.minecraft.redstone": "Redstone-Staub", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.resin_clump": "Harz<PERSON>lump<PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.rib_armor_trim_smithing_template.new": "Rippen-Rüstungsbesatz", "item.minecraft.rotten_flesh": "Verrottetes Fleisch", "item.minecraft.saddle": "Sattel", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "Lac<PERSON><PERSON><PERSON>", "item.minecraft.salmon_spawn_egg": "Lachs-Spawn-Ei", "item.minecraft.scrape_pottery_sherd": "Abschabe-Töpferscherbe", "item.minecraft.scute": "Hornschild", "item.minecraft.sentry_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.sentry_armor_trim_smithing_template.new": "Wachen-Rüstungsbesatz", "item.minecraft.shaper_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.shaper_armor_trim_smithing_template.new": "Gestalter-Rüstungsbesatz", "item.minecraft.sheaf_pottery_shard": "Garben-Töpferscherbe", "item.minecraft.sheaf_pottery_sherd": "Garben-Töpferscherbe", "item.minecraft.shears": "<PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Schafs-Spawn-Ei", "item.minecraft.shelter_pottery_shard": "Zufluchts-Töpferscherbe", "item.minecraft.shelter_pottery_sherd": "Zufluchts-Töpferscherbe", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON>", "item.minecraft.shield.cyan": "Türkiser Schild", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON> Schild", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.orange": "<PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON>", "item.minecraft.shield.purple": "<PERSON><PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Shulker-Spawn-Ei", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.silence_armor_trim_smithing_template.new": "Stille-Rüstungsbesatz", "item.minecraft.silverfish_spawn_egg": "Silberfischchen-Spawn-Ei", "item.minecraft.skeleton_horse_spawn_egg": "Skelettpferde-Spawn-Ei", "item.minecraft.skeleton_spawn_egg": "Skelett-Spawn-Ei", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Schädel-Bannervorlage", "item.minecraft.skull_pottery_shard": "Totenkopf-Töpferscherbe", "item.minecraft.skull_pottery_sherd": "Totenkopf-Töpferscherbe", "item.minecraft.slime_ball": "Schleimball", "item.minecraft.slime_spawn_egg": "Schleim-Spawn-Ei", "item.minecraft.smithing_template": "Schmiedevorlage", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON><PERSON> auf:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Barren oder Kristall hinzufügen", "item.minecraft.smithing_template.armor_trim.applies_to": "Rüstung", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Rüstungsteil hinzufügen", "item.minecraft.smithing_template.armor_trim.ingredients": "Barren & Kristalle", "item.minecraft.smithing_template.ingredients": "Werkstoffe:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Netheritbarren hinzufügen", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamantausrüstung", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Rüstungsteil, Werkzeug oder Schwert aus Diamant hinzufügen", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netheritbarren", "item.minecraft.smithing_template.upgrade": "Aufwertung: ", "item.minecraft.sniffer_spawn_egg": "Schnüffler-Spawn-Ei", "item.minecraft.snort_pottery_shard": "Schnaub-Töpferscherbe", "item.minecraft.snort_pottery_sherd": "Schnaub-Töpferscherbe", "item.minecraft.snout_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.snout_armor_trim_smithing_template.new": "Schnauzen-Rüstungsbesatz", "item.minecraft.snow_golem_spawn_egg": "Schneegolem-Spawn-Ei", "item.minecraft.snowball": "Schneeball", "item.minecraft.spectral_arrow": "Spektralpfeil", "item.minecraft.spider_eye": "Spinnenauge", "item.minecraft.spider_spawn_egg": "Spinnen-Spawn-Ei", "item.minecraft.spire_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.spire_armor_trim_smithing_template.new": "Turmspitzen-Rüstungsbesatz", "item.minecraft.splash_potion": "Wurftrank", "item.minecraft.splash_potion.effect.awkward": "Seltsamer Wurftrank", "item.minecraft.splash_potion.effect.empty": "Nicht braubarer Wurftrank", "item.minecraft.splash_potion.effect.fire_resistance": "Wurftrank der Feuerresistenz", "item.minecraft.splash_potion.effect.harming": "Wurftrank des Schadens", "item.minecraft.splash_potion.effect.healing": "Wurftrank der Heilung", "item.minecraft.splash_potion.effect.infested": "Wurftrank des Befalls", "item.minecraft.splash_potion.effect.invisibility": "Wurftrank der Unsichtbarkeit", "item.minecraft.splash_potion.effect.leaping": "Wurftrank der Sprungkraft", "item.minecraft.splash_potion.effect.levitation": "Wurftrank der Schwebekraft", "item.minecraft.splash_potion.effect.luck": "Wurftrank des Glücks", "item.minecraft.splash_potion.effect.mundane": "Gewöhnlicher Wurftrank", "item.minecraft.splash_potion.effect.night_vision": "Wurftrank der Nachtsicht", "item.minecraft.splash_potion.effect.oozing": "Wurftrank des Schleimens", "item.minecraft.splash_potion.effect.poison": "Wurftrank der Vergiftung", "item.minecraft.splash_potion.effect.regeneration": "Wurftrank der Regeneration", "item.minecraft.splash_potion.effect.slow_falling": "Wurftrank des sanften Falls", "item.minecraft.splash_potion.effect.slowness": "Wurftrank der Langsamkeit", "item.minecraft.splash_potion.effect.strength": "Wurftrank der Stärke", "item.minecraft.splash_potion.effect.swiftness": "Wurftrank der Schnelligkeit", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Wurftrank des Schildkrötenmeisters", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "Wurftrank der Unterwasseratmung", "item.minecraft.splash_potion.effect.weakness": "Wurftrank der Schwäche", "item.minecraft.splash_potion.effect.weaving": "Wurftrank des Webens", "item.minecraft.splash_potion.effect.wind_charged": "Wurftrank der Windladung", "item.minecraft.spruce_boat": "Fichtenholzboot", "item.minecraft.spruce_chest_boat": "Fichtenholztruhenboot", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Tintenfisch-Spawn-Ei", "item.minecraft.stick": "Stock", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "Steinspitz<PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "Steinschwert", "item.minecraft.stray_spawn_egg": "Eiswanderer-Spawn-Ei", "item.minecraft.strider_spawn_egg": "Schreiter-Spawn-Ei", "item.minecraft.string": "Faden", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "Süßbeeren", "item.minecraft.tadpole_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tadpole_spawn_egg": "Kaulquappen-Spawn-Ei", "item.minecraft.tide_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.tide_armor_trim_smithing_template.new": "Gezeiten-Rüstungsbesatz", "item.minecraft.tipped_arrow": "Getränkter Pfeil", "item.minecraft.tipped_arrow.effect.awkward": "Getränkter Pfeil", "item.minecraft.tipped_arrow.effect.empty": "Nicht herstellbarer getränkter Pfeil", "item.minecraft.tipped_arrow.effect.fire_resistance": "Pfeil der Feuerresistenz", "item.minecraft.tipped_arrow.effect.harming": "Pfeil des Schadens", "item.minecraft.tipped_arrow.effect.healing": "Pfeil der Heilung", "item.minecraft.tipped_arrow.effect.infested": "Pfeil des Befalls", "item.minecraft.tipped_arrow.effect.invisibility": "Pfeil der Unsichtbarkeit", "item.minecraft.tipped_arrow.effect.leaping": "Pfeil der Sprungkraft", "item.minecraft.tipped_arrow.effect.levitation": "Pfeil der Schwebekraft", "item.minecraft.tipped_arrow.effect.luck": "Pfeil des Glücks", "item.minecraft.tipped_arrow.effect.mundane": "Getränkter Pfeil", "item.minecraft.tipped_arrow.effect.night_vision": "Pfeil der Nachtsicht", "item.minecraft.tipped_arrow.effect.oozing": "Pfeil des Schleimens", "item.minecraft.tipped_arrow.effect.poison": "Pfeil der Vergiftung", "item.minecraft.tipped_arrow.effect.regeneration": "Pfeil der Regeneration", "item.minecraft.tipped_arrow.effect.slow_falling": "Pfeil des sanften Falls", "item.minecraft.tipped_arrow.effect.slowness": "Pfeil der Langsamkeit", "item.minecraft.tipped_arrow.effect.strength": "Pfeil der Stärke", "item.minecraft.tipped_arrow.effect.swiftness": "Pfeil der Schnelligkeit", "item.minecraft.tipped_arrow.effect.thick": "Getränkter Pfeil", "item.minecraft.tipped_arrow.effect.turtle_master": "Pfeil des Schildkrötenmeisters", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "Pfeil der Unterwasseratmung", "item.minecraft.tipped_arrow.effect.weakness": "Pfeil der Schwäche", "item.minecraft.tipped_arrow.effect.weaving": "Pfeil des Webens", "item.minecraft.tipped_arrow.effect.wind_charged": "Pfeil der Windladung", "item.minecraft.tnt_minecart": "TNT-Lore", "item.minecraft.torchflower_seeds": "Fackelliliensamen", "item.minecraft.totem_of_undying": "Totem der Unsterblichkeit", "item.minecraft.trader_llama_spawn_egg": "Händlerlama-Spawn-Ei", "item.minecraft.trial_key": "Prüfungsschlüssel", "item.minecraft.trident": "Dreizack", "item.minecraft.tropical_fish": "Tropenfisch", "item.minecraft.tropical_fish_bucket": "Tropenfischeimer", "item.minecraft.tropical_fish_spawn_egg": "Tropenfisch-Spawn-Ei", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON>kr<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.turtle_scute": "Schildkröten-Hornschild", "item.minecraft.turtle_spawn_egg": "Schildkröten-Spawn-Ei", "item.minecraft.vex_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.vex_armor_trim_smithing_template.new": "Plagegeister-Rüstungsbesatz", "item.minecraft.vex_spawn_egg": "Plagegeister-Spawn-Ei", "item.minecraft.villager_spawn_egg": "Dorfbewohner-Spawn-Ei", "item.minecraft.vindicator_spawn_egg": "Diener-Spawn-Ei", "item.minecraft.wandering_trader_spawn_egg": "Fahrender-Händler-Spawn-Ei", "item.minecraft.ward_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.ward_armor_trim_smithing_template.new": "Warthof-Rüstungsbesatz", "item.minecraft.warden_spawn_egg": "Wärter-Spawn-Ei", "item.minecraft.warped_fungus_on_a_stick": "Wirrpilzrute", "item.minecraft.water_bucket": "Wassereimer", "item.minecraft.wayfinder_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wegfinder-Rüstungsbesatz", "item.minecraft.wheat": "<PERSON><PERSON>", "item.minecraft.wheat_seeds": "Weizenkörner", "item.minecraft.white_bundle": "Weißes Bündel", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "Weißes Geschirr", "item.minecraft.wild_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.wild_armor_trim_smithing_template.new": "Wildnis-Rüstungsbesatz", "item.minecraft.wind_charge": "Windkugel", "item.minecraft.witch_spawn_egg": "Hexen-Spawn-Ei", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON>-<PERSON><PERSON>ett-Spawn-Ei", "item.minecraft.wither_spawn_egg": "Wither-Spawn-Ei", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Wolfs-Spawn-Ei", "item.minecraft.wooden_axe": "Holzaxt", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "Holzspitzhacke", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "Holzschwert", "item.minecraft.writable_book": "<PERSON><PERSON> und Feder", "item.minecraft.written_book": "Beschriebenes Buch", "item.minecraft.yellow_bundle": "Gelbes Bündel", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON> Geschirr", "item.minecraft.zoglin_spawn_egg": "Zoglin-Spawn-Ei", "item.minecraft.zombie_horse_spawn_egg": "Zombiepferde-Spawn-Ei", "item.minecraft.zombie_spawn_egg": "Zombie-Spawn-Ei", "item.minecraft.zombie_villager_spawn_egg": "Zombiedorfbewohner-Spawn-Ei", "item.minecraft.zombified_piglin_spawn_egg": "Zombifizierter-Piglin-Spawn-Ei", "item.modifiers.any": "Ausgerüstet:", "item.modifiers.armor": "Angelegt:", "item.modifiers.body": "Angelegt:", "item.modifiers.chest": "<PERSON>:", "item.modifiers.feet": "An den Füßen:", "item.modifiers.hand": "Gehalten:", "item.modifiers.head": "<PERSON><PERSON> <PERSON>:", "item.modifiers.legs": "<PERSON> <PERSON> Be<PERSON>:", "item.modifiers.mainhand": "In der Haupthand:", "item.modifiers.offhand": "In der Zweithand:", "item.modifiers.saddle": "Gesattelt:", "item.nbt_tags": "NBT: %s Eigenschaft(en)", "item.op_block_warning.line1": "Achtung:", "item.op_block_warning.line2": "Die Benutzung dieses Gegenstandes könnte einen Befehl ausführen", "item.op_block_warning.line3": "<PERSON><PERSON> ben<PERSON>, wenn du den genauen Inhalt kennst!", "item.unbreakable": "Unzerstörbar", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "Farbige Blöcke", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "Nahrungsmittel & Tränke", "itemGroup.crafting": "Werkstoffe", "itemGroup.foodAndDrink": "Nahrungsmittel & Tränke", "itemGroup.functional": "Gebrauchsblöcke", "itemGroup.hotbar": "Gespeicherte Schnellzugriffsleisten", "itemGroup.ingredients": "Werkstoffe", "itemGroup.inventory": "Überlebensinventar", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Operatorhilfsmittel", "itemGroup.redstone": "Redstone", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "Spawn‐Eier", "itemGroup.tools": "Werkzeuge & Hilfsmittel", "item_modifier.unknown": "Unbekannter Gegenstandsmodifikator ‚%s‘", "jigsaw_block.final_state": "Wird zu:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Ausgerichtet", "jigsaw_block.joint.rollable": "Dr<PERSON><PERSON>", "jigsaw_block.joint_label": "Verbindung:", "jigsaw_block.keep_jigsaws": "Verbundblöcke behalten", "jigsaw_block.levels": "Ebenen: %s", "jigsaw_block.name": "Name:", "jigsaw_block.placement_priority": "Platzierpriorität:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON> sich dieser Verbundblock mit einem Stück verbindet, ist dies die Reihenfolge, in der das Stück auf Verbindungen zur Gesamtkonstruktion verarbeitet wird.\n\nStücke werden in absteigender Priorität verarbeitet, wobei Gleichstände durch die Einfügungsreihenfolge gelöst werden.", "jigsaw_block.pool": "Bezugsquelle:", "jigsaw_block.selection_priority": "Auswahlpriorität:", "jigsaw_block.selection_priority.tooltip": "Wenn das zugehörige Stück auf Verbindungen verarbeitet wird, ist dies die Reihenfolge, in der dieser Verbundblock versucht, sich mit seinem Zielstück zu verbinden.\n\nVerbünde werden in absteigender Priorität verarbeitet, wobei Gleichstände durch eine zufällige Reihenfolge gelöst werden.", "jigsaw_block.target": "Zielname:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (Spieluhr)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> – Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Fortschritte", "key.attack": "Angreifen/A<PERSON>uen", "key.back": "Rück<PERSON><PERSON><PERSON>", "key.categories.creative": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "key.categories.gameplay": "Spielmechanik", "key.categories.inventory": "Inventar", "key.categories.misc": "Verschiedenes", "key.categories.movement": "Bewegung", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "Spielsteuerung", "key.chat": "<PERSON><PERSON>", "key.command": "Befehlszeile öffnen", "key.drop": "<PERSON><PERSON><PERSON><PERSON> fallen lassen", "key.forward": "Vorwärts", "key.fullscreen": "Vollbild wechseln", "key.hotbar.1": "Schnellzugriff 1", "key.hotbar.2": "Schnellzugriff 2", "key.hotbar.3": "Schnellzugriff 3", "key.hotbar.4": "Schnellzugriff 4", "key.hotbar.5": "Schnellzugriff 5", "key.hotbar.6": "Schnellzugriff 6", "key.hotbar.7": "Schnellzugriff 7", "key.hotbar.8": "Schnellzugriff 8", "key.hotbar.9": "Schnellzugriff 9", "key.inventory": "Inventar öffnen/schließen", "key.jump": "Springen", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Rücktaste", "key.keyboard.caps.lock": "Feststelltaste", "key.keyboard.comma": ",", "key.keyboard.delete": "Entf", "key.keyboard.down": "Unten", "key.keyboard.end": "<PERSON><PERSON>", "key.keyboard.enter": "Eingabe", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Pos 1", "key.keyboard.insert": "Einfg", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "<PERSON><PERSON> ,", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "Links", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Strg links", "key.keyboard.left.shift": "Umschalt links", "key.keyboard.left.win": "Meta links", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Bild ab", "key.keyboard.page.up": "Bild auf", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Druck", "key.keyboard.right": "<PERSON><PERSON><PERSON>", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON>rg rechts", "key.keyboard.right.shift": "Umschalt rechts", "key.keyboard.right.win": "<PERSON><PERSON> rechts", "key.keyboard.scroll.lock": "<PERSON><PERSON>", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON> belegt", "key.keyboard.up": "<PERSON><PERSON>", "key.keyboard.world.1": "Welt 1", "key.keyboard.world.2": "Welt 2", "key.left": "Links", "key.loadToolbarActivator": "Schnellzugriffsleiste laden", "key.mouse": "Maustaste %1$s", "key.mouse.left": "Linksklick", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "Rechtsklick", "key.pickItem": "Block auswählen", "key.playerlist": "S<PERSON><PERSON> auflisten", "key.quickActions": "Schnellaktionen", "key.right": "<PERSON><PERSON><PERSON>", "key.saveToolbarActivator": "Schnellzugriffsleiste speichern", "key.screenshot": "Screenshot aufnehmen", "key.smoothCamera": "Kameraverhalten wechseln", "key.sneak": "Schleichen", "key.socialInteractions": "Fenster für soziale Interaktionen", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> her<PERSON> (Zuschauer)", "key.sprint": "Sprinten", "key.swapOffhand": "Gegenstand mit Zweithand tauschen", "key.togglePerspective": "Perspektive wechseln", "key.use": "Benutzen/Platzieren", "known_server_link.announcements": "Ankündigungen", "known_server_link.community": "Gemeinschaft", "known_server_link.community_guidelines": "Gemeinschaftsrichtlinien", "known_server_link.feedback": "Rückmeldung", "known_server_link.forums": "<PERSON>en", "known_server_link.news": "Neuigkeiten", "known_server_link.report_bug": "Fehler am Server melden", "known_server_link.status": "Status", "known_server_link.support": "Hilfe/Unterstützung", "known_server_link.website": "Website", "lanServer.otherPlayers": "Einstellungen für andere Spieler", "lanServer.port": "Portnummer", "lanServer.port.invalid": "<PERSON>in gültiger Port.\nLass das Eingabefeld leer oder gib eine Nummer zwischen 1024 und 65535 ein.", "lanServer.port.invalid.new": "Kein gültiger Port.\nLass das Eingabefeld leer oder gib eine Nummer zwischen %s und %s ein.", "lanServer.port.unavailable": "Port nicht verfügbar.\nLass das Eingabefeld leer oder gib eine andere Nummer zwischen 1024 und 65535 ein.", "lanServer.port.unavailable.new": "Port nicht verfügbar.\nLass das Eingabefeld leer oder gib eine andere Nummer zwischen %s und %s ein.", "lanServer.scanning": "Suche nach Spielen im lokalen Netzwerk", "lanServer.start": "LAN‐Welt starten", "lanServer.title": "LAN‐Welt", "language.code": "deu_<PERSON>", "language.name": "De<PERSON>ch", "language.region": "Deutschland", "lectern.take_book": "<PERSON>uch nehmen", "loading.progress": "%s %%", "mco.account.privacy.info": "Erfahre mehr über Mojang und Datenschutzgesetze", "mco.account.privacy.info.button": "Erfahre mehr über die DSGVO", "mco.account.privacy.information": "Mojang wendet bestimmte Verfahren an, um Kinder und deren Privatsphäre zu schützen; so werden das „Gesetz zum Schutz der Privatsphäre von Kindern im Internet“ (COPPA) und die Datenschutz‐Grundverordnung (DSGVO) eingehalten.\n\nMöglicherweise musst du die Zustimmung deiner Eltern e<PERSON>holen, bevor du auf dein Realms‐Konto zugreifen kannst.", "mco.account.privacyinfo": "Mojang wendet bestimmte Verfahren an, um Kinder und deren Privatsphäre zu schützen; so werden das „Gesetz zum Schutz der Privatsphäre von Kindern im Internet“ (COPPA) und die Datenschutz‐Grundverordnung (DSGVO) eingehalten.\n\nMöglicherweise musst du die Zustimmung deiner Eltern e<PERSON>holen, bevor du auf dein Realms‐Konto zugreifen kannst.\n\nWenn du ein älteres Minecraft‐Konto hast (du meldest dich mit deinem Benutzernamen an), musst du dieses in ein Mojang‐Konto umwandeln, um auf Realms zugreifen zu können.", "mco.account.update": "Konto aktualisieren", "mco.activity.noactivity": "Keine Aktivität in dem/den letzten %s Tag(en)", "mco.activity.title": "Spieleraktivität", "mco.backup.button.download": "Neueste herunt<PERSON>laden", "mco.backup.button.reset": "Welt zurücksetzen", "mco.backup.button.restore": "Wiederherstellen", "mco.backup.button.upload": "Welt hochladen", "mco.backup.changes.tooltip": "Änderungen", "mco.backup.entry": "Sicherung (%s)", "mco.backup.entry.description": "Beschreibung", "mco.backup.entry.enabledPack": "Aktivierte(s) Paket(e)", "mco.backup.entry.gameDifficulty": "Spielschwierigkeit", "mco.backup.entry.gameMode": "Spielm<PERSON>", "mco.backup.entry.gameServerVersion": "Spielserverversion", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Startwert", "mco.backup.entry.templateName": "<PERSON><PERSON><PERSON>nname", "mco.backup.entry.undefined": "Unbestimmte Änderung", "mco.backup.entry.uploaded": "Hochgeladen", "mco.backup.entry.worldType": "Welttyp", "mco.backup.generate.world": "Welt generieren", "mco.backup.info.title": "Änderungen gegenüber der letzten Sicherung", "mco.backup.narration": "Sicherung von %s", "mco.backup.nobackups": "Dieses Realm hat derzeit keine Sicherungen.", "mco.backup.restoring": "<PERSON><PERSON> <PERSON> wied<PERSON>hers<PERSON>len", "mco.backup.unknown": "UNBEKANNT", "mco.brokenworld.download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "Her<PERSON><PERSON>gelade<PERSON>", "mco.brokenworld.message.line1": "<PERSON>te setze sie zurück oder wähle eine andere Welt aus.", "mco.brokenworld.message.line2": "Du kannst die Welt auch für den Einzelspielermodus herunterladen.", "mco.brokenworld.minigame.title": "Dieses Minispiel wird nicht mehr unterstützt", "mco.brokenworld.nonowner.error": "Bitte warte auf das Zurücksetzen der Welt durch den Realm‐Besitzer", "mco.brokenworld.nonowner.title": "Diese Welt ist veraltet", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Z<PERSON>ücksetzen", "mco.brokenworld.title": "Deine aktuelle Welt wird nicht mehr unterstützt", "mco.client.incompatible.msg.line1": "De<PERSON> ist nicht mit Realms kompatibel.", "mco.client.incompatible.msg.line2": "Bitte nutze die neueste Version von Minecraft.", "mco.client.incompatible.msg.line3": "Realms ist nicht mit Entwicklungsversionen kompatibel.", "mco.client.incompatible.title": "Inkompatibler Client!", "mco.client.outdated.stable.version": "Die Version deines Clients (%s) ist nicht mit Realms kompatibel.\n\nBitte verwende die neueste Version von Minecraft.", "mco.client.unsupported.snapshot.version": "Die Version deines Clients (%s) ist nicht mit Realms kompatibel.\n\nRealms ist für diese Entwicklungsversion nicht verfügbar.", "mco.compatibility.downgrade": "Herabstufen", "mco.compatibility.downgrade.description": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s. Eine Welt herabzustufen, kann diese beschädigen – wir können nicht garantieren, dass sie geladen wird oder funktioniert.\n\nUnter „Sicherungen“ wird eine Sicherheitskopie deiner Welt gespeichert. Bitte setze sie im Bedarfsfall darauf zurück.", "mco.compatibility.incompatible.popup.title": "Inkompatible Version", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON> Welt, die du betreten willst, ist mit deiner aktuellen Version nicht kompatibel.", "mco.compatibility.incompatible.series.popup.message": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s.\n\nDiese Versionsstände sind nicht miteinander kompatibel. Um in der vorliegenden Version zu spielen, wird eine neue Welt benötigt.", "mco.compatibility.unverifiable.message": "Es konnte nicht festgestellt werden, in welcher Version diese Welt zuletzt gespielt wurde. Wenn du sie hoch‐ bzw. herabstufst, wird automatisch eine Sicherheitskopie erstellt und unter „Sicherungen“ gespeichert.", "mco.compatibility.unverifiable.title": "Kompatibilität nicht überprüfbar", "mco.compatibility.upgrade": "Hochstufen", "mco.compatibility.upgrade.description": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s.\n\nUnter „Sicherungen“ wird eine Sicherheitskopie deiner Welt gespeichert.\n\nBitte setze sie im Bedarfsfall darauf zurück.", "mco.compatibility.upgrade.friend.description": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s.\n\nUnter „Sicherungen“ wird eine Sicherheitskopie der Welt gespeichert.\n\nDer Besitzer des Realms kann die Welt im Bedarfsfall wiederherstellen.", "mco.compatibility.upgrade.title": "Möchtest du diese Welt wirklich hochstufen?", "mco.configure.current.minigame": "Aktuell", "mco.configure.world.activityfeed.disabled": "Spieleraktivitätsanzeige vorübergehend ausgeschaltet", "mco.configure.world.backup": "Sicherungen", "mco.configure.world.buttons.activity": "Spieleraktivität", "mco.configure.world.buttons.close": "Realm vorübergehend schließen", "mco.configure.world.buttons.delete": "Löschen", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Einstellungen", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON> einladen", "mco.configure.world.buttons.moreoptions": "Weitere Optionen", "mco.configure.world.buttons.newworld": "Neue Welt", "mco.configure.world.buttons.open": "<PERSON> wieder ö<PERSON>nen", "mco.configure.world.buttons.options": "Weltoptionen", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Region auswählen …", "mco.configure.world.buttons.resetworld": "Welt zurücksetzen", "mco.configure.world.buttons.save": "Speichern", "mco.configure.world.buttons.settings": "Einstellungen", "mco.configure.world.buttons.subscription": "Abonnement", "mco.configure.world.buttons.switchminigame": "Minispiel wechseln", "mco.configure.world.close.question.line1": "Du kannst dein Realm vorübergehend sch<PERSON>ßen, sodass nicht darauf gespielt werden kann, während du Änderungen vornimmst. Öffne es anschließend wieder, sobald du fertig bist.\n\nDein Realms‐Abonnement wird dadurch nicht gekündigt.", "mco.configure.world.close.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.configure.world.close.question.title": "Du musst Änderungen vornehmen, ohne dass es zu Störungen kommt?", "mco.configure.world.closing": "Das Realm wird vorübergehend geschlossen …", "mco.configure.world.commandBlocks": "Befehlsblöcke", "mco.configure.world.delete.button": "Realm löschen", "mco.configure.world.delete.question.line1": "<PERSON>in <PERSON> wird endgültig gelöscht", "mco.configure.world.delete.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.configure.world.description": "Beschreibung des Realms", "mco.configure.world.edit.slot.name": "Name der Welt", "mco.configure.world.edit.subscreen.adventuremap": "Einige Einstellungen sind deaktiviert, da deine aktuelle Welt eine Abenteuerwelt ist", "mco.configure.world.edit.subscreen.experience": "Einige Einstellungen sind deaktiviert, da deine aktuelle Welt eine Erfahrungswelt ist", "mco.configure.world.edit.subscreen.inspiration": "Einige Einstellungen sind deaktiviert, da deine aktuelle Welt eine Inspirationswelt ist", "mco.configure.world.forceGameMode": "Spielmodus erzwingen", "mco.configure.world.invite.narration": "Du hast %s neue Einladung(en)", "mco.configure.world.invite.profile.name": "Name", "mco.configure.world.invited": "Eingeladen", "mco.configure.world.invited.number": "Eingeladen (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Operator", "mco.configure.world.invites.remove.tooltip": "Entfernen", "mco.configure.world.leave.question.line1": "Wenn du dieses Realm verlässt, kannst du es nicht mehr betreten, bis du erneut eingeladen wirst", "mco.configure.world.leave.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.configure.world.loading": "Realm wird geladen", "mco.configure.world.location": "Ort", "mco.configure.world.minigame": "Aktuell: %s", "mco.configure.world.name": "Name des Realms", "mco.configure.world.opening": "Das Realm wird geöffnet …", "mco.configure.world.players.error": "Es existiert kein Spieler mit diesem Namen", "mco.configure.world.players.inviting": "<PERSON><PERSON>ler wird eingeladen …", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "Spie<PERSON> gegen Spieler (PvP)", "mco.configure.world.region_preference": "Bevorzugte Region", "mco.configure.world.region_preference.title": "Regionsauswahl", "mco.configure.world.reset.question.line1": "Deine Welt wird neu generiert und deine aktuelle Welt geht verloren.", "mco.configure.world.reset.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.configure.world.resourcepack.question": "Du benötigst ein benutzerdefiniertes Ressourcenpaket, um auf diesem Realm zu spielen.\n\nMöchtest du es herunterladen und damit spielen?", "mco.configure.world.resourcepack.question.line1": "Du benötigst ein benutzerdefiniertes Ressourcenpaket, um auf diesem Realm zu spielen.", "mco.configure.world.resourcepack.question.line2": "Möchtest du es herunterladen und damit spielen?", "mco.configure.world.restore.download.question.line1": "Die Welt wird heruntergeladen und zu deinen Einzelspielerwelten hinzugefügt.", "mco.configure.world.restore.download.question.line2": "Möchtest du fortfahren?", "mco.configure.world.restore.question.line1": "Deine Welt wird auf den Stand vom ‚%s‘ (%s) zurückgesetzt", "mco.configure.world.restore.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.configure.world.settings.expired": "Die Einstellungen eines abgelaufenen Realms können nicht bearbeitet werden", "mco.configure.world.settings.title": "Einstellungen", "mco.configure.world.slot": "Welt %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "<PERSON>in <PERSON> wird auf eine andere Welt gewechselt", "mco.configure.world.slot.switch.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.configure.world.slot.tooltip": "<PERSON>u Welt wechseln", "mco.configure.world.slot.tooltip.active": "Beitreten", "mco.configure.world.slot.tooltip.minigame": "Zu Minispiel wechseln", "mco.configure.world.spawnAnimals": "<PERSON><PERSON> erzeugen", "mco.configure.world.spawnMonsters": "<PERSON> erzeugen", "mco.configure.world.spawnNPCs": "Dorfbewohner erzeugen", "mco.configure.world.spawnProtection": "Einstiegsbereichschutz", "mco.configure.world.spawn_toggle.message": "Wenn du diese Option ausschaltest, werden alle vorhandenen Objekte dieses Ty<PERSON> entfernt", "mco.configure.world.spawn_toggle.message.npc": "<PERSON>n du diese Option ausschaltest, werden alle vorhandenen Objekte dieses <PERSON>, wie <PERSON><PERSON><PERSON>hner, entfernt", "mco.configure.world.spawn_toggle.title": "Achtung!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "Tag", "mco.configure.world.subscription.days": "Tage", "mco.configure.world.subscription.expired": "Abgelaufen", "mco.configure.world.subscription.extend": "Abonnement verlängern", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> als ein Tag", "mco.configure.world.subscription.month": "<PERSON><PERSON>", "mco.configure.world.subscription.months": "Monate", "mco.configure.world.subscription.recurring.daysleft": "Automatisch verlängert in", "mco.configure.world.subscription.recurring.info": "Änderungen an deinem Realms‐Abonnement, wie die Verlängerung der Laufzeit oder die Deaktivierung der wiederkehrenden Abrechnung, werden erst ab deinem nächsten Abrechnungstermin berücksichtigt.", "mco.configure.world.subscription.remaining.days": "%1$s Tag(e)", "mco.configure.world.subscription.remaining.months": "%1$s Monat(e)", "mco.configure.world.subscription.remaining.months.days": "%1$s Monat(e), %2$s Tag(e)", "mco.configure.world.subscription.start": "Begin<PERSON>", "mco.configure.world.subscription.tab": "Abonnement", "mco.configure.world.subscription.timeleft": "Verbleibende Zeit", "mco.configure.world.subscription.title": "<PERSON>in <PERSON>", "mco.configure.world.subscription.unknown": "Unbekannt", "mco.configure.world.switch.slot": "<PERSON>lt erzeugen", "mco.configure.world.switch.slot.subtitle": "Diese Welt ist leer; lege fest, wie sie erzeugt werden soll.", "mco.configure.world.title": "Realm einrichten:", "mco.configure.world.uninvite.player": "Bist du sicher, dass du %s wieder ausladen möchtest?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> du sicher, dass du diesen Spieler ausladen möchtest:", "mco.configure.worlds.title": "Welten", "mco.connect.authorizing": "Anmelden …", "mco.connect.connecting": "Verbindung mit dem Realm wird hergestellt …", "mco.connect.failed": "Verbindung mit dem Realm fehlgeschlagen", "mco.connect.region": "Serverregion: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "Erzeugen", "mco.create.world.error": "Du musst einen <PERSON>n e<PERSON>ben!", "mco.create.world.failed": "Welterstellung fehlgeschlagen!", "mco.create.world.reset.title": "Welt wird erzeugt …", "mco.create.world.skip": "Überspringen", "mco.create.world.subtitle": "<PERSON><PERSON> du m<PERSON>, wähle die Welt für dein neues Realm aus", "mco.create.world.wait": "Realm wird erzeugt …", "mco.download.cancelled": "Herunterladen abgebrochen", "mco.download.confirmation.line1": "Die Welt, die du herunterladen willst, ist größer als %s.", "mco.download.confirmation.line2": "Du wirst diese Welt nicht mehr auf dein Realm hochladen können", "mco.download.confirmation.oversized": "<PERSON> Welt, die du herunterladen willst, ist größer als %s.\n\nDu wirst diese Welt nicht mehr auf dein Realm hochladen können.", "mco.download.done": "Herunterladen abgeschlossen", "mco.download.downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.extracting": "Entpacken", "mco.download.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gescheitert", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON>laden wird vorbereitet", "mco.download.resourcePack.fail": "Herunterladen des Ressourcenpakets fehlgeschlagen!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s pro Sekunde", "mco.download.title": "Neueste Welt wird heruntergeladen", "mco.error.invalid.session.message": "<PERSON>te versuche, Minecraft und ggf. den Launcher neu zu starten", "mco.error.invalid.session.title": "Ungültige Sitzung", "mco.errorMessage.6001": "Veralteter Client", "mco.errorMessage.6002": "Nutzungsbedingungen nicht akzeptiert", "mco.errorMessage.6003": "<PERSON><PERSON><PERSON>lade<PERSON><PERSON> erre<PERSON>t", "mco.errorMessage.6004": "Hochladelimit erreicht", "mco.errorMessage.6005": "<PERSON>lt ges<PERSON>rt", "mco.errorMessage.6006": "Welt ist veraltet", "mco.errorMessage.6007": "<PERSON>utzer ist in zu vielen Realms", "mco.errorMessage.6008": "Ungültiger Realm‐Name", "mco.errorMessage.6009": "Ungültige Realm‐Beschreibung", "mco.errorMessage.connectionFailure": "Ein Fehler ist aufgetreten, bitte versuche es später erneut.", "mco.errorMessage.generic": "Ein Fehler ist aufgetreten: ", "mco.errorMessage.initialize.failed": "Realm‐Initialisierung fehlgeschlagen", "mco.errorMessage.noDetails": "<PERSON><PERSON>hlerbeschreibung vorhanden", "mco.errorMessage.realmsService": "Ein Fehler ist aufgetreten (%s):", "mco.errorMessage.realmsService.configurationError": "<PERSON>im <PERSON> der Weltoptionen ist ein unerwarteter Fehler aufgetreten", "mco.errorMessage.realmsService.connectivity": "Verbindung zu Realms konnte nicht hergestellt werden: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kompatible Version konnte nicht überprüft werden; erhaltene Antwort: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON><PERSON> wied<PERSON>n", "mco.errorMessage.serviceBusy": "Realms ist derzeit stark ausgelastet.\n<PERSON>te versuche, dich in ein paar <PERSON>uten erneut mit deinem Realm zu verbinden.", "mco.gui.button": "Schaltfläche", "mco.gui.ok": "OK", "mco.info": "Hinweis!", "mco.invited.player.narration": "%s wurde eingeladen", "mco.invites.button.accept": "<PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "Keine ausstehenden Einladungen!", "mco.invites.pending": "Neue Einladung(en)!", "mco.invites.title": "Ausstehende Einladungen", "mco.minigame.world.changeButton": "Anderes Minispiel auswählen", "mco.minigame.world.info.line1": "Dies ersetzt deine Welt vorübergehend mit einem Minispiel!", "mco.minigame.world.info.line2": "Du kannst später ohne Verluste zu deiner ursprünglichen Welt zurückkehren.", "mco.minigame.world.noSelection": "Bitte triff eine Auswahl", "mco.minigame.world.restore": "Beende Minispiel …", "mco.minigame.world.restore.question.line1": "Das Minispiel wird beendet und dein Realm wird wiederhergestellt.", "mco.minigame.world.restore.question.line2": "Bist du sicher, dass du das tun möchtest?", "mco.minigame.world.selected": "Ausgewähltes Minispiel:", "mco.minigame.world.slot.screen.title": "Welt wird gewechselt …", "mco.minigame.world.startButton": "Wechseln", "mco.minigame.world.starting.screen.title": "Starte Minispiel …", "mco.minigame.world.stopButton": "Minispiel beenden", "mco.minigame.world.switch.new": "Ein anderes Minispiel auswählen?", "mco.minigame.world.switch.title": "Minispiel wechseln", "mco.minigame.world.title": "Realm zu Minispiel wechseln", "mco.news": "Realms‐Neuigkeiten", "mco.notification.dismiss": "Schließen", "mco.notification.transferSubscription.buttonText": "Jetzt übertragen", "mco.notification.transferSubscription.message": "Java‐Realms‐Abonnements werden in den Microsoft Store verschoben. Lass dein Abonnement nicht auslaufen!\nJetzt übertragen und 30 Tage Realms kostenlos erhalten.\nGehe zum Profil auf minecraft.net, um dein Abonnement zu übertragen.", "mco.notification.visitUrl.buttonText.default": "<PERSON>", "mco.notification.visitUrl.message.default": "Bitte besuche den nachfolgenden Link", "mco.onlinePlayers": "Spieler online", "mco.play.button.realm.closed": "Realm ist geschlossen", "mco.question": "Frage", "mco.reset.world.adventure": "Abenteuerwelten", "mco.reset.world.experience": "Erfahrungswelten", "mco.reset.world.generate": "Neue Welt", "mco.reset.world.inspiration": "Inspirationswelten", "mco.reset.world.resetting.screen.title": "Welt wird zurückgesetzt …", "mco.reset.world.seed": "Startwert (optional)", "mco.reset.world.template": "Weltvorlagen", "mco.reset.world.title": "Welt zurücksetzen", "mco.reset.world.upload": "Welt hochladen", "mco.reset.world.warning": "Dies wird die aktuelle Welt deines Realms ersetzen", "mco.selectServer.buy": "<PERSON><PERSON>e ein Realm!", "mco.selectServer.close": "Schließen", "mco.selectServer.closed": "Deaktiviertes Realm", "mco.selectServer.closeserver": "Realm schließen", "mco.selectServer.configure": "Einrichten", "mco.selectServer.configureRealm": "Realm einrichten", "mco.selectServer.create": "Realm erstellen", "mco.selectServer.create.subtitle": "<PERSON><PERSON>hle die Welt für dein neues Realm aus", "mco.selectServer.expired": "Abgelaufenes Realm", "mco.selectServer.expiredList": "Dein Abo ist abgelaufen", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Abonnieren", "mco.selectServer.expiredTrial": "<PERSON>in Probeabo ist abgelaufen", "mco.selectServer.expires.day": "Lä<PERSON>t in einem Tag ab", "mco.selectServer.expires.days": "Läuft in %s Tagen ab", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON><PERSON> bald ab", "mco.selectServer.leave": "Realm verlassen", "mco.selectServer.loading": "Realm‐Auswahl wird geladen", "mco.selectServer.mapOnlySupportedForVersion": "Diese Welt wird in %s nicht unterstützt", "mco.selectServer.minigame": "Minispiel:", "mco.selectServer.minigameName": "Minispiel: %s", "mco.selectServer.minigameNotSupportedInVersion": "Dieses Minispiel kann nicht mit %s gespielt werden", "mco.selectServer.noRealms": "Du scheinst kein Realm zu haben. Füge ein Realm hinzu, um mit deinen Freunden zu spielen.", "mco.selectServer.note": "<PERSON><PERSON><PERSON><PERSON>:", "mco.selectServer.open": "Offenes Realm", "mco.selectServer.openserver": "Realm <PERSON>", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms ist ein sicherer und einfacher Weg, Minecraft online mit bis zu zehn Freunden gleichzeitig zu spielen. Es bietet zahlreiche Minispiele und viele exklusive Welten! Nur der Besitzer des Realms muss zahlen.", "mco.selectServer.purchase": "Realm hinzufügen", "mco.selectServer.trial": "<PERSON>bie<PERSON>’s aus!", "mco.selectServer.uninitialized": "Realm erstellen!", "mco.snapshot.createSnapshotPopup.text": "<PERSON> bi<PERSON> da<PERSON>, ein kostenloses Realm für Entwicklungsversionen zu erstellen, das mit deinem zahlungspflichtigen Realms‐Abonnement verknüpft wird. Dieses neue Entwicklungsversionen‐Realm kann so lange genutzt werden, wie das zahlungspflichtige Abonnement aktiv ist. An deinem bezahlten Realm ändert sich dadurch nichts.", "mco.snapshot.createSnapshotPopup.title": "Entwicklungsversionen‐Realm erstellen?", "mco.snapshot.creating": "Entwicklungsversionen‐Realm wird erstellt …", "mco.snapshot.description": "Verknüpft mit „%s“", "mco.snapshot.friendsRealm.downgrade": "Du <PERSON>ötigst die Version %s, um dieses Realm zu betreten.", "mco.snapshot.friendsRealm.upgrade": "%s muss das Realm hochstufen, bevor du in dieser Version darauf spielen kannst.", "mco.snapshot.paired": "Dieses Entwicklungsversionen‐Realm ist mit „%s“ verknüpft", "mco.snapshot.parent.tooltip": "Verwende die aktuelle Vollversion, um auf diesem Realm zu spielen.", "mco.snapshot.start": "Kostenloses Entwicklungsversionen‐Realm anlegen", "mco.snapshot.subscription.info": "Dies ist ein Realm für Entwicklungsversionen, das mit dem Abonnement deines Realms „%s“ verknüpft ist. E<PERSON> bleibt so lange aktiv, wie das damit verknüpfte Realm aktiv ist.", "mco.snapshot.tooltip": "Entwicklungsversionen‐Realms verschaffen dir einen Einblick in zukünftige Versionen von Minecraft, die möglicherweise neue Spielelemente und sonstige Änderungen enthalten.\n\nDeine normalen Realms kannst du in der aktuellen Vollversion abrufen.", "mco.snapshotRealmsPopup.message": "Realms gibt es nun auch in Entwicklungsversionen, beginnend mit 23w41a. Jedes Realms‐Abonnement wird um ein kostenloses Realm für Entwicklungsversionen ergänzt, das von deinem normalen Java‐Realm getrennt ist!", "mco.snapshotRealmsPopup.title": "Realms nun in Entwicklungsversionen verfügbar", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON> er<PERSON>", "mco.template.button.publisher": "Herausgeber", "mco.template.button.select": "Auswählen", "mco.template.button.trailer": "Vorschau", "mco.template.default.name": "Weltvorlage", "mco.template.info.tooltip": "Webseite des Herausgebers", "mco.template.name": "Vorlage", "mco.template.select.failure": "Die Liste mit den Inhalten dieser Kategorie konnte nicht abgerufen werden. \nBitte überprüfe deine Internetverbindung oder versuche es später erneut.", "mco.template.select.narrate.authors": "Autoren: %s", "mco.template.select.narrate.version": "Version %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, diese Inhaltskategorie scheint aktuell leer zu sein.\n<PERSON>te schaue demnächst noch einmal vorbei, oder erw<PERSON>ge,\nwenn du ein Ersteller bist, %s.", "mco.template.select.none.linkTitle": "selbst einen Inhalt einzureichen", "mco.template.title": "Weltvorlagen", "mco.template.title.minigame": "Minispiele", "mco.template.trailer.tooltip": "Weltvorschau", "mco.terms.buttons.agree": "Zustimmen", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "Ich akzeptiere die Minecraft Realms", "mco.terms.sentence.2": "Nutzungsbedingungen", "mco.terms.title": "Realms‐Nutzungsbedingungen", "mco.time.daysAgo": "vor %1$s Tag(en)", "mco.time.hoursAgo": "vor %1$s Stunde(n)", "mco.time.minutesAgo": "vor %1$s Minute(n)", "mco.time.now": "gerade eben", "mco.time.secondsAgo": "vor %1$s Sekunde(n)", "mco.trial.message.line1": "Möchtest du dein eigenes Realm?", "mco.trial.message.line2": "<PERSON>licke hier für mehr Informationen!", "mco.upload.button.name": "Hochladen", "mco.upload.cancelled": "Hochladen abgebrochen", "mco.upload.close.failure": "<PERSON>in Realm konnte nicht geschlossen werden, bitte versuche es später erneut", "mco.upload.done": "Hochladen abgeschlossen", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Hochladen gescheitert! (%s)", "mco.upload.failed.too_big.description": "Die ausgewählte Welt ist zu groß. Die maximal zulässige Größe beträgt %s.", "mco.upload.failed.too_big.title": "<PERSON>lt zu groß", "mco.upload.hardcore": "Welten im Hardcore‐Modus können nicht hochgeladen werden!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Bereite deine Weltdaten vor", "mco.upload.select.world.none": "<PERSON><PERSON> Einzelspielerwelten gefunden!", "mco.upload.select.world.subtitle": "Bitte wähle eine Einzelspielerwelt zum Hochladen", "mco.upload.select.world.title": "Welt hochladen", "mco.upload.size.failure.line1": "‚%s‘ ist zu groß!", "mco.upload.size.failure.line2": "Es ist %s. Die maximal zulässige Größe ist %s.", "mco.upload.uploading": "Lade ‚%s‘ hoch", "mco.upload.verifying": "Überprüfe deine Welt", "mco.version": "Version: %s", "mco.warning": "Achtung!", "mco.worldSlot.minigame": "Minispiel", "menu.custom_options": "Benutzerdefinierte Optionen …", "menu.custom_options.title": "Benutzerdefinierte Optionen", "menu.custom_options.tooltip": "Hin<PERSON>s: Benutzerdefinierte Optionen werden von Servern und/oder Inhalten Dritter bereitgestellt.\nSei vorsichtig!", "menu.custom_screen_info.button_narration": "Dies ist ein benutzerdefiniertes Menü. Mehr erfahren.", "menu.custom_screen_info.contents": "Die Inhalte dieses Fensters werden von <PERSON> und Karten Dritter definiert, die Mojang Studios oder Microsoft weder gehören, noch von ihnen betrieben oder überwacht werden.\n\nSei vorsichtig! Lasse immer Vorsicht walten, wenn du Internetadressen folgst, und verrate niemals deine persönlichen Daten (wie deine Anmeldedaten).\n\nWenn dieses Fenster dich am Spielen hindert, kannst du deine Verbindung zum aktuellen Server auch über die untenstehende Schaltfläche trennen.", "menu.custom_screen_info.disconnect": "Benutzerdefiniertes Menü abgelehnt", "menu.custom_screen_info.title": "<PERSON><PERSON><PERSON><PERSON> zu benutzerdefinierten Menü<PERSON>", "menu.custom_screen_info.tooltip": "Dies ist ein benutzerdefiniertes Menü. <PERSON><PERSON><PERSON> hier, um mehr zu erfahren.", "menu.disconnect": "Verbindung trennen", "menu.feedback": "Rückmeldung …", "menu.feedback.title": "Rückmeldung", "menu.game": "Spielmenü", "menu.modded": " (modifiziert)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Optionen …", "menu.paused": "Spiel angehalten", "menu.playdemo": "Demowelt spielen", "menu.playerReporting": "<PERSON><PERSON><PERSON> melden", "menu.preparingSpawn": "Einstiegsbereich wird vorbereitet: %s %%", "menu.quick_actions": "Schnellaktionen …", "menu.quick_actions.title": "Schnellaktionen", "menu.quit": "Spiel beenden", "menu.reportBugs": "<PERSON><PERSON> melden", "menu.resetdemo": "Demowelt zurücksetzen", "menu.returnToGame": "Zurück zum Spiel", "menu.returnToMenu": "Speichern und zurück zum Hauptmenü", "menu.savingChunks": "Chunks werden gespeichert", "menu.savingLevel": "Welt wird gespeichert", "menu.sendFeedback": "Feedback geben", "menu.server_links": "Server‐Links …", "menu.server_links.title": "Server‐Links", "menu.shareToLan": "Im <PERSON>", "menu.singleplayer": "Einzelspieler", "menu.working": "Wird bearbei<PERSON>t …", "merchant.deprecated": "Dorfbewohner füllen ihren Warenbestand bis zu zweimal am Tag auf.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON>", "merchant.level.4": "Experte", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s – %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Drücke %1$s, um abzusteigen", "multiplayer.applyingPack": "Ressourcenpaket wird angewandt", "multiplayer.confirm_command.parse_errors": "<PERSON>, einen unbekannten oder ungültigen Befehl auszuführen.\nBist du sicher?\nBefehl: %s", "multiplayer.confirm_command.permissions_required": "<PERSON>, einen <PERSON>, der ein erhöhtes Berechtigungslevel erfordert.\nDies könnte sich negativ auf dein Spiel auswirken.\nBist du sicher?\nBefehl: %s", "multiplayer.confirm_command.title": "Befehlsausführung bestätigen", "multiplayer.disconnect.authservers_down": "Die Authentifizierungsserver sind derzeit nicht erreichbar. Bitte versuche es später erneut!", "multiplayer.disconnect.bad_chat_index": "Verpasste oder umgeordnete Chatnachricht vom Server erkannt", "multiplayer.disconnect.banned": "Du wurdest auf diesem Server ges<PERSON>rt", "multiplayer.disconnect.banned.expiration": "\nDeine <PERSON> wird am %s aufgehoben", "multiplayer.disconnect.banned.reason": "Du wurdest auf diesem Server gesperrt. \nGrund: %s", "multiplayer.disconnect.banned_ip.expiration": "\nDeine <PERSON> wird am %s aufgehoben", "multiplayer.disconnect.banned_ip.reason": "Deine IP‐Adresse wurde auf diesem Server gesperrt. \nGrund: %s", "multiplayer.disconnect.chat_validation_failed": "Fehler bei der Überprüfung der Chatnachricht", "multiplayer.disconnect.duplicate_login": "Du hast dich von einem anderen Standort aus angemeldet", "multiplayer.disconnect.expired_public_key": "Der öffentliche Profilschlüssel ist abgelaufen. Überprüfe, ob deine Systemzeit synchronisiert ist, und versuche, dein Spiel neu zu starten.", "multiplayer.disconnect.flying": "Fliegen ist auf diesem Server nicht erlaubt", "multiplayer.disconnect.generic": "Verbindung getrennt", "multiplayer.disconnect.idling": "Du warst zu lange untätig!", "multiplayer.disconnect.illegal_characters": "Unzulässige Zeichen im Chat", "multiplayer.disconnect.incompatible": "Inkompatibler Client! Verwende bitte %s", "multiplayer.disconnect.invalid_entity_attacked": "<PERSON>s wurde versucht, ein ungültiges Objekt anzugreifen", "multiplayer.disconnect.invalid_packet": "Der Server hat ein ungültiges Paket gesendet", "multiplayer.disconnect.invalid_player_data": "Ungültige Spielerdaten", "multiplayer.disconnect.invalid_player_movement": "Ungültiges Paket zur Spielerbewegung empfangen", "multiplayer.disconnect.invalid_public_key_signature": "Ungültige Signatur für den öffentlichen Profilschlüssel.\nVersuche, dein Spiel neu zu starten.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ungültige Signatur für den öffentlichen Profilschlüssel.\nVersuche, dein Spiel neu zu starten.", "multiplayer.disconnect.invalid_vehicle_movement": "Ungültiges Paket zur Fahrmittelbewegung empfangen", "multiplayer.disconnect.ip_banned": "Deine IP‐Adresse wurde auf diesem Server gesperrt", "multiplayer.disconnect.kicked": "Von einem Operator hinausgeworfen", "multiplayer.disconnect.missing_tags": "Unvollständiger Etikettensatz vom Server empfangen.\nBitte benachrichtige den Serverbetreiber.", "multiplayer.disconnect.name_taken": "Dieser Name wird bereits verwendet", "multiplayer.disconnect.not_whitelisted": "Du stehst nicht auf der Gästeliste dieses Servers!", "multiplayer.disconnect.out_of_order_chat": "Unerwartetes Chatpaket empfangen. Hat sich deine Systemzeit geändert?", "multiplayer.disconnect.outdated_client": "Inkompatibler Client! Verwende bitte %s", "multiplayer.disconnect.outdated_server": "Inkompatibler Client! Verwende bitte %s", "multiplayer.disconnect.server_full": "Der Server ist voll!", "multiplayer.disconnect.server_shutdown": "Server geschlossen", "multiplayer.disconnect.slow_login": "Der Anmeldevorgang hat zu lange gedauert", "multiplayer.disconnect.too_many_pending_chats": "Zu viele unbestätigte Chatnachrichten", "multiplayer.disconnect.transfers_disabled": "Der Server nimmt keine Übertragungen entgegen.", "multiplayer.disconnect.unexpected_query_response": "Unerwartete benutzerdefinierte Daten vom Client empfangen", "multiplayer.disconnect.unsigned_chat": "Chatpaket mit fehlender oder ungültiger Signatur empfangen.", "multiplayer.disconnect.unverified_username": "Benutzername konnte nicht überprüft werden!", "multiplayer.downloadingStats": "Statistiken werden abgerufen …", "multiplayer.downloadingTerrain": "Landschaft wird geladen …", "multiplayer.lan.server_found": "Neuen Server gefunden: %s", "multiplayer.message_not_delivered": "Chatnachricht konnte nicht zugestellt werden, bitte Protokolldateien des Servers prüfen: %s", "multiplayer.player.joined": "%s hat das Spiel betreten", "multiplayer.player.joined.renamed": "%s (früher bekannt als %s) hat das Spiel betreten", "multiplayer.player.left": "%s hat das Spiel verlassen", "multiplayer.player.list.hp": "%s LP", "multiplayer.player.list.narration": "Spieler online: %s", "multiplayer.requiredTexturePrompt.disconnect": "Der Server erfordert ein benutzerdefiniertes Ressourcenpaket", "multiplayer.requiredTexturePrompt.line1": "Dieser Server erfordert die Verwendung eines benutzerdefinierten Ressourcenpakets.", "multiplayer.requiredTexturePrompt.line2": "<PERSON>n du das Ressourcenpaket ablehnst, wird deine Verbindung mit dem Server getrennt.", "multiplayer.socialInteractions.not_available": "Soziale Interaktionen sind nur in Mehrspielerwelten verfügbar", "multiplayer.status.and_more": "… und %s weitere …", "multiplayer.status.cancelled": "Abgebrochen", "multiplayer.status.cannot_connect": "Die Verbindung zum Server kann nicht hergestellt werden", "multiplayer.status.cannot_resolve": "Die Serveradresse kann nicht aufgelöst werden", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Inkompatible Version!", "multiplayer.status.motd.narration": "Nachricht des Tages: %s", "multiplayer.status.no_connection": "(keine Verbindu<PERSON>)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping: %s Millisekunden", "multiplayer.status.pinging": "Abfragen …", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s von %s <PERSON><PERSON><PERSON>n online", "multiplayer.status.quitting": "Wird beendet", "multiplayer.status.request_handled": "Statusan<PERSON>ge wurde bearbeitet", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Status ohne Anfrage erhalten", "multiplayer.status.version.narration": "Server‐Version: %s", "multiplayer.stopSleeping": "Aufstehen", "multiplayer.texturePrompt.failure.line1": "Das Ressourcenpaket des Servers konnte nicht angewandt werden", "multiplayer.texturePrompt.failure.line2": "Mechan<PERSON>en, die benutzerdefinierte Ressourcen erfordern, funktionieren möglicherweise nicht wie erwartet", "multiplayer.texturePrompt.line1": "Dieser Server empfiehlt die Verwendung eines benutzerdefinierten Ressourcenpakets.", "multiplayer.texturePrompt.line2": "Möchtest du es automagisch herunterladen und installieren?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nNachricht vom Server:\n%s", "multiplayer.title": "Mehrspielermodus spielen", "multiplayer.unsecureserver.toast": "Die auf diesem Server gesendeten Nachrichten können verändert werden und entsprechen möglicherweise nicht der ursprünglichen Nachricht.", "multiplayer.unsecureserver.toast.title": "Chatnachrichten sind nicht überprüfbar", "multiplayerWarning.check": "<PERSON><PERSON> Hinwei<PERSON> nicht mehr anzeigen", "multiplayerWarning.header": "Vorsicht: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Dr<PERSON>tern", "multiplayerWarning.message": "Vorsicht: Der Mehrspielermodus wird über Drittanbieter‐Server angeboten, die Mojang Studios oder Microsoft weder gehören, noch von diesen betrieben oder überwacht werden. Bei der Verwendung des Mehrspielermodus könntest du unmoderierten Chatnachrichten oder sonstigen nutzergenerierten Inhalten ausgesetzt sein, die womöglich nicht für jeden angemessen sind.", "music.game.a_familiar_room": "<PERSON> – A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> – An Ordinary Day", "music.game.ancestry": "<PERSON> Ancestry", "music.game.below_and_above": "<PERSON> – Below and Above", "music.game.broken_clocks": "<PERSON> – Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> – Comforting Memories", "music.game.creative.aria_math": "C418 – Aria Math", "music.game.creative.biome_fest": "C418 – Biome Fest", "music.game.creative.blind_spots": "C418 – Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 – Taswell", "music.game.crescent_dunes": "<PERSON> – Crescent Dunes", "music.game.danny": "C418 – <PERSON>", "music.game.deeper": "<PERSON> – <PERSON>er", "music.game.dry_hands": "C418 – Dry Hands", "music.game.echo_in_the_wind": "<PERSON> – Echo in the Wind", "music.game.eld_unknown": "<PERSON> – <PERSON><PERSON>", "music.game.end.alpha": "C418 – Alpha", "music.game.end.boss": "C418 – <PERSON>", "music.game.end.the_end": "C418 – The End", "music.game.endless": "<PERSON> – End<PERSON>", "music.game.featherfall": "<PERSON> – Featherfall", "music.game.fireflies": "<PERSON> – Fireflies", "music.game.floating_dream": "<PERSON><PERSON> – Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> – Infinite Amethyst", "music.game.key": "C418 – Key", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> – Left to Bloom", "music.game.lilypad": "<PERSON> – Lily<PERSON>", "music.game.living_mice": "C418 – <PERSON> Mice", "music.game.mice_on_venus": "C418 – <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 – Mine<PERSON>", "music.game.nether.ballad_of_the_cats": "C418 – Ballad of the Cats", "music.game.nether.concrete_halls": "C418 – Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 – <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> – <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> – So Below", "music.game.nether.warmth": "C418 – Warmth", "music.game.one_more_day": "<PERSON> – One More Day", "music.game.os_piano": "<PERSON> – <PERSON>’s Piano", "music.game.oxygene": "C418 – Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> Puzzlebox", "music.game.stand_tall": "<PERSON> – Stand Tall", "music.game.subwoofer_lullaby": "C418 – Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> – Firebugs", "music.game.swamp.labyrinthine": "<PERSON> – Labyrinthine", "music.game.sweden": "C418 – Sweden", "music.game.watcher": "<PERSON> – Watcher", "music.game.water.axolotl": "C418 – Axolotl", "music.game.water.dragon_fish": "C418 – <PERSON> Fish", "music.game.water.shuniji": "C418 – <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON> – <PERSON>", "music.game.wet_hands": "C418 – <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> yakusoku", "music.menu.beginning_2": "C418 – Beginning 2", "music.menu.floating_trees": "C418 – Floating Trees", "music.menu.moog_city_2": "C418 – Moog City 2", "music.menu.mutation": "C418 – Mutation", "narration.button": "Schaltfläche: %s", "narration.button.usage.focused": "Drücke die Eingabetaste zum Aktivieren", "narration.button.usage.hovered": "Linksklick zum Aktivieren", "narration.checkbox": "Kontrollkästchen: %s", "narration.checkbox.usage.focused": "Drücke die Eingabetaste zum Umschalten", "narration.checkbox.usage.hovered": "Linksklick zum Umschalten", "narration.component_list.usage": "<PERSON>ücke die Tabulatortaste, um zum nächsten Element zu wechseln", "narration.cycle_button.usage.focused": "Drücke die Eingabetaste, um zu %s zu wechseln", "narration.cycle_button.usage.hovered": "<PERSON><PERSON><PERSON>, um zu %s zu wechseln", "narration.edit_box": "Eingabefeld: %s", "narration.item": "Gegenstand: %s", "narration.recipe": "Rezept für %s", "narration.recipe.usage": "Linksklick zum Auswählen", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON><PERSON>, um mehr Rezepte anzuzeigen", "narration.selection.usage": "Drücke die obere oder untere Pfeiltaste, um zu einem anderen Eintrag zu wechseln", "narration.slider.usage.focused": "<PERSON>ücke die linke oder rechte Pfeiltaste, um den Wert zu ändern", "narration.slider.usage.hovered": "<PERSON><PERSON><PERSON> den Schieberegler, um den Wert zu ändern", "narration.suggestion": "Vorschlag %s von %s ausgewählt: %s", "narration.suggestion.tooltip": "Vorschlag %s von %s ausgewählt: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON>ücke die Tabulatortaste, um zum nächsten Vorschlag zu blättern", "narration.suggestion.usage.cycle.hidable": "<PERSON><PERSON>e die Tabulatortaste, um zum nächsten Vorschlag zu blättern, oder Escape, um die Vorschläge zu verlassen", "narration.suggestion.usage.fill.fixed": "Drücke die Tabulatortaste, um den Vorschlag zu verwenden", "narration.suggestion.usage.fill.hidable": "<PERSON><PERSON>e die Tabulatortaste, um den Vorschlag zu verwenden, oder Escape, um die Vorschläge zu verlassen", "narration.tab_navigation.usage": "Drücke Steuerung und die Tabulatortaste, um zwischen den Reitern zu wechseln", "narrator.button.accessibility": "Barrierefreiheit", "narrator.button.difficulty_lock": "Schwierigkeitssperre", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Entsperrt", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s ist auf %s gesetzt", "narrator.controls.reset": "%s auf Standard zurücksetzen", "narrator.controls.unbound": "%s ist nicht zugewiesen", "narrator.joining": "Beitreten", "narrator.loading": "Wird geladen: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Listenzeile %s von %s ausgewählt", "narrator.position.object_list": "Zeilenelement %s von %s ausgewählt", "narrator.position.screen": "Bildschirmelement %s von %s", "narrator.position.tab": "Reiter %s von %s ausgewählt", "narrator.ready_to_play": "Be<PERSON><PERSON> zu spielen", "narrator.screen.title": "Hauptmenü", "narrator.screen.usage": "Verwende den Mauszeiger oder die Tabulatortaste, um ein Element auszuwählen", "narrator.select": "Ausgewählt: %s", "narrator.select.world": "%s, zuletzt gespielt: %s, %s, %s, Version: %s", "narrator.select.world_info": "%s, zuletzt gespielt: %s, %s", "narrator.toast.disabled": "Sprachausgabe deaktiviert", "narrator.toast.enabled": "Sprachausgabe aktiviert", "optimizeWorld.confirm.description": "Dies wird deine Welt durch Speichern der Daten im neuesten Format optimieren. Dies kann eine sehr lange Zeit dauern, abh<PERSON><PERSON><PERSON> von der Größe deiner Welt. <PERSON><PERSON> durchgeführt, kann deine Welt im Spiel schneller sein, kann jedoch nicht mehr mit älteren Versionen des Spiels geladen werden. <PERSON><PERSON> du sicher, dass du fortfahren möchtest?", "optimizeWorld.confirm.proceed": "Kopie erstellen und optimieren", "optimizeWorld.confirm.title": "Welt optimieren", "optimizeWorld.info.converted": "Aktualisierte Chunks: %s", "optimizeWorld.info.skipped": "Übersprungene Chunks: %s", "optimizeWorld.info.total": "Gesamte Chunks: %s", "optimizeWorld.progress.counter": "%s/%s", "optimizeWorld.progress.percentage": "%s %%", "optimizeWorld.stage.counting": "Chunks werden gezählt …", "optimizeWorld.stage.failed": "Fehlgeschlagen! :(", "optimizeWorld.stage.finished": "Fertigstellen …", "optimizeWorld.stage.finished.chunks": "Chunk‐Aktualisierung wird abgeschlossen …", "optimizeWorld.stage.finished.entities": "Objektaktualisierung wird abgeschlossen …", "optimizeWorld.stage.finished.poi": "Zielpunktaktualisierung wird abgeschlossen …", "optimizeWorld.stage.upgrading": "Alle Chunks werden aktualisiert …", "optimizeWorld.stage.upgrading.chunks": "Alle Chunks werden aktualisiert …", "optimizeWorld.stage.upgrading.entities": "Alle Objekte werden aktualisiert …", "optimizeWorld.stage.upgrading.poi": "Alle Zielpunkte werden aktualisiert …", "optimizeWorld.title": "Welt ‚%s‘ wird optimiert", "options.accessibility": "Barrierefreiheit …", "options.accessibility.high_contrast": "<PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "Das Ressourcenpaket für hohen Kontrast ist nicht verfügbar.", "options.accessibility.high_contrast.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Kontrast von Elementen der Benutzeroberfläche.", "options.accessibility.high_contrast_block_outline": "Kontras<PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "<PERSON>rhöht den Kontrast der Umrisslinien des angezielten Blocks.", "options.accessibility.link": "Barrierefreiheitsleitfaden", "options.accessibility.menu_background_blurriness": "Menühintergrund‐Unschärfe", "options.accessibility.menu_background_blurriness.tooltip": "Steuert die Weichzeichnung der Menühintergründe.", "options.accessibility.narrator_hotkey": "Sprachausgaben‐Kurzanwahl", "options.accessibility.narrator_hotkey.mac.tooltip": "Ermöglicht es, die Sprachausgabe mit [⌘] + [B] ein‐ und auszuschalten.", "options.accessibility.narrator_hotkey.tooltip": "Ermöglicht es, die Sprachausgabe mit [Strg] + [B] ein‐ und auszuschalten.", "options.accessibility.panorama_speed": "Panorama‐Bildlaufgeschwindigkeit", "options.accessibility.text_background": "Texthintergrund", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Überall", "options.accessibility.text_background_opacity": "Texthintergrund‐Deckkraft", "options.accessibility.title": "Barrierefreiheitseinstellungen", "options.allowServerListing": "Server‐Auflistung zulassen", "options.allowServerListing.tooltip": "Server können Online‐Spieler als Teil ihres öffentlichen Status auflisten.\nWenn diese Option deaktiviert ist, erscheint dein Name nicht in solchen Listen.", "options.ao": "Weiche Beleuchtung", "options.ao.max": "Maximum", "options.ao.min": "Minimum", "options.ao.off": "Aus", "options.attack.crosshair": "Fadenkreuz", "options.attack.hotbar": "Schnellzugr.", "options.attackIndicator": "Angriffsanzeige", "options.audioDevice": "G<PERSON><PERSON>", "options.audioDevice.default": "Systemstandard", "options.autoJump": "Automatisch springen", "options.autoSuggestCommands": "Befehlsvorschläge", "options.autosaveIndicator": "Autospeichern‐Anzeige", "options.biomeBlendRadius": "Biomübergang", "options.biomeBlendRadius.1": "Aus (am schnellsten)", "options.biomeBlendRadius.11": "11 × 11 (extrem)", "options.biomeBlendRadius.13": "13 × 13 (protzig)", "options.biomeBlendRadius.15": "15 × 15 (maximal)", "options.biomeBlendRadius.3": "3 × 3 (s<PERSON>nell)", "options.biomeBlendRadius.5": "5 × 5 (normal)", "options.biomeBlendRadius.7": "7 × 7 (hoch)", "options.biomeBlendRadius.9": "9 × 9 (sehr hoch)", "options.chat": "Chateinstellungen …", "options.chat.color": "<PERSON><PERSON>", "options.chat.delay": "Chatverzögerung: %s Sekunde(n)", "options.chat.delay_none": "Chatverzögerung: <PERSON><PERSON>", "options.chat.height.focused": "Hö<PERSON> (geöffnet)", "options.chat.height.unfocused": "Höhe (geschlossen)", "options.chat.line_spacing": "Zeilenabstand", "options.chat.links": "Weblinks", "options.chat.links.prompt": "Links bestätigen lassen", "options.chat.opacity": "Chat‐Textdeckkraft", "options.chat.scale": "Chat‐Textgröße", "options.chat.title": "Chateinstellungen", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "Versteckt", "options.chat.visibility.system": "<PERSON><PERSON>", "options.chat.width": "Breite", "options.chunks": "%s Chunks", "options.clouds.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.clouds.fast": "<PERSON><PERSON><PERSON>", "options.controls": "Steuerung …", "options.credits_and_attribution": "Mitwirkende & Namensnennung …", "options.damageTiltStrength": "Schadensneigung", "options.damageTiltStrength.tooltip": "Die Stärke des Kamerawackelns, das beim Erleiden von Sc<PERSON>en auftritt.", "options.darkMojangStudiosBackgroundColor": "Schwarzweiß‐Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Ändert die Hintergrundfarbe des Ladebildschirms von Mojang Studios zu Schwarz.", "options.darknessEffectScale": "Pulsierende Dunkelheit", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON>, wie stark der Effekt „Dunkelheit“ pulsiert, wenn ein Wärter oder Sculk‐Kreischer ihn an dich weitergibt.", "options.difficulty": "Schwierigkeit", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Feindliche Kreaturen erscheinen, verursachen aber nur geringen Schaden. Die Hungerleiste leert sich und zehrt die Gesundheit bis auf fünf <PERSON> auf.", "options.difficulty.hard": "Schwer", "options.difficulty.hard.info": "Feindliche Kreaturen erscheinen und verursachen hohen Schaden. Die Hungerleiste leert sich und kann zum Hungertod führen.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Feindliche Kreaturen erscheinen und verursachen normalen Schaden. Die Hungerleiste leert sich und zehrt die Gesundheit bis auf ein halbes Herz auf.", "options.difficulty.online": "Server‐Schwierigkeit", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "<PERSON>ine feindlichen Kreaturen und nur manche neutrale Kreaturen erscheinen. Die Hungerleiste leert sich nicht und verlorene Gesundheit erholt sich mit der Zeit.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Klassischer Stereoklang", "options.directionalAudio.on.tooltip": "Verwendet HRTF‐basierten Raumklang, um die Simulation von 3‐D‐Ton zu verbessern. Erfordert HRTF‐kompatible Audiogeräte und lässt sich am besten mit Kopfhörern erleben.", "options.discrete_mouse_scroll": "Eigenständiges Blättern", "options.entityDistanceScaling": "Objektweite", "options.entityShadows": "Objektschatten", "options.font": "Schriftarteinstellungen …", "options.font.title": "Schriftarteinstellungen", "options.forceUnicodeFont": "Unicode‐Schriftart", "options.fov": "<PERSON><PERSON><PERSON>", "options.fov.max": "Quake‐Profi", "options.fov.min": "Normal", "options.fovEffectScale": "Sichtfeldeffekte", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON>, wie stark das Sichtfeld durch Spielmechanikeffekte verändert wird.", "options.framerate": "%s FPS", "options.framerateLimit": "<PERSON><PERSON>", "options.framerateLimit.max": "Unbegrenzt", "options.fullscreen": "Vollbild", "options.fullscreen.current": "Aktuell", "options.fullscreen.entry": "%s × %s (%s Hz, %s Bit)", "options.fullscreen.resolution": "Vollbildauflösung", "options.fullscreen.unavailable": "Einstellung nicht verfügbar", "options.gamma": "Helligkeit", "options.gamma.default": "Standard", "options.gamma.max": "Hell", "options.gamma.min": "<PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Schimmerbewegung", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON>, wie schnell sich der Schimmer verzauberter Gegenstände bewegt.", "options.glintStrength": "Schimmerstärke", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON>, wie deutlich der Schimmer verzauberter Gegenstände sichtbar ist.", "options.graphics": "<PERSON>ik<PERSON><PERSON>", "options.graphics.fabulous": "<PERSON><PERSON><PERSON>haft!", "options.graphics.fabulous.tooltip": "Der Grafikmodus „%s“ ver<PERSON><PERSON>r, um Wetter, Wolken und Partikel auch hinter lichtdurchlässigen Blöcken und Wasser darzustellen.\nDies kann die Leistung auf tragbaren Geräten und 4K‐Bildschirmen erheblich beeinträchtigen.", "options.graphics.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "Der Grafikmodus „Schön“ stellt auf den meisten Geräten einen Ausgleich zwischen Leistung und Qualität her.\n<PERSON><PERSON>, Wolken und Partikel können hinter lichtdurchlässigen Blöcken und Wasser nicht angezeigt werden.", "options.graphics.fast": "<PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Der Grafikmodus „Schnell“ reduziert die Menge an sichtbarem Regen und Schnee.\nTransparenzeffekte sind für verschiedene Blöcke wie Laub deaktiviert.", "options.graphics.warning.accept": "Ohne Unterstützung fortfahren", "options.graphics.warning.cancel": "Bring mich zurück", "options.graphics.warning.message": "<PERSON><PERSON> wurde er<PERSON>, dass deine Grafikkarte den Grafikmodus „%s“ nicht unterstützt.\n\nDu kannst diese Meldung ignorieren und fortfahren, aber für dein Gerät wird keine Unterstützung angeboten, wenn du dich entscheidest, den Grafikmodus „%s“ zu verwenden.", "options.graphics.warning.renderer": "Renderer erkannt: [%s]", "options.graphics.warning.title": "Grafikkarte wird nicht unterstützt", "options.graphics.warning.vendor": "Hersteller erkannt: [%s]", "options.graphics.warning.version": "OpenGL‐Version erkannt: [%s]", "options.guiScale": "GUI‐Größe", "options.guiScale.auto": "Automatisch", "options.hidden": "Versteckt", "options.hideLightningFlashes": "Blitzlichter verstecken", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dass Blitze den Himmel aufleuchten lassen. Die Blitze selbst sind weiterhin sichtbar.", "options.hideMatchedNames": "Nach <PERSON> filtern", "options.hideMatchedNames.tooltip": "Einige Server von Drittanbietern senden Chatnachrichten in einem nicht standardgemäßen Format.\nWenn diese Option aktiviert ist, werden ausgeblendeten Spielern ihre Nachrichten anhand der Chatabsendernamen zugeordnet.", "options.hideSplashTexts": "Menüsprüche ausblenden", "options.hideSplashTexts.tooltip": "Blendet die gelben Sprüche im Hauptmenü aus.", "options.inactivityFpsLimit": "FPS verringern, wenn", "options.inactivityFpsLimit.afk": "Abwesend", "options.inactivityFpsLimit.afk.tooltip": "Begrenzt die Bildwiederholrate auf 30 Bilder pro Sekunde, wenn der Spieler länger als 1 Minute keine Eingabe tätigt. Begrenzt sie nach 9 weiteren Minuten auf 10 Bilder pro Sekunde.", "options.inactivityFpsLimit.minimized": "Minimiert", "options.inactivityFpsLimit.minimized.tooltip": "Begrenzt die Bildwiederholrate nur dann, wenn das Spielfenster minimiert ist.", "options.invertMouse": "<PERSON><PERSON>", "options.japaneseGlyphVariants": "Japanische Glyphenvarianten", "options.japaneseGlyphVariants.tooltip": "Verwendet japanische Varianten von CJK‐Zeichen in der Standardschriftart.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Umschalten", "options.language": "<PERSON><PERSON><PERSON> …", "options.language.title": "<PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "Die Übersetzungen sind möglicherweise nicht ganz genau.", "options.languageWarning": "Die Übersetzungen sind möglicherweise nicht ganz genau.", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "Links", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap‐Stufen", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "Hu<PERSON>", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON> Ä<PERSON>el", "options.mouseWheelSensitivity": "Mausradempfindlichkeit", "options.mouse_settings": "Mauseinstellungen …", "options.mouse_settings.title": "Mauseinstellungen", "options.multiplayer.title": "Mehrspielereinstellungen …", "options.multiplier": "%s×", "options.music_frequency": "Musikhäufigkeit", "options.music_frequency.constant": "Durchgä<PERSON><PERSON>", "options.music_frequency.default": "Standard", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON>, wie häufig Musik beim Spielen in einer Welt wiedergegeben wird.", "options.narrator": "Sprachausgabe", "options.narrator.all": "<PERSON><PERSON> alles", "options.narrator.chat": "<PERSON><PERSON>", "options.narrator.notavailable": "Nicht verfügbar", "options.narrator.off": "Aus", "options.narrator.system": "Liest System", "options.notifications.display_time": "Benachrichtigungsdauer", "options.notifications.display_time.tooltip": "Beeinflusst die Zeitspanne, in der alle Benachrichtigungen auf dem Bildschirm sichtbar bleiben.", "options.off": "Aus", "options.off.composed": "%s: Aus", "options.on": "An", "options.on.composed": "%s: An", "options.online": "Online …", "options.online.title": "Online‐Einstellungen", "options.onlyShowSecureChat": "<PERSON><PERSON> sic<PERSON>n Cha<PERSON> anzeigen", "options.onlyShowSecureChat.tooltip": "Zeige nur Nachrichten von anderen Spielern an, die nachweislich von diesen gesendet und nicht verändert wurden.", "options.operatorItemsTab": "Operatorhilfsmittel‐Reiter", "options.particles": "Partikel", "options.particles.all": "Alle", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Minimal", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Chunk‐Kompilierung", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON> block<PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Einige Aktionen innerhalb eines Chunks führen zu einer sofortigen Neukompilierung des Chunks. Dazu gehören das Platzieren und Zerstören von <PERSON>lö<PERSON>n.", "options.prioritizeChunkUpdates.nearby": "Ganz blockierend", "options.prioritizeChunkUpdates.nearby.tooltip": "Benachbarte Chunks werden immer sofort kompiliert. Dies kann die Spielleistung beeinträchtigen, wenn Blöcke platziert oder zerstört werden.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Benachbarte Chunks werden parallel kompiliert. Dies kann zu kurz sichtbaren Löchern führen, wenn Blöcke zerstört werden.", "options.rawMouseInput": "<PERSON><PERSON><PERSON>", "options.realmsNotifications": "Realms‐Neuigkeiten & Einladungen", "options.realmsNotifications.tooltip": "Ruft Realms‐Neuigkeiten und ‐Einladungen im Hauptmenü ab und zeigt ihre jeweiligen Symbole auf der Schaltfläche „Realms“ an.", "options.reducedDebugInfo": "Reduzierte Debug‐Infos", "options.renderClouds": "Wolken", "options.renderCloudsDistance": "Wolkenreichweite", "options.renderDistance": "Sichtweite", "options.resourcepack": "Ressourcenpakete …", "options.rotateWithMinecart": "<PERSON><PERSON> <PERSON> mit<PERSON>", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON>, ob sich die Blickrichtung des Spielers mit Loren mitdreht. Nur in Welten verfügbar, in denen das Experiment „Lorenverbesse‐ rungen“ eingeschaltet ist.", "options.screenEffectScale": "Verzerrungseffekte", "options.screenEffectScale.tooltip": "Stärke von Übelkeits‐ und Netherportal‐Verzerrungseffekten.\nBei niedrigeren Werten wird der Übelkeitseffekt durch eine grüne Überlagerung ersetzt.", "options.sensitivity": "Empfindlichkeit", "options.sensitivity.max": "TURBO!!!", "options.sensitivity.min": "*gähn*", "options.showNowPlayingToast": "Liedtitel einblenden", "options.showNowPlayingToast.tooltip": "<PERSON><PERSON>gt eine Einblendung, sobald ein Lied zu spielen beginnt. Während das Lied abgespielt wird, ist diese Einblendung außerdem im Pausenmenü zu sehen.", "options.showSubtitles": "Untertitel anzeigen", "options.simulationDistance": "Simulationsweite", "options.skinCustomisation": "Skin‐Anpassung …", "options.skinCustomisation.title": "Skin‐Anpassung", "options.sounds": "Musik & Geräusche …", "options.sounds.title": "Musik‐ & Geräuschoptionen", "options.telemetry": "Telemetriedaten …", "options.telemetry.button": "Datenerhebung", "options.telemetry.button.tooltip": "„%s“ enthält nur erforderliche Daten.\n„%s“ enthält sowohl optionale als auch erforderliche Daten.", "options.telemetry.disabled": "Telemetrie ist deaktiviert.", "options.telemetry.state.all": "Alles", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "Optionen", "options.touchscreen": "Touchscreen‐Modus", "options.video": "Grafikeinstellungen …", "options.videoTitle": "Grafikeinstellungen", "options.viewBobbing": "Gehbewegung", "options.visible": "<PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft hat keinen freien Arbeitsspeicher mehr.\n\nDies könnte an einem Fehler im Spiel liegen oder daran, dass der Java Virtual Machine zu wenig Arbeitsspeicher zugewiesen wurde.\n\nUm eine Beschädigung der Welt zu verhindern, wurde die aktuelle Spielsitzung beendet. Wir haben versucht, so viel Arbeitsspeicher freizugeben, sodass du zum Hauptmenü zurückkehren und weiterspielen kannst; möglicherweise hat es funktioniert.\n\nBitte starte das Spiel neu, wenn diese Meldung erneut angezeigt wird.", "outOfMemory.title": "Kein freier Arbeitsspeicher mehr!", "pack.available.title": "Verfügbar", "pack.copyFailure": "Pakete konnten nicht kopiert werden", "pack.dropConfirm": "Möchtest du die folgenden Pakete zu Minecraft hinzufügen?", "pack.dropInfo": "<PERSON><PERSON><PERSON> Pakete in dieses Fenster, um sie hinzuzufügen.", "pack.dropRejected.message": "Die folgenden Einträge waren keine gültigen Pakete und wurden nicht kopiert:\n %s", "pack.dropRejected.title": "Nicht‐Paket‐Einträge", "pack.folderInfo": "(<PERSON>et<PERSON><PERSON> hier einfügen)", "pack.incompatible": "Inkompatibel", "pack.incompatible.confirm.new": "Dieses Paket wurde für eine neuere Version von Minecraft erstellt und funktioniert möglicherweise nicht richtig.", "pack.incompatible.confirm.old": "Dieses Paket wurde für eine ältere Version von Minecraft erstellt und funktioniert möglicherweise nicht mehr richtig.", "pack.incompatible.confirm.title": "B<PERSON> du sicher, dass du dieses Paket laden möchtest?", "pack.incompatible.new": "(für eine neuere Version von Minecraft erstellt)", "pack.incompatible.old": "(für eine ältere Version von Minecraft erstellt)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Paketordner öffnen", "pack.selected.title": "Ausgewählt", "pack.source.builtin": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.feature": "<PERSON><PERSON><PERSON>", "pack.source.local": "lokal", "pack.source.server": "Server", "pack.source.world": "Welt", "painting.dimensions": "%s × %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Der Hinterhof", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON>iel erfolgreich bombardiert", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Der Blumenstrauß", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Der Totenkopf in Flammen", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON> Büste", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Der Höhlenvogel", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Der Kostümwechsel", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Die Begegnung", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Erde", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Der Endgegner", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON> Kämpfer", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Der Fund", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Bodenständig", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Der Döner mit drei Pfefferonen", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Nebelschwaden", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativ", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON> Kugel", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Die Zitruseule", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Der Durchgang", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Das Schweinebildnis", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Der Pfennigbaum", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Der Zeigefinger", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON>", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON>ü<PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Die Mühsal des Irdischen", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Ein Totenkopf und Rosen", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Die Bühne ist bereitet", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON> Sonnenblumen", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "<PERSON>", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Die Gezeiten", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Entpackt", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Die Ödnis", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Luft", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>", "painting.random": "Zufälliges Motiv", "parsing.bool.expected": "Wahrheitswert erwartet", "parsing.bool.invalid": "Ungültiger Wahrheitswert: ‚%s‘ ist weder ‚true‘ noch ‚false‘", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON> erwartet", "parsing.double.invalid": "Ungültige Kommazahl ‚%s‘", "parsing.expected": "‚%s‘ erwartet", "parsing.float.expected": "<PERSON><PERSON><PERSON><PERSON> erwartet", "parsing.float.invalid": "Ungültige Kommazahl ‚%s‘", "parsing.int.expected": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "parsing.int.invalid": "Ungültige Ganzzahl ‚%s‘", "parsing.long.expected": "<PERSON> er<PERSON>", "parsing.long.invalid": "Ungültige lange Ganzzahl ‚%s‘", "parsing.quote.escape": "Ungültige Escape-Sequenz ‚\\%s‘ in Zeichenkette", "parsing.quote.expected.end": "Fehlendes Anführungszeichen am Ende der Zeichenkette", "parsing.quote.expected.start": "Fehlendes Anführungszeichen am Beginn der Zeichenkette", "particle.invalidOptions": "Partikeleigenschaften können nicht ausgewertet werden: %s", "particle.notFound": "Unbekanntes Partikel ‚%s‘", "permissions.requires.entity": "<PERSON>ser Befehl kann nur von einem Objekt ausgeführt werden", "permissions.requires.player": "<PERSON>ser Befehl kann nur von einem Spieler ausgeführt werden", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Auswirkungen:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unbekanntes Prädikat ‚%s‘", "quickplay.error.invalid_identifier": "Es konnte keine Welt mit der angegebenen Kennung gefunden werden", "quickplay.error.realm_connect": "Verbindung mit dem Realm konnte nicht hergestellt werden", "quickplay.error.realm_permission": "<PERSON><PERSON><PERSON><PERSON> Berechtigung, eine Verbindung mit diesem Realm herzustellen", "quickplay.error.title": "Schnellspielen fehlgeschlagen", "realms.configuration.region.australia_east": "New South Wales, Australien", "realms.configuration.region.australia_southeast": "Victoria, Australien", "realms.configuration.region.brazil_south": "Brasilien", "realms.configuration.region.central_india": "Indien", "realms.configuration.region.central_us": "Iowa, Vereinigte Staaten", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, Vereinigte Staaten", "realms.configuration.region.east_us_2": "North Carolina, Vereinigte Staaten", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.japan_west": "Westjapan", "realms.configuration.region.korea_central": "Südkorea", "realms.configuration.region.north_central_us": "Illinois, Vereinigte Staaten", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Texas, Vereinigte Staaten", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "Schweden", "realms.configuration.region.uae_north": "Vereinigte Arabische Emirate (VAE)", "realms.configuration.region.uk_south": "Südengland", "realms.configuration.region.west_central_us": "Utah, Vereinigte Staaten", "realms.configuration.region.west_europe": "Niederlande", "realms.configuration.region.west_us": "Kalifornien, Vereinigte Staaten", "realms.configuration.region.west_us_2": "Washington, Vereinigte Staaten", "realms.configuration.region_preference.automatic_owner": "Automatisch (nahe Realm‐Besitzer)", "realms.configuration.region_preference.automatic_player": "Automatisch (nahe 1. Spieler je Sitzung)", "realms.missing.snapshot.error.text": "Realms wird derzeit in Entwicklungsversionen nicht unterstützt", "recipe.notFound": "Unbekanntes Rezept ‚%s‘", "recipe.toast.description": "Sc<PERSON>u in dein Rezeptbuch", "recipe.toast.title": "Rezept(e) freigeschaltet!", "record.nowPlaying": "Jetzt spielt: %s", "recover_world.bug_tracker": "Einen Fehler melden", "recover_world.button": "Wiederherstellung versuchen", "recover_world.done.failed": "Die Welt konnte nicht aus früherem Zustand wiederhergestellt werden.", "recover_world.done.success": "Die Welt wurde erfolgreich wiederhergestellt!", "recover_world.done.title": "Wiederherstellung abgeschlossen", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "<PERSON><PERSON>, den Weltordner „%s“ einzulesen, sind die unten angegebenen Fehler aufgetreten.\nMöglicherweise lässt sich die Welt aus einem älteren Zustand wiederherstellen, oder du kannst dieses Problem im Bugtracker melden.", "recover_world.no_fallback": "<PERSON><PERSON> Z<PERSON>and für eine Wiederherstellung vorhanden", "recover_world.restore": "Wiederherstellung versuchen", "recover_world.restoring": "Es wird versucht, die Welt wiederherzustellen …", "recover_world.state_entry": "<PERSON><PERSON><PERSON> von %s: ", "recover_world.state_entry.unknown": "unbekannt", "recover_world.title": "Laden der Welt fehlgeschlagen", "recover_world.warning": "Laden der Weltbeschreibung fehlgeschlagen", "resourcePack.broken_assets": "DEFEKTE RESSOURCEN ERKANNT", "resourcePack.high_contrast.name": "<PERSON><PERSON>", "resourcePack.load_fail": "Neuladen der Ressourcen fehlgeschlagen", "resourcePack.programmer_art.name": "Programmiererkunst", "resourcePack.runtime_failure": "Fehler in Ressourcenpaket erkannt", "resourcePack.server.name": "Weltspezifische Ressourcen", "resourcePack.title": "Ressourcenpakete auswählen", "resourcePack.vanilla.description": "<PERSON> Standardaussehen von Minecraft", "resourcePack.vanilla.name": "Standard", "resourcepack.downloading": "Ressourcenpaket wird heruntergeladen", "resourcepack.progress": "<PERSON>i wird heruntergeladen (%s MB) …", "resourcepack.requesting": "<PERSON>e Anfrage …", "screenshot.failure": "<PERSON>nn Screenshot nicht speichern: %s", "screenshot.success": "Screenshot gespeichert unter %s", "selectServer.add": "Server hinz<PERSON><PERSON><PERSON>", "selectServer.defaultName": "Minecraft‐Server", "selectServer.delete": "Löschen", "selectServer.deleteButton": "Löschen", "selectServer.deleteQuestion": "B<PERSON> du sicher, dass du diesen Server entfernen möchtest?", "selectServer.deleteWarning": "‚%s‘ wird für immer verloren sein! (Eine lange Zeit!)", "selectServer.direct": "Direktverbindung", "selectServer.edit": "<PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(verst<PERSON><PERSON>)", "selectServer.refresh": "Aktualisieren", "selectServer.select": "Server beitreten", "selectWorld.access_failure": "Zugriff auf die Welt fehlgeschlagen", "selectWorld.allowCommands": "Ch<PERSON><PERSON> er<PERSON>ben", "selectWorld.allowCommands.info": "Be<PERSON>hle wie /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON><PERSON> erlauben", "selectWorld.backupEraseCache": "Zwischengespeicherte Daten löschen", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON> erstellen und laden", "selectWorld.backupJoinSkipButton": "Ich weiß, was ich tue!", "selectWorld.backupQuestion.customized": "Angepasste Welten werden nicht mehr unterstützt", "selectWorld.backupQuestion.downgrade": "Das Herabstufen einer Welt wird nicht unterstützt", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON>, die experimentelle Einstellungen verwenden, werden nicht unterstützt", "selectWorld.backupQuestion.snapshot": "Möchtest du diese Welt wirklich laden?", "selectWorld.backupWarning.customized": "Leider werden angepasste Welten in dieser Version von Minecraft nicht unterstützt. Die Welt kann weiterhin geladen werden und alles bleibt so wie es war, aber neu generierte Landschaften werden nicht mehr angepasst sein. Wir entschuldigen uns für die Unannehmlichkeiten!", "selectWorld.backupWarning.downgrade": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s. Eine Welt herabzustufen, kann diese beschädigen – wir können nicht garantieren, dass sie geladen wird oder funktioniert. Wenn du trotzdem fortfahren möchtest, erstelle bitte eine Sicherheitskopie.", "selectWorld.backupWarning.experimental": "Diese Welt verwendet experimentelle Einstellungen, die jederzeit nicht mehr funktionieren können. Wir können nicht garantieren, dass sie geladen wird oder funktioniert. Du betrittst unbekanntes Gelände!", "selectWorld.backupWarning.snapshot": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s. Bitte erstelle eine Sicherheitskopie für den Fall, dass die Welt beschädigt wird.", "selectWorld.bonusItems": "Bonustruhe", "selectWorld.cheats": "Cheats", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Muss umgewandelt werden!", "selectWorld.conversion.tooltip": "Diese Welt muss in einer älteren Version (wie 1.6.4) ge<PERSON><PERSON><PERSON> werden, um sicher umgewandelt zu werden", "selectWorld.create": "Neue Welt erstellen", "selectWorld.customizeType": "<PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Datenpakete", "selectWorld.data_read": "Weltdaten werden eingelesen …", "selectWorld.delete": "Löschen", "selectWorld.deleteButton": "Löschen", "selectWorld.deleteQuestion": "B<PERSON> du sicher, dass du diese Welt löschen möchtest?", "selectWorld.deleteWarning": "‚%s‘ wird für immer verloren sein! (Eine lange Zeit!)", "selectWorld.delete_failure": "Löschen der Welt fehlgeschlagen", "selectWorld.edit": "<PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "Sicherheitskopie erstellen", "selectWorld.edit.backupCreated": "Gesichert: %s", "selectWorld.edit.backupFailed": "Sicherung fehlgeschlagen", "selectWorld.edit.backupFolder": "Sicherheitskopienordner öffnen", "selectWorld.edit.backupSize": "Größe: %s MB", "selectWorld.edit.export_worldgen_settings": "Generierungseinstellungen exportieren", "selectWorld.edit.export_worldgen_settings.failure": "Export fehlgeschlagen", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.openFolder": "Weltordner öffnen", "selectWorld.edit.optimize": "Welt optimieren", "selectWorld.edit.resetIcon": "Symbol zurücksetzen", "selectWorld.edit.save": "Speichern", "selectWorld.edit.title": "Welt bearbeiten", "selectWorld.enterName": "Name der Welt", "selectWorld.enterSeed": "Startwert für den Weltengenerator", "selectWorld.experimental": "Experimentell", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Erforderliche experimentelle Spielelemente: %s", "selectWorld.experimental.details.title": "Anforderungen der experimentellen Spielelemente", "selectWorld.experimental.message": "Sei vorsichtig!\nDiese Konfiguration er<PERSON><PERSON>, die sich noch in der Entwicklung befinden. Deine Welt könnte abstürzen, kaputt<PERSON>hen oder mit zukünftigen Aktualisierungen nicht mehr funktionieren.", "selectWorld.experimental.title": "Warnung: <PERSON><PERSON>", "selectWorld.experiments": "Experimente", "selectWorld.experiments.info": "Experimente sind mögliche neue Spielelemente. <PERSON><PERSON> <PERSON><PERSON>, da deine Welt beschädigt werden kann. Nach dem Erstellen der Welt können sie nicht mehr deaktiviert werden.", "selectWorld.futureworld.error.text": "<PERSON><PERSON> einer Welt aus einer neueren Version ist ein Fehler aufgetreten. Dies war von Anfang an ein riskantes Unterfangen; leider hat es nicht funktioniert.", "selectWorld.futureworld.error.title": "Ein Fehler ist aufgetreten!", "selectWorld.gameMode": "Spielm<PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Wie der Überlebensmodus, aber Blöcke können nicht platziert oder abgebaut werden.", "selectWorld.gameMode.adventure.line1": "<PERSON>ie der Überlebensmodus, Blöcke können jedoch", "selectWorld.gameMode.adventure.line2": "nicht platziert oder zerstört werden", "selectWorld.gameMode.creative": "K<PERSON><PERSON>v", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, baue und erkunde ohne Grenzen. Du kannst fliegen, verfügst über unendlich viel Materialien und Monster können dir nichts anhaben.", "selectWorld.gameMode.creative.line1": "Unbegrenzte Rohstoffe, Flugmodus und", "selectWorld.gameMode.creative.line2": "sofortiges Zerstören von <PERSON>", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Der Überlebensmodus, ges<PERSON>rt auf der Schwierigkeit „Schwer“. Du kannst dich nicht wiederbeleben, wenn du stirbst.", "selectWorld.gameMode.hardcore.line1": "Überlebensmodus auf der höchsten", "selectWorld.gameMode.hardcore.line2": "Schwierigkeit mit endgültigem Tod", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON> gucken, nicht anfassen.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON> gucken, nicht anfassen", "selectWorld.gameMode.survival": "Überleben", "selectWorld.gameMode.survival.info": "Erkunde eine geheimnisvolle Welt, in der du bauen, sammeln, Handwerk treiben und Monster bekämpfen kannst.", "selectWorld.gameMode.survival.line1": "Suche nach Ressourcen, baue Werkzeuge, sammle", "selectWorld.gameMode.survival.line2": "Erfahrung, Gesundheit und Hunger", "selectWorld.gameRules": "Spielregeln", "selectWorld.import_worldgen_settings": "Einstellungen importieren", "selectWorld.import_worldgen_settings.failure": "Fehler beim Importieren der Einstellungen", "selectWorld.import_worldgen_settings.select_file": "Einstellungsdatei auswählen (.json)", "selectWorld.incompatible.description": "Diese Welt kann in der aktuellen Version nicht geöffnet werden.\nSie wurde zuletzt in der Version %s gespielt.", "selectWorld.incompatible.info": "Inkompatible Version: %s", "selectWorld.incompatible.title": "Inkompatible Version", "selectWorld.incompatible.tooltip": "Diese Welt kann nicht ge<PERSON><PERSON><PERSON> werden, da sie mit einer inkompatiblen Version erstellt wurde.", "selectWorld.incompatible_series": "Mit einer inkompatiblen Version erstellt", "selectWorld.load_folder_access": "<PERSON><PERSON> den Ordner, in dem die Welten gespeichert sind, konnte nicht zugegriffen werden!", "selectWorld.loading_list": "Weltauswahl wird geladen", "selectWorld.locked": "Von einer anderen laufenden Minecraft‐Instanz gesperrt", "selectWorld.mapFeatures": "Bauwerke generieren", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, Schiffswracks usw.", "selectWorld.mapType": "Welttyp", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Weitere Weltoptionen …", "selectWorld.newWorld": "Neue Welt", "selectWorld.recreate": "<PERSON><PERSON>", "selectWorld.recreate.customized.text": "Angepasste Welten werden in dieser Version von Minecraft nicht mehr unterstützt. Es wird versucht, die Welt erneut mit demselben Startwert und denselben Eigenschaften zu erzeugen, aber alle Landschaftsanpassungen gehen verloren. Wir entschuldigen uns für die Unannehmlichkeiten!", "selectWorld.recreate.customized.title": "Angepasste Welten werden nicht mehr unterstützt", "selectWorld.recreate.error.text": "Etwas ist beim Versuch eine Welt wieder zu beschaffen schief gelaufen.", "selectWorld.recreate.error.title": "Ein Fehler ist aufgetreten!", "selectWorld.resource_load": "Ressourcen werden vorbereitet …", "selectWorld.resultFolder": "Wird g<PERSON><PERSON><PERSON> in:", "selectWorld.search": "nach Welten suchen", "selectWorld.seedInfo": "<PERSON><PERSON> las<PERSON> für einen zufälligen Startwert", "selectWorld.select": "Ausgewählte Welt spielen", "selectWorld.targetFolder": "Speicherordner: %s", "selectWorld.title": "Welt auswählen", "selectWorld.tooltip.fromNewerVersion1": "Die Welt wurde in einer neueren Version gespeichert,", "selectWorld.tooltip.fromNewerVersion2": "das Laden dieser Welt könnte Probleme verursachen!", "selectWorld.tooltip.snapshot1": "<PERSON>er<PERSON><PERSON> nicht, diese Welt zu sichern,", "selectWorld.tooltip.snapshot2": "bevor du sie in dieser Version öffnest.", "selectWorld.unable_to_load": "Welten konnten nicht geladen werden", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "Trotzdem laden", "selectWorld.versionQuestion": "Möchtest du diese Welt wirklich laden?", "selectWorld.versionUnknown": "unbekannt", "selectWorld.versionWarning": "Diese Welt wurde zuletzt in der Version %s gespielt. Das Laden dieser Welt könnte Schäden verursachen!", "selectWorld.warning.deprecated.question": "Einige der verwendeten Spielelemente sind veraltet und werden in Zukunft nicht mehr funktionieren. Möchtest du fortfahren?", "selectWorld.warning.deprecated.title": "Achtung! Diese Einstellungen verwenden veraltete Spielelemente", "selectWorld.warning.experimental.question": "Diese Einstellungen sind experimentell und könnten eines Tages nicht mehr funktionieren. Möchtest du fortfahren?", "selectWorld.warning.experimental.title": "Achtung! Diese Einstellungen verwenden experimentelle Spielelemente", "selectWorld.warning.lowDiskSpace.description": "Auf deinem Gerät ist nicht mehr viel freier Speicherplatz vorhanden.\nWenn der Speicherplatz während des Spiels aufgebraucht ist, kann dies dazu führen, dass deine Welt beschädigt wird.", "selectWorld.warning.lowDiskSpace.title": "Achtung! Wenig freier Speicherplatz!", "selectWorld.world": "Welt", "sign.edit": "Schildbeschriftung bearbeiten", "sleep.not_possible": "Die Nacht kann nicht durchgeschlafen werden", "sleep.players_sleeping": "%s/%s Spielern schlafen", "sleep.skipping_night": "Die Nacht wird durchgeschlafen", "slot.only_single_allowed": "Nur einzelne Inventarplätze sind erlaubt, ‚%s‘ erhalten", "slot.unknown": "Unbekannter Inventarplatz ‚%s‘", "snbt.parser.empty_key": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein", "snbt.parser.expected_binary_numeral": "Bin<PERSON><PERSON><PERSON> erwartet", "snbt.parser.expected_decimal_numeral": "Dezimalzahl erwartet", "snbt.parser.expected_float_type": "Gleitkommazahl erwartet", "snbt.parser.expected_hex_escape": "Zeichenkette der Länge %s erwartet", "snbt.parser.expected_hex_numeral": "Hexadez<PERSON><PERSON><PERSON><PERSON> erwart<PERSON>", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "snbt.parser.expected_non_negative_number": "Nichtnegative Zahl erwartet", "snbt.parser.expected_number_or_boolean": "Zahl oder Wahrheitswert erwartet", "snbt.parser.expected_string_uuid": "Zeichenkette erwartet, die eine gültige UUID darstellt", "snbt.parser.expected_unquoted_string": "Gültige Zeichenkette ohne Anführungszeichen erwartet", "snbt.parser.infinity_not_allowed": "Nicht endliche Zahlen sind nicht erlaubt", "snbt.parser.invalid_array_element_type": "<PERSON>g<PERSON><PERSON><PERSON> von <PERSON>", "snbt.parser.invalid_character_name": "Ungültiger Name von Unicode‐Zeichen", "snbt.parser.invalid_codepoint": "Ungültiger Wert für Unicode‐Zeichen: %s", "snbt.parser.invalid_string_contents": "Ungültiger Inhalt der Zeichenkette", "snbt.parser.invalid_unquoted_start": "Zeichenketten ohne Anführungszeichen dürfen nicht mit den Ziffern 0–9 oder mit den Zeichen + und − beginnen", "snbt.parser.leading_zero_not_allowed": "Dezimalzahlen dürfen nicht mit 0 beginnen", "snbt.parser.no_such_operation": "Eine Operation ‚%s‘ existiert nicht", "snbt.parser.number_parse_failure": "Zahl konnte nicht gelesen werden: %s", "snbt.parser.undescore_not_allowed": "Unterstriche sind an Anfang und Ende einer Zahl nicht erlaubt", "soundCategory.ambient": "Atmosphäre/Umgebung", "soundCategory.block": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.hostile": "Feindliche Kreaturen", "soundCategory.master": "Gesamtlautstärke", "soundCategory.music": "Mu<PERSON>", "soundCategory.neutral": "Freundliche Kreaturen", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Musikbl<PERSON>cke", "soundCategory.ui": "Benutzeroberfläche", "soundCategory.voice": "Stimme/Sprache", "soundCategory.weather": "Wetter", "spectatorMenu.close": "<PERSON><PERSON> sch<PERSON>ßen", "spectatorMenu.next_page": "Nächste Seite", "spectatorMenu.previous_page": "Vorherige Seite", "spectatorMenu.root.prompt": "<PERSON><PERSON>e eine <PERSON>, um einen <PERSON>hl zu wählen, und drücke sie erneut, um ihn auszuführen.", "spectatorMenu.team_teleport": "Zu <PERSON>mitglied teleportieren", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> das Team, zu dem du dich teleportieren möchtest", "spectatorMenu.teleport": "Zu <PERSON>ler teleportieren", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> den Spieler, zu dem du dich teleportieren möchtest", "stat.generalButton": "Allgemein", "stat.itemsButton": "Gegenstände", "stat.minecraft.animals_bred": "Tiere gezüchtet", "stat.minecraft.aviate_one_cm": "Strecke mit Elytren geflogen", "stat.minecraft.bell_ring": "Glocken geläutet", "stat.minecraft.boat_one_cm": "Strecke in Boot gefahren", "stat.minecraft.clean_armor": "Rüstungsteile gewaschen", "stat.minecraft.clean_banner": "Banner gewaschen", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> gew<PERSON>chen", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON> gek<PERSON>rt", "stat.minecraft.crouch_one_cm": "Strecke geschlichen", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> mit Schild abgewehrt", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON> zugefügt", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> zu<PERSON>f<PERSON> (aufgenommen)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> zugefügt (widerstanden)", "stat.minecraft.damage_resisted": "Sc<PERSON>en widerstanden", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON> er<PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON>", "stat.minecraft.drop": "Gegenstände fallen gelassen", "stat.minecraft.eat_cake_slice": "Kuchenstücke gegessen", "stat.minecraft.enchant_item": "Gegenstände verzaubert", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON> gefallen", "stat.minecraft.fill_cauldron": "Kessel gefüllt", "stat.minecraft.fish_caught": "Fische gefangen", "stat.minecraft.fly_one_cm": "Strecke geflogen", "stat.minecraft.happy_ghast_one_cm": "Strecke auf glücklichem Ghast geflogen", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON> auf P<PERSON><PERSON> geritten", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON>er durchsucht", "stat.minecraft.inspect_dropper": "Spender durchsucht", "stat.minecraft.inspect_hopper": "<PERSON>chter durchsucht", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_beacon": "Leuchtfe<PERSON> benutzt", "stat.minecraft.interact_with_blast_furnace": "Schmelzöfen benutzt", "stat.minecraft.interact_with_brewingstand": "Braustände benutzt", "stat.minecraft.interact_with_campfire": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_cartography_table": "Kartentische benutzt", "stat.minecraft.interact_with_crafting_table": "Werkbänke benutzt", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON>", "stat.minecraft.interact_with_grindstone": "Schleifsteine benutzt", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "Schmiedetische benutzt", "stat.minecraft.interact_with_smoker": "Räucheröfen <PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "S<PERSON>le verlassen", "stat.minecraft.minecart_one_cm": "Strecke in Lore gefahren", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON> g<PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> g<PERSON>", "stat.minecraft.open_enderchest": "Endertruhen g<PERSON>öffnet", "stat.minecraft.open_shulker_box": "<PERSON>lker-<PERSON><PERSON>", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON> auf Schwein geritten", "stat.minecraft.play_noteblock": "Notenblöcke abgespielt", "stat.minecraft.play_record": "Schallplatten abgespielt", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> getötet", "stat.minecraft.pot_flower": "Pflanzen eingetopft", "stat.minecraft.raid_trigger": "Überfälle ausgelöst", "stat.minecraft.raid_win": "<PERSON>berf<PERSON><PERSON> besiegt", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON>", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "Strecke gesprintet", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON> auf Schreiter geritten", "stat.minecraft.swim_one_cm": "Strecke geschwommen", "stat.minecraft.talked_to_villager": "Mit Dorfbewohnern gesprochen", "stat.minecraft.target_hit": "Zielblöcke getroffen", "stat.minecraft.time_since_death": "Zeit seit letztem Tod", "stat.minecraft.time_since_rest": "Zeit seit letztem Ausruhen", "stat.minecraft.total_world_time": "Zeit mit geöffneter Welt", "stat.minecraft.traded_with_villager": "Mit Dorfbewohnern gehandelt", "stat.minecraft.trigger_trapped_chest": "Redstone-Truhen ausgelöst", "stat.minecraft.tune_noteblock": "Notenblöcke gestimmt", "stat.minecraft.use_cauldron": "Wasser aus Kesseln geschöpft", "stat.minecraft.walk_on_water_one_cm": "Streck<PERSON> auf Wasser gelaufen", "stat.minecraft.walk_one_cm": "Strecke gelaufen", "stat.minecraft.walk_under_water_one_cm": "St<PERSON><PERSON> unter Wasser gelaufen", "stat.mobsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Ver<PERSON><PERSON>t", "stat_type.minecraft.crafted": "Hergestellt", "stat_type.minecraft.dropped": "<PERSON> gelassen", "stat_type.minecraft.killed": "Du hast %s %s erlegt", "stat_type.minecraft.killed.none": "Du hast %s nie erlegt", "stat_type.minecraft.killed_by": "%s hat dich %s‐mal getötet", "stat_type.minecraft.killed_by.none": "Du wurdest nie von %s getötet", "stat_type.minecraft.mined": "Abgebaut", "stat_type.minecraft.picked_up": "Aufgesammelt", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON>", "stats.none": "X", "structure_block.button.detect_size": "Erkennen", "structure_block.button.load": "Laden", "structure_block.button.save": "Speichern", "structure_block.custom_data": "Benutzerdefinierte NBT-Daten", "structure_block.detect_size": "Konstruktionsgröße und ‐position erkennen:", "structure_block.hover.corner": "Ecke: %s", "structure_block.hover.data": "Daten: %s", "structure_block.hover.load": "Laden: %s", "structure_block.hover.save": "Speichern: %s", "structure_block.include_entities": "Objekte einbeziehen:", "structure_block.integrity": "Vollständigkeit & Startwert (Konstruktion)", "structure_block.integrity.integrity": "Konstruktionsvollständigkeit", "structure_block.integrity.seed": "Konstruktions-Startwert", "structure_block.invalid_structure_name": "Ungültiger Konstruktionsname ‚%s‘", "structure_block.load_not_found": "Konstruktion ‚%s‘ nicht gefunden", "structure_block.load_prepare": "Position von Konstruktion ‚%s‘ vorbereitet", "structure_block.load_success": "Konstruktion ‚%s‘ geladen", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "Daten", "structure_block.mode.load": "Laden", "structure_block.mode.save": "Speichern", "structure_block.mode_info.corner": "Eck‐Modus – Markierung von Position und Größe", "structure_block.mode_info.data": "Daten‐Modus – Markierung der Spiellogik", "structure_block.mode_info.load": "Lade‐Modus – Au<PERSON> Datei laden", "structure_block.mode_info.save": "Speicher‐Modus – In Datei speichern", "structure_block.position": "Relative Position", "structure_block.position.x": "Relative X-Position", "structure_block.position.y": "Relative Y-Position", "structure_block.position.z": "Relative Z-Position", "structure_block.save_failure": "Konstruktion ‚%s‘ konnte nicht gespeichert werden", "structure_block.save_success": "Konstruktion als ‚%s‘ gespeichert", "structure_block.show_air": "Unsichtbare Blöcke anzeigen:", "structure_block.show_boundingbox": "Begrenzungsrahmen anzeigen:", "structure_block.size": "Konstruktionsgröße", "structure_block.size.x": "Konstruktionsgröße (X)", "structure_block.size.y": "Konstruktionsgröße (Y)", "structure_block.size.z": "Konstruktionsgröße (Z)", "structure_block.size_failure": "Kann Konstruktionsgröße nicht erkennen - füge Ecken mit gleichem Konstruktionsnamen hinzu", "structure_block.size_success": "<PERSON><PERSON><PERSON><PERSON> von ‚%s‘ erfolgreich erkannt", "structure_block.strict": "Strikte Platzierung:", "structure_block.structure_name": "Konstruktionsname", "subtitles.ambient.cave": "Gruseliges Geräusch", "subtitles.ambient.sound": "Unheimliches Geräusch", "subtitles.block.amethyst_block.chime": "Amethyst klingt", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON> schwingt mit", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> gelandet", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON>ass schließt", "subtitles.block.barrel.open": "<PERSON><PERSON>", "subtitles.block.beacon.activate": "Leuchtfeuer aktiviert", "subtitles.block.beacon.ambient": "Leuchtfeuer wummert", "subtitles.block.beacon.deactivate": "Leuchtfeuer deaktiviert", "subtitles.block.beacon.power_select": "Leuchtfeuerkraft ausgewählt", "subtitles.block.beehive.drip": "<PERSON><PERSON> tropft", "subtitles.block.beehive.enter": "Biene betritt Stock", "subtitles.block.beehive.exit": "Biene verlässt Stock", "subtitles.block.beehive.shear": "<PERSON><PERSON> schabt", "subtitles.block.beehive.work": "Bienen arbeiten", "subtitles.block.bell.resonate": "<PERSON>locke hallt nach", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Tropfblatt kippt", "subtitles.block.big_dripleaf.tilt_up": "Tropfblatt richtet sich", "subtitles.block.blastfurnace.fire_crackle": "Schmelzofen knistert", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON> blubbert", "subtitles.block.bubble_column.bubble_pop": "Blasen platzen", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> sprudeln", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "Blasen wirbeln", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> z<PERSON>hen", "subtitles.block.button.click": "<PERSON><PERSON><PERSON> klickt", "subtitles.block.cake.add_candle": "Kuchen schwabbelt", "subtitles.block.campfire.crackle": "Lagerfeuer knistert", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON> knistert", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "T<PERSON><PERSON> schließt", "subtitles.block.chest.locked": "Truhe verschlossen", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.death": "Chorusb<PERSON><PERSON><PERSON> welkt", "subtitles.block.chorus_flower.grow": "Chorusblüte wächst", "subtitles.block.comparator.click": "Komp<PERSON><PERSON> klickt", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON> geleert", "subtitles.block.composter.fill": "Komposter befüllt", "subtitles.block.composter.ready": "Komposter kompostiert", "subtitles.block.conduit.activate": "Aquisator aktiviert", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON> puls<PERSON><PERSON>", "subtitles.block.conduit.attack.target": "Aquisator greift an", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Kupferleuchte schaltet aus", "subtitles.block.copper_bulb.turn_on": "Kupferle<PERSON>te schaltet ein", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.crafter.craft": "<PERSON>rker werkelt", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON> versagt", "subtitles.block.creaking_heart.hurt": "Knarzherz grummelt", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON> Gerä<PERSON>", "subtitles.block.creaking_heart.spawn": "Knarzherz erwacht", "subtitles.block.deadbush.idle": "Zweige knistern", "subtitles.block.decorated_pot.insert": "Verzierter Krug befüllt", "subtitles.block.decorated_pot.insert_fail": "Verzierter Krug wackelt", "subtitles.block.decorated_pot.shatter": "Verzierter Krug zerbricht", "subtitles.block.dispenser.dispense": "Gegenstand geworfen", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON> versagt", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON> knarrt", "subtitles.block.dried_ghast.ambient": "Trockene Geräusche", "subtitles.block.dried_ghast.ambient_water": "Ausgetrockneter Ghast nimmt Was<PERSON> auf", "subtitles.block.dried_ghast.place_in_water": "Ausgetrockneter Ghast quillt", "subtitles.block.dried_ghast.transition": "Ausgetrocknetem Ghast geht es besser", "subtitles.block.dry_grass.ambient": "Wind weht", "subtitles.block.enchantment_table.use": "Zaubertisch benutzt", "subtitles.block.end_portal.spawn": "Endportal öffnet sich", "subtitles.block.end_portal_frame.fill": "Enderauge eingesetzt", "subtitles.block.eyeblossom.close": "Augenblüte schließt", "subtitles.block.eyeblossom.idle": "Augenblüte <PERSON>", "subtitles.block.eyeblossom.open": "Augenblü<PERSON>", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON> knarrt", "subtitles.block.fire.ambient": "<PERSON><PERSON> knistert", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Glühwürmchen sch<PERSON>rren", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> knistert", "subtitles.block.generic.break": "Block zerstört", "subtitles.block.generic.fall": "Etwas fällt auf einen Block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Block bricht", "subtitles.block.generic.place": "Block platziert", "subtitles.block.grindstone.use": "Schleifs<PERSON> benutzt", "subtitles.block.growing_plant.crop": "Pflanze gestutzt", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> wackelt", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.lava.ambient": "<PERSON>va brodelt", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON>", "subtitles.block.note_block.note": "<PERSON><PERSON><PERSON> spielt", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON><PERSON> Gerä<PERSON>", "subtitles.block.piston.move": "Kolben arbeitet", "subtitles.block.pointed_dripstone.drip_lava": "Lava tropft", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava tropft in Kessel", "subtitles.block.pointed_dripstone.drip_water": "Wasser tropft", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Wasser tropft in Kessel", "subtitles.block.pointed_dripstone.land": "Stalaktit fällt herunter", "subtitles.block.portal.ambient": "<PERSON> wabert", "subtitles.block.portal.travel": "Portalrauschen lässt nach", "subtitles.block.portal.trigger": "Portalrauschen nimmt zu", "subtitles.block.pressure_plate.click": "Druckplatte klickt", "subtitles.block.pumpkin.carve": "<PERSON><PERSON> sch<PERSON>t", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> z<PERSON>", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> wabert", "subtitles.block.respawn_anchor.charge": "Seelenanker aufgeladen", "subtitles.block.respawn_anchor.deplete": "Seelenanker verbraucht", "subtitles.block.respawn_anchor.set_spawn": "Seelenanker setzt Wiedereinstiegspunkt", "subtitles.block.sand.idle": "Sand weht", "subtitles.block.sand.wind": "Wind weht", "subtitles.block.sculk.charge": "Sculk brodelt", "subtitles.block.sculk.spread": "Sculk verbreitet sich", "subtitles.block.sculk_catalyst.bloom": "Sculk‐Katalysator erblüht", "subtitles.block.sculk_sensor.clicking": "Sculk‐Sensor klickt", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-Sensor hört auf zu klicken", "subtitles.block.sculk_shrieker.shriek": "Sculk‐Kreischer kreischt", "subtitles.block.shulker_box.close": "Shulker‐Ki<PERSON> schließt", "subtitles.block.shulker_box.open": "Shulker‐Kiste ö<PERSON>net", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> wackelt", "subtitles.block.smithing_table.use": "Schmiedetisch benutzt", "subtitles.block.smoker.smoke": "Räucherofen räuchert", "subtitles.block.sniffer_egg.crack": "Schnüffler‐<PERSON>i knackt", "subtitles.block.sniffer_egg.hatch": "Schnüfflerjunges schlüpft", "subtitles.block.sniffer_egg.plop": "Schnüffler legt Ei", "subtitles.block.sponge.absorb": "<PERSON><PERSON><PERSON> saugt auf", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON> g<PERSON>", "subtitles.block.trapdoor.close": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.open": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON><PERSON> knarrt", "subtitles.block.trial_spawner.about_to_spawn_item": "Unheilvoller Gegenstand bereitet vor", "subtitles.block.trial_spawner.ambient": "Prüfungs‐Spawner knistert", "subtitles.block.trial_spawner.ambient_charged": "Unheilvolles Geknister", "subtitles.block.trial_spawner.ambient_ominous": "Unheilvolles Geknister", "subtitles.block.trial_spawner.charge_activate": "Unheil umhüllt Prüfungs‐Spawner", "subtitles.block.trial_spawner.close_shutter": "Prüfungs‐Spawner schließt", "subtitles.block.trial_spawner.detect_player": "Prüfungs‐Spawner lädt auf", "subtitles.block.trial_spawner.eject_item": "Prüfungs‐Spawner wirft Beute aus", "subtitles.block.trial_spawner.ominous_activate": "Unheil umhüllt Prüfungs‐Spawner", "subtitles.block.trial_spawner.open_shutter": "Prüfungs‐Spawner öffnet", "subtitles.block.trial_spawner.spawn_item": "Unheilvoller Gegenstand fallen gelassen", "subtitles.block.trial_spawner.spawn_item_begin": "Unheilvoller Gegenstand erscheint", "subtitles.block.trial_spawner.spawn_mob": "Prüfungs‐Spawner erschafft Kreatur", "subtitles.block.tripwire.attach": "Stolperdraht gespannt", "subtitles.block.tripwire.click": "Stolperdraht ausgelöst", "subtitles.block.tripwire.detach": "Stolperdraht entspannt", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "Tresor knistert", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON> sch<PERSON>t", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "Tresor wirft Gegenstand aus", "subtitles.block.vault.insert_item": "Tresor entriegelt", "subtitles.block.vault.insert_item_fail": "Tresor ak<PERSON><PERSON><PERSON><PERSON> nicht", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "Tresor weist <PERSON><PERSON> ab", "subtitles.block.water.ambient": "<PERSON><PERSON> fließt", "subtitles.block.wet_sponge.dries": "<PERSON><PERSON><PERSON> trocknet", "subtitles.chiseled_bookshelf.insert": "<PERSON>uch hineingestellt", "subtitles.chiseled_bookshelf.insert_enchanted": "Verzaubertes Buch hineingestellt", "subtitles.chiseled_bookshelf.take": "<PERSON>uch entnommen", "subtitles.chiseled_bookshelf.take_enchanted": "Verzaubertes Buch entnommen", "subtitles.enchant.thorns.hit": "Dornen stechen", "subtitles.entity.allay.ambient_with_item": "Hilfsgeist sucht", "subtitles.entity.allay.ambient_without_item": "Hilfsgeist schmachtet", "subtitles.entity.allay.death": "Hilfsgeist stirbt", "subtitles.entity.allay.hurt": "Hilfsgeist ni<PERSON><PERSON>", "subtitles.entity.allay.item_given": "Hilfsgeist gluckst", "subtitles.entity.allay.item_taken": "Hilfs<PERSON><PERSON> hilft", "subtitles.entity.allay.item_thrown": "Hilfsge<PERSON> schle<PERSON>rt", "subtitles.entity.armadillo.ambient": "Gürteltier grunzt", "subtitles.entity.armadillo.brush": "Hornschild abgebürstet", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.armadillo.hurt": "Gürteltier nimmt <PERSON>en", "subtitles.entity.armadillo.hurt_reduced": "Gürteltier schützt sich", "subtitles.entity.armadillo.land": "Gürteltier landet", "subtitles.entity.armadillo.peek": "Gürteltier späht", "subtitles.entity.armadillo.roll": "Gürteltier rollt sich ein", "subtitles.entity.armadillo.scute_drop": "Gürteltier wirft Hornschild ab", "subtitles.entity.armadillo.unroll_finish": "G<PERSON>rteltier entrollt sich", "subtitles.entity.armadillo.unroll_start": "Gürteltier späht", "subtitles.entity.armor_stand.fall": "Etwas ist gefallen", "subtitles.entity.arrow.hit": "Pfeil trifft", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> getroffen", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> ab<PERSON><PERSON><PERSON>t", "subtitles.entity.axolotl.attack": "Axolotl greift an", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.axolotl.hurt": "Axolotl nimmt <PERSON>", "subtitles.entity.axolotl.idle_air": "Axolo<PERSON>", "subtitles.entity.axolotl.idle_water": "Axolo<PERSON>", "subtitles.entity.axolotl.splash": "Axolotl plätschert", "subtitles.entity.axolotl.swim": "Axolotl schwimmt", "subtitles.entity.bat.ambient": "Fledermaus kreischt", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON> fliegt los", "subtitles.entity.bee.ambient": "Biene summt", "subtitles.entity.bee.death": "<PERSON><PERSON>e stirbt", "subtitles.entity.bee.hurt": "Biene nimmt Sc<PERSON>en", "subtitles.entity.bee.loop": "Biene summt", "subtitles.entity.bee.loop_aggressive": "Biene summt verärgert", "subtitles.entity.bee.pollinate": "Biene summt fröhlich", "subtitles.entity.bee.sting": "Biene sticht", "subtitles.entity.blaze.ambient": "<PERSON><PERSON> atmet", "subtitles.entity.blaze.burn": "<PERSON>he knistert", "subtitles.entity.blaze.death": "<PERSON><PERSON> stirbt", "subtitles.entity.blaze.hurt": "<PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON> s<PERSON>t", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Sumpfskelett klappert", "subtitles.entity.bogged.death": "Sumpfskelett stirbt", "subtitles.entity.bogged.hurt": "Sumpfskelett nimmt Schaden", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON> lädt auf", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> wehrt ab", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON><PERSON> fliegt", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON> schwi<PERSON>t", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON> holt Luft", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON> landet", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON> wirbelt", "subtitles.entity.breeze.wind_burst": "Windkugel platzt", "subtitles.entity.camel.ambient": "Dr<PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON> pre<PERSON>t", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON> erholt sich", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> frisst", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "Dr<PERSON><PERSON> g<PERSON>t", "subtitles.entity.camel.sit": "Dr<PERSON>dar legt sich hin", "subtitles.entity.camel.stand": "Dr<PERSON>dar erhebt sich", "subtitles.entity.camel.step": "Dr<PERSON><PERSON>", "subtitles.entity.camel.step_sand": "Dromedar läuf<PERSON> im Sand", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> bettelt", "subtitles.entity.cat.death": "<PERSON><PERSON> stirbt", "subtitles.entity.cat.eat": "<PERSON><PERSON> frisst", "subtitles.entity.cat.hiss": "<PERSON><PERSON> faucht", "subtitles.entity.cat.hurt": "<PERSON><PERSON> nimm<PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON> schnu<PERSON>t", "subtitles.entity.chicken.ambient": "<PERSON><PERSON> gackert", "subtitles.entity.chicken.death": "<PERSON><PERSON> stirbt", "subtitles.entity.chicken.egg": "<PERSON><PERSON> legt Ei", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.cod.flop": "Kabeljau platscht", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON> muht", "subtitles.entity.cow.death": "<PERSON><PERSON> stirbt", "subtitles.entity.cow.hurt": "<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.cow.milk": "<PERSON>h wird gemolken", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.ambient": "Knarz knarzt", "subtitles.entity.creaking.attack": "K<PERSON>z greift an", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON> beruhigt sich", "subtitles.entity.creaking.death": "Knarz zerbr<PERSON>lt", "subtitles.entity.creaking.freeze": "Knarz erstarrt", "subtitles.entity.creaking.spawn": "Knarz nimmt Gestalt an", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON> get<PERSON>n", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON> zu<PERSON>t", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON> rührt sich", "subtitles.entity.creeper.death": "<PERSON><PERSON>per stirbt", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.creeper.primed": "C<PERSON>per z<PERSON>t", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "Delfin pfeift", "subtitles.entity.dolphin.attack": "Delfin greift an", "subtitles.entity.dolphin.death": "Delfin stirbt", "subtitles.entity.dolphin.eat": "Delfin frisst", "subtitles.entity.dolphin.hurt": "Del<PERSON> nimmt <PERSON>", "subtitles.entity.dolphin.jump": "<PERSON><PERSON> springt", "subtitles.entity.dolphin.play": "<PERSON><PERSON> spielt", "subtitles.entity.dolphin.splash": "Delfin platscht", "subtitles.entity.dolphin.swim": "Delfin schwimmt", "subtitles.entity.donkey.ambient": "<PERSON><PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON> wiehert", "subtitles.entity.donkey.chest": "<PERSON><PERSON> bepackt", "subtitles.entity.donkey.death": "<PERSON><PERSON> stirbt", "subtitles.entity.donkey.eat": "<PERSON><PERSON> frisst", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> springt", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON> gluckert", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON> gluckert", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.drowned.hurt": "Ertrunkener nimmt Schaden", "subtitles.entity.drowned.shoot": "Ertrunkener wirft Dreizack", "subtitles.entity.drowned.step": "Ertrunkener läuft", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON> schwimmt", "subtitles.entity.egg.throw": "<PERSON><PERSON> fliegt", "subtitles.entity.elder_guardian.ambient": "G<PERSON>ßw<PERSON>cht<PERSON> raunt", "subtitles.entity.elder_guardian.ambient_land": "Großwächter zappelt", "subtitles.entity.elder_guardian.curse": "Großwächter verflucht", "subtitles.entity.elder_guardian.death": "G<PERSON>ßwächter stirbt", "subtitles.entity.elder_guardian.flop": "Großwächter platscht", "subtitles.entity.elder_guardian.hurt": "Großwächter nimmt Schaden", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON> stirbt", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON> flattert", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON> nimm<PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON>", "subtitles.entity.ender_eye.death": "Enderauge platzt", "subtitles.entity.ender_eye.launch": "Enderauge geworfen", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> fliegt", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> woopt", "subtitles.entity.enderman.death": "<PERSON><PERSON> stirbt", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.enderman.scream": "Enderman kreischt", "subtitles.entity.enderman.stare": "<PERSON><PERSON> schreit", "subtitles.entity.enderman.teleport": "Enderman teleportiert", "subtitles.entity.endermite.ambient": "Endermite krabbelt", "subtitles.entity.endermite.death": "Endermite stirbt", "subtitles.entity.endermite.hurt": "Endermite nimmt <PERSON>en", "subtitles.entity.evoker.ambient": "Ma<PERSON>r murmelt", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON> z<PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "Ma<PERSON>r jubelt", "subtitles.entity.evoker.death": "Magier stirbt", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.evoker.prepare_attack": "Magier bereitet Angriff vor", "subtitles.entity.evoker.prepare_summon": "Magier bereitet Beschwörung vor", "subtitles.entity.evoker.prepare_wololo": "Magier bereitet Zauber vor", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> schnappen zu", "subtitles.entity.experience_orb.pickup": "Erfahrung erhalten", "subtitles.entity.firework_rocket.blast": "Feuerwerk explodiert", "subtitles.entity.firework_rocket.launch": "Feuerwerk zündet", "subtitles.entity.firework_rocket.twinkle": "Feuerwerk funkelt", "subtitles.entity.fish.swim": "Platschen", "subtitles.entity.fishing_bobber.retrieve": "Schwimmer eingeholt", "subtitles.entity.fishing_bobber.splash": "Schwimmer platscht", "subtitles.entity.fishing_bobber.throw": "Schwimmer ausgeworfen", "subtitles.entity.fox.aggro": "Fuchs erzürnt", "subtitles.entity.fox.ambient": "Fuchs wins<PERSON>t", "subtitles.entity.fox.bite": "<PERSON><PERSON> beißt", "subtitles.entity.fox.death": "Fuchs stirbt", "subtitles.entity.fox.eat": "Fuchs frisst", "subtitles.entity.fox.hurt": "Fuchs nimmt Schaden", "subtitles.entity.fox.screech": "Fuchs kreischt", "subtitles.entity.fox.sleep": "Fuchs schnarcht", "subtitles.entity.fox.sniff": "<PERSON><PERSON> schnu<PERSON>t", "subtitles.entity.fox.spit": "Fuchs spuckt aus", "subtitles.entity.fox.teleport": "Fuchs teleportiert", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> quakt", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON> stir<PERSON>", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON> fris<PERSON>", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.generic.big_fall": "Etwas ist gefallen", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Sterben", "subtitles.entity.generic.drink": "Schl<PERSON>rfen", "subtitles.entity.generic.eat": "Essen", "subtitles.entity.generic.explode": "Explosion", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "Etwas nimmt <PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON> stolpert", "subtitles.entity.generic.splash": "Platschen", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Windkugel platzt", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> jammert", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> gurrt", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON> erscheint", "subtitles.entity.glow_item_frame.add_item": "Leuchtrahm<PERSON> g<PERSON>t", "subtitles.entity.glow_item_frame.break": "Leuchtrahmen zerbricht", "subtitles.entity.glow_item_frame.place": "Leuchtrahm<PERSON> platz<PERSON>", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> gel<PERSON>t", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> klickt", "subtitles.entity.glow_squid.ambient": "Leuchttintenfisch schwimmt", "subtitles.entity.glow_squid.death": "Leuchttintenfisch stirbt", "subtitles.entity.glow_squid.hurt": "Leuchttintenfisch nimmt Schaden", "subtitles.entity.glow_squid.squirt": "Leuchttintenfisch verspritzt Tinte", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> meckert", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> frisst", "subtitles.entity.goat.horn_break": "Ziegenhorn bricht ab", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>en", "subtitles.entity.goat.long_jump": "Ziege springt", "subtitles.entity.goat.milk": "Ziege wird gemolken", "subtitles.entity.goat.prepare_ram": "Z<PERSON>ge stampft", "subtitles.entity.goat.ram_impact": "Ziege rammt", "subtitles.entity.goat.screaming.ambient": "Ziege schreit", "subtitles.entity.goat.step": "Ziege läuft", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.guardian.ambient_land": "Wächter zappelt", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON> schi<PERSON>t", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.guardian.flop": "<PERSON><PERSON>cht<PERSON> platscht", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmt Schaden", "subtitles.entity.happy_ghast.ambient": "Glücklicher Ghast trällert", "subtitles.entity.happy_ghast.death": "Glücklicher G<PERSON> stirbt", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "Glücklicher Ghast ist flugbereit", "subtitles.entity.happy_ghast.harness_goggles_up": "Glücklicher Ghast hält an", "subtitles.entity.happy_ghast.hurt": "Glücklicher Ghast nimm<PERSON>en", "subtitles.entity.happy_ghast.unequip": "Geschirr abgenommen", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>t", "subtitles.entity.hoglin.angry": "<PERSON><PERSON>n grunzt verärgert", "subtitles.entity.hoglin.attack": "<PERSON><PERSON>n greift an", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> wird zombifiziert", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.hoglin.retreat": "Hoglin weicht zurück", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> wiehert", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> wiehert", "subtitles.entity.horse.armor": "Rossharnis<PERSON> angelegt", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> schna<PERSON>t", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.horse.eat": "<PERSON><PERSON>d frisst", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON> galoppiert", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON> g<PERSON>t", "subtitles.entity.husk.ambient": "Wüstenzombie stöhnt", "subtitles.entity.husk.converted_to_zombie": "Wüstenzombie wird zu <PERSON>", "subtitles.entity.husk.death": "Wüstenzombie stirbt", "subtitles.entity.husk.hurt": "Wüstenzombie nimmt Schaden", "subtitles.entity.illusioner.ambient": "Illusionist mur<PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "Illusionist <PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.death": "Illusionist stirbt", "subtitles.entity.illusioner.hurt": "Illusionist <PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "Illusionist <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Illusionist bere<PERSON>t Blindheit vor", "subtitles.entity.illusioner.prepare_mirror": "Illusionist bereitet Projektion vor", "subtitles.entity.iron_golem.attack": "Eisengolem greift an", "subtitles.entity.iron_golem.damage": "Eisengolem wird beschädigt", "subtitles.entity.iron_golem.death": "Eisengolem stirbt", "subtitles.entity.iron_golem.hurt": "Eisengolem nimmt Schaden", "subtitles.entity.iron_golem.repair": "Eisengolem repariert", "subtitles.entity.item.break": "Gegenstand zerbricht", "subtitles.entity.item.pickup": "Gegenstand aufgehoben", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON>", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> an<PERSON>", "subtitles.entity.lightning_bolt.impact": "Blitz schlägt ein", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON> grollt", "subtitles.entity.llama.ambient": "<PERSON> bl<PERSON>", "subtitles.entity.llama.angry": "<PERSON> blökt ver<PERSON><PERSON>t", "subtitles.entity.llama.chest": "<PERSON> bepackt", "subtitles.entity.llama.death": "<PERSON> stirbt", "subtitles.entity.llama.eat": "<PERSON> frisst", "subtitles.entity.llama.hurt": "<PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON> spuckt", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Magmawürfel stirbt", "subtitles.entity.magma_cube.hurt": "Magmawürfel nimmt Schaden", "subtitles.entity.magma_cube.squish": "Magmawürfel schwabbelt", "subtitles.entity.minecart.inside": "<PERSON>re rattert", "subtitles.entity.minecart.inside_underwater": "<PERSON>re rattert unter Was<PERSON>", "subtitles.entity.minecart.riding": "Lore rollt", "subtitles.entity.mooshroom.convert": "Moosh<PERSON> verwandelt sich", "subtitles.entity.mooshroom.eat": "Mooshroom frisst", "subtitles.entity.mooshroom.milk": "Mooshroom wird gemolken", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom wird seltsam gemolken", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> wie<PERSON>t", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> be<PERSON>t", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> fris<PERSON>", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.painting.break": "Gemälde zerbricht", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON><PERSON> platz<PERSON>", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON> grollt", "subtitles.entity.panda.ambient": "<PERSON><PERSON> hechelt", "subtitles.entity.panda.bite": "Panda beißt", "subtitles.entity.panda.cant_breed": "Panda blökt", "subtitles.entity.panda.death": "Panda stirbt", "subtitles.entity.panda.eat": "Panda frisst", "subtitles.entity.panda.hurt": "<PERSON><PERSON> nimm<PERSON>", "subtitles.entity.panda.pre_sneeze": "Panda vers<PERSON><PERSON><PERSON>", "subtitles.entity.panda.sneeze": "Panda niest", "subtitles.entity.panda.step": "Panda lä<PERSON>t", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON> w<PERSON>", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> frisst", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON> flattert", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON> schwi<PERSON>t", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON> knarzt", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "Papagei krab<PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON> jam<PERSON>t", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON>i schwabbelt", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON>i schwabbelt", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON> p<PERSON>t", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> jam<PERSON>t", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.phantom.ambient": "Phantom kreischt", "subtitles.entity.phantom.bite": "<PERSON> beißt", "subtitles.entity.phantom.death": "Phantom stirbt", "subtitles.entity.phantom.flap": "Phantom flattert", "subtitles.entity.phantom.hurt": "Phantom nimmt <PERSON>", "subtitles.entity.phantom.swoop": "Phantom stößt herab", "subtitles.entity.pig.ambient": "<PERSON><PERSON>wein grunzt", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.pig.saddle": "Schwein gesattelt", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> bewundert Gegenstand", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> schna<PERSON>t", "subtitles.entity.piglin.angry": "<PERSON><PERSON> schnaubt verärgert", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feiert", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> wird zombifiziert", "subtitles.entity.piglin.death": "<PERSON><PERSON> stirbt", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> schna<PERSON>t ne<PERSON>", "subtitles.entity.piglin.retreat": "Piglin weicht zurück", "subtitles.entity.piglin.step": "<PERSON><PERSON> l<PERSON>t", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON>", "subtitles.entity.piglin_brute.angry": "Piglin-<PERSON><PERSON> schna<PERSON>t verärgert", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin-<PERSON><PERSON> wird zombifiziert", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON>-<PERSON><PERSON> stirbt", "subtitles.entity.piglin_brute.hurt": "Piglin-<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.piglin_brute.step": "Piglin-<PERSON><PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.pillager.celebrate": "Plünder<PERSON> jubelt", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.pillager.hurt": "Plünderer nimmt <PERSON>en", "subtitles.entity.player.attack.crit": "Volltreffer", "subtitles.entity.player.attack.knockback": "Rückstoßangriff", "subtitles.entity.player.attack.strong": "<PERSON><PERSON>", "subtitles.entity.player.attack.sweep": "Schwungangriff", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON> friert", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> brennt", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> bimmelt", "subtitles.entity.player.teleport": "Spieler teleportiert", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> knurrt", "subtitles.entity.polar_bear.ambient_baby": "Eisbärenjunges brummt", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>mm<PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "Flasche zerbricht", "subtitles.entity.potion.throw": "Flasche geworfen", "subtitles.entity.puffer_fish.blow_out": "Kugelfisch schrumpft", "subtitles.entity.puffer_fish.blow_up": "<PERSON>gel<PERSON><PERSON> bläst sich auf", "subtitles.entity.puffer_fish.death": "Kugelfisch stirbt", "subtitles.entity.puffer_fish.flop": "Kugelfisch platscht", "subtitles.entity.puffer_fish.hurt": "Kugelfisch nimmt Schaden", "subtitles.entity.puffer_fish.sting": "Kugelfisch sticht", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> greift an", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON> nimmt Schaden", "subtitles.entity.rabbit.jump": "Kaninchen hoppelt", "subtitles.entity.ravager.ambient": "Verwüster grunzt", "subtitles.entity.ravager.attack": "Verwüster beißt", "subtitles.entity.ravager.celebrate": "Verwüster jubelt", "subtitles.entity.ravager.death": "Verwüster stirbt", "subtitles.entity.ravager.hurt": "Verwüster nimmt Schaden", "subtitles.entity.ravager.roar": "Verwüster brüllt", "subtitles.entity.ravager.step": "Verwüster läuft", "subtitles.entity.ravager.stunned": "Verwüster betäubt", "subtitles.entity.salmon.death": "Lachs stirbt", "subtitles.entity.salmon.flop": "Lachs platscht", "subtitles.entity.salmon.hurt": "<PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> la<PERSON>t", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleportiert", "subtitles.entity.shulker_bullet.hit": "Shulker-Geschoss explodiert", "subtitles.entity.shulker_bullet.hurt": "Shulker-Geschoss zerbricht", "subtitles.entity.silverfish.ambient": "Silberfischchen zischt", "subtitles.entity.silverfish.death": "Silberfischchen stirbt", "subtitles.entity.silverfish.hurt": "Silberfischchen nimmt Schaden", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON> klap<PERSON>", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON> verwandelt sich zu <PERSON>wanderer", "subtitles.entity.skeleton.death": "Skelett stirbt", "subtitles.entity.skeleton.hurt": "Skelett nimmt Schaden", "subtitles.entity.skeleton.shoot": "Skelett schießt", "subtitles.entity.skeleton_horse.ambient": "Skelettpferd schreit", "subtitles.entity.skeleton_horse.death": "Skelettpferd stirbt", "subtitles.entity.skeleton_horse.hurt": "Skelettpferd nimmt Schaden", "subtitles.entity.skeleton_horse.jump_water": "Skelettpferd springt", "subtitles.entity.skeleton_horse.swim": "Skelettpferd schwimmt", "subtitles.entity.slime.attack": "Schleim greift an", "subtitles.entity.slime.death": "Schleim stirbt", "subtitles.entity.slime.hurt": "Schleim nimmt Schaden", "subtitles.entity.slime.squish": "Schleim schwabbelt", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.sniffer.digging": "Schnüffler wühlt", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON><PERSON> erhebt sich", "subtitles.entity.sniffer.drop_seed": "Schnüffler lässt Samen fallen", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.sniffer.egg_crack": "Schnüffler‐<PERSON>i knackt", "subtitles.entity.sniffer.egg_hatch": "Schnüfflerjunges schlüpft", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON> freut sich", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.sniffer.idle": "Schnüffler grunzt", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON><PERSON> wittert", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON><PERSON> sucht", "subtitles.entity.sniffer.sniffing": "Schnüffler schnüffelt", "subtitles.entity.sniffer.step": "Schnüffler läuft", "subtitles.entity.snow_golem.death": "Schneegolem stirbt", "subtitles.entity.snow_golem.hurt": "Schneegolem nimmt Sc<PERSON>en", "subtitles.entity.snowball.throw": "Schneeball fliegt", "subtitles.entity.spider.ambient": "<PERSON><PERSON>", "subtitles.entity.spider.death": "<PERSON><PERSON> stirbt", "subtitles.entity.spider.hurt": "<PERSON><PERSON> nimm<PERSON>", "subtitles.entity.squid.ambient": "Tintenfisch schwimmt", "subtitles.entity.squid.death": "Tintenfisch stirbt", "subtitles.entity.squid.hurt": "Tintenfisch nimmt Schaden", "subtitles.entity.squid.squirt": "Tintenfisch verspritzt Tinte", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "Eiswanderer stirbt", "subtitles.entity.stray.hurt": "Eiswanderer nimmt <PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON><PERSON> fris<PERSON>", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON><PERSON> trällert", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmt Schaden", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>er zirpt", "subtitles.entity.strider.retreat": "Schreiter weicht zurück", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON><PERSON> wächst heran", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "subtitles.entity.tnt.primed": "TNT zischt", "subtitles.entity.tropical_fish.death": "Tropenfisch stirbt", "subtitles.entity.tropical_fish.flop": "Tropenfisch zappelt", "subtitles.entity.tropical_fish.hurt": "Tropenfisch nimmt Schaden", "subtitles.entity.turtle.ambient_land": "Schildkröte fiept", "subtitles.entity.turtle.death": "<PERSON><PERSON>dkr<PERSON><PERSON> stirbt", "subtitles.entity.turtle.death_baby": "Schildkrötenjunges stirbt", "subtitles.entity.turtle.egg_break": "Schildkrötenei zerbricht", "subtitles.entity.turtle.egg_crack": "Schildkrötenei knackt", "subtitles.entity.turtle.egg_hatch": "Schildkrötenjunges schlüpft", "subtitles.entity.turtle.hurt": "Schildkrö<PERSON> nimm<PERSON>", "subtitles.entity.turtle.hurt_baby": "Schildkrötenjunges nimmt Schaden", "subtitles.entity.turtle.lay_egg": "Schildkröte legt Ei", "subtitles.entity.turtle.shamble": "Schildkrö<PERSON> wats<PERSON>t", "subtitles.entity.turtle.shamble_baby": "Schildkrötenjunges watschelt", "subtitles.entity.turtle.swim": "Schildkröte schwimmt", "subtitles.entity.vex.ambient": "Plagege<PERSON> plagt", "subtitles.entity.vex.charge": "Plagegeist schreit", "subtitles.entity.vex.death": "Plagegeist stirbt", "subtitles.entity.vex.hurt": "Plagegeist nimm<PERSON>", "subtitles.entity.villager.ambient": "Dorfbewohner murmelt", "subtitles.entity.villager.celebrate": "Dorfbewohner jubelt", "subtitles.entity.villager.death": "Dorfbewohner stirbt", "subtitles.entity.villager.hurt": "Dorfbewohner nimmt Schaden", "subtitles.entity.villager.no": "Dorfbewohner lehnt ab", "subtitles.entity.villager.trade": "Dorfbewohner handelt", "subtitles.entity.villager.work_armorer": "Panzermacher arbeitet", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON> arbeitet", "subtitles.entity.villager.work_cartographer": "Kartograf arbeit<PERSON>", "subtitles.entity.villager.work_cleric": "Geistlicher arbeitet", "subtitles.entity.villager.work_farmer": "Bauer arbeitet", "subtitles.entity.villager.work_fisherman": "Fischer arbeitet", "subtitles.entity.villager.work_fletcher": "Pfeilmacher arbeitet", "subtitles.entity.villager.work_leatherworker": "Gerber arbeitet", "subtitles.entity.villager.work_librarian": "Bibliothekar arbeitet", "subtitles.entity.villager.work_mason": "Maurer arbeitet", "subtitles.entity.villager.work_shepherd": "Schäfer arbeitet", "subtitles.entity.villager.work_toolsmith": "Grobschmied arbeitet", "subtitles.entity.villager.work_weaponsmith": "Waffenschmied arbeitet", "subtitles.entity.villager.yes": "Dorfbewohner stimmt zu", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON> murmelt", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON> jubelt", "subtitles.entity.vindicator.death": "<PERSON>ner stirbt", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON> nimmt <PERSON>en", "subtitles.entity.wandering_trader.ambient": "Fahrender Händler murmelt", "subtitles.entity.wandering_trader.death": "Fahrender Händler stirbt", "subtitles.entity.wandering_trader.disappeared": "Fahrender Händler verschwindet", "subtitles.entity.wandering_trader.drink_milk": "Fahrender Händler trinkt Milch", "subtitles.entity.wandering_trader.drink_potion": "Fahrender Händler trinkt Trank", "subtitles.entity.wandering_trader.hurt": "Fahrender Händler nimmt Schaden", "subtitles.entity.wandering_trader.no": "Fahrender Händler lehnt ab", "subtitles.entity.wandering_trader.reappeared": "Fahrender Händler erscheint", "subtitles.entity.wandering_trader.trade": "Fahrender Händler handelt", "subtitles.entity.wandering_trader.yes": "Fahrender Händler stimmt zu", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON> knurrt verärgert", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON> jammert", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON> tobt", "subtitles.entity.warden.attack_impact": "Wärter landet Treffer", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> gräbt", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON> taucht auf", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON><PERSON><PERSON> schl<PERSON>gt", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> nimmt Schaden", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> horcht auf", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> horcht verärgert auf", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON> n<PERSON>hert sich", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON> rückt vor", "subtitles.entity.warden.nearby_closest": "W<PERSON>rter naht", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> knallt", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> lädt auf", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON> läuft", "subtitles.entity.warden.tendril_clicks": "Wärterranken klicken", "subtitles.entity.wind_charge.throw": "<PERSON>kugel fliegt", "subtitles.entity.wind_charge.wind_burst": "Windkugel platzt", "subtitles.entity.witch.ambient": "Hexe kichert", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> jubelt", "subtitles.entity.witch.death": "He<PERSON> stirbt", "subtitles.entity.witch.drink": "Hexe trinkt", "subtitles.entity.witch.hurt": "<PERSON><PERSON> nimmt Schaden", "subtitles.entity.witch.throw": "Hexe wirft", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON>er stirbt", "subtitles.entity.wither.hurt": "<PERSON><PERSON> ni<PERSON><PERSON>", "subtitles.entity.wither.shoot": "Wither greift an", "subtitles.entity.wither.spawn": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klappert", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON>Skelett stirbt", "subtitles.entity.wither_skeleton.hurt": "With<PERSON><PERSON>Skelett nimmt Schaden", "subtitles.entity.wolf.ambient": "<PERSON> hechelt", "subtitles.entity.wolf.bark": "<PERSON> bellt", "subtitles.entity.wolf.death": "<PERSON> stirbt", "subtitles.entity.wolf.growl": "<PERSON> knurrt", "subtitles.entity.wolf.hurt": "<PERSON> nimmt <PERSON>", "subtitles.entity.wolf.pant": "<PERSON> hechelt", "subtitles.entity.wolf.shake": "<PERSON> sch<PERSON> sich", "subtitles.entity.wolf.whine": "<PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>zt", "subtitles.entity.zoglin.angry": "Zoglin grunzt verärgert", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> greift an", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON> stöhnt", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> wird ger<PERSON><PERSON>lt", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "Zombie wird zu <PERSON>runk<PERSON>m", "subtitles.entity.zombie.death": "Zombie stirbt", "subtitles.entity.zombie.destroy_egg": "Schildkrötenei zertreten", "subtitles.entity.zombie.hurt": "<PERSON> nimmt <PERSON>", "subtitles.entity.zombie.infect": "Zombie infiziert", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> schreit", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.zombie_villager.ambient": "Zombiedorfbewohner stöhnt", "subtitles.entity.zombie_villager.converted": "Zombiedorfbewohner geheilt", "subtitles.entity.zombie_villager.cure": "Zombiedorfbewohner zischt", "subtitles.entity.zombie_villager.death": "Zombiedorfbewohner stirbt", "subtitles.entity.zombie_villager.hurt": "Zombiedorfbewohner nimmt Schaden", "subtitles.entity.zombified_piglin.ambient": "Zombifizierter Piglin grunzt", "subtitles.entity.zombified_piglin.angry": "Zombifizierter Piglin grunzt verärgert", "subtitles.entity.zombified_piglin.death": "Zombifizierter Piglin stirbt", "subtitles.entity.zombified_piglin.hurt": "Zombifizierter Piglin nimmt Schaden", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON>il erg<PERSON>", "subtitles.event.mob_effect.raid_omen": "Überfall naht", "subtitles.event.mob_effect.trial_omen": "Unheilvolle Prüfung naht", "subtitles.event.raid.horn": "Unheilvolles Horn erschallt", "subtitles.item.armor.equip": "Ausrüstung angelegt", "subtitles.item.armor.equip_chain": "Kettenrüstung rasselt", "subtitles.item.armor.equip_diamond": "Diamantrüstung klirrt", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> rascheln", "subtitles.item.armor.equip_gold": "Goldrüstung klimpert", "subtitles.item.armor.equip_iron": "Eisenrüstung scheppert", "subtitles.item.armor.equip_leather": "Lederrüstung knirscht", "subtitles.item.armor.equip_netherite": "Netheritrüstung scheppert", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON>dkr<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.item.armor.equip_wolf": "Wolfspanzer festgezurrt", "subtitles.item.armor.unequip_wolf": "Wolfspanzer abgestreift", "subtitles.item.axe.scrape": "<PERSON><PERSON>t schabt", "subtitles.item.axe.strip": "<PERSON><PERSON>t schabt", "subtitles.item.axe.wax_off": "Entwachsen", "subtitles.item.bone_meal.use": "Knochenmehl rieselt", "subtitles.item.book.page_turn": "Seite raschelt", "subtitles.item.book.put": "Buch klatscht", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON> gel<PERSON>t", "subtitles.item.bottle.fill": "Flasche gefüllt", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Kiesabpinseln", "subtitles.item.brush.brushing.gravel.complete": "Kiesabpinseln abgeschlossen", "subtitles.item.brush.brushing.sand": "Sandabpinseln", "subtitles.item.brush.brushing.sand.complete": "Sandabpinseln abgeschlossen", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl aufgenommen", "subtitles.item.bucket.fill_fish": "<PERSON>sch eingefangen", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON><PERSON> entleert", "subtitles.item.bundle.insert": "Gegenstand eingepackt", "subtitles.item.bundle.insert_fail": "Bündel voll", "subtitles.item.bundle.remove_one": "Gegenstand ausgepackt", "subtitles.item.chorus_fruit.teleport": "Spieler teleportiert", "subtitles.item.crop.plant": "Pflanze gepflanzt", "subtitles.item.crossbow.charge": "<PERSON><PERSON><PERSON> spannt", "subtitles.item.crossbow.hit": "Pfeil trifft", "subtitles.item.crossbow.load": "Armbrust lädt", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Feuerkugel zischt", "subtitles.item.flintandsteel.use": "Feuerzeug zündet", "subtitles.item.glow_ink_sac.use": "Leuchttintenbeutel kleckst", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON> spielt", "subtitles.item.hoe.till": "<PERSON><PERSON> pflügt", "subtitles.item.honey_bottle.drink": "Schlucken", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "Rossharnisch abgestreift", "subtitles.item.ink_sac.use": "Tintenbe<PERSON>l kleckst", "subtitles.item.lead.break": "<PERSON><PERSON> re<PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON> an<PERSON>", "subtitles.item.lead.untied": "<PERSON><PERSON>", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON>ich abgestreift", "subtitles.item.lodestone_compass.lock": "Kompass auf Leitstein ausgerichtet", "subtitles.item.mace.smash_air": "Streitkolben schmettert", "subtitles.item.mace.smash_ground": "Streitkolben schmettert", "subtitles.item.nether_wart.plant": "Pflanze gepflanzt", "subtitles.item.ominous_bottle.dispose": "Flasche zerbricht", "subtitles.item.saddle.unequip": "Sattel abgestreift", "subtitles.item.shears.shear": "<PERSON><PERSON> schne<PERSON>t", "subtitles.item.shears.snip": "<PERSON><PERSON> schne<PERSON>t", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON> wehrt ab", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON><PERSON> fährt ein", "subtitles.item.spyglass.use": "Fernrohr fährt aus", "subtitles.item.totem.use": "Totem ausgelöst", "subtitles.item.trident.hit": "Dreizack spießt auf", "subtitles.item.trident.hit_ground": "Dreizack vibriert", "subtitles.item.trident.return": "Dreizack kehrt zurück", "subtitles.item.trident.riptide": "Dreizack zieht", "subtitles.item.trident.throw": "Dreizack klirrt", "subtitles.item.trident.thunder": "Dreizackdonner grollt", "subtitles.item.wolf_armor.break": "Wolfspanzer zerbricht", "subtitles.item.wolf_armor.crack": "Wolfspanzer wird brüchig", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON> beschädigt", "subtitles.item.wolf_armor.repair": "Wolfspanzer repariert", "subtitles.particle.soul_escape": "<PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON> g<PERSON>", "subtitles.ui.hud.bubble_pop": "Luftvorrat nimmt ab", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.weather.rain": "<PERSON><PERSON> p<PERSON>t", "symlink_warning.message": "Welten aus Ordnern mit symbolischen Verknüpfungen zu laden, stellt ein Risi<PERSON>, wenn du dich nicht genau damit auskennst. Bitte besuche %s, um mehr zu erfahren.", "symlink_warning.message.pack": "Pakete mit symbolischen Verknüpfungen zu laden, stellt ein Risi<PERSON>, wenn du dich nicht genau damit auskennst. Bitte besuche %s, um mehr zu erfahren.", "symlink_warning.message.world": "Welten aus Ordnern mit symbolischen Verknüpfungen zu laden, stellt ein Risi<PERSON>, wenn du dich nicht genau damit auskennst. Bitte besuche %s, um mehr zu erfahren.", "symlink_warning.more_info": "Weitere Informationen", "symlink_warning.title": "Weltordner enthält symbolische Verknüpfungen", "symlink_warning.title.pack": "Hinzugefügte(s) Paket(e) enthält/enthalten symbolische Verknüpfungen", "symlink_warning.title.world": "Weltordner enthält symbolische Verknüpfungen", "team.collision.always": "Immer", "team.collision.never": "<PERSON><PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON><PERSON> andere Teammitglieder", "team.collision.pushOwnTeam": "Schiebe eigene Teammitglieder", "team.notFound": "Unbekanntes Team ‚%s‘", "team.visibility.always": "Immer", "team.visibility.hideForOtherTeams": "Für andere Teams verstecken", "team.visibility.hideForOwnTeam": "F<PERSON>r das eigene Team verstecken", "team.visibility.never": "<PERSON><PERSON>", "telemetry.event.advancement_made.description": "<PERSON><PERSON> er<PERSON>, unter welchen Gegebenheiten Fortschritte erzielt werden, kann uns dabei helfen, den Spielverlauf besser zu verstehen und ihn zu verbessern.", "telemetry.event.advancement_made.title": "Fortschritt erzielt", "telemetry.event.game_load_times.description": "<PERSON><PERSON> kann uns helfen, her<PERSON><PERSON><PERSON><PERSON>, wo Verbesserungen des Startvorgangs notwendig sind, indem es die Ausführungszeiten der einzelnen Startphasen misst.", "telemetry.event.game_load_times.title": "Spielladezeiten", "telemetry.event.optional": "%s (optional)", "telemetry.event.optional.disabled": "%s (optional) – <PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.event.performance_metrics.description": "Die genaue Ermittlung des allgemeinen Leistungsprofils von Minecraft hilft uns, das Spiel an eine Vielzahl von Geräteeigenschaften und Betriebssystemen abzustimmen und dafür zu optimieren. \nDie Angabe der Spielversion hilft uns, das Leistungsprofil für neue Versionen von Minecraft zu vergleichen.", "telemetry.event.performance_metrics.title": "Leistungsmetriken", "telemetry.event.required": "%s (erforderlich)", "telemetry.event.world_load_times.description": "<PERSON><PERSON> ist wich<PERSON>g, dass wir verstehen, wie lange es dauert, eine Welt zu betreten, und wie sich das im Lauf der Zeit ändert. Wenn wir beispielsweise neue Spielelemente hinzufügen oder umfassende technische Änderungen vornehmen, müssen wir wissen, wie sich das auf die Ladezeiten auswirkt.", "telemetry.event.world_load_times.title": "Weltladezeiten", "telemetry.event.world_loaded.description": "<PERSON><PERSON> <PERSON>, wie Spieler Minecraft spielen (z. B. <PERSON>lmodus, Spielversion und modifizierter Client bzw. Server), erlaubt es uns, Spielaktualisierungen auf die Bereiche auszurichten, die ihnen von Bedeutung sind.\nDas Ereignis „Welt geladen“ ist mit dem Ereignis „Welt entladen“ gekoppelt, um zu berechnen, wie lange die Spielsitzung gedauert hat.", "telemetry.event.world_loaded.title": "Welt geladen", "telemetry.event.world_unloaded.description": "Dieses <PERSON>reign<PERSON> ist mit dem Ereignis „Welt geladen“ gek<PERSON><PERSON><PERSON>, um zu berechnen, wie lange die Weltsitzung gedauert hat.\nWenn eine Weltsitzung beendet wurde (Verlassen der Welt bzw. Trennen der Verbindung zu einem Server), wird die Dauer (in Sekunden und Ticks) gemessen.", "telemetry.event.world_unloaded.title": "Welt entladen", "telemetry.property.advancement_game_time.title": "Spielzeit (Ticks)", "telemetry.property.advancement_id.title": "Fortschritts‐ID", "telemetry.property.client_id.title": "Client‐ID", "telemetry.property.client_modded.title": "Client modifiziert", "telemetry.property.dedicated_memory_kb.title": "Zugewiesener Arbeitsspeicher (KB)", "telemetry.property.event_timestamp_utc.title": "Zeitpunkt des Ereignisses (koordinierte Weltzeit)", "telemetry.property.frame_rate_samples.title": "Datenwerte zur Bildwiederholrate (FPS)", "telemetry.property.game_mode.title": "Spielm<PERSON>", "telemetry.property.game_version.title": "Spielversion", "telemetry.property.launcher_name.title": "Launcher‐Name", "telemetry.property.load_time_bootstrap_ms.title": "Initialisierungszeit (Millisekunden)", "telemetry.property.load_time_loading_overlay_ms.title": "Zeit im Ladebildschirm (Millisekunden)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON><PERSON>, bevor sich das Spielfenster öffnet (Millisekunden)", "telemetry.property.load_time_total_time_ms.title": "Gesamte Ladezeit (Millisekunden)", "telemetry.property.minecraft_session_id.title": "Minecraft‐Sitzungs‐ID", "telemetry.property.new_world.title": "Neue Welt", "telemetry.property.number_of_samples.title": "Anzahl der Datenwerte", "telemetry.property.operating_system.title": "Betriebssystem", "telemetry.property.opt_in.title": "Erforderliches/optionales <PERSON>", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Realms‐Weltinhalt (Name des Minispiels)", "telemetry.property.render_distance.title": "Sichtweite", "telemetry.property.render_time_samples.title": "Datenwerte zur Renderzeit", "telemetry.property.seconds_since_load.title": "Zeit seit dem Laden (Sekunden)", "telemetry.property.server_modded.title": "Server modifiziert", "telemetry.property.server_type.title": "Servertyp", "telemetry.property.ticks_since_load.title": "Zeit seit dem Laden (Ticks)", "telemetry.property.used_memory_samples.title": "Verwendeter Arbeitsspeicher", "telemetry.property.user_id.title": "Benutzer‐ID", "telemetry.property.world_load_time_ms.title": "Weltladezeit (Millisekunden)", "telemetry.property.world_session_id.title": "Welt‐Sitzungs‐ID", "telemetry_info.button.give_feedback": "Feedback geben", "telemetry_info.button.privacy_statement": "Datenschutzerklärung", "telemetry_info.button.show_data": "<PERSON><PERSON> e<PERSON>ehen", "telemetry_info.opt_in.description": "Ich stimme der Übermittlung optionaler Telemetriedaten zu", "telemetry_info.property_title": "Enthaltene Daten", "telemetry_info.screen.description": "Die Erhebung dieser Daten hilft uns, Minecraft zu verbessern, indem wir uns damit in die Richtungen weisen lassen, die unseren Spielern wichtig sind.\nDu kannst auch zusätzliches Feedback abgeben, damit wir Minecraft weiter verbessern können.", "telemetry_info.screen.title": "Telemetrie‐Datenerhebung", "test.error.block_property_mismatch": "Zustand %s wurde als %s erwartet, war aber %s", "test.error.block_property_missing": "Blockeigenschaft nicht vorhanden, Eigenschaft %s wurde als %s erwartet", "test.error.entity_property": "Objekt %s erfüllte Test nicht: %s", "test.error.entity_property_details": "Objekt %s hat Test %s nicht erfüllt: %s erwartet, war aber %s", "test.error.expected_block": "Block %s erwartet, war aber %s", "test.error.expected_block_tag": "Block wurde in #%s erwartet, war aber %s", "test.error.expected_container_contents": "<PERSON><PERSON><PERSON><PERSON> muss enthalten: %s", "test.error.expected_container_contents_single": "<PERSON><PERSON><PERSON><PERSON> muss ein %s enthalten", "test.error.expected_empty_container": "<PERSON><PERSON><PERSON><PERSON> muss leer sein", "test.error.expected_entity": "%s erwartet", "test.error.expected_entity_around": "%s wurde bei %s, %s, %s erwartet", "test.error.expected_entity_count": "%s Objekte vom Typ %s erwartet, %s gefunden", "test.error.expected_entity_data": "Objektdaten %s wurden erwartet, waren aber %s", "test.error.expected_entity_data_predicate": "Objektdaten für %s stimmen nicht überein", "test.error.expected_entity_effect": "Effekt für %s wurde als %s %s erwartet", "test.error.expected_entity_having": "Objektinventar hätte %s enthalten sollen", "test.error.expected_entity_holding": "Objekt hätte %s halten sollen", "test.error.expected_entity_in_test": "Erwartete %s im Test", "test.error.expected_entity_not_touching": "%s wurde nicht als an %s, %s, %s angrenzend erwartet (relativ: %s, %s, %s)", "test.error.expected_entity_touching": "%s wurde als an %s, %s, %s angrenzend erwartet (relativ: %s, %s, %s)", "test.error.expected_item": "Gegenstand vom Typ %s erwartet", "test.error.expected_items_count": "%s Gegenstände vom Typ %s erwartet, aber %s gefunden", "test.error.fail": "Fehlkriterien erfüllt", "test.error.invalid_block_type": "Unerwarteter Blocktyp gefunden: %s", "test.error.missing_block_entity": "Fehlendes Blockobjekt", "test.error.position": "%s bei %s, %s, %s (relativ: %s, %s, %s) in Tick %s", "test.error.sequence.condition_already_triggered": "Bedingung bereits in Tick %s ausgelöst", "test.error.sequence.condition_not_triggered": "Bedingung nicht ausgelöst", "test.error.sequence.invalid_tick": "In ungültigem Tick stattgefunden: %s wurde erwartet", "test.error.sequence.not_completed": "Test vor Abschluss des Vorgangs abgelaufen", "test.error.set_biome": "Biom für Test konnte nicht festgelegt werden", "test.error.spawn_failure": "Objekt %s konnte nicht erzeugt werden", "test.error.state_not_equal": "Falscher Zustand. %s erwartet, war %s", "test.error.structure.failure": "Testkonstruktion für %s konnte nicht platziert werden", "test.error.tick": "%s in Tick %s", "test.error.ticking_without_structure": "Testausführung begann, bevor Konstruktion platziert wurde", "test.error.timeout.no_result": "Weder Erfolg noch Fehlschlag innerhalb von %s Ticks", "test.error.timeout.no_sequences_finished": "<PERSON><PERSON> Testabläufe innerhalb von %s Ticks abgeschlossen", "test.error.too_many_entities": "Nur ein %s um %s, %s, %s erwartet, aber %s gefunden", "test.error.unexpected_block": "Block wurde nicht als %s erwartet", "test.error.unexpected_entity": "<PERSON><PERSON> erwartet, %s vorzufinden", "test.error.unexpected_item": "Gegenstand vom Typ %s wurde nicht erwartet", "test.error.unknown": "Unbekannter interner <PERSON><PERSON>: %s", "test.error.value_not_equal": "%s wurde als %s erwartet, war aber %s", "test.error.wrong_block_entity": "Falscher Blockobjekttyp: %s", "test_block.error.missing": "Testkonstruktion hat keinen %s‐Block", "test_block.error.too_many": "Zu viele %s‐Blöcke", "test_block.invalid_timeout": "Ungültiges Timeout (%s) – muss eine positive <PERSON><PERSON><PERSON> von <PERSON> sein", "test_block.message": "Nachricht:", "test_block.mode.accept": "Akzeptieren", "test_block.mode.fail": "Fehlschlag", "test_block.mode.log": "Protokoll", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Akzeptanzmodus – <PERSON>mmt (teilweisen) Erfolg eines Tests entgegen", "test_block.mode_info.fail": "Fehlermodus – Lässt den Test fehlschlagen", "test_block.mode_info.log": "Protokollmodus – Protokolliere eine Nachricht", "test_block.mode_info.start": "Startmodus – Der Anfangspunkt eines Tests", "test_instance.action.reset": "Zurücksetzen und laden", "test_instance.action.run": "Laden und ausführen", "test_instance.action.save": "Konstruktion speichern", "test_instance.description.batch": "Teil: %s", "test_instance.description.failed": "Fehlgeschlagen: %s", "test_instance.description.function": "Funktion: %s", "test_instance.description.invalid_id": "Ungültige Test‐ID", "test_instance.description.no_test": "Dieser Test existiert nicht", "test_instance.description.structure": "Konstruktion: %s", "test_instance.description.type": "Typ: %s", "test_instance.type.block_based": "Blockbasierter Test", "test_instance.type.function": "Integrierter Funktionstest", "test_instance_block.entities": "Objekte:", "test_instance_block.error.no_test": "Testinstanz bei %s, %s, %s kann nicht ausgeführt werden, da ihr kein Test zugewiesen ist", "test_instance_block.error.no_test_structure": "Testinstanz bei %s, %s, %s konnte nicht ausgeführt werden, da ihre Testkonstruktion fehlt", "test_instance_block.error.unable_to_save": "Testkonstruktionsvorlage für Testinstanz bei %s, %s, %s kann nicht gespeichert werden", "test_instance_block.invalid": "[ung<PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Test %s wurde erfolgreich zurückgesetzt", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Größe der Testkonstruktion", "test_instance_block.starting": "Test %s wird gestartet", "test_instance_block.test_id": "Testinstanz‐ID", "title.32bit.deprecation": "32‐Bit‐System erkannt: Dies kann dich daran hindern, in Zukunft zu spielen, da ein 64‐Bit‐System erforderlich ist!", "title.32bit.deprecation.realms": "Minecraft wird bald ein 64‐Bit‐System benötigen, wodurch du Realms auf diesem Gerät nicht mehr spielen oder verwenden kannst. Du musst jedes Realms‐Abonnement manuell kündigen.", "title.32bit.deprecation.realms.check": "<PERSON><PERSON> Hinwei<PERSON> nicht mehr anzeigen", "title.32bit.deprecation.realms.header": "32‐Bit‐System erkannt", "title.credits": "Copyright Mojang AB. Nicht verbreiten!", "title.multiplayer.disabled": "Der Mehrspielermodus ist deaktiviert. Bitte überprüfe die Einstellungen deines Microsoft‐Kontos.", "title.multiplayer.disabled.banned.name": "Du musst deinen Profilnamen ändern, bevor du online spielen kannst", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON> ist dauerhaft vom Online‐Spiel ausgeschlossen", "title.multiplayer.disabled.banned.temporary": "<PERSON><PERSON> ist vorübergehend vom Online‐Spiel ausgeschlossen", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Drittanbieter-Server)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Einzelspieler", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "Hi %", "translation.test.invalid2": "Hi %s", "translation.test.none": "Hallo, Welt!", "translation.test.world": "Welt", "trim_material.minecraft.amethyst": "Amethyst", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragd", "trim_material.minecraft.gold": "Gold", "trim_material.minecraft.iron": "Eisen", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Netherit", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstone", "trim_material.minecraft.resin": "<PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Bolzen-Rüstungsbesatz", "trim_pattern.minecraft.coast": "Küsten-Rüstungsbesatz", "trim_pattern.minecraft.dune": "Dünen-Rüstungsbesatz", "trim_pattern.minecraft.eye": "Augen-Rüstungsbesatz", "trim_pattern.minecraft.flow": "Fluss-Rüstungsbesatz", "trim_pattern.minecraft.host": "Gastwirts-Rüstungsbesatz", "trim_pattern.minecraft.raiser": "Aufzieher-Rüstungsbesatz", "trim_pattern.minecraft.rib": "Rippen-Rüstungsbesatz", "trim_pattern.minecraft.sentry": "Wachen-Rüstungsbesatz", "trim_pattern.minecraft.shaper": "Gestalter-Rüstungsbesatz", "trim_pattern.minecraft.silence": "Stille-Rüstungsbesatz", "trim_pattern.minecraft.snout": "Schnauzen-Rüstungsbesatz", "trim_pattern.minecraft.spire": "Turmspitzen-Rüstungsbesatz", "trim_pattern.minecraft.tide": "Gezeiten-Rüstungsbesatz", "trim_pattern.minecraft.vex": "Plagegeister-Rüstungsbesatz", "trim_pattern.minecraft.ward": "Warthof-Rüstungsbesatz", "trim_pattern.minecraft.wayfinder": "Wegfinder-Rüstungsbesatz", "trim_pattern.minecraft.wild": "Wildnis-Rüstungsbesatz", "tutorial.bundleInsert.description": "Rechtsklick zum Befüllen", "tutorial.bundleInsert.title": "Verwende ein Bündel", "tutorial.craft_planks.description": "Das Rezeptbuch hilft dir", "tutorial.craft_planks.title": "<PERSON><PERSON>tter her", "tutorial.find_tree.description": "Schlage ihn für Holz", "tutorial.find_tree.title": "<PERSON><PERSON> e<PERSON>", "tutorial.look.description": "Bewege die Maus", "tutorial.look.title": "<PERSON>eh dich um", "tutorial.move.description": "Springe mit %s", "tutorial.move.title": "Laufe mit %s, %s, %s und %s", "tutorial.open_inventory.description": "Drücke %s", "tutorial.open_inventory.title": "Öffne dein Inventar", "tutorial.punch_tree.description": "Halte %s fest", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON>", "tutorial.socialInteractions.description": "Drücke %s zum Öffnen", "tutorial.socialInteractions.title": "Soziale Interaktionen", "upgrade.minecraft.netherite_upgrade": "Netheritaufwertung"}