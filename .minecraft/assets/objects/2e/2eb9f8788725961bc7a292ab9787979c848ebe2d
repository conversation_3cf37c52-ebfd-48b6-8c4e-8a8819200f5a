{"accessibility.onboarding.accessibility.button": "Optiones accessabilitatis...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON> ut narratore utaris", "accessibility.onboarding.screen.title": "Salve!\n\nIn Minecraft advenisti. Visne narratore uti vel optiones accessibilitatis inspicere?", "addServer.add": "Serva exique", "addServer.enterIp": "<PERSON>us moderatri", "addServer.enterName": "Nomen moderatri", "addServer.resourcePack": "Compilationes supplementorum moderatri", "addServer.resourcePack.disabled": "Inactivum", "addServer.resourcePack.enabled": "Activum", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.title": "<PERSON>ta <PERSON>em moderatri", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Modus", "advMode.mode.auto": "Iterans", "advMode.mode.autoexec.bat": "Semper effectus", "advMode.mode.conditional": "Condicionalis", "advMode.mode.redstone": "<PERSON><PERSON> pulsus", "advMode.mode.redstoneTriggered": "Redstone requirens", "advMode.mode.sequence": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "Absol<PERSON><PERSON>", "advMode.notAllowed": "Debes esse lusor operans in modo creatiuo", "advMode.notEnabled": "<PERSON>ubi iussorum in hoc moderatro permissi non sunt", "advMode.previousOutput": "Effectus prior", "advMode.setCommand": "Pone iussum pro cubo", "advMode.setCommand.success": "Iussum positum: %s", "advMode.trackOutput": "Nota effectum", "advMode.triggering": "Effectio", "advMode.type": "Genus", "advancement.advancementNotFound": "Hoc incrementum ignotum est: %s", "advancements.adventure.adventuring_time.description": "Explora omnia biomata", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON>s iactatus et alto", "advancements.adventure.arbalistic.description": "Una quinque mobilia manuballista interfice", "advancements.adventure.arbalistic.title": "Arbalis<PERSON>", "advancements.adventure.avoid_vibration.description": "Iuxta sculk sentiens vel vigilem furtim ambula ne te sentiat", "advancements.adventure.avoid_vibration.title": "Tutum silentii premium", "advancements.adventure.blowback.description": "Ventiferum missile ventoso suo deflexo interfice", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON> secundus", "advancements.adventure.brush_armadillo.description": "Squamas dasypodis e dasypode peniculo cape", "advancements.adventure.brush_armadillo.title": "Squama non squala?", "advancements.adventure.bullseye.description": "Centrum pali sagitta feri ut abes triginta aut plus metrorum", "advancements.adventure.bullseye.title": "Destinata feritur", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fac vas pictum ex quattuor testis", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Fictilium fictio", "advancements.adventure.crafters_crafting_crafters.description": "Sta prope fabricator cum fabricaret fabricatorem", "advancements.adventure.crafters_crafting_crafters.title": "Fabricatores fabricatores fabricant", "advancements.adventure.fall_from_world_height.description": "De summo mundo (i.e. de limite aedificationis) ad imum mundum cadere supervive", "advancements.adventure.fall_from_world_height.title": "Speluncae et scopuli", "advancements.adventure.heart_transplanter.description": "Pone cordem crepacis cum constitutione recta inter duo truncos quercos pallidae", "advancements.adventure.heart_transplanter.title": "Translator cordis", "advancements.adventure.hero_of_the_village.description": "Defende prospere vicum ab incursione", "advancements.adventure.hero_of_the_village.title": "Vindex vici", "advancements.adventure.honey_block_slide.description": "Cade propter cubum mellis ut tenerius solum contingas", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON><PERSON> in fundo", "advancements.adventure.kill_a_mob.description": "Interfice quodlibet monstrum hostile", "advancements.adventure.kill_a_mob.title": "Monstrorum venator", "advancements.adventure.kill_all_mobs.description": "Interfice unum omnium monstrorum hostilium", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON> venantur", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Iuxta sculk mutans mobile interfice", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Serpit", "advancements.adventure.lighten_up.description": "Scabe lumen cupreum secure ut plus illuminaret", "advancements.adventure.lighten_up.title": "Notio clara", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Custodi vicanum ab inopinato concussu sine igne incipiendo", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON> sed intactus", "advancements.adventure.minecraft_trials_edition.description": "Ingredere conclave periculorum", "advancements.adventure.minecraft_trials_edition.title": "<PERSON><PERSON><PERSON> periculari", "advancements.adventure.ol_betsy.description": "Iace sagittam man<PERSON>", "advancements.adventure.ol_betsy.title": "Ballistarius", "advancements.adventure.overoverkill.description": "Inflige L corda vulneris uno ictu clavae", "advancements.adventure.overoverkill.title": "Decidi et occidi", "advancements.adventure.play_jukebox_in_meadows.description": "Fac ut prata sono musicae e phonographo vivescant", "advancements.adventure.play_jukebox_in_meadows.title": "Musica pellite curas", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Lege signum potentiae ab Armario Sculpto adhibens comparatorem", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Potentia librorum", "advancements.adventure.revaulting.description": "Resera arcam praesagam clave praesaga", "advancements.adventure.revaulting.title": "Arca arcana", "advancements.adventure.root.description": "Itinera exploratio pugnaeque", "advancements.adventure.root.title": "Expeditio", "advancements.adventure.salvage_sherd.description": "Peniculo testam ex cubo suspecto extrahe", "advancements.adventure.salvage_sherd.title": "Reliquiae maiorum", "advancements.adventure.shoot_arrow.description": "Aliquid sagitta ice", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "In lecto dorme ut locum renascendi tuum mutes", "advancements.adventure.sleep_in_bed.title": "Somnia dulcia", "advancements.adventure.sniper_duel.description": "Occide sceletum qui absit quinquaginta aut plus metrorum", "advancements.adventure.sniper_duel.title": "Certamen sagittariorum", "advancements.adventure.spyglass_at_dragon.description": "Telescopio adspice draconem Ender", "advancements.adventure.spyglass_at_dragon.title": "E<PERSON>ne maior avis?", "advancements.adventure.spyglass_at_ghast.description": "Telescopio adspice ghast", "advancements.adventure.spyglass_at_ghast.title": "E<PERSON>ne nubes?", "advancements.adventure.spyglass_at_parrot.description": "Telescopio adspice psittacum", "advancements.adventure.spyglass_at_parrot.title": "<PERSON><PERSON><PERSON> avis?", "advancements.adventure.summon_iron_golem.description": "Arcesse automatum ferreum ut vicum tueatur", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Eice tridentem ad aliquid.\nAttende: unicum telum tuum eicere prudens non est.", "advancements.adventure.throw_trident.title": "Iactus et ictus", "advancements.adventure.totem_of_undying.description": "Utere simulacro immortalitatis ad mortem fallendam", "advancements.adventure.totem_of_undying.title": "Vita post mortem", "advancements.adventure.trade.description": "Eme a vicano quid vel quid vicano vende", "advancements.adventure.trade.title": "Salve lucrum!", "advancements.adventure.trade_at_world_height.description": "In limite constructionis eme a vicano quid vel quid vicano vende", "advancements.adventure.trade_at_world_height.title": "Mercator stellarum", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Semel saltem adhibe has formas fabriles: cum turre, cum rostro, cum costis, vigiliarum, silentis, vexatoris, cum aestu, viatoris", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Supervacuus labor", "advancements.adventure.trim_with_any_armor_pattern.description": "Fabrica tegimen ornatum apud mensam fabricae", "advancements.adventure.trim_with_any_armor_pattern.title": "Faber est suae quisque formae", "advancements.adventure.two_birds_one_arrow.description": "Interfice duo phantasmata sagitta <PERSON>is", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON> aves, una sagitta", "advancements.adventure.under_lock_and_key.description": "Resera arcam clave periculorum", "advancements.adventure.under_lock_and_key.title": "<PERSON><PERSON><PERSON> conclavis", "advancements.adventure.use_lodestone.description": "Pyxis magnetica vi magnetica magnetitae imbue", "advancements.adventure.use_lodestone.title": "Vi per quam via videbis", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON>ina vicanum", "advancements.adventure.very_very_frightening.title": "Valde valde perterritum", "advancements.adventure.voluntary_exile.description": "Interfice capitaneum incursionis.\nForsitan mane ab vicis pro tempore...", "advancements.adventure.voluntary_exile.title": "Aqua et igni", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Super nivem pulveream ambula... et ne ea submersum eris", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON> sicut cuniculus", "advancements.adventure.who_needs_rockets.description": "Sali missile ventoso et ascende super VIII cubos", "advancements.adventure.who_needs_rockets.title": "Quis pyrotechnemam vult?", "advancements.adventure.whos_the_pillager_now.description": "Da praedatore gustatum medicinae suae", "advancements.adventure.whos_the_pillager_now.title": "Quis praedabit ipsos praedatores?", "advancements.empty": "Hic quicquam esse non videtur...", "advancements.end.dragon_breath.description": "Collige halitum draconis in utre vitreo", "advancements.end.dragon_breath.title": "Vae olfactui!", "advancements.end.dragon_egg.description": "Tene ovum draconis", "advancements.end.dragon_egg.title": "Ab ovo", "advancements.end.elytra.description": "<PERSON><PERSON> elytra", "advancements.end.elytra.title": "Cum pennis volare", "advancements.end.enter_end_gateway.description": "Fuge ab insula", "advancements.end.enter_end_gateway.title": "Tempus fugiendi", "advancements.end.find_end_city.description": "Ini, quidnam accidere possit?", "advancements.end.find_end_city.title": "Civitas caelestis", "advancements.end.kill_dragon.description": "Feliciter", "advancements.end.kill_dragon.title": "Libera End", "advancements.end.levitate.description": "Levitationem age L cubos super shulker oppugnationes", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON> summus", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON> iterum anima", "advancements.end.respawn_dragon.title": "Finis... Iterum...", "advancements.end.root.description": "Aut principium?", "advancements.end.root.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fac ut relevator placentam super cubum musicum demittat", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON><PERSON> cantus", "advancements.husbandry.allay_deliver_item_to_player.description": "Fac ut relevator res ad te adferat", "advancements.husbandry.allay_deliver_item_to_player.title": "Do ut des", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON>a salamandram mexicanam prende", "advancements.husbandry.axolotl_in_a_bucket.title": "Lepidissimus e rapacibus", "advancements.husbandry.balanced_diet.description": "Ede omnia quae edi possunt, etiam ea quae non sunt tibi bona", "advancements.husbandry.balanced_diet.title": "Quicquid est, est", "advancements.husbandry.breed_all_animals.description": "Ale duo animalia omnius generis ut fetent!", "advancements.husbandry.breed_all_animals.title": "Duo et duo", "advancements.husbandry.breed_an_animal.description": "Ale duo animalia ut fetent", "advancements.husbandry.breed_an_animal.title": "Da mi basia mille", "advancements.husbandry.complete_catalogue.description": "Mansueface omnia genera felium!", "advancements.husbandry.complete_catalogue.title": "Catalogus perfelectus", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON> odorisequ<PERSON>", "advancements.husbandry.feed_snifflet.title": "Institutio odoratoria", "advancements.husbandry.fishy_business.description": "Prende piscem", "advancements.husbandry.fishy_business.title": "Piscis primum a capite foetet", "advancements.husbandry.froglights.description": "Omnes ranaelanternas in inventario tuo habe", "advancements.husbandry.froglights.title": "Lanternae sunt omnes divisae in partes tres", "advancements.husbandry.kill_axolotl_target.description": "Cum salamandra mexicana coniunctus vince pugnam", "advancements.husbandry.kill_axolotl_target.title": "Amicitia semper prodest!", "advancements.husbandry.leash_all_frog_variants.description": "Omnia genera ranae copula vinci", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON> enim sunt tres congregatae", "advancements.husbandry.make_a_sign_glow.description": "Fac scripturam tituli lucidam", "advancements.husbandry.make_a_sign_glow.title": "Fiat lucis", "advancements.husbandry.netherite_hoe.description": "Sarculum latere Netheritae meliora, tum vitae arbitria retracta", "advancements.husbandry.netherite_hoe.title": "Dedicatio vera", "advancements.husbandry.obtain_sniffer_egg.description": "Adipiscere ovum odorisequi", "advancements.husbandry.obtain_sniffer_egg.title": "Odor te tenetne?", "advancements.husbandry.place_dried_ghast_in_water.description": "Pone cubum Ghast siccati in aquam", "advancements.husbandry.place_dried_ghast_in_water.title": "Carpe aquam!", "advancements.husbandry.plant_any_sniffer_seed.description": "Insere quodvis semen ab odorisequo inventum", "advancements.husbandry.plant_any_sniffer_seed.title": "Stirps antiqua", "advancements.husbandry.plant_seed.description": "Insere semen et specta crescens", "advancements.husbandry.plant_seed.title": "Deos qui novit agrestis", "advancements.husbandry.remove_wolf_armor.description": "Eripe forfice tegmen lupinum e lupo", "advancements.husbandry.remove_wolf_armor.title": "Num novit nudum?", "advancements.husbandry.repair_wolf_armor.description": "Refice squamis dasypodis tegmen lupinum fractum", "advancements.husbandry.repair_wolf_armor.title": "Bonum ut novum", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Rati cum capra naviga", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON> carpentarius", "advancements.husbandry.root.description": "Mundus est plenus amicorum et cibi", "advancements.husbandry.root.title": "Agricultura", "advancements.husbandry.safely_harvest_honey.description": "Foco usus apibus nullis irritatis mel ex alveario collige", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON> laborantes, mel apportantes", "advancements.husbandry.silk_touch_nest.description": "Nidum apium, in quo sint 3 apibus, contacto serici move", "advancements.husbandry.silk_touch_nest.title": "Repozzzitio totalis", "advancements.husbandry.tactical_fishing.description": "Prende piscem... sine harundine!", "advancements.husbandry.tactical_fishing.title": "<PERSON>sca<PERSON><PERSON> callida", "advancements.husbandry.tadpole_in_a_bucket.description": "Hama gyrinum prende", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON> amat", "advancements.husbandry.tame_an_animal.description": "Mansueface animal", "advancements.husbandry.tame_an_animal.title": "Amicissimi", "advancements.husbandry.wax_off.description": "<PERSON>abe ceram e cubo cupreo!", "advancements.husbandry.wax_off.title": "Cera scabitur", "advancements.husbandry.wax_on.description": "Illine favum cubo cupreo!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON> illinitur", "advancements.husbandry.whole_pack.description": "Mansueface omnia genera luporum", "advancements.husbandry.whole_pack.title": "Grex lupinus", "advancements.nether.all_effects.description": "Obtine effectus omnes eodem tempore", "advancements.nether.all_effects.title": "Nunc pede libero pulsanda tellus", "advancements.nether.all_potions.description": "Obtine effectus omnium potionum eodem tempore", "advancements.nether.all_potions.title": "Nunc est bibendum", "advancements.nether.brew_potion.description": "Misce potionem", "advancements.nether.brew_potion.title": "Vinum venenum", "advancements.nether.charge_respawn_anchor.description": "Ancoram renascendi maxime firma", "advancements.nether.charge_respawn_anchor.title": "Etiam in lava sunt ancorae", "advancements.nether.create_beacon.description": "Construe et pone radiatorem", "advancements.nether.create_beacon.title": "Radiator radicat", "advancements.nether.create_full_beacon.description": "Da vim plenam radiatori", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON> maximus", "advancements.nether.distract_piglin.description": "Da alicui <PERSON> aurum", "advancements.nether.distract_piglin.title": "Splenditia pura", "advancements.nether.explore_nether.description": "Explora omnia biomata Nether", "advancements.nether.explore_nether.title": "Per locos calidos", "advancements.nether.fast_travel.description": "Utere Nether ut iter facias per VII milia metrorum Terrae", "advancements.nether.fast_travel.title": "<PERSON><PERSON> subspatialis", "advancements.nether.find_bastion.description": "Intra ad reilquias aggeris", "advancements.nether.find_bastion.title": "<PERSON><PERSON> antiquus", "advancements.nether.find_fortress.description": "Irrumpe in arcem Nether", "advancements.nether.find_fortress.title": "Arx atra", "advancements.nether.get_wither_skull.description": "Obtine calvam sceleti wither", "advancements.nether.get_wither_skull.title": "<PERSON><PERSON><PERSON><PERSON> sceletus", "advancements.nether.loot_bastion.description": "Furare a cista reliquiarum aggeris", "advancements.nether.loot_bastion.title": "In Verrem", "advancements.nether.netherite_armor.description": "Obtine cunctum tegimen Netheritae", "advancements.nether.netherite_armor.title": "Sedimina maxime", "advancements.nether.obtain_ancient_debris.description": "Obtine antiqua sedimina", "advancements.nether.obtain_ancient_debris.title": "In imo terrae", "advancements.nether.obtain_blaze_rod.description": "Virga flammiferi cape", "advancements.nether.obtain_blaze_rod.title": "In ignem", "advancements.nether.obtain_crying_obsidian.description": "Lapidem obsidianum flentem obtine", "advancements.nether.obtain_crying_obsidian.title": "Cae<PERSON> quis secat?", "advancements.nether.return_to_sender.description": "Ghast globo flammeo interfice", "advancements.nether.return_to_sender.title": "Reddite quae sunt ghast ghast", "advancements.nether.ride_strider.description": "Ambulatore vectore ambula cum fungo distorto in virga", "advancements.nether.ride_strider.title": "Ratis cum cruribus", "advancements.nether.ride_strider_in_overworld_lava.description": "Ambulatore perlongissimum iter curre per lacum lavae Terrenum", "advancements.nether.ride_strider_in_overworld_lava.title": "Infernae speluncae", "advancements.nether.root.description": "Relinque domi togam", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON>er <PERSON>e", "advancements.nether.summon_wither.title": "Per aspera ad astra", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON> ghast ex Nether, ferque eum tute domum Terrenum... tum eum occide", "advancements.nether.uneasy_alliance.title": "Infidum foedus", "advancements.nether.use_lodestone.description": "Pyxis magnetica vi magnetica magnetitae imbue", "advancements.nether.use_lodestone.title": "Vi per quam via videbis", "advancements.progress": "%s ex %s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Vicanum resurrectum infirma atque cura", "advancements.story.cure_zombie_villager.title": "Medicus resurrectus", "advancements.story.deflect_arrow.description": "Deflecte iaculum scuto", "advancements.story.deflect_arrow.title": "Non hodie, gratias do", "advancements.story.enchant_item.description": "Incanta entitatem in mensa incantandi", "advancements.story.enchant_item.title": "Incantator", "advancements.story.enter_the_end.description": "Intra portam End", "advancements.story.enter_the_end.title": "E<PERSON>ne finis?", "advancements.story.enter_the_nether.description": "Aedifica, igni et intra portam Nether", "advancements.story.enter_the_nether.title": "Pandere res alta terra et caligine mersas", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> oculum", "advancements.story.follow_ender_eye.title": "O ocelle!", "advancements.story.form_obsidian.description": "Lapidem obsidianum obtine", "advancements.story.form_obsidian.title": "Inventio Obsidi", "advancements.story.iron_tools.description": "Dolabram tuam meliorem fac", "advancements.story.iron_tools.title": "Ad metalla", "advancements.story.lava_bucket.description": "Comple hamam lava", "advancements.story.lava_bucket.title": "Caldarium", "advancements.story.mine_diamond.description": "Obtine adamantes", "advancements.story.mine_diamond.title": "Adamantes!", "advancements.story.mine_stone.description": "Effode saxum dolabra nova tua", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON> lapidea", "advancements.story.obtain_armor.description": "Te tege tegmine ferreo", "advancements.story.obtain_armor.title": "Indue", "advancements.story.root.description": "Cor et fabula ludi", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Tegimen adamantinum vitas servavit", "advancements.story.shiny_gear.title": "Adamantus maxime", "advancements.story.smelt_iron.description": "Funde laterem ferreum", "advancements.story.smelt_iron.title": "Ferrum ferre", "advancements.story.upgrade_tools.description": "Meliorem dolabram fabrica", "advancements.story.upgrade_tools.title": "Ad meliora", "advancements.toast.challenge": "Arduum peractum!", "advancements.toast.goal": "Finis consecutus!", "advancements.toast.task": "Res gesta!", "argument.anchor.invalid": "Locus signi %s irritus est", "argument.angle.incomplete": "<PERSON><PERSON><PERSON><PERSON> abest (unus numerus exspectatus erat)", "argument.angle.invalid": "Rotatio irrita", "argument.block.id.invalid": "Genus cubi ignotum '%s'", "argument.block.property.duplicate": "Proprietas '%s' semel regulari modo potest pro cube %s", "argument.block.property.invalid": "Cubus %s '%s' non accipit pro proprietate %s", "argument.block.property.novalue": "Exspectatus valor proprietati '%s' cubi %s", "argument.block.property.unclosed": "Scribe ] ad finem statuus cubi signandam", "argument.block.property.unknown": "Cubus %s proprietatem '%s' non habet", "argument.block.tag.disallowed": "Non notae, sed tantum veri cubi hic permissae sunt", "argument.color.invalid": "Color '%s' ignotus est", "argument.component.invalid": "Pars nuntii irrita es: %s", "argument.criteria.invalid": "Criterion ignotum '%s'", "argument.dimension.invalid": "Dimensio '%s' ignota est", "argument.double.big": "Numerus decimarius altero accuratior maior %s esse non potes, sed %s invenitur", "argument.double.low": "Numerus decimarius altero accuratior minor %s esse non potes, sed %s invenitur", "argument.entity.invalid": "Nomen aut UUA invalidum est", "argument.entity.notfound.entity": "Nulla entitas inventa est", "argument.entity.notfound.player": "<PERSON><PERSON><PERSON> lusor inventus est", "argument.entity.options.advancements.description": "Lusores cum incrementis", "argument.entity.options.distance.description": "Intervallum ab entitate", "argument.entity.options.distance.negative": "Intervallum negativum esse non potes", "argument.entity.options.dx.description": "Entitates inter x et x + dx", "argument.entity.options.dy.description": "Entitates inter y et y + dy", "argument.entity.options.dz.description": "Entitates inter z et z + dz", "argument.entity.options.gamemode.description": "Lusores in modo ludi quodam", "argument.entity.options.inapplicable": "Optio '%s' hic est non apta", "argument.entity.options.level.description": "<PERSON><PERSON><PERSON> experientiae", "argument.entity.options.level.negative": "Gradus negativi esse non possunt", "argument.entity.options.limit.description": "Finis numeri entitatum reddendarum", "argument.entity.options.limit.toosmall": "Finis minor uno esse non potest", "argument.entity.options.mode.invalid": "Modus ludi irritus vel ignotus: '%s'", "argument.entity.options.name.description": "Nomen entitatis", "argument.entity.options.nbt.description": "Entitates cum NBT", "argument.entity.options.predicate.description": "Praedicatum consuetudinis", "argument.entity.options.scores.description": "Entitates cum victoriis", "argument.entity.options.sort.description": "Ordinare entitates", "argument.entity.options.sort.irreversible": "Irritus aut ignotus species descriptus '%s'", "argument.entity.options.tag.description": "Entitates cum nota", "argument.entity.options.team.description": "Entitates in factione", "argument.entity.options.type.description": "Entitates generis", "argument.entity.options.type.invalid": "Genus entitatis non est validus vel scitus: '%s'", "argument.entity.options.unknown": "Optio ignota: %s", "argument.entity.options.unterminated": "Expectata finem optionum", "argument.entity.options.valueless": "Expectatus valor optionis '%s'", "argument.entity.options.x.description": "x positio", "argument.entity.options.x_rotation.description": "Rotatio entitatis in axe X", "argument.entity.options.y.description": "y positio", "argument.entity.options.y_rotation.description": "Rotatio entitatis in axe Y", "argument.entity.options.z.description": "z positio", "argument.entity.selector.allEntities": "Omnes entitates", "argument.entity.selector.allPlayers": "Omnes lusores", "argument.entity.selector.missing": "Selector abest", "argument.entity.selector.nearestEntity": "Entitas proxima", "argument.entity.selector.nearestPlayer": "Lusor proximus", "argument.entity.selector.not_allowed": "Hic selector permissus non est", "argument.entity.selector.randomPlayer": "Lusor fortuitus", "argument.entity.selector.self": "Haec entitas", "argument.entity.selector.unknown": "Selector '%s' ignotus est", "argument.entity.toomany": "Una tantum entitas nec multae (ut selector indicat) indicari potes selectore", "argument.enum.invalid": "Hic valor irritus est: \"%s\"", "argument.float.big": "Numerus decimarius maior %s esse non potest, sed %s invenitur", "argument.float.low": "Numerus decimarius minor %s esse non potest, sed %s invenitur", "argument.gamemode.invalid": "Modus ludi ignoratus: %s", "argument.hexcolor.invalid": "Codex coloris sedecimalis '%s' irritus est", "argument.id.invalid": "ID irrita", "argument.id.unknown": "Ignota ID: %s", "argument.integer.big": "Numerus plenus maior %s esse non potest, sed %s invenitur", "argument.integer.low": "Numerus plenus minor %s esse non potest, sed %s invenitur", "argument.item.id.invalid": "Res ignota: %s", "argument.item.tag.disallowed": "Non notae, sed tantum verae res hic permissae sunt", "argument.literal.incorrect": "Expectatus scriptus %s", "argument.long.big": "Longus plus quam %s non esse debet, %s invenit", "argument.long.low": "Longus minus quam %s non esse debet, %s invenit", "argument.message.too_long": "Nuntius ex %s figuris constat, sed maxime mitti possunt figurae %s", "argument.nbt.array.invalid": "Genus matricis irritum: '%s'", "argument.nbt.array.mixed": "%s in %s inserere non potest", "argument.nbt.expected.compound": "Nota composita exspectabatur", "argument.nbt.expected.key": "Expectatum NBT castrum", "argument.nbt.expected.value": "Expectatus valor", "argument.nbt.list.mixed": "%s in indicem %s inserere non potest", "argument.nbt.trailing": "Data finalia expectata non erant", "argument.player.entities": "Lusores tantum hoc iusso affici possunt, sed selector inscriptus entitates connumerat", "argument.player.toomany": "Unus tantum lusor nec multi (ut selector indicat) indicari potes selectore", "argument.player.unknown": "<PERSON>le lusor non exsistit", "argument.pos.missing.double": "Exspectatae latitudo et longitudo", "argument.pos.missing.int": "Exspectatus locus cubi", "argument.pos.mixed": "Uti et locales et globales latitudo et longitudo non potes (semper ^ utere aut noli uti)", "argument.pos.outofbounds": "Positio illa excedit limites permissi.", "argument.pos.outofworld": "Ille locus in mundo non adest!", "argument.pos.unloaded": "Ille locus non est impositus", "argument.pos2d.incomplete": "Imperfectus (II coordinati exspectabantur)", "argument.pos3d.incomplete": "Latitudo et longitudo absunt (tres numeri exspectati erant)", "argument.range.empty": "Expectatus numerus aut intervallum numerorum", "argument.range.ints": "Hic solum pleni numeri nec decimarii esse possunt", "argument.range.swapped": "Minimus maior quam maximus non potest", "argument.resource.invalid_type": "Elementum '%s' habet iniuriam generis '%s' ('%s' exspectabatur)", "argument.resource.not_found": "Elementum '%s' generis '%s' invenire non potest", "argument.resource_or_id.failed_to_parse": "Aedificium interpretari non potuit: %s", "argument.resource_or_id.invalid": "Nota vel identificatio irrita", "argument.resource_or_id.no_such_element": "Elementum '%s' in monumento '%s' invenire non potest", "argument.resource_selector.not_found": "Selectorem '%s' generis '%s' non invenit", "argument.resource_tag.invalid_type": "Nota '%s' genus iniuriae '%s' habet ('%s' exspectabatur)", "argument.resource_tag.not_found": "Notam '%s' generis '%s' invenire non potest", "argument.rotation.incomplete": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> (duo numeri exspectati erant)", "argument.scoreHolder.empty": "<PERSON>ui esset ulla victoria inveniri non potuit", "argument.scoreboardDisplaySlot.invalid": "Locus %s ignotus est", "argument.style.invalid": "Oratio irrita: %s", "argument.time.invalid_tick_count": "Numerus momentorum negativus esse non potest", "argument.time.invalid_unit": "Unitas irrita", "argument.time.tick_count_too_low": "Numerus momentorum non minus quam %s, inventa %s", "argument.uuid.invalid": "UUA irritus est", "argument.waypoint.invalid": "Entitas selecta non est locus itineris", "arguments.block.tag.unknown": "Nota cubi ignota '%s'", "arguments.function.tag.unknown": "Nota functionis ignota '%s'", "arguments.function.unknown": "Functio ignota: %s", "arguments.item.component.expected": "Pars rei exspectabatur", "arguments.item.component.malformed": "Pars deformis '%s': '%s'", "arguments.item.component.repeated": "Pars rei '%s' iteratur, sed unus valor solus definiri potest", "arguments.item.component.unknown": "Pars rei ignota: '%s'", "arguments.item.malformed": "Res deformis: '%s'", "arguments.item.overstacked": "%s acervo inesse non potest qui ex pluribus quam %s rebus constet", "arguments.item.predicate.malformed": "Praedicatum '%s' deforme: '%s'", "arguments.item.predicate.unknown": "Praedicatum rei ignotum: '%s'", "arguments.item.tag.unknown": "Nota rei ignota '%s'", "arguments.nbtpath.node.invalid": "NBT res itineris irrita est", "arguments.nbtpath.nothing_found": "Nihil inventum est, quid convenit: %s", "arguments.nbtpath.too_deep": "NBT consequentis est nimis penitus insitum", "arguments.nbtpath.too_large": "NBT consequentis est nimis magnum", "arguments.objective.notFound": "Meta %s ignota est", "arguments.objective.readonly": "Victoriae meta %s mutari non possut", "arguments.operation.div0": "Dividere zero non potest", "arguments.operation.invalid": "Operatio irrita", "arguments.swizzle.invalid": "Axes irriti sunt: coniunctio axium 'x', 'y' vel 'z' exspectabatur", "attribute.modifier.equals.0": "%2$s %1$s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%2$s %1$s%%", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "%2$s +%1$s%%", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "%2$s -%1$s%%", "attribute.name.armor": "<PERSON><PERSON><PERSON> teg<PERSON>s", "attribute.name.armor_toughness": "Soliditas tegminis", "attribute.name.attack_damage": "Iniuria impetus", "attribute.name.attack_knockback": "Repugnantia impetus", "attribute.name.attack_speed": "Velocitas impetus", "attribute.name.block_break_speed": "Celeritas fodendi", "attribute.name.block_interaction_range": "Intervallum tangendi cubos", "attribute.name.burning_time": "Tempus ardendi", "attribute.name.camera_distance": "Intervallum camerae", "attribute.name.entity_interaction_range": "<PERSON><PERSON>ia tangendi entitates", "attribute.name.explosion_knockback_resistance": "Repug<PERSON>ia repellendi displosionibus", "attribute.name.fall_damage_multiplier": "Gradus vulnerum ob casum", "attribute.name.flying_speed": "Celeritas volatus", "attribute.name.follow_range": "Intervallum animadvertendi mobilis", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON> teg<PERSON>s", "attribute.name.generic.armor_toughness": "Soliditas tegminis", "attribute.name.generic.attack_damage": "Iniuria impetus", "attribute.name.generic.attack_knockback": "Repugnantia iniuriae", "attribute.name.generic.attack_speed": "Velocitas impetus", "attribute.name.generic.block_interaction_range": "Intervallum tangendi cubos", "attribute.name.generic.burning_time": "Tempus ardendi", "attribute.name.generic.entity_interaction_range": "<PERSON><PERSON>ia tangendi entitates", "attribute.name.generic.explosion_knockback_resistance": "Repug<PERSON>ia repellendi displosionibus", "attribute.name.generic.fall_damage_multiplier": "Gradus vulnerum ob casum", "attribute.name.generic.flying_speed": "Celeritas volatus", "attribute.name.generic.follow_range": "Intervallum animadvertendi mobilis", "attribute.name.generic.gravity": "Vis gravitatis", "attribute.name.generic.jump_strength": "Vis saltus", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "Fortuna", "attribute.name.generic.max_absorption": "Absorptio maxima", "attribute.name.generic.max_health": "Salus maxima", "attribute.name.generic.movement_efficiency": "Efficacia movendi", "attribute.name.generic.movement_speed": "Celeritas", "attribute.name.generic.oxygen_bonus": "Praemium oxygenii", "attribute.name.generic.safe_fall_distance": "Altitudo tuta ad cadendum", "attribute.name.generic.scale": "Magnitudo", "attribute.name.generic.step_height": "Altitudo graduum", "attribute.name.generic.water_movement_efficiency": "Efficacia movendi in aqua", "attribute.name.gravity": "Vis gravitatis", "attribute.name.horse.jump_strength": "Vigor saltus equi", "attribute.name.jump_strength": "Vis saltus", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON> repellendi", "attribute.name.luck": "Fortuna", "attribute.name.max_absorption": "Absorptio maxima", "attribute.name.max_health": "Salus maxima", "attribute.name.mining_efficiency": "Efficacia effodendi", "attribute.name.movement_efficiency": "Efficacia movendi", "attribute.name.movement_speed": "Celeritas", "attribute.name.oxygen_bonus": "Praemium oxygenii", "attribute.name.player.block_break_speed": "Celeritas fodendi", "attribute.name.player.block_interaction_range": "<PERSON><PERSON><PERSON> tangendi cubos", "attribute.name.player.entity_interaction_range": "<PERSON><PERSON>ia tangendi entitates", "attribute.name.player.mining_efficiency": "Efficacia effodendi", "attribute.name.player.sneaking_speed": "Celeritas furtim ambulandi", "attribute.name.player.submerged_mining_speed": "Efficacia effodendi in aqua", "attribute.name.player.sweeping_damage_ratio": "Ratio vulneris de<PERSON>di", "attribute.name.safe_fall_distance": "Altitudo tuta ad cadendum", "attribute.name.scale": "Magnitudo", "attribute.name.sneaking_speed": "Velocitas furtim ambulandi", "attribute.name.spawn_reinforcements": "Resurrecti auxiliares", "attribute.name.step_height": "Altitudo graduum", "attribute.name.submerged_mining_speed": "Efficacia effodendi in aqua", "attribute.name.sweeping_damage_ratio": "Ratio vulneris de<PERSON>di", "attribute.name.tempt_range": "Intervallum alliciendi", "attribute.name.water_movement_efficiency": "Efficacia movendi in aqua", "attribute.name.waypoint_receive_range": "Intervallum accipientis locum itineris", "attribute.name.waypoint_transmit_range": "Intervallum transmittentis locum itineris", "attribute.name.zombie.spawn_reinforcements": "Resurrecti auxiliares", "biome.minecraft.badlands": "Malae terrae", "biome.minecraft.bamboo_jungle": "<PERSON>ungala harundinum Indicarum", "biome.minecraft.basalt_deltas": "Paludes basaltae", "biome.minecraft.beach": "<PERSON><PERSON>", "biome.minecraft.birch_forest": "<PERSON>", "biome.minecraft.cherry_grove": "<PERSON><PERSON> cerasorum", "biome.minecraft.cold_ocean": "Mare frigidum", "biome.minecraft.crimson_forest": "<PERSON>", "biome.minecraft.dark_forest": "<PERSON>", "biome.minecraft.deep_cold_ocean": "Mare frigidum profundum", "biome.minecraft.deep_dark": "Obscuritas profunda", "biome.minecraft.deep_frozen_ocean": "Mare gelatum profundum", "biome.minecraft.deep_lukewarm_ocean": "Mare tepidum profundum", "biome.minecraft.deep_ocean": "Mare profundum", "biome.minecraft.desert": "Deserta", "biome.minecraft.dripstone_caves": "Speluncae saxorum stillantium", "biome.minecraft.end_barrens": "Vastitas End", "biome.minecraft.end_highlands": "Montana End", "biome.minecraft.end_midlands": "Mediterranea End", "biome.minecraft.eroded_badlands": "Malae terrae erosae", "biome.minecraft.flower_forest": "Silva florida", "biome.minecraft.forest": "<PERSON>", "biome.minecraft.frozen_ocean": "Mare gelatum", "biome.minecraft.frozen_peaks": "Cacumina glacialia", "biome.minecraft.frozen_river": "Flumen gelatum", "biome.minecraft.grove": "<PERSON><PERSON>", "biome.minecraft.ice_spikes": "Columnae glaciales", "biome.minecraft.jagged_peaks": "Cacumina iniqua", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Mare tepidum", "biome.minecraft.lush_caves": "Speluncae virentes", "biome.minecraft.mangrove_swamp": "Palus manglium", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Agri fungini", "biome.minecraft.nether_wastes": "<PERSON><PERSON>", "biome.minecraft.ocean": "Mare", "biome.minecraft.old_growth_birch_forest": "Silva veterum betullarum", "biome.minecraft.old_growth_pine_taiga": "Silva borealis veterum pinuum", "biome.minecraft.old_growth_spruce_taiga": "Silva borealis veterum picearum", "biome.minecraft.pale_garden": "Hortus pallidus", "biome.minecraft.plains": "Planities", "biome.minecraft.river": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Planum altum savanae", "biome.minecraft.small_end_islands": "Parvae insulae End", "biome.minecraft.snowy_beach": "Litus niveum", "biome.minecraft.snowy_plains": "Planities nivea", "biome.minecraft.snowy_slopes": "<PERSON><PERSON>vi nivei", "biome.minecraft.snowy_taiga": "Silva borealis nivea", "biome.minecraft.soul_sand_valley": "Vallis harenae animarum", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON> rara", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON><PERSON> lapido<PERSON>", "biome.minecraft.stony_shore": "<PERSON><PERSON> lapidosum", "biome.minecraft.sunflower_plains": "Planities helianthorum", "biome.minecraft.swamp": "Palus", "biome.minecraft.taiga": "<PERSON> borealis", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "<PERSON><PERSON>", "biome.minecraft.warm_ocean": "Mare calidum", "biome.minecraft.warped_forest": "<PERSON> distorta", "biome.minecraft.windswept_forest": "<PERSON>", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON> ventosi", "biome.minecraft.windswept_hills": "<PERSON><PERSON>i", "biome.minecraft.windswept_savanna": "<PERSON><PERSON> ventosa", "biome.minecraft.wooded_badlands": "Malae terrae silvosae", "block.minecraft.acacia_button": "Premendus acaciae", "block.minecraft.acacia_door": "Ostium acaciae", "block.minecraft.acacia_fence": "Saepes acaciae", "block.minecraft.acacia_fence_gate": "Foris acaciae", "block.minecraft.acacia_hanging_sign": "Titulus pendens acaciae", "block.minecraft.acacia_leaves": "Foliae acaciae", "block.minecraft.acacia_log": "Truncus acaciae", "block.minecraft.acacia_planks": "Tabulae acaciae", "block.minecraft.acacia_pressure_plate": "Tabula premenda acaciae", "block.minecraft.acacia_sapling": "A<PERSON>us<PERSON> acaciae", "block.minecraft.acacia_sign": "Titulus acaciae", "block.minecraft.acacia_slab": "Gradus acaciae", "block.minecraft.acacia_stairs": "Scalae acaciae", "block.minecraft.acacia_trapdoor": "Ostium horizontale acaciae", "block.minecraft.acacia_wall_hanging_sign": "Titulus pendens muralis acaciae", "block.minecraft.acacia_wall_sign": "Titulus muralis acaciae", "block.minecraft.acacia_wood": "Lignum acaciae", "block.minecraft.activator_rail": "Ferrivia incitans", "block.minecraft.air": "<PERSON><PERSON>", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "<PERSON><PERSON><PERSON> amethystinus", "block.minecraft.amethyst_cluster": "Coacervatio amethystorum", "block.minecraft.ancient_debris": "Antiqua sedimina", "block.minecraft.andesite": "Andesites", "block.minecraft.andesite_slab": "<PERSON>radus and<PERSON>e", "block.minecraft.andesite_stairs": "Scalae andesitae", "block.minecraft.andesite_wall": "Murus andesitae", "block.minecraft.anvil": "Incus", "block.minecraft.attached_melon_stem": "<PERSON><PERSON><PERSON> peponis adiunctus", "block.minecraft.attached_pumpkin_stem": "Caulis cucurbitae adiunctus", "block.minecraft.azalea": "Rhododendron", "block.minecraft.azalea_leaves": "Foliae rhododendri", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_block": "<PERSON><PERSON><PERSON> harundinum Indicarum", "block.minecraft.bamboo_button": "Premendum harundinum Indicarum", "block.minecraft.bamboo_door": "Ostium harundinum Indicarum", "block.minecraft.bamboo_fence": "<PERSON><PERSON><PERSON> harundinum Indicarum", "block.minecraft.bamboo_fence_gate": "Foris harundinum Indicarum", "block.minecraft.bamboo_hanging_sign": "Titulus pendens harundinum Indicarum", "block.minecraft.bamboo_mosaic": "Pavimentum harundinum Indicarum", "block.minecraft.bamboo_mosaic_slab": "<PERSON><PERSON><PERSON> pavimenti harundinum Indicarum", "block.minecraft.bamboo_mosaic_stairs": "Scalae pavimenti harundinum Indicarum", "block.minecraft.bamboo_planks": "Tabulae harundinum Indicarum", "block.minecraft.bamboo_pressure_plate": "Tabula premenda harundinum Indicarum", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON> harundinis <PERSON>ae", "block.minecraft.bamboo_sign": "Titulus harundinum Indicarum", "block.minecraft.bamboo_slab": "<PERSON><PERSON><PERSON> harundinum Indicarum", "block.minecraft.bamboo_stairs": "Scalae harundinum Indicarum", "block.minecraft.bamboo_trapdoor": "Ostium horizontale harundinum Indicarum", "block.minecraft.bamboo_wall_hanging_sign": "Titulus pendens muralis harundinum Indicarum", "block.minecraft.bamboo_wall_sign": "Titulus muralis harundinum Indicarum", "block.minecraft.banner.base.black": "Campus totus niger", "block.minecraft.banner.base.blue": "Campus totus caeruleus", "block.minecraft.banner.base.brown": "Campus totus brunneus", "block.minecraft.banner.base.cyan": "Campus totus cyaneus", "block.minecraft.banner.base.gray": "Campus totus cinereus", "block.minecraft.banner.base.green": "Campus totus viridis", "block.minecraft.banner.base.light_blue": "Campus totus aerius", "block.minecraft.banner.base.light_gray": "Campus totus canus", "block.minecraft.banner.base.lime": "Campus totus prasinus", "block.minecraft.banner.base.magenta": "Campus totus rubropurpureus", "block.minecraft.banner.base.orange": "Campus totus aurantius", "block.minecraft.banner.base.pink": "Campus totus roseus", "block.minecraft.banner.base.purple": "Campus totus purpureus", "block.minecraft.banner.base.red": "Campus totus ruber", "block.minecraft.banner.base.white": "Campus totus albus", "block.minecraft.banner.base.yellow": "Campus totus flavus", "block.minecraft.banner.border.black": "Ora nigra", "block.minecraft.banner.border.blue": "<PERSON>a caerulea", "block.minecraft.banner.border.brown": "<PERSON>a brunnea", "block.minecraft.banner.border.cyan": "Ora cyanea", "block.minecraft.banner.border.gray": "Ora cinerea", "block.minecraft.banner.border.green": "<PERSON>a viridis", "block.minecraft.banner.border.light_blue": "Ora aeria", "block.minecraft.banner.border.light_gray": "<PERSON>a cana", "block.minecraft.banner.border.lime": "Ora prasina", "block.minecraft.banner.border.magenta": "Ora rubropurpurea", "block.minecraft.banner.border.orange": "Ora aurantia", "block.minecraft.banner.border.pink": "Ora rosea", "block.minecraft.banner.border.purple": "Ora purpurea", "block.minecraft.banner.border.red": "Ora rubra", "block.minecraft.banner.border.white": "Ora alba", "block.minecraft.banner.border.yellow": "Ora flava", "block.minecraft.banner.bricks.black": "Ager testaceus nigrus", "block.minecraft.banner.bricks.blue": "Ager testaceus caeruleus", "block.minecraft.banner.bricks.brown": "Ager testaceus brunneus", "block.minecraft.banner.bricks.cyan": "Ager testaceus cyaneus", "block.minecraft.banner.bricks.gray": "Ager testaceus cinereus", "block.minecraft.banner.bricks.green": "Ager testaceus viridis", "block.minecraft.banner.bricks.light_blue": "Ager testaceus aerius", "block.minecraft.banner.bricks.light_gray": "Ager testaceus canus", "block.minecraft.banner.bricks.lime": "Ager testaceus prasinus", "block.minecraft.banner.bricks.magenta": "Ager testaceus rubropurpureus", "block.minecraft.banner.bricks.orange": "Ager testaceus aurantius", "block.minecraft.banner.bricks.pink": "Ager testaceus roseus", "block.minecraft.banner.bricks.purple": "Ager testaceus purpureus", "block.minecraft.banner.bricks.red": "Ager testaceus ruber", "block.minecraft.banner.bricks.white": "Ager testaceus albus", "block.minecraft.banner.bricks.yellow": "Ager testaceus flavus", "block.minecraft.banner.circle.black": "Circlus niger", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON><PERSON> caeruleus", "block.minecraft.banner.circle.brown": "Cir<PERSON><PERSON> brunneus", "block.minecraft.banner.circle.cyan": "Circlus cyaneus", "block.minecraft.banner.circle.gray": "Cir<PERSON><PERSON> cinereus", "block.minecraft.banner.circle.green": "C<PERSON><PERSON><PERSON> viridis", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON><PERSON> aerius", "block.minecraft.banner.circle.light_gray": "<PERSON>ir<PERSON><PERSON> canus", "block.minecraft.banner.circle.lime": "C<PERSON><PERSON><PERSON> prasinus", "block.minecraft.banner.circle.magenta": "Circ<PERSON> rubropurpureus", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON><PERSON> aurantius", "block.minecraft.banner.circle.pink": "Circlus roseus", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON><PERSON> purpureus", "block.minecraft.banner.circle.red": "Circlus ruber", "block.minecraft.banner.circle.white": "Circlus albus", "block.minecraft.banner.circle.yellow": "Circlus flavus", "block.minecraft.banner.creeper.black": "Imago creeper nigra", "block.minecraft.banner.creeper.blue": "Imago creeper caerulea", "block.minecraft.banner.creeper.brown": "Imago creeper brunnea", "block.minecraft.banner.creeper.cyan": "Imago creeper cyanea", "block.minecraft.banner.creeper.gray": "Imago creeper cinerea", "block.minecraft.banner.creeper.green": "Imago creeper viridis", "block.minecraft.banner.creeper.light_blue": "Imago creeper aeria", "block.minecraft.banner.creeper.light_gray": "Imago creeper cana", "block.minecraft.banner.creeper.lime": "Imago creeper prasina", "block.minecraft.banner.creeper.magenta": "Imago creeper rubropurpurea", "block.minecraft.banner.creeper.orange": "Imago creeper aurantia", "block.minecraft.banner.creeper.pink": "Imago creeper rosea", "block.minecraft.banner.creeper.purple": "Imago creeper purpurea", "block.minecraft.banner.creeper.red": "Imago creeper rubra", "block.minecraft.banner.creeper.white": "Imago creeper alba", "block.minecraft.banner.creeper.yellow": "Imago creeper flava", "block.minecraft.banner.cross.black": "Facies decem nigra", "block.minecraft.banner.cross.blue": "Facies decem caerulea", "block.minecraft.banner.cross.brown": "Facies decem brunnea", "block.minecraft.banner.cross.cyan": "Facies decem cyanea", "block.minecraft.banner.cross.gray": "Facies decem cinerea", "block.minecraft.banner.cross.green": "Facies decem viridis", "block.minecraft.banner.cross.light_blue": "Facies decem aeria", "block.minecraft.banner.cross.light_gray": "Facies decem cana", "block.minecraft.banner.cross.lime": "Facies decem prasina", "block.minecraft.banner.cross.magenta": "Facies decem rubropurpurea", "block.minecraft.banner.cross.orange": "Facies decem aurantia", "block.minecraft.banner.cross.pink": "Facies decem rosea", "block.minecraft.banner.cross.purple": "Facies decem purpurea", "block.minecraft.banner.cross.red": "Facies decem rubra", "block.minecraft.banner.cross.white": "Facies decem alba", "block.minecraft.banner.cross.yellow": "Facies decem flava", "block.minecraft.banner.curly_border.black": "Finitio curvum nigrum", "block.minecraft.banner.curly_border.blue": "Finitio curvum caeruleum", "block.minecraft.banner.curly_border.brown": "Finitio curvum brunneum", "block.minecraft.banner.curly_border.cyan": "Finitio curvum cyaneum", "block.minecraft.banner.curly_border.gray": "Finitio curvum cinereum", "block.minecraft.banner.curly_border.green": "Finitio curvum viride", "block.minecraft.banner.curly_border.light_blue": "Finitio curvum aerium", "block.minecraft.banner.curly_border.light_gray": "Finitio curvum canum", "block.minecraft.banner.curly_border.lime": "Finitio curvum prasinum", "block.minecraft.banner.curly_border.magenta": "Finitio curvum rubropurpureum", "block.minecraft.banner.curly_border.orange": "Finitio curvum aurantium", "block.minecraft.banner.curly_border.pink": "Finitio curvum roseum", "block.minecraft.banner.curly_border.purple": "Finitio curvum purpureum", "block.minecraft.banner.curly_border.red": "Finitio curvum rubrum", "block.minecraft.banner.curly_border.white": "Finitio curvum album", "block.minecraft.banner.curly_border.yellow": "Finitio curvum flavum", "block.minecraft.banner.diagonal_left.black": "Per anfractus sinistrae niger", "block.minecraft.banner.diagonal_left.blue": "Per anfractus sinistrae caeruleus", "block.minecraft.banner.diagonal_left.brown": "Per anfractus sinistrae brunneus", "block.minecraft.banner.diagonal_left.cyan": "Per anfractus sinistrae cyaneus", "block.minecraft.banner.diagonal_left.gray": "Per anfractus sinistrae cinereus", "block.minecraft.banner.diagonal_left.green": "Per anfractus sinistrae viridis", "block.minecraft.banner.diagonal_left.light_blue": "Per anfractus sinistrae aerius", "block.minecraft.banner.diagonal_left.light_gray": "Per anfractus sinistrae canus", "block.minecraft.banner.diagonal_left.lime": "Per anfractus sinistrae prasinus", "block.minecraft.banner.diagonal_left.magenta": "Per anfractum sinisterae rubropurpureum", "block.minecraft.banner.diagonal_left.orange": "Per anfractus sinistrae aurantius", "block.minecraft.banner.diagonal_left.pink": "Per anfractus sinistrae roseus", "block.minecraft.banner.diagonal_left.purple": "Per anfractus sinistrae purpureus", "block.minecraft.banner.diagonal_left.red": "Per anfractus sinistrae ruber", "block.minecraft.banner.diagonal_left.white": "Per anfractus sinistrae albus", "block.minecraft.banner.diagonal_left.yellow": "Per anfractus sinistrae flavus", "block.minecraft.banner.diagonal_right.black": "Per anfractum nigrum", "block.minecraft.banner.diagonal_right.blue": "Per anfractum caeruleum", "block.minecraft.banner.diagonal_right.brown": "Per anfractum brunneum", "block.minecraft.banner.diagonal_right.cyan": "Per anfractum cyaneum", "block.minecraft.banner.diagonal_right.gray": "Per anfractum cinereum", "block.minecraft.banner.diagonal_right.green": "Per anfractum viride", "block.minecraft.banner.diagonal_right.light_blue": "Per anfractum aerium", "block.minecraft.banner.diagonal_right.light_gray": "Per anfractum canum", "block.minecraft.banner.diagonal_right.lime": "Per anfractum prasinum", "block.minecraft.banner.diagonal_right.magenta": "Per anfractum sinisterae", "block.minecraft.banner.diagonal_right.orange": "Per anfractum aurantium", "block.minecraft.banner.diagonal_right.pink": "Per anfractum roseum", "block.minecraft.banner.diagonal_right.purple": "Per anfractum purpureum", "block.minecraft.banner.diagonal_right.red": "Per anfractum rubrum", "block.minecraft.banner.diagonal_right.white": "Per anfractum album", "block.minecraft.banner.diagonal_right.yellow": "Per anfractum flavum", "block.minecraft.banner.diagonal_up_left.black": "Per anfractum conversum nigrum", "block.minecraft.banner.diagonal_up_left.blue": "Per anfractum conversum caeruleum", "block.minecraft.banner.diagonal_up_left.brown": "Per anfractum conversum brunneum", "block.minecraft.banner.diagonal_up_left.cyan": "Per anfractum conversum cyaneum", "block.minecraft.banner.diagonal_up_left.gray": "Per anfractum conversum cinereum", "block.minecraft.banner.diagonal_up_left.green": "Per anfractum conversum viridem", "block.minecraft.banner.diagonal_up_left.light_blue": "Per anfractum conversum aerium", "block.minecraft.banner.diagonal_up_left.light_gray": "Per anfractum conversum canum", "block.minecraft.banner.diagonal_up_left.lime": "Per anfractum conversum prasinum", "block.minecraft.banner.diagonal_up_left.magenta": "Per anfractum conversum rubropurpureum", "block.minecraft.banner.diagonal_up_left.orange": "Per anfractum conversum aurantium", "block.minecraft.banner.diagonal_up_left.pink": "Per anfractum conversum roseum", "block.minecraft.banner.diagonal_up_left.purple": "Per anfractum conversum purpureum", "block.minecraft.banner.diagonal_up_left.red": "Per anfractum conversum rubrum", "block.minecraft.banner.diagonal_up_left.white": "Per anfractum conversum album", "block.minecraft.banner.diagonal_up_left.yellow": "Per anfractum conversum flavum", "block.minecraft.banner.diagonal_up_right.black": "Per anfractum sinistrae conversum nigrum", "block.minecraft.banner.diagonal_up_right.blue": "Per anfractum sinistrae conversum caeruleum", "block.minecraft.banner.diagonal_up_right.brown": "Per anfractum sinistrae conversum brunneum", "block.minecraft.banner.diagonal_up_right.cyan": "Per anfractum sinistrae conversum cyaneum", "block.minecraft.banner.diagonal_up_right.gray": "Per anfractum sinistrae conversum cinereum", "block.minecraft.banner.diagonal_up_right.green": "Per anfractum sinistrae conversum viridem", "block.minecraft.banner.diagonal_up_right.light_blue": "Per anfractum sinistrae conversum aerium", "block.minecraft.banner.diagonal_up_right.light_gray": "Per anfractum sinistrae conversum canum", "block.minecraft.banner.diagonal_up_right.lime": "Per anfractum sinistrae conversum prasinum", "block.minecraft.banner.diagonal_up_right.magenta": "Per anfractum sinisterae conversum rubropurpureum", "block.minecraft.banner.diagonal_up_right.orange": "Per anfractum sinistrae conversum aurantium", "block.minecraft.banner.diagonal_up_right.pink": "Per anfractum sinistrae conversum roseum", "block.minecraft.banner.diagonal_up_right.purple": "Per anfractum sinistrae conversum purpureum", "block.minecraft.banner.diagonal_up_right.red": "Per anfractum sinistrae conversum rubrum", "block.minecraft.banner.diagonal_up_right.white": "Per anfractum sinistrae conversum album", "block.minecraft.banner.diagonal_up_right.yellow": "Per anfractum sinistrae conversum flavum", "block.minecraft.banner.flow.black": "Fluxus niger", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON> caeruleus", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON> brunneus", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON> cyaneus", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> cinereus", "block.minecraft.banner.flow.green": "Flux<PERSON> viridis", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON> aerius", "block.minecraft.banner.flow.light_gray": "Fluxus canus", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON> prasinus", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON> rubropurpureus", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON> aurantius", "block.minecraft.banner.flow.pink": "Fluxus roseus", "block.minecraft.banner.flow.purple": "<PERSON>lux<PERSON> purpureus", "block.minecraft.banner.flow.red": "<PERSON>lux<PERSON> ruber", "block.minecraft.banner.flow.white": "Fluxus albus", "block.minecraft.banner.flow.yellow": "Fluxus flavus", "block.minecraft.banner.flower.black": "Imago floris nigrum", "block.minecraft.banner.flower.blue": "Imago floris caeruleum", "block.minecraft.banner.flower.brown": "Imago floris brunneum", "block.minecraft.banner.flower.cyan": "Imago floris cyaneum", "block.minecraft.banner.flower.gray": "Imago floris cinereum", "block.minecraft.banner.flower.green": "Imago floris viride", "block.minecraft.banner.flower.light_blue": "Imago floris aerium", "block.minecraft.banner.flower.light_gray": "Imago floris canum", "block.minecraft.banner.flower.lime": "Imago floris prasinum", "block.minecraft.banner.flower.magenta": "Imago floris rubrop<PERSON>ei", "block.minecraft.banner.flower.orange": "Imago floris aurantium", "block.minecraft.banner.flower.pink": "Imago floris roseum", "block.minecraft.banner.flower.purple": "Imago floris purpureum", "block.minecraft.banner.flower.red": "Imago floris rubrum", "block.minecraft.banner.flower.white": "Imago floris album", "block.minecraft.banner.flower.yellow": "Imago floris flavum", "block.minecraft.banner.globe.black": "Globus niger", "block.minecraft.banner.globe.blue": "Globus caeruleus", "block.minecraft.banner.globe.brown": "Globus brunneus", "block.minecraft.banner.globe.cyan": "Globus cyaneus", "block.minecraft.banner.globe.gray": "Globus cinereus", "block.minecraft.banner.globe.green": "Globus viridis", "block.minecraft.banner.globe.light_blue": "Globus aerius", "block.minecraft.banner.globe.light_gray": "Globus canus", "block.minecraft.banner.globe.lime": "Globus prasinus", "block.minecraft.banner.globe.magenta": "Globus rubropurpureus", "block.minecraft.banner.globe.orange": "Globus aurantius", "block.minecraft.banner.globe.pink": "Globus roseus", "block.minecraft.banner.globe.purple": "Globus purpureus", "block.minecraft.banner.globe.red": "Globus ruber", "block.minecraft.banner.globe.white": "Globus albus", "block.minecraft.banner.globe.yellow": "Globus flavus", "block.minecraft.banner.gradient.black": "<PERSON>li<PERSON>s niger", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON><PERSON> caeruleus", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON> brunneus", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON> cyaneus", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON><PERSON> cinereus", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON> viridis", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON><PERSON> aerius", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON><PERSON> canus", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON> prasinus", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON> rubropurpureus", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON><PERSON> aurantius", "block.minecraft.banner.gradient.pink": "<PERSON>li<PERSON><PERSON> roseus", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON><PERSON> purpureus", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON><PERSON> ruber", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON> albus", "block.minecraft.banner.gradient.yellow": "Clivus flavus", "block.minecraft.banner.gradient_up.black": "Clivus basis niger", "block.minecraft.banner.gradient_up.blue": "Clivus basis caeruleus", "block.minecraft.banner.gradient_up.brown": "Clivus basis brunneus", "block.minecraft.banner.gradient_up.cyan": "Clivus basis cyaneus", "block.minecraft.banner.gradient_up.gray": "Clivus basis cinereus", "block.minecraft.banner.gradient_up.green": "Clivus basis viridis", "block.minecraft.banner.gradient_up.light_blue": "Clivus basis aerius", "block.minecraft.banner.gradient_up.light_gray": "Clivus basis canus", "block.minecraft.banner.gradient_up.lime": "<PERSON>li<PERSON><PERSON> basis prasinus", "block.minecraft.banner.gradient_up.magenta": "Cli<PERSON>s basis rubropurpureus", "block.minecraft.banner.gradient_up.orange": "Clivus basis aurantius", "block.minecraft.banner.gradient_up.pink": "Clivus basis roseus", "block.minecraft.banner.gradient_up.purple": "Clivus basis purpureus", "block.minecraft.banner.gradient_up.red": "Clivus basis ruber", "block.minecraft.banner.gradient_up.white": "Clivus basis albus", "block.minecraft.banner.gradient_up.yellow": "Clivus basis flavus", "block.minecraft.banner.guster.black": "<PERSON>or niger", "block.minecraft.banner.guster.blue": "<PERSON><PERSON> caeruleus", "block.minecraft.banner.guster.brown": "<PERSON><PERSON> brunneus", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON> cyaneus", "block.minecraft.banner.guster.gray": "<PERSON><PERSON> cinereus", "block.minecraft.banner.guster.green": "<PERSON><PERSON> viridis", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON> aerius", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON> canus", "block.minecraft.banner.guster.lime": "<PERSON><PERSON> prasinus", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON> rub<PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON> aurantius", "block.minecraft.banner.guster.pink": "<PERSON><PERSON> roseus", "block.minecraft.banner.guster.purple": "<PERSON><PERSON> purpureus", "block.minecraft.banner.guster.red": "<PERSON><PERSON> ruber", "block.minecraft.banner.guster.white": "<PERSON><PERSON> albus", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON> flavitus", "block.minecraft.banner.half_horizontal.black": "Per fasciam nigram", "block.minecraft.banner.half_horizontal.blue": "Per fasciam caeruleam", "block.minecraft.banner.half_horizontal.brown": "Per fasciam brunneam", "block.minecraft.banner.half_horizontal.cyan": "Per fasciam cyaneam", "block.minecraft.banner.half_horizontal.gray": "Per fasciam cineream", "block.minecraft.banner.half_horizontal.green": "Per fasciam viridem", "block.minecraft.banner.half_horizontal.light_blue": "Per fasciam aeriam", "block.minecraft.banner.half_horizontal.light_gray": "Per fasciam canam", "block.minecraft.banner.half_horizontal.lime": "Per fasciam prasinam", "block.minecraft.banner.half_horizontal.magenta": "Per fasciam rubropurpuream", "block.minecraft.banner.half_horizontal.orange": "Per fasciam aurantiam", "block.minecraft.banner.half_horizontal.pink": "Per fasciam roseam", "block.minecraft.banner.half_horizontal.purple": "Per fasciam purpuream", "block.minecraft.banner.half_horizontal.red": "Per fasciam rubram", "block.minecraft.banner.half_horizontal.white": "Per fasciam albam", "block.minecraft.banner.half_horizontal.yellow": "Per fasciam flavam", "block.minecraft.banner.half_horizontal_bottom.black": "Per fasciam conversam nigram", "block.minecraft.banner.half_horizontal_bottom.blue": "Per fasciam conversam caeruleam", "block.minecraft.banner.half_horizontal_bottom.brown": "Per fasciam conversam brunneam", "block.minecraft.banner.half_horizontal_bottom.cyan": "Per fasciam conversam cyaneam", "block.minecraft.banner.half_horizontal_bottom.gray": "Per fasciam conversam cineream", "block.minecraft.banner.half_horizontal_bottom.green": "Per fasciam conversam viridem", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Per fasciam conversam aeriam", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Per fasciam conversam canam", "block.minecraft.banner.half_horizontal_bottom.lime": "Per fasciam conversam prasinam", "block.minecraft.banner.half_horizontal_bottom.magenta": "Per fasciam conversam rubropurpuream", "block.minecraft.banner.half_horizontal_bottom.orange": "Per fasciam conversam aurantiam", "block.minecraft.banner.half_horizontal_bottom.pink": "Per fasciam conversam roseam", "block.minecraft.banner.half_horizontal_bottom.purple": "Per fasciam conversam purpuream", "block.minecraft.banner.half_horizontal_bottom.red": "Per fasciam conversam rubram", "block.minecraft.banner.half_horizontal_bottom.white": "Per fasciam conversam albam", "block.minecraft.banner.half_horizontal_bottom.yellow": "Per fasciam conversam flavam", "block.minecraft.banner.half_vertical.black": "Per palum nigrum", "block.minecraft.banner.half_vertical.blue": "Per palum caeruleum", "block.minecraft.banner.half_vertical.brown": "Per palum brunneum", "block.minecraft.banner.half_vertical.cyan": "Per palum cyaneum", "block.minecraft.banner.half_vertical.gray": "Per palum cinereum", "block.minecraft.banner.half_vertical.green": "Per palum viridem", "block.minecraft.banner.half_vertical.light_blue": "Per palum aerium", "block.minecraft.banner.half_vertical.light_gray": "Per palum canum", "block.minecraft.banner.half_vertical.lime": "Per palum prasinum", "block.minecraft.banner.half_vertical.magenta": "Per palum rubropurpureum", "block.minecraft.banner.half_vertical.orange": "Per palum aurantium", "block.minecraft.banner.half_vertical.pink": "Per palum roseum", "block.minecraft.banner.half_vertical.purple": "Per palum purpureum", "block.minecraft.banner.half_vertical.red": "Per palum rubrum", "block.minecraft.banner.half_vertical.white": "Per palum album", "block.minecraft.banner.half_vertical.yellow": "Per palum flavum", "block.minecraft.banner.half_vertical_right.black": "Per Palum Conversum Nigrum", "block.minecraft.banner.half_vertical_right.blue": "Per palum conversum caerulem", "block.minecraft.banner.half_vertical_right.brown": "Per palum conversum brunneum", "block.minecraft.banner.half_vertical_right.cyan": "Per palum conversum cyaneum", "block.minecraft.banner.half_vertical_right.gray": "Per palum conversum cinereum", "block.minecraft.banner.half_vertical_right.green": "Per palum conversum viridem", "block.minecraft.banner.half_vertical_right.light_blue": "Per palum conversum aerium", "block.minecraft.banner.half_vertical_right.light_gray": "Per palum conversum canum", "block.minecraft.banner.half_vertical_right.lime": "Per palum conversum prasinum", "block.minecraft.banner.half_vertical_right.magenta": "Per palum conversum rubropurpureum", "block.minecraft.banner.half_vertical_right.orange": "Per palum conversum aurantium", "block.minecraft.banner.half_vertical_right.pink": "Per palum conversum roseum", "block.minecraft.banner.half_vertical_right.purple": "Per palum conversum purpureum", "block.minecraft.banner.half_vertical_right.red": "Per palum conversum rubrum", "block.minecraft.banner.half_vertical_right.white": "Per palum conversum album", "block.minecraft.banner.half_vertical_right.yellow": "Per palum conversum flavum", "block.minecraft.banner.mojang.black": "Res nigra", "block.minecraft.banner.mojang.blue": "<PERSON>s caerulea", "block.minecraft.banner.mojang.brown": "Res brunnea", "block.minecraft.banner.mojang.cyan": "Res cyanea", "block.minecraft.banner.mojang.gray": "Res cinerea", "block.minecraft.banner.mojang.green": "Res viridis", "block.minecraft.banner.mojang.light_blue": "Res aeria", "block.minecraft.banner.mojang.light_gray": "<PERSON>s cana", "block.minecraft.banner.mojang.lime": "Res prasina", "block.minecraft.banner.mojang.magenta": "Res rubropurpurea", "block.minecraft.banner.mojang.orange": "Res aurantia", "block.minecraft.banner.mojang.pink": "<PERSON>s rosea", "block.minecraft.banner.mojang.purple": "Res purpurea", "block.minecraft.banner.mojang.red": "Res rubra", "block.minecraft.banner.mojang.white": "Res alba", "block.minecraft.banner.mojang.yellow": "Res flava", "block.minecraft.banner.piglin.black": "Rostrum nigrum", "block.minecraft.banner.piglin.blue": "Rostrum caeruleum", "block.minecraft.banner.piglin.brown": "Rostrum brunneum", "block.minecraft.banner.piglin.cyan": "Rostrum cyaneum", "block.minecraft.banner.piglin.gray": "Rostrum cinereum", "block.minecraft.banner.piglin.green": "Rostrum viride", "block.minecraft.banner.piglin.light_blue": "Rostrum aerium", "block.minecraft.banner.piglin.light_gray": "Rostrum canum", "block.minecraft.banner.piglin.lime": "Rostrum prasinum", "block.minecraft.banner.piglin.magenta": "Rostrum rubropurpureum", "block.minecraft.banner.piglin.orange": "Rostrum aurantium", "block.minecraft.banner.piglin.pink": "Rostrum roseum", "block.minecraft.banner.piglin.purple": "Rostrum purpureum", "block.minecraft.banner.piglin.red": "Rostrum rubrum", "block.minecraft.banner.piglin.white": "Rostrum album", "block.minecraft.banner.piglin.yellow": "Rostrum flavum", "block.minecraft.banner.rhombus.black": "Rhombus niger", "block.minecraft.banner.rhombus.blue": "Rhombus caeruleus", "block.minecraft.banner.rhombus.brown": "Rhombus brunneus", "block.minecraft.banner.rhombus.cyan": "Rhombus cyaneus", "block.minecraft.banner.rhombus.gray": "Rhombus cinereus", "block.minecraft.banner.rhombus.green": "Rhombus viridis", "block.minecraft.banner.rhombus.light_blue": "Rhombus aerius", "block.minecraft.banner.rhombus.light_gray": "Rhombus canus", "block.minecraft.banner.rhombus.lime": "Rhombus prasinus", "block.minecraft.banner.rhombus.magenta": "Rhombus rubropurpureus", "block.minecraft.banner.rhombus.orange": "Rhombus aurantius", "block.minecraft.banner.rhombus.pink": "Rhombus roseus", "block.minecraft.banner.rhombus.purple": "Rhom<PERSON> purpureus", "block.minecraft.banner.rhombus.red": "Rhombus ruber", "block.minecraft.banner.rhombus.white": "Rhombus albus", "block.minecraft.banner.rhombus.yellow": "Rhombus flavus", "block.minecraft.banner.skull.black": "Imago calvae nigra", "block.minecraft.banner.skull.blue": "Imago calvae caerulea", "block.minecraft.banner.skull.brown": "Imago calvae brunnea", "block.minecraft.banner.skull.cyan": "Imago calvae cyanea", "block.minecraft.banner.skull.gray": "Imago calvae cinerea", "block.minecraft.banner.skull.green": "Imago calvae viridis", "block.minecraft.banner.skull.light_blue": "Imago calvae aeria", "block.minecraft.banner.skull.light_gray": "Imago calvae cana", "block.minecraft.banner.skull.lime": "Imago calvae prasina", "block.minecraft.banner.skull.magenta": "Imago calvae rubropurpurea", "block.minecraft.banner.skull.orange": "Imago calvae aurantia", "block.minecraft.banner.skull.pink": "Imago calvae rosea", "block.minecraft.banner.skull.purple": "Imago calvae purpurea", "block.minecraft.banner.skull.red": "Imago calvae rubra", "block.minecraft.banner.skull.white": "Imago calvae alba", "block.minecraft.banner.skull.yellow": "Imago calvae flava", "block.minecraft.banner.small_stripes.black": "Pali niger", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON> caeruleus", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON> brunneus", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON> cyaneus", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON> cinereus", "block.minecraft.banner.small_stripes.green": "Pali viridisa", "block.minecraft.banner.small_stripes.light_blue": "Pa<PERSON> aerius", "block.minecraft.banner.small_stripes.light_gray": "Pali canus", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON> prasinus", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "Pali aurantius", "block.minecraft.banner.small_stripes.pink": "Pali roseus", "block.minecraft.banner.small_stripes.purple": "Pali purpureus", "block.minecraft.banner.small_stripes.red": "Pali ruber", "block.minecraft.banner.small_stripes.white": "Pali albus", "block.minecraft.banner.small_stripes.yellow": "Pali flavus", "block.minecraft.banner.square_bottom_left.black": "Bas dexterae cantonis nigra", "block.minecraft.banner.square_bottom_left.blue": "Bas dexterae cantonis caerulea", "block.minecraft.banner.square_bottom_left.brown": "Bas dexterae cantonis brunnea", "block.minecraft.banner.square_bottom_left.cyan": "Bas dexterae cantonis cyanea", "block.minecraft.banner.square_bottom_left.gray": "Bas dexterae cantonis cinerea", "block.minecraft.banner.square_bottom_left.green": "Bas dexterae cantonis viridis", "block.minecraft.banner.square_bottom_left.light_blue": "Bas dexterae cantonis aeria", "block.minecraft.banner.square_bottom_left.light_gray": "Bas dexterae cantonis cana", "block.minecraft.banner.square_bottom_left.lime": "Bas dexterae cantonis prasina", "block.minecraft.banner.square_bottom_left.magenta": "Bas dexterae cantonis rubropurpurea", "block.minecraft.banner.square_bottom_left.orange": "Bas dexterae cantonis aurantia", "block.minecraft.banner.square_bottom_left.pink": "Bas dexterae cantonis rosea", "block.minecraft.banner.square_bottom_left.purple": "Bas dexterae cantonis purpurea", "block.minecraft.banner.square_bottom_left.red": "Bas dexterae cantonis rubra", "block.minecraft.banner.square_bottom_left.white": "Bas dexterae cantonis alba", "block.minecraft.banner.square_bottom_left.yellow": "Bas dexterae cantonis flava", "block.minecraft.banner.square_bottom_right.black": "Angulo deorsum sinistro nigro distinctum", "block.minecraft.banner.square_bottom_right.blue": "Angulo deorsum sinistro caeruleo distinctum", "block.minecraft.banner.square_bottom_right.brown": "Angulo deorsum sinistro brunneo distinctum", "block.minecraft.banner.square_bottom_right.cyan": "Angulo deorsum sinistro cyaneo distinctum", "block.minecraft.banner.square_bottom_right.gray": "Angulo deorsum sinistro cinereo distinctum", "block.minecraft.banner.square_bottom_right.green": "Angulo deorsum sinistro viridi distinctum", "block.minecraft.banner.square_bottom_right.light_blue": "Angulo deorsum sinistro aerio distinctum", "block.minecraft.banner.square_bottom_right.light_gray": "Angulo deorsum sinistro cano distinctum", "block.minecraft.banner.square_bottom_right.lime": "Angulo deorsum sinistro prasino distinctum", "block.minecraft.banner.square_bottom_right.magenta": "Angulo deorsum sinistro rubropurpureo distinctum", "block.minecraft.banner.square_bottom_right.orange": "Angulo deorsum sinistro aurantio distinctum", "block.minecraft.banner.square_bottom_right.pink": "Angulo deorsum sinistro roseo distinctum", "block.minecraft.banner.square_bottom_right.purple": "Angulo deorsum sinistro purpureo distinctum", "block.minecraft.banner.square_bottom_right.red": "Angulo deorsum sinistro rubro distinctum", "block.minecraft.banner.square_bottom_right.white": "Angulo deorsum sinistro albo distinctum", "block.minecraft.banner.square_bottom_right.yellow": "Angulo deorsum sinistro flavo distinctum", "block.minecraft.banner.square_top_left.black": "Caput dextrae cantonis nigri", "block.minecraft.banner.square_top_left.blue": "Caput dextrae cantonis caerulei", "block.minecraft.banner.square_top_left.brown": "Caput dextrae cantonis brunnei", "block.minecraft.banner.square_top_left.cyan": "Caput dextrae cantonis cyanei", "block.minecraft.banner.square_top_left.gray": "Caput dextrae cantonis cinerei", "block.minecraft.banner.square_top_left.green": "Caput dextrae cantonis viridis", "block.minecraft.banner.square_top_left.light_blue": "Caput dextrae cantonis aerii", "block.minecraft.banner.square_top_left.light_gray": "Caput dextrae cantonis cani", "block.minecraft.banner.square_top_left.lime": "Caput dextrae cantonis prasini", "block.minecraft.banner.square_top_left.magenta": "Caput dextrae cantonis rubropurpurei", "block.minecraft.banner.square_top_left.orange": "Caput dextrae cantonis aurantii", "block.minecraft.banner.square_top_left.pink": "Caput dextrae cantonis rosei", "block.minecraft.banner.square_top_left.purple": "Caput dextrae cantonis purpurei", "block.minecraft.banner.square_top_left.red": "Caput dextrae cantonis rubri", "block.minecraft.banner.square_top_left.white": "Caput dextrae cantonis albi", "block.minecraft.banner.square_top_left.yellow": "Caput dextrae cantonis flavi", "block.minecraft.banner.square_top_right.black": "Caput sinistrae cantonis nigri", "block.minecraft.banner.square_top_right.blue": "Caput sinistrae cantonis caerulei", "block.minecraft.banner.square_top_right.brown": "Caput sinistrae cantonis brunnei", "block.minecraft.banner.square_top_right.cyan": "Caput sinistrae cantonis cyanei", "block.minecraft.banner.square_top_right.gray": "Caput sinistrae cantonis cinerei", "block.minecraft.banner.square_top_right.green": "Caput sinistrae cantonis viridis", "block.minecraft.banner.square_top_right.light_blue": "Caput sinistrae cantonis aerii", "block.minecraft.banner.square_top_right.light_gray": "Caput sinistrae cantonis cani", "block.minecraft.banner.square_top_right.lime": "Caput sinistrae cantonis prasini", "block.minecraft.banner.square_top_right.magenta": "Caput sinistrae cantonis rubropurpurei", "block.minecraft.banner.square_top_right.orange": "Caput sinistrae cantonis aurantii", "block.minecraft.banner.square_top_right.pink": "Caput sinistrae cantonis rosei", "block.minecraft.banner.square_top_right.purple": "Caput sinistrae cantonis purpurei", "block.minecraft.banner.square_top_right.red": "Caput sinistrae cantonis rubri", "block.minecraft.banner.square_top_right.white": "Caput sinistrae cantonis albi", "block.minecraft.banner.square_top_right.yellow": "Caput sinistrae cantonis flavi", "block.minecraft.banner.straight_cross.black": "Crux nigra", "block.minecraft.banner.straight_cross.blue": "C<PERSON>x caerulea", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON>x brunnea", "block.minecraft.banner.straight_cross.cyan": "Crux cyanea", "block.minecraft.banner.straight_cross.gray": "Crux cinerea", "block.minecraft.banner.straight_cross.green": "Crux viridis", "block.minecraft.banner.straight_cross.light_blue": "Crux aeria", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON> cana", "block.minecraft.banner.straight_cross.lime": "Crux prasina", "block.minecraft.banner.straight_cross.magenta": "Crux rubropurpurea", "block.minecraft.banner.straight_cross.orange": "Crux aurantia", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON>x rosea", "block.minecraft.banner.straight_cross.purple": "Crux purpurea", "block.minecraft.banner.straight_cross.red": "Crux rubra", "block.minecraft.banner.straight_cross.white": "Crux alba", "block.minecraft.banner.straight_cross.yellow": "Crux flava", "block.minecraft.banner.stripe_bottom.black": "Bas nigra", "block.minecraft.banner.stripe_bottom.blue": "<PERSON>s caerulea", "block.minecraft.banner.stripe_bottom.brown": "Bas brunnea", "block.minecraft.banner.stripe_bottom.cyan": "Bas cyanea", "block.minecraft.banner.stripe_bottom.gray": "Bas cinerea", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON> viridis", "block.minecraft.banner.stripe_bottom.light_blue": "Bas aeria", "block.minecraft.banner.stripe_bottom.light_gray": "Bas cana", "block.minecraft.banner.stripe_bottom.lime": "Bas prasina", "block.minecraft.banner.stripe_bottom.magenta": "Bas rubropurpurea", "block.minecraft.banner.stripe_bottom.orange": "Bas aurantia", "block.minecraft.banner.stripe_bottom.pink": "Bas rosea", "block.minecraft.banner.stripe_bottom.purple": "Bas purpurea", "block.minecraft.banner.stripe_bottom.red": "Bas rubra", "block.minecraft.banner.stripe_bottom.white": "Bas alba", "block.minecraft.banner.stripe_bottom.yellow": "Bas flava", "block.minecraft.banner.stripe_center.black": "Palus niger", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> caeruleus", "block.minecraft.banner.stripe_center.brown": "Palus brunneus", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON> cyaneus", "block.minecraft.banner.stripe_center.gray": "Pa<PERSON> cinereus", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON> viridis", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON> aerius", "block.minecraft.banner.stripe_center.light_gray": "Palus canus", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON> prasinus", "block.minecraft.banner.stripe_center.magenta": "Palus rubropurpureus", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON> aurantius", "block.minecraft.banner.stripe_center.pink": "Palus roseus", "block.minecraft.banner.stripe_center.purple": "Palus purpureus", "block.minecraft.banner.stripe_center.red": "Palus ruber", "block.minecraft.banner.stripe_center.white": "Palus albus", "block.minecraft.banner.stripe_center.yellow": "Palus flavus", "block.minecraft.banner.stripe_downleft.black": "Anfractus sinistrae niger", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON> sinistrae caeruleus", "block.minecraft.banner.stripe_downleft.brown": "An<PERSON><PERSON> sinistrae brunneus", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON> sinistrae cyaneus", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON><PERSON> sinistrae cinereus", "block.minecraft.banner.stripe_downleft.green": "An<PERSON><PERSON> sinistrae viridis", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON><PERSON> sinistrae aerius", "block.minecraft.banner.stripe_downleft.light_gray": "An<PERSON><PERSON> sinistrae canus", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON><PERSON> sinistrae prasinus", "block.minecraft.banner.stripe_downleft.magenta": "An<PERSON><PERSON> sinisterae rubropurpureus", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON><PERSON> sinistrae aurantius", "block.minecraft.banner.stripe_downleft.pink": "An<PERSON><PERSON> sinistrae roseus", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON><PERSON> sinistrae purpureus", "block.minecraft.banner.stripe_downleft.red": "An<PERSON><PERSON> sinistrae ruber", "block.minecraft.banner.stripe_downleft.white": "An<PERSON>ctus sinistrae albus", "block.minecraft.banner.stripe_downleft.yellow": "An<PERSON>ctus sinistrae flavus", "block.minecraft.banner.stripe_downright.black": "An<PERSON>ctus niger", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON> caeruleus", "block.minecraft.banner.stripe_downright.brown": "An<PERSON><PERSON> brunneus", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON> cyaneus", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON> cinereus", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON> viridis", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON><PERSON> aerius", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON> canus", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON> prasinus", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON> rubropurpureus", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON><PERSON> aurantius", "block.minecraft.banner.stripe_downright.pink": "An<PERSON><PERSON> roseus", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON><PERSON> purpureus", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON> ruber", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON> albus", "block.minecraft.banner.stripe_downright.yellow": "An<PERSON>ctus flavus", "block.minecraft.banner.stripe_left.black": "Palus dextrae niger", "block.minecraft.banner.stripe_left.blue": "Palus dextrae caeruleus", "block.minecraft.banner.stripe_left.brown": "Palus dextrae brunneus", "block.minecraft.banner.stripe_left.cyan": "Palus dextrae cyaneus", "block.minecraft.banner.stripe_left.gray": "Palus dextrae cinereus", "block.minecraft.banner.stripe_left.green": "Palus dextrae viridis", "block.minecraft.banner.stripe_left.light_blue": "Palus dextrae aerius", "block.minecraft.banner.stripe_left.light_gray": "Palus dextrae canus", "block.minecraft.banner.stripe_left.lime": "Palus dextrae prasinus", "block.minecraft.banner.stripe_left.magenta": "Palus dexterae rubropurpureus", "block.minecraft.banner.stripe_left.orange": "Palus dextrae aurantius", "block.minecraft.banner.stripe_left.pink": "Palus dextrae roseus", "block.minecraft.banner.stripe_left.purple": "Palus dextrae purpureus", "block.minecraft.banner.stripe_left.red": "Palus dextrae ruber", "block.minecraft.banner.stripe_left.white": "Palus dextrae albus", "block.minecraft.banner.stripe_left.yellow": "Palus dextrae flavus", "block.minecraft.banner.stripe_middle.black": "Fascia nigra distinctum", "block.minecraft.banner.stripe_middle.blue": "Fascia caerulea distinctum", "block.minecraft.banner.stripe_middle.brown": "Fascia brunnea distinctum", "block.minecraft.banner.stripe_middle.cyan": "Fascia cyanea distinctum", "block.minecraft.banner.stripe_middle.gray": "Fascia cinerea distinctum", "block.minecraft.banner.stripe_middle.green": "Fascia viridi distinctum", "block.minecraft.banner.stripe_middle.light_blue": "Fascia aeria distinctum", "block.minecraft.banner.stripe_middle.light_gray": "Fascia cana distinctum", "block.minecraft.banner.stripe_middle.lime": "Fascia prasina distinctum", "block.minecraft.banner.stripe_middle.magenta": "Fascia rubropurpurea distinctum", "block.minecraft.banner.stripe_middle.orange": "Fascia aurantia distinctum", "block.minecraft.banner.stripe_middle.pink": "Fascia rosea distinctum", "block.minecraft.banner.stripe_middle.purple": "Fascia purpurea distinctum", "block.minecraft.banner.stripe_middle.red": "Fascia rubra distinctum", "block.minecraft.banner.stripe_middle.white": "Fascia candida distinctum", "block.minecraft.banner.stripe_middle.yellow": "Fascia flava distinctum", "block.minecraft.banner.stripe_right.black": "Palus sinistrae niger", "block.minecraft.banner.stripe_right.blue": "Palus sinistrae caeruleus", "block.minecraft.banner.stripe_right.brown": "Palus sinistrae brunneus", "block.minecraft.banner.stripe_right.cyan": "Palus sinistrae cyaneus", "block.minecraft.banner.stripe_right.gray": "Palus sinistrae cinereus", "block.minecraft.banner.stripe_right.green": "Palus sinistrae viridis", "block.minecraft.banner.stripe_right.light_blue": "Palus sinistrae aerius", "block.minecraft.banner.stripe_right.light_gray": "Palus sinistrae canus", "block.minecraft.banner.stripe_right.lime": "Palus sinistrae prasinus", "block.minecraft.banner.stripe_right.magenta": "Palus <PERSON>ae rubropurpureus", "block.minecraft.banner.stripe_right.orange": "Palus sinistrae aurantius", "block.minecraft.banner.stripe_right.pink": "Palus sinistrae roseus", "block.minecraft.banner.stripe_right.purple": "Palus sinistrae purpureus", "block.minecraft.banner.stripe_right.red": "Palus sinistrae ruber", "block.minecraft.banner.stripe_right.white": "Palus sinistrae albus", "block.minecraft.banner.stripe_right.yellow": "Palus sinistrae flavus", "block.minecraft.banner.stripe_top.black": "Caput nigra", "block.minecraft.banner.stripe_top.blue": "Caput caeruleum", "block.minecraft.banner.stripe_top.brown": "Caput brunneum", "block.minecraft.banner.stripe_top.cyan": "Caput cyaneum", "block.minecraft.banner.stripe_top.gray": "Caput cinereum", "block.minecraft.banner.stripe_top.green": "Caput viride", "block.minecraft.banner.stripe_top.light_blue": "Caput aerium", "block.minecraft.banner.stripe_top.light_gray": "Caput canum", "block.minecraft.banner.stripe_top.lime": "Caput prasinum", "block.minecraft.banner.stripe_top.magenta": "Caput rubropurpureum", "block.minecraft.banner.stripe_top.orange": "Caput aurantium", "block.minecraft.banner.stripe_top.pink": "Caput roseum", "block.minecraft.banner.stripe_top.purple": "Caput purpureum", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "Caput album", "block.minecraft.banner.stripe_top.yellow": "Caput flavum", "block.minecraft.banner.triangle_bottom.black": "Triangulum nigrum", "block.minecraft.banner.triangle_bottom.blue": "Triangulum caeruleum", "block.minecraft.banner.triangle_bottom.brown": "Triangulum brunneum", "block.minecraft.banner.triangle_bottom.cyan": "Triangulum cyaneum", "block.minecraft.banner.triangle_bottom.gray": "Triangulum cinereum", "block.minecraft.banner.triangle_bottom.green": "Triangulum viride", "block.minecraft.banner.triangle_bottom.light_blue": "Triangulum aerium", "block.minecraft.banner.triangle_bottom.light_gray": "Triangulum canum", "block.minecraft.banner.triangle_bottom.lime": "Triangulum prasinum", "block.minecraft.banner.triangle_bottom.magenta": "Triangulum rubropurpureum", "block.minecraft.banner.triangle_bottom.orange": "Triangulum aurantium", "block.minecraft.banner.triangle_bottom.pink": "Triangulum roseum", "block.minecraft.banner.triangle_bottom.purple": "Triangulum purpureum", "block.minecraft.banner.triangle_bottom.red": "Triangulum rubrum", "block.minecraft.banner.triangle_bottom.white": "Triangulum album", "block.minecraft.banner.triangle_bottom.yellow": "Triangulum flavum", "block.minecraft.banner.triangle_top.black": "Triangulum conversum nigrum", "block.minecraft.banner.triangle_top.blue": "Triangulum conversum caeruleum", "block.minecraft.banner.triangle_top.brown": "Triangulum conversum brunneum", "block.minecraft.banner.triangle_top.cyan": "Triangulum conversum cyaneum", "block.minecraft.banner.triangle_top.gray": "Triangulum conversum cinereum", "block.minecraft.banner.triangle_top.green": "Triangulum conversum viride", "block.minecraft.banner.triangle_top.light_blue": "Triangulum conversum aerium", "block.minecraft.banner.triangle_top.light_gray": "Triangulum conversum canum", "block.minecraft.banner.triangle_top.lime": "Triangulum conversum prasinum", "block.minecraft.banner.triangle_top.magenta": "Triangulum conversum rubropurpureum", "block.minecraft.banner.triangle_top.orange": "Triangulum conversum aurantium", "block.minecraft.banner.triangle_top.pink": "Triangulum conversum roseum", "block.minecraft.banner.triangle_top.purple": "Triangulum conversum purpureum", "block.minecraft.banner.triangle_top.red": "Triangulum conversum rubrum", "block.minecraft.banner.triangle_top.white": "Triangulum Conversum Album", "block.minecraft.banner.triangle_top.yellow": "Triangulum conversum flavum", "block.minecraft.banner.triangles_bottom.black": "Bas curva nigra", "block.minecraft.banner.triangles_bottom.blue": "Bas curva caerulea", "block.minecraft.banner.triangles_bottom.brown": "Bas curva brunnea", "block.minecraft.banner.triangles_bottom.cyan": "Bas curva cyanea", "block.minecraft.banner.triangles_bottom.gray": "Bas curva cinerea", "block.minecraft.banner.triangles_bottom.green": "Bas curva viridis", "block.minecraft.banner.triangles_bottom.light_blue": "Bas curva aeria", "block.minecraft.banner.triangles_bottom.light_gray": "Bas curva cana", "block.minecraft.banner.triangles_bottom.lime": "Bas curva prasina", "block.minecraft.banner.triangles_bottom.magenta": "Bas curva rubropurpurea", "block.minecraft.banner.triangles_bottom.orange": "Bas curva aurantia", "block.minecraft.banner.triangles_bottom.pink": "Bas curva rosea", "block.minecraft.banner.triangles_bottom.purple": "Bas curva purpurea", "block.minecraft.banner.triangles_bottom.red": "Bas curva rubra", "block.minecraft.banner.triangles_bottom.white": "Bas curva alba", "block.minecraft.banner.triangles_bottom.yellow": "Bas curva flava", "block.minecraft.banner.triangles_top.black": "Caput curvus niger", "block.minecraft.banner.triangles_top.blue": "Caput curvus caeruleus", "block.minecraft.banner.triangles_top.brown": "Caput curvus brunneus", "block.minecraft.banner.triangles_top.cyan": "Caput curvus cyaneus", "block.minecraft.banner.triangles_top.gray": "Caput curvus cinereus", "block.minecraft.banner.triangles_top.green": "Caput curvus viridis", "block.minecraft.banner.triangles_top.light_blue": "Caput curvus aerius", "block.minecraft.banner.triangles_top.light_gray": "Caput curvus canus", "block.minecraft.banner.triangles_top.lime": "Caput curvus prasinus", "block.minecraft.banner.triangles_top.magenta": "Caput curvus rubropurpurea", "block.minecraft.banner.triangles_top.orange": "Caput curvus aurantius", "block.minecraft.banner.triangles_top.pink": "Caput curvus roseus", "block.minecraft.banner.triangles_top.purple": "Caput curvus purpureus", "block.minecraft.banner.triangles_top.red": "Caput curvus ruber", "block.minecraft.banner.triangles_top.white": "Caput curvus albus", "block.minecraft.banner.triangles_top.yellow": "Caput curvus flavus", "block.minecraft.barrel": "Cupa", "block.minecraft.barrier": "Claustrum", "block.minecraft.basalt": "Basaltes", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "Prima potentia", "block.minecraft.beacon.secondary": "Secunda potentia", "block.minecraft.bed.no_sleep": "Nocte solum aut in tempestatibus dormire licet", "block.minecraft.bed.not_safe": "Dormire non potes, monstra propinqua sunt", "block.minecraft.bed.obstructed": "Hic lectus obstructus est", "block.minecraft.bed.occupied": "Hic lectus occupatus est", "block.minecraft.bed.too_far_away": "Dorm<PERSON> non potes; ab lecto nimis abes", "block.minecraft.bedrock": "Saxum infimum", "block.minecraft.bee_nest": "Nidus apium", "block.minecraft.beehive": "Alvearium", "block.minecraft.beetroots": "Betae", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Folia inclinabilis lata", "block.minecraft.big_dripleaf_stem": "Caulis foliae inclinabilis latae", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON> betullae", "block.minecraft.birch_door": "Ostium betulae", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON>ae", "block.minecraft.birch_fence_gate": "<PERSON><PERSON> betullae", "block.minecraft.birch_hanging_sign": "Titulus pendens betullae", "block.minecraft.birch_leaves": "Foliae betullae", "block.minecraft.birch_log": "<PERSON><PERSON><PERSON> betullae", "block.minecraft.birch_planks": "Tabulae betullae", "block.minecraft.birch_pressure_plate": "Tabula premenda betullae", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON> betullae", "block.minecraft.birch_sign": "<PERSON>it<PERSON> betullae", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON>ae", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON> betullae", "block.minecraft.birch_trapdoor": "Ostium horizontale betulae", "block.minecraft.birch_wall_hanging_sign": "Titulus pendens muralis betullae", "block.minecraft.birch_wall_sign": "<PERSON>it<PERSON> muralis betullae", "block.minecraft.birch_wood": "Lignum betullae", "block.minecraft.black_banner": "Vexillum nigrum", "block.minecraft.black_bed": "Lectus niger", "block.minecraft.black_candle": "Candela nigra", "block.minecraft.black_candle_cake": "Placenta cum candela nigra", "block.minecraft.black_carpet": "Tapes niger", "block.minecraft.black_concrete": "Opus caementicium nigrum", "block.minecraft.black_concrete_powder": "Caementum nigrum", "block.minecraft.black_glazed_terracotta": "Terra cocta smaltata nigra", "block.minecraft.black_shulker_box": "<PERSON><PERSON> shulker nigra", "block.minecraft.black_stained_glass": "Vitrum tinctum nigrum", "block.minecraft.black_stained_glass_pane": "Fenestra vitrea tincta nigra", "block.minecraft.black_terracotta": "Terra cocta nigra", "block.minecraft.black_wool": "<PERSON> nigra", "block.minecraft.blackstone": "Saxum nigrum", "block.minecraft.blackstone_slab": "<PERSON><PERSON>us saxi nigri", "block.minecraft.blackstone_stairs": "Scalae saxi nigri", "block.minecraft.blackstone_wall": "Murus saxi nigri", "block.minecraft.blast_furnace": "Caminus", "block.minecraft.blue_banner": "Vexillum venetum", "block.minecraft.blue_bed": "Lectus caeruleus", "block.minecraft.blue_candle": "Candela caerulea", "block.minecraft.blue_candle_cake": "Placenta cum candela caerulea", "block.minecraft.blue_carpet": "<PERSON><PERSON> caeruleus", "block.minecraft.blue_concrete": "Opus caementicium caeruleum", "block.minecraft.blue_concrete_powder": "Caementum caeruleum", "block.minecraft.blue_glazed_terracotta": "Terra cocta smaltata caerulea", "block.minecraft.blue_ice": "Glacies caerulea", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON> caerulea", "block.minecraft.blue_shulker_box": "<PERSON><PERSON> shulker caerulea", "block.minecraft.blue_stained_glass": "Vitrum tinctum caeruleum", "block.minecraft.blue_stained_glass_pane": "Fenestra vitrea tincta caerulea", "block.minecraft.blue_terracotta": "Terra cocta caerulea", "block.minecraft.blue_wool": "<PERSON> caerulea", "block.minecraft.bone_block": "<PERSON><PERSON><PERSON> osseus", "block.minecraft.bookshelf": "Armarium", "block.minecraft.brain_coral": "Corallium ramosum", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON>i <PERSON>ii", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON> ramosa", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> ramosa horizontalis", "block.minecraft.brewing_stand": "Mixtarius", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON> later<PERSON>us", "block.minecraft.brick_stairs": "Scalae latericiae", "block.minecraft.brick_wall": "<PERSON><PERSON> latericius", "block.minecraft.bricks": "Lateres", "block.minecraft.brown_banner": "Vexillum brunneum", "block.minecraft.brown_bed": "Lectus brunneus", "block.minecraft.brown_candle": "Candela brunnea", "block.minecraft.brown_candle_cake": "Placenta cum candela brunnea", "block.minecraft.brown_carpet": "<PERSON><PERSON> brunneus", "block.minecraft.brown_concrete": "Opus caementicium brunneum", "block.minecraft.brown_concrete_powder": "Caementum brunneum", "block.minecraft.brown_glazed_terracotta": "Terra cocta smaltata brunnea", "block.minecraft.brown_mushroom": "Fungus brunneus", "block.minecraft.brown_mushroom_block": "Cubus fungi bruni", "block.minecraft.brown_shulker_box": "<PERSON><PERSON> shulker brunnea", "block.minecraft.brown_stained_glass": "Vitrum tinctum brunneum", "block.minecraft.brown_stained_glass_pane": "Fenestra vitrea tincta brunea", "block.minecraft.brown_terracotta": "Terra cocta brunnea", "block.minecraft.brown_wool": "<PERSON> brunnea", "block.minecraft.bubble_column": "<PERSON><PERSON>", "block.minecraft.bubble_coral": "Corallium bullatum", "block.minecraft.bubble_coral_block": "<PERSON><PERSON><PERSON>i bullati", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON> bullata", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON>ia bullata horizontalis", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON> gem<PERSON>", "block.minecraft.bush": "Frutex", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "<PERSON><PERSON> cacti", "block.minecraft.cake": "Placenta", "block.minecraft.calcite": "Asbertites", "block.minecraft.calibrated_sculk_sensor": "Sculk metiens", "block.minecraft.campfire": "Focus", "block.minecraft.candle": "Candela", "block.minecraft.candle_cake": "Placenta cum candela", "block.minecraft.carrots": "Carotae", "block.minecraft.cartography_table": "Mensa chartographiae", "block.minecraft.carved_pumpkin": "Curcurbita sculpta", "block.minecraft.cauldron": "Cortina", "block.minecraft.cave_air": "Aer speluncarum", "block.minecraft.cave_vines": "Vineae cavernarum", "block.minecraft.cave_vines_plant": "Stirps vinearum cavernarum", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "<PERSON><PERSON><PERSON> i<PERSON>orum concatenatus", "block.minecraft.cherry_button": "<PERSON><PERSON><PERSON> cerasi", "block.minecraft.cherry_door": "Ost<PERSON> cerasi", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON> cera<PERSON>", "block.minecraft.cherry_hanging_sign": "Titulus pendens cerasi", "block.minecraft.cherry_leaves": "Foliae cerasi", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON> cera<PERSON>", "block.minecraft.cherry_planks": "Tabulae cerasi", "block.minecraft.cherry_pressure_plate": "Tabula premenda cerasi", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON> cerasi", "block.minecraft.cherry_sign": "<PERSON>it<PERSON> cerasi", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON> cera<PERSON>", "block.minecraft.cherry_stairs": "<PERSON>ala<PERSON> cera<PERSON>", "block.minecraft.cherry_trapdoor": "Ostium horizontale cerasi", "block.minecraft.cherry_wall_hanging_sign": "Titulus pendens muralis cerasi", "block.minecraft.cherry_wall_sign": "<PERSON>itulus muralis cerasi", "block.minecraft.cherry_wood": "Lignum cerasi", "block.minecraft.chest": "Cista", "block.minecraft.chipped_anvil": "Incus dedolatus", "block.minecraft.chiseled_bookshelf": "Armarium caelatum", "block.minecraft.chiseled_copper": "Cuprum caelatum", "block.minecraft.chiseled_deepslate": "Lapis sectilis altus caelatus", "block.minecraft.chiseled_nether_bricks": "Lateres <PERSON>her caelati", "block.minecraft.chiseled_polished_blackstone": "Saxum nigrum politum caelatum", "block.minecraft.chiseled_quartz_block": "<PERSON>ub<PERSON> crystallinus caelatus", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON> harenarius ruber caelatus", "block.minecraft.chiseled_resin_bricks": "Lateres resinae caelati", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON> harenarius caelatus", "block.minecraft.chiseled_stone_bricks": "Lateres saxei caelati", "block.minecraft.chiseled_tuff": "<PERSON><PERSON> caelatus", "block.minecraft.chiseled_tuff_bricks": "Lateres tophi caelati", "block.minecraft.chorus_flower": "<PERSON>los chori", "block.minecraft.chorus_plant": "<PERSON><PERSON><PERSON> chori", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON> clausus", "block.minecraft.coal_block": "<PERSON><PERSON><PERSON> carbonarius", "block.minecraft.coal_ore": "Aes carbonarium", "block.minecraft.coarse_dirt": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON> lapidis sectilis alti", "block.minecraft.cobbled_deepslate_slab": "Gradus lapillorum lapidis sectilis alti", "block.minecraft.cobbled_deepslate_stairs": "Scalae lapillorum lapidis sectilis alti", "block.minecraft.cobbled_deepslate_wall": "<PERSON>rus lapillorum lapidis sectilis alti", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON> lapillorum", "block.minecraft.cobblestone_stairs": "Scalae lapideae", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> lapideus", "block.minecraft.cobweb": "Araneum", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "<PERSON><PERSON><PERSON>orum", "block.minecraft.comparator": "Comparatrum redstone", "block.minecraft.composter": "Cupa laetaminis", "block.minecraft.conduit": "Conductus", "block.minecraft.copper_block": "Cub<PERSON> cupreus", "block.minecraft.copper_bulb": "Lumen cupreum", "block.minecraft.copper_door": "Ostium cupreum", "block.minecraft.copper_grate": "<PERSON>lathri cuprei", "block.minecraft.copper_ore": "<PERSON><PERSON> cupreum", "block.minecraft.copper_trapdoor": "Ostium horizontale cupreum", "block.minecraft.cornflower": "<PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Lateres lapidis sectilis alti <PERSON>i", "block.minecraft.cracked_deepslate_tiles": "Laterculi lapidis sectilis alti <PERSON>i", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON> <PERSON>i", "block.minecraft.cracked_polished_blackstone_bricks": "Lateres saxi nigri politi rimosi", "block.minecraft.cracked_stone_bricks": "Lateres saxei rimosi", "block.minecraft.crafter": "Fabricator", "block.minecraft.crafting_table": "Mensa fabricationis", "block.minecraft.creaking_heart": "Cor crepacis", "block.minecraft.creeper_head": "Caput creeper", "block.minecraft.creeper_wall_head": "Caput creeper murale", "block.minecraft.crimson_button": "Premendus coccinus", "block.minecraft.crimson_door": "Ostium coccinum", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON>ccina", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON> coccina", "block.minecraft.crimson_fungus": "Fungus coccinus", "block.minecraft.crimson_hanging_sign": "Titulus pendens coccinus", "block.minecraft.crimson_hyphae": "Hyphae coccinae", "block.minecraft.crimson_nylium": "Infelium coccinum", "block.minecraft.crimson_planks": "Tabulae coccinae", "block.minecraft.crimson_pressure_plate": "Tabula premenda coccina", "block.minecraft.crimson_roots": "Radices coccinae", "block.minecraft.crimson_sign": "<PERSON>it<PERSON> coccinus", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON> coccinus", "block.minecraft.crimson_stairs": "Scalae coccinae", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON> coccinus", "block.minecraft.crimson_trapdoor": "Ostium horizontale coccinum", "block.minecraft.crimson_wall_hanging_sign": "Titulus pendens muralis coccinus", "block.minecraft.crimson_wall_sign": "<PERSON>itulus muralis coccinus", "block.minecraft.crying_obsidian": "Lapis obsidianus flens", "block.minecraft.cut_copper": "Cup<PERSON> scalptus", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON> cupri <PERSON>", "block.minecraft.cut_copper_stairs": "<PERSON><PERSON><PERSON> cupri <PERSON>ti", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON> harenarius ruber scalptus", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON><PERSON> lapidis rubri harenarii <PERSON>ti", "block.minecraft.cut_sandstone": "<PERSON><PERSON> harenarius scalptus", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON> lapidis harenarii <PERSON>ti", "block.minecraft.cyan_banner": "Vexillum cyaneum", "block.minecraft.cyan_bed": "Lectus cyaneus", "block.minecraft.cyan_candle": "Candela cyanea", "block.minecraft.cyan_candle_cake": "Placenta cum candela cyanea", "block.minecraft.cyan_carpet": "<PERSON><PERSON> cyaneus", "block.minecraft.cyan_concrete": "Opus caementicium cyaneum", "block.minecraft.cyan_concrete_powder": "Caementum cyaneum", "block.minecraft.cyan_glazed_terracotta": "Terra cocta smaltata cyanea", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON> shulker cyanea", "block.minecraft.cyan_stained_glass": "Vitrum tinctum cyaneum", "block.minecraft.cyan_stained_glass_pane": "Fenestra vitrea tincta cyanea", "block.minecraft.cyan_terracotta": "Terra cocta cyanea", "block.minecraft.cyan_wool": "Lana cyanea", "block.minecraft.damaged_anvil": "Incus fractus", "block.minecraft.dandelion": "Taraxacum", "block.minecraft.dark_oak_button": "Premendus quercus nigrae", "block.minecraft.dark_oak_door": "Ostium quercus nigrae", "block.minecraft.dark_oak_fence": "<PERSON><PERSON><PERSON> quercus nigrae", "block.minecraft.dark_oak_fence_gate": "Foris quercus nigrae", "block.minecraft.dark_oak_hanging_sign": "Titulus pendens quercus nigrae", "block.minecraft.dark_oak_leaves": "Foliae quercus nigrae", "block.minecraft.dark_oak_log": "<PERSON>run<PERSON> quercus nigrae", "block.minecraft.dark_oak_planks": "Tabulae quercus nigrae", "block.minecraft.dark_oak_pressure_plate": "Tabula premenda quercus nigrae", "block.minecraft.dark_oak_sapling": "Arbuscula quercus nigrae", "block.minecraft.dark_oak_sign": "Titulus quercus nigrae", "block.minecraft.dark_oak_slab": "<PERSON><PERSON><PERSON> quercus nigrae", "block.minecraft.dark_oak_stairs": "Scalae quercus nigrae", "block.minecraft.dark_oak_trapdoor": "Ostium horizontale quercus nigrae", "block.minecraft.dark_oak_wall_hanging_sign": "Titulus pendens muralis quercus nigrae", "block.minecraft.dark_oak_wall_sign": "Titulus muralis quercus nigrae", "block.minecraft.dark_oak_wood": "Lignum quercus nigrae", "block.minecraft.dark_prismarine": "Prismarina obscura", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON> prismarinus obscurus", "block.minecraft.dark_prismarine_stairs": "Scalae prismarinae obscurae", "block.minecraft.daylight_detector": "Senstrum lucis diei", "block.minecraft.dead_brain_coral": "Corallium ramosum mortuum", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON> corallii ramosi mortui", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> ramosa mortua", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> ramosa horizontalis mortua", "block.minecraft.dead_bubble_coral": "Corallium bullatum mortuum", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON> corallii bullati mortui", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON> bullata mortua", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON>ia bullata horizontalis mortua", "block.minecraft.dead_bush": "<PERSON><PERSON>x mortuus", "block.minecraft.dead_fire_coral": "Corallium ignicolorum mortuum", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON> corallii ignicolori mortui", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON> ignicolora mortua", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON>ia ignicolora horizontalis mortua", "block.minecraft.dead_horn_coral": "Corallium cornutum mortuum", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON> corallii cornuti mortui", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON>ia cornuta mortua", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON>ia cornuta horizontalis mortua", "block.minecraft.dead_tube_coral": "Corallium tubulatum mortuum", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON> corallii tubulati mortui", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON> tubulata mortua", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON> tubulata horizontalis mortua", "block.minecraft.decorated_pot": "Vas pictum", "block.minecraft.deepslate": "Lapis sectilis alti", "block.minecraft.deepslate_brick_slab": "Gradus laterum lapidis sectilis alti", "block.minecraft.deepslate_brick_stairs": "Scalae laterum lapidis sectilis alti", "block.minecraft.deepslate_brick_wall": "Murus laterum lapidis sectilis alti", "block.minecraft.deepslate_bricks": "Lateres lapidis sectilis alti", "block.minecraft.deepslate_coal_ore": "Aes carbonarium in lapide sectili alto", "block.minecraft.deepslate_copper_ore": "Aes cupreum in lapide sectili alto", "block.minecraft.deepslate_diamond_ore": "A<PERSON> adamantinum in lapide sectili alto", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON> smaragdinum in lapide sectili alto", "block.minecraft.deepslate_gold_ore": "Aes aureum in lapide sectili alto", "block.minecraft.deepslate_iron_ore": "Aes ferreum in lapide sectili alto", "block.minecraft.deepslate_lapis_ore": "A<PERSON> lapidis lazuli in lapide sectile alto", "block.minecraft.deepslate_redstone_ore": "Aes rubrolepideum in lapide sectili alto", "block.minecraft.deepslate_tile_slab": "<PERSON><PERSON><PERSON> laterculorum lapidis sectilis alti", "block.minecraft.deepslate_tile_stairs": "Scalae laterculorum lapidis sectilis alti", "block.minecraft.deepslate_tile_wall": "<PERSON>rus laterculorum lapidis sectilis alti", "block.minecraft.deepslate_tiles": "Laterculi lapidis sectilis alti", "block.minecraft.detector_rail": "Ferrivia percipens", "block.minecraft.diamond_block": "<PERSON><PERSON><PERSON> adamantinus", "block.minecraft.diamond_ore": "<PERSON><PERSON> adamantinum", "block.minecraft.diorite": "Diorites", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON> dioritae", "block.minecraft.diorite_stairs": "Scalae dioriteae", "block.minecraft.diorite_wall": "<PERSON><PERSON> dioritae", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "Semita terrena", "block.minecraft.dispenser": "Distributor", "block.minecraft.dragon_egg": "Ovum draconis", "block.minecraft.dragon_head": "<PERSON><PERSON> drac<PERSON>", "block.minecraft.dragon_wall_head": "<PERSON>ut draconis murale", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON> siccatus", "block.minecraft.dried_kelp_block": "Cubus algarum siccarum", "block.minecraft.dripstone_block": "<PERSON><PERSON><PERSON> saxi <PERSON>s", "block.minecraft.dropper": "Demittetrum", "block.minecraft.emerald_block": "<PERSON><PERSON><PERSON> smaragdinus", "block.minecraft.emerald_ore": "<PERSON><PERSON> smaragdinum", "block.minecraft.enchanting_table": "<PERSON><PERSON> in<PERSON>", "block.minecraft.end_gateway": "Transitus End", "block.minecraft.end_portal": "Porta End", "block.minecraft.end_portal_frame": "Replum portae End", "block.minecraft.end_rod": "V<PERSON>ga <PERSON>", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "<PERSON><PERSON>us laterum lapidis End", "block.minecraft.end_stone_brick_stairs": "Scalae laterum lapidis End", "block.minecraft.end_stone_brick_wall": "Murus laterum lapidis End", "block.minecraft.end_stone_bricks": "<PERSON><PERSON> lapidis <PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Cuprum caelatum obvium", "block.minecraft.exposed_copper": "Cuprum obvium", "block.minecraft.exposed_copper_bulb": "Lumen cupreum obvium", "block.minecraft.exposed_copper_door": "Ostium cupreum obvium", "block.minecraft.exposed_copper_grate": "Clathri cuprei obvii", "block.minecraft.exposed_copper_trapdoor": "Ostium horizontale cupreum obvium", "block.minecraft.exposed_cut_copper": "Cuprum scalptum obvium", "block.minecraft.exposed_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri <PERSON>ti obvius", "block.minecraft.exposed_cut_copper_stairs": "Scalae cupri scalpti obviae", "block.minecraft.farmland": "Terra arata", "block.minecraft.fern": "Filix", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "Corallium ignicolorum", "block.minecraft.fire_coral_block": "<PERSON><PERSON><PERSON> coral<PERSON>i ignicolori", "block.minecraft.fire_coral_fan": "<PERSON><PERSON><PERSON> ignicolora", "block.minecraft.fire_coral_wall_fan": "Gorgonia ignicolora horizontalis", "block.minecraft.firefly_bush": "Frutex cicindelarum", "block.minecraft.fletching_table": "Mensa sagittationis", "block.minecraft.flower_pot": "Vas", "block.minecraft.flowering_azalea": "Rhododendron floridum", "block.minecraft.flowering_azalea_leaves": "Foliae rhododendri floridi", "block.minecraft.frogspawn": "<PERSON><PERSON> ran<PERSON>", "block.minecraft.frosted_ice": "Glacies fragilis", "block.minecraft.furnace": "Fornax", "block.minecraft.gilded_blackstone": "Saxum nigrum aurifer", "block.minecraft.glass": "Vitrum", "block.minecraft.glass_pane": "Fenestra vitrea", "block.minecraft.glow_lichen": "<PERSON><PERSON> lucidus", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Cub<PERSON> aureus", "block.minecraft.gold_ore": "A<PERSON> aureum", "block.minecraft.granite": "Granitum", "block.minecraft.granite_slab": "<PERSON><PERSON><PERSON> graniteus", "block.minecraft.granite_stairs": "Scalae graniteae", "block.minecraft.granite_wall": "<PERSON><PERSON> graniteus", "block.minecraft.grass": "Gramen", "block.minecraft.grass_block": "Cub<PERSON> graminis", "block.minecraft.gravel": "G<PERSON><PERSON>", "block.minecraft.gray_banner": "Vexillum cinereum", "block.minecraft.gray_bed": "Lectus cinereus", "block.minecraft.gray_candle": "Candela cinerea", "block.minecraft.gray_candle_cake": "Placenta cum candela cinerea", "block.minecraft.gray_carpet": "<PERSON><PERSON> cinereus", "block.minecraft.gray_concrete": "Opus caementicium cinereum", "block.minecraft.gray_concrete_powder": "Caementum cinereum", "block.minecraft.gray_glazed_terracotta": "Terra cocta smaltata cinerea", "block.minecraft.gray_shulker_box": "Capsa shulker cinerea", "block.minecraft.gray_stained_glass": "Vitrum tinctum cinereum", "block.minecraft.gray_stained_glass_pane": "Fenestra vitrea tincta cinerea", "block.minecraft.gray_terracotta": "Terra cocta cinerea", "block.minecraft.gray_wool": "Lana cinerea", "block.minecraft.green_banner": "Vexillum viride", "block.minecraft.green_bed": "Lectus viridis", "block.minecraft.green_candle": "<PERSON><PERSON> viridis", "block.minecraft.green_candle_cake": "Placenta cum candela viridi", "block.minecraft.green_carpet": "Ta<PERSON> viridis", "block.minecraft.green_concrete": "Opus caementicium viride", "block.minecraft.green_concrete_powder": "Caementum viride", "block.minecraft.green_glazed_terracotta": "Terra cocta smaltata viridis", "block.minecraft.green_shulker_box": "<PERSON><PERSON> shulker viridis", "block.minecraft.green_stained_glass": "Vitrum tinctum viride", "block.minecraft.green_stained_glass_pane": "Fenestra vitrea tincta viridis", "block.minecraft.green_terracotta": "Terra cocta viridis", "block.minecraft.green_wool": "<PERSON> viridis", "block.minecraft.grindstone": "Cos", "block.minecraft.hanging_roots": "Radices pensiles", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON> faeni", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> ponderosus", "block.minecraft.heavy_weighted_pressure_plate": "Tabula premenda molibus magnis", "block.minecraft.honey_block": "<PERSON><PERSON><PERSON> mellis", "block.minecraft.honeycomb_block": "<PERSON><PERSON><PERSON> favorum", "block.minecraft.hopper": "Infundibulum", "block.minecraft.horn_coral": "Corallium cornutum", "block.minecraft.horn_coral_block": "<PERSON><PERSON><PERSON> corallii cornuti", "block.minecraft.horn_coral_fan": "<PERSON><PERSON>ia cornuta", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON>ia cornuta horizontalis", "block.minecraft.ice": "Glacies", "block.minecraft.infested_chiseled_stone_bricks": "Lateres saxei caelati infestati", "block.minecraft.infested_cobblestone": "<PERSON><PERSON>lli infestati", "block.minecraft.infested_cracked_stone_bricks": "Lateres saxei rimosi infestati", "block.minecraft.infested_deepslate": "Lapis sectilis altus infestus", "block.minecraft.infested_mossy_stone_bricks": "Lateres saxei muscosi infestati", "block.minecraft.infested_stone": "Saxum infestatum", "block.minecraft.infested_stone_bricks": "Lateres saxei infestati", "block.minecraft.iron_bars": "Vectes ferrei", "block.minecraft.iron_block": "<PERSON><PERSON><PERSON> ferreus", "block.minecraft.iron_door": "<PERSON><PERSON> ferrea", "block.minecraft.iron_ore": "<PERSON><PERSON> ferreum", "block.minecraft.iron_trapdoor": "Ostium horizontale ferreum", "block.minecraft.jack_o_lantern": "Cucurbita illuminata", "block.minecraft.jigsaw": "<PERSON><PERSON><PERSON> iunctionis", "block.minecraft.jukebox": "Phonographum", "block.minecraft.jungle_button": "Premen<PERSON> theobromatis", "block.minecraft.jungle_door": "Ostium theobromatis", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON> theobroma<PERSON>", "block.minecraft.jungle_fence_gate": "<PERSON>is theobromatis", "block.minecraft.jungle_hanging_sign": "Titulus pendens theobromatis", "block.minecraft.jungle_leaves": "Foliae theobromatis", "block.minecraft.jungle_log": "<PERSON><PERSON><PERSON> theobromatis", "block.minecraft.jungle_planks": "Tabulae theobromatis", "block.minecraft.jungle_pressure_plate": "Tabula premenda theobromatis", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON><PERSON> theobromatis", "block.minecraft.jungle_sign": "<PERSON>it<PERSON> theobromatis", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON> theobromatis", "block.minecraft.jungle_stairs": "<PERSON>ala<PERSON> theobromatis", "block.minecraft.jungle_trapdoor": "Ostium horizontale theobromatis", "block.minecraft.jungle_wall_hanging_sign": "<PERSON>it<PERSON> pendens muralis theobromatis", "block.minecraft.jungle_wall_sign": "<PERSON>it<PERSON> muralis theobromatis", "block.minecraft.jungle_wood": "Lignum theobromatis", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "Caulis algae", "block.minecraft.ladder": "Scala", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "<PERSON><PERSON><PERSON> lapidis lazuli", "block.minecraft.lapis_ore": "<PERSON><PERSON> lapidis lazuli", "block.minecraft.large_amethyst_bud": "<PERSON> amethystina maturior", "block.minecraft.large_fern": "Filix magna", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Cortina cum lava", "block.minecraft.leaf_litter": "Stramentum foliarum", "block.minecraft.lectern": "Lectrum", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "Vexillum aerium", "block.minecraft.light_blue_bed": "Lectus aerius", "block.minecraft.light_blue_candle": "Candela aeria", "block.minecraft.light_blue_candle_cake": "Placenta cum candela aeria", "block.minecraft.light_blue_carpet": "<PERSON><PERSON> aerius", "block.minecraft.light_blue_concrete": "Opus caementicium aerium", "block.minecraft.light_blue_concrete_powder": "Caementum aerium", "block.minecraft.light_blue_glazed_terracotta": "Terra cocta smaltata aeria", "block.minecraft.light_blue_shulker_box": "Capsa shulker aeria", "block.minecraft.light_blue_stained_glass": "Vitrum tinctum aerium", "block.minecraft.light_blue_stained_glass_pane": "Fenestra vitrea tincta aeria", "block.minecraft.light_blue_terracotta": "Terra cocta aeria", "block.minecraft.light_blue_wool": "Lana aeria", "block.minecraft.light_gray_banner": "Vexillum canum", "block.minecraft.light_gray_bed": "Lectus canus", "block.minecraft.light_gray_candle": "Candela cana", "block.minecraft.light_gray_candle_cake": "Placenta cum candela cana", "block.minecraft.light_gray_carpet": "<PERSON><PERSON> canus", "block.minecraft.light_gray_concrete": "Opus caementicium canum", "block.minecraft.light_gray_concrete_powder": "Caementum canum", "block.minecraft.light_gray_glazed_terracotta": "Terra cocta smaltata cana", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON> shulker cana", "block.minecraft.light_gray_stained_glass": "Vitrum tinctum canum", "block.minecraft.light_gray_stained_glass_pane": "Fenestra vitrea tincta cana", "block.minecraft.light_gray_terracotta": "Terra cocta cana", "block.minecraft.light_gray_wool": "<PERSON>a", "block.minecraft.light_weighted_pressure_plate": "Tabula permenda molibus parvis", "block.minecraft.lightning_rod": "Virga fulmina attrahens", "block.minecraft.lilac": "Syringa", "block.minecraft.lily_of_the_valley": "Convallaria", "block.minecraft.lily_pad": "Nymphaea", "block.minecraft.lime_banner": "Vexillum prasinum", "block.minecraft.lime_bed": "Lectus prasinus", "block.minecraft.lime_candle": "Candela prasina", "block.minecraft.lime_candle_cake": "Placenta cum candela prasina", "block.minecraft.lime_carpet": "<PERSON><PERSON> prasinus", "block.minecraft.lime_concrete": "Opus caementicium prasinum", "block.minecraft.lime_concrete_powder": "Caementum prasinum", "block.minecraft.lime_glazed_terracotta": "Terra cocta smaltata prasina", "block.minecraft.lime_shulker_box": "<PERSON><PERSON> shulker prasina", "block.minecraft.lime_stained_glass": "Vitrum tinctum prasinum", "block.minecraft.lime_stained_glass_pane": "Fenestra vitrea tincta prasina", "block.minecraft.lime_terracotta": "Terra cocta prasina", "block.minecraft.lime_wool": "<PERSON>", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON> lapis", "block.minecraft.loom": "Tela", "block.minecraft.magenta_banner": "Vexillum rubropurpureum", "block.minecraft.magenta_bed": "Lectus rubropurpureus", "block.minecraft.magenta_candle": "Candela rubropurpurea", "block.minecraft.magenta_candle_cake": "Placenta cum candela rubropurpurea", "block.minecraft.magenta_carpet": "Tapes rubropurpurea", "block.minecraft.magenta_concrete": "Opus caementicium rubropurpureum", "block.minecraft.magenta_concrete_powder": "Caementum rubropurpureum", "block.minecraft.magenta_glazed_terracotta": "Terra cocta smaltata rubropurpurea", "block.minecraft.magenta_shulker_box": "Cap<PERSON> shulker rubropurpurea", "block.minecraft.magenta_stained_glass": "Vitrum tinctum rubropurpureum", "block.minecraft.magenta_stained_glass_pane": "Fenestra vitrea tincta rubropurpurea", "block.minecraft.magenta_terracotta": "Terra cocta rubropurpurea", "block.minecraft.magenta_wool": "<PERSON>", "block.minecraft.magma_block": "Cub<PERSON> magmatis", "block.minecraft.mangrove_button": "Premendum manglis", "block.minecraft.mangrove_door": "Ostium manglis", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON> man<PERSON>s", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON> manglis", "block.minecraft.mangrove_hanging_sign": "Titulus pendens manglis", "block.minecraft.mangrove_leaves": "Foliae manglis", "block.minecraft.mangrove_log": "<PERSON>run<PERSON> manglis", "block.minecraft.mangrove_planks": "Tabulae manglis", "block.minecraft.mangrove_pressure_plate": "Tabula premenda manglis", "block.minecraft.mangrove_propagule": "Semen manglis", "block.minecraft.mangrove_roots": "Radices manglis", "block.minecraft.mangrove_sign": "Titulus manglis", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON> man<PERSON>s", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON> man<PERSON>s", "block.minecraft.mangrove_trapdoor": "Ostium horizontale manglis", "block.minecraft.mangrove_wall_hanging_sign": "Titulus pendens muralis manglis", "block.minecraft.mangrove_wall_sign": "Titulus muralis manglis", "block.minecraft.mangrove_wood": "Lignum manglis", "block.minecraft.medium_amethyst_bud": "Gemma amethystina medie matura", "block.minecraft.melon": "Pepo", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON> peponis", "block.minecraft.moss_block": "<PERSON><PERSON><PERSON> musci", "block.minecraft.moss_carpet": "Muscus", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON> mus<PERSON>", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON>us lapillorum muscosorum", "block.minecraft.mossy_cobblestone_stairs": "Scalae lapillorum muscosorum", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> lapideus muscosus", "block.minecraft.mossy_stone_brick_slab": "Gradus laterum saxeorum muscosorum", "block.minecraft.mossy_stone_brick_stairs": "Scalae laterum saxeorum muscosorum", "block.minecraft.mossy_stone_brick_wall": "Murus laterum saxeorum muscosorum", "block.minecraft.mossy_stone_bricks": "Lateres saxei muscosi", "block.minecraft.moving_piston": "Embolus movens", "block.minecraft.mud": "Lutum", "block.minecraft.mud_brick_slab": "Gradus laterum luteorum", "block.minecraft.mud_brick_stairs": "Scalae laterum luteorum", "block.minecraft.mud_brick_wall": "Murus laterum luteorum", "block.minecraft.mud_bricks": "Lateres lutei", "block.minecraft.muddy_mangrove_roots": "Radices manglis luteosae", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.mycelium": "Mycelium", "block.minecraft.nether_brick_fence": "<PERSON><PERSON><PERSON> laterum Nether", "block.minecraft.nether_brick_slab": "Gradus laterum Nether", "block.minecraft.nether_brick_stairs": "Scalae laterum Nether", "block.minecraft.nether_brick_wall": "Murus laterum Nether", "block.minecraft.nether_bricks": "<PERSON><PERSON>", "block.minecraft.nether_gold_ore": "Aes aureum Nether", "block.minecraft.nether_portal": "<PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON> crystalli <PERSON>", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Cubus verrucarum Net<PERSON>", "block.minecraft.netherite_block": "<PERSON><PERSON><PERSON>", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Cubus musicus", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON> querceus", "block.minecraft.oak_door": "Ostium querceum", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON> que<PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON> quercea", "block.minecraft.oak_hanging_sign": "Titulus pendens querceus", "block.minecraft.oak_leaves": "Foliae quercus", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON> quercus", "block.minecraft.oak_planks": "Tabulae querceae", "block.minecraft.oak_pressure_plate": "Tabula premenda quercea", "block.minecraft.oak_sapling": "Arbus<PERSON> quercus", "block.minecraft.oak_sign": "<PERSON>it<PERSON> querceus", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON> querceus", "block.minecraft.oak_stairs": "Scalae querceae", "block.minecraft.oak_trapdoor": "Ostium horizontale querceum", "block.minecraft.oak_wall_hanging_sign": "Titulus pendens muralis querceus", "block.minecraft.oak_wall_sign": "<PERSON>it<PERSON> muralis querceus", "block.minecraft.oak_wood": "Lignum querceum", "block.minecraft.observer": "Spectator", "block.minecraft.obsidian": "<PERSON><PERSON> obsidianus", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON> flavescens", "block.minecraft.ominous_banner": "Vexillum praesagum", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON> apertus", "block.minecraft.orange_banner": "Vexillum aurantium", "block.minecraft.orange_bed": "Lectus aurantius", "block.minecraft.orange_candle": "<PERSON><PERSON> aurantia", "block.minecraft.orange_candle_cake": "Placenta cum candela aurantia", "block.minecraft.orange_carpet": "<PERSON><PERSON> aurantius", "block.minecraft.orange_concrete": "Opus caementicium aurantium", "block.minecraft.orange_concrete_powder": "Caementum aurantium", "block.minecraft.orange_glazed_terracotta": "Terra cocta smaltata aurantia", "block.minecraft.orange_shulker_box": "<PERSON><PERSON> shulker aurantia", "block.minecraft.orange_stained_glass": "Vitrum tinctum aurantium", "block.minecraft.orange_stained_glass_pane": "Fenestra vitrea tincta aurantia", "block.minecraft.orange_terracotta": "Terra cocta aurantia", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> aurantia", "block.minecraft.orange_wool": "<PERSON> auranti<PERSON>", "block.minecraft.oxeye_daisy": "Leucanthemum vulgare", "block.minecraft.oxidized_chiseled_copper": "Cuprum caelatum robiginosum", "block.minecraft.oxidized_copper": "Cuprum robiginosum", "block.minecraft.oxidized_copper_bulb": "Lumen cupreum robiginosum", "block.minecraft.oxidized_copper_door": "Ostium cupreum robiginosum", "block.minecraft.oxidized_copper_grate": "Clathri cuprei robiginosi", "block.minecraft.oxidized_copper_trapdoor": "Ostium horizontale cupreum robiginosum", "block.minecraft.oxidized_cut_copper": "Cuprum scalptum robiginosum", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri <PERSON>ti robiginosus", "block.minecraft.oxidized_cut_copper_stairs": "Scalae cupri <PERSON>ti robigi<PERSON>ae", "block.minecraft.packed_ice": "Glacies stipata", "block.minecraft.packed_mud": "Lutum stipatum", "block.minecraft.pale_hanging_moss": "Muscus pallidus pensilis", "block.minecraft.pale_moss_block": "<PERSON><PERSON><PERSON> musci pallidi", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON> pallidus", "block.minecraft.pale_oak_button": "Premendus quercus pallidae", "block.minecraft.pale_oak_door": "Ostium quercus pallidae", "block.minecraft.pale_oak_fence": "Saepes quercus pallidae", "block.minecraft.pale_oak_fence_gate": "Foris quercus pallidae", "block.minecraft.pale_oak_hanging_sign": "Titulus pendens quercus pallidae", "block.minecraft.pale_oak_leaves": "Foliae quercus pallidae", "block.minecraft.pale_oak_log": "Truncus quercus pallidae", "block.minecraft.pale_oak_planks": "Tabulae quercus pallidae", "block.minecraft.pale_oak_pressure_plate": "Tabula premenda quercus pallidae", "block.minecraft.pale_oak_sapling": "Arbuscula quercus pallidae", "block.minecraft.pale_oak_sign": "Titulus quercus pallidae", "block.minecraft.pale_oak_slab": "<PERSON>radus quercus pallidae", "block.minecraft.pale_oak_stairs": "Scalae quercus pallidae", "block.minecraft.pale_oak_trapdoor": "Ostium horizontale quercus pallidae", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON>itulus pendens muralis quercus pallidae", "block.minecraft.pale_oak_wall_sign": "<PERSON>itulus muralis quercus pallidae", "block.minecraft.pale_oak_wood": "Lignum quercus pallidae", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON> violacea", "block.minecraft.peony": "Paeonia", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON><PERSON> querceus saxeus", "block.minecraft.piglin_head": "<PERSON><PERSON> piglin", "block.minecraft.piglin_wall_head": "<PERSON>ut piglin murale", "block.minecraft.pink_banner": "Vexillum roseum", "block.minecraft.pink_bed": "Lectus roseus", "block.minecraft.pink_candle": "Candela rosea", "block.minecraft.pink_candle_cake": "Placenta cum candela rosea", "block.minecraft.pink_carpet": "Tapes roseus", "block.minecraft.pink_concrete": "Opus caementicium roseum", "block.minecraft.pink_concrete_powder": "Caementum roseum", "block.minecraft.pink_glazed_terracotta": "Terra cocta smaltata rosea", "block.minecraft.pink_petals": "Folia rosea", "block.minecraft.pink_shulker_box": "<PERSON><PERSON> shul<PERSON> rosea", "block.minecraft.pink_stained_glass": "Vitrum tinctum roseum", "block.minecraft.pink_stained_glass_pane": "Fenestra vitrea tincta rosa", "block.minecraft.pink_terracotta": "Terra cocta rosea", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosea", "block.minecraft.pink_wool": "<PERSON> rosea", "block.minecraft.piston": "Embolus", "block.minecraft.piston_head": "Caput emboli", "block.minecraft.pitcher_crop": "Hydria Crop", "block.minecraft.pitcher_plant": "Nepenthes", "block.minecraft.player_head": "<PERSON>ut lusoris", "block.minecraft.player_head.named": "Caput lusoris %s", "block.minecraft.player_wall_head": "Caput lusoris murale", "block.minecraft.podzol": "Subcinerarium", "block.minecraft.pointed_dripstone": "Saxum stillans acuminatum", "block.minecraft.polished_andesite": "Andesites politus", "block.minecraft.polished_andesite_slab": "Gradus andesitae politi", "block.minecraft.polished_andesite_stairs": "Scalae andesiteae politi", "block.minecraft.polished_basalt": "Basal<PERSON> politus", "block.minecraft.polished_blackstone": "Saxum nigrum politum", "block.minecraft.polished_blackstone_brick_slab": "Gradus laterum saxi nigri politi", "block.minecraft.polished_blackstone_brick_stairs": "Scalae laterum saxi nigri politi", "block.minecraft.polished_blackstone_brick_wall": "Murus laterum saxi nigri politi", "block.minecraft.polished_blackstone_bricks": "Lateres saxi nigri politi", "block.minecraft.polished_blackstone_button": "Premendus saxi nigri politi", "block.minecraft.polished_blackstone_pressure_plate": "Tabula premenda saxi nigri politi", "block.minecraft.polished_blackstone_slab": "Gradus saxi nigri politi", "block.minecraft.polished_blackstone_stairs": "Scalae saxi nigri politi", "block.minecraft.polished_blackstone_wall": "Murus saxi nigri politi", "block.minecraft.polished_deepslate": "Lapis sectilis altus politus", "block.minecraft.polished_deepslate_slab": "<PERSON><PERSON><PERSON> lapidis sectilis alti politi", "block.minecraft.polished_deepslate_stairs": "Scalae lapidis sectilis alti politi", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> lapidis sectilis alti politi", "block.minecraft.polished_diorite": "Diorites politus", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON><PERSON> dioriteus politus", "block.minecraft.polished_diorite_stairs": "Scalae dioriteae politae", "block.minecraft.polished_granite": "Granitum politum", "block.minecraft.polished_granite_slab": "<PERSON><PERSON><PERSON> graniteus politus", "block.minecraft.polished_granite_stairs": "Scalae graniteae politae", "block.minecraft.polished_tuff": "<PERSON><PERSON> politus", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON><PERSON> tophi politi", "block.minecraft.polished_tuff_stairs": "Scalae tophi politi", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON> tophi politi", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "Patatae", "block.minecraft.potted_acacia_sapling": "Arbus<PERSON> acaciae inserta", "block.minecraft.potted_allium": "Allium in vase", "block.minecraft.potted_azalea_bush": "Rhododendron in vase", "block.minecraft.potted_azure_bluet": "Houstonia caerulea in vase", "block.minecraft.potted_bamboo": "<PERSON><PERSON><PERSON> in vase", "block.minecraft.potted_birch_sapling": "Arbuscula betullae in vase", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON> coerulea in vase", "block.minecraft.potted_brown_mushroom": "Fungus brunus in vase", "block.minecraft.potted_cactus": "Cactus in vase", "block.minecraft.potted_cherry_sapling": "A<PERSON><PERSON><PERSON> cerasi in vase", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON><PERSON> clausus in vase", "block.minecraft.potted_cornflower": "<PERSON><PERSON> in vase", "block.minecraft.potted_crimson_fungus": "Fungus coccinus in vase", "block.minecraft.potted_crimson_roots": "Radices coccinae in vase", "block.minecraft.potted_dandelion": "Taraxacum in vase", "block.minecraft.potted_dark_oak_sapling": "Arbuscula quercus nigrae in vase", "block.minecraft.potted_dead_bush": "Frutex mortuus in vase", "block.minecraft.potted_fern": "Filix in vase", "block.minecraft.potted_flowering_azalea_bush": "Rhododendron floridum in vase", "block.minecraft.potted_jungle_sapling": "Arbuscula theobromatis in vase", "block.minecraft.potted_lily_of_the_valley": "Convallaria in vase", "block.minecraft.potted_mangrove_propagule": "Semen manglis in vase", "block.minecraft.potted_oak_sapling": "Arbuscula quercus in vase", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON><PERSON> apertus in vase", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON><PERSON> aurantia in vase", "block.minecraft.potted_oxeye_daisy": "Leucanthemum vulgare in vase", "block.minecraft.potted_pale_oak_sapling": "Arbuscula quercus pallidae in vase", "block.minecraft.potted_pink_tulip": "<PERSON>lipa rosea in vase", "block.minecraft.potted_poppy": "Papaver in vase", "block.minecraft.potted_red_mushroom": "Fungus ruber in vase", "block.minecraft.potted_red_tulip": "Tulipa rubra in vase", "block.minecraft.potted_spruce_sapling": "Arbuscula piceae in vase", "block.minecraft.potted_torchflower": "Facularia in vase", "block.minecraft.potted_warped_fungus": "Fungus distortus in vase", "block.minecraft.potted_warped_roots": "Radices distortae insertae", "block.minecraft.potted_white_tulip": "<PERSON><PERSON><PERSON> alba in vase", "block.minecraft.potted_wither_rose": "<PERSON> wither in vase", "block.minecraft.powder_snow": "Nix pulverea", "block.minecraft.powder_snow_cauldron": "Cortina cum nivi pulverea", "block.minecraft.powered_rail": "Ferrivia electrica", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "G<PERSON>us laterum prismarinorum", "block.minecraft.prismarine_brick_stairs": "G<PERSON>us laterum prismarinorum", "block.minecraft.prismarine_bricks": "<PERSON><PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON> prismarinus", "block.minecraft.prismarine_stairs": "Scalae prismarinae", "block.minecraft.prismarine_wall": "<PERSON><PERSON> prismarinus", "block.minecraft.pumpkin": "Cucurbita", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON> cucurbitae", "block.minecraft.purple_banner": "Vexillum purpureum", "block.minecraft.purple_bed": "Lectus purpureus", "block.minecraft.purple_candle": "Candela purpurea", "block.minecraft.purple_candle_cake": "Placenta cum candela purpurea", "block.minecraft.purple_carpet": "<PERSON><PERSON> purpureus", "block.minecraft.purple_concrete": "Opus caementicium purpureum", "block.minecraft.purple_concrete_powder": "Caementum purpureum", "block.minecraft.purple_glazed_terracotta": "Terra cocta smaltata purpurea", "block.minecraft.purple_shulker_box": "<PERSON><PERSON> shulker purpurea", "block.minecraft.purple_stained_glass": "Vitrum tinctum purpureum", "block.minecraft.purple_stained_glass_pane": "Fenestra vitrea tincta purpurea", "block.minecraft.purple_terracotta": "Terra cocta purpurea", "block.minecraft.purple_wool": "<PERSON> purpurea", "block.minecraft.purpur_block": "<PERSON><PERSON><PERSON> purpur", "block.minecraft.purpur_pillar": "<PERSON>umna purpur", "block.minecraft.purpur_slab": "<PERSON><PERSON><PERSON> purpur", "block.minecraft.purpur_stairs": "<PERSON>alae purpur", "block.minecraft.quartz_block": "Cubus crystallinum", "block.minecraft.quartz_bricks": "Lateres crystallini", "block.minecraft.quartz_pillar": "Columna crystallina", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON> crystallinus", "block.minecraft.quartz_stairs": "Scalae crystallinae", "block.minecraft.rail": "Ferrivia", "block.minecraft.raw_copper_block": "Cubus cupri infecti", "block.minecraft.raw_gold_block": "Cubus auri infecti", "block.minecraft.raw_iron_block": "Cubus ferri infecti", "block.minecraft.red_banner": "Vexillum rubrum", "block.minecraft.red_bed": "Lectus ruber", "block.minecraft.red_candle": "Candela rubra", "block.minecraft.red_candle_cake": "Placenta cum candela rubra", "block.minecraft.red_carpet": "<PERSON><PERSON> ruber", "block.minecraft.red_concrete": "Opus caementicium rubrum", "block.minecraft.red_concrete_powder": "Caementum rubrum", "block.minecraft.red_glazed_terracotta": "Terra cocta smaltata rubra", "block.minecraft.red_mushroom": "Fungus ruber", "block.minecraft.red_mushroom_block": "Cubus fungi rubri", "block.minecraft.red_nether_brick_slab": "Gradus laterum Nether rubrorum", "block.minecraft.red_nether_brick_stairs": "Scalae laterum Nether rubrorum", "block.minecraft.red_nether_brick_wall": "Murus laterum Nether rubrorum", "block.minecraft.red_nether_bricks": "Lateres Nether rubri", "block.minecraft.red_sand": "<PERSON><PERSON> rubra", "block.minecraft.red_sandstone": "<PERSON><PERSON> harenarius ruber", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON><PERSON> lapidis harenarii rubri", "block.minecraft.red_sandstone_stairs": "Scalae lapidis harenarii rubri", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> lapidis harenarii rubri", "block.minecraft.red_shulker_box": "<PERSON><PERSON> shulker rubra", "block.minecraft.red_stained_glass": "Vitrum tinctum rubrum", "block.minecraft.red_stained_glass_pane": "Fenestra vitrea tincta rubra", "block.minecraft.red_terracotta": "Terra cocta rubra", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> rubra", "block.minecraft.red_wool": "<PERSON> rubra", "block.minecraft.redstone_block": "Cubus redstone", "block.minecraft.redstone_lamp": "Lampas redstone", "block.minecraft.redstone_ore": "<PERSON>es redstone", "block.minecraft.redstone_torch": "Fax redstone", "block.minecraft.redstone_wall_torch": "Fax muralis redstone", "block.minecraft.redstone_wire": "Funis redstone", "block.minecraft.reinforced_deepslate": "Lapis sectilis altus firmior", "block.minecraft.repeater": "Iteratrum redstone", "block.minecraft.repeating_command_block": "<PERSON><PERSON><PERSON> i<PERSON>orum iterans", "block.minecraft.resin_block": "<PERSON><PERSON><PERSON> resinae", "block.minecraft.resin_brick_slab": "Gradus laterum resinae", "block.minecraft.resin_brick_stairs": "Scalae laterum resinae", "block.minecraft.resin_brick_wall": "Murus laterum resinae", "block.minecraft.resin_bricks": "<PERSON><PERSON> resinae", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> resinae", "block.minecraft.respawn_anchor": "Ancora renas<PERSON>i", "block.minecraft.rooted_dirt": "Humus cum radicibus", "block.minecraft.rose_bush": "Frutex rosarum", "block.minecraft.sand": "<PERSON><PERSON>", "block.minecraft.sandstone": "<PERSON><PERSON> harenarius", "block.minecraft.sandstone_slab": "<PERSON><PERSON><PERSON> lapidis harena<PERSON>i", "block.minecraft.sandstone_stairs": "<PERSON>alae lapidis harena<PERSON>i", "block.minecraft.sandstone_wall": "<PERSON><PERSON> lapidis harena<PERSON>i", "block.minecraft.scaffolding": "Fulcimen", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk mutans", "block.minecraft.sculk_sensor": "Sculk sentiens", "block.minecraft.sculk_shrieker": "Sculk clamans", "block.minecraft.sculk_vein": "<PERSON><PERSON> sculk", "block.minecraft.sea_lantern": "Lanterna marina", "block.minecraft.sea_pickle": "Pyrosoma", "block.minecraft.seagrass": "Gramen marinum", "block.minecraft.set_spawn": "Hic locus renascendi tuus factus est", "block.minecraft.short_dry_grass": "Gramen aridum breve", "block.minecraft.short_grass": "Gramen breve", "block.minecraft.shroomlight": "Lucifungus", "block.minecraft.shulker_box": "<PERSON><PERSON> s<PERSON>", "block.minecraft.skeleton_skull": "<PERSON>va sceleti", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON> sceleti muralis", "block.minecraft.slime_block": "<PERSON><PERSON><PERSON> muci", "block.minecraft.small_amethyst_bud": "Gemma amethystina nova", "block.minecraft.small_dripleaf": "Folia inclinabilis parva", "block.minecraft.smithing_table": "Mensa fabricae", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Basaltes glabrus", "block.minecraft.smooth_quartz": "Cubus crystalli glabri", "block.minecraft.smooth_quartz_slab": "Gradus crystalli glabri", "block.minecraft.smooth_quartz_stairs": "Gradus crystalli glabri", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> harenarius ruber glaber", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON><PERSON> lapidis harenarii rubri glabri", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON><PERSON> lapidis harenarii rubri glabri", "block.minecraft.smooth_sandstone": "<PERSON><PERSON> harenarius glaber", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON> lapidis harenarii glabri", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON><PERSON> lapidis harenarii glabri", "block.minecraft.smooth_stone": "Saxum glabrum", "block.minecraft.smooth_stone_slab": "Gradus saxi glabri", "block.minecraft.sniffer_egg": "Ovum odorisequi", "block.minecraft.snow": "<PERSON>", "block.minecraft.snow_block": "<PERSON>ub<PERSON> niveus", "block.minecraft.soul_campfire": "Focus animarum", "block.minecraft.soul_fire": "Ignis animarum", "block.minecraft.soul_lantern": "Lanterna animarum", "block.minecraft.soul_sand": "<PERSON><PERSON> animarum", "block.minecraft.soul_soil": "<PERSON>mus animarum", "block.minecraft.soul_torch": "Fax animarum", "block.minecraft.soul_wall_torch": "Fax muralis animarum", "block.minecraft.spawn.not_valid": "Lectus vel ancora renascendi firma tibi non erant vel tui obstructi sunt", "block.minecraft.spawner": "Creator monstrorum", "block.minecraft.spawner.desc1": "Usus ovo creationis:", "block.minecraft.spawner.desc2": "Genus mobilis ponit", "block.minecraft.sponge": "Spongia", "block.minecraft.spore_blossom": "<PERSON>los sporas spargens", "block.minecraft.spruce_button": "Premendus piceae", "block.minecraft.spruce_door": "Ostium piceae", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON> p<PERSON>ae", "block.minecraft.spruce_fence_gate": "<PERSON>is piceae", "block.minecraft.spruce_hanging_sign": "Titulus pendens piceae", "block.minecraft.spruce_leaves": "Foliae piceae", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON> piceae", "block.minecraft.spruce_planks": "Tabulae piceae", "block.minecraft.spruce_pressure_plate": "Tabula premenda piceae", "block.minecraft.spruce_sapling": "A<PERSON><PERSON><PERSON> piceae", "block.minecraft.spruce_sign": "<PERSON>itulus piceae", "block.minecraft.spruce_slab": "<PERSON><PERSON><PERSON> p<PERSON>ae", "block.minecraft.spruce_stairs": "Scalae piceae", "block.minecraft.spruce_trapdoor": "Ostium horizontale piceae", "block.minecraft.spruce_wall_hanging_sign": "Titulus pendens muralis piceae", "block.minecraft.spruce_wall_sign": "<PERSON>itulus muralis piceae", "block.minecraft.spruce_wood": "Lignum piceae", "block.minecraft.sticky_piston": "Embolus tenax", "block.minecraft.stone": "Saxum", "block.minecraft.stone_brick_slab": "Gradus laterum saxeorum", "block.minecraft.stone_brick_stairs": "Scalae laterum saxeorum", "block.minecraft.stone_brick_wall": "Murus laterum saxeorum", "block.minecraft.stone_bricks": "<PERSON><PERSON> saxei", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON> saxeus", "block.minecraft.stone_pressure_plate": "Tabula premenda saxea", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON> saxeus", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON> saxeae", "block.minecraft.stonecutter": "Saxumsecator", "block.minecraft.stripped_acacia_log": "Truncus acaciae decorticatus", "block.minecraft.stripped_acacia_wood": "Lignum acaciae decorticatum", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON> harundinum Indicarum decorticatarum", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON> betullae decorticatus", "block.minecraft.stripped_birch_wood": "Lignum betullae decorticatum", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON> cerasi decorticatus", "block.minecraft.stripped_cherry_wood": "Lignum cerasi decorticatum", "block.minecraft.stripped_crimson_hyphae": "Hyphae coccinae decorticatae", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON> coccinus decorticatus", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON> quercus nigrae decorticatus", "block.minecraft.stripped_dark_oak_wood": "Lignum quercus nigrae decorticatum", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON> theobromatis decorticatus", "block.minecraft.stripped_jungle_wood": "Lignum theobromatis decorticatum", "block.minecraft.stripped_mangrove_log": "<PERSON>run<PERSON> manglis decorticatus", "block.minecraft.stripped_mangrove_wood": "Lignum manglis decorticatum", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON> quercus decorticatus", "block.minecraft.stripped_oak_wood": "Lignum querceum decorticatum", "block.minecraft.stripped_pale_oak_log": "Truncus quercus pallidae decorticatus", "block.minecraft.stripped_pale_oak_wood": "Lignum quercus pallidae decorticatum", "block.minecraft.stripped_spruce_log": "T<PERSON><PERSON> piceae decorticatus", "block.minecraft.stripped_spruce_wood": "Lignum piceae decorticatum", "block.minecraft.stripped_warped_hyphae": "Hyphae distortae decorticatae", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON> distortus decorticatus", "block.minecraft.structure_block": "<PERSON><PERSON><PERSON> a<PERSON>i", "block.minecraft.structure_void": "Concavum aedifici", "block.minecraft.sugar_cane": "Canna sacchari", "block.minecraft.sunflower": "Helianthus", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON>a", "block.minecraft.suspicious_sand": "Arena suspecta", "block.minecraft.sweet_berry_bush": "Frutex bacarum dulcium", "block.minecraft.tall_dry_grass": "Gramen aridum procerum", "block.minecraft.tall_grass": "Gramen altum", "block.minecraft.tall_seagrass": "Gramen marinum altum", "block.minecraft.target": "Palus", "block.minecraft.terracotta": "Terra cocta", "block.minecraft.test_block": "<PERSON><PERSON><PERSON> probationis", "block.minecraft.test_instance_block": "<PERSON><PERSON><PERSON> unius probationis", "block.minecraft.tinted_glass": "Vitrum fuscum", "block.minecraft.tnt": "Dynamites", "block.minecraft.tnt.disabled": "Displosiones dynamitis debilitantur", "block.minecraft.torch": "Fax", "block.minecraft.torchflower": "Facularia", "block.minecraft.torchflower_crop": "Planta faculariae", "block.minecraft.trapped_chest": "Cista dolosa", "block.minecraft.trial_spawner": "Creator periculorum", "block.minecraft.tripwire": "Laqueus", "block.minecraft.tripwire_hook": "<PERSON><PERSON> laquei", "block.minecraft.tube_coral": "Corallium tubulatum", "block.minecraft.tube_coral_block": "<PERSON><PERSON><PERSON> corallii tubulati", "block.minecraft.tube_coral_fan": "<PERSON><PERSON><PERSON> tubulata", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON> tubulata horizontalis", "block.minecraft.tuff": "Tophus", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON>us laterum tophi", "block.minecraft.tuff_brick_stairs": "Scalae laterum tophi", "block.minecraft.tuff_brick_wall": "Murus laterum tophi", "block.minecraft.tuff_bricks": "<PERSON>es tophi", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON> tophi", "block.minecraft.tuff_wall": "<PERSON><PERSON> tophi", "block.minecraft.turtle_egg": "Ovum testudinis", "block.minecraft.twisting_vines": "Vineae contortae", "block.minecraft.twisting_vines_plant": "Stirps vinearum contortarum", "block.minecraft.vault": "Arca", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON> virescens", "block.minecraft.vine": "Vineae", "block.minecraft.void_air": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.wall_torch": "Fax muralis", "block.minecraft.warped_button": "Premendus distortus", "block.minecraft.warped_door": "Ostium distortum", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON> distorta", "block.minecraft.warped_fence_gate": "Foris distorta", "block.minecraft.warped_fungus": "Fungus distortus", "block.minecraft.warped_hanging_sign": "Titulus pendens distortus", "block.minecraft.warped_hyphae": "Hyphae distortae", "block.minecraft.warped_nylium": "Infelium distortum", "block.minecraft.warped_planks": "Tabulae distortae", "block.minecraft.warped_pressure_plate": "Tabula premenda distorta", "block.minecraft.warped_roots": "Radices distortae", "block.minecraft.warped_sign": "<PERSON>itulus distortus", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON> distortus", "block.minecraft.warped_stairs": "Scalae distortae", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON> distortus", "block.minecraft.warped_trapdoor": "Ostium horizontale distortum", "block.minecraft.warped_wall_hanging_sign": "Titulus pendens muralis distortus", "block.minecraft.warped_wall_sign": "<PERSON>itulus muralis distortus", "block.minecraft.warped_wart_block": "Cubus verrucarum distortarum", "block.minecraft.water": "Aqua", "block.minecraft.water_cauldron": "Cortina cum aqua", "block.minecraft.waxed_chiseled_copper": "Cuprum caelatum attritum", "block.minecraft.waxed_copper_block": "<PERSON>ubus cupreus ceratus", "block.minecraft.waxed_copper_bulb": "Lumen cupreum ceratum", "block.minecraft.waxed_copper_door": "Ostium cupreum ceratum", "block.minecraft.waxed_copper_grate": "Clathri cuprei cerati", "block.minecraft.waxed_copper_trapdoor": "Ostium horizontale cupreum ceratum", "block.minecraft.waxed_cut_copper": "Cuprum scalptum ceratum", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri <PERSON>ti ceratus", "block.minecraft.waxed_cut_copper_stairs": "Scalae cupri scalpti ceratae", "block.minecraft.waxed_exposed_chiseled_copper": "Cuprum caelatum obvium ceratum", "block.minecraft.waxed_exposed_copper": "Cuprum obvium ceratum", "block.minecraft.waxed_exposed_copper_bulb": "Lumen cupreum obvium ceratum", "block.minecraft.waxed_exposed_copper_door": "Ostium cupreum obvium ceratum", "block.minecraft.waxed_exposed_copper_grate": "Clathri cuprei obvii cerati", "block.minecraft.waxed_exposed_copper_trapdoor": "Ostium horizontale cupreum obvium ceratum", "block.minecraft.waxed_exposed_cut_copper": "Cuprum scalptum obvium ceratum", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri <PERSON>ti obvius ceratus", "block.minecraft.waxed_exposed_cut_copper_stairs": "Scalae cupri scalpti obviae ceratae", "block.minecraft.waxed_oxidized_chiseled_copper": "Cupreum caelatum robiginosum ceratum", "block.minecraft.waxed_oxidized_copper": "Cuprum robiginosum ceratum", "block.minecraft.waxed_oxidized_copper_bulb": "Lumen cupreum robiginosum ceratum", "block.minecraft.waxed_oxidized_copper_door": "Ostium cupreum robiginosum ceratum", "block.minecraft.waxed_oxidized_copper_grate": "Clathri cuprei robiginosi cerati", "block.minecraft.waxed_oxidized_copper_trapdoor": "Ostium horizontale cupreum robiginosum ceratum", "block.minecraft.waxed_oxidized_cut_copper": "Cuprum scalptum robiginosum ceratum", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri <PERSON>ti robiginosus ceratus", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Scalae cupri scalpti robiginosae ceratae", "block.minecraft.waxed_weathered_chiseled_copper": "Cupreum caelatum attritum ceratum", "block.minecraft.waxed_weathered_copper": "Cuprum attritum ceratum", "block.minecraft.waxed_weathered_copper_bulb": "Lumen cupreum attritum ceratum", "block.minecraft.waxed_weathered_copper_door": "Ostium cupreum attritum ceratum", "block.minecraft.waxed_weathered_copper_grate": "Clathri cuprei attriti cerati", "block.minecraft.waxed_weathered_copper_trapdoor": "Ostium horizontale cupreum attritum ceratum", "block.minecraft.waxed_weathered_cut_copper": "Cuprum scalptum attritum ceratum", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri scalpti attritus ceratus", "block.minecraft.waxed_weathered_cut_copper_stairs": "Scalae cupri scalpti attritae ceratae", "block.minecraft.weathered_chiseled_copper": "Cuprum caelatum attritum", "block.minecraft.weathered_copper": "Cuprum attritum", "block.minecraft.weathered_copper_bulb": "Lumen cupreum attritum", "block.minecraft.weathered_copper_door": "Ostium cupreum attritum", "block.minecraft.weathered_copper_grate": "Clathri cuprei attriti", "block.minecraft.weathered_copper_trapdoor": "Ostium horizontale cupreum attritum", "block.minecraft.weathered_cut_copper": "Cuprum scalptum attritum", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> cupri scalpti attritus", "block.minecraft.weathered_cut_copper_stairs": "Scalae cupri scalpti attritae", "block.minecraft.weeping_vines": "Vineae lacrimosae", "block.minecraft.weeping_vines_plant": "Stirps vinearum lacrimosarum", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON><PERSON> madida", "block.minecraft.wheat": "<PERSON><PERSON> frumenti", "block.minecraft.white_banner": "Vexillum album", "block.minecraft.white_bed": "Lectus albus", "block.minecraft.white_candle": "Candela alba", "block.minecraft.white_candle_cake": "Placenta cum candela alba", "block.minecraft.white_carpet": "Tapes albus", "block.minecraft.white_concrete": "Opus caementicium album", "block.minecraft.white_concrete_powder": "Caementum album", "block.minecraft.white_glazed_terracotta": "Terra cocta smaltata alba", "block.minecraft.white_shulker_box": "Capsa shulker alba", "block.minecraft.white_stained_glass": "Vitrum tinctum album", "block.minecraft.white_stained_glass_pane": "Fenestra vitrea tincta alba", "block.minecraft.white_terracotta": "Terra cocta alba", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> alba", "block.minecraft.white_wool": "Lana alba", "block.minecraft.wildflowers": "<PERSON> silvestrum", "block.minecraft.wither_rose": "<PERSON> wither", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON> sceleti wither", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON> sceleti wither muralis", "block.minecraft.yellow_banner": "Vexillum flauum", "block.minecraft.yellow_bed": "Lectus flavus", "block.minecraft.yellow_candle": "Candela flava", "block.minecraft.yellow_candle_cake": "Placenta cum candela flava", "block.minecraft.yellow_carpet": "Tapes flavus", "block.minecraft.yellow_concrete": "Opus caementicium flavum", "block.minecraft.yellow_concrete_powder": "Caementum flavum", "block.minecraft.yellow_glazed_terracotta": "Terra cocta smaltata flava", "block.minecraft.yellow_shulker_box": "Capsa shulker flava", "block.minecraft.yellow_stained_glass": "Vitrum tinctum flavum", "block.minecraft.yellow_stained_glass_pane": "Fenestra vitrea tincta flava", "block.minecraft.yellow_terracotta": "Terra cocta flava", "block.minecraft.yellow_wool": "Lana flava", "block.minecraft.zombie_head": "<PERSON>ut resurrecti", "block.minecraft.zombie_wall_head": "Caput ressurrecti murale", "book.byAuthor": "ab %1$s", "book.edit.title": "Simulacrum mutationem libri", "book.editTitle": "Inscribe librum:", "book.finalizeButton": "Subscribe et claude", "book.finalizeWarning": "Nota bene! Liber subscriptus mutari non poterit.", "book.generation.0": "Originalis", "book.generation.1": "Exemplar originalis", "book.generation.2": "Exemplar exemplaris", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Nota libri irrita *", "book.pageIndicator": "Pagina %1$s ex %2$s", "book.page_button.next": "Pagina proxima", "book.page_button.previous": "<PERSON><PERSON><PERSON> prior", "book.sign.title": "Simulacrum subscriptionem libri", "book.sign.titlebox": "Titulus", "book.signButton": "Subscribe", "book.view.title": "Simulacrum conspectus libri", "build.tooHigh": "Limes altitudinis aedificationis %s est", "chat.cannotSend": "Mittere nuntium ad locutorium non potes", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Ice ut te teletransportes", "chat.copy": "Copia ad latibulum", "chat.copy.click": "Ice ut copies ad latibulum", "chat.deleted_marker": "Hoc nuntium moderatro deletum est.", "chat.disabled.chain_broken": "Locutorium uti non potes quia catena nuntiorum interrupta est. Moderatro iterum conecti conare.", "chat.disabled.expiredProfileKey": "Locutorium uti non potes quia clavis publica personae nimis vetus est. Moderatro iterum conecti conare.", "chat.disabled.invalid_command_signature": "<PERSON><PERSON>o signaturae argumenti inopinatae vel absentes erant.", "chat.disabled.invalid_signature": "Locutorium signaturam irritam erat. Moderatro iterum conecti conare.", "chat.disabled.launcher": "Locutorium optionibus deductoris inactivum est. Nuntium mitti non potest.", "chat.disabled.missingProfileKey": "Locutorium uti non potes quia clavis publica personae deest. Moderatro iterum conecti conare.", "chat.disabled.options": "Locutorium inactivum optionibus clientis factum est.", "chat.disabled.out_of_order_chat": "Locutorium inordinate receptum est. Tempusne systematis tui mutatum est?", "chat.disabled.profile": "Locutorium optionibus licentiae non permisit. Preme '%s' iterum ad plus informationum.", "chat.disabled.profile.moreInfo": "Locutorium optionibus licentiae non permisit. Nuntios mitti vel videri non potest.", "chat.editBox": "locutorium", "chat.filtered": "Hoc nuntium moderatro celatum est.", "chat.filtered_full": "Moderatrum nuntium tuum aliquot lusores celavit.", "chat.link.confirm": "Visne vero hanc paginam interretialem aperire?", "chat.link.confirmTrusted": "Visne hoc ligamen aperire vel id ad latibulum copiare?", "chat.link.open": "Aperi in navigatro", "chat.link.warning": "Numquam ligamina ab infidis aperi!", "chat.queue": "[+%s nuntii accepturi]", "chat.square_brackets": "[%s]", "chat.tag.error": "Moderatrum nuntium irritum misit.", "chat.tag.modified": "Nuntium moderatro mutarum est. Hoc nuntium missum est:", "chat.tag.not_secure": "Nuntium referri non potest: nam nescimus sitne mutatum.", "chat.tag.system": "Nuntium referri non potest: nam moderatro missum est.", "chat.tag.system_single_player": "Nuntium moderatri.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s arduum %s peregit", "chat.type.advancement.goal": "%s finem %s consecutus est", "chat.type.advancement.task": "%s incrementum %s fecit", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Mitte nuntium ad factionem", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s inquit %s", "chat.validation_error": "Error <PERSON>is lo<PERSON>orii", "chat_screen.message": "Nuntium mittendum: %s", "chat_screen.title": "Simulacrum lo<PERSON>orii", "chat_screen.usage": "Scribe nuntium et preme Mitte ut mittas", "chunk.toast.checkLog": "Refer tabulas ut videas plus rationum", "chunk.toast.loadFailure": "Partem in %s imponere non potuit", "chunk.toast.lowDiskSpace": "Spatium in disco tuo macrius est!", "chunk.toast.lowDiskSpace.description": "Servare mundum fortasse non potero.", "chunk.toast.saveFailure": "Partem in %s servare non potuit", "clear.failed.multiple": "Res nullae in lusoribus %s inventae sunt", "clear.failed.single": "Res nullae in lusore %s inventae sunt", "color.minecraft.black": "Niger", "color.minecraft.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON>", "color.minecraft.lime": "<PERSON><PERSON><PERSON>", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON><PERSON>", "color.minecraft.purple": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.red": "R<PERSON><PERSON>", "color.minecraft.white": "Albus", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[HIC]", "command.context.parse_error": "%s in positione %s: %s", "command.exception": "Hoc iussum interpretari non potui: %s", "command.expected.separator": "Spatium album in fine argumenti expectabatur, sed data ulteriora inventa sunt", "command.failed": "Error inexspectatus evenit cum hoc iussum efficere conarer", "command.forkLimit": "Numerus maximus (%s) contextuum adventus est", "command.unknown.argument": "Argumentum iussi irritum est", "command.unknown.command": "Iussum ignotum vel incompletum est, vide infra pro errore", "commands.advancement.criterionNotFound": "Incrementum %1$s non concludit criterion '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Criterion '%s' incrementi %s ad %s lusores donare non potuit quod id iam habent", "commands.advancement.grant.criterion.to.many.success": "Criterion '%s' incrementi %s ad %s lusores donavit", "commands.advancement.grant.criterion.to.one.failure": "Criterion '%s' incrementi %s ad %s donare non potuit quod id iam habent", "commands.advancement.grant.criterion.to.one.success": "Criterion '%s' incrementi %s ad %s donavit", "commands.advancement.grant.many.to.many.failure": "%s incrementa ad %s lusores donare non potuit quod ea iam habent", "commands.advancement.grant.many.to.many.success": "Incrementa %s lusoribus %s tributa sunt", "commands.advancement.grant.many.to.one.failure": "Incrementa %s lusori %s tribuere non potui quod ille ea iamiam habet", "commands.advancement.grant.many.to.one.success": "%s incrementa lusori %s tributa sunt", "commands.advancement.grant.one.to.many.failure": "Incrementum %s lusoribus %s tribuere non potui quod illi id iamiam habent", "commands.advancement.grant.one.to.many.success": "Incrementum %s lusoribus %s tributum est", "commands.advancement.grant.one.to.one.failure": "Incrementum %s lusori %s tribuere non potui quod ille id iamiam habet", "commands.advancement.grant.one.to.one.success": "Incrementum %s lusori %s tributum est", "commands.advancement.revoke.criterion.to.many.failure": "Criterion '%s' incrementi %s ex %s lusoribus renuntiare non potuit quod id non habent", "commands.advancement.revoke.criterion.to.many.success": "Criterion '%s' incrementi %s ex %s lusoribus renuntiavit", "commands.advancement.revoke.criterion.to.one.failure": "Criterion '%s' incrementi %s ex %s renuntiare non potuit quod id non habent", "commands.advancement.revoke.criterion.to.one.success": "Criterion '%s' incrementi %s ex %s renuntiavit", "commands.advancement.revoke.many.to.many.failure": "%s incrementa ex %s lusoribus renuntiare non potuit quod ea non habent", "commands.advancement.revoke.many.to.many.success": "%s incrementa ex %s lusoribus renuntiavit", "commands.advancement.revoke.many.to.one.failure": "%s incrementa ex %s renuntiare non potuit quod ea non habent", "commands.advancement.revoke.many.to.one.success": "%s incrementa ex %s renuntiavit", "commands.advancement.revoke.one.to.many.failure": "Incrementum %s ex %s lusoribus renuntiare non potuit quod id non habent", "commands.advancement.revoke.one.to.many.success": "Incrementum %s ex %s lusoribus renuntiavit", "commands.advancement.revoke.one.to.one.failure": "Incrementum %s ex %s renuntiare non potuit quod id non habent", "commands.advancement.revoke.one.to.one.success": "Incrementum %s ex %s renuntiavit", "commands.attribute.base_value.get.success": "Valor basis propri %s entitatis %s est %s", "commands.attribute.base_value.reset.success": "Valor basis propri %s entitatis %s reinitiatus est ad %s", "commands.attribute.base_value.set.success": "Valor basis propri %s entitatis %s mutatur ad %s", "commands.attribute.failed.entity": "%s huic iusso entitas irrita est", "commands.attribute.failed.modifier_already_present": "Mutator %s iam proprio %s entitatis %s est", "commands.attribute.failed.no_attribute": "Entitae %s proprium %s non est", "commands.attribute.failed.no_modifier": "Proprio %s entitatis %s mutator %s non est", "commands.attribute.modifier.add.success": "Additur mutator %s proprio %s entitatis %s", "commands.attribute.modifier.remove.success": "Removetur mutator %s a proprio %s entitatis %s", "commands.attribute.modifier.value.get.success": "Valor mutatoris %s propri %s entitatis %s est %s", "commands.attribute.value.get.success": "Valor propri %s entitatis %s est %s", "commands.ban.failed": "Nihil mutatum est. Lusor iamiam vetitus est", "commands.ban.success": "Vetitus %s: %s", "commands.banip.failed": "Nihil mutatum est. FI iamiam vetita est", "commands.banip.info": "Hoc interdictum %s lusores afficit: %s", "commands.banip.invalid": "FI irrita vel lusor ignotus", "commands.banip.success": "FI %s vetita: %s", "commands.banlist.entry": "%s ab %s vetitus(a) est: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON> ignotus)", "commands.banlist.list": "%s interdicta sunt:", "commands.banlist.none": "Nullae vetitiones sunt", "commands.bossbar.create.failed": "Cuius ID est '%s' asser iamiam exstat", "commands.bossbar.create.success": "Asser %s creatur", "commands.bossbar.get.max": "Maximus valor asseris %s est %s", "commands.bossbar.get.players.none": "Asseri %s ad hoc nullus lusor in linea est", "commands.bossbar.get.players.some": "Asser consuetudinis %s %s lusores qui adsunt habet: %s", "commands.bossbar.get.value": "Valor asseris %s est %s", "commands.bossbar.get.visible.hidden": "Asser %s celatur", "commands.bossbar.get.visible.visible": "Asser %s ad hoc visibilis est", "commands.bossbar.list.bars.none": "<PERSON>ulli asseres activi sunt", "commands.bossbar.list.bars.some": "%s asseres consuetudinis activi sunt: %s", "commands.bossbar.remove.success": "Asser %s removetur", "commands.bossbar.set.color.success": "Asseris %s color demutatur", "commands.bossbar.set.color.unchanged": "Nihil mutatum est. Ille iamiam color huius asseris est", "commands.bossbar.set.max.success": "Asseris %s valor maximus ad %s demutatur", "commands.bossbar.set.max.unchanged": "Nihil mutatum est. Ille iamiam valor maximus huius asseris est", "commands.bossbar.set.name.success": "Asseris %s nomen demutatur", "commands.bossbar.set.name.unchanged": "Nihil mutatum est. Illoc iamiam nomen huius asseris est", "commands.bossbar.set.players.success.none": "Asser %s nullo lusore est", "commands.bossbar.set.players.success.some": "Asser consuetudinis %s nunc %s lusores habet: %s", "commands.bossbar.set.players.unchanged": "Nihil mutatus est. Illis lusoribus iamiam est et nemo ad asserem addendus vel ab assere removendus est", "commands.bossbar.set.style.success": "Asseris %s stylum demutatur", "commands.bossbar.set.style.unchanged": "Nihil mutatum est. Illoc iamiam stylum huius asseris est", "commands.bossbar.set.value.success": "Asseris %s, valor ad %s demutatur", "commands.bossbar.set.value.unchanged": "Nihil mutatum est. Illoc iamiam valor huius asseris est", "commands.bossbar.set.visibility.unchanged.hidden": "Nihil mutatum est. Asser iamiam celabatur", "commands.bossbar.set.visibility.unchanged.visible": "Nihil mutatur est. Asser iamiam visibilis est", "commands.bossbar.set.visible.success.hidden": "Asser %s nunc celatur", "commands.bossbar.set.visible.success.visible": "Asser %s nunc visibilis est", "commands.bossbar.unknown": "Cuius ID est '%s' nullus asser exstat", "commands.clear.success.multiple": "%s res remotae sunt a %s lusoribus", "commands.clear.success.single": "%s res remotae sunt a lusore %s", "commands.clear.test.multiple": "%s convenientes res inventae sunt apud %s lusores", "commands.clear.test.single": "%s convenientes res inventae sunt apud lusorem %s", "commands.clone.failed": "Cubi nulli duplicati sunt", "commands.clone.overlap": "Origo et destinatio non interpenetrare possunt", "commands.clone.success": "Prospere %s cubos duplicavit", "commands.clone.toobig": "Nimii cubi in determinatio area sunt (maximus %s, cetra %s)", "commands.damage.invulnerable": "Selectus est inviolabilis ad genus damni datum", "commands.damage.success": "%s damnum ad %s aptatus est", "commands.data.block.get": "%s in cubo %s, %s, %s, post numerus multiplicans %s datus, est %s", "commands.data.block.invalid": "Cubo destinato data non sunt", "commands.data.block.modified": "Data cubi in %s, %s, %s mutantur", "commands.data.block.query": "Cubo in %s, %s, %s haec data sunt: %s", "commands.data.entity.get": "%s in %s, post numerus multiplicans %s datus, est %s", "commands.data.entity.invalid": "Data lusoris mutare non potes", "commands.data.entity.modified": "Data enitatis %s mutantur", "commands.data.entity.query": "Entitati %s haec data sunt: %s", "commands.data.get.invalid": "%s obtinere non potest; notae numerales solum condonatae sunt", "commands.data.get.multiple": "Hoc argumentum unum valorem generis NBT accipit", "commands.data.get.unknown": "%s obtinere non potest; nota non exsistis", "commands.data.merge.failed": "Nihil mutatum. Designatae proprietates hos valores iam habent", "commands.data.modify.expected_list": "Matricula exspectabatur, accipitur: %s", "commands.data.modify.expected_object": "Obiectum exspectabatur, accipitur: %s", "commands.data.modify.expected_value": "Pretium exspectabatur, accipitur: %s", "commands.data.modify.invalid_index": "Irritus index tabulae: %s", "commands.data.modify.invalid_substring": "Indices subseriei illiciti sunt: ab %s ad %s", "commands.data.storage.get": "%s in arca %s, post numerus multiplicans %s datus, est %s", "commands.data.storage.modified": "Arca %s mutatur", "commands.data.storage.query": "Arca %s hoc habet intra: %s", "commands.datapack.create.already_exists": "Compilatio nomine '%s' iamiam existat", "commands.datapack.create.invalid_full_name": "Nomen novae compilationis '%s' irritum est", "commands.datapack.create.invalid_name": "Irritae insunt figurae in nomine novae compilationis: '%s'", "commands.datapack.create.io_failure": "Compilationem nomine '%s' creare non potest, inspice tabulas", "commands.datapack.create.metadata_encode_failure": "Datum de datis ad sarcinam nomine '%s' ratiocinari non potuit: %s", "commands.datapack.create.success": "Novam compilationem vacuam nomine '%s' creavit", "commands.datapack.disable.failed": "Compilatio '%s' inactiva est!", "commands.datapack.disable.failed.feature": "Compilatio '%s' inactivum fieri non potest quod in vexillo activo est!", "commands.datapack.enable.failed": "Compilatio '%s' iamiam activa est!", "commands.datapack.enable.failed.no_flags": "Compilatio '%s' activum fieri non potest quod vexilla requisita in hoc mundo non activa est: %s!", "commands.datapack.list.available.none": "Nullae compilationes datorum disponibiles sunt", "commands.datapack.list.available.success": "%s compilationes datorum disponibiles sunt: %s", "commands.datapack.list.enabled.none": "Nullae compilationes datorum activae sunt", "commands.datapack.list.enabled.success": "%s compilationes datorum activae sunt: %s", "commands.datapack.modify.disable": "Compilatio datorum %s inactiva facturum sum", "commands.datapack.modify.enable": "Compilatio datorum %s activa facturum sum", "commands.datapack.unknown": "Compilatio datorum ignota '%s'", "commands.debug.alreadyRunning": "Descriptor momentorum iamiam coepit", "commands.debug.function.noRecursion": "De intra functionem describere non possum", "commands.debug.function.noReturnRun": "Investigando cuncto cum \"return run\" non uti potes", "commands.debug.function.success.multiple": "Exitus iussorum %s ex functionibus %s descripti sunt in scapo %s", "commands.debug.function.success.single": "Exitus iussorum %s ex functione '%s' descripti sunt in scapo %s", "commands.debug.function.traceFailed": "Functionem describere non potui", "commands.debug.notRunning": "Descriptor momentorum non iam coepit", "commands.debug.started": "Descriptio momentorum coepta est", "commands.debug.stopped": "Descriptio momentorum constita est post %s secunda et %s momenta (%s momenta singulis secundis)", "commands.defaultgamemode.success": "Modus ludendi normalis nunc est %s", "commands.deop.failed": "Nihil mutatum est. Lusor iamiam administrator non est", "commands.deop.success": "%s nunc administrator moderatri non est", "commands.dialog.clear.multiple": "Ludus commercium cum lusoribus %s rupit", "commands.dialog.clear.single": "Ludus commercium cum lusori %s rupit", "commands.dialog.show.multiple": "Ludus commercium cum lusoribus %s aperuit", "commands.dialog.show.single": "Ludus commercium cum lusore %s aperuit", "commands.difficulty.failure": "Difficultas mutata non est; ea %s iam definita est", "commands.difficulty.query": "Difficultas %s est", "commands.difficulty.success": "Difficultas posita est %s", "commands.drop.no_held_items": "Entitates non possunt ullam rem tenere", "commands.drop.no_loot_table": "Entitas %s non habet tabulam praedae", "commands.drop.no_loot_table.block": "Cubus %s tabulam praedae non habet", "commands.drop.success.multiple": "%s res emissae sunt", "commands.drop.success.multiple_with_table": "%s res emissae sunt e tabula praedae %s", "commands.drop.success.single": "Demisit %s %s", "commands.drop.success.single_with_table": "Stillaret %s %s ex mensa praedae irrumpunt %s", "commands.effect.clear.everything.failed": "Selecto non sunt effectus, ergo ab eo effectus removere non potes", "commands.effect.clear.everything.success.multiple": "Omnes effectus a %s selectis remoti sunt", "commands.effect.clear.everything.success.single": "Omnes effecti a %s re<PERSON>ti sunt", "commands.effect.clear.specific.failed": "Selecto effectus petitus non est", "commands.effect.clear.specific.success.multiple": "Effectus %s a %s selectis remotus est", "commands.effect.clear.specific.success.single": "Effectus %s a %s remotus est", "commands.effect.give.failed": "Aptare hunc effectum non potes (selectus immunis ab effectibus est vel potentior effectus eo prior aptatus est)", "commands.effect.give.success.multiple": "Effectus %s selectibus %s aptatus est", "commands.effect.give.success.single": "Effectus %s ad %s aptatus est", "commands.enchant.failed": "Nihil mutatum est. Aut selecti nullam rem in manibus habent aut incantatio aptari non potuit", "commands.enchant.failed.entity": "%s huic iusso entitas irrita est", "commands.enchant.failed.incompatible": "%s eam incantationem non suscipit", "commands.enchant.failed.itemless": "%s rem non tenet", "commands.enchant.failed.level": "%s maior est quam gradus maximus (%s) incantatione susceptus", "commands.enchant.success.multiple": "Incantatio %s in res %s entitatum induta est", "commands.enchant.success.single": "Incantatio %s in rem entitatis %s induta est", "commands.execute.blocks.toobig": "Nimii cubi in determinatio area sunt (maximus %s, certa %s)", "commands.execute.conditional.fail": "Probatio male evenit", "commands.execute.conditional.fail_count": "Probatio desinitur, exitus malus, numerus: %s", "commands.execute.conditional.pass": "Probatio bene evenit", "commands.execute.conditional.pass_count": "Probatio desinitur, exitus bonus, numerus: %s", "commands.execute.function.instantiationFailure": "Instantiare functionem %s non potuit: %s", "commands.experience.add.levels.success.multiple": "%s gradus experientiae lusoribus %s donati sunt", "commands.experience.add.levels.success.single": "%s gradus experientiae lusori %s donati sunt", "commands.experience.add.points.success.multiple": "%s puncta experientiae lusoribus %s donata sunt", "commands.experience.add.points.success.single": "%s puncta experientiae lusori %s tributa sunt", "commands.experience.query.levels": "%s %s gradus experientiae habet", "commands.experience.query.points": "%s %s puncta experientiae habet", "commands.experience.set.levels.success.multiple": "%s gradus experientiae in %s lusoribus posuit", "commands.experience.set.levels.success.single": "%s gradus experientiae in %s posuit", "commands.experience.set.points.invalid": "Tot experientia ut maximum gradum experientiae huius lusoris superet dari non potest", "commands.experience.set.points.success.multiple": "%s puncta experientiae in %s lusoribus posuit", "commands.experience.set.points.success.single": "%s puncta experientiae in %s posuit", "commands.fill.failed": "Cubi nulli completi sunt", "commands.fill.success": "Prospere %s cubos complevit", "commands.fill.toobig": "Nimii cubi in determinatio area sunt (maximus %s, certa %s)", "commands.fillbiome.success": "Biomes inter %s, %s, %s et %s, %s, %s", "commands.fillbiome.success.count": "%s rationes biomae positae sunt inter %s, %s, %s et %s, %s, %s", "commands.fillbiome.toobig": "Nimis multi caudices in determinato volumine (maximum %s, determinatum %s)", "commands.forceload.added.failure": "Nullae partes indicatae sunt imponendo obligato", "commands.forceload.added.multiple": "%s partes indicatae sunt in %s ab %s ad %s esse imponendo obligato", "commands.forceload.added.none": "Nullae partes obligatae inventae sunt in %s", "commands.forceload.added.single": "Pars indicata %s in %s esse imponendo obligato", "commands.forceload.list.multiple": "%s partes obligatae inventae sunt in %s ad: %s", "commands.forceload.list.single": "Pars obligata inventa est in %s ad: %s", "commands.forceload.query.failure": "Pars ad %s in %s non indicata est imponendo obligato", "commands.forceload.query.success": "Pars ad %s in %s indicata est imponendo obligato", "commands.forceload.removed.all": "Omnes partes obligatae deindicatae sunt in %s", "commands.forceload.removed.failure": "Nullae partes deletae sunt ex imponendo obligato", "commands.forceload.removed.multiple": "%s partes deindicatae sunt in %s ab %s ad %s imponendo obligato", "commands.forceload.removed.single": "Pars %s deindicata est in %s imponendo obligato", "commands.forceload.toobig": "Nimiae partes in regionem dativum (maximum %s, dativae %s)", "commands.function.error.argument_not_compound": "Genus argumenti irritum est: datur %s, sed COMPOUND exspectabatur", "commands.function.error.missing_argument": "Argumentum %2$s functionis %1$s abest", "commands.function.error.missing_arguments": "Argumenta functionis %s absunt", "commands.function.error.parse": "Dum macroiussum %s fit exemplum: Iussum '%s' erat causa erroris: %s", "commands.function.instantiationFailure": "Instantiare functionem %s non potuit: %s", "commands.function.result": "Functio %s reddit %s", "commands.function.scheduled.multiple": "Exsequor functiones %s", "commands.function.scheduled.no_functions": "Functiones quas %s appellantur invenire non potuit", "commands.function.scheduled.single": "Exsequor functionem %s", "commands.function.success.multiple": "Effecta %s iussa ex %s functionibus", "commands.function.success.multiple.result": "Effecta %s functiones", "commands.function.success.single": "Effecta %s iussa ex functione '%s'", "commands.function.success.single.result": "Functio '%2$s' rediit %1$s", "commands.gamemode.success.other": "Modus ludi lusoris %s nunc %s est", "commands.gamemode.success.self": "Modus ludus tuus nunc %s est", "commands.gamerule.query": "Ius ludi %s vulgo est: %s", "commands.gamerule.set": "Ius ludi %s nunc factum est: %s", "commands.give.failed.toomanyitems": "%2$s donari non potest, quod numerus eius %1$s superat", "commands.give.success.multiple": "%s %s lusoribus %s donatum est", "commands.give.success.single": "%s %s lusori %s donatum est", "commands.help.failed": "Iussum ignotum est vel permissiones insufficientes", "commands.item.block.set.success": "Spacium ad %s, %s, %s cum %s repositum est", "commands.item.entity.set.success.multiple": "Spacium in %s entitates cum %s repositum est", "commands.item.entity.set.success.single": "Spacium in %s cum %s repositum est", "commands.item.source.no_such_slot": "Origo spacium %s non habet", "commands.item.source.not_a_container": "Positio originis %s, %s, %s res tenere non potest", "commands.item.target.no_changed.known_item": "A nullo selectorum res %s in spacium %s accepta est", "commands.item.target.no_changes": "A nullo selectorum res in spacium %s accepta est", "commands.item.target.no_such_slot": "Selecto non est spacium %s", "commands.item.target.not_a_container": "Positio destinata %s, %s, %s res tenere non potest", "commands.jfr.dump.failed": "Descriptio JFR servari non potuit: %s", "commands.jfr.start.failed": "Descriptio JFR incipi non potuit", "commands.jfr.started": "Descriptio JFR coepta est", "commands.jfr.stopped": "Descriptio JFR desinit servataque est in %s", "commands.kick.owner.failed": "Expelli erus ludi reti locali non potest", "commands.kick.singleplayer.failed": "In ludo unius non conexus expellere non potest", "commands.kick.success": "%s expulsus(a) est: %s", "commands.kill.success.multiple": "%s enititates interfectae sunt", "commands.kill.success.single": "%s occisus est", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Sunt %s de max %s online lusoribus: %s", "commands.locate.biome.not_found": "Bioma \"%s\" in vicinitate inveniri non potuit", "commands.locate.biome.success": "Citimus %s est ad %s (%s cubi ab hoc)", "commands.locate.poi.not_found": "Locus quaerendus \"%s\" in vicinitate inveniri non potuit", "commands.locate.poi.success": "Citimus %s est ad %s (%s cubi ab hoc)", "commands.locate.structure.invalid": "Non est aedificium \"%s\"", "commands.locate.structure.not_found": "Structura \"%s\" in vicinitate inveniri non potuit", "commands.locate.structure.success": "Citimus %s est ad %s (%s cubi ab hoc)", "commands.message.display.incoming": "%s tibi susurrat: %s", "commands.message.display.outgoing": "Susurras ad %s: %s", "commands.op.failed": "Nihil mutatum est. Lusor iamiam administrator est", "commands.op.success": "%s nunc administrator moderatri est", "commands.pardon.failed": "Nihil mutatum est. Lusor non vetitus est", "commands.pardon.success": "%s vetitio amota est", "commands.pardonip.failed": "Nihil mutatum est. FI non vetita est", "commands.pardonip.invalid": "FI irrita", "commands.pardonip.success": "FI %s vetitio amota est", "commands.particle.failed": "Non omnes videre eam particulam poterant", "commands.particle.success": "Particula %s monstratur", "commands.perf.alreadyRunning": "Descriptor efficacitatis iamiam coepit", "commands.perf.notRunning": "Descriptor efficacitatis non iam coepit", "commands.perf.reportFailed": "Relatio ad emendandum creari non potuit", "commands.perf.reportSaved": "Relatio ad emendandum creata est in %s", "commands.perf.started": "Descriptio efficacitatis X secundorum coepta est (utere '/perf stop' ut prius desinat)", "commands.perf.stopped": "Descriptio efficacitatis constita est post %s secunda et %s momenta (%s momenta singulis secundis)", "commands.place.feature.failed": "Addendum poni non potuit", "commands.place.feature.invalid": "Non est addendum \"%s\"", "commands.place.feature.success": "\"%s\" in %s, %s, %s positum est", "commands.place.jigsaw.failed": "Iunctio generari non potuit", "commands.place.jigsaw.invalid": "Non est genus formarum \"%s\"", "commands.place.jigsaw.success": "Iunctio generata est in %s, %s, %s", "commands.place.structure.failed": "Aedificium poni non potuit", "commands.place.structure.invalid": "Non est aedificium \"%s\"", "commands.place.structure.success": "Aedificium \"%s\" generatum est in %s, %s, %s", "commands.place.template.failed": "Forma poni non potuit", "commands.place.template.invalid": "Non est forma \"%s\"", "commands.place.template.success": "Forma %s in %s, %s, %s posita est", "commands.playsound.failed": "Sonus longinquior est quam ut audiatur", "commands.playsound.success.multiple": "Sonus %s lusoribus %s reproducitur", "commands.playsound.success.single": "Sonus %s ad %s reproducitur", "commands.publish.alreadyPublished": "Ludus multorum iam hospitaris in porta %s", "commands.publish.failed": "Ludum localem hospitare non potes", "commands.publish.started": "Ludus localis hospitaris in porta %s", "commands.publish.success": "Ludus multorum nunc hospitaris in porta %s", "commands.random.error.range_too_large": "Necesse est intervallo valoris fortuiti esse minore quam 2147483647", "commands.random.error.range_too_small": "Necesse est invervallo valoris fortuiti esse plure 1", "commands.random.reset.all.success": "%s seriei fortuitae repositae sunt", "commands.random.reset.success": "Series fortuita %s reposita est", "commands.random.roll": "%s iecit %s (inter %s et %s)", "commands.random.sample.success": "Valor fortuita: %s", "commands.recipe.give.failed": "Praecepta nova nemo didicit", "commands.recipe.give.success.multiple": "Praecepta %s pro %s lusoribus reserata sunt", "commands.recipe.give.success.single": "Praecepta %s pro %s reserata sunt", "commands.recipe.take.failed": "Praecepta nulla possunt dedisci", "commands.recipe.take.success.multiple": "%s praecepta ab %s lusoribus ablata sunt", "commands.recipe.take.success.single": "%s praecepta ab %s ablata sunt", "commands.reload.failure": "Reimponare non potuit, data vetera reservantur", "commands.reload.success": "Reimpono!", "commands.ride.already_riding": "%s iam equitantes %s", "commands.ride.dismount.success": "%s equitantes obturaverunt %s", "commands.ride.mount.failure.cant_ride_players": "Lusores equitari non possunt", "commands.ride.mount.failure.generic": "%s non potuit incipere equitantes %s", "commands.ride.mount.failure.loop": "Entitates in se vel aliquo eius vectorum ascendere non potest", "commands.ride.mount.failure.wrong_dimension": "Entitatem in alia dimensione ascendere non potest", "commands.ride.mount.success": "%s equitantes incipit %s", "commands.ride.not_riding": "%s ullo vehiculo non equitat", "commands.rotate.success": "%s rotabatur", "commands.save.alreadyOff": "Servare iamiam inactivum est", "commands.save.alreadyOn": "Servare iamiam activum est", "commands.save.disabled": "<PERSON><PERSON><PERSON> automate nunc inactivus est", "commands.save.enabled": "<PERSON><PERSON><PERSON> automate nunc activus est", "commands.save.failed": "Ser<PERSON>e ludum non possum (cura ut habeas satis spatium in disco tuo)", "commands.save.saving": "Ludus servatur (hoc accipere licet quidem tempus!)", "commands.save.success": "Ludus servatus est", "commands.schedule.cleared.failure": "Schedula cum ID %s non est", "commands.schedule.cleared.success": "%s schedulae cum id %s delentur", "commands.schedule.created.function": "Functio '%s' efficitur post %s momenta tempore ludi %s", "commands.schedule.created.tag": "Functiones in nota '%s' efficiuntur post %s momenta tempore ludi %s", "commands.schedule.macro": "Macroiussum constitui non potest", "commands.schedule.same_tick": "Pro usitato momento contitui non potest", "commands.scoreboard.objectives.add.duplicate": "Illud nomen iamiam alicui metae est", "commands.scoreboard.objectives.add.success": "Meta nova %s creata est", "commands.scoreboard.objectives.display.alreadyEmpty": "Nihil mutatum est. In illo loco iamiam nulla meta ostendebatur", "commands.scoreboard.objectives.display.alreadySet": "Nihil mutatum est. In illo loco iamiam illa meta ostendebatur", "commands.scoreboard.objectives.display.cleared": "In loco %s nunc nulla meta ostenditur", "commands.scoreboard.objectives.display.set": "In loco %s nunc meta %s ostenditur", "commands.scoreboard.objectives.list.empty": "Metae non sunt", "commands.scoreboard.objectives.list.success": "%s metae sunt: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Nomina iuxta victorias meta %s non iam automate mutantur", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Nomina iuxta victorias meta %s nunc automate mutantur", "commands.scoreboard.objectives.modify.displayname": "Nomen visibile metae %s factum est %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Forma numerorum communis metae %s deleta est", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Forma numerorum communis metae %s mutata est", "commands.scoreboard.objectives.modify.rendertype": "Modus ostendendi metae %s mutatus est", "commands.scoreboard.objectives.remove.success": "Meta %s deleta est", "commands.scoreboard.players.add.success.multiple": "Victoriae %1$s ad victorias entitatum %3$s meta %2$s additae sunt", "commands.scoreboard.players.add.success.single": "Victoriae %1$s ad victorias entitatis %3$s meta %2$s additae sunt (nunc sunt %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Nomina iuxta victorias meta %2$s entitatum %1$s deleta sunt", "commands.scoreboard.players.display.name.clear.success.single": "Nomen iuxta victorias meta %2$s entitatis %1$s deletum est", "commands.scoreboard.players.display.name.set.success.multiple": "Nomina iuxta victorias meta %3$s entitatum %2$s facta sunt %1$s", "commands.scoreboard.players.display.name.set.success.single": "Nomen iuxta victorias entitatis %2$s meta %3$s factum est %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Forma numerorum metae %2$s entitatibus %1$s ad formam communem revertit", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Forma numerorum metae %2$s entitati %1$s ad formam communem revertit", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Forma numerorum metae %2$s entitatibus %1$s mutata est", "commands.scoreboard.players.display.numberFormat.set.success.single": "Forma numerorum metae %2$s entitati %1$s mutata est", "commands.scoreboard.players.enable.failed": "Nihil mutatum est. Haec chele iamiam effici potest", "commands.scoreboard.players.enable.invalid": "Chelae (metae generis \"trigger\") tantum effici possunt", "commands.scoreboard.players.enable.success.multiple": "Chele %s ab entitatibus %s nunc effici potest", "commands.scoreboard.players.enable.success.single": "Chele %s ab entitate %s nunc effici potest", "commands.scoreboard.players.get.null": "Victoriae entitatis %2$s meta %1$s legi non possunt: nam numquam scriptae sunt", "commands.scoreboard.players.get.success": "Victoriae entitatis %1$s meta %3$s sunt %2$s", "commands.scoreboard.players.list.empty": "Meta nullam entitatem sequitur", "commands.scoreboard.players.list.entity.empty": "Entitatis %s nullae victoriae servantur", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "Entitatis %s victoriae metis %s servantur:", "commands.scoreboard.players.list.success": "Meta %s entitates sequitur: %s", "commands.scoreboard.players.operation.success.multiple": "Victoriae entitatum %2$s meta %1$s mutatae sunt", "commands.scoreboard.players.operation.success.single": "Victoriae entitatis %2$s meta %1$s factae sunt %3$s", "commands.scoreboard.players.remove.success.multiple": "Victoriae %1$s de victoriis entitatum %3$s meta %2$s deductae sunt", "commands.scoreboard.players.remove.success.single": "Victoriae %1$s de victoriis entitatis %3$s meta %2$s deductae sunt (nunc sunt %4$s)", "commands.scoreboard.players.reset.all.multiple": "Omnes victoriae entitatum %s deletae sunt", "commands.scoreboard.players.reset.all.single": "Omnes victoriae entitatis %s deletae sunt", "commands.scoreboard.players.reset.specific.multiple": "Victoriae entitatum %2$s meta %1$s deletae sunt", "commands.scoreboard.players.reset.specific.single": "Victoriae entitatis %2$s meta %1$s deletae sunt", "commands.scoreboard.players.set.success.multiple": "Victoriae entitatum %2$s meta %1$s factae sunt %3$s", "commands.scoreboard.players.set.success.single": "Victoriae entitatis %2$s meta %1$s factae sunt %3$s", "commands.seed.success": "Semen: %s", "commands.setblock.failed": "<PERSON><PERSON>us poni non potuit", "commands.setblock.success": "Cubus mutatus est in %s, %s, %s", "commands.setidletimeout.success": "Tempus maximum lusorum inactivorum %s minuta(e) est", "commands.setidletimeout.success.disabled": "Tempus maximum lusorum inactivorum inactivum est", "commands.setworldspawn.failure.not_overworld": "Locum reincunabuli unversalis modo in terra fingeri potest", "commands.setworldspawn.success": "Locus reincunabuli universalis mutatur, nunc %s, %s, %s [%s] est", "commands.spawnpoint.success.multiple": "Locus reincunabuli mutatur, nunc %s, %s, %s [%s] in %s pro %s lusores est", "commands.spawnpoint.success.single": "Locus reincunabuli mutatur, nunc %s, %s, %s [%s] in %s pro %s est", "commands.spectate.not_spectator": "%s non est in modo spectatoris", "commands.spectate.self": "Te spectare non potest", "commands.spectate.success.started": "Nunc spectas %s", "commands.spectate.success.stopped": "Non iam entitatem spectas", "commands.spreadplayers.failed.entities": "Entitates %s circa %s, %s dispergi non potuerunt, quia nimiae entitates sunt: conare dispergere maxime %s entitates", "commands.spreadplayers.failed.invalid.height": "maxHeight %s irritum est; maiorem imo mundo (%s) esse exspectabatur", "commands.spreadplayers.failed.teams": "Factiones %s circa %s, %s dispergi non potuerunt, quia nimiae entitates sunt, conare dispergere maxime %s factiones", "commands.spreadplayers.success.entities": "%s lusores circa %s,%s dispersi sunt, intervallum inter lusores circiter %s cuborum est", "commands.spreadplayers.success.teams": "%s factiones circa %s,%s dispersae sunt, intervallum inter lusores circiter %s cuborum est", "commands.stop.stopping": "Moderatrum clauditur", "commands.stopsound.success.source.any": "Omnes '%s' soni constiti sunt", "commands.stopsound.success.source.sound": "Sonum '%s' in origine '%s' constitus est", "commands.stopsound.success.sourceless.any": "Omnes soni constiti sunt", "commands.stopsound.success.sourceless.sound": "Sonus '%s' constitus est", "commands.summon.failed": "Creare entitatem non potest", "commands.summon.failed.uuid": "Creare entitatem non potest propter UUIDs duplicatos sunt", "commands.summon.invalidPosition": "Irritum locum creationis", "commands.summon.success": "Novus %s creatur", "commands.tag.add.failed": "Selecto ulla nota non est vel nimiae notae sunt", "commands.tag.add.success.multiple": "Nota '%s' ad %s entitates addita est", "commands.tag.add.success.single": "Nota '%s' ad %s addita est", "commands.tag.list.multiple.empty": "<PERSON><PERSON>ae notae sunt in %s entitatibus", "commands.tag.list.multiple.success": "%s entitatem %s notas habent: %s", "commands.tag.list.single.empty": "%s notas non habet", "commands.tag.list.single.success": "%s %s notas habet: %s", "commands.tag.remove.failed": "Selecto haec nota non est", "commands.tag.remove.success.multiple": "Nota '%s' ab %s entitatibus remota est", "commands.tag.remove.success.single": "Nota '%s' ab %s remota est", "commands.team.add.duplicate": "Alicui factioni id nomen iam est", "commands.team.add.success": "Factio %s creata est", "commands.team.empty.success": "%s socii a factione %s remoti sunt", "commands.team.empty.unchanged": "Nihil mutatum est. Ea factio iam vacua est", "commands.team.join.success.multiple": "%s socii factioni %s additi sunt", "commands.team.join.success.single": "%s factioni %s additus(a) est", "commands.team.leave.success.multiple": "%s socii ab omnibus factionibus remoti sunt", "commands.team.leave.success.single": "%s ab omnibus factionibus remotus(a) est", "commands.team.list.members.empty": "Non sunt socii in factione %s", "commands.team.list.members.success": "Factioni %s %s socii sunt: %s", "commands.team.list.teams.empty": "Nullae factiones sunt", "commands.team.list.teams.success": "Sunt %s factiones: %s", "commands.team.option.collisionRule.success": "Nunc lusores factionis %s %s", "commands.team.option.collisionRule.unchanged": "Nihil mutatum est. Lusores eius factionis iam ea ratione trudi possunt", "commands.team.option.color.success": "Color factionis %s factus est: %s", "commands.team.option.color.unchanged": "Nihil mutatum est. Ei factioni is color iam est", "commands.team.option.deathMessageVisibility.success": "Nunc nuntia mortis lusorum factionis %s %s ostenduntur", "commands.team.option.deathMessageVisibility.unchanged": "Nihil mutatum est. Nuntia mortis lusorum eius factionis iam ea ratione ostenduntur", "commands.team.option.friendlyfire.alreadyDisabled": "Nihil mutatum est. Lusores eius factionis socios iam ferire non possunt", "commands.team.option.friendlyfire.alreadyEnabled": "Nihil mutatum est. Lusores eius factionis socios iam ferire possunt", "commands.team.option.friendlyfire.disabled": "Nunc lusores factionis %s socios ferire non possunt", "commands.team.option.friendlyfire.enabled": "Nunc lusores factionis %s socios ferire possunt", "commands.team.option.name.success": "Nomen factionis %s mutatum est", "commands.team.option.name.unchanged": "Nihil mutatum est. Ei factioni id nomen iam est", "commands.team.option.nametagVisibility.success": "Nunc nomina lusorum factionis %s %s ostenduntur", "commands.team.option.nametagVisibility.unchanged": "Nihil mutatum est. Nomina lusorum eius factionis iam ea ratione ostenduntur", "commands.team.option.prefix.success": "Praepositio factionis ad %s posita est", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nihil mutatum est. Ea factio socios invisibiles iam videre non potest", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nihil mutatum est. Ea factio socios invisibiles iam videre potest", "commands.team.option.seeFriendlyInvisibles.disabled": "Factio %s socios invisibiles nondum videre potest", "commands.team.option.seeFriendlyInvisibles.enabled": "Nunc factio %s socios invisibiles videre potest", "commands.team.option.suffix.success": "Additamentum factionis ad %s positum est", "commands.team.remove.success": "Factio %s deleta est", "commands.teammsg.failed.noteam": "Nuntium ad factionem tuam mittere non potes quod in nulla factione es", "commands.teleport.invalidPosition": "Locus teletransportationis irritus est", "commands.teleport.success.entity.multiple": "%s entitates ad %s teletransportatae sunt", "commands.teleport.success.entity.single": "%s ad %s teletransportatus est", "commands.teleport.success.location.multiple": "%s entitates ad %s, %s, %s teletransportatae sunt", "commands.teleport.success.location.single": "%s ad %s, %s, %s teletransportatus est", "commands.test.batch.starting": "Atmosphaera %s classis %s initiabam", "commands.test.clear.error.no_tests": "Ullas probationes invenire non potuit ut deleat", "commands.test.clear.success": "%s aedificia putata sunt", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Ice ut copies ad latibulum", "commands.test.create.success": "Compositio probationis ad probationem %s creata est", "commands.test.error.no_test_containing_pos": "Instantiam probationis quam continet %s, %s, %s invenire non potest", "commands.test.error.no_test_instances": "<PERSON><PERSON><PERSON> instantias probationis invenit", "commands.test.error.non_existant_test": "Probatio %s inveniri non potuit", "commands.test.error.structure_not_found": "Aedificium probationis %s inveniri non potuit", "commands.test.error.test_instance_not_found": "Instantia probationis entitatis cubi inveniri non potuit", "commands.test.error.test_instance_not_found.position": "Instantia probationis entitatis cubi ad probationem in %s, %s, %s inveniri non potuit", "commands.test.error.too_large": "Magnitudo aedificii esse minor quam %s cubi in quoque diametro debet", "commands.test.locate.done": "Indicens finivit, %s aedificia invenit", "commands.test.locate.found": "Adificium ad: %s invenit (intervallum: %s)", "commands.test.locate.started": "Aedificia probationis indicere incepit, hoc accipere licet quidem tempus...", "commands.test.no_tests": "Nullae probationes exsequi", "commands.test.relative_position": "Locus ad %s: %s relativus est", "commands.test.reset.error.no_tests": "Ullas probationes invenire non potuit ut reinitiet", "commands.test.reset.success": "%s aedificia reinitiata sunt", "commands.test.run.no_tests": "Nullae probationes inventae sunt", "commands.test.run.running": "%s probationes exsequitur...", "commands.test.summary": "Probatio ludi complevit! %s probationes exsecutae sunt", "commands.test.summary.all_required_passed": "Omnes probationes requisitae compleverunt :)", "commands.test.summary.failed": "%s probationes requisitae defecerunt :(", "commands.test.summary.optional_failed": "%s probationes facultativae defecerunt", "commands.tick.query.percentiles": "Percentiles: P L:%sms P XCV: %sms P XCIX: %sms, exemplum: %s", "commands.tick.query.rate.running": "Momentorum celeritas selecta: %s in unum secundum.\nTempus in unum momentum circiter est %sms (Selectum: %sms)", "commands.tick.query.rate.sprinting": "Momentorum celeritas selecta: %s in unum secundum (praetermissa, modo pro ratione).\nTempus in unum secundum circiter est %sms", "commands.tick.rate.success": "Momentorum celeritas selecta posita est ad %s in unum secundum", "commands.tick.sprint.report": "Cursus perfectus est cum celeritate %s momentorum in unum secundum et %sms in unum momentum", "commands.tick.sprint.stop.fail": "Cursus momentorum non progreditur", "commands.tick.sprint.stop.success": "Cursus momentorum status est", "commands.tick.status.frozen": "Ludus non movet", "commands.tick.status.lagging": "<PERSON><PERSON> movet, sed cum momentorum celeritate selecta sufficere non potest", "commands.tick.status.running": "<PERSON>dus movet ut solet", "commands.tick.status.sprinting": "Ludus currit", "commands.tick.step.fail": "Ludus prodiri non potui - primum ludus sistendus est", "commands.tick.step.stop.fail": "Gradus momentorum non progreditur", "commands.tick.step.stop.success": "Gradus momentorum status est", "commands.tick.step.success": "%s momenta gradiuntur", "commands.time.query": "Hora %s est", "commands.time.set": "Tempus posuit %s", "commands.title.cleared.multiple": "Titulos pro %s lusoribus putatos sunt", "commands.title.cleared.single": "Titulos pro %s putatos sunt", "commands.title.reset.multiple": "Reinitia optiones tituli pro %s lusoribus", "commands.title.reset.single": "Reinitia optiones tituli pro %s", "commands.title.show.actionbar.multiple": "Monstrare titulus actionbar novum pro %s lusoribus", "commands.title.show.actionbar.single": "Monstrare titulus actionbar novum pro %s", "commands.title.show.subtitle.multiple": "Monstrare subtitulum novum pro %s lusoribus", "commands.title.show.subtitle.single": "Monstrare subtitulum novum pro %s", "commands.title.show.title.multiple": "Monstrare titulum novum pro %s lusoribus", "commands.title.show.title.single": "Monstrare titulum novum pro %s", "commands.title.times.multiple": "Tempus tituli pro %s lusores demutatus est", "commands.title.times.single": "Tempus tituli pro %s demutatus est", "commands.transfer.error.no_players": "<PERSON><PERSON><PERSON> lusor qui transferretur datus est", "commands.transfer.success.multiple": "%s lusores ad %s:%s transferuntur", "commands.transfer.success.single": "%s ad %s:%s transfertur", "commands.trigger.add.success": "%s efficitur (%s eius numero additur)", "commands.trigger.failed.invalid": "Solum metae generis chelae effici possunt", "commands.trigger.failed.unprimed": "Haec meta efficere non potes", "commands.trigger.set.success": "%s efficitur (eius numerus ad %s mutatur)", "commands.trigger.simple.success": "%s efficitur", "commands.version.build_time": "(Dies compilationis) build_time = %s", "commands.version.data": "(Versio datorum mundi) data = %s", "commands.version.header": "Descriptio versionis moderatri:", "commands.version.id": "(Nomen versionis) id = %s", "commands.version.name": "(Agnomen versionis) name = %s", "commands.version.pack.data": "(Versio compilationum datorum) pack_data = %s", "commands.version.pack.resource": "(Versio compilationum supplementorum) pack_resource = %s", "commands.version.protocol": "(Versio datorum transmissorum) protocol = %s (%s)", "commands.version.series": "(Series) series = %s", "commands.version.stable.no": "(Versio instabilis) stable = no", "commands.version.stable.yes": "(Versio stabilis) stable = yes", "commands.waypoint.list.empty": "Nulla loca itineris in %s sunt", "commands.waypoint.list.success": "%s loca itineris in %s: %s", "commands.waypoint.modify.color": "Color loci itineris nunc est %s", "commands.waypoint.modify.color.reset": "Reinitia colorem loci itineris", "commands.waypoint.modify.style": "Genus loci itieris mutatum est", "commands.weather.set.clear": "Tempestas serenam mutatur", "commands.weather.set.rain": "Tempestas imbrem mutatur", "commands.weather.set.thunder": "Tempestas imbrem fulmenque mutatur", "commands.whitelist.add.failed": "Lusor iam est in indice albo", "commands.whitelist.add.success": "In indicem album %s additus est", "commands.whitelist.alreadyOff": "Index albus est iam inactivus", "commands.whitelist.alreadyOn": "Index albus est iam activus", "commands.whitelist.disabled": "Index albus nunc inactivus est", "commands.whitelist.enabled": "Index albus est nunc activus", "commands.whitelist.list": "Sunt %s lusores in indice albo: %s", "commands.whitelist.none": "<PERSON><PERSON><PERSON> lusores sunt in indice albo", "commands.whitelist.reloaded": "Catena personarum admissarum reimposita est", "commands.whitelist.remove.failed": "Lusor non est in indice albo", "commands.whitelist.remove.success": "Ex indice albo %s remotus est", "commands.worldborder.center.failed": "Nihil mutatum est. Mundi terminis centrum iamiam illic est", "commands.worldborder.center.success": "Mundi terminis centrum mutatur, nunc est %s, %s", "commands.worldborder.damage.amount.failed": "Nihil mutatur est. Mundi terminis iniuria iamiam illa est", "commands.worldborder.damage.amount.success": "Mundi terminis iniuria ad %s per cubos quoque secundo mutatur", "commands.worldborder.damage.buffer.failed": "Nihil mutatur est. Praeter mundi termen spatium ante iniuriam iamiam illud est", "commands.worldborder.damage.buffer.success": "Praeter mundi termen spatium sine iniura ad %s cubos mutatur", "commands.worldborder.get": "Mundi terminis latitudo hunc est %s cubi", "commands.worldborder.set.failed.big": "Mundi termen maior quam %s cuborum esse non potest", "commands.worldborder.set.failed.far": "Mundi termen plus quam %s cubis abesse non potest", "commands.worldborder.set.failed.nochange": "Nihil mutatum est. Mundi terminis latitudo iamiam illa est", "commands.worldborder.set.failed.small": "Mundi termen minor 1 cubi esse non potest", "commands.worldborder.set.grow": "Mundi terminis latitudo ad %s cubos in %s secundis augetur", "commands.worldborder.set.immediate": "Mundi terminis latitudo ad %s cubos mutatur", "commands.worldborder.set.shrink": "Mundi terminis latitudo ad %s cubos in %s secundis deminuitur", "commands.worldborder.warning.distance.failed": "Nihil mutatur est. Mundi terminis monitus spatium iamiam illud est", "commands.worldborder.warning.distance.success": "Mundi terminis monitus spatium ad %s cubos mutatur", "commands.worldborder.warning.time.failed": "Nihil mutatur est. Mundi terminis monitus tempus iamiam illud est", "commands.worldborder.warning.time.success": "Mundi terminis monitus tempus ad %s secundos mutatur", "compliance.playtime.greaterThan24Hours": "Iam diutius quam per XXIV horas ludebas", "compliance.playtime.hours": "Iam per %s horas ludebas", "compliance.playtime.message": "Admodum ludere vitam tuam cotidianam afficere potest", "connect.aborted": "Interruptus", "connect.authorizing": "Inis...", "connect.connecting": "<PERSON><PERSON><PERSON> conector...", "connect.encrypting": "Intrico...", "connect.failed": "Moderatro conecti non potui", "connect.failed.transfer": "In moderatrum transferente, conectere non potuit", "connect.joining": "In mundum inis...", "connect.negotiating": "Negotiationes...", "connect.reconfiging": "Reinstructiones...", "connect.reconfiguring": "Reinstructiones...", "connect.transferring": "In moderatrum novum transferris...", "container.barrel": "Cupa", "container.beacon": "<PERSON><PERSON><PERSON>", "container.beehive.bees": "Apes: %s ex %s", "container.beehive.honey": "Mel: %s ex %s", "container.blast_furnace": "Caminus", "container.brewing": "Mixtarius", "container.cartography_table": "Mensa chartographiae", "container.chest": "Cista", "container.chestDouble": "Cista magna", "container.crafter": "Fabricator", "container.crafting": "Fabricatio", "container.creative": "Selectio rerum", "container.dispenser": "Distributor", "container.dropper": "Demittetrum", "container.enchant": "Incanta", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapides lazuli", "container.enchant.lapis.one": "I Lapis <PERSON>li", "container.enchant.level.many": "%s gradus incantandi", "container.enchant.level.one": "1 gradus incantandi", "container.enchant.level.requirement": "Gradus requisiti: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "Fornax", "container.grindstone_title": "Reficere et delustrare", "container.hopper": "Infundibulum rerum", "container.inventory": "Inventarium", "container.isLocked": "%s est clausum!", "container.lectern": "Lectrum", "container.loom": "Tela", "container.repair": "Reparare et nominare", "container.repair.cost": "Sumptus incantationis: %1$s", "container.repair.expensive": "Nimis sumptuosus!", "container.shulkerBox": "<PERSON><PERSON> s<PERSON>", "container.shulkerBox.itemCount": "%2$s %1$s", "container.shulkerBox.more": "et %s plures...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Aperiri non potest. Praeda nondum generata.", "container.stonecutter": "Saxumsecator", "container.upgrade": "Meliora res", "container.upgrade.error_tooltip": "Res hac ratione regenerari non potest", "container.upgrade.missing_template_tooltip": "Adde formam fabrilem", "controls.keybinds": "Associationes clavium...", "controls.keybinds.duplicateKeybinds": "Hoc clav quoque in usum est pro:\n%s", "controls.keybinds.title": "Associationes clavium", "controls.reset": "Reinitia", "controls.resetAll": "Reinitia signa", "controls.title": "<PERSON><PERSON>", "createWorld.customize.buffet.biome": "Selige bioma", "createWorld.customize.buffet.title": "Immutatio uni biomae", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Fundus - %s", "createWorld.customize.flat.layer.top": "Summitas - %s", "createWorld.customize.flat.removeLayer": "Dele planum", "createWorld.customize.flat.tile": "Materia strati", "createWorld.customize.flat.title": "Immutatio planissimi", "createWorld.customize.presets": "Formae praefactae", "createWorld.customize.presets.list": "<PERSON><PERSON>, haec aliqui sunt quod ante fecimus!", "createWorld.customize.presets.select": "Uti formam praefactam", "createWorld.customize.presets.share": "Visne formam tuam praefactam cum alicui communicare? Utere rectangulum inferum!", "createWorld.customize.presets.title": "Elige formam praefactam", "createWorld.preparing": "Paro ut mundus creetur...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Cetera", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Auctores operum additorum", "credits_and_attribution.button.credits": "Auctor<PERSON> ludi", "credits_and_attribution.button.licenses": "Leges de operibus additis adhibendis", "credits_and_attribution.screen.title": "Auctor<PERSON>", "dataPack.bundle.description": "Addit sarcinam ut eam experiri possis", "dataPack.bundle.name": "Sarcinae", "dataPack.locator_bar.description": "Ostende directionem aliorum lusorum in ludo multorum", "dataPack.locator_bar.name": "Vectis indicii", "dataPack.minecart_improvements.description": "<PERSON><PERSON><PERSON>s motus melior", "dataPack.minecart_improvements.name": "<PERSON><PERSON><PERSON>", "dataPack.redstone_experiments.description": "Emendationes Experimentales de Redstone", "dataPack.redstone_experiments.name": "Experimenta de redstone", "dataPack.title": "Selige compilationes datorum", "dataPack.trade_rebalance.description": "Mercedes vicanorum renovatae sunt", "dataPack.trade_rebalance.name": "Renovatio mercedum vicanorum", "dataPack.update_1_20.description": "Novae res in Minecraft 1.20", "dataPack.update_1_20.name": "Emendatio 1.20", "dataPack.update_1_21.description": "Novae res in Minecraft 1.21", "dataPack.update_1_21.name": "Emendatio 1.21", "dataPack.validation.back": "Redi rursum", "dataPack.validation.failed": "Compilationes irritae sunt!", "dataPack.validation.reset": "Reinitia ad normales", "dataPack.validation.working": "Compilationes datorum inspiciuntur...", "dataPack.vanilla.description": "Data normalia in Minecraft", "dataPack.vanilla.name": "Normalis", "dataPack.winter_drop.description": "Res novae quae largitione hiberna addentur", "dataPack.winter_drop.name": "Largitio hiberna", "datapackFailure.safeMode": "<PERSON><PERSON> certus", "datapackFailure.safeMode.failed.description": "Hic mundus data servandae irrita vel corrupta continet.", "datapackFailure.safeMode.failed.title": "Mundum in modo certo imponere non potuit.", "datapackFailure.title": "Mundum imponere non potes prae errores in compilationibus datorum selectis.\nAut potes conari mundum imponere solum cum compilatione datorum normali (i. e. modum certum) aut redire ad simulacrum principale et figere errores ipse.", "death.attack.anvil": "%1$s ab incude cadente contritus(a) est", "death.attack.anvil.player": "%1$s ab incude cadente contritus(a) est dum cum %2$s pugnavit", "death.attack.arrow": "%1$s ab %2$s sagittatus(a) est", "death.attack.arrow.item": "%1$s ab %2$s cum %3$s sagittatus(a) est", "death.attack.badRespawnPoint.link": "Commento intentionali ludi", "death.attack.badRespawnPoint.message": "%1$s ab %2$s occisus(a) est", "death.attack.cactus": "%1$s ad mortem pungebatur", "death.attack.cactus.player": "%1$s in cactum ambulavit dum ab %2$s defugit", "death.attack.cramming": "%1$s contritus(a) est", "death.attack.cramming.player": "%1$s ab %2$s contritus(a) est", "death.attack.dragonBreath": "%1$s halitu draconis assavitur", "death.attack.dragonBreath.player": "%1$s ab %2$s halitu draconis assavitur", "death.attack.drown": "%1$s mersit", "death.attack.drown.player": "%1$s mersit dum ab %2$s defugit", "death.attack.dryout": "%1$s peraruit", "death.attack.dryout.player": "%1$s peraruit dum ab %2$s defugit", "death.attack.even_more_magic": "%1$s ab amplius magia mortuus(a) est", "death.attack.explosion": "%1$s dirupit", "death.attack.explosion.player": "%1$s ab %2$s diruptus(a) est", "death.attack.explosion.player.item": "%1$s ab %2$s cum %3$s diruptus(a) est", "death.attack.fall": "%1$s terram nimis conflixit", "death.attack.fall.player": "%1$s terram nimis conflixit dum ab %2$s defugit", "death.attack.fallingBlock": "%1$s ab cubo cadente contritus(a) est", "death.attack.fallingBlock.player": "%1$s ab cubo cadente contritus(a) est dum cum %2$s pugnavit", "death.attack.fallingStalactite": "%1$s stalactita cadente punctus(a) est", "death.attack.fallingStalactite.player": "%1$s stalactita cadente punctus(a) est dum cum %2$s pugnat", "death.attack.fireball": "%1$s ab %2$s globo igneo conflagravit", "death.attack.fireball.item": "%1$s ab %2$s cum %3$s quom globo igneo conflagravit", "death.attack.fireworks": "%1$s est ruptus(a) et ereptus(a)", "death.attack.fireworks.item": "%1$s est ruptus(a) et ereptus(a) ob missile pyrotechnicum ab %2$s ex %3$s iactum", "death.attack.fireworks.player": "%1$s est ruptus(a) et ereptus(a) dum cum %2$s pugnat", "death.attack.flyIntoWall": "%1$s energiam cineticam sensit", "death.attack.flyIntoWall.player": "%1$s energiam cineticam sensit dum ab %2$s defugit", "death.attack.freeze": "%1$s ad mortem congelavit", "death.attack.freeze.player": "%1$s ad mortem congelatus(a) est a %2$s", "death.attack.generic": "%1$s mortuus(a) est", "death.attack.generic.player": "%1$s propter %2$s mortuus(a) est", "death.attack.genericKill": "%1$s interfectus(a) est", "death.attack.genericKill.player": "%1$s interfectus(a) est dum cum %2$s pugnat", "death.attack.hotFloor": "%1$s dispexit ut solum lava sit", "death.attack.hotFloor.player": "%1$s in regionem periculosam ambulavit propter %2$s", "death.attack.inFire": "%1$s conflagravit", "death.attack.inFire.player": "%1$s in flammam ambulavit dum cum %2$s pugnavit", "death.attack.inWall": "%1$s in muro suffocavit", "death.attack.inWall.player": "%1$s in muro exanimatus(a) est dum cum %2$s pugnat", "death.attack.indirectMagic": "%1$s ab %2$s cum magia occisus(a) est", "death.attack.indirectMagic.item": "%1$s ab %2$s cum %3$s occisus(a) est", "death.attack.lava": "%1$s in lava natare conatus(a) est", "death.attack.lava.player": "%1$s in lava natare conatus(a) est ut ab %2$s fugeret", "death.attack.lightningBolt": "%1$s fulmine ictus(a) est", "death.attack.lightningBolt.player": "%1$s fulmine ictus(a) est dum cum %2$s pugnat", "death.attack.mace_smash": "%1$s ab %2$s percisus(a) est", "death.attack.mace_smash.item": "%1$s ab %2$s cum %3$s percisus(a) est", "death.attack.magic": "%1$s ab magia mortuus(a) est", "death.attack.magic.player": "%1$s ab magia mortuus(a) est dum temptabat %2$s fugere", "death.attack.message_too_long": "<PERSON><PERSON>, nuntius nimis longus mittere plene erat. Mea culpa! Ecce est versio brevior: %s", "death.attack.mob": "%1$s ab %2$s occisus(a) est", "death.attack.mob.item": "%1$s ab %2$s cum %3$s occisus(a) est", "death.attack.onFire": "%1$s ad mortem arsit", "death.attack.onFire.item": "%1$s tote conflagravit dum cum %2$s %3$s ferente pugnat", "death.attack.onFire.player": "%1$s tote conflagravit dum cum %2$s pugnat", "death.attack.outOfWorld": "%1$s ex mundo cecidit", "death.attack.outOfWorld.player": "%1$s in eodem mundo atque %2$s vivere non voluit", "death.attack.outsideBorder": "%1$s exiit confinia mundi", "death.attack.outsideBorder.player": "%1$s exiit confinia mundi dum cum %2$s pugnavit", "death.attack.player": "%1$s ab %2$s occisus(a) est", "death.attack.player.item": "%1$s ab %2$s cum %3$s occisus(a) est", "death.attack.sonic_boom": "%1$s vi soni boatus deletus(a) est", "death.attack.sonic_boom.item": "%1$s vi soni boatus deletus(a) est dum ab %2$s %3$s ferente defugit", "death.attack.sonic_boom.player": "%1$s vi soni boatus deletus(a) est dum ab %2$s defugit", "death.attack.stalagmite": "%1$s pungebat super stalagmites", "death.attack.stalagmite.player": "%1$s pungebat super stalagmites dum %2$s pugnare", "death.attack.starve": "%1$s ad mortem esurivit", "death.attack.starve.player": "%1$s ad mortem esurivit dum cum %2$s pugnat", "death.attack.sting": "%1$s ad mortem punctum est", "death.attack.sting.item": "%1$s ad mortem punctum est ab %2$s cum %3$s", "death.attack.sting.player": "%1$s ad mortem punctum est ab %2$s", "death.attack.sweetBerryBush": "%1$s a frutice bacarum dulcium ad mortem punctus(a) est", "death.attack.sweetBerryBush.player": "%1$s a frutice bacarum dulcium ad mortem punctus(a) dum ab %2$s defugit", "death.attack.thorns": "%1$s occisus(a) est dum %2$s nocuit", "death.attack.thorns.item": "%1$s ab %3$s occisus(a) est dum %2$s nocuit", "death.attack.thrown": "%1$s ab %2$s pulsatus est", "death.attack.thrown.item": "%1$s ab %2$s cum %3$s pulsatus est", "death.attack.trident": "%1$s ab %2$s transfixus(a) est", "death.attack.trident.item": "%1$s ab %2$s cum %3$s transfixus(a) est", "death.attack.wither": "%1$s deustus(a) est", "death.attack.wither.player": "%1$s deustus(a) est dum cum %2$s pugnat", "death.attack.witherSkull": "%1$s ictus(a) est calva ab %2$s", "death.attack.witherSkull.item": "%1$s ictus(a) est calva ab %2$s cum %3$s", "death.fell.accident.generic": "%1$s de loco alto cecidit", "death.fell.accident.ladder": "%1$s de scala cecidit", "death.fell.accident.other_climbable": "%1$s cecidit cum conscendit", "death.fell.accident.scaffolding": "%1$s de fulcimine cecidit", "death.fell.accident.twisting_vines": "%1$s de vineis contortis cecidit", "death.fell.accident.vines": "%1$s de vineis cecidit", "death.fell.accident.weeping_vines": "%1$s de vineis lacrimosis cecidit", "death.fell.assist": "%1$s ab %2$s cadere damnatus(a) est", "death.fell.assist.item": "%1$s ab %2$s cum %3$s cadere damnatus(a) est", "death.fell.finish": "%1$s longe cecidit et a %2$s peractum est", "death.fell.finish.item": "%1$s longe cecidit et a %2$s cum %3$s peractum est", "death.fell.killer": "%1$s demittere damnatus est", "deathScreen.quit.confirm": "Visne vero ludum relinquere?", "deathScreen.respawn": "Renascere", "deathScreen.score": "Victoriae", "deathScreen.score.value": "Victoriae: %s", "deathScreen.spectate": "Specta mundum", "deathScreen.title": "Mortuus(a) es!", "deathScreen.title.hardcore": "Ludus est finitus!", "deathScreen.titleScreen": "Simulacrum principale", "debug.advanced_tooltips.help": "F III + H = Descriptiones proditae", "debug.advanced_tooltips.off": "Descriptiones proditae: latent", "debug.advanced_tooltips.on": "Descriptiones proditae: monstrantur", "debug.chunk_boundaries.help": "F III + G = Monstra fines partium", "debug.chunk_boundaries.off": "Fines partium: latent", "debug.chunk_boundaries.on": "Fines partium: monstrantur", "debug.clear_chat.help": "F III + D = Purificare locutorium", "debug.copy_location.help": "F III + C = Ut exemplum locus /tp iussus, habere F III + C ut corruere quod ludus", "debug.copy_location.message": "Positio ad latibulum copiata est", "debug.crash.message": "F III + C premitur. Ho<PERSON> corruet ludus nisi absolutus erit.", "debug.crash.warning": "Corruet post %s...", "debug.creative_spectator.error": "Non licet tibi modum ludi mutare", "debug.creative_spectator.help": "F III + N = Commutare modum inter priorem modumque spectatoris", "debug.dump_dynamic_textures": "Textus dynamici servati sunt in %s", "debug.dump_dynamic_textures.help": "F3 + A = Tubur dynamis texturis", "debug.gamemodes.error": "Non licet tibi indicem modum mutandi aperire", "debug.gamemodes.help": "F III + F IV = Aperi indicem modum mutandi", "debug.gamemodes.press_f4": "[ F IV ]", "debug.gamemodes.select_next": "%s Posterior", "debug.help.help": "F III + Q = Ostendere hanc tabulam", "debug.help.message": "Associationes clavium:", "debug.inspect.client.block": "Ex client data cubis copiata sunt ad latibulum", "debug.inspect.client.entity": "Ex client data entitatis copiata sunt ad latibulum", "debug.inspect.help": "F III + I = Copia data entitatis vel cubi ad latibulum", "debug.inspect.server.block": "Ex moderatro data cubis copiata sunt ad latibulum", "debug.inspect.server.entity": "Ex moderatro data entitatis copiata sunt ad latibulum", "debug.pause.help": "F III + Fuga = Consiste sine aperire indicem optionum consistendi (si consistere licet)", "debug.pause_focus.help": "F III + P = Consiste cum focus perditur", "debug.pause_focus.off": "Consiste cum focus perditur: inactivum", "debug.pause_focus.on": "Consiste cum focus perditur: activum", "debug.prefix": "[Inquire]:", "debug.profiling.help": "F III + L = Incipe aut desine descriptionem", "debug.profiling.start": "Descriptio %s secundorum coepta est. Utere F III + L ut prius desinat", "debug.profiling.stop": "Descriptio desiit. Relatus servatus est in %s", "debug.reload_chunks.help": "F III + A = Relegere morsa", "debug.reload_chunks.message": "Omnes partes reimponuntur", "debug.reload_resourcepacks.help": "F III + T = Reimpono compilationes supplementorum", "debug.reload_resourcepacks.message": "Compilationes supplementorum reimpositae sunt", "debug.show_hitboxes.help": "F III + B = Monstra arcas feriendas", "debug.show_hitboxes.off": "Arcae feriendae: abditae", "debug.show_hitboxes.on": "Arcae feriendae: monstrantur", "debug.version.header": "Descriptio versionis clientis:", "debug.version.help": "F III + V = Descriptio versionis clientis", "demo.day.1": "Haec demonstratio quinque dies ludi durabit. Fac optima quae potes!", "demo.day.2": "<PERSON><PERSON> secundus", "demo.day.3": "<PERSON><PERSON> tertius", "demo.day.4": "<PERSON>s quartus", "demo.day.5": "Hic dies ultimus tuus est!", "demo.day.6": "Excessisti tuum diem quintum. Utere %s ut imaginem creationis tuae serves.", "demo.day.warning": "Tempus tuus paene deminuitur!", "demo.demoExpired": "Tempus demonstrationis exiit!", "demo.help.buy": "Nunc eme!", "demo.help.fullWrapped": "Haec demonstratio quinque dies in ludo (circiter unam horam quadragintaque minutas in vita vera). Quaere incrementa in factis! Fruere!", "demo.help.inventory": "Preme %1$s ut inventarium tuum aperias", "demo.help.jump": "Preme %1$s ut salias", "demo.help.later": "Procede ludere!", "demo.help.movement": "Utere clavibus %1$s, %2$s, %3$s, %4$s et mure ut ambules", "demo.help.movementMouse": "Utere mure ut circumspectes", "demo.help.movementShort": "Preme %1$s, %2$s, %3$s, %4$s ut ambules", "demo.help.title": "Minecraft modus demonstrationis", "demo.remainingTime": "Tempus manens: %s", "demo.reminder": "Tempus demonstrationis terminavit. Ludum eme ut continues aut novum mundum incipe!", "difficulty.lock.question": "Visne vero difficultatem mundi firmare? Difficultas %1$s manebit, nec umquam mutari poterit.", "difficulty.lock.title": "Difficultas mundi firma", "disconnect.endOfStream": "Finis fluctus", "disconnect.exceeded_packet_rate": "Expulsatus(a) quia limitem fascis excedebat", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Neglecta status petitionem", "disconnect.loginFailedInfo": "Inire non potuit: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Vetatur ludere cum aliis. Quaeso, optiones Microsoft inspicias.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> irrita (conare ludum deductoremque reincipere)", "disconnect.loginFailedInfo.serversUnavailable": "Servitores consignationis contingeri non possunt. Iterum conare, quaeso.", "disconnect.loginFailedInfo.userBanned": "Tu interdictus(a) es a ludendo ludum interretialem", "disconnect.lost": "Conexus amissus est", "disconnect.packetError": "Error commentariorum nexus", "disconnect.spam": "Expulsus(a) es quia saginare", "disconnect.timeout": "Tempus exiit", "disconnect.transfer": "Translatus(a) es ad moderatrum alterum", "disconnect.unknownHost": "<PERSON><PERSON><PERSON> ignotus est", "download.pack.failed": "%s ex %s compilationes extrahere non potuerunt", "download.pack.progress.bytes": "Progressus: %s (magnitudo totalis ignota)", "download.pack.progress.percent": "Progressus: %s%%", "download.pack.title": "Extrahens compilationem supplementorum %s/%s", "editGamerule.default": "Valor normalis: %s", "editGamerule.title": "Muta iures ludi", "effect.duration.infinite": "Perpetuum", "effect.minecraft.absorption": "Absorptio", "effect.minecraft.bad_omen": "Omen malum", "effect.minecraft.blindness": "Caecitas", "effect.minecraft.conduit_power": "Potentia conducti", "effect.minecraft.darkness": "Tenebrae", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON>", "effect.minecraft.fire_resistance": "Resistentia igni", "effect.minecraft.glowing": "Lu<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Festinatio", "effect.minecraft.health_boost": "Incrementum salutis", "effect.minecraft.hero_of_the_village": "Vindex vici", "effect.minecraft.hunger": "Fames", "effect.minecraft.infested": "Infestatio", "effect.minecraft.instant_damage": "Vulnus immediatum", "effect.minecraft.instant_health": "Salus immediata", "effect.minecraft.invisibility": "Invisibilitas", "effect.minecraft.jump_boost": "<PERSON><PERSON> salire", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Fortuna", "effect.minecraft.mining_fatigue": "Perfodio lassitudine", "effect.minecraft.nausea": "Nausia", "effect.minecraft.night_vision": "Visio nocturna", "effect.minecraft.oozing": "Manatio", "effect.minecraft.poison": "Venenum", "effect.minecraft.raid_omen": "Omen incursionis", "effect.minecraft.regeneration": "Regeneratio", "effect.minecraft.resistance": "Resistentia", "effect.minecraft.saturation": "Saturitas", "effect.minecraft.slow_falling": "<PERSON><PERSON> lentus", "effect.minecraft.slowness": "Tarditas", "effect.minecraft.speed": "Velocitas", "effect.minecraft.strength": "Fortitudo", "effect.minecraft.trial_omen": "Omen periculorum", "effect.minecraft.unluck": "Fortuna mala", "effect.minecraft.water_breathing": "Respiratio in aqua", "effect.minecraft.weakness": "Infirmitas", "effect.minecraft.weaving": "Contextus", "effect.minecraft.wind_charged": "Inflatio", "effect.minecraft.wither": "<PERSON><PERSON><PERSON>", "effect.none": "Effectus non est", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Affinitas aquae", "enchantment.minecraft.bane_of_arthropods": "Inimicus aranearum", "enchantment.minecraft.binding_curse": "Exsecratio ligandi", "enchantment.minecraft.blast_protection": "Depulsio fragorum", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON> fulmineus", "enchantment.minecraft.density": "Densitas", "enchantment.minecraft.depth_strider": "Cursor aquae", "enchantment.minecraft.efficiency": "Efficacia", "enchantment.minecraft.feather_falling": "C<PERSON> pinnatus", "enchantment.minecraft.fire_aspect": "Ardor", "enchantment.minecraft.fire_protection": "Depulsio flammarum", "enchantment.minecraft.flame": "Sagittae flammarum", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Viator hiemis", "enchantment.minecraft.impaling": "Iaculum hamatum", "enchantment.minecraft.infinity": "Infinitas", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Praedatio", "enchantment.minecraft.loyalty": "Fides", "enchantment.minecraft.luck_of_the_sea": "Fortuna maris", "enchantment.minecraft.lure": "Esca", "enchantment.minecraft.mending": "Refectio", "enchantment.minecraft.multishot": "Iactus multiplex", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Vis", "enchantment.minecraft.projectile_protection": "Depulsio iaculorum", "enchantment.minecraft.protection": "Praesidium", "enchantment.minecraft.punch": "Pulsatio", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON><PERSON> celeris", "enchantment.minecraft.respiration": "Respiratio", "enchantment.minecraft.riptide": "Vertex", "enchantment.minecraft.sharpness": "Acies", "enchantment.minecraft.silk_touch": "Tactus sericus", "enchantment.minecraft.smite": "Caedens", "enchantment.minecraft.soul_speed": "Velocitas animarum", "enchantment.minecraft.sweeping": "Labrum deverrens", "enchantment.minecraft.sweeping_edge": "Labrum deverrens", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON>", "enchantment.minecraft.thorns": "Spinae", "enchantment.minecraft.unbreaking": "Infragilitas", "enchantment.minecraft.vanishing_curse": "Exsecratio evanescendi", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON><PERSON> vent<PERSON>", "entity.minecraft.acacia_boat": "Ratis acaciae", "entity.minecraft.acacia_chest_boat": "Ratis acaciae cum cista", "entity.minecraft.allay": "Relevator", "entity.minecraft.area_effect_cloud": "Effectus nubis areae", "entity.minecraft.armadillo": "Dasypus", "entity.minecraft.armor_stand": "Statumen armorum", "entity.minecraft.arrow": "Sagitta", "entity.minecraft.axolotl": "Salamandra mexicana", "entity.minecraft.bamboo_chest_raft": "Ratis harundinum Indicarum cum cista", "entity.minecraft.bamboo_raft": "Ratis harundinum Indicarum", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Apis", "entity.minecraft.birch_boat": "<PERSON><PERSON> betullae", "entity.minecraft.birch_chest_boat": "Ratis betullae cum cista", "entity.minecraft.blaze": "Flammifer", "entity.minecraft.block_display": "Imago cubi", "entity.minecraft.boat": "<PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON>ludiva<PERSON>", "entity.minecraft.breeze": "Ventifer", "entity.minecraft.breeze_wind_charge": "Missile ventosum", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "<PERSON><PERSON> cavernae", "entity.minecraft.cherry_boat": "<PERSON><PERSON> cera<PERSON>", "entity.minecraft.cherry_chest_boat": "Ratis cerasi cum cista", "entity.minecraft.chest_boat": "Ratis cum cista", "entity.minecraft.chest_minecart": "Plaustrum cista", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Plaustrum cum cubo iussorum", "entity.minecraft.cow": "Vacca", "entity.minecraft.creaking": "Crepax", "entity.minecraft.creaking_transient": "Crepax", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON>is quercus nigrae", "entity.minecraft.dark_oak_chest_boat": "Ratis quercus nigrae cum cista", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "Asinus", "entity.minecraft.dragon_fireball": "Globus flammeus draconis", "entity.minecraft.drowned": "Mersus", "entity.minecraft.egg": "Ovum iactum", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON> antiquus", "entity.minecraft.end_crystal": "Crystallum End", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evocator", "entity.minecraft.evoker_fangs": "<PERSON><PERSON> evocatoris", "entity.minecraft.experience_bottle": "<PERSON><PERSON> incantandi iactus", "entity.minecraft.experience_orb": "Orbis experientiae", "entity.minecraft.eye_of_ender": "Ender oculus", "entity.minecraft.falling_block": "<PERSON><PERSON><PERSON> decidens", "entity.minecraft.falling_block_type": "%s in casu", "entity.minecraft.fireball": "Globus flammeus", "entity.minecraft.firework_rocket": "Missile pyrotechnicum", "entity.minecraft.fishing_bobber": "Allector piscatorius", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Plaustrum fornace", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Gigas", "entity.minecraft.glow_item_frame": "Forma rerum lucida", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON> lucida", "entity.minecraft.goat": "<PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Plaustrum infundibulo", "entity.minecraft.horse": "<PERSON>qu<PERSON>", "entity.minecraft.husk": "<PERSON><PERSON>", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "<PERSON><PERSON>", "entity.minecraft.iron_golem": "Automatum ferreum", "entity.minecraft.item": "Res", "entity.minecraft.item_display": "Imago rei", "entity.minecraft.item_frame": "Forma rerum", "entity.minecraft.jungle_boat": "<PERSON>is theobromatis", "entity.minecraft.jungle_chest_boat": "Ratis theobromatis cum cista", "entity.minecraft.killer_bunny": "Cuni<PERSON>", "entity.minecraft.leash_knot": "Nodus lori", "entity.minecraft.lightning_bolt": "Fulmen", "entity.minecraft.lingering_potion": "<PERSON>tio commorans", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Sputum lamae", "entity.minecraft.magma_cube": "Cub<PERSON> magmatis", "entity.minecraft.mangrove_boat": "<PERSON><PERSON> manglis", "entity.minecraft.mangrove_chest_boat": "Ratis manglis cum cista", "entity.minecraft.marker": "Signum", "entity.minecraft.minecart": "Plaustrum", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON> quercea", "entity.minecraft.oak_chest_boat": "Ratis quercea cum cista", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Creator rerum praesagus", "entity.minecraft.painting": "Pictura", "entity.minecraft.pale_oak_boat": "Ratis quercus pallidae", "entity.minecraft.pale_oak_chest_boat": "Ratis quercus pallidae cum cista", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Psittacus", "entity.minecraft.phantom": "Phantas<PERSON>", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin ferus", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "Lu<PERSON>", "entity.minecraft.polar_bear": "Ursus polaris", "entity.minecraft.potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON> aculeatus", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Vastator", "entity.minecraft.salmon": "Salmo", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON> shulker", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON>quus sceletalis", "entity.minecraft.slime": "Mucus", "entity.minecraft.small_fireball": "Globus flammeus parvus", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Automatum niveum", "entity.minecraft.snowball": "<PERSON><PERSON> nivis", "entity.minecraft.spawner_minecart": "Plaustrum creatore monstrorum", "entity.minecraft.spectral_arrow": "Sagitta lucifera", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.spruce_boat": "<PERSON>is piceae", "entity.minecraft.spruce_chest_boat": "Ratis piceae cum cista", "entity.minecraft.squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.stray": "<PERSON><PERSON><PERSON>", "entity.minecraft.strider": "Ambulator", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Imago scripti", "entity.minecraft.tnt": "Dynamites ardens", "entity.minecraft.tnt_minecart": "Plaustrum fodinae cum dynamite", "entity.minecraft.trader_llama": "<PERSON> mercatoris", "entity.minecraft.trident": "Tridens", "entity.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> multigener", "entity.minecraft.tropical_fish.predefined.0": "Amphip<PERSON> percula", "entity.minecraft.tropical_fish.predefined.1": "Zebrasoma rostratum", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON> cornutus", "entity.minecraft.tropical_fish.predefined.11": "Chaetodon ornatissimus", "entity.minecraft.tropical_fish.predefined.12": "Scarus", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON> ciliaris", "entity.minecraft.tropical_fish.predefined.14": "Cichlida rubra", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "Polymenida", "entity.minecraft.tropical_fish.predefined.18": "Amphi<PERSON><PERSON> frenatus", "entity.minecraft.tropical_fish.predefined.19": "Balistida", "entity.minecraft.tropical_fish.predefined.2": "Paracanth<PERSON> hepatus", "entity.minecraft.tropical_fish.predefined.20": "Sc<PERSON><PERSON> hypselopterus", "entity.minecraft.tropical_fish.predefined.21": "Zebras<PERSON> flavescens", "entity.minecraft.tropical_fish.predefined.3": "Chaetodon", "entity.minecraft.tropical_fish.predefined.4": "Cichlida", "entity.minecraft.tropical_fish.predefined.5": "Amphiprion", "entity.minecraft.tropical_fish.predefined.6": "Betta splendens rosea", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromida", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>e", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "A<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Saliens", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "Coba", "entity.minecraft.tropical_fish.type.snooper": "Speculator", "entity.minecraft.tropical_fish.type.spotty": "Interstinctus", "entity.minecraft.tropical_fish.type.stripey": "<PERSON>can<PERSON> strigatus", "entity.minecraft.tropical_fish.type.sunstreak": "Solar<PERSON>", "entity.minecraft.turtle": "Testudo", "entity.minecraft.vex": "Vexator", "entity.minecraft.villager": "<PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON> tegminum", "entity.minecraft.villager.butcher": "<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Cartographus", "entity.minecraft.villager.cleric": "Sa<PERSON><PERSON>", "entity.minecraft.villager.farmer": "Agricola", "entity.minecraft.villager.fisherman": "Piscator", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Operarius corii", "entity.minecraft.villager.librarian": "<PERSON>ib<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.mason": "Caementari<PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "<PERSON><PERSON>", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "Faber instrumentorum", "entity.minecraft.villager.weaponsmith": "Faber telorum", "entity.minecraft.vindicator": "Vindicator", "entity.minecraft.wandering_trader": "Me<PERSON>tor vagus", "entity.minecraft.warden": "Vigil", "entity.minecraft.wind_charge": "Missile ventosum", "entity.minecraft.witch": "Venefica", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON> wither", "entity.minecraft.wither_skull": "<PERSON><PERSON> wither", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Resurrectus", "entity.minecraft.zombie_horse": "Equus resurrectus", "entity.minecraft.zombie_villager": "<PERSON><PERSON> resurrectus", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> resurrectus", "entity.not_summonable": "Entitas generis %s creari non potest", "event.minecraft.raid": "Incursio", "event.minecraft.raid.defeat": "Strages", "event.minecraft.raid.defeat.full": "Incursio - Strages", "event.minecraft.raid.raiders_remaining": "Incursatores restantes: %s", "event.minecraft.raid.victory": "Victoria", "event.minecraft.raid.victory.full": "Incursio - Victoria", "filled_map.buried_treasure": "<PERSON><PERSON> the<PERSON>i defossi", "filled_map.explorer_jungle": "Charta exploratoris i<PERSON>e", "filled_map.explorer_swamp": "Charta exploratoris paludis", "filled_map.id": "Id #%s", "filled_map.level": "(Gradus %s ex %s)", "filled_map.locked": "<PERSON><PERSON>", "filled_map.mansion": "Charta exploratoris silvestris", "filled_map.monument": "Charta exploratoris maris", "filled_map.scale": "Magnitudo est 1:%s", "filled_map.trial_chambers": "Charta exploratoris periculorum", "filled_map.unknown": "Charta ignota", "filled_map.village_desert": "Charta vici in desertis", "filled_map.village_plains": "Charta vici in planitiebus", "filled_map.village_savanna": "Charta vici in savana", "filled_map.village_snowy": "Charta vici in niveis", "filled_map.village_taiga": "Charta vici in silva boriale", "flat_world_preset.minecraft.bottomless_pit": "Abyssus sine fundo", "flat_world_preset.minecraft.classic_flat": "Terra plana prior", "flat_world_preset.minecraft.desert": "Deserta", "flat_world_preset.minecraft.overworld": "Terra", "flat_world_preset.minecraft.redstone_ready": "Paratus pro redstone", "flat_world_preset.minecraft.snowy_kingdom": "Regnum niveum", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON><PERSON> fossoris", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON> maritimus", "flat_world_preset.unknown": "???", "gameMode.adventure": "Modus explorandi", "gameMode.changed": "Modus ludi tuus in %s mutatus est", "gameMode.creative": "<PERSON><PERSON> fingendi", "gameMode.hardcore": "Modus extremus!", "gameMode.spectator": "<PERSON><PERSON> spectatoris", "gameMode.survival": "Modus supervivendi", "gamerule.allowFireTicksAwayFromPlayer": "Propaga ignem a lusoribus", "gamerule.allowFireTicksAwayFromPlayer.description": "Num ignis et lava posse propagare ultra VIII partes ab ullo lusore debent", "gamerule.announceAdvancements": "Nuntia incrementa", "gamerule.blockExplosionDropDecay": "In displosione tagendi cubos, quidam cubi praedam suam non emittent", "gamerule.blockExplosionDropDecay.description": "Quaedam guttae e cubis destructis displosione tagendo cuborum amissae sunt in displosione.", "gamerule.category.chat": "Locutorium", "gamerule.category.drops": "Res demissae", "gamerule.category.misc": "Miscellaneus", "gamerule.category.mobs": "Mobilia", "gamerule.category.player": "Lu<PERSON>", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.updates": "Renovationes mundi", "gamerule.commandBlockOutput": "Mitte effectus cuborum iussorum", "gamerule.commandModificationBlockLimit": "Cubi qui iusso mutari possunt", "gamerule.commandModificationBlockLimit.description": "Quoteni cubi iussis (e. g. /fill et /clone) mutari possint.", "gamerule.disableElytraMovementCheck": "Noli quaerere velocitas volatus cum elytris", "gamerule.disablePlayerMovementCheck": "Noli quaerere celeritatem lusorum", "gamerule.disableRaids": "Noli generare incursiones", "gamerule.doDaylightCycle": "Sol progreditur", "gamerule.doEntityDrops": "Demitte res entitatum", "gamerule.doEntityDrops.description": "Res emittae e plaustris fodinae (et eorum inventariis), formis ferum, ratibus, et ceteribus", "gamerule.doFireTick": "Activa incendia", "gamerule.doImmediateRespawn": "Statim renascere", "gamerule.doInsomnia": "<PERSON>ra phantasma", "gamerule.doLimitedCrafting": "Quaere praecepta ut fabrices", "gamerule.doLimitedCrafting.description": "Si activus est, lusores solum praecepta reclusa fabricare poterunt.", "gamerule.doMobLoot": "Demitte res mobilium", "gamerule.doMobLoot.description": "Res emissas e mobilibus moderatur etiam orbes experientiae.", "gamerule.doMobSpawning": "Genera mobilia", "gamerule.doMobSpawning.description": "Aliqui entitates iures separatas fortasse habent.", "gamerule.doPatrolSpawning": "Genera manus praedatorum", "gamerule.doTileDrops": "Demitte cubos", "gamerule.doTileDrops.description": "Res emissas e cubis moderatur etiam orbes experientiae.", "gamerule.doTraderSpawning": "Genera mercatores vagi", "gamerule.doVinesSpread": "Vineae crescunt", "gamerule.doVinesSpread.description": "Vinaene cubus in cubos confines crescat. Cetera genera vinearum, ut vineas lacrimosas vel contortas, haec lex non afficit.", "gamerule.doWardenSpawning": "Genera vigiles", "gamerule.doWeatherCycle": "Tempestas commutatur", "gamerule.drowningDamage": "Inflige vulnera ob demersionem", "gamerule.enderPearlsVanishOnDeath": "Margaritae Ender i<PERSON>ae in morte evanescibunt", "gamerule.enderPearlsVanishOnDeath.description": "Num martagitae Ender iactae a lusore evanuerint cum ille moretur.", "gamerule.entitiesWithPassengersCanUsePortals": "Entitates cum vectoribus portas pertransire possunt", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permitte entitates cum vectoribus portas nether endque transitusque end transire.", "gamerule.fallDamage": "Inflige vulnera ob casum", "gamerule.fireDamage": "Inflige vulnera ob ignem", "gamerule.forgiveDeadPlayers": "Ignosce lusores mortuos", "gamerule.forgiveDeadPlayers.description": "Mobilia media irata, cum lusor, cui irati sint, in vicinitate moritur, irata esse desinunt.", "gamerule.freezeDamage": "Inflige vulnera ob gelum", "gamerule.globalSoundEvents": "Soni ab omnibus auditi", "gamerule.globalSoundEvents.description": "Cum eventus quidam ludi fiunt, sicut umbo creat, sonus ubique auditur.", "gamerule.keepInventory": "Serva inventarium post mortem", "gamerule.lavaSourceConversion": "Lava fons fit", "gamerule.lavaSourceConversion.description": "<PERSON>va fluens, circa quam duae lavae fontes sint, fons fit.", "gamerule.locatorBar": "Utere vecte indicandi lusoris", "gamerule.locatorBar.description": "Cum activus, vectis in simulacro ostenditur ut directionem lusorum indicet.", "gamerule.logAdminCommands": "<PERSON><PERSON> administratoris", "gamerule.maxCommandChainLength": "Finis magnitudinis catenarum i<PERSON>orum", "gamerule.maxCommandChainLength.description": "Adhibet mandatum clausurae vincula et functiones.", "gamerule.maxCommandForkCount": "Finis contextuum iussorum", "gamerule.maxCommandForkCount.description": "Maximus numerus contextuum quos iussi sicut 'execute as' uti possunt.", "gamerule.maxEntityCramming": "Finis congregationis entitatum", "gamerule.minecartMaxSpeed": "Velocitas maxima plaustri", "gamerule.minecartMaxSpeed.description": "Velocitas normalis maxima plaustri in terra moventis.", "gamerule.mobExplosionDropDecay": "In displosionibus a mobilibus, quidam cubi praedam suam non emittent", "gamerule.mobExplosionDropDecay.description": "Quaedam guttae e cubis destructis displosione a mobilibus amissae sunt in displosione.", "gamerule.mobGriefing": "Permitte eversionem a mobilibus", "gamerule.naturalRegeneration": "Regeneratio salutis", "gamerule.playersNetherPortalCreativeDelay": "<PERSON>ra lusori intrare in Nether per portam in modo fingendi", "gamerule.playersNetherPortalCreativeDelay.description": "Tempus (momenta) quod lusori fingenti morandum est in porta Nether antequam Nether intrat.", "gamerule.playersNetherPortalDefaultDelay": "<PERSON>ra lusori intrare in Nether per portam non in modo fingendi", "gamerule.playersNetherPortalDefaultDelay.description": "Tempus (momenta) quod lusori non fingenti morandum est in porta Nether antequam Nether intrat.", "gamerule.playersSleepingPercentage": "Pars dormiens lusorum", "gamerule.playersSleepingPercentage.description": "Centesimae partes lusorum cui dormiendum est ut noctem transiliant.", "gamerule.projectilesCanBreakBlocks": "<PERSON><PERSON><PERSON> cubos frangere possunt", "gamerule.projectilesCanBreakBlocks.description": "Num iacula cum ictibus cubos qui illis destrui possunt destruuntur.", "gamerule.randomTickSpeed": "Velocitas momentorum fortuitorum", "gamerule.reducedDebugInfo": "Minue indicem ad emendandum", "gamerule.reducedDebugInfo.description": "Res simulacri emendandi definit.", "gamerule.sendCommandFeedback": "Mitte explicationem iussorum", "gamerule.showDeathMessages": "<PERSON>tte nuntios mortis", "gamerule.snowAccumulationHeight": "<PERSON><PERSON><PERSON> <PERSON> nivis", "gamerule.snowAccumulationHeight.description": "Cum ningueret, coria ninguium humi formant usque hunc numerum coriorum.", "gamerule.spawnChunkRadius": "Radius partium circa loco creandi", "gamerule.spawnChunkRadius.description": "Numerus partium quae impositae permanent circa loco creandi in terra.", "gamerule.spawnRadius": "Amplitudo loci renascendi", "gamerule.spawnRadius.description": "Mutat spatium circa loco creandi in quo lusores creari potest.", "gamerule.spectatorsGenerateChunks": "Permitte spectatoribus ut terram generent", "gamerule.tntExplodes": "Dynamites permisi esse activae et explodere", "gamerule.tntExplosionDropDecay": "In displosionibus dynamitis, quidam cubi praedam suam non emittent", "gamerule.tntExplosionDropDecay.description": "Quaedam guttae e cubis destructis displosione dynamitis amissae sunt in displosione.", "gamerule.universalAnger": "Mobilia omnibus irascuntur", "gamerule.universalAnger.description": "Mobilia media irata non modo lusorem qui eos lacessiverit sed omnes lusores in vicinitate oppugnant. Melius operat si ius forgiveDeadPlayers inactivus est.", "gamerule.waterSourceConversion": "Aqua fons fit", "gamerule.waterSourceConversion.description": "Aqua fluens, circa quam duae aquae fontes sint, fons fit.", "generator.custom": "Proprii coloris", "generator.customized": "<PERSON><PERSON> derectus", "generator.minecraft.amplified": "AMPLIFICATUS", "generator.minecraft.amplified.info": "Animadverte: Modo pro ioco est! Computatrum potens requirit.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON><PERSON> emend<PERSON>i", "generator.minecraft.flat": "Planissimus", "generator.minecraft.large_biomes": "Biomata magna", "generator.minecraft.normal": "Normalis", "generator.minecraft.single_biome_surface": "Bioma unum", "generator.single_biome_caves": "Speluncae", "generator.single_biome_floating_islands": "Insulae in aere", "gui.abuseReport.attestation": "Relatione submissa, informationem a te comparatam veram et perfectam esse, ut scire potes, iuras.", "gui.abuseReport.comments": "Senten<PERSON><PERSON>", "gui.abuseReport.describe": "Partiri singula nos adiuvat ut erudite iudicemus.", "gui.abuseReport.discard.content": "Si cesseris, hanc relationem et sententias tuas amittes.\nEsne certus cedere velle?", "gui.abuseReport.discard.discard": "<PERSON>ull<PERSON> re relata exi", "gui.abuseReport.discard.draft": "Serva ut textus", "gui.abuseReport.discard.return": "<PERSON>ge mutare", "gui.abuseReport.discard.title": "Relinque relationem et sententias?", "gui.abuseReport.draft.content": "Visne continere mutare relationem quae adest vel eam abicere et creare novam?", "gui.abuseReport.draft.discard": "Abice", "gui.abuseReport.draft.edit": "<PERSON>ge mutare", "gui.abuseReport.draft.quittotitle.content": "Visne continere mutare vel abicere?", "gui.abuseReport.draft.quittotitle.title": "Tu habes relationem locutorii in exemplo quae amissura est si relinquas", "gui.abuseReport.draft.title": "Muta relationem locutorii in exemplo?", "gui.abuseReport.error.title": "Relatio mitti non potuit", "gui.abuseReport.message": "Ubi vidisti malos mores?\nHoc adiuvat nos in investigatione tuae causae.", "gui.abuseReport.more_comments": "Ea quae evenerunt describe:", "gui.abuseReport.name.comment_box_label": "Describe cur hoc nomen referre vis:", "gui.abuseReport.name.reporting": "Refers \"%s\".", "gui.abuseReport.name.title": "Refer nomen lusoris", "gui.abuseReport.observed_what": "Cur hoc refers?", "gui.abuseReport.read_info": "Quid sunt relationes?", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Temeta vel mala medicamenta", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Aliquis aliis suadet ut contra leges temetum bibant vel medicamenta emant, vendant, possideant vel adhibeant.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Abusus sexualis filiorum aut abusio", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Aliquis loquitur de moribus turpibus vel aliter promovendis in infantibus implicandis.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Infamatio vel mendacia ad alios decipiendos", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Aliquis ad alios fraudandos vel decipiendos aliquem infamat, alium ac se esse simulat vel mentitur.", "gui.abuseReport.reason.description": "Descriptio:", "gui.abuseReport.reason.false_reporting": "Relatio falsa", "gui.abuseReport.reason.generic": "Volo lusorem referre", "gui.abuseReport.reason.generic.description": "Me vexavit vel fecit quod mihi non placet.", "gui.abuseReport.reason.harassment_or_bullying": "Vexatio vel importunitas", "gui.abuseReport.reason.harassment_or_bullying.description": "Aliquis tibi vel aliis verbis vel vi incommodus est. Quod certum fit, cum te vel alium invisum saepe alloqui conetur vel res, quae at te vel alios pertineant, quas vulgare non licet, vulget.", "gui.abuseReport.reason.hate_speech": "Sermo odiosus", "gui.abuseReport.reason.hate_speech.description": "Aliquis te vel alium lusorem obtrectat ob eius religionem, nationem, sexum vel ob alias res a quibus is quis sit cognoscat.", "gui.abuseReport.reason.imminent_harm": "Minae alios vulnerandi", "gui.abuseReport.reason.imminent_harm.description": "Aliquis in vita vera vulneraturum te vel alios minatur.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imagines nudorum nolentium", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Aliquis de imaginibus nudorum quas vulgare non licet loquitur, has communicat vel eos qui has communicent iuvat.", "gui.abuseReport.reason.self_harm_or_suicide": "Minae sui vulnerandi vel sibi mortis consciscendae", "gui.abuseReport.reason.self_harm_or_suicide.description": "Aliquis vulneraturum se minatur vel de se in vita vera vulnerando loquitur.", "gui.abuseReport.reason.sexually_inappropriate": "Sexuale nec idoneum", "gui.abuseReport.reason.sexually_inappropriate.description": "Facies expressae de actibus, visceribus, aut violentiis sexualibus.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Tromocratiam vel vim opinione adhibere", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Aliquis de tromocratiam vel vim opinione sua adhibendo loquitur vel hoc suadet vel minatur, partis, religionis, ideologiae vel alia causa.", "gui.abuseReport.reason.title": "Causam referendi selige", "gui.abuseReport.report_sent_msg": "Relationem tuam recepimus. Tibi gratias agimus!\n\nModeratores nostri relationem tuam quam primum inspicient.", "gui.abuseReport.select_reason": "Causam referendi selige", "gui.abuseReport.send": "Relationem mitte", "gui.abuseReport.send.comment_too_long": "Breviorem fac sententiam", "gui.abuseReport.send.error_message": "Error evenit dum relatio mittitur:\n\"%s\"", "gui.abuseReport.send.generic_error": "Error inopinatus evenit dum relatio tua mittitur.", "gui.abuseReport.send.http_error": "Error protocolli transitionis hypertextus inopinatus evenit dum relatio tua mittitur.", "gui.abuseReport.send.json_error": "Bolum deformem evenit dum relatio tua mittitur.", "gui.abuseReport.send.no_reason": "Tibi causa referendi seligenda est", "gui.abuseReport.send.not_attested": "Lege verba supra scripta et imple quadratum ut relationem mittere possis", "gui.abuseReport.send.service_unavailable": "Relationes abusus pervenire non potest. Confirme ut interrete coniungaris et iterum conare.", "gui.abuseReport.sending.title": "Relatio mittitur...", "gui.abuseReport.sent.title": "Relatio missa est", "gui.abuseReport.skin.title": "Refer faciem lusoris", "gui.abuseReport.title": "<PERSON>fer lusorem", "gui.abuseReport.type.chat": "Nuntia locutorii", "gui.abuseReport.type.name": "Nomen lusoris", "gui.abuseReport.type.skin": "<PERSON>ac<PERSON> lusoris", "gui.acknowledge": "Confirmo", "gui.advancements": "Incrementa", "gui.all": "Omnes", "gui.back": "Retro", "gui.banned.description": "%s\n\n%s\n\nPlura disce in sequenti pagina: %s", "gui.banned.description.permanent": "Licentia tua perpetuo interdicta est; interretialem ludum ludere aut Realms iungere non potes.", "gui.banned.description.reason": "Nuper nos infamiam ad malos mores per rationem vestram recepimus. Moderatores nostri causam tuam nunc recenseverunt et eam ut %s identificarunt, quae contra signa Communitatis Minecraft.", "gui.banned.description.reason_id": "Codex: %s", "gui.banned.description.reason_id_message": "Codex: %s - %s", "gui.banned.description.temporary": "%s Usque, non potes ludere ludum interretialem vel Realms iungere.", "gui.banned.description.temporary.duration": "Licentia tua est pro tempore suspensa et reactiva erit post %s.", "gui.banned.description.unknownreason": "Nuper nos infamiam ad malos mores per rationem vestram recepimus. Moderatores nostri causam tuam nunc recensuerunt et identificaverunt se contra signa Communitatis Minecraft.", "gui.banned.name.description": "Nomen tibi praesente - \"%s\" - Normas Consortionis nostras violat. Licet tibi ludere ludum unius, sed nomen tibi mutandum est prae licet tibi ludere ludum interretialem.\n\nDisce plurem vel submitte tuae causae recensionem in nexo: %s", "gui.banned.name.title": "Nomen non licet in ludo multorum", "gui.banned.reason.defamation_impersonation_false_information": "Agere partes vel informationem partiri ut alii vel usi vel decepti sint", "gui.banned.reason.drugs": "Rationes mediamentorum illicitorum", "gui.banned.reason.extreme_violence_or_gore": "Imagines violentiae vel cruoris sicut in vita vera", "gui.banned.reason.false_reporting": "Nimiae renuntiationes falsae vel fictae", "gui.banned.reason.fraud": "Fraudulenta comparatio vel usus rerum", "gui.banned.reason.generic_violation": "<PERSON>tio normarum consortionis", "gui.banned.reason.harassment_or_bullying": "Lingua maledica usa directe maleque", "gui.banned.reason.hate_speech": "Sermo odiosus vel discrimen", "gui.banned.reason.hate_terrorism_notorious_figure": "Rationes globorum odiosorum, ordinationum terrorum, vel hominum famosorum", "gui.banned.reason.imminent_harm_to_person_or_property": "Consilium ut hominibus rebusve in vita vera nocere", "gui.banned.reason.nudity_or_pornography": "Ostentatio rerum obscenarum vel impudicarum", "gui.banned.reason.sexually_inappropriate": "Res sexuales", "gui.banned.reason.spam_or_advertising": "Quisquiliae vel renunciatio", "gui.banned.skin.description": "Facies tua praesentis Normas Consortionis nostras violat. Licet tibi ludere cum facie normale, vel selige novam.\n\nDisce plurem vel submitte tuae causae recensionem in nexo: %s", "gui.banned.skin.title": "Facies illicita est", "gui.banned.title.permanent": "Ideo interdictum in perpetuum", "gui.banned.title.temporary": "Licentia pro tempore suspensa", "gui.cancel": "Oblittera", "gui.chatReport.comments": "Senten<PERSON><PERSON>", "gui.chatReport.describe": "Si accurate cum nobis ea quae evenerunt communicabis, rectius deliberare poterimus.", "gui.chatReport.discard.content": "Si cesseris, hanc relationem et sententias tuas amittes.\nEsne certus cedere velle?", "gui.chatReport.discard.discard": "<PERSON>ull<PERSON> re relata exi", "gui.chatReport.discard.draft": "Serva ut textus", "gui.chatReport.discard.return": "<PERSON>ge mutare", "gui.chatReport.discard.title": "Relinque relationem et sententias?", "gui.chatReport.draft.content": "Visne continere mutatio textus praesentialis vel abiicias et creare novum?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "<PERSON>ge mutare", "gui.chatReport.draft.quittotitle.content": "Visne continere mutare vel abicere?", "gui.chatReport.draft.quittotitle.title": "Tu habes relationem locutorii in exemplo quae amissura est si relinquas", "gui.chatReport.draft.title": "Muta textus sermocinationem fama?", "gui.chatReport.more_comments": "Ea quae evenerunt describe:", "gui.chatReport.observed_what": "<PERSON><PERSON> ha<PERSON> refers?", "gui.chatReport.read_info": "Quid sunt relationes?", "gui.chatReport.report_sent_msg": "Relationem tuam recepimus. Tibi gratias agimus!\n\nModeratores nostri relationem tuam quam primum inspicient.", "gui.chatReport.select_chat": "E locutorio nuntia quae referre vis selige", "gui.chatReport.select_reason": "Causam referendi selige", "gui.chatReport.selected_chat": "E locutorio nuntia %s de quibus referre vis selecta sunt", "gui.chatReport.send": "Relationem mitte", "gui.chatReport.send.comments_too_long": "Fac breviorem sententiam", "gui.chatReport.send.no_reason": "Tibi causa referendi seligenda est", "gui.chatReport.send.no_reported_messages": "Tibi minime unum nuntium quod referre vis e locutorio seligendum est", "gui.chatReport.send.too_many_messages": "Nimia nuntia referre vis", "gui.chatReport.title": "Refer nuntia lusoris", "gui.chatSelection.context": "Nuntia quae hanc electionem circumdant includentur ut contextus novos praebeant", "gui.chatSelection.fold": "%s nuntia celantur", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s locutorium iniit", "gui.chatSelection.message.narrate": "%s dixit: %s hora %s", "gui.chatSelection.selected": "%s nuntia selegisti (maxime %s seligere potes)", "gui.chatSelection.title": "E locutorio nuntia quae referre vis selige", "gui.continue": "Perge", "gui.copy_link_to_clipboard": "Copia nexum ad latibulum", "gui.days": "%s dies", "gui.done": "Serva exique", "gui.down": "Deorsum", "gui.entity_tooltip.type": "Genus: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Scapa %s reiecta sunt", "gui.fileDropFailure.title": "Scapa addere non potuit", "gui.hours": "%s horae", "gui.loadingMinecraft": "Minecraft construitur", "gui.minutes": "%s minutae", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Premendus %s", "gui.narrate.editBox": "Quadrum ad scribendum %s: %s", "gui.narrate.slider": "Regulator %s", "gui.narrate.tab": "%s pars", "gui.no": "Non", "gui.none": "<PERSON><PERSON><PERSON>", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Aperi indicem relationum", "gui.proceed": "Procede", "gui.recipebook.moreRecipes": "Ice dextrum ut reliqua videas", "gui.recipebook.page": "%s ex %s", "gui.recipebook.search_hint": "Quaere...", "gui.recipebook.toggleRecipes.all": "Omnes monstrantur", "gui.recipebook.toggleRecipes.blastable": "Conflabiles monstratur", "gui.recipebook.toggleRecipes.craftable": "Fabricabiles monstrantur", "gui.recipebook.toggleRecipes.smeltable": "Decoctabiles monstrantur", "gui.recipebook.toggleRecipes.smokable": "Fumigabiles monstrantur", "gui.report_to_server": "Refer ad moderatrum", "gui.socialInteractions.blocking_hint": "Administra licentia Microsoft", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON><PERSON> lusori nuntii abstinetur", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON><PERSON> lusoris nuntii celantur", "gui.socialInteractions.hidden_in_chat": "Nuntia de %s celabuntur", "gui.socialInteractions.hide": "Cela in locutorio", "gui.socialInteractions.narration.hide": "Cela nuntia de %s", "gui.socialInteractions.narration.report": "Refer nuntia lusoris %s", "gui.socialInteractions.narration.show": "Ostende nuntia de %s", "gui.socialInteractions.report": "<PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON> lusor illius nominis inveniri non potuit", "gui.socialInteractions.search_hint": "Quaere...", "gui.socialInteractions.server_label.multiple": "%s - %s lusores", "gui.socialInteractions.server_label.single": "%s - %s lusor", "gui.socialInteractions.show": "Ostende in locutorio", "gui.socialInteractions.shown_in_chat": "Nuntia de %s ostendetur", "gui.socialInteractions.status_blocked": "A<PERSON>ten<PERSON>", "gui.socialInteractions.status_blocked_offline": "Abstentus - non conexus", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Celatus - non conexus", "gui.socialInteractions.status_offline": "Non conexus", "gui.socialInteractions.tab_all": "Omnes", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Optiones sociorum", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON> nuntia", "gui.socialInteractions.tooltip.report": "Refer nuntia lusoris", "gui.socialInteractions.tooltip.report.disabled": "Relationes accipere non possumus", "gui.socialInteractions.tooltip.report.no_messages": "Nullum nuntium a lusore %s missum referri potest", "gui.socialInteractions.tooltip.report.not_reportable": "Nuntia huius lusoris referri non possunt quia eius nuntia in hoc moderatro certa non sunt", "gui.socialInteractions.tooltip.show": "Ostende nuntia", "gui.stats": "Statisticae", "gui.toMenu": "Redire ad indicem moderatrorum", "gui.toRealms": "Redi ad indicem Realms", "gui.toTitle": "Principale ad simulacrum redire", "gui.toWorld": "Redi ad indicem mundorum", "gui.togglable_slot": "Ice ut spacium inactivum sit", "gui.up": "Sursum", "gui.waitingForResponse.button.inactive": "Redi (%ss)", "gui.waitingForResponse.title": "Moderatrum exspectas", "gui.yes": "<PERSON>a", "hanging_sign.edit": "Inscribe in titulo pendente", "instrument.minecraft.admire_goat_horn": "Admiratio", "instrument.minecraft.call_goat_horn": "Clamor", "instrument.minecraft.dream_goat_horn": "Somnium", "instrument.minecraft.feel_goat_horn": "Sensus", "instrument.minecraft.ponder_goat_horn": "Meditatio", "instrument.minecraft.seek_goat_horn": "Scrutinium", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Desiderium", "inventory.binSlot": "<PERSON><PERSON> dele", "inventory.hotbarInfo": "Potes servare res vectum cum %1$s+%2$s", "inventory.hotbarSaved": "Res vectis servata est (potes reponere con %1$s+%2$s)", "item.canBreak": "Destruere potest:", "item.canPlace": "Poni potest super:", "item.canUse.unknown": "<PERSON><PERSON><PERSON>", "item.color": "Color: %s", "item.components": "%s partes", "item.disabled": "Res qua uti non potest", "item.durability": "Durabilitas: %s / %s", "item.dyed": "Tinctum", "item.minecraft.acacia_boat": "Ratis acaciae", "item.minecraft.acacia_chest_boat": "Ratis acaciae cum cista", "item.minecraft.allay_spawn_egg": "Ovum creationis relevatoris", "item.minecraft.amethyst_shard": "Fragmentum amethystinum", "item.minecraft.angler_pottery_shard": "Testa cum harundine picta", "item.minecraft.angler_pottery_sherd": "Testa cum harundine picta", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Testa cum arco picto", "item.minecraft.archer_pottery_sherd": "Testa cum arco picto", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON> dasypo<PERSON>", "item.minecraft.armadillo_spawn_egg": "Ovum creationis dasypodis", "item.minecraft.armor_stand": "Statumen armorum", "item.minecraft.arms_up_pottery_shard": "Testa cum brachiis pictis", "item.minecraft.arms_up_pottery_sherd": "Testa cum brachiis pictis", "item.minecraft.arrow": "Sagitta", "item.minecraft.axolotl_bucket": "Hama cum salamandra mexicana", "item.minecraft.axolotl_spawn_egg": "Ovum creationis salamandrae mexicanae", "item.minecraft.baked_potato": "Patata cocta", "item.minecraft.bamboo_chest_raft": "Ratis harundinum Indicarum cum cista", "item.minecraft.bamboo_raft": "Ratis harundinum Indicarum", "item.minecraft.bat_spawn_egg": "Ovum creationis vespertilionis", "item.minecraft.bee_spawn_egg": "Ovum creationis apis", "item.minecraft.beef": "<PERSON><PERSON><PERSON> cruda", "item.minecraft.beetroot": "Beta", "item.minecraft.beetroot_seeds": "Semina betaceae", "item.minecraft.beetroot_soup": "Ius betaceum", "item.minecraft.birch_boat": "<PERSON><PERSON> betullae", "item.minecraft.birch_chest_boat": "Ratis betullae cum cista", "item.minecraft.black_bundle": "Sarcina nigra", "item.minecraft.black_dye": "Tinctura nigra", "item.minecraft.black_harness": "Capistrum nigrum", "item.minecraft.blade_pottery_shard": "Testa cum ense picto", "item.minecraft.blade_pottery_sherd": "Testa cum ense picto", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON> flammifer", "item.minecraft.blaze_rod": "Virga flammifera", "item.minecraft.blaze_spawn_egg": "Ovum creationis flammiferi", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON> caerulea", "item.minecraft.blue_dye": "Tinctura caerulea", "item.minecraft.blue_egg": "Ovum lividum", "item.minecraft.blue_harness": "Capistrum caeruleum", "item.minecraft.bogged_spawn_egg": "Ovum creationis paludivagi", "item.minecraft.bolt_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ornamentum tegminis cum clavis", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON>ssa molita", "item.minecraft.book": "Liber", "item.minecraft.bordure_indented_banner_pattern": "Finitio curvum exemplar vexilli", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON>", "item.minecraft.breeze_rod": "Virga ventifera", "item.minecraft.breeze_spawn_egg": "Ovum creationis ventiferi", "item.minecraft.brewer_pottery_shard": "Testa cum potione picta", "item.minecraft.brewer_pottery_sherd": "Testa cum potione picta", "item.minecraft.brewing_stand": "Mixtarius", "item.minecraft.brick": "Later", "item.minecraft.brown_bundle": "Sa<PERSON><PERSON> brunnea", "item.minecraft.brown_dye": "Tinctura brunnea", "item.minecraft.brown_egg": "Ovum brunneum", "item.minecraft.brown_harness": "Capistrum brunneum", "item.minecraft.brush": "Pen<PERSON>ulus", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "Vacua", "item.minecraft.bundle.empty.description": "Continet acervum rerum multorum generum", "item.minecraft.bundle.full": "<PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s ex %s", "item.minecraft.burn_pottery_shard": "Testa cum igni picto", "item.minecraft.burn_pottery_sherd": "Testa cum igni picto", "item.minecraft.camel_spawn_egg": "Ovum creationis cameli", "item.minecraft.carrot": "Carota", "item.minecraft.carrot_on_a_stick": "Carota in virga", "item.minecraft.cat_spawn_egg": "Ovum creationis feles", "item.minecraft.cauldron": "Cortina", "item.minecraft.cave_spider_spawn_egg": "Ovum creationis araneae cavernae", "item.minecraft.chainmail_boots": "Caligae reticuli ferrei", "item.minecraft.chainmail_chestplate": "Lorica reticuli ferrei", "item.minecraft.chainmail_helmet": "Galea reticuli ferrei", "item.minecraft.chainmail_leggings": "Ocreae reticuli ferrei", "item.minecraft.charcoal": "Carbo ex ligno", "item.minecraft.cherry_boat": "<PERSON><PERSON> cera<PERSON>", "item.minecraft.cherry_chest_boat": "Ratis cerasi cum cista", "item.minecraft.chest_minecart": "Plaustrum fodinae cum cista", "item.minecraft.chicken": "<PERSON><PERSON> gallina<PERSON> cruda", "item.minecraft.chicken_spawn_egg": "Ovum creationis pulli", "item.minecraft.chorus_fruit": "<PERSON><PERSON> chori", "item.minecraft.clay_ball": "<PERSON><PERSON> argillae", "item.minecraft.clock": "Horologium", "item.minecraft.coal": "Carbo", "item.minecraft.coast_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.coast_armor_trim_smithing_template.new": "Ornamentum tegminis litorale", "item.minecraft.cocoa_beans": "Fabae theobromatis", "item.minecraft.cod": "<PERSON><PERSON><PERSON> cruda", "item.minecraft.cod_bucket": "Hama cum morua", "item.minecraft.cod_spawn_egg": "Ovum creationis moruae", "item.minecraft.command_block_minecart": "Plaustrum fodinae cum cubo iussorum", "item.minecraft.compass": "Acus magnetica", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON> cocta", "item.minecraft.cooked_chicken": "<PERSON>o gallinacea cocta", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON> cocta", "item.minecraft.cooked_mutton": "A<PERSON>na cocta", "item.minecraft.cooked_porkchop": "<PERSON><PERSON> cocta", "item.minecraft.cooked_rabbit": "Caro cuniculi cocta", "item.minecraft.cooked_salmon": "Salmo coctus", "item.minecraft.cookie": "Crustulum", "item.minecraft.copper_ingot": "Later cupreus", "item.minecraft.cow_spawn_egg": "Ovum creationis vaccae", "item.minecraft.creaking_spawn_egg": "Ovum creationis crepacis", "item.minecraft.creeper_banner_pattern": "Exemplar vexilli", "item.minecraft.creeper_banner_pattern.desc": "Imago creeper", "item.minecraft.creeper_banner_pattern.new": "Imago creeper exemplar vexilli", "item.minecraft.creeper_spawn_egg": "Ovum creationis creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Iaculum:", "item.minecraft.crossbow.projectile.multiple": "Proiecta: %s x %s", "item.minecraft.crossbow.projectile.single": "Proiectus: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>ina cyanea", "item.minecraft.cyan_dye": "Tinctura cyanea", "item.minecraft.cyan_harness": "Capistrum cyaneum", "item.minecraft.danger_pottery_shard": "Testa cum periculo picto", "item.minecraft.danger_pottery_sherd": "Testa cum periculo picto", "item.minecraft.dark_oak_boat": "<PERSON>is quercus nigrae", "item.minecraft.dark_oak_chest_boat": "Ratis quercus nigrae cum cista", "item.minecraft.debug_stick": "<PERSON><PERSON><PERSON> emendandi", "item.minecraft.debug_stick.empty": "%s nulla adiuncta habet", "item.minecraft.debug_stick.select": "\"%s\" selectus est (%s)", "item.minecraft.debug_stick.update": "\"%s\" ad %s", "item.minecraft.diamond": "<PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON> adamantina", "item.minecraft.diamond_boots": "Caligae adamantinae", "item.minecraft.diamond_chestplate": "Lorica adamantina", "item.minecraft.diamond_helmet": "<PERSON><PERSON> adamantina", "item.minecraft.diamond_hoe": "Sarculum adamantinum", "item.minecraft.diamond_horse_armor": "Tegimen equinum adamantinum", "item.minecraft.diamond_leggings": "Ocreae adamantinae", "item.minecraft.diamond_pickaxe": "Do<PERSON>bra adamantina", "item.minecraft.diamond_shovel": "<PERSON><PERSON> adaman<PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON> adamantinus", "item.minecraft.disc_fragment_5": "Fragmentum phonodisci", "item.minecraft.disc_fragment_5.desc": "Phonodiscus - 5", "item.minecraft.dolphin_spawn_egg": "Ovum creationis delphini", "item.minecraft.donkey_spawn_egg": "Ovum creationis asini", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON> draconis", "item.minecraft.dried_kelp": "Alga sicca", "item.minecraft.drowned_spawn_egg": "Ovum creationis mersi", "item.minecraft.dune_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.dune_armor_trim_smithing_template.new": "Ornamentum tegminis deserticum", "item.minecraft.echo_shard": "Fragmentum resonans", "item.minecraft.egg": "Ovum", "item.minecraft.elder_guardian_spawn_egg": "Ovum creationis custodis postnati", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Liber incantatus", "item.minecraft.enchanted_golden_apple": "Malum aureum incantatum", "item.minecraft.end_crystal": "Crystallum End", "item.minecraft.ender_dragon_spawn_egg": "Ovum creationi draconis <PERSON>", "item.minecraft.ender_eye": "Ender oculus", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Ovum creationis enderman", "item.minecraft.endermite_spawn_egg": "Ovum creationis endermite", "item.minecraft.evoker_spawn_egg": "Ovum creationis evocatoris", "item.minecraft.experience_bottle": "<PERSON><PERSON>", "item.minecraft.explorer_pottery_shard": "Testa cum itinere picto", "item.minecraft.explorer_pottery_sherd": "Testa cum itinere picto", "item.minecraft.eye_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.eye_armor_trim_smithing_template.new": "Ornamentum tegminis cum oculo", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "O<PERSON> araneae fermentatus", "item.minecraft.field_masoned_banner_pattern": "Ager testaceus exemplar vexilli", "item.minecraft.filled_map": "Charta", "item.minecraft.fire_charge": "Missile igneum", "item.minecraft.firework_rocket": "Missile pyrotechnicum", "item.minecraft.firework_rocket.flight": "Tempus volatus:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "<PERSON><PERSON><PERSON><PERSON> pyrotechnicus", "item.minecraft.firework_star.black": "Nigra", "item.minecraft.firework_star.blue": "C<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Proprii coloris", "item.minecraft.firework_star.cyan": "Cyanea", "item.minecraft.firework_star.fade_to": "Quae fit", "item.minecraft.firework_star.flicker": "Corusca", "item.minecraft.firework_star.gray": "Cinerea", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Aeria", "item.minecraft.firework_star.light_gray": "Can<PERSON>", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "Rubropurpurea", "item.minecraft.firework_star.orange": "Aurant<PERSON>", "item.minecraft.firework_star.pink": "<PERSON><PERSON>", "item.minecraft.firework_star.purple": "Purpurea", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Figura ignota", "item.minecraft.firework_star.shape.burst": "Eruptio", "item.minecraft.firework_star.shape.creeper": "Forma creeper", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.firework_star.shape.small_ball": "Pila minor", "item.minecraft.firework_star.shape.star": "Forma stellae", "item.minecraft.firework_star.trail": "Cum sulco", "item.minecraft.firework_star.white": "Alba", "item.minecraft.firework_star.yellow": "Flava", "item.minecraft.fishing_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.flint": "Silex", "item.minecraft.flint_and_steel": "Ignitabulum", "item.minecraft.flow_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.flow_armor_trim_smithing_template.new": "Ornamentum tegminis cum fluxu", "item.minecraft.flow_banner_pattern": "Exemplar vexilli", "item.minecraft.flow_banner_pattern.desc": "Fluxus", "item.minecraft.flow_banner_pattern.new": "Fluxus exemplar vexilli", "item.minecraft.flow_pottery_sherd": "Testa cum fluxo picta", "item.minecraft.flower_banner_pattern": "Exemplar vexilli", "item.minecraft.flower_banner_pattern.desc": "Imago floris", "item.minecraft.flower_banner_pattern.new": "Imago floris exemplar vexilli", "item.minecraft.flower_pot": "Vas", "item.minecraft.fox_spawn_egg": "Ovum creationis vulpis", "item.minecraft.friend_pottery_shard": "Testa cum amico picto", "item.minecraft.friend_pottery_sherd": "Testa cum amico picto", "item.minecraft.frog_spawn_egg": "Ovum creationis ranae", "item.minecraft.furnace_minecart": "Plaustrum fodinae cum fornace", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON>m creationis ghast", "item.minecraft.ghast_tear": "<PERSON><PERSON> ghast", "item.minecraft.glass_bottle": "<PERSON><PERSON> vitrei", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON>io peponis fulgens", "item.minecraft.globe_banner_pattern": "Exemplar vexilli", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Globus exemplar vexilli", "item.minecraft.glow_berries": "Bacae lucidae", "item.minecraft.glow_ink_sac": "Saccus atramenti lucidi", "item.minecraft.glow_item_frame": "Forma reris lucida", "item.minecraft.glow_squid_spawn_egg": "Ovum creationis lolliginis lucidae", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON> lucilapideus", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>rae", "item.minecraft.goat_spawn_egg": "Ovum creationis caprae", "item.minecraft.gold_ingot": "Later aurea", "item.minecraft.gold_nugget": "Massa aurea", "item.minecraft.golden_apple": "Malum aureum", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON> aurea", "item.minecraft.golden_boots": "Caligae aureae", "item.minecraft.golden_carrot": "Carota aurea", "item.minecraft.golden_chestplate": "Lorica aurea", "item.minecraft.golden_helmet": "Galea aurea", "item.minecraft.golden_hoe": "Sarculum aureum", "item.minecraft.golden_horse_armor": "Tegimen equinum aureum", "item.minecraft.golden_leggings": "Ocreae aureae", "item.minecraft.golden_pickaxe": "Dolabra aurea", "item.minecraft.golden_shovel": "Pala aurea", "item.minecraft.golden_sword": "<PERSON><PERSON> aureus", "item.minecraft.gray_bundle": "Sarcina cinerea", "item.minecraft.gray_dye": "Tinctura cinerea", "item.minecraft.gray_harness": "Capistrum cinereum", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON> viridis", "item.minecraft.green_dye": "Tinctura viridis", "item.minecraft.green_harness": "Capistrum viride", "item.minecraft.guardian_spawn_egg": "Ovum creationis custodis", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON> pyrius", "item.minecraft.guster_banner_pattern": "Exemplar vexilli", "item.minecraft.guster_banner_pattern.desc": "Ventifer", "item.minecraft.guster_banner_pattern.new": "Ventifer exemplar vexilli", "item.minecraft.guster_pottery_sherd": "Testa cum ventifero picto", "item.minecraft.happy_ghast_spawn_egg": "Ovum creationis ghast laeti", "item.minecraft.harness": "Capistrum", "item.minecraft.heart_of_the_sea": "Cor maris", "item.minecraft.heart_pottery_shard": "Testa cum corde picto", "item.minecraft.heart_pottery_sherd": "Testa cum corde picto", "item.minecraft.heartbreak_pottery_shard": "Testa cum corde fracto picto", "item.minecraft.heartbreak_pottery_sherd": "Testa cum corde fracto picto", "item.minecraft.hoglin_spawn_egg": "Ovum creationis hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON>", "item.minecraft.hopper_minecart": "Plaustrum fodinae cum infundibulo", "item.minecraft.horse_spawn_egg": "Ovum creationis equi", "item.minecraft.host_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.host_armor_trim_smithing_template.new": "Ornamentum tegminis hospitis", "item.minecraft.howl_pottery_shard": "Testa cum lupa picta", "item.minecraft.howl_pottery_sherd": "Testa cum lupa picta", "item.minecraft.husk_spawn_egg": "Ovum creationis cassi", "item.minecraft.ink_sac": "Saccus atramenti", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON> ferrea", "item.minecraft.iron_boots": "Caligae ferreae", "item.minecraft.iron_chestplate": "Lorica ferrea", "item.minecraft.iron_golem_spawn_egg": "Ovum creationis automati ferrei", "item.minecraft.iron_helmet": "Galea ferrea", "item.minecraft.iron_hoe": "Sarc<PERSON>rreum", "item.minecraft.iron_horse_armor": "Tegimen equinum ferreum", "item.minecraft.iron_ingot": "Later ferreus", "item.minecraft.iron_leggings": "Ocreae ferreae", "item.minecraft.iron_nugget": "Massa ferrea", "item.minecraft.iron_pickaxe": "Dolabra ferrea", "item.minecraft.iron_shovel": "<PERSON>la ferrea", "item.minecraft.iron_sword": "<PERSON><PERSON> ferreus", "item.minecraft.item_frame": "Forma rerum", "item.minecraft.jungle_boat": "<PERSON>is theobromatis", "item.minecraft.jungle_chest_boat": "Ratis theobromatis cum cista", "item.minecraft.knowledge_book": "Liber scientiae", "item.minecraft.lapis_lazuli": "<PERSON><PERSON> lazuli", "item.minecraft.lava_bucket": "<PERSON>a lavae", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "Corium", "item.minecraft.leather_boots": "Calcei scortei", "item.minecraft.leather_chestplate": "Tunica scortea", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON> scorteus", "item.minecraft.leather_horse_armor": "Tegimen equinum scorteum", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON> scorteae", "item.minecraft.light_blue_bundle": "Sarcina aeria", "item.minecraft.light_blue_dye": "Tinctura aeria", "item.minecraft.light_blue_harness": "Capistrum aerium", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON> cana", "item.minecraft.light_gray_dye": "Tinctura cana", "item.minecraft.light_gray_harness": "Capistrum canum", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON> prasina", "item.minecraft.lime_dye": "Tinctura prasina", "item.minecraft.lime_harness": "Capistrum prasinum", "item.minecraft.lingering_potion": "<PERSON>tio commorans", "item.minecraft.lingering_potion.effect.awkward": "<PERSON>tio inconcinna commorans", "item.minecraft.lingering_potion.effect.empty": "Potio infabricabilis commorans", "item.minecraft.lingering_potion.effect.fire_resistance": "Potio resistentiae igni commorans", "item.minecraft.lingering_potion.effect.harming": "Potio calamitatis commorans", "item.minecraft.lingering_potion.effect.healing": "Potio curationis commorans", "item.minecraft.lingering_potion.effect.infested": "Potio infestationis commorans", "item.minecraft.lingering_potion.effect.invisibility": "Potio invisibilitatis commorans", "item.minecraft.lingering_potion.effect.leaping": "Potio saltantis commorans", "item.minecraft.lingering_potion.effect.levitation": "Potio evectionis commorans", "item.minecraft.lingering_potion.effect.luck": "Potio fortunae commorans", "item.minecraft.lingering_potion.effect.mundane": "Potio mundana commorans", "item.minecraft.lingering_potion.effect.night_vision": "Potio visionis nocturnae commorans", "item.minecraft.lingering_potion.effect.oozing": "<PERSON>tio manationis commorans", "item.minecraft.lingering_potion.effect.poison": "Potio veneni commorans", "item.minecraft.lingering_potion.effect.regeneration": "Potio regenerationis commorans", "item.minecraft.lingering_potion.effect.slow_falling": "Potio casus lenti commorans", "item.minecraft.lingering_potion.effect.slowness": "Potio tarditatis commorans", "item.minecraft.lingering_potion.effect.strength": "Potio fortitudinis commorans", "item.minecraft.lingering_potion.effect.swiftness": "Potio velocitatis commorans", "item.minecraft.lingering_potion.effect.thick": "Potio crassa commorans", "item.minecraft.lingering_potion.effect.turtle_master": "Potio magistri testudinis commorans", "item.minecraft.lingering_potion.effect.water": "Uter aquae commorans", "item.minecraft.lingering_potion.effect.water_breathing": "Potio respirationis in aqua commorans", "item.minecraft.lingering_potion.effect.weakness": "Potio infirmitatis commorans", "item.minecraft.lingering_potion.effect.weaving": "Potio contextus commorans", "item.minecraft.lingering_potion.effect.wind_charged": "Potio inflationis commorans", "item.minecraft.llama_spawn_egg": "Ovum creationis lamae", "item.minecraft.lodestone_compass": "Acus magnetica vi magnetica imbuta", "item.minecraft.mace": "Clava ferrea", "item.minecraft.magenta_bundle": "Sarcina rubropurpurea", "item.minecraft.magenta_dye": "Tinctura rubropurpurea", "item.minecraft.magenta_harness": "Capistrum rubropurpureum", "item.minecraft.magma_cream": "Massa magmatis", "item.minecraft.magma_cube_spawn_egg": "Ovum creationis cubi magmatis", "item.minecraft.mangrove_boat": "<PERSON><PERSON> manglis", "item.minecraft.mangrove_chest_boat": "Ratis manglis cum cista", "item.minecraft.map": "<PERSON><PERSON> inanis", "item.minecraft.melon_seeds": "<PERSON><PERSON> peponis", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON> peponis", "item.minecraft.milk_bucket": "Hama lactis", "item.minecraft.minecart": "Plaustrum fodinae", "item.minecraft.miner_pottery_shard": "Testa cum dolabra picta", "item.minecraft.miner_pottery_sherd": "Testa cum dolabra picta", "item.minecraft.mojang_banner_pattern": "Exemplar vexilli", "item.minecraft.mojang_banner_pattern.desc": "Res", "item.minecraft.mojang_banner_pattern.new": "Res exemplar vexilli", "item.minecraft.mooshroom_spawn_egg": "O<PERSON>m <PERSON>is mooshroom", "item.minecraft.mourner_pottery_shard": "Testa cum luctu picto", "item.minecraft.mourner_pottery_sherd": "Testa cum luctu picto", "item.minecraft.mule_spawn_egg": "Ovum creationis muli", "item.minecraft.mushroom_stew": "Ius funginum", "item.minecraft.music_disc_11": "Phonodiscus", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Phonodiscus", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Phonodiscus", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Phonodiscus", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Phonodiscus", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Phonodiscus", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Phonodiscus", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Phonodiscus", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (campanulae)", "item.minecraft.music_disc_far": "Phonodiscus", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Phonodiscus", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Phonodiscus", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Phonodiscus", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Phonodiscus", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Phonodiscus", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Phonodiscus", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Phonodiscus", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Phonodiscus", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Phonodiscus", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Phonodiscus", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Phonodiscus", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Phonodiscus", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON> c<PERSON>a", "item.minecraft.name_tag": "Nota nominis", "item.minecraft.nautilus_shell": "<PERSON><PERSON> nautili", "item.minecraft.nether_brick": "Later <PERSON><PERSON>", "item.minecraft.nether_star": "<PERSON>", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_boots": "Caligae Netheritae", "item.minecraft.netherite_chestplate": "Lorica Netheritae", "item.minecraft.netherite_helmet": "Galea Netheritae", "item.minecraft.netherite_hoe": "Sarculum Netheritae", "item.minecraft.netherite_ingot": "Later Netheritae", "item.minecraft.netherite_leggings": "Ocreae Netheritae", "item.minecraft.netherite_pickaxe": "Dolabra Netheritae", "item.minecraft.netherite_scrap": "Frustum Netheritae", "item.minecraft.netherite_shovel": "<PERSON><PERSON>", "item.minecraft.netherite_sword": "Gladius Netheritae", "item.minecraft.netherite_upgrade_smithing_template": "Forma fabrilis", "item.minecraft.netherite_upgrade_smithing_template.new": "Solidamentum ex Netherite", "item.minecraft.oak_boat": "<PERSON><PERSON> quercea", "item.minecraft.oak_chest_boat": "Ratis quercea cum cista", "item.minecraft.ocelot_spawn_egg": "Ovum creationis pardi", "item.minecraft.ominous_bottle": "<PERSON><PERSON> praesagus", "item.minecraft.ominous_trial_key": "Clavis periculorum praesaga", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> aurantia", "item.minecraft.orange_dye": "Tinctura aurantia", "item.minecraft.orange_harness": "Capistrum aurantium", "item.minecraft.painting": "Pictura", "item.minecraft.pale_oak_boat": "Ratis quercus pallidae", "item.minecraft.pale_oak_chest_boat": "Ratis quercus pallidae cum cista", "item.minecraft.panda_spawn_egg": "Ovum creationis pandae", "item.minecraft.paper": "Papyrus", "item.minecraft.parrot_spawn_egg": "Ovum creationis psittaci", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON> phantasmatis", "item.minecraft.phantom_spawn_egg": "Ovum creationis phantasmatis", "item.minecraft.pig_spawn_egg": "Ovum creationis suis", "item.minecraft.piglin_banner_pattern": "Exemplar vexilli", "item.minecraft.piglin_banner_pattern.desc": "Rostrum", "item.minecraft.piglin_banner_pattern.new": "Rostrum exemplar vexilli", "item.minecraft.piglin_brute_spawn_egg": "Ovum creationis piglin feri", "item.minecraft.piglin_spawn_egg": "Ovum creationis piglin", "item.minecraft.pillager_spawn_egg": "Ovum creationis praedatoris", "item.minecraft.pink_bundle": "Sa<PERSON>ina rosea", "item.minecraft.pink_dye": "Tinctura rosea", "item.minecraft.pink_harness": "Capistrum roseum", "item.minecraft.pitcher_plant": "Nepenthes", "item.minecraft.pitcher_pod": "Nepenthis siliqua", "item.minecraft.plenty_pottery_shard": "Testa cum copia picta", "item.minecraft.plenty_pottery_sherd": "Testa cum copia picta", "item.minecraft.poisonous_potato": "Patata venenata", "item.minecraft.polar_bear_spawn_egg": "Ovum creationis ursi polaris", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON> chori displosus", "item.minecraft.porkchop": "<PERSON><PERSON> cruda", "item.minecraft.potato": "Pat<PERSON>", "item.minecraft.potion": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON> inconcinna", "item.minecraft.potion.effect.empty": "Potio infabricabilis", "item.minecraft.potion.effect.fire_resistance": "Potio resistentiae igni", "item.minecraft.potion.effect.harming": "Potio calamitatis", "item.minecraft.potion.effect.healing": "Potio curationis", "item.minecraft.potion.effect.infested": "Potio infestationis", "item.minecraft.potion.effect.invisibility": "Potio invisibilitatis", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON> salt<PERSON>", "item.minecraft.potion.effect.levitation": "<PERSON>tio evectionis", "item.minecraft.potion.effect.luck": "Potio fortunae", "item.minecraft.potion.effect.mundane": "<PERSON>tio mundana", "item.minecraft.potion.effect.night_vision": "Potio visionis nocturnae", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON> man<PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON> veneni", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON>o casus lenti", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON> tardi<PERSON>is", "item.minecraft.potion.effect.strength": "Potio fortitudinis", "item.minecraft.potion.effect.swiftness": "Potio velocitatis", "item.minecraft.potion.effect.thick": "<PERSON>tio crassa", "item.minecraft.potion.effect.turtle_master": "<PERSON>tio magistri testudinis", "item.minecraft.potion.effect.water": "<PERSON><PERSON> aquae", "item.minecraft.potion.effect.water_breathing": "Potio respirationis in aqua", "item.minecraft.potion.effect.weakness": "<PERSON>tio infirmitatis", "item.minecraft.potion.effect.weaving": "<PERSON>tio contextus", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_archer": "Testa cum arco picta", "item.minecraft.pottery_shard_arms_up": "Testa cum brachiis pictis", "item.minecraft.pottery_shard_prize": "Testa cum adamante picto", "item.minecraft.pottery_shard_skull": "Testa cum calva picta", "item.minecraft.powder_snow_bucket": "Hama nivis pulvereae", "item.minecraft.prismarine_crystals": "Crystalla prismarinae", "item.minecraft.prismarine_shard": "Fragmentum prismarinum", "item.minecraft.prize_pottery_shard": "Testa cum adamante picto", "item.minecraft.prize_pottery_sherd": "Testa cum adamante picto", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON> aculeatus", "item.minecraft.pufferfish_bucket": "Hama cum pisce aculeato", "item.minecraft.pufferfish_spawn_egg": "Ovum creationis piscis aculeati", "item.minecraft.pumpkin_pie": "Placenta cucurbitae", "item.minecraft.pumpkin_seeds": "Semina cucurbitae", "item.minecraft.purple_bundle": "Sarcina purpurea", "item.minecraft.purple_dye": "Tinctura purpurea", "item.minecraft.purple_harness": "Capistrum purpureum", "item.minecraft.quartz": "<PERSON><PERSON>", "item.minecraft.rabbit": "Caro cuniculi cruda", "item.minecraft.rabbit_foot": "<PERSON><PERSON> cuniculi", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON> cunicularis", "item.minecraft.rabbit_spawn_egg": "Ovum creationis cuniculi", "item.minecraft.rabbit_stew": "<PERSON><PERSON> de cuniculo", "item.minecraft.raiser_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ornamentum tegminis tollentis", "item.minecraft.ravager_spawn_egg": "Ovum creationis vastatoris", "item.minecraft.raw_copper": "Cuprum infectum", "item.minecraft.raw_gold": "Aurum infectum", "item.minecraft.raw_iron": "Ferrum infectum", "item.minecraft.recovery_compass": "Pyxis magnetica ad recuperandum", "item.minecraft.red_bundle": "Sarcina rubra", "item.minecraft.red_dye": "Tinctura rubra", "item.minecraft.red_harness": "Capistrum rubrum", "item.minecraft.redstone": "Pulvis redstone", "item.minecraft.resin_brick": "Later resinae", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> resinae", "item.minecraft.rib_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.rib_armor_trim_smithing_template.new": "Ornamentum tegminis cum costis", "item.minecraft.rotten_flesh": "<PERSON><PERSON>", "item.minecraft.saddle": "Sagma", "item.minecraft.salmon": "<PERSON><PERSON> crudus", "item.minecraft.salmon_bucket": "Hama cum salmone", "item.minecraft.salmon_spawn_egg": "Ovum creationis salmonis", "item.minecraft.scrape_pottery_sherd": "Testa cum secure picta", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ornamentum tegminis excubitorum", "item.minecraft.shaper_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ornamentum tegminis fictoris", "item.minecraft.sheaf_pottery_shard": "Testa cum frumento picto", "item.minecraft.sheaf_pottery_sherd": "Testa cum frumento picto", "item.minecraft.shears": "Forfex", "item.minecraft.sheep_spawn_egg": "Ovum creationis ovis", "item.minecraft.shelter_pottery_shard": "Testa cum perfugio picto", "item.minecraft.shelter_pottery_sherd": "Testa cum perfugio picto", "item.minecraft.shield": "Scutum", "item.minecraft.shield.black": "Scutum nigrum", "item.minecraft.shield.blue": "Scutum caeruleum", "item.minecraft.shield.brown": "Scutum brunneum", "item.minecraft.shield.cyan": "Scutum cyaneum", "item.minecraft.shield.gray": "Scutum cinereum", "item.minecraft.shield.green": "Scutum viridis", "item.minecraft.shield.light_blue": "Scutum aerium", "item.minecraft.shield.light_gray": "Scutum canum", "item.minecraft.shield.lime": "Scutum prasinum", "item.minecraft.shield.magenta": "Scutum rubropurpureum", "item.minecraft.shield.orange": "Scutum aurantium", "item.minecraft.shield.pink": "Scutum roseum", "item.minecraft.shield.purple": "Scutum purpureum", "item.minecraft.shield.red": "Scutum rubrum", "item.minecraft.shield.white": "Scutum album", "item.minecraft.shield.yellow": "Scutum flavum", "item.minecraft.shulker_shell": "<PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "O<PERSON>m creation<PERSON> shulker", "item.minecraft.sign": "Titulus", "item.minecraft.silence_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.silence_armor_trim_smithing_template.new": "Ornamentum tegminis silentis", "item.minecraft.silverfish_spawn_egg": "Ovum creationis lepismatis", "item.minecraft.skeleton_horse_spawn_egg": "Ovum creationis equi sceletalis", "item.minecraft.skeleton_spawn_egg": "Ovum creationis sceleti", "item.minecraft.skull_banner_pattern": "Exemplar vexilli", "item.minecraft.skull_banner_pattern.desc": "Imago calvae", "item.minecraft.skull_banner_pattern.new": "Imago calvae exemplar vexilli", "item.minecraft.skull_pottery_shard": "Testa cum calva picta", "item.minecraft.skull_pottery_sherd": "Testa cum calva picta", "item.minecraft.slime_ball": "<PERSON><PERSON> muci", "item.minecraft.slime_spawn_egg": "Ovum creationis muci", "item.minecraft.smithing_template": "Forma fabrilis", "item.minecraft.smithing_template.applies_to": "Pertinet ad:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Adde laterem vel lapidem", "item.minecraft.smithing_template.armor_trim.applies_to": "Tegmina", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Adde tegimen", "item.minecraft.smithing_template.armor_trim.ingredients": "<PERSON><PERSON><PERSON> lapidibus<PERSON>", "item.minecraft.smithing_template.ingredients": "Fabricatur cum:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Adde laterem Netheritae", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Arma vel instrumenta adamantina", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Adde telum, tegimen vel instrumentum adamantinum", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Latere Netheritae", "item.minecraft.smithing_template.upgrade": "Solidamentum: ", "item.minecraft.sniffer_spawn_egg": "Ovum creationis odorisequi", "item.minecraft.snort_pottery_shard": "Testa cum rostro picto", "item.minecraft.snort_pottery_sherd": "Testa cum rostro picto", "item.minecraft.snout_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.snout_armor_trim_smithing_template.new": "Ornamentum tegminis cum rostro", "item.minecraft.snow_golem_spawn_egg": "Ovum creationis automati nivei", "item.minecraft.snowball": "<PERSON><PERSON> nivis", "item.minecraft.spectral_arrow": "Sagitta lucifera", "item.minecraft.spider_eye": "Oculus araneae", "item.minecraft.spider_spawn_egg": "Ovum creationis araneae", "item.minecraft.spire_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.spire_armor_trim_smithing_template.new": "Ornamentum tegminis cum turre", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "Potio inconcinna aspergens", "item.minecraft.splash_potion.effect.empty": "Potio infabricabilis aspergens", "item.minecraft.splash_potion.effect.fire_resistance": "Potio resistentiae igni aspergens", "item.minecraft.splash_potion.effect.harming": "Potio calamitatis aspergens", "item.minecraft.splash_potion.effect.healing": "Potio curationis aspergens", "item.minecraft.splash_potion.effect.infested": "Potio infestationis aspergens", "item.minecraft.splash_potion.effect.invisibility": "Potio invisibilitatis aspergens", "item.minecraft.splash_potion.effect.leaping": "Potio saltantis aspergens", "item.minecraft.splash_potion.effect.levitation": "Potio evectionis aspergens", "item.minecraft.splash_potion.effect.luck": "Potio fortunae aspergens", "item.minecraft.splash_potion.effect.mundane": "Potio mundana aspergens", "item.minecraft.splash_potion.effect.night_vision": "Potio visionis nocturnae aspergens", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON> manationis aspergens", "item.minecraft.splash_potion.effect.poison": "<PERSON>tio veneni aspergens", "item.minecraft.splash_potion.effect.regeneration": "Potio regenerationis aspergens", "item.minecraft.splash_potion.effect.slow_falling": "Potio casus lenti aspergens", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON>o tarditatis aspergens", "item.minecraft.splash_potion.effect.strength": "Potio fortitudinis aspergens", "item.minecraft.splash_potion.effect.swiftness": "Potio velocitatis aspergens", "item.minecraft.splash_potion.effect.thick": "Potio crassa aspergens", "item.minecraft.splash_potion.effect.turtle_master": "Potio magistri testudinis aspergens", "item.minecraft.splash_potion.effect.water": "Uter aquae aspergens", "item.minecraft.splash_potion.effect.water_breathing": "Potio respirationis in aqua aspergens", "item.minecraft.splash_potion.effect.weakness": "Potio infirmitatis aspergens", "item.minecraft.splash_potion.effect.weaving": "Potio contextus aspergens", "item.minecraft.splash_potion.effect.wind_charged": "<PERSON>tio inflationis aspergens", "item.minecraft.spruce_boat": "<PERSON>is piceae", "item.minecraft.spruce_chest_boat": "Ratis piceae cum cista", "item.minecraft.spyglass": "Telescopium", "item.minecraft.squid_spawn_egg": "Ovum creationis lolliginis", "item.minecraft.stick": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON> saxeus", "item.minecraft.stone_hoe": "Sarculum saxeum", "item.minecraft.stone_pickaxe": "Dolabra saxea", "item.minecraft.stone_shovel": "<PERSON><PERSON> saxea", "item.minecraft.stone_sword": "<PERSON><PERSON> saxeus", "item.minecraft.stray_spawn_egg": "Ovum creationis aberratoris", "item.minecraft.strider_spawn_egg": "Ovum creationis ambulatoris", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "Saccharum", "item.minecraft.suspicious_stew": "Ius suspectum", "item.minecraft.sweet_berries": "Bacae dulces", "item.minecraft.tadpole_bucket": "Hama cum gyrino", "item.minecraft.tadpole_spawn_egg": "Ovum creationis gyrini", "item.minecraft.tide_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.tide_armor_trim_smithing_template.new": "Ornamentum tegminis cum aestu", "item.minecraft.tipped_arrow": "Sagitta imbuta", "item.minecraft.tipped_arrow.effect.awkward": "Sagitta imbuta", "item.minecraft.tipped_arrow.effect.empty": "Sagitta imbuta infabricabilis", "item.minecraft.tipped_arrow.effect.fire_resistance": "Sagitta resistentiae igni", "item.minecraft.tipped_arrow.effect.harming": "Sagitta calamitatis", "item.minecraft.tipped_arrow.effect.healing": "Sagitta curationis", "item.minecraft.tipped_arrow.effect.infested": "Sagitta infestationis", "item.minecraft.tipped_arrow.effect.invisibility": "Sagitta invisibilitatis", "item.minecraft.tipped_arrow.effect.leaping": "Sagitta saltantis", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON>gitta evectionis", "item.minecraft.tipped_arrow.effect.luck": "Sagitta fortunae", "item.minecraft.tipped_arrow.effect.mundane": "Sagitta imbuta", "item.minecraft.tipped_arrow.effect.night_vision": "Sagitta visionis nocturnae", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON><PERSON> man<PERSON>is", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> veneni", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON><PERSON>is", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON>gitta casus lenti", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON>ta tarditatis", "item.minecraft.tipped_arrow.effect.strength": "Sagitta fortitudinis", "item.minecraft.tipped_arrow.effect.swiftness": "Sagitta velocitatis", "item.minecraft.tipped_arrow.effect.thick": "Sagitta imbuta", "item.minecraft.tipped_arrow.effect.turtle_master": "Sagitta magistri testudinis", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON> aspergentis", "item.minecraft.tipped_arrow.effect.water_breathing": "Sagitta respirationis in aqua", "item.minecraft.tipped_arrow.effect.weakness": "Sagitta infirmitatis", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON><PERSON>ta contextus", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON><PERSON> inflationis", "item.minecraft.tnt_minecart": "Plaustrum fodinae cum dynamite", "item.minecraft.torchflower_seeds": "Semina faculariae", "item.minecraft.totem_of_undying": "Simulacrum immortalitatis", "item.minecraft.trader_llama_spawn_egg": "Ovum creationis lamae mercatoris", "item.minecraft.trial_key": "<PERSON><PERSON>is periculorum", "item.minecraft.trident": "Tridens", "item.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> multigener", "item.minecraft.tropical_fish_bucket": "Hama cum pisce multigeneri", "item.minecraft.tropical_fish_spawn_egg": "Ovum creationis piscis multigeneris", "item.minecraft.turtle_helmet": "<PERSON>a testudinis", "item.minecraft.turtle_scute": "<PERSON><PERSON><PERSON> testudinis", "item.minecraft.turtle_spawn_egg": "Ovum creationis testudinis", "item.minecraft.vex_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.vex_armor_trim_smithing_template.new": "Ornamentum tegminis vexatoris", "item.minecraft.vex_spawn_egg": "Ovum creationis vexatoris", "item.minecraft.villager_spawn_egg": "Ovum creationis vicani", "item.minecraft.vindicator_spawn_egg": "Ovum creationis vindicatoris", "item.minecraft.wandering_trader_spawn_egg": "Ovum creationis mercatoris vagi", "item.minecraft.ward_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.ward_armor_trim_smithing_template.new": "Ornamentum tegminis vigiliarum", "item.minecraft.warden_spawn_egg": "Ovum creationis vigilis", "item.minecraft.warped_fungus_on_a_stick": "Fungus distortus in virga", "item.minecraft.water_bucket": "Hama aquae", "item.minecraft.wayfinder_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ornamentum tegminis viatoris", "item.minecraft.wheat": "Frumentum", "item.minecraft.wheat_seeds": "<PERSON>na frumenti", "item.minecraft.white_bundle": "Sa<PERSON>ina alba", "item.minecraft.white_dye": "Tinctura alba", "item.minecraft.white_harness": "Capistrum album", "item.minecraft.wild_armor_trim_smithing_template": "Forma fabrilis", "item.minecraft.wild_armor_trim_smithing_template.new": "Ornamentum tegminis silvanum", "item.minecraft.wind_charge": "Missile ventosum", "item.minecraft.witch_spawn_egg": "Ovum creationis veneficae", "item.minecraft.wither_skeleton_spawn_egg": "Ovum creationis sceleti wither", "item.minecraft.wither_spawn_egg": "Ovum creationis wither", "item.minecraft.wolf_armor": "Tegmen lupinum", "item.minecraft.wolf_spawn_egg": "Ovum creationis lupi", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON> lignea", "item.minecraft.wooden_hoe": "Sarculum ligneum", "item.minecraft.wooden_pickaxe": "Dolabra lignea", "item.minecraft.wooden_shovel": "<PERSON>la lignea", "item.minecraft.wooden_sword": "<PERSON><PERSON> ligneus", "item.minecraft.writable_book": "Liber et penna", "item.minecraft.written_book": "Liber inscriptus", "item.minecraft.yellow_bundle": "Sarcina flava", "item.minecraft.yellow_dye": "Tinctura flava", "item.minecraft.yellow_harness": "Capistrum flavum", "item.minecraft.zoglin_spawn_egg": "Ovum creationis z<PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "Ovum creationis equi resurrecti", "item.minecraft.zombie_spawn_egg": "Ovum creationis resurrecti", "item.minecraft.zombie_villager_spawn_egg": "Ovum creationis vicani resurrecti", "item.minecraft.zombified_piglin_spawn_egg": "Ovum creationis piglin resurrecti", "item.modifiers.any": "Dum induitur:", "item.modifiers.armor": "Dum geritur:", "item.modifiers.body": "Dum induitur:", "item.modifiers.chest": "Dum in corpore:", "item.modifiers.feet": "Dum in pedibus:", "item.modifiers.hand": "Dum tenetur:", "item.modifiers.head": "Dum in capite:", "item.modifiers.legs": "Dum in cruribus:", "item.modifiers.mainhand": "In manu priore:", "item.modifiers.offhand": "In manu secunda:", "item.modifiers.saddle": "Dum sternitur:", "item.nbt_tags": "NBT: nota(e) %s", "item.op_block_warning.line1": "Cave:", "item.op_block_warning.line2": "Hac re uti iussum efficere potest", "item.op_block_warning.line3": "Noli uti nisi accurate res quae continentur scis!", "item.unbreakable": "Infragi<PERSON>", "itemGroup.buildingBlocks": "Materiae aedificationis", "itemGroup.coloredBlocks": "<PERSON><PERSON> tincti", "itemGroup.combat": "Arma", "itemGroup.consumables": "Res quae edi possunt", "itemGroup.crafting": "Res ad fabricandum utiles", "itemGroup.foodAndDrink": "Cibi potionesque", "itemGroup.functional": "Cubi quibus uti potes", "itemGroup.hotbar": "Res vectis servata", "itemGroup.ingredients": "Res ad alias res faciendas", "itemGroup.inventory": "Inventarium supervivendi", "itemGroup.natural": "Cubi naturales", "itemGroup.op": "Instrumenta administratoris", "itemGroup.redstone": "Cubi redstone", "itemGroup.search": "Quaere res", "itemGroup.spawnEggs": "<PERSON><PERSON>", "itemGroup.tools": "Instrumenta resque quibus uti potes", "item_modifier.unknown": "Modificator rei ignotus: %s", "jigsaw_block.final_state": "Fit:", "jigsaw_block.generate": "Genera", "jigsaw_block.joint.aligned": "Non volvibilis", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "<PERSON><PERSON><PERSON>:", "jigsaw_block.keep_jigsaws": "Serva iunctiones", "jigsaw_block.levels": "Gradus generationis: %s", "jigsaw_block.name": "Nomen:", "jigsaw_block.placement_priority": "Prioritas ponendi:", "jigsaw_block.placement_priority.tooltip": "Cum hic cubus iunctionis ad ullam partem iungitur, hic ordo est in quo illa pars numeratur pro conexibus in aedificio toto.\n\nPartes numerabitur a prioritate superiore ad inferiorem, contentionibus resolutis ordine insertionis.", "jigsaw_block.pool": "Iungenda:", "jigsaw_block.selection_priority": "Prioritas electionis:", "jigsaw_block.selection_priority.tooltip": "Cum pars genitor numeretur pro conexibus, hic ordo est in quo hic cubus iunctionis se iungere ad selectam partem temptat.\n\nIunctiones numerabitur a prioritate superiore ad inferiorem, contentionibus resolutis ordine fortuito.", "jigsaw_block.target": "Scopi nomen:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (campanulae)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Incrementa", "key.attack": "Oppugna/Destrue", "key.back": "Ambula retrorsum", "key.categories.creative": "<PERSON><PERSON> fingendi", "key.categories.gameplay": "Lusus", "key.categories.inventory": "Inventarium", "key.categories.misc": "Miscellaneus", "key.categories.movement": "<PERSON><PERSON>", "key.categories.multiplayer": "Ludus multorum", "key.categories.ui": "Iunctura ludi", "key.chat": "Aperi locutorium", "key.command": "Scribe iussum", "key.drop": "Rem lectam emitte", "key.forward": "Ambula prorsum", "key.fullscreen": "Simulacrum plenum", "key.hotbar.1": "Res vectis arca I", "key.hotbar.2": "Res vectis arca II", "key.hotbar.3": "Res vectis arca III", "key.hotbar.4": "Res vectis arca IV", "key.hotbar.5": "Res vectis arca V", "key.hotbar.6": "Res vectis arca VI", "key.hotbar.7": "Res vectis arca VII", "key.hotbar.8": "Res vectis arca VIII", "key.hotbar.9": "Res vectis arca IX", "key.inventory": "Inventarium aperi/claude", "key.jump": "Saltare", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Retro del.", "key.keyboard.caps.lock": "Firm<PERSON>.", "key.keyboard.comma": ",", "key.keyboard.delete": "Dele", "key.keyboard.down": "Deorsum", "key.keyboard.end": "<PERSON><PERSON>", "key.keyboard.enter": "<PERSON><PERSON>", "key.keyboard.equal": "=", "key.keyboard.escape": "Fuga", "key.keyboard.f1": "F I", "key.keyboard.f10": "F X", "key.keyboard.f11": "F XI", "key.keyboard.f12": "F XII", "key.keyboard.f13": "F XIII", "key.keyboard.f14": "F XIV", "key.keyboard.f15": "F XV", "key.keyboard.f16": "F XVI", "key.keyboard.f17": "F XVII", "key.keyboard.f18": "F XVIII", "key.keyboard.f19": "F XIX", "key.keyboard.f2": "F II", "key.keyboard.f20": "F XX", "key.keyboard.f21": "F XXI", "key.keyboard.f22": "F XXII", "key.keyboard.f23": "F XXIII", "key.keyboard.f24": "F XXIV", "key.keyboard.f25": "F XXV", "key.keyboard.f3": "F III", "key.keyboard.f4": "F IV", "key.keyboard.f5": "F V", "key.keyboard.f6": "F VI", "key.keyboard.f7": "F VII", "key.keyboard.f8": "F VIII", "key.keyboard.f9": "F IX", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>", "key.keyboard.insert": "Insere", "key.keyboard.keypad.0": "N (clav. n.)", "key.keyboard.keypad.1": "I (clav. n.)", "key.keyboard.keypad.2": "II (clav. n.)", "key.keyboard.keypad.3": "III (clav. n.)", "key.keyboard.keypad.4": "IV (clav. n.)", "key.keyboard.keypad.5": "V (clav. n.)", "key.keyboard.keypad.6": "VI (clav. n.)", "key.keyboard.keypad.7": "VII (clav. n.)", "key.keyboard.keypad.8": "VIII (clav. n.)", "key.keyboard.keypad.9": "IX (clav. n.)", "key.keyboard.keypad.add": "+ (clav. n.)", "key.keyboard.keypad.decimal": ". (clav. n.)", "key.keyboard.keypad.divide": "/ (clav. n.)", "key.keyboard.keypad.enter": "<PERSON><PERSON> (clav. n.)", "key.keyboard.keypad.equal": "= (clav. n.)", "key.keyboard.keypad.multiply": "* (clav. n.)", "key.keyboard.keypad.subtract": "- (clav. n.)", "key.keyboard.left": "Sinistrorsum", "key.keyboard.left.alt": "Alt sinister", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl sinister", "key.keyboard.left.shift": "<PERSON><PERSON><PERSON> sin.", "key.keyboard.left.win": "Comm. Sin.", "key.keyboard.menu": "Electiones", "key.keyboard.minus": "-", "key.keyboard.num.lock": "<PERSON>ir<PERSON><PERSON>", "key.keyboard.page.down": "Pagina inf.", "key.keyboard.page.up": "Pagina sup.", "key.keyboard.pause": "Consiste", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON> imag.", "key.keyboard.right": "Dextrorsum", "key.keyboard.right.alt": "Alt dexter", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl dexter", "key.keyboard.right.shift": "Mai<PERSON>c dex.", "key.keyboard.right.win": "Comm<PERSON> Dex.", "key.keyboard.scroll.lock": "Offirmator rotae", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Spacium", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Non nexus", "key.keyboard.up": "Sursum", "key.keyboard.world.1": "<PERSON>nd<PERSON>", "key.keyboard.world.2": "Mundus II", "key.left": "Ambula sinistrorsum", "key.loadToolbarActivator": "Repone reis vectis activator", "key.mouse": "Icendus %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON>.", "key.mouse.middle": "Premendus Med.", "key.mouse.right": "<PERSON><PERSON><PERSON>.", "key.pickItem": "Lege cubum", "key.playerlist": "Indica lusores", "key.quickActions": "Actiones celeres", "key.right": "Ambula dextrorsum", "key.saveToolbarActivator": "Serva reis vectis activator", "key.screenshot": "Transcribe simulacrum", "key.smoothCamera": "Muta modum circumspiciendi", "key.sneak": "Furtim ambula", "key.socialInteractions": "Simulacrum optionum sociorum", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> l<PERSON> (spectatores)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Commuta rem re in manu altera", "key.togglePerspective": "Apta spectaculum", "key.use": "Utere re/Pone cubum", "known_server_link.announcements": "Nuntia", "known_server_link.community": "Consortio", "known_server_link.community_guidelines": "Normae consortionis", "known_server_link.feedback": "Emendationes", "known_server_link.forums": "Fora", "known_server_link.news": "Nuntia", "known_server_link.report_bug": "<PERSON><PERSON><PERSON> moderatri refer", "known_server_link.status": "Status", "known_server_link.support": "Susceptio", "known_server_link.website": "<PERSON>gina interretialis", "lanServer.otherPlayers": "Optiones de lusoribus aliis", "lanServer.port": "Numerus portus", "lanServer.port.invalid": "Porta irrita est.\nQuadrum ad scribendum vacuum relinque vel numerum inter 1024 et 65535 scribe.", "lanServer.port.invalid.new": "Porta irrita est.\nQuadrum ad scribendum vacuum relinque vel numerum inter %s et %s scribe.", "lanServer.port.unavailable": "Porta uti non potes.\nQuadrum ad scribendum vacuum relinque vel alium numerum inter 1024 et 65535 scribe.", "lanServer.port.unavailable.new": "Porta uti non potes.\nQuadrum ad scribendum vacuum relinque vel alium numerum inter %s et %s scribe.", "lanServer.scanning": "Ludos in nexo locali tuo quaerit", "lanServer.start": "Conecte mundum reti locali", "lanServer.title": "Mundus RL", "language.code": "lat_VA", "language.name": "Latina", "language.region": "Latium", "lectern.take_book": "Librum cape", "loading.progress": "%s%%", "mco.account.privacy.info": "Hic de Mojang legibusque vitae privatae amplius legere potes", "mco.account.privacy.info.button": "Plus de GDPR legere", "mco.account.privacy.information": "Mojang quasdam formulas ut liberi et secreta sua servantur in obsequio Children's Online Privacy Act (COPPA) et General Data Protection Regulation (GDPR).\n\nFortasse tibi necesse est permissionem parentum adipisci antequam licentia tua Realms uti potes.", "mco.account.privacyinfo": "Quibusdam modis utitur Mojang ut liberos vitamque privatam eorum tutemur. Conformamus ergo nos ad Liberorum Vitae Privatae Praesidii in Linea Actum (LVPPLA) et ad Datorum Generalis Protectionis Regulationem (DGPR).\n\nNecesse est fortasse ut parentes consentant prius quam ratiocinium Realmorum tuum intres.\n\nSi antiquius ratiocinium habeas (si nomine - non inscriptione electrica - intres) mutandum erit ad ratiocinium Mojang ad Realmos intrandum.", "mco.account.update": "<PERSON>va licentiam", "mco.activity.noactivity": "Iam dies %s sunt cum nemo in hoc realm ludit", "mco.activity.title": "Activitas lusoris", "mco.backup.button.download": "Extrahe novissimum", "mco.backup.button.reset": "Reini<PERSON> mundum", "mco.backup.button.restore": "<PERSON><PERSON><PERSON>", "mco.backup.button.upload": "Mundum mitte", "mco.backup.changes.tooltip": "Mutationes", "mco.backup.entry": "Exemplar (%s)", "mco.backup.entry.description": "Descriptio", "mco.backup.entry.enabledPack": "Compilationes electae", "mco.backup.entry.gameDifficulty": "Difficultas ludi", "mco.backup.entry.gameMode": "<PERSON><PERSON> ludi", "mco.backup.entry.gameServerVersion": "Versio moderatri ludi", "mco.backup.entry.name": "Nomen", "mco.backup.entry.seed": "Semen", "mco.backup.entry.templateName": "Nomen formae", "mco.backup.entry.undefined": "Mutatio indesignata", "mco.backup.entry.uploaded": "Transmissum", "mco.backup.entry.worldType": "Genus mundi", "mco.backup.generate.world": "<PERSON><PERSON> mundum", "mco.backup.info.title": "Mutationes ab exemplare proximo", "mco.backup.narration": "Exemplum ex %s", "mco.backup.nobackups": "Nunc non sunt exemplaria huius realm.", "mco.backup.restoring": "Realm tuum restituitur", "mco.backup.unknown": "NESCITUM", "mco.brokenworld.download": "Extrahe", "mco.brokenworld.downloaded": "Mundus extractus est", "mco.brokenworld.message.line1": "Repone vel elige mundum alium.", "mco.brokenworld.message.line2": "Etiam potes optare mundum ad ludum unius extrahere.", "mco.brokenworld.minigame.title": "Hic ludulus non iam suscipitur", "mco.brokenworld.nonowner.error": "Praestolare erum Realm mundum reponere", "mco.brokenworld.nonowner.title": "Mundum est antiquum", "mco.brokenworld.play": "Lu<PERSON>", "mco.brokenworld.reset": "Reinitia", "mco.brokenworld.title": "Mundus currens tuus non iam suscipitur", "mco.client.incompatible.msg.line1": "Cliens quo uteris ad Realms non convenit.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON><PERSON><PERSON>, versione Minecraft recentissima utaris.", "mco.client.incompatible.msg.line3": "Realms ad versiones praecoces non convenit.", "mco.client.incompatible.title": "Cliens non convenit!", "mco.client.outdated.stable.version": "Versio clientis tui (%s) cum Realms non convenit.\n\nLude novissima versio Minecraft.", "mco.client.unsupported.snapshot.version": "Versio clientis tui (%s) cum Realms non convenit.\n\nRealms non luderi potest in hac versione praecoce.", "mco.compatibility.downgrade": "Versionem fac veteriorem", "mco.compatibility.downgrade.description": "Hic mundus antea in versione %s lusus est; uteris versione %s. Degradare mundum fortasse corruptiones efficere potest - spondere eum extrahi vel operare non possumus.\n\nExemplar mundi tui servabitur in \"Exemplaria mundi\". Restitue mundum tuum si necesse est.", "mco.compatibility.incompatible.popup.title": "Versio obsoleta", "mco.compatibility.incompatible.releaseType.popup.message": "Mundus quem iungere conaris incongruus est cum versione qua usaris.", "mco.compatibility.incompatible.series.popup.message": "Hic mundus antea in versione %s lusus est; uteris versione %s.\n\nHae series incongruae in invicem sunt. Novus mundus requiritur ut in hac versione luderes.", "mco.compatibility.unverifiable.message": "V<PERSON>io in qua hic mundus antea lusus est probari non potuit. Si mundus regeneraverit vel degeneraverit, exemplar mundi automate creabitur et servabitur in \"exemplares mundorum\".", "mco.compatibility.unverifiable.title": "Num versio convenit non probari potest", "mco.compatibility.upgrade": "Versionem fac recentiorem", "mco.compatibility.upgrade.description": "Hic mundus in versione %s antea lusus est; uteris versionem %s.\n\nExemplar mundi tui servabitur in \"Exemplaria mundi\".\n\nRestitue mundum tuum si necesse est.", "mco.compatibility.upgrade.friend.description": "Hic mundus antea in versione %s lusus est; uteris versione %s.\n\nExemplar mundi servabitur in \"exemplares mundorum\".\n\n<PERSON>inus Realm, si vult, mundum reinstaurare potest.", "mco.compatibility.upgrade.title": "Visne vero formam huius mundi recentiorem facere?", "mco.configure.current.minigame": "Adest", "mco.configure.world.activityfeed.disabled": "Informatio de lusoribus hoc tempore remota est", "mco.configure.world.backup": "Exemplaria mundi", "mco.configure.world.buttons.activity": "Activitas lusoris", "mco.configure.world.buttons.close": "<PERSON> claude", "mco.configure.world.buttons.delete": "Dele", "mco.configure.world.buttons.done": "Serva exique", "mco.configure.world.buttons.edit": "Optiones", "mco.configure.world.buttons.invite": "Invi<PERSON>", "mco.configure.world.buttons.moreoptions": "Optiones plures", "mco.configure.world.buttons.newworld": "Mundus novus", "mco.configure.world.buttons.open": "Realm aperi", "mco.configure.world.buttons.options": "Optiones mundi", "mco.configure.world.buttons.players": "Lusores", "mco.configure.world.buttons.region_preference": "Selige regionem...", "mco.configure.world.buttons.resetworld": "Reini<PERSON> mundum", "mco.configure.world.buttons.save": "<PERSON><PERSON>", "mco.configure.world.buttons.settings": "Optiones", "mco.configure.world.buttons.subscription": "Subscriptio", "mco.configure.world.buttons.switchminigame": "Ludum parvum permuta", "mco.configure.world.close.question.line1": "Realm tuum inaccessum fiet.", "mco.configure.world.close.question.line2": "Visne vero procedere?", "mco.configure.world.close.question.title": "Re<PERSON>risne facere mutationes sine discidio?", "mco.configure.world.closing": "Realm clauditur...", "mco.configure.world.commandBlocks": "<PERSON><PERSON>", "mco.configure.world.delete.button": "Realm dele", "mco.configure.world.delete.question.line1": "Realm tuum sempiterne delebitur", "mco.configure.world.delete.question.line2": "Visne vero procedere?", "mco.configure.world.description": "Descriptio realm", "mco.configure.world.edit.slot.name": "Nomen mundi", "mco.configure.world.edit.subscreen.adventuremap": "Nonnullae optiones vetantur cum mundus praesens sit adventurus", "mco.configure.world.edit.subscreen.experience": "Aliquae optiones vetantur cum hic mundus sit experientia", "mco.configure.world.edit.subscreen.inspiration": "Nonnullae optiones vetantur cum praesens mundus sit inspiratio", "mco.configure.world.forceGameMode": "Coge modum ludi", "mco.configure.world.invite.narration": "%s novas invitationes habes", "mco.configure.world.invite.profile.name": "Nomen", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Invitati (%s)", "mco.configure.world.invites.normal.tooltip": "Lusor normalis", "mco.configure.world.invites.ops.tooltip": "Administrator", "mco.configure.world.invites.remove.tooltip": "Remove", "mco.configure.world.leave.question.line1": "Si tu ab hoc Realm discesseris, id non videbis, nisi tu iterum invitatus eris", "mco.configure.world.leave.question.line2": "Visne vero procedere?", "mco.configure.world.loading": "Realm construitur", "mco.configure.world.location": "Locus", "mco.configure.world.minigame": "Adest: %s", "mco.configure.world.name": "Nomen realm", "mco.configure.world.opening": "Realm aperitur...", "mco.configure.world.players.error": "Non exsistit lusor nominis dati", "mco.configure.world.players.inviting": "Lusor invitatur...", "mco.configure.world.players.title": "Lusores", "mco.configure.world.pvp": "Lusor contra lusorem", "mco.configure.world.region_preference": "Favor regionis", "mco.configure.world.region_preference.title": "Electio regionis", "mco.configure.world.reset.question.line1": "Tuus mundus refacietur et tuus mundus praesens amittetur", "mco.configure.world.reset.question.line2": "Visne vero procedere?", "mco.configure.world.resourcepack.question": "Compilatione supplementorum tibi utendum est ut in hoc Realm ludas.\n\nVisne eam extrahere et in Realm ludere?", "mco.configure.world.resourcepack.question.line1": "Requiris compilationem supplimentorum ludere in hoc Realm", "mco.configure.world.resourcepack.question.line2": "Visne id extrahere et ludere?", "mco.configure.world.restore.download.question.line1": "Mundus extrahetur et tuis mundis unius lusoris addetur.", "mco.configure.world.restore.download.question.line2": "Visne perseverare?", "mco.configure.world.restore.question.line1": "Mundus restaurabitur diei '%s' (%s)", "mco.configure.world.restore.question.line2": "Visne vero procedere?", "mco.configure.world.settings.expired": "Optiones Realm exspirati mutare non potes", "mco.configure.world.settings.title": "Optiones", "mco.configure.world.slot": "Mundus %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Realm tuum pro alio mutabit mundum", "mco.configure.world.slot.switch.question.line2": "Visne vero procedere?", "mco.configure.world.slot.tooltip": "Permuta ad mundum", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "Permuta ad ludum parvum", "mco.configure.world.spawnAnimals": "Animalia crea", "mco.configure.world.spawnMonsters": "<PERSON>ias crea", "mco.configure.world.spawnNPCs": "Vicanos crea", "mco.configure.world.spawnProtection": "Tutamen loci creandi", "mco.configure.world.spawn_toggle.message": "Hanc optionem inactivam facere omnia quae sunt illius generis removebit", "mco.configure.world.spawn_toggle.message.npc": "Hanc optionem inactivam facere omnia quae sunt illius generis removebit, sicut vicani", "mco.configure.world.spawn_toggle.title": "Cave!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "dies", "mco.configure.world.subscription.days": "dies", "mco.configure.world.subscription.expired": "Exspiratum", "mco.configure.world.subscription.extend": "Extende Subnotationem", "mco.configure.world.subscription.less_than_a_day": "Minus quam dies", "mco.configure.world.subscription.month": "mensis", "mco.configure.world.subscription.months": "menses", "mco.configure.world.subscription.recurring.daysleft": "Se novaturum ad", "mco.configure.world.subscription.recurring.info": "Subscriptioni Realms tuo mutationes e.g. tempora coniungere vel pensiones recurrentes sistere quae factae sunt non reddetur usque ad rationem tuam proximam.", "mco.configure.world.subscription.remaining.days": "%1$s dies", "mco.configure.world.subscription.remaining.months": "%1$s menses", "mco.configure.world.subscription.remaining.months.days": "%1$s menses, %2$s dies", "mco.configure.world.subscription.start": "Dies initii", "mco.configure.world.subscription.tab": "Subscriptio", "mco.configure.world.subscription.timeleft": "Tempus manens", "mco.configure.world.subscription.title": "Indicium subscriptionis", "mco.configure.world.subscription.unknown": "Ignota", "mco.configure.world.switch.slot": "<PERSON><PERSON> mundum", "mco.configure.world.switch.slot.subtitle": "Hoc mundus vacuus est, selege quomodo creare mundum tuum", "mco.configure.world.title": "Instrue Realm:", "mco.configure.world.uninvite.player": "Esne certus ut '%s' invitationem removere velis?", "mco.configure.world.uninvite.question": "Esne certus ut invitationem removere velis", "mco.configure.worlds.title": "Mundi", "mco.connect.authorizing": "Ineo...", "mco.connect.connecting": "Cum realm conectitur...", "mco.connect.failed": "Realm conecti non potuit", "mco.connect.region": "Regio moderatri: %s", "mco.connect.success": "Conexus sum", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Nomen inscribere debes!", "mco.create.world.failed": "Mundus creari non potuit!", "mco.create.world.reset.title": "Mundus creatur...", "mco.create.world.skip": "Exsulte", "mco.create.world.subtitle": "Facultative, selige quem mundum in novum realm tuum ponere velis", "mco.create.world.wait": "Realm creatur...", "mco.download.cancelled": "Extrahere discindebat", "mco.download.confirmation.line1": "Mundus extrahendus maior quam %s est", "mco.download.confirmation.line2": "Hic mundus ad Realm tuum iterum mittere non poteris", "mco.download.confirmation.oversized": "Mundum quem extrahes maior est quam %s\n\nHunc mundum ad realm imponere iterum non poteris", "mco.download.done": "Mundus extractus est", "mco.download.downloading": "<PERSON><PERSON><PERSON>", "mco.download.extracting": "<PERSON><PERSON><PERSON> em<PERSON>", "mco.download.failed": "Mundus extrahi non potuit", "mco.download.percent": "%s %%", "mco.download.preparing": "Extractio paratur", "mco.download.resourcePack.fail": "Compilatio supplementorum extrahi non potuit!", "mco.download.speed": "(%s quoque secundo)", "mco.download.speed.narration": "%s quoque secundo", "mco.download.title": "<PERSON>nd<PERSON> novissimus <PERSON>ur", "mco.error.invalid.session.message": "Conare reincipere Minecraft", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON> irrita", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON> obsoletus", "mco.errorMessage.6002": "Leges servitiorum non acceptantur", "mco.errorMessage.6003": "Limes extractionis excessus est", "mco.errorMessage.6004": "Limes transmissionis excessus est", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON> clausus", "mco.errorMessage.6006": "<PERSON><PERSON><PERSON> antiquus", "mco.errorMessage.6007": "Lusor in Realms nimium est", "mco.errorMessage.6008": "Nomen Realm irritum est", "mco.errorMessage.6009": "Descriptio Realm irrita est", "mco.errorMessage.connectionFailure": "Error evenit, conare iterum postea.", "mco.errorMessage.generic": "Error evenit: ", "mco.errorMessage.initialize.failed": "Realm creare non potuit", "mco.errorMessage.noDetails": "Singula erroris non praebita sunt", "mco.errorMessage.realmsService": "Error evenit (%s):", "mco.errorMessage.realmsService.configurationError": "Error inopinatus evenit dum optiones mundi mutat", "mco.errorMessage.realmsService.connectivity": "Realms conecti non potui: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Num versio convenit non confirmare potui, responsum acceptum: %s", "mco.errorMessage.retry": "Operationem reconare", "mco.errorMessage.serviceBusy": "Realms nunc occupatissimus est.\nRealm conecti iterum conare posterius.", "mco.gui.button": "Premendus", "mco.gui.ok": "<PERSON><PERSON>", "mco.info": "Informationes!", "mco.invited.player.narration": "Lusor %s invitatus est", "mco.invites.button.accept": "Accipe", "mco.invites.button.reject": "<PERSON><PERSON>", "mco.invites.nopending": "Omnes invitationes vidisti!", "mco.invites.pending": "Novae invitationes!", "mco.invites.title": "Index invitationum", "mco.minigame.world.changeButton": "Selige alium ludum parvum", "mco.minigame.world.info.line1": "Ho<PERSON>, eo tempore, reponet tuum mundum cum ludo parvo!", "mco.minigame.world.info.line2": "Poteris ad tuum mundum veterem redire sine amittendo.", "mco.minigame.world.noSelection": "Selige", "mco.minigame.world.restore": "Ludus parvus finitur...", "mco.minigame.world.restore.question.line1": "Ludus parvus desinet et realm tuum restaurabitur.", "mco.minigame.world.restore.question.line2": "Visne vero procedere?", "mco.minigame.world.selected": "El<PERSON><PERSON> ludus parvus:", "mco.minigame.world.slot.screen.title": "Mundus permutatur...", "mco.minigame.world.startButton": "Cambe", "mco.minigame.world.starting.screen.title": "Ludus parvus incipitur...", "mco.minigame.world.stopButton": "Ludi parvi fac finem", "mco.minigame.world.switch.new": "Visne seligere alium ludum parvum?", "mco.minigame.world.switch.title": "Ludum parvum permuta", "mco.minigame.world.title": "Permuta realm ad ludum parvum", "mco.news": "Nuntia Realms", "mco.notification.dismiss": "Neglige", "mco.notification.transferSubscription.buttonText": "Transfer statim", "mco.notification.transferSubscription.message": "Iava Realms subscriptiones movebitur in Microsoft Store. Noli tuam subscriptionem pendere neglegere!\nTransfer statim et recipies XXX dies Realms gratuite.\nQuaere \"Profile\" in minecraft.net ut subscriptionem tuam transferas.", "mco.notification.visitUrl.buttonText.default": "Aperi nexum", "mco.notification.visitUrl.message.default": "Visita nexum infra", "mco.onlinePlayers": "Lusor<PERSON> qui adsunt", "mco.play.button.realm.closed": "Realm clausum est", "mco.question": "Quaest<PERSON>", "mco.reset.world.adventure": "Expeditiones", "mco.reset.world.experience": "Experientiae", "mco.reset.world.generate": "Mundus novus", "mco.reset.world.inspiration": "Inspiratio", "mco.reset.world.resetting.screen.title": "Mundus reficitur...", "mco.reset.world.seed": "Semen (facultativum)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.title": "Reinstitue mundum", "mco.reset.world.upload": "Mundum transmitte", "mco.reset.world.warning": "Hoc reponet currentem mundum tui Realm", "mco.selectServer.buy": "Eme realm!", "mco.selectServer.close": "<PERSON><PERSON>", "mco.selectServer.closed": "Regnum clausum", "mco.selectServer.closeserver": "<PERSON>", "mco.selectServer.configure": "Realm instrue", "mco.selectServer.configureRealm": "Realm instrue", "mco.selectServer.create": "Crea realm", "mco.selectServer.create.subtitle": "Selige quem mundum ponendum in realm novum tuum", "mco.selectServer.expired": "Regnum Exspiratum", "mco.selectServer.expiredList": "Subscriptio tuae exspirabat", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Subscribe", "mco.selectServer.expiredTrial": "Tempus exempli terminavit", "mco.selectServer.expires.day": "In uno die expirabit", "mco.selectServer.expires.days": "%s die expirabit", "mco.selectServer.expires.soon": "Mox expirabit", "mco.selectServer.leave": "A realm discede", "mco.selectServer.loading": "Realms series construitur", "mco.selectServer.mapOnlySupportedForVersion": "Hic mundus non suscipitur in versione %s", "mco.selectServer.minigame": "Ludus parvus:", "mco.selectServer.minigameName": "Ludus parvus: %s", "mco.selectServer.minigameNotSupportedInVersion": "Hic ludus parvus non suscipitur in versione %s", "mco.selectServer.noRealms": "Videtur te Realm non habere. Adde regnum ut cum amicis tuis ludas.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Regnum apertum", "mco.selectServer.openserver": "Realm aperi", "mco.selectServer.play": "Ludere", "mco.selectServer.popup": "Realms est fidus, simplex modus Minecraft cum usque decem amicis ludendi. Sustinet multos ludos parvos multosque specialiter factos mundos! Solum dominus regni solvere debet.", "mco.selectServer.purchase": "<PERSON> adde", "mco.selectServer.trial": "Adquire exemplum!", "mco.selectServer.uninitialized": "Ice, ut Realm crees!", "mco.snapshot.createSnapshotPopup.text": "Creaturus(a) es gratuitum Realm in versione praecoce quod cunctum cum Realm tua cara subscriptione erit. Hoc Realm ludere potes donec subscriptio peribitur. Realm carum tuum non afficetur.", "mco.snapshot.createSnapshotPopup.title": "Creane <PERSON> in versione praecoce?", "mco.snapshot.creating": "Creans Realm in versione praecoce...", "mco.snapshot.description": "Cunctum cum %s", "mco.snapshot.friendsRealm.downgrade": "Necesse est tibi ludere in versione %s ut hoc Realm adiungas", "mco.snapshot.friendsRealm.upgrade": "Necesse est %s regenerare Realm eius antequam ludere potes in hac versione praecoce", "mco.snapshot.paired": "Hoc Realm in versione praecoce cunctum est cum %s", "mco.snapshot.parent.tooltip": "Utere versione novissima Minecraft ut hoc Realm ludas", "mco.snapshot.start": "Incipe gratuitum Realm in versione praecoce", "mco.snapshot.subscription.info": "Hoc est Realm in versione praecoce quod cunctum est cum subscriptione tui Realm '%s'. Activum manebit dum Realm carum activum erit.", "mco.snapshot.tooltip": "Utere Realms in versionibus praecocibus ut versiones Minecraft noviores videas, quae fortasse novas qualitates et alias mutationes habebit.\n\nInvenire potes Realms normales in versionibus ludi missis.", "mco.snapshotRealmsPopup.message": "Realms nunc ludi possunt in versionibus praecocibus a versione praecoce XXIIIwXLIa. Omnis subscriptio Realms habet Realm in versione praecoce quod separatum est a Realm normale!", "mco.snapshotRealmsPopup.title": "Realms nunc ludi possunt in versionibus praecocibus", "mco.snapshotRealmsPopup.urlText": "Magis discere", "mco.template.button.publisher": "Editor", "mco.template.button.select": "Selige", "mco.template.button.trailer": "Introductio", "mco.template.default.name": "<PERSON><PERSON><PERSON> el<PERSON> (Arbitrarius)", "mco.template.info.tooltip": "Paginae interretialis editor", "mco.template.name": "Modus", "mco.template.select.failure": "Catenam supplementorum huius generis reperire non potuimus. Queso inspice conexum tuum, vel deinde iterum conare.", "mco.template.select.narrate.authors": "Scriptores: %s", "mco.template.select.narrate.version": "versio %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, hic ordo rerum vacuus est.\nRefer posterius quaerens novas res, aut si creator es,\n%s.", "mco.template.select.none.linkTitle": "considera tute aliquid mittare", "mco.template.title": "Formae mundi", "mco.template.title.minigame": "<PERSON><PERSON> parvi", "mco.template.trailer.tooltip": "<PERSON>rae<PERSON><PERSON> chartae", "mco.terms.buttons.agree": "Assentior", "mco.terms.buttons.disagree": "Non assentior", "mco.terms.sentence.1": "Annuo ad Minecraft Realms", "mco.terms.sentence.2": "Leges servitiorum", "mco.terms.title": "Leges de operibus Realms", "mco.time.daysAgo": "abhinc %1$s dies", "mco.time.hoursAgo": "abhinc %1$s hora(e)", "mco.time.minutesAgo": "abhinc %1$s minuta(e)", "mco.time.now": "nunc", "mco.time.secondsAgo": "abhinc %1$s secunda(e)", "mco.trial.message.line1": "Vis tuum Realm adipisci?", "mco.trial.message.line2": "Ad plus informationum hic ice!", "mco.upload.button.name": "Transmitte", "mco.upload.cancelled": "Transmissio rescissa est", "mco.upload.close.failure": "Realm tuum claudi non potuit, iterum conare posterius", "mco.upload.done": "Mundus transmissus est", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Transmittere non potuit! (%s)", "mco.upload.failed.too_big.description": "Mundus selectus nimis magnus est. Magnitudo maxima concessa est %s.", "mco.upload.failed.too_big.title": "Mundus nimis magnus est", "mco.upload.hardcore": "Mundi extremi imponi non possunt!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON>us tuus paratur", "mco.upload.select.world.none": "Nulli mundi unius lusoris inventi sunt!", "mco.upload.select.world.subtitle": "Elige mundum ludi unius ut mittas", "mco.upload.select.world.title": "Mundum mitte", "mco.upload.size.failure.line1": "'%s' magnus nimium est!", "mco.upload.size.failure.line2": "Est %s. Magnitudo maximum concessum est %s.", "mco.upload.uploading": "'%s' mittitur", "mco.upload.verifying": "<PERSON><PERSON><PERSON> tuus probatur", "mco.version": "Versio: %s", "mco.warning": "Cave!", "mco.worldSlot.minigame": "Ludus parvus", "menu.custom_options": "Optiones consuetudinis...", "menu.custom_options.title": "Optiones consuetudinis", "menu.custom_options.tooltip": "Attende: Optiones consuetudinis a moderatris partium tertiarum ac/vel re praebentur.\nCaute utendum est!", "menu.custom_screen_info.button_narration": "Hoc est simulacrum consuetudinis. Disce plus.", "menu.custom_screen_info.contents": "Res huius simulacri a moderatris partium tertiarum et chartis reguntur, quae nec a Mojang Studios nec a Microsoft possidentur, administrantur, vel moderantur.\n\nCaute utendum est! Cave semper cum ligamina sequeris, nec umquam informationes personales, etiam singula aditus, praebeas.\n\nSi hoc simulacrum te impedit ne ludas, etiam disiungi potes a moderatro per premendum infra.", "menu.custom_screen_info.disconnect": "Simulacrum consuetudinis reiectum est", "menu.custom_screen_info.title": "Nota de simulacris consuetudinis", "menu.custom_screen_info.tooltip": "Hoc est simulacrum consuetudinis. Ice hic ut plus discas.", "menu.disconnect": "Conexionem interrumpe", "menu.feedback": "Emendationes...", "menu.feedback.title": "Emendationes", "menu.game": "<PERSON>a ludi", "menu.modded": " (mutatus)", "menu.multiplayer": "Ludus multorum", "menu.online": "Minecraft Realms", "menu.options": "Optiones...", "menu.paused": "Ludus consistit", "menu.playdemo": "Lude mundum demonstrationis", "menu.playerReporting": "Lusorem referre", "menu.preparingSpawn": "Area natalis paratur: %s%%", "menu.quick_actions": "Actiones celeres...", "menu.quick_actions.title": "Actiones celeres", "menu.quit": "Relinque ludum", "menu.reportBugs": "Menda perscribe", "menu.resetdemo": "Refice mundum demonstrationis", "menu.returnToGame": "Redi in Ludo", "menu.returnToMenu": "Serva et exi ad titulum", "menu.savingChunks": "Partes servantur", "menu.savingLevel": "<PERSON><PERSON><PERSON> servatur", "menu.sendFeedback": "Emendationem da", "menu.server_links": "Nexus moderatrorum...", "menu.server_links.title": "Nexus moderatrorum", "menu.shareToLan": "Aperi reti locali", "menu.singleplayer": "<PERSON><PERSON> unius", "menu.working": "Laboratur…", "merchant.deprecated": "Vicani usque a duas versationes per diem resupplent.", "merchant.level.1": "Tiro", "merchant.level.2": "Discipulus", "merchant.level.3": "<PERSON><PERSON>", "merchant.level.4": "<PERSON>", "merchant.level.5": "Artifex", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Preme %1$s ut descendas", "multiplayer.applyingPack": "Compilatio supplementorum adhibetur", "multiplayer.confirm_command.parse_errors": "Iussum incognitum vel irritum efficere conaris.\nEsne certus?\nIussum: %s", "multiplayer.confirm_command.permissions_required": "Iussum quod permissiones editas requirit efficere conaris.\nHoc fortasse ludum tuum male afficit.\nEsne certus?\nIussum: %s", "multiplayer.confirm_command.title": "Confirma effectum iussi", "multiplayer.disconnect.authservers_down": "Certificandi servitores inexpediti sunt. <PERSON><PERSON><PERSON><PERSON>, postea iterum coneris, nos paenitet!", "multiplayer.disconnect.bad_chat_index": "Deprehendi ab moderatrum nuntium praeteritum aut inordinatum", "multiplayer.disconnect.banned": "Tu ex hoc moderatro vetitus(a) es", "multiplayer.disconnect.banned.expiration": "\n%s vetitio tua amovebitur", "multiplayer.disconnect.banned.reason": "Ex hoc moderatro vetitus(a) est.\nCausa: %s", "multiplayer.disconnect.banned_ip.expiration": "\nInterdictum tuum in %s removebitur", "multiplayer.disconnect.banned_ip.reason": "Inscriptio FI tua de hoc moderatro vetita est.\nCausa: %s", "multiplayer.disconnect.chat_validation_failed": "Nuntium locutorii verificare non potuit", "multiplayer.disconnect.duplicate_login": "Aliunde intravisti", "multiplayer.disconnect.expired_public_key": "Clavis puplica personae exspirata est. Inspice ut tempus systematis tui synchronizatus sit, et ludum claudere iterumque aperire conare.", "multiplayer.disconnect.flying": "In hoc moderatro volare non potest", "multiplayer.disconnect.generic": "Connectio interrupta", "multiplayer.disconnect.idling": "Vacabas nimium diu!", "multiplayer.disconnect.illegal_characters": "Figuras illicitas in nuntio misisti", "multiplayer.disconnect.incompatible": "Cliens non convenit! Quaeso, %s utaris", "multiplayer.disconnect.invalid_entity_attacked": "Temptavit entitatem irritam oppugnare", "multiplayer.disconnect.invalid_packet": "Moderatrum fascem irritum misit", "multiplayer.disconnect.invalid_player_data": "<PERSON> lusoris irrita sunt", "multiplayer.disconnect.invalid_player_movement": "Acceptus fascis irritus ad lusorem movendum", "multiplayer.disconnect.invalid_public_key_signature": "Clavis publica irrite subscripta est.\nLudum claudere et iterum aperire conare.", "multiplayer.disconnect.invalid_public_key_signature.new": "Clavis publica irrite subscripta est.\nLudum claudere et iterum aperire conare.", "multiplayer.disconnect.invalid_vehicle_movement": "Acceptus fascis irritus ad vehiculum movendum", "multiplayer.disconnect.ip_banned": "FI tua in hoc moderatro vetita est", "multiplayer.disconnect.kicked": "Administrator te expulit", "multiplayer.disconnect.missing_tags": "Series notarum, a moderatro recepta, incompleta est. Continge administratorem moderatri.", "multiplayer.disconnect.name_taken": "Nomen illud iamiam optatum est", "multiplayer.disconnect.not_whitelisted": "In indice albo huius moderatri non es!", "multiplayer.disconnect.out_of_order_chat": "<PERSON>ascis locutorii inordinate receptus est. Tempusne systematis tui mutatum est?", "multiplayer.disconnect.outdated_client": "Cliens obsoletus! Uti %s", "multiplayer.disconnect.outdated_server": "Cliens obsoletus! Uti %s", "multiplayer.disconnect.server_full": "Moderatrum est plenum!", "multiplayer.disconnect.server_shutdown": "Moderatrum clausum est", "multiplayer.disconnect.slow_login": "Es intrare nimium conatus diu", "multiplayer.disconnect.too_many_pending_chats": "Nimia nuntia locutorii inrecognita", "multiplayer.disconnect.transfers_disabled": "Moderatrum translata non accipit", "multiplayer.disconnect.unexpected_query_response": "Data consuetudinis inopinata ex cliente", "multiplayer.disconnect.unsigned_chat": "Fascis locutorii sine subscriptione vel cum irrita subscriptione receptus est.", "multiplayer.disconnect.unverified_username": "Nomen verificare non potuit!", "multiplayer.downloadingStats": "Statisticae reciperantur...", "multiplayer.downloadingTerrain": "Ager construitur...", "multiplayer.lan.server_found": "Novum moderatrum inventum est: %s", "multiplayer.message_not_delivered": "Muttered nuntium at locutorium non potes, quere trabes moderatri: %s", "multiplayer.player.joined": "%s ludum iunxit", "multiplayer.player.joined.renamed": "%s, antea cognitus %s nomine, ad lusum se coniunxit", "multiplayer.player.left": "%s ludum exiit", "multiplayer.player.list.hp": "%ssalus", "multiplayer.player.list.narration": "Lusores qui adest: %s", "multiplayer.requiredTexturePrompt.disconnect": "Moderatrum quaerit compilationem supplementorum", "multiplayer.requiredTexturePrompt.line1": "Hoc moderatrum usum compilationis supplementorum coercit.", "multiplayer.requiredTexturePrompt.line2": "Si aspernatus erit hanc compilationem supplementorum, connectio cum hoc moderatro interrumpetur.", "multiplayer.socialInteractions.not_available": "Optionibus sociorum tantum in mundis ubi ludis cum aliis uti potes", "multiplayer.status.and_more": "... et %s plures ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Moderatro conecti non possum", "multiplayer.status.cannot_resolve": "Nomen hospitis decernere non potest", "multiplayer.status.finished": "<PERSON><PERSON>", "multiplayer.status.incompatible": "Versio obsoleta!", "multiplayer.status.motd.narration": "Nuntium diei: %s", "multiplayer.status.no_connection": "(nullus conexus)", "multiplayer.status.old": "Vetus", "multiplayer.status.online": "Activum", "multiplayer.status.ping": "%s millesimae partes secundi", "multiplayer.status.ping.narration": "Resonantia %s millesimi secundorum", "multiplayer.status.pinging": "Conexus comprobatur...", "multiplayer.status.player_count": "%s ex %s", "multiplayer.status.player_count.narration": "%s ex %s lusores adsunt", "multiplayer.status.quitting": "Exeo", "multiplayer.status.request_handled": "Status petitio accepta", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Iniussum statum receptus est", "multiplayer.status.version.narration": "Moderatri versio: %s", "multiplayer.stopSleeping": "E lecto egredere", "multiplayer.texturePrompt.failure.line1": "Compilatio supplementorum moderatri adhiberi non potuit", "multiplayer.texturePrompt.failure.line2": "Quidquid supplementa propria requirit necopinato operare posset", "multiplayer.texturePrompt.line1": "Hoc moderatrum suadet ut compilatione propria supplementorum utaris.", "multiplayer.texturePrompt.line2": "Visne eam extrahere et magice instituere?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nNuntium de moderatro:\n%s", "multiplayer.title": "Lude cum aliis", "multiplayer.unsecureserver.toast": "Nuntia quae in hoc moderatro mittuntur mutari possunt nec nuntiorum verba eadem quae is qui nuntium misit scripsit semper erunt", "multiplayer.unsecureserver.toast.title": "Nuntia in hoc moderatro certa non sunt", "multiplayerWarning.check": "Ne hoc simulacrum iterum ostendatur", "multiplayerWarning.header": "Attende: tertiae partis ludus interretialis", "multiplayerWarning.message": "Attentio: <PERSON><PERSON> in linea est oblatus ab servitoribus partium tertiarum, quae a Mojang Studios vel Microsoft non possidentur, laborantur, vel aspectantur. Inter ludum in linea ad nuntios non moderatos sermonis vel typos alios contentorum generatorum ab utenibus, qui non sint apti omnibus, exponaris.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Premendus: %s", "narration.button.usage.focused": "Preme Mitte ut id activum facias", "narration.button.usage.hovered": "Ice sinistrum ad id activum faciendum", "narration.checkbox": "Quadrum signandum: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON> ut permutes", "narration.checkbox.usage.hovered": "Ice sinistrum ad permutandum", "narration.component_list.usage": "Preme Tab ut ad rem proximam eas", "narration.cycle_button.usage.focused": "Preme Mitte ut id pro %s permutes", "narration.cycle_button.usage.hovered": "Ice sinistrum ad id pro %s mutandum", "narration.edit_box": "Quadrum ad scribendum: %s", "narration.item": "Res: %s", "narration.recipe": "Praeceptum ut %s fabricetur", "narration.recipe.usage": "Ice sinistrum ad seligendum", "narration.recipe.usage.more": "Ice dextrum ad cetera praecepta monstranda", "narration.selection.usage": "Preme claves sursum vel deorsum ut ad aliam rem eas", "narration.slider.usage.focused": "Preme claves sinistrorsum vel dextrorsum ad valorem permutandum", "narration.slider.usage.hovered": "Trahe regulatorem ad valorem mutandum", "narration.suggestion": "Suggestio selecta est %s ex %s: %s", "narration.suggestion.tooltip": "Suggestio selecta est %s ex %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Preme Tab ut moveas ad proximam suggestionem", "narration.suggestion.usage.cycle.hidable": "Preme Tab ut moveas ad proximam suggestionem, vel Fuga ut suggestiones relinquas", "narration.suggestion.usage.fill.fixed": "<PERSON>me <PERSON>b ut hac suggestione utaris", "narration.suggestion.usage.fill.hidable": "Preme <PERSON>b ut hac suggestione utaris, vel Fuga ut suggestiones relinquas", "narration.tab_navigation.usage": "Dum Control premis, preme Tab ut ad aliam paginam eas", "narrator.button.accessibility": "Accessibilitas", "narrator.button.difficulty_lock": "<PERSON><PERSON>s", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Non clausa", "narrator.button.language": "<PERSON><PERSON>", "narrator.controls.bound": "%s adstrictus est clavi %s", "narrator.controls.reset": "Premendum %s reinitiare", "narrator.controls.unbound": "%s adstrictus non est", "narrator.joining": "Iungis", "narrator.loading": "%s imponitur", "narrator.loading.done": "Factum", "narrator.position.list": "Versus indicis %s ex %s selectus est", "narrator.position.object_list": "Res versus %s ex %s selecta est", "narrator.position.screen": "Pars simulacri %s ex %s", "narrator.position.tab": "Sectio %s ex %s selecta est", "narrator.ready_to_play": "Promptus ad ludere", "narrator.screen.title": "Simulacrum principale", "narrator.screen.usage": "Utere digito muris vel premendo Tab ad seligendam rem", "narrator.select": "Selectus: %s", "narrator.select.world": "%s selectus est, in quo novissime lusisti die %s, %s, %s, versio: %s", "narrator.select.world_info": "%s selectum est, in %s, %s ludisti", "narrator.toast.disabled": "Narrator inactivus", "narrator.toast.enabled": "Narrator activus", "optimizeWorld.confirm.description": "Hoc mundum tuum optimum facere conabitur, ut omnia data forma ludi recentissima serventur. Hoc plurimum temporis consumere poterit, secundum mundum tuum. Eo facto, in mundo tuo celerius ludere poterit, neque iam ad vetustiores ludi versiones conveniet. Esne certus te procedere velle?", "optimizeWorld.confirm.proceed": "Facto exemplari fac optimum mundum", "optimizeWorld.confirm.title": "Optimum fac mundum", "optimizeWorld.info.converted": "Partes emendatae: %s", "optimizeWorld.info.skipped": "Partes exsuperatae: %s", "optimizeWorld.info.total": "Cunctae partes: %s", "optimizeWorld.progress.counter": "%s ex %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Partes numerantur...", "optimizeWorld.stage.failed": "Defecit! :(", "optimizeWorld.stage.finished": "Mundus optimus factus est...", "optimizeWorld.stage.finished.chunks": "Partium forma recentior facta est...", "optimizeWorld.stage.finished.entities": "Entitatum forma recentior facta est...", "optimizeWorld.stage.finished.poi": "Locorum quaerendorum forma recentior facta est...", "optimizeWorld.stage.upgrading": "Omnium forma recentior fit...", "optimizeWorld.stage.upgrading.chunks": "Omnium partium forma recentior fit...", "optimizeWorld.stage.upgrading.entities": "Omnium entitatum forma recentior fit...", "optimizeWorld.stage.upgrading.poi": "Omnium locorum quaerendorum forma recentior fit...", "optimizeWorld.title": "Mundus '%s' optimus fit", "options.accessibility": "Optiones accessabilitatis...", "options.accessibility.high_contrast": "Disparan<PERSON>", "options.accessibility.high_contrast.error.tooltip": "Disparantia Magna comp. suppl. ad manum non est.", "options.accessibility.high_contrast.tooltip": "Disparantiam elementi UI amplificat.", "options.accessibility.high_contrast_block_outline": "Circumscriptiones cubi magnae disparantiae", "options.accessibility.high_contrast_block_outline.tooltip": "Circumscriptionem disparantiam cubi de cubo electo amplificat.", "options.accessibility.link": "Dux accessabilitatis", "options.accessibility.menu_background_blurriness": "Obscuritas fundorum chartarum", "options.accessibility.menu_background_blurriness.tooltip": "Obscuritatem fundorum chartarum mutat.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Narrator fieri activum vel inactivum potest cum 'Cmd+B' utaris.", "options.accessibility.narrator_hotkey.tooltip": "Narrator fieri activum vel inactivum potest cum 'Ctrl+B' utaris.", "options.accessibility.panorama_speed": "Prospectus celeritas rotae muris", "options.accessibility.text_background": "Fundus scripti", "options.accessibility.text_background.chat": "In locutorio", "options.accessibility.text_background.everywhere": "Ubique", "options.accessibility.text_background_opacity": "Opacitas fundi scripti", "options.accessibility.title": "Optiones accessabilitatis", "options.allowServerListing": "Nomen in indicibus", "options.allowServerListing.tooltip": "Quaedam moderatra indicant nomina lusorum eo conexorum in statu publico suo.\nSi haec optio inactiva est, nomen tuus in his indicibus non monstrabitur.", "options.ao": "Lux glabrae", "options.ao.max": "Maxima", "options.ao.min": "<PERSON><PERSON>", "options.ao.off": "<PERSON><PERSON><PERSON>", "options.attack.crosshair": "Monstratorem", "options.attack.hotbar": "Res vectis", "options.attackIndicator": "Indicator iniuriae", "options.audioDevice": "Audio fabrica", "options.audioDevice.default": "Normale systematis", "options.autoJump": "Statim salire", "options.autoSuggestCommands": "Suggestiones de iussis", "options.autosaveIndicator": "Signum ludum servari", "options.biomeBlendRadius": "Mixtura inter biomata", "options.biomeBlendRadius.1": "INACTIVUM (celerrimum)", "options.biomeBlendRadius.11": "11x11 (extremus)", "options.biomeBlendRadius.13": "13x13 (salaco)", "options.biomeBlendRadius.15": "15x15 (maximus)", "options.biomeBlendRadius.3": "3x3 (celer)", "options.biomeBlendRadius.5": "5x5 (medius)", "options.biomeBlendRadius.7": "7x7 (altus)", "options.biomeBlendRadius.9": "9x9 (altior)", "options.chat": "Optiones locutorii...", "options.chat.color": "Colores", "options.chat.delay": "Temp. inter nuntios: %s sec.", "options.chat.delay_none": "Temp. inter nuntios: nullum", "options.chat.height.focused": "<PERSON><PERSON><PERSON> moderata", "options.chat.height.unfocused": "Altitudo immoderata", "options.chat.line_spacing": "Distantia inter versos", "options.chat.links": "Ligamina interretis", "options.chat.links.prompt": "Roga de ligaminibus", "options.chat.opacity": "Opacitas scripti loc.", "options.chat.scale": "Magnitudo scripti loc.", "options.chat.title": "Optiones locutorii", "options.chat.visibility": "Locutorium", "options.chat.visibility.full": "Ostenditur", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON><PERSON> tantum", "options.chat.width": "Latitudo", "options.chunks": "%s partes", "options.clouds.fancy": "Elegantia", "options.clouds.fast": "<PERSON><PERSON><PERSON>", "options.controls": "Frena...", "options.credits_and_attribution": "Auctores...", "options.damageTiltStrength": "Inclinatio vulneris", "options.damageTiltStrength.tooltip": "Magnitudo tremoris camerae propter inuriam.", "options.darkMojangStudiosBackgroundColor": "Signum unius coloris", "options.darkMojangStudiosBackgroundColor.tooltip": "Nerum facit fundum simulacri impositionis cum signum Mojang Studios.", "options.darknessEffectScale": "Tenebrae vibrant", "options.darknessEffectScale.tooltip": "Moderatur pulsus effectus Tenebrarum a vigile vel sculk clamante effecti.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "Facilis", "options.difficulty.easy.info": "Mobilia hostilia creantur sed minus nocent. Famis metrica deminuit et usque ad cordes quinque exhaurit.", "options.difficulty.hard": "Difficilis", "options.difficulty.hard.info": "Mobilia hostilia creantur et plus nocent. Famis metrica deminuit et omnes cordes exhaurit.", "options.difficulty.hardcore": "Extrema", "options.difficulty.normal": "Mediocris", "options.difficulty.normal.info": "Mobilia hostilia creantur et solite nocent. Famis metrica deminuit et usque ad cordis unum partem exhaurit.", "options.difficulty.online": "Difficultas moderatri", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Mobilia hostilia non creantur et quidam mobilia media creantur. Famis metrica non deminuit et salus gradatim regenerabitur.", "options.directionalAudio": "Sonus directionalis", "options.directionalAudio.off.tooltip": "Sonus stereophonicus normalis.", "options.directionalAudio.on.tooltip": "Utitur sonis directionalibus HRTF ut soni similius trium dimensionum videantur. Tibi supelectilibus sonoris utendum est qui ad HRTF conveniant, atque ut auscultabulis utaris suademus.", "options.discrete_mouse_scroll": "Excursio discretus", "options.entityDistanceScaling": "Distantia entitatum", "options.entityShadows": "Umbrae entitatum", "options.font": "Optiones scripturae...", "options.font.title": "Optiones scripturae", "options.forceUnicodeFont": "Scriptura Unicode ubique utere", "options.fov": "<PERSON><PERSON> visus", "options.fov.max": "Visus peritorum", "options.fov.min": "Normalis", "options.fovEffectScale": "Effectus campi visionis", "options.fovEffectScale.tooltip": "Moderatur magnitudinem variationis anguli visus effectibus ludi.", "options.framerate": "%s fps", "options.framerateLimit": "Maxime replicircuitio", "options.framerateLimit.max": "Infinitus", "options.fullscreen": "Simulacrum plenum", "options.fullscreen.current": "Adest", "options.fullscreen.entry": "%sx%s@%s (%sbiti)", "options.fullscreen.resolution": "Definitio simulacri pleni", "options.fullscreen.unavailable": "Optio non praesto", "options.gamma": "Candor", "options.gamma.default": "Normalis", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Ten<PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON> celeritas", "options.glintSpeed.tooltip": "Moderatur cum quanta celeritate nitor visualis renidet per res incantatas.", "options.glintStrength": "<PERSON><PERSON><PERSON> robur", "options.glintStrength.tooltip": "Moderatur quantus perlucidus nitor visualis est in rebus incantatis.", "options.graphics": "Res graphicae", "options.graphics.fabulous": "Mirabilia!", "options.graphics.fabulous.tooltip": "Visualia %s usa sunt umbrantes scrinii ut tempestae, nubes particulaeque describentur.\nHic posset graviter afficere transacta portatilibus commentis scriniisque quattuor miliorum elementorum.", "options.graphics.fancy": "Elegantia", "options.graphics.fancy.tooltip": "Visualia elegantia exaequant praestantiam munditia multis machinis.\nTempestae, nubes particulaeque possent non apparere post cubos perlucidos vel aquam.", "options.graphics.fast": "Frugalia", "options.graphics.fast.tooltip": "Visualia frugalia deminuent numerum visibilis imbris nivisque.\nPerluciditas inactivus est multis cubis (e. g. foliis arboris).", "options.graphics.warning.accept": "Procede sine susceptione", "options.graphics.warning.cancel": "Redi rursum", "options.graphics.warning.message": "Tabulam visualium tuam detectam non suscipit visualia %s\n\nPotes ignorare hunc nuntium, sed nulla susceptio dabitur pro computatro tuo si visualia %s uti optaveris.", "options.graphics.warning.renderer": "Descriptor detectus: [%s]", "options.graphics.warning.title": "Tabula visualium non suscipitur", "options.graphics.warning.vendor": "Venditor detectus: [%s]", "options.graphics.warning.version": "Versio OpenGL detecta: [%s]", "options.guiScale": "Magnitudo GUI", "options.guiScale.auto": "Automatica", "options.hidden": "Celatum", "options.hideLightningFlashes": "<PERSON>la fulgorem fulminum", "options.hideLightningFlashes.tooltip": "Facit ut caelus non fulgeat cum fulminat. Fulmina visibilia manebunt.", "options.hideMatchedNames": "Cela nomina congrua", "options.hideMatchedNames.tooltip": "Aliqua moderatra tertiarum partium nuntia locutoris inusitatis formis mittunt.\nHac optione activa, celandi lusores nominibus missorum cognoscentur.", "options.hideSplashTexts": "Abscondite textus aspersos", "options.hideSplashTexts.tooltip": "Abscondit textum flavum aspersum in charta principe.", "options.inactivityFpsLimit": "Minue FPS dum", "options.inactivityFpsLimit.afk": "lusor absit", "options.inactivityFpsLimit.afk.tooltip": "Replicircuitio ad XXX limitatur dum ludus intrationem nullam a lusore per plus quam unam minutam accipiat. Post IX minutas alias, ad X ulterius limitatur.", "options.inactivityFpsLimit.minimized": "fenestra ludi celetur", "options.inactivityFpsLimit.minimized.tooltip": "Replicircuitio limitatur solum dum fenestra ludi celetur.", "options.invertMouse": "Inverta murem", "options.japaneseGlyphVariants": "Scribe Iaponico more", "options.japaneseGlyphVariants.tooltip": "Figuras Sinenses Iaponicas Coreanasque Iaponico more scribe", "options.key.hold": "Tene", "options.key.toggle": "<PERSON><PERSON>", "options.language": "Lingua...", "options.language.title": "<PERSON><PERSON>", "options.languageAccuracyWarning": "(Conversiones e lingua Anglica in alias linguas tote accuratae non esse possunt)", "options.languageWarning": "Conversiones e lingua Anglica in alias linguas tote accuratae non esse possunt", "options.mainHand": "<PERSON><PERSON> prior", "options.mainHand.left": "Sinistra", "options.mainHand.right": "<PERSON><PERSON>", "options.mipmapLevels": "<PERSON><PERSON><PERSON> chartae MI<PERSON>", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "Petasus", "options.modelPart.jacket": "Tunica", "options.modelPart.left_pants_leg": "<PERSON><PERSON><PERSON> sinistra", "options.modelPart.left_sleeve": "Manica sinistra", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON> dextra", "options.modelPart.right_sleeve": "Manica dextra", "options.mouseWheelSensitivity": "Sensitivitas rotae muris", "options.mouse_settings": "Optiones muris...", "options.mouse_settings.title": "Optiones muris", "options.multiplayer.title": "Optiones ludi multorum...", "options.multiplier": "%sx", "options.music_frequency": "Frequentatio musicae", "options.music_frequency.constant": "<PERSON><PERSON><PERSON>", "options.music_frequency.default": "Normalis", "options.music_frequency.frequent": "Frequens", "options.music_frequency.tooltip": "Quam frequenter musica canat mutat dum in mundo ludi.", "options.narrator": "Narrator", "options.narrator.all": "Omnia narrat", "options.narrator.chat": "Loc. narrat", "options.narrator.notavailable": "Non disponibilis", "options.narrator.off": "INACTIVUM", "options.narrator.system": "<PERSON><PERSON> narrat", "options.notifications.display_time": "Tempus notificationum", "options.notifications.display_time.tooltip": "Spatium temporis afficit ut omnes notificationes in simulacro visibiles maneant.", "options.off": "INACTIV.", "options.off.composed": "%s: INACTIVUM", "options.on": "ACTIVUM", "options.on.composed": "%s: ACTIVUM", "options.online": "Ludus interretialis...", "options.online.title": "Optiones ludi interretialis", "options.onlyShowSecureChat": "Nuntia certa tantum", "options.onlyShowSecureChat.tooltip": "Tantum nuntia ab aliis histriones ostenta quae verificari possunt ab illo lusore missas esse nec mutatae sunt.", "options.operatorItemsTab": "Sectio rerum operatoris", "options.particles": "Particulae", "options.particles.all": "Omnes", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "<PERSON>uam paucissimae", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %s ep", "options.prioritizeChunkUpdates": "Constructio partium", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON> detine<PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Quaedam agere, sicut cubum ponere vel destruere, faciet ut subito partes iterum construantur.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON> detinens", "options.prioritizeChunkUpdates.nearby.tooltip": "Partes vicinae semper statim construuntur. Hoc cum cubi ponuntur vel destruuntur efficacitatem ludi afficere potest.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Partes vicinae parallele construuntur. Hoc cum cubi destruuntur brevia vacua visualia efficere potest.", "options.rawMouseInput": "Intratio Cruda", "options.realmsNotifications": "Realms nuntia invitationesque", "options.realmsNotifications.tooltip": "Nuntia invitationesque Realms adipiscitur in simulacro principale et imagines suas in premendo Realms ponit.", "options.reducedDebugInfo": "Min. index ad emendandum", "options.renderClouds": "Nubes", "options.renderCloudsDistance": "Intervallum nubis", "options.renderDistance": "Ampl. spatii visibilis", "options.resourcepack": "Compil. supplementorum...", "options.rotateWithMinecart": "Rota cum plaustris", "options.rotateWithMinecart.tooltip": "Lusorne circumspiciat cum plaustrum se vertat. Hac optione tantum uti potes in mundis cum experimento plaustrorum meliorum.", "options.screenEffectScale": "Effectus distortionum", "options.screenEffectScale.tooltip": "Magnitudo detortionis effectu nausia et porta Nether.\nCum valor decrescat, viridis corona effectu nausia substituitur.", "options.sensitivity": "Sensitivitas", "options.sensitivity.max": "VELOCITAS MAXIMA!!!", "options.sensitivity.min": "*oscitatio*", "options.showNowPlayingToast": "Ostende nuntia subita musicae", "options.showNowPlayingToast.tooltip": "Quandocumque cantus cantare incipit, nuntium subitum ostendit. Idem nuntium in electione consistendi adsidue ostenditur dum cantus cantat.", "options.showSubtitles": "Ostende verba", "options.simulationDistance": "Ampl. spatii activi", "options.skinCustomisation": "Immutatio faciei...", "options.skinCustomisation.title": "Immutatio faciei", "options.sounds": "Soni et musica...", "options.sounds.title": "Optiones musicae sonorumque", "options.telemetry": "Data telemetrica...", "options.telemetry.button": "Collectio datorum", "options.telemetry.button.tooltip": "\"%s\" solum datum necessarium includit.\n\"%s\" alterem etiam datum requisitum includit.", "options.telemetry.disabled": "Telemetria inactiva est.", "options.telemetry.state.all": "Omnes", "options.telemetry.state.minimal": "Minimus", "options.telemetry.state.none": "<PERSON><PERSON><PERSON>", "options.title": "Optiones", "options.touchscreen": "Modus simulacri tangibilis", "options.video": "Optiones visuales...", "options.videoTitle": "Optiones visuales", "options.viewBobbing": "<PERSON><PERSON><PERSON> nutans", "options.visible": "Visibil<PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft memoria deest, fortasse ob mendum in ludo, fortasse cum Java Virtual Machine satis memoriae datum non est.\n\nNe mundus corrumperetur, hic ludus clausus est. Satis memoriae expedire ut ad optiones tituli redire posset conati sumus, sed id fortasse prospere non evenit.\n\nSi hoc nuntium iterum vides, ludum claude et iterum aperi.", "outOfMemory.title": "Memoria deest!", "pack.available.title": "Utiles", "pack.copyFailure": "Compilationem transcribere non potui", "pack.dropConfirm": "Visne addere has compilationes ad Minecraft?", "pack.dropInfo": "Trahi demitteque scapos in hanc fenestram ut compilationes addes", "pack.dropRejected.message": "Has rationes irritae sunt et non replicatae sunt: %s", "pack.dropRejected.title": "Rationes quae non comp. sunt", "pack.folderInfo": "(Hic pone scapos comp. dat.)", "pack.incompatible": "Obsoletus", "pack.incompatible.confirm.new": "Haec compilatio versioni Minecraftariae recentiori fiebat et non iam recte fungatur.", "pack.incompatible.confirm.old": "Haec compilatio versioni Minecraftariae vetustiori fiebat et non iam recte fungatur.", "pack.incompatible.confirm.title": "Visne vero hanc compilationem imponere?", "pack.incompatible.new": "(Facta editioni Minecraftariae recentiori)", "pack.incompatible.old": "(Facta editioni Minecraftariae vetustiori)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Aperi scapum comp.", "pack.selected.title": "Selectae", "pack.source.builtin": "insita", "pack.source.feature": "qualitas", "pack.source.local": "localis", "pack.source.server": "<PERSON><PERSON><PERSON>", "pack.source.world": "mundus", "painting.dimensions": "Alta cubos %s lata %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanica", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON>rt<PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroca", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Inimicus tormentis verberatus est", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Flores", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Cranium ardens", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Protome", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON><PERSON> antri", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON><PERSON>, domine <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Terra", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Inimicus finis", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Filix", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Inventio", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Chebeb cum tribus capsicis", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Imaneb<PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Sulphuratum", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatio", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Orbis", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "U<PERSON>laci<PERSON><PERSON>", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Via", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON><PERSON> ovata", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Index", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Stagnum", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Aquae", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON><PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON>", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON>varia rosaeque", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Scaena parata est", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "occasus_solis", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Deponitum", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Desertum", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Aqua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Pictura fortuita", "parsing.bool.expected": "Boolean exspectabatur", "parsing.bool.invalid": "Boolean irritus est, 'vera' aut 'falsa' exspectabatur sed '%s' invenit", "parsing.double.expected": "Numerus decimarius altero accuratior exspectabatur", "parsing.double.invalid": "Numerus decimarius altero accuratior '%s' irritus est", "parsing.expected": "'%s' exspectabatur", "parsing.float.expected": "Numerus cum commate mutante exspectabatur", "parsing.float.invalid": "Numerus cum commate mutante irritus '%s'", "parsing.int.expected": "Numerus plenus exspectabatur", "parsing.int.invalid": "Numerus plenus '%s' irritus est", "parsing.long.expected": "<PERSON><PERSON> exspectabatur", "parsing.long.invalid": "Irritus longus '%s'", "parsing.quote.escape": "Irrita pars ignoranda '\\%s' in scripto", "parsing.quote.expected.end": "Scriptum conclude signo citationis", "parsing.quote.expected.start": "Utere signo citationis initio scripti", "particle.invalidOptions": "Interpretari optiones particularum non potui: %s", "particle.notFound": "Haec particula ignota est: %s", "permissions.requires.entity": "Entitas necesse est ut hoc iussum hic efficiatur", "permissions.requires.player": "Lusor necesse est ut hunc iussum hic efficiatur", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Effectus:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Hoc praedicatum ignotum est: %s", "quickplay.error.invalid_identifier": "Mundum cum identificatore praebito invenire non potuit", "quickplay.error.realm_connect": "Realm conecti non potui", "quickplay.error.realm_permission": "Expers permissionis adiungere ad Realm", "quickplay.error.title": "Ludere celeriter non potuit", "realms.configuration.region.australia_east": "Nova Cambria Australis, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasilia", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iova, CFA", "realms.configuration.region.east_asia": "Hongcongum", "realms.configuration.region.east_us": "Virginia, CFA", "realms.configuration.region.east_us_2": "Carolina Septentrionalis, CFA", "realms.configuration.region.france_central": "Francia", "realms.configuration.region.japan_east": "Iaponia orientalis", "realms.configuration.region.japan_west": "Iapon<PERSON> occidentalis", "realms.configuration.region.korea_central": "Corea Meridiana", "realms.configuration.region.north_central_us": "Illinoesia, CFA", "realms.configuration.region.north_europe": "Hibernia", "realms.configuration.region.south_central_us": "Texia, CFA", "realms.configuration.region.southeast_asia": "Singapura", "realms.configuration.region.sweden_central": "Svet<PERSON>", "realms.configuration.region.uae_north": "Principatus <PERSON> F<PERSON>ati (PAF)", "realms.configuration.region.uk_south": "Anglia meridiana", "realms.configuration.region.west_central_us": "Uta, CFA", "realms.configuration.region.west_europe": "Nederlandia", "realms.configuration.region.west_us": "California, CFA", "realms.configuration.region.west_us_2": "Vasintonia, CFA", "realms.configuration.region_preference.automatic_owner": "Automatus (resonantia eri Realm)", "realms.configuration.region_preference.automatic_player": "Automatus (primus sessionem iungere)", "realms.missing.snapshot.error.text": "Realms in versionibus praecocibus adhuc non suscipitur", "recipe.notFound": "Hoc praeceptum ignotum est: %s", "recipe.toast.description": "Quaere librum praeceptorum tuum", "recipe.toast.title": "Nova praecepta reserata!", "record.nowPlaying": "Reproducens: %s", "recover_world.bug_tracker": "Mendum perscribe", "recover_world.button": "Temptare recuperare", "recover_world.done.failed": "Recuperare mundum e statu priore non potuit.", "recover_world.done.success": "Recuperatio secunda est!", "recover_world.done.title": "Recuperatio perfecta est", "recover_world.issue.missing_file": "Scapum deest", "recover_world.issue.none": "Nullae quaestiones", "recover_world.message": "Haec evenerunt dum scrinium mundi \"%s\" legere conor.\nFortasse mundus restitui potest e statu vetustiore. Hoc mendum in indice mendorum perscribere potes.", "recover_world.no_fallback": "Nullus status e qua recuperari potest est", "recover_world.restore": "Temptare recuperare", "recover_world.restoring": "Temptat mundum recuperare...", "recover_world.state_entry": "Status a %s: ", "recover_world.state_entry.unknown": "ignotum", "recover_world.title": "Construere mundum non potuit", "recover_world.warning": "Construere mundi rationem non potuit", "resourcePack.broken_assets": "INSTRUMENTA PERDITA DEPREHENDUNTUR", "resourcePack.high_contrast.name": "Disparan<PERSON>", "resourcePack.load_fail": "Compilationes supplementorum reimponere non potuit", "resourcePack.programmer_art.name": "Prisca pictura", "resourcePack.runtime_failure": "Error compilationum supplementorum detectus est", "resourcePack.server.name": "Mundi propria supplementa", "resourcePack.title": "Selige compilationes supplementorum", "resourcePack.vanilla.description": "Minecraft consuetus aspectus", "resourcePack.vanilla.name": "Normalis", "resourcepack.downloading": "Compilatio supplementorum extrahitur", "resourcepack.progress": "Scapus extrahitur (%s MB)...", "resourcepack.requesting": "Petitum fit...", "screenshot.failure": "Imaginem servare non potui: %s", "screenshot.success": "Imago ut %s transcribitur", "selectServer.add": "Adde moderatrum", "selectServer.defaultName": "Moderatrum Minecraft", "selectServer.delete": "Dele", "selectServer.deleteButton": "Dele", "selectServer.deleteQuestion": "Visne vero hoc moderatrum delere?", "selectServer.deleteWarning": "'%s' amittetur aeternaliter! (Tempus longum!)", "selectServer.direct": "Con<PERSON>us prorsus", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(celatum)", "selectServer.refresh": "Recipera", "selectServer.select": "Adiunge moderatrum", "selectWorld.access_failure": "Inire in mundum non potes", "selectWorld.allowCommands": "Permitte fraudes", "selectWorld.allowCommands.info": "<PERSON><PERSON>a sicut /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "Erade datum coactum", "selectWorld.backupJoinConfirmButton": "Serva exemplar et impone", "selectWorld.backupJoinSkipButton": "Scio quid facio!", "selectWorld.backupQuestion.customized": "Mundi derecti non iam suscipiuntur", "selectWorld.backupQuestion.downgrade": "Mundum degradare non suscipitur", "selectWorld.backupQuestion.experimental": "Mundi cum optionibus instabilibus non suscipitur", "selectWorld.backupQuestion.snapshot": "Visne vero hunc mundum imponere?", "selectWorld.backupWarning.customized": "Hac in versione mundos derectos non suscipimus. Etiamnum hunc mundum omnibus manentibus imponere possumus, sed nova creata terra derecta non erit. Paenitet nos incommoditatis!", "selectWorld.backupWarning.downgrade": "Hic mundus nuperrime versione %s impositus est; versione %s nunc uteris. Mundum degradans corruptionem efficere potes, nec spondere possumus te mundum degradatum imponere vel in eo ludere posse. Si tamen pergere vis, quaesumus, facias exemplar.", "selectWorld.backupWarning.experimental": "Hic mundus usus est optiones instabiles quae quocumque tempore desinere operare possent. Spondere non possumus mundum imposturum vel operaturum esse. Hic sunt leones!", "selectWorld.backupWarning.snapshot": "Hic mundus in versione %s nuper lusus est; versione %s nunc uteris. Fac exemplarem ne mundus corruptis amittatur.", "selectWorld.bonusItems": "Cista gratuita", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "Convertendum est!", "selectWorld.conversion.tooltip": "Hoc mundum aperiendum est in vetustiora versione (sicut 1.6.4), ut tute convertatur", "selectWorld.create": "Crea mundum novum", "selectWorld.customizeType": "Derige", "selectWorld.dataPacks": "Comp. datorum", "selectWorld.data_read": "Legens data mundi...", "selectWorld.delete": "Dele", "selectWorld.deleteButton": "Dele", "selectWorld.deleteQuestion": "Visne vero hunc mundum delere?", "selectWorld.deleteWarning": "'%s' amittetur aeternaliter! (Tempus longum!)", "selectWorld.delete_failure": "Delere mundum non potes", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON>va exemplar", "selectWorld.edit.backupCreated": "Servatus est: %s", "selectWorld.edit.backupFailed": "Exemplar servari non potuit", "selectWorld.edit.backupFolder": "Aperi scrinium exemplarium", "selectWorld.edit.backupSize": "magnitudine: %s MB", "selectWorld.edit.export_worldgen_settings": "Exporta optiones generationis mundi", "selectWorld.edit.export_worldgen_settings.failure": "Non potui exportare", "selectWorld.edit.export_worldgen_settings.success": "Exportatae sunt", "selectWorld.edit.openFolder": "Aperi scapum mundorum", "selectWorld.edit.optimize": "Fac optimum mundum", "selectWorld.edit.resetIcon": "Restitue imago", "selectWorld.edit.save": "<PERSON><PERSON>", "selectWorld.edit.title": "<PERSON>ta mundum", "selectWorld.enterName": "Nomen mundi", "selectWorld.enterSeed": "Semen ad mundum generandum", "selectWorld.experimental": "<PERSON><PERSON>", "selectWorld.experimental.details": "Singulae res", "selectWorld.experimental.details.entry": "Res experimentalum requisitae sunt: %s", "selectWorld.experimental.details.title": "Necessarii rei experimentalis", "selectWorld.experimental.message": "Cave!\nHaec figura qualitates quas evolvunt requirit. Mundum tuum fortasse corruat vel diruat vel geratur cum renovationibus futuris.", "selectWorld.experimental.title": "<PERSON><PERSON><PERSON>um", "selectWorld.experiments": "Experimenta", "selectWorld.experiments.info": "Experimenta qualitates novae futurae sunt. Cave; res fortasse diruant. Experimenta inactiva fieri non possunt postquam mundum creatum est.", "selectWorld.futureworld.error.text": "Aliquid malum evenit inter conatum imponendi mundi ex versione futura. Hoc operatio periculosa erat; nos paenitet pro defectione.", "selectWorld.futureworld.error.title": "Error evenit!", "selectWorld.gameMode": "<PERSON><PERSON> ludi", "selectWorld.gameMode.adventure": "Expeditio", "selectWorld.gameMode.adventure.info": "Ut supervivendi modus, sed cubi neque addi neque removari possunt.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON><PERSON> modo supervivendi, sed cubi", "selectWorld.gameMode.adventure.line2": "neque addi neque deleri possunt", "selectWorld.gameMode.creative": "Fingendi", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, aedifica, et explora sine finibus. Potes volare, materia infinita habere, et a monstris non noceri.", "selectWorld.gameMode.creative.line1": "Res infinitae, volatus liberus", "selectWorld.gameMode.creative.line2": "et cubos statim destruere", "selectWorld.gameMode.hardcore": "Extremus", "selectWorld.gameMode.hardcore.info": "Mo<PERSON> supervivendi 'Difficilis' difficultatem clausus. Si moriris renasci non potes.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON><PERSON> modo supervivendi, in difficultatem", "selectWorld.gameMode.hardcore.line2": "maximam affixus solumque unam vitam habes", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "Videre potes, sed non tangere.", "selectWorld.gameMode.spectator.line1": "<PERSON>idere potes, sed non tangere", "selectWorld.gameMode.survival": "Supervivendi", "selectWorld.gameMode.survival.info": "Explora mundum arcanum in quo aedificis, adipisceris, fabricas, et monstra pugnas.", "selectWorld.gameMode.survival.line1": "Materias quaere, fabrica, acquire", "selectWorld.gameMode.survival.line2": "gradus, sanitatem et famem", "selectWorld.gameRules": "<PERSON><PERSON> ludi", "selectWorld.import_worldgen_settings": "Importa optiones", "selectWorld.import_worldgen_settings.failure": "Error evenit dum optiones importantur", "selectWorld.import_worldgen_settings.select_file": "Selige scapum optionum (.json)", "selectWorld.incompatible.description": "Hic mundus in hac versione aperiri non potest.\nAntea in versione %s lusus est.", "selectWorld.incompatible.info": "Versio obsoleta: %s", "selectWorld.incompatible.title": "Versio obsoleta", "selectWorld.incompatible.tooltip": "Hic mundus aperiri non potest quod creatus est versione obsoleta.", "selectWorld.incompatible_series": "Creatus versione quae non convenit", "selectWorld.load_folder_access": "Quo mundi ludi servantur, scrinium legi non potest vel eum inire non potest!", "selectWorld.loading_list": "Index mundorum imponitur", "selectWorld.locked": "<PERSON>am apertus in instantiam aliam <PERSON>", "selectWorld.mapFeatures": "Genera aedificia", "selectWorld.mapFeatures.info": "Vici, naufragia, etc.", "selectWorld.mapType": "Genus mundi", "selectWorld.mapType.normal": "Normale", "selectWorld.moreWorldOptions": "Plures optiones mundi...", "selectWorld.newWorld": "Mundus novus", "selectWorld.recreate": "Duplica", "selectWorld.recreate.customized.text": "Mundi derecti non iam suscipitur in hoc versione. Hoc recreare cum eodem semine proprietatibusque conari possumus, sed omnes immutationes terrae amittentur. Paenitet nos incommoditas!", "selectWorld.recreate.customized.title": "Mundi derecti non iam suscipiuntur", "selectWorld.recreate.error.text": "Aliquid deliquit dum mundum recreare temptabat.", "selectWorld.recreate.error.title": "Error evenit!", "selectWorld.resource_load": "Supplementa parantur...", "selectWorld.resultFolder": "Servabitur in:", "selectWorld.search": "quaere mundos", "selectWorld.seedInfo": "Tene vacuum ad utendum semine fortuito", "selectWorld.select": "In mundo selecto lude", "selectWorld.targetFolder": "Servabitur in scrinio: %s", "selectWorld.title": "Selige mundum", "selectWorld.tooltip.fromNewerVersion1": "Mundus recentiore versione servatus est,", "selectWorld.tooltip.fromNewerVersion2": "hunc mundum carricare fortasse mala efficiat!", "selectWorld.tooltip.snapshot1": "Ne oblivisci creare exemplarem huius mundi per securitatem", "selectWorld.tooltip.snapshot2": "priusquam eum in hanc versionem praecocem carricas.", "selectWorld.unable_to_load": "Mundi carricari non potuerunt", "selectWorld.version": "Versio:", "selectWorld.versionJoinButton": "Impone utcumque", "selectWorld.versionQuestion": "Visne vero hunc mundum imponere?", "selectWorld.versionUnknown": "ignota", "selectWorld.versionWarning": "Hic mundus in versione %s nuper lusus est. Imponere eum in hac versione corruptiones efficere potest!", "selectWorld.warning.deprecated.question": "Aliquae res abominatae sunt et in futurum desinent. Visne procedere?", "selectWorld.warning.deprecated.title": "Cave! Haec optiones abominatis rebus utuntur", "selectWorld.warning.experimental.question": "Haec optiones experimentalae sunt et fortasse operare non potuit. Visne procedere?", "selectWorld.warning.experimental.title": "Cave! Haec optiones experimentalibus rebus utuntur", "selectWorld.warning.lowDiskSpace.description": "Spatium in systemata tua paene deest.\nDeesse spatium huiusmodi cum luderes mundum tuum laedere potest.", "selectWorld.warning.lowDiskSpace.title": "Cave! Spatium in disco macrius est!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "Inscribe in titulo", "sleep.not_possible": "Quoticumque dormiant hanc noctem edormire non potes", "sleep.players_sleeping": "%s lusores dormientes ex %s", "sleep.skipping_night": "Hanc noctem edormis", "slot.only_single_allowed": "Modo spacia unica licent, recipitur '%s'", "slot.unknown": "Spatium '%s' ignotum est", "snbt.parser.empty_key": "Clavis vacua esse non potest", "snbt.parser.expected_binary_numeral": "Numerus binarius exspectatus est", "snbt.parser.expected_decimal_numeral": "Numerus decimalis exspectatus est", "snbt.parser.expected_float_type": "Numerus decimarius exspectatus est", "snbt.parser.expected_hex_escape": "Scriptum figurarum %s exspectabatur", "snbt.parser.expected_hex_numeral": "Numerus sedecimalis exspectatus est", "snbt.parser.expected_integer_type": "Numerus integer exspectatus est", "snbt.parser.expected_non_negative_number": "Numerus non negativus exspectatus est", "snbt.parser.expected_number_or_boolean": "Numerus aut boolean exspectabatur", "snbt.parser.expected_string_uuid": "Scriptum repraesententem UUID validum exspectabatur", "snbt.parser.expected_unquoted_string": "Scriptum validum non quotum exspectabatur", "snbt.parser.infinity_not_allowed": "Numeri infiniti non licent", "snbt.parser.invalid_array_element_type": "Matrix generis elementi irrita est", "snbt.parser.invalid_character_name": "Nomen figurae Unicode irritum est", "snbt.parser.invalid_codepoint": "Numerus figurae Unicode irritus est: %s", "snbt.parser.invalid_string_contents": "Res scripti irritae sunt", "snbt.parser.invalid_unquoted_start": "Scripta non quota initare non possunt cum numeris zerum-IX, + aut -", "snbt.parser.leading_zero_not_allowed": "Numeri decimales cum zero initiare non possunt", "snbt.parser.no_such_operation": "Operatio: %s non exsistit", "snbt.parser.number_parse_failure": "Numerum: %s interpretari non potuit", "snbt.parser.undescore_not_allowed": "Interductus inferiores in principio fineve numeri scribi non possunt", "soundCategory.ambient": "Ambitum", "soundCategory.block": "<PERSON><PERSON>", "soundCategory.hostile": "Monstri", "soundCategory.master": "Sonus omnis", "soundCategory.music": "Musica", "soundCategory.neutral": "<PERSON>es", "soundCategory.player": "Lusores", "soundCategory.record": "Phonographum/Cubi musici", "soundCategory.ui": "UI", "soundCategory.voice": "Vox/Locutio", "soundCategory.weather": "Tempestas", "spectatorMenu.close": "<PERSON> electiones", "spectatorMenu.next_page": "Pagina proxima", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON> prior", "spectatorMenu.root.prompt": "Preme premendum semel ad iussum seligendum, iterum ad iubendum.", "spectatorMenu.team_teleport": "Te teletransporta ad socium", "spectatorMenu.team_teleport.prompt": "Selige factionem ad quam te teletransportes", "spectatorMenu.teleport": "Ad lusorem te teletransporta", "spectatorMenu.teleport.prompt": "Selige lusorem ad quem te teletransportes", "stat.generalButton": "Generales", "stat.itemsButton": "Res", "stat.minecraft.animals_bred": "Animalia alti ut fetarent", "stat.minecraft.aviate_one_cm": "Intervallum elytris pervolatum", "stat.minecraft.bell_ring": "Campanae tinnitae", "stat.minecraft.boat_one_cm": "Intervallum rati cursum", "stat.minecraft.clean_armor": "Tegmina decolorata", "stat.minecraft.clean_banner": "Vexilla decolorata", "stat.minecraft.clean_shulker_box": "Capsae shul<PERSON> decoloratae", "stat.minecraft.climb_one_cm": "Intervallum scansum", "stat.minecraft.crouch_one_cm": "Intervallum conquiniscendo cursum", "stat.minecraft.damage_absorbed": "Vulnus absorptum", "stat.minecraft.damage_blocked_by_shield": "Vulnus scuto elusum", "stat.minecraft.damage_dealt": "Vulnus inflictum", "stat.minecraft.damage_dealt_absorbed": "Vulnus inflictum (absorptum)", "stat.minecraft.damage_dealt_resisted": "Vulnus inflictum (resist. elusum)", "stat.minecraft.damage_resisted": "Vulnus resistentia elusum", "stat.minecraft.damage_taken": "Vulnus acceptum", "stat.minecraft.deaths": "Copia mortium", "stat.minecraft.drop": "Res emissae", "stat.minecraft.eat_cake_slice": "Frusta placentae esa", "stat.minecraft.enchant_item": "Res incantatae", "stat.minecraft.fall_one_cm": "Intervallum cadendo cursum", "stat.minecraft.fill_cauldron": "Cortinae completae", "stat.minecraft.fish_caught": "<PERSON><PERSON>ces comprensi", "stat.minecraft.fly_one_cm": "Intervallum pervolatum", "stat.minecraft.happy_ghast_one_cm": "Intervallum ghast laeto vectum", "stat.minecraft.horse_one_cm": "Intervallum perequitatum", "stat.minecraft.inspect_dispenser": "Distributores inspecti", "stat.minecraft.inspect_dropper": "Demittetra inspecta", "stat.minecraft.inspect_hopper": "Infundibula inspecta", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON> incudis", "stat.minecraft.interact_with_beacon": "<PERSON>us radiatoris", "stat.minecraft.interact_with_blast_furnace": "<PERSON><PERSON> camini", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON> mixtarii", "stat.minecraft.interact_with_campfire": "Usus foci", "stat.minecraft.interact_with_cartography_table": "Usus mensae chartographiae", "stat.minecraft.interact_with_crafting_table": "Usus mensae fabricationis", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON> fornacis", "stat.minecraft.interact_with_grindstone": "<PERSON>us cosis", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON> lectri", "stat.minecraft.interact_with_loom": "<PERSON>us telae", "stat.minecraft.interact_with_smithing_table": "Usus mensae fabricae", "stat.minecraft.interact_with_smoker": "<PERSON>us fumigatoris", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON> saxumsecatoris", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON> relicti", "stat.minecraft.minecart_one_cm": "Intervallum plastro fodinae cursum", "stat.minecraft.mob_kills": "Mobilia interfecta", "stat.minecraft.open_barrel": "Cupae apertae", "stat.minecraft.open_chest": "Cistae apertae", "stat.minecraft.open_enderchest": "Cistae <PERSON>ae", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON> shul<PERSON> apertae", "stat.minecraft.pig_one_cm": "Intervallum sue vectum", "stat.minecraft.play_noteblock": "Cubi musici canti", "stat.minecraft.play_record": "Phonodisci reproducti", "stat.minecraft.play_time": "Minuta in ludo cursa", "stat.minecraft.player_kills": "Lusores interfecti", "stat.minecraft.pot_flower": "Plantae in vasem insertae", "stat.minecraft.raid_trigger": "Incursiones coeptae", "stat.minecraft.raid_win": "Incursiones victae", "stat.minecraft.sleep_in_bed": "Quotienscumque dormisti in lecto", "stat.minecraft.sneak_time": "Tempus cursum cum furtim ambulares", "stat.minecraft.sprint_one_cm": "Intervallum cursu cursum", "stat.minecraft.strider_one_cm": "Intervallum ambulatore cursum", "stat.minecraft.swim_one_cm": "Intervallum natatum", "stat.minecraft.talked_to_villager": "Colloquia cum vicanis", "stat.minecraft.target_hit": "Pali icti", "stat.minecraft.time_since_death": "Tempus post mortem ultimam", "stat.minecraft.time_since_rest": "Tempus post somnum priorem", "stat.minecraft.total_world_time": "Tempus cum mundo aperto", "stat.minecraft.traded_with_villager": "Compensationes cum vicanis", "stat.minecraft.trigger_trapped_chest": "Cistae dolosae citatae", "stat.minecraft.tune_noteblock": "Cubi musici contenti", "stat.minecraft.use_cauldron": "Aqua sublata de cortina", "stat.minecraft.walk_on_water_one_cm": "Intervallum ambulatum super aqua", "stat.minecraft.walk_one_cm": "Intervallum ambulatum", "stat.minecraft.walk_under_water_one_cm": "Intervallum ambulatum sub aqua", "stat.mobsButton": "Mobilia", "stat_type.minecraft.broken": "Fracta", "stat_type.minecraft.crafted": "Fabricata", "stat_type.minecraft.dropped": "Emissa", "stat_type.minecraft.killed": "Interfecisti %s %s", "stat_type.minecraft.killed.none": "Tu numquam %s cecidisti", "stat_type.minecraft.killed_by": "%s tu caedit %s tempus (ora)", "stat_type.minecraft.killed_by.none": "%s te numquam interfecit", "stat_type.minecraft.mined": "Fossa", "stat_type.minecraft.picked_up": "Collecta", "stat_type.minecraft.used": "Adhibita", "stats.none": "-", "structure_block.button.detect_size": "DEPREHENDE", "structure_block.button.load": "EXTRAHE", "structure_block.button.save": "SERVA", "structure_block.custom_data": "Nomen unicum notae datorum", "structure_block.detect_size": "Deprehende magnitudinem locumque aedifici:", "structure_block.hover.corner": "Angulus: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Impone: %s", "structure_block.hover.save": "Serva: %s", "structure_block.include_entities": "Include entitates:", "structure_block.integrity": "Integriatas aedifici et semen", "structure_block.integrity.integrity": "Integritas a<PERSON>ii", "structure_block.integrity.seed": "Semen a<PERSON>ii", "structure_block.invalid_structure_name": "Nomen aedificii '%s' irritum est", "structure_block.load_not_found": "Aedificium '%s' non disponibile est", "structure_block.load_prepare": "Locus aedificii '%s' paratus est", "structure_block.load_success": "Aedeficium imponitur ex %s", "structure_block.mode.corner": "Angulus", "structure_block.mode.data": "Data", "structure_block.mode.load": "Impone", "structure_block.mode.save": "<PERSON><PERSON>", "structure_block.mode_info.corner": "Modus anguli - indicator positionis et magnitudinis", "structure_block.mode_info.data": "Modus datarum - indicator logicae ludi", "structure_block.mode_info.load": "Modus imponendi - impone ex archivo", "structure_block.mode_info.save": "<PERSON><PERSON> servandi - scribe in archivum", "structure_block.position": "Locus relativus", "structure_block.position.x": "locus relativus x", "structure_block.position.y": "locus relativus y", "structure_block.position.z": "locus relativus z", "structure_block.save_failure": "Aedificium '%s' servari non potest", "structure_block.save_success": "Aedificium servatum sicut '%s'", "structure_block.show_air": "Ostende cubos invisibiles:", "structure_block.show_boundingbox": "Ostende zonam delimitationis:", "structure_block.size": "Ma<PERSON><PERSON><PERSON>ii", "structure_block.size.x": "magnitudo aedificii x", "structure_block.size.y": "magnitudo aedificii y", "structure_block.size.z": "magnitudo aedificii z", "structure_block.size_failure": "Magnitudo structurae deprehendi non potest. Adde angulos cum nominibus structurarum parium", "structure_block.size_success": "Magnitudo prospere deprehenditur aedificio '%s'", "structure_block.strict": "Collocatio severae:", "structure_block.structure_name": "Nomen aedificii", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON> atrox", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON> atrox", "subtitles.block.amethyst_block.chime": "<PERSON><PERSON><PERSON> ameth<PERSON>", "subtitles.block.amethyst_block.resonate": "Amethystinum crystallum resonat", "subtitles.block.anvil.destroy": "Incus fractus est", "subtitles.block.anvil.land": "Incus impulit", "subtitles.block.anvil.use": "Incus adhibetur", "subtitles.block.barrel.close": "Cupa clauditur", "subtitles.block.barrel.open": "Cupa aperitur", "subtitles.block.beacon.activate": "Radiator activatus", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON> vibrat", "subtitles.block.beacon.deactivate": "Radiator deactivatus", "subtitles.block.beacon.power_select": "Effectus radiatoris seligitur", "subtitles.block.beehive.drip": "<PERSON>", "subtitles.block.beehive.enter": "Apis intrat alvarium", "subtitles.block.beehive.exit": "Apis exit alvarium", "subtitles.block.beehive.shear": "Forfex scabit", "subtitles.block.beehive.work": "Apes laborant", "subtitles.block.bell.resonate": "Campana resonat", "subtitles.block.bell.use": "<PERSON><PERSON> tinnit", "subtitles.block.big_dripleaf.tilt_down": "Folia inclinabilis se inclinat", "subtitles.block.big_dripleaf.tilt_up": "Folia inclinabilis se erigit", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON> crepitat", "subtitles.block.brewing_stand.brew": "Mixtarius bullit", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON> erumpunt", "subtitles.block.bubble_column.upwards_ambient": "<PERSON>ae fluunt", "subtitles.block.bubble_column.upwards_inside": "<PERSON>ae ascendentes", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON> to<PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON>ae descendentes", "subtitles.block.button.click": "Premendus crotolat", "subtitles.block.cake.add_candle": "Candela in placentam infigitur", "subtitles.block.campfire.crackle": "Focus crepitat", "subtitles.block.candle.crackle": "Candela crepitat", "subtitles.block.candle.extinguish": "Candela exstinguitur", "subtitles.block.chest.close": "Cista claudit", "subtitles.block.chest.locked": "Cista saepta", "subtitles.block.chest.open": "Cista aperitur", "subtitles.block.chorus_flower.death": "Flos chori marcet", "subtitles.block.chorus_flower.grow": "Flos chori surgit", "subtitles.block.comparator.click": "Comparatrum crotolat", "subtitles.block.composter.empty": "Compostiera vacuatur", "subtitles.block.composter.fill": "Compostiera pletur", "subtitles.block.composter.ready": "Compostiera operat", "subtitles.block.conduit.activate": "Conductus activatus", "subtitles.block.conduit.ambient": "Conductus micat", "subtitles.block.conduit.attack.target": "Conductus oppugnat", "subtitles.block.conduit.deactivate": "Conductus deactivatus", "subtitles.block.copper_bulb.turn_off": "Lumen cupreum inactivum fit", "subtitles.block.copper_bulb.turn_on": "Lumen cupreum activum fit", "subtitles.block.copper_trapdoor.close": "Ostium horizontale clauditur", "subtitles.block.copper_trapdoor.open": "Ostium horizontale aperitur", "subtitles.block.crafter.craft": "Fabricator fabricat", "subtitles.block.crafter.fail": "Fabricator deest", "subtitles.block.creaking_heart.hurt": "Cor crepacis ululat", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON> atrox", "subtitles.block.creaking_heart.spawn": "Cor crepacis expergiscitur", "subtitles.block.deadbush.idle": "Soni aridi", "subtitles.block.decorated_pot.insert": "Vas pictum pletur", "subtitles.block.decorated_pot.insert_fail": "Vas pictum titubat", "subtitles.block.decorated_pot.shatter": "Vas pictum frangitur", "subtitles.block.dispenser.dispense": "Res dispensata", "subtitles.block.dispenser.fail": "Dispensatrum defuit", "subtitles.block.door.toggle": "Ostium stridit", "subtitles.block.dried_ghast.ambient": "Soni ariditatis", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> siccatus inrigatur", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> siccatus lavatur", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> siccatus meliorem sentit", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON> ventosi", "subtitles.block.enchantment_table.use": "<PERSON><PERSON> incantandi adhibetur", "subtitles.block.end_portal.spawn": "Porta End aperitur", "subtitles.block.end_portal_frame.fill": "Ender oculus inseritur", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON> claudit", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON> susurrat", "subtitles.block.eyeblossom.open": "Floculus aperit", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON> stridit", "subtitles.block.fire.ambient": "<PERSON><PERSON><PERSON> crepitat", "subtitles.block.fire.extinguish": "Ignis exstinctus", "subtitles.block.firefly_bush.idle": "Cicindelae bombiunt", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON>s<PERSON>", "subtitles.block.furnace.fire_crackle": "Fornax crepitat", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON> fractus", "subtitles.block.generic.fall": "Aliquid in cubum decidit", "subtitles.block.generic.footsteps": "Vestigia", "subtitles.block.generic.hit": "Cubus frangens", "subtitles.block.generic.place": "<PERSON><PERSON><PERSON> collocatus", "subtitles.block.grindstone.use": "<PERSON><PERSON> adhi<PERSON>ur", "subtitles.block.growing_plant.crop": "Planta deputatur", "subtitles.block.hanging_sign.waxed_interact_fail": "Titulus titubat", "subtitles.block.honey_block.slide": "<PERSON>itur de cubo mellis", "subtitles.block.iron_trapdoor.close": "Ostium horizontale clauditur", "subtitles.block.iron_trapdoor.open": "Ostium horizontale aperitur", "subtitles.block.lava.ambient": "Lava bullat", "subtitles.block.lava.extinguish": "Lava sibilat", "subtitles.block.lever.click": "Vectis movetur", "subtitles.block.note_block.note": "Cubus musicus canit", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON> atrox", "subtitles.block.piston.move": "Embolus movet", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> stillat", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava in cortinam stillat", "subtitles.block.pointed_dripstone.drip_water": "Aqua stillat", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Aqua in cortinam stillat", "subtitles.block.pointed_dripstone.land": "Stalactites corruit", "subtitles.block.portal.ambient": "Porta sonat", "subtitles.block.portal.travel": "Sonus portae pallescet", "subtitles.block.portal.trigger": "Sonus portae amplificatur", "subtitles.block.pressure_plate.click": "Tabula premenda premitur", "subtitles.block.pumpkin.carve": "Forfex scalpit", "subtitles.block.redstone_torch.burnout": "Fax exstinguitur", "subtitles.block.respawn_anchor.ambient": "Porta sonat", "subtitles.block.respawn_anchor.charge": "Ancora renascendi firmatur", "subtitles.block.respawn_anchor.deplete": "Ancora renascendi laxatur", "subtitles.block.respawn_anchor.set_spawn": "Ancora renascendi adhibetur", "subtitles.block.sand.idle": "<PERSON><PERSON> haren<PERSON>i", "subtitles.block.sand.wind": "<PERSON><PERSON> ventosi", "subtitles.block.sculk.charge": "Sculk bullit", "subtitles.block.sculk.spread": "Sculk se pandit", "subtitles.block.sculk_catalyst.bloom": "Sculk mutans floret", "subtitles.block.sculk_sensor.clicking": "Sculk sentiens vibrare incipit", "subtitles.block.sculk_sensor.clicking_stop": "Sculk sentiens vibrare desinit", "subtitles.block.sculk_shrieker.shriek": "Sculk clamans clamat", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> se claudit", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> se aperit", "subtitles.block.sign.waxed_interact_fail": "Titulus titubat", "subtitles.block.smithing_table.use": "Mensa fabricae adhibetur", "subtitles.block.smoker.smoke": "Fumigator fumigat", "subtitles.block.sniffer_egg.crack": "Ovum odorisequi crepitat", "subtitles.block.sniffer_egg.hatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nascitur", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cec<PERSON>it", "subtitles.block.sponge.absorb": "Spongia exsorbet", "subtitles.block.sweet_berry_bush.pick_berries": "Bacae leguntur", "subtitles.block.trapdoor.close": "Ostium horizontale clauditur", "subtitles.block.trapdoor.open": "Ostium horizontale aperitur", "subtitles.block.trapdoor.toggle": "Ostium horizontale stridit", "subtitles.block.trial_spawner.about_to_spawn_item": "Res praesaga paratur", "subtitles.block.trial_spawner.ambient": "Creator periculorum crepitat", "subtitles.block.trial_spawner.ambient_charged": "Creator periculorum praesagus crepitat", "subtitles.block.trial_spawner.ambient_ominous": "Crepitatio praesaga", "subtitles.block.trial_spawner.charge_activate": "Omen creatorem periculorum devorat", "subtitles.block.trial_spawner.close_shutter": "Creator periculorum claudit", "subtitles.block.trial_spawner.detect_player": "Creator periculorum se auget", "subtitles.block.trial_spawner.eject_item": "Creator periculorum res iacit", "subtitles.block.trial_spawner.ominous_activate": "Omen creatorem periculorum devorat", "subtitles.block.trial_spawner.open_shutter": "Creator periculorum aperit", "subtitles.block.trial_spawner.spawn_item": "Res praesaga emittitur", "subtitles.block.trial_spawner.spawn_item_begin": "Res praesaga apparet", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON><PERSON> creantur", "subtitles.block.tripwire.attach": "Laqueus adiungitur", "subtitles.block.tripwire.click": "Laqueus crotolat", "subtitles.block.tripwire.detach": "Laqueus abducitur", "subtitles.block.vault.activate": "<PERSON>a a<PERSON>t", "subtitles.block.vault.ambient": "Arca crepitat", "subtitles.block.vault.close_shutter": "<PERSON><PERSON> clauditur", "subtitles.block.vault.deactivate": "<PERSON><PERSON> ex<PERSON>ur", "subtitles.block.vault.eject_item": "Arca rem eicit", "subtitles.block.vault.insert_item": "Arca reseratur", "subtitles.block.vault.insert_item_fail": "Arca non reseratur", "subtitles.block.vault.open_shutter": "Arca aperitur", "subtitles.block.vault.reject_rewarded_player": "Arca lusorem repudiat", "subtitles.block.water.ambient": "Aqua fluit", "subtitles.block.wet_sponge.dries": "S<PERSON>ngia siccatur", "subtitles.chiseled_bookshelf.insert": "Liber ponitur", "subtitles.chiseled_bookshelf.insert_enchanted": "Liber incantatus ponitur", "subtitles.chiseled_bookshelf.take": "Liber capitur", "subtitles.chiseled_bookshelf.take_enchanted": "Liber incantatus capitur", "subtitles.enchant.thorns.hit": "Aculei pungit", "subtitles.entity.allay.ambient_with_item": "Relevator quaerit", "subtitles.entity.allay.ambient_without_item": "Relevator cupit", "subtitles.entity.allay.death": "Relevator moritur", "subtitles.entity.allay.hurt": "Relevator dolet", "subtitles.entity.allay.item_given": "Relevator ridet", "subtitles.entity.allay.item_taken": "Relevator relevat", "subtitles.entity.allay.item_thrown": "Relevator iacit", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.armadillo.brush": "S<PERSON><PERSON> verritur", "subtitles.entity.armadillo.death": "Dasypus moritur", "subtitles.entity.armadillo.eat": "Dasypus edit", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.armadillo.hurt_reduced": "Dasypus se protegit", "subtitles.entity.armadillo.land": "Dasypus cadit", "subtitles.entity.armadillo.peek": "Dasypus circumspicit", "subtitles.entity.armadillo.roll": "Dasypus volvitur", "subtitles.entity.armadillo.scute_drop": "Dasypus squamam fundit", "subtitles.entity.armadillo.unroll_finish": "Dasypus evolvitur", "subtitles.entity.armadillo.unroll_start": "Dasypus circumspicit", "subtitles.entity.armor_stand.fall": "Quid cecidit", "subtitles.entity.arrow.hit": "Sagitta adfigit", "subtitles.entity.arrow.hit_player": "Lu<PERSON> icitur", "subtitles.entity.arrow.shoot": "Sagitta iactatur", "subtitles.entity.axolotl.attack": "Salamandra mexicana oppugnat", "subtitles.entity.axolotl.death": "Salamandra mexicana moritur", "subtitles.entity.axolotl.hurt": "Salamandra mexicana dolet", "subtitles.entity.axolotl.idle_air": "Salamandra mexicana pipiat", "subtitles.entity.axolotl.idle_water": "Salamandra mexicana pipiat", "subtitles.entity.axolotl.splash": "Salamandra mexicana adspargit", "subtitles.entity.axolotl.swim": "Salamandra mexicana natat", "subtitles.entity.bat.ambient": "<PERSON>es<PERSON><PERSON>o plip<PERSON>", "subtitles.entity.bat.death": "Vespertilio moritur", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Vespertilio subvolat", "subtitles.entity.bee.ambient": "<PERSON><PERSON> bombit", "subtitles.entity.bee.death": "<PERSON>pis moritur", "subtitles.entity.bee.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON> bombit", "subtitles.entity.bee.loop_aggressive": "Apis bombit irate", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> bombit feliciter", "subtitles.entity.bee.sting": "<PERSON><PERSON> punget", "subtitles.entity.blaze.ambient": "Flammifer spirat", "subtitles.entity.blaze.burn": "Flammifer crepitat", "subtitles.entity.blaze.death": "Flammifer moritur", "subtitles.entity.blaze.hurt": "Flammifer dolet", "subtitles.entity.blaze.shoot": "Flammifer conicit", "subtitles.entity.boat.paddle_land": "Remorum pulsus", "subtitles.entity.boat.paddle_water": "Remorum pulsus", "subtitles.entity.bogged.ambient": "Paludivagus crepat", "subtitles.entity.bogged.death": "Paludivagus moritur", "subtitles.entity.bogged.hurt": "Paludiva<PERSON> dolet", "subtitles.entity.breeze.charge": "Ventifer parat saltum", "subtitles.entity.breeze.death": "Ventifer moritur", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> de<PERSON>", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.breeze.idle_air": "Ventifer volat", "subtitles.entity.breeze.idle_ground": "Ventifer murmurat", "subtitles.entity.breeze.inhale": "Ventifer inhalat", "subtitles.entity.breeze.jump": "Ventifer salit", "subtitles.entity.breeze.land": "Ventifer cadit", "subtitles.entity.breeze.shoot": "Ventifer conicit", "subtitles.entity.breeze.slide": "Vent<PERSON> labitur", "subtitles.entity.breeze.whirl": "Ventifer se intorquet", "subtitles.entity.breeze.wind_burst": "Missile ventosum rumpitur", "subtitles.entity.camel.ambient": "<PERSON><PERSON> grun<PERSON>t", "subtitles.entity.camel.dash": "Camelus iacit", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON> recuperat", "subtitles.entity.camel.death": "<PERSON>lus moritur", "subtitles.entity.camel.eat": "Camelus edit", "subtitles.entity.camel.hurt": "<PERSON><PERSON> dolet", "subtitles.entity.camel.saddle": "Sagma cingitur", "subtitles.entity.camel.sit": "<PERSON><PERSON> sedet", "subtitles.entity.camel.stand": "Camelus exsurgit", "subtitles.entity.camel.step": "Camelus ambulat", "subtitles.entity.camel.step_sand": "Camelus ambulat in harena", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> mendicat", "subtitles.entity.cat.death": "<PERSON><PERSON> moritur", "subtitles.entity.cat.eat": "<PERSON><PERSON> edit", "subtitles.entity.cat.hiss": "<PERSON><PERSON> sibilat", "subtitles.entity.cat.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON> murmuret", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.chicken.egg": "<PERSON>ullus ovum creat", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON> salit", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "Vacca <PERSON>", "subtitles.entity.cow.death": "Vacca moritur", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.milk": "Vacca mulgetur", "subtitles.entity.creaking.activate": "Crepax oritur", "subtitles.entity.creaking.ambient": "Crepax crepat", "subtitles.entity.creaking.attack": "Crepax oppugnat", "subtitles.entity.creaking.deactivate": "Crepax finitur", "subtitles.entity.creaking.death": "Crepax moritur", "subtitles.entity.creaking.freeze": "Crepax rigescit", "subtitles.entity.creaking.spawn": "Crepax animatur", "subtitles.entity.creaking.sway": "Crepax defenditur", "subtitles.entity.creaking.twitch": "Crepax vellit", "subtitles.entity.creaking.unfreeze": "Crepax movetur", "subtitles.entity.creeper.death": "Creeper moritur", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> dolet", "subtitles.entity.creeper.primed": "Creeper sibilat", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON> pip<PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> stridit", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> oppugna<PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> edit", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON> salit", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> ludit", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> adspargit", "subtitles.entity.dolphin.swim": "Delphinus natat", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> rudit", "subtitles.entity.donkey.angry": "<PERSON><PERSON> hinnit", "subtitles.entity.donkey.chest": "<PERSON><PERSON> cistae oneratur", "subtitles.entity.donkey.death": "<PERSON><PERSON> moritur", "subtitles.entity.donkey.eat": "<PERSON><PERSON> edit", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> salit", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON> singultat", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON> singultat", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.drowned.shoot": "Mersus tridentem conicit", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON> graditur", "subtitles.entity.drowned.swim": "Mersus natat", "subtitles.entity.egg.throw": "Ovum volat", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON> postnatus gemit", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON> postnatus plaudit", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON> post<PERSON>us anathemat", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON> postnatus moritur", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON> post<PERSON>us salit", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON> post<PERSON> do<PERSON>", "subtitles.entity.ender_dragon.ambient": "Draco detonat", "subtitles.entity.ender_dragon.death": "Draco moritur", "subtitles.entity.ender_dragon.flap": "Draco plaudit", "subtitles.entity.ender_dragon.growl": "Draco fremit", "subtitles.entity.ender_dragon.hurt": "Draco dolet", "subtitles.entity.ender_dragon.shoot": "Draco conicit", "subtitles.entity.ender_eye.death": "Ender oculus cadit", "subtitles.entity.ender_eye.launch": "Ender oculus intorquetur", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON> Ender volat", "subtitles.entity.enderman.ambient": "Enderman \"fvuup\" sonat", "subtitles.entity.enderman.death": "<PERSON><PERSON> moritur", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> dolet", "subtitles.entity.enderman.scream": "Enderman vociferatur", "subtitles.entity.enderman.stare": "<PERSON><PERSON> clamat", "subtitles.entity.enderman.teleport": "Enderman se teletransportat", "subtitles.entity.endermite.ambient": "Endermite festinat", "subtitles.entity.endermite.death": "Endermite moritur", "subtitles.entity.endermite.hurt": "Endermite dolet", "subtitles.entity.evoker.ambient": "Evocator muttit", "subtitles.entity.evoker.cast_spell": "Evocator incantat", "subtitles.entity.evoker.celebrate": "Evocator hilarat", "subtitles.entity.evoker.death": "Evocator moritur", "subtitles.entity.evoker.hurt": "Evocator dolet", "subtitles.entity.evoker.prepare_attack": "Evocator parat oppugnare", "subtitles.entity.evoker.prepare_summon": "Evocator parat vocare", "subtitles.entity.evoker.prepare_wololo": "Evocator parat fascinare", "subtitles.entity.evoker_fangs.attack": "Denticuli increpat", "subtitles.entity.experience_orb.pickup": "Experientia percepta", "subtitles.entity.firework_rocket.blast": "Pyrotechnema attonat", "subtitles.entity.firework_rocket.launch": "Pyrotechnema emittitur", "subtitles.entity.firework_rocket.twinkle": "Pyrotechnema micat", "subtitles.entity.fish.swim": "<PERSON><PERSON>cis nat", "subtitles.entity.fishing_bobber.retrieve": "Allector piscatorius attrahitur", "subtitles.entity.fishing_bobber.splash": "Allector piscatorius adspargit", "subtitles.entity.fishing_bobber.throw": "Allector piscatorius iactatur", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> irascitur", "subtitles.entity.fox.ambient": "<PERSON>ulp<PERSON> stridit", "subtitles.entity.fox.bite": "<PERSON>ulp<PERSON> mordet", "subtitles.entity.fox.death": "Vulpes moritur", "subtitles.entity.fox.eat": "Vulpes edit", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> dolet", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> bubulat", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON> stertit", "subtitles.entity.fox.sniff": "Vulpes odoratur", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> spuit", "subtitles.entity.fox.teleport": "Vulpes se teletransportat", "subtitles.entity.frog.ambient": "<PERSON> garrit", "subtitles.entity.frog.death": "<PERSON> mori<PERSON>", "subtitles.entity.frog.eat": "<PERSON> edit", "subtitles.entity.frog.hurt": "<PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON> ova parit", "subtitles.entity.frog.long_jump": "<PERSON> salit", "subtitles.entity.generic.big_fall": "Aliquid cecidit", "subtitles.entity.generic.burn": "Ardere", "subtitles.entity.generic.death": "Nex", "subtitles.entity.generic.drink": "Libare", "subtitles.entity.generic.eat": "<PERSON><PERSON>", "subtitles.entity.generic.explode": "Displosio", "subtitles.entity.generic.extinguish_fire": "Flamma exstinguitur", "subtitles.entity.generic.hurt": "<PERSON><PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> caes<PERSON>", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "Natare", "subtitles.entity.generic.wind_burst": "Missile ventosum rumptitur", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> clamat", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> mori<PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> conicit", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> muttit", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> moritur", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.ghastling.spawn": "Ghastulus apparet", "subtitles.entity.glow_item_frame.add_item": "Forma rerum lucida completur", "subtitles.entity.glow_item_frame.break": "Forma rerum lucida frangitur", "subtitles.entity.glow_item_frame.place": "Forma rerum lucida collocata est", "subtitles.entity.glow_item_frame.remove_item": "Forma rerum lucida vacuatur", "subtitles.entity.glow_item_frame.rotate_item": "Forma rerum lucida rotatur", "subtitles.entity.glow_squid.ambient": "Lolligo lucida natat", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON> lucida moritur", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON> lucida do<PERSON>", "subtitles.entity.glow_squid.squirt": "Lolligo lucida atramentum eiaculat", "subtitles.entity.goat.ambient": "<PERSON><PERSON> belat", "subtitles.entity.goat.death": "<PERSON><PERSON> moritur", "subtitles.entity.goat.eat": "<PERSON><PERSON> edit", "subtitles.entity.goat.horn_break": "Cornu caprae frangitur", "subtitles.entity.goat.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> salit", "subtitles.entity.goat.milk": "<PERSON><PERSON> mulgetur", "subtitles.entity.goat.prepare_ram": "Capra calcat", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON> offendit", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> mugit", "subtitles.entity.goat.step": "<PERSON><PERSON> graditur", "subtitles.entity.guardian.ambient": "<PERSON>ustos gemit", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON> plaudit", "subtitles.entity.guardian.attack": "<PERSON>ustos conicit", "subtitles.entity.guardian.death": "Custos mori<PERSON>", "subtitles.entity.guardian.flop": "<PERSON>ustos salit", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON> la<PERSON> cantat", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> la<PERSON> moritur", "subtitles.entity.happy_ghast.equip": "Capistrum cingitur", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> laetus est paratus", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> la<PERSON> constat", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> <PERSON> dolet", "subtitles.entity.happy_ghast.unequip": "Capistrum ininstruitur", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> grun<PERSON>t", "subtitles.entity.hoglin.angry": "Hoglin iracunde grunnit", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> oppu<PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON>n in zoglin mutatur", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> dolet", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> retrahit", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> graditur", "subtitles.entity.horse.ambient": "<PERSON>quus hinnit", "subtitles.entity.horse.angry": "<PERSON>quus hinnit", "subtitles.entity.horse.armor": "Tegimen equinum induitur", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON><PERSON> spirat", "subtitles.entity.horse.death": "<PERSON>quus moritur", "subtitles.entity.horse.eat": "<PERSON>quus edit", "subtitles.entity.horse.gallop": "Equus currit", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON><PERSON> dolet", "subtitles.entity.horse.jump": "Equus salit", "subtitles.entity.horse.saddle": "Sagma cingitur", "subtitles.entity.husk.ambient": "<PERSON><PERSON> gemit", "subtitles.entity.husk.converted_to_zombie": "Cassus in resurrectum mutatur", "subtitles.entity.husk.death": "<PERSON><PERSON> moritur", "subtitles.entity.husk.hurt": "<PERSON><PERSON>", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON>st<PERSON><PERSON><PERSON> muttit", "subtitles.entity.illusioner.cast_spell": "<PERSON>raest<PERSON><PERSON><PERSON> incantat", "subtitles.entity.illusioner.death": "<PERSON>raest<PERSON><PERSON><PERSON> moritur", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> disponet", "subtitles.entity.illusioner.prepare_blindness": "Praest<PERSON><PERSON>us caecitatem parat", "subtitles.entity.illusioner.prepare_mirror": "Praestigicanus imagonem speculeam parat", "subtitles.entity.iron_golem.attack": "Automatum ferreum oppugnat", "subtitles.entity.iron_golem.damage": "Automatum ferreum frangitur", "subtitles.entity.iron_golem.death": "Automatum ferreum moritur", "subtitles.entity.iron_golem.hurt": "Automatum ferreum dolet", "subtitles.entity.iron_golem.repair": "Automatum ferreum reficitur", "subtitles.entity.item.break": "<PERSON>s frangitur", "subtitles.entity.item.pickup": "<PERSON><PERSON> cec<PERSON>it", "subtitles.entity.item_frame.add_item": "Formae rerum additur res", "subtitles.entity.item_frame.break": "Forma rerum frangitur", "subtitles.entity.item_frame.place": "Forma rerum collocata", "subtitles.entity.item_frame.remove_item": "Forma rerum vacuatur", "subtitles.entity.item_frame.rotate_item": "Forma rerum rotatur", "subtitles.entity.leash_knot.break": "Nodus lori frangit", "subtitles.entity.leash_knot.place": "Nodus lori nectitur", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON><PERSON> fulminat", "subtitles.entity.lightning_bolt.thunder": "Fulgur tonat", "subtitles.entity.llama.ambient": "<PERSON> bala<PERSON>", "subtitles.entity.llama.angry": "<PERSON> irate balat", "subtitles.entity.llama.chest": "Cista lamae cingitur", "subtitles.entity.llama.death": "<PERSON> m<PERSON>", "subtitles.entity.llama.eat": "<PERSON> edit", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON> spuit", "subtitles.entity.llama.step": "<PERSON> graditur", "subtitles.entity.llama.swag": "Lama ornata est", "subtitles.entity.magma_cube.death": "Cubus magmatis moritur", "subtitles.entity.magma_cube.hurt": "<PERSON>ub<PERSON> magmatis dolet", "subtitles.entity.magma_cube.squish": "Cubus magmatis urget", "subtitles.entity.minecart.inside": "Plaustrum tinnit", "subtitles.entity.minecart.inside_underwater": "Plaustrum sub aqua tinnit", "subtitles.entity.minecart.riding": "Plaustrum fodinae volvet", "subtitles.entity.mooshroom.convert": "Mooshroom mutatur", "subtitles.entity.mooshroom.eat": "Mooshroom edit", "subtitles.entity.mooshroom.milk": "Mooshroom mulgetur", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom mulgetur suspecte", "subtitles.entity.mule.ambient": "Mulus clamat i-a", "subtitles.entity.mule.angry": "<PERSON><PERSON> hinnit", "subtitles.entity.mule.chest": "Cista muli cingitur", "subtitles.entity.mule.death": "<PERSON><PERSON> moritur", "subtitles.entity.mule.eat": "Mulus edit", "subtitles.entity.mule.hurt": "<PERSON><PERSON> dolet", "subtitles.entity.mule.jump": "<PERSON>lus salit", "subtitles.entity.painting.break": "Pictura frangitur", "subtitles.entity.painting.place": "Pictura ponitur", "subtitles.entity.panda.aggressive_ambient": "<PERSON>da spirat", "subtitles.entity.panda.ambient": "<PERSON><PERSON>", "subtitles.entity.panda.bite": "Panda mordet", "subtitles.entity.panda.cant_breed": "<PERSON>da balat", "subtitles.entity.panda.death": "Panda moritur", "subtitles.entity.panda.eat": "Panda edit", "subtitles.entity.panda.hurt": "Panda dolet", "subtitles.entity.panda.pre_sneeze": "Nasum pandae salit", "subtitles.entity.panda.sneeze": "Panda sternuit", "subtitles.entity.panda.step": "Panda graditur", "subtitles.entity.panda.worried_ambient": "Panda gannit", "subtitles.entity.parrot.ambient": "Psittacus loquitur", "subtitles.entity.parrot.death": "Psittacus moritur", "subtitles.entity.parrot.eats": "Psittacus edit", "subtitles.entity.parrot.fly": "Psittacus volat", "subtitles.entity.parrot.hurts": "<PERSON><PERSON>tta<PERSON> dolet", "subtitles.entity.parrot.imitate.blaze": "Psitta<PERSON> spirat", "subtitles.entity.parrot.imitate.bogged": "Psittacus crepat", "subtitles.entity.parrot.imitate.breeze": "Psittacus murmurat ventose", "subtitles.entity.parrot.imitate.creaking": "Psittacus crepat", "subtitles.entity.parrot.imitate.creeper": "Psittacus sibilat", "subtitles.entity.parrot.imitate.drowned": "Psittacus singultat", "subtitles.entity.parrot.imitate.elder_guardian": "Psittacus gemit", "subtitles.entity.parrot.imitate.ender_dragon": "Psittacus detonat", "subtitles.entity.parrot.imitate.endermite": "Psittacus festinat", "subtitles.entity.parrot.imitate.evoker": "Psittacus muttit", "subtitles.entity.parrot.imitate.ghast": "Psittacus clamat", "subtitles.entity.parrot.imitate.guardian": "Psittacus gemit", "subtitles.entity.parrot.imitate.hoglin": "Psittaca boat", "subtitles.entity.parrot.imitate.husk": "Psittacus gemit", "subtitles.entity.parrot.imitate.illusioner": "Psittacus muttit", "subtitles.entity.parrot.imitate.magma_cube": "Psitta<PERSON> urget", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON><PERSON> bubulat", "subtitles.entity.parrot.imitate.piglin": "Psitta<PERSON> grunnit", "subtitles.entity.parrot.imitate.piglin_brute": "Psitta<PERSON> grunnit", "subtitles.entity.parrot.imitate.pillager": "Psittacus muttit", "subtitles.entity.parrot.imitate.ravager": "Psitta<PERSON> grunnit", "subtitles.entity.parrot.imitate.shulker": "Psittacus latet", "subtitles.entity.parrot.imitate.silverfish": "Psittacus sibilat", "subtitles.entity.parrot.imitate.skeleton": "Psittacus crepat", "subtitles.entity.parrot.imitate.slime": "Psitta<PERSON> urget", "subtitles.entity.parrot.imitate.spider": "Psittacus sibilat", "subtitles.entity.parrot.imitate.stray": "Psittacus crepat", "subtitles.entity.parrot.imitate.vex": "Psittacus vexat", "subtitles.entity.parrot.imitate.vindicator": "Psittacus sussurat", "subtitles.entity.parrot.imitate.warden": "Psittacus queritur", "subtitles.entity.parrot.imitate.witch": "Psittacus ridet", "subtitles.entity.parrot.imitate.wither": "Psittacus irascitur", "subtitles.entity.parrot.imitate.wither_skeleton": "Psittacus crepat", "subtitles.entity.parrot.imitate.zoglin": "Psittaca boat", "subtitles.entity.parrot.imitate.zombie": "Psittacus gemit", "subtitles.entity.parrot.imitate.zombie_villager": "Psittacus gemit", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON><PERSON> bubula<PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON><PERSON> admordet", "subtitles.entity.phantom.death": "Phantasma moritur", "subtitles.entity.phantom.flap": "Phantasma plaudit", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON><PERSON> dolet", "subtitles.entity.phantom.swoop": "Phantas<PERSON> cern<PERSON>t", "subtitles.entity.pig.ambient": "<PERSON><PERSON> grun<PERSON>t", "subtitles.entity.pig.death": "<PERSON>s moritur", "subtitles.entity.pig.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.pig.saddle": "Sagma cingitur", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> rem admiratur", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> grun<PERSON>t", "subtitles.entity.piglin.angry": "Piglin iracunde grunnit", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> celebrat", "subtitles.entity.piglin.converted_to_zombified": "Piglin in piglin resurrectum mutatur", "subtitles.entity.piglin.death": "<PERSON><PERSON> moritur", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> dolet", "subtitles.entity.piglin.jealous": "<PERSON>lin invidiose grunnit", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> retrahit", "subtitles.entity.piglin.step": "<PERSON><PERSON> graditur", "subtitles.entity.piglin_brute.ambient": "Piglin ferus grunnit", "subtitles.entity.piglin_brute.angry": "Piglin ferus iracunde grunnit", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin ferus in piglin resurrectum mutatur", "subtitles.entity.piglin_brute.death": "Piglin ferus moritur", "subtitles.entity.piglin_brute.hurt": "Piglin ferus dolet", "subtitles.entity.piglin_brute.step": "Piglin ferus graditur", "subtitles.entity.pillager.ambient": "Praedator muttit", "subtitles.entity.pillager.celebrate": "<PERSON>raedator hilarat", "subtitles.entity.pillager.death": "Praedator moritur", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON> dolet", "subtitles.entity.player.attack.crit": "Iniuria critica", "subtitles.entity.player.attack.knockback": "Iniuria cum repugnantia", "subtitles.entity.player.attack.strong": "Iniuria fortis", "subtitles.entity.player.attack.sweep": "Iniuria deverrens", "subtitles.entity.player.attack.weak": "Iniuria imbecilla", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Lusor moritur", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON> congelat", "subtitles.entity.player.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON>sor mergit", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON> ardet", "subtitles.entity.player.levelup": "<PERSON><PERSON> tinnit", "subtitles.entity.player.teleport": "Lusor teleportat", "subtitles.entity.polar_bear.ambient": "Ursus polaris gemit", "subtitles.entity.polar_bear.ambient_baby": "Ursus polaris susurrat", "subtitles.entity.polar_bear.death": "Ursus polaris moritur", "subtitles.entity.polar_bear.hurt": "Ursus polaris dolet", "subtitles.entity.polar_bear.warning": "Ursus polaris detonat", "subtitles.entity.potion.splash": "<PERSON><PERSON> frang<PERSON>ur", "subtitles.entity.potion.throw": "<PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON><PERSON> aculeatus sufflat", "subtitles.entity.puffer_fish.blow_up": "<PERSON><PERSON><PERSON> aculeatus es<PERSON>lat", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON> aculeatus moritur", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON><PERSON> aculeatus salit", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON> aculeatus dolet", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON><PERSON> aculeatus pungit", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON> stridet", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> op<PERSON>", "subtitles.entity.rabbit.death": "Cuni<PERSON> moritur", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> salit", "subtitles.entity.ravager.ambient": "Vast<PERSON> grunnit", "subtitles.entity.ravager.attack": "Vastator mordet", "subtitles.entity.ravager.celebrate": "Vastator hilarat", "subtitles.entity.ravager.death": "Vastator moritur", "subtitles.entity.ravager.hurt": "Vastator dolet", "subtitles.entity.ravager.roar": "Vastator detonat", "subtitles.entity.ravager.step": "Vastator graditur", "subtitles.entity.ravager.stunned": "Vastator stupefacitur", "subtitles.entity.salmon.death": "Salmo moritur", "subtitles.entity.salmon.flop": "Salmo salit", "subtitles.entity.salmon.hurt": "<PERSON><PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> b<PERSON>t", "subtitles.entity.sheep.death": "<PERSON><PERSON> mori<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> latet", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> se claudit", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> mori<PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> dolet", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> se aperit", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> conicit", "subtitles.entity.shulker.teleport": "Shulker se teletransportat", "subtitles.entity.shulker_bullet.hit": "<PERSON>um shulker explodet", "subtitles.entity.shulker_bullet.hurt": "Telum shulker frangitur", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON> sibilat", "subtitles.entity.silverfish.death": "Le<PERSON><PERSON> moritur", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON> crepat", "subtitles.entity.skeleton.converted_to_stray": "Sceletus in aberratorem mutatur", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON> conicit", "subtitles.entity.skeleton_horse.ambient": "Equus sceletalis clamat", "subtitles.entity.skeleton_horse.death": "Equus sceletalis moritur", "subtitles.entity.skeleton_horse.hurt": "<PERSON>quus sceletalis dolet", "subtitles.entity.skeleton_horse.jump_water": "<PERSON>quus sceletalis salit", "subtitles.entity.skeleton_horse.swim": "Equus sceletus natat", "subtitles.entity.slime.attack": "<PERSON><PERSON> oppugnat", "subtitles.entity.slime.death": "<PERSON><PERSON> moritur", "subtitles.entity.slime.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON> urget", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moritur", "subtitles.entity.sniffer.digging": "Odorisequus fodit", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> exsurgit", "subtitles.entity.sniffer.drop_seed": "Odorisequus semen invenit", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> edit", "subtitles.entity.sniffer.egg_crack": "Ovum odorisequi crepitat", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nascitur", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laeta<PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.sniffer.scenting": "Odorisequus odorat", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> quaerit", "subtitles.entity.sniffer.sniffing": "Odorisequus odorem sequitur", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> graditur", "subtitles.entity.snow_golem.death": "Automatum niveum moritur", "subtitles.entity.snow_golem.hurt": "Automatum niveum dolet", "subtitles.entity.snowball.throw": "<PERSON>la nivis volat", "subtitles.entity.spider.ambient": "<PERSON><PERSON> sibilat", "subtitles.entity.spider.death": "<PERSON><PERSON> moritur", "subtitles.entity.spider.hurt": "<PERSON><PERSON>", "subtitles.entity.squid.ambient": "Lolligo natat", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.squid.squirt": "Lolligo atramentum eiaculat", "subtitles.entity.stray.ambient": "Aberra<PERSON> crepat", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON> dolet", "subtitles.entity.strider.death": "Ambulator moritur", "subtitles.entity.strider.eat": "Ambulator edit", "subtitles.entity.strider.happy": "Ambulator blaterat", "subtitles.entity.strider.hurt": "Ambulator dolet", "subtitles.entity.strider.idle": "Ambulator pipit", "subtitles.entity.strider.retreat": "Ambulator retrahit", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> mori<PERSON>", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON> salit", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.tnt.primed": "Dynamites sibilat", "subtitles.entity.tropical_fish.death": "Piscis multigener necatur", "subtitles.entity.tropical_fish.flop": "Piscis multigener salit", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON>cis multigener dolet", "subtitles.entity.turtle.ambient_land": "Testudo pipiat", "subtitles.entity.turtle.death": "<PERSON>udo moritur", "subtitles.entity.turtle.death_baby": "In<PERSON>s testudinis moritur", "subtitles.entity.turtle.egg_break": "Ovum testudinis frangitur", "subtitles.entity.turtle.egg_crack": "Ovum testudinis finditur", "subtitles.entity.turtle.egg_hatch": "Ex ovis testudinis excluditur", "subtitles.entity.turtle.hurt": "<PERSON>udo dolet", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON><PERSON> testudinis dolet", "subtitles.entity.turtle.lay_egg": "Testudo ovum parit", "subtitles.entity.turtle.shamble": "<PERSON>udo se<PERSON>it", "subtitles.entity.turtle.shamble_baby": "In<PERSON>s testudinis serpit", "subtitles.entity.turtle.swim": "Testudo natat", "subtitles.entity.vex.ambient": "Vexator vexat", "subtitles.entity.vex.charge": "Vexator stridet", "subtitles.entity.vex.death": "Vexator moritur", "subtitles.entity.vex.hurt": "Vexator dolet", "subtitles.entity.villager.ambient": "<PERSON><PERSON> murmurat", "subtitles.entity.villager.celebrate": "<PERSON><PERSON> hilarat", "subtitles.entity.villager.death": "<PERSON><PERSON> moritur", "subtitles.entity.villager.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.villager.no": "Vicanus dissentit", "subtitles.entity.villager.trade": "<PERSON><PERSON> negotitur", "subtitles.entity.villager.work_armorer": "Faber tegminum laborat", "subtitles.entity.villager.work_butcher": "Lanius laborat", "subtitles.entity.villager.work_cartographer": "Cartographus laborat", "subtitles.entity.villager.work_cleric": "Clericus laborat", "subtitles.entity.villager.work_farmer": "Agricola laborat", "subtitles.entity.villager.work_fisherman": "Piscator laborat", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON><PERSON> laborat", "subtitles.entity.villager.work_leatherworker": "Operarius corii laborat", "subtitles.entity.villager.work_librarian": "Bibliothecar<PERSON> laborat", "subtitles.entity.villager.work_mason": "Cementarius laborat", "subtitles.entity.villager.work_shepherd": "Pastor laborat", "subtitles.entity.villager.work_toolsmith": "Faber instrumenti laborat", "subtitles.entity.villager.work_weaponsmith": "Faber ferri laborat", "subtitles.entity.villager.yes": "Vicanus assentit", "subtitles.entity.vindicator.ambient": "Vindicator sussurat", "subtitles.entity.vindicator.celebrate": "Vindicator hilarat", "subtitles.entity.vindicator.death": "Vindicator moritur", "subtitles.entity.vindicator.hurt": "Vindicator dolet", "subtitles.entity.wandering_trader.ambient": "Mercator vagus muttit", "subtitles.entity.wandering_trader.death": "Mercator vagus moritur", "subtitles.entity.wandering_trader.disappeared": "Mercator vagus evanescit", "subtitles.entity.wandering_trader.drink_milk": "Mercator vagus lac bibit", "subtitles.entity.wandering_trader.drink_potion": "Mercator vagus potionem bibit", "subtitles.entity.wandering_trader.hurt": "Me<PERSON><PERSON> vagus dolet", "subtitles.entity.wandering_trader.no": "Mercator vagus dissentit", "subtitles.entity.wandering_trader.reappeared": "Mercator vagus apparet", "subtitles.entity.wandering_trader.trade": "Mercator vagus mercatur", "subtitles.entity.wandering_trader.yes": "Mercator vagus assentit", "subtitles.entity.warden.agitated": "Vigil irate gemit", "subtitles.entity.warden.ambient": "Vigil queritur", "subtitles.entity.warden.angry": "Vigil furit", "subtitles.entity.warden.attack_impact": "Vigil icit", "subtitles.entity.warden.death": "Vigil moritur", "subtitles.entity.warden.dig": "Vigil fodit", "subtitles.entity.warden.emerge": "Vigil emergit", "subtitles.entity.warden.heartbeat": "Cor vigilis pulsat", "subtitles.entity.warden.hurt": "Vigil dolet", "subtitles.entity.warden.listening": "Vigil animadvertit", "subtitles.entity.warden.listening_angry": "Vigil irate animadvertit", "subtitles.entity.warden.nearby_close": "Vigil advenit", "subtitles.entity.warden.nearby_closer": "Vigil adpropinquat", "subtitles.entity.warden.nearby_closest": "Vigil adest", "subtitles.entity.warden.roar": "Vigil detonat", "subtitles.entity.warden.sniff": "Vigil odoratur", "subtitles.entity.warden.sonic_boom": "Vigil boat", "subtitles.entity.warden.sonic_charge": "Vigil parat boatum", "subtitles.entity.warden.step": "Vigil graditur", "subtitles.entity.warden.tendril_clicks": "Vigil sensit motum", "subtitles.entity.wind_charge.throw": "Missile ventosum volat", "subtitles.entity.wind_charge.wind_burst": "Missile ventosum rumptitur", "subtitles.entity.witch.ambient": "Venefica ridet", "subtitles.entity.witch.celebrate": "Venefica hilarat", "subtitles.entity.witch.death": "Venefica moritur", "subtitles.entity.witch.drink": "Venefica bibit", "subtitles.entity.witch.hurt": "Venefica dolet", "subtitles.entity.witch.throw": "Venefica iacit", "subtitles.entity.wither.ambient": "<PERSON><PERSON> irasci<PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON> moritur", "subtitles.entity.wither.hurt": "<PERSON><PERSON> dolet", "subtitles.entity.wither.shoot": "<PERSON><PERSON> op<PERSON>", "subtitles.entity.wither.spawn": "<PERSON>er solvitur", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON> wither crepat", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON> wither moritur", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON> wither dolet", "subtitles.entity.wolf.ambient": "<PERSON><PERSON> an<PERSON>", "subtitles.entity.wolf.bark": "<PERSON><PERSON> latrat", "subtitles.entity.wolf.death": "<PERSON><PERSON> moritur", "subtitles.entity.wolf.growl": "Lupus fremit", "subtitles.entity.wolf.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.wolf.pant": "<PERSON><PERSON> an<PERSON>", "subtitles.entity.wolf.shake": "Lupus commovet", "subtitles.entity.wolf.whine": "<PERSON><PERSON> vagit", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> grun<PERSON>t", "subtitles.entity.zoglin.angry": "Zoglin iracunde grunnit", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> oppu<PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> moritur", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> dolet", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> graditur", "subtitles.entity.zombie.ambient": "Resurrectus gemit", "subtitles.entity.zombie.attack_wooden_door": "Ostium tremitur", "subtitles.entity.zombie.break_wooden_door": "Ostium frangitur", "subtitles.entity.zombie.converted_to_drowned": "Resurrectus in mersum mutatur", "subtitles.entity.zombie.death": "Resurrectus moritur", "subtitles.entity.zombie.destroy_egg": "Ovum testudinis conculcatur", "subtitles.entity.zombie.hurt": "Resurrectus dolet", "subtitles.entity.zombie.infect": "Resurrectus inficit", "subtitles.entity.zombie_horse.ambient": "Equus resurrectus clamat", "subtitles.entity.zombie_horse.death": "Equus resurrectus moritur", "subtitles.entity.zombie_horse.hurt": "Equus resurrectus dolet", "subtitles.entity.zombie_villager.ambient": "Vicanus resurrectus gemit", "subtitles.entity.zombie_villager.converted": "Vic<PERSON> resurrectus fatur", "subtitles.entity.zombie_villager.cure": "Vicanus resurrectus starnuit", "subtitles.entity.zombie_villager.death": "Vicanus resurrectus moritur", "subtitles.entity.zombie_villager.hurt": "<PERSON><PERSON> resurrectus dolet", "subtitles.entity.zombified_piglin.ambient": "Piglin resurrectus grunnit", "subtitles.entity.zombified_piglin.angry": "Piglin resurrectus iracunde grunnit", "subtitles.entity.zombified_piglin.death": "Piglin resurrectus moritur", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> resurrectus dolet", "subtitles.event.mob_effect.bad_omen": "Omen capit", "subtitles.event.mob_effect.raid_omen": "Incursio prope impendit", "subtitles.event.mob_effect.trial_omen": "Periculum praesagum prope impendit", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON> praesaga clangit", "subtitles.item.armor.equip": "Tegumen armatur", "subtitles.item.armor.equip_chain": "Tegimen reticuli ferrei tinnit", "subtitles.item.armor.equip_diamond": "Tegimen adamantinum crepat", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> crepant", "subtitles.item.armor.equip_gold": "Tegimen aureum tinnit", "subtitles.item.armor.equip_iron": "Tegimen ferreum crepat", "subtitles.item.armor.equip_leather": "Tegimen scorteum crepat", "subtitles.item.armor.equip_netherite": "Tegimen Netheritae crepat", "subtitles.item.armor.equip_turtle": "Testa testudinis resonat", "subtitles.item.armor.equip_wolf": "Tegmen lupinum induitur", "subtitles.item.armor.unequip_wolf": "Tegmen lupinum aufertur", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON> scabit", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> decorticat", "subtitles.item.axe.wax_off": "Cera scabitur", "subtitles.item.bone_meal.use": "<PERSON>ssa molita crepant", "subtitles.item.book.page_turn": "<PERSON><PERSON>a crepat", "subtitles.item.book.put": "Liber ponitur", "subtitles.item.bottle.empty": "<PERSON><PERSON> vacuatur", "subtitles.item.bottle.fill": "<PERSON><PERSON> pletur", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON> verrit", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON> verritur", "subtitles.item.brush.brushing.gravel.complete": "Glarea versa est", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON> verri<PERSON>", "subtitles.item.brush.brushing.sand.complete": "Harena versa est", "subtitles.item.bucket.empty": "Hama vacuatur", "subtitles.item.bucket.fill": "<PERSON><PERSON> p<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Salamandra mexicana capta est", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> capitur", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON> captus est", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON> vacuatur", "subtitles.item.bundle.insert": "Res in sarcina ponitur", "subtitles.item.bundle.insert_fail": "Sarcina plena est", "subtitles.item.bundle.remove_one": "Res e sarcina extrahitur", "subtitles.item.chorus_fruit.teleport": "Lusor se teletransportat", "subtitles.item.crop.plant": "Planta inseritur", "subtitles.item.crossbow.charge": "Man<PERSON><PERSON><PERSON> carricat", "subtitles.item.crossbow.hit": "Sagitta adfigit", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crossbow.shoot": "Man<PERSON><PERSON><PERSON> i<PERSON>atur", "subtitles.item.dye.use": "Tinctura tingit", "subtitles.item.elytra.flying": "Con<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Globus flammeus sonat", "subtitles.item.flintandsteel.use": "Ignitabulum crepitat", "subtitles.item.glow_ink_sac.use": "Saccus atramenti lucidi visco", "subtitles.item.goat_horn.play": "Cornu caprae canit", "subtitles.item.hoe.till": "Sarculum colens", "subtitles.item.honey_bottle.drink": "<PERSON> sorbet", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON> illinitur", "subtitles.item.horse_armor.unequip": "Tegimen equinum aufertur", "subtitles.item.ink_sac.use": "Saccus atramenti visco", "subtitles.item.lead.break": "Copula frangitur", "subtitles.item.lead.tied": "Co<PERSON>la nectitur", "subtitles.item.lead.untied": "Copula solvitur", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON> aufertur", "subtitles.item.lodestone_compass.lock": "Acus magnetica lapide magnete vi magnetica imbuitur", "subtitles.item.mace.smash_air": "<PERSON><PERSON> percutit", "subtitles.item.mace.smash_ground": "<PERSON><PERSON> percutit", "subtitles.item.nether_wart.plant": "Planta inseritur", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON> frang<PERSON>ur", "subtitles.item.saddle.unequip": "Sagma aufertur", "subtitles.item.shears.shear": "Forfex crotolat", "subtitles.item.shears.snip": "Forfex detondet", "subtitles.item.shield.block": "Scutum arceat", "subtitles.item.shovel.flatten": "Pala dilatat", "subtitles.item.spyglass.stop_using": "Telescopium brevius fit", "subtitles.item.spyglass.use": "Telescopium longius fit", "subtitles.item.totem.use": "Figura incitatur", "subtitles.item.trident.hit": "Tridens transigit", "subtitles.item.trident.hit_ground": "Tridens tremescit", "subtitles.item.trident.return": "Tridens redit", "subtitles.item.trident.riptide": "Tridens properat", "subtitles.item.trident.throw": "Tridens tinnit", "subtitles.item.trident.thunder": "<PERSON>tus tridentis tonat", "subtitles.item.wolf_armor.break": "Tegmen lupinum frangitur", "subtitles.item.wolf_armor.crack": "Tegmen lupinum dissilitur", "subtitles.item.wolf_armor.damage": "Tegmen lupinum vulneratur", "subtitles.item.wolf_armor.repair": "Tegmen lupinum reficitur", "subtitles.particle.soul_escape": "Anima effugit", "subtitles.ui.cartography_table.take_result": "Scriptio in charta", "subtitles.ui.hud.bubble_pop": "Metrum spiritus cadit", "subtitles.ui.loom.take_result": "Tela adhibetur", "subtitles.ui.stonecutter.take_result": "Saxumsecator adhibetur", "subtitles.weather.rain": "Imber cadit", "symlink_warning.message": "Extrahere mundos e scriniis cum nexis symbolicis dubium esse potest si quid facis accurate nescis. Visita %s ut plus discas.", "symlink_warning.message.pack": "Extrahere compilationes e scapis cum nexis symbolicis dubium esse potest si quid facis accurate nescis. Visita %s ut plus discas.", "symlink_warning.message.world": "Extrahere mundos e scriniis cum nexis symbolicis dubium esse potest si quid facis accurate nescis. Visita %s ut plus discas.", "symlink_warning.more_info": "Plus nuntiorum", "symlink_warning.title": "Scapum mundi nexa symbolica habet", "symlink_warning.title.pack": "Compilationes additae ligamina symbolica habent", "symlink_warning.title.world": "Scapum mundi nexa symbolica habet", "team.collision.always": "ab omnibus trudi possunt", "team.collision.never": "trudi non possunt", "team.collision.pushOtherTeams": "a sua factione tantum trudi possunt", "team.collision.pushOwnTeam": "ab aliis factionibus tantum trudi possunt", "team.notFound": "Haec factio ignota est: %s", "team.visibility.always": "omnibus lusoribus", "team.visibility.hideForOtherTeams": "factioni suae tantum", "team.visibility.hideForOwnTeam": "aliis factionibus tantum", "team.visibility.never": "nulli lusori", "telemetry.event.advancement_made.description": "Intellegere contextum incrementarum accipendarum adiuvat nos intellegere emendareque progressionem ludi.", "telemetry.event.advancement_made.title": "Incrementum factum", "telemetry.event.game_load_times.description": "Hoc eventum adiuvare nos potest ut cognosceremus ubi emendationes efficacitati exsiliendi necesse sint, numerando tempora effectus vicerum exsiliendarum.", "telemetry.event.game_load_times.title": "<PERSON><PERSON>ra extrahendi ludi", "telemetry.event.optional": "%s (facultativum)", "telemetry.event.optional.disabled": "%s (facultativum) - Inactivum", "telemetry.event.performance_metrics.description": "Scire descriptorem generalem efficacitatis Minecraft adiuvat nos ut ludum vellicemus perficamusque seriei vastae machinarum designationum et systemae internae. \nVersio ludi inclusa est ut nos adiuvet ut descriptores efficacitatis versionibus Minecraft novis comparemus.", "telemetry.event.performance_metrics.title": "<PERSON><PERSON> efficacitatis", "telemetry.event.required": "%s (Requisitus)", "telemetry.event.world_load_times.description": "Nobis mundum iungendo tempora intellegere melius est, et quomodo in temporibus mutatur. Exempli gratia, cum qualitates addamus vel maiores technicas mutationes efficamus, nobis intellegere effectum temporum extrahendi melius est.", "telemetry.event.world_load_times.title": "<PERSON><PERSON>ra extrahendi mundi", "telemetry.event.world_loaded.description": "Scire quomodo lusores Minecraft ludunt (e.g. modus ludi, mutationes clienti vel moderatri, et versio ludi) nos adiuvat ut emendationes ludi intendamus in areas quibus lusores maxime curant.\nEventum mundus lectus et eventum mundus delectus cuncta numerantur ut tempus ludendi ludi accurate numeratur.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON> imponitur", "telemetry.event.world_unloaded.description": "Hoc eventum cunctum cum mundus lectus evento numeratur ut accurate quamdiu sessio ludi permanet numeratur.\nTempus (secunda et momenta) numeratur cum sessio mundi exierit (exieris ad titulum, a moderatro connectionem interrumpas).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON> deponitur", "telemetry.property.advancement_game_time.title": "Quot momenta lusisti", "telemetry.property.advancement_id.title": "Incrementum", "telemetry.property.client_id.title": "<PERSON><PERSON><PERSON>", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON> mutatus sit", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicata (kB)", "telemetry.property.event_timestamp_utc.title": "Signum temporis eventi (UTC)", "telemetry.property.frame_rate_samples.title": "Replicircuitionis exempla (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON> ludi", "telemetry.property.game_version.title": "<PERSON><PERSON><PERSON> ludi", "telemetry.property.launcher_name.title": "Nomen deductoris", "telemetry.property.load_time_bootstrap_ms.title": "Tempus Bootstrap (millesimi secundorum)", "telemetry.property.load_time_loading_overlay_ms.title": "Tempus in simulacro extrahendi (millesimi secundorum)", "telemetry.property.load_time_pre_window_ms.title": "Tempus ante fenestra aperitur (millesimi secundorum)", "telemetry.property.load_time_total_time_ms.title": "Totum tempus imponere (millesimi secundorum)", "telemetry.property.minecraft_session_id.title": "<PERSON><PERSON><PERSON>", "telemetry.property.new_world.title": "<PERSON>nd<PERSON><PERSON> novus sit", "telemetry.property.number_of_samples.title": "Exemplorum numerus", "telemetry.property.operating_system.title": "Systema internum", "telemetry.property.opt_in.title": "Num consentis", "telemetry.property.platform.title": "Rostrum", "telemetry.property.realms_map_content.title": "Res regni chartae (nomen luduli)", "telemetry.property.render_distance.title": "Amp<PERSON><PERSON> spatii visibilis", "telemetry.property.render_time_samples.title": "Exempla temporum reddendi", "telemetry.property.seconds_since_load.title": "Tempus postquam extraxi (secunda)", "telemetry.property.server_modded.title": "Moderatrumne mutatum sit", "telemetry.property.server_type.title": "Genus moderatri", "telemetry.property.ticks_since_load.title": "Tempus postquam extraxi (momenta)", "telemetry.property.used_memory_samples.title": "Memoria volatilis adhibita", "telemetry.property.user_id.title": "<PERSON><PERSON>", "telemetry.property.world_load_time_ms.title": "Tempus extrahendi mundi (millesimi secundorum)", "telemetry.property.world_session_id.title": "<PERSON><PERSON><PERSON> mundi", "telemetry_info.button.give_feedback": "Emendationem da", "telemetry_info.button.privacy_statement": "Declaratio de vita privata", "telemetry_info.button.show_data": "Scrinium datorum aperi", "telemetry_info.opt_in.description": "Adsentio mittendo telemetriorum datorum facultativorum", "telemetry_info.property_title": "Haec data mittentur:", "telemetry_info.screen.description": "Colligere hoc datum nos adiuvat ut Minecraft emendemus quod dirigit nos in directiones quae ad lusores nostros pertinent.\nPotes etiam mittere emendationem novam ut nos Minecraft emendare adiuves.", "telemetry_info.screen.title": "Colligere Telemetriarum Datarum", "test.error.block_property_mismatch": "Proprium %s exspectabatur esse %s, sed %s erat", "test.error.block_property_missing": "Proprium %s cubo esse exspectabatur %s, sed non erat", "test.error.entity_property": "Experimentum de entitate %s male evenit: %s", "test.error.entity_property_details": "Experimentum de entitate %s male evenit: %s, %s exspectabatur, %s erat", "test.error.expected_block": "Cubus %s exspectabatur, %s accipitur", "test.error.expected_block_tag": "Cubus in #%s exspectabatur, %s accipitur", "test.error.expected_container_contents": "Cubus qui res tenere potest %s continere debet", "test.error.expected_container_contents_single": "Cubus qui res tenere potest unum %s continere debet", "test.error.expected_empty_container": "Cubus qui res tenere potest vacuus esse debet", "test.error.expected_entity": "%s exspectabatur", "test.error.expected_entity_around": "%s exsistere circiter %s, %s, %s exspectabatur", "test.error.expected_entity_count": "%s entitates generis %s exspectabantur, %s invenit", "test.error.expected_entity_data": "Datum entitatis esse %s exspectabatur, %s erat", "test.error.expected_entity_data_predicate": "Datum entitatis ad %s male sociat", "test.error.expected_entity_effect": "%s habere effectum %s %s exspectabatur", "test.error.expected_entity_having": "Inventarium entitatis %s habere debet", "test.error.expected_entity_holding": "Entitas %s teneri debet", "test.error.expected_entity_in_test": "%s exsistare in probatione exspectabatur", "test.error.expected_entity_not_touching": "%s tangere %s, %s, %s non exspectabatur (relativus: %s, %s, %s)", "test.error.expected_entity_touching": "%s tangere %s, %s, %s exspectabatur (relativus: %s, %s, %s)", "test.error.expected_item": "Res generis %s exspectata est", "test.error.expected_items_count": "%s res generis %s exspectatus est, %s invenit", "test.error.fail": "Condiciones eventus mali completur", "test.error.invalid_block_type": "Inopinatum genus cubi invenitum est: %s", "test.error.missing_block_entity": "Entitas cubi abest", "test.error.position": "%s ad %s, %s, %s (relativus: %s, %s, %s) momento %s", "test.error.sequence.condition_already_triggered": "Condicio iamiam efficitur in %s", "test.error.sequence.condition_not_triggered": "Condicio non efficitur", "test.error.sequence.invalid_tick": "Successit in momento irrito: %s exspectatur", "test.error.sequence.not_completed": "Probatio ante seriem completam tempus exiit", "test.error.set_biome": "Bioma probationis mutari non potuit", "test.error.spawn_failure": "Entitas %s creari non potuit", "test.error.state_not_equal": "Status irritus est. %s exspectatus, sed %s erat", "test.error.structure.failure": "Aedificium probationis ad %s ponere non potuit", "test.error.tick": "%s in momento %s", "test.error.ticking_without_structure": "Probatio momenti ante ponere aedificium", "test.error.timeout.no_result": "Neque successit neque defecit in %s momentis", "test.error.timeout.no_sequences_finished": "Nullae sequentiae finiverunt in %s momentis", "test.error.too_many_entities": "Unum solum %s exsistere circum %s, %s, %s exspectabatur, sed %s invenit", "test.error.unexpected_block": "Cubus esse %s non exspectatus est", "test.error.unexpected_entity": "%s exsistere non exspectabatur", "test.error.unexpected_item": "Res generis %s non expectata est", "test.error.unknown": "Error internalis ignotus: %s", "test.error.value_not_equal": "%s esse %s expectabatur, %s erat", "test.error.wrong_block_entity": "Genus entitatis cubi irritum est: %s", "test_block.error.missing": "Aedificium probationis cubum %s non habet", "test_block.error.too_many": "Nimis multi cubi %s", "test_block.invalid_timeout": "Interdictum breve irritum est (%s) - oportet esse momentorum numerus positivus", "test_block.message": "Nuntium:", "test_block.mode.accept": "Ev. bonus", "test_block.mode.fail": "Ev. malus", "test_block.mode.log": "Descriptio", "test_block.mode.start": "Principium", "test_block.mode_info.accept": "Modus eventus boni - Facit ut probatio partim bene eveniat", "test_block.mode_info.fail": "Modus eventus mali - Facit ut probatio male eveniat", "test_block.mode_info.log": "Modus descriptionis - Nuntium mittit", "test_block.mode_info.start": "Modus principi - Principium probationis", "test_instance.action.reset": "Reinitia et impone", "test_instance.action.run": "Impone et initia", "test_instance.action.save": "Serva aedificium", "test_instance.description.batch": "Classis: %s", "test_instance.description.failed": "Defecit: %s", "test_instance.description.function": "Functio: %s", "test_instance.description.invalid_id": "ID probationis irrita est", "test_instance.description.no_test": "Probatio non exsistit", "test_instance.description.structure": "Aedificium: %s", "test_instance.description.type": "Genus: %s", "test_instance.type.block_based": "Probatio in cubo", "test_instance.type.function": "Probatio functionis insita", "test_instance_block.entities": "Entitates:", "test_instance_block.error.no_test": "Instantiam probationis ad %s, %s, %s exsequi non potest quia probationem indefinitam habet", "test_instance_block.error.no_test_structure": "Instantiam probationis ad %s, %s, %s exsequi non potest quia aedificium probationis non habet", "test_instance_block.error.unable_to_save": "Formam aedificii probationis ad instantiam probationis in %s, %s, %s servare non potest", "test_instance_block.invalid": "[irritus]", "test_instance_block.reset_success": "Reinitium ad probationem: %s successit", "test_instance_block.rotation": "Rotatio:", "test_instance_block.size": "Magni<PERSON><PERSON> a<PERSON>is", "test_instance_block.starting": "Probatio %s incipitur", "test_instance_block.test_id": "ID instantiae probationis", "title.32bit.deprecation": "Systemate XXXII bitorum uteris: quod, cum Minecraft systemate LXIV bitorum egeat, ludere te impediet!", "title.32bit.deprecation.realms": "Minecraft systemate LXIV bitorum mox egebit: quod hoc instrumento ludere vel Realms uti te impediet. Subscriptiones Realms tibi renuntiandae erunt.", "title.32bit.deprecation.realms.check": "Ne hoc simulacrum iterum ostendatur", "title.32bit.deprecation.realms.header": "Systemate XXXII bitorum uteris", "title.credits": "Ius auctoris tenetur a Mojang AB. Noli distribuere!", "title.multiplayer.disabled": "Ludus multorum inactivus est. Inspice Microsoft optiones licentiae tuae.", "title.multiplayer.disabled.banned.name": "Nomen tibi mutandum est ante ludere interretialiter tibi licebit", "title.multiplayer.disabled.banned.permanent": "Licentia tua est in perpetuum suspensa a ludis interretialibus", "title.multiplayer.disabled.banned.temporary": "Licentia tua est pro tempore suspensa a ludis interretialibus", "title.multiplayer.lan": "<PERSON><PERSON> multorum (RL)", "title.multiplayer.other": "Ludus multorum (moderatrum partium tertiarum)", "title.multiplayer.realms": "<PERSON><PERSON> multorum (Realms)", "title.singleplayer": "<PERSON><PERSON> unius", "translation.test.args": "%s %s", "translation.test.complex": "Praefixus, %s%2$s iterum %s et %1$s tandem %s atque %1$s iterum!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "salve %", "translation.test.invalid2": "salve %s", "translation.test.none": "Salve, munde!", "translation.test.world": "munde", "trim_material.minecraft.amethyst": "Ex amethysto", "trim_material.minecraft.copper": "Ex cupro", "trim_material.minecraft.diamond": "Ex adamante", "trim_material.minecraft.emerald": "Ex smaragdo", "trim_material.minecraft.gold": "Ex auro", "trim_material.minecraft.iron": "Ex ferro", "trim_material.minecraft.lapis": "Ex lapide lazuli", "trim_material.minecraft.netherite": "Ex Netherite", "trim_material.minecraft.quartz": "Ex crystallo", "trim_material.minecraft.redstone": "Ex redstone", "trim_material.minecraft.resin": "Ex resina", "trim_pattern.minecraft.bolt": "Ornamentum tegminis cum clavis", "trim_pattern.minecraft.coast": "Ornamentum tegminis litorale", "trim_pattern.minecraft.dune": "Ornamentum tegminis desertico", "trim_pattern.minecraft.eye": "Ornamentum tegminis cum oculo", "trim_pattern.minecraft.flow": "Ornamentum tegminis cum fluxu", "trim_pattern.minecraft.host": "Ornamentum tegminis hospitis", "trim_pattern.minecraft.raiser": "Ornamentum tegminis sublatoris", "trim_pattern.minecraft.rib": "Ornamentum tegminis cum costis", "trim_pattern.minecraft.sentry": "Ornamentum tegminis excubitorum", "trim_pattern.minecraft.shaper": "Ornamentum tegminis fictoris", "trim_pattern.minecraft.silence": "Ornamentum tegminis silentis", "trim_pattern.minecraft.snout": "Ornamentum tegminis cum rostro", "trim_pattern.minecraft.spire": "Ornamentum tegminis cum turre", "trim_pattern.minecraft.tide": "Ornamentum tegminis cum aestu", "trim_pattern.minecraft.vex": "Ornamentum tegminis vexatoris", "trim_pattern.minecraft.ward": "Ornamentum tegminis vigiliarum", "trim_pattern.minecraft.wayfinder": "Ornamentum tegminis viatoris", "trim_pattern.minecraft.wild": "Ornamentum tegminis silvanum", "tutorial.bundleInsert.description": "Ice dextrum ut res addas", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON> sarcina", "tutorial.craft_planks.description": "Liber praeceptorum adiuvare potest", "tutorial.craft_planks.title": "Fabrica tabulas ligneas", "tutorial.find_tree.description": "Caede id ut lignum colligas", "tutorial.find_tree.title": "Quaere arborem", "tutorial.look.description": "Utere mure tuo ut vertas", "tutorial.look.title": "Circumspice", "tutorial.move.description": "Sali cum %s clave", "tutorial.move.title": "Move cum %s, %s, %s, et %s clavibus", "tutorial.open_inventory.description": "Preme %s", "tutorial.open_inventory.title": "Inventarium tuum aperi", "tutorial.punch_tree.description": "Opprime %s clavem iugiter", "tutorial.punch_tree.title": "Dele arborem", "tutorial.socialInteractions.description": "Preme %s ut aperias", "tutorial.socialInteractions.title": "Optiones sociorum", "upgrade.minecraft.netherite_upgrade": "Solidamentum ex Netherite"}