{"accessibility.onboarding.accessibility.button": "Sentakuna fu spelapu...", "accessibility.onboarding.screen.narrator": "Tasta Enter per asa lesakoi", "accessibility.onboarding.screen.title": "B<PERSON>ula Minecraft made!\n\nDu vil auki lesakoi os anse brukapu kawarina?", "addServer.add": "Owari", "addServer.enterIp": "Serverdvera", "addServer.enterName": "<PERSON><PERSON><PERSON>", "addServer.resourcePack": "Shirenabaksu na k<PERSON>ani", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON> nai", "addServer.resourcePack.enabled": "<PERSON><PERSON><PERSON>", "addServer.resourcePack.prompt": "Spore", "addServer.title": "<PERSON><PERSON>", "advMode.command": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode": "Troposfal", "advMode.mode.auto": "<PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "<PERSON><PERSON>", "advMode.mode.conditional": "<PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Tolkazeraz", "advMode.mode.redstoneTriggered": "Redstone trengena", "advMode.mode.sequence": "<PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "Utenfanshuka", "advMode.notAllowed": "Treng jewaltdjin na mahatropos", "advMode.notEnabled": "Jewaltdado nai dwaibma inne aftoo server", "advMode.previousOutput": "Dankak<PERSON>na", "advMode.setCommand": "<PERSON><PERSON><PERSON> ye<PERSON>ko per dado", "advMode.setCommand.success": "<PERSON><PERSON><PERSON><PERSON> sentaku dan: %s", "advMode.trackOutput": "<PERSON><PERSON> k<PERSON>", "advMode.triggering": "<PERSON><PERSON><PERSON>", "advMode.type": "Fal", "advancement.advancementNotFound": "Kuamnai jingena: %s", "advancements.adventure.adventuring_time.description": "<PERSON><PERSON> tont fal fu lant", "advancements.adventure.adventuring_time.title": "Hszfultid", "advancements.adventure.arbalistic.description": "Vras go shal na en strela", "advancements.adventure.arbalistic.title": "Erbaumvrasena", "advancements.adventure.avoid_vibration.description": "Pinunojalaka bara Sculk aistiabma os eskuzhin per nai bli hœrena", "advancements.adventure.avoid_vibration.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.blowback.description": "Vras luftsjal mit suryk-jitena luftbam", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Bruk sikat au gha komuskepone fu komushemysh komushemysh made", "advancements.adventure.brush_armadillo.title": "Isn't It Scute?", "advancements.adventure.bullseye.description": "<PERSON><PERSON><PERSON> mellan fu jenadado made na prapatai fu 30 meetoru na pol", "advancements.adventure.bullseye.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Maha raz<PERSON><PERSON><PERSON>na lulekligne 4 luleklignetel kara", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Shuchulik Reforma", "advancements.adventure.crafters_crafting_crafters.description": "Jam para mahadjin koske maha mahadjin", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON> mahaena na mahadjin", "advancements.adventure.fall_from_world_height.description": "Spada veltele kara (letste nasi<PERSON><PERSON> pit<PERSON>) veltpol made au bidesvona", "advancements.adventure.fall_from_world_height.title": "Siranga au berktumam", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Kervracz", "advancements.adventure.hero_of_the_village.description": "Kømuske statnen razharza kara najing", "advancements.adventure.hero_of_the_village.title": "Gangdjin molodjets fu polisnen", "advancements.adventure.honey_block_slide.description": "Spada mjaltdado made per zihaspada", "advancements.adventure.honey_block_slide.title": "<PERSON> je mjalt", "advancements.adventure.kill_a_mob.description": "Vras mikava morko", "advancements.adventure.kill_a_mob.title": "Morko vrasdjin", "advancements.adventure.kill_all_mobs.description": "Vras ein fu al fal fu morko", "advancements.adventure.kill_all_mobs.title": "Morko vrasena", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON> para Sculk errupnedvaibma", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.lighten_up.description": "Shing kupkat-kruska mit jondu per maha pluskirkas", "advancements.adventure.lighten_up.title": "Tsa kirk<PERSON>lik", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Eshku statdjin pozeus kara uten hono", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON>ez<PERSON><PERSON>", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON><PERSON><PERSON> na bides dera fu iskatdai", "advancements.adventure.minecraft_trials_edition.title": "Mincraft: iskat(dai)fal", "advancements.adventure.ol_betsy.description": "Jit na ruzhjo", "advancements.adventure.ol_betsy.title": "G<PERSON><PERSON><PERSON>", "advancements.adventure.overoverkill.description": "Anslag mit bulavá 50 ker na en slagraz", "advancements.adventure.overoverkill.title": "Vrasdai-dai", "advancements.adventure.play_jukebox_in_meadows.description": "<PERSON><PERSON><PERSON> munter grun zan fu lidbaksu", "advancements.adventure.play_jukebox_in_meadows.title": "Zan fu muzik", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Lesa zeusatai na seresena libretumam kara, br<PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Zeus fu libre", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Gjen-ziharum", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON>, razse au krig", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "Sikat susdado na saada luleklignetel", "advancements.adventure.salvage_sherd.title": "<PERSON> milunai, to vik<PERSON><PERSON>", "advancements.adventure.shoot_arrow.description": "Slag jokuting mit lunasen au strela", "advancements.adventure.shoot_arrow.title": "Na<PERSON><PERSON><PERSON>!", "advancements.adventure.sleep_in_bed.description": "Ilta na bet per kawariti gjenvona-plas", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON><PERSON> onar", "advancements.adventure.sniper_duel.description": "Vras ponedjin na prapatai fu 50 metoru na pol", "advancements.adventure.sniper_duel.title": "Ruz<PERSON><PERSON> na ruzhjo", "advancements.adventure.spyglass_at_dragon.description": "<PERSON> <PERSON><PERSON> rju mit prapaseting", "advancements.adventure.spyglass_at_dragon.title": "Ljetauto we?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON> mit prapaseting", "advancements.adventure.spyglass_at_ghast.title": "Luht<PERSON>ah we?", "advancements.adventure.spyglass_at_parrot.description": "Se hanufugel mit prapasetinn", "advancements.adventure.spyglass_at_parrot.title": "Fogel we?", "advancements.adventure.summon_iron_golem.description": "Nasiintua ayadjin jerkat per apu eshku polisnen", "advancements.adventure.summon_iron_golem.title": "Apud<PERSON> k<PERSON>n", "advancements.adventure.throw_trident.description": "<PERSON><PERSON> sa<PERSON> joku made.\nVikti: <PERSON><PERSON><PERSON> tolka k<PERSON>brukting fu du ie warui miepie.", "advancements.adventure.throw_trident.title": "<PERSON>i tsa<PERSON>, sini ersentakena", "advancements.adventure.totem_of_undying.description": "Bruk ervona-boneka per humba shinu", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trade.description": "Kuupahok braa mit statdjin", "advancements.adventure.trade.title": "<PERSON><PERSON> teraz!", "advancements.adventure.trade_at_world_height.description": "Sad<PERSON>a tinn mit statzhin lonn veltele", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Nasii afto komuskefal ein raz os plus: Berk<PERSON>k, Han<PERSON>, Baks<PERSON>e, Eshku, Pinuno, Rovo, Hav, Strelating", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Jerkatergo mit smak", "advancements.adventure.trim_with_any_armor_pattern.description": "Mah falena eshkuklea mitt jerkatstol", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON><PERSON> neo kozha", "advancements.adventure.two_birds_one_arrow.description": "Vras ni on mit ein bidestrela", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON>na na ein strela", "advancements.adventure.under_lock_and_key.description": "Auki zigarum mit klucj fu iskatdai", "advancements.adventure.under_lock_and_key.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.adventure.use_lodestone.title": "Country Lode, Take Me Home", "advancements.adventure.very_very_frightening.description": "Zeusslag Polisdjin", "advancements.adventure.very_very_frightening.title": "Taktaktak achor", "advancements.adventure.voluntary_exile.description": "Vras atamadjin fu razharza. \n<PERSON><PERSON><PERSON> braa iskat borte fu stat apaarmirai...", "advancements.adventure.voluntary_exile.title": "Erkeshitena na vil f'sebja", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Jalaka oba alk upash... ytten spada inhe", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.who_needs_rockets.description": "Bruk luftbam per jit sebja oba na 8 dado", "advancements.adventure.who_needs_rockets.title": "A na dare trengena ljet<PERSON>?", "advancements.adventure.whos_the_pillager_now.description": "Suru polis<PERSON><PERSON><PERSON><PERSON> made lik sore tak dua suru du made", "advancements.adventure.whos_the_pillager_now.title": "Da se, dare pravda spilk<PERSON>~", "advancements.empty": "Mie her ohare kana...", "advancements.end.dragon_breath.description": "Eshku rjuuhengi ine glasbadjeil", "advancements.end.dragon_breath.title": "<PERSON><PERSON>", "advancements.end.dragon_egg.description": "<PERSON><PERSON>", "advancements.end.dragon_egg.title": "Mirailindazma", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON><PERSON> fynna", "advancements.end.elytra.title": "Ele tte mono siniplas", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> moshir kara", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.find_end_city.description": "Davai ende, nazenai?", "advancements.end.find_end_city.title": "Polis na Owari fu End", "advancements.end.kill_dragon.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.end.kill_dragon.title": "Enddjiju", "advancements.end.levitate.description": "Lyettanen 50 dado grun harza fu shulker", "advancements.end.levitate.title": "<PERSON> sejena her kara", "advancements.end.respawn_dragon.description": "Gjensintua enderrju", "advancements.end.respawn_dragon.title": "End... <PERSON><PERSON>d lan...", "advancements.end.root.description": "O<PERSON> had<PERSON>?", "advancements.end.root.title": "End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Apudjin spada torta akote nuotidado", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON> fu sintwadag", "advancements.husbandry.allay_deliver_item_to_player.description": "Apudjin bidra du made ting", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON><PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON><PERSON> atheks<PERSON>l inne klingedai", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON> usfait", "advancements.husbandry.balanced_diet.description": "<PERSON> al ka deki namena, hataa nai z<PERSON>va", "advancements.husbandry.balanced_diet.title": "Heiwa <PERSON>", "advancements.husbandry.breed_all_animals.description": "Maha al vauvadur!", "advancements.husbandry.breed_all_animals.title": "Ni na ni", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON>ra ni duur mitrjoo cji made", "advancements.husbandry.breed_an_animal.title": "Bonk! Da qirum made", "advancements.husbandry.complete_catalogue.description": "Znakoma al kotfal!", "advancements.husbandry.complete_catalogue.title": "Ka <PERSON>?", "advancements.husbandry.feed_snifflet.description": "Sipponen na nioidjinnen", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "<PERSON>a sakana", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "Har al babagkirkas lonn hartumam", "advancements.husbandry.froglights.title": "Tre shal ein hjerne!", "advancements.husbandry.kill_axolotl_target.description": "Na krigklaani fu Axolotl jing", "advancements.husbandry.kill_axolotl_target.title": "Zdorvapanpizma fu mikzma!", "advancements.husbandry.leash_all_frog_variants.description": "Festa al fal fu baba mit gushonoito", "advancements.husbandry.leash_all_frog_variants.title": "Ói! Al baba na pol pal na pol", "advancements.husbandry.make_a_sign_glow.description": "Mah kotumam fu mifalva anta kirkas", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON>am kotumam!", "advancements.husbandry.netherite_hoe.description": "Bruk netherdadonen per <PERSON><PERSON><PERSON><PERSON>, de gjenraz<PERSON> vonasenta<PERSON> tu", "advancements.husbandry.netherite_hoe.title": "Tak d<PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON> jaitso fu nioidjin", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON><PERSON>!", "advancements.husbandry.plant_any_sniffer_seed.description": "Enterra mivilena pie fu Nioidjin", "advancements.husbandry.plant_any_sniffer_seed.title": "Enterra pie fu Dantid", "advancements.husbandry.plant_seed.description": "Nasii pie au se rupne", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "Bra lyk Neo", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON> chip mit œ<PERSON>ke", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Ochkip!", "advancements.husbandry.root.description": "Mange mikshtof au namshtof na velt", "advancements.husbandry.root.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Bruk honokrais per ubidra mjalt melittavove kara, nai na rovo fu melittara", "advancements.husbandry.safely_harvest_honey.title": "Melittami<PERSON>", "advancements.husbandry.silk_touch_nest.description": "Ugoki melittavove, 3 melitta ine, bruk <PERSON>", "advancements.husbandry.silk_touch_nest.title": "<PERSON><PERSON>", "advancements.husbandry.tactical_fishing.description": "Sada sakana... uten sakanasadasen!", "advancements.husbandry.tactical_fishing.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON><PERSON> jirinos mit klingjedai", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON> yoku duur", "advancements.husbandry.tame_an_animal.title": "Letstebraa mik per altid", "advancements.husbandry.wax_off.description": "Zengaru dresha kupkatdada kara!", "advancements.husbandry.wax_off.title": "Dreshite", "advancements.husbandry.wax_on.description": "Nasi m<PERSON>mi kupkatdado made!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "An<PERSON><PERSON><PERSON> al taikakawarena na samaraz", "advancements.nether.all_effects.title": "Vapa pan, do<PERSON><PERSON><PERSON>?", "advancements.nether.all_potions.description": "An<PERSON><PERSON><PERSON> al taikaishke na samaraz", "advancements.nether.all_potions.title": "Vapa pan", "advancements.nether.brew_potion.description": "<PERSON><PERSON>", "advancements.nether.brew_potion.title": "<PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON><PERSON> gje<PERSON>ado lets<PERSON> zeus", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> t<PERSON><PERSON> \"nun\" <PERSON><PERSON>", "advancements.nether.create_beacon.description": "Maha au nasii kirkasflakka", "advancements.nether.create_beacon.title": "Surykbidra lunakirkas", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON> lets<PERSON><PERSON> kirkasflakka", "advancements.nether.create_full_beacon.title": "Djong kirkas ne", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> piglin mitt gelt", "advancements.nether.distract_piglin.title": "<PERSON><PERSON> kirakira", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON> al lantfal fu Nether", "advancements.nether.explore_nether.title": "<PERSON>", "advancements.nether.fast_travel.description": "Mit nethertropos shkoi 7 km na Atamavelt", "advancements.nether.fast_travel.title": "<PERSON><PERSON><PERSON>", "advancements.nether.find_bastion.description": "Shkine gam<PERSON>tumam", "advancements.nether.find_bastion.title": "<PERSON><PERSON><PERSON> plus chad", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON><PERSON> se<PERSON><PERSON> z<PERSON> made fu Nether", "advancements.nether.find_fortress.title": "Erkømuskevove", "advancements.nether.get_wither_skull.description": "<PERSON>ha atama fu Witherponedjin", "advancements.nether.get_wither_skull.title": "Kjadai a<PERSON> p<PERSON>jin", "advancements.nether.loot_bastion.description": "<PERSON><PERSON> baksu kara ine Protohuomidai", "advancements.nether.loot_bastion.title": "Krigsvinja", "advancements.nether.netherite_armor.description": "<PERSON>ada hel eshkuklea na Netherit", "advancements.nether.netherite_armor.title": "Enmy<PERSON>ra bite", "advancements.nether.obtain_ancient_debris.description": "Saada protomyl", "advancements.nether.obtain_ancient_debris.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON> honov<PERSON>ka honodjin kara", "advancements.nether.obtain_blaze_rod.title": "Ine hono made", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON> naku-g<PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.title": "Dare cher nakuvoshii?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON><PERSON> mit honomjah", "advancements.nether.return_to_sender.title": "<PERSON><PERSON> made", "advancements.nether.ride_strider.description": "Shkoi na jalakadjin mitt erstranitari-na-vjetka", "advancements.nether.ride_strider.title": "Afto cjip har jalaka", "advancements.nether.ride_strider_in_overworld_lava.description": "<PERSON><PERSON> erbides havnen fu zhotishke inne Obavelt", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> trengena", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "“Vi de<PERSON>; “vi za” <PERSON>er", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON><PERSON>, de bidra <PERSON><PERSON><PERSON> made... de vras", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON> knzi<PERSON>", "advancements.nether.use_lodestone.description": "Bruk dokobma na plasdado", "advancements.nether.use_lodestone.title": "<PERSON><PERSON> made virta", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "<PERSON><PERSON><PERSON> sh<PERSON>s au <PERSON><PERSON><PERSON><PERSON> made", "advancements.story.cure_zombie_villager.title": "Odaremreformadjin", "advancements.story.deflect_arrow.description": "<PERSON><PERSON><PERSON><PERSON> strela mitt kømuske", "advancements.story.deflect_arrow.title": "<PERSON><PERSON>, danke", "advancements.story.enchant_item.description": "Taikainonno ting oba taikastol", "advancements.story.enchant_item.title": "Taikadjin", "advancements.story.enter_the_end.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON> d<PERSON>i", "advancements.story.enter_the_end.title": "End we?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON> de p<PERSON>jot sit shkine netherdver<PERSON>i", "advancements.story.enter_the_nether.title": "Treng skjoi <PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ender me", "advancements.story.follow_ender_eye.title": "Un se pitka ima", "advancements.story.form_obsidian.description": "<PERSON>ha au gørmoi dado fu Glasishi", "advancements.story.form_obsidian.title": "Maha glas bides hono", "advancements.story.iron_tools.description": "Mahaneo fu puangørmoi", "advancements.story.iron_tools.title": "Jertak trelo", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON><PERSON> fu badjel <PERSON>", "advancements.story.lava_bucket.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.description": "<PERSON><PERSON>", "advancements.story.mine_diamond.title": "<PERSON><PERSON>!", "advancements.story.mine_stone.description": "<PERSON><PERSON> ishi mit neo puangørmoi", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "<PERSON><PERSON>ku sebja bruk tel fu eshkuklea na yerkat", "advancements.story.obtain_armor.title": "Trag neo kozha", "advancements.story.root.description": "<PERSON>hal au vimi na spil", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON><PERSON><PERSON> an<PERSON> djin", "advancements.story.shiny_gear.title": "Trag al helenaishi ka jam", "advancements.story.smelt_iron.description": "Vapatoreu <PERSON>", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON> brukting", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.title": "<PERSON><PERSON> <PERSON>", "advancements.toast.challenge": "Haastana jingena!", "advancements.toast.goal": "Ka vilena, to surena!", "advancements.toast.task": "<PERSON><PERSON><PERSON><PERSON><PERSON> jo!", "argument.anchor.invalid": "Uwaki shalfestaplas %s", "argument.angle.incomplete": "<PERSON>i hel (vildan 1 ikatai)", "argument.angle.invalid": "Uwaki ik", "argument.block.id.invalid": "Kuamnai dadofal %s", "argument.block.property.duplicate": "Impla '%s' mono deki nasii einraz per dado %s", "argument.block.property.invalid": "Dado %s nai ainlat '%s' na %s-impla", "argument.block.property.novalue": "<PERSON><PERSON><PERSON> impla per kawarjena '%s' fu dado %s", "argument.block.property.unclosed": "<PERSON><PERSON><PERSON> ] per ma<PERSON><PERSON> dadoi<PERSON>la", "argument.block.property.unknown": "Dado %s har nai %s na impla", "argument.block.tag.disallowed": "<PERSON><PERSON>en uso her, mono dado pravda", "argument.color.invalid": "Kuamnai faria %s", "argument.component.invalid": "<PERSON><PERSON><PERSON> dwaibmatel: %s", "argument.criteria.invalid": "Uwaki impla '%s'", "argument.dimension.invalid": "Uwaki abad '%s'", "argument.double.big": "Double-lasku mus nai oba %s, men se %s", "argument.double.low": "Double-lasku mus nai unna %s, men se %s", "argument.entity.invalid": "Uwaki namae os UUID", "argument.entity.notfound.entity": "<PERSON>l shal finna dan", "argument.entity.notfound.player": "<PERSON>l spild<PERSON> finna", "argument.entity.options.advancements.description": "Spildjin mit jing<PERSON><PERSON><PERSON>na", "argument.entity.options.distance.description": "Prapatatai shal made", "argument.entity.options.distance.negative": "Prapatai lakinai lop (unna 0)", "argument.entity.options.dx.description": "Shal melan x au x + dx", "argument.entity.options.dy.description": "Shal melan y au y + dy", "argument.entity.options.dz.description": "Shal melan z au z + dz", "argument.entity.options.gamemode.description": "Speldjin na speltropos", "argument.entity.options.inapplicable": "<PERSON><PERSON><PERSON> '%s' u<PERSON>ki her", "argument.entity.options.level.description": "Mestariatai", "argument.entity.options.level.negative": "Mestari-atai la<PERSON>ai lop (unna 0)", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON> shal suruk made", "argument.entity.options.limit.toosmall": "Ele mus 1 os plus", "argument.entity.options.mode.invalid": "Uwaki os shirenanai spiltropos '%s'", "argument.entity.options.name.description": "<PERSON><PERSON> fu shal", "argument.entity.options.nbt.description": "Shal ka NBT har", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON> ka du vil", "argument.entity.options.scores.description": "<PERSON><PERSON> ka spilpik har", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> shal", "argument.entity.options.sort.irreversible": "Uso os nai shirena fal fu mahaparyaad '%s'", "argument.entity.options.tag.description": "<PERSON><PERSON> mit namaenen", "argument.entity.options.team.description": "<PERSON><PERSON> na k<PERSON>ani", "argument.entity.options.type.description": "S<PERSON> na fal", "argument.entity.options.type.invalid": "Uwaki os shirenanai shalfal '%s'", "argument.entity.options.unknown": "<PERSON><PERSON><PERSON> '%s'", "argument.entity.options.unterminated": "<PERSON><PERSON><PERSON> ka kawarjena owari", "argument.entity.options.valueless": "Træng impla per kawarina '%s' men jam nai", "argument.entity.options.x.description": "x-abad", "argument.entity.options.x_rotation.description": "X-gunro fu shal", "argument.entity.options.y.description": "y-abad", "argument.entity.options.y_rotation.description": "Y-gunro fu shal", "argument.entity.options.z.description": "z-abad plas", "argument.entity.selector.allEntities": "<PERSON>hal", "argument.entity.selector.allPlayers": "Al spildjin", "argument.entity.selector.missing": "Fal fu sentakuko jam nai", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON><PERSON> shal", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON> lets<PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.randomPlayer": "<PERSON><PERSON> spi<PERSON>", "argument.entity.selector.self": "Afto shal", "argument.entity.selector.unknown": "T<PERSON>o shalsentakufal '%s'", "argument.entity.toomany": "<PERSON><PERSON><PERSON>n ting laki, men antajena sentakuko laki plus ting", "argument.enum.invalid": "Uwa<PERSON> atai \"%s\"", "argument.float.big": "Float-lasku treng %s na ele, men finna %s", "argument.float.low": "Float-lasku treng %s na pol, men finna %s", "argument.gamemode.invalid": "Znakomanai speltropos: %s", "argument.hexcolor.invalid": "Kakuna '%s' tte uwaki na tenkasi-tro", "argument.id.invalid": "<PERSON><PERSON><PERSON>", "argument.id.unknown": "Trelo sjallasku %s", "argument.integer.big": "Hel-lasku mus sama os minus ka %s, se %s", "argument.integer.low": "Hellasku mus sama os plus ka %s, a jam %s", "argument.item.id.invalid": "Knshirena ting '%s'", "argument.item.tag.disallowed": "<PERSON>aenen uso her, mono ting", "argument.literal.incorrect": "<PERSON><PERSON><PERSON> tsat<PERSON> %s", "argument.long.big": "Pitkalasku mus tak os minus stur ka %s; jam %s", "argument.long.low": "Pitka lasku mus sama os plus stur ka %s; jam %s", "argument.message.too_long": "Pochta obahobit (%s > lestehobit %s kirain)", "argument.nbt.array.invalid": "Uwaki fal fu tumam '%s'", "argument.nbt.array.mixed": "%s dekinai nasiena inne %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON>", "argument.nbt.expected.value": "<PERSON><PERSON><PERSON> impla", "argument.nbt.list.mixed": "Dekinai nasii %s inne tumam made fu %s", "argument.nbt.trailing": "Uwaki za viljena owari fu implavirta", "argument.player.entities": "<PERSON>o spildjin deki sentakena mit afto jew<PERSON>, men antajena sentakuko auen laki ting", "argument.player.toomany": "<PERSON><PERSON><PERSON>n spildjin laki, men ant<PERSON><PERSON> sentakuko ainlat plus spildjin", "argument.player.unknown": "Jamnai spildjin", "argument.pos.missing.double": "<PERSON><PERSON>dan p<PERSON>", "argument.pos.missing.int": "Vildan plas fu dado", "argument.pos.mixed": "Veltlik au sebjalik parjadtropos dekinai samatid brukjena (al mit ^, os nil mus)", "argument.pos.outofbounds": "Plas ekso la<PERSON>.", "argument.pos.outofworld": "Plas ekso velt!", "argument.pos.unloaded": "Plas nai lesena", "argument.pos2d.incomplete": "<PERSON><PERSON> hel (vildan 2 plaslasku)", "argument.pos3d.incomplete": "<PERSON><PERSON> hel (vildan 3 plaslasku)", "argument.range.empty": "<PERSON><PERSON><PERSON> impla os karamade", "argument.range.ints": "<PERSON>o da hel lasku, la<PERSON><PERSON> tel", "argument.range.swapped": "Pol dekinai plusstor na ele", "argument.resource.invalid_type": "Dwaibmatel '%s' har uso fal '%s' (vilena '%s')", "argument.resource.not_found": "Fynnakinai tel '%s' na fal '%s'", "argument.resource_or_id.failed_to_parse": "Humba dan lesa zdan<PERSON>: %s", "argument.resource_or_id.invalid": "Uwa<PERSON> id os na<PERSON>n", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Impla '%s' har uso fal '%s' (vilti '%s')", "argument.resource_tag.not_found": "Nai deki fynna na<PERSON> '%s' fu fal '%s'", "argument.rotation.incomplete": "Nai hel (vildan 2 abad na plaslasku)", "argument.scoreHolder.empty": "Nil hardjin fu spilpik mit tsunaga afto made finnena", "argument.scoreboardDisplaySlot.invalid": "Knshirena mahseplas '%s'", "argument.style.invalid": "Uwaki setropos: %s", "argument.time.invalid_tick_count": "Atai fu tidleo mus nai lap", "argument.time.invalid_unit": "<PERSON><PERSON><PERSON>", "argument.time.tick_count_too_low": "Lasku fu tidleo mus minusnai %s, a fynna dan %s", "argument.uuid.invalid": "Uwaki UUID", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Kuamnai dadoimpla %s", "arguments.function.tag.unknown": "Uwaki dwaibma-namaenen %s", "arguments.function.unknown": "Uwaki dwai<PERSON>ko %s", "arguments.item.component.expected": "Vil dan tingtel", "arguments.item.component.malformed": "Warukakena '%s' tel: '%s'", "arguments.item.component.repeated": "Tingtel '%s' g<PERSON><PERSON>a, men mono ainlatena ein lasku", "arguments.item.component.unknown": "Knshirena tingtel '%s'", "arguments.item.malformed": "Warumahena: '%s'", "arguments.item.overstacked": "%s deki mono %s na kaban", "arguments.item.predicate.malformed": "Warukakena '%s' json: '%s'", "arguments.item.predicate.unknown": "<PERSON><PERSON><PERSON><PERSON> ting-json: '%s'", "arguments.item.tag.unknown": "Trelo ting-namaenen %s", "arguments.nbtpath.node.invalid": "Uwaki NBT-naruga-dwaibmatel", "arguments.nbtpath.nothing_found": "Nai finna dwaib<PERSON>l lik: %s", "arguments.nbtpath.too_deep": "Ti-NBT obaidaun", "arguments.nbtpath.too_large": "Ti-NBT obastor", "arguments.objective.notFound": "Knznakoma muspie '%s'", "arguments.objective.readonly": "Muspie '%s' tte lesa-mono", "arguments.operation.div0": "<PERSON><PERSON><PERSON> lasku oba nil", "arguments.operation.invalid": "<PERSON><PERSON><PERSON> iskat", "arguments.swizzle.invalid": "<PERSON><PERSON><PERSON> swizzle, vil bruk 'x', 'y' au 'z' na joku parjad", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "Arkatai fu slag", "attribute.name.attack_knockback": "Ugokiatai fu slag", "attribute.name.attack_speed": "Bystratai fu slag", "attribute.name.block_break_speed": "Bystratai fu dadoperpa", "attribute.name.block_interaction_range": "Block Interaction Range", "attribute.name.burning_time": "<PERSON><PERSON> tida<PERSON>", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Entity Interaction Range", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Razatai na spadaarka", "attribute.name.flying_speed": "Bystratai na ljeta", "attribute.name.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "Arkapik fu slag", "attribute.name.generic.attack_knockback": "Ugokiatai fu slag", "attribute.name.generic.attack_speed": "Bystratai fu slag", "attribute.name.generic.block_interaction_range": "Prapatai-ele fu dadobruk", "attribute.name.generic.burning_time": "Tidatai na hono", "attribute.name.generic.entity_interaction_range": "Prapatai-ele fu s<PERSON>ruk", "attribute.name.generic.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.generic.fall_damage_multiplier": "Razatai fu spaada-arkatai", "attribute.name.generic.flying_speed": "Bystratai na ljeta", "attribute.name.generic.follow_range": "Prapaatai fu sjal-r<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "Pal-djongata<PERSON>", "attribute.name.generic.knockback_resistance": "Kundargutsjo arkakomus<PERSON>", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_health": "Ele fu zdorvatai", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "Bystratai", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "<PERSON>iha spada-atai", "attribute.name.generic.scale": "Stuuratai", "attribute.name.generic.step_height": "Obatai fu leo", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "Spa<PERSON>z<PERSON>", "attribute.name.horse.jump_strength": "<PERSON><PERSON><PERSON> p<PERSON>", "attribute.name.jump_strength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.knockback_resistance": "Knockback Resistance", "attribute.name.luck": "<PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Letstemange zdorvapik", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Brukatai na shkoi", "attribute.name.movement_speed": "Bystratai", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Bystratai fu dadoperpa", "attribute.name.player.block_interaction_range": "Prapatai-ele fu dadobruk", "attribute.name.player.entity_interaction_range": "Prapatai-ele fu s<PERSON>ruk", "attribute.name.player.mining_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "Scale", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Isgeškoihuursimper", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON> gye<PERSON> made", "biome.minecraft.badlands": "Skald", "biome.minecraft.bamboo_jungle": "Sembaumdasos", "biome.minecraft.basalt_deltas": "<PERSON><PERSON><PERSON>", "biome.minecraft.beach": "San<PERSON>", "biome.minecraft.birch_forest": "Volkod<PERSON>", "biome.minecraft.cherry_grove": "Sakuradasos", "biome.minecraft.cold_ocean": "<PERSON>ui mare", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "Ku<PERSON>dasos", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON> samui mare", "biome.minecraft.deep_dark": "<PERSON><PERSON> kurai", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON> it<PERSON> mare", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON> la<PERSON> mare", "biome.minecraft.deep_ocean": "<PERSON>un mare", "biome.minecraft.desert": "Sandai", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON>", "biome.minecraft.end_barrens": "<PERSON> y<PERSON>vona", "biome.minecraft.end_highlands": "<PERSON> fja<PERSON>t", "biome.minecraft.end_midlands": "<PERSON> me<PERSON>", "biome.minecraft.eroded_badlands": "Razplujenaskald", "biome.minecraft.flower_forest": "Luledasos", "biome.minecraft.forest": "<PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON>na mare", "biome.minecraft.frozen_peaks": "Samuidai pikara", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON> virta", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Samuiskhe aistio", "biome.minecraft.jagged_peaks": "Bokbok Bergpik", "biome.minecraft.jungle": "Pluidasos", "biome.minecraft.lukewarm_ocean": "Lagomvapa mare", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>-satter", "biome.minecraft.meadow": "<PERSON><PERSON>", "biome.minecraft.mushroom_fields": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.nether_wastes": "Netherskald", "biome.minecraft.ocean": "Mare", "biome.minecraft.old_growth_birch_forest": "Volkodja protodasos", "biome.minecraft.old_growth_pine_taiga": "Protodasos fu julbaumdai", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON> pro<PERSON>", "biome.minecraft.pale_garden": "<PERSON><PERSON>", "biome.minecraft.plains": "Munauki", "biome.minecraft.river": "Virta", "biome.minecraft.savanna": "Vapapol", "biome.minecraft.savanna_plateau": "Vapapolstol", "biome.minecraft.small_end_islands": "Chisai End ishkelant", "biome.minecraft.snowy_beach": "<PERSON>ash maresan", "biome.minecraft.snowy_plains": "Upash munauki", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_taiga": "Upashdasos", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON> berk<PERSON>", "biome.minecraft.sparse_jungle": "Plyjdasos-htel<PERSON>", "biome.minecraft.stony_peaks": "Is<PERSON>ik", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Solluleauki", "biome.minecraft.swamp": "Satter", "biome.minecraft.taiga": "Taigá", "biome.minecraft.the_end": "<PERSON><PERSON>", "biome.minecraft.the_void": "<PERSON><PERSON><PERSON>", "biome.minecraft.warm_ocean": "Vapa mare", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON> is<PERSON><PERSON> be<PERSON>", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON> be<PERSON>", "biome.minecraft.windswept_savanna": "Vintena vapapol", "biome.minecraft.wooded_badlands": "Dasosskald", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "Akakja dvera", "block.minecraft.acacia_fence": "Akakja vjetkatumam", "block.minecraft.acacia_fence_gate": "Akakja vjetkadvera", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_leaves": "Akakjalehti", "block.minecraft.acacia_log": "Akakjadjido", "block.minecraft.acacia_planks": "Akakja-baumtel", "block.minecraft.acacia_pressure_plate": "Akakja j<PERSON>", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.acacia_sign": "<PERSON><PERSON><PERSON><PERSON> kotumam", "block.minecraft.acacia_slab": "Akakja handado", "block.minecraft.acacia_stairs": "<PERSON><PERSON><PERSON><PERSON> leo", "block.minecraft.acacia_trapdoor": "Akakja jalakadvera", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> shankotumam shann tumam", "block.minecraft.acacia_wall_sign": "<PERSON><PERSON><PERSON><PERSON> kotumam per tumam", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.activator_rail": "Zeusøze-naruga", "block.minecraft.air": "Luft", "block.minecraft.allium": "<PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "<PERSON><PERSON><PERSON><PERSON> dado", "block.minecraft.amethyst_cluster": "<PERSON><PERSON>acha petra", "block.minecraft.ancient_debris": "Protomyl", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Handado na andesit", "block.minecraft.andesite_stairs": "Andesitleo", "block.minecraft.andesite_wall": "Andesittumam", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "<PERSON><PERSON><PERSON>-d<PERSON>o", "block.minecraft.attached_pumpkin_stem": "Djido fu achor<PERSON><PERSON> tsun<PERSON>na", "block.minecraft.azalea": "Azalija", "block.minecraft.azalea_leaves": "Lehti fu azalija", "block.minecraft.azure_bluet": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_block": "Sembaumdado", "block.minecraft.bamboo_button": "Se<PERSON><PERSON> presmi", "block.minecraft.bamboo_door": "Se<PERSON>um dver", "block.minecraft.bamboo_fence": "Sembaum vjetkatumam", "block.minecraft.bamboo_fence_gate": "Sembaum vjetkadvera", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> shan<PERSON>m", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON><PERSON> sembaum", "block.minecraft.bamboo_mosaic_slab": "Breitena sembaum na handado", "block.minecraft.bamboo_mosaic_stairs": "Breitena sembaum na leo", "block.minecraft.bamboo_planks": "Sembaumtel", "block.minecraft.bamboo_pressure_plate": "Se<PERSON><PERSON> jalakata<PERSON>", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON> kotumam", "block.minecraft.bamboo_slab": "Se<PERSON>um handado", "block.minecraft.bamboo_stairs": "Se<PERSON>um leo", "block.minecraft.bamboo_trapdoor": "Se<PERSON><PERSON> jalakadver", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> shankotumam shann tumam", "block.minecraft.bamboo_wall_sign": "Sembaum kotumam per tumam", "block.minecraft.banner.base.black": "Kuro pol", "block.minecraft.banner.base.blue": "Blau pol", "block.minecraft.banner.base.brown": "<PERSON><PERSON> pol", "block.minecraft.banner.base.cyan": "Tsulus pol", "block.minecraft.banner.base.gray": "<PERSON><PERSON> pol", "block.minecraft.banner.base.green": "Midori pol", "block.minecraft.banner.base.light_blue": "Sini pol", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON> pol", "block.minecraft.banner.base.lime": "Shiromidori pol", "block.minecraft.banner.base.magenta": "Romura pol", "block.minecraft.banner.base.orange": "Portokali pol", "block.minecraft.banner.base.pink": "Roza pol", "block.minecraft.banner.base.purple": "Muranen pol", "block.minecraft.banner.base.red": "Ro pol", "block.minecraft.banner.base.white": "Shiro pol", "block.minecraft.banner.base.yellow": "<PERSON><PERSON> pol", "block.minecraft.banner.border.black": "<PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "Portokali bideskrais", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> hina gdent", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON> hina gdent", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON> hina gdent", "block.minecraft.banner.bricks.light_blue": "<PERSON>i hina gdent", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.orange": "Portokali hina gdent", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON> hina gdent", "block.minecraft.banner.bricks.red": "<PERSON>o hina gdent", "block.minecraft.banner.bricks.white": "<PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON> hina g<PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON> me<PERSON>", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Portokali melankrais", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON>ranen me<PERSON>s", "block.minecraft.banner.circle.red": "<PERSON><PERSON> <PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.green": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON> Creeper bumshtof", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.orange": "Portokali Creeper bumshtof", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.purple": "Murane<PERSON> C<PERSON> bumshtof", "block.minecraft.banner.creeper.red": "<PERSON>o <PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.white": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON> bumshtof", "block.minecraft.banner.cross.black": "<PERSON><PERSON>", "block.minecraft.banner.cross.blue": "Blau eksfal", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.gray": "<PERSON><PERSON>", "block.minecraft.banner.cross.green": "Midori eksfal", "block.minecraft.banner.cross.light_blue": "Sini eksfal", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON>ri <PERSON>", "block.minecraft.banner.cross.magenta": "Romura eksfal", "block.minecraft.banner.cross.orange": "Portokali eksfal", "block.minecraft.banner.cross.pink": "Roza eksfal", "block.minecraft.banner.cross.purple": "Muranen eksfal", "block.minecraft.banner.cross.red": "Ro eksfal", "block.minecraft.banner.cross.white": "<PERSON><PERSON>l", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON> mitt hammas", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON> bides<PERSON><PERSON> mitt hammas", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> bid<PERSON><PERSON>is mitt hammas", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON> bid<PERSON>is mitt hammas", "block.minecraft.banner.curly_border.light_blue": "<PERSON>i bideskrais mitt hammas", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.orange": "Portokali bideskrais mitt hammas", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON><PERSON> bides<PERSON>is mitt hammas", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON> bid<PERSON>is mitt hammas", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON> bid<PERSON> mitt hammas", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> mitt hammas", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON> treik", "block.minecraft.banner.diagonal_left.blue": "Blau migioba treik", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON> mi<PERSON> treik", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON> migio<PERSON> treik", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON> mi<PERSON> treik", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON> mi<PERSON> treik", "block.minecraft.banner.diagonal_left.light_blue": "Sini migioba treik", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON> mi<PERSON> treik", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON><PERSON><PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON> mi<PERSON> treik", "block.minecraft.banner.diagonal_left.orange": "Portokali migioba treik", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON> mi<PERSON> treik", "block.minecraft.banner.diagonal_left.purple": "Muranen migioba treik", "block.minecraft.banner.diagonal_left.red": "Ro migioba treik", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON> treik", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON> l<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_right.blue": "Blau ljevaoba treik", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON> l<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON> l<PERSON> treik", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON> l<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON> lje<PERSON>a treik", "block.minecraft.banner.diagonal_right.light_blue": "Sini ljevaoba treik", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON> l<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON> lje<PERSON> treik", "block.minecraft.banner.diagonal_right.orange": "Portokali ljevaoba treik", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON> lje<PERSON> treik", "block.minecraft.banner.diagonal_right.purple": "Muranen ljevaoba treik", "block.minecraft.banner.diagonal_right.red": "Ro lje<PERSON>oba treik", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON> mi<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON> migi<PERSON>na treik", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON> mi<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON> migiunna treik", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON> migi<PERSON>na treik", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON> mi<PERSON>na treik", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON>i migiunna treik", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON><PERSON><PERSON> migi<PERSON>na treik", "block.minecraft.banner.diagonal_up_left.lime": "<PERSON><PERSON><PERSON><PERSON> mi<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON> mi<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON>i migiunna treik", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON> mi<PERSON>na treik", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON><PERSON> migiunna treik", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON> migiunna treik", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON> mi<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON> ljevaunna treik", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON> lje<PERSON> treik", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON> l<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON> lje<PERSON>na treik", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON> ljevaunna treik", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON><PERSON> lje<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON> lje<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_right.orange": "Portokali ljevaunna treik", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON> lje<PERSON> tre<PERSON>", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON> lje<PERSON>unna treik", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON> lje<PERSON><PERSON>na treik", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON> l<PERSON> t<PERSON>", "block.minecraft.banner.flow.black": "<PERSON><PERSON> virta", "block.minecraft.banner.flow.blue": "Blau virta", "block.minecraft.banner.flow.brown": "<PERSON><PERSON> virta", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON> virta", "block.minecraft.banner.flow.gray": "<PERSON><PERSON> virta", "block.minecraft.banner.flow.green": "<PERSON><PERSON> virta", "block.minecraft.banner.flow.light_blue": "Sini virta", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> virta", "block.minecraft.banner.flow.lime": "Z<PERSON>lon virta", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON> virta", "block.minecraft.banner.flow.orange": "Portokali virta", "block.minecraft.banner.flow.pink": "<PERSON><PERSON>a virta", "block.minecraft.banner.flow.purple": "Muranen virta", "block.minecraft.banner.flow.red": "Ros virta", "block.minecraft.banner.flow.white": "<PERSON><PERSON> virta", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> virta", "block.minecraft.banner.flower.black": "<PERSON><PERSON> lule bumshtof", "block.minecraft.banner.flower.blue": "Blau lule bumshtof", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> lule bumshtof", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON> lule bumshtof", "block.minecraft.banner.flower.gray": "<PERSON><PERSON> lule bumshtof", "block.minecraft.banner.flower.green": "Midori lule bumshtof", "block.minecraft.banner.flower.light_blue": "Sini lule bumshtof", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON> lule bumshtof", "block.minecraft.banner.flower.lime": "Shi<PERSON><PERSON>ri lule bumshtof", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON>ura lule bumshtof", "block.minecraft.banner.flower.orange": "Portokali lule bumshtof", "block.minecraft.banner.flower.pink": "<PERSON>oza lule bumshtof", "block.minecraft.banner.flower.purple": "Muranen lule bumshtof", "block.minecraft.banner.flower.red": "Ro lule bumshtof", "block.minecraft.banner.flower.white": "<PERSON>ro lule bumshtof", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> lule bumshtof", "block.minecraft.banner.globe.black": "<PERSON><PERSON>", "block.minecraft.banner.globe.blue": "Blau veltmjah", "block.minecraft.banner.globe.brown": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON> velt<PERSON>", "block.minecraft.banner.globe.gray": "<PERSON><PERSON>", "block.minecraft.banner.globe.green": "Midori veltmjah", "block.minecraft.banner.globe.light_blue": "Sini veltmjah", "block.minecraft.banner.globe.light_gray": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.globe.lime": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON> velt<PERSON>", "block.minecraft.banner.globe.orange": "Portokali veltmjah", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.globe.purple": "Muranen veltmjah", "block.minecraft.banner.globe.red": "Ro veltmjah", "block.minecraft.banner.globe.white": "<PERSON><PERSON>", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON>", "block.minecraft.banner.gradient.black": "<PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "Blau obavisk", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON>ori obavisk", "block.minecraft.banner.gradient.light_blue": "Sini obavisk", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.orange": "Portokali obavisk", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.gradient.purple": "Muranen obavisk", "block.minecraft.banner.gradient.red": "Ro obavisk", "block.minecraft.banner.gradient.white": "<PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.blue": "Blau unnavisk", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON> un<PERSON>", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.orange": "Portokali unnavisk", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON> un<PERSON>", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON> <PERSON>", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON>", "block.minecraft.banner.guster.black": "<PERSON><PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON> luf<PERSON>nen", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.guster.orange": "Portokali luftnen", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON> o<PERSON>l", "block.minecraft.banner.half_horizontal.blue": "<PERSON>lau obatel", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON> o<PERSON>l", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.light_blue": "Sini obatel", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.orange": "Portokali obatel", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.purple": "Muranen obatel", "block.minecraft.banner.half_horizontal.red": "Ro obatel", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON> obatel", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON> un<PERSON>l", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON> unnatel", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON> un<PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON> un<PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON> un<PERSON>l", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON><PERSON> un<PERSON>l", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON> un<PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "Portokali unnatel", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON><PERSON> un<PERSON>l", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON> unnatel", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.blue": "Blau ljevatel", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.half_vertical.orange": "Portokali ljevatel", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.half_vertical.purple": "Muranen ljevatel", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON><PERSON> migitel", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON> mi<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON> mi<PERSON>el", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON> mi<PERSON>", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.orange": "Portokali migitel", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON><PERSON> mi<PERSON>el", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON> mi<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON> ting", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "Portokali Ting", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON>", "block.minecraft.banner.mojang.white": "<PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON>", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> hana", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> hana", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON> hana", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> hana", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON> hana", "block.minecraft.banner.piglin.green": "<PERSON><PERSON> hana", "block.minecraft.banner.piglin.light_blue": "Sini hana", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON> hana", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON> hana", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON> hana", "block.minecraft.banner.piglin.orange": "Portokali hana", "block.minecraft.banner.piglin.pink": "<PERSON>oza hana", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON> hana", "block.minecraft.banner.piglin.red": "Ro hana", "block.minecraft.banner.piglin.white": "<PERSON><PERSON> hana", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON> hana", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "Blau melankereik", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "Midori melankereik", "block.minecraft.banner.rhombus.light_blue": "Sini <PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Portokali melankereik", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "Muranen melankereik", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON>", "block.minecraft.banner.skull.black": "<PERSON><PERSON> p<PERSON> bumshtof", "block.minecraft.banner.skull.blue": "<PERSON><PERSON> poneatama bumshtof", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> pone<PERSON>ma bumshtof", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON> poneatama bumshtof", "block.minecraft.banner.skull.gray": "<PERSON><PERSON> pone<PERSON>ma bumshtof", "block.minecraft.banner.skull.green": "<PERSON><PERSON> pone<PERSON>ma bumshtof", "block.minecraft.banner.skull.light_blue": "Sini poneatama bumshtof", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON> pone<PERSON>ma bumshtof", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> bumshtof", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON> poneatama bumshtof", "block.minecraft.banner.skull.orange": "Portokali poneatama bumshtof", "block.minecraft.banner.skull.pink": "<PERSON><PERSON>a pone<PERSON>ma bumshtof", "block.minecraft.banner.skull.purple": "Muranen poneatama bumshtof", "block.minecraft.banner.skull.red": "Ro poneatama bumshtof", "block.minecraft.banner.skull.white": "<PERSON><PERSON> p<PERSON> bumshtof", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> p<PERSON> bumshtof", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON> un<PERSON>a sensen", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON> un<PERSON>a sensen", "block.minecraft.banner.small_stripes.light_blue": "<PERSON>i unnaoba sensen", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON><PERSON> un<PERSON> sensen", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> unnaoba sensen", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON> unna<PERSON>a sensen", "block.minecraft.banner.small_stripes.red": "<PERSON>o unnaoba sensen", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON> un<PERSON>a sensen", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON> un<PERSON>a sensen", "block.minecraft.banner.square_bottom_left.black": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON> lje<PERSON>unna", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "<PERSON><PERSON> l<PERSON>na", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_bottom_left.orange": "Portokali ljevaunna", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON><PERSON> lje<PERSON>", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_bottom_left.white": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON> mi<PERSON>na", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON><PERSON> migi<PERSON>na", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON> mi<PERSON>na", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON> mi<PERSON>na", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON><PERSON> mi<PERSON>na", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON><PERSON> migi<PERSON>na", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.purple": "<PERSON><PERSON><PERSON> mi<PERSON>na", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON> mi<PERSON>na", "block.minecraft.banner.square_bottom_right.white": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.black": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.blue": "Blau ljevaoba", "block.minecraft.banner.square_top_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.cyan": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_top_left.light_blue": "Sini lje<PERSON>", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_left.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_left.magenta": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_top_left.orange": "Portokali ljevaoba", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_top_left.purple": "Muranen lje<PERSON>oba", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON> lje<PERSON>", "block.minecraft.banner.square_top_left.white": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON><PERSON> mi<PERSON>", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.light_blue": "<PERSON>i mi<PERSON>", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.orange": "Portokali migioba", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.purple": "<PERSON><PERSON><PERSON> mi<PERSON>", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.white": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.blue": "Blau plusfal", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "Midori plusfal", "block.minecraft.banner.straight_cross.light_blue": "Sini plus<PERSON>l", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "Portokali plusfal", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "Muranen plusfal", "block.minecraft.banner.straight_cross.red": "Ro plusfal", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON> un<PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON> unnatretel", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON> un<PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON> un<PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON> unnat<PERSON>el", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON> unnatretel", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON> un<PERSON>el", "block.minecraft.banner.stripe_bottom.orange": "Portokali unnatretel", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON><PERSON> unnatretel", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON> un<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON> un<PERSON>a", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON> un<PERSON>a", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON> un<PERSON>", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "Portokali unnaoba", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON><PERSON> un<PERSON><PERSON>a", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Portokali migil<PERSON>vastrela", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.blue": "Blau ljevamigistrela", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "Midori ljevamigistrela", "block.minecraft.banner.stripe_downright.light_blue": "Sini ljevamigistrela", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_downright.orange": "Portokali ljevamigistrela", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_downright.purple": "Muranen ljevamigistrela", "block.minecraft.banner.stripe_downright.red": "Ro lje<PERSON>la", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON> lje<PERSON>retel", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_left.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.orange": "Portokali ljevatretel", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON><PERSON> lje<PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "Blau ljevamigisen", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "Portokali ljevamigisen", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.purple": "Muranen l<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.orange": "Portokali migiteltel", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON><PERSON> mi<PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON> <PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON> obat<PERSON>el", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON> o<PERSON>el", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON> o<PERSON>", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON> o<PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON> obat<PERSON>el", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.stripe_top.orange": "Portokali obatretel", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON>ranen obatretel", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON> o<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.blue": "Blau unnatreik", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.orange": "Portokali unnatreik", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.purple": "Mu<PERSON><PERSON> un<PERSON>ik", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.blue": "Blau obatreik", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON> o<PERSON>", "block.minecraft.banner.triangle_top.light_blue": "<PERSON><PERSON> o<PERSON>ik", "block.minecraft.banner.triangle_top.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.orange": "Portokali obatreik", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.purple": "Muranen obatreik", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON> obat<PERSON>ik", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON> un<PERSON><PERSON>el mitt hammas", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON> un<PERSON>el mitt hammas", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON>i unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON><PERSON> un<PERSON><PERSON>el mitt hammas", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.orange": "Portokali unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON> un<PERSON><PERSON>el mitt hammas", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON><PERSON> unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.red": "<PERSON>o unnatretel mitt hammas", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON> unnat<PERSON>el mitt hammas", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> un<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON> obat<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> obat<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> o<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON> obatretel mitt hammas", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON> obatretel mitt hammas", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON> obatretel mitt hammas", "block.minecraft.banner.triangles_top.light_blue": "<PERSON>i obatretel mitt hammas", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON> obatretel mitt hammas", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON> obatretel mitt hammas", "block.minecraft.banner.triangles_top.orange": "Portokali obatretel mitt hammas", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON> obat<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON> obatretel mitt hammas", "block.minecraft.banner.triangles_top.red": "<PERSON>o obatretel mitt hammas", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON> obat<PERSON>el mitt hammas", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> o<PERSON>el mitt hammas", "block.minecraft.barrel": "Badjeldai", "block.minecraft.barrier": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Kirkasflakka", "block.minecraft.beacon.primary": "<PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON> taika", "block.minecraft.bed.no_sleep": "Mono laki kola na naht au na zeusburja", "block.minecraft.bed.not_safe": "Nai laki ilta ima; jam mø<PERSON>", "block.minecraft.bed.obstructed": "Afto bet tumamena", "block.minecraft.bed.occupied": "Afto bet ende sat", "block.minecraft.bed.too_far_away": "Nai laki ilta ima; bet-kara oba<PERSON>ka", "block.minecraft.bedrock": "Polishidai", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "Zamklinje", "block.minecraft.big_dripleaf": "<PERSON>ur ishkelehhti", "block.minecraft.big_dripleaf_stem": "Djido fu stur ishkel<PERSON><PERSON>i", "block.minecraft.birch_button": "Volkod<PERSON>i", "block.minecraft.birch_door": "Volkodja dvera", "block.minecraft.birch_fence": "Volkodja vjetkatumam", "block.minecraft.birch_fence_gate": "Volkodja vjetkadvera", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_leaves": "Volkodjalehti", "block.minecraft.birch_log": "Volkodjadjido", "block.minecraft.birch_planks": "Volkodja baumtel", "block.minecraft.birch_pressure_plate": "Volkodja jalakatasta", "block.minecraft.birch_sapling": "Volkodja bau<PERSON>nen", "block.minecraft.birch_sign": "Volkozha kotumam", "block.minecraft.birch_slab": "Volkodja handado", "block.minecraft.birch_stairs": "Volkodja leo", "block.minecraft.birch_trapdoor": "Volkodja jalakadvera", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> shankotumam shann tumam", "block.minecraft.birch_wall_sign": "Volkodja kotumam per tumam", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "<PERSON><PERSON> flakka", "block.minecraft.black_bed": "<PERSON><PERSON> bet", "block.minecraft.black_candle": "<PERSON><PERSON> cheri", "block.minecraft.black_candle_cake": "<PERSON><PERSON><PERSON> mit kuro cheri", "block.minecraft.black_carpet": "<PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON> v<PERSON> g<PERSON>", "block.minecraft.black_shulker_box": "<PERSON><PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.black_terracotta": "<PERSON><PERSON>", "block.minecraft.black_wool": "<PERSON><PERSON>", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Ku<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_stairs": "<PERSON><PERSON><PERSON> leo", "block.minecraft.blackstone_wall": "<PERSON><PERSON><PERSON> tumam", "block.minecraft.blast_furnace": "Erhonobet", "block.minecraft.blue_banner": "Blau flakka", "block.minecraft.blue_bed": "<PERSON><PERSON> <PERSON>t", "block.minecraft.blue_candle": "<PERSON><PERSON> cheri", "block.minecraft.blue_candle_cake": "<PERSON><PERSON><PERSON> mit blau cheri", "block.minecraft.blue_carpet": "Blau lammaspol", "block.minecraft.blue_concrete": "<PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON> h<PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON> vapajena glina", "block.minecraft.blue_ice": "<PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON> s<PERSON>jik", "block.minecraft.blue_shulker_box": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON> glina", "block.minecraft.blue_wool": "<PERSON><PERSON> <PERSON>", "block.minecraft.bone_block": "Ponedado", "block.minecraft.bookshelf": "Libretumam", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dado", "block.minecraft.brain_coral_fan": "Hjernegupkishi na tsubasa", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Gdent handado", "block.minecraft.brick_stairs": "G<PERSON> leo", "block.minecraft.brick_wall": "Gdenttumam", "block.minecraft.bricks": "Gdent", "block.minecraft.brown_banner": "<PERSON><PERSON> flakka", "block.minecraft.brown_bed": "<PERSON><PERSON> bett", "block.minecraft.brown_candle": "<PERSON><PERSON> cheri", "block.minecraft.brown_candle_cake": "<PERSON><PERSON><PERSON> mit brun cheri", "block.minecraft.brown_carpet": "<PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON> v<PERSON> glina", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.brown_terracotta": "<PERSON><PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Luftmjah-tumam", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dado", "block.minecraft.bubble_coral_fan": "Mjahgupkishi na tsubasa", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.budding_amethyst": "Petrasintwa Murakacha", "block.minecraft.bush": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Sagarolule", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.campfire": "Honobetnen", "block.minecraft.candle": "<PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON><PERSON> mit cheri", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Kartaergostol", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON><PERSON> mitt kao", "block.minecraft.cauldron": "Tofdai", "block.minecraft.cave_air": "Sirangaluft", "block.minecraft.cave_vines": "Siranga spadaruti", "block.minecraft.cave_vines_plant": "Siranga spadaruti zhido", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_button": "<PERSON> ta<PERSON>en", "block.minecraft.cherry_door": "Sakuradvera", "block.minecraft.cherry_fence": "Sakura-vjetkatumam", "block.minecraft.cherry_fence_gate": "Sakura-vjetkadvera", "block.minecraft.cherry_hanging_sign": "<PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "Sakuradjido", "block.minecraft.cherry_planks": "Sakura-baumtel", "block.minecraft.cherry_pressure_plate": "Sakura jalakatasta", "block.minecraft.cherry_sapling": "<PERSON> bau<PERSON>", "block.minecraft.cherry_sign": "<PERSON> kotumam", "block.minecraft.cherry_slab": "Sakura handado", "block.minecraft.cherry_stairs": "<PERSON> leo", "block.minecraft.cherry_trapdoor": "<PERSON> jalakadvera", "block.minecraft.cherry_wall_hanging_sign": "<PERSON> shankotumam shann tumam", "block.minecraft.cherry_wall_sign": "Sakura kotumam per tumam", "block.minecraft.cherry_wood": "<PERSON><PERSON>", "block.minecraft.chest": "Baksudai", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON> perpenanen", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON> libret<PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON><PERSON> kacha dado", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON> rosh sanishi", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON> m<PERSON> gd<PERSON>", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON> sa<PERSON>i", "block.minecraft.chiseled_stone_bricks": "<PERSON><PERSON><PERSON> ishi <PERSON>", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON> fete<PERSON>", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coal_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coal_ore": "Kolmeranja", "block.minecraft.coarse_dirt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "Handado na idaun le<PERSON>n", "block.minecraft.cobbled_deepslate_stairs": "Leo na idaun le<PERSON>n", "block.minecraft.cobbled_deepslate_wall": "Tu<PERSON><PERSON> na na idaun le<PERSON>n", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON> handado", "block.minecraft.cobblestone_stairs": "Ishinen<PERSON>", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON> tumam", "block.minecraft.cobweb": "Kashalka-Rye<PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Jewaltdado", "block.minecraft.comparator": "Redstonesamacjigau", "block.minecraft.composter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.conduit": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "<PERSON><PERSON><PERSON> dado", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON> d<PERSON>a", "block.minecraft.copper_grate": "Kupkat rjet", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON> jala<PERSON>a", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Perpena idaunishi-gdent", "block.minecraft.cracked_deepslate_tiles": "Perpena idaunishi prehotel", "block.minecraft.cracked_nether_bricks": "Perpena Nether gdent", "block.minecraft.cracked_polished_blackstone_bricks": "Gdent na perpena polena kuroishi", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON>a ishig<PERSON>", "block.minecraft.crafter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creaking_heart": "Ker f'Djidodjin", "block.minecraft.creeper_head": "Creeperatama", "block.minecraft.creeper_wall_head": "Creeperatama fu tumam", "block.minecraft.crimson_button": "<PERSON><PERSON>", "block.minecraft.crimson_door": "Rodai dvera", "block.minecraft.crimson_fence": "Rodai vjetkatumam", "block.minecraft.crimson_fence_gate": "Rodai vjetkadvera", "block.minecraft.crimson_fungus": "<PERSON><PERSON> manitari", "block.minecraft.crimson_hanging_sign": "Roshdai shan<PERSON>m", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON> faan<PERSON>", "block.minecraft.crimson_planks": "Rodaibaumtel", "block.minecraft.crimson_pressure_plate": "Rodai jalakatasta", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Rod<PERSON> kotumam", "block.minecraft.crimson_slab": "Rodai handado", "block.minecraft.crimson_stairs": "<PERSON><PERSON> leo", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "Rodai j<PERSON>dvera", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON> shankotumam shann tumam", "block.minecraft.crimson_wall_sign": "Rodai kotumam per tumam", "block.minecraft.crying_obsidian": "<PERSON><PERSON> g<PERSON>ishi", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON> kupkat", "block.minecraft.cut_copper_slab": "Tsamena kupkat handado", "block.minecraft.cut_copper_stairs": "<PERSON><PERSON>na k<PERSON>", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON> tsamena <PERSON>", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON> sanishi", "block.minecraft.cut_sandstone_slab": "Handado na tsamena sanishi", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON> flakka", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON> bett", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON> cheri", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON><PERSON> mit tsulus cheri", "block.minecraft.cyan_carpet": "<PERSON><PERSON>us ylpol", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON> vapa<PERSON>na glina", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON> glina", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON> ul", "block.minecraft.damaged_anvil": "Bigorna perpena", "block.minecraft.dandelion": "Kirolule", "block.minecraft.dark_oak_button": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON> d<PERSON>a", "block.minecraft.dark_oak_fence": "Kurairus vjetkatumam", "block.minecraft.dark_oak_fence_gate": "Kurairus vjetkadvera", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON>-d<PERSON>o", "block.minecraft.dark_oak_planks": "Kurairusbaumtel", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.dark_oak_sign": "<PERSON><PERSON><PERSON> kotumam", "block.minecraft.dark_oak_slab": "<PERSON><PERSON><PERSON> handado", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON><PERSON> leo", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> shankotumam shann tumam", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON> kotumam per tumam", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON>-baum", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON> handado", "block.minecraft.dark_prismarine_stairs": "<PERSON> na Kuraiprizmarin", "block.minecraft.daylight_detector": "Anlerabma fu solkirkas", "block.minecraft.dead_brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON>u dado fu hjerne-gup<PERSON>hi", "block.minecraft.dead_brain_coral_fan": "Tsubasa na shinu-hjernegupkishi", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON>u tuma<PERSON>hti fu hjerne-gup<PERSON>hi", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "Shinu dado fu mjah-gupkishi", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON><PERSON> na shinu-mjah<PERSON><PERSON><PERSON>hi", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON>u tuma<PERSON>hti fu Mjah-gup<PERSON>hi", "block.minecraft.dead_bush": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "Shinu dado fu honoo-gupkishi", "block.minecraft.dead_fire_coral_fan": "Tsubasa na shinu-honoogupkishi", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON>u tuma<PERSON>hti honoo-gup<PERSON>hi", "block.minecraft.dead_horn_coral": "Shinu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "Shinu dado fu nor-gupkishi", "block.minecraft.dead_horn_coral_fan": "Tsubasa na shinu-norgupkishi", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON>u tumam<PERSON>hti fu nor-gupkishi", "block.minecraft.dead_tube_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dado", "block.minecraft.dead_tube_coral_fan": "T<PERSON>basa na shinu-gwangup<PERSON>hi", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON>u tuma<PERSON>hti fu gwan-gup<PERSON>hi", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.deepslate": "<PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "<PERSON><PERSON><PERSON>-g<PERSON> handado", "block.minecraft.deepslate_brick_stairs": "Ida<PERSON><PERSON> gdent leo", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON><PERSON>-g<PERSON> tumam", "block.minecraft.deepslate_bricks": "Gdent na idaun le<PERSON>hi", "block.minecraft.deepslate_coal_ore": "Kolmeranja na idaun le<PERSON>hi", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON> z<PERSON>rozran<PERSON>", "block.minecraft.deepslate_gold_ore": "Kelkatranja na idaun le<PERSON>hi", "block.minecraft.deepslate_iron_ore": "Jerkatranja na idaun lehtishi", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON> b<PERSON>-ran<PERSON>", "block.minecraft.deepslate_redstone_ore": "<PERSON><PERSON> redstone ranja", "block.minecraft.deepslate_tile_slab": "Handado na idaunishi prehotel", "block.minecraft.deepslate_tile_stairs": "Leo na idaunishi prehotel", "block.minecraft.deepslate_tile_wall": "Tumam na idaunishi prehotel", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "Uaistia-naruga", "block.minecraft.diamond_block": "<PERSON><PERSON>-dado", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Handado na diorit", "block.minecraft.diorite_stairs": "Dioritle<PERSON>", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON> tumam", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "Ternaruga", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Marekaalidado", "block.minecraft.dripstone_block": "<PERSON><PERSON><PERSON>", "block.minecraft.dropper": "Spaadabaks<PERSON>", "block.minecraft.emerald_block": "Zomorozdado", "block.minecraft.emerald_ore": "Zomorozranja", "block.minecraft.enchanting_table": "Taikastol", "block.minecraft.end_gateway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_portal": "End-d<PERSON><PERSON>i", "block.minecraft.end_portal_frame": "Enddveratel", "block.minecraft.end_rod": "End-vjetka", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "End-ishi g<PERSON>", "block.minecraft.end_stone_brick_stairs": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_wall": "End-ishi g<PERSON>am", "block.minecraft.end_stone_bricks": "End ishi gdent", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> k<PERSON>", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.exposed_copper_bulb": "Djangenanen kupkat kruska", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> kupkat dvera", "block.minecraft.exposed_copper_grate": "Djangenanen kupkat rjet", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>n kupkat jalakadvera", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON><PERSON> tsamena kupkat", "block.minecraft.exposed_cut_copper_slab": "Handado na pojhangena tsamena kupkat", "block.minecraft.exposed_cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.farmland": "<PERSON><PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dado", "block.minecraft.fire_coral_fan": "Hon<PERSON>gu<PERSON><PERSON><PERSON> na tsubasa", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.firefly_bush": "Kirkas<PERSON>ge baumnen", "block.minecraft.fletching_table": "Strelastol", "block.minecraft.flower_pot": "Luleklinje", "block.minecraft.flowering_azalea": "Luleazalea", "block.minecraft.flowering_azalea_leaves": "Lehti fu luleazalija", "block.minecraft.frogspawn": "Eg fu baba", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.furnace": "Honobet", "block.minecraft.gilded_blackstone": "Geltkuroishi", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glasmad<PERSON>", "block.minecraft.glow_lichen": "Kiragira <PERSON>", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "<PERSON><PERSON><PERSON>jer<PERSON>dad<PERSON>", "block.minecraft.gold_ore": "Keltranja", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Handado na granit", "block.minecraft.granite_stairs": "Granitleo", "block.minecraft.granite_wall": "Granitt<PERSON><PERSON>", "block.minecraft.grass": "<PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON>", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON> flakka", "block.minecraft.gray_bed": "<PERSON><PERSON> bett", "block.minecraft.gray_candle": "<PERSON><PERSON> cheri", "block.minecraft.gray_candle_cake": "<PERSON><PERSON>rta mit gris cheri", "block.minecraft.gray_carpet": "<PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON> v<PERSON>na glina", "block.minecraft.gray_shulker_box": "<PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON> glina", "block.minecraft.gray_wool": "<PERSON><PERSON>", "block.minecraft.green_banner": "<PERSON>ori flakka", "block.minecraft.green_bed": "<PERSON><PERSON> bett", "block.minecraft.green_candle": "<PERSON><PERSON> cheri", "block.minecraft.green_candle_cake": "<PERSON><PERSON><PERSON> mit midori cheri", "block.minecraft.green_carpet": "Midori lammaspol", "block.minecraft.green_concrete": "<PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON> h<PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON> vapajena glina", "block.minecraft.green_shulker_box": "<PERSON><PERSON>", "block.minecraft.green_stained_glass": "Midori glas", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON> g<PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON> glina", "block.minecraft.green_wool": "<PERSON><PERSON> ul", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Jalakatasta mit vehtd<PERSON><PERSON>ma", "block.minecraft.honey_block": "Mjaltdado", "block.minecraft.honeycomb_block": "Mjaltrjetdado", "block.minecraft.hopper": "Bidrabma", "block.minecraft.horn_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral_block": "Norgupkishi-dado", "block.minecraft.horn_coral_fan": "Norgupkishi na tsubasa", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON> seresena ishi<PERSON>", "block.minecraft.infested_cobblestone": "<PERSON><PERSON> ishinen", "block.minecraft.infested_cracked_stone_bricks": "Flan perpena ishigdent", "block.minecraft.infested_deepslate": "<PERSON><PERSON> idaun le<PERSON>", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON> mung<PERSON><PERSON> is<PERSON>", "block.minecraft.infested_stone": "<PERSON><PERSON> ishi", "block.minecraft.infested_stone_bricks": "<PERSON>lan ishi gdent", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_door": "Jerkatdvera", "block.minecraft.iron_ore": "Jerkatranja", "block.minecraft.iron_trapdoor": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.jack_o_lantern": "Achorkirkaskran", "block.minecraft.jigsaw": "Falcherdado", "block.minecraft.jukebox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_button": "Pluidasos tastanen", "block.minecraft.jungle_door": "Pluidasos dvera", "block.minecraft.jungle_fence": "Pluidasos vjetkatumam", "block.minecraft.jungle_fence_gate": "Pluidasos vjetkadvera", "block.minecraft.jungle_hanging_sign": "Pluidas<PERSON>", "block.minecraft.jungle_leaves": "Pluidasoslehti", "block.minecraft.jungle_log": "Pluidasos-djido", "block.minecraft.jungle_planks": "Pluidasos-baumtel", "block.minecraft.jungle_pressure_plate": "Pluidasos jalakatasta", "block.minecraft.jungle_sapling": "Pluidasos baumnen", "block.minecraft.jungle_sign": "Pluidasos kotumam", "block.minecraft.jungle_slab": "Pluidasos handado", "block.minecraft.jungle_stairs": "Pluidasos leo", "block.minecraft.jungle_trapdoor": "Pluidasos jalakadvera", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> shankotumam shann tumam", "block.minecraft.jungle_wall_sign": "Pluidasos kotumam per tumam", "block.minecraft.jungle_wood": "Pluidasos-baum", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON> fu <PERSON>", "block.minecraft.ladder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Kirkasklinje", "block.minecraft.lapis_block": "Blaushtofdado", "block.minecraft.lapis_ore": "Blaushtofranja", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON> mura<PERSON>cha petrapie", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON> ha<PERSON><PERSON>i", "block.minecraft.lava": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lava_cauldron": "Tofdai fu zjotishke", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "Librestol", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "Sini flakka", "block.minecraft.light_blue_bed": "<PERSON><PERSON> bett", "block.minecraft.light_blue_candle": "<PERSON>i cheri", "block.minecraft.light_blue_candle_cake": "T<PERSON>rta mit sini cheri", "block.minecraft.light_blue_carpet": "Sini lammaspol", "block.minecraft.light_blue_concrete": "<PERSON><PERSON> h<PERSON>", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON> huo<PERSON>", "block.minecraft.light_blue_glazed_terracotta": "Sini vapajena glina", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON>", "block.minecraft.light_blue_stained_glass": "Sini glas", "block.minecraft.light_blue_stained_glass_pane": "<PERSON><PERSON> g<PERSON>i", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON> glina", "block.minecraft.light_blue_wool": "<PERSON><PERSON> ul", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON> flakka", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON> bett", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON> cheri", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON>rta mit shirogris cheri", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON> lamma<PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON> v<PERSON> glina", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON> glina", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON> ul", "block.minecraft.light_weighted_pressure_plate": "Jalakatasta mit lihtdwaibma", "block.minecraft.lightning_rod": "Zeus-zikha<PERSON>jet<PERSON>", "block.minecraft.lilac": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "Tanilule", "block.minecraft.lily_pad": "Ishkelulepol", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON> flakka", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON> bett", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON> cheri", "block.minecraft.lime_candle_cake": "T<PERSON>rta mit shiromidori cheri", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON> glas", "block.minecraft.lime_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON> glina", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON> ul", "block.minecraft.lodestone": "Plasdado", "block.minecraft.loom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON> flakka", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON> bett", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON> cheri", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON><PERSON> mit romura cheri", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON> ylpol", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON> huo<PERSON>o", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON> v<PERSON> glina", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON> glina", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON> ul", "block.minecraft.magma_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_button": "<PERSON><PERSON><PERSON> na mangr", "block.minecraft.mangrove_door": "Dera na mangr", "block.minecraft.mangrove_fence": "Mangra vjetkatumam", "block.minecraft.mangrove_fence_gate": "Vjetkadvera fu mangr", "block.minecraft.mangrove_hanging_sign": "Shan<PERSON><PERSON><PERSON> na mangra", "block.minecraft.mangrove_leaves": "Lehtimangra", "block.minecraft.mangrove_log": "Mangrdjid<PERSON>", "block.minecraft.mangrove_planks": "Baumtel fu mangra", "block.minecraft.mangrove_pressure_plate": "Tastalasku na mangr", "block.minecraft.mangrove_propagule": "Piedai fu mangr", "block.minecraft.mangrove_roots": "Man<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_sign": "<PERSON>tto na mangr", "block.minecraft.mangrove_slab": "Mangr handado", "block.minecraft.mangrove_stairs": "<PERSON> na mangr", "block.minecraft.mangrove_trapdoor": "Jalakadera na mangr", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON>-shang<PERSON><PERSON> na mangra", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON><PERSON>-chatto na mangr", "block.minecraft.mangrove_wood": "Mangrbaum", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON> m<PERSON> pet<PERSON>", "block.minecraft.melon": "Ishkefraut", "block.minecraft.melon_stem": "Ishkefraut-djido", "block.minecraft.moss_block": "<PERSON><PERSON><PERSON><PERSON><PERSON> dado", "block.minecraft.moss_carpet": "Mungodja pol", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON> razishi", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON>na razishi handado", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> razishi leo", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> razishi tumam", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> ishig<PERSON> handado", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ishi g<PERSON>", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> ishig<PERSON> tumam", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moving_piston": "Ugoki z<PERSON>t", "block.minecraft.mud": "Pantano", "block.minecraft.mud_brick_slab": "Handado na pantanogdent", "block.minecraft.mud_brick_stairs": "Pantano g<PERSON> leo", "block.minecraft.mud_brick_wall": "Pantano g<PERSON> tumam", "block.minecraft.mud_bricks": "Pantanogdent", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON> ranja mangra", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON><PERSON> manitari", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Nethergdent vjetkatumam", "block.minecraft.nether_brick_slab": "Handado na Nether gdent", "block.minecraft.nether_brick_stairs": "Nethergdent leo", "block.minecraft.nether_brick_wall": "Nether gdent tumam", "block.minecraft.nether_bricks": "Nethergdent", "block.minecraft.nether_gold_ore": "Nether-geltranja", "block.minecraft.nether_portal": "Netherdvera", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON>", "block.minecraft.nether_sprouts": "Nethermunnen", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Netherluledado", "block.minecraft.netherite_block": "Nethershtofdado", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Nuotidado", "block.minecraft.oak_button": "<PERSON><PERSON> presmi", "block.minecraft.oak_door": "<PERSON><PERSON> dvera", "block.minecraft.oak_fence": "Rus vjetkatumam", "block.minecraft.oak_fence_gate": "Rus vjetkadvera", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON>", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "Rus-baumtel", "block.minecraft.oak_pressure_plate": "<PERSON><PERSON> j<PERSON>", "block.minecraft.oak_sapling": "<PERSON><PERSON>", "block.minecraft.oak_sign": "<PERSON><PERSON> kotumam", "block.minecraft.oak_slab": "<PERSON><PERSON> handado", "block.minecraft.oak_stairs": "Rus leo", "block.minecraft.oak_trapdoor": "<PERSON><PERSON>", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON> spadakotumam shann tumam", "block.minecraft.oak_wall_sign": "<PERSON><PERSON> kotumam na tumam", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.observer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON> b<PERSON>", "block.minecraft.ominous_banner": "Flakka mitt udacjikokoro", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_banner": "Portokali flakka", "block.minecraft.orange_bed": "Portokali bett", "block.minecraft.orange_candle": "Portokali cheri", "block.minecraft.orange_candle_cake": "T<PERSON>rta mit portokali cheri", "block.minecraft.orange_carpet": "Portokali ylpol", "block.minecraft.orange_concrete": "Portokali huomidado", "block.minecraft.orange_concrete_powder": "Portokali huomishtof", "block.minecraft.orange_glazed_terracotta": "Portokali vapajena glina", "block.minecraft.orange_shulker_box": "Portokali shulker<PERSON>u", "block.minecraft.orange_stained_glass": "Portokali glas", "block.minecraft.orange_stained_glass_pane": "Portokali glaslehti", "block.minecraft.orange_terracotta": "Portokali glina", "block.minecraft.orange_tulip": "Portokali tulipan", "block.minecraft.orange_wool": "Portokali ul", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON><PERSON><PERSON> kupkat kruska", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> kupkat dvera", "block.minecraft.oxidized_copper_grate": "Erdjangena kupkat rjet", "block.minecraft.oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON> kupkat jalakad<PERSON>a", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> kupkat", "block.minecraft.oxidized_cut_copper_slab": "Handado na heljhangena tsamena kupkat", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON>tano", "block.minecraft.pale_hanging_moss": "Shiromungodja shangranja", "block.minecraft.pale_moss_block": "<PERSON><PERSON>o", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON> mungodja pol", "block.minecraft.pale_oak_button": "<PERSON><PERSON><PERSON> ta<PERSON>", "block.minecraft.pale_oak_door": "<PERSON><PERSON><PERSON> d<PERSON>a", "block.minecraft.pale_oak_fence": "Shirorus vjetkatumam", "block.minecraft.pale_oak_fence_gate": "Shirorus vjetkadvera", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_planks": "Shirorusbaumtel", "block.minecraft.pale_oak_pressure_plate": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.pale_oak_sign": "<PERSON><PERSON><PERSON> kotuma<PERSON>", "block.minecraft.pale_oak_slab": "<PERSON><PERSON><PERSON> handado", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON> leo", "block.minecraft.pale_oak_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> shakotumam shann tumam", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON><PERSON> kotumam na tumam", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pearlescent_froglight": "Peitralik baba-kirkas", "block.minecraft.peony": "Pivonia", "block.minecraft.petrified_oak_slab": "Ishiøzena R<PERSON> Poltel", "block.minecraft.piglin_head": "Atama Piglina", "block.minecraft.piglin_wall_head": "Tumamatama fu Pig<PERSON>", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> flakka", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON> bett", "block.minecraft.pink_candle": "<PERSON><PERSON>a cheri", "block.minecraft.pink_candle_cake": "<PERSON><PERSON><PERSON> mit roza cheri", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON> v<PERSON> glina", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> glas", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON> glina", "block.minecraft.pink_tulip": "<PERSON><PERSON>a tulipan", "block.minecraft.pink_wool": "<PERSON><PERSON><PERSON> ul", "block.minecraft.piston": "Zehant", "block.minecraft.piston_head": "Zehantnen", "block.minecraft.pitcher_crop": "Ovashi fu klingjeruti", "block.minecraft.pitcher_plant": "Klingjeruti", "block.minecraft.player_head": "Atama fu spildjin", "block.minecraft.player_head.named": "Atama fu %s", "block.minecraft.player_wall_head": "Tumamatama fu spildjin", "block.minecraft.podzol": "Padzól", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON> fu pluishi", "block.minecraft.polished_andesite": "<PERSON><PERSON>", "block.minecraft.polished_andesite_slab": "Handado na polena andesit", "block.minecraft.polished_andesite_stairs": "<PERSON> fu polena andesit", "block.minecraft.polished_basalt": "Polena basalt", "block.minecraft.polished_blackstone": "<PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Handado na polena kuro<PERSON>-gdent", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON> na polena kuro<PERSON>-gdent", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON> na polena kuro<PERSON>-gdent", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON> na polena kuroishi", "block.minecraft.polished_blackstone_button": "Tasta na polena kuroishi", "block.minecraft.polished_blackstone_pressure_plate": "Jalakatasta na polena kuroishi", "block.minecraft.polished_blackstone_slab": "Handado na polena kuroishi", "block.minecraft.polished_blackstone_stairs": "<PERSON> na polena kuroishi", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON> na polena kuroishi", "block.minecraft.polished_deepslate": "<PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Handado na polena <PERSON>", "block.minecraft.polished_deepslate_stairs": "<PERSON> na polena idaunishi", "block.minecraft.polished_deepslate_wall": "Tu<PERSON>m na polena idaunishi", "block.minecraft.polished_diorite": "Polenadiorit", "block.minecraft.polished_diorite_slab": "Handado na polena diorit", "block.minecraft.polished_diorite_stairs": "Polenadiorit-leo", "block.minecraft.polished_granite": "Polenagranit", "block.minecraft.polished_granite_slab": "Handado na polena granit", "block.minecraft.polished_granite_stairs": "Polenagranit-leo", "block.minecraft.polished_tuff": "<PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "Handado na polena fetemishi", "block.minecraft.polished_tuff_stairs": "<PERSON> fu polena fetemishi", "block.minecraft.polished_tuff_wall": "Tu<PERSON>m na polena fetemishi", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Akakjabaumnen klinjena", "block.minecraft.potted_allium": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_azalea_bush": "Klinjena azalija", "block.minecraft.potted_azure_bluet": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.potted_bamboo": "<PERSON><PERSON><PERSON> klin<PERSON>na", "block.minecraft.potted_birch_sapling": "Volkodjabaumnen klinjena", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_brown_mushroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_cactus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_cherry_sapling": "Sakurabaumnen klinjena", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON> manitari k<PERSON>na", "block.minecraft.potted_crimson_roots": "Rod<PERSON><PERSON><PERSON>", "block.minecraft.potted_dandelion": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_dark_oak_sapling": "Ku<PERSON>rusbaumnen klinjena", "block.minecraft.potted_dead_bush": "Shinurazvjetka klinjena", "block.minecraft.potted_fern": "Hantlehti klinjena", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON><PERSON> lule<PERSON>ja", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON><PERSON>na baumnen fu pluidasos", "block.minecraft.potted_lily_of_the_valley": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.potted_mangrove_propagule": "Piedai fu mangr klinjena", "block.minecraft.potted_oak_sapling": "Rusbaumnen klinjena", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_orange_tulip": "Portokali simpellule klinjena", "block.minecraft.potted_oxeye_daisy": "Gjuumenen klinjena", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON>nen klinjena", "block.minecraft.potted_pink_tulip": "Rózafarje simpellule klinjena", "block.minecraft.potted_poppy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.potted_red_tulip": "Ro simpellule klinjena", "block.minecraft.potted_spruce_sapling": "Julbaumnen klinjena", "block.minecraft.potted_torchflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_warped_fungus": "Erstranitari k<PERSON>", "block.minecraft.potted_warped_roots": "<PERSON><PERSON><PERSON> ranja klin<PERSON>na", "block.minecraft.potted_white_tulip": "<PERSON><PERSON> simpell<PERSON> klinjena", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow": "Alkupash", "block.minecraft.powder_snow_cauldron": "Tofdai fu alk upash", "block.minecraft.powered_rail": "Zejerkatnaruga", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prizmarin gdent handado", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> gdent leo", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON> handado", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON> leo", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON> tumam", "block.minecraft.pumpkin": "Achorfraut", "block.minecraft.pumpkin_stem": "Achorfrautdjido", "block.minecraft.purple_banner": "Muranen flakka", "block.minecraft.purple_bed": "<PERSON><PERSON><PERSON> bett", "block.minecraft.purple_candle": "<PERSON><PERSON><PERSON> cheri", "block.minecraft.purple_candle_cake": "T<PERSON>rta mit muranen cheri", "block.minecraft.purple_carpet": "Muranen lammaspol", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON> huo<PERSON>", "block.minecraft.purple_concrete_powder": "Murane<PERSON> huomis<PERSON>of", "block.minecraft.purple_glazed_terracotta": "Muranen vapajena glina", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Muranen glas", "block.minecraft.purple_stained_glass_pane": "Muranen g<PERSON>i", "block.minecraft.purple_terracotta": "<PERSON>ranen glina", "block.minecraft.purple_wool": "<PERSON><PERSON><PERSON> ul", "block.minecraft.purpur_block": "Purpur dado", "block.minecraft.purpur_pillar": "P<PERSON><PERSON>tumam", "block.minecraft.purpur_slab": "Purpurteldado", "block.minecraft.purpur_stairs": "P<PERSON><PERSON>leo", "block.minecraft.quartz_block": "<PERSON><PERSON> dado", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "<PERSON><PERSON>o", "block.minecraft.quartz_slab": "<PERSON><PERSON>", "block.minecraft.quartz_stairs": "<PERSON><PERSON> leo", "block.minecraft.rail": "Jerkatnaruga", "block.minecraft.raw_copper_block": "<PERSON><PERSON> fu y<PERSON><PERSON><PERSON> kupkat", "block.minecraft.raw_gold_block": "<PERSON><PERSON><PERSON>ova geltkat dado", "block.minecraft.raw_iron_block": "Yttengotova jerkat dado", "block.minecraft.red_banner": "<PERSON><PERSON> flakka", "block.minecraft.red_bed": "<PERSON><PERSON> bett", "block.minecraft.red_candle": "<PERSON>o cheri", "block.minecraft.red_candle_cake": "<PERSON><PERSON>rta mit ro cheri", "block.minecraft.red_carpet": "Ro lammaspol", "block.minecraft.red_concrete": "<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON> h<PERSON><PERSON>of", "block.minecraft.red_glazed_terracotta": "Ro vapajena glina", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON>o manita<PERSON>do", "block.minecraft.red_nether_brick_slab": "Rosh nether gdent handado", "block.minecraft.red_nether_brick_stairs": "Ro nether gdent leo", "block.minecraft.red_nether_brick_wall": "Ro nether gdent tumam", "block.minecraft.red_nether_bricks": "Ro nether gdent", "block.minecraft.red_sand": "<PERSON><PERSON>", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Rosanhantel", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON>", "block.minecraft.red_stained_glass": "Ro glas", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON>i", "block.minecraft.red_terracotta": "<PERSON><PERSON> glina", "block.minecraft.red_tulip": "Ro tulipan", "block.minecraft.red_wool": "<PERSON><PERSON> ul", "block.minecraft.redstone_block": "Redstone dado", "block.minecraft.redstone_lamp": "Redstone kirkas", "block.minecraft.redstone_ore": "Redstone ranja", "block.minecraft.redstone_torch": "Redstone vjetka", "block.minecraft.redstone_wall_torch": "Redstone vjetka na tumam", "block.minecraft.redstone_wire": "Redstone zesen", "block.minecraft.reinforced_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.repeater": "Redstonevent", "block.minecraft.repeating_command_block": "Razgjensuru jewaltdado", "block.minecraft.resin_block": "Mjaltpetra dado", "block.minecraft.resin_brick_slab": "Mjaltpetra gdjent handado", "block.minecraft.resin_brick_stairs": "Mjaltpetra gdjent leo", "block.minecraft.resin_brick_wall": "Mjaltpetra gdjent tumam", "block.minecraft.resin_bricks": "Mjaltpetra gdjent", "block.minecraft.resin_clump": "Mjaltpetramjah", "block.minecraft.respawn_anchor": "Gje<PERSON>intuadado", "block.minecraft.rooted_dirt": "<PERSON><PERSON> mit ranja", "block.minecraft.rose_bush": "R<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "San", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Sanishi handado", "block.minecraft.sandstone_stairs": "San<PERSON>", "block.minecraft.sandstone_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.scaffolding": "Rjetpol", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk errufnedvaibma", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_shrieker": "Sculk shreidvaibma", "block.minecraft.sculk_vein": "Sculk gwannen", "block.minecraft.sea_lantern": "Mare-kirkaskran", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Gjensintuaplas ka<PERSON>na", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON> mun", "block.minecraft.shroomlight": "Manitarikirkas", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Ponedjinatama", "block.minecraft.skeleton_wall_skull": "Ponedjinatama na tumam", "block.minecraft.slime_block": "<PERSON><PERSON><PERSON> dado", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON><PERSON> mura<PERSON> petrapie", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "Stol per jerk<PERSON>go", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Pollik basalt", "block.minecraft.smooth_quartz": "<PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON> kacha handado", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON> kacha leo", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_slab": "Rosanfuwahandado", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON> na ro sanishi", "block.minecraft.smooth_sandstone": "<PERSON><PERSON> sanishi", "block.minecraft.smooth_sandstone_slab": "Handado na polena sanishi", "block.minecraft.smooth_sandstone_stairs": "<PERSON> na polena sanishi", "block.minecraft.smooth_stone": "<PERSON><PERSON> ishi", "block.minecraft.smooth_stone_slab": "Handado na pollik ishi", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow": "Upash", "block.minecraft.snow_block": "Upashdado", "block.minecraft.soul_campfire": "Shalhonobetnen", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "Shalkirkasklinje", "block.minecraft.soul_sand": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "Shalkirkasvjetka", "block.minecraft.soul_wall_torch": "Shalkirkasvjetka na tumam", "block.minecraft.spawn.not_valid": "Har nil huo<PERSON><PERSON>t os nil gjensintua-festena mit zeus, os jam men tumamena", "block.minecraft.spawner": "Mørkø sintuabma", "block.minecraft.spawner.desc1": "Bruk sintuajaitso:", "block.minecraft.spawner.desc2": "<PERSON><PERSON> fal fu von<PERSON>al", "block.minecraft.sponge": "Glubka", "block.minecraft.spore_blossom": "Alkpje lulenen", "block.minecraft.spruce_button": "<PERSON> presmi", "block.minecraft.spruce_door": "Jul dvera", "block.minecraft.spruce_fence": "Jul vjetkatumam", "block.minecraft.spruce_fence_gate": "Jul vjetkadvera", "block.minecraft.spruce_hanging_sign": "<PERSON> s<PERSON>", "block.minecraft.spruce_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_log": "Juldjido", "block.minecraft.spruce_planks": "<PERSON> b<PERSON>", "block.minecraft.spruce_pressure_plate": "Jul jalakatasta", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "<PERSON> kotumam", "block.minecraft.spruce_slab": "Jul handado", "block.minecraft.spruce_stairs": "Jul leo", "block.minecraft.spruce_trapdoor": "<PERSON> jalakad<PERSON>a", "block.minecraft.spruce_wall_hanging_sign": "<PERSON> shan-kotumam tumam-shang", "block.minecraft.spruce_wall_sign": "<PERSON> kotumam na tumam", "block.minecraft.spruce_wood": "<PERSON><PERSON>", "block.minecraft.sticky_piston": "Mjaltlik hantnen", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Ishigdent handado", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON>g<PERSON> leo", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> g<PERSON> tumam", "block.minecraft.stone_bricks": "<PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON>i", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON>", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Utengodja <PERSON>", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_log": "Utengodja volkodjadjido", "block.minecraft.stripped_birch_wood": "Utengod<PERSON>", "block.minecraft.stripped_cherry_log": "Utengodja sakuradjido", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "Utengod<PERSON> k<PERSON>", "block.minecraft.stripped_dark_oak_wood": "Uten<PERSON><PERSON><PERSON> k<PERSON>-baum", "block.minecraft.stripped_jungle_log": "Utengodja pluidasos-djido", "block.minecraft.stripped_jungle_wood": "Utengodja pluidasos-baum", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON><PERSON> man<PERSON>o", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON><PERSON> baum fu mangra", "block.minecraft.stripped_oak_log": "Utengodja rusdjido", "block.minecraft.stripped_oak_wood": "<PERSON>ten<PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "Uten<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_log": "Utengodja juldjido", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_hyphae": "Utengodja erstrani baum", "block.minecraft.stripped_warped_stem": "Utengodja erstrani djido", "block.minecraft.structure_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.structure_void": "Polfal oharedado", "block.minecraft.sugar_cane": "Sokkerid<PERSON><PERSON>", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "Sussan", "block.minecraft.sweet_berry_bush": "Frautnenrazvjetka", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_grass": "Pitkamun", "block.minecraft.tall_seagrass": "Pitkahavmun", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Glina", "block.minecraft.test_block": "Iskatdado", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "Kurai Glas", "block.minecraft.tnt": "Bamdado", "block.minecraft.tnt.disabled": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torch": "Kirkasvjetka", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Kirkasblume ruti", "block.minecraft.trapped_chest": "Uwa<PERSON>ba<PERSON><PERSON>", "block.minecraft.trial_spawner": "Iskatdai Sintuabma", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Spadanoitofesta", "block.minecraft.tube_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>-dado", "block.minecraft.tube_coral_fan": "Gwangup<PERSON><PERSON> na tsubasa", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON>ishi gdent handado", "block.minecraft.tuff_brick_stairs": "<PERSON><PERSON><PERSON> gdent leo", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON> gdent tumam", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON> gdent", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON> handado", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Breshkajaitso", "block.minecraft.twisting_vines": "Raskrungut spadaruti", "block.minecraft.twisting_vines_plant": "Raskrungut spadaruti zhido", "block.minecraft.vault": "Ziharum", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> baba<PERSON>", "block.minecraft.vine": "Spadaruti", "block.minecraft.void_air": "<PERSON><PERSON><PERSON>", "block.minecraft.wall_torch": "Tumamkirkasvjetka", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON> ta<PERSON>en", "block.minecraft.warped_door": "Erstrani dvera", "block.minecraft.warped_fence": "Erstrani vjetkatumam", "block.minecraft.warped_fence_gate": "Erstrani vjetkadvera", "block.minecraft.warped_fungus": "Erstranitari", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON> baumshtof", "block.minecraft.warped_pressure_plate": "Erstrani j<PERSON>katasta", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> kotumam", "block.minecraft.warped_slab": "Erstrani handado", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON> leo", "block.minecraft.warped_stem": "Erstrani djido", "block.minecraft.warped_trapdoor": "Erstrani j<PERSON>dvera", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> shankotumam shann tumam", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> kotumam shann tumam", "block.minecraft.warped_wart_block": "Erstrani luledado", "block.minecraft.water": "<PERSON><PERSON><PERSON>", "block.minecraft.water_cauldron": "Tofdai fu ishke", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_block": "<PERSON><PERSON><PERSON> k<PERSON> dado", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON> k<PERSON> k<PERSON>ka", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON> k<PERSON> d<PERSON>a", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON> k<PERSON> rjet", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON><PERSON> k<PERSON> jala<PERSON>a", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON><PERSON> t<PERSON> kupkat handado", "block.minecraft.waxed_cut_copper_stairs": "<PERSON><PERSON><PERSON> t<PERSON>na kupkat leo", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON> d<PERSON> se<PERSON> k<PERSON>", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> kup<PERSON>", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON> kup<PERSON> k<PERSON>ka", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON> d<PERSON> kup<PERSON> d<PERSON>a", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON> d<PERSON> k<PERSON> rjet", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON><PERSON><PERSON> kup<PERSON> jalaka<PERSON>a", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON><PERSON> poj<PERSON> tsamena kupkat", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON><PERSON> poj<PERSON>ena tsamena kupkat handado", "block.minecraft.waxed_exposed_cut_copper_stairs": "<PERSON><PERSON><PERSON> poj<PERSON>ena tsamena kupkat leo", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON> er<PERSON> se<PERSON> k<PERSON>", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON> er<PERSON> k<PERSON>t k<PERSON>ka", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON> er<PERSON> kup<PERSON> d<PERSON>a", "block.minecraft.waxed_oxidized_copper_grate": "<PERSON><PERSON><PERSON> er<PERSON> k<PERSON> rjet", "block.minecraft.waxed_oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON> kupkat jalaka<PERSON>a", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON> t<PERSON> kup<PERSON>", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON> tsamena kupkat handado", "block.minecraft.waxed_oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON> tsamena kupkat leo", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON> d<PERSON> se<PERSON> k<PERSON>", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON> d<PERSON> kupkat k<PERSON>ka", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON> d<PERSON> kupkat dvera", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON> d<PERSON> kupkat rjet", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON><PERSON> d<PERSON> kupkat jalaka<PERSON>a", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON> er<PERSON> t<PERSON> k<PERSON>", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> kupkat handado", "block.minecraft.waxed_weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON> er<PERSON> tsamena kupkat leo", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> k<PERSON>", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.weathered_copper_bulb": "djangen<PERSON><PERSON> kupkat kruska", "block.minecraft.weathered_copper_door": "<PERSON>jan<PERSON><PERSON>i kupkat dvera", "block.minecraft.weathered_copper_grate": "Djangenadai kupkat rjet", "block.minecraft.weathered_copper_trapdoor": "Djangenadai kupkat jalakad<PERSON>a", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> k<PERSON>", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> tsamena kupkat handado", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> tsamena kupkat leo", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON> spa<PERSON>", "block.minecraft.wet_sponge": "Mokri glub<PERSON>", "block.minecraft.wheat": "<PERSON><PERSON>", "block.minecraft.white_banner": "<PERSON><PERSON> flakka", "block.minecraft.white_bed": "<PERSON><PERSON> bett", "block.minecraft.white_candle": "<PERSON><PERSON> cheri", "block.minecraft.white_candle_cake": "<PERSON><PERSON><PERSON> mit shiro cheri", "block.minecraft.white_carpet": "<PERSON><PERSON> la<PERSON>ol", "block.minecraft.white_concrete": "<PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON> v<PERSON> g<PERSON>", "block.minecraft.white_shulker_box": "<PERSON><PERSON>", "block.minecraft.white_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.white_terracotta": "<PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON> tulipan", "block.minecraft.white_wool": "<PERSON><PERSON>", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Witherponed<PERSON> atama", "block.minecraft.wither_skeleton_wall_skull": "Witherponedjin atama na tumam", "block.minecraft.yellow_banner": "<PERSON><PERSON> flakka", "block.minecraft.yellow_bed": "<PERSON><PERSON> bett", "block.minecraft.yellow_candle": "<PERSON><PERSON> cheri", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON><PERSON> mit kiiro cheri", "block.minecraft.yellow_carpet": "<PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON> v<PERSON>", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON> glas", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.yellow_terracotta": "<PERSON><PERSON>", "block.minecraft.yellow_wool": "<PERSON><PERSON>", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON> atama na tumam", "book.byAuthor": "grun %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "<PERSON><PERSON> libranamae her:", "book.finalizeButton": "<PERSON>si namae au owari lesa", "book.finalizeWarning": "Da tjiru! Tiid kara ka ende nasi namae libre made, nil deki kawari vimi.", "book.generation.0": "<PERSON><PERSON><PERSON><PERSON>", "book.generation.1": "Furiversio", "book.generation.2": "Fu<PERSON>fu<PERSON><PERSON>", "book.generation.3": "Tak furi", "book.invalid.tag": "* Dekinai namaenen fu libre *", "book.pageIndicator": "Lehti %1$ss fu %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON>", "book.page_button.previous": "<PERSON><PERSON><PERSON>", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "<PERSON><PERSON>", "book.signButton": "<PERSON><PERSON><PERSON>", "book.view.title": "Book View Screen", "build.tooHigh": "Pitkasma ele per nasidado %s", "chat.cannotSend": "<PERSON><PERSON><PERSON>", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Kliq per pal", "chat.copy": "Zehuskenen made", "chat.copy.click": "Ror per mah zehuskenen made", "chat.deleted_marker": "Afto poshta keshtena na server-tera.", "chat.disabled.chain_broken": "Perpa rofaj de chatto de<PERSON>. Tsa gjentsunaga.", "chat.disabled.expiredProfileKey": "<PERSON>tto kiniena grun gammel blogeta zekluch. Bitte iskat gjentsunaga.", "chat.disabled.invalid_command_signature": "<PERSON> jewaldko vilena innekaku borte, os jam dan kn<PERSON>ena tuo.", "chat.disabled.invalid_signature": "Unna<PERSON>ku na chatto uwaki. Bite ti iskat bengt<PERSON><PERSON>.", "chat.disabled.launcher": "Had<PERSON><PERSON><PERSON> an<PERSON> chatto. Pochta antade<PERSON>.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> kinijena grun jamnai blogeta zekluch. Bitte iskat gjentsunaga.", "chat.disabled.options": "<PERSON>tto kiini na sentakunara.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> sa<PERSON>. Toki na kompuj kawari we?", "chat.disabled.profile": "<PERSON><PERSON> de<PERSON> grun sentakuna fu zefuga. Tasta ti '%s' plustid per lera plus.", "chat.disabled.profile.moreInfo": "Na sentakuna fu zefuga, chatto la<PERSON>. Anta au lesa pochta de<PERSON>ai.", "chat.editBox": "kaku", "chat.filtered": "Keshitena grun server.", "chat.filtered_full": "Server furi poshta fu du per joku spildjin.", "chat.link.confirm": "Z<PERSON>i vil bides zedvera?", "chat.link.confirmTrusted": "Auki afto zedvera os nasii zehuskenen made?", "chat.link.open": "<PERSON><PERSON> z<PERSON>et made", "chat.link.warning": "Na nil slucja da auki zedvera da du fsjtonai grun!", "chat.queue": "[+%s pochta ka vent]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server antaa dan dwai<PERSON><PERSON>-poch<PERSON>.", "chat.tag.modified": "<PERSON><PERSON><PERSON> kawarena na server. <PERSON><PERSON>:", "chat.tag.not_secure": "Poshta nai pravdajena. Deki nai anopeta.", "chat.tag.system": "Server-poshta. <PERSON><PERSON> an<PERSON>.", "chat.tag.system_single_player": "Kakuna fu server.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ying kriig %s", "chat.type.advancement.goal": "%s jing dan %s", "chat.type.advancement.task": "%s da ying dan %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON> made", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s hanu %s", "chat.validation_error": "Pochta humba dan prav<PERSON>", "chat_screen.message": "Antaa-vil<PERSON>na pochta: %s", "chat_screen.title": "Cha<PERSON>leht<PERSON>", "chat_screen.usage": "Nasi pochta de tasta Enter per antaa", "chunk.toast.checkLog": "<PERSON>sa sluchatumam per plus", "chunk.toast.loadFailure": "Humba lesa velttelnen long %s", "chunk.toast.lowDiskSpace": "<PERSON><PERSON><PERSON><PERSON><PERSON> akote sat!", "chunk.toast.lowDiskSpace.description": "Tabun dekinai ufne velt.", "chunk.toast.saveFailure": "Humba ufne velttelnen long %s", "clear.failed.multiple": "Nil ting finna mit %s spildjin", "clear.failed.single": "Nil ting finna mit spildjin: %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON>", "color.minecraft.light_blue": "<PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON>", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.magenta": "<PERSON><PERSON><PERSON>", "color.minecraft.orange": "Portokali", "color.minecraft.pink": "<PERSON><PERSON><PERSON>", "color.minecraft.purple": "Muranen", "color.minecraft.red": "Ro", "color.minecraft.white": "<PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<--[HER]", "command.context.parse_error": "%s na %s: %s", "command.exception": "Fshto dan nai jewaltfraz: %s", "command.expected.separator": "<PERSON><PERSON><PERSON> ka avarata<PERSON> owari impla, men jamdan plus kirain", "command.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> fudu natun er<PERSON>ai", "command.forkLimit": "<PERSON><PERSON> na mangeele fu longzma (%s)", "command.unknown.argument": "Uwaki impla na jewaltfraz", "command.unknown.command": "Uwaki os knhel jewaltfraz", "commands.advancement.criterionNotFound": "%1$s iskatna trængnai '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "<PERSON><PERSON><PERSON> nasii ligrunn '%s' surjena %s made %s spildjin made grunn ende her", "commands.advancement.grant.criterion.to.many.success": "Nasiidan ligrunn '%s' surjena %s made %s spildjin made", "commands.advancement.grant.criterion.to.one.failure": "<PERSON><PERSON>dan nasii ligrunn '%s' surjena %s made spildjin %s made grunn ende her", "commands.advancement.grant.criterion.to.one.success": "Nasiidan ligrunn '%s' surjena %s made spildjin %s made", "commands.advancement.grant.many.to.many.failure": "Humbadan antaa surjena %s %s spildjin made grun ende har", "commands.advancement.grant.many.to.many.success": "Nasiidan %s surjena %s spildjin made", "commands.advancement.grant.many.to.one.failure": "Humbadan antaa %s surjena %s made grun ende har", "commands.advancement.grant.many.to.one.success": "Nasiidan %s surjena %s made", "commands.advancement.grant.one.to.many.failure": "Humbadan antaa surjena %s %s spildjin made grun ende har", "commands.advancement.grant.one.to.many.success": "Nasiidan surjena %s %s spildjin made", "commands.advancement.grant.one.to.one.failure": "Humbadan antaa surjena %s %s made grun ende har", "commands.advancement.grant.one.to.one.success": "Nasiidan surjena %s spildjin %s made", "commands.advancement.revoke.criterion.to.many.failure": "Humbadan saada ligrunn '%s' surjena %s made %s spildjin made grunn ende harnai", "commands.advancement.revoke.criterion.to.many.success": "Ersada ruru '%s' fu jingsurjena %s %sdjin kara", "commands.advancement.revoke.criterion.to.one.failure": "Humbadan saada ligrunn '%s' surjena %s made %s made grunn ende harnai", "commands.advancement.revoke.criterion.to.one.success": "Ersada ruru '%s' fu jingsurjena %s %s kara", "commands.advancement.revoke.many.to.many.failure": "Humbadan saada %s surjena %s spildjin kara grunn ende harnai", "commands.advancement.revoke.many.to.many.success": "Ersada %s jingsurjena %sdjin kara", "commands.advancement.revoke.many.to.one.failure": "Humbadan saada %s surjena %s kara grunn ende harnai", "commands.advancement.revoke.many.to.one.success": "Ersada %s jingsurjena %s kara", "commands.advancement.revoke.one.to.many.failure": "Humbadan saada surjena %s %s spildjin kara grunn ende harnai", "commands.advancement.revoke.one.to.many.success": "Ersada jingsurjena %s %sdjin kara", "commands.advancement.revoke.one.to.one.failure": "Hu<PERSON>dan saada surjena %s %s kara grunn ende harnai", "commands.advancement.revoke.one.to.one.success": "Ersada jingsurjena %s %s kara", "commands.attribute.base_value.get.success": "Grunatai fu impla %s fu shal %s - %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Grunatai fu impla %s fu shal %s ende %s", "commands.attribute.failed.entity": "%s uso shalfal per afto surjena", "commands.attribute.failed.modifier_already_present": "<PERSON><PERSON><PERSON> %s ende na impla %s fu shal %s", "commands.attribute.failed.no_attribute": "Shal %s harnai impla %s", "commands.attribute.failed.no_modifier": "Impla %s per shal %s yam uten ikawar<PERSON> %s", "commands.attribute.modifier.add.success": "Nasiidan ka<PERSON>na %s impla %s fu shal %s made", "commands.attribute.modifier.remove.success": "<PERSON><PERSON><PERSON> %s impla %s fu shal %s kara", "commands.attribute.modifier.value.get.success": "<PERSON><PERSON> %s na impla %s fu shal %s je %s", "commands.attribute.value.get.success": "Impla %s fu shal %s - %s", "commands.ban.failed": "<PERSON><PERSON>, tuo spildjin ende banena", "commands.ban.success": "Erkeshite dan %s: %s", "commands.banip.failed": "<PERSON><PERSON>, tuo zeplas ende banena", "commands.banip.info": "Banena %s speldjin: %s", "commands.banip.invalid": "Uwaki zeplas os spild<PERSON>", "commands.banip.success": "Erkeshite dan zeplas (IP) %s: %s", "commands.banlist.entry": "%s banena na %s: %s", "commands.banlist.entry.unknown": "(knshirena)", "commands.banlist.list": "Jam %s ban:", "commands.banlist.none": "Jam nil er<PERSON><PERSON><PERSON><PERSON>", "commands.bossbar.create.failed": "Vonataisen mitt namae %s jam ende", "commands.bossbar.create.success": "<PERSON><PERSON><PERSON> %s", "commands.bossbar.get.max": "Vonataisen %s har eleatai: %s", "commands.bossbar.get.players.none": "Vonataisen %s ima har nil spildjin narjet", "commands.bossbar.get.players.some": "Vonataisen %s har %s spildjin ima narjet: %s", "commands.bossbar.get.value": "Vonataisen %s har atai: %s", "commands.bossbar.get.visible.hidden": "Vonataisen %s ima furijena", "commands.bossbar.get.visible.visible": "Vonataisen %s ima blogeta", "commands.bossbar.list.bars.none": "Jam nil von<PERSON>isen ende", "commands.bossbar.list.bars.some": "Jam %s vonataisen brukena: %s", "commands.bossbar.remove.success": "Keshite dan vonataisen %s", "commands.bossbar.set.color.success": "Faria fu vonataisen %s kawari dan", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON>, faria fu tuo vonataisen ende tak", "commands.bossbar.set.max.success": "Vonataisen %s har neo eleatai: %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON>, el<PERSON><PERSON> fu tuo vonataisen ende tak", "commands.bossbar.set.name.success": "Vonataisen %s har neo namae", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON>, namae fu tuo vonataisen ende tak", "commands.bossbar.set.players.success.none": "Vonataisen %s ende har nil spildjin", "commands.bossbar.set.players.success.some": "Vonataisen %s ende har %s spildjin: %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON>, tuo spildjin ende na <PERSON>, nil jam per nasii os keshite", "commands.bossbar.set.style.success": "Vonataisen %s har neo setropos", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON>, setropos fu tuo vonataisen ende tak", "commands.bossbar.set.value.success": "Vonataisen %s har neo atai, ima %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON>, atai fu tuo vonataisen ende tak", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON>, tuo von<PERSON>isen ende furijena", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON>, tuo von<PERSON>isen ende ma<PERSON>", "commands.bossbar.set.visible.success.hidden": "Vonataisen %s imakara furi", "commands.bossbar.set.visible.success.visible": "Vonataisen %s imakara bloge", "commands.bossbar.unknown": "<PERSON><PERSON>n mitt laskunen '%s'", "commands.clear.success.multiple": "Keshite dan %s ting %s speldjin kara", "commands.clear.success.single": "Keshite dan %s ting %s (speldjin) kara", "commands.clear.test.multiple": "Fynnena %s lík ting na %s speldjin", "commands.clear.test.single": "Fynnena %s lík ting na speldjin %s", "commands.clone.failed": "Nil dado plustidena", "commands.clone.overlap": "Hadjiplas au owariplas lakinai har sama tel", "commands.clone.success": "Gjenmahena %s dado najing", "commands.clone.toobig": "Tak mange velttelnen ine tuo niabad (na ele %s, men deer %s)", "commands.damage.invulnerable": "Jenadjin arka nil grun harz<PERSON>al", "commands.damage.success": "<PERSON><PERSON>a dan mit %s %s", "commands.data.block.get": "%s na dado %s, %s, %s, li atairaz sama %s, bli %s", "commands.data.block.invalid": "<PERSON><PERSON><PERSON> dado jenai dadoshal", "commands.data.block.modified": "Reforma impla fu dado na %s, %s, %s", "commands.data.block.query": "%s, %s, %s har baksu shirena: %s", "commands.data.entity.get": "%s per %s mit atairaz %s je %s", "commands.data.entity.invalid": "<PERSON><PERSON><PERSON> kawari impla fu spildjin", "commands.data.entity.modified": "<PERSON><PERSON> dan impla fu shal na %s", "commands.data.entity.query": "%s har impla na: %s", "commands.data.get.invalid": "Dekinai sada %s; mono namaenen na lasku deki", "commands.data.get.multiple": "Afto baksu ainlat ein NBT-impla", "commands.data.get.unknown": "<PERSON><PERSON><PERSON> sada %s; na<PERSON><PERSON><PERSON> jamnai", "commands.data.merge.failed": "<PERSON><PERSON> ka<PERSON>. <PERSON><PERSON>na impla ende tak", "commands.data.modify.expected_list": "<PERSON><PERSON><PERSON> tuma<PERSON>, a saadena: %s", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON>, a saadena: %s", "commands.data.modify.expected_value": "<PERSON>il dan impla, saada dan: %s", "commands.data.modify.invalid_index": "Uwaki tumamlasku %s", "commands.data.modify.invalid_substring": "Warui ergolasku fu zekotel: %s kara %s made", "commands.data.storage.get": "%s na ufneplas %s za atairaz %s bli %s", "commands.data.storage.modified": "<PERSON><PERSON> dan ufneplas %s", "commands.data.storage.query": "Ufneplas %s har afto inne: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "<PERSON><PERSON> '%s' nai brukena!", "commands.datapack.disable.failed.feature": "<PERSON><PERSON> '%s' la<PERSON><PERSON> k<PERSON>, grun tel fu asa flakkanen!", "commands.datapack.enable.failed": "<PERSON><PERSON> '%s' ende brukena!", "commands.datapack.enable.failed.no_flags": "<PERSON><PERSON> '%s' dekinai brukena, za-grun trengen flakkanen lakinai na velt: %s!", "commands.datapack.list.available.none": "Jam nil plus implakaban ka dekiti bli brukena", "commands.datapack.list.available.success": "Jam %s imp<PERSON><PERSON>n ka dekiti bruk: %s", "commands.datapack.list.enabled.none": "Jam nil implakaban ka ende brukena", "commands.datapack.list.enabled.success": "Jam %s implakaban ka ende brukena: %s", "commands.datapack.modify.disable": "Yamete bruk implakaban %s", "commands.datapack.modify.enable": "Hadjibruk implakaban %s", "commands.datapack.unknown": "<PERSON><PERSON><PERSON> '%s'", "commands.debug.alreadyRunning": "Tidleo-rømse ende ergo", "commands.debug.function.noRecursion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mellan dwaibmaruga", "commands.debug.function.noReturnRun": "Virtakaku dwaibmanai na suryk-virta", "commands.debug.function.success.multiple": "Virtakaku %s jewaldko %s jewaldko-klaani kara na zeting made %s", "commands.debug.function.success.single": "Virtakaku %s jewaldko jewaldko-klaani kara '%s', ufnena na zeting %s", "commands.debug.function.traceFailed": "<PERSON>mba dan shuchu na dwaibmaruga", "commands.debug.notRunning": "Rømsebma fu tidleo ende hadjinai", "commands.debug.started": "<PERSON><PERSON> dan rø<PERSON> fu tidleo", "commands.debug.stopped": "Yametedan tidleo-rømsebma za %s sho au %s tokenen (%s tokenen per sho)", "commands.defaultgamemode.success": "<PERSON><PERSON><PERSON> hasteatai ima bli %s", "commands.deop.failed": "<PERSON><PERSON>, spild<PERSON> nai jewal<PERSON><PERSON>", "commands.deop.success": "%s ima nai ye<PERSON>jin", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Hasteatai ende %s, de <PERSON><PERSON><PERSON>", "commands.difficulty.query": "Hasteatai: %s", "commands.difficulty.success": "Hasteatai bli dan %s", "commands.drop.no_held_items": "Afto S<PERSON> dekinai har ting", "commands.drop.no_loot_table": "Shal %s harnai vrassada<PERSON>am", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": "Spaada %s ting", "commands.drop.success.multiple_with_table": "Spaada %s ting gavatstol %s kara", "commands.drop.success.single": "Spadena %s %s", "commands.drop.success.single_with_table": "Spaada %s %s gavatstol %s kara", "commands.effect.clear.everything.failed": "Jen<PERSON>jin har nil taikena per keshite", "commands.effect.clear.everything.success.multiple": "Keshite al taikena %s shal kara", "commands.effect.clear.everything.success.single": "Keshite al taikena %s kara", "commands.effect.clear.specific.failed": "<PERSON><PERSON><PERSON> nai har vilena taikena", "commands.effect.clear.specific.success.multiple": "Keshite taikena %s %s shal kara", "commands.effect.clear.specific.success.single": "Keshite taikena %s %s kara", "commands.effect.give.failed": "<PERSON><PERSON><PERSON> antaa tuo taikena (shal de<PERSON>ai taikena os ende taikena joku plus djong)", "commands.effect.give.success.multiple": "Antaa taikena %s %sdjin made", "commands.effect.give.success.single": "Antaa taikena %s %s made", "commands.enchant.failed": "<PERSON><PERSON>. Na sentakenadjin jam nil na hant, os taikena de<PERSON>ai", "commands.enchant.failed.entity": "%s uso shalfal per afto jewaltko", "commands.enchant.failed.incompatible": "%s dekinai bruk afto taika", "commands.enchant.failed.itemless": "%s nai har ting inne hantnen", "commands.enchant.failed.level": "%s plus ka <PERSON>temange atai, sama %s, lakjena per taika", "commands.enchant.success.multiple": "Antaa taika %s %s shal made", "commands.enchant.success.single": "Antaa taika %s ting fu %s made", "commands.execute.blocks.toobig": "<PERSON>nge velttelnen ine tuo niabad (mus na ele %s, men deer %s)", "commands.execute.conditional.fail": "<PERSON><PERSON><PERSON> hum<PERSON>", "commands.execute.conditional.fail_count": "Iskatna humba %ssraz", "commands.execute.conditional.pass": "<PERSON><PERSON><PERSON> jing", "commands.execute.conditional.pass_count": "Iskatna jing: %s raz", "commands.execute.function.instantiationFailure": "Humba dan had<PERSON><PERSON>ze dwaibmaruga %s: %s", "commands.experience.add.levels.success.multiple": "Antaa %s mestarileo %s spildjin made", "commands.experience.add.levels.success.single": "Antaa %s mestarileo %s made", "commands.experience.add.points.success.multiple": "Antaa %s mestaripik %s spildjin made", "commands.experience.add.points.success.single": "Aantaa %s mestaripik %s made", "commands.experience.query.levels": "%s har %s mestarileo", "commands.experience.query.points": "%s har %s mestaripik", "commands.experience.set.levels.success.multiple": "Sentaku %s mestarileo na %s spildjin", "commands.experience.set.levels.success.single": "Sentaku %s mestarileo na %s", "commands.experience.set.points.invalid": "Mestariatainen nai deki kawarena ova lestemange atai per mestariatai fu spildjin", "commands.experience.set.points.success.multiple": "Sentaku %s mestaripik na %s spildjin", "commands.experience.set.points.success.single": "Sentaku %s mestaripik na %s", "commands.fill.failed": "Nil dado pulap", "commands.fill.success": "<PERSON> pulap %s dado", "commands.fill.toobig": "<PERSON>nge dado na tuo niabad (mus na ele %s, men der %s)", "commands.fillbiome.success": "Lantfal jewaldena mellan %s, %s, %s au %s, %s, %s", "commands.fillbiome.success.count": "%s lantfal-raz mellan %s, %s, %s au %s, %s, %s jewaldena", "commands.fillbiome.toobig": "Obamange dado na sentakena ohare (ele %s, sentakena %s)", "commands.forceload.added.failure": "<PERSON><PERSON> velttelnen sentakena per bai lesa", "commands.forceload.added.multiple": "%s velttelnen inne %s %s kara %s joubjena per balesjena", "commands.forceload.added.none": "Velttelnen per mirai balesa nai fynnjena ine %s", "commands.forceload.added.single": "%ss velttelnen inne %s joubjena per balesa", "commands.forceload.list.multiple": "%s tel warukini dan finna inne %s na: %s", "commands.forceload.list.single": "Balesjena velttelnen fynna inne %s na: %s", "commands.forceload.query.failure": "Velttelnen na %s ine %s mahena ka mirai balesa nai", "commands.forceload.query.success": "Velttelnen %s na %s mahena ka mirai balesa", "commands.forceload.removed.all": "Mah ka al velttelnen ine %s mirai balesajenanai", "commands.forceload.removed.failure": "<PERSON>l velttelnen keshite balesa-tumam kara", "commands.forceload.removed.multiple": "Mah ka %s velltelnen na %s %s kara %s made mirai balesajena", "commands.forceload.removed.single": "Mah ka velttelnen %s ine %s nai balesena", "commands.forceload.toobig": "Tak mange velttelnen ine tuo niabad (na ele %s, men deer %s)", "commands.function.error.argument_not_compound": "Knparjadena inne-tel fal: %s, vil dan <PERSON>", "commands.function.error.missing_argument": "Inne-tel %2$s per %1$s dwaibmaruga milu", "commands.function.error.missing_arguments": "Inne-tel per %s dwaibmaruga milu", "commands.function.error.parse": "Na hadji fu jewaldvirta %s: jewald<PERSON> '%s' humbayze dan: '%s'", "commands.function.instantiationFailure": "Humba dan had<PERSON><PERSON>ze dwaibmaruga %s: %s", "commands.function.result": "Dwaibmaruga %s svar dan %s", "commands.function.scheduled.multiple": "Dwaibma ima %s dwaibmaruga", "commands.function.scheduled.no_functions": "Fynakkinai dwaibmaruga na namae %s", "commands.function.scheduled.single": "<PERSON><PERSON> dwaibma %s", "commands.function.success.multiple": "Jing dan %s dwai<PERSON><PERSON><PERSON> %s kara", "commands.function.success.multiple.result": "Jing dan %s dwaibmaruga", "commands.function.success.single": "Jing dan %s dwai<PERSON><PERSON><PERSON> '%s' kara", "commands.function.success.single.result": "Dwaibmaruga '%2$s' svar %1$s", "commands.gamemode.success.other": "Spiltropos fu %s kawarjena %s made", "commands.gamemode.success.self": "Mah sebja na %s", "commands.gamerule.query": "Spilruuru %s har imi na %s", "commands.gamerule.set": "Spilruuru %s ima har imi na %s", "commands.give.failed.toomanyitems": "Nai deki anta plus na %s %s", "commands.give.success.multiple": "Antaa %s %s %s spildjin made", "commands.give.success.single": "Antaa %s %s %s made", "commands.help.failed": "<PERSON><PERSON><PERSON> os la<PERSON>i", "commands.item.block.set.success": "Dlaudel dan oharenen na %s, %s, %s per %s", "commands.item.entity.set.success.multiple": "Dlaudel dan oharenen na %s shal per %s", "commands.item.entity.set.success.single": "Dlaudel dan oharenen na %s per %s", "commands.item.source.no_such_slot": "Ranja harnai oh<PERSON>nen %s", "commands.item.source.not_a_container": "Plas %s, %s, %s nai kaban", "commands.item.target.no_changed.known_item": "Nil sentakena lakidan %s tera %s-oharenen made", "commands.item.target.no_changes": "Nil sentakena lakiena na %s made", "commands.item.target.no_such_slot": "Sentakena har nil %s perplas", "commands.item.target.not_a_container": "Sentakena plaslasku %s, %s, %s nai kaban", "commands.jfr.dump.failed": "Humba ufne JFR: %s", "commands.jfr.start.failed": "<PERSON><PERSON> dan hadji J<PERSON> an<PERSON>", "commands.jfr.started": "JFR an<PERSON> hadji", "commands.jfr.stopped": "JFR ji<PERSON>, ufne na %s", "commands.kick.owner.failed": "Keshitekinai atamadjin fu server na LANspil", "commands.kick.singleplayer.failed": "Keshite dekinai na utenrjet tolkaspil", "commands.kick.success": "Mahashkekso %s: %s", "commands.kill.success.multiple": "Ende vras %s sjal", "commands.kill.success.single": "Vras dan %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Yam narjet %s spildjin una ele %s: %s", "commands.locate.biome.not_found": "Nai fynna lant<PERSON>l \"%s\" na lagom prapatai", "commands.locate.biome.success": "Letste bara %s lonn %s (%s dado prapa)", "commands.locate.poi.not_found": "Nai fynna dan kjomiplas fu fal \"%s\" na lagom prapatai", "commands.locate.poi.success": "Letste bara %s lonn %s (%s dado prapa)", "commands.locate.structure.invalid": "Jam nil polfal na \"%s\"", "commands.locate.structure.not_found": "Dekinai fynna polfal \"%s\" na para", "commands.locate.structure.success": "Letste bara %s lonn %s (%s dado prapa)", "commands.message.display.incoming": "%s chicheu du made: %s", "commands.message.display.outgoing": "Du chicheu %s made: %s", "commands.op.failed": "<PERSON><PERSON>, tuo spildjin ende jewaltdjin", "commands.op.success": "Ma<PERSON>an %s <PERSON><PERSON><PERSON>", "commands.pardon.failed": "<PERSON><PERSON>, tuo spildjin nai banena", "commands.pardon.success": "Bengainlat %s", "commands.pardonip.failed": "<PERSON><PERSON>, tuo zeplas nai banena", "commands.pardonip.invalid": "<PERSON><PERSON><PERSON> zeplas", "commands.pardonip.success": "Bengainlat zeplas %s", "commands.particle.failed": "Telnen nai sejena fu nildjin", "commands.particle.success": "Mahasejena raztelnen %s", "commands.perf.alreadyRunning": "Bystrazma ende ergo", "commands.perf.notRunning": "Bystrazma naj ende ergo", "commands.perf.reportFailed": "Hu<PERSON>dan maha anopeta na zefiks", "commands.perf.reportSaved": "Zefiks anopeta mahena na %s", "commands.perf.started": "Hadji 10-sho virta-farza (bruk '/perf stop' per jamete)", "commands.perf.stopped": "Jamete dan virtafarza pu %s sho mit %s tidleo (%s tidleo na sho)", "commands.place.feature.failed": "Humba nasii impla", "commands.place.feature.invalid": "Jam nil zdanietel na fal \"%s\"", "commands.place.feature.success": "Nasena \"%s\" lonn %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON> anmaha hammass<PERSON>l", "commands.place.jigsaw.invalid": "Jam nil polfalikaban mit fal \"%s\"", "commands.place.jigsaw.success": "Sintwena rombe lonn %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON> dan pomaha <PERSON>", "commands.place.structure.invalid": "Jam nil zdanie na fal \"%s\"", "commands.place.structure.success": "Posintua dan vove \"%s\" na %s, %s, %s", "commands.place.template.failed": "<PERSON>mba dan r<PERSON>ha polfal kara", "commands.place.template.invalid": "Jam nai polfal na ID \"%s\"", "commands.place.template.success": "Polesa dan polfal \"%s\" na %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON> oba<PERSON>pa per <PERSON>ø<PERSON>", "commands.playsound.success.multiple": "Antaa zan %s %s spildjin made", "commands.playsound.success.single": "Antaa zan %s %s spildjin made", "commands.publish.alreadyPublished": "Mitriospil ende auki %s plas kara", "commands.publish.failed": "<PERSON><PERSON><PERSON> auki spil pashuun para made", "commands.publish.started": "LAN-spil ima dwaibma na zekuchinen %s", "commands.publish.success": "Mitriospil ima auki %s plas kara", "commands.random.error.range_too_large": "Letstestor pitkatai fu zarilasku tte 2147483646", "commands.random.error.range_too_small": "Letstechisai pitkatai fu zarilasku tte 2", "commands.random.reset.all.success": "Pogjen dan %s zariparjad", "commands.random.reset.success": "Pogjen zari parjad %s", "commands.random.roll": "%s zarisu dan %s (%s kara %s made)", "commands.random.sample.success": "<PERSON><PERSON><PERSON>: %s", "commands.recipe.give.failed": "<PERSON>e lerajena nil mahat<PERSON>", "commands.recipe.give.success.multiple": "Mahadekise fu %s mahtropos %s spildjin made", "commands.recipe.give.success.single": "Blogeta ima %s mahtropos per %s", "commands.recipe.take.failed": "<PERSON>e vasujena nil ma<PERSON>", "commands.recipe.take.success.multiple": "Keshite %s mahatropos per %s spildjin", "commands.recipe.take.success.single": "Keshite %s mahatropos per %s", "commands.reload.failure": "Humbadan gjenlesa - tatsu bruk gammel shirena", "commands.reload.success": "<PERSON><PERSON><PERSON><PERSON>a ima!", "commands.ride.already_riding": "%s ende ajaa %s", "commands.ride.dismount.success": "%s owari ajaa %s", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "commands.ride.mount.failure.generic": "%s humba dan ajaa %s", "commands.ride.mount.failure.loop": "S<PERSON> dekinai suwarøzena na sebja os ajaadjin fusebja", "commands.ride.mount.failure.wrong_dimension": "Dekinai posuwaru na shal na chigau velt", "commands.ride.mount.success": "%s hadji dan ajaa %s", "commands.ride.not_riding": "%s nai ajaa nil", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Ufne ende nai sentakena", "commands.save.alreadyOn": "Ufne ende sentakena", "commands.save.disabled": "Vaufnele ima kiini", "commands.save.enabled": "Vaufnele ima auki", "commands.save.failed": "Humbadan ufne spil (jam lagom ohare na ufnekrais?)", "commands.save.saving": "<PERSON><PERSON>ne spil ima (tab treng mamjént!)", "commands.save.success": "Spil ufnena", "commands.schedule.cleared.failure": "Nil slucharyet mit laskunen %s", "commands.schedule.cleared.success": "Keshitedan %s slucjakarta na id %s", "commands.schedule.created.function": "Kartakaku dan jewaldko-klaani '%s' bi %s tidleo na spiltid %s", "commands.schedule.created.tag": "Surupie namaenen '%s' na %s-mirai tidleo na spiltiid %s", "commands.schedule.macro": "<PERSON><PERSON><PERSON> surupie jeval<PERSON>", "commands.schedule.same_tick": "<PERSON><PERSON><PERSON> ma<PERSON> per shonen ka ima", "commands.scoreboard.objectives.add.duplicate": "Ende jam muspie", "commands.scoreboard.objectives.add.success": "Neo muspie %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON>, e<PERSON>n-oharenen ende ohare", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON>, ma<PERSON><PERSON><PERSON> ende har afto muspie", "commands.scoreboard.objectives.display.cleared": "Muspie keshtena sejenaplasnen %s kara", "commands.scoreboard.objectives.display.set": "<PERSON><PERSON> se<PERSON> %s mahase surjena %s", "commands.scoreboard.objectives.list.empty": "Jam nil muspie", "commands.scoreboard.objectives.list.success": "Jam %s tuvatpie: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "<PERSON><PERSON><PERSON> dan vaneole per vilenadai %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "<PERSON><PERSON><PERSON> dan vakawarile per vilena %s", "commands.scoreboard.objectives.modify.displayname": "Senanamae fu muspie %s kawarena %s made", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Ke<PERSON>te dan snjano lasku-kakutropos na vilena %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "<PERSON><PERSON> dan snjano lasku-kakutropos per vilena %s", "commands.scoreboard.objectives.modify.rendertype": "Mahsetropos fu muspie %s kawarena", "commands.scoreboard.objectives.remove.success": "Keshite muspie %s", "commands.scoreboard.players.add.success.multiple": "Nasii %s %s-made na %s sjal", "commands.scoreboard.players.add.success.single": "Nasii %s %s-made per %s (ima %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "<PERSON><PERSON>te dan seena namae fu %s shal na %s", "commands.scoreboard.players.display.name.clear.success.single": "<PERSON><PERSON>te dan seena namae fu %s na %s", "commands.scoreboard.players.display.name.set.success.multiple": "<PERSON><PERSON> dan seena namae, bli %s fu %s shal na %s", "commands.scoreboard.players.display.name.set.success.single": "<PERSON><PERSON> dan seena namae, bli %s fu %s na %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "<PERSON><PERSON><PERSON> dan lasku-kakutropos na %s shal na %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "<PERSON><PERSON>te dan lasku-kakutropos fu %s na %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "<PERSON><PERSON> dan lasku-kakutropos per %s shal na %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "<PERSON><PERSON> dan lasku-kakutropos fu %s na %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON>, tuo seb<PERSON><PERSON> ende jam", "commands.scoreboard.players.enable.invalid": "<PERSON>o brukliik mit dwai<PERSON><PERSON><PERSON><PERSON>", "commands.scoreboard.players.enable.success.multiple": "Bruk dwaibmatropos %s per %s shal", "commands.scoreboard.players.enable.success.single": "Bruk dwaibmatropos %s per %s", "commands.scoreboard.players.get.null": "<PERSON><PERSON><PERSON> sada %s impla fu %s; ende nai antaajena", "commands.scoreboard.players.get.success": "%s har %s %s", "commands.scoreboard.players.list.empty": "<PERSON> nil shuchuena shal", "commands.scoreboard.players.list.entity.empty": "%s har nil jingpik", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s har %s na pikatai:", "commands.scoreboard.players.list.success": "Jam %s <PERSON><PERSON><PERSON><PERSON><PERSON> sjal", "commands.scoreboard.players.operation.success.multiple": "Reformadan %s per %s shal", "commands.scoreboard.players.operation.success.single": "<PERSON><PERSON> %s fu %s %s-made", "commands.scoreboard.players.remove.success.multiple": "Keshite dan %s %s kara per %s shal", "commands.scoreboard.players.remove.success.single": "Keshite dan %s %s-kara per %s (bli %s)", "commands.scoreboard.players.reset.all.multiple": "Pogjen al spilpik fu %s shal", "commands.scoreboard.players.reset.all.single": "Pogjen al spilpik fu %s", "commands.scoreboard.players.reset.specific.multiple": "Pogjen %s fu %s shal", "commands.scoreboard.players.reset.specific.single": "Pogjen %s na %s", "commands.scoreboard.players.set.success.multiple": "<PERSON><PERSON> %s na %s shal %s made", "commands.scoreboard.players.set.success.single": "Kawari %s na %s %s made", "commands.seed.success": "Zaripie: %s", "commands.setblock.failed": "Humba sentaku dado", "commands.setblock.success": "Dado na %s, %s, %s ka<PERSON><PERSON>", "commands.setidletimeout.success": "Spildjin keshitevent ima %s fun", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON> sentaku sintuaplas na mono hadjivelt", "commands.setworldspawn.success": "Sintuaplas %s, %s, %s [%s] sentakena", "commands.spawnpoint.success.multiple": "Sentaku sintuaplas %s, %s, %s [%s] ine %s per %s spildjin", "commands.spawnpoint.success.single": "Sentaku sintuaplas %s, %s, %s [%s] ine %s per %s", "commands.spectate.not_spectator": "%s nai na sedjintropos", "commands.spectate.self": "<PERSON><PERSON><PERSON> se mitt me fu du", "commands.spectate.success.started": "Ima mittse %s", "commands.spectate.success.stopped": "I<PERSON><PERSON> raz<PERSON> shal", "commands.spreadplayers.failed.entities": "Nai deki rmnasii %s shal para %s, %s (obamange shal ka deki jamena inne speisu - da iskat nasii rmnasiitai nil plus ka %s)", "commands.spreadplayers.failed.invalid.height": "Uso maxHeight %s; vilena plus pitka na velt letste pol %s", "commands.spreadplayers.failed.teams": "Nai deki rmnasii %s kla<PERSON> para %s, %s (obamange shal ka deki jamena inne speisu - da iskat nasii rmnasiitai nil plus ka %s)", "commands.spreadplayers.success.entities": "%s spildjin rmnasiijena para %s, %s na pitkatai %s dado prapa rjoho kara", "commands.spreadplayers.success.teams": "%s klaani rmnasiijena para %s, %s na pitkatai %s dado prapa rjoho kara", "commands.stop.stopping": "Server il<PERSON><PERSON>la", "commands.stopsound.success.source.any": "Yametedan al zan na '%s'", "commands.stopsound.success.source.sound": "Yametedan zan '%s' per %s", "commands.stopsound.success.sourceless.any": "Yametedan alzan", "commands.stopsound.success.sourceless.sound": "Yametedan zan '%s'", "commands.summon.failed": "<PERSON><PERSON><PERSON> an<PERSON>a shal", "commands.summon.failed.uuid": "<PERSON><PERSON><PERSON> an<PERSON>a shal grunn UUID samapaari", "commands.summon.invalidPosition": "Uwaki plasnen per nasiintua", "commands.summon.success": "Nasiintua %s neo", "commands.tag.add.failed": "<PERSON>e jena<PERSON> har oba<PERSON>ge na<PERSON>, os har namaenen sent<PERSON>na", "commands.tag.add.success.multiple": "Nasii na<PERSON>enen %s %s sjal made", "commands.tag.add.success.single": "Nasii namaenen %s %s made", "commands.tag.list.multiple.empty": "Jam nil namaenen na tuo %s shal", "commands.tag.list.multiple.success": "%s sjal har %s na<PERSON><PERSON>n nahel: %s", "commands.tag.list.single.empty": "%s nai har namaenen", "commands.tag.list.single.success": "%s har %s namaenen: %s", "commands.tag.remove.failed": "Jenadjin nai har tak namaenen", "commands.tag.remove.success.multiple": "<PERSON><PERSON><PERSON> na<PERSON>n '%s' %s shal kara", "commands.tag.remove.success.single": "<PERSON><PERSON><PERSON> na<PERSON>n '%s' %s kara", "commands.team.add.duplicate": "Jam ende ein klani mit afto namae", "commands.team.add.success": "<PERSON><PERSON><PERSON>: %s", "commands.team.empty.success": "%% pashuun keshitena klaani %s kara", "commands.team.empty.unchanged": "<PERSON><PERSON>, tuo spild<PERSON><PERSON><PERSON> ende ohare", "commands.team.join.success.multiple": "Nasi %s klaanidjin %s made", "commands.team.join.success.single": "Nasi %s klaanidjin %s made", "commands.team.leave.success.multiple": "Keshitena %s klaanidjin mi-klaani-va kara", "commands.team.leave.success.single": "Keshitena %s klaani kara", "commands.team.list.members.empty": "Yam nil pashuun na k<PERSON>ani %s", "commands.team.list.members.success": "Klaani %s jam %s pashuun: %s", "commands.team.list.teams.empty": "Jam nil spildjinklani", "commands.team.list.teams.success": "Jam %s klaani: %s", "commands.team.option.collisionRule.success": "Erslagruru per klani %s bli \"%s\"", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON>, uwakirørruru ende tak", "commands.team.option.color.success": "Reformadan varje fu klaani %s %s made", "commands.team.option.color.unchanged": "<PERSON><PERSON>, tuo spild<PERSON> ende har tuo farie", "commands.team.option.deathMessageVisibility.success": "Shinuvimikakena sejena per klani %s bli \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON>, shinublogepochta dekisetropos ende tak", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON>, mikhono ende lakinai na tuo spildjin<PERSON>ani", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON>, mikarka ende lakijena na tuo spildjinklaani", "commands.team.option.friendlyfire.disabled": "Mikslag nai brukena per klaani %s", "commands.team.option.friendlyfire.enabled": "Mikslag brukena klaani %s made", "commands.team.option.name.success": "Reformadan namae fu klaani %s", "commands.team.option.name.unchanged": "<PERSON><PERSON>, tuo spild<PERSON> ende har tuo namae", "commands.team.option.nametagVisibility.success": "<PERSON><PERSON>en sejena per klaani %s ima \"%s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON>, blogezma fu namaenen ende tak", "commands.team.option.prefix.success": "Ljevafesta fu klaani ima %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON>, tuo spild<PERSON> ende dekinai se furi klaanimik", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON>, tuo spild<PERSON> ende laki se sejena-de<PERSON><PERSON> k<PERSON>k", "commands.team.option.seeFriendlyInvisibles.disabled": "Klaani %s ima lakinai se furi klaanimik", "commands.team.option.seeFriendlyInvisibles.enabled": "Klaani %s ima se furi klaanimik", "commands.team.option.suffix.success": "Owarifesta fu klaani ima %s", "commands.team.remove.success": "Keshite klaani %s", "commands.teammsg.failed.noteam": "Per hanu klani made, du treng klani", "commands.teleport.invalidPosition": "Uwaki plasnen per popal", "commands.teleport.success.entity.multiple": "Popaløze dan %s shal %s made", "commands.teleport.success.entity.single": "Popaløze %s dan %s made", "commands.teleport.success.location.multiple": "Popaløze dan %s shal na %s, %s, %s made", "commands.teleport.success.location.single": "Mah ka %s popal %s, %s, %s made", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Hyakutelleo: H50: %stts H95: %stts H99: %stts, brukena: %s", "commands.tick.query.rate.running": "Vilena bystratai na tidleo: %s na sho\nSnano tid na leo: %stts (vilena: %stts)", "commands.tick.query.rate.sprinting": "Vilena bystratai na tidleo: %s na sho (hest<PERSON>a, mono per lesadjin).\nSnanomellan tid na leo: %stts", "commands.tick.rate.success": "Sentaku dan vilena bystratai fu tidleo %s leo na sho", "commands.tick.sprint.report": "Djinsai jing na %s leo inne sho, %s tuhattel-sho na tidleo", "commands.tick.sprint.stop.fail": "Tidleo-djinsai nai slucha", "commands.tick.sprint.stop.success": "<PERSON><PERSON> dan imalik tid<PERSON>-djinsai", "commands.tick.status.frozen": "Spil jame dan", "commands.tick.status.lagging": "Spil ende dwaibma, a dekinai na vilena bystratai fu tidleo", "commands.tick.status.running": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON> s<PERSON>", "commands.tick.status.sprinting": "<PERSON><PERSON><PERSON> dwaibma na djinsai", "commands.tick.step.fail": "<PERSON><PERSON><PERSON> bai tidleo - spil musti jamejena na<PERSON>s", "commands.tick.step.stop.fail": "<PERSON>id<PERSON> nai slucha", "commands.tick.step.stop.success": "<PERSON><PERSON> dan imalik tidleo", "commands.tick.step.success": "Ergoti %s tidleo", "commands.time.query": "Toki/tid ima %s", "commands.time.set": "<PERSON><PERSON> toki %s made", "commands.title.cleared.multiple": "Keshite namaenen fu %s spildjin", "commands.title.cleared.single": "Keshite namaenen fu %s", "commands.title.reset.multiple": "Pogjen namaesentakjena per %s spildjin", "commands.title.reset.single": "Pogjen namaesentakjena per %s", "commands.title.show.actionbar.multiple": "Ma<PERSON>e neo surunamae antajena per %sdjin", "commands.title.show.actionbar.single": "Ma<PERSON>e neo surunamae antajena per %s", "commands.title.show.subtitle.multiple": "Mahase neo unanamae antajena per %sdjin", "commands.title.show.subtitle.single": "Ma<PERSON>e neo unanamae antajena per %s", "commands.title.show.title.multiple": "Mahase neo atamanamae antajena per %sdjin", "commands.title.show.title.single": "Mahase neo atamanamae antajena per %s", "commands.title.times.multiple": "Namaesetiid ka<PERSON> per %s spildjin", "commands.title.times.single": "Namaesetiid ka<PERSON> per %s", "commands.transfer.error.no_players": "Mus sentaku en spildjin per anugoki", "commands.transfer.success.multiple": "Anugoki %s spildjin na %s:%s made", "commands.transfer.success.single": "Anugoki %s na %s:%s made", "commands.trigger.add.success": "Ma<PERSON>waibma fu sebjasfar %s (nasii %s atai made)", "commands.trigger.failed.invalid": "<PERSON><PERSON> ma<PERSON><PERSON>ma fu muspie mono ka sebjasvar-fal", "commands.trigger.failed.unprimed": "<PERSON><PERSON> de<PERSON>ai jing afto tuvatna", "commands.trigger.set.success": "Ma<PERSON>waibma fu sebjasfar %s (mah ka atai sama %s)", "commands.trigger.simple.success": "Mahadwaibma fu sebjasfar %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Pogoda ende ohare", "commands.weather.set.rain": "Pogoda ende plui", "commands.weather.set.thunder": "Pogoda ende plui au zeus", "commands.whitelist.add.failed": "Spildjin ende ine ainlattumam", "commands.whitelist.add.success": "Nasiidan %s ainlattumam made", "commands.whitelist.alreadyOff": "Ainlattumam ende kestjena", "commands.whitelist.alreadyOn": "Ainlattumam ende zejena", "commands.whitelist.disabled": "<PERSON><PERSON><PERSON><PERSON> kestjena ima", "commands.whitelist.enabled": "<PERSON><PERSON><PERSON><PERSON> zejena ima", "commands.whitelist.list": "Jam %s spildjin na bratumam: %s", "commands.whitelist.none": "Jam nildjin na ainlattumam", "commands.whitelist.reloaded": "Ainlattuma<PERSON>", "commands.whitelist.remove.failed": "Spildjin nai ine ainlattumam", "commands.whitelist.remove.success": "Keshitena %s ain<PERSON><PERSON><PERSON> kara", "commands.worldborder.center.failed": "<PERSON><PERSON>, veltowari melann ende deer", "commands.worldborder.center.success": "Veltgodja melanpik ende %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON>, veltowari arkatai ende tak stuur", "commands.worldborder.damage.amount.success": "Veltgodja arkatidtai ende %s sho", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON>, velt<PERSON><PERSON> arkakømuskepitkatai ende tak stuur", "commands.worldborder.damage.buffer.success": "Veltgodja arkakømuske-paksuatai ende %s dado", "commands.worldborder.get": "Veltgodja paksuatai ima %s dado", "commands.worldborder.set.failed.big": "Velt eksotumam nai deki plus pitka na %s dado", "commands.worldborder.set.failed.far": "Velt-tumamsa nai deki plus prapa na %s dado", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON>, velt<PERSON>ri ende tak stuur", "commands.worldborder.set.failed.small": "Velt eksotumam nai deki plus hobit na 1 dado", "commands.worldborder.set.grow": "<PERSON><PERSON>ba veltersen pitkatai %s dado made na %s sho", "commands.worldborder.set.immediate": "Paksuatai fu Veltgodja ima %s dado", "commands.worldborder.set.shrink": "Paksuatai fu veltgodja hiidas bli %s dado bi %s sho", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON>, velt<PERSON><PERSON> ma<PERSON><PERSON><PERSON><PERSON> ende tak stuur pitkatai", "commands.worldborder.warning.distance.success": "Prapatai per opeta-pochta grun veltgodja ima %s dado", "commands.worldborder.warning.time.failed": "<PERSON><PERSON>, velt<PERSON><PERSON> ma<PERSON><PERSON><PERSON><PERSON> ende tak pitka tiid", "commands.worldborder.warning.time.success": "Sentaku veltograd anopetatid %s sho", "compliance.playtime.greaterThan24Hours": "Du na spil plus na 24 zhikan made", "compliance.playtime.hours": "Du spil na %s djikan", "compliance.playtime.message": "<PERSON><PERSON> spil deki bakawari aldag vona", "connect.aborted": "<PERSON><PERSON> dan", "connect.authorizing": "Zeskjine...", "connect.connecting": "Tsunaga server made...", "connect.encrypting": "<PERSON>ha ziha-taina...", "connect.failed": "Hu<PERSON> dan tsunaga server made", "connect.failed.transfer": "Tsunagahumba anugokija server made", "connect.joining": "Tulla velt...", "connect.negotiating": "<PERSON><PERSON>...", "connect.reconfiging": "<PERSON><PERSON>...", "connect.reconfiguring": "<PERSON><PERSON>...", "connect.transferring": "<PERSON><PERSON><PERSON> andra server made...", "container.barrel": "Badjeldai", "container.beacon": "Kirkasflakka", "container.beehive.bees": "Melitta: %s / %s", "container.beehive.honey": "Mjalt: %s / %s", "container.blast_furnace": "Honobet", "container.brewing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "Kartaergostol", "container.chest": "Baksu", "container.chestDouble": "<PERSON><PERSON><PERSON> baksu", "container.crafter": "<PERSON><PERSON><PERSON><PERSON>", "container.crafting": "<PERSON><PERSON><PERSON>", "container.creative": "Sentaku tin", "container.dispenser": "<PERSON><PERSON><PERSON>", "container.dropper": "Spaadabaks<PERSON>", "container.enchant": "Taikainonno", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s blauishi", "container.enchant.lapis.one": "1 blauishi", "container.enchant.level.many": "%s taikaatai", "container.enchant.level.one": "1 taikaatai", "container.enchant.level.requirement": "Treng %s mestariatai", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Honobet", "container.grindstone_title": "Reforma au keshite taika", "container.hopper": "<PERSON><PERSON><PERSON><PERSON>", "container.inventory": "<PERSON><PERSON><PERSON><PERSON>", "container.isLocked": "%s festena!", "container.lectern": "Librestol", "container.loom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.repair": "Reforma au anhaisa", "container.repair.cost": "Taika treng: %1$s", "container.repair.expensive": "<PERSON><PERSON><PERSON><PERSON>!", "container.shulkerBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "au %s plus...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "<PERSON><PERSON>dan lesa. <PERSON><PERSON><PERSON> imawen nai razmahena.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Plusreforma trakena", "container.upgrade.error_tooltip": "<PERSON><PERSON> mit afto tropos", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON><PERSON>", "controls.keybinds": "Tastakjannos...", "controls.keybinds.duplicateKeybinds": "Afto presmi auen per:\n%s", "controls.keybinds.title": "Tastakjannos", "controls.reset": "<PERSON>gje<PERSON>", "controls.resetAll": "Tastak<PERSON><PERSON> made", "controls.title": "Tastajewald", "createWorld.customize.buffet.biome": "Bitte sentaku joku fal fu lant", "createWorld.customize.buffet.title": "Velttropos 'Bufei'", "createWorld.customize.flat.height": "Pit<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Unnatel - %s", "createWorld.customize.flat.layer.top": "Obatel - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON> qatai", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON> kawarena", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "O<PERSON>, lakiti bruk aftoo ende mahena na vi!", "createWorld.customize.presets.select": "Bruk implabaksu", "createWorld.customize.presets.share": "Vil antaa endemahena jokudjin made? B<PERSON> baksu na una!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON> endema<PERSON>a", "createWorld.preparing": "<PERSON>li ima gotov per veltmaha...", "createWorld.tab.game.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.more.title": "Plus", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Grundjin", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "Kjoukapunkt", "credits_and_attribution.screen.title": "Hvala au grundjin", "dataPack.bundle.description": "Bruk wigel festakaban", "dataPack.bundle.name": "Festakaban", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "<PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Neoyze kaupahok per statdjin", "dataPack.trade_rebalance.name": "Rømpolna na kaupahok fu statdjin", "dataPack.update_1_20.description": "Neo dekina au impla per Minecraft 1.20", "dataPack.update_1_20.name": "Otonøzana 1.20", "dataPack.update_1_21.description": "Neozma na Minecraft 1.21", "dataPack.update_1_21.name": "Versholasku 1.21", "dataPack.validation.back": "<PERSON><PERSON>", "dataPack.validation.failed": "Humba dan lesaiskat implakaban!", "dataPack.validation.reset": "Pogjen na Hadjifal", "dataPack.validation.working": "Iskaat li ziha bruk tuo implakaban...", "dataPack.vanilla.description": "Snjano impla fu Minecraft", "dataPack.vanilla.name": "Snjano", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "Zihatropos", "datapackFailure.safeMode.failed.description": "Afto velt na joku plas uwaki-ufnena.", "datapackFailure.safeMode.failed.title": "Humba lese velt na zihatropos.", "datapackFailure.title": "<PERSON>wa<PERSON> inne implakaban mah humba lese velt.\n<PERSON><PERSON><PERSON> iskat lese na mono snjano implakaban (na \"zihatropos\"), os de<PERSON>ti suruk huomilehti made au fiks na sebja.", "death.attack.anvil": "%1$s gushojena fu bigorna", "death.attack.anvil.player": "%1$s gushojena fu bigorna samatid slag %2$s", "death.attack.arrow": "%1$s erstrelena fu %2$s", "death.attack.arrow.item": "%1$s erstrelena fu %2$s mit %3$s", "death.attack.badRespawnPoint.link": "<PERSON><PERSON><PERSON><PERSON> mitt surupie", "death.attack.badRespawnPoint.message": "%1$s vrasena fu %2$s", "death.attack.cactus": "%1$s erpuangena", "death.attack.cactus.player": "%1$s errør aistiolule samatid jalaka %2$s kara", "death.attack.cramming": "%1$s ergushojena", "death.attack.cramming.player": "%1$s gushojena fu %2$s", "death.attack.dragonBreath": "%1$s ervapjena na rjuu-hengi", "death.attack.dragonBreath.player": "%1$s ervapjena na rjuu-hengi per %2$s", "death.attack.drown": "%1$s erglug", "death.attack.drown.player": "%1$s erglug samatid jalaka %2$s kara", "death.attack.dryout": "%1$s erd<PERSON><PERSON> dan", "death.attack.dryout.player": "%1$s erd<PERSON><PERSON> dan na ushkeks %2$s kara", "death.attack.even_more_magic": "%1$s tak vrasena mit taika", "death.attack.explosion": "%1$s erubam", "death.attack.explosion.player": "%1$s erubamena na %2$s", "death.attack.explosion.player.item": "%1$s erbamena na %2$s mit %3$s", "death.attack.fall": "%1$s dan erspada", "death.attack.fall.player": "%1$s dan erspada is<PERSON><PERSON> shkekso %2$s kara", "death.attack.fallingBlock": "%1$s gushoena fu spada dado", "death.attack.fallingBlock.player": "%1$s gushoena fu spada dado krigja %2$s", "death.attack.fallingStalactite": "%1$s erpılkhena na sparena ishipwann", "death.attack.fallingStalactite.player": "%1$s erpılkhena na sparena is<PERSON>wann kesha krigdan %2$s", "death.attack.fireball": "%1$s ersada honomjah %2$s kara", "death.attack.fireball.item": "%1$s ersada honomjah %2$s kara mit %3$s", "death.attack.fireworks": "%1$s shkekso mitt bambam", "death.attack.fireworks.item": "%1$s shkekso mitt bambam na grun fu honomjachstrela grunn %2$s %3$s kara", "death.attack.fireworks.player": "%1$s shkekso mitt bambam samatid slag %2$s", "death.attack.flyIntoWall": "%1$s smak skjoizeus", "death.attack.flyIntoWall.player": "%1$s smak skjoizeus samatid jalaka %2$s kara", "death.attack.freeze": "%1$s ersamui dan", "death.attack.freeze.player": "%1$s ersamui dan grun %2$s", "death.attack.generic": "%1$s shinu", "death.attack.generic.player": "%1$s shinu grun %2$s", "death.attack.genericKill": "%1$s vrasena", "death.attack.genericKill.player": "%1$s vrasena krigja mit %2$s", "death.attack.hotFloor": "%1$s lera ka pol zjotishke", "death.attack.hotFloor.player": "%1$s erja<PERSON>a dan grun %2$s", "death.attack.inFire": "%1$s erzhot", "death.attack.inFire.player": "%1$s ershkine hono krigja %2$s", "death.attack.inWall": "%1$s shinu inne tumam", "death.attack.inWall.player": "%1$s shinu inne tumam krigja %2$s", "death.attack.indirectMagic": "%1$s vrasena fu %2$s na taika", "death.attack.indirectMagic.item": "%1$s vrasena fu %2$s mit %3$s", "death.attack.lava": "%1$s dan iskat ljeta inne zjotishke", "death.attack.lava.player": "%1$s dan iskat ljeta inne zjotishke na djinsai %2$s kara", "death.attack.lightningBolt": "%1$s erzeusena", "death.attack.lightningBolt.player": "%1$s erzeusena krigja %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s tak vrasena na taika", "death.attack.magic.player": "%1$s tak vrasena na taika djinsaija %2$s kara", "death.attack.message_too_long": "Na pravda pochta tak pitka ka <PERSON>mbadan anta na hel. Gomen! Plushobit her: %s", "death.attack.mob": "%1$s vrasena na %2$s", "death.attack.mob.item": "%1$s vrasena na %2$s mit %3$s", "death.attack.onFire": "%1$s erzhot", "death.attack.onFire.item": "%1$s erzhot na nil made nakrig %2$s brukja %3$s", "death.attack.onFire.player": "%1$s erzhot na nil made nakrig %2$s", "death.attack.outOfWorld": "%1$s erspada velt kara", "death.attack.outOfWorld.player": "%1$s vilnai dan vona sama velt ke %2$s", "death.attack.outsideBorder": "%1$s shangdan oba tumam fu velt", "death.attack.outsideBorder.player": "%1$s shangdan oba tumam fu velt krigja %2$s", "death.attack.player": "%1$s vrasena na %2$s", "death.attack.player.item": "%1$s vrasena na %2$s mit %3$s", "death.attack.sonic_boom": "%1$s annilena na zambum", "death.attack.sonic_boom.item": "%1$s annilena na zambum kesha iskatdan shkèi %2$s, mit %3$s, kara", "death.attack.sonic_boom.player": "%1$s annilena na zambum kesha iskatdan shkèi %2$s kara", "death.attack.stalagmite": "%1$s erpılkhena oba ishipwann", "death.attack.stalagmite.player": "%1$s erpılk<PERSON>a oba is<PERSON>wann kesha krigdan %2$s", "death.attack.starve": "%1$s ernamohare", "death.attack.starve.player": "%1$s ernamohare krigja %2$s", "death.attack.sting": "%1$s erpuangena", "death.attack.sting.item": "%1$s erpuangena na %2$s mit %3$s", "death.attack.sting.player": "%1$s erpuangena na %2$s", "death.attack.sweetBerryBush": "%1$s erpuangena fu frautnenrazvjetka", "death.attack.sweetBerryBush.player": "%1$s erpuangena fu frautnenrazvjetkasamatid iskat jalaka %2$s kara", "death.attack.thorns": "%1$s shinu grun iskat arka %2$s", "death.attack.thorns.item": "%1$s vrasena fu %3$s iskat slag %2$s", "death.attack.thrown": "%1$s razarka %2$s kara", "death.attack.thrown.item": "%1$s razarka %2$s kara mit %3$s", "death.attack.trident": "%1$s erpılkhena na %2$s", "death.attack.trident.item": "%1$s erpılkhena na %2$s mit %3$s", "death.attack.wither": "%1$s hnopit", "death.attack.wither.player": "%1$s hnopit samatid krig %2$s", "death.attack.witherSkull": "%1$s strelena na poneatama %2$s kara", "death.attack.witherSkull.item": "%1$s strelena na atamapone %2$s kara %3$sol", "death.fell.accident.generic": "%1$s erspada oba kara", "death.fell.accident.ladder": "%1$s erspada obaatai kara", "death.fell.accident.other_climbable": "%1$s erspada samatid leo", "death.fell.accident.scaffolding": "%1$s erspada nisele kara", "death.fell.accident.twisting_vines": "%1$s erspada raskrungut spadaruti kara", "death.fell.accident.vines": "%1$s erspada spadaruti kara", "death.fell.accident.weeping_vines": "%1$s erspada nakuspadaruti kara", "death.fell.assist": "%1$s udachi spada grun %2$s", "death.fell.assist.item": "%1$s udachi spada grun %2$s mit %3$s", "death.fell.finish": "%1$s spada, de %2$s erowari sore", "death.fell.finish.item": "%1$s spada, de %2$s erowari sore %3$s brukena", "death.fell.killer": "%1$s udachi spada", "deathScreen.quit.confirm": "Vil shkekso?", "deathScreen.respawn": "Gjensintua", "deathScreen.score": "Spilpik", "deathScreen.score.value": "Pikatai: %s", "deathScreen.spectate": "Mono anse velt", "deathScreen.title": "<PERSON>jinu dan!", "deathScreen.title.hardcore": "<PERSON><PERSON><PERSON> o<PERSON>!", "deathScreen.titleScreen": "Senta<PERSON><PERSON>am", "debug.advanced_tooltips.help": "F3 + H = <PERSON>ts<PERSON>in mahklarna", "debug.advanced_tooltips.off": "Svinnr sentak<PERSON>na: taina", "debug.advanced_tooltips.on": "Svinnr sentak<PERSON>na: sejen", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON>e tumam na velttelnen", "debug.chunk_boundaries.off": "Velttelnen akotesen: furi", "debug.chunk_boundaries.on": "Velttelnen akotesen: mahse", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON> chatto", "debug.copy_location.help": "F3 + C = Zesada imaplas na /tp-dajena-tropos, tasta F3 + C plus per perpa spil", "debug.copy_location.message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> made", "debug.crash.message": "F3 + C tatsu ima. <PERSON> nai owari, spil perpa.", "debug.crash.warning": "Perpa sebja bi %s...", "debug.creative_spectator.error": "<PERSON><PERSON><PERSON> kawari spiltropos; treng kioka", "debug.creative_spectator.help": "F3 + <PERSON> = <PERSON><PERSON> s<PERSON> <-> setropos", "debug.dump_dynamic_textures": "<PERSON><PERSON>ne dan kawari-kodjariso na %s", "debug.dump_dynamic_textures.help": "F3 + S = <PERSON><PERSON><PERSON> ka<PERSON> kozhariso", "debug.gamemodes.error": "<PERSON><PERSON>dan auki spiltropossentakubrukting; treng kjoka", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON> spiltropossentakubrukting", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s miraiti", "debug.help.help": "F3 + Q = <PERSON><PERSON>e afto tumam", "debug.help.message": "Tastatsunagena:", "debug.inspect.client.block": "Dadoimpla fu brukdjin bliz<PERSON><PERSON> zehuskenen made", "debug.inspect.client.entity": "Jamshirena fu brukdjin bliz<PERSON><PERSON> made", "debug.inspect.help": "F3 + I = Shal-/dad<PERSON><PERSON> made", "debug.inspect.server.block": "Dadoimpla fu server bli<PERSON><PERSON><PERSON> zehuskenen made", "debug.inspect.server.entity": "Jamimpla fu server bli<PERSON><PERSON><PERSON> zehuskenen made", "debug.pause.help": "F3 + Esc = <PERSON><PERSON><PERSON> uten auki owarinensentakutumam (mono li deki)", "debug.pause_focus.help": "F3 + P = Dwaibmavent koske uten shuchu", "debug.pause_focus.off": "Vent na eksoklikjena -- nai", "debug.pause_focus.on": "Vent na eksoklikjena -- akk", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = <PERSON><PERSON>/owari implavirta", "debug.profiling.start": "Implavirta ufena na %s sho. Brukti F3 + L per owari uten vent", "debug.profiling.stop": "Tsuiterazkaku owari. Ufne dan na %s", "debug.reload_chunks.help": "F3 + A = Gjenlesa velttelnen", "debug.reload_chunks.message": "Gjenlesa al velttelnen", "debug.reload_resourcepacks.help": "F3 + T = Gjenlesa shirenabaksu", "debug.reload_resourcepacks.message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan shire<PERSON>u", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON><PERSON> rørbaksu fu shal", "debug.show_hitboxes.off": "Slagbaku: taina", "debug.show_hitboxes.on": "Slagbaku: sejena", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Afto iskatversio mono 5 spildah - braergotsa!", "demo.day.2": "<PERSON><PERSON> daag", "demo.day.3": "<PERSON>res daag", "demo.day.4": "<PERSON><PERSON> daag", "demo.day.5": "Afto letste daag na du!", "demo.day.6": "<PERSON><PERSON> <PERSON>wari gos dah. Surutsa %s per ufne zetumamriso fu mahena fu du.", "demo.day.warning": "Tid fu du akote owari!", "demo.demoExpired": "<PERSON><PERSON> brukti<PERSON>!", "demo.help.buy": "Kuupa ima!", "demo.help.fullWrapped": "Bruktid fu afto iskatversio - 5 spildah (para 1 djikan au 40 fun gwirtid). Da razse surjenatumam per hjerneapu! Nintendotsa!", "demo.help.inventory": "Bruk %1$s tasta per auki hartumam", "demo.help.jump": "Pal na tasta %1$s", "demo.help.later": "Bengspil!", "demo.help.movement": "Bruk %1$s, %2$s, %3$s, %4$s tasta au mysh per razshkoi", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> mitt mysh", "demo.help.movementShort": "Tasta %1$s, %2$s, %3$s, %4$s per ugoki", "demo.help.title": "Minecraft Iskatfal", "demo.remainingTime": "Lasena tiid: %s", "demo.reminder": "Tiid eskatena yamete, kuupa spil per ende spil os hadji velt neo!", "difficulty.lock.question": "Zettai vil erfesta haasteatai na %1$s? Almirai festati, au nilmirai kawarilaki.", "difficulty.lock.title": "Mahfesta hastatai velta", "disconnect.endOfStream": "<PERSON><PERSON><PERSON><PERSON>", "disconnect.exceeded_packet_rate": "<PERSON><PERSON><PERSON><PERSON><PERSON> grun obabystra antaa zekonen", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "<PERSON><PERSON><PERSON>", "disconnect.loginFailedInfo": "Humba zepaperi: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Mi<PERSON>os<PERSON><PERSON> ima lakinai. Bitte se sentakana fu Microsoft-zepaperi.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> brukti<PERSON> (Tabun gjenauki spel au hadjibma)", "disconnect.loginFailedInfo.serversUnavailable": "Server per lesa zepaperi ima utend<PERSON>. Bite plustid.", "disconnect.loginFailedInfo.userBanned": "Lakinai spil narjet grun banena", "disconnect.lost": "<PERSON><PERSON><PERSON> tsam dan", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>anu", "disconnect.timeout": "<PERSON><PERSON>nt dan", "disconnect.transfer": "<PERSON><PERSON><PERSON> dan andra server made", "disconnect.unknownHost": "<PERSON><PERSON><PERSON> k<PERSON>", "download.pack.failed": "%s na %s baksu humba rjetlesa", "download.pack.progress.bytes": "Jenatai: %s (sturatai nai shirena)", "download.pack.progress.percent": "Jenatai: %s%%", "download.pack.title": "Rjetlesa basu %s/%s", "editGamerule.default": "Hadjiimpla: %s", "editGamerule.title": "<PERSON><PERSON> s<PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON>", "effect.minecraft.bad_omen": "Mirainafa", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "<PERSON> fu tsunagasen", "effect.minecraft.darkness": "Ku<PERSON>", "effect.minecraft.dolphins_grace": "Fliire fu maresvinja", "effect.minecraft.fire_resistance": "Eshku per honoo", "effect.minecraft.glowing": "<PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.health_boost": "Zdorvaplus", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON>", "effect.minecraft.hunger": "Oharemauge", "effect.minecraft.infested": "<PERSON>lan", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON>", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "Pal plus", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Rvotakokoro", "effect.minecraft.night_vision": "Nahtse", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON>", "effect.minecraft.poison": "<PERSON><PERSON>", "effect.minecraft.raid_omen": "Raz<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Razbengvona", "effect.minecraft.resistance": "Arkakomuske", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "Hidas spada", "effect.minecraft.slowness": "<PERSON><PERSON>", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "Djong", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "Nafa", "effect.minecraft.water_breathing": "Iskjehengi", "effect.minecraft.weakness": "<PERSON><PERSON> mukki", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Luftpulap", "effect.minecraft.wither": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.none": "Suru nil", "enchantment.level.1": "1s", "enchantment.level.10": "10s", "enchantment.level.2": "2s", "enchantment.level.3": "3s", "enchantment.level.4": "4s", "enchantment.level.5": "5s", "enchantment.level.6": "6s", "enchantment.level.7": "7s", "enchantment.level.8": "8s", "enchantment.level.9": "9s", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "Utenmik na mangejalaka", "enchantment.minecraft.binding_curse": "Erfestanafa", "enchantment.minecraft.blast_protection": "Bam eshku", "enchantment.minecraft.breach": "<PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON>", "enchantment.minecraft.density": "Vehtdai", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "Pjera spada", "enchantment.minecraft.fire_aspect": "Honoimpla", "enchantment.minecraft.fire_protection": "<PERSON>shku na honoo", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Udachidai", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Kornukopia", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Plussad<PERSON>", "enchantment.minecraft.loyalty": "Hinashkoi", "enchantment.minecraft.luck_of_the_sea": "Udacji fu hav", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Reforma", "enchantment.minecraft.multishot": "Razpostrel", "enchantment.minecraft.piercing": "Bidesslag", "enchantment.minecraft.power": "Zeus", "enchantment.minecraft.projectile_protection": "Strelakømuske", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Slag", "enchantment.minecraft.quick_charge": "<PERSON><PERSON>", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Erishketropos", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "<PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Shalbistra", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping_edge": "Voikatana", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Djidopin", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Erbortenafa", "enchantment.minecraft.wind_burst": "Luftbam", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "Apuzhin", "entity.minecraft.area_effect_cloud": "Taikakumo fu plas", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "Strela", "entity.minecraft.axolotl": "Atheksetl", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "Honodjin", "entity.minecraft.block_display": "Dad<PERSON><PERSON><PERSON>", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Luftbamba", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> mit baksu", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON> mit jew<PERSON><PERSON><PERSON>o", "entity.minecraft.cow": "Gju", "entity.minecraft.creaking": "Djidodjin", "entity.minecraft.creaking_transient": "Djidodjin", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Maresvinja", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Rjuuhonomjah", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON><PERSON>", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "Endpetra", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON>a ender-san<PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Taikantadjin", "entity.minecraft.evoker_fangs": "Taikantadjin Hammas", "entity.minecraft.experience_bottle": "Taikaklinye yiitena", "entity.minecraft.experience_orb": "Mestarimjach", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "<PERSON><PERSON>", "entity.minecraft.falling_block_type": "Spaada %s", "entity.minecraft.fireball": "Honomjah", "entity.minecraft.firework_rocket": "Honomjachs<PERSON>la", "entity.minecraft.fishing_bobber": "Sakanamjah", "entity.minecraft.fox": "Kitsne", "entity.minecraft.frog": "Baba", "entity.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON> mit honobet", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Sturdaidjin", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Zihadjin", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> mit sadantating", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "<PERSON><PERSON><PERSON>", "entity.minecraft.illusioner": "Furitaikadjin", "entity.minecraft.interaction": "Brukzma", "entity.minecraft.iron_golem": "Jerkat-robottodai", "entity.minecraft.item": "<PERSON>g", "entity.minecraft.item_display": "<PERSON>g <PERSON>n", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Nuitodeza", "entity.minecraft.lightning_bolt": "Zeusstrelaa", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_boat": "Mangrc<PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> mit baksu", "entity.minecraft.marker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "Baumkot", "entity.minecraft.ominous_item_spawner": "Nafakoro sintuabma", "entity.minecraft.painting": "Riso", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON>-sjip", "entity.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON>-sjip mit baksu", "entity.minecraft.panda": "Karhukot", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "On", "entity.minecraft.pig": "Sfinja", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin-Mukkidjin", "entity.minecraft.pillager": "Basadakrigzhin", "entity.minecraft.player": "Spildjin", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "Taikaiskje", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Erviskenadjin", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Sokeribage", "entity.minecraft.skeleton": "Ponedjin", "entity.minecraft.skeleton_horse": "Ponehengest", "entity.minecraft.slime": "<PERSON><PERSON><PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.sniffer": "Nioidjin", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON>", "entity.minecraft.snowball": "Upasmjacj", "entity.minecraft.spawner_minecart": "Sintuajaitso f'Rofai mit Morko", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "Jalakadjin", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "<PERSON><PERSON><PERSON>", "entity.minecraft.tnt": "Pozjotena Bambam", "entity.minecraft.tnt_minecart": "<PERSON><PERSON><PERSON> mit Bambam", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Sardeshkadai", "entity.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> fu <PERSON>", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "<PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "Dronnetelesakana", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON> <PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON> me<PERSON>", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON>kari", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "Ki<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.7": "Razpiksakana", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Gdhendsakana", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Strelaeshkudjin", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Ersuhadjin", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Razsenlik", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON>", "entity.minecraft.turtle": "Breshka", "entity.minecraft.vex": "Rovozhin", "entity.minecraft.villager": "Statdjin", "entity.minecraft.villager.armorer": "Komuskemahdjin", "entity.minecraft.villager.butcher": "Shøtdjin", "entity.minecraft.villager.cartographer": "Kartadjin", "entity.minecraft.villager.cleric": "Kamihuomidjin", "entity.minecraft.villager.farmer": "Nuntshangdjin", "entity.minecraft.villager.fisherman": "Sakanadjin", "entity.minecraft.villager.fletcher": "Streladjin", "entity.minecraft.villager.leatherworker": "Gjukodjadjin", "entity.minecraft.villager.librarian": "Librehuomidjin", "entity.minecraft.villager.mason": "Hwomimahadjin", "entity.minecraft.villager.nitwit": "Bakadjin", "entity.minecraft.villager.none": "Statdjin", "entity.minecraft.villager.shepherd": "Lammasdjin", "entity.minecraft.villager.toolsmith": "Bruktingdjin", "entity.minecraft.villager.weaponsmith": "Kriigbruktingmahadjin", "entity.minecraft.vindicator": "Pravdaøzesjin", "entity.minecraft.wandering_trader": "Rasshkèi <PERSON>n", "entity.minecraft.warden": "Erdarmdjin", "entity.minecraft.wind_charge": "Luftbamba", "entity.minecraft.witch": "Taikadjin", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherponedjin", "entity.minecraft.wither_skull": "Wither<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Odaremhengest", "entity.minecraft.zombie_villager": "Odarempolisdjin", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.not_summonable": "Posintua dekinai shal na fal %s", "event.minecraft.raid": "Razharza", "event.minecraft.raid.defeat": "Humba", "event.minecraft.raid.defeat.full": "Erharza - Humba", "event.minecraft.raid.raiders_remaining": "Lasena razharzadjin: %s", "event.minecraft.raid.victory": "Jingdai", "event.minecraft.raid.victory.full": "Erharza - Jing", "filled_map.buried_treasure": "Tainalek<PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "Pluidasos<PERSON><PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.id": "Lasku %s", "filled_map.level": "(Atai %s/%s)", "filled_map.locked": "Festena", "filled_map.mansion": "Razdasosikarta", "filled_map.monument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.scale": "Har stuuratai fu 1:%s", "filled_map.trial_chambers": "Karta fu iskatdai-rumara", "filled_map.unknown": "Kuamnai karta", "filled_map.village_desert": "Karta fu sanpolis", "filled_map.village_plains": "Poliskarta fu munauki", "filled_map.village_savanna": "Poliskarta fu vapapol", "filled_map.village_snowy": "Poliskarta fu upashlant", "filled_map.village_taiga": "Poliskarta fu upashdasos", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Snjano <PERSON>", "flat_world_preset.minecraft.desert": "Djotridai", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.snowy_kingdom": "Upashlant", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Onar fu <PERSON>", "flat_world_preset.minecraft.water_world": "Ishkevelt", "flat_world_preset.unknown": "???", "gameMode.adventure": "Hshfultropos", "gameMode.changed": "Spiltropos fu du kawarjena %s made", "gameMode.creative": "Is<PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "Erhaastetropos!", "gameMode.spectator": "Sedjintropos", "gameMode.survival": "Bidesvona-tropos", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON><PERSON> koske surjena surjena", "gamerule.blockExplosionDropDecay": "<PERSON> mellan-dado bam, joku dadu spada nil gavat", "gamerule.blockExplosionDropDecay.description": "<PERSON><PERSON> spadatinn dado shahhtena na bum kara khnopit na bum.", "gamerule.category.chat": "<PERSON><PERSON>", "gamerule.category.drops": "Spadating", "gamerule.category.misc": "<PERSON>r", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "Spildjin", "gamerule.category.spawning": "Nasiintua", "gamerule.category.updates": "<PERSON><PERSON>t neo-øzanara", "gamerule.commandBlockOutput": "<PERSON><PERSON><PERSON> svarena per da<PERSON>na grun jew<PERSON>", "gamerule.commandModificationBlockLimit": "Dado-eleatai fu jewaldko-kawari", "gamerule.commandModificationBlockLimit.description": "Atai dado kawariena na en raz mit ein j<PERSON>, tato pulap os gjen<PERSON>a.", "gamerule.disableElytraMovementCheck": "Senai osnai uwaki ugoki mitt tsubasa", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "Razharza nai brukena", "gamerule.doDaylightCycle": "Mahashkoi tiid", "gamerule.doEntityDrops": "Ting baksu kara", "gamerule.doEntityDrops.description": "<PERSON><PERSON>t spadena na rofai-kara (baks<PERSON><PERSON><PERSON> awen), esh<PERSON><PERSON> kara, chip kara, au<PERSON>.", "gamerule.doFireTick": "Suru tidleonen na honoo", "gamerule.doImmediateRespawn": "Gjensintua ima", "gamerule.doInsomnia": "Sin<PERSON><PERSON> on", "gamerule.doLimitedCrafting": "Mus lera mahatropos per viskmaha", "gamerule.doLimitedCrafting.description": "<PERSON> bruke<PERSON>, spild<PERSON> deki maha mono lerenadan mahatrop<PERSON>.", "gamerule.doMobLoot": "<PERSON><PERSON> kara", "gamerule.doMobLoot.description": "<PERSON><PERSON> spadai<PERSON>la fu von<PERSON>, mit mestarimjach.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doMobSpawning.description": "<PERSON><PERSON> shal deki har andra ru<PERSON>.", "gamerule.doPatrolSpawning": "Nasiintua darmaziha kundar polis<PERSON>rigdjin", "gamerule.doTileDrops": "Ting dado kara", "gamerule.doTileDrops.description": "<PERSON><PERSON>t spadena dado kara, awen me<PERSON><PERSON><PERSON><PERSON>.", "gamerule.doTraderSpawning": "Sintwa rasshkèi ho<PERSON>zhin", "gamerule.doVinesSpread": "Spadaruti spadahtella", "gamerule.doVinesSpread.description": "<PERSON><PERSON> li snjano spadaruti nazari palhtella akote-dado made. <PERSON>i jewald andra fal fu spadaruti tato nak<PERSON>, razkrungut-spadaruti auau.", "gamerule.doWardenSpawning": "Sintua es<PERSON>", "gamerule.doWeatherCycle": "Reforma pogoda", "gamerule.drowningDamage": "Jam utenhengiarka", "gamerule.enderPearlsVanishOnDeath": "Jitena ender-sanmjah xnopyt na shinuna", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON> jitena <PERSON>-<PERSON><PERSON><PERSON> xnopytnai-xnopyt ti, za shinu jit<PERSON>.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "Jam spadaarka", "gamerule.fireDamage": "Jam honoarka", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON> shinu spildjin", "gamerule.forgiveDeadPlayers.description": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON>li vonating jamete bøøze koske periskatjena spildjin shinu para.", "gamerule.freezeDamage": "<PERSON>", "gamerule.globalSoundEvents": "Helveltslucha zam", "gamerule.globalSoundEvents.description": "Koske apàr jokutinn slucha na spil, tato<PERSON>a sintua fu j<PERSON>, zam h<PERSON><PERSON> al<PERSON>.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON> harjena za shinu", "gamerule.lavaSourceConversion": "<PERSON><PERSON><PERSON><PERSON><PERSON> bli z<PERSON><PERSON>n", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON>tishkesh<PERSON>i bli kran li jam ni zjotishkekran akote.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "<PERSON><PERSON>e jeval<PERSON>ko fu jevaltdjin", "gamerule.maxCommandChainLength": "Jewaltdadofestasen stuuratai naele", "gamerule.maxCommandChainLength.description": "Na jewaltdado-rofaj au dwaibmaruga.", "gamerule.maxCommandForkCount": "Slucjaplas-eleatai fu jewaldko", "gamerule.maxCommandForkCount.description": "Eleatai fu slucjaplas k deki brukena tato 'anergo na'.", "gamerule.maxEntityCramming": "Shaltønnigutsjo polatai", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, joku dado har nil gavat", "gamerule.mobExplosionDropDecay.description": "<PERSON><PERSON> gavat grun bam<PERSON><PERSON><PERSON> dado (t<PERSON><PERSON> grun <PERSON>) auen erkeshitena.", "gamerule.mobGriefing": "<PERSON>ki ka vonashal perpa ting", "gamerule.naturalRegeneration": "<PERSON>ha gjen fu z<PERSON>ai", "gamerule.playersNetherPortalCreativeDelay": "Venttid per Netherdvera na ishuspel", "gamerule.playersNetherPortalCreativeDelay.description": "Tid (na leo) dabi mus ventena inne taikadvera per ke spildjin anugoki andr velt made.", "gamerule.playersNetherPortalDefaultDelay": "Venttid na f'speldjin Netherdvera", "gamerule.playersNetherPortalDefaultDelay.description": "Tid (na leo) dabi spildjin musti vent na Netherdvera per anugoki andra velt made.", "gamerule.playersSleepingPercentage": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.playersSleepingPercentage.description": "Fuhjakasma fu spilzhin ka mus kola per pal bides nakht.", "gamerule.projectilesCanBreakBlocks": "<PERSON>rela deki perpa dado", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON> de<PERSON>zma fu dado bli perpena grun strela.", "gamerule.randomTickSpeed": "Bystratai fu zari-tidleo", "gamerule.reducedDebugInfo": "<PERSON>us zef<PERSON> shirena", "gamerule.reducedDebugInfo.description": "<PERSON><PERSON><PERSON><PERSON> atai fu seena ting na dwaibma-ekran.", "gamerule.sendCommandFeedback": "<PERSON><PERSON>", "gamerule.showDeathMessages": "<PERSON><PERSON><PERSON> s<PERSON><PERSON>", "gamerule.snowAccumulationHeight": "Letste pitkasma fu upash", "gamerule.snowAccumulationHeight.description": "Koske plui upash, vë fu upash ammons oba pol na letste afto atai fu v made.", "gamerule.spawnChunkRadius": "Prapazma na sintua-velttelnen", "gamerule.spawnChunkRadius.description": "Atai fu velttelnen ke ende asa para sintuap<PERSON> na hadjivelt.", "gamerule.spawnRadius": "Gjensintua parakrais stuuratai", "gamerule.spawnRadius.description": "<PERSON><PERSON> prap<PERSON>ma sintu<PERSON>-kara dana spildjin deki sintuena.", "gamerule.spectatorsGenerateChunks": "De<PERSON> ka spildjin na setropos maha neo la<PERSON>nen", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "Na dadobam, joku dado gavat nil", "gamerule.tntExplosionDropDecay.description": "<PERSON><PERSON> gavat grun bamkes<PERSON>ena dado (t<PERSON><PERSON> grun bamdado) auen erkeshit<PERSON>.", "gamerule.universalAnger": "<PERSON><PERSON><PERSON> per aldjin", "gamerule.universalAnger.description": "<PERSON><PERSON><PERSON> hø<PERSON>li vonating harza al spildjin para, nai mono tsatain far<PERSON>. <PERSON><PERSON><PERSON><PERSON> lets<PERSON><PERSON><PERSON> li forgiveDeadPlayers nai brukjena.", "gamerule.waterSourceConversion": "<PERSON><PERSON> bli kran", "gamerule.waterSourceConversion.description": "Ishkeshkoi bli kran li jam ni ishkekran akote.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.customized": "<PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "ERRUPNE", "generator.minecraft.amplified.info": "<PERSON><PERSON><PERSON>: Prosta na nintendo! Treng ustaz zhongmuki na kompju.", "generator.minecraft.debug_all_block_states": "Mylvras Tropos", "generator.minecraft.flat": "Poldai", "generator.minecraft.large_biomes": "<PERSON><PERSON>", "generator.minecraft.normal": "Hadjifal", "generator.minecraft.single_biome_surface": "Ein-mono Lantafal", "generator.single_biome_caves": "Siranga", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "T<PERSON>teka<PERSON>", "gui.abuseReport.describe": "Li antaa plus tsuite, de plus brati svarsentaku fuvi.", "gui.abuseReport.discard.content": "<PERSON>, de erspadati anopeta au tsuitera.\n<PERSON><PERSON><PERSON> we, ke vilti shkekso?", "gui.abuseReport.discard.discard": "Shkekso au erspaada anopeta", "gui.abuseReport.discard.draft": "Ufne na tabunversho", "gui.abuseReport.discard.return": "<PERSON><PERSON>", "gui.abuseReport.discard.title": "Erspaada anopeta au tsuitera?", "gui.abuseReport.draft.content": "Vilti du ende razkawari jamversho fu anopeta, os myl-jit sore au hadji naneo?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "<PERSON><PERSON>", "gui.abuseReport.draft.quittotitle.content": "Vilti ende kawari os erspaadati?", "gui.abuseReport.draft.quittotitle.title": "Jam na du ende tabunversho ke spaadenati li shkekso", "gui.abuseReport.draft.title": "<PERSON><PERSON> tabun<PERSON>ho fu anopeta?", "gui.abuseReport.error.title": "<PERSON>mba dan na antaa anopetana", "gui.abuseReport.message": "Doko se dan warui suruzma?\nAfto aputi na razzuhe oba slucharjet.", "gui.abuseReport.more_comments": "Bite opeta oba ka slucha dan:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "Du anopeta \"%s\".", "gui.abuseReport.name.title": "<PERSON><PERSON><PERSON> oba warui spild<PERSON>", "gui.abuseReport.observed_what": "Naze anopeta afto?", "gui.abuseReport.read_info": "Lera oba anopetarjet", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Tuinnen os kirkas", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Jo<PERSON><PERSON><PERSON> gutjo-hanu andradjin per bruk tuinnen nauwaki os suru joku andra navir<PERSON>, os gutjo andradjin per glug kirkas na obamaloddjin.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON>-fal basuru os babruk fu molodadjin", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON><PERSON><PERSON> hanu oba (os napaksu blogeta per) ike suruna para lapsi.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON>, os uso shiruna", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Yoku<PERSON><PERSON> basuru brukja namae fu and<PERSON><PERSON>, bafuri na yokudjin koske nafig, os opeta uso shiruna per babruk os tunøze andrad<PERSON>.", "gui.abuseReport.reason.description": "Impla:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON>/<PERSON><PERSON>", "gui.abuseReport.reason.generic": "Un vil anopeta tsuite sore", "gui.abuseReport.reason.generic.description": "Un rovoena grun sore, os sore suru dan joku un duanai.", "gui.abuseReport.reason.harassment_or_bullying": "Hanuslag os harzapochta", "gui.abuseReport.reason.harassment_or_bullying.description": "Jo<PERSON><PERSON><PERSON> hanuslag, ha<PERSON><PERSON>, os razbahanu na du os and<PERSON><PERSON> made. Tabun pashun raziskat anpochta/anhanu du made (os andradjin) uten lakina, os blogeta tainashiruna fu djin uten lakina (\"doxing\" tte ha<PERSON><PERSON><PERSON>).", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "Jokudjin harza du kari (os andra speldjin) grun pashuntropos fu jenadjin, ta<PERSON><PERSON><PERSON> v<PERSON>it<PERSON>, kod<PERSON><PERSON><PERSON>, sintuaplas, os duatropos.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON>-harz<PERSON> - <PERSON><PERSON>u ke harzamirai djin made", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON><PERSON><PERSON> bahanu ke tuvatmirai nagwir harza du os andradjin.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Yin<PERSON>riso pochtena uten uslova", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON> hanu oba, <PERSON><PERSON><PERSON><PERSON>, os napaksu blogeta yinsy os netopalik riso.", "gui.abuseReport.reason.self_harm_or_suicide": "Tullaarga - sebya-arga os sebyavras", "gui.abuseReport.reason.self_harm_or_suicide.description": "Yo<PERSON><PERSON><PERSON> erhanu ke mirai arga os vras sebya inne gvir velt, os razhanu oba afto mipie.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Anachorzmadai os erike bak<PERSON>", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON><PERSON><PERSON> hanu oba, blogeta per, os bahanu ka mirai suru joku na fal fu anachorzmadai os erike bak<PERSON>zma grun jew<PERSON>po<PERSON>, v<PERSON><PERSON><PERSON><PERSON>, os mi<PERSON><PERSON>.", "gui.abuseReport.reason.title": "Sentaku <PERSON> fu Anopeta", "gui.abuseReport.report_sent_msg": "Vi saada dan anopetapochta fu du. Dankedai!\n\nKla<PERSON> fuvi erlesati tak bystra ke deki.", "gui.abuseReport.select_reason": "Sentaku fal fu anopeta-pochta", "gui.abuseReport.send": "Antaa anopeta-pochta", "gui.abuseReport.send.comment_too_long": "<PERSON>e hobbitøze tsuitana", "gui.abuseReport.send.error_message": "<PERSON><PERSON><PERSON><PERSON> svarena na antaa anopetana:\n'%s'", "gui.abuseReport.send.generic_error": "<PERSON><PERSON> ishaika bli dan koske antaa anopetana.", "gui.abuseReport.send.http_error": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ena koske antaa anopetana.", "gui.abuseReport.send.json_error": "Jam dan warui implakaban koske antaa anopetana fu du.", "gui.abuseReport.send.no_reason": "Bite sentakutsa fal fu anopetana", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Dekinai urør Babruk Anopeta ergorjet. Bite spør li jam rjettsunaga de gjeniskat.", "gui.abuseReport.sending.title": "Anta ima anopetana...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON> dan", "gui.abuseReport.skin.title": "<PERSON><PERSON><PERSON> grun kod<PERSON>o", "gui.abuseReport.title": "An<PERSON><PERSON> tsui spildjin", "gui.abuseReport.type.chat": "<PERSON><PERSON>", "gui.abuseReport.type.name": "S<PERSON>lnamae", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.acknowledge": "Fshto", "gui.advancements": "<PERSON><PERSON>", "gui.all": "Al", "gui.back": "<PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON> plus naher: %s", "gui.banned.description.permanent": "Zefuga banena na almirai made, imi ke dekinai narjet os na Realms spil.", "gui.banned.description.reason": "Dannen sada vi anopeta grun warui slucha na zefuga fu du. Jewalddjin ima rømlesa dan slucharjet de anhaissa sore na %s, tropos ke uwaki na høfliruru fu Minecraft.", "gui.banned.description.reason_id": "Slucha-ID: %s", "gui.banned.description.reason_id_message": "Slucha-ID: %s - %s", "gui.banned.description.temporary": "%s Taz made, dekinai spelti narjet os na Realms.", "gui.banned.description.temporary.duration": "<PERSON><PERSON><PERSON> fudu apartid yamena, de beng<PERSON>i na %s.", "gui.banned.description.unknownreason": "Dannen sada vi anopeta grun warui slucha na zefuga fu du. Jewalddjin ima rømlesa dan slucharjet, au iken ke tropos uwaki na høfliruru fu Minecraft.", "gui.banned.name.description": "<PERSON>ae fu du - \"%s\" - kundr ruurutumam napaksu. <PERSON><PERSON> ende spel nato<PERSON>a, a musti kawari namae per spel na zerjet.\n\nLera plus, os antaa kundrsvar long: %s", "gui.banned.name.title": "Tak namae lakinai na <PERSON>anispil", "gui.banned.reason.defamation_impersonation_false_information": "Fig na andra pashun os antaana uso tsuiterjet per bajewalt andradjin", "gui.banned.reason.drugs": "<PERSON><PERSON> tsui uwa<PERSON>in", "gui.banned.reason.extreme_violence_or_gore": "Riso per gwir krigzma os netopaike", "gui.banned.reason.false_reporting": "Obamange uso os nai gvir anopeta", "gui.banned.reason.fraud": "Bafig saada os brukzma fu shtof", "gui.banned.reason.generic_violation": "Kundrazma fu ruuru napaksu", "gui.banned.reason.harassment_or_bullying": "Ikebahanu brukena na kriglik tropos", "gui.banned.reason.hate_speech": "<PERSON><PERSON>ahanu os basentakuzma", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, <PERSON><PERSON><PERSON><PERSON>, os b<PERSON><PERSON><PERSON><PERSON>", "gui.banned.reason.imminent_harm_to_person_or_property": "Surupie per arka pashun os perpa ting", "gui.banned.reason.nudity_or_pornography": "Antaana fu riso, punkt, os andra fal fu qiufne", "gui.banned.reason.sexually_inappropriate": "Tsuitepik os hanuvirta oba qizma", "gui.banned.reason.spam_or_advertising": "Barazshtof o<PERSON>", "gui.banned.skin.description": "Imalik kodjariso kundr ruurutumam napaksu. <PERSON>e laki spel na snjano kodjariso, os sentaku neo.\n\nLera plus, os antaa kundrsvar long: %s", "gui.banned.skin.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.banned.title.permanent": "Zefuga Erbanena na Almirai", "gui.banned.title.temporary": "Zefuga na <PERSON> ya<PERSON>ena", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Li plus impla anta, plus shir<PERSON>k zafal dekiti jam na vi.", "gui.chatReport.discard.content": "<PERSON> shke<PERSON>, spadati anopetana au pochta fu sebja.\n<PERSON><PERSON><PERSON> vil shkekso?", "gui.chatReport.discard.discard": "Shkekso au Keshite Anopeta", "gui.chatReport.discard.draft": "Ufne na Tabunversho", "gui.chatReport.discard.return": "<PERSON><PERSON>", "gui.chatReport.discard.title": "Keshite anopeta au pochta?", "gui.chatReport.draft.content": "<PERSON><PERSON><PERSON> beng razkawari na jamfal fu anopetana, os keshiteti per gjenhadji neo versho?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "<PERSON><PERSON>", "gui.chatReport.draft.quittotitle.content": "Vilti beng raz<PERSON>ri os keshite?", "gui.chatReport.draft.quittotitle.title": "Jam jamfal fu anopeta ke keshitena ti, li shke<PERSON>o", "gui.chatReport.draft.title": "<PERSON><PERSON> ergofal fu anopeta?", "gui.chatReport.more_comments": "Bite mahklar slucharjet:", "gui.chatReport.observed_what": "Naze anopeta tsuite afto?", "gui.chatReport.read_info": "<PERSON><PERSON>ope<PERSON>", "gui.chatReport.report_sent_msg": "Anopeta antajena najing. Dankedai!\n\n<PERSON>lani fuvi razlesa mirai, tak moralik ke deki.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON> pochta tsuite ke anopetati", "gui.chatReport.select_reason": "Sentaku <PERSON> fu Anopeta", "gui.chatReport.selected_chat": "%s <PERSON><PERSON><PERSON> per Anopeta", "gui.chatReport.send": "<PERSON><PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "Bite chisaiøze pochta", "gui.chatReport.send.no_reason": "Senta<PERSON> <PERSON> hjerneklaani fu anopetana", "gui.chatReport.send.no_reported_messages": "Bite sentaku (na pol) en pochta, tsui ke anopetati", "gui.chatReport.send.too_many_messages": "<PERSON><PERSON> obamange pochta inekaku", "gui.chatReport.title": "Anopeta Tsuite Spildjin", "gui.chatSelection.context": "<PERSON><PERSON><PERSON> akote sentakjena awen antaajena per shiru hanuvirta tsuiting", "gui.chatSelection.fold": "%s pochta furiena", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s tulla dan chatto made", "gui.chatSelection.message.narrate": "%s hanu dan: %s nataz %s", "gui.chatSelection.selected": "%s/%s pochta sentakena", "gui.chatSelection.title": "Da sentaku hanasuna per anopeta", "gui.continue": "<PERSON><PERSON>", "gui.copy_link_to_clipboard": "<PERSON><PERSON><PERSON> husket<PERSON> made", "gui.days": "%s dag", "gui.done": "Owari", "gui.down": "Una", "gui.entity_tooltip.type": "Fal: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s djikan", "gui.loadingMinecraft": "Lesa Minecraft", "gui.minutes": "%s fun", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s presmi", "gui.narrate.editBox": "%s reformabaksu: %s", "gui.narrate.slider": "%s ataisen", "gui.narrate.tab": "%s lehti", "gui.no": "<PERSON>i", "gui.none": "<PERSON>l", "gui.ok": "Bra", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "<PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "<PERSON><PERSON><PERSON><PERSON> per plus", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Suha...", "gui.recipebook.toggleRecipes.all": "Al sejena", "gui.recipebook.toggleRecipes.blastable": "Se vapatoreudeki ting", "gui.recipebook.toggleRecipes.craftable": "Mahenade<PERSON> se<PERSON>na", "gui.recipebook.toggleRecipes.smeltable": "Se oreudeki ting", "gui.recipebook.toggleRecipes.smokable": "Se djotrenadeki ting", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "<PERSON><PERSON><PERSON> mit Microsoft-zefuga", "gui.socialInteractions.empty_blocked": "Jamnai speldjin ke hestujena", "gui.socialInteractions.empty_hidden": "<PERSON>l spildjin furijena inne chatto", "gui.socialInteractions.hidden_in_chat": "Pocht fu hanutasta %s kara mirai furi", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON> inne chatto", "gui.socialInteractions.narration.hide": "Furi pochta %s kara", "gui.socialInteractions.narration.report": "Anopeta tsuite spildjin %s", "gui.socialInteractions.narration.show": "Se pochta %s kara", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON><PERSON> finna joku spildjin mit afto namae", "gui.socialInteractions.search_hint": "Suha...", "gui.socialInteractions.server_label.multiple": "%s - %s spildjin", "gui.socialInteractions.server_label.single": "%s - %s spildjin", "gui.socialInteractions.show": "Se ine chatto", "gui.socialInteractions.shown_in_chat": "Pocht fu %s kara mirai sejena", "gui.socialInteractions.status_blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "<PERSON>st<PERSON><PERSON><PERSON> - nai narjet", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Furijena - nai na rjet", "gui.socialInteractions.status_offline": "Nai na ryet", "gui.socialInteractions.tab_all": "Al", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Pa<PERSON><PERSON>jet", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> p<PERSON>ta", "gui.socialInteractions.tooltip.report": "An<PERSON><PERSON> tsui spildjin", "gui.socialInteractions.tooltip.report.disabled": "Dwaibma fu anopetara dwaibmanai", "gui.socialInteractions.tooltip.report.no_messages": "Nil jam pochta %s tsui ke anopeta deki", "gui.socialInteractions.tooltip.report.not_reportable": "Tsuite afto speldjin dekinai anopeta grun pochta fu sore dekinai na afto server tsatain shirena", "gui.socialInteractions.tooltip.show": "Se pochta", "gui.stats": "Tsuitelasku", "gui.toMenu": "Suruk servertumam made", "gui.toRealms": "Suryk na Realms-tumam made", "gui.toTitle": "<PERSON><PERSON> made", "gui.toWorld": "Suryk na velt-tumam", "gui.togglable_slot": "Asklik per kolaøze baksunen", "gui.up": "Oba", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Akk", "hanging_sign.edit": "<PERSON><PERSON> shan-k<PERSON><PERSON><PERSON>", "instrument.minecraft.admire_goat_horn": "Fshase", "instrument.minecraft.call_goat_horn": "Anogo<PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "Aistia", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "<PERSON><PERSON> ting", "inventory.hotbarInfo": "Ufne tingtumam na tasta %1$s+%2$s", "inventory.hotbarSaved": "Zeka<PERSON><PERSON>na tintum<PERSON> (gjensada mitt %1$s+%2$s)", "item.canBreak": "Perpaki:", "item.canPlace": "<PERSON><PERSON><PERSON> oba:", "item.canUse.unknown": "Knshirena", "item.color": "Varje: %s", "item.components": "%s tel", "item.disabled": "La<PERSON><PERSON> ting", "item.durability": "Atai stuur: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Akakja cjip", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON><PERSON> cjip mit baksu", "item.minecraft.allay_spawn_egg": "Sintuajaitso f'Apudjin", "item.minecraft.amethyst_shard": "Tel fu murakacha", "item.minecraft.angler_pottery_shard": "Sakanadjin luleklignetel", "item.minecraft.angler_pottery_sherd": "Sakanadjin luleklignetel", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Lunadjin luleklignetel", "item.minecraft.archer_pottery_sherd": "Lunadjin luleklignetel", "item.minecraft.armadillo_scute": "Komuskepone fu komushemysh", "item.minecraft.armadillo_spawn_egg": "Sintuajaitso f'Komuskemysh", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "Hantiwai luleklignetel", "item.minecraft.arms_up_pottery_sherd": "Hantiwai luleklignetel", "item.minecraft.arrow": "Strela", "item.minecraft.axolotl_bucket": "Bad<PERSON>l mit atheksetl", "item.minecraft.axolotl_spawn_egg": "Sintuajaitso f'Atheksetl", "item.minecraft.baked_potato": "Toreukartofel", "item.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON> chipnen mit baksu", "item.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON>", "item.minecraft.bat_spawn_egg": "Sintuajaitso f'Ljetamys", "item.minecraft.bee_spawn_egg": "Sintuajaitso f'Melitta", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Volkodja cjip", "item.minecraft.birch_chest_boat": "Volkodja chip mit baksu", "item.minecraft.black_bundle": "<PERSON><PERSON> festa<PERSON>ban", "item.minecraft.black_dye": "<PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "Honodjinvjetka", "item.minecraft.blaze_spawn_egg": "Sintuajaitso f'Honodjin", "item.minecraft.blue_bundle": "Blau festakaban", "item.minecraft.blue_dye": "Blau faria", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Sintuajaitso f'Boggeta", "item.minecraft.bolt_armor_trim_smithing_template": "Jerkatergo karta", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "Ponenen", "item.minecraft.book": "Libre", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "Staklingje", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Luftsjal-vjetka", "item.minecraft.breeze_spawn_egg": "Sintuajaitso fu Luftsjal", "item.minecraft.brewer_pottery_shard": "Toreudjin luleklignetel", "item.minecraft.brewer_pottery_sherd": "Toreudjin luleklignetel", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "Gdent", "item.minecraft.brown_bundle": "<PERSON><PERSON> festakaban", "item.minecraft.brown_dye": "<PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Sikat", "item.minecraft.bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Festakaban", "item.minecraft.bundle.empty": "Oh<PERSON>", "item.minecraft.bundle.empty.description": "<PERSON><PERSON> har viskena kaban fu ting", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON>o l<PERSON>", "item.minecraft.burn_pottery_sherd": "<PERSON>o l<PERSON>", "item.minecraft.camel_spawn_egg": "Sintuajaitso f'Sangju", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Vjetkakaroten", "item.minecraft.cat_spawn_egg": "Sintuajaitso f'Kot", "item.minecraft.cauldron": "Tofdai", "item.minecraft.cave_spider_spawn_egg": "Sintuajaitso f'Sirangakasjalka", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON><PERSON> tø<PERSON>", "item.minecraft.chainmail_chestplate": "Jerka<PERSON>l-s<PERSON><PERSON>", "item.minecraft.chainmail_helmet": "Jerkatul-pipo", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "Sakuracjip mit baksu", "item.minecraft.chest_minecart": "<PERSON><PERSON><PERSON><PERSON> mit baksu", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Sintuajaitso f'Kuritsa", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "Glinashtof/G<PERSON>ter", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "S<PERSON><PERSON>lein<PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "Badzhel fu shirosakana", "item.minecraft.cod_spawn_egg": "Sintuajaitso f'Shirosakana", "item.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON><PERSON> mit jewaltdado", "item.minecraft.compass": "Do<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Kuhjnena gju", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON>a", "item.minecraft.cooked_mutton": "<PERSON><PERSON>", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON> kuh<PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON> dadonen", "item.minecraft.cow_spawn_egg": "Sintuajaitso f'Gju", "item.minecraft.creaking_spawn_egg": "Sintuajaitso f'Djidodjin", "item.minecraft.creeper_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON> bumshtof", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "Sintuajaitso f'Creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Na strela:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "Culus festakaban", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON> faria", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> c<PERSON>p", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> cjip mit baksu", "item.minecraft.debug_stick": "Zereforma vjetka", "item.minecraft.debug_stick.empty": "%s har nil impla", "item.minecraft.debug_stick.select": "sentakena \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" kara %s made", "item.minecraft.diamond": "Helenapetra", "item.minecraft.diamond_axe": "<PERSON><PERSON> j<PERSON>u", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON>ats<PERSON> na helenaishi", "item.minecraft.diamond_helmet": "<PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON> q<PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON> herus", "item.minecraft.disc_fragment_5": "Tel fu musikkrais", "item.minecraft.disc_fragment_5.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dolphin_spawn_egg": "Sintuajaitso f'Maresvinja", "item.minecraft.donkey_spawn_egg": "Sintuajaitso f'Esel", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Sintuajaitso f'Eridaunjena", "item.minecraft.dune_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "Raz-zam telnen", "item.minecraft.egg": "Jaitso", "item.minecraft.elder_guardian_spawn_egg": "Sintuajaitso f'Gammeljewaltdjin", "item.minecraft.elytra": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.emerald": "Zomorrod", "item.minecraft.enchanted_book": "Taikalibre", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON>", "item.minecraft.end_crystal": "Endpetra", "item.minecraft.ender_dragon_spawn_egg": "Sintuajaitso f'Enderrju", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Sintuajaitso f'Enderdjin", "item.minecraft.endermite_spawn_egg": "Sintuajaitso f'Endermite", "item.minecraft.evoker_spawn_egg": "Sintuajaitso f'Taikantadjin", "item.minecraft.experience_bottle": "Taikaklinye", "item.minecraft.explorer_pottery_shard": "Sughadjin luleklignetel", "item.minecraft.explorer_pottery_sherd": "Sughadjin luleklignetel", "item.minecraft.eye_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "Pjera", "item.minecraft.fermented_spider_eye": "Ereshku me fu kasiyalaka", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "Karta", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Honomjachs<PERSON>la", "item.minecraft.firework_rocket.flight": "Tidatai fu ljeta:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Bamzvezda", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON> smak", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON> bli", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Portokali", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "Muranen", "item.minecraft.firework_star.red": "Ro", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON> fal", "item.minecraft.firework_star.shape.burst": "Zvezdabam", "item.minecraft.firework_star.shape.creeper": "Creeperfal", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON><PERSON> m<PERSON>ch", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON> m<PERSON>", "item.minecraft.firework_star.shape.star": "Zvezdafal", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "Sakanavjet<PERSON>", "item.minecraft.flint": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON> mit jerkat", "item.minecraft.flow_armor_trim_smithing_template": "Jerkatergo karta", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.flow_banner_pattern.desc": "Virta", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Luleklignetel", "item.minecraft.flower_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "Luleklinje", "item.minecraft.fox_spawn_egg": "Sintuajaitso f'Kitsune", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.frog_spawn_egg": "Sintuajaitso f'Baba", "item.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON><PERSON> mit honobet", "item.minecraft.ghast_spawn_egg": "Sintuajaitso f'Ghast", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "Glasbadjeil", "item.minecraft.glistering_melon_slice": "Razkirkas is<PERSON>frauttel", "item.minecraft.globe_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.globe_banner_pattern.desc": "Veltmjacj", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Sintwajaitso f'Kiragira <PERSON>", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_horn": "Œchkenor", "item.minecraft.goat_spawn_egg": "Sintuajaitso f'øchke", "item.minecraft.gold_ingot": "Geltdadonen", "item.minecraft.gold_nugget": "Geltnen", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON> jondu", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "Geltkaroten", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON> beish<PERSON>u", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON> beipipo", "item.minecraft.golden_hoe": "<PERSON><PERSON> piegø<PERSON>oi", "item.minecraft.golden_horse_armor": "Kelt eshkuklea per hengest", "item.minecraft.golden_leggings": "Gelt-jala<PERSON>kle<PERSON>", "item.minecraft.golden_pickaxe": "<PERSON><PERSON>ørmoi", "item.minecraft.golden_shovel": "Keltqoilara", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON><PERSON> festa<PERSON>ban", "item.minecraft.gray_dye": "<PERSON><PERSON>ia", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Midori festakaban", "item.minecraft.green_dye": "Midori faria", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "Sintuajaitso f'zihadjin", "item.minecraft.gunpowder": "Bamsokeri", "item.minecraft.guster_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.guster_banner_pattern.desc": "Luftnen", "item.minecraft.guster_banner_pattern.new": "Luftnen Ishuparjad fu flakka", "item.minecraft.guster_pottery_sherd": "Luftnen klignetel", "item.minecraft.happy_ghast_spawn_egg": "Sintuajaitso f'Ghastnen", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Ker fu mare", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON> l<PERSON>", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON> l<PERSON>", "item.minecraft.heartbreak_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heartbreak_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hoglin_spawn_egg": "Sintuajaitso f'Hoglin", "item.minecraft.honey_bottle": "M<PERSON>lt<PERSON><PERSON>l", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON><PERSON> mit sadantating", "item.minecraft.horse_spawn_egg": "Sintuajaitso f'Hengest", "item.minecraft.host_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.husk_spawn_egg": "Sintuajaitso f'Utenshal", "item.minecraft.ink_sac": "Kurokabang", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON> jondu", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON> tø<PERSON>", "item.minecraft.iron_chestplate": "Jerkat es<PERSON>kushat<PERSON>", "item.minecraft.iron_golem_spawn_egg": "Sintuajaitso f'Jerkatrobotto", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON> pipo", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON><PERSON>t eshkuklea per hengest", "item.minecraft.iron_ingot": "Jerkatdadonen", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON> j<PERSON>", "item.minecraft.iron_nugget": "Jerkatnen", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Pluidasos cjip", "item.minecraft.jungle_chest_boat": "Pluidasos cjip mit baksu", "item.minecraft.knowledge_book": "Shirulibre", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Badzhel fu jhotishke", "item.minecraft.lead": "Gutsjonuito", "item.minecraft.leather": "Gjukodja", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "Gju<PERSON>d<PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Gjukodja es<PERSON>a per hengest", "item.minecraft.leather_leggings": "Gjukodja jala<PERSON>a", "item.minecraft.light_blue_bundle": "Sini festakaban", "item.minecraft.light_blue_dye": "<PERSON>i faria", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON> festa<PERSON>ban", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON> festakaban", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Polbamiskje", "item.minecraft.lingering_potion.effect.awkward": "Polbamishke trelo", "item.minecraft.lingering_potion.effect.empty": "Polbamisk<PERSON> kafan", "item.minecraft.lingering_potion.effect.fire_resistance": "Polbamiskje ho<PERSON>", "item.minecraft.lingering_potion.effect.harming": "Polbamiskje arka", "item.minecraft.lingering_potion.effect.healing": "Polbamiskje reforma", "item.minecraft.lingering_potion.effect.infested": "Polbamiskje fu flan", "item.minecraft.lingering_potion.effect.invisibility": "Polbamiskje se<PERSON>", "item.minecraft.lingering_potion.effect.leaping": "Polbamiskje per paldai", "item.minecraft.lingering_potion.effect.levitation": "Polbamiskje per ljeta", "item.minecraft.lingering_potion.effect.luck": "Polbamiskje per braudacji", "item.minecraft.lingering_potion.effect.mundane": "Polbamis<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "Polbamiskje per nahtse", "item.minecraft.lingering_potion.effect.oozing": "Polbamiskje per umokri", "item.minecraft.lingering_potion.effect.poison": "Polbamiskje per tuin", "item.minecraft.lingering_potion.effect.regeneration": "Polbamiskje per zdorvabli", "item.minecraft.lingering_potion.effect.slow_falling": "Polbamiskje per hiidasspada", "item.minecraft.lingering_potion.effect.slowness": "Polbamiskje per hiidas", "item.minecraft.lingering_potion.effect.strength": "Polbamiskje per djong", "item.minecraft.lingering_potion.effect.swiftness": "Polbamiskje per bystrazma", "item.minecraft.lingering_potion.effect.thick": "Polbamishke m<PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Polbamiskje fu breshkamestari", "item.minecraft.lingering_potion.effect.water": "Polbamiskje", "item.minecraft.lingering_potion.effect.water_breathing": "Polbamiskje per ishkehengi", "item.minecraft.lingering_potion.effect.weakness": "Polbamiskje per shvants", "item.minecraft.lingering_potion.effect.weaving": "Polbamiskje per raznuito", "item.minecraft.lingering_potion.effect.wind_charged": "Polbamiskje per luftzeus", "item.minecraft.llama_spawn_egg": "Sintuajaitso f'Lama", "item.minecraft.lodestone_compass": "Dokotoke per plasdado", "item.minecraft.mace": "Bulavá", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON> festakaban", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON>ia", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magma_cube_spawn_egg": "Sintuajaitso f'Zjotsewas", "item.minecraft.mangrove_boat": "Mangrc<PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> mit baksu", "item.minecraft.map": "Ohare karta", "item.minecraft.melon_seeds": "Ishkefrautpie", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON> fu nyu", "item.minecraft.minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Shahtadjin luleklignetel", "item.minecraft.miner_pottery_sherd": "Shahtadjin luleklignetel", "item.minecraft.mojang_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.mojang_banner_pattern.desc": "<PERSON>g", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "Sintuajaitso f'Mooshroom", "item.minecraft.mourner_pottery_shard": "Nakudjin luleklignetel", "item.minecraft.mourner_pottery_sherd": "Nakudjin luleklignetel", "item.minecraft.mule_spawn_egg": "Sintuajaitso f'Hengesel", "item.minecraft.mushroom_stew": "Manitarist<PERSON>", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - dadora", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - kot", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chiiip", "item.minecraft.music_disc_creator": "Music Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (Liidbaksu)", "item.minecraft.music_disc_far": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_far.desc": "C418 - prapa", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mall.desc": "C418 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON>", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - <PERSON><PERSON><PERSON>", "item.minecraft.music_disc_precipice": "Music Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - vent", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "Komuske fu Beihjen", "item.minecraft.nether_brick": "Nether gdent", "item.minecraft.nether_star": "Nethersfezda", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON><PERSON> jondu", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON> tøvel", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON> es<PERSON>kus<PERSON>", "item.minecraft.netherite_helmet": "Netherit pipo", "item.minecraft.netherite_hoe": "Netherit piegørmoi", "item.minecraft.netherite_ingot": "<PERSON><PERSON><PERSON> dadonen", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON> j<PERSON>", "item.minecraft.netherite_pickaxe": "Nethershtof puangørmoi", "item.minecraft.netherite_scrap": "<PERSON>herit myl", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON> qoilara", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON><PERSON> herus", "item.minecraft.netherite_upgrade_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> mit baksu", "item.minecraft.ocelot_spawn_egg": "Sintuajaitso f'Baumkot", "item.minecraft.ominous_bottle": "Badjel fu nafakoro", "item.minecraft.ominous_trial_key": "Iskatdaiklucj fu nafakoro", "item.minecraft.orange_bundle": "Portokali festakaban", "item.minecraft.orange_dye": "Portokali faria", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "Riso", "item.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON>-sjip", "item.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON>-sjip mit baksu", "item.minecraft.panda_spawn_egg": "Sintuajaitso f'Pantaa", "item.minecraft.paper": "<PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Sintuajaitso f'Hanufogel", "item.minecraft.phantom_membrane": "Godzha f'On", "item.minecraft.phantom_spawn_egg": "Sintuajaitso f'On", "item.minecraft.pig_spawn_egg": "Sintuajaitso f'Sfinja", "item.minecraft.piglin_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "Sintuajaitso f'Djongpiglin", "item.minecraft.piglin_spawn_egg": "Sintuajaitso f'Piglin", "item.minecraft.pillager_spawn_egg": "Sintuajaitso f'Poliskrigdjin", "item.minecraft.pink_bundle": "Roza festakaban", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Klingjeruti", "item.minecraft.pitcher_pod": "Piekaban fu klingjeruti", "item.minecraft.plenty_pottery_shard": "Lagom luleklignetel", "item.minecraft.plenty_pottery_sherd": "Lagom luleklignetel", "item.minecraft.poisonous_potato": "Tuinkartofel", "item.minecraft.polar_bear_spawn_egg": "Sintuajaitso f'Upashkarhu", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON><PERSON><PERSON> bam<PERSON>", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "Taikaiskje", "item.minecraft.potion.effect.awkward": "Taikaishke trelo", "item.minecraft.potion.effect.empty": "Tai<PERSON>sh<PERSON> kafan", "item.minecraft.potion.effect.fire_resistance": "Taikaiskje honoziha", "item.minecraft.potion.effect.harming": "Taikaiskje arka", "item.minecraft.potion.effect.healing": "Taikaiskje reforma", "item.minecraft.potion.effect.infested": "Taikaiskje fu flan", "item.minecraft.potion.effect.invisibility": "Taikaiskje sena<PERSON>na", "item.minecraft.potion.effect.leaping": "Taikaiskje paldai", "item.minecraft.potion.effect.levitation": "Taikaiskje ljeta", "item.minecraft.potion.effect.luck": "Taikaiskje braudacji", "item.minecraft.potion.effect.mundane": "Taikaishke <PERSON>", "item.minecraft.potion.effect.night_vision": "Taikaiskje nahtse", "item.minecraft.potion.effect.oozing": "Taikaiskje fu umokrizma", "item.minecraft.potion.effect.poison": "Taikaiskje tuin", "item.minecraft.potion.effect.regeneration": "Taikaiskje gjenzdorva", "item.minecraft.potion.effect.slow_falling": "Taikaiskje ljetaspada", "item.minecraft.potion.effect.slowness": "Taikai<PERSON><PERSON> hidas", "item.minecraft.potion.effect.strength": "Taikaiskje djon", "item.minecraft.potion.effect.swiftness": "Taikaiskje bistra", "item.minecraft.potion.effect.thick": "Taikaishke festadai", "item.minecraft.potion.effect.turtle_master": "Taikaiskje grenzajewalt", "item.minecraft.potion.effect.water": "Iskjeklinje", "item.minecraft.potion.effect.water_breathing": "Taikaiskje iskjeluft", "item.minecraft.potion.effect.weakness": "Taikaiskje sfjanc", "item.minecraft.potion.effect.weaving": "Taikaiskje fu raznuito", "item.minecraft.potion.effect.wind_charged": "Taikaiskje fu luftzeus", "item.minecraft.pottery_shard_archer": "Lunadjin luleklignetel", "item.minecraft.pottery_shard_arms_up": "Hantiwai luleklignetel", "item.minecraft.pottery_shard_prize": "Jingbaksu luleklignetel", "item.minecraft.pottery_shard_skull": "Poneatama lule<PERSON>", "item.minecraft.powder_snow_bucket": "Badzhel fu alk upash", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON> telnen", "item.minecraft.prize_pottery_shard": "Jingbaksu luleklignetel", "item.minecraft.prize_pottery_sherd": "Jingbaksu luleklignetel", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON><PERSON> mitt rup<PERSON><PERSON>na", "item.minecraft.pufferfish_spawn_egg": "Sintuajaitso f'Rupnesakana", "item.minecraft.pumpkin_pie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "Achorfrautpie", "item.minecraft.purple_bundle": "<PERSON><PERSON><PERSON> festakaban", "item.minecraft.purple_dye": "Muranen faria", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON> kacha", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "Kunelikodja", "item.minecraft.rabbit_spawn_egg": "Sintuajaitso f'Kuneli", "item.minecraft.rabbit_stew": "Kunelista", "item.minecraft.raiser_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "Sintuajaitso f'Erviskenadjin", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON> kupkat", "item.minecraft.raw_gold": "<PERSON><PERSON> uten gotova", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON> jerkat", "item.minecraft.recovery_compass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_bundle": "Ros festa<PERSON>ban", "item.minecraft.red_dye": "Ro faria", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Redstone", "item.minecraft.resin_brick": "Mjaltpetra gdjent", "item.minecraft.resin_clump": "Mjaltpetramjah", "item.minecraft.rib_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.saddle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "Badzhel fu honosakana", "item.minecraft.salmon_spawn_egg": "Sintuajaitso f'Honoosakana", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.scute": "Komuskepone", "item.minecraft.sentry_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "Baumnen luleklignetel", "item.minecraft.sheaf_pottery_sherd": "Baumnen luleklignetel", "item.minecraft.shears": "Tsambliznets", "item.minecraft.sheep_spawn_egg": "Sintuajaitso f'Lammas", "item.minecraft.shelter_pottery_shard": "Vomi l<PERSON>", "item.minecraft.shelter_pottery_sherd": "Vomi l<PERSON>", "item.minecraft.shield": "Komuske", "item.minecraft.shield.black": "<PERSON><PERSON>om<PERSON>", "item.minecraft.shield.blue": "Blau komuske", "item.minecraft.shield.brown": "<PERSON><PERSON> kom<PERSON>e", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON> kom<PERSON>e", "item.minecraft.shield.gray": "<PERSON><PERSON>om<PERSON>e", "item.minecraft.shield.green": "<PERSON><PERSON> komuske", "item.minecraft.shield.light_blue": "Sini komuske", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON> kom<PERSON>e", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON> komuske", "item.minecraft.shield.orange": "Portokali komuske", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON> komuske", "item.minecraft.shield.purple": "Muranen komuske", "item.minecraft.shield.red": "<PERSON><PERSON> komuske", "item.minecraft.shield.white": "<PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON>", "item.minecraft.shulker_shell": "Shulkergodja", "item.minecraft.shulker_spawn_egg": "Sintuajaitso f'<PERSON><PERSON>er", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "Sintuajaitso f'Sokeribage", "item.minecraft.skeleton_horse_spawn_egg": "Sintuajaitso f'Ponehengest", "item.minecraft.skeleton_spawn_egg": "Sintuajaitso f'Ponedjin", "item.minecraft.skull_banner_pattern": "Ishuparjad fu flakka", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON> bumshtof", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Poneatama lule<PERSON>", "item.minecraft.skull_pottery_sherd": "Poneatama lule<PERSON>", "item.minecraft.slime_ball": "<PERSON><PERSON><PERSON> m<PERSON>", "item.minecraft.slime_spawn_egg": "Sintuajaitso f'Gymi", "item.minecraft.smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "Jam na:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON>si dadonen os petra", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "<PERSON><PERSON><PERSON> kom<PERSON>", "item.minecraft.smithing_template.armor_trim.ingredients": "Dadonen au petra", "item.minecraft.smithing_template.ingredients": "Trengena:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Nasii komusketel os kriigbruk os brukting ka na helenishifal", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Dadonen fu Netherit", "item.minecraft.smithing_template.upgrade": "Plusbrøze: ", "item.minecraft.sniffer_spawn_egg": "Sintuajaitso f'Nioidjin", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON>", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Sintuajaitso f'Upashun", "item.minecraft.snowball": "Upasmjacj", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "Sintuajaitso f'Kasjalaka", "item.minecraft.spire_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "Bamiskje", "item.minecraft.splash_potion.effect.awkward": "Bamishke trelo", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "Bamisk<PERSON>", "item.minecraft.splash_potion.effect.harming": "Bamiskje arka", "item.minecraft.splash_potion.effect.healing": "Bamiskje reforma", "item.minecraft.splash_potion.effect.infested": "Flan-bamiskje", "item.minecraft.splash_potion.effect.invisibility": "Bamiskje <PERSON>", "item.minecraft.splash_potion.effect.leaping": "Bamisk<PERSON> paldai", "item.minecraft.splash_potion.effect.levitation": "Bamiskje ljeta", "item.minecraft.splash_potion.effect.luck": "Bamiskje <PERSON>", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.night_vision": "Bamiskje <PERSON>", "item.minecraft.splash_potion.effect.oozing": "Umokri-bamiskje", "item.minecraft.splash_potion.effect.poison": "Bamisk<PERSON> tuin", "item.minecraft.splash_potion.effect.regeneration": "Bamiskje gjenzdorva", "item.minecraft.splash_potion.effect.slow_falling": "Bamiskje ljetaspada", "item.minecraft.splash_potion.effect.slowness": "Ba<PERSON><PERSON><PERSON> hid<PERSON>", "item.minecraft.splash_potion.effect.strength": "Bamiskje djon", "item.minecraft.splash_potion.effect.swiftness": "Bamiskje bistra", "item.minecraft.splash_potion.effect.thick": "Bamishke festadai", "item.minecraft.splash_potion.effect.turtle_master": "Bamiskje grenzajewalt", "item.minecraft.splash_potion.effect.water": "Bamiskje", "item.minecraft.splash_potion.effect.water_breathing": "Bamiskje <PERSON>", "item.minecraft.splash_potion.effect.weakness": "Bamiskje sfjanc", "item.minecraft.splash_potion.effect.weaving": "Rjetnafa-bamiskje", "item.minecraft.splash_potion.effect.wind_charged": "Bamiskje fu luftpulap", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON><PERSON> mit baksu", "item.minecraft.spyglass": "Prapaseting", "item.minecraft.squid_spawn_egg": "Sintuajaitso f'Majakahjen", "item.minecraft.stick": "Vjetka", "item.minecraft.stone_axe": "<PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON>", "item.minecraft.stone_sword": "<PERSON><PERSON> herus", "item.minecraft.stray_spawn_egg": "Sintuajaitso f'Milupone", "item.minecraft.strider_spawn_egg": "Sintuajeitso fu Jalakadjin", "item.minecraft.string": "Nuito", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Sussta", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON> fra<PERSON>", "item.minecraft.tadpole_bucket": "Badzhel fu jirinos", "item.minecraft.tadpole_spawn_egg": "Sintuajaitso f'Jirinos", "item.minecraft.tide_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "Taikastrela", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON> mitt ta<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.empty": "Strela fu kafan", "item.minecraft.tipped_arrow.effect.fire_resistance": "Strela fu honokømuske", "item.minecraft.tipped_arrow.effect.harming": "Strela fu arka", "item.minecraft.tipped_arrow.effect.healing": "Strela fu reforma", "item.minecraft.tipped_arrow.effect.infested": "<PERSON>rela fu flan", "item.minecraft.tipped_arrow.effect.invisibility": "Strela fu unsejenadeki", "item.minecraft.tipped_arrow.effect.leaping": "Strela fu paldai", "item.minecraft.tipped_arrow.effect.levitation": "Strela fu ljeta", "item.minecraft.tipped_arrow.effect.luck": "Strela fu braudachi", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON> mitt ta<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "Strela fu nahtsedeki", "item.minecraft.tipped_arrow.effect.oozing": "Strela fu umokri", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON>a fu tuin", "item.minecraft.tipped_arrow.effect.regeneration": "Strela fu gjenzdorva", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON>rela fu hidasspada", "item.minecraft.tipped_arrow.effect.slowness": "Strela fu hidas", "item.minecraft.tipped_arrow.effect.strength": "Strela fu djong", "item.minecraft.tipped_arrow.effect.swiftness": "Strela fu bistra", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON> mitt ta<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "Strela fu grenzajewalt", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON> fu ishkejit", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON>rela fu ish<PERSON>hen<PERSON>deki", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON> fu shvants", "item.minecraft.tipped_arrow.effect.weaving": "Strela fu raznuito", "item.minecraft.tipped_arrow.effect.wind_charged": "Strela fu luftpulap", "item.minecraft.tnt_minecart": "<PERSON><PERSON><PERSON><PERSON> mit bambam", "item.minecraft.torchflower_seeds": "Kirkasblume pie", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trader_llama_spawn_egg": "Sintuajaitso f'Hoklama", "item.minecraft.trial_key": "Iskatklucj", "item.minecraft.trident": "Sardeshkadai", "item.minecraft.tropical_fish": "Sakana fu veltkraisdai", "item.minecraft.tropical_fish_bucket": "Badzhel fu sakana fu mellan<PERSON>krais", "item.minecraft.tropical_fish_spawn_egg": "Sintuajaitso f'Sakana fu mellanveltkrais", "item.minecraft.turtle_helmet": "Breshkakomuske", "item.minecraft.turtle_scute": "Komuskepone fu breshka", "item.minecraft.turtle_spawn_egg": "Sintuajaitso f'Breshka", "item.minecraft.vex_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "Sintuajaitso f'Rovodjin", "item.minecraft.villager_spawn_egg": "Sintuajaitso f'Statdjin", "item.minecraft.vindicator_spawn_egg": "Sintuajaitso f'Warusadadjin", "item.minecraft.wandering_trader_spawn_egg": "Sintuajaitso f'Razskei hokdjin", "item.minecraft.ward_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Sintuajaitso f'Erdarmdjin", "item.minecraft.warped_fungus_on_a_stick": "Erstranitari na vjetka", "item.minecraft.water_bucket": "Is<PERSON>ke<PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "<PERSON><PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON>", "item.minecraft.white_bundle": "<PERSON><PERSON> festa<PERSON>ban", "item.minecraft.white_dye": "<PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "Jerka<PERSON>ba<PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Luftbamba", "item.minecraft.witch_spawn_egg": "Sintuajaitso f'Taikadjin", "item.minecraft.wither_skeleton_spawn_egg": "Sintuajaitso f'Witherponedjin", "item.minecraft.wither_spawn_egg": "Sintuajaitso f'Wither", "item.minecraft.wolf_armor": "Eshkuklea fu lupo", "item.minecraft.wolf_spawn_egg": "Sintuajaitso f'Lupo", "item.minecraft.wooden_axe": "<PERSON>um jondu", "item.minecraft.wooden_hoe": "Baum piegø<PERSON>oi", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON>oi", "item.minecraft.wooden_shovel": "Baum qoilara", "item.minecraft.wooden_sword": "Baum herus", "item.minecraft.writable_book": "Libre mit pjera", "item.minecraft.written_book": "Kakena libre", "item.minecraft.yellow_bundle": "<PERSON><PERSON> f<PERSON>", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Sintuajaitso f'Zoglin", "item.minecraft.zombie_horse_spawn_egg": "Sintuajaitso f'Odaremhengest", "item.minecraft.zombie_spawn_egg": "Sintuajaitso f'Odarem", "item.minecraft.zombie_villager_spawn_egg": "Sintuajaitso f'Odaremstatdjin", "item.minecraft.zombified_piglin_spawn_egg": "Sintuajaitso f'Odarempiglin", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "Na trag:", "item.modifiers.chest": "Koske na melannetopa:", "item.modifiers.feet": "Koske na jalakanen:", "item.modifiers.hand": "When held:", "item.modifiers.head": "Koske na atama:", "item.modifiers.legs": "Koske na jalaka:", "item.modifiers.mainhand": "Li ine hantnen:", "item.modifiers.offhand": "Li ine nis hantnen:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s namaenen", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "<PERSON><PERSON><PERSON> afto ting deki mahadui bruuk iew<PERSON>ko", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON><PERSON> li širunaidu alting iine!", "item.unbreakable": "Perpenakinai", "itemGroup.buildingBlocks": "<PERSON>o per huomi", "itemGroup.coloredBlocks": "<PERSON><PERSON> mit varge", "itemGroup.combat": "Harza/Krig", "itemGroup.consumables": "Ting ka deki nam", "itemGroup.crafting": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.foodAndDrink": "<PERSON> au glug", "itemGroup.functional": "<PERSON><PERSON>", "itemGroup.hotbar": "<PERSON><PERSON><PERSON><PERSON> tin<PERSON>", "itemGroup.ingredients": "<PERSON><PERSON>gena", "itemGroup.inventory": "Vonatropos harena", "itemGroup.natural": "<PERSON><PERSON><PERSON>", "itemGroup.op": "Brukting per jewalddjin", "itemGroup.redstone": "Redstone dado", "itemGroup.search": "<PERSON><PERSON> ting", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Brukting au -bma", "item_modifier.unknown": "<PERSON><PERSON><PERSON> kawarina fu ting: %s", "jigsaw_block.final_state": "Bli:", "jigsaw_block.generate": "Razmaha", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "<PERSON><PERSON>", "jigsaw_block.joint_label": "Nasiitropos:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON> ka jigsawdado tatsu", "jigsaw_block.levels": "Mestariatai: %s", "jigsaw_block.name": "Namae:", "jigsaw_block.placement_priority": "Jewaltparjad fu nasi:", "jigsaw_block.placement_priority.tooltip": "Koske afto Festatel dado tsunaga andra, afto tte parjad nake tuo tel ergoena per tsunaga na plusstor zdanie.\n\nTelara ti ergoena na spadalik parjad. Jewaltparjad fu innegutjo sentaku jingdjin samadjin kara.", "jigsaw_block.pool": "Sentakudekjena:", "jigsaw_block.selection_priority": "Jewaltparjad fu sentaku:", "jigsaw_block.selection_priority.tooltip": "Koske mamatel ergoena per tsun<PERSON><PERSON>, afto e parjad nake afto festatel iskat tsunaga na vilena tel.\n\nFestatel ergoena ti na spadalik parjad, au jingdjin fu sama<PERSON><PERSON> sentakena na zari.", "jigsaw_block.target": "<PERSON>ae fu jenadjin:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (Liidbaksu)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON>", "key.attack": "Harza/Perpa", "key.back": "<PERSON><PERSON>a hina", "key.categories.creative": "Is<PERSON><PERSON><PERSON><PERSON>", "key.categories.gameplay": "Spiltropos", "key.categories.inventory": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.misc": "<PERSON>r", "key.categories.movement": "U<PERSON><PERSON>", "key.categories.multiplayer": "<PERSON><PERSON>", "key.categories.ui": "<PERSON><PERSON><PERSON><PERSON>", "key.chat": "<PERSON><PERSON> chatto", "key.command": "<PERSON><PERSON>-chatt<PERSON>", "key.drop": "<PERSON>ada sentakena ting", "key.forward": "<PERSON><PERSON>a fura", "key.fullscreen": "Sentaku he<PERSON>", "key.hotbar.1": "1s hant<PERSON><PERSON>m plas", "key.hotbar.2": "2s hant<PERSON><PERSON><PERSON> plas", "key.hotbar.3": "3s hant<PERSON><PERSON>m plas", "key.hotbar.4": "4s hant<PERSON><PERSON>m plas", "key.hotbar.5": "5s hant<PERSON><PERSON>m plas", "key.hotbar.6": "6s hant<PERSON><PERSON><PERSON> plas", "key.hotbar.7": "7s hant<PERSON><PERSON>m plas", "key.hotbar.8": "8s hant<PERSON><PERSON>m plas", "key.hotbar.9": "8s hant<PERSON><PERSON>m plas", "key.inventory": "<PERSON><PERSON>/<PERSON><PERSON>i har<PERSON>na", "key.jump": "Pal", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\\\", "key.keyboard.backspace": "Ke<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON> tasta", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.down": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.equal": "=", "key.keyboard.escape": "Shkekso", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Huomitas<PERSON>", "key.keyboard.insert": "Mellantasta", "key.keyboard.keypad.0": "Laskutasta 0", "key.keyboard.keypad.1": "Laskutasta 1", "key.keyboard.keypad.2": "Laskutasta 2", "key.keyboard.keypad.3": "Laskutasta 3", "key.keyboard.keypad.4": "Laskutasta 4", "key.keyboard.keypad.5": "Laskutasta 5", "key.keyboard.keypad.6": "Laskutasta 6", "key.keyboard.keypad.7": "Laskutasta 7", "key.keyboard.keypad.8": "Laskutasta 8", "key.keyboard.keypad.9": "Laskutasta 9", "key.keyboard.keypad.add": "Laskutasta +", "key.keyboard.keypad.decimal": "Laskutasta Pik", "key.keyboard.keypad.divide": "Laskutasta /", "key.keyboard.keypad.enter": "Laskutas<PERSON> Nasi", "key.keyboard.keypad.equal": "Laskutasta =", "key.keyboard.keypad.multiply": "Laskutasta *", "key.keyboard.keypad.subtract": "Laskutasta -", "key.keyboard.left": "<PERSON><PERSON><PERSON> strela", "key.keyboard.left.alt": "Ljeva Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ljeva Ctrl", "key.keyboard.left.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.left.win": "<PERSON><PERSON><PERSON>", "key.keyboard.menu": "Senta<PERSON><PERSON>am", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Numlocktasta", "key.keyboard.page.down": "Zalehti-tasta", "key.keyboard.page.up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.pause": "<PERSON><PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.right.alt": "<PERSON><PERSON>", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON><PERSON>", "key.keyboard.right.shift": "<PERSON><PERSON>", "key.keyboard.right.win": "<PERSON><PERSON>", "key.keyboard.scroll.lock": "O<PERSON>unashkoifesta", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Oharetasta", "key.keyboard.tab": "Pitka-oharetasta", "key.keyboard.unknown": "<PERSON>i brukena", "key.keyboard.up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.world.1": "Velt 1", "key.keyboard.world.2": "Velt 2", "key.left": "Ugoki ljeva made", "key.loadToolbarActivator": "Dwaibmalese tingtumam", "key.mouse": "Rër %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON>", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "<PERSON><PERSON> pre<PERSON>i", "key.pickItem": "<PERSON><PERSON><PERSON> dado", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "Quick Actions", "key.right": "<PERSON><PERSON><PERSON> migi made", "key.saveToolbarActivator": "Tingtumamufne", "key.screenshot": "<PERSON><PERSON>", "key.smoothCamera": "Sentaku filmlik metropos", "key.sneak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.socialInteractions": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.spectatorOutlines": "<PERSON><PERSON><PERSON><PERSON> spild<PERSON> (sedjin)", "key.sprint": "Djinsai", "key.swapOffhand": "<PERSON><PERSON><PERSON><PERSON>", "key.togglePerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> setropos", "key.use": "<PERSON>ruk ting/nasii dado", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forums", "known_server_link.news": "News", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Status", "known_server_link.support": "Support", "known_server_link.website": "<PERSON><PERSON><PERSON>", "lanServer.otherPlayers": "Sentakena fu andrad<PERSON>", "lanServer.port": "Zemado-<PERSON><PERSON>", "lanServer.port.invalid": "<PERSON><PERSON><PERSON> warui.\n<PERSON><PERSON> baksu musti oh<PERSON>, os innehar lasku mellan 1024 kara 65535 made.", "lanServer.port.invalid.new": "<PERSON>onen warui.\n<PERSON><PERSON><PERSON><PERSON><PERSON> musti oh<PERSON>, os innehar lasku mellan %s kara %s made.", "lanServer.port.unavailable": "<PERSON><PERSON><PERSON>.\nDa bruknai kakubaksu os kaku lasku mellan 1024 au 65535.", "lanServer.port.unavailable.new": "<PERSON><PERSON><PERSON>.\nDa bruknai kakubaksu os kaku lasku mellan %s au %s.", "lanServer.scanning": "Razsuhe spil na LAN fu du", "lanServer.start": "Hadji bloge velt na LAN", "lanServer.title": "LAN Velt", "language.code": "qpv", "language.name": "Viossa", "language.region": "<PERSON><PERSON><PERSON>", "lectern.take_book": "Saada libre", "loading.progress": "%s%%", "mco.account.privacy.info": "Lese plus tsui Mojang au ruru oba jinsyzma", "mco.account.privacy.info.button": "Lesa plus oba GDPR", "mco.account.privacy.information": "Mojang bruk joku ruuru per kømuske lapsi au jinsyzma fuhe na uslova Children’s Online Privacy Protection Act (COPPA) au General Data Protection Regulation (GDPR).\n\n<PERSON> du lapsi, r<PERSON>hin fu du musti anlaki per bruk Realms zekao.", "mco.account.privacyinfo": "Mojang bruk joku ruuru per kømuske lapsi au jinsyzma fuhe na uslova Children’s Online Privacy Protection Act (COPPA) au General Data Protection Regulation (GDPR).\n\n<PERSON> du lapsi, r<PERSON>hin fu du musti anlaki per bruk Realms zekao.\n\nLi du har plusgammel Minecraft zekao (du bruk brukdjinnamae per zekoni) sitt du mus kawari afto zekao na Mojang zekao made per bruk Realms.", "mco.account.update": "<PERSON><PERSON>", "mco.activity.noactivity": "Nil suruna %s dag kara", "mco.activity.title": "Slucharjet na spildjin", "mco.backup.button.download": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.reset": "Gjensintwa velt", "mco.backup.button.restore": "Gyensintua", "mco.backup.button.upload": "<PERSON><PERSON><PERSON> velt", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "Zihaufne (%s)", "mco.backup.entry.description": "Tsuite", "mco.backup.entry.enabledPack": "<PERSON><PERSON> bruk <PERSON>n", "mco.backup.entry.gameDifficulty": "Haasteatai fu spel", "mco.backup.entry.gameMode": "Speltropos", "mco.backup.entry.gameServerVersion": "Versholasku fu server", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "Namae fu Pol<PERSON>l", "mco.backup.entry.undefined": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>na", "mco.backup.entry.uploaded": "<PERSON><PERSON><PERSON>", "mco.backup.entry.worldType": "Veltfal", "mco.backup.generate.world": "Sintwa velt", "mco.backup.info.title": "<PERSON><PERSON> pu dan-zih<PERSON><PERSON>ne", "mco.backup.narration": "Zihaufne na %s", "mco.backup.nobackups": "Afto Realm ima nai har zihaufne.", "mco.backup.restoring": "Ima reforma Realm fu du", "mco.backup.unknown": "KNSHIRENA", "mco.brokenworld.download": "Zesada", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "<PERSON>te pogjen os sentaku ander velt.", "mco.brokenworld.message.line2": "<PERSON><PERSON> deki sentaku zesada velt to<PERSON><PERSON><PERSON> made.", "mco.brokenworld.minigame.title": "Afto spilnen im<PERSON>ra d<PERSON>", "mco.brokenworld.nonowner.error": "Bitte advent jevaltdjin fu Realm gjensintwa velt", "mco.brokenworld.nonowner.title": "<PERSON><PERSON><PERSON> poga<PERSON>l", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "<PERSON><PERSON><PERSON>", "mco.brokenworld.title": "<PERSON><PERSON> velt fu du ima dwaibmanai", "mco.client.incompatible.msg.line1": "Spelabma mit Realms lagomnai.", "mco.client.incompatible.msg.line2": "Bitte bruk lesteneo versio fu Minecraft.", "mco.client.incompatible.msg.line3": "Realm nai dvaibma na wigel fallasku.", "mco.client.incompatible.title": "Lgaomnai spelabma!", "mco.client.outdated.stable.version": "Versho fu spelabma (%s) dwaibmanai na Realms.\n\nBitte bruk lesteneo versio fu Minecraft.", "mco.client.unsupported.snapshot.version": "Versho fu spelabma (%s) dwaibmanai na Realms.\n\nRealms bruk-dekinai na afto vershonen.", "mco.compatibility.downgrade": "Gammelfal made", "mco.compatibility.downgrade.description": "Afto velt dantid na %ssfal speljena; a du ima na %s spel. Hina-rømkawari fu velt tabun erperpati - de<PERSON>ai erzettai, ke dekiti lesena os ke dwaibmati bra. \n\nZihaufne mirai na \"Velt-zihaufne\". Bite suryklesa li trengena.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "Danversho fu spelabma brukena na afto velt zettainai. Li veltversho <PERSON>, zihaufne mirai bli na \"Velt-zihaufne\".", "mco.compatibility.unverifiable.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>i", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Afto velt dantid na %ssfal speljena; a du ima har %s.\n\nZihaufne mirai na \"Velt-zihaufne\". Bite suryklesa li trengena.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "<PERSON><PERSON><PERSON> vil plusneøze afto velt?", "mco.configure.current.minigame": "<PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Sluchavirta fu spildjin imangoro la<PERSON>ai", "mco.configure.world.backup": "Velt-zihaufne", "mco.configure.world.buttons.activity": "Sluchara na spildjin", "mco.configure.world.buttons.close": "Kini <PERSON>", "mco.configure.world.buttons.delete": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "Owari", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "<PERSON><PERSON> spi<PERSON>", "mco.configure.world.buttons.moreoptions": "Plus sentakuna", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Auki <PERSON>", "mco.configure.world.buttons.options": "Sentakuna fu velt", "mco.configure.world.buttons.players": "Spildjin", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Gjensintwa velt", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON> s<PERSON>", "mco.configure.world.close.question.line1": "Realm fu du blimir erkinena.", "mco.configure.world.close.question.line2": "Du zettai vil ende suru?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Na kini Realm...", "mco.configure.world.commandBlocks": "Jewaltidwai<PERSON><PERSON> dado", "mco.configure.world.delete.button": "Keshite Realm", "mco.configure.world.delete.question.line1": "Realm fu du ertatsumir keshitena", "mco.configure.world.delete.question.line2": "Du zettai vil ende suru?", "mco.configure.world.description": "Realm tsuite", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON> sentakena velt na <PERSON>l, joku sentakena de<PERSON>ai", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON> sentakena velt na vimifal, joku sentakena de<PERSON>ai", "mco.configure.world.edit.subscreen.inspiration": "<PERSON><PERSON> sentakena velt na guaufal, joku sentakena de<PERSON>ai", "mco.configure.world.forceGameMode": "<PERSON><PERSON> s<PERSON>l", "mco.configure.world.invite.narration": "Saada dan %s ainlatna", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Ainlatena", "mco.configure.world.invited.number": "Ainlatena (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Brukzhin", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Li shkekso realm, senaiti uten plustid ainlatena bli", "mco.configure.world.leave.question.line2": "Du zettai vil ende suru?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "Plas", "mco.configure.world.minigame": "Ima: %s", "mco.configure.world.name": "Realm namaï", "mco.configure.world.opening": "Na auki Realm...", "mco.configure.world.players.error": "Nai jam spilzhin mit nasena na<PERSON>", "mco.configure.world.players.inviting": "<PERSON><PERSON> ima spildjin...", "mco.configure.world.players.title": "Spilzhin", "mco.configure.world.pvp": "Spilzhin kundur spilzhin", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Velt fu du gjensintwamir au velt fu ima khnopitmir", "mco.configure.world.reset.question.line2": "Du zettai vil ende suru?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "Per spel na afto Realm, trengti saada tsatain shirenabaksu", "mco.configure.world.resourcepack.question.line2": "Vil zesada au spil to?", "mco.configure.world.restore.download.question.line1": "Afto velt zesadenamir au nasenamir na tolkazhin velt fu du.", "mco.configure.world.restore.download.question.line2": "Vil benn?", "mco.configure.world.restore.question.line1": "Velt fu du reformamir tid '%s' made (%s)", "mco.configure.world.restore.question.line2": "Du zettai vil ende suru?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Velt %s", "mco.configure.world.slot.empty": "Oh<PERSON>", "mco.configure.world.slot.switch.question.line1": "Realm fu du kawarimir ander velt made", "mco.configure.world.slot.switch.question.line2": "Du zettai vil ende suru?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON><PERSON> velt", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "<PERSON><PERSON><PERSON><PERSON> spilnen", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON> dyr", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON> morko", "mco.configure.world.spawnNPCs": "Posintua NPC", "mco.configure.world.spawnProtection": "Sintuangoro eshku", "mco.configure.world.spawn_toggle.message": "Afto kap na 'nai' ti ERKESHITE AL-TONT jamshal na tuo fal", "mco.configure.world.spawn_toggle.message.npc": "Afto kap na 'nai' ti ERKESHITE AL-TONT jamshal na tuo fal, tato Statdjin", "mco.configure.world.spawn_toggle.title": "<PERSON><PERSON>!", "mco.configure.world.status": "Tatsusma", "mco.configure.world.subscription.day": "dag", "mco.configure.world.subscription.days": "dag", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Popitkæøze bruktid", "mco.configure.world.subscription.less_than_a_day": "Minus na ein dag", "mco.configure.world.subscription.month": "muai", "mco.configure.world.subscription.months": "muai", "mco.configure.world.subscription.recurring.daysleft": "<PERSON><PERSON><PERSON><PERSON><PERSON> beng<PERSON>a za", "mco.configure.world.subscription.recurring.info": "<PERSON><PERSON> na Realms tidkaupa, tato tuma<PERSON><PERSON><PERSON> tidkaupa os jame ka<PERSON>, mirai nai seena pu ima de mirairaz fu tidkaupa.", "mco.configure.world.subscription.remaining.days": "%1$s dag()", "mco.configure.world.subscription.remaining.months": "%1$s mwai", "mco.configure.world.subscription.remaining.months.days": "%1$s mwai, %2$s dag", "mco.configure.world.subscription.start": "Hadjidag", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Ende tid", "mco.configure.world.subscription.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> fudu", "mco.configure.world.subscription.unknown": "Nai shirena", "mco.configure.world.switch.slot": "Maha velt", "mco.configure.world.switch.slot.subtitle": "Afto velt ohare, da sentaku hur maha velt fu du", "mco.configure.world.title": "Razkap realma:", "mco.configure.world.uninvite.player": "Vilti zettai knainlat '%s'?", "mco.configure.world.uninvite.question": "Ziha we vilti knainlat", "mco.configure.worlds.title": "<PERSON><PERSON><PERSON>", "mco.connect.authorizing": "Na zeshkinhe...", "mco.connect.connecting": "<PERSON> tsunaga <PERSON> made...", "mco.connect.failed": "<PERSON><PERSON>dan tsunaga Realm made", "mco.connect.region": "Server region: %s", "mco.connect.success": "<PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Mus nasi nama<PERSON>!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "Ima maha velt...", "mco.create.world.skip": "Pal", "mco.create.world.subtitle": "Li du vil, sentaku velt per nasi na neo Realm fu du", "mco.create.world.wait": "Na maha Realm...", "mco.download.cancelled": "<PERSON><PERSON><PERSON><PERSON> jam<PERSON>na", "mco.download.confirmation.line1": "Velt du akote zesadamir plus stur na %s", "mco.download.confirmation.line2": "<PERSON>i la<PERSON> gjenz<PERSON>ta afto velt Realm fu du made", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "Pozesadena", "mco.download.downloading": "Na zesada", "mco.download.extracting": "<PERSON>ks<PERSON><PERSON><PERSON><PERSON> ima", "mco.download.failed": "<PERSON><PERSON><PERSON>", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON> <PERSON>ova zesada", "mco.download.resourcePack.fail": "Humba lesa shirenabaksu!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Na zesaada letste neo velt", "mco.error.invalid.session.message": "Bitte iskat gjenauki Minecraft", "mco.error.invalid.session.title": "Lagomnai zefugatid", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON> spelab<PERSON>", "mco.errorMessage.6002": "Tumam fu Uslovara uslovennai", "mco.errorMessage.6003": "Long ele fu zesada", "mco.errorMessage.6004": "Long ele fu zeanta", "mco.errorMessage.6005": "Velt anfestena", "mco.errorMessage.6006": "<PERSON><PERSON><PERSON> poga<PERSON>l", "mco.errorMessage.6007": "Brukdjin na obamange Realm", "mco.errorMessage.6008": "<PERSON><PERSON> namae fu <PERSON>", "mco.errorMessage.6009": "<PERSON>ui tsuitana fu Realm", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON> dan, bitte gjeniskat na mirai.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON> slucha: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "Jam nil impla antaena", "mco.errorMessage.realmsService": "<PERSON><PERSON><PERSON> slucha (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Humba tsunaga Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "<PERSON><PERSON><PERSON> lesa imali<PERSON>-ve<PERSON><PERSON>, svar jam na: %s", "mco.errorMessage.retry": "Gjeniskat", "mco.errorMessage.serviceBusy": "Realms ima zanjat.\nT<PERSON> j<PERSON>-mirainen tuvat gjentsunaga na Realms made.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "Bra", "mco.info": "Impla!", "mco.invited.player.narration": "Ainlatena spildjin %s", "mco.invites.button.accept": "<PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "Nil neo ainlat!", "mco.invites.pending": "Neo ainlatna!", "mco.invites.title": "<PERSON><PERSON> ka vent", "mco.minigame.world.changeButton": "<PERSON> sentaku ander spilnen", "mco.minigame.world.info.line1": "<PERSON><PERSON>del na tidnen velt per spilnen!", "mco.minigame.world.info.line2": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>i gammel velt fu du made ytten humba niltinn.", "mco.minigame.world.noSelection": "<PERSON><PERSON> da sentaku", "mco.minigame.world.restore": "<PERSON> owari spilnen...", "mco.minigame.world.restore.question.line1": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>mir au Realm fu du reformenamir.", "mco.minigame.world.restore.question.line2": "Du zettai vil ende suru?", "mco.minigame.world.selected": "<PERSON><PERSON><PERSON> spilnen:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON> ka<PERSON> velt...", "mco.minigame.world.startButton": "Dlabdel", "mco.minigame.world.starting.screen.title": "Na hadzhi spilnen...", "mco.minigame.world.stopButton": "<PERSON><PERSON> spilnen", "mco.minigame.world.switch.new": "Vil sentaku ander spilnen?", "mco.minigame.world.switch.title": "<PERSON><PERSON> s<PERSON>", "mco.minigame.world.title": "Kawari Realm spilnen made", "mco.news": "Sluchana fu Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Antaa ima", "mco.notification.transferSubscription.message": "Realm tidkaupa ima anugoki na Microsoft Store. Spaadanai tsa kaupatid!\nLi anugoki ima saadati 30 dag na Realms utnkaupa.\nShkoitsa na Profile long minecraft.net per anugoki kaupatid.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON> z<PERSON>a", "mco.notification.visitUrl.message.default": "Bite bides zedvera unna", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "Spore", "mco.reset.world.adventure": "Korovirta", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "Neo velt", "mco.reset.world.inspiration": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.resetting.screen.title": "Na gjenmaha velt...", "mco.reset.world.seed": "Zep<PERSON> (nai trænena)", "mco.reset.world.template": "<PERSON>eltpolfara", "mco.reset.world.title": "Gjensintwa velt", "mco.reset.world.upload": "<PERSON><PERSON><PERSON> velt", "mco.reset.world.warning": "Afto mirai na plas fu imas velt fu Realm fu du", "mco.selectServer.buy": "Kaupa Realm!", "mco.selectServer.close": "<PERSON><PERSON>", "mco.selectServer.closed": "Kinena Realm", "mco.selectServer.closeserver": "Kini <PERSON>", "mco.selectServer.configure": "Raz<PERSON><PERSON><PERSON>", "mco.selectServer.configureRealm": "<PERSON> kawarina", "mco.selectServer.create": "Sintwa Realm", "mco.selectServer.create.subtitle": "Sentaku velt per anugoki na Realm", "mco.selectServer.expired": "Powarjena Realm", "mco.selectServer.expiredList": "<PERSON><PERSON><PERSON><PERSON> dan nastakkar", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "<PERSON><PERSON><PERSON><PERSON>, sta<PERSON><PERSON> du", "mco.selectServer.expires.day": "<PERSON><PERSON> na ein dag", "mco.selectServer.expires.days": "Powari na %s dag", "mco.selectServer.expires.soon": "<PERSON><PERSON> na susu", "mco.selectServer.leave": "Shkekso Realm", "mco.selectServer.loading": "Lesa tumam Realms", "mco.selectServer.mapOnlySupportedForVersion": "Afto karta nai dvaibma na %s", "mco.selectServer.minigame": "Spilnen:", "mco.selectServer.minigameName": "Spilnen: %s", "mco.selectServer.minigameNotSupportedInVersion": "Nai deki spil afto spilnen na %s", "mco.selectServer.noRealms": "<PERSON>na ke jam nil Realm. Maha per spel mit klaanidjin.", "mco.selectServer.note": "Da lese na bra:", "mco.selectServer.open": "Aukena Realm", "mco.selectServer.openserver": "Auki <PERSON>", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms zikha, simper tropos per spil Minecraft velt narjet mit den mik naele na sama tid.  De<PERSON> mange spilnen au sebjakawarena velt! Mono j<PERSON><PERSON>hin fu Realm mus anta gelt.", "mco.selectServer.purchase": "Nasi <PERSON>", "mco.selectServer.trial": "Da wigel!", "mco.selectServer.uninitialized": "Asklik per anhadji Realm!", "mco.snapshot.createSnapshotPopup.text": "<PERSON> a<PERSON>em<PERSON>i sintua vershonen-Realm, ke mirai tsunaga kaupaena tidkaupa Realms. Afto neo vershonen-Realm mirai brukena<PERSON>i ftedi, ke tidkaupa ende jam. Kaupaena Realm nai kawariena grun afto.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON><PERSON> vershonen-<PERSON>?", "mco.snapshot.creating": "<PERSON><PERSON> sintua vershonen-Realm...", "mco.snapshot.description": "Na \"%s\" tsunaga", "mco.snapshot.friendsRealm.downgrade": "Træng versho %s per inne afto Realm", "mco.snapshot.friendsRealm.upgrade": "%s mus anneøze Realm de du deki spil na afto versho fu spelabma", "mco.snapshot.paired": "Afto vershonen-Realm tsunaga \"%s\"", "mco.snapshot.parent.tooltip": "Bruk letsteneo blogeta versho fu Minecraft per spel na afto Realm", "mco.snapshot.start": "<PERSON><PERSON> vershonen-Realm", "mco.snapshot.subscription.info": "Afto vershonen-Realm tsunaga tidkaupa fu Realm fu du '%s'. Mirai ende jam ftedi ke hanena Realm jam kaupena.", "mco.snapshot.tooltip": "<PERSON><PERSON><PERSON>shot Realms per polera para neo versho fu Minecraft; tabun na he neo dekizma au andra kawari.", "mco.snapshotRealmsPopup.message": "Realms ima blogeta na vershonen, hadji na vershonen 23w41a. <PERSON>nti tidkaupa Realms bidra auen vershonen-Realm, chigau ke snjano Realm fudu na Java!", "mco.snapshotRealmsPopup.title": "Realms ima na Snapshots dekiena", "mco.snapshotRealmsPopup.urlText": "Lera Plus", "mco.template.button.publisher": "Blogedjin", "mco.template.button.select": "Sentaku", "mco.template.button.trailer": "Defilm", "mco.template.default.name": "Veltpolfal", "mco.template.info.tooltip": "Rjetplas fu blogedjin", "mco.template.name": "Polfal", "mco.template.select.failure": "Vi humba dan saada tumam fu impla na afto hjernekla<PERSON>. <PERSON>e anse r<PERSON>, os tabun plusmirai iskat ti.", "mco.template.select.narrate.authors": "Kakuzhin: %s", "mco.template.select.narrate.version": "fallasku %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, seena ke afto hjer<PERSON> ima ohare.\nBite anse mirainen per neo impla, os li du jam na mahadjin,\n%s.", "mco.template.select.none.linkTitle": "da smjetta zeposhta jokuting du made", "mco.template.title": "Veltpolfal", "mco.template.title.minigame": "S<PERSON>lnen", "mco.template.trailer.tooltip": "Karta defilm", "mco.terms.buttons.agree": "<PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON>a", "mco.terms.sentence.1": "Un uslova Minecraft Realms", "mco.terms.sentence.2": "Tumam fu Uslov<PERSON>", "mco.terms.title": "Uslovara-Tumam per Realms", "mco.time.daysAgo": "%1$s dag dan", "mco.time.hoursAgo": "%1$s djikan dan", "mco.time.minutesAgo": "%1$s fun dan", "mco.time.now": "akoteima", "mco.time.secondsAgo": "%1$s sho dan", "mco.trial.message.line1": "Vil har Realm fu du?", "mco.trial.message.line2": "Her askliq per lera plus!", "mco.upload.button.name": "Zeantaa", "mco.upload.cancelled": "<PERSON><PERSON><PERSON>na", "mco.upload.close.failure": "Nai deki kini Realm fu du, bitte da gjeniskat mirnen", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Zeanta umbadan! (%s)", "mco.upload.failed.too_big.description": "Afto velt obastur. Lestestur velt %s", "mco.upload.failed.too_big.title": "Velt obastur", "mco.upload.hardcore": "Nai laki zeanta erhaste velt!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON> velt fu du", "mco.upload.select.world.none": "Nai fynna tolk<PERSON> velt!", "mco.upload.select.world.subtitle": "<PERSON>te da sentaku tolk<PERSON>hin velt per zeanta", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON> velt", "mco.upload.size.failure.line1": "'%s' obastur!", "mco.upload.size.failure.line2": "Afto %s. Letste stur lakena sturatai %s.", "mco.upload.uploading": "Ima anta '%s'", "mco.upload.verifying": "An<PERSON>a parjad fu velt", "mco.version": "Fallasku: %s", "mco.warning": "<PERSON><PERSON>!", "mco.worldSlot.minigame": "S<PERSON>lnen", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "<PERSON><PERSON>", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Sentakutumam fu spil", "menu.modded": " (<PERSON><PERSON><PERSON>)", "menu.multiplayer": "Spel mit klaanidjin", "menu.online": "Minecraft Realms", "menu.options": "Senta<PERSON>na...", "menu.paused": "Spiltid vent", "menu.playdemo": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "menu.playerReporting": "Anopeta Tsui Spildjin", "menu.preparingSpawn": "<PERSON><PERSON> sintu<PERSON>: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "<PERSON><PERSON> spil", "menu.reportBugs": "<PERSON><PERSON><PERSON>", "menu.resetdemo": "<PERSON><PERSON><PERSON><PERSON>", "menu.returnToGame": "<PERSON><PERSON> spil made", "menu.returnToMenu": "Ufne au sh<PERSON>ks<PERSON> na<PERSON>hti made", "menu.savingChunks": "Ufne ima velttelnen", "menu.savingLevel": "Ufne ima velt", "menu.sendFeedback": "<PERSON><PERSON><PERSON> mi<PERSON>", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "Ainlat per LAN-rjet", "menu.singleplayer": "Spel namono", "menu.working": "Ima ergo...", "merchant.deprecated": "Benghok fu statdjin gjen<PERSON>lap ni raz na ele na dag.", "merchant.level.1": "Neodjin", "merchant.level.2": "Leradjin", "merchant.level.3": "Mellandjin", "merchant.level.4": "Chaddjin", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Tasta %1$s per shkekso", "multiplayer.applyingPack": "<PERSON><PERSON> s<PERSON>", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Server fu lesa zefuga ergonai; gomen! Bite iskat mirai!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "<PERSON> erkshitejena afto server kara", "multiplayer.disconnect.banned.expiration": "\nBanna fu du owari na %s", "multiplayer.disconnect.banned.reason": "Du erkeshitejena afto server.\nGrun: %s", "multiplayer.disconnect.banned_ip.expiration": "\nBanna fu du owari na %s", "multiplayer.disconnect.banned_ip.reason": "Zeplas fu du erkeshitena (banena) afto server kara.\nGrun: %s", "multiplayer.disconnect.chat_validation_failed": "Pochta humba dan na pravdanzhe", "multiplayer.disconnect.duplicate_login": "<PERSON> zesh<PERSON>e dan andr kompju kara", "multiplayer.disconnect.expired_public_key": "Ergammel blogeta klyuch. Da zuha lera, ke tid fu kompju jam na paryad, au ilta-asa spel.", "multiplayer.disconnect.flying": "Nai laki lyetaa ine aftoo server", "multiplayer.disconnect.generic": "Tsam dan", "multiplayer.disconnect.idling": "Du suru dan nil obamange tid!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON><PERSON><PERSON><PERSON> kakena", "multiplayer.disconnect.incompatible": "Lagomnai brukdwaibma! Bitte bruk %s", "multiplayer.disconnect.invalid_entity_attacked": "<PERSON><PERSON> harza perpena jamting", "multiplayer.disconnect.invalid_packet": "Zekonen serverkara dwaibmanai", "multiplayer.disconnect.invalid_player_data": "Dwaibmanai impla fu speldjin", "multiplayer.disconnect.invalid_player_movement": "<PERSON><PERSON><PERSON> shkoi-konen saada dan speldjin kara", "multiplayer.disconnect.invalid_public_key_signature": "<PERSON>ui zihakaku na blogeta klyuch.\nIskat da mahailta-asa spel.", "multiplayer.disconnect.invalid_public_key_signature.new": "Warui <PERSON> na zefuga.\nTsa iskat gjenhadji spil.", "multiplayer.disconnect.invalid_vehicle_movement": "Uwaki auto-shkoi zekonen saada dan", "multiplayer.disconnect.ip_banned": "Zeplas (IP) fu du banena na afto server", "multiplayer.disconnect.kicked": "<PERSON><PERSON><PERSON><PERSON> grun jew<PERSON>", "multiplayer.disconnect.missing_tags": "<PERSON>lnai na<PERSON>enen saadajena server kara.\\nDa bite hanu serverjewaltdjin made.", "multiplayer.disconnect.name_taken": "<PERSON><PERSON> ende harena", "multiplayer.disconnect.not_whitelisted": "Du nai na ainlattumam fu afto server!", "multiplayer.disconnect.out_of_order_chat": "<PERSON>za-par<PERSON><PERSON>a pochta saadena. Toki fu kompju kawari dan we?", "multiplayer.disconnect.outdated_client": "Gammel brukdwaibma! Bitte bruk %s", "multiplayer.disconnect.outdated_server": "Gammel server! Bite bruk %s", "multiplayer.disconnect.server_full": "Pulap server!", "multiplayer.disconnect.server_shutdown": "Server kinni dan", "multiplayer.disconnect.slow_login": "Bruktiid fu zetula obapitka", "multiplayer.disconnect.too_many_pending_chats": "Obamange pochta nai sejena", "multiplayer.disconnect.transfers_disabled": "Server nai saada anugokina", "multiplayer.disconnect.unexpected_query_response": "Uwa<PERSON> ka<PERSON>na serverbrukkompju kara", "multiplayer.disconnect.unsigned_chat": "<PERSON><PERSON><PERSON> poch<PERSON>l uten zihakaku, os mit warui zihakaku.", "multiplayer.disconnect.unverified_username": "<PERSON><PERSON><PERSON> iskat mahapravda namae!", "multiplayer.downloadingStats": "<PERSON><PERSON><PERSON> s<PERSON>...", "multiplayer.downloadingTerrain": "Lesa terrdai...", "multiplayer.lan.server_found": "Neo server fynnena: %s", "multiplayer.message_not_delivered": "%s-hadji pochta Humbadan bidrjena. Bite seda sluchatumam fu server", "multiplayer.player.joined": "%s tulladan spil", "multiplayer.player.joined.renamed": "%s (haissadan %s) tulla spil", "multiplayer.player.left": "%s shkekso spil", "multiplayer.player.list.hp": "%szp", "multiplayer.player.list.narration": "Narjetdjin: %s", "multiplayer.requiredTexturePrompt.disconnect": "Afto zespilserver treng shirenabaksu", "multiplayer.requiredTexturePrompt.line1": "Afto zespilserver treng ka bruk shirenabaksu.", "multiplayer.requiredTexturePrompt.line2": "Li jit afto s<PERSON><PERSON><PERSON><PERSON><PERSON>, owari tsunaga mit afto zespilserver.", "multiplayer.socialInteractions.not_available": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON> laki mono ine spil mitt and<PERSON><PERSON>", "multiplayer.status.and_more": "… au %s plus andra ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON> dan", "multiplayer.status.cannot_connect": "Hu<PERSON>dan tsunaga server made", "multiplayer.status.cannot_resolve": "Dekinai fshto zeplas per zenamae", "multiplayer.status.finished": "<PERSON><PERSON> dan", "multiplayer.status.incompatible": "Versio lagomnai!", "multiplayer.status.motd.narration": "Pochta fu dag: %s", "multiplayer.status.no_connection": "(nil zeryet)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "Narjet", "multiplayer.status.ping": "%s shotuhattel", "multiplayer.status.ping.narration": "Svartid %s sho-tuhattel", "multiplayer.status.pinging": "Ima ping...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s fu %s narjetdjin", "multiplayer.status.quitting": "<PERSON><PERSON>", "multiplayer.status.request_handled": "<PERSON><PERSON> svar dwaib<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Dan waruantaa tsuitekaku", "multiplayer.status.version.narration": "Versiolasku servera: %s", "multiplayer.stopSleeping": "<PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "Shirenabaksu fu zespilserver nai dekidan antena", "multiplayer.texturePrompt.failure.line2": "<PERSON><PERSON><PERSON> dvaib<PERSON>ma ka trænn tsatain implabaksu dekiti nai dvaibma na snano", "multiplayer.texturePrompt.line1": "Afto zespilserver mie brati li bruk shirenabaksu.", "multiplayer.texturePrompt.line2": "<PERSON>ilti zesadena au dwaibmena mit seb<PERSON><PERSON><PERSON>?", "multiplayer.texturePrompt.serverPrompt": "%s\\n\\nPoshta zespilserver made:\\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON> mit k<PERSON>anidjin", "multiplayer.unsecureserver.toast": "Poshta poshtena inhe afto server de<PERSON><PERSON> kawari au nai uslova ka tastena", "multiplayer.unsecureserver.toast.title": "<PERSON>chta de<PERSON> p<PERSON>a", "multiplayerWarning.check": "Da nai gjenmahse afto tumam", "multiplayerWarning.header": "<PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON> spil mit eksodjin", "multiplayerWarning.message": "Oi! Spil na zerjet utnjewalt au utneshku fu nai Mojang nai Microsoft. Aftokara, na rjetspil, tabun spilserver nai reforma nai jevalt tasta. Nai Mojang nai Microsoft svar grun zerjetkusipæ.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Tasta: %s", "narration.button.usage.focused": "Tasta Enter per poasa", "narration.button.usage.hovered": "<PERSON><PERSON><PERSON> na myshljeva per poasa", "narration.checkbox": "Akkubaksu: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON> Enter per dlabdel", "narration.checkbox.usage.hovered": "<PERSON><PERSON><PERSON><PERSON><PERSON> per dlabdel", "narration.component_list.usage": "Tasta Tab per sentaku miraitel", "narration.cycle_button.usage.focused": "Tasta Enter per dlabdel na %s made", "narration.cycle_button.usage.hovered": "Kliq na myshljeva per dlabdel na %s made", "narration.edit_box": "Kawaribaksu: %s", "narration.item": "Item: %s", "narration.recipe": "Mahatropos fu %s", "narration.recipe.usage": "<PERSON><PERSON><PERSON><PERSON><PERSON> per sentaku", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON> per plus mahatropos", "narration.selection.usage": "Tasta oba au una per kiva na andr impla made", "narration.slider.usage.focused": "<PERSON><PERSON> ljeva au migi per kawari lasku", "narration.slider.usage.hovered": "B<PERSON>ra ghating na ataisen per kawari lasku", "narration.suggestion": "Sentakena miepie %s %s kara: %s", "narration.suggestion.tooltip": "Sentakena bruk-miepie %s %s kara: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Tasta Tab per sentaku mirai periken", "narration.suggestion.usage.cycle.hidable": "Tasta Tab per sentaku mirai periken, os Escape per shkekso periken kara", "narration.suggestion.usage.fill.fixed": "Tasta Tab per bruk periken", "narration.suggestion.usage.fill.hidable": "Tasta Tab per bruk periken, os Escape per shkekso periken", "narration.tab_navigation.usage": "Bruk Ctrl au Tab per pal na tumamlehti", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Festana fu <PERSON>", "narrator.button.difficulty_lock.locked": "Festa", "narrator.button.difficulty_lock.unlocked": "Festanai", "narrator.button.language": "Glossa", "narrator.controls.bound": "%s festa %s shang", "narrator.controls.reset": "Pogjen presmi %s", "narrator.controls.unbound": "%s har nil zafal", "narrator.joining": "<PERSON><PERSON>", "narrator.loading": "Lese: %s", "narrator.loading.done": "Owari", "narrator.position.list": "Sentaku dan pol %s fu %s tumam kara", "narrator.position.object_list": "Sentaku dan tel %s (%s kara) fu pol", "narrator.position.screen": "Ekrantel %s fu %s kara", "narrator.position.tab": "Sentaku dan tumamlehti %s na %s kara", "narrator.ready_to_play": "<PERSON><PERSON><PERSON><PERSON>", "narrator.screen.title": "<PERSON><PERSON><PERSON><PERSON>", "narrator.screen.usage": "<PERSON><PERSON><PERSON> os Tab per sentaku tel", "narrator.select": "Sentakena: %s", "narrator.select.world": "Sentaku %s, letste kvellik spilraz na: %s, %s, %s, fallasku: %s", "narrator.select.world_info": "Sentaku %s, dan spel: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON>", "optimizeWorld.confirm.description": "<PERSON>vat mirai nasi al dwaibmatel na letste imalik fal, per maha velt letste gotovena. <PERSON><PERSON><PERSON> mange pitka tid, mit impla fu velt. <PERSON><PERSON> gotova, velt plus<PERSON><PERSON><PERSON>, men dekinai bruk mit plusgam<PERSON> Shah<PERSON>ah<PERSON> las<PERSON>fal. <PERSON><PERSON><PERSON> vil suru we?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> velt", "optimizeWorld.info.converted": "Poneojena velttelnen: %s", "optimizeWorld.info.skipped": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>: %s", "optimizeWorld.info.total": "Al velttelnen: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Lasku ima atai velttelnen...", "optimizeWorld.stage.failed": "Humba dan! >:(", "optimizeWorld.stage.finished": "Owariht<PERSON>...", "optimizeWorld.stage.finished.chunks": "<PERSON><PERSON><PERSON>ella anta neo parjad fu veltelnen...", "optimizeWorld.stage.finished.entities": "<PERSON><PERSON><PERSON><PERSON> anta neo parjad fu sjal...", "optimizeWorld.stage.finished.poi": "<PERSON><PERSON><PERSON><PERSON> anta neo parjad fu kjomiplas...", "optimizeWorld.stage.upgrading": "Antahtella neo parjad fu velttel...", "optimizeWorld.stage.upgrading.chunks": "Antahtella neo parjad fu velttel...", "optimizeWorld.stage.upgrading.entities": "Anta ima neo parjad fu sjal...", "optimizeWorld.stage.upgrading.poi": "Neoyze al kjomipik...", "optimizeWorld.title": "Par<PERSON><PERSON><PERSON> velt '%s'", "options.accessibility": "Sentakuna fu spelapu...", "options.accessibility.high_contrast": "Letstechigau Sejena", "options.accessibility.high_contrast.error.tooltip": "<PERSON><PERSON><PERSON>an <PERSON>chi<PERSON> Sejena milu", "options.accessibility.high_contrast.tooltip": "<PERSON><PERSON> per fal ti bli plussimpel sejena", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "<PERSON>r<PERSON>mi fu spelapu", "options.accessibility.menu_background_blurriness": "<PERSON>az<PERSON> hina sentaku-e<PERSON>n", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON> fua-seen<PERSON>ma fu riso hina sentaku-e<PERSON>n", "options.accessibility.narrator_hotkey": "Presmi per koilesadjin", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON><PERSON><PERSON> ke koilesadjin deki brukena mit 'Cmd+B'", "options.accessibility.narrator_hotkey.tooltip": "<PERSON><PERSON><PERSON> ke koilesadjin deki brukena mit 'Ctrl+B'", "options.accessibility.panorama_speed": "<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background.chat": "Hanutasta/Chatto", "options.accessibility.text_background.everywhere": "Alplas", "options.accessibility.text_background_opacity": "Klaaratai bides tekstihina", "options.accessibility.title": "Sentakuna fu spelapu...", "options.allowServerListing": "<PERSON><PERSON>", "options.allowServerListing.tooltip": "Server deki bloge narjetdjin na tsuiteimpla.\nNabruk afto sentakuna, namae ti blogetanai.", "options.ao": "Fuwa kirk<PERSON>pulap", "options.ao.max": "Letstemange", "options.ao.min": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.off": "NAI", "options.attack.crosshair": "Slagpik", "options.attack.hotbar": "Bystratumam", "options.attackIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.audioDevice": "Kompjuting", "options.audioDevice.default": "Hadjifal fu Kompju", "options.autoJump": "<PERSON><PERSON><PERSON><PERSON>", "options.autoSuggestCommands": "Jewalt-apupie", "options.autosaveIndicator": "Opetanen fu leoufne", "options.biomeBlendRadius": "Viskatai fu asmiplas", "options.biomeBlendRadius.1": "NAI (letste bistra)", "options.biomeBlendRadius.11": "11x11 (Tak bra)", "options.biomeBlendRadius.13": "13x13 (Vilsejenadjin)", "options.biomeBlendRadius.15": "15x15 (<PERSON> ele)", "options.biomeBlendRadius.3": "3x3 (Bistra)", "options.biomeBlendRadius.5": "5x5 (Snjano)", "options.biomeBlendRadius.7": "7x7 (Bra)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON>)", "options.chat": "Chatto-sentakuna...", "options.chat.color": "Varje", "options.chat.delay": "<PERSON><PERSON>sta hiidas-atai: %s sho", "options.chat.delay_none": "<PERSON><PERSON><PERSON> hiidas-atai: Nil", "options.chat.height.focused": "Ataipitka na shuchu", "options.chat.height.unfocused": "Pitkatai ut<PERSON>", "options.chat.line_spacing": "Ohare melan pochta", "options.chat.links": "Zedvera", "options.chat.links.prompt": "Spore koske zedvera", "options.chat.opacity": "Klaaratai bides pochta", "options.chat.scale": "Stuuratai fu Chattokirain", "options.chat.title": "Chatto-sentakuna...", "options.chat.visibility": "<PERSON><PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON> jew<PERSON>", "options.chat.width": "<PERSON><PERSON><PERSON><PERSON>", "options.chunks": "%s velttelnen", "options.clouds.fancy": "Helena", "options.clouds.fast": "Bistra", "options.controls": "Tastatropos...", "options.credits_and_attribution": "<PERSON><PERSON><PERSON> au darezma...", "options.damageTiltStrength": "Arkaik", "options.damageTiltStrength.tooltip": "Atai ugokena ekran koske arka.", "options.darkMojangStudiosBackgroundColor": "Spilkao na nifarge", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON>h varge fu Mojang Studios ventlehti bli kuro.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON> nak<PERSON>", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON> katai kuraisma tainakokoro rasasa koske eskuzhin os Sculk shreidvaibma anta du made.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "<PERSON><PERSON><PERSON><PERSON>, men minus arkaoze. Nam-sen bli minus bi tid au tønni zdorvapik na 5 ker made.", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "<PERSON><PERSON><PERSON><PERSON> razsintua mit plus arkadjong na snjano. Nam-sen bli minus bi tid au tønni zdorvapik na nil made.", "options.difficulty.hardcore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal": "<PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "<PERSON><PERSON><PERSON><PERSON> raz<PERSON>a mit snjano arkapik. Nam-sen bli minus bi tid au tønni zdorvapik na hanker made.", "options.difficulty.online": "Haasteatai fu Server", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "<PERSON>l harzashal au apar ma<PERSON>a-vonashal usintua. Namd<PERSON> blinai minus, au zdorvapik bli plus bi tid.", "options.directionalAudio": "Plusbra Strelazan", "options.directionalAudio.off.tooltip": "<PERSON><PERSON><PERSON> fal", "options.directionalAudio.on.tooltip": "Bruk HRTF (atama-kara zankawari) per plusgwirøze zan na 3D. Treng HRTF-deki kompju<PERSON>, au mit zekuchinen letstebra aistiati.", "options.discrete_mouse_scroll": "Oba-una naleo", "options.entityDistanceScaling": "Prapatai fu shal", "options.entityShadows": "<PERSON>rai una shal", "options.font": "Sentakuna fu kakutropos...", "options.font.title": "Sentakuna fu kakutropos", "options.forceUnicodeFont": "Bai Unicode-font bruk", "options.fov": "Se-ik", "options.fov.max": "Quake Jingdjin", "options.fov.min": "<PERSON><PERSON><PERSON>", "options.fovEffectScale": "<PERSON><PERSON><PERSON><PERSON> kawarina", "options.fovEffectScale.tooltip": "Jewalt katai setropos kawari koske bistra os hiidas.", "options.framerate": "%s riso/sjo", "options.framerateLimit": "Eleatai fu zese per sho", "options.framerateLimit.max": "Utenele", "options.fullscreen": "<PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.current": "<PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "options.fullscreen.unavailable": "<PERSON><PERSON><PERSON> sentaku", "options.gamma": "Kirkasatai", "options.gamma.default": "Hadjifal", "options.gamma.max": "Kirkasdai", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Bystratai kiragiria", "options.glintSpeed.tooltip": "<PERSON><PERSON> bystratai fu kirigira na taikating.", "options.glintStrength": "Kirkasatai fu kiragira", "options.glintStrength.tooltip": "<PERSON><PERSON> bidesse-atai fu kiragira na taikating.", "options.graphics": "Setropos", "options.graphics.fabulous": "Tak helena!", "options.graphics.fabulous.tooltip": "%s risotropos bruk taikariso per pogoda, kumo au raztelnen hina bidessejena dado au ishke. Aftoo plus veht na risobma fu hantkompju au 4K ekran.", "options.graphics.fancy": "Helena", "options.graphics.fancy.tooltip": "Helena risotropos ergo mellan helenazma au brystrazma per para al kompyu. De<PERSON> ka pogoda, kumo au raztelnen nai sena hina bidessejena dado os ishke.", "options.graphics.fast": "Bistra", "options.graphics.fast.tooltip": "Bistra risotropos mahaminus katai plui au upash sejena.\n<PERSON><PERSON><PERSON> bid<PERSON><PERSON>, ta<PERSON><PERSON><PERSON> le<PERSON>, k<PERSON><PERSON><PERSON>.", "options.graphics.warning.accept": "Uten ergo", "options.graphics.warning.cancel": "Suryktsa", "options.graphics.warning.message": "<PERSON> lesa, ekrandwaibma fu du kjokanai %s sent<PERSON><PERSON>/kawarina. <PERSON><PERSON><PERSON><PERSON> he<PERSON>, men li tatsu<PERSON> %s e<PERSON><PERSON><PERSON><PERSON>, antajena nil apu na ishaika.", "options.graphics.warning.renderer": "Zekakutropos fynnena: [%s]", "options.graphics.warning.title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "options.graphics.warning.vendor": "Hokdjin fynnena: [%s]", "options.graphics.warning.version": "OpenGL-versio fynnena: [%s]", "options.guiScale": "GUI stuuratai", "options.guiScale.auto": "<PERSON><PERSON><PERSON><PERSON>", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON>", "options.hideLightningFlashes.tooltip": "Knlaki ke zeustrelaa sintua razkirkas na sini. Mono zeustrelaa ende seenamirai.", "options.hideMatchedNames": "<PERSON><PERSON> finnena namae", "options.hideMatchedNames.tooltip": "Eksolik server tabun antaati chattopochta na knsnjano parjadtropos.\nLi bruk afto sent<PERSON>, kn<PERSON>na speldjin mirai tsunagena napol fu namae fu antadjin.", "options.hideSplashTexts": "<PERSON><PERSON>", "options.hideSplashTexts.tooltip": "<PERSON>ri ti zaripunkt/fraz (kiro) na hadji-ekran.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "<PERSON><PERSON><PERSON><PERSON> strela fu mysh", "options.japaneseGlyphVariants": "<PERSON><PERSON><PERSON> ka<PERSON>", "options.japaneseGlyphVariants.tooltip": "Bruk ti nihongossa-fal fu CJK-tasta na snjano-kakutropos", "options.key.hold": "Pitkatasta", "options.key.toggle": "<PERSON><PERSON><PERSON>", "options.language": "Glossa...", "options.language.title": "Glossa", "options.languageAccuracyWarning": "(Glossakyannos apartid dekiti pravda na minus k 100%%)", "options.languageWarning": "Glossakyannos apartid dekiti pravda na minus k 100%%", "options.mainHand": "<PERSON><PERSON> ha<PERSON>", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON>", "options.mipmapLevels": "<PERSON><PERSON><PERSON> atai", "options.modelPart.cape": "Kleadai", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "Paltoo", "options.modelPart.left_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON>", "options.mouseWheelSensitivity": "Oba-una bystratai", "options.mouse_settings": "<PERSON><PERSON><PERSON><PERSON>...", "options.mouse_settings.title": "<PERSON><PERSON><PERSON><PERSON>", "options.multiplayer.title": "<PERSON><PERSON><PERSON> na mitrjospil...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "<PERSON>oi", "options.narrator.chat": "<PERSON><PERSON> nakoi", "options.narrator.notavailable": "De<PERSON>ai", "options.narrator.off": "NAI", "options.narrator.system": "<PERSON><PERSON><PERSON><PERSON> nakoi", "options.notifications.display_time": "Opetanen jamtid", "options.notifications.display_time.tooltip": "<PERSON>ald tidatai daper al opetanen na ekran tatsu.", "options.off": "NAI", "options.off.composed": "%s: NAI", "options.on": "AKK", "options.on.composed": "%s: AKK", "options.online": "Narjet...", "options.online.title": "Sentakuna fu zerjet", "options.onlyShowSecureChat": "Se <PERSON>", "options.onlyShowSecureChat.tooltip": "Se Mono pochtara spildjin kara, ke erzettai mono sorekara, uten nil kawari.", "options.operatorItemsTab": "<PERSON><PERSON>-brukting lehti", "options.particles": "Luftsan-tel", "options.particles.all": "Al", "options.particles.decreased": "Minus", "options.particles.minimal": "Letste minus", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON> fu velttelnen", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Joku slucha na velttelnen razgushti hel tel utenvent, tato dadonosi au dadoperpa.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Para velttelnen altid na utenvent razgushena. <PERSON><PERSON><PERSON> blikiti na velttidleo koske dado nosi os perpa.", "options.prioritizeChunkUpdates.none": "<PERSON>tenjame<PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Paravelttel razgushena na chigau dwaibmavirta. <PERSON><PERSON> afto, tabun j<PERSON><PERSON>li veltbidesse ti koske veltdado perpena.", "options.rawMouseInput": "<PERSON><PERSON><PERSON><PERSON>", "options.realmsNotifications": "Slucharjet au blogena fu Realms", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "<PERSON>us zef<PERSON> shirena", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "<PERSON><PERSON><PERSON><PERSON>", "options.resourcepack": "Shirenabaksu...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "<PERSON><PERSON><PERSON>", "options.screenEffectScale.tooltip": "Djongatai fu sekawarjena fu revotasmi au dveradai.\nLi bruk chiisai atai, nai sekawa<PERSON><PERSON>na men midori faria oba.", "options.sensitivity": "<PERSON><PERSON><PERSON><PERSON>", "options.sensitivity.max": "FAANBISTRA!!!", "options.sensitivity.min": "*luftrevota*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Se unakakena", "options.simulationDistance": "Prapatai fu rmdwaibma", "options.skinCustomisation": "Kodjakawari...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON> fu godja", "options.sounds": "Lid au zan...", "options.sounds.title": "Kawarena fu lid au zan", "options.telemetry": "Impla na fonhom...", "options.telemetry.button": "Impla razbidra", "options.telemetry.button.tooltip": "\"%s\" pochta mono trengena impla.\n\"%s\" pochta trengennai, au trengena impla.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON>.", "options.telemetry.state.all": "Na hel", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON><PERSON>", "options.telemetry.state.none": "Na nil", "options.title": "<PERSON><PERSON><PERSON>", "options.touchscreen": "Rørekran-tropos", "options.video": "<PERSON><PERSON>na fu shkoiriso...", "options.videoTitle": "<PERSON><PERSON><PERSON> fu shko<PERSON>so", "options.viewBobbing": "<PERSON><PERSON><PERSON>", "options.visible": "<PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft ima uten huskeohare.\n\n<PERSON><PERSON><PERSON> tte, tabun grun zebagge na spel, os grun Java Virtual Machine har unnamange huskebma auen.\n\nPer knainlat razperpa fu veltimpla, spel owari dan. Tuvat dan oharøze bramange huskebma per suryk na hadjilehti de ben<PERSON>, men deki humba ende.\n\nLi plustid afto pochta sejena, de bitewi<PERSON><PERSON><PERSON> owari-hadji spel.", "outOfMemory.title": "Huskebma tte ersat!", "pack.available.title": "<PERSON><PERSON><PERSON>", "pack.copyFailure": "<PERSON>mba dan mah b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.dropConfirm": "Zettai vil nasii tuo shirenabaksu Minecraft made?", "pack.dropInfo": "Eshku zeting mitt zemysh heer per nasii shirenabaksu", "pack.dropRejected.message": "Afto impla uwaki kaban au nai gjenena:\n%s", "pack.dropRejected.title": "Kabannai impla", "pack.folderInfo": "(<PERSON><PERSON> s<PERSON><PERSON><PERSON> heer made)", "pack.incompatible": "Lagomnai", "pack.incompatible.confirm.new": "Afto shirenabaksu per plusneo versio fu Minecraft, sitt tabun perpa na afto versio.", "pack.incompatible.confirm.old": "Afto shirenabaksu per plusgammel versio fu Minecraft, sitt tabun perpa na afto versio.", "pack.incompatible.confirm.title": "<PERSON><PERSON>i vil lesa afto shirena<PERSON>u?", "pack.incompatible.new": "(Per plus neo versio fu Minecraft mahena)", "pack.incompatible.old": "(Per plus gammel versio fu Minecraft mahena)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON> fu Shirenabaksura", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "dwai<PERSON><PERSON> na hadji", "pack.source.feature": "impla", "pack.source.local": "her", "pack.source.server": "server", "pack.source.world": "velt", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull on Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Zarifal", "parsing.bool.expected": "<PERSON><PERSON><PERSON>", "parsing.bool.invalid": "<PERSON><PERSON><PERSON> p<PERSON>, vildan 'true' os 'false' men se '%s'", "parsing.double.expected": "<PERSON><PERSON><PERSON>", "parsing.double.invalid": "<PERSON><PERSON><PERSON>ku '%s'", "parsing.expected": "<PERSON><PERSON><PERSON> '%s'", "parsing.float.expected": "<PERSON><PERSON><PERSON>", "parsing.float.invalid": "Uwaki <PERSON>ku '%s'", "parsing.int.expected": "<PERSON><PERSON><PERSON>", "parsing.int.invalid": "Uwaki hellasku '%s'", "parsing.long.expected": "<PERSON><PERSON><PERSON>", "parsing.long.invalid": "<PERSON><PERSON><PERSON> long<PERSON>ku '%s'", "parsing.quote.escape": "Uwaki shkeksokirain '%s' ine gjenhanena kakena", "parsing.quote.expected.end": "Uwaki vasu dan kiinni gjenhanukirain", "parsing.quote.expected.start": "<PERSON><PERSON>dan ka kirainsen had<PERSON>jena g<PERSON>nh<PERSON>", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Trelo raztelnen: %s", "permissions.requires.entity": "Ohang her per bruk dwaib<PERSON><PERSON> trengjena", "permissions.requires.player": "<PERSON><PERSON><PERSON><PERSON> musti her afta dwaib<PERSON>ko bruk", "potion.potency.1": "2s", "potion.potency.2": "3s", "potion.potency.3": "4s", "potion.potency.4": "5s", "potion.potency.5": "6s", "potion.whenDrank": "Li glug:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Trelo lifras: %s", "quickplay.error.invalid_identifier": "<PERSON><PERSON><PERSON> fynna velt mit antaena t<PERSON>ae", "quickplay.error.realm_connect": "Realm-<PERSON><PERSON><PERSON> humba", "quickplay.error.realm_permission": "Nai har kjokana per tsunaga afto Realm", "quickplay.error.title": "<PERSON>mba dan by<PERSON><PERSON><PERSON>", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms dekinai bruk mitt iskaatversio", "recipe.notFound": "Trelo mahtropos: %s", "recipe.toast.description": "Da se mahtroli", "recipe.toast.title": "Neo mahatropos lerena!", "record.nowPlaying": "Muzik ima: %s", "recover_world.bug_tracker": "An<PERSON><PERSON> zebagge", "recover_world.button": "Iskat reforma", "recover_world.done.failed": "Humba dan huskereforma danraz kara.", "recover_world.done.success": "Reforma jing dan!", "recover_world.done.title": "<PERSON><PERSON>", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "<PERSON>fto ishajka bli dan koske iskat lesa veltkaban \"%s\".\nTabun deki bengreforma velt andra jam<PERSON>l kara, os deki uopeta tsuite afto na ishajkatumam.", "recover_world.no_fallback": "Jam nil jamfal per bengreforma", "recover_world.restore": "<PERSON><PERSON> bengreforma", "recover_world.restoring": "<PERSON><PERSON> ima bengreforma velt...", "recover_world.state_entry": "Jamfal %s kara: ", "recover_world.state_entry.unknown": "knshirena", "recover_world.title": "Humba lesa velt", "recover_world.warning": "Humba lesa tsuitepunkt fu velt", "resourcePack.broken_assets": "PERPENA JOKU ZETING", "resourcePack.high_contrast.name": "Letstechigau Sejena", "resourcePack.load_fail": "Humba dan gjen<PERSON>a shirena<PERSON>ban", "resourcePack.programmer_art.name": "Risostaben fu Dwaibmadjin", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "Veltfansju<PERSON> bruk<PERSON>", "resourcePack.title": "Sentaku shire<PERSON>u", "resourcePack.vanilla.description": "Snano impla au asmi fu Minecraft", "resourcePack.vanilla.name": "<PERSON>no", "resourcepack.downloading": "<PERSON><PERSON>ada s<PERSON>", "resourcepack.progress": "Zesada implabaksu (%s MB)...", "resourcepack.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON> ima...", "screenshot.failure": "Dekinaidan kaku ekranriso: %s", "screenshot.success": "Ekranriso kaku<PERSON>na na %s", "selectServer.add": "Nasii server", "selectServer.defaultName": "Minecraft-server", "selectServer.delete": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "Vil keshite afto server?", "selectServer.deleteWarning": "%s' o<PERSON><PERSON>na koske viossashinutid! (<PERSON><PERSON> pitka tid!)", "selectServer.direct": "Tsunaga na zedvera", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(Taina k<PERSON>)", "selectServer.refresh": "Gjenlese", "selectServer.select": "Shkine server", "selectWorld.access_failure": "<PERSON><PERSON> rør velt", "selectWorld.allowCommands": "<PERSON><PERSON> u<PERSON>", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ta<PERSON><PERSON>a /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> ufnena impla", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON> z<PERSON> de lesa", "selectWorld.backupJoinSkipButton": "<PERSON>e shiru, da pinuno!", "selectWorld.backupQuestion.customized": "<PERSON><PERSON>t kawarena na dutropos ima nai laki", "selectWorld.backupQuestion.downgrade": "<PERSON><PERSON><PERSON> kawari versio per velt gammel made", "selectWorld.backupQuestion.experimental": "Velt na iskatfal-sentakuna nai dwaibma", "selectWorld.backupQuestion.snapshot": "<PERSON><PERSON><PERSON> vil lese afto velt?", "selectWorld.backupWarning.customized": "Na trist, sebja-ka<PERSON>na velt nai dwaibma na afto fal fu Minecraft. De<PERSON> hotja bruk afto velt au eshku gammeltropos fu ka ende jam, men al neomahena gaiatel nai bruk sebja-kawaritropos. Gomen grun deza!", "selectWorld.backupWarning.downgrade": "Afto velt dantid na %ssfal speljena; a du ima na %s spel. Hina-rømkawari fu velt tabun erperpati - de<PERSON>ai erzettai, ke lesjena dekiti os dwaibmati bra. Li vilti ende, davai zihab<PERSON>znets ufne!", "selectWorld.backupWarning.experimental": "Afto velt bruk iskatlik-sentakuna ka mirai dekti perpa. <PERSON>l zettai ka ying lesena au dwaibmena. Da rjuuchuu!", "selectWorld.backupWarning.snapshot": "Afto velt dantid spilena na %ss laskufal, a ima na %ss laskufal. Mahatsa ziha-bliznets per nai erspadati velt, li yamti z<PERSON>!", "selectWorld.bonusItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Mus mahaneo implatropos!", "selectWorld.conversion.tooltip": "Afto velt musti brukena na plusgammel laskufal (tato 1.6.4) per z<PERSON>va rø<PERSON>ri", "selectWorld.create": "Maha neo velt", "selectWorld.customizeType": "<PERSON><PERSON> s<PERSON>k", "selectWorld.dataPacks": "Implakaban", "selectWorld.data_read": "Lese veltimpla...", "selectWorld.delete": "<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "<PERSON><PERSON>i vil keshite afto velt?", "selectWorld.deleteWarning": "%s' o<PERSON><PERSON>na koske viossashinutid! (<PERSON><PERSON> pitka tid!)", "selectWorld.delete_failure": "Humba keshite velt", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON>", "selectWorld.edit.backupCreated": "<PERSON><PERSON><PERSON><PERSON>ne dan: %s", "selectWorld.edit.backupFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backupFolder": "Shkine kaban fu velt-zihaufne", "selectWorld.edit.backupSize": "sturatai: %s MB", "selectWorld.edit.export_worldgen_settings": "Ufne sentakuna fu veltmaha-tropos", "selectWorld.edit.export_worldgen_settings.failure": "Humbadan na ufne", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON>nen<PERSON>", "selectWorld.edit.openFolder": "<PERSON><PERSON>", "selectWorld.edit.optimize": "<PERSON><PERSON><PERSON><PERSON><PERSON> velt", "selectWorld.edit.resetIcon": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.save": "Ufne", "selectWorld.edit.title": "<PERSON><PERSON> velt", "selectWorld.enterName": "<PERSON><PERSON><PERSON>namae", "selectWorld.enterSeed": "<PERSON><PERSON><PERSON> per veltsintua", "selectWorld.experimental": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Trænena dvaibma na wigel: %s", "selectWorld.experimental.details.title": "Trænasma fu dvaibmasma na wigel", "selectWorld.experimental.message": "Mietalik tsa!\nAfto sentakufal træng dwaibma ke ende anergoena. Velt tabun spada, perpa, os mit miraiversho dwaibmanai.", "selectWorld.experimental.title": "Blogeta fu Is<PERSON>-d<PERSON><PERSON><PERSON>", "selectWorld.experiments": "Iskatdai", "selectWorld.experiments.info": "Iskat tte tabun miraiti tel fu spel. Bite zihalik grun perpadeki titing. Iskatdai dekinai keshitena za velt rmmahena.", "selectWorld.futureworld.error.text": "<PERSON><PERSON> lesedeza slucha dan mit velt ke jam na obaneo versio. Afto achorhaaste iskat, de stakkar gomen grun nai dwaibma dan.", "selectWorld.futureworld.error.title": "Ein ishaika slucha!", "selectWorld.gameMode": "Spiltropos", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "<PERSON>a na bidesvonat<PERSON>os, mena dado nai deki bli nasiena os keshitena.", "selectWorld.gameMode.adventure.line1": "Sama na bidesvonatropos, men dado de<PERSON>ai", "selectWorld.gameMode.adventure.line2": "<PERSON><PERSON>ena os kesh<PERSON>na", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON> sofnai, ma<PERSON>, h<PERSON><PERSON><PERSON><PERSON>, au shire<PERSON>. <PERSON><PERSON>, har so<PERSON><PERSON><PERSON><PERSON><PERSON>, au nai deki bli pwanena na morko.", "selectWorld.gameMode.creative.line1": "Utenele brukshtof au djijuu ljetaa au", "selectWorld.gameMode.creative.line2": "perpa dado tsatainde", "selectWorld.gameMode.hardcore": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "Bidesvonatropos festena na 'Haaste'. Nai deki gjensintua li shinu.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON> bid<PERSON>-tropos, festa na letstehaaste", "selectWorld.gameMode.hardcore.line2": "hasteatai au mono ein <PERSON>z", "selectWorld.gameMode.spectator": "Sedjin", "selectWorld.gameMode.spectator.info": "Du deki se men naj rør.", "selectWorld.gameMode.spectator.line1": "<PERSON>ki se men rør nai", "selectWorld.gameMode.survival": "Bidesvona", "selectWorld.gameMode.survival.info": "<PERSON><PERSON><PERSON><PERSON><PERSON> ine ain straanivelt ka ine du deki huomimaha, tings<PERSON><PERSON>, maha, au kri<PERSON> morko.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON> bruk<PERSON><PERSON><PERSON>, maha, saada", "selectWorld.gameMode.survival.line2": "mestari-, zdorva-, nampulapatai", "selectWorld.gameRules": "Spilruuru", "selectWorld.import_worldgen_settings": "<PERSON><PERSON>", "selectWorld.import_worldgen_settings.failure": "Uwakidan koske lesa sentakuna", "selectWorld.import_worldgen_settings.select_file": "Sentaku zeting fu sentakuna (.json)", "selectWorld.incompatible.description": "Afto velt dekinai na afto versho.\nDan spelena na versho %s.", "selectWorld.incompatible.info": "Brukkinai versho: %s", "selectWorld.incompatible.title": "Brukkinai versho", "selectWorld.incompatible.tooltip": "Afto velt dekinai lesena grun mahena mit chigau versho.", "selectWorld.incompatible_series": "<PERSON><PERSON>a na neviaf versho", "selectWorld.load_folder_access": "<PERSON><PERSON><PERSON> lese os rør kaban doko spilvelt festaharena!", "selectWorld.loading_list": "Lesa tumam fu velt", "selectWorld.locked": "<PERSON><PERSON> grun <PERSON><PERSON>-dwaib<PERSON>z", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON>", "selectWorld.mapFeatures.info": "Stat, spaadenacjip, auau.", "selectWorld.mapType": "Veltfal", "selectWorld.mapType.normal": "<PERSON><PERSON><PERSON>", "selectWorld.moreWorldOptions": "<PERSON><PERSON>ge kawarjena fu velt...", "selectWorld.newWorld": "Neo velt", "selectWorld.recreate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "<PERSON><PERSON>t kawarjena nai ende eshku na afto fal fu Minecraft. <PERSON><PERSON> iskat gyenmaha na sama pie au impla, men al terfal kawari keshtena. Gomen grun deza!", "selectWorld.recreate.customized.title": "<PERSON>elt ke kawarena na dutropos imakara la<PERSON>ai", "selectWorld.recreate.error.text": "<PERSON><PERSON> slucha uso koske iskat gyenmaha velt.", "selectWorld.recreate.error.title": "Ein ishaika slucha!", "selectWorld.resource_load": "Razgotova shtof...", "selectWorld.resultFolder": "<PERSON><PERSON><PERSON> mirai her made:", "selectWorld.search": "suha velt", "selectWorld.seedInfo": "Ohare per zari laskupie", "selectWorld.select": "Spel na sentakena velt", "selectWorld.targetFolder": "Ufnekaban: %s", "selectWorld.title": "Sentaku velt", "selectWorld.tooltip.fromNewerVersion1": "Velt ufnena na plusneo fal,", "selectWorld.tooltip.fromNewerVersion2": "li lese velt, de<PERSON>ti r<PERSON>!", "selectWorld.tooltip.snapshot1": "Wasunai-tsa mah zihabliznets fu afto velt", "selectWorld.tooltip.snapshot2": "de auki inne afto versio.", "selectWorld.unable_to_load": "<PERSON><PERSON><PERSON> dan lesa veltara", "selectWorld.version": "Fallasku:", "selectWorld.versionJoinButton": "<PERSON><PERSON> hotja", "selectWorld.versionQuestion": "<PERSON><PERSON><PERSON> vil lese afto velt?", "selectWorld.versionUnknown": "nai shirena", "selectWorld.versionWarning": "<PERSON>o velt letste kvelliik spilena mitt fallasku %s - li ufne, deki joku bli warumahena!", "selectWorld.warning.deprecated.question": "<PERSON><PERSON> dwai<PERSON>ma brukena er<PERSON>, de jokumirai perpati. Vilti ende?", "selectWorld.warning.deprecated.title": "Vikti! Afto kawarina bruk ergammel dwaibma", "selectWorld.warning.experimental.question": "Afto kawarina jam na iskat<PERSON>l, de tabun jokumirai dwaibmanai. Vilti ende?", "selectWorld.warning.experimental.title": "Vikti! Afto kawarina bruk dwaibma na iskatfal", "selectWorld.warning.lowDiskSpace.description": "Jam akote nil plus oharezma na kompju fudu.\nLi bruk al oharezma koske spil, velt tabun erperpena mirai.", "selectWorld.warning.lowDiskSpace.title": "<PERSON><PERSON>ts<PERSON>! <PERSON><PERSON>ge oh<PERSON>z<PERSON> na ufnekrais!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "<PERSON><PERSON> k<PERSON>m", "sleep.not_possible": "Nil ilta dekiti bystra-bides afto naht", "sleep.players_sleeping": "%s/%s spilzhin na kola", "sleep.skipping_night": "Na kola bides afto nakht", "slot.only_single_allowed": "Mono ein gø<PERSON>oi laki, sada dan '%s'", "slot.unknown": "<PERSON><PERSON><PERSON>n na<PERSON> '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Asmi/Pogoda", "soundCategory.block": "<PERSON><PERSON>", "soundCategory.hostile": "<PERSON><PERSON><PERSON>", "soundCategory.master": "Ogoeatai napaksu", "soundCategory.music": "Lid", "soundCategory.neutral": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.player": "Spildjin", "soundCategory.record": "S<PERSON><PERSON><PERSON><PERSON><PERSON>/nuotidado", "soundCategory.ui": "UI", "soundCategory.voice": "<PERSON><PERSON>", "soundCategory.weather": "Pogoda", "spectatorMenu.close": "<PERSON><PERSON>", "spectatorMenu.next_page": "Miraileht<PERSON>", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.root.prompt": "Tasta joku tastanen per sentaku jew<PERSON>, de gjentasta per bruk.", "spectatorMenu.team_teleport": "Pal klanidjin made", "spectatorMenu.team_teleport.prompt": "Sentaku spildjinklani ka pal hei made", "spectatorMenu.teleport": "Popal spildjin made", "spectatorMenu.teleport.prompt": "Sentaku spildjin per pal sore made", "stat.generalButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.itemsButton": "<PERSON>g", "stat.minecraft.animals_bred": "<PERSON><PERSON><PERSON> j<PERSON>", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON> grun tsubasa", "stat.minecraft.bell_ring": "Zamøzena <PERSON>lin<PERSON>", "stat.minecraft.boat_one_cm": "Pitkatai na cjip", "stat.minecraft.clean_armor": "Jerkatkodjatel soodjena", "stat.minecraft.clean_banner": "<PERSON>lak<PERSON> sod<PERSON>", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON>", "stat.minecraft.climb_one_cm": "Pitkatai obunaleojena", "stat.minecraft.crouch_one_cm": "Pitkatai mit krøltatsutropos", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "Komuskejena a<PERSON>", "stat.minecraft.damage_dealt": "Arkaa<PERSON> na<PERSON>na", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON> antaena (namena)", "stat.minecraft.damage_dealt_resisted": "Arka nasii (eshku)", "stat.minecraft.damage_resisted": "Arka eshku", "stat.minecraft.damage_taken": "Arka made", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON> atai", "stat.minecraft.drop": "Spadena ting", "stat.minecraft.eat_cake_slice": "Tortatel namena", "stat.minecraft.enchant_item": "<PERSON><PERSON><PERSON> ting", "stat.minecraft.fall_one_cm": "Pitkatai spadena", "stat.minecraft.fill_cauldron": "Tofdai <PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON> festena", "stat.minecraft.fly_one_cm": "Pitkatai ljetajena", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Pit<PERSON><PERSON> grun hengest", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON><PERSON> sadant<PERSON>", "stat.minecraft.interact_with_anvil": "Brukzma na bigorna", "stat.minecraft.interact_with_beacon": "Kirkasflakka bruk<PERSON>na", "stat.minecraft.interact_with_blast_furnace": "<PERSON>apa<PERSON><PERSON><PERSON> br<PERSON>", "stat.minecraft.interact_with_brewingstand": "Taikaviskenaplas bruk<PERSON>", "stat.minecraft.interact_with_campfire": "<PERSON><PERSON><PERSON><PERSON> br<PERSON>", "stat.minecraft.interact_with_cartography_table": "Ka<PERSON>ergos<PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_furnace": "Brukraz fu honobet", "stat.minecraft.interact_with_grindstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_lectern": "Nasiitatsu na libre brukjena", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "<PERSON><PERSON><PERSON><PERSON> stol bruk<PERSON>na", "stat.minecraft.interact_with_smoker": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON>", "stat.minecraft.jump": "Pal", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.minecart_one_cm": "Pitkata<PERSON> grun rofai", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "Bad<PERSON><PERSON><PERSON> aukiena", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.pig_one_cm": "Pit<PERSON><PERSON> nasvinyaa", "stat.minecraft.play_noteblock": "Zamøzena nuotidado", "stat.minecraft.play_record": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.play_time": "Speltid", "stat.minecraft.player_kills": "Spildjin vrasena", "stat.minecraft.pot_flower": "Rupneting klinjena", "stat.minecraft.raid_trigger": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "stat.minecraft.raid_win": "<PERSON><PERSON><PERSON><PERSON><PERSON>a jingena", "stat.minecraft.sleep_in_bed": "Razara na betkola", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON><PERSON>lak<PERSON> tidatai", "stat.minecraft.sprint_one_cm": "Pitkatai na djingsai", "stat.minecraft.strider_one_cm": "Pitkatai na jalakadjin", "stat.minecraft.swim_one_cm": "Pitkata<PERSON>", "stat.minecraft.talked_to_villager": "<PERSON>t stat<PERSON><PERSON> hanena", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON> s<PERSON>", "stat.minecraft.time_since_death": "Tid ima made lecteraz tjinu kara", "stat.minecraft.time_since_rest": "Tid ima made lecteraz maha<PERSON>ji kara", "stat.minecraft.total_world_time": "Tid mit velt auki", "stat.minecraft.traded_with_villager": "Kuupahokraz mit statdjin", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.tune_noteblock": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.use_cauldron": "<PERSON><PERSON><PERSON> saadena tofdai kara", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON> najalaka oba ishke", "stat.minecraft.walk_one_cm": "Pitkatai na jalaka", "stat.minecraft.walk_under_water_one_cm": "Pitkatai na jalaka ine ishke", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Perpen<PERSON>z", "stat_type.minecraft.crafted": "Mahenaraz", "stat_type.minecraft.dropped": "Spadena", "stat_type.minecraft.killed": "Du vras dan %s %s", "stat_type.minecraft.killed.none": "Du nilraz vras %s", "stat_type.minecraft.killed_by": "%s vras du %s raz", "stat_type.minecraft.killed_by.none": "%s nilraz vras du", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.used": "Brukraz", "stats.none": "-", "structure_block.button.detect_size": "ANAISTIA", "structure_block.button.load": "LESE", "structure_block.button.save": "UFNE", "structure_block.custom_data": "Namaenen fu impla na dutropos", "structure_block.detect_size": "Farza storatai au dokozma fu zdanie:", "structure_block.hover.corner": "Ik: %s", "structure_block.hover.data": "Impla: %s", "structure_block.hover.load": "Lesa: %s", "structure_block.hover.save": "Ufne: %s", "structure_block.include_entities": "<PERSON><PERSON><PERSON> s<PERSON>:", "structure_block.integrity": "Polfal heltropos au zarilasku", "structure_block.integrity.integrity": "Polfal helzma", "structure_block.integrity.seed": "Zarilasku per polfal", "structure_block.invalid_structure_name": "Uwaki polfal namae '%s'", "structure_block.load_not_found": "Polfal '%s' dekinai saadena", "structure_block.load_prepare": "Polfal '%s' gotov per nasi ine plas", "structure_block.load_success": "Polfal lesena '%s' kara", "structure_block.mode.corner": "Ik", "structure_block.mode.data": "Impla", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Ufne", "structure_block.mode_info.corner": "Ikfal--nasijenaplas au chiistuurmahaklaar", "structure_block.mode_info.data": "Implatropos: s<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "structure_block.mode_info.load": "Lesafal - zeting kara lesa", "structure_block.mode_info.save": "Ufnetropos - zeting made kaku", "structure_block.position": "Hadjip<PERSON> kara", "structure_block.position.x": "x-<PERSON><PERSON> had<PERSON><PERSON><PERSON> kara", "structure_block.position.y": "y-a<PERSON> had<PERSON>las kara", "structure_block.position.z": "z-<PERSON><PERSON> hadjiplas kara", "structure_block.save_failure": "Humbadan ufne polfal '%s'", "structure_block.save_success": "Polfal ufnena na '%s'", "structure_block.show_air": "<PERSON><PERSON><PERSON> sekinai dado:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON> treabad-god<PERSON>:", "structure_block.size": "Polfal sturatai", "structure_block.size.x": "polfal x-sturatai", "structure_block.size.y": "polfal y-sturatai", "structure_block.size.z": "polfal z-sturatai", "structure_block.size_failure": "Humbadan fshto stuuratai fu polfal. Da nasii ik mitt sama polfalnamae", "structure_block.size_success": "Jing lera storatai fu %s", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "<PERSON><PERSON> p<PERSON>", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON> zan", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "<PERSON><PERSON><PERSON>anne<PERSON>", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON> laulu", "subtitles.block.anvil.destroy": "<PERSON>orn<PERSON> erperpena", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> potulla", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON> brukena", "subtitles.block.barrel.close": "Bad<PERSON><PERSON>i auki", "subtitles.block.barrel.open": "Badjeldai kiini", "subtitles.block.beacon.activate": "<PERSON><PERSON><PERSON><PERSON><PERSON> hadji d<PERSON>", "subtitles.block.beacon.ambient": "Kirkasflakka møm", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> dwaibma", "subtitles.block.beacon.power_select": "<PERSON><PERSON><PERSON><PERSON><PERSON> zeus sentakendan", "subtitles.block.beehive.drip": "Mjalt spada", "subtitles.block.beehive.enter": "<PERSON><PERSON><PERSON> s<PERSON> vomi", "subtitles.block.beehive.exit": "<PERSON>itt<PERSON> s<PERSON> vomi", "subtitles.block.beehive.shear": "Tsamb<PERSON><PERSON><PERSON> z<PERSON>", "subtitles.block.beehive.work": "<PERSON><PERSON>a ergo", "subtitles.block.bell.resonate": "Zamklinje tacuzam", "subtitles.block.bell.use": "Zamklinje zam", "subtitles.block.big_dripleaf.tilt_down": "Ishkel<PERSON>ti krungut oba", "subtitles.block.big_dripleaf.tilt_up": "Is<PERSON><PERSON><PERSON><PERSON> krungut una", "subtitles.block.blastfurnace.fire_crackle": "Vapatoreutin perpazam", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bub-bub", "subtitles.block.bubble_column.bubble_pop": "Luftmjacj perpa", "subtitles.block.bubble_column.upwards_ambient": "Luftmjacj skjoi", "subtitles.block.bubble_column.upwards_inside": "Luftmjacj bidra", "subtitles.block.bubble_column.whirlpool_ambient": "Luftmjacj visk", "subtitles.block.bubble_column.whirlpool_inside": "Luftmjah shu", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.block.campfire.crackle": "Kraishono razkiva", "subtitles.block.candle.crackle": "<PERSON><PERSON>", "subtitles.block.candle.extinguish": "<PERSON><PERSON> jame <PERSON>t", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.locked": "<PERSON>ksu festena", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> flan", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON><PERSON><PERSON> rupne", "subtitles.block.comparator.click": "<PERSON>ih<PERSON> ding", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> ohare", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.block.composter.ready": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>nam", "subtitles.block.conduit.activate": "<PERSON><PERSON> hadzhi dva<PERSON>ma", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON> razasa", "subtitles.block.conduit.attack.target": "Tsunagasen slag", "subtitles.block.conduit.deactivate": "<PERSON><PERSON> d<PERSON>", "subtitles.block.copper_bulb.turn_off": "<PERSON><PERSON><PERSON> kruska kini", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON><PERSON> kruska auki", "subtitles.block.copper_trapdoor.close": "J<PERSON>ka<PERSON><PERSON><PERSON> kini", "subtitles.block.copper_trapdoor.open": "Jalakad<PERSON>a auki", "subtitles.block.crafter.craft": "<PERSON><PERSON><PERSON> maha", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON> humba maha", "subtitles.block.creaking_heart.hurt": "Ker f'Djidodjin arka", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON> zam", "subtitles.block.creaking_heart.spawn": "Ker f'<PERSON><PERSON><PERSON><PERSON> asa", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON> zam", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.block.decorated_pot.insert_fail": "Kligne kiva", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON> perpa", "subtitles.block.dispenser.dispense": "<PERSON><PERSON><PERSON> anta", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON> shreinen", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Taikas<PERSON><PERSON> brukena", "subtitles.block.end_portal.spawn": "<PERSON><PERSON><PERSON><PERSON> auki", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON> festena", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON> kini", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON> chicheu", "subtitles.block.eyeblossom.open": "<PERSON><PERSON><PERSON> auki", "subtitles.block.fence_gate.toggle": "Vjetkadvera shreinen", "subtitles.block.fire.ambient": "<PERSON>oo kiva", "subtitles.block.fire.extinguish": "<PERSON><PERSON> keshtena", "subtitles.block.firefly_bush.idle": "Kirkasbagge bzzt", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON>", "subtitles.block.furnace.fire_crackle": "Honobet razcrjeg", "subtitles.block.generic.break": "<PERSON><PERSON> perpena", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "Jalakanen", "subtitles.block.generic.hit": "<PERSON>o bli perpa", "subtitles.block.generic.place": "<PERSON><PERSON>", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "<PERSON>uti chamena", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON><PERSON> kiva", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON>", "subtitles.block.iron_trapdoor.close": "Poldvera auki", "subtitles.block.iron_trapdoor.open": "Poldvera kini", "subtitles.block.lava.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> strela l<PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.block.note_block.note": "Nwotodado zam", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "Zehant ugoki", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Jhotishke pluinen inhe tofdai", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON><PERSON> pluinen inhe tofdai", "subtitles.block.pointed_dripstone.land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON><PERSON> visa<PERSON>a", "subtitles.block.portal.travel": "Dveradaizam bli pinuno", "subtitles.block.portal.trigger": "Dver<PERSON><PERSON><PERSON> bli ogoe", "subtitles.block.pressure_plate.click": "Jalakapresmi <PERSON>", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "Kirkasvjet<PERSON> tsis", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> visa<PERSON>a", "subtitles.block.respawn_anchor.charge": "Gjensintuadado saada bruktiid", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bruk<PERSON>", "subtitles.block.respawn_anchor.set_spawn": "Gjensintuadado festa gjensintuaplas", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Sculk rasbwapa", "subtitles.block.sculk.spread": "Sculk gele", "subtitles.block.sculk_catalyst.bloom": "Sculk errufnedvaibma mwerm", "subtitles.block.sculk_sensor.clicking": "Sculk aistjadvaibma hadzhi raskiva", "subtitles.block.sculk_sensor.clicking_stop": "Sculk aistjadvaib<PERSON> o<PERSON> raskiva", "subtitles.block.sculk_shrieker.shriek": "Sculk shreidvaib<PERSON> shr<PERSON>i", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> kini", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> au<PERSON>", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON><PERSON> kiva", "subtitles.block.smithing_table.use": "Razjerkatstol brukena", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.crack": "Jaitso f'nioidjin uperpanen", "subtitles.block.sniffer_egg.hatch": "Jaitso f'nioidjin uperpa", "subtitles.block.sniffer_egg.plop": "Nioidjin promp", "subtitles.block.sponge.absorb": "Glubka slorp", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON> lasa", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "Poldvera iiii", "subtitles.block.trial_spawner.about_to_spawn_item": "<PERSON><PERSON><PERSON> gotova sebja", "subtitles.block.trial_spawner.ambient": "Iskatdai-sintuabma razkrek", "subtitles.block.trial_spawner.ambient_charged": "Nafacz<PERSON> iskatdai-sintuabma razkrek", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Nafa ernam iskatdai-sintuabma", "subtitles.block.trial_spawner.close_shutter": "Iskatdai-sin<PERSON><PERSON><PERSON> kini", "subtitles.block.trial_spawner.detect_player": "Iskatdai-sintuabma saada zeus", "subtitles.block.trial_spawner.eject_item": "Iskatdai-sintuabma jit gavatara", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Iskatdai-sintuabma auki", "subtitles.block.trial_spawner.spawn_item": "<PERSON><PERSON><PERSON><PERSON> ting spada", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON><PERSON><PERSON><PERSON> ting bli", "subtitles.block.trial_spawner.spawn_mob": "Iskatdai-sintu<PERSON><PERSON> sintua <PERSON>", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON> tsam", "subtitles.block.vault.activate": "Ziharu<PERSON> pozjot", "subtitles.block.vault.ambient": "Ziharum razkiva", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON><PERSON> kini", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON><PERSON> jame z<PERSON>t", "subtitles.block.vault.eject_item": "<PERSON><PERSON><PERSON><PERSON> jit gavat", "subtitles.block.vault.insert_item": "Ziharum knklucjena", "subtitles.block.vault.insert_item_fail": "Ziharum ende klucjena", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON><PERSON> auki", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "Glubka bli djotri", "subtitles.chiseled_bookshelf.insert": "Libre nasijena", "subtitles.chiseled_bookshelf.insert_enchanted": "Taikalibre nasiena", "subtitles.chiseled_bookshelf.take": "Libre saada dan", "subtitles.chiseled_bookshelf.take_enchanted": "Tai<PERSON><PERSON><PERSON> sa<PERSON>na", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON><PERSON>wang", "subtitles.entity.allay.ambient_with_item": "Apudjin suxa", "subtitles.entity.allay.ambient_without_item": "Apudjin vil", "subtitles.entity.allay.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.allay.hurt": "Apudjin arka", "subtitles.entity.allay.item_given": "<PERSON><PERSON><PERSON><PERSON> fliere", "subtitles.entity.allay.item_taken": "Apudjin glau", "subtitles.entity.allay.item_thrown": "Apudjin jit", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>h", "subtitles.entity.armadillo.brush": "Komuske<PERSON><PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON>m", "subtitles.entity.armadillo.hurt": "Ko<PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.armadillo.hurt_reduced": "Komuskemysh eshku sebja", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.roll": "Ko<PERSON><PERSON><PERSON>sh bli krais", "subtitles.entity.armadillo.scute_drop": "Komuskemysh spaada komuskepone", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "Joku spada", "subtitles.entity.arrow.hit": "Strela slag", "subtitles.entity.arrow.hit_player": "Spildjin slagjena", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> ji<PERSON>a", "subtitles.entity.axolotl.attack": "Atheksetl slag", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.axolotl.hurt": "Atheksetl arka", "subtitles.entity.axolotl.idle_air": "Atheksetl pi", "subtitles.entity.axolotl.idle_water": "Atheksetl pi", "subtitles.entity.axolotl.splash": "Atheks<PERSON><PERSON> t<PERSON>h", "subtitles.entity.axolotl.swim": "Atheksetl ishkeljetaa", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.hurt": "Ljetamys arka", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.ambient": "Melitta bzz", "subtitles.entity.bee.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.bee.loop": "Melitta bzz", "subtitles.entity.bee.loop_aggressive": "<PERSON>itta bzz mit bøøze", "subtitles.entity.bee.pollinate": "Melitta bzz mit glau", "subtitles.entity.bee.sting": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Honodjin luft", "subtitles.entity.blaze.burn": "Honodjin razkiva", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.blaze.hurt": "Honodjin arka", "subtitles.entity.blaze.shoot": "Honod<PERSON> strela", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON> rigigi", "subtitles.entity.bogged.death": "<PERSON>gg<PERSON> shinu", "subtitles.entity.bogged.hurt": "Boggeta arka", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON> sebja", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.breeze.deflect": "Luftsjal borteslag", "subtitles.entity.breeze.hurt": "Luftsjal arka", "subtitles.entity.breeze.idle_air": "Lu<PERSON><PERSON><PERSON> ljeta", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON><PERSON> pal", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON><PERSON> strela", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON><PERSON>lu", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.wind_burst": "Luftbamba pobam", "subtitles.entity.camel.ambient": "<PERSON><PERSON> borbor", "subtitles.entity.camel.dash": "<PERSON><PERSON> jit sebja", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON> shinu", "subtitles.entity.camel.eat": "<PERSON><PERSON> nam", "subtitles.entity.camel.hurt": "<PERSON><PERSON> arka", "subtitles.entity.camel.saddle": "Dyrisu festena", "subtitles.entity.camel.sit": "<PERSON><PERSON>", "subtitles.entity.camel.stand": "Sangju tatsu", "subtitles.entity.camel.step": "<PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON> jalaka oba san", "subtitles.entity.cat.ambient": "<PERSON><PERSON> mjau", "subtitles.entity.cat.beg_for_food": "Kot razspöre", "subtitles.entity.cat.death": "<PERSON><PERSON> shinu", "subtitles.entity.cat.eat": "Kot nam", "subtitles.entity.cat.hiss": "<PERSON><PERSON> t<PERSON>h", "subtitles.entity.cat.hurt": "Kot arka", "subtitles.entity.cat.purr": "Kot krkr", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON> kuk", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> sintua jaitso", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.cow.ambient": "<PERSON><PERSON> moo", "subtitles.entity.cow.death": "<PERSON><PERSON> sjinu", "subtitles.entity.cow.hurt": "Gju arka", "subtitles.entity.cow.milk": "Gju njujena", "subtitles.entity.creaking.activate": "Djidodjin boze", "subtitles.entity.creaking.ambient": "Djidodjin krrk", "subtitles.entity.creaking.attack": "Djidodjin slag", "subtitles.entity.creaking.deactivate": "Djidodjin panpi", "subtitles.entity.creaking.death": "Djidod<PERSON> shinu", "subtitles.entity.creaking.freeze": "Djidodjin jamete", "subtitles.entity.creaking.spawn": "Djidodjin sintua", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Djidodjin razkiva", "subtitles.entity.creaking.unfreeze": "Djidodjin skoi", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.creeper.hurt": "Creeper arga", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> tsish", "subtitles.entity.dolphin.ambient": "Maresvinja pi", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.attack": "Maresvinja harza", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.dolphin.eat": "Maresvinja nam", "subtitles.entity.dolphin.hurt": "Maresvinja arka", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON><PERSON> pal", "subtitles.entity.dolphin.play": "Maresvinja spil", "subtitles.entity.dolphin.splash": "Maresvinja slag ishke", "subtitles.entity.dolphin.swim": "Maresvinja <PERSON>kel<PERSON>", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> fliire", "subtitles.entity.donkey.angry": "<PERSON><PERSON> shrei", "subtitles.entity.donkey.chest": "Esel saada Baksu", "subtitles.entity.donkey.death": "<PERSON><PERSON> shinu", "subtitles.entity.donkey.eat": "<PERSON>sel nam", "subtitles.entity.donkey.hurt": "E<PERSON> arka", "subtitles.entity.donkey.jump": "<PERSON><PERSON> pop<PERSON>", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> jit sa<PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.egg.throw": "Jaitso ljetaa", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.curse": "Gammeljewalt nafa", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.elder_guardian.hurt": "Gammeljewalt arka", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> yiit", "subtitles.entity.ender_eye.death": "Ender me spada", "subtitles.entity.ender_eye.launch": "Ender me jitena", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> peitra ljeta", "subtitles.entity.enderman.ambient": "<PERSON>i fu End<PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON> er<PERSON>i", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.enderman.teleport": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.endermite.ambient": "Endermite gira", "subtitles.entity.endermite.death": "Endermite shinu", "subtitles.entity.endermite.hurt": "Endermite arka", "subtitles.entity.evoker.ambient": "Taikan<PERSON>zhi<PERSON> møm", "subtitles.entity.evoker.cast_spell": "Taikantadjin taikanta", "subtitles.entity.evoker.celebrate": "Taikantadjin iwai", "subtitles.entity.evoker.death": "Taikantadjin shinu", "subtitles.entity.evoker.hurt": "Taikantadjin arka", "subtitles.entity.evoker.prepare_attack": "Taikantadjin gotov taikaslag", "subtitles.entity.evoker.prepare_summon": "Taikan<PERSON><PERSON> gotov taika mahavona", "subtitles.entity.evoker.prepare_wololo": "Taikantadjin gotov razertønni", "subtitles.entity.evoker_fangs.attack": "Hammas<PERSON> kap<PERSON>p", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.blast": "Zan fu iwaibam", "subtitles.entity.firework_rocket.launch": "Iwaibam poljeta", "subtitles.entity.firework_rocket.twinkle": "Iwaibam razkiva", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "Gjensaadena sa<PERSON>amjah", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> bli bose", "subtitles.entity.fox.ambient": "Kitsne pi", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> hammas", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.fox.eat": "Kitsne nam", "subtitles.entity.fox.hurt": "Kitsne arka", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> shrei", "subtitles.entity.fox.sleep": "Kitsne zzzu", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> pokran", "subtitles.entity.fox.teleport": "Kitsne pougoki", "subtitles.entity.frog.ambient": "Baba zam", "subtitles.entity.frog.death": "Baba shinu", "subtitles.entity.frog.eat": "Baba nam", "subtitles.entity.frog.hurt": "Baba arka", "subtitles.entity.frog.lay_spawn": "Baba nasi eg", "subtitles.entity.frog.long_jump": "<PERSON> pal", "subtitles.entity.generic.big_fall": "Joku spada", "subtitles.entity.generic.burn": "<PERSON><PERSON>", "subtitles.entity.generic.death": "<PERSON><PERSON>", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Nam", "subtitles.entity.generic.explode": "Bambam", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON> keshtena", "subtitles.entity.generic.hurt": "Jam arka", "subtitles.entity.generic.small_fall": "Jam udacjispada", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON> bam", "subtitles.entity.generic.swim": "Inn<PERSON> is<PERSON>", "subtitles.entity.generic.wind_burst": "Luftbamba bam", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.ghast.hurt": "Ghast arka", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> strela", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.entity.glow_item_frame.break": "Kiragira ekr<PERSON>n perpa", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON> na<PERSON>a", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON> oh<PERSON>na", "subtitles.entity.glow_item_frame.rotate_item": "Tinn na kiragira ekràn krungut", "subtitles.entity.glow_squid.ambient": "<PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.glow_squid.squirt": "<PERSON><PERSON><PERSON>n jit farge", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> baha", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.goat.eat": "Øchke nam", "subtitles.entity.goat.horn_break": "Øchke nor spara", "subtitles.entity.goat.hurt": "Øchke arka", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> pal", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> njujena", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON>ke raspwam jalaka", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.ambient_land": "Zihadjin tsuabasuru", "subtitles.entity.guardian.attack": "Zihadjin strela", "subtitles.entity.guardian.death": "Zih<PERSON>jin shinu", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> pokiva", "subtitles.entity.guardian.hurt": "Zihadjin arka", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> borbor", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> borbor bosemente", "subtitles.entity.hoglin.attack": "Hoglin slag", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.hoglin.hurt": "Hoglin arka", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> s<PERSON>o", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> iha", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> iha", "subtitles.entity.horse.armor": "Hengest potrag komuske", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> hengi", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.horse.eat": "Hengest nam", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.horse.hurt": "Hengest arka", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> pal", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON><PERSON> posuwaru", "subtitles.entity.husk.ambient": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON> bli odarem", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.husk.hurt": "Utenshal arka", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "Risotaikadjin antaa taika", "subtitles.entity.illusioner.death": "Risotaikadjin shinu", "subtitles.entity.illusioner.hurt": "Risotaikadjin arka", "subtitles.entity.illusioner.mirror_move": "Risotaikadjin pogoki", "subtitles.entity.illusioner.prepare_blindness": "Risotaika<PERSON><PERSON> gotova taika mevrasena", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gotova taika <PERSON>o", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON><PERSON><PERSON><PERSON> perpena", "subtitles.entity.iron_golem.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.iron_golem.repair": "Jerkatrobotto reformena", "subtitles.entity.item.break": "Ting perpa", "subtitles.entity.item.pickup": "Ting pospada", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> perpena", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> oh<PERSON>", "subtitles.entity.item_frame.rotate_item": "Tinn na ekràn krungut", "subtitles.entity.leash_knot.break": "Nuitoishi perpa", "subtitles.entity.leash_knot.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.impact": "Zeusstrelaa slag", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON> og<PERSON>", "subtitles.entity.llama.ambient": "<PERSON> vivi", "subtitles.entity.llama.angry": "<PERSON> vivi bøze", "subtitles.entity.llama.chest": "<PERSON> festena mit baksu", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON> nam", "subtitles.entity.llama.hurt": "<PERSON> arka", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON> trag helena<PERSON>a", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.magma_cube.hurt": "Zjotsewas arka", "subtitles.entity.magma_cube.squish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Rofai shkoi", "subtitles.entity.mooshroom.convert": "Mooshroom pokawari", "subtitles.entity.mooshroom.eat": "Mooshroom nam", "subtitles.entity.mooshroom.milk": "Mooshroom njujena", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom njujena furilik", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON> iha", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> bø<PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> trag baksu", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.mule.eat": "Hengesel nam", "subtitles.entity.mule.hurt": "Hengesel arka", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON> pal", "subtitles.entity.painting.break": "Riso perpa", "subtitles.entity.painting.place": "Riso nasi<PERSON>", "subtitles.entity.panda.aggressive_ambient": "Karhukot zam hana", "subtitles.entity.panda.ambient": "Karhukot luftdai", "subtitles.entity.panda.bite": "<PERSON><PERSON><PERSON><PERSON> hammas", "subtitles.entity.panda.cant_breed": "Karhu<PERSON> shrei", "subtitles.entity.panda.death": "<PERSON><PERSON><PERSON><PERSON> tjinu", "subtitles.entity.panda.eat": "Karhukot nam", "subtitles.entity.panda.hurt": "Karhukot arka", "subtitles.entity.panda.pre_sneeze": "Kar<PERSON><PERSON> hanakiva", "subtitles.entity.panda.sneeze": "Karhu<PERSON> hanashrei", "subtitles.entity.panda.step": "Karhukot pojalaka", "subtitles.entity.panda.worried_ambient": "Karhukot zam trist", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON><PERSON> hanu", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> s<PERSON>u", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> luft", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creaking": "Hanufugel krrk", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON><PERSON>h", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>i", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON><PERSON> gira", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON><PERSON> shrei", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON><PERSON> rase", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON><PERSON>h", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON><PERSON>h", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "Hanufugel rovo", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON><PERSON>anu", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON><PERSON> bli bose", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.phantom.ambient": "Eledjin nit<PERSON>ikaogoe", "subtitles.entity.phantom.bite": "On hammas", "subtitles.entity.phantom.death": "On shinu", "subtitles.entity.phantom.flap": "On kiva tsubasa", "subtitles.entity.phantom.hurt": "On arka", "subtitles.entity.phantom.swoop": "On spada", "subtitles.entity.pig.ambient": "S<PERSON>ja hoi", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON> tjinu", "subtitles.entity.pig.hurt": "Sfinja arka", "subtitles.entity.pig.saddle": "Hengestisu bli na hengest", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> razanse ting", "subtitles.entity.piglin.ambient": "<PERSON><PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON> ha<PERSON>engi bosemente", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> iwai", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> bli o<PERSON><PERSON>-<PERSON><PERSON>", "subtitles.entity.piglin.death": "<PERSON><PERSON> shinu", "subtitles.entity.piglin.hurt": "Piglin arka", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> hanahengi ervilmente", "subtitles.entity.piglin.retreat": "<PERSON><PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON> jalaka", "subtitles.entity.piglin_brute.ambient": "<PERSON>zhon<PERSON>", "subtitles.entity.piglin_brute.angry": "<PERSON>zhon<PERSON> ha<PERSON>i bosemente", "subtitles.entity.piglin_brute.converted_to_zombified": "Djong<PERSON><PERSON><PERSON> bli o<PERSON>", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.piglin_brute.hurt": "Djongpiglin arka", "subtitles.entity.piglin_brute.step": "Djongpiglin jalaka", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "Poliskrigdjin glauogoe", "subtitles.entity.pillager.death": "Poliskrigdjin tjinu", "subtitles.entity.pillager.hurt": "Poliskrigdjin arka", "subtitles.entity.player.attack.crit": "Tsataindjong slag", "subtitles.entity.player.attack.knockback": "Kundargutsjo-harza", "subtitles.entity.player.attack.strong": "Djong slag", "subtitles.entity.player.attack.sweep": "Shu slag", "subtitles.entity.player.attack.weak": "Shvants slag", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Spild<PERSON> shinu", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "Spildjin arka", "subtitles.entity.player.hurt_drown": "Spildjin erglug", "subtitles.entity.player.hurt_on_fire": "Spildjin hono", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON><PERSON> ding", "subtitles.entity.player.teleport": "<PERSON><PERSON><PERSON><PERSON> pal", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON> shrei", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON> perpa", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON><PERSON><PERSON><PERSON> bli nai rufne", "subtitles.entity.puffer_fish.blow_up": "<PERSON><PERSON><PERSON><PERSON><PERSON> rupne", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>u", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON><PERSON><PERSON><PERSON>wan", "subtitles.entity.rabbit.ambient": "Kuneli pi", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> ha<PERSON>a", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.rabbit.hurt": "Kuneli arka", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> pal", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.ravager.attack": "Erviskenadjin hammas", "subtitles.entity.ravager.celebrate": "Erviskenadjin iwai", "subtitles.entity.ravager.death": "Erviskenadjin shinu", "subtitles.entity.ravager.hurt": "Erviskenadjin arka", "subtitles.entity.ravager.roar": "Erviskenadjin shrei", "subtitles.entity.ravager.step": "Erviskenadjin yalaka", "subtitles.entity.ravager.stunned": "Erperpazhin slagjametena", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON><PERSON> poki<PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON><PERSON> arka", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> ba", "subtitles.entity.sheep.death": "<PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> arka", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> kini", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> t<PERSON>u", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> au<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> strela", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker_bullet.hit": "Shulkerbam bum", "subtitles.entity.shulker_bullet.hurt": "Shulkerbam perpa", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON> tjinu", "subtitles.entity.silverfish.hurt": "Sokeribage arka", "subtitles.entity.skeleton.ambient": "Ponezhin ponekiva", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON>n bli milu<PERSON>ne", "subtitles.entity.skeleton.death": "Ponedjin tjinu", "subtitles.entity.skeleton.hurt": "Ponedjin arka", "subtitles.entity.skeleton.shoot": "Ponedjin strela", "subtitles.entity.skeleton_horse.ambient": "Ponehengest ogoe", "subtitles.entity.skeleton_horse.death": "Ponehengest tjinu", "subtitles.entity.skeleton_horse.hurt": "Ponehengest arka", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Ponehengest iskjeljetaa", "subtitles.entity.slime.attack": "Gymi slag", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.sniffer.digging": "Nioidjin gørmoj", "subtitles.entity.sniffer.digging_stop": "Nioidjin bli tatsu", "subtitles.entity.sniffer.drop_seed": "Nioidjin spaada pie", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON> nam", "subtitles.entity.sniffer.egg_crack": "Jaitso f'nioidjin uperpanen", "subtitles.entity.sniffer.egg_hatch": "Jaitso f'nioidjin uperpa", "subtitles.entity.sniffer.happy": "Nioidjin glaudai", "subtitles.entity.sniffer.hurt": "Nioidjin arka", "subtitles.entity.sniffer.idle": "Nioidjin gorlazan", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> nioi", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON> ni<PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.snow_golem.death": "<PERSON><PERSON><PERSON> t<PERSON>u", "subtitles.entity.snow_golem.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.snowball.throw": "Upasmjacj ljeta", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>u", "subtitles.entity.spider.hurt": "Kasjalka arka", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.squid.hurt": "Majaka<PERSON>jen arka", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON><PERSON><PERSON> indit shtiftpphi", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON>ne arka", "subtitles.entity.strider.death": "Jalakadjin shinu", "subtitles.entity.strider.eat": "Jalakadjin nam", "subtitles.entity.strider.happy": "Jalakadjin trr", "subtitles.entity.strider.hurt": "Jalakadjin arga", "subtitles.entity.strider.idle": "Jalakadjin pi", "subtitles.entity.strider.retreat": "Jalakadjin shkekso", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON> poki<PERSON>", "subtitles.entity.tadpole.grow_up": "<PERSON>rinos rufne", "subtitles.entity.tadpole.hurt": "Ji<PERSON>s arka", "subtitles.entity.tnt.primed": "Bamvjetka zam sss", "subtitles.entity.tropical_fish.death": "Sakana fu veltkraisdai shinu", "subtitles.entity.tropical_fish.flop": "Sakana fu veltkraisdai pokiva", "subtitles.entity.tropical_fish.hurt": "Sakana fu veltkraisdai arga", "subtitles.entity.turtle.ambient_land": "Breshka pi", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.turtle.egg_break": "Breshkajaitso perpena", "subtitles.entity.turtle.egg_crack": "Breshkaja<PERSON><PERSON>", "subtitles.entity.turtle.egg_hatch": "Breshkajaitso sintwa", "subtitles.entity.turtle.hurt": "Breshka arka", "subtitles.entity.turtle.hurt_baby": "Breshkanen arka", "subtitles.entity.turtle.lay_egg": "Bresh<PERSON> antaa jaitso", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON> hid<PERSON>a", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.swim": "Breshka ishkeljetaa", "subtitles.entity.vex.ambient": "Rovozhin rovo", "subtitles.entity.vex.charge": "R<PERSON><PERSON>n baflire", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.vex.hurt": "Rovozhin arka", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.villager.celebrate": "Statdjin ura", "subtitles.entity.villager.death": "Statdjin tjinu", "subtitles.entity.villager.hurt": "Statdjin arka", "subtitles.entity.villager.no": "Statdjin nai uslova", "subtitles.entity.villager.trade": "Statdjin hok", "subtitles.entity.villager.work_armorer": "Jerkatmahdjin ergo", "subtitles.entity.villager.work_butcher": "Shøtdjin ergo", "subtitles.entity.villager.work_cartographer": "Kartadjin ergo", "subtitles.entity.villager.work_cleric": "Kamihuomidjin ergo", "subtitles.entity.villager.work_farmer": "Gotovadjin fu pieplas ergo", "subtitles.entity.villager.work_fisherman": "Sakanadjin ergo", "subtitles.entity.villager.work_fletcher": "Streladjin ergo", "subtitles.entity.villager.work_leatherworker": "Gjukodjadjin ergo", "subtitles.entity.villager.work_librarian": "Librehuomidjin", "subtitles.entity.villager.work_mason": "Hwomimahadjin ergo", "subtitles.entity.villager.work_shepherd": "Lammasdjin ergo", "subtitles.entity.villager.work_toolsmith": "Bruktingdjin ergo", "subtitles.entity.villager.work_weaponsmith": "Kriigbruktingmahadjin ergo", "subtitles.entity.villager.yes": "Statdjin uslova", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> pohanu", "subtitles.entity.vindicator.celebrate": "Warumelonzhin ivai", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON>n shinu", "subtitles.entity.vindicator.hurt": "Warumelonzhin arka", "subtitles.entity.wandering_trader.ambient": "Rasshkèi-<PERSON><PERSON><PERSON><PERSON> hanu<PERSON>", "subtitles.entity.wandering_trader.death": "Rasshkèi-hokzhin shinu", "subtitles.entity.wandering_trader.disappeared": "Rasshkèi hokzhin khnopit", "subtitles.entity.wandering_trader.drink_milk": "Rasshkèi-hokzhin glug nju", "subtitles.entity.wandering_trader.drink_potion": "Rasshkèi-hokzhin glug taikaishke", "subtitles.entity.wandering_trader.hurt": "Rasshkèi-hokzhin arka", "subtitles.entity.wandering_trader.no": "Rasshkèi hokzhin knuslova", "subtitles.entity.wandering_trader.reappeared": "Rasshkèi hokzhin tulla", "subtitles.entity.wandering_trader.trade": "Rasshkèi hokzhin hok", "subtitles.entity.wandering_trader.yes": "Rasshkèi hokzhin uslova", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON><PERSON> bor<PERSON> bosemente", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON><PERSON> bli bose", "subtitles.entity.warden.attack_impact": "Eskuzhin slag", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.warden.dig": "Eskuzhin enterra", "subtitles.entity.warden.emerge": "Eskuzhin eksterra", "subtitles.entity.warden.heartbeat": "Ker fu eks<PERSON><PERSON> baramba", "subtitles.entity.warden.hurt": "Eskuzhin arka", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON><PERSON> hœr", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON><PERSON> hœr bosemente", "subtitles.entity.warden.nearby_close": "Eskuzhin bara", "subtitles.entity.warden.nearby_closer": "E<PERSON>uz<PERSON> baradai", "subtitles.entity.warden.nearby_closest": "Eskuzhin akote", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON><PERSON> ran<PERSON>i", "subtitles.entity.warden.sonic_boom": "Eskuzhin indit zambum", "subtitles.entity.warden.sonic_charge": "Eskuzhin pulap slag", "subtitles.entity.warden.step": "Eskuzhin jalaka", "subtitles.entity.warden.tendril_clicks": "Kerojai fu eskuzhin raskiva", "subtitles.entity.wind_charge.throw": "Luftbamba ljeta", "subtitles.entity.wind_charge.wind_burst": "Luftbamba ubam", "subtitles.entity.witch.ambient": "Bataikazhin flirenen", "subtitles.entity.witch.celebrate": "Taikadjin glau ogoe", "subtitles.entity.witch.death": "Taikadjin shinu", "subtitles.entity.witch.drink": "Taikadjin glug", "subtitles.entity.witch.hurt": "Taikadjin arka", "subtitles.entity.witch.throw": "Taikadjin strela", "subtitles.entity.wither.ambient": "Wither zam boze", "subtitles.entity.wither.death": "Wither shinu", "subtitles.entity.wither.hurt": "Wither arka", "subtitles.entity.wither.shoot": "Wither slag", "subtitles.entity.wither.spawn": "Wither sintua", "subtitles.entity.wither_skeleton.ambient": "Witherponedjin kiva", "subtitles.entity.wither_skeleton.death": "Witherponed<PERSON> shinu", "subtitles.entity.wither_skeleton.hurt": "Witherponedjin arka", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON><PERSON> s<PERSON>u", "subtitles.entity.wolf.growl": "<PERSON><PERSON> borbor", "subtitles.entity.wolf.hurt": "<PERSON><PERSON> arka", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "<PERSON><PERSON>", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> borbor", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> bøze", "subtitles.entity.zoglin.attack": "Zoglin slag", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.zoglin.hurt": "Zoglin arka", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> borbor", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> raz<PERSON>", "subtitles.entity.zombie.break_wooden_door": "Dvera perpena", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> shinu", "subtitles.entity.zombie.destroy_egg": "Breshka<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> arka", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON> antaa b<PERSON>ki", "subtitles.entity.zombie_horse.ambient": "Odaremhengest kova", "subtitles.entity.zombie_horse.death": "Odaremhengest shinu", "subtitles.entity.zombie_horse.hurt": "Odaremhengest arka", "subtitles.entity.zombie_villager.ambient": "Odarempoliszhin borbor", "subtitles.entity.zombie_villager.converted": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.cure": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.death": "Odarempolisdjin shinu", "subtitles.entity.zombie_villager.hurt": "Odarempolisdjin arka", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON> borbor", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON> borbor bosemente", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> shinu", "subtitles.entity.zombified_piglin.hurt": "Odarempiglin arka", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON> prit<PERSON>a", "subtitles.event.mob_effect.raid_omen": "Para erharza bli", "subtitles.event.mob_effect.trial_omen": "Nafaczor iskatdai joku para", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON><PERSON> ushrei", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON><PERSON><PERSON> trakena", "subtitles.item.armor.equip_chain": "Jerkatuleshku razkiva", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ding", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON><PERSON> shuu~", "subtitles.item.armor.equip_gold": "Eshkuklea na gelt kiva", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON> be<PERSON> kiva", "subtitles.item.armor.equip_leather": "Gjukodja fuwa<PERSON>wa", "subtitles.item.armor.equip_netherite": "Netherit-eshkuklea zam", "subtitles.item.armor.equip_turtle": "Breshkakømuske donk", "subtitles.item.armor.equip_wolf": "Eshkuklea fu lupo an<PERSON>rkena", "subtitles.item.armor.unequip_wolf": "Esh<PERSON>klea fu lupo karajena", "subtitles.item.axe.scrape": "j<PERSON>u z<PERSON>", "subtitles.item.axe.strip": "Keshitegodja fu baumtel", "subtitles.item.axe.wax_off": "<PERSON><PERSON> dresha", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON> zan", "subtitles.item.book.page_turn": "Lehti kiva", "subtitles.item.book.put": "<PERSON><PERSON> bøm", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "Sikat", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON>", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON> si<PERSON> is<PERSON>an", "subtitles.item.brush.brushing.sand": "Sikat san", "subtitles.item.brush.brushing.sand.complete": "<PERSON>wari sikat san", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON> bli ohare", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON> bli pulap", "subtitles.item.bucket.fill_axolotl": "Atheks<PERSON><PERSON> sa<PERSON>na", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> festena", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON> ghena", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.insert": "<PERSON><PERSON> nasiena", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.item.bundle.remove_one": "<PERSON><PERSON>", "subtitles.item.chorus_fruit.teleport": "Spildjin visalatapal", "subtitles.item.crop.plant": "Rupneting nasiena", "subtitles.item.crossbow.charge": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>", "subtitles.item.crossbow.hit": "Strela slag", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.dye.use": "<PERSON><PERSON>", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON><PERSON> shu", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON> mit jerkat kapk<PERSON>p", "subtitles.item.glow_ink_sac.use": "<PERSON><PERSON><PERSON> sht<PERSON><PERSON><PERSON> kaban gushena", "subtitles.item.goat_horn.play": "Œchke-nor spela", "subtitles.item.hoe.till": "Piegørmoi bideshkoi", "subtitles.item.honey_bottle.drink": "Glug mjalt", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON> d<PERSON>a", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaban gushena", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "<PERSON><PERSON><PERSON><PERSON> tø<PERSON>", "subtitles.item.mace.smash_air": "Bulava uslag", "subtitles.item.mace.smash_ground": "<PERSON><PERSON>va baslag", "subtitles.item.nether_wart.plant": "<PERSON><PERSON> nasiena", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON>", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Tsamb<PERSON>z<PERSON> kliqna", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Kømuske poeshku", "subtitles.item.shovel.flatten": "Qoilara poløze", "subtitles.item.spyglass.stop_using": "Prapasetinn anobit", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.totem.use": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "subtitles.item.trident.hit": "Sardeshkadai pwangjena", "subtitles.item.trident.hit_ground": "Sardeshkadai razkiva", "subtitles.item.trident.return": "Sardeshkadai suruk", "subtitles.item.trident.riptide": "<PERSON>rdes<PERSON><PERSON><PERSON> shu", "subtitles.item.trident.throw": "Sardeshka<PERSON> ding", "subtitles.item.trident.thunder": "Sardeshkadai zezan", "subtitles.item.wolf_armor.break": "Eshkuklea fu lupo perpa", "subtitles.item.wolf_armor.crack": "Esh<PERSON>klea fu lupo cherena", "subtitles.item.wolf_armor.damage": "Eshkuklea fu lupo arkaena", "subtitles.item.wolf_armor.repair": "Eshkuklea fu lupo reformaena", "subtitles.particle.soul_escape": "<PERSON><PERSON> sh<PERSON>o", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON>", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON><PERSON> brukena", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON> brukena", "subtitles.weather.rain": "P<PERSON><PERSON>", "symlink_warning.message": "Lesa velt na kaban mit symlink tabun kn<PERSON><PERSON>, li na<PERSON><PERSON> tsatain ka suruena. Bite na %s made per lera plus.", "symlink_warning.message.pack": "<PERSON>a implakaban mit symlink tabun kn<PERSON><PERSON>, li na<PERSON><PERSON> tsatain ka suruena. Bite na %s made per lera plus.", "symlink_warning.message.world": "Lesa velt na kaban mit symlink tabun kn<PERSON><PERSON>, li na<PERSON><PERSON> tsatain ka suruena. Bite na %s made per lera plus.", "symlink_warning.more_info": "Plus tsuite", "symlink_warning.title": "Veltkaban bruk symlink jo", "symlink_warning.title.pack": "<PERSON><PERSON><PERSON>n har symlink jo", "symlink_warning.title.world": "Veltkaban bruk symlink jo", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON> and<PERSON>", "team.collision.pushOwnTeam": "G<PERSON>jo klani fu sebja", "team.notFound": "<PERSON><PERSON><PERSON> '%s'", "team.visibility.always": "<PERSON><PERSON>", "team.visibility.hideForOtherTeams": "<PERSON><PERSON><PERSON><PERSON> per andra k<PERSON>ani", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON><PERSON> per klaani na sebja", "team.visibility.never": "<PERSON><PERSON><PERSON>", "telemetry.event.advancement_made.description": "Fshtozma oba slucharjet para jingleo, afto mahplussimpel ke Vi deki plusbra parjadøze virta fu spela.", "telemetry.event.advancement_made.title": "<PERSON><PERSON> jena", "telemetry.event.game_load_times.description": "Afto implapik apu ke Vi uleradeki hur <PERSON><PERSON><PERSON>ze hadji-bystrazma/djongzma, farzaja tid brukena na tonti leo fu spelhadji.", "telemetry.event.game_load_times.title": "Lesatid fu spel", "telemetry.event.optional": "%s (Trengnai)", "telemetry.event.optional.disabled": "%s (Trengnai) - Iskatnai", "telemetry.event.performance_metrics.description": "Fshtona fu hel bystrazma fu Minecraft apu vi per maha spil plus bra per mange fal fu kompju au dbaima-poldai. Fallasku ti brukena per apu vi na se au bidzhau bystrazma fu neofal fu Minecraft.", "telemetry.event.performance_metrics.title": "Djongzma-impla", "telemetry.event.required": "%s (Treng)", "telemetry.event.world_load_times.description": "<PERSON> <PERSON>, g<PERSON>na shiru katai tid spelabma bruk per shkinne velt, au kawarizma na afto bi tid. <PERSON><PERSON><PERSON><PERSON>, koske antaena neo dekina, os Vi ergo na stordai kawari na netopa fu spelabma, treng anse ka rjetzma jam na veltlesatid.", "telemetry.event.world_load_times.title": "Tiidatai na lesana fu velta", "telemetry.event.world_loaded.description": "<PERSON><PERSON><PERSON> <PERSON><PERSON> spel<PERSON><PERSON> (tato spel<PERSON>l, li mod-ena, auen versho) ainlat ke Vi deki shuchu kawari na tuo tel fu spel ke letstevikti na speldjin.\n\"Velt-lesajena\" ufnepik tsunaga \"Velt-kntragena\" ufnepik per lera ka mange tid brukena per spel.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_unloaded.description": "Afto implapik tsunaga mit \"<PERSON><PERSON><PERSON>-les<PERSON>\" implapik per lera tidatai fu speltid.\n<PERSON><PERSON><PERSON> (na sho au tidleo) farzaena koske veltspeltid owari (shkekso na glavna-ekran, shkekso server kara).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON> (tidleo)", "telemetry.property.advancement_id.title": "Lasku fu jingleo", "telemetry.property.client_id.title": "Tsunagadjin ID", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON><PERSON> husk<PERSON> (kB)", "telemetry.property.event_timestamp_utc.title": "Sluchatoki (UTC)", "telemetry.property.frame_rate_samples.title": "Bystratai fu ekranvala (ENS)", "telemetry.property.game_mode.title": "Speltropos", "telemetry.property.game_version.title": "Fallasku fu spil", "telemetry.property.launcher_name.title": "<PERSON><PERSON> fu had<PERSON><PERSON>ma", "telemetry.property.load_time_bootstrap_ms.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (tuhattelsho)", "telemetry.property.load_time_loading_overlay_ms.title": "Tid na glavna-ekran (tts)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON><PERSON> de ekran auki (tts)", "telemetry.property.load_time_total_time_ms.title": "Tid napakus per hadji spel (tts)", "telemetry.property.minecraft_session_id.title": "Speltid ID", "telemetry.property.new_world.title": "Neo velt", "telemetry.property.number_of_samples.title": "Implapikatai", "telemetry.property.operating_system.title": "Dwaibma-poldai", "telemetry.property.opt_in.title": "Na Vil", "telemetry.property.platform.title": "Kompjupolfal", "telemetry.property.realms_map_content.title": "Veltimpla na Realms (Namae fu matrjoshka)", "telemetry.property.render_distance.title": "Sepitkatai", "telemetry.property.render_time_samples.title": "Ekrankaku-tidpikatai", "telemetry.property.seconds_since_load.title": "<PERSON>a balesena kara tidatai (Sho)", "telemetry.property.server_modded.title": "<PERSON> ka<PERSON><PERSON>", "telemetry.property.server_type.title": "Serverfal", "telemetry.property.ticks_since_load.title": "Tid za rmlesa (tidleo)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON><PERSON> (huskebma)", "telemetry.property.user_id.title": "Brukdjin ID", "telemetry.property.world_load_time_ms.title": "<PERSON><PERSON>t balesa tidatai (tuhattel-sho)", "telemetry.property.world_session_id.title": "Tsatainlasku fu velt-speltid", "telemetry_info.button.give_feedback": "<PERSON><PERSON><PERSON> iken", "telemetry_info.button.privacy_statement": "Tsuite implazi<PERSON><PERSON>ma", "telemetry_info.button.show_data": "<PERSON>ki impla fuun", "telemetry_info.opt_in.description": "Un anuslova zeanta bruk<PERSON>-ufneimpla (nai baiena)", "telemetry_info.property_title": "<PERSON><PERSON> implara", "telemetry_info.screen.description": "Razbidra fu afto implara apu plusbrøze Minecraft grun guthjo vi na strela made ke na spildjin bra fanshuka.\nDekiti auen antaa poplus svar per apu plusbrøzazma fu Minecraft.", "telemetry_info.screen.title": "<PERSON>onhom imp<PERSON>", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Obamange %s dado", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "<PERSON><PERSON>", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Humbadan: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "Jamnai afto iskat", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Fal: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[uwaki]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Hadji iskat %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit kompju fynnajena: afto tabun bai du mir nai deki spil grun 64-bit kompju mir trengena!", "title.32bit.deprecation.realms": "Mirainen Minecraft treng mirai 64-bit kom<PERSON>j<PERSON>l, dakara dekinai ti Realms bruk os spel na afto kompju. Miraiti treng hantolj anjame mi-Realms-tidkaupa-va ke na du jam.", "title.32bit.deprecation.realms.check": "Nilraz plus se afto lehti", "title.32bit.deprecation.realms.header": "32-bit kompju fynnajena", "title.credits": "Copyright Mojang AB. Danai razantaa!", "title.multiplayer.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> ima kinjena. Bitte se kawarjena fu Microsoft-zepaperi.", "title.multiplayer.disabled.banned.name": "<PERSON><PERSON> mus kawariena de du speldeki narjet", "title.multiplayer.disabled.banned.permanent": "Zefuga eryam<PERSON>na de nilmirai dekiti spil narjet", "title.multiplayer.disabled.banned.temporary": "Zefuga apartid yametena de de<PERSON>ai spil narjet", "title.multiplayer.lan": "<PERSON><PERSON> (Huomirjet)", "title.multiplayer.other": "<PERSON><PERSON> (Prapa server)", "title.multiplayer.realms": "<PERSON><PERSON> (Realms)", "title.singleplayer": "Tolkaspel", "translation.test.args": "%s %s", "translation.test.complex": "Hadjifesta, %s%2$s gyen %s au %1$s naowari %s awen %1$s gyen!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "jaa %", "translation.test.invalid2": "jaa %s", "translation.test.none": "<PERSON><PERSON><PERSON><PERSON> made!", "translation.test.world": "velt", "trim_material.minecraft.amethyst": "<PERSON><PERSON><PERSON><PERSON> shtof", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON> shtof", "trim_material.minecraft.diamond": "<PERSON><PERSON> shtof", "trim_material.minecraft.emerald": "Zomorroz shtof", "trim_material.minecraft.gold": "<PERSON>lt shtof", "trim_material.minecraft.iron": "Jerkatshtof", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON>her<PERSON> shtof", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstone shtof", "trim_material.minecraft.resin": "<PERSON><PERSON><PERSON><PERSON><PERSON> stof", "trim_pattern.minecraft.bolt": "<PERSON><PERSON><PERSON> kom<PERSON>efal", "trim_pattern.minecraft.coast": "Mare komuskefal", "trim_pattern.minecraft.dune": "Sanberk komuskefal", "trim_pattern.minecraft.eye": "Me komuskefal", "trim_pattern.minecraft.flow": "Virta komuskefal", "trim_pattern.minecraft.host": "Vomi komuskefal", "trim_pattern.minecraft.raiser": "Avara komuskefal", "trim_pattern.minecraft.rib": "Baksupone komuskefal", "trim_pattern.minecraft.sentry": "Sedjin komuskefal", "trim_pattern.minecraft.shaper": "Mahdjin komuskefal", "trim_pattern.minecraft.silence": "Pinuno komuskefal", "trim_pattern.minecraft.snout": "<PERSON><PERSON> kom<PERSON>", "trim_pattern.minecraft.spire": "Berkpik komuskefal", "trim_pattern.minecraft.tide": "Hav komuskefal", "trim_pattern.minecraft.vex": "Rovo komuskefal", "trim_pattern.minecraft.ward": "Eshku komuskefal", "trim_pattern.minecraft.wayfinder": "Strelating komuskefal", "trim_pattern.minecraft.wild": "Dasos komuskefal", "tutorial.bundleInsert.description": "Migimys per nasi tinn", "tutorial.bundleInsert.title": "Bruk festakaban", "tutorial.craft_planks.description": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>ti apu", "tutorial.craft_planks.title": "<PERSON><PERSON> b<PERSON>", "tutorial.find_tree.description": "Slag per saada baumshtof", "tutorial.find_tree.title": "<PERSON><PERSON><PERSON> baum", "tutorial.look.description": "Bruk zemysh per krungut", "tutorial.look.title": "Se nakrais", "tutorial.move.description": "Pal mit %s", "tutorial.move.title": "Ugoki mitt %s, %s, %s, %s", "tutorial.open_inventory.description": "Da tasta %s", "tutorial.open_inventory.title": "<PERSON><PERSON> ha<PERSON>", "tutorial.punch_tree.description": "Pitkatasta %s", "tutorial.punch_tree.title": "<PERSON><PERSON> baum", "tutorial.socialInteractions.description": "%s tasta per auki", "tutorial.socialInteractions.title": "Pa<PERSON><PERSON>jet", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON><PERSON>"}