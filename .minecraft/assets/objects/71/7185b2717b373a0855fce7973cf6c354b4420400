{"accessibility.onboarding.accessibility.button": "ante pi ken ale...", "accessibility.onboarding.screen.narrator": "o pilin e nena pi linja sin (enter) tawa ilo toki", "accessibility.onboarding.screen.title": "o kama pona tawa musi Manka!\n\nsina wile ala wile ken e ilo toki? sina wile ala wile tawa lipu 'ante pi ken ale'?", "addServer.add": "pini", "addServer.enterIp": "nimi IP pi ma kulupu", "addServer.enterName": "nimi pi ma kulupu", "addServer.resourcePack": "ante pi lukin ma", "addServer.resourcePack.disabled": "ken ala", "addServer.resourcePack.enabled": "ken", "addServer.resourcePack.prompt": "mi o alasa e wile sina", "addServer.title": "o ante", "advMode.command": "toki wawa", "advMode.mode": "nasin", "advMode.mode.auto": "sama sin", "advMode.mode.autoexec.bat": "wile ala pi ko wawa", "advMode.mode.conditional": "pali lon pali monsi", "advMode.mode.redstone": "pali wan", "advMode.mode.redstoneTriggered": "wile pi ko Redstone", "advMode.mode.sequence": "linja kiwen", "advMode.mode.unconditional": "pali lon tenpo ale", "advMode.notAllowed": "sina wile e ni la, o kama jan sewi", "advMode.notEnabled": "ma kulupu ni li kepeken ala ilo pi toki wawa", "advMode.previousOutput": "toki pi tenpo pini poka", "advMode.setCommand": "o pali e toki wawa", "advMode.setCommand.success": "toki wawa: %s", "advMode.trackOutput": "o alasa e ijo tan", "advMode.triggering": "open", "advMode.type": "ante", "advancement.advancementNotFound": "mi sona ala e pali pona ni: %s", "advancements.adventure.adventuring_time.description": "o noka lon ma ante ali", "advancements.adventure.adventuring_time.title": "tenpo alasa", "advancements.adventure.arbalistic.description": "o moli e ijo ante luka kepeken palisa wan tan ilo alasa supa", "advancements.adventure.arbalistic.title": "moli mute a!", "advancements.adventure.avoid_vibration.description": "o tawa anpa lon poka pi soko Sculk anu monsuta kute tawa ni: ona li kute ala e sina", "advancements.adventure.avoid_vibration.title": "o kalama ala!", "advancements.adventure.blowback.description": "o moli e monsuta kon kepeken sike kon ona", "advancements.adventure.blowback.title": "esun kon", "advancements.adventure.brush_armadillo.description": "o kama jo e kiwen selo tan soweli pi kiwen selo kepeken ilo pi weka ko", "advancements.adventure.brush_armadillo.title": "selo sin pi soweli pona!", "advancements.adventure.bullseye.description": "o pana e palisa alasa lon sike insa pi leko ni: sike pi alasa musi", "advancements.adventure.bullseye.title": "musi pona!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "o pali e poki kiwen sitelen kepeken kipisi tu tu", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "kama sin pona", "advancements.adventure.crafters_crafting_crafters.description": "ilo pali li pali e ilo pali la o lon poka ona", "advancements.adventure.crafters_crafting_crafters.title": "ilo pali li pali ilo e ilo pali", "advancements.adventure.fall_from_world_height.description": "o tawa tan sewi pi ma ale, tawa kiwen pi pakala ala, o moli ala", "advancements.adventure.fall_from_world_height.title": "lupa en nena", "advancements.adventure.heart_transplanter.description": "o pana e lawa pi monsuta kasi lon insa pi leko ni tu: sijelo pi kasi walo.", "advancements.adventure.heart_transplanter.title": "ante lawa", "advancements.adventure.hero_of_the_village.description": "o anpa e utala lon ma tomo", "advancements.adventure.hero_of_the_village.title": "utala musi", "advancements.adventure.honey_block_slide.description": " leko pi suwi pipi la o tawa anpa lon poka", "advancements.adventure.honey_block_slide.title": "awen lon suwi", "advancements.adventure.kill_a_mob.description": "o moli e monsuta", "advancements.adventure.kill_a_mob.title": "open pi alasa monsuta", "advancements.adventure.kill_all_mobs.description": "o moli e wan pi monsuta ali", "advancements.adventure.kill_all_mobs.title": "pini pi alasa monsuta", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "o moli e ijo lon poka pi soko Sculk mama", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "ona li kama suli", "advancements.adventure.lighten_up.description": "o weka e laso tan suno ante kepeken ilo kipisi. suno ante li kama suno mute", "advancements.adventure.lighten_up.title": "ale li jelo", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "o awen e jan tomo tan linja suno. o kama kin e ni: seli li kama ala", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "jan awen sewi", "advancements.adventure.minecraft_trials_edition.description": "o lon tomo alasa", "advancements.adventure.minecraft_trials_edition.title": "musi Manka: tomo utala", "advancements.adventure.ol_betsy.description": "o kepeken ilo alasa supa", "advancements.adventure.ol_betsy.title": "mani sina anu moli sina!", "advancements.adventure.overoverkill.description": "utala wan la o pakala pi mute 50 kepeken palisa wawa", "advancements.adventure.overoverkill.title": "utala wawa a!", "advancements.adventure.play_jukebox_in_meadows.description": "o pana e sike kalama lon ilo pi kalama musi, lon ma laso sewi", "advancements.adventure.play_jukebox_in_meadows.title": "kalama sin (lon ma kasi)", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "o lukin e nanpa wawa pi poki lipu kepeken ilo pi ante wawa", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "wawa lipu", "advancements.adventure.revaulting.description": "o open e poki mani ike kepeken ilo open ike pi tomo alasa", "advancements.adventure.revaulting.title": "poki ike", "advancements.adventure.root.description": "alasa en tawa en utala", "advancements.adventure.root.title": "alasa", "advancements.adventure.salvage_sherd.description": "o kama jo e kipisi poki tan leko nasa kepeken ilo pi weka ko", "advancements.adventure.salvage_sherd.title": "alasa awen", "advancements.adventure.shoot_arrow.description": "o alasa kepeken ilo alasa", "advancements.adventure.shoot_arrow.title": "alasa sina nanpa wan!", "advancements.adventure.sleep_in_bed.description": "o kepeken supa lape o ante e ma pi lon sin sina", "advancements.adventure.sleep_in_bed.title": "o lape pona", "advancements.adventure.sniper_duel.description": "o moli e jan palisa tan leko 50 lon weka", "advancements.adventure.sniper_duel.title": "utala pi ilo alasa", "advancements.adventure.spyglass_at_dragon.description": "o lukin e akesi pi ma End kepeken palisa lukin", "advancements.adventure.spyglass_at_dragon.title": "ni li tomo tawa sewi anu seme?", "advancements.adventure.spyglass_at_ghast.description": "o lukin e monsuta Ghast kepeken palisa lukin", "advancements.adventure.spyglass_at_ghast.title": "ni li sike sewi anu seme?", "advancements.adventure.spyglass_at_parrot.description": "o lukin e waso toki kepeken palisa lukin", "advancements.adventure.spyglass_at_parrot.title": "ni li waso anu seme?", "advancements.adventure.summon_iron_golem.description": "o pali e jan ilo pi kiwen walo. ona o awen e ma tomo.", "advancements.adventure.summon_iron_golem.title": "jan pona a!", "advancements.adventure.throw_trident.description": "o pana e ilo alasa kala tawa ijo. \ntaso o sona e ni: sina jo e ona wan taso la sina wile ala pana!", "advancements.adventure.throw_trident.title": "ni li kala ala!", "advancements.adventure.totem_of_undying.description": "o kepeken ilo pi moli ala", "advancements.adventure.totem_of_undying.title": "o anpa e moli", "advancements.adventure.trade.description": "o esun e ijo tan jan tomo", "advancements.adventure.trade.title": "esun pona a!", "advancements.adventure.trade_at_world_height.description": "o esun e ijo lon sewi pi ma ale", "advancements.adventure.trade_at_world_height.title": "esun sewi", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "o kepeken namako len ni ale: nena, soweli, palisa, awen, kalama ala, kon, kala, nasin", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "jan pi sona sitelen", "advancements.adventure.trim_with_any_armor_pattern.description": "o pali e len namako kepeken supa ilo", "advancements.adventure.trim_with_any_armor_pattern.title": "len namako a!", "advancements.adventure.two_birds_one_arrow.description": "o moli e waso mun tu kepeken palisa alasa wan", "advancements.adventure.two_birds_one_arrow.title": "waso tu en palisa alasa wan", "advancements.adventure.under_lock_and_key.description": "o kepeken ilo open pi tomo alasa lon poki mani", "advancements.adventure.under_lock_and_key.title": "poki li awen li pini", "advancements.adventure.use_lodestone.description": "o kepeken ilo nasin lon kiwen nasin", "advancements.adventure.use_lodestone.title": "ma mama", "advancements.adventure.very_very_frightening.description": "o utala e jan tomo kepeken linja suno", "advancements.adventure.very_very_frightening.title": "len sin nasa", "advancements.adventure.voluntary_exile.description": "o moli e jan utala lawa.\nken la sina wile awen lon weka ma tomo...", "advancements.adventure.voluntary_exile.title": "jan weka", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "o tawa lon ko lete kon... o anpa ala lon ona", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "noka suwi", "advancements.adventure.who_needs_rockets.description": "kepeken sike kon la, o sewi pi leko 8 e sina", "advancements.adventure.who_needs_rockets.title": "sama waso la sina kepeken ala ilo", "advancements.adventure.whos_the_pillager_now.description": "o kepeken ilo utala pi jan ike alasa tawa moli pi ona sama", "advancements.adventure.whos_the_pillager_now.title": "tenpo ni la jan alasa li jan seme?", "advancements.empty": "ala li lon...", "advancements.end.dragon_breath.description": "o awen e kon pi akesi suli lon poki lili", "advancements.end.dragon_breath.title": "kon jaki tan uta", "advancements.end.dragon_egg.description": "o jo e sike mama akesi pi ma End", "advancements.end.dragon_egg.title": "kulupu akesi tenpo", "advancements.end.elytra.description": "o kama lukin e len waso", "advancements.end.elytra.title": "ni li waso!", "advancements.end.enter_end_gateway.description": "o tawa lon weka pi ma lawa", "advancements.end.enter_end_gateway.title": "jan tawa lon sewi", "advancements.end.find_end_city.description": "o tawa insa! seme li ken kama?", "advancements.end.find_end_city.title": "tomo lon pini musi", "advancements.end.kill_dragon.description": "tawa pona!", "advancements.end.kill_dragon.title": "o anpa e sewi", "advancements.end.levitate.description": "o tawa sewi lon leko 50 tan utala pi monsu<PERSON>", "advancements.end.levitate.title": "lukin li pona tan ni", "advancements.end.respawn_dragon.description": "o lon sin e akesi pi ma End", "advancements.end.respawn_dragon.title": "ma pini... sin...", "advancements.end.root.description": "anu ma open?", "advancements.end.root.title": "ma pini", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "o kama e ni: kon pona li pana e pan suwi lon poka pi leko kalama", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "o kalama musi!", "advancements.husbandry.allay_deliver_item_to_player.description": "o kama e ni: kon pona li pana e ijo tawa sina", "advancements.husbandry.allay_deliver_item_to_player.title": "sina jan pi kon pona", "advancements.husbandry.axolotl_in_a_bucket.description": "o poki e akesi suwi", "advancements.husbandry.axolotl_in_a_bucket.title": "soweli alasa suwi a!", "advancements.husbandry.balanced_diet.description": "ona li pona anu ike tawa sina la o moku e moku ali", "advancements.husbandry.balanced_diet.title": "ken moku la...", "advancements.husbandry.breed_all_animals.description": "o kama e ni: soweli ante ali li unpa!", "advancements.husbandry.breed_all_animals.title": "tenpo wan la tu", "advancements.husbandry.breed_an_animal.description": "o kama e ni: soweli tu li unpa", "advancements.husbandry.breed_an_animal.title": "sona unpa", "advancements.husbandry.complete_catalogue.description": "o pana e moku tawa soweli suwi ante ale!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "o pana e moku tawa soweli lili alasa", "advancements.husbandry.feed_snifflet.title": "nena lili", "advancements.husbandry.fishy_business.description": "o kama jo e kala", "advancements.husbandry.fishy_business.title": "selo jaki", "advancements.husbandry.froglights.description": "o jo e suno akesi ale", "advancements.husbandry.froglights.title": "wawa pi mi mute li wan!", "advancements.husbandry.kill_axolotl_target.description": "o utala kepeken akesi suwi o moli e monsuta", "advancements.husbandry.kill_axolotl_target.title": "wawa pona pi pona kulupu!", "advancements.husbandry.leash_all_frog_variants.description": "o kama e ni: akesi sewi pi kule ale li lon linja", "advancements.husbandry.leash_all_frog_variants.title": "kulupu pona a", "advancements.husbandry.make_a_sign_glow.description": "o suno e lipu sitelen kepeken telo suno", "advancements.husbandry.make_a_sign_glow.title": "sitelen pona", "advancements.husbandry.netherite_hoe.description": "sina pona e ilo kasi kepeken mani Netherite... tan seme a!?", "advancements.husbandry.netherite_hoe.title": "pali wawa a!", "advancements.husbandry.obtain_sniffer_egg.description": "o kama jo e kiwen mama pi soweli suli alasa", "advancements.husbandry.obtain_sniffer_egg.title": "akesi anu soweli anu waso?", "advancements.husbandry.place_dried_ghast_in_water.description": "o pana e kiwen Ghast pi telo ala lon telo", "advancements.husbandry.place_dried_ghast_in_water.title": "kiwen, telo, kon!", "advancements.husbandry.plant_any_sniffer_seed.description": "o ma e kasi pi soweli suli alasa", "advancements.husbandry.plant_any_sniffer_seed.title": "kasi pi tenpo pini", "advancements.husbandry.plant_seed.description": "o pana e mama kasi lon ma", "advancements.husbandry.plant_seed.title": "jan kasi", "advancements.husbandry.remove_wolf_armor.description": "o weka e len pi soweli pona tan soweli pona kepeken ilo kipisi lili", "advancements.husbandry.remove_wolf_armor.title": "sona kipisi", "advancements.husbandry.repair_wolf_armor.description": "o pona ale e len pakala pi soweli pona kepeken kiwen selo", "advancements.husbandry.repair_wolf_armor.title": "o kama pona!", "advancements.husbandry.ride_a_boat_with_a_goat.description": "o lon poka soweli nena lon ilo tawa telo", "advancements.husbandry.ride_a_boat_with_a_goat.title": "ilo tawa utala!", "advancements.husbandry.root.description": "ma ali la jan pona mute en moku mute li lon", "advancements.husbandry.root.title": "nasin pona", "advancements.husbandry.safely_harvest_honey.description": "o kama jo e suwi pipi kepeken seli palisa", "advancements.husbandry.safely_harvest_honey.title": "jan pipi", "advancements.husbandry.silk_touch_nest.description": "o tawa e tomo pi pipi suwi 3 kepeken wawa pi luka suwi", "advancements.husbandry.silk_touch_nest.title": "tawa tomo!", "advancements.husbandry.tactical_fishing.description": "o kama jo e kala... kepeken ala ilo kala!", "advancements.husbandry.tactical_fishing.title": "tomo kala", "advancements.husbandry.tadpole_in_a_bucket.description": "o poki e akesi jaki lili", "advancements.husbandry.tadpole_in_a_bucket.title": "tomo tawa mi li jo e kala linja mute", "advancements.husbandry.tame_an_animal.description": "o kama olin tawa soweli", "advancements.husbandry.tame_an_animal.title": "olin pi tenpo ale", "advancements.husbandry.wax_off.description": "o weka e selo awen tan leko pi kiwen ante", "advancements.husbandry.wax_off.title": "weka selo", "advancements.husbandry.wax_on.description": "o pana e lipu suwi tawa leko pi kiwen ante", "advancements.husbandry.wax_on.title": "pana selo", "advancements.husbandry.whole_pack.description": "o pana e moku tawa soweli pona ante ali!", "advancements.husbandry.whole_pack.title": "kulupu ale pi kule pona ale", "advancements.nether.all_effects.description": "tenpo sama la o pilin e wawa ante ali", "advancements.nether.all_effects.title": "seme a!?", "advancements.nether.all_potions.description": "tenpo sama la o pilin e wawa ante ali pi telo wawa", "advancements.nether.all_potions.title": "pilin nasa a!", "advancements.nether.brew_potion.description": "o pali e telo wawa", "advancements.nether.brew_potion.title": "jan pi telo wawa", "advancements.nether.charge_respawn_anchor.description": "o pana e kiwen suno 4 tawa ilo pi kama sin", "advancements.nether.charge_respawn_anchor.title": "sin mute a", "advancements.nether.create_beacon.description": "o pali e ilo wawa o pana e ona lon anpa", "advancements.nether.create_beacon.title": "wawa tan sewi", "advancements.nether.create_full_beacon.description": "o wawa ale e ilo wawa", "advancements.nether.create_full_beacon.title": "wawa mute a!", "advancements.nether.distract_piglin.description": "o pana e mani jelo tawa jan <PERSON>", "advancements.nether.distract_piglin.title": "olin pi jan anpa", "advancements.nether.explore_nether.description": "o lukin e ma <PERSON>her ali", "advancements.nether.explore_nether.title": "jan tawa pi anpa ma", "advancements.nether.fast_travel.description": "o kepeken ma seli tawa ni: sina tawa lon leko 7000 lon ma pona", "advancements.nether.fast_travel.title": "nasin anpa", "advancements.nether.find_bastion.description": "o tawa insa tomo pi jan soweli", "advancements.nether.find_bastion.title": "tomo pi tenpo moli", "advancements.nether.find_fortress.description": "o tawa lon insa pi tomo utala", "advancements.nether.find_fortress.title": "tomo utala", "advancements.nether.get_wither_skull.description": "o kama jo e lawa pi jan moli <PERSON>", "advancements.nether.get_wither_skull.title": "monsuta ike a!", "advancements.nether.loot_bastion.description": "o open e poki lon tomo pi jan soweli", "advancements.nether.loot_bastion.title": "soweli utala", "advancements.nether.netherite_armor.description": "o jo e len ali pi mani Netherite", "advancements.nether.netherite_armor.title": "jan lawa pi ma anpa", "advancements.nether.obtain_ancient_debris.description": "o jo e pakala mani", "advancements.nether.obtain_ancient_debris.title": "lon pilin ma", "advancements.nether.obtain_blaze_rod.description": "o kama jo e palisa jelo", "advancements.nether.obtain_blaze_rod.title": "seli mute a!", "advancements.nether.obtain_crying_obsidian.description": "o kama jo e kiwen wawa pakala", "advancements.nether.obtain_crying_obsidian.title": "jan seme li pakala e pilin kiwen...", "advancements.nether.return_to_sender.description": "o moli e monsuta Ghas<PERSON> kepeken sike seli", "advancements.nether.return_to_sender.title": "ni li sike seli sina anu seme?", "advancements.nether.ride_strider.description": "o tawa lon akesi seli kepeken soko lon palisa", "advancements.nether.ride_strider.title": "noka seli", "advancements.nether.ride_strider_in_overworld_lava.description": "o tawa pi tenpo muuuute lon akesi seli lon telo seli lon ma pona", "advancements.nether.ride_strider_in_overworld_lava.title": "ma mama weka", "advancements.nether.root.description": "sina wile e len lete", "advancements.nether.root.title": "ma <PERSON><PERSON>", "advancements.nether.summon_wither.description": "o kama e monsuta <PERSON>er", "advancements.nether.summon_wither.title": "moli pimeja", "advancements.nether.uneasy_alliance.description": "o tawa e monsuta <PERSON> tan ma Nether tawa ma pona... o moli e ona", "advancements.nether.uneasy_alliance.title": "kulupu nasa", "advancements.nether.use_lodestone.description": "o kepeken ilo nasin lon kiwen nasin", "advancements.nether.use_lodestone.title": "ma mama", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "o kepeken e telo pi utala ike e kili jelo tawa ni: jan tomo li kama moli ala", "advancements.story.cure_zombie_villager.title": "jan pi pona sijelo", "advancements.story.deflect_arrow.description": "o pini e palisa alasa kepeken sinpin sijelo", "advancements.story.deflect_arrow.title": "tenpo ni ala a!", "advancements.story.enchant_item.description": "o pana e wawa lon ijo", "advancements.story.enchant_item.title": "jan wawa", "advancements.story.enter_the_end.description": "o tawa lon lupa pi ma End", "advancements.story.enter_the_end.title": "ni li pini anu seme?", "advancements.story.enter_the_nether.description": "o pali e lupa pi ma <PERSON>her, o open e ona, o tawa insa", "advancements.story.enter_the_nether.title": "seli anpa", "advancements.story.follow_ender_eye.description": "o awen lon nasin pi oko pi ma End", "advancements.story.follow_ender_eye.title": "musi oko", "advancements.story.form_obsidian.description": "o jo e kiwen wawa pimeja", "advancements.story.form_obsidian.title": "seli lete", "advancements.story.iron_tools.description": "o pona e ilo pakala sina", "advancements.story.iron_tools.title": "mun li lon palisa", "advancements.story.lava_bucket.description": "o poki e telo seli", "advancements.story.lava_bucket.title": "seli mute a!", "advancements.story.mine_diamond.description": "o kama jo e kiwen laso", "advancements.story.mine_diamond.title": "kiwen laso a!", "advancements.story.mine_stone.description": "o pakala e kiwen kepeken ilo sin sina", "advancements.story.mine_stone.title": "tenpo kiwen", "advancements.story.obtain_armor.description": "o awen e sina kepeken len walo", "advancements.story.obtain_armor.title": "len wawa", "advancements.story.root.description": "leko lon pilin musi", "advancements.story.root.title": "musi <PERSON>", "advancements.story.shiny_gear.description": "len laso li awen tan moli", "advancements.story.shiny_gear.title": "mani mute", "advancements.story.smelt_iron.description": "o pali e kiwen walo", "advancements.story.smelt_iron.title": "ken pali li suli!", "advancements.story.upgrade_tools.description": "o pali e ilo pakala sin pona", "advancements.story.upgrade_tools.title": "ilo sin a!", "advancements.toast.challenge": "sina sewi!", "advancements.toast.goal": "sina pini pona!", "advancements.toast.task": "pali li pini!", "argument.anchor.invalid": "ni li noka ala li oko ala: %s", "argument.angle.incomplete": "pini ala tan ni: nena wan li lon ala li wile", "argument.angle.invalid": "nena li ike", "argument.block.id.invalid": "nasin \"%s\" li ike", "argument.block.property.duplicate": "sina ken ala pana e ijo \"%s\" tu tawa %s", "argument.block.property.invalid": "%s li wile ala e ijo \"%s\" tawa nasin %s", "argument.block.property.novalue": "mi wile e nanpa lon nasin \"%s\" pi ijo %s", "argument.block.property.unclosed": "sina pana ala e \" ] \"", "argument.block.property.unknown": "%s li jo ala e nasin \"%s\"", "argument.block.tag.disallowed": "sina pana e nimi lili. taso, mi wile e leko", "argument.color.invalid": "kule \"%s\" li ike", "argument.component.invalid": "toki ni li ike: %s", "argument.criteria.invalid": "mi sona ala e '%s'", "argument.dimension.invalid": "ma \"%s\" li ike", "argument.double.big": "nanpa ni la mi wile ala e nanpa mute %s. taso, mi kama jo e nanpa %s", "argument.double.low": "nanpa ni la mi wile ala e nanpa lili %s. taso, mi kama jo e nanpa %s", "argument.entity.invalid": "nimi ike anu nimi <PERSON> ike", "argument.entity.notfound.entity": "mi lukin ala e ijo", "argument.entity.notfound.player": "mi lukin ala e jan", "argument.entity.options.advancements.description": "jan pi pali pona ni:", "argument.entity.options.distance.description": "weka tawa ijo kon", "argument.entity.options.distance.negative": "nanpa pi weka tawa ona li nanpa weka, ni li ike", "argument.entity.options.dx.description": "ijo tan x tawa x + dx", "argument.entity.options.dy.description": "ijo tan ma y tawa ma y + dy", "argument.entity.options.dz.description": "ijo tan ma z tawa ma z + dz", "argument.entity.options.gamemode.description": "jan kepeken nasin musi ni:", "argument.entity.options.inapplicable": "nasin pi ijo \"%s\" li ike lon ni", "argument.entity.options.level.description": "mute pi wawa laso", "argument.entity.options.level.negative": "nanpa pi wawa sona li nanpa weka, ni li ike", "argument.entity.options.limit.description": "nanpa sewi ijo", "argument.entity.options.limit.toosmall": "nanpa pi ken suli o suli tawa wan", "argument.entity.options.mode.invalid": "nasin musi \"%s\" li ike", "argument.entity.options.name.description": "nimi ijo", "argument.entity.options.nbt.description": "ijo pi sona NBT ni:", "argument.entity.options.predicate.description": "nimi lili awen ante", "argument.entity.options.scores.description": "ijo pi nanpa pona ni:", "argument.entity.options.sort.description": "o kulupu e ijo", "argument.entity.options.sort.irreversible": "nasin pi ijo wile \"%s\" li ike", "argument.entity.options.tag.description": "ijo pi nimi lili ni:", "argument.entity.options.team.description": "ijo lon kulupu ni:", "argument.entity.options.type.description": "ijo pi nasin ni:", "argument.entity.options.type.invalid": "nasin pi ijo \"%s\" li ike", "argument.entity.options.unknown": "nasin pi ijo \"%s\" li ike", "argument.entity.options.unterminated": "mi wile e pini pi ante nasin", "argument.entity.options.valueless": "mi wile e nanpa lon \"%s\"", "argument.entity.options.x.description": "ma pi nasin x", "argument.entity.options.x_rotation.description": "nasin lukin x pi ijo ni", "argument.entity.options.y.description": "ma pi nasin y", "argument.entity.options.y_rotation.description": "nasin lukin y pi ijo ni", "argument.entity.options.z.description": "ma pi nasin z", "argument.entity.selector.allEntities": "ijo kon ale", "argument.entity.selector.allPlayers": "jan ale", "argument.entity.selector.missing": "nasin pi kulupu pi ijo wile sina li lon ala", "argument.entity.selector.nearestEntity": "ijo pi poka nanpa wan", "argument.entity.selector.nearestPlayer": "jan poka", "argument.entity.selector.not_allowed": "kulupu pi ijo wile ni li ike", "argument.entity.selector.randomPlayer": "jan wan pi nasin ala", "argument.entity.selector.self": "ijo pi toki ni", "argument.entity.selector.unknown": "nasin pi kulupu pi ijo \"%s\" li ike", "argument.entity.toomany": "toki wawa ni li wile e ijo wan taso", "argument.enum.invalid": "ijo ike \"%s\"", "argument.float.big": "nanpa ni la mi wile ala e nanpa mute %s. taso, mi kama jo e nanpa %s", "argument.float.low": "nanpa ni la mi wile ala e nanpa lili %s. taso, mi kama jo e nanpa %s", "argument.gamemode.invalid": "mi sona ala e nasin musi '%s'", "argument.hexcolor.invalid": "'%s' li kule pona ala", "argument.id.invalid": "nanpa li ike", "argument.id.unknown": "nanpa ni li ike: %s", "argument.integer.big": "mi wile ala e nanpa ni: ona li suli tawa %s. taso, mi kama jo e nanpa %s", "argument.integer.low": "mi wile ala e nanpa ni: ona li lili tawa %s. taso, mi kama jo e nanpa %s", "argument.item.id.invalid": "ijo \"%s\" li ike", "argument.item.tag.disallowed": "sina pana e nimi lili. taso, mi wile e ijo", "argument.literal.incorrect": "mi wile e %s", "argument.long.big": "nanpa la mi wile ala e nanpa mute %s. taso, mi kama jo e nanpa %s", "argument.long.low": "nanpa la mi wile ala e nanpa lili %s. taso, mi kama jo e nanpa %s", "argument.message.too_long": "toki li suli ike. (suli %s li ike. suli %s li pona)", "argument.nbt.array.invalid": "nasin pi kulupu nimi \"%s\" li ike", "argument.nbt.array.mixed": "mi ken ala pana e %s tawa %s", "argument.nbt.expected.compound": "mi wile e nimi lili kepeken nimi sona", "argument.nbt.expected.key": "sina pana ala e nanpa open", "argument.nbt.expected.value": "sina pana ala e nanpa", "argument.nbt.list.mixed": "mi ken ala pana e %s tawa lipu pi nasin %s", "argument.nbt.trailing": "sona pi nasin tan li lon. mi wile ala e ni", "argument.player.entities": "toki wawa ni li wile e jan taso", "argument.player.toomany": "toki wawa ni li wile e jan wan taso", "argument.player.unknown": "jan ni li lon ala", "argument.pos.missing.double": "mi wile e lon ma", "argument.pos.missing.int": "mi wile e lon leko", "argument.pos.mixed": "o kepeken sitelen \" ^ \" lon nanpa ale, anu lon nanpa ala", "argument.pos.outofbounds": "lon ma ni li lon insa ala.", "argument.pos.outofworld": "ma ni li lon ala!", "argument.pos.unloaded": "ma ni li lon ala (o tawa lon poka ona)", "argument.pos2d.incomplete": "pini ala (mi wile e nanpa ma tu)", "argument.pos3d.incomplete": "pini ala (mi wile e nanpa ma tu wan)", "argument.range.empty": "mi wile e nanpa anu nanpa mute", "argument.range.ints": "mi wile e nanpa pi kipisi ala", "argument.range.swapped": "nanpa lili li mute tawa nanpa mute, ni li ike", "argument.resource.invalid_type": "ijo '%s' li jo e nasin ike '%s' (ona li wile e '%s')", "argument.resource.not_found": "mi ken ala alasa e ijo '%s' pi nasin '%s'", "argument.resource_or_id.failed_to_parse": "mi sona ala e tomo: %s", "argument.resource_or_id.invalid": "nimi ike", "argument.resource_or_id.no_such_element": "ijo '%s' li lon ala lipu '%s'", "argument.resource_selector.not_found": "ala li sama kulupu wile '%s' pi nasin '%s'", "argument.resource_tag.invalid_type": "nimi '%s' li jo e nasin ike '%s' (ona li wile e '%s')", "argument.resource_tag.not_found": "mi ken ala alasa e nimi '%s' pi nasin '%s'", "argument.rotation.incomplete": "pini ala (mi wile e lon ma tu)", "argument.scoreHolder.empty": "mi lukin ala e poki pi nanpa ni", "argument.scoreboardDisplaySlot.invalid": "poki \"%s\" li ike", "argument.style.invalid": "namako ike: %s", "argument.time.invalid_tick_count": "nanpa pi tenpo lili li ken ala nanpa weka", "argument.time.invalid_unit": "wan ni li ike", "argument.time.tick_count_too_low": "nanpa pi tenpo lili la mi wile ala e nanpa lili %s. taso, mi kama jo e nanpa %s", "argument.uuid.invalid": "<PERSON><PERSON> li ike", "argument.waypoint.invalid": "ijo ni li sitelen ma ala", "arguments.block.tag.unknown": "nimi lili \"%s\" li ike", "arguments.function.tag.unknown": "nimi lili pi poki toki \"%s\" li ike", "arguments.function.unknown": "poki toki %s li ike", "arguments.item.component.expected": "mi wile e kipisi ijo", "arguments.item.component.malformed": "kipisi ike '%s': '%s'", "arguments.item.component.repeated": "kipisi ijo '%s' li mute. taso wan taso li ken lon", "arguments.item.component.unknown": "mi sona ala e kipisi ijo '%s'", "arguments.item.malformed": "ijo ike: '%s'", "arguments.item.overstacked": "sina ken jo e %s pi mute %s taso lon poki wan", "arguments.item.predicate.malformed": "lawa ijo ike '%s': '%s'", "arguments.item.predicate.unknown": "lawa ijo pi sona ala '%s\"", "arguments.item.tag.unknown": "nimi lili pi ijo \"%s\" li ike", "arguments.nbtpath.node.invalid": "nasin pi lipu nanpa li ike", "arguments.nbtpath.nothing_found": "ijo ala li sama %s", "arguments.nbtpath.too_deep": "sona nanpa NBT li suli ike", "arguments.nbtpath.too_large": "sona NBT li suli ike", "arguments.objective.notFound": "wile \"%s\" li ike", "arguments.objective.readonly": "sina ken lukin taso e wile \"%s\"", "arguments.operation.div0": "sina ken ala kipisi kepeken ala a", "arguments.operation.invalid": "pali li ike", "arguments.swizzle.invalid": "mi wile e lon ma \"X\" e lon ma \"Y\" e lon ma \"Z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%2$s: %1$s%%", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "%2$s: +%1$s", "attribute.modifier.plus.1": "%2$s: +%1$s%%", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "%2$s: -%1$s", "attribute.modifier.take.1": "%2$s: -%1$s%%", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "len utala", "attribute.name.armor_toughness": "awen wawa pi len utala", "attribute.name.attack_damage": "wawa utala", "attribute.name.attack_knockback": "wawa utala pi tawa monsi", "attribute.name.attack_speed": "wawa tenpo utala", "attribute.name.block_break_speed": "wawa tenpo pi pakala leko", "attribute.name.block_interaction_range": "suli weka pi ken pali leko", "attribute.name.burning_time": "tenpo seli", "attribute.name.camera_distance": "weka pi lukin sina", "attribute.name.entity_interaction_range": "suli weka pi ken pali ijo", "attribute.name.explosion_knockback_resistance": "lili tawa tan seli pakala", "attribute.name.fall_damage_multiplier": "suli pakala pi tawa anpa", "attribute.name.flying_speed": "wawa pi tawa kon", "attribute.name.follow_range": "weka la monsuta li tawa", "attribute.name.generic.armor": "awen len", "attribute.name.generic.armor_toughness": "awen wawa len", "attribute.name.generic.attack_damage": "wawa utala", "attribute.name.generic.attack_knockback": "wawa pi tawa monsi utala", "attribute.name.generic.attack_speed": "wawa tenpo utala", "attribute.name.generic.block_interaction_range": "suli weka pi ante leko", "attribute.name.generic.burning_time": "tenpo seli", "attribute.name.generic.entity_interaction_range": "suli weka pi ante ijo", "attribute.name.generic.explosion_knockback_resistance": "lili tawa tan seli pakala", "attribute.name.generic.fall_damage_multiplier": "suli pakala pi tawa anpa", "attribute.name.generic.flying_speed": "wawa pi tawa kon", "attribute.name.generic.follow_range": "weka la monsuta li tawa", "attribute.name.generic.gravity": "wawa anpa", "attribute.name.generic.jump_strength": "wawa pi tawa sewi", "attribute.name.generic.knockback_resistance": "tawa tan utala", "attribute.name.generic.luck": "pona ken", "attribute.name.generic.max_absorption": "nanpa suli pi pilin awen", "attribute.name.generic.max_health": "sijelo nanpa suli", "attribute.name.generic.movement_efficiency": "wawa tawa lon supa ike", "attribute.name.generic.movement_speed": "wawa tawa", "attribute.name.generic.oxygen_bonus": "kon namako", "attribute.name.generic.safe_fall_distance": "sewi pi tawa anpa pona", "attribute.name.generic.scale": "suli", "attribute.name.generic.step_height": "sewi tawa", "attribute.name.generic.water_movement_efficiency": "wawa tawa lon telo", "attribute.name.gravity": "wawa anpa", "attribute.name.horse.jump_strength": "wawa pi tawa sewi pi soweli tawa", "attribute.name.jump_strength": "wawa pi tawa sewi", "attribute.name.knockback_resistance": "lili tawa tan utala", "attribute.name.luck": "ken pona", "attribute.name.max_absorption": "nanpa suli pi pilin awen", "attribute.name.max_health": "sijelo nanpa suli", "attribute.name.mining_efficiency": "wawa pakala", "attribute.name.movement_efficiency": "wawa tawa", "attribute.name.movement_speed": "wawa pi tenpo tawa", "attribute.name.oxygen_bonus": "kon namako", "attribute.name.player.block_break_speed": "wawa tawa pi pakala leko", "attribute.name.player.block_interaction_range": "suli weka pi ante leko", "attribute.name.player.entity_interaction_range": "suli weka pi ante ijo", "attribute.name.player.mining_efficiency": "wawa pakala", "attribute.name.player.sneaking_speed": "wawa pi tawa anpa", "attribute.name.player.submerged_mining_speed": "wawa pakala lon telo", "attribute.name.player.sweeping_damage_ratio": "suli pakala pi utala mute", "attribute.name.safe_fall_distance": "sewi pi tawa anpa pona", "attribute.name.scale": "suli", "attribute.name.sneaking_speed": "wawa tawa pi tawa anpa", "attribute.name.spawn_reinforcements": "pana pona pi jan moli", "attribute.name.step_height": "sewi tawa", "attribute.name.submerged_mining_speed": "wawa pakala lon telo", "attribute.name.sweeping_damage_ratio": "suli pakala pi utala mute", "attribute.name.tempt_range": "weka pi soweli wile", "attribute.name.water_movement_efficiency": "wawa tawa lon telo", "attribute.name.waypoint_receive_range": "weka lukin pi sitelen ma", "attribute.name.waypoint_transmit_range": "weka pana pi sitelen ma", "attribute.name.zombie.spawn_reinforcements": "jan moli <PERSON> kama", "biome.minecraft.badlands": "ma pi kiwen kule", "biome.minecraft.bamboo_jungle": "ma pi kasi palisa", "biome.minecraft.basalt_deltas": "ma pi seli lete", "biome.minecraft.beach": "ma lon poka telo", "biome.minecraft.birch_forest": "ma pi kasi jelo", "biome.minecraft.cherry_grove": "ma pi kasi suwi", "biome.minecraft.cold_ocean": "telo lete", "biome.minecraft.crimson_forest": "ma soko loje", "biome.minecraft.dark_forest": "ma pi kasi pimeja", "biome.minecraft.deep_cold_ocean": "telo lete anpa", "biome.minecraft.deep_dark": "lupa pimeja", "biome.minecraft.deep_frozen_ocean": "telo anpa pi supa kiwen", "biome.minecraft.deep_lukewarm_ocean": "telo anpa pi seli lili", "biome.minecraft.deep_ocean": "telo anpa", "biome.minecraft.desert": "ma seli", "biome.minecraft.dripstone_caves": "lupa palisa", "biome.minecraft.end_barrens": "ma <PERSON> weka", "biome.minecraft.end_highlands": "ma <PERSON>", "biome.minecraft.end_midlands": "ma <PERSON>a", "biome.minecraft.eroded_badlands": "ma pakala pi kiwen kule", "biome.minecraft.flower_forest": "ma pi kasi kule", "biome.minecraft.forest": "ma kasi", "biome.minecraft.frozen_ocean": "telo pi supa kiwen", "biome.minecraft.frozen_peaks": "nena sewi pi telo kiwen", "biome.minecraft.frozen_river": "telo linja pi supa kiwen", "biome.minecraft.grove": "ma kasi lete sewi", "biome.minecraft.ice_spikes": "palisa pi kiwen telo", "biome.minecraft.jagged_peaks": "nena sewi pakala", "biome.minecraft.jungle": "ma pi kasi suli", "biome.minecraft.lukewarm_ocean": "telo pi seli lili", "biome.minecraft.lush_caves": "lupa kasi", "biome.minecraft.mangrove_swamp": "ma pi kasi kiwen telo", "biome.minecraft.meadow": "ma laso sewi", "biome.minecraft.mushroom_fields": "ma soko", "biome.minecraft.nether_wastes": "ma Nether pi anpa loje", "biome.minecraft.ocean": "telo", "biome.minecraft.old_growth_birch_forest": "ma pi kasi jelo suli", "biome.minecraft.old_growth_pine_taiga": "ma pi kasi lete suli pi lipu mute ala", "biome.minecraft.old_growth_spruce_taiga": "ma pi kasi lete suli", "biome.minecraft.pale_garden": "ma pi kasi walo", "biome.minecraft.plains": "supa pi kasi lili", "biome.minecraft.river": "telo linja", "biome.minecraft.savanna": "ma pi kasi seli", "biome.minecraft.savanna_plateau": "ma supa pi kasi seli", "biome.minecraft.small_end_islands": "ma <PERSON> lili", "biome.minecraft.snowy_beach": "ma lete lon poka telo", "biome.minecraft.snowy_plains": "ma supa lete", "biome.minecraft.snowy_slopes": "nena suli pi ko lete", "biome.minecraft.snowy_taiga": "ma lete pi kasi lete", "biome.minecraft.soul_sand_valley": "ma pi ko kon", "biome.minecraft.sparse_jungle": "ma pi laso wawa", "biome.minecraft.stony_peaks": "nena suli kiwen", "biome.minecraft.stony_shore": "ma kiwen lon poka telo", "biome.minecraft.sunflower_plains": "ma pi kasi suno", "biome.minecraft.swamp": "ma pi telo jaki", "biome.minecraft.taiga": "ma pi kasi lete", "biome.minecraft.the_end": "ma <PERSON>", "biome.minecraft.the_void": "pimeja anpa", "biome.minecraft.warm_ocean": "telo seli", "biome.minecraft.warped_forest": "ma soko laso", "biome.minecraft.windswept_forest": "nena suli kasi", "biome.minecraft.windswept_gravelly_hills": "nena suli pi kiwen lili", "biome.minecraft.windswept_hills": "nena suli", "biome.minecraft.windswept_savanna": "ma pakala pi kasi seli", "biome.minecraft.wooded_badlands": "ma kasi pi kiwen kule", "block.minecraft.acacia_button": "nena ilo pi kasi seli", "block.minecraft.acacia_door": "lupa pi kasi seli", "block.minecraft.acacia_fence": "sinpin palisa pi kasi seli", "block.minecraft.acacia_fence_gate": "sinpin tawa pi kasi seli", "block.minecraft.acacia_hanging_sign": "lipu sitelen sewi pi kasi seli", "block.minecraft.acacia_leaves": "lipu pi kasi seli", "block.minecraft.acacia_log": "sijelo pi kasi seli", "block.minecraft.acacia_planks": "kiwen kipisi pi kasi seli", "block.minecraft.acacia_pressure_plate": "supa noka pi kasi seli", "block.minecraft.acacia_sapling": "kasi seli sin", "block.minecraft.acacia_sign": "lipu sitelen pi kasi seli", "block.minecraft.acacia_slab": "supa pi kasi seli", "block.minecraft.acacia_stairs": "leko sewi pi kasi seli", "block.minecraft.acacia_trapdoor": "lupa anpa pi kasi seli", "block.minecraft.acacia_wall_hanging_sign": "lipu sitelen sewi pi kasi loje jelo lon sinpin", "block.minecraft.acacia_wall_sign": "lipu sitelen kasi pi loje jelo lon sinpin", "block.minecraft.acacia_wood": "kiwen pi kasi seli", "block.minecraft.activator_rail": "nasin palisa weka", "block.minecraft.air": "kon", "block.minecraft.allium": "kasi pi laso loje", "block.minecraft.amethyst_block": "kiwen kalama", "block.minecraft.amethyst_cluster": "kulupu pi kiwen kalama", "block.minecraft.ancient_debris": "pakala mani", "block.minecraft.andesite": "kiwen pimeja walo", "block.minecraft.andesite_slab": "supa pi kiwen pimeja walo", "block.minecraft.andesite_stairs": "leko sewi pi kiwen pimeja walo", "block.minecraft.andesite_wall": "sinpin pi kiwen pimeja walo", "block.minecraft.anvil": "supa pona", "block.minecraft.attached_melon_stem": "kasi lon kili suli laso", "block.minecraft.attached_pumpkin_stem": "kasi pi kili suli lon kili", "block.minecraft.azalea": "kasi lupa", "block.minecraft.azalea_leaves": "lipu pi kasi kiwen lupa", "block.minecraft.azure_bluet": "kasi walo lili", "block.minecraft.bamboo": "kasi palisa", "block.minecraft.bamboo_block": "leko pi kasi palisa", "block.minecraft.bamboo_button": "nena ilo pi kasi palisa", "block.minecraft.bamboo_door": "lupa pi kasi palisa", "block.minecraft.bamboo_fence": "sinpin palisa pi kasi palisa", "block.minecraft.bamboo_fence_gate": "sinpin tawa pi kasi palisa", "block.minecraft.bamboo_hanging_sign": "lipu sitelen sewi pi kasi palisa", "block.minecraft.bamboo_mosaic": "kiwen kipisi namako pi kasi palisa", "block.minecraft.bamboo_mosaic_slab": "supa namako pi kasi palisa", "block.minecraft.bamboo_mosaic_stairs": "leko sewi namako pi kasi palisa", "block.minecraft.bamboo_planks": "kiwen kipisi pi kasi palisa", "block.minecraft.bamboo_pressure_plate": "supa noka pi kasi palisa", "block.minecraft.bamboo_sapling": "kasi palisa sin", "block.minecraft.bamboo_sign": "lipu sitelen pi kasi palisa", "block.minecraft.bamboo_slab": "supa pi kasi palisa", "block.minecraft.bamboo_stairs": "leko sewi pi kasi palisa", "block.minecraft.bamboo_trapdoor": "lupa anpa pi kasi palisa", "block.minecraft.bamboo_wall_hanging_sign": "lipu sitelen sewi pi kasi palisa lon sinpin", "block.minecraft.bamboo_wall_sign": "lipu sitelen pi kasi palisa lon sinpin", "block.minecraft.banner.base.black": "ale pimeja", "block.minecraft.banner.base.blue": "ale pi laso telo", "block.minecraft.banner.base.brown": "ale pi loje ma", "block.minecraft.banner.base.cyan": "ale laso", "block.minecraft.banner.base.gray": "ale pi pimeja walo", "block.minecraft.banner.base.green": "ale pi laso kasi", "block.minecraft.banner.base.light_blue": "ale pi laso sewi", "block.minecraft.banner.base.light_gray": "ale pi walo pimeja", "block.minecraft.banner.base.lime": "ale pi laso jelo", "block.minecraft.banner.base.magenta": "ale pi loje laso", "block.minecraft.banner.base.orange": "ale pi loje jelo", "block.minecraft.banner.base.pink": "ale pi loje walo", "block.minecraft.banner.base.purple": "ale pi laso loje", "block.minecraft.banner.base.red": "ale loje", "block.minecraft.banner.base.white": "ale walo", "block.minecraft.banner.base.yellow": "ale jelo", "block.minecraft.banner.border.black": "selo pimeja", "block.minecraft.banner.border.blue": "selo pi laso telo", "block.minecraft.banner.border.brown": "selo pi loje ma", "block.minecraft.banner.border.cyan": "selo laso", "block.minecraft.banner.border.gray": "selo pi pimeja walo", "block.minecraft.banner.border.green": "selo pi laso kasi", "block.minecraft.banner.border.light_blue": "selo pi laso sewi", "block.minecraft.banner.border.light_gray": "selo pi walo pimeja", "block.minecraft.banner.border.lime": "selo pi laso jelo", "block.minecraft.banner.border.magenta": "selo pi loje laso", "block.minecraft.banner.border.orange": "selo pi loje jelo", "block.minecraft.banner.border.pink": "selo pi loje walo", "block.minecraft.banner.border.purple": "selo pi laso loje", "block.minecraft.banner.border.red": "selo loje", "block.minecraft.banner.border.white": "selo walo", "block.minecraft.banner.border.yellow": "selo jelo", "block.minecraft.banner.bricks.black": "leko lili p<PERSON>ja", "block.minecraft.banner.bricks.blue": "leko lili pi laso telo", "block.minecraft.banner.bricks.brown": "leko lili pi loje ma", "block.minecraft.banner.bricks.cyan": "leko lili laso", "block.minecraft.banner.bricks.gray": "leko lili pi pimeja walo", "block.minecraft.banner.bricks.green": "leko lili pi laso kasi", "block.minecraft.banner.bricks.light_blue": "leko lili pi laso sewi", "block.minecraft.banner.bricks.light_gray": "leko lili pi walo pimeja", "block.minecraft.banner.bricks.lime": "leko lili pi laso jelo", "block.minecraft.banner.bricks.magenta": "leko lili pi loje laso", "block.minecraft.banner.bricks.orange": "leko lili pi loje jelo", "block.minecraft.banner.bricks.pink": "leko lili pi loje walo", "block.minecraft.banner.bricks.purple": "leko lili pi laso loje", "block.minecraft.banner.bricks.red": "leko lili loje", "block.minecraft.banner.bricks.white": "leko lili walo", "block.minecraft.banner.bricks.yellow": "leko lili jelo", "block.minecraft.banner.circle.black": "sike pimeja", "block.minecraft.banner.circle.blue": "sike pi laso telo", "block.minecraft.banner.circle.brown": "sike pi loje ma", "block.minecraft.banner.circle.cyan": "sike laso", "block.minecraft.banner.circle.gray": "sike pi pimeja walo", "block.minecraft.banner.circle.green": "sike pi laso kasi", "block.minecraft.banner.circle.light_blue": "sike pi laso sewi", "block.minecraft.banner.circle.light_gray": "sike pi walo pimeja", "block.minecraft.banner.circle.lime": "sike pi laso jelo", "block.minecraft.banner.circle.magenta": "sike pi loje laso", "block.minecraft.banner.circle.orange": "sike pi loje jelo", "block.minecraft.banner.circle.pink": "sike pi loje walo", "block.minecraft.banner.circle.purple": "sike pi laso loje", "block.minecraft.banner.circle.red": "sike loje", "block.minecraft.banner.circle.white": "sike walo", "block.minecraft.banner.circle.yellow": "sike jelo", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> pimeja", "block.minecraft.banner.creeper.blue": "monsuta Creeper pi laso telo", "block.minecraft.banner.creeper.brown": "mon<PERSON><PERSON> pi loje ma", "block.minecraft.banner.creeper.cyan": "mon<PERSON>ta <PERSON> laso", "block.minecraft.banner.creeper.gray": "monsuta Creeper pi pimeja walo", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON> Creeper pi laso kasi", "block.minecraft.banner.creeper.light_blue": "monsuta Creeper pi laso sewi", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON>reeper pi walo pimeja", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON> Creeper pi laso jelo", "block.minecraft.banner.creeper.magenta": "mon<PERSON>ta Creeper pi loje laso", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON>ree<PERSON> pi loje jelo", "block.minecraft.banner.creeper.pink": "mon<PERSON><PERSON>ree<PERSON> pi loje walo", "block.minecraft.banner.creeper.purple": "mon<PERSON><PERSON> Creeper pi laso loje", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON> loje", "block.minecraft.banner.creeper.white": "mon<PERSON><PERSON> walo", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON> jelo", "block.minecraft.banner.cross.black": "sitelen 'x' pimeja", "block.minecraft.banner.cross.blue": "sitelen 'x' pi laso telo", "block.minecraft.banner.cross.brown": "sitelen 'x' pi loje ma", "block.minecraft.banner.cross.cyan": "sitelen 'x' laso", "block.minecraft.banner.cross.gray": "sitelen 'x' pi pimeja walo", "block.minecraft.banner.cross.green": "sitelen 'x' pi laso kasi", "block.minecraft.banner.cross.light_blue": "sitelen 'x' pi laso sewi", "block.minecraft.banner.cross.light_gray": "sitelen 'x' pi walo pimeja", "block.minecraft.banner.cross.lime": "sitelen 'x' pi laso jelo", "block.minecraft.banner.cross.magenta": "sitelen 'x' pi loje laso", "block.minecraft.banner.cross.orange": "sitelen 'x' pi loje jelo", "block.minecraft.banner.cross.pink": "sitelen 'x' pi loje walo", "block.minecraft.banner.cross.purple": "sitelen 'x' pi laso loje", "block.minecraft.banner.cross.red": "sitelen 'x' loje", "block.minecraft.banner.cross.white": "sitelen 'x' walo", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> 'x' jelo", "block.minecraft.banner.curly_border.black": "selo nena pimeja", "block.minecraft.banner.curly_border.blue": "selo nena pi laso telo", "block.minecraft.banner.curly_border.brown": "selo nena pi loje ma", "block.minecraft.banner.curly_border.cyan": "selo nena laso", "block.minecraft.banner.curly_border.gray": "selo nena pi pimeja walo", "block.minecraft.banner.curly_border.green": "selo nena pi laso kasi", "block.minecraft.banner.curly_border.light_blue": "selo nena pi laso sewi", "block.minecraft.banner.curly_border.light_gray": "selo nena pi walo pimeja", "block.minecraft.banner.curly_border.lime": "selo nena pi laso jelo", "block.minecraft.banner.curly_border.magenta": "selo nena pi loje laso", "block.minecraft.banner.curly_border.orange": "selo nena pi loje jelo", "block.minecraft.banner.curly_border.pink": "selo nena pi loje walo", "block.minecraft.banner.curly_border.purple": "selo nena pi laso loje", "block.minecraft.banner.curly_border.red": "selo nena loje", "block.minecraft.banner.curly_border.white": "selo nena walo", "block.minecraft.banner.curly_border.yellow": "selo nena jelo", "block.minecraft.banner.diagonal_left.black": "kipisi pimeja lon poka open sewi", "block.minecraft.banner.diagonal_left.blue": "kipisi pi laso telo lon poka open sewi", "block.minecraft.banner.diagonal_left.brown": "kipisi pi loje ma lon poka open sewi", "block.minecraft.banner.diagonal_left.cyan": "kipisi laso lon poka open sewi", "block.minecraft.banner.diagonal_left.gray": "kipisi pi pimeja walo lon poka open sewi", "block.minecraft.banner.diagonal_left.green": "kipisi pi laso kasi lon poka open sewi", "block.minecraft.banner.diagonal_left.light_blue": "kipisi pi laso sewi lon poka open sewi", "block.minecraft.banner.diagonal_left.light_gray": "kipisi pi walo pimeja lon poka open sewi", "block.minecraft.banner.diagonal_left.lime": "kipisi pi laso jelo lon poka open sewi", "block.minecraft.banner.diagonal_left.magenta": "kipisi pi loje laso lon poka open sewi", "block.minecraft.banner.diagonal_left.orange": "kipisi pi loje jelo lon poka open sewi", "block.minecraft.banner.diagonal_left.pink": "kipisi pi loje walo lon poka open sewi", "block.minecraft.banner.diagonal_left.purple": "kipisi pi laso loje lon poka open sewi", "block.minecraft.banner.diagonal_left.red": "kipisi loje lon poka open sewi", "block.minecraft.banner.diagonal_left.white": "kipisi walo lon poka open sewi", "block.minecraft.banner.diagonal_left.yellow": "kipisi jelo lon poka open sewi", "block.minecraft.banner.diagonal_right.black": "kipisi pimeja lon poka pini sewi", "block.minecraft.banner.diagonal_right.blue": "kipisi pi laso telo lon poka pini sewi", "block.minecraft.banner.diagonal_right.brown": "kipisi pi loje ma lon poka pini sewi", "block.minecraft.banner.diagonal_right.cyan": "kipisi laso lon poka pini sewi", "block.minecraft.banner.diagonal_right.gray": "kipisi pi pimeja walo lon poka pini sewi", "block.minecraft.banner.diagonal_right.green": "kipisi pi laso kasi lon poka pini sewi", "block.minecraft.banner.diagonal_right.light_blue": "kipisi pi laso sewi lon poka pini sewi", "block.minecraft.banner.diagonal_right.light_gray": "kipisi pi walo pimeja lon poka pini sewi", "block.minecraft.banner.diagonal_right.lime": "kipisi laso lon poka pini sewi", "block.minecraft.banner.diagonal_right.magenta": "kipisi pi loje laso lon poka pini sewi", "block.minecraft.banner.diagonal_right.orange": "kipisi pi loje jelo lon poka pini sewi", "block.minecraft.banner.diagonal_right.pink": "kipisi pi loje walo lon poka pini sewi", "block.minecraft.banner.diagonal_right.purple": "kipisi pi laso loje lon poka pini sewi", "block.minecraft.banner.diagonal_right.red": "kipisi loje lon poka pini sewi", "block.minecraft.banner.diagonal_right.white": "kipisi walo lon poka pini sewi", "block.minecraft.banner.diagonal_right.yellow": "kipisi jelo lon poka pini sewi", "block.minecraft.banner.diagonal_up_left.black": "kipisi pimeja lon poka open anpa", "block.minecraft.banner.diagonal_up_left.blue": "kipisi pi laso telo lon poka open anpa", "block.minecraft.banner.diagonal_up_left.brown": "kipisi pi loje ma lon poka open anpa", "block.minecraft.banner.diagonal_up_left.cyan": "kipisi laso lon poka open anpa", "block.minecraft.banner.diagonal_up_left.gray": "kipisi pi pimeja walo lon poka open anpa", "block.minecraft.banner.diagonal_up_left.green": "kipisi pi laso kasi lon poka open anpa", "block.minecraft.banner.diagonal_up_left.light_blue": "kipisi pi laso sewi lon poka open anpa", "block.minecraft.banner.diagonal_up_left.light_gray": "kipisi pi walo pimeja lon poka open anpa", "block.minecraft.banner.diagonal_up_left.lime": "kipisi pi laso jelo lon poka open anpa", "block.minecraft.banner.diagonal_up_left.magenta": "kipisi pi loje laso lon poka open anpa", "block.minecraft.banner.diagonal_up_left.orange": "kipisi pi loje jelo lon poka open anpa", "block.minecraft.banner.diagonal_up_left.pink": "kipisi pi loje walo lon poka open anpa", "block.minecraft.banner.diagonal_up_left.purple": "kipisi pi laso loje lon poka open anpa", "block.minecraft.banner.diagonal_up_left.red": "kipisi loje lon poka open anpa", "block.minecraft.banner.diagonal_up_left.white": "kipisi walo lon poka open anpa", "block.minecraft.banner.diagonal_up_left.yellow": "kipisi jelo lon poka open anpa", "block.minecraft.banner.diagonal_up_right.black": "kipisi pimeja lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.blue": "kipisi pi laso telo lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.brown": "kipisi pi loje ma lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.cyan": "kipisi laso lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.gray": "kipisi pi pimeja walo lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.green": "kipisi pi laso kasi lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.light_blue": "kipisi pi laso sewi lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.light_gray": "kipisi pi walo pimeja lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.lime": "kipisi pi laso jelo lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.magenta": "kipisi loje laso lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.orange": "kipisi pi loje jelo lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.pink": "kipisi pi loje walo lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.purple": "kipisi pi laso loje lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.red": "kipisi loje lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.white": "kipisi walo lon poka pini anpa", "block.minecraft.banner.diagonal_up_right.yellow": "kipisi jelo lon poka pini anpa", "block.minecraft.banner.flow.black": "esun pimeja", "block.minecraft.banner.flow.blue": "esun pi laso telo", "block.minecraft.banner.flow.brown": "esun pi loje ma", "block.minecraft.banner.flow.cyan": "esun laso", "block.minecraft.banner.flow.gray": "esun pi pimeja walo", "block.minecraft.banner.flow.green": "esun pi laso kasi", "block.minecraft.banner.flow.light_blue": "esun pi laso sewi", "block.minecraft.banner.flow.light_gray": "esun pi walo pimeja", "block.minecraft.banner.flow.lime": "esun pi laso jelo", "block.minecraft.banner.flow.magenta": "esun pi loje laso", "block.minecraft.banner.flow.orange": "esun pi loje jelo", "block.minecraft.banner.flow.pink": "esun pi loje walo", "block.minecraft.banner.flow.purple": "esun pi laso loje", "block.minecraft.banner.flow.red": "esun loje", "block.minecraft.banner.flow.white": "esun walo", "block.minecraft.banner.flow.yellow": "esun jelo", "block.minecraft.banner.flower.black": "sike kasi pimeja", "block.minecraft.banner.flower.blue": "sike kasi pi laso telo", "block.minecraft.banner.flower.brown": "sike kasi pi loje ma", "block.minecraft.banner.flower.cyan": "sike kasi laso", "block.minecraft.banner.flower.gray": "sike kasi pi pimeja walo", "block.minecraft.banner.flower.green": "sike kasi pi laso kasi", "block.minecraft.banner.flower.light_blue": "sike kasi pi laso sewi", "block.minecraft.banner.flower.light_gray": "sike kasi pi walo pimeja", "block.minecraft.banner.flower.lime": "sike kasi pi laso jelo", "block.minecraft.banner.flower.magenta": "sike kasi pi loje laso", "block.minecraft.banner.flower.orange": "sike kasi pi loje jelo", "block.minecraft.banner.flower.pink": "sike kasi pi loje walo", "block.minecraft.banner.flower.purple": "sike kasi pi laso loje", "block.minecraft.banner.flower.red": "sike kasi loje", "block.minecraft.banner.flower.white": "sike kasi walo", "block.minecraft.banner.flower.yellow": "sike kasi jelo", "block.minecraft.banner.globe.black": "<PERSON>len pimeja pi sike ma", "block.minecraft.banner.globe.blue": "sitelen pi sike ma pi laso telo", "block.minecraft.banner.globe.brown": "sitelen pi ma ali pi loje ma", "block.minecraft.banner.globe.cyan": "sitelen laso pi sike ma", "block.minecraft.banner.globe.gray": "sitelen pi sike ma pi pimeja walo", "block.minecraft.banner.globe.green": "sitelen pi sike ma pi laso kasi", "block.minecraft.banner.globe.light_blue": "sitelen pi sike ma pi laso sewi", "block.minecraft.banner.globe.light_gray": "sitelen pi sike ma pi walo pimeja", "block.minecraft.banner.globe.lime": "sitelen pi sike ma pi laso jelo", "block.minecraft.banner.globe.magenta": "sitelen pi sike ma pi loje laso", "block.minecraft.banner.globe.orange": "sitelen pi sike ma pi loje jelo", "block.minecraft.banner.globe.pink": "sitelen pi sike ma pi loje walo", "block.minecraft.banner.globe.purple": "sitelen pi sike ma pi laso loje", "block.minecraft.banner.globe.red": "sitelen loje pi sike ma", "block.minecraft.banner.globe.white": "sitelen walo pi sike ma", "block.minecraft.banner.globe.yellow": "sitelen jelo pi sike ma", "block.minecraft.banner.gradient.black": "pimeja kama sewi", "block.minecraft.banner.gradient.blue": "laso telo kama sewi", "block.minecraft.banner.gradient.brown": "loje ma kama sewi", "block.minecraft.banner.gradient.cyan": "laso kama sewi", "block.minecraft.banner.gradient.gray": "pimeja walo kama sewi", "block.minecraft.banner.gradient.green": "laso kasi kama sewi", "block.minecraft.banner.gradient.light_blue": "laso sewi kama sewi", "block.minecraft.banner.gradient.light_gray": "walo pimeja kama sewi", "block.minecraft.banner.gradient.lime": "laso jelo kama sewi", "block.minecraft.banner.gradient.magenta": "loje laso kama sewi", "block.minecraft.banner.gradient.orange": "loje jelo kama sewi", "block.minecraft.banner.gradient.pink": "loje walo kama sewi", "block.minecraft.banner.gradient.purple": "laso loje kama sewi", "block.minecraft.banner.gradient.red": "loje kama sewi", "block.minecraft.banner.gradient.white": "walo kama sewi", "block.minecraft.banner.gradient.yellow": "jelo kama sewi", "block.minecraft.banner.gradient_up.black": "pimeja kama anpa", "block.minecraft.banner.gradient_up.blue": "laso telo kama anpa", "block.minecraft.banner.gradient_up.brown": "loje ma kama anpa", "block.minecraft.banner.gradient_up.cyan": "laso kama anpa", "block.minecraft.banner.gradient_up.gray": "pimeja walo kama anpa", "block.minecraft.banner.gradient_up.green": "laso kasi kama anpa", "block.minecraft.banner.gradient_up.light_blue": "laso sewi kama anpa", "block.minecraft.banner.gradient_up.light_gray": "walo pimeja kama anpa", "block.minecraft.banner.gradient_up.lime": "laso jelo kama anpa", "block.minecraft.banner.gradient_up.magenta": "loje laso kama anpa", "block.minecraft.banner.gradient_up.orange": "loje jelo kama anpa", "block.minecraft.banner.gradient_up.pink": "loje walo kama anpa", "block.minecraft.banner.gradient_up.purple": "laso loje kama anpa", "block.minecraft.banner.gradient_up.red": "loje kama anpa", "block.minecraft.banner.gradient_up.white": "walo kama anpa", "block.minecraft.banner.gradient_up.yellow": "jelo kama anpa", "block.minecraft.banner.guster.black": "monsuta kon pimeja", "block.minecraft.banner.guster.blue": "monsuta kon pi laso telo", "block.minecraft.banner.guster.brown": "monsuta kon pi loje ma", "block.minecraft.banner.guster.cyan": "monsuta kon laso", "block.minecraft.banner.guster.gray": "monsuta kon pi pimeja walo", "block.minecraft.banner.guster.green": "monsuta kon pi laso kasi", "block.minecraft.banner.guster.light_blue": "monsuta kon pi laso sewi", "block.minecraft.banner.guster.light_gray": "monsuta kon pi walo pimeja", "block.minecraft.banner.guster.lime": "monsuta kon pi laso jelo", "block.minecraft.banner.guster.magenta": "monsuta kon pi loje laso", "block.minecraft.banner.guster.orange": "monsuta kon pi loje jelo", "block.minecraft.banner.guster.pink": "monsuta kon pi loje walo", "block.minecraft.banner.guster.purple": "monsuta kon pi laso loje", "block.minecraft.banner.guster.red": "monsuta kon loje", "block.minecraft.banner.guster.white": "monsuta kon walo", "block.minecraft.banner.guster.yellow": "monsuta kon jelo", "block.minecraft.banner.half_horizontal.black": "kip<PERSON> sewi pimeja", "block.minecraft.banner.half_horizontal.blue": "kipisi sewi pi laso telo", "block.minecraft.banner.half_horizontal.brown": "kipisi sewi pi loje ma", "block.minecraft.banner.half_horizontal.cyan": "kipisi sewi laso", "block.minecraft.banner.half_horizontal.gray": "kipisi sewi pi pimeja walo", "block.minecraft.banner.half_horizontal.green": "kipisi sewi pi laso kasi", "block.minecraft.banner.half_horizontal.light_blue": "kipisi sewi pi laso sewi", "block.minecraft.banner.half_horizontal.light_gray": "kipisi sewi pi walo pimeja", "block.minecraft.banner.half_horizontal.lime": "kipisi sewi pi laso jelo", "block.minecraft.banner.half_horizontal.magenta": "kipisi sewi pi loje laso", "block.minecraft.banner.half_horizontal.orange": "kipisi sewi pi loje jelo", "block.minecraft.banner.half_horizontal.pink": "kipisi sewi pi loje walo", "block.minecraft.banner.half_horizontal.purple": "kipisi sewi pi laso loje", "block.minecraft.banner.half_horizontal.red": "kipisi sewi loje", "block.minecraft.banner.half_horizontal.white": "kipisi sewi walo", "block.minecraft.banner.half_horizontal.yellow": "kip<PERSON> sewi jelo", "block.minecraft.banner.half_horizontal_bottom.black": "kipisi anpa pimeja", "block.minecraft.banner.half_horizontal_bottom.blue": "kipisi anpa pi laso telo", "block.minecraft.banner.half_horizontal_bottom.brown": "kipisi anpa pi loje ma", "block.minecraft.banner.half_horizontal_bottom.cyan": "kipisi anpa laso", "block.minecraft.banner.half_horizontal_bottom.gray": "kipisi anpa pi pimeja walo", "block.minecraft.banner.half_horizontal_bottom.green": "kipisi anpa pi laso kasi", "block.minecraft.banner.half_horizontal_bottom.light_blue": "kipisi anpa pi laso sewi", "block.minecraft.banner.half_horizontal_bottom.light_gray": "kipisi anpa pi walo pimeja", "block.minecraft.banner.half_horizontal_bottom.lime": "kipisi anpa pi laso jelo", "block.minecraft.banner.half_horizontal_bottom.magenta": "kipisi anpa pi loje laso", "block.minecraft.banner.half_horizontal_bottom.orange": "kipisi anpa pi loje jelo", "block.minecraft.banner.half_horizontal_bottom.pink": "kipisi anpa pi loje walo", "block.minecraft.banner.half_horizontal_bottom.purple": "kipisi anpa pi laso loje", "block.minecraft.banner.half_horizontal_bottom.red": "kipisi anpa loje", "block.minecraft.banner.half_horizontal_bottom.white": "kipisi anpa walo", "block.minecraft.banner.half_horizontal_bottom.yellow": "kipisi anpa jelo", "block.minecraft.banner.half_vertical.black": "kipisi pimeja lon poka open", "block.minecraft.banner.half_vertical.blue": "kipisi pi laso telo lon poka open", "block.minecraft.banner.half_vertical.brown": "kipisi pi loje ma lon poka open", "block.minecraft.banner.half_vertical.cyan": "kipisi laso lon poka open", "block.minecraft.banner.half_vertical.gray": "kipisi pi pimeja walo lon poka open", "block.minecraft.banner.half_vertical.green": "kipisi pi laso kasi lon poka open", "block.minecraft.banner.half_vertical.light_blue": "kipisi pi laso sewi lon poka open", "block.minecraft.banner.half_vertical.light_gray": "kipisi pi walo pimeja lon poka open", "block.minecraft.banner.half_vertical.lime": "kipisi pi laso jelo lon poka open", "block.minecraft.banner.half_vertical.magenta": "kipisi pi loje laso lon poka open", "block.minecraft.banner.half_vertical.orange": "kipisi pi loje jelo lon poka open", "block.minecraft.banner.half_vertical.pink": "kipisi pi loje walo lon poka open", "block.minecraft.banner.half_vertical.purple": "tu laso loje lon poka pilin", "block.minecraft.banner.half_vertical.red": "kipisi loje lon poka open", "block.minecraft.banner.half_vertical.white": "kipisi walo lon poka open", "block.minecraft.banner.half_vertical.yellow": "kipisi jelo lon poka open", "block.minecraft.banner.half_vertical_right.black": "kipisi pimeja lon poka pini", "block.minecraft.banner.half_vertical_right.blue": "kipisi pi laso telo lon poka pini", "block.minecraft.banner.half_vertical_right.brown": "kipisi pi loje ma lon poka pini", "block.minecraft.banner.half_vertical_right.cyan": "kipisi laso lon poka pini", "block.minecraft.banner.half_vertical_right.gray": "kipisi pi pimeja walo lon poka pini", "block.minecraft.banner.half_vertical_right.green": "kipisi pi laso kasi lon poka pini", "block.minecraft.banner.half_vertical_right.light_blue": "kipisi pi laso sewi lon poka pini", "block.minecraft.banner.half_vertical_right.light_gray": "kipisi pi walo pimeja lon poka pini", "block.minecraft.banner.half_vertical_right.lime": "kipisi pi laso jelo lon poka pini", "block.minecraft.banner.half_vertical_right.magenta": "kipisi pi loje laso lon poka pini", "block.minecraft.banner.half_vertical_right.orange": "kipisi pi loje jelo lon poka pini", "block.minecraft.banner.half_vertical_right.pink": "kipisi pi loje walo lon poka pini", "block.minecraft.banner.half_vertical_right.purple": "kipisi pi laso loje lon poka pini", "block.minecraft.banner.half_vertical_right.red": "kipisi loje lon poka pini", "block.minecraft.banner.half_vertical_right.white": "kipisi walo lon poka pini", "block.minecraft.banner.half_vertical_right.yellow": "kipisi jelo lon poka pini", "block.minecraft.banner.mojang.black": "<PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "ijo pi laso telo", "block.minecraft.banner.mojang.brown": "ijo pi loje ma", "block.minecraft.banner.mojang.cyan": "ijo laso", "block.minecraft.banner.mojang.gray": "ijo pi pimeja walo", "block.minecraft.banner.mojang.green": "ijo pi laso kasi", "block.minecraft.banner.mojang.light_blue": "ijo pi laso sewi", "block.minecraft.banner.mojang.light_gray": "ijo pi walo pimeja", "block.minecraft.banner.mojang.lime": "ijo pi laso jelo", "block.minecraft.banner.mojang.magenta": "ijo pi loje laso", "block.minecraft.banner.mojang.orange": "ijo pi loje jelo", "block.minecraft.banner.mojang.pink": "ijo pi loje walo", "block.minecraft.banner.mojang.purple": "ijo pi laso loje", "block.minecraft.banner.mojang.red": "ijo loje", "block.minecraft.banner.mojang.white": "ijo walo", "block.minecraft.banner.mojang.yellow": "ijo jelo", "block.minecraft.banner.piglin.black": "nena soweli pimeja", "block.minecraft.banner.piglin.blue": "nena soweli pi laso telo", "block.minecraft.banner.piglin.brown": "nena soweli pi loje ma", "block.minecraft.banner.piglin.cyan": "nena soweli laso", "block.minecraft.banner.piglin.gray": "nena soweli pimeja walo", "block.minecraft.banner.piglin.green": "nena soweli pi laso kasi", "block.minecraft.banner.piglin.light_blue": "nena soweli pi laso sewi", "block.minecraft.banner.piglin.light_gray": "nena soweli walo pimeja", "block.minecraft.banner.piglin.lime": "nena soweli laso jelo", "block.minecraft.banner.piglin.magenta": "nena soweli loje laso", "block.minecraft.banner.piglin.orange": "nena soweli loje jelo", "block.minecraft.banner.piglin.pink": "nena soweli loje walo", "block.minecraft.banner.piglin.purple": "nena soweli laso loje", "block.minecraft.banner.piglin.red": "nena soweli loje", "block.minecraft.banner.piglin.white": "nena soweli walo", "block.minecraft.banner.piglin.yellow": "nena soweli jelo", "block.minecraft.banner.rhombus.black": "leko insa pimeja", "block.minecraft.banner.rhombus.blue": "leko insa pi laso telo", "block.minecraft.banner.rhombus.brown": "leko insa pi loje ma", "block.minecraft.banner.rhombus.cyan": "leko insa laso", "block.minecraft.banner.rhombus.gray": "leko insa pi pimeja walo", "block.minecraft.banner.rhombus.green": "leko insa pi laso kasi", "block.minecraft.banner.rhombus.light_blue": "leko insa pi laso sewi", "block.minecraft.banner.rhombus.light_gray": "leko insa pi walo pimeja", "block.minecraft.banner.rhombus.lime": "leko insa pi laso jelo", "block.minecraft.banner.rhombus.magenta": "leko insa pi loje laso", "block.minecraft.banner.rhombus.orange": "leko insa pi loje jelo", "block.minecraft.banner.rhombus.pink": "leko insa pi loje walo", "block.minecraft.banner.rhombus.purple": "leko insa pi laso loje", "block.minecraft.banner.rhombus.red": "leko insa loje", "block.minecraft.banner.rhombus.white": "leko insa walo", "block.minecraft.banner.rhombus.yellow": "leko insa jelo", "block.minecraft.banner.skull.black": "sitelen moli pimeja", "block.minecraft.banner.skull.blue": "sitelen moli pi laso telo", "block.minecraft.banner.skull.brown": "sitelen moli pi loje ma", "block.minecraft.banner.skull.cyan": "sitelen moli laso", "block.minecraft.banner.skull.gray": "sitelen moli pi pimeja walo", "block.minecraft.banner.skull.green": "sitelen moli pi laso kasi", "block.minecraft.banner.skull.light_blue": "sitelen moli pi laso sewi", "block.minecraft.banner.skull.light_gray": "sitelen moli pi walo pimeja", "block.minecraft.banner.skull.lime": "sitelen moli pi laso jelo", "block.minecraft.banner.skull.magenta": "sitelen moli pi loje laso", "block.minecraft.banner.skull.orange": "sitelen moli pi loje jelo", "block.minecraft.banner.skull.pink": "sitelen moli pi loje walo", "block.minecraft.banner.skull.purple": "sitelen moli pi laso loje", "block.minecraft.banner.skull.red": "sitelen moli loje", "block.minecraft.banner.skull.white": "sitelen moli walo", "block.minecraft.banner.skull.yellow": "<PERSON>len moli jelo", "block.minecraft.banner.small_stripes.black": "palisa mute pimeja", "block.minecraft.banner.small_stripes.blue": "palisa mute pi laso telo", "block.minecraft.banner.small_stripes.brown": "palisa mute pi loje ma", "block.minecraft.banner.small_stripes.cyan": "palisa mute laso", "block.minecraft.banner.small_stripes.gray": "palisa mute pi pimeja walo", "block.minecraft.banner.small_stripes.green": "palisa mute pi laso kasi", "block.minecraft.banner.small_stripes.light_blue": "palisa mute pi laso sewi", "block.minecraft.banner.small_stripes.light_gray": "palisa mute pi walo pimeja", "block.minecraft.banner.small_stripes.lime": "palisa mute pi laso jelo", "block.minecraft.banner.small_stripes.magenta": "palisa mute pi loje laso", "block.minecraft.banner.small_stripes.orange": "palisa mute pi loje jelo", "block.minecraft.banner.small_stripes.pink": "palisa mute pi loje walo", "block.minecraft.banner.small_stripes.purple": "palisa mute laso loje", "block.minecraft.banner.small_stripes.red": "palisa mute loje", "block.minecraft.banner.small_stripes.white": "palisa mute walo", "block.minecraft.banner.small_stripes.yellow": "palisa mute jelo", "block.minecraft.banner.square_bottom_left.black": "leko pimeja lon poka open anpa", "block.minecraft.banner.square_bottom_left.blue": "leko pi laso telo lon poka open anpa", "block.minecraft.banner.square_bottom_left.brown": "leko pi loje ma lon poka open anpa", "block.minecraft.banner.square_bottom_left.cyan": "leko laso lon poka open anpa", "block.minecraft.banner.square_bottom_left.gray": "leko pi pimeja walo lon poka open anpa", "block.minecraft.banner.square_bottom_left.green": "leko pi laso kasi lon poka open anpa", "block.minecraft.banner.square_bottom_left.light_blue": "leko pi laso sewi lon poka open anpa", "block.minecraft.banner.square_bottom_left.light_gray": "leko pi walo pimeja lon poka open anpa", "block.minecraft.banner.square_bottom_left.lime": "leko pi laso jelo lon poka open anpa", "block.minecraft.banner.square_bottom_left.magenta": "leko pi loje laso lon poka open anpa", "block.minecraft.banner.square_bottom_left.orange": "leko pi loje jelo lon poka open anpa", "block.minecraft.banner.square_bottom_left.pink": "leko pi loje walo lon poka open anpa", "block.minecraft.banner.square_bottom_left.purple": "leko pi laso loje lon poka open anpa", "block.minecraft.banner.square_bottom_left.red": "leko loje lon poka open anpa", "block.minecraft.banner.square_bottom_left.white": "leko walo lon poka open anpa", "block.minecraft.banner.square_bottom_left.yellow": "leko jelo lon poka open anpa", "block.minecraft.banner.square_bottom_right.black": "leko pimeja lon poka pini anpa", "block.minecraft.banner.square_bottom_right.blue": "leko pi laso telo lon poka pini anpa", "block.minecraft.banner.square_bottom_right.brown": "leko pi loje ma lon poka pini anpa", "block.minecraft.banner.square_bottom_right.cyan": "leko laso lon poka pini anpa", "block.minecraft.banner.square_bottom_right.gray": "leko pi pimeja walo lon poka pini anpa", "block.minecraft.banner.square_bottom_right.green": "leko pi laso kasi lon poka pini anpa", "block.minecraft.banner.square_bottom_right.light_blue": "leko pi laso sewi lon poka pini anpa", "block.minecraft.banner.square_bottom_right.light_gray": "leko pi walo pimeja lon poka pini anpa", "block.minecraft.banner.square_bottom_right.lime": "leko pi laso jelo lon poka pini anpa", "block.minecraft.banner.square_bottom_right.magenta": "leko pi loje laso lon poka pini anpa", "block.minecraft.banner.square_bottom_right.orange": "leko pi loje jelo lon poka pini anpa", "block.minecraft.banner.square_bottom_right.pink": "leko pi loje walo lon poka pini anpa", "block.minecraft.banner.square_bottom_right.purple": "leko pi laso loje lon poka pini anpa", "block.minecraft.banner.square_bottom_right.red": "leko loje lon poka pini anpa", "block.minecraft.banner.square_bottom_right.white": "leko walo lon poka pini anpa", "block.minecraft.banner.square_bottom_right.yellow": "leko jelo lon poka pini anpa", "block.minecraft.banner.square_top_left.black": "leko pimeja lon poka open sewi", "block.minecraft.banner.square_top_left.blue": "leko pi laso telo lon poka open sewi", "block.minecraft.banner.square_top_left.brown": "leko pi loje ma lon poka open sewi", "block.minecraft.banner.square_top_left.cyan": "leko laso lon poka open sewi", "block.minecraft.banner.square_top_left.gray": "leko pi pimeja walo lon poka open sewi", "block.minecraft.banner.square_top_left.green": "leko pi laso kasi lon poka open sewi", "block.minecraft.banner.square_top_left.light_blue": "leko pi laso sewi lon poka open sewi", "block.minecraft.banner.square_top_left.light_gray": "leko pi walo pimeja lon poka open sewi", "block.minecraft.banner.square_top_left.lime": "leko pi laso jelo lon poka open sewi", "block.minecraft.banner.square_top_left.magenta": "leko pi loje laso lon poka open sewi", "block.minecraft.banner.square_top_left.orange": "leko pi loje jelo lon poka open sewi", "block.minecraft.banner.square_top_left.pink": "leko pi loje walo lon poka open sewi", "block.minecraft.banner.square_top_left.purple": "leko pi laso loje lon poka open sewi", "block.minecraft.banner.square_top_left.red": "leko loje lon poka open sewi", "block.minecraft.banner.square_top_left.white": "leko walo lon poka open sewi", "block.minecraft.banner.square_top_left.yellow": "leko jelo lon poka open sewi", "block.minecraft.banner.square_top_right.black": "leko pimeja lon poka pini sewi", "block.minecraft.banner.square_top_right.blue": "leko pi laso telo lon poka pini sewi", "block.minecraft.banner.square_top_right.brown": "leko pi loje ma lon poka pini sewi", "block.minecraft.banner.square_top_right.cyan": "leko laso lon poka pini sewi", "block.minecraft.banner.square_top_right.gray": "leko pi pimeja walo lon poka pini sewi", "block.minecraft.banner.square_top_right.green": "leko pi laso kasi lon poka pini sewi", "block.minecraft.banner.square_top_right.light_blue": "leko pi laso sewi lon poka pini sewi", "block.minecraft.banner.square_top_right.light_gray": "leko pi walo pimeja lon poka pini sewi", "block.minecraft.banner.square_top_right.lime": "leko pi laso jelo lon poka pini sewi", "block.minecraft.banner.square_top_right.magenta": "leko pi loje laso lon poka pini sewi", "block.minecraft.banner.square_top_right.orange": "leko pi loje jelo lon poka pini sewi", "block.minecraft.banner.square_top_right.pink": "leko pi loje walo lon poka pini sewi", "block.minecraft.banner.square_top_right.purple": "leko pi laso loje lon poka pini sewi", "block.minecraft.banner.square_top_right.red": "leko loje lon poka pini sewi", "block.minecraft.banner.square_top_right.white": "leko walo lon poka pini sewi", "block.minecraft.banner.square_top_right.yellow": "leko pi jelo lon poka pini sewi", "block.minecraft.banner.straight_cross.black": "sitelen '+' pimeja", "block.minecraft.banner.straight_cross.blue": "sitelen '+' pi laso telo", "block.minecraft.banner.straight_cross.brown": "sitelen '+' pi loje ma", "block.minecraft.banner.straight_cross.cyan": "sitelen '+' laso", "block.minecraft.banner.straight_cross.gray": "sitelen '+' pi pimeja walo", "block.minecraft.banner.straight_cross.green": "sitelen '+' pi laso kasi", "block.minecraft.banner.straight_cross.light_blue": "sitelen '+' pi laso sewi", "block.minecraft.banner.straight_cross.light_gray": "sitelen '+' pi walo pimeja", "block.minecraft.banner.straight_cross.lime": "sitelen '+' pi laso jelo", "block.minecraft.banner.straight_cross.magenta": "sitelen '+' pi loje laso", "block.minecraft.banner.straight_cross.orange": "sitelen '+' pi loje jelo", "block.minecraft.banner.straight_cross.pink": "sitelen '+' pi loje walo", "block.minecraft.banner.straight_cross.purple": "sitelen '+' pi laso loje", "block.minecraft.banner.straight_cross.red": "sitelen '+' loje", "block.minecraft.banner.straight_cross.white": "sitelen '+' walo", "block.minecraft.banner.straight_cross.yellow": "sitelen '+' jelo", "block.minecraft.banner.stripe_bottom.black": "leko an<PERSON> pimeja", "block.minecraft.banner.stripe_bottom.blue": "leko anpa pi laso telo", "block.minecraft.banner.stripe_bottom.brown": "leko anpa pi loje ma", "block.minecraft.banner.stripe_bottom.cyan": "leko anpa laso", "block.minecraft.banner.stripe_bottom.gray": "leko anpa pi pimeja walo", "block.minecraft.banner.stripe_bottom.green": "leko anpa pi laso kasi", "block.minecraft.banner.stripe_bottom.light_blue": "leko anpa pi laso sewi", "block.minecraft.banner.stripe_bottom.light_gray": "leko anpa pi walo pimeja", "block.minecraft.banner.stripe_bottom.lime": "leko anpa pi laso jelo", "block.minecraft.banner.stripe_bottom.magenta": "leko anpa pi loje laso", "block.minecraft.banner.stripe_bottom.orange": "leko anpa pi loje jelo", "block.minecraft.banner.stripe_bottom.pink": "leko anpa pi loje walo", "block.minecraft.banner.stripe_bottom.purple": "leko anpa pi laso loje", "block.minecraft.banner.stripe_bottom.red": "leko anpa loje", "block.minecraft.banner.stripe_bottom.white": "leko anpa walo", "block.minecraft.banner.stripe_bottom.yellow": "leko an<PERSON> jelo", "block.minecraft.banner.stripe_center.black": "palisa pimeja", "block.minecraft.banner.stripe_center.blue": "palisa pi laso telo", "block.minecraft.banner.stripe_center.brown": "palisa pi loje ma", "block.minecraft.banner.stripe_center.cyan": "palisa laso", "block.minecraft.banner.stripe_center.gray": "palisa pi pimeja walo", "block.minecraft.banner.stripe_center.green": "palisa pi laso kasi", "block.minecraft.banner.stripe_center.light_blue": "palisa pi laso sewi", "block.minecraft.banner.stripe_center.light_gray": "palisa pi walo pimeja", "block.minecraft.banner.stripe_center.lime": "palisa pi laso jelo", "block.minecraft.banner.stripe_center.magenta": "palisa pi loje laso", "block.minecraft.banner.stripe_center.orange": "palisa pi loje jelo", "block.minecraft.banner.stripe_center.pink": "palisa pi loje walo", "block.minecraft.banner.stripe_center.purple": "palisa pi laso loje", "block.minecraft.banner.stripe_center.red": "palisa loje", "block.minecraft.banner.stripe_center.white": "palisa walo", "block.minecraft.banner.stripe_center.yellow": "palisa jelo", "block.minecraft.banner.stripe_downleft.black": "sitelen '/' pimeja", "block.minecraft.banner.stripe_downleft.blue": "sitelen '/' pi laso telo", "block.minecraft.banner.stripe_downleft.brown": "sitelen '/' pi loje ma", "block.minecraft.banner.stripe_downleft.cyan": "sitelen '/' laso", "block.minecraft.banner.stripe_downleft.gray": "sitelen '/' pi pimeja walo", "block.minecraft.banner.stripe_downleft.green": "sitelen '/' pi laso kasi", "block.minecraft.banner.stripe_downleft.light_blue": "sitelen '/' pi laso sewi", "block.minecraft.banner.stripe_downleft.light_gray": "sitelen '/' pi walo pimeja", "block.minecraft.banner.stripe_downleft.lime": "sitelen '/' pi laso jelo", "block.minecraft.banner.stripe_downleft.magenta": "sitelen '/' pi loje laso", "block.minecraft.banner.stripe_downleft.orange": "sitelen '/' pi loje jelo", "block.minecraft.banner.stripe_downleft.pink": "sitelen '/' pi loje walo", "block.minecraft.banner.stripe_downleft.purple": "sitelen '/' pi laso loje", "block.minecraft.banner.stripe_downleft.red": "sitelen '/' loje", "block.minecraft.banner.stripe_downleft.white": "sitelen '/' walo", "block.minecraft.banner.stripe_downleft.yellow": "sitelen '/' jelo", "block.minecraft.banner.stripe_downright.black": "sitelen '\\' pimeja", "block.minecraft.banner.stripe_downright.blue": "sitelen '\\' pi laso telo", "block.minecraft.banner.stripe_downright.brown": "sitelen '\\' pi loje ma", "block.minecraft.banner.stripe_downright.cyan": "sitelen '\\' laso", "block.minecraft.banner.stripe_downright.gray": "sitelen '\\' pi pimeja walo", "block.minecraft.banner.stripe_downright.green": "sitelen '\\' pi laso kasi", "block.minecraft.banner.stripe_downright.light_blue": "sitelen '\\' pi laso sewi", "block.minecraft.banner.stripe_downright.light_gray": "sitelen '\\' pi walo pimeja", "block.minecraft.banner.stripe_downright.lime": "sitelen '\\' pi laso jelo", "block.minecraft.banner.stripe_downright.magenta": "sitelen '\\' pi loje laso", "block.minecraft.banner.stripe_downright.orange": "sitelen '\\' pi loje jelo", "block.minecraft.banner.stripe_downright.pink": "sitelen '\\' pi loje walo", "block.minecraft.banner.stripe_downright.purple": "sitelen '\\' laso loje", "block.minecraft.banner.stripe_downright.red": "sitelen '\\' loje", "block.minecraft.banner.stripe_downright.white": "sitelen '\\' walo", "block.minecraft.banner.stripe_downright.yellow": "sitelen '\\' jelo", "block.minecraft.banner.stripe_left.black": "palisa pimeja lon poka open", "block.minecraft.banner.stripe_left.blue": "palisa pi laso telo lon poka open", "block.minecraft.banner.stripe_left.brown": "palisa pi loje ma lon poka open", "block.minecraft.banner.stripe_left.cyan": "palisa laso lon poka open", "block.minecraft.banner.stripe_left.gray": "palisa pi pimeja walo lon poka open", "block.minecraft.banner.stripe_left.green": "palisa pi laso kasi lon poka open", "block.minecraft.banner.stripe_left.light_blue": "palisa pi laso sewi lon poka open", "block.minecraft.banner.stripe_left.light_gray": "palisa pi walo pimeja lon poka open", "block.minecraft.banner.stripe_left.lime": "palisa pi laso jelo lon poka open", "block.minecraft.banner.stripe_left.magenta": "palisa pi loje laso lon poka open", "block.minecraft.banner.stripe_left.orange": "palisa pi loje jelo lon poka open", "block.minecraft.banner.stripe_left.pink": "palisa pi loje walo lon poka open", "block.minecraft.banner.stripe_left.purple": "palisa pi laso loje lon poka open", "block.minecraft.banner.stripe_left.red": "palisa loje lon poka open", "block.minecraft.banner.stripe_left.white": "palisa walo lon poka open", "block.minecraft.banner.stripe_left.yellow": "palisa jelo lon poka open", "block.minecraft.banner.stripe_middle.black": "linja supa pimeja", "block.minecraft.banner.stripe_middle.blue": "linja supa pi laso telo", "block.minecraft.banner.stripe_middle.brown": "linja supa pi loje ma", "block.minecraft.banner.stripe_middle.cyan": "linja supa laso", "block.minecraft.banner.stripe_middle.gray": "linja supa pi pimeja walo", "block.minecraft.banner.stripe_middle.green": "linja supa pi laso kasi", "block.minecraft.banner.stripe_middle.light_blue": "linja supa pi laso sewi", "block.minecraft.banner.stripe_middle.light_gray": "linja supa pi walo pimeja", "block.minecraft.banner.stripe_middle.lime": "linja supa pi laso jelo", "block.minecraft.banner.stripe_middle.magenta": "linja supa pi loje laso", "block.minecraft.banner.stripe_middle.orange": "linja supa pi loje jelo", "block.minecraft.banner.stripe_middle.pink": "linja supa pi loje walo", "block.minecraft.banner.stripe_middle.purple": "linja supa pi laso loje", "block.minecraft.banner.stripe_middle.red": "linja supa loje", "block.minecraft.banner.stripe_middle.white": "linja supa walo", "block.minecraft.banner.stripe_middle.yellow": "linja supa jelo", "block.minecraft.banner.stripe_right.black": "palisa pimeja lon poka pini", "block.minecraft.banner.stripe_right.blue": "palisa pi laso telo lon poka pini", "block.minecraft.banner.stripe_right.brown": "palisa pi loje ma lon poka pini", "block.minecraft.banner.stripe_right.cyan": "palisa laso lon poka pini", "block.minecraft.banner.stripe_right.gray": "palisa pi pimeja walo lon poka pini", "block.minecraft.banner.stripe_right.green": "palisa pi laso kasi lon poka pini", "block.minecraft.banner.stripe_right.light_blue": "palisa pi laso sewi lon poka pini", "block.minecraft.banner.stripe_right.light_gray": "palisa pi walo pimeja lon poka pini", "block.minecraft.banner.stripe_right.lime": "palisa pi laso jelo lon poka pini", "block.minecraft.banner.stripe_right.magenta": "palisa pi loje laso lon poka pini", "block.minecraft.banner.stripe_right.orange": "palisa pi loje jelo lon poka pini", "block.minecraft.banner.stripe_right.pink": "palisa pi loje walo lon poka pini", "block.minecraft.banner.stripe_right.purple": "palisa pi laso loje lon poka pini", "block.minecraft.banner.stripe_right.red": "palisa loje lon poka pini", "block.minecraft.banner.stripe_right.white": "palisa walo lon poka pini", "block.minecraft.banner.stripe_right.yellow": "palisa jelo lon poka pini", "block.minecraft.banner.stripe_top.black": "leko sewi pimeja", "block.minecraft.banner.stripe_top.blue": "leko sewi pi laso telo", "block.minecraft.banner.stripe_top.brown": "leko sewi pi loje ma", "block.minecraft.banner.stripe_top.cyan": "leko sewi laso", "block.minecraft.banner.stripe_top.gray": "leko sewi pi pimeja walo", "block.minecraft.banner.stripe_top.green": "leko sewi pi laso kasi", "block.minecraft.banner.stripe_top.light_blue": "leko sewi pi laso sewi", "block.minecraft.banner.stripe_top.light_gray": "leko sewi pi walo pimeja", "block.minecraft.banner.stripe_top.lime": "leko sewi pi laso jelo", "block.minecraft.banner.stripe_top.magenta": "leko sewi pi loje laso", "block.minecraft.banner.stripe_top.orange": "leko sewi pi loje jelo", "block.minecraft.banner.stripe_top.pink": "leko sewi pi loje walo", "block.minecraft.banner.stripe_top.purple": "leko sewi pi laso loje", "block.minecraft.banner.stripe_top.red": "leko sewi loje", "block.minecraft.banner.stripe_top.white": "leko sewi walo", "block.minecraft.banner.stripe_top.yellow": "leko sewi jelo", "block.minecraft.banner.triangle_bottom.black": "<PERSON>na p<PERSON>", "block.minecraft.banner.triangle_bottom.blue": "nena pi laso telo", "block.minecraft.banner.triangle_bottom.brown": "nena pi loje ma", "block.minecraft.banner.triangle_bottom.cyan": "nena laso", "block.minecraft.banner.triangle_bottom.gray": "nena pi pimeja walo", "block.minecraft.banner.triangle_bottom.green": "nena pi laso kasi", "block.minecraft.banner.triangle_bottom.light_blue": "nena pi laso sewi", "block.minecraft.banner.triangle_bottom.light_gray": "nena pi walo pimeja", "block.minecraft.banner.triangle_bottom.lime": "nena pi laso jelo", "block.minecraft.banner.triangle_bottom.magenta": "nena pi loje laso", "block.minecraft.banner.triangle_bottom.orange": "nena pi loje jelo", "block.minecraft.banner.triangle_bottom.pink": "nena pi loje walo", "block.minecraft.banner.triangle_bottom.purple": "nena pi laso loje", "block.minecraft.banner.triangle_bottom.red": "nena loje", "block.minecraft.banner.triangle_bottom.white": "nena walo", "block.minecraft.banner.triangle_bottom.yellow": "nena jelo", "block.minecraft.banner.triangle_top.black": "nena sewi p<PERSON>ja", "block.minecraft.banner.triangle_top.blue": "nena sewi pi laso telo", "block.minecraft.banner.triangle_top.brown": "nena sewi pi loje ma", "block.minecraft.banner.triangle_top.cyan": "nena sewi laso", "block.minecraft.banner.triangle_top.gray": "nena sewi pi pimeja walo", "block.minecraft.banner.triangle_top.green": "nena sewi pi laso kasi", "block.minecraft.banner.triangle_top.light_blue": "nena sewi pi laso sewi", "block.minecraft.banner.triangle_top.light_gray": "nena sewi pi walo pimeja", "block.minecraft.banner.triangle_top.lime": "nena sewi pi laso jelo", "block.minecraft.banner.triangle_top.magenta": "nena sewi pi loje laso", "block.minecraft.banner.triangle_top.orange": "nena sewi pi loje jelo", "block.minecraft.banner.triangle_top.pink": "nena sewi pi loje walo", "block.minecraft.banner.triangle_top.purple": "nena sewi pi laso loje", "block.minecraft.banner.triangle_top.red": "nena sewi loje", "block.minecraft.banner.triangle_top.white": "nena sewi walo", "block.minecraft.banner.triangle_top.yellow": "nena sewi jelo", "block.minecraft.banner.triangles_bottom.black": "nena mute anpa pimeja", "block.minecraft.banner.triangles_bottom.blue": "nena mute anpa pi laso telo", "block.minecraft.banner.triangles_bottom.brown": "nena mute anpa pi loje ma", "block.minecraft.banner.triangles_bottom.cyan": "nena mute anpa laso", "block.minecraft.banner.triangles_bottom.gray": "nena mute anpa pi pimeja walo", "block.minecraft.banner.triangles_bottom.green": "nena mute anpa pi laso kasi", "block.minecraft.banner.triangles_bottom.light_blue": "nena mute anpa pi laso sewi", "block.minecraft.banner.triangles_bottom.light_gray": "nena mute anpa pi walo pimeja", "block.minecraft.banner.triangles_bottom.lime": "nena mute anpa pi laso jelo", "block.minecraft.banner.triangles_bottom.magenta": "nena mute anpa pi loje laso", "block.minecraft.banner.triangles_bottom.orange": "nena mute anpa pi loje jelo", "block.minecraft.banner.triangles_bottom.pink": "nena mute anpa pi loje walo", "block.minecraft.banner.triangles_bottom.purple": "nena mute anpa laso loje", "block.minecraft.banner.triangles_bottom.red": "nena mute anpa loje", "block.minecraft.banner.triangles_bottom.white": "nena mute anpa walo", "block.minecraft.banner.triangles_bottom.yellow": "nena mute anpa jelo", "block.minecraft.banner.triangles_top.black": "nena mute sewi pimeja", "block.minecraft.banner.triangles_top.blue": "nena mute sewi pi laso telo", "block.minecraft.banner.triangles_top.brown": "nena mute sewi pi loje ma", "block.minecraft.banner.triangles_top.cyan": "nena mute sewi laso", "block.minecraft.banner.triangles_top.gray": "nena mute sewi pi pimeja walo", "block.minecraft.banner.triangles_top.green": "nena mute sewi pi laso kasi", "block.minecraft.banner.triangles_top.light_blue": "nena mute sewi pi laso sewi", "block.minecraft.banner.triangles_top.light_gray": "nena mute sewi pi walo pimeja", "block.minecraft.banner.triangles_top.lime": "nena mute sewi pi laso jelo", "block.minecraft.banner.triangles_top.magenta": "nena mute sewi pi loje laso", "block.minecraft.banner.triangles_top.orange": "nena mute sewi pi loje jelo", "block.minecraft.banner.triangles_top.pink": "nena mute sewi pi loje walo", "block.minecraft.banner.triangles_top.purple": "nena mute sewi laso loje", "block.minecraft.banner.triangles_top.red": "nena mute sewi loje", "block.minecraft.banner.triangles_top.white": "nena mute sewi walo", "block.minecraft.banner.triangles_top.yellow": "nena mute sewi jelo", "block.minecraft.barrel": "poki sike", "block.minecraft.barrier": "sinpin kon", "block.minecraft.basalt": "kiwen palisa pimeja", "block.minecraft.beacon": "ilo wawa", "block.minecraft.beacon.primary": "wawa nanpa wan", "block.minecraft.beacon.secondary": "wawa nanpa tu", "block.minecraft.bed.no_sleep": "sina ken lape lon tenpo pimeja anu tenpo pi linja suno", "block.minecraft.bed.not_safe": "sina ken ala lape tan ni: monsuta li lon poka", "block.minecraft.bed.obstructed": "sina ken ala kepeken supa ni tan ni: ijo li lon ona", "block.minecraft.bed.occupied": "jan li kepeken supa lape ni", "block.minecraft.bed.too_far_away": "sina ken ala lape tan ni: supa lape li lon poka ala", "block.minecraft.bedrock": "kiwen pi pakala ala", "block.minecraft.bee_nest": "tomo pi pipi suwi", "block.minecraft.beehive": "tomo pali pi pipi suwi", "block.minecraft.beetroots": "kili ma loje", "block.minecraft.bell": "ilo kalama jelo", "block.minecraft.big_dripleaf": "kasi supa", "block.minecraft.big_dripleaf_stem": "sijelo pi kasi supa", "block.minecraft.birch_button": "nena ilo pi kasi jelo", "block.minecraft.birch_door": "lupa pi kasi jelo", "block.minecraft.birch_fence": "sinpin palisa pi kasi jelo", "block.minecraft.birch_fence_gate": "sinpin tawa pi kasi jelo", "block.minecraft.birch_hanging_sign": "lipu sitelen sewi pi kasi jelo", "block.minecraft.birch_leaves": "lipu pi kasi jelo", "block.minecraft.birch_log": "sijelo pi kasi jelo", "block.minecraft.birch_planks": "kiwen kipisi pi kasi jelo", "block.minecraft.birch_pressure_plate": "supa noka pi kasi jelo", "block.minecraft.birch_sapling": "kasi jelo sin", "block.minecraft.birch_sign": "lipu sitelen pi kasi jelo", "block.minecraft.birch_slab": "supa pi kasi jelo", "block.minecraft.birch_stairs": "leko sewi pi kasi jelo", "block.minecraft.birch_trapdoor": "lupa anpa pi kasi jelo", "block.minecraft.birch_wall_hanging_sign": "lipu sitelen sewi pi kasi jelo lon sinpin", "block.minecraft.birch_wall_sign": "lipu sitelen pi kasi jelo lon sinpin", "block.minecraft.birch_wood": "kiwen pi kasi jelo", "block.minecraft.black_banner": "len <PERSON>len pimeja", "block.minecraft.black_bed": "supa lape pimeja", "block.minecraft.black_candle": "palisa suno lili pimeja", "block.minecraft.black_candle_cake": "palisa suno lili pimeja lon pan suwi", "block.minecraft.black_carpet": "len anpa pimeja", "block.minecraft.black_concrete": "kiwen kule pimeja", "block.minecraft.black_concrete_powder": "ko kule pimeja", "block.minecraft.black_glazed_terracotta": "kiwen namako p<PERSON>ja", "block.minecraft.black_shulker_box": "poki pimeja pi mon<PERSON><PERSON>", "block.minecraft.black_stained_glass": "kiwen lukin pimeja", "block.minecraft.black_stained_glass_pane": "sinpin lukin pimeja", "block.minecraft.black_terracotta": "ma kiwen pimeja", "block.minecraft.black_wool": "len pimeja", "block.minecraft.blackstone": "kiwen pimeja", "block.minecraft.blackstone_slab": "supa kiwen pimeja", "block.minecraft.blackstone_stairs": "leko sewi pi kiwen pimeja", "block.minecraft.blackstone_wall": "sinpin kiwen pimeja", "block.minecraft.blast_furnace": "poki pi seli kiwen", "block.minecraft.blue_banner": "len sitelen pi laso telo", "block.minecraft.blue_bed": "supa lape pi laso telo", "block.minecraft.blue_candle": "palisa suno lili pi laso telo", "block.minecraft.blue_candle_cake": "palisa suno lili pi laso telo lon pan suwi", "block.minecraft.blue_carpet": "len anpa pi laso telo", "block.minecraft.blue_concrete": "kiwen kule pi laso telo", "block.minecraft.blue_concrete_powder": "ko kule pi laso telo", "block.minecraft.blue_glazed_terracotta": "kiwen namako pi laso telo", "block.minecraft.blue_ice": "kiwen telo laso", "block.minecraft.blue_orchid": "kasi pi laso sewi", "block.minecraft.blue_shulker_box": "poki pi laso telo pi monsu<PERSON>", "block.minecraft.blue_stained_glass": "kiwen lukin pi laso telo", "block.minecraft.blue_stained_glass_pane": "sinpin lukin pi laso telo", "block.minecraft.blue_terracotta": "ma kiwen pi laso telo", "block.minecraft.blue_wool": "len pi laso telo", "block.minecraft.bone_block": "kiwen sijelo suli", "block.minecraft.bookshelf": "leko lipu", "block.minecraft.brain_coral": "kiwen kala pi loje walo", "block.minecraft.brain_coral_block": "kiwen kala suli pi loje walo", "block.minecraft.brain_coral_fan": "lipu pi kiwen kala pi loje walo", "block.minecraft.brain_coral_wall_fan": "lipu sinpin pi kiwen kala pi loje walo", "block.minecraft.brewing_stand": "ilo pi telo wawa", "block.minecraft.brick_slab": "supa pi kiwen leko loje", "block.minecraft.brick_stairs": "leko sewi pi kiwen leko loje", "block.minecraft.brick_wall": "sinpin pi kiwen leko loje", "block.minecraft.bricks": "kiwen leko loje", "block.minecraft.brown_banner": "len sitelen pi loje ma", "block.minecraft.brown_bed": "supa lape pi loje ma", "block.minecraft.brown_candle": "palisa suno lili pi loje ma", "block.minecraft.brown_candle_cake": "palisa suno lili pi loje ma lon pan suwi", "block.minecraft.brown_carpet": "len anpa pi loje ma", "block.minecraft.brown_concrete": "kiwen kule pi loje ma", "block.minecraft.brown_concrete_powder": "ko kule pi loje ma", "block.minecraft.brown_glazed_terracotta": "kiwen namako pi loje ma", "block.minecraft.brown_mushroom": "soko pi loje ma", "block.minecraft.brown_mushroom_block": "soko suli pi loje ma", "block.minecraft.brown_shulker_box": "poki pi loje ma pi mon<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "kiwen lukin pi loje ma", "block.minecraft.brown_stained_glass_pane": "sinpin lukin pi loje ma", "block.minecraft.brown_terracotta": "ma kiwen pi loje ma", "block.minecraft.brown_wool": "len pi loje ma", "block.minecraft.bubble_column": "sike kon", "block.minecraft.bubble_coral": "kiwen kala pi laso loje", "block.minecraft.bubble_coral_block": "kiwen kala suli pi laso loje", "block.minecraft.bubble_coral_fan": "lipu pi kiwen kala pi laso loje", "block.minecraft.bubble_coral_wall_fan": "lipu sinpin pi kiwen kala pi laso loje", "block.minecraft.budding_amethyst": "kiwen kalama mama", "block.minecraft.bush": "kasi anpa lili", "block.minecraft.cactus": "kasi kipisi", "block.minecraft.cactus_flower": "kasi suwi pi kasi kipisi", "block.minecraft.cake": "pan suwi", "block.minecraft.calcite": "kiwen walo ko", "block.minecraft.calibrated_sculk_sensor": "soko Sculk kute pi sona kalama", "block.minecraft.campfire": "seli palisa", "block.minecraft.candle": "palisa suno lili", "block.minecraft.candle_cake": "palisa suno lili lon pan suwi", "block.minecraft.carrots": "kili palisa", "block.minecraft.cartography_table": "supa pi sitelen ma", "block.minecraft.carved_pumpkin": "kili suli monsuta", "block.minecraft.cauldron": "poki suli", "block.minecraft.cave_air": "kon anpa", "block.minecraft.cave_vines": "kasi linja lupa", "block.minecraft.cave_vines_plant": "kasi linja lupa", "block.minecraft.chain": "linja kiwen", "block.minecraft.chain_command_block": "ilo pi toki wawa lon linja", "block.minecraft.cherry_button": "nena ilo pi kasi suwi", "block.minecraft.cherry_door": "lupa pi kasi suwi", "block.minecraft.cherry_fence": "sinpin palisa pi kasi suwi", "block.minecraft.cherry_fence_gate": "sinpin tawa pi kasi suwi", "block.minecraft.cherry_hanging_sign": "lipu sitelen sewi pi kasi suwi", "block.minecraft.cherry_leaves": "lipu pi kasi suwi", "block.minecraft.cherry_log": "sijelo pi kasi suwi", "block.minecraft.cherry_planks": "kiwen kipisi pi kasi suwi", "block.minecraft.cherry_pressure_plate": "supa noka pi kasi suwi", "block.minecraft.cherry_sapling": "kasi suwi sin", "block.minecraft.cherry_sign": "lipu sitelen pi kasi suwi", "block.minecraft.cherry_slab": "supa pi kasi suwi", "block.minecraft.cherry_stairs": "leko sewi pi kasi suwi", "block.minecraft.cherry_trapdoor": "lupa anpa pi kasi suwi", "block.minecraft.cherry_wall_hanging_sign": "lipu sitelen sewi pi kasi suwi", "block.minecraft.cherry_wall_sign": "lipu sitelen pi kasi suwi lon sinpin", "block.minecraft.cherry_wood": "kiwen pi kasi suwi", "block.minecraft.chest": "poki", "block.minecraft.chipped_anvil": "supa pona pi pakala lili", "block.minecraft.chiseled_bookshelf": "poki lipu", "block.minecraft.chiseled_copper": "kiwen ante sitelen", "block.minecraft.chiseled_deepslate": "kiwen anpa sitelen", "block.minecraft.chiseled_nether_bricks": "kiwen leko sitelen pi ma Nether", "block.minecraft.chiseled_polished_blackstone": "kiwen pimeja sitelen", "block.minecraft.chiseled_quartz_block": "kiwen walo sitelen", "block.minecraft.chiseled_red_sandstone": "kiwen ko loje sitelen", "block.minecraft.chiseled_resin_bricks": "kiwen leko sitelen pi kiwen ko loje", "block.minecraft.chiseled_sandstone": "kiwen ko sitelen", "block.minecraft.chiseled_stone_bricks": "kiwen leko sitelen", "block.minecraft.chiseled_tuff": "ko seli kiwen sitelen", "block.minecraft.chiseled_tuff_bricks": "ko seli kiwen leko sitelen", "block.minecraft.chorus_flower": "lawa pi kasi nasa", "block.minecraft.chorus_plant": "kasi nasa", "block.minecraft.clay": "ma ko", "block.minecraft.closed_eyeblossom": "kasi oko pini", "block.minecraft.coal_block": "leko pi ko pimeja", "block.minecraft.coal_ore": "ko pimeja pi insa kiwen", "block.minecraft.coarse_dirt": "ma pi kiwen lili", "block.minecraft.cobbled_deepslate": "kiwen anpa nena", "block.minecraft.cobbled_deepslate_slab": "supa pi kiwen anpa nena", "block.minecraft.cobbled_deepslate_stairs": "leko sewi pi kiwen anpa nena", "block.minecraft.cobbled_deepslate_wall": "sinpin pi kiwen anpa nena", "block.minecraft.cobblestone": "kiwen nena", "block.minecraft.cobblestone_slab": "supa pi kiwen nena", "block.minecraft.cobblestone_stairs": "leko sewi pi kiwen nena", "block.minecraft.cobblestone_wall": "sinpin pi kiwen nena", "block.minecraft.cobweb": "linja pipi", "block.minecraft.cocoa": "kili pi loje ma", "block.minecraft.command_block": "ilo pi toki wawa", "block.minecraft.comparator": "ilo pi ante wawa pi ko Redstone", "block.minecraft.composter": "poki jaki", "block.minecraft.conduit": "ilo wawa telo", "block.minecraft.copper_block": "leko pi kiwen ante", "block.minecraft.copper_bulb": "suno ante", "block.minecraft.copper_door": "lupa pi kiwen ante", "block.minecraft.copper_grate": "kiwen ante lukin", "block.minecraft.copper_ore": "kiwen ante pi insa kiwen", "block.minecraft.copper_trapdoor": "lupa anpa pi kiwen ante", "block.minecraft.cornflower": "kasi pi laso telo", "block.minecraft.cracked_deepslate_bricks": "kiwen anpa leko pakala", "block.minecraft.cracked_deepslate_tiles": "kiwen anpa pi leko lipu pakala", "block.minecraft.cracked_nether_bricks": "kiwen leko pakala pi ma <PERSON>her", "block.minecraft.cracked_polished_blackstone_bricks": "kiwen pimeja leko pakala", "block.minecraft.cracked_stone_bricks": "kiwen leko pakala", "block.minecraft.crafter": "ilo pali", "block.minecraft.crafting_table": "supa pali", "block.minecraft.creaking_heart": "lawa pi monsuta kasi", "block.minecraft.creeper_head": "lawa pi monsuta <PERSON>reeper", "block.minecraft.creeper_wall_head": "lawa pi monsuta Creeper lon sinpin", "block.minecraft.crimson_button": "nena ilo pi soko loje", "block.minecraft.crimson_door": "lupa pi soko loje", "block.minecraft.crimson_fence": "sinpin palisa pi soko loje", "block.minecraft.crimson_fence_gate": "sinpin tawa pi soko loje", "block.minecraft.crimson_fungus": "soko loje seli", "block.minecraft.crimson_hanging_sign": "lipu sitelen sewi pi soko loje", "block.minecraft.crimson_hyphae": "sijelo selo pi soko loje", "block.minecraft.crimson_nylium": "kiwen selo pi soko loje", "block.minecraft.crimson_planks": "kiwen kipisi pi soko loje", "block.minecraft.crimson_pressure_plate": "supa noka pi soko loje", "block.minecraft.crimson_roots": "noka kasi loje", "block.minecraft.crimson_sign": "lipu sitelen pi soko loje", "block.minecraft.crimson_slab": "supa pi soko loje", "block.minecraft.crimson_stairs": "leko sewi pi soko loje", "block.minecraft.crimson_stem": "sijelo pi soko loje", "block.minecraft.crimson_trapdoor": "lupa anpa pi soko loje", "block.minecraft.crimson_wall_hanging_sign": "lipu sitelen sewi pi soko loje lon sinpin", "block.minecraft.crimson_wall_sign": "lipu sitelen pi soko loje lon sinpin", "block.minecraft.crying_obsidian": "kiwen wawa pakala", "block.minecraft.cut_copper": "kiwen ante kipisi", "block.minecraft.cut_copper_slab": "supa pi kiwen ante", "block.minecraft.cut_copper_stairs": "leko sewi pi kiwen ante", "block.minecraft.cut_red_sandstone": "kiwen ko loje kipisi", "block.minecraft.cut_red_sandstone_slab": "supa pi kiwen ko loje kipisi", "block.minecraft.cut_sandstone": "kiwen ko kipisi", "block.minecraft.cut_sandstone_slab": "supa pi kiwen ko kipisi", "block.minecraft.cyan_banner": "len sitelen laso", "block.minecraft.cyan_bed": "supa lape laso", "block.minecraft.cyan_candle": "palisa suno lili laso", "block.minecraft.cyan_candle_cake": "palisa suno lili laso lon pan suwi", "block.minecraft.cyan_carpet": "len anpa laso", "block.minecraft.cyan_concrete": "kiwen kule laso", "block.minecraft.cyan_concrete_powder": "ko kule laso", "block.minecraft.cyan_glazed_terracotta": "kiwen namako laso", "block.minecraft.cyan_shulker_box": "poki laso pi monsu<PERSON>", "block.minecraft.cyan_stained_glass": "kiwen lukin laso", "block.minecraft.cyan_stained_glass_pane": "sinpin lukin laso", "block.minecraft.cyan_terracotta": "ma kiwen laso", "block.minecraft.cyan_wool": "len laso", "block.minecraft.damaged_anvil": "supa pona pakala", "block.minecraft.dandelion": "kasi jelo", "block.minecraft.dark_oak_button": "nena ilo pi kasi pimeja", "block.minecraft.dark_oak_door": "lupa pi kasi pimeja", "block.minecraft.dark_oak_fence": "sinpin palisa pi kasi pimeja", "block.minecraft.dark_oak_fence_gate": "sinpin tawa pi kasi pimeja", "block.minecraft.dark_oak_hanging_sign": "lipu sitelen sewi pi kasi pimeja", "block.minecraft.dark_oak_leaves": "lipu pi kasi pimeja", "block.minecraft.dark_oak_log": "sijelo pi kasi pimeja", "block.minecraft.dark_oak_planks": "kiwen kipisi pi kasi pimeja", "block.minecraft.dark_oak_pressure_plate": "supa noka pi kasi pimeja", "block.minecraft.dark_oak_sapling": "kasi pimeja sin", "block.minecraft.dark_oak_sign": "lipu sitelen pi kasi pimeja", "block.minecraft.dark_oak_slab": "supa pi kasi pimeja", "block.minecraft.dark_oak_stairs": "leko sewi pi kasi pimeja", "block.minecraft.dark_oak_trapdoor": "lupa anpa pi kasi pimeja", "block.minecraft.dark_oak_wall_hanging_sign": "lipu sitelen sewi pi kasi pimeja lon sinpin", "block.minecraft.dark_oak_wall_sign": "lipu sitelen pi kasi pimeja lon sinpin", "block.minecraft.dark_oak_wood": "kiwen pi kasi pimeja", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "supa pi kiwen Prismari<PERSON> p<PERSON>ja", "block.minecraft.dark_prismarine_stairs": "leko sewi pi kiwen Prismarine p<PERSON>", "block.minecraft.daylight_detector": "ilo lukin pi suno sewi", "block.minecraft.dead_brain_coral": "kiwen kala moli pi loje walo", "block.minecraft.dead_brain_coral_block": "kiwen kala suli moli pi loje walo", "block.minecraft.dead_brain_coral_fan": "lipu pi kiwen kala moli pi loje walo", "block.minecraft.dead_brain_coral_wall_fan": "lipu sinpin pi kiwen kala moli pi loje walo", "block.minecraft.dead_bubble_coral": "kiwen kala moli pi laso loje", "block.minecraft.dead_bubble_coral_block": "kiwen kala suli moli pi laso loje", "block.minecraft.dead_bubble_coral_fan": "lipu pi kiwen kala moli pi laso loje", "block.minecraft.dead_bubble_coral_wall_fan": "lipu sinpin pi kiwen kala moli pi laso loje", "block.minecraft.dead_bush": "kasi moli", "block.minecraft.dead_fire_coral": "kiwen kala loje moli", "block.minecraft.dead_fire_coral_block": "kiwen kala loje suli moli", "block.minecraft.dead_fire_coral_fan": "lipu pi kiwen kala loje moli", "block.minecraft.dead_fire_coral_wall_fan": "lipu sinpin pi kiwen kala loje moli", "block.minecraft.dead_horn_coral": "kiwen kala jelo moli", "block.minecraft.dead_horn_coral_block": "kiwen kala jelo suli moli", "block.minecraft.dead_horn_coral_fan": "lipu pi kiwen kala jelo moli", "block.minecraft.dead_horn_coral_wall_fan": "lipu sinpin pi kiwen kala jelo moli", "block.minecraft.dead_tube_coral": "kiwen kala laso moli", "block.minecraft.dead_tube_coral_block": "kiwen kala laso suli moli", "block.minecraft.dead_tube_coral_fan": "lipu pi kiwen kala laso moli", "block.minecraft.dead_tube_coral_wall_fan": "lipu sinpin pi kiwen kala laso moli", "block.minecraft.decorated_pot": "poki kiwen sitelen", "block.minecraft.deepslate": "kiwen anpa", "block.minecraft.deepslate_brick_slab": "supa pi kiwen anpa leko", "block.minecraft.deepslate_brick_stairs": "leko sewi pi kiwen anpa leko", "block.minecraft.deepslate_brick_wall": "sinpin pi kiwen anpa leko", "block.minecraft.deepslate_bricks": "kiwen anpa leko", "block.minecraft.deepslate_coal_ore": "ko pimeja pi kiwen anpa", "block.minecraft.deepslate_copper_ore": "kiwen ante pi kiwen anpa", "block.minecraft.deepslate_diamond_ore": "kiwen laso pi kiwen anpa", "block.minecraft.deepslate_emerald_ore": "kiwen esun pi kiwen anpa", "block.minecraft.deepslate_gold_ore": "kiwen jelo pi kiwen anpa", "block.minecraft.deepslate_iron_ore": "kiwen walo pi kiwen anpa", "block.minecraft.deepslate_lapis_ore": "kiwen pi laso telo pi kiwen anpa", "block.minecraft.deepslate_redstone_ore": "ko Redstone pi kiwen anpa", "block.minecraft.deepslate_tile_slab": "supa pi kiwen anpa pi leko lipu", "block.minecraft.deepslate_tile_stairs": "leko sewi pi kiwen anpa pi leko lipu", "block.minecraft.deepslate_tile_wall": "sinpin pi kiwen anpa pi leko lipu", "block.minecraft.deepslate_tiles": "kiwen anpa pi leko lipu", "block.minecraft.detector_rail": "nasin palisa lukin", "block.minecraft.diamond_block": "leko pi kiwen laso", "block.minecraft.diamond_ore": "kiwen laso pi insa kiwen", "block.minecraft.diorite": "kiwen walo pimeja", "block.minecraft.diorite_slab": "supa pi kiwen walo pimeja", "block.minecraft.diorite_stairs": "leko sewi pi kiwen walo pimeja", "block.minecraft.diorite_wall": "sinpin pi kiwen walo pimeja", "block.minecraft.dirt": "ma", "block.minecraft.dirt_path": "ma nasin", "block.minecraft.dispenser": "poki pi pana wawa", "block.minecraft.dragon_egg": "sike mama akesi pi ma <PERSON>", "block.minecraft.dragon_head": "lawa pi akesi pini", "block.minecraft.dragon_wall_head": "lawa pi akesi End lon sinpin", "block.minecraft.dried_ghast": "ki<PERSON> G<PERSON> pi telo ala", "block.minecraft.dried_kelp_block": "kulupu pi kasi telo", "block.minecraft.dripstone_block": "kiwen palisa", "block.minecraft.dropper": "poki pana", "block.minecraft.emerald_block": "leko pi kiwen esun", "block.minecraft.emerald_ore": "kiwen esun pi insa kiwen", "block.minecraft.enchanting_table": "supa wawa", "block.minecraft.end_gateway": "lupa weka pi ma End", "block.minecraft.end_portal": "lupa pi ma End", "block.minecraft.end_portal_frame": "selo lupa pi ma End", "block.minecraft.end_rod": "palisa pi ma End", "block.minecraft.end_stone": "kiwen pi ma End", "block.minecraft.end_stone_brick_slab": "supa kiwen pi ma End", "block.minecraft.end_stone_brick_stairs": "leko sewi pi ma <PERSON>", "block.minecraft.end_stone_brick_wall": "sinpin kiwen pi ma <PERSON>", "block.minecraft.end_stone_bricks": "kiwen leko pi ma <PERSON>", "block.minecraft.ender_chest": "poki pi ma End", "block.minecraft.exposed_chiseled_copper": "kiwen ante sitelen pi loje ma", "block.minecraft.exposed_copper": "kiwen ante pi loje ma", "block.minecraft.exposed_copper_bulb": "suno ante pi loje ma", "block.minecraft.exposed_copper_door": "lupa ante pi loje ma", "block.minecraft.exposed_copper_grate": "kiwen ante lukin pi loje ma", "block.minecraft.exposed_copper_trapdoor": "lupa anpa ante pi loje ma", "block.minecraft.exposed_cut_copper": "kiwen ante kipisi pi loje ma", "block.minecraft.exposed_cut_copper_slab": "supa pi kiwen ante pi loje ma", "block.minecraft.exposed_cut_copper_stairs": "leko sewi pi kiwen ante pi loje ma", "block.minecraft.farmland": "ma mama", "block.minecraft.fern": "kasi pi lipu waso", "block.minecraft.fire": "seli", "block.minecraft.fire_coral": "kiwen kala loje", "block.minecraft.fire_coral_block": "kiwen kala loje suli", "block.minecraft.fire_coral_fan": "lipu pi kiwen kala loje", "block.minecraft.fire_coral_wall_fan": "lipu sinpin pi kiwen kala loje", "block.minecraft.firefly_bush": "kasi anpa lili pi pipi suno", "block.minecraft.fletching_table": "supa pi palisa alasa", "block.minecraft.flower_pot": "poki kasi", "block.minecraft.flowering_azalea": "kasi kiwen lupa kule", "block.minecraft.flowering_azalea_leaves": "lipu kule pi kasi kiwen lupa", "block.minecraft.frogspawn": "sike mama pi akesi sewi lili", "block.minecraft.frosted_ice": "kiwen telo weka", "block.minecraft.furnace": "poki seli", "block.minecraft.gilded_blackstone": "kiwen pimeja mani", "block.minecraft.glass": "kiwen lukin", "block.minecraft.glass_pane": "sinpin lukin", "block.minecraft.glow_lichen": "kasi len suno", "block.minecraft.glowstone": "kiwen suno", "block.minecraft.gold_block": "leko pi kiwen jelo", "block.minecraft.gold_ore": "kiwen jelo pi insa kiwen", "block.minecraft.granite": "kiwen loje", "block.minecraft.granite_slab": "supa pi kiwen loje", "block.minecraft.granite_stairs": "leko sewi pi kiwen loje", "block.minecraft.granite_wall": "sinpin pi kiwen loje", "block.minecraft.grass": "kasi anpa", "block.minecraft.grass_block": "ma kasi", "block.minecraft.gravel": "kiwen lili <PERSON>", "block.minecraft.gray_banner": "len sitelen pi pimeja walo", "block.minecraft.gray_bed": "supa lape pi pimeja walo", "block.minecraft.gray_candle": "palisa suno lili pi pimeja walo", "block.minecraft.gray_candle_cake": "palisa suno lili pi pimeja walo lon pan suwi", "block.minecraft.gray_carpet": "len anpa pi pimeja walo", "block.minecraft.gray_concrete": "kiwen kule pi pimeja walo", "block.minecraft.gray_concrete_powder": "ko kule pi pimeja walo", "block.minecraft.gray_glazed_terracotta": "kiwen namako pi pimeja walo", "block.minecraft.gray_shulker_box": "poki pi pimeja walo pi mon<PERSON><PERSON>", "block.minecraft.gray_stained_glass": "kiwen lukin pi pimeja walo", "block.minecraft.gray_stained_glass_pane": "sinpin lukin pi pimeja walo", "block.minecraft.gray_terracotta": "ma kiwen pi pimeja walo", "block.minecraft.gray_wool": "len pi pimeja walo", "block.minecraft.green_banner": "len sitelen pi laso kasi", "block.minecraft.green_bed": "supa lape pi laso kasi", "block.minecraft.green_candle": "palisa suno lili pi laso kasi", "block.minecraft.green_candle_cake": "palisa suno lili pi laso kasi lon pan suwi", "block.minecraft.green_carpet": "len anpa pi laso kasi", "block.minecraft.green_concrete": "kiwen kule pi laso kasi", "block.minecraft.green_concrete_powder": "ko kule pi laso kasi", "block.minecraft.green_glazed_terracotta": "kiwen namako pi laso kasi", "block.minecraft.green_shulker_box": "poki pi laso kasi pi monsu<PERSON>", "block.minecraft.green_stained_glass": "kiwen lukin pi laso kasi", "block.minecraft.green_stained_glass_pane": "sinpin lukin pi laso kasi", "block.minecraft.green_terracotta": "ma kiwen pi laso kasi", "block.minecraft.green_wool": "len pi laso kasi", "block.minecraft.grindstone": "sike pi pona ilo", "block.minecraft.hanging_roots": "noka kasi", "block.minecraft.hay_block": "kulupu pan", "block.minecraft.heavy_core": "kiwen leko wawa", "block.minecraft.heavy_weighted_pressure_plate": "supa noka pi kiwen walo", "block.minecraft.honey_block": "leko pi suwi pipi", "block.minecraft.honeycomb_block": "lipu suwi suli", "block.minecraft.hopper": "poki pi tawa ijo", "block.minecraft.horn_coral": "kiwen kala jelo", "block.minecraft.horn_coral_block": "kiwen kala jelo suli", "block.minecraft.horn_coral_fan": "lipu pi kiwen kala jelo", "block.minecraft.horn_coral_wall_fan": "lipu sinpin pi kiwen kala jelo", "block.minecraft.ice": "kiwen telo", "block.minecraft.infested_chiseled_stone_bricks": "kiwen leko sitelen pipi", "block.minecraft.infested_cobblestone": "kiwen nena pipi", "block.minecraft.infested_cracked_stone_bricks": "kiwen leko pakala pipi", "block.minecraft.infested_deepslate": "kiwen anpa pipi", "block.minecraft.infested_mossy_stone_bricks": "kiwen leko kasi pipi", "block.minecraft.infested_stone": "kiwen pipi", "block.minecraft.infested_stone_bricks": "kiwen leko pipi", "block.minecraft.iron_bars": "sinpin palisa pi kiwen walo", "block.minecraft.iron_block": "leko pi kiwen walo", "block.minecraft.iron_door": "lupa pi kiwen walo", "block.minecraft.iron_ore": "kiwen walo pi insa kiwen", "block.minecraft.iron_trapdoor": "lupa anpa pi kiwen walo", "block.minecraft.jack_o_lantern": "kili suli suno", "block.minecraft.jigsaw": "leko pi ijo poka", "block.minecraft.jukebox": "ilo pi kalama musi", "block.minecraft.jungle_button": "nena ilo pi kasi suli", "block.minecraft.jungle_door": "lupa pi kasi suli", "block.minecraft.jungle_fence": "sinpin palisa pi kasi suli", "block.minecraft.jungle_fence_gate": "sinpin tawa pi kasi suli", "block.minecraft.jungle_hanging_sign": "lipu sitelen sewi pi kasi suli", "block.minecraft.jungle_leaves": "lipu pi kasi suli", "block.minecraft.jungle_log": "sijelo pi kasi suli", "block.minecraft.jungle_planks": "kiwen kipisi pi kasi suli", "block.minecraft.jungle_pressure_plate": "supa noka pi kasi suli", "block.minecraft.jungle_sapling": "kasi suli sin", "block.minecraft.jungle_sign": "lipu sitelen pi kasi suli", "block.minecraft.jungle_slab": "supa pi kasi suli", "block.minecraft.jungle_stairs": "leko sewi pi kasi suli", "block.minecraft.jungle_trapdoor": "lupa anpa pi kasi suli", "block.minecraft.jungle_wall_hanging_sign": "lipu sitelen sewi pi kasi suli lon sinpin", "block.minecraft.jungle_wall_sign": "lipu sitelen pi kasi suli lon sinpin", "block.minecraft.jungle_wood": "kiwen pi kasi suli", "block.minecraft.kelp": "kasi linja telo", "block.minecraft.kelp_plant": "kasi linja telo", "block.minecraft.ladder": "nasin palisa sewi", "block.minecraft.lantern": "poki suno", "block.minecraft.lapis_block": "kiwen suli pi laso telo", "block.minecraft.lapis_ore": "kiwen pi laso telo pi insa kiwen", "block.minecraft.large_amethyst_bud": "kiwen kalama kama suli", "block.minecraft.large_fern": "kasi suli pi lipu waso", "block.minecraft.lava": "telo seli", "block.minecraft.lava_cauldron": "telo seli lon poki telo suli", "block.minecraft.leaf_litter": "kulupu anpa pi lipu kasi", "block.minecraft.lectern": "supa lipu", "block.minecraft.lever": "palisa ilo", "block.minecraft.light": "suno", "block.minecraft.light_blue_banner": "len sitelen pi laso sewi", "block.minecraft.light_blue_bed": "supa lape pi laso sewi", "block.minecraft.light_blue_candle": "palisa suno lili pi laso sewi", "block.minecraft.light_blue_candle_cake": "palisa suno lili pi laso sewi lon pan suwi", "block.minecraft.light_blue_carpet": "len anpa pi laso sewi", "block.minecraft.light_blue_concrete": "kiwen kule pi laso sewi", "block.minecraft.light_blue_concrete_powder": "ko kule pi laso sewi", "block.minecraft.light_blue_glazed_terracotta": "kiwen namako pi laso sewi", "block.minecraft.light_blue_shulker_box": "poki pi laso sewi pi mon<PERSON><PERSON>", "block.minecraft.light_blue_stained_glass": "kiwen lukin pi laso sewi", "block.minecraft.light_blue_stained_glass_pane": "sinpin lukin pi laso sewi", "block.minecraft.light_blue_terracotta": "ma kiwen pi laso sewi", "block.minecraft.light_blue_wool": "len pi laso sewi", "block.minecraft.light_gray_banner": "len sitelen pi walo pimeja", "block.minecraft.light_gray_bed": "supa lape pi walo pimeja", "block.minecraft.light_gray_candle": "palisa suno lili pi walo pimeja", "block.minecraft.light_gray_candle_cake": "palisa suno lili pi walo pimeja lon pan suwi", "block.minecraft.light_gray_carpet": "len anpa pi walo pimeja", "block.minecraft.light_gray_concrete": "kiwen kule pi walo pimeja", "block.minecraft.light_gray_concrete_powder": "ko kule pi walo pimeja", "block.minecraft.light_gray_glazed_terracotta": "kiwen namako pi walo pimeja", "block.minecraft.light_gray_shulker_box": "poki pi walo pimeja pi mon<PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "kiwen lukin pi walo pimeja", "block.minecraft.light_gray_stained_glass_pane": "sinpin lukin pi walo pimeja", "block.minecraft.light_gray_terracotta": "ma kiwen pi walo pimeja", "block.minecraft.light_gray_wool": "len pi walo pimeja", "block.minecraft.light_weighted_pressure_plate": "supa noka pi kiwen jelo", "block.minecraft.lightning_rod": "palisa pi linja wawa suno", "block.minecraft.lilac": "kasi suli pi loje laso", "block.minecraft.lily_of_the_valley": "kasi lili suwi", "block.minecraft.lily_pad": "kasi telo sike", "block.minecraft.lime_banner": "len sitelen pi laso jelo", "block.minecraft.lime_bed": "supa lape pi laso jelo", "block.minecraft.lime_candle": "palisa suno lili pi laso jelo", "block.minecraft.lime_candle_cake": "palisa suno lili pi laso jelo lon pan suwi", "block.minecraft.lime_carpet": "len anpa pi laso jelo", "block.minecraft.lime_concrete": "kiwen kule pi laso jelo", "block.minecraft.lime_concrete_powder": "ko kule pi laso jelo", "block.minecraft.lime_glazed_terracotta": "kiwen namako pi laso jelo", "block.minecraft.lime_shulker_box": "poki pi laso jelo pi mon<PERSON><PERSON>", "block.minecraft.lime_stained_glass": "kiwen lukin pi laso jelo", "block.minecraft.lime_stained_glass_pane": "sinpin lukin pi laso jelo", "block.minecraft.lime_terracotta": "ma kiwen pi laso jelo", "block.minecraft.lime_wool": "len pi laso jelo", "block.minecraft.lodestone": "kiwen nasin", "block.minecraft.loom": "ilo len", "block.minecraft.magenta_banner": "len sitelen pi loje laso", "block.minecraft.magenta_bed": "supa lape pi loje laso", "block.minecraft.magenta_candle": "palisa suno lili pi loje laso", "block.minecraft.magenta_candle_cake": "palisa suno lili pi loje laso lon pan suwi", "block.minecraft.magenta_carpet": "len anpa pi loje laso", "block.minecraft.magenta_concrete": "kiwen kule pi loje laso", "block.minecraft.magenta_concrete_powder": "ko kule pi loje laso", "block.minecraft.magenta_glazed_terracotta": "kiwen namako pi loje laso", "block.minecraft.magenta_shulker_box": "poki pi loje laso pi monsu<PERSON>", "block.minecraft.magenta_stained_glass": "kiwen lukin pi loje laso", "block.minecraft.magenta_stained_glass_pane": "sinpin lukin pi loje laso", "block.minecraft.magenta_terracotta": "ma kiwen pi loje laso", "block.minecraft.magenta_wool": "len pi loje laso", "block.minecraft.magma_block": "kiwen seli", "block.minecraft.mangrove_button": "nena ilo pi kasi telo", "block.minecraft.mangrove_door": "lupa pi kasi telo", "block.minecraft.mangrove_fence": "sinpin palisa pi kasi telo", "block.minecraft.mangrove_fence_gate": "sinpin tawa pi kasi telo", "block.minecraft.mangrove_hanging_sign": "lipu sitelen sewi pi kasi telo", "block.minecraft.mangrove_leaves": "lipu pi kasi telo", "block.minecraft.mangrove_log": "sijelo pi kasi telo", "block.minecraft.mangrove_planks": "kiwen kipisi pi kasi telo", "block.minecraft.mangrove_pressure_plate": "supa noka pi kasi telo", "block.minecraft.mangrove_propagule": "kasi kiwen telo sin", "block.minecraft.mangrove_roots": "noka pi kasi telo", "block.minecraft.mangrove_sign": "lipu sitelen pi kasi telo", "block.minecraft.mangrove_slab": "supa pi kasi telo", "block.minecraft.mangrove_stairs": "leko sewi pi kasi telo", "block.minecraft.mangrove_trapdoor": "lupa anpa pi kasi telo", "block.minecraft.mangrove_wall_hanging_sign": "lipu sitelen sewi pi kasi telo lon sinpin", "block.minecraft.mangrove_wall_sign": "lipu sitelen pi kasi telo lon sinpin", "block.minecraft.mangrove_wood": "kiwen pi kasi telo", "block.minecraft.medium_amethyst_bud": "kiwen kalama kama lili", "block.minecraft.melon": "kili suli laso", "block.minecraft.melon_stem": "kasi pi kili suli laso", "block.minecraft.moss_block": "leko pi len kasi", "block.minecraft.moss_carpet": "len anpa kasi", "block.minecraft.mossy_cobblestone": "kiwen nena kasi", "block.minecraft.mossy_cobblestone_slab": "supa pi kiwen nena kasi", "block.minecraft.mossy_cobblestone_stairs": "leko sewi pi kiwen nena kasi", "block.minecraft.mossy_cobblestone_wall": "sinpin pi kiwen nena kasi", "block.minecraft.mossy_stone_brick_slab": "supa pi kiwen leko kasi", "block.minecraft.mossy_stone_brick_stairs": "leko sewi pi kiwen leko kasi", "block.minecraft.mossy_stone_brick_wall": "sinpin pi kiwen leko kasi", "block.minecraft.mossy_stone_bricks": "kiwen leko kasi", "block.minecraft.moving_piston": "ilo tawa tawa", "block.minecraft.mud": "ma telo", "block.minecraft.mud_brick_slab": "supa pi kiwen leko ma", "block.minecraft.mud_brick_stairs": "leko sewi pi kiwen leko ma", "block.minecraft.mud_brick_wall": "sinpin pi kiwen leko ma", "block.minecraft.mud_bricks": "kiwen leko ma", "block.minecraft.muddy_mangrove_roots": "ma noka pi kasi telo", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON> soko", "block.minecraft.mycelium": "ma soko", "block.minecraft.nether_brick_fence": "sinpin palisa pi ma Nether", "block.minecraft.nether_brick_slab": "supa pi ma Nether", "block.minecraft.nether_brick_stairs": "leko sewi pi ma Nether", "block.minecraft.nether_brick_wall": "sinpin kiwen pi ma <PERSON>her", "block.minecraft.nether_bricks": "kiwen leko pi ma <PERSON>her", "block.minecraft.nether_gold_ore": "kiwen jelo pi kiwen pi ma <PERSON>her", "block.minecraft.nether_portal": "lupa nasa pi ma Nether", "block.minecraft.nether_quartz_ore": "kiwen walo pi kiwen pi ma Nether", "block.minecraft.nether_sprouts": "kasi lili pi ma <PERSON>her", "block.minecraft.nether_wart": "soko wawa pi ma Nether", "block.minecraft.nether_wart_block": "soko wawa suli pi ma Nether", "block.minecraft.netherite_block": "leko pi mani Netherite", "block.minecraft.netherrack": "<PERSON><PERSON><PERSON>", "block.minecraft.note_block": "leko kalama", "block.minecraft.oak_button": "nena ilo pi kasi kili", "block.minecraft.oak_door": "lupa pi kasi kili", "block.minecraft.oak_fence": "sinpin palisa pi kasi kili", "block.minecraft.oak_fence_gate": "sinpin tawa pi kasi kili", "block.minecraft.oak_hanging_sign": "lipu sitelen sewi pi kasi kili", "block.minecraft.oak_leaves": "lipu pi kasi kili", "block.minecraft.oak_log": "sijelo pi kasi kili", "block.minecraft.oak_planks": "kiwen kipisi pi kasi kili", "block.minecraft.oak_pressure_plate": "supa noka pi kasi kili", "block.minecraft.oak_sapling": "kasi kili sin", "block.minecraft.oak_sign": "lipu sitelen pi kasi kili", "block.minecraft.oak_slab": "supa pi kasi kili", "block.minecraft.oak_stairs": "leko sewi pi kasi kili", "block.minecraft.oak_trapdoor": "lupa anpa pi kasi kili", "block.minecraft.oak_wall_hanging_sign": "lipu sitelen sewi pi kasi kili lon sinpin", "block.minecraft.oak_wall_sign": "lipu sitelen pi kasi kili lon sinpin", "block.minecraft.oak_wood": "kiwen pi kasi kili", "block.minecraft.observer": "ilo lukin wawa", "block.minecraft.obsidian": "kiwen pimeja wawa", "block.minecraft.ochre_froglight": "suno akesi jelo", "block.minecraft.ominous_banner": "len sitelen ike", "block.minecraft.open_eyeblossom": "kasi oko open", "block.minecraft.orange_banner": "len sitelen pi loje jelo", "block.minecraft.orange_bed": "supa lape pi loje jelo", "block.minecraft.orange_candle": "palisa suno lili pi loje jelo", "block.minecraft.orange_candle_cake": "palisa suno lili pi loje jelo lon pan suwi", "block.minecraft.orange_carpet": "len anpa pi loje jelo", "block.minecraft.orange_concrete": "kiwen kule pi loje jelo", "block.minecraft.orange_concrete_powder": "ko kule pi loje jelo", "block.minecraft.orange_glazed_terracotta": "kiwen namako pi loje jelo", "block.minecraft.orange_shulker_box": "poki pi loje jelo pi mon<PERSON><PERSON>", "block.minecraft.orange_stained_glass": "kiwen lukin pi loje jelo", "block.minecraft.orange_stained_glass_pane": "sinpin lukin pi loje jelo", "block.minecraft.orange_terracotta": "ma kiwen pi loje jelo", "block.minecraft.orange_tulip": "kasi uta pi loje jelo", "block.minecraft.orange_wool": "len pi loje jelo", "block.minecraft.oxeye_daisy": "kasi pi walo pimeja", "block.minecraft.oxidized_chiseled_copper": "kiwen ante sitelen laso", "block.minecraft.oxidized_copper": "kiwen ante laso", "block.minecraft.oxidized_copper_bulb": "suno ante laso", "block.minecraft.oxidized_copper_door": "lupa ante laso", "block.minecraft.oxidized_copper_grate": "kiwen ante lukin laso", "block.minecraft.oxidized_copper_trapdoor": "lupa anpa ante laso", "block.minecraft.oxidized_cut_copper": "kiwen ante kipisi laso", "block.minecraft.oxidized_cut_copper_slab": "supa pi kiwen ante laso", "block.minecraft.oxidized_cut_copper_stairs": "leko sewi pi kiwen ante laso", "block.minecraft.packed_ice": "kiwen telo kiwen", "block.minecraft.packed_mud": "ma telo kiwen", "block.minecraft.pale_hanging_moss": "kasi linja walo", "block.minecraft.pale_moss_block": "leko pi len kasi walo", "block.minecraft.pale_moss_carpet": "len anpa pi len kasi walo", "block.minecraft.pale_oak_button": "nena ilo pi kasi walo", "block.minecraft.pale_oak_door": "lupa pi kasi walo", "block.minecraft.pale_oak_fence": "sinpin palisa pi kasi walo", "block.minecraft.pale_oak_fence_gate": "sinpin tawa pi kasi walo", "block.minecraft.pale_oak_hanging_sign": "lipu sitelen sewi pi kasi walo", "block.minecraft.pale_oak_leaves": "lipu pi kasi walo", "block.minecraft.pale_oak_log": "sijelo pi kasi walo", "block.minecraft.pale_oak_planks": "kiwen kipisi pi kasi walo", "block.minecraft.pale_oak_pressure_plate": "supa noka pi kasi walo", "block.minecraft.pale_oak_sapling": "kasi walo sin", "block.minecraft.pale_oak_sign": "lipu sitelen pi kasi walo", "block.minecraft.pale_oak_slab": "supa pi kasi walo", "block.minecraft.pale_oak_stairs": "leko sewi pi kasi walo", "block.minecraft.pale_oak_trapdoor": "lupa anpa pi kasi walo", "block.minecraft.pale_oak_wall_hanging_sign": "lipu sitelen sewi pi kasi walo lon sinpin", "block.minecraft.pale_oak_wall_sign": "lipu sitelen pi kasi walo lon sinpin", "block.minecraft.pale_oak_wood": "kiwen pi kasi walo", "block.minecraft.pearlescent_froglight": "suno akesi walo", "block.minecraft.peony": "kasi suli pi loje walo", "block.minecraft.petrified_oak_slab": "supa pi kasi kili moli", "block.minecraft.piglin_head": "lawa pi jan <PERSON>", "block.minecraft.piglin_wall_head": "lawa pi jan <PERSON> lon sinpin", "block.minecraft.pink_banner": "len sitelen pi loje walo", "block.minecraft.pink_bed": "supa lape pi loje walo", "block.minecraft.pink_candle": "palisa suno lili pi loje walo", "block.minecraft.pink_candle_cake": "palisa suno lili pi loje walo lon pan suwi", "block.minecraft.pink_carpet": "len anpa pi loje walo", "block.minecraft.pink_concrete": "kiwen kule pi loje walo", "block.minecraft.pink_concrete_powder": "ko kule pi loje walo", "block.minecraft.pink_glazed_terracotta": "kiwen namako pi loje walo", "block.minecraft.pink_petals": "lipu kule pi kasi suwi", "block.minecraft.pink_shulker_box": "poki pi loje walo pi mon<PERSON><PERSON>", "block.minecraft.pink_stained_glass": "kiwen lukin pi loje walo", "block.minecraft.pink_stained_glass_pane": "sinpin lukin pi loje walo", "block.minecraft.pink_terracotta": "ma kiwen pi loje walo", "block.minecraft.pink_tulip": "kasi uta pi loje walo", "block.minecraft.pink_wool": "len pi loje walo", "block.minecraft.piston": "ilo tawa", "block.minecraft.piston_head": "luka pi ilo tawa", "block.minecraft.pitcher_crop": "kasi poki pi ma mama", "block.minecraft.pitcher_plant": "kasi poki", "block.minecraft.player_head": "lawa jan", "block.minecraft.player_head.named": "lawa %s", "block.minecraft.player_wall_head": "lawa jan lon sinpin", "block.minecraft.podzol": "ma kule", "block.minecraft.pointed_dripstone": "palisa kiwen", "block.minecraft.polished_andesite": "kiwen pimeja walo pi selo pona", "block.minecraft.polished_andesite_slab": "supa pi kiwen pimeja walo pi selo pona", "block.minecraft.polished_andesite_stairs": "leko sewi pi kiwen pimeja walo pi selo pona", "block.minecraft.polished_basalt": "kiwen palisa pimeja pi selo pona", "block.minecraft.polished_blackstone": "kiwen pimeja pi selo pona", "block.minecraft.polished_blackstone_brick_slab": "supa pi kiwen pimeja leko", "block.minecraft.polished_blackstone_brick_stairs": "leko sewi pi kiwen pimeja leko", "block.minecraft.polished_blackstone_brick_wall": "sinpin pi kiwen pimeja leko", "block.minecraft.polished_blackstone_bricks": "kiwen pimeja leko", "block.minecraft.polished_blackstone_button": "nena ilo pi kiwen pimeja pi selo pona", "block.minecraft.polished_blackstone_pressure_plate": "supa noka pi kiwen pimeja pi selo pona", "block.minecraft.polished_blackstone_slab": "supa pi kiwen pimeja pi selo pona", "block.minecraft.polished_blackstone_stairs": "leko sewi pi kiwen pimeja pi selo pona", "block.minecraft.polished_blackstone_wall": "sinpin pi kiwen pimeja pi selo pona", "block.minecraft.polished_deepslate": "kiwen anpa pi selo pona", "block.minecraft.polished_deepslate_slab": "supa pi kiwen anpa pi selo pona", "block.minecraft.polished_deepslate_stairs": "leko sewi pi kiwen anpa pi selo pona", "block.minecraft.polished_deepslate_wall": "sinpin pi kiwen anpa pi selo pona", "block.minecraft.polished_diorite": "kiwen walo pimeja pi selo pona", "block.minecraft.polished_diorite_slab": "supa pi kiwen walo pimeja pi selo pona", "block.minecraft.polished_diorite_stairs": "leko sewi pi kiwen walo pimeja pi selo pona", "block.minecraft.polished_granite": "kiwen loje pi selo pona", "block.minecraft.polished_granite_slab": "supa pi kiwen loje pi selo pona", "block.minecraft.polished_granite_stairs": "leko sewi pi kiwen loje pi selo pona", "block.minecraft.polished_tuff": "ko seli kiwen pi selo pona", "block.minecraft.polished_tuff_slab": "supa pi ko seli kiwen pi selo pona", "block.minecraft.polished_tuff_stairs": "leko sewi pi ko seli kiwen pi selo pona", "block.minecraft.polished_tuff_wall": "sinpin pi ko seli kiwen pi selo pona", "block.minecraft.poppy": "kasi loje", "block.minecraft.potatoes": "kili ma", "block.minecraft.potted_acacia_sapling": "kasi seli lon poki", "block.minecraft.potted_allium": "kasi pi laso loje lon poki", "block.minecraft.potted_azalea_bush": "kasi lupa lon poki", "block.minecraft.potted_azure_bluet": "kasi walo lili lon poki", "block.minecraft.potted_bamboo": "kasi palisa lon poki", "block.minecraft.potted_birch_sapling": "kasi jelo lon poki", "block.minecraft.potted_blue_orchid": "kasi pi laso sewi lon poki", "block.minecraft.potted_brown_mushroom": "soko pi loje ma lon poki", "block.minecraft.potted_cactus": "kasi kipisi lon poki", "block.minecraft.potted_cherry_sapling": "kasi suwi lon poki", "block.minecraft.potted_closed_eyeblossom": "kasi oko pini lon poki", "block.minecraft.potted_cornflower": "kasi laso lon poki", "block.minecraft.potted_crimson_fungus": "soko loje seli lon poki", "block.minecraft.potted_crimson_roots": "noka kasi loje lon poki", "block.minecraft.potted_dandelion": "kasi jelo lon poki", "block.minecraft.potted_dark_oak_sapling": "kasi pimeja lon poki", "block.minecraft.potted_dead_bush": "kasi moli lon poki", "block.minecraft.potted_fern": "kasi pi lipu waso lon poki", "block.minecraft.potted_flowering_azalea_bush": "kasi lupa kule lon poki", "block.minecraft.potted_jungle_sapling": "kasi suli lon poki", "block.minecraft.potted_lily_of_the_valley": "kasi lili suwi lon poki", "block.minecraft.potted_mangrove_propagule": "kasi kiwen telo sin lon poki", "block.minecraft.potted_oak_sapling": "kasi kili lon poki", "block.minecraft.potted_open_eyeblossom": "kasi oko open lon poki", "block.minecraft.potted_orange_tulip": "kasi uta pi loje jelo lon poki", "block.minecraft.potted_oxeye_daisy": "kasi pi walo pimeja lon poki", "block.minecraft.potted_pale_oak_sapling": "kasi walo lon poki", "block.minecraft.potted_pink_tulip": "kasi uta pi loje walo lon poki", "block.minecraft.potted_poppy": "kasi loje lon poki", "block.minecraft.potted_red_mushroom": "soko loje walo lon poki", "block.minecraft.potted_red_tulip": "kasi uta loje lon poki", "block.minecraft.potted_spruce_sapling": "kasi lete lon poki", "block.minecraft.potted_torchflower": "kasi seli lon poki", "block.minecraft.potted_warped_fungus": "soko laso lon poki", "block.minecraft.potted_warped_roots": "noka kasi laso lon poki", "block.minecraft.potted_white_tulip": "kasi uta walo lon poki", "block.minecraft.potted_wither_rose": "kasi moli <PERSON>er lon poki", "block.minecraft.powder_snow": "ko lete kon", "block.minecraft.powder_snow_cauldron": "ko lete kon lon poki telo suli", "block.minecraft.powered_rail": "nasin palisa wawa", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "supa pi kiwen leko <PERSON>", "block.minecraft.prismarine_brick_stairs": "leko sewi pi kiwen leko <PERSON>", "block.minecraft.prismarine_bricks": "kiwen leko <PERSON>", "block.minecraft.prismarine_slab": "supa pi kiwen Prismarine", "block.minecraft.prismarine_stairs": "leko sewi pi kiwen Prismarine", "block.minecraft.prismarine_wall": "sinpin pi kiwen Prismarine", "block.minecraft.pumpkin": "kili suli jelo", "block.minecraft.pumpkin_stem": "kasi pi kili suli jelo", "block.minecraft.purple_banner": "len sitelen pi laso loje", "block.minecraft.purple_bed": "supa lape pi laso loje", "block.minecraft.purple_candle": "palisa suno lili pi laso loje", "block.minecraft.purple_candle_cake": "palisa suno lili pi laso loje lon pan suwi", "block.minecraft.purple_carpet": "len anpa pi laso loje", "block.minecraft.purple_concrete": "kiwen kule pi laso loje", "block.minecraft.purple_concrete_powder": "ko kule pi laso loje", "block.minecraft.purple_glazed_terracotta": "kiwen namako pi laso loje", "block.minecraft.purple_shulker_box": "poki pi laso loje pi mon<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "kiwen lukin pi laso loje", "block.minecraft.purple_stained_glass_pane": "sinpin lukin pi laso loje", "block.minecraft.purple_terracotta": "ma kiwen pi laso loje", "block.minecraft.purple_wool": "len pi laso loje", "block.minecraft.purpur_block": "k<PERSON>wen Purpur", "block.minecraft.purpur_pillar": "kiwen Purpur palisa", "block.minecraft.purpur_slab": "supa pi kiwen Purpur", "block.minecraft.purpur_stairs": "leko sewi pi kiwen Purpur", "block.minecraft.quartz_block": "leko pi kiwen walo pi ma seli", "block.minecraft.quartz_bricks": "kiwen walo leko", "block.minecraft.quartz_pillar": "kiwen walo palisa", "block.minecraft.quartz_slab": "supa pi kiwen walo", "block.minecraft.quartz_stairs": "leko sewi pi kiwen walo", "block.minecraft.rail": "nasin palisa", "block.minecraft.raw_copper_block": "leko pi kiwen ante lete", "block.minecraft.raw_gold_block": "leko pi kiwen jelo lete", "block.minecraft.raw_iron_block": "leko pi kiwen walo lete", "block.minecraft.red_banner": "len sitelen loje", "block.minecraft.red_bed": "supa lape loje", "block.minecraft.red_candle": "palisa suno lili loje", "block.minecraft.red_candle_cake": "palisa suno lili loje lon pan suwi", "block.minecraft.red_carpet": "len anpa loje", "block.minecraft.red_concrete": "kiwen kule loje", "block.minecraft.red_concrete_powder": "ko kule loje", "block.minecraft.red_glazed_terracotta": "kiwen namako loje", "block.minecraft.red_mushroom": "soko loje walo", "block.minecraft.red_mushroom_block": "soko suli loje walo", "block.minecraft.red_nether_brick_slab": "supa pi kiwen leko loje pi ma Nether", "block.minecraft.red_nether_brick_stairs": "leko sewi pi kiwen leko loje pi ma Nether", "block.minecraft.red_nether_brick_wall": "sinpin pi kiwen leko loje pi ma Nether", "block.minecraft.red_nether_bricks": "kiwen leko loje pi ma Nether", "block.minecraft.red_sand": "ko loje", "block.minecraft.red_sandstone": "kiwen ko loje", "block.minecraft.red_sandstone_slab": "supa pi kiwen ko loje", "block.minecraft.red_sandstone_stairs": "leko sewi pi kiwen ko loje", "block.minecraft.red_sandstone_wall": "sinpin pi kiwen ko loje", "block.minecraft.red_shulker_box": "poki loje pi mon<PERSON><PERSON>", "block.minecraft.red_stained_glass": "kiwen lukin loje", "block.minecraft.red_stained_glass_pane": "sinpin lukin loje", "block.minecraft.red_terracotta": "ma kiwen loje", "block.minecraft.red_tulip": "kasi uta loje", "block.minecraft.red_wool": "len loje", "block.minecraft.redstone_block": "leko pi ko Redstone", "block.minecraft.redstone_lamp": "suno pi ko Redstone", "block.minecraft.redstone_ore": "ko Redstone pi insa kiwen", "block.minecraft.redstone_torch": "palisa suno loje", "block.minecraft.redstone_wall_torch": "palisa suno pi ko Redstone lon sinpin", "block.minecraft.redstone_wire": "linja pi ko Redstone", "block.minecraft.reinforced_deepslate": "kiwen anpa wawa", "block.minecraft.repeater": "ilo pi tenpo wawa pi ko Redstone", "block.minecraft.repeating_command_block": "ilo pi toki wawa lon sike", "block.minecraft.resin_block": "leko pi kiwen ko loje", "block.minecraft.resin_brick_slab": "supa pi kiwen ko loje", "block.minecraft.resin_brick_stairs": "leko sewi pi kiwen ko loje", "block.minecraft.resin_brick_wall": "sinpin kiwen pi kiwen ko loje", "block.minecraft.resin_bricks": "kiwen leko pi kiwen ko loje", "block.minecraft.resin_clump": "kulupu pi kiwen ko loje", "block.minecraft.respawn_anchor": "ilo pi kama sin", "block.minecraft.rooted_dirt": "ma pi noka kasi", "block.minecraft.rose_bush": "kasi loje suwi", "block.minecraft.sand": "ko", "block.minecraft.sandstone": "kiwen ko", "block.minecraft.sandstone_slab": "supa pi kiwen ko", "block.minecraft.sandstone_stairs": "leko sewi pi kiwen ko", "block.minecraft.sandstone_wall": "sinpin pi kiwen ko", "block.minecraft.scaffolding": "supa palisa", "block.minecraft.sculk": "<PERSON><PERSON>", "block.minecraft.sculk_catalyst": "so<PERSON> Sculk mama", "block.minecraft.sculk_sensor": "soko Sculk kute", "block.minecraft.sculk_shrieker": "soko Sculk kalama", "block.minecraft.sculk_vein": "len pi soko Sculk", "block.minecraft.sea_lantern": "suno telo", "block.minecraft.sea_pickle": "kili telo", "block.minecraft.seagrass": "kasi telo", "block.minecraft.set_spawn": "ma ni la sina kama sin", "block.minecraft.short_dry_grass": "kasi anpa pi telo ala", "block.minecraft.short_grass": "kasi anpa", "block.minecraft.shroomlight": "suno soko", "block.minecraft.shulker_box": "poki pi mon<PERSON><PERSON>", "block.minecraft.skeleton_skull": "lawa pi jan moli palisa", "block.minecraft.skeleton_wall_skull": "lawa pi jan moli palisa lon sinpin", "block.minecraft.slime_block": "ko monsuta suli", "block.minecraft.small_amethyst_bud": "kiwen kalama kama sin", "block.minecraft.small_dripleaf": "kasi supa lili", "block.minecraft.smithing_table": "supa ilo", "block.minecraft.smoker": "poki pi seli moku", "block.minecraft.smooth_basalt": "kiwen pi seli lete pi selo pona", "block.minecraft.smooth_quartz": "kiwen walo suli pi selo pona", "block.minecraft.smooth_quartz_slab": "supa pi kiwen walo pi selo pona", "block.minecraft.smooth_quartz_stairs": "leko sewi pi kiwen walo pi selo pona", "block.minecraft.smooth_red_sandstone": "kiwen ko loje pi selo pona", "block.minecraft.smooth_red_sandstone_slab": "supa pi kiwen ko loje pi selo pona", "block.minecraft.smooth_red_sandstone_stairs": "leko sewi pi kiwen ko loje pi selo pona", "block.minecraft.smooth_sandstone": "kiwen ko pi selo pona", "block.minecraft.smooth_sandstone_slab": "supa pi kiwen ko pi selo pona", "block.minecraft.smooth_sandstone_stairs": "leko sewi pi kiwen ko pi selo pona", "block.minecraft.smooth_stone": "kiwen pi selo pona", "block.minecraft.smooth_stone_slab": "supa kiwen pi selo pona", "block.minecraft.sniffer_egg": "sike mama pi soweli suli alasa", "block.minecraft.snow": "ko lete", "block.minecraft.snow_block": "ko lete suli", "block.minecraft.soul_campfire": "seli palisa kon", "block.minecraft.soul_fire": "seli kon", "block.minecraft.soul_lantern": "suno kon", "block.minecraft.soul_sand": "ko kon", "block.minecraft.soul_soil": "ma kon", "block.minecraft.soul_torch": "palisa suno kon", "block.minecraft.soul_wall_torch": "palisa suno kon lon sinpin", "block.minecraft.spawn.not_valid": "sina jo ala e supa lape e ilo pi kama sin. ken la ona li pakala", "block.minecraft.spawner": "ilo kama", "block.minecraft.spawner.desc1": "sina kepeken sike mama la:", "block.minecraft.spawner.desc2": "ni li ante e soweli/monsuta", "block.minecraft.sponge": "ko pi weka telo", "block.minecraft.spore_blossom": "kasi kule pana", "block.minecraft.spruce_button": "nena ilo pi kasi lete", "block.minecraft.spruce_door": "lupa pi kasi lete", "block.minecraft.spruce_fence": "sinpin palisa pi kasi lete", "block.minecraft.spruce_fence_gate": "sinpin tawa pi kasi lete", "block.minecraft.spruce_hanging_sign": "lipu sitelen sewi pi kasi lete", "block.minecraft.spruce_leaves": "lipu pi kasi lete", "block.minecraft.spruce_log": "sijelo pi kasi lete", "block.minecraft.spruce_planks": "kiwen kipisi pi kasi lete", "block.minecraft.spruce_pressure_plate": "supa noka pi kasi lete", "block.minecraft.spruce_sapling": "kasi lete sin", "block.minecraft.spruce_sign": "lipu sitelen pi kasi lete", "block.minecraft.spruce_slab": "supa pi kasi lete", "block.minecraft.spruce_stairs": "leko sewi pi kasi lete", "block.minecraft.spruce_trapdoor": "lupa anpa pi kasi lete", "block.minecraft.spruce_wall_hanging_sign": "lipu sitelen sewi pi kasi lete lon sinpin", "block.minecraft.spruce_wall_sign": "lipu sitelen pi kasi lete lon sinpin", "block.minecraft.spruce_wood": "kiwen pi kasi lete", "block.minecraft.sticky_piston": "ilo tawa ko", "block.minecraft.stone": "kiwen", "block.minecraft.stone_brick_slab": "supa pi kiwen leko", "block.minecraft.stone_brick_stairs": "leko sewi pi kiwen leko", "block.minecraft.stone_brick_wall": "sinpin pi kiwen leko", "block.minecraft.stone_bricks": "kiwen leko", "block.minecraft.stone_button": "nena ilo kiwen", "block.minecraft.stone_pressure_plate": "supa noka kiwen", "block.minecraft.stone_slab": "supa kiwen", "block.minecraft.stone_stairs": "leko sewi kiwen", "block.minecraft.stonecutter": "sike pi kipisi kiwen", "block.minecraft.stripped_acacia_log": "sijelo pi kasi seli pi selo weka", "block.minecraft.stripped_acacia_wood": "kiwen pi kasi seli pi selo weka", "block.minecraft.stripped_bamboo_block": "sijelo pi leko kasi palisa", "block.minecraft.stripped_birch_log": "sijelo pi kasi jelo pi selo weka", "block.minecraft.stripped_birch_wood": "kiwen pi kasi jelo pi selo weka", "block.minecraft.stripped_cherry_log": "sijelo pi kasi suwi pi selo weka", "block.minecraft.stripped_cherry_wood": "kiwen pi kasi suwi pi selo weka", "block.minecraft.stripped_crimson_hyphae": "sijelo selo pi soko loje pi selo weka", "block.minecraft.stripped_crimson_stem": "sijelo pi soko loje pi selo weka", "block.minecraft.stripped_dark_oak_log": "sijelo pi kasi pimeja pi selo weka", "block.minecraft.stripped_dark_oak_wood": "kiwen pi kasi pimeja pi selo weka", "block.minecraft.stripped_jungle_log": "sijelo pi kasi suli pi selo weka", "block.minecraft.stripped_jungle_wood": "kiwen pi kasi suli pi selo weka", "block.minecraft.stripped_mangrove_log": "sijelo pi kasi telo pi selo weka", "block.minecraft.stripped_mangrove_wood": "kiwen pi kasi telo pi selo weka", "block.minecraft.stripped_oak_log": "sijelo pi kasi kili pi selo weka", "block.minecraft.stripped_oak_wood": "kiwen pi kasi kili pi selo weka", "block.minecraft.stripped_pale_oak_log": "sijelo pi kasi walo pi selo weka", "block.minecraft.stripped_pale_oak_wood": "kiwen pi kasi walo pi selo weka", "block.minecraft.stripped_spruce_log": "sijelo pi kasi lete pi selo weka", "block.minecraft.stripped_spruce_wood": "kiwen pi kasi lete pi selo weka", "block.minecraft.stripped_warped_hyphae": "sijelo selo pi soko laso pi selo weka", "block.minecraft.stripped_warped_stem": "sijelo pi soko laso pi selo weka", "block.minecraft.structure_block": "ilo pi pali tomo", "block.minecraft.structure_void": "ala pi pali tomo", "block.minecraft.sugar_cane": "kasi palisa suwi", "block.minecraft.sunflower": "kasi suno", "block.minecraft.suspicious_gravel": "kiwen lili mute nasa", "block.minecraft.suspicious_sand": "ko nasa", "block.minecraft.sweet_berry_bush": "kasi pi kili suwi", "block.minecraft.tall_dry_grass": "kasi suli pi telo ala", "block.minecraft.tall_grass": "kasi anpa suli", "block.minecraft.tall_seagrass": "kasi telo suli", "block.minecraft.target": "sike pi alasa musi", "block.minecraft.terracotta": "ma kiwen", "block.minecraft.test_block": "leko alasa", "block.minecraft.test_instance_block": "poki pi leko alasa", "block.minecraft.tinted_glass": "kiwen lukin pi awen suno", "block.minecraft.tnt": "ilo pi seli pakala", "block.minecraft.tnt.disabled": "pakala li tan ilo pi seli pakala la ona li ken ala", "block.minecraft.torch": "palisa suno", "block.minecraft.torchflower": "kasi seli", "block.minecraft.torchflower_crop": "kasi seli pi ma mama", "block.minecraft.trapped_chest": "poki lukin", "block.minecraft.trial_spawner": "ilo kama pi tomo alasa", "block.minecraft.tripwire": "linja noka", "block.minecraft.tripwire_hook": "ilo pi linja noka", "block.minecraft.tube_coral": "kiwen kala laso", "block.minecraft.tube_coral_block": "kiwen kala laso suli", "block.minecraft.tube_coral_fan": "lipu pi kiwen kala laso", "block.minecraft.tube_coral_wall_fan": "lipu sinpin pi kiwen kala laso", "block.minecraft.tuff": "ko seli kiwen", "block.minecraft.tuff_brick_slab": "supa pi ko seli kiwen leko", "block.minecraft.tuff_brick_stairs": "leko sewi pi ko seli kiwen leko", "block.minecraft.tuff_brick_wall": "sinpin pi ko seli kiwen leko", "block.minecraft.tuff_bricks": "ko seli kiwen leko", "block.minecraft.tuff_slab": "supa pi ko seli kiwen", "block.minecraft.tuff_stairs": "leko sewi pi ko seli kiwen", "block.minecraft.tuff_wall": "sinpin pi ko seli kiwen", "block.minecraft.turtle_egg": "sike mama pi akesi tomo", "block.minecraft.twisting_vines": "kasi linja laso", "block.minecraft.twisting_vines_plant": "kasi linja laso", "block.minecraft.vault": "poki mani", "block.minecraft.verdant_froglight": "suno akesi laso", "block.minecraft.vine": "kasi linja", "block.minecraft.void_air": "kon pimeja", "block.minecraft.wall_torch": "palisa suno lon sinpin", "block.minecraft.warped_button": "nena ilo pi soko laso", "block.minecraft.warped_door": "lupa pi soko laso", "block.minecraft.warped_fence": "sinpin palisa pi soko laso", "block.minecraft.warped_fence_gate": "sinpin tawa pi soko laso", "block.minecraft.warped_fungus": "soko laso", "block.minecraft.warped_hanging_sign": "lipu sitelen sewi pi soko laso", "block.minecraft.warped_hyphae": "sijelo selo pi soko laso", "block.minecraft.warped_nylium": "kiwen selo pi soko laso", "block.minecraft.warped_planks": "kiwen kipisi pi soko laso", "block.minecraft.warped_pressure_plate": "supa noka pi soko laso", "block.minecraft.warped_roots": "noka kasi laso", "block.minecraft.warped_sign": "lipu sitelen pi soko laso", "block.minecraft.warped_slab": "supa pi soko laso", "block.minecraft.warped_stairs": "leko sewi pi soko laso", "block.minecraft.warped_stem": "sijelo pi soko laso", "block.minecraft.warped_trapdoor": "lupa anpa pi soko laso", "block.minecraft.warped_wall_hanging_sign": "lipu sitelen sewi pi soko laso lon sinpin", "block.minecraft.warped_wall_sign": "lipu sitelen pi soko laso lon sinpin", "block.minecraft.warped_wart_block": "soko laso suli", "block.minecraft.water": "telo", "block.minecraft.water_cauldron": "telo lon poki telo suli", "block.minecraft.waxed_chiseled_copper": "kiwen ante sitelen awen", "block.minecraft.waxed_copper_block": "leko pi kiwen ante awen", "block.minecraft.waxed_copper_bulb": "suno ante awen", "block.minecraft.waxed_copper_door": "lupa ante awen", "block.minecraft.waxed_copper_grate": "kiwen ante lukin awen", "block.minecraft.waxed_copper_trapdoor": "lupa anpa ante awen", "block.minecraft.waxed_cut_copper": "kiwen ante kipisi awen", "block.minecraft.waxed_cut_copper_slab": "supa pi kiwen ante awen", "block.minecraft.waxed_cut_copper_stairs": "leko sewi awen pi kiwen ante", "block.minecraft.waxed_exposed_chiseled_copper": "kiwen ante sitelen awen pi loje ma", "block.minecraft.waxed_exposed_copper": "kiwen ante awen pi loje ma", "block.minecraft.waxed_exposed_copper_bulb": "suno ante awen pi loje ma", "block.minecraft.waxed_exposed_copper_door": "lupa ante awen pi loje ma", "block.minecraft.waxed_exposed_copper_grate": "kiwen ante lukin awen pi loje ma", "block.minecraft.waxed_exposed_copper_trapdoor": "lupa anpa ante awen pi loje ma", "block.minecraft.waxed_exposed_cut_copper": "kiwen ante kipisi awen pi loje ma", "block.minecraft.waxed_exposed_cut_copper_slab": "supa pi kiwen ante awen pi loje ma", "block.minecraft.waxed_exposed_cut_copper_stairs": "leko sewi awen pi kiwen ante pi loje ma", "block.minecraft.waxed_oxidized_chiseled_copper": "kiwen ante sitelen awen laso", "block.minecraft.waxed_oxidized_copper": "kiwen ante awen laso", "block.minecraft.waxed_oxidized_copper_bulb": "suno ante awen laso", "block.minecraft.waxed_oxidized_copper_door": "lupa ante awen laso", "block.minecraft.waxed_oxidized_copper_grate": "kiwen ante lukin awen laso", "block.minecraft.waxed_oxidized_copper_trapdoor": "lupa anpa ante awen laso", "block.minecraft.waxed_oxidized_cut_copper": "kiwen ante kipisi awen laso", "block.minecraft.waxed_oxidized_cut_copper_slab": "supa pi kiwen ante awen laso", "block.minecraft.waxed_oxidized_cut_copper_stairs": "leko sewi awen pi kiwen ante laso", "block.minecraft.waxed_weathered_chiseled_copper": "kiwen ante sitelen awen pi laso lili", "block.minecraft.waxed_weathered_copper": "kiwen ante awen pi laso lili", "block.minecraft.waxed_weathered_copper_bulb": "kiwen ante suno awen pi laso lili", "block.minecraft.waxed_weathered_copper_door": "lupa ante awen pi laso lili", "block.minecraft.waxed_weathered_copper_grate": "kiwen ante lukin awen pi laso lili", "block.minecraft.waxed_weathered_copper_trapdoor": "lupa anpa ante awen pi laso lili", "block.minecraft.waxed_weathered_cut_copper": "kiwen ante kipisi awen pi laso lili", "block.minecraft.waxed_weathered_cut_copper_slab": "supa pi kiwen ante awen pi laso lili", "block.minecraft.waxed_weathered_cut_copper_stairs": "leko sewi awen pi kiwen ante pi laso lili", "block.minecraft.weathered_chiseled_copper": "kiwen ante sitelen pi laso lili", "block.minecraft.weathered_copper": "kiwen ante pi laso lili", "block.minecraft.weathered_copper_bulb": "suno ante pi laso lili", "block.minecraft.weathered_copper_door": "lupa ante pi laso lili", "block.minecraft.weathered_copper_grate": "kiwen ante lukin pi laso lili", "block.minecraft.weathered_copper_trapdoor": "lupa anpa ante pi laso lili", "block.minecraft.weathered_cut_copper": "kiwen ante kipisi pi laso lili", "block.minecraft.weathered_cut_copper_slab": "supa pi kiwen ante pi laso lili", "block.minecraft.weathered_cut_copper_stairs": "leko sewi pi kiwen ante pi laso lili", "block.minecraft.weeping_vines": "kasi linja loje", "block.minecraft.weeping_vines_plant": "kasi linja loje", "block.minecraft.wet_sponge": "ko telo", "block.minecraft.wheat": "kasi pan", "block.minecraft.white_banner": "len sitelen walo", "block.minecraft.white_bed": "supa lape walo", "block.minecraft.white_candle": "palisa suno lili walo", "block.minecraft.white_candle_cake": "palisa suno lili walo lon pan suwi", "block.minecraft.white_carpet": "len anpa walo", "block.minecraft.white_concrete": "kiwen kule walo", "block.minecraft.white_concrete_powder": "ko kule walo", "block.minecraft.white_glazed_terracotta": "kiwen namako walo", "block.minecraft.white_shulker_box": "poki walo pi mon<PERSON><PERSON>", "block.minecraft.white_stained_glass": "kiwen lukin walo", "block.minecraft.white_stained_glass_pane": "sinpin lukin walo", "block.minecraft.white_terracotta": "ma kiwen walo", "block.minecraft.white_tulip": "kasi uta walo", "block.minecraft.white_wool": "len walo", "block.minecraft.wildflowers": "kasi kule lili", "block.minecraft.wither_rose": "kasi moli <PERSON>", "block.minecraft.wither_skeleton_skull": "lawa pi jan moli palisa <PERSON>", "block.minecraft.wither_skeleton_wall_skull": "lawa pi jan moli palisa Wither lon sinpin", "block.minecraft.yellow_banner": "len <PERSON>len jelo", "block.minecraft.yellow_bed": "supa lape jelo", "block.minecraft.yellow_candle": "palisa suno lili jelo", "block.minecraft.yellow_candle_cake": "palisa suno lili jelo lon pan suwi", "block.minecraft.yellow_carpet": "len anpa jelo", "block.minecraft.yellow_concrete": "kiwen kule jelo", "block.minecraft.yellow_concrete_powder": "ko kule jelo", "block.minecraft.yellow_glazed_terracotta": "kiwen namako jelo", "block.minecraft.yellow_shulker_box": "poki jelo pi mon<PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "kiwen lukin jelo", "block.minecraft.yellow_stained_glass_pane": "sinpin lukin jelo", "block.minecraft.yellow_terracotta": "ma kiwen jelo", "block.minecraft.yellow_wool": "len jelo", "block.minecraft.zombie_head": "lawa pi jan moli", "block.minecraft.zombie_wall_head": "lawa pi jan moli lon sinpin", "book.byAuthor": "tan %1$s", "book.edit.title": "ante pi lipu ni", "book.editTitle": "o sitelen e nimi lipu:", "book.finalizeButton": "o pini e lipu", "book.finalizeWarning": "o sona e ni! sina sitelen e nimi sina lon lipu la, sina ken ala ante e ona.", "book.generation.0": "lipu nanpa wan", "book.generation.1": "lipu tan lipu nanpa wan", "book.generation.2": "sama lipu tan sama lipu", "book.generation.3": "pakala lili", "book.invalid.tag": "* sitelen ike *", "book.pageIndicator": "lipu nanpa %1$s (%2$s li lon)", "book.page_button.next": "<PERSON>u kama", "book.page_button.previous": "lipu pini", "book.sign.title": "lipu pi sitelen nimi lipu", "book.sign.titlebox": "nimi", "book.signButton": "o pana e nimi sina", "book.view.title": "lipu pi lukin lipu", "build.tooHigh": "sina ken ala pali lon sewi %s", "chat.cannotSend": "mi ken ala pana e toki sina", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "o tawa ma ni", "chat.copy": "o pana e ni tawa poki sama", "chat.copy.click": "o pana e ni tawa poki sama", "chat.deleted_marker": "ma kulupu li weka e toki ni.", "chat.disabled.chain_broken": "pakala! sina ken ala toki tan ijo nasa. o weka li kama sin.", "chat.disabled.expiredProfileKey": "toki li ken ala tan ijo nasa. o alasa kama sin.", "chat.disabled.invalid_command_signature": "toki wawa la nanpa awen li ante anu ike.", "chat.disabled.invalid_signature": "toki li jo e nanpa awen ike. o weka o kama sin.", "chat.disabled.launcher": "ilo open sina li ken ala e toki. mi ken ala pana e toki sina.", "chat.disabled.missingProfileKey": "pakala! sina ken ala toki tan ijo nasa. o weka li kama sin.", "chat.disabled.options": "ante sina li ken ala e toki.", "chat.disabled.out_of_order_chat": "mi lukin e toki pi nasin tenpo ike. tenpo pi ilo sina li ante ala ante?", "chat.disabled.profile": "wile pi nimi musi sina li ken ala e toki. o luka sin e '%s' tawa sona namako.", "chat.disabled.profile.moreInfo": "wile pi nimi musi sina li ken ala e toki. sina ken ala pana e toki li ken ala lukin e toki pi jan ante.", "chat.editBox": "toki", "chat.filtered": "ma kulupu li lawa e toki.", "chat.filtered_full": "mu kulupu li len e toki sina tawa jan mute.", "chat.link.confirm": "sina wile ala wile open e lipu ni?", "chat.link.confirmTrusted": "sina wile open e lipu ni anu sina wile poki e nasin tawa ona?", "chat.link.open": "o open", "chat.link.warning": "o sona e pona pi jan pana a!", "chat.queue": "[linja sitelen %s li kama]", "chat.square_brackets": "[%s]", "chat.tag.error": "ma kulupu li pana e toki ike", "chat.tag.modified": "ma kulupu li ante e toki ni. open la ona li ni:", "chat.tag.not_secure": "ma kulupu li sona ala e jan pi toki ni. sina ken ala pana e ona.", "chat.tag.system": "toki pi ma kulupu. sina ken ala pana e ni.", "chat.tag.system_single_player": "toki pi ma kulupu.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s li pini e pali suli ni: %s", "chat.type.advancement.goal": "%s li pali e ni: %s", "chat.type.advancement.task": "%s li pali e ni: %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "o toki tawa kulupu", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s li toki e %s", "chat.validation_error": "ma kulupu li ken ala lukin e ni: toki sina li pona", "chat_screen.message": "toki pana: %s", "chat_screen.title": "lipu toki", "chat_screen.usage": "o sitelen lon ni o kepeken nena Enter tawa pana", "chunk.toast.checkLog": "sina lukin e lipu la, sina ken sona e ante", "chunk.toast.loadFailure": "mi ken ala sona e leko suli lon %s", "chunk.toast.lowDiskSpace": "poki awen li lili!", "chunk.toast.lowDiskSpace.description": "ken la mi ken ala awen e ma.", "chunk.toast.saveFailure": "mi ken ala awen e leko suli lon %s", "clear.failed.multiple": "jan %s li jo ala e ijo", "clear.failed.single": "%s li jo ala e ijo", "color.minecraft.black": "pime<PERSON>", "color.minecraft.blue": "laso telo", "color.minecraft.brown": "loje ma", "color.minecraft.cyan": "laso", "color.minecraft.gray": "pimeja walo", "color.minecraft.green": "laso kasi", "color.minecraft.light_blue": "laso sewi", "color.minecraft.light_gray": "walo pimeja", "color.minecraft.lime": "laso jelo", "color.minecraft.magenta": "loje laso", "color.minecraft.orange": "loje jelo", "color.minecraft.pink": "loje walo", "color.minecraft.purple": "laso loje", "color.minecraft.red": "loje", "color.minecraft.white": "walo", "color.minecraft.yellow": "jelo", "command.context.here": "<--[LON NI]", "command.context.parse_error": "%s lon ma nanpa %s: %s", "command.exception": "mi sona ala e toki wawa ni: %s", "command.expected.separator": "mi wile e weka lon pini, taso ijo li lon", "command.failed": "sina toki e toki wawa ni la mi pakala", "command.forkLimit": "ante sona pi toki wawa li mute nanpa suli (%s)", "command.unknown.argument": "sina pana e ijo ike lon toki wawa", "command.unknown.command": "toki wawa sina li pakala. o lukin anpa", "commands.advancement.criterionNotFound": "pali pona %1$s li jo ala e \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "mi ken ala pana e pali lili \"%s\" pi pali pona %s tawa jan %s tan ni: pali ni li awen lon ona", "commands.advancement.grant.criterion.to.many.success": "mi pana e pali lili \"%s\" pi pali pona %s tawa jan %s", "commands.advancement.grant.criterion.to.one.failure": "mi ken ala pana e pali lili \"%s\" pi pali pona %s tawa jan %s tan ni: pali ni li awen lon ona", "commands.advancement.grant.criterion.to.one.success": "mi pana e pali lili \"%s\" pi pali pona %s tawa jan %s", "commands.advancement.grant.many.to.many.failure": "mi ken ala pana e pali pona %s tawa jan %s tan ni: pali ni li awen lon ona", "commands.advancement.grant.many.to.many.success": "mi pana e pali pona %s tawa jan %s", "commands.advancement.grant.many.to.one.failure": "mi ken ala pana e pali pona %s tawa jan %s tan ni: pali ni li awen lon ona", "commands.advancement.grant.many.to.one.success": "mi pana e pali pona %s tawa jan %s", "commands.advancement.grant.one.to.many.failure": "mi ken ala pana e pali pona %s tawa jan %s tan ni: pali ni li awen lon ona", "commands.advancement.grant.one.to.many.success": "mi pana e pali pona %s tawa jan %s", "commands.advancement.grant.one.to.one.failure": "mi ken ala pana e pali pona %s tawa jan %s tan ni: pali ni li awen lon ona", "commands.advancement.grant.one.to.one.success": "mi pana e pali pona %s tawa jan %s", "commands.advancement.revoke.criterion.to.many.failure": "mi ken ala weka e pali lili \"%s\" pi pali pona %s tan jan %s tan ni: pali ni li lon ala ona", "commands.advancement.revoke.criterion.to.many.success": "mi weka e pali lili \"%s\" pi pali pona %s tan jan %s", "commands.advancement.revoke.criterion.to.one.failure": "mi ken ala weka e pali lili \"%s\" pi pali pona %s tan jan %s tan ni: pali ni li lon ala ona", "commands.advancement.revoke.criterion.to.one.success": "mi weka e pali lili \"%s\" pi pali pona %s tan jan %s", "commands.advancement.revoke.many.to.many.failure": "mi ken ala weka e pali pini %s tan jan %s tan ni: pali ni li lon ala ona", "commands.advancement.revoke.many.to.many.success": "mi weka e pali pona %s tan jan %s", "commands.advancement.revoke.many.to.one.failure": "mi ken ala weka e pali pini %s tan jan %s tan ni: pali ni li lon ala ona", "commands.advancement.revoke.many.to.one.success": "mi weka e pali pona %s tan jan %s", "commands.advancement.revoke.one.to.many.failure": "mi ken ala weka e pali pini %s tan jan %s tan ni: pali ni li lon ala ona", "commands.advancement.revoke.one.to.many.success": "mi weka e pali pona %s tan jan %s", "commands.advancement.revoke.one.to.one.failure": "mi ken ala weka e pali pona %s tan jan %s tan ni: pali ni li lon ala ona", "commands.advancement.revoke.one.to.one.success": "mi weka e pali pona %s tan jan %s", "commands.attribute.base_value.get.success": "ante ala la nasin %s lon ijo %s li %s", "commands.attribute.base_value.reset.success": "ante ala la nasin %s lon ijo %s li kama ona pi nasa ala: %s", "commands.attribute.base_value.set.success": "ante ala la nasin %s lon ijo %s li kama %s", "commands.attribute.failed.entity": "ijo %s li ike tawa toki wawa ni", "commands.attribute.failed.modifier_already_present": "ante %s li awen lon nasin %s pi ijo %s", "commands.attribute.failed.no_attribute": "ijo %s la nasin %s li lon ala", "commands.attribute.failed.no_modifier": "nasin %s pi ijo %s la ante %s li lon ala", "commands.attribute.modifier.add.success": "mi pana e ante %s lon nasin %s pi ijo %s", "commands.attribute.modifier.remove.success": "mi weka e ante %s tan nasin %s pi ijo %s", "commands.attribute.modifier.value.get.success": "ante %s lon nasin %s pi ijo %s li %s", "commands.attribute.value.get.success": "nasin %s lon ijo %s li %s", "commands.ban.failed": "mi ante e ala. awen la jan ni li ken ala kama", "commands.ban.success": "jan %s li ken ala kama: %s", "commands.banip.failed": "mi ante e ala. awen la nimi IP ni li ken ala kama", "commands.banip.info": "jan %s li ken ala kama: %s", "commands.banip.invalid": "nimi <PERSON> ni anu jan ni li lon ala", "commands.banip.success": "nimi IP %s li ken ala kama: %s", "commands.banlist.entry": "jan %s li ken ala kama lon tenpo ale. ni li tan jan %s: %s", "commands.banlist.entry.unknown": "(sona ala)", "commands.banlist.list": "jan %s li ken ala kama:", "commands.banlist.none": "jan ale li ken kama", "commands.bossbar.create.failed": "ijo ante pi lukin moli li kepeken nimi \"%s\"", "commands.bossbar.create.success": "mi pali e ijo pi lukin moli pi nimi %s", "commands.bossbar.get.max": "ijo pi lukin moli pi nimi %s la nanpa suli ona li %s", "commands.bossbar.get.players.none": "ijo pi lukin moli pi nimi %s la jan ala li lon", "commands.bossbar.get.players.some": "ijo pi lukin moli pi nimi %s la jan %s li lon li %s", "commands.bossbar.get.value": "ijo pi lukin moli pi nimi %s la nanpa ona li %s", "commands.bossbar.get.visible.hidden": "jan li ken ala lukin e ijo pi lukin moli pi nimi %s", "commands.bossbar.get.visible.visible": "jan li ken lukin e ijo pi lukin moli pi nimi %s", "commands.bossbar.list.bars.none": "ijo pi lukin moli li lon ala", "commands.bossbar.list.bars.some": "ijo pi lukin moli %s li lon li ni: %s", "commands.bossbar.remove.success": "mi weka e ijo pi lukin moli pi nimi %s", "commands.bossbar.set.color.success": "ijo pi lukin moli pi nimi %s la sina ante e kule", "commands.bossbar.set.color.unchanged": "mi ante e ala. kule ni li awen kule pi ijo ni pi lukin moli", "commands.bossbar.set.max.success": "ijo pi lukin moli pi nimi %s la nanpa suli li kama %s", "commands.bossbar.set.max.unchanged": "mi ante e ala. nanpa ni li awen nanpa suli pi ijo ni pi lukin moli", "commands.bossbar.set.name.success": "ijo pi lukin moli pi nimi %s la sina ante e nimi", "commands.bossbar.set.name.unchanged": "mi ante e ala. nimi ni li awen nimi pi ijo ni pi lukin moli", "commands.bossbar.set.players.success.none": "ijo pi lukin moli pi nimi %s la jan ala li lon", "commands.bossbar.set.players.success.some": "ijo pi lukin moli pi nimi %s la jan %s li lon li %s", "commands.bossbar.set.players.unchanged": "mi ante e ala. jan ni li awen lon ijo ni pi lukin moli", "commands.bossbar.set.style.success": "ijo pi lukin moli pi nimi %s la sina ante e lukin", "commands.bossbar.set.style.unchanged": "mi ante e ala. lukin ni li awen lukin pi ijo ni pi lukin moli", "commands.bossbar.set.value.success": "ijo pi lukin moli pi nimi %s la nanpa ona li kama %s", "commands.bossbar.set.value.unchanged": "mi ante e ala. nanpa ni li awen nanpa pi ijo ni pi lukin moli", "commands.bossbar.set.visibility.unchanged.hidden": "mi ante e ala. awen la jan li ken ala lukin e ijo ni pi lukin moli", "commands.bossbar.set.visibility.unchanged.visible": "mi ante e ala. awen la jan li ken lukin e ijo ni pi lukin moli", "commands.bossbar.set.visible.success.hidden": "jan li ken ala lukin e ijo pi lukin moli pi nimi %s", "commands.bossbar.set.visible.success.visible": "jan li ken lukin e ijo pi lukin moli pi nimi %s", "commands.bossbar.unknown": "ijo ala pi lukin moli li kepeken nimi \"%s\"", "commands.clear.success.multiple": "mi weka e ijo %s tan jan %s", "commands.clear.success.single": "mi weka e ijo %s tan jan %s", "commands.clear.test.multiple": "mi lukin e ijo sama %s lon jan %s", "commands.clear.test.single": "mi lukin e ijo sama %s lon jan %s", "commands.clone.failed": "mi tu ala e ma", "commands.clone.overlap": "mi ken ala tu e ma tan ni: ma tu li lon sama ma wan", "commands.clone.success": "mi pali sama e leko %s", "commands.clone.toobig": "sina pana e ma pi suli ike (nanpa suli li %s, sina wile e ona %s)", "commands.damage.invulnerable": "pakala ni li ken ala pakala e ijo ni", "commands.damage.success": "mi pana e pakala %s tawa %s", "commands.data.block.get": "suli pi mute %5$s la sona %1$s lon leko pi ma %2$s, %3$s, %4$s li %5$s", "commands.data.block.invalid": "ijo ni li leko ijo ala", "commands.data.block.modified": "mi ante e sona leko lon ma %s, %s, %s", "commands.data.block.query": "leko lon ma %s, %s, %s la sona leko ni li lon: %s", "commands.data.entity.get": "suli pi mute %3$s la sona %1$s lon %2$s li %4$s", "commands.data.entity.invalid": "mi ken ala ante e nanpa sona jan", "commands.data.entity.modified": "mi ante e sona %s", "commands.data.entity.query": "ijo %s la sona ni li lon: %s", "commands.data.get.invalid": "mi ken ala kama jo e %s. nimi lili ni o nanpa", "commands.data.get.multiple": "sina pana ike e ijo mute. o pana e ijo wan taso", "commands.data.get.unknown": "mi ken ala kama jo e %s; ona li lon ala", "commands.data.merge.failed": "mi ante e ala. sina wile e ijo sama ijo lon", "commands.data.modify.expected_list": "mi wile e lipu. ni li ike: %s", "commands.data.modify.expected_object": "mi wile e ijo. ni li ike: %s", "commands.data.modify.expected_value": "mi wile e nanpa. ni li ike: %s", "commands.data.modify.invalid_index": "nanpa ijo lipu ike: %s", "commands.data.modify.invalid_substring": "nanpa ma ni pi kipisi pi linja sitelen li ike: ona li tan %s tawa %s", "commands.data.storage.get": "suli pi mute %3$s la %1$s lon poki %2$s li %4$s", "commands.data.storage.modified": "mi ante e poki %s", "commands.data.storage.query": "poki %s li jo e ni: %s", "commands.datapack.create.already_exists": "poki ante li kepeken nimi '%s'", "commands.datapack.create.invalid_full_name": "nimi '%s' pi poki sin li ken ala lon", "commands.datapack.create.invalid_name": "sitelen ike li lon nimi '%s' pi poki sin", "commands.datapack.create.io_failure": "mi ken ala pali e poki kepeken nimi '%s'. o lukin e lipu", "commands.datapack.create.metadata_encode_failure": "mi ken ala ante pi awen sona e sona sona tawa poki pi nimi '%s': %s", "commands.datapack.create.success": "mi pali e poki sin pi nimi '%s'", "commands.datapack.disable.failed": "sina kepeken ala toki wawa \"%s\"!", "commands.datapack.disable.failed.feature": "mi ken ala kepeken ala poki '%s'. ni li ken e ijo!", "commands.datapack.enable.failed": "sina awen kepeken toki wawa \"%s\"!", "commands.datapack.enable.failed.no_flags": "poki '%s' li ken ala tan ni: ma li ken ala e ijo ni: %s!", "commands.datapack.list.available.none": "ante ala pi nasin musi li lon", "commands.datapack.list.available.success": "ante musi %s li lon li ni: %s", "commands.datapack.list.enabled.none": "sina kepeken toki wawa ala", "commands.datapack.list.enabled.success": "sina kepeken ante musi %s. ona li ni: %s", "commands.datapack.modify.disable": "mi weka e toki wawa %s", "commands.datapack.modify.enable": "mi ken e toki wawa %s", "commands.datapack.unknown": "mi sona ala e toki wawa \"%s\"", "commands.debug.alreadyRunning": "mi awen alasa e sona nanpa", "commands.debug.function.noRecursion": "mi ken ala lukin e nasin pi toki wawa lon insa pi pali wawa", "commands.debug.function.noReturnRun": "mi ken ala lukin e nasin pi toki wawa kepeken toki wawa \"return run\"", "commands.debug.function.success.multiple": "mi pana e nasin pi toki wawa %s tan pali wawa %s tawa lipu %s", "commands.debug.function.success.single": "mi pana e nasin pi toki wawa %s tan pali wawa '%s' tawa lipu %s", "commands.debug.function.traceFailed": "mi ken ala lukin e nasin pi toki wawa", "commands.debug.notRunning": "alasa pi sona nanpa li open ala", "commands.debug.started": "alasa pi sona nanpa li open", "commands.debug.stopped": "alasa pi sona nanpa li kepeken tenpo lili %s e tenpo musi lili %s (tenpo lili wan la tenpo musi lili %s li lon)", "commands.defaultgamemode.success": "tenpo ni la nasin musi pi ante ala li %s", "commands.deop.failed": "mi ante e ala. jan ni li jan lawa ala", "commands.deop.success": "%s li kama jan lawa ala", "commands.dialog.clear.multiple": "mi weka e toki tawa jan %s", "commands.dialog.clear.single": "mi weka e toki tawa %s", "commands.dialog.show.multiple": "mi pana e toki tawa jan %s", "commands.dialog.show.single": "mi pana e toki tawa %s", "commands.difficulty.failure": "mi ante ala e suli ike tan ni: suli ike li awen %s", "commands.difficulty.query": "suli ike li %s", "commands.difficulty.success": "suli ike li %s", "commands.drop.no_held_items": "ijo ni li ken ala jo e ijo", "commands.drop.no_loot_table": "ijo %s li jo ala e lipu alasa", "commands.drop.no_loot_table.block": "leko %s li jo ala e lipu alasa", "commands.drop.success.multiple": "sina weka jo e ijo %s", "commands.drop.success.multiple_with_table": "sina weka jo e ijo %s tan lipu alasa %s", "commands.drop.success.single": "sina weka jo e %s %s", "commands.drop.success.single_with_table": "sina weka jo e ijo %s %s tan lipu alasa %s", "commands.effect.clear.everything.failed": "jan ni la ona li lon ala", "commands.effect.clear.everything.success.multiple": "mi weka e pilin ale tan ijo %s", "commands.effect.clear.everything.success.single": "mi weka e pilin ale tan %s", "commands.effect.clear.specific.failed": "jan ni la ni li lon ala", "commands.effect.clear.specific.success.multiple": "mi weka e %s tan ijo %s", "commands.effect.clear.specific.success.single": "mi weka e %s tan %s", "commands.effect.give.failed": "sina ken ala pana e ni tawa jan ni (ona li awen tan ni, anu ona li awen jo e ona wawa)", "commands.effect.give.success.multiple": "mi pana e %s tawa ijo %s", "commands.effect.give.success.single": "mi pana e %s tawa ijo %s", "commands.enchant.failed": "mi ante e ala. ken la ona li jo ala e ijo lon luka ona. ken la ijo ni li ken ala kepeken wawa ni", "commands.enchant.failed.entity": "ijo %s li ike tawa toki wawa ni", "commands.enchant.failed.incompatible": "ijo %s li ken ala kepeken wawa ni", "commands.enchant.failed.itemless": "%s li jo ala e ijo", "commands.enchant.failed.level": "wawa ni li ken kepeken wawa %2$s taso. sina wile pana e wawa %1$s tawa ona a", "commands.enchant.success.multiple": "mi pana e wawa %s tawa ijo %s", "commands.enchant.success.single": "mi pana e wawa %s tawa ijo pi jan %s", "commands.execute.blocks.toobig": "sina pana e ma pi suli ike (nanpa suli li %s, sina wile e ona %s)", "commands.execute.conditional.fail": "pali li pini ike", "commands.execute.conditional.fail_count": "pali li pini ike. mute li %s", "commands.execute.conditional.pass": "pali li pini pona", "commands.execute.conditional.pass_count": "pali li pini pona. mute li %s", "commands.execute.function.instantiationFailure": "mi open ala e pali %s: %s", "commands.experience.add.levels.success.multiple": "mi pana e wawa laso suli %s tawa jan musi %s", "commands.experience.add.levels.success.single": "mi pana e wawa laso suli %s tawa %s", "commands.experience.add.points.success.multiple": "mi pana e wawa laso %s tawa jan musi %s", "commands.experience.add.points.success.single": "mi pana e wawa laso %s tawa %s", "commands.experience.query.levels": "jan %s li wawa laso suli pi mute %s", "commands.experience.query.points": "jan %s li wawa laso pi mute %s", "commands.experience.set.levels.success.multiple": "wawa laso %s li lon jan %s", "commands.experience.set.levels.success.single": "wawa laso %s li lon %s", "commands.experience.set.points.invalid": "mi pana ala e wawa laso tan ni: wawa ni li mute ike", "commands.experience.set.points.success.multiple": "wawa laso %s li lon jan %s", "commands.experience.set.points.success.single": "wawa laso %s li lon %s", "commands.fill.failed": "mi pali ala", "commands.fill.success": "mi pali e leko %s", "commands.fill.toobig": "sina pana e ma pi suli ike (nanpa suli li %s, sina wile e ona %s)", "commands.fillbiome.success": "ma lon insa pi %s, %s, %s pi %s, %s, %s li ante", "commands.fillbiome.success.count": "nanpa %s tan nasin ma li lon insa ni: %s, %s, %s en %s, %s, %s", "commands.fillbiome.toobig": "sina pana e ma pi suli ike (nanpa suli li %s, sina wile e ona %s)", "commands.forceload.added.failure": "mi pana e ma ala tawa lon wile", "commands.forceload.added.multiple": "mi pana e kipisi ma %s lon %s tan ma %s tawa ma %s tawa lon wile", "commands.forceload.added.none": "mi lukin ala e kipisi ma wile lon ma %s", "commands.forceload.added.single": "mi pana e kipisi ma %s lon %s tawa lon wile", "commands.forceload.list.multiple": "mi lukin e kipisi ma wile %s lon ma %s lon %s", "commands.forceload.list.single": "mi lukin e kipisi ma wile lon ma %s lon %s", "commands.forceload.query.failure": "kipisi ma lon %s pi ma %s li lon wile ala", "commands.forceload.query.success": "mi pana e kipisi ma lon %s pi ma %s tawa lon wile", "commands.forceload.removed.all": "mi weka e kipisi ma ale lon %s tan lon wile", "commands.forceload.removed.failure": "mi weka e kipisi ma ala tan lon wile", "commands.forceload.removed.multiple": "mi weka e kipisi ma %s lon %s tan %s tawa %s tan lon wile", "commands.forceload.removed.single": "mi weka e kipisi ma %s lon %s tan lon wile", "commands.forceload.toobig": "mi lukin e kipisi ma pi mute ike (nanpa suli li %s, taso sina wile e ona %s)", "commands.function.error.argument_not_compound": "sona %s li ike. mi wile e sona kepeken nimi sona", "commands.function.error.missing_argument": "sona %2$s li weka tawa pali %1$s", "commands.function.error.missing_arguments": "sona li weka tawa pali %s", "commands.function.error.parse": "mi open e toki wawa %s pi ken ante la, toki wawa '%s' li kama e pakala: %s", "commands.function.instantiationFailure": "mi open ala e pali %s: %s", "commands.function.result": "pali %s li toki e ni: %s", "commands.function.scheduled.multiple": "pali ni li lon li pali: %s", "commands.function.scheduled.no_functions": "mi ken ala alasa e pali pi nimi %s", "commands.function.scheduled.single": "pali %s li lon li pali", "commands.function.success.multiple": "mi pali e toki wawa %s tan poki toki %s", "commands.function.success.multiple.result": "mi pali e ijo %s", "commands.function.success.single": "mi pali e toki wawa %s tan ijo %s", "commands.function.success.single.result": "poki '%2$s' li toki e ni: %1$s", "commands.gamemode.success.other": "mi ante e nasin musi pi jan %s tawa %s", "commands.gamemode.success.self": "mi ante e nasin musi sina tawa %s", "commands.gamerule.query": "nasin musi %s li %s", "commands.gamerule.set": "nasin musi %s li %s", "commands.give.failed.toomanyitems": "mi ken ala pana e %2$s %1$s anu mute suli", "commands.give.success.multiple": "mi pana e %2$s %1$s tawa jan %3$s", "commands.give.success.single": "mi pana e %2$s %1$s tawa jan %s", "commands.help.failed": "mi sona ala e toki wawa ni. ken la sina ken ala toki e toki wawa ni", "commands.item.block.set.success": "mi ante e ijo lon poki jo lon %s, %s, %s tawa %s", "commands.item.entity.set.success.multiple": "mi ante e poki jo lon jan %s tawa %s", "commands.item.entity.set.success.single": "mi ante e ijo lon poki jo lon %s tawa %s", "commands.item.source.no_such_slot": "jan ni li jo ala e poki jo nanpa %s", "commands.item.source.not_a_container": "lon nanpa %s, %s, %s li poki ala", "commands.item.target.no_changed.known_item": "%s li ken ala tawa poki %s", "commands.item.target.no_changes": "ijo li ken ala tawa poki %s", "commands.item.target.no_such_slot": "jan ni li jo ala e poki jo nanpa %s", "commands.item.target.not_a_container": "lon nanpa %s, %s, %s la poki li lon ala", "commands.jfr.dump.failed": "mi ken ala pana e sona pi alasa JFR ni: %s", "commands.jfr.start.failed": "mi ken ala open e alasa JFR", "commands.jfr.started": "mi open e alasa JFR", "commands.jfr.stopped": "alasa JFR li pini li pana tawa %s", "commands.kick.owner.failed": "sina ken ala weka e jan lawa pi musi LAN", "commands.kick.singleplayer.failed": "mi ken ala weka e jan lon musi pi jan wan", "commands.kick.success": "mi weka e jan %s: %s", "commands.kill.success.multiple": "mi moli e ijo %s", "commands.kill.success.single": "mi moli e %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "jan %s li lon. jan %s li ken lon. jan ni li lon: %s", "commands.locate.biome.not_found": "ma %s li lon poka ala", "commands.locate.biome.success": "%s poka li lon %s (leko %s lon weka)", "commands.locate.poi.not_found": "mi ken ala alasa e ijo \"%s\" lon poka sina", "commands.locate.poi.success": "%s poka li lon %s (leko %s lon weka)", "commands.locate.structure.invalid": "ijo \"%s\" li lon ala", "commands.locate.structure.not_found": "ijo \"%s\" ala li lon poka", "commands.locate.structure.success": "%s li lon poka sina la ona li lon ma %s (suli weka li %s)", "commands.message.display.incoming": "jan %s li toki lili tawa sina e ni: %s", "commands.message.display.outgoing": "sina toki lili tawa jan %s e ni: %s", "commands.op.failed": "mi ante e ala. awen la jan ni li jan lawa", "commands.op.success": "%s li kama jan lawa", "commands.pardon.failed": "mi ante e ala. awen la jan ni li ken kama", "commands.pardon.success": "sin la %s li ken kama", "commands.pardonip.failed": "mi ante e ala. awen la jan pi nimi IP ni li ken kama", "commands.pardonip.invalid": "nimi <PERSON> ni li ike", "commands.pardonip.success": "sin la nimi IP %s li ken kama", "commands.particle.failed": "jan ala li ken lukin e lili ni", "commands.particle.success": "mi lon e lili %s", "commands.perf.alreadyRunning": "mi awen alasa e sona nanpa", "commands.perf.notRunning": "alasa pi sona nanpa li open ala", "commands.perf.reportFailed": "mi pakala e pali pi lipu sona", "commands.perf.reportSaved": "mi pali e lipu sona lon %s", "commands.perf.started": "mi open alasa kepeken tenpo lili luka luka (o kepeken \"/perf stop\" tawa pini alasa lon tenpo lili)", "commands.perf.stopped": "tenpo lili %s en tenpo lili musi %s la mi pini alasa (tenpo lili musi %s li sama tenpo lili wan)", "commands.place.feature.failed": "mi ken ala pana e ijo", "commands.place.feature.invalid": "ijo \"%s\" li lon ala", "commands.place.feature.success": "mi pana e \"%s\" lon %s, %s, %s", "commands.place.jigsaw.failed": "mi ken ala pali e ijo poka", "commands.place.jigsaw.invalid": "kulupu ijo ala li jo e nimi \"%s\"", "commands.place.jigsaw.success": "mi pana e ijo poka lon %s, %s, %s", "commands.place.structure.failed": "mi ken ala pana e tomo", "commands.place.structure.invalid": "ijo \"%s\" li lon ala", "commands.place.structure.success": "mi kama e ijo \"%s\" lon %s, %s, %s", "commands.place.template.failed": "mi ken ala pana e ijo", "commands.place.template.invalid": "tomo ala li jo e nimi \"%s\"", "commands.place.template.success": "mi lon e ijo \"%s\" lon %s, %s, %s", "commands.playsound.failed": "jan ala li ken kute e kalama ni", "commands.playsound.success.multiple": "mi kalama e %s lon jan %s", "commands.playsound.success.single": "mi kalama e %s lon jan %s", "commands.publish.alreadyPublished": "musi kulupu li awen lon poki ilo %s", "commands.publish.failed": "mi ken ala lawa e musi pi jan poka", "commands.publish.started": "musi pi jan poka li lon poki ilo %s", "commands.publish.success": "musi kulupu li lon poki ilo %s", "commands.random.error.range_too_large": "suli pi nasin ala li wile lili tawa nanpa 2147483646 anu sama ona", "commands.random.error.range_too_small": "suli pi nasin ala li wile suli tawa nanpa 2 anu sama ona", "commands.random.reset.all.success": "mi sin e kulupu %s pi nasin ala", "commands.random.reset.success": "mi sin e kulupu %s pi nasin ala", "commands.random.roll": "%s li kama e %s (tan %s tawa %s)", "commands.random.sample.success": "nanpa nasa: %s", "commands.recipe.give.failed": "mi pana ala e nasin pali", "commands.recipe.give.success.multiple": "sina pana e nasin pali %s tawa jan %s", "commands.recipe.give.success.single": "sina pana e nasin pali %s tawa %s", "commands.recipe.take.failed": "mi weka ala e nasin pali", "commands.recipe.take.success.multiple": "sina weka e nasin pali %s tan jan %s", "commands.recipe.take.success.single": "sina weka e nasin pali %s tan %s", "commands.reload.failure": "mi ken ala sin e ni. mi awen e ijo pi tenpo pini", "commands.reload.success": "mi sin e ni!", "commands.ride.already_riding": "%s li lawa kin e ijo ante:  %s", "commands.ride.dismount.success": "%s li pini lawa e %s", "commands.ride.mount.failure.cant_ride_players": "ijo ala li ken lawa e jan musi", "commands.ride.mount.failure.generic": "%s li ken ala kama lawa e %s", "commands.ride.mount.failure.loop": "ijo li ken ala lawa e ona sama anu ijo anpa ona", "commands.ride.mount.failure.wrong_dimension": "ijo li ken ala lawa e ijo lon ma ante", "commands.ride.mount.success": "%s li kama lawa e %s", "commands.ride.not_riding": "%s li lawa e ijo tawa ala", "commands.rotate.success": "mi tawa sike e %s", "commands.save.alreadyOff": "mi wile ala awen e musi", "commands.save.alreadyOn": "mi wile awen e musi", "commands.save.disabled": "mi wile ala awen e musi lon tenpo ale", "commands.save.enabled": "mi wile awen e musi lon tenpo ale", "commands.save.failed": "mi ken ala awen e musi (sina jo ala jo e poki lon ilo sona?)", "commands.save.saving": "mi awen e musi (o awen lon tenpo lili!)", "commands.save.success": "musi li awen", "commands.schedule.cleared.failure": "mi lukin ala e wile pi nimi %s", "commands.schedule.cleared.success": "mi weka e wile %s pi nimi %s", "commands.schedule.created.function": "tenpo lili %s la mi wile pali e \"%s\" lon tenpo musi %s", "commands.schedule.created.tag": "tenpo lili musi %s la mi wile pali e \"%s\" lon tenpo musi %s", "commands.schedule.macro": "mi ken ala tenpo e toki wawa pi ken ante", "commands.schedule.same_tick": "mi ken ala pana e nasin tawa tenpo lili musi ni", "commands.scoreboard.objectives.add.duplicate": "wile pi nimi ni li lon", "commands.scoreboard.objectives.add.success": "mi pali e nasin sin %s", "commands.scoreboard.objectives.display.alreadyEmpty": "mi ante e ala. awen la poki ni li jo e ala", "commands.scoreboard.objectives.display.alreadySet": "mi ante e ala. poki ni li awen jo e ijo ni", "commands.scoreboard.objectives.display.cleared": "mi weka e nasin ale lon poki %s", "commands.scoreboard.objectives.display.set": "poki %s la nasin %s li lon", "commands.scoreboard.objectives.list.empty": "ona ala li lon", "commands.scoreboard.objectives.list.success": "nasin %s li lon: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "mi ken ala e pana lukin sin pi nasin %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "mi ken e pana lukin sin pi nasin %s", "commands.scoreboard.objectives.modify.displayname": "mi ante e nimi lukin %s tawa %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "nasin %s la, mi weka e nasin nanpa pi ante ala", "commands.scoreboard.objectives.modify.objectiveFormat.set": "nasin %s la, mi ante e nasin nanpa pi ante ala", "commands.scoreboard.objectives.modify.rendertype": "mi ante e nasin ilo sitelen pi nasin %s", "commands.scoreboard.objectives.remove.success": "mi weka e nasin %s", "commands.scoreboard.players.add.success.multiple": "ijo %3$s la mi pana e %1$s tawa %2$s", "commands.scoreboard.players.add.success.single": "%3$s la mi pana e %1$s tawa %2$s (ona li kama %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "mi weka e nimi lukin tawa ijo %s lon %s", "commands.scoreboard.players.display.name.clear.success.single": "mi weka e nimi lukin tawa %s lon %s", "commands.scoreboard.players.display.name.set.success.multiple": "mi kama nimi %s e nimi lukin pi ijo %s lon %s", "commands.scoreboard.players.display.name.set.success.single": "mi kama nimi %s e nimi lukin pi ijo %s lon %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "mi weka e nasin nanpa tawa ijo %s lon %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "mi weka e nasin nanpa tawa %s lon %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "mi ante e nasin nanpa tawa ijo %s lon %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "mi ante e nasin nanpa tawa %s lon %s", "commands.scoreboard.players.enable.failed": "mi ante e ala. nasin open ni li awen lon", "commands.scoreboard.players.enable.invalid": "wile li nasin open la sina ken lon e ni", "commands.scoreboard.players.enable.success.multiple": "ijo %2$s li ken kepeken %1$s", "commands.scoreboard.players.enable.success.single": "%2$s li ken kepeken %1$s", "commands.scoreboard.players.get.null": "mi ken ala kama jo e %s tawa %s. ala li lon", "commands.scoreboard.players.get.success": "%1$s li jo e %3$s %2$s", "commands.scoreboard.players.list.empty": "mi lukin e ijo ala", "commands.scoreboard.players.list.entity.empty": "ijo %s la nanpa li lon ala", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s la nanpa %s li lon li ni:", "commands.scoreboard.players.list.success": "mi lukin e ijo %s. ona li ni: %s", "commands.scoreboard.players.operation.success.multiple": "ijo %s la mi sin e %s", "commands.scoreboard.players.operation.success.single": "o awen e %s pi %s, tawa %s", "commands.scoreboard.players.remove.success.multiple": "ijo %3$s la mi weka e %1$s tan %2$s", "commands.scoreboard.players.remove.success.single": "%3$s la mi weka e %1$s tan %2$s (ona li kama %4$s)", "commands.scoreboard.players.reset.all.multiple": "ijo %s la mi ante e nanpa ale ona tawa sin", "commands.scoreboard.players.reset.all.single": "%s la mi ante e nanpa ale ona tawa sin", "commands.scoreboard.players.reset.specific.multiple": "ijo %2$s la mi ante e %$1s tawa sin", "commands.scoreboard.players.reset.specific.single": "%2$s la mi ante e %1$s tawa sin", "commands.scoreboard.players.set.success.multiple": "ijo $2$s la mi ante e %1$s tawa %3$s", "commands.scoreboard.players.set.success.single": "jan $2$s la mi ante e %1$s tawa %3$s", "commands.seed.success": "nimi ma wawa: %s", "commands.setblock.failed": "mi ken ala pali e ni", "commands.setblock.success": "sina ante e ijo lon ma %s, %s, %s", "commands.setidletimeout.success": "jan li tawa ala lon tenpo %s la, mi weka e ona", "commands.setidletimeout.success.disabled": "mi weka ala e jan pi tawa ala", "commands.setworldspawn.failure.not_overworld": "ma pi open jan li ken lon ma pona taso", "commands.setworldspawn.success": "nanpa open pi ma ale li kama %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "nanpa pi ma open li kama %s, %s, %s [%s] lon ma %s tawa jan %s", "commands.spawnpoint.success.single": "nanpa pi ma open li kama %s, %s, %s [%s] lon ma %s tawa %s", "commands.spectate.not_spectator": "%s li jan lukin ala", "commands.spectate.self": "sina ken ala lukin e sina", "commands.spectate.success.started": "sina lukin e %s", "commands.spectate.success.stopped": "sina lukin ala e ijo", "commands.spreadplayers.failed.entities": "mi tawa e ijo %s tawa ma ante mute lon poka pi ma %s, %s. (ma li lili a la ijo li ken ala lon ona. o kepeken nanpa weka %s)", "commands.spreadplayers.failed.invalid.height": "nanpa pi sewi suli mute %s li ike. nanpa pi ken anpa %s la mi wile e nanpa pi ken sewi suli", "commands.spreadplayers.failed.teams": "mi tawa e kulupu jan %s tawa ma ante mute lon poka pi ma %s, %s. (ma li lili a la ijo li ken ala lon ona. o kepeken nanpa weka %s)", "commands.spreadplayers.success.entities": "mi tawa e jan %s tawa ma ante mute lon poka pi ma %s, %s. jan ante la jan li lon poka pi suli %s", "commands.spreadplayers.success.teams": "mi tawa e kulupu jan %s tawa ma ante mute lon poka pi ma %s, %s. jan ante la jan li lon poka pi suli %s", "commands.stop.stopping": "mi pini e ma kulupu", "commands.stopsound.success.source.any": "kalama \"%s\" ale li pini", "commands.stopsound.success.source.sound": "kalama \"%s\" tan \"%s\" li pini", "commands.stopsound.success.sourceless.any": "kalama ale li pini", "commands.stopsound.success.sourceless.sound": "kalama \"%s\" li pini", "commands.summon.failed": "mi ken ala kama e ni", "commands.summon.failed.uuid": "mi ken ala kama e ona tan ni: nanpa ijo UUID li sama nanpa UUID pi ijo ante", "commands.summon.invalidPosition": "mi ken ala lon. ma ni li ike", "commands.summon.success": "%s sin li kama lon", "commands.tag.add.failed": "ijo ni li jo e nimi lili ni anu nimi lili mute", "commands.tag.add.success.multiple": "sina pana e nimi lili \"%s\" tawa ijo %s", "commands.tag.add.success.single": "sina pana e nimi lili \"%s\" tawa %s", "commands.tag.list.multiple.empty": "ijo %s la nimi lili li lon ala", "commands.tag.list.multiple.success": "ijo %s la nimi lili %s li lon li ni: %s", "commands.tag.list.single.empty": "%s la nimi lili ala li lon", "commands.tag.list.single.success": "ijo %s la nimi lili %s li lon li ni: %s", "commands.tag.remove.failed": "ijo ni li jo ala e nimi lili ni", "commands.tag.remove.success.multiple": "sina weka e nimi lili \"%s\" tan ijo %s", "commands.tag.remove.success.single": "sina weka e nimi lili \"%s\" tan %s", "commands.team.add.duplicate": "kulupu ante li kepeken nimi ni", "commands.team.add.success": "mi pali e kulupu %s", "commands.team.empty.success": "mi weka e jan %s tan kulupu %s", "commands.team.empty.unchanged": "mi ante e ala. kulupu ni la jan li lon ala", "commands.team.join.success.multiple": "mi pana e jan %s tawa kulupu %s", "commands.team.join.success.single": "mi pana e %s tawa kulupu %s", "commands.team.leave.success.multiple": "mi weka e jan %s tan kulupu ale", "commands.team.leave.success.single": "mi weka e %s tan kulupu ale", "commands.team.list.members.empty": "kulupu %s la jan li lon ala", "commands.team.list.members.success": "kulupu %s la jan %s li lon: %s", "commands.team.list.teams.empty": "kulupu ala li lon", "commands.team.list.teams.success": "kulupu %s li lon li ni: %s", "commands.team.option.collisionRule.success": "ken pi tawa jan pi kulupu %s li \"%s\"", "commands.team.option.collisionRule.unchanged": "mi ante e ala. awen la ken pi tawa jan li ni", "commands.team.option.color.success": "mi ante e kule pi kulupu %s tawa %s", "commands.team.option.color.unchanged": "mi ante e ala. kulupu ni la kule ni li awen lon", "commands.team.option.deathMessageVisibility.success": "ken pi toki moli pi kulupu %s li \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "mi ante e ala. awen la lukin pi toki moli li ni", "commands.team.option.friendlyfire.alreadyDisabled": "mi ante e ala. awen la jan pi kulupu ni li ken ala pakala e jan ante pi kulupu ni", "commands.team.option.friendlyfire.alreadyEnabled": "mi ante e ala. awen la jan pi kulupu ni li ken pakala e jan ante pi kulupu ni", "commands.team.option.friendlyfire.disabled": "jan pi kulupu %s li ken ala pakala e jan ante pi kulupu ni", "commands.team.option.friendlyfire.enabled": "jan pi kulupu %s li ken pakala e jan ante pi kulupu ni", "commands.team.option.name.success": "mi sin e nimi pi kulupu %s", "commands.team.option.name.unchanged": "mi ante e ala. kulupu ni la nimi ni li awen lon", "commands.team.option.nametagVisibility.success": "mute lukin nimi pi kulupu %s li \"%s\"", "commands.team.option.nametagVisibility.unchanged": "mi ante e ala. awen la lukin pi lipu nimi li ni", "commands.team.option.prefix.success": "nimi monsi kulupu li %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "mi ante e ala. awen la jan pi kulupu ni ken ala lukin e jan kon pi kulupu ona", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "mi ante e ala. awen la jan pi kulupu ni ken lukin e jan kon pi kulupu ona", "commands.team.option.seeFriendlyInvisibles.disabled": "jan pi kulupu %s li ken ala lukin e jan kon pi kulupu ona", "commands.team.option.seeFriendlyInvisibles.enabled": "jan pi kulupu %s li ken lukin e jan kon pi kulupu ona", "commands.team.option.suffix.success": "nimi sinpin kulupu li %s", "commands.team.remove.success": "mi weka e kulupu %s", "commands.teammsg.failed.noteam": "sina wile toki tawa kulupu sina la, o lon kulupu a", "commands.teleport.invalidPosition": "mi ken ala tawa e ijo. ma pi kama ona li ike", "commands.teleport.success.entity.multiple": "mi tawa e ijo %s tawa %s", "commands.teleport.success.entity.single": "mi tawa e %s tawa %s", "commands.teleport.success.location.multiple": "mi tawa e ijo %s tawa ma %s, %s, %s", "commands.teleport.success.location.single": "mi tawa e %s tawa ma %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "pali li lon ala tawa weka", "commands.test.clear.success": "mi weka e tomo %s", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "o pana e ni tawa poki sama", "commands.test.create.success": "mi pali e open pi alasa sona %s", "commands.test.error.no_test_containing_pos": "mi ken ala alasa e ijo ni pi alasa sona: ma %s, %s, %s li lon insa", "commands.test.error.no_test_instances": "mi ken ala lukin e pali poki", "commands.test.error.non_existant_test": "mi ken ala alasa e pali %s", "commands.test.error.structure_not_found": "mi ken ala lukin e tomo pali %s", "commands.test.error.test_instance_not_found": "ijo pi alasa sona la, mi ken ala alasa e ijo leko", "commands.test.error.test_instance_not_found.position": "ijo pi alasa sona li lon ma %s, %s, %s la, mi ken ala alasa e ijo leko", "commands.test.error.too_large": "mi wile e suli tomo ni: leko %s la, ona li lili lon nasin ale", "commands.test.locate.done": "alasa li pini, mi lukin e tomo %s", "commands.test.locate.found": "mi lukin e tomo lon: %s (weka: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "pali li lon ala tawa pali", "commands.test.relative_position": "ma %s la ma %s", "commands.test.reset.error.no_tests": "pali li lon ala tawa sin", "commands.test.reset.success": "mi sin e tomo %s", "commands.test.run.no_tests": "pali li lon ala", "commands.test.run.running": "mi pali e pali %s", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "pali suli ale li pona :)", "commands.test.summary.failed": "pali suli %s li pakala :(", "commands.test.summary.optional_failed": "pali %s pi suli ala li pakala", "commands.tick.query.percentiles": "nanpa: 50: %sms 95: %sms 99: %sms, alasa: %s", "commands.tick.query.rate.running": "mi alasa wawa tawa %s\ntenpo pi kipisi tenpo: %sms (alasa: %sms)", "commands.tick.query.rate.sprinting": "mi alasa wawa tawa %s (mi ken ala ni, taso sona)\ntenpo pi kipisi tenpo: %sms", "commands.tick.rate.success": "mi ante e tenpo lili tawa %s", "commands.tick.sprint.report": "tawa wawa li pini. wawa tawa li tenpo lili %s lon tenpo suli anu tenpo lili lili %s lon tenpo lili", "commands.tick.sprint.stop.fail": "tawa wawa pi tenpo lili li lon ala", "commands.tick.sprint.stop.success": "tawa wawa pi tenpo lili li pini", "commands.tick.status.frozen": "musi li awen pi tawa ala", "commands.tick.status.lagging": "musi li tawa. taso mute pi tenpo lili lon wile sina la ilo musi li ken ala", "commands.tick.status.running": "musi li tawa pi nasa ala", "commands.tick.status.sprinting": "musi li tawa wawa", "commands.tick.step.fail": "mi ken ala tawa lili e musi - musi o awen pi tawa ala", "commands.tick.step.stop.fail": "kipisi pi tenpo lili wan li lon ala", "commands.tick.step.stop.success": "kipisi pi tenpo lili wan li pakala", "commands.tick.step.success": "mi tawa e musi lon tenpo lili %s", "commands.time.query": "tenpo ni li %s", "commands.time.set": "mi ante e tenpo tawa %s", "commands.title.cleared.multiple": "mi weka e nimi suli pi jan %s", "commands.title.cleared.single": "mi weka e nimi suli %s", "commands.title.reset.multiple": "mi weka e ante lon nimi suli pi jan %s", "commands.title.reset.single": "mi weka e ante lon nimi suli pi jan %s", "commands.title.show.actionbar.multiple": "mi pana e nimi lon sewi poki jo tawa jan %s", "commands.title.show.actionbar.single": "mi pana e nimi lon sewi poki jo tawa %s", "commands.title.show.subtitle.multiple": "mi pana e nimi lili tawa jan %s", "commands.title.show.subtitle.single": "mi pana e nimi lili tawa %s", "commands.title.show.title.multiple": "mi pana e nimi suli sin tawa jan %s", "commands.title.show.title.single": "mi pana e nimi suli sin tawa %s", "commands.title.times.multiple": "jan %s la mi ante e tenpo lukin pi nimi suli", "commands.title.times.single": "%s la mi ante e tenpo lukin pi nimi suli", "commands.transfer.error.no_players": "tawa la mi wile e jan", "commands.transfer.success.multiple": "mi tawa e jan %s tawa %s:%s", "commands.transfer.success.single": "mi tawa e %s tawa %s:%s", "commands.trigger.add.success": "%s li open la mi pana e %s tawa nanpa", "commands.trigger.failed.invalid": "wile li nasin open la sina ken kama open e ni", "commands.trigger.failed.unprimed": "tenpo ni la sina ken ala open e ni", "commands.trigger.set.success": "%s li open la nanpa li kama %s", "commands.trigger.simple.success": "%s li open", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "nanpa musi pi ma kulupu li ni:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "sitelen ma ala li lon %s", "commands.waypoint.list.success": "sitelen ma %s li lon %s: %s", "commands.waypoint.modify.color": "sitelen ma li kama kule %s", "commands.waypoint.modify.color.reset": "sitelen ma li kama kule open", "commands.waypoint.modify.style": "nasin pi sitelen ma li kama ante", "commands.weather.set.clear": "sewi li pana ala e telo", "commands.weather.set.rain": "sewi li pana e telo", "commands.weather.set.thunder": "sewi li pana e telo e linja suno", "commands.whitelist.add.failed": "awen la jan ni li jan pona", "commands.whitelist.add.success": "mi pana e jan %s tawa lipu pi jan pona", "commands.whitelist.alreadyOff": "awen la lipu pi jan pona li lon ala", "commands.whitelist.alreadyOn": "awen la lipu pi jan pona li lon", "commands.whitelist.disabled": "lipu pi jan pona li lon ala", "commands.whitelist.enabled": "lipu pi jan pona li lon", "commands.whitelist.list": "lipu pi jan pona li jo e jan %s: %s", "commands.whitelist.none": "lipu pi jan pona li jo ala e jan", "commands.whitelist.reloaded": "lipu pi jan pona li kama sin", "commands.whitelist.remove.failed": "awen la jan ni li jan pona ala", "commands.whitelist.remove.success": "mi weka e jan %s tan lipu pi jan pona", "commands.worldborder.center.failed": "mi ante e ala. awen la selo ma li lon ni", "commands.worldborder.center.success": "selo ma li kama lon %s, %s", "commands.worldborder.damage.amount.failed": "mi ante e ala. jan lon insa ala selo ma la ona o pakala pi mute ni", "commands.worldborder.damage.amount.success": "jan li weka tan selo ma la ona li pakala pi mute %s lon tenpo lili wan", "commands.worldborder.damage.buffer.failed": "mi ante e ala. jan lon poka ni ala selo ma la ona o pakala", "commands.worldborder.damage.buffer.success": "jan li weka tan selo ma pi mute %s la ona li pakala", "commands.worldborder.get": "selo ma li suli ni: leko %s", "commands.worldborder.set.failed.big": "mi ante e ala tan ni: selo ma li suli ike a", "commands.worldborder.set.failed.far": "selo ma li ken ala lon sewi pi leko %s", "commands.worldborder.set.failed.nochange": "mi ante e ala. awen la selo ma li suli ni", "commands.worldborder.set.failed.small": "suli pi selo ma li ken ala lon anpa leko wan", "commands.worldborder.set.grow": "mi suli e selo ma tawa suli pi leko %s lon tenpo lili %s", "commands.worldborder.set.immediate": "selo ma li kama suli ni: leko %s", "commands.worldborder.set.shrink": "mi lili e selo ma tawa suli pi leko %s lon tenpo lili %s", "commands.worldborder.warning.distance.failed": "mi ante e ala. jan lon poka ni ala selo ma la mi toki tawa ona", "commands.worldborder.warning.distance.success": "jan li weka tan selo ma lon leko %s la mi toki tawa ona", "commands.worldborder.warning.time.failed": "mi ante e ala. jan lon insa ala selo ma lon tenpo ni la mi toki tawa ona", "commands.worldborder.warning.time.success": "jan li weka tan selo ma lon tenpo lili %s la mi toki tawa ona", "compliance.playtime.greaterThan24Hours": "sike suno wa li pini la sina awen musi", "compliance.playtime.hours": "tenpo %s la sina musi", "compliance.playtime.message": "musi pi tenpo suli li ken ike e ali sina", "connect.aborted": "mi pini", "connect.authorizing": "ma kulupu li kama sona e nimi sina...", "connect.connecting": "mi kama jo e sona nanpa...", "connect.encrypting": "mi lon e sona nanpa...", "connect.failed": "tawa ma li pakala ike", "connect.failed.transfer": "sina tawa ma kulupu sin la, toki li pakala", "connect.joining": "sina tawa ma...", "connect.negotiating": "mi toki tawa ma kulupu...", "connect.reconfiging": "ante...", "connect.reconfiguring": "ante...", "connect.transferring": "mi tawa e sina tawa ma kulupu sin...", "container.barrel": "poki sike", "container.beacon": "ilo wawa", "container.beehive.bees": "pipi suwi: %s / %s", "container.beehive.honey": "suwi: %s / %s", "container.blast_furnace": "poki seli kiwen", "container.brewing": "ilo pi telo wawa", "container.cartography_table": "supa pi sitelen ma", "container.chest": "poki", "container.chestDouble": "poki suli", "container.crafter": "ilo pali", "container.crafting": "pali", "container.creative": "o jo e ijo", "container.dispenser": "poki pi pana wawa", "container.dropper": "poki pana", "container.enchant": "supa wawa", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "kiwen %s pi laso telo", "container.enchant.lapis.one": "kiwen wan pi laso telo", "container.enchant.level.many": "wawa %s", "container.enchant.level.one": "wawa wan", "container.enchant.level.requirement": "ona wile e wawa ni: %s", "container.enderchest": "poki pi ma End", "container.furnace": "poki seli", "container.grindstone_title": "kama pona ilo en weka wawa", "container.hopper": "poki pi tawa ijo", "container.inventory": "jo sina", "container.isLocked": "%s li ken ala open tan awen!", "container.lectern": "supa lipu", "container.loom": "ilo len", "container.repair": "o pona o nimi", "container.repair.cost": "ni li wile e wawa %1$s", "container.repair.expensive": "ni li wawa mute a!", "container.shulkerBox": "poki pi mon<PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "ijo %s kin...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "poki seli moku", "container.spectatorCantOpen": "sina ken ala open tan ni: ijo li lon ala.", "container.stonecutter": "sike pi kipisi kiwen", "container.upgrade": "o sewi e len e ilo", "container.upgrade.error_tooltip": "ijo li ken ala kama wawa sama ni", "container.upgrade.missing_template_tooltip": "o pana e lipu pi namako len", "controls.keybinds": "kepeken pi ilo nena...", "controls.keybinds.duplicateKeybinds": "nena ni li kepeken kin tawa ni:\n%s", "controls.keybinds.title": "kepeken pi ilo nena", "controls.reset": "o ante ala", "controls.resetAll": "o weka e ante", "controls.title": "nasin lawa", "createWorld.customize.buffet.biome": "sina wile e ma seme?", "createWorld.customize.buffet.title": "ma namako mute", "createWorld.customize.flat.height": "mute sewi", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "anpa - %s", "createWorld.customize.flat.layer.top": "sewi - %s", "createWorld.customize.flat.removeLayer": "o weka e ijo ni", "createWorld.customize.flat.tile": "ijo", "createWorld.customize.flat.title": "ilo ante pi ma supa suli", "createWorld.customize.presets": "ma namako", "createWorld.customize.presets.list": "ante la, mi pali e ni lon tenpo pini!", "createWorld.customize.presets.select": "o lon e ni", "createWorld.customize.presets.share": "sina wile pana e ma sama ni tawa jan ante la, o kepeken lipu lon anpa!", "createWorld.customize.presets.title": "sina wile e ma seme?", "createWorld.preparing": "mi open pali e ma...", "createWorld.tab.game.title": "musi", "createWorld.tab.more.title": "ijo ante", "createWorld.tab.world.title": "ma", "credits_and_attribution.button.attribution": "nimi pi jan pali ante", "credits_and_attribution.button.credits": "nimi pi jan pali", "credits_and_attribution.button.licenses": "ken kepeken", "credits_and_attribution.screen.title": "nimi pi jan pali", "dataPack.bundle.description": "ni li ken e ijo 'poki len'", "dataPack.bundle.name": "poki len", "dataPack.locator_bar.description": "sina musi lon poka pi jan ante la sina ken lukin e tawa pi jan ante", "dataPack.locator_bar.name": "ijo pi lukin lon", "dataPack.minecart_improvements.description": "tawa pona pi poki tawa", "dataPack.minecart_improvements.name": "pona pi poki tawa", "dataPack.redstone_experiments.description": "ante pini ala tawa ko Redstone", "dataPack.redstone_experiments.name": "ante pi pini ala pi ko Redstone", "dataPack.title": "o kepeken poki pi toki wawa", "dataPack.trade_rebalance.description": "mi ante e esun pi jan tomo", "dataPack.trade_rebalance.name": "jan esun ante pi nanpa musi sin", "dataPack.update_1_20.description": "ijo sin pi musi Manka nanpa 1.20", "dataPack.update_1_20.name": "nanpa musi 1.20", "dataPack.update_1_21.description": "ijo sin pi musi Manka nanpa 1.21", "dataPack.update_1_21.name": "nanpa musi 1.21", "dataPack.validation.back": "o tawa monsi", "dataPack.validation.failed": "ijo sina li ike!", "dataPack.validation.reset": "o weka e ante ale", "dataPack.validation.working": "mi alasa e pona pi ijo ni...", "dataPack.vanilla.description": "sona nanpa pi musi Manka pi ante ala", "dataPack.vanilla.name": "nasin pi ante ala", "dataPack.winter_drop.description": "ijo sin pi sin pi tenpo lete", "dataPack.winter_drop.name": "sin pi tenpo lete", "datapackFailure.safeMode": "o kepeken ala toki wawa", "datapackFailure.safeMode.failed.description": "nanpa sona pi ma ni li pakala.", "datapackFailure.safeMode.failed.title": "mi ken ala lon e ma kepeken toki wawa ala.", "datapackFailure.title": "mi ken ala open e ma tan ni: pakala li lon poki pi toki wawa. o weka e poki pakala o open sin e ma.", "death.attack.anvil": "supa pona li anpa lon %1$s li moli e ona", "death.attack.anvil.player": "%1$s li utala e %2$s li moli tan supa pona", "death.attack.arrow": "%2$s li moli e %1$s kepeken ilo alasa", "death.attack.arrow.item": "%2$s li moli e %1$s kepeken ilo alasa %3$s", "death.attack.badRespawnPoint.link": "nasin musi a", "death.attack.badRespawnPoint.message": "%2$s li moli e %1$s", "death.attack.cactus": "%1$s li moli tan kasi kipisi", "death.attack.cactus.player": "%1$s li alasa weka tan %2$s li moli tan kasi kipisi", "death.attack.cramming": "%1$s li moli tan kon ala", "death.attack.cramming.player": "%1$s li moli tan kon ala tan poka %2$s", "death.attack.dragonBreath": "kon pi akesi pini li seli e %1$s", "death.attack.dragonBreath.player": "%2$s li seli e %1$s kepeken kon pi akesi pini", "death.attack.drown": "%1$s li moli tan telo", "death.attack.drown.player": "%1$s li alasa weka tan %2$s li moli tan telo", "death.attack.dryout": "%1$s li moli tan telo ala", "death.attack.dryout.player": "%1$s li alasa weka tan %2$s li moli tan telo ala", "death.attack.even_more_magic": "%1$s li moli tan wawa namako", "death.attack.explosion": "%1$s li seli pakala", "death.attack.explosion.player": "%2$s li seli pakala e %1$s", "death.attack.explosion.player.item": "%2$s li seli pakala e %1$s kepeken %3$s", "death.attack.fall": "%1$s li tawa wawa anpa", "death.attack.fall.player": "%1$s li alasa weka tan %2$s li tawa wawa anpa", "death.attack.fallingBlock": "leko li anpa lon %1$s li moli e ona", "death.attack.fallingBlock.player": "%1$s li utala e %2$s li moli tan anpa leko", "death.attack.fallingStalactite": "palisa kiwen li moli e %1$s tan sewi", "death.attack.fallingStalactite.player": "%1$s li utala e %2$s li moli lupa tan palisa kiwen sewi", "death.attack.fireball": "%2$s li moli e %1$s kepeken sike seli", "death.attack.fireball.item": "%2$s li moli e %1$s kepeken sike seli tan %3$s", "death.attack.fireworks": "%1$s li seli wawa", "death.attack.fireworks.item": "%1$s li moli tan seli kule a! %3$s li pana e ona kepeken %2$s", "death.attack.fireworks.player": "%1$s li utala e %2$s li seli wawa", "death.attack.flyIntoWall": "%1$s li pilin e wawa tawa", "death.attack.flyIntoWall.player": "%1$s li alasa weka tan %2$s li pilin e wawa tawa", "death.attack.freeze": "%1$s li moli tan lete", "death.attack.freeze.player": "%2$s li moli lete e %1$s", "death.attack.generic": "%1$s li moli", "death.attack.generic.player": "%2$s li moli e %1$s", "death.attack.genericKill": "%1$s li moli", "death.attack.genericKill.player": "%1$s li utala e %2$s li kama moli", "death.attack.hotFloor": "kiwen seli li moli e %1$s", "death.attack.hotFloor.player": "noka %1$s li kama lon kiwen seli tan %2$s", "death.attack.inFire": "%1$s li moli seli", "death.attack.inFire.player": "%1$s li utala e %2$s li moli seli", "death.attack.inWall": "%1$s li moli tan kon ala", "death.attack.inWall.player": "%1$s li utala e %2$s li moli tan kon ala", "death.attack.indirectMagic": "%2$s li moli e %1$s kepeken wawa", "death.attack.indirectMagic.item": "%2$s li moli e %1$s kepeken %3$s", "death.attack.lava": "%1$s li tawa lon telo seli", "death.attack.lava.player": "%1$s li wile weka tan %2$s li moli lon telo seli", "death.attack.lightningBolt": "wawa sewi li moli e %1$s", "death.attack.lightningBolt.player": "%1$s li utala e %2$s li moli tan wawa sewi", "death.attack.mace_smash": "%1$s li moli tan pakala pi anpa wawa tan %2$s", "death.attack.mace_smash.item": "%1$s li moli tan pakala pi anpa wawa tan %2$s kepeken %3$s", "death.attack.magic": "wawa nasa li moli e %1$s", "death.attack.magic.player": "%1$s li alasa weka tan %2$s li moli tan wawa ", "death.attack.message_too_long": "moli sina li wile e nimi mute a! ni la mi lili e ona: %s", "death.attack.mob": "%2$s li moli e %1$s", "death.attack.mob.item": "%2$s li moli e %1$s kepeken %3$s", "death.attack.onFire": "%1$s li moli seli", "death.attack.onFire.item": "%1$s li utala e %2$s li moli tan seli. %2$s li kepeken %3$s", "death.attack.onFire.player": "%1$s li utala e %2$s li moli seli", "death.attack.outOfWorld": "%1$s li weka anpa tan ma", "death.attack.outOfWorld.player": "%1$s li utala e %2$s li weka anpa tan ma", "death.attack.outsideBorder": "%1$s li weka tan ma", "death.attack.outsideBorder.player": "%1$s li utala e %2$s li weka tan ma", "death.attack.player": "%2$s li moli e %1$s", "death.attack.player.item": "%2$s li moli e %1$s kepeken %3$s", "death.attack.sonic_boom": "kalama wawa li moli e %1$s", "death.attack.sonic_boom.item": "%1$s li alasa weka tan %2$s li moli tan kalama wawa. %2$s li kepeken %3$s", "death.attack.sonic_boom.player": "%1$s li alasa weka tan %2$s li moli tan kalama wawa", "death.attack.stalagmite": "%1$s li moli lupa tan palisa kiwen", "death.attack.stalagmite.player": "%1$s li utala e %2$s li moli lupa tan palisa kiwen", "death.attack.starve": "%1$s li moli tan moku ala", "death.attack.starve.player": "%1$s li utala e %2$s li moli tan moku ala", "death.attack.sting": "%1$s li moli tan pipi suwi", "death.attack.sting.item": "%2$s li moli e %1$s kepeken %3$s", "death.attack.sting.player": "%2$s li moli e %1$s kepeken kipisi monsi", "death.attack.sweetBerryBush": "kasi pi kili suwi li moli e %1$s", "death.attack.sweetBerryBush.player": "%1$s li alasa weka tan %2$s li moli tan kasi pi kili suwi", "death.attack.thorns": "%1$s li wile pakala e %2$s li moli", "death.attack.thorns.item": "%1$s li wile pakala e %2$s li moli tan %3$s", "death.attack.thrown": "%2$s li moli e %1$s", "death.attack.thrown.item": "%2$s li moli e %1$s kepeken %3$s", "death.attack.trident": "%2$s li moli e %1$s kepeken ilo alasa telo", "death.attack.trident.item": "%2$s li moli e %1$s kepeken %3$s lon insa ona", "death.attack.wither": "%1$s li moli tan wawa pimeja", "death.attack.wither.player": "%1$s li utala e %2$s li moli tan wawa pimeja", "death.attack.witherSkull": "%2$s li pana e lawa ike li moli e %1$s", "death.attack.witherSkull.item": "%2$s li moli e %1$s kepeken %3$s", "death.fell.accident.generic": "%1$s li tawa anpa tan sewi", "death.fell.accident.ladder": "%1$s li tawa anpa tan nasin palisa sewi", "death.fell.accident.other_climbable": "%1$s li wile tawa sewi li tawa anpa tan sewi", "death.fell.accident.scaffolding": "%1$s li tawa anpa tan supa palisa", "death.fell.accident.twisting_vines": "%1$s li tawa anpa tan kasi linja laso", "death.fell.accident.vines": "%1$s li tawa anpa tan kasi linja", "death.fell.accident.weeping_vines": "%1$s li tawa anpa tan kasi linja loje", "death.fell.assist": "%2$s li anpa e %1$s tawa moli", "death.fell.assist.item": "%2$s li anpa e %1$s kepeken %3$s tawa moli", "death.fell.finish": "%1$s li tawa anpa li kama moli tan %2$s", "death.fell.finish.item": "%1$s li tawa anpa li kama moli tan %2$s kepeken %3$s", "death.fell.killer": "jan li anpa e %1$s tawa moli", "deathScreen.quit.confirm": "sina wile ala wile weka?", "deathScreen.respawn": "o lon sin", "deathScreen.score": "nanpa wawa", "deathScreen.score.value": "nanpa: %s", "deathScreen.spectate": "o lukin e ma", "deathScreen.title": "sina moli!", "deathScreen.title.hardcore": "musi li pini!", "deathScreen.titleScreen": "lipu open", "debug.advanced_tooltips.help": "F3 + H = o sona e nasin ilo namako", "debug.advanced_tooltips.off": "sona ilo namako: ken ala lukin", "debug.advanced_tooltips.on": "sona ilo namako: ken lukin", "debug.chunk_boundaries.help": "F3 + G = o kama lukin e selo pi leko suli", "debug.chunk_boundaries.off": "selo pi kipisi ma: ken ala lukin", "debug.chunk_boundaries.on": "selo pi kipisi ma: ken lukin", "debug.clear_chat.help": "F3 + D = o weka e toki", "debug.copy_location.help": "F3 + C la o sitelen e toki wawa /tp kepeken ma sina. sina wile moli e musi la o awen luka e ni a", "debug.copy_location.message": "mi pana e nanpa ma sina tawa poki sama", "debug.crash.message": "sina kepeken F3 + C. sina pini ala la musi li moli.", "debug.crash.warning": "tenpo tawa moli musi: %s...", "debug.creative_spectator.error": "mi ken ala ante e nasin musi sina tan ken ala sina", "debug.creative_spectator.help": "F3 + N = o ante e nasin musi tawa/tan jan lukin", "debug.dump_dynamic_textures": "mi pana e sitelen ma lon poki %s", "debug.dump_dynamic_textures.help": "F3 + S = o kama jo e sitelen ma lon weka musi", "debug.gamemodes.error": "mi ken ala open e ilo ante pi nasin musi tan ken ala sina", "debug.gamemodes.help": "F3 + F4 = o open e ilo ante pi nasin musi", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s ante", "debug.help.help": "F3 + Q = o kama lukin e kulupu nimi ni", "debug.help.message": "kepeken pi ilo nena:", "debug.inspect.client.block": "musi sina la mi poki e sama pi sona nanpa ma", "debug.inspect.client.entity": "musi sina la mi poki e sama pi sona nanpa ijo", "debug.inspect.help": "F3 + I la o pana e ni tawa poki sama: sona nanpa pi ijo tawa anu sona nanpa leko", "debug.inspect.server.block": "ma kulupu la mi pali e sama pi sona nanpa leko", "debug.inspect.server.entity": "mi pali e sama pi sona nanpa pi ijo tawa tan ma kulupu ni", "debug.pause.help": "F3 + Esc = (ni li ken la) o awen e musi kepeken ala lipu awen", "debug.pause_focus.help": "F3 + P la sina lukin ala e musi la musi li awen", "debug.pause_focus.off": "sina lukin ala e musi la musi li awen: lon ala", "debug.pause_focus.on": "sina lukin ala e musi la musi li awen: lon", "debug.prefix": "[weka pakala]:", "debug.profiling.help": "F3 + L la o open o pini e alasa pi sona nanpa", "debug.profiling.start": "tenpo lili %s la mi alasa. o kepeken F3 + L tawa pini", "debug.profiling.stop": "alasa li pini. mi awen e sona nanpa lon %s", "debug.reload_chunks.help": "F3 + A = o lon sin e leko suli", "debug.reload_chunks.message": "mi lon sin e ma", "debug.reload_resourcepacks.help": "F3 + T = o lon sin e poki sitelen kalama", "debug.reload_resourcepacks.message": "mi lon sin e ante sitelen kalama", "debug.show_hitboxes.help": "F3 + B = o kama lukin e selo ijo utala", "debug.show_hitboxes.off": "selo utala: ken ala lukin", "debug.show_hitboxes.on": "selo utala: ken lukin", "debug.version.header": "sona pi nanpa musi sina:", "debug.version.help": "F3 + V = sona pi nanpa musi sina", "demo.day.1": "tenpo suno 5 musi la sina ken musi lon ma ni!", "demo.day.2": "tenpo suno nanpa tu", "demo.day.3": "tenpo suno nanpa tu wan", "demo.day.4": "tenpo suno nanpa tu tu", "demo.day.5": "tenpo suno wan taso li lon!", "demo.day.6": "tenpo musi sina li pini. sina wile sitelen e pali sina la, o kepeken %s.", "demo.day.warning": "tenpo pini li kama a!", "demo.demoExpired": "tenpo sina li pini!", "demo.help.buy": "esun musi", "demo.help.fullWrapped": "tenpo suno 5 musi (1h40m) la, sina ken musi lon ma ni. musi pona!", "demo.help.inventory": "sina ken lukin e jo sina kepeken %1$s", "demo.help.jump": "sina ken tawa sewi kepeken %1$s", "demo.help.later": "o awen musi!", "demo.help.movement": "o lukin kepeken ilo luka. %1$s en %2$s en %3$s en %4$s la sina ken tawa", "demo.help.movementMouse": "o lukin kepeken ilo luka (ona li lukin sama soweli lili)", "demo.help.movementShort": "%1$s en %2$s en %3$s en %4$s la sina ken tawa", "demo.help.title": "musi Manka pi wile ala mani", "demo.remainingTime": "mute pi tenpo awen sina: %s", "demo.reminder": "tenpo sina li pini, sina ken pana e mani tawa awen musi, anu sina ken pali e ma sin!", "difficulty.lock.question": "sina wile ala wile awen e ike ma? tenpo ale la ona li %1$s. awen la sina ken ala ante e ni!", "difficulty.lock.title": "o pini e ante pi ike ma", "disconnect.endOfStream": "pana sitelen li pini", "disconnect.exceeded_packet_rate": "sina weka tan ni: sina pana pi mute a e ijo lili pi ilo sona", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "mi weka e wile lon sina", "disconnect.loginFailedInfo": "kama sina li ike: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "sina ken ala kepeken ma kulupu. o lukin e lawa pi nimi Microsoft sina.", "disconnect.loginFailedInfo.invalidSession": "musi sina li ike. o open sin e musi sina e ilo sina pi open musi.", "disconnect.loginFailedInfo.serversUnavailable": "tenpo ni la ma kulupu li ken ala kama jo e nimi sina. o kama sin lon tenpo kama.", "disconnect.loginFailedInfo.userBanned": "sina ken ala musi kulupu", "disconnect.lost": "mi ken ala toki tawa kulupu", "disconnect.packetError": "sina pana e ijo pakala tawa ma kulupu.", "disconnect.spam": "jan li weka e sina tan ni: sina toki e mute jaki", "disconnect.timeout": "sina kepeken tenpo suli ike", "disconnect.transfer": "sina tawa ma kulupu sin", "disconnect.unknownHost": "mi ken ala alasa e ma kulupu", "download.pack.failed": "poki %s la mi ken ala jo e poki %s", "download.pack.progress.bytes": "kama poki: %s (mi sona ala e suli pali)", "download.pack.progress.percent": "kama poki: %s%%", "download.pack.title": "mi kama jo e poki sitelen kalama nanpa %s. %s li lon", "editGamerule.default": "nasin pi ante ala li ni: %s", "editGamerule.title": "ante e lawa musi", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "pilin awen", "effect.minecraft.bad_omen": "ike kama", "effect.minecraft.blindness": "lukin ala", "effect.minecraft.conduit_power": "wawa telo", "effect.minecraft.darkness": "pimeja suli", "effect.minecraft.dolphins_grace": "pona kala", "effect.minecraft.fire_resistance": "awen lete", "effect.minecraft.glowing": "suno", "effect.minecraft.haste": "pakala wawa", "effect.minecraft.health_boost": "pilin mute", "effect.minecraft.hero_of_the_village": "jan pona pi ma tomo", "effect.minecraft.hunger": "wile moku", "effect.minecraft.infested": "kama pipi", "effect.minecraft.instant_damage": "ike si<PERSON>lo", "effect.minecraft.instant_health": "pona sijelo", "effect.minecraft.invisibility": "sijelo kon", "effect.minecraft.jump_boost": "noka wawa", "effect.minecraft.levitation": "tawa sewi", "effect.minecraft.luck": "ken pona", "effect.minecraft.mining_fatigue": "pakala ike", "effect.minecraft.nausea": "pilin jaki", "effect.minecraft.night_vision": "lukin wawa", "effect.minecraft.oozing": "pana ko", "effect.minecraft.poison": "ike sijelo kama", "effect.minecraft.raid_omen": "ike kama pi utala tomo", "effect.minecraft.regeneration": "pona sijelo kama", "effect.minecraft.resistance": "awen", "effect.minecraft.saturation": "pona moku", "effect.minecraft.slow_falling": "anpa ala", "effect.minecraft.slowness": "tawa ike", "effect.minecraft.speed": "tawa wawa", "effect.minecraft.strength": "utala wawa", "effect.minecraft.trial_omen": "ike kama pi tomo alasa", "effect.minecraft.unluck": "ken ike", "effect.minecraft.water_breathing": "kon lon telo", "effect.minecraft.weakness": "utala ike", "effect.minecraft.weaving": "kama linja", "effect.minecraft.wind_charged": "pana kon", "effect.minecraft.wither": "moli pimeja", "effect.none": "wawa ala", "enchantment.level.1": "pi wawa wan", "enchantment.level.10": "pi wawa luka luka", "enchantment.level.2": "pi wawa tu", "enchantment.level.3": "pi wawa tu wan", "enchantment.level.4": "pi wawa tu tu", "enchantment.level.5": "pi wawa luka", "enchantment.level.6": "pi wawa luka wan", "enchantment.level.7": "pi wawa luka tu", "enchantment.level.8": "pi wawa luka tu wan", "enchantment.level.9": "pi wawa luka tu tu", "enchantment.minecraft.aqua_affinity": "luka telo", "enchantment.minecraft.bane_of_arthropods": "wawa tawa pipi", "enchantment.minecraft.binding_curse": "ike pi awen sijelo", "enchantment.minecraft.blast_protection": "awen pi seli pakala", "enchantment.minecraft.breach": "pakala pi len awen", "enchantment.minecraft.channeling": "wawa pi linja suno", "enchantment.minecraft.density": "wawa kiwen", "enchantment.minecraft.depth_strider": "noka kala", "enchantment.minecraft.efficiency": "wawa pakala", "enchantment.minecraft.feather_falling": "noka waso", "enchantment.minecraft.fire_aspect": "seli", "enchantment.minecraft.fire_protection": "awen tan seli", "enchantment.minecraft.flame": "palisa seli", "enchantment.minecraft.fortune": "mani mute", "enchantment.minecraft.frost_walker": "noka lete", "enchantment.minecraft.impaling": "wawa tawa kala", "enchantment.minecraft.infinity": "palisa ale", "enchantment.minecraft.knockback": "tawa monsi", "enchantment.minecraft.looting": "alasa pona", "enchantment.minecraft.loyalty": "tawa sike", "enchantment.minecraft.luck_of_the_sea": "alasa kala pona", "enchantment.minecraft.lure": "kala mute", "enchantment.minecraft.mending": "kama sin", "enchantment.minecraft.multishot": "palisa mute", "enchantment.minecraft.piercing": "palisa kipisi", "enchantment.minecraft.power": "palisa wawa", "enchantment.minecraft.projectile_protection": "awen pana", "enchantment.minecraft.protection": "awen", "enchantment.minecraft.punch": "palisa pi tawa monsi", "enchantment.minecraft.quick_charge": "kama palisa wawa", "enchantment.minecraft.respiration": "kon telo", "enchantment.minecraft.riptide": "tawa wawa telo", "enchantment.minecraft.sharpness": "kipisi", "enchantment.minecraft.silk_touch": "luka suwi", "enchantment.minecraft.smite": "wawa tawa moli", "enchantment.minecraft.soul_speed": "noka kon", "enchantment.minecraft.sweeping": "utala suli", "enchantment.minecraft.sweeping_edge": "utala suli", "enchantment.minecraft.swift_sneak": "noka suwi wawa", "enchantment.minecraft.thorns": "selo kipisi", "enchantment.minecraft.unbreaking": "pakala ala", "enchantment.minecraft.vanishing_curse": "ike pi ijo weka", "enchantment.minecraft.wind_burst": "wawa kon", "entity.minecraft.acacia_boat": "ilo tawa telo pi kasi seli", "entity.minecraft.acacia_chest_boat": "poki lon ilo tawa telo pi kasi seli", "entity.minecraft.allay": "kon pona", "entity.minecraft.area_effect_cloud": "kon kule wawa", "entity.minecraft.armadillo": "soweli pi kiwen selo", "entity.minecraft.armor_stand": "ilo jo len", "entity.minecraft.arrow": "palisa alasa", "entity.minecraft.axolotl": "a<PERSON>i suwi", "entity.minecraft.bamboo_chest_raft": "poki lon ilo telo pi kasi palisa", "entity.minecraft.bamboo_raft": "ilo telo pi kasi palisa", "entity.minecraft.bat": "waso pimeja", "entity.minecraft.bee": "pipi suwi", "entity.minecraft.birch_boat": "ilo tawa telo pi kasi jelo", "entity.minecraft.birch_chest_boat": "poki lon ilo tawa telo pi kasi jelo", "entity.minecraft.blaze": "monsuta seli", "entity.minecraft.block_display": "<PERSON><PERSON> leko", "entity.minecraft.boat": "ilo tawa telo", "entity.minecraft.bogged": "jan moli jaki", "entity.minecraft.breeze": "monsuta kon", "entity.minecraft.breeze_wind_charge": "sike kon", "entity.minecraft.camel": "soweli pi nena monsi", "entity.minecraft.cat": "sowe<PERSON> suwi", "entity.minecraft.cave_spider": "pipi lupa", "entity.minecraft.cherry_boat": "ilo tawa telo pi kasi suwi", "entity.minecraft.cherry_chest_boat": "poki lon ilo tawa telo pi kasi suwi", "entity.minecraft.chest_boat": "poki lon ilo tawa telo", "entity.minecraft.chest_minecart": "poki ijo lon poki tawa", "entity.minecraft.chicken": "waso walo", "entity.minecraft.cod": "kala walo", "entity.minecraft.command_block_minecart": "ilo pi toki wawa lon poki tawa", "entity.minecraft.cow": "mani", "entity.minecraft.creaking": "monsuta kasi", "entity.minecraft.creaking_transient": "monsuta kasi", "entity.minecraft.creeper": "<PERSON><PERSON><PERSON>", "entity.minecraft.dark_oak_boat": "ilo tawa telo pi kasi pimeja", "entity.minecraft.dark_oak_chest_boat": "poki lon ilo tawa telo pi kasi pimeja", "entity.minecraft.dolphin": "kala suli", "entity.minecraft.donkey": "soweli jo", "entity.minecraft.dragon_fireball": "sike seli pi akesi pini", "entity.minecraft.drowned": "jan moli telo", "entity.minecraft.egg": "sike mama pana", "entity.minecraft.elder_guardian": "kala oko suli", "entity.minecraft.end_crystal": "ilo wawa pi ma End", "entity.minecraft.ender_dragon": "akesi pi ma End", "entity.minecraft.ender_pearl": "sike pana pi ma End", "entity.minecraft.enderman": "jan pi ma <PERSON>", "entity.minecraft.endermite": "pipi pi ma End", "entity.minecraft.evoker": "jan ike wawa", "entity.minecraft.evoker_fangs": "palisa uta pi jan ike wawa", "entity.minecraft.experience_bottle": "poki pana pi wawa laso", "entity.minecraft.experience_orb": "sike pi wawa laso", "entity.minecraft.eye_of_ender": "oko pi ma End", "entity.minecraft.falling_block": "leko pi tawa anpa", "entity.minecraft.falling_block_type": "%s pi tawa anpa", "entity.minecraft.fireball": "sike seli", "entity.minecraft.firework_rocket": "palisa pi seli kule", "entity.minecraft.fishing_bobber": "ijo lon supa telo", "entity.minecraft.fox": "soweli pi linja monsi", "entity.minecraft.frog": "a<PERSON><PERSON> sewi", "entity.minecraft.furnace_minecart": "poki seli lon poki tawa", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "jan moli suli", "entity.minecraft.glow_item_frame": "lipu poki suno", "entity.minecraft.glow_squid": "kala luka suno", "entity.minecraft.goat": "soweli nena", "entity.minecraft.guardian": "kala oko", "entity.minecraft.happy_ghast": "waso pona Ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "poki pi tawa ijo lon poki tawa", "entity.minecraft.horse": "soweli tawa", "entity.minecraft.husk": "jan moli seli", "entity.minecraft.illusioner": "jan ike pi <PERSON>len nasa", "entity.minecraft.interaction": "kepeken", "entity.minecraft.iron_golem": "jan ilo pi kiwen walo", "entity.minecraft.item": "ijo", "entity.minecraft.item_display": "<PERSON><PERSON> ijo", "entity.minecraft.item_frame": "lipu poki", "entity.minecraft.jungle_boat": "ilo tawa telo pi kasi suli", "entity.minecraft.jungle_chest_boat": "poki lon ilo tawa telo pi kasi suli", "entity.minecraft.killer_bunny": "soweli lili monsuta", "entity.minecraft.leash_knot": "linja sinpin", "entity.minecraft.lightning_bolt": "linja pi wawa suno", "entity.minecraft.lingering_potion": "kon wawa", "entity.minecraft.llama": "soweli pi telo uta", "entity.minecraft.llama_spit": "telo uta soweli", "entity.minecraft.magma_cube": "monsuta ko seli", "entity.minecraft.mangrove_boat": "ilo tawa telo pi kasi telo", "entity.minecraft.mangrove_chest_boat": "poki lon ilo tawa telo pi kasi telo", "entity.minecraft.marker": "kon nasin", "entity.minecraft.minecart": "poki tawa", "entity.minecraft.mooshroom": "mani <PERSON>", "entity.minecraft.mule": "soweli jo tawa", "entity.minecraft.oak_boat": "ilo tawa telo pi kasi kili", "entity.minecraft.oak_chest_boat": "poki lon ilo tawa telo pi kasi kili", "entity.minecraft.ocelot": "soweli alasa", "entity.minecraft.ominous_item_spawner": "ijo kama ike", "entity.minecraft.painting": "sitelen", "entity.minecraft.pale_oak_boat": "ilo tawa telo pi kasi walo", "entity.minecraft.pale_oak_chest_boat": "poki lon ilo tawa telo pi kasi walo", "entity.minecraft.panda": "soweli pimeja walo", "entity.minecraft.parrot": "waso toki", "entity.minecraft.phantom": "waso mun", "entity.minecraft.pig": "soweli pi loje walo", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "jan <PERSON> wawa", "entity.minecraft.pillager": "jan ike alasa", "entity.minecraft.player": "jan", "entity.minecraft.polar_bear": "soweli lete", "entity.minecraft.potion": "telo wawa", "entity.minecraft.pufferfish": "kala pi kama suli", "entity.minecraft.rabbit": "soweli lili", "entity.minecraft.ravager": "monsuta suli", "entity.minecraft.salmon": "kala loje", "entity.minecraft.sheep": "soweli len", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "pana pi monsu<PERSON>", "entity.minecraft.silverfish": "pipi kiwen", "entity.minecraft.skeleton": "jan moli palisa", "entity.minecraft.skeleton_horse": "soweli tawa palisa", "entity.minecraft.slime": "monsuta ko", "entity.minecraft.small_fireball": "sike seli lili", "entity.minecraft.sniffer": "soweli suli alasa", "entity.minecraft.snow_golem": "jan pi ko lete", "entity.minecraft.snowball": "sike pi ko lete", "entity.minecraft.spawner_minecart": " ilo kama lon poki tawa", "entity.minecraft.spectral_arrow": "palisa alasa suno", "entity.minecraft.spider": "pipi noka", "entity.minecraft.splash_potion": "telo wawa pana", "entity.minecraft.spruce_boat": "ilo tawa telo pi kasi lete", "entity.minecraft.spruce_chest_boat": "poki lon ilo tawa telo pi kasi lete", "entity.minecraft.squid": "kala luka", "entity.minecraft.stray": "jan moli lete", "entity.minecraft.strider": "a<PERSON>i seli", "entity.minecraft.tadpole": "akesi sewi lili", "entity.minecraft.text_display": "sitelen sitelen", "entity.minecraft.tnt": "ilo seli pakala suno", "entity.minecraft.tnt_minecart": " seli pakala lon poki tawa", "entity.minecraft.trader_llama": "soweli pi jan esun", "entity.minecraft.trident": "ilo alasa telo", "entity.minecraft.tropical_fish": "kala kule", "entity.minecraft.tropical_fish.predefined.0": "kala pimeja jelo", "entity.minecraft.tropical_fish.predefined.1": "kala kipisi pimeja", "entity.minecraft.tropical_fish.predefined.10": "kala pi uta lili", "entity.minecraft.tropical_fish.predefined.11": "kala ko walo en loje jelo", "entity.minecraft.tropical_fish.predefined.12": "kala waso", "entity.minecraft.tropical_fish.predefined.13": "kala pi meli lawa", "entity.minecraft.tropical_fish.predefined.14": "kala <PERSON> loje", "entity.minecraft.tropical_fish.predefined.15": "kala pi uta loje", "entity.minecraft.tropical_fish.predefined.16": "kala walo loje", "entity.minecraft.tropical_fish.predefined.17": "kala pi linja lili <PERSON>", "entity.minecraft.tropical_fish.predefined.18": "kala nasa loje", "entity.minecraft.tropical_fish.predefined.19": "kala lili walo", "entity.minecraft.tropical_fish.predefined.2": "kala kipisi laso", "entity.minecraft.tropical_fish.predefined.20": "kala pi noka jelo", "entity.minecraft.tropical_fish.predefined.21": "kala kipisi jelo", "entity.minecraft.tropical_fish.predefined.3": "kala pi pipi kule", "entity.minecraft.tropical_fish.predefined.4": "kala laso lili", "entity.minecraft.tropical_fish.predefined.5": "kala nasa", "entity.minecraft.tropical_fish.predefined.6": "kala suwi", "entity.minecraft.tropical_fish.predefined.7": "kala pi anpa jelo", "entity.minecraft.tropical_fish.predefined.8": "kala ko walo en loje", "entity.minecraft.tropical_fish.predefined.9": "kala pi linja lili", "entity.minecraft.tropical_fish.type.betty": "<PERSON>la <PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "kala leko", "entity.minecraft.tropical_fish.type.brinely": "kala namako", "entity.minecraft.tropical_fish.type.clayfish": "kala ko", "entity.minecraft.tropical_fish.type.dasher": "kala wawa", "entity.minecraft.tropical_fish.type.flopper": "kala sike", "entity.minecraft.tropical_fish.type.glitter": "kala mun", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "kala lape", "entity.minecraft.tropical_fish.type.spotty": "kala pi sike lili", "entity.minecraft.tropical_fish.type.stripey": "kala pi linja kule", "entity.minecraft.tropical_fish.type.sunstreak": "kala suno", "entity.minecraft.turtle": "akesi tomo", "entity.minecraft.vex": "kon ike", "entity.minecraft.villager": "jan tomo", "entity.minecraft.villager.armorer": "jan pi len utala", "entity.minecraft.villager.butcher": "jan moku", "entity.minecraft.villager.cartographer": "jan <PERSON>len", "entity.minecraft.villager.cleric": "jan sewi", "entity.minecraft.villager.farmer": "jan kasi", "entity.minecraft.villager.fisherman": "jan kala", "entity.minecraft.villager.fletcher": "jan pi palisa alasa", "entity.minecraft.villager.leatherworker": "jan pi selo soweli", "entity.minecraft.villager.librarian": "jan pi lipu sona", "entity.minecraft.villager.mason": "jan kiwen", "entity.minecraft.villager.nitwit": "jan pi sona ala", "entity.minecraft.villager.none": "jan tomo", "entity.minecraft.villager.shepherd": "jan len", "entity.minecraft.villager.toolsmith": "jan ilo", "entity.minecraft.villager.weaponsmith": "jan pi ilo utala", "entity.minecraft.vindicator": "jan ike kipisi", "entity.minecraft.wandering_trader": "jan esun", "entity.minecraft.warden": "monsuta kute", "entity.minecraft.wind_charge": "sike kon", "entity.minecraft.witch": "jan pi telo wawa", "entity.minecraft.wither": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither_skeleton": "jan moli palisa <PERSON>", "entity.minecraft.wither_skull": "lawa pi monsuta <PERSON>", "entity.minecraft.wolf": "soweli pona", "entity.minecraft.zoglin": "sowe<PERSON> moli <PERSON>", "entity.minecraft.zombie": "jan moli", "entity.minecraft.zombie_horse": "soweli tawa moli", "entity.minecraft.zombie_villager": "jan tomo moli", "entity.minecraft.zombified_piglin": "jan <PERSON><PERSON> moli", "entity.not_summonable": "mi ken ala kama e ijo %s", "event.minecraft.raid": "utala tomo", "event.minecraft.raid.defeat": "pini ike", "event.minecraft.raid.defeat.full": "utala tomo li pini ike", "event.minecraft.raid.raiders_remaining": "jan utala pi moli ala: %s", "event.minecraft.raid.victory": "pini pona", "event.minecraft.raid.victory.full": "utala tomo li pini pona", "filled_map.buried_treasure": "sitelen ma pi poki mani anpa", "filled_map.explorer_jungle": "sitelen tomo pi kasi suli", "filled_map.explorer_swamp": "sitelen tomo pi telo jaki", "filled_map.id": "nanpa %s", "filled_map.level": "(suli %s/%s)", "filled_map.locked": "awen", "filled_map.mansion": "sitelen ma pi tomo pimeja suli", "filled_map.monument": "sitelen ma pi tomo telo suli", "filled_map.scale": "suli lukin li 1:%s", "filled_map.trial_chambers": "sitelen ma pi tomo alasa", "filled_map.unknown": "sitelen ma ante", "filled_map.village_desert": "sitelen tomo pi ma seli", "filled_map.village_plains": "sitelen tomo pi ma supa", "filled_map.village_savanna": "sitelen tomo pi ma seli", "filled_map.village_snowy": "sitelen tomo pi ko lete", "filled_map.village_taiga": "sitelen tomo pi ma lete", "flat_world_preset.minecraft.bottomless_pit": "ma pi anpa ala", "flat_world_preset.minecraft.classic_flat": "supa suli pi tenpo pini", "flat_world_preset.minecraft.desert": "ma seli", "flat_world_preset.minecraft.overworld": "ma pona!", "flat_world_preset.minecraft.redstone_ready": "ma tawa ko Redstone", "flat_world_preset.minecraft.snowy_kingdom": "ma pi ko lete", "flat_world_preset.minecraft.the_void": "kon pimeja", "flat_world_preset.minecraft.tunnelers_dream": "lupa anpa", "flat_world_preset.minecraft.water_world": "ma telo", "flat_world_preset.unknown": "???", "gameMode.adventure": "jan alasa", "gameMode.changed": "mi ante e nasin musi sina tawa %s", "gameMode.creative": "jan sewi", "gameMode.hardcore": "jan pi lon wan taso!", "gameMode.spectator": "jan lukin", "gameMode.survival": "jan ma", "gamerule.allowFireTicksAwayFromPlayer": "jan li weka la, o ken tawa e seli", "gamerule.allowFireTicksAwayFromPlayer.description": "ni li lawa e ken tawa pi ijo seli ni: leko suli 8 la, ona li weka mute tan jan ale", "gamerule.announceAdvancements": "o toki e pali pona", "gamerule.blockExplosionDropDecay": "seli pakala li ken pakala ale e leko", "gamerule.blockExplosionDropDecay.description": "ilo seli pakala li ken pakala ale e ijo.", "gamerule.category.chat": "toki", "gamerule.category.drops": "ijo tan moli", "gamerule.category.misc": "ijo ante", "gamerule.category.mobs": "soweli en monsuta", "gamerule.category.player": "jan musi", "gamerule.category.spawning": "sin ijo", "gamerule.category.updates": "ante ma", "gamerule.commandBlockOutput": "o pana ale e toki tan ilo lawa", "gamerule.commandModificationBlockLimit": "nanpa ni la, toki wawa ken ante e leko", "gamerule.commandModificationBlockLimit.description": "sina ken ante e mute leko ni tan toki wawa wan, sama /fill sama /clone.", "gamerule.disableElytraMovementCheck": "len waso la mi lukin ala e wawa tawa", "gamerule.disablePlayerMovementCheck": "jan la mi lukin ala e wawa tawa", "gamerule.disableRaids": "o ken ala e utala pi ma tomo", "gamerule.doDaylightCycle": "o ken e tawa tenpo", "gamerule.doEntityDrops": "ijo li pana e jo ona tan pakala", "gamerule.doEntityDrops.description": "ni la, ilo tawa en lipu poki en ijo ante li pana anu pana ala e ijo ona lon pakala ona.", "gamerule.doFireTick": "o ken e tawa seli", "gamerule.doImmediateRespawn": "o lon sin e jan kepeken tenpo lili a", "gamerule.doInsomnia": "o ken e waso mun", "gamerule.doLimitedCrafting": "o wile e nasin pali", "gamerule.doLimitedCrafting.description": "ni la sina o sona e nasin pali tawa pali ijo", "gamerule.doMobLoot": "moli la soweli li pana e ijo", "gamerule.doMobLoot.description": "ni la soweli en monsuta en ijo ante li pana e ijo ona e wawa ona lon moli ona.", "gamerule.doMobSpawning": "o ken e soweli e monsuta", "gamerule.doMobSpawning.description": "ken la ijo li kama kepeken nasin ante.", "gamerule.doPatrolSpawning": "o ken e kulupu pi jan ike", "gamerule.doTileDrops": "pakala la kiwen en ijo ante li pana e ijo", "gamerule.doTileDrops.description": "kiwen en ma en ijo ante li pana ala e ijo e wawa sona lon pakala ona.", "gamerule.doTraderSpawning": "o ken e jan esun", "gamerule.doVinesSpread": "kasi linja li kama suli", "gamerule.doVinesSpread.description": "ni li lawa e ni: kasi linja pi laso kasi li kama ala kama suli tawa leko lon poka ona. ni li lawa ala e kasi linja ante sama ni: kasi linja loje anu kasi linja pi laso telo anu ante.", "gamerule.doWardenSpawning": "o ken e monsuta kute", "gamerule.doWeatherCycle": "o ken e ante sewi", "gamerule.drowningDamage": "o ken e moli telo pi kon ala", "gamerule.enderPearlsVanishOnDeath": "sina moli la sike pi ma End li weka", "gamerule.enderPearlsVanishOnDeath.description": "jan li pana e sike pi ma End li moli la, sike o awen anu weka.", "gamerule.entitiesWithPassengersCanUsePortals": "ijo li lawa e ijo la ona li ken kepeken lupa wawa", "gamerule.entitiesWithPassengersCanUsePortals.description": "ijo li tawa lon ijo ante la, ona o ken tawa kepeken lupa pi ma Nether anu lupa pi ma End anu lupa weka pi ma End.", "gamerule.fallDamage": "o ken e moli tan tawa anpa", "gamerule.fireDamage": "o ken e moli tan seli", "gamerule.forgiveDeadPlayers": "jan li moli la ijo li pilin pona tawa ona", "gamerule.forgiveDeadPlayers.description": "jan li ike tawa monsuta li moli lon poka la pilin ike monsuta li weka.", "gamerule.freezeDamage": "o ken e pakala tan lete", "gamerule.globalSoundEvents": "kalama pi ma ale", "gamerule.globalSoundEvents.description": "jan musi ale li ken kute e mute lili kalama, sama ni: kalama pi monsuta Wither sin.", "gamerule.keepInventory": "moli la o awen e jo sina", "gamerule.lavaSourceConversion": "telo seli li ken mama", "gamerule.lavaSourceConversion.description": "telo seli awen tu li lon poka li telo seli tawa la, ona li kama telo awen", "gamerule.locatorBar": "o ken e ijo pi lukin lon", "gamerule.locatorBar.description": "ni li kama ken la, ijo pi lukin lon li pana sona ni: jan ante li lon seme li tawa seme.", "gamerule.logAdminCommands": "o pana e toki wawa pi jan lawa tawa jan ale", "gamerule.maxCommandChainLength": "mute pi ilo lawa lon linja wan", "gamerule.maxCommandChainLength.description": "ilo lawa pi mute ni li ken pali lon linja poka wan.", "gamerule.maxCommandForkCount": "mute pi ante sona", "gamerule.maxCommandForkCount.description": "mute ni la toki wawa li ante sona. toki wawa 'execute as' li sama.", "gamerule.maxEntityCramming": "mute ijo lon insa pi ijo ante", "gamerule.minecartMaxSpeed": "wawa tawa suli pi poki tawa", "gamerule.minecartMaxSpeed.description": "wawa tawa suli pi poki tawa lon ma", "gamerule.mobExplosionDropDecay": "seli pakala li ken pakala ale e leko", "gamerule.mobExplosionDropDecay.description": "ilo seli pakala li ken pakala ale e ijo.", "gamerule.mobGriefing": "o ken e pakala tan monsuta", "gamerule.naturalRegeneration": "o sin e sijelo sina kepeken tenpo taso", "gamerule.playersNetherPortalCreativeDelay": "tenpo pi lupa wawa tawa jan sewi", "gamerule.playersNetherPortalCreativeDelay.description": "tenpo pi mute ni (kepeken tenpo lili) la, jan li jan sewi li lon lupa wawa pi ma Nether la, ona li tawa ma ante.", "gamerule.playersNetherPortalDefaultDelay": "tenpo pi lupa wawa tawa jan pi sewi ala", "gamerule.playersNetherPortalDefaultDelay.description": "tenpo pi mute ni (kepeken tenpo lili) la, jan li jan sewi ala li lon lupa wawa pi ma Nether la, ona li tawa ma ante.", "gamerule.playersSleepingPercentage": "nanpa pi jan lape", "gamerule.playersSleepingPercentage.description": "kipisi ni jan li lape la tenpo pimeja li weka.", "gamerule.projectilesCanBreakBlocks": "ijo pi tawa wawa li ken pakala e leko", "gamerule.projectilesCanBreakBlocks.description": "ni la ijo pi tawa wawa li ken pakala e leko.", "gamerule.randomTickSpeed": "tenpo wile pi kama kasi pi ijo ante", "gamerule.reducedDebugInfo": "f3 la sona lili", "gamerule.reducedDebugInfo.description": "ni li lili e sona lon lipu F3.", "gamerule.sendCommandFeedback": "o pana e toki tan toki wawa", "gamerule.showDeathMessages": "o pana e toki lon moli jan", "gamerule.snowAccumulationHeight": "sewi pi ko lete", "gamerule.snowAccumulationHeight.description": "sewi li ken pana e mute ni pi ko lete lon anpa", "gamerule.spawnChunkRadius": "suli pi ma lon", "gamerule.spawnChunkRadius.description": "ma pi nanpa ni li lon ale lon poka pi ma open.", "gamerule.spawnRadius": "suli ma pi kama sin", "gamerule.spawnRadius.description": "ma poka pi mute ni la jan li ken open.", "gamerule.spectatorsGenerateChunks": "ni la jan lukin li ken kama e ma sin lon tawa ona", "gamerule.tntExplodes": "o ken e pakala tan ilo pi seli pakala", "gamerule.tntExplosionDropDecay": "ilo seli pakala li awen e ijo ale ala", "gamerule.tntExplosionDropDecay.description": "ilo seli pakala li ken pakala ale e ijo.", "gamerule.universalAnger": "ike tawa jan ale", "gamerule.universalAnger.description": "monsuta li pilin ike tawa jan wan la ona li ike tawa jan ale ante kin. nasin forgiveDeadPlayers li lon ala la ni li pali pona.", "gamerule.waterSourceConversion": "telo li ken mama", "gamerule.waterSourceConversion.description": "telo awen tu li lon poka li telo tawa la, ona li kama telo awen", "generator.custom": "kule ante", "generator.customized": "ijo pi ante mute pi tenpo pini", "generator.minecraft.amplified": "SULI NASA NENA", "generator.minecraft.amplified.info": "nasin ni li musi li kepeken ilo wawa a!", "generator.minecraft.debug_all_block_states": "ma pi ijo ale", "generator.minecraft.flat": "supa suli", "generator.minecraft.large_biomes": "ma nasin suli", "generator.minecraft.normal": "pona", "generator.minecraft.single_biome_surface": "ma wan", "generator.single_biome_caves": "lupa ma", "generator.single_biome_floating_islands": "ma lili mute sewi", "gui.abuseReport.attestation": "sina pana e ni la, sina toki e ni: sona sina li lon li pona.", "gui.abuseReport.comments": "toki namako sina", "gui.abuseReport.describe": "sina pana e sona suli la kulupu lawa li ken lawa pona", "gui.abuseReport.discard.content": "sina weka la, pana sina en toki sina kin li weka. sina wile ala wile ni?", "gui.abuseReport.discard.discard": "o weka", "gui.abuseReport.discard.draft": "o awen e ni tawa tenpo kama", "gui.abuseReport.discard.return": "o awen sitelen", "gui.abuseReport.discard.title": "sina wile ala wile weka e pana sina e toki sina?", "gui.abuseReport.draft.content": "sina wile ala wile awen ante e pana ni? wile ala la, sina ken weka e ona li pali e pana sin.", "gui.abuseReport.draft.discard": "o weka", "gui.abuseReport.draft.edit": "o awen sitelen", "gui.abuseReport.draft.quittotitle.content": "sina wile awen sitelen e ni anu seme?", "gui.abuseReport.draft.quittotitle.title": "sina open e pana pi toki ike! sina pini musi la, pana ni li pini kin", "gui.abuseReport.draft.title": "o ante e pana ni anu seme?", "gui.abuseReport.error.title": "mi pakala; sina ken ala pana", "gui.abuseReport.message": "seme la sina lukin e ike? sona ni li pona e kama sona mi.", "gui.abuseReport.more_comments": "seme li kama? o toki e ni:", "gui.abuseReport.name.comment_box_label": "o toki e ni: seme li ike lon nimi ni?", "gui.abuseReport.name.reporting": "sina pana e nimi %s", "gui.abuseReport.name.title": "o pana e nimi pi jan ike", "gui.abuseReport.observed_what": "sina pana e toki ni tan seme?", "gui.abuseReport.read_info": "o kama sona e pana toki", "gui.abuseReport.reason.alcohol_tobacco_drugs": "ijo pi pakala sijelo anu telo nasa", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "jan li wile e ni: jan ante li pali lon poka pi moku/telo nasa ike. ante la jan li wile e ni: jan lili li moku e telo nasa.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "unpa anu pakala tawa jan lili", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "jan li toki e unpa pi jan lili, anu ona li wawa e ni.", "gui.abuseReport.reason.defamation_impersonation_false_information": "jan li toki e ijo pi lon ala, anu alasa e ni: ona li sina tawa lukin pi jan ante.", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "jan li toki e ijo pi lon ala, anu alasa e ni: ona li sina tawa lukin pi jan ante.", "gui.abuseReport.reason.description": "tan:", "gui.abuseReport.reason.false_reporting": "jan li pana ike e nimi", "gui.abuseReport.reason.generic": "sina wile pana e toki", "gui.abuseReport.reason.generic.description": "pali ona anu toki ona li nasa ike tawa mi / pali wan ona li ike tawa mi", "gui.abuseReport.reason.harassment_or_bullying": "pali ike", "gui.abuseReport.reason.harassment_or_bullying.description": "jan li alasa pakala e sina anu jan ante, kepeken toki. ken la jan li awen toki e sina, taso sina wile ala e ni. ken la ona li pana e sona pi lon sina, taso sina wile ala e ni.", "gui.abuseReport.reason.hate_speech": "toki pi ike kulupu", "gui.abuseReport.reason.hate_speech.description": "jan li toki ike tawa sina anu jan ante tan nasin sewi, anu kulu<PERSON>, anu nasin o<PERSON>.", "gui.abuseReport.reason.imminent_harm": "ike pi tenpo kama poka / wile pakala tawa jan ante", "gui.abuseReport.reason.imminent_harm.description": "jan li toki e ni: ona li wile pakala e sina anu jan ante.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "sitelen sijelo pi wile sina ala", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "jan li pana e sitelen sijelo. ante la ona li toki e ijo pi sitelen sijelo.", "gui.abuseReport.reason.self_harm_or_suicide": "ike pi tenpo kama / pakala anu moli pi ona sama", "gui.abuseReport.reason.self_harm_or_suicide.description": "jan li wile pakala lon e ona sama, anu jan li toki e ijo ni.", "gui.abuseReport.reason.sexually_inappropriate": "ike unpa", "gui.abuseReport.reason.sexually_inappropriate.description": "selo jan li ike unpa.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "pali pi ike kulupu", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "jan li toki e ijo pi utala moli pi jan lon, anu jan li wile pali e ni.", "gui.abuseReport.reason.title": "toki li ike tan seme?", "gui.abuseReport.report_sent_msg": "kulupu lawa li jo e pana sina. sina pona!\n\nkulupu li lukin e ona lon tenpo poka ken.", "gui.abuseReport.select_reason": "kulupu seme li pona tawa pana ni?", "gui.abuseReport.send": "o pana e toki", "gui.abuseReport.send.comment_too_long": "o lili e toki", "gui.abuseReport.send.error_message": "sina ken ala pana. ni li tan:", "gui.abuseReport.send.generic_error": "mi pakala, li sona ala e tan.", "gui.abuseReport.send.http_error": "sina ken ala pana. pakala HTTP li kama.", "gui.abuseReport.send.json_error": "sina ken ala pana. mi lukin e ijo pakala.", "gui.abuseReport.send.no_reason": "o wile e kulupu tawa pana ni", "gui.abuseReport.send.not_attested": "o lukin e sitelen sewi. o luka e leko. sina ni la, sina ken pana e toki pana", "gui.abuseReport.send.service_unavailable": "sina ken ala pana. ilo musi sina li lon linja la sina o alasa sin.", "gui.abuseReport.sending.title": "pana sina li tawa...", "gui.abuseReport.sent.title": "pini", "gui.abuseReport.skin.title": "o pana e selo pi jan ike", "gui.abuseReport.title": "o pana e nimi pi jan ike", "gui.abuseReport.type.chat": "toki", "gui.abuseReport.type.name": "nimi jan", "gui.abuseReport.type.skin": "selo jan", "gui.acknowledge": "mi sona", "gui.advancements": "pali pona", "gui.all": "ale", "gui.back": "tawa monsi", "gui.banned.description": "%s\n\n%s\n\no kama sona lon lipu ni: %s", "gui.banned.description.permanent": "tenpo ali la sina ken ala musi kulupu, lon ma kulupu anu ma <PERSON>s.", "gui.banned.description.reason": "tenpo poka la jan li pana e ike sona tawa kulupu lawa. kulupu li lukin e ijo sina li pilin e ni: ona li ike tan '%s'. ni li ike tawa nasin kulupu pi musi Manka.", "gui.banned.description.reason_id": "toki ilo: %s", "gui.banned.description.reason_id_message": "toki ilo: %s - %s", "gui.banned.description.temporary": "%s \ntenpo ni li kama ala la sina ken ala kepeken ma kulupu anu ma <PERSON>s.", "gui.banned.description.temporary.duration": "sina ken ala musi. tenpo %s li pini la sina ken musi sin.", "gui.banned.description.unknownreason": "tenpo poka la jan li toki e ni: sina ijo ike. kulupu lawa li lukin e ijo sina. ijo sina li ike kin tawa nasin pi kulupu Manka.", "gui.banned.name.description": "nimi sina - \"%s\" - li ike tawa wile mi. sina ken musi pi sina taso. taso, sina o ante e nimi sina tawa musi pi jan ante.\n\no sona namako anu pana e lipu sona lon ni: %s", "gui.banned.name.title": "ma kulupu la nimi sina li ike", "gui.banned.reason.defamation_impersonation_false_information": "sina kama e ni: sona pi jan ante la sina jan ante. ante la sina pana e sona ike tawa ike jan.", "gui.banned.reason.drugs": "toki pi moku nasa ike", "gui.banned.reason.extreme_violence_or_gore": "sina pana e sitelen anu toki pi moli jan", "gui.banned.reason.false_reporting": "sina pana e nimi jan mute la jan li ike ala", "gui.banned.reason.fraud": "jan li kama jo ike li kepeken ike e ijo", "gui.banned.reason.generic_violation": "sina kute ala e nasin kulupu pi musi Manka", "gui.banned.reason.harassment_or_bullying": "toki pakala", "gui.banned.reason.hate_speech": "toki ike", "gui.banned.reason.hate_terrorism_notorious_figure": "toki pi kulupu ike anu jan ike", "gui.banned.reason.imminent_harm_to_person_or_property": "wile pakala pi jan lon anu ijo ante lon", "gui.banned.reason.nudity_or_pornography": "sina pana e sitelen unpa anu sitelen pi jaki ante", "gui.banned.reason.sexually_inappropriate": "ijo unpa", "gui.banned.reason.spam_or_advertising": "toki pi mute ike anu toki pi alasa esun", "gui.banned.skin.description": "len sina li ike tawa kulupu Mojang. sike ken awen musi kepeken len open. ante la, sina ken kepeken len sin.\n\no sona namako anu pana e lipu sona lon ni: %s", "gui.banned.skin.title": "selo sina li ike", "gui.banned.title.permanent": "nimi sina li weka lon tenpo ale", "gui.banned.title.temporary": "nimi sina li weka lon tenpo lili", "gui.cancel": "o ala", "gui.chatReport.comments": "toki namako sina", "gui.chatReport.describe": "sina pana e sona suli la kulupu lawa li ken lawa pona", "gui.chatReport.discard.content": "sina weka la, pana sina en toki sina kin li weka. sina wile ala wile ni?", "gui.chatReport.discard.discard": "o weka", "gui.chatReport.discard.draft": "o awen e ni tawa tenpo kama", "gui.chatReport.discard.return": "o awen sitelen", "gui.chatReport.discard.title": "o weka e pana sina?", "gui.chatReport.draft.content": "sina wile ala wile awen e pana ni? ante la sina ken weka e ona li pali e ona sin", "gui.chatReport.draft.discard": "o weka", "gui.chatReport.draft.edit": "o awen sitelen", "gui.chatReport.draft.quittotitle.content": "sina wile awen sitelen e ni anu seme?", "gui.chatReport.draft.quittotitle.title": "sina open e pana pi toki ike! sina pini musi la, pana ni li pini kin", "gui.chatReport.draft.title": "o ante e pana ni anu seme?", "gui.chatReport.more_comments": "seme li kama? o toki e ni:", "gui.chatReport.observed_what": "sina pana e toki ni tan seme?", "gui.chatReport.read_info": "o kama sona e pana toki", "gui.chatReport.report_sent_msg": "kulupu lawa li jo e pana sina. sina pona!\n\nkulupu li lukin e ona lon tenpo poka ken.", "gui.chatReport.select_chat": "o wile e toki tawa pana ni", "gui.chatReport.select_reason": "o wile e kulupu tawa pana ni", "gui.chatReport.selected_chat": "toki ni li ike tawa sina: %s", "gui.chatReport.send": "o pana e toki", "gui.chatReport.send.comments_too_long": "o lili e toki", "gui.chatReport.send.no_reason": "o wile e kulupu tawa pana ni", "gui.chatReport.send.no_reported_messages": "sina wile pana e toki la, o wile e toki wan", "gui.chatReport.send.too_many_messages": "sina alasa pana e toki pi mute ike lon toki ni", "gui.chatReport.title": "o pana e toki pi jan ike", "gui.chatSelection.context": "pana li poki e toki namako lon poka pi toki ni, tawa sona namako", "gui.chatSelection.fold": "toki %s li len", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s li open toki", "gui.chatSelection.message.narrate": "%s li toki e '%s' lon %s", "gui.chatSelection.selected": "sina wile e toki %s/%s", "gui.chatSelection.title": "o wile e toki tawa pana sina", "gui.continue": "o awen tawa", "gui.copy_link_to_clipboard": "o pana e sitelen nasin ni tawa poki sama", "gui.days": "sike suno %s", "gui.done": "pini", "gui.down": "tawa anpa", "gui.entity_tooltip.type": "nasin: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "lipu %s li pakala li kama ala", "gui.fileDropFailure.title": "mi ken ala pana e poki", "gui.hours": "leko tenpo %s", "gui.loadingMinecraft": "musi <PERSON> li kama", "gui.minutes": "tenpo lili %s", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "nena %s", "gui.narrate.editBox": "poki ante %s: %s", "gui.narrate.slider": "ilo linja %s", "gui.narrate.tab": "lipu %s", "gui.no": "ala", "gui.none": "ala", "gui.ok": "pona", "gui.open_report_dir": "o open e poki toki", "gui.proceed": "o awen tawa", "gui.recipebook.moreRecipes": "o kepeken nena nanpa tu tawa lukin namako", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "o alasa...", "gui.recipebook.toggleRecipes.all": "ijo ale", "gui.recipebook.toggleRecipes.blastable": "ijo tan poki seli kiwen", "gui.recipebook.toggleRecipes.craftable": "ijo tan supa pali", "gui.recipebook.toggleRecipes.smeltable": "ijo tan poki seli", "gui.recipebook.toggleRecipes.smokable": "ijo tan poki pi seli moku", "gui.report_to_server": "o toki tawa ma kulupu", "gui.socialInteractions.blocking_hint": "o lawa kepeken nimi pi kulupu Microsoft", "gui.socialInteractions.empty_blocked": "jan toki ala li weka tawa sina", "gui.socialInteractions.empty_hidden": "jan toki ala li len", "gui.socialInteractions.hidden_in_chat": "toki tan %s li kama len", "gui.socialInteractions.hide": "o len lon toki", "gui.socialInteractions.narration.hide": "o len e toki tan jan %s", "gui.socialInteractions.narration.report": "o pana e nimi pi jan %s", "gui.socialInteractions.narration.show": "o lukin e toki tan jan %s", "gui.socialInteractions.report": "toki pana", "gui.socialInteractions.search_empty": "jan pi nimi ni li lon ala", "gui.socialInteractions.search_hint": "o alasa...", "gui.socialInteractions.server_label.multiple": "%s - jan %s", "gui.socialInteractions.server_label.single": "%s - %s jan", "gui.socialInteractions.show": "o lukin lon toki", "gui.socialInteractions.shown_in_chat": "toki tan %s li kama len ala", "gui.socialInteractions.status_blocked": "weka tawa sina", "gui.socialInteractions.status_blocked_offline": "weka tawa sina - weka", "gui.socialInteractions.status_hidden": "len", "gui.socialInteractions.status_hidden_offline": "len - weka", "gui.socialInteractions.status_offline": "weka", "gui.socialInteractions.tab_all": "ale", "gui.socialInteractions.tab_blocked": "weka tawa sina", "gui.socialInteractions.tab_hidden": "len", "gui.socialInteractions.title": "toki sijelo", "gui.socialInteractions.tooltip.hide": "o len e toki", "gui.socialInteractions.tooltip.report": "o pana e sona pi ijo ike pi jan ni", "gui.socialInteractions.tooltip.report.disabled": "ilo pi pana toki li lon ala", "gui.socialInteractions.tooltip.report.no_messages": "toki ala pi ken pana li lon tan jan %s", "gui.socialInteractions.tooltip.report.not_reportable": "sina ken ala pana e sona pi toki ni: ma kulupu ni li sona ala e pona pi toki jan", "gui.socialInteractions.tooltip.show": "o lukin e toki", "gui.stats": "sona nanpa", "gui.toMenu": "o tawa lipu pi ma kulupu", "gui.toRealms": "o tawa lipu pi ma Realms", "gui.toTitle": "o tawa lipu open", "gui.toWorld": "o tawa lipu ma", "gui.togglable_slot": "o weka e poki", "gui.up": "tawa sewi", "gui.waitingForResponse.button.inactive": "o tawa monsi (%ss)", "gui.waitingForResponse.title": "mi awen tawa ma kulupu", "gui.yes": "wile", "hanging_sign.edit": "o sitelen lon lipu sitelen sewi", "instrument.minecraft.admire_goat_horn": "olin", "instrument.minecraft.call_goat_horn": "kalama", "instrument.minecraft.dream_goat_horn": "lukin", "instrument.minecraft.feel_goat_horn": "pilin", "instrument.minecraft.ponder_goat_horn": "sona", "instrument.minecraft.seek_goat_horn": "alasa", "instrument.minecraft.sing_goat_horn": "mu", "instrument.minecraft.yearn_goat_horn": "wile", "inventory.binSlot": "o weka e ijo", "inventory.hotbarInfo": "o poki awen e kulupu ijo kepeken %1$s+%2$s", "inventory.hotbarSaved": "linja poki ilo li awen (o lon e ni kepeken nena %1$s+%2$s)", "item.canBreak": "ni li ken pakala e ni:", "item.canPlace": "sina ken pana e ni lon:", "item.canUse.unknown": "sona ala", "item.color": "kule: %s", "item.components": "kipisi %s", "item.disabled": "ijo pi ken ala", "item.durability": "mute pona: %s / %s", "item.dyed": "kule", "item.minecraft.acacia_boat": "ilo tawa telo pi kasi seli", "item.minecraft.acacia_chest_boat": "poki lon ilo tawa telo pi kasi seli", "item.minecraft.allay_spawn_egg": "sike mama pi kon pona", "item.minecraft.amethyst_shard": "kipisi pi kiwen kalama", "item.minecraft.angler_pottery_shard": "kipisi poki kala", "item.minecraft.angler_pottery_sherd": "kipisi poki kala", "item.minecraft.apple": "kili loje", "item.minecraft.archer_pottery_shard": "kipisi poki alasa", "item.minecraft.archer_pottery_sherd": "kipisi poki alasa", "item.minecraft.armadillo_scute": "kiwen selo tan soweli pi kiwen selo", "item.minecraft.armadillo_spawn_egg": "sike mama pi soweli pi kiwen selo", "item.minecraft.armor_stand": "ilo jo len", "item.minecraft.arms_up_pottery_shard": "kipisi poki wawa", "item.minecraft.arms_up_pottery_sherd": "kipisi poki wawa", "item.minecraft.arrow": "palisa alasa", "item.minecraft.axolotl_bucket": "akesi suwi lon poki", "item.minecraft.axolotl_spawn_egg": "sike mama pi akesi suwi", "item.minecraft.baked_potato": "kili ma seli", "item.minecraft.bamboo_chest_raft": "poki lon supa tawa telo pi kasi palisa", "item.minecraft.bamboo_raft": "supa tawa telo pi kasi palisa", "item.minecraft.bat_spawn_egg": "sike mama pi waso pimeja", "item.minecraft.bee_spawn_egg": "sike mama pi pipi suwi", "item.minecraft.beef": "moku mani lete", "item.minecraft.beetroot": "kili ma loje", "item.minecraft.beetroot_seeds": "sike mama pi kili ma loje", "item.minecraft.beetroot_soup": "telo moku pi kili ma loje", "item.minecraft.birch_boat": "ilo tawa telo pi kasi jelo", "item.minecraft.birch_chest_boat": "poki lon ilo tawa telo pi kasi jelo", "item.minecraft.black_bundle": "poki len pimeja", "item.minecraft.black_dye": "kule pimeja", "item.minecraft.black_harness": "len sewi waso pimeja", "item.minecraft.blade_pottery_shard": "kipisi poki utala", "item.minecraft.blade_pottery_sherd": "kipisi poki utala", "item.minecraft.blaze_powder": "ko pi palisa seli", "item.minecraft.blaze_rod": "palisa seli", "item.minecraft.blaze_spawn_egg": "sike mama pi monsuta seli", "item.minecraft.blue_bundle": "poki len pi laso telo", "item.minecraft.blue_dye": "kule pi laso telo", "item.minecraft.blue_egg": "sike mama waso laso", "item.minecraft.blue_harness": "len sewi waso pi laso telo", "item.minecraft.bogged_spawn_egg": "sike mama pi jan moli jaki", "item.minecraft.bolt_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.bolt_armor_trim_smithing_template.new": "namako len tawa", "item.minecraft.bone": "palisa sijelo", "item.minecraft.bone_meal": "ko pi palisa sijelo", "item.minecraft.book": "lipu suli", "item.minecraft.bordure_indented_banner_pattern": "ilo sitelen pi selo nena", "item.minecraft.bow": "ilo alasa", "item.minecraft.bowl": "poki moku", "item.minecraft.bread": "pan", "item.minecraft.breeze_rod": "palisa kon", "item.minecraft.breeze_spawn_egg": "sike mama pi monsuta kon", "item.minecraft.brewer_pottery_shard": "kipisi poki telo", "item.minecraft.brewer_pottery_sherd": "kipisi poki telo", "item.minecraft.brewing_stand": "ilo pi telo wawa", "item.minecraft.brick": "leko lili", "item.minecraft.brown_bundle": "poki len pi loje ma", "item.minecraft.brown_dye": "kule pi loje ma", "item.minecraft.brown_egg": "sike mama waso loje", "item.minecraft.brown_harness": "len sewi waso pi loje ma", "item.minecraft.brush": "ilo pi weka ko", "item.minecraft.bucket": "poki telo", "item.minecraft.bundle": "poki len", "item.minecraft.bundle.empty": "jo ala", "item.minecraft.bundle.empty.description": "ni li ken jo e kulupu pi ijo mute ante", "item.minecraft.bundle.full": "jo ale", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "kipisi poki seli", "item.minecraft.burn_pottery_sherd": "kipisi poki seli", "item.minecraft.camel_spawn_egg": "sike mama pi soweli pi nena monsi", "item.minecraft.carrot": "kili palisa", "item.minecraft.carrot_on_a_stick": "kili palisa lon palisa", "item.minecraft.cat_spawn_egg": "sike mama pi soweli suwi", "item.minecraft.cauldron": "poki telo suli", "item.minecraft.cave_spider_spawn_egg": "sike mama pi pipi lupa", "item.minecraft.chainmail_boots": "len noka anpa pi sike lili", "item.minecraft.chainmail_chestplate": "len sijelo pi sike lili", "item.minecraft.chainmail_helmet": "len lawa pi sike lili", "item.minecraft.chainmail_leggings": "len noka pi sike lili", "item.minecraft.charcoal": "kiwen kasi pi moku seli", "item.minecraft.cherry_boat": "ilo tawa telo pi kasi suwi", "item.minecraft.cherry_chest_boat": "poki lon ilo tawa telo pi kasi suwi", "item.minecraft.chest_minecart": "poki ijo lon poki tawa", "item.minecraft.chicken": "moku waso lete", "item.minecraft.chicken_spawn_egg": "sike mama pi waso walo", "item.minecraft.chorus_fruit": "kili nasa", "item.minecraft.clay_ball": "sike pi ma ko", "item.minecraft.clock": "ilo tenpo", "item.minecraft.coal": "ko pimeja", "item.minecraft.coast_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.coast_armor_trim_smithing_template.new": "namako len telo", "item.minecraft.cocoa_beans": "kili pi kule ma", "item.minecraft.cod": "kala walo lete", "item.minecraft.cod_bucket": "kala walo lon poki", "item.minecraft.cod_spawn_egg": "sike mama pi kala walo", "item.minecraft.command_block_minecart": "ilo pi toki wawa lon poki tawa", "item.minecraft.compass": "ilo nasin", "item.minecraft.cooked_beef": "moku mani seli", "item.minecraft.cooked_chicken": "moku waso seli", "item.minecraft.cooked_cod": "moku seli pi kala walo", "item.minecraft.cooked_mutton": "moku seli pi soweli len", "item.minecraft.cooked_porkchop": "moku seli pi soweli moku", "item.minecraft.cooked_rabbit": "moku seli pi soweli lili", "item.minecraft.cooked_salmon": "moku seli pi kala loje", "item.minecraft.cookie": "pan sike suwi", "item.minecraft.copper_ingot": "kiwen ante", "item.minecraft.cow_spawn_egg": "sike mama mani", "item.minecraft.creaking_spawn_egg": "sike mama pi monsuta kasi", "item.minecraft.creeper_banner_pattern": "ilo sitelen", "item.minecraft.creeper_banner_pattern.desc": "sinpin pi monsuta <PERSON>per", "item.minecraft.creeper_banner_pattern.new": "ilo sitelen pi monsuta Creeper", "item.minecraft.creeper_spawn_egg": "sike mama pi monsuta <PERSON>", "item.minecraft.crossbow": "ilo alasa supa", "item.minecraft.crossbow.projectile": "ijo pana:", "item.minecraft.crossbow.projectile.multiple": "ijo pana: %s x %s", "item.minecraft.crossbow.projectile.single": "ijo pana: %s", "item.minecraft.cyan_bundle": "poki len laso", "item.minecraft.cyan_dye": "kule laso", "item.minecraft.cyan_harness": "len sewi waso laso", "item.minecraft.danger_pottery_shard": "kipisi poki monsuta", "item.minecraft.danger_pottery_sherd": "kipisi poki monsuta", "item.minecraft.dark_oak_boat": "ilo tawa telo pi kasi pimeja", "item.minecraft.dark_oak_chest_boat": "poki lon ilo tawa telo pi kasi pimeja", "item.minecraft.debug_stick": "palisa wawa", "item.minecraft.debug_stick.empty": "%s li jo e ijo ala", "item.minecraft.debug_stick.select": "nasin \"%s\" la \"%s\" li lon", "item.minecraft.debug_stick.update": "\"%s\" li kama %s", "item.minecraft.diamond": "kiwen laso", "item.minecraft.diamond_axe": "ilo kipisi laso", "item.minecraft.diamond_boots": "len noka anpa laso", "item.minecraft.diamond_chestplate": "len sijelo laso", "item.minecraft.diamond_helmet": "len lawa laso", "item.minecraft.diamond_hoe": "ilo kasi laso", "item.minecraft.diamond_horse_armor": "len soweli laso", "item.minecraft.diamond_leggings": "len noka laso", "item.minecraft.diamond_pickaxe": "ilo pakala laso", "item.minecraft.diamond_shovel": "ilo ma laso", "item.minecraft.diamond_sword": "palisa utala laso", "item.minecraft.disc_fragment_5": "kipisi pi sike kalama", "item.minecraft.disc_fragment_5.desc": "sike kalama - 5", "item.minecraft.dolphin_spawn_egg": "sike mama pi kala suli", "item.minecraft.donkey_spawn_egg": "sike mama pi soweli jo", "item.minecraft.dragon_breath": "kon pi akesi pini", "item.minecraft.dried_kelp": "kasi telo seli", "item.minecraft.drowned_spawn_egg": "sike mama pi jan moli telo", "item.minecraft.dune_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.dune_armor_trim_smithing_template.new": "namako len seli", "item.minecraft.echo_shard": "kipisi pi kalama sama", "item.minecraft.egg": "sike mama waso walo", "item.minecraft.elder_guardian_spawn_egg": "sike mama pi kala oko suli", "item.minecraft.elytra": "luka waso", "item.minecraft.emerald": "kiwen esun", "item.minecraft.enchanted_book": "lipu wawa", "item.minecraft.enchanted_golden_apple": "kili mani wawa", "item.minecraft.end_crystal": "ilo wawa pi ma End", "item.minecraft.ender_dragon_spawn_egg": "sike mama pi akesi pi ma End", "item.minecraft.ender_eye": "oko pi ma End", "item.minecraft.ender_pearl": "sike pi ma End", "item.minecraft.enderman_spawn_egg": "sike mama pi jan pi ma End", "item.minecraft.endermite_spawn_egg": "sike mama pi pipi pi ma End", "item.minecraft.evoker_spawn_egg": "sike mama pi jan ike wawa", "item.minecraft.experience_bottle": "wawa laso lon poki", "item.minecraft.explorer_pottery_shard": "kipisi poki lipu", "item.minecraft.explorer_pottery_sherd": "kipisi poki lipu", "item.minecraft.eye_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.eye_armor_trim_smithing_template.new": "namako len oko", "item.minecraft.feather": "lipu waso", "item.minecraft.fermented_spider_eye": "oko pipi nasa", "item.minecraft.field_masoned_banner_pattern": "ilo sitelen pi leko lili", "item.minecraft.filled_map": "sitelen ma", "item.minecraft.fire_charge": "sike seli", "item.minecraft.firework_rocket": "palisa pi seli kule", "item.minecraft.firework_rocket.flight": "wawa tawa:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "sike pi seli kule", "item.minecraft.firework_star.black": "pime<PERSON>", "item.minecraft.firework_star.blue": "laso telo", "item.minecraft.firework_star.brown": "loje ma", "item.minecraft.firework_star.custom_color": "kule ante", "item.minecraft.firework_star.cyan": "laso", "item.minecraft.firework_star.fade_to": "kama", "item.minecraft.firework_star.flicker": "tawa mun", "item.minecraft.firework_star.gray": "pimeja walo", "item.minecraft.firework_star.green": "laso kasi", "item.minecraft.firework_star.light_blue": "laso sewi", "item.minecraft.firework_star.light_gray": "walo pimeja", "item.minecraft.firework_star.lime": "laso jelo", "item.minecraft.firework_star.magenta": "loje laso", "item.minecraft.firework_star.orange": "loje jelo", "item.minecraft.firework_star.pink": "loje walo", "item.minecraft.firework_star.purple": "laso loje", "item.minecraft.firework_star.red": "loje", "item.minecraft.firework_star.shape": "selo ante", "item.minecraft.firework_star.shape.burst": "pakala wawa", "item.minecraft.firework_star.shape.creeper": "sinpin pi monsuta <PERSON>per", "item.minecraft.firework_star.shape.large_ball": "sike suli", "item.minecraft.firework_star.shape.small_ball": "sike lili", "item.minecraft.firework_star.shape.star": "selo mun", "item.minecraft.firework_star.trail": "linja suno", "item.minecraft.firework_star.white": "walo", "item.minecraft.firework_star.yellow": "jelo", "item.minecraft.fishing_rod": "ilo kala", "item.minecraft.flint": "kiwen pimeja kipisi", "item.minecraft.flint_and_steel": "ilo seli", "item.minecraft.flow_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.flow_armor_trim_smithing_template.new": "namako len esun", "item.minecraft.flow_banner_pattern": "ilo sitelen", "item.minecraft.flow_banner_pattern.desc": "esun", "item.minecraft.flow_banner_pattern.new": "ilo sitelen esun", "item.minecraft.flow_pottery_sherd": "kipisi poki esun", "item.minecraft.flower_banner_pattern": "ilo sitelen pi kasi kule", "item.minecraft.flower_banner_pattern.desc": "kasi kule", "item.minecraft.flower_banner_pattern.new": "ilo sitelen pi kasi kule", "item.minecraft.flower_pot": "poki kasi", "item.minecraft.fox_spawn_egg": "sike mama pi soweli pi linja monsi", "item.minecraft.friend_pottery_shard": "kipisi poki pona", "item.minecraft.friend_pottery_sherd": "kipisi poki pona", "item.minecraft.frog_spawn_egg": "sike mama pi akesi sewi", "item.minecraft.furnace_minecart": "poki seli lon poki tawa", "item.minecraft.ghast_spawn_egg": "sike mama pi monsu<PERSON>", "item.minecraft.ghast_tear": "telo oko pi monsuta <PERSON>", "item.minecraft.glass_bottle": "poki lili", "item.minecraft.glistering_melon_slice": "kipisi kili mani", "item.minecraft.globe_banner_pattern": "ilo sitelen pi leko ma", "item.minecraft.globe_banner_pattern.desc": "sike pi ma ale", "item.minecraft.globe_banner_pattern.new": "ilo sitelen pi leko ma", "item.minecraft.glow_berries": "kili lili suno", "item.minecraft.glow_ink_sac": "telo suno", "item.minecraft.glow_item_frame": "lipu poki suno", "item.minecraft.glow_squid_spawn_egg": "sike mama pi kala luka suno", "item.minecraft.glowstone_dust": "ko suno", "item.minecraft.goat_horn": "palisa kalama pi soweli nena", "item.minecraft.goat_spawn_egg": "sike mama pi soweli nena", "item.minecraft.gold_ingot": "kiwen jelo", "item.minecraft.gold_nugget": "kiwen jelo lili", "item.minecraft.golden_apple": "kili mani", "item.minecraft.golden_axe": "ilo kipisi jelo", "item.minecraft.golden_boots": "len noka anpa jelo", "item.minecraft.golden_carrot": "kili palisa mani", "item.minecraft.golden_chestplate": "len sijelo jelo", "item.minecraft.golden_helmet": "len lawa jelo", "item.minecraft.golden_hoe": "ilo kasi jelo", "item.minecraft.golden_horse_armor": "len soweli jelo", "item.minecraft.golden_leggings": "len noka jelo", "item.minecraft.golden_pickaxe": "ilo pakala jelo", "item.minecraft.golden_shovel": "ilo ma jelo", "item.minecraft.golden_sword": "palisa utala jelo", "item.minecraft.gray_bundle": "poki len pi pimeja walo", "item.minecraft.gray_dye": "kule pi pimeja walo", "item.minecraft.gray_harness": "len sewi waso pi pimeja walo", "item.minecraft.green_bundle": "poki len pi laso kasi", "item.minecraft.green_dye": "kule kasi", "item.minecraft.green_harness": "len sewi waso pi laso kasi", "item.minecraft.guardian_spawn_egg": "sike mama pi kala oko", "item.minecraft.gunpowder": "ko pi seli pakala", "item.minecraft.guster_banner_pattern": "ilo sitelen", "item.minecraft.guster_banner_pattern.desc": "monsuta kon", "item.minecraft.guster_banner_pattern.new": "ilo sitelen pi monsuta kon", "item.minecraft.guster_pottery_sherd": "kipisi poki pi monsuta kon", "item.minecraft.happy_ghast_spawn_egg": "sike mama pi waso pona Ghast", "item.minecraft.harness": "len sewi pi waso pona", "item.minecraft.heart_of_the_sea": "pilin pi telo suli", "item.minecraft.heart_pottery_shard": "kipisi poki pilin", "item.minecraft.heart_pottery_sherd": "kipisi poki pilin", "item.minecraft.heartbreak_pottery_shard": "kipisi poki pakala", "item.minecraft.heartbreak_pottery_sherd": "kipisi poki pakala", "item.minecraft.hoglin_spawn_egg": "sike mama pi soweli <PERSON>n", "item.minecraft.honey_bottle": "suwi pipi lon poki", "item.minecraft.honeycomb": "<PERSON>u suwi", "item.minecraft.hopper_minecart": "poki pi tawa ijo lon poki tawa", "item.minecraft.horse_spawn_egg": "sike mama pi soweli tawa", "item.minecraft.host_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.host_armor_trim_smithing_template.new": "namako len lawa", "item.minecraft.howl_pottery_shard": "kipisi poki soweli", "item.minecraft.howl_pottery_sherd": "kipisi poki soweli", "item.minecraft.husk_spawn_egg": "sike mama pi jan moli seli", "item.minecraft.ink_sac": "telo pimeja", "item.minecraft.iron_axe": "ilo kipisi walo", "item.minecraft.iron_boots": "len noka anpa walo", "item.minecraft.iron_chestplate": "len sijelo walo", "item.minecraft.iron_golem_spawn_egg": "sike mama pi jan ilo pi kiwen walo", "item.minecraft.iron_helmet": "len lawa walo", "item.minecraft.iron_hoe": "ilo kasi walo", "item.minecraft.iron_horse_armor": "len soweli walo", "item.minecraft.iron_ingot": "kiwen walo", "item.minecraft.iron_leggings": "len noka walo", "item.minecraft.iron_nugget": "kiwen walo lili", "item.minecraft.iron_pickaxe": "ilo pakala walo", "item.minecraft.iron_shovel": "ilo ma walo", "item.minecraft.iron_sword": "palisa utala walo", "item.minecraft.item_frame": "lipu poki", "item.minecraft.jungle_boat": "ilo tawa telo pi kasi suli", "item.minecraft.jungle_chest_boat": "poki lon ilo tawa telo pi kasi suli", "item.minecraft.knowledge_book": "lipu pi sona pali", "item.minecraft.lapis_lazuli": "kiwen pi laso telo", "item.minecraft.lava_bucket": "telo seli lon poki", "item.minecraft.lead": "linja lawa", "item.minecraft.leather": "selo soweli", "item.minecraft.leather_boots": "len noka anpa selo", "item.minecraft.leather_chestplate": "len sijelo selo", "item.minecraft.leather_helmet": "len lawa selo", "item.minecraft.leather_horse_armor": "len soweli selo", "item.minecraft.leather_leggings": "len noka selo", "item.minecraft.light_blue_bundle": "poki len pi laso sewi", "item.minecraft.light_blue_dye": "kule pi laso sewi", "item.minecraft.light_blue_harness": "len sewi waso pi laso sewi", "item.minecraft.light_gray_bundle": "poki len pi walo pimeja", "item.minecraft.light_gray_dye": "kule pi walo pimeja", "item.minecraft.light_gray_harness": "len sewi waso pi walo pimeja", "item.minecraft.lime_bundle": "poki len pi laso jelo", "item.minecraft.lime_dye": "kule pi laso jelo", "item.minecraft.lime_harness": "len sewi waso pi laso jelo", "item.minecraft.lingering_potion": "kon wawa", "item.minecraft.lingering_potion.effect.awkward": "kon wawa pi soko wawa", "item.minecraft.lingering_potion.effect.empty": "kon wawa pi ken ala pali", "item.minecraft.lingering_potion.effect.fire_resistance": "kon wawa pi awen lete", "item.minecraft.lingering_potion.effect.harming": "kon wawa pi ike sijelo", "item.minecraft.lingering_potion.effect.healing": "kon wawa pi pona sijelo", "item.minecraft.lingering_potion.effect.infested": "kon wawa pi kama pipi", "item.minecraft.lingering_potion.effect.invisibility": "kon wawa pi sijelo kon", "item.minecraft.lingering_potion.effect.leaping": "kon wawa pi noka wawa", "item.minecraft.lingering_potion.effect.levitation": "kon wawa pi tawa sewi", "item.minecraft.lingering_potion.effect.luck": "kon wawa pi ken pona", "item.minecraft.lingering_potion.effect.mundane": "kon wawa pi wawa lili", "item.minecraft.lingering_potion.effect.night_vision": "kon wawa pi lukin wawa", "item.minecraft.lingering_potion.effect.oozing": "kon wawa pi pana ko", "item.minecraft.lingering_potion.effect.poison": "kon wawa pi ike sijelo kama", "item.minecraft.lingering_potion.effect.regeneration": "kon wawa pi pona sijelo kama", "item.minecraft.lingering_potion.effect.slow_falling": "kon wawa pi anpa ala", "item.minecraft.lingering_potion.effect.slowness": "kon wawa pi tawa ike", "item.minecraft.lingering_potion.effect.strength": "kon wawa pi utala wawa", "item.minecraft.lingering_potion.effect.swiftness": "kon wawa pi tawa wawa", "item.minecraft.lingering_potion.effect.thick": "kon wawa pi wawa lili", "item.minecraft.lingering_potion.effect.turtle_master": "kon wawa pi kama kiwen", "item.minecraft.lingering_potion.effect.water": "telo kon", "item.minecraft.lingering_potion.effect.water_breathing": "kon wawa pi kon telo", "item.minecraft.lingering_potion.effect.weakness": "kon wawa pi utala ike", "item.minecraft.lingering_potion.effect.weaving": "kon wawa pi kama linja", "item.minecraft.lingering_potion.effect.wind_charged": "kon wawa pi pana kon", "item.minecraft.llama_spawn_egg": "sike mama pi soweli pi telo uta", "item.minecraft.lodestone_compass": "ilo nasin pi kiwen nasin", "item.minecraft.mace": "palisa wawa", "item.minecraft.magenta_bundle": "poki len pi loje laso", "item.minecraft.magenta_dye": "kule pi loje laso", "item.minecraft.magenta_harness": "len sewi waso pi loje laso", "item.minecraft.magma_cream": "ko monsuta seli", "item.minecraft.magma_cube_spawn_egg": "sike mama pi monsuta ko seli", "item.minecraft.mangrove_boat": "ilo tawa telo pi kasi telo", "item.minecraft.mangrove_chest_boat": "poki lon ilo tawa telo pi kasi telo", "item.minecraft.map": "sitelen ma weka", "item.minecraft.melon_seeds": "sike mama pi kili suli laso", "item.minecraft.melon_slice": "kipisi pi kili suli laso", "item.minecraft.milk_bucket": "telo walo lon poki", "item.minecraft.minecart": "poki tawa", "item.minecraft.miner_pottery_shard": "kipisi poki ilo", "item.minecraft.miner_pottery_sherd": "kipisi poki ilo", "item.minecraft.mojang_banner_pattern": "ilo sitelen ijo", "item.minecraft.mojang_banner_pattern.desc": "ijo", "item.minecraft.mojang_banner_pattern.new": "ilo sitelen ijo", "item.minecraft.mooshroom_spawn_egg": "sike mama pi mani <PERSON>", "item.minecraft.mourner_pottery_shard": "kipisi poki kute", "item.minecraft.mourner_pottery_sherd": "kipisi poki kute", "item.minecraft.mule_spawn_egg": "sike mama pi soweli jo tawa", "item.minecraft.mushroom_stew": "telo moku soko", "item.minecraft.music_disc_11": "sike kalama", "item.minecraft.music_disc_11.desc": "jan C418 - 11", "item.minecraft.music_disc_13": "sike kalama", "item.minecraft.music_disc_13.desc": "jan C418 - 13", "item.minecraft.music_disc_5": "sike kalama", "item.minecraft.music_disc_5.desc": "j<PERSON> <PERSON> - 5", "item.minecraft.music_disc_blocks": "sike kalama", "item.minecraft.music_disc_blocks.desc": "jan C418 - blocks", "item.minecraft.music_disc_cat": "sike kalama", "item.minecraft.music_disc_cat.desc": "jan C418 - cat", "item.minecraft.music_disc_chirp": "sike kalama", "item.minecraft.music_disc_chirp.desc": "jan C418 - chirp", "item.minecraft.music_disc_creator": "sike kalama", "item.minecraft.music_disc_creator.desc": "<PERSON><PERSON> <PERSON> - C<PERSON>", "item.minecraft.music_disc_creator_music_box": "sike kalama", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON><PERSON> <PERSON> - Creator (kepeken poki pi kalama musi)", "item.minecraft.music_disc_far": "sike kalama", "item.minecraft.music_disc_far.desc": "jan C418 - far", "item.minecraft.music_disc_lava_chicken": "sike kalama", "item.minecraft.music_disc_lava_chicken.desc": "kulupu Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "sike kalama", "item.minecraft.music_disc_mall.desc": "jan C418 - mall", "item.minecraft.music_disc_mellohi": "sike kalama", "item.minecraft.music_disc_mellohi.desc": "jan C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "sike kalama", "item.minecraft.music_disc_otherside.desc": "j<PERSON> - otherside", "item.minecraft.music_disc_pigstep": "sike kalama", "item.minecraft.music_disc_pigstep.desc": "j<PERSON> <PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "sike kalama", "item.minecraft.music_disc_precipice.desc": "jan <PERSON> - Precipice", "item.minecraft.music_disc_relic": "sike kalama", "item.minecraft.music_disc_relic.desc": "jan <PERSON>", "item.minecraft.music_disc_stal": "sike kalama", "item.minecraft.music_disc_stal.desc": "jan C418 - stal", "item.minecraft.music_disc_strad": "sike kalama", "item.minecraft.music_disc_strad.desc": "jan C418 - strad", "item.minecraft.music_disc_tears": "sike kalama", "item.minecraft.music_disc_tears.desc": "jan <PERSON> - Tears", "item.minecraft.music_disc_wait": "sike kalama", "item.minecraft.music_disc_wait.desc": "jan C418 - wait", "item.minecraft.music_disc_ward": "sike kalama", "item.minecraft.music_disc_ward.desc": "jan C418 - ward", "item.minecraft.mutton": "moku lete pi soweli len", "item.minecraft.name_tag": "lipu nimi", "item.minecraft.nautilus_shell": "tomo kala", "item.minecraft.nether_brick": "kiwen leko lili pi ma Nether", "item.minecraft.nether_star": "mun pi ma <PERSON>her", "item.minecraft.nether_wart": "soko wawa pi ma Nether", "item.minecraft.netherite_axe": "ilo kipisi pi mani Netherite", "item.minecraft.netherite_boots": "len noka anpa pi mani Netherite", "item.minecraft.netherite_chestplate": "len sijelo pi mani <PERSON>herite", "item.minecraft.netherite_helmet": "len lawa pi mani Netherite", "item.minecraft.netherite_hoe": "ilo kasi pi mani Netherite", "item.minecraft.netherite_ingot": "mani <PERSON>ite", "item.minecraft.netherite_leggings": "len noka pi mani Netherite", "item.minecraft.netherite_pickaxe": "ilo pakala pi mani Netherite", "item.minecraft.netherite_scrap": "mani <PERSON>ite pakala", "item.minecraft.netherite_shovel": "ilo ma pi mani Netherite", "item.minecraft.netherite_sword": "palisa utala pi mani Netherite", "item.minecraft.netherite_upgrade_smithing_template": "lipu pi namako len", "item.minecraft.netherite_upgrade_smithing_template.new": "namako len wawa pi mani Netherite", "item.minecraft.oak_boat": "ilo tawa telo pi kasi kili", "item.minecraft.oak_chest_boat": "poki lon ilo tawa telo pi kasi kili", "item.minecraft.ocelot_spawn_egg": "sike mama pi soweli alasa", "item.minecraft.ominous_bottle": "poki lili ike", "item.minecraft.ominous_trial_key": "ilo open ike pi tomo alasa", "item.minecraft.orange_bundle": "poki len pi loje jelo", "item.minecraft.orange_dye": "kule pi loje jelo", "item.minecraft.orange_harness": "len sewi waso pi loje jelo", "item.minecraft.painting": "sitelen", "item.minecraft.pale_oak_boat": "ilo tawa telo pi kasi walo", "item.minecraft.pale_oak_chest_boat": "poki lon ilo tawa telo pi kasi walo", "item.minecraft.panda_spawn_egg": "sike mama pi soweli pimeja walo", "item.minecraft.paper": "lipu", "item.minecraft.parrot_spawn_egg": "sike mama pi waso toki", "item.minecraft.phantom_membrane": "selo pi waso mun", "item.minecraft.phantom_spawn_egg": "sike mama pi waso mun", "item.minecraft.pig_spawn_egg": "sike mama soweli pi loje walo", "item.minecraft.piglin_banner_pattern": "ilo sitelen pi nena soweli", "item.minecraft.piglin_banner_pattern.desc": "nena soweli", "item.minecraft.piglin_banner_pattern.new": "ilo sitelen pi nena soweli", "item.minecraft.piglin_brute_spawn_egg": "sike mama pi jan <PERSON> wawa", "item.minecraft.piglin_spawn_egg": "sike mama pi jan <PERSON>", "item.minecraft.pillager_spawn_egg": "sike mama pi jan ike alasa", "item.minecraft.pink_bundle": "poki len pi loje walo", "item.minecraft.pink_dye": "kule pi loje walo", "item.minecraft.pink_harness": "len sewi waso pi loje walo", "item.minecraft.pitcher_plant": "kasi poki", "item.minecraft.pitcher_pod": "sike mama pi kasi poki", "item.minecraft.plenty_pottery_shard": "kipisi poki poki", "item.minecraft.plenty_pottery_sherd": "kipisi poki poki", "item.minecraft.poisonous_potato": "kili ma jaki", "item.minecraft.polar_bear_spawn_egg": "sike mama pi soweli lete", "item.minecraft.popped_chorus_fruit": "kili nasa seli", "item.minecraft.porkchop": "moku lete pi soweli moku", "item.minecraft.potato": "kili ma", "item.minecraft.potion": "telo wawa", "item.minecraft.potion.effect.awkward": "telo pi soko wawa", "item.minecraft.potion.effect.empty": "telo pi ken ala pali", "item.minecraft.potion.effect.fire_resistance": "telo pi awen lete", "item.minecraft.potion.effect.harming": "telo pi ike sijelo", "item.minecraft.potion.effect.healing": "telo pi pona sijelo", "item.minecraft.potion.effect.infested": "telo pi kama pipi", "item.minecraft.potion.effect.invisibility": "telo pi sijelo kon", "item.minecraft.potion.effect.leaping": "telo pi noka wawa", "item.minecraft.potion.effect.levitation": "telo pi tawa sewi", "item.minecraft.potion.effect.luck": "telo pi ken pona", "item.minecraft.potion.effect.mundane": "telo pi wawa lili", "item.minecraft.potion.effect.night_vision": "telo pi lukin wawa", "item.minecraft.potion.effect.oozing": "telo pi pana ko", "item.minecraft.potion.effect.poison": "telo pi ike sijelo kama", "item.minecraft.potion.effect.regeneration": "telo pi pona sijelo kama", "item.minecraft.potion.effect.slow_falling": "telo pi anpa ala", "item.minecraft.potion.effect.slowness": "telo pi tawa ike", "item.minecraft.potion.effect.strength": "telo pi utala wawa", "item.minecraft.potion.effect.swiftness": "telo pi tawa wawa", "item.minecraft.potion.effect.thick": "telo pi ko suno", "item.minecraft.potion.effect.turtle_master": "telo pi kama kiwen", "item.minecraft.potion.effect.water": "telo lon poki lili", "item.minecraft.potion.effect.water_breathing": "telo pi kon telo", "item.minecraft.potion.effect.weakness": "telo pi utala ike", "item.minecraft.potion.effect.weaving": "telo pi kama linja", "item.minecraft.potion.effect.wind_charged": "telo pi pana kon", "item.minecraft.pottery_shard_archer": "kipisi poki alasa", "item.minecraft.pottery_shard_arms_up": "kipisi poki wawa", "item.minecraft.pottery_shard_prize": "kipisi poki kiwen", "item.minecraft.pottery_shard_skull": "kipisi poki lawa", "item.minecraft.powder_snow_bucket": "ko lete kon lon poki", "item.minecraft.prismarine_crystals": "kipisi suno pi kiwen Prismarine", "item.minecraft.prismarine_shard": "kipisi pi kiwen Prismarine", "item.minecraft.prize_pottery_shard": "kipisi poki kiwen", "item.minecraft.prize_pottery_sherd": "kipisi poki kiwen", "item.minecraft.pufferfish": "kala pi kama suli", "item.minecraft.pufferfish_bucket": "kala pi kama suli lon poki", "item.minecraft.pufferfish_spawn_egg": "sike mama pi kala pi kama suli", "item.minecraft.pumpkin_pie": "pan suwi pi kili suli jelo", "item.minecraft.pumpkin_seeds": "sike mama pi kili suli jelo", "item.minecraft.purple_bundle": "poki len pi laso loje", "item.minecraft.purple_dye": "kule pi laso loje", "item.minecraft.purple_harness": "len sewi waso pi laso loje", "item.minecraft.quartz": "kiwen walo pi ma <PERSON>her", "item.minecraft.rabbit": "moku lete pi soweli lili", "item.minecraft.rabbit_foot": "noka pi soweli lili", "item.minecraft.rabbit_hide": "selo pi soweli lili", "item.minecraft.rabbit_spawn_egg": "sike mama pi soweli lili", "item.minecraft.rabbit_stew": "telo moku pi soweli lili", "item.minecraft.raiser_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.raiser_armor_trim_smithing_template.new": "namako len sewi", "item.minecraft.ravager_spawn_egg": "sike mama pi monsuta suli", "item.minecraft.raw_copper": "kiwen ante lete", "item.minecraft.raw_gold": "kiwen jelo lete", "item.minecraft.raw_iron": "kiwen walo lete", "item.minecraft.recovery_compass": "ilo nasin moli", "item.minecraft.red_bundle": "poki len loje", "item.minecraft.red_dye": "kule loje", "item.minecraft.red_harness": "len sewi waso loje", "item.minecraft.redstone": "ko Redstone", "item.minecraft.resin_brick": "kiwen leko lili pi kiwen ko loje", "item.minecraft.resin_clump": "kiwen ko loje", "item.minecraft.rib_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.rib_armor_trim_smithing_template.new": "namako len palisa", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON> jaki", "item.minecraft.saddle": "len monsi soweli", "item.minecraft.salmon": "kala loje lete", "item.minecraft.salmon_bucket": "kala loje lon poki", "item.minecraft.salmon_spawn_egg": "sike mama pi kala loje", "item.minecraft.scrape_pottery_sherd": "kipisi poki selo", "item.minecraft.scute": "kiwen selo", "item.minecraft.sentry_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.sentry_armor_trim_smithing_template.new": "namako len utala", "item.minecraft.shaper_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.shaper_armor_trim_smithing_template.new": "namako len pali", "item.minecraft.sheaf_pottery_shard": "kipisi poki pan", "item.minecraft.sheaf_pottery_sherd": "kipisi poki pan", "item.minecraft.shears": "ilo kipisi lili", "item.minecraft.sheep_spawn_egg": "sike mama pi soweli len", "item.minecraft.shelter_pottery_shard": "kipisi poki kasi", "item.minecraft.shelter_pottery_sherd": "kipisi poki kasi", "item.minecraft.shield": "sin<PERSON> sijelo", "item.minecraft.shield.black": "sin<PERSON> sijelo p<PERSON>", "item.minecraft.shield.blue": "sinpin sijelo pi laso telo", "item.minecraft.shield.brown": "sinpin sijelo pi loje ma", "item.minecraft.shield.cyan": "sinpin sijelo laso", "item.minecraft.shield.gray": "sinpin sijelo pi pimeja walo", "item.minecraft.shield.green": "sinpin sijelo pi laso kasi", "item.minecraft.shield.light_blue": "sinpin sijelo pi laso sewi", "item.minecraft.shield.light_gray": "sinpin sijelo pi walo pimeja", "item.minecraft.shield.lime": "sinpin sijelo pi laso jelo", "item.minecraft.shield.magenta": "sinpin sijelo pi loje laso", "item.minecraft.shield.orange": "sinpin sijelo pi loje jelo", "item.minecraft.shield.pink": "sinpin sijelo pi loje walo", "item.minecraft.shield.purple": "sinpin sijelo pi laso loje", "item.minecraft.shield.red": "sinpin sijelo loje", "item.minecraft.shield.white": "sinpin sijelo walo", "item.minecraft.shield.yellow": "sinpin sijelo jelo", "item.minecraft.shulker_shell": "selo pi mon<PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "sike mama pi monsu<PERSON>", "item.minecraft.sign": "lipu sitelen", "item.minecraft.silence_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.silence_armor_trim_smithing_template.new": "namako len pi kalama ala", "item.minecraft.silverfish_spawn_egg": "sike mama pi pipi kiwen", "item.minecraft.skeleton_horse_spawn_egg": "sike mama pi soweli tawa palisa", "item.minecraft.skeleton_spawn_egg": "sike mama pi jan moli palisa", "item.minecraft.skull_banner_pattern": "ilo sitelen pi lawa moli", "item.minecraft.skull_banner_pattern.desc": "sinpin pi jan moli palisa", "item.minecraft.skull_banner_pattern.new": "ilo sitelen pi lawa moli", "item.minecraft.skull_pottery_shard": "kipisi poki lawa", "item.minecraft.skull_pottery_sherd": "kipisi poki lawa", "item.minecraft.slime_ball": "ko monsuta", "item.minecraft.slime_spawn_egg": "sike mama pi monsuta ko", "item.minecraft.smithing_template": "lipu pi namako len", "item.minecraft.smithing_template.applies_to": "tawa:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "o pana e kiwen kule", "item.minecraft.smithing_template.armor_trim.applies_to": "len utala", "item.minecraft.smithing_template.armor_trim.base_slot_description": "o pana e len", "item.minecraft.smithing_template.armor_trim.ingredients": "kiwen kule", "item.minecraft.smithing_template.ingredients": "ijo pali:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "o pana e mani Netherite", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "ilo pi kiwen laso", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "o pana e ilo pi kiwen laso anu len pi kiwen laso", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "mani <PERSON>ite", "item.minecraft.smithing_template.upgrade": "namako: ", "item.minecraft.sniffer_spawn_egg": "sike mama pi soweli suli alasa", "item.minecraft.snort_pottery_shard": "kipisi poki akesi", "item.minecraft.snort_pottery_sherd": "kipisi poki akesi", "item.minecraft.snout_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.snout_armor_trim_smithing_template.new": "namako len soweli", "item.minecraft.snow_golem_spawn_egg": "sike mama pi jan pi ko lete", "item.minecraft.snowball": "sike ko lete", "item.minecraft.spectral_arrow": "palisa alasa suno", "item.minecraft.spider_eye": "oko pipi", "item.minecraft.spider_spawn_egg": "sike mama pi pipi noka", "item.minecraft.spire_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.spire_armor_trim_smithing_template.new": "namako len nena", "item.minecraft.splash_potion": "telo wawa pana", "item.minecraft.splash_potion.effect.awkward": "telo pana pi soko wawa", "item.minecraft.splash_potion.effect.empty": "telo pana pi ken ala pali", "item.minecraft.splash_potion.effect.fire_resistance": "telo pana pi awen lete", "item.minecraft.splash_potion.effect.harming": "telo pana pi ike sijelo", "item.minecraft.splash_potion.effect.healing": "telo pana pi pona sijelo", "item.minecraft.splash_potion.effect.infested": "telo pana pi kama pipi", "item.minecraft.splash_potion.effect.invisibility": "telo pana pi sijelo kon", "item.minecraft.splash_potion.effect.leaping": "telo pana pi noka suli", "item.minecraft.splash_potion.effect.levitation": "telo pana pi tawa sewi", "item.minecraft.splash_potion.effect.luck": "telo pana pi pona ken", "item.minecraft.splash_potion.effect.mundane": "telo pana pi wawa lili", "item.minecraft.splash_potion.effect.night_vision": "telo pana pi lukin wawa", "item.minecraft.splash_potion.effect.oozing": "telo pana pi pana ko", "item.minecraft.splash_potion.effect.poison": "telo pana pi ike sijelo kama", "item.minecraft.splash_potion.effect.regeneration": "telo pana pi pona sijelo awen", "item.minecraft.splash_potion.effect.slow_falling": "telo pana pi anpa ala", "item.minecraft.splash_potion.effect.slowness": "telo pana pi tawa ike", "item.minecraft.splash_potion.effect.strength": "telo pana pi utala wawa", "item.minecraft.splash_potion.effect.swiftness": "telo pana pi tawa wawa", "item.minecraft.splash_potion.effect.thick": "telo pana pi wawa lili", "item.minecraft.splash_potion.effect.turtle_master": "telo pana pi kama kiwen", "item.minecraft.splash_potion.effect.water": "telo pana", "item.minecraft.splash_potion.effect.water_breathing": "telo pana pi kon telo", "item.minecraft.splash_potion.effect.weakness": "telo pana pi utala ike", "item.minecraft.splash_potion.effect.weaving": "telo pana pi kama linja", "item.minecraft.splash_potion.effect.wind_charged": "telo pana pi pana kon", "item.minecraft.spruce_boat": "ilo tawa telo pi kasi lete", "item.minecraft.spruce_chest_boat": "poki lon ilo tawa telo pi kasi lete", "item.minecraft.spyglass": "palisa lukin", "item.minecraft.squid_spawn_egg": "sike mama pi kala luka", "item.minecraft.stick": "palisa", "item.minecraft.stone_axe": "ilo kipisi kiwen", "item.minecraft.stone_hoe": "ilo kasi kiwen", "item.minecraft.stone_pickaxe": "ilo pakala kiwen", "item.minecraft.stone_shovel": "ilo ma kiwen", "item.minecraft.stone_sword": "palisa utala kiwen", "item.minecraft.stray_spawn_egg": "sike mama pi jan moli lete", "item.minecraft.strider_spawn_egg": "sike mama pi akesi seli", "item.minecraft.string": "linja", "item.minecraft.sugar": "suwi kasi", "item.minecraft.suspicious_stew": "telo moku nasa", "item.minecraft.sweet_berries": "kili suwi", "item.minecraft.tadpole_bucket": "akesi sewi lili lon poki", "item.minecraft.tadpole_spawn_egg": "sike mama pi akesi sewi lili", "item.minecraft.tide_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.tide_armor_trim_smithing_template.new": "namako len kala", "item.minecraft.tipped_arrow": "palisa alasa wawa", "item.minecraft.tipped_arrow.effect.awkward": "palisa alasa pi wawa nasa", "item.minecraft.tipped_arrow.effect.empty": "palisa alasa wawa pi ken ala pali", "item.minecraft.tipped_arrow.effect.fire_resistance": "palisa alasa pi awen lete", "item.minecraft.tipped_arrow.effect.harming": "palisa alasa pi ike sijelo", "item.minecraft.tipped_arrow.effect.healing": "palisa alasa pi pona sijelo", "item.minecraft.tipped_arrow.effect.infested": "palisa alasa pi kama pipi", "item.minecraft.tipped_arrow.effect.invisibility": "palisa alasa pi sijelo kon", "item.minecraft.tipped_arrow.effect.leaping": "palisa alasa pi noka wawa", "item.minecraft.tipped_arrow.effect.levitation": "palisa alasa pi tawa sewi", "item.minecraft.tipped_arrow.effect.luck": "palisa alasa pi ken pona", "item.minecraft.tipped_arrow.effect.mundane": "palisa alasa pi wawa lili", "item.minecraft.tipped_arrow.effect.night_vision": "palisa alasa pi lukin wawa", "item.minecraft.tipped_arrow.effect.oozing": "palisa alasa pi pana ko", "item.minecraft.tipped_arrow.effect.poison": "palisa alasa pi ike sijelo kama", "item.minecraft.tipped_arrow.effect.regeneration": "palisa alasa pi pona kama sijelo", "item.minecraft.tipped_arrow.effect.slow_falling": "palisa alasa pi anpa ala", "item.minecraft.tipped_arrow.effect.slowness": "palisa alasa pi tawa ike", "item.minecraft.tipped_arrow.effect.strength": "palisa alasa pi utala wawa", "item.minecraft.tipped_arrow.effect.swiftness": "palisa alasa pi tawa wawa", "item.minecraft.tipped_arrow.effect.thick": "palisa alasa pi wawa ala", "item.minecraft.tipped_arrow.effect.turtle_master": "palisa alasa pi kama kiwen", "item.minecraft.tipped_arrow.effect.water": "palisa alasa telo", "item.minecraft.tipped_arrow.effect.water_breathing": "palisa alasa pi kon telo", "item.minecraft.tipped_arrow.effect.weakness": "palisa alasa pi utala ike", "item.minecraft.tipped_arrow.effect.weaving": "palisa alasa pi kama linja", "item.minecraft.tipped_arrow.effect.wind_charged": "palisa alasa pi pana kon", "item.minecraft.tnt_minecart": " seli pakala lon poki tawa", "item.minecraft.torchflower_seeds": "sike mama pi kasi seli", "item.minecraft.totem_of_undying": "ilo pi moli ala", "item.minecraft.trader_llama_spawn_egg": "sike mama pi soweli pi jan esun", "item.minecraft.trial_key": "ilo open pi tomo alasa", "item.minecraft.trident": "ilo alasa telo", "item.minecraft.tropical_fish": "kala kule", "item.minecraft.tropical_fish_bucket": "kala kule lon poki", "item.minecraft.tropical_fish_spawn_egg": "sike mama pi kala kule", "item.minecraft.turtle_helmet": "selo a<PERSON>i", "item.minecraft.turtle_scute": "kiwen selo tan akesi tomo", "item.minecraft.turtle_spawn_egg": "sike mama pi akesi tomo", "item.minecraft.vex_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.vex_armor_trim_smithing_template.new": "namako len kon", "item.minecraft.vex_spawn_egg": "sike mama pi kon ike", "item.minecraft.villager_spawn_egg": "sike mama pi jan tomo", "item.minecraft.vindicator_spawn_egg": "sike mama pi jan ike kipisi", "item.minecraft.wandering_trader_spawn_egg": "sike mama pi jan esun", "item.minecraft.ward_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.ward_armor_trim_smithing_template.new": "namako len awen", "item.minecraft.warden_spawn_egg": "sike mama pi monsuta kute", "item.minecraft.warped_fungus_on_a_stick": "soko lon palisa", "item.minecraft.water_bucket": "telo lon poki", "item.minecraft.wayfinder_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "namako len nasin", "item.minecraft.wheat": "kasi pan", "item.minecraft.wheat_seeds": "sike mama pan", "item.minecraft.white_bundle": "poki len walo", "item.minecraft.white_dye": "kule walo", "item.minecraft.white_harness": "len sewi waso walo", "item.minecraft.wild_armor_trim_smithing_template": "lipu pi namako len", "item.minecraft.wild_armor_trim_smithing_template.new": "namako len kasi", "item.minecraft.wind_charge": "sike kon", "item.minecraft.witch_spawn_egg": "sike mama pi jan pi telo wawa", "item.minecraft.wither_skeleton_spawn_egg": "sike mama pi jan moli palisa <PERSON>", "item.minecraft.wither_spawn_egg": "sike mama pi monsuta <PERSON>", "item.minecraft.wolf_armor": "len pi soweli pona", "item.minecraft.wolf_spawn_egg": "sike mama pi soweli pona", "item.minecraft.wooden_axe": "ilo kipisi kasi", "item.minecraft.wooden_hoe": "ilo kasi tan kasi", "item.minecraft.wooden_pickaxe": "ilo pakala kasi", "item.minecraft.wooden_shovel": "ilo ma kasi", "item.minecraft.wooden_sword": "palisa utala kasi", "item.minecraft.writable_book": "lipu en ilo sitelen", "item.minecraft.written_book": "lipu sitelen", "item.minecraft.yellow_bundle": "poki len jelo", "item.minecraft.yellow_dye": "kule jelo", "item.minecraft.yellow_harness": "len sewi waso jelo", "item.minecraft.zoglin_spawn_egg": "sike mama pi soweli moli Zoglin", "item.minecraft.zombie_horse_spawn_egg": "sike mama pi soweli tawa moli", "item.minecraft.zombie_spawn_egg": "sike mama pi jan moli", "item.minecraft.zombie_villager_spawn_egg": "sike mama pi jan tomo moli", "item.minecraft.zombified_piglin_spawn_egg": "sike mama pi jan <PERSON> moli", "item.modifiers.any": "lon sijelo la:", "item.modifiers.armor": "len la:", "item.modifiers.body": "len la:", "item.modifiers.chest": "lon sijelo la", "item.modifiers.feet": "lon noka la:", "item.modifiers.hand": "lon luka la:", "item.modifiers.head": "lon lawa la", "item.modifiers.legs": "lon noka la", "item.modifiers.mainhand": "lon luka lawa la", "item.modifiers.offhand": "lon luka ante la", "item.modifiers.saddle": "lon monsi soweli la", "item.nbt_tags": "nanpa sona %s li lon", "item.op_block_warning.line1": "o sona e ni:", "item.op_block_warning.line2": "sina kepeken ijo ni la, toki lawa li ken lon", "item.op_block_warning.line3": "sina sona ala e ale insa la o kepeken ala a!", "item.unbreakable": "ken ala pakala", "itemGroup.buildingBlocks": "pali", "itemGroup.coloredBlocks": "leko kule", "itemGroup.combat": "utala", "itemGroup.consumables": "ijo kepeken pi tenpo wan", "itemGroup.crafting": "pali", "itemGroup.foodAndDrink": "moku", "itemGroup.functional": "leko ilo", "itemGroup.hotbar": "mi poki e ijo kulupu ni:", "itemGroup.ingredients": "ijo pali", "itemGroup.inventory": "jo", "itemGroup.natural": "leko tan ma", "itemGroup.op": "ilo pi jan lawa", "itemGroup.redstone": "ijo pi ko Redstone", "itemGroup.search": "o alasa e ijo", "itemGroup.spawnEggs": "sike mama", "itemGroup.tools": "ilo luka", "item_modifier.unknown": "ante ijo \"%s\" li ike", "jigsaw_block.final_state": "ni li kama:", "jigsaw_block.generate": "o pali", "jigsaw_block.joint.aligned": "palisa", "jigsaw_block.joint.rollable": "tawa sike", "jigsaw_block.joint_label": "nasin pali:", "jigsaw_block.keep_jigsaws": "o awen e leko ni lon insa", "jigsaw_block.levels": "mute: %s", "jigsaw_block.name": "nimi:", "jigsaw_block.placement_priority": "suli pana:", "jigsaw_block.placement_priority.tooltip": "leko pi ijo poka li linja e kipisi la, tomo suli la kipisi linja li nasin ni: \n\nijo pi suli mute tawa ijo pi lili mute, ijo suli sama la ijo li nasin tan nasin pana.", "jigsaw_block.pool": "kulupu ijo:", "jigsaw_block.selection_priority": "suli lukin:", "jigsaw_block.selection_priority.tooltip": "mi lukin e poka pi ijo poka mama la, ona li alasa poka tawa leko pi ijo poka kepeken mute pi suli ni.\n\nmi lukin e ijo poka tan mute suli tawa mute lili. mute suli li sama la, mi lukin e ona kepeken nasin ala.", "jigsaw_block.target": "nimi ijo:", "jukebox_song.minecraft.11": "jan C418 - 11", "jukebox_song.minecraft.13": "jan C418 - 13", "jukebox_song.minecraft.5": "j<PERSON> <PERSON> - 5", "jukebox_song.minecraft.blocks": "jan C418 - blocks", "jukebox_song.minecraft.cat": "jan C418 - cat", "jukebox_song.minecraft.chirp": "jan C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON><PERSON> <PERSON> - C<PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON><PERSON> <PERSON> - Creator (kepeken poki pi kalama musi)", "jukebox_song.minecraft.far": "jan C418 - far", "jukebox_song.minecraft.lava_chicken": "kulupu Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "jan C418 - mall", "jukebox_song.minecraft.mellohi": "jan C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "j<PERSON> - otherside", "jukebox_song.minecraft.pigstep": "j<PERSON> <PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "jan <PERSON> - Precipice", "jukebox_song.minecraft.relic": "jan <PERSON>", "jukebox_song.minecraft.stal": "jan C418 - stal", "jukebox_song.minecraft.strad": "jan C418 - strad", "jukebox_song.minecraft.tears": "jan <PERSON> - Tears", "jukebox_song.minecraft.wait": "jan C418 - wait", "jukebox_song.minecraft.ward": "jan C418 - ward", "key.advancements": "pali pona", "key.attack": "pakala/utala", "key.back": "o tawa monsi", "key.categories.creative": "jan sewi", "key.categories.gameplay": "musi", "key.categories.inventory": "jo", "key.categories.misc": "ijo ante", "key.categories.movement": "tawa", "key.categories.multiplayer": "musi kulupu", "key.categories.ui": "nasin ilo musi", "key.chat": "open toki", "key.command": "toki wawa", "key.drop": "pana anpa", "key.forward": "o tawa sinpin", "key.fullscreen": "o kepeken sinpin ale", "key.hotbar.1": "ilo jo poka [1]", "key.hotbar.2": "ilo jo poka [2]", "key.hotbar.3": "ilo jo poka [3]", "key.hotbar.4": "ilo jo poka [4]", "key.hotbar.5": "ilo jo poka [5]", "key.hotbar.6": "ilo jo poka [6]", "key.hotbar.7": "ilo jo poka [7]", "key.hotbar.8": "ilo jo poka [8]", "key.hotbar.9": "ilo jo poka [9]", "key.inventory": "lukin/pini lukin pi jo sina", "key.jump": "tawa sewi", "key.keyboard.apostrophe": "nena [ ' ]", "key.keyboard.backslash": "nena \\", "key.keyboard.backspace": "nena pi weka sitelen monsi", "key.keyboard.caps.lock": "nena pi awen suli", "key.keyboard.comma": "nena ,", "key.keyboard.delete": "nena pi weka sitelen sinpin", "key.keyboard.down": "nena ↓", "key.keyboard.end": "nena pini", "key.keyboard.enter": "nena pi linja sin", "key.keyboard.equal": "nena =", "key.keyboard.escape": "nena [esc]", "key.keyboard.f1": "<PERSON><PERSON> [F1]", "key.keyboard.f10": "<PERSON><PERSON> [F10]", "key.keyboard.f11": "<PERSON><PERSON> [F11]", "key.keyboard.f12": "<PERSON><PERSON> [F12]", "key.keyboard.f13": "<PERSON><PERSON> [F13]", "key.keyboard.f14": "<PERSON><PERSON> [F14]", "key.keyboard.f15": "<PERSON><PERSON> [F15]", "key.keyboard.f16": "<PERSON><PERSON> [F16]", "key.keyboard.f17": "<PERSON><PERSON> [F17]", "key.keyboard.f18": "<PERSON><PERSON> [F18]", "key.keyboard.f19": "<PERSON><PERSON> [F19]", "key.keyboard.f2": "nena [F2]", "key.keyboard.f20": "nena [F20]", "key.keyboard.f21": "nena [F21]", "key.keyboard.f22": "nena [F22]", "key.keyboard.f23": "nena [F23]", "key.keyboard.f24": "nena [F24]", "key.keyboard.f25": "nena [F25]", "key.keyboard.f3": "nena [F3]", "key.keyboard.f4": "nena [F4]", "key.keyboard.f5": "nena [F5]", "key.keyboard.f6": "nena [F6]", "key.keyboard.f7": "nena [F7]", "key.keyboard.f8": "nena [F8]", "key.keyboard.f9": "nena [F9]", "key.keyboard.grave.accent": "nena `", "key.keyboard.home": "nena tomo", "key.keyboard.insert": "nena pi nasin pana", "key.keyboard.keypad.0": "nena [0] lon leko nanpa", "key.keyboard.keypad.1": "nena [1] lon leko nanpa", "key.keyboard.keypad.2": "nena [2] lon leko nanpa", "key.keyboard.keypad.3": "nena [3] lon leko nanpa", "key.keyboard.keypad.4": "nena [4] lon leko nanpa", "key.keyboard.keypad.5": "nena [5] lon leko nanpa", "key.keyboard.keypad.6": "nena [6] lon leko nanpa", "key.keyboard.keypad.7": "nena [7] lon leko nanpa", "key.keyboard.keypad.8": "nena [8] lon leko nanpa", "key.keyboard.keypad.9": "nena [9] lon leko nanpa", "key.keyboard.keypad.add": "nena [ + ] lon leko nanpa", "key.keyboard.keypad.decimal": "nena [ . ] lon leko nanpa", "key.keyboard.keypad.divide": "nena [ / ] lon leko nanpa", "key.keyboard.keypad.enter": "nena pi linja sin lon leko nanpa", "key.keyboard.keypad.equal": "nena [ = ] lon leko nanpa", "key.keyboard.keypad.multiply": "nena [ * ] lon leko nanpa", "key.keyboard.keypad.subtract": "nena [ - ] lon leko nanpa", "key.keyboard.left": "nena ←", "key.keyboard.left.alt": "nena [alt] lon poka open", "key.keyboard.left.bracket": "nena [", "key.keyboard.left.control": "nena lawa (Ctrl) lon poka open", "key.keyboard.left.shift": "nena [shift] lon poka open", "key.keyboard.left.win": "nena [<PERSON>] lon poka open", "key.keyboard.menu": "nena pi lipu lawa", "key.keyboard.minus": "nena [ - ]", "key.keyboard.num.lock": "nena pi awen nanpa", "key.keyboard.page.down": "nena pi lipu anpa", "key.keyboard.page.up": "nena pi lipu sewi", "key.keyboard.pause": "nena awen", "key.keyboard.period": "nena [ . ]", "key.keyboard.print.screen": "nena [<PERSON>rt Sc] pi sitelen sinpin", "key.keyboard.right": "nena →", "key.keyboard.right.alt": "nena [alt] lon poka pini", "key.keyboard.right.bracket": "nena ]", "key.keyboard.right.control": "nena lawa (Ctrl) lon poka pini", "key.keyboard.right.shift": "nena [shift] lon poka pini", "key.keyboard.right.win": "nena [<PERSON>] lon poka pini", "key.keyboard.scroll.lock": "nena pi awen pi sike tawa", "key.keyboard.semicolon": "nena [ ; ]", "key.keyboard.slash": "nena [ / ]", "key.keyboard.space": "nena kon", "key.keyboard.tab": "nena [tab]", "key.keyboard.unknown": "nena ala", "key.keyboard.up": "nena ↑", "key.keyboard.world.1": "ma nanpa wan", "key.keyboard.world.2": "ma nanpa tu", "key.left": "tawa poka open", "key.loadToolbarActivator": "o lon e nasin pi poki jo", "key.mouse": "nena %1$s pi ilo luka", "key.mouse.left": "nena lawa pi ilo luka", "key.mouse.middle": "nena sike pi ilo luka", "key.mouse.right": "nena nanpa tu pi ilo luka", "key.pickItem": "o jo e ijo pi lukin sina", "key.playerlist": "o lukin e jan musi ale", "key.quickActions": "pali pi tenpo lili", "key.right": "tawa poka pini", "key.saveToolbarActivator": "o awen e nasin pi poki jo", "key.screenshot": "o sitelen", "key.smoothCamera": "o kepeken nasin lukin pi sitelen tawa", "key.sneak": "tawa anpa", "key.socialInteractions": "toki kulupu", "key.spectatorOutlines": "o lukin e jan (ni li ken pi jan lukin taso)", "key.sprint": "tawa wawa", "key.swapOffhand": "o ante e luka", "key.togglePerspective": "o ante e nasin lukin", "key.use": "kepeken/pana", "known_server_link.announcements": "toki sin", "known_server_link.community": "kulupu", "known_server_link.community_guidelines": "lawa kulupu", "known_server_link.feedback": "toki pilin", "known_server_link.forums": "kulupu toki", "known_server_link.news": "ijo sin", "known_server_link.report_bug": "o toki e pakala pi ma kulupu", "known_server_link.status": "lon", "known_server_link.support": "wile sona", "known_server_link.website": "lipu", "lanServer.otherPlayers": "nasin pi jan ante", "lanServer.port": "nanpa tawa musi LAN", "lanServer.port.invalid": "nanpa ni li ike.\no pana e nanpa lon insa pi 1024 pi 65535. ante la o pana ala e nanpa.", "lanServer.port.invalid.new": "nanpa ni li ike.\no pana e nanpa tan %s tawa %s. ante la o pana ala.", "lanServer.port.unavailable": "nanpa ni li ike.\no pana e nanpa lon insa pi 1024 pi 65535. ante la o pana ala e nanpa.", "lanServer.port.unavailable.new": "nanpa ni li ike.\no pana e nanpa lon insa pi %s pi %s. ante la o pana ala e nanpa.", "lanServer.scanning": "mi alasa e ma tan jan poka", "lanServer.start": "o open e ma pi jan poka", "lanServer.title": "ma pi jan poka (LAN)", "language.code": "tok", "language.name": "toki pona", "language.region": "ma pona", "lectern.take_book": "o kama jo e lipu", "loading.progress": "%s%%", "mco.account.privacy.info": "o kama sona e nasin pi len awen e kulupu Mojang", "mco.account.privacy.info.button": "o lukin e sona namako pi lipu GDPR", "mco.account.privacy.information": "kulupu Mojang li pali mute tawa awen pi jan lili. pali ni li kepeken pali pi awen len pi jan lili (COPPA) li kepeken pali nasin pi awen nanpa (GDPR).\n\nken la, sina wile kama lon ma <PERSON> la, o toki tawa mama sina o sona e wile ona.", "mco.account.privacyinfo": "kulupu Mojang li pali mute tawa awen pi jan lili. pali ni li kepeken pali pi awen len pi jan lili (COPPA) li kepeken pali nasin pi awen nanpa (GDPR).\n\nken la, sina wile kama lon ma <PERSON>s la, o toki tawa mama sina o sona e wile ona.\n\nnimi musi sina li tan tenpo pini la (sina kama lon kepeken nimi sina), o tawa e nimi musi ni tawa nimi musi Mojang. ni la, sina ken kama lon ma Realms.", "mco.account.update": "o sin e nimi musi", "mco.activity.noactivity": "jan ala li lon ma ni lon tenpo suno %s pini", "mco.activity.title": "pali pi jan musi", "mco.backup.button.download": "o kama jo e ma pi tenpo sin", "mco.backup.button.reset": "o pali sin e ma ni", "mco.backup.button.restore": "o weka e ante", "mco.backup.button.upload": "o pana e ma", "mco.backup.changes.tooltip": "ante", "mco.backup.entry": "sama awen ma (%s)", "mco.backup.entry.description": "sona", "mco.backup.entry.enabledPack": "sina ken e poki ni:", "mco.backup.entry.gameDifficulty": "mute ike", "mco.backup.entry.gameMode": "nasin musi", "mco.backup.entry.gameServerVersion": "nanpa musi pi ma kulupu", "mco.backup.entry.name": "nimi", "mco.backup.entry.seed": "nimi ma wawa", "mco.backup.entry.templateName": "nimi nasin", "mco.backup.entry.undefined": "ante pi sona ala", "mco.backup.entry.uploaded": "pana", "mco.backup.entry.worldType": "nasin ma", "mco.backup.generate.world": "o pali e ma", "mco.backup.info.title": "ante pi poki awen tan tenpo pini poka", "mco.backup.narration": "poki awen tan %s", "mco.backup.nobackups": "ma Realm ni la awen ma li lon ala.", "mco.backup.restoring": "mi kama jo e ma Realm sina pi tenpo pini", "mco.backup.unknown": "SONA ALA", "mco.brokenworld.download": "o kama jo", "mco.brokenworld.downloaded": "sina jo e ni:", "mco.brokenworld.message.line1": "o pali sin e ma anu o wile e ma ante.", "mco.brokenworld.message.line2": "wile ante la sina ken ante e ma ni tawa ma pi jan wan taso.", "mco.brokenworld.minigame.title": "musi lili ni li tan tenpo pini li ken ala lon.", "mco.brokenworld.nonowner.error": "o awen. jan lawa o open sin e ma", "mco.brokenworld.nonowner.title": "ma ni li ten tenpo pini", "mco.brokenworld.play": "o musi", "mco.brokenworld.reset": "o ante tawa sin", "mco.brokenworld.title": "ma sina li tan tenpo pini li ken ala lon.", "mco.client.incompatible.msg.line1": "nasin ilo sina li ken ala kepeken ma <PERSON>s.", "mco.client.incompatible.msg.line2": "o kepeken musi Manka pi tenpo ni.", "mco.client.incompatible.msg.line3": "ma <PERSON>s li ken ala, kepeken nanpa namako pi musi Man<PERSON>.", "mco.client.incompatible.title": "nanpa pi musi sina li pona ala!", "mco.client.outdated.stable.version": "nanpa pi musi sina (%s) li ike tawa ma Realms.\n\no kepeken musi Manka sin.", "mco.client.unsupported.snapshot.version": "nanpa pi musi sina (%s) li ike tawa ma Realms.\n\nsina ken ala ma Realms lon nanpa musi ni pi pini ala.", "mco.compatibility.downgrade": "tawa monsi", "mco.compatibility.downgrade.description": "tenpo poka la sina musi lon ma ni kepeken musi nanpa %s. sina lon musi pi nanpa %s. sina ante monsi e ma la, ona li ken pakala.\n\nma sama li lon \"World backups\". wile la, o kepeken ma ni.", "mco.compatibility.incompatible.popup.title": "nanpa musi ike", "mco.compatibility.incompatible.releaseType.popup.message": "ma ni la nanpa pi musi sina li ike.", "mco.compatibility.incompatible.series.popup.message": "ma li lon nanpa musi %s. sina lon nanpa musi %s.\n\nni tu li ike tan ni tu. o pali e ma sin lon nanpa ni.", "mco.compatibility.unverifiable.message": "mi ken ala sona e nanpa musi pi ma ni. nanpa pi ma ni li kama pona anu kama ike la, mi pali e ma sama lon poki \"World backups\".", "mco.compatibility.unverifiable.title": "ni li pona la mi sona ala e lon", "mco.compatibility.upgrade": "namako", "mco.compatibility.upgrade.description": "tenpo pini la ma ni li musi lon %s. sina lon %s\nma ni li awen lon \"World backups\". tenpo wile la ni li kepeken.", "mco.compatibility.upgrade.friend.description": "tenpo pini la ma ni li musi lon nanpa %s. sina lon nanpa %s.\n\nma ni li awen lon \"World Backups\".\n\njan lawa pi ma Realms li ken lon e ma awen.", "mco.compatibility.upgrade.title": "sina wile ala wile sin e ma sina?", "mco.configure.current.minigame": "musi lili pi tenpo ni", "mco.configure.world.activityfeed.disabled": "tenpo lili la sona pi lon jan li lon ala", "mco.configure.world.backup": "awen ma", "mco.configure.world.buttons.activity": "pali jan", "mco.configure.world.buttons.close": "o pini pi tenpo lili e ma <PERSON>", "mco.configure.world.buttons.delete": "o weka", "mco.configure.world.buttons.done": "pini", "mco.configure.world.buttons.edit": "ante nasin", "mco.configure.world.buttons.invite": "o kama e jan ante", "mco.configure.world.buttons.moreoptions": "ante mute", "mco.configure.world.buttons.newworld": "ma sin", "mco.configure.world.buttons.open": "o open sin e ma Realm", "mco.configure.world.buttons.options": "o ante e ma", "mco.configure.world.buttons.players": "jan musi", "mco.configure.world.buttons.region_preference": "o wile e ma...", "mco.configure.world.buttons.resetworld": "o pali sin e ma", "mco.configure.world.buttons.save": "o awen", "mco.configure.world.buttons.settings": "ante nasin", "mco.configure.world.buttons.subscription": "tenpo jo", "mco.configure.world.buttons.switchminigame": "o ante e musi lili", "mco.configure.world.close.question.line1": "sina ken pini pi tenpo lili e ma Realm sina. ni li ken ala e musi tawa ni: sina ken ante lili e ma. sina pini e ante la, o open sin e ona.\n\nni li weka ala e tenpo jo sina pi ma Realms", "mco.configure.world.close.question.line2": "sina wile awen tawa anu seme?", "mco.configure.world.close.question.title": "sina wile ala wile pali e ante kepeken ala pini?", "mco.configure.world.closing": "mi pini e ma Realm...", "mco.configure.world.commandBlocks": "ilo pi toki wawa", "mco.configure.world.delete.button": "o weka e ma Realms", "mco.configure.world.delete.question.line1": "sina weka e ma Realm la ona li weka pi tenpo ale a", "mco.configure.world.delete.question.line2": "sina wile awen tawa anu seme?", "mco.configure.world.description": "sona namako pi ma <PERSON>s", "mco.configure.world.edit.slot.name": "nimi ma", "mco.configure.world.edit.subscreen.adventuremap": "ken la ante li lon ala tan ni: ma sina pi tenpo ni li ma alasa", "mco.configure.world.edit.subscreen.experience": "ken la ante li lon ala tan ni: ma sina pi tenpo ni li ma pi pali pona", "mco.configure.world.edit.subscreen.inspiration": "ken la ante li lon ala tan ni: ma sina pi tenpo ni li ma pi ijo suli", "mco.configure.world.forceGameMode": "o wile e nasin musi", "mco.configure.world.invite.narration": "jan %s li wile e kama sina", "mco.configure.world.invite.profile.name": "nimi", "mco.configure.world.invited": "jan kama", "mco.configure.world.invited.number": "mute pi jan kama: %s", "mco.configure.world.invites.normal.tooltip": "jan", "mco.configure.world.invites.ops.tooltip": "jan lawa", "mco.configure.world.invites.remove.tooltip": "o weka", "mco.configure.world.leave.question.line1": "sina weka tan ma Realm ni la sina ken ala kama sin li ken ala lukin e ona. sina wile kama sin la jan ante o kama e sina", "mco.configure.world.leave.question.line2": "sina wile awen tawa anu seme?", "mco.configure.world.loading": "mi lon e ma <PERSON>", "mco.configure.world.location": "lon ma", "mco.configure.world.minigame": "lon tenpo ni: %s", "mco.configure.world.name": "nimi pi ma Realms", "mco.configure.world.opening": "mi open e ma Realm...", "mco.configure.world.players.error": "jan pi nimi ni li lon ala", "mco.configure.world.players.inviting": "mi kama e jan ante...", "mco.configure.world.players.title": "jan musi", "mco.configure.world.pvp": "utala jan", "mco.configure.world.region_preference": "ma lon pi wile sina", "mco.configure.world.region_preference.title": "ante pi ma lon wile", "mco.configure.world.reset.question.line1": "mi pali sin e ma. ma pi tenpo ni li weka", "mco.configure.world.reset.question.line2": "sina wile awen tawa anu seme?", "mco.configure.world.resourcepack.question": "sina wile musi lon ma <PERSON> ni la o ante e sitelen e kalama kepeken poki.\n\nsina wile ala wile kama e ni tawa musi?", "mco.configure.world.resourcepack.question.line1": "sina wile musi lon ma <PERSON> ni la o ante e sitelen e kalama kepeken poki.", "mco.configure.world.resourcepack.question.line2": "sina wile kama jo e ni li wile musi anu seme?", "mco.configure.world.restore.download.question.line1": "sina kama jo e ma. ona li tawa lipu pi ma sina pi jan wan taso.", "mco.configure.world.restore.download.question.line2": "sina wile ala wile e ni?", "mco.configure.world.restore.question.line1": "mi kama e ma sina pi tenpo \"%s\" (%s)", "mco.configure.world.restore.question.line2": "sina wile awen tawa anu seme?", "mco.configure.world.settings.expired": "sina ken ala ante e ma <PERSON> pini", "mco.configure.world.settings.title": "ante nasin", "mco.configure.world.slot": "ma %s", "mco.configure.world.slot.empty": "jo ala", "mco.configure.world.slot.switch.question.line1": "mi ante e ma Realm ni tawa ma ante", "mco.configure.world.slot.switch.question.line2": "sina wile awen tawa anu seme?", "mco.configure.world.slot.tooltip": "o ante e ma tawa ma ni", "mco.configure.world.slot.tooltip.active": "o musi", "mco.configure.world.slot.tooltip.minigame": "o ante e musi lili tawa ona ni", "mco.configure.world.spawnAnimals": "o lon e soweli e akesi", "mco.configure.world.spawnMonsters": "o lon e monsuta", "mco.configure.world.spawnNPCs": "o lon e jan ilo", "mco.configure.world.spawnProtection": "o awen e ma pi open jan", "mco.configure.world.spawn_toggle.message": "sina ante e ni la ijo ale sama li pini", "mco.configure.world.spawn_toggle.message.npc": "sina ante e ni la ijo ale sama li weka, sama jan tomo", "mco.configure.world.spawn_toggle.title": "o sona e ni!", "mco.configure.world.status": "lon", "mco.configure.world.subscription.day": "tenpo suno", "mco.configure.world.subscription.days": "tenpo suno", "mco.configure.world.subscription.expired": "pini", "mco.configure.world.subscription.extend": "o pana e tenpo namako", "mco.configure.world.subscription.less_than_a_day": "tenpo lili", "mco.configure.world.subscription.month": "tenpo pi sike mun", "mco.configure.world.subscription.months": "tenpo pi sike mun", "mco.configure.world.subscription.recurring.daysleft": "mi pana e tenpo namako lon tenpo ni:", "mco.configure.world.subscription.recurring.info": "sina wile lukin e ante esun pi musi Realms la sina o awen tawa esun kama poka", "mco.configure.world.subscription.remaining.days": "sike suno %1$s", "mco.configure.world.subscription.remaining.months": "sike mun %1$s", "mco.configure.world.subscription.remaining.months.days": "sike mun %1$s en sike suno %2$s", "mco.configure.world.subscription.start": "tenpo open", "mco.configure.world.subscription.tab": "tenpo jo", "mco.configure.world.subscription.timeleft": "tenpo", "mco.configure.world.subscription.title": "tenpo jo sina", "mco.configure.world.subscription.unknown": "sona ala", "mco.configure.world.switch.slot": "o pali e ma", "mco.configure.world.switch.slot.subtitle": "ma ni la ala li lon. sina wile pali e ma kepeken nasin seme?", "mco.configure.world.title": "o ante e ma Realm:", "mco.configure.world.uninvite.player": "sina wile weka e ken '%s' kama anu seme?", "mco.configure.world.uninvite.question": "sina wile ala wile weka e wile pi kama jan?", "mco.configure.worlds.title": "ma", "mco.connect.authorizing": "ma kulupu li kama jo e nimi sina...", "mco.connect.connecting": "mi kama jo e sona pi ma Realm...", "mco.connect.failed": "mi ken ala lukin e ma <PERSON>", "mco.connect.region": "ma lon pi ma kulupu: %s", "mco.connect.success": "pini", "mco.create.world": "o pali", "mco.create.world.error": "o pana e nimi!", "mco.create.world.failed": "mi ken ala pali e ma!", "mco.create.world.reset.title": "mi pali e ma...", "mco.create.world.skip": "o weka", "mco.create.world.subtitle": "ante la, o pana e ma pi wile sina lon ma <PERSON> sin sina", "mco.create.world.wait": "mi pali e ma Realm...", "mco.download.cancelled": "sina pini e kama jo", "mco.download.confirmation.line1": "sina wile kama jo e ma ni. taso, ona li suli %s", "mco.download.confirmation.line2": "tenpo kama la sina ken ala pana sin e ma ni tawa ma Realm sina", "mco.download.confirmation.oversized": "sina wile kama jo e ma ni. taso, suli ona li suli tawa %s. ni li ike.\n\ntenpo kama la sina ken ala pana sin e ma ni tawa ma Realm sina", "mco.download.done": "kama jo li pini", "mco.download.downloading": "mi kama jo", "mco.download.extracting": "mi kama jo e insa", "mco.download.failed": "kama jo li pakala", "mco.download.percent": "%s %%", "mco.download.preparing": "mi wile kama jo", "mco.download.resourcePack.fail": "mi ken ala kama jo e poki sitelen kalama!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "mi kama jo e ma sin", "mco.error.invalid.session.message": "o open sin e musi Manka", "mco.error.invalid.session.title": "open ike", "mco.errorMessage.6001": "musi sina li tan tenpo pini", "mco.errorMessage.6002": "sina wile ala kepeken nasin", "mco.errorMessage.6003": "sina kama jo e ijo mute a", "mco.errorMessage.6004": "sina pana e ijo mute a", "mco.errorMessage.6005": "ma li open ala", "mco.errorMessage.6006": "ma li kepeken nanpa musi pi tenpo pini", "mco.errorMessage.6007": "jan li lon ma <PERSON>s pi mute ike", "mco.errorMessage.6008": "nimi Realms pakala", "mco.errorMessage.6009": "sona Realms pakala", "mco.errorMessage.connectionFailure": "pakala li lon. o alasa sin lon tenpo kama.", "mco.errorMessage.generic": "pakala li lon: ", "mco.errorMessage.initialize.failed": "mi ken ala open e ma Realm", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "mi pakala (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "mi ken ala tawa ma Realms: %s", "mco.errorMessage.realmsService.realmsError": "ma Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "mi ken ala alasa nanpa musi pona. mi kute e ni: %s", "mco.errorMessage.retry": "o pali sin", "mco.errorMessage.serviceBusy": "ma Realms li pali mute lon tenpo ni.\no tawa ma Realms sina lon tenpo sin poka.", "mco.gui.button": "nena", "mco.gui.ok": "pona", "mco.info": "sona!", "mco.invited.player.narration": "jan %s li ken kama", "mco.invites.button.accept": "wile", "mco.invites.button.reject": "wile ala", "mco.invites.nopending": "jan ala li wile e kama sina!", "mco.invites.pending": "jan li wile e kama sina!", "mco.invites.title": "wile pi kama sina", "mco.minigame.world.changeButton": "o wile e musi lili ante", "mco.minigame.world.info.line1": "ni la sina ante e ma pi tenpo ni tawa musi lili!", "mco.minigame.world.info.line2": "sina ken weka e ante ni lon tenpo kama. ma sina li awen.", "mco.minigame.world.noSelection": "sina wile e seme?", "mco.minigame.world.restore": "mi pini e musi lili...", "mco.minigame.world.restore.question.line1": "musi lili ni li pini. ma <PERSON> sina li kama sin.", "mco.minigame.world.restore.question.line2": "sina wile ala wile pini e musi lili?", "mco.minigame.world.selected": "sina wile e musi lili ni:", "mco.minigame.world.slot.screen.title": "mi ante e ma...", "mco.minigame.world.startButton": "ante", "mco.minigame.world.starting.screen.title": "mi pali e musi lili...", "mco.minigame.world.stopButton": "o pini e musi lili", "mco.minigame.world.switch.new": "sina wile e musi lili ante anu seme?", "mco.minigame.world.switch.title": "o ante e musi lili", "mco.minigame.world.title": "o ante e ma Realm tawa musi lili", "mco.news": "ijo sin pi ma Realms", "mco.notification.dismiss": "o weka", "mco.notification.transferSubscription.buttonText": "o tawa lon tenpo ni", "mco.notification.transferSubscription.message": "esun ma Realms li tawa esun pi kulupu Microsoft. tenpo jo sina o pini ala a!\no ante lon tenpo ni la sina ken jo e tenpo mun wan lon ma Realms kepeken ala mani.\no tawa Profile lon minecraft.net la sina ken ante e tenpo jo sina.", "mco.notification.visitUrl.buttonText.default": "o open", "mco.notification.visitUrl.message.default": "o open e lipu ni:", "mco.onlinePlayers": "jan musi lon ma ni", "mco.play.button.realm.closed": "ma <PERSON> li pini", "mco.question": "wile sona", "mco.reset.world.adventure": "alasa", "mco.reset.world.experience": "ma pi pali pona", "mco.reset.world.generate": "ma sin", "mco.reset.world.inspiration": "ma pi ijo suli", "mco.reset.world.resetting.screen.title": "mi ante e ma tawa sin...", "mco.reset.world.seed": "nanpa tan (sina ken pana ala)", "mco.reset.world.template": "nasin ma", "mco.reset.world.title": "o pali sin e ma", "mco.reset.world.upload": "o pana e ma", "mco.reset.world.warning": "ni la sina weka e ma pi tenpo ni li pana e ma sin", "mco.selectServer.buy": "o esun e ma Realms!", "mco.selectServer.close": "o weka", "mco.selectServer.closed": "ma <PERSON> pini", "mco.selectServer.closeserver": "o pini e ma <PERSON>", "mco.selectServer.configure": "o ante e ma Wen", "mco.selectServer.configureRealm": "o ante e ma Realms", "mco.selectServer.create": "o pali e ma <PERSON>s", "mco.selectServer.create.subtitle": "ma seme o lon ma <PERSON> sin sina?", "mco.selectServer.expired": "ma <PERSON> weka", "mco.selectServer.expiredList": "tenpo jo sina li pini", "mco.selectServer.expiredRenew": "o esun e jo sin", "mco.selectServer.expiredSubscribe": "o esun e jo", "mco.selectServer.expiredTrial": "tenpo musi sina li pini", "mco.selectServer.expires.day": "pini li kama lon tenpo suno wan kama", "mco.selectServer.expires.days": "pini li kama lon tenpo suno %s", "mco.selectServer.expires.soon": "pini li kama lon tenpo kama lili", "mco.selectServer.leave": "o weka tan ma Realms", "mco.selectServer.loading": "mi lon e lipu pi ma <PERSON>s", "mco.selectServer.mapOnlySupportedForVersion": "sina ken ala pali e ma ni lon musi nanpa %s", "mco.selectServer.minigame": "musi lili:", "mco.selectServer.minigameName": "musi lili: %s", "mco.selectServer.minigameNotSupportedInVersion": "sina ken ala musi e musi lili ni lon musi nanpa %s", "mco.selectServer.noRealms": "sina jo ala e ma <PERSON>s la o kama jo e ma <PERSON>s tawa musi pi jan pona sina.", "mco.selectServer.note": "o sona e ni:", "mco.selectServer.open": "ma Realm open", "mco.selectServer.openserver": "o open e ma Realms", "mco.selectServer.play": "o musi", "mco.selectServer.popup": "ma Realms li awen li pona. sina ken musi lon ma pi musi Manka. jan pona 10 li ken kama. musi lili mute en ma namako mute li lon! jan lawa taso li wile pana e mani.", "mco.selectServer.purchase": "ma Realms sin", "mco.selectServer.trial": "o musi e ma <PERSON>s lon tenpo lili!", "mco.selectServer.uninitialized": "o open e ma Realm sin sina!", "mco.snapshot.createSnapshotPopup.text": "sina pali e ma Realms pi nanpa musi namako. ni li lon esun sama pi ma Realms. ma Realms pi nanpa musi namako sin li lon sama lon esun pi ma Realms. esun pi ma Realms sina li pakala ala.", "mco.snapshot.createSnapshotPopup.title": "sina wile ala wile pali e ma <PERSON>s pi nanpa musi namako?", "mco.snapshot.creating": "mi pali e ma <PERSON>s pi nanpa musi namako...", "mco.snapshot.description": "ni li esun sama \"%s\"", "mco.snapshot.friendsRealm.downgrade": "sina wile kepeken napa musi %s tawa ma Realms ni", "mco.snapshot.friendsRealm.upgrade": "%s li sin e nanpa musi pi ma Realms ona la, sina ken musi lon nanpa musi ni", "mco.snapshot.paired": "ma Realms ni pi nanpa musi namako li sama ma Realms \"%s\"", "mco.snapshot.parent.tooltip": "sina wile musi lon ma <PERSON> ni la, o kepeken nanpa musi sin", "mco.snapshot.start": "mani ala la, o open e ma Realms sin pi nanpa musi namako", "mco.snapshot.subscription.info": "ni li ma Realms pi nanpa musi namako li kepeken esun sama pi ma Realms '%s'. ona li lon sama ma Realms pi esun sama.", "mco.snapshot.tooltip": "o kepeken ma Realms pi nanpa musi namako la, sina ken musi <PERSON> sin. ijo sin li lon ni.\n\nsina ken alasa e ma Realms pi nanpa musi pona.", "mco.snapshotRealmsPopup.message": "nanpa musi namako 23w41a la, ma Realms li ken kepeken nanpa musi namako. sin la, sina jo e ma Realms tu. nanpa wan li tawa nanpa musi pona. nanpa tu li tawa nanpa musi namako!", "mco.snapshotRealmsPopup.title": "sin la, ma <PERSON>s li ken lon nanpa musi namako", "mco.snapshotRealmsPopup.urlText": "sona namako", "mco.template.button.publisher": "jan pana", "mco.template.button.select": "o wile", "mco.template.button.trailer": "<PERSON>len tawa sona", "mco.template.default.name": "nasin ma", "mco.template.info.tooltip": "lipu pi jan pali", "mco.template.name": "nasin", "mco.template.select.failure": "mi ken ala kama jo e ijo ni.\no lukin lon tenpo sin.", "mco.template.select.narrate.authors": "jan pali: %s", "mco.template.select.narrate.version": "nanpa ante %s", "mco.template.select.none": "lon ala.\no lukin sin e ni lon tenpo kama. sina jan pali la\n%s.", "mco.template.select.none.linkTitle": "sina ken pana e ijo sina", "mco.template.title": "nasin ma", "mco.template.title.minigame": "musi lili", "mco.template.trailer.tooltip": "sitelen tawa sona ma", "mco.terms.buttons.agree": "ni li pona", "mco.terms.buttons.disagree": "ni li ike", "mco.terms.sentence.1": "nasin pi ma <PERSON>s li pona", "mco.terms.sentence.2": "nasin", "mco.terms.title": "nasin pi ma <PERSON>s", "mco.time.daysAgo": "lon sike suno pini nanpa %1$s", "mco.time.hoursAgo": "lon leko tenpo pini nanpa %1$s", "mco.time.minutesAgo": "lon tenpo lili pini nanpa %1$s", "mco.time.now": "lon tenpo ni", "mco.time.secondsAgo": "lon tenpo lili lili pini nanpa %1$s", "mco.trial.message.line1": "sina wile e ma Realm sina anu seme?", "mco.trial.message.line2": "sona namako la o lukin lon ni!", "mco.upload.button.name": "o pana", "mco.upload.cancelled": "sina pini e pana", "mco.upload.close.failure": "mi ken ala pini e ma <PERSON> sina, o alasa sin lon tenpo kama", "mco.upload.done": "pana li pini", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "pana li pakala! (%s)", "mco.upload.failed.too_big.description": "ma ni li suli pi mute ike. suli pi mute ken li %s.", "mco.upload.failed.too_big.title": "ma li suli pi mute ike", "mco.upload.hardcore": "sina ken ala pana e ma pi moli wan taso!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "mi open pali e ma sina", "mco.upload.select.world.none": "ma pi jan wan taso li lon ala!", "mco.upload.select.world.subtitle": "o wile e ma pana", "mco.upload.select.world.title": "o pana e ma", "mco.upload.size.failure.line1": "\"%s\" li suli mute!", "mco.upload.size.failure.line2": "ona li %s. nanpa suli ken li %s.", "mco.upload.uploading": "mi pana e \"%s\"", "mco.upload.verifying": "mi lukin e pona pi ma sina", "mco.version": "nanpa musi: %s", "mco.warning": "o awen a!", "mco.worldSlot.minigame": "musi lili", "menu.custom_options": "ante pi nasin kulupu...", "menu.custom_options.title": "ante pi nasin kulupu", "menu.custom_options.tooltip": "o sona e ni: kulupu Mojang en kulupu Microsoft li lawa ala e ijo ni. o kepeken sona!", "menu.custom_screen_info.button_narration": "lipu ni li ken ante. o kama sona namako.", "menu.custom_screen_info.contents": "kulupu ante li lawa e lipu ni. kulupu Mojang Studios en kulupu Microsoft li lawa ala e lipu ni.\n\no kepeken sona! sina tawa lipu la, o awen. o pana ala e sona jan sina e sona pi kama sina.\n\nsina ken ala musi tan lipu ni la, sina ken weka tan ma kulupu kepeken nena lon anpa.", "menu.custom_screen_info.disconnect": "lipu ni li pona ala", "menu.custom_screen_info.title": "sona pi lipu pi ken ante", "menu.custom_screen_info.tooltip": "lipu ni li ken ante. o kama sona namako lon ni.", "menu.disconnect": "o weka tan ma kulupu", "menu.feedback": "o toki tawa jan lawa...", "menu.feedback.title": "o toki tawa jan lawa", "menu.game": "lipu lawa", "menu.modded": " (namako musi li lon)", "menu.multiplayer": "musi kulupu", "menu.online": "ma Realms", "menu.options": "ante nasin...", "menu.paused": "musi li awen", "menu.playdemo": "o musi e ma pi tenpo lili", "menu.playerReporting": "pana nimi pi jan ike", "menu.preparingSpawn": "mi pali e ma open: %s%%", "menu.quick_actions": "pali pi tenpo lili...", "menu.quick_actions.title": "pali pi tenpo lili", "menu.quit": "o pini e musi", "menu.reportBugs": "o toki e pakala musi", "menu.resetdemo": "o ante e ma pi tenpo lili tawa sin", "menu.returnToGame": "o musi", "menu.returnToMenu": "o awen e musi o tawa lipu open", "menu.savingChunks": "mi awen e ma", "menu.savingLevel": "mi awen e ma", "menu.sendFeedback": "o toki e pilin", "menu.server_links": "lipu pi ma kulupu...", "menu.server_links.title": "lipu pi ma kulupu", "menu.shareToLan": "o ken e jan pona (LAN)", "menu.singleplayer": "musi pi jan wan", "menu.working": "mi pali...", "merchant.deprecated": "tenpo suno ale la, jan esun li sin e jo ona lon tenpo tu.", "merchant.level.1": "jan sin", "merchant.level.2": "jan pi kama sona", "merchant.level.3": "jan pali", "merchant.level.4": "jan pi ken mute", "merchant.level.5": "jan sona", "merchant.title": "%s - %s", "merchant.trades": "esun", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "o kepeken %1$s tawa weka insa", "multiplayer.applyingPack": "mi ante e sitelen e kalama", "multiplayer.confirm_command.parse_errors": "sina alasa pali e toki wawa ni: ona li sona ala anu ike.\nsina wile ala wile?\ntoki wawa: %s", "multiplayer.confirm_command.permissions_required": "sina alasa pali e toki wawa ni: ona li wile e ken wawa.\nni li ken ike e musi sina.\nsina wile ala wile?\ntoki wawa: %s", "multiplayer.confirm_command.title": "o lon e pali pi toki wawa", "multiplayer.disconnect.authservers_down": "ilo pi pona musi li pakala. o kama sin lon tenpo kama a", "multiplayer.disconnect.bad_chat_index": "mi lukin e toki weka anu toki pi nasin ante ike tan ma kulupu", "multiplayer.disconnect.banned": "sina weka suli tan ma ni", "multiplayer.disconnect.banned.expiration": "\ntenpo %s la sina ken kama sin tawa ma kulupu ni", "multiplayer.disconnect.banned.reason": "sina weka suli tan ma ni\ntan ni: %s", "multiplayer.disconnect.banned_ip.expiration": "\ntenpo %s la sina ken kama sin tawa ma kulupu ni", "multiplayer.disconnect.banned_ip.reason": "nimi IP sina weka suli tan ma ni\ntan ni: %s", "multiplayer.disconnect.chat_validation_failed": "toki nasa", "multiplayer.disconnect.duplicate_login": "tenpo ni la sina lon ma ni kepeken ilo ante kin", "multiplayer.disconnect.expired_public_key": "pakala! ken la sina ante e tenpo pi ilo sina. o pini musi o open sin e musi.", "multiplayer.disconnect.flying": "o tawa ala lon kon a", "multiplayer.disconnect.generic": "sina weka", "multiplayer.disconnect.idling": "tenpo pi mute ike la sina pali e ala", "multiplayer.disconnect.illegal_characters": "sitelen lili ike li lon toki", "multiplayer.disconnect.incompatible": "musi sina li sama ala! sina wile musi lon %s", "multiplayer.disconnect.invalid_entity_attacked": "sina lukin utala e ijo li ken ala", "multiplayer.disconnect.invalid_packet": "ma kulupu li pana e ijo pakala", "multiplayer.disconnect.invalid_player_data": "nanpa sona jan li pakala", "multiplayer.disconnect.invalid_player_movement": "sina tawa nasa lon ma ni", "multiplayer.disconnect.invalid_public_key_signature": "pakala! o pini musi o open sin e musi.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "tomo sina li tawa nasa lon ma ni", "multiplayer.disconnect.ip_banned": "nimi <PERSON> sina li weka suli tan ma ni", "multiplayer.disconnect.kicked": "jan lawa li weka e sina", "multiplayer.disconnect.missing_tags": "toki ilo li pakala. o toki tawa jan lawa pi musi ni.", "multiplayer.disconnect.name_taken": "jan ante li kepeken nimi sina", "multiplayer.disconnect.not_whitelisted": "kulupu li wile ala e kama sina tan ni: sina lon ala lipu pi jan ken", "multiplayer.disconnect.out_of_order_chat": "pakala! toki sina li nasa. tenpo pi ilo sina li ante anu seme?", "multiplayer.disconnect.outdated_client": "nanpa musi sina li ike! o kepeken %s", "multiplayer.disconnect.outdated_server": "nanpa musi sina li ike! o kepeken %s", "multiplayer.disconnect.server_full": "ma ni li ken ala poki e sina tan ni: jan mute a li lon!", "multiplayer.disconnect.server_shutdown": "ma kulupu li open ala", "multiplayer.disconnect.slow_login": "kama sina li kepeken tenpo suli ike", "multiplayer.disconnect.too_many_pending_chats": "jan li pana e mute toki ike tawa ma kulupu", "multiplayer.disconnect.transfers_disabled": "sina ken ala tawa ma kulupu ni tan ma kulupu ante", "multiplayer.disconnect.unexpected_query_response": "sona ilo nasa li kama tan musi sina", "multiplayer.disconnect.unsigned_chat": "pakala! toki sina li nasa. ma kulupu li sona ala e jan sina.", "multiplayer.disconnect.unverified_username": "ma kulupu li sona ala e pona pi nimi sina!", "multiplayer.downloadingStats": "mi kama jo e sona nanpa...", "multiplayer.downloadingTerrain": "mi lon e ma...", "multiplayer.lan.server_found": "mi kama jo e ma kulupu sin: %s", "multiplayer.message_not_delivered": "toki ni li ken ala kama. o lukin e lipu ni: %s", "multiplayer.player.joined": "%s li kama", "multiplayer.player.joined.renamed": "%s (nimi pi sin ala li %s) li kama", "multiplayer.player.left": "%s li weka", "multiplayer.player.list.hp": "pona sijelo %s", "multiplayer.player.list.narration": "jan musi lon ma ni: %s", "multiplayer.requiredTexturePrompt.disconnect": "ma kulupu ni li wile e ni: sina ante e sitelen e kalama.", "multiplayer.requiredTexturePrompt.line1": "ma kulupu ni li wile e ni: sina ante e sitelen e kalama.", "multiplayer.requiredTexturePrompt.line2": "sina wile ala kepeken poki ni la sina kama weka tan ma kulupu ni.", "multiplayer.socialInteractions.not_available": "ijo kulupu li lon ma kulupu taso", "multiplayer.status.and_more": "... poka la ijo %s li lon ...", "multiplayer.status.cancelled": "pini", "multiplayer.status.cannot_connect": "mi ken ala tawa e sina", "multiplayer.status.cannot_resolve": "mi ken ala alasa e sona pi ma kulupu", "multiplayer.status.finished": "pini", "multiplayer.status.incompatible": "ma kulupu la musi sina li sama ala!", "multiplayer.status.motd.narration": "sitelen pi sike suno ni: %s", "multiplayer.status.no_connection": "(mi ken ala toki tawa kulupu)", "multiplayer.status.old": "sin ala", "multiplayer.status.online": "lon linja", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "toki pi ma kulupu li kepeken tenpo pi lili mute %s (Milliseconds)", "multiplayer.status.pinging": "mi toki tawa ma kulupu...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "jan musi %s li lon ma ni. jan musi %s li ken.", "multiplayer.status.quitting": "sina kama weka", "multiplayer.status.request_handled": "ilo li pini lukin e toki wile nasin", "multiplayer.status.unknown": "seme???", "multiplayer.status.unrequested": "mi kama jo e sona li wile ala e ona", "multiplayer.status.version.narration": "nanpa musi pi ma kulupu: %s", "multiplayer.stopSleeping": "o weka tan supa lape", "multiplayer.texturePrompt.failure.line1": "mi ken ala kepeken poki sitelen kalama pi ma kulupu", "multiplayer.texturePrompt.failure.line2": "musi li wile e poki sitelen kalama la ona li ken nasa", "multiplayer.texturePrompt.line1": "ma kulupu ni li wile e ni: sina ante e poki sitelen kalama lon ma ni.", "multiplayer.texturePrompt.line2": "sina wile ala wile e ante ni?", "multiplayer.texturePrompt.serverPrompt": "%s\n\ntoki tan ma kulupu:\n%s", "multiplayer.title": "o musi kulupu", "multiplayer.unsecureserver.toast": "ma kulupu ni li ken ante e toki jan", "multiplayer.unsecureserver.toast.title": "ma kulupu li ken ala sona e pona pi toki jan", "multiplayerWarning.check": "o pana ala e lipu ni", "multiplayerWarning.header": "o sona e ni!", "multiplayerWarning.message": "musi kulupu la, kulupu Mojang en kulupu Microsoft li jo ala li lawa ala e ona. sina lukin e toki e ijo pi jan ante. ken la ona li pona ala tawa sina.", "music.game.a_familiar_room": "jan <PERSON> - A Familiar Room", "music.game.an_ordinary_day": "jan <PERSON> - An Ordinary Day", "music.game.ancestry": "j<PERSON> <PERSON> - An<PERSON><PERSON>", "music.game.below_and_above": "jan <PERSON> - Below and Above", "music.game.broken_clocks": "jan <PERSON> - Broken Clocks", "music.game.bromeliad": "jan <PERSON> - Bromeliad", "music.game.clark": "jan C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> <PERSON> - Comforting Memories", "music.game.creative.aria_math": "jan C418 - <PERSON> Math", "music.game.creative.biome_fest": "jan C418 - Biome Fest", "music.game.creative.blind_spots": "jan C418 - Blind Spots", "music.game.creative.dreiton": "jan C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "jan C418 - <PERSON><PERSON>", "music.game.creative.taswell": "jan C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "jan <PERSON> - Crescent Dunes", "music.game.danny": "jan C418 - <PERSON>", "music.game.deeper": "j<PERSON> <PERSON> - <PERSON>er", "music.game.dry_hands": "jan C418 - Dry Hands", "music.game.echo_in_the_wind": "jan <PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON><PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "jan C418 - <PERSON>", "music.game.end.boss": "jan C418 - <PERSON>", "music.game.end.the_end": "jan C418 - The End", "music.game.endless": "<PERSON><PERSON> - Endless", "music.game.featherfall": "jan <PERSON> - Featherfall", "music.game.fireflies": "jan <PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> <PERSON> - Floating Dream", "music.game.haggstrom": "jan C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON><PERSON> <PERSON> - Infinite Amethyst", "music.game.key": "jan C418 - Key", "music.game.komorebi": "jan <PERSON><PERSON> k<PERSON><PERSON>bi", "music.game.left_to_bloom": "<PERSON><PERSON> - Left to Bloom", "music.game.lilypad": "jan <PERSON> - Lily<PERSON>", "music.game.living_mice": "jan C418 - <PERSON>", "music.game.mice_on_venus": "jan C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "jan C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "jan C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "jan C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON><PERSON> <PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "jan C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "j<PERSON> <PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON><PERSON> - So Below", "music.game.nether.warmth": "jan C418 - <PERSON><PERSON>", "music.game.one_more_day": "<PERSON><PERSON> - One More Day", "music.game.os_piano": "jan <PERSON> - O's Piano", "music.game.oxygene": "jan C418 - <PERSON><PERSON>g<PERSON>", "music.game.pokopoko": "jan <PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "jan <PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON><PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "jan C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.swamp.aerie": "j<PERSON> <PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON><PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON><PERSON> <PERSON> - Labyrinthine", "music.game.sweden": "jan C418 - Sweden", "music.game.watcher": "jan <PERSON> - Watcher", "music.game.water.axolotl": "jan C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "jan C418 - <PERSON> Fish", "music.game.water.shuniji": "jan C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "j<PERSON> <PERSON> - <PERSON>", "music.game.wet_hands": "jan C418 - <PERSON> Hands", "music.game.yakusoku": "jan <PERSON><PERSON><PERSON><PERSON>", "music.menu.beginning_2": "jan C418 - Beginning 2", "music.menu.floating_trees": "jan C418 - Floating Trees", "music.menu.moog_city_2": "jan C418 - Moog City 2", "music.menu.mutation": "jan C418 - Mu<PERSON>", "narration.button": "ilo nena: %s", "narration.button.usage.focused": "o kepeken nena <PERSON> tawa open", "narration.button.usage.hovered": "o kepeken nena lon poka open pi ilo luka tawa open", "narration.checkbox": "wile sina: %s", "narration.checkbox.usage.focused": "o kepeken nena Enter tawa open anu pini", "narration.checkbox.usage.hovered": "o kepeken nena lon poka open pi ilo luka tawa open anu pini", "narration.component_list.usage": "o kepeken nena Tab tawa kute pi ijo kama", "narration.cycle_button.usage.focused": "o kepeken nena Enter tawa ni: sina ante tawa %s", "narration.cycle_button.usage.hovered": "o kepeken nena lon poka open pi ilo luka tawa ni: sina ante tawa %s", "narration.edit_box": "poki ante: %s", "narration.item": "ijo: %s", "narration.recipe": "nasin pali tawa %s", "narration.recipe.usage": "o kepeken nena lon poka open pi ilo luka tawa wile", "narration.recipe.usage.more": "o kepeken nena lon poka pini pi ilo luka tawa lukin mute", "narration.selection.usage": "o kepeken nena sewi anu nena anpa", "narration.slider.usage.focused": "o kepeken nena tawa pi poka pilin pi poka lawa tawa ante mute", "narration.slider.usage.hovered": "o tawa e nena kepeken ilo luka tawa ante mute", "narration.suggestion": "wile %s tan %s: %s", "narration.suggestion.tooltip": "wile %s tan %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "o luka e nena <PERSON>, sina kepeken pana mi ante", "narration.suggestion.usage.cycle.hidable": "o luka e nena <PERSON>, sina kepeken pana mi ante. o luka e nena <PERSON> la, sina weka e pana mi", "narration.suggestion.usage.fill.fixed": "o luka e nena <PERSON>, sina kepeken pana mi", "narration.suggestion.usage.fill.hidable": "sina luka e nena <PERSON>, sina kepeken pana mi. sina luka e nena <PERSON> la, sina weka e pana mi", "narration.tab_navigation.usage": "o kepeken nena lawa (Ctrl) en nena Tab tawa ante e lipu (Tabs)", "narrator.button.accessibility": "pona kepeken", "narrator.button.difficulty_lock": "o awen e ike ma", "narrator.button.difficulty_lock.locked": "awen", "narrator.button.difficulty_lock.unlocked": "awen ala", "narrator.button.language": "toki", "narrator.controls.bound": "%s li lon %s", "narrator.controls.reset": "o ante e nena %s tawa ona pi tenpo pini", "narrator.controls.unbound": "%s li lon ala", "narrator.joining": "sina kama", "narrator.loading": "mi kama e ma: %s", "narrator.loading.done": "pini", "narrator.position.list": "linja nanpa %s tan %s", "narrator.position.object_list": "ijo nanpa %s tan %s", "narrator.position.screen": "ijo nanpa %s tan %s", "narrator.position.tab": "lipu nanpa %s tan %s", "narrator.ready_to_play": "musi li open", "narrator.screen.title": "lipu open", "narrator.screen.usage": "o kepeken ilo luka anu nena Ta<PERSON> tawa wile ijo", "narrator.select": "sina pilin e %s", "narrator.select.world": "sina pilin e %s, tenpo musi li %s, %s, %s, musi Manka: %s", "narrator.select.world_info": "sina lon ma %s. sina musi lon ona lon tenpo suno %s, lon tenpo %s", "narrator.toast.disabled": "ilo toki li lon ala", "narrator.toast.enabled": "ilo toki li lon", "optimizeWorld.confirm.description": "ni li pali e ni: mi sin pona e sona nanpa pi ma ni. ma sina li suli la, ni li kepeken tenpo mute. mi pini la, sina musi pona mute, taso sina ken ala lon ma ni kepeken musi Manka pi sin ala. sina wile ala wile pali e ni?", "optimizeWorld.confirm.proceed": "o pali e poki awen, o pona e ma", "optimizeWorld.confirm.title": "o pona e ma", "optimizeWorld.info.converted": "ma nanpa ni li pona: %s", "optimizeWorld.info.skipped": "mi pona ala e ma nanpa ni: %s", "optimizeWorld.info.total": "ma %s li lon", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "mi nanpa e ma...", "optimizeWorld.stage.failed": "mi pakala! :(", "optimizeWorld.stage.finished": "mi pini...", "optimizeWorld.stage.finished.chunks": "mi pini pona e ma...", "optimizeWorld.stage.finished.entities": "mi pini pona e ijo kon...", "optimizeWorld.stage.finished.poi": "mi pini pona e ma suli...", "optimizeWorld.stage.upgrading": "mi pona e ma ale...", "optimizeWorld.stage.upgrading.chunks": "mi pona e ma ale...", "optimizeWorld.stage.upgrading.entities": "mi pona e ijo kon ale...", "optimizeWorld.stage.upgrading.poi": "mi pona e ma suli ale...", "optimizeWorld.title": "mi pona e ma \"%s\"", "options.accessibility": "ante pi ken ale...", "options.accessibility.high_contrast": "kule pi ante suli", "options.accessibility.high_contrast.error.tooltip": "mi jo ala e poki sitelen pi kule pi ante suli", "options.accessibility.high_contrast.tooltip": "ni li suli e ante kule pi ilo nena", "options.accessibility.high_contrast_block_outline": "selo leko suli", "options.accessibility.high_contrast_block_outline.tooltip": "sina lukin e leko la ona li kama jo e selo leko suli.", "options.accessibility.link": "sona pi lipu ni", "options.accessibility.menu_background_blurriness": "lipu lawa la, mute pi ken lukin o seme", "options.accessibility.menu_background_blurriness.tooltip": "ni li ante e mute pi ken lukin lon monsi pi lipu lawa", "options.accessibility.narrator_hotkey": "nena pi ilo toki", "options.accessibility.narrator_hotkey.mac.tooltip": "ni la sina ken open li ken pini e ilo toki kalama kepeken nena 'Cmd+B'", "options.accessibility.narrator_hotkey.tooltip": "ni la sina ken open li ken pini e ilo toki kalama kepeken nena 'Ctrl+B'", "options.accessibility.panorama_speed": "wawa tawa pi sitelen ma lon lipu open", "options.accessibility.text_background": "pimeja lon anpa toki", "options.accessibility.text_background.chat": "toki", "options.accessibility.text_background.everywhere": "lon ale", "options.accessibility.text_background_opacity": "pimeja pi anpa toki", "options.accessibility.title": "ante pi ken ale...", "options.allowServerListing": "o ken e lukin pi nimi sina lon ma kulupu", "options.allowServerListing.tooltip": "ma kulupu la sina ken lukin e lipu pi jan musi ale. sina wile la nimi sina li lon lipu ni ala.", "options.ao": "wan suno pimeja", "options.ao.max": "mute", "options.ao.min": "lili", "options.ao.off": "ala", "options.attack.crosshair": "lon lukin", "options.attack.hotbar": "lon jo sina", "options.attackIndicator": "ilo lukin pi tenpo utala", "options.audioDevice": "ilo", "options.audioDevice.default": "nasin ilo pi ante ala", "options.autoJump": "tawa sewi lon tenpo ale", "options.autoSuggestCommands": "lukin e toki wawa ken", "options.autosaveIndicator": "<PERSON>len poki pi sona musi", "options.biomeBlendRadius": "wan pi kule ma", "options.biomeBlendRadius.1": "ala (pona tawa ilo sona)", "options.biomeBlendRadius.11": "11x11 (suli a)", "options.biomeBlendRadius.13": "13x13 (wawa)", "options.biomeBlendRadius.15": "15x15 (sewi)", "options.biomeBlendRadius.3": "3x3 (pona tawa ilo)", "options.biomeBlendRadius.5": "5x5 (pona)", "options.biomeBlendRadius.7": "7x7 (mute)", "options.biomeBlendRadius.9": "9x9 (mute a)", "options.chat": "ante pi toki jan...", "options.chat.color": "kule", "options.chat.delay": "tenpo lili %s la toki li kama lukin", "options.chat.delay_none": "tenpo pi toki ala: ala", "options.chat.height.focused": "suli sewi", "options.chat.height.unfocused": "mute sewi lon tenpo pi toki ala", "options.chat.line_spacing": "kon lon insa pi linja toki", "options.chat.links": "nasin tawa lipu ante", "options.chat.links.prompt": "mi o sona e wile sina", "options.chat.opacity": "wawa pi lukin toki", "options.chat.scale": "suli toki", "options.chat.title": "ante pi toki jan...", "options.chat.visibility": "lukin toki", "options.chat.visibility.full": "ken lukin", "options.chat.visibility.hidden": "len", "options.chat.visibility.system": "toki wawa taso", "options.chat.width": "suli poka", "options.chunks": "leko suli %s", "options.clouds.fancy": "pona lukin", "options.clouds.fast": "pona tawa ilo", "options.controls": "ilo lawa...", "options.credits_and_attribution": "nimi pi jan pali...", "options.damageTiltStrength": "tawa lukin tan pakala", "options.damageTiltStrength.tooltip": "ni li mute pi tawa lukin tan pakala tawa sina.", "options.darkMojangStudiosBackgroundColor": "sitelen pi kule tu taso", "options.darkMojangStudiosBackgroundColor.tooltip": "ni li ante e monsi sitelen pi kulupu Mojang Studios tawa pimeja.", "options.darknessEffectScale": "wawa pi pimeja suli", "options.darknessEffectScale.tooltip": "ni li lawa e mute ni: monsuta kute anu soko Sculk kalama li pana e pimeja suli tawa sina la ona li wawa seme e pimeja.", "options.difficulty": "mute ike", "options.difficulty.easy": "ike ala", "options.difficulty.easy.info": "<PERSON>suta li lon, taso ona li wawa ala. sina wile moku, sina ni ala la sijelo sina li kama ike, tawa pilin 5 taso.", "options.difficulty.hard": "ike <PERSON>", "options.difficulty.hard.info": "monsuta li lon li wawa mute. sina ken moli tan wile moku.", "options.difficulty.hardcore": "moli wan taso", "options.difficulty.normal": "ike lili", "options.difficulty.normal.info": "monsuta li lon li wawa lili. sina moku ala la moli li kama poka, taso sina ken ala moli tan wile moku taso.", "options.difficulty.online": "mute ike pi ma kulupu", "options.difficulty.peaceful": "monsuta ala", "options.difficulty.peaceful.info": "monsuta en soweli ike li weka. sina pilin ala e wile moku. sijelo sina li kama pona tan pali ala.", "options.directionalAudio": "kalama pi lon ante", "options.directionalAudio.off.tooltip": "nasin kalama pi kute tu tan tenpo pini", "options.directionalAudio.on.tooltip": "ni li ken lon ilo nanpa sina la, ilo HRTF li pona e tan e tawa pi kalama ale. sina wile kepeken ilo kute lon lawa sina tawa ni.", "options.discrete_mouse_scroll": "tawa pona pi ilo sike", "options.entityDistanceScaling": "suli pi ken lukin pi ijo kon", "options.entityShadows": "pimeja lon anpa ijo", "options.font": "ante pi nasin sitelen...", "options.font.title": "ante pi nasin sitelen", "options.forceUnicodeFont": "o kepeken sitelen Juniko", "options.fov": "mute lukin", "options.fov.max": "wawa a", "options.fov.min": "pona", "options.fovEffectScale": "ante pi mute lukin", "options.fovEffectScale.tooltip": "ni li lawa e ni: mute lukin sina li ken ante tan wawa musi nasa.", "options.framerate": "sitelen tenpo %s", "options.framerateLimit": "mute pi sitelen tenpo", "options.framerateLimit.max": "sitelen ale", "options.fullscreen": "sinpin musi li suli ale", "options.fullscreen.current": "nasin pi tenpo lon", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "suli lukin lon sinpin suli", "options.fullscreen.unavailable": "sina ken ala ante", "options.gamma": "wawa suno", "options.gamma.default": "pona", "options.gamma.max": "wawa", "options.gamma.min": "lili", "options.generic_value": "%s: %s", "options.glintSpeed": "tenpo pi suno len", "options.glintSpeed.tooltip": "ni li lawa e mute tenpo pi suno wawa", "options.glintStrength": "wawa pi suno len", "options.glintStrength.tooltip": "ni li lawa e mute lukin pi suno wawa", "options.graphics": "sitelen", "options.graphics.fabulous": "pona lukin a!", "options.graphics.fabulous.tooltip": "nasin sitelen %s li kepeken nasin suno. nasin ni li ken sitelen e suno e ijo kon lon monsi pi ijo kon (sama kiwen lukin anu telo). ni li ike tawa sinpin suli li ike tawa sinpin lili kin.", "options.graphics.fancy": "pona lukin", "options.graphics.fancy.tooltip": "lukin pona li sama e wile pona e wile pi ilo sona mute. kon en ko sewi en ijo lili a li ken weka lon monsi pi ijo kon (sama kiwen lukin, anu sama telo).", "options.graphics.fast": "pona tawa ilo", "options.graphics.fast.tooltip": "ni li lili e mute lukin pi telo sewi e mute pi ko lete. sina ken ala lukin lon insa pi lipu kasi.", "options.graphics.warning.accept": "o awen kepeken", "options.graphics.warning.cancel": "o tawa monsi", "options.graphics.warning.message": "ilo lukin sina li wawa lili li ken ala kepeken e nasin pi %s. sina awen kepeken la, ike nasa li ken kama...", "options.graphics.warning.renderer": "mi lukin e ilo sitelen ni: [%s]", "options.graphics.warning.title": "ilo lukin sina li ken ala", "options.graphics.warning.vendor": "mi lukin e ilo pana ni: [%s]", "options.graphics.warning.version": "mi lukin e OpenGL ni: [%s]", "options.guiScale": "suli GUI", "options.guiScale.auto": "pona", "options.hidden": "len", "options.hideLightningFlashes": "o len e walo wawa pi linja suno", "options.hideLightningFlashes.tooltip": "linja suno li walo ala e ale sewi. jan li awen ken lukin e ona.", "options.hideMatchedNames": "o len e toki jan ale", "options.hideMatchedNames.tooltip": "ken la ma kulupu li pana e toki jan kepeken nasin nasa. kepeken ni la sina wile e ona la mi awen len e toki jan ale.", "options.hideSplashTexts": "o weka e toki jelo", "options.hideSplashTexts.tooltip": "ni li weka e toki jelo lon lipu open.", "options.inactivityFpsLimit": "ni la o lili e sitelen tenpo", "options.inactivityFpsLimit.afk": "weka tan ilo", "options.inactivityFpsLimit.afk.tooltip": "jan li musi ala lon tenpo lili wan la, nanpa sewi pi sitelen tenpo li 30. tenpo lili 9 la, nanpa sewi pi sitelen tenpo li 10.", "options.inactivityFpsLimit.minimized": "lipu li lili", "options.inactivityFpsLimit.minimized.tooltip": "lipu musi li lili taso la, sitelen tenpo li mute ala.", "options.invertMouse": "o ante e nasin lukin", "options.japaneseGlyphVariants": "sitelen lili pi toki Nijon", "options.japaneseGlyphVariants.tooltip": "ni li kepeken sitelen Kansi pi toki Nijon.", "options.key.hold": "awen luka", "options.key.toggle": "wile / ala", "options.language": "toki...", "options.language.title": "toki", "options.languageAccuracyWarning": "(ken la ante toki ni li pona ali ala, nasin mute pi ante toki li lon)", "options.languageWarning": "ken la ante toki ni li pona ali ala, nasin mute pi ante toki li lon", "options.mainHand": "luka lawa", "options.mainHand.left": "poka open", "options.mainHand.right": "lon poka pini", "options.mipmapLevels": "pona ijo lon poka ala", "options.modelPart.cape": "len lipu monsi", "options.modelPart.hat": "len lawa", "options.modelPart.jacket": "len sijelo", "options.modelPart.left_pants_leg": "len noka lon poka pilin", "options.modelPart.left_sleeve": "len luka lon poka open", "options.modelPart.right_pants_leg": "len noka lon poka pi pilin ala", "options.modelPart.right_sleeve": "len luka lon poka pini", "options.mouseWheelSensitivity": "wawa tawa pi ilo sike", "options.mouse_settings": "ante pi ilo luka...", "options.mouse_settings.title": "ante pi ilo luka", "options.multiplayer.title": "ante pi ma kulupu...", "options.multiplier": "%sx", "options.music_frequency": "kalama sin o lon tenpo pi mute seme", "options.music_frequency.constant": "tenpo ale", "options.music_frequency.default": "nasin pi ante ala", "options.music_frequency.frequent": "tenpo mute", "options.music_frequency.tooltip": "mi ante e mute pi tenpo ni: kalama sin li kama lon.", "options.narrator": "ilo toki", "options.narrator.all": "ale", "options.narrator.chat": "toki jan", "options.narrator.notavailable": "ken ala", "options.narrator.off": "ala", "options.narrator.system": "toki pi jan ala", "options.notifications.display_time": "tenpo lukin toki", "options.notifications.display_time.tooltip": "ni li lawa e mute tenpo ni: sina ken lukin e toki sin.", "options.off": "ala", "options.off.composed": "%s: ala", "options.on": "wile", "options.on.composed": "%s: lon", "options.online": "lon linja...", "options.online.title": "ante nasin lon linja", "options.onlyShowSecureChat": "o lukin e toki awen taso", "options.onlyShowSecureChat.tooltip": "o lukin taso e toki ni: ma kulupu li sona e pona nimi pi jan musi, li ante ala e toki.", "options.operatorItemsTab": "lipu ijo pi jan lawa", "options.particles": "ijo lili", "options.particles.all": "ale", "options.particles.decreased": "mute lili", "options.particles.minimal": "mute pi lili nanpa wan", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "ilo pali pi leko suli", "options.prioritizeChunkUpdates.byPlayer": "pali jan", "options.prioritizeChunkUpdates.byPlayer.tooltip": "pali lili lon ma suli li pali ante e ma suli lon tenpo sama. pali lili ni li pana leko li pakala leko.", "options.prioritizeChunkUpdates.nearby": "lon poka sina", "options.prioritizeChunkUpdates.nearby.tooltip": "leko suli lon poka sina li pali lon tenpo wan. ni la musi li ken ike tan pana leko anu pakala leko.", "options.prioritizeChunkUpdates.none": "linja", "options.prioritizeChunkUpdates.none.tooltip": "leko suli lon poka sina li pali lon nasin linja. ni la ma li ken lukin nasa tan pana leko anu pakala leko.", "options.rawMouseInput": "tawa tan luka", "options.realmsNotifications": "wile kama en ijo sin pi ma Realms", "options.realmsNotifications.tooltip": "mi pana e ijo sin e wile pi kama sina lon lipu pi ma <PERSON>s.", "options.reducedDebugInfo": "sona lili lon f3", "options.renderClouds": "ko walo sewi", "options.renderCloudsDistance": "weka pi kon walo sewi", "options.renderDistance": "suli pi ken lukin", "options.resourcepack": "o ante e poki sitelen kalama...", "options.rotateWithMinecart": "tawa sike lon poki tawa", "options.rotateWithMinecart.tooltip": "poki tawa li tawa sike la, lukin jan li tawa sike. ni li ken lon ma ni taso: nasin nasa 'pona pi poki tawa' li lon.", "options.screenEffectScale": "lukin nasa musi", "options.screenEffectScale.tooltip": "wawa pi tawa sinpin pi pilin ike insa. ni li lili la, sinpin sina li tawa ala li laso taso.", "options.sensitivity": "wawa pi tawa lukin", "options.sensitivity.max": "WAWA A", "options.sensitivity.min": "lape", "options.showNowPlayingToast": "mi pana e nimi pi kalama musi", "options.showNowPlayingToast.tooltip": "ni li pana e nimi pi kalama musi lon, lon open kalama. nimi sama li lon tenpo ante pi open ala lon ma ni: sina pini pi tenpo lili e musi.", "options.showSubtitles": "<PERSON><PERSON> kalama", "options.simulationDistance": "suli pi lon tawa", "options.skinCustomisation": "ante pi selo jan...", "options.skinCustomisation.title": "ante pi selo jan", "options.sounds": "kalama...", "options.sounds.title": "ante kalama", "options.telemetry": "sona Telemesi...", "options.telemetry.button": "kama jo sona", "options.telemetry.button.tooltip": "sona wile taso li lon \"%s\"\nsona ale li lon \"%s\"", "options.telemetry.disabled": "sona Telemesi li lon ala.", "options.telemetry.state.all": "ale", "options.telemetry.state.minimal": "lili", "options.telemetry.state.none": "ala", "options.title": "ante", "options.touchscreen": "o luka lon sinpin", "options.video": "ante sitelen...", "options.videoTitle": "ante sitelen", "options.viewBobbing": "tawa lili tan noka", "options.visible": "lukin", "options.vsync": "wan supa sinpin", "outOfMemory.message": "musi Manka la poki sona li pini.\n\nni li ken tan ni: pakala li lon. ante la ilo Java li ken jo e wawa sona lili.\n\npakala ma o lon ala la, musi li pini. mi alasa pana e poki sona. ni la, ken la, sina ken tawa lipu lawa li ken musi kin. taso, ni li ken pakala.\n\ntoki ni li lon tenpo tu la, o open sin e musi.", "outOfMemory.title": "sona pi ilo ni li lili tawa musi ni!", "pack.available.title": "ken kepeken", "pack.copyFailure": "mi ken ala sama e ijo", "pack.dropConfirm": "sina wile ala wile pana e ijo ni tawa musi Manka?", "pack.dropInfo": "o pana e ijo tawa lipu ni tawa kepeken ona", "pack.dropRejected.message": "ijo ni li poki ala anu poki ike la mi pana ala e ona:\n%s", "pack.dropRejected.title": "ijo ni li poki ala", "pack.folderInfo": "(o pana e poki tawa mi)", "pack.incompatible": "ken ala kepeken", "pack.incompatible.confirm.new": "ni li tan musi Manka sin li ken pakala.", "pack.incompatible.confirm.old": "ni li tan musi Manka pi tenpo pini li ken pakala.", "pack.incompatible.confirm.title": "sina wile ala wile kepeken ni?", "pack.incompatible.new": "(ni li tan musi <PERSON> sin)", "pack.incompatible.old": "(ni li tan musi Manka pi tenpo pini)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "o open e poki lon ilo nanpa", "pack.selected.title": "wile", "pack.source.builtin": "tan musi", "pack.source.feature": "ijo ante", "pack.source.local": "tan ilo", "pack.source.server": "tan ma kulupu", "pack.source.world": "ma", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "ma <PERSON><PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "ma utala", "painting.minecraft.aztec2.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "ma utala ante", "painting.minecraft.backyard.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "ma monsi", "painting.minecraft.baroque.author": "jan <PERSON>", "painting.minecraft.baroque.title": "nasin pi kule suno", "painting.minecraft.bomb.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "ilo li pakala wile", "painting.minecraft.bouquet.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "kulupu pi kasi kule", "painting.minecraft.burning_skull.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "lawa moli seli", "painting.minecraft.bust.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "kiwen jan", "painting.minecraft.cavebird.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "waso lon lupa", "painting.minecraft.changing.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "ante", "painting.minecraft.cotan.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "jan <PERSON>", "painting.minecraft.courbet.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "jan <PERSON>, toki", "painting.minecraft.creebet.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "sin<PERSON> monsuta", "painting.minecraft.dennis.author": "jan <PERSON>", "painting.minecraft.dennis.title": "sowe<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "musi <PERSON>", "painting.minecraft.earth.author": "kulupu Mojang", "painting.minecraft.earth.title": "ma", "painting.minecraft.endboss.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "monsuta pini", "painting.minecraft.fern.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "kasi pi lipu waso", "painting.minecraft.fighters.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "jan utala", "painting.minecraft.finding.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "alasa", "painting.minecraft.fire.author": "kulupu Mojang", "painting.minecraft.fire.title": "seli", "painting.minecraft.graham.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "j<PERSON>", "painting.minecraft.humble.author": "jan <PERSON>", "painting.minecraft.humble.title": "pilin pona", "painting.minecraft.kebab.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "moku Kepa en kili <PERSON>epelo<PERSON> tu wan", "painting.minecraft.lowmist.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "kon walo anpa ma", "painting.minecraft.match.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "palisa seli", "painting.minecraft.meditative.author": "jan <PERSON>", "painting.minecraft.meditative.title": "kasi loje pi pilin pona", "painting.minecraft.orb.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "sike suno", "painting.minecraft.owlemons.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "waso alasa en kili jelo", "painting.minecraft.passage.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "nasin", "painting.minecraft.pigscene.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON>len soweli", "painting.minecraft.plant.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "kasi <PERSON>ite", "painting.minecraft.pointer.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "luka utala", "painting.minecraft.pond.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "telo lili", "painting.minecraft.pool.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "poki telo suli musi", "painting.minecraft.prairie_ride.author": "jan <PERSON>", "painting.minecraft.prairie_ride.title": "tawa lon supa", "painting.minecraft.sea.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "poka pi telo suli", "painting.minecraft.skeleton.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "jan palisa moli", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "lawa moli en kasi kule", "painting.minecraft.stage.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "musi li ken open", "painting.minecraft.sunflowers.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "kasi suno", "painting.minecraft.sunset.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "pini suno", "painting.minecraft.tides.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "telo tawa", "painting.minecraft.unpacked.author": "jan <PERSON>", "painting.minecraft.unpacked.title": "sitelen open", "painting.minecraft.void.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "ma ala", "painting.minecraft.wanderer.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "jan tawa", "painting.minecraft.wasteland.author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "ma jaki", "painting.minecraft.water.author": "kulupu Mojang", "painting.minecraft.water.title": "telo", "painting.minecraft.wind.author": "kulupu Mojang", "painting.minecraft.wind.title": "kon", "painting.minecraft.wither.author": "kulupu Mojang", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "ken ale", "parsing.bool.expected": "mi wile e \"wile\" anu \"ala\"", "parsing.bool.invalid": "mi wile e \"wile\" anu \"ala\". taso, mi kama jo e \"%s\"", "parsing.double.expected": "mi wile e nanpa pi kipisi mute", "parsing.double.invalid": "nanpa \"%s\" li ike", "parsing.expected": "mi wile e \"%s\"", "parsing.float.expected": "mi wile e nanpa pi kipisi lili", "parsing.float.invalid": "nanpa \"%s\" li ike", "parsing.int.expected": "mi wile e nanpa pi kipisi ala", "parsing.int.invalid": "nanpa \"%s\" li ike", "parsing.long.expected": "mi wile e nanpa pi nasin suli", "parsing.long.invalid": "nanpa \"%s\" li ike", "parsing.quote.escape": "linja sitelen la kulupu sitelen li \"\\%s\" ala li ike", "parsing.quote.expected.end": "sina pini ala e linja sitelen", "parsing.quote.expected.start": "sina pana ala e sitelen \". taso, sina wile open e linja toki", "particle.invalidOptions": "mi sona ala e ante pi ijo lili: %s", "particle.notFound": "lili ni li ike: %s", "permissions.requires.entity": "toki wawa ni li wile e ijo", "permissions.requires.player": "toki wawa ni li wile e jan", "potion.potency.1": "pi wawa tu", "potion.potency.2": "pi wawa tu wan", "potion.potency.3": "pi wawa tu tu", "potion.potency.4": "pi wawa luka", "potion.potency.5": "pi wawa luka wan", "potion.whenDrank": "kepeken la", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "nimi lili awen ike: %s", "quickplay.error.invalid_identifier": "mi ken ala alasa e ma ni kepeken nimi ni", "quickplay.error.realm_connect": "mi ken ala tawa ma <PERSON>", "quickplay.error.realm_permission": "sina jo ala e ken tawa ni: sina tawa ma Realm", "quickplay.error.title": "mi ken ala musi pi wawa tenpo", "realms.configuration.region.australia_east": "kipisi ma <PERSON>usawe, pi ma <PERSON>", "realms.configuration.region.australia_southeast": "kipisi ma Witowija, pi ma Oseja", "realms.configuration.region.brazil_south": "<PERSON> <PERSON><PERSON><PERSON>", "realms.configuration.region.central_india": "ma <PERSON><PERSON>a", "realms.configuration.region.central_us": "kipisi ma <PERSON>, pi ma <PERSON>", "realms.configuration.region.east_asia": "ma <PERSON><PERSON>", "realms.configuration.region.east_us": "kip<PERSON> ma <PERSON>, pi ma <PERSON>", "realms.configuration.region.east_us_2": "kipisi ma <PERSON> pi ma <PERSON>", "realms.configuration.region.france_central": "ma <PERSON><PERSON>", "realms.configuration.region.japan_east": "kipisi pi ma <PERSON>jon lon poka pi open suno", "realms.configuration.region.japan_west": "kipisi pi ma <PERSON>jon lon poka pi pini suno", "realms.configuration.region.korea_central": "ma <PERSON><PERSON>", "realms.configuration.region.north_central_us": "kipisi ma <PERSON>, pi ma <PERSON>", "realms.configuration.region.north_europe": "ma <PERSON>e", "realms.configuration.region.south_central_us": "kipisi ma <PERSON>, pi ma <PERSON>", "realms.configuration.region.southeast_asia": "ma <PERSON><PERSON><PERSON>", "realms.configuration.region.sweden_central": "<PERSON> <PERSON><PERSON>", "realms.configuration.region.uae_north": "ma <PERSON><PERSON><PERSON>", "realms.configuration.region.uk_south": "ma <PERSON>ke lon poka pi ma tomo <PERSON>", "realms.configuration.region.west_central_us": "kipisi ma <PERSON>, pi ma <PERSON>", "realms.configuration.region.west_europe": "<PERSON> <PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "kip<PERSON> ma <PERSON>, pi ma <PERSON>", "realms.configuration.region.west_us_2": "kip<PERSON> ma <PERSON>, pi ma <PERSON> ", "realms.configuration.region_preference.automatic_owner": "ilo li wile e ma (tan ma ni: jan lawa pi ma Realm li lon ona)", "realms.configuration.region_preference.automatic_player": "ilo li wile e ma (ma pi jan kama nanpa wan)", "realms.missing.snapshot.error.text": "nanpa namako pi musi Manka la sina ken ala kepeken e ma <PERSON>s", "recipe.notFound": "nasin pali ni li ike: %s", "recipe.toast.description": "o lukin e lipu sina pali", "recipe.toast.title": "sina sona e nasin pali sin!", "record.nowPlaying": "sina kute e ni: %s", "recover_world.bug_tracker": "o toki e pakala", "recover_world.button": "o alasa pona e ma", "recover_world.done.failed": "mi ken ala alasa pona tan lon pini.", "recover_world.done.success": "alasa pona li pali pona!", "recover_world.done.title": "alasa pona li pini", "recover_world.issue.missing_file": "lipu weka", "recover_world.issue.none": "pakala ala", "recover_world.message": "mi lukin e poki ma \"%s\" la, pakala ni li lon.\nmi ken open e ma pini anu sina pana e pakala ni lon lipu pi pakala musi.", "recover_world.no_fallback": "lon pini ala li lon la, mi ken ala alasa pona", "recover_world.restore": "o alasa pona e ma", "recover_world.restoring": "mi alasa pona e ma...", "recover_world.state_entry": "lon pini tan %s: ", "recover_world.state_entry.unknown": "mi sona ala", "recover_world.title": "mi ken ala open e ma", "recover_world.warning": "mi ken ala open e lipu lili ma", "resourcePack.broken_assets": "IJO PAKALA LI LON", "resourcePack.high_contrast.name": "kule pi ante suli", "resourcePack.load_fail": "mi pakala!", "resourcePack.programmer_art.name": "poki sitelen pi nasin pini", "resourcePack.runtime_failure": "mi lukin e pakala pi poki sitelen kalama", "resourcePack.server.name": "sitelen en kalama lon ma ni taso", "resourcePack.title": "sina wile ante e sitelen e kalama kepeken seme?", "resourcePack.vanilla.description": "musi li lukin ante ala li kalama ante ala", "resourcePack.vanilla.name": "nasin pi ante ala", "resourcepack.downloading": "mi kama jo e poki sitelen kalama", "resourcepack.progress": "mi kama jo e ijo pi suli MB %s", "resourcepack.requesting": "mi alasa...", "screenshot.failure": "mi ken ala poki e sitelen pi nimi %s", "screenshot.success": "mi poki e sitelen kepeken nimi %s", "selectServer.add": "ma kulupu sin", "selectServer.defaultName": "ma kulupu", "selectServer.delete": "o weka", "selectServer.deleteButton": "o weka", "selectServer.deleteQuestion": "sina wile ala wile weka e ma kulupu ni?", "selectServer.deleteWarning": "\"%s\" li weka lon tenpo ale! (tenpo suli a!)", "selectServer.direct": "o musi lon tenpo wan", "selectServer.edit": "o ante", "selectServer.hiddenAddress": "(len)", "selectServer.refresh": "o sin", "selectServer.select": "o musi", "selectWorld.access_failure": "ma li ken ala open", "selectWorld.allowCommands": "o ken e wawa ale", "selectWorld.allowCommands.info": "toki wawa li sama /gamemode, /experience", "selectWorld.allowCommands.new": "o ken e wawa ale", "selectWorld.backupEraseCache": "o weka e sona awen lon ilo sina", "selectWorld.backupJoinConfirmButton": "o pali e awen ma. o lon e ma.", "selectWorld.backupJoinSkipButton": "o awen ala o lon e ma a!", "selectWorld.backupQuestion.customized": "tenpo ni la sina ken ala lawa e pali ma", "selectWorld.backupQuestion.downgrade": "sina ken ala lon e ma tawa musi pi tenpo pini", "selectWorld.backupQuestion.experimental": "sina ken ala kepeken ma pi nasin nasa", "selectWorld.backupQuestion.snapshot": "sina wile ala wile lon e ma ni?", "selectWorld.backupWarning.customized": "sina ken ala lawa e pali ma tan ni: musi li sin ala. mi ken lon e ma ni, taso sina tawa la ma lon sina li ante ala.", "selectWorld.backupWarning.downgrade": "tenpo poka la sina musi lon ma ni kepeken musi nanpa %s. sina lon musi pi nanpa %s. sina ante monsi e ma la ona li ken pakala. sina wile ante e ona la, o pali e ma awen sama ma ni!", "selectWorld.backupWarning.experimental": "ma ni li kepeken nasin nasa. tenpo ale la ona li ken pakala. mi sona ala e ni: ma ni li pali pona.", "selectWorld.backupWarning.snapshot": "ma ni li tan musi nanpa %s. tenpo ni la, sina kepeken musi nanpa %s. ma ni li ken pakala la, o pali e sama pi ma ni!", "selectWorld.bonusItems": "poki open", "selectWorld.cheats": "toki wawa", "selectWorld.commands": "pali ilo", "selectWorld.conversion": "sina wile ante e sona nanpa pi ma ni!", "selectWorld.conversion.tooltip": "sina wile ante e nanpa musi la o open e ma ni kepeken nanpa musi pi tenpo pini (1.6.4 anu ante)", "selectWorld.create": "o pali e ma sin", "selectWorld.customizeType": "o ante", "selectWorld.dataPacks": "toki wawa", "selectWorld.data_read": "mi lukin e ma...", "selectWorld.delete": "o weka", "selectWorld.deleteButton": "o weka", "selectWorld.deleteQuestion": "sina wile ala wile weka e ma ni?", "selectWorld.deleteWarning": "\"%s\" li weka lon tenpo ale! (tenpo ni li suli!)", "selectWorld.delete_failure": "mi ken ala weka e ma", "selectWorld.edit": "o ante", "selectWorld.edit.backup": "o pali e ma awen sama ma ni", "selectWorld.edit.backupCreated": "mute pi sona nanpa lon poki: %s", "selectWorld.edit.backupFailed": "mi pakala! :(", "selectWorld.edit.backupFolder": "o open e poki pi ma awen", "selectWorld.edit.backupSize": "mute suli: %s MB", "selectWorld.edit.export_worldgen_settings": "o pana e nasin wile pi pali ma", "selectWorld.edit.export_worldgen_settings.failure": "pana li pakala", "selectWorld.edit.export_worldgen_settings.success": "pana li pini pona", "selectWorld.edit.openFolder": "o open e poki ma", "selectWorld.edit.optimize": "o pona e ma", "selectWorld.edit.resetIcon": "o weka e sitelen lili", "selectWorld.edit.save": "o awen", "selectWorld.edit.title": "o ante e ma", "selectWorld.enterName": "nimi ma", "selectWorld.enterSeed": "nanpa tan pi ilo pali ma", "selectWorld.experimental": "pini ala", "selectWorld.experimental.details": "sona", "selectWorld.experimental.details.entry": "musi li wile e nasin nasa ni: %s", "selectWorld.experimental.details.title": "wile pi nasin nasa", "selectWorld.experimental.message": "o sona!\nante pi wile sina li kepeken nasin nasa. ma sina li ken pakala lon tenpo ni anu tenpo kama.", "selectWorld.experimental.title": "nasin nasa!", "selectWorld.experiments": "ante sin", "selectWorld.experiments.info": "ni li ante sin. ken la, ijo ni li pakala lon tenpo kama. sina pali e ma la, sina ken ala weka e ni.", "selectWorld.futureworld.error.text": "pakala la, sina lukin open e ma pi tenpo kama. ni li pali ike la, ona li pali ala.", "selectWorld.futureworld.error.title": "pakala li lon!", "selectWorld.gameMode": "nasin musi", "selectWorld.gameMode.adventure": "jan alasa", "selectWorld.gameMode.adventure.info": "ni li sama nasin pi jan ma, taso sina ken ala pana anu weka e leko.", "selectWorld.gameMode.adventure.line1": "ni li sama nasin musi pi jan ma, taso sina", "selectWorld.gameMode.adventure.line2": "ken ala pana li ken ala pakala e ijo", "selectWorld.gameMode.creative": "jan sewi", "selectWorld.gameMode.creative.info": "sina ken pali e ijo ale. sina ken waso, sina ken kama jo e ijo ale, sina ken ala pakala tan monsuta.", "selectWorld.gameMode.creative.line1": "sina jo e leko ale, li ken tawa lon kon,", "selectWorld.gameMode.creative.line2": "li pakala e ijo kepeken tenpo ala", "selectWorld.gameMode.hardcore": "jan pi lon wan taso", "selectWorld.gameMode.hardcore.info": "mute ike li 'mute' li ken ala ante. sina moli la sina ken ala kama sin.", "selectWorld.gameMode.hardcore.line1": "ni li sama nasin musi pi jan ma. taso sina moli la, ma li moli kin.", "selectWorld.gameMode.hardcore.line2": "sina moli la, ma li moli kin.", "selectWorld.gameMode.spectator": "jan lukin", "selectWorld.gameMode.spectator.info": "sina kon. sina ken lukin, taso ijo ante ala li ken.", "selectWorld.gameMode.spectator.line1": "sina ken lukin taso sina ken ala pilin", "selectWorld.gameMode.survival": "jan ma", "selectWorld.gameMode.survival.info": "o kama lukin e ma sin. sina ken pali e ilo e tomo, li ken utala e monsuta.", "selectWorld.gameMode.survival.line1": "o alasa o pali e ijo, o kama e", "selectWorld.gameMode.survival.line2": "wawa, li ken moli, li wile moku", "selectWorld.gameRules": "lawa musi", "selectWorld.import_worldgen_settings": "o kepeken lawa tan lipu", "selectWorld.import_worldgen_settings.failure": "pakala li lon lipu lawa", "selectWorld.import_worldgen_settings.select_file": "o pana e lipu lawa (.json)", "selectWorld.incompatible.description": "mi ken ala open e ma ni. nanpa musi pi ma ni li %s.", "selectWorld.incompatible.info": "ma ni li lon musi pi nanpa ike: %s", "selectWorld.incompatible.title": "ma pi nanpa musi ike", "selectWorld.incompatible.tooltip": "mi ken ala open e ma ni tan ni: mi pali e ma ni lon musi pi nanpa ike.", "selectWorld.incompatible_series": "jan li pali e ni kepeken nanpa musi ike", "selectWorld.load_folder_access": "mi ken ala open e poki pi awen ma!", "selectWorld.loading_list": "mi lon e lipu ma", "selectWorld.locked": "sina musi e ma ni lon musi Manka open ante", "selectWorld.mapFeatures": "o kama e tomo", "selectWorld.mapFeatures.info": "ma tomo, tomo telo pakala, ijo ante sama.", "selectWorld.mapType": "nasin ma", "selectWorld.mapType.normal": "pona", "selectWorld.moreWorldOptions": "o ante e ma...", "selectWorld.newWorld": "ma sin", "selectWorld.recreate": "o pali sin", "selectWorld.recreate.customized.text": "tenpo ni la sina ken ala lawa e pali ma. mi ken alasa e pali sama, taso ante ma li weka.", "selectWorld.recreate.customized.title": "sina ken ala lawa e pali ma sama ni lon nanpa musi ni", "selectWorld.recreate.error.text": "mi alasa e pali sin pi ma ni la, pakala li kama :(", "selectWorld.recreate.error.title": "pakala li lon!", "selectWorld.resource_load": "mi pali e ijo...", "selectWorld.resultFolder": "mi awen e ona lon:", "selectWorld.search": "o alasa e ma", "selectWorld.seedInfo": "sina sitelen ala la mi pali e nimi namako", "selectWorld.select": "o open e ma ni", "selectWorld.targetFolder": "mi awen e ona lon lipu ni: %s", "selectWorld.title": "sina wile e ma seme?", "selectWorld.tooltip.fromNewerVersion1": "ma ni li tan musi <PERSON>ka sin.", "selectWorld.tooltip.fromNewerVersion2": "ni la, lon pi ma ni li ken pakala a!", "selectWorld.tooltip.snapshot1": "sina pali e awen pi ma ni anu seme?", "selectWorld.tooltip.snapshot2": "o pali e ma awen sama ma ni.", "selectWorld.unable_to_load": "mi ken ala lon e ma ni", "selectWorld.version": "sin musi:", "selectWorld.versionJoinButton": "o lon e ma", "selectWorld.versionQuestion": "sina wile ala wile lon e ma ni?", "selectWorld.versionUnknown": "mi sona ala", "selectWorld.versionWarning": "ma ni li tan musi Manka nanpa %s. sina open e ni la ona li ken pakala!", "selectWorld.warning.deprecated.question": "sina kepeken nasin pi nanpa musi pini. tenpo kama la ona li pakala. sina wile kepeken ona anu seme?", "selectWorld.warning.deprecated.title": "o sona! sina kepeken nasin pi nanpa musi pini", "selectWorld.warning.experimental.question": "nasin ni li nasa li ken pakala lon tenpo kama. sina wile kepeken ona anu seme?", "selectWorld.warning.experimental.title": "o sona! sina kepeken nasin nasa", "selectWorld.warning.lowDiskSpace.description": "poki sona sina li ken ala kama jo e sona mute sin. \nsona ale ken li lon ona la sina lon musi la ma sina li ken pakala.", "selectWorld.warning.lowDiskSpace.title": "o sona! poki awen li lili!", "selectWorld.world": "ma", "sign.edit": "o sitelen lon lipu kiwen sitelen", "sleep.not_possible": "lape li ken ala weka e tenpo pimeja ni", "sleep.players_sleeping": "jan wile %s la jan %s li lape", "sleep.skipping_night": "lape li weka e tenpo pimeja ni", "slot.only_single_allowed": "poki jo wan taso li ken. ni li ike: '%s'", "slot.unknown": "poki jo \"%s\" li ike", "snbt.parser.empty_key": "ilo open li ala, ni li ike", "snbt.parser.expected_binary_numeral": "mi wile e nanpa pi nasin nanpa 2", "snbt.parser.expected_decimal_numeral": "mi wile e nanpa pi nasin nanpa 10", "snbt.parser.expected_float_type": "mi wile e nanpa pi ken kipisi", "snbt.parser.expected_hex_escape": "mi wile e nanpa nimi pi sitelen %s", "snbt.parser.expected_hex_numeral": "mi wile e nanpa pi nasin nanpa 16", "snbt.parser.expected_integer_type": "mi wile e nanpa pi kipisi ala", "snbt.parser.expected_non_negative_number": "mi wile e nanpa weka ala", "snbt.parser.expected_number_or_boolean": "mi wile e nanpa anu \"wile\" anu \"ala\"", "snbt.parser.expected_string_uuid": "mi wile e linja sitelen sama nimi UUID pona", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "nanpa pi pini ala li ken ala", "snbt.parser.invalid_array_element_type": "nasin pi ijo kulupu li ike", "snbt.parser.invalid_character_name": "nimi ni li lon ala <PERSON>len <PERSON>", "snbt.parser.invalid_codepoint": "nanpa ni li lon ala sitelen Juniko: %s", "snbt.parser.invalid_string_contents": "sitelen insa li ike", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "nanpa pi nasin nanpa 10 li ken ala open kepeken sitelen 0", "snbt.parser.no_such_operation": "pali %s li lon ala", "snbt.parser.number_parse_failure": "mi sona ala e nanpa ni: %s", "snbt.parser.undescore_not_allowed": "sitelen pi linja anpa li ken ala lon open nanpa lon pini nanpa", "soundCategory.ambient": "kalama tan ma", "soundCategory.block": "kalama tan ijo", "soundCategory.hostile": "mu monsuta", "soundCategory.master": "kalama ale", "soundCategory.music": "kalama musi", "soundCategory.neutral": "mu soweli", "soundCategory.player": "kalama jan", "soundCategory.record": "kalama musi tan ilo", "soundCategory.ui": "kalama tan lipu ", "soundCategory.voice": "toki", "soundCategory.weather": "kalama sewi", "spectatorMenu.close": "ala", "spectatorMenu.next_page": "o tawa sinpin", "spectatorMenu.previous_page": "o tawa monsi", "spectatorMenu.root.prompt": "sina wile e ilo seme?", "spectatorMenu.team_teleport": "o tawa jan pi kulupu sama", "spectatorMenu.team_teleport.prompt": "sina wile tawa kulupu seme?", "spectatorMenu.teleport": "o tawa jan", "spectatorMenu.teleport.prompt": "sina wile tawa jan seme?", "stat.generalButton": "ijo ante", "stat.itemsButton": "ijo", "stat.minecraft.animals_bred": "soweli lili sin", "stat.minecraft.aviate_one_cm": "mute tawa kepeken luka waso", "stat.minecraft.bell_ring": "kepeken pi ilo kalama jelo", "stat.minecraft.boat_one_cm": "mute tawa kepeken ilo tawa telo", "stat.minecraft.clean_armor": "weka kule len", "stat.minecraft.clean_banner": "weka kule pi len sitelen", "stat.minecraft.clean_shulker_box": "weka kule tan poki pi mon<PERSON><PERSON>", "stat.minecraft.climb_one_cm": "mute pi tawa sewi", "stat.minecraft.crouch_one_cm": "mute pi tawa lili", "stat.minecraft.damage_absorbed": "pakala tawa pilin jelo sina", "stat.minecraft.damage_blocked_by_shield": "pakala weka kepeken sinpin sijelo", "stat.minecraft.damage_dealt": "pana pakala", "stat.minecraft.damage_dealt_absorbed": "pana pakala tawa pilin jelo", "stat.minecraft.damage_dealt_resisted": "pana pi pakala pini", "stat.minecraft.damage_resisted": "pakala weka", "stat.minecraft.damage_taken": "pakala pi sijelo sina", "stat.minecraft.deaths": "moli sina", "stat.minecraft.drop": "pana anpa ijo", "stat.minecraft.eat_cake_slice": "moku pi pan suwi kipisi", "stat.minecraft.enchant_item": "pana wawa ijo", "stat.minecraft.fall_one_cm": "mute pi tawa anpa", "stat.minecraft.fill_cauldron": "pana telo tawa poki suli", "stat.minecraft.fish_caught": "alasa kala", "stat.minecraft.fly_one_cm": "mute pi tawa kon", "stat.minecraft.happy_ghast_one_cm": "mute tawa lon waso pona Ghast", "stat.minecraft.horse_one_cm": "mute tawa lon soweli tawa", "stat.minecraft.inspect_dispenser": "alasa lon insa pi poki pana wawa", "stat.minecraft.inspect_dropper": "alasa lon insa pi poki pana", "stat.minecraft.inspect_hopper": "alasa lon poki pi tawa ijo", "stat.minecraft.interact_with_anvil": "kepeken pi ilo wan", "stat.minecraft.interact_with_beacon": "kepeken pi ilo wawa", "stat.minecraft.interact_with_blast_furnace": "kepeken poki pi seli kiwen", "stat.minecraft.interact_with_brewingstand": "kepeken ilo pi telo wawa", "stat.minecraft.interact_with_campfire": "kepeken pi seli palisa", "stat.minecraft.interact_with_cartography_table": "kepeken supa pi sitelen ma", "stat.minecraft.interact_with_crafting_table": "kepeken pi supa pali", "stat.minecraft.interact_with_furnace": "kepeken pi poki seli", "stat.minecraft.interact_with_grindstone": "kepeken sike pi pona ilo", "stat.minecraft.interact_with_lectern": "kepeken pi supa lipu", "stat.minecraft.interact_with_loom": "kepeken pi ilo len", "stat.minecraft.interact_with_smithing_table": "kepeken pi supa ilo", "stat.minecraft.interact_with_smoker": "kepeken poki pi seli moku", "stat.minecraft.interact_with_stonecutter": "kepeken ilo pi kipisi kiwen", "stat.minecraft.jump": "nanpa pi tawa sewi", "stat.minecraft.leave_game": "pini musi", "stat.minecraft.minecart_one_cm": "mute tawa lon poki tawa", "stat.minecraft.mob_kills": "pana moli monsuta", "stat.minecraft.open_barrel": "open pi poki sike", "stat.minecraft.open_chest": "open poki", "stat.minecraft.open_enderchest": "open poki pi ma End", "stat.minecraft.open_shulker_box": "open poki pi mon<PERSON><PERSON>", "stat.minecraft.pig_one_cm": "tawa kepeken soweli pi loje walo", "stat.minecraft.play_noteblock": "kepeken pi leko kalama", "stat.minecraft.play_record": "kute pi sike kalama", "stat.minecraft.play_time": "mute pi tenpo musi", "stat.minecraft.player_kills": "pana moli jan", "stat.minecraft.pot_flower": "pana kasi lon poki kasi", "stat.minecraft.raid_trigger": "open pi utala kulupu", "stat.minecraft.raid_win": "pini pona pi utala kulupu", "stat.minecraft.sleep_in_bed": "mute lape", "stat.minecraft.sneak_time": "tenpo pi tawa anpa", "stat.minecraft.sprint_one_cm": "mute pi tawa wawa", "stat.minecraft.strider_one_cm": "mute tawa lon akesi seli", "stat.minecraft.swim_one_cm": "mute tawa lon insa telo", "stat.minecraft.talked_to_villager": "toki tawa jan tomo", "stat.minecraft.target_hit": "kepeken sike pi alasa musi", "stat.minecraft.time_since_death": "tenpo pi moli ala", "stat.minecraft.time_since_rest": "tenpo pi lape ala", "stat.minecraft.total_world_time": "tenpo lon ma ni", "stat.minecraft.traded_with_villager": "esun pi jan tomo", "stat.minecraft.trigger_trapped_chest": "open pi poki wawa", "stat.minecraft.tune_noteblock": "ante pi leko kalama", "stat.minecraft.use_cauldron": "weka telo tan poki suli", "stat.minecraft.walk_on_water_one_cm": "mute tawa lon selo telo", "stat.minecraft.walk_one_cm": "mute pi tawa noka", "stat.minecraft.walk_under_water_one_cm": "mute tawa lon anpa telo", "stat.mobsButton": "soweli en monsuta", "stat_type.minecraft.broken": "nanpa pi pakala ilo", "stat_type.minecraft.crafted": "nanpa pali", "stat_type.minecraft.dropped": "pana anpa", "stat_type.minecraft.killed": "sina moli e %2$s %1$s", "stat_type.minecraft.killed.none": "sina moli ala e %s", "stat_type.minecraft.killed_by": "%s li moli e sina lon tenpo %s", "stat_type.minecraft.killed_by.none": "%s li moli ala e sina", "stat_type.minecraft.mined": "nanpa pakala", "stat_type.minecraft.picked_up": "kama jo", "stat_type.minecraft.used": "nanpa kepeken", "stats.none": "-", "structure_block.button.detect_size": "LUKIN", "structure_block.button.load": "o lon", "structure_block.button.save": "o poki", "structure_block.custom_data": "nimi tomo la sina wile pali", "structure_block.detect_size": "o lukin e awen tomo e suli tomo:", "structure_block.hover.corner": "pini poka: %s", "structure_block.hover.data": "sona nanpa: %s", "structure_block.hover.load": "o lon e '%s'", "structure_block.hover.save": "o awen e '%s'", "structure_block.include_entities": "o poki e ijo lon insa:", "structure_block.integrity": "ante nanpa pi leko weka", "structure_block.integrity.integrity": "mute nanpa pi leko weka", "structure_block.integrity.seed": "nimi nanpa tomo", "structure_block.invalid_structure_name": "'%s' li nimi tomo ike", "structure_block.load_not_found": "tomo \"%s\" li ken lon ala", "structure_block.load_prepare": "pana pi tomo '%s' li awen", "structure_block.load_success": "mi lon e tomo tan '%s'", "structure_block.mode.corner": "pini poka", "structure_block.mode.data": "sona nanpa", "structure_block.mode.load": "o lon", "structure_block.mode.save": "o awen", "structure_block.mode_info.corner": "Corner Mode - Placement and size marker", "structure_block.mode_info.data": "Data Mode - Game logic marker", "structure_block.mode_info.load": "nasin pali: lon - lon tan lipu", "structure_block.mode_info.save": "nasin pali: poki - poki tawa lipu", "structure_block.position": "awen ma tan ilo ni", "structure_block.position.x": "nanpa X pi ma awen tan ilo ni", "structure_block.position.y": "nanpa Y pi ma awen tan ilo ni", "structure_block.position.z": "nanpa Z pi ma awen tan ilo ni", "structure_block.save_failure": "mi ken ala awen e tomo '%s'", "structure_block.save_success": "mi awen e tomo kepeken nimi '%s'", "structure_block.show_air": "o lukin e leko kon:", "structure_block.show_boundingbox": "o lukin e selo:", "structure_block.size": "suli tomo", "structure_block.size.x": "suli tomo x", "structure_block.size.y": "suli tomo y", "structure_block.size.z": "suli tomo z", "structure_block.size_failure": "mi ken ala lukin e suli tomo. o pana e pini poka kepeken nimi tomo sama", "structure_block.size_success": "mi kama lukin e suli pi tomo '%s'", "structure_block.strict": "o lon e leko pi sin ala:", "structure_block.structure_name": "nimi tomo", "subtitles.ambient.cave": "kalama monsuta", "subtitles.ambient.sound": "kalama monsuta", "subtitles.block.amethyst_block.chime": "kiwen li kalama suwi", "subtitles.block.amethyst_block.resonate": "kiwen li kalama sama", "subtitles.block.anvil.destroy": "supa pona li pakala", "subtitles.block.anvil.land": "supa pona li kama anpa", "subtitles.block.anvil.use": "jan li kepeken supa pona", "subtitles.block.barrel.close": "poki sike li kama pini", "subtitles.block.barrel.open": "poki sike li open", "subtitles.block.beacon.activate": "ilo wawa li open", "subtitles.block.beacon.ambient": "ilo wawa li kalama", "subtitles.block.beacon.deactivate": "ilo wawa li pini", "subtitles.block.beacon.power_select": "jan li lawa e ilo wawa", "subtitles.block.beehive.drip": "suwi pipi li tawa anpa", "subtitles.block.beehive.enter": "pipi suwi li tawa insa tomo", "subtitles.block.beehive.exit": "pipi suwi li weka tan tomo", "subtitles.block.beehive.shear": "ilo li kipisi e tomo pipi", "subtitles.block.beehive.work": "pipi suwi li pali", "subtitles.block.bell.resonate": "ilo kalama jelo li awen kalama", "subtitles.block.bell.use": "ilo kalama jelo li open kalama", "subtitles.block.big_dripleaf.tilt_down": "kasi supa li anpa", "subtitles.block.big_dripleaf.tilt_up": "kasi supa li sewi", "subtitles.block.blastfurnace.fire_crackle": "poki pi seli kiwen li kalama", "subtitles.block.brewing_stand.brew": "ilo pi telo wawa li kalama", "subtitles.block.bubble_column.bubble_pop": "sike kon lili li pakala", "subtitles.block.bubble_column.upwards_ambient": "sike kon lili li tawa", "subtitles.block.bubble_column.upwards_inside": "sike kon lili li kalama", "subtitles.block.bubble_column.whirlpool_ambient": "sike kon lili li tawa", "subtitles.block.bubble_column.whirlpool_inside": "sike kon lili li tawa", "subtitles.block.button.click": "nena ilo li kalama", "subtitles.block.cake.add_candle": "jan li lupa e pan suwi", "subtitles.block.campfire.crackle": "seli palisa li kalama", "subtitles.block.candle.crackle": "palisa suno li seli", "subtitles.block.candle.extinguish": "palisa suno lili li lete", "subtitles.block.chest.close": "poki li kama pini", "subtitles.block.chest.locked": "poki li ken ala open", "subtitles.block.chest.open": "poki li open", "subtitles.block.chorus_flower.death": "lawa kasi nasa li moli", "subtitles.block.chorus_flower.grow": "lawa kasi nasa li kama suli", "subtitles.block.comparator.click": "ilo pi ante wawa li kalama lili", "subtitles.block.composter.empty": "jaki li weka tan poki", "subtitles.block.composter.fill": "poki jaki li kama jo e jaki", "subtitles.block.composter.ready": "poki jaki li pali", "subtitles.block.conduit.activate": "ilo wawa telo li open", "subtitles.block.conduit.ambient": "ilo wawa telo li tawa sama pilin", "subtitles.block.conduit.attack.target": "ilo wawa telo li utala", "subtitles.block.conduit.deactivate": "ilo wawa telo li pini", "subtitles.block.copper_bulb.turn_off": "kiwen ante suno li pini suno", "subtitles.block.copper_bulb.turn_on": "suno ante li open suno", "subtitles.block.copper_trapdoor.close": "lupa anpa li pini", "subtitles.block.copper_trapdoor.open": "lupa anpa li open", "subtitles.block.crafter.craft": "ilo pali li pali", "subtitles.block.crafter.fail": "ilo pali li pakala pali", "subtitles.block.creaking_heart.hurt": "lawa pi monsuta kasi li kalama wawa tan pakala", "subtitles.block.creaking_heart.idle": "kalama monsuta", "subtitles.block.creaking_heart.spawn": "lawa pi monsuta kasi li kama e monsuta", "subtitles.block.deadbush.idle": "mu pi kasi moli", "subtitles.block.decorated_pot.insert": "ijo li tawa insa pi poki kiwen sitelen", "subtitles.block.decorated_pot.insert_fail": "poki kiwen sitelen li ken ala poki", "subtitles.block.decorated_pot.shatter": "poki kiwen sitelen li pakala", "subtitles.block.dispenser.dispense": "poki pi pana wawa li pana", "subtitles.block.dispenser.fail": "poki pi pana wawa li ken ala pana", "subtitles.block.door.toggle": "lupa li kalama", "subtitles.block.dried_ghast.ambient": "ijo pi telo ala li mu", "subtitles.block.dried_ghast.ambient_water": "ki<PERSON> Ghas<PERSON> pi telo ala li mu telo", "subtitles.block.dried_ghast.place_in_water": "ki<PERSON> Ghast pi telo ala li kama insa e telo", "subtitles.block.dried_ghast.transition": "ki<PERSON> Ghas<PERSON> pi telo ala li pilin pona", "subtitles.block.dry_grass.ambient": "kalama kon", "subtitles.block.enchantment_table.use": "supa wawa li pali", "subtitles.block.end_portal.spawn": "lupa pi ma End li open", "subtitles.block.end_portal_frame.fill": "oko pi ma End li kama awen", "subtitles.block.eyeblossom.close": "kasi oko li kama pini", "subtitles.block.eyeblossom.idle": "kasi oko li mu lili", "subtitles.block.eyeblossom.open": "kasi oko li kama open", "subtitles.block.fence_gate.toggle": "sinpin tawa li kalama", "subtitles.block.fire.ambient": "seli li kalama", "subtitles.block.fire.extinguish": "seli li weka", "subtitles.block.firefly_bush.idle": "pipi suno li mu", "subtitles.block.frogspawn.hatch": "sike mama pi akesi sewi lili li open", "subtitles.block.furnace.fire_crackle": "poki seli li kalama", "subtitles.block.generic.break": "ijo li pakala", "subtitles.block.generic.fall": "ijo li tawa anpa lon leko", "subtitles.block.generic.footsteps": "kalama noka", "subtitles.block.generic.hit": "leko li kama pakala", "subtitles.block.generic.place": "jan li pana e ijo", "subtitles.block.grindstone.use": "jan li kepeken sike pi pona ilo", "subtitles.block.growing_plant.crop": "kasi li pini kama suli", "subtitles.block.hanging_sign.waxed_interact_fail": "lipu sitelen li tawa nasa", "subtitles.block.honey_block.slide": "jan li tawa anpa lon suwi pipi", "subtitles.block.iron_trapdoor.close": "lupa anpa li pini", "subtitles.block.iron_trapdoor.open": "lupa anpa li open", "subtitles.block.lava.ambient": "telo seli li kalama", "subtitles.block.lava.extinguish": "telo seli li kalama", "subtitles.block.lever.click": "palisa ilo li kalama", "subtitles.block.note_block.note": "leko kalama li kalama", "subtitles.block.pale_hanging_moss.idle": "kalama monsuta", "subtitles.block.piston.move": "ilo tawa li tawa", "subtitles.block.pointed_dripstone.drip_lava": "telo seli li kama anpa", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "telo seli li anpa tawa poki telo suli", "subtitles.block.pointed_dripstone.drip_water": "telo li kama anpa", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "telo li anpa tawa poki telo suli", "subtitles.block.pointed_dripstone.land": "palisa kiwen li kama anpa", "subtitles.block.portal.ambient": "lupa nasa li mu", "subtitles.block.portal.travel": "kalama pi lupa nasa li weka", "subtitles.block.portal.trigger": "kalama pi lupa wawa li suli", "subtitles.block.pressure_plate.click": "supa noka li kalama", "subtitles.block.pumpkin.carve": "ilo kipisi lili li pali", "subtitles.block.redstone_torch.burnout": "palisa suno li weka", "subtitles.block.respawn_anchor.ambient": "ilo pi kama sin li mu", "subtitles.block.respawn_anchor.charge": "ilo pi kama sin li wawa", "subtitles.block.respawn_anchor.deplete": "wawa li weka tan ilo pi kama sin", "subtitles.block.respawn_anchor.set_spawn": "ilo pi kama sin li awen e sina", "subtitles.block.sand.idle": "kalama ko", "subtitles.block.sand.wind": "kalama kon", "subtitles.block.sculk.charge": "soko Sculk li pana e sike wawa", "subtitles.block.sculk.spread": "soko Sculk li kama suli", "subtitles.block.sculk_catalyst.bloom": "soko Sculk mama li pana", "subtitles.block.sculk_sensor.clicking": "soko Sculk kute li open kalama", "subtitles.block.sculk_sensor.clicking_stop": "soko Sculk kute li pini kalama", "subtitles.block.sculk_shrieker.shriek": "soko Sculk kalama li mu", "subtitles.block.shulker_box.close": "poki pi monsuta <PERSON> li pini", "subtitles.block.shulker_box.open": "poki pi mon<PERSON><PERSON> li open", "subtitles.block.sign.waxed_interact_fail": "lipu sitelen li mu tawa", "subtitles.block.smithing_table.use": "supa ilo li pali", "subtitles.block.smoker.smoke": "poki pi seli moku li pali", "subtitles.block.sniffer_egg.crack": "sike mama pi soweli suli alasa li pakala", "subtitles.block.sniffer_egg.hatch": "sike mama pi soweli suli alasa li open", "subtitles.block.sniffer_egg.plop": "soweli suli alasa Ii kama mama", "subtitles.block.sponge.absorb": "ko li weka e telo", "subtitles.block.sweet_berry_bush.pick_berries": "kili suwi li weka tan kasi", "subtitles.block.trapdoor.close": "lupa anpa li pini", "subtitles.block.trapdoor.open": "lupa anpa li open", "subtitles.block.trapdoor.toggle": "lupa anpa li kalama", "subtitles.block.trial_spawner.about_to_spawn_item": "ijo ike li kama pana", "subtitles.block.trial_spawner.ambient": "ilo kama pi tomo alasa li kalama", "subtitles.block.trial_spawner.ambient_charged": "ilo kama ike pi tomo alasa li mu", "subtitles.block.trial_spawner.ambient_ominous": "ilo kama ike pi tomo alasa li mu", "subtitles.block.trial_spawner.charge_activate": "ike kama li selo e ilo kama pi tomo alasa", "subtitles.block.trial_spawner.close_shutter": "ilo kama pi tomo alasa li pini", "subtitles.block.trial_spawner.detect_player": "ilo kama pi tomo alasa li kama wawa", "subtitles.block.trial_spawner.eject_item": "ilo kama pi tomo alasa li pana", "subtitles.block.trial_spawner.ominous_activate": "ike kama li selo e ilo kama pi tomo alasa", "subtitles.block.trial_spawner.open_shutter": "ilo kama pi tomo alasa li open", "subtitles.block.trial_spawner.spawn_item": "ijo ike li pana", "subtitles.block.trial_spawner.spawn_item_begin": "ijo ike li lon", "subtitles.block.trial_spawner.spawn_mob": "monsuta li lon tan ilo kama pi tomo alasa", "subtitles.block.tripwire.attach": "linja noka li awen lon ilo", "subtitles.block.tripwire.click": "ilo pi linja noka li kalama", "subtitles.block.tripwire.detach": "linja li pakala", "subtitles.block.vault.activate": "poki mani li seli", "subtitles.block.vault.ambient": "poki mani li kalama", "subtitles.block.vault.close_shutter": "poki mani li kama pini", "subtitles.block.vault.deactivate": "poki mani li lete", "subtitles.block.vault.eject_item": "poki mani li pana", "subtitles.block.vault.insert_item": "poki mani li kama jo e ken open", "subtitles.block.vault.insert_item_fail": "poki mani li ken ala open", "subtitles.block.vault.open_shutter": "poki mani li kama open", "subtitles.block.vault.reject_rewarded_player": "poki mani li pana ala e ijo tawa jan ni", "subtitles.block.water.ambient": "telo li tawa", "subtitles.block.wet_sponge.dries": "telo li weka tan ko telo", "subtitles.chiseled_bookshelf.insert": "lipu li tawa insa poki", "subtitles.chiseled_bookshelf.insert_enchanted": "lipu wawa li tawa insa poki", "subtitles.chiseled_bookshelf.take": "lipu li weka tan poki", "subtitles.chiseled_bookshelf.take_enchanted": "lipu wawa li weka tan poki", "subtitles.enchant.thorns.hit": "selo kipisi li pana e pakala", "subtitles.entity.allay.ambient_with_item": "kon pona li alasa", "subtitles.entity.allay.ambient_without_item": "kon pona li mu wile", "subtitles.entity.allay.death": "kon pona li moli", "subtitles.entity.allay.hurt": "kon pona li pakala", "subtitles.entity.allay.item_given": "kon pona li mu musi", "subtitles.entity.allay.item_taken": "kon pona li kama jo", "subtitles.entity.allay.item_thrown": "kon pona li pana", "subtitles.entity.armadillo.ambient": "soweli pi kiwen selo li mu", "subtitles.entity.armadillo.brush": "ilo li weka e kiwen selo soweli", "subtitles.entity.armadillo.death": "soweli pi kiwen selo li moli", "subtitles.entity.armadillo.eat": "soweli pi kiwen selo li moku", "subtitles.entity.armadillo.hurt": "soweli pi kiwen selo li pakala", "subtitles.entity.armadillo.hurt_reduced": "soweli pi kiwen selo li awen", "subtitles.entity.armadillo.land": "soweli pi kiwen selo li ma", "subtitles.entity.armadillo.peek": "soweli pi kiwen selo li lukin lili", "subtitles.entity.armadillo.roll": "soweli pi kiwen selo li kama sike", "subtitles.entity.armadillo.scute_drop": "soweli pi kiwen selo li pana e kiwen selo", "subtitles.entity.armadillo.unroll_finish": "soweli pi kiwen selo li kama sike ala", "subtitles.entity.armadillo.unroll_start": "soweli pi kiwen selo li lukin lili", "subtitles.entity.armor_stand.fall": "ijo li kama anpa", "subtitles.entity.arrow.hit": "palisa alasa li utala", "subtitles.entity.arrow.hit_player": "palisa alasa li utala e jan", "subtitles.entity.arrow.shoot": "palisa alasa li kama pana", "subtitles.entity.axolotl.attack": "akesi suwi li utala", "subtitles.entity.axolotl.death": "akesi suwi li moli", "subtitles.entity.axolotl.hurt": "akesi suwi li pakala", "subtitles.entity.axolotl.idle_air": "akesi suwi li mu", "subtitles.entity.axolotl.idle_water": "akesi suwi li mu", "subtitles.entity.axolotl.splash": "akesi suwi li tawa e telo", "subtitles.entity.axolotl.swim": "akesi suwi li tawa telo", "subtitles.entity.bat.ambient": "waso pimeja li mu", "subtitles.entity.bat.death": "waso pimeja li moli", "subtitles.entity.bat.hurt": "waso pimeja li pakala", "subtitles.entity.bat.takeoff": "waso pimeja li tawa sewi", "subtitles.entity.bee.ambient": "pipi suwi li mu", "subtitles.entity.bee.death": "pipi suwi li moli", "subtitles.entity.bee.hurt": "pipi suwi li pakala", "subtitles.entity.bee.loop": "pipi suwi li mu", "subtitles.entity.bee.loop_aggressive": "pipi suwi li mu ike", "subtitles.entity.bee.pollinate": "pipi suwi li mu pona", "subtitles.entity.bee.sting": "pipi suwi li utala", "subtitles.entity.blaze.ambient": "monsuta seli li mu kon", "subtitles.entity.blaze.burn": "monsuta seli li mu", "subtitles.entity.blaze.death": "monsuta seli li moli", "subtitles.entity.blaze.hurt": "monsuta seli li pakala", "subtitles.entity.blaze.shoot": "monsuta seli li pana", "subtitles.entity.boat.paddle_land": "ilo tawa telo li tawa lon ma", "subtitles.entity.boat.paddle_water": "ilo tawa telo li tawa", "subtitles.entity.bogged.ambient": "jan moli jaki li kalama", "subtitles.entity.bogged.death": "jan moli jaki li moli", "subtitles.entity.bogged.hurt": "jan moli jaki li pakala", "subtitles.entity.breeze.charge": "monsuta kon li wile sewi", "subtitles.entity.breeze.death": "monsuta kon li moli", "subtitles.entity.breeze.deflect": "monsuta kon li tawa e ijo", "subtitles.entity.breeze.hurt": "monsuta kon li pakala", "subtitles.entity.breeze.idle_air": "monsuta kon li tawa kon", "subtitles.entity.breeze.idle_ground": "monsuta kon li kalama", "subtitles.entity.breeze.inhale": "monsuta kon li insa e kon", "subtitles.entity.breeze.jump": "monsuta kon li tawa sewi", "subtitles.entity.breeze.land": "monsuta kon li anpa", "subtitles.entity.breeze.shoot": "monsuta kon li pana", "subtitles.entity.breeze.slide": "monsuta kon li tawa", "subtitles.entity.breeze.whirl": "monsuta kon li tawa sike", "subtitles.entity.breeze.wind_burst": "sike kon li pakala", "subtitles.entity.camel.ambient": "soweli pi nena monsi li mu", "subtitles.entity.camel.dash": "soweli pi nena monsi li tawa wawa", "subtitles.entity.camel.dash_ready": "soweli pi nena monsi li kama wawa", "subtitles.entity.camel.death": "soweli pi nena monsi li moli", "subtitles.entity.camel.eat": "soweli pi nena monsi li moku", "subtitles.entity.camel.hurt": "soweli pi nena monsi li pakala", "subtitles.entity.camel.saddle": "jan li len e soweli pi nena monsi", "subtitles.entity.camel.sit": "soweli pi nena monsi li lape", "subtitles.entity.camel.stand": "soweli pi nena monsi li pini lape", "subtitles.entity.camel.step": "soweli pi nena monsi li tawa", "subtitles.entity.camel.step_sand": "soweli pi nena monsi li tawa ko", "subtitles.entity.cat.ambient": "soweli suwi li mu", "subtitles.entity.cat.beg_for_food": "soweli suwi li mu tan wile moku", "subtitles.entity.cat.death": "soweli suwi li moli", "subtitles.entity.cat.eat": "soweli suwi li moku", "subtitles.entity.cat.hiss": "soweli suwi li mu ike", "subtitles.entity.cat.hurt": "soweli suwi li pakala", "subtitles.entity.cat.purr": "soweli suwi li mu olin", "subtitles.entity.chicken.ambient": "waso walo li mu", "subtitles.entity.chicken.death": "waso walo li moli", "subtitles.entity.chicken.egg": "waso walo li pana e sike mama", "subtitles.entity.chicken.hurt": "waso walo li pakala", "subtitles.entity.cod.death": "kala walo li moli", "subtitles.entity.cod.flop": "kala walo li tawa lon ma", "subtitles.entity.cod.hurt": "kala walo li pakala", "subtitles.entity.cow.ambient": "mani li mu", "subtitles.entity.cow.death": "mani li moli", "subtitles.entity.cow.hurt": "mani li pakala", "subtitles.entity.cow.milk": "mani li pana e telo mama", "subtitles.entity.creaking.activate": "monsuta kasi li lukin e jan", "subtitles.entity.creaking.ambient": "monsuta kasi li mu", "subtitles.entity.creaking.attack": "monsuta kasi li utala e jan", "subtitles.entity.creaking.deactivate": "monsuta kasi li lukin ala e jan", "subtitles.entity.creaking.death": "monsuta kasi li moli", "subtitles.entity.creaking.freeze": "monsuta kasi li awen pi tawa ala", "subtitles.entity.creaking.spawn": "monsuta kasi li kama lon", "subtitles.entity.creaking.sway": "monsuta kasi li pakala ala", "subtitles.entity.creaking.twitch": "monsuta kasi li tawa lili", "subtitles.entity.creaking.unfreeze": "monsuta kasi li tawa", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON>reeper li moli", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> li pakala", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> li mu", "subtitles.entity.dolphin.ambient": "kala suli li mu", "subtitles.entity.dolphin.ambient_water": "kala suli li mu musi", "subtitles.entity.dolphin.attack": "kala suli li utala", "subtitles.entity.dolphin.death": "kala suli li moli", "subtitles.entity.dolphin.eat": "kala suli li moku", "subtitles.entity.dolphin.hurt": "kala suli li pakala", "subtitles.entity.dolphin.jump": "kala suli li tawa sewi tan telo", "subtitles.entity.dolphin.play": "kala suli li musi", "subtitles.entity.dolphin.splash": "kala suli li tawa e telo", "subtitles.entity.dolphin.swim": "kala suli li tawa", "subtitles.entity.donkey.ambient": "soweli jo li mu", "subtitles.entity.donkey.angry": "soweli jo li mu ike", "subtitles.entity.donkey.chest": "soweli jo li kama jo e poki", "subtitles.entity.donkey.death": "soweli jo li moli", "subtitles.entity.donkey.eat": "soweli jo li moku", "subtitles.entity.donkey.hurt": "soweli jo li pakala", "subtitles.entity.donkey.jump": "soweli jo li tawa sewi", "subtitles.entity.drowned.ambient": "jan moli telo li mu", "subtitles.entity.drowned.ambient_water": "jan moli telo li mu", "subtitles.entity.drowned.death": "jan moli telo li moli", "subtitles.entity.drowned.hurt": "jan moli telo li pakala", "subtitles.entity.drowned.shoot": "jan moli telo li pana e ilo", "subtitles.entity.drowned.step": "jan moli telo li tawa noka", "subtitles.entity.drowned.swim": "jan moli telo li tawa telo", "subtitles.entity.egg.throw": "sike mama li tawa kon", "subtitles.entity.elder_guardian.ambient": "kala oko suli li mu", "subtitles.entity.elder_guardian.ambient_land": "kala oko suli li mu pi telo ala", "subtitles.entity.elder_guardian.curse": "kala oko suli li pana e wawa ike", "subtitles.entity.elder_guardian.death": "kala oko suli li moli", "subtitles.entity.elder_guardian.flop": "kala oko suli li tawa lon ma", "subtitles.entity.elder_guardian.hurt": "kala oko suli li pakala", "subtitles.entity.ender_dragon.ambient": "akesi pi ma End li mu", "subtitles.entity.ender_dragon.death": "akesi pi ma End li moli", "subtitles.entity.ender_dragon.flap": "akesi pi ma End li tawa kon", "subtitles.entity.ender_dragon.growl": "akesi pi ma End li mu wawa", "subtitles.entity.ender_dragon.hurt": "akesi pi ma <PERSON> li pakala", "subtitles.entity.ender_dragon.shoot": "akesi pi ma End li pana", "subtitles.entity.ender_eye.death": "oko pi ma End li tawa anpa", "subtitles.entity.ender_eye.launch": "oko pi ma End li tawa lon kon", "subtitles.entity.ender_pearl.throw": "sike pi ma End li tawa lon kon", "subtitles.entity.enderman.ambient": "jan pi ma End li mu", "subtitles.entity.enderman.death": "jan pi ma <PERSON> li moli", "subtitles.entity.enderman.hurt": "jan pi ma <PERSON> li pakala", "subtitles.entity.enderman.scream": "jan pi ma <PERSON> li kalama wawa", "subtitles.entity.enderman.stare": "jan pi ma End li toki ike", "subtitles.entity.enderman.teleport": "jan pi ma End li tawa nasa", "subtitles.entity.endermite.ambient": "pipi pi ma End li tawa", "subtitles.entity.endermite.death": "pipi pi ma End li moli", "subtitles.entity.endermite.hurt": "pipi pi ma End li pakala", "subtitles.entity.evoker.ambient": "jan wawa li toki", "subtitles.entity.evoker.cast_spell": "jan ike wawa li pana e wawa", "subtitles.entity.evoker.celebrate": "jan ike wawa li toki e pona", "subtitles.entity.evoker.death": "jan ike wawa li moli", "subtitles.entity.evoker.hurt": "jan ike wawa li pakala", "subtitles.entity.evoker.prepare_attack": "jan ike wawa li pali e utala", "subtitles.entity.evoker.prepare_summon": "jan ike wawa li pali e jan kon", "subtitles.entity.evoker.prepare_wololo": "jan ike wawa li pali e wawa", "subtitles.entity.evoker_fangs.attack": "palisa uta li kalama", "subtitles.entity.experience_orb.pickup": "jan li kama jo e wawa laso", "subtitles.entity.firework_rocket.blast": "palisa pi seli kule li pakala seli", "subtitles.entity.firework_rocket.launch": "palisa pi seli kule li tawa", "subtitles.entity.firework_rocket.twinkle": "palisa pi seli kule li suno", "subtitles.entity.fish.swim": "telo li mu tan kala", "subtitles.entity.fishing_bobber.retrieve": "ilo alasa kala li kama", "subtitles.entity.fishing_bobber.splash": "ilo alasa kala li tawa e telo", "subtitles.entity.fishing_bobber.throw": "jan li kepeken ilo alasa kala", "subtitles.entity.fox.aggro": "soweli pi linja monsi li pilin ike", "subtitles.entity.fox.ambient": "soweli pi linja monsi li mu lili", "subtitles.entity.fox.bite": "soweli pi linja monsi li uta", "subtitles.entity.fox.death": "soweli pi linja monsi li moli", "subtitles.entity.fox.eat": "soweli pi linja monsi li moku", "subtitles.entity.fox.hurt": "soweli pi linja monsi li pakala", "subtitles.entity.fox.screech": "soweli pi linja monsi li mu wawa", "subtitles.entity.fox.sleep": "soweli pi linja monsi li mu lape", "subtitles.entity.fox.sniff": "soweli pi linja monsi li pilin e kon", "subtitles.entity.fox.spit": "soweli pi linja monsi li pana e telo uta", "subtitles.entity.fox.teleport": "soweli pi linja monsi li tawa nasa", "subtitles.entity.frog.ambient": "akesi sewi li mu", "subtitles.entity.frog.death": "akesi sewi li moli", "subtitles.entity.frog.eat": "akesi sewi li moku", "subtitles.entity.frog.hurt": "akesi sewi li pakala", "subtitles.entity.frog.lay_spawn": "akesi sewi li pana e sike mama", "subtitles.entity.frog.long_jump": "akesi sewi li tawa sewi", "subtitles.entity.generic.big_fall": "ijo li kama anpa", "subtitles.entity.generic.burn": "seli", "subtitles.entity.generic.death": "moli", "subtitles.entity.generic.drink": "moku telo", "subtitles.entity.generic.eat": "moku", "subtitles.entity.generic.explode": "seli pakala", "subtitles.entity.generic.extinguish_fire": "seli li weka", "subtitles.entity.generic.hurt": "ijo li pakala", "subtitles.entity.generic.small_fall": "ijo li tawa lili anpa", "subtitles.entity.generic.splash": "kalama telo", "subtitles.entity.generic.swim": "kalama pi tawa telo", "subtitles.entity.generic.wind_burst": "sike kon li pakala", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> li mu", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> li moli", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> li pakala", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> li pana", "subtitles.entity.ghastling.ambient": "waso lili <PERSON> li mu", "subtitles.entity.ghastling.death": "waso lili <PERSON> li moli", "subtitles.entity.ghastling.hurt": "waso lili <PERSON> li pakala", "subtitles.entity.ghastling.spawn": "waso lili <PERSON> li kama lon", "subtitles.entity.glow_item_frame.add_item": "ijo li tawa lipu poki suno", "subtitles.entity.glow_item_frame.break": "lipu poki suno li pakala", "subtitles.entity.glow_item_frame.place": "jan li pana e lipu poki suno", "subtitles.entity.glow_item_frame.remove_item": "ijo li weka tan lipu poki suno", "subtitles.entity.glow_item_frame.rotate_item": "ijo li tawa lon lipu poki suno", "subtitles.entity.glow_squid.ambient": "kala luka suno li tawa", "subtitles.entity.glow_squid.death": "kala luka suno li moli", "subtitles.entity.glow_squid.hurt": "kala luka suno li pakala", "subtitles.entity.glow_squid.squirt": "kala luka suno li pana e pimeja", "subtitles.entity.goat.ambient": "soweli nena li mu", "subtitles.entity.goat.death": "soweli nena li moli", "subtitles.entity.goat.eat": "soweli nena li moku", "subtitles.entity.goat.horn_break": "palisa lawa li pakala", "subtitles.entity.goat.hurt": "soweli nena li pakala", "subtitles.entity.goat.long_jump": "soweli nena li tawa sewi", "subtitles.entity.goat.milk": "soweli nena li pana e telo mama", "subtitles.entity.goat.prepare_ram": "soweli nena li kalama noka", "subtitles.entity.goat.ram_impact": "soweli nena li utala wawa", "subtitles.entity.goat.screaming.ambient": "soweli nena li mu wawa", "subtitles.entity.goat.step": "soweli nena li tawa", "subtitles.entity.guardian.ambient": "kala oko li mu", "subtitles.entity.guardian.ambient_land": "kala oko li mu pi telo ala", "subtitles.entity.guardian.attack": "kala oko li pana", "subtitles.entity.guardian.death": "kala oko li moli", "subtitles.entity.guardian.flop": "kala oko li tawa lon ma", "subtitles.entity.guardian.hurt": "kala oko li pakala", "subtitles.entity.happy_ghast.ambient": "waso pona Ghast li mu", "subtitles.entity.happy_ghast.death": "waso pona Ghast li moli", "subtitles.entity.happy_ghast.equip": "len sewi waso li kama lon waso pona", "subtitles.entity.happy_ghast.harness_goggles_down": "waso pona Ghast li ken tawa", "subtitles.entity.happy_ghast.harness_goggles_up": "waso pona Ghast li pini tawa", "subtitles.entity.happy_ghast.hurt": "waso pona Ghast li pakala", "subtitles.entity.happy_ghast.unequip": "len sewi waso li kama lon ala waso pona", "subtitles.entity.hoglin.ambient": "soweli <PERSON>n li mu", "subtitles.entity.hoglin.angry": "sowe<PERSON>n li mu ike", "subtitles.entity.hoglin.attack": "soweli <PERSON>n li utala", "subtitles.entity.hoglin.converted_to_zombified": "soweli Hoglin li kama soweli moli Zoglin", "subtitles.entity.hoglin.death": "soweli <PERSON>n li moli", "subtitles.entity.hoglin.hurt": "soweli <PERSON>n li pakala", "subtitles.entity.hoglin.retreat": "soweli <PERSON>glin li tawa monsi", "subtitles.entity.hoglin.step": "soweli <PERSON>n li tawa", "subtitles.entity.horse.ambient": "soweli tawa li mu", "subtitles.entity.horse.angry": "soweli tawa li mu", "subtitles.entity.horse.armor": "len soweli li kama lon soweli", "subtitles.entity.horse.breathe": "soweli tawa li mu kon", "subtitles.entity.horse.death": "soweli tawa li moli", "subtitles.entity.horse.eat": "soweli tawa li moku", "subtitles.entity.horse.gallop": "soweli tawa li tawa", "subtitles.entity.horse.hurt": "soweli tawa li pakala", "subtitles.entity.horse.jump": "soweli tawa li tawa sewi", "subtitles.entity.horse.saddle": "jan li len e soweli tawa", "subtitles.entity.husk.ambient": "jan moli seli li mu", "subtitles.entity.husk.converted_to_zombie": "jan moli seli li kama jan moli", "subtitles.entity.husk.death": "jan moli seli li moli", "subtitles.entity.husk.hurt": "jan moli seli li pakala", "subtitles.entity.illusioner.ambient": "jan ike sitelen li toki", "subtitles.entity.illusioner.cast_spell": "jan ike sitelen li pana e wawa", "subtitles.entity.illusioner.death": "jan ike sitelen li moli", "subtitles.entity.illusioner.hurt": "jan ike <PERSON>len li pakala", "subtitles.entity.illusioner.mirror_move": "jan ike sitelen li tawa nasa", "subtitles.entity.illusioner.prepare_blindness": "jan ike sitelen li pali e wawa ike", "subtitles.entity.illusioner.prepare_mirror": "jan ike sitelen li pali e sitelen", "subtitles.entity.iron_golem.attack": "jan ilo pi kiwen walo li utala", "subtitles.entity.iron_golem.damage": "jan ilo pi kiwen walo li pakala", "subtitles.entity.iron_golem.death": "jan ilo pi kiwen walo li moli", "subtitles.entity.iron_golem.hurt": "jan ilo pi kiwen walo li pakala", "subtitles.entity.iron_golem.repair": "jan ilo pi kiwen walo li pona sin", "subtitles.entity.item.break": "ijo li pakala", "subtitles.entity.item.pickup": "jan li kama jo e ijo", "subtitles.entity.item_frame.add_item": "jan li pana e ijo lon lipu poki", "subtitles.entity.item_frame.break": "lipu poki li pakala", "subtitles.entity.item_frame.place": "jan li pana e lipu poki", "subtitles.entity.item_frame.remove_item": "lipu poki li pini jo e ijo", "subtitles.entity.item_frame.rotate_item": "ijo li sike lon lipu poki", "subtitles.entity.leash_knot.break": "linja sinpin li pakala", "subtitles.entity.leash_knot.place": "linja sinpin li awen", "subtitles.entity.lightning_bolt.impact": "linja pi wawa suno li kama!", "subtitles.entity.lightning_bolt.thunder": "sewi li kalama wawa", "subtitles.entity.llama.ambient": "soweli pi telo uta li mu", "subtitles.entity.llama.angry": "soweli pi telo uta li mu ike", "subtitles.entity.llama.chest": "soweli pi telo uta li kama jo e poki", "subtitles.entity.llama.death": "soweli pi telo uta li moli", "subtitles.entity.llama.eat": "soweli pi telo uta li moku", "subtitles.entity.llama.hurt": "soweli pi telo uta li pakala", "subtitles.entity.llama.spit": "soweli pi telo uta li pana e telo uta", "subtitles.entity.llama.step": "soweli pi telo uta li tawa", "subtitles.entity.llama.swag": "jan li namako e soweli pi telo uta", "subtitles.entity.magma_cube.death": "monsuta ko seli li moli", "subtitles.entity.magma_cube.hurt": "monsuta ko seli li pakala", "subtitles.entity.magma_cube.squish": "monsuta ko seli li kalama ko", "subtitles.entity.minecart.inside": "poki tawa li mu", "subtitles.entity.minecart.inside_underwater": "poki tawa li mu lon telo", "subtitles.entity.minecart.riding": "poki tawa li tawa", "subtitles.entity.mooshroom.convert": "mani <PERSON> li kama ante", "subtitles.entity.mooshroom.eat": "mani <PERSON>room li moku", "subtitles.entity.mooshroom.milk": "mani <PERSON>room li pana e telo mama", "subtitles.entity.mooshroom.suspicious_milk": "mani <PERSON>room li pana nasa e telo", "subtitles.entity.mule.ambient": "soweli jo tawa li mu", "subtitles.entity.mule.angry": "soweli jo tawa li mu ike", "subtitles.entity.mule.chest": "soweli jo tawa li kama jo e poki", "subtitles.entity.mule.death": "soweli jo tawa li moli", "subtitles.entity.mule.eat": "soweli jo tawa li moku", "subtitles.entity.mule.hurt": "soweli jo tawa li pakala", "subtitles.entity.mule.jump": "soweli jo tawa li tawa sewi", "subtitles.entity.painting.break": "sitelen li pakala", "subtitles.entity.painting.place": "jan li pana e sitelen", "subtitles.entity.panda.aggressive_ambient": "soweli pimeja walo li mu kon ike", "subtitles.entity.panda.ambient": "soweli pimeja walo li kon", "subtitles.entity.panda.bite": "soweli pimeja walo li uta", "subtitles.entity.panda.cant_breed": "soweli pimeja walo li mu", "subtitles.entity.panda.death": "soweli pimeja walo li moli", "subtitles.entity.panda.eat": "soweli pimeja walo li moku", "subtitles.entity.panda.hurt": "soweli pimeja walo li pakala", "subtitles.entity.panda.pre_sneeze": "soweli pimeja walo li pilin nasa lon nena", "subtitles.entity.panda.sneeze": "soweli pimeja walo li kon wawa", "subtitles.entity.panda.step": "soweli pimeja walo li tawa", "subtitles.entity.panda.worried_ambient": "soweli pimeja walo li mu lili", "subtitles.entity.parrot.ambient": "waso toki li toki", "subtitles.entity.parrot.death": "waso toki li moli", "subtitles.entity.parrot.eats": "waso toki li moku", "subtitles.entity.parrot.fly": "waso toki li tawa kon", "subtitles.entity.parrot.hurts": "waso toki li pakala", "subtitles.entity.parrot.imitate.blaze": "waso toki li mu sama monsuta seli", "subtitles.entity.parrot.imitate.bogged": "waso toki li mu sama jan moli jaki", "subtitles.entity.parrot.imitate.breeze": "waso toki li mu sama monsuta kon", "subtitles.entity.parrot.imitate.creaking": "waso toki li mu sama monsuta kasi", "subtitles.entity.parrot.imitate.creeper": "waso toki li mu sama monsuta Creeper", "subtitles.entity.parrot.imitate.drowned": "waso toki li mu sama jan moli telo", "subtitles.entity.parrot.imitate.elder_guardian": "waso toki li mu sama kala oko", "subtitles.entity.parrot.imitate.ender_dragon": "waso toki li mu sama akesi pi ma End", "subtitles.entity.parrot.imitate.endermite": "waso toki li mu sama pipi pi ma End", "subtitles.entity.parrot.imitate.evoker": "waso toki li toki sama jan ike wawa", "subtitles.entity.parrot.imitate.ghast": "waso toki li mu sama monsu<PERSON>", "subtitles.entity.parrot.imitate.guardian": "waso toki li mu sama kala oko", "subtitles.entity.parrot.imitate.hoglin": "waso toki li mu sama soweli <PERSON>n", "subtitles.entity.parrot.imitate.husk": "waso toki li mu sama jan moli seli", "subtitles.entity.parrot.imitate.illusioner": "waso toki li mu sama jan ike sitelen", "subtitles.entity.parrot.imitate.magma_cube": "waso toki li mu sama monsuta ko seli", "subtitles.entity.parrot.imitate.phantom": "waso toki li mu sama waso mun", "subtitles.entity.parrot.imitate.piglin": "waso toki li mu sama jan <PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "waso toki li mu sama jan <PERSON> wawa", "subtitles.entity.parrot.imitate.pillager": "waso kule li toki sama jan ike", "subtitles.entity.parrot.imitate.ravager": "waso toki li mu sama monsuta suli", "subtitles.entity.parrot.imitate.shulker": "waso toki li mu sama monsu<PERSON>", "subtitles.entity.parrot.imitate.silverfish": "waso toki li mu sama pipi kiwen", "subtitles.entity.parrot.imitate.skeleton": "waso toki li mu sama jan moli palisa", "subtitles.entity.parrot.imitate.slime": "waso toki li mu sama monsuta ko", "subtitles.entity.parrot.imitate.spider": "waso toki li mu sama pipi noka", "subtitles.entity.parrot.imitate.stray": "waso toki li mu sama jan moli lete", "subtitles.entity.parrot.imitate.vex": "waso toki li mu sama kon ike", "subtitles.entity.parrot.imitate.vindicator": "waso toki li toki sama jan ike kipisi", "subtitles.entity.parrot.imitate.warden": "waso toki li mu sama monsuta kute", "subtitles.entity.parrot.imitate.witch": "waso toki li toki sama jan pi telo wawa", "subtitles.entity.parrot.imitate.wither": "waso toki li mu sama monsuta <PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "waso toki li mu sama jan moli palisa Wither", "subtitles.entity.parrot.imitate.zoglin": "waso toki li mu sama soweli moli Zoglin", "subtitles.entity.parrot.imitate.zombie": "waso toki li mu sama jan moli", "subtitles.entity.parrot.imitate.zombie_villager": "waso toki li mu sama jan tomo moli", "subtitles.entity.phantom.ambient": "waso mun li mu", "subtitles.entity.phantom.bite": "waso mun li uta", "subtitles.entity.phantom.death": "waso mun li moli", "subtitles.entity.phantom.flap": "waso mun li tawa kon", "subtitles.entity.phantom.hurt": "waso mun li pakala", "subtitles.entity.phantom.swoop": "waso mun li utala tan sewi", "subtitles.entity.pig.ambient": "soweli pi loje walo li mu", "subtitles.entity.pig.death": "soweli pi loje walo li moli", "subtitles.entity.pig.hurt": "soweli pi loje walo li pakala", "subtitles.entity.pig.saddle": "jan li len e soweli moku", "subtitles.entity.piglin.admiring_item": "jan <PERSON> li lukin pona e ijo", "subtitles.entity.piglin.ambient": "jan <PERSON> li mu", "subtitles.entity.piglin.angry": "jan <PERSON> li mu ike", "subtitles.entity.piglin.celebrate": "jan <PERSON> li mu tan pilin pona", "subtitles.entity.piglin.converted_to_zombified": "jan <PERSON> li moli nasa", "subtitles.entity.piglin.death": "jan <PERSON> li moli", "subtitles.entity.piglin.hurt": "jan <PERSON> li pakala", "subtitles.entity.piglin.jealous": "jan <PERSON> li mu tan wile mani", "subtitles.entity.piglin.retreat": "jan <PERSON> li tawa monsi", "subtitles.entity.piglin.step": "jan <PERSON> li tawa", "subtitles.entity.piglin_brute.ambient": "jan <PERSON><PERSON> wawa li mu", "subtitles.entity.piglin_brute.angry": "jan <PERSON> wawa li mu ike", "subtitles.entity.piglin_brute.converted_to_zombified": "jan <PERSON> wawa li moli nasa", "subtitles.entity.piglin_brute.death": "jan <PERSON><PERSON> wawa li moli", "subtitles.entity.piglin_brute.hurt": "jan <PERSON><PERSON> wawa li pakala", "subtitles.entity.piglin_brute.step": "jan <PERSON><PERSON> wawa li tawa", "subtitles.entity.pillager.ambient": "jan ike alasa li toki", "subtitles.entity.pillager.celebrate": "jan ike alasa li toki e pona", "subtitles.entity.pillager.death": "jan ike alasa li moli", "subtitles.entity.pillager.hurt": "jan ike alasa li pakala", "subtitles.entity.player.attack.crit": "utala wawa", "subtitles.entity.player.attack.knockback": "utala tawa", "subtitles.entity.player.attack.strong": "utala wawa", "subtitles.entity.player.attack.sweep": "utala suli", "subtitles.entity.player.attack.weak": "utala pi wawa lili", "subtitles.entity.player.burp": "kalama moku", "subtitles.entity.player.death": "jan li moli", "subtitles.entity.player.freeze_hurt": "jan li lete", "subtitles.entity.player.hurt": "jan li pakala", "subtitles.entity.player.hurt_drown": "jan li pakala tan weka kon", "subtitles.entity.player.hurt_on_fire": "jan li seli", "subtitles.entity.player.levelup": "jan li kama wawa", "subtitles.entity.player.teleport": "jan li tawa nasa", "subtitles.entity.polar_bear.ambient": "soweli lete li mu", "subtitles.entity.polar_bear.ambient_baby": "soweli lete lili li mu", "subtitles.entity.polar_bear.death": "soweli lete li moli", "subtitles.entity.polar_bear.hurt": "soweli lete li pakala", "subtitles.entity.polar_bear.warning": "soweli lete li mu ike", "subtitles.entity.potion.splash": "poki lili li pakala", "subtitles.entity.potion.throw": "poki lili li tawa", "subtitles.entity.puffer_fish.blow_out": "kala pi kama suli li kama lili", "subtitles.entity.puffer_fish.blow_up": "kala pi kama suli li kama suli", "subtitles.entity.puffer_fish.death": "kala pi kama suli li moli", "subtitles.entity.puffer_fish.flop": "kala pi kama suli li wile telo", "subtitles.entity.puffer_fish.hurt": "kala pi kama suli li pakala", "subtitles.entity.puffer_fish.sting": "kala pi kama suli li utala", "subtitles.entity.rabbit.ambient": "soweli lili li mu", "subtitles.entity.rabbit.attack": "soweli lili li utala", "subtitles.entity.rabbit.death": "soweli lili li moli", "subtitles.entity.rabbit.hurt": "soweli lili li pakala", "subtitles.entity.rabbit.jump": "soweli lili li tawa", "subtitles.entity.ravager.ambient": "monsuta suli li mu lili", "subtitles.entity.ravager.attack": "monsuta suli li uta", "subtitles.entity.ravager.celebrate": "monsuta suli li mu pona", "subtitles.entity.ravager.death": "monsuta suli li moli", "subtitles.entity.ravager.hurt": "monsuta suli li pakala", "subtitles.entity.ravager.roar": "monsuta suli li mu wawa", "subtitles.entity.ravager.step": "monsuta suli li tawa", "subtitles.entity.ravager.stunned": "monsuta suli li pilin nasa", "subtitles.entity.salmon.death": "kala loje li moli", "subtitles.entity.salmon.flop": "kala loje li tawa lon ma", "subtitles.entity.salmon.hurt": "kala loje li pakala", "subtitles.entity.sheep.ambient": "soweli len li mu", "subtitles.entity.sheep.death": "soweli len li moli", "subtitles.entity.sheep.hurt": "soweli len li pakala", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> li mu", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> li pini", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> li moli", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> li pakala", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> li open", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> li pana", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> li tawa nasa", "subtitles.entity.shulker_bullet.hit": "pana pi monsuta <PERSON> li pakala", "subtitles.entity.shulker_bullet.hurt": "pana pi monsuta <PERSON> li pakala", "subtitles.entity.silverfish.ambient": "pipi kiwen li mu", "subtitles.entity.silverfish.death": "pipi kiwen li moli", "subtitles.entity.silverfish.hurt": "pipi kiwen li pakala", "subtitles.entity.skeleton.ambient": "jan moli palisa li kalama", "subtitles.entity.skeleton.converted_to_stray": "jan moli palisa li kama lete", "subtitles.entity.skeleton.death": "jan moli palisa li moli", "subtitles.entity.skeleton.hurt": "jan moli palisa li pakala", "subtitles.entity.skeleton.shoot": "jan moli palisa li pana", "subtitles.entity.skeleton_horse.ambient": "soweli tawa palisa li mu", "subtitles.entity.skeleton_horse.death": "soweli tawa palisa li moli", "subtitles.entity.skeleton_horse.hurt": "soweli tawa palisa li pakala", "subtitles.entity.skeleton_horse.jump_water": "soweli tawa palisa li tawa sewi", "subtitles.entity.skeleton_horse.swim": "soweli tawa palisa li tawa lon telo", "subtitles.entity.slime.attack": "monsuta ko li utala", "subtitles.entity.slime.death": "monsuta ko li moli", "subtitles.entity.slime.hurt": "monsuta ko li pakala", "subtitles.entity.slime.squish": "monsuta ko li kalama ko", "subtitles.entity.sniffer.death": "soweli suli alasa li moli", "subtitles.entity.sniffer.digging": "soweli suli alasa li tawa e ma", "subtitles.entity.sniffer.digging_stop": "soweli suli alasa li kama sewi", "subtitles.entity.sniffer.drop_seed": "soweli suli alasa li pana e sike mama kasi", "subtitles.entity.sniffer.eat": "soweli suli alasa li moku", "subtitles.entity.sniffer.egg_crack": "kiwen mama pi soweli suli alasa li pakala lili", "subtitles.entity.sniffer.egg_hatch": "kiwen mama pi soweli suli alasa li open", "subtitles.entity.sniffer.happy": "soweli suli alasa li kalama musi", "subtitles.entity.sniffer.hurt": "soweli suli alasa li pakala", "subtitles.entity.sniffer.idle": "soweli suli alasa li mu", "subtitles.entity.sniffer.scenting": "soweli suli alasa li pilin e kon", "subtitles.entity.sniffer.searching": "soweli suli li alasa", "subtitles.entity.sniffer.sniffing": "soweli suli alasa li pilin e kon", "subtitles.entity.sniffer.step": "soweli suli alasa li tawa", "subtitles.entity.snow_golem.death": "jan pi ko lete li moli", "subtitles.entity.snow_golem.hurt": "jan pi ko lete li pakala", "subtitles.entity.snowball.throw": "sike pi ko lete li tawa lon kon", "subtitles.entity.spider.ambient": "pipi noka li mu", "subtitles.entity.spider.death": "pipi noka li moli", "subtitles.entity.spider.hurt": "pipi noka li pakala", "subtitles.entity.squid.ambient": "kala luka li tawa", "subtitles.entity.squid.death": "kala luka li moli", "subtitles.entity.squid.hurt": "kala luka li pakala", "subtitles.entity.squid.squirt": "kala luka li pana e pimeja", "subtitles.entity.stray.ambient": "jan moli lete li kalama", "subtitles.entity.stray.death": "jan palisa lete li moli", "subtitles.entity.stray.hurt": "jan palisa lete li pakala", "subtitles.entity.strider.death": "akesi seli li moli", "subtitles.entity.strider.eat": "akesi seli li moku", "subtitles.entity.strider.happy": "akesi seli li mu pona", "subtitles.entity.strider.hurt": "akesi seli li pakala", "subtitles.entity.strider.idle": "akesi seli li mu", "subtitles.entity.strider.retreat": "akesi seli li tawa monsi", "subtitles.entity.tadpole.death": "akesi sewi lili li moli", "subtitles.entity.tadpole.flop": "akesi sewi lili li tawa lon ma", "subtitles.entity.tadpole.grow_up": "akesi sewi lili li kama suli", "subtitles.entity.tadpole.hurt": "akesi sewi lili li pakala", "subtitles.entity.tnt.primed": "ilo pi seli pakala li kalama lili", "subtitles.entity.tropical_fish.death": "kala kule li moli", "subtitles.entity.tropical_fish.flop": "kala kule li tawa lon ma", "subtitles.entity.tropical_fish.hurt": "kala kule li pakala", "subtitles.entity.turtle.ambient_land": "akesi tomo li mu", "subtitles.entity.turtle.death": "akesi tomo li moli", "subtitles.entity.turtle.death_baby": "akesi tomo lili li moli", "subtitles.entity.turtle.egg_break": "sike mama akesi li pakala", "subtitles.entity.turtle.egg_crack": "sike mama akesi li pakala lili", "subtitles.entity.turtle.egg_hatch": "sike mama akesi li open", "subtitles.entity.turtle.hurt": "akesi tomo li pakala", "subtitles.entity.turtle.hurt_baby": "akesi tomo lili li pakala", "subtitles.entity.turtle.lay_egg": "akesi kiwen li pana e sike mama", "subtitles.entity.turtle.shamble": "akesi tomo li tawa", "subtitles.entity.turtle.shamble_baby": "akesi tomo lili li tawa", "subtitles.entity.turtle.swim": "akesi tomo li tawa telo", "subtitles.entity.vex.ambient": "kon ike li mu", "subtitles.entity.vex.charge": "kon ike li mu utala", "subtitles.entity.vex.death": "kon ike li moli", "subtitles.entity.vex.hurt": "kon ike li pakala", "subtitles.entity.villager.ambient": "jan tomo li toki", "subtitles.entity.villager.celebrate": "jan tomo li toki e pona", "subtitles.entity.villager.death": "jan tomo li moli", "subtitles.entity.villager.hurt": "jan tomo li pakala", "subtitles.entity.villager.no": "jan tomo li pilin ante", "subtitles.entity.villager.trade": "jan tomo li esun", "subtitles.entity.villager.work_armorer": "jan tomo pi len utala li pali", "subtitles.entity.villager.work_butcher": "jan tomo moku li pali", "subtitles.entity.villager.work_cartographer": "jan tomo pi sitelen ma li pali", "subtitles.entity.villager.work_cleric": "jan tomo sewi li pali", "subtitles.entity.villager.work_farmer": "jan tomo kasi li pali", "subtitles.entity.villager.work_fisherman": "jan tomo kala li pali", "subtitles.entity.villager.work_fletcher": "jan tomo pi palisa alasa li pali", "subtitles.entity.villager.work_leatherworker": "jan tomo pi selo soweli li pali", "subtitles.entity.villager.work_librarian": "jan tomo sona li pali", "subtitles.entity.villager.work_mason": "jan tomo kiwen li pali", "subtitles.entity.villager.work_shepherd": "jan tomo len li pali", "subtitles.entity.villager.work_toolsmith": "jan tomo ilo li pali", "subtitles.entity.villager.work_weaponsmith": "jan tomo pi ilo utala li pali", "subtitles.entity.villager.yes": "jan tomo li pilin sama", "subtitles.entity.vindicator.ambient": "jan ike kipisi li toki", "subtitles.entity.vindicator.celebrate": "jan ike kipisi li toki e pona", "subtitles.entity.vindicator.death": "jan ike kipisi li moli", "subtitles.entity.vindicator.hurt": "jan ike kipisi li pakala", "subtitles.entity.wandering_trader.ambient": "jan esun li toki", "subtitles.entity.wandering_trader.death": "jan esun li moli", "subtitles.entity.wandering_trader.disappeared": "jan esun li weka", "subtitles.entity.wandering_trader.drink_milk": "jan esun li moku e telo walo", "subtitles.entity.wandering_trader.drink_potion": "jan esun li moku e telo wawa", "subtitles.entity.wandering_trader.hurt": "jan esun li pakala", "subtitles.entity.wandering_trader.no": "jan esun li pilin ante", "subtitles.entity.wandering_trader.reappeared": "jan esun li kama", "subtitles.entity.wandering_trader.trade": "jan esun li esun", "subtitles.entity.wandering_trader.yes": "jan esun li pilin sama", "subtitles.entity.warden.agitated": "monsuta kute li mu ike", "subtitles.entity.warden.ambient": "monsuta kute li mu", "subtitles.entity.warden.angry": "monsuta kute li pilin ike mute", "subtitles.entity.warden.attack_impact": "monsuta kute li utala", "subtitles.entity.warden.death": "monsuta kute li moli", "subtitles.entity.warden.dig": "monsuta kute li weka tawa anpa", "subtitles.entity.warden.emerge": "monsuta kute li kama tan supa", "subtitles.entity.warden.heartbeat": "insa pilin pi monsuta kute li kalama", "subtitles.entity.warden.hurt": "monsuta kute li pakala", "subtitles.entity.warden.listening": "monsuta kute li kute", "subtitles.entity.warden.listening_angry": "monsuta kute li kute ike", "subtitles.entity.warden.nearby_close": "monsuta kute li tawa sina", "subtitles.entity.warden.nearby_closer": "monsuta kute li tawa poka", "subtitles.entity.warden.nearby_closest": "monsuta kute li tawa poka mute", "subtitles.entity.warden.roar": "monsuta kute li mu wawa", "subtitles.entity.warden.sniff": "monsuta kute li pilin e kon", "subtitles.entity.warden.sonic_boom": "monsuta kute li kalama wawa", "subtitles.entity.warden.sonic_charge": "monsuta kute li tawa utala", "subtitles.entity.warden.step": "monsuta kute li tawa", "subtitles.entity.warden.tendril_clicks": "linja pi monsuta kute li kalama", "subtitles.entity.wind_charge.throw": "sike kon li tawa kon", "subtitles.entity.wind_charge.wind_burst": "sike kon li pakala", "subtitles.entity.witch.ambient": "jan pi telo wawa li kalama musi", "subtitles.entity.witch.celebrate": "jan pi telo wawa li toki e pona", "subtitles.entity.witch.death": "jan pi telo wawa li moli", "subtitles.entity.witch.drink": "jan pi telo wawa li moku", "subtitles.entity.witch.hurt": "jan pi telo wawa li pakala", "subtitles.entity.witch.throw": "jan pi telo wawa li pana", "subtitles.entity.wither.ambient": "<PERSON><PERSON><PERSON> li mu ike", "subtitles.entity.wither.death": "<PERSON><PERSON><PERSON> li moli", "subtitles.entity.wither.hurt": "<PERSON><PERSON><PERSON> li pakala", "subtitles.entity.wither.shoot": "<PERSON><PERSON><PERSON> li utala", "subtitles.entity.wither.spawn": "<PERSON><PERSON><PERSON> li kama wawa", "subtitles.entity.wither_skeleton.ambient": "jan moli palisa Wither li kalama", "subtitles.entity.wither_skeleton.death": "jan moli palisa Wither li moli", "subtitles.entity.wither_skeleton.hurt": "jan moli palisa Wither li pakala", "subtitles.entity.wolf.ambient": "soweli pona li mu kon", "subtitles.entity.wolf.bark": "soweli pona li mu", "subtitles.entity.wolf.death": "soweli pona li moli", "subtitles.entity.wolf.growl": "soweli pona li mu ike", "subtitles.entity.wolf.hurt": "soweli pona li pakala", "subtitles.entity.wolf.pant": "soweli pona li mu kon", "subtitles.entity.wolf.shake": "soweli pona li weka e telo tan sijelo", "subtitles.entity.wolf.whine": "soweli pona li mu pi pilin ike", "subtitles.entity.zoglin.ambient": "soweli moli Zoglin li mu", "subtitles.entity.zoglin.angry": "soweli moli Zoglin li mu ike", "subtitles.entity.zoglin.attack": "soweli moli Zoglin li utala", "subtitles.entity.zoglin.death": "soweli moli Zoglin li moli pini", "subtitles.entity.zoglin.hurt": "soweli moli Zoglin li pakala", "subtitles.entity.zoglin.step": "soweli moli Zoglin li tawa", "subtitles.entity.zombie.ambient": "jan moli li mu", "subtitles.entity.zombie.attack_wooden_door": "lupa li tawa lili", "subtitles.entity.zombie.break_wooden_door": "lupa li pakala", "subtitles.entity.zombie.converted_to_drowned": "jan moli li kama jan moli telo", "subtitles.entity.zombie.death": "jan moli li moli", "subtitles.entity.zombie.destroy_egg": "sike mama li pakala", "subtitles.entity.zombie.hurt": "jan moli li pakala", "subtitles.entity.zombie.infect": "jan moli li ike e jan tomo", "subtitles.entity.zombie_horse.ambient": "soweli tawa moli li mu", "subtitles.entity.zombie_horse.death": "soweli tawa moli li moli", "subtitles.entity.zombie_horse.hurt": "soweli tawa moli li pakala", "subtitles.entity.zombie_villager.ambient": "jan tomo moli li mu", "subtitles.entity.zombie_villager.converted": "jan tomo moli li kalama wawa", "subtitles.entity.zombie_villager.cure": "jan tomo moli li kama pona", "subtitles.entity.zombie_villager.death": "jan tomo moli li moli", "subtitles.entity.zombie_villager.hurt": "jan tomo moli li pakala", "subtitles.entity.zombified_piglin.ambient": "jan <PERSON><PERSON> moli li mu", "subtitles.entity.zombified_piglin.angry": "jan <PERSON> moli li mu ike", "subtitles.entity.zombified_piglin.death": "jan <PERSON><PERSON> moli li moli pini", "subtitles.entity.zombified_piglin.hurt": "jan <PERSON><PERSON> moli li pakala", "subtitles.event.mob_effect.bad_omen": "ike kama li kama lon", "subtitles.event.mob_effect.raid_omen": "utala tomo li lon poka", "subtitles.event.mob_effect.trial_omen": "alasa ike li lon poka", "subtitles.event.raid.horn": "ilo kalama ike li kalama", "subtitles.item.armor.equip": "jan li len", "subtitles.item.armor.equip_chain": "len pi sike lili li kalama", "subtitles.item.armor.equip_diamond": "len laso li kalama", "subtitles.item.armor.equip_elytra": "luka waso li kalama lili", "subtitles.item.armor.equip_gold": "len jelo li kalama", "subtitles.item.armor.equip_iron": "len walo li kalama", "subtitles.item.armor.equip_leather": "len selo li kalama lili", "subtitles.item.armor.equip_netherite": "len pimeja li kalama", "subtitles.item.armor.equip_turtle": "selo akesi li kalama", "subtitles.item.armor.equip_wolf": "len li kama lon soweli pona", "subtitles.item.armor.unequip_wolf": "len li weka tan soweli pona", "subtitles.item.axe.scrape": "ilo kipisi li weka e selo", "subtitles.item.axe.strip": "selo li weka tan kasi", "subtitles.item.axe.wax_off": "selo awen li weka", "subtitles.item.bone_meal.use": "ko pi palisa sijelo li kalama", "subtitles.item.book.page_turn": "lipu li kalama lili", "subtitles.item.book.put": "lipu li kalama", "subtitles.item.bottle.empty": "poki lili li weka e telo ona", "subtitles.item.bottle.fill": "poki telo lili li poki", "subtitles.item.brush.brushing.generic": "ilo li weka e ko", "subtitles.item.brush.brushing.gravel": "ilo li weka e kiwen lili", "subtitles.item.brush.brushing.gravel.complete": "weka pi kiwen lili li pini", "subtitles.item.brush.brushing.sand": "ilo li weka e ko", "subtitles.item.brush.brushing.sand.complete": "weka ko li pini", "subtitles.item.bucket.empty": "telo li weka tan poki", "subtitles.item.bucket.fill": "poki telo li poki", "subtitles.item.bucket.fill_axolotl": "jan li poki e akesi suwi", "subtitles.item.bucket.fill_fish": "kala li tawa poki", "subtitles.item.bucket.fill_tadpole": "akesi sewi lili li tawa poki", "subtitles.item.bundle.drop_contents": "ijo li weka tan poki len", "subtitles.item.bundle.insert": "ijo li tawa lon insa pi poki len", "subtitles.item.bundle.insert_fail": "poki len li jo ale", "subtitles.item.bundle.remove_one": "ijo li weka tan poki len", "subtitles.item.chorus_fruit.teleport": "jan li tawa suli", "subtitles.item.crop.plant": "jan li kasi e ma", "subtitles.item.crossbow.charge": "ilo alasa supa li kalama", "subtitles.item.crossbow.hit": "palisa alasa li utala", "subtitles.item.crossbow.load": "ilo alasa supa li awen", "subtitles.item.crossbow.shoot": "ilo alasa supa li pana", "subtitles.item.dye.use": "kule li kule", "subtitles.item.elytra.flying": "len waso li mu", "subtitles.item.firecharge.use": "sike seli li tawa", "subtitles.item.flintandsteel.use": "ilo seli li kalama", "subtitles.item.glow_ink_sac.use": "telo suno li sitelen", "subtitles.item.goat_horn.play": "palisa pi soweli nena li kalama", "subtitles.item.hoe.till": "jan li kepeken ilo kasi", "subtitles.item.honey_bottle.drink": "jan li moku e suwi pipi", "subtitles.item.honeycomb.wax_on": "jan li pana e selo awen", "subtitles.item.horse_armor.unequip": "len awen li weka tan soweli tawa", "subtitles.item.ink_sac.use": "telo pimeja li sitelen", "subtitles.item.lead.break": "linja lawa li pakala", "subtitles.item.lead.tied": "linja lawa li kama lon ijo", "subtitles.item.lead.untied": "linja lawa li kama lon ala ijo", "subtitles.item.llama_carpet.unequip": "namako li weka tan soweli pi telo uta", "subtitles.item.lodestone_compass.lock": "ilo nasin li kama awen", "subtitles.item.mace.smash_air": "palisa wawa li pakala e ijo", "subtitles.item.mace.smash_ground": "palisa wawa li pakala e ijo", "subtitles.item.nether_wart.plant": "jan li pana e soko lon ma", "subtitles.item.ominous_bottle.dispose": "poki lili li pakala", "subtitles.item.saddle.unequip": "len monsi li weka tan soweli tawa", "subtitles.item.shears.shear": "ilo kipisi lili li kalama", "subtitles.item.shears.snip": "ilo kipisi lili li kipisi", "subtitles.item.shield.block": "sinpin sijelo li awen", "subtitles.item.shovel.flatten": "ilo ma li supa e ma", "subtitles.item.spyglass.stop_using": "palisa lukin li lili", "subtitles.item.spyglass.use": "palisa lukin li suli", "subtitles.item.totem.use": "ilo pi moli ala li wawa", "subtitles.item.trident.hit": "ilo alasa telo li utala", "subtitles.item.trident.hit_ground": "ilo alasa telo li kalama lon ma", "subtitles.item.trident.return": "ilo alasa telo li kama", "subtitles.item.trident.riptide": "ilo alasa telo li tawa wawa", "subtitles.item.trident.throw": "ilo alasa telo li kalama", "subtitles.item.trident.thunder": "ilo alasa telo li kalama sewi", "subtitles.item.wolf_armor.break": "len pi soweli pona li pakala", "subtitles.item.wolf_armor.crack": "len pi soweli pona li kama kipisi", "subtitles.item.wolf_armor.damage": "len pi soweli pona li pakala lili", "subtitles.item.wolf_armor.repair": "len pi soweli pona li kama pona sin", "subtitles.particle.soul_escape": "kon li weka", "subtitles.ui.cartography_table.take_result": "jan li <PERSON>len e ma", "subtitles.ui.hud.bubble_pop": "kon wan li weka", "subtitles.ui.loom.take_result": "ilo len li pali", "subtitles.ui.stonecutter.take_result": "sike pi kipisi kiwen li pali", "subtitles.weather.rain": "sewi li pana e telo", "symlink_warning.message": "ma tan poki ma pi nasin tawa poki ante li ken ike. kama sona la o lukin e %s.", "symlink_warning.message.pack": "poki li jo e nasin tawa poki ante la ni li ken pakala e ilo sina. kama sona namako la tawa lipu %s.", "symlink_warning.message.world": "ma tan poki ma pi nasin tawa poki ante li ken ike. kama sona la o lukin e %s.", "symlink_warning.more_info": "sona namako", "symlink_warning.title": "poki ma li jo e nasin tawa poki ante", "symlink_warning.title.pack": "poki sin ni li jo e nasin tawa poki ante", "symlink_warning.title.world": "poki ma li jo e nasin tawa poki ante", "team.collision.always": "tenpo ale", "team.collision.never": "tenpo ala", "team.collision.pushOtherTeams": "sina ken ala ken tawa e jan pi kulupu ante?", "team.collision.pushOwnTeam": "sina ken ala ken tawa e jan pi kulupu sina?", "team.notFound": "kulupu \"%s\" li ike", "team.visibility.always": "tenpo ale", "team.visibility.hideForOtherTeams": "o len tan kulupu ante", "team.visibility.hideForOwnTeam": "o len tan kulupu sina", "team.visibility.never": "tenpo ala", "telemetry.event.advancement_made.description": "mi sona e nasin pi pali pona la mi ken pona e nasin pi pini musi.", "telemetry.event.advancement_made.title": "pali li pini", "telemetry.event.game_load_times.description": "mi ken kama sona pona e tenpo open kepeken tenpo ni pi open musi kepeken ni: mi kama sona e suli tenpo pali pi kipisi open.", "telemetry.event.game_load_times.title": "tenpo pi kama lon musi", "telemetry.event.optional": "%s (sina ken kepeken ala)", "telemetry.event.optional.disabled": "%s (sina ken kepeken ala) - kepeken ala", "telemetry.event.performance_metrics.description": "kulupu Mojen li sona e wawa pi musi Manka lon ilo sina la, ona li ken wawa e musi Manka tawa ilo mute, tawa nasin ilo mute kin. \nsona ni li wile e nanpa musi tawa ni: kulupu Mojen li ken sona e wawa pi nanpa namako pi musi Manka.", "telemetry.event.performance_metrics.title": "sona pi wawa musi lon ilo sina", "telemetry.event.required": "%s (sina ken ala kepeken ala)", "telemetry.event.world_load_times.description": "sona ni li suli tawa kulupu mi. mi ante e musi Manka la mi wile sona e ni: ni li ante seme e tenpo pi kama lon ma?", "telemetry.event.world_load_times.title": "tenpo pi kama lon ma", "telemetry.event.world_loaded.description": "kulupu Mojang li sona e musi pi jan musi la (nasin musi, kepeken pi namako musi, nanpa musi), ona li ken pali e ijo sin tawa kepeken pi jan mute.\nilo Telemesi li kepeken 'ma li lon' li kepeken 'ma li lon ala' tawa sona ni: sina musi lon tenpo pi mute seme?", "telemetry.event.world_loaded.title": "ma li lon", "telemetry.event.world_unloaded.description": "mi kepeken pini musi kepeken open musi tawa kama jo e ni: tenpo sama seme la ma li open. sina tawa lipu open la, sina tawa weka pi ma kulupu la, mi kama jo e nanpa ni (tenpo lili lon en tenpo musi lili).", "telemetry.event.world_unloaded.title": "ma li lon ala", "telemetry.property.advancement_game_time.title": "tenpo musi (kepeken tenpo lili)", "telemetry.property.advancement_id.title": "nanpa pi pali pona", "telemetry.property.client_id.title": "nanpa pi ilo sina", "telemetry.property.client_modded.title": "musi sina li namako", "telemetry.property.dedicated_memory_kb.title": "ilo sina li ken poki e sona musi pi mute ni (kB)", "telemetry.property.event_timestamp_utc.title": "tenpo pi kama ijo (UTC)", "telemetry.property.frame_rate_samples.title": "wawa tenpo musi (FPS)", "telemetry.property.game_mode.title": "nasin musi", "telemetry.property.game_version.title": "nanpa musi", "telemetry.property.launcher_name.title": "nimi pi ilo open", "telemetry.property.load_time_bootstrap_ms.title": "tenpo pi lon musi ale (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "tenpo lon lipu pi open musi (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "tenpo pi open lipu (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "tenpo pi lon musi ale (Milliseconds)", "telemetry.property.minecraft_session_id.title": "nanpa pi tenpo musi", "telemetry.property.new_world.title": "ma sin", "telemetry.property.number_of_samples.title": "nanpa ni: mi lukin pali", "telemetry.property.operating_system.title": "nasin pi ilo sina", "telemetry.property.opt_in.title": "lon wile sina", "telemetry.property.platform.title": "nasin pi musi Man<PERSON>", "telemetry.property.realms_map_content.title": "musi pi ma <PERSON>s (nimi musi)", "telemetry.property.render_distance.title": "suli pi ken lukin", "telemetry.property.render_time_samples.title": "alasa tawa tenpo sitelen", "telemetry.property.seconds_since_load.title": "tenpo lili ni la ma li lon (Seconds)", "telemetry.property.server_modded.title": "ma kulupu li kepeken namako musi", "telemetry.property.server_type.title": "nasin pi ma kulupu", "telemetry.property.ticks_since_load.title": "tenpo musi lili ni la ma li lon", "telemetry.property.used_memory_samples.title": "musi li kepeken mute ni pi sona RAM", "telemetry.property.user_id.title": "nanpa pi jan kepeken", "telemetry.property.world_load_time_ms.title": "tenpo pi lon ma (Milliseconds)", "telemetry.property.world_session_id.title": "nanpa pi open sin pi ma ni", "telemetry_info.button.give_feedback": "o pana e wile", "telemetry_info.button.privacy_statement": "toki lawa pi len awen", "telemetry_info.button.show_data": "o lukin e sona mi", "telemetry_info.opt_in.description": "mi wile pana e nanpa namako", "telemetry_info.property_title": "sona lon ni", "telemetry_info.screen.description": "kulupu Mojen li kama jo e sona ni tawa ni: ona li sona e wile pi jan musi, li ken awen pona e musi Manka. namako la sina ken pana e toki sina tawa alasa ni.", "telemetry_info.screen.title": "kama jo sona pi ilo Telemesi", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "ijo kon %s li ken ala", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "mi wile e leko %s. leko %s li lon", "test.error.expected_block_tag": "mi wile e leko pi kulupu #%s. leko %s li lon", "test.error.expected_container_contents": "poki o jo e %s", "test.error.expected_container_contents_single": "poki o jo e %s wan taso", "test.error.expected_empty_container": "poki o jo ala e ijo", "test.error.expected_entity": "mi wile e %s", "test.error.expected_entity_around": "mi wile e %s lon poka pi ma %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "mi wile e ni: %s li jo e %s%s", "test.error.expected_entity_having": "jo ijo o poki e %s", "test.error.expected_entity_holding": "ijo o jo e %s lon luka", "test.error.expected_entity_in_test": "mi wile e %s lon pali", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "mi wile e ni: %s li lon %s, %s, %s (ma open pali la %s, %s, %s)", "test.error.expected_item": "mi wile e ijo %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "ijo leko li lon ala", "test.error.position": "%s. ni li lon %s, %s, %s (ma open pali la %s %s %s). ni li lon tenpo nanpa %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "mi pona lon kipisi ike pi tenpo lili: mi wile e %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "mi ken ala lon e ma ", "test.error.spawn_failure": "mi ken ala lon e ijo %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "mi pana ala e tomo pali tawa %s", "test.error.tick": "%s. ni li lon tenpo nanpa %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "mi ken ala pona li ken ala ike lon %s ", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "mi wile e %s wan taso o lon poka pi ma %s, %s, %s. taso, %s li lon", "test.error.unexpected_block": "mi wile ala e ni: leko li %s", "test.error.unexpected_entity": "sona mi la %s o lon ala", "test.error.unexpected_item": "sona mi la ijo %s o lon ala", "test.error.unknown": "pali ike pi lon ala sona li lon: %s", "test.error.value_not_equal": "mi wile e ni: %s li %s. taso, ona li %s", "test.error.wrong_block_entity": "kule pi ijo leko ni li ike: %s", "test_block.error.missing": "leko %s li weka tan tomo pali", "test_block.error.too_many": "mute pi leko ni li ike: %s", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "toki:", "test_block.mode.accept": "nasin pona", "test_block.mode.fail": "nasin ike", "test_block.mode.log": "nasin toki", "test_block.mode.start": "nasin open", "test_block.mode_info.accept": "nasin pona - o pana e pona tawa (lili) pali", "test_block.mode_info.fail": "nasin ike - o pana e ike tawa pali", "test_block.mode_info.log": "nasin toki - o pana e toki", "test_block.mode_info.start": "nasin open - o open e pali", "test_instance.action.reset": "o sin o lon", "test_instance.action.run": "o lon o pali", "test_instance.action.save": "o awen e tomo", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "mi pakala: %s", "test_instance.description.function": "nasin: %s", "test_instance.description.invalid_id": "nimi pali ni li lon ala", "test_instance.description.no_test": "alasa li lon ala", "test_instance.description.structure": "tomo: %s", "test_instance.description.type": "nasin: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "ijo:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "mi ken ala awen e nasin pi tomo pali tawa poki pali lon %s, %s, %s", "test_instance_block.invalid": "[ike]", "test_instance_block.reset_success": "sin li pona tawa pali: %s", "test_instance_block.rotation": "lon:", "test_instance_block.size": "suli pi tomo pali", "test_instance_block.starting": "mi open e alasa %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "mi lukin e ni: ilo sina li kepeken nasin 32-bit. ken la sina ken ala musi kepeken ni lon tenpo kama. tenpo kama la ilo sina o kepeken nasin 64-bit.", "title.32bit.deprecation.realms": "tenpo kama la musi Manka li wile e ilo pi nasin 64-bit. ni la sina ken ala musi lon ma kulupu kepeken ilo ni, li wile pini e esun pi ma Realms.", "title.32bit.deprecation.realms.check": "o pana sin ala e lipu ni", "title.32bit.deprecation.realms.header": "mi lukin e ni: ilo sina li kepeken 32-bit", "title.credits": "nasin pali pi kulupu Mojang li jo e toki ken. o pana ala e musi ni!", "title.multiplayer.disabled": "sina ken ala musi kulupu, kepeken nimi musi ni. o lukin e lawa pi nimi sina pi kulupu Microsoft.", "title.multiplayer.disabled.banned.name": "sina wile musi pi ma kulupu la o ante e nimi sina", "title.multiplayer.disabled.banned.permanent": "tenpo ale la sina ken ala musi kulupu", "title.multiplayer.disabled.banned.temporary": "tenpo lili la sina ken ala kepeken e ma kulupu", "title.multiplayer.lan": "ma kulupu pi jan poka (LAN)", "title.multiplayer.other": "ma kulupu", "title.multiplayer.realms": "ma Realms", "title.singleplayer": "musi pi jan wan", "translation.test.args": "%s %s", "translation.test.complex": "<PERSON>jo <PERSON>, sin la %s%2$s en %s en %1$s. pini la %s en %1$s lon tenpo sin tu!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "toki %", "translation.test.invalid2": "toki %s", "translation.test.none": "ale o toki!", "translation.test.world": "ma", "trim_material.minecraft.amethyst": "ijo pi kiwen pi loje laso", "trim_material.minecraft.copper": "ijo pi kiwen pi loje jelo", "trim_material.minecraft.diamond": "ijo pi kiwen laso", "trim_material.minecraft.emerald": "ijo pi kiwen esun", "trim_material.minecraft.gold": "ijo pi kiwen jelo", "trim_material.minecraft.iron": "ijo pi kiwen walo", "trim_material.minecraft.lapis": "ijo pi kiwen pi laso telo", "trim_material.minecraft.netherite": "ijo pi mani <PERSON>", "trim_material.minecraft.quartz": "ijo pi kiwen walo pi ma <PERSON>her", "trim_material.minecraft.redstone": "ijo pi ko Redstone", "trim_material.minecraft.resin": "ijo kiwen ko loje", "trim_pattern.minecraft.bolt": "namako len tawa", "trim_pattern.minecraft.coast": "namako len telo", "trim_pattern.minecraft.dune": "namako len seli", "trim_pattern.minecraft.eye": "namako len oko", "trim_pattern.minecraft.flow": "namako len esun", "trim_pattern.minecraft.host": "namako len lawa", "trim_pattern.minecraft.raiser": "namako len sewi", "trim_pattern.minecraft.rib": "namako len palisa", "trim_pattern.minecraft.sentry": "namako len utala", "trim_pattern.minecraft.shaper": "namako len pali", "trim_pattern.minecraft.silence": "namako len pi kalama ala", "trim_pattern.minecraft.snout": "namako len soweli", "trim_pattern.minecraft.spire": "namako len nena", "trim_pattern.minecraft.tide": "namako len kala", "trim_pattern.minecraft.vex": "namako len kon", "trim_pattern.minecraft.ward": "namako len awen", "trim_pattern.minecraft.wayfinder": "namako len nasin", "trim_pattern.minecraft.wild": "namako len kasi", "tutorial.bundleInsert.description": "o pana e ijo kepeken nena nanpa tu pi ilo luka", "tutorial.bundleInsert.title": "o kepeken poki len", "tutorial.craft_planks.description": "ken la lipu pi nasin pali li pona", "tutorial.craft_planks.title": "o pali e kasi kiwen kipisi", "tutorial.find_tree.description": "o utala e kasi suli o kama jo e kiwen kasi", "tutorial.find_tree.title": "o alasa e kasi suli", "tutorial.look.description": "ilo luka la sina ken lukin li ken tawa sike", "tutorial.look.title": "o lukin e ma", "tutorial.move.description": "o tawa sewi kepeken nena %s", "tutorial.move.title": "o tawa kepeken nena ni: %s en %s en %s en %s", "tutorial.open_inventory.description": "o luka e %s", "tutorial.open_inventory.title": "o lukin e jo sina", "tutorial.punch_tree.description": "o awen luka e %s", "tutorial.punch_tree.title": "o pakala e kasi suli", "tutorial.socialInteractions.description": "o open kepeken nena %s", "tutorial.socialInteractions.title": "toki kulupu", "upgrade.minecraft.netherite_upgrade": "namako len wawa pi mani Netherite"}