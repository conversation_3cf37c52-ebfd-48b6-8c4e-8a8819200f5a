{"accessibility.onboarding.accessibility.button": "Toegankelijkheidsinstellingen", "accessibility.onboarding.screen.narrator": "Druk op enter om de verteller in te schakelen", "accessibility.onboarding.screen.title": "Welkom in Minecraft!\n\nWil je de verteller inschakelen of de toegankelijkheidsinstellingen bekijken?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "Serveradres", "addServer.enterName": "Servernaam", "addServer.resourcePack": "Serverbronpakketten", "addServer.resourcePack.disabled": "uitgeschakeld", "addServer.resourcePack.enabled": "ing<PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.prompt": "vragen", "addServer.title": "Serverinformatie bewerken", "advMode.command": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Altijd actief", "advMode.mode.conditional": "Voorwaardelijk", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Vereist Redstone", "advMode.mode.sequence": "Gekoppeld", "advMode.mode.unconditional": "Onvoorwaardelijk", "advMode.notAllowed": "Je moet een gepromoveerde speler in de creatieve modus zijn", "advMode.notEnabled": "Opdrachtblokken zijn niet ingeschakeld op deze server", "advMode.previousOutput": "<PERSON><PERSON><PERSON> u<PERSON>", "advMode.setCommand": "Consoleopdracht voor blok instellen", "advMode.setCommand.success": "Opdracht ingesteld: %s", "advMode.trackOutput": "Uitvoer bijhouden", "advMode.triggering": "<PERSON><PERSON>", "advMode.type": "Type", "advancement.advancementNotFound": "Vooruitgang '%s' is onbekend", "advancements.adventure.adventuring_time.description": "Ontdek elk klimaat", "advancements.adventure.adventuring_time.title": "Tijd voor avontuur", "advancements.adventure.arbalistic.description": "<PERSON><PERSON> vij<PERSON> un<PERSON>e we<PERSON> met <PERSON><PERSON>", "advancements.adventure.arbalistic.title": "Arbalistiek", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON><PERSON> in de buurt van een sculksensor of hoeder, zodat ze jou niet opmerken", "advancements.adventure.avoid_vibration.title": "Wereldrecord sluipen", "advancements.adventure.blowback.description": "Dood een vlaag met een door een vlaag afgevuurde windvlaag", "advancements.adventure.blowback.title": "Zware tegenslag", "advancements.adventure.brush_armadillo.description": "Gebruik een borstel om een hoornplaat van een gordeldier te krijgen", "advancements.adventure.brush_armadillo.title": "Wat een plaatje", "advancements.adventure.bullseye.description": "<PERSON><PERSON> de roos van een do<PERSON> op ten minste 30 meter afstand", "advancements.adventure.bullseye.title": "In de roos!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Maak een gedecoreerde pot van vier aardewerkscherven", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON> zorg gere<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Sta in de buurt van een vervaardigingsmachine die een vervaardigingsmachine vervaardigt", "advancements.adventure.crafters_crafting_crafters.title": "Autoproductie", "advancements.adventure.fall_from_world_height.description": "Overleef een vrije val van de bovenka<PERSON> van de wereld (bouwlimiet) tot de bodem van de wereld", "advancements.adventure.fall_from_world_height.title": "Met je neus op de bodem vallen", "advancements.adventure.heart_transplanter.description": "Plaats een schorswakerhart op de juiste manier tussen twee bleke eikenstammen", "advancements.adventure.heart_transplanter.title": "Hartenter", "advancements.adventure.hero_of_the_village.description": "Verdedig een dorp succesvol tegen een overval", "advancements.adventure.hero_of_the_village.title": "Held der dorpelingen", "advancements.adventure.honey_block_slide.description": "Spring tegen een honingblok om je val te breken", "advancements.adventure.honey_block_slide.title": "Kleverige situatie", "advancements.adventure.kill_a_mob.description": "Dood een vijandig monster", "advancements.adventure.kill_a_mob.title": "Monsterjager", "advancements.adventure.kill_all_mobs.description": "<PERSON><PERSON> vijandige <PERSON>", "advancements.adventure.kill_all_mobs.title": "Op monsters gejaagd", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Dood een wezen in de buurt van een sculkkatalysator", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Als een olievlek", "advancements.adventure.lighten_up.description": "Ver<PERSON>er een koperen lamp door de oxidatie eraf te schrapen met een bijl", "advancements.adventure.lighten_up.title": "<PERSON><PERSON> lichter vooruitzicht", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Bescherm een dorpeling tegen een ongewenste schok zonder een brand te starten", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Sc<PERSON><PERSON>beschermer", "advancements.adventure.minecraft_trials_edition.description": "Betreed een beproevingskamer", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Beproevingseditie", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> met een k<PERSON>g", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON> trouwe", "advancements.adventure.overoverkill.description": "Breng vijftig hartjes schade toe in één knotsslag", "advancements.adventure.overoverkill.title": "<PERSON>eel te veel van het goede", "advancements.adventure.play_jukebox_in_meadows.description": "<PERSON><PERSON><PERSON> de weide tot leven met het geluid van muziek uit een platenspeler", "advancements.adventure.play_jukebox_in_meadows.title": "De bergen zingen", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON><PERSON> de stroomsterkte van een bewerkte boekenkast uit met een verge<PERSON>er", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON><PERSON> verhalen", "advancements.adventure.revaulting.description": "<PERSON>al een onheilspellende klu<PERSON> van het slot met een onheilspellende beproevingssleutel", "advancements.adventure.revaulting.title": "<PERSON> overwonnen", "advancements.adventure.root.description": "Avontuur, ontdekking en gevecht", "advancements.adventure.root.title": "Avontuur", "advancements.adventure.salvage_sherd.description": "<PERSON>rst<PERSON> een verdacht blok om een aardewerkscherf te krijgen", "advancements.adventure.salvage_sherd.title": "Archeologisch onderzoek", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON><PERSON> op iets met een pijl", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON> in een bed om je respawnpunt te veranderen", "advancements.adventure.sleep_in_bed.title": "Slaap zacht", "advancements.adventure.sniper_duel.description": "Dood een skelet op een afstand van minstens vijftig blokken", "advancements.adventure.sniper_duel.title": "Sluipschuttersduel", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> naar de Enderdraak door een verrekijker", "advancements.adventure.spyglass_at_dragon.title": "Is het een vliegtuig?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON>jk naar een <PERSON> door een verrekijker", "advancements.adventure.spyglass_at_ghast.title": "Is het een ballon?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON>jk naar een papegaai door een verrekijker", "advancements.adventure.spyglass_at_parrot.title": "Is het een vogel?", "advancements.adventure.summon_iron_golem.description": "Help met de verdediging van een dorp door een ijzergolem op te roepen", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Gooi een drietand ergens naartoe.\nOpmerking: je enige wapen weggooien is geen goed idee.", "advancements.adventure.throw_trident.title": "Een a<PERSON>...?", "advancements.adventure.totem_of_undying.description": "Gebruik een totem der onsterfelijkheid om te voorkomen dat je dood gaat", "advancements.adventure.totem_of_undying.title": "Tweede kans", "advancements.adventure.trade.description": "<PERSON> succesvol met een do<PERSON><PERSON><PERSON>", "advancements.adventure.trade.title": "Wat een deal!", "advancements.adventure.trade_at_world_height.description": "<PERSON> met een do<PERSON><PERSON><PERSON><PERSON><PERSON> op de bouwlimiet", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Gebruik de volgende smeedssjablonen ten minste één keer: erger<PERSON>, geti<PERSON><PERSON>, hoeder, padvinder, piek, rib, snoet en stilte", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "Maak een versierd stuk harnas in een smeedstafel", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Dood twee fantomen met een doorborende pijl", "advancements.adventure.two_birds_one_arrow.title": "<PERSON>we<PERSON> vogels in één schot", "advancements.adventure.under_lock_and_key.description": "Open een k<PERSON><PERSON> met een be<PERSON><PERSON><PERSON><PERSON>l", "advancements.adventure.under_lock_and_key.title": "<PERSON>chter slot en grendel", "advancements.adventure.use_lodestone.description": "Gebruik een kompas op een zeilsteen", "advancements.adventure.use_lodestone.title": "Op eigen kompas zeilen", "advancements.adventure.very_very_frightening.description": "Raak een do<PERSON><PERSON>g met een b<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.very_very_frightening.title": "Heel erg beangstigend", "advancements.adventure.voluntary_exile.description": "Dood een overvalsleider.\nHet is misschien verstandig om voorlopig uit de buurt van dorpen te blijven...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> in ballingschap", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Loop op poedersneeuw... zonder erin te zakken", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON>o licht als een veertje", "advancements.adventure.who_needs_rockets.description": "Gebruik een windvlaag om jezelf zeven blokken te lanceren", "advancements.adventure.who_needs_rockets.title": "Zonder vleugels!", "advancements.adventure.whos_the_pillager_now.description": "<PERSON><PERSON> een rover een koekje van eigen deeg", "advancements.adventure.whos_the_pillager_now.title": "Wie is nu de rover?", "advancements.empty": "Het lijkt erop dat hier niets is...", "advancements.end.dragon_breath.description": "<PERSON><PERSON><PERSON><PERSON> drakena<PERSON>m in een glazen fles", "advancements.end.dragon_breath.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "advancements.end.dragon_egg.description": "Houd het drakenei vast", "advancements.end.dragon_egg.title": "De volgende generatie", "advancements.end.elytra.description": "<PERSON>d een de<PERSON>child", "advancements.end.elytra.title": "Alles is mogelijk", "advancements.end.enter_end_gateway.description": "Ontsnap van het eiland", "advancements.end.enter_end_gateway.title": "Afgelegen vlucht", "advancements.end.find_end_city.description": "Ga door, wat zou er kunnen geb<PERSON>en?", "advancements.end.find_end_city.title": "De stad in het einde van het spel", "advancements.end.kill_dragon.description": "Succes", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Zweef vijftig blokken omhoog door een aanval van een <PERSON>", "advancements.end.levitate.title": "Wat een uitz<PERSON>t!", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.respawn_dragon.title": "Het einde... Alweer...", "advancements.end.root.description": "Of het begin?", "advancements.end.root.title": "De End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Help een helper een taart te laten vallen bij een nootblok", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "De <PERSON> op de taart", "advancements.husbandry.allay_deliver_item_to_player.description": "Laat een helper voorwerpen naar je brengen", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON>jn beste vriend", "advancements.husbandry.axolotl_in_a_bucket.description": "Vang een axolotl met een emmer", "advancements.husbandry.axolotl_in_a_bucket.title": "Het schattigste roofdier", "advancements.husbandry.balanced_diet.description": "Eet alles wat eet<PERSON><PERSON> is, zelfs als het niet goed voor je is", "advancements.husbandry.balanced_diet.title": "<PERSON><PERSON> geb<PERSON> dieet", "advancements.husbandry.breed_all_animals.description": "Fok alle dieren!", "advancements.husbandry.breed_all_animals.title": "Twee bij twee", "advancements.husbandry.breed_an_animal.description": "Fok twee dieren", "advancements.husbandry.breed_an_animal.title": "De bloempjes en de bijtjes", "advancements.husbandry.complete_catalogue.description": "Tem alle kattenvarianten!", "advancements.husbandry.complete_catalogue.title": "De volledige kat-alogus", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON> een snu<PERSON>", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "Vang een vis", "advancements.husbandry.fishy_business.title": "Daar zit een luchtje aan", "advancements.husbandry.froglights.description": "Heb alle kikker<PERSON>ten in je inventaris", "advancements.husbandry.froglights.title": "Gebundelde krachten", "advancements.husbandry.kill_axolotl_target.description": "<PERSON><PERSON> <PERSON><PERSON> met een axolotl en win een gevecht", "advancements.husbandry.kill_axolotl_target.title": "De helende kracht der vriendschap!", "advancements.husbandry.leash_all_frog_variants.description": "<PERSON><PERSON> elke kik<PERSON> aan een leid<PERSON>w", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON> k<PERSON>", "advancements.husbandry.make_a_sign_glow.description": "Laat de tekst op een bord gloeien", "advancements.husbandry.make_a_sign_glow.title": "<PERSON>jk dat eens schitteren!", "advancements.husbandry.netherite_hoe.description": "Gebruik een Netherietstaaf om een schoffel te <PERSON>n en beoordeel daarna je leven<PERSON>uzes", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON> toewi<PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Verkrijg een snuffelaarsei", "advancements.husbandry.obtain_sniffer_egg.title": "Interessante geur", "advancements.husbandry.place_dried_ghast_in_water.description": "Plaats een uitgedroo<PERSON>d <PERSON>blo<PERSON> in water", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON>ppen en nathouden", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant een snuffe<PERSON>", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON>en van het verleden", "advancements.husbandry.plant_seed.description": "Plant een zaad en bekijk hoe het groeit", "advancements.husbandry.plant_seed.title": "Gras zien groeien", "advancements.husbandry.remove_wolf_armor.description": "Gebruik een schaar om wolvenhar<PERSON> van een wolf te verwijderen", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON>, knabbel, knui<PERSON>je", "advancements.husbandry.repair_wolf_armor.description": "Gebruik een gordeldierhoornplaat om wolvenharnas te repareren", "advancements.husbandry.repair_wolf_armor.title": "<PERSON>o goed als nieuw", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Stap in een boot en dobber met een geit", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Wat een gekke geit!", "advancements.husbandry.root.description": "De wereld zit vol met vrienden en eten", "advancements.husbandry.root.title": "Agricultuur", "advancements.husbandry.safely_harvest_honey.description": "Gebruik een kampvuur om honing uit een bijenkorf te verzamelen met een glazen fles, zonder dat de bijen van slag raken", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON> er maar bij", "advancements.husbandry.silk_touch_nest.description": "Verplaats een bije<PERSON><PERSON> of <PERSON>korf met drie bijen erin door gebruik te maken van zijden streling", "advancements.husbandry.silk_touch_nest.title": "De beste locatie is een bij<PERSON>ak", "advancements.husbandry.tactical_fishing.description": "Vang een vis... zonder een hengel!", "advancements.husbandry.tactical_fishing.title": "Tactisch vissen", "advancements.husbandry.tadpole_in_a_bucket.description": "Vang een kik<PERSON><PERSON> met een emmer", "advancements.husbandry.tadpole_in_a_bucket.title": "Als een kikker op een kluitje", "advancements.husbandry.tame_an_animal.description": "Tem een dier", "advancements.husbandry.tame_an_animal.title": "Beste vrienden voor altijd", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON> was van een koperblok!", "advancements.husbandry.wax_off.title": "Wat was was eer was was was?", "advancements.husbandry.wax_on.description": "Was met honingraat een koperen blok in!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "Tem elk van de wolvenvarianten", "advancements.husbandry.whole_pack.title": "De roedel compleet", "advancements.nether.all_effects.description": "Heb elk effect op hetzelfde moment", "advancements.nether.all_effects.title": "Bestaan er grenzen?", "advancements.nether.all_potions.description": "Heb elk drankeffect op hetzelfde moment", "advancements.nether.all_potions.title": "<PERSON>en wilde cocktail", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> een drank", "advancements.nether.brew_potion.title": "Plaatselijke brouwerij", "advancements.nether.charge_respawn_anchor.description": "Laad een respawnanker op tot het maximum", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> echt \"negen\" levens", "advancements.nether.create_beacon.description": "Bouw en plaats een baken", "advancements.nether.create_beacon.title": "Breng het baken naar huis", "advancements.nether.create_full_beacon.description": "Breng een baken op volle kracht", "advancements.nether.create_full_beacon.title": "Bakenmaker", "advancements.nether.distract_piglin.description": "<PERSON><PERSON> piglins af met goud", "advancements.nether.distract_piglin.title": "Glimmend spul", "advancements.nether.explore_nether.description": "Ontdek alle Netherklimaten", "advancements.nether.explore_nether.title": "Snikhete bestemmingen", "advancements.nether.fast_travel.description": "Gebruik de Nether om zeven kilometer in de bovenwereld te reizen", "advancements.nether.fast_travel.title": "Tussenruimtebubbel", "advancements.nether.find_bastion.description": "Betreed een bolwerkruïne", "advancements.nether.find_bastion.title": "Dat waren de tijden", "advancements.nether.find_fortress.description": "Breek in bij een Netherfort", "advancements.nether.find_fortress.title": "Een verschrikkelijk fort", "advancements.nether.get_wither_skull.description": "Verkrijg een Witherskelettenschedel", "advancements.nether.get_wither_skull.title": "Eng, grie<PERSON>ig skelet", "advancements.nether.loot_bastion.description": "Plunder een kist in een bolwerkruïne", "advancements.nether.loot_bastion.title": "Oorlogsvarkens", "advancements.nether.netherite_armor.description": "Verkrijg een volledig Netherieten harnas", "advancements.nether.netherite_armor.title": "Bedek me met puin", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON>k<PERSON><PERSON><PERSON> oud puin", "advancements.nether.obtain_ancient_debris.title": "Verborgen in de dieptes", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON><PERSON> een <PERSON> van z'n staf", "advancements.nether.obtain_blaze_rod.title": "Door het vuur", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> huilend obsidiaan", "advancements.nether.obtain_crying_obsidian.title": "Wie snijdt er uien?", "advancements.nether.return_to_sender.description": "Vern<PERSON><PERSON> een <PERSON> met een vuurbal", "advancements.nether.return_to_sender.title": "Retour afzender", "advancements.nether.ride_strider.description": "<PERSON><PERSON><PERSON><PERSON> een <PERSON><PERSON> met een spookachtige zwam aan een stok", "advancements.nether.ride_strider.title": "Een lopende boot?!", "advancements.nether.ride_strider_in_overworld_lava.description": "Maak een laaaange lavawandeling op een lavarijder in de bovenwereld", "advancements.nether.ride_strider_in_overworld_lava.title": "Net als thuis", "advancements.nether.root.description": "<PERSON>reng zomerkleren mee", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON> op", "advancements.nether.summon_wither.title": "De vliegende Wither", "advancements.nether.uneasy_alliance.description": "Red een <PERSON><PERSON> uit de <PERSON>, breng het veilig naar de bovenwereld... en dood het", "advancements.nether.uneasy_alliance.title": "Ongemakkelijke alliantie", "advancements.nether.use_lodestone.description": "Gebruik een kompas op een zeilsteen", "advancements.nether.use_lodestone.title": "Op eigen kompas zeilen", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Verzwak en genees een zombiedorpeling", "advancements.story.cure_zombie_villager.title": "Zombiedokter", "advancements.story.deflect_arrow.description": "Kaats een projectiel af met een schild", "advancements.story.deflect_arrow.title": "<PERSON><PERSON>, bedan<PERSON>", "advancements.story.enchant_item.description": "Betover een voorwerp op een betoveringstafel", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Ga door een Endportaal", "advancements.story.enter_the_end.title": "Het einde?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON>, activeer en ga door een Netherportaal", "advancements.story.enter_the_nether.title": "We moeten dieper", "advancements.story.follow_ender_eye.description": "Volg een Enderoog", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON> oog", "advancements.story.form_obsidian.description": "Verkrijg obsid<PERSON>", "advancements.story.form_obsidian.title": "IJsemmeruitdaging", "advancements.story.iron_tools.description": "Upgrade je houweel", "advancements.story.iron_tools.title": "<PERSON><PERSON><PERSON> met handen smeden", "advancements.story.lava_bucket.description": "Vul een emmer met lava", "advancements.story.lava_bucket.title": "Heet spul", "advancements.story.mine_diamond.description": "<PERSON>erk<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "Diamanten!", "advancements.story.mine_stone.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> met je ni<PERSON><PERSON> ho<PERSON><PERSON>", "advancements.story.mine_stone.title": "Steentijd", "advancements.story.obtain_armor.description": "<PERSON><PERSON><PERSON> j<PERSON> met een stuk ij<PERSON>en harnas", "advancements.story.obtain_armor.title": "In pak", "advancements.story.root.description": "Het hart en verha<PERSON> van het spel", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON><PERSON><PERSON> harnas redt levens", "advancements.story.shiny_gear.title": "Bedek me met diamanten", "advancements.story.smelt_iron.description": "<PERSON>meed een i<PERSON>", "advancements.story.smelt_iron.title": "IJzerwaren verkrijgen", "advancements.story.upgrade_tools.description": "Maak een betere houweel", "advancements.story.upgrade_tools.title": "Upgraden", "advancements.toast.challenge": "Uitdaging voltooid!", "advancements.toast.goal": "<PERSON><PERSON> bere<PERSON>t!", "advancements.toast.task": "Vooruitgang geboekt!", "argument.anchor.invalid": "Entiteitsankerpositie '%s' is ongeldig", "argument.angle.incomplete": "Incompleet (één <PERSON>at verwacht)", "argument.angle.invalid": "Ongeldige hoek", "argument.block.id.invalid": "Bloktype '%s' is onbekend", "argument.block.property.duplicate": "Eigenschap '%s' mag maar één keer voorkomen op blok '%s'", "argument.block.property.invalid": "Blok '%s' accepteert '%s' voor eigenschap '%s' niet", "argument.block.property.novalue": "Waarde voor eigenschap '%s' van blok '%s' verwacht", "argument.block.property.unclosed": "Sluitende ] verwacht voor blokstatuseigenschappen", "argument.block.property.unknown": "Blok '%s' heeft eigenschap '%s' niet", "argument.block.tag.disallowed": "Eigenschappen zijn niet <PERSON>, alleen blokken", "argument.color.invalid": "Kleur '%s' is onbekend", "argument.component.invalid": "Chatcomponent '%s' is ongeldig", "argument.criteria.invalid": "Criterium '%s' is onbekend", "argument.dimension.invalid": "Dimensie '%s' is onbekend", "argument.double.big": "Dubbeleprecisiegetal mag niet hoger zijn dan %s, %s gevonden", "argument.double.low": "Dubbeleprecisiegetal mag niet lager zijn dan %s, %s gevonden", "argument.entity.invalid": "Ongeldige naam of ongeldig UUID", "argument.entity.notfound.entity": "<PERSON>n entiteit gevonden", "argument.entity.notfound.player": "<PERSON><PERSON> speler gevonden", "argument.entity.options.advancements.description": "<PERSON><PERSON><PERSON> met vooruitgangen", "argument.entity.options.distance.description": "Afstand tot entiteit", "argument.entity.options.distance.negative": "Afstand mag niet negatief zijn", "argument.entity.options.dx.description": "Entiteiten tussen x en x + dx", "argument.entity.options.dy.description": "Entiteiten tussen y en y + dy", "argument.entity.options.dz.description": "Entiteiten tussen z en z + dz", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON> met s<PERSON><PERSON><PERSON>", "argument.entity.options.inapplicable": "<PERSON><PERSON> '%s' is hier niet van toepassing", "argument.entity.options.level.description": "Ervaringsniveau", "argument.entity.options.level.negative": "Niveau mag niet negatief zijn", "argument.entity.options.limit.description": "Maximumaantal entiteiten te retourneren", "argument.entity.options.limit.toosmall": "Limiet moet minstens 1 zijn", "argument.entity.options.mode.invalid": "Spelmodus '%s' is ongeldig of onbekend", "argument.entity.options.name.description": "Entiteitsnaam", "argument.entity.options.nbt.description": "Entiteiten met NBT", "argument.entity.options.predicate.description": "Aangepast predicaat", "argument.entity.options.scores.description": "Entiteiten met scores", "argument.entity.options.sort.description": "Entiteiten sorteren", "argument.entity.options.sort.irreversible": "Sorteertype '%s' is ongeldig of onbekend", "argument.entity.options.tag.description": "Entiteiten met eigenschap", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON><PERSON> in team", "argument.entity.options.type.description": "Entiteiten van type", "argument.entity.options.type.invalid": "Entiteitstype '%s' is ongeldig of onbekend", "argument.entity.options.unknown": "<PERSON>tie '%s' is onbekend", "argument.entity.options.unterminated": "Einde van opties verwacht", "argument.entity.options.valueless": "Waarde verwacht voor optie '%s'", "argument.entity.options.x.description": "x-positie", "argument.entity.options.x_rotation.description": "x-rotatie van en<PERSON>", "argument.entity.options.y.description": "y-positie", "argument.entity.options.y_rotation.description": "y-rotatie van en<PERSON>", "argument.entity.options.z.description": "z-positie", "argument.entity.selector.allEntities": "Alle entiteiten", "argument.entity.selector.allPlayers": "Alle spelers", "argument.entity.selector.missing": "Ontbrekend selecteerdertype", "argument.entity.selector.nearestEntity": "Dichtsbijzijnde entiteit", "argument.entity.selector.nearestPlayer": "Dichtstbijzijnde speler", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON> niet <PERSON>", "argument.entity.selector.randomPlayer": "Willek<PERSON><PERSON> speler", "argument.entity.selector.self": "Huidige entiteit", "argument.entity.selector.unknown": "Selecteerdertype '%s' is onbekend", "argument.entity.toomany": "<PERSON><PERSON> entiteit is toe<PERSON><PERSON><PERSON>, maar de gebruikte selecteerder staat meer dan <PERSON> toe", "argument.enum.invalid": "Waarde '%s' ongeldig", "argument.float.big": "Zwevendekommagetal mag niet hoger zijn dan %s, %s gevonden", "argument.float.low": "Zwevendekommagetal mag niet lager zijn dan %s, %s gevonden", "argument.gamemode.invalid": "Onbekende spelmodus: %s", "argument.hexcolor.invalid": "Ongeldige hexadecimale kleurcode '%s'", "argument.id.invalid": "Ongeldig ID", "argument.id.unknown": "ID '%s' is onbekend", "argument.integer.big": "<PERSON><PERSON><PERSON> getal mag niet hoger zijn dan %s, %s gevonden", "argument.integer.low": "<PERSON><PERSON><PERSON> getal mag niet lager zijn dan %s, %s gevonden", "argument.item.id.invalid": "Voorwerp '%s' is onbekend", "argument.item.tag.disallowed": "Eigenschappen zijn niet toe<PERSON>, alleen voorwerpen", "argument.literal.incorrect": "Literaal %s verwacht", "argument.long.big": "<PERSON> geheel getal mag niet hoger zijn dan %s, %s gevonden", "argument.long.low": "Lang geheel getal mag niet lager zijn dan %s, %s gevonden", "argument.message.too_long": "Chatbericht was te lang (%s tekens, meer dan de limiet van %s)", "argument.nbt.array.invalid": "Lijsttype '%s' is ongeldig", "argument.nbt.array.mixed": "'%s' toevoegen aan '%s' mislukt", "argument.nbt.expected.compound": "Verwachtte samengestelde eigenschap", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON><PERSON> verwacht", "argument.nbt.expected.value": "Waarde verwacht", "argument.nbt.list.mixed": "'%s' toevoegen aan lijst '%s' mislukt", "argument.nbt.trailing": "Onverwachte extra gegevens", "argument.player.entities": "Alleen spelers mogen beïnvloed worden door deze op<PERSON>cht, maar de gebruikte selecteerder omvat entiteiten", "argument.player.toomany": "<PERSON><PERSON> speler is toe<PERSON><PERSON><PERSON>, maar de gebruikte selecteerder staat meer dan <PERSON><PERSON> toe", "argument.player.unknown": "<PERSON><PERSON> speler bestaat niet", "argument.pos.missing.double": "Coördinaat verwacht", "argument.pos.missing.int": "Blokpositie verwacht", "argument.pos.mixed": "Wereldcoördinaten en lokale coördinaten kunnen niet gemixt worden (alles moet óf ^ gebruiken óf niet)", "argument.pos.outofbounds": "Die positie is buiten de toegestane grenzen.", "argument.pos.outofworld": "Die positie bevindt zich buiten de wereld!", "argument.pos.unloaded": "Die positie is niet geladen", "argument.pos2d.incomplete": "Incompleet (twee coördinaten verwacht)", "argument.pos3d.incomplete": "Incompleet (drie coördinaten verwacht)", "argument.range.empty": "<PERSON>aar<PERSON> of waardebereik verwacht", "argument.range.ints": "<PERSON><PERSON> gehele getallen zijn <PERSON>, decimale getallen niet", "argument.range.swapped": "Minimum mag niet groter zijn dan het maximum", "argument.resource.invalid_type": "Verkeerd type '%2$s' voor element '%1$s' ('%3$s' verwacht)", "argument.resource.not_found": "Element '%s' met type '%s' niet gevonden", "argument.resource_or_id.failed_to_parse": "Constructie '%s' verwerken mislukt", "argument.resource_or_id.invalid": "Ongeldig ID of ongeldige eigenschap", "argument.resource_or_id.no_such_element": "Element '%s' niet gevonden in register '%s'", "argument.resource_selector.not_found": "<PERSON><PERSON>ten voor selecteerder '%s' van type '%s'", "argument.resource_tag.invalid_type": "Verkeerd type '%2$s' voor eigenschap '%1$s' ('%3$s' verwacht)", "argument.resource_tag.not_found": "Eigenschap '%s' met type '%s' niet gevonden", "argument.rotation.incomplete": "Incompleet (twee coördinaten verwacht)", "argument.scoreHolder.empty": "<PERSON><PERSON> van relevante scorehouders mislukt", "argument.scoreboardDisplaySlot.invalid": "Weergavevak '%s' is onbekend", "argument.style.invalid": "Ongeldige stijl: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON>aan<PERSON> moet niet-negatief zijn", "argument.time.invalid_unit": "Ongeldige eenheid", "argument.time.tick_count_too_low": "Aantal ticks mag niet lager zijn dan %s, %s gevonden", "argument.uuid.invalid": "Ongeldig UUID", "argument.waypoint.invalid": "Geselecteerde entiteit is geen wegwi<PERSON>zer", "arguments.block.tag.unknown": "Blokeigenschap '%s' is onbekend", "arguments.function.tag.unknown": "Functie-eigenschap '%s' is onbekend", "arguments.function.unknown": "Functie '%s' is onbekend", "arguments.item.component.expected": "Verwachtte voorwerpcomponent", "arguments.item.component.malformed": "Component '%s' is ongeldig: %s", "arguments.item.component.repeated": "Voorwerpcomponent '%s' werd herhaald, maar slechts één waarde kan gebruikt worden", "arguments.item.component.unknown": "Voorwerpcomponent '%s' is onbekend", "arguments.item.malformed": "Voorwerp is ongelding: %s", "arguments.item.overstacked": "'%s' kan gestapeld worden tot maximaal %s", "arguments.item.predicate.malformed": "Predicaat '%s' is ongeldig: %s", "arguments.item.predicate.unknown": "Voorwerppredicaat '%s' is onbekend", "arguments.item.tag.unknown": "Voorwerpeigenschap '%s' is onbekend", "arguments.nbtpath.node.invalid": "Ongeldig NBT-padelement", "arguments.nbtpath.nothing_found": "Geen elementen gevonden die overeenkomen met %s", "arguments.nbtpath.too_deep": "Resulterende NBT heeft te veel diepteniveaus", "arguments.nbtpath.too_large": "Resulterende NBT te groot", "arguments.objective.notFound": "Scoreborddoel '%s' is onbekend", "arguments.objective.readonly": "Scoreborddoel '%s' is alleen-lezen", "arguments.operation.div0": "Kan niet delen door nul", "arguments.operation.invalid": "Ongeldige bewerking", "arguments.swizzle.invalid": "Ongeldige ascombinatie; een combinatie van 'x', 'y' en 'z' werd verwacht", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "aanvalsschade", "attribute.name.attack_knockback": "Aanvalsterugslag", "attribute.name.attack_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "Blokbreeksnelheid", "attribute.name.block_interaction_range": "Blokinteractiebereik", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "camera-afstand", "attribute.name.entity_interaction_range": "Entiteitinteractiebereik", "attribute.name.explosion_knockback_resistance": "explosieterugslagsweerstand", "attribute.name.fall_damage_multiplier": "Valschadevermenigvuldiger", "attribute.name.flying_speed": "Vliegsnelheid", "attribute.name.follow_range": "Volgbereik wezens", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "aanvalsschade", "attribute.name.generic.attack_knockback": "Aanvalsterugslag", "attribute.name.generic.attack_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.block_interaction_range": "Blokinteractiebereik", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Entiteitinteractiebereik", "attribute.name.generic.explosion_knockback_resistance": "explosieterugslagsweerstand", "attribute.name.generic.fall_damage_multiplier": "Valschadevermenigvuldiger", "attribute.name.generic.flying_speed": "Vliegsnelheid", "attribute.name.generic.follow_range": "Volgbereik wezens", "attribute.name.generic.gravity": "Zwaartekracht", "attribute.name.generic.jump_strength": "Springkracht", "attribute.name.generic.knockback_resistance": "terugslagweerstand", "attribute.name.generic.luck": "Geluk", "attribute.name.generic.max_absorption": "Maximumabsorptie", "attribute.name.generic.max_health": "Maximale gezondheid", "attribute.name.generic.movement_efficiency": "beweging<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "Veilige valafstand", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Staphoogte", "attribute.name.generic.water_movement_efficiency": "waterbewegingsefficiëntie", "attribute.name.gravity": "Zwaartekracht", "attribute.name.horse.jump_strength": "Sterkte paardensprong", "attribute.name.jump_strength": "Springkracht", "attribute.name.knockback_resistance": "terugslagsweerstand", "attribute.name.luck": "Geluk", "attribute.name.max_absorption": "Maximumabsorptie", "attribute.name.max_health": "Maximale gezondheid", "attribute.name.mining_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.movement_efficiency": "beweging<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blokbreeksnelheid", "attribute.name.player.block_interaction_range": "Blokinteractiebereik", "attribute.name.player.entity_interaction_range": "Entiteitinteractiebereik", "attribute.name.player.mining_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "slu<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.submerged_mining_speed": "mi<PERSON><PERSON><PERSON><PERSON><PERSON> onderwater", "attribute.name.player.sweeping_damage_ratio": "verscherpingsschaderatio", "attribute.name.safe_fall_distance": "Veilige valafstand", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "slu<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.spawn_reinforcements": "Zombieversterkingen", "attribute.name.step_height": "Staphoogte", "attribute.name.submerged_mining_speed": "mi<PERSON><PERSON><PERSON><PERSON><PERSON> onderwater", "attribute.name.sweeping_damage_ratio": "verscherpingsschaderatio", "attribute.name.tempt_range": "Wezenlokbereik", "attribute.name.water_movement_efficiency": "waterbewegingsefficiëntie", "attribute.name.waypoint_receive_range": "wegwijzerontvangstbereik", "attribute.name.waypoint_transmit_range": "wegwijzeruitzendbereik", "attribute.name.zombie.spawn_reinforcements": "Zombieversterkingen", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "Bamboe-oerwoud", "biome.minecraft.basalt_deltas": "Ba<PERSON><PERSON><PERSON><PERSON>'s", "biome.minecraft.beach": "Strand", "biome.minecraft.birch_forest": "Berkenbos", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON> bos", "biome.minecraft.dark_forest": "<PERSON><PERSON> bos", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON> die<PERSON> o<PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON><PERSON> die<PERSON> o<PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON> die<PERSON> o<PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_barrens": "De End - verlaten eiland", "biome.minecraft.end_highlands": "<PERSON> hoogland<PERSON>", "biome.minecraft.end_midlands": "De End - midlanden", "biome.minecraft.eroded_badlands": "Geërodeerde badlands", "biome.minecraft.flower_forest": "Bloemenbos", "biome.minecraft.forest": "<PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON><PERSON> riv<PERSON>", "biome.minecraft.grove": "Bosje", "biome.minecraft.ice_spikes": "IJspijlers", "biome.minecraft.jagged_peaks": "Scherpe pieken", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON>", "biome.minecraft.lush_caves": "Weelderige grotten", "biome.minecraft.mangrove_swamp": "Mangrovemoeras", "biome.minecraft.meadow": "<PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Paddenstoelenvelden", "biome.minecraft.nether_wastes": "Netherwoestenij", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Oerberkenbos", "biome.minecraft.old_growth_pine_taiga": "Oernaaldbomentaiga", "biome.minecraft.old_growth_spruce_taiga": "Oersparrentaiga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON> tuin", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "De End - <PERSON>agland<PERSON>", "biome.minecraft.snowy_beach": "Besneeuwd strand", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> velden", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pistes", "biome.minecraft.snowy_taiga": "Besneeuwde taiga", "biome.minecraft.soul_sand_valley": "Zielenzandvallei", "biome.minecraft.sparse_jungle": "Dunbegroeid o<PERSON>woud", "biome.minecraft.stony_peaks": "Steenpieken", "biome.minecraft.stony_shore": "Steenkust", "biome.minecraft.sunflower_plains": "Zonnebloemvelden", "biome.minecraft.swamp": "Moeras", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "De End", "biome.minecraft.the_void": "De leegte", "biome.minecraft.warm_ocean": "<PERSON><PERSON> o<PERSON>an", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON> bos", "biome.minecraft.windswept_forest": "Onst<PERSON><PERSON><PERSON> bos", "biome.minecraft.windswept_gravelly_hills": "Onst<PERSON>mi<PERSON> grindh<PERSON>s", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON> savanne", "biome.minecraft.wooded_badlands": "Beboste badlands", "block.minecraft.acacia_button": "Acaciahouten knop", "block.minecraft.acacia_door": "Acaciahouten deur", "block.minecraft.acacia_fence": "Acaciahouten hek", "block.minecraft.acacia_fence_gate": "Acaciahouten poort", "block.minecraft.acacia_hanging_sign": "Acaciahouten hangbord", "block.minecraft.acacia_leaves": "Acaciabladeren", "block.minecraft.acacia_log": "Acaciastam", "block.minecraft.acacia_planks": "Acaciahouten planken", "block.minecraft.acacia_pressure_plate": "Acaciahouten drukplaat", "block.minecraft.acacia_sapling": "A<PERSON>za<PERSON>", "block.minecraft.acacia_sign": "Acaciahouten bord", "block.minecraft.acacia_slab": "Acaciahouten plaat", "block.minecraft.acacia_stairs": "Acaciahouten trap", "block.minecraft.acacia_trapdoor": "Acaciahouten valluik", "block.minecraft.acacia_wall_hanging_sign": "Acaciahouten uithangbord", "block.minecraft.acacia_wall_sign": "Acaciahouten bord aan muur", "block.minecraft.acacia_wood": "Acaciahout", "block.minecraft.activator_rail": "Activatiespoor", "block.minecraft.air": "<PERSON><PERSON>", "block.minecraft.allium": "Look", "block.minecraft.amethyst_block": "Amethistblok", "block.minecraft.amethyst_cluster": "Amethistcluster", "block.minecraft.ancient_debris": "Oud puin", "block.minecraft.andesite": "Andesiet", "block.minecraft.andesite_slab": "Andesietplaat", "block.minecraft.andesite_stairs": "Andesiettrap", "block.minecraft.andesite_wall": "Andesietmuur", "block.minecraft.anvil": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Vaste meloenstam", "block.minecraft.attached_pumpkin_stem": "Vaste pompo<PERSON>tam", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Azaleabladeren", "block.minecraft.azure_bluet": "Porseleinsterretje", "block.minecraft.bamboo": "Bamboe", "block.minecraft.bamboo_block": "Bamboeblok", "block.minecraft.bamboo_button": "Bamboeknop", "block.minecraft.bamboo_door": "Bamboedeur", "block.minecraft.bamboo_fence": "Bamboehek", "block.minecraft.bamboo_fence_gate": "Bamboepoort", "block.minecraft.bamboo_hanging_sign": "Bamboehangbord", "block.minecraft.bamboo_mosaic": "Bambo<PERSON>oz<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic_slab": "Bamboemozaïekplaat", "block.minecraft.bamboo_mosaic_stairs": "Bamboemozaïektrap", "block.minecraft.bamboo_planks": "Bamboeplanken", "block.minecraft.bamboo_pressure_plate": "Bamboedrukplaat", "block.minecraft.bamboo_sapling": "Bamboescheut", "block.minecraft.bamboo_sign": "Bamboebord", "block.minecraft.bamboo_slab": "Bamboeplaat", "block.minecraft.bamboo_stairs": "Bamboetrap", "block.minecraft.bamboo_trapdoor": "Bamboevalluik", "block.minecraft.bamboo_wall_hanging_sign": "Bamboe-uithangbord", "block.minecraft.bamboo_wall_sign": "Bamboebord aan muur", "block.minecraft.banner.base.black": "<PERSON><PERSON> veld", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON> veld", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON> veld", "block.minecraft.banner.base.cyan": "Turquoise veld", "block.minecraft.banner.base.gray": "<PERSON><PERSON><PERSON><PERSON> veld", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON> veld", "block.minecraft.banner.base.light_blue": "Lichtblauw veld", "block.minecraft.banner.base.light_gray": "Lichtgrijs veld", "block.minecraft.banner.base.lime": "Lichtgroen veld", "block.minecraft.banner.base.magenta": "Magenta veld", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON> veld", "block.minecraft.banner.base.pink": "Roze veld", "block.minecraft.banner.base.purple": "<PERSON><PERSON> veld", "block.minecraft.banner.base.red": "Rood veld", "block.minecraft.banner.base.white": "Wit veld", "block.minecraft.banner.base.yellow": "<PERSON><PERSON> veld", "block.minecraft.banner.border.black": "Zwarte zoom", "block.minecraft.banner.border.blue": "Blauwe zoom", "block.minecraft.banner.border.brown": "Bruine zoom", "block.minecraft.banner.border.cyan": "Turquoise zoom", "block.minecraft.banner.border.gray": "Grijze zoom", "block.minecraft.banner.border.green": "Groene zoom", "block.minecraft.banner.border.light_blue": "Lichtblauwe zoom", "block.minecraft.banner.border.light_gray": "Lichtgrijze zoom", "block.minecraft.banner.border.lime": "Lichtgroene zoom", "block.minecraft.banner.border.magenta": "Magenta zoom", "block.minecraft.banner.border.orange": "Oranje zoom", "block.minecraft.banner.border.pink": "Roze zoom", "block.minecraft.banner.border.purple": "Paarse zoom", "block.minecraft.banner.border.red": "Rode zoom", "block.minecraft.banner.border.white": "Witte zoom", "block.minecraft.banner.border.yellow": "Gele zoom", "block.minecraft.banner.bricks.black": "Zwart metselwerk", "block.minecraft.banner.bricks.blue": "Blauw metselwerk", "block.minecraft.banner.bricks.brown": "Bruin metselwerk", "block.minecraft.banner.bricks.cyan": "Turquoise metselwerk", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.green": "Groen metselwerk", "block.minecraft.banner.bricks.light_blue": "Lichtblauw metselwerk", "block.minecraft.banner.bricks.light_gray": "Lichtgrijs metselwerk", "block.minecraft.banner.bricks.lime": "Lichtgroen metselwerk", "block.minecraft.banner.bricks.magenta": "Magenta metselwerk", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON>je <PERSON>sel<PERSON>", "block.minecraft.banner.bricks.pink": "Roze metselwerk", "block.minecraft.banner.bricks.purple": "Paars metselwerk", "block.minecraft.banner.bricks.red": "<PERSON>ood metselwerk", "block.minecraft.banner.bricks.white": "Wit metselwerk", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON>sel<PERSON>", "block.minecraft.banner.circle.black": "Zwarte koek", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.circle.cyan": "Turquoise koek", "block.minecraft.banner.circle.gray": "G<PERSON><PERSON><PERSON> k<PERSON>k", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.circle.light_blue": "Lichtblauwe koek", "block.minecraft.banner.circle.light_gray": "Lichtgrijze koek", "block.minecraft.banner.circle.lime": "Lichtgroene koek", "block.minecraft.banner.circle.magenta": "Ma<PERSON>a koek", "block.minecraft.banner.circle.orange": "Or<PERSON>je k<PERSON>", "block.minecraft.banner.circle.pink": "Roze koek", "block.minecraft.banner.circle.purple": "Pa<PERSON>e koek", "block.minecraft.banner.circle.red": "Rode k<PERSON>k", "block.minecraft.banner.circle.white": "<PERSON><PERSON> koek", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Turquoise Creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "Lichtblauwe Creeper", "block.minecraft.banner.creeper.light_gray": "Lichtgrijze Creeper", "block.minecraft.banner.creeper.lime": "Lichtgroene Creeper", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON>", "block.minecraft.banner.cross.black": "<PERSON><PERSON>", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.cross.cyan": "Turquoise andrea<PERSON>is", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "Lichtblauw andreaskruis", "block.minecraft.banner.cross.light_gray": "Lichtgri<PERSON><PERSON>", "block.minecraft.banner.cross.lime": "Lichtgroen andreaskruis", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON> and<PERSON>", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.pink": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.cross.purple": "<PERSON><PERSON> <PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON>", "block.minecraft.banner.cross.white": "Wit andreask<PERSON>", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.black": "Zwarte ingesprongen zoom", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> ing<PERSON> zoom", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> ingesp<PERSON>en zoom", "block.minecraft.banner.curly_border.cyan": "Turquoise ingesprongen zoom", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON> ingesprongen zoom", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> ingesprongen zoom", "block.minecraft.banner.curly_border.light_blue": "Lichtblauwe ingesprongen zoom", "block.minecraft.banner.curly_border.light_gray": "Lichtgrijze ingesprongen zoom", "block.minecraft.banner.curly_border.lime": "Lichtgroene ingesprongen zoom", "block.minecraft.banner.curly_border.magenta": "Magenta ingesprongen zoom", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON> zoom", "block.minecraft.banner.curly_border.pink": "Roze ingesprongen zoom", "block.minecraft.banner.curly_border.purple": "Pa<PERSON>e ingesprongen zoom", "block.minecraft.banner.curly_border.red": "<PERSON>e ingesprongen zoom", "block.minecraft.banner.curly_border.white": "Witte ingesprongen zoom", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> ing<PERSON>en zoom", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON> schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON> schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON>in schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.cyan": "Turquoise schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.light_blue": "Lichtblauw schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.light_gray": "Lichtgrijs schu<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_left.lime": "Lichtgroen schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.magenta": "Magenta schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON> schu<PERSON> verdeeld", "block.minecraft.banner.diagonal_left.pink": "Roze schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON> schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.red": "Rood schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.white": "Wit schuinlinks verdeeld", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON> schuinlinks verdeeld", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON> schu<PERSON> verdeeld", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON> schu<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON> schu<PERSON><PERSON>s verdeeld", "block.minecraft.banner.diagonal_right.cyan": "Turquoise schuinrechts verdeeld", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> ve<PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON> schu<PERSON>s verdeeld", "block.minecraft.banner.diagonal_right.light_blue": "Lichtblauw schuinrechts verdeeld", "block.minecraft.banner.diagonal_right.light_gray": "Lichtgrijs s<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_right.lime": "Lichtgroen schuinrechts verdeeld", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON> schu<PERSON>s verdeeld", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON> s<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON>oze schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON> schu<PERSON>s verdeeld", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON> schu<PERSON>s verde<PERSON>", "block.minecraft.banner.diagonal_right.white": "Wit schuinrechts verdeeld", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON> s<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Zwart omgekeerd schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_up_left.blue": "Blauw omgekeerd schu<PERSON>recht<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_left.brown": "Bruin omgekeerd schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_up_left.cyan": "Turquoise omgekeerd schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON><PERSON> om<PERSON><PERSON><PERSON> s<PERSON> verde<PERSON>", "block.minecraft.banner.diagonal_up_left.green": "Groen omgekeerd schu<PERSON>recht<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_left.light_blue": "Lichtblauw omgekeerd schuinrechts verdeeld", "block.minecraft.banner.diagonal_up_left.light_gray": "Lichtgrijs omgekeerd schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_up_left.lime": "Lichtgroen omgekeerd schuinrechts verdeeld", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta omgekeerd schu<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_left.orange": "Oranje omgekeerd schu<PERSON>recht<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_left.pink": "Roze omgekeerd schu<PERSON>recht<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_left.purple": "Paars omgekeerd schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON> omge<PERSON><PERSON> s<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_left.white": "Wit omgekeerd schu<PERSON>rechts verdeeld", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> verdeeld", "block.minecraft.banner.diagonal_up_right.black": "Zwart omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.blue": "Blauw omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.brown": "Bruin omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.cyan": "Turquoise omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON><PERSON> om<PERSON><PERSON><PERSON> schu<PERSON>s verde<PERSON>", "block.minecraft.banner.diagonal_up_right.green": "Groen omgekeerd schu<PERSON>links verdeeld", "block.minecraft.banner.diagonal_up_right.light_blue": "Lichtblauw omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.light_gray": "Lichtgrijs omgekeerd schu<PERSON>links verdeeld", "block.minecraft.banner.diagonal_up_right.lime": "Lichtgroen omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.orange": "Oranje omgekeerd schu<PERSON>links verdeeld", "block.minecraft.banner.diagonal_up_right.pink": "Roze omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.purple": "Paars omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.red": "Rood omge<PERSON><PERSON> schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.white": "Wit omgekeerd schuinlinks verdeeld", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON> om<PERSON><PERSON><PERSON> schu<PERSON>links verdeeld", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> windstroming", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON> windstroming", "block.minecraft.banner.flow.brown": "Bruine windstroming", "block.minecraft.banner.flow.cyan": "Turquoise windstroming", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON><PERSON> windstroming", "block.minecraft.banner.flow.green": "Groene windstroming", "block.minecraft.banner.flow.light_blue": "Lichtblauwe windstroming", "block.minecraft.banner.flow.light_gray": "Lichtgrijze windstroming", "block.minecraft.banner.flow.lime": "Lichtgroene windstroming", "block.minecraft.banner.flow.magenta": "Magenta windstroming", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON> windstroming", "block.minecraft.banner.flow.pink": "Roze windstroming", "block.minecraft.banner.flow.purple": "Paarse windstroming", "block.minecraft.banner.flow.red": "<PERSON><PERSON> windstroming", "block.minecraft.banner.flow.white": "Witte windstroming", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> windstroming", "block.minecraft.banner.flower.black": "Zwarte bloem", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON> bloem", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON> bloem", "block.minecraft.banner.flower.cyan": "Turquoise bloem", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON><PERSON> bloem", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON> bloem", "block.minecraft.banner.flower.light_blue": "Lichtblauwe bloem", "block.minecraft.banner.flower.light_gray": "Lichtgrijze bloem", "block.minecraft.banner.flower.lime": "Lichtgroene bloem", "block.minecraft.banner.flower.magenta": "Magenta bloem", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON> bloem", "block.minecraft.banner.flower.pink": "Roze bloem", "block.minecraft.banner.flower.purple": "<PERSON><PERSON><PERSON> bloem", "block.minecraft.banner.flower.red": "<PERSON><PERSON> bloem", "block.minecraft.banner.flower.white": "<PERSON><PERSON> bloem", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> bloem", "block.minecraft.banner.globe.black": "Zwarte globe", "block.minecraft.banner.globe.blue": "Blauwe globe", "block.minecraft.banner.globe.brown": "Bruine globe", "block.minecraft.banner.globe.cyan": "Turquoise globe", "block.minecraft.banner.globe.gray": "Grijze globe", "block.minecraft.banner.globe.green": "Groene globe", "block.minecraft.banner.globe.light_blue": "Lichtblauwe globe", "block.minecraft.banner.globe.light_gray": "Lichtgrijze globe", "block.minecraft.banner.globe.lime": "Lichtgroene globe", "block.minecraft.banner.globe.magenta": "Magenta globe", "block.minecraft.banner.globe.orange": "<PERSON><PERSON>je globe", "block.minecraft.banner.globe.pink": "Roze globe", "block.minecraft.banner.globe.purple": "Paarse globe", "block.minecraft.banner.globe.red": "<PERSON><PERSON> globe", "block.minecraft.banner.globe.white": "Witte globe", "block.minecraft.banner.globe.yellow": "Gele globe", "block.minecraft.banner.gradient.black": "<PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Turquoise gradiënt", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Lichtblauw gradiënt", "block.minecraft.banner.gradient.light_gray": "Lichtgrij<PERSON> g<PERSON>", "block.minecraft.banner.gradient.lime": "Lichtgroen gradiënt", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON> grad<PERSON>", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON> grad<PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON> grad<PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.gradient.white": "Wit gradiënt", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.cyan": "Turquoise schild<PERSON><PERSON><PERSON>nt", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.light_blue": "Lichtblauw schildvoetgradiënt", "block.minecraft.banner.gradient_up.light_gray": "Lichtgri<PERSON><PERSON>", "block.minecraft.banner.gradient_up.lime": "Lichtgroen schildvoetgradiënt", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.white": "Wit schild<PERSON>etgradiënt", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON> ruk<PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.guster.cyan": "Turquoise rukwind", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON> ruk<PERSON>", "block.minecraft.banner.guster.light_blue": "Lichtblauwe rukwind", "block.minecraft.banner.guster.light_gray": "Lichtgrijze rukwind", "block.minecraft.banner.guster.lime": "Lichtgroene rukwind", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON> ruk<PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON>oze rukwind", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON> ruk<PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON> rukwind", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.half_horizontal.black": "Zwartgebalkt", "block.minecraft.banner.half_horizontal.blue": "Blauwgebalkt", "block.minecraft.banner.half_horizontal.brown": "Bruingebalkt", "block.minecraft.banner.half_horizontal.cyan": "Turquoisegebalkt", "block.minecraft.banner.half_horizontal.gray": "Grijsgebalkt", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON>eb<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Lichtblauwgebalkt", "block.minecraft.banner.half_horizontal.light_gray": "Lichtgrijsgebalkt", "block.minecraft.banner.half_horizontal.lime": "Lichtgroengebalkt", "block.minecraft.banner.half_horizontal.magenta": "Magentagebalkt", "block.minecraft.banner.half_horizontal.orange": "Oranjegebalkt", "block.minecraft.banner.half_horizontal.pink": "Rozegebalkt", "block.minecraft.banner.half_horizontal.purple": "Paarsgebalkt", "block.minecraft.banner.half_horizontal.red": "Roodgebalkt", "block.minecraft.banner.half_horizontal.white": "Witgebalkt", "block.minecraft.banner.half_horizontal.yellow": "Geelgebalkt", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON> omge<PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "Blauw omgekeerd <PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "Bruin omgeke<PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "Turquoise omgeke<PERSON> g<PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "Groen omgekeerd <PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Lichtblauw omgekeerd g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Lichtgrijs omgekeerd <PERSON>", "block.minecraft.banner.half_horizontal_bottom.lime": "Lichtgroen omgekeerd gebalkt", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magenta omgekeerd <PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "Oranje omgekeerd <PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "Roze omgekeerd g<PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "Paars omgekeerd <PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON> om<PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "Wit omgekeerd g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "Turq<PERSON>isege<PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "G<PERSON>j<PERSON>ge<PERSON><PERSON>", "block.minecraft.banner.half_vertical.green": "Groengepaald", "block.minecraft.banner.half_vertical.light_blue": "Lichtblauwgepaald", "block.minecraft.banner.half_vertical.light_gray": "Lichtgrijsgepaald", "block.minecraft.banner.half_vertical.lime": "Lichtgroengepaald", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.orange": "Oranjegepaald", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.purple": "Paarsgepa<PERSON>", "block.minecraft.banner.half_vertical.red": "Roodgepaald", "block.minecraft.banner.half_vertical.white": "Witgepaald", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "Zwart omgekeerd g<PERSON>", "block.minecraft.banner.half_vertical_right.blue": "Blauw omgekeerd g<PERSON>", "block.minecraft.banner.half_vertical_right.brown": "Bruin omgekeerd g<PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Turquoise omgekeerd gep<PERSON>d", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON><PERSON> om<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.green": "Groen omgekeerd g<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Lichtblauw omgekeerd gepaald", "block.minecraft.banner.half_vertical_right.light_gray": "Lichtgrijs omgekeerd gepaald", "block.minecraft.banner.half_vertical_right.lime": "Lichtgroen omgekeerd gepaald", "block.minecraft.banner.half_vertical_right.magenta": "Magenta omgekeerd g<PERSON>", "block.minecraft.banner.half_vertical_right.orange": "Oranje omgekeerd g<PERSON>d", "block.minecraft.banner.half_vertical_right.pink": "Roze omgekeerd g<PERSON>d", "block.minecraft.banner.half_vertical_right.purple": "Paars omgekeerd g<PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON> om<PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.white": "Wit omgekeerd gep<PERSON>d", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Turquoise Mojang<PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "Lichtblauw Mojanglogo", "block.minecraft.banner.mojang.light_gray": "Lichtgrijs <PERSON>", "block.minecraft.banner.mojang.lime": "Lichtgroen Mojanglogo", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON>", "block.minecraft.banner.mojang.white": "Wit Mojanglogo", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON>", "block.minecraft.banner.piglin.black": "Zwarte snuit", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON> snuit", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON> snuit", "block.minecraft.banner.piglin.cyan": "Turquoise snuit", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON><PERSON> snuit", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON> snuit", "block.minecraft.banner.piglin.light_blue": "Lichtblauwe snuit", "block.minecraft.banner.piglin.light_gray": "Lichtgrijze snuit", "block.minecraft.banner.piglin.lime": "Lichtgroene snuit", "block.minecraft.banner.piglin.magenta": "Magenta snuit", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON><PERSON> snuit", "block.minecraft.banner.piglin.pink": "Roze snuit", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>e snuit", "block.minecraft.banner.piglin.red": "<PERSON><PERSON> snuit", "block.minecraft.banner.piglin.white": "Witte snuit", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON> snuit", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "Turquoise lozanje", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "Lichtblauw lozanje", "block.minecraft.banner.rhombus.light_gray": "Lichtgrijs <PERSON>", "block.minecraft.banner.rhombus.lime": "Lichtgroen lozanje", "block.minecraft.banner.rhombus.magenta": "Magenta lo<PERSON>", "block.minecraft.banner.rhombus.orange": "Or<PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON>oze lozanje", "block.minecraft.banner.rhombus.purple": "Paars lozanje", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON> lo<PERSON>", "block.minecraft.banner.rhombus.white": "Wit lozanje", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON>", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON> schedel", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON> schedel", "block.minecraft.banner.skull.cyan": "Turquoise schedel", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON> schedel", "block.minecraft.banner.skull.light_blue": "Lichtblauwe schedel", "block.minecraft.banner.skull.light_gray": "Lichtgri<PERSON><PERSON> schedel", "block.minecraft.banner.skull.lime": "Lichtgroene schedel", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON> schedel", "block.minecraft.banner.skull.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON>e schedel", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON> schedel", "block.minecraft.banner.skull.red": "<PERSON><PERSON> schedel", "block.minecraft.banner.skull.white": "<PERSON><PERSON> schedel", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> s<PERSON>el", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.cyan": "Turquoise palen", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.green": "G<PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.light_blue": "Lichtblauwe palen", "block.minecraft.banner.small_stripes.light_gray": "Lichtg<PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.lime": "Lichtgroene palen", "block.minecraft.banner.small_stripes.magenta": "Ma<PERSON>a palen", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.pink": "Roze palen", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON> palen", "block.minecraft.banner.small_stripes.white": "<PERSON>itte palen", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON> palen", "block.minecraft.banner.square_bottom_left.black": "<PERSON>wart kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON> kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON><PERSON> kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.cyan": "Turquoise kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON><PERSON> kwart<PERSON> recht<PERSON>der", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON> kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.light_blue": "Lichtblauw kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.light_gray": "Lichtgrijs kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.lime": "Lichtgroen kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.magenta": "Ma<PERSON>a kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.orange": "<PERSON><PERSON><PERSON> kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.pink": "<PERSON>oze kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.purple": "Paars kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON> kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.white": "Wit kwartier rechtsonder", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON> kwartier rechtsonder", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON> kwart<PERSON>", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON><PERSON> kwart<PERSON>", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON><PERSON> kwartier links<PERSON>er", "block.minecraft.banner.square_bottom_right.cyan": "Turquoise kwartier linksonder", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON> kwartier <PERSON>er", "block.minecraft.banner.square_bottom_right.light_blue": "Lichtblauw kwartier linksonder", "block.minecraft.banner.square_bottom_right.light_gray": "Lichtgrijs kwartier linksonder", "block.minecraft.banner.square_bottom_right.lime": "Lichtgroen kwartier linksonder", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON> kwartier linksonder", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON><PERSON> kwart<PERSON>", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON>e kwartier linksonder", "block.minecraft.banner.square_bottom_right.purple": "<PERSON><PERSON> kwartier linksonder", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON> kwartier links<PERSON>er", "block.minecraft.banner.square_bottom_right.white": "Wit kwartier linksonder", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON> kwart<PERSON><PERSON>", "block.minecraft.banner.square_top_left.black": "<PERSON><PERSON> kwartier rechtsboven", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON><PERSON> kwartier rechtsboven", "block.minecraft.banner.square_top_left.brown": "<PERSON><PERSON>in kwartier rechtsboven", "block.minecraft.banner.square_top_left.cyan": "Turquoise kwartier rechtsboven", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON><PERSON> kwartier rechtsboven", "block.minecraft.banner.square_top_left.green": "G<PERSON><PERSON> kwartier rechtsboven", "block.minecraft.banner.square_top_left.light_blue": "Lichtblauw kwartier rechtsboven", "block.minecraft.banner.square_top_left.light_gray": "Lichtgrijs kwartier rechtsboven", "block.minecraft.banner.square_top_left.lime": "Lichtgroen kwartier rechtsboven", "block.minecraft.banner.square_top_left.magenta": "Magenta kwartier rechtsboven", "block.minecraft.banner.square_top_left.orange": "<PERSON><PERSON>je kwartier rechtsboven", "block.minecraft.banner.square_top_left.pink": "Roze kwartier rechtsboven", "block.minecraft.banner.square_top_left.purple": "Paars kwartier rechtsboven", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON> kwartier rechtsboven", "block.minecraft.banner.square_top_left.white": "Wit kwartier rechtsboven", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON> kwartier rechtsboven", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON> kwartier linksboven", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON><PERSON> kwartier linksboven", "block.minecraft.banner.square_top_right.brown": "Bruin kwartier linksboven", "block.minecraft.banner.square_top_right.cyan": "Turquoise kwartier linksboven", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> linksboven", "block.minecraft.banner.square_top_right.green": "G<PERSON><PERSON> kwartier linksboven", "block.minecraft.banner.square_top_right.light_blue": "Lichtblauw kwartier linksboven", "block.minecraft.banner.square_top_right.light_gray": "Lichtgrijs kwartier linksboven", "block.minecraft.banner.square_top_right.lime": "Lichtgroen kwartier linksboven", "block.minecraft.banner.square_top_right.magenta": "Magenta kwartier linksboven", "block.minecraft.banner.square_top_right.orange": "Oranje kwartier linksboven", "block.minecraft.banner.square_top_right.pink": "Roze kwartier linksboven", "block.minecraft.banner.square_top_right.purple": "Paars kwartier linksboven", "block.minecraft.banner.square_top_right.red": "<PERSON>ood kwartier linksboven", "block.minecraft.banner.square_top_right.white": "Wit kwartier linksboven", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON> kwartier linksboven", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "Turquoise kruis", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Lichtblauw kruis", "block.minecraft.banner.straight_cross.light_gray": "Lichtgrijs k<PERSON>", "block.minecraft.banner.straight_cross.lime": "Lichtgroen kruis", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON> k<PERSON>is", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.white": "Wit kruis", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON> schild<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON> schild<PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON> schild<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Turquoise schildvoet", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON> schild<PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Lichtblauw schildvoet", "block.minecraft.banner.stripe_bottom.light_gray": "Lichtgrij<PERSON> s<PERSON>", "block.minecraft.banner.stripe_bottom.lime": "Lichtgroen schildvoet", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON> schild<PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON>oze schild<PERSON>et", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON> schild<PERSON>et", "block.minecraft.banner.stripe_bottom.red": "<PERSON>ood schild<PERSON>et", "block.minecraft.banner.stripe_bottom.white": "Wit schildvoet", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "Zwarte paal", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON> paal", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON> paal", "block.minecraft.banner.stripe_center.cyan": "Turquoise paal", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON><PERSON> paal", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON> paal", "block.minecraft.banner.stripe_center.light_blue": "Lichtblauwe paal", "block.minecraft.banner.stripe_center.light_gray": "Lichtgrijze paal", "block.minecraft.banner.stripe_center.lime": "Lichtgroene paal", "block.minecraft.banner.stripe_center.magenta": "Magenta paal", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON>oze paal", "block.minecraft.banner.stripe_center.purple": "Pa<PERSON>e paal", "block.minecraft.banner.stripe_center.red": "Rode paal", "block.minecraft.banner.stripe_center.white": "<PERSON>itte paal", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> paal", "block.minecraft.banner.stripe_downleft.black": "Zwarte linkerschuinbalk", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON>bal<PERSON>", "block.minecraft.banner.stripe_downleft.brown": "Bruine linkerschuinbalk", "block.minecraft.banner.stripe_downleft.cyan": "Turquoise linkerschuinbalk", "block.minecraft.banner.stripe_downleft.gray": "G<PERSON><PERSON><PERSON> linkerschuinbalk", "block.minecraft.banner.stripe_downleft.green": "Groene linkerschuinbalk", "block.minecraft.banner.stripe_downleft.light_blue": "Lichtblauwe linkerschuinbalk", "block.minecraft.banner.stripe_downleft.light_gray": "Lichtgrijze linkerschuinbalk", "block.minecraft.banner.stripe_downleft.lime": "Lichtgroene linkerschuinbalk", "block.minecraft.banner.stripe_downleft.magenta": "Magenta linkerschuinbalk", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON><PERSON>bal<PERSON>", "block.minecraft.banner.stripe_downleft.pink": "Roze linkerschuinbalk", "block.minecraft.banner.stripe_downleft.purple": "Paarse linkerschuinbalk", "block.minecraft.banner.stripe_downleft.red": "Rode linkerschuinbalk", "block.minecraft.banner.stripe_downleft.white": "Witte linkerschuinbalk", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON>inbalk", "block.minecraft.banner.stripe_downright.black": "Zwarte rechterschuinbalk", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "Turquoise rechters<PERSON>inbalk", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Lichtblauwe rechterschuinbalk", "block.minecraft.banner.stripe_downright.light_gray": "Lichtgrijze rechterschuinbalk", "block.minecraft.banner.stripe_downright.lime": "Lichtgroene rechterschuinbalk", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON> recht<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON><PERSON> rechters<PERSON>balk", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON> rechterschuinbalk", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON> rechterschuinbalk", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "Z<PERSON><PERSON> rechter<PERSON>", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.cyan": "Turquoise rechterpaal", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Lichtblauwe rechterpaal", "block.minecraft.banner.stripe_left.light_gray": "Lichtgrijze re<PERSON>al", "block.minecraft.banner.stripe_left.lime": "Lichtgroene rechterpaal", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON>e recht<PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON><PERSON> re<PERSON><PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.black": "Zwarte dwarsbalk", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Turquoise dwarsbalk", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>balk", "block.minecraft.banner.stripe_middle.green": "G<PERSON><PERSON> d<PERSON>balk", "block.minecraft.banner.stripe_middle.light_blue": "Lichtblauwe dwarsbalk", "block.minecraft.banner.stripe_middle.light_gray": "Lichtgrijze dwarsbalk", "block.minecraft.banner.stripe_middle.lime": "Lichtgroene dwarsbalk", "block.minecraft.banner.stripe_middle.magenta": "Magenta dwarsbalk", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "Roze dwarsbalk", "block.minecraft.banner.stripe_middle.purple": "Paarse dwarsbalk", "block.minecraft.banner.stripe_middle.red": "Rode dwarsbalk", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON> dwar<PERSON>balk", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Zwarte linkerpaal", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON> linker<PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "Turquoise linkerpaal", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON><PERSON> link<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.green": "Groene linkerpaal", "block.minecraft.banner.stripe_right.light_blue": "Lichtblauwe linkerpaal", "block.minecraft.banner.stripe_right.light_gray": "Lichtgrijze linkerpaal", "block.minecraft.banner.stripe_right.lime": "Lichtgroene linkerpaal", "block.minecraft.banner.stripe_right.magenta": "Magenta linkerpaal", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.pink": "Roze linkerpaal", "block.minecraft.banner.stripe_right.purple": "Paarse linker<PERSON>al", "block.minecraft.banner.stripe_right.red": "Rode linker<PERSON><PERSON>", "block.minecraft.banner.stripe_right.white": "Witte linker<PERSON>al", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> linker<PERSON><PERSON>", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON> schildhoofd", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON> schildhoofd", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON> schildhoofd", "block.minecraft.banner.stripe_top.cyan": "Turquoise schildhoofd", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON> schildhoofd", "block.minecraft.banner.stripe_top.light_blue": "Lichtblauw schildhoofd", "block.minecraft.banner.stripe_top.light_gray": "Lichtgrij<PERSON> schildhoofd", "block.minecraft.banner.stripe_top.lime": "Lichtgroen schildhoofd", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON> schildhoofd", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON><PERSON> s<PERSON>ho<PERSON>", "block.minecraft.banner.stripe_top.pink": "Roze schildhoofd", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON> schildhoofd", "block.minecraft.banner.stripe_top.red": "Rood schildhoofd", "block.minecraft.banner.stripe_top.white": "Wit schildhoofd", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON> schildhoofd", "block.minecraft.banner.triangle_bottom.black": "Zwarte keper", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Turquoise keper", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Lichtblauwe keper", "block.minecraft.banner.triangle_bottom.light_gray": "Lichtgrijze keper", "block.minecraft.banner.triangle_bottom.lime": "Lichtgroene keper", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON> keper", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "<PERSON>oze keper", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON><PERSON> keper", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON> keper", "block.minecraft.banner.triangle_bottom.white": "<PERSON>itte keper", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.black": "Zwarte omgekeerde keper", "block.minecraft.banner.triangle_top.blue": "Blauwe omgekeerde keper", "block.minecraft.banner.triangle_top.brown": "Bruine omgekeerde keper", "block.minecraft.banner.triangle_top.cyan": "Turquoise omgekeerde keper", "block.minecraft.banner.triangle_top.gray": "Grijze omgekeerde keper", "block.minecraft.banner.triangle_top.green": "Groene omgekeerde keper", "block.minecraft.banner.triangle_top.light_blue": "Lichtblauwe omgekeerde keper", "block.minecraft.banner.triangle_top.light_gray": "Lichtgrijze omgekeerde keper", "block.minecraft.banner.triangle_top.lime": "Lichtgroene omgekeerde keper", "block.minecraft.banner.triangle_top.magenta": "Magenta omgekeerde keper", "block.minecraft.banner.triangle_top.orange": "Oranje omgekeerde keper", "block.minecraft.banner.triangle_top.pink": "Roze omgekeerde keper", "block.minecraft.banner.triangle_top.purple": "Paarse omgekeerde keper", "block.minecraft.banner.triangle_top.red": "Rode omgekeerde keper", "block.minecraft.banner.triangle_top.white": "Witte omgekeerde keper", "block.minecraft.banner.triangle_top.yellow": "Gele omgekeerde keper", "block.minecraft.banner.triangles_bottom.black": "Zwartgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.blue": "Blauwgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.brown": "Bruingegol<PERSON>d schildvoet", "block.minecraft.banner.triangles_bottom.cyan": "Turquoisegegolfd schildvoet", "block.minecraft.banner.triangles_bottom.gray": "Grijsgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Lichtblauwgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.light_gray": "Lichtgrijsgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.lime": "Lichtgroengegolfd schild<PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "Magentagegolfd schildvoet", "block.minecraft.banner.triangles_bottom.orange": "Oranjegegolfd schildvoet", "block.minecraft.banner.triangles_bottom.pink": "Rozegegolfd schildvoet", "block.minecraft.banner.triangles_bottom.purple": "Paarsgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.red": "Roodgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.white": "Witgegolfd schildvoet", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> schild<PERSON>et", "block.minecraft.banner.triangles_top.black": "Zwartgegolfd schildhoofd", "block.minecraft.banner.triangles_top.blue": "Blauwgegolfd schildhoofd", "block.minecraft.banner.triangles_top.brown": "Bruingegolfd schildhoofd", "block.minecraft.banner.triangles_top.cyan": "Turquoisegegolfd schildhoofd", "block.minecraft.banner.triangles_top.gray": "Grijsgegolfd schildhoofd", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schildhoofd", "block.minecraft.banner.triangles_top.light_blue": "Lichtblauwgegolfd schildhoofd", "block.minecraft.banner.triangles_top.light_gray": "Lichtgrijsgegolfd schildhoofd", "block.minecraft.banner.triangles_top.lime": "Lichtgroengegolfd schildhoofd", "block.minecraft.banner.triangles_top.magenta": "Magentagegolfd schildhoofd", "block.minecraft.banner.triangles_top.orange": "Oranjegegolfd schildhoofd", "block.minecraft.banner.triangles_top.pink": "Rozegegolfd schildhoofd", "block.minecraft.banner.triangles_top.purple": "Paarsgegolfd schildhoofd", "block.minecraft.banner.triangles_top.red": "Roodgegolfd schildhoofd", "block.minecraft.banner.triangles_top.white": "Witgegolfd schildhoofd", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON>gol<PERSON>d schildhoofd", "block.minecraft.barrel": "Vat", "block.minecraft.barrier": "Barrière", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Baken", "block.minecraft.beacon.primary": "Primaire kracht", "block.minecraft.beacon.secondary": "Secundaire kracht", "block.minecraft.bed.no_sleep": "Je kunt alleen 's nachts of tijdens onweer slapen", "block.minecraft.bed.not_safe": "Je kunt nu niet slapen; er zijn monsters in de buurt", "block.minecraft.bed.obstructed": "Dit bed wordt geblokkeerd", "block.minecraft.bed.occupied": "Dit bed is bezet", "block.minecraft.bed.too_far_away": "Je kunt nu niet slapen; het bed is te ver weg", "block.minecraft.bedrock": "Bodemsteen", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "Bel", "block.minecraft.big_dripleaf": "Groot dru<PERSON>d", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON> d<PERSON>gel", "block.minecraft.birch_button": "Berkenhout<PERSON> knop", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> deur", "block.minecraft.birch_fence": "Berkenhouten hek", "block.minecraft.birch_fence_gate": "Berkenhouten poort", "block.minecraft.birch_hanging_sign": "Berkenhouten hangbord", "block.minecraft.birch_leaves": "Berkenbladeren", "block.minecraft.birch_log": "Berkens<PERSON>", "block.minecraft.birch_planks": "Berkenhouten planken", "block.minecraft.birch_pressure_plate": "Berkenhouten drukplaat", "block.minecraft.birch_sapling": "Berkenzaailing", "block.minecraft.birch_sign": "Berkenhouten bord", "block.minecraft.birch_slab": "Berkenhouten plaat", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.birch_trapdoor": "Berkenhouten valluik", "block.minecraft.birch_wall_hanging_sign": "Berkenhouten uithangbord", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord aan muur", "block.minecraft.birch_wood": "Berkenhout", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON> banier", "block.minecraft.black_bed": "Zwart bed", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON> kaars", "block.minecraft.black_candle_cake": "<PERSON><PERSON> met z<PERSON><PERSON> kaars", "block.minecraft.black_carpet": "<PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON> beton", "block.minecraft.black_concrete_powder": "Zwart cement", "block.minecraft.black_glazed_terracotta": "Zwartgeglazuurd terracotta", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass": "Zwartgekleurd glas", "block.minecraft.black_stained_glass_pane": "Zwartgekleurde glazen ruit", "block.minecraft.black_terracotta": "Zwart terracotta", "block.minecraft.black_wool": "Zwarte wol", "block.minecraft.blackstone": "Zwartsteen", "block.minecraft.blackstone_slab": "Zwartstenen plaat", "block.minecraft.blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.blackstone_wall": "Zwartstenen muur", "block.minecraft.blast_furnace": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_bed": "Blauw bed", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON> met b<PERSON><PERSON> kaars", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> beton", "block.minecraft.blue_concrete_powder": "Blauw cement", "block.minecraft.blue_glazed_terracotta": "Blauwgeglazuurd terracotta", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "Blauwgekleurd glas", "block.minecraft.blue_stained_glass_pane": "Blauwgekleurde glazen ruit", "block.minecraft.blue_terracotta": "Blauw terracotta", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON> wol", "block.minecraft.bone_block": "Bottenblok", "block.minecraft.bookshelf": "Boekenkast", "block.minecraft.brain_coral": "Hersenkoraal", "block.minecraft.brain_coral_block": "Hersenkoraalblok", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aan muur", "block.minecraft.brewing_stand": "Brouws<PERSON><PERSON>ard", "block.minecraft.brick_slab": "Bakstenen plaat", "block.minecraft.brick_stairs": "Bakstenen trap", "block.minecraft.brick_wall": "Bakstenen muur", "block.minecraft.bricks": "Bakstenen", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON> banier", "block.minecraft.brown_bed": "Bruin bed", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON> kaars", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> met bruine kaars", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> tap<PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON> beton", "block.minecraft.brown_concrete_powder": "Bruin cement", "block.minecraft.brown_glazed_terracotta": "Bruingeglazuurd terracotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON> paddenstoel", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON><PERSON> paddenstoelenblok", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Bruingekleurd glas", "block.minecraft.brown_stained_glass_pane": "Bruingekleurde glazen ruit", "block.minecraft.brown_terracotta": "Bruin terracotta", "block.minecraft.brown_wool": "<PERSON><PERSON><PERSON> wol", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "Blaaskoraal", "block.minecraft.bubble_coral_block": "Blaaskoraalblok", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "Blaaskoraalwaai<PERSON> aan muur", "block.minecraft.budding_amethyst": "Uitlopende amethist", "block.minecraft.bush": "Struik", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Cactusb<PERSON>em", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calciet", "block.minecraft.calibrated_sculk_sensor": "Gekalibreerd<PERSON> sculk<PERSON>", "block.minecraft.campfire": "Kampvuur", "block.minecraft.candle": "<PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> met kaars", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Kartografietafel", "block.minecraft.carved_pumpkin": "Bewerkte pompoen", "block.minecraft.cauldron": "Ketel", "block.minecraft.cave_air": "Grottenlucht", "block.minecraft.cave_vines": "Grottenlianen", "block.minecraft.cave_vines_plant": "Grottenlianenplant", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "Gekoppeld opdrachtblok", "block.minecraft.cherry_button": "<PERSON>rsenhouten knop", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON> deur", "block.minecraft.cherry_fence": "Kersenhouten hek", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON><PERSON> poortje", "block.minecraft.cherry_hanging_sign": "Kersenhouten hangbord", "block.minecraft.cherry_leaves": "Kersenbladeren", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "Kersenhouten planken", "block.minecraft.cherry_pressure_plate": "Kersenhouten drukplaat", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Ke<PERSON>houten bord", "block.minecraft.cherry_slab": "Kersenhouten plaat", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.cherry_trapdoor": "Kersenhouten valluik", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> uithangbord", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON><PERSON> bord aan muur", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "Gebarsten aambeeld", "block.minecraft.chiseled_bookshelf": "Bewerkte boekenkast", "block.minecraft.chiseled_copper": "Bewerkt koperblok", "block.minecraft.chiseled_deepslate": "Gebeiteld wrevelsteen", "block.minecraft.chiseled_nether_bricks": "Gebeitelde Netherbakstenen", "block.minecraft.chiseled_polished_blackstone": "Gebeiteld gepolijst zwartsteen", "block.minecraft.chiseled_quartz_block": "Gebeiteld kwartsblok", "block.minecraft.chiseled_red_sandstone": "Gebeiteld rood zandsteen", "block.minecraft.chiseled_resin_bricks": "Gebeitelde harsbakstenen", "block.minecraft.chiseled_sandstone": "Gebeiteld zandsteen", "block.minecraft.chiseled_stone_bricks": "Gebeitelde blokstenen", "block.minecraft.chiseled_tuff": "Gebeiteld tufsteen", "block.minecraft.chiseled_tuff_bricks": "Gebeitelde tufstenen blokstenen", "block.minecraft.chorus_flower": "Chorusbloem", "block.minecraft.chorus_plant": "Chorusplant", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Gesloten glu<PERSON>", "block.minecraft.coal_block": "Steenkoolblok", "block.minecraft.coal_ore": "Steenkoolerts", "block.minecraft.coarse_dirt": "Grove aarde", "block.minecraft.cobbled_deepslate": "Gep<PERSON><PERSON>d wrevel<PERSON>en", "block.minecraft.cobbled_deepslate_slab": "Geplaveide wrevelstenen plaat", "block.minecraft.cobbled_deepslate_stairs": "Geplaveide wrevelstenen trap", "block.minecraft.cobbled_deepslate_wall": "Geplaveide wrevelstenen muur", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON> plaat", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON> muur", "block.minecraft.cobweb": "Spinnenweb", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "Opdrachtblok", "block.minecraft.comparator": "Redstonevergelijker", "block.minecraft.composter": "Compostbak", "block.minecraft.conduit": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Koperblok", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON> lamp", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "Koperen rooster", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.cornflower": "Korenb<PERSON>em", "block.minecraft.cracked_deepslate_bricks": "Gebarsten wrevelstenen blokstenen", "block.minecraft.cracked_deepslate_tiles": "Gebarsten wrevelstenen tegels", "block.minecraft.cracked_nether_bricks": "Gebarsten Netherbakstenen", "block.minecraft.cracked_polished_blackstone_bricks": "Gebarsten gepolijste zwartstenen blokstenen", "block.minecraft.cracked_stone_bricks": "Gebarsten blokstenen", "block.minecraft.crafter": "Vervaardigingsmachine", "block.minecraft.crafting_table": "Werkbank", "block.minecraft.creaking_heart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creeper_head": "Creeperhoofd", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aan muur", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON> houten knop", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON> houten deur", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON>d houten hek", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON> houten poort", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON> zwam", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> houten hangbord", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>n", "block.minecraft.crimson_nylium": "He<PERSON><PERSON>d nylium", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON> houten planken", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> houten drukp<PERSON>at", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON> wortels", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON> houten bord", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON> houten plaat", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON> houten trap", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON> stam", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON> houten valluik", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> houten uithangbord", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON> houten bord aan muur", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON> obsid<PERSON>", "block.minecraft.cut_copper": "Gesneden koperblok", "block.minecraft.cut_copper_slab": "Gesneden koperen plaat", "block.minecraft.cut_copper_stairs": "Gesneden koperen trap", "block.minecraft.cut_red_sandstone": "Gesneden rood zandsteen", "block.minecraft.cut_red_sandstone_slab": "Gesneden rode zandstenen plaat", "block.minecraft.cut_sandstone": "Gesneden zandsteen", "block.minecraft.cut_sandstone_slab": "Gesneden zandstenen plaat", "block.minecraft.cyan_banner": "Turquoise banier", "block.minecraft.cyan_bed": "Turquoise bed", "block.minecraft.cyan_candle": "Turquoise kaars", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> met turquoise kaars", "block.minecraft.cyan_carpet": "Turquoise tapijt", "block.minecraft.cyan_concrete": "Turquoise beton", "block.minecraft.cyan_concrete_powder": "Turquoise cement", "block.minecraft.cyan_glazed_terracotta": "Turquoisegeglazuurd terracotta", "block.minecraft.cyan_shulker_box": "Turquoise <PERSON>", "block.minecraft.cyan_stained_glass": "Turquoisegekleurd glas", "block.minecraft.cyan_stained_glass_pane": "Turquoisegekleurde glazen ruit", "block.minecraft.cyan_terracotta": "Turquoise terracotta", "block.minecraft.cyan_wool": "Turquoise wol", "block.minecraft.damaged_anvil": "Beschadigd a<PERSON>ld", "block.minecraft.dandelion": "Paardenbloem", "block.minecraft.dark_oak_button": "<PERSON><PERSON><PERSON> eikenhouten knop", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON> e<PERSON>nhouten deur", "block.minecraft.dark_oak_fence": "Donker eikenhouten hek", "block.minecraft.dark_oak_fence_gate": "Donkere eikenhouten poort", "block.minecraft.dark_oak_hanging_sign": "Donker eikenhouten hangbord", "block.minecraft.dark_oak_leaves": "Donkere eikenbladeren", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.dark_oak_planks": "Donkere eikenhouten planken", "block.minecraft.dark_oak_pressure_plate": "Donkere eikenhouten drukplaat", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.dark_oak_sign": "Donker eikenhouten bord", "block.minecraft.dark_oak_slab": "Donkere eikenhouten plaat", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON><PERSON> e<PERSON> trap", "block.minecraft.dark_oak_trapdoor": "Donker eikenhouten valluik", "block.minecraft.dark_oak_wall_hanging_sign": "Donker eikenhouten uithangbord", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON> e<PERSON>nh<PERSON>en bord aan muur", "block.minecraft.dark_oak_wood": "<PERSON><PERSON> e<PERSON>out", "block.minecraft.dark_prismarine": "Donkerprismarien", "block.minecraft.dark_prismarine_slab": "Donkerprismarienplaat", "block.minecraft.dark_prismarine_stairs": "Donkerprismarientrap", "block.minecraft.daylight_detector": "Daglichtsensor", "block.minecraft.dead_brain_coral": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON> aan muur", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>b<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON> b<PERSON> aan muur", "block.minecraft.dead_bush": "<PERSON><PERSON> struik", "block.minecraft.dead_fire_coral": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "Dood brandkoraalblok", "block.minecraft.dead_fire_coral_fan": "Dode brand<PERSON>aal<PERSON>aier", "block.minecraft.dead_fire_coral_wall_fan": "Dode brand<PERSON>ai<PERSON> aan muur", "block.minecraft.dead_horn_coral": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON> aan muur", "block.minecraft.dead_tube_coral": "<PERSON><PERSON> b<PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON> b<PERSON>", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON> b<PERSON> aan muur", "block.minecraft.decorated_pot": "Versierde pot", "block.minecraft.deepslate": "Wrevelsteen", "block.minecraft.deepslate_brick_slab": "Wrevelblokstenen plaat", "block.minecraft.deepslate_brick_stairs": "Wrevelblokstenen trap", "block.minecraft.deepslate_brick_wall": "Wrevelblokstenen muur", "block.minecraft.deepslate_bricks": "Wrevelstenen blokstenen", "block.minecraft.deepslate_coal_ore": "Wrevelsteen-steenkoolerts", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "Wrevelsteendiamanterts", "block.minecraft.deepslate_emerald_ore": "Wrevelsteensmaragderts", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_lapis_ore": "Wrevelsteenlapislazuli-erts", "block.minecraft.deepslate_redstone_ore": "Wrevelsteenredstone-erts", "block.minecraft.deepslate_tile_slab": "Wrevelstenen tegelplaat", "block.minecraft.deepslate_tile_stairs": "Wrevelstenen tegeltrap", "block.minecraft.deepslate_tile_wall": "Wrevelstenen tegelmuur", "block.minecraft.deepslate_tiles": "Wrevelstenen tegels", "block.minecraft.detector_rail": "Detectiespoor", "block.minecraft.diamond_block": "Diamantblok", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "Dioriet", "block.minecraft.diorite_slab": "Diorietplaat", "block.minecraft.diorite_stairs": "Dioriettrap", "block.minecraft.diorite_wall": "Diorietmuur", "block.minecraft.dirt": "A<PERSON>e", "block.minecraft.dirt_path": "Aardepad", "block.minecraft.dispenser": "Dispenser", "block.minecraft.dragon_egg": "Drakenei", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON> aan muur", "block.minecraft.dried_ghast": "Uitged<PERSON>g<PERSON>", "block.minecraft.dried_kelp_block": "Gedroogd kelpblok", "block.minecraft.dripstone_block": "Druipsteenblok", "block.minecraft.dropper": "Dropper", "block.minecraft.emerald_block": "Smaragdblok", "block.minecraft.emerald_ore": "Smaragderts", "block.minecraft.enchanting_table": "Betoveringstafel", "block.minecraft.end_gateway": "Endtoegangsportaal", "block.minecraft.end_portal": "Endportaal", "block.minecraft.end_portal_frame": "Endportaalframe", "block.minecraft.end_rod": "Endstaf", "block.minecraft.end_stone": "Endsteen", "block.minecraft.end_stone_brick_slab": "Endblokstenen plaat", "block.minecraft.end_stone_brick_stairs": "Endblokstenen trap", "block.minecraft.end_stone_brick_wall": "Endblokstenen muur", "block.minecraft.end_stone_bricks": "Endblokstenen", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Blootgesteld bewerkt koperblok", "block.minecraft.exposed_copper": "Blootgesteld koperblok", "block.minecraft.exposed_copper_bulb": "Blootgestelde koperen lamp", "block.minecraft.exposed_copper_door": "Blootgestelde koperen deur", "block.minecraft.exposed_copper_grate": "Blootgesteld koperen rooster", "block.minecraft.exposed_copper_trapdoor": "Blootgesteld koperen valluik", "block.minecraft.exposed_cut_copper": "Blootgesteld gesneden koperblok", "block.minecraft.exposed_cut_copper_slab": "Blootgestelde gesneden koperen plaat", "block.minecraft.exposed_cut_copper_stairs": "Blootgestelde gesneden koperen trap", "block.minecraft.farmland": "Akkerland", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "Brandkoraal", "block.minecraft.fire_coral_block": "Brandkoraalblok", "block.minecraft.fire_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_wall_fan": "Brandkoraalwaaier aan muur", "block.minecraft.firefly_bush": "Vuurvliegjesstruik", "block.minecraft.fletching_table": "Bevederingstafel", "block.minecraft.flower_pot": "Bloempot", "block.minecraft.flowering_azalea": "Bloeiende azalea", "block.minecraft.flowering_azalea_leaves": "Bloeiende azaleabladeren", "block.minecraft.frogspawn": "Kikkerdril", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON><PERSON> ij<PERSON>", "block.minecraft.furnace": "Oven", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>en", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glazen ruit", "block.minecraft.glow_lichen": "Gloeiend korstmos", "block.minecraft.glowstone": "Gloeisteen", "block.minecraft.gold_block": "Goudblok", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.granite": "Gran<PERSON>", "block.minecraft.granite_slab": "Granietplaat", "block.minecraft.granite_stairs": "Graniettrap", "block.minecraft.granite_wall": "Granietmuur", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Grasblok", "block.minecraft.gravel": "Grind", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> met g<PERSON><PERSON><PERSON>ars", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "Grijs cement", "block.minecraft.gray_glazed_terracotta": "Grijsgeglazuurd terracotta", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "Grijsgekleurd glas", "block.minecraft.gray_stained_glass_pane": "Grijsgekleurde glazen ruit", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON><PERSON> terracotta", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON><PERSON> wol", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON> banier", "block.minecraft.green_bed": "G<PERSON>en bed", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON> kaars", "block.minecraft.green_candle_cake": "<PERSON><PERSON> met gro<PERSON> kaars", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON> beton", "block.minecraft.green_concrete_powder": "Groen cement", "block.minecraft.green_glazed_terracotta": "Groengeglazuurd terracotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "Groengekleurd glas", "block.minecraft.green_stained_glass_pane": "Groengekleurde glazen ruit", "block.minecraft.green_terracotta": "Groen terracotta", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON> wol", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> wortels", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "Zware kern", "block.minecraft.heavy_weighted_pressure_plate": "Zware drukgevoelige plaat", "block.minecraft.honey_block": "Honingblok", "block.minecraft.honeycomb_block": "Honingraatblok", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Hoornkoraal", "block.minecraft.horn_coral_block": "Hoornkoraalblok", "block.minecraft.horn_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON>koraalwaai<PERSON> aan muur", "block.minecraft.ice": "IJs", "block.minecraft.infested_chiseled_stone_bricks": "Geïnfecteerde gebeitelde blokstenen", "block.minecraft.infested_cobblestone": "Geïnfecteerde keisteen", "block.minecraft.infested_cracked_stone_bricks": "Geïnfecteerde gebarsten blokstenen", "block.minecraft.infested_deepslate": "Geïnfecteerd wrevelsteen", "block.minecraft.infested_mossy_stone_bricks": "Geïnfecteerde mossige blokstenen", "block.minecraft.infested_stone": "Geïnfecteerde steen", "block.minecraft.infested_stone_bricks": "Geïnfecteerde blokstenen", "block.minecraft.iron_bars": "T<PERSON>ies", "block.minecraft.iron_block": "IJzerblok", "block.minecraft.iron_door": "IJzeren deur", "block.minecraft.iron_ore": "IJzererts", "block.minecraft.iron_trapdoor": "IJzeren valluik", "block.minecraft.jack_o_lantern": "Jack-o'-lantern", "block.minecraft.jigsaw": "Puzzelblok", "block.minecraft.jukebox": "Platenspeler", "block.minecraft.jungle_button": "Oerwoudhouten knop", "block.minecraft.jungle_door": "<PERSON>er<PERSON>ud<PERSON><PERSON> deur", "block.minecraft.jungle_fence": "Oerwoudhouten hek", "block.minecraft.jungle_fence_gate": "Oerwoudhouten poort", "block.minecraft.jungle_hanging_sign": "Oerwoudhouten hangbord", "block.minecraft.jungle_leaves": "Oerwoudbladeren", "block.minecraft.jungle_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_planks": "Oerwoudhouten planken", "block.minecraft.jungle_pressure_plate": "Oerwoudhouten drukplaat", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_sign": "Oerwoudhouten bord", "block.minecraft.jungle_slab": "Oerwoudhouten plaat", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON>udhouten trap", "block.minecraft.jungle_trapdoor": "Oerwoudhouten valluik", "block.minecraft.jungle_wall_hanging_sign": "Oerwoudhouten uithangbord", "block.minecraft.jungle_wall_sign": "Oer<PERSON>udhouten bord aan muur", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ladder": "Ladder", "block.minecraft.lantern": "Lantaar<PERSON>", "block.minecraft.lapis_block": "Lapislazuliblok", "block.minecraft.lapis_ore": "Lapis lazuli-erts", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON> ameth<PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON> varen", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Ketel met lava", "block.minecraft.leaf_litter": "<PERSON><PERSON> bladeren", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "Licht", "block.minecraft.light_blue_banner": "Lichtblauwe banier", "block.minecraft.light_blue_bed": "Lichtblauw bed", "block.minecraft.light_blue_candle": "Lichtblauwe kaars", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> met l<PERSON><PERSON><PERSON><PERSON><PERSON> kaars", "block.minecraft.light_blue_carpet": "Lichtblauw tapijt", "block.minecraft.light_blue_concrete": "Lichtblauw beton", "block.minecraft.light_blue_concrete_powder": "Lichtblauw cement", "block.minecraft.light_blue_glazed_terracotta": "Lichtblauwgeglazuurd terracotta", "block.minecraft.light_blue_shulker_box": "Lichtblauwe Shulkerdoos", "block.minecraft.light_blue_stained_glass": "Lichtblauwgekleurd glas", "block.minecraft.light_blue_stained_glass_pane": "Lichtblauwgekleurde glazen ruit", "block.minecraft.light_blue_terracotta": "Lichtblauw terracotta", "block.minecraft.light_blue_wool": "Lichtblauwe wol", "block.minecraft.light_gray_banner": "<PERSON>chtg<PERSON><PERSON><PERSON> banier", "block.minecraft.light_gray_bed": "Lichtgrijs bed", "block.minecraft.light_gray_candle": "Lichtgri<PERSON><PERSON> kaars", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ars", "block.minecraft.light_gray_carpet": "Lichtgrijs tapi<PERSON>", "block.minecraft.light_gray_concrete": "Lichtgri<PERSON><PERSON> beton", "block.minecraft.light_gray_concrete_powder": "Lichtgrijs cement", "block.minecraft.light_gray_glazed_terracotta": "Lichtgrijsgeglazuurd terracotta", "block.minecraft.light_gray_shulker_box": "Lichtgri<PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "Lichtgrijsgekleurd glas", "block.minecraft.light_gray_stained_glass_pane": "Lichtgrijsgekleurde glazen ruit", "block.minecraft.light_gray_terracotta": "Lichtgrijs terracotta", "block.minecraft.light_gray_wool": "Lichtgrijze wol", "block.minecraft.light_weighted_pressure_plate": "Lichte drukgevoelige plaat", "block.minecraft.lightning_rod": "Bliksemafleider", "block.minecraft.lilac": "Sering", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON>van-<PERSON>len", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "<PERSON>chtgro<PERSON> banier", "block.minecraft.lime_bed": "Lichtgroen bed", "block.minecraft.lime_candle": "Lichtgroene kaars", "block.minecraft.lime_candle_cake": "<PERSON><PERSON> met l<PERSON><PERSON><PERSON><PERSON> kaars", "block.minecraft.lime_carpet": "Lichtgroen tapijt", "block.minecraft.lime_concrete": "<PERSON>cht<PERSON><PERSON> beton", "block.minecraft.lime_concrete_powder": "Lichtgroen cement", "block.minecraft.lime_glazed_terracotta": "Lichtgroengeglazuurd terracotta", "block.minecraft.lime_shulker_box": "Lichtgroene Shulkerdoos", "block.minecraft.lime_stained_glass": "Lichtgroengekleurd glas", "block.minecraft.lime_stained_glass_pane": "Lichtgroengekleurde glazen ruit", "block.minecraft.lime_terracotta": "Lichtgroen terracotta", "block.minecraft.lime_wool": "Lichtgroene wol", "block.minecraft.lodestone": "Zeilsteen", "block.minecraft.loom": "<PERSON><PERSON><PERSON>ou<PERSON>", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON> banier", "block.minecraft.magenta_bed": "Magenta bed", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON> kaars", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON> met magenta kaars", "block.minecraft.magenta_carpet": "Ma<PERSON><PERSON> tapi<PERSON>t", "block.minecraft.magenta_concrete": "Ma<PERSON>a beton", "block.minecraft.magenta_concrete_powder": "Magenta cement", "block.minecraft.magenta_glazed_terracotta": "Magentageglazuurd terracotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "Magentagekleurd glas", "block.minecraft.magenta_stained_glass_pane": "Magentagekleurde glazen ruit", "block.minecraft.magenta_terracotta": "Magenta terracotta", "block.minecraft.magenta_wool": "Magenta wol", "block.minecraft.magma_block": "Magmablok", "block.minecraft.mangrove_button": "Mangrovehouten knop", "block.minecraft.mangrove_door": "Mangrovehouten deur", "block.minecraft.mangrove_fence": "Mangrovehouten hek", "block.minecraft.mangrove_fence_gate": "Mangrovehouten poort", "block.minecraft.mangrove_hanging_sign": "Mangrovehouten hangbord", "block.minecraft.mangrove_leaves": "Mangrovebladeren", "block.minecraft.mangrove_log": "Mangrovestam", "block.minecraft.mangrove_planks": "Mangrovehouten planken", "block.minecraft.mangrove_pressure_plate": "Mangrovehouten drukplaat", "block.minecraft.mangrove_propagule": "Mangrovebroedknop", "block.minecraft.mangrove_roots": "Mangrovewortels", "block.minecraft.mangrove_sign": "Mangrovehouten bord", "block.minecraft.mangrove_slab": "Mangrovehouten plaat", "block.minecraft.mangrove_stairs": "Mangrovehouten trap", "block.minecraft.mangrove_trapdoor": "Mangrovehouten valluik", "block.minecraft.mangrove_wall_hanging_sign": "Mangrovehouten uithangbord", "block.minecraft.mangrove_wall_sign": "Mangrovehouten bord aan muur", "block.minecraft.mangrove_wood": "Mangrovehout", "block.minecraft.medium_amethyst_bud": "Middelgrote amethistknop", "block.minecraft.melon": "<PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Mosblok", "block.minecraft.moss_carpet": "Mostapijt", "block.minecraft.mossy_cobblestone": "Mossige keisteen", "block.minecraft.mossy_cobblestone_slab": "Mossige keistenen plaat", "block.minecraft.mossy_cobblestone_stairs": "Mossige keistenen trap", "block.minecraft.mossy_cobblestone_wall": "Mossige keistenen muur", "block.minecraft.mossy_stone_brick_slab": "Mossige blokstenen plaat", "block.minecraft.mossy_stone_brick_stairs": "Mossige blokstenen trap", "block.minecraft.mossy_stone_brick_wall": "Mossige blokstenen muur", "block.minecraft.mossy_stone_bricks": "Mossige blokstenen", "block.minecraft.moving_piston": "Bewegen<PERSON> zu<PERSON>", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Modderstenen plaat", "block.minecraft.mud_brick_stairs": "Mo<PERSON><PERSON>en trap", "block.minecraft.mud_brick_wall": "Modder<PERSON><PERSON> muur", "block.minecraft.mud_bricks": "Modderstenen", "block.minecraft.muddy_mangrove_roots": "Modderige mangrovewortels", "block.minecraft.mushroom_stem": "Paddenstoelenstam", "block.minecraft.mycelium": "Zwamvlok", "block.minecraft.nether_brick_fence": "Netherbakstenen hek", "block.minecraft.nether_brick_slab": "Netherbakstenen plaat", "block.minecraft.nether_brick_stairs": "Netherbakstenen trap", "block.minecraft.nether_brick_wall": "Netherbakstenen muur", "block.minecraft.nether_bricks": "Netherbakstenen", "block.minecraft.nether_gold_ore": "Nethergouderts", "block.minecraft.nether_portal": "Netherportaal", "block.minecraft.nether_quartz_ore": "Netherkwartserts", "block.minecraft.nether_sprouts": "Netherscheuten", "block.minecraft.nether_wart": "<PERSON>herk<PERSON><PERSON>", "block.minecraft.nether_wart_block": "Netherkruidblok", "block.minecraft.netherite_block": "Netherietblok", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Nootblok", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> knop", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> deur", "block.minecraft.oak_fence": "Eikenhouten hek", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON> poort", "block.minecraft.oak_hanging_sign": "Eikenhouten hangbord", "block.minecraft.oak_leaves": "Eikenbladeren", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "Eikenhouten planken", "block.minecraft.oak_pressure_plate": "Eikenhouten drukplaat", "block.minecraft.oak_sapling": "E<PERSON>nzaailing", "block.minecraft.oak_sign": "Eikenhouten bord", "block.minecraft.oak_slab": "Eikenhouten plaat", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.oak_trapdoor": "Eikenhouten valluik", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>bord", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord aan muur", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.observer": "Observeerder", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.ominous_banner": "Onheilspellende banier", "block.minecraft.open_eyeblossom": "Geopende gluurbloesem", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON> bed", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> met <PERSON><PERSON><PERSON> kaars", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "Oranje cement", "block.minecraft.orange_glazed_terracotta": "Oranjegeglazuurd terracotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass": "Oranjegekleurd glas", "block.minecraft.orange_stained_glass_pane": "Oranjegekleurde glazen ruit", "block.minecraft.orange_terracotta": "Oranje terracotta", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> tulp", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON> wol", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Geoxideerd bewerkt koperblok", "block.minecraft.oxidized_copper": "Geoxideerd <PERSON>", "block.minecraft.oxidized_copper_bulb": "Geoxideerde koperen lamp", "block.minecraft.oxidized_copper_door": "Geoxideerde koperen deur", "block.minecraft.oxidized_copper_grate": "Geoxideerd koperen rooster", "block.minecraft.oxidized_copper_trapdoor": "Geoxideerd koperen valluik", "block.minecraft.oxidized_cut_copper": "Geoxideerd gesneden koperblok", "block.minecraft.oxidized_cut_copper_slab": "Geoxideerde gesneden koperen plaat", "block.minecraft.oxidized_cut_copper_stairs": "Geoxideerde gesneden koperen trap", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "Pakmo<PERSON>", "block.minecraft.pale_hanging_moss": "Bleek hangend mos", "block.minecraft.pale_moss_block": "Bleek mosblok", "block.minecraft.pale_moss_carpet": "Bleek mostapijt", "block.minecraft.pale_oak_button": "Bleke eikenhouten knop", "block.minecraft.pale_oak_door": "Bleke eikenhouten deur", "block.minecraft.pale_oak_fence": "Bleek eikenhouten hek", "block.minecraft.pale_oak_fence_gate": "Bleke eikenhouten poort", "block.minecraft.pale_oak_hanging_sign": "Bleek eikenhouten hangbord", "block.minecraft.pale_oak_leaves": "Bleke eikenbladeren", "block.minecraft.pale_oak_log": "Bleke eikenstam", "block.minecraft.pale_oak_planks": "Bleke eikenhouten planken", "block.minecraft.pale_oak_pressure_plate": "Bleke eikenhouten drukplaat", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.pale_oak_sign": "Bleek eikenhouten bord", "block.minecraft.pale_oak_slab": "Bleke eikenhouten plaat", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON> e<PERSON> trap", "block.minecraft.pale_oak_trapdoor": "Bleke eikenhouten valluik", "block.minecraft.pale_oak_wall_hanging_sign": "Bleek eikenhouten uithangbord", "block.minecraft.pale_oak_wall_sign": "Bleek eikenhouten bord aan muur", "block.minecraft.pale_oak_wood": "Bleek eikenhout", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON> k<PERSON>", "block.minecraft.peony": "Pioenroos", "block.minecraft.petrified_oak_slab": "Versteende eikenhouten plaat", "block.minecraft.piglin_head": "Piglinhoofd", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON> aan muur", "block.minecraft.pink_banner": "<PERSON><PERSON>e banier", "block.minecraft.pink_bed": "Roze bed", "block.minecraft.pink_candle": "Roze kaars", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> met roze kaars", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON> tap<PERSON>t", "block.minecraft.pink_concrete": "Roze beton", "block.minecraft.pink_concrete_powder": "Roze cement", "block.minecraft.pink_glazed_terracotta": "Rozegeglazuurd terracotta", "block.minecraft.pink_petals": "R<PERSON>e b<PERSON>adjes", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_stained_glass": "Rozegekleurd glas", "block.minecraft.pink_stained_glass_pane": "Rozegekleurde glazen ruit", "block.minecraft.pink_terracotta": "Roze terracotta", "block.minecraft.pink_tulip": "Roze tulp", "block.minecraft.pink_wool": "Roze wol", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "Zuigerhoofd", "block.minecraft.pitcher_crop": "Bekerplantgewas", "block.minecraft.pitcher_plant": "Bekerplant", "block.minecraft.player_head": "Spelershoofd", "block.minecraft.player_head.named": "Hoofd van %s", "block.minecraft.player_wall_head": "Spelerhoofd aan muur", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "Puntige druipsteen", "block.minecraft.polished_andesite": "Gepolijst andesiet", "block.minecraft.polished_andesite_slab": "Gepolijste andesietplaat", "block.minecraft.polished_andesite_stairs": "Gepolijste andesiettrap", "block.minecraft.polished_basalt": "Gepolijst basalt", "block.minecraft.polished_blackstone": "Gepolijst zwartsteen", "block.minecraft.polished_blackstone_brick_slab": "Gepolijste zwartstenen blokstenen plaat", "block.minecraft.polished_blackstone_brick_stairs": "Gepolijste zwartstenen blokstenen trap", "block.minecraft.polished_blackstone_brick_wall": "Gepolijste zwartstenen blokstenen muur", "block.minecraft.polished_blackstone_bricks": "Gepolijste zwartstenen blokstenen", "block.minecraft.polished_blackstone_button": "Gepolijste zwartstenen knop", "block.minecraft.polished_blackstone_pressure_plate": "Gepolijste zwartstenen drukplaat", "block.minecraft.polished_blackstone_slab": "Gepolijste zwartstenen plaat", "block.minecraft.polished_blackstone_stairs": "Gepolijste zwartstenen trap", "block.minecraft.polished_blackstone_wall": "Gepolijste zwartstenen muur", "block.minecraft.polished_deepslate": "Gepolijst wrevelsteen", "block.minecraft.polished_deepslate_slab": "Gepolijste wrevelstenen plaat", "block.minecraft.polished_deepslate_stairs": "Gepolijste wrevelstenen trap", "block.minecraft.polished_deepslate_wall": "Gepolijste wrevelstenen muur", "block.minecraft.polished_diorite": "Gepolijst dioriet", "block.minecraft.polished_diorite_slab": "Gepolijste diorietplaat", "block.minecraft.polished_diorite_stairs": "Gepolijste dioriettrap", "block.minecraft.polished_granite": "Gepolijst graniet", "block.minecraft.polished_granite_slab": "Gepolijste granietplaat", "block.minecraft.polished_granite_stairs": "Gepolijste graniettrap", "block.minecraft.polished_tuff": "Gepolijst tufsteen", "block.minecraft.polished_tuff_slab": "Gepolijste tufstenen plaat", "block.minecraft.polished_tuff_stairs": "Gepolijste tufstenen trap", "block.minecraft.polished_tuff_wall": "Gepolijste tufstenen muur", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Aardappels", "block.minecraft.potted_acacia_sapling": "Opgepotte acaciazaailing", "block.minecraft.potted_allium": "Opgepot look", "block.minecraft.potted_azalea_bush": "Opgepotte azalea", "block.minecraft.potted_azure_bluet": "Opgepot porseleinsterretje", "block.minecraft.potted_bamboo": "Opgepotte bamboe", "block.minecraft.potted_birch_sapling": "Opgepotte berkenzaailing", "block.minecraft.potted_blue_orchid": "Opgepotte blauwe <PERSON>ee", "block.minecraft.potted_brown_mushroom": "Opgepotte bruine paddenstoel", "block.minecraft.potted_cactus": "Opgepotte cactus", "block.minecraft.potted_cherry_sapling": "Opgepotte kersenzaailing", "block.minecraft.potted_closed_eyeblossom": "Opgepotte gesloten gluurbloesem", "block.minecraft.potted_cornflower": "Opgepotte korenbloem", "block.minecraft.potted_crimson_fungus": "Opgepotte helrode zwam", "block.minecraft.potted_crimson_roots": "Opgepot<PERSON> helrode wortels", "block.minecraft.potted_dandelion": "Opgepotte paardenbloem", "block.minecraft.potted_dark_oak_sapling": "Opgepotte donkere eikenzaailing", "block.minecraft.potted_dead_bush": "Opgepotte dode struik", "block.minecraft.potted_fern": "Opgepotte varen", "block.minecraft.potted_flowering_azalea_bush": "Opgepotte bloeiende azalea", "block.minecraft.potted_jungle_sapling": "Opgepotte <PERSON>", "block.minecraft.potted_lily_of_the_valley": "Opgepot le<PERSON>tje-van-<PERSON>len", "block.minecraft.potted_mangrove_propagule": "Opgepotte mangrovebroedknop", "block.minecraft.potted_oak_sapling": "Opgepotte eikenzaailing", "block.minecraft.potted_open_eyeblossom": "Opgepotte geopende gluurbloesem", "block.minecraft.potted_orange_tulip": "Opgepotte oranje tulp", "block.minecraft.potted_oxeye_daisy": "Opgepotte margriet", "block.minecraft.potted_pale_oak_sapling": "Opgepotte bleke eikenzaailing", "block.minecraft.potted_pink_tulip": "Opgepotte roze tulp", "block.minecraft.potted_poppy": "Opgepotte klaproos", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON><PERSON><PERSON> rode paddenstoel", "block.minecraft.potted_red_tulip": "<PERSON><PERSON><PERSON><PERSON> rode tulp", "block.minecraft.potted_spruce_sapling": "Opgepotte sparrenzaailing", "block.minecraft.potted_torchflower": "Opgepotte fakkelbloem", "block.minecraft.potted_warped_fungus": "Opgepotte spookachtige zwam", "block.minecraft.potted_warped_roots": "Opgepotte spookachtige wortels", "block.minecraft.potted_white_tulip": "<PERSON><PERSON><PERSON><PERSON> witte tulp", "block.minecraft.potted_wither_rose": "Opgepotte <PERSON>", "block.minecraft.powder_snow": "Poedersneeuw", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON> met p<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine": "Prismarien", "block.minecraft.prismarine_brick_slab": "Prismarienblokstenen plaat", "block.minecraft.prismarine_brick_stairs": "Prismarienblokstenen trap", "block.minecraft.prismarine_bricks": "Prismarienblokstenen", "block.minecraft.prismarine_slab": "Prismarienplaat", "block.minecraft.prismarine_stairs": "Prismarientrap", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "Pompoen", "block.minecraft.pumpkin_stem": "Po<PERSON>enstam", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON> banier", "block.minecraft.purple_bed": "Paars bed", "block.minecraft.purple_candle": "<PERSON><PERSON><PERSON> kaars", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> met p<PERSON><PERSON> kaars", "block.minecraft.purple_carpet": "<PERSON><PERSON> tapi<PERSON>t", "block.minecraft.purple_concrete": "<PERSON><PERSON> beton", "block.minecraft.purple_concrete_powder": "Paars cement", "block.minecraft.purple_glazed_terracotta": "Paarsgeglazuurd terracotta", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Paarsgekleurd glas", "block.minecraft.purple_stained_glass_pane": "Paarsgekleurde glazen ruit", "block.minecraft.purple_terracotta": "Paars terracotta", "block.minecraft.purple_wool": "Pa<PERSON>e wol", "block.minecraft.purpur_block": "Purpurblok", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_slab": "Purpurplaat", "block.minecraft.purpur_stairs": "Purpurtrap", "block.minecraft.quartz_block": "Kwartsblok", "block.minecraft.quartz_bricks": "Kwartsblokstenen", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "Kwartsplaat", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_gold_block": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_iron_block": "<PERSON><PERSON><PERSON> i<PERSON>blok", "block.minecraft.red_banner": "<PERSON><PERSON> ban<PERSON>", "block.minecraft.red_bed": "Rood bed", "block.minecraft.red_candle": "<PERSON><PERSON> kaars", "block.minecraft.red_candle_cake": "<PERSON><PERSON> met rode kaars", "block.minecraft.red_carpet": "<PERSON><PERSON>", "block.minecraft.red_concrete": "Rood beton", "block.minecraft.red_concrete_powder": "Rood cement", "block.minecraft.red_glazed_terracotta": "Roodgeglazuurd terracotta", "block.minecraft.red_mushroom": "Rode paddenstoel", "block.minecraft.red_mushroom_block": "Rood paddenstoelenblok", "block.minecraft.red_nether_brick_slab": "Rode Netherbakstenen plaat", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON> Netherbakstenen trap", "block.minecraft.red_nether_brick_wall": "Rode Netherbakstenen muur", "block.minecraft.red_nether_bricks": "Rode Netherbakstenen", "block.minecraft.red_sand": "Rood zand", "block.minecraft.red_sandstone": "<PERSON><PERSON> z<PERSON>", "block.minecraft.red_sandstone_slab": "Rode zandstenen plaat", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON> zandstenen trap", "block.minecraft.red_sandstone_wall": "Rode z<PERSON>stenen muur", "block.minecraft.red_shulker_box": "<PERSON><PERSON>", "block.minecraft.red_stained_glass": "Roodgekleurd glas", "block.minecraft.red_stained_glass_pane": "Roodgekleurde glazen ruit", "block.minecraft.red_terracotta": "Rood terracotta", "block.minecraft.red_tulip": "Rode tulp", "block.minecraft.red_wool": "<PERSON><PERSON> wol", "block.minecraft.redstone_block": "Redstoneblok", "block.minecraft.redstone_lamp": "Redstonelamp", "block.minecraft.redstone_ore": "Redstone-erts", "block.minecraft.redstone_torch": "Redstonefakkel", "block.minecraft.redstone_wall_torch": "Redstonefakkel aan muur", "block.minecraft.redstone_wire": "Redstonedraad", "block.minecraft.reinforced_deepslate": "Gewapend wrevelsteen", "block.minecraft.repeater": "Redstoneversterker", "block.minecraft.repeating_command_block": "Herhalend opdrachtblok", "block.minecraft.resin_block": "Harsblok", "block.minecraft.resin_brick_slab": "Harsbakstenen plaat", "block.minecraft.resin_brick_stairs": "<PERSON>rsbakstenen trap", "block.minecraft.resin_brick_wall": "Harsbakstenen muur", "block.minecraft.resin_bricks": "Harsbakstenen", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "Respawnanker", "block.minecraft.rooted_dirt": "Gewortelde aarde", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "Zand", "block.minecraft.sandstone": "Zandsteen", "block.minecraft.sandstone_slab": "Zandstenen plaat", "block.minecraft.sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.sandstone_wall": "<PERSON>and<PERSON><PERSON> muur", "block.minecraft.scaffolding": "Steiger", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculkkatalysator", "block.minecraft.sculk_sensor": "Sculksensor", "block.minecraft.sculk_shrieker": "<PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "Zeeaugurk", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Respawnpunt ingesteld", "block.minecraft.short_dry_grass": "Kort droog gras", "block.minecraft.short_grass": "Kort gras", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Skelettenschedel", "block.minecraft.skeleton_wall_skull": "Skelettenschedel aan muur", "block.minecraft.slime_block": "Slijmblok", "block.minecraft.small_amethyst_bud": "Kleine amethistknop", "block.minecraft.small_dripleaf": "Klein druipblad", "block.minecraft.smithing_table": "Smeedstafel", "block.minecraft.smoker": "Roker", "block.minecraft.smooth_basalt": "Glad basalt", "block.minecraft.smooth_quartz": "Glad kwartsblok", "block.minecraft.smooth_quartz_slab": "Gladde kwartsplaat", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON> k<PERSON>", "block.minecraft.smooth_red_sandstone": "Glad rood zandsteen", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON> rode zand<PERSON>en plaat", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON> rode zand<PERSON><PERSON> trap", "block.minecraft.smooth_sandstone": "Glad zandsteen", "block.minecraft.smooth_sandstone_slab": "Gladde zandstenen plaat", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON> z<PERSON>en trap", "block.minecraft.smooth_stone": "<PERSON><PERSON> steen", "block.minecraft.smooth_stone_slab": "Gladde stenen plaat", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "Sneeuwblok", "block.minecraft.soul_campfire": "Zielenkampvuur", "block.minecraft.soul_fire": "Zielenvuur", "block.minecraft.soul_lantern": "Zielenlantaarn", "block.minecraft.soul_sand": "Zielenzand", "block.minecraft.soul_soil": "Zielenaarde", "block.minecraft.soul_torch": "Zielenfakkel", "block.minecraft.soul_wall_torch": "Zielenfakkel aan muur", "block.minecraft.spawn.not_valid": "Je hebt geen bed of op<PERSON><PERSON><PERSON> respawnanker of ze waren geblokkeerd", "block.minecraft.spawner": "Monsterkooi", "block.minecraft.spawner.desc1": "<PERSON><PERSON><PERSON> met spawnei:", "block.minecraft.spawner.desc2": "Bepaalt te spawnen wezentype", "block.minecraft.sponge": "Spons", "block.minecraft.spore_blossom": "Sporebloesem", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> knop", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> deur", "block.minecraft.spruce_fence": "Sparrenhouten hek", "block.minecraft.spruce_fence_gate": "Sparrenh<PERSON><PERSON> poort", "block.minecraft.spruce_hanging_sign": "Sparrenhouten hangbord", "block.minecraft.spruce_leaves": "Sparrennaalden", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "Sparrenhouten planken", "block.minecraft.spruce_pressure_plate": "Sparrenhouten drukplaat", "block.minecraft.spruce_sapling": "Sparrenzaailing", "block.minecraft.spruce_sign": "Sparrenhouten bord", "block.minecraft.spruce_slab": "Sparrenhouten plaat", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> trap", "block.minecraft.spruce_trapdoor": "Sparrenhouten valluik", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>bord", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord aan muur", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Blokstenen plaat", "block.minecraft.stone_brick_stairs": "Blokstenen trap", "block.minecraft.stone_brick_wall": "Blokstenen muur", "block.minecraft.stone_bricks": "Blokstenen", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON> knop", "block.minecraft.stone_pressure_plate": "Stenen d<PERSON>", "block.minecraft.stone_slab": "Stenen plaat", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON> trap", "block.minecraft.stonecutter": "Steenzaag", "block.minecraft.stripped_acacia_log": "Gestripte acaciastam", "block.minecraft.stripped_acacia_wood": "Gestript acaciahout", "block.minecraft.stripped_bamboo_block": "G<PERSON><PERSON>t bamboeblok", "block.minecraft.stripped_birch_log": "Gest<PERSON><PERSON> berk<PERSON>", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON><PERSON> berken<PERSON>", "block.minecraft.stripped_cherry_log": "Gest<PERSON><PERSON> k<PERSON>", "block.minecraft.stripped_cherry_wood": "G<PERSON><PERSON><PERSON> k<PERSON>out", "block.minecraft.stripped_crimson_hyphae": "Gestripte helrode schimmeldraden", "block.minecraft.stripped_crimson_stem": "Gest<PERSON><PERSON> helrode stam", "block.minecraft.stripped_dark_oak_log": "Gestripte donkere eike<PERSON>am", "block.minecraft.stripped_dark_oak_wood": "Gestript donker eikenhout", "block.minecraft.stripped_jungle_log": "Gestrip<PERSON> oerwo<PERSON>tam", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "Gestripte mangrovestam", "block.minecraft.stripped_mangrove_wood": "Gestript mangrovehout", "block.minecraft.stripped_oak_log": "Gestripte eikenstam", "block.minecraft.stripped_oak_wood": "Gest<PERSON>t e<PERSON>nhout", "block.minecraft.stripped_pale_oak_log": "Gestripte bleke e<PERSON>am", "block.minecraft.stripped_pale_oak_wood": "Gestript bleek eikenhout", "block.minecraft.stripped_spruce_log": "Gestrip<PERSON> sparrenstam", "block.minecraft.stripped_spruce_wood": "G<PERSON><PERSON>t sparrenhout", "block.minecraft.stripped_warped_hyphae": "Gestripte spookachtige schimmeldraden", "block.minecraft.stripped_warped_stem": "Gest<PERSON>te spookachtige stam", "block.minecraft.structure_block": "Constructieblok", "block.minecraft.structure_void": "Constructieleegte", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Zonnebloem", "block.minecraft.suspicious_gravel": "Verdacht grind", "block.minecraft.suspicious_sand": "Verdacht zand", "block.minecraft.sweet_berry_bush": "Zoetbesstruik", "block.minecraft.tall_dry_grass": "Hoog droog gras", "block.minecraft.tall_grass": "Hoog gras", "block.minecraft.tall_seagrass": "<PERSON><PERSON> zee<PERSON>s", "block.minecraft.target": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terracotta", "block.minecraft.test_block": "Testblok", "block.minecraft.test_instance_block": "Testinstantieblok", "block.minecraft.tinted_glass": "Getint glas", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-explosies zijn uitgeschakeld", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Fakkelbloem", "block.minecraft.torchflower_crop": "Fakkelbloemgewas", "block.minecraft.trapped_chest": "<PERSON><PERSON> met v<PERSON><PERSON><PERSON>", "block.minecraft.trial_spawner": "Be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire": "Struikeldraad", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "Buiskoraal", "block.minecraft.tube_coral_block": "Buiskoraalblok", "block.minecraft.tube_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_wall_fan": "Buiskoraalwaai<PERSON> aan muur", "block.minecraft.tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tufstenen blokstenen plaat", "block.minecraft.tuff_brick_stairs": "Tufstenen blokstenen trap", "block.minecraft.tuff_brick_wall": "Tufstenen blokstenen muur", "block.minecraft.tuff_bricks": "Tufstenen blokstenen", "block.minecraft.tuff_slab": "Tufstenen plaat", "block.minecraft.tuff_stairs": "Tufstenen trap", "block.minecraft.tuff_wall": "Tufstenen muur", "block.minecraft.turtle_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "Verwikkelde klimop", "block.minecraft.twisting_vines_plant": "Verwikkelde klimplant", "block.minecraft.vault": "<PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "Leegtelucht", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON> aan muur", "block.minecraft.warped_button": "Spookachtige houten knop", "block.minecraft.warped_door": "Spookachtige houten deur", "block.minecraft.warped_fence": "Spookachtig houten hek", "block.minecraft.warped_fence_gate": "Spookachtige houten poort", "block.minecraft.warped_fungus": "Spookachtige zwam", "block.minecraft.warped_hanging_sign": "Spookachtig houten hangbord", "block.minecraft.warped_hyphae": "Spookachtige schimmeldraden", "block.minecraft.warped_nylium": "Spookachtig nylium", "block.minecraft.warped_planks": "Spookachtige houten planken", "block.minecraft.warped_pressure_plate": "Spookachtige houten drukplaat", "block.minecraft.warped_roots": "Spookachtige wortels", "block.minecraft.warped_sign": "Spooka<PERSON><PERSON> houten bord", "block.minecraft.warped_slab": "Spookachtige houten plaat", "block.minecraft.warped_stairs": "Spooka<PERSON>ige houten trap", "block.minecraft.warped_stem": "Spo<PERSON><PERSON><PERSON> stam", "block.minecraft.warped_trapdoor": "Spookachtig houten valluik", "block.minecraft.warped_wall_hanging_sign": "Spookachtig houten uithangbord", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> houten bord aan muur", "block.minecraft.warped_wart_block": "Spookachtig kruidblok", "block.minecraft.water": "Water", "block.minecraft.water_cauldron": "Ketel met water", "block.minecraft.waxed_chiseled_copper": "Ingewassen bewerkt koperblok", "block.minecraft.waxed_copper_block": "Ingewassen koperblok", "block.minecraft.waxed_copper_bulb": "Ingewassen koperen lamp", "block.minecraft.waxed_copper_door": "Ingewassen koperen deur", "block.minecraft.waxed_copper_grate": "Ingewassen koperen rooster", "block.minecraft.waxed_copper_trapdoor": "Ingewassen koperen valluik", "block.minecraft.waxed_cut_copper": "Ingewassen gesneden koperblok", "block.minecraft.waxed_cut_copper_slab": "Ingewassen gesneden koperen plaat", "block.minecraft.waxed_cut_copper_stairs": "Ingewassen gesneden koperen trap", "block.minecraft.waxed_exposed_chiseled_copper": "Ingewassen blootgesteld bewerkt koperblok", "block.minecraft.waxed_exposed_copper": "Ingewassen blootgesteld koperblok", "block.minecraft.waxed_exposed_copper_bulb": "Ingewassen blootgestelde koperen lamp", "block.minecraft.waxed_exposed_copper_door": "Ingewassen blootgestelde koperen deur", "block.minecraft.waxed_exposed_copper_grate": "Ingewassen blootgesteld koperen rooster", "block.minecraft.waxed_exposed_copper_trapdoor": "Ingewassen blootgesteld koperen valluik", "block.minecraft.waxed_exposed_cut_copper": "Ingewassen blootgesteld gesneden koperblok", "block.minecraft.waxed_exposed_cut_copper_slab": "Ingewassen blootgestelde gesneden koperen plaat", "block.minecraft.waxed_exposed_cut_copper_stairs": "Ingewassen blootgestelde gesneden koperen trap", "block.minecraft.waxed_oxidized_chiseled_copper": "Ingewassen geoxideerd bewerkt koperblok", "block.minecraft.waxed_oxidized_copper": "Ingewassen geoxideerd <PERSON>lok", "block.minecraft.waxed_oxidized_copper_bulb": "Ingewassen geoxideerde koperen lamp", "block.minecraft.waxed_oxidized_copper_door": "Ingewassen geoxideerde koperen deur", "block.minecraft.waxed_oxidized_copper_grate": "Ingewassen geoxideerd koperen rooster", "block.minecraft.waxed_oxidized_copper_trapdoor": "Ingewassen geoxideerd koperen valluik", "block.minecraft.waxed_oxidized_cut_copper": "Ingewassen geoxideerd gesneden koperblok", "block.minecraft.waxed_oxidized_cut_copper_slab": "Ingewassen geoxideerde gesneden koperen plaat", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Ingewassen geoxideerde gesneden koperen trap", "block.minecraft.waxed_weathered_chiseled_copper": "Ingewassen verweerd bewerkt koperblok", "block.minecraft.waxed_weathered_copper": "Ingewassen verweerd k<PERSON>lo<PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "Ingewassen verweerde koperen lamp", "block.minecraft.waxed_weathered_copper_door": "Ingewassen verweerde koperen deur", "block.minecraft.waxed_weathered_copper_grate": "Ingewassen verweerd koperen rooster", "block.minecraft.waxed_weathered_copper_trapdoor": "Ingewassen verweerd koperen valluik", "block.minecraft.waxed_weathered_cut_copper": "Ingewassen verweerd gesneden koperblok", "block.minecraft.waxed_weathered_cut_copper_slab": "Ingewassen verweerde gesneden koperen plaat", "block.minecraft.waxed_weathered_cut_copper_stairs": "Ingewassen verweerde gesneden koperen trap", "block.minecraft.weathered_chiseled_copper": "Verweerd bewerkt koperblok", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "Verweerde koperen lamp", "block.minecraft.weathered_copper_door": "Verweerde koperen deur", "block.minecraft.weathered_copper_grate": "Verweerd koperen rooster", "block.minecraft.weathered_copper_trapdoor": "Verweerd koperen valluik", "block.minecraft.weathered_cut_copper": "Verweerd gesneden koperblok", "block.minecraft.weathered_cut_copper_slab": "Verweerde gesneden koperen plaat", "block.minecraft.weathered_cut_copper_stairs": "Verweerde gesneden koperen trap", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON><PERSON> klim<PERSON>lant", "block.minecraft.wet_sponge": "Natte spons", "block.minecraft.wheat": "Tarwe", "block.minecraft.white_banner": "<PERSON><PERSON> banier", "block.minecraft.white_bed": "Wit bed", "block.minecraft.white_candle": "<PERSON><PERSON> kaars", "block.minecraft.white_candle_cake": "<PERSON><PERSON> met witte kaars", "block.minecraft.white_carpet": "Wit tapijt", "block.minecraft.white_concrete": "Wit beton", "block.minecraft.white_concrete_powder": "Wit cement", "block.minecraft.white_glazed_terracotta": "Witgeglazuurd terracotta", "block.minecraft.white_shulker_box": "<PERSON><PERSON>", "block.minecraft.white_stained_glass": "Witgekleurd glas", "block.minecraft.white_stained_glass_pane": "Witgekleurde glazen ruit", "block.minecraft.white_terracotta": "Wit terracotta", "block.minecraft.white_tulip": "Witte tulp", "block.minecraft.white_wool": "Witte wol", "block.minecraft.wildflowers": "<PERSON> bloemen", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Witherskelettenschedel", "block.minecraft.wither_skeleton_wall_skull": "Witherskelettenschedel aan muur", "block.minecraft.yellow_banner": "<PERSON><PERSON>", "block.minecraft.yellow_bed": "Geel bed", "block.minecraft.yellow_candle": "<PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> met gele kaars", "block.minecraft.yellow_carpet": "<PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON> beton", "block.minecraft.yellow_concrete_powder": "Geel cement", "block.minecraft.yellow_glazed_terracotta": "Geelgeglazuurd terracotta", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "Geelgekleurd glas", "block.minecraft.yellow_stained_glass_pane": "Geelgekleurde glazen ruit", "block.minecraft.yellow_terracotta": "Geel terracotta", "block.minecraft.yellow_wool": "<PERSON><PERSON> wol", "block.minecraft.zombie_head": "Zombiehoofd", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON> aan muur", "book.byAuthor": "door %1$s", "book.edit.title": "Boekbewerkingsmenu", "book.editTitle": "Titel:", "book.finalizeButton": "Signeren en sluiten", "book.finalizeWarning": "Let op! Als een boek een<PERSON><PERSON> is gesigneerd, is het niet meer te bewerken!", "book.generation.0": "Origineel", "book.generation.1": "<PERSON><PERSON>", "book.generation.2": "<PERSON><PERSON> van een kopie", "book.generation.3": "Gehavend", "book.invalid.tag": "* ongeldige boekeigenschap *", "book.pageIndicator": "pagina %1$s van %2$s", "book.page_button.next": "Volgende pagina", "book.page_button.previous": "Vorige pagina", "book.sign.title": "Boeksigneringsmenu", "book.sign.titlebox": "Titel", "book.signButton": "Signeren", "book.view.title": "Boekweergavemenu", "build.tooHigh": "Hoogtelimiet voor bouwen is %s", "chat.cannotSend": "Chatbericht verzenden mislukt", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klik om te teleporteren", "chat.copy": "<PERSON><PERSON><PERSON><PERSON>", "chat.copy.click": "Klik om te kopiëren", "chat.deleted_marker": "Dit bericht werd verwijderd door de server.", "chat.disabled.chain_broken": "Chat uitgeschakeld door een onderbroken keten. Probeer opnieuw verbinding te maken.", "chat.disabled.expiredProfileKey": "Chat uitgeschakeld wegens verlopen openbare profielsleutel. Probeer opnieuw verbinding te maken.", "chat.disabled.invalid_command_signature": "Opdracht bevatte onverwachte of ontbrekende opdrachtargumenthandtekeningen.", "chat.disabled.invalid_signature": "Chat heeft een ongeldige handtekening. Probeer opnieuw verbinding te maken.", "chat.disabled.launcher": "Chat uitgeschakeld door launcherinstelling. Kan bericht niet versturen.", "chat.disabled.missingProfileKey": "Chat uitgeschakeld wegens ontbrekende openbare profielsleutel. Probeer opnieuw verbinding te maken.", "chat.disabled.options": "Chat uitgeschakeld in spelinstellingen.", "chat.disabled.out_of_order_chat": "Chatber<PERSON>ten niet op volgorde ontvangen. Is je systeemtijd veranderd?", "chat.disabled.profile": "Chat niet toegestaan door accountinstellingen. Druk nogmaals op '%s' voor meer informatie.", "chat.disabled.profile.moreInfo": "Chat niet toegestaan door accountinstellingen. Kan geen berichten verzenden of weergeven.", "chat.editBox": "chat", "chat.filtered": "Gefilterd door de server.", "chat.filtered_full": "De server heeft je bericht voor sommige spelers verborgen.", "chat.link.confirm": "Weet je zeker dat je de volgende website wilt openen?", "chat.link.confirmTrusted": "Wil je deze link openen of kopiëren?", "chat.link.open": "Openen in browser", "chat.link.warning": "Open nooit links van mensen die je niet vertrou<PERSON>t!", "chat.queue": "[+%s regel(s) in de wachtrij]", "chat.square_brackets": "[%s]", "chat.tag.error": "De server verzond een ongeldig bericht.", "chat.tag.modified": "Bericht aangepast door de server. Origineel:", "chat.tag.not_secure": "Ongeverifieerd bericht. Kan niet gerapporteerd worden.", "chat.tag.system": "Serverbericht. Kan niet gerapporteerd worden.", "chat.tag.system_single_player": "Serverbericht.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s voltooide uitdaging %s", "chat.type.advancement.goal": "%s bereikte doel %s", "chat.type.advancement.task": "%s heeft vooruitgang %s behaald", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Bericht sturen naar team", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s zegt %s", "chat.validation_error": "Chatvalidatiefout", "chat_screen.message": "Bericht om te versturen: %s", "chat_screen.title": "Chatscherm", "chat_screen.usage": "<PERSON><PERSON><PERSON> bericht in en druk op Enter om te versturen", "chunk.toast.checkLog": "Bekijk het logboek voor meer informatie", "chunk.toast.loadFailure": "Chunk laden mislukt op %s", "chunk.toast.lowDiskSpace": "Opslagruimte bijna vol!", "chunk.toast.lowDiskSpace.description": "De wereld kan mogelijk niet worden opgeslagen.", "chunk.toast.saveFailure": "Chunk opslaan mislukt op %s", "clear.failed.multiple": "Geen voorwerpen gevonden in de inventaris van %s spelers", "clear.failed.single": "Geen voorwerpen gevonden in de inventaris van %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "<PERSON><PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON><PERSON>", "color.minecraft.cyan": "Turquoise", "color.minecraft.gray": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Lichtblauw", "color.minecraft.light_gray": "Lichtgrijs", "color.minecraft.lime": "Lichtgroen", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "Roze", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "Rood", "color.minecraft.white": "Wit", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<--[HIER]", "command.context.parse_error": "%s op positie %s: %s", "command.exception": "Interpreteren van opdracht '%s' mislukt", "command.expected.separator": "<PERSON>it<PERSON><PERSON>te verwacht om een argument te beëindigen, maar trof extra gegevens aan", "command.failed": "Er trad een onverwachte fout op tijdens het uitvoeren van deze opdracht", "command.forkLimit": "Maximumaantal contexten (%s) bereikt", "command.unknown.argument": "Incorrect argument voor opdracht", "command.unknown.command": "On<PERSON><PERSON>e of incomplete opdracht. <PERSON>ie hieronder voor de foutmelding", "commands.advancement.criterionNotFound": "Vooruitgang '%1$s' bevat criterium '%2$s' niet", "commands.advancement.grant.criterion.to.many.failure": "Criterium '%s' van vooruitgang '%s' aan %s spelers geven mislukt, omdat ze dit criterium al hebben", "commands.advancement.grant.criterion.to.many.success": "Criterium '%s' van vooruitgang '%s' aan %s spelers gegeven", "commands.advancement.grant.criterion.to.one.failure": "Criterium '%s' van vooruitgang '%s' aan %s geven mislukt, omdat de speler dit criterium al heeft", "commands.advancement.grant.criterion.to.one.success": "Criterium '%s' van vooruitgang '%s' gegeven aan %s", "commands.advancement.grant.many.to.many.failure": "%s vooruitgangen geven aan %s spelers mislukt, omdat ze ze al hebben", "commands.advancement.grant.many.to.many.success": "%s vooruitgangen gegeven aan %s spelers", "commands.advancement.grant.many.to.one.failure": "%s vooruitgangen aan %s geven mislukt, omdat de speler ze al heeft", "commands.advancement.grant.many.to.one.success": "%s vooruitgangen gegeven aan %s", "commands.advancement.grant.one.to.many.failure": "Vooruitgang '%s' geven aan %s spelers mislukt, omdat ze deze al hebben", "commands.advancement.grant.one.to.many.success": "Vooruitgang '%s' gegeven aan %s spelers", "commands.advancement.grant.one.to.one.failure": "Vooruitgang '%s' geven aan %s mislukt, omdat de speler deze al heeft", "commands.advancement.grant.one.to.one.success": "Vooruitgang '%s' gegeven aan %s", "commands.advancement.revoke.criterion.to.many.failure": "Criterium '%s' van vooruitgang '%s' intrekken van %s spelers mislukt, omdat ze dit criterium niet hebben", "commands.advancement.revoke.criterion.to.many.success": "Criterium '%s' van vooruitgang '%s' ingetrokken van %s spelers", "commands.advancement.revoke.criterion.to.one.failure": "Criterium '%s' van vooruitgang '%s' intrekken van %s mislukt, omdat de speler het criterium niet heeft", "commands.advancement.revoke.criterion.to.one.success": "Criterium '%s' van vooruitgang '%s' van %s ingetrokken", "commands.advancement.revoke.many.to.many.failure": "%s vooruitgangen intrekken van %s spelers mislukt, omdat ze deze niet hebben", "commands.advancement.revoke.many.to.many.success": "%s vooruitgangen ingetrokken van %s spelers", "commands.advancement.revoke.many.to.one.failure": "%s vooruitgangen intrekken van %s mislukt, omdat de speler ze niet heeft", "commands.advancement.revoke.many.to.one.success": "%s vooruitgangen van %s ingetrokken", "commands.advancement.revoke.one.to.many.failure": "Vooruitgang '%s' intrekken van %s spelers mislukt, omdat ze deze niet hebben", "commands.advancement.revoke.one.to.many.success": "Vooruitgang '%s' ingetrokken van %s spelers", "commands.advancement.revoke.one.to.one.failure": "Vooruitgang '%s' intrekken van %s mislukt, omdat de speler deze niet heeft", "commands.advancement.revoke.one.to.one.success": "Vooruitgang '%s' ingetrokken van %s", "commands.attribute.base_value.get.success": "Basiswaarde van attribuut %s voor entiteit %s is %s", "commands.attribute.base_value.reset.success": "Basiswaarde van attribuut %s voor entiteit %s teruggezet naar standaardwaarde %s", "commands.attribute.base_value.set.success": "Basiswaarde voor attribuut %s voor entiteit %s is gezet naar %s", "commands.attribute.failed.entity": "'%s' is geen geldig entiteit voor deze opdracht", "commands.attribute.failed.modifier_already_present": "Aanpasser %s is al aanwezig op attribuut %s voor entiteit %s", "commands.attribute.failed.no_attribute": "Entiteit %s heeft attribuut %s niet", "commands.attribute.failed.no_modifier": "Attribuut %s voor entiteit %s heeft aanpasser %s niet", "commands.attribute.modifier.add.success": "Aanpasser %s aan attribuut %s voor entiteit %s toegevoegd", "commands.attribute.modifier.remove.success": "Aanpasser %s van attribuut %s voor entiteit %s verwijderd", "commands.attribute.modifier.value.get.success": "Waarde van aanpasser %s op attribuut %s voor entiteit %s is %s", "commands.attribute.value.get.success": "Waarde van attribuut %s voor entiteit %s is %s", "commands.ban.failed": "<PERSON><PERSON> g<PERSON>, omdat deze speler al geblokkeerd is", "commands.ban.success": "%s geblokkeerd: %s", "commands.banip.failed": "<PERSON><PERSON> g<PERSON>ij<PERSON>d, omdat dit IP-adres al geblokkeerd is", "commands.banip.info": "Deze blokkade heeft invloed op %s speler(s): %s", "commands.banip.invalid": "Ongeldig IP-adres of onbekende speler", "commands.banip.success": "IP-adres %s geblokkeerd: %s", "commands.banlist.entry": "%s werd geblokkeerd door %s: %s", "commands.banlist.entry.unknown": "(onbekend)", "commands.banlist.list": "%s speler(s) is/zijn geblo<PERSON>erd:", "commands.banlist.none": "Geen spelers geblokkeerd", "commands.bossbar.create.failed": "<PERSON><PERSON> met ID '%s' bestaat al", "commands.bossbar.create.success": "Aangepaste baasbalk '%s' gemaakt", "commands.bossbar.get.max": "Aangepaste baasbalk '%s' heeft %s als maximum", "commands.bossbar.get.players.none": "Voor aangepaste baasbalk '%s' zijn geen spelers online", "commands.bossbar.get.players.some": "Voor aangepaste baasbalk '%s' is/zijn %s speler(s) momenteel online: %s", "commands.bossbar.get.value": "Aangepaste baasbalk '%s' heeft %s als waarde", "commands.bossbar.get.visible.hidden": "Aangepaste baasbalk '%s' is verborgen", "commands.bossbar.get.visible.visible": "Aangepaste baasbalk '%s' is zich<PERSON><PERSON>ar", "commands.bossbar.list.bars.none": "Er zijn geen aangepaste baasbalken actief", "commands.bossbar.list.bars.some": "Er is/zijn %s aangepaste baasbalk(en) actief: %s", "commands.bossbar.remove.success": "Aangepaste baasbalk '%s' verwijderd", "commands.bossbar.set.color.success": "Aangepaste baasbalk '%s' van kleur veranderd", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON>, omdat dit al de kleur van deze baasbalk is", "commands.bossbar.set.max.success": "Aangepaste baasbalk '%s' heeft %s als nieuw maximum", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON>, omdat dit al het maximum van deze baasbalk is", "commands.bossbar.set.name.success": "Aangepaste baasbalk '%s' hernoemd", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON>, omdat dit al de naam van deze baa<PERSON>bal<PERSON> is", "commands.bossbar.set.players.success.none": "Aangepaste baasbalk '%s' heeft niet langer spelers", "commands.bossbar.set.players.success.some": "Aangepaste baasbalk '%s' heeft %s speler(s): %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON>, omdat deze spelers al aan de baasbalk zijn toegevoegd en niemand kan worden toegevoegd of worden verwijderd", "commands.bossbar.set.style.success": "Aangepaste baasbalk '%s' heeft een andere stijl", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON>, omdat dit al de stijl van deze baasbalk is", "commands.bossbar.set.value.success": "Aangepaste baasbalk '%s' heeft %s als nieuwe waarde", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON>, omdat dit al de waarde van deze baasbalk is", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON> g<PERSON>, omdat deze baasbalk al verborgen is", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON> g<PERSON>, omdat deze baasbalk al zichtbaar is", "commands.bossbar.set.visible.success.hidden": "Aangepaste baasbalk '%s' verborgen", "commands.bossbar.set.visible.success.visible": "Aangepaste baasbalk '%s' zichtbaar gemaakt", "commands.bossbar.unknown": "<PERSON><PERSON> <PERSON><PERSON><PERSON> geen baa<PERSON><PERSON> met ID '%s'", "commands.clear.success.multiple": "%s voorwerp(en) uit de inventaris van %s spelers verwijderd", "commands.clear.success.single": "%s voorwerp(en) uit de inventaris van %s verwijderd", "commands.clear.test.multiple": "%s overeenkomend(e) voorwerp(en) gevonden in de inventaris van %s spelers", "commands.clear.test.single": "%s overeenkomend(e) voorwerp(en) gevonden in de inventaris van %s", "commands.clone.failed": "<PERSON>n blokken geklo<PERSON>", "commands.clone.overlap": "Bron- en bestemmingsgebieden mogen niet overlappen", "commands.clone.success": "%s blok(ken) succesvol gekloond", "commands.clone.toobig": "Te veel blokken in het opgegeven gebied (maximaal %s, %s opgegeven)", "commands.damage.invulnerable": "<PERSON><PERSON><PERSON><PERSON> is onaantastbaar voor het opgegeven type schade", "commands.damage.success": "%s schadepunten toegebracht aan %s", "commands.data.block.get": "'%s' van het blok op positie %s, %s, %s na schalingsfactor %s is %s", "commands.data.block.invalid": "Het do<PERSON><PERSON> is geen bloken<PERSON>it", "commands.data.block.modified": "Blokgegevens op positie %s, %s, %s aangepast", "commands.data.block.query": "%s, %s, %s heeft de volgende blokgegevens: %s", "commands.data.entity.get": "'%s' van %s na schalingsfactor %s is %s", "commands.data.entity.invalid": "Spelergegevens aanpassen mislukt", "commands.data.entity.modified": "Entiteitgegevens van %s aangepast", "commands.data.entity.query": "%s heeft de volgende entiteitgegevens: %s", "commands.data.get.invalid": "Verkrijgen van '%s' mislukt; alleen numerieke eigenschappen zijn toeges<PERSON>an", "commands.data.get.multiple": "Dit argument accepteert een enkele NBT-waarde", "commands.data.get.unknown": "Verkrij<PERSON> van '%s' mislukt; eigenschap bestaat niet", "commands.data.merge.failed": "<PERSON><PERSON>, omdat de gespecificeerde eigenschappen al deze waarden hebben", "commands.data.modify.expected_list": "Lijst verwacht; %s gekregen", "commands.data.modify.expected_object": "Object verwacht; %s gekregen", "commands.data.modify.expected_value": "Waarde verwacht; %s gekregen", "commands.data.modify.invalid_index": "Ongeldige lijstindex: %s", "commands.data.modify.invalid_substring": "Ongeldige subtekenreeksindexen: %s tot %s", "commands.data.storage.get": "%s in opslag %s na schalingsfactor %s is %s", "commands.data.storage.modified": "Opslag %s aangepast", "commands.data.storage.query": "Opslag %s heeft de volgende inhoud: %s", "commands.datapack.create.already_exists": "<PERSON><PERSON> met de naam '%s' bestaat al", "commands.datapack.create.invalid_full_name": "Nieuwe datapakketnaam '%s' ongeldig", "commands.datapack.create.invalid_name": "Ongeldige karakters in nieuwe pakketnaam '%s'", "commands.datapack.create.io_failure": "<PERSON><PERSON> met naam '%s' maken mislukt. Bekijk de logboeken.", "commands.datapack.create.metadata_encode_failure": "Metadata encoderen mislukt voor pakket met naam '%s': %s", "commands.datapack.create.success": "<PERSON><PERSON> met naam '%s' gema<PERSON>t", "commands.datapack.disable.failed": "Gegevenspakket '%s' is niet ingeschakeld!", "commands.datapack.disable.failed.feature": "Pakket '%s' kan niet worden uitgeschakeld, omdat het onderdeel is van een ingeschakelde optie!", "commands.datapack.enable.failed": "Gegevenspakket '%s' is al ingeschakeld!", "commands.datapack.enable.failed.no_flags": "Gegevenspakket '%s' kan niet ingeschakeld worden, omdat de vereiste instellingen niet ingeschakeld zijn in deze wereld: %s!", "commands.datapack.list.available.none": "<PERSON>r zijn geen gegevenspakketten meer beschik<PERSON>ar", "commands.datapack.list.available.success": "Er is/zijn %s gegevenspakket(ten) beschikbaar: %s", "commands.datapack.list.enabled.none": "<PERSON>r zijn geen gegevenspakketten ingeschakeld", "commands.datapack.list.enabled.success": "Er is/zijn %s gegevenspakket(ten) ingeschakeld: %s", "commands.datapack.modify.disable": "Gegevenspakket %s wordt uitgeschakeld", "commands.datapack.modify.enable": "Gegevenspakket %s wordt ingeschakeld", "commands.datapack.unknown": "Gegevenspakket '%s' is onbekend", "commands.debug.alreadyRunning": "Tickprofileerder is al gestart", "commands.debug.function.noRecursion": "Kan niet opsporen vanuit een functie", "commands.debug.function.noReturnRun": "Opsporen niet mogelijk bij /return run", "commands.debug.function.success.multiple": "%s opdracht(en) van %s functies opgespoord en geplaatst in bestand %s", "commands.debug.function.success.single": "%s opdracht(en) van functie '%s' opgespoord en geplaatst in bestand %s", "commands.debug.function.traceFailed": "Functie opsporen mislukt", "commands.debug.notRunning": "Tickprofileerder nog niet gestart", "commands.debug.started": "Tickprofilering gestart", "commands.debug.stopped": "Tickprofilering gestopt na %s seconde(n) en %s tick(s) (%s tick(s) per seconde)", "commands.defaultgamemode.success": "De standaardspelmodus is nu %s", "commands.deop.failed": "<PERSON><PERSON> g<PERSON>, omdat deze speler geen beheerder is", "commands.deop.success": "%s is niet langer een <PERSON><PERSON><PERSON>r", "commands.dialog.clear.multiple": "Dialoogvenster voor %s spelers gewist", "commands.dialog.clear.single": "Dialoogvenster voor %s gewist", "commands.dialog.show.multiple": "Dialoogvenster aan %s spelers getoond", "commands.dialog.show.single": "Dialoogvenster aan %s getoond", "commands.difficulty.failure": "<PERSON><PERSON><PERSON> werd niet g<PERSON>, omdat dit al '%s' is", "commands.difficulty.query": "Het niveau is %s", "commands.difficulty.success": "Het niveau is ingesteld op %s", "commands.drop.no_held_items": "Entiteit kan geen voorwerpen vasthouden", "commands.drop.no_loot_table": "Entiteit %s heeft geen buittabel", "commands.drop.no_loot_table.block": "Blok %s heeft geen buittabel", "commands.drop.success.multiple": "%s voorwerpen laten vallen", "commands.drop.success.multiple_with_table": "%s voorwerpen uit buittabel %s laten vallen", "commands.drop.success.single": "%s (×%s) laten vallen", "commands.drop.success.single_with_table": "%s (×%s) laten vallen uit buittabel %s", "commands.effect.clear.everything.failed": "<PERSON><PERSON> heeft geen te verwijderen effecten", "commands.effect.clear.everything.success.multiple": "Alle effecten verwijderd van %s doelwitten", "commands.effect.clear.everything.success.single": "Alle effecten verwijderd van %s", "commands.effect.clear.specific.failed": "<PERSON><PERSON> heeft niet het opgevraagde effect", "commands.effect.clear.specific.success.multiple": "Effect %s verwij<PERSON>d van %s doelwitten", "commands.effect.clear.specific.success.single": "Effect %s verwij<PERSON>d van %s", "commands.effect.give.failed": "Dit effect toepassen mislukt (het doel is óf immuun voor effecten óf heeft iets sterkers)", "commands.effect.give.success.multiple": "Effect %s toegevoegd aan %s doelwitten", "commands.effect.give.success.single": "Effect %s toegevoegd aan %s", "commands.enchant.failed": "<PERSON><PERSON> g<PERSON>, omdat de doelen óf geen voorwerp in de hand hebben óf de betovering kan niet worden toegevoegd", "commands.enchant.failed.entity": "'%s' is geen geldig entiteit voor deze opdracht", "commands.enchant.failed.incompatible": "'%s' ondersteunt deze betovering niet", "commands.enchant.failed.itemless": "%s houdt geen voorwerp vast", "commands.enchant.failed.level": "%s is hoger dan het ondersteunde maximumniveau %s voor die betovering", "commands.enchant.success.multiple": "Betovering %s toegepast op %s entiteiten", "commands.enchant.success.single": "Betovering %s toegepast op voorwerp van %s", "commands.execute.blocks.toobig": "Te veel blokken in het opgegeven gebied (maximaal %s, %s opgegeven)", "commands.execute.conditional.fail": "Test mislukt", "commands.execute.conditional.fail_count": "Test mislukt, aantal: %s", "commands.execute.conditional.pass": "Test geslaagd", "commands.execute.conditional.pass_count": "Test geslaagd, aantal: %s", "commands.execute.function.instantiationFailure": "Functie %s instantiëren mislukt: %s", "commands.experience.add.levels.success.multiple": "%s ervaringsniveau(s) aan %s spelers gegeven", "commands.experience.add.levels.success.single": "%s ervaringsniveau(s) aan %s gegeven", "commands.experience.add.points.success.multiple": "%s ervaringspunt(en) aan %s spelers gegeven", "commands.experience.add.points.success.single": "%s ervaringspunt(en) aan %s gegeven", "commands.experience.query.levels": "%s heeft %s ervaringsniveau(s)", "commands.experience.query.points": "%s heeft %s ervaringspunten", "commands.experience.set.levels.success.multiple": "%2$s spelers hebben nu %1$s ervaringsniveau(s)", "commands.experience.set.levels.success.single": "%2$s heeft nu %1$s ervaringsniveau(s)", "commands.experience.set.points.invalid": "Ervaringspunten kunnen niet hoger zijn dan het maximumaantal punten van het huidige niveau van de s<PERSON>er", "commands.experience.set.points.success.multiple": "%2$s spelers hebben nu %1$s ervaringspunt(en)", "commands.experience.set.points.success.single": "%2$s heeft nu %1$s ervaringspunt(en)", "commands.fill.failed": "<PERSON><PERSON> blokken gevuld", "commands.fill.success": "%s blok(ken) succesvol gevuld", "commands.fill.toobig": "Te veel blokken in het opgegeven gebied (maximaal %s, %s opgegeven)", "commands.fillbiome.success": "Klimaten ingesteld tussen %s, %s, %s en %s, %s, %s", "commands.fillbiome.success.count": "%s klimaat/klimaten ingesteld tussen %s, %s, %s en %s, %s, %s", "commands.fillbiome.toobig": "Te veel blokken in het opgegeven gebied (maximaal %s, %s opgegeven)", "commands.forceload.added.failure": "Er werden geen chunks gemarkeerd als geforceerd geladen", "commands.forceload.added.multiple": "%1$s chunks (van %3$s tot %4$s) in dimensie %2$s gemarkeerd als geforceerd geladen", "commands.forceload.added.none": "Er werden geen geforceerd geladen chunks in dimensie %s gevonden", "commands.forceload.added.single": "Chunk %s in dimensie %s gemarkeerd als geforceerd geladen", "commands.forceload.list.multiple": "%s geforceerd geladen chunks in dimensie %s werden gevonden op %s", "commands.forceload.list.single": "Geforceerd geladen chunk in dimensie %s werd gevonden op %s", "commands.forceload.query.failure": "Chunk %s in dimensie %s is niet gemarkeerd als geforceerd geladen", "commands.forceload.query.success": "Chunk %s in dimensie %s gemarkeerd als geforceerd geladen", "commands.forceload.removed.all": "Alle geforceerd geladen chunks in dimensie %s zijn niet langer aldus gemarkeerd", "commands.forceload.removed.failure": "Er werden geen chunks niet langer gemarkeerd voor geforceerd laden", "commands.forceload.removed.multiple": "%1$s chunks (van %3$s tot %4$s) in dimensie %2$s worden niet langer gemarkeerd als geforceerd geladen", "commands.forceload.removed.single": "Chunk %s in dimensie %s wordt niet langer geforceerd geladen", "commands.forceload.toobig": "Te veel chunks in het opgegeven gebied (maximaal %s, %s opgegeven)", "commands.function.error.argument_not_compound": "Ongeldige argumentsoort: %s; COMPOUND verwacht", "commands.function.error.missing_argument": "Argument %2$s van functie %1$s ontbreekt", "commands.function.error.missing_arguments": "Argumenten van functie %s ontbreken", "commands.function.error.parse": "Tijdens het instantiëren van macro %s veroorzaakte de opdracht '%s' een fout: %s", "commands.function.instantiationFailure": "Functie %s instantiëren mislukt: %s", "commands.function.result": "Functie %s retourneerde %s", "commands.function.scheduled.multiple": "Functies %s gestart", "commands.function.scheduled.no_functions": "Kon functies met naam '%s' niet vinden", "commands.function.scheduled.single": "Functie %s gestart", "commands.function.success.multiple": "%s opdracht(en) van %s functies uitgevoerd", "commands.function.success.multiple.result": "%s functies uitgevoerd", "commands.function.success.single": "%s opdracht(en) van functie '%s' uitgevoerd", "commands.function.success.single.result": "Functie '%2$s' retourneerde %1$s", "commands.gamemode.success.other": "Spelmodus van %s gewijzigd naar %s", "commands.gamemode.success.self": "Eigen spelmodus gewijzigd naar %s", "commands.gamerule.query": "Spelregel %s is op dit moment '%s'", "commands.gamerule.set": "Spelregel %s gewijzigd naar '%s'", "commands.give.failed.toomanyitems": "Meer dan %s van %s geven mislukt", "commands.give.success.multiple": "%2$s (×%1$s) gegeven aan %3$s spelers", "commands.give.success.single": "%2$s (×%1$s) gegeven aan %3$s", "commands.help.failed": "<PERSON><PERSON><PERSON><PERSON> opdracht of te weinig permissies", "commands.item.block.set.success": "Een inventarisvak op %s, %s, %s vervangen door %s", "commands.item.entity.set.success.multiple": "<PERSON>en inventarisvak van %s entiteiten vervangen door %s", "commands.item.entity.set.success.single": "<PERSON>en inventarisvak van %s vervangen door %s", "commands.item.source.no_such_slot": "De bron heeft inventarisvak %s niet", "commands.item.source.not_a_container": "Bronpositie %s, %s, %s bevat geen inventaris", "commands.item.target.no_changed.known_item": "Geen doelen accepteren %s in inventarisvak %s", "commands.item.target.no_changes": "Geen doelen accepteren voorwerpen in inventarisvak %s", "commands.item.target.no_such_slot": "Het doel heeft inventarisvak %s niet", "commands.item.target.not_a_container": "Doelpositie %s, %s, %s bevat geen inventaris", "commands.jfr.dump.failed": "JFR-opname dumpen mislukt: %s", "commands.jfr.start.failed": "JFR-profilering starten mislukt", "commands.jfr.started": "JFR-profilering gestart", "commands.jfr.stopped": "JFR-profilering gestopt en gedumpt in %s", "commands.kick.owner.failed": "<PERSON><PERSON> in LAN-spel niet uit het spel zetten", "commands.kick.singleplayer.failed": "Spelers kunnen niet uit het spel worden gezet als je offline alleen speelt", "commands.kick.success": "%s werd uit het spel gezet: %s", "commands.kill.success.multiple": "%s entiteit(en) gedood", "commands.kill.success.single": "%s gedood", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Er zijn %s van de maximaal %s spelers online: %s", "commands.locate.biome.not_found": "<PERSON><PERSON> van klimaatsoort '%s' binnen een redelijke afstand mislukt", "commands.locate.biome.success": "Dichtstbijzijnde %s bevindt zich op %s (%s blokken afstand)", "commands.locate.poi.not_found": "<PERSON><PERSON> van herkenningspunt '%s' binnen een redelijke afstand mislukt", "commands.locate.poi.success": "Dichtstbijzijnde %s bevindt zich op %s (%s blokken afstand)", "commands.locate.structure.invalid": "Constructietype '%s' bestaat niet", "commands.locate.structure.not_found": "Constructietype '%s' in de buurt vinden mislukt", "commands.locate.structure.success": "Dichtstbijzijnde %s bevindt zich op %s (%s blokken afstand)", "commands.message.display.incoming": "%s fluistert naar jou: %s", "commands.message.display.outgoing": "Je fluistert naar %s: %s", "commands.op.failed": "<PERSON><PERSON> g<PERSON>, omdat deze speler al beheerder is", "commands.op.success": "%s is nu een serverbeheerder", "commands.pardon.failed": "<PERSON><PERSON> g<PERSON>, omdat deze speler niet geb<PERSON> is", "commands.pardon.success": "%s g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.pardonip.failed": "<PERSON><PERSON> g<PERSON>d, omdat dit IP-adres niet geblo<PERSON>erd is", "commands.pardonip.invalid": "Ongeldig IP-adres", "commands.pardonip.success": "IP-adres %s gedeblokkeerd", "commands.particle.failed": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> was voor niemand z<PERSON>", "commands.particle.success": "Deeltje %s wordt getoond", "commands.perf.alreadyRunning": "Prestatieprofileerder is al gestart", "commands.perf.notRunning": "Prestatieprofileerder nog niet gestart", "commands.perf.reportFailed": "Debugrapport maken mislukt", "commands.perf.reportSaved": "Debugrapport opgeslagen als %s", "commands.perf.started": "<PERSON><PERSON><PERSON> met 10 seconden prestaties profileren (gebruik '/perf stop' om vroegtijdig te stoppen)", "commands.perf.stopped": "Prestatieprofilering gestopt na %s seconde(n) en %s tick(s) (%s tick(s) per seconde)", "commands.place.feature.failed": "Kenmerk plaatsen mislukt", "commands.place.feature.invalid": "Kenmerktype '%s' bestaat niet", "commands.place.feature.success": "'%s' geplaatst op %s, %s, %s", "commands.place.jigsaw.failed": "Puzzelblok genereren mislukt", "commands.place.jigsaw.invalid": "Sjabloonpoeltype '%s' bestaat niet", "commands.place.jigsaw.success": "Puzzelblok gegenereerd op %s, %s, %s", "commands.place.structure.failed": "Constructie plaatsen mislukt", "commands.place.structure.invalid": "Constructietype '%s' bestaat niet", "commands.place.structure.success": "Constructie '%s' gegenereerd op %s, %s, %s", "commands.place.template.failed": "Sjabloon plaatsen mislukt", "commands.place.template.invalid": "Sjabloon-id '%s' bestaat niet", "commands.place.template.success": "Sjabloon '%s' geladen op %s, %s, %s", "commands.playsound.failed": "Het geluid is te ver weg om gehoord te worden", "commands.playsound.success.multiple": "Geluid %s afgespeeld voor %s spelers", "commands.playsound.success.single": "Geluid %s afgespeeld voor %s", "commands.publish.alreadyPublished": "Samen spelen wordt al gehost op poort %s", "commands.publish.failed": "Lokaal spel hosten mislukt", "commands.publish.started": "Lokaal spel wordt gehost op poort %s", "commands.publish.success": "Samen spelen wordt gehost op poort %s", "commands.random.error.range_too_large": "Bereik om willekeurige waarde uit te kiezen mag niet groter dan 2147483646 zijn", "commands.random.error.range_too_small": "Bereik om willekeurige waarde uit te kiezen mag niet kleiner dan 2 zijn", "commands.random.reset.all.success": "%s willek<PERSON><PERSON> reeks(en) gereset", "commands.random.reset.success": "Willekeurige reeks %s gereset", "commands.random.roll": "%s wierp %s (tussen %s en %s)", "commands.random.sample.success": "Willekeurige waarde: %s", "commands.recipe.give.failed": "<PERSON><PERSON> nieuwe recepten geleerd", "commands.recipe.give.success.multiple": "%s recept(en) vrijgespeeld voor %s spelers", "commands.recipe.give.success.single": "%s recept(en) vrijgespeeld voor %s", "commands.recipe.take.failed": "<PERSON>n nieuwe recepten werden afgenomen", "commands.recipe.take.success.multiple": "%s recept(en) teruggenomen van %s spelers", "commands.recipe.take.success.single": "%s recept(en) teruggenomen van %s", "commands.reload.failure": "<PERSON><PERSON><PERSON> mislukt; oude gegevens worden behouden", "commands.reload.success": "Herladen!", "commands.ride.already_riding": "%s berijdt %s al", "commands.ride.dismount.success": "%s stopte %s te berijden", "commands.ride.mount.failure.cant_ride_players": "Spel<PERSON> kunnen niet worden bereden", "commands.ride.mount.failure.generic": "%s kon %s niet beri<PERSON>den", "commands.ride.mount.failure.loop": "Entiteit kan zich<PERSON><PERSON> of zijn passagiers niet berijden", "commands.ride.mount.failure.wrong_dimension": "Kan entiteit niet berijden in andere dimensie", "commands.ride.mount.success": "%s begon %s te berijden", "commands.ride.not_riding": "%s berijdt geen voertuig", "commands.rotate.success": "%s gedraaid", "commands.save.alreadyOff": "Opslaan is al uitgeschakeld", "commands.save.alreadyOn": "Opslaan is al ingeschakeld", "commands.save.disabled": "Automatisch opslaan is nu uitgeschakeld", "commands.save.enabled": "Automatisch opslaan ingeschakeld", "commands.save.failed": "<PERSON><PERSON> m<PERSON>jk om het spel op te slaan (is er genoeg vrije ruimte?)", "commands.save.saving": "Spel wordt opgeslagen (dit kan even duren!)", "commands.save.success": "Spel opgeslagen", "commands.schedule.cleared.failure": "<PERSON>n <PERSON> met ID %s gevonden", "commands.schedule.cleared.success": "%s planning(en) met ID %s verwijderd", "commands.schedule.created.function": "Functie '%s' gepland na %s tick(s) op speltijd %s", "commands.schedule.created.tag": "Eigenschap '%s' gepland na %s tick(s) op speltijd %s", "commands.schedule.macro": "Een macro kon niet ingepland worden", "commands.schedule.same_tick": "Plannen voor huidige tick kan niet", "commands.scoreboard.objectives.add.duplicate": "<PERSON>en doel heeft al deze naam", "commands.scoreboard.objectives.add.success": "Doel '%s' gemaakt", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON> g<PERSON>, omdat het weergavevak al leeg is", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON>, omdat het weergavevak al dat doel toont", "commands.scoreboard.objectives.display.cleared": "Elk doel op weergavevak %s verwijderd", "commands.scoreboard.objectives.display.set": "Weergavevak %s toont nu doel '%s'", "commands.scoreboard.objectives.list.empty": "<PERSON>r zijn geen do<PERSON>n", "commands.scoreboard.objectives.list.success": "Er is/zijn %s doel(en): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Doel %s wordt niet langer automatisch bijgewerkt", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Doel %s wordt vanaf nu automatisch bijgewerkt", "commands.scoreboard.objectives.modify.displayname": "<PERSON>erga<PERSON><PERSON><PERSON> van '%s' naar '%s' veranderd", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Standaardgetalindeling gewist van doel %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Standaardgetalindeling gewijzigd van doel %s", "commands.scoreboard.objectives.modify.rendertype": "Weergavesoort van doel %s veranderd", "commands.scoreboard.objectives.remove.success": "Do<PERSON> '%s' verwi<PERSON>derd", "commands.scoreboard.players.add.success.multiple": "%s toegevoegd aan %s voor %s entiteiten", "commands.scoreboard.players.add.success.single": "%1$s toegevoegd aan %2$s (nu %4$s) voor %3$s", "commands.scoreboard.players.display.name.clear.success.multiple": "Weergavenaam gewist van %s entiteiten in %s", "commands.scoreboard.players.display.name.clear.success.single": "Weergavenaam gewist van %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Weergavenaam van %2$s entiteiten in %3$s gewijzigd naar %1$s", "commands.scoreboard.players.display.name.set.success.single": "Weergavenaam van %2$s in %3$s gewijzigd naar %1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Getalindeling gewist voor %s entiteiten in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Getalindeling gewist voor %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Getalindeling gewijzigd voor %s entiteiten in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Getalindeling gewijzigd voor %s in %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON>, omdat de trigger al ingeschakeld is", "commands.scoreboard.players.enable.invalid": "Inschakelen werkt alleen op triggerdoelen", "commands.scoreboard.players.enable.success.multiple": "Trigger '%s' voor %s entiteiten ingeschakeld", "commands.scoreboard.players.enable.success.single": "Trigger '%s' ingeschakeld voor %s", "commands.scoreboard.players.get.null": "<PERSON>erk<PERSON><PERSON><PERSON> van waarde van '%s' van %s mislukt, omdat geen gezet is", "commands.scoreboard.players.get.success": "%s heeft %s %s", "commands.scoreboard.players.list.empty": "Er worden geen entiteiten gevolgd", "commands.scoreboard.players.list.entity.empty": "%s heeft geen scores", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s heeft %s score(s):", "commands.scoreboard.players.list.success": "%s entiteit(en) wordt/worden gevolgd: %s", "commands.scoreboard.players.operation.success.multiple": "%s gewijzigd voor %s entiteiten", "commands.scoreboard.players.operation.success.single": "%1$s ingesteld op %3$s voor %2$s", "commands.scoreboard.players.remove.success.multiple": "%s verwijderd van %s voor %s entiteiten", "commands.scoreboard.players.remove.success.single": "%1$s verwi<PERSON><PERSON><PERSON> van %2$s (nu %4$s) voor %3$s", "commands.scoreboard.players.reset.all.multiple": "Alle scores gereset van %s entiteiten", "commands.scoreboard.players.reset.all.single": "Alle scores gereset voor %s", "commands.scoreboard.players.reset.specific.multiple": "%s gereset voor %s entiteiten", "commands.scoreboard.players.reset.specific.single": "%s gereset voor %s", "commands.scoreboard.players.set.success.multiple": "%1$s ingesteld op %3$s voor %2$s entiteiten", "commands.scoreboard.players.set.success.single": "%1$s ingesteld op %3$s voor %2$s", "commands.seed.success": "Startwaarde: %s", "commands.setblock.failed": "<PERSON><PERSON><PERSON><PERSON> van blok mislukt", "commands.setblock.success": "Blok op %s, %s, %s gewijzigd", "commands.setidletimeout.success": "Maximale tijd die een speler inactief mag zijn ingesteld op %s minuut/minuten", "commands.setidletimeout.success.disabled": "De maximale tijd dat een speler inactief mag zijn uitgeschakeld", "commands.setworldspawn.failure.not_overworld": "Het wereldspawnpunt kan alleen worden ingesteld in de bovenwereld", "commands.setworldspawn.success": "Wereldspawnpunt ingesteld op %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Spawnpunt van %6$s spelers ingesteld op %1$s, %2$s, %3$s [%4$s] in %5$s", "commands.spawnpoint.success.single": "Spawnpunt van %6$s ingesteld op %1$s, %2$s, %3$s [%4$s] in %5$s", "commands.spectate.not_spectator": "%s zit niet in de toeschouwermodus", "commands.spectate.self": "Je kunt jezelf niet <PERSON>", "commands.spectate.success.started": "%s aan het <PERSON><PERSON>", "commands.spectate.success.stopped": "Je schouwt niet langer een entiteit toe", "commands.spreadplayers.failed.entities": "%s entiteit(en) rondom %s, %s verspreiden mislukt (te veel entiteiten voor de ruimte - probeer een verspreiding van maximaal %s)", "commands.spreadplayers.failed.invalid.height": "Waarde %s voor maxHeight ongeldig. Hoger dan wereldminimum %s verwacht.", "commands.spreadplayers.failed.teams": "%s team(s) rondom %s, %s verspreiden mislukt (te veel entiteiten voor de ruimte - probeer een verspreiding van maximaal %s)", "commands.spreadplayers.success.entities": "%s entiteit(en) verspreid over %s, %s, met een gemiddelde afstand van %s blok(ken) tussen entiteit", "commands.spreadplayers.success.teams": "%s team(s) verspreid over %s, %s, met een gemiddelde afstand van %s blok(ken) tussen teams", "commands.stop.stopping": "Server stoppen...", "commands.stopsound.success.source.any": "Alle geluiden van bron '%s' gestopt", "commands.stopsound.success.source.sound": "Geluid '%s' gestopt van bron '%s'", "commands.stopsound.success.sourceless.any": "Alle geluiden gestopt", "commands.stopsound.success.sourceless.sound": "Geluid '%s' gestopt", "commands.summon.failed": "Entiteit oproepen mislukt", "commands.summon.failed.uuid": "Entiteit oproepen mislukt vanwege dubbele UUID's", "commands.summon.invalidPosition": "Ongeldige positie voor oproepen", "commands.summon.success": "Nieuw(e) %s opgeroepen", "commands.tag.add.failed": "<PERSON><PERSON> heeft al die eigenschap of heeft er te veel", "commands.tag.add.success.multiple": "Eigenschap '%s' toegevoegd aan %s entiteiten", "commands.tag.add.success.single": "Eigenschap '%s' toegevoegd aan %s", "commands.tag.list.multiple.empty": "<PERSON><PERSON>s entiteiten hebben eigenschappen", "commands.tag.list.multiple.success": "De %s entiteiten hebben in totaal %s eigenschap(pen): %s", "commands.tag.list.single.empty": "%s heeft geen eigenschappen", "commands.tag.list.single.success": "%s heeft %s eigenschap(pen): %s", "commands.tag.remove.failed": "<PERSON><PERSON> heeft deze eigenschap niet", "commands.tag.remove.success.multiple": "Eigenschap '%s' verwijderd van %s entiteiten", "commands.tag.remove.success.single": "Eigenschap '%s' verwijderd van %s", "commands.team.add.duplicate": "Een team heeft al deze naam", "commands.team.add.success": "Team %s gemaakt", "commands.team.empty.success": "%s lid/leden van team %s verwijderd", "commands.team.empty.unchanged": "<PERSON><PERSON> g<PERSON>, omdat dit team al leeg is", "commands.team.join.success.multiple": "%s leden aan team %s toege<PERSON>egd", "commands.team.join.success.single": "%s toegevoegd aan team %s", "commands.team.leave.success.multiple": "%s leden uit elk team verwijderd", "commands.team.leave.success.single": "%s verwijderd uit elk team", "commands.team.list.members.empty": "Team %s heeft geen leden", "commands.team.list.members.success": "Team %s heeft %s lid/leden: %s", "commands.team.list.teams.empty": "Er zijn geen teams", "commands.team.list.teams.success": "Er is/zijn %s team(s): %s", "commands.team.option.collisionRule.success": "Contactregel voor team %s ingesteld op '%s'", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON>, omdat de contactregel al deze waarde heeft", "commands.team.option.color.success": "<PERSON><PERSON><PERSON> van team %s gewi<PERSON><PERSON>d naar %s", "commands.team.option.color.unchanged": "<PERSON><PERSON>, omdat dit team al die kleur heeft", "commands.team.option.deathMessageVisibility.success": "Overlijdensberichtzichtbaarheid van team %s ingesteld op '%s'", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON>, omdat de overlijdensberichtzichtbaarheid al deze waarde heeft", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON>d, omdat vriendschappelijk vuur al uitgeschakeld is voor dit team", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON>, omdat vriendschappelijk vuur al ingeschakeld is voor dit team", "commands.team.option.friendlyfire.disabled": "Vriendschappelijk vuur voor team %s uitgeschakeld", "commands.team.option.friendlyfire.enabled": "Vriendschappelijk vuur voor team %s ingeschakeld", "commands.team.option.name.success": "<PERSON><PERSON> van team %s bijgewerkt", "commands.team.option.name.unchanged": "<PERSON><PERSON>, omdat dit team al die naam heeft", "commands.team.option.nametagVisibility.success": "Naamkaartzichtbaarheid van team %s ingesteld op '%s'", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON>, omda<PERSON> de naamzichtbaarheid al deze waarde heeft", "commands.team.option.prefix.success": "Teamvoorvoegsel gezet op '%s'", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON>d, omdat dit team onzichtbare leden al niet kan zien", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON> g<PERSON>i<PERSON>d, omdat dit team onzichtbare leden al kan zien", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s kan niet langer onzichtbare teamleden zien", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s kan on<PERSON><PERSON><PERSON>e teamleden zien", "commands.team.option.suffix.success": "Teamachtervoegsel gezet op '%s'", "commands.team.remove.success": "Team '%s' ver<PERSON><PERSON><PERSON><PERSON>", "commands.teammsg.failed.noteam": "Je moet lid zijn van een team om je team een bericht te sturen", "commands.teleport.invalidPosition": "Ongeldige positie voor teleportatie", "commands.teleport.success.entity.multiple": "%s entiteiten geteleporteerd naar %s", "commands.teleport.success.entity.single": "%s naar %s geteleporteerd", "commands.teleport.success.location.multiple": "%s entiteiten geteleporteerd naar %s, %s, %s", "commands.teleport.success.location.single": "%s geteleporteerd naar %s, %s, %s", "commands.test.batch.starting": "Omgeving %s groep %s starten", "commands.test.clear.error.no_tests": "<PERSON>n te wissen testen gevonden", "commands.test.clear.success": "%s constructie(s) gewist", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klik om naar klembord te kopiëren", "commands.test.create.success": "Testopstelling gemaakt voor test %s", "commands.test.error.no_test_containing_pos": "Kon geen testinstantie vinden die %s, %s, %s bevat", "commands.test.error.no_test_instances": "Geen testinstanties gevonden", "commands.test.error.non_existant_test": "Kon test %s niet vinden", "commands.test.error.structure_not_found": "Kon testconstructie %s niet vinden", "commands.test.error.test_instance_not_found": "Kon testinstantieblokentiteit niet vinden", "commands.test.error.test_instance_not_found.position": "Kon geen testinstantieblokentiteit voor test op %s, %s, %s vinden", "commands.test.error.too_large": "De constructiegrootte moet kleiner zijn dan %s blokken in iedere richting", "commands.test.locate.done": "Lokaliseren afgerond met %s gevonden constructie(s)", "commands.test.locate.found": "Constructie op %s gevonden (afstand: %s)", "commands.test.locate.started": "Testconstructies lokaliseren gestart. Dit kan even duren...", "commands.test.no_tests": "<PERSON><PERSON> testen om uit te voeren", "commands.test.relative_position": "Relatieve positie tot %s: %s", "commands.test.reset.error.no_tests": "<PERSON>n testen gevonden om te resetten", "commands.test.reset.success": "%s constructie(s) gereset", "commands.test.run.no_tests": "<PERSON><PERSON> testen gevonden", "commands.test.run.running": "%s test(en) uitvoeren...", "commands.test.summary": "Speltest afgerond! %s test(en) uitgevoerd", "commands.test.summary.all_required_passed": "Alle vereiste testen geslaagd :)", "commands.test.summary.failed": "%s vereiste test(en) mislukt :(", "commands.test.summary.optional_failed": "%s optionele test(en) mislukt", "commands.tick.query.percentiles": "Percentielen: 50e: %sms; 95e: %sms; 99e: %sms; datasetgrootte: %s", "commands.tick.query.rate.running": "Tickfrequentiedoel: %s per seconde.\nGemiddelde tijd tussen ticks: %sms (doel: %sms)", "commands.tick.query.rate.sprinting": "Tickfrequentiedoel: %s per seconde (genegeerd, alleen ter referentie).\nGemiddelde tijd tussen ticks: %sms", "commands.tick.rate.success": "Tickfrequentiedoel op %s per seconde gezet", "commands.tick.sprint.report": "Sprint voltooid met %s ticks per seconde (%sms per tick)", "commands.tick.sprint.stop.fail": "Er is op dit moment geen ticksprint bezig", "commands.tick.sprint.stop.success": "Een ticksprint werd onderbroken", "commands.tick.status.frozen": "Het spel is bevroren", "commands.tick.status.lagging": "Het spel draait, maar kan het ingestelde tickfrequent<PERSON><PERSON>l niet halen", "commands.tick.status.running": "Het spel draait zoals verwacht", "commands.tick.status.sprinting": "Het spel sprint", "commands.tick.step.fail": "Kon het spel niet stappen. Het spel moet eerst bevroren worden.", "commands.tick.step.stop.fail": "Er is op dit moment geen tickstap bezig", "commands.tick.step.stop.success": "<PERSON>en tickstap werd onderbroken", "commands.tick.step.success": "%s tick(s) worden gestapt", "commands.time.query": "De tijd is %s", "commands.time.set": "Tijd verzet naar %s", "commands.title.cleared.multiple": "Titels voor %s spelers gewist", "commands.title.cleared.single": "Titels voor %s gewist", "commands.title.reset.multiple": "Titelinstellingen voor %s spelers gereset", "commands.title.reset.single": "Titelinstellingen voor %s gereset", "commands.title.show.actionbar.multiple": "Nieuwe werkbalktitel wordt voor %s spelers getoond", "commands.title.show.actionbar.single": "Nieuwe werkbalktitel wordt voor %s getoond", "commands.title.show.subtitle.multiple": "Nieuwe subtitel wordt voor %s spelers getoond", "commands.title.show.subtitle.single": "Nieuwe subtitel wordt voor %s getoond", "commands.title.show.title.multiple": "Nieuwe titel wordt voor %s spelers getoond", "commands.title.show.title.single": "Nieuwe titel wordt voor %s getoond", "commands.title.times.multiple": "Titeltoontijd gewijzigd voor %s spelers", "commands.title.times.single": "Titeltoontijd gewijzigd voor %s", "commands.transfer.error.no_players": "Geef ten minste één speler op om door te sturen", "commands.transfer.success.multiple": "%s spelers doorgestuurd naar %s:%s", "commands.transfer.success.single": "%s doorgestuurd naar %s:%s", "commands.trigger.add.success": "%s geactiveerd (%s toegevoegd aan de waarde)", "commands.trigger.failed.invalid": "Je kunt alleen doelen activeren die van het 'triggertype' zijn", "commands.trigger.failed.unprimed": "Je kunt dit doel nog niet activeren", "commands.trigger.set.success": "%s geactiveerd (waarde gezet op %s)", "commands.trigger.simple.success": "%s geactiveerd", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "<PERSON><PERSON> we<PERSON> in %s", "commands.waypoint.list.success": "%s wegwijzer(s) in %s: %s", "commands.waypoint.modify.color": "Wegwijzerkleur is nu %s", "commands.waypoint.modify.color.reset": "Wegwijzerkle<PERSON> gereset", "commands.waypoint.modify.style": "Wegwijzerstijl gewijzigd", "commands.weather.set.clear": "<PERSON>er ingesteld op helder", "commands.weather.set.rain": "Weer ingesteld op regen", "commands.weather.set.thunder": "Weer ingesteld op regen en onweer", "commands.whitelist.add.failed": "De<PERSON> speler staat al op de witte lijst", "commands.whitelist.add.success": "%s toege<PERSON><PERSON>d aan de witte lijst", "commands.whitelist.alreadyOff": "Witte lijst is al uitgeschakeld", "commands.whitelist.alreadyOn": "Witte lijst is al ingeschakeld", "commands.whitelist.disabled": "Witte lijst uitgeschakeld", "commands.whitelist.enabled": "Witte lijst ingeschakeld", "commands.whitelist.list": "%s speler(s) staat/staan op de witte lijst: %s", "commands.whitelist.none": "<PERSON>r staan geen spelers op de witte lijst", "commands.whitelist.reloaded": "<PERSON>itte lijst herladen", "commands.whitelist.remove.failed": "De<PERSON> speler staat al niet op de witte lijst", "commands.whitelist.remove.success": "%s ver<PERSON><PERSON><PERSON><PERSON> van de witte lijst", "commands.worldborder.center.failed": "<PERSON><PERSON>, om<PERSON><PERSON> de wereldgrens al op dit punt gecentreerd is", "commands.worldborder.center.success": "<PERSON>den van de wereldgrens ingesteld op %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON>, omdat de wereldgrensschadehoeveelheid al die hoeveelheid is", "commands.worldborder.damage.amount.success": "Wereldgrensschadehoeveelheid ingesteld op %s per blok per seconde", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON>, omdat de wereldgrensschadebuffer al deze afstand is", "commands.worldborder.damage.buffer.success": "Wereldgrensschadebuffer ingesteld op %s blok(ken)", "commands.worldborder.get": "De wereldgrens is momenteel %s blok(ken) breed", "commands.worldborder.set.failed.big": "Wereldgrens kan niet groter zijn dan %s blokken breed", "commands.worldborder.set.failed.far": "Wereldgrens kan niet verder strekken dan %s blokken", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON>, om<PERSON><PERSON> de <PERSON> al deze grootte heeft", "commands.worldborder.set.failed.small": "Wereldgrens kan niet kleiner zijn dan <PERSON><PERSON> blok breed", "commands.worldborder.set.grow": "Wereldgrens wordt vergroot naar %s blokken breed in %s seconde(n)", "commands.worldborder.set.immediate": "Wereldgrens ingesteld op %s blok(ken) breed", "commands.worldborder.set.shrink": "Wereldgrens wordt over %2$s seconde(n) verkleind tot een breedte van %1$s blok(ken)", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON>, omdat de wereldgrenswaarschuwingsafstand al deze afstand is", "commands.worldborder.warning.distance.success": "Wereldgrenswaarschuwingsafstand ingesteld op %s blok(ken)", "commands.worldborder.warning.time.failed": "<PERSON><PERSON>, om<PERSON><PERSON> de wereldgrenswaarschuwingstijd al deze tijd is", "commands.worldborder.warning.time.success": "Wereldgrenswaarschuwingstijd ingesteld op %s seconde(n)", "compliance.playtime.greaterThan24Hours": "Je speelt al langer dan 24 uur", "compliance.playtime.hours": "Je speelt al %s uur", "compliance.playtime.message": "Je dagelijkse leven kan worden aangetast als je te veel gamet", "connect.aborted": "Afgebroken", "connect.authorizing": "Aanmelden...", "connect.connecting": "<PERSON><PERSON><PERSON><PERSON> met de server...", "connect.encrypting": "Versleutelen...", "connect.failed": "Verbinden met server misluk<PERSON>", "connect.failed.transfer": "Verbinding maken mislukt tijdens het doorsturen naar de server", "connect.joining": "<PERSON>ld betreden...", "connect.negotiating": "Onderhandelen...", "connect.reconfiging": "Opnieuw instellen...", "connect.reconfiguring": "Opnieuw instellen...", "connect.transferring": "Doorsturen naar nieuwe server...", "container.barrel": "Vat", "container.beacon": "Baken", "container.beehive.bees": "Bijen: %s/%s", "container.beehive.honey": "Honing: %s/%s", "container.blast_furnace": "<PERSON><PERSON><PERSON>", "container.brewing": "Brouws<PERSON><PERSON>ard", "container.cartography_table": "Kartografietafel", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON> kist", "container.crafter": "Vervaardigingsmachine", "container.crafting": "Vervaardiging", "container.creative": "Voorwerpselectie", "container.dispenser": "Dispenser", "container.dropper": "Dropper", "container.enchant": "Betovering", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lapis lazuli", "container.enchant.lapis.one": "1 lapis lazuli", "container.enchant.level.many": "%s betoveringsniveaus", "container.enchant.level.one": "1 betoveringsniveau", "container.enchant.level.requirement": "Niveau %s vereist", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Oven", "container.grindstone_title": "Repareren en debetoveren", "container.hopper": "Voorwerptrechter", "container.inventory": "Inventaris", "container.isLocked": "%s is vergrendeld!", "container.lectern": "<PERSON><PERSON><PERSON>", "container.loom": "<PERSON><PERSON><PERSON>ou<PERSON>", "container.repair": "Repareren en benoemen", "container.repair.cost": "Betoveringskosten: %1$s", "container.repair.expensive": "Te duur!", "container.shulkerBox": "<PERSON><PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "en nog %s...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Roker", "container.spectatorCantOpen": "Openen mislukt. Inhoud nog niet gegenereerd.", "container.stonecutter": "Steenzaag", "container.upgrade": "Uitrusting upgraden", "container.upgrade.error_tooltip": "Voorwerp kan op deze manier niet geüpgraded worden", "container.upgrade.missing_template_tooltip": "Smeedssjabloon toevoegen", "controls.keybinds": "Toegewezen toetsen", "controls.keybinds.duplicateKeybinds": "Deze toets is ook in gebruik voor:\n%s", "controls.keybinds.title": "Toegewezen toetsen", "controls.reset": "Resetten", "controls.resetAll": "<PERSON><PERSON><PERSON> resetten", "controls.title": "Besturing", "createWorld.customize.buffet.biome": "Selecteer een k<PERSON>t", "createWorld.customize.buffet.title": "Buffetwereldaanpassing", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Onderaan - %s", "createWorld.customize.flat.layer.top": "Bovenaan - %s", "createWorld.customize.flat.removeLayer": "Laag verwijderen", "createWorld.customize.flat.tile": "Laagmateriaal", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON> a<PERSON>", "createWorld.customize.presets": "Sjablonen", "createWorld.customize.presets.list": "Hier zijn ook een paar die wij hebben gemaakt!", "createWorld.customize.presets.select": "Sjabloon geb<PERSON>iken", "createWorld.customize.presets.share": "Wil je je s<PERSON><PERSON><PERSON><PERSON> met i<PERSON><PERSON> <PERSON><PERSON>? Gebruik het vak hieronder!", "createWorld.customize.presets.title": "Sjabloon selecteren", "createWorld.preparing": "Wereldcreatie voorbereiden...", "createWorld.tab.game.title": "Spel", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON>", "credits_and_attribution.button.attribution": "Attributie", "credits_and_attribution.button.credits": "Vermeldingen", "credits_and_attribution.button.licenses": "Licenties", "credits_and_attribution.screen.title": "Vermeldingen en attributie", "dataPack.bundle.description": "Sc<PERSON>elt de experimentele bundel in", "dataPack.bundle.name": "Bundels", "dataPack.locator_bar.description": "<PERSON> <PERSON><PERSON> van anderen weergeven tijdens het samen spelen", "dataPack.locator_bar.name": "Opsporingsbalk", "dataPack.minecart_improvements.description": "Verbeterde voortbeweging van mijnkarren", "dataPack.minecart_improvements.name": "Mijnkarverbeteringen", "dataPack.redstone_experiments.description": "Experimentele Redstonewijzigingen", "dataPack.redstone_experiments.name": "Redstone-experimenten", "dataPack.title": "Gegevenspakketten selecteren", "dataPack.trade_rebalance.description": "Veranderd handels<PERSON><PERSON> van dorpelingen", "dataPack.trade_rebalance.name": "<PERSON><PERSON><PERSON><PERSON> van dorpelingenhandel", "dataPack.update_1_20.description": "Nieuwe functies en inhoud voor Minecraft 1.20", "dataPack.update_1_20.name": "Update 1.20", "dataPack.update_1_21.description": "Nieuwe functies en inhoud voor Minecraft 1.21", "dataPack.update_1_21.name": "Update 1.21", "dataPack.validation.back": "Terug", "dataPack.validation.failed": "Gegevenspakketvalidatie mislukt!", "dataPack.validation.reset": "Standaardpakket gebruiken", "dataPack.validation.working": "Geselecteerde gegevenspakketten valideren...", "dataPack.vanilla.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van Minecraft", "dataPack.vanilla.name": "Standaard", "dataPack.winter_drop.description": "Nieuwe functionaliteit en inhoud voor de kleine winterupdate", "dataPack.winter_drop.name": "Kleine winterupdate", "datapackFailure.safeMode": "Veilige modus", "datapackFailure.safeMode.failed.description": "<PERSON>ze wereld bevat ongeldige of beschadigde gegevens.", "datapackFailure.safeMode.failed.title": "<PERSON><PERSON> van wereld in veilige modus mislukt.", "datapackFailure.title": "Door fouten in de momenteel geselecteerde gegevenspakketten kon de wereld niet worden geladen.\nJe kunt proberen de wereld te laden met alleen het standaardgegevenspakket ('veilige modus') of je kunt teruggaan naar het hoofdmenu en het handmatig oplossen.", "death.attack.anvil": "Een vallend aambeeld plette %1$s", "death.attack.anvil.player": "<PERSON><PERSON> a<PERSON>beeld plette %1$s tijdens een gevecht met %2$s", "death.attack.arrow": "%2$s schoot %1$s dood", "death.attack.arrow.item": "%2$s schoot %1$s dood met %3$s", "death.attack.badRespawnPoint.link": "<PERSON>eel spelontwerp", "death.attack.badRespawnPoint.message": "%2$s doodde %1$s", "death.attack.cactus": "%1$s werd doodgeprikt", "death.attack.cactus.player": "%1$s liep tegen een cactus tijdens het vluchten voor %2$s", "death.attack.cramming": "%1$s werd te veel geplet", "death.attack.cramming.player": "%2$s verpletterde %1$s", "death.attack.dragonBreath": "Drakenadem roosterde %1$s", "death.attack.dragonBreath.player": "Drakenadem van %2$s roosterde %1$s", "death.attack.drown": "%1$s verdronk", "death.attack.drown.player": "%1$s verdronk tijdens het vluchten voor %2$s", "death.attack.dryout": "%1$s droogde uit", "death.attack.dryout.player": "%1$s droogde uit tijdens het vluchten voor %2$s", "death.attack.even_more_magic": "Heel veel magie doodde %1$s", "death.attack.explosion": "%1$s blies op", "death.attack.explosion.player": "%2$s blies %1$s op", "death.attack.explosion.player.item": "%2$s blies %1$s op met %3$s", "death.attack.fall": "%1$s viel te pletter", "death.attack.fall.player": "%1$s viel te pletter tijdens het vluchten voor %2$s", "death.attack.fallingBlock": "Een vallend blok plette %1$s", "death.attack.fallingBlock.player": "Een vallend blok plette %1$s tijdens een gevecht met %2$s", "death.attack.fallingStalactite": "Een vallende stalactiet spietste %1$s", "death.attack.fallingStalactite.player": "Een vallende stalactiet spietste %1$s tijdens een gevecht met %2$s", "death.attack.fireball": "%2$s doodde %1$s met een vuurbal", "death.attack.fireball.item": "%2$s met %3$s doodde %1$s met een vuurbal", "death.attack.fireworks": "%1$s ging af met een knal", "death.attack.fireworks.item": "%1$s ging af met een knal vanwege vuurwerk afgevuurd van %3$s door %2$s", "death.attack.fireworks.player": "%1$s ging af met een knal tijdens een gevecht met %2$s", "death.attack.flyIntoWall": "%1$s ervoer kinetische energie", "death.attack.flyIntoWall.player": "%1$s ervoer kinetische energie tijdens het vluchten voor %2$s", "death.attack.freeze": "%1$s bevroor", "death.attack.freeze.player": "%2$s bevroor %1$s", "death.attack.generic": "%1$s stierf", "death.attack.generic.player": "%1$s stierf vanwege %2$s", "death.attack.genericKill": "%1$s werd gedood", "death.attack.genericKill.player": "%1$s werd gedood tijdens een gevecht met %2$s", "death.attack.hotFloor": "%1$s ontdekte dat de vloer lava was", "death.attack.hotFloor.player": "%1$s liep in de gevarenzone door %2$s", "death.attack.inFire": "%1$s ging in vlammen op", "death.attack.inFire.player": "%1$s liep in een vuur tijdens een gevecht met %2$s", "death.attack.inWall": "%1$s stikte in een muur", "death.attack.inWall.player": "%1$s stikte in een muur tijdens een gevecht met %2$s", "death.attack.indirectMagic": "%2$s doodde %1$s met magie", "death.attack.indirectMagic.item": "%2$s doodde %1$s met %3$s", "death.attack.lava": "%1$s probeerde in lava te zwemmen", "death.attack.lava.player": "%1$s probeerde in lava te zwemmen om %2$s te ontlopen", "death.attack.lightningBolt": "Bliksem trof %1$s", "death.attack.lightningBolt.player": "Bliksem trof %1$s tijdens een gevecht met %2$s", "death.attack.mace_smash": "%2$s plette %1$s", "death.attack.mace_smash.item": "%2$s plette %1$s met %3$s", "death.attack.magic": "Magie doodde %1$s", "death.attack.magic.player": "Magie doodde %1$s tijdens het vluchten voor %2$s", "death.attack.message_too_long": "Eigenlijk is het bericht te lang om volledig te tonen. Sorry! Hier is de verkleinde versie: %s", "death.attack.mob": "%2$s vermoordde %1$s", "death.attack.mob.item": "%2$s vermoordde %1$s met %3$s", "death.attack.onFire": "%1$s verbrandde levend", "death.attack.onFire.item": "%1$s verbrandde levend tijdens een gevecht met %2$s hanterende %3$s", "death.attack.onFire.player": "%1$s verbrandde levend tijdens een gevecht met %2$s", "death.attack.outOfWorld": "%1$s viel uit de wereld", "death.attack.outOfWorld.player": "%1$s wilde niet in de wereld van %2$s wonen", "death.attack.outsideBorder": "%1$s verliet de begrenzingen van <PERSON> wereld", "death.attack.outsideBorder.player": "%1$s verliet de begrenzingen van de wereld tijdens een gevecht met %2$s", "death.attack.player": "%2$s vermoordde %1$s", "death.attack.player.item": "%2$s vermoordde %1$s met %3$s", "death.attack.sonic_boom": "Een sonische gil vernietigde %1$s", "death.attack.sonic_boom.item": "Een sonische gil vernietigde %1$s tijdens het vluchten voor %2$s hanterende %3$s", "death.attack.sonic_boom.player": "Een sonische gil vernietigde %1$s tijdens het vluchten voor %2$s", "death.attack.stalagmite": "<PERSON><PERSON> stalag<PERSON>t spietste %1$s", "death.attack.stalagmite.player": "Een stalagmiet spietste %1$s tijdens een gevecht met %2$s", "death.attack.starve": "%1$s verhongerde", "death.attack.starve.player": "%1$s verhongerde tijdens een gevecht met %2$s", "death.attack.sting": "%1$s werd doodgestoken", "death.attack.sting.item": "%2$s stak %1$s dood met %3$s", "death.attack.sting.player": "%2$s stak %1$s dood", "death.attack.sweetBerryBush": "Een zoetbesstruik prikte %1$s dood", "death.attack.sweetBerryBush.player": "Een zoetbesstruik prikte %1$s dood tijdens het vluchten voor %2$s", "death.attack.thorns": "%1$s werd gedood tijdens het aanvallen van %2$s", "death.attack.thorns.item": "%1$s werd gedood door %3$s tijdens het aanvallen van %2$s", "death.attack.thrown": "%2$s takelde %1$s toe", "death.attack.thrown.item": "%2$s met %3$s takelde %1$s toe", "death.attack.trident": "%2$s spietste %1$s", "death.attack.trident.item": "%2$s spietste %1$s met %3$s", "death.attack.wither": "%1$s verschrompelde", "death.attack.wither.player": "%1$s verschrompelde tijdens een gevecht met %2$s", "death.attack.witherSkull": "<PERSON><PERSON> schedel van %2$s schoot %1$s dood", "death.attack.witherSkull.item": "<PERSON><PERSON> schedel van %2$s met %3$s schoot %1$s dood", "death.fell.accident.generic": "%1$s viel van een hoge plek", "death.fell.accident.ladder": "%1$s viel van een ladder", "death.fell.accident.other_climbable": "%1$s viel tijdens het klimmen", "death.fell.accident.scaffolding": "%1$s viel van een steiger", "death.fell.accident.twisting_vines": "%1$s viel uit een verwikkelde klimop", "death.fell.accident.vines": "%1$s viel uit lianen", "death.fell.accident.weeping_vines": "%1$s viel uit een hangende klimop", "death.fell.assist": "%2$s doemde %1$s tot vallen", "death.fell.assist.item": "%2$s doemde %1$s tot vallen met %3$s", "death.fell.finish": "%1$s viel te ver en werd afgemaakt door %2$s", "death.fell.finish.item": "%1$s viel te ver en werd afgemaakt door %2$s met %3$s", "death.fell.killer": "%1$s werd gedoemd te vallen", "deathScreen.quit.confirm": "Weet je zeker dat je wilt afsluiten?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Score", "deathScreen.score.value": "Score: %s", "deathScreen.spectate": "<PERSON><PERSON>", "deathScreen.title": "Je bent doodgegaan!", "deathScreen.title.hardcore": "Game over!", "deathScreen.titleScreen": "Hoofdmenu", "debug.advanced_tooltips.help": "F3 + H = Geavanceerde tekstballonnen", "debug.advanced_tooltips.off": "Geavanceerde tekstballonnen: verborgen", "debug.advanced_tooltips.on": "Geavanceerde tekstballonnen: zichtbaar", "debug.chunk_boundaries.help": "F3 + G = Chunkgrenzen tonen", "debug.chunk_boundaries.off": "Chunkgrenzen: verborgen", "debug.chunk_boundaries.on": "Chunkgrenzen: z<PERSON><PERSON><PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = <PERSON>t wissen", "debug.copy_location.help": "F3 + C = Locatie als /tp-opdracht kopiëren. Houd F3 + C ingedrukt om het spel te crashen", "debug.copy_location.message": "Locatie gekopieerd", "debug.crash.message": "F3 + C wordt ingedrukt gehouden. Dit crasht het spel, tenzij de toetsen worden losgelaten.", "debug.crash.warning": "Spel crasht in %s...", "debug.creative_spectator.error": "Spelmodus wisselen mislukt. <PERSON><PERSON>", "debug.creative_spectator.help": "F3 + N = Tussen vorige modus en toeschouwermodus wisselen", "debug.dump_dynamic_textures": "Dynamische texturen opgeslagen in %s", "debug.dump_dynamic_textures.help": "F3 + S = Dynamische texturen dumpen", "debug.gamemodes.error": "Spelmoduswisselaar openen mislukt. Geen toestemming", "debug.gamemodes.help": "F3 + F4 = Spelmoduswisselaar openen", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "Volgende: %s", "debug.help.help": "F3 + Q = Deze lijst tonen", "debug.help.message": "Toegewezen toetsen:", "debug.inspect.client.block": "Blokgegeven<PERSON> van cli<PERSON>nt naar klembord gekopieerd", "debug.inspect.client.entity": "Entiteitgegevens van c<PERSON>nt naar klembord gekopieerd", "debug.inspect.help": "F3 + I = Entiteit- of blokgegevens kopiëren", "debug.inspect.server.block": "Blokgegevens van de server naar klembord gekopieerd", "debug.inspect.server.entity": "Entiteitgegevens van de server naar klembord gekopieerd", "debug.pause.help": "F3 + Esc = <PERSON><PERSON><PERSON> zonder pauzemenu (als pauzeren mogelijk is)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON><PERSON> op verliezen van focus", "debug.pause_focus.off": "Pauzeren bij verliezen van focus: uitgeschakeld", "debug.pause_focus.on": "Pauzeren bij verliezen van focus: ingeschakeld", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = <PERSON>ren starten/stoppen", "debug.profiling.start": "Profileren gestart voor %s seconden. Gebruik F3 + L om vroegtijdig te stoppen", "debug.profiling.stop": "Profileren gestopt. Resultaten opgeslagen in %s", "debug.reload_chunks.help": "F3 + A = Chunks herladen", "debug.reload_chunks.message": "Alle chunks aan het herladen", "debug.reload_resourcepacks.help": "F3 + T = Bronpakketten herladen", "debug.reload_resourcepacks.message": "Bronpakketten herladen", "debug.show_hitboxes.help": "F3 + B = Contactvelden tonen", "debug.show_hitboxes.off": "Contactvelden: verborgen", "debug.show_hitboxes.on": "Contactvelden: z<PERSON><PERSON><PERSON><PERSON>", "debug.version.header": "Spelversie-informatie:", "debug.version.help": "F3 + V = Spelversie-informatie", "demo.day.1": "Deze demo duurt vijf spel<PERSON>gen, doe je best!", "demo.day.2": "Dag twee", "demo.day.3": "Dag drie", "demo.day.4": "<PERSON><PERSON> vier", "demo.day.5": "Dit is je laatste dag!", "demo.day.6": "Je vijfde dag is voorbij. Gebruik %s om een schermafbeelding van je creatie te maken.", "demo.day.warning": "Je tijd is bijna om!", "demo.demoExpired": "De demotijd zit erop!", "demo.help.buy": "Nu kopen!", "demo.help.fullWrapped": "De demo duurt vijf speldagen (ongeveer 1 uur en 40 minuten). Bekijk de vooruitgangen voor hints! Veel plezier!", "demo.help.inventory": "Gebruik %1$s om je inventaris te openen", "demo.help.jump": "Spring door op %1$s te drukken", "demo.help.later": "Verder spelen!", "demo.help.movement": "Gebruik %1$s, %2$s, %3$s, %4$s en de muis om rond te lopen", "demo.help.movementMouse": "<PERSON><PERSON> rond met de muis", "demo.help.movementShort": "Beweeg door op %1$s, %2$s, %3$s of %4$s te drukken.", "demo.help.title": "Minecraftdemomodus", "demo.remainingTime": "Resterende tijd: %s", "demo.reminder": "De demoperiode is verlopen. Ko<PERSON> het spel om verder te gaan of begin een nieuwe wereld!", "difficulty.lock.question": "Weet je zeker dat je het niveau van deze wereld wilt vergrendelen? Dit zal de wereld vergrendelen op %1$s en je kunt dit later niet meer wijzigen.", "difficulty.lock.title": "Wereldniveau vergrendelen", "disconnect.endOfStream": "Einde gegevensoverdracht", "disconnect.exceeded_packet_rate": "Uit de server gezet voor het overschrijden van de gegevenslimiet", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Statusverzoek negeren", "disconnect.loginFailedInfo": "Aanmelden mislukt: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON> is uitgeschakeld. Controleer je Microsoftaccountinstellingen.", "disconnect.loginFailedInfo.invalidSession": "Ongel<PERSON><PERSON> sessie (probeer het spel en de launcher opnieuw te starten)", "disconnect.loginFailedInfo.serversUnavailable": "De verificatieservers zijn momenteel niet bere<PERSON>. Probeer het later opnieuw.", "disconnect.loginFailedInfo.userBanned": "Je bent geblo<PERSON> en kunt niet online spelen", "disconnect.lost": "Verbinding verloren", "disconnect.packetError": "Netwerkprotocolfout", "disconnect.spam": "Eruit gezet voor spammen", "disconnect.timeout": "<PERSON><PERSON> ant<PERSON>", "disconnect.transfer": "Doorgestuurd naar andere server", "disconnect.unknownHost": "Onbekende host", "download.pack.failed": "%s van %s pakketten konden niet gedownload worden", "download.pack.progress.bytes": "Voortgang: %s (totale grootte onbekend)", "download.pack.progress.percent": "Voortgang: %s%%", "download.pack.title": "Bronpakket %s van %s downloaden", "editGamerule.default": "Standaard: %s", "editGamerule.title": "Spelregels bewerken", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorptie", "effect.minecraft.bad_omen": "Slecht voorteken", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "<PERSON><PERSON><PERSON>", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Gunst der dolfijnen", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON>bestendigheid", "effect.minecraft.glowing": "Gloed", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Gezondheidsboost", "effect.minecraft.hero_of_the_village": "Held der dorpelingen", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Infestatie", "effect.minecraft.instant_damage": "Onmiddellijke schade", "effect.minecraft.instant_health": "Onmiddellijke genezing", "effect.minecraft.invisibility": "Onzichtbaarheid", "effect.minecraft.jump_boost": "Sprongversterking", "effect.minecraft.levitation": "Zweving", "effect.minecraft.luck": "Geluk", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Misselijkhe<PERSON>", "effect.minecraft.night_vision": "Nachtzicht", "effect.minecraft.oozing": "Slijmerigheid", "effect.minecraft.poison": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Overvalsonheil", "effect.minecraft.regeneration": "Regeneratie", "effect.minecraft.resistance": "Weerstand", "effect.minecraft.saturation": "Verzadiging", "effect.minecraft.slow_falling": "Valvertraging", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "<PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "Beproevingsonheil", "effect.minecraft.unluck": "Pech", "effect.minecraft.water_breathing": "Wateradem", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON>", "effect.minecraft.wind_charged": "Jagende wind", "effect.minecraft.wither": "Verschrompeling", "effect.none": "<PERSON><PERSON>en", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Hydrofiel", "enchantment.minecraft.bane_of_arthropods": "Verderf der geleedpotigen", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON> der binding", "enchantment.minecraft.blast_protection": "Explosiebescherming", "enchantment.minecraft.breach": "Bres", "enchantment.minecraft.channeling": "Supergeleiding", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Waterloper", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "Vederzacht vallen", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Vuurbescherming", "enchantment.minecraft.flame": "Vlam", "enchantment.minecraft.fortune": "Geluk", "enchantment.minecraft.frost_walker": "<PERSON><PERSON>tl<PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Oneindigheid", "enchantment.minecraft.knockback": "Terugslag", "enchantment.minecraft.looting": "Plundering", "enchantment.minecraft.loyalty": "Loyaliteit", "enchantment.minecraft.luck_of_the_sea": "Geluk der zeeën", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Multischot", "enchantment.minecraft.piercing": "Doorboren", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Projectielbescherming", "enchantment.minecraft.protection": "Bescherming", "enchantment.minecraft.punch": "Terugstoot", "enchantment.minecraft.quick_charge": "Snelladen", "enchantment.minecraft.respiration": "Ademhaling", "enchantment.minecraft.riptide": "Stortvloed", "enchantment.minecraft.sharpness": "Sc<PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON><PERSON> streling", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Zielensnel<PERSON><PERSON>", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.swift_sneak": "Snel sluipen", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Vloek der verdwijning", "enchantment.minecraft.wind_burst": "<PERSON><PERSON>", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON> boot", "entity.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.allay": "Helper", "entity.minecraft.area_effect_cloud": "Gebiedseffectwolk", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "Pijl", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON><PERSON> met kist", "entity.minecraft.bamboo_raft": "Bamboevlot", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Blokweergave", "entity.minecraft.boat": "Boot", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Vlaag", "entity.minecraft.breeze_wind_charge": "Windvlaag", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON>", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON> boot", "entity.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.chest_boat": "<PERSON><PERSON> met kist", "entity.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> met kist", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "Kabeljau<PERSON>", "entity.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Schorswaker", "entity.minecraft.creaking_transient": "Schorswaker", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> e<PERSON> boot", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Drakenvuurbal", "entity.minecraft.drowned": "Drenkeling", "entity.minecraft.egg": "Gegooid ei", "entity.minecraft.elder_guardian": "<PERSON><PERSON> bewaker", "entity.minecraft.end_crystal": "Endkristal", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker": "Oproeper", "entity.minecraft.evoker_fangs": "Opgeroepen tanden", "entity.minecraft.experience_bottle": "Gegooid magiërsextract", "entity.minecraft.experience_orb": "Ervaringsbol", "entity.minecraft.eye_of_ender": "Enderoog", "entity.minecraft.falling_block": "Vallend blok", "entity.minecraft.falling_block_type": "%s (vallend)", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON>", "entity.minecraft.fox": "Vos", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON> met oven", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Gloeiende voorwerplijst", "entity.minecraft.glow_squid": "G<PERSON>eiend<PERSON> inktvis", "entity.minecraft.goat": "Geit", "entity.minecraft.guardian": "Bewaker", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> met t<PERSON><PERSON>", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "Mum<PERSON>", "entity.minecraft.illusioner": "Illusionist", "entity.minecraft.interaction": "Interactie", "entity.minecraft.iron_golem": "IJzergolem", "entity.minecraft.item": "Voorwerp", "entity.minecraft.item_display": "Voorwerpweergave", "entity.minecraft.item_frame": "Voorwerplijst", "entity.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boot", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.killer_bunny": "<PERSON><PERSON> m<PERSON><PERSON>", "entity.minecraft.leash_knot": "Le<PERSON><PERSON>uwknoop", "entity.minecraft.lightning_bolt": "Bliksemschicht", "entity.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmakubus", "entity.minecraft.mangrove_boat": "Mangrovehouten boot", "entity.minecraft.mangrove_chest_boat": "Mangrovehouten boot met kist", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Onheilspellende voorwerpspawner", "entity.minecraft.painting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON> e<PERSON> boot", "entity.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Papegaai", "entity.minecraft.phantom": "Fantoom", "entity.minecraft.pig": "Varken", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglinbruut", "entity.minecraft.pillager": "Rover", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "IJsbeer", "entity.minecraft.potion": "Drank", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Verwoester", "entity.minecraft.salmon": "Zalm", "entity.minecraft.sheep": "Sc<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Skelet", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Slijmkubus", "entity.minecraft.small_fireball": "Kleine vuurbal", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Sneeuwgolem", "entity.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON>", "entity.minecraft.spectral_arrow": "Spectrale pijl", "entity.minecraft.spider": "Spin", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON> drank", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "entity.minecraft.squid": "Inktvis", "entity.minecraft.stray": "Verdwaalde", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Tekstweergave", "entity.minecraft.tnt": "Aangestoken TNT", "entity.minecraft.tnt_minecart": "<PERSON><PERSON><PERSON> met TNT", "entity.minecraft.trader_llama": "Handelaarslama", "entity.minecraft.trident": "Drietand", "entity.minecraft.tropical_fish": "Tropische vis", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Zwarte zeilvindoktersvis", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "Ornamentkoraalvlinder", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON>ek<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "Rode cichlide", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "entity.minecraft.tropical_fish.predefined.16": "Rode snapper", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Cichlide", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Siamese kempvis", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Koningssnapper", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Bediller", "entity.minecraft.tropical_fish.type.spotty": "Gevlekte vis", "entity.minecraft.tropical_fish.type.stripey": "Gestreepte lo<PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Zonnestraal", "entity.minecraft.turtle": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vex": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "Harnassmid", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Kartograaf", "entity.minecraft.villager.cleric": "Geestelijke", "entity.minecraft.villager.farmer": "Boer", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Lee<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliothecar<PERSON>", "entity.minecraft.villager.mason": "<PERSON>eenhouwer", "entity.minecraft.villager.nitwit": "Onbenul", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Gereedschapssmid", "entity.minecraft.villager.weaponsmith": "Wapensmid", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "Rondreizende handelaar", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Windvlaag", "entity.minecraft.witch": "<PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherskelet", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Zombiedorpeling", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON>", "entity.not_summonable": "Kan entiteit met type '%s' niet oproepen", "event.minecraft.raid": "Overval", "event.minecraft.raid.defeat": "Verloren", "event.minecraft.raid.defeat.full": "Overval - verloren", "event.minecraft.raid.raiders_remaining": "%s overvaller(s) over", "event.minecraft.raid.victory": "Overwinning", "event.minecraft.raid.victory.full": "Overval - overwinning", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "Oerwoudverkennerskaart", "filled_map.explorer_swamp": "Moerasverkennerskaart", "filled_map.id": "Nr. %s", "filled_map.level": "(niveau %s/%s)", "filled_map.locked": "Statisch", "filled_map.mansion": "Bosverkennerskaart", "filled_map.monument": "Oceaanverkennerskaart", "filled_map.scale": "Schaal 1:%s", "filled_map.trial_chambers": "Beproevingskamerkaart", "filled_map.unknown": "<PERSON><PERSON><PERSON><PERSON> kaart", "filled_map.village_desert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_plains": "Veldendorp<PERSON>art", "filled_map.village_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_snowy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_taiga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON> put", "flat_world_preset.minecraft.classic_flat": "Klassiek vlak", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Bovenwereld", "flat_world_preset.minecraft.redstone_ready": "Klaar voor Redstone", "flat_world_preset.minecraft.snowy_kingdom": "Besneeuwd koninkrijk", "flat_world_preset.minecraft.the_void": "De leegte", "flat_world_preset.minecraft.tunnelers_dream": "Mijnwerkersdroom", "flat_world_preset.minecraft.water_world": "Waterwereld", "flat_world_preset.unknown": "???", "gameMode.adventure": "Avontuurmodus", "gameMode.changed": "Jo<PERSON>w spelmodus werd gewijzigd naar %s", "gameMode.creative": "Creatieve modus", "gameMode.hardcore": "Hardcoremodus", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "Overlevingsmodus", "gamerule.allowFireTicksAwayFromPlayer": "Tick vuur op een afstand van de <PERSON>er", "gamerule.allowFireTicksAwayFromPlayer.description": "Bepaalt of vuur en lava getickt kunnen worden op een afstand van meer dan acht chunks van een speler", "gamerule.announceAdvancements": "Vooruitgangen aankondigen", "gamerule.blockExplosionDropDecay": "Bij explosies veroorzaakt door interactie, laten sommige blokken zichzelf niet vallen", "gamerule.blockExplosionDropDecay.description": "Sommige gevallen blokken veroorzaakt door explosies van blokinteracties, gaan verloren in de explosie.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "<PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Spawnen", "gamerule.category.updates": "Wereldupdates", "gamerule.commandBlockOutput": "Opdrachtblokkenuitvoer registreren", "gamerule.commandModificationBlockLimit": "Blokkenlimiet modificatie-opdrachten", "gamerule.commandModificationBlockLimit.description": "Aantal blokken dat in <PERSON><PERSON> keer kan worden aangepast door een opdracht zoals /fill of /clone.", "gamerule.disableElytraMovementCheck": "Dekschildbewegingscontrole uitschakelen", "gamerule.disablePlayerMovementCheck": "Spelerbewegingscontrole uitschakelen", "gamerule.disableRaids": "Overvallen uitzetten", "gamerule.doDaylightCycle": "Tijdsverloop", "gamerule.doEntityDrops": "Entiteitsuitrusting laten vallen", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON> buit van mijn<PERSON> (inclusief inventaris), voorwerplijsten, boten, etc.", "gamerule.doFireTick": "<PERSON><PERSON><PERSON> <PERSON>n", "gamerule.doImmediateRespawn": "Direct respawnen", "gamerule.doInsomnia": "Fantomen spawnen", "gamerule.doLimitedCrafting": "Recept vereisen voor verwerking", "gamerule.doLimitedCrafting.description": "Indien ingeschakeld kunnen spelers alleen ontgrendelde recepten maken.", "gamerule.doMobLoot": "Wezenbuit laten vallen", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON> buit van wezens, inclusief ervaringsbollen.", "gamerule.doMobSpawning": "<PERSON>zens spawnen", "gamerule.doMobSpawning.description": "Sommige entiteiten hebben mogelijk aparte regels.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON><PERSON><PERSON> spawnen", "gamerule.doTileDrops": "Blokken laten vallen", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON> buit van blokken, inclusief ervaringsbollen.", "gamerule.doTraderSpawning": "Rondreizende handelaren spawnen", "gamerule.doVinesSpread": "<PERSON>nen verspreiden zich", "gamerule.doVinesSpread.description": "Be<PERSON><PERSON>t of lianen zich willek<PERSON>ig verspreiden naar aangrenzende blokken. Heeft geen effect op andere liaansoorten zoals hangende en verwikkelde klimop.", "gamerule.doWardenSpawning": "Hoeders spawnen", "gamerule.doWeatherCycle": "Weer updaten", "gamerule.drowningDamage": "Verdrinkingsschade toedienen", "gamerule.enderPearlsVanishOnDeath": "Gegooide Enderparels verdwijnen bij dood", "gamerule.enderPearlsVanishOnDeath.description": "Of de door een speler gegooide Enderparels verdwijnen wanneer die speler doodgaat.", "gamerule.entitiesWithPassengersCanUsePortals": "Entiteiten met passagiers kunnen portalen gebruiken", "gamerule.entitiesWithPassengersCanUsePortals.description": "Entiteiten met passagiers kunnen door Nether-, End- en Endpoortportalen teleporteren.", "gamerule.fallDamage": "Valschade <PERSON>", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "Dode spelers vergeven", "gamerule.forgiveDeadPlayers.description": "<PERSON>ze neutrale wezens zijn niet langer boos wanneer hun doels<PERSON>er in de buurt sterft.", "gamerule.freezeDamage": "Vorstschade toedienen", "gamerule.globalSoundEvents": "Wereldwijde gel<PERSON>", "gamerule.globalSoundEvents.description": "Het geluid van bepaalde gebeurtenissen wordt overal gehoord, zoals het spawnen van een baas.", "gamerule.keepInventory": "Inventaris behouden na dood", "gamerule.lavaSourceConversion": "Lava wordt bronblok", "gamerule.lavaSourceConversion.description": "Als stromende lava aan twee zijden wordt geflankeerd door lavabronblokken, verandert het in een bronblok.", "gamerule.locatorBar": "Speleropsporingsbalk inschakelen", "gamerule.locatorBar.description": "Als dit ingeschakeld is, wordt een balk weergegeven die de richting van spelers aangeeft.", "gamerule.logAdminCommands": "Beheerdersopdrachten registreren", "gamerule.maxCommandChainLength": "Maximale lengte gekoppelde opdrachtblokken", "gamerule.maxCommandChainLength.description": "Geldt voor gekoppelde opdrachtblokken en functies.", "gamerule.maxCommandForkCount": "Opdrachtcontextlimiet", "gamerule.maxCommandForkCount.description": "Maximumaantal contexten die gebruikt kunnen worden door opdrachten als 'execute as'.", "gamerule.maxEntityCramming": "Entiteitsoppropdrempelwaarde", "gamerule.minecartMaxSpeed": "Maximale s<PERSON><PERSON><PERSON>", "gamerule.minecartMaxSpeed.description": "De maximale standaardsnelheid van een voortbewegende mijnkar op land.", "gamerule.mobExplosionDropDecay": "Bij explosies veroorzaakt door wezens, laten sommige blokken zichzelf niet vallen", "gamerule.mobExplosionDropDecay.description": "Sommige gevallen blokken veroorzaakt door explosies van wezens, gaan verloren in de explosie.", "gamerule.mobGriefing": "Destructieve acties door wezens", "gamerule.naturalRegeneration": "Gezondheid regenereren", "gamerule.playersNetherPortalCreativeDelay": "Netherportaalvertraging in de creatieve modus", "gamerule.playersNetherPortalCreativeDelay.description": "Tijd in ticks hoelang een speler in de creatieve modus in een Netherportaal moet staan voordat van dimensies gewisseld wordt.", "gamerule.playersNetherPortalDefaultDelay": "Netherportaalvertraging in een niet-creatieve modus", "gamerule.playersNetherPortalDefaultDelay.description": "Tijd in ticks hoelang een speler in een niet-creatieve modus in een Netherportaal moet staan voordat van dimensies gewisseld wordt.", "gamerule.playersSleepingPercentage": "Slaappercentage", "gamerule.playersSleepingPercentage.description": "Het percentage spelers dat moet slapen om de nacht over te slaan.", "gamerule.projectilesCanBreakBlocks": "Projectielen kunnen blokken breken", "gamerule.projectilesCanBreakBlocks.description": "Bepaalt of projectielen bij impact blokken kunnen breken die door projectielen gebroken kunnen worden.", "gamerule.randomTickSpeed": "Willekeurige ticksnelheid", "gamerule.reducedDebugInfo": "Debuginformatie verminderen", "gamerule.reducedDebugInfo.description": "<PERSON><PERSON><PERSON> van het debugscherm beperken.", "gamerule.sendCommandFeedback": "Opdrachtfeedback tonen", "gamerule.showDeathMessages": "Overlijdensberichten tonen", "gamerule.snowAccumulationHeight": "Sneeuwophopingshoogte", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gem<PERSON><PERSON> van<PERSON>, hopen op tot maximaal dit aantal lagen wanneer het sneeuwt.", "gamerule.spawnChunkRadius": "Spawn<PERSON><PERSON><PERSON><PERSON>", "gamerule.spawnChunkRadius.description": "Het aantal chunks rondom het wereldspawnpunt die geladen moeten blijven.", "gamerule.spawnRadius": "Respawnlocatieradius", "gamerule.spawnRadius.description": "Be<PERSON><PERSON><PERSON> de grootte van het gebied rondom het wereldspawnpunt waarin spelers kunnen beginnen.", "gamerule.spectatorsGenerateChunks": "Toes<PERSON>uwers kunnen terrein generen", "gamerule.tntExplodes": "Bepaalt of TNT aangestoken kan worden en kan exploderen", "gamerule.tntExplosionDropDecay": "Bij TNT-explosies laten sommige blokken zichzelf niet vallen", "gamerule.tntExplosionDropDecay.description": "Sommige gevallen blokken veroorzaakt door TNT-explosies, gaan verloren in de explosie.", "gamerule.universalAnger": "<PERSON><PERSON> boosheid", "gamerule.universalAnger.description": "Boze neutrale wezens vallen iedere speler in de buurt aan, niet alleen de speler die hen boos gemaakt heeft. Werkt het beste als 'forgiveDeadPlayers' uit staat.", "gamerule.waterSourceConversion": "Water wordt bronblok", "gamerule.waterSourceConversion.description": "Als stromend water aan twee zijden wordt geflankeerd door waterbronblokken, verandert het in een bronblok.", "generator.custom": "aangepast", "generator.customized": "oud a<PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "VERSTERKT", "generator.minecraft.amplified.info": "Let op: gewoon voor de lol! Vereist een krachtige computer.", "generator.minecraft.debug_all_block_states": "debugmodus", "generator.minecraft.flat": "supervlak", "generator.minecraft.large_biomes": "grote klimaten", "generator.minecraft.normal": "standaard", "generator.minecraft.single_biome_surface": "<PERSON><PERSON>", "generator.single_biome_caves": "grotten", "generator.single_biome_floating_islands": "zwevende e<PERSON>", "gui.abuseReport.attestation": "Door deze melding te versturen, bevestig je dat de aangedragen informatie, voor zover mogelijk, nauwkeurig en compleet is.", "gui.abuseReport.comments": "Opmerkingen", "gui.abuseReport.describe": "Door details te delen, kunnen wij een geïnformeerd besluit maken.", "gui.abuseReport.discard.content": "Als je dit scherm verlaat, verlies je deze melding en je beschrijving.\nWeet je zeker dat je dit scherm wilt sluiten?", "gui.abuseReport.discard.discard": "Sluiten en melding weggooien", "gui.abuseReport.discard.draft": "Opslaan als klad", "gui.abuseReport.discard.return": "Bewerken", "gui.abuseReport.discard.title": "Melding en beschrijving weggooien?", "gui.abuseReport.draft.content": "Wil je de bestaande melding bewerken of deze weggooien en een nieuwe maken?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Bewerken", "gui.abuseReport.draft.quittotitle.content": "Wil je de melding bewerken of weggooien?", "gui.abuseReport.draft.quittotitle.title": "Je hebt een kladversie van een melding. Deze zal verdwijnen als je je afmeldt.", "gui.abuseReport.draft.title": "<PERSON><PERSON><PERSON><PERSON> van melding bewerken?", "gui.abuseReport.error.title": "Er trad een probleem op bij het sturen van jouw melding", "gui.abuseReport.message": "Waar heb je dit wangedrag waargenomen?\nDeze informatie helpt ons bij het onderzoeken.", "gui.abuseReport.more_comments": "<PERSON><PERSON><PERSON><PERSON><PERSON> wat er gebeurd is:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> waarom je deze naam wilt melden:", "gui.abuseReport.name.reporting": "Je maakt een melding over '%s'.", "gui.abuseReport.name.title": "Spelersnaam melden", "gui.abuseReport.observed_what": "Waarom meld je dit?", "gui.abuseReport.read_info": "<PERSON><PERSON> over melden", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drugs of alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Iemand moedigt anderen aan om deel te nemen aan illegale drugsgerelateerde activiteiten of promoot alcoholconsumptie door minderjarigen.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON><PERSON> of mishandeling van een kind", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Iemand praat over of promoot on<PERSON><PERSON>t ged<PERSON> met betrekking tot kinderen.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON>, laster of een andere belediging, identiteitsfraude of onjuiste informatie", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>gt iemands reputatie, doet zich voor als iemand die diegene niet is of deelt onjuiste informatie met als doel om misbruik te plegen of anderen te misleiden.", "gui.abuseReport.reason.description": "Beschrijving:", "gui.abuseReport.reason.false_reporting": "Onterechte melding", "gui.abuseReport.reason.generic": "Ik wil deze speler melden", "gui.abuseReport.reason.generic.description": "Deze speler irriteert mij of heeft iets gedaan wat mij niet bevalt.", "gui.abuseReport.reason.harassment_or_bullying": "Lastig<PERSON>len of pesten", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON><PERSON>, bestookt of pest jou of iemand anders. Het herhaaldelijk proberen om contact te leggen met jou of iemand anders valt hier ook onder, evenals het zonder toestemming plaatsen van privégegevens van jou of iemand anders (doxen).", "gui.abuseReport.reason.hate_speech": "Haatspreken", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON>d valt jou of een andere speler aan op <PERSON> van karakteristieken van hun identiteit, zoals religie, ras of seks<PERSON><PERSON> g<PERSON>.", "gui.abuseReport.reason.imminent_harm": "Dreigende verwonding - Bedreiging om iemand anders te verwonden", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> jou of iemand and<PERSON> met het toe<PERSON><PERSON> van verwon<PERSON> in het echt.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> intiem beeldmateriaal", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON>emand praat over, vers<PERSON><PERSON><PERSON>t of promoot op een andere manier privacygevoelig en intiem beeldmateriaal.", "gui.abuseReport.reason.self_harm_or_suicide": "Dreigende verwonding - Zelfverwonding of zelfdoding", "gui.abuseReport.reason.self_harm_or_suicide.description": "Iemand d<PERSON><PERSON><PERSON> z<PERSON><PERSON><PERSON> in het echt te verwonden of praat over zelfverwonding in het echt.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins van grafische natuur met betrekking tot geslachtsorganen, seks<PERSON><PERSON> geweld en seksuele handelingen.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme of gewelddadig extremisme", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON>d <PERSON><PERSON><PERSON> over, promoot of d<PERSON><PERSON><PERSON> met het plegen van terroristische daden of gewelddadig extremisme om politieke, religieuze, ideologische of andere redenen.", "gui.abuseReport.reason.title": "Meldingscategorie selecteren", "gui.abuseReport.report_sent_msg": "We hebben je melding ontvangen. Dankjewel!\n\nOns team kijkt er zo snel mogelijk naar.", "gui.abuseReport.select_reason": "Meldingscategorie selecteren", "gui.abuseReport.send": "Melding sturen", "gui.abuseReport.send.comment_too_long": "De opmerking is te lang", "gui.abuseReport.send.error_message": "Fout ontvangen tijdens het verzenden van je melding:\n'%s'", "gui.abuseReport.send.generic_error": "Onverwachte fout aangetroffen tijdens het sturen van je melding.", "gui.abuseReport.send.http_error": "Er trad een onverwachte HTTP-fout op bij het sturen van jouw melding.", "gui.abuseReport.send.json_error": "Defecte payload aangetroffen bij het sturen van je melding.", "gui.abuseReport.send.no_reason": "Selecteer een melding<PERSON><PERSON><PERSON>ie", "gui.abuseReport.send.not_attested": "<PERSON><PERSON>, voor<PERSON>t je de melding kan versturen, bovenstaande tekst en kruis het vakje aan", "gui.abuseReport.send.service_unavailable": "Kan de misbruikmeldingsdienst niet bereiken. Zorg ervoor dat je verbonden bent met het internet en probeer opnieuw.", "gui.abuseReport.sending.title": "Melding verzenden...", "gui.abuseReport.sent.title": "Melding verstuurd", "gui.abuseReport.skin.title": "<PERSON><PERSON><PERSON>skin melden", "gui.abuseReport.title": "<PERSON><PERSON><PERSON> melden", "gui.abuseReport.type.chat": "Chatberichten", "gui.abuseReport.type.name": "Spelersnaam", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON><PERSON>", "gui.acknowledge": "Begrepen", "gui.advancements": "Vooruitgangen", "gui.all": "Alle", "gui.back": "Terug", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON> meer via de volgende link: %s", "gui.banned.description.permanent": "Jouw account is permanent geblokkeerd. Je kunt niet online of op Realms spelen.", "gui.banned.description.reason": "We hebben een melding ontvangen van slecht gedrag afkomstig van jouw account. Onze moderatoren hebben de zaak bekeken en het geïdentificeerd als %s. Dit gaat tegen de gemeenschapsstandaarden van Minecraft in.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s - %s", "gui.banned.description.temporary": "%s Tot die tijd kun je niet online of op Realms spelen.", "gui.banned.description.temporary.duration": "Jouw account is tijdelijk geschorst. Deze schorsing verloopt over %s.", "gui.banned.description.unknownreason": "We hebben een melding ontvangen van slecht gedrag afkomstig van j<PERSON>w account. Onze moderatoren hebben de zaak bekeken en bepaald dat het tegen de gemeenschapsstandaarden van Minecraft gaat.", "gui.banned.name.description": "Je huidige naam - '%s' - overtreedt onze gemeenschapsstandaarden. Je kunt alleen spelen, maar je moet je naam veranderen om online te kunnen spelen.\n\n<PERSON><PERSON> meer hierover of dien een herzieningsverzoek in op %s", "gui.banned.name.title": "Naam online niet toegestaan", "gui.banned.reason.defamation_impersonation_false_information": "Identiteitsfraude of informatie delen om anderen uit te buiten of te misleiden", "gui.banned.reason.drugs": "Verwijzingen naar illegale drugs", "gui.banned.reason.extreme_violence_or_gore": "<PERSON><PERSON><PERSON><PERSON><PERSON> van <PERSON> geweld of bloederige beelden", "gui.banned.reason.false_reporting": "Overmatige valse of onnauwkeurige meldingen", "gui.banned.reason.fraud": "<PERSON><PERSON><PERSON><PERSON> verkri<PERSON> of gebruiken van inhoud", "gui.banned.reason.generic_violation": "<PERSON><PERSON><PERSON> van gemeenschapsrichtli<PERSON>en", "gui.banned.reason.harassment_or_bullying": "Beledigend taalgebruik op een gerichte, schadeli<PERSON><PERSON> manier", "gui.banned.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON><PERSON> of discriminatie", "gui.banned.reason.hate_terrorism_notorious_figure": "Verwijzingen naar haatgroepen, terroristische organisaties of beruchte figuren", "gui.banned.reason.imminent_harm_to_person_or_property": "Intentie om schade toe te brengen aan echte personen of eigendommen", "gui.banned.reason.nudity_or_pornography": "Ontuchtig of pornografisch materiaal weergeven", "gui.banned.reason.sexually_inappropriate": "Onderwerpen of inhoud van seksuele aard", "gui.banned.reason.spam_or_advertising": "Spam of reclame", "gui.banned.skin.description": "Je huidige skin overtreedt onze gemeenschapsstandaarden. Je kunt met een standaar<PERSON><PERSON> spelen of een nieuwe uploaden.\n\n<PERSON><PERSON> meer hierover of dien een herzieningsverzoek in op %s", "gui.banned.skin.title": "<PERSON> niet toe<PERSON>an", "gui.banned.title.permanent": "Account permanent g<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.banned.title.temporary": "Account ti<PERSON><PERSON><PERSON><PERSON> ges<PERSON>", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Opmerkingen", "gui.chatReport.describe": "Door details te delen, kunnen wij een geïnformeerd besluit maken.", "gui.chatReport.discard.content": "Als je dit scherm verlaat, verlies je deze melding en je beschrijving.\nWeet je zeker dat je dit scherm wilt sluiten?", "gui.chatReport.discard.discard": "Sluiten en melding weggooien", "gui.chatReport.discard.draft": "Opslaan als klad", "gui.chatReport.discard.return": "Bewerken", "gui.chatReport.discard.title": "Melding en beschrijving weggooien?", "gui.chatReport.draft.content": "Wil je de bestaande melding bewerken of deze weggooien en een nieuwe maken?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Bewerken", "gui.chatReport.draft.quittotitle.content": "Wil je de melding bewerken of weggooien?", "gui.chatReport.draft.quittotitle.title": "Je hebt een kladversie van een melding. Deze zal verdwijnen als je je afmeldt.", "gui.chatReport.draft.title": "<PERSON><PERSON><PERSON><PERSON> van melding bewerken?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON><PERSON><PERSON> wat er gebeurd is:", "gui.chatReport.observed_what": "Waarom meld je dit?", "gui.chatReport.read_info": "<PERSON><PERSON> over melden", "gui.chatReport.report_sent_msg": "We hebben je melding ontvangen. Dankjewel!\n\nOns team kijkt er zo snel mogelijk naar.", "gui.chatReport.select_chat": "Selecteer te melden chat<PERSON><PERSON>ten", "gui.chatReport.select_reason": "Meldingscategorie selecteren", "gui.chatReport.selected_chat": "%s te melden chatbericht(en) geselecteerd", "gui.chatReport.send": "Melding sturen", "gui.chatReport.send.comments_too_long": "De opmerking is te lang", "gui.chatReport.send.no_reason": "Selecteer een melding<PERSON><PERSON><PERSON>ie", "gui.chatReport.send.no_reported_messages": "Selecteer minstens één te melden chat<PERSON>t", "gui.chatReport.send.too_many_messages": "Te veel berichten aan melding toegevoegd", "gui.chatReport.title": "<PERSON><PERSON><PERSON> melden", "gui.chatSelection.context": "Berichten rondom deze selectie worden bijgevoegd voor extra context", "gui.chatSelection.fold": "%s berichten verborgen", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s is de chat binnengekomen", "gui.chatSelection.message.narrate": "%s zei: %s om %s", "gui.chatSelection.selected": "%s/%s bericht(en) geselecteerd", "gui.chatSelection.title": "Selecteer te melden chat<PERSON><PERSON>ten", "gui.continue": "Doorgaan", "gui.copy_link_to_clipboard": "Kopiëren naar klembord", "gui.days": "%s dag(en)", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "Omlaag", "gui.entity_tooltip.type": "Type: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s geweigerde bestanden", "gui.fileDropFailure.title": "<PERSON><PERSON><PERSON> toe<PERSON>n mislukt", "gui.hours": "%s uur", "gui.loadingMinecraft": "Minecraft laden...", "gui.minutes": "%s minu(u)t(en)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s: knop", "gui.narrate.editBox": "%s: bewerkingsvak: %s", "gui.narrate.slider": "%s: s<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.narrate.tab": "%s-tab", "gui.no": "<PERSON><PERSON>", "gui.none": "<PERSON><PERSON>", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Meldingenmap openen", "gui.proceed": "Doorgaan", "gui.recipebook.moreRecipes": "Klik op de rechtermuisknop voor meer", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Zoeken...", "gui.recipebook.toggleRecipes.all": "Alles tonen", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON>t<PERSON>ar in hoogoven tonen", "gui.recipebook.toggleRecipes.craftable": "Vervaardigbaar tonen", "gui.recipebook.toggleRecipes.smeltable": "Smeltbaar tonen", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON><PERSON> tonen", "gui.report_to_server": "Melden aan server", "gui.socialInteractions.blocking_hint": "Met Microsoftaccount beheren", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON> g<PERSON><PERSON><PERSON> spelers in chat", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON> spelers verborgen in chat", "gui.socialInteractions.hidden_in_chat": "Chatberichten van %s worden verborgen", "gui.socialInteractions.hide": "Verbergen in chat", "gui.socialInteractions.narration.hide": "Berichten van %s verbergen", "gui.socialInteractions.narration.report": "Speler %s melden", "gui.socialInteractions.narration.show": "Berichten van %s tonen", "gui.socialInteractions.report": "Melden", "gui.socialInteractions.search_empty": "<PERSON><PERSON> s<PERSON> met die naam gevonden", "gui.socialInteractions.search_hint": "Zoeken...", "gui.socialInteractions.server_label.multiple": "%s - %s spelers", "gui.socialInteractions.server_label.single": "%s - %s speler", "gui.socialInteractions.show": "<PERSON><PERSON> in chat", "gui.socialInteractions.shown_in_chat": "Chatberichten van %s worden getoond", "gui.socialInteractions.status_blocked": "Geblokkeerd", "gui.socialInteractions.status_blocked_offline": "Geblokkeerd - Offline", "gui.socialInteractions.status_hidden": "Verborgen", "gui.socialInteractions.status_hidden_offline": "Verborgen - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "Alles", "gui.socialInteractions.tab_blocked": "Geblokkeerd", "gui.socialInteractions.tab_hidden": "Verborgen", "gui.socialInteractions.title": "Sociale interacties", "gui.socialInteractions.tooltip.hide": "Berichten verbergen", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON> melden", "gui.socialInteractions.tooltip.report.disabled": "Meldingsdienst niet besch<PERSON>", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON><PERSON> be<PERSON> van %s die kunnen worden gemeld", "gui.socialInteractions.tooltip.report.not_reportable": "Deze speler kan niet worden gemeld, omdat diens chatberichten niet kunnen worden geverifieerd op deze server", "gui.socialInteractions.tooltip.show": "Berichten tonen", "gui.stats": "Statistieken", "gui.toMenu": "Terug naar de serverli<PERSON>st", "gui.toRealms": "Terug naar de Realmslijst", "gui.toTitle": "Terug naar het hoofdmenu", "gui.toWorld": "Terug naar de wereldli<PERSON>st", "gui.togglable_slot": "Klik om vak uit te schakelen", "gui.up": "Omhoog", "gui.waitingForResponse.button.inactive": "Terug (%ss)", "gui.waitingForResponse.title": "Wachten op server", "gui.yes": "<PERSON>a", "hanging_sign.edit": "Bericht op hangbord bewerken", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Call", "instrument.minecraft.dream_goat_horn": "Dream", "instrument.minecraft.feel_goat_horn": "Feel", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Seek", "instrument.minecraft.sing_goat_horn": "Sing", "instrument.minecraft.yearn_goat_horn": "Yearn", "inventory.binSlot": "Voorwerp vernietigen", "inventory.hotbarInfo": "Sla werkbalk op met %1$s+%2$s", "inventory.hotbarSaved": "Werkbalk opgeslagen (draai terug met %1$s+%2$s)", "item.canBreak": "Breekt:", "item.canPlace": "P<PERSON>atsbaar op:", "item.canUse.unknown": "Onbekend", "item.color": "Kleur: %s", "item.components": "%s component(en)", "item.disabled": "Uitgeschakeld voorwerp", "item.durability": "Levensduur: %s/%s", "item.dyed": "Geverfd", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON> boot", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON><PERSON> boot met kist", "item.minecraft.allay_spawn_egg": "Helperspawnei", "item.minecraft.amethyst_shard": "Ameth<PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_shard": "Aardewerkscherf 'visser'", "item.minecraft.angler_pottery_sherd": "Aardewerkscherf 'visser'", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Aardewerkscherf 'boogschutter'", "item.minecraft.archer_pottery_sherd": "Aardewerkscherf 'boogschutter'", "item.minecraft.armadillo_scute": "Gordeldierhoornplaat", "item.minecraft.armadillo_spawn_egg": "Gordeldierenspawnei", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "Aardewerkscherf 'geheven armen'", "item.minecraft.arms_up_pottery_sherd": "Aardewerkscherf 'geheven armen'", "item.minecraft.arrow": "Pijl", "item.minecraft.axolotl_bucket": "<PERSON><PERSON> a<PERSON>", "item.minecraft.axolotl_spawn_egg": "Axolotlsp<PERSON>i", "item.minecraft.baked_potato": "Gebakken aardappel", "item.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON><PERSON> met kist", "item.minecraft.bamboo_raft": "Bamboevlot", "item.minecraft.bat_spawn_egg": "Vleermuizenspawnei", "item.minecraft.bee_spawn_egg": "Bijenspawnei", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "Biet", "item.minecraft.beetroot_seeds": "Bietenzaden", "item.minecraft.beetroot_soup": "Bietensoep", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot", "item.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "item.minecraft.black_bundle": "Zwarte bundel", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.black_harness": "<PERSON><PERSON> tuig", "item.minecraft.blade_pottery_shard": "Aardewerkscherf 'zwaard'", "item.minecraft.blade_pottery_sherd": "Aardewerkscherf 'zwaard'", "item.minecraft.blaze_powder": "Blazepoeder", "item.minecraft.blaze_rod": "Blazestaf", "item.minecraft.blaze_spawn_egg": "Blazespawnei", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON> bundel", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON>lauw ei", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON> tuig", "item.minecraft.bogged_spawn_egg": "Zomperspawnei", "item.minecraft.bolt_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.bolt_armor_trim_smithing_template.new": "Harnasversiering 'bout'", "item.minecraft.bone": "Bot", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Ingesprongenzoombanierpatroon", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "Brood", "item.minecraft.breeze_rod": "Vlaagsta<PERSON>", "item.minecraft.breeze_spawn_egg": "Vlagenspawnei", "item.minecraft.brewer_pottery_shard": "Aardewerkscherf 'brouwer'", "item.minecraft.brewer_pottery_sherd": "Aardewerkscherf 'brouwer'", "item.minecraft.brewing_stand": "Brouws<PERSON><PERSON>ard", "item.minecraft.brick": "Baksteen", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON> bundel", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON>ruin ei", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON> tuig", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "Bundel", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Kan een stapel van verschillende voorwerpen vasthouden", "item.minecraft.bundle.full": "Vol", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Aardewerkscherf 'branden'", "item.minecraft.burn_pottery_sherd": "Aardewerkscherf 'branden'", "item.minecraft.camel_spawn_egg": "Dromedarisspawnei", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON> aan een stok", "item.minecraft.cat_spawn_egg": "Kattenspawnei", "item.minecraft.cauldron": "Ketel", "item.minecraft.cave_spider_spawn_egg": "G<PERSON><PERSON><PERSON><PERSON>i", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON>nka<PERSON>", "item.minecraft.chainmail_leggings": "Maliënbroek", "item.minecraft.charcoal": "Houtskool", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON> boot", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON><PERSON> boot met kist", "item.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> met kist", "item.minecraft.chicken": "<PERSON><PERSON><PERSON> kip", "item.minecraft.chicken_spawn_egg": "Kippenspawnei", "item.minecraft.chorus_fruit": "Chorusfruit", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clock": "Klok", "item.minecraft.coal": "Steenkool", "item.minecraft.coast_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.coast_armor_trim_smithing_template.new": "Harnasversiering 'kust'", "item.minecraft.cocoa_beans": "Cacaobonen", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON>", "item.minecraft.cod_spawn_egg": "Kabeljauwspawnei", "item.minecraft.command_block_minecart": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.compass": "Kompas", "item.minecraft.cooked_beef": "Biefstuk", "item.minecraft.cooked_chicken": "Gebraden kip", "item.minecraft.cooked_cod": "Gebakken kabeljauw", "item.minecraft.cooked_mutton": "Gebraden schapenvlees", "item.minecraft.cooked_porkchop": "Gebraden varkensvlees", "item.minecraft.cooked_rabbit": "Gebraden konijnenvlees", "item.minecraft.cooked_salmon": "Gebakken zalm", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Koperstaaf", "item.minecraft.cow_spawn_egg": "Koeienspawnei", "item.minecraft.creaking_spawn_egg": "Schorswakerspawnei", "item.minecraft.creeper_banner_pattern": "Banierpatroon", "item.minecraft.creeper_banner_pattern.desc": "Creeperwapen", "item.minecraft.creeper_banner_pattern.new": "Creeperwapenbanierpatroon", "item.minecraft.creeper_spawn_egg": "Creeperspawnei", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Projectiel:", "item.minecraft.crossbow.projectile.multiple": "Projectiel: %s × %s", "item.minecraft.crossbow.projectile.single": "Projectiel: %s", "item.minecraft.cyan_bundle": "Turquoise bundel", "item.minecraft.cyan_dye": "Turquoise kleurstof", "item.minecraft.cyan_harness": "Turquoise tuig", "item.minecraft.danger_pottery_shard": "Aardewerkscherf 'gevaar'", "item.minecraft.danger_pottery_sherd": "Aardewerkscherf 'gevaar'", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> e<PERSON> boot", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> boot met kist", "item.minecraft.debug_stick": "Debugstok", "item.minecraft.debug_stick.empty": "%s heeft geen eigenschappen", "item.minecraft.debug_stick.select": "'%s' geselecteerd (%s)", "item.minecraft.debug_stick.update": "'%s' naar %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON> bi<PERSON>l", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON> la<PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON> helm", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON> schoffel", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON> ho<PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON> schep", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "Muziekplaatfragment", "item.minecraft.disc_fragment_5.desc": "Muziekplaat - 5", "item.minecraft.dolphin_spawn_egg": "Do<PERSON>ijnenspawnei", "item.minecraft.donkey_spawn_egg": "E<PERSON>spawnei", "item.minecraft.dragon_breath": "Drakenadem", "item.minecraft.dried_kelp": "Gedroogde kelp", "item.minecraft.drowned_spawn_egg": "Drenkelingenspawnei", "item.minecraft.dune_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.dune_armor_trim_smithing_template.new": "Harnasversiering 'duin'", "item.minecraft.echo_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.egg": "<PERSON>i", "item.minecraft.elder_guardian_spawn_egg": "Oudebewakerspawnei", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Betoverd boek", "item.minecraft.enchanted_golden_apple": "Betoverde gouden appel", "item.minecraft.end_crystal": "Endkristal", "item.minecraft.ender_dragon_spawn_egg": "Enderdrakenspawnei", "item.minecraft.ender_eye": "Enderoog", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "End<PERSON>nenspawnei", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON><PERSON>jtspawn<PERSON>", "item.minecraft.evoker_spawn_egg": "Oproeperspawnei", "item.minecraft.experience_bottle": "Magiërsextract", "item.minecraft.explorer_pottery_shard": "Aardewerkscherf 'ontdekker'", "item.minecraft.explorer_pottery_sherd": "Aardewerkscherf 'ontdekker'", "item.minecraft.eye_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.eye_armor_trim_smithing_template.new": "Harnasversiering 'oog'", "item.minecraft.feather": "Veer", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON>ment<PERSON>d spinnenoog", "item.minecraft.field_masoned_banner_pattern": "Metselwerkbanierpatroon", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket.flight": "Vluchtduur:", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Vuurwerkster", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Aangepast", "item.minecraft.firework_star.cyan": "Turquoise", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON>g naar", "item.minecraft.firework_star.flicker": "<PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Lichtblauw", "item.minecraft.firework_star.light_gray": "Lichtgrijs", "item.minecraft.firework_star.lime": "Lichtgroen", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "Roze", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "Rood", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON><PERSON> vorm", "item.minecraft.firework_star.shape.burst": "Explosie", "item.minecraft.firework_star.shape.creeper": "Creepervormig", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON> bal", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON> bal", "item.minecraft.firework_star.shape.star": "Stervormig", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "Wit", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flint": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON><PERSON>urs<PERSON> en staal", "item.minecraft.flow_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.flow_armor_trim_smithing_template.new": "Harnasversiering 'windstroming'", "item.minecraft.flow_banner_pattern": "Banierpatroon", "item.minecraft.flow_banner_pattern.desc": "Windstroming", "item.minecraft.flow_banner_pattern.new": "Windstromingbanierpatroon", "item.minecraft.flow_pottery_sherd": "Aardewerkscherf 'windstroming'", "item.minecraft.flower_banner_pattern": "Banierpatroon", "item.minecraft.flower_banner_pattern.desc": "Bloemwapen", "item.minecraft.flower_banner_pattern.new": "Bloemwapenbanierpatroon", "item.minecraft.flower_pot": "Bloempot", "item.minecraft.fox_spawn_egg": "Vossenspawnei", "item.minecraft.friend_pottery_shard": "Aardewerkscherf 'vriend'", "item.minecraft.friend_pottery_sherd": "Aardewerkscherf 'vriend'", "item.minecraft.frog_spawn_egg": "Kikkerspawnei", "item.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON> met oven", "item.minecraft.ghast_spawn_egg": "Ghastspawnei", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "Glazen fles", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON> me<PERSON>f", "item.minecraft.globe_banner_pattern": "Banierpatroon", "item.minecraft.globe_banner_pattern.desc": "Globe", "item.minecraft.globe_banner_pattern.new": "Globebanierpatroon", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "Gloeiende voorwerplijst", "item.minecraft.glow_squid_spawn_egg": "Gloeiendeinktvissenspawnei", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_horn": "Geitenhoorn", "item.minecraft.goat_spawn_egg": "Geitenspawnei", "item.minecraft.gold_ingot": "Goudstaaf", "item.minecraft.gold_nugget": "Goudklompje", "item.minecraft.golden_apple": "Gouden appel", "item.minecraft.golden_axe": "<PERSON><PERSON>n bijl", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON> laarzen", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON> wortel", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON> kuras", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON> helm", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON> schoffel", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON> ho<PERSON>", "item.minecraft.golden_shovel": "<PERSON><PERSON>n schep", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON><PERSON> bundel", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON><PERSON> tuig", "item.minecraft.green_bundle": "G<PERSON><PERSON> bundel", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON> tuig", "item.minecraft.guardian_spawn_egg": "Bewakerspawnei", "item.minecraft.gunpowder": "Buskruit", "item.minecraft.guster_banner_pattern": "Banierpatroon", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Rukwindbanierpatroon", "item.minecraft.guster_pottery_sherd": "Aardewerkscherf 'rukwind'", "item.minecraft.happy_ghast_spawn_egg": "Vrolijkeghastspawnei", "item.minecraft.harness": "<PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON>", "item.minecraft.heart_pottery_shard": "Aardewerkscher<PERSON> 'hart'", "item.minecraft.heart_pottery_sherd": "Aardewerkscher<PERSON> 'hart'", "item.minecraft.heartbreak_pottery_shard": "Aardewerkscherf 'gebroken hart'", "item.minecraft.heartbreak_pottery_sherd": "Aardewerkscherf 'gebroken hart'", "item.minecraft.hoglin_spawn_egg": "Hoglinspawnei", "item.minecraft.honey_bottle": "Honingfles", "item.minecraft.honeycomb": "Honingraat", "item.minecraft.hopper_minecart": "<PERSON><PERSON><PERSON> met t<PERSON><PERSON>", "item.minecraft.horse_spawn_egg": "Paardenspawnei", "item.minecraft.host_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.host_armor_trim_smithing_template.new": "Harnasversiering 'gastheer'", "item.minecraft.howl_pottery_shard": "Aardewerkscher<PERSON> 'huilen'", "item.minecraft.howl_pottery_sherd": "Aardewerkscher<PERSON> 'huilen'", "item.minecraft.husk_spawn_egg": "Mummiespawnei", "item.minecraft.ink_sac": "Inktzak", "item.minecraft.iron_axe": "IJzeren bijl", "item.minecraft.iron_boots": "IJzeren laarzen", "item.minecraft.iron_chestplate": "IJzeren kuras", "item.minecraft.iron_golem_spawn_egg": "IJzergolemspawnei", "item.minecraft.iron_helmet": "IJzeren helm", "item.minecraft.iron_hoe": "IJzeren schoffel", "item.minecraft.iron_horse_armor": "IJzeren paardenharnas", "item.minecraft.iron_ingot": "IJzerstaaf", "item.minecraft.iron_leggings": "IJzeren beenstukken", "item.minecraft.iron_nugget": "IJzerklompje", "item.minecraft.iron_pickaxe": "I<PERSON><PERSON><PERSON> ho<PERSON>", "item.minecraft.iron_shovel": "IJzeren schep", "item.minecraft.iron_sword": "IJzeren zwaard", "item.minecraft.item_frame": "Voorwerplijst", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boot", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "item.minecraft.knowledge_book": "<PERSON><PERSON> der kennis", "item.minecraft.lapis_lazuli": "<PERSON><PERSON> lazuli", "item.minecraft.lava_bucket": "Emmer lava", "item.minecraft.lead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON>", "item.minecraft.leather_horse_armor": "<PERSON><PERSON>", "item.minecraft.leather_leggings": "<PERSON><PERSON> br<PERSON>", "item.minecraft.light_blue_bundle": "Lichtblauwe bundel", "item.minecraft.light_blue_dye": "Lichtblauwe kleurstof", "item.minecraft.light_blue_harness": "Lichtblauw tuig", "item.minecraft.light_gray_bundle": "Lichtgrijze bundel", "item.minecraft.light_gray_dye": "Lichtgri<PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON>chtgri<PERSON><PERSON> tuig", "item.minecraft.lime_bundle": "Lichtgroene bundel", "item.minecraft.lime_dye": "Lichtgroene kleurstof", "item.minecraft.lime_harness": "Lichtgro<PERSON> tuig", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> drank", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank", "item.minecraft.lingering_potion.effect.fire_resistance": "Aanhoudende drank der vuurbestendigheid", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank der verwonding", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank der genezing", "item.minecraft.lingering_potion.effect.infested": "Aanhoudende drank der infestatie", "item.minecraft.lingering_potion.effect.invisibility": "Aanhoudende drank der onzichtbaarheid", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON>hou<PERSON><PERSON> drank der sprongen", "item.minecraft.lingering_potion.effect.levitation": "Aanhoudende drank der zweving", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank des geluks", "item.minecraft.lingering_potion.effect.mundane": "Aanhoudende triviale drank", "item.minecraft.lingering_potion.effect.night_vision": "Aanhoudende drank des nachtzichts", "item.minecraft.lingering_potion.effect.oozing": "Aanhoudende drank der slijmerigheid", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank des gifs", "item.minecraft.lingering_potion.effect.regeneration": "Aanhoudende drank der regeneratie", "item.minecraft.lingering_potion.effect.slow_falling": "Aanhoudende drank der valvertraging", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON>hou<PERSON><PERSON> drank der traagheid", "item.minecraft.lingering_potion.effect.strength": "Aanhoudende drank der kracht", "item.minecraft.lingering_potion.effect.swiftness": "Aanhoudende drank der vlotheid", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON> stroper<PERSON> drank", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank der schildpadmeester", "item.minecraft.lingering_potion.effect.water": "Aanhoudende waterfles", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank des wateradems", "item.minecraft.lingering_potion.effect.weakness": "Aanhoudende drank der zwakheid", "item.minecraft.lingering_potion.effect.weaving": "Aanhou<PERSON>de drank der weven", "item.minecraft.lingering_potion.effect.wind_charged": "Aanhoudende drank des jagende winds", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "Kompas wijzend naar zeilsteen", "item.minecraft.mace": "Knots", "item.minecraft.magenta_bundle": "Magenta bundel", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON>a tuig", "item.minecraft.magma_cream": "Magmacrème", "item.minecraft.magma_cube_spawn_egg": "Magmakubusspawnei", "item.minecraft.mangrove_boat": "Mangrovehouten boot", "item.minecraft.mangrove_chest_boat": "Mangrovehouten boot met kist", "item.minecraft.map": "<PERSON><PERSON> kaart", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON> melk", "item.minecraft.minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Aardewerkscherf 'mijnwerker'", "item.minecraft.miner_pottery_sherd": "Aardewerkscherf 'mijnwerker'", "item.minecraft.mojang_banner_pattern": "Banierpatroon", "item.minecraft.mojang_banner_pattern.desc": "Mojanglogo", "item.minecraft.mojang_banner_pattern.new": "Mojanglogobanierpatroon", "item.minecraft.mooshroom_spawn_egg": "Mooshroomspawnei", "item.minecraft.mourner_pottery_shard": "Aardewerkscherf 'rouw'", "item.minecraft.mourner_pottery_sherd": "Aardewerkscherf 'rouw'", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mushroom_stew": "Paddenstoelenstoofpot", "item.minecraft.music_disc_11": "Muziekplaat", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Muziekplaat", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Muziekplaat", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Muziekplaat", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Muziekplaat", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Muziekplaat", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Muziekplaat", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Muziekplaat", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Muziekplaat", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Muziekplaat", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Muziekplaat", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Muziekplaat", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Muziekplaat", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Muziekplaat", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Muziekplaat", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Muziekplaat", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Muziekplaat", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Muziekplaat", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Muziekplaat", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Muziekplaat", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Muziekplaat", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "Nautilus<PERSON><PERSON><PERSON>", "item.minecraft.nether_brick": "Netherbaksteen", "item.minecraft.nether_star": "Netherster", "item.minecraft.nether_wart": "<PERSON>herk<PERSON><PERSON>", "item.minecraft.netherite_axe": "Netherieten bijl", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON><PERSON> laarzen", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON><PERSON> kuras", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON><PERSON> helm", "item.minecraft.netherite_hoe": "<PERSON><PERSON><PERSON><PERSON> schoffel", "item.minecraft.netherite_ingot": "Netherietstaaf", "item.minecraft.netherite_leggings": "Netherieten beenstukken", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON><PERSON><PERSON> hou<PERSON>", "item.minecraft.netherite_scrap": "Netherietschroot", "item.minecraft.netherite_shovel": "Netherieten schep", "item.minecraft.netherite_sword": "Netherieten zwaard", "item.minecraft.netherite_upgrade_smithing_template": "Smeedssjabloon", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherietupgrade", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "item.minecraft.ocelot_spawn_egg": "Ocelottenspawnei", "item.minecraft.ominous_bottle": "Onheilspellende fles", "item.minecraft.ominous_trial_key": "Onheilspellende beproevingssleutel", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> bundel", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON> tuig", "item.minecraft.painting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON> e<PERSON> boot", "item.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON> boot met kist", "item.minecraft.panda_spawn_egg": "Pandaspawnei", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Papegaaienspawnei", "item.minecraft.phantom_membrane": "Fantoommemb<PERSON>an", "item.minecraft.phantom_spawn_egg": "Fantomenspawnei", "item.minecraft.pig_spawn_egg": "Varkensspawnei", "item.minecraft.piglin_banner_pattern": "Banierpatroon", "item.minecraft.piglin_banner_pattern.desc": "Snuit", "item.minecraft.piglin_banner_pattern.new": "Snoetbanierpatroon", "item.minecraft.piglin_brute_spawn_egg": "Piglinbruutspawnei", "item.minecraft.piglin_spawn_egg": "Piglinspawnei", "item.minecraft.pillager_spawn_egg": "Roverspawnei", "item.minecraft.pink_bundle": "Roze bundel", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.pink_harness": "Roze tuig", "item.minecraft.pitcher_plant": "Bekerplant", "item.minecraft.pitcher_pod": "Bekerplantpeul", "item.minecraft.plenty_pottery_shard": "Aardewerkscherf 'overvloed'", "item.minecraft.plenty_pottery_sherd": "Aardewerkscherf 'overvloed'", "item.minecraft.poisonous_potato": "Giftige aardappel", "item.minecraft.polar_bear_spawn_egg": "IJsberenspawnei", "item.minecraft.popped_chorus_fruit": "Gepoft chorusfruit", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "Aardappel", "item.minecraft.potion": "Drank", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> drank", "item.minecraft.potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> drank", "item.minecraft.potion.effect.fire_resistance": "Drank der vuurbestendigheid", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON> der verwonding", "item.minecraft.potion.effect.healing": "Dran<PERSON> der genezing", "item.minecraft.potion.effect.infested": "Drank der infestatie", "item.minecraft.potion.effect.invisibility": "Drank der onzichtbaarheid", "item.minecraft.potion.effect.leaping": "Drank der sprongen", "item.minecraft.potion.effect.levitation": "Drank der zweving", "item.minecraft.potion.effect.luck": "Drank des geluks", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON> drank", "item.minecraft.potion.effect.night_vision": "Drank des nachtzichts", "item.minecraft.potion.effect.oozing": "Drank der slijmerigheid", "item.minecraft.potion.effect.poison": "Drank des gifs", "item.minecraft.potion.effect.regeneration": "Drank der regeneratie", "item.minecraft.potion.effect.slow_falling": "Drank der valvertraging", "item.minecraft.potion.effect.slowness": "Dr<PERSON><PERSON> t<PERSON>aghei<PERSON>", "item.minecraft.potion.effect.strength": "Drank der kracht", "item.minecraft.potion.effect.swiftness": "Drank der vlotheid", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON> drank", "item.minecraft.potion.effect.turtle_master": "Dr<PERSON><PERSON> der schildpadmeester", "item.minecraft.potion.effect.water": "Waterfles", "item.minecraft.potion.effect.water_breathing": "Drank des wateradems", "item.minecraft.potion.effect.weakness": "Drank der zwakheid", "item.minecraft.potion.effect.weaving": "Dran<PERSON> der weven", "item.minecraft.potion.effect.wind_charged": "Drank des jagende winds", "item.minecraft.pottery_shard_archer": "Aardewerkscherf 'boogschutter'", "item.minecraft.pottery_shard_arms_up": "Aardewerkscherf 'geheven armen'", "item.minecraft.pottery_shard_prize": "Aardewerkscherf 'prijs'", "item.minecraft.pottery_shard_skull": "Aardewerkscher<PERSON> 'schedel'", "item.minecraft.powder_snow_bucket": "<PERSON><PERSON> p<PERSON>", "item.minecraft.prismarine_crystals": "Prismarienkristallen", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "Aardewerkscherf 'prijs'", "item.minecraft.prize_pottery_sherd": "Aardewerkscherf 'prijs'", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON>", "item.minecraft.pufferfish_spawn_egg": "Kogelvissenspawnei", "item.minecraft.pumpkin_pie": "Pompoentaart", "item.minecraft.pumpkin_seeds": "Pompoenpitten", "item.minecraft.purple_bundle": "Pa<PERSON><PERSON> bundel", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON> tuig", "item.minecraft.quartz": "Netherkwarts", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.rabbit_foot": "Konijnenpootje", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Konijnenspawnei", "item.minecraft.rabbit_stew": "Konijnenstoofpot", "item.minecraft.raiser_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.raiser_armor_trim_smithing_template.new": "Harnasversiering 'fokker'", "item.minecraft.ravager_spawn_egg": "Verwoesterspawnei", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON> koper", "item.minecraft.raw_gold": "<PERSON>uw goud", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Herstelkompas", "item.minecraft.red_bundle": "<PERSON>e bundel", "item.minecraft.red_dye": "<PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON>ood tuig", "item.minecraft.redstone": "Redstonestof", "item.minecraft.resin_brick": "Ha<PERSON>baksteen", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.rib_armor_trim_smithing_template.new": "Harnasversiering 'rib'", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON> vlees", "item.minecraft.saddle": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "<PERSON><PERSON> zal<PERSON>", "item.minecraft.salmon_spawn_egg": "Zalmspawnei", "item.minecraft.scrape_pottery_sherd": "Aardewerks<PERSON><PERSON> 'schrapen'", "item.minecraft.scute": "Hoornschild", "item.minecraft.sentry_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.sentry_armor_trim_smithing_template.new": "Harnasversiering 's<PERSON>wa<PERSON>'", "item.minecraft.shaper_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.shaper_armor_trim_smithing_template.new": "Harnasversiering 'vormer'", "item.minecraft.sheaf_pottery_shard": "Aardewerks<PERSON><PERSON> 'schoof'", "item.minecraft.sheaf_pottery_sherd": "Aardewerks<PERSON><PERSON> 'schoof'", "item.minecraft.shears": "<PERSON><PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>i", "item.minecraft.shelter_pottery_shard": "Aardewerkscherf 'schuilplaats'", "item.minecraft.shelter_pottery_sherd": "Aardewerkscherf 'schuilplaats'", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON> schild", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> schild", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> schild", "item.minecraft.shield.cyan": "Turquoise schild", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON><PERSON> schild", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON> schild", "item.minecraft.shield.light_blue": "Lichtblauw schild", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schild", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON> schild", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON> schild", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON> schild", "item.minecraft.shield.pink": "Roze schild", "item.minecraft.shield.purple": "<PERSON><PERSON> schild", "item.minecraft.shield.red": "Rood schild", "item.minecraft.shield.white": "Wit schild", "item.minecraft.shield.yellow": "<PERSON><PERSON> schild", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Shulkerspawnei", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.silence_armor_trim_smithing_template.new": "Harnasversiering 'stilte'", "item.minecraft.silverfish_spawn_egg": "Zilvervissenspawnei", "item.minecraft.skeleton_horse_spawn_egg": "Skeletpaardenspawnei", "item.minecraft.skeleton_spawn_egg": "Skelettenspawnei", "item.minecraft.skull_banner_pattern": "Banierpatroon", "item.minecraft.skull_banner_pattern.desc": "Schedelwapen", "item.minecraft.skull_banner_pattern.new": "Schedelwapenbanierpatroon", "item.minecraft.skull_pottery_shard": "Aardewerkscher<PERSON> 'schedel'", "item.minecraft.skull_pottery_sherd": "Aardewerkscher<PERSON> 'schedel'", "item.minecraft.slime_ball": "Slijmbal", "item.minecraft.slime_spawn_egg": "Slijmkubusspawnei", "item.minecraft.smithing_template": "Smeedssjabloon", "item.minecraft.smithing_template.applies_to": "<PERSON> op:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON><PERSON><PERSON> of kristal toevoegen", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Harnasstuk toevoegen", "item.minecraft.smithing_template.armor_trim.ingredients": "Staven en kristallen", "item.minecraft.smithing_template.ingredients": "Ingrediënten:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Netherietstaaf toevoegen", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamanten uitrusting", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON><PERSON> harna<PERSON>, wapen of gereedschap toevoegen", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherietstaaf", "item.minecraft.smithing_template.upgrade": "Upgrade(s): ", "item.minecraft.sniffer_spawn_egg": "Snuffelaarspawnei", "item.minecraft.snort_pottery_shard": "Aardewerkscherf 'snuffelen'", "item.minecraft.snort_pottery_sherd": "Aardewerkscherf 'snuffelen'", "item.minecraft.snout_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.snout_armor_trim_smithing_template.new": "Harnasversiering 'snoet'", "item.minecraft.snow_golem_spawn_egg": "Sneeuwgolemspawnei", "item.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spectral_arrow": "Spectrale pijl", "item.minecraft.spider_eye": "Spinnenoog", "item.minecraft.spider_spawn_egg": "Spinnenspawnei", "item.minecraft.spire_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.spire_armor_trim_smithing_template.new": "Harnasversiering 'piek'", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON> drank", "item.minecraft.splash_potion.effect.awkward": "Spattende vreemde drank", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON> drank", "item.minecraft.splash_potion.effect.fire_resistance": "Spattende drank der vuurbestendigheid", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON> drank der verwonding", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON> drank der genezing", "item.minecraft.splash_potion.effect.infested": "Spattende drank der infestatie", "item.minecraft.splash_potion.effect.invisibility": "Spattende drank der onzichtbaarheid", "item.minecraft.splash_potion.effect.leaping": "Spattende drank der sprongen", "item.minecraft.splash_potion.effect.levitation": "Spattende drank der zweving", "item.minecraft.splash_potion.effect.luck": "<PERSON><PERSON><PERSON> drank des geluks", "item.minecraft.splash_potion.effect.mundane": "Spattende triviale drank", "item.minecraft.splash_potion.effect.night_vision": "Spattende drank des nachtzichts", "item.minecraft.splash_potion.effect.oozing": "Spattende drank der slijmerigheid", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON> drank des gifs", "item.minecraft.splash_potion.effect.regeneration": "Spattende drank der regeneratie", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON> drank der valvertraging", "item.minecraft.splash_potion.effect.slowness": "Spattende drank der traagheid", "item.minecraft.splash_potion.effect.strength": "Spattende drank der kracht", "item.minecraft.splash_potion.effect.swiftness": "Spattende drank der vlotheid", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON> stroperige drank", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON> drank der schildpadmeester", "item.minecraft.splash_potion.effect.water": "Spattende waterfles", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON> drank des wateradems", "item.minecraft.splash_potion.effect.weakness": "Spattende drank der zwakheid", "item.minecraft.splash_potion.effect.weaving": "Spa<PERSON><PERSON> drank der weven", "item.minecraft.splash_potion.effect.wind_charged": "Spattende drank des jagende winds", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boot met kist", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Inktvissenspawnei", "item.minecraft.stick": "Stok", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON> bi<PERSON>l", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON> schoffel", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON>n schep", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Verdwaaldenspawnei", "item.minecraft.strider_spawn_egg": "Lavarijderspawnei", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Geheimzinnige stoofpot", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tadpole_bucket": "<PERSON><PERSON> k<PERSON>", "item.minecraft.tadpole_spawn_egg": "Kikker<PERSON>spawnei", "item.minecraft.tide_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.tide_armor_trim_smithing_template.new": "Harnasversiering 'getijde'", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON> pijl", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON> pijl", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> getipte pijl", "item.minecraft.tipped_arrow.effect.fire_resistance": "Pijl der vuurbestendigheid", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON> <PERSON> verwonding", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON> der genezing", "item.minecraft.tipped_arrow.effect.infested": "Pijl der infestatie", "item.minecraft.tipped_arrow.effect.invisibility": "Pijl der onzichtbaarheid", "item.minecraft.tipped_arrow.effect.leaping": "Pijl der sprongen", "item.minecraft.tipped_arrow.effect.levitation": "Pijl der zweving", "item.minecraft.tipped_arrow.effect.luck": "Pijl des geluks", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON> pijl", "item.minecraft.tipped_arrow.effect.night_vision": "Pijl des nachtzichts", "item.minecraft.tipped_arrow.effect.oozing": "Pijl der slijmerigheid", "item.minecraft.tipped_arrow.effect.poison": "Pijl des gifs", "item.minecraft.tipped_arrow.effect.regeneration": "Pijl der regeneratie", "item.minecraft.tipped_arrow.effect.slow_falling": "Pi<PERSON><PERSON> der valvertraging", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON> der traagheid", "item.minecraft.tipped_arrow.effect.strength": "Pijl der kracht", "item.minecraft.tipped_arrow.effect.swiftness": "Pijl der vlotheid", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON> pijl", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON><PERSON> der schildpadmeester", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON> der spatting", "item.minecraft.tipped_arrow.effect.water_breathing": "Pijl des wateradems", "item.minecraft.tipped_arrow.effect.weakness": "Pijl der zwakheid", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON><PERSON><PERSON> der weven", "item.minecraft.tipped_arrow.effect.wind_charged": "Pijl des jagende winds", "item.minecraft.tnt_minecart": "<PERSON><PERSON><PERSON> met TNT", "item.minecraft.torchflower_seeds": "Fakkelbloemzaden", "item.minecraft.totem_of_undying": "Totem der onsterfelijkheid", "item.minecraft.trader_llama_spawn_egg": "Handelaarslamaspawnei", "item.minecraft.trial_key": "Beproevingssleutel", "item.minecraft.trident": "Drietand", "item.minecraft.tropical_fish": "Tropische vis", "item.minecraft.tropical_fish_bucket": "Emmer tropische vis", "item.minecraft.tropical_fish_spawn_egg": "Tropischevissenspaw<PERSON>i", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON>padschild", "item.minecraft.turtle_scute": "<PERSON><PERSON><PERSON>padhoornschild", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "item.minecraft.vex_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.vex_armor_trim_smithing_template.new": "Harnasversiering 'ergernis'", "item.minecraft.vex_spawn_egg": "E<PERSON><PERSON><PERSON>spaw<PERSON>i", "item.minecraft.villager_spawn_egg": "Dorpelingenspawnei", "item.minecraft.vindicator_spawn_egg": "Verdedigerspawnei", "item.minecraft.wandering_trader_spawn_egg": "Rondreizendehandelaarsspawnei", "item.minecraft.ward_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.ward_armor_trim_smithing_template.new": "Harnasversiering 'hoeder'", "item.minecraft.warden_spawn_egg": "Hoederspawnei", "item.minecraft.warped_fungus_on_a_stick": "Spookachtige zwam aan een stok", "item.minecraft.water_bucket": "Emmer water", "item.minecraft.wayfinder_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Harnasversiering 'padvinder'", "item.minecraft.wheat": "Tarwe", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_bundle": "<PERSON><PERSON> bundel", "item.minecraft.white_dye": "<PERSON><PERSON>", "item.minecraft.white_harness": "Wit tuig", "item.minecraft.wild_armor_trim_smithing_template": "Smeedssjabloon", "item.minecraft.wild_armor_trim_smithing_template.new": "Harnasversiering 'verwilderd'", "item.minecraft.wind_charge": "Windvlaag", "item.minecraft.witch_spawn_egg": "Heksenspawnei", "item.minecraft.wither_skeleton_spawn_egg": "Witherskelettenspawnei", "item.minecraft.wither_spawn_egg": "Witherspawnei", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Wolvenspawnei", "item.minecraft.wooden_axe": "<PERSON><PERSON>n bijl", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON> schoffel", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON> houweel", "item.minecraft.wooden_shovel": "<PERSON><PERSON>n schep", "item.minecraft.wooden_sword": "<PERSON><PERSON>n zwaard", "item.minecraft.writable_book": "<PERSON><PERSON> en veer", "item.minecraft.written_book": "Gesch<PERSON><PERSON> boek", "item.minecraft.yellow_bundle": "<PERSON><PERSON> bundel", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON> tuig", "item.minecraft.zoglin_spawn_egg": "Zoglinspawnei", "item.minecraft.zombie_horse_spawn_egg": "Zombiepaardenspawnei", "item.minecraft.zombie_spawn_egg": "Zombiespawnei", "item.minecraft.zombie_villager_spawn_egg": "Zombiedorpelingenspawnei", "item.minecraft.zombified_piglin_spawn_egg": "Zombiepiglinspawnei", "item.modifiers.any": "<PERSON><PERSON> a<PERSON>:", "item.modifiers.armor": "<PERSON><PERSON> ged<PERSON>n:", "item.modifiers.body": "<PERSON><PERSON> a<PERSON>:", "item.modifiers.chest": "<PERSON><PERSON> op de borst:", "item.modifiers.feet": "<PERSON><PERSON> op de voeten:", "item.modifiers.hand": "<PERSON><PERSON> vast<PERSON>den:", "item.modifiers.head": "Wan<PERSON> op het hoofd:", "item.modifiers.legs": "<PERSON><PERSON> op de benen:", "item.modifiers.mainhand": "Wanneer in hoofdhand:", "item.modifiers.offhand": "Wanneer in secundaire hand:", "item.modifiers.saddle": "<PERSON><PERSON> gezadeld:", "item.nbt_tags": "NBT: %s label(s)", "item.op_block_warning.line1": "Waarschuwing:", "item.op_block_warning.line2": "Gebruik van dit voorwerp kan leiden tot de uitvoer van opdrachten", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON><PERSON> het niet, tenzij je de exacte inhoud ervan kent!", "item.unbreakable": "<PERSON><PERSON>ekba<PERSON>", "itemGroup.buildingBlocks": "Bouwblokken", "itemGroup.coloredBlocks": "Gekleurde blokken", "itemGroup.combat": "Gevecht", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Vervaardiging", "itemGroup.foodAndDrink": "Voedsel en dranken", "itemGroup.functional": "Functionele blokken", "itemGroup.hotbar": "Opgeslagen werkbalken", "itemGroup.ingredients": "Ingrediënten", "itemGroup.inventory": "Inventaris", "itemGroup.natural": "Natuurlijke blokken", "itemGroup.op": "Beheerdersvoorwerpen", "itemGroup.redstone": "Redstoneblokken", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "Spawneieren", "itemGroup.tools": "Gereedschap en gebruiksvoorwerpen", "item_modifier.unknown": "Voorwerpaanpasser '%s' is onbekend", "jigsaw_block.final_state": "Wordt:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Uitgelijnd", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "Verbindingstype:", "jigsaw_block.keep_jigsaws": "Blokken behouden", "jigsaw_block.levels": "Niveaus: %s", "jigsaw_block.name": "Naam:", "jigsaw_block.placement_priority": "Plaatsingsprioriteit:", "jigsaw_block.placement_priority.tooltip": "Dit is de volgorde waarin dit puzzelstuk wordt behandeld bij verbindingen in de bredere constructie wanneer het aansluit bij een ander stuk.\n\n<PERSON><PERSON><PERSON> worden in aflopende prioriteit behandeld, waar het moment van toevoeging wordt gebruikt in geval van gelijke prioriteit.", "jigsaw_block.pool": "Bronpoel:", "jigsaw_block.selection_priority": "Selectieprioriteit:", "jigsaw_block.selection_priority.tooltip": "Dit is de volgorde waarin dit puzzelblok probeert zich te verbinden met het doelstuk bij het behandelen van de verbindingen van het doelstuk.\n\nPuzzelblokken worden in aflopende prioriteit behandeld, waarbij een willekeurig bepaalde volgorde wordt gebruikt in geval van gelijke prioriteit.", "jigsaw_block.target": "Doelnaam:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Vooruitgangen", "key.attack": "Aanvallen/vernietigen", "key.back": "Achteruit lopen", "key.categories.creative": "Creatieve modus", "key.categories.gameplay": "Spel", "key.categories.inventory": "Inventaris", "key.categories.misc": "<PERSON><PERSON><PERSON>", "key.categories.movement": "Beweging", "key.categories.multiplayer": "<PERSON>n spelen", "key.categories.ui": "Spelinterface", "key.chat": "Chat openen", "key.command": "Opdrachtregel openen", "key.drop": "Geselecteerde voorwerp laten vallen", "key.forward": "Vooruit lopen", "key.fullscreen": "Volledig scherm omschakelen", "key.hotbar.1": "Werkbalkvak 1", "key.hotbar.2": "Werkbalkvak 2", "key.hotbar.3": "Werkbalkvak 3", "key.hotbar.4": "Werkbalkvak 4", "key.hotbar.5": "Werkbalkvak 5", "key.hotbar.6": "Werkbalkvak 6", "key.hotbar.7": "Werkbalkvak 7", "key.hotbar.8": "Werkbalkvak 8", "key.hotbar.9": "Werkbalkvak 9", "key.inventory": "Inventaris openen/sluiten", "key.jump": "Springen", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Del", "key.keyboard.down": "Pijl omlaag", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Ins", "key.keyboard.keypad.0": "Keypad 0", "key.keyboard.keypad.1": "Keypad 1", "key.keyboard.keypad.2": "Keypad 2", "key.keyboard.keypad.3": "Keypad 3", "key.keyboard.keypad.4": "Keypad 4", "key.keyboard.keypad.5": "Keypad 5", "key.keyboard.keypad.6": "Keypad 6", "key.keyboard.keypad.7": "Keypad 7", "key.keyboard.keypad.8": "Keypad 8", "key.keyboard.keypad.9": "Keypad 9", "key.keyboard.keypad.add": "Keypad +", "key.keyboard.keypad.decimal": "Keypad decimaal", "key.keyboard.keypad.divide": "Keypad /", "key.keyboard.keypad.enter": "Keypad enter", "key.keyboard.keypad.equal": "Keypad =", "key.keyboard.keypad.multiply": "Keypad *", "key.keyboard.keypad.subtract": "Keypad -", "key.keyboard.left": "Linkerpijl", "key.keyboard.left.alt": "<PERSON><PERSON><PERSON>", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Linkerctrl", "key.keyboard.left.shift": "Linkershift", "key.keyboard.left.win": "Linkerstart", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num lock", "key.keyboard.page.down": "Page down", "key.keyboard.page.up": "Page up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Prt sc", "key.keyboard.right": "Rechterpijl", "key.keyboard.right.alt": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Rechterctrl", "key.keyboard.right.shift": "Rechtershift", "key.keyboard.right.win": "Rechterstart", "key.keyboard.scroll.lock": "Scroll lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Spatiebalk", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON> ing<PERSON>", "key.keyboard.up": "Pijl omhoog", "key.keyboard.world.1": "Wereld 1", "key.keyboard.world.2": "Wereld 2", "key.left": "Zijwaarts naar links", "key.loadToolbarActivator": "Plaktoets om werkbalk te laden", "key.mouse": "Knop %1$s", "key.mouse.left": "Linksklikken", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "Rechtsklikken", "key.pickItem": "Blok kiezen", "key.playerlist": "Spelerslijst", "key.quickActions": "Snelle acties", "key.right": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> naar rechts", "key.saveToolbarActivator": "Plaktoets om werkbalk op te slaan", "key.screenshot": "Schermafbeelding maken", "key.smoothCamera": "Filmische camera omschakelen", "key.sneak": "<PERSON><PERSON><PERSON><PERSON>", "key.socialInteractions": "Sociale interacties", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> (toeschouwers)", "key.sprint": "Sprinten", "key.swapOffhand": "Voorwerpen met andere hand wisselen", "key.togglePerspective": "Perspectief omschakelen", "key.use": "Voorwerp gebruiken/blok plaatsen", "known_server_link.announcements": "Aankondigingen", "known_server_link.community": "Gemeenschap", "known_server_link.community_guidelines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Fora", "known_server_link.news": "Nieuws", "known_server_link.report_bug": "Serverbug melden", "known_server_link.status": "Status", "known_server_link.support": "Ondersteuning", "known_server_link.website": "Website", "lanServer.otherPlayers": "Instellingen voor andere spelers", "lanServer.port": "Poortnummer", "lanServer.port.invalid": "Ongeldig poortnummer.\nLaat het veld leeg of voer een poortnummer tussen 1024 en 65535 in.", "lanServer.port.invalid.new": "Ongeldig poortnummer.\nLaat het veld leeg of voer een poortnummer tussen %s en %s in.", "lanServer.port.unavailable": "Poort niet beschik<PERSON>.\nLaat het veld leeg of voer een ander poortnummer tussen 1024 en 65535 in.", "lanServer.port.unavailable.new": "Poort niet beschikba<PERSON>.\nLaat het veld leeg of voer een ander poortnummer tussen %s en %s in.", "lanServer.scanning": "<PERSON><PERSON>n naar spellen op je lokale netwerk", "lanServer.start": "LAN-wereld starten", "lanServer.title": "LAN-wereld", "language.code": "nld_NL", "language.name": "Nederlands", "language.region": "Nederland", "lectern.take_book": "<PERSON>ek pakken", "loading.progress": "%s%%", "mco.account.privacy.info": "<PERSON><PERSON> meer over <PERSON>jan<PERSON> en de privacy<PERSON>", "mco.account.privacy.info.button": "<PERSON><PERSON> le<PERSON> over de AVG", "mco.account.privacy.information": "Mojang implementeert bepaalde procedures om kinderen te beschermen en hun privacy te waarborgen, waaronder het naleven van de Children's Online Privacy Protection Act (COPPA) en de Algemene verordening gegevensbescherming (AVG).\n\nJe moet misschien toestemming van jouw ouder(s)/verzorger(s) vragen vóórdat je jouw Realmsaccount gaat gebruiken.", "mco.account.privacyinfo": "Mojang implementeert bepaalde procedures om kinderen te beschermen en hun privacy te waarborgen. Hieronder valt het naleven van de Children's Online Privacy Protection Act (COPPA) en de Algemene verordening gegevensbescherming (AVG).\n\nJe moet misschien toestemming van jouw ouder(s)/verzorger(s) vragen vóórdat je jouw Realmsaccount gaat gebruiken.\n\nAls je een ouder Minecraftaccount gebruikt waarbij je je a<PERSON><PERSON><PERSON> met je gebruikersna<PERSON>, moet je dit account migreren naar een Mojangaccount om Realms te gebruiken.", "mco.account.update": "Account bijwerken", "mco.activity.noactivity": "Geen activiteit in de afgelopen %s dag(en)", "mco.activity.title": "Spelersactiviteit", "mco.backup.button.download": "Recentste downloaden", "mco.backup.button.reset": "<PERSON><PERSON> resetten", "mco.backup.button.restore": "Herstellen", "mco.backup.button.upload": "Wereld uploaden", "mco.backup.changes.tooltip": "Veranderingen", "mco.backup.entry": "Back-up (%s)", "mco.backup.entry.description": "Beschrijving", "mco.backup.entry.enabledPack": "Ingeschakeld(e) pakket(ten)", "mco.backup.entry.gameDifficulty": "Moeilijkheidsgraad", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Server<PERSON>ie", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "Startwaarde", "mco.backup.entry.templateName": "Sjabloonnaam", "mco.backup.entry.undefined": "Ongedefinieerde wijziging", "mco.backup.entry.uploaded": "Geüpload", "mco.backup.entry.worldType": "Wereldtype", "mco.backup.generate.world": "<PERSON><PERSON> gene<PERSON>", "mco.backup.info.title": "Wijzigingen sinds laatste back-up", "mco.backup.narration": "Back-up van %s", "mco.backup.nobackups": "Deze Realm heeft momenteel geen back-ups.", "mco.backup.restoring": "<PERSON> hers<PERSON>len", "mco.backup.unknown": "ONBEKEND", "mco.brokenworld.download": "Downloaden", "mco.brokenworld.downloaded": "Gedownload", "mco.brokenworld.message.line1": "Reset de wereld of kies een andere wereld.", "mco.brokenworld.message.line2": "Je kunt er ook voor kiezen om de wereld te downloaden.", "mco.brokenworld.minigame.title": "Dit minispel wordt niet langer ondersteund", "mco.brokenworld.nonowner.error": "Wacht tot de eigenaar van <PERSON> wereld reset", "mco.brokenworld.nonowner.title": "Verouderde wereld", "mco.brokenworld.play": "Spelen", "mco.brokenworld.reset": "Resetten", "mco.brokenworld.title": "Je huidige wereld wordt niet langer ondersteund", "mco.client.incompatible.msg.line1": "<PERSON> cli<PERSON> is incompatibel met Realms.", "mco.client.incompatible.msg.line2": "Gebruik de recentste versie van Minecraft.", "mco.client.incompatible.msg.line3": "Realms is incompatibel met snapshotversies.", "mco.client.incompatible.title": "Incompatibele cliënt!", "mco.client.outdated.stable.version": "Je clientversie (%s) is niet compatibel met Realms.\n\nGebruik de recentste versie van Minecraft.", "mco.client.unsupported.snapshot.version": "Je clientversie (%s) is niet compatibel met Realms.\n\nRealms is niet be<PERSON><PERSON><PERSON> in deze snapshotversie.", "mco.compatibility.downgrade": "Downgraden", "mco.compatibility.downgrade.description": "Deze wereld werd voor het laatst gespeeld in %s; je gebruikt versie %s. Downgraden kan een wereld beschadigen - we kunnen niet garanderen dat het zal laden of werken.\n\nEen back-up van je wereld wordt opgeslagen onder \"Wereldback-ups\". Herste<PERSON> je wereld als dat nodig is.", "mco.compatibility.incompatible.popup.title": "Incompatibele versie", "mco.compatibility.incompatible.releaseType.popup.message": "De wereld die je probeert te betreden is niet compatibel met de versie waarin je speelt.", "mco.compatibility.incompatible.series.popup.message": "Deze wereld is voor het laatst betreden in versie %s; je speelt nu in versie %s.\n\nDeze versies zijn niet compatibel met el<PERSON><PERSON>. Om in deze versie te spelen, heb je een nieuwe wereld nodig.", "mco.compatibility.unverifiable.message": "De versie waarin deze wereld voor het laatst gespeeld werd, kon niet geverifieerd worden. Als deze wereld wordt geüpgraded of gedowngraded, wordt een back-up automatisch gemaakt en opgeslagen onder \"Wereldback-ups\".", "mco.compatibility.unverifiable.title": "Compatibiliteit niet verifieerbaar", "mco.compatibility.upgrade": "Upgraden", "mco.compatibility.upgrade.description": "Deze wereld werd voor het laatst gespeeld in %s; je gebruikt %s.\n\n<PERSON>en back-up van je wereld wordt opgeslagen onder \"Wereldback-ups\".\n\nHerste<PERSON> je wereld wanneer dat nodig is.", "mco.compatibility.upgrade.friend.description": "Deze wereld werd voor het laatst gespeeld in %s; je gebruikt %s.\n\n<PERSON>en back-up van de wereld wordt opgeslagen onder \"Wereldback-ups\".\n\nDe eigena<PERSON> van <PERSON> ka<PERSON>, wanne<PERSON> nodig, de wereld herstellen.", "mco.compatibility.upgrade.title": "Wil je deze wereld echt upgraden?", "mco.configure.current.minigame": "<PERSON><PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Spelersfeed tijdelijk uitgeschakeld", "mco.configure.world.backup": "Wereldback-ups", "mco.configure.world.buttons.activity": "Spelersactiviteit", "mco.configure.world.buttons.close": "Realm tijdelijk sluiten", "mco.configure.world.buttons.delete": "Verwijderen", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Instellingen", "mco.configure.world.buttons.invite": "Speler uitnodigen", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON> instellingen", "mco.configure.world.buttons.newworld": "<PERSON><PERSON><PERSON> wereld", "mco.configure.world.buttons.open": "Realm heropenen", "mco.configure.world.buttons.options": "Wereldinstellingen", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Regio k<PERSON>zen", "mco.configure.world.buttons.resetworld": "Realm resetten", "mco.configure.world.buttons.save": "Opsla<PERSON>", "mco.configure.world.buttons.settings": "Instellingen", "mco.configure.world.buttons.subscription": "Abonnement", "mco.configure.world.buttons.switchminigame": "Minispel wisselen", "mco.configure.world.close.question.line1": "Je kunt je Realm tijdelijk sluiten. <PERSON><PERSON><PERSON> kun je dan niet spelen terwijl je aanpassingen doorvoert. Heropen hem wanneer je klaar bent.\n\nDit annuleert je Realmsabonnement niet.", "mco.configure.world.close.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.configure.world.close.question.title": "Wij<PERSON>ingen maken zonder onderbrekingen te veroorzaken?", "mco.configure.world.closing": "Realm tijdelijk sluiten...", "mco.configure.world.commandBlocks": "Opdrachtblokken", "mco.configure.world.delete.button": "Realm verwijderen", "mco.configure.world.delete.question.line1": "Je <PERSON> wordt permanent verwijderd", "mco.configure.world.delete.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.configure.world.description": "Realmbeschrijving", "mco.configure.world.edit.slot.name": "<PERSON>ld<PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "Enkele instellingen zijn uitgeschakeld omdat je huidige wereld een avontuur is", "mco.configure.world.edit.subscreen.experience": "Enkele instellingen zijn uitgeschakeld omdat je huidige wereld een ervaring is", "mco.configure.world.edit.subscreen.inspiration": "Enkele instellingen zijn uitgeschakeld omdat jouw huidige wereld een inspiratie is", "mco.configure.world.forceGameMode": "Spelmodus forceren", "mco.configure.world.invite.narration": "Je hebt %s nieuwe uitnodiging(en)", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Uitgenodigd", "mco.configure.world.invited.number": "Uitgenodigd (%s)", "mco.configure.world.invites.normal.tooltip": "Normale gebruiker", "mco.configure.world.invites.ops.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "Verwijderen", "mco.configure.world.leave.question.line1": "Je ziet deze Realm niet meer, tenzij je opnieuw uitgenodigd wordt.", "mco.configure.world.leave.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.configure.world.loading": "Realm laden", "mco.configure.world.location": "Locatie", "mco.configure.world.minigame": "Huidig: %s", "mco.configure.world.name": "Realmnaam", "mco.configure.world.opening": "Realm openen...", "mco.configure.world.players.error": "<PERSON><PERSON> met de gegeven naam bestaat niet", "mco.configure.world.players.inviting": "Speler uitnodigen...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PvP", "mco.configure.world.region_preference": "Regiovoorkeur", "mco.configure.world.region_preference.title": "Regiovoorkeur", "mco.configure.world.reset.question.line1": "Je wereld wordt opnieuw gegener<PERSON>d, waardoor je huidige wereld verloren gaat.", "mco.configure.world.reset.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.configure.world.resourcepack.question": "Deze Realm vereist het gebruik van een aangepast bronpakket.\n\nWil je deze downloaden en gaan spelen?", "mco.configure.world.resourcepack.question.line1": "Deze Realm heeft een aangepast bronpakket nodig.", "mco.configure.world.resourcepack.question.line2": "Wil je het automatisch downloaden en installeren om te spelen?", "mco.configure.world.restore.download.question.line1": "De wereld wordt gedownload en toegevoegd aan jouw eigen werelden.", "mco.configure.world.restore.download.question.line2": "Wil je doorgaan?", "mco.configure.world.restore.question.line1": "Je wereld wordt hersteld naar de volgende datum: %s (%s)", "mco.configure.world.restore.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.configure.world.settings.expired": "Je kunt de instellingen van een verlopen Realm niet bewerken", "mco.configure.world.settings.title": "Instellingen", "mco.configure.world.slot": "Wereld %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Je <PERSON> wordt overgeschakeld naar een andere wereld.", "mco.configure.world.slot.switch.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.configure.world.slot.tooltip": "Omschakelen naar wereld", "mco.configure.world.slot.tooltip.active": "Bezoeken", "mco.configure.world.slot.tooltip.minigame": "Overschakelen naar minispel", "mco.configure.world.spawnAnimals": "<PERSON><PERSON> spawnen", "mco.configure.world.spawnMonsters": "Monsters spawnen", "mco.configure.world.spawnNPCs": "NPC's spawnen", "mco.configure.world.spawnProtection": "Spawnbescherming", "mco.configure.world.spawn_toggle.message": "Door deze instelling uit te schakelen, verwijder je alle entiteiten van dat type", "mco.configure.world.spawn_toggle.message.npc": "Door deze instelling uit te schakelen, verwijder je alle entiteiten van dat type, zoals dorpelingen", "mco.configure.world.spawn_toggle.title": "Waarschuwing!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "dag", "mco.configure.world.subscription.days": "dagen", "mco.configure.world.subscription.expired": "Verlopen", "mco.configure.world.subscription.extend": "Abonnement verlengen", "mco.configure.world.subscription.less_than_a_day": "minder dan een dag", "mco.configure.world.subscription.month": "maand", "mco.configure.world.subscription.months": "ma<PERSON>en", "mco.configure.world.subscription.recurring.daysleft": "Wordt automatisch vernieuwd over", "mco.configure.world.subscription.recurring.info": "Veranderingen aan je Realmsabonnement, zoals het verlengen ervan of het uitschakelen van terugkerend factureren, worden niet getoond tot de volgende factureringsdatum.", "mco.configure.world.subscription.remaining.days": "%1$s dag(en)", "mco.configure.world.subscription.remaining.months": "%1$s maand(en)", "mco.configure.world.subscription.remaining.months.days": "%1$s maand(en) en %2$s dag(en)", "mco.configure.world.subscription.start": "Begindatum", "mco.configure.world.subscription.tab": "Abonnement", "mco.configure.world.subscription.timeleft": "Resterende tijd", "mco.configure.world.subscription.title": "Abonnementsinformatie", "mco.configure.world.subscription.unknown": "Onbekend", "mco.configure.world.switch.slot": "<PERSON><PERSON>", "mco.configure.world.switch.slot.subtitle": "Deze wereld is leeg. <PERSON><PERSON> hoe je de wereld wilt creëren.", "mco.configure.world.title": "Realm configureren:", "mco.configure.world.uninvite.player": "Weet je zeker dat je de uitnodiging aan '%s' wilt intrekken?", "mco.configure.world.uninvite.question": "Weet je zeker dat je de uitnodiging wilt intrekken van", "mco.configure.worlds.title": "Werelden", "mco.connect.authorizing": "Aanmelden...", "mco.connect.connecting": "<PERSON><PERSON><PERSON><PERSON> met de <PERSON>...", "mco.connect.failed": "<PERSON><PERSON><PERSON><PERSON> met <PERSON> <PERSON> mi<PERSON>t", "mco.connect.region": "Serverregio: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Je moet een naam invoeren!", "mco.create.world.failed": "Wereld aanmaken mislukt!", "mco.create.world.reset.title": "<PERSON><PERSON> creëren...", "mco.create.world.skip": "Overslaan", "mco.create.world.subtitle": "Je kan eventueel selecteren welke wereld je op je nieuwe Realm wilt zetten", "mco.create.world.wait": "Realm creëren...", "mco.download.cancelled": "Downloaden geannuleerd", "mco.download.confirmation.line1": "De te downloaden wereld is groter dan %s", "mco.download.confirmation.line2": "Het is niet meer mogelijk deze wereld te uploaden naar je Realm", "mco.download.confirmation.oversized": "Je staat op het punt een wereld te downloaden die groter is dan %s\n\nJe kunt deze wereld niet weer uploaden naar je Realm", "mco.download.done": "Downloaden voltooid", "mco.download.downloading": "Downloaden", "mco.download.extracting": "Uitpakken", "mco.download.failed": "Downloaden mislukt", "mco.download.percent": "%s%%", "mco.download.preparing": "Downloaden voorbereiden", "mco.download.resourcePack.fail": "Bronpakket downloaden mislukt!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Nieuwste wereld downloaden", "mco.error.invalid.session.message": "Probeer Minecraft opnieuw te starten", "mco.error.invalid.session.title": "Ongeldige sessie", "mco.errorMessage.6001": "Vero<PERSON><PERSON> cliënt", "mco.errorMessage.6002": "Algemene voorwaarden niet geaccepteerd", "mco.errorMessage.6003": "Downloadlimiet bereikt", "mco.errorMessage.6004": "Uploadlimiet bereikt", "mco.errorMessage.6005": "<PERSON><PERSON> vergrendeld", "mco.errorMessage.6006": "Verouderde wereld", "mco.errorMessage.6007": "Gebruiker is lid van te veel Realms", "mco.errorMessage.6008": "<PERSON>naam ongeldig", "mco.errorMessage.6009": "Realmbeschrijving ongeldig", "mco.errorMessage.connectionFailure": "Er trad een fout op. <PERSON>beer het later opnieuw.", "mco.errorMessage.generic": "Er trad een fout op: ", "mco.errorMessage.initialize.failed": "Realm initiailiseren mislukt", "mco.errorMessage.noDetails": "Geen details voor fout gegeven", "mco.errorMessage.realmsService": "Er trad een fout op (%s):", "mco.errorMessage.realmsService.configurationError": "Er trad een onverwachte fout op tijdens het bewerken van wereldinstellingen", "mco.errorMessage.realmsService.connectivity": "Kan geen verbinding met Realms maken: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kan niet op compatibele versie controleren: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON> opnieuw", "mco.errorMessage.serviceBusy": "Het is op dit moment druk bij Realms.\n<PERSON><PERSON><PERSON> over een paar minuten opnieuw verbinding te maken met jouw domein.", "mco.gui.button": "Knop", "mco.gui.ok": "<PERSON><PERSON>", "mco.info": "Informatie!", "mco.invited.player.narration": "Speler %s uitgenodigd", "mco.invites.button.accept": "Accept<PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON>n openstaande uitnodigingen!", "mco.invites.pending": "Nieuwe uitnodiging(en)!", "mco.invites.title": "Openstaande uitnodigingen", "mco.minigame.world.changeButton": "Ander minispel kiezen", "mco.minigame.world.info.line1": "Je wereld wordt tijdelijk vervangen door een minispel!", "mco.minigame.world.info.line2": "Je kunt later terugkeren naar je oorspronkelijke wereld, zonder dat je iets verliest.", "mco.minigame.world.noSelection": "Maak een keuze", "mco.minigame.world.restore": "Minispel beëindigen...", "mco.minigame.world.restore.question.line1": "Het minispel wordt beëindigd en je Realm wordt hersteld.", "mco.minigame.world.restore.question.line2": "Weet je zeker dat je dit wilt doen?", "mco.minigame.world.selected": "Geselecteerd minispel:", "mco.minigame.world.slot.screen.title": "Wereld overschakelen...", "mco.minigame.world.startButton": "Overschakelen", "mco.minigame.world.starting.screen.title": "Minispel starten...", "mco.minigame.world.stopButton": "Minispel beëindigen", "mco.minigame.world.switch.new": "Ander minispel kiezen?", "mco.minigame.world.switch.title": "Minispel wijzigen", "mco.minigame.world.title": "Realm omschakelen naar minispel", "mco.news": "Realmsnieuws", "mco.notification.dismiss": "Sluiten", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON>", "mco.notification.transferSubscription.message": "Javarealmsabonnementen worden verplaatst naar de Microsoft Store. Laat je abonnement niet verlopen!\nZet 'm nu over en krijg dertig dagen Realms gratis.\nGa naar je profiel op minecraft.net en zet je abonnement over.", "mco.notification.visitUrl.buttonText.default": "Link openen", "mco.notification.visitUrl.message.default": "Bezoek onderstaande link", "mco.onlinePlayers": "Spelers online", "mco.play.button.realm.closed": "Realm is gesloten", "mco.question": "Vraag", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Ervaringen", "mco.reset.world.generate": "<PERSON><PERSON><PERSON> wereld", "mco.reset.world.inspiration": "Inspiratie", "mco.reset.world.resetting.screen.title": "Wereld resetten...", "mco.reset.world.seed": "Startwaarde (optioneel)", "mco.reset.world.template": "Wereldsjablonen", "mco.reset.world.title": "<PERSON><PERSON> resetten", "mco.reset.world.upload": "Wereld uploaden", "mco.reset.world.warning": "Dit vervangt je huidige Realmwereld voorgoed", "mco.selectServer.buy": "Koop een Realm!", "mco.selectServer.close": "Sluiten", "mco.selectServer.closed": "Gede<PERSON>erde Realm", "mco.selectServer.closeserver": "Realm sluiten", "mco.selectServer.configure": "Configureren", "mco.selectServer.configureRealm": "Realm instellen", "mco.selectServer.create": "Realm maken", "mco.selectServer.create.subtitle": "Selecteer welke wereld je op je nieuwe <PERSON> wilt zetten", "mco.selectServer.expired": "Verlopen Realm", "mco.selectServer.expiredList": "Je Realmsabonnement is verlopen", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "Afgelopen proefperiode", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON><PERSON> over <PERSON><PERSON> dag", "mco.selectServer.expires.days": "<PERSON><PERSON><PERSON><PERSON> over %s dagen", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON><PERSON> binnen<PERSON>t", "mco.selectServer.leave": "Realm verlaten", "mco.selectServer.loading": "Realmslijst laden", "mco.selectServer.mapOnlySupportedForVersion": "Deze wereld wordt niet ondersteund in %s", "mco.selectServer.minigame": "Minispel:", "mco.selectServer.minigameName": "Minispel: %s", "mco.selectServer.minigameNotSupportedInVersion": "Dit minispel kan niet gespeeld worden in %s", "mco.selectServer.noRealms": "Het lijkt erop dat je geen Realm hebt. Voeg een Realm toe om met je vrienden samen te spelen.", "mco.selectServer.note": "Opmerking:", "mco.selectServer.open": "Realm openen", "mco.selectServer.openserver": "Realm openen", "mco.selectServer.play": "Spelen", "mco.selectServer.popup": "Realms is een veilige en makkelijke manier om een onlinewereld met tot wel tien vrienden te delen. Er zijn veel minispellen en meer dan genoeg aangepaste werelden! Alleen de eigenaar van het domein hoeft te betalen.", "mco.selectServer.purchase": "Realm toevoegen", "mco.selectServer.trial": "Neem een proefperiode!", "mco.selectServer.uninitialized": "Klik om een nieuwe Realm te beginnen!", "mco.snapshot.createSnapshotPopup.text": "Je gaat een gratis snapshotrealm maken die deel uitmaakt van je Realmsabonnement. Deze nieuwe snapshotrealm blijft toegankelijk zolang je abonnement actief is. Dit heeft geen effect op je betaalde Realm.", "mco.snapshot.createSnapshotPopup.title": "Snapshotrealm maken?", "mco.snapshot.creating": "Snapshotrealm maken...", "mco.snapshot.description": "Ma<PERSON>t deel uit van %s", "mco.snapshot.friendsRealm.downgrade": "Je moet %s gebruiken om op deze Realm te spelen", "mco.snapshot.friendsRealm.upgrade": "%s moet hun Realm upgraden voordat je met deze snapshot erop kan spelen", "mco.snapshot.paired": "Deze snapshotrealm maakt deel uit van %s", "mco.snapshot.parent.tooltip": "Gebruik de nieuwste release van Minecraft om op deze Realm te spelen", "mco.snapshot.start": "Grat<PERSON> snapshot<PERSON>m beginnen", "mco.snapshot.subscription.info": "Deze snapshotrealm maakt deel uit van het abonnement van Realm '%s'. Het blijft actief zolang de Realm waar het deel van uitmaakt actief is.", "mco.snapshot.tooltip": "Gebruik snapshotrealms om een voorproefje te krijgen van toekomstige Minecraftversies. Deze kunnen nieuwe functionaliteiten en veranderingen bevatten.\n\nJe kunt je gewone Realms vinden in de releaseversie van het spel.", "mco.snapshotRealmsPopup.message": "Vanaf 23w41a kun je in een snapshot op Realms spelen. Elk Realmsabonnement heeft een gratis snapshotrealm die apart staat van je normale Realm!", "mco.snapshotRealmsPopup.title": "Realms nu besch<PERSON><PERSON><PERSON> in snapshots", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON>", "mco.template.button.publisher": "Gemaakt door", "mco.template.button.select": "Selecteren", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "Wereldsjabloon", "mco.template.info.tooltip": "Website van de maker", "mco.template.name": "Sjabloon", "mco.template.select.failure": "<PERSON><PERSON><PERSON> inhoudslijst voor deze categorie mislukt.\nControleer je internetverbinding of probeer het later opnieuw.", "mco.template.select.narrate.authors": "Auteurs: %s", "mco.template.select.narrate.version": "versie %s", "mco.template.select.none": "Oeps, het lijkt erop dat deze categorie op dit moment leeg is.\n<PERSON><PERSON> later terug voor nieuwe inhoud of, als je een maker bent,\n%s.", "mco.template.select.none.linkTitle": "overweeg zelf iets in te sturen", "mco.template.title": "Wereldsjablonen", "mco.template.title.minigame": "Minispellen", "mco.template.trailer.tooltip": "<PERSON>ldtrailer", "mco.terms.buttons.agree": "Accept<PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "<PERSON>k ga ak<PERSON><PERSON> met de algemene voorwaarden", "mco.terms.sentence.2": "van Minecraft Realms", "mco.terms.title": "Algemene voorwaarden voor Realms", "mco.time.daysAgo": "%1$s dag(en) geleden", "mco.time.hoursAgo": "%1$s uur geleden", "mco.time.minutesAgo": "%1$s minu(u)t(en) geleden", "mco.time.now": "zojuist", "mco.time.secondsAgo": "%1$s seconde(n) geleden", "mco.trial.message.line1": "Wil je je eigen Realm?", "mco.trial.message.line2": "<PERSON><PERSON> hier voor meer informatie!", "mco.upload.button.name": "Uploaden", "mco.upload.cancelled": "Uploaden geannuleerd", "mco.upload.close.failure": "Kan je <PERSON> niet sluiten. Probeer het later opnieuw.", "mco.upload.done": "Uploaden voltooid", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Uploaden mislukt! (%s)", "mco.upload.failed.too_big.description": "De geselecteerde wereld is te groot. De maximaal toegestane grootte is %s.", "mco.upload.failed.too_big.title": "<PERSON>ld te groot", "mco.upload.hardcore": "Hardcorewerelden kunnen niet worden geüpload!", "mco.upload.percent": "%s%%", "mco.upload.preparing": "Wereld voorbereiden", "mco.upload.select.world.none": "Geen eigen werelden gevonden!", "mco.upload.select.world.subtitle": "Selecteer een eigen wereld om te uploaden", "mco.upload.select.world.title": "Wereld uploaden", "mco.upload.size.failure.line1": "'%s' is te groot!", "mco.upload.size.failure.line2": "De grootte is %s. De maximaal toegestane grootte is %s.", "mco.upload.uploading": "'%s' uploaden", "mco.upload.verifying": "<PERSON><PERSON> controleren", "mco.version": "Versie: %s", "mco.warning": "Waarschuwing!", "mco.worldSlot.minigame": "Minispel", "menu.custom_options": "Aangepaste instellingen", "menu.custom_options.title": "Aangepaste instellingen", "menu.custom_options.tooltip": "Opmerking: Aangepaste instellingen zijn afkomstig van servers en/of inhoud van een derde partij.\nWees voorzichtig!", "menu.custom_screen_info.button_narration": "Dit is een aangepast scherm. Meer weten.", "menu.custom_screen_info.contents": "De inhoud van dit scherm wordt beheerd door servers en werelden van een derde partij die niet eigendom is van, beheerd wordt door of onder toezicht staat van Mojang Studios of Microsoft.\n\nWees voorzichtig! Wees terughoudend bij het volgen van links en geef nooit zomaar je persoonlijke informatie, inclusief aanmeldgegevens, weg.\n\nAls dit scherm voorkomt dat je kunt spelen, kun je ook de verbinding met de server verbreken met onderstaande knop.", "menu.custom_screen_info.disconnect": "Aangepast scherm afgewezen", "menu.custom_screen_info.title": "Opmerking over aangepaste schermen", "menu.custom_screen_info.tooltip": "<PERSON>t is een aangepast scherm. <PERSON><PERSON> hier om meer te lezen.", "menu.disconnect": "Verbinding verbreken", "menu.feedback": "<PERSON><PERSON><PERSON>", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Spelmenu", "menu.modded": " (gemodificeerd)", "menu.multiplayer": "<PERSON>n spelen", "menu.online": "Minecraft Realms", "menu.options": "Instellingen", "menu.paused": "<PERSON><PERSON> gepauzeerd", "menu.playdemo": "Demowereld spelen", "menu.playerReporting": "<PERSON><PERSON><PERSON> melden", "menu.preparingSpawn": "Spawngebied voorbereiden: %s%%", "menu.quick_actions": "Snelle acties", "menu.quick_actions.title": "Snelle acties", "menu.quit": "Spel afsluiten", "menu.reportBugs": "<PERSON> rapporteren", "menu.resetdemo": "Demowereld resetten", "menu.returnToGame": "Terug naar het spel", "menu.returnToMenu": "Opslaan en terug naar het hoofdmenu", "menu.savingChunks": "Chunks opslaan", "menu.savingLevel": "<PERSON><PERSON>", "menu.sendFeedback": "Feedback geven", "menu.server_links": "Serverlinks", "menu.server_links.title": "Serverlinks", "menu.shareToLan": "Openen voor LAN", "menu.singleplayer": "<PERSON>een spelen", "menu.working": "Bezig...", "merchant.deprecated": "<PERSON><PERSON>elin<PERSON> vullen hun voorraad tot twee keer per dag aan.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON>", "merchant.level.4": "Deskundige", "merchant.level.5": "Meester", "merchant.title": "%s - %s", "merchant.trades": "<PERSON>uil<PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Druk op %1$s om af te stappen", "multiplayer.applyingPack": "Bronpakket toepassen", "multiplayer.confirm_command.parse_errors": "Je probeert een onbekende of ongeldige opdracht uit te voeren.\nWeet je het zeker?\nOpdracht: %s", "multiplayer.confirm_command.permissions_required": "Je probeert een opdracht uit te voeren die extra toestemmingen vereist.\nDit kan je spel negatief beïnvloeden.\nWeet je het zeker?\nOpdracht: %s", "multiplayer.confirm_command.title": "Opdracht uitvoeren bevestigen", "multiplayer.disconnect.authservers_down": "Verificatieservers werken niet. Probeer het later opnieuw!", "multiplayer.disconnect.bad_chat_index": "Ontbrekend of opnieuw geordend chatbericht van de server gedetecteerd", "multiplayer.disconnect.banned": "Je <PERSON> geb<PERSON>erd op deze server", "multiplayer.disconnect.banned.expiration": "\nDe blokkade verloopt op %s", "multiplayer.disconnect.banned.reason": "Je bent geblo<PERSON>erd op deze server. Reden: %s", "multiplayer.disconnect.banned_ip.expiration": "\nDe blokkade verloopt op %s", "multiplayer.disconnect.banned_ip.reason": "Jouw IP-adres is geblokkeerd op deze server. Reden: %s", "multiplayer.disconnect.chat_validation_failed": "Chatbericht valideren mislukt", "multiplayer.disconnect.duplicate_login": "Je meldde je aan vanaf een andere locatie", "multiplayer.disconnect.expired_public_key": "Openbare profielsleutel verlopen. Controleer of je systeemtijd goed is ingesteld en probeer je spel opnieuw te starten.", "multiplayer.disconnect.flying": "Vliegen is uitgeschakeld op deze server", "multiplayer.disconnect.generic": "Verbinding verbroken", "multiplayer.disconnect.idling": "Je hebt te lang stil gestaan!", "multiplayer.disconnect.illegal_characters": "Ongeldige karakters in de chat", "multiplayer.disconnect.incompatible": "Incompatibele cliënt! Gebruik %s", "multiplayer.disconnect.invalid_entity_attacked": "Er werd gep<PERSON>erd een ongeldig entiteit aan te vallen", "multiplayer.disconnect.invalid_packet": "De server stuurde ongeldige gegevens", "multiplayer.disconnect.invalid_player_data": "Ongeldige spelergegevens", "multiplayer.disconnect.invalid_player_movement": "Ongeldige gegevens voor bewegende speler ontvangen", "multiplayer.disconnect.invalid_public_key_signature": "Ongeldige handtekening voor openbare profielsleutel.\nProbeer je spel opnieuw te starten.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ongeldige handtekening voor openbare profielsleutel.\nProbeer je spel opnieuw te starten.", "multiplayer.disconnect.invalid_vehicle_movement": "Ongeldige gegevens voor bewegend voertuig ontvangen", "multiplayer.disconnect.ip_banned": "Jouw IP-adres is geblokkeerd op deze server", "multiplayer.disconnect.kicked": "Uit de server gezet door een beheerder", "multiplayer.disconnect.missing_tags": "Onvolledige set eigenschappen ontvangen van de server.\nNeem contact op met de serverbeheerder.", "multiplayer.disconnect.name_taken": "Deze naam wordt al gebruikt", "multiplayer.disconnect.not_whitelisted": "Je staat niet op de witte lijst van deze server!", "multiplayer.disconnect.out_of_order_chat": "Chatpakket in verkeerde volgorde ontvangen. Is je systeemtijd veranderd?", "multiplayer.disconnect.outdated_client": "Incompatibele cliënt! Gebruik %s", "multiplayer.disconnect.outdated_server": "Incompatibele cliënt! Gebruik %s", "multiplayer.disconnect.server_full": "De server is vol!", "multiplayer.disconnect.server_shutdown": "Server werd gesloten", "multiplayer.disconnect.slow_login": "Het duurde te lang om in te loggen", "multiplayer.disconnect.too_many_pending_chats": "Te veel niet-erkende chatberichten", "multiplayer.disconnect.transfers_disabled": "Server sta<PERSON> doorsturen niet toe", "multiplayer.disconnect.unexpected_query_response": "Onverwachte aangepaste g<PERSON><PERSON> van c<PERSON>", "multiplayer.disconnect.unsigned_chat": "Chatpakket ontvangen met ontbrekende of ongeldige handtekening.", "multiplayer.disconnect.unverified_username": "Het veri<PERSON><PERSON><PERSON> van de gebruikersnaam mislukte!", "multiplayer.downloadingStats": "Statistieken ophalen...", "multiplayer.downloadingTerrain": "Terrein laden...", "multiplayer.lan.server_found": "Nieuwe server gevonden: %s", "multiplayer.message_not_delivered": "Chatbericht kon niet worden ontvangen; controleer de serverlogboeken: %s", "multiplayer.player.joined": "%s kwam het spel binnen", "multiplayer.player.joined.renamed": "%s (voorheen bekend als %s) kwam het spel binnen", "multiplayer.player.left": "%s verliet het spel", "multiplayer.player.list.hp": "%sgp", "multiplayer.player.list.narration": "Spelers online: %s", "multiplayer.requiredTexturePrompt.disconnect": "Deze server vereist een aangepast bronpakket", "multiplayer.requiredTexturePrompt.line1": "Deze server vereist het gebruik van een aangepast bronpakket.", "multiplayer.requiredTexturePrompt.line2": "Het weigeren van dit aangepaste bronpakket zal de verbinding met deze server verbreken.", "multiplayer.socialInteractions.not_available": "Sociale interacties zijn alleen besch<PERSON> in werelden waar je samen speelt", "multiplayer.status.and_more": "...en nog %s andere speler(s)...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Verbinding maken met de server mislukt", "multiplayer.status.cannot_resolve": "<PERSON><PERSON><PERSON><PERSON> van host<PERSON> mis<PERSON>t", "multiplayer.status.finished": "Voltooid", "multiplayer.status.incompatible": "Incompatibele versie!", "multiplayer.status.motd.narration": "<PERSON><PERSON><PERSON> van de dag: %s", "multiplayer.status.no_connection": "(geen verbinding)", "multiplayer.status.old": "Vero<PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "%s milliseconden ping", "multiplayer.status.pinging": "Pingen...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s van %s spelers online", "multiplayer.status.quitting": "Afmelden", "multiplayer.status.request_handled": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd behandeld", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Ongevraagde status ontvangen", "multiplayer.status.version.narration": "Serverversie: %s", "multiplayer.stopSleeping": "Opsta<PERSON>", "multiplayer.texturePrompt.failure.line1": "Serverbronpakket kon niet worden toegepast", "multiplayer.texturePrompt.failure.line2": "Functionaliteiten die aangepaste bronnen vereisen werken mogelijk niet zoals verwacht", "multiplayer.texturePrompt.line1": "Deze server raadt aan een aangepast bronpakket te gebruiken.", "multiplayer.texturePrompt.line2": "Wil je het automatisch downloaden en installeren?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nBe<PERSON>t van de server:\n%s", "multiplayer.title": "<PERSON>n spelen", "multiplayer.unsecureserver.toast": "Berichten die op deze server worden verzonden zijn mogelijk bewerkt en niet representatief van het origineel", "multiplayer.unsecureserver.toast.title": "Chatberichten verifiëren mislukt", "multiplayerWarning.check": "Dit scherm niet meer tonen", "multiplayerWarning.header": "Let op: samen spelen bij derden", "multiplayerWarning.message": "Waarschuwing: online spelen wordt aangeboden door servers van derden die geen eigendom zijn van, niet worden beheerd door, noch onder toezicht staan van Mojang Studios of Microsoft. Tijdens online spelen word je mogelijk blootgesteld aan niet-gemodereerde chatberichten of andere soorten door gebruikers gegenereerde inhoud die mogelijk niet voor iedereen geschikt is.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Knop: %s", "narration.button.usage.focused": "Druk op Enter om te activeren", "narration.button.usage.hovered": "Klik op de linkermuisknop om te activeren", "narration.checkbox": "Selectievakje: %s", "narration.checkbox.usage.focused": "Druk op Enter om te wisselen", "narration.checkbox.usage.hovered": "Klik op de linkermuisknop om te wisselen", "narration.component_list.usage": "Druk op Tab om naar volgend element te navigeren", "narration.cycle_button.usage.focused": "Druk op Enter om over te schakelen naar %s", "narration.cycle_button.usage.hovered": "Klik op de linkermuisknop om over te schakelen naar %s", "narration.edit_box": "Bewerkingsvak: %s", "narration.item": "Voorwerp: %s", "narration.recipe": "Recept voor %s", "narration.recipe.usage": "Klik op de linkermuisknop om te selecteren", "narration.recipe.usage.more": "Klik op de rechtermuisknop om meer recepten te tonen", "narration.selection.usage": "Gebruik pijltjestoetsen omhoog en omlaag om een ander item te selecteren", "narration.slider.usage.focused": "Gebruik pijltjestoetsen links en rechts om waarde te wijzigen", "narration.slider.usage.hovered": "Sleep schuifregelaar om waarde te wijzigen", "narration.suggestion": "Suggestie %s van %s geselecteerd: %s", "narration.suggestion.tooltip": "Suggestie %s van %s geselecteerd: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Druk op tab om naar de volgende suggestie te gaan", "narration.suggestion.usage.cycle.hidable": "Druk op tab om naar de volgende suggestie te gaan of op escape om de suggesties te verlaten", "narration.suggestion.usage.fill.fixed": "Druk op tab om de suggestie te gebruiken", "narration.suggestion.usage.fill.hidable": "Druk op tab om de suggestie te gebruiken of op escape om de suggesties te verlaten", "narration.tab_navigation.usage": "Druk op Ctrl en Tab om van tabblad te wisselen", "narrator.button.accessibility": "Toegankelijkheid", "narrator.button.difficulty_lock": "Niveauvergrendeling", "narrator.button.difficulty_lock.locked": "Vergrendeld", "narrator.button.difficulty_lock.unlocked": "Ontgrendeld", "narrator.button.language": "Taal", "narrator.controls.bound": "%s is gebonden aan %s", "narrator.controls.reset": "%s-knop resetten", "narrator.controls.unbound": "%s is niet gebonden", "narrator.joining": "Betreden", "narrator.loading": "%s aan het laden", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Rij %s van %s geselecteerd", "narrator.position.object_list": "Rijelement %s van %s geselecteerd", "narrator.position.screen": "Schermelement %s van %s", "narrator.position.tab": "Tabblad %s van %s geselecteerd", "narrator.ready_to_play": "<PERSON><PERSON><PERSON> om te spelen", "narrator.screen.title": "Hoofdmenu", "narrator.screen.usage": "<PERSON><PERSON><PERSON><PERSON> of Tab-knop om element te selecteren", "narrator.select": "%s geselecteerd", "narrator.select.world": "%s geselecteerd; laatst gespeeld: %s om %s in spelmodus %s; versie: %s", "narrator.select.world_info": "%s geselecteerd; laatst gespeeld: %s om %s", "narrator.toast.disabled": "Verteller uitgeschakeld", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> inges<PERSON>d", "optimizeWorld.confirm.description": "Hier<PERSON> zal geprobeerd worden je wereld te optimaliseren, zodat alle gegevens in het recentste spelformaat wordt opgeslagen. Dit kan heel lang duren, a<PERSON><PERSON><PERSON><PERSON><PERSON> van de wereld. Wan<PERSON> dit klaar is, kan de wereld sneller zijn, maar is het niet langer compatibel met oudere versies van het spel. Weet je zeker dat je verder wilt gaan?", "optimizeWorld.confirm.proceed": "Back-up maken en optimaliseren", "optimizeWorld.confirm.title": "<PERSON>ld optimaliseren", "optimizeWorld.info.converted": "Geüpgradede chunks: %s", "optimizeWorld.info.skipped": "Overgeslagen chunks: %s", "optimizeWorld.info.total": "Totaal aantal chunks: %s", "optimizeWorld.progress.counter": "%s/%s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Chunks tellen...", "optimizeWorld.stage.failed": "Mislukt! :(", "optimizeWorld.stage.finished": "Afronden...", "optimizeWorld.stage.finished.chunks": "Upgraden van chunks afronden...", "optimizeWorld.stage.finished.entities": "Upgraden van entiteiten afronden...", "optimizeWorld.stage.finished.poi": "Upgrade<PERSON> van herkenningspunten afronden...", "optimizeWorld.stage.upgrading": "Chunks upgraden...", "optimizeWorld.stage.upgrading.chunks": "Chunks upgraden...", "optimizeWorld.stage.upgrading.entities": "Entiteiten upgraden...", "optimizeWorld.stage.upgrading.poi": "Herkenningspunten upgraden...", "optimizeWorld.title": "Wereld '%s' optimaliseren", "options.accessibility": "Toegankelijkheidsinstellingen", "options.accessibility.high_contrast": "Hoog contrast", "options.accessibility.high_contrast.error.tooltip": "Bronpakket voor hoog contrast niet be<PERSON>", "options.accessibility.high_contrast.tooltip": "Verhoogt het contrast van interface-elementen", "options.accessibility.high_contrast_block_outline": "<PERSON><PERSON> <PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "Verhoogt het blokomlijningscontrast van het aangekeken blok.", "options.accessibility.link": "Toegankelijkheidsgids", "options.accessibility.menu_background_blurriness": "Wazigheid menu-achtergrond", "options.accessibility.menu_background_blurriness.tooltip": "Past de wazigheid aan van <PERSON>achtergronden", "options.accessibility.narrator_hotkey": "Vertellersneltoets", "options.accessibility.narrator_hotkey.mac.tooltip": "Met ⌘ + B de verteller in- en uitschakelen", "options.accessibility.narrator_hotkey.tooltip": "Met Ctrl + B de verteller in- en uitschakelen", "options.accessibility.panorama_speed": "Panoramadra<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background": "Tekstachtergrond", "options.accessibility.text_background.chat": "chat", "options.accessibility.text_background.everywhere": "overal", "options.accessibility.text_background_opacity": "Achtergronddoorzichtigheid", "options.accessibility.title": "Toegankelijkheidsinstellingen", "options.allowServerListing": "<PERSON>am in serverlijst", "options.allowServerListing.tooltip": "Servers kunnen online spelers tonen als onderdeel van hun publieke status.\nDoor deze instelling uit te zetten, wordt jouw naam niet in deze lijsten getoond.", "options.ao": "<PERSON><PERSON> be<PERSON>", "options.ao.max": "maximum", "options.ao.min": "minimum", "options.ao.off": "UIT", "options.attack.crosshair": "vizier", "options.attack.hotbar": "werkbalk", "options.attackIndicator": "Aanvalsindicator", "options.audioDevice": "Apparaat", "options.audioDevice.default": "systeems<PERSON><PERSON><PERSON>", "options.autoJump": "Automatisch springen", "options.autoSuggestCommands": "Opdrachtsuggesties", "options.autosaveIndicator": "Opslagindicator", "options.biomeBlendRadius": "Klimaatovergang", "options.biomeBlendRadius.1": "UIT (snelst)", "options.biomeBlendRadius.11": "11×11 (extreem)", "options.biomeBlendRadius.13": "13×13 (uitslover)", "options.biomeBlendRadius.15": "15×15 (maximum)", "options.biomeBlendRadius.3": "3×3 (snel)", "options.biomeBlendRadius.5": "5×5 (normaal)", "options.biomeBlendRadius.7": "7×7 (hoog)", "options.biomeBlendRadius.9": "9×9 (heel hoog)", "options.chat": "Chatinstellingen", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Chatvertraging: %s second(en)", "options.chat.delay_none": "Chatvertraging: geen", "options.chat.height.focused": "Hoogte actief", "options.chat.height.unfocused": "Hoogte inactief", "options.chat.line_spacing": "Regelafstand", "options.chat.links": "Links", "options.chat.links.prompt": "vragen bij links", "options.chat.opacity": "Chattekstdoorzichtigheid", "options.chat.scale": "Chattekstgrootte", "options.chat.title": "Chatinstellingen", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "verborgen", "options.chat.visibility.system": "alleen opdrachten", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "fraai", "options.clouds.fast": "snel", "options.controls": "Besturing", "options.credits_and_attribution": "Vermeldingen en attributie", "options.damageTiltStrength": "Schadekanteling", "options.damageTiltStrength.tooltip": "Hoeveel de camera kantelt wanneer je gewond raakt.", "options.darkMojangStudiosBackgroundColor": "Zwart-wit logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Verandert de achtergrondkleur van het Mojang Studios-laadscherm naar zwart.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "Bepa<PERSON>t hoeveel het duisterniseffect pulseert wanneer een hoeder of sculkkrijser het aan je geeft.", "options.difficulty": "Niveau", "options.difficulty.easy": "makkelijk", "options.difficulty.easy.info": "<PERSON><PERSON><PERSON><PERSON> wezens spawnen, maar dienen minder schade toe. De hongerbalk raakt leeg en vermindert dan je gezondheid tot vijf hartje<PERSON>.", "options.difficulty.hard": "moeili<PERSON>", "options.difficulty.hard.info": "Vijandige wezens spawnen en dienen meer schade toe. De hongerbalk raakt leeg en vermindert dan al je gezondheid.", "options.difficulty.hardcore": "hardcore", "options.difficulty.normal": "norm<PERSON>", "options.difficulty.normal.info": "Vijandige wezens spawnen en dienen de standaardhoeveelheid schade toe. De hongerbalk raakt leeg en vermindert dan je gezondheid tot een half hartje.", "options.difficulty.online": "Servermoeilijkheidsgraad", "options.difficulty.peaceful": "vredig", "options.difficulty.peaceful.info": "<PERSON>n vijandige wezens en alleen sommige neutrale wezens spawnen. De hongerbalk raakt nooit leeg en gezondheid wordt met de tijd bijgevuld.", "options.directionalAudio": "3D-geluid", "options.directionalAudio.off.tooltip": "Klassiek stereogeluid", "options.directionalAudio.on.tooltip": "Gebruikt op HRTF-gebaseerd directioneel geluid om de simulatie van 3D-geluid te verbeteren. Vereist audiohardware met ondersteuning voor HRTF. Krijg de beste ervar<PERSON> met een hoofdtelefoon.", "options.discrete_mouse_scroll": "Discreet scrollen", "options.entityDistanceScaling": "Entiteitsafstand", "options.entityShadows": "Entiteitsschaduwen", "options.font": "Lettertype-instellingen", "options.font.title": "Lettertype-instellingen", "options.forceUnicodeFont": "Unicode forceren", "options.fov": "Gezichtsveld", "options.fov.max": "Quakepro", "options.fov.min": "norm<PERSON>", "options.fovEffectScale": "Gezichtsveldeffecten", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON> hoeveel het gezichtsveld kan wij<PERSON>en met s<PERSON><PERSON><PERSON><PERSON>.", "options.framerate": "%s fps", "options.framerateLimit": "Max. framerate", "options.framerateLimit.max": "onbeperkt", "options.fullscreen": "Volledig scherm", "options.fullscreen.current": "huidig", "options.fullscreen.entry": "%s×%s@%s (%s-bit)", "options.fullscreen.resolution": "Volledigschermresolutie", "options.fullscreen.unavailable": "instelling niet be<PERSON><PERSON>", "options.gamma": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "standaard", "options.gamma.max": "helder", "options.gamma.min": "donker", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.glintSpeed.tooltip": "Bepa<PERSON>t hoe snel het glanseffect beweegt over betoverde voorwerpen.", "options.glintStrength": "Glanssterkte", "options.glintStrength.tooltip": "Bepaalt de transparantie van het glanseffect op betoverde voorwerpen.", "options.graphics": "Weergave", "options.graphics.fabulous": "fantastisch!", "options.graphics.fabulous.tooltip": "De grafische weergave \"%s\" gebruikt schermshaders om weer, wolken en deeltjes achter doorzichtige blokken en water te tekenen.\nDit kan een grote invloed hebben op de prestaties van draagbare apparaten en 4K-schermen.", "options.graphics.fancy": "fraai", "options.graphics.fancy.tooltip": "De fraaie grafische weergave balanceert prestatie en kwaliteit voor de meeste machines.\nWe<PERSON>, wolken en deeltjes worden mogelijk niet weergegeven achter doorzichtige blokken of water.", "options.graphics.fast": "snel", "options.graphics.fast.tooltip": "De snelle grafische weergave vermindert de zichtbare hoeveelheid regen en sneeuw.\nTransparantie-effecten worden uitgeschakeld voor een aantal blokken, waaronder bladeren.", "options.graphics.warning.accept": "<PERSON><PERSON><PERSON> zonder ondersteuning", "options.graphics.warning.cancel": "Terug", "options.graphics.warning.message": "Er werd gedetecteerd dat je grafische apparaat de grafische instelling \"%s\" niet ondersteunt.\n\nJe mag dit negeren en doorgaan, maar er wordt geen ondersteuning aangeboden voor jouw apparaat als je de grafische instelling \"%s\" gebruikt.", "options.graphics.warning.renderer": "Gedetecteerde renderer: [%s]", "options.graphics.warning.title": "Grafisch apparaat wordt niet ondersteund", "options.graphics.warning.vendor": "Gedetecteerde leverancier: [%s]", "options.graphics.warning.version": "Gedetecteerde OpenGL-versie: [%s]", "options.guiScale": "GUI-schaal", "options.guiScale.auto": "automatisch", "options.hidden": "verborgen", "options.hideLightningFlashes": "Bliksemflitsen verbergen", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON>gt ervoor dat de lucht niet oplicht bij blikseminslagen. Bliksemschichten blijven wel zich<PERSON>ar.", "options.hideMatchedNames": "Op naam verbergen", "options.hideMatchedNames.tooltip": "Servers van derden kunnen chatberichten sturen in een afwijkend format.\nDoor deze instelling in te schakelen, worden spelers in chat verborgen op basis van afzender.", "options.hideSplashTexts": "Splashtekst verbergen", "options.hideSplashTexts.tooltip": "<PERSON>er<PERSON>t de gele splashtekst in het hoofdmenu.", "options.inactivityFpsLimit": "FPS verminderen wanneer", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limiteert de framerate tot dertig wanneer het spel geen invoer van een speler krijgt voor langer dan een minuut. Na negen minuten wordt dit gelimiteerd tot tien.", "options.inactivityFpsLimit.minimized": "geminimaliseerd", "options.inactivityFpsLimit.minimized.tooltip": "Lim<PERSON>ert de framerate alleen wanneer het spel geminimaliseerd is.", "options.invertMouse": "<PERSON><PERSON> s<PERSON>", "options.japaneseGlyphVariants": "<PERSON><PERSON> ka<PERSON>", "options.japaneseGlyphVariants.tooltip": "Gebruikt de Japanse varianten van de CJK-karakters in het standaardlettertype", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "omschakelen", "options.language": "Taal", "options.language.title": "Taal", "options.languageAccuracyWarning": "(vertalingen zijn mogelijk onnauwkeurig)", "options.languageWarning": "Vertalingen zijn mogelijk onnauwkeurig", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "links", "options.mainHand.right": "rechts", "options.mipmapLevels": "Detailniveaus", "options.modelPart.cape": "Cape", "options.modelPart.hat": "Hoed", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Linkerbroekspijp", "options.modelPart.left_sleeve": "Linkermouw", "options.modelPart.right_pants_leg": "Rechterbroekspijp", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.mouseWheelSensitivity": "Scrollgevoeligheid", "options.mouse_settings": "Muisinstellingen", "options.mouse_settings.title": "Muisinstellingen", "options.multiplayer.title": "Instellingen voor samen spelen", "options.multiplier": "%s×", "options.music_frequency": "Muziekfrequentie", "options.music_frequency.constant": "constant", "options.music_frequency.default": "standaard", "options.music_frequency.frequent": "vaak", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON>t hoe vaak muziek speelt wanneer je in een wereld bent.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "vertelt alles", "options.narrator.chat": "vertelt chatberichten", "options.narrator.notavailable": "niet be<PERSON>", "options.narrator.off": "UIT", "options.narrator.system": "vertelt systeember<PERSON>ten", "options.notifications.display_time": "Meldingsduur", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON>t hoe lang meldingen op het scherm zichtba<PERSON> blijven.", "options.off": "UIT", "options.off.composed": "%s: UIT", "options.on": "AAN", "options.on.composed": "%s: AAN", "options.online": "Online-instellingen", "options.online.title": "Online-instellingen", "options.onlyShowSecureChat": "Alleen beveiligde chat weergeven", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON> be<PERSON> van andere spelers weergeven waarvan geverifieerd kan worden dat ze door die speler zijn verzonden en niet zijn aangepast.", "options.operatorItemsTab": "Tabblad beheerdersvoorwerpen", "options.particles": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.all": "alles", "options.particles.decreased": "verminderd", "options.particles.minimal": "minimaal", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Chunkbouwer", "options.prioritizeChunkUpdates.byPlayer": "deels asynchroon", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Chunks worden onmiddellijk opnieuw opgebouwd bij bepaalde acties zoals het plaatsen en vernietigen van blokken.", "options.prioritizeChunkUpdates.nearby": "asynchroon", "options.prioritizeChunkUpdates.nearby.tooltip": "Nabije chunks worden altijd onmiddellijk opgebouwd. Spelprestaties kunnen verslechteren bij het plaatsen en vernietigen van blokken.", "options.prioritizeChunkUpdates.none": "synchroon", "options.prioritizeChunkUpdates.none.tooltip": "Nabije chunks worden parallel opgebouwd. Er kunnen kort gaten worden weergegeven bij het vernietigen van blokken.", "options.rawMouseInput": "Onbewerkte invoer", "options.realmsNotifications": "Realmsnieuws en -uitnodigingen", "options.realmsNotifications.tooltip": "Haalt Realmsnieuws en -uitnodigingen op in het titelscherm en toont hun respectievelijke icoontjes op de Realmsknop.", "options.reducedDebugInfo": "<PERSON><PERSON> debuginformatie", "options.renderClouds": "Wolken", "options.renderCloudsDistance": "Wolkenafstand", "options.renderDistance": "Weergavebereik", "options.resourcepack": "Bronpakketten", "options.rotateWithMinecart": "<PERSON> <PERSON><PERSON><PERSON><PERSON>en", "options.rotateWithMinecart.tooltip": "Of het z<PERSON><PERSON><PERSON><PERSON> van de <PERSON>er mee moet dra<PERSON>en met een dra<PERSON><PERSON><PERSON>. <PERSON><PERSON> in werelden waar de experimentele instelling 'Mijnkarverbeteringen' ingeschakeld is.", "options.screenEffectScale": "Vervormingseffecten", "options.screenEffectScale.tooltip": "Misselijkheidskracht- en Netherportaalschermvervormingseffecten.\nBij lagere waarden wordt het misselijkheidseffect vervangen door een groene waas.", "options.sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sensitivity.max": "SUPERSNEL!!!", "options.sensitivity.min": "*gaap*", "options.showNowPlayingToast": "Muziektekstwolk tonen", "options.showNowPlayingToast.tooltip": "Toont een tekstwolk wanneer muziek begint te spelen. Indien ingeschakeld, wordt deze tekstwolk continu getoond in het pauzemenu terwijl muziek speelt.", "options.showSubtitles": "Ondertiteling", "options.simulationDistance": "Simulatieafstand", "options.skinCustomisation": "<PERSON> aan<PERSON>en", "options.skinCustomisation.title": "<PERSON> aan<PERSON>en", "options.sounds": "Muziek en geluiden", "options.sounds.title": "Muziek- en geluidinstellingen", "options.telemetry": "Gegevensverzameling", "options.telemetry.button": "Gegevensverzameling", "options.telemetry.button.tooltip": "\"%s\" omvat alleen de vereiste gegevens.\n\"%s\" omvat, naast de vereiste gegevens, ook <PERSON><PERSON> gegevens.", "options.telemetry.disabled": "Dataverzameling uitgeschakeld.", "options.telemetry.state.all": "alles", "options.telemetry.state.minimal": "minimaal", "options.telemetry.state.none": "geen", "options.title": "Instellingen", "options.touchscreen": "Touchscreenmodus", "options.video": "Grafische instellingen", "options.videoTitle": "Grafische instellingen", "options.viewBobbing": "Loopbeweging", "options.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft heeft geen geheugen meer.\n\nDit kan veroorzaakt zijn door een probleem in het spel of omdat de Java Virtual Machine niet genoeg geheugen toegewezen heeft gekregen.\n\nOm wereldbeschadigingen te voorkomen, is het huidige spel gestopt. We hebben geprobeerd genoeg geheugen vrij te maken zodat je verder kan spelen, maar dit heeft mogelijk niet gewerkt.\n\nHerstart het spel als dit bericht nog eens verschijnt.", "outOfMemory.title": "<PERSON>n geheugen meer!", "pack.available.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.copyFailure": "Pakketten kopiëren mislukt", "pack.dropConfirm": "Wil je de volgende pakketten toevoegen aan Minecraft?", "pack.dropInfo": "Sleep bestanden naar dit venster om pakketten toe te voegen", "pack.dropRejected.message": "De volgende lijst bevat ongeldige pakketten en werden niet gekopieerd:\n %s", "pack.dropRejected.title": "Elementen die geen pakket zijn", "pack.folderInfo": "(plaats pakketbestanden hier)", "pack.incompatible": "Incompatibel", "pack.incompatible.confirm.new": "Dit pakket werd gemaakt voor een nieuwere versie van Minecraft en werkt mogelijk niet goed meer.", "pack.incompatible.confirm.old": "Dit pakket werd gemaakt voor een oudere versie van Minecraft en werkt mogelijk niet goed meer.", "pack.incompatible.confirm.title": "Weet je zeker dat je dit pakket wilt laden?", "pack.incompatible.new": "(gemaakt voor een nieuwere versie van Minecraft)", "pack.incompatible.old": "(gemaakt voor een oudere versie van Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Pakkettenmap openen", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "ingebouwd", "pack.source.feature": "functionaliteit", "pack.source.local": "lokaal", "pack.source.server": "server", "pack.source.world": "wereld", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanees", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Binnenplaats", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON><PERSON> succes<PERSON>l gebombardeerd", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Boeket", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON><PERSON> schedel", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Buste", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Grottenvogel", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "A<PERSON>e", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Eindbaas", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Opgraving", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "<PERSON><PERSON><PERSON> met drie pepers", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lage nevel", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Lucifer", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatief", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Citruil", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Varkensscène", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Vijver", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON><PERSON> z<PERSON>d", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Rit over de prairie", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Aardse zorgen", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Schedel en rozen", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON>es staat klaar", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Zonnebloemen", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Uitgepakt", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "De leegte", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Woestenij", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Willekeurige variant", "parsing.bool.expected": "<PERSON><PERSON><PERSON><PERSON> waarde verwacht", "parsing.bool.invalid": "Ongeldige booleaanse waarde; 'true' of 'false' verwacht, maar '%s' gevonden", "parsing.double.expected": "Dubbeleprecisiegetal verwacht", "parsing.double.invalid": "Dubbeleprecisiegetal '%s' is ongeldig", "parsing.expected": "'%s' verwacht", "parsing.float.expected": "Zwevendekommagetal verwacht", "parsing.float.invalid": "Zwevendekommagetal '%s' is ongeldig", "parsing.int.expected": "<PERSON><PERSON><PERSON> getal verwacht", "parsing.int.invalid": "<PERSON><PERSON><PERSON> getal '%s' is ongeldig", "parsing.long.expected": "<PERSON> geheel getal verwacht", "parsing.long.invalid": "<PERSON> geheel getal '%s' is ongeldig", "parsing.quote.escape": "Ongeldige ontsnappingssequentie '\\%s' in de gequoteerde tekenreeks", "parsing.quote.expected.end": "Ongesloten gequoteerde tekenreeks", "parsing.quote.expected.start": "Aanhalingsteken om een tekenreeks te <PERSON>nen verwacht", "particle.invalidOptions": "Deeltjesopties '%s' verwerken mislukt", "particle.notFound": "Deeltje '%s' is onbekend", "permissions.requires.entity": "<PERSON>en entiteit moet deze opdracht uitvoeren", "permissions.requires.player": "<PERSON><PERSON> speler moet deze opdracht uitvoeren", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Wanneer toegepast:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicaat '%s' is onbekend", "quickplay.error.invalid_identifier": "<PERSON><PERSON> wereld gevonden met de opgegeven identiteit", "quickplay.error.realm_connect": "<PERSON>n geen verbinding met de <PERSON> maken", "quickplay.error.realm_permission": "<PERSON><PERSON> om met deze Realm te verbinden", "quickplay.error.title": "Snel spelen mislukt", "realms.configuration.region.australia_east": "New South Wales, Australië", "realms.configuration.region.australia_southeast": "Victoria, Australië", "realms.configuration.region.brazil_south": "Brazilië", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, Verenigde Staten", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, Verenigde Staten", "realms.configuration.region.east_us_2": "Noord-Carolina, Verenigde Staten", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Oost-Japan", "realms.configuration.region.japan_west": "West-Japan", "realms.configuration.region.korea_central": "Zuid-Korea", "realms.configuration.region.north_central_us": "Illinois, Verenigde Staten", "realms.configuration.region.north_europe": "Ierland", "realms.configuration.region.south_central_us": "Texas, Verenigde Staten", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Zweden", "realms.configuration.region.uae_north": "Verenigde Arabische Emiraten", "realms.configuration.region.uk_south": "Zuid-Engeland", "realms.configuration.region.west_central_us": "Utah, Verenigde Staten", "realms.configuration.region.west_europe": "Nederland", "realms.configuration.region.west_us": "Californië, Verenigde Staten", "realms.configuration.region.west_us_2": "Washington, Verenigde Staten", "realms.configuration.region_preference.automatic_owner": "Automatisch (ping van Realmeigenaar)", "realms.configuration.region_preference.automatic_player": "Automatisch (ping van eerste speler in sessie)", "realms.missing.snapshot.error.text": "Realms wordt in snapshots niet ondersteund", "recipe.notFound": "Recept '%s' is onbekend", "recipe.toast.description": "Bekijk je receptenboek", "recipe.toast.title": "Nieuw(e) recept(en) vrijgespeeld!", "record.nowPlaying": "Nu aan het spelen: %s", "recover_world.bug_tracker": "<PERSON>ug rapporteren", "recover_world.button": "Proberen te herstellen", "recover_world.done.failed": "Kon niet herstellen van een vorige toestand.", "recover_world.done.success": "Herstel succesvol!", "recover_world.done.title": "<PERSON><PERSON><PERSON> gereed", "recover_world.issue.missing_file": "Bestand ontbreekt", "recover_world.issue.none": "<PERSON><PERSON> problemen", "recover_world.message": "De volgende problemen traden op bij het proberen te lezen van wereldmap \"%s\".\nJe kunt proberen de wereld te herstellen via een vorige toestand of je kunt dit probleem rapporteren op de bugtracker.", "recover_world.no_fallback": "<PERSON><PERSON>d om van te herstellen beschik<PERSON>", "recover_world.restore": "Proberen te herstellen", "recover_world.restoring": "<PERSON><PERSON> proberen te herstellen...", "recover_world.state_entry": "Toestand van %s: ", "recover_world.state_entry.unknown": "onbekend", "recover_world.title": "Wereld laden mislukt", "recover_world.warning": "Wereldsamenvatting laden mislukt", "resourcePack.broken_assets": "NIET-W<PERSON>KENDE BRONNEN GEDETECTEERD", "resourcePack.high_contrast.name": "Hoog contrast", "resourcePack.load_fail": "<PERSON><PERSON><PERSON> van bronpak<PERSON> mislukt", "resourcePack.programmer_art.name": "Programmeerderskunst", "resourcePack.runtime_failure": "Fout in bronpakket gedetecteerd", "resourcePack.server.name": "Wereld-specifieke hulpbronnen", "resourcePack.title": "Bronpakketten selecteren", "resourcePack.vanilla.description": "<PERSON>t standaar<PERSON><PERSON><PERSON><PERSON> van Minecraft", "resourcePack.vanilla.name": "Standaard", "resourcepack.downloading": "Bronpakketten aan het downloaden", "resourcepack.progress": "Bestand downloaden (%s MB)...", "resourcepack.requesting": "Verzoek maken...", "screenshot.failure": "Schermafbeelding opslaan mislukt: %s", "screenshot.success": "Schermafbeelding opgeslagen als %s", "selectServer.add": "Server toevoegen", "selectServer.defaultName": "Minecraftserver", "selectServer.delete": "Verwijderen", "selectServer.deleteButton": "Verwijderen", "selectServer.deleteQuestion": "Weet je zeker dat je deze server wilt verwijderen?", "selectServer.deleteWarning": "'%s' gaat voor eeuwig verloren (en dat is lang!)", "selectServer.direct": "Directe verbinding", "selectServer.edit": "Bewerken", "selectServer.hiddenAddress": "(verborgen)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Server bezoeken", "selectWorld.access_failure": "<PERSON><PERSON> betreden mislukt", "selectWorld.allowCommands": "Cheats", "selectWorld.allowCommands.info": "Opdrachten zoals /gamemode en /experience", "selectWorld.allowCommands.new": "Opdracht<PERSON>", "selectWorld.backupEraseCache": "Gecachete gegevens wissen", "selectWorld.backupJoinConfirmButton": "Back-up maken en laden", "selectWorld.backupJoinSkipButton": "Ik weet wat ik doe!", "selectWorld.backupQuestion.customized": "Aangepaste werelden worden niet langer ondersteund", "selectWorld.backupQuestion.downgrade": "Een wereld downgraden wordt niet ondersteund", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON><PERSON> met experimentele instellingen worden niet ondersteund", "selectWorld.backupQuestion.snapshot": "Wil je deze wereld echt laden?", "selectWorld.backupWarning.customized": "We ondersteunen aangepaste werelden niet in deze versie van Minecraft. De wereld kan nog steeds geladen worden en alles houden zoals het was, maar nieuw terrein is niet langer aangepast. Het spijt ons voor het ongemak!", "selectWorld.backupWarning.downgrade": "Deze wereld werd voor het laatst gespeeld in %s; je gebruikt versie %s. Downgraden kan een wereld beschadigen - we kunnen niet garanderen dat het zal laden of werken. Als je nog steeds door wilt gaan, maak dan een back-up!", "selectWorld.backupWarning.experimental": "Deze wereld gebruikt experimentele instellingen die op elk moment kunnen stoppen met functioneren. We kunnen niet garanderen dat deze wereld laadt of werkt. Betreden op eigen risico!", "selectWorld.backupWarning.snapshot": "Deze wereld werd voor het laatst gespeeld in %s; je gebruikt %s. <PERSON>ak een back-up voor het geval je wereld beschadigingen oploopt!", "selectWorld.bonusItems": "Bonuskist", "selectWorld.cheats": "Cheats", "selectWorld.commands": "Opdrachten", "selectWorld.conversion": "Moet worden omgezet!", "selectWorld.conversion.tooltip": "<PERSON><PERSON> wereld moet worden geopend in een oudere versie (zoals 1.6.4) om veilig te worden omgezet", "selectWorld.create": "<PERSON><PERSON>", "selectWorld.customizeType": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Gegevenspakketten", "selectWorld.data_read": "Wereldgegevens lezen...", "selectWorld.delete": "Verwijderen", "selectWorld.deleteButton": "Verwijderen", "selectWorld.deleteQuestion": "Weet je zeker dat je deze wereld wilt verwijderen?", "selectWorld.deleteWarning": "'%s' zal voor eeuwig verloren gaan (en dat is lang)!", "selectWorld.delete_failure": "<PERSON>ld verwijderen mislukt", "selectWorld.edit": "Bewerken", "selectWorld.edit.backup": "Back-up maken", "selectWorld.edit.backupCreated": "Back-up g<PERSON><PERSON><PERSON> van '%s'", "selectWorld.edit.backupFailed": "Back-up maken mislukt", "selectWorld.edit.backupFolder": "Back-upmap openen", "selectWorld.edit.backupSize": "grootte: %s MB", "selectWorld.edit.export_worldgen_settings": "Generatorinstellingen exporteren", "selectWorld.edit.export_worldgen_settings.failure": "Exporteren mislukt", "selectWorld.edit.export_worldgen_settings.success": "Geëxporteerd", "selectWorld.edit.openFolder": "Map openen", "selectWorld.edit.optimize": "Optimaliseren", "selectWorld.edit.resetIcon": "Pictogram resetten", "selectWorld.edit.save": "Opsla<PERSON>", "selectWorld.edit.title": "<PERSON>ld bewerken", "selectWorld.enterName": "<PERSON>ld<PERSON><PERSON>", "selectWorld.enterSeed": "Startwaarde voor de wereldgenerator", "selectWorld.experimental": "Experimenteel", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Vereiste experimentele functies: %s", "selectWorld.experimental.details.title": "Vereiste experimentele functies", "selectWorld.experimental.message": "Wees voorzichtig!\nDeze configuratie vereist functies die nog in ontwikkeling zijn. <PERSON> <PERSON>ld kan crashen, kapot<PERSON>an of geen toekomstige versies ondersteunen.", "selectWorld.experimental.title": "Waarschuwing: experimentele functies", "selectWorld.experiments": "Experimenten", "selectWorld.experiments.info": "Experimenten zijn potentiële nieuwe functies. <PERSON><PERSON>, want er kunnen dingen misgaan. Experimenten kunnen niet worden uitgeschakeld na het creëren van de <PERSON>.", "selectWorld.futureworld.error.text": "Iets ging verkeerd tijdens het laden van een wereld die voor het laatst geopend werd in een toekomstige versie. Dit was, om te beginnen, al risicovol. Het spijt ons dat het niet werkte.", "selectWorld.futureworld.error.title": "Er trad een fout op!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Net als de overlevingsmodus, maar je kan blokken niet plaatsen of verwijderen.", "selectWorld.gameMode.adventure.line1": "Net als de overlevingsmodus, maar blokken kunnen niet", "selectWorld.gameMode.adventure.line2": "geplaatst of gebroken worden", "selectWorld.gameMode.creative": "creatief", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, bouw en ontdek ongelimiteerd. Je kunt vliegen, hebt eindeloze materialen en je kunt niet gewond raken door monsters.", "selectWorld.gameMode.creative.line1": "Onbeperkte grondstoffen, vrij rondvliegen en", "selectWorld.gameMode.creative.line2": "onmiddellijk blokken vernietigen", "selectWorld.gameMode.hardcore": "hardcore", "selectWorld.gameMode.hardcore.info": "Net als de overlevingsmodus, maar vastgezet op niveau 'moeilijk'. Je kunt niet respawnen wanneer je sterft.", "selectWorld.gameMode.hardcore.line1": "Net als de overlevingsmodus, vastgezet op het moeilijkste", "selectWorld.gameMode.hardcore.line2": "niveau en slechts één leven", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON> kan kijken, maar niet a<PERSON>en.", "selectWorld.gameMode.spectator.line1": "<PERSON> kan kijken, maar niet a<PERSON>en", "selectWorld.gameMode.survival": "overleven", "selectWorld.gameMode.survival.info": "<PERSON><PERSON><PERSON> mysterieuze werelden waar je bouwt, ve<PERSON><PERSON><PERSON>, maakt en tegen monsters vecht.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON>, maak gere<PERSON>, verz<PERSON>", "selectWorld.gameMode.survival.line2": "ervaringspunten en strijd om te overleven", "selectWorld.gameRules": "Spelregels", "selectWorld.import_worldgen_settings": "Instellingen importeren", "selectWorld.import_worldgen_settings.failure": "Instellingen importeren mislukt", "selectWorld.import_worldgen_settings.select_file": "Instellingenbestand kiezen (.json)", "selectWorld.incompatible.description": "Deze wereld kan niet geopend worden in deze versie.\nHet werd voor het laatst gespeeld in %s.", "selectWorld.incompatible.info": "Incompatibele versie: %s", "selectWorld.incompatible.title": "Incompatibele versie", "selectWorld.incompatible.tooltip": "De<PERSON> wereld kan niet geopend worden omdat het gemaakt werd in een incompatibele versie.", "selectWorld.incompatible_series": "Gemaakt in een incompatibele versie", "selectWorld.load_folder_access": "<PERSON>n toegang tot map waar spelwerelden worden opgeslagen!", "selectWorld.loading_list": "Wereld<PERSON><PERSON><PERSON> laden", "selectWorld.locked": "Vergrendeld door een andere instantie van Minecraft", "selectWorld.mapFeatures": "Constructies", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, scheepswrakken en meer", "selectWorld.mapType": "Wereldtype", "selectWorld.mapType.normal": "norm<PERSON>", "selectWorld.moreWorldOptions": "<PERSON><PERSON><PERSON>", "selectWorld.newWorld": "<PERSON><PERSON><PERSON> wereld", "selectWorld.recreate": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Aangepaste werelden worden niet langer ondersteund in deze versie van Minecraft. We kunnen proberen de wereld opnieuw te creëren met dezelfde startwaarde en eigenschappen, maar terreinaanpassingen gaan verloren. Het spijt ons voor het ongemak!", "selectWorld.recreate.customized.title": "Aangepaste werelden worden niet langer ondersteund", "selectWorld.recreate.error.text": "Iets ging verkeerd bij het her<PERSON><PERSON><PERSON> van een wereld.", "selectWorld.recreate.error.title": "Er trad een fout op!", "selectWorld.resource_load": "Bronnen voorbereiden...", "selectWorld.resultFolder": "Wordt opgeslagen in:", "selectWorld.search": "zoeken naar werelden", "selectWorld.seedInfo": "Laat leeg voor een willekeurige startwaarde", "selectWorld.select": "<PERSON><PERSON> spelen", "selectWorld.targetFolder": "Wordt opgeslagen in: %s", "selectWorld.title": "<PERSON>ld selecteren", "selectWorld.tooltip.fromNewerVersion1": "De<PERSON> wereld werd opgeslagen in een nieuwere versie.", "selectWorld.tooltip.fromNewerVersion2": "Het laden van de wereld in deze versie kan problemen veroorzaken!", "selectWorld.tooltip.snapshot1": "Vergeet niet een back-up te maken van de<PERSON> wereld", "selectWorld.tooltip.snapshot2": "voordat je het gaat laden in deze snapshot.", "selectWorld.unable_to_load": "<PERSON>lde<PERSON> laden mislukt", "selectWorld.version": "Versie:", "selectWorld.versionJoinButton": "Toch laden", "selectWorld.versionQuestion": "Wil je deze wereld echt laden?", "selectWorld.versionUnknown": "onbekend", "selectWorld.versionWarning": "Deze wereld werd voor het laatst gespeeld in %s. Het laden van de wereld in deze versie kan beschadigingen veroorzaken!", "selectWorld.warning.deprecated.question": "Sommige gebruikte functies zijn verouderd en zullen in de toekomst stoppen met functioneren. Wil je doorgaan?", "selectWorld.warning.deprecated.title": "Waarschuwing! Deze instellingen gebruiken verouderde functies", "selectWorld.warning.experimental.question": "Deze instellingen zijn experimenteel en kunnen op een gegeven moment stoppen met functioneren. Wil je doorgaan?", "selectWorld.warning.experimental.title": "Waarschuwing! Deze instellingen gebruiken experimentele functies", "selectWorld.warning.lowDiskSpace.description": "Er is niet veel opslagruimte over.\nEen volle schijf kan leiden tot schade aan je wereld wanneer je speelt.", "selectWorld.warning.lowDiskSpace.title": "Waarschuwing! Opslagruimte bijna vol!", "selectWorld.world": "<PERSON><PERSON>", "sign.edit": "Bordtekst bewerken", "sleep.not_possible": "Deze nacht kan niet worden overgeslagen", "sleep.players_sleeping": "%s/%s spelers slapen", "sleep.skipping_night": "Door de nacht slapen", "slot.only_single_allowed": "Alleen enkel<PERSON>ge vakken zijn toe<PERSON>, '%s' gekregen", "slot.unknown": "Vak '%s' is onbekend", "snbt.parser.empty_key": "<PERSON><PERSON><PERSON><PERSON> mag niet leeg zijn", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON> getal ver<PERSON>cht", "snbt.parser.expected_decimal_numeral": "<PERSON><PERSON><PERSON> getal verwacht", "snbt.parser.expected_float_type": "Zwevendekommagetal verwacht", "snbt.parser.expected_hex_escape": "Karakterliteraal met lengte %s verwacht", "snbt.parser.expected_hex_numeral": "Hexadecimaal getal verwacht", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON> getal verwacht", "snbt.parser.expected_non_negative_number": "Niet-negatief getal verwacht", "snbt.parser.expected_number_or_boolean": "Getal of booleaanse waarde verwacht", "snbt.parser.expected_string_uuid": "Geldige UUID als tekenreeks verwacht", "snbt.parser.expected_unquoted_string": "Geldige tekenreeks zonder aanhalingstekens verwacht", "snbt.parser.infinity_not_allowed": "Oneindige getallen zijn niet toe<PERSON>an", "snbt.parser.invalid_array_element_type": "Ongeldig lijstelementtype", "snbt.parser.invalid_character_name": "Ongeldige Unicodekarakternaam", "snbt.parser.invalid_codepoint": "Ongeldige Unicodekarakterwaarde: %s", "snbt.parser.invalid_string_contents": "Ongeldige tekenreeksinhoud", "snbt.parser.invalid_unquoted_start": "Tekenreeksen zonder aanhalingstekens mogen niet beginnen met de tekens 0-9, + of -", "snbt.parser.leading_zero_not_allowed": "<PERSON><PERSON><PERSON> getallen mogen niet beginnen met 0", "snbt.parser.no_such_operation": "Niet-bestaande bewerking: %s", "snbt.parser.number_parse_failure": "Getal '%s' verwerken mislukt", "snbt.parser.undescore_not_allowed": "<PERSON>allen mogen niet beginnen of eindigen met een laag streepje", "soundCategory.ambient": "Omgeving", "soundCategory.block": "Blokken", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON> wezens", "soundCategory.master": "Hoofdvolume", "soundCategory.music": "Muziek", "soundCategory.neutral": "Vriendelijke wezens", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Platenspeler/nootblokken", "soundCategory.ui": "Interface", "soundCategory.voice": "Stem/spraak", "soundCategory.weather": "<PERSON>er", "spectatorMenu.close": "<PERSON><PERSON> sluiten", "spectatorMenu.next_page": "Volgende pagina", "spectatorMenu.previous_page": "Vorige pagina", "spectatorMenu.root.prompt": "Druk op een knop om een opdracht te selecteren en nogmaals om die te gebruiken.", "spectatorMenu.team_teleport": "Naar teamlid teleporteren", "spectatorMenu.team_teleport.prompt": "Selecteer een team om naar te teleporteren", "spectatorMenu.teleport": "Naar speler teleporteren", "spectatorMenu.teleport.prompt": "Selecteer een speler om naar te teleporteren", "stat.generalButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.itemsButton": "Voorwerpen", "stat.minecraft.animals_bred": "<PERSON><PERSON> g<PERSON>", "stat.minecraft.aviate_one_cm": "Afstand per dekschild", "stat.minecraft.bell_ring": "Klokken geluid", "stat.minecraft.boat_one_cm": "Afstand per boot", "stat.minecraft.clean_armor": "<PERSON><PERSON><PERSON> harna<PERSON> s<PERSON>", "stat.minecraft.clean_banner": "<PERSON><PERSON><PERSON>", "stat.minecraft.clean_shulker_box": "Shulkerdozen schoongemaakt", "stat.minecraft.climb_one_cm": "Afstand geklo<PERSON>n", "stat.minecraft.crouch_one_cm": "Afstand geslopen", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON> geabsorbeerd", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> g<PERSON>erd door schild", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON> toe<PERSON>d", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> (geabsorbeerd)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> (weerstaan)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "Schade opgelopen", "stat.minecraft.deaths": "Aantal keren doodgegaan", "stat.minecraft.drop": "Voorwerpen laten vallen", "stat.minecraft.eat_cake_slice": "Taartpunten gegeten", "stat.minecraft.enchant_item": "Voorwerpen betoverd", "stat.minecraft.fall_one_cm": "Afstand gevallen", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON> gevuld", "stat.minecraft.fish_caught": "Vissen gevangen", "stat.minecraft.fly_one_cm": "Afstand gevlogen", "stat.minecraft.happy_ghast_one_cm": "Afstand per vrolijke Ghast", "stat.minecraft.horse_one_cm": "Afstand te paard", "stat.minecraft.inspect_dispenser": "Dispensers doorzocht", "stat.minecraft.inspect_dropper": "Droppers doorzocht", "stat.minecraft.inspect_hopper": "T<PERSON>ers doorzocht", "stat.minecraft.interact_with_anvil": "Interacties met a<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_beacon": "Interacties met baken", "stat.minecraft.interact_with_blast_furnace": "Interacties met hoogoven", "stat.minecraft.interact_with_brewingstand": "Interacties met brou<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Interacties met <PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_cartography_table": "Interacties met kartogra<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "Interacties met werkbank", "stat.minecraft.interact_with_furnace": "Interacties met oven", "stat.minecraft.interact_with_grindstone": "Interacties met s<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_lectern": "Interacties met lessenaar", "stat.minecraft.interact_with_loom": "Interacties met we<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "Interacties met s<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smoker": "Interacties met roker", "stat.minecraft.interact_with_stonecutter": "Interacties met steen<PERSON><PERSON>", "stat.minecraft.jump": "Sp<PERSON><PERSON>", "stat.minecraft.leave_game": "Spellen afgesloten", "stat.minecraft.minecart_one_cm": "Afstand per mijnkar", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON> gedood", "stat.minecraft.open_barrel": "Vaten geopend", "stat.minecraft.open_chest": "<PERSON><PERSON> geopend", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON> geopend", "stat.minecraft.open_shulker_box": "Shulkerdozen geopend", "stat.minecraft.pig_one_cm": "Afstand per varken", "stat.minecraft.play_noteblock": "Nootblok<PERSON> bespeeld", "stat.minecraft.play_record": "Muziekplaten afgespeeld", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "Spel<PERSON> gedood", "stat.minecraft.pot_flower": "<PERSON>en gepot", "stat.minecraft.raid_trigger": "Overvallen veroorzaakt", "stat.minecraft.raid_win": "Overvallen gewonnen", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON> in bed ges<PERSON>en", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "Afstand gesprint", "stat.minecraft.strider_one_cm": "Afstand per lavari<PERSON>der", "stat.minecraft.swim_one_cm": "Afstand gezwommen", "stat.minecraft.talked_to_villager": "Met dorpelingen gepraat", "stat.minecraft.target_hit": "Doelwitten geraakt", "stat.minecraft.time_since_death": "Tijd sinds laatste dood", "stat.minecraft.time_since_rest": "Tijd sinds laatste rust", "stat.minecraft.total_world_time": "<PERSON><PERSON><PERSON><PERSON> met de wereld geopend", "stat.minecraft.traded_with_villager": "Met dorpelingen geruild", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON> met vals<PERSON>k gea<PERSON>", "stat.minecraft.tune_noteblock": "Nootblokken gestemd", "stat.minecraft.use_cauldron": "Water uit ketel gehaald", "stat.minecraft.walk_on_water_one_cm": "Afstand gelopen op water", "stat.minecraft.walk_one_cm": "Afstand gelopen", "stat.minecraft.walk_under_water_one_cm": "Afstand gelopen onder water", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON><PERSON> g<PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON>", "stat_type.minecraft.dropped": "Laten vallen", "stat_type.minecraft.killed": "Je hebt %s %s gedood", "stat_type.minecraft.killed.none": "Je hebt %s nooit gedood", "stat_type.minecraft.killed_by": "%s heeft jou %s keer gedood", "stat_type.minecraft.killed_by.none": "Je bent nooit gedood door %s", "stat_type.minecraft.mined": "<PERSON><PERSON>", "stat_type.minecraft.picked_up": "Opgepakt", "stat_type.minecraft.used": "<PERSON><PERSON> g<PERSON>", "stats.none": "-", "structure_block.button.detect_size": "ZOEKEN", "structure_block.button.load": "LADEN", "structure_block.button.save": "OPSLAAN", "structure_block.custom_data": "Aangepaste gegevenseigenschapsnaam", "structure_block.detect_size": "Detecteer grootte en positie van constructie:", "structure_block.hover.corner": "Hoek: %s", "structure_block.hover.data": "Gegevens: %s", "structure_block.hover.load": "Laden: %s", "structure_block.hover.save": "Opslaan: %s", "structure_block.include_entities": "Entiteiten meerekenen:", "structure_block.integrity": "Constructie-integriteit en startwaarde", "structure_block.integrity.integrity": "Constructie-integriteit", "structure_block.integrity.seed": "Constructiestartwaarde", "structure_block.invalid_structure_name": "Constructienaam '%s' is ongeldig", "structure_block.load_not_found": "Constructie '%s' is niet be<PERSON>", "structure_block.load_prepare": "Positie voor constructie '%s' voorbereid", "structure_block.load_success": "Constructie geladen uit '%s'", "structure_block.mode.corner": "Hoek", "structure_block.mode.data": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.mode.load": "Laden", "structure_block.mode.save": "Opsla<PERSON>", "structure_block.mode_info.corner": "Hoekmodus - plaats- en groottemarkeerder", "structure_block.mode_info.data": "Gegevensmodus - spellogicamarkeerder", "structure_block.mode_info.load": "Laadmodus - uit bestand laden", "structure_block.mode_info.save": "Opslagmodus - naar bestand schrijven", "structure_block.position": "Relatieve positie", "structure_block.position.x": "relatieve positie x", "structure_block.position.y": "relatieve positie y", "structure_block.position.z": "relatieve positie z", "structure_block.save_failure": "Constructie '%s' opslaan mislukt", "structure_block.save_success": "Constructie opgeslagen als '%s'", "structure_block.show_air": "Onzichtbare blokken tonen:", "structure_block.show_boundingbox": "Begrenzing tonen:", "structure_block.size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.size.x": "constructiegrootte x", "structure_block.size.y": "constructiegrootte y", "structure_block.size.z": "constructiegrootte z", "structure_block.size_failure": "Constructiegrootte bepalen mislukt. <PERSON><PERSON><PERSON> met overeenkomende constructienamen toe.", "structure_block.size_success": "Grootte succesvol gevonden voor '%s'", "structure_block.strict": "Strikt plaatsen:", "structure_block.structure_name": "Constructienaam", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON> gel<PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON><PERSON> gel<PERSON>", "subtitles.block.amethyst_block.chime": "Ameth<PERSON> klingelt", "subtitles.block.amethyst_block.resonate": "Amethist resoneert", "subtitles.block.anvil.destroy": "Aambeeld wordt vernietigd", "subtitles.block.anvil.land": "Aambeeld landt", "subtitles.block.anvil.use": "Aambeeld wordt gebruikt", "subtitles.block.barrel.close": "Vat sluit", "subtitles.block.barrel.open": "Vat opent", "subtitles.block.beacon.activate": "<PERSON><PERSON>", "subtitles.block.beacon.ambient": "Baken zoemt", "subtitles.block.beacon.deactivate": "<PERSON><PERSON> de<PERSON>ert", "subtitles.block.beacon.power_select": "Bakenkracht wordt geselecteerd", "subtitles.block.beehive.drip": "Honing druppelt", "subtitles.block.beehive.enter": "<PERSON><PERSON><PERSON> gaat bi<PERSON> in", "subtitles.block.beehive.exit": "<PERSON><PERSON><PERSON> verl<PERSON><PERSON>", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON>", "subtitles.block.beehive.work": "<PERSON><PERSON><PERSON><PERSON> werken", "subtitles.block.bell.resonate": "<PERSON> resoneert", "subtitles.block.bell.use": "<PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Druipblad kantelt omlaag", "subtitles.block.big_dripleaf.tilt_up": "Druipblad kantelt omhoog", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON> knettert", "subtitles.block.brewing_stand.brew": "Brouwstan<PERSON>ard bubbelt", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON> knappen", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON> stromen", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON> suizen", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON> we<PERSON>len", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON>n z<PERSON>men", "subtitles.block.button.click": "Knop klikt", "subtitles.block.cake.add_candle": "<PERSON><PERSON> wordt in taart gezet", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON><PERSON> knettert", "subtitles.block.candle.crackle": "<PERSON><PERSON> knettert", "subtitles.block.candle.extinguish": "<PERSON><PERSON> do<PERSON>", "subtitles.block.chest.close": "<PERSON>st sluit", "subtitles.block.chest.locked": "<PERSON><PERSON> vergrendelt", "subtitles.block.chest.open": "<PERSON><PERSON> opent", "subtitles.block.chorus_flower.death": "Chorusb<PERSON>em vergaat", "subtitles.block.chorus_flower.grow": "Chorusbloem groeit", "subtitles.block.comparator.click": "Vergelijker klikt", "subtitles.block.composter.empty": "Compostbak leegt", "subtitles.block.composter.fill": "Compostbak vult", "subtitles.block.composter.ready": "Compostbak composteert", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON> van de zee valt aan", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON> de <PERSON> de<PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "<PERSON><PERSON><PERSON> gaat uit", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON><PERSON> gaat aan", "subtitles.block.copper_trapdoor.close": "Valluik sluit", "subtitles.block.copper_trapdoor.open": "Valluik opent", "subtitles.block.crafter.craft": "Vervaardigingsmachine vervaardigt", "subtitles.block.crafter.fail": "Vervaardigingsmachine vervaardigde niets", "subtitles.block.creaking_heart.hurt": "Schorswakerhart gromt", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON> gel<PERSON>", "subtitles.block.creaking_heart.spawn": "Schorswakerhart ontwaakt", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "Versierde pot wordt gevuld", "subtitles.block.decorated_pot.insert_fail": "Versierde pot wiebelt", "subtitles.block.decorated_pot.shatter": "Versierde pot sneuvelt", "subtitles.block.dispenser.dispense": "Voorwerp wordt weggeschoten", "subtitles.block.dispenser.fail": "Dispenser faalt", "subtitles.block.door.toggle": "<PERSON><PERSON> kra<PERSON>t", "subtitles.block.dried_ghast.ambient": "Uitged<PERSON>g<PERSON> puft", "subtitles.block.dried_ghast.ambient_water": "Uitgedroog<PERSON> rehydrateert", "subtitles.block.dried_ghast.place_in_water": "Uitgedroogde Ghast wordt doordrenkt", "subtitles.block.dried_ghast.transition": "Uitgedroogde G<PERSON> voelt zich beter", "subtitles.block.dry_grass.ambient": "Windgeluiden", "subtitles.block.enchantment_table.use": "Betoveringstafel wordt gebruikt", "subtitles.block.end_portal.spawn": "Endportaal opent", "subtitles.block.end_portal_frame.fill": "Enderoog wordt geplaatst", "subtitles.block.eyeblossom.close": "Gluurbloesem sluit", "subtitles.block.eyeblossom.idle": "Gluurbloesem fluistert", "subtitles.block.eyeblossom.open": "Gluurbloesem opent", "subtitles.block.fence_gate.toggle": "Poort kraakt", "subtitles.block.fire.ambient": "<PERSON><PERSON><PERSON> knettert", "subtitles.block.fire.extinguish": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.block.firefly_bush.idle": "Vuurvliegjes <PERSON>", "subtitles.block.frogspawn.hatch": "Kikkerdril komt uit", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> knettert", "subtitles.block.generic.break": "Blok wordt gebroken", "subtitles.block.generic.fall": "<PERSON><PERSON> valt op een blok", "subtitles.block.generic.footsteps": "Voetstappen", "subtitles.block.generic.hit": "Blok wordt gebroken", "subtitles.block.generic.place": "Blok wordt geplaatst", "subtitles.block.grindstone.use": "Slijpsteen wordt gebruikt", "subtitles.block.growing_plant.crop": "Plant wordt gesnoeid", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON> wie<PERSON>", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON><PERSON> langs een honingblok", "subtitles.block.iron_trapdoor.close": "Valluik sluit", "subtitles.block.iron_trapdoor.open": "Valluik opent", "subtitles.block.lava.ambient": "<PERSON><PERSON> bor<PERSON>t", "subtitles.block.lava.extinguish": "Lava sist", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON> klikt", "subtitles.block.note_block.note": "Nootblok speelt", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON><PERSON> gel<PERSON>", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON> bewe<PERSON>t", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> d<PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> dru<PERSON>t in ketel", "subtitles.block.pointed_dripstone.drip_water": "Water druipt", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Water druipt in ketel", "subtitles.block.pointed_dripstone.land": "<PERSON><PERSON><PERSON><PERSON> stort neer", "subtitles.block.portal.ambient": "Portaal suist", "subtitles.block.portal.travel": "Portaalgeluid vervaagt", "subtitles.block.portal.trigger": "Portaalgeluid versterkt", "subtitles.block.pressure_plate.click": "Drukplaat klikt", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON> holt uit", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> sist", "subtitles.block.respawn_anchor.ambient": "Re<PERSON>awnanker suist", "subtitles.block.respawn_anchor.charge": "Respawnanker wordt opgeladen", "subtitles.block.respawn_anchor.deplete": "Respawnanker wordt gebruikt", "subtitles.block.respawn_anchor.set_spawn": "Respawnanker stelt spawnpunt in", "subtitles.block.sand.idle": "Zandgeluiden", "subtitles.block.sand.wind": "Windgeluiden", "subtitles.block.sculk.charge": "Sculk bubbelt", "subtitles.block.sculk.spread": "Sculk verspreidt", "subtitles.block.sculk_catalyst.bloom": "Sculkkatalysator bloeit", "subtitles.block.sculk_sensor.clicking": "Sculksensor begint te klikken", "subtitles.block.sculk_sensor.clicking_stop": "Sculksensor stopt te klikken", "subtitles.block.sculk_shrieker.shriek": "Sculkkrijser krijst", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON><PERSON> sluit", "subtitles.block.shulker_box.open": "Shulkerdoos opent", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON> wie<PERSON>", "subtitles.block.smithing_table.use": "Smeedstafel wordt gebruikt", "subtitles.block.smoker.smoke": "Roker rookt", "subtitles.block.sniffer_egg.crack": "Snuffelaarsei breekt", "subtitles.block.sniffer_egg.hatch": "Snuffelaarsei komt uit", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON><PERSON> plopt", "subtitles.block.sponge.absorb": "Spons absorbeert", "subtitles.block.sweet_berry_bush.pick_berries": "Bessen worden geplukt", "subtitles.block.trapdoor.close": "Valluik sluit", "subtitles.block.trapdoor.open": "Valluik opent", "subtitles.block.trapdoor.toggle": "Valluik kraakt", "subtitles.block.trial_spawner.about_to_spawn_item": "Onheilspellende beproevingskooi bereidt zich voor", "subtitles.block.trial_spawner.ambient": "Beproevingsko<PERSON> knettert", "subtitles.block.trial_spawner.ambient_charged": "Onheilspellende beproevingskooi knettert", "subtitles.block.trial_spawner.ambient_ominous": "Onheilspellende beproevingskooi knettert", "subtitles.block.trial_spawner.charge_activate": "<PERSON><PERSON><PERSON> ne<PERSON>t de beproevingskooi in bezit", "subtitles.block.trial_spawner.close_shutter": "Beproevingskooi sluit", "subtitles.block.trial_spawner.detect_player": "Beproevingskooi laadt op", "subtitles.block.trial_spawner.eject_item": "Beproevingskooi spuwt voorwerpen uit", "subtitles.block.trial_spawner.ominous_activate": "<PERSON><PERSON><PERSON> neemt beproevingskooi in bezit", "subtitles.block.trial_spawner.open_shutter": "Beproevingskooi opent", "subtitles.block.trial_spawner.spawn_item": "Onheilspellend voorwerp valt", "subtitles.block.trial_spawner.spawn_item_begin": "Onheilspellend voorwerp verschijnt", "subtitles.block.trial_spawner.spawn_mob": "Beproevingskooi spawnt een wezen", "subtitles.block.tripwire.attach": "Struikeldraad wordt vastgezet", "subtitles.block.tripwire.click": "Struikeldraad klikt", "subtitles.block.tripwire.detach": "Struikeldraad wordt losgekoppeld", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON> on<PERSON>", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON> knette<PERSON>", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON> sluit", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "<PERSON><PERSON><PERSON> werpt voorwerp uit", "subtitles.block.vault.insert_item": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.insert_item_fail": "Kluis blijft op slot", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON>t", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON> wijst al beloonde speler af", "subtitles.block.water.ambient": "Water stroomt", "subtitles.block.wet_sponge.dries": "Spons droogt op", "subtitles.chiseled_bookshelf.insert": "Boek wordt geplaatst", "subtitles.chiseled_bookshelf.insert_enchanted": "Betoverd boek wordt geplaatst", "subtitles.chiseled_bookshelf.take": "Boek wordt gepakt", "subtitles.chiseled_bookshelf.take_enchanted": "Betoverd boek wordt gepakt", "subtitles.enchant.thorns.hit": "Doornen prikken", "subtitles.entity.allay.ambient_with_item": "Helper zoe<PERSON>", "subtitles.entity.allay.ambient_without_item": "Helper verl<PERSON>t", "subtitles.entity.allay.death": "Helper sterft", "subtitles.entity.allay.hurt": "Helper raakt gewond", "subtitles.entity.allay.item_given": "Helper g<PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_taken": "Helper helpt", "subtitles.entity.allay.item_thrown": "Helper werpt", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON><PERSON> gromt", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON><PERSON> schuurt", "subtitles.entity.armadillo.death": "Gordeldier sterft", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON> eet", "subtitles.entity.armadillo.hurt": "Gordeldier raakt gewond", "subtitles.entity.armadillo.hurt_reduced": "Gordeldier beschermt zichzelf", "subtitles.entity.armadillo.land": "Gordeldier landt", "subtitles.entity.armadillo.peek": "Gordeldier gluurt", "subtitles.entity.armadillo.roll": "Gordeldier rolt op", "subtitles.entity.armadillo.scute_drop": "Gordeldierhoornplaat valt", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON><PERSON> ontrolt", "subtitles.entity.armadillo.unroll_start": "Gordeldier gluurt", "subtitles.entity.armor_stand.fall": "Iets viel", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON> raakt", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> wordt geraakt", "subtitles.entity.arrow.shoot": "Pijl wordt geschoten", "subtitles.entity.axolotl.attack": "Axolotl valt aan", "subtitles.entity.axolotl.death": "Axolotl sterft", "subtitles.entity.axolotl.hurt": "Axolotl raakt gewond", "subtitles.entity.axolotl.idle_air": "Axolotl tsjilpt", "subtitles.entity.axolotl.idle_water": "Axolotl tsjilpt", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON> spet<PERSON>t", "subtitles.entity.axolotl.swim": "Axolotl zwemt", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> pie<PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> sterft", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.bat.takeoff": "Vleermuis gaat vliegen", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.bee.loop": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON><PERSON> z<PERSON>t boos", "subtitles.entity.bee.pollinate": "<PERSON><PERSON><PERSON> zoe<PERSON>t blij", "subtitles.entity.bee.sting": "<PERSON><PERSON><PERSON> s<PERSON>kt", "subtitles.entity.blaze.ambient": "<PERSON> ademt", "subtitles.entity.blaze.burn": "<PERSON> knettert", "subtitles.entity.blaze.death": "<PERSON> sterft", "subtitles.entity.blaze.hurt": "Blaze raakt gewond", "subtitles.entity.blaze.shoot": "<PERSON> schiet", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Zomper ratelt", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.breeze.charge": "V<PERSON>ag blaast", "subtitles.entity.breeze.death": "Vlaag sterft", "subtitles.entity.breeze.deflect": "Vlaag kaatst af", "subtitles.entity.breeze.hurt": "Vlaag raakt gewond", "subtitles.entity.breeze.idle_air": "Vlaag vliegt", "subtitles.entity.breeze.idle_ground": "V<PERSON>ag briest", "subtitles.entity.breeze.inhale": "V<PERSON><PERSON> ademt in", "subtitles.entity.breeze.jump": "Vlaag springt", "subtitles.entity.breeze.land": "Vlaag landt", "subtitles.entity.breeze.shoot": "V<PERSON>ag schiet", "subtitles.entity.breeze.slide": "V<PERSON>ag schu<PERSON>", "subtitles.entity.breeze.whirl": "Vlaag wervelt", "subtitles.entity.breeze.wind_burst": "Windvlaag barst los", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON><PERSON> sprint", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON><PERSON>t", "subtitles.entity.camel.death": "Dr<PERSON><PERSON><PERSON> sterft", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON><PERSON> eet", "subtitles.entity.camel.hurt": "Dr<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.camel.saddle": "Zadel wordt opgelegd", "subtitles.entity.camel.sit": "Dromedaris zit", "subtitles.entity.camel.stand": "Dromedaris staat op", "subtitles.entity.camel.step": "Dromedar<PERSON> loopt", "subtitles.entity.camel.step_sand": "Dromedaris loopt in zand", "subtitles.entity.cat.ambient": "<PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON> smeekt", "subtitles.entity.cat.death": "<PERSON> sterft", "subtitles.entity.cat.eat": "Kat eet", "subtitles.entity.cat.hiss": "Kat sist", "subtitles.entity.cat.hurt": "Kat raakt gewond", "subtitles.entity.cat.purr": "<PERSON> spint", "subtitles.entity.chicken.ambient": "<PERSON><PERSON> ka<PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON> sterft", "subtitles.entity.chicken.egg": "<PERSON><PERSON> plopt", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> raakt gewond", "subtitles.entity.cod.death": "Kabeljauw sterft", "subtitles.entity.cod.flop": "Kabel<PERSON><PERSON>w flopt", "subtitles.entity.cod.hurt": "Kabeljauw raakt gewond", "subtitles.entity.cow.ambient": "<PERSON><PERSON> loeit", "subtitles.entity.cow.death": "<PERSON><PERSON> sterft", "subtitles.entity.cow.hurt": "<PERSON>e raakt gewond", "subtitles.entity.cow.milk": "Koe wordt gemolken", "subtitles.entity.creaking.activate": "Schorswaker kijkt", "subtitles.entity.creaking.ambient": "Schorswaker kraakt", "subtitles.entity.creaking.attack": "Schorswaker valt aan", "subtitles.entity.creaking.deactivate": "Schorswaker kalmeert", "subtitles.entity.creaking.death": "Schorswaker verkruimelt", "subtitles.entity.creaking.freeze": "Schorswaker stopt", "subtitles.entity.creaking.spawn": "Schorswaker manifesteert", "subtitles.entity.creaking.sway": "Schorswaker wordt geraakt", "subtitles.entity.creaking.twitch": "Schorswaker stuiptrekt", "subtitles.entity.creaking.unfreeze": "Schorswaker beweegt", "subtitles.entity.creeper.death": "Creeper sterft", "subtitles.entity.creeper.hurt": "Creeper raakt gewond", "subtitles.entity.creeper.primed": "Creeper sist", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON><PERSON> fluit", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON><PERSON> valt aan", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ft", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON><PERSON> eet", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> balkt", "subtitles.entity.donkey.angry": "<PERSON><PERSON> hinn<PERSON>", "subtitles.entity.donkey.chest": "Kist wordt op ezel gezet", "subtitles.entity.donkey.death": "<PERSON><PERSON> sterft", "subtitles.entity.donkey.eat": "<PERSON><PERSON> eet", "subtitles.entity.donkey.hurt": "<PERSON>zel raakt gewond", "subtitles.entity.donkey.jump": "<PERSON><PERSON> springt", "subtitles.entity.drowned.ambient": "Drenkeling gorgelt", "subtitles.entity.drowned.ambient_water": "Drenkeling gorgelt", "subtitles.entity.drowned.death": "Drenkeling sterft", "subtitles.entity.drowned.hurt": "Drenkeling raakt gewond", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON> gooit drietand", "subtitles.entity.drowned.step": "Drenkeling loopt", "subtitles.entity.drowned.swim": "Drenkeling zwemt", "subtitles.entity.egg.throw": "<PERSON><PERSON> vliegt", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON> bewaker kreunt", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON> bewaker flappert", "subtitles.entity.elder_guardian.curse": "<PERSON>ude bewaker vervloekt", "subtitles.entity.elder_guardian.death": "<PERSON>ude bewaker sterft", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON> bewaker flopt", "subtitles.entity.elder_guardian.hurt": "<PERSON>ude bewaker raakt gewond", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON> brult", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON> gromt", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> s<PERSON>et", "subtitles.entity.ender_eye.death": "Enderoog valt", "subtitles.entity.ender_eye.launch": "Enderoog schiet", "subtitles.entity.ender_pearl.throw": "Enderparel vliegt", "subtitles.entity.enderman.ambient": "Enderman vwoopt", "subtitles.entity.enderman.death": "<PERSON><PERSON> sterft", "subtitles.entity.enderman.hurt": "Enderman raakt gewond", "subtitles.entity.enderman.scream": "<PERSON><PERSON> schreeu<PERSON>t", "subtitles.entity.enderman.stare": "<PERSON><PERSON> schreeu<PERSON>t", "subtitles.entity.enderman.teleport": "<PERSON><PERSON> teleporteert", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.endermite.death": "<PERSON>ermi<PERSON><PERSON> sterft", "subtitles.entity.endermite.hurt": "Endermijt raakt gewond", "subtitles.entity.evoker.ambient": "Oproeper mompelt", "subtitles.entity.evoker.cast_spell": "Oproeper spreekt vloek uit", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON> juicht", "subtitles.entity.evoker.death": "Oproeper sterft", "subtitles.entity.evoker.hurt": "Oproeper raakt gewond", "subtitles.entity.evoker.prepare_attack": "Oproeper bereidt aanval voor", "subtitles.entity.evoker.prepare_summon": "Oproeper bereidt oproepen voor", "subtitles.entity.evoker.prepare_wololo": "Oproeper bereidt kleurverandering voor", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON> knappen", "subtitles.entity.experience_orb.pickup": "Ervaring verkregen", "subtitles.entity.firework_rocket.blast": "Vuurwerk knalt", "subtitles.entity.firework_rocket.launch": "Vuurwerk lanceert", "subtitles.entity.firework_rocket.twinkle": "Vuurwerk glinstert", "subtitles.entity.fish.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "Dobber wordt binnengehaald", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON> p<PERSON>t", "subtitles.entity.fishing_bobber.throw": "Dobber wordt gegooid", "subtitles.entity.fox.aggro": "Vos wordt boos", "subtitles.entity.fox.ambient": "Vos piept", "subtitles.entity.fox.bite": "<PERSON><PERSON>", "subtitles.entity.fox.death": "Vos sterft", "subtitles.entity.fox.eat": "Vos eet", "subtitles.entity.fox.hurt": "Vos raakt gewond", "subtitles.entity.fox.screech": "Vos piept", "subtitles.entity.fox.sleep": "<PERSON><PERSON>", "subtitles.entity.fox.sniff": "<PERSON>os rui<PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON> s<PERSON>t", "subtitles.entity.fox.teleport": "Vos teleporteert", "subtitles.entity.frog.ambient": "<PERSON><PERSON> k<PERSON>akt", "subtitles.entity.frog.death": "<PERSON><PERSON> sterft", "subtitles.entity.frog.eat": "<PERSON><PERSON> eet", "subtitles.entity.frog.hurt": "<PERSON><PERSON> raakt gewond", "subtitles.entity.frog.lay_spawn": "Kikker zet kikkerdril af", "subtitles.entity.frog.long_jump": "<PERSON><PERSON> springt", "subtitles.entity.generic.big_fall": "Iets viel", "subtitles.entity.generic.burn": "<PERSON><PERSON>", "subtitles.entity.generic.death": "Sterven", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "<PERSON><PERSON>", "subtitles.entity.generic.explode": "Explosie", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.generic.hurt": "Iets raakt gewond", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> strui<PERSON>de", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Windvlaag vlaagt", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> schree<PERSON><PERSON>t", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> schiet", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> pie<PERSON>", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> sterft", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.add_item": "Gloeiende voorwerplijst wordt gevuld", "subtitles.entity.glow_item_frame.break": "Gloeiende voorwerplijst breekt", "subtitles.entity.glow_item_frame.place": "Gloeiende voorwerplijst wordt geplaatst", "subtitles.entity.glow_item_frame.remove_item": "Gloeiende voorwerplijst wordt geleegd", "subtitles.entity.glow_item_frame.rotate_item": "Gloeiende voorwerplijst klikt", "subtitles.entity.glow_squid.ambient": "Gloeiende inktvis zwemt", "subtitles.entity.glow_squid.death": "Gloeiende inktvis sterft", "subtitles.entity.glow_squid.hurt": "Gloeiende inktvis raakt gewond", "subtitles.entity.glow_squid.squirt": "Gloeiende inktvis spuit inkt", "subtitles.entity.goat.ambient": "<PERSON><PERSON> blaat", "subtitles.entity.goat.death": "Geit sterft", "subtitles.entity.goat.eat": "Geit eet", "subtitles.entity.goat.horn_break": "Geitenhoorn breekt af", "subtitles.entity.goat.hurt": "Geit raakt gewond", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> springt", "subtitles.entity.goat.milk": "Geit wordt gemolken", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stompt", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON>t", "subtitles.entity.goat.screaming.ambient": "Geit brult", "subtitles.entity.goat.step": "<PERSON>eit loopt", "subtitles.entity.guardian.ambient": "Bewaker kreunt", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON> flappert", "subtitles.entity.guardian.attack": "Bewaker schiet", "subtitles.entity.guardian.death": "Bewaker sterft", "subtitles.entity.guardian.flop": "Bewaker flopt", "subtitles.entity.guardian.hurt": "Bewaker raakt gewond", "subtitles.entity.happy_ghast.ambient": "Vrolijke Ghast k<PERSON>rt", "subtitles.entity.happy_ghast.death": "Vrolijke Ghast sterft", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON> wordt aangedaan", "subtitles.entity.happy_ghast.harness_goggles_down": "Vrolijke Ghast s<PERSON> k<PERSON>ar", "subtitles.entity.happy_ghast.harness_goggles_up": "Vrolijke Ghast stopt", "subtitles.entity.happy_ghast.hurt": "Vrolijke Ghast raakt gewond", "subtitles.entity.happy_ghast.unequip": "Tuig wordt uitgedaan", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> gromt", "subtitles.entity.hoglin.angry": "<PERSON><PERSON>n gromt boos", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> valt aan", "subtitles.entity.hoglin.converted_to_zombified": "Hoglin wordt zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n raakt gewond", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON>n vlucht", "subtitles.entity.hoglin.step": "Hoglin loopt", "subtitles.entity.horse.ambient": "<PERSON><PERSON>nn<PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>nn<PERSON>", "subtitles.entity.horse.armor": "Paardenharnas wordt aangedaan", "subtitles.entity.horse.breathe": "<PERSON><PERSON> ademt", "subtitles.entity.horse.death": "<PERSON><PERSON> sterft", "subtitles.entity.horse.eat": "<PERSON>ard eet", "subtitles.entity.horse.gallop": "<PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON> raakt gewond", "subtitles.entity.horse.jump": "<PERSON><PERSON> springt", "subtitles.entity.horse.saddle": "Zadel wordt opgelegd", "subtitles.entity.husk.ambient": "<PERSON><PERSON> kreunt", "subtitles.entity.husk.converted_to_zombie": "Mummie werd een zombie", "subtitles.entity.husk.death": "Mummie sterft", "subtitles.entity.husk.hurt": "Mummie raakt gewond", "subtitles.entity.illusioner.ambient": "Illusionist mompelt", "subtitles.entity.illusioner.cast_spell": "Illusionist spree<PERSON> spreuk uit", "subtitles.entity.illusioner.death": "Illusionist sterft", "subtitles.entity.illusioner.hurt": "Illusionist raa<PERSON> gewond", "subtitles.entity.illusioner.mirror_move": "Illusion<PERSON> duplice<PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Illusionist be<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> voor", "subtitles.entity.illusioner.prepare_mirror": "Illusionist bere<PERSON><PERSON> spiegelbeeld voor", "subtitles.entity.iron_golem.attack": "IJzergolem valt aan", "subtitles.entity.iron_golem.damage": "IJzergolem breekt", "subtitles.entity.iron_golem.death": "IJzergolem sterft", "subtitles.entity.iron_golem.hurt": "IJzergolem raakt gewond", "subtitles.entity.iron_golem.repair": "IJzergolem wordt gerepareerd", "subtitles.entity.item.break": "Voorwerp breekt", "subtitles.entity.item.pickup": "Voorwerp plopt", "subtitles.entity.item_frame.add_item": "Voorwerplijst wordt gevuld", "subtitles.entity.item_frame.break": "Voorwerplijst breekt", "subtitles.entity.item_frame.place": "Voorwerplijst wordt geplaatst", "subtitles.entity.item_frame.remove_item": "Voorwerplijst wordt geleegd", "subtitles.entity.item_frame.rotate_item": "Voorwerplijst klikt", "subtitles.entity.leash_knot.break": "Leidtouwknoop breekt", "subtitles.entity.leash_knot.place": "Leidtouwknoop wordt vastgebonden", "subtitles.entity.lightning_bolt.impact": "Bliksem slaat in", "subtitles.entity.lightning_bolt.thunder": "Donderslag", "subtitles.entity.llama.ambient": "<PERSON> b<PERSON>", "subtitles.entity.llama.angry": "<PERSON> bla<PERSON> boos", "subtitles.entity.llama.chest": "Kist wordt op lama gezet", "subtitles.entity.llama.death": "<PERSON> sterft", "subtitles.entity.llama.eat": "<PERSON> eet", "subtitles.entity.llama.hurt": "<PERSON> raakt gewond", "subtitles.entity.llama.spit": "<PERSON> s<PERSON>", "subtitles.entity.llama.step": "<PERSON> loopt", "subtitles.entity.llama.swag": "<PERSON> wordt versierd", "subtitles.entity.magma_cube.death": "Magmakubus sterft", "subtitles.entity.magma_cube.hurt": "Magmakubus raakt gewond", "subtitles.entity.magma_cube.squish": "Magmakubus plet", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> ram<PERSON>", "subtitles.entity.minecart.inside_underwater": "<PERSON><PERSON><PERSON> rammelt onderwater", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON> rolt", "subtitles.entity.mooshroom.convert": "Mooshroom transformeert", "subtitles.entity.mooshroom.eat": "Mooshroom eet", "subtitles.entity.mooshroom.milk": "Mooshroom wordt gemolken", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom wordt geheimzinnig gemolken", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON> balkt", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> hinn<PERSON>", "subtitles.entity.mule.chest": "Kist wordt op muilezel gezet", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> eet", "subtitles.entity.mule.hurt": "Muilezel raakt gewond", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> breekt", "subtitles.entity.painting.place": "Schilderij wordt geplaatst", "subtitles.entity.panda.aggressive_ambient": "Panda tiert", "subtitles.entity.panda.ambient": "Panda hijgt", "subtitles.entity.panda.bite": "Panda bijt", "subtitles.entity.panda.cant_breed": "Panda blaat", "subtitles.entity.panda.death": "Panda sterft", "subtitles.entity.panda.eat": "Panda eet", "subtitles.entity.panda.hurt": "Panda raakt gewond", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON> van panda kriebelt", "subtitles.entity.panda.sneeze": "Panda niest", "subtitles.entity.panda.step": "Panda loopt", "subtitles.entity.panda.worried_ambient": "Panda jankt", "subtitles.entity.parrot.ambient": "Papegaai praat", "subtitles.entity.parrot.death": "Papegaai sterft", "subtitles.entity.parrot.eats": "Papegaai eet", "subtitles.entity.parrot.fly": "Papegaai fladdert", "subtitles.entity.parrot.hurts": "Papegaai raakt gewond", "subtitles.entity.parrot.imitate.blaze": "Papegaai ademt", "subtitles.entity.parrot.imitate.bogged": "Papegaai ratelt", "subtitles.entity.parrot.imitate.breeze": "Papegaai briest", "subtitles.entity.parrot.imitate.creaking": "Papegaai kraakt", "subtitles.entity.parrot.imitate.creeper": "Papegaai sist", "subtitles.entity.parrot.imitate.drowned": "Papegaai gorgelt", "subtitles.entity.parrot.imitate.elder_guardian": "Papegaai kreunt", "subtitles.entity.parrot.imitate.ender_dragon": "Papegaai brult", "subtitles.entity.parrot.imitate.endermite": "Papegaai schuifelt", "subtitles.entity.parrot.imitate.evoker": "Papegaai mompelt", "subtitles.entity.parrot.imitate.ghast": "Papegaai schreeuwt", "subtitles.entity.parrot.imitate.guardian": "Papegaai kreunt", "subtitles.entity.parrot.imitate.hoglin": "Papegaai gromt", "subtitles.entity.parrot.imitate.husk": "Papegaai kreunt", "subtitles.entity.parrot.imitate.illusioner": "Papegaai mompelt", "subtitles.entity.parrot.imitate.magma_cube": "Papegaai plet", "subtitles.entity.parrot.imitate.phantom": "Papegaai schreeuwt", "subtitles.entity.parrot.imitate.piglin": "Papegaai knort", "subtitles.entity.parrot.imitate.piglin_brute": "Papegaai knort", "subtitles.entity.parrot.imitate.pillager": "Papegaai mompelt", "subtitles.entity.parrot.imitate.ravager": "Papegaai gromt", "subtitles.entity.parrot.imitate.shulker": "Papegaai loert", "subtitles.entity.parrot.imitate.silverfish": "Papegaai sist", "subtitles.entity.parrot.imitate.skeleton": "Papegaai ratelt", "subtitles.entity.parrot.imitate.slime": "Papegaai plet", "subtitles.entity.parrot.imitate.spider": "Papegaai sist", "subtitles.entity.parrot.imitate.stray": "Papegaai ratelt", "subtitles.entity.parrot.imitate.vex": "Papegaai ergert", "subtitles.entity.parrot.imitate.vindicator": "Papegaai mompelt", "subtitles.entity.parrot.imitate.warden": "Papegaai jammert", "subtitles.entity.parrot.imitate.witch": "Papegaai gie<PERSON>t", "subtitles.entity.parrot.imitate.wither": "Papegaai wordt boos", "subtitles.entity.parrot.imitate.wither_skeleton": "Papegaai ratelt", "subtitles.entity.parrot.imitate.zoglin": "Papegaai gromt", "subtitles.entity.parrot.imitate.zombie": "Papegaai kreunt", "subtitles.entity.parrot.imitate.zombie_villager": "Papegaai kreunt", "subtitles.entity.phantom.ambient": "Fantoom schreeuwt", "subtitles.entity.phantom.bite": "Fantoom bijt", "subtitles.entity.phantom.death": "Fantoom sterft", "subtitles.entity.phantom.flap": "<PERSON><PERSON><PERSON> flappert", "subtitles.entity.phantom.hurt": "Fantoom raakt gewond", "subtitles.entity.phantom.swoop": "Fanto<PERSON> haalt uit", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON> knort", "subtitles.entity.pig.death": "Varken sterft", "subtitles.entity.pig.hurt": "Varken raakt gewond", "subtitles.entity.pig.saddle": "Zadel wordt opgelegd", "subtitles.entity.piglin.admiring_item": "<PERSON>lin bewondert voorwerp", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> knort", "subtitles.entity.piglin.angry": "<PERSON><PERSON> knort boos", "subtitles.entity.piglin.celebrate": "Piglin viert feest", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> wordt zombiepiglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> sterft", "subtitles.entity.piglin.hurt": "Piglin raakt gewond", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> knort jaloers", "subtitles.entity.piglin.retreat": "Piglin vlucht", "subtitles.entity.piglin.step": "<PERSON><PERSON> loopt", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> knort", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON> knort boos", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglinbruut wordt zombiepiglin", "subtitles.entity.piglin_brute.death": "Piglinbruut sterft", "subtitles.entity.piglin_brute.hurt": "Piglinbruut raakt gewond", "subtitles.entity.piglin_brute.step": "Piglinbruut loopt", "subtitles.entity.pillager.ambient": "Rover mompelt", "subtitles.entity.pillager.celebrate": "Rover juicht", "subtitles.entity.pillager.death": "Rover sterft", "subtitles.entity.pillager.hurt": "Rover raakt gewond", "subtitles.entity.player.attack.crit": "Kritische aanval", "subtitles.entity.player.attack.knockback": "Terugslagaanval", "subtitles.entity.player.attack.strong": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.sweep": "Verscherpte a<PERSON>val", "subtitles.entity.player.attack.weak": "Zwakke a<PERSON>", "subtitles.entity.player.burp": "Boer", "subtitles.entity.player.death": "<PERSON>peler sterft", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON> bev<PERSON>t", "subtitles.entity.player.hurt": "<PERSON><PERSON>er raakt gewond", "subtitles.entity.player.hurt_drown": "Speler verdrinkt", "subtitles.entity.player.hurt_on_fire": "<PERSON>pel<PERSON> brandt", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> gaat niveau omhoog", "subtitles.entity.player.teleport": "<PERSON><PERSON><PERSON> teleporteert", "subtitles.entity.polar_bear.ambient": "IJsbeer gromt", "subtitles.entity.polar_bear.ambient_baby": "IJsbeer bromt", "subtitles.entity.polar_bear.death": "IJsbeer sterft", "subtitles.entity.polar_bear.hurt": "IJsbeer raakt gewond", "subtitles.entity.polar_bear.warning": "IJsbeer brult", "subtitles.entity.potion.splash": "<PERSON>les breekt", "subtitles.entity.potion.throw": "Fles wordt gegooid", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON><PERSON>tl<PERSON>", "subtitles.entity.puffer_fish.blow_up": "Kogelvis blaast op", "subtitles.entity.puffer_fish.death": "<PERSON>gelvis sterft", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON><PERSON> flopt", "subtitles.entity.puffer_fish.hurt": "Kogelvis raakt gewond", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON><PERSON> steekt", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> valt aan", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON>jn raakt gewond", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.ambient": "Verwoester gromt", "subtitles.entity.ravager.attack": "Verwoester bijt", "subtitles.entity.ravager.celebrate": "Verwo<PERSON> juicht", "subtitles.entity.ravager.death": "Verwoester sterft", "subtitles.entity.ravager.hurt": "Verwoester raakt gewond", "subtitles.entity.ravager.roar": "Verwoester brult", "subtitles.entity.ravager.step": "Verwoester loopt", "subtitles.entity.ravager.stunned": "Verwoester wordt verdooft", "subtitles.entity.salmon.death": "<PERSON><PERSON>m sterft", "subtitles.entity.salmon.flop": "Zalm flopt", "subtitles.entity.salmon.hurt": "Zalm raakt gewond", "subtitles.entity.sheep.ambient": "Sc<PERSON><PERSON> blaat", "subtitles.entity.sheep.death": "Schaap sterft", "subtitles.entity.sheep.hurt": "Schaap raakt gewond", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> loert", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> sluit", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opent", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> schiet", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleport<PERSON>t", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker_bullet.hurt": "Shulkerkogel breekt", "subtitles.entity.silverfish.ambient": "Zilver<PERSON> sist", "subtitles.entity.silverfish.death": "Zilvervis sterft", "subtitles.entity.silverfish.hurt": "Zilvervis raakt gewond", "subtitles.entity.skeleton.ambient": "Skelet rammelt", "subtitles.entity.skeleton.converted_to_stray": "Skelet wordt een verdwaalde", "subtitles.entity.skeleton.death": "Skelet sterft", "subtitles.entity.skeleton.hurt": "Skelet raakt gewond", "subtitles.entity.skeleton.shoot": "Skelet schiet", "subtitles.entity.skeleton_horse.ambient": "Skelet<PERSON><PERSON> schreeu<PERSON>t", "subtitles.entity.skeleton_horse.death": "Skeletpaard sterft", "subtitles.entity.skeleton_horse.hurt": "Skeletpaard raakt gewond", "subtitles.entity.skeleton_horse.jump_water": "Skeletpaard springt", "subtitles.entity.skeleton_horse.swim": "Skeletpaard zwemt", "subtitles.entity.slime.attack": "Slijmkubus valt aan", "subtitles.entity.slime.death": "Slijmkubus sterft", "subtitles.entity.slime.hurt": "Slijmkubus raakt gewond", "subtitles.entity.slime.squish": "Slijmkubus plet", "subtitles.entity.sniffer.death": "S<PERSON><PERSON>laar sterft", "subtitles.entity.sniffer.digging": "Snuffelaar graaft", "subtitles.entity.sniffer.digging_stop": "Snuffelaar staat op", "subtitles.entity.sniffer.drop_seed": "S<PERSON>ffelaar laat zaad vallen", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> eet", "subtitles.entity.sniffer.egg_crack": "Snuffelaarsei breekt", "subtitles.entity.sniffer.egg_hatch": "Snuffelaarsei komt uit", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON> geniet", "subtitles.entity.sniffer.hurt": "Snuffelaar raakt gewond", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON> knort", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruikt", "subtitles.entity.sniffer.searching": "S<PERSON><PERSON>laar z<PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON><PERSON> snuft", "subtitles.entity.sniffer.step": "Snuffelaar loopt", "subtitles.entity.snow_golem.death": "Sneeuwgolem sterft", "subtitles.entity.snow_golem.hurt": "Sneeuwgolem raakt gewond", "subtitles.entity.snowball.throw": "Sneeuwbal vliegt", "subtitles.entity.spider.ambient": "Spin sist", "subtitles.entity.spider.death": "Spin sterft", "subtitles.entity.spider.hurt": "Spin raakt gewond", "subtitles.entity.squid.ambient": "Inktvis zwemt", "subtitles.entity.squid.death": "Inktvis sterft", "subtitles.entity.squid.hurt": "Inktvis raakt gewond", "subtitles.entity.squid.squirt": "Inktvis spuit inkt", "subtitles.entity.stray.ambient": "Verdwaalde ratelt", "subtitles.entity.stray.death": "Verdwaalde sterft", "subtitles.entity.stray.hurt": "Verdwaalde raakt gewond", "subtitles.entity.strider.death": "Lavar<PERSON><PERSON><PERSON> sterft", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> eet", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.hurt": "Lavar<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "Lavarijder trekt zich terug", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON> flopt", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON> wordt volwassen", "subtitles.entity.tadpole.hurt": "Kikkervis raakt gewond", "subtitles.entity.tnt.primed": "TNT sist", "subtitles.entity.tropical_fish.death": "Tropische vis sterft", "subtitles.entity.tropical_fish.flop": "Tropische vis flopt", "subtitles.entity.tropical_fish.hurt": "Tropische vis raakt gewond", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON>pad tsjilpt", "subtitles.entity.turtle.death": "Sc<PERSON>dpad sterft", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sterft", "subtitles.entity.turtle.egg_break": "Sc<PERSON>dpadei breekt", "subtitles.entity.turtle.egg_crack": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>t", "subtitles.entity.turtle.egg_hatch": "Schildpadei komt uit", "subtitles.entity.turtle.hurt": "Sc<PERSON>dpad raakt gewond", "subtitles.entity.turtle.hurt_baby": "Schildpadbaby raakt gewond", "subtitles.entity.turtle.lay_egg": "Sc<PERSON><PERSON>pad legt ei", "subtitles.entity.turtle.shamble": "Schildpad loopt langzaam", "subtitles.entity.turtle.shamble_baby": "Schildpadbaby loopt langzaam", "subtitles.entity.turtle.swim": "Schildpad zwemt", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON> ergert", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> kri<PERSON>", "subtitles.entity.vex.death": "<PERSON><PERSON>nis sterft", "subtitles.entity.vex.hurt": "Ergernis raakt gewond", "subtitles.entity.villager.ambient": "Dorpeling mompelt", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON><PERSON> juicht", "subtitles.entity.villager.death": "Dorpeling sterft", "subtitles.entity.villager.hurt": "Dorpeling raakt gewond", "subtitles.entity.villager.no": "Do<PERSON>eling gaat niet akko<PERSON>", "subtitles.entity.villager.trade": "Dorpeling ruilt", "subtitles.entity.villager.work_armorer": "Harnassmid werkt", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON> werkt", "subtitles.entity.villager.work_cartographer": "Kartograaf werkt", "subtitles.entity.villager.work_cleric": "Geestelijke werkt", "subtitles.entity.villager.work_farmer": "Boer werkt", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON> werkt", "subtitles.entity.villager.work_fletcher": "Pijlenmaker werkt", "subtitles.entity.villager.work_leatherworker": "Leerlooier werkt", "subtitles.entity.villager.work_librarian": "Bibliothecaris we<PERSON>t", "subtitles.entity.villager.work_mason": "Steenhouwer werkt", "subtitles.entity.villager.work_shepherd": "Herder werkt", "subtitles.entity.villager.work_toolsmith": "Gereedschapssmid werkt", "subtitles.entity.villager.work_weaponsmith": "Wapensmid werkt", "subtitles.entity.villager.yes": "Dorpeling gaat akkoord", "subtitles.entity.vindicator.ambient": "Verdediger mompelt", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON> jui<PERSON>", "subtitles.entity.vindicator.death": "Verdediger sterft", "subtitles.entity.vindicator.hurt": "Verdediger raakt gewond", "subtitles.entity.wandering_trader.ambient": "Rondreizende handelaar mompelt", "subtitles.entity.wandering_trader.death": "Rondreizende handelaar sterft", "subtitles.entity.wandering_trader.disappeared": "Rondreizende handelaar verdwijnt", "subtitles.entity.wandering_trader.drink_milk": "Rondreizende handelaar drinkt melk", "subtitles.entity.wandering_trader.drink_potion": "Rondreizende handelaar drinkt drank", "subtitles.entity.wandering_trader.hurt": "Rondreizende handelaar raakt gewond", "subtitles.entity.wandering_trader.no": "Rondreizende handelaar gaat niet akkoord", "subtitles.entity.wandering_trader.reappeared": "Rondreizende handelaar verschijnt", "subtitles.entity.wandering_trader.trade": "Rondreizende handelaar ruilt", "subtitles.entity.wandering_trader.yes": "Rondreizende handelaar gaat akkoord", "subtitles.entity.warden.agitated": "Hoeder kreunt boos", "subtitles.entity.warden.ambient": "<PERSON>eder jammert", "subtitles.entity.warden.angry": "Hoeder tiert", "subtitles.entity.warden.attack_impact": "Hoeder treft", "subtitles.entity.warden.death": "Hoeder sterft", "subtitles.entity.warden.dig": "Hoeder graaft", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.heartbeat": "Ho<PERSON>rhartslag", "subtitles.entity.warden.hurt": "<PERSON><PERSON>r raakt gewond", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> ne<PERSON>t waar", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON>r ne<PERSON>t boos waar", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON>r nadert", "subtitles.entity.warden.nearby_closer": "Hoeder loopt door", "subtitles.entity.warden.nearby_closest": "<PERSON>eder komt dichtbij", "subtitles.entity.warden.roar": "<PERSON>eder brult", "subtitles.entity.warden.sniff": "<PERSON>ede<PERSON> snuift", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON>r vuurt af", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> la<PERSON>t op", "subtitles.entity.warden.step": "Hoeder loopt", "subtitles.entity.warden.tendril_clicks": "Hoederhoorsprieten klikken", "subtitles.entity.wind_charge.throw": "Windvlaag jaagt", "subtitles.entity.wind_charge.wind_burst": "Windvlaag barst los", "subtitles.entity.witch.ambient": "<PERSON><PERSON> g<PERSON>t", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> jui<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON> sterft", "subtitles.entity.witch.drink": "<PERSON><PERSON> drinkt", "subtitles.entity.witch.hurt": "<PERSON>ks raakt gewond", "subtitles.entity.witch.throw": "<PERSON><PERSON> gooit", "subtitles.entity.wither.ambient": "Wither wordt boos", "subtitles.entity.wither.death": "Wither sterft", "subtitles.entity.wither.hurt": "<PERSON>er raakt gewond", "subtitles.entity.wither.shoot": "Wither valt aan", "subtitles.entity.wither.spawn": "Wither wordt vrijgelaten", "subtitles.entity.wither_skeleton.ambient": "Witherskelet ratelt", "subtitles.entity.wither_skeleton.death": "Witherskelet sterft", "subtitles.entity.wither_skeleton.hurt": "Witherskelet raakt gewond", "subtitles.entity.wolf.ambient": "<PERSON> hijgt", "subtitles.entity.wolf.bark": "<PERSON> blaft", "subtitles.entity.wolf.death": "<PERSON> sterft", "subtitles.entity.wolf.growl": "<PERSON> gromt", "subtitles.entity.wolf.hurt": "<PERSON> raakt gewond", "subtitles.entity.wolf.pant": "<PERSON> hijgt", "subtitles.entity.wolf.shake": "<PERSON> schu<PERSON>", "subtitles.entity.wolf.whine": "<PERSON> jammert", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> gromt", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> gromt boos", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> valt aan", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.zoglin.hurt": "Zoglin raakt gewond", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> loopt", "subtitles.entity.zombie.ambient": "<PERSON> gromt", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON> schudt", "subtitles.entity.zombie.break_wooden_door": "Deur breekt", "subtitles.entity.zombie.converted_to_drowned": "Zombie wordt een drenkeling", "subtitles.entity.zombie.death": "Zombie sterft", "subtitles.entity.zombie.destroy_egg": "Schildpadei wordt kapotgetrapt", "subtitles.entity.zombie.hurt": "Zombie raakt gewond", "subtitles.entity.zombie.infect": "Zombie infecteert", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON>ard huilt", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> sterft", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> raakt gewond", "subtitles.entity.zombie_villager.ambient": "Zombiedorpeling kreunt", "subtitles.entity.zombie_villager.converted": "Zombiedorpeling schreeuwt", "subtitles.entity.zombie_villager.cure": "Zombiedorpeling snuift", "subtitles.entity.zombie_villager.death": "Zombiedorpeling sterft", "subtitles.entity.zombie_villager.hurt": "Zombiedorpeling raakt gewond", "subtitles.entity.zombified_piglin.ambient": "Zombiepiglin gromt", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON><PERSON> gromt boos", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON>n sterft", "subtitles.entity.zombified_piglin.hurt": "Zombiepiglin raakt gewond", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON><PERSON> neemt in bezit", "subtitles.event.mob_effect.raid_omen": "<PERSON><PERSON> overval ligt op de loer", "subtitles.event.mob_effect.trial_omen": "<PERSON>en onheilspellende beproeving ligt op de loer", "subtitles.event.raid.horn": "Onheilspellende hoorn schalt", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON> wordt aangetrokken", "subtitles.item.armor.equip_chain": "Maliënh<PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON> harnas rammelt", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> ruist", "subtitles.item.armor.equip_gold": "<PERSON>uden harnas rammelt", "subtitles.item.armor.equip_iron": "IJzeren harnas rammelt", "subtitles.item.armor.equip_leather": "<PERSON><PERSON> harna<PERSON> r<PERSON>t", "subtitles.item.armor.equip_netherite": "<PERSON><PERSON><PERSON><PERSON> harnas klettert", "subtitles.item.armor.equip_turtle": "Schildpadschild bonkt", "subtitles.item.armor.equip_wolf": "Wolvenharnas wordt aangetrokken", "subtitles.item.armor.unequip_wolf": "Wolvenharnas wordt uitgetrokken", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.axe.wax_off": "Inwassen wordt ongedaan gemaakt", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON><PERSON> kreukelt", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON> ruist", "subtitles.item.book.put": "<PERSON><PERSON>", "subtitles.item.bottle.empty": "<PERSON><PERSON> le<PERSON>t", "subtitles.item.bottle.fill": "Fles vult", "subtitles.item.brush.brushing.generic": "Geborstel", "subtitles.item.brush.brushing.gravel": "Grind wordt geborsteld", "subtitles.item.brush.brushing.gravel.complete": "Grind weggeborsteld", "subtitles.item.brush.brushing.sand": "Zand wordt geborsteld", "subtitles.item.brush.brushing.sand.complete": "Zand weggeborsteld", "subtitles.item.bucket.empty": "<PERSON><PERSON> le<PERSON>", "subtitles.item.bucket.fill": "Emmer vult", "subtitles.item.bucket.fill_axolotl": "Axolotl wordt opgeschept", "subtitles.item.bucket.fill_fish": "Vis wordt gevangen", "subtitles.item.bucket.fill_tadpole": "<PERSON>kker<PERSON> wordt gevangen", "subtitles.item.bundle.drop_contents": "Bundel wordt geleegd", "subtitles.item.bundle.insert": "Voorwerp wordt ingepakt", "subtitles.item.bundle.insert_fail": "<PERSON>le bundel", "subtitles.item.bundle.remove_one": "Voorwerp wordt uitgepakt", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON> teleporteert", "subtitles.item.crop.plant": "G<PERSON>as wordt geplant", "subtitles.item.crossbow.charge": "Kruisboog wordt gespannen", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON> raakt", "subtitles.item.crossbow.load": "Kruisboog wordt geladen", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> schiet", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON><PERSON> suist", "subtitles.item.flintandsteel.use": "Vuursteen en staal k<PERSON>t", "subtitles.item.glow_ink_sac.use": "<PERSON><PERSON>ei<PERSON><PERSON> inktzak smeert", "subtitles.item.goat_horn.play": "Geiten<PERSON><PERSON> speelt", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON> spit om", "subtitles.item.honey_bottle.drink": "Geslurp", "subtitles.item.honeycomb.wax_on": "Blok wordt ingewassen", "subtitles.item.horse_armor.unequip": "Paardenharnas wordt uitgetrokken", "subtitles.item.ink_sac.use": "Inktzak smeert", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> knapt", "subtitles.item.lead.tied": "Leidtouw wordt vastgebonden", "subtitles.item.lead.untied": "Leidtouw wordt losgemaakt", "subtitles.item.llama_carpet.unequip": "Tapijt wordt afgelegd", "subtitles.item.lodestone_compass.lock": "Kompas past zich aan aan zeil<PERSON>en", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON> z<PERSON>", "subtitles.item.mace.smash_ground": "Knot<PERSON> plet", "subtitles.item.nether_wart.plant": "Netherkruid wordt geplant", "subtitles.item.ominous_bottle.dispose": "<PERSON>les breekt", "subtitles.item.saddle.unequip": "Zadel wordt afgelegd", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON> kn<PERSON>t", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON> kn<PERSON>t", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON> plet", "subtitles.item.spyglass.stop_using": "Verrekijker schuift in", "subtitles.item.spyglass.use": "Verrekijker schuift uit", "subtitles.item.totem.use": "Totem activeert", "subtitles.item.trident.hit": "<PERSON><PERSON><PERSON> steekt", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON> trilt", "subtitles.item.trident.return": "Drietand komt terug", "subtitles.item.trident.riptide": "Drietand zoemt", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON> klettert", "subtitles.item.trident.thunder": "Dr<PERSON><PERSON><PERSON><PERSON> knettert", "subtitles.item.wolf_armor.break": "Wolvenharnas breekt", "subtitles.item.wolf_armor.crack": "Wolvenharnas kraakt", "subtitles.item.wolf_armor.damage": "Wolvenharnas loopt schade op", "subtitles.item.wolf_armor.repair": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd gerepareerd", "subtitles.particle.soul_escape": "<PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON> wordt getekend", "subtitles.ui.hud.bubble_pop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>t", "subtitles.ui.loom.take_result": "Weefgetouw wordt gebruikt", "subtitles.ui.stonecutter.take_result": "Steenzaag wordt gebruikt", "subtitles.weather.rain": "Regen valt", "symlink_warning.message": "Werelden uit mappen laden met symbolische links kan onveilig zijn als je niet weet wat je doet. <PERSON><PERSON> meer op %s.", "symlink_warning.message.pack": "<PERSON><PERSON><PERSON> met symbolische links laden kan onveilig zijn als je niet weet wat je doet. <PERSON><PERSON> meer op %s.", "symlink_warning.message.world": "Werelden uit mappen laden met symbolische links kan onveilig zijn als je niet weet wat je doet. <PERSON><PERSON> meer op %s.", "symlink_warning.more_info": "Meer informatie", "symlink_warning.title": "Wereldmap bevat symbolische links", "symlink_warning.title.pack": "Toegevoegd(e) pakket(ten) bevat(ten) symbolische links", "symlink_warning.title.world": "Wereldmap bevat symbolische links", "team.collision.always": "Altijd", "team.collision.never": "Nooit", "team.collision.pushOtherTeams": "Andere teams duwen", "team.collision.pushOwnTeam": "Eigen team duwen", "team.notFound": "Team '%s' is onbekend", "team.visibility.always": "Altijd", "team.visibility.hideForOtherTeams": "Verbergen voor andere teams", "team.visibility.hideForOwnTeam": "Verbergen voor eigen team", "team.visibility.never": "Nooit", "telemetry.event.advancement_made.description": "Door de context bij het boeken van vooruitgang te begrijpen, kunnen we de voortgang van het spel beter begrijpen en verbeteren.", "telemetry.event.advancement_made.title": "Vooruitgang geboekt", "telemetry.event.game_load_times.description": "Deze gebeurtenis helpt ons vast te stellen waar de verbeterpunten liggen bij het opstarten door de uitvoertijd van opstartfasen te meten.", "telemetry.event.game_load_times.title": "S<PERSON>ad<PERSON>j<PERSON>", "telemetry.event.optional": "%s (optioneel)", "telemetry.event.optional.disabled": "%s (optioneel) - uitgeschakeld", "telemetry.event.performance_metrics.description": "Met prestatiegegevens uit Minecraft kunnen we het spel afstemmen op en optimaliseren voor een breed scala aan apparaten met verschillende specificaties en besturingssystemen. \nDe spelversie wordt meegestuurd zodat we de prestatieprofielen kunnen vergelijken met nieuwe Minecraftversies.", "telemetry.event.performance_metrics.title": "Prestatiegegevens", "telemetry.event.required": "%s (vereist)", "telemetry.event.world_load_times.description": "Het is voor ons belangrijk te weten hoelang het duurt om een wereld te laden en hoe dat met de tijd verandert. Wanneer we bijvoorbeeld nieuwe functies toevoegen of grote technische wijzigingen uitvoeren, moeten we zien welke impact dit heeft op de laadtijd.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "Door te weten hoe spelers Minecraft spelen (bijvoorbeeld spelmodus, gemodificeerde cliënt of server en spelversie), kunnen we spelupdates focussen op gebieden die spelers het meest aangaan. De wereld laden-gebeurtenis vormt een koppel met de wereld ontladen-gebeurtenis en gebruiken we om te berekenen hoe lang een wereldsessie duurde.", "telemetry.event.world_loaded.title": "<PERSON><PERSON> geladen", "telemetry.event.world_unloaded.description": "De wereld ontladen-gebeurtenis vormt een koppel met de wereld laden-gebeurtenis en gebruiken we om te berekenen hoelang een wereldsessie duurde.\nDe duur (in seconden en ticks) wordt gemeten wanneer een wereldsessie stopt (teruggaan naar hoofdmenu en verbinding met server verbreken).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ticks)", "telemetry.property.advancement_id.title": "Vooruitgangs-ID", "telemetry.property.client_id.title": "Cliënt-ID", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON><PERSON> gem<PERSON>", "telemetry.property.dedicated_memory_kb.title": "Toegewezen geheugen (kB)", "telemetry.property.event_timestamp_utc.title": "Gebeurtenistijdstip (UTC)", "telemetry.property.frame_rate_samples.title": "Steekproef framerate (fps)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "Spelversie", "telemetry.property.launcher_name.title": "Launchernaam", "telemetry.property.load_time_bootstrap_ms.title": "Bootstraptijd (milliseconden)", "telemetry.property.load_time_loading_overlay_ms.title": "Tijd in laadscherm (milliseconden)", "telemetry.property.load_time_pre_window_ms.title": "Tijd tot openen venster (milliseconden)", "telemetry.property.load_time_total_time_ms.title": "Totale laadtijd (milliseconden)", "telemetry.property.minecraft_session_id.title": "Minecraftsessie-ID", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON> wereld", "telemetry.property.number_of_samples.title": "Steekproeflengte", "telemetry.property.operating_system.title": "Besturingssysteem", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realmswereldinhoud (naam minispel)", "telemetry.property.render_distance.title": "Weergavebereik", "telemetry.property.render_time_samples.title": "Steekproef weergavetijd", "telemetry.property.seconds_since_load.title": "<PERSON>i<PERSON><PERSON> sinds laden (seconden)", "telemetry.property.server_modded.title": "Server gemodificeerd", "telemetry.property.server_type.title": "Servertype", "telemetry.property.ticks_since_load.title": "Tijd sinds laden (ticks)", "telemetry.property.used_memory_samples.title": "Werkgeheugengebruik", "telemetry.property.user_id.title": "Gebruikers-ID", "telemetry.property.world_load_time_ms.title": "<PERSON>ld<PERSON><PERSON>ti<PERSON><PERSON> (milliseconden)", "telemetry.property.world_session_id.title": "Wereldsessie-ID", "telemetry_info.button.give_feedback": "Feedback geven", "telemetry_info.button.privacy_statement": "Privacyinformatie", "telemetry_info.button.show_data": "Gegevens be<PERSON>", "telemetry_info.opt_in.description": "<PERSON><PERSON> ga <PERSON><PERSON><PERSON><PERSON> met het verst<PERSON> van verzamelde optionele gege<PERSON>s", "telemetry_info.property_title": "Inbegrepen gegevens", "telemetry_info.screen.description": "Door deze gegevens te verzamelen, help je ons Minecraft te verbeteren in richtingen die relevant zijn voor onze spelers.\nJe kunt ook extra feedback geven, zodat we Minecraft kunnen blijven verbeteren.", "telemetry_info.screen.title": "Gegevensverzameling", "test.error.block_property_mismatch": "Verwachtte dat eigenschap %s %s was; %s aangetroffen", "test.error.block_property_missing": "Blokeigenschap ontbreekt; verwachtte %2$s voor eigenschap %1$s", "test.error.entity_property": "Entiteit %s faalde test: %s", "test.error.entity_property_details": "Entiteit %s faalde test: %s; verwachtte:%s; %s aangetroffen", "test.error.expected_block": "Verwachtte blok %s, %s aangetroffen", "test.error.expected_block_tag": "Verwachtte blok in #%s, %s aangetroffen", "test.error.expected_container_contents": "Inventaris dient te bevatten: %s", "test.error.expected_container_contents_single": "Inventaris dient eenmaal te bevatten: %s", "test.error.expected_empty_container": "Inventaris dient leeg te zijn", "test.error.expected_entity": "Verwachtte %s", "test.error.expected_entity_around": "Verwachtte dat %s bestond rondom %s, %s, %s", "test.error.expected_entity_count": "Verwachtte %s entiteiten van type %s, %s aangetroffen", "test.error.expected_entity_data": "Verwachtte entiteitdata %s, %s aangetroffen", "test.error.expected_entity_data_predicate": "Entiteitdata komt niet overeen voor %s", "test.error.expected_entity_effect": "Verwachtte dat %s effect %s %s had", "test.error.expected_entity_having": "Entiteitsinventaris moet %s bevatten", "test.error.expected_entity_holding": "Entiteit zou %s moeten vasthouden", "test.error.expected_entity_in_test": "Verwachtte dat %s bestond in test", "test.error.expected_entity_not_touching": "Verwachtte niet dat %s op %s, %s, %s (relatief: %s, %s, %s) aanraakte", "test.error.expected_entity_touching": "Verwachtte dat %s op %s, %s, %s (relatief: %s, %s, %s) aanraakte", "test.error.expected_item": "Verwachtte voorwerp van type %s", "test.error.expected_items_count": "Verwachtte %s voorwerpen van type %s; %s aangetroffen", "test.error.fail": "<PERSON><PERSON> mi<PERSON>lukkingsvoorwaarden voldaan", "test.error.invalid_block_type": "Onverwacht bloktype gevonden: %s", "test.error.missing_block_entity": "Ontbrekend blokentiteit", "test.error.position": "%s op %s, %s, %s (relatief: %s, %s, %s) om tick %s", "test.error.sequence.condition_already_triggered": "Aan voorwaarde al voldaan op %s", "test.error.sequence.condition_not_triggered": "<PERSON><PERSON> voor<PERSON>e niet voldaan", "test.error.sequence.invalid_tick": "Gelukt tijdens ongeldige tick: verwachtte %s", "test.error.sequence.not_completed": "Test eindigde voordat de reeks werd afgerond", "test.error.set_biome": "Klimaat instellen voor test mislukt", "test.error.spawn_failure": "Entiteit %s maken mislukt", "test.error.state_not_equal": "Incorrecte status. Verwachtte %s; %s aangetroffen", "test.error.structure.failure": "Testconstructie plaatsen voor %s mislukt", "test.error.tick": "%s om tick %s", "test.error.ticking_without_structure": "Tickende test voordat een constructie werd geplaatst", "test.error.timeout.no_result": "Test niet ges<PERSON>d of mislukt binnen %s ticks", "test.error.timeout.no_sequences_finished": "<PERSON><PERSON> reeksen binnen %s ticks afgerond", "test.error.too_many_entities": "Verwachtte dat slechts één %s bestond rondom %s, %s, %s; %s aangetroffen", "test.error.unexpected_block": "Verwachtte niet dat het blok %s was", "test.error.unexpected_entity": "Verwachtte niet dat %s bestond", "test.error.unexpected_item": "Verwachtte geen voorwerp van type %s", "test.error.unknown": "Onbekende interne fout: %s", "test.error.value_not_equal": "Verwachtte dat %s %s was; %s aangetroffen", "test.error.wrong_block_entity": "Incorrect blokentiteittype: %s", "test_block.error.missing": "In testconstructie ontbreekt blok %s", "test_block.error.too_many": "Te veel %s-blokken", "test_block.invalid_timeout": "Ongeldige time-out (%s): dit moet een postief aantal ticks zijn", "test_block.message": "Bericht:", "test_block.mode.accept": "Accept<PERSON><PERSON>", "test_block.mode.fail": "Mislukken", "test_block.mode.log": "Logboek", "test_block.mode.start": "Starten", "test_block.mode_info.accept": "Acceptatiemodus: accepteert succes voor (een deel van) een test", "test_block.mode_info.fail": "Mislukkingsmodus: de test mislukt", "test_block.mode_info.log": "Logboekmodus: leg een bericht vast", "test_block.mode_info.start": "Startmodus: het begin van een test", "test_instance.action.reset": "Resetten en laden", "test_instance.action.run": "Laden en uitvoeren", "test_instance.action.save": "Construct<PERSON> op<PERSON>an", "test_instance.description.batch": "Groep: %s", "test_instance.description.failed": "Mislukt: %s", "test_instance.description.function": "Functie: %s", "test_instance.description.invalid_id": "Ongeldige test-ID", "test_instance.description.no_test": "Niet-best<PERSON><PERSON> test", "test_instance.description.structure": "Constructie: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Blokgebaseerde test", "test_instance.type.function": "Ingebouwde functietest", "test_instance_block.entities": "Entiteiten:", "test_instance_block.error.no_test": "Testinstantie uitvoeren op %s, %s, %s onmogelijk omdat deze geen vastgelegde test heeft", "test_instance_block.error.no_test_structure": "Testinstantie uitvoeren op %s, %s, %s onmogelijk omdat deze geen testconstructie heeft", "test_instance_block.error.unable_to_save": "Testconstructiesjabloon voor testinstantie op %s, %s, %s opslaan mislukt", "test_instance_block.invalid": "[ongeldig]", "test_instance_block.reset_success": "Test succesvol gereset voor %s", "test_instance_block.rotation": "Rotatie:", "test_instance_block.size": "Testconstructuegrootte", "test_instance_block.starting": "Test %s starten", "test_instance_block.test_id": "Testinstantie-ID", "title.32bit.deprecation": "32-bitssysteem gedetecteerd: in de toekomst kun je mogelijk niet meer spelen omdat een 64-bitssysteem vereist wordt!", "title.32bit.deprecation.realms": "Binnenkort vereist Minecraft een 64-bitssyste<PERSON>, waard<PERSON> je niet meer kunt spelen of Realms kunt gebruiken op dit apparaat. Realmsabonnementen moeten handmatig geannuleerd worden.", "title.32bit.deprecation.realms.check": "Dit scherm niet meer tonen", "title.32bit.deprecation.realms.header": "32-bitssysteem gedetecteerd", "title.credits": "© Mojang AB. Alle rechten voorbehouden.", "title.multiplayer.disabled": "<PERSON><PERSON> is uitgeschakeld. Controleer je Microsoftaccountinstellingen.", "title.multiplayer.disabled.banned.name": "Je moet je naam veranderen voordat je online kan spelen", "title.multiplayer.disabled.banned.permanent": "Jouw account is permanent geschorst van online spelen", "title.multiplayer.disabled.banned.temporary": "Jouw account is tijdelijk geschorst van online spelen", "title.multiplayer.lan": "<PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON> (server van derden)", "title.multiplayer.realms": "<PERSON><PERSON> (Realms)", "title.singleplayer": "<PERSON>een spelen", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s weer %s en %1$s ten slotte %s en ook %1$s weer!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hallo %", "translation.test.invalid2": "hallo %s", "translation.test.none": "Hallo, wereld!", "translation.test.world": "wereld", "trim_material.minecraft.amethyst": "Amethistmateriaal", "trim_material.minecraft.copper": "Kopermateriaal", "trim_material.minecraft.diamond": "Diamantmateriaal", "trim_material.minecraft.emerald": "Smaragdmateriaal", "trim_material.minecraft.gold": "<PERSON>ud<PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "IJzermateriaal", "trim_material.minecraft.lapis": "Lapislazulimateriaal", "trim_material.minecraft.netherite": "Netherietmateriaal", "trim_material.minecraft.quartz": "Kwartsmateriaal", "trim_material.minecraft.redstone": "Redstonemateriaal", "trim_material.minecraft.resin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Harnasversiering 'bout'", "trim_pattern.minecraft.coast": "Harnasversiering 'kust'", "trim_pattern.minecraft.dune": "Harnasversiering 'duin'", "trim_pattern.minecraft.eye": "Harnasversiering 'oog'", "trim_pattern.minecraft.flow": "Harnasversiering 'windstroming'", "trim_pattern.minecraft.host": "Harnasversiering 'gastheer'", "trim_pattern.minecraft.raiser": "Harnasversiering 'fokker'", "trim_pattern.minecraft.rib": "Harnasversiering 'rib'", "trim_pattern.minecraft.sentry": "Harnasversiering 's<PERSON>wa<PERSON>'", "trim_pattern.minecraft.shaper": "Harnasversiering 'vormer'", "trim_pattern.minecraft.silence": "Harnasversiering 'stilte'", "trim_pattern.minecraft.snout": "Harnasversiering 'snoet'", "trim_pattern.minecraft.spire": "Harnasversiering 'piek'", "trim_pattern.minecraft.tide": "Harnasversiering 'getijde'", "trim_pattern.minecraft.vex": "Harnasversiering 'ergernis'", "trim_pattern.minecraft.ward": "Harnasversiering 'hoeder'", "trim_pattern.minecraft.wayfinder": "Harnasversiering 'padvinder'", "trim_pattern.minecraft.wild": "Harnasversiering 'verwilderd'", "tutorial.bundleInsert.description": "Rechtsklik om voorwerpen toe te voegen", "tutorial.bundleInsert.title": "Gebruik een bundel", "tutorial.craft_planks.description": "Het receptenboek kan hulp bieden", "tutorial.craft_planks.title": "Maak houten planken", "tutorial.find_tree.description": "<PERSON>om<PERSON> het om hout te verzamelen", "tutorial.find_tree.title": "Vind een boom", "tutorial.look.description": "Gebruik je muis om te dra<PERSON>en", "tutorial.look.title": "<PERSON>jk rond", "tutorial.move.description": "Spring met %s", "tutorial.move.title": "Loop met %s, %s, %s en %s", "tutorial.open_inventory.description": "Druk op %s", "tutorial.open_inventory.title": "Open je inventaris", "tutorial.punch_tree.description": "Houd %s ingedrukt", "tutorial.punch_tree.title": "Vernietig de boom", "tutorial.socialInteractions.description": "Druk op %s om het scherm te openen", "tutorial.socialInteractions.title": "Sociale interacties", "upgrade.minecraft.netherite_upgrade": "Netherietupgrade"}