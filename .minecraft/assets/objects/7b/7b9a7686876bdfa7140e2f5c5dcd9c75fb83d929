{"accessibility.onboarding.accessibility.button": "Ajustes de accesibilidad...", "accessibility.onboarding.screen.narrator": "Presiona Enter para activar el narrador", "accessibility.onboarding.screen.title": "¡Bienvenido a Minecraft!\n\n¿Te gustaría activar el narrador o ver los ajustes de accesiblidad?", "addServer.add": "Aceptar", "addServer.enterIp": "Dirección IP", "addServer.enterName": "Nombre del server", "addServer.resourcePack": "Recursos del server", "addServer.resourcePack.disabled": "Desactivado", "addServer.resourcePack.enabled": "Activado", "addServer.resourcePack.prompt": "Preguntar", "addServer.title": "Información del server", "advMode.command": "Comando de consola", "advMode.mode": "Modo", "advMode.mode.auto": "Repetición", "advMode.mode.autoexec.bat": "Siempre activo", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "<PERSON>mpul<PERSON>", "advMode.mode.redstoneTriggered": "Necesita redstone", "advMode.mode.sequence": "En cadena", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "Se necesita ser operador y estar en modo creativo", "advMode.notEnabled": "Los bloques de comandos no están habilitados en este server", "advMode.previousOutput": "Comando anterior", "advMode.setCommand": "Establece un comando de consola para el bloque", "advMode.setCommand.success": "Comando establecido: %s", "advMode.trackOutput": "Ver el resultado", "advMode.triggering": "Activando", "advMode.type": "Tipo", "advancement.advancementNotFound": "Progreso desconocido: %s", "advancements.adventure.adventuring_time.description": "Descubre todos los biomas", "advancements.adventure.adventuring_time.title": "Hora de aventura", "advancements.adventure.arbalistic.description": "Mata a cinco mobs únicos con un solo disparo de ballesta", "advancements.adventure.arbalistic.title": "Ballestería avanzada", "advancements.adventure.avoid_vibration.description": "Agáchate cerca de un sensor de sculk o de un warden para evitar que te detecten", "advancements.adventure.avoid_vibration.title": "Discreción 100", "advancements.adventure.blowback.description": "Mata un breeze devolviéndole una carga de viento", "advancements.adventure.blowback.title": "¡Sóplame este ojo!", "advancements.adventure.brush_armadillo.description": "Consigue escamas de armadillo cepillando a uno con un cepillo", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.bullseye.description": "Golpea el blanco de una diana desde al menos 30 metros de distancia", "advancements.adventure.bullseye.title": "¡En el blanco!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Arma una vasija decorada con 4 fragmentos de cerámica", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauración delicada", "advancements.adventure.crafters_crafting_crafters.description": "Presencia a una crafteadora craftear una crafteadora", "advancements.adventure.crafters_crafting_crafters.title": "La industrialización", "advancements.adventure.fall_from_world_height.description": "Cae desde el límite de altura del mundo hasta lo más profundo de este sin morir en el intento", "advancements.adventure.fall_from_world_height.title": "Cuevas y acantilados", "advancements.adventure.heart_transplanter.description": "Coloca un corazón de crepitante con la alineación correcta entre dos troncos de roble pálido", "advancements.adventure.heart_transplanter.title": "Trasplantador de corazón", "advancements.adventure.hero_of_the_village.description": "Defiende exitosamente una aldea de una invasión", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON> aldea", "advancements.adventure.honey_block_slide.description": "Deslízate en un bloque de miel para parar tu caída", "advancements.adventure.honey_block_slide.title": "Situación pegajosa", "advancements.adventure.kill_a_mob.description": "Mata a una criatura hostil", "advancements.adventure.kill_a_mob.title": "Cazador de monstruos", "advancements.adventure.kill_all_mobs.description": "Mata a un mob hostil de cada tipo", "advancements.adventure.kill_all_mobs.title": "Monst<PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Mata una criatura cerca de un catalizador de sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "¿¡Se esparce!?", "advancements.adventure.lighten_up.description": "Aumenta el brillo de una lámpara de cobre raspándola con un hacha", "advancements.adventure.lighten_up.title": "Lúcete", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protege a un aldeano de un choque eléctrico indeseado sin iniciar un incendio", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Cortacorrientes", "advancements.adventure.minecraft_trials_edition.description": "Pon un pie en un calabozo de desafío", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON>(s)", "advancements.adventure.ol_betsy.description": "Dispara una ballesta", "advancements.adventure.ol_betsy.title": "La vieja confiable", "advancements.adventure.overoverkill.description": "Saca 50 corazones de daño con un solo golpe usando la maza", "advancements.adventure.overoverkill.title": "Masacre", "advancements.adventure.play_jukebox_in_meadows.description": "Haz que los prados vibren con el dulce cantar de un tocadiscos", "advancements.adventure.play_jukebox_in_meadows.title": "El dulce cantar", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Lee la señal de Redstone de un estante de libros usando un comparador", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "El poder de la lectura", "advancements.adventure.revaulting.description": "Desbloquea una bóveda ominosa con una llave de desafío ominosa", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.root.description": "Aventura, exploración y combate", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Consigue un fragmento de cerámica cepillando un bloque sospechoso", "advancements.adventure.salvage_sherd.title": "El pasado está presente", "advancements.adventure.shoot_arrow.description": "Dispárale a algo con una flecha", "advancements.adventure.shoot_arrow.title": "¡Buena puntería!", "advancements.adventure.sleep_in_bed.description": "Duerme en una cama para cambiar tu punto de reaparición", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> sue<PERSON>", "advancements.adventure.sniper_duel.description": "Mata a un esqueleto desde 50 metros o más", "advancements.adventure.sniper_duel.title": "Francocazadores", "advancements.adventure.spyglass_at_dragon.description": "Mira al Enderdragón a través de un catalejo", "advancements.adventure.spyglass_at_dragon.title": "¡<PERSON>í está Jean!", "advancements.adventure.spyglass_at_ghast.description": "Mira a un ghast a través de un catalejo", "advancements.adventure.spyglass_at_ghast.title": "¡<PERSON>, un extraterrestre!", "advancements.adventure.spyglass_at_parrot.description": "Mira a un loro a través de un catalejo", "advancements.adventure.spyglass_at_parrot.title": "¡<PERSON>í está <PERSON>!", "advancements.adventure.summon_iron_golem.description": "Invoca un gólem de hierro para que ayude a defender una aldea", "advancements.adventure.summon_iron_golem.title": "El guardaespaldas", "advancements.adventure.throw_trident.description": "Tírale un tridente a algo.\nConsejo: Tirar tu única arma no es una buena idea.", "advancements.adventure.throw_trident.title": "Maravillosa jugada", "advancements.adventure.totem_of_undying.description": "Usa un tótem de la inmortalidad para engañar a la muerte", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Haz un intercambio con un aldeano", "advancements.adventure.trade.title": "¡Qué ofertón!", "advancements.adventure.trade_at_world_height.description": "Haz un intercambio con un aldeano en el límite de altura del mundo", "advancements.adventure.trade_at_world_height.title": "Ofertón estelar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Usa alguno de estos moldes de herrería al menos una vez: aguja, hocico, costillas, guardia, silencio, vex, marea, guía", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Maestro de la herrería", "advancements.adventure.trim_with_any_armor_pattern.description": "Decora una armadura en una mesa de herrería", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Mata a dos phantoms con una sola flecha perforante", "advancements.adventure.two_birds_one_arrow.title": "Dos pájaros de un tiro", "advancements.adventure.under_lock_and_key.description": "Abre una bóveda con una llave de desafío", "advancements.adventure.under_lock_and_key.title": "Bajo llave", "advancements.adventure.use_lodestone.description": "I<PERSON>ta una brújula con magnetita", "advancements.adventure.use_lodestone.title": "Ma<PERSON><PERSON><PERSON>, llévame a casa", "advancements.adventure.very_very_frightening.description": "Golpea a un aldeano con un rayo", "advancements.adventure.very_very_frightening.title": "Impactrueno", "advancements.adventure.voluntary_exile.description": "Mata al capitán de la invasión.\nSería mejor alejarte de las aldeas por un tiempo...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camina sobre nieve polvo... sin hundirte", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON> como conejo", "advancements.adventure.who_needs_rockets.description": "Impúlsate 8 bloques al cielo con una carga de viento", "advancements.adventure.who_needs_rockets.title": "Sé que puedo volar", "advancements.adventure.whos_the_pillager_now.description": "Dale al saqueador el sabor de su propia medicina", "advancements.adventure.whos_the_pillager_now.title": "Saqueador saqueado", "advancements.empty": "Parece que no hay nada por aquí...", "advancements.end.dragon_breath.description": "Recoge aliento de dragón en una botella", "advancements.end.dragon_breath.title": "¿Una mentita?", "advancements.end.dragon_egg.description": "Consigue el huevo del dragón", "advancements.end.dragon_egg.title": "La nueva generación", "advancements.end.elytra.description": "Encuentra unas elytra", "advancements.end.elytra.title": "Al infinito... ¡y más allá!", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON> de la isla principal", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON> remota", "advancements.end.find_end_city.description": "<PERSON><PERSON>, ¿qué es lo peor que podría pasar?", "advancements.end.find_end_city.title": "La ciudad al final del juego", "advancements.end.kill_dragon.description": "Buena suerte", "advancements.end.kill_dragon.title": "Libera al End", "advancements.end.levitate.description": "Levita a una altura de 50 bloques al ser atacado por un shulker", "advancements.end.levitate.title": "Buenas vistas desde aquí", "advancements.end.respawn_dragon.description": "Vuelve a invocar al Enderdragón", "advancements.end.respawn_dragon.title": "Se acabó... ¿Otra vez?", "advancements.end.root.description": "¿Se acabó? O... ¿sólo acaba de empezar?", "advancements.end.root.title": "El End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Haz que un allay suelte una torta en un bloque musical", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "¡Cumpleaños feliz!", "advancements.husbandry.allay_deliver_item_to_player.description": "Haz que un allay te entregue un ítem", "advancements.husbandry.allay_deliver_item_to_player.title": "Yo soy tu amigo fiel", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON>rapa un ajolote en un balde", "advancements.husbandry.axolotl_in_a_bucket.title": "Dios mío... La creatura", "advancements.husbandry.balanced_diet.description": "Come todo lo que sea comestible, aunque no sea saludable", "advancements.husbandry.balanced_diet.title": "Una dieta balanceada", "advancements.husbandry.breed_all_animals.description": "¡Cría a todos los animales!", "advancements.husbandry.breed_all_animals.title": "De par en par", "advancements.husbandry.breed_an_animal.description": "Haz que dos animales de la misma especie se reproduzcan", "advancements.husbandry.breed_an_animal.title": "¡Son pololos!", "advancements.husbandry.complete_catalogue.description": "¡Domestica todas las especies gatunas!", "advancements.husbandry.complete_catalogue.title": "Un gatálogo completo", "advancements.husbandry.feed_snifflet.description": "<PERSON> de comer a un sniffer bebé", "advancements.husbandry.feed_snifflet.title": "¡Olfatitos!", "advancements.husbandry.fishy_business.description": "Atrapa un pez", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "Ten todas las raniluces en tu inventario", "advancements.husbandry.froglights.title": "¡Con nuestros poderes combinados!", "advancements.husbandry.kill_axolotl_target.description": "Haz equipo con un ajolote y ayúdalo a ganar una pelea", "advancements.husbandry.kill_axolotl_target.title": "¡El curativo poder de la amistad!", "advancements.husbandry.leash_all_frog_variants.description": "Ponles correa a todas las variantes de rana", "advancements.husbandry.leash_all_frog_variants.title": "Llegaron los sapos", "advancements.husbandry.make_a_sign_glow.description": "Haz brillar el texto de cualquier tipo de cartel", "advancements.husbandry.make_a_sign_glow.title": "¡Brilla y observa!", "advancements.husbandry.netherite_hoe.description": "Usa un lingote de netherita para mejorar un azadón y luego reevalúa lo que haces con tu vida", "advancements.husbandry.netherite_hoe.title": "Malas costumbres", "advancements.husbandry.obtain_sniffer_egg.description": "Obtén un huevo de sniffer", "advancements.husbandry.obtain_sniffer_egg.title": "¿Qué es ese olor?", "advancements.husbandry.place_dried_ghast_in_water.description": "Coloca un bloque de ghast deshidratado en agua", "advancements.husbandry.place_dried_ghast_in_water.title": "¡Mantente hidratado!", "advancements.husbandry.plant_any_sniffer_seed.description": "Planta una semilla encontrada por un sniffer", "advancements.husbandry.plant_any_sniffer_seed.title": "Plantando el pasado", "advancements.husbandry.plant_seed.description": "Planta una semilla y mira como crece", "advancements.husbandry.plant_seed.title": "Crecen tan rápido...", "advancements.husbandry.remove_wolf_armor.description": "Quítale la armadura a un lobo con unas tijeras", "advancements.husbandry.remove_wolf_armor.title": "Acicalado", "advancements.husbandry.repair_wolf_armor.description": "Repara por completo una armadura para lobos dañada con escamas de armadillo", "advancements.husbandry.repair_wolf_armor.title": "¡Como nuevo!", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Súbete a un bote y flota junto a una cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "¿A dónde zarpamos cabrita?", "advancements.husbandry.root.description": "El mundo está lleno de amigos y comida", "advancements.husbandry.root.title": "Prosperidad", "advancements.husbandry.safely_harvest_honey.description": "Usa una fogata para recoger miel de una colmena con una botella sin hacer enojar a las abejas", "advancements.husbandry.safely_harvest_honey.title": "Abelante, estás en casa", "advancements.husbandry.silk_touch_nest.description": "Mueve una colmena o un apiario con 3 abejas dentro usando toque de seda", "advancements.husbandry.silk_touch_nest.title": "Abejé<PERSON>os de aquí", "advancements.husbandry.tactical_fishing.description": "Atrapa un pez... ¡Sin caña de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca táctica", "advancements.husbandry.tadpole_in_a_bucket.description": "Atrapa un renacuajo en un balde", "advancements.husbandry.tadpole_in_a_bucket.title": "¿Un baldecuajo?", "advancements.husbandry.tame_an_animal.description": "Domestica a un animal", "advancements.husbandry.tame_an_animal.title": "Mejores amigos por siempre", "advancements.husbandry.wax_off.description": "¡Limpia la cera de un bloque de cobre!", "advancements.husbandry.wax_off.title": "Pulir...", "advancements.husbandry.wax_on.description": "¡Aplica miel a un bloque de cobre!", "advancements.husbandry.wax_on.title": "Encerar...", "advancements.husbandry.whole_pack.description": "Domestica a todas las razas de lobo", "advancements.husbandry.whole_pack.title": "Tírame a los lobos", "advancements.nether.all_effects.description": "Obtén todos los efectos posibles al mismo tiempo", "advancements.nether.all_effects.title": "¿Cómo llegamos aquí?", "advancements.nether.all_potions.description": "Obtén todos los efectos de poción al mismo tiempo", "advancements.nether.all_potions.title": "¡La chupilca del diablo!", "advancements.nether.brew_potion.description": "Prepara una poción", "advancements.nether.brew_potion.title": "Destilería local", "advancements.nether.charge_respawn_anchor.description": "Carga un nexo de reaparición al máximo", "advancements.nether.charge_respawn_anchor.title": "No alcanzó para nueve vidas", "advancements.nether.create_beacon.description": "Fabrica y coloca un faro", "advancements.nether.create_beacon.title": "Hágase la luz", "advancements.nether.create_full_beacon.description": "Haz que un faro trabaje a máxima potencia", "advancements.nether.create_full_beacon.title": "Faroneitor", "advancements.nether.distract_piglin.description": "Distrae a un piglin con algo de oro", "advancements.nether.distract_piglin.title": "Mi precioso...", "advancements.nether.explore_nether.description": "Explora todos los biomas del Nether", "advancements.nether.explore_nether.title": "Turismo infernal", "advancements.nether.fast_travel.description": "Usa el Nether para recorrer 7 km en la superficie", "advancements.nether.fast_travel.title": "Burbuja subespecial", "advancements.nether.find_bastion.description": "Entra a las ruinas de un bastión", "advancements.nether.find_bastion.title": "Qué tiempos aquellos", "advancements.nether.find_fortress.description": "Adéntrate en una fortaleza del Nether", "advancements.nether.find_fortress.title": "Una fortaleza terrible", "advancements.nether.get_wither_skull.description": "Consigue una calavera de es<PERSON><PERSON>", "advancements.nether.get_wither_skull.title": "Debiste apuntar a la cabeza", "advancements.nether.loot_bastion.description": "Saquea un cofre en las ruinas de un bastión", "advancements.nether.loot_bastion.title": "¡Guerra de chanchos!", "advancements.nether.netherite_armor.description": "Obten una armadura completa de netherita", "advancements.nether.netherite_armor.title": "Cúbreme de escombros", "advancements.nether.obtain_ancient_debris.description": "Consigue escombros ancestrales", "advancements.nether.obtain_ancient_debris.title": "Oculto en las profundidades", "advancements.nether.obtain_blaze_rod.description": "Consigue una barra de blaze", "advancements.nether.obtain_blaze_rod.title": "¡En llamas!", "advancements.nether.obtain_crying_obsidian.description": "Consigue obsidiana llorona", "advancements.nether.obtain_crying_obsidian.title": "¿Quién está cortando cebollas?", "advancements.nether.return_to_sender.description": "Mata un ghast devolviéndole una bola de fuego", "advancements.nether.return_to_sender.title": "Devolver al remitente", "advancements.nether.ride_strider.description": "Monta a un strider con una caña con hongo distorsionado", "advancements.nether.ride_strider.title": "¡Un bote con patas!", "advancements.nether.ride_strider_in_overworld_lava.description": "L<PERSON>a a un strider a dar un laaaargo paseo en un lago de lava de el mundo superficial", "advancements.nether.ride_strider_in_overworld_lava.title": "Se siente como en casa", "advancements.nether.root.description": "Tráete ropa de verano", "advancements.nether.root.title": "<PERSON>", "advancements.nether.summon_wither.description": "Invoca al Wither", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Rescata a un Ghast del Nether, llévalo sano y salvo a la superficie... y... acaba con él", "advancements.nether.uneasy_alliance.title": "Falsa al<PERSON>za", "advancements.nether.use_lodestone.description": "I<PERSON>ta una brújula con un bloque de magnetita", "advancements.nether.use_lodestone.title": "Magne<PERSON>ta, llévame a casita", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilita y cura a un aldeano zombi", "advancements.story.cure_zombie_villager.title": "Zombiólogo", "advancements.story.deflect_arrow.description": "Desvía un proyectil con el escudo", "advancements.story.deflect_arrow.title": "¡<PERSON><PERSON>ta no, joven!", "advancements.story.enchant_item.description": "Encanta un objeto en una mesa de encantamientos", "advancements.story.enchant_item.title": "¡Qué encantador!", "advancements.story.enter_the_end.description": "Localiza, activa y atraviesa un portal al End", "advancements.story.enter_the_end.title": "¿Se acabó?", "advancements.story.enter_the_nether.description": "Construye, activa y atraviesa un portal al Nether", "advancements.story.enter_the_nether.title": "Tenemos que ir más profundo", "advancements.story.follow_ender_eye.description": "Sigue un ojo de ender", "advancements.story.follow_ender_eye.title": "Ojo espía", "advancements.story.form_obsidian.description": "Consigue un bloque de obsidiana", "advancements.story.form_obsidian.title": "Mente fría", "advancements.story.iron_tools.description": "Mejora tu picota", "advancements.story.iron_tools.title": "¿No es hierrónico?", "advancements.story.lava_bucket.description": "<PERSON><PERSON> un balde con lava", "advancements.story.lava_bucket.title": "¡La cosa está que arde!", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON> di<PERSON>", "advancements.story.mine_diamond.title": "¡Diamantes!", "advancements.story.mine_stone.description": "Mina un poco de piedra con tu nueva picota", "advancements.story.mine_stone.title": "La Edad de Piedra", "advancements.story.obtain_armor.description": "Protégete con una pieza de armadura de hierro", "advancements.story.obtain_armor.title": "Vístete", "advancements.story.root.description": "El corazón y la historia del juego", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "La armadura de diamante salva vidas", "advancements.story.shiny_gear.title": "Cúbreme de diamantes", "advancements.story.smelt_iron.description": "Funde un lingote de hierro", "advancements.story.smelt_iron.title": "La Edad de Hierro", "advancements.story.upgrade_tools.description": "Hazte una mejor picota", "advancements.story.upgrade_tools.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.toast.challenge": "¡Desafío completado!", "advancements.toast.goal": "¡Objetivo alcanzado!", "advancements.toast.task": "¡Progreso realizado!", "argument.anchor.invalid": "La posición de anclaje de la entidad no es válida: %s", "argument.angle.incomplete": "Incompleto (se requiere 1 ángulo)", "argument.angle.invalid": "Ángulo no válido", "argument.block.id.invalid": "Tipo de bloque desconocido: %s", "argument.block.property.duplicate": "La propiedad \"%s\" del bloque %s solo puede establecerse una vez", "argument.block.property.invalid": "El bloque %s no acepta \"%s\" para la propiedad \"%s\"", "argument.block.property.novalue": "Se requiere un valor para la propiedad \"%s\" del bloque %s", "argument.block.property.unclosed": "Se requiere \"]\" para cerrar las propiedades del bloque", "argument.block.property.unknown": "El bloque %s no posee la propiedad \"%s\"", "argument.block.tag.disallowed": "Las etiquetas no están permitidas aquí, solo los bloques actuales", "argument.color.invalid": "Color desconocido: %s", "argument.component.invalid": "Componente de chat no válido: %s", "argument.criteria.invalid": "Criterio desconocido '%s'", "argument.dimension.invalid": "Dimensión “%s” desconocida", "argument.double.big": "El valor doble no puede ser mayor que %s (encontrado: %s)", "argument.double.low": "El valor doble no puede ser menor que %s (encontrado: %s)", "argument.entity.invalid": "Nombre o UUID no válidos", "argument.entity.notfound.entity": "No se encontraron entidades", "argument.entity.notfound.player": "No se encontraron jugadores", "argument.entity.options.advancements.description": "Jugadores con progresos", "argument.entity.options.distance.description": "Distancia de entidad", "argument.entity.options.distance.negative": "La distancia no puede ser negativa", "argument.entity.options.dx.description": "Entidades entre X y X + dX", "argument.entity.options.dy.description": "Entidades entre Y e Y + dY", "argument.entity.options.dz.description": "Entidades entre Z y Z + dZ", "argument.entity.options.gamemode.description": "Jugadores con modo de juego", "argument.entity.options.inapplicable": "La opción \"%s\" no puede aplicarse aquí", "argument.entity.options.level.description": "Nivel de experiencia", "argument.entity.options.level.negative": "El nivel no puede ser negativo", "argument.entity.options.limit.description": "Número máximo de entidades a devolver", "argument.entity.options.limit.toosmall": "El límite debe ser de al menos 1", "argument.entity.options.mode.invalid": "Modo de juego no válido o desconocido: %s", "argument.entity.options.name.description": "Nombre de entidad", "argument.entity.options.nbt.description": "Entidades con NBT", "argument.entity.options.predicate.description": "Predicado personalizado", "argument.entity.options.scores.description": "Entidades con una puntuación", "argument.entity.options.sort.description": "Clasificar entidades", "argument.entity.options.sort.irreversible": "El tipo de clasificación \"%s\" es desconocido o no es válido", "argument.entity.options.tag.description": "Entidades con tag", "argument.entity.options.team.description": "Entidades en un equipo", "argument.entity.options.type.description": "Entidades de un tipo", "argument.entity.options.type.invalid": "Tipo de entidad desconocido o no válido: %s", "argument.entity.options.unknown": "Opción desconocida: %s", "argument.entity.options.unterminated": "Se requiere \"]\" para cerrar las opciones", "argument.entity.options.valueless": "Se requiere un valor para la opción \"%s\"", "argument.entity.options.x.description": "posición x", "argument.entity.options.x_rotation.description": "Rotación X de la entidad", "argument.entity.options.y.description": "posición y", "argument.entity.options.y_rotation.description": "Rotación Y de la entidad", "argument.entity.options.z.description": "posición z", "argument.entity.selector.allEntities": "Todas las entidades", "argument.entity.selector.allPlayers": "Todos los jugadores", "argument.entity.selector.missing": "Falta el tipo de selector", "argument.entity.selector.nearestEntity": "Entidad más cercana", "argument.entity.selector.nearestPlayer": "Jugador más cercano", "argument.entity.selector.not_allowed": "Selector no permitido", "argument.entity.selector.randomPlayer": "Jugador al azar", "argument.entity.selector.self": "Entidad actual", "argument.entity.selector.unknown": "Tipo de selector desconocido: %s", "argument.entity.toomany": "Solo se permite una entidad, pero el selector utilizado permite más de una", "argument.enum.invalid": "Valor \"%s\" invalido", "argument.float.big": "El valor float no puede ser mayor que %s (encontrado: %s)", "argument.float.low": "El valor float no puede ser menor que %s (encontrado: %s)", "argument.gamemode.invalid": "Modo de juego desconocido: %s", "argument.hexcolor.invalid": "Código hex de color no válido: '%s'", "argument.id.invalid": "ID no válida", "argument.id.unknown": "ID desconocida: %s", "argument.integer.big": "El número entero no puede ser mayor que %s (encontrado: %s)", "argument.integer.low": "El número entero no puede ser menor que %s (encontrado: %s)", "argument.item.id.invalid": "Objeto desconocido: %s", "argument.item.tag.disallowed": "Aquí no se permite el uso de etiquetas, solo objetos actuales", "argument.literal.incorrect": "Se requiere el valor literal \"%s\"", "argument.long.big": "Variable long no debe ser más que %s, se encontró %s", "argument.long.low": "El largo no puede ser menor que %s, encontrado: %s", "argument.message.too_long": "El mensaje es demasiado largo (%s > máximo %s caracteres)", "argument.nbt.array.invalid": "Tipo de formación no válido: %s", "argument.nbt.array.mixed": "No se pudo insertar %s en %s", "argument.nbt.expected.compound": "Etiqueta compuesta prevista", "argument.nbt.expected.key": "Se requiere clave", "argument.nbt.expected.value": "Se requiere valor", "argument.nbt.list.mixed": "No se pudo insertar %s en la lista de %s", "argument.nbt.trailing": "Hay datos de más", "argument.player.entities": "Este comando solo afecta a jugadores, pero el selector utilizado incluye entidades", "argument.player.toomany": "Solo se permite un jugador, pero el selector utilizado permite más de uno", "argument.player.unknown": "Ese jugador no existe", "argument.pos.missing.double": "Se requiere una coordenada", "argument.pos.missing.int": "Se requiere la posición de un bloque", "argument.pos.mixed": "No se puede usar una mezcla de coordenadas global y local (o se utiliza ^ con todas o con ninguna)", "argument.pos.outofbounds": "Esta posición está afuera de los límites permitidos.", "argument.pos.outofworld": "¡Esa posición está fuera de este mundo!", "argument.pos.unloaded": "Esa posición no está cargada", "argument.pos2d.incomplete": "Incompleto (se esperaban 2 coordenadas)", "argument.pos3d.incomplete": "Incompleto (se requieren 3 coordenadas)", "argument.range.empty": "Se requiere un valor o un intervalo de valores", "argument.range.ints": "Solo se permiten números enteros, no decimales", "argument.range.swapped": "El mínimo no puede ser mayor que el máximo", "argument.resource.invalid_type": "El elemento '%s' es del tipo incorrecto '%s' (se esperaba '%s')", "argument.resource.not_found": "No se pudo encontrar el elemento '%s' de tipo '%s'", "argument.resource_or_id.failed_to_parse": "No se pudo analizar la estructura: %s", "argument.resource_or_id.invalid": "ID o etiqueta inválida", "argument.resource_or_id.no_such_element": "No se encontró el elemento '%s' en el registro '%s'", "argument.resource_selector.not_found": "Sin coincidencias para el selector '%s' del tipo '%s'", "argument.resource_tag.invalid_type": "La etiqueta '%s' es del tipo incorrecto '%s' (se esperaba '%s')", "argument.resource_tag.not_found": "No se pudo encontrar la etiqueta '%s' de tipo '%s'", "argument.rotation.incomplete": "Incompleto (se requieren 2 coordenadas)", "argument.scoreHolder.empty": "No se encontraron marcadores de puntuación relevantes", "argument.scoreboardDisplaySlot.invalid": "Espacio de muestra desconocido: %s", "argument.style.invalid": "Estilo in<PERSON>: %s", "argument.time.invalid_tick_count": "El número de ticks no puede ser negativo", "argument.time.invalid_unit": "Unidad inválida", "argument.time.tick_count_too_low": "El número de ticks no puede ser menor a %s, se encontraron %s", "argument.uuid.invalid": "UUID no válida", "argument.waypoint.invalid": "La entidad seleccionada no es un punto de ruta", "arguments.block.tag.unknown": "Etiqueta de bloque desconocida: %s", "arguments.function.tag.unknown": "Etiqueta de función desconocida: %s", "arguments.function.unknown": "Función desconocida: %s", "arguments.item.component.expected": "Componente de ítem esperado", "arguments.item.component.malformed": "Componente '%s' mal formado: '%s'", "arguments.item.component.repeated": "El componente de objeto '%s' se repitió, pero solo puede especificarse un único valor", "arguments.item.component.unknown": "Componente de ítem desconocido '%s'", "arguments.item.malformed": "Ítem mal formado: '%s'", "arguments.item.overstacked": "%s solo puede apilarse hasta %s", "arguments.item.predicate.malformed": "Predicado '%s' mal formado: '%s'", "arguments.item.predicate.unknown": "Predicado de ítem desconocido: %s", "arguments.item.tag.unknown": "Etiqueta de objeto desconocida: %s", "arguments.nbtpath.node.invalid": "Ruta NBT inválida", "arguments.nbtpath.nothing_found": "No se encontraron elementos que coincidieran con %s", "arguments.nbtpath.too_deep": "El NBT resultante tiene una red demasiado grande", "arguments.nbtpath.too_large": "El NBT resultante es demasiado grande", "arguments.objective.notFound": "Objetivo de la tabla de puntuación desconocido: %s", "arguments.objective.readonly": "El objetivo \"%s\" de la tabla de puntuación es solo de lectura", "arguments.operation.div0": "No se puede dividir entre cero", "arguments.operation.invalid": "Operación no válida", "arguments.swizzle.invalid": "Ejes no válidos: se requiere la combinación de \"X\", \"Y\" y \"Z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%2$s: %1$s%%", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "%2$s: +%1$s", "attribute.modifier.plus.1": "%2$s: +%1$s%%", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "%2$s: -%1$s", "attribute.modifier.take.1": "%2$s: -%1$s%%", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Dureza de armadura", "attribute.name.attack_damage": "<PERSON><PERSON> por ataque", "attribute.name.attack_knockback": "Empuje por ataque", "attribute.name.attack_speed": "Velocidad de ataque", "attribute.name.block_break_speed": "Velocidad al romper bloques", "attribute.name.block_interaction_range": "Rango de interacción con bloques", "attribute.name.burning_time": "Tiempo de quemado", "attribute.name.camera_distance": "Distancia de la cámara", "attribute.name.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.explosion_knockback_resistance": "Resistencia al empuje de explosiones", "attribute.name.fall_damage_multiplier": "Multiplicador de daño por caída", "attribute.name.flying_speed": "Velocidad de vuelo", "attribute.name.follow_range": "<PERSON><PERSON> de seguimiento de mobs", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Dureza de armadura", "attribute.name.generic.attack_damage": "<PERSON><PERSON> por golpe", "attribute.name.generic.attack_knockback": "Empuje de ataque", "attribute.name.generic.attack_speed": "Velocidad de ataque", "attribute.name.generic.block_interaction_range": "Rango de interacción con bloques", "attribute.name.generic.burning_time": "Tiempo de quemado", "attribute.name.generic.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.generic.explosion_knockback_resistance": "Resistencia al empuje por explosiones", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de daño por caída", "attribute.name.generic.flying_speed": "Velocidad de vuelo", "attribute.name.generic.follow_range": "<PERSON><PERSON>", "attribute.name.generic.gravity": "Gravedad", "attribute.name.generic.jump_strength": "Potencia de salto", "attribute.name.generic.knockback_resistance": "Resistencia al empuje", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Absorción máxima", "attribute.name.generic.max_health": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.generic.movement_efficiency": "Eficiencia de movimiento", "attribute.name.generic.movement_speed": "Velocidad", "attribute.name.generic.oxygen_bonus": "Oxígeno extra", "attribute.name.generic.safe_fall_distance": "Distancia de caída segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Altura de pasos", "attribute.name.generic.water_movement_efficiency": "Eficiencia de movimiento acuático", "attribute.name.gravity": "Gravedad", "attribute.name.horse.jump_strength": "Fuerza de salto", "attribute.name.jump_strength": "Potencia de salto", "attribute.name.knockback_resistance": "Resistencia al empuje", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Absorción máxima", "attribute.name.max_health": "Vida máxima", "attribute.name.mining_efficiency": "Eficiencia al romper bloques", "attribute.name.movement_efficiency": "Eficiencia de movimiento", "attribute.name.movement_speed": "Velocidad", "attribute.name.oxygen_bonus": "Bonus de oxígeno", "attribute.name.player.block_break_speed": "Velocidad de ruptura de bloques", "attribute.name.player.block_interaction_range": "Rango de interacción con bloques", "attribute.name.player.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.player.mining_efficiency": "Eficiencia al minar", "attribute.name.player.sneaking_speed": "Velocidad al agacharse", "attribute.name.player.submerged_mining_speed": "Eficiencia al minar bajo el agua", "attribute.name.player.sweeping_damage_ratio": "Proporción del daño de barrido", "attribute.name.safe_fall_distance": "Distancia de caída segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocidad al agacharse", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "attribute.name.step_height": "Altura de pisadas", "attribute.name.submerged_mining_speed": "Eficiencia al romper bloques sumergido", "attribute.name.sweeping_damage_ratio": "Proporción del daño de barrido", "attribute.name.tempt_range": "Rango de atracción de mobs", "attribute.name.water_movement_efficiency": "Eficiencia de movimiento acuático", "attribute.name.waypoint_receive_range": "Rango de Recepción del Punto de Ruta", "attribute.name.waypoint_transmit_range": "Rango de Transmisión del Punto de Ruta", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "biome.minecraft.badlands": "Tierras baldías", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Deltas de basalto", "biome.minecraft.beach": "Playa", "biome.minecraft.birch_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.cherry_grove": "Bosque de cerezos", "biome.minecraft.cold_ocean": "Océano frío", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "Bosque oscuro", "biome.minecraft.deep_cold_ocean": "Océano frío profundo", "biome.minecraft.deep_dark": "Oscuridad profunda", "biome.minecraft.deep_frozen_ocean": "Océano congelado profundo", "biome.minecraft.deep_lukewarm_ocean": "Océano tibio profundo", "biome.minecraft.deep_ocean": "Océano profundo", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON>", "biome.minecraft.end_barrens": "El End - Zona árida", "biome.minecraft.end_highlands": "El End - Zona alta", "biome.minecraft.end_midlands": "El End - Zona media", "biome.minecraft.eroded_badlands": "Tierras baldías <PERSON>s", "biome.minecraft.flower_forest": "Bosque florido", "biome.minecraft.forest": "Bosque", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON> congelado", "biome.minecraft.frozen_peaks": "Cumbres congeladas", "biome.minecraft.frozen_river": "Río congelado", "biome.minecraft.grove": "Arboleda", "biome.minecraft.ice_spikes": "Espinas de hielo", "biome.minecraft.jagged_peaks": "Cumbres escarpadas", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Océano tibio", "biome.minecraft.lush_caves": "<PERSON><PERSON>s fron<PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "Campo", "biome.minecraft.mushroom_fields": "Campo de callampas", "biome.minecraft.nether_wastes": "Desiertos del Nether", "biome.minecraft.ocean": "<PERSON><PERSON>ano", "biome.minecraft.old_growth_birch_forest": "Abedular ancestral", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON> de pinos ancestral", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON> de abetos ancestral", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "Planicie", "biome.minecraft.river": "Río", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Meseta de sabana", "biome.minecraft.small_end_islands": "El End - <PERSON><PERSON> peque<PERSON>", "biome.minecraft.snowy_beach": "Playa nevada", "biome.minecraft.snowy_plains": "Planicie nevada", "biome.minecraft.snowy_slopes": "Cuestas nevadas", "biome.minecraft.snowy_taiga": "Taiga nevada", "biome.minecraft.soul_sand_valley": "Valle de almas", "biome.minecraft.sparse_jungle": "Jungla dispersa", "biome.minecraft.stony_peaks": "Cumbres rocosas", "biome.minecraft.stony_shore": "Costa rocosa", "biome.minecraft.sunflower_plains": "Planicie de girasoles", "biome.minecraft.swamp": "Pantano", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "El End", "biome.minecraft.the_void": "El vacío", "biome.minecraft.warm_ocean": "Océano <PERSON>", "biome.minecraft.warped_forest": "Bosque distorsionado", "biome.minecraft.windswept_forest": "<PERSON><PERSON> ventoso", "biome.minecraft.windswept_gravelly_hills": "Cerros de grava ventosos", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON> ventoso<PERSON>", "biome.minecraft.windswept_savanna": "Sabana ventosa", "biome.minecraft.wooded_badlands": "Baldías frondosas", "block.minecraft.acacia_button": "Botón de acacia", "block.minecraft.acacia_door": "Puerta de acacia", "block.minecraft.acacia_fence": "Valla de acacia", "block.minecraft.acacia_fence_gate": "Puerta de valla de acacia", "block.minecraft.acacia_hanging_sign": "Cartel colgante de acacia", "block.minecraft.acacia_leaves": "Hojas de acacia", "block.minecraft.acacia_log": "Tronco de acacia", "block.minecraft.acacia_planks": "Tablas de acacia", "block.minecraft.acacia_pressure_plate": "Placa de presión de acacia", "block.minecraft.acacia_sapling": "Brote de acacia", "block.minecraft.acacia_sign": "Cartel de acacia", "block.minecraft.acacia_slab": "Losa de acacia", "block.minecraft.acacia_stairs": "Escaleras de acacia", "block.minecraft.acacia_trapdoor": "T<PERSON><PERSON>lla de acacia", "block.minecraft.acacia_wall_hanging_sign": "Cartel colgante de acacia de pared", "block.minecraft.acacia_wall_sign": "Cartel de acacia en pared", "block.minecraft.acacia_wood": "Madera de acacia", "block.minecraft.activator_rail": "Riel activador", "block.minecraft.air": "Aire", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Bloque de amatista", "block.minecraft.amethyst_cluster": "<PERSON>ci<PERSON> de amatista", "block.minecraft.ancient_debris": "Escombros ancestrales", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Losa de andesita", "block.minecraft.andesite_stairs": "Escaleras de andesita", "block.minecraft.andesite_wall": "<PERSON><PERSON> de and<PERSON>", "block.minecraft.anvil": "Yun<PERSON>", "block.minecraft.attached_melon_stem": "Tallo de sandía unido", "block.minecraft.attached_pumpkin_stem": "Tallo de zapallo unido", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Hojas de azalea", "block.minecraft.azure_bluet": "Houstonia", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "Bloque de bam<PERSON>ú", "block.minecraft.bamboo_button": "Botón de bam<PERSON>ú", "block.minecraft.bamboo_door": "<PERSON><PERSON><PERSON> de b<PERSON>ú", "block.minecraft.bamboo_fence": "Valla de <PERSON>ú", "block.minecraft.bamboo_fence_gate": "Puerta de valla de bambú", "block.minecraft.bamboo_hanging_sign": "Cartel colgante de bambú", "block.minecraft.bamboo_mosaic": "Mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_slab": "Losa de mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_stairs": "Escaleras de mosaico de bambú", "block.minecraft.bamboo_planks": "Tablas de bam<PERSON>ú", "block.minecraft.bamboo_pressure_plate": "Placa de presión de bambú", "block.minecraft.bamboo_sapling": "<PERSON><PERSON>", "block.minecraft.bamboo_sign": "Cartel de bambú", "block.minecraft.bamboo_slab": "Losa de bam<PERSON>ú", "block.minecraft.bamboo_stairs": "Escaleras de bambú", "block.minecraft.bamboo_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de bam<PERSON>ú", "block.minecraft.bamboo_wall_hanging_sign": "Cartel colgante de bambú de pared", "block.minecraft.bamboo_wall_sign": "Cartel de bambú en pared", "block.minecraft.banner.base.black": "Tela completamente negra", "block.minecraft.banner.base.blue": "Tela completamente azul", "block.minecraft.banner.base.brown": "Tela completamente café", "block.minecraft.banner.base.cyan": "Tela completamente cian", "block.minecraft.banner.base.gray": "Tela completamente gris", "block.minecraft.banner.base.green": "Tela completamente verde", "block.minecraft.banner.base.light_blue": "Tela completamente celeste", "block.minecraft.banner.base.light_gray": "Tela completamente gris claro", "block.minecraft.banner.base.lime": "Tela completamente verde lima", "block.minecraft.banner.base.magenta": "Tela completamente magenta", "block.minecraft.banner.base.orange": "Tela completamente naranja", "block.minecraft.banner.base.pink": "Tela completamente rosada", "block.minecraft.banner.base.purple": "Tela completamente morada", "block.minecraft.banner.base.red": "Tela completamente roja", "block.minecraft.banner.base.white": "Tela completamente blanca", "block.minecraft.banner.base.yellow": "Tela completamente amarilla", "block.minecraft.banner.border.black": "Bordura negra", "block.minecraft.banner.border.blue": "Bordura azul", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.border.green": "Bordura verde", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.border.lime": "Bordura verde lima", "block.minecraft.banner.border.magenta": "Bordura magenta", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON> naranja", "block.minecraft.banner.border.pink": "Bordura rosada", "block.minecraft.banner.border.purple": "Bordura morada", "block.minecraft.banner.border.red": "Bord<PERSON> roja", "block.minecraft.banner.border.white": "<PERSON>rd<PERSON> blanca", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON> amarilla", "block.minecraft.banner.bricks.black": "Mazonado negro", "block.minecraft.banner.bricks.blue": "Mazonado azul", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.bricks.cyan": "Ma<PERSON><PERSON> cian", "block.minecraft.banner.bricks.gray": "Mazonado gris", "block.minecraft.banner.bricks.green": "Mazonado verde", "block.minecraft.banner.bricks.light_blue": "Mazonado celeste", "block.minecraft.banner.bricks.light_gray": "Mazonado gris claro", "block.minecraft.banner.bricks.lime": "Mazonado verde lima", "block.minecraft.banner.bricks.magenta": "Mazonado magenta", "block.minecraft.banner.bricks.orange": "Ma<PERSON>ado naranja", "block.minecraft.banner.bricks.pink": "Ma<PERSON>ado rosado", "block.minecraft.banner.bricks.purple": "Mazonado morado", "block.minecraft.banner.bricks.red": "Mazonado rojo", "block.minecraft.banner.bricks.white": "Mazonado blanco", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON> negro", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.circle.green": "<PERSON><PERSON> verde", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.circle.lime": "<PERSON>l verde lima", "block.minecraft.banner.circle.magenta": "Roel magenta", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON> rosado", "block.minecraft.banner.circle.purple": "<PERSON><PERSON> morado", "block.minecraft.banner.circle.red": "<PERSON><PERSON> rojo", "block.minecraft.banner.circle.white": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Estampado negro de creeper", "block.minecraft.banner.creeper.blue": "Estampado azul de creeper", "block.minecraft.banner.creeper.brown": "Estampado marr<PERSON> de creeper", "block.minecraft.banner.creeper.cyan": "Estampado cian <PERSON> creeper", "block.minecraft.banner.creeper.gray": "Estampado gris de creeper", "block.minecraft.banner.creeper.green": "Estampado verde de creeper", "block.minecraft.banner.creeper.light_blue": "Estampado celeste de creeper", "block.minecraft.banner.creeper.light_gray": "Estampado gris claro de creeper", "block.minecraft.banner.creeper.lime": "Estampado verde lima de creeper", "block.minecraft.banner.creeper.magenta": "Estampado magenta de creeper", "block.minecraft.banner.creeper.orange": "Estampado naranja de creeper", "block.minecraft.banner.creeper.pink": "Estampado rosado de creeper", "block.minecraft.banner.creeper.purple": "Estampado morado de creeper", "block.minecraft.banner.creeper.red": "Estampado rojo de creeper", "block.minecraft.banner.creeper.white": "Estampado blanco de creeper", "block.minecraft.banner.creeper.yellow": "Estamp<PERSON> amarillo de creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON> negra", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> azul", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.cross.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.cross.green": "<PERSON>pa verde", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON> celeste", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.cross.lime": "Aspa verde lima", "block.minecraft.banner.cross.magenta": "Aspa magenta", "block.minecraft.banner.cross.orange": "<PERSON><PERSON> naranja", "block.minecraft.banner.cross.pink": "Aspa rosada", "block.minecraft.banner.cross.purple": "Aspa morada", "block.minecraft.banner.cross.red": "<PERSON><PERSON> roja", "block.minecraft.banner.cross.white": "As<PERSON> blanca", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> amarilla", "block.minecraft.banner.curly_border.black": "Bordura negra dentada", "block.minecraft.banner.curly_border.blue": "Bordura azul dentada", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> marr<PERSON> dentada", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> cian dentada", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> gris dentada", "block.minecraft.banner.curly_border.green": "<PERSON>rd<PERSON> verde dentada", "block.minecraft.banner.curly_border.light_blue": "Bordura celeste dentada", "block.minecraft.banner.curly_border.light_gray": "<PERSON>rdura gris claro dentada", "block.minecraft.banner.curly_border.lime": "Bordura verde lima dentada", "block.minecraft.banner.curly_border.magenta": "Bordura magenta dentada", "block.minecraft.banner.curly_border.orange": "<PERSON>rd<PERSON> naranja dentada", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> rosada dentada", "block.minecraft.banner.curly_border.purple": "<PERSON>rd<PERSON> morada dentada", "block.minecraft.banner.curly_border.red": "<PERSON>rd<PERSON> roja dentada", "block.minecraft.banner.curly_border.white": "Bordura blanca dentada", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> amarilla dentada", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON> negro", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON>do verde lima", "block.minecraft.banner.diagonal_left.magenta": "Tajado magenta", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON> rosado", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON> morado", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "Tronchado negro", "block.minecraft.banner.diagonal_right.blue": "Tron<PERSON>do <PERSON>l", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON><PERSON>ian", "block.minecraft.banner.diagonal_right.gray": "Tronchado gris", "block.minecraft.banner.diagonal_right.green": "Tronchado verde", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.diagonal_right.light_gray": "Tronchado gris claro", "block.minecraft.banner.diagonal_right.lime": "Tronchado verde lima", "block.minecraft.banner.diagonal_right.magenta": "Tronchado magenta", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON> rosado", "block.minecraft.banner.diagonal_right.purple": "Tron<PERSON><PERSON> morado", "block.minecraft.banner.diagonal_right.red": "Tron<PERSON>do rojo", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Tronchado negro invertido", "block.minecraft.banner.diagonal_up_left.blue": "Tronchado azul invertido", "block.minecraft.banner.diagonal_up_left.brown": "Tronchado marrón invertido", "block.minecraft.banner.diagonal_up_left.cyan": "Tronchado cian invertido", "block.minecraft.banner.diagonal_up_left.gray": "Tronchado gris invertido", "block.minecraft.banner.diagonal_up_left.green": "Tronchado verde invertido", "block.minecraft.banner.diagonal_up_left.light_blue": "Tronchado celeste invertido", "block.minecraft.banner.diagonal_up_left.light_gray": "Tronchado gris claro invertido", "block.minecraft.banner.diagonal_up_left.lime": "Tronchado verde lima invertido", "block.minecraft.banner.diagonal_up_left.magenta": "Tronchado magenta invertido", "block.minecraft.banner.diagonal_up_left.orange": "Tronchado naranja invertido", "block.minecraft.banner.diagonal_up_left.pink": "Tronchado rosado invertido", "block.minecraft.banner.diagonal_up_left.purple": "Tronchado morado invertido", "block.minecraft.banner.diagonal_up_left.red": "Tronchado rojo invertido", "block.minecraft.banner.diagonal_up_left.white": "Tronchado blanco invertido", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON><PERSON> amarillo invertido", "block.minecraft.banner.diagonal_up_right.black": "Tajado negro invertido", "block.minecraft.banner.diagonal_up_right.blue": "Tajado a<PERSON>l invertido", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> ma<PERSON> invertido", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON> cian invertido", "block.minecraft.banner.diagonal_up_right.gray": "Tajado gris invertido", "block.minecraft.banner.diagonal_up_right.green": "Tajado verde invertido", "block.minecraft.banner.diagonal_up_right.light_blue": "Tajado celeste invertido", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON>do gris claro invertido", "block.minecraft.banner.diagonal_up_right.lime": "Tajado verde lima invertido", "block.minecraft.banner.diagonal_up_right.magenta": "Tajado magenta invertido", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON><PERSON> na<PERSON>ja invertido", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON>do rosado invertido", "block.minecraft.banner.diagonal_up_right.purple": "Ta<PERSON>do morado invertido", "block.minecraft.banner.diagonal_up_right.red": "Tajado rojo invertido", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> blanco invertido", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> am<PERSON>llo invertido", "block.minecraft.banner.flow.black": "Flujo negro", "block.minecraft.banner.flow.blue": "<PERSON><PERSON>jo a<PERSON>l", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.flow.green": "<PERSON><PERSON>jo verde", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.flow.lime": "Flujo verde lima", "block.minecraft.banner.flow.magenta": "Flujo magenta", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON> rosado", "block.minecraft.banner.flow.purple": "<PERSON><PERSON><PERSON> morado", "block.minecraft.banner.flow.red": "<PERSON><PERSON>jo rojo", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.black": "Estampado negro de flor", "block.minecraft.banner.flower.blue": "Estampado azul de flor", "block.minecraft.banner.flower.brown": "Estampado marrón de flor", "block.minecraft.banner.flower.cyan": "Estampado cian de flor", "block.minecraft.banner.flower.gray": "Estampado gris de flor", "block.minecraft.banner.flower.green": "Estampado verde de flor", "block.minecraft.banner.flower.light_blue": "Estampado celeste de flor", "block.minecraft.banner.flower.light_gray": "Estampado gris claro de flor", "block.minecraft.banner.flower.lime": "Estampado verde lima de flor", "block.minecraft.banner.flower.magenta": "Estampado magenta de flor", "block.minecraft.banner.flower.orange": "Estampado naranja de flor", "block.minecraft.banner.flower.pink": "Estampado rosado de flor", "block.minecraft.banner.flower.purple": "Estampado morado de flor", "block.minecraft.banner.flower.red": "Estampado rojo de flor", "block.minecraft.banner.flower.white": "Estampado blanco de flor", "block.minecraft.banner.flower.yellow": "Estampado amarillo de flor", "block.minecraft.banner.globe.black": "Planeta negro", "block.minecraft.banner.globe.blue": "Planeta azul", "block.minecraft.banner.globe.brown": "Planeta café", "block.minecraft.banner.globe.cyan": "Planeta cian", "block.minecraft.banner.globe.gray": "Planeta gris", "block.minecraft.banner.globe.green": "Planeta verde", "block.minecraft.banner.globe.light_blue": "Planeta celeste", "block.minecraft.banner.globe.light_gray": "Planeta gris claro", "block.minecraft.banner.globe.lime": "Planeta lima", "block.minecraft.banner.globe.magenta": "Planeta magenta", "block.minecraft.banner.globe.orange": "Planeta naranja", "block.minecraft.banner.globe.pink": "Planeta rosado", "block.minecraft.banner.globe.purple": "Planeta morado", "block.minecraft.banner.globe.red": "Planeta rojo", "block.minecraft.banner.globe.white": "Planeta blanco", "block.minecraft.banner.globe.yellow": "Planeta amarillo", "block.minecraft.banner.gradient.black": "Gradiente negro", "block.minecraft.banner.gradient.blue": "Gradiente azul", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Gradiente cian", "block.minecraft.banner.gradient.gray": "Gradiente gris", "block.minecraft.banner.gradient.green": "Gradiente verde", "block.minecraft.banner.gradient.light_blue": "Gradiente celeste", "block.minecraft.banner.gradient.light_gray": "Grad<PERSON>e gris claro", "block.minecraft.banner.gradient.lime": "Gradiente verde lima", "block.minecraft.banner.gradient.magenta": "Gradiente magenta", "block.minecraft.banner.gradient.orange": "Gradiente naranja", "block.minecraft.banner.gradient.pink": "Gradiente rosado", "block.minecraft.banner.gradient.purple": "Gradiente morado", "block.minecraft.banner.gradient.red": "Gradiente rojo", "block.minecraft.banner.gradient.white": "Gradiente blanco", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON><PERSON> am<PERSON>llo", "block.minecraft.banner.gradient_up.black": "Gradiente negro en base", "block.minecraft.banner.gradient_up.blue": "Gradiente azul en base", "block.minecraft.banner.gradient_up.brown": "Gradiente marrón en base", "block.minecraft.banner.gradient_up.cyan": "Gradiente cian en base", "block.minecraft.banner.gradient_up.gray": "Gradiente gris en base", "block.minecraft.banner.gradient_up.green": "Gradiente verde en base", "block.minecraft.banner.gradient_up.light_blue": "Gradiente celeste en base", "block.minecraft.banner.gradient_up.light_gray": "Gradiente gris claro en base", "block.minecraft.banner.gradient_up.lime": "Gradiente verde lima en base", "block.minecraft.banner.gradient_up.magenta": "Gradiente magenta en base", "block.minecraft.banner.gradient_up.orange": "Gradiente naranja en base", "block.minecraft.banner.gradient_up.pink": "Gradiente rosado en base", "block.minecraft.banner.gradient_up.purple": "Gradiente morado en base", "block.minecraft.banner.gradient_up.red": "Gradiente rojo en base", "block.minecraft.banner.gradient_up.white": "Gradiente blanco en base", "block.minecraft.banner.gradient_up.yellow": "Gradiente amarillo en base", "block.minecraft.banner.guster.black": "Torbellino negro", "block.minecraft.banner.guster.blue": "Torbellino azul", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.guster.green": "Torbellino verde", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.guster.lime": "Torbellino verde lima", "block.minecraft.banner.guster.magenta": "Torbellino magenta", "block.minecraft.banner.guster.orange": "Torbellino naranja", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON> rosado", "block.minecraft.banner.guster.purple": "Torbell<PERSON> morado", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "Cortado negro", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.half_horizontal.gray": "Cortado gris", "block.minecraft.banner.half_horizontal.green": "Cortado verde", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.half_horizontal.light_gray": "Cortado gris claro", "block.minecraft.banner.half_horizontal.lime": "Cortado verde lima", "block.minecraft.banner.half_horizontal.magenta": "Cortado magenta", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON>rtado rosado", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON>do morado", "block.minecraft.banner.half_horizontal.red": "<PERSON>rtado rojo", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "Cortado negro invertido", "block.minecraft.banner.half_horizontal_bottom.blue": "Cortado azul invertido", "block.minecraft.banner.half_horizontal_bottom.brown": "Cortado marrón invertido", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON>rtado cian invertido", "block.minecraft.banner.half_horizontal_bottom.gray": "Cortado gris invertido", "block.minecraft.banner.half_horizontal_bottom.green": "Cortado verde invertido", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Cortado celeste invertido", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Cortado gris claro invertido", "block.minecraft.banner.half_horizontal_bottom.lime": "Cortado verde lima invertido", "block.minecraft.banner.half_horizontal_bottom.magenta": "Cortado magenta invertido", "block.minecraft.banner.half_horizontal_bottom.orange": "Cortado naranja invertido", "block.minecraft.banner.half_horizontal_bottom.pink": "Cortado rosado invertido", "block.minecraft.banner.half_horizontal_bottom.purple": "Cortado morado invertido", "block.minecraft.banner.half_horizontal_bottom.red": "Cortado rojo invertido", "block.minecraft.banner.half_horizontal_bottom.white": "Cortado blanco invertido", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON> amarillo invertido", "block.minecraft.banner.half_vertical.black": "Flanco negro", "block.minecraft.banner.half_vertical.blue": "Flanco azul", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.half_vertical.gray": "Flanco gris", "block.minecraft.banner.half_vertical.green": "Flanco verde", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.half_vertical.light_gray": "Flanco gris claro", "block.minecraft.banner.half_vertical.lime": "Flanco verde lima", "block.minecraft.banner.half_vertical.magenta": "Flanco magenta", "block.minecraft.banner.half_vertical.orange": "Flanco <PERSON>", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> rosado", "block.minecraft.banner.half_vertical.purple": "<PERSON>lanco morado", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.half_vertical.white": "Partido blanco", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "Flanco negro invertido", "block.minecraft.banner.half_vertical_right.blue": "Flanco azul invertido", "block.minecraft.banner.half_vertical_right.brown": "Flanco ma<PERSON>ón invertido", "block.minecraft.banner.half_vertical_right.cyan": "Flanco cian invertido", "block.minecraft.banner.half_vertical_right.gray": "Flanco gris invertido", "block.minecraft.banner.half_vertical_right.green": "Flanco verde invertido", "block.minecraft.banner.half_vertical_right.light_blue": "Flanco celeste invertido", "block.minecraft.banner.half_vertical_right.light_gray": "Flanco gris claro invertido", "block.minecraft.banner.half_vertical_right.lime": "Flanco verde lima invertido", "block.minecraft.banner.half_vertical_right.magenta": "Flanco magenta invertido", "block.minecraft.banner.half_vertical_right.orange": "Flanco naranja invertido", "block.minecraft.banner.half_vertical_right.pink": "Flanco rosado invertido", "block.minecraft.banner.half_vertical_right.purple": "Flanco morado invertido", "block.minecraft.banner.half_vertical_right.red": "Flanco rojo invertido", "block.minecraft.banner.half_vertical_right.white": "Flanco blanco invertido", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>co amarillo invertido", "block.minecraft.banner.mojang.black": "Cosa negra", "block.minecraft.banner.mojang.blue": "Cosa a<PERSON>l", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.mojang.gray": "<PERSON>sa gris", "block.minecraft.banner.mojang.green": "Cosa verde", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON> celeste", "block.minecraft.banner.mojang.light_gray": "<PERSON>sa gris claro", "block.minecraft.banner.mojang.lime": "Cosa verde lima", "block.minecraft.banner.mojang.magenta": "Cosa magenta", "block.minecraft.banner.mojang.orange": "Cosa naranja", "block.minecraft.banner.mojang.pink": "Cosa rosada", "block.minecraft.banner.mojang.purple": "Cosa morada", "block.minecraft.banner.mojang.red": "Cosa roja", "block.minecraft.banner.mojang.white": "Cosa blanca", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> amarilla", "block.minecraft.banner.piglin.black": "Hocico negro", "block.minecraft.banner.piglin.blue": "Hocico azul", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.piglin.gray": "Hocico gris", "block.minecraft.banner.piglin.green": "Hocico verde", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.piglin.light_gray": "<PERSON>cico gris claro", "block.minecraft.banner.piglin.lime": "Hocico verde lima", "block.minecraft.banner.piglin.magenta": "Hocico magenta", "block.minecraft.banner.piglin.orange": "Hocico na<PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON> morado", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "Rombo negro", "block.minecraft.banner.rhombus.blue": "Rombo azul", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.rhombus.gray": "Rombo gris", "block.minecraft.banner.rhombus.green": "Rombo verde", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON>o gris claro", "block.minecraft.banner.rhombus.lime": "Rombo verde lima", "block.minecraft.banner.rhombus.magenta": "Rombo magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON>o rosado", "block.minecraft.banner.rhombus.purple": "Rombo morado", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>o rojo", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>o blanco", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.skull.black": "Estampado negro de calavera", "block.minecraft.banner.skull.blue": "Estampado azul de calavera", "block.minecraft.banner.skull.brown": "Estampado marrón de calavera", "block.minecraft.banner.skull.cyan": "Estampado cian de calavera", "block.minecraft.banner.skull.gray": "Estampado gris de calavera", "block.minecraft.banner.skull.green": "Estampado verde de calavera", "block.minecraft.banner.skull.light_blue": "Estampado celeste de calavera", "block.minecraft.banner.skull.light_gray": "Estampado gris claro de calavera", "block.minecraft.banner.skull.lime": "Estampado verde lima de calavera", "block.minecraft.banner.skull.magenta": "Estampado magenta de calavera", "block.minecraft.banner.skull.orange": "Estampado naranja de calavera", "block.minecraft.banner.skull.pink": "Estampado rosado de calavera", "block.minecraft.banner.skull.purple": "Estampado morado de calavera", "block.minecraft.banner.skull.red": "Estampado rojo de calavera", "block.minecraft.banner.skull.white": "Estampado blanco de calavera", "block.minecraft.banner.skull.yellow": "Estampado amarillo de calavera", "block.minecraft.banner.small_stripes.black": "Bastonado negro", "block.minecraft.banner.small_stripes.blue": "Bastonado azul", "block.minecraft.banner.small_stripes.brown": "Bastonado marrón", "block.minecraft.banner.small_stripes.cyan": "Ba<PERSON><PERSON> cian", "block.minecraft.banner.small_stripes.gray": "Bastonado gris", "block.minecraft.banner.small_stripes.green": "Bastonado verde", "block.minecraft.banner.small_stripes.light_blue": "Bastonado celeste", "block.minecraft.banner.small_stripes.light_gray": "Bastonado gris claro", "block.minecraft.banner.small_stripes.lime": "Bastonado verde lima", "block.minecraft.banner.small_stripes.magenta": "Bastonado magenta", "block.minecraft.banner.small_stripes.orange": "Bastonado naranja", "block.minecraft.banner.small_stripes.pink": "Bastonado rosado", "block.minecraft.banner.small_stripes.purple": "Bastonado morado", "block.minecraft.banner.small_stripes.red": "Bastonado rojo", "block.minecraft.banner.small_stripes.white": "Bastonado blanco", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.square_bottom_left.black": "Cantón diestro negro en campaña", "block.minecraft.banner.square_bottom_left.blue": "Cantón diestro azul en campaña", "block.minecraft.banner.square_bottom_left.brown": "Cantón diestro café en campaña", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON>t<PERSON> diestro cian en campaña", "block.minecraft.banner.square_bottom_left.gray": "Cant<PERSON> diestro gris en campaña", "block.minecraft.banner.square_bottom_left.green": "Cantón diestro verde en campaña", "block.minecraft.banner.square_bottom_left.light_blue": "Cantón diestro celeste en campaña", "block.minecraft.banner.square_bottom_left.light_gray": "Cant<PERSON> diestro gris claro en campaña", "block.minecraft.banner.square_bottom_left.lime": "Cantón diestro verde lima en campaña", "block.minecraft.banner.square_bottom_left.magenta": "Cantón diestro magenta en campaña", "block.minecraft.banner.square_bottom_left.orange": "Cantón diestro naranja en campaña", "block.minecraft.banner.square_bottom_left.pink": "Cantón diestro rosado en campaña", "block.minecraft.banner.square_bottom_left.purple": "Cantón diestro morado en campaña", "block.minecraft.banner.square_bottom_left.red": "Cantón diestro rojo en campaña", "block.minecraft.banner.square_bottom_left.white": "Cantón diestro blanco en campaña", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON>t<PERSON> diestro amarillo en campaña", "block.minecraft.banner.square_bottom_right.black": "Cantón siniestro negro en campaña", "block.minecraft.banner.square_bottom_right.blue": "Cantón siniestro azul en campaña", "block.minecraft.banner.square_bottom_right.brown": "Cantón siniestro café en campaña", "block.minecraft.banner.square_bottom_right.cyan": "Cantón siniestro cian en campaña", "block.minecraft.banner.square_bottom_right.gray": "Cantón siniestro gris en campaña", "block.minecraft.banner.square_bottom_right.green": "Cantón siniestro verde en campaña", "block.minecraft.banner.square_bottom_right.light_blue": "Cantón siniestro celeste en campaña", "block.minecraft.banner.square_bottom_right.light_gray": "Cantón siniestro gris claro en campaña", "block.minecraft.banner.square_bottom_right.lime": "Cantón siniestro verde lima en campaña", "block.minecraft.banner.square_bottom_right.magenta": "Cantón siniestro magenta en campaña", "block.minecraft.banner.square_bottom_right.orange": "Cantón siniestro naranja en campaña", "block.minecraft.banner.square_bottom_right.pink": "Cantón siniestro rosado en campaña", "block.minecraft.banner.square_bottom_right.purple": "Cantón siniestro morado en campaña", "block.minecraft.banner.square_bottom_right.red": "Cantón siniestro rojo en campaña", "block.minecraft.banner.square_bottom_right.white": "Cantón siniestro blanco en campaña", "block.minecraft.banner.square_bottom_right.yellow": "Cantón siniestro amarillo en campaña", "block.minecraft.banner.square_top_left.black": "Cantón diestro negro en jefe", "block.minecraft.banner.square_top_left.blue": "Cantón diestro azul en jefe", "block.minecraft.banner.square_top_left.brown": "Cantón diestro café en jefe", "block.minecraft.banner.square_top_left.cyan": "Cantón diestro cian en jefe", "block.minecraft.banner.square_top_left.gray": "Cantón diestro gris en jefe", "block.minecraft.banner.square_top_left.green": "Cantón diestro verde en jefe", "block.minecraft.banner.square_top_left.light_blue": "Cantón diestro celeste en jefe", "block.minecraft.banner.square_top_left.light_gray": "Cantón diestro gris claro en jefe", "block.minecraft.banner.square_top_left.lime": "Cantón diestro verde lima en jefe", "block.minecraft.banner.square_top_left.magenta": "Cantón diestro magenta en jefe", "block.minecraft.banner.square_top_left.orange": "Cantón diestro naranja en jefe", "block.minecraft.banner.square_top_left.pink": "Cantón diestro rosado en jefe", "block.minecraft.banner.square_top_left.purple": "Cantón diestro morado en jefe", "block.minecraft.banner.square_top_left.red": "Cantón diestro rojo en jefe", "block.minecraft.banner.square_top_left.white": "Cantón diestro blanco en jefe", "block.minecraft.banner.square_top_left.yellow": "Cant<PERSON> diestro amarillo en jefe", "block.minecraft.banner.square_top_right.black": "Cantón siniestro negro en jefe", "block.minecraft.banner.square_top_right.blue": "Cantón siniestro azul en jefe", "block.minecraft.banner.square_top_right.brown": "Cantón siniestro café en jefe", "block.minecraft.banner.square_top_right.cyan": "Cantón siniestro cian en jefe", "block.minecraft.banner.square_top_right.gray": "Cantón siniestro gris en jefe", "block.minecraft.banner.square_top_right.green": "Cantón siniestro verde en jefe", "block.minecraft.banner.square_top_right.light_blue": "Cantón siniestro celeste en jefe", "block.minecraft.banner.square_top_right.light_gray": "Cantón siniestro gris claro en jefe", "block.minecraft.banner.square_top_right.lime": "Cantón siniestro verde lima en jefe", "block.minecraft.banner.square_top_right.magenta": "Cantón siniestro magenta en jefe", "block.minecraft.banner.square_top_right.orange": "Cantón naranja en jefe a siniestra", "block.minecraft.banner.square_top_right.pink": "Cantón siniestro rosado en jefe", "block.minecraft.banner.square_top_right.purple": "Cantón siniestro morado en jefe", "block.minecraft.banner.square_top_right.red": "Cantón siniestro rojo en jefe", "block.minecraft.banner.square_top_right.white": "Cantón blanco en jefe a siniestra", "block.minecraft.banner.square_top_right.yellow": "Cantón siniestro amarillo en jefe", "block.minecraft.banner.straight_cross.black": "Cruz negra", "block.minecraft.banner.straight_cross.blue": "Cruz azul", "block.minecraft.banner.straight_cross.brown": "Cruz café", "block.minecraft.banner.straight_cross.cyan": "<PERSON> cian", "block.minecraft.banner.straight_cross.gray": "<PERSON> gris", "block.minecraft.banner.straight_cross.green": "Cruz verde", "block.minecraft.banner.straight_cross.light_blue": "<PERSON> celeste", "block.minecraft.banner.straight_cross.light_gray": "<PERSON> gris claro", "block.minecraft.banner.straight_cross.lime": "Cruz verde lima", "block.minecraft.banner.straight_cross.magenta": "Cruz magenta", "block.minecraft.banner.straight_cross.orange": "<PERSON> naranja", "block.minecraft.banner.straight_cross.pink": "Cruz rosada", "block.minecraft.banner.straight_cross.purple": "Cruz morada", "block.minecraft.banner.straight_cross.red": "Cruz roja", "block.minecraft.banner.straight_cross.white": "Cruz blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON> amari<PERSON>", "block.minecraft.banner.stripe_bottom.black": "Base Negra", "block.minecraft.banner.stripe_bottom.blue": "Base Azul", "block.minecraft.banner.stripe_bottom.brown": "Base Marrón ", "block.minecraft.banner.stripe_bottom.cyan": "Base Cian", "block.minecraft.banner.stripe_bottom.gray": "Base Gris", "block.minecraft.banner.stripe_bottom.green": "Base Verde", "block.minecraft.banner.stripe_bottom.light_blue": "Base celeste", "block.minecraft.banner.stripe_bottom.light_gray": "Base G<PERSON>", "block.minecraft.banner.stripe_bottom.lime": "Base Lima", "block.minecraft.banner.stripe_bottom.magenta": "Base Magenta", "block.minecraft.banner.stripe_bottom.orange": "Base Naranja", "block.minecraft.banner.stripe_bottom.pink": "Base Rosada", "block.minecraft.banner.stripe_bottom.purple": "Base Lila", "block.minecraft.banner.stripe_bottom.red": "Base Roja", "block.minecraft.banner.stripe_bottom.white": "Base Blanca", "block.minecraft.banner.stripe_bottom.yellow": " Base Amarilla", "block.minecraft.banner.stripe_center.black": "<PERSON>lo negro", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON>l", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON>ian", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.stripe_center.green": "Palo verde", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON> celeste", "block.minecraft.banner.stripe_center.light_gray": "<PERSON>lo gris claro", "block.minecraft.banner.stripe_center.lime": "Palo verde lima", "block.minecraft.banner.stripe_center.magenta": "Palo magenta", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON>lo rosado", "block.minecraft.banner.stripe_center.purple": "<PERSON>lo morado", "block.minecraft.banner.stripe_center.red": "<PERSON>lo rojo", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON> blanco", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Banda negra a siniestra", "block.minecraft.banner.stripe_downleft.blue": "Banda azul a siniestra", "block.minecraft.banner.stripe_downleft.brown": "Banda marrón a siniestra", "block.minecraft.banner.stripe_downleft.cyan": "Banda cian a siniestra", "block.minecraft.banner.stripe_downleft.gray": "Banda gris a siniestra", "block.minecraft.banner.stripe_downleft.green": "Banda verde a siniestra", "block.minecraft.banner.stripe_downleft.light_blue": "Banda celeste a siniestra", "block.minecraft.banner.stripe_downleft.light_gray": "Banda gris claro a siniestra", "block.minecraft.banner.stripe_downleft.lime": "Banda verde lima a siniestra", "block.minecraft.banner.stripe_downleft.magenta": "Banda magenta a siniestra", "block.minecraft.banner.stripe_downleft.orange": "Banda naranja a siniestra", "block.minecraft.banner.stripe_downleft.pink": "Banda rosada a siniestra", "block.minecraft.banner.stripe_downleft.purple": "Banda morada a siniestra", "block.minecraft.banner.stripe_downleft.red": "Banda roja a siniestra", "block.minecraft.banner.stripe_downleft.white": "Banda blanca a siniestra", "block.minecraft.banner.stripe_downleft.yellow": "Banda amarilla a siniestra", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda azul", "block.minecraft.banner.stripe_downright.brown": "Banda marrón", "block.minecraft.banner.stripe_downright.cyan": "Banda cian", "block.minecraft.banner.stripe_downright.gray": "Banda gris", "block.minecraft.banner.stripe_downright.green": "Banda verde", "block.minecraft.banner.stripe_downright.light_blue": "Banda celeste", "block.minecraft.banner.stripe_downright.light_gray": "Banda gris claro", "block.minecraft.banner.stripe_downright.lime": "Banda verde lima", "block.minecraft.banner.stripe_downright.magenta": "Banda magenta", "block.minecraft.banner.stripe_downright.orange": "Banda naranja", "block.minecraft.banner.stripe_downright.pink": "Banda rosada", "block.minecraft.banner.stripe_downright.purple": "Banda morada", "block.minecraft.banner.stripe_downright.red": "Banda roja", "block.minecraft.banner.stripe_downright.white": "Banda blanca", "block.minecraft.banner.stripe_downright.yellow": "Banda amarilla", "block.minecraft.banner.stripe_left.black": "Palo negro a diestra", "block.minecraft.banner.stripe_left.blue": "<PERSON>lo azul a diestra", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON> marr<PERSON> a diestra", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON> cian a diestra", "block.minecraft.banner.stripe_left.gray": "<PERSON>lo gris a diestra", "block.minecraft.banner.stripe_left.green": "Palo verde a diestra", "block.minecraft.banner.stripe_left.light_blue": "<PERSON>lo celeste a diestra", "block.minecraft.banner.stripe_left.light_gray": "Palo gris claro a diestra", "block.minecraft.banner.stripe_left.lime": "Palo verde lima a diestra", "block.minecraft.banner.stripe_left.magenta": "Palo magenta a diestra", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON> naranja a diestra", "block.minecraft.banner.stripe_left.pink": "Palo rosado a diestra", "block.minecraft.banner.stripe_left.purple": "Palo morado a diestra", "block.minecraft.banner.stripe_left.red": "<PERSON>lo rojo a diestra", "block.minecraft.banner.stripe_left.white": "Palo blanco a diestra", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON> amarillo a diestra", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON> negra", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.stripe_middle.lime": "Franja verde lima", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON> magenta", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> rosada", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON> mora<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.stripe_right.black": "Palo negro a siniestra", "block.minecraft.banner.stripe_right.blue": "Palo azul a siniestra", "block.minecraft.banner.stripe_right.brown": "<PERSON>lo marrón a siniestra", "block.minecraft.banner.stripe_right.cyan": "<PERSON>lo cian a siniestra", "block.minecraft.banner.stripe_right.gray": "Palo gris a siniestra", "block.minecraft.banner.stripe_right.green": "Palo verde a siniestra", "block.minecraft.banner.stripe_right.light_blue": "Palo celeste a siniestra", "block.minecraft.banner.stripe_right.light_gray": "Palo gris claro a siniestra", "block.minecraft.banner.stripe_right.lime": "Palo verde lima a siniestra", "block.minecraft.banner.stripe_right.magenta": "Palo magenta a siniestra", "block.minecraft.banner.stripe_right.orange": "<PERSON>lo naranja a siniestra", "block.minecraft.banner.stripe_right.pink": "Palo rosado a siniestra", "block.minecraft.banner.stripe_right.purple": "Palo morado a siniestra", "block.minecraft.banner.stripe_right.red": "Palo rojo a siniestra", "block.minecraft.banner.stripe_right.white": "Palo blanco a siniestra", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> amarillo a siniestra", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON> ", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "Jefe Verde", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "Chevrón negro", "block.minecraft.banner.triangle_bottom.blue": "Chevrón azul", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Chevr<PERSON> cian", "block.minecraft.banner.triangle_bottom.gray": "Chevrón gris", "block.minecraft.banner.triangle_bottom.green": "Chevrón verde", "block.minecraft.banner.triangle_bottom.light_blue": "Chevr<PERSON> celeste", "block.minecraft.banner.triangle_bottom.light_gray": "Chevrón gris claro", "block.minecraft.banner.triangle_bottom.lime": "Chevrón verde lima", "block.minecraft.banner.triangle_bottom.magenta": "Chevrón magenta", "block.minecraft.banner.triangle_bottom.orange": "Chevrón naranja", "block.minecraft.banner.triangle_bottom.pink": "Chevrón rosado", "block.minecraft.banner.triangle_bottom.purple": "Chevrón morado", "block.minecraft.banner.triangle_bottom.red": "Chevrón rojo", "block.minecraft.banner.triangle_bottom.white": "Chevrón blanco", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.triangle_top.black": "Chevrón negro invertido", "block.minecraft.banner.triangle_top.blue": "Chevrón azul invertido", "block.minecraft.banner.triangle_top.brown": "Chevrón marrón invertido", "block.minecraft.banner.triangle_top.cyan": "Chevrón cian invertido", "block.minecraft.banner.triangle_top.gray": "Chevrón gris invertido", "block.minecraft.banner.triangle_top.green": "Chevrón verde invertido", "block.minecraft.banner.triangle_top.light_blue": "Chevrón celeste invertido", "block.minecraft.banner.triangle_top.light_gray": "Chevrón gris claro invertido", "block.minecraft.banner.triangle_top.lime": "Chevrón verde lima invertido", "block.minecraft.banner.triangle_top.magenta": "Chevrón magenta invertido", "block.minecraft.banner.triangle_top.orange": "Chevrón naranja invertido", "block.minecraft.banner.triangle_top.pink": "Chevrón rosado invertido", "block.minecraft.banner.triangle_top.purple": "Chevrón morado invertido", "block.minecraft.banner.triangle_top.red": "Chevrón rojo invertido", "block.minecraft.banner.triangle_top.white": "Chevrón blanco invertido", "block.minecraft.banner.triangle_top.yellow": "Chevrón amarillo invertido", "block.minecraft.banner.triangles_bottom.black": "Base negra dentada", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON> roja", "block.minecraft.banner.triangles_bottom.brown": "Base marrón dentada", "block.minecraft.banner.triangles_bottom.cyan": "Base cian dentada", "block.minecraft.banner.triangles_bottom.gray": "Base gris dentada", "block.minecraft.banner.triangles_bottom.green": "Base verde dentada", "block.minecraft.banner.triangles_bottom.light_blue": "Base celeste dentada", "block.minecraft.banner.triangles_bottom.light_gray": "Base gris claro dentada", "block.minecraft.banner.triangles_bottom.lime": "Base verde lima dentada", "block.minecraft.banner.triangles_bottom.magenta": "Base morada dentada", "block.minecraft.banner.triangles_bottom.orange": "Base naranja dentada", "block.minecraft.banner.triangles_bottom.pink": "Base rosada dentada", "block.minecraft.banner.triangles_bottom.purple": "Base morada dentada", "block.minecraft.banner.triangles_bottom.red": "Base roja dentada", "block.minecraft.banner.triangles_bottom.white": "Base blanca dentada", "block.minecraft.banner.triangles_bottom.yellow": "Base amarilla dentada", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON> negro dentado", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> a<PERSON>l dentado", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> ma<PERSON> den<PERSON>o", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON> c<PERSON>o", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON> gris dentado", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON> verde dentado", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON> celeste dentado", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON> gris claro dentado", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON> verde lima dentado", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON> magenta dentado", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON> na<PERSON>ja dentado", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON> rosado dentado", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> morado dentado", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> rojo dentado", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON> blanco dentado", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> am<PERSON> den<PERSON>o", "block.minecraft.barrel": "Barril", "block.minecraft.barrier": "Barr<PERSON>", "block.minecraft.basalt": "Basalto", "block.minecraft.beacon": "Faro", "block.minecraft.beacon.primary": "Poder primario", "block.minecraft.beacon.secondary": "Poder secundario", "block.minecraft.bed.no_sleep": "Solo puedes dormir de noche o durante tormentas eléctricas", "block.minecraft.bed.not_safe": "No puedes dormir ahora: hay monstruos cerca", "block.minecraft.bed.obstructed": "Esta cama está obstruida", "block.minecraft.bed.occupied": "Esta cama está ocupada", "block.minecraft.bed.too_far_away": "No te puedes acostar ahora: la cama está muy lejos", "block.minecraft.bedrock": "Piedra base", "block.minecraft.bee_nest": "Colmena", "block.minecraft.beehive": "Apiario", "block.minecraft.beetroots": "Betarragas", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Plantaforma grande", "block.minecraft.big_dripleaf_stem": "Tallo de plantaforma grande", "block.minecraft.birch_button": "Botón de abedul", "block.minecraft.birch_door": "Puer<PERSON> de a<PERSON>ul", "block.minecraft.birch_fence": "Valla de abedul", "block.minecraft.birch_fence_gate": "Puerta de valla de abedul", "block.minecraft.birch_hanging_sign": "Cartel colgante de abedul", "block.minecraft.birch_leaves": "Hojas de abedul", "block.minecraft.birch_log": "Tronco de abedul", "block.minecraft.birch_planks": "Tablas de abedul", "block.minecraft.birch_pressure_plate": "Placa de presión de abedul", "block.minecraft.birch_sapling": "<PERSON><PERSON>", "block.minecraft.birch_sign": "Cartel de abedul", "block.minecraft.birch_slab": "Losa de abedul", "block.minecraft.birch_stairs": "Escaleras de abedul", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de a<PERSON>ul", "block.minecraft.birch_wall_hanging_sign": "Cartel colgante de abedul de pared", "block.minecraft.birch_wall_sign": "Cartel de abedul en pared", "block.minecraft.birch_wood": "<PERSON><PERSON>", "block.minecraft.black_banner": "Estandarte negro", "block.minecraft.black_bed": "Cama negra", "block.minecraft.black_candle": "Vela negra", "block.minecraft.black_candle_cake": "Torta con vela negra", "block.minecraft.black_carpet": "Alfombra negra", "block.minecraft.black_concrete": "Hormigón negro", "block.minecraft.black_concrete_powder": "Cemento negro", "block.minecraft.black_glazed_terracotta": "Azulejo negro", "block.minecraft.black_shulker_box": "Caja de shulker negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON> te<PERSON> de negro", "block.minecraft.black_stained_glass_pane": "Panel de vidrio negro", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "<PERSON> negra", "block.minecraft.blackstone": "Piedra negra", "block.minecraft.blackstone_slab": "Losa de piedra negra", "block.minecraft.blackstone_stairs": "Escaleras de piedra negra", "block.minecraft.blackstone_wall": "<PERSON><PERSON> de piedra negra", "block.minecraft.blast_furnace": "Alto horno", "block.minecraft.blue_banner": "Estandarte azul", "block.minecraft.blue_bed": "Cama a<PERSON>l", "block.minecraft.blue_candle": "<PERSON><PERSON> azul", "block.minecraft.blue_candle_cake": "Torta con vela azul", "block.minecraft.blue_carpet": "Alfombra azul", "block.minecraft.blue_concrete": "Hormigón azul", "block.minecraft.blue_concrete_powder": "Cemento azul", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> azul", "block.minecraft.blue_ice": "<PERSON><PERSON>", "block.minecraft.blue_orchid": "Orquídea a<PERSON>l", "block.minecraft.blue_shulker_box": "Caja de shulker azul", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.blue_stained_glass_pane": "Panel de vidrio azul", "block.minecraft.blue_terracotta": "Terracota azul", "block.minecraft.blue_wool": "<PERSON>", "block.minecraft.bone_block": "Bloque de huesos", "block.minecraft.bookshelf": "Librería", "block.minecraft.brain_coral": "Coral cerebro", "block.minecraft.brain_coral_block": "Bloque de coral cerebro", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON> cerebro", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> cerebro en pared", "block.minecraft.brewing_stand": "Alambique", "block.minecraft.brick_slab": "Losa de ladrillos", "block.minecraft.brick_stairs": "Escaleras de ladrillos", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "Ladrillos", "block.minecraft.brown_banner": "Estandarte café", "block.minecraft.brown_bed": "Cama café", "block.minecraft.brown_candle": "Vela café", "block.minecraft.brown_candle_cake": "Torta con vela café", "block.minecraft.brown_carpet": "Alfombra café", "block.minecraft.brown_concrete": "Hormigón café", "block.minecraft.brown_concrete_powder": "Cemento café", "block.minecraft.brown_glazed_terracotta": "Azulejo café", "block.minecraft.brown_mushroom": "Callampa café", "block.minecraft.brown_mushroom_block": "Bloque de callampa café", "block.minecraft.brown_shulker_box": "Caja de shulker café", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.brown_stained_glass_pane": "Panel de vidrio café", "block.minecraft.brown_terracotta": "Terracota café", "block.minecraft.brown_wool": "Lana café", "block.minecraft.bubble_column": "Columna de burbujas", "block.minecraft.bubble_coral": "Coral burbuja", "block.minecraft.bubble_coral_block": "Bloque de coral burbuja", "block.minecraft.bubble_coral_fan": "Gorgon<PERSON> burbuja", "block.minecraft.bubble_coral_wall_fan": "Gorgonia burbuja en pared", "block.minecraft.budding_amethyst": "Brotador de amatista", "block.minecraft.bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de cactus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor de sculk calibrado", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Vela", "block.minecraft.candle_cake": "Torta con vela", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Mesa de cartografía", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON> tallado", "block.minecraft.cauldron": "Calder<PERSON>", "block.minecraft.cave_air": "Aire de cueva", "block.minecraft.cave_vines": "Enredaderas de cueva", "block.minecraft.cave_vines_plant": "Planta de enredaderas de cueva", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloque de comandos en cadena", "block.minecraft.cherry_button": "Botón de cerezo", "block.minecraft.cherry_door": "<PERSON>uer<PERSON> de cerezo", "block.minecraft.cherry_fence": "Valla de cerezo", "block.minecraft.cherry_fence_gate": "Puerta de valla de cerezo", "block.minecraft.cherry_hanging_sign": "Cartel colgante de cerezo", "block.minecraft.cherry_leaves": "Hojas de cerezo", "block.minecraft.cherry_log": "Tronco de cerezo", "block.minecraft.cherry_planks": "Tablas de cerezo", "block.minecraft.cherry_pressure_plate": "Placa de presión de cerezo", "block.minecraft.cherry_sapling": "<PERSON><PERSON> cere<PERSON>", "block.minecraft.cherry_sign": "Cartel de cerezo", "block.minecraft.cherry_slab": "Losa de cerezo", "block.minecraft.cherry_stairs": "Escaleras de cerezo", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de cerezo", "block.minecraft.cherry_wall_hanging_sign": "Cartel colgante de cerezo de pared", "block.minecraft.cherry_wall_sign": "Cartel colgante de cerezo", "block.minecraft.cherry_wood": "<PERSON><PERSON> c<PERSON>", "block.minecraft.chest": "Cofre", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON> de libros", "block.minecraft.chiseled_copper": "Cobre cincelado", "block.minecraft.chiseled_deepslate": "Pizarra abismal cincelada", "block.minecraft.chiseled_nether_bricks": "Ladrillos del Nether cincelados", "block.minecraft.chiseled_polished_blackstone": "Piedra negra pulida cincelada", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON>rzo cin<PERSON>", "block.minecraft.chiseled_red_sandstone": "Arenisca roja cincelada", "block.minecraft.chiseled_resin_bricks": "Ladrillos de resina cincelados", "block.minecraft.chiseled_sandstone": "Arenisca cincelada", "block.minecraft.chiseled_stone_bricks": "Ladrillos de piedra cincelados", "block.minecraft.chiseled_tuff": "Toba cincelada", "block.minecraft.chiseled_tuff_bricks": "Ladrillos de toba cincelados", "block.minecraft.chorus_flower": "Flor chorus", "block.minecraft.chorus_plant": "Planta chorus", "block.minecraft.clay": "Bloque de arcilla", "block.minecraft.closed_eyeblossom": "Miraflor cerrada", "block.minecraft.coal_block": "Bloque de car<PERSON>ón", "block.minecraft.coal_ore": "Mineral de carbón", "block.minecraft.coarse_dirt": "Tierra estéril", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>rra a<PERSON>", "block.minecraft.cobbled_deepslate_slab": "Losa de ado<PERSON><PERSON>ín de pizarra abismal", "block.minecraft.cobbled_deepslate_stairs": "Escaleras de adoquín de pizarra abismal", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> de p<PERSON> a<PERSON>", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Losa de adoquín", "block.minecraft.cobblestone_stairs": "Escaleras de adoquín", "block.minecraft.cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.cobweb": "Telaraña", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "Bloque de comandos", "block.minecraft.comparator": "Comparador de redstone", "block.minecraft.composter": "Compostera", "block.minecraft.conduit": "Canalizador", "block.minecraft.copper_block": "Bloque de cobre", "block.minecraft.copper_bulb": "Lámpara de cobre", "block.minecraft.copper_door": "Puerta de cobre", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Mineral de cobre", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de cobre", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Ladrillos de pizarra abismal agrietados", "block.minecraft.cracked_deepslate_tiles": "Baldosas de pizarra abismal agrietadas", "block.minecraft.cracked_nether_bricks": "Ladrillos del Nether agrietados", "block.minecraft.cracked_polished_blackstone_bricks": "Ladrillos de piedra negra pulida agrietados", "block.minecraft.cracked_stone_bricks": "Ladrillos de piedra agrietados", "block.minecraft.crafter": "Crafteadora", "block.minecraft.crafting_table": "Mesa de crafteo", "block.minecraft.creaking_heart": "Corazón de crujidor", "block.minecraft.creeper_head": "C<PERSON>za de creeper", "block.minecraft.creeper_wall_head": "Cabeza de creeper en pared", "block.minecraft.crimson_button": "Bo<PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "Puerta de valla carmesí", "block.minecraft.crimson_fungus": "<PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "Cartel colgante carmesí", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON>", "block.minecraft.crimson_planks": "Tablas car<PERSON>", "block.minecraft.crimson_pressure_plate": "Placa de presión carmesí", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Cartel carmesí", "block.minecraft.crimson_slab": "Losa car<PERSON>í", "block.minecraft.crimson_stairs": "Escaleras carmesí", "block.minecraft.crimson_stem": "Tallo car<PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Cartel colgante carmesí de pared", "block.minecraft.crimson_wall_sign": "Letrero carmesí en pared", "block.minecraft.crying_obsidian": "Obsidiana ll<PERSON>na", "block.minecraft.cut_copper": "Cobre cortado", "block.minecraft.cut_copper_slab": "Losa de cobre cortado", "block.minecraft.cut_copper_stairs": "Escaleras de cobre cortado", "block.minecraft.cut_red_sandstone": "Arenisca roja cortada", "block.minecraft.cut_red_sandstone_slab": "Losa de arenisca roja cortada", "block.minecraft.cut_sandstone": "Arenisca cortada", "block.minecraft.cut_sandstone_slab": "Losa de arenisca cortada", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_bed": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle_cake": "Torta con vela cian", "block.minecraft.cyan_carpet": "Alfombra cian", "block.minecraft.cyan_concrete": "Hormig<PERSON> cian", "block.minecraft.cyan_concrete_powder": "Cemento cian", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_shulker_box": "Caja de shulker cian", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "Panel de vidrio cian", "block.minecraft.cyan_terracotta": "Terracota cian", "block.minecraft.cyan_wool": "<PERSON> c<PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON> m<PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "Botón de roble oscuro", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_fence": "Valla de roble oscuro", "block.minecraft.dark_oak_fence_gate": "Puerta de valla de roble oscuro", "block.minecraft.dark_oak_hanging_sign": "Cartel colgante de roble oscuro", "block.minecraft.dark_oak_leaves": "Hojas de roble oscuro", "block.minecraft.dark_oak_log": "Tronco de roble oscuro", "block.minecraft.dark_oak_planks": "Tablas de roble oscuro", "block.minecraft.dark_oak_pressure_plate": "Placa de presión de roble oscuro", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_sign": "Cartel de roble oscuro", "block.minecraft.dark_oak_slab": "Losa de roble oscuro", "block.minecraft.dark_oak_stairs": "Escaleras de roble oscuro", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_wall_hanging_sign": "Cartel colgante de roble oscuro de pared", "block.minecraft.dark_oak_wall_sign": "Cartel de roble oscuro en pared", "block.minecraft.dark_oak_wood": "<PERSON><PERSON> de roble oscuro", "block.minecraft.dark_prismarine": "Prismarina oscura", "block.minecraft.dark_prismarine_slab": "Losa de prismarina oscura", "block.minecraft.dark_prismarine_stairs": "Escaleras de prismarina oscura", "block.minecraft.daylight_detector": "Detector de luz ambiental", "block.minecraft.dead_brain_coral": "Coral cerebro muerto", "block.minecraft.dead_brain_coral_block": "Bloque de coral cerebro muerto", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> cerebro muerta", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cerebro muerta en pared", "block.minecraft.dead_bubble_coral": "Coral burbuja muerto", "block.minecraft.dead_bubble_coral_block": "Bloque de coral burbuja muerto", "block.minecraft.dead_bubble_coral_fan": "Gorgonia burbuja muerta", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgonia de burbuja muerta en pared", "block.minecraft.dead_bush": "Arbusto seco", "block.minecraft.dead_fire_coral": "Coral de fuego muerto", "block.minecraft.dead_fire_coral_block": "Bloque de coral de fuego muerto", "block.minecraft.dead_fire_coral_fan": "Gorgonia de fuego muerta", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de fuego muerta en pared", "block.minecraft.dead_horn_coral": "Coral cuerno muerto", "block.minecraft.dead_horn_coral_block": "Bloque de coral cuerno muerto", "block.minecraft.dead_horn_coral_fan": "Gorgonia cuerno muerta", "block.minecraft.dead_horn_coral_wall_fan": "Gorgonia de cuerno muerta en pared", "block.minecraft.dead_tube_coral": "Coral de tubo muerto", "block.minecraft.dead_tube_coral_block": "Bloque de coral del tubo muerto", "block.minecraft.dead_tube_coral_fan": "Gorgonia de tubo muerta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgonia de tubo muerta en pared", "block.minecraft.decorated_pot": "Vasija decorada", "block.minecraft.deepslate": "Pi<PERSON>ra a<PERSON>", "block.minecraft.deepslate_brick_slab": "Losa de ladrillos de pizarra abismal", "block.minecraft.deepslate_brick_stairs": "Escaleras de ladrillos de pizarra abismal", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> de <PERSON> de pizarra abismal", "block.minecraft.deepslate_bricks": "Ladrillos de pizarra abismal", "block.minecraft.deepslate_coal_ore": "Mineral de carbón abismal", "block.minecraft.deepslate_copper_ore": "Mineral de cobre abismal", "block.minecraft.deepslate_diamond_ore": "Mineral de diamante abismal", "block.minecraft.deepslate_emerald_ore": "Mineral de esmeralda abismal", "block.minecraft.deepslate_gold_ore": "Mineral de oro abismal", "block.minecraft.deepslate_iron_ore": "Mineral de hierro abis<PERSON>", "block.minecraft.deepslate_lapis_ore": "Mineral de lapislázuli abismal", "block.minecraft.deepslate_redstone_ore": "Mineral de redstone abismal", "block.minecraft.deepslate_tile_slab": "Losa de baldosas de pizarra abismal", "block.minecraft.deepslate_tile_stairs": "Escaleras de baldosas de pizarra abismal", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> de <PERSON> de pizarra abismal", "block.minecraft.deepslate_tiles": "Baldosas de pizarra abismal", "block.minecraft.detector_rail": "Riel detector", "block.minecraft.diamond_block": "Bloque de diamante", "block.minecraft.diamond_ore": "Mineral de diamante", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Losa de diorita", "block.minecraft.diorite_stairs": "Escaleras de diorita", "block.minecraft.diorite_wall": "<PERSON><PERSON>", "block.minecraft.dirt": "Tierra", "block.minecraft.dirt_path": "Camino de tierra", "block.minecraft.dispenser": "Dispensador", "block.minecraft.dragon_egg": "Huevo de dragón", "block.minecraft.dragon_head": "Cabeza de dragón", "block.minecraft.dragon_wall_head": "Cabeza de dragón en pared", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Bloque de algas secas", "block.minecraft.dripstone_block": "Bloque de espeleotema", "block.minecraft.dropper": "Soltador", "block.minecraft.emerald_block": "Bloque de esmeralda", "block.minecraft.emerald_ore": "Mineral de esmeralda", "block.minecraft.enchanting_table": "Mesa de encantamientos", "block.minecraft.end_gateway": "Acceso al End", "block.minecraft.end_portal": "Marco <PERSON> portal del End", "block.minecraft.end_portal_frame": "Marco <PERSON> portal del End", "block.minecraft.end_rod": "Barra del End", "block.minecraft.end_stone": "Piedra del End", "block.minecraft.end_stone_brick_slab": "Losa de ladrillos de piedra del End", "block.minecraft.end_stone_brick_stairs": "Escaleras de ladrillos de piedra del End", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra del End", "block.minecraft.end_stone_bricks": "Ladrillos de piedra del End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON> <PERSON> ender", "block.minecraft.exposed_chiseled_copper": "Cobre cincelado expuesto", "block.minecraft.exposed_copper": "Cobre expuesto", "block.minecraft.exposed_copper_bulb": "Lámpara de cobre expuesto", "block.minecraft.exposed_copper_door": "Puerta de cobre expuesto", "block.minecraft.exposed_copper_grate": "Rejilla de cobre expuesto", "block.minecraft.exposed_copper_trapdoor": "Trampilla de cobre expuesto", "block.minecraft.exposed_cut_copper": "Cobre cortado expuesto", "block.minecraft.exposed_cut_copper_slab": "Losa de cobre cortado expuesto", "block.minecraft.exposed_cut_copper_stairs": "Escaleras de cobre cortado expuesto", "block.minecraft.farmland": "Tierra de cultivo", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "Fuego", "block.minecraft.fire_coral": "Coral de fuego", "block.minecraft.fire_coral_block": "Bloque de coral de fuego", "block.minecraft.fire_coral_fan": "Gorgonia de fuego", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de fuego en pared", "block.minecraft.firefly_bush": "Arbusto con luciérnagas", "block.minecraft.fletching_table": "Mesa de flechas", "block.minecraft.flower_pot": "<PERSON><PERSON>", "block.minecraft.flowering_azalea": "Azalea florida", "block.minecraft.flowering_azalea_leaves": "Hojas de azalea florida", "block.minecraft.frogspawn": "<PERSON><PERSON> de rana", "block.minecraft.frosted_ice": "Escarcha", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "Piedra negra con oro incrustado", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "Panel de vidrio", "block.minecraft.glow_lichen": "Líquen brillante", "block.minecraft.glowstone": "Glowstone", "block.minecraft.gold_block": "Bloque de oro", "block.minecraft.gold_ore": "Mineral de oro", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Losa de granito", "block.minecraft.granite_stairs": "Escaleras de granito", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "Pasto", "block.minecraft.grass_block": "Bloque de pasto", "block.minecraft.gravel": "Grava", "block.minecraft.gray_banner": "Estandarte gris", "block.minecraft.gray_bed": "<PERSON>a gris", "block.minecraft.gray_candle": "<PERSON><PERSON> gris", "block.minecraft.gray_candle_cake": "Torta con vela gris", "block.minecraft.gray_carpet": "Alfombra gris", "block.minecraft.gray_concrete": "Hormig<PERSON> gris", "block.minecraft.gray_concrete_powder": "Cemento gris", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.gray_shulker_box": "Caja de shulker gris", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "Panel de vidrio gris", "block.minecraft.gray_terracotta": "Terracota gris", "block.minecraft.gray_wool": "<PERSON> gris", "block.minecraft.green_banner": "Estandarte verde", "block.minecraft.green_bed": "Cama verde", "block.minecraft.green_candle": "Vela verde", "block.minecraft.green_candle_cake": "Torta con vela verde", "block.minecraft.green_carpet": "Alfombra verde", "block.minecraft.green_concrete": "Hormigón verde", "block.minecraft.green_concrete_powder": "Cemento verde", "block.minecraft.green_glazed_terracotta": "A<PERSON>lejo verde", "block.minecraft.green_shulker_box": "Caja de shulker verde", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> te<PERSON> de verde", "block.minecraft.green_stained_glass_pane": "Panel de vidrio verde", "block.minecraft.green_terracotta": "Terracota verde", "block.minecraft.green_wool": "Lana verde", "block.minecraft.grindstone": "A<PERSON>lad<PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> co<PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> pesado", "block.minecraft.heavy_weighted_pressure_plate": "Placa de presión para peso elevado", "block.minecraft.honey_block": "Bloque de miel", "block.minecraft.honeycomb_block": "Bloque de panal", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Coral cuerno", "block.minecraft.horn_coral_block": "Bloque de coral cuerno", "block.minecraft.horn_coral_fan": "Gorgonia cuerno", "block.minecraft.horn_coral_wall_fan": "Gorgonia cuerno en pared", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Ladrillos de piedra cincelados infestados", "block.minecraft.infested_cobblestone": "Adoquín infestado", "block.minecraft.infested_cracked_stone_bricks": "Ladrillos de piedra agrietados infestados", "block.minecraft.infested_deepslate": "Pizarra abismal infestada", "block.minecraft.infested_mossy_stone_bricks": "Ladrillos de piedra musgosos infestados", "block.minecraft.infested_stone": "Piedra infestada", "block.minecraft.infested_stone_bricks": "Ladrillos de piedra infestados", "block.minecraft.iron_bars": "<PERSON><PERSON> de <PERSON>", "block.minecraft.iron_block": "Bloque de hi<PERSON>ro", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON> de <PERSON>ro", "block.minecraft.iron_trapdoor": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.jack_o_lantern": "Zapallo de Halloween", "block.minecraft.jigsaw": "Bloque r<PERSON>", "block.minecraft.jukebox": "Tocadiscos", "block.minecraft.jungle_button": "Botón de j<PERSON>", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence": "<PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON><PERSON> de valla de jungla", "block.minecraft.jungle_hanging_sign": "Cartel colgante de jungla", "block.minecraft.jungle_leaves": "Hojas de jungla", "block.minecraft.jungle_log": "Tronco de jungla", "block.minecraft.jungle_planks": "Tablas de j<PERSON>la", "block.minecraft.jungle_pressure_plate": "Placa de presión de jungla", "block.minecraft.jungle_sapling": "<PERSON><PERSON>", "block.minecraft.jungle_sign": "Cartel de jungla", "block.minecraft.jungle_slab": "<PERSON><PERSON>", "block.minecraft.jungle_stairs": "Escaleras de jungla", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "Cartel colgante de jungla de pared", "block.minecraft.jungle_wall_sign": "Cartel de jungla en pared", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "Tallo de alga", "block.minecraft.ladder": "Escalera de mano", "block.minecraft.lantern": "Farol", "block.minecraft.lapis_block": "Bloque de lapislázuli", "block.minecraft.lapis_ore": "Mineral de lapislázuli", "block.minecraft.large_amethyst_bud": "Brote de amatista grande", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON> alto", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Caldero con lava", "block.minecraft.leaf_litter": "Hojas secas", "block.minecraft.lectern": "Atril", "block.minecraft.lever": "Palanca", "block.minecraft.light": "Luz", "block.minecraft.light_blue_banner": "Estandarte celeste", "block.minecraft.light_blue_bed": "<PERSON><PERSON> celeste", "block.minecraft.light_blue_candle": "<PERSON><PERSON> celeste", "block.minecraft.light_blue_candle_cake": "Torta con vela celeste", "block.minecraft.light_blue_carpet": "Alfombra celeste", "block.minecraft.light_blue_concrete": "Hormigón celeste", "block.minecraft.light_blue_concrete_powder": "Cemento celeste", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> celeste", "block.minecraft.light_blue_shulker_box": "Caja de shulker celeste", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Panel de vidrio celeste", "block.minecraft.light_blue_terracotta": "Terracota celeste", "block.minecraft.light_blue_wool": "<PERSON> celeste", "block.minecraft.light_gray_banner": "Estandarte gris claro", "block.minecraft.light_gray_bed": "<PERSON>a gris claro", "block.minecraft.light_gray_candle": "<PERSON><PERSON> gris claro", "block.minecraft.light_gray_candle_cake": "<PERSON>ta con vela gris claro", "block.minecraft.light_gray_carpet": "Alfombra gris claro", "block.minecraft.light_gray_concrete": "Hormigón gris claro", "block.minecraft.light_gray_concrete_powder": "Cemento gris claro", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON>jo gris claro", "block.minecraft.light_gray_shulker_box": "Caja de shulker gris claro", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> te<PERSON> de gris claro", "block.minecraft.light_gray_stained_glass_pane": "Panel de vidrio gris claro", "block.minecraft.light_gray_terracotta": "Terracota gris claro", "block.minecraft.light_gray_wool": "<PERSON> gris claro", "block.minecraft.light_weighted_pressure_plate": "Placa de presión para peso liviano", "block.minecraft.lightning_rod": "Pararrayos", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "Lirio del valle", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Estandarte verde lima", "block.minecraft.lime_bed": "Cama verde lima", "block.minecraft.lime_candle": "Vela verde lima", "block.minecraft.lime_candle_cake": "Torta con vela verde lima", "block.minecraft.lime_carpet": "Alfombra verde lima", "block.minecraft.lime_concrete": "Hormigón verde lima", "block.minecraft.lime_concrete_powder": "Cemento verde lima", "block.minecraft.lime_glazed_terracotta": "Azulejo verde lima", "block.minecraft.lime_shulker_box": "Caja de shulker verde lima", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON> te<PERSON> de verde lima", "block.minecraft.lime_stained_glass_pane": "Panel de vidrio verde lima", "block.minecraft.lime_terracotta": "Terracota verde lima", "block.minecraft.lime_wool": "Lana verde lima", "block.minecraft.lodestone": "Ma<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Telar", "block.minecraft.magenta_banner": "Estandarte magenta", "block.minecraft.magenta_bed": "Cama magenta", "block.minecraft.magenta_candle": "Vela magenta", "block.minecraft.magenta_candle_cake": "Torta con vela magenta", "block.minecraft.magenta_carpet": "Alfombra magenta", "block.minecraft.magenta_concrete": "Hormigón magenta", "block.minecraft.magenta_concrete_powder": "Cemento magenta", "block.minecraft.magenta_glazed_terracotta": "Azulejo magenta", "block.minecraft.magenta_shulker_box": "Caja de shulker magenta", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON> te<PERSON> de magenta", "block.minecraft.magenta_stained_glass_pane": "Panel de vidrio magenta", "block.minecraft.magenta_terracotta": "Terracota magenta", "block.minecraft.magenta_wool": "Lana magenta", "block.minecraft.magma_block": "Bloque de magma", "block.minecraft.mangrove_button": "Botón de mangle", "block.minecraft.mangrove_door": "<PERSON>uerta de mangle", "block.minecraft.mangrove_fence": "Valla de mangle", "block.minecraft.mangrove_fence_gate": "Puerta de valla de mangle", "block.minecraft.mangrove_hanging_sign": "Cartel colgante de mangle", "block.minecraft.mangrove_leaves": "Hojas de mangle", "block.minecraft.mangrove_log": "Tronco de mangle", "block.minecraft.mangrove_planks": "Tablas de mangle", "block.minecraft.mangrove_pressure_plate": "Placa de presión de mangle", "block.minecraft.mangrove_propagule": "Propágulo de mangle", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_sign": "Cartel de mangle", "block.minecraft.mangrove_slab": "Losa de mangle", "block.minecraft.mangrove_stairs": "Escaleras de mangle", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_wall_hanging_sign": "Cartel colgante de mangle de pared", "block.minecraft.mangrove_wall_sign": "Cartel de mangle en pared", "block.minecraft.mangrove_wood": "<PERSON><PERSON> de mangle", "block.minecraft.medium_amethyst_bud": "Brote de amatista mediano", "block.minecraft.melon": "Sandía", "block.minecraft.melon_stem": "Tallo de sandía", "block.minecraft.moss_block": "Bloque de musgo", "block.minecraft.moss_carpet": "Capa de musgo", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.mossy_cobblestone_slab": "Losa de ado<PERSON><PERSON><PERSON> mus<PERSON>o", "block.minecraft.mossy_cobblestone_stairs": "Escaleras de adoquín musgoso", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> <PERSON> ad<PERSON><PERSON><PERSON> mus<PERSON>o", "block.minecraft.mossy_stone_brick_slab": "Losa de ladrillos de piedra musgosa", "block.minecraft.mossy_stone_brick_stairs": "Escaleras de ladrillos de piedra musgosa", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra musgosos", "block.minecraft.mossy_stone_bricks": "Ladrillos de piedra musgosos", "block.minecraft.moving_piston": "Pistón en movimiento", "block.minecraft.mud": "Barro", "block.minecraft.mud_brick_slab": "Losa de ladrillos de adobe", "block.minecraft.mud_brick_stairs": "Escaleras de ladrillos de adobe", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> de ladrillos de adobe", "block.minecraft.mud_bricks": "Ladrillos de adobe", "block.minecraft.muddy_mangrove_roots": "Raices de mangle con barro", "block.minecraft.mushroom_stem": "Tallo de callampa", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Valla de ladrillos del Nether", "block.minecraft.nether_brick_slab": "Losa de ladrillos del Nether", "block.minecraft.nether_brick_stairs": "Escaleras de ladrillos del Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> del Nether", "block.minecraft.nether_bricks": "Ladrillos del Nether", "block.minecraft.nether_gold_ore": "Mineral de oro del Nether", "block.minecraft.nether_portal": "Portal al Nether", "block.minecraft.nether_quartz_ore": "Mineral de cuarzo del Nether", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON> del nether", "block.minecraft.nether_wart": "Verrugas del Nether", "block.minecraft.nether_wart_block": "Bloque de verrugas del Nether", "block.minecraft.netherite_block": "Bloque de netherita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloque musical", "block.minecraft.oak_button": "Botón de roble", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.oak_fence": "<PERSON>la <PERSON> r<PERSON>", "block.minecraft.oak_fence_gate": "<PERSON>uer<PERSON> de valla de roble", "block.minecraft.oak_hanging_sign": "Cartel colgante de roble", "block.minecraft.oak_leaves": "Hojas de roble", "block.minecraft.oak_log": "Tronco de roble", "block.minecraft.oak_planks": "Tablas de roble", "block.minecraft.oak_pressure_plate": "Placa de presión de roble", "block.minecraft.oak_sapling": "<PERSON><PERSON>", "block.minecraft.oak_sign": "Cartel de roble", "block.minecraft.oak_slab": "Losa de roble", "block.minecraft.oak_stairs": "Escaleras de roble", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.oak_wall_hanging_sign": "Cartel colgante de roble de pared", "block.minecraft.oak_wall_sign": "Cartel de roble en pared", "block.minecraft.oak_wood": "<PERSON><PERSON>", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "<PERSON><PERSON>z ocre", "block.minecraft.ominous_banner": "Estandarte de mal presagio", "block.minecraft.open_eyeblossom": "Miraflor abierta", "block.minecraft.orange_banner": "Estandarte naranja", "block.minecraft.orange_bed": "<PERSON><PERSON> naranja", "block.minecraft.orange_candle": "<PERSON><PERSON> naranja", "block.minecraft.orange_candle_cake": "Torta con vela naranja", "block.minecraft.orange_carpet": "Alfombra naranja", "block.minecraft.orange_concrete": "Hormigón naranja", "block.minecraft.orange_concrete_powder": "Cemento naranja", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_shulker_box": "<PERSON>aja de shulker naranja", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panel de vidrio naranja", "block.minecraft.orange_terracotta": "Terracota naranja", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> na<PERSON>ja", "block.minecraft.orange_wool": "<PERSON>", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Cobre cincelado oxidado", "block.minecraft.oxidized_copper": "Cobre oxidado", "block.minecraft.oxidized_copper_bulb": "Lámpara de cobre oxidado", "block.minecraft.oxidized_copper_door": "Puerta de cobre oxidado", "block.minecraft.oxidized_copper_grate": "Rejilla de cobre oxidado", "block.minecraft.oxidized_copper_trapdoor": "Trampilla de cobre oxidado", "block.minecraft.oxidized_cut_copper": "Cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_slab": "Losa de cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_stairs": "Escaleras de cobre cortado oxidado", "block.minecraft.packed_ice": "Hielo compacto", "block.minecraft.packed_mud": "Adobe", "block.minecraft.pale_hanging_moss": "Musgo p<PERSON><PERSON>o co<PERSON>", "block.minecraft.pale_moss_block": "Bloque de musgo pálido", "block.minecraft.pale_moss_carpet": "Capa de musgo pálido", "block.minecraft.pale_oak_button": "Botón de roble pálido", "block.minecraft.pale_oak_door": "<PERSON><PERSON><PERSON> de roble p<PERSON>", "block.minecraft.pale_oak_fence": "Valla de roble p<PERSON>", "block.minecraft.pale_oak_fence_gate": "Puerta de valla de roble p<PERSON>o", "block.minecraft.pale_oak_hanging_sign": "Cartel colgante de roble pálido", "block.minecraft.pale_oak_leaves": "Hojas de roble pálido", "block.minecraft.pale_oak_log": "Tronco de roble pálido", "block.minecraft.pale_oak_planks": "Tablas de roble pálido", "block.minecraft.pale_oak_pressure_plate": "Placa de presión de roble pálido", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON> de roble <PERSON>", "block.minecraft.pale_oak_sign": "Cartel de roble pálido", "block.minecraft.pale_oak_slab": "Losa de roble pá<PERSON>o", "block.minecraft.pale_oak_stairs": "Escaleras de roble pálido", "block.minecraft.pale_oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de roble <PERSON>", "block.minecraft.pale_oak_wall_hanging_sign": "Cartel colgante de roble pálido de pared", "block.minecraft.pale_oak_wall_sign": "Cartel de roble pálido en pared", "block.minecraft.pale_oak_wood": "<PERSON><PERSON> de roble p<PERSON>", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON> na<PERSON>", "block.minecraft.peony": "Peonía", "block.minecraft.petrified_oak_slab": "Losa de roble petrificada", "block.minecraft.piglin_head": "Cabeza de piglin", "block.minecraft.piglin_wall_head": "Cabeza de piglin en pared", "block.minecraft.pink_banner": "E<PERSON><PERSON> rosado", "block.minecraft.pink_bed": "Cama rosada", "block.minecraft.pink_candle": "<PERSON><PERSON> rosada", "block.minecraft.pink_candle_cake": "Torta con vela rosada", "block.minecraft.pink_carpet": "Alfombra rosada", "block.minecraft.pink_concrete": "Hormigón rosado", "block.minecraft.pink_concrete_powder": "Cemento rosado", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> rosado", "block.minecraft.pink_petals": "Pé<PERSON><PERSON> rosados", "block.minecraft.pink_shulker_box": "Caja de shulker rosada", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> de rosado", "block.minecraft.pink_stained_glass_pane": "Panel de vidrio rosado", "block.minecraft.pink_terracotta": "Terracota rosada", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosado", "block.minecraft.pink_wool": "Lana rosada", "block.minecraft.piston": "Pistón", "block.minecraft.piston_head": "Cabeza de pistón", "block.minecraft.pitcher_crop": "Cultivo de planta jarra", "block.minecraft.pitcher_plant": "<PERSON>a jarra", "block.minecraft.player_head": "Cabeza de jugador", "block.minecraft.player_head.named": "Cabeza de %s", "block.minecraft.player_wall_head": "Cabeza de jugador en pared", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Espeleotema puntiagudo", "block.minecraft.polished_andesite": "<PERSON><PERSON> pulida", "block.minecraft.polished_andesite_slab": "Losa de andesita pulida", "block.minecraft.polished_andesite_stairs": "Escaleras de andesita pulida", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone": "Piedra negra pulida", "block.minecraft.polished_blackstone_brick_slab": "Losa de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_brick_stairs": "Escaleras de ladrillos piedra negra pulida", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_bricks": "Ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_button": "Botón de piedra negra pulida", "block.minecraft.polished_blackstone_pressure_plate": "Placa de presión de piedra negra pulida", "block.minecraft.polished_blackstone_slab": "Losa de piedra negra pulida", "block.minecraft.polished_blackstone_stairs": "Escaleras de piedra negra pulida", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> de piedra negra pulida", "block.minecraft.polished_deepslate": "Pi<PERSON>ra abismal pulida", "block.minecraft.polished_deepslate_slab": "Losa de pizarra abismal pulida", "block.minecraft.polished_deepslate_stairs": "Escaleras de pizarra abismal pulida", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> de pizarra abismal pulida", "block.minecraft.polished_diorite": "<PERSON><PERSON>ta pulida", "block.minecraft.polished_diorite_slab": "Losa de diorita pulida", "block.minecraft.polished_diorite_stairs": "Escaleras de diorita pulida", "block.minecraft.polished_granite": "<PERSON><PERSON>", "block.minecraft.polished_granite_slab": "Losa de granito pulido", "block.minecraft.polished_granite_stairs": "Escaleras de granito pulido", "block.minecraft.polished_tuff": "<PERSON><PERSON> pulida", "block.minecraft.polished_tuff_slab": "Losa de toba pulida", "block.minecraft.polished_tuff_stairs": "Escaleras de toba pulida", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON> de toba pulida", "block.minecraft.poppy": "Amapola", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "<PERSON><PERSON> con brote de acacia", "block.minecraft.potted_allium": "<PERSON><PERSON> con allium", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON> con azalea", "block.minecraft.potted_azure_bluet": "<PERSON><PERSON> con houstonia", "block.minecraft.potted_bamboo": "<PERSON><PERSON> con bambú", "block.minecraft.potted_birch_sapling": "<PERSON><PERSON> con brote de abedul", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON> con orquídea azul", "block.minecraft.potted_brown_mushroom": "Macetero con callampa café", "block.minecraft.potted_cactus": "Macetero con cactus", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON> con brote de cerezo", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON> con miraflor cerrada", "block.minecraft.potted_cornflower": "<PERSON><PERSON> con aciano", "block.minecraft.potted_crimson_fungus": "Hongo carmesí en macetero", "block.minecraft.potted_crimson_roots": "Raíces carmesí en macetero", "block.minecraft.potted_dandelion": "<PERSON><PERSON> con diente de león", "block.minecraft.potted_dark_oak_sapling": "<PERSON><PERSON> con brote de roble oscuro", "block.minecraft.potted_dead_bush": "<PERSON><PERSON> con arbusto seco", "block.minecraft.potted_fern": "<PERSON><PERSON> con helecho", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON> con azalea floreciente", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON> con brote de jungla", "block.minecraft.potted_lily_of_the_valley": "Mace<PERSON> con lirio de los valles", "block.minecraft.potted_mangrove_propagule": "<PERSON><PERSON> con propágulo de mangle", "block.minecraft.potted_oak_sapling": "<PERSON><PERSON> con brote de roble", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON> con miraflor abierta", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON> con tulipán naranja", "block.minecraft.potted_oxeye_daisy": "<PERSON><PERSON> con margarita", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON> con brote de roble pálido", "block.minecraft.potted_pink_tulip": "<PERSON><PERSON> con tulipán rosado", "block.minecraft.potted_poppy": "<PERSON><PERSON> con amapola", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON> con callampa roja", "block.minecraft.potted_red_tulip": "<PERSON><PERSON> con tulipán rojo", "block.minecraft.potted_spruce_sapling": "<PERSON><PERSON> con brote de abeto", "block.minecraft.potted_torchflower": "<PERSON><PERSON> con anflorcha", "block.minecraft.potted_warped_fungus": "Hongo distorsionado en macetero", "block.minecraft.potted_warped_roots": "Raíces distorsionadas en macetero", "block.minecraft.potted_white_tulip": "<PERSON><PERSON> con tulipán blanco", "block.minecraft.potted_wither_rose": "<PERSON><PERSON> con rosa <PERSON>", "block.minecraft.powder_snow": "Nieve polvo", "block.minecraft.powder_snow_cauldron": "Caldero con nieve polvo", "block.minecraft.powered_rail": "<PERSON><PERSON> prop<PERSON>", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Losa de ladrillos de prismarina", "block.minecraft.prismarine_brick_stairs": "Escaleras de ladrillos de prismarina", "block.minecraft.prismarine_bricks": "Ladrillos de prismarina", "block.minecraft.prismarine_slab": "Losa de prismarina", "block.minecraft.prismarine_stairs": "Escaleras de prismarina", "block.minecraft.prismarine_wall": "<PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Tallo de <PERSON>llo", "block.minecraft.purple_banner": "Estandarte morado", "block.minecraft.purple_bed": "Cama morada", "block.minecraft.purple_candle": "<PERSON><PERSON> morada", "block.minecraft.purple_candle_cake": "Torta con vela morada", "block.minecraft.purple_carpet": "Alfombra morada", "block.minecraft.purple_concrete": "Hormigón morado", "block.minecraft.purple_concrete_powder": "Cemento morado", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> morado", "block.minecraft.purple_shulker_box": "Caja de shulker morada", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> de morado", "block.minecraft.purple_stained_glass_pane": "Panel de vidrio morado", "block.minecraft.purple_terracotta": "Terracota morada", "block.minecraft.purple_wool": "<PERSON> mora<PERSON>", "block.minecraft.purpur_block": "Bloque de púrpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON>", "block.minecraft.purpur_slab": "Losa de púrpur", "block.minecraft.purpur_stairs": "Escaleras de púrpur", "block.minecraft.quartz_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_bricks": "Ladrillos de cuarzo", "block.minecraft.quartz_pillar": "<PERSON><PERSON>", "block.minecraft.quartz_slab": "Losa de cuarzo", "block.minecraft.quartz_stairs": "Escaleras de cuarzo", "block.minecraft.rail": "Riel", "block.minecraft.raw_copper_block": "Bloque de cobre en bruto", "block.minecraft.raw_gold_block": "Bloque de oro en bruto", "block.minecraft.raw_iron_block": "Bloque de hierro en bruto", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_bed": "Cama roja", "block.minecraft.red_candle": "<PERSON><PERSON> roja", "block.minecraft.red_candle_cake": "Torta con vela roja", "block.minecraft.red_carpet": "Alfombra roja", "block.minecraft.red_concrete": "Hormigón rojo", "block.minecraft.red_concrete_powder": "Cemento rojo", "block.minecraft.red_glazed_terracotta": "A<PERSON><PERSON>jo rojo", "block.minecraft.red_mushroom": "Callampa roja", "block.minecraft.red_mushroom_block": "Bloque de callampa rojo", "block.minecraft.red_nether_brick_slab": "Losa de ladrillos del Nether rojos", "block.minecraft.red_nether_brick_stairs": "Escaleras de ladrillos del Nether rojos", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> de ladrillos del Nether rojos", "block.minecraft.red_nether_bricks": "Ladrillos del Nether rojos", "block.minecraft.red_sand": "Arena roja", "block.minecraft.red_sandstone": "Arenisca roja", "block.minecraft.red_sandstone_slab": "Losa de arenisca roja", "block.minecraft.red_sandstone_stairs": "Escaleras de arenisca roja", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> de arenisca roja", "block.minecraft.red_shulker_box": "Caja de shulker roja", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> de rojo", "block.minecraft.red_stained_glass_pane": "Panel de vidrio rojo", "block.minecraft.red_terracotta": "Terracota roja", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_wool": "<PERSON>", "block.minecraft.redstone_block": "Bloque de redstone", "block.minecraft.redstone_lamp": "Lámpara de redstone", "block.minecraft.redstone_ore": "Mineral de redstone", "block.minecraft.redstone_torch": "Antorcha de redstone", "block.minecraft.redstone_wall_torch": "<PERSON><PERSON><PERSON> de redstone en pared", "block.minecraft.redstone_wire": "Cable de redstone", "block.minecraft.reinforced_deepslate": "Pizarra abismal reforzada", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Bloque de comandos de repetición", "block.minecraft.resin_block": "Bloque de resina", "block.minecraft.resin_brick_slab": "Losa de ladrillos de resina", "block.minecraft.resin_brick_stairs": "Escaleras de ladrillos de resina", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> de ladrillos de resina", "block.minecraft.resin_bricks": "Ladrillos de resina", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "Nexo de reaparición", "block.minecraft.rooted_dirt": "<PERSON>ra enraizada", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Arena", "block.minecraft.sandstone": "Arenisca", "block.minecraft.sandstone_slab": "Losa de arenisca", "block.minecraft.sandstone_stairs": "Escaleras de arenisca", "block.minecraft.sandstone_wall": "<PERSON><PERSON>", "block.minecraft.scaffolding": "Andamio", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador de sculk", "block.minecraft.sculk_sensor": "Sensor de sculk", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON> de sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Linterna marina", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Planta marina", "block.minecraft.set_spawn": "Punto de reaparición establecido", "block.minecraft.short_dry_grass": "Pasto corto seco", "block.minecraft.short_grass": "Pasto corto", "block.minecraft.shroomlight": "Callampiluz", "block.minecraft.shulker_box": "<PERSON><PERSON> de <PERSON>ker", "block.minecraft.skeleton_skull": "Calavera de esqueleto", "block.minecraft.skeleton_wall_skull": "Calavera de esqueleto en pared", "block.minecraft.slime_block": "Bloque de slime", "block.minecraft.small_amethyst_bud": "Brote de amatista pequeño", "block.minecraft.small_dripleaf": "Plantaforma pequeña", "block.minecraft.smithing_table": "Mesa de herrería", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Ba<PERSON>to liso", "block.minecraft.smooth_quartz": "Bloque de cuarzo liso", "block.minecraft.smooth_quartz_slab": "Losa de cuarzo liso", "block.minecraft.smooth_quartz_stairs": "Escaleras de cuarzo liso", "block.minecraft.smooth_red_sandstone": "Arenisca roja lisa", "block.minecraft.smooth_red_sandstone_slab": "Losa de arenisca roja lisa", "block.minecraft.smooth_red_sandstone_stairs": "Escaleras de arenisca roja lisa", "block.minecraft.smooth_sandstone": "Arenisca lisa", "block.minecraft.smooth_sandstone_slab": "Losa de arenisca lisa", "block.minecraft.smooth_sandstone_stairs": "Escaleras de arenisca lisa", "block.minecraft.smooth_stone": "<PERSON><PERSON> lisa", "block.minecraft.smooth_stone_slab": "Losa de piedra lisa", "block.minecraft.sniffer_egg": "<PERSON><PERSON> de sniffer", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Bloque de nieve", "block.minecraft.soul_campfire": "Fogata de almas", "block.minecraft.soul_fire": "Fuego de almas", "block.minecraft.soul_lantern": "Farol de almas", "block.minecraft.soul_sand": "Arena de almas", "block.minecraft.soul_soil": "Tierra de almas", "block.minecraft.soul_torch": "An<PERSON><PERSON> de almas", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON> de almas en pared", "block.minecraft.spawn.not_valid": "No tienes una cama o nexo de reaparición cargado, o alguno está obstruido", "block.minecraft.spawner": "Spawner", "block.minecraft.spawner.desc1": "Interactúa con un huevo generador:", "block.minecraft.spawner.desc2": "Establece el tipo de mob", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Flor de esporas", "block.minecraft.spruce_button": "Botón de abeto", "block.minecraft.spruce_door": "Puerta de abeto", "block.minecraft.spruce_fence": "Valla de abeto", "block.minecraft.spruce_fence_gate": "Puerta de valla de abeto", "block.minecraft.spruce_hanging_sign": "Cartel colgante de abeto", "block.minecraft.spruce_leaves": "Hojas de abeto", "block.minecraft.spruce_log": "Tronco de abeto", "block.minecraft.spruce_planks": "Tablas de abeto", "block.minecraft.spruce_pressure_plate": "Placa de presión de abeto", "block.minecraft.spruce_sapling": "<PERSON><PERSON>", "block.minecraft.spruce_sign": "Cartel de abeto", "block.minecraft.spruce_slab": "Losa de abeto", "block.minecraft.spruce_stairs": "Escaleras de abeto", "block.minecraft.spruce_trapdoor": "<PERSON><PERSON><PERSON><PERSON> de a<PERSON>o", "block.minecraft.spruce_wall_hanging_sign": "Cartel colgante de abeto de pared", "block.minecraft.spruce_wall_sign": "Cartel de abeto en pared", "block.minecraft.spruce_wood": "<PERSON>ra de a<PERSON>o", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON> pegajoso", "block.minecraft.stone": "Piedra", "block.minecraft.stone_brick_slab": "Losa de ladrillos de piedra", "block.minecraft.stone_brick_stairs": "Escaleras de ladrillos de piedra", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra", "block.minecraft.stone_bricks": "Ladrillos de piedra", "block.minecraft.stone_button": "Botón de piedra", "block.minecraft.stone_pressure_plate": "Placa de presión de piedra", "block.minecraft.stone_slab": "Losa de piedra", "block.minecraft.stone_stairs": "Escaleras de piedra", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Tronco de acacia sin corteza", "block.minecraft.stripped_acacia_wood": "Madera de acacia sin corteza", "block.minecraft.stripped_bamboo_block": "Bloque de bambú sin corteza", "block.minecraft.stripped_birch_log": "Tronco de abedul sin corteza", "block.minecraft.stripped_birch_wood": "Madera de abedul sin corteza", "block.minecraft.stripped_cherry_log": "Tronco de cerezo sin corteza", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON> de cerezo sin corteza", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON> pela<PERSON>", "block.minecraft.stripped_crimson_stem": "Tallo carmesí pelado", "block.minecraft.stripped_dark_oak_log": "Tronco de roble oscuro sin corteza", "block.minecraft.stripped_dark_oak_wood": "Madera de roble oscuro sin corteza", "block.minecraft.stripped_jungle_log": "Tronco de jungla sin corteza", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON> de jungla sin corteza", "block.minecraft.stripped_mangrove_log": "Tronco de mangle sin corteza", "block.minecraft.stripped_mangrove_wood": "<PERSON>ra de mangle sin corteza", "block.minecraft.stripped_oak_log": "Tronco de roble sin corteza", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON> de roble sin corteza", "block.minecraft.stripped_pale_oak_log": "Tronco de roble pálido sin corteza", "block.minecraft.stripped_pale_oak_wood": "<PERSON>ra de roble pálido sin corteza", "block.minecraft.stripped_spruce_log": "Tronco de abeto sin corteza", "block.minecraft.stripped_spruce_wood": "Madera de abeto sin corteza", "block.minecraft.stripped_warped_hyphae": "Hifas distorsionadas peladas", "block.minecraft.stripped_warped_stem": "Tallo distorsionado pelado", "block.minecraft.structure_block": "Bloque estructural", "block.minecraft.structure_void": "Vacío estructural", "block.minecraft.sugar_cane": "Caña de azúcar", "block.minecraft.sunflower": "Girasol", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON> sospechosa", "block.minecraft.suspicious_sand": "Arena sospechosa", "block.minecraft.sweet_berry_bush": "Arbusto de bayas dulces", "block.minecraft.tall_dry_grass": "Pasto alto seco", "block.minecraft.tall_grass": "Pasto alto", "block.minecraft.tall_seagrass": "Planta marina alta", "block.minecraft.target": "<PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "Bloque de testeo", "block.minecraft.test_instance_block": "Bloque de prueba de instancias", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON> opaco", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Explosiones de TNT desactivadas", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "An<PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Cultivo de anflorcha", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON> trampa", "block.minecraft.trial_spawner": "Spawner de desafío", "block.minecraft.tripwire": "Hilo de activación", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "Coral del tubo", "block.minecraft.tube_coral_block": "Bloque de coral del tubo", "block.minecraft.tube_coral_fan": "Gorgonia del tubo", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON> de tubo en pared", "block.minecraft.tuff": "Toba", "block.minecraft.tuff_brick_slab": "Losa de ladrillos de toba", "block.minecraft.tuff_brick_stairs": "Escaleras de ladrillos de toba", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> de ladrill<PERSON> de toba", "block.minecraft.tuff_bricks": "Ladrillos de toba", "block.minecraft.tuff_slab": "Losa de toba", "block.minecraft.tuff_stairs": "Escaleras de toba", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Huevo de tortuga", "block.minecraft.twisting_vines": "Enredaderas retorcidas", "block.minecraft.twisting_vines_plant": "Enredaderas retorcidas", "block.minecraft.vault": "Bóveda", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> verdo<PERSON>", "block.minecraft.vine": "Enredadera", "block.minecraft.void_air": "Vacío", "block.minecraft.wall_torch": "An<PERSON><PERSON> en pared", "block.minecraft.warped_button": "Botón distorsionado", "block.minecraft.warped_door": "Puerta distorsionada", "block.minecraft.warped_fence": "Valla distorsionada", "block.minecraft.warped_fence_gate": "Puerta de valla distorsionada", "block.minecraft.warped_fungus": "Hongo distorsionado", "block.minecraft.warped_hanging_sign": "Cartel colgante distorsionado", "block.minecraft.warped_hyphae": "Hifas distorsionadas", "block.minecraft.warped_nylium": "<PERSON>lio distorsionado", "block.minecraft.warped_planks": "Tablas distorsionadas", "block.minecraft.warped_pressure_plate": "Placa de presión distorsionada", "block.minecraft.warped_roots": "Raíces distorsionadas", "block.minecraft.warped_sign": "Cartel distorsionado", "block.minecraft.warped_slab": "Losa distorsionada", "block.minecraft.warped_stairs": "Escaleras distorsionadas", "block.minecraft.warped_stem": "Tallo distorsionado", "block.minecraft.warped_trapdoor": "Trampilla distorsionada", "block.minecraft.warped_wall_hanging_sign": "Cartel colgante distorsionado de pared", "block.minecraft.warped_wall_sign": "Letrero distorsionado en pared", "block.minecraft.warped_wart_block": "Bloque de verrugas distorsionadas", "block.minecraft.water": "Agua", "block.minecraft.water_cauldron": "Caldero con agua", "block.minecraft.waxed_chiseled_copper": "Cobre cincelado encerado", "block.minecraft.waxed_copper_block": "Bloque de cobre encerado", "block.minecraft.waxed_copper_bulb": "Lámpara de cobre encerado", "block.minecraft.waxed_copper_door": "Puerta de cobre encerado", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON> de cobre encerado", "block.minecraft.waxed_copper_trapdoor": "Trampilla de cobre encerado", "block.minecraft.waxed_cut_copper": "Cobre cortado encerado", "block.minecraft.waxed_cut_copper_slab": "Losa de cobre cortado encerado", "block.minecraft.waxed_cut_copper_stairs": "Escaleras de cobre cortado encerado", "block.minecraft.waxed_exposed_chiseled_copper": "Cobre cincelado expuesto encerado", "block.minecraft.waxed_exposed_copper": "Cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_bulb": "Lámpara de cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_door": "Puerta de cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_grate": "Rejilla de cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_trapdoor": "Trampilla de cobre expuesto encerado", "block.minecraft.waxed_exposed_cut_copper": "Cobre cortado expuesto encerado", "block.minecraft.waxed_exposed_cut_copper_slab": "Losa de cobre cortado expuesto encerado", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escaleras de cobre cortado expuesto encerado", "block.minecraft.waxed_oxidized_chiseled_copper": "Cobre cincelado oxidado encerado", "block.minecraft.waxed_oxidized_copper": "Cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_bulb": "Lámpara de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_door": "Puerta de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_grate": "Rejilla de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_trapdoor": "Trampilla de cobre oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper": "Cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_slab": "Losa de cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escaleras de cobre cortado oxidado encerado", "block.minecraft.waxed_weathered_chiseled_copper": "Cobre cincelado degradado encerado", "block.minecraft.waxed_weathered_copper": "Cobre degradado encerado", "block.minecraft.waxed_weathered_copper_bulb": "Lámpara de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_door": "Puerta de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_grate": "Rejilla de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_trapdoor": "Trampilla de cobre degradado encerado", "block.minecraft.waxed_weathered_cut_copper": "Cobre cortado degradado encerado", "block.minecraft.waxed_weathered_cut_copper_slab": "Losa de cobre cortado degradado encerado", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escaleras de cobre cortado degradado encerado", "block.minecraft.weathered_chiseled_copper": "Cobre cincelado degradado", "block.minecraft.weathered_copper": "Cobre degradado", "block.minecraft.weathered_copper_bulb": "Lámpara de cobre degradado", "block.minecraft.weathered_copper_door": "Puerta de cobre degradado", "block.minecraft.weathered_copper_grate": "Rejilla de cobre degradado", "block.minecraft.weathered_copper_trapdoor": "Trampilla de cobre degradado", "block.minecraft.weathered_cut_copper": "Cobre cortado degradado", "block.minecraft.weathered_cut_copper_slab": "Losa de cobre cortado degradado", "block.minecraft.weathered_cut_copper_stairs": "Escaleras de cobre cortado degradado", "block.minecraft.weeping_vines": "Enredaderas lloronas", "block.minecraft.weeping_vines_plant": "Enredaderas lloronas", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON><PERSON> moja<PERSON>", "block.minecraft.wheat": "Cultivo de trigo", "block.minecraft.white_banner": "Estandarte blanco", "block.minecraft.white_bed": "Cama blanca", "block.minecraft.white_candle": "<PERSON>ela blanca", "block.minecraft.white_candle_cake": "Torta con vela blanca", "block.minecraft.white_carpet": "Alfombra blanca", "block.minecraft.white_concrete": "Hormigón blanco", "block.minecraft.white_concrete_powder": "Cemento blanco", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.white_shulker_box": "Caja de shulker blanca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON>lanco", "block.minecraft.white_stained_glass_pane": "Panel de vidrio blanco", "block.minecraft.white_terracotta": "Terracota blanca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.white_wool": "<PERSON> blan<PERSON>", "block.minecraft.wildflowers": "<PERSON> silvestres", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Calavera de esqueleto <PERSON>", "block.minecraft.wither_skeleton_wall_skull": "Calavera de esqueleto <PERSON> en pared", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON> amarilla", "block.minecraft.yellow_candle": "<PERSON><PERSON> amarilla", "block.minecraft.yellow_candle_cake": "Torta con vela amarilla", "block.minecraft.yellow_carpet": "Alfombra amarilla", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON><PERSON> amarillo", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON> de shulker amarilla", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Panel de vidrio amarillo", "block.minecraft.yellow_terracotta": "Terracota amarilla", "block.minecraft.yellow_wool": "<PERSON>", "block.minecraft.zombie_head": "Cabeza de zombi", "block.minecraft.zombie_wall_head": "Cabeza de zombi en pared", "book.byAuthor": "escrito por %1$s", "book.edit.title": "Pantalla para Editar el Libro", "book.editTitle": "Título del libro:", "book.finalizeButton": "<PERSON><PERSON><PERSON> y cerrar", "book.finalizeWarning": "¡Atención! Si lo firmas no podrás editarlo.", "book.generation.0": "Original", "book.generation.1": "Copia del original", "book.generation.2": "Copia de una copia", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Etiqueta del libro no válida *", "book.pageIndicator": "Página %1$s de %2$s", "book.page_button.next": "Siguiente página", "book.page_button.previous": "Página anterior", "book.sign.title": "Pantalla para Firmar el Libro", "book.sign.titlebox": "<PERSON><PERSON><PERSON><PERSON>", "book.signButton": "<PERSON><PERSON><PERSON>", "book.view.title": "Pantalla de Vista del Libro", "build.tooHigh": "El límite de altura para construir es de %s bloques", "chat.cannotSend": "No se pudo enviar el mensaje", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Haz click para teletransportarte", "chat.copy": "Copiar link", "chat.copy.click": "Haz click para copiar al portapapeles", "chat.deleted_marker": "Este mensaje ha sido borrado por el server.", "chat.disabled.chain_broken": "El chat se ha deshabilitado debido a una cadena rota. Por favor, intenta reconectarte.", "chat.disabled.expiredProfileKey": "Se desactivó el chat debido a que expiró la clave pública del perfil. Por favor, intenta reconectarte.", "chat.disabled.invalid_command_signature": "El comando tuvo firmas de argumento de comando inesperadas o faltantes.", "chat.disabled.invalid_signature": "El chat tuvo una firma inválida. Por favor, vuelve a conectarte.", "chat.disabled.launcher": "Chat desactivado en las opciones del launcher. No se pudo enviar el mensaje.", "chat.disabled.missingProfileKey": "Se desactivó el chat debido a que no se encontró la clave pública del perfil. Por favor, intenta reconectarte.", "chat.disabled.options": "Chat desactivado en las opciones del cliente.", "chat.disabled.out_of_order_chat": "El chat se recibió desordenado. ¿Se cambió la hora del sistema?", "chat.disabled.profile": "Chat desactivado en los ajustes de la cuenta. Presiona '%s' de nuevo para más información.", "chat.disabled.profile.moreInfo": "Chat desactivado en los ajustes de la cuenta. No se pueden enviar ni ver mensajes.", "chat.editBox": "chat", "chat.filtered": "Filtrado por el server.", "chat.filtered_full": "El server ocultó tu mensaje a algunos jugadores.", "chat.link.confirm": "¿Seguro que quieres abrir esta página web?", "chat.link.confirmTrusted": "¿Quieres abrir este link o copiarlo?", "chat.link.open": "Abrir link", "chat.link.warning": "¡Nunca abras links de personas en las que no confíes!", "chat.queue": "[+%s línea(s) pendiente(s)]", "chat.square_brackets": "[%s]", "chat.tag.error": "El servidor envió un mensaje inválido.", "chat.tag.modified": "Mensaje modificado por el server. Original:", "chat.tag.not_secure": "Mensaje no verificado. No se puede reportar.", "chat.tag.system": "Mensaje del server. No puede ser reportado.", "chat.tag.system_single_player": "Mensaje del server.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ha completado el desafío %s", "chat.type.advancement.goal": "%s ha alcanzado el objetivo %s", "chat.type.advancement.task": "%s ha conseguido el logro %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Enviar mensaje al equipo", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s dice: %s", "chat.validation_error": "Error al validar el chat", "chat_screen.message": "Mensaje escrito: %s", "chat_screen.title": "<PERSON><PERSON><PERSON>", "chat_screen.usage": "Escribe un mensaje y presiona Enter para enviarlo", "chunk.toast.checkLog": "Ver registro para más detalles", "chunk.toast.loadFailure": "Error al cargar chunk en %s", "chunk.toast.lowDiskSpace": "¡Almacenamiento insuficiente!", "chunk.toast.lowDiskSpace.description": "Es posible que no se pueda guardar el mundo.", "chunk.toast.saveFailure": "Error al guardar chunk en %s", "clear.failed.multiple": "No se han encontrado objetos en el inventario de %s jugadores", "clear.failed.single": "No se han encontrado objetos en el inventario de %s", "color.minecraft.black": "Negro", "color.minecraft.blue": "Azul", "color.minecraft.brown": "Café", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verde", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON>", "color.minecraft.lime": "Lima", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "Amarillo", "command.context.here": "<--[AQUÍ]", "command.context.parse_error": "%s en la posición %s: %s", "command.exception": "No se puede analizar el comando: %s", "command.expected.separator": "Se requiere un espacio en blanco para concluir un argumento. Algunos datos están escritos sin separar", "command.failed": "Se produjo un error inesperado al intentar ejecutar ese comando", "command.forkLimit": "Se ha alcanzado el número máximo de contextos (%s)", "command.unknown.argument": "El argumento del comando es incorrecto", "command.unknown.command": "Comando desconocido o incompleto, se adjunta el error abajo", "commands.advancement.criterionNotFound": "El progreso %1$s no contiene el criterio \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "No se pudo conceder el criterio \"%s\" del progreso %s a %s jugadores porque ya lo tiene", "commands.advancement.grant.criterion.to.many.success": "Se concedió el criterio \"%s\" del progreso %s a %s jugadores", "commands.advancement.grant.criterion.to.one.failure": "No se pudo conceder el criterio \"%s\" del progreso %s a %s porque ya lo tiene", "commands.advancement.grant.criterion.to.one.success": "Se concedió el criterio \"%s\" del progreso %s a %s", "commands.advancement.grant.many.to.many.failure": "No se pudieron conceder %s progresos a %s jugadores porque ya los tienen", "commands.advancement.grant.many.to.many.success": "Se dieron %s progresos a %s jugadores", "commands.advancement.grant.many.to.one.failure": "No se pudieron conceder %s progresos a %s porque ya los tienen", "commands.advancement.grant.many.to.one.success": "Se concedieron %s progresos a %s", "commands.advancement.grant.one.to.many.failure": "No se pudo conceder el progreso %s a %s jugadores porque ya lo tienen", "commands.advancement.grant.one.to.many.success": "Se concedió el progreso %s a %s jugadores", "commands.advancement.grant.one.to.one.failure": "No se pudo conceder el progreso %s a %s porque ya lo tiene", "commands.advancement.grant.one.to.one.success": "Se ha concedido el progreso %s a %s", "commands.advancement.revoke.criterion.to.many.failure": "No se les pudo borrar el criterio \"%s\" del progreso %s a %s jugadores ya que no lo tenian", "commands.advancement.revoke.criterion.to.many.success": "Se les borró el criterio \"%s\" del progreso %s a %s jugadores", "commands.advancement.revoke.criterion.to.one.failure": "No se le pudo borrar el criterio \"%s\" del progreso %s a %s porque no lo tiene", "commands.advancement.revoke.criterion.to.one.success": "Se le borró el criterio \"%s\" del progreso %s a %s", "commands.advancement.revoke.many.to.many.failure": "No se les pudieron borrar %s progresos a %s jugadores ya que no los tenian", "commands.advancement.revoke.many.to.many.success": "Se les borraron %s progresos a %s jugadores", "commands.advancement.revoke.many.to.one.failure": "No se le pudieron borrar %s progresos a %s ya que no los tenian", "commands.advancement.revoke.many.to.one.success": "Se borraron %s progresos de %s", "commands.advancement.revoke.one.to.many.failure": "No se les pudo borrar el progreso %s a %s jugadores porque no lo tienen", "commands.advancement.revoke.one.to.many.success": "Se les borró el progreso %s a %s jugadores", "commands.advancement.revoke.one.to.one.failure": "No se le pudo borrar el progreso %s a %s porque no lo tiene", "commands.advancement.revoke.one.to.one.success": "Se le borró el progreso %s a %s", "commands.attribute.base_value.get.success": "El valor base del atributo %s para la entidad %s es %s", "commands.attribute.base_value.reset.success": "El valor base del atributo %s para la entidad %s se estableció a su valor predeterminado %s", "commands.attribute.base_value.set.success": "El valor base para el atributo %s para la entidad %s se fijó a %s", "commands.attribute.failed.entity": "%s no es una entidad válida para este comando", "commands.attribute.failed.modifier_already_present": "El modificador %s ya está presente en el atributo %s para la entidad %s", "commands.attribute.failed.no_attribute": "La entidad %s no posee el atributo %s", "commands.attribute.failed.no_modifier": "El atributo %s de la entidad %s no posee el modificador %s", "commands.attribute.modifier.add.success": "Añadido el modificador %s al atributo %s para la entidad %s", "commands.attribute.modifier.remove.success": "Eliminado el modificador %s del atributo %s para la entidad %s", "commands.attribute.modifier.value.get.success": "El valor del modificador %s en el atributo %s para la entidad %s es %s", "commands.attribute.value.get.success": "El valor del atributo %s para la entidad %s es %s", "commands.ban.failed": "Nada cambió, el jugador ya estaba baneado", "commands.ban.success": "Se ha baneado a %s: %s", "commands.banip.failed": "Nada cambió, la IP ya estaba baneada", "commands.banip.info": "Este ban afecta a %s jugador(es): %s", "commands.banip.invalid": "IP no válida o jugador desconocido", "commands.banip.success": "Se baneó la IP %s: %s", "commands.banlist.entry": "%s fue baneado/a por %s: %s", "commands.banlist.entry.unknown": "(Desconocida)", "commands.banlist.list": "Hay %s ban(s):", "commands.banlist.none": "No hay nadie baneado", "commands.bossbar.create.failed": "Ya existe una barra de jefe con el ID \"%s\"", "commands.bossbar.create.success": "Se ha creado la barra de jefe \"%s\"", "commands.bossbar.get.max": "La barra de jefe \"%s\" tiene un valor máximo de %s", "commands.bossbar.get.players.none": "La barra de jefe \"%s\" no tiene jugadores conectados", "commands.bossbar.get.players.some": "La barra de jefe %s tiene %s jugador(es) en línea: %s", "commands.bossbar.get.value": "La barra de jefe \"%s\" tiene un valor de %s", "commands.bossbar.get.visible.hidden": "La barra de jefe \"%s\" está oculta", "commands.bossbar.get.visible.visible": "La barra de jefe \"%s\" está visible", "commands.bossbar.list.bars.none": "No hay ninguna barra de jefe activa", "commands.bossbar.list.bars.some": "Hay %s barra(s) de jefe activa(s): %s", "commands.bossbar.remove.success": "Se ha eliminado la barra de jefe \"%s\"", "commands.bossbar.set.color.success": "Se ha recoloreado la barra de jefe \"%s\"", "commands.bossbar.set.color.unchanged": "Nada cambió, ese ya era el color de esta barra de jefe", "commands.bossbar.set.max.success": "Se ha cambiado el máximo de la barra de jefe de \"%s\" a %s", "commands.bossbar.set.max.unchanged": "Nada cambió, ese ya era el máximo de esta barra de jefe", "commands.bossbar.set.name.success": "Se ha renombrado la barra de jefe \"%s\"", "commands.bossbar.set.name.unchanged": "Nada cambi<PERSON>, ese ya era el nombre de esta barra de jefe", "commands.bossbar.set.players.success.none": "La barra de jefe \"%s\" no tiene jugadores asociados", "commands.bossbar.set.players.success.some": "La barra de jefe %s ahora tiene %s jugador(es): %s", "commands.bossbar.set.players.unchanged": "Nada camb<PERSON>, esos jugadores ya están en la barra de jefe sin nadie para agregar o quitar", "commands.bossbar.set.style.success": "Se ha cambiado el estilo de la barra de jefe \"%s\"", "commands.bossbar.set.style.unchanged": "Nada cambió, ese ya era el estilo de esta barra de jefe", "commands.bossbar.set.value.success": "Se ha cambiado el valor de la barra de jefe de \"%s\" a %s", "commands.bossbar.set.value.unchanged": "Nada cambi<PERSON>, ese ya era el valor de esta barra de jefe", "commands.bossbar.set.visibility.unchanged.hidden": "Nada cambió, la barra de jefe ya estaba oculta", "commands.bossbar.set.visibility.unchanged.visible": "Nada cambió, la barra de jefe ya era visible", "commands.bossbar.set.visible.success.hidden": "La barra de jefe \"%s\" está ahora oculta", "commands.bossbar.set.visible.success.visible": "La barra de jefe \"%s\" es ahora visible", "commands.bossbar.unknown": "No existe ninguna barra de jefe con el ID \"%s\"", "commands.clear.success.multiple": "Se eliminaron %s ítem(s) de %s jugadores", "commands.clear.success.single": "Se eliminaron %s ítem(s) del jugador %s", "commands.clear.test.multiple": "Se encontraron %s ítem(s) coincidentes en %s jugadores", "commands.clear.test.single": "Se encontraron %s ítem(s) coincidentes en el jugador %s", "commands.clone.failed": "No se clonó ningún bloque", "commands.clone.overlap": "El área de origen y el área de destino no pueden coincidir", "commands.clone.success": "Se clonaron %s bloque(s)", "commands.clone.toobig": "Hay demasiados bloques en el área especificada (máximo %s, especificados: %s)", "commands.damage.invulnerable": "El objetivo es invulnerable al tipo de daño especificado", "commands.damage.success": "Se aplicó %s de daño a %s", "commands.data.block.get": "%s en el bloque en %s, %s, %s, después de la escala de %s ha cambiado a %s", "commands.data.block.invalid": "El bloque especificado no es una entidad-bloque", "commands.data.block.modified": "Se modificaron los datos del bloque en %s, %s, %s", "commands.data.block.query": "El bloque en %s, %s, %s contiene los siguientes datos: %s", "commands.data.entity.get": "%s de %s, después de un factor escala de %s, ha cambiado a %s", "commands.data.entity.invalid": "No se pudieron modificar los datos del jugador", "commands.data.entity.modified": "Se modificaron los datos de la entidad %s", "commands.data.entity.query": "La entidad %s tiene los siguientes datos: %s", "commands.data.get.invalid": "No se pudieron obtener %s, solo se permiten etiquetas numéricas", "commands.data.get.multiple": "Este argumento solo acepta un valor NBT", "commands.data.get.unknown": "No se pudo obtener %s, la etiqueta no existe", "commands.data.merge.failed": "Nada cambió. Las propiedades especificadas ya tienen esos valores", "commands.data.modify.expected_list": "Se requiere lista, obtenido: %s", "commands.data.modify.expected_object": "Se requiere objeto, obtenido: %s", "commands.data.modify.expected_value": "Se esperaba un valor, se obtuvo: %s", "commands.data.modify.invalid_index": "Índice de lista no válido: %s", "commands.data.modify.invalid_substring": "Índices de subcadena inválidos: de %s a %s", "commands.data.storage.get": "%s guardados %s tras un factor de escalado de %s son %s", "commands.data.storage.modified": "Se ha modificado el contenedor %s", "commands.data.storage.query": "El contenedor %s tiene lo siguiente: %s", "commands.datapack.create.already_exists": "Ya hay un paquete con ese nombre: '%s'", "commands.datapack.create.invalid_full_name": "Nombre inválido para el nuevo paquete '%s'", "commands.datapack.create.invalid_name": "Caracteres inválidos en el nombre del nuevo paquete: '%s'", "commands.datapack.create.io_failure": "No se pudo crear un paquete con el nombre '%s', revisa los registros", "commands.datapack.create.metadata_encode_failure": "No se pudo codificar la información sobre los datos del paquete con el nombre '%s': %s", "commands.datapack.create.success": "Se creó un nuevo paquete vacío con el nombre '%s'", "commands.datapack.disable.failed": "¡El paquete de datos \"%s\" no está activado!", "commands.datapack.disable.failed.feature": "¡No se puede desactivar el paquete '%s' porque es parte de una propiedad activada!", "commands.datapack.enable.failed": "¡El paquete de datos \"%s\" ya estaba activado!", "commands.datapack.enable.failed.no_flags": "¡No se puede activar el paquete '%s' porque el mundo no tiene habilitadas las propiedades necesarias: %s!", "commands.datapack.list.available.none": "No hay más paquetes de datos disponibles", "commands.datapack.list.available.success": "Hay %s paquete(s) de datos disponible(s): %s", "commands.datapack.list.enabled.none": "No hay paquetes de datos activos", "commands.datapack.list.enabled.success": "Hay %s paquete(s) de datos activado(s): %s", "commands.datapack.modify.disable": "Desactivando el paquete de datos %s", "commands.datapack.modify.enable": "Activando el paquete de datos %s", "commands.datapack.unknown": "Paquete de datos desconocido: %s", "commands.debug.alreadyRunning": "El análisis de ticks ya ha comenzado", "commands.debug.function.noRecursion": "No se puede rastrear dentro de una función", "commands.debug.function.noReturnRun": "No se puede rastrear con \"/return run\"", "commands.debug.function.success.multiple": "Se rastreó %s comando(s) de %s funciones al archivo resultante %s", "commands.debug.function.success.single": "Se rastreó %s comando(s) de la función '%s' al archivo resultante %s", "commands.debug.function.traceFailed": "Error al rastrear función", "commands.debug.notRunning": "El análisis de ticks no ha comenzado", "commands.debug.started": "Análisis de ticks iniciado", "commands.debug.stopped": "Análisis de ticks detenido luego de %s segundo(s) y (%s tick(s) %s tick(s) por segundo)", "commands.defaultgamemode.success": "El modo de juego por defecto ahora es %s", "commands.deop.failed": "Nada cambió, el jugador no era administrador", "commands.deop.success": "%s ya no es administrador(a) de este server", "commands.dialog.clear.multiple": "Conversación eliminada para %s jugadores", "commands.dialog.clear.single": "Conversación eliminada para %s", "commands.dialog.show.multiple": "Conversación mostrada a %s jugadores", "commands.dialog.show.single": "Conversación mostrada a %s", "commands.difficulty.failure": "La dificultad no ha cambiado, ya estaba configurada en %s", "commands.difficulty.query": "La dificultad está configurada en %s", "commands.difficulty.success": "La dificultad se estableció en %s", "commands.drop.no_held_items": "La entidad no puede sostener ningún objeto", "commands.drop.no_loot_table": "La entidad %s no tiene tabla de loot", "commands.drop.no_loot_table.block": "El bloque %s no tiene tabla de loot", "commands.drop.success.multiple": "Se soltaron %s objetos", "commands.drop.success.multiple_with_table": "Se soltaron %s objetos de la tabla de loot %s", "commands.drop.success.single": "Se ha(n) soltado %s %s", "commands.drop.success.single_with_table": "Eliminados %s %s de la tabla de loot %s", "commands.effect.clear.everything.failed": "El objetivo no tiene efectos que eliminar", "commands.effect.clear.everything.success.multiple": "Se les borraron todos los efectos a %s objetivos", "commands.effect.clear.everything.success.single": "Se le borraron todos los efectos a %s", "commands.effect.clear.specific.failed": "El objetivo no tiene el efecto especificado", "commands.effect.clear.specific.success.multiple": "Se les borró el efecto %s a %s objetivos", "commands.effect.clear.specific.success.single": "Se le borró el efecto %s a %s", "commands.effect.give.failed": "No se pudo aplicar este efecto (el objetivo es inmune a los efectos o tiene algo más potente)", "commands.effect.give.success.multiple": "Se les aplicó el efecto %s a %s objetivos", "commands.effect.give.success.single": "Se le aplicó el efecto %s a %s", "commands.enchant.failed": "Nada cambió, el objetivo no tenía un objeto en la mano o no se pudo aplicar el encantamiento", "commands.enchant.failed.entity": "%s no es una entidad válida para este comando", "commands.enchant.failed.incompatible": "%s no admite este encantamiento", "commands.enchant.failed.itemless": "%s no sostiene ningún objeto", "commands.enchant.failed.level": "%s es superior al nivel máximo del encantamiento (%s)", "commands.enchant.success.multiple": "Se les aplicó el encantamiento %s a los objetos de %s entidades", "commands.enchant.success.single": "Se le aplicó el encantamiento %s al objeto de %s", "commands.execute.blocks.toobig": "Hay demasiados bloques en el área especificada (máximo %s, especificados: %s)", "commands.execute.conditional.fail": "Prueba fallida", "commands.execute.conditional.fail_count": "Prueba fallida, cantidad: %s", "commands.execute.conditional.pass": "Prueba superada", "commands.execute.conditional.pass_count": "La prueba superada, cantidad: %s", "commands.execute.function.instantiationFailure": "Error al instanciar la función %s: %s", "commands.experience.add.levels.success.multiple": "Se les agregaron %s niveles de experiencia a %s jugadores", "commands.experience.add.levels.success.single": "Se le agregaron %s niveles de experiencia a %s", "commands.experience.add.points.success.multiple": "Se les agregaron %s puntos de experiencia a %s jugadores", "commands.experience.add.points.success.single": "Se le agregaron %s puntos de experiencia a %s", "commands.experience.query.levels": "%s tiene %s niveles de experiencia", "commands.experience.query.points": "%s tiene %s puntos de experiencia", "commands.experience.set.levels.success.multiple": "Se les cambiaron el nivel de experiencia de %s jugadores a %s", "commands.experience.set.levels.success.single": "Se estableció el nivel de experiencia de %s a %s", "commands.experience.set.points.invalid": "No se pueden establecer puntos de experiencia por encima del máximo de puntos actuales", "commands.experience.set.points.success.multiple": "Se les establecieron los puntos de experiencia de %s jugadores a %s", "commands.experience.set.points.success.single": "Se establecieron los puntos de experiencia de %s a %s", "commands.fill.failed": "No se rellenaron bloques", "commands.fill.success": "Se rellenaron %s bloque(s)", "commands.fill.toobig": "Demasiados bloques en el área especificada (máximo %s, especificados: %s)", "commands.fillbiome.success": "Biomas establecidos entre %s, %s, %s y %s, %s, %s", "commands.fillbiome.success.count": "Esteblecida(s) %s entrada(s) al bioma entre %s, %s, %s y %s, %s, %s", "commands.fillbiome.toobig": "Demasiados bloques en el volumen indicado (máximo %s, especificados: %s)", "commands.forceload.added.failure": "Ningún chunk fue marcado para forzar la carga", "commands.forceload.added.multiple": "%s chunks fueron marcados en %s de %s a %s para forzar la carga", "commands.forceload.added.none": "Ningún chunk forzosamente cargado fue encontrado en %s", "commands.forceload.added.single": "El chunk %s fue marcado en %s para forzar la carga", "commands.forceload.list.multiple": "%s chunks forzosamente cargados fueron encontrados en %s en: %s", "commands.forceload.list.single": "Un chunk forzosamente cargado fue encontrado en %s en: %s", "commands.forceload.query.failure": "El chunk de %s en %s no está marcado para forzar la carga", "commands.forceload.query.success": "El chunk de %s en %s está marcado para forzar la carga", "commands.forceload.removed.all": "Se desmarcaron todos los chunks forzosamente cargados en %s", "commands.forceload.removed.failure": "Ningún chunk forzosamente cargado fue eliminado", "commands.forceload.removed.multiple": "%s chunks fueron desmarcados en %s de %s a %s para forzar la carga", "commands.forceload.removed.single": "El chunk %s fue desmarcado en %s para forzar la carga", "commands.forceload.toobig": "Hay demasiados chunks en el área especificada (máximo %s, especificados: %s)", "commands.function.error.argument_not_compound": "Tipo de argumento no válido: %s, se esperaba Compound", "commands.function.error.missing_argument": "Falta el argumento %2$s en la función %1$s", "commands.function.error.missing_arguments": "Faltan argumentos en la función %s", "commands.function.error.parse": "Al ejecutar el macro %s: el comando '%s' provocó el error: %s", "commands.function.instantiationFailure": "Error al instanciar la función %s: %s", "commands.function.result": "La función %s devolvió %s", "commands.function.scheduled.multiple": "Ejecutando funciones %s", "commands.function.scheduled.no_functions": "No se encontró ninguna función con el nombre %s", "commands.function.scheduled.single": "Ejecutando función %s", "commands.function.success.multiple": "Se ejecutó %s comando(s) de %s funciones", "commands.function.success.multiple.result": "Se ejecutaron %s funciones", "commands.function.success.single": "Se ejecutó %s comando(s) de la función '%s'", "commands.function.success.single.result": "La función '%2$s' retornó %1$s", "commands.gamemode.success.other": "Se cambió el modo de juego de %s a %s", "commands.gamemode.success.self": "Se cambió el modo de juego a %s", "commands.gamerule.query": "La regla %s está establecida como \"%s\"", "commands.gamerule.set": "La regla %s se cambió a \"%s\"", "commands.give.failed.toomanyitems": "No se puede dar más que %s de %s", "commands.give.success.multiple": "%s jugadores recibieron %s de %s", "commands.give.success.single": "%3$s recibió %1$s %2$s", "commands.help.failed": "Comando desconocido o permiso insuficiente", "commands.item.block.set.success": "Se ha reemplazado un espacio en %s, %s, %s con %s", "commands.item.entity.set.success.multiple": "Se ha reemplazado un espacio en %s entidades con %s", "commands.item.entity.set.success.single": "Se ha reemplazado un espacio en %s con %s", "commands.item.source.no_such_slot": "El objetivo no tiene el espacio %s", "commands.item.source.not_a_container": "La posición inicial %s, %s, %s no es un contenedor", "commands.item.target.no_changed.known_item": "Ningún objetivo acepto el ítem %s en el espacio %s", "commands.item.target.no_changes": "Ningún objetivo aceptó el ítem en el espacio %s", "commands.item.target.no_such_slot": "El objetivo especificado no tiene el espacio %s", "commands.item.target.not_a_container": "La posición del objetivo %s, %s, %s no es un contenedor", "commands.jfr.dump.failed": "Error al intentar almacenar el análisis JFR: %s", "commands.jfr.start.failed": "Error al iniciar el análisis de tipo JFR", "commands.jfr.started": "Análisis de tipo JFR iniciado", "commands.jfr.stopped": "El análisis de tipo JFR se finalizó y almacenó en %s", "commands.kick.owner.failed": "No puedes expulsar al dueño de un server LAN", "commands.kick.singleplayer.failed": "No se puede expulsar en un mundo de un jugador", "commands.kick.success": "Se ha expulsado a %s: %s", "commands.kill.success.multiple": "Se han eliminado %s entidades", "commands.kill.success.single": "Mataste a %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hay %s de un máximo de %s jugadores conectados: %s", "commands.locate.biome.not_found": "No se ha encontrado un bioma de tipo \"%s\" a una distancia razonable", "commands.locate.biome.success": "La estructura %s está en %s (a %s bloques de distancia)", "commands.locate.poi.not_found": "No se pudo encontrar un punto de interés de tipo \"%s\" a una distancia razonable", "commands.locate.poi.success": "La estructura %s más cercana está en %s (a %s bloques de distancia)", "commands.locate.structure.invalid": "No hay ninguna estructura de tipo \"%s\"", "commands.locate.structure.not_found": "No se encontró ninguna estructura de tipo \"%s\" cerca", "commands.locate.structure.success": "La estructura %s está en %s (a %s bloques de distancia)", "commands.message.display.incoming": "%s te susurró: %s", "commands.message.display.outgoing": "Susurraste a %s: %s", "commands.op.failed": "<PERSON>da camb<PERSON>, el jugador ya era administrador", "commands.op.success": "%s se ha convertido en administrador(a) de este server", "commands.pardon.failed": "Nada cambió, el jugador no estaba baneado", "commands.pardon.success": "Se ha desbaneado a %s", "commands.pardonip.failed": "Nada cambió, la IP no estaba baneada", "commands.pardonip.invalid": "Dirección IP no válida", "commands.pardonip.success": "Se ha desbaneado la IP %s", "commands.particle.failed": "La partícula no era visible para nadie", "commands.particle.success": "Mostrando la partícula %s", "commands.perf.alreadyRunning": "El análisis de rendimiento ya ha comenzado", "commands.perf.notRunning": "El análisis de rendimiento no ha comenzado", "commands.perf.reportFailed": "Error al crear el reporte debug", "commands.perf.reportSaved": "Creado reporte debug en %s", "commands.perf.started": "Análisis de rendimiento iniciado por 10 segundos (usa '/perf stop' para interrumpirlo)", "commands.perf.stopped": "Se detuvo el análisis de rendimiento luego de %s segundo(s) y %s tick(s) (%s tick(s) por segundo)", "commands.place.feature.failed": "Error al insertar elemento", "commands.place.feature.invalid": "No hay un elemento de tipo \"%s\"", "commands.place.feature.success": "Se insertó el elemento \"%s\" en %s, %s, %s", "commands.place.jigsaw.failed": "Error al generar pieza", "commands.place.jigsaw.invalid": "El grupo de plantillas \"%s\" no existe", "commands.place.jigsaw.success": "Pieza generada en %s, %s, %s", "commands.place.structure.failed": "Error al generar estructura", "commands.place.structure.invalid": "No hay ninguna estructura de tipo \"%s\"", "commands.place.structure.success": "Se generó la estructura \"%s\" en %s, %s, %s", "commands.place.template.failed": "Error al colocar la plantilla", "commands.place.template.invalid": "No hay ninguna plantilla con la ID \"%s\"", "commands.place.template.success": "Plantilla \"%s\" cargada en %s, %s, %s", "commands.playsound.failed": "El sonido está demasiado lejos para ser escuchado", "commands.playsound.success.multiple": "Se reprodujo %s a %s jugadores", "commands.playsound.success.single": "Sonido %s reproducido a %s", "commands.publish.alreadyPublished": "La partida multijugador ya estaba alojada en el puerto %s", "commands.publish.failed": "No se pudo alojar la partida en LAN", "commands.publish.started": "Mundo en LAN alojado en el puerto %s", "commands.publish.success": "La partida multijugador está alojada ahora en el puerto %s", "commands.random.error.range_too_large": "El rango del número al azar debe ser como máximo 2147483646", "commands.random.error.range_too_small": "El rango del número al azar debe ser como mínimo 2", "commands.random.reset.all.success": "Se reseteó %s secuencia(s) aleatoria(s)", "commands.random.reset.success": "Se reseteó la secuencia aleatoria %s", "commands.random.roll": "%s sacó %s (entre %s y %s)", "commands.random.sample.success": "Número al azar: %s", "commands.recipe.give.failed": "No se han aprendido recetas", "commands.recipe.give.success.multiple": "Se desbloqueó %s receta(s) a %s jugadores", "commands.recipe.give.success.single": "Se desbloqueó %s receta(s) a %s", "commands.recipe.take.failed": "No hay recetas que olvidar", "commands.recipe.take.success.multiple": "Se eliminó %s receta(s) a %s jugadores", "commands.recipe.take.success.single": "Se eliminó %s receta(s) a %s", "commands.reload.failure": "<PERSON><PERSON><PERSON> fallida, manteniendo datos antiguos", "commands.reload.success": "¡Recargando!", "commands.ride.already_riding": "%s ya está montando a %s", "commands.ride.dismount.success": "%s se bajó de %s", "commands.ride.mount.failure.cant_ride_players": "No se puede montar un jugador", "commands.ride.mount.failure.generic": "%s no puede montar a %s", "commands.ride.mount.failure.loop": "No se puede montar una entidad en sí misma ni en sus pasajeros", "commands.ride.mount.failure.wrong_dimension": "No se puede montar una entidad en una dimensión diferente", "commands.ride.mount.success": "%s se montó en %s", "commands.ride.not_riding": "%s no está montando ningún vehículo", "commands.rotate.success": "Se rotó a %s", "commands.save.alreadyOff": "El guardado ya está desactivado", "commands.save.alreadyOn": "El guardado ya está activado", "commands.save.disabled": "Autoguardado desactivado", "commands.save.enabled": "Autoguardado activado", "commands.save.failed": "Error al guardar la partida (¿hay suficiente espacio en el disco duro?)", "commands.save.saving": "Guardando la partida... (Esto podría tardar un poco)", "commands.save.success": "Partida guardada", "commands.schedule.cleared.failure": "No hay tareas programadas guardadas con id %s", "commands.schedule.cleared.success": "Se eliminó %s tarea(s) programada(s) con el id %s", "commands.schedule.created.function": "Se programó la función '%s' dentro de %s tick(s) en tiempo de juego %s", "commands.schedule.created.tag": "Se programó la etiqueta '%s' dentro de %s tick(s) en tiempo de juego %s", "commands.schedule.macro": "No se pudo programar un macro", "commands.schedule.same_tick": "No se pudo programar para el ciclo actual", "commands.scoreboard.objectives.add.duplicate": "Ya existe un objetivo con este nombre", "commands.scoreboard.objectives.add.success": "Se creó el objetivo %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nada cambió, este espacio de muestra ya estaba vacío", "commands.scoreboard.objectives.display.alreadySet": "Nada cambió, ese espacio de muestra ya estaba mostrando ese objetivo", "commands.scoreboard.objectives.display.cleared": "Se quitaron todos los objetivos que se mostraban en el espacio %s", "commands.scoreboard.objectives.display.set": "Se estableció que el espacio de muestra %s mostrara el objetivo %s", "commands.scoreboard.objectives.list.empty": "No hay objetivos", "commands.scoreboard.objectives.list.success": "Hay %s objetivo(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Se desactivó la actualización automática del nombre del objetivo %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Se activó la actualización automática del nombre del objetivo %s", "commands.scoreboard.objectives.modify.displayname": "Se cambió el nombre de %s a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Se quitó el formato numérico predeterminado del objetivo %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Se cambió el formato numérico predeterminado del objetivo %s", "commands.scoreboard.objectives.modify.rendertype": "Se ha cambiado el tipo de renderizado del objetivo %s", "commands.scoreboard.objectives.remove.success": "Se eliminó el objetivo %s", "commands.scoreboard.players.add.success.multiple": "Se añadieron %s puntos a %s para %s entidades", "commands.scoreboard.players.add.success.single": "Se añadieron %s puntos a %s para %s (ahora tiene %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Se limpió el nombre de %s entidades en %s", "commands.scoreboard.players.display.name.clear.success.single": "Se limpió el nombre de %s en %s", "commands.scoreboard.players.display.name.set.success.multiple": "Se cambió el nombre a %s a %s entidades en %s", "commands.scoreboard.players.display.name.set.success.single": "Se cambió el nombre a %s a %s en %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Se limpió el formato numérico de %s entidades en %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Se limpió el formato numérico de %s en %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Se cambió el formato numérico de %s entidades en %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Se cambió el formato numérico de %s en %s", "commands.scoreboard.players.enable.failed": "Nada cambió, el activador ya estaba activado", "commands.scoreboard.players.enable.invalid": "La función \"enable\" solo sirve con objetivos de tipo \"trigger\"", "commands.scoreboard.players.enable.success.multiple": "Se activó el trigger %s para %s entidades", "commands.scoreboard.players.enable.success.single": "Se activó el activador %s para %s", "commands.scoreboard.players.get.null": "No se pudo obtener la puntuación de %s para %s, no hay registro", "commands.scoreboard.players.get.success": "%s tiene una puntuación de %s en %s", "commands.scoreboard.players.list.empty": "No hay entidades registradas", "commands.scoreboard.players.list.entity.empty": "%s no tiene puntuaciones que mostrar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s tiene %s punto(s):", "commands.scoreboard.players.list.success": "Hay %s entidad(es) registrada(s): %s", "commands.scoreboard.players.operation.success.multiple": "Se actualizó la puntuación de %s para %s entidades", "commands.scoreboard.players.operation.success.single": "Se fijó %s para %s a %s", "commands.scoreboard.players.remove.success.multiple": "Se eliminaron %s puntos de %s para %s entidades", "commands.scoreboard.players.remove.success.single": "Se rliminaron %s puntos de %s para %s (ahora tiene %s)", "commands.scoreboard.players.reset.all.multiple": "Se reiniciaron todas las puntuaciones de %s entidades", "commands.scoreboard.players.reset.all.single": "Se reiniciaron todas las puntuaciones de %s", "commands.scoreboard.players.reset.specific.multiple": "Se reinició la puntuación de %s para %s entidades", "commands.scoreboard.players.reset.specific.single": "Se reinició la puntuación de %s para %s", "commands.scoreboard.players.set.success.multiple": "Se cambió la puntuación del objetivo %s para %s entidades a %s", "commands.scoreboard.players.set.success.single": "Se cambió la puntuación del objetivo %s de %s a %s", "commands.seed.success": "Semilla: %s", "commands.setblock.failed": "No se pudo colocar el bloque", "commands.setblock.success": "El bloque en %s, %s, %s cambió", "commands.setidletimeout.success": "Se estableció el máximo de inactividad de jugadores a %s minuto(s)", "commands.setidletimeout.success.disabled": "Se desactivó el tiempo máximo de inactividad por jugador", "commands.setworldspawn.failure.not_overworld": "El punto de aparición del mundo no puede estar en otra dimensión", "commands.setworldspawn.success": "Se ha cambiado el punto de aparición del mundo a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Definido el punto de reaparición en %s, %s, %s [%s] en la dimensión %s para %s jugadores", "commands.spawnpoint.success.single": "Definido el punto de reaparición en %s, %s, %s [%s] en la dimensión %s para %s", "commands.spectate.not_spectator": "%s no está en modo espectador", "commands.spectate.self": "No puedes observarte a ti mismo", "commands.spectate.success.started": "Ahora observas a %s", "commands.spectate.success.stopped": "Ya no estás observando a una entidad", "commands.spreadplayers.failed.entities": "No se pudo repartir %s entidad(es) entre %s y %s (demasiadas entidades para ese espacio, prueba repartiéndolas al menos a %s)", "commands.spreadplayers.failed.invalid.height": "Valor inválido para maxHeight %s; requiere un valor mayor al mínimo del mundo %s", "commands.spreadplayers.failed.teams": "No se pudo repartir %s equipo(s) entre %s y %s (demasiadas entidades para ese espacio, prueba repartiéndolas al menos a %s)", "commands.spreadplayers.success.entities": "Se repartió %s entidad(es) entre %s, %s con una distancia promedio de %s bloque(s)", "commands.spreadplayers.success.teams": "Se repartió %s equipo(s) entre %s, %s con una distancia promedio de %s bloque(s)", "commands.stop.stopping": "Deteniendo el server", "commands.stopsound.success.source.any": "Se detuvieron todos los sonidos \"%s\"", "commands.stopsound.success.source.sound": "Se detuvo el sonido \"%s\" de la fuente \"%s\"", "commands.stopsound.success.sourceless.any": "Se detuvieron todos los sonidos", "commands.stopsound.success.sourceless.sound": "Se detuvo el sonido \"%s\"", "commands.summon.failed": "No se pudo generar la entidad", "commands.summon.failed.uuid": "No se puede convocar a la entidad debido a UUID duplicados", "commands.summon.invalidPosition": "Posición inválida para generar una entidad", "commands.summon.success": "Se generó la entidad \"%s\"", "commands.tag.add.failed": "El objetivo ya tenía la etiqueta o tiene demasiadas", "commands.tag.add.success.multiple": "Se les añadió la etiqueta \"%s\" a %s entidades", "commands.tag.add.success.single": "Se ha añadido la etiqueta \"%s\" a %s", "commands.tag.list.multiple.empty": "Las %s entidades no tienen etiquetas", "commands.tag.list.multiple.success": "Las %s entidades tienen un total de %s etiquetas: %s", "commands.tag.list.single.empty": "%s no tiene etiquetas", "commands.tag.list.single.success": "%s tiene %s etiquetas: %s", "commands.tag.remove.failed": "El objetivo no tiene esta etiqueta", "commands.tag.remove.success.multiple": "Se les eliminó la etiqueta \"%s\" a %s entidades", "commands.tag.remove.success.single": "Se le eliminó la etiqueta \"%s\" a %s", "commands.team.add.duplicate": "Ya existe un equipo con este nombre", "commands.team.add.success": "Se ha creado el equipo %s", "commands.team.empty.success": "Se expulsó %s miembro(s) del equipo %s", "commands.team.empty.unchanged": "Nada cambió, el equipo ya estaba vacío", "commands.team.join.success.multiple": "Se añadieron %s miembros al equipo %s", "commands.team.join.success.single": "Se añadió a %s al equipo %s", "commands.team.leave.success.multiple": "Se eliminaron a %s miembros de sus equipos", "commands.team.leave.success.single": "Se eliminó a %s de su equipo", "commands.team.list.members.empty": "El equipo %s no tiene miembros", "commands.team.list.members.success": "El equipo %s tiene %s miembro(s): %s", "commands.team.list.teams.empty": "No hay equipos", "commands.team.list.teams.success": "Hay %s equipo(s): %s", "commands.team.option.collisionRule.success": "Se cambió la regla de colisión del equipo %s a \"%s\"", "commands.team.option.collisionRule.unchanged": "Nada cambió, la regla de colisión ya tenía ese valor", "commands.team.option.color.success": "Se cambió el color del equipo %s a %s", "commands.team.option.color.unchanged": "Nada cambió, el equipo ya tenía ese color", "commands.team.option.deathMessageVisibility.success": "Se cambió la visibilidad de los avisos de muertes del equipo %s a \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nada cambió, la visibilidad de avisos de muertes ya tenía ese valor", "commands.team.option.friendlyfire.alreadyDisabled": "Nada cambió, el fuego amigo ya estaba desactivado en ese equipo", "commands.team.option.friendlyfire.alreadyEnabled": "Nada cambió, el fuego amigo ya estaba activado en ese equipo", "commands.team.option.friendlyfire.disabled": "Se desactivó el fuego amigo en el equipo %s", "commands.team.option.friendlyfire.enabled": "Se activó el fuego amigo en el equipo %s", "commands.team.option.name.success": "Se ha actualizado el nombre del equipo %s", "commands.team.option.name.unchanged": "Nada cambió. Ese equipo ya tenía ese nombre", "commands.team.option.nametagVisibility.success": "Se cambió la visibilidad de los nombres de los jugadores del equipo %s a \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nada cambió, la visibilidad de nombres ya tenía ese valor", "commands.team.option.prefix.success": "Se cambió el prefijo del equipo a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nada cambió, los miembros del equipo no podían ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nada cambió, los miembros ya podían ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "Desde ahora los miembros del equipo %s no podrán ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "Desde ahora los miembros del equipo %s podrán ver a sus compañeros invisibles", "commands.team.option.suffix.success": "Se cambió el sufijo del equipo a %s", "commands.team.remove.success": "Se eliminó el equipo %s", "commands.teammsg.failed.noteam": "Para enviar un mensaje a tu equipo debes pertenecer a uno", "commands.teleport.invalidPosition": "Posición inválida para teletransportarse", "commands.teleport.success.entity.multiple": "%s entidades teletransportadas a %s", "commands.teleport.success.entity.single": "%s teletransportado hasta %s", "commands.teleport.success.location.multiple": "%s entidades teletransportadas a %s, %s, %s", "commands.teleport.success.location.single": "%s teletransportado a %s, %s, %s", "commands.test.batch.starting": "Iniciando entorno %s del lote %s", "commands.test.clear.error.no_tests": "No se ha podido encontrar ninguna prueba para eliminar", "commands.test.clear.success": "Se han eliminado %s estructura(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Haz clic para copiar al portapapeles", "commands.test.create.success": "Configuración creada para la prueba %s", "commands.test.error.no_test_containing_pos": "No se ha podido encontrar ningún caso de prueba que contenga %s, %s, %s", "commands.test.error.no_test_instances": "No se encontraron instancias de testeo", "commands.test.error.non_existant_test": "No se pudo encontrar la prueba %s", "commands.test.error.structure_not_found": "No se ha podido encontrar la estructura de prueba %s", "commands.test.error.test_instance_not_found": "No se ha podido encontrar ningún bloque de caso de prueba", "commands.test.error.test_instance_not_found.position": "No se ha podido encontrar ningún bloque de caso de prueba en %s, %s, %s", "commands.test.error.too_large": "El tamaño de la estructura debe ser menor a %s bloques en cada eje", "commands.test.locate.done": "Búsqueda terminada, se han encontrado %s estructura(s)", "commands.test.locate.found": "Se ha encontrado una estructura en: %s (distancia: %s)", "commands.test.locate.started": "Se ha iniciado la búsqueda de estructuras de prueba, esto puede tardar un poco...", "commands.test.no_tests": "No hay pruebas que ejecutar", "commands.test.relative_position": "Posición relativa a %s: %s", "commands.test.reset.error.no_tests": "No se ha encontrado ninguna prueba para reiniciar", "commands.test.reset.success": "Se han reiniciado %s estructura(s)", "commands.test.run.no_tests": "Ninguna prueba encontrada", "commands.test.run.running": "Realizando %s prueba(s)...", "commands.test.summary": "¡Se completó la prueba del juego! Se realizó %s prueba(s)", "commands.test.summary.all_required_passed": "Se han superado todas las pruebas requeridas :)", "commands.test.summary.failed": "Fallaron %s pruebas requeridas :(", "commands.test.summary.optional_failed": "Fallaron %s prueba(s) opcional(es)", "commands.tick.query.percentiles": "Percentiles P50: %sms, P95: %sms, P99: %sms, muestra: %s", "commands.tick.query.rate.running": "Tasa de ticks objetivo: %s por segundo.\nTiempo por tick promedio: %sms (Objetivo: %sms)", "commands.tick.query.rate.sprinting": "Tasa de ticks objetivo: %s por segundo (ignorado, solo de referencia)\nTiempo por tick promedio: %sms", "commands.tick.rate.success": "Se estableció la tasa de ticks a %s por segundo", "commands.tick.sprint.report": "Se completó la aceleración de ticks con %s ticks por segundo, ó %s ms por tick", "commands.tick.sprint.stop.fail": "No hay una aceleración de ticks en curso", "commands.tick.sprint.stop.success": "Se interrumpió la aceleración de ticks", "commands.tick.status.frozen": "Se congeló el juego", "commands.tick.status.lagging": "El juego se está ejecutando, pero no pudo mantenerse con la tasa de ticks buscada", "commands.tick.status.running": "El juego se ejecutará a velocidad normal", "commands.tick.status.sprinting": "Ejecutando a velocidad alta", "commands.tick.step.fail": "No se pudo ejecutar tick del juego, el juego debe estar congelado primero", "commands.tick.step.stop.fail": "No hay ninguna ejecución de tick en progreso", "commands.tick.step.stop.success": "Se interrumpió la ejecución del tick actual", "commands.tick.step.success": "Ejecutando %s tick(s)", "commands.time.query": "Hora: %s ticks", "commands.time.set": "Tiempo ajustado a %s ticks", "commands.title.cleared.multiple": "Se eliminaron los títulos de %s jugadores", "commands.title.cleared.single": "Se eliminaron los títulos de %s", "commands.title.reset.multiple": "Se reiniciaron las opciones de título para %s jugadores", "commands.title.reset.single": "Se reiniciaron las opciones de título para %s", "commands.title.show.actionbar.multiple": "Mostrando nuevo título en barra de objetos para %s jugadores", "commands.title.show.actionbar.single": "Mostrando nuevo título en la barra de objetos de %s", "commands.title.show.subtitle.multiple": "Mostrando nuevo subtítulo para %s jugadores", "commands.title.show.subtitle.single": "Mostrando nuevo subtítulo para %s", "commands.title.show.title.multiple": "Mostrando nuevo título para %s jugadores", "commands.title.show.title.single": "Mostrando nuevo título para %s", "commands.title.times.multiple": "Se cambió el tiempo de visualización del título para %s jugadores", "commands.title.times.single": "Se cambió el tiempo de visualización del título para %s", "commands.transfer.error.no_players": "Debes especificar al menos un jugador a transferir", "commands.transfer.success.multiple": "Transfiriendo %s jugadores a %s:%s", "commands.transfer.success.single": "Trasnfiriendo a %s a %s:%s", "commands.trigger.add.success": "Se activó el activador %s (se añadió %s al valor)", "commands.trigger.failed.invalid": "Solo puedes activar objetivos de tipo \"trigger\"", "commands.trigger.failed.unprimed": "Aún no puedes activar este objetivo activador", "commands.trigger.set.success": "Se activó el activador %s (se cambió el valor a %s)", "commands.trigger.simple.success": "Se activó el activador %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No hay puntos de referencia en %s", "commands.waypoint.list.success": "%s punto(s) de referencia en %s: %s", "commands.waypoint.modify.color": "El punto de referencia ahora es de color %s", "commands.waypoint.modify.color.reset": "Reinicia el color del punto de referencia", "commands.waypoint.modify.style": "Se cambió el estilo del marcador", "commands.weather.set.clear": "Se cambió el tiempo a despejado", "commands.weather.set.rain": "Se cambió el tiempo a lluvia", "commands.weather.set.thunder": "Se cambió el tiempo a tormenta", "commands.whitelist.add.failed": "El jugador ya estaba en la lista blanca", "commands.whitelist.add.success": "%s fue agregado a la lista blanca", "commands.whitelist.alreadyOff": "La lista blanca ya estaba desactivada", "commands.whitelist.alreadyOn": "La lista blanca ya estaba activada", "commands.whitelist.disabled": "Se desactivó la lista blanca", "commands.whitelist.enabled": "Se activó la lista blanca", "commands.whitelist.list": "Hay %s jugador(es) en la lista blanca: %s", "commands.whitelist.none": "No hay jugadores en la lista blanca", "commands.whitelist.reloaded": "La lista blanca fue recargada", "commands.whitelist.remove.failed": "El jugador no estaba en la lista blanca", "commands.whitelist.remove.success": "%s fue borrado de la lista blanca", "commands.worldborder.center.failed": "Nada cambió, el borde del mundo ya estaba establecido en esa posición", "commands.worldborder.center.success": "Se cambió el centro del borde del mundo a las coordenadas %s, %s", "commands.worldborder.damage.amount.failed": "Nada cambió, el borde del mundo ya inflige esta cantidad de daño", "commands.worldborder.damage.amount.success": "Se ha establecido el daño fuera del borde del mundo a %s por bloque cada segundo", "commands.worldborder.damage.buffer.failed": "Nada cambió, la zona segura fuera del mundo ya estaba a esa distancia", "commands.worldborder.damage.buffer.success": "Se estableció la zona segura fuera del mundo a %s bloque(s)", "commands.worldborder.get": "El borde del mundo actual tiene %s bloque(s) de ancho", "commands.worldborder.set.failed.big": "El borde del mundo no puede tener una extensión mayor a %s bloques", "commands.worldborder.set.failed.far": "El borde del mundo no puede tener una longitud mayor a %s bloques", "commands.worldborder.set.failed.nochange": "Nada cambió, el borde del mundo ya tenía este tamaño", "commands.worldborder.set.failed.small": "El borde del mundo no puede tener una extension menor a 1 bloque", "commands.worldborder.set.grow": "Aumentando el borde del mundo en %s bloques durante %s segundos", "commands.worldborder.set.immediate": "Se estableció el borde del mundo a %s bloque(s) de ancho", "commands.worldborder.set.shrink": "Reduciendo el borde del mundo a %s bloque(s) de ancho en %s segundo(s)", "commands.worldborder.warning.distance.failed": "Nada cambió, el aviso del borde del mundo ya tenía esa distancia", "commands.worldborder.warning.distance.success": "Se estableció la distancia de advertencia del borde del mundo a %s bloque(s)", "commands.worldborder.warning.time.failed": "Nada cambió, el aviso del borde del mundo ya duraba esa cantidad de tiempo", "commands.worldborder.warning.time.success": "Se estableció el tiempo advertencia del borde del mundo a %s segundo(s)", "compliance.playtime.greaterThan24Hours": "Has estado jugando por más de 24 horas", "compliance.playtime.hours": "Has estado jugando por %s hora(s)", "compliance.playtime.message": "Jugar mucho tiempo podría interferir con tu vida diaria", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Iniciando se<PERSON>...", "connect.connecting": "Conectando con el server...", "connect.encrypting": "Encriptando...", "connect.failed": "No se pudo conectar con el server", "connect.failed.transfer": "Error de conexión al transferir al servidor", "connect.joining": "Entrando al mundo...", "connect.negotiating": "Conectando...", "connect.reconfiging": "Reconfigurando...", "connect.reconfiguring": "Reconfigurando...", "connect.transferring": "Transfiriendo a nuevo servidor...", "container.barrel": "Barril", "container.beacon": "Faro", "container.beehive.bees": "Abejas: %s / %s", "container.beehive.honey": "Miel: %s / %s", "container.blast_furnace": "Alto horno", "container.brewing": "Alambique", "container.cartography_table": "Mesa de cartografía", "container.chest": "Cofre", "container.chestDouble": "Cofre grande", "container.crafter": "Crafteadora", "container.crafting": "Fabricación", "container.creative": "Selección de ítems", "container.dispenser": "Dispensador", "container.dropper": "Soltador", "container.enchant": "Encantar", "container.enchant.clue": "¿ %s . . . ?", "container.enchant.lapis.many": "%s lapis<PERSON><PERSON><PERSON><PERSON>", "container.enchant.lapis.one": "1 lapislázuli", "container.enchant.level.many": "%s niveles de experiencia", "container.enchant.level.one": "1 nivel de experiencia", "container.enchant.level.requirement": "Nivel requerido: %s", "container.enderchest": "<PERSON><PERSON><PERSON>", "container.furnace": "<PERSON><PERSON>", "container.grindstone_title": "Reparar y desencantar", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Inventario", "container.isLocked": "¡%s está bloqueado!", "container.lectern": "Atril", "container.loom": "Telar", "container.repair": "Reparar y renombrar", "container.repair.cost": "Costo: %1$s", "container.repair.expensive": "¡Demasiado caro!", "container.shulkerBox": "<PERSON><PERSON> de <PERSON>ker", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "y %s más...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "No se puede abrir. El loot no se ha generado aún.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "<PERSON><PERSON>rar equipamiento", "container.upgrade.error_tooltip": "Este ítem no se puede mejorar así", "container.upgrade.missing_template_tooltip": "Pon un molde de herrería", "controls.keybinds": "Teclas...", "controls.keybinds.duplicateKeybinds": "Esta tecla también se asignó a:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON>", "controls.reset": "Reiniciar", "controls.resetAll": "Reiniciar teclas", "controls.title": "Controles", "createWorld.customize.buffet.biome": "Por favor, selecciona el bioma del mundo", "createWorld.customize.buffet.title": "Personalizar bioma único", "createWorld.customize.flat.height": "Altura", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "(Fondo) %s", "createWorld.customize.flat.layer.top": "(Superficie) %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> capa", "createWorld.customize.flat.tile": "Material de la capa", "createWorld.customize.flat.title": "Personalización del mundo extraplano", "createWorld.customize.presets": "Plantillas", "createWorld.customize.presets.list": "O si lo prefieres, ¡puedes elegir entre estas que ya creamos!", "createWorld.customize.presets.select": "Usar plantilla", "createWorld.customize.presets.share": "¿Quieres compartir tu plantilla con alguien? ¡Usa la caja de abajo!", "createWorld.customize.presets.title": "Selecciona una Plantilla", "createWorld.preparing": "Preparando para la creación del mundo...", "createWorld.tab.game.title": "Ju<PERSON>", "createWorld.tab.more.title": "Más", "createWorld.tab.world.title": "Mundo", "credits_and_attribution.button.attribution": "Atribuciones", "credits_and_attribution.button.credits": "C<PERSON>dit<PERSON>", "credits_and_attribution.button.licenses": "Licencias", "credits_and_attribution.screen.title": "Créditos y atribuciones", "dataPack.bundle.description": "Habilita el ítem experimental: Saco", "dataPack.bundle.name": "Sacos", "dataPack.locator_bar.description": "Indica la dirección de otros jugadores en multijugador", "dataPack.locator_bar.name": "Barra localizadora", "dataPack.minecart_improvements.description": "Mejoras al movimiento de los vagones", "dataPack.minecart_improvements.name": "V<PERSON><PERSON> mejorados", "dataPack.redstone_experiments.description": "Cambios experimentales a la redstone", "dataPack.redstone_experiments.name": "Redstone experimental", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Tradeos con aldeanos actualizados", "dataPack.trade_rebalance.name": "Rebalance de tradeos de aldeanos", "dataPack.update_1_20.description": "Nuevas características y contenido para Minecraft 1.20", "dataPack.update_1_20.name": "Actualización 1.20", "dataPack.update_1_21.description": "Nuevas características y contenido para Minecraft 1.21", "dataPack.update_1_21.name": "Actualización 1.21", "dataPack.validation.back": "Volver", "dataPack.validation.failed": "¡Error al validar paquetes de datos!", "dataPack.validation.reset": "Cambiar a predeterminado", "dataPack.validation.working": "Validando paquetes de datos seleccionados...", "dataPack.vanilla.description": "Los datos por defecto de Minecraft", "dataPack.vanilla.name": "Por defecto", "dataPack.winter_drop.description": "Nuevas características y contenido del drop de invierno", "dataPack.winter_drop.name": "Drop de invierno", "datapackFailure.safeMode": "<PERSON><PERSON> se<PERSON>", "datapackFailure.safeMode.failed.description": "Este mundo contiene datos de guardado no válidos o corruptos.", "datapackFailure.safeMode.failed.title": "Error al cargar el mundo en modo seguro.", "datapackFailure.title": "No se pudo cargar el mundo por errores en los paquetes de datos seleccionados.\nPuedes intentar cargarlo solo con el paquete de datos vanilla (\"modo seguro\") o volver al menú principal y solucionarlo manualmente.", "death.attack.anvil": "%1$s fue aplastado por un yunque", "death.attack.anvil.player": "%1$s fue aplastado por un yunque mientras peleaba contra %2$s", "death.attack.arrow": "%1$s murió por un flechazo de %2$s", "death.attack.arrow.item": "%1$s murió por un flechazo de %2$s usando su %3$s", "death.attack.badRespawnPoint.link": "Diseño intencional del juego", "death.attack.badRespawnPoint.message": "%1$s fue víctima de %2$s", "death.attack.cactus": "%1$s se pinchó hasta morir", "death.attack.cactus.player": "%1$s caminó cerca de un cactus mientras intentaba escapar de %2$s", "death.attack.cramming": "%1$s fue aplastado con demasiada fuerza", "death.attack.cramming.player": "%1$s fue aplastado por %2$s", "death.attack.dragonBreath": "%1$s se asó en aliento de dragón", "death.attack.dragonBreath.player": "%1$s se asó en aliento de dragón por culpa de %2$s", "death.attack.drown": "%1$s se ahogó", "death.attack.drown.player": "%1$s se ahogó mientras intentaba escapar de %2$s", "death.attack.dryout": "%1$s murió de deshidratación", "death.attack.dryout.player": "%1$s murió de deshidratación mientras intentaba escapar de %2$s", "death.attack.even_more_magic": "%1$s ha muerto por mucho más que arte de magia", "death.attack.explosion": "%1$s explotó", "death.attack.explosion.player": "%1$s fue detonado por %2$s", "death.attack.explosion.player.item": "%1$s ha explotado por %2$s usando su %3$s", "death.attack.fall": "%1$s se golpeó contra el suelo muy fuerte", "death.attack.fall.player": "%1$s se golpeó contra el suelo muy fuerte tratando de escapar de %2$s", "death.attack.fallingBlock": "%1$s fue aplastado por un bloque", "death.attack.fallingBlock.player": "%1$s fue aplastado por un bloque mientras peleaba contra %2$s", "death.attack.fallingStalactite": "%1$s fue atravesado por una estalactita", "death.attack.fallingStalactite.player": "%1$s fue atravesado/a por una estalactita mientras luchaba contra %2$s", "death.attack.fireball": "%1$s fue calcinado por una bola de fuego de %2$s", "death.attack.fireball.item": "%1$s fue calcinado por una bola de fuego de %2$s con su %3$s", "death.attack.fireworks": "%1$s se fue con un bang", "death.attack.fireworks.item": "%1$s explotó por un fuego artificial disparado desde un/a %3$s por %2$s", "death.attack.fireworks.player": "%1$s se fue con un bang mientras peleaba contra %2$s", "death.attack.flyIntoWall": "%1$s experimentó la energía cinética", "death.attack.flyIntoWall.player": "%1$s experimentó la energía cinética tratando de escapar de %2$s", "death.attack.freeze": "%1$s se congeló hasta la muerte", "death.attack.freeze.player": "%1$s fue congelado hasta la muerte por %2$s", "death.attack.generic": "%1$s murió", "death.attack.generic.player": "%1$s murió por culpa de %2$s", "death.attack.genericKill": "%1$s fue asesinado", "death.attack.genericKill.player": "%1$s murió mientras peleaba contra %2$s", "death.attack.hotFloor": "%1$s descubrió que el suelo era lava", "death.attack.hotFloor.player": "%1$s metió la pata por culpa de %2$s", "death.attack.inFire": "%1$s ardió en llamas", "death.attack.inFire.player": "%1$s ardió en llamas mientras luchaba contra %2$s", "death.attack.inWall": "%1$s se asfixió en una pared", "death.attack.inWall.player": "%1$s se asfixió en una pared mientras peleaba contra %2$s", "death.attack.indirectMagic": "%1$s fue asesinado por la magia de %2$s", "death.attack.indirectMagic.item": "%1$s fue asesinado por %2$s con su %3$s", "death.attack.lava": "%1$s intentó nadar en la lava", "death.attack.lava.player": "%1$s intentó nadar en la lava para escapar de %2$s", "death.attack.lightningBolt": "A %1$s le cayó un rayo", "death.attack.lightningBolt.player": "A %1$s le cayó un rayo mientras luchaba contra %2$s", "death.attack.mace_smash": "%1$s fue reventado por %2$s", "death.attack.mace_smash.item": "%1$s fue reventado por %2$s con su %3$s", "death.attack.magic": "%1$s murió por arte de magia", "death.attack.magic.player": "%1$s murió por arte de magia tratando de escapar de %2$s", "death.attack.message_too_long": "El mensaje era demasiado largo para mostrarse completo. ¡Lo sentimos! Versión abreviada: %s", "death.attack.mob": "%1$s fue víctima de %2$s", "death.attack.mob.item": "%1$s fue asesinado por %2$s con su %3$s", "death.attack.onFire": "%1$s se quemó hasta la muerte", "death.attack.onFire.item": "%1$s se quemó hasta las cenizas mientras luchaba contra %2$s empuñando %3$s", "death.attack.onFire.player": "%1$s se quemó hasta las cenizas mientras luchaba contra %2$s", "death.attack.outOfWorld": "%1$s cayó fuera del mundo", "death.attack.outOfWorld.player": "%1$s no quería vivir en el mismo mundo que %2$s", "death.attack.outsideBorder": "%1$s abandonó los confines de este mundo", "death.attack.outsideBorder.player": "%1$s abandonó los confines de este mundo mientras peleaba contra %2$s", "death.attack.player": "%1$s fue asesinado por %2$s", "death.attack.player.item": "%1$s fue asesinado por %2$s con su %3$s", "death.attack.sonic_boom": "%1$s fue obliterado por una onda super-sónica", "death.attack.sonic_boom.item": "%1$s fue obliterado por una onda super-sónica tratando de escapar de %2$s empuñando %3$s", "death.attack.sonic_boom.player": "%1$s fue obliterado por una onda super-sónica tratando de escapar de %2$s", "death.attack.stalagmite": "%1$s fue empalado en una estalagmita", "death.attack.stalagmite.player": "%1$s fue empalado en una estalagmita mientras luchaba contra %2$s", "death.attack.starve": "%1$s murió de hambre", "death.attack.starve.player": "%1$s murió de hambre mientras peleaba contra %2$s", "death.attack.sting": "%1$s fue picado hasta la muerte", "death.attack.sting.item": "%1$s fue picado hasta la muerte por %2$s usando %3$s", "death.attack.sting.player": "%1$s fue picado hasta la muerte por %2$s", "death.attack.sweetBerryBush": "%1$s fue asesinado por un arbusto de bayas dulces", "death.attack.sweetBerryBush.player": "%1$s se pinchó con arbusto de bayas dulces hasta morir mientras intentaba escapar de %2$s", "death.attack.thorns": "%1$s fue asesinado mientras trataba de golpear a %2$s", "death.attack.thorns.item": "%1$s fue asesinado por %3$s mientras intentaba pegarle a %2$s", "death.attack.thrown": "%1$s fue golpeado por %2$s", "death.attack.thrown.item": "%1$s fue golpeado por %2$s con su %3$s", "death.attack.trident": "%1$s fue empalado por %2$s", "death.attack.trident.item": "%1$s fue empalado por %2$s y su %3$s", "death.attack.wither": "%1$s murió de wither", "death.attack.wither.player": "%1$s murió de descomposición mientras peleaba contra %2$s", "death.attack.witherSkull": "%1$s fue disparado por una calavera de %2$s", "death.attack.witherSkull.item": "%1$s fue disparado por una calavera de %2$s usando %3$s", "death.fell.accident.generic": "%1$s cayó desde muy alto", "death.fell.accident.ladder": "%1$s se cayó de una escalera", "death.fell.accident.other_climbable": "%1$s se cayó mientras escalaba", "death.fell.accident.scaffolding": "%1$s se cayó de un andamio", "death.fell.accident.twisting_vines": "%1$s se cayó de unas enredaderas retorcidas", "death.fell.accident.vines": "%1$s se cayó de unas lianas", "death.fell.accident.weeping_vines": "%1$s se cayó de unas enredaderas lloronas", "death.fell.assist": "%1$s fue empujado desde muy alto por %2$s", "death.fell.assist.item": "%1$s fue condenado a caer por %2$s con su %3$s", "death.fell.finish": "%1$s cayó desde muy alto y fue asesinado por %2$s", "death.fell.finish.item": "%1$s cayó desde muy alto y fue asesinado por %2$s con su %3$s", "death.fell.killer": "%1$s fue condenado a caer desde muy alto", "deathScreen.quit.confirm": "¿Se<PERSON>ro que quieres salir?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuación", "deathScreen.score.value": "Puntuación: %s", "deathScreen.spectate": "<PERSON><PERSON>ar mundo", "deathScreen.title": "¡Has muerto!", "deathScreen.title.hardcore": "¡Fin del juego!", "deathScreen.titleScreen": "<PERSON><PERSON> principal", "debug.advanced_tooltips.help": "F3 + H = Descripciones avanzadas", "debug.advanced_tooltips.off": "Descripciones avanzadas: ocultar", "debug.advanced_tooltips.on": "Descripciones avanzadas: sí", "debug.chunk_boundaries.help": "F3 + G = Mostrar bordes de chunks", "debug.chunk_boundaries.off": "Bordes de chunks: ocultar", "debug.chunk_boundaries.on": "Bordes de chunks: mostrar", "debug.clear_chat.help": "F3 + D = Limpiar chat", "debug.copy_location.help": "F3 + C = Copiar la ubicación como comando /tp. Mantenlas pulsadas para forzar el cierre del juego", "debug.copy_location.message": "Ubicación copiada en el portapapeles", "debug.crash.message": "Estás pulsando F3 + C. Si no las sueltas, el juego dejará de funcionar.", "debug.crash.warning": "El juego dejará de funcionar en %s...", "debug.creative_spectator.error": "No se pudo cambiar el modo de juego; no tienes permisos", "debug.creative_spectator.help": "F3 + N = Alternar entre el modo de juego anterior <-> espectador", "debug.dump_dynamic_textures": "Texturas dinámicas guardadas en %s", "debug.dump_dynamic_textures.help": "F3 + S = Exportar texturas dinámicas", "debug.gamemodes.error": "No se pudo abrir el menú de modos de juego, no tienes permiso", "debug.gamemodes.help": "F3 + F4 = <PERSON>brir menú de modos de juego", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s <PERSON><PERSON><PERSON><PERSON>", "debug.help.help": "F3 + Q = Mostrar esta lista", "debug.help.message": "Atajos:", "debug.inspect.client.block": "Se copiaron los datos de bloques del cliente al portapapeles", "debug.inspect.client.entity": "Se copiaron los datos de entidades del cliente al portapapeles", "debug.inspect.help": "F3 + I = Copiar datos de entidad o bloque al portapapeles", "debug.inspect.server.block": "Se copiaron los datos de bloques del server al portapapeles", "debug.inspect.server.entity": "Se copiaron los datos de entidades del server al portapapeles", "debug.pause.help": "F3 + Esc = Pausa sin acceder al menú de pausa (solo si es posible)", "debug.pause_focus.help": "F3 + P = Pausar juego al cambiar de ventana", "debug.pause_focus.off": "Pausar al minimizar pantalla: no", "debug.pause_focus.on": "Pausar al minimizar pantalla: sí", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Iniciar/detener análisis de rendimiento", "debug.profiling.start": "Análisis iniciado por %s segundos. Usa F3 + L para interrumpirlo", "debug.profiling.stop": "Análisis finalizado. Resultados guardados en %s", "debug.reload_chunks.help": "F3 + A = Recargar chunks", "debug.reload_chunks.message": "Recargando todos los chunks", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON><PERSON> paquetes de recursos", "debug.reload_resourcepacks.message": "Paquetes de recursos recargados", "debug.show_hitboxes.help": "F3 + B = Mostrar cajas de colisiones", "debug.show_hitboxes.off": "Cajas de colisiones: ocultar", "debug.show_hitboxes.on": "Cajas de colisiones: mostrar", "debug.version.header": "Información de la versión del cliente:", "debug.version.help": "F3 + V = Información de la versión del cliente", "demo.day.1": "Esta demo durará 5 días del juego. ¡Aprovechalos!", "demo.day.2": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.4": "Cuarto día", "demo.day.5": "¡Este es tu último día!", "demo.day.6": "Ya pasó el quinto día. Usa %s para sacarle una screenshot a tu creación.", "demo.day.warning": "¡Se te va a acabar el tiempo!", "demo.demoExpired": "¡La demo ha finalizado!", "demo.help.buy": "¡<PERSON>mprar ahora!", "demo.help.fullWrapped": "Esta demo durará 5 días del juego (al rededor de 1 hora y 40 minutos de tiempo real). ¡Revisa los progresos para obtener pistas! ¡Diviértete!", "demo.help.inventory": "Presiona %1$s para abrir el inventario", "demo.help.jump": "Presiona %1$s para saltar", "demo.help.later": "¡Seguir jugando!", "demo.help.movement": "Usa %1$s,%2$s, %3$s, %4$s para moverte y el mouse para mirar", "demo.help.movementMouse": "<PERSON>a el mouse para mirar a tu alrededor", "demo.help.movementShort": "Presiona %1$s, %2$s, %3$s o %4$s para moverte", "demo.help.title": "Demo de Minecraft", "demo.remainingTime": "Tiempo restante: %s", "demo.reminder": "La demo ha terminado. ¡Puedes comprar el juego para continuar y crear un nuevo mundo!", "difficulty.lock.question": "¿Seguro que quieres bloquear la dificultad de este mundo? Esto lo establecerá siempre en %1$s y no podrás volver a cambiarlo.", "difficulty.lock.title": "Bloquear la dificultad del mundo", "disconnect.endOfStream": "Fin de la conexión", "disconnect.exceeded_packet_rate": "Expulsado por exceder límite de frecuencia de paquetes", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorando solicitud de estado", "disconnect.loginFailedInfo": "Error al iniciar sesión: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El modo multijugador está desactivado. Por favor, revisa los ajustes de tu cuenta de Microsoft.", "disconnect.loginFailedInfo.invalidSession": "La sesión no es válida (prueba a reiniciar el juego y el launcher)", "disconnect.loginFailedInfo.serversUnavailable": "No se puede acceder a los servers de autentificación. Por favor inténtalo nuevamente.", "disconnect.loginFailedInfo.userBanned": "Estas baneado del juego Online", "disconnect.lost": "Conexión perdida", "disconnect.packetError": "Error de protocolo de red", "disconnect.spam": "Expulsado por hacer spam", "disconnect.timeout": "Tiempo de espera agotado", "disconnect.transfer": "Transferido a otro servidor", "disconnect.unknownHost": "Host desconocido", "download.pack.failed": "No se pudo descargar %s de %s paquetes", "download.pack.progress.bytes": "Progreso: %s (tamaño total desconocido)", "download.pack.progress.percent": "Progreso: %s%%", "download.pack.title": "Descargando paquete de recursos %s/%s", "editGamerule.default": "Por defecto: %s", "editGamerule.title": "Editar las reglas del juego", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorción", "effect.minecraft.bad_omen": "Mal presagio", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Canalización", "effect.minecraft.darkness": "Oscuridad", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON>", "effect.minecraft.fire_resistance": "Resistencia í<PERSON>a", "effect.minecraft.glowing": "Luminiscencia", "effect.minecraft.haste": "Apuro minero", "effect.minecraft.health_boost": "<PERSON><PERSON> mejorada", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON> aldea", "effect.minecraft.hunger": "Hambre", "effect.minecraft.infested": "Infestación", "effect.minecraft.instant_damage": "<PERSON><PERSON>", "effect.minecraft.instant_health": "Curación instantánea", "effect.minecraft.invisibility": "Invisibilidad", "effect.minecraft.jump_boost": "Supersalto", "effect.minecraft.levitation": "Levitación", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Fatiga minera", "effect.minecraft.nausea": "Náuseas", "effect.minecraft.night_vision": "Visión nocturna", "effect.minecraft.oozing": "Secreción", "effect.minecraft.poison": "Veneno", "effect.minecraft.raid_omen": "Presagio de invasión", "effect.minecraft.regeneration": "Regeneración", "effect.minecraft.resistance": "Resistencia", "effect.minecraft.saturation": "Saturación", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitud", "effect.minecraft.speed": "Velocidad", "effect.minecraft.strength": "Fuerza", "effect.minecraft.trial_omen": "Presagio de desafío", "effect.minecraft.unluck": "<PERSON><PERSON> suerte", "effect.minecraft.water_breathing": "Respiración", "effect.minecraft.weakness": "Debilidad", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Hinchazón", "effect.minecraft.wither": "<PERSON>er", "effect.none": "Sin efectos", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinidad acuática", "enchantment.minecraft.bane_of_arthropods": "Perdición de los artrópodos", "enchantment.minecraft.binding_curse": "Maldición de ligamiento", "enchantment.minecraft.blast_protection": "Protección contra explosiones", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Conductividad", "enchantment.minecraft.density": "Densidad", "enchantment.minecraft.depth_strider": "Agilidad acuática", "enchantment.minecraft.efficiency": "Eficiencia", "enchantment.minecraft.feather_falling": "Caída de pluma", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Protección contra el fuego", "enchantment.minecraft.flame": "Fuego", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Paso helado", "enchantment.minecraft.impaling": "Empalamiento", "enchantment.minecraft.infinity": "Infinidad", "enchantment.minecraft.knockback": "Empuje", "enchantment.minecraft.looting": "Saqueo", "enchantment.minecraft.loyalty": "Lealtad", "enchantment.minecraft.luck_of_the_sea": "Suerte de los mares", "enchantment.minecraft.lure": "Atracción", "enchantment.minecraft.mending": "Reparación", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "Perforación", "enchantment.minecraft.power": "<PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Protección contra proyectiles", "enchantment.minecraft.protection": "Protección", "enchantment.minecraft.punch": "Retroceso", "enchantment.minecraft.quick_charge": "Carga rápida", "enchantment.minecraft.respiration": "Respiración", "enchantment.minecraft.riptide": "Corriente marina", "enchantment.minecraft.sharpness": "<PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Toque de seda", "enchantment.minecraft.smite": "Castigo", "enchantment.minecraft.soul_speed": "Velocidad de almas", "enchantment.minecraft.sweeping": "<PERSON>lo a<PERSON>or", "enchantment.minecraft.sweeping_edge": "<PERSON>lo a<PERSON>or", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Espinas", "enchantment.minecraft.unbreaking": "Irrompibilidad", "enchantment.minecraft.vanishing_curse": "Maldición de desaparición", "enchantment.minecraft.wind_burst": "Aeroimpulso", "entity.minecraft.acacia_boat": "Bote de acacia", "entity.minecraft.acacia_chest_boat": "Bote de acacia con cofre", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nube de efecto persistente", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Soporte para armadura", "entity.minecraft.arrow": "Fle<PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Balsa de bambú con cofre", "entity.minecraft.bamboo_raft": "Balsa de bambú", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "Bote de abedul con cofre", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Display de bloque", "entity.minecraft.boat": "Bo<PERSON>", "entity.minecraft.bogged": "Ciénago", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Carga de viento", "entity.minecraft.camel": "<PERSON>llo", "entity.minecraft.cat": "Gato", "entity.minecraft.cave_spider": "<PERSON><PERSON>", "entity.minecraft.cherry_boat": "<PERSON><PERSON> c<PERSON>", "entity.minecraft.cherry_chest_boat": "<PERSON>te de cerezo con cofre", "entity.minecraft.chest_boat": "<PERSON>te con cofre", "entity.minecraft.chest_minecart": "Vagón con cofre", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Bacalao", "entity.minecraft.command_block_minecart": "Vagón con bloque de comandos", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Bote de roble oscuro", "entity.minecraft.dark_oak_chest_boat": "Bote de roble oscuro con cofre", "entity.minecraft.dolphin": "Delfín", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bola de fuego de dragón", "entity.minecraft.drowned": "Drowned", "entity.minecraft.egg": "<PERSON><PERSON> lanza<PERSON>", "entity.minecraft.elder_guardian": "Guardián mayor", "entity.minecraft.end_crystal": "Cristal del End", "entity.minecraft.ender_dragon": "Enderdragón", "entity.minecraft.ender_pearl": "<PERSON><PERSON> de ender la<PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Invocador", "entity.minecraft.evoker_fangs": "Colmillos de invocador", "entity.minecraft.experience_bottle": "Botella de encantamientos lanzada", "entity.minecraft.experience_orb": "Esfera de experiencia", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>", "entity.minecraft.falling_block": "Bloque cayendo", "entity.minecraft.falling_block_type": "%s cayendo", "entity.minecraft.fireball": "Bola de fuego", "entity.minecraft.firework_rocket": "Cohete de fuegos artificiales", "entity.minecraft.fishing_bobber": "Boya de pesca", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Vagón con <PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Zombi gigante", "entity.minecraft.glow_item_frame": "<PERSON> brillante", "entity.minecraft.glow_squid": "Calamar brillante", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardián", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON> feliz", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vagón con tolva", "entity.minecraft.horse": "Caballo", "entity.minecraft.husk": "Husk", "entity.minecraft.illusioner": "Ilusionador", "entity.minecraft.interaction": "Interacción", "entity.minecraft.iron_golem": "Gólem de <PERSON>ro", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Display de ítem", "entity.minecraft.item_frame": "<PERSON>", "entity.minecraft.jungle_boat": "<PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON> de jungla con cofre", "entity.minecraft.killer_bunny": "El conejo asesino", "entity.minecraft.leash_knot": "<PERSON><PERSON> de cuerda", "entity.minecraft.lightning_bolt": "Rayo", "entity.minecraft.lingering_potion": "Poción persistente", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Escupitajo de llama", "entity.minecraft.magma_cube": "Cubo de magma", "entity.minecraft.mangrove_boat": "<PERSON><PERSON> de mangle", "entity.minecraft.mangrove_chest_boat": "Bote de mangle con cofre", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "Vagón", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON>te de roble con cofre", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Generador de objetos siniestro", "entity.minecraft.painting": "Cuadro", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON> de roble p<PERSON>", "entity.minecraft.pale_oak_chest_boat": "Bote de roble pálido con cofre", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> bruto", "entity.minecraft.pillager": "Saqueador", "entity.minecraft.player": "Jugador(a)", "entity.minecraft.polar_bear": "Oso polar", "entity.minecraft.potion": "Poción", "entity.minecraft.pufferfish": "Pez globo", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Devastador", "entity.minecraft.salmon": "Salmón", "entity.minecraft.sheep": "Oveja", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Proyectil de <PERSON>ker", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Esqueleto", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON> esqueleto", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Bola de fuego pequeña", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Gólem de nieve", "entity.minecraft.snowball": "<PERSON><PERSON> de <PERSON>eve", "entity.minecraft.spawner_minecart": "Vagón con spawner", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Poción arrojadiza", "entity.minecraft.spruce_boat": "Bo<PERSON> de a<PERSON>", "entity.minecraft.spruce_chest_boat": "Bote de abeto con cofre", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "Stray", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Display de texto", "entity.minecraft.tnt": "TNT activa", "entity.minecraft.tnt_minecart": "Vagón con TNT", "entity.minecraft.trader_llama": "Llama ambulante", "entity.minecraft.trident": "Tridente", "entity.minecraft.tropical_fish": "Pez tropical", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Pez cirujano negro", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON> moro", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON> mariposa adornado", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> loro", "entity.minecraft.tropical_fish.predefined.13": "Guinea", "entity.minecraft.tropical_fish.predefined.14": "Cíclido rojo", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "Pargo rojo", "entity.minecraft.tropical_fish.predefined.17": "Barbudo", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON> payaso tomate", "entity.minecraft.tropical_fish.predefined.19": "Pez ballesta", "entity.minecraft.tropical_fish.predefined.2": "Pez cirujano azul", "entity.minecraft.tropical_fish.predefined.20": "Pez loro de cola amarilla", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON> cirujano amarillo", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON> mariposa", "entity.minecraft.tropical_fish.predefined.4": "Cíclido", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON> payaso", "entity.minecraft.tropical_fish.predefined.6": "Betta algodón de azúcar", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "Pargo rojo emperador", "entity.minecraft.tropical_fish.predefined.9": "Salmonete", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON> betta", "entity.minecraft.tropical_fish.type.blockfish": "Pez bloque", "entity.minecraft.tropical_fish.type.brinely": "De agua salada", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON> arcilla", "entity.minecraft.tropical_fish.type.dasher": "A rayas", "entity.minecraft.tropical_fish.type.flopper": "Saltarín", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "De arrecife", "entity.minecraft.tropical_fish.type.spotty": "A puntos", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Pez rayo de sol", "entity.minecraft.turtle": "Tortuga", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Aldeano", "entity.minecraft.villager.armorer": "Escudero", "entity.minecraft.villager.butcher": "Carnicero", "entity.minecraft.villager.cartographer": "Cartógrafo", "entity.minecraft.villager.cleric": "Sacerdote", "entity.minecraft.villager.farmer": "Granjero", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Librero", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Imb<PERSON>cil", "entity.minecraft.villager.none": "Aldeano", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Armero", "entity.minecraft.vindicator": "Vindicador", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON> ambulante", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Carga de viento", "entity.minecraft.witch": "Bruja", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Esquelet<PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "Lobo", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON> zombi", "entity.minecraft.zombie_villager": "Aldeano zombi", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> zomb<PERSON>ado", "entity.not_summonable": "No se pudo generar la entidad de tipo %s", "event.minecraft.raid": "Invasión", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Invasión - Derrota", "event.minecraft.raid.raiders_remaining": "Invasores restantes: %s", "event.minecraft.raid.victory": "Victoria", "event.minecraft.raid.victory.full": "Invasión - Victoria", "filled_map.buried_treasure": "Mapa del tesoro", "filled_map.explorer_jungle": "Mapa de exploración de jungla", "filled_map.explorer_swamp": "Mapa de exploración de pantano", "filled_map.id": "ID: %s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Bloqueado", "filled_map.mansion": "Mapa de exploración de bosques", "filled_map.monument": "Mapa de exploración del océano", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Mapa de calabozo de desafío", "filled_map.unknown": "Mapa desconocido", "filled_map.village_desert": "Mapa de exploración de desierto", "filled_map.village_plains": "Mapa de exploración de planicie", "filled_map.village_savanna": "Mapa de exploración de sabana", "filled_map.village_snowy": "Mapa de aldea nevada", "filled_map.village_taiga": "Mapa de aldea de taiga", "flat_world_preset.minecraft.bottomless_pit": "Pozo sin fondo", "flat_world_preset.minecraft.classic_flat": "Clásico", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Mundo superficial", "flat_world_preset.minecraft.redstone_ready": "Listo para redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON> ne<PERSON>do", "flat_world_preset.minecraft.the_void": "El vacío", "flat_world_preset.minecraft.tunnelers_dream": "Sueño del minero", "flat_world_preset.minecraft.water_world": "Mundo acuático", "flat_world_preset.unknown": "¿¿??", "gameMode.adventure": "Aventura", "gameMode.changed": "Modo de juego cambiado a %s", "gameMode.creative": "Creativo", "gameMode.hardcore": "Extremo", "gameMode.spectator": "Espectador", "gameMode.survival": "Supervivencia", "gamerule.allowFireTicksAwayFromPlayer": "Cargar fuego lejos de los jugadores", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla si el fuego o la lava deberían estar cargados al estar a más de 8 chunks de distancia de cualquier jugador", "gamerule.announceAdvancements": "Anunciar progresos", "gamerule.blockExplosionDropDecay": "En explosiones por bloques, algunos bloques no soltarán ítems", "gamerule.blockExplosionDropDecay.description": "Algunos de los ítems soltados por bloques destruidos en explosiones que son causadas por interacciones con bloques se pierden en la explosión.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Varios", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Jugador", "gamerule.category.spawning": "Generación de criaturas", "gamerule.category.updates": "Actualizaciones del mundo", "gamerule.commandBlockOutput": "Notificar uso de bloques de comandos", "gamerule.commandModificationBlockLimit": "Límite de bloques modificables por comandos", "gamerule.commandModificationBlockLimit.description": "Número de bloques que pueden ser modificados por un solo comando, como \"fill\" o \"clone\".", "gamerule.disableElytraMovementCheck": "Deshabilitar verificación de elytras", "gamerule.disablePlayerMovementCheck": "Deshabilitar chequeo de movimiento del jugador", "gamerule.disableRaids": "Desactivar invasiones", "gamerule.doDaylightCycle": "Avanzar ciclo de día y noche", "gamerule.doEntityDrops": "<PERSON><PERSON> caer <PERSON>tems de entidades", "gamerule.doEntityDrops.description": "Controla drops de vagones (incluyendo sus inventarios), marcos, botes, etc.", "gamerule.doFireTick": "Propagación del fuego", "gamerule.doImmediateRespawn": "Reaparición instantánea", "gamerule.doInsomnia": "<PERSON>rar phantoms", "gamerule.doLimitedCrafting": "Exigir recetas para poder fabricar", "gamerule.doLimitedCrafting.description": "Si se activa, solo se podrán craftear recetas aprendidas.", "gamerule.doMobLoot": "Obtener loot de criaturas", "gamerule.doMobLoot.description": "Controla si los mobs sueltan o no recursos, incluyendo orbes de experiencia.", "gamerule.doMobSpawning": "Generar mobs", "gamerule.doMobSpawning.description": "Algunas entidades podrían tener reglas distintas.", "gamerule.doPatrolSpawning": "Generar patrullas de saqueadores", "gamerule.doTileDrops": "Soltar bloques", "gamerule.doTileDrops.description": "Controla los drops de bloques, incluyendo orbes de experiencia.", "gamerule.doTraderSpawning": "Generar vendedores ambulantes", "gamerule.doVinesSpread": "Propagación de enredaderas", "gamerule.doVinesSpread.description": "Controla si las enredaderas se propagan en bloques adyacentes al azar. No afecta a otro tipo de enredaderas, como enredaderas lloronas, enredaderas retorcidas, etcétera.", "gamerule.doWardenSpawning": "Generar wardens", "gamerule.doWeatherCycle": "Ciclo meteorológico", "gamerule.drowningDamage": "<PERSON>ño por ahogamiento", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON><PERSON> perlas de ender al morir", "gamerule.enderPearlsVanishOnDeath.description": "Las perlas de ender lanzadas por un jugador se romperán si ese jugador muere.", "gamerule.entitiesWithPassengersCanUsePortals": "Las entidades con pasajeros pueden usar portales", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permite que las entidades con pasajeros puedan viajar a través de portales al Nether, al End, y accesos al End.", "gamerule.fallDamage": "Daño por caída", "gamerule.fireDamage": "Daño por fuego", "gamerule.forgiveDeadPlayers": "Perdonar jugadores muertos", "gamerule.forgiveDeadPlayers.description": "Los mobs neutrales que estén enojados dejan de estar enojados cuando el jugador seleccionado muere cerca.", "gamerule.freezeDamage": "Daño por congelamiento", "gamerule.globalSoundEvents": "Eventos de sonido globales", "gamerule.globalSoundEvents.description": "<PERSON><PERSON><PERSON> o<PERSON>rran ciertos eventos del juego, como la generación de un boss, el sonido se oirá en todas partes.", "gamerule.keepInventory": "Mantener inventario al morir", "gamerule.lavaSourceConversion": "La lava se convierte en fuente", "gamerule.lavaSourceConversion.description": "Cuando la lava que fluye es rodeada en dos lados por fuentes de lava, esta se convertirá también en fuente.", "gamerule.locatorBar": "Activar barra localizadora de jugadores", "gamerule.locatorBar.description": "Al activarlo, aparece una barra en la pantalla que muestra la dirección de los jugadores.", "gamerule.logAdminCommands": "Avisar si un Admin utilizó comandos", "gamerule.maxCommandChainLength": "Límite de comandos en cadena", "gamerule.maxCommandChainLength.description": "Se aplica a las cadenas de bloques de comandos y funciones.", "gamerule.maxCommandForkCount": "Límite de circunstancias por comando", "gamerule.maxCommandForkCount.description": "El número máximo de circunstancias que pueden ser usadas por comandos como 'execute as'.", "gamerule.maxEntityCramming": "Límite de entidades por bloque", "gamerule.minecartMaxSpeed": "Velocidad máxima de vagones", "gamerule.minecartMaxSpeed.description": "Velocidad máxima por defecto de los vagones en movimiento por tierra.", "gamerule.mobExplosionDropDecay": "En las explosiones por mobs, algunos bloques no soltarán ítems", "gamerule.mobExplosionDropDecay.description": "Algunos de los ítems de bloques destruídos por explosiones causadas por mobs se pierden en la explosión.", "gamerule.mobGriefing": "Permitir que las criaturas destruyan", "gamerule.naturalRegeneration": "Regenerar vida", "gamerule.playersNetherPortalCreativeDelay": "Delay del portal del Nether en creativo", "gamerule.playersNetherPortalCreativeDelay.description": "Tiempo (en ticks) que un jugador necesita esperar para cambiar de dimensión al pararse en un portal del Nether en modo creativo.", "gamerule.playersNetherPortalDefaultDelay": "Delay del portal del Nether predeterminado", "gamerule.playersNetherPortalDefaultDelay.description": "Tiempo (en ticks) que un jugador necesita esperar para cambiar de dimensión al pararse en un portal del Nether en cualquier modo de juego distinto a creativo.", "gamerule.playersSleepingPercentage": "Porcentaje de jugadores para dormir", "gamerule.playersSleepingPercentage.description": "El porcentaje de jugadores necesarios para saltar la noche al dormir.", "gamerule.projectilesCanBreakBlocks": "Los proyectiles pueden romper bloques", "gamerule.projectilesCanBreakBlocks.description": "Controla si los proyectiles pueden o no destruir los bloques que deberían al impactar.", "gamerule.randomTickSpeed": "Velocidad de tics al azar", "gamerule.reducedDebugInfo": "Reducir datos de F3", "gamerule.reducedDebugInfo.description": "Limita el contenido de la pantalla de debug.", "gamerule.sendCommandFeedback": "Mostrar respuestas de comandos", "gamerule.showDeathMessages": "<PERSON>rar mensajes de muertes", "gamerule.snowAccumulationHeight": "Altura de acumulación de nieve", "gamerule.snowAccumulationHeight.description": "<PERSON>uando esté nevando, se formarán capas de nieve en el piso hasta un máximo de capas de este número.", "gamerule.spawnChunkRadius": "Chunks de radio de aparición", "gamerule.spawnChunkRadius.description": "Cantidad de chunks que permanecen cargados alrededor del punto de aparición.", "gamerule.spawnRadius": "Radio de reaparición", "gamerule.spawnRadius.description": "Controla el tamaño del área del punto de aparición en el que aparecen los jugadores.", "gamerule.spectatorsGenerateChunks": "Generar chunks en modo espectador", "gamerule.tntExplodes": "Permitir explosiones de TNT", "gamerule.tntExplosionDropDecay": "En las explosiones por TNT, algunos bloques no soltarán ítems", "gamerule.tntExplosionDropDecay.description": "Algunos de los ítems de bloques destruidos por explosiones de TNT se pierden en la explosión.", "gamerule.universalAnger": "Enojo universal", "gamerule.universalAnger.description": "Los mobs neutrales enojados atacan a cualquier jugador que esté cerca, no solo al jugador que los haya enojado. Funciona mejor si la regla forgiveDeadPlayers está desactivada.", "gamerule.waterSourceConversion": "El agua se convierte en fuente", "gamerule.waterSourceConversion.description": "Cuando agua fluyendo es rodeada de fuentes de agua por ambos lados, esta se convertirá también en fuente.", "generator.custom": "personalizado", "generator.customized": "Personalizado (antiguo)", "generator.minecraft.amplified": "AMPLIFICADO", "generator.minecraft.amplified.info": "Nota: ¡Solo por diversión! Requiere un computador potente.", "generator.minecraft.debug_all_block_states": "Debug", "generator.minecraft.flat": "Extraplano", "generator.minecraft.large_biomes": "Superbiomas", "generator.minecraft.normal": "Por defecto", "generator.minecraft.single_biome_surface": "Bioma único", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON> flotantes", "gui.abuseReport.attestation": "Al enviar este reporte, confirmas que la información que proporcionas es correcta y considera todo lo que sepas.", "gui.abuseReport.comments": "Observaciones", "gui.abuseReport.describe": "Compartir detalles nos ayudará a tomar una decisión bien informada.", "gui.abuseReport.discard.content": "Si sales ahora, este reporte y tus observaciones no se guardarán.\n¿Estás seguro de que quieres salir?", "gui.abuseReport.discard.discard": "Salir y descartar reporte", "gui.abuseReport.discard.draft": "Guardar borrador", "gui.abuseReport.discard.return": "Con<PERSON><PERSON>r editando", "gui.abuseReport.discard.title": "¿Descartar reporte y observaciones?", "gui.abuseReport.draft.content": "¿Quieres seguir editando el reporte existente o descartarlo y crear uno nuevo?", "gui.abuseReport.draft.discard": "Descar<PERSON>", "gui.abuseReport.draft.edit": "Con<PERSON><PERSON>r editando", "gui.abuseReport.draft.quittotitle.content": "¿Quieres seguir editán<PERSON>lo o descartarlo?", "gui.abuseReport.draft.quittotitle.title": "Tienes un borrador de reporte de chat que se perderá si sales", "gui.abuseReport.draft.title": "¿Editar borrador de reporte de chat?", "gui.abuseReport.error.title": "Ocurrió un problema al enviar el reporte", "gui.abuseReport.message": "¿Dónde identificaste el mal comportamiento?\nEsto nos ayudará en la investigación de tu caso.", "gui.abuseReport.more_comments": "Por favor, describe la situación:", "gui.abuseReport.name.comment_box_label": "Describe por qué quieres denunciar este nombre:", "gui.abuseReport.name.reporting": "Estás reportando a \"%s\".", "gui.abuseReport.name.title": "Reportar nombre inapropiado", "gui.abuseReport.observed_what": "¿Por qué estás reportando esto?", "gui.abuseReport.read_info": "Acerca de reportar", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogas o alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Alguien está incentivando a otros a formar parte de actos ilegales relacionados a drogas o consumir alcohol siendo menor de edad.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Sexualización o abuso infantil", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Alguien está hablando de, o promoviendo comportamientos indecentes que involucran a menores.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamación", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Alguien está dañando la reputación de otra persona, compartiendo, por ejemplo, información falsa con el objetivo de aprovecharse o engañar a otros.", "gui.abuseReport.reason.description": "Descripción:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON>cia falsa", "gui.abuseReport.reason.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.generic.description": "Me molestó / hizo algo que no me agrada.", "gui.abuseReport.reason.harassment_or_bullying": "Bullying o acoso", "gui.abuseReport.reason.harassment_or_bullying.description": "Alguien está hostigando, atacando o acosándote a ti o a otra persona. Esto incluye haber intentado contactar repetidamente contigo o con otra persona o publicar información personal privada sobre ti u otra persona sin consentimiento (doxeo).", "gui.abuseReport.reason.hate_speech": "Mensajes de odio", "gui.abuseReport.reason.hate_speech.description": "Alguien te está atacando a ti o a otro jugador en función de las características de su identidad, como la religión, la raza o la sexualidad.", "gui.abuseReport.reason.imminent_harm": "Amenaza de daños a otros", "gui.abuseReport.reason.imminent_harm.description": "Alguien está amenazando con hacerte daño a ti o a otra persona en la vida real.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imágenes íntimas sin consentimiento", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Alguien está pidiendo, compartiendo o de otra forma impulsando al tráfico de imágenes privadas e íntimas.", "gui.abuseReport.reason.self_harm_or_suicide": "Autolesión o suicidio", "gui.abuseReport.reason.self_harm_or_suicide.description": "Alguien amenaza con autolesionarse en la vida real o habla acerca de hacerse daño en la vida real.", "gui.abuseReport.reason.sexually_inappropriate": "Contenido sexual", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins que refieren a actos sexuales, órganos sexuales y violencia sexual.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismo o extremismo violento", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Alguien está hablando, promoviendo o amenazando con cometer actos de terrorismo o extremismo violento por razones políticas, religiosas, ideológicas u otras razones.", "gui.abuseReport.reason.title": "Seleccionar categoria de reporte", "gui.abuseReport.report_sent_msg": "Hemos recibido tu reporte correctamente. ¡Muchas gracias!\n\nNuestro equipo lo revisará cuanto antes.", "gui.abuseReport.select_reason": "Seleccionar categoria de reporte", "gui.abuseReport.send": "Enviar reporte", "gui.abuseReport.send.comment_too_long": "Por favor, resume la observación", "gui.abuseReport.send.error_message": "Ocurrió un error al enviar tu reporte: \n'%s'", "gui.abuseReport.send.generic_error": "Se ha producido un error inesperado al enviar tu reporte.", "gui.abuseReport.send.http_error": "Se ha producido un error HTTP inesperado al enviar tu denuncia.", "gui.abuseReport.send.json_error": "Se ha detectado un paquete mal estructurado al enviar tu denuncia.", "gui.abuseReport.send.no_reason": "Por favor, selecciona una categoría para el reporte", "gui.abuseReport.send.not_attested": "Por favor, lee el texto que está arriba y marca la casilla para poder enviar el reporte", "gui.abuseReport.send.service_unavailable": "No se ha podido comunicar con el servicio de Reportes de Abuso. Por favor, revisa tu conexión a internet e inténtalo nuevamente.", "gui.abuseReport.sending.title": "Enviando tu denuncia...", "gui.abuseReport.sent.title": "Reporte enviado", "gui.abuseReport.skin.title": "Reportar skin de jugador", "gui.abuseReport.title": "<PERSON>ar jugador", "gui.abuseReport.type.chat": "Mensajes del chat", "gui.abuseReport.type.name": "Nombre del jugador", "gui.abuseReport.type.skin": "Skin del jugador", "gui.acknowledge": "Entendido", "gui.advancements": "Progresos", "gui.all": "<PERSON><PERSON>", "gui.back": "Volver", "gui.banned.description": "%s\n\n%s\n\nMás información en el siguiente link: %s", "gui.banned.description.permanent": "Tu cuenta esta baneada permanentemente, lo que significa que no podrás jugar en línea ni unirte a Realms.", "gui.banned.description.reason": "Recientemente recibimos una denuncia por mal comportamiento por parte de tu cuenta. Nuestros moderadores han revisado el caso y lo han identificado como %s, lo que va en contra de las Normas de la Comunidad de Minecraft.", "gui.banned.description.reason_id": "Código: %s", "gui.banned.description.reason_id_message": "Código: %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON> entonces, no podrás jugar en línea o unirte a Realms.", "gui.banned.description.temporary.duration": "Tu cuenta ha sido temporalmente suspendida y será reactivada en %s.", "gui.banned.description.unknownreason": "Hace poco recibimos una denuncia por mal comportamiento por parte de tu cuenta. Nuestros moderadores han revisado el caso e identificaron que va en contra de las Normas de la Comunidad de Minecraft.", "gui.banned.name.description": "Tu nombre actual, \"%s\", incumple las Normas de la Comunidad. Puedes jugar en mundos de un jugador, pero deber<PERSON> cambiar tu nombre para poder jugar en multijugador.\n\nAprende más o solicita una revisión del caso en el siguiente link: %s", "gui.banned.name.title": "Nombre no permitido en multijugador", "gui.banned.reason.defamation_impersonation_false_information": "Suplantación o intercambio de información para engañar o aprovecharse de otros", "gui.banned.reason.drugs": "Referencias a drogas ilegales", "gui.banned.reason.extreme_violence_or_gore": "Representaciones de violencia extrema o gore realista", "gui.banned.reason.false_reporting": "Demas<PERSON>dos reportes falsos o inexactos", "gui.banned.reason.fraud": "Adquisición o uso de contenido fraudulento", "gui.banned.reason.generic_violation": "Violación de normas de la comunidad", "gui.banned.reason.harassment_or_bullying": "Lenguaje abusivo utilizado de manera dirigida y dañina", "gui.banned.reason.hate_speech": "Discurso de odio o discriminación", "gui.banned.reason.hate_terrorism_notorious_figure": "Referencias a grupos de odio, organizaciones terroristas o figuras notorias", "gui.banned.reason.imminent_harm_to_person_or_property": "Intención de causar daños a personas o bienes en la vida real", "gui.banned.reason.nudity_or_pornography": "Exhibición de material obsceno o pornográfico", "gui.banned.reason.sexually_inappropriate": "Temas o contenido de naturaleza sexual", "gui.banned.reason.spam_or_advertising": "Spam o publicidad", "gui.banned.skin.description": "Tu skin actual incumple las Normas de la Comunidad. Podrás seguir jugando con una skin predeterminada o cambiando tu skin a una distinta.\n\nAprende más o solicita una revisión del caso en el siguiente link: %s", "gui.banned.skin.title": "Skin no permitida", "gui.banned.title.permanent": "Cuenta baneada permanentemente", "gui.banned.title.temporary": "Cuenta temporalmente suspendida", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Observaciones", "gui.chatReport.describe": "Compartir detalles nos ayudará a tomar una decisión bien informada.", "gui.chatReport.discard.content": "Si sales ahora, este reporte y sus observaciones no se guardarán.\n¿Estás seguro de que quieres salir?", "gui.chatReport.discard.discard": "Salir y descartar reporte", "gui.chatReport.discard.draft": "Guardar borrador", "gui.chatReport.discard.return": "Con<PERSON><PERSON>r editando", "gui.chatReport.discard.title": "¿Descartar reporte y observaciones?", "gui.chatReport.draft.content": "¿Quieres seguir editando el reporte existente o descartarlo y crear uno nuevo?", "gui.chatReport.draft.discard": "Descar<PERSON>", "gui.chatReport.draft.edit": "Con<PERSON><PERSON>r editando", "gui.chatReport.draft.quittotitle.content": "¿Quieres seguir editán<PERSON>lo o descartarlo?", "gui.chatReport.draft.quittotitle.title": "Tienes un borrador de reporte que se perderá si te sales", "gui.chatReport.draft.title": "¿Editar borrador de reporte?", "gui.chatReport.more_comments": "Por favor, describe la situación:", "gui.chatReport.observed_what": "¿Por qué estás reportando esto?", "gui.chatReport.read_info": "Acerca de Reportar", "gui.chatReport.report_sent_msg": "Hemos recibido tu reporte correctamente. ¡Muchas gracias!\n\nNuestro equipo lo revisará cuanto antes.", "gui.chatReport.select_chat": "Selecciona los mensajes del chat a reportar", "gui.chatReport.select_reason": "Selecciona una categoría de reporte", "gui.chatReport.selected_chat": "%s mensaje(s) seleccionados para reportar", "gui.chatReport.send": "Enviar reporte", "gui.chatReport.send.comments_too_long": "Por favor, resume la observación", "gui.chatReport.send.no_reason": "Por favor, elige una categoria para el reporte", "gui.chatReport.send.no_reported_messages": "Por favor, selecciona al menos un mensaje del chat para reportar", "gui.chatReport.send.too_many_messages": "Intentando incluir muchos mensajes en el reporte", "gui.chatReport.title": "Denunciar mensa<PERSON> de jugador", "gui.chatSelection.context": "Los mensajes cercanos a esta selección se incluirán para proporcionar contexto adicional", "gui.chatSelection.fold": "%s mensajes ocultos", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s entró al chat", "gui.chatSelection.message.narrate": "%s dijo: %s en %s", "gui.chatSelection.selected": "%s de %s mensajes seleccionados", "gui.chatSelection.title": "Elegir mensajes del chat a reportar", "gui.continue": "<PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copiar link al portapapeles", "gui.days": "%s día(s)", "gui.done": "Aceptar", "gui.down": "Abajo", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s archivos rechazados", "gui.fileDropFailure.title": "Error al añadir archivos", "gui.hours": "%s hora(s)", "gui.loadingMinecraft": "Cargando Minecraft", "gui.minutes": "%s minuto(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "botón de %s", "gui.narrate.editBox": "Barra de texto de %s: %s", "gui.narrate.slider": "Barra de %s", "gui.narrate.tab": "Pestaña de %s", "gui.no": "No", "gui.none": "<PERSON><PERSON><PERSON>", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Abrir directorio de reportes", "gui.proceed": "Proceder", "gui.recipebook.moreRecipes": "Haz click derecho para más", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Buscar...", "gui.recipebook.toggleRecipes.all": "Mostrando todo", "gui.recipebook.toggleRecipes.blastable": "Mostrando fundibles", "gui.recipebook.toggleRecipes.craftable": "Mostrando crafteables", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON> horneables", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON> ahuma<PERSON>", "gui.report_to_server": "Informar al server", "gui.socialInteractions.blocking_hint": "Administrar con cuenta de Microsoft", "gui.socialInteractions.empty_blocked": "No hay jugadores bloqueados en el chat", "gui.socialInteractions.empty_hidden": "No hay jugadores ocultos en el chat", "gui.socialInteractions.hidden_in_chat": "Los mensajes de %s estarán ocultos en el chat", "gui.socialInteractions.hide": "Ocultar en el chat", "gui.socialInteractions.narration.hide": "Ocultar mensajes de %s", "gui.socialInteractions.narration.report": "Reportar jugador %s", "gui.socialInteractions.narration.show": "Mostrar mensajes de %s", "gui.socialInteractions.report": "Reportar", "gui.socialInteractions.search_empty": "No se ha encontrado ningún jugador con ese nombre", "gui.socialInteractions.search_hint": "Buscar...", "gui.socialInteractions.server_label.multiple": "%s - %s jugadores", "gui.socialInteractions.server_label.single": "%s - %s jugador", "gui.socialInteractions.show": "Mostrar en el chat", "gui.socialInteractions.shown_in_chat": "Los mensajes de %s serán visibles en el chat", "gui.socialInteractions.status_blocked": "Bloqueado", "gui.socialInteractions.status_blocked_offline": "Bloqueado - Sin conexión", "gui.socialInteractions.status_hidden": "Oculto", "gui.socialInteractions.status_hidden_offline": "Oculto - Sin conexión", "gui.socialInteractions.status_offline": "Sin conexión", "gui.socialInteractions.tab_all": "Todo", "gui.socialInteractions.tab_blocked": "Bloqueado", "gui.socialInteractions.tab_hidden": "Oculto", "gui.socialInteractions.title": "Interacciones sociales", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "<PERSON>ar jugador", "gui.socialInteractions.tooltip.report.disabled": "El servicio de reportes no está disponible", "gui.socialInteractions.tooltip.report.no_messages": "No hay mensajes del jugador %s para reportar", "gui.socialInteractions.tooltip.report.not_reportable": "No puedes reportar a este jugador. Sus mensajes no pueden verificarse en este server", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> mensajes", "gui.stats": "Estadísticas", "gui.toMenu": "Volver a la lista de servers", "gui.toRealms": "Volver a la lista de Realms", "gui.toTitle": "<PERSON>ver al menú principal", "gui.toWorld": "Volver a la lista de mundos", "gui.togglable_slot": "Haz clic para desactivar el espacio", "gui.up": "Arriba", "gui.waitingForResponse.button.inactive": "Volver (%ss)", "gui.waitingForResponse.title": "Esperando al server", "gui.yes": "Sí", "hanging_sign.edit": "Editar mensaje del cartel colgante", "instrument.minecraft.admire_goat_horn": "Admiración", "instrument.minecraft.call_goat_horn": "Llamada", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "Sen<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Reflexión", "instrument.minecraft.seek_goat_horn": "Búsqueda", "instrument.minecraft.sing_goat_horn": "Canto", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Bo<PERSON>r", "inventory.hotbarInfo": "Guarda la barra con %1$s+%2$s", "inventory.hotbarSaved": "Barra de ítems guardada (restáurala con %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON><PERSON> romper:", "item.canPlace": "Puede colocarse sobre:", "item.canUse.unknown": "Desconocido", "item.color": "Color: %s", "item.components": "%s componente(s)", "item.disabled": "Ítem deshabilitado", "item.durability": "Durabilidad: %s / %s", "item.dyed": "Teñido", "item.minecraft.acacia_boat": "Bote de acacia", "item.minecraft.acacia_chest_boat": "Bote de acacia con cofre", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON> allay", "item.minecraft.amethyst_shard": "Fragmento de amatista", "item.minecraft.angler_pottery_shard": "Fragmento de cerámica de pescador", "item.minecraft.angler_pottery_sherd": "Fragmento de cerámica de pescador", "item.minecraft.apple": "Man<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Fragmento de cerámica de arquero", "item.minecraft.archer_pottery_sherd": "Fragmento de cerámica de arquero", "item.minecraft.armadillo_scute": "Escama de armadillo", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON>", "item.minecraft.armor_stand": "Soporte para armadura", "item.minecraft.arms_up_pottery_shard": "Fragmento de cerámica de brazos arriba", "item.minecraft.arms_up_pottery_sherd": "Fragmento de cerámica de brazos arriba", "item.minecraft.arrow": "Fle<PERSON>", "item.minecraft.axolotl_bucket": "<PERSON><PERSON> con ajolote", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON>", "item.minecraft.baked_potato": "<PERSON>", "item.minecraft.bamboo_chest_raft": "Balsa de bambú con cofre", "item.minecraft.bamboo_raft": "Balsa de bambú", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON> abeja", "item.minecraft.beef": "<PERSON><PERSON> crudo", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Semillas de betarraga", "item.minecraft.beetroot_soup": "Sopa de betarragas", "item.minecraft.birch_boat": "<PERSON><PERSON>", "item.minecraft.birch_chest_boat": "Bote de abedul con cofre", "item.minecraft.black_bundle": "Saco negro", "item.minecraft.black_dye": "Tinte negro", "item.minecraft.black_harness": "Arnés negro", "item.minecraft.blade_pottery_shard": "Fragmento de cerámica de cuchilla", "item.minecraft.blade_pottery_sherd": "Fragmento de cerámica de cuchilla", "item.minecraft.blaze_powder": "Polvo de blaze", "item.minecraft.blaze_rod": "Barra de blaze", "item.minecraft.blaze_spawn_egg": "Generar blaze", "item.minecraft.blue_bundle": "Saco azul", "item.minecraft.blue_dye": "<PERSON>te a<PERSON>l", "item.minecraft.blue_egg": "Huevo azul", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Generar ci<PERSON>ago", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ornamento de remache", "item.minecraft.bone": "Hueso", "item.minecraft.bone_meal": "Polvo de hueso", "item.minecraft.book": "Libro", "item.minecraft.bordure_indented_banner_pattern": "Estampado de bordura dentada", "item.minecraft.bow": "Arco", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "Barra de breeze", "item.minecraft.breeze_spawn_egg": "Generar breeze", "item.minecraft.brewer_pottery_shard": "Fragmento de cerámica de alquimia", "item.minecraft.brewer_pottery_sherd": "Fragmento de cerámica de alquimia", "item.minecraft.brewing_stand": "Alambique", "item.minecraft.brick": "Ladrillo", "item.minecraft.brown_bundle": "Saco <PERSON>", "item.minecraft.brown_dye": "Tinte café", "item.minecraft.brown_egg": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "Brocha", "item.minecraft.bucket": "Balde", "item.minecraft.bundle": "Saco", "item.minecraft.bundle.empty": "Vacío", "item.minecraft.bundle.empty.description": "Puede guardar varios ítems", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Fragmento de cerámica de fogata", "item.minecraft.burn_pottery_sherd": "Fragmento de cerámica de fogata", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON> camello", "item.minecraft.carrot": "Zanahoria", "item.minecraft.carrot_on_a_stick": "Caña con zanahoria", "item.minecraft.cat_spawn_egg": "Generar gato", "item.minecraft.cauldron": "Calder<PERSON>", "item.minecraft.cave_spider_spawn_egg": "Generar a<PERSON> cue<PERSON>", "item.minecraft.chainmail_boots": "Botas de cota de mallas", "item.minecraft.chainmail_chestplate": "Pechera de cota de mallas", "item.minecraft.chainmail_helmet": "Casco de cota de mallas", "item.minecraft.chainmail_leggings": "Pantalones de cota de mallas", "item.minecraft.charcoal": "Carbón vegetal", "item.minecraft.cherry_boat": "<PERSON><PERSON> c<PERSON>", "item.minecraft.cherry_chest_boat": "<PERSON>te de cerezo con cofre", "item.minecraft.chest_minecart": "Vagón con cofre", "item.minecraft.chicken": "<PERSON><PERSON> crudo", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON> gallina", "item.minecraft.chorus_fruit": "Fruta chorus", "item.minecraft.clay_ball": "<PERSON><PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbón", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.coast_armor_trim_smithing_template.new": "Ornamento de costa", "item.minecraft.cocoa_beans": "Granos de cacao", "item.minecraft.cod": "Bacalao crudo", "item.minecraft.cod_bucket": "Balde con bacalao", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON> b<PERSON>", "item.minecraft.command_block_minecart": "Vagón con bloque de comandos", "item.minecraft.compass": "Brújula", "item.minecraft.cooked_beef": "<PERSON><PERSON> asado", "item.minecraft.cooked_chicken": "<PERSON><PERSON>", "item.minecraft.cooked_cod": "Bacalao asado", "item.minecraft.cooked_mutton": "Cordero asado", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON> asada", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "Salmón asado", "item.minecraft.cookie": "Galleta", "item.minecraft.copper_ingot": "Lingote de cobre", "item.minecraft.cow_spawn_egg": "Generar vaca", "item.minecraft.creaking_spawn_egg": "<PERSON><PERSON> crujidor", "item.minecraft.creeper_banner_pattern": "Diseño de estandarte", "item.minecraft.creeper_banner_pattern.desc": "Estampado de creeper", "item.minecraft.creeper_banner_pattern.new": "Estampado de creeper", "item.minecraft.creeper_spawn_egg": "Generar creeper", "item.minecraft.crossbow": "Ballesta", "item.minecraft.crossbow.projectile": "Proyectil:", "item.minecraft.crossbow.projectile.multiple": "Proyectil: %s x %s", "item.minecraft.crossbow.projectile.single": "Proyectil: %s", "item.minecraft.cyan_bundle": "Saco cian", "item.minecraft.cyan_dye": "<PERSON><PERSON> cian", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Fragmento de cerámica de peligro", "item.minecraft.danger_pottery_sherd": "Fragmento de cerámica de peligro", "item.minecraft.dark_oak_boat": "Bote de roble oscuro", "item.minecraft.dark_oak_chest_boat": "Bote de roble oscuro con cofre", "item.minecraft.debug_stick": "<PERSON>lo debug", "item.minecraft.debug_stick.empty": "%s no tiene propiedades", "item.minecraft.debug_stick.select": "seleccionado \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" cambió a %s", "item.minecraft.diamond": "Diamante", "item.minecraft.diamond_axe": "<PERSON><PERSON>", "item.minecraft.diamond_boots": "Botas de diamante", "item.minecraft.diamond_chestplate": "Pechera de diamante", "item.minecraft.diamond_helmet": "Casco de diamante", "item.minecraft.diamond_hoe": "Azadón de diamante", "item.minecraft.diamond_horse_armor": "Armadura de diamante para caballos", "item.minecraft.diamond_leggings": "Pantalones de diamante", "item.minecraft.diamond_pickaxe": "Picota de diamante", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Espada de diamante", "item.minecraft.disc_fragment_5": "Fragmento de disco", "item.minecraft.disc_fragment_5.desc": "Disco de música - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON> burro", "item.minecraft.dragon_breath": "<PERSON><PERSON>", "item.minecraft.dried_kelp": "Algas secas", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON> drowned", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.dune_armor_trim_smithing_template.new": "Ornamento de duna", "item.minecraft.echo_shard": "Fragmento resonante", "item.minecraft.egg": "Huevo", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON> mayor", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Esm<PERSON><PERSON>", "item.minecraft.enchanted_book": "Libro encantado", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> encantada", "item.minecraft.end_crystal": "Cristal del End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "Generar endermite", "item.minecraft.evoker_spawn_egg": "Generar invocador", "item.minecraft.experience_bottle": "Botella de encantamientos", "item.minecraft.explorer_pottery_shard": "Fragmento de cerámica de explorador", "item.minecraft.explorer_pottery_sherd": "Fragmento de cerámica de explorador", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.eye_armor_trim_smithing_template.new": "Ornamento de ojo", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Ojo de araña fermentado", "item.minecraft.field_masoned_banner_pattern": "Estampado de mampostería", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Carga ígnea", "item.minecraft.firework_rocket": "Cohete de fuegos artificiales", "item.minecraft.firework_rocket.flight": "Duración del vuelo:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Pólvora de fuegos artificiales", "item.minecraft.firework_star.black": "negra", "item.minecraft.firework_star.blue": "Azul", "item.minecraft.firework_star.brown": "Café", "item.minecraft.firework_star.custom_color": "Personalizado", "item.minecraft.firework_star.cyan": "cian", "item.minecraft.firework_star.fade_to": "Se vuelve", "item.minecraft.firework_star.flicker": "Centelleo", "item.minecraft.firework_star.gray": "gris", "item.minecraft.firework_star.green": "verde", "item.minecraft.firework_star.light_blue": "celeste", "item.minecraft.firework_star.light_gray": "gris claro", "item.minecraft.firework_star.lime": "verde lima", "item.minecraft.firework_star.magenta": "magenta", "item.minecraft.firework_star.orange": "naran<PERSON>", "item.minecraft.firework_star.pink": "rosada", "item.minecraft.firework_star.purple": "morada", "item.minecraft.firework_star.red": "roja", "item.minecraft.firework_star.shape": "Forma desconocida", "item.minecraft.firework_star.shape.burst": "Explosión", "item.minecraft.firework_star.shape.creeper": "Con forma de creeper", "item.minecraft.firework_star.shape.large_ball": "Esfera grande", "item.minecraft.firework_star.shape.small_ball": "Esfera pequeña", "item.minecraft.firework_star.shape.star": "Con forma de estrella", "item.minecraft.firework_star.trail": "Rastro", "item.minecraft.firework_star.white": "blanca", "item.minecraft.firework_star.yellow": "amarilla", "item.minecraft.fishing_rod": "Caña de pescar", "item.minecraft.flint": "Pedernal", "item.minecraft.flint_and_steel": "Encendedor", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.flow_armor_trim_smithing_template.new": "Ornamento de flujo", "item.minecraft.flow_banner_pattern": "Diseño de estandarte", "item.minecraft.flow_banner_pattern.desc": "Estampado de espiral", "item.minecraft.flow_banner_pattern.new": "Estampado de espiral", "item.minecraft.flow_pottery_sherd": "Fragmento de cerámica de flujo", "item.minecraft.flower_banner_pattern": "Diseño de estandarte", "item.minecraft.flower_banner_pattern.desc": "Estampado de flor", "item.minecraft.flower_banner_pattern.new": "Estampado de flor", "item.minecraft.flower_pot": "<PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON>", "item.minecraft.friend_pottery_shard": "Fragmento de cerámica de amigo", "item.minecraft.friend_pottery_sherd": "Fragmento de cerámica de amigo", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON> rana", "item.minecraft.furnace_minecart": "Vagón con horno", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "Lágrim<PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Pedazo de sandía brillante", "item.minecraft.globe_banner_pattern": "Diseño de estandarte", "item.minecraft.globe_banner_pattern.desc": "Estampado de planeta", "item.minecraft.globe_banner_pattern.new": "Estampado de planeta", "item.minecraft.glow_berries": "Bayas brillantes", "item.minecraft.glow_ink_sac": "Saco de tinta brillante", "item.minecraft.glow_item_frame": "<PERSON> brillante", "item.minecraft.glow_squid_spawn_egg": "Generar calamar brillante", "item.minecraft.glowstone_dust": "Polvo de glowstone", "item.minecraft.goat_horn": "Cacho de cabra", "item.minecraft.goat_spawn_egg": "Generar cabra", "item.minecraft.gold_ingot": "Lingote de oro", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> de oro", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "Hacha de oro", "item.minecraft.golden_boots": "Botas de oro", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Pechera de oro", "item.minecraft.golden_helmet": "Casco de oro", "item.minecraft.golden_hoe": "Azadón de oro", "item.minecraft.golden_horse_armor": "Armadura de oro para caballos", "item.minecraft.golden_leggings": "Pantalones de oro", "item.minecraft.golden_pickaxe": "Picota de oro", "item.minecraft.golden_shovel": "Pala de oro", "item.minecraft.golden_sword": "Espada de oro", "item.minecraft.gray_bundle": "Saco gris", "item.minecraft.gray_dye": "Tinte gris", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "Saco verde", "item.minecraft.green_dye": "Tinte verde", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON> verde", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON>", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "Diseño de estandarte", "item.minecraft.guster_banner_pattern.desc": "Estampado de torbellino", "item.minecraft.guster_banner_pattern.new": "Estampado de torbellino", "item.minecraft.guster_pottery_sherd": "Fragmento de cerámica de torbellino", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON> ghast feliz", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Corazón del Mar", "item.minecraft.heart_pottery_shard": "Fragmento de cerámica de corazón", "item.minecraft.heart_pottery_sherd": "Fragmento de cerámica de corazón", "item.minecraft.heartbreak_pottery_shard": "Fragmento de cerámica de corazón roto", "item.minecraft.heartbreak_pottery_sherd": "Fragmento de cerámica de corazón roto", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON> ho<PERSON>n", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON> con miel", "item.minecraft.honeycomb": "Panal", "item.minecraft.hopper_minecart": "Vagón con tolva", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON> caballo", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.host_armor_trim_smithing_template.new": "Ornamento de anfritrión", "item.minecraft.howl_pottery_shard": "Fragmento de cerámica de aullido", "item.minecraft.howl_pottery_sherd": "Fragmento de cerámica de aullido", "item.minecraft.husk_spawn_egg": "Generar husk", "item.minecraft.ink_sac": "Saco de tinta", "item.minecraft.iron_axe": "<PERSON><PERSON> <PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON>ro", "item.minecraft.iron_chestplate": "Pechera de hierro", "item.minecraft.iron_golem_spawn_egg": "Generar gó<PERSON>", "item.minecraft.iron_helmet": "<PERSON><PERSON>", "item.minecraft.iron_hoe": "Azadón de hierro", "item.minecraft.iron_horse_armor": "Armadura de hierro para caballos", "item.minecraft.iron_ingot": "Ling<PERSON> de hierro", "item.minecraft.iron_leggings": "Pantalones de hierro", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Picota de hierro", "item.minecraft.iron_shovel": "<PERSON><PERSON>", "item.minecraft.iron_sword": "E<PERSON><PERSON> de <PERSON>", "item.minecraft.item_frame": "<PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON> de jungla con cofre", "item.minecraft.knowledge_book": "Enciclopedia", "item.minecraft.lapis_lazuli": "La<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Balde con lava", "item.minecraft.lead": "Cuerda", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Botas de cuero", "item.minecraft.leather_chestplate": "Túnica de cuero", "item.minecraft.leather_helmet": "Gorro <PERSON>", "item.minecraft.leather_horse_armor": "Armadura de cuero para caballos", "item.minecraft.leather_leggings": "Pantalones de cuero", "item.minecraft.light_blue_bundle": "Saco celeste", "item.minecraft.light_blue_dye": "<PERSON><PERSON> celeste", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.light_gray_bundle": "Saco gris claro", "item.minecraft.light_gray_dye": "<PERSON>te gris claro", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON> gris claro", "item.minecraft.lime_bundle": "Saco verde lima", "item.minecraft.lime_dye": "Tinte verde lima", "item.minecraft.lime_harness": "Arnés verde lima", "item.minecraft.lingering_potion": "Poción persistente", "item.minecraft.lingering_potion.effect.awkward": "Poción persistente rara", "item.minecraft.lingering_potion.effect.empty": "Poción persistente no fabricable", "item.minecraft.lingering_potion.effect.fire_resistance": "Poción persistente de resistencia ígnea", "item.minecraft.lingering_potion.effect.harming": "Poción persistente de daño", "item.minecraft.lingering_potion.effect.healing": "Poción persistente de curación", "item.minecraft.lingering_potion.effect.infested": "Poción de infestación persistente", "item.minecraft.lingering_potion.effect.invisibility": "Poción persistente de invisibilidad", "item.minecraft.lingering_potion.effect.leaping": "Poción persistente de salto", "item.minecraft.lingering_potion.effect.levitation": "Poción persistente de levitación", "item.minecraft.lingering_potion.effect.luck": "Poción persistente de suerte", "item.minecraft.lingering_potion.effect.mundane": "Poción persistente banal", "item.minecraft.lingering_potion.effect.night_vision": "Poción persistente de visión nocturna", "item.minecraft.lingering_potion.effect.oozing": "Poción de secreción persistente", "item.minecraft.lingering_potion.effect.poison": "Poción persistente de veneno", "item.minecraft.lingering_potion.effect.regeneration": "Poción persistente de regeneración", "item.minecraft.lingering_potion.effect.slow_falling": "Poción persistente de caída lenta", "item.minecraft.lingering_potion.effect.slowness": "Poción persistente de lentitud", "item.minecraft.lingering_potion.effect.strength": "Poción persistente de fuerza", "item.minecraft.lingering_potion.effect.swiftness": "Poción persistente de velocidad", "item.minecraft.lingering_potion.effect.thick": "Poción persistente espesa", "item.minecraft.lingering_potion.effect.turtle_master": "Poción persistente del maestro tortuga", "item.minecraft.lingering_potion.effect.water": "Botella con agua persistente", "item.minecraft.lingering_potion.effect.water_breathing": "Poción persistente de respiración acuática", "item.minecraft.lingering_potion.effect.weakness": "Poción persistente de debilidad", "item.minecraft.lingering_potion.effect.weaving": "Poción de tejido persistente", "item.minecraft.lingering_potion.effect.wind_charged": "Poción de carga de viento persistente", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> llama", "item.minecraft.lodestone_compass": "<PERSON><PERSON><PERSON><PERSON><PERSON> im<PERSON>", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Saco magenta", "item.minecraft.magenta_dye": "Tinte magenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON> magenta", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Generar cubo de magma", "item.minecraft.mangrove_boat": "<PERSON><PERSON> de mangle", "item.minecraft.mangrove_chest_boat": "Bote de mangle con cofre", "item.minecraft.map": "Mapa en blanco", "item.minecraft.melon_seeds": "Semillas de sandía", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "Balde con leche", "item.minecraft.minecart": "Vagón", "item.minecraft.miner_pottery_shard": "Fragmento de cerámica de minero", "item.minecraft.miner_pottery_sherd": "Fragmento de cerámica de minero", "item.minecraft.mojang_banner_pattern": "Diseño de estandarte", "item.minecraft.mojang_banner_pattern.desc": "Estampado de cosa", "item.minecraft.mojang_banner_pattern.new": "Estampado de cosa", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "Fragmento de cerámica de monstruo", "item.minecraft.mourner_pottery_sherd": "Fragmento de cerámica de monstruo", "item.minecraft.mule_spawn_egg": "<PERSON>rar mula", "item.minecraft.mushroom_stew": "Cazuela de callampas", "item.minecraft.music_disc_11": "Disco de música", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disco de música", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disco de música", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disco de música", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disco de música", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disco de música", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disco de música", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disco de música", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Disco de música", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disco de música", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disco de música", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disco de música", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disco de música", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disco de música", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disco de música", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disco de música", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disco de música", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disco de música", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disco de música", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disco de música", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disco de música", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Cordero crudo", "item.minecraft.name_tag": "Etiqueta", "item.minecraft.nautilus_shell": "Caparazón de nautilo", "item.minecraft.nether_brick": "Ladrillo del Nether", "item.minecraft.nether_star": "Estrella del Nether", "item.minecraft.nether_wart": "Verrugas del Nether", "item.minecraft.netherite_axe": "<PERSON><PERSON> de netherita", "item.minecraft.netherite_boots": "Botas de netherita", "item.minecraft.netherite_chestplate": "Pechera de netherita", "item.minecraft.netherite_helmet": "Casco de netherita", "item.minecraft.netherite_hoe": "Azadón de netherita", "item.minecraft.netherite_ingot": "Lingote de netherita", "item.minecraft.netherite_leggings": "Pantalones de netherita", "item.minecraft.netherite_pickaxe": "Picota de netherita", "item.minecraft.netherite_scrap": "Placas de netherita", "item.minecraft.netherite_shovel": "<PERSON>la de netherita", "item.minecraft.netherite_sword": "Espada de netherita", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.netherite_upgrade_smithing_template.new": "Me<PERSON>ra de netherita", "item.minecraft.oak_boat": "<PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON>te de roble con cofre", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON> o<PERSON>", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON> ominosa", "item.minecraft.ominous_trial_key": "Llave de desafío ominosa", "item.minecraft.orange_bundle": "Saco naranja", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "Cuadro", "item.minecraft.pale_oak_boat": "<PERSON><PERSON> de roble p<PERSON>", "item.minecraft.pale_oak_chest_boat": "Bote de roble pálido con cofre", "item.minecraft.panda_spawn_egg": "<PERSON>rar panda", "item.minecraft.paper": "Papel", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON> loro", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON> de phantom", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON> phantom", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON> chancho", "item.minecraft.piglin_banner_pattern": "Diseño de estandarte", "item.minecraft.piglin_banner_pattern.desc": "Estampado de hocico", "item.minecraft.piglin_banner_pattern.new": "Estampado de hocico", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON> piglin bruto", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "Generar saqueador", "item.minecraft.pink_bundle": "Saco rosado", "item.minecraft.pink_dye": "<PERSON>te rosado", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON> rosa<PERSON>", "item.minecraft.pitcher_plant": "<PERSON>a jarra", "item.minecraft.pitcher_pod": "<PERSON>lla de planta jarra", "item.minecraft.plenty_pottery_shard": "Fragmento de cerámica de riqueza", "item.minecraft.plenty_pottery_sherd": "Fragmento de cerámica de riqueza", "item.minecraft.poisonous_potato": "<PERSON>", "item.minecraft.polar_bear_spawn_egg": "Generar oso polar", "item.minecraft.popped_chorus_fruit": "Fruta chorus asada", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON> cruda", "item.minecraft.potato": "<PERSON>", "item.minecraft.potion": "Poción", "item.minecraft.potion.effect.awkward": "Poción rara", "item.minecraft.potion.effect.empty": "Poción no fabricable", "item.minecraft.potion.effect.fire_resistance": "Poción de resistencia ígnea", "item.minecraft.potion.effect.harming": "Poción de daño", "item.minecraft.potion.effect.healing": "Poción de curación", "item.minecraft.potion.effect.infested": "Poción de infestación", "item.minecraft.potion.effect.invisibility": "Poción de invisibilidad", "item.minecraft.potion.effect.leaping": "Poción de salto", "item.minecraft.potion.effect.levitation": "Poción de levitación", "item.minecraft.potion.effect.luck": "Poción de suerte", "item.minecraft.potion.effect.mundane": "Poción banal", "item.minecraft.potion.effect.night_vision": "Poción de visión nocturna", "item.minecraft.potion.effect.oozing": "Poción de secreción", "item.minecraft.potion.effect.poison": "Poción de veneno", "item.minecraft.potion.effect.regeneration": "Poción de regeneración", "item.minecraft.potion.effect.slow_falling": "Poción de caída lenta", "item.minecraft.potion.effect.slowness": "Poción de lentitud", "item.minecraft.potion.effect.strength": "Poción de fuerza", "item.minecraft.potion.effect.swiftness": "Poción de velocidad", "item.minecraft.potion.effect.thick": "Poción espesa", "item.minecraft.potion.effect.turtle_master": "Poción del maestro tortuga", "item.minecraft.potion.effect.water": "Botella con agua", "item.minecraft.potion.effect.water_breathing": "Poción de respiración acuática", "item.minecraft.potion.effect.weakness": "Poción de debilidad", "item.minecraft.potion.effect.weaving": "Poción de tejido", "item.minecraft.potion.effect.wind_charged": "Poción de Carga de Viento", "item.minecraft.pottery_shard_archer": "Fragmento de cerámica de arquero", "item.minecraft.pottery_shard_arms_up": "Fragmento de cerámica de brazos arriba", "item.minecraft.pottery_shard_prize": "Fragmento de cerámica de tesoro", "item.minecraft.pottery_shard_skull": "Fragmento de cerámica de calavera", "item.minecraft.powder_snow_bucket": "Balde con nieve polvo", "item.minecraft.prismarine_crystals": "Cristales de prismarina", "item.minecraft.prismarine_shard": "Fragmento de prismarina", "item.minecraft.prize_pottery_shard": "Fragmento de cerámica de tesoro", "item.minecraft.prize_pottery_sherd": "Fragmento de cerámica de tesoro", "item.minecraft.pufferfish": "Pez globo", "item.minecraft.pufferfish_bucket": "Balde con pez globo", "item.minecraft.pufferfish_spawn_egg": "Generar pez globo", "item.minecraft.pumpkin_pie": "Torta de z<PERSON>llo", "item.minecraft.pumpkin_seeds": "Semillas de zapallo", "item.minecraft.purple_bundle": "Saco morado", "item.minecraft.purple_dye": "<PERSON>te morado", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON> mora<PERSON>", "item.minecraft.quartz": "Cuarz<PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.rabbit_foot": "Pata de conejo", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON>", "item.minecraft.rabbit_stew": "Cazuela de conejo", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ornamento de criador", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.raw_copper": "Cobre en bruto", "item.minecraft.raw_gold": "Oro en bruto", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON> en bruto", "item.minecraft.recovery_compass": "Brújula de recuperación", "item.minecraft.red_bundle": "Saco rojo", "item.minecraft.red_dye": "<PERSON><PERSON> rojo", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> rojo", "item.minecraft.redstone": "Polvo de redstone", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON> resin<PERSON>", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.rib_armor_trim_smithing_template.new": "Ornamento de costillas", "item.minecraft.rotten_flesh": "<PERSON><PERSON>", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.salmon_bucket": "Balde con salmón", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "Fragmento de cerámica de raspado", "item.minecraft.scute": "Escama", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ornamento de centinela", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ornamento de formador", "item.minecraft.sheaf_pottery_shard": "Fragmento de cerámica de heno", "item.minecraft.sheaf_pottery_sherd": "Fragmento de cerámica de heno", "item.minecraft.shears": "Tijeras", "item.minecraft.sheep_spawn_egg": "Generar oveja", "item.minecraft.shelter_pottery_shard": "Fragmento de cerámica de refugio", "item.minecraft.shelter_pottery_sherd": "Fragmento de cerámica de refugio", "item.minecraft.shield": "Escudo", "item.minecraft.shield.black": "Escudo negro", "item.minecraft.shield.blue": "Escudo a<PERSON>l", "item.minecraft.shield.brown": "Escudo café", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON> cian", "item.minecraft.shield.gray": "Escudo gris", "item.minecraft.shield.green": "Escudo verde", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON> celeste", "item.minecraft.shield.light_gray": "Escudo gris claro", "item.minecraft.shield.lime": "Escudo verde lima", "item.minecraft.shield.magenta": "Escudo magenta", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON>s<PERSON>do rosado", "item.minecraft.shield.purple": "Escudo morado", "item.minecraft.shield.red": "Escudo rojo", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON> blanco", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "Caparazón de <PERSON>ker", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "Cartel", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.silence_armor_trim_smithing_template.new": "Ornamento de silencio", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON> lepisma", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON> caballo esqueleto", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON> es<PERSON>", "item.minecraft.skull_banner_pattern": "Diseño de estandarte", "item.minecraft.skull_banner_pattern.desc": "Estampado de calavera", "item.minecraft.skull_banner_pattern.new": "Estampado de calavera", "item.minecraft.skull_pottery_shard": "Fragmento de cerámica de calavera", "item.minecraft.skull_pottery_sherd": "Fragmento de cerámica de calavera", "item.minecraft.slime_ball": "Bola de slime", "item.minecraft.slime_spawn_egg": "Generar slime", "item.minecraft.smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.smithing_template.applies_to": "Para:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Pon un lingote o cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Pon una armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingotes y cristales", "item.minecraft.smithing_template.ingredients": "Ingredientes:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Pon un lingote de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipamiento de diamante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Pon una armadura, arma o herramienta de diamante", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingote de netherita", "item.minecraft.smithing_template.upgrade": "<PERSON><PERSON><PERSON>: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON> sniffer", "item.minecraft.snort_pottery_shard": "Fragmento de cerámica de olfato", "item.minecraft.snort_pottery_sherd": "Fragmento de cerámica de Sniffer", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.snout_armor_trim_smithing_template.new": "Ornamento de hocico", "item.minecraft.snow_golem_spawn_egg": "Generar gólem de nieve", "item.minecraft.snowball": "<PERSON><PERSON> de <PERSON>eve", "item.minecraft.spectral_arrow": "Flecha espectral", "item.minecraft.spider_eye": "<PERSON><PERSON> de a<PERSON>ña", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.spire_armor_trim_smithing_template.new": "Ornamento de aguja", "item.minecraft.splash_potion": "Poción arrojadiza", "item.minecraft.splash_potion.effect.awkward": "Poción arrojadiza rara", "item.minecraft.splash_potion.effect.empty": "Poción arrojadiza no fabricable", "item.minecraft.splash_potion.effect.fire_resistance": "Poción arrojadiza de resistencia ígnea", "item.minecraft.splash_potion.effect.harming": "Poción arrojadiza de <PERSON>ño", "item.minecraft.splash_potion.effect.healing": "Poción arrojadiza de curación", "item.minecraft.splash_potion.effect.infested": "Poción de infestación arrojadiza", "item.minecraft.splash_potion.effect.invisibility": "Poción arrojadiza de invisibilidad", "item.minecraft.splash_potion.effect.leaping": "Poción arrojadiza de salto", "item.minecraft.splash_potion.effect.levitation": "Poción arrojadiza de levitación", "item.minecraft.splash_potion.effect.luck": "Poción arrojadiza de suerte", "item.minecraft.splash_potion.effect.mundane": "Poción arrojadiza banal", "item.minecraft.splash_potion.effect.night_vision": "Poción arrojadiza de visión nocturna", "item.minecraft.splash_potion.effect.oozing": "Poción de secreción arrojadiza", "item.minecraft.splash_potion.effect.poison": "Poción arrojadiza de veneno", "item.minecraft.splash_potion.effect.regeneration": "Poción arrojadiza de regeneración", "item.minecraft.splash_potion.effect.slow_falling": "Poción arrojadiza de caída lenta", "item.minecraft.splash_potion.effect.slowness": "Poción arrojadiza de lentitud", "item.minecraft.splash_potion.effect.strength": "Poción arrojadiza de fuerza", "item.minecraft.splash_potion.effect.swiftness": "Poción arrojadiza de velocidad", "item.minecraft.splash_potion.effect.thick": "Poción arrojadiza espesa", "item.minecraft.splash_potion.effect.turtle_master": "Poción arrojadiza del maestro tortuga", "item.minecraft.splash_potion.effect.water": "Botella con agua arrojadiza", "item.minecraft.splash_potion.effect.water_breathing": "Poción arrojadiza de respiración acuática", "item.minecraft.splash_potion.effect.weakness": "Poción arrojadiza de debilidad", "item.minecraft.splash_potion.effect.weaving": "Poción de tejido arrojadiza", "item.minecraft.splash_potion.effect.wind_charged": "Poción de Carga de Viento Arrojadiza", "item.minecraft.spruce_boat": "Bo<PERSON> de a<PERSON>", "item.minecraft.spruce_chest_boat": "Bote de abeto con cofre", "item.minecraft.spyglass": "Catalejo", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON> calamar", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON> de piedra", "item.minecraft.stone_hoe": "Azadón de piedra", "item.minecraft.stone_pickaxe": "Picota de piedra", "item.minecraft.stone_shovel": "Pala de piedra", "item.minecraft.stone_sword": "Espada de piedra", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON> stray", "item.minecraft.strider_spawn_egg": "Generar strider", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Cazuel<PERSON> sospechosa", "item.minecraft.sweet_berries": "Bayas dulces", "item.minecraft.tadpole_bucket": "Balde con renacuajo", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.tide_armor_trim_smithing_template.new": "Ornamento de marea", "item.minecraft.tipped_arrow": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.awkward": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.empty": "Flecha con efecto no fabricable", "item.minecraft.tipped_arrow.effect.fire_resistance": "Flecha de resistencia <PERSON>", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Flecha de curación", "item.minecraft.tipped_arrow.effect.infested": "Flecha de infestación", "item.minecraft.tipped_arrow.effect.invisibility": "Flecha de invisibilidad", "item.minecraft.tipped_arrow.effect.leaping": "Flecha de salto", "item.minecraft.tipped_arrow.effect.levitation": "Flecha de levitación", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.night_vision": "Flecha de visión nocturna", "item.minecraft.tipped_arrow.effect.oozing": "Flecha de secreción", "item.minecraft.tipped_arrow.effect.poison": "Flecha de veneno", "item.minecraft.tipped_arrow.effect.regeneration": "Flecha de regeneración", "item.minecraft.tipped_arrow.effect.slow_falling": "Flecha de caída lenta", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "Flecha de fuerza", "item.minecraft.tipped_arrow.effect.swiftness": "Flecha de velocidad", "item.minecraft.tipped_arrow.effect.thick": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.turtle_master": "Flecha del maestro tortuga", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "Flecha de Respiración acuática", "item.minecraft.tipped_arrow.effect.weakness": "Flecha de debilidad", "item.minecraft.tipped_arrow.effect.weaving": "Flecha de tejido", "item.minecraft.tipped_arrow.effect.wind_charged": "Flecha de Carga de Viento", "item.minecraft.tnt_minecart": "Vagón con TNT", "item.minecraft.torchflower_seeds": "Semillas de anflorcha", "item.minecraft.totem_of_undying": "Tótem de la inmortalidad", "item.minecraft.trader_llama_spawn_egg": "Generar llama ambulante", "item.minecraft.trial_key": "Llave de desafío", "item.minecraft.trident": "Tridente", "item.minecraft.tropical_fish": "Pez tropical", "item.minecraft.tropical_fish_bucket": "Balde con pez tropical", "item.minecraft.tropical_fish_spawn_egg": "Generar pez tropical", "item.minecraft.turtle_helmet": "Caparazón de tortuga", "item.minecraft.turtle_scute": "Escama de tortuga", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON> tortuga", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.vex_armor_trim_smithing_template.new": "Ornamento de vex", "item.minecraft.vex_spawn_egg": "Generar vex", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.vindicator_spawn_egg": "Generar vindicador", "item.minecraft.wandering_trader_spawn_egg": "<PERSON>rar vendedor ambulante", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.ward_armor_trim_smithing_template.new": "Ornamento de guardia", "item.minecraft.warden_spawn_egg": "Generar warden", "item.minecraft.warped_fungus_on_a_stick": "Caña con hongo distorsionado", "item.minecraft.water_bucket": "Balde con agua", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ornamento de guía", "item.minecraft.wheat": "Trigo", "item.minecraft.wheat_seeds": "Semillas de trigo", "item.minecraft.white_bundle": "Saco blanco", "item.minecraft.white_dye": "<PERSON>te blanco", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON> blan<PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.wild_armor_trim_smithing_template.new": "Ornamento salvaje", "item.minecraft.wind_charge": "Carga de viento", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON> bruja", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON> <PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wolf_armor": "Armadura para lobos", "item.minecraft.wolf_spawn_egg": "Generar lobo", "item.minecraft.wooden_axe": "<PERSON><PERSON>", "item.minecraft.wooden_hoe": "Azadón de madera", "item.minecraft.wooden_pickaxe": "Picota de madera", "item.minecraft.wooden_shovel": "<PERSON><PERSON>", "item.minecraft.wooden_sword": "Espada de madera", "item.minecraft.writable_book": "Libro y pluma", "item.minecraft.written_book": "Libro escrito", "item.minecraft.yellow_bundle": "<PERSON><PERSON> am<PERSON>", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON> caballo zombi", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON> z<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON> al<PERSON> z<PERSON>i", "item.minecraft.zombified_piglin_spawn_egg": "Generar piglin zombificado", "item.modifiers.any": "Al equipar:", "item.modifiers.armor": "En el cuerpo:", "item.modifiers.body": "Al equipar:", "item.modifiers.chest": "En el cuerpo:", "item.modifiers.feet": "En los pies:", "item.modifiers.hand": "En la mano:", "item.modifiers.head": "En la cabeza:", "item.modifiers.legs": "En las piernas:", "item.modifiers.mainhand": "En la mano principal:", "item.modifiers.offhand": "En la mano secundaria:", "item.modifiers.saddle": "Al equipar montura:", "item.nbt_tags": "NBT: %s tag(s)", "item.op_block_warning.line1": "Advertencia:", "item.op_block_warning.line2": "El uso de este ítem podría causar la ejecución de un comando", "item.op_block_warning.line3": "¡No lo utilices a menos que conozcas su contenido exacto!", "item.unbreakable": "Irrompible", "itemGroup.buildingBlocks": "Bloques de construcción", "itemGroup.coloredBlocks": "Bloques de colores", "itemGroup.combat": "Armamento", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Crafteo", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> y bebidas", "itemGroup.functional": "Bloques funcionales", "itemGroup.hotbar": "Barras guardadas", "itemGroup.ingredients": "Ingredientes", "itemGroup.inventory": "Inventario", "itemGroup.natural": "Bloques naturales", "itemGroup.op": "Herramientas de administrador", "itemGroup.redstone": "Bloques de redstone", "itemGroup.search": "Buscar ítems", "itemGroup.spawnEggs": "Huevos generadores", "itemGroup.tools": "Herramientas", "item_modifier.unknown": "Modificador de ítem desconocido: %s", "jigsaw_block.final_state": "Se convierte en:", "jigsaw_block.generate": "Generar", "jigsaw_block.joint.aligned": "<PERSON><PERSON>", "jigsaw_block.joint.rollable": "Rotable", "jigsaw_block.joint_label": "Tipo de unión:", "jigsaw_block.keep_jigsaws": "Mantener bloques", "jigsaw_block.levels": "Niveles: %s", "jigsaw_block.name": "Nombre:", "jigsaw_block.placement_priority": "Orden de colocación:", "jigsaw_block.placement_priority.tooltip": "Cuando este bloque de rompecabezas se conecta con una pieza, este es el orden en el cual esa pieza es procesada por conexiones con la estructura más amplia.\n\nLas piezas serán procesadas por prioridad de manera descendente, rompiendo empates por orden de inserción.", "jigsaw_block.pool": "Grupo objetivo:", "jigsaw_block.selection_priority": "Orden de selección:", "jigsaw_block.selection_priority.tooltip": "Cuando la pieza padre está siendo procesada por conexiones, este será el orden en el cual este bloque de rompecabezas intente conectarse con su pieza objetivo.\n\nLos rompecabezas serán procesados por orden de prioridad descendente, deshaciéndose los empates de forma aleatoria.", "jigsaw_block.target": "Nombre del objetivo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON>reator (cajita musical)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Progresos", "key.attack": "Atacar/Destruir", "key.back": "Caminar hacia atrás", "key.categories.creative": "<PERSON>do <PERSON>", "key.categories.gameplay": "Acciones del juego", "key.categories.inventory": "Inventario", "key.categories.misc": "Varios", "key.categories.movement": "Acciones de movimiento", "key.categories.multiplayer": "Multijugador", "key.categories.ui": "Interfaz del juego", "key.chat": "<PERSON><PERSON><PERSON> chat", "key.command": "<PERSON><PERSON><PERSON> comando en chat", "key.drop": "Tirar el ítem seleccionado", "key.forward": "Caminar hacia delante", "key.fullscreen": "Pantalla completa", "key.hotbar.1": "Barra rápida 1", "key.hotbar.2": "Barra rápida 2", "key.hotbar.3": "Barra rápida 3", "key.hotbar.4": "Barra rápida 4", "key.hotbar.5": "Barra rápida 5", "key.hotbar.6": "Barra rápida 6", "key.hotbar.7": "Barra rápida 7", "key.hotbar.8": "Barra rápida 8", "key.hotbar.9": "Barra rápida 9", "key.inventory": "Abrir/Cerrar inventario", "key.jump": "Saltar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Retroceso", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Eliminar", "key.keyboard.down": "<PERSON><PERSON><PERSON> ab.", "key.keyboard.end": "Fin", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>o", "key.keyboard.insert": "Insertar", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Intro (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "Flecha izq.", "key.keyboard.left.alt": "Alt izq.", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl izq.", "key.keyboard.left.shift": "Shift izq.", "key.keyboard.left.win": "Win izq.", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Blo<PERSON>", "key.keyboard.page.down": "Av P<PERSON>g", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON>", "key.keyboard.right": "<PERSON><PERSON><PERSON> der.", "key.keyboard.right.alt": "Alt der.", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl der.", "key.keyboard.right.shift": "Shift der.", "key.keyboard.right.win": "<PERSON> der.", "key.keyboard.scroll.lock": "Bloq Despl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Espacio", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON>", "key.keyboard.up": "<PERSON><PERSON><PERSON> arr.", "key.keyboard.world.1": "Macro 1", "key.keyboard.world.2": "Macro 2", "key.left": "Caminar hacia la izquierda", "key.loadToolbarActivator": "<PERSON>gar barra de objetos", "key.mouse": "Botón %1$s", "key.mouse.left": "Click izq.", "key.mouse.middle": "Click med.", "key.mouse.right": "<PERSON><PERSON> der.", "key.pickItem": "Copiar bloque", "key.playerlist": "Lista de jugadores", "key.quickActions": "Acciones rápidas", "key.right": "Caminar por la derecha", "key.saveToolbarActivator": "Guardar barra de objetos", "key.screenshot": "<PERSON>cer captura de pantalla", "key.smoothCamera": "Alternar cámara cinemática", "key.sneak": "Agacharse", "key.socialInteractions": "Pantalla de interacciones sociales", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> (Espectadores)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Cambiar í<PERSON> de mano", "key.togglePerspective": "Cambiar la perspectiva", "key.use": "Interactuar/Colocar bloque", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Comunidad", "known_server_link.community_guidelines": "Normas de la comunidad", "known_server_link.feedback": "Comentarios", "known_server_link.forums": "For<PERSON>", "known_server_link.news": "Noticias", "known_server_link.report_bug": "Informar error del server", "known_server_link.status": "Estado", "known_server_link.support": "Soporte", "known_server_link.website": "Sitio web", "lanServer.otherPlayers": "Ajustes para otros jugadores", "lanServer.port": "Puerto", "lanServer.port.invalid": "Puerto inválido.\nDeja la caja de edición en blanco o ingresa otro número entre 1024 y 65535.", "lanServer.port.invalid.new": "Puerto no disponible.\nDeja la caja de edición en blanco o ingresa un número entre %s y %s.", "lanServer.port.unavailable": "Puerto no disponible.\nDeja la caja de edición en blanco o ingresa otro número entre 1024 y 65535.", "lanServer.port.unavailable.new": "Puerto no disponible.\nDeja la caja de edición en blanco o ingresa otro número entre %s y %s.", "lanServer.scanning": "Buscando mundos LAN", "lanServer.start": "Abrir mundo en LAN", "lanServer.title": "Mundo LAN", "language.code": "spa_CL", "language.name": "Español", "language.region": "Chile", "lectern.take_book": "<PERSON><PERSON>rar libro", "loading.progress": "%s%%", "mco.account.privacy.info": "Leer más sobre Mojang y leyes de privacidad", "mco.account.privacy.info.button": "Más información sobre el RGPD", "mco.account.privacy.information": "Mojang implementa ciertos procedimientos para ayudar a proteger a los menores y su privacidad, incluyendo cumplir con la Ley de Protección de Privacidad Infantil en Internet (COPPA) y el Reglamento General de Protección de Datos (RGPD).\n\nPuede que necesites el permiso de tus padres para acceder a tu cuenta de Realms.", "mco.account.privacyinfo": "Mojang implementa ciertos procedimientos para ayudar a proteger a los menores y su privacidad que consisten en cumplir con la Ley de Protección de Privacidad Infantil en Internet (COPPA) y el Reglamento General de Protección de Datos (RGPD).\n\nPuede que necesites el permiso de tus padres para acceder a tu cuenta de Realms.\n\nSi tienes una cuenta de Minecraft antigua (inicia sesión con tu nombre de usuario), necesitarás migrar tu cuenta a una de Mojang para poder acceder a Realms.", "mco.account.update": "Actualizar cuenta", "mco.activity.noactivity": "Sin actividad desde hace %s día(s)", "mco.activity.title": "Actividad de jugadores", "mco.backup.button.download": "Descargar la última", "mco.backup.button.reset": "Resetear mundo", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "Subir mundo", "mco.backup.changes.tooltip": "Cambios", "mco.backup.entry": "Copia de seguridad (%s)", "mco.backup.entry.description": "Descripción", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON>(s) activo(s)", "mco.backup.entry.gameDifficulty": "Dificultad", "mco.backup.entry.gameMode": "<PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Versión del juego del server", "mco.backup.entry.name": "Nombre", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "Nombre de la plantilla", "mco.backup.entry.undefined": "Cambio indefinido", "mco.backup.entry.uploaded": "Subido", "mco.backup.entry.worldType": "Tipo de mundo", "mco.backup.generate.world": "Generar mundo", "mco.backup.info.title": "Cambios de la última copia de seguridad", "mco.backup.narration": "<PERSON><PERSON><PERSON> de %s", "mco.backup.nobackups": "Este Realm no tiene copias de seguridad.", "mco.backup.restoring": "Restaurando Realm", "mco.backup.unknown": "DESCONOCIDO", "mco.brokenworld.download": "<PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Por favor, reinicia el mundo o selecciona otro.", "mco.brokenworld.message.line2": "También puedes descargar el mundo para el modo un jugador.", "mco.brokenworld.minigame.title": "Este minijuego ya no es compatible", "mco.brokenworld.nonowner.error": "Por favor, espera a que el propietario del Realm restablezca el mundo", "mco.brokenworld.nonowner.title": "El mundo no está actualizado", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Reiniciar", "mco.brokenworld.title": "Tu mundo actual ya no es compatible", "mco.client.incompatible.msg.line1": "Tu cliente no es compatible con Minecraft Realms.", "mco.client.incompatible.msg.line2": "Por favor, usa la versión estable más reciente de Minecraft ya que", "mco.client.incompatible.msg.line3": "Realms no es compatible con snapshots ni versiones de prueba.", "mco.client.incompatible.title": "¡Cliente incompatible!", "mco.client.outdated.stable.version": "Tu versión del cliente (%s) no es compatible con Realms.\n\nPor favor, actualiza a la versión más reciente de Minecraft.", "mco.client.unsupported.snapshot.version": "Tu versión del cliente (%s) no es compatible con Realms.\n\nRealms no está disponible para esta versión snapshot.", "mco.compatibility.downgrade": "Desactualizar", "mco.compatibility.downgrade.description": "Este mundo se jugó por última vez en la versión %s; estás en la versión %s. Desactualizar un mundo a una versión anterior podría causar que se corrompa. No podemos garantizar que cargue o funcione.\n\nSe guardará una copia de seguridad de tu mundo en la carpeta \"World Backups\". Por favor, restaura tu mundo si es necesario.", "mco.compatibility.incompatible.popup.title": "Versión incompatible", "mco.compatibility.incompatible.releaseType.popup.message": "El mundo al que te estás intentando unir es incompatible con la versión en la que estás.", "mco.compatibility.incompatible.series.popup.message": "Este mundo se jugó por última vez en la versión %s; estás en la versión %s.\n\nEstas series no son compatibles entre sí. Necesitarás un mundo nuevo para jugar en esta versión.", "mco.compatibility.unverifiable.message": "No se pudo verificar la versión en la que se jugó este mundo por última vez. Si el mundo se actualiza o desactualiza, se creará automáticamente una copia de seguridad que se guardará en \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibilidad no verificable", "mco.compatibility.upgrade": "Actualizar", "mco.compatibility.upgrade.description": "Este mundo se jugó por última vez en la versión %s; estás en la versión %s.\n\nSe guardará un respaldo de tu mundo en la carpeta \"World Backups\". \n\nPor favor, restaura tu mundo si es necesario.", "mco.compatibility.upgrade.friend.description": "Este mundo se jugó por última vez en la versión %s; estás en la versión %s.\n\nSe guardará un respaldo del mundo en la carpeta \"World Backups\". \n\nEl dueño del Realm podrá restaurar el mundo si es necesario.", "mco.compatibility.upgrade.title": "¿Seguro de que quieres actualizar este mundo?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "El feed de jugadores se desactivó temporalmente", "mco.configure.world.backup": "Copias de seguridad del mundo", "mco.configure.world.buttons.activity": "Actividad de jugadores", "mco.configure.world.buttons.close": "Cerrar temporalmente Realm", "mco.configure.world.buttons.delete": "Bo<PERSON>r", "mco.configure.world.buttons.done": "Aceptar", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "<PERSON>vi<PERSON> jugador", "mco.configure.world.buttons.moreoptions": "Más opciones", "mco.configure.world.buttons.newworld": "Mundo nuevo", "mco.configure.world.buttons.open": "Abrir Realm", "mco.configure.world.buttons.options": "Opciones de mundo", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Seleccionar región...", "mco.configure.world.buttons.resetworld": "Resetear mundo", "mco.configure.world.buttons.save": "Guardar", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Suscripción", "mco.configure.world.buttons.switchminigame": "Cambiar de minijuego", "mco.configure.world.close.question.line1": "Tu realm dejará de estar disponible.", "mco.configure.world.close.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.close.question.title": "¿Necesitas hacer cambios sin interrupciones?", "mco.configure.world.closing": "Cerrando el Realm...", "mco.configure.world.commandBlocks": "Bloques de comandos", "mco.configure.world.delete.button": "Eliminar Realm", "mco.configure.world.delete.question.line1": "Tu realm se borrará permanentemente", "mco.configure.world.delete.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.description": "Descripción del Realm", "mco.configure.world.edit.slot.name": "Nombre del mundo", "mco.configure.world.edit.subscreen.adventuremap": "Algunos ajustes están desactivados por tener un mapa de aventuras", "mco.configure.world.edit.subscreen.experience": "Algunos ajustes están desactivados por tener un mapa de experiencias", "mco.configure.world.edit.subscreen.inspiration": "Algunas opciones están desactivadas debido a que tu mundo actual es una fuente de inspiración", "mco.configure.world.forceGameMode": "<PERSON><PERSON> modo de juego", "mco.configure.world.invite.narration": "Invitaciones nuevas: %s", "mco.configure.world.invite.profile.name": "Nombre", "mco.configure.world.invited": "Invitados", "mco.configure.world.invited.number": "Invitado (%s)", "mco.configure.world.invites.normal.tooltip": "Usuario normal", "mco.configure.world.invites.ops.tooltip": "Administrador(a)", "mco.configure.world.invites.remove.tooltip": "Eliminar", "mco.configure.world.leave.question.line1": "Si abandonas este realm no podrás verlo hasta que te vuelvan a invitar", "mco.configure.world.leave.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.loading": "Cargando Realm", "mco.configure.world.location": "Ubicación", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nombre del Realm", "mco.configure.world.opening": "Abriendo el Realm...", "mco.configure.world.players.error": "No existe ningún jugador con ese nombre", "mco.configure.world.players.inviting": "Invitando jugador...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP (JcJ)", "mco.configure.world.region_preference": "Preferencia de región", "mco.configure.world.region_preference.title": "Selección de región", "mco.configure.world.reset.question.line1": "Tu mundo será regenerado y el actual se perderá", "mco.configure.world.reset.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.resourcepack.question": "Necesitas un paquete de recursos personalizado para jugar en este Realm\n\n¿Quieres descargarlo y jugar?", "mco.configure.world.resourcepack.question.line1": "Necesitas un paquete de recursos personalizado para jugar en este Real", "mco.configure.world.resourcepack.question.line2": "¿Quieres descargarlo e instalarlo automáticamente para jugar?", "mco.configure.world.restore.download.question.line1": "El mundo será descargado y agregado a tus mundos para un jugador.", "mco.configure.world.restore.download.question.line2": "¿Quieres continuar?", "mco.configure.world.restore.question.line1": "Tu Realm será restaurado a la fecha \"%s\" (%s)", "mco.configure.world.restore.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.settings.expired": "No es posible modificar la configuración de un Realms que ha caducado", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Mundo %s", "mco.configure.world.slot.empty": "Vacío", "mco.configure.world.slot.switch.question.line1": "Tu realm se cambiará a otro mundo", "mco.configure.world.slot.switch.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.slot.tooltip": "Cambiar al mundo", "mco.configure.world.slot.tooltip.active": "Entrar", "mco.configure.world.slot.tooltip.minigame": "Cambiar a minijuego", "mco.configure.world.spawnAnimals": "Generar animales", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Generar PNJs", "mco.configure.world.spawnProtection": "Protección de la zona de aparición", "mco.configure.world.spawn_toggle.message": "Desactivar esta opción eliminará a todas las entidades de ese tipo existentes", "mco.configure.world.spawn_toggle.message.npc": "Desactivar esta opción eliminará a todas las entidades de ese tipo existentes, por ejemplo, los aldeanos", "mco.configure.world.spawn_toggle.title": "¡Advertencia!", "mco.configure.world.status": "Estado", "mco.configure.world.subscription.day": "día", "mco.configure.world.subscription.days": "días", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Extender suscrip<PERSON>", "mco.configure.world.subscription.less_than_a_day": "Menos de un día", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "meses", "mco.configure.world.subscription.recurring.daysleft": "Renovado automáticamente en", "mco.configure.world.subscription.recurring.info": "Los cambios hechos a tu suscripción de Realms, como incrementar la duración de tu suscripción o el cobro automático, no se aplicarán hasta la próxima fecha de facturación.", "mco.configure.world.subscription.remaining.days": "%1$s día(s)", "mco.configure.world.subscription.remaining.months": "%1$s mes(es)", "mco.configure.world.subscription.remaining.months.days": "%1$s mes(es), %2$s día(s)", "mco.configure.world.subscription.start": "Fecha de inicio", "mco.configure.world.subscription.tab": "Suscripción", "mco.configure.world.subscription.timeleft": "Tiempo restante", "mco.configure.world.subscription.title": "Tu suscripción", "mco.configure.world.subscription.unknown": "Desconocido", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> mundo", "mco.configure.world.switch.slot.subtitle": "El mundo está vacío. Elige qué quieres hacer", "mco.configure.world.title": "Configurar Realm:", "mco.configure.world.uninvite.player": "¿Seguro de que quieres cancelar la invitación a '%s'?", "mco.configure.world.uninvite.question": "¿Quieres cancelar la invitación de", "mco.configure.worlds.title": "Mundos", "mco.connect.authorizing": "Iniciando se<PERSON>...", "mco.connect.connecting": "Conectando al Realm...", "mco.connect.failed": "Error al conectar con el Realm", "mco.connect.region": "Región del servidor: %s", "mco.connect.success": "Listo", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "¡Necesitas escribir un nombre!", "mco.create.world.failed": "¡Error al crear el mundo!", "mco.create.world.reset.title": "Creando mundo...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "Si lo deseas, selecciona un mundo para ponerlo en tu nuevo realm", "mco.create.world.wait": "Creando el Realm...", "mco.download.cancelled": "Descarga cancelada", "mco.download.confirmation.line1": "El mundo que intentas descargar supera los %s", "mco.download.confirmation.line2": "No podrás subir este mundo a tu realm de nuevo", "mco.download.confirmation.oversized": "El mundo que intentas descargar supera los %s\n\nNo podrás volver a subir este mundo a tu Realm", "mco.download.done": "<PERSON><PERSON><PERSON> completada", "mco.download.downloading": "Descargando", "mco.download.extracting": "Extrayendo", "mco.download.failed": "<PERSON><PERSON><PERSON> fallida", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparando <PERSON>", "mco.download.resourcePack.fail": "¡Error al descargar el paquete de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Descargando la última copia del mundo", "mco.error.invalid.session.message": "Por favor, prueba a reiniciar Minecraft", "mco.error.invalid.session.title": "Sesión inválida", "mco.errorMessage.6001": "Cliente desactualizado", "mco.errorMessage.6002": "Términos de servicio no aceptados", "mco.errorMessage.6003": "Límite de descarga alcanzado", "mco.errorMessage.6004": "Límite de subida alcanzado", "mco.errorMessage.6005": "Mundo bloqueado", "mco.errorMessage.6006": "Mundo desactualizado", "mco.errorMessage.6007": "El usuario está en demasiados Realms", "mco.errorMessage.6008": "Nombre del Realm es inválido", "mco.errorMessage.6009": "Descripción del Realm es inválida", "mco.errorMessage.connectionFailure": "Ha ocurrido un error. <PERSON><PERSON> favor, inténtalo de nuevo más tarde.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON><PERSON> un error: ", "mco.errorMessage.initialize.failed": "Error al iniciar el Realm", "mco.errorMessage.noDetails": "No se proporcionaron detalles del error", "mco.errorMessage.realmsService": "Ocurrió un error (%s):", "mco.errorMessage.realmsService.configurationError": "Editando un espacio de reino inactivo inesperado", "mco.errorMessage.realmsService.connectivity": "No se pudo conectar a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "No se pudo revisar la compatibilidad de la versión. Respuesta obtenida: %s", "mco.errorMessage.retry": "Reintentar operación", "mco.errorMessage.serviceBusy": "El servicio de Realms se encuentra ocupado en este momento.\nPor favor, intenta conectarte de nuevo a tu Realm más tarde.", "mco.gui.button": "Botón", "mco.gui.ok": "<PERSON><PERSON>", "mco.info": "¡Información!", "mco.invited.player.narration": "Se invitó al jugador %s", "mco.invites.button.accept": "Aceptar", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "¡No hay invitaciones pendientes!", "mco.invites.pending": "¡Nuevas invitaciones!", "mco.invites.title": "Invitaciones pendientes", "mco.minigame.world.changeButton": "Seleccionar o<PERSON> mini<PERSON>", "mco.minigame.world.info.line1": "¡Esto cambiará temporalmente tu mundo por un minijuego!", "mco.minigame.world.info.line2": "<PERSON><PERSON> puedes regresar a tu mundo original sin perder nada.", "mco.minigame.world.noSelection": "Selecciona un minijuego", "mco.minigame.world.restore": "Terminando minijuego...", "mco.minigame.world.restore.question.line1": "El minijuego finalizará y tu Realm se restaurará.", "mco.minigame.world.restore.question.line2": "¿Se<PERSON>ro que quieres hacerlo?", "mco.minigame.world.selected": "Minijuego seleccionado:", "mco.minigame.world.slot.screen.title": "Cambiando de mundo...", "mco.minigame.world.startButton": "Cambiar", "mco.minigame.world.starting.screen.title": "Iniciando minijuego...", "mco.minigame.world.stopButton": "Terminar minijuego", "mco.minigame.world.switch.new": "¿Seleccionar otro minijuego?", "mco.minigame.world.switch.title": "Cambiar minijuego", "mco.minigame.world.title": "Cambiar Realm a minijuego", "mco.news": "Novedades de Minecraft Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON> ahora", "mco.notification.transferSubscription.message": "Estamos moviendo las suscripciones de Realms Java a la Microsoft Store. ¡No dejes que tu suscripción expire!\nTransfiere tu suscripción ahora y obtén 30 días de Realms gratis.\nVe a tu perfil en minecraft.net para transferir tu suscripción.", "mco.notification.visitUrl.buttonText.default": "Abrir link", "mco.notification.visitUrl.message.default": "Por favor, visita el link a continuación", "mco.onlinePlayers": "Jugadores en línea", "mco.play.button.realm.closed": "El Realm está cerrado", "mco.question": "Pregunta", "mco.reset.world.adventure": "Aventuras", "mco.reset.world.experience": "Experiencias", "mco.reset.world.generate": "Mundo nuevo", "mco.reset.world.inspiration": "Inspiración", "mco.reset.world.resetting.screen.title": "Reiniciando mundo...", "mco.reset.world.seed": "<PERSON><PERSON> (opcional)", "mco.reset.world.template": "Plantillas de mundo", "mco.reset.world.title": "Resetear mundo", "mco.reset.world.upload": "Subir mundo", "mco.reset.world.warning": "Esto sustituirá el mundo actual de tu Realm", "mco.selectServer.buy": "¡Compra un Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Realm cerrado", "mco.selectServer.closeserver": "Cerrar realm", "mco.selectServer.configure": "Configurar", "mco.selectServer.configureRealm": "Configurar Realm", "mco.selectServer.create": "Crear Realm", "mco.selectServer.create.subtitle": "Elige qué mundo quieres poner en tu nuevo Realm", "mco.selectServer.expired": "Realm caducado", "mco.selectServer.expiredList": "Tu suscripción expiró", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Suscribirse", "mco.selectServer.expiredTrial": "Tu periodo de prueba terminó", "mco.selectServer.expires.day": "Expira en 1 día", "mco.selectServer.expires.days": "Expira en %s días", "mco.selectServer.expires.soon": "Expirará pronto", "mco.selectServer.leave": "Abandonar Realm", "mco.selectServer.loading": "Cargando lista de Realms", "mco.selectServer.mapOnlySupportedForVersion": "Este mapa no es compatible con la %s", "mco.selectServer.minigame": "Minijuego:", "mco.selectServer.minigameName": "Minijuego: %s", "mco.selectServer.minigameNotSupportedInVersion": "No puedes jugar este minijuego en la %s", "mco.selectServer.noRealms": "Parece que no tienes ningún Realm. Agrega un Realm para jugar con tus amigos.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Abrir realm", "mco.selectServer.openserver": "Realm abierto", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Realms es una forma simple y segura de jugar Minecraft con hasta diez amigos a la vez. ¡Soporta minijuegos y mundos personalizados! Solo paga el dueño.", "mco.selectServer.purchase": "Agrerar Realm", "mco.selectServer.trial": "¡Pruébalo!", "mco.selectServer.uninitialized": "¡Haz clic para crear tu nuevo realm!", "mco.snapshot.createSnapshotPopup.text": "Estás a punto de crear un Realm para snapshots gratis que estará vinculado a tu suscripción de Realms de pago. Este Realm para snapshots será accesible mientras tu suscripción de pago esté activa. Tu Realm principal no se verá afectado.", "mco.snapshot.createSnapshotPopup.title": "¿Crear Realm para snapshots?", "mco.snapshot.creating": "Creando <PERSON> para snapshots...", "mco.snapshot.description": "Vinculado con %s", "mco.snapshot.friendsRealm.downgrade": "Debes estar en la versión %s para unirte a este Realm", "mco.snapshot.friendsRealm.upgrade": "%s deberá actualizar su Realm para jugar en esta versión", "mco.snapshot.paired": "Este Realm para snapshots está vinculado con %s", "mco.snapshot.parent.tooltip": "Usa la versión estable de Minecraft más reciente para jugar en este Realm", "mco.snapshot.start": "Iniciar Realm para snapshots", "mco.snapshot.subscription.info": "Este es un Realm para snapshots que está vinculado a la suscripción de tu Realm \"%s\". Permanecerá activo mientras el Realm principal lo esté.", "mco.snapshot.tooltip": "Usa los Realms para snapshots para echar un vistazo a próximas versiones de Minecraft que pueden incluir nuevas características y otros cambios.\n\nTus Realms normales permanecerán en la versión estable del juego.", "mco.snapshotRealmsPopup.message": "Los Realms ahora están disponibles para snapshots desde la 23w41a en adelante. ¡Todas las suscripciones de Realms vienen con un Realm para snapshots gratis, además de tu Realm de Java normal!", "mco.snapshotRealmsPopup.title": "Realms ahora está disponible para snapshots", "mco.snapshotRealmsPopup.urlText": "Más información", "mco.template.button.publisher": "Publicado por", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Plantilla del mundo", "mco.template.info.tooltip": "Web del creador", "mco.template.name": "Plantilla", "mco.template.select.failure": "No pudimos recuperar la lista de contenido para esta categoría.\nPor favor, revisa tu conexión a internet o inténtalo de nuevo más tarde.", "mco.template.select.narrate.authors": "Autores: %s", "mco.template.select.narrate.version": "versión %s", "mco.template.select.none": "¡Ups! Parece que esta categoría no tiene contenido actualmente.\nPor favor, revísala más tarde para ver si hay contenido nuevo, o, si eres un creador(a), %s.", "mco.template.select.none.linkTitle": "quizás podrías enviarnos alguna de tus ideas", "mco.template.title": "Plantillas de mundos", "mco.template.title.minigame": "Minijuegos", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON>a", "mco.terms.buttons.agree": "Acepto", "mco.terms.buttons.disagree": "No acepto", "mco.terms.sentence.1": "Acepto los términos de servicio", "mco.terms.sentence.2": "de Minecraft Realms", "mco.terms.title": "Términos de servicio de Minecraft Realms", "mco.time.daysAgo": "Hace %1$s día(s)", "mco.time.hoursAgo": "Hace %1$s hora(s)", "mco.time.minutesAgo": "Hace %1$s minuto(s)", "mco.time.now": "ahora", "mco.time.secondsAgo": "Hace %1$s segundo(s)", "mco.trial.message.line1": "¿Quieres conseguir tu propio realm?", "mco.trial.message.line2": "¡Haz click aquí para más información!", "mco.upload.button.name": "Subir", "mco.upload.cancelled": "Subida cancelada", "mco.upload.close.failure": "No se ha podido cerrar tu Realm, inténtalo de nuevo más tarde", "mco.upload.done": "Subida completada", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "¡Subida fallida! (%s)", "mco.upload.failed.too_big.description": "El mundo seleccionado es demasiado grande. El tamaño máximo permitido es de %s.", "mco.upload.failed.too_big.title": "El mundo es demasiado grande", "mco.upload.hardcore": "¡Los mundos en modo extremo no pueden ser subidos!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparando tu mundo", "mco.upload.select.world.none": "¡No se han encontrado mundos en el modo un jugador!", "mco.upload.select.world.subtitle": "Selecciona un mundo del modo un jugador para subirlo", "mco.upload.select.world.title": "Subir mundo", "mco.upload.size.failure.line1": "¡\"%s\" es demasiado grande!", "mco.upload.size.failure.line2": "Pesa %s. El peso máximo permitido es %s.", "mco.upload.uploading": "Subiendo '%s'", "mco.upload.verifying": "Verificando tu mundo", "mco.version": "Versión: %s", "mco.warning": "¡Advertencia!", "mco.worldSlot.minigame": "Minijuego", "menu.custom_options": "Opciones personalizadas...", "menu.custom_options.title": "Opciones personalizadas", "menu.custom_options.tooltip": "Nota: Las opciones personalizadas provienen de servidores y/o contenido de terceros.\n¡Manéjalas con cuidado!", "menu.custom_screen_info.button_narration": "Esta es una pantalla personalizada. Obtén más información.", "menu.custom_screen_info.contents": "El contenido de esta pantalla es controlado por servidores y mapas de terceros que no son propiedad ni están operados o supervisados por Mojang Studios ni Microsoft.\n\n¡Úsalo con precaución! Siempre ten cuidado al seguir enlaces y nunca compartas tu información personal, incluidos tus datos de inicio de sesión.\n\nSi esta pantalla te impide jugar, también puedes desconectarte del servidor actual usando el botón de abajo.", "menu.custom_screen_info.disconnect": "Pantalla personalizada rechazada", "menu.custom_screen_info.title": "Nota sobre las pantallas personalizadas", "menu.custom_screen_info.tooltip": "Esta es una pantalla personalizada. Haz clic aquí para obtener más información.", "menu.disconnect": "Desconectarse", "menu.feedback": "Comentarios...", "menu.feedback.title": "Comentarios", "menu.game": "Menú de pausa", "menu.modded": " (Con mods)", "menu.multiplayer": "Multijugador", "menu.online": "Minecraft Realms", "menu.options": "Opciones...", "menu.paused": "Juego en pausa", "menu.playdemo": "<PERSON><PERSON> al mundo demo", "menu.playerReporting": "<PERSON>ar jugador", "menu.preparingSpawn": "Preparando área de aparición: %s%%", "menu.quick_actions": "Acciones rápidas...", "menu.quick_actions.title": "Acciones rápidas", "menu.quit": "<PERSON><PERSON><PERSON> juego", "menu.reportBugs": "Reportar errores", "menu.resetdemo": "Reiniciar el mundo demo", "menu.returnToGame": "Volver al juego", "menu.returnToMenu": "Guardar y volver al menú", "menu.savingChunks": "Guardando chunks", "menu.savingLevel": "Guardando mundo", "menu.sendFeedback": "Enviar opinión", "menu.server_links": "Links del server...", "menu.server_links.title": "Links del server", "menu.shareToLan": "Abrir en LAN", "menu.singleplayer": "Un jugador", "menu.working": "Funcionando...", "merchant.deprecated": "Los aldeanos se reaprovisionan dos veces al día.", "merchant.level.1": "Novato", "merchant.level.2": "Aprendiz", "merchant.level.3": "Competente", "merchant.level.4": "Experto", "merchant.level.5": "Maestro", "merchant.title": "%s - %s", "merchant.trades": "Intercambios", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Pulsa %1$s para bajarte", "multiplayer.applyingPack": "Aplicando paquete de recursos", "multiplayer.confirm_command.parse_errors": "Estás intentando ejecutar un comando no reconocido o inválido. \n¿Estás seguro? \nComando: %s", "multiplayer.confirm_command.permissions_required": "Estás intentando ejecutar un comando que requiere permisos elevados. Esto podría afectar negativamente tu juego.\n¿Estás seguro? \nComando: %s", "multiplayer.confirm_command.title": "Confirmar ejecución del comando", "multiplayer.disconnect.authservers_down": "Los servers de autenticación no funcionan. Por favor, inténtalo más tarde y disculpa las molestias.", "multiplayer.disconnect.bad_chat_index": "Mensaje de chat perdido o reordenado desde servidor", "multiplayer.disconnect.banned": "Te han baneado de este server", "multiplayer.disconnect.banned.expiration": "\n<PERSON>cha de desbaneo: %s", "multiplayer.disconnect.banned.reason": "Te han baneado de este server.\nMotivo: %s", "multiplayer.disconnect.banned_ip.expiration": "\n<PERSON>cha de desbaneo: %s", "multiplayer.disconnect.banned_ip.reason": "Tu dirección IP está baneada en este server.\nMotivo: %s", "multiplayer.disconnect.chat_validation_failed": "Error de validación del mensaje de chat", "multiplayer.disconnect.duplicate_login": "Te conectaste desde otra ubicación", "multiplayer.disconnect.expired_public_key": "La clave pública de perfil ha caducado. Comprueba que la hora de tu sistema esté sincronizada y prueba reiniciar tu juego.", "multiplayer.disconnect.flying": "No está permitido volar en este server", "multiplayer.disconnect.generic": "Desconectado/a", "multiplayer.disconnect.idling": "¡Estuviste inactivo por mucho tiempo!", "multiplayer.disconnect.illegal_characters": "Caracteres no permitidos en el chat", "multiplayer.disconnect.incompatible": "¡Cliente incompatible! Por favor, usa %s", "multiplayer.disconnect.invalid_entity_attacked": "Se <PERSON>ó atacar a una entidad no válida", "multiplayer.disconnect.invalid_packet": "El server envió un paquete no válido", "multiplayer.disconnect.invalid_player_data": "Datos del jugador no válidos", "multiplayer.disconnect.invalid_player_movement": "Se detectó un movimiento de jugador no válido", "multiplayer.disconnect.invalid_public_key_signature": "Signatura de la clave pública del perfil inválida.\nPrueba a reiniciar el juego.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firma de la llave pública inválida.\nIntenta reiniciar tu juego.", "multiplayer.disconnect.invalid_vehicle_movement": "Se dectectó un movimiento de vehículo no válido", "multiplayer.disconnect.ip_banned": "Tu dirección IP está baneada en este server", "multiplayer.disconnect.kicked": "Has sido expulsado por un administrador", "multiplayer.disconnect.missing_tags": "El server envió un conjunto de tags incompleto.\nPor favor, contacta con algún admin del server.", "multiplayer.disconnect.name_taken": "Ese nombre ya está en uso", "multiplayer.disconnect.not_whitelisted": "¡No estás en la lista blanca del server!", "multiplayer.disconnect.out_of_order_chat": "Se ha recibido un paquete del chat incongruente. ¿Ha habido algún cambio en la hora de tu dispositivo?", "multiplayer.disconnect.outdated_client": "¡Cliente incompatible! Por favor, usa la versión %s", "multiplayer.disconnect.outdated_server": "¡Cliente incompatible! Por favor, usa la versión %s", "multiplayer.disconnect.server_full": "¡El server está lleno!", "multiplayer.disconnect.server_shutdown": "Server cerrado", "multiplayer.disconnect.slow_login": "La conexión tardó demasiado", "multiplayer.disconnect.too_many_pending_chats": "Demasiados mensajes de chat sin confirmar", "multiplayer.disconnect.transfers_disabled": "El servidor no acepta transferencias", "multiplayer.disconnect.unexpected_query_response": "Se enviaron datos desconocidos por parte del cliente", "multiplayer.disconnect.unsigned_chat": "Se ha recibido un paquete de chat sin firma o con una firma no válida.", "multiplayer.disconnect.unverified_username": "¡No se pudo verificar tu nombre de usuario!", "multiplayer.downloadingStats": "Recuperando estadísticas...", "multiplayer.downloadingTerrain": "Cargando terreno...", "multiplayer.lan.server_found": "Nuevo server encontrado: %s", "multiplayer.message_not_delivered": "No se pudo enviar el mensaje al chat, revisa los registros del server: %s", "multiplayer.player.joined": "%s se unió a la partida", "multiplayer.player.joined.renamed": "%s (antes conocido como %s) se unió a la partida", "multiplayer.player.left": "%s abandonó la partida", "multiplayer.player.list.hp": "%sPS", "multiplayer.player.list.narration": "Jugadores conectados: %s", "multiplayer.requiredTexturePrompt.disconnect": "El server requiere un paquete de recursos personalizado", "multiplayer.requiredTexturePrompt.line1": "El server recomienda usar su propio paquete de recursos.", "multiplayer.requiredTexturePrompt.line2": "<PERSON><PERSON><PERSON> el paquete de recursos personalizado te desconectará de este server.", "multiplayer.socialInteractions.not_available": "Las interacciones sociales solo están disponibles en mundos multijugador", "multiplayer.status.and_more": "... y %s más ...", "multiplayer.status.cancelled": "Cancelado", "multiplayer.status.cannot_connect": "E<PERSON>r de conexión", "multiplayer.status.cannot_resolve": "Error al resolver el nombre del host", "multiplayer.status.finished": "Finalizado", "multiplayer.status.incompatible": "¡Versión incompatible!", "multiplayer.status.motd.narration": "Mensaje del día: %s", "multiplayer.status.no_connection": "(sin conexión)", "multiplayer.status.old": "Desactualizado", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "%s milisegundos de latencia", "multiplayer.status.pinging": "Conectando...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s de %s jugadores online", "multiplayer.status.quitting": "Desconectando", "multiplayer.status.request_handled": "Se ha recibido la solicitud de estado", "multiplayer.status.unknown": "¿¿??", "multiplayer.status.unrequested": "Se ha recibido un estado no solicitado", "multiplayer.status.version.narration": "Versión del server: %s", "multiplayer.stopSleeping": "Levantarte de la cama", "multiplayer.texturePrompt.failure.line1": "El paquete de recursos del server no se pudo aplicar", "multiplayer.texturePrompt.failure.line2": "Cualquier funcionalidad que requiera recursos personalizados podría no funcionar como se espera", "multiplayer.texturePrompt.line1": "El server recomienda usar su propio paquete de recursos.", "multiplayer.texturePrompt.line2": "¿Quieres descargarlo e instalarlo automágicamente?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMensaje del server:\n%s", "multiplayer.title": "Multijugador", "multiplayer.unsecureserver.toast": "Los mensajes enviados en este servidor pueden ser modificados y podrían no reflejar el mensaje original", "multiplayer.unsecureserver.toast.title": "Los mensajes del chat no se pueden verificar", "multiplayerWarning.check": "No mostrar esta pantalla de nuevo", "multiplayerWarning.header": "Precaución: juego online de terceros", "multiplayerWarning.message": "Precaución: El juego online es ofrecido por servers de terceros, los cuales no son operados, supervisados ni son propiedad de Mojang Studios o Microsoft. Durante el juego online, puedes estar expuesto/a a mensajes de chat no moderados u otro tipo de contenido generado por usuarios que puede no ser apropiado para todos.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygene", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botón: %s", "narration.button.usage.focused": "Presiona enter para activar", "narration.button.usage.hovered": "Haz click izquierdo para activar", "narration.checkbox": "Casilla: %s", "narration.checkbox.usage.focused": "Presiona enter para alternar", "narration.checkbox.usage.hovered": "Haz click izquierdo para alternar", "narration.component_list.usage": "Presiona el tabulador para navegar al siguiente elemento", "narration.cycle_button.usage.focused": "Presiona enter para cambiar a %s", "narration.cycle_button.usage.hovered": "Haz click izquierdo para cambiar a %s", "narration.edit_box": "Editar caja: %s", "narration.item": "Ítem: %s", "narration.recipe": "Receta para %s", "narration.recipe.usage": "Haz click izquierdo para seleccionar", "narration.recipe.usage.more": "Haz click derecho para mostrar más recetas", "narration.selection.usage": "Presiona los botones de arriba y abajo para desplazarte a otra entrada", "narration.slider.usage.focused": "Presiona las teclas de izquierda o derecha para cambiar el valor", "narration.slider.usage.hovered": "Arrastra la barra para cambiar el valor", "narration.suggestion": "Se ha seleccionado la sugerencia %s de %s: %s", "narration.suggestion.tooltip": "Se ha seleccionado la sugerencia %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Pulsa Tab para pasar a la siguiente sugerencia", "narration.suggestion.usage.cycle.hidable": "Pulsa <PERSON>b para pasar a la siguiente sugerencia, o Esc para salir de las sugerencias", "narration.suggestion.usage.fill.fixed": "Pulsa Tab para usar la sugerencia", "narration.suggestion.usage.fill.hidable": "Pulsa Tab para usar la sugerencia, o Esc para salir de las sugerencias", "narration.tab_navigation.usage": "Presiona Ctrl y Tab para cambiar entre pestañas", "narrator.button.accessibility": "Accesibilidad", "narrator.button.difficulty_lock": "Bloqueo de dificultad", "narrator.button.difficulty_lock.locked": "Bloqueada", "narrator.button.difficulty_lock.unlocked": "Desbloqueada", "narrator.button.language": "Idioma", "narrator.controls.bound": "La acción %s está asignada al botón %s", "narrator.controls.reset": "Reiniciar botón de %s", "narrator.controls.unbound": "La acción %s no tiene asignada ningún botón", "narrator.joining": "Entrando", "narrator.loading": "Cargando: %s", "narrator.loading.done": "Aceptar", "narrator.position.list": "Se ha seleccionado la fila de la lista %s de %s", "narrator.position.object_list": "Se ha seleccionado el elemento de la fila %s de %s", "narrator.position.screen": "Elemento en pantalla %s de %s", "narrator.position.tab": "Pestaña seleccionada %s de %s", "narrator.ready_to_play": "Listo para jugar", "narrator.screen.title": "<PERSON><PERSON> principal", "narrator.screen.usage": "Usa el cursor del mouse o el tabulador para seleccionar un elemento", "narrator.select": "Ha seleccionado: %s", "narrator.select.world": "Seleccionado %s, última partida: %s, %s, %s, versión: %s", "narrator.select.world_info": "%s seleccionado, última vez jugado: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> activado", "optimizeWorld.confirm.description": "Esto tratará de optimizar tu mundo asegurándose de que todos los datos se guarden en el formato más reciente. Dependiendo del tamaño del mundo, esto podría demorarse bastante. Una vez terminado, tu mundo debería de funcionar aún más rápido, pero ya no será compatible con versiones antiguas. ¿Estás seguro de que quieres hacerlo?", "optimizeWorld.confirm.proceed": "<PERSON><PERSON><PERSON> re<PERSON>aldo y Optimizar", "optimizeWorld.confirm.title": "<PERSON>timi<PERSON> mundo", "optimizeWorld.info.converted": "Chunks actualizados: %s", "optimizeWorld.info.skipped": "Chunks omitidos: %s", "optimizeWorld.info.total": "Chunks en total: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Calculando chunks...", "optimizeWorld.stage.failed": "¡Error! :(", "optimizeWorld.stage.finished": "Finalizando...", "optimizeWorld.stage.finished.chunks": "Finalizando optimización de chunks...", "optimizeWorld.stage.finished.entities": "Finalizando optimización de entidades...", "optimizeWorld.stage.finished.poi": "Finalizando optimización de puntos de interés...", "optimizeWorld.stage.upgrading": "Optimizando todos los chunks...", "optimizeWorld.stage.upgrading.chunks": "Optimizando todos los chunks...", "optimizeWorld.stage.upgrading.entities": "Optimizando todas las entidades...", "optimizeWorld.stage.upgrading.poi": "Optimizando todos los puntos de interés...", "optimizeWorld.title": "Optimizando el mundo \"%s\"", "options.accessibility": "Accesibilidad...", "options.accessibility.high_contrast": "<PERSON> contraste", "options.accessibility.high_contrast.error.tooltip": "El paquete de recursos de alto contraste no está disponible.", "options.accessibility.high_contrast.tooltip": "Aumenta el contraste de los elementos en la interfaz.", "options.accessibility.high_contrast_block_outline": "Alto contraste en el contorno de bloques", "options.accessibility.high_contrast_block_outline.tooltip": "Aumenta el contraste del contorno al bloque que miras.", "options.accessibility.link": "Guía de accesibilidad", "options.accessibility.menu_background_blurriness": "Desenfoque de fondo del menú", "options.accessibility.menu_background_blurriness.tooltip": "Controla el difuminado del fondo en los menús.", "options.accessibility.narrator_hotkey": "Atajo al narrador", "options.accessibility.narrator_hotkey.mac.tooltip": "Permite activar o desactivar el narrador con Cmd + B.", "options.accessibility.narrator_hotkey.tooltip": "Permite activar o desactivar el narrador con Ctrl + B.", "options.accessibility.panorama_speed": "Velocidad del panorama", "options.accessibility.text_background": "Fondo del texto", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Siempre", "options.accessibility.text_background_opacity": "Opacidad del fondo", "options.accessibility.title": "Accesibilidad", "options.allowServerListing": "Listado de servers", "options.allowServerListing.tooltip": "Los servers pueden mostrar usuarios conectados como parte de su estado público.\nAl desactivar esta opción, tu nombre no se mostrará en dichas listas.", "options.ao": "Iluminación suave", "options.ao.max": "Máxima", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "NO", "options.attack.crosshair": "Mira", "options.attack.hotbar": "Barr<PERSON>", "options.attackIndicator": "Indicador de ataque", "options.audioDevice": "Dispositivo", "options.audioDevice.default": "Por defecto del sistema", "options.autoJump": "Salto automático", "options.autoSuggestCommands": "Sugerir comandos", "options.autosaveIndicator": "Indicar autoguardado", "options.biomeBlendRadius": "Mezcla de biomas", "options.biomeBlendRadius.1": "NO (<PERSON><PERSON> r<PERSON>)", "options.biomeBlendRadius.11": "11x11 (Extremo)", "options.biomeBlendRadius.13": "13x13 (Exagerado)", "options.biomeBlendRadius.15": "15x15 (<PERSON>)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (Alto)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON> alto)", "options.chat": "Ajustes del chat...", "options.chat.color": "Colores", "options.chat.delay": "Retrasar chat: %s segundos", "options.chat.delay_none": "Retraso del chat: NO", "options.chat.height.focused": "Altura máx. (abierto)", "options.chat.height.unfocused": "Altura máx. (cerrado)", "options.chat.line_spacing": "Interlineado", "options.chat.links": "Links de internet", "options.chat.links.prompt": "Avisar al abrir links", "options.chat.opacity": "Opacidad del chat", "options.chat.scale": "Tamaño del texto", "options.chat.title": "Cha<PERSON>", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Visible", "options.chat.visibility.hidden": "Oculto", "options.chat.visibility.system": "Solo comandos", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Detallados", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controles...", "options.credits_and_attribution": "Créditos y atribuciones...", "options.damageTiltStrength": "Sacudida por daño", "options.damageTiltStrength.tooltip": "Cuánto se sacudirá la cámara al recibir daño.", "options.darkMojangStudiosBackgroundColor": "Logo monocromá<PERSON>o", "options.darkMojangStudiosBackgroundColor.tooltip": "Cambia el fondo del logo Mojang Studios de la pantalla de carga a negro.", "options.darknessEffectScale": "Pulso de oscuridad", "options.darknessEffectScale.tooltip": "Controla cuanto pulsa el efecto de oscuridad cuando un warden o un chillador de skulk te lo provoca.", "options.difficulty": "Dificultad", "options.difficulty.easy": "F<PERSON><PERSON>l", "options.difficulty.easy.info": "Aparecen mobs hostiles, pero hacen menos daño. La barra de hambre disminuye y puede dañarte hasta dejarte a 5 corazones.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Aparecen mobs hostiles y estos hacen más daño de lo normal. Puedes morir de hambre.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Aparecen mobs hostiles que hacen un daño regular. La barra de hambre disminuye y puede dañarte hasta dejarte a medio corazón.", "options.difficulty.online": "Dificultad del server", "options.difficulty.peaceful": "Pacífico", "options.difficulty.peaceful.info": "Sin mobs hostiles, y solo se generan algunos mobs neutrales. La barra de hambre no disminuye y regeneras vida con el tiempo.", "options.directionalAudio": "Audio direccional", "options.directionalAudio.off.tooltip": "Sonido estéreo clásico.", "options.directionalAudio.on.tooltip": "Usa audio direccional basado en HRTF para mejorar la simulación de sonido tridimensional. Se requiere hardware de audio compatible con HRTF. La experiencia mejora con el uso de audífonos.", "options.discrete_mouse_scroll": "Desplazamiento discreto", "options.entityDistanceScaling": "Distancia de entidades", "options.entityShadows": "Sombras de entidades", "options.font": "Ajustes de fuente...", "options.font.title": "Ajustes de fuente", "options.forceUnicodeFont": "Fuente unicode", "options.fov": "FOV", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Efectos del FOV", "options.fovEffectScale.tooltip": "Controla la variación del campo de visión (FOV) con los efectos del juego.", "options.framerate": "%s fps", "options.framerateLimit": "Máximos FPS", "options.framerateLimit.max": "Sin límite", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "Actual", "options.fullscreen.entry": "%sx%s a %sHz (%s bits)", "options.fullscreen.resolution": "Pantalla completa", "options.fullscreen.unavailable": "Ajuste no disponible", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "Por defecto", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Oscuro", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocidad de brillo", "options.glintSpeed.tooltip": "Controla la velocidad del brillo de los ítems encantados.", "options.glintStrength": "Intensidad de brillo", "options.glintStrength.tooltip": "Controla la transparencia del brillo de los ítems encantados.", "options.graphics": "Grá<PERSON><PERSON>", "options.graphics.fabulous": "¡Fabulosos!", "options.graphics.fabulous.tooltip": "Los gráficos %s usan shaders para dibujar efectos del clima, nubes y partículas detrás de bloques transparentes y agua.\nEsta opción podría afectar severamente el rendimiento en dispositivos portátiles y pantallas 4K.", "options.graphics.fancy": "Detallados", "options.graphics.fancy.tooltip": "Los gráficos detallados equilibran el rendimiento y la calidad para la mayoría de dispositivos.\nLos efectos del clima, nubes y partículas pueden no aparecer detrás de bloques transparentes o agua.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Los gráficos rápidos reducen la cantidad de lluvia y nieve visible.\nLos efectos de transparencia se desactivan para bloques como las hojas de árbol.", "options.graphics.warning.accept": "Continuar sin soporte", "options.graphics.warning.cancel": "Volver atrás", "options.graphics.warning.message": "Hemos detectado que tu tarjeta gráfica no tiene soporte para la opción de gráficos %s.\n\n<PERSON><PERSON><PERSON> ignorar este mensaje y continuar, pero no ofreceremos soporte para tu dispositivo si eliges usar los gráficos %s.", "options.graphics.warning.renderer": "Renderizador detectado: [%s]", "options.graphics.warning.title": "Dispositivo gráfico incompatible", "options.graphics.warning.vendor": "Fabricante detectado: [%s]", "options.graphics.warning.version": "Versión de OpenGL detectada: [%s]", "options.guiScale": "Tamaño de interfaz", "options.guiScale.auto": "Auto", "options.hidden": "Oculto", "options.hideLightningFlashes": "Ocultar re<PERSON>", "options.hideLightningFlashes.tooltip": "Apaga los destellos en el cielo producidos por rayos. Los rayos en sí permanecerán visibles.", "options.hideMatchedNames": "Ocultar nombres iguales", "options.hideMatchedNames.tooltip": "Los servidores de terceros podrían enviar mensajes en formatos no estándar.\nCon esta opción activada, los jugadores ocultos serán emparejados según su nombre de emisor en el chat.", "options.hideSplashTexts": "Ocultar textos splash", "options.hideSplashTexts.tooltip": "Oculta los textos splash amarillos del menú principal.", "options.inactivityFpsLimit": "Reducir FPS al", "options.inactivityFpsLimit.afk": "Estar AFK", "options.inactivityFpsLimit.afk.tooltip": "<PERSON>ita la tasa de fotogramas a 30 si el jugador no interactúa con el juego por más de un minuto. Se reducirá a 10 luego de 9 minutos más.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON>ita la tasa de fotogramas al minimizar la ventana del juego.", "options.invertMouse": "Invertir mouse", "options.japaneseGlyphVariants": "<PERSON><PERSON><PERSON> de glifos japoneses", "options.japaneseGlyphVariants.tooltip": "Usa variantes japonesas de los caracteres CJK en la fuente predeterminada.", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Alternar", "options.language": "Idioma...", "options.language.title": "Idioma", "options.languageAccuracyWarning": "(Algunas traducciones pueden contener errores)", "options.languageWarning": "La traducción puede que no sea correcta al 100%%", "options.mainHand": "<PERSON><PERSON> principal", "options.mainHand.left": "Iz<PERSON>erda", "options.mainHand.right": "Derecha", "options.mipmapLevels": "<PERSON><PERSON>", "options.modelPart.cape": "Capa", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON><PERSON>a", "options.modelPart.left_sleeve": "Manga izquierda", "options.modelPart.right_pants_leg": "Pernera derecha", "options.modelPart.right_sleeve": "Manga derecha", "options.mouseWheelSensitivity": "Sensibilidad de scroll", "options.mouse_settings": "Ajustes del mouse...", "options.mouse_settings.title": "Ajustes del mouse", "options.multiplayer.title": "Multijugador...", "options.multiplier": "%sx", "options.music_frequency": "Frecuencia de la música", "options.music_frequency.constant": "<PERSON><PERSON><PERSON>", "options.music_frequency.default": "Predeterminado", "options.music_frequency.frequent": "Frecuente", "options.music_frequency.tooltip": "Cambia qué tan seguido se reproduce la música mientras estás en el mundo del juego.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "<PERSON><PERSON><PERSON> todo", "options.narrator.chat": "<PERSON><PERSON><PERSON> chat", "options.narrator.notavailable": "No disponible", "options.narrator.off": "NO", "options.narrator.system": "<PERSON><PERSON><PERSON> sistema", "options.notifications.display_time": "Tiempo de notificación", "options.notifications.display_time.tooltip": "Controla por cuánto tiempo se mostrarán las notificaciones en pantalla.", "options.off": "NO", "options.off.composed": "%s: NO", "options.on": "SÍ", "options.on.composed": "%s: SÍ", "options.online": "Online...", "options.online.title": "Opciones del online", "options.onlyShowSecureChat": "<PERSON><PERSON> seguro", "options.onlyShowSecureChat.tooltip": "Solo mostrar los mensajes de otros jugadores que pueda verificarse que hayan sido enviados por ellos, y que no hayan sido modificados.", "options.operatorItemsTab": "Ítems de administrador", "options.particles": "Partículas", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "Reducidas", "options.particles.minimal": "Mínimas", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Chunks", "options.prioritizeChunkUpdates.byPlayer": "<PERSON>r jugador", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Ciertas acciones en un chunk harán que este se recompile inmediatamente. Esto incluye poner y destruir bloques.", "options.prioritizeChunkUpdates.nearby": "Completos", "options.prioritizeChunkUpdates.nearby.tooltip": "Los chunks cercanos se compilarán constantemente. Esto podría afectar el rendimiento del juego al poner o destruir bloques.", "options.prioritizeChunkUpdates.none": "Por hilos", "options.prioritizeChunkUpdates.none.tooltip": "Los chunks cercanos se compilarán en hilos paralelos. Esto podría resultar en falsos vacíos visuales al romper bloques.", "options.rawMouseInput": "Entrada directa", "options.realmsNotifications": "Novedades de Realms e invitaciones", "options.realmsNotifications.tooltip": "Busca noticias e invitaciones de Realms en el menú principal y muestra sus respectivos íconos en el botón de Realms.", "options.reducedDebugInfo": "Reducir datos de F3", "options.renderClouds": "Nubes", "options.renderCloudsDistance": "Distancia de las nubes", "options.renderDistance": "Renderizado", "options.resourcepack": "Paquetes de recursos...", "options.rotateWithMinecart": "<PERSON><PERSON><PERSON> con vagones", "options.rotateWithMinecart.tooltip": "Controla si la cámara debería rotar al montar en un vagón. Disponible solo en mundos con el ajuste experimental «Mejoras de vagón» activado.", "options.screenEffectScale": "Efectos de distorsión", "options.screenEffectScale.tooltip": "Intensidad de los efectos de distorsión en pantalla causados por la náusea y los portales del Nether.\nA valores menores, los efectos de náusea se sustituirán por un coloreado verdoso de la pantalla.", "options.sensitivity": "Sensibilidad", "options.sensitivity.max": "¡¡HIPERRÁPIDO!!", "options.sensitivity.min": "*bostezo*", "options.showNowPlayingToast": "Mostrar notificación de música", "options.showNowPlayingToast.tooltip": "Muestra una notificación cuando comienza una canción. La misma notificación se muestra constantemente en el menú de pausa del juego mientras la canción está sonando.", "options.showSubtitles": "Mostrar subtítulos", "options.simulationDistance": "Simulación", "options.skinCustomisation": "Personalizar skin...", "options.skinCustomisation.title": "Personalizar skin", "options.sounds": "Música y sonido...", "options.sounds.title": "Opciones de música y sonido", "options.telemetry": "Telemetría...", "options.telemetry.button": "Recopilación de datos", "options.telemetry.button.tooltip": "\"%s\" solo incluye la información necesaria.\n\"%s\" incluye información opcional tanto como necesaria.", "options.telemetry.disabled": "La telemetría está desactivada.", "options.telemetry.state.all": "Todo", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "NO", "options.title": "Opciones", "options.touchscreen": "<PERSON>do <PERSON> táctil", "options.video": "Gráficos...", "options.videoTitle": "Ajustes de gráficos", "options.viewBobbing": "Balanceo", "options.visible": "Visible", "options.vsync": "VSync", "outOfMemory.message": "Minecraft se quedó sin memoria.\n\nEsto podría deberse a un error en el juego o a que la máquina virtual de Java no tiene suficiente memoria asignada.\n\nPara evitar la corrupción de tus mundos, el juego se cerró. Hemos intentado liberar suficiente memoria para permitirte regresar al menú principal y volver a jugar, pero es posible que esto no haya funcionado.\n\nReinicia el juego si vuelves a ver este mensaje.", "outOfMemory.title": "¡Sin memoria!", "pack.available.title": "Disponible", "pack.copyFailure": "Error al copiar los paquetes", "pack.dropConfirm": "¿Quieres agregar los siguientes paquetes a Minecraft?", "pack.dropInfo": "Arrastra y suelta archivos a esta ventana para agregar paquetes", "pack.dropRejected.message": "No se copiaron las siguientes entradas por no ser paquetes válidos:\n %s", "pack.dropRejected.title": "Entradas de tipo no-paquete", "pack.folderInfo": "(Pon aquí los archivos de paquetes)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "Este paquete ha sido diseñado para una versión de Minecraft más reciente y puede que no funcione correctamente.", "pack.incompatible.confirm.old": "Este paquete ha sido diseñado para una versión de Minecraft más antigua y puede que ya no funcione correctamente.", "pack.incompatible.confirm.title": "¿Se<PERSON>ro que quieres cargar este paquete?", "pack.incompatible.new": "(Diseñado para una versión de Minecraft más reciente)", "pack.incompatible.old": "(Diseñado para una versión de Minecraft anterior)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON>a", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "integrado", "pack.source.feature": "característica", "pack.source.local": "local", "pack.source.server": "server", "pack.source.world": "mundo", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Variante aleatoria", "parsing.bool.expected": "Se requiere un valor booleano", "parsing.bool.invalid": "<PERSON>or booleano no válido: \"%s\" no es ni \"true\" ni \"false”", "parsing.double.expected": "Se requiere un valor doble", "parsing.double.invalid": "Valor doble no válido: %s", "parsing.expected": "Se requiere \"%s\"", "parsing.float.expected": "Se requiere un valor float", "parsing.float.invalid": "Valor float no válido: %s", "parsing.int.expected": "Se requiere un número entero", "parsing.int.invalid": "Número entero no válido: %s", "parsing.long.expected": "Se requiere un valor long", "parsing.long.invalid": "Variable long '%s' inválida", "parsing.quote.escape": "La secuencia de escape \"\\%s\" no es válida en una cadena con comillas", "parsing.quote.expected.end": "Falta el cierre de comillas de la cadena", "parsing.quote.expected.start": "Faltan comillas al inicio de la cadena", "particle.invalidOptions": "No se pudo analizar las opciones de partículas: %s", "particle.notFound": "Partícula desconocida: %s", "permissions.requires.entity": "Se requiere una entidad para ejecutar aquí este comando", "permissions.requires.player": "Se requiere un jugador para ejecutar aquí este comando", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Al aplicarse:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicado desconocido: %s", "quickplay.error.invalid_identifier": "No se pudo encontrar el mundo con el identificador proporcionado", "quickplay.error.realm_connect": "No se pudo conectar al Realm", "quickplay.error.realm_permission": "No tienes permiso para conectarte a este Realm", "quickplay.error.title": "Error al ejecutar inicio rápido", "realms.configuration.region.australia_east": "Nueva Gales del Sur, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, EE. UU.", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, EE. UU.", "realms.configuration.region.east_us_2": "Carolina del Norte, EE. UU.", "realms.configuration.region.france_central": "Francia", "realms.configuration.region.japan_east": "Este de Japón", "realms.configuration.region.japan_west": "Oeste de Japón", "realms.configuration.region.korea_central": "Corea del Sur", "realms.configuration.region.north_central_us": "Illinois, EE. UU.", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Tejas, EE. UU.", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON>", "realms.configuration.region.uae_north": "Emiratos Árabes Unidos (EAU)", "realms.configuration.region.uk_south": "Sur de Inglaterra", "realms.configuration.region.west_central_us": "Utah, EE. UU.", "realms.configuration.region.west_europe": "País<PERSON>", "realms.configuration.region.west_us": "California, EE. UU.", "realms.configuration.region.west_us_2": "Washington, EE. UU.", "realms.configuration.region_preference.automatic_owner": "Automática (según la conexión del dueño)", "realms.configuration.region_preference.automatic_player": "Automática (según quien entre primero)", "realms.missing.snapshot.error.text": "Minecraft Realms no es compatible con las versiones de prueba", "recipe.notFound": "Receta desconocida: %s", "recipe.toast.description": "Ve tu libro de recetas", "recipe.toast.title": "¡Nuevas receta(s)!", "record.nowPlaying": "Estás escuchando: %s", "recover_world.bug_tracker": "Reportar un bug", "recover_world.button": "Intentar recuperar", "recover_world.done.failed": "No se pudo recuperar el mundo en su estado anterior.", "recover_world.done.success": "¡Recuperación exitosa!", "recover_world.done.title": "Recuperación completada", "recover_world.issue.missing_file": "Falta un archivo", "recover_world.issue.none": "<PERSON> problemas", "recover_world.message": "Ocurrieron los siguientes errores al intentar leer la carpeta \"%s\" del mundo.\nPodría ser posible restaurar el mundo de algún estado anterior o puedes reportar el problema en el bug tracker.", "recover_world.no_fallback": "No hay ningún estado anterior para restaurar", "recover_world.restore": "Intentar restaurar", "recover_world.restoring": "Intentando restaurar el mundo...", "recover_world.state_entry": "Estado desde %s: ", "recover_world.state_entry.unknown": "desconocido", "recover_world.title": "Error al cargar el mundo", "recover_world.warning": "Error al cargar el resumen del mundo", "resourcePack.broken_assets": "RECURSOS DEFECTUOSOS DETECTADOS", "resourcePack.high_contrast.name": "<PERSON> contraste", "resourcePack.load_fail": "Error al recargar el recurso", "resourcePack.programmer_art.name": "Arte de programador", "resourcePack.runtime_failure": "Se ha detectado un error en un paquete de recursos", "resourcePack.server.name": "Recursos específicos del mundo", "resourcePack.title": "Elegir paquetes de recursos", "resourcePack.vanilla.description": "El estilo y sentir clásico de Minecraft", "resourcePack.vanilla.name": "Por defecto", "resourcepack.downloading": "Descargando paque<PERSON> de recursos", "resourcepack.progress": "Descargando archivo (%s MB)...", "resourcepack.requesting": "Haciendo solicitud...", "screenshot.failure": "No se pudo guardar la captura de pantalla: %s", "screenshot.success": "Captura guardada como %s", "selectServer.add": "Agregar server", "selectServer.defaultName": "Server de Minecraft", "selectServer.delete": "Bo<PERSON>r", "selectServer.deleteButton": "Bo<PERSON>r", "selectServer.deleteQuestion": "¿Seguro que deseas remover este server?", "selectServer.deleteWarning": "\"%s\" desaparecerá... ¡para siempre! (¡Por mucho tiempo!)", "selectServer.direct": "Conexión directa", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(IP oculta)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Entrar al server", "selectWorld.access_failure": "Error accediendo al mundo", "selectWorld.allowCommands": "Per<PERSON><PERSON> comandos", "selectWorld.allowCommands.info": "Comandos como /gamemode, /experience...", "selectWorld.allowCommands.new": "Per<PERSON><PERSON> comandos", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> datos en caché", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON>r un respaldo y cargar", "selectWorld.backupJoinSkipButton": "¡Sé lo que estoy haciendo!", "selectWorld.backupQuestion.customized": "Los mundos personalizados ya no son compatibles", "selectWorld.backupQuestion.downgrade": "Degradar un mundo a una versión anterior no está soportado", "selectWorld.backupQuestion.experimental": "Los mundos que usan los ajustes experimentales no tienen soporte", "selectWorld.backupQuestion.snapshot": "¿Seguro de que quieres cargar este mundo?", "selectWorld.backupWarning.customized": "Desafortunadamente, los mundos personalizados no son compatibles con esta versión de Minecraft. Aún así, podemos seguir cargando este mundo y mantenerlo tal y como era, pero todo el terreno nuevo que se genere no será personalizado. ¡Sentimos las molestias!", "selectWorld.backupWarning.downgrade": "Este mundo fue jugado por última vez en la versión %s; estás en la versión %s. Degradar la versión de un mundo podría corromperlo. No podemos garantizar que vaya a cargar o funcionar. Si aún así quieres continuar, ¡por favor haz un respaldo!", "selectWorld.backupWarning.experimental": "Este mundo usa ajustes experimentales que podrían dejar de funcionar en cualquier momento. No podemos garantizar que cargue o funcione. ¡Podría ser peligroso!", "selectWorld.backupWarning.snapshot": "Este mundo se jugó por última vez en la versión %s; estás en la versión %s. Te recomendamos crear un respaldo en caso de que exprimentes problemas con el mundo.", "selectWorld.bonusItems": "Cofre bonus", "selectWorld.cheats": "Trucos", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "¡Debe ser convertido!", "selectWorld.conversion.tooltip": "Este mundo debe ser abierto en una versión antigua (como la 1.6.4) para ser convertido de forma segura", "selectWorld.create": "<PERSON><PERSON>r un mundo nuevo", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "Paquetes de <PERSON>", "selectWorld.data_read": "Leyendo datos del mundo...", "selectWorld.delete": "Eliminar", "selectWorld.deleteButton": "Bo<PERSON>r", "selectWorld.deleteQuestion": "¿Estás seguro de que quieres borrar este mundo?", "selectWorld.deleteWarning": "¡“%s” desaparecerá para siempre! (¡Por muuucho tiempo!)", "selectWorld.delete_failure": "Error eliminando el mundo", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON><PERSON> un respaldo", "selectWorld.edit.backupCreated": "Copiado: %s", "selectWorld.edit.backupFailed": "Error al realizar el respaldo", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON>a de copias", "selectWorld.edit.backupSize": "mundo copiado (%s MB)", "selectWorld.edit.export_worldgen_settings": "Exportar ajustes de generación", "selectWorld.edit.export_worldgen_settings.failure": "Error al exportar", "selectWorld.edit.export_worldgen_settings.success": "Exportado", "selectWorld.edit.openFolder": "<PERSON><PERSON>r carpeta del mundo", "selectWorld.edit.optimize": "<PERSON>timi<PERSON> mundo", "selectWorld.edit.resetIcon": "Restable<PERSON>", "selectWorld.edit.save": "Guardar", "selectWorld.edit.title": "<PERSON><PERSON>", "selectWorld.enterName": "Nombre del mundo", "selectWorld.enterSeed": "Semilla para generar el mundo", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalles", "selectWorld.experimental.details.entry": "Características experimentales requeridas: %s", "selectWorld.experimental.details.title": "Requisitos de características experimentales", "selectWorld.experimental.message": "¡Ten cuidado!\nEsta configuración hace uso de características que aún están en desarrollo. Tu mundo podría crashear, corromperse o no funcionar en futuras actualizaciones.", "selectWorld.experimental.title": "Aviso de características experimentales", "selectWorld.experiments": "Experimentos", "selectWorld.experiments.info": "Los experimentos son posibles nuevas características. Sé cuidadoso porque pueden romper ciertas cosas. Los experimentos no se pueden desactivar después de crear el mundo.", "selectWorld.futureworld.error.text": "Algo falló intentando cargar un mundo de una versión posterior. Esta fue una operación riesgosa desde el inicio; Sentimos que no haya funcionado.", "selectWorld.futureworld.error.title": "¡Ha ocurrido un error!", "selectWorld.gameMode": "Modo", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Igual al modo supervivencia, pero no se pueden poner ni romper bloques.", "selectWorld.gameMode.adventure.line1": "Igual que el modo supervivencia, pero no", "selectWorld.gameMode.adventure.line2": "puedes colocar ni destruir bloques", "selectWorld.gameMode.creative": "Creativo", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, construye y explora sin límites. <PERSON><PERSON><PERSON> volar, tener recursos ilimitados, y los monstruos no podrán hacerte daño.", "selectWorld.gameMode.creative.line1": "Recursos ilimitados, vuelo libre y", "selectWorld.gameMode.creative.line2": "destrucción de bloques al instante.", "selectWorld.gameMode.hardcore": "Extremo", "selectWorld.gameMode.hardcore.info": "Como el modo supervivencia, pero con la dificultad bloqueada en \"Difícil\". <PERSON> mueres, no podrás reaparecer.", "selectWorld.gameMode.hardcore.line1": "Igual que el modo supervivencia, pero", "selectWorld.gameMode.hardcore.line2": "bloqueado en difícil y con una única vida.", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON><PERSON> mi<PERSON>, pero no tocar.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON><PERSON> mi<PERSON>, pero no tocar", "selectWorld.gameMode.survival": "Supervivencia", "selectWorld.gameMode.survival.info": "Explora un misterioso mundo en donde construirás, recolectarás, craftearás y lucharás contra monstruos.", "selectWorld.gameMode.survival.line1": "Busca recursos, fabrica herramientas", "selectWorld.gameMode.survival.line2": "obtén niveles, salud y comida.", "selectWorld.gameRules": "Reglas del juego", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON>r a<PERSON>", "selectWorld.import_worldgen_settings.failure": "Error al importar ajustes", "selectWorld.import_worldgen_settings.select_file": "Seleccionar archivo de ajustes (.json)", "selectWorld.incompatible.description": "Este mundo no se puede jugar en esta versión.\nSe jugó por última vez en la versión %s.", "selectWorld.incompatible.info": "Versión incompatible: %s", "selectWorld.incompatible.title": "Versión incompatible", "selectWorld.incompatible.tooltip": "No se puede jugar este mundo porque fue creado en una versión incompatible.", "selectWorld.incompatible_series": "Creado en una versión incompatible", "selectWorld.load_folder_access": "¡No se ha podido leer o acceder a la carpeta donde se guardan los mundos!", "selectWorld.loading_list": "Cargando lista de mundos", "selectWorld.locked": "Mundo abierto en otra sesión activa de Minecraft", "selectWorld.mapFeatures": "Generar estructuras", "selectWorld.mapFeatures.info": "Aldeas, naufragios, etc.", "selectWorld.mapType": "Tipo de mundo", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Más opciones del mundo...", "selectWorld.newWorld": "Mundo Nuevo", "selectWorld.recreate": "<PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Los mundos personalizados no son compatibles con esta versión de Minecraft. Podríamos intentar recrear el mundo con la misma semilla y las mismas propiedades, pero todo el terreno personalizado se perderá. ¡Sentimos las molestias!", "selectWorld.recreate.customized.title": "Los mundos personalizados ya no son compatibles", "selectWorld.recreate.error.text": "Algo falló mientras se trataba de recrear un mundo.", "selectWorld.recreate.error.title": "¡Se produjo un error!", "selectWorld.resource_load": "Preparando recursos...", "selectWorld.resultFolder": "Será guardado en:", "selectWorld.search": "buscar mundos", "selectWorld.seedInfo": "Dejar en blanco para una semilla al azar", "selectWorld.select": "<PERSON><PERSON> al mundo seleccionado", "selectWorld.targetFolder": "Carpeta de guardado: %s", "selectWorld.title": "Seleccionar mundo", "selectWorld.tooltip.fromNewerVersion1": "Este mundo fue guardado en una versión más reciente,", "selectWorld.tooltip.fromNewerVersion2": "¡cargar este mundo podría causar problemas!", "selectWorld.tooltip.snapshot1": "No te olvides de respaldar este mundo", "selectWorld.tooltip.snapshot2": "antes de cargarlo en esta snapshot.", "selectWorld.unable_to_load": "No se pudieron cargar los mundos", "selectWorld.version": "Versión:", "selectWorld.versionJoinButton": "<PERSON><PERSON> de todas formas", "selectWorld.versionQuestion": "¿Seguro de que quieres cargar este mundo?", "selectWorld.versionUnknown": "desconocido", "selectWorld.versionWarning": "¡Este mundo se jugó por última vez en la versión %s! ¡Si lo juegas en la versión actual podría corromperse!", "selectWorld.warning.deprecated.question": "Algunas de las características utilizadas están obsoletas y dejarán de funcionar en el futuro. ¿Quieres continuar?", "selectWorld.warning.deprecated.title": "¡Advertencia! Estos ajustes utilizan características obsoletas", "selectWorld.warning.experimental.question": "Estos ajustes son experimentales y podrían dejar de funcionar un día. ¿Deseas continuar?", "selectWorld.warning.experimental.title": "¡Advertencia! Estos ajustes utilizan características experimentales", "selectWorld.warning.lowDiskSpace.description": "No hay suficiente espacio de almacenamiento en tu dispositivo.\nQuedarte sin espacio durante la partida podría ocasionar que tu mundo se corrompa.", "selectWorld.warning.lowDiskSpace.title": "¡Advertencia! ¡Almacenamiento insuficiente!", "selectWorld.world": "Mundo", "sign.edit": "Editar mensaje del cartel", "sleep.not_possible": "La cantidad de jugadores durmiendo no es suficiente para pasar la noche", "sleep.players_sleeping": "%s/%s jugadores durmiendo", "sleep.skipping_night": "Saltando esta noche", "slot.only_single_allowed": "Solo se admiten espacios individuales, se obtuvo '%s'", "slot.unknown": "Espacio desconocido: %s", "snbt.parser.empty_key": "La llave no puede estar vacía", "snbt.parser.expected_binary_numeral": "Se esperaba un número binario", "snbt.parser.expected_decimal_numeral": "Se esperaba un número decimal", "snbt.parser.expected_float_type": "Se esperaba un número de coma flotante", "snbt.parser.expected_hex_escape": "Se esperaba una cadena de longitud %s", "snbt.parser.expected_hex_numeral": "Se esperaba un número hexadecimal", "snbt.parser.expected_integer_type": "Se esperaba un número entero", "snbt.parser.expected_non_negative_number": "Se esperaba un número no negativo", "snbt.parser.expected_number_or_boolean": "Se requiere un número o un valor booleano", "snbt.parser.expected_string_uuid": "Se requiere una cadena con un UUID válido", "snbt.parser.expected_unquoted_string": "Se requiere una cadena sin comillas válida", "snbt.parser.infinity_not_allowed": "No se permiten números infinitos", "snbt.parser.invalid_array_element_type": "Tipo de formación de elementos no válida", "snbt.parser.invalid_character_name": "Nombre de carácter Unicode no válido", "snbt.parser.invalid_codepoint": "Carácter Unicode no válido: %s", "snbt.parser.invalid_string_contents": "Contenido de cadenas no válido", "snbt.parser.invalid_unquoted_start": "Las cadenas sin comillas no pueden empezar por los dígitos 0-9, + o -", "snbt.parser.leading_zero_not_allowed": "Los números decimales no pueden empezar con 0", "snbt.parser.no_such_operation": "La operación no existe: %s", "snbt.parser.number_parse_failure": "Error al analizar número: %s", "snbt.parser.undescore_not_allowed": "Caracteres subrayados no están permitidos al comienzo o al final de un número", "soundCategory.ambient": "Ambiente", "soundCategory.block": "Bloques", "soundCategory.hostile": "Criaturas hostiles", "soundCategory.master": "Volumen general", "soundCategory.music": "Música", "soundCategory.neutral": "Criaturas pacíficas", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Bloques musicales", "soundCategory.ui": "Interfaz", "soundCategory.voice": "Voces y diálogos", "soundCategory.weather": "Clima", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "spectatorMenu.previous_page": "Página anterior", "spectatorMenu.root.prompt": "Presiona una tecla para seleccionar un comando, y vuelve a presionarla para usarlo.", "spectatorMenu.team_teleport": "Teletransportarse a un miembro del equipo", "spectatorMenu.team_teleport.prompt": "Elige un equipo al que teletransportarte", "spectatorMenu.teleport": "Teletransportarse a un jugador", "spectatorMenu.teleport.prompt": "Elige a un jugador al que teletransportarte", "stat.generalButton": "General", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "<PERSON><PERSON> criados", "stat.minecraft.aviate_one_cm": "Distancia volada con elytra", "stat.minecraft.bell_ring": "Campanas to<PERSON>das", "stat.minecraft.boat_one_cm": "Distancia en bote", "stat.minecraft.clean_armor": "Piezas de armadura limpiadas", "stat.minecraft.clean_banner": "Estandartes limpiados", "stat.minecraft.clean_shulker_box": "Cajas de shulker vaciadas", "stat.minecraft.climb_one_cm": "Distancia escalada", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.damage_absorbed": "Daño <PERSON>", "stat.minecraft.damage_blocked_by_shield": "Daño bloqueado con Escudo", "stat.minecraft.damage_dealt": "<PERSON><PERSON> causado", "stat.minecraft.damage_dealt_absorbed": "<PERSON>ño causado (absorbido)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> causado (resistido)", "stat.minecraft.damage_resisted": "Daño resistido", "stat.minecraft.damage_taken": "<PERSON><PERSON> recibido", "stat.minecraft.deaths": "Númer<PERSON>uer<PERSON>", "stat.minecraft.drop": "Objetos tirados", "stat.minecraft.eat_cake_slice": "Trozos de torta comidos", "stat.minecraft.enchant_item": "Objetos encantados", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "Calderos ll<PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON> capturados", "stat.minecraft.fly_one_cm": "Distancia volada", "stat.minecraft.happy_ghast_one_cm": "Distancia en ghast feliz", "stat.minecraft.horse_one_cm": "Distancia a caballo", "stat.minecraft.inspect_dispenser": "Dispensadores examinados", "stat.minecraft.inspect_dropper": "Soltadores examinados", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "Usos de yunques", "stat.minecraft.interact_with_beacon": "Usos de faros", "stat.minecraft.interact_with_blast_furnace": "Usos de altos hornos", "stat.minecraft.interact_with_brewingstand": "Usos de alambiques", "stat.minecraft.interact_with_campfire": "Usos de fogatas", "stat.minecraft.interact_with_cartography_table": "Usos de mesas de cartografía", "stat.minecraft.interact_with_crafting_table": "Usos de mesas de crafteo", "stat.minecraft.interact_with_furnace": "Usos de hornos", "stat.minecraft.interact_with_grindstone": "Usos de afiladoras", "stat.minecraft.interact_with_lectern": "Usos de atriles", "stat.minecraft.interact_with_loom": "Usos de telares", "stat.minecraft.interact_with_smithing_table": "Usos de mesas de herrería", "stat.minecraft.interact_with_smoker": "Usos de ahumadores", "stat.minecraft.interact_with_stonecutter": "Usos de cortapiedras", "stat.minecraft.jump": "Saltos", "stat.minecraft.leave_game": "Veces que saliste", "stat.minecraft.minecart_one_cm": "Distancia en vagón", "stat.minecraft.mob_kills": "<PERSON><PERSON> matados", "stat.minecraft.open_barrel": "<PERSON><PERSON> abiertos", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> abie<PERSON>", "stat.minecraft.open_enderchest": "Cofres del End abiertos", "stat.minecraft.open_shulker_box": "Cajas de shulker abiertas", "stat.minecraft.pig_one_cm": "Distancia en chancho", "stat.minecraft.play_noteblock": "Bloques musicales tocados", "stat.minecraft.play_record": "Discos reproducidos", "stat.minecraft.play_time": "Tiempo jugado", "stat.minecraft.player_kills": "Jugadores asesinados", "stat.minecraft.pot_flower": "Plantas colocadas en maceteros", "stat.minecraft.raid_trigger": "Invasiones causadas", "stat.minecraft.raid_win": "<PERSON>es ganadas", "stat.minecraft.sleep_in_bed": "Veces dormido/a", "stat.minecraft.sneak_time": "Tiempo agachado", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> corriendo", "stat.minecraft.strider_one_cm": "Distancia en strider", "stat.minecraft.swim_one_cm": "Distancia nadada", "stat.minecraft.talked_to_villager": "Conversaciones con aldeanos", "stat.minecraft.target_hit": "<PERSON><PERSON>", "stat.minecraft.time_since_death": "Tiempo desde la última muerte", "stat.minecraft.time_since_rest": "Tiempo desde la última siesta", "stat.minecraft.total_world_time": "Tiempo con el mundo abierto", "stat.minecraft.traded_with_villager": "Comercios con aldeanos", "stat.minecraft.trigger_trapped_chest": "Cofres trampa activados", "stat.minecraft.tune_noteblock": "Bloques musicales afinados", "stat.minecraft.use_cauldron": "Agua tomada de calderos", "stat.minecraft.walk_on_water_one_cm": "Recorrido sobre agua", "stat.minecraft.walk_one_cm": "Distancia caminada", "stat.minecraft.walk_under_water_one_cm": "Recorrido bajo agua", "stat.mobsButton": "<PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON> roto", "stat_type.minecraft.crafted": "Veces fabricado", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.killed": "Has matado %s", "stat_type.minecraft.killed.none": "Nunca has matado uno/a", "stat_type.minecraft.killed_by": "Te ha matado %2$s vez/veces", "stat_type.minecraft.killed_by.none": "Nunca te ha matado", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON> picado", "stat_type.minecraft.picked_up": "Veces recolectado", "stat_type.minecraft.used": "<PERSON>eces usado", "stats.none": "-", "structure_block.button.detect_size": "DETECTAR", "structure_block.button.load": "CARGAR", "structure_block.button.save": "GUARDAR", "structure_block.custom_data": "Nombre de dataTag personalizado", "structure_block.detect_size": "Detectar tamaño y posición:", "structure_block.hover.corner": "Esquina: %s", "structure_block.hover.data": "Datos: %s", "structure_block.hover.load": "Cargar: %s", "structure_block.hover.save": "Guardar: %s", "structure_block.include_entities": "Incluir entidades:", "structure_block.integrity": "Integridad y semilla de la estructura", "structure_block.integrity.integrity": "Integridad de la estructura", "structure_block.integrity.seed": "Semilla de la estructura", "structure_block.invalid_structure_name": "\"%s\" no es un nombre de estructura válido", "structure_block.load_not_found": "La estructura \"%s\" no está disponible", "structure_block.load_prepare": "Posición de la estructura \"%s\" preparada", "structure_block.load_success": "Estructura \"%s\" cargada", "structure_block.mode.corner": "Esquina", "structure_block.mode.data": "Datos", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Guardar", "structure_block.mode_info.corner": "Esquina: indica posición y tamaño", "structure_block.mode_info.data": "Modo datos: usa la lógica del juego", "structure_block.mode_info.load": "Modo cargar: carga desde un archivo", "structure_block.mode_info.save": "Modo guardar: guarda en un archivo", "structure_block.position": "Posición relativa", "structure_block.position.x": "posición relativa x", "structure_block.position.y": "posición relativa y", "structure_block.position.z": "posición relativa z", "structure_block.save_failure": "No se pudo guardar la estructura \"%s\"", "structure_block.save_success": "Estructura guardada como \"%s\"", "structure_block.show_air": "Mostrar bloques invisibles:", "structure_block.show_boundingbox": "Mostrar límites (Bounding Box):", "structure_block.size": "Tamaño de la estructura", "structure_block.size.x": "tamaño de la estructura x", "structure_block.size.y": "tamaño de la estructura y", "structure_block.size.z": "tamaño de la estructura z", "structure_block.size_failure": "No se ha podido detectar el tamaño de la estructura. Añade más esquinas con el nombre de la estructura correspondiente", "structure_block.size_success": "Tamaño de \"%s\" detectado", "structure_block.strict": "Colocación estricta:", "structure_block.structure_name": "Nombre de la estructura", "subtitles.ambient.cave": "Ruido desconocido", "subtitles.ambient.sound": "Ruido inquietante", "subtitles.block.amethyst_block.chime": "Amatista resuena", "subtitles.block.amethyst_block.resonate": "Amatista resuena", "subtitles.block.anvil.destroy": "<PERSON><PERSON> des<PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON> usado", "subtitles.block.barrel.close": "Barril se cierra", "subtitles.block.barrel.open": "Barril se abre", "subtitles.block.beacon.activate": "Faro se activa", "subtitles.block.beacon.ambient": "<PERSON><PERSON> resonando", "subtitles.block.beacon.deactivate": "Faro se desactiva", "subtitles.block.beacon.power_select": "Poder del faro seleccionado", "subtitles.block.beehive.drip": "<PERSON><PERSON>", "subtitles.block.beehive.enter": "<PERSON><PERSON> entra en una colmena", "subtitles.block.beehive.exit": "Abeja sale de una colmena", "subtitles.block.beehive.shear": "Tijeras cortan", "subtitles.block.beehive.work": "<PERSON><PERSON>", "subtitles.block.bell.resonate": "Campana resuena", "subtitles.block.bell.use": "Campanada", "subtitles.block.big_dripleaf.tilt_down": "Plantaforma ladea hacia abajo", "subtitles.block.big_dripleaf.tilt_up": "Plantaforma se ladea hacia arriba", "subtitles.block.blastfurnace.fire_crackle": "Alto horno chispea", "subtitles.block.brewing_stand.brew": "Alambique burbujea", "subtitles.block.bubble_column.bubble_pop": "Estallido de burbuja", "subtitles.block.bubble_column.upwards_ambient": "Flujo de burbujas", "subtitles.block.bubble_column.upwards_inside": "Sonido de burbujas", "subtitles.block.bubble_column.whirlpool_ambient": "Remolino de burbujas", "subtitles.block.bubble_column.whirlpool_inside": "Zumbido de burbujas", "subtitles.block.button.click": "Chasquido de botón", "subtitles.block.cake.add_candle": "Torta se aplasta", "subtitles.block.campfire.crackle": "Fogata chisporrotea", "subtitles.block.candle.crackle": "<PERSON>ela chisporrotea", "subtitles.block.candle.extinguish": "Vela se apaga", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON> cer<PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> blo<PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON> abierto", "subtitles.block.chorus_flower.death": "Flor chorus se marchita", "subtitles.block.chorus_flower.grow": "Flor chorus crece", "subtitles.block.comparator.click": "Chasquido de comparador", "subtitles.block.composter.empty": "Compostera vaciada", "subtitles.block.composter.fill": "Compostera llenada", "subtitles.block.composter.ready": "Compostaje listo", "subtitles.block.conduit.activate": "Canalizador se activa", "subtitles.block.conduit.ambient": "Canalizador emite un pulso", "subtitles.block.conduit.attack.target": "Canalizador atacando", "subtitles.block.conduit.deactivate": "Canalizador se desactiva", "subtitles.block.copper_bulb.turn_off": "Lámpara de cobre se apaga", "subtitles.block.copper_bulb.turn_on": "Lámpara de cobre se prende", "subtitles.block.copper_trapdoor.close": "Trampilla se cierra", "subtitles.block.copper_trapdoor.open": "Trampilla se abre", "subtitles.block.crafter.craft": "Crafteadora craftea", "subtitles.block.crafter.fail": "Crafteadora falla", "subtitles.block.creaking_heart.hurt": "Corazón de crujidor murmura", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.spawn": "Corazón de crujidor despierta", "subtitles.block.deadbush.idle": "Ruidos secos", "subtitles.block.decorated_pot.insert": "Vasija decorada se llena", "subtitles.block.decorated_pot.insert_fail": "Vasija decorada se tambalea", "subtitles.block.decorated_pot.shatter": "Vasija decorada se quiebra", "subtitles.block.dispenser.dispense": "Objeto dispensado", "subtitles.block.dispenser.fail": "Error del dispensador", "subtitles.block.door.toggle": "Crujido de puerta", "subtitles.block.dried_ghast.ambient": "Sonidos de sequedad", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> deshi<PERSON> se rehidrata", "subtitles.block.dried_ghast.place_in_water": "G<PERSON>t deshidratado puesto en agua", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> <PERSON> al<PERSON>ia", "subtitles.block.dry_grass.ambient": "Sonidos de viento", "subtitles.block.enchantment_table.use": "Mesa de encantamientos usada", "subtitles.block.end_portal.spawn": "Portal del End abierto", "subtitles.block.end_portal_frame.fill": "<PERSON>jo de ender es colocado", "subtitles.block.eyeblossom.close": "Miraflor se cierra", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON> susurra", "subtitles.block.eyeblossom.open": "Miraflor se abre", "subtitles.block.fence_gate.toggle": "Crujido de puerta de valla", "subtitles.block.fire.ambient": "Chisporroteo de fuego", "subtitles.block.fire.extinguish": "Fuego extinguido", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON> zumban", "subtitles.block.frogspawn.hatch": "Renacuajos eclosionan", "subtitles.block.furnace.fire_crackle": "Horn<PERSON> encendido", "subtitles.block.generic.break": "Bloque roto", "subtitles.block.generic.fall": "Algo cae sobre un bloque", "subtitles.block.generic.footsteps": "Pisadas", "subtitles.block.generic.hit": "Rotura de bloque", "subtitles.block.generic.place": "Bloque colocado", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON> usada", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> cortada", "subtitles.block.hanging_sign.waxed_interact_fail": "Cartel se tambalea", "subtitles.block.honey_block.slide": "Deslizamiento en un bloque de miel", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> cerrada", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON><PERSON> abierta", "subtitles.block.lava.ambient": "Burbujeo de lava", "subtitles.block.lava.extinguish": "Siseo de la lava", "subtitles.block.lever.click": "Chasquido de palanca", "subtitles.block.note_block.note": "Bloque musical sonando", "subtitles.block.pale_hanging_moss.idle": "Ruido inquietante", "subtitles.block.piston.move": "Movimiento de pistón", "subtitles.block.pointed_dripstone.drip_lava": "Goteo de lava", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Gota de lava cae en un caldero", "subtitles.block.pointed_dripstone.drip_water": "Goteo de agua", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Gota de agua cae en un caldero", "subtitles.block.pointed_dripstone.land": "Estalactita se cae y rompe", "subtitles.block.portal.ambient": "Zumbido de portal", "subtitles.block.portal.travel": "Ruido de portal desvaneciéndose", "subtitles.block.portal.trigger": "Ruido de portal intensificandose", "subtitles.block.pressure_plate.click": "Chasquido de placa de presión", "subtitles.block.pumpkin.carve": "Tijeras tallando", "subtitles.block.redstone_torch.burnout": "Antorcha arde", "subtitles.block.respawn_anchor.ambient": "Zumbido de nexo de reaparición", "subtitles.block.respawn_anchor.charge": "El nexo de reaparición está cargado", "subtitles.block.respawn_anchor.deplete": "Nexo de reaparición pierde carga", "subtitles.block.respawn_anchor.set_spawn": "El nexo de reaparición se ha establecido", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON> arenosos", "subtitles.block.sand.wind": "Viento", "subtitles.block.sculk.charge": "Sculk burbujea", "subtitles.block.sculk.spread": "Sculk se esparce", "subtitles.block.sculk_catalyst.bloom": "Catalizador de sculk brota", "subtitles.block.sculk_sensor.clicking": "Sensor de sculk comienza a chasquear", "subtitles.block.sculk_sensor.clicking_stop": "Sensor de sculk deja de chasquear", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> de skulk chilla", "subtitles.block.shulker_box.close": "Caja de shulker se cierra", "subtitles.block.shulker_box.open": "Caja de shulker se abre", "subtitles.block.sign.waxed_interact_fail": "Cartel se tambalea", "subtitles.block.smithing_table.use": "Mesa de herrería usada", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON> echa humo", "subtitles.block.sniffer_egg.crack": "Huevo de sniffer cruje", "subtitles.block.sniffer_egg.hatch": "Huevo de sniffer eclosiona", "subtitles.block.sniffer_egg.plop": "<PERSON>ni<PERSON> se deja caer", "subtitles.block.sponge.absorb": "Esponja <PERSON>", "subtitles.block.sweet_berry_bush.pick_berries": "Bayas caen", "subtitles.block.trapdoor.close": "Trampilla se cierra", "subtitles.block.trapdoor.open": "Trampilla se abre", "subtitles.block.trapdoor.toggle": "Crujido de trampilla", "subtitles.block.trial_spawner.about_to_spawn_item": "Objeto siniestro se prepara", "subtitles.block.trial_spawner.ambient": "Spawner de desafío crepita", "subtitles.block.trial_spawner.ambient_charged": "Chisporroteo ominoso", "subtitles.block.trial_spawner.ambient_ominous": "<PERSON><PERSON><PERSON> ominoso", "subtitles.block.trial_spawner.charge_activate": "Presagio engulle un Generador de Desafío", "subtitles.block.trial_spawner.close_shutter": "Spawner de desafío se cierra", "subtitles.block.trial_spawner.detect_player": "Spawner de desafío se carga", "subtitles.block.trial_spawner.eject_item": "Spawner de desafío expulsa ítems", "subtitles.block.trial_spawner.ominous_activate": "Presagio envuelve un Generador de Desafío", "subtitles.block.trial_spawner.open_shutter": "Spawner de desafío se abre", "subtitles.block.trial_spawner.spawn_item": "Objeto ominoso soltado", "subtitles.block.trial_spawner.spawn_item_begin": "Objeto ominoso aparece", "subtitles.block.trial_spawner.spawn_mob": "Spawner de desafío genera un mob", "subtitles.block.tripwire.attach": "<PERSON>lo trampa se conecta", "subtitles.block.tripwire.click": "Chasquido de hilo trampa", "subtitles.block.tripwire.detach": "<PERSON>lo trampa se desconecta", "subtitles.block.vault.activate": "Bóveda se activa", "subtitles.block.vault.ambient": "Bóveda chisporrotea", "subtitles.block.vault.close_shutter": "Bóveda se cierra", "subtitles.block.vault.deactivate": "Bóveda se desactiva", "subtitles.block.vault.eject_item": "Bóveda expulsa un ítem", "subtitles.block.vault.insert_item": "Bóveda se desbloquea", "subtitles.block.vault.insert_item_fail": "Bóveda rechaza un ítem", "subtitles.block.vault.open_shutter": "Bóveda se abre", "subtitles.block.vault.reject_rewarded_player": "Bóveda rechaza a un jugador", "subtitles.block.water.ambient": "Agua fluyendo", "subtitles.block.wet_sponge.dries": "Esponja se seca", "subtitles.chiseled_bookshelf.insert": "Libro colocado", "subtitles.chiseled_bookshelf.insert_enchanted": "Libro encantado colocado", "subtitles.chiseled_bookshelf.take": "Libro tomado", "subtitles.chiseled_bookshelf.take_enchanted": "Libro encantado tomado", "subtitles.enchant.thorns.hit": "Pinchazo con espinas", "subtitles.entity.allay.ambient_with_item": "Allay busca", "subtitles.entity.allay.ambient_without_item": "<PERSON>ay anhela", "subtitles.entity.allay.death": "<PERSON>ay muere", "subtitles.entity.allay.hurt": "Allay herido", "subtitles.entity.allay.item_given": "<PERSON>ay jugue<PERSON>a", "subtitles.entity.allay.item_taken": "Allay alivia", "subtitles.entity.allay.item_thrown": "Allay arroja", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> chilla", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON> cepilla<PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.armadillo.hurt_reduced": "Armadillo se protege", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> se asoma", "subtitles.entity.armadillo.roll": "Armadillo se hace bolita", "subtitles.entity.armadillo.scute_drop": "Armadillo suelta una escama", "subtitles.entity.armadillo.unroll_finish": "Armadillo se desenrolla", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> se asoma", "subtitles.entity.armor_stand.fall": "Caída de un objeto", "subtitles.entity.arrow.hit": "Impacto de flecha", "subtitles.entity.arrow.hit_player": "Impacto a un jugador", "subtitles.entity.arrow.shoot": "Disparo de flecha", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON><PERSON> chilla", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON> chilla", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON> sal<PERSON>a", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> nada", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Murciélago echa a volar", "subtitles.entity.bee.ambient": "Zumbido de abeja", "subtitles.entity.bee.death": "<PERSON><PERSON> muere", "subtitles.entity.bee.hurt": "<PERSON><PERSON>", "subtitles.entity.bee.loop": "Zumbido de abeja", "subtitles.entity.bee.loop_aggressive": "Zumbido de abeja enojada", "subtitles.entity.bee.pollinate": "Zumbido de abeja alegre", "subtitles.entity.bee.sting": "Abeja pica", "subtitles.entity.blaze.ambient": "Blaze respira", "subtitles.entity.blaze.burn": "Blaze chisporrotea", "subtitles.entity.blaze.death": "<PERSON> muere", "subtitles.entity.blaze.hurt": "<PERSON> herido", "subtitles.entity.blaze.shoot": "<PERSON> dispara", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON> con dificultad", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Ciénago traquet<PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.bogged.hurt": "Ciénago her<PERSON>", "subtitles.entity.breeze.charge": "Breeze carga", "subtitles.entity.breeze.death": "<PERSON><PERSON> muere", "subtitles.entity.breeze.deflect": "<PERSON><PERSON>", "subtitles.entity.breeze.hurt": "Breeze herido", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON> vuela", "subtitles.entity.breeze.idle_ground": "Breeze zumba", "subtitles.entity.breeze.inhale": "Breeze inhala", "subtitles.entity.breeze.jump": "Breeze salta", "subtitles.entity.breeze.land": "Breeze aterriza", "subtitles.entity.breeze.shoot": "<PERSON><PERSON> dispara", "subtitles.entity.breeze.slide": "<PERSON>ze se desliza", "subtitles.entity.breeze.whirl": "<PERSON><PERSON> ulula", "subtitles.entity.breeze.wind_burst": "Carga de viento estalla", "subtitles.entity.camel.ambient": "Camello ronca", "subtitles.entity.camel.dash": "Camello se lanza", "subtitles.entity.camel.dash_ready": "Camello se recupera", "subtitles.entity.camel.death": "Camello muere", "subtitles.entity.camel.eat": "Camello come", "subtitles.entity.camel.hurt": "Camello herido", "subtitles.entity.camel.saddle": "Montura se equipa", "subtitles.entity.camel.sit": "Camello se sienta", "subtitles.entity.camel.stand": "Camello se para", "subtitles.entity.camel.step": "Pasos de camello", "subtitles.entity.camel.step_sand": "Pasos de camello sobre arena", "subtitles.entity.cat.ambient": "<PERSON><PERSON> maulla", "subtitles.entity.cat.beg_for_food": "Gato pidiendo", "subtitles.entity.cat.death": "<PERSON><PERSON> muere", "subtitles.entity.cat.eat": "Gato come", "subtitles.entity.cat.hiss": "<PERSON><PERSON> bufa", "subtitles.entity.cat.hurt": "Gato herido", "subtitles.entity.cat.purr": "<PERSON><PERSON> r<PERSON>a", "subtitles.entity.chicken.ambient": "Cacareo de gallina", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.chicken.egg": "Gallina pone un huevo", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.cod.death": "Bacalao muere", "subtitles.entity.cod.flop": "Bacalao chapotea", "subtitles.entity.cod.hurt": "Bacalao herido", "subtitles.entity.cow.ambient": "Mugido de vaca", "subtitles.entity.cow.death": "Vaca muere", "subtitles.entity.cow.hurt": "Vaca herida", "subtitles.entity.cow.milk": "Vaca ordeñada", "subtitles.entity.creaking.activate": "Crujidor observa", "subtitles.entity.creaking.ambient": "Crujidor cruje", "subtitles.entity.creaking.attack": "Cru<PERSON>or ataca", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON> se <PERSON>a", "subtitles.entity.creaking.death": "Crujidor se desmorona", "subtitles.entity.creaking.freeze": "Crujidor quieto", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON><PERSON> emerge", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON> go<PERSON>", "subtitles.entity.creaking.twitch": "Crujidor se retuerce", "subtitles.entity.creaking.unfreeze": "Crujidor se mueve", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> de creeper", "subtitles.entity.dolphin.ambient": "Delfín chirría", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> silba", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.dolphin.eat": "Delfín comiendo", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> jugando", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> sa<PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.ambient": "Rebuzno de burro", "subtitles.entity.donkey.angry": "<PERSON><PERSON> relincha", "subtitles.entity.donkey.chest": "Baúl equipado a un burro", "subtitles.entity.donkey.death": "<PERSON><PERSON> muere", "subtitles.entity.donkey.eat": "<PERSON><PERSON> come", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> salta", "subtitles.entity.drowned.ambient": "Drowned gargarea", "subtitles.entity.drowned.ambient_water": "Drowned gargarea", "subtitles.entity.drowned.death": "Drowned muere", "subtitles.entity.drowned.hurt": "Drowned herido", "subtitles.entity.drowned.shoot": "Drowned lanza un tridente", "subtitles.entity.drowned.step": "<PERSON><PERSON>", "subtitles.entity.drowned.swim": "Drowned nada", "subtitles.entity.egg.throw": "<PERSON><PERSON> lanza<PERSON>", "subtitles.entity.elder_guardian.ambient": "Guardián mayor se queja", "subtitles.entity.elder_guardian.ambient_land": "Guardián mayor <PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.curse": "Maldición de guardián mayor", "subtitles.entity.elder_guardian.death": "Guardián mayor muere", "subtitles.entity.elder_guardian.flop": "Guardián mayor chap<PERSON><PERSON>", "subtitles.entity.elder_guardian.hurt": "Guardián mayor herido", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON> ruge", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ender_dragon.flap": "Dr<PERSON>ón aletea", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ender_eye.death": "<PERSON>jo de ender cayendo", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> de ender lanzado", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> de ender la<PERSON>", "subtitles.entity.enderman.ambient": "Enderman vwupea", "subtitles.entity.enderman.death": "<PERSON><PERSON> muer<PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.enderman.scream": "Enderman grita", "subtitles.entity.enderman.stare": "Enderman gritando", "subtitles.entity.enderman.teleport": "Enderman se teletransporta", "subtitles.entity.endermite.ambient": "Endermite se arrastra", "subtitles.entity.endermite.death": "Endermite muere", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.evoker.ambient": "Invocador murmura", "subtitles.entity.evoker.cast_spell": "Invocador lanza un hechizo", "subtitles.entity.evoker.celebrate": "Invocador festejando", "subtitles.entity.evoker.death": "Invocador muere", "subtitles.entity.evoker.hurt": "Invocador herido", "subtitles.entity.evoker.prepare_attack": "Invocador prepara un ataque", "subtitles.entity.evoker.prepare_summon": "Invocador prepara una invocación", "subtitles.entity.evoker.prepare_wololo": "Invocador prepara un hechizo", "subtitles.entity.evoker_fangs.attack": "Rotura de colmillos", "subtitles.entity.experience_orb.pickup": "Experiencia obtenida", "subtitles.entity.firework_rocket.blast": "Explosión de fuegos artificiales", "subtitles.entity.firework_rocket.launch": "Lanzamiento de fuegos artificiales", "subtitles.entity.firework_rocket.twinkle": "Destellos de fuegos artificiales", "subtitles.entity.fish.swim": "Chapoteos", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON> recogido", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON> sal<PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON> chilla", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> muer<PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> chirr<PERSON>", "subtitles.entity.fox.sleep": "Zorro ronca", "subtitles.entity.fox.sniff": "Zorro olfatea", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> escupe", "subtitles.entity.fox.teleport": "Zorro se teletransporta", "subtitles.entity.frog.ambient": "<PERSON> croa", "subtitles.entity.frog.death": "<PERSON> muere", "subtitles.entity.frog.eat": "<PERSON> come", "subtitles.entity.frog.hurt": "Rana <PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON>", "subtitles.entity.frog.long_jump": "<PERSON>a", "subtitles.entity.generic.big_fall": "Algo cae", "subtitles.entity.generic.burn": "Se quema", "subtitles.entity.generic.death": "Agonía", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Comiendo", "subtitles.entity.generic.explode": "Explosión", "subtitles.entity.generic.extinguish_fire": "Fuego extinguido", "subtitles.entity.generic.hurt": "Algo te hiere", "subtitles.entity.generic.small_fall": "Leve impacto contra el suelo", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "Na<PERSON>do", "subtitles.entity.generic.wind_burst": "Carga de viento estalla", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> se arrulla", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ghastling.hurt": "Ghastling herido", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> a<PERSON>e", "subtitles.entity.glow_item_frame.add_item": "Objeto colocado en un marco brillante", "subtitles.entity.glow_item_frame.break": "<PERSON> brillante quitado", "subtitles.entity.glow_item_frame.place": "<PERSON> brillante colocado", "subtitles.entity.glow_item_frame.remove_item": "Objeto retirado de un marco brillante", "subtitles.entity.glow_item_frame.rotate_item": "Chasquido de marco de ítems brillante", "subtitles.entity.glow_squid.ambient": "Calamar brillante nada", "subtitles.entity.glow_squid.death": "Calamar brillante muere", "subtitles.entity.glow_squid.hurt": "Calamar brillante herido", "subtitles.entity.glow_squid.squirt": "Calamar brillante dispara tinta", "subtitles.entity.goat.ambient": "Balido de cabra", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.goat.horn_break": "Cacho de cabra se desprende", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.goat.long_jump": "Cabra pega un salto", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON> pisotea", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON> embis<PERSON>", "subtitles.entity.goat.screaming.ambient": "Bramido <PERSON>", "subtitles.entity.goat.step": "Pasos de cabra", "subtitles.entity.guardian.ambient": "G<PERSON><PERSON> de guardián", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "Guardián dispara", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> chap<PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON> feliz can<PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> feliz muere", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> feliz listo", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> feliz para", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> feliz herido", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> gruñe furiosamente", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> se convierte en zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n her<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> se retira", "subtitles.entity.hoglin.step": "Pasos de hoglin", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> relincha", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> relincha", "subtitles.entity.horse.armor": "Armadura de caballo equipada", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> resopla", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.horse.gallop": "Caballo galopa", "subtitles.entity.horse.hurt": "Caballo herido", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.horse.saddle": "<PERSON>ura equipada", "subtitles.entity.husk.ambient": "G<PERSON><PERSON> de husk", "subtitles.entity.husk.converted_to_zombie": "Zombi se desmomifica", "subtitles.entity.husk.death": "<PERSON><PERSON> muere", "subtitles.entity.husk.hurt": "Husk herido", "subtitles.entity.illusioner.ambient": "Ilusionador murmura", "subtitles.entity.illusioner.cast_spell": "Ilusionador lanza un hechizo", "subtitles.entity.illusioner.death": "Ilusionador muere", "subtitles.entity.illusioner.hurt": "Ilusionador herido", "subtitles.entity.illusioner.mirror_move": "Ilusionador se desplaza", "subtitles.entity.illusioner.prepare_blindness": "Ilusionador se prepara para cegar", "subtitles.entity.illusioner.prepare_mirror": "Ilusionador prepara el reflejo viviente", "subtitles.entity.iron_golem.attack": "Gólem de hierro ataca", "subtitles.entity.iron_golem.damage": "Gólem de hierro se rompe", "subtitles.entity.iron_golem.death": "Gólem de hierro muere", "subtitles.entity.iron_golem.hurt": "Gólem de hierro herido", "subtitles.entity.iron_golem.repair": "Gólem de hierro es arreglado", "subtitles.entity.item.break": "Objet<PERSON> destruido", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "Objeto colocado en un marco", "subtitles.entity.item_frame.break": "<PERSON> quitado", "subtitles.entity.item_frame.place": "<PERSON> colocado", "subtitles.entity.item_frame.remove_item": "Objeto retirado de un marco", "subtitles.entity.item_frame.rotate_item": "Chasquido de marco de <PERSON>", "subtitles.entity.leash_knot.break": "Nudo de rienda se rompe", "subtitles.entity.leash_knot.place": "Rienda atada", "subtitles.entity.lightning_bolt.impact": "Rayos", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON><PERSON>", "subtitles.entity.llama.angry": "Balido de llama furiosa", "subtitles.entity.llama.chest": "Baúl equipado a una llama", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.llama.spit": "Llama escupe", "subtitles.entity.llama.step": "Pasos de llama", "subtitles.entity.llama.swag": "Llama es adornada", "subtitles.entity.magma_cube.death": "Cubo de magma muere", "subtitles.entity.magma_cube.hurt": "Cubo de magma herido", "subtitles.entity.magma_cube.squish": "Cubo de magma apretándose", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON>", "subtitles.entity.minecart.inside_underwater": "Chi<PERSON>do de vagón bajo el agua", "subtitles.entity.minecart.riding": "Vagón en movimiento", "subtitles.entity.mooshroom.convert": "Mooshroom se transforma", "subtitles.entity.mooshroom.eat": "Mooshroom come", "subtitles.entity.mooshroom.milk": "Mooshroom ordeñada", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom ordeñada de forma sospechosa", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON> de <PERSON>ula", "subtitles.entity.mule.angry": "<PERSON><PERSON> relincha", "subtitles.entity.mule.chest": "<PERSON>úl equipado a una mula", "subtitles.entity.mule.death": "<PERSON><PERSON> muere", "subtitles.entity.mule.eat": "<PERSON><PERSON> come", "subtitles.entity.mule.hurt": "<PERSON><PERSON> herida", "subtitles.entity.mule.jump": "<PERSON><PERSON> salta", "subtitles.entity.painting.break": "Cuadro quitado", "subtitles.entity.painting.place": "Cuadro puesto", "subtitles.entity.panda.aggressive_ambient": "Panda jadea", "subtitles.entity.panda.ambient": "Panda resuella", "subtitles.entity.panda.bite": "Mordiscos de panda", "subtitles.entity.panda.cant_breed": "Balido de panda", "subtitles.entity.panda.death": "<PERSON>da muere", "subtitles.entity.panda.eat": "Panda come", "subtitles.entity.panda.hurt": "Panda herido", "subtitles.entity.panda.pre_sneeze": "Panda se rasca la nariz", "subtitles.entity.panda.sneeze": "Panda estorn<PERSON>", "subtitles.entity.panda.step": "Pasos de panda", "subtitles.entity.panda.worried_ambient": "Gemido <PERSON>da", "subtitles.entity.parrot.ambient": "<PERSON><PERSON> habla", "subtitles.entity.parrot.death": "<PERSON><PERSON> muere", "subtitles.entity.parrot.eats": "<PERSON>ro come", "subtitles.entity.parrot.fly": "<PERSON><PERSON> revolotea", "subtitles.entity.parrot.hurts": "<PERSON>ro herido", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON> respira", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON> tra<PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON> ulula", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON> c<PERSON>", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON> si<PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON> gar<PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "Loro gime", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON> ruge", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON> se arrastra", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON> murmura", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "Loro gime", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON> murmura", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON> chilla", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON> bufa", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON> murmura", "subtitles.entity.parrot.imitate.ravager": "Loro hace un guarrido", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON> si<PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON> tra<PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON> aplasta", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON> si<PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON> tra<PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON> molesta", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> imita", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON> r<PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON> se enoja", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON> tra<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON>", "subtitles.entity.phantom.ambient": "Phantom chilla", "subtitles.entity.phantom.bite": "Phantom muerde", "subtitles.entity.phantom.death": "Phantom muere", "subtitles.entity.phantom.flap": "Phantom aletea", "subtitles.entity.phantom.hurt": "Phantom herido", "subtitles.entity.phantom.swoop": "Phantom se acerca", "subtitles.entity.pig.ambient": "<PERSON><PERSON> hace un guarrido", "subtitles.entity.pig.death": "<PERSON><PERSON> muere", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "<PERSON>ura equipada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> admira un ítem", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON> gruñe furiosamente", "subtitles.entity.piglin.celebrate": "<PERSON>lin celebra", "subtitles.entity.piglin.converted_to_zombified": "<PERSON>lin se convierte en piglin zombificado", "subtitles.entity.piglin.death": "<PERSON><PERSON> muere", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> gruñe envidiosamente", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> se retira", "subtitles.entity.piglin.step": "Pasos de piglin", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> bruto bufa", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> bruto bufa furiosamente", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> bruto se zombifica", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> bruto muere", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> bruto herido", "subtitles.entity.piglin_brute.step": "<PERSON>s de piglin bruto", "subtitles.entity.pillager.ambient": "Saqueador murmura", "subtitles.entity.pillager.celebrate": "Saqueador celebra", "subtitles.entity.pillager.death": "<PERSON>que<PERSON> muere", "subtitles.entity.pillager.hurt": "Saqueador herido", "subtitles.entity.player.attack.crit": "Ataque crítico", "subtitles.entity.player.attack.knockback": "Ataque con empuje", "subtitles.entity.player.attack.strong": "Ataque fuerte", "subtitles.entity.player.attack.sweep": "Ataque con barrido", "subtitles.entity.player.attack.weak": "Ataque débil", "subtitles.entity.player.burp": "Eructo", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.player.freeze_hurt": "Jugador se congela", "subtitles.entity.player.hurt": "<PERSON><PERSON>r herido", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON>r <PERSON>", "subtitles.entity.player.levelup": "Jugador sube de nivel", "subtitles.entity.player.teleport": "Jugador se teletransporta", "subtitles.entity.polar_bear.ambient": "G<PERSON><PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON> de oso polar tararea", "subtitles.entity.polar_bear.death": "<PERSON>so polar muere", "subtitles.entity.polar_bear.hurt": "Oso polar herido", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON> polar ruge", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON> se rompe", "subtitles.entity.potion.throw": "Botella es lanzada", "subtitles.entity.puffer_fish.blow_out": "Pez globo de desinfla", "subtitles.entity.puffer_fish.blow_up": "Pez globo se infla", "subtitles.entity.puffer_fish.death": "Pez globo muere", "subtitles.entity.puffer_fish.flop": "Pez globo chapotea", "subtitles.entity.puffer_fish.hurt": "Pez globo herido", "subtitles.entity.puffer_fish.sting": "Picadura de pez globo", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON> chilla", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>o ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON>o her<PERSON>", "subtitles.entity.rabbit.jump": "Conejo brinca", "subtitles.entity.ravager.ambient": "Gruñidos de devastador", "subtitles.entity.ravager.attack": "Devast<PERSON> muerde", "subtitles.entity.ravager.celebrate": "Devastador festeja", "subtitles.entity.ravager.death": "Devast<PERSON> muere", "subtitles.entity.ravager.hurt": "Devastador herido", "subtitles.entity.ravager.roar": "Rug<PERSON>s <PERSON>or", "subtitles.entity.ravager.step": "Pasos de devastador", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.salmon.flop": "Salmón chapotea", "subtitles.entity.salmon.hurt": "Salmón herido", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON> hace <PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.sheep.hurt": "Oveja herida", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> se cierra", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.shulker.open": "<PERSON>lk<PERSON> se abre", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> se teletransporta", "subtitles.entity.shulker_bullet.hit": "Proyectil de shulker explota", "subtitles.entity.shulker_bullet.hurt": "Proyectil de shulker se rompe", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON> sisea", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.skeleton.ambient": "Esqueleto traquetea", "subtitles.entity.skeleton.converted_to_stray": "Esqueleto se congela", "subtitles.entity.skeleton.death": "Esqueleto muere", "subtitles.entity.skeleton.hurt": "Esqueleto herido", "subtitles.entity.skeleton.shoot": "Esqueleto dispara", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> de caballo esqueleto", "subtitles.entity.skeleton_horse.death": "C<PERSON>llo esqueleto muere", "subtitles.entity.skeleton_horse.hurt": "Caballo esqueleto herido", "subtitles.entity.skeleton_horse.jump_water": "Caballo esqueleto saltando", "subtitles.entity.skeleton_horse.swim": "Caballo esqueleto nada", "subtitles.entity.slime.attack": "Slime ataca", "subtitles.entity.slime.death": "Slime muere", "subtitles.entity.slime.hurt": "Slime herido", "subtitles.entity.slime.squish": "Slime chapotea", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.sniffer.digging": "Sniffer excava", "subtitles.entity.sniffer.digging_stop": "<PERSON>ni<PERSON> se levanta", "subtitles.entity.sniffer.drop_seed": "<PERSON>niffer suelta una semilla", "subtitles.entity.sniffer.eat": "Sniffer come", "subtitles.entity.sniffer.egg_crack": "Huevo de sniffer cruje", "subtitles.entity.sniffer.egg_hatch": "Huevo de sniffer eclosiona", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> se deleita", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> olorosa", "subtitles.entity.sniffer.searching": "Sniffer busca", "subtitles.entity.sniffer.sniffing": "Sniffer olfatea", "subtitles.entity.sniffer.step": "Pasos de sniffer", "subtitles.entity.snow_golem.death": "Gólem de nieve muere", "subtitles.entity.snow_golem.hurt": "Gólem de nieve herido", "subtitles.entity.snowball.throw": "Bola de nieve lanzada", "subtitles.entity.spider.ambient": "Siseo de araña", "subtitles.entity.spider.death": "<PERSON><PERSON> muere", "subtitles.entity.spider.hurt": "<PERSON><PERSON> herida", "subtitles.entity.squid.ambient": "Calamar <PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.squid.squirt": "Calamar dispara tinta", "subtitles.entity.stray.ambient": "<PERSON>ray traquetea", "subtitles.entity.stray.death": "<PERSON><PERSON> muere", "subtitles.entity.stray.hurt": "Stray herido", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.strider.eat": "Strider come", "subtitles.entity.strider.happy": "Strider celebra", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.strider.idle": "Strider chilla", "subtitles.entity.strider.retreat": "Strider se retira", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.tadpole.flop": "Renac<PERSON>jo chapotea", "subtitles.entity.tadpole.grow_up": "Renacuajo crece", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.tnt.primed": "TNT activada", "subtitles.entity.tropical_fish.death": "Pez tropical muere", "subtitles.entity.tropical_fish.flop": "Pez tropical chapotea", "subtitles.entity.tropical_fish.hurt": "Pez tropical herido", "subtitles.entity.turtle.ambient_land": "Tortuga chirría", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.turtle.death_baby": "Cría de tortuga muere", "subtitles.entity.turtle.egg_break": "Huevo de tortuga se rompe", "subtitles.entity.turtle.egg_crack": "Huevo de tortuga se resquebraja", "subtitles.entity.turtle.egg_hatch": "Huevo de tortuga eclosiona", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.turtle.hurt_baby": "Cría de tortuga herida", "subtitles.entity.turtle.lay_egg": "Tortuga pone un huevo", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON> repta", "subtitles.entity.turtle.shamble_baby": "<PERSON>ría de tortuga repta", "subtitles.entity.turtle.swim": "Tortuga nada", "subtitles.entity.vex.ambient": "Vex molesta", "subtitles.entity.vex.charge": "Vex chilla", "subtitles.entity.vex.death": "Vex muere", "subtitles.entity.vex.hurt": "Vex herido", "subtitles.entity.villager.ambient": "Aldeano murmura", "subtitles.entity.villager.celebrate": "Aldeano celebra", "subtitles.entity.villager.death": "Aldeano muere", "subtitles.entity.villager.hurt": "Aldeano herido", "subtitles.entity.villager.no": "Aldeano rechaza", "subtitles.entity.villager.trade": "Aldeano intercambia", "subtitles.entity.villager.work_armorer": "Escudero trabaja", "subtitles.entity.villager.work_butcher": "Carnicero trabaja", "subtitles.entity.villager.work_cartographer": "Cartógrafo trabaja", "subtitles.entity.villager.work_cleric": "Sacerdote trabaja", "subtitles.entity.villager.work_farmer": "Granjero trabaja", "subtitles.entity.villager.work_fisherman": "Pescador trabaja", "subtitles.entity.villager.work_fletcher": "Flechero trabaja", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> trabaja", "subtitles.entity.villager.work_librarian": "Bibliotecario trabaja", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON> t<PERSON>", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON> t<PERSON>", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON> t<PERSON>aja", "subtitles.entity.villager.yes": "Aldeano acepta", "subtitles.entity.vindicator.ambient": "Vindicador murmura", "subtitles.entity.vindicator.celebrate": "Vindicador celebra", "subtitles.entity.vindicator.death": "Vindicador muere", "subtitles.entity.vindicator.hurt": "Vindicador herido", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> ambulante balbucea", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON> ambulante muere", "subtitles.entity.wandering_trader.disappeared": "Vendedor ambulante desaparece", "subtitles.entity.wandering_trader.drink_milk": "Vendedor ambulante toma leche", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON> ambulante se toma una poción", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON> ambulante herido", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON>or ambulante rechaza", "subtitles.entity.wandering_trader.reappeared": "Vendedor ambulante aparece", "subtitles.entity.wandering_trader.trade": "Vendedor ambulante intercambia", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON>or ambulante acepta", "subtitles.entity.warden.agitated": "<PERSON> gru<PERSON>e furioso", "subtitles.entity.warden.ambient": "Warden re<PERSON><PERSON>", "subtitles.entity.warden.angry": "Warden se enfurece", "subtitles.entity.warden.attack_impact": "Warden da un golpe", "subtitles.entity.warden.death": "<PERSON> muere", "subtitles.entity.warden.dig": "Warden excava", "subtitles.entity.warden.emerge": "Warden emerge", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> late", "subtitles.entity.warden.hurt": "Warden herido", "subtitles.entity.warden.listening": "<PERSON> detecta", "subtitles.entity.warden.listening_angry": "Warden <PERSON><PERSON>", "subtitles.entity.warden.nearby_close": "Warden se aproxima", "subtitles.entity.warden.nearby_closer": "Warden a<PERSON>", "subtitles.entity.warden.nearby_closest": "Warden viene cerca", "subtitles.entity.warden.roar": "<PERSON> ruge", "subtitles.entity.warden.sniff": "Warden olfatea", "subtitles.entity.warden.sonic_boom": "Warden hace una descarga", "subtitles.entity.warden.sonic_charge": "Warden carga", "subtitles.entity.warden.step": "Pasos de warden", "subtitles.entity.warden.tendril_clicks": "Antenas de Warden chasquean", "subtitles.entity.wind_charge.throw": "Carga de viento lanzada", "subtitles.entity.wind_charge.wind_burst": "Carga de viento estalla", "subtitles.entity.witch.ambient": "Bruja ríe", "subtitles.entity.witch.celebrate": "Bruja celebra", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> bebe", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.witch.throw": "Bruja lanza", "subtitles.entity.wither.ambient": "<PERSON><PERSON> se enoja", "subtitles.entity.wither.death": "<PERSON><PERSON> muere", "subtitles.entity.wither.hurt": "Wither herido", "subtitles.entity.wither.shoot": "Wither ataca", "subtitles.entity.wither.spawn": "<PERSON><PERSON> liberado", "subtitles.entity.wither_skeleton.ambient": "Esqueleto Wither tra<PERSON>", "subtitles.entity.wither_skeleton.death": "Esqueleto Wither muere", "subtitles.entity.wither_skeleton.hurt": "Esqueleto Wither herido", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "Lobo ladra", "subtitles.entity.wolf.death": "Lobo muere", "subtitles.entity.wolf.growl": "Gruñ<PERSON> de <PERSON>", "subtitles.entity.wolf.hurt": "Lobo herido", "subtitles.entity.wolf.pant": "Lobo jadea", "subtitles.entity.wolf.shake": "Lobo se sacude", "subtitles.entity.wolf.whine": "Lobo gime", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> gruñe furiosamente", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.zoglin.step": "Pasos de z<PERSON>lin", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> go<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> se rompe", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON> se ahoga", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.zombie.destroy_egg": "Huevo de tortuga es pisoteado", "subtitles.entity.zombie.hurt": "<PERSON><PERSON>i herido", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON> infecta", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> de caballo zombi", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON>llo zombi muere", "subtitles.entity.zombie_horse.hurt": "Caballo zombi herido", "subtitles.entity.zombie_villager.ambient": "G<PERSON><PERSON> de aldeano zombi", "subtitles.entity.zombie_villager.converted": "Zombi aldeano se alborota", "subtitles.entity.zombie_villager.cure": "Zombi aldeano suspira", "subtitles.entity.zombie_villager.death": "Aldeano zombi muere", "subtitles.entity.zombie_villager.hurt": "Aldeano zombi herido", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> zombificado gruñe", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON> zombificado gruñe con rabia", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombificado muere", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> zombificado herido", "subtitles.event.mob_effect.bad_omen": "Presagio se manifiesta", "subtitles.event.mob_effect.raid_omen": "Se avecina una invasión", "subtitles.event.mob_effect.trial_omen": "Se avecina un desafío ominoso", "subtitles.event.raid.horn": "<PERSON><PERSON>", "subtitles.item.armor.equip": "Crujid<PERSON>", "subtitles.item.armor.equip_chain": "Armadura de cota de mallas cruje", "subtitles.item.armor.equip_diamond": "Armadura de diamante tintinea", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> de elytra", "subtitles.item.armor.equip_gold": "Armadura de oro rechina", "subtitles.item.armor.equip_iron": "Armadura de hierro rechina", "subtitles.item.armor.equip_leather": "Armadura de cuero roza", "subtitles.item.armor.equip_netherite": "Armadura de netherita chin<PERSON>ea", "subtitles.item.armor.equip_turtle": "Caparazón de tortuga resuena", "subtitles.item.armor.equip_wolf": "Armadura para lobos equipada", "subtitles.item.armor.unequip_wolf": "Armadura para lobos soltada", "subtitles.item.axe.scrape": "<PERSON><PERSON> raspa", "subtitles.item.axe.strip": "<PERSON><PERSON> raspa", "subtitles.item.axe.wax_off": "<PERSON><PERSON> pulida", "subtitles.item.bone_meal.use": "Polvo de hueso abona", "subtitles.item.book.page_turn": "Cambio de página", "subtitles.item.book.put": "Libro colocado", "subtitles.item.bottle.empty": "Botella es vaciada", "subtitles.item.bottle.fill": "Botella es llenada", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Cepillado de grava", "subtitles.item.brush.brushing.gravel.complete": "Grava cepillada", "subtitles.item.brush.brushing.sand": "Cepillado de arena", "subtitles.item.brush.brushing.sand.complete": "Arena cepillada", "subtitles.item.bucket.empty": "Balde vaciado", "subtitles.item.bucket.fill": "<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> recogido", "subtitles.item.bucket.fill_fish": "Pez es capturado", "subtitles.item.bucket.fill_tadpole": "Renacuajo es capturado", "subtitles.item.bundle.drop_contents": "Saco es vaciado", "subtitles.item.bundle.insert": "Ítem empacado", "subtitles.item.bundle.insert_fail": "Saco lleno", "subtitles.item.bundle.remove_one": "Ítem desempacado", "subtitles.item.chorus_fruit.teleport": "Jugador se teletransporta", "subtitles.item.crop.plant": "<PERSON>lla cultivada", "subtitles.item.crossbow.charge": "Ballesta se carga", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON>a", "subtitles.item.crossbow.load": "Ballesta cargada", "subtitles.item.crossbow.shoot": "Disparo de ballesta", "subtitles.item.dye.use": "<PERSON><PERSON> ti<PERSON>e", "subtitles.item.elytra.flying": "Silbido de viento", "subtitles.item.firecharge.use": "Zumbido de bola de fuego", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON><PERSON> de en<PERSON>ed<PERSON>", "subtitles.item.glow_ink_sac.use": "Saco de tinta brillante mancha", "subtitles.item.goat_horn.play": "Cacho de cabra suena", "subtitles.item.hoe.till": "Azadón arando", "subtitles.item.honey_bottle.drink": "Tragando", "subtitles.item.honeycomb.wax_on": "Cera aplicada", "subtitles.item.horse_armor.unequip": "La armadura del caballo se rompe", "subtitles.item.ink_sac.use": "Saco de tinta mancha", "subtitles.item.lead.break": "<PERSON>uerda se rompe", "subtitles.item.lead.tied": "Cuerda atada", "subtitles.item.lead.untied": "Cuerda desatada", "subtitles.item.llama_carpet.unequip": "La alfombra se rompe", "subtitles.item.lodestone_compass.lock": "Brújula imantada a magnetita", "subtitles.item.mace.smash_air": "<PERSON><PERSON> aplasta", "subtitles.item.mace.smash_ground": "<PERSON><PERSON> aplasta", "subtitles.item.nether_wart.plant": "Verrugas cultivadas", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> se rompe", "subtitles.item.saddle.unequip": "La montura se rompe", "subtitles.item.shears.shear": "Chasquido de tijeras", "subtitles.item.shears.snip": "Tijeras cortan", "subtitles.item.shield.block": "Bloqueo con escudo", "subtitles.item.shovel.flatten": "Pala aplanando", "subtitles.item.spyglass.stop_using": "Catalejo se retrae", "subtitles.item.spyglass.use": "Catalejo se <PERSON>e", "subtitles.item.totem.use": "Tótem activado", "subtitles.item.trident.hit": "Apuñalada de tridente", "subtitles.item.trident.hit_ground": "Tridente vibra", "subtitles.item.trident.return": "Tridente vuelve", "subtitles.item.trident.riptide": "Tridente zumba", "subtitles.item.trident.throw": "Ruidos de tridente", "subtitles.item.trident.thunder": "Truenos de tridente", "subtitles.item.wolf_armor.break": "Armadura para lobos se rompe", "subtitles.item.wolf_armor.crack": "Armadura para lobos se agrieta", "subtitles.item.wolf_armor.damage": "Armadura para lobos es dañada", "subtitles.item.wolf_armor.repair": "Armadura para lobos reparada", "subtitles.particle.soul_escape": "Alma se escapa", "subtitles.ui.cartography_table.take_result": "Mapa dibujado", "subtitles.ui.hud.bubble_pop": "Oxígeno se agota", "subtitles.ui.loom.take_result": "Telar usado", "subtitles.ui.stonecutter.take_result": "Cortapiedras usado", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Cargar mundos desde carpetas con enlaces simbólicos puede no ser seguro si no sabes con certeza lo que estás haciendo. Por favor, visita %s para más información.", "symlink_warning.message.pack": "<PERSON><PERSON> paquetes con enlaces simbólicos puede no ser seguro si no sabes con certeza lo que estás haciendo. Por favor, visita %s para más información.", "symlink_warning.message.world": "Cargar mundos desde carpetas con enlaces simbólicos puede no ser seguro si no sabes con certeza lo que estás haciendo. Por favor, visita %s para más información.", "symlink_warning.more_info": "Más información", "symlink_warning.title": "La carpeta del mundo contiene enlaces simbólicos", "symlink_warning.title.pack": "El o los paquetes añadidos contienen enlaces simbólicos", "symlink_warning.title.world": "La carpeta del mundo contiene enlaces simbólicos", "team.collision.always": "Siempre", "team.collision.never": "Nunca", "team.collision.pushOtherTeams": "Colisión entre jugadores de distinto equipo", "team.collision.pushOwnTeam": "Colisión entre jugadores del mismo equipo", "team.notFound": "Equipo desconocido: %s", "team.visibility.always": "Siempre", "team.visibility.hideForOtherTeams": "Ocultar para otros equipos", "team.visibility.hideForOwnTeam": "Ocultar para el propio equipo", "team.visibility.never": "Nunca", "telemetry.event.advancement_made.description": "Entender el contexto detrás de recibir un logro nos puede ayudar a entender mejor y mejorar el progreso del juego.", "telemetry.event.advancement_made.title": "Progreso realizado", "telemetry.event.game_load_times.description": "Este evento nos ayuda a determinar dónde se necesitan mejoras de rendimiento, midiendo los tiempos de ejecución de las diferentes fases del inicio del juego.", "telemetry.event.game_load_times.title": "Tiempos de carga del juego", "telemetry.event.optional": "%s (opcional)", "telemetry.event.optional.disabled": "%s (opcional) - Deshabilitado", "telemetry.event.performance_metrics.description": "Saber el perfil de rendimiento general de Minecraft nos ayuda a optimizar el juego para una amplia gama de especificaciones técnicas de equipos y sistemas operativos. \nSe incluye la versión del juego para ayudarnos a comparar el perfil de rendimiento en las nuevas versiones de Minecraft.", "telemetry.event.performance_metrics.title": "Métricas de rendimiento", "telemetry.event.required": "%s (necesario)", "telemetry.event.world_load_times.description": "Es importante para nosotros saber cuánto tiempo se tarda en entrar a un mundo y cómo esto varía con el tiempo. Por ejemplo, cuando agregamos nuevas características o cuando hacemos grandes cambios técnicos, necesitamos saber que impacto tuvieron en los tiempos de carga.", "telemetry.event.world_load_times.title": "Tiempos de carga de mundos", "telemetry.event.world_loaded.description": "Saber cómo los jugadores juegan Minecraft (por ejemplo, el modo de juego, modificaciones del cliente o servidor y versión del juego) nos ayuda a mejorar los aspectos que más le importan a los jugadores.\nEl evento \"Mundo cargado\" va de la mano con el de \"Mundo cerrado\" para calcular el tiempo de juego.", "telemetry.event.world_loaded.title": "<PERSON><PERSON> cargado", "telemetry.event.world_unloaded.description": "Este evento va de la mano con el de \"Mundo cargado\", con el fin de calcular cuánto duró la sesión de juego. La duración (en segundos y ticks) se mide cuando se termina la sesión del mundo (saliendo al menú principal o desconectándose de un server).", "telemetry.event.world_unloaded.title": "Mundo cerrado", "telemetry.property.advancement_game_time.title": "Tiempo de juego (ticks)", "telemetry.property.advancement_id.title": "ID de progreso", "telemetry.property.client_id.title": "ID del cliente", "telemetry.property.client_modded.title": "Cliente modificado", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicada (kB)", "telemetry.property.event_timestamp_utc.title": "Fecha y hora del evento (UTC)", "telemetry.property.frame_rate_samples.title": "Muestras de fotogramas por segundo (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "Versión del juego", "telemetry.property.launcher_name.title": "Nombre del launcher", "telemetry.property.load_time_bootstrap_ms.title": "Tiempo de inicialización (milisegundos)", "telemetry.property.load_time_loading_overlay_ms.title": "Tiempo en pantalla de carga (milisegundos)", "telemetry.property.load_time_pre_window_ms.title": "Tiempo antes de inicio de ventana (milisegundos)", "telemetry.property.load_time_total_time_ms.title": "Tiempo total de carga (milisegundos)", "telemetry.property.minecraft_session_id.title": "ID de la sesión de Minecraft", "telemetry.property.new_world.title": "Mundo nuevo", "telemetry.property.number_of_samples.title": "Cantidad de muestras", "telemetry.property.operating_system.title": "Sistema operativo", "telemetry.property.opt_in.title": "Inscripción", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Contenido del mapa de Realms (nombre del minijuego)", "telemetry.property.render_distance.title": "Distancia de renderizado", "telemetry.property.render_time_samples.title": "Tasa de renderizado de muestra", "telemetry.property.seconds_since_load.title": "Tiempo desde la carga (segundos)", "telemetry.property.server_modded.title": "Server modificado", "telemetry.property.server_type.title": "Tipo de server", "telemetry.property.ticks_since_load.title": "Tiempo desde la carga (ticks)", "telemetry.property.used_memory_samples.title": "Uso de RAM", "telemetry.property.user_id.title": "ID de usuario", "telemetry.property.world_load_time_ms.title": "Tiempo de carga del mundo (milisegundos)", "telemetry.property.world_session_id.title": "ID de sesión del mundo", "telemetry_info.button.give_feedback": "<PERSON><PERSON>r sugerencias", "telemetry_info.button.privacy_statement": "Declaración de privacidad", "telemetry_info.button.show_data": "Ver mis datos", "telemetry_info.opt_in.description": "Doy mi consentimiento para el envío de información de telemetría opcional", "telemetry_info.property_title": "<PERSON>tos incluidos", "telemetry_info.screen.description": "Recopilar estos datos nos ayuda a mejorar Minecraft guiándonos en direcciones que son relevantes para nuestros jugadores. Puedes enviar también información adicional para ayudarnos a seguir mejorando Minecraft.", "telemetry_info.screen.title": "Recopilación de datos de telemetría", "test.error.block_property_mismatch": "Se esperaba que la propiedad %s sea %s, y se obtuvo %s", "test.error.block_property_missing": "Propiedad del bloque restante, se esperaba que la propiedad %s sea %s", "test.error.entity_property": "Entidad %s falló la prueba: %s", "test.error.entity_property_details": "Entidad %s falló la prueba: %s, se esperaba %s, y se obtuvo %s", "test.error.expected_block": "Se esperaba el bloque %s, se obtuvo %s", "test.error.expected_block_tag": "Se esperaba el bloque en #%s, y se obtuvo %s", "test.error.expected_container_contents": "Contenedor debe contener: %s", "test.error.expected_container_contents_single": "Contenedor debe contener un único: %s", "test.error.expected_empty_container": "El contenedor debe estar vacío", "test.error.expected_entity": "Se esperaba %s", "test.error.expected_entity_around": "Se esperó que %s exista alrededor de %s, %s, %s", "test.error.expected_entity_count": "Se esperó %s entidades de tipo %s, y se obtuvo %s", "test.error.expected_entity_data": "Se esperó que los datos de la entidad sean: %s, y se obtuvo %s", "test.error.expected_entity_data_predicate": "Los datos de la entidad no coinciden para %s", "test.error.expected_entity_effect": "Se esperó que %s tenga el efecto %s %s", "test.error.expected_entity_having": "El inventario de la entidad debe contener %s", "test.error.expected_entity_holding": "La entidad debe estar sosteniendo %s", "test.error.expected_entity_in_test": "Se requiere que %s exista en la prueba", "test.error.expected_entity_not_touching": "No se esperaba que %s tocase %s, %s, %s (relativo: %s, %s, %s)", "test.error.expected_entity_touching": "Se requiere que %s toque %s, %s, %s (relativo: %s, %s, %s)", "test.error.expected_item": "Se requiere un objeto del tipo %s", "test.error.expected_items_count": "Se requieren %s objetos del tipo %s, se encontraron %s", "test.error.fail": "Se cumplieron las condiciones de falla", "test.error.invalid_block_type": "Se encontró un tipo de bloque no esperado: %s", "test.error.missing_block_entity": "Falta una entidad-bloque", "test.error.position": "%s en %s, %s, %s (relativo: %s, %s, %s) en el tic %s", "test.error.sequence.condition_already_triggered": "Condición ya activada en %s", "test.error.sequence.condition_not_triggered": "Condición no activada", "test.error.sequence.invalid_tick": "Éxito en tic no válido: se esperaba %s", "test.error.sequence.not_completed": "La prueba se terminó antes de que se completara la secuencia", "test.error.set_biome": "No se pudo establecer el bioma para la prueba", "test.error.spawn_failure": "No se pudo crear la entidad %s", "test.error.state_not_equal": "Estado incorrecto. Se requiere %s, obtenido: %s", "test.error.structure.failure": "No se pudo analizar la estructura: %s", "test.error.tick": "%s en el tic %s", "test.error.ticking_without_structure": "Ejecutar la prueba antes de colocar la estructura", "test.error.timeout.no_result": "No hubo ningún resultado durante %s tics", "test.error.timeout.no_sequences_finished": "Ninguna secuencia finalizó durante %s ticks", "test.error.too_many_entities": "Se requiere que exista un único %s alrededor de %s, %s, %s, pero se encontraron %s", "test.error.unexpected_block": "No se esperaba que el bloque fuese %s", "test.error.unexpected_entity": "No esperaba que existiera\n", "test.error.unexpected_item": "No se esperaba un objeto del tipo %s", "test.error.unknown": "Error interno desconocido: %s", "test.error.value_not_equal": "Se requiere que %s sea %s, obtenido: %s", "test.error.wrong_block_entity": "Tipo incorrecto de entidad-bloque: %s", "test_block.error.missing": "Falta el bloque %s en la estructura de prueba", "test_block.error.too_many": "Demasiados bloques %s", "test_block.invalid_timeout": "Tiempo de espera no válido (%s): debe ser un número positivo de tics", "test_block.message": "Mensaje:", "test_block.mode.accept": "Aceptar", "test_block.mode.fail": "<PERSON>ar", "test_block.mode.log": "Registrar", "test_block.mode.start": "Iniciar", "test_block.mode_info.accept": "Modo aceptar: acepta superación de (parte de) una prueba", "test_block.mode_info.fail": "Modo fallar: falla la prueba", "test_block.mode_info.log": "Modo Registro - Registra un mensaje", "test_block.mode_info.start": "Modo iniciar: el punto de inicio de una prueba", "test_instance.action.reset": "Reiniciar y cargar", "test_instance.action.run": "<PERSON><PERSON> y ejecutar", "test_instance.action.save": "Guardar estructura", "test_instance.description.batch": "Lote: %s", "test_instance.description.failed": "Falló: %s", "test_instance.description.function": "Función: %s", "test_instance.description.invalid_id": "Id. de prueba no válida", "test_instance.description.no_test": "Esa prueba no existe", "test_instance.description.structure": "Estructura: %s", "test_instance.description.type": "Tipo: %s", "test_instance.type.block_based": "Prueba basada en bloques", "test_instance.type.function": "Prueba de función integrada", "test_instance_block.entities": "Entidades:", "test_instance_block.error.no_test": "No se ha podido ejecutar el caso de prueba en %s, %s, %s porque no se ha definido una prueba", "test_instance_block.error.no_test_structure": "No se ha podido ejecutar el caso de prueba en %s, %s, %s porque no hay estructura de prueba", "test_instance_block.error.unable_to_save": "No se pudo guardar la plantilla de estructura de prueba en %s, %s, %s", "test_instance_block.invalid": "[no válido]", "test_instance_block.reset_success": "Se ha reiniciado la prueba: %s", "test_instance_block.rotation": "Rotación:", "test_instance_block.size": "Tamaño de la estructura de testeo", "test_instance_block.starting": "Iniciando prueba %s", "test_instance_block.test_id": "ID del caso de prueba", "title.32bit.deprecation": "Sistema de 32-bits detectado: ¡Esto podría impedirte jugar en el futuro, ya que se necesitara un sistema de 64-bits!", "title.32bit.deprecation.realms": "Minecraft próximamente requerirá un sistema de 64 bits, lo que te impedirá seguir usando Realms en este dispositivo. Necesitaras cancelar manualmente cualquier suscripción a Realms.", "title.32bit.deprecation.realms.check": "No volver a mostrar este mensaje", "title.32bit.deprecation.realms.header": "Sistema de 32 bits detectado", "title.credits": "Copyright Mojang AB. ¡No distribuir!", "title.multiplayer.disabled": "Modo multijugador desactivado. Por favor, revisa los ajustes de tu cuenta de Microsoft.", "title.multiplayer.disabled.banned.name": "Debes cambiar tu nombre de usuario antes de poder jugar online", "title.multiplayer.disabled.banned.permanent": "Tu cuenta está permanentemente suspendida del multijugador online", "title.multiplayer.disabled.banned.temporary": "Tu cuenta fue suspendida temporalmente del juego online", "title.multiplayer.lan": "Multijugador (LAN)", "title.multiplayer.other": "Multijugador (terceros)", "title.multiplayer.realms": "Multijugador (Realms)", "title.singleplayer": "Un jugador", "translation.test.args": "%s %s", "translation.test.complex": "Prefijo, %s%2$s otra vez %s y %1$s por último %s ¡y también %1$s otra vez!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "Hola %", "translation.test.invalid2": "hola %s", "translation.test.none": "¡Hola, mundo!", "translation.test.world": "mundo", "trim_material.minecraft.amethyst": "Material de amatista", "trim_material.minecraft.copper": "Material de cobre", "trim_material.minecraft.diamond": "Material de diamante", "trim_material.minecraft.emerald": "Material de esmeralda", "trim_material.minecraft.gold": "Material de oro", "trim_material.minecraft.iron": "Material de hierro", "trim_material.minecraft.lapis": "Material de lapislázuli", "trim_material.minecraft.netherite": "Material de netherita", "trim_material.minecraft.quartz": "Material de cuarzo", "trim_material.minecraft.redstone": "Material de redstone", "trim_material.minecraft.resin": "Material de resina", "trim_pattern.minecraft.bolt": "Ornamento de remache", "trim_pattern.minecraft.coast": "Ornamento de costa", "trim_pattern.minecraft.dune": "Ornamento de duna", "trim_pattern.minecraft.eye": "Ornamento de ojo", "trim_pattern.minecraft.flow": "Ornamento de flujo", "trim_pattern.minecraft.host": "Ornamento de anfritrión", "trim_pattern.minecraft.raiser": "Ornamento de criador", "trim_pattern.minecraft.rib": "Ornamento de costillas", "trim_pattern.minecraft.sentry": "Ornamento de centinela", "trim_pattern.minecraft.shaper": "Ornamento de formador", "trim_pattern.minecraft.silence": "Ornamento de silencio", "trim_pattern.minecraft.snout": "Ornamento de hocico", "trim_pattern.minecraft.spire": "Ornamento de aguja", "trim_pattern.minecraft.tide": "Ornamento de marea", "trim_pattern.minecraft.vex": "Ornamento de vex", "trim_pattern.minecraft.ward": "Ornamento de guardia", "trim_pattern.minecraft.wayfinder": "Ornamento de guía", "trim_pattern.minecraft.wild": "Ornamento salvaje", "tutorial.bundleInsert.description": "Click derecho para añadir ítems", "tutorial.bundleInsert.title": "Usa un saco", "tutorial.craft_planks.description": "El libro de recetas ayuda", "tutorial.craft_planks.title": "Fabrica madera", "tutorial.find_tree.description": "Golpea su tronco para conseguir madera", "tutorial.find_tree.title": "Encuentra un árbol", "tutorial.look.description": "Para ello, usa el mouse", "tutorial.look.title": "<PERSON> a tu alrededor", "tutorial.move.description": "Salta con %s", "tutorial.move.title": "Camina con %s, %s, %s y %s", "tutorial.open_inventory.description": "Pulsa %s", "tutorial.open_inventory.title": "Abre tu inventario", "tutorial.punch_tree.description": "Mantén %s", "tutorial.punch_tree.title": "Destruye el árbol", "tutorial.socialInteractions.description": "Presiona %s para abrir", "tutorial.socialInteractions.title": "Interacciones sociales", "upgrade.minecraft.netherite_upgrade": "Me<PERSON>ra de netherita"}