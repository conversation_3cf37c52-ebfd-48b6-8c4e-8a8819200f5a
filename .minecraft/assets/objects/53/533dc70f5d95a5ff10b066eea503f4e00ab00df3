{"accessibility.onboarding.accessibility.button": "Conf. de l'accesibilidá…", "accessibility.onboarding.screen.narrator": "Primi Intro p'activar el narrador", "accessibility.onboarding.screen.title": "¡Afáyate en Minecraft!\n\n¿Quies activar el narrador o dir a la configuración de l'accesibilidá?", "addServer.add": "<PERSON><PERSON>", "addServer.enterIp": "Direición del sirvidor", "addServer.enterName": "Nome del sirvidor", "addServer.resourcePack": "Paqts. recursos del sirvidor", "addServer.resourcePack.disabled": "Non", "addServer.resourcePack.enabled": "Sí", "addServer.resourcePack.prompt": "Entrugar", "addServer.title": "Edición de la información d'un sirvidor", "advMode.command": "Comandu de la consola", "advMode.mode": "<PERSON><PERSON>", "advMode.mode.auto": "Iterativu", "advMode.mode.autoexec.bat": "Siempres activu", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "Impulsu", "advMode.mode.redstoneTriggered": "Precisa redstone", "advMode.mode.sequence": "Cadena", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "Has ser operador nel mou creativu", "advMode.notEnabled": "Los bloques de comandos nun tán activaos nesti sirvidor", "advMode.previousOutput": "Salida anterior", "advMode.setCommand": "Afita un comandu nel bloque", "advMode.setCommand.success": "Comandu afitáu: %s", "advMode.trackOutput": "Ver la resultancia", "advMode.triggering": "Activando", "advMode.type": "Tipu", "advancement.advancementNotFound": "Desconozse l'avance: %s", "advancements.adventure.adventuring_time.description": "Descubri tolos biomes", "advancements.adventure.adventuring_time.title": "Hora d'aventures", "advancements.adventure.arbalistic.description": "Cola ballesta, mata a cinco creatures úniques d'un tiru", "advancements.adventure.arbalistic.title": "Arbalística", "advancements.adventure.avoid_vibration.description": "<PERSON>a gach<PERSON>u cerca d'un Sensor de Sculk o Guardia pa que nun te sienta", "advancements.adventure.avoid_vibration.title": "Furtivu 100", "advancements.adventure.blowback.description": "Mata un Breeze reflexando una de les sos cargues d'aire", "advancements.adventure.blowback.title": "Voló p'atrás", "advancements.adventure.brush_armadillo.description": "Algama Escudos d'Armadillu d'un Armadillu usando un Cepiyu", "advancements.adventure.brush_armadillo.title": "¿Nun ye escamaraviyosu?", "advancements.adventure.bullseye.description": "Da nel blancu d'una diana dende, pelo menos, 30 metros de distancia", "advancements.adventure.bullseye.title": "¡Nun toques la diana!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fai una vasía decorada con 4 fragmentos de cerámica", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauración Minuciosa", "advancements.adventure.crafters_crafting_crafters.description": "Tate cerca a un Crafter cuando esti faiga un Crafter", "advancements.adventure.crafters_crafting_crafters.title": "Facedores Faciendo Facedores", "advancements.adventure.fall_from_world_height.description": "Déxate cayer de la cima'l mundiu (llende de construcción) a lo más fondero y sobrevive", "advancements.adventure.fall_from_world_height.title": "Cueves y Cantiles", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Ciruxanu cardiovascular", "advancements.adventure.hero_of_the_village.description": "Defendi un pueblu de les invasiones", "advancements.adventure.hero_of_the_village.title": "Héroe'l pueblu", "advancements.adventure.honey_block_slide.description": "Cai enriba d'un bloque de miel p'amortiguar la cayida", "advancements.adventure.honey_block_slide.title": "¡A pol bollu!", "advancements.adventure.kill_a_mob.description": "Mata a cualesquier monstruu fosqueru", "advancements.adventure.kill_a_mob.title": "Cazamonstruos", "advancements.adventure.kill_all_mobs.description": "Mata a cada monstruu fosqueru", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Mata un monstruu cerca d'un Catalizador de Sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Medra", "advancements.adventure.lighten_up.description": "Llima una bombilla de cobre con un hachu pa facela rellumar más", "advancements.adventure.lighten_up.title": "Alluma esa cara", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protexi a un Aldeanu d'una descarga llétrica inesperada ensin provocar una quema", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Téunicu en pararrayos", "advancements.adventure.minecraft_trials_edition.description": "Entra nuna Cá<PERSON>", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON> Prueba(es)", "advancements.adventure.ol_betsy.description": "Tirar a la ballesta", "advancements.adventure.ol_betsy.title": "La vieya Betsy", "advancements.adventure.overoverkill.description": "Fai 50 puntos de dañu d'un solo golpe usand<PERSON>'l <PERSON>lu", "advancements.adventure.overoverkill.title": "Estra-Estramuertu", "advancements.adventure.play_jukebox_in_meadows.description": "Fai que los praos vibren col dulce cantar d'un Tocadiscos", "advancements.adventure.play_jukebox_in_meadows.title": "El soníu de la música", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Llei la señal de potencia d'una estantería cincelada usando un Comparador", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "El poder de los llibros", "advancements.adventure.revaulting.description": "Desbloquear una Caxa Perigosa con una Clave de Prueba Perigosa", "advancements.adventure.revaulting.title": "La Caxa Rural de Pandora", "advancements.adventure.root.description": "Aventures, esploración ya combates", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Llimpia un Bloque Sospechosu pa llograr un Fragmentu de Potería", "advancements.adventure.salvage_sherd.title": "El pasáu ta nel presente", "advancements.adventure.shoot_arrow.description": "Dispara a daqué con una Flecha", "advancements.adventure.shoot_arrow.title": "¡Bona puntería!", "advancements.adventure.sleep_in_bed.description": "Dormi nuna Cama pa camudar el to puntu de remanecimientu", "advancements.adventure.sleep_in_bed.title": "¡Dórmite neñu!", "advancements.adventure.sniper_duel.description": "Mata a una cadarma dende más de 50 metros", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON> d'arqueros", "advancements.adventure.spyglass_at_dragon.description": "Mira al Cuélebre del End con un cataleju", "advancements.adventure.spyglass_at_dragon.title": "¿Ye un avión?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON> a un Ghast con un Cataleju", "advancements.adventure.spyglass_at_ghast.title": "¿Ye un globu?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON> a un Loru con un Cataleju", "advancements.adventure.spyglass_at_parrot.title": "¿Ye un páxaru?", "advancements.adventure.summon_iron_golem.description": "Invoca al gólem de fierro pa que t'ayude a defender un pueblu", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON> fer<PERSON>", "advancements.adventure.throw_trident.description": "Llanza una Traenta escontra daqué.\nNota: Tirar la to única arma nun ye una bona idea.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON><PERSON><PERSON> cayer", "advancements.adventure.totem_of_undying.description": "Usa un tótem d'invencibilidá pa burllar a la muerte", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Comercia con un aldeanu", "advancements.adventure.trade.title": "¡Vaya ufierta!", "advancements.adventure.trade_at_world_height.description": "Comercia con un aldeanu nel llende altura del mundu", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplica estes plantiyes de ferrería siquier una vegada: <PERSON><PERSON><PERSON>, Focicu, Costiella, Custodia, Silenciu, Fadiu, Marea, Orientador", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Ferrería con estilu", "advancements.adventure.trim_with_any_armor_pattern.description": "Fai una armadura recortada n'una mesa de ferrería", "advancements.adventure.trim_with_any_armor_pattern.title": "Creando una nueva apariencia", "advancements.adventure.two_birds_one_arrow.description": "Mata a dos pantasmes con una flecha perforadora", "advancements.adventure.two_birds_one_arrow.title": "Dos páxaros d'un tiru", "advancements.adventure.under_lock_and_key.description": "Usa una llave de prueba n'una bóveda", "advancements.adventure.under_lock_and_key.title": "Cerráu de trancón", "advancements.adventure.use_lodestone.description": "I<PERSON>ta una brúxula con una magnetita", "advancements.adventure.use_lodestone.title": "Ma<PERSON><PERSON><PERSON>, llévame a casa", "advancements.adventure.very_very_frightening.description": "Da a un aldeanu con un rayu", "advancements.adventure.very_very_frightening.title": "Rayos ya centelles", "advancements.adventure.voluntary_exile.description": "Mata al capitán d'una invasión ya dempués nun t'averes a nengún pueblu nun tiempu, ¿acuéi?", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camina sobre la ñeve en polvu... ensin fundite per ella", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON> pluma", "advancements.adventure.who_needs_rockets.description": "Usa una Carga de Vientu para autu-propulsate 7 bloques escontra riba", "advancements.adventure.who_needs_rockets.title": "Los Cohetes son del Pasáu", "advancements.adventure.whos_the_pillager_now.description": "Da-y a un saquiador la so propia melecina", "advancements.adventure.whos_the_pillager_now.title": "<PERSON>qui<PERSON>", "advancements.empty": "<PERSON>un paez qu'equí heba nada…", "advancements.end.dragon_breath.description": "Recueyi aliendu de cuélebre con frascos", "advancements.end.dragon_breath.title": "¡Vaya fedor d'aliendu!", "advancements.end.dragon_egg.description": "Lleva'l güevu de cuélebre", "advancements.end.dragon_egg.title": "La xeneración viniente", "advancements.end.elytra.description": "Atopa un élitru", "advancements.end.elytra.title": "La llende ye'l cielu", "advancements.end.enter_end_gateway.description": "Escapa de la islla", "advancements.end.enter_end_gateway.title": "Escape remotu", "advancements.end.find_end_city.description": "<PERSON><PERSON>, ¿qué podría asoceder?", "advancements.end.find_end_city.title": "La ciudá na fin del xuegu", "advancements.end.kill_dragon.description": "<PERSON><PERSON> suerte", "advancements.end.kill_dragon.title": "Llibera l'End", "advancements.end.levitate.description": "Llevita 50 bloques pola mor de los ataques d'un shulker", "advancements.end.levitate.title": "Bones vistes dende equí", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON> al cuélebre d'End<PERSON>", "advancements.end.respawn_dragon.title": "Déjà vu", "advancements.end.root.description": "¿O'l comienzu?", "advancements.end.root.title": "The End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fai qu'un allay dexe cayer una tarta n'un bloque de notes", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON>u de Cumpleaños", "advancements.husbandry.allay_deliver_item_to_player.description": "Fai qu'un Allay apúrra-y artículos", "advancements.husbandry.allay_deliver_item_to_player.title": "Tienes un amigo en min", "advancements.husbandry.axolotl_in_a_bucket.description": "Atrapa un axolote con un calderu", "advancements.husbandry.axolotl_in_a_bucket.title": "El depredador más adurable", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> tolo comible, magar que seya dañible", "advancements.husbandry.balanced_diet.title": "Dieta equilibrada", "advancements.husbandry.breed_all_animals.description": "¡Cría tolos animales!", "advancements.husbandry.breed_all_animals.title": "A pares", "advancements.husbandry.breed_an_animal.description": "Alimenta a dos animales xuntos", "advancements.husbandry.breed_an_animal.title": "Les flores ya les abeyes", "advancements.husbandry.complete_catalogue.description": "¡Adoma toles variantes de gatu!", "advancements.husbandry.complete_catalogue.title": "Un gatálogu completu", "advancements.husbandry.feed_snifflet.description": "Ali<PERSON><PERSON> a <PERSON>nifflet", "advancements.husbandry.feed_snifflet.title": "Pequenos golifos", "advancements.husbandry.fishy_business.description": "Pesca un pexe", "advancements.husbandry.fishy_business.title": "Asuntu es<PERSON>osu", "advancements.husbandry.froglights.description": "Tener tolos <PERSON>l uces nel to inventariu", "advancements.husbandry.froglights.title": "¡Xuntos y Xuníos, Podemos!", "advancements.husbandry.kill_axolotl_target.description": "Forma equipu con un ajolote y gana una engarradiella", "advancements.husbandry.kill_axolotl_target.title": "¡El poder curatible de l'amistá!", "advancements.husbandry.leash_all_frog_variants.description": "Besorgen Si<PERSON> sich jede Frog-Variante an der Leine", "advancements.husbandry.leash_all_frog_variants.title": "Wenn die Truppe in die Stadt hüpft", "advancements.husbandry.make_a_sign_glow.description": "Bringen Sie den Text jeder Art von Schild zum Leuchten", "advancements.husbandry.make_a_sign_glow.title": "G<PERSON><PERSON><PERSON> und siehe!", "advancements.husbandry.netherite_hoe.description": "Usa un lingote de cetrería para ameyorar una azada y devalua tus decisiones bitales", "advancements.husbandry.netherite_hoe.title": "El valir marafundiao", "advancements.husbandry.obtain_sniffer_egg.description": "Consiga un Güevo Anfiler", "advancements.husbandry.obtain_sniffer_egg.title": "Cuele interesante", "advancements.husbandry.place_dried_ghast_in_water.description": "Pon un bloque de ghast secu n'agua", "advancements.husbandry.place_dried_ghast_in_water.title": "¡Mantente hidratáu!", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant any Sniffer seed", "advancements.husbandry.plant_any_sniffer_seed.title": "Անցյալի տնկում", "advancements.husbandry.plant_seed.description": "Planta semiente y mírala medrar", "advancements.husbandry.plant_seed.title": "Un llugar semáu", "advancements.husbandry.remove_wolf_armor.description": "<PERSON><PERSON>rar l'Armadura de llobu d'un llobu usando tiéras", "advancements.husbandry.remove_wolf_armor.title": "Brillanteza de la Tiéra", "advancements.husbandry.repair_wolf_armor.description": "Igua una Armadura de Llobu usando Escudos d'Armadillu", "advancements.husbandry.repair_wolf_armor.title": "Como nuevu", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Metese nuna llancha y flotar con una cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "¡Cualquier cosa que te moleste!", "advancements.husbandry.root.description": "El mundu ta enllén de collacios ya comida", "advancements.husbandry.root.title": "<PERSON><PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Usa una foguera pa recoyer el miel d'una colmena col usu d'una botella ensin enfadar a les abeyes", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON>, esta ye la to casa", "advancements.husbandry.silk_touch_nest.description": "Mueve unu Truébanu con 3 abeyes usando toque de seda", "advancements.husbandry.silk_touch_nest.title": "<PERSON><PERSON> d'abeyar, ho", "advancements.husbandry.tactical_fishing.description": "Atrapa un pexe... ¡Ensin una caña de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca táutica", "advancements.husbandry.tadpole_in_a_bucket.description": "Atrapa un renacuayu nun cubu", "advancements.husbandry.tadpole_in_a_bucket.title": "Renac<PERSON><PERSON> al balde", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON>la un animal", "advancements.husbandry.tame_an_animal.title": "Volver a llamber el xatu", "advancements.husbandry.wax_off.description": "¡Llima la cera d'un bloque de cobre!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "¡Aplicar Caxellu a un Bloque de cobre!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "Domar una de cada variante de llobu", "advancements.husbandry.whole_pack.title": "La manada enteru", "advancements.nether.all_effects.description": "Ten aplicaos tolos efeutos al empar", "advancements.nether.all_effects.title": "¿Cómo aportemos hasta equí?", "advancements.nether.all_potions.description": "Ten aplicaos tolos efeutos de brebaxes al empar", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON><PERSON> peligro<PERSON>", "advancements.nether.brew_potion.description": "<PERSON>aer un potingue", "advancements.nether.brew_potion.title": "Destile<PERSON>ía ll<PERSON>al", "advancements.nether.charge_respawn_anchor.description": "Carga un Ancla de reapaición al máximu", "advancements.nether.charge_respawn_anchor.title": "Igual 9 vides non, ¿eh?", "advancements.nether.create_beacon.description": "Construye y asitia un faru", "advancements.nether.create_beacon.title": "Allumando'l camín", "advancements.nether.create_full_beacon.description": "Dai a un faru máxima potencia", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "Distrái a los piglins con oru", "advancements.nether.distract_piglin.title": "Fiebre pol oru", "advancements.nether.explore_nether.description": "Esplora tolos biomes del Nether", "advancements.nether.explore_nether.title": "Turismu infernal", "advancements.nether.fast_travel.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON>her pa percorrer 7 km na superficie", "advancements.nether.fast_travel.title": "Corvadura espacial", "advancements.nether.find_bastion.description": "Entra nun Remanente de Bastión", "advancements.nether.find_bastion.title": "Recuerdos de cuantayá", "advancements.nether.find_fortress.description": "Adiéntrate nuna fortaleza del Nether", "advancements.nether.find_fortress.title": "Una fortaleza terrible", "advancements.nether.get_wither_skull.description": "Consigu<PERSON>'l craniu d'una cadarma de <PERSON>", "advancements.nether.get_wither_skull.title": "A testeraes", "advancements.nether.loot_bastion.description": "Roba una arca nuna Remanente de bastión", "advancements.nether.loot_bastion.title": "Guerreros gochos", "advancements.nether.netherite_armor.description": "Consigue tol conxuntu de l'armadura de netherita", "advancements.nether.netherite_armor.title": "Cubríime d'escombriu", "advancements.nether.obtain_ancient_debris.description": "Consigui escombriu", "advancements.nether.obtain_ancient_debris.title": "No más fondo", "advancements.nether.obtain_blaze_rod.description": "Rínca-y una vara a un espíritu llumiegu", "advancements.nether.obtain_blaze_rod.title": "<PERSON>eu en culu", "advancements.nether.obtain_crying_obsidian.description": "Consigui obsidiana lloroso", "advancements.nether.obtain_crying_obsidian.title": "¿Quién corta cebolles, ho?", "advancements.nether.return_to_sender.description": "Destrúi un ghast con una bola de fueu", "advancements.nether.return_to_sender.title": "Mensaxe torgáu", "advancements.nether.ride_strider.description": "<PERSON>ate nun Lavagante con un hongo alteráu nun palu", "advancements.nether.ride_strider.title": "Esta barca tien pates", "advancements.nether.ride_strider_in_overworld_lava.description": "Da un llaaaaaaaaargu paséu con un lavagante sobro un llagu de lava na superficie", "advancements.nether.ride_strider_in_overworld_lava.title": "Siéntese como nel llar", "advancements.nether.root.description": "Trai ropa braniego", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Invoca al wither", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Rescata un ghast del Nether, llévalu con seguranza al mundu normal… y dempués mátalu", "advancements.nether.uneasy_alliance.title": "<PERSON>ie<PERSON>, -a, -o", "advancements.nether.use_lodestone.description": "Usa una brúxula n'una magnetita", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, llévame a casina", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilita y dempués cura a un aldeanu zombi", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Devuelve un proyectil con un escudu", "advancements.story.deflect_arrow.title": "<PERSON><PERSON><PERSON>, gracies", "advancements.story.enchant_item.description": "Encantexa un oxetu nuna tabla d'encantexos", "advancements.story.enchant_item.title": "Encantexador", "advancements.story.enter_the_end.description": "Entra nel portal del End", "advancements.story.enter_the_end.title": "The End?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, priendi ya entra nun portal del Nether", "advancements.story.enter_the_nether.title": "Diendo p'acull<PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON>gu<PERSON> a un gueyu d'Ender", "advancements.story.follow_ender_eye.title": "Güeyu esclucador", "advancements.story.form_obsidian.description": "Consigue un bloque d'obsidiana", "advancements.story.form_obsidian.title": "<PERSON><PERSON>… ¿esfrecío?", "advancements.story.iron_tools.description": "<PERSON><PERSON><PERSON> el to picu", "advancements.story.iron_tools.title": "<PERSON><PERSON> dar en fierro", "advancements.story.lava_bucket.description": "<PERSON><PERSON> un caldeiru con agua", "advancements.story.lava_bucket.title": "De sangre caliente", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "¡Diamantes!", "advancements.story.mine_stone.description": "<PERSON> piedres col to nuevu picu", "advancements.story.mine_stone.title": "Ed<PERSON> de <PERSON>dra", "advancements.story.obtain_armor.description": "Protéxite con una pieza d'armadura de fierro", "advancements.story.obtain_armor.title": "Vístite", "advancements.story.root.description": "El corazón ya la hestoria del xuegu", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "L'armadura de diamante salva vides", "advancements.story.shiny_gear.title": "Cubríime con diamantes", "advancements.story.smelt_iron.description": "Funde un lingote de fierro", "advancements.story.smelt_iron.title": "En fierros", "advancements.story.upgrade_tools.description": "Faite un meyor picu de fierro", "advancements.story.upgrade_tools.title": "Calidá ameyorada", "advancements.toast.challenge": "¡Completóse un retu!", "advancements.toast.goal": "¡Algamóse una meta!", "advancements.toast.task": "¡Fízose un avance!", "argument.anchor.invalid": "La ubicación de anclaje de la entidad no es válida: %s", "argument.angle.incomplete": "Incompletu (requier 1 ángulu)", "argument.angle.invalid": "L'ángulu nun ye válidu", "argument.block.id.invalid": "Tipu de bloque \"%s' desconocíu", "argument.block.property.duplicate": "La propiedá '%s' d'l bloque %s solo pode ponese una vegada", "argument.block.property.invalid": "El bloque %s nun acepta \"%s\" para la propiedá \"%s\"", "argument.block.property.novalue": "Ríquese un valor para la propiedá \"%s\" del bloque %s", "argument.block.property.unclosed": "Esperábase'l corchete de zarru pa bloquiar les propiedaes del estáu", "argument.block.property.unknown": "El bloque %s nun tien la propiedá «%s»", "argument.block.tag.disallowed": "Equí nun se permiten les etiquetes, namás bloques", "argument.color.invalid": "Desconozse'l color «%s»", "argument.component.invalid": "Componente de chat non válidu: %s", "argument.criteria.invalid": "Desconozse'l criteriu «%s»", "argument.dimension.invalid": "Desconozse la dimensión «%s»", "argument.double.big": "El valor doble nun ha ser mayor que %s, atopóse %s", "argument.double.low": "El valor doble nun ha ser menor que %s, atopóse %s", "argument.entity.invalid": "El nome o la UUID nun son v<PERSON><PERSON><PERSON>", "argument.entity.notfound.entity": "<PERSON><PERSON> s'<PERSON>ó nenguna entidá", "argument.entity.notfound.player": "<PERSON><PERSON> s'<PERSON><PERSON> neng<PERSON> xugador", "argument.entity.options.advancements.description": "Xugadores con avances", "argument.entity.options.distance.description": "Distancia hasta la entidá", "argument.entity.options.distance.negative": "La distancia nun pue ser negativa", "argument.entity.options.dx.description": "Entidaes ente x ya x + dx", "argument.entity.options.dy.description": "Entidaes ente y ya y + dy", "argument.entity.options.dz.description": "Entidaes ente z ya z + dz", "argument.entity.options.gamemode.description": "Xugadores con mou de xuegu", "argument.entity.options.inapplicable": "Equí nun se pue aplicar la opción «%s»", "argument.entity.options.level.description": "Nivel d'esperiencia", "argument.entity.options.level.negative": "El nivel nun habría ser negativu", "argument.entity.options.limit.description": "El númberu máximu d'entidaes a devolver", "argument.entity.options.limit.toosmall": "La llende ha ser de 1", "argument.entity.options.mode.invalid": "Mou de xuegu non válidu o desconocíu: %s", "argument.entity.options.name.description": "Nome d'entidá", "argument.entity.options.nbt.description": "Entidaes con NBT", "argument.entity.options.predicate.description": "Predicáu personalizáu", "argument.entity.options.scores.description": "Entidaes con puntuaciones", "argument.entity.options.sort.description": "Ordenar entidaes", "argument.entity.options.sort.irreversible": "El tipu de clasificación \"%s\" ye desconocíu o nun ye válidu", "argument.entity.options.tag.description": "Entidaes con etiqueta", "argument.entity.options.team.description": "Entidaes nel equipu", "argument.entity.options.type.description": "Entidaes de un tipu determináu", "argument.entity.options.type.invalid": "Tipu d'entidá inválidu o desconocíu '%s'", "argument.entity.options.unknown": "Desconozse la opción «%s»", "argument.entity.options.unterminated": "Esperábase un corchete de zarru", "argument.entity.options.valueless": "Valor esperáu pa la opción '%s'", "argument.entity.options.x.description": "Posición en x", "argument.entity.options.x_rotation.description": "Rotación en x de la entidá", "argument.entity.options.y.description": "Posición en y", "argument.entity.options.y_rotation.description": "Rotación en y de la entidad", "argument.entity.options.z.description": "Posición en z", "argument.entity.selector.allEntities": "<PERSON>les entidaes", "argument.entity.selector.allPlayers": "<PERSON><PERSON>", "argument.entity.selector.missing": "Faltache'l tipu de selector", "argument.entity.selector.nearestEntity": "La entidad más cercana", "argument.entity.selector.nearestPlayer": "El xugador más cercanu", "argument.entity.selector.not_allowed": "Selector non permitíu", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON> al debalu", "argument.entity.selector.self": "Entidá actual", "argument.entity.selector.unknown": "Tipu de selector desconocíu '%s'", "argument.entity.toomany": "Namás se permite una entidá mas el selector apurríu permite más d'una", "argument.enum.invalid": "Valor non válidu \"%s\"", "argument.float.big": "El valor flotante nun ha ser mayor que %s, atopóse %s", "argument.float.low": "El valor flotante nun ha ser menor que %s, atopóse %s", "argument.gamemode.invalid": "Modu de xuegu desconocíu: %s", "argument.hexcolor.invalid": "Códigu de color hexadecimal non válidu '%s'", "argument.id.invalid": "ID non válida", "argument.id.unknown": "Desconozse la ID: %s", "argument.integer.big": "El valor enteru nun ha ser mayor que %s, atopóse %s", "argument.integer.low": "El valor enteru nun ha ser menor que %s, atopóse %s", "argument.item.id.invalid": "Desconozse l'oxetu «%s»", "argument.item.tag.disallowed": "Les etiquetes nun tan permitíes equí, namái elementos reales", "argument.literal.incorrect": "Espérase un lletal %s", "argument.long.big": "El valor llongu nun ha ser mayor que %s, atopóse %s", "argument.long.low": "El valor llongu nun ha ser menor que %s, atopóse %s", "argument.message.too_long": "El mensaxe de chat yera muncho llargu (%s > máximu de %s caracteres)", "argument.nbt.array.invalid": "Triba de matriz inválida '%s'", "argument.nbt.array.mixed": "Nun se pue inxertar %s en %s", "argument.nbt.expected.compound": "Requíerese etiqueta compuesta", "argument.nbt.expected.key": "Esperábase una clave", "argument.nbt.expected.value": "Esperábase un valor", "argument.nbt.list.mixed": "Nun se pue inxertar %s na llista de %s", "argument.nbt.trailing": "Datos finales inesperaos", "argument.player.entities": "<PERSON><PERSON>i comandu solo afecta a xugadores, mas el selector utilizáu inclúi entidaes", "argument.player.toomany": "Namás se permite un xugador mas el selector apurríu permite más d'unu", "argument.player.unknown": "<PERSON><PERSON> x<PERSON>dor nun esiste", "argument.pos.missing.double": "Esperábase una coordenada", "argument.pos.missing.int": "Ríquese la posición d'un bloque", "argument.pos.mixed": "Nun se puen mecer les coordenaes del mundu ya les llocales (les dos han usar ^ o non)", "argument.pos.outofbounds": "Esa posición ta fuera de les llendes permitíes.", "argument.pos.outofworld": "¡Esa posición ta fuera d'esti mundu!", "argument.pos.unloaded": "Esa posición nun ta cargada", "argument.pos2d.incomplete": "Incompletu (precísense 2 coordenaes)", "argument.pos3d.incomplete": "El comandu ta incompletu (esperábense 3 coordenaes)", "argument.range.empty": "Valor esperáu o rangu de valores", "argument.range.ints": "Namás se permiten númberos enteros, non decimales", "argument.range.swapped": "El mínimu nun pue ser mayor que'l máximu", "argument.resource.invalid_type": "L'elementu '%s' tien un tipu incorreutu '%s' (esperábase '%s')", "argument.resource.not_found": "Nun se pue alcontrar l'elementu '%s' del tipu '%s'", "argument.resource_or_id.failed_to_parse": "Fallu al analizar la estructura: %s", "argument.resource_or_id.invalid": "Identificador o etiqueta inválida", "argument.resource_or_id.no_such_element": "Non puede atopase l'elementu '%s' nel rexistru '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "La etiqueta '%s' tien un tipu incorreutu '%s' (esperábase '%s')", "argument.resource_tag.not_found": "Nun se pue alcontrar la etiqueta '%s' de tipu '%s'", "argument.rotation.incomplete": "El comandu ta incompletu (esperábense 2 coordenaes)", "argument.scoreHolder.empty": "<PERSON>un s'<PERSON>aron marcadores de puntuación relevantes", "argument.scoreboardDisplaySlot.invalid": "El ranuru d'amuesa desconocíu '%s'", "argument.style.invalid": "L'estilu ye inválidu: %s", "argument.time.invalid_tick_count": "La cuntá de marques tien que ser non negativa", "argument.time.invalid_unit": "La unidá ye inválida", "argument.time.tick_count_too_low": "La cuntá de marques nun puede ser menos de %s, atopóse %s", "argument.uuid.invalid": "La UUID nun ye válida", "argument.waypoint.invalid": "La entidá seleccioná nun tien una señal", "arguments.block.tag.unknown": "Etiqueta de bloque desconocida '%s'", "arguments.function.tag.unknown": "Etiqueta de función desconocida '%s'", "arguments.function.unknown": "Desconozse la función %s", "arguments.item.component.expected": "Componente d'elementu esperáu", "arguments.item.component.malformed": "Componente mal formáu '%s': '%s'", "arguments.item.component.repeated": "El componenti d'elementu '%s' s'esta repitiendo, pero namás pue especificase un valor", "arguments.item.component.unknown": "Componente d'elementu desconocíu '%s'", "arguments.item.malformed": "Elementu mal formáu: '%s'", "arguments.item.overstacked": "%s namás pue apilase hasta %s", "arguments.item.predicate.malformed": "Predicáu mal formáu: '%s' '%s'", "arguments.item.predicate.unknown": "Predicáu d'elementu desconocíu '%s'", "arguments.item.tag.unknown": "Etiqueta d'elementu desconocida '%s'", "arguments.nbtpath.node.invalid": "Elementu de ruta NBT inválidu", "arguments.nbtpath.nothing_found": "Nun s'atoparon elementos que concasen con %s", "arguments.nbtpath.too_deep": "NBT resultante demasiado anidáu", "arguments.nbtpath.too_large": "Taas oo keentay NBT aad u weyn", "arguments.objective.notFound": "Oxetivu de tabla desconocíu '%s'", "arguments.objective.readonly": "L'oxetivu éstoesu de la tabla '%s' es namái de llectura", "arguments.operation.div0": "Nun se pue dividir por cero", "arguments.operation.invalid": "La operación ye inválida", "arguments.swizzle.invalid": "Exes non válides: ríquese la combinación de \"X\", \"Y\" y \"Z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Dureza de la armadura", "attribute.name.attack_damage": "Daño de ataque", "attribute.name.attack_knockback": "Retroceso de ataque", "attribute.name.attack_speed": "Velocidá d'ataque", "attribute.name.block_break_speed": "Velocidá de rotura de bloques", "attribute.name.block_interaction_range": "Rango d'interacción de bloques", "attribute.name.burning_time": "Tiempu de quemáu", "attribute.name.camera_distance": "Distancia de la Cámara", "attribute.name.entity_interaction_range": "Rango d'interacción d'entidaes", "attribute.name.explosion_knockback_resistance": "Resistencia al retroceso de la explosión", "attribute.name.fall_damage_multiplier": "Multiplicador de daño por caída", "attribute.name.flying_speed": "Velocidá de vuelu", "attribute.name.follow_range": "Rangu de seguimientu de la mafia", "attribute.name.generic.armor": "d'<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "de durez d'armadura", "attribute.name.generic.attack_damage": "de <PERSON><PERSON><PERSON>'<PERSON>", "attribute.name.generic.attack_knockback": "Retrocesu d'ataque", "attribute.name.generic.attack_speed": "de velocidá d'ataque", "attribute.name.generic.block_interaction_range": "Rangu d'interacción con bloques", "attribute.name.generic.burning_time": "Tiempu de quemáu", "attribute.name.generic.entity_interaction_range": "Rangu d'interacción con entidaes", "attribute.name.generic.explosion_knockback_resistance": "Resistencia l'empuxón d'esplosión", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de dañu per cayida", "attribute.name.generic.flying_speed": "de velocidá de vuelu", "attribute.name.generic.follow_range": "de rangu de siguimientu de creatures", "attribute.name.generic.gravity": "Gravedá", "attribute.name.generic.jump_strength": "Potencia de Saltu", "attribute.name.generic.knockback_resistance": "de resistencia al emburriu", "attribute.name.generic.luck": "Potra", "attribute.name.generic.max_absorption": "Absorción máxima", "attribute.name.generic.max_health": "de vida máxima", "attribute.name.generic.movement_efficiency": "Eficiencia de movimientu", "attribute.name.generic.movement_speed": "Velocidá", "attribute.name.generic.oxygen_bonus": "Bonus d'oxíxenu", "attribute.name.generic.safe_fall_distance": "Distancia de cayida segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Altor del pasu", "attribute.name.generic.water_movement_efficiency": "Eficiencia de movimientu na agua", "attribute.name.gravity": "Gravedá", "attribute.name.horse.jump_strength": "Fuercia del saltu'l caballu", "attribute.name.jump_strength": "Fuerza del saltu", "attribute.name.knockback_resistance": "Resistencia de retrocesu", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Absorción Máxima", "attribute.name.max_health": "Salú máxima", "attribute.name.mining_efficiency": "Eficiencia al minar", "attribute.name.movement_efficiency": "Eficiencia de movimientu", "attribute.name.movement_speed": "Velocidá", "attribute.name.oxygen_bonus": "Bonus d'osíxenu", "attribute.name.player.block_break_speed": "Velocidá de frayatu del bloque", "attribute.name.player.block_interaction_range": "Rangue d'interacción con bloques", "attribute.name.player.entity_interaction_range": "Rangue d'interacción con entidaes", "attribute.name.player.mining_efficiency": "Eficiencia de minaú", "attribute.name.player.sneaking_speed": "Velocidá d'escuenta", "attribute.name.player.submerged_mining_speed": "Velocidá de mina sumerxida", "attribute.name.player.sweeping_damage_ratio": "Ra<PERSON><PERSON> de <PERSON> de <PERSON>", "attribute.name.safe_fall_distance": "Distancia de cayida segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocidá d'esclucar", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "attribute.name.step_height": "<PERSON><PERSON>", "attribute.name.submerged_mining_speed": "Velocidá de minería sumerxida", "attribute.name.sweeping_damage_ratio": "Relación de dañu de barríu", "attribute.name.tempt_range": "Radiu d'atracción de criatures", "attribute.name.water_movement_efficiency": "Eficiencia de movimientu nel agua", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON>is", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Delta de basaltu", "biome.minecraft.beach": "<PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Viesca de bidul", "biome.minecraft.cherry_grove": "<PERSON>les de cerezos", "biome.minecraft.cold_ocean": "Océanu fríu", "biome.minecraft.crimson_forest": "Viesca carmesina", "biome.minecraft.dark_forest": "Viesca escura", "biome.minecraft.deep_cold_ocean": "Océanu fr<PERSON>u fondu", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON> xel<PERSON>u fondu", "biome.minecraft.deep_lukewarm_ocean": "Océanu templáu fondu", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON> fondu", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Cueves d'espeleotema", "biome.minecraft.end_barrens": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_highlands": "Tierres altes del End", "biome.minecraft.end_midlands": "Medio End", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "Viesca floral", "biome.minecraft.forest": "Viesca", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "Picos de Europa", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Picos de xelu", "biome.minecraft.jagged_peaks": "Picos escarpiaos", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Océanu templáu", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON> frondoses", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Campos de fungos", "biome.minecraft.nether_wastes": "<PERSON><PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Bocarza de quilces pernenes", "biome.minecraft.old_growth_pine_taiga": "Anciana taiga de pino", "biome.minecraft.old_growth_spruce_taiga": "Antigua taiga de abetos", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "Llanada", "biome.minecraft.river": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Altiplanu de la sabana", "biome.minecraft.small_end_islands": "Islles pequeñes del End", "biome.minecraft.snowy_beach": "<PERSON><PERSON><PERSON> nevada", "biome.minecraft.snowy_plains": "Llanures nevancies", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_taiga": "Taiga nevada", "biome.minecraft.soul_sand_valley": "Valle de sable d'ánimes", "biome.minecraft.sparse_jungle": "Xunglá espaciada", "biome.minecraft.stony_peaks": "Picos pedre<PERSON>os", "biome.minecraft.stony_shore": "<PERSON> ped<PERSON>", "biome.minecraft.sunflower_plains": "Llanada de xirasoles", "biome.minecraft.swamp": "Pantanu", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON><PERSON>", "biome.minecraft.the_void": "Mieres del camin", "biome.minecraft.warm_ocean": "Océanu caliente", "biome.minecraft.warped_forest": "Viesca distorsionada", "biome.minecraft.windswept_forest": "Bosque barríu polo vientu", "biome.minecraft.windswept_gravelly_hills": "Colles calizas desgastadas polo vientu", "biome.minecraft.windswept_hills": "Colles barríes polo vientu", "biome.minecraft.windswept_savanna": "Savana barría polo vientu", "biome.minecraft.wooded_badlands": "Brameles con arboláu", "block.minecraft.acacia_button": "Botón d'alcacia", "block.minecraft.acacia_door": "Puerta <PERSON>'al<PERSON>", "block.minecraft.acacia_fence": "Valla <PERSON>'<PERSON>", "block.minecraft.acacia_fence_gate": "Portiella <PERSON>", "block.minecraft.acacia_hanging_sign": "Cartel colgante d'acaciu", "block.minecraft.acacia_leaves": "Fueyes <PERSON>", "block.minecraft.acacia_log": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_planks": "Tables d'alcacia", "block.minecraft.acacia_pressure_plate": "Placa de presión d'alcacia", "block.minecraft.acacia_sapling": "<PERSON>iltu <PERSON>'<PERSON>", "block.minecraft.acacia_sign": "Cartelu <PERSON>", "block.minecraft.acacia_slab": "Llábana d'alcacia", "block.minecraft.acacia_stairs": "Pasales d'alcacia", "block.minecraft.acacia_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_wall_hanging_sign": "Cartel colgante d'acaciu pa la pared", "block.minecraft.acacia_wall_sign": "Cartelu d'alcacia na parede", "block.minecraft.acacia_wood": "Madera d'alcacia con corteza", "block.minecraft.activator_rail": "Vía activadora", "block.minecraft.air": "Aire", "block.minecraft.allium": "<PERSON><PERSON> bravu", "block.minecraft.amethyst_block": "Bloque d'amatista", "block.minecraft.amethyst_cluster": "Conxuntu d'amatistas", "block.minecraft.ancient_debris": "Escombrios antiguos", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Llábana d'andesita", "block.minecraft.andesite_stairs": "Pasales d'andesita", "block.minecraft.andesite_wall": "<PERSON><PERSON> d'and<PERSON>ta", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Tallu de sandía xun<PERSON>u", "block.minecraft.attached_pumpkin_stem": "Tallu de calabaza xuníu", "block.minecraft.azalea": "Rododend<PERSON>", "block.minecraft.azalea_leaves": "Fueyes de rododendru", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_button": "Botón de bam<PERSON>ú", "block.minecraft.bamboo_door": "<PERSON><PERSON><PERSON> de b<PERSON>ú", "block.minecraft.bamboo_fence": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_fence_gate": "Puerta de cer<PERSON>áu de bam<PERSON>ú", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON> de bam<PERSON>", "block.minecraft.bamboo_mosaic": "Mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_slab": "L<PERSON><PERSON><PERSON><PERSON> de mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_stairs": "Escaleres de mosaico de bam<PERSON>ú", "block.minecraft.bamboo_planks": "Tablones de bam<PERSON>ú", "block.minecraft.bamboo_pressure_plate": "Placa de presión de bambú", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON> de bam<PERSON>", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.bamboo_slab": "<PERSON><PERSON>", "block.minecraft.bamboo_stairs": "Escaleres de bam<PERSON>ú", "block.minecraft.bamboo_trapdoor": "Portella trampa de bambú", "block.minecraft.bamboo_wall_hanging_sign": "Cartelu de bambú pa colgar na paret", "block.minecraft.bamboo_wall_sign": "Cartelu de bambú pa pindiar na pared", "block.minecraft.banner.base.black": "Estandarte en prieto", "block.minecraft.banner.base.blue": "Estandarte n'azul", "block.minecraft.banner.base.brown": "Estandarte en marrón", "block.minecraft.banner.base.cyan": "Estandarte en ciano", "block.minecraft.banner.base.gray": "Estandarte en buxo", "block.minecraft.banner.base.green": "Estandarte en verde", "block.minecraft.banner.base.light_blue": "Estandarte n'azul claro", "block.minecraft.banner.base.light_gray": "Estandarte en buxo claro", "block.minecraft.banner.base.lime": "Estandarte en llima", "block.minecraft.banner.base.magenta": "Estandarte en maxenta", "block.minecraft.banner.base.orange": "Estandarte en naranxa", "block.minecraft.banner.base.pink": "Estandarte en rosa", "block.minecraft.banner.base.purple": "Estandarte en morao", "block.minecraft.banner.base.red": "Estandarte en colorao", "block.minecraft.banner.base.white": "Estandarte en blanco", "block.minecraft.banner.base.yellow": "Estandarte en mariello", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>tu", "block.minecraft.banner.border.blue": "Berb<PERSON>u a<PERSON>l", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.border.green": "Berb<PERSON><PERSON> verde", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON> azul claru", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON> llima", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.border.orange": "Berb<PERSON>u naran<PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.banner.border.red": "Berbesu <PERSON>áu", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "Enlladr<PERSON>o prieto", "block.minecraft.banner.bricks.blue": "En<PERSON>dr<PERSON>o a<PERSON>l", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.bricks.green": "Enlladr<PERSON>o verde", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON>dr<PERSON>o azul claro", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> buxu claru", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> llima", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.bricks.orange": "En<PERSON>dr<PERSON>o naranxa", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> mor<PERSON>", "block.minecraft.banner.bricks.red": "Enlladriyao colorao", "block.minecraft.banner.bricks.white": "Enlladr<PERSON>o blanco", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.circle.black": "Redondel prietu", "block.minecraft.banner.circle.blue": "Redondel azul", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Redondel cianu", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.circle.green": "Redondel verde", "block.minecraft.banner.circle.light_blue": "Redondel azul claru", "block.minecraft.banner.circle.light_gray": "Redondel buxu claro", "block.minecraft.banner.circle.lime": "Redondel llima", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.circle.orange": "Redondel naranxa", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.circle.purple": "Redondel mor<PERSON>", "block.minecraft.banner.circle.red": "Redondel coloráu", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.creeper.black": "Dibuxu prietu de <PERSON>per", "block.minecraft.banner.creeper.blue": "Dibuxu azul de Creeper", "block.minecraft.banner.creeper.brown": "Dibuxu ma<PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Dibuxu c<PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON><PERSON> buxu <PERSON>", "block.minecraft.banner.creeper.green": "Dibuxu verde de Creeper", "block.minecraft.banner.creeper.light_blue": "Dibuxu azul clar<PERSON>", "block.minecraft.banner.creeper.light_gray": "Dibu<PERSON>u buxu claru <PERSON>", "block.minecraft.banner.creeper.lime": "Dibuxu llima de Creeper", "block.minecraft.banner.creeper.magenta": "Dibux<PERSON> ma<PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "Dibuxu naranxa de Creeper", "block.minecraft.banner.creeper.pink": "Dibuxu rosa de Creeper", "block.minecraft.banner.creeper.purple": "Dibuxu mor<PERSON><PERSON>", "block.minecraft.banner.creeper.red": "Dibuxu coloráu de Creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "<PERSON>sa prieta", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Nasa ciana", "block.minecraft.banner.cross.gray": "<PERSON><PERSON> buxa", "block.minecraft.banner.cross.green": "Nasa verde", "block.minecraft.banner.cross.light_blue": "<PERSON>sa azul clara", "block.minecraft.banner.cross.light_gray": "<PERSON>sa buxa clara", "block.minecraft.banner.cross.lime": "<PERSON><PERSON> llima", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON> ma<PERSON>ta", "block.minecraft.banner.cross.orange": "<PERSON><PERSON> na<PERSON>", "block.minecraft.banner.cross.pink": "<PERSON>sa rosa", "block.minecraft.banner.cross.purple": "<PERSON>sa morada", "block.minecraft.banner.cross.red": "Nasa colorada", "block.minecraft.banner.cross.white": "<PERSON><PERSON> blanca", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> ma<PERSON>la", "block.minecraft.banner.curly_border.black": "Berbesu dentáu en prieto", "block.minecraft.banner.curly_border.blue": "Berb<PERSON>u dentáu n'azul", "block.minecraft.banner.curly_border.brown": "Berbesu dentáu en marrón", "block.minecraft.banner.curly_border.cyan": "Berbesu dentáu en ciano", "block.minecraft.banner.curly_border.gray": "Berb<PERSON>u dentáu en buxo", "block.minecraft.banner.curly_border.green": "Berb<PERSON>u dentáu en verde", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON><PERSON> dentáu n'azul claro", "block.minecraft.banner.curly_border.light_gray": "Berb<PERSON><PERSON> dentáu en buxo claro", "block.minecraft.banner.curly_border.lime": "Berb<PERSON>u dentáu en llima", "block.minecraft.banner.curly_border.magenta": "Berb<PERSON>u dentáu en maxenta", "block.minecraft.banner.curly_border.orange": "Berbesu dentáu en naranxa", "block.minecraft.banner.curly_border.pink": "Berb<PERSON>u dentáu en rosa", "block.minecraft.banner.curly_border.purple": "Berb<PERSON>u dentáu en morao", "block.minecraft.banner.curly_border.red": "Berbesu dentáu en colorao", "block.minecraft.banner.curly_border.white": "Berbesu dentáu en blanco", "block.minecraft.banner.curly_border.yellow": "<PERSON>rb<PERSON>u dentáu en mariello", "block.minecraft.banner.diagonal_left.black": "Triángulu cimeru a manzorga en prieto", "block.minecraft.banner.diagonal_left.blue": "Triángulu cimeru a manzorga n'azul", "block.minecraft.banner.diagonal_left.brown": "Triángulu cimeru a manzorga en marrón", "block.minecraft.banner.diagonal_left.cyan": "Triángulu cimeru a manzorga en ciano", "block.minecraft.banner.diagonal_left.gray": "Triángulu cimeru a manzorga en buxo", "block.minecraft.banner.diagonal_left.green": "Triángulu cimeru a manzorga en verde", "block.minecraft.banner.diagonal_left.light_blue": "Triángulu cimeru a manzorga n'azul claro", "block.minecraft.banner.diagonal_left.light_gray": "Triángulu cimeru a manzorga en buxo claro", "block.minecraft.banner.diagonal_left.lime": "Triángulu cimeru a manzorga en llima", "block.minecraft.banner.diagonal_left.magenta": "Triángulu cimeru a manzorga en maxenta", "block.minecraft.banner.diagonal_left.orange": "Triángulu cimeru a manzorga en naranxa", "block.minecraft.banner.diagonal_left.pink": "Triángulu cimeru a manzorga en rosa", "block.minecraft.banner.diagonal_left.purple": "Triángulu cimeru a manzorga en morao", "block.minecraft.banner.diagonal_left.red": "Triángulu cimeru a manzorga en colorao", "block.minecraft.banner.diagonal_left.white": "Triángulu cimeru a manzorga en blanco", "block.minecraft.banner.diagonal_left.yellow": "Triángulu cimeru a manzorga en mariello", "block.minecraft.banner.diagonal_right.black": "Triángulu cimeru a mandrecha en prieto", "block.minecraft.banner.diagonal_right.blue": "Triángulu cimeru a mandrecha n'azul", "block.minecraft.banner.diagonal_right.brown": "Triángulu cimeru a mandrecha en marrón", "block.minecraft.banner.diagonal_right.cyan": "Triángulu cimeru a mandrecha en ciano", "block.minecraft.banner.diagonal_right.gray": "Trián<PERSON><PERSON> cimeru a mandrecha en buxo", "block.minecraft.banner.diagonal_right.green": "Trián<PERSON><PERSON> cimeru a mandrecha en verde", "block.minecraft.banner.diagonal_right.light_blue": "Triángulu cimeru a mandrecha n'azul claro", "block.minecraft.banner.diagonal_right.light_gray": "Trián<PERSON><PERSON> cimeru a mandrecha en buxo claro", "block.minecraft.banner.diagonal_right.lime": "Triángulu cimeru a mandrecha en llima", "block.minecraft.banner.diagonal_right.magenta": "Trián<PERSON><PERSON> cimeru a mandrecha en maxenta", "block.minecraft.banner.diagonal_right.orange": "Triángulu cimeru a mandrecha en naranxa", "block.minecraft.banner.diagonal_right.pink": "Trián<PERSON><PERSON> cimeru a mandrecha en rosa", "block.minecraft.banner.diagonal_right.purple": "Triángulu cimeru a mandrecha en morao", "block.minecraft.banner.diagonal_right.red": "Triángulu cimeru a mandrecha en colorao", "block.minecraft.banner.diagonal_right.white": "Triángulu cimeru a mandrecha en blanco", "block.minecraft.banner.diagonal_right.yellow": "Trián<PERSON><PERSON> cimeru a mandrecha en mariello", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en prieto", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha n'azul", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON>án<PERSON><PERSON> baxeru a mandrecha en marrón", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en ciano", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en buxo", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en verde", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha n'azul claro", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON>án<PERSON><PERSON> baxeru a mandrecha en buxo claro", "block.minecraft.banner.diagonal_up_left.lime": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en llima", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en maxenta", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en naranxa", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en rosa", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en morao", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en colorao", "block.minecraft.banner.diagonal_up_left.white": "Trián<PERSON><PERSON> baxeru a mandrecha en blanco", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en mariello", "block.minecraft.banner.diagonal_up_right.black": "Triángulu baxeru a manzorga en prieto", "block.minecraft.banner.diagonal_up_right.blue": "Triángulu baxeru a manzorga n'azul", "block.minecraft.banner.diagonal_up_right.brown": "Triángulu baxeru a manzorga en marrón", "block.minecraft.banner.diagonal_up_right.cyan": "Triángulu baxeru a manzorga en ciano", "block.minecraft.banner.diagonal_up_right.gray": "Triángulu baxeru a manzorga en buxo", "block.minecraft.banner.diagonal_up_right.green": "Triángulu baxeru a manzorga en verde", "block.minecraft.banner.diagonal_up_right.light_blue": "Triángulu baxeru a manzorga n'azul claro", "block.minecraft.banner.diagonal_up_right.light_gray": "Triángulu baxeru a manzorga en buxo claro", "block.minecraft.banner.diagonal_up_right.lime": "Triángulu baxeru a manzorga en llima", "block.minecraft.banner.diagonal_up_right.magenta": "Triángulu baxeru a manzorga en maxenta", "block.minecraft.banner.diagonal_up_right.orange": "Triángulu baxeru a manzorga en naranxa", "block.minecraft.banner.diagonal_up_right.pink": "Triángulu baxeru a manzorga en rosa", "block.minecraft.banner.diagonal_up_right.purple": "Triángulu baxeru a manzorga en morao", "block.minecraft.banner.diagonal_up_right.red": "Triángulu baxeru a manzorga en colorao", "block.minecraft.banner.diagonal_up_right.white": "Triángulu baxeru a manzorga en blanco", "block.minecraft.banner.diagonal_up_right.yellow": "Triángulu baxeru a manzorga en mariello", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> negru", "block.minecraft.banner.flow.blue": "Fluyíu a<PERSON>l", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.banner.flow.green": "<PERSON>luy<PERSON><PERSON> verde", "block.minecraft.banner.flow.light_blue": "Fluyíu azul claro", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON><PERSON>u gris claro", "block.minecraft.banner.flow.lime": "Fluyíu lima", "block.minecraft.banner.flow.magenta": "Fluyíu magenta", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.flow.purple": "Fluyíu <PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.black": "<PERSON>lor prieta", "block.minecraft.banner.flower.blue": "Flor azul", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.flower.cyan": "Flor ciana", "block.minecraft.banner.flower.gray": "Flor buxa", "block.minecraft.banner.flower.green": "Flor verde", "block.minecraft.banner.flower.light_blue": "Flor azul clara", "block.minecraft.banner.flower.light_gray": "Flor buxa clara", "block.minecraft.banner.flower.lime": "<PERSON>lor llima", "block.minecraft.banner.flower.magenta": "<PERSON>lor maxenta", "block.minecraft.banner.flower.orange": "Flor naranxa", "block.minecraft.banner.flower.pink": "Flor rosa", "block.minecraft.banner.flower.purple": "Flor morada", "block.minecraft.banner.flower.red": "Flor colorada", "block.minecraft.banner.flower.white": "Flor blanca", "block.minecraft.banner.flower.yellow": "<PERSON>lor mariella", "block.minecraft.banner.globe.black": "Planeta prietu", "block.minecraft.banner.globe.blue": "Planeta azul", "block.minecraft.banner.globe.brown": "Planeta marrón", "block.minecraft.banner.globe.cyan": "Planeta cianu", "block.minecraft.banner.globe.gray": "Planeta buxu", "block.minecraft.banner.globe.green": "Planeta verde", "block.minecraft.banner.globe.light_blue": "Planeta azul claru", "block.minecraft.banner.globe.light_gray": "Planeta buxa clara", "block.minecraft.banner.globe.lime": "Planeta llima", "block.minecraft.banner.globe.magenta": "Planeta maxenta", "block.minecraft.banner.globe.orange": "Planeta naranxa", "block.minecraft.banner.globe.pink": "Planeta rosa", "block.minecraft.banner.globe.purple": "Planeta moráu", "block.minecraft.banner.globe.red": "Planeta coloráu", "block.minecraft.banner.globe.white": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.globe.yellow": "Planeta mariellu", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON><PERSON> prietu dende lo cimero", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON><PERSON> azul dende lo cimero", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> dende lo cimero", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON> cianu dende lo cimero", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON><PERSON> buxu dende lo cimero", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON> verde dende lo cimero", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON><PERSON> azul claro dende lo cimero", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru dende lo cimero", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON> llima dende lo cimero", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta dende lo cimero", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON>u naranxa dende lo cimero", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON><PERSON> rosa dende lo cimero", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON><PERSON> moráu dende lo cimero", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON><PERSON> color<PERSON>u dende lo cimero", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON> dende lo cimero", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> dende lo cimero", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON><PERSON><PERSON> prietu dende lo baxero", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON><PERSON> azul dende lo baxero", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> dende lo baxero", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON><PERSON> cian dende lo baxero", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON><PERSON> buxu dende lo baxero", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> verde dende lo baxero", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON><PERSON><PERSON> azul claro dende lo baxero", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru dende lo baxero", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON><PERSON> llima dende lo baxero", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta dende lo baxero", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON><PERSON><PERSON> naranxa dende lo baxero", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON><PERSON> rosa dende lo baxero", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON><PERSON> moráu dende lo baxero", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON><PERSON> color<PERSON>u dende lo baxero", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON><PERSON> blancu dende lo baxero", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> dende lo baxero", "block.minecraft.banner.guster.black": "Rafagón negro", "block.minecraft.banner.guster.blue": "Estampáu azul de breeze", "block.minecraft.banner.guster.brown": "Estampáu ma<PERSON>", "block.minecraft.banner.guster.cyan": "Estampáu cian <PERSON>", "block.minecraft.banner.guster.gray": "Estampáu gris de breeze", "block.minecraft.banner.guster.green": "Estampáu verde de breeze", "block.minecraft.banner.guster.light_blue": "Estampáu azul claro de breeze", "block.minecraft.banner.guster.light_gray": "Estampáu gris claro de breeze", "block.minecraft.banner.guster.lime": "Estampáu llima de breeze", "block.minecraft.banner.guster.magenta": "Estampáu magenta de breeze", "block.minecraft.banner.guster.orange": "Estampáu naranxa de breeze", "block.minecraft.banner.guster.pink": "Estampáu rosa de breeze", "block.minecraft.banner.guster.purple": "Estampáu moráu de <PERSON>", "block.minecraft.banner.guster.red": "Estampáu collor<PERSON> de <PERSON>", "block.minecraft.banner.guster.white": "Estampáu blan<PERSON>", "block.minecraft.banner.guster.yellow": "Estampáu ma<PERSON>", "block.minecraft.banner.half_horizontal.black": "Metá cimera en prieto", "block.minecraft.banner.half_horizontal.blue": "Metá cimera n'azul", "block.minecraft.banner.half_horizontal.brown": "Metá cimera en marrón", "block.minecraft.banner.half_horizontal.cyan": "Metá cimera en ciano", "block.minecraft.banner.half_horizontal.gray": "Metá cimera en buxo", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON> cimera en verde", "block.minecraft.banner.half_horizontal.light_blue": "Metá cimera n'azul claro", "block.minecraft.banner.half_horizontal.light_gray": "Metá cimera en buxo claro", "block.minecraft.banner.half_horizontal.lime": "Metá cimera en llima", "block.minecraft.banner.half_horizontal.magenta": "Met<PERSON> cimera en maxenta", "block.minecraft.banner.half_horizontal.orange": "Metá cimera en naranxa", "block.minecraft.banner.half_horizontal.pink": "Metá cimera en rosa", "block.minecraft.banner.half_horizontal.purple": "Metá cimera en moráu", "block.minecraft.banner.half_horizontal.red": "Metá cimera en colorao", "block.minecraft.banner.half_horizontal.white": "Metá cimera en blanco", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON> cimera en mariello", "block.minecraft.banner.half_horizontal_bottom.black": "Metá baxera en prieto", "block.minecraft.banner.half_horizontal_bottom.blue": "Metá baxera n'azul", "block.minecraft.banner.half_horizontal_bottom.brown": "Metá baxera en marrón", "block.minecraft.banner.half_horizontal_bottom.cyan": "Metá baxera en ciano", "block.minecraft.banner.half_horizontal_bottom.gray": "Metá baxera en buxo", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON> baxera en verde", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Metá baxera n'azul claro", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Metá baxera en buxo claro", "block.minecraft.banner.half_horizontal_bottom.lime": "Metá baxera en llima", "block.minecraft.banner.half_horizontal_bottom.magenta": "Metá baxera en maxenta", "block.minecraft.banner.half_horizontal_bottom.orange": "Metá baxera en naranxa", "block.minecraft.banner.half_horizontal_bottom.pink": "Metá baxera en rosa", "block.minecraft.banner.half_horizontal_bottom.purple": "Metá baxera en morao", "block.minecraft.banner.half_horizontal_bottom.red": "Metá baxera en colorao", "block.minecraft.banner.half_horizontal_bottom.white": "Metá baxera en blanco", "block.minecraft.banner.half_horizontal_bottom.yellow": "Metá baxera en mariello", "block.minecraft.banner.half_vertical.black": "Metá a manzorga en prieto", "block.minecraft.banner.half_vertical.blue": "Metá a manzorga n'azul", "block.minecraft.banner.half_vertical.brown": "Metá a manzorga en marrón", "block.minecraft.banner.half_vertical.cyan": "Metá a manzorga en ciano", "block.minecraft.banner.half_vertical.gray": "Metá a manzorga en buxo", "block.minecraft.banner.half_vertical.green": "Metá a manzorga en verde", "block.minecraft.banner.half_vertical.light_blue": "Metá a manzorga n'azul claro", "block.minecraft.banner.half_vertical.light_gray": "Metá a manzorga en buxu claro", "block.minecraft.banner.half_vertical.lime": "Metá a manzorga en llima", "block.minecraft.banner.half_vertical.magenta": "Metá a manzorga en maxenta", "block.minecraft.banner.half_vertical.orange": "Metá a manzorga en naranxa", "block.minecraft.banner.half_vertical.pink": "Metá a manzorga en rosa", "block.minecraft.banner.half_vertical.purple": "Metá a manzorga en moráu", "block.minecraft.banner.half_vertical.red": "Metá a manzorga en colorao", "block.minecraft.banner.half_vertical.white": "Metá a manzorga en blanco", "block.minecraft.banner.half_vertical.yellow": "Metá a manzorga en mariello", "block.minecraft.banner.half_vertical_right.black": "Met<PERSON> a mandrecha en prieto", "block.minecraft.banner.half_vertical_right.blue": "Met<PERSON> a mandrecha n'azul", "block.minecraft.banner.half_vertical_right.brown": "Met<PERSON> a mandrecha en marrón", "block.minecraft.banner.half_vertical_right.cyan": "Metá a mandrecha en ciano", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON> a mandrecha en buxo", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON> a mandrecha en verde", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON> a mandrecha n'azul claro", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON> a mandrecha en buxo claro", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON> a mandrecha en llima", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON> a mandrecha en maxenta", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON> a mandrecha en naranxa", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON> a mandrecha en rosa", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> a mandrecha en morao", "block.minecraft.banner.half_vertical_right.red": "Met<PERSON> a mandrecha en colorao", "block.minecraft.banner.half_vertical_right.white": "Metá a mandrecha en blanco", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> a mandrecha en mariello", "block.minecraft.banner.mojang.black": "Llogotipu prietu", "block.minecraft.banner.mojang.blue": "Llogotipu azul", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Llogotip<PERSON> cianu", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.mojang.green": "Llogotipu verde", "block.minecraft.banner.mojang.light_blue": "Llogotipu azul claro", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> buxu claro", "block.minecraft.banner.mojang.lime": "L<PERSON><PERSON><PERSON><PERSON> llima", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.mojang.orange": "Llogotipu naranxa", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.mojang.purple": "Llogotip<PERSON> mor<PERSON>u", "block.minecraft.banner.mojang.red": "Llogotipu coloráu", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.black": "Focicu prietu", "block.minecraft.banner.piglin.blue": "Focicu azul", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON><PERSON> cianu", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON>u buxu", "block.minecraft.banner.piglin.green": "Focicu verde", "block.minecraft.banner.piglin.light_blue": "Focicu azul claru", "block.minecraft.banner.piglin.light_gray": "Focicu gris claru", "block.minecraft.banner.piglin.lime": "<PERSON>oc<PERSON><PERSON> llima", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.piglin.orange": "Focicu naranxa", "block.minecraft.banner.piglin.pink": "Focic<PERSON> rosa", "block.minecraft.banner.piglin.purple": "Focicu mor<PERSON>u", "block.minecraft.banner.piglin.red": "Focicu coloráu", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON> prietu", "block.minecraft.banner.rhombus.blue": "R<PERSON><PERSON> azul", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> cianu", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.rhombus.green": "Rombu verde", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON> azul claru", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON>u buxu claro", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON> llima", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON> naranxa", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON> mor<PERSON>u", "block.minecraft.banner.rhombus.red": "Romb<PERSON> coloráu", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.skull.black": "<PERSON>rani<PERSON> p<PERSON>tu", "block.minecraft.banner.skull.blue": "Craniu azul", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON> cianu", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.skull.green": "<PERSON><PERSON>u verde", "block.minecraft.banner.skull.light_blue": "<PERSON>raniu azul claru", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON> buxu claru", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON> llima", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.skull.orange": "Craniu naranxa", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.banner.skull.red": "Craniu <PERSON>á<PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON> con faces en prieto", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON> con faces n'azul", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> con faces en marrón", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON> con faces en ciano", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON> con faces en buxo", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON> con faces en verde", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON> con faces n'azul claro", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON> con faces en buxo claro", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON> con faces en llima", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON> con faces en maxenta", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> con faces en naranxa", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON> con faces en rosa", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON> con faces en morao", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON> con faces en colorao", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> con faces en blanco", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON> con faces en mariello", "block.minecraft.banner.square_bottom_left.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en prieto", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha n'azul", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en marrón", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en ciano", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en buxo", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en verde", "block.minecraft.banner.square_bottom_left.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha n'azul claro", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en buxo claro", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en llima", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en maxenta", "block.minecraft.banner.square_bottom_left.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en naranxa", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en rosa", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en morao", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en colorao", "block.minecraft.banner.square_bottom_left.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en blanco", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a mandrecha en mariello", "block.minecraft.banner.square_bottom_right.black": "C<PERSON>r<PERSON>u baxeru a manzorga en prieto", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON>r<PERSON><PERSON> baxeru a manzorga n'azul", "block.minecraft.banner.square_bottom_right.brown": "C<PERSON>r<PERSON>u baxeru a manzorga en marrón", "block.minecraft.banner.square_bottom_right.cyan": "Cuadráu baxeru a manzorga en ciano", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON>r<PERSON>u baxeru a manzorga en buxo", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a manzorga en verde", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a manzorga n'azul claro", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON>r<PERSON><PERSON> baxeru a manzorga en buxo claro", "block.minecraft.banner.square_bottom_right.lime": "C<PERSON>r<PERSON>u baxeru a manzorga en llima", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a manzorga en maxenta", "block.minecraft.banner.square_bottom_right.orange": "C<PERSON>r<PERSON>u baxeru a manzorga en naranxa", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON>r<PERSON><PERSON> baxeru a manzorga en rosa", "block.minecraft.banner.square_bottom_right.purple": "C<PERSON>r<PERSON>u baxeru a manzorga en morao", "block.minecraft.banner.square_bottom_right.red": "C<PERSON>r<PERSON><PERSON> baxeru a manzorga en colorao", "block.minecraft.banner.square_bottom_right.white": "Cuadráu baxeru a manzorga en blanco", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> baxeru a manzorga en mariello", "block.minecraft.banner.square_top_left.black": "C<PERSON>r<PERSON><PERSON> cimeru a mandrecha en prieto", "block.minecraft.banner.square_top_left.blue": "C<PERSON>r<PERSON><PERSON> cimeru a mandrecha n'azul", "block.minecraft.banner.square_top_left.brown": "Cuadr<PERSON>u cimeru a mandrecha en marrón", "block.minecraft.banner.square_top_left.cyan": "Cuadráu cimeru a mandrecha en ciano", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> cimeru a mandrecha en buxo", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> cimeru a mandrecha en verde", "block.minecraft.banner.square_top_left.light_blue": "C<PERSON>r<PERSON><PERSON> cimeru a mandrecha n'azul claro", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON>r<PERSON><PERSON> cimeru a mandrecha en buxo claro", "block.minecraft.banner.square_top_left.lime": "C<PERSON>r<PERSON>u cimeru a mandrecha en llima", "block.minecraft.banner.square_top_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> cimeru a mandrecha en maxenta", "block.minecraft.banner.square_top_left.orange": "C<PERSON>r<PERSON>u cimeru a mandrecha en naranxa", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON>r<PERSON><PERSON> cimeru a mandrecha en rosa", "block.minecraft.banner.square_top_left.purple": "C<PERSON>r<PERSON><PERSON> cimeru a mandrecha en morao", "block.minecraft.banner.square_top_left.red": "C<PERSON>r<PERSON><PERSON> cimeru a mandrecha en colorao", "block.minecraft.banner.square_top_left.white": "C<PERSON>r<PERSON>u cimeru a mandrecha en blanco", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> cimeru a mandrecha en mariello", "block.minecraft.banner.square_top_right.black": "Cuadráu cimeru a manzorga en prieto", "block.minecraft.banner.square_top_right.blue": "Cuadráu cimeru a manzorga n'azul", "block.minecraft.banner.square_top_right.brown": "Cuadráu cimeru a manzorga en marrón", "block.minecraft.banner.square_top_right.cyan": "Cuadráu cimeru a manzorga en ciano", "block.minecraft.banner.square_top_right.gray": "Cuadráu cimeru a manzorga en buxo", "block.minecraft.banner.square_top_right.green": "Cuadráu cimeru a manzorga en verde", "block.minecraft.banner.square_top_right.light_blue": "Cuadráu cimeru a manzorga n'azul claro", "block.minecraft.banner.square_top_right.light_gray": "Cuadráu cimeru a manzorga en buxo claro", "block.minecraft.banner.square_top_right.lime": "Cuadráu cimeru a manzorga en llima", "block.minecraft.banner.square_top_right.magenta": "Cuadráu cimeru a manzorga en maxenta", "block.minecraft.banner.square_top_right.orange": "Cuadráu cimeru a manzorga en naranxa", "block.minecraft.banner.square_top_right.pink": "Cuadráu cimeru a manzorga en rosa", "block.minecraft.banner.square_top_right.purple": "Cuadráu cimeru a manzorga en morao", "block.minecraft.banner.square_top_right.red": "Cuadráu cimeru a manzorga en colorao", "block.minecraft.banner.square_top_right.white": "Cuadráu cimeru a manzorga en blanco", "block.minecraft.banner.square_top_right.yellow": "Cuadráu cimeru a manzorga en mariello", "block.minecraft.banner.straight_cross.black": "Cruz prieta", "block.minecraft.banner.straight_cross.blue": "Cruz azul", "block.minecraft.banner.straight_cross.brown": "<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Cruz ciana", "block.minecraft.banner.straight_cross.gray": "<PERSON> buxa", "block.minecraft.banner.straight_cross.green": "Cruz verde", "block.minecraft.banner.straight_cross.light_blue": "Cruz azul clara", "block.minecraft.banner.straight_cross.light_gray": "<PERSON> buxa claro", "block.minecraft.banner.straight_cross.lime": "<PERSON> ll<PERSON>", "block.minecraft.banner.straight_cross.magenta": "<PERSON> maxenta", "block.minecraft.banner.straight_cross.orange": "Cruz naranxa", "block.minecraft.banner.straight_cross.pink": "<PERSON> rosa", "block.minecraft.banner.straight_cross.purple": "Cruz morada", "block.minecraft.banner.straight_cross.red": "Cruz colorada", "block.minecraft.banner.straight_cross.white": "Cruz blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON> mariella", "block.minecraft.banner.stripe_bottom.black": "Base prieta", "block.minecraft.banner.stripe_bottom.blue": "Base azul", "block.minecraft.banner.stripe_bottom.brown": "Base marrón", "block.minecraft.banner.stripe_bottom.cyan": "Base ciana", "block.minecraft.banner.stripe_bottom.gray": "Base buxa", "block.minecraft.banner.stripe_bottom.green": "Base verde", "block.minecraft.banner.stripe_bottom.light_blue": "Base azul claro", "block.minecraft.banner.stripe_bottom.light_gray": "Base buxa claro", "block.minecraft.banner.stripe_bottom.lime": "Base llima", "block.minecraft.banner.stripe_bottom.magenta": "Base maxenta", "block.minecraft.banner.stripe_bottom.orange": "Base naranxa", "block.minecraft.banner.stripe_bottom.pink": "Base rosa", "block.minecraft.banner.stripe_bottom.purple": "Base morada", "block.minecraft.banner.stripe_bottom.red": "Base colorada", "block.minecraft.banner.stripe_bottom.white": "Base blanca", "block.minecraft.banner.stripe_bottom.yellow": "Base mariella", "block.minecraft.banner.stripe_center.black": "Faza prieta central", "block.minecraft.banner.stripe_center.blue": "Faza azul central", "block.minecraft.banner.stripe_center.brown": "Faza marrón central", "block.minecraft.banner.stripe_center.cyan": "Faza ciana central", "block.minecraft.banner.stripe_center.gray": "Faza buxa central", "block.minecraft.banner.stripe_center.green": "Faza verde central", "block.minecraft.banner.stripe_center.light_blue": "Faza azul clara central", "block.minecraft.banner.stripe_center.light_gray": "Faza buxa clara central", "block.minecraft.banner.stripe_center.lime": "Faza llima central", "block.minecraft.banner.stripe_center.magenta": "Faza maxenta central", "block.minecraft.banner.stripe_center.orange": "Faza naranxa central", "block.minecraft.banner.stripe_center.pink": "Faza rosa central", "block.minecraft.banner.stripe_center.purple": "Faza morada central", "block.minecraft.banner.stripe_center.red": "Faza colorada central", "block.minecraft.banner.stripe_center.white": "Faza blanca central", "block.minecraft.banner.stripe_center.yellow": "Faza mariella central", "block.minecraft.banner.stripe_downleft.black": "Llinia cruciada a manzorga en prieto", "block.minecraft.banner.stripe_downleft.blue": "Llinia cruciada a manzorga n'azul", "block.minecraft.banner.stripe_downleft.brown": "Llinia cruciada a manzorga en marrón", "block.minecraft.banner.stripe_downleft.cyan": "Llinia cruciada a manzorga en ciano", "block.minecraft.banner.stripe_downleft.gray": "Llinia cruciada a manzorga en buxo", "block.minecraft.banner.stripe_downleft.green": "Llinia cruciada a manzorga en verde", "block.minecraft.banner.stripe_downleft.light_blue": "Llinia cruciada a manzorga n'azul claro", "block.minecraft.banner.stripe_downleft.light_gray": "Llinia cruciada a manzorga en buxo claro", "block.minecraft.banner.stripe_downleft.lime": "Llinia cruciada a manzorga en llima", "block.minecraft.banner.stripe_downleft.magenta": "Llinia cruciada a manzorga en maxenta", "block.minecraft.banner.stripe_downleft.orange": "Llinia cruciada a manzorga en naranxa", "block.minecraft.banner.stripe_downleft.pink": "Llinia cruciada a manzorga en rosa", "block.minecraft.banner.stripe_downleft.purple": "Llinia cruciada a manzorga en morao", "block.minecraft.banner.stripe_downleft.red": "Llinia cruciada a manzorga en colorao", "block.minecraft.banner.stripe_downleft.white": "Llinia cruciada a manzorga en blanco", "block.minecraft.banner.stripe_downleft.yellow": "Llinia cruciada a manzorga en mariello", "block.minecraft.banner.stripe_downright.black": "Llinia cruciada en prieto", "block.minecraft.banner.stripe_downright.blue": "Llinia cruciada n'azul", "block.minecraft.banner.stripe_downright.brown": "Llinia cruciada en marrón", "block.minecraft.banner.stripe_downright.cyan": "Llinia cruciada en ciano", "block.minecraft.banner.stripe_downright.gray": "Llinia cruciada en buxo", "block.minecraft.banner.stripe_downright.green": "Llinia cruciada en verde", "block.minecraft.banner.stripe_downright.light_blue": "Llinia cruciada n'azul claro", "block.minecraft.banner.stripe_downright.light_gray": "Llinia cruciada en buxo claro", "block.minecraft.banner.stripe_downright.lime": "Llinia cruciada en llima", "block.minecraft.banner.stripe_downright.magenta": "Llinia cruciada en maxenta", "block.minecraft.banner.stripe_downright.orange": "Llinia cruciada en naranxa", "block.minecraft.banner.stripe_downright.pink": "Llinia cruciada en rosa", "block.minecraft.banner.stripe_downright.purple": "Llinia cruciada en morao", "block.minecraft.banner.stripe_downright.red": "Llinia cruciada en colorao", "block.minecraft.banner.stripe_downright.white": "Llinia cruciada en blanco", "block.minecraft.banner.stripe_downright.yellow": "Llinia cruciada en mariello", "block.minecraft.banner.stripe_left.black": "Faza prieta a mandrecha", "block.minecraft.banner.stripe_left.blue": "Faza azul a mandrecha", "block.minecraft.banner.stripe_left.brown": "<PERSON>aza marrón a mandrecha", "block.minecraft.banner.stripe_left.cyan": "Faza ciana a mandrecha", "block.minecraft.banner.stripe_left.gray": "<PERSON>aza buxa a mandrecha", "block.minecraft.banner.stripe_left.green": "Faza verde a mandrecha", "block.minecraft.banner.stripe_left.light_blue": "<PERSON>aza azul clara a mandrecha", "block.minecraft.banner.stripe_left.light_gray": "<PERSON>aza buxa clara a mandrecha", "block.minecraft.banner.stripe_left.lime": "Faza llima a mandrecha", "block.minecraft.banner.stripe_left.magenta": "<PERSON>aza maxenta a mandrecha", "block.minecraft.banner.stripe_left.orange": "Faza naranxa a mandrecha", "block.minecraft.banner.stripe_left.pink": "Faza rosa a mandrecha", "block.minecraft.banner.stripe_left.purple": "Faza morada a mandrecha", "block.minecraft.banner.stripe_left.red": "Faza colorada a mandrecha", "block.minecraft.banner.stripe_left.white": "Faza blanca a mandrecha", "block.minecraft.banner.stripe_left.yellow": "<PERSON>aza mariella a mandrecha", "block.minecraft.banner.stripe_middle.black": "Llinia prieta central", "block.minecraft.banner.stripe_middle.blue": "Llinia azul central", "block.minecraft.banner.stripe_middle.brown": "Llinia ma<PERSON>ón central", "block.minecraft.banner.stripe_middle.cyan": "Llinia ciana central", "block.minecraft.banner.stripe_middle.gray": "Llinia buxa central", "block.minecraft.banner.stripe_middle.green": "Llinia verde central", "block.minecraft.banner.stripe_middle.light_blue": "Llinia azul clara central", "block.minecraft.banner.stripe_middle.light_gray": "Llinia buxa clara central", "block.minecraft.banner.stripe_middle.lime": "Llinia llima central", "block.minecraft.banner.stripe_middle.magenta": "Llinia maxenta central", "block.minecraft.banner.stripe_middle.orange": "Llinia naranxa central", "block.minecraft.banner.stripe_middle.pink": "Llinia rosa central", "block.minecraft.banner.stripe_middle.purple": "Llinia morada central", "block.minecraft.banner.stripe_middle.red": "Llinia colorada central", "block.minecraft.banner.stripe_middle.white": "Llinia blanca central", "block.minecraft.banner.stripe_middle.yellow": "Llinia mariella central", "block.minecraft.banner.stripe_right.black": "Faza prieta a manzorga", "block.minecraft.banner.stripe_right.blue": "Faza azul a manzorga", "block.minecraft.banner.stripe_right.brown": "Faza marrón a manzorga", "block.minecraft.banner.stripe_right.cyan": "Faza ciana a manzorga", "block.minecraft.banner.stripe_right.gray": "Faza buxa a manzorga", "block.minecraft.banner.stripe_right.green": "Faza verde a manzorga", "block.minecraft.banner.stripe_right.light_blue": "Faza azul clara a manzorga", "block.minecraft.banner.stripe_right.light_gray": "Faza buxa clara a manzorga", "block.minecraft.banner.stripe_right.lime": "Faza llima a manzorga", "block.minecraft.banner.stripe_right.magenta": "Faza maxenta a manzorga", "block.minecraft.banner.stripe_right.orange": "Faza naranxa a manzorga", "block.minecraft.banner.stripe_right.pink": "Faza rosa a manzorga", "block.minecraft.banner.stripe_right.purple": "Faza morada a manzorga", "block.minecraft.banner.stripe_right.red": "Faza colorada a manzorga", "block.minecraft.banner.stripe_right.white": "Faza blanca a manzorga", "block.minecraft.banner.stripe_right.yellow": "Faza mariella a manzorga", "block.minecraft.banner.stripe_top.black": "Dent<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_top.blue": "Dentáu a<PERSON>l", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.stripe_top.green": "<PERSON>t<PERSON><PERSON> verde", "block.minecraft.banner.stripe_top.light_blue": "Dentáu azul claro", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claro", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON> llima", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.stripe_top.orange": "Dent<PERSON><PERSON> na<PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON><PERSON><PERSON> mor<PERSON>u", "block.minecraft.banner.stripe_top.red": "Dentáu <PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "Chebrón prietu", "block.minecraft.banner.triangle_bottom.blue": "Chebrón azul", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Ch<PERSON><PERSON><PERSON> cianu", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON><PERSON> buxu", "block.minecraft.banner.triangle_bottom.green": "Chebrón verde", "block.minecraft.banner.triangle_bottom.light_blue": "Chebrón azul claru", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru", "block.minecraft.banner.triangle_bottom.lime": "Chebr<PERSON> llima", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.triangle_bottom.orange": "Chebrón naranxa", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.triangle_bottom.purple": "Ch<PERSON><PERSON><PERSON> mor<PERSON>u", "block.minecraft.banner.triangle_bottom.red": "Chebrón coloráu", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>lu", "block.minecraft.banner.triangle_top.black": "Chebrón prietu cimeru", "block.minecraft.banner.triangle_top.blue": "Chebrón azul cimeru", "block.minecraft.banner.triangle_top.brown": "Chebrón marrón cimeru", "block.minecraft.banner.triangle_top.cyan": "Chebrón cian cimeru", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON><PERSON><PERSON> buxu cimeru", "block.minecraft.banner.triangle_top.green": "Chebrón verde cimeru", "block.minecraft.banner.triangle_top.light_blue": "Chebrón azul claru cimeru", "block.minecraft.banner.triangle_top.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru cimeru", "block.minecraft.banner.triangle_top.lime": "Chebrón llima cimeru", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta cimeru", "block.minecraft.banner.triangle_top.orange": "Chebrón naranxa cimeru", "block.minecraft.banner.triangle_top.pink": "Chebrón rosa cimeru", "block.minecraft.banner.triangle_top.purple": "Chebrón moráu cimeru", "block.minecraft.banner.triangle_top.red": "Chebrón coloráu cimeru", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON> cimeru", "block.minecraft.banner.triangle_top.yellow": "Chebrón mariel<PERSON> cimeru", "block.minecraft.banner.triangles_bottom.black": "<PERSON>t<PERSON><PERSON> prietu baxeru", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON><PERSON> azul baxeru", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> baxeru", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON> cian baxeru", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON><PERSON> buxu baxeru", "block.minecraft.banner.triangles_bottom.green": "Dent<PERSON><PERSON> verde baxeru", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON> azul claru baxeru", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru baxeru", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON><PERSON> llima baxeru", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta baxeru", "block.minecraft.banner.triangles_bottom.orange": "Dentáu naranxa baxeru", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON><PERSON> rosa baxeru", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON><PERSON><PERSON> mor<PERSON>u baxeru", "block.minecraft.banner.triangles_bottom.red": "<PERSON>t<PERSON><PERSON> color<PERSON> baxeru", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON> baxeru", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> baxeru", "block.minecraft.banner.triangles_top.black": "Dentáu prietu cimeru", "block.minecraft.banner.triangles_top.blue": "Dentáu azul cimeru", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> cimeru", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON><PERSON> cian cimeru", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON><PERSON> buxu cimeru", "block.minecraft.banner.triangles_top.green": "Dentáu verde cimeru", "block.minecraft.banner.triangles_top.light_blue": "Dentáu azul claru cimeru", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claru cimeru", "block.minecraft.banner.triangles_top.lime": "Dentáu llima cimeru", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta cimeru", "block.minecraft.banner.triangles_top.orange": "Dentáu naranxa cimeru", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON><PERSON> rosa cimeru", "block.minecraft.banner.triangles_top.purple": "<PERSON>t<PERSON><PERSON> moráu cimeru", "block.minecraft.banner.triangles_top.red": "Dentáu coloráu cimeru", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON> c<PERSON>", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> cimeru", "block.minecraft.barrel": "Barril", "block.minecraft.barrier": "Barr<PERSON>", "block.minecraft.basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon": "Baliza", "block.minecraft.beacon.primary": "<PERSON><PERSON> primariu", "block.minecraft.beacon.secondary": "<PERSON><PERSON> secunda<PERSON>u", "block.minecraft.bed.no_sleep": "Namás pues dormir pela nueche o demientres tempestaes llétriques", "block.minecraft.bed.not_safe": "<PERSON><PERSON>a nun pues descansar, hai monstruos cierca", "block.minecraft.bed.obstructed": "La cama ta torgada", "block.minecraft.bed.occupied": "Esta cama ta ocupada", "block.minecraft.bed.too_far_away": "<PERSON><PERSON>a nun pues descansar, la cama ta perlloñe", "block.minecraft.bedrock": "Piedra base", "block.minecraft.bee_nest": "<PERSON><PERSON> d'abeyes", "block.minecraft.beehive": "Colmena", "block.minecraft.beetroots": "Remolaches", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Plantaforma grande", "block.minecraft.big_dripleaf_stem": "Tallu de plantaforma grande", "block.minecraft.birch_button": "Botón de bidul", "block.minecraft.birch_door": "Puerta de bidul", "block.minecraft.birch_fence": "Valla de bidul", "block.minecraft.birch_fence_gate": "Portiella de bidul", "block.minecraft.birch_hanging_sign": "Cartel сolgante de abedul", "block.minecraft.birch_leaves": "Fueyes de bidul", "block.minecraft.birch_log": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.birch_planks": "Tables de bidul", "block.minecraft.birch_pressure_plate": "Placa de presión de bidul", "block.minecraft.birch_sapling": "Biltu de bidul", "block.minecraft.birch_sign": "Cartelu de bidul", "block.minecraft.birch_slab": "Llábana de bidul", "block.minecraft.birch_stairs": "Pasales de bidul", "block.minecraft.birch_trapdoor": "Carbayu de bidul", "block.minecraft.birch_wall_hanging_sign": "Cartel de pared colgante de abedul", "block.minecraft.birch_wall_sign": "Cartelu de bidul na parede", "block.minecraft.birch_wood": "Madera de bidul con corteza", "block.minecraft.black_banner": "Estandarte prietu", "block.minecraft.black_bed": "Cama prieta", "block.minecraft.black_candle": "<PERSON><PERSON> prieta", "block.minecraft.black_candle_cake": "Tarta con una vela prieta", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON> prieta", "block.minecraft.black_concrete": "Formigón prieto", "block.minecraft.black_concrete_powder": "Povisa de formigón prieto", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>tu", "block.minecraft.black_shulker_box": "Caxa prieta de <PERSON>ker", "block.minecraft.black_stained_glass": "Vid<PERSON> tintao en prieto", "block.minecraft.black_stained_glass_pane": "Panel de vidru tintao en prieto", "block.minecraft.black_terracotta": "Terracota prieto", "block.minecraft.black_wool": "<PERSON><PERSON>", "block.minecraft.blackstone": "Nig<PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Llábana de nigrilitu", "block.minecraft.blackstone_stairs": "Pasales de nigrilitu", "block.minecraft.blackstone_wall": "<PERSON><PERSON> de nigrilitu", "block.minecraft.blast_furnace": "<PERSON><PERSON> fornu", "block.minecraft.blue_banner": "Estandarte azul", "block.minecraft.blue_bed": "Cama a<PERSON>l", "block.minecraft.blue_candle": "<PERSON><PERSON> azul", "block.minecraft.blue_candle_cake": "Tarta con una vela azul", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.blue_concrete": "Formigón azul", "block.minecraft.blue_concrete_powder": "Povisa de formigón azul", "block.minecraft.blue_glazed_terracotta": "<PERSON>zu<PERSON>u azul", "block.minecraft.blue_ice": "<PERSON>elu", "block.minecraft.blue_orchid": "Orquídea a<PERSON>l", "block.minecraft.blue_shulker_box": "Caxa a<PERSON>", "block.minecraft.blue_stained_glass": "V<PERSON><PERSON> tintao n'azul", "block.minecraft.blue_stained_glass_pane": "Panel de vidru tintao n'azul", "block.minecraft.blue_terracotta": "Terracota azul", "block.minecraft.blue_wool": "<PERSON><PERSON>", "block.minecraft.bone_block": "Bloque o<PERSON>", "block.minecraft.bookshelf": "Llibrería", "block.minecraft.brain_coral": "Coral de cerebru", "block.minecraft.brain_coral_block": "Bloque de coral de cerebru", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cere<PERSON> de parede", "block.minecraft.brewing_stand": "Destilador", "block.minecraft.brick_slab": "Llábana de lladriyos", "block.minecraft.brick_stairs": "Pasales de lladriyos", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Tarta con una vela marrón", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "Formigón marrón", "block.minecraft.brown_concrete_powder": "Povisa de formigón marrón", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom": "Xampiñón", "block.minecraft.brown_mushroom_block": "Bloque de fungos marrones", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "V<PERSON><PERSON> tintao en marrón", "block.minecraft.brown_stained_glass_pane": "Panel de vidru tintao en marrón", "block.minecraft.brown_terracotta": "Terracota marrón", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Columna de <PERSON>", "block.minecraft.bubble_coral": "Coral de b<PERSON>boyos", "block.minecraft.bubble_coral_block": "Bloque de coral de borboyos", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> bor<PERSON> de parede", "block.minecraft.budding_amethyst": "Amatista naciente", "block.minecraft.bush": "Parrotal", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de cactus", "block.minecraft.cake": "Tarta", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor sculk calibráu", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Vela", "block.minecraft.candle_cake": "Tarta con una vela", "block.minecraft.carrots": "Cenahories", "block.minecraft.cartography_table": "Mesa de cartografía", "block.minecraft.carved_pumpkin": "Calabaza tallada", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "Aire de cueva", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON> de cue<PERSON>", "block.minecraft.cave_vines_plant": "Planta de vies de cueva", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloque de comandos en cadena", "block.minecraft.cherry_button": "Botón de cereza", "block.minecraft.cherry_door": "<PERSON>uer<PERSON> de cerezo", "block.minecraft.cherry_fence": "Valla de cerezu", "block.minecraft.cherry_fence_gate": "Portón de cerezu", "block.minecraft.cherry_hanging_sign": "Cartel colgante de cerezo", "block.minecraft.cherry_leaves": "Fueyes de cerezu", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "Tablones de cerezu", "block.minecraft.cherry_pressure_plate": "Placa de presión de cerezu", "block.minecraft.cherry_sapling": "Renovellu de cerezu", "block.minecraft.cherry_sign": "Señal de cerezu", "block.minecraft.cherry_slab": "Tabla de cerezu", "block.minecraft.cherry_stairs": "Escaleres de cerezu", "block.minecraft.cherry_trapdoor": "Escaleres de cerezu", "block.minecraft.cherry_wall_hanging_sign": "Señal de parede colgante de cerezu", "block.minecraft.cherry_wall_sign": "Señal de parede de cerezu", "block.minecraft.cherry_wood": "<PERSON><PERSON> c<PERSON>zu", "block.minecraft.chest": "Bagul", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON> estella<PERSON>", "block.minecraft.chiseled_bookshelf": "Librería trabayada a mano", "block.minecraft.chiseled_copper": "Co<PERSON> talláu", "block.minecraft.chiseled_deepslate": "Pizarra cincelao", "block.minecraft.chiseled_nether_bricks": "Lladriyos del Nether cincelaos", "block.minecraft.chiseled_polished_blackstone": "Nigrilitu pulío ya cincelao", "block.minecraft.chiseled_quartz_block": "Bloque de cuarzu cincelao", "block.minecraft.chiseled_red_sandstone": "Gres colorao cincelao", "block.minecraft.chiseled_resin_bricks": "Lladriyos de resina cincelaos", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON> cincel<PERSON>", "block.minecraft.chiseled_stone_bricks": "Lladriyos de piedra cincelao", "block.minecraft.chiseled_tuff": "<PERSON><PERSON> es<PERSON>", "block.minecraft.chiseled_tuff_bricks": "Lladríos de tufu esculpíos", "block.minecraft.chorus_flower": "Flor del Coru", "block.minecraft.chorus_plant": "Planta del Coru", "block.minecraft.clay": "<PERSON><PERSON> santo", "block.minecraft.closed_eyeblossom": "Miraflor cerrada", "block.minecraft.coal_block": "Bloque de car<PERSON>ón", "block.minecraft.coal_ore": "Veta de carbón", "block.minecraft.coarse_dirt": "Tierra seco", "block.minecraft.cobbled_deepslate": "Llastra de pizarra", "block.minecraft.cobbled_deepslate_slab": "Llábana de llastra de pizarra", "block.minecraft.cobbled_deepslate_stairs": "Pasales de llastra de pizarra", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> de llastra de p<PERSON>rra", "block.minecraft.cobblestone": "Llastra", "block.minecraft.cobblestone_slab": "Llábana de llastra", "block.minecraft.cobblestone_stairs": "Pasales de llastra", "block.minecraft.cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.cobweb": "Telaraña", "block.minecraft.cocoa": "Cacáu", "block.minecraft.command_block": "Bloque de comandos", "block.minecraft.comparator": "Comparador de redstone", "block.minecraft.composter": "Compostador", "block.minecraft.conduit": "Canalizador", "block.minecraft.copper_block": "Bloque de cobre", "block.minecraft.copper_bulb": "Bombía de cobre", "block.minecraft.copper_door": "Puerta de cobre", "block.minecraft.copper_grate": "Reixeta de cobre", "block.minecraft.copper_ore": "Veta de cobre", "block.minecraft.copper_trapdoor": "Reixeta de cobre", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Lladriyos de pizarra frañaos", "block.minecraft.cracked_deepslate_tiles": "Azulexos de pizarra frañaos", "block.minecraft.cracked_nether_bricks": "Lladriyos del Nether frañaos", "block.minecraft.cracked_polished_blackstone_bricks": "Lladriyos de nigrilitu pulío frañaos", "block.minecraft.cracked_stone_bricks": "Lladriyos de piedra frañaos", "block.minecraft.crafter": "Artesán", "block.minecraft.crafting_table": "Mesa d'ellaboración", "block.minecraft.creaking_heart": "Corazón de llarpiador", "block.minecraft.creeper_head": "Tiesta de Creeper", "block.minecraft.creeper_wall_head": "Tiesta de creeper na parede", "block.minecraft.crimson_button": "Bo<PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON> car<PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "<PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "Cartel colgante carmesí", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "Tables carmesines", "block.minecraft.crimson_pressure_plate": "Placa de presión carmesín", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "Llábana carmesina", "block.minecraft.crimson_stairs": "Pasales carmesinos", "block.minecraft.crimson_stem": "<PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Cartel coleandero de muria car<PERSON>ín", "block.minecraft.crimson_wall_sign": "Cartel de muria carmesí", "block.minecraft.crying_obsidian": "O<PERSON><PERSON><PERSON> ll<PERSON>", "block.minecraft.cut_copper": "Cobre cortao", "block.minecraft.cut_copper_slab": "Llábana de cobre cortao", "block.minecraft.cut_copper_stairs": "Pasales de cobre cortao", "block.minecraft.cut_red_sandstone": "Gres colorao cortao", "block.minecraft.cut_red_sandstone_slab": "Llábana de gres colorao cortao", "block.minecraft.cut_sandstone": "<PERSON><PERSON> cortao", "block.minecraft.cut_sandstone_slab": "Llábana de gres cortao", "block.minecraft.cyan_banner": "Estand<PERSON> cianu", "block.minecraft.cyan_bed": "Cama ciana", "block.minecraft.cyan_candle": "Vela ciana", "block.minecraft.cyan_candle_cake": "Tarta con una vela ciana", "block.minecraft.cyan_carpet": "Moqueta ciana", "block.minecraft.cyan_concrete": "Formigón ciano", "block.minecraft.cyan_concrete_powder": "Povisa de formigón ciano", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> cianu", "block.minecraft.cyan_shulker_box": "Caxa c<PERSON>", "block.minecraft.cyan_stained_glass": "Vidru tintao en ciano", "block.minecraft.cyan_stained_glass_pane": "Panel de vidru tintao en ciano", "block.minecraft.cyan_terracotta": "Terracota ciano", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.dandelion": "Xiblata", "block.minecraft.dark_oak_button": "Botón de carbayu escuro", "block.minecraft.dark_oak_door": "Puerta de carbayu escuro", "block.minecraft.dark_oak_fence": "Valla de carbayu escuro", "block.minecraft.dark_oak_fence_gate": "Portiella de carbayu escuro", "block.minecraft.dark_oak_hanging_sign": "Cartel colgante de roble oscuro", "block.minecraft.dark_oak_leaves": "Fueyes de carbayu escuro", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON> <PERSON> car<PERSON>u escuro", "block.minecraft.dark_oak_planks": "Tables de carbayu escuro", "block.minecraft.dark_oak_pressure_plate": "Placa de presión de carbayu escuro", "block.minecraft.dark_oak_sapling": "Biltu de carbayu escuro", "block.minecraft.dark_oak_sign": "Cartelu de carbayu escuro", "block.minecraft.dark_oak_slab": "Llábana de carbayu escuro", "block.minecraft.dark_oak_stairs": "Pasales de carbayu escuro", "block.minecraft.dark_oak_trapdoor": "Trapiella de carbayu escuro", "block.minecraft.dark_oak_wall_hanging_sign": "Cartel colgante de roble oscuro", "block.minecraft.dark_oak_wall_sign": "Cartelu de carbayu escuro na parede", "block.minecraft.dark_oak_wood": "Madera de carbayu escuro con corteza", "block.minecraft.dark_prismarine": "Prismarín escuro", "block.minecraft.dark_prismarine_slab": "Llábana de prismarín escuro", "block.minecraft.dark_prismarine_stairs": "Pasales de prismarín escuro", "block.minecraft.daylight_detector": "Sensor de llume solar", "block.minecraft.dead_brain_coral": "Coral de cerebru muerto", "block.minecraft.dead_brain_coral_block": "Bloque de coral de cerebru muerto", "block.minecraft.dead_brain_coral_fan": "Go<PERSON><PERSON> de cerebru muerto", "block.minecraft.dead_brain_coral_wall_fan": "Gorgonia de cerebros muerto de parede", "block.minecraft.dead_bubble_coral": "Coral de borboyos muerto", "block.minecraft.dead_bubble_coral_block": "Bloque de coral de borboyos muerto", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON> de borboyos muerto", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> de borboyos muerto de parede", "block.minecraft.dead_bush": "<PERSON><PERSON> muertu", "block.minecraft.dead_fire_coral": "Coral de fueu muerto", "block.minecraft.dead_fire_coral_block": "Bloque de coral de fueu muerto", "block.minecraft.dead_fire_coral_fan": "Gorgonia de fueu muerto", "block.minecraft.dead_fire_coral_wall_fan": "Gorgonia de fueu muerto de parede", "block.minecraft.dead_horn_coral": "Coral de cuernos muerto", "block.minecraft.dead_horn_coral_block": "Bloque de coral de cuernos muerto", "block.minecraft.dead_horn_coral_fan": "Gorgonia de cuernos muerto", "block.minecraft.dead_horn_coral_wall_fan": "Gorgonia de cuernos muerto de parede", "block.minecraft.dead_tube_coral": "Coral de tubos muerto", "block.minecraft.dead_tube_coral_block": "Bloque de coral de tubos muerto", "block.minecraft.dead_tube_coral_fan": "Gorgonia de tubos muerto", "block.minecraft.dead_tube_coral_wall_fan": "Gorgonia de tubos muerto de parede", "block.minecraft.decorated_pot": "Maceta decorada", "block.minecraft.deepslate": "Pizarra", "block.minecraft.deepslate_brick_slab": "Llábana de lladriyos de pizarra", "block.minecraft.deepslate_brick_stairs": "Pasales de lladriyos de pizarra", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> <PERSON> lladriyo<PERSON> de p<PERSON>rra", "block.minecraft.deepslate_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_coal_ore": "Veta de carbón en pizarra", "block.minecraft.deepslate_copper_ore": "Veta de cobre en pizarra", "block.minecraft.deepslate_diamond_ore": "Veta de diamante en pizarra", "block.minecraft.deepslate_emerald_ore": "Veta d'esmeralda en pizarra", "block.minecraft.deepslate_gold_ore": "Veta d'oru en pizarra", "block.minecraft.deepslate_iron_ore": "Veta de fierro en pizarra", "block.minecraft.deepslate_lapis_ore": "Veta de llapislázuli en pizarra", "block.minecraft.deepslate_redstone_ore": "Veta de redstone en pizarra", "block.minecraft.deepslate_tile_slab": "Llábana d'azulexos de pizarra", "block.minecraft.deepslate_tile_stairs": "Pasales d'azulexos de pizarra", "block.minecraft.deepslate_tile_wall": "Muria d'azulexos de pizarra", "block.minecraft.deepslate_tiles": "Azulexos de pizarra", "block.minecraft.detector_rail": "<PERSON><PERSON> detector<PERSON>", "block.minecraft.diamond_block": "Bloque de diamante", "block.minecraft.diamond_ore": "Veta de diamante", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Llábana de diorita", "block.minecraft.diorite_stairs": "Pasales de diorita", "block.minecraft.diorite_wall": "<PERSON>ria de di<PERSON>", "block.minecraft.dirt": "Tierra", "block.minecraft.dirt_path": "Camín de <PERSON>", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "Tiesta de cuélebre", "block.minecraft.dragon_wall_head": "Tiesta de cuélebre na parede", "block.minecraft.dried_ghast": "G<PERSON><PERSON> seco", "block.minecraft.dried_kelp_block": "Bloque de laminarial seco", "block.minecraft.dripstone_block": "Bloque d'espeleotema", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Bloque d'esmeralda", "block.minecraft.emerald_ore": "Veta d'esmeralda", "block.minecraft.enchanting_table": "Mesa d'encantexos", "block.minecraft.end_gateway": "Pasera al End", "block.minecraft.end_portal": "Portal del End", "block.minecraft.end_portal_frame": "<PERSON><PERSON>'un portal al End", "block.minecraft.end_rod": "Vara del End", "block.minecraft.end_stone": "Piedra del End", "block.minecraft.end_stone_brick_slab": "Llábana de lladriyos del End", "block.minecraft.end_stone_brick_stairs": "Pasales de lladriyos del End", "block.minecraft.end_stone_brick_wall": "<PERSON>ria de lladriyos de piedra del End", "block.minecraft.end_stone_bricks": "Lladriyos de piedra del End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Cobre cincelado expuesto", "block.minecraft.exposed_copper": "Cobre espuesto", "block.minecraft.exposed_copper_bulb": "Bombilla de cobre expuesta", "block.minecraft.exposed_copper_door": "Puerta de cobre expuesta", "block.minecraft.exposed_copper_grate": "Reixa de cobre espuesta", "block.minecraft.exposed_copper_trapdoor": "Trampa de cobre expuesta", "block.minecraft.exposed_cut_copper": "Cobre cortado expuesto", "block.minecraft.exposed_cut_copper_slab": "Loza de cobre cortada expuesta", "block.minecraft.exposed_cut_copper_stairs": "Escaleras de cobre cortadas expuestas", "block.minecraft.farmland": "Bloque d'eru", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "Coral de fueu", "block.minecraft.fire_coral_block": "Bloque de coral de fueu", "block.minecraft.fire_coral_fan": "Gorgonia <PERSON>ue<PERSON>", "block.minecraft.fire_coral_wall_fan": "Gorgon<PERSON> de fueu de parede", "block.minecraft.firefly_bush": "Parrotal de lluciérnagas", "block.minecraft.fletching_table": "Mesa de fleches", "block.minecraft.flower_pot": "Tiestu", "block.minecraft.flowering_azalea": "Rododendru flore<PERSON>", "block.minecraft.flowering_azalea_leaves": "Fueyes de rododentru florecío", "block.minecraft.frogspawn": "Asoccançada de sapos", "block.minecraft.frosted_ice": "Xelada", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "Nigrilitu con oru", "block.minecraft.glass": "V<PERSON><PERSON>", "block.minecraft.glass_pane": "Panel de vidru", "block.minecraft.glow_lichen": "<PERSON><PERSON> re<PERSON>", "block.minecraft.glowstone": "<PERSON><PERSON>", "block.minecraft.gold_block": "Bloque d'oru", "block.minecraft.gold_ore": "Veta d'oru", "block.minecraft.granite": "Granitu", "block.minecraft.granite_slab": "Llábana de granitu", "block.minecraft.granite_stairs": "Pasales de granitu", "block.minecraft.granite_wall": "<PERSON><PERSON> de grani<PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "Tapín", "block.minecraft.gravel": "Grava", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON> buxu", "block.minecraft.gray_bed": "<PERSON><PERSON> buxa", "block.minecraft.gray_candle": "<PERSON><PERSON> buxa", "block.minecraft.gray_candle_cake": "Tarta con una vela buxa", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON> buxa", "block.minecraft.gray_concrete": "Formig<PERSON> buxo", "block.minecraft.gray_concrete_powder": "Povisa de formigón buxo", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> buxu", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON> buxa <PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> tintao en buxo", "block.minecraft.gray_stained_glass_pane": "Panel de vidru tintao en buxo", "block.minecraft.gray_terracotta": "Terracota buxo", "block.minecraft.gray_wool": "<PERSON><PERSON> b<PERSON>o", "block.minecraft.green_banner": "Estandarte verde", "block.minecraft.green_bed": "Cama verde", "block.minecraft.green_candle": "Vela verde", "block.minecraft.green_candle_cake": "Tarta con una vela verde", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON> verde", "block.minecraft.green_concrete": "Formigón verde", "block.minecraft.green_concrete_powder": "Povisa de formigón verde", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON>lexu verde", "block.minecraft.green_shulker_box": "Caxa verde de shulker", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> tintao en verde", "block.minecraft.green_stained_glass_pane": "Panel de vidru tintao en verde", "block.minecraft.green_terracotta": "Terracota verde", "block.minecraft.green_wool": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.grindstone": "Piedra p'amolar", "block.minecraft.hanging_roots": " <PERSON><PERSON><PERSON> co<PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Placa de presión pa pesu pesáu", "block.minecraft.honey_block": "Bloque de miel", "block.minecraft.honeycomb_block": "Bloque de ca<PERSON>llos", "block.minecraft.hopper": "Embudu", "block.minecraft.horn_coral": "Coral de cuernos", "block.minecraft.horn_coral_block": "Bloque de coral de cuernos", "block.minecraft.horn_coral_fan": "Gorgonia de cuernos", "block.minecraft.horn_coral_wall_fan": "Gorgonia de tubos de parede", "block.minecraft.ice": "<PERSON>elu", "block.minecraft.infested_chiseled_stone_bricks": "Lladriyos de piedra cincelao infestaos", "block.minecraft.infested_cobblestone": "Llastra infestao", "block.minecraft.infested_cracked_stone_bricks": "Lladriyos de piedra frañíos ya infestaos", "block.minecraft.infested_deepslate": "Pizarra infestao", "block.minecraft.infested_mossy_stone_bricks": "Lladriyos de piedra mofoso ya infestao", "block.minecraft.infested_stone": "Piedra infestao", "block.minecraft.infested_stone_bricks": "Lladriyos de piedra infestao", "block.minecraft.iron_bars": "Barrotes de fi<PERSON>ro", "block.minecraft.iron_block": "Bloque de fi<PERSON>ro", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "Veta de fierro", "block.minecraft.iron_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jack_o_lantern": "<PERSON>'<PERSON>", "block.minecraft.jigsaw": "Bloque de tétramuxería", "block.minecraft.jukebox": "Tocadiscos", "block.minecraft.jungle_button": "Botón de madera de la xungla", "block.minecraft.jungle_door": "Puerta de madera de la xungla", "block.minecraft.jungle_fence": "Valla de madera de la xungla", "block.minecraft.jungle_fence_gate": "Portiella de madera de la xungla", "block.minecraft.jungle_hanging_sign": "Se<PERSON><PERSON> colgante de la selva", "block.minecraft.jungle_leaves": "Fu<PERSON><PERSON> de <PERSON> xungla", "block.minecraft.jungle_log": "<PERSON><PERSON><PERSON> <PERSON> de la <PERSON>", "block.minecraft.jungle_planks": "Tables de madera de la xungla", "block.minecraft.jungle_pressure_plate": "Placa de presión de madera de la xungla", "block.minecraft.jungle_sapling": "<PERSON><PERSON>u de <PERSON>ungla", "block.minecraft.jungle_sign": "Cartelu de madera de la <PERSON>ung<PERSON>", "block.minecraft.jungle_slab": "Llábana de madera de la xungla", "block.minecraft.jungle_stairs": "Pasales de madera de la xungla", "block.minecraft.jungle_trapdoor": "Trapiella de madera de la <PERSON>ungla", "block.minecraft.jungle_wall_hanging_sign": "Paniellu colgante de la muria de la selva", "block.minecraft.jungle_wall_sign": "Cartelu de madera de la xungla na parede", "block.minecraft.jungle_wood": "Madera de la xungla con corteza", "block.minecraft.kelp": "Laminarial", "block.minecraft.kelp_plant": "Biltu de laminarial", "block.minecraft.ladder": "Escalera de mano", "block.minecraft.lantern": "Llámpara", "block.minecraft.lapis_block": "Bloque de llapislázuli", "block.minecraft.lapis_ore": "Veta de llapislázuli", "block.minecraft.large_amethyst_bud": "Broche de amatista grande", "block.minecraft.large_fern": "Felechu grande", "block.minecraft.lava": "Llava", "block.minecraft.lava_cauldron": "<PERSON>u con llava", "block.minecraft.leaf_litter": "Fueyes seques", "block.minecraft.lectern": "Atril", "block.minecraft.lever": "Palanca", "block.minecraft.light": "Lluz", "block.minecraft.light_blue_banner": "Estandarte azul claru", "block.minecraft.light_blue_bed": "Cama azul claro", "block.minecraft.light_blue_candle": "<PERSON>ela azul claro", "block.minecraft.light_blue_candle_cake": "Tarta con cendra azul claru", "block.minecraft.light_blue_carpet": "<PERSON>queta azul claro", "block.minecraft.light_blue_concrete": "Formigón azul claro", "block.minecraft.light_blue_concrete_powder": "Povisa de formigón azul claro", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON>u azul claro", "block.minecraft.light_blue_shulker_box": "Caxa azul claro de <PERSON>ker", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> tintao n'azul claro", "block.minecraft.light_blue_stained_glass_pane": "Panel de vidru tintao n'azul claro", "block.minecraft.light_blue_terracotta": "Terracota azul claro", "block.minecraft.light_blue_wool": "<PERSON><PERSON> a<PERSON> claro", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON> buxu claru", "block.minecraft.light_gray_bed": "<PERSON>a buxo claro", "block.minecraft.light_gray_candle": "<PERSON><PERSON> gris clara", "block.minecraft.light_gray_candle_cake": "Tarta con vela gris clara", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON> buxa clara", "block.minecraft.light_gray_concrete": "Formigón buxo claro", "block.minecraft.light_gray_concrete_powder": "Povisa de formigón buxo claro", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> buxu claro", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON> buxa claro de <PERSON>ker", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> tintao en buxo claro", "block.minecraft.light_gray_stained_glass_pane": "Panel de vidru tintao en buxo claro", "block.minecraft.light_gray_terracotta": "Terracota buxo claro", "block.minecraft.light_gray_wool": "<PERSON><PERSON> buxo claro", "block.minecraft.light_weighted_pressure_plate": "Placa de presión pa pesu llixeru", "block.minecraft.lightning_rod": "Pararrayos", "block.minecraft.lilac": "Lilá", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON> de los valles", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Estandarte llima", "block.minecraft.lime_bed": "<PERSON><PERSON> llima", "block.minecraft.lime_candle": "<PERSON><PERSON> llima", "block.minecraft.lime_candle_cake": "Tarta con una vela llima", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON> llima", "block.minecraft.lime_concrete": "Formigón llima", "block.minecraft.lime_concrete_powder": "Povisa de formigón llima", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> llima", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON> llima de <PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON> tintao en llima", "block.minecraft.lime_stained_glass_pane": "Panel de vidru tintao en llima", "block.minecraft.lime_terracotta": "Terracota llima", "block.minecraft.lime_wool": "<PERSON><PERSON>", "block.minecraft.lodestone": "Ma<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Telar", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.magenta_bed": "<PERSON><PERSON> maxenta", "block.minecraft.magenta_candle": "<PERSON><PERSON> maxenta", "block.minecraft.magenta_candle_cake": "Tarta con una vela maxenta", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.magenta_concrete": "<PERSON>ig<PERSON> maxenta", "block.minecraft.magenta_concrete_powder": "Povisa de formigón maxenta", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON> tintao en maxenta", "block.minecraft.magenta_stained_glass_pane": "Panel de vidru tintao en maxenta", "block.minecraft.magenta_terracotta": "Terracota maxenta", "block.minecraft.magenta_wool": "<PERSON><PERSON>", "block.minecraft.magma_block": "Bloque ma<PERSON>u", "block.minecraft.mangrove_button": "Botón de manglar", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON> de man<PERSON>lar", "block.minecraft.mangrove_fence": "<PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "<PERSON>uer<PERSON> de cercu de mangle", "block.minecraft.mangrove_hanging_sign": "Cartel colgante de mangle", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_log": "Tronco de mangle", "block.minecraft.mangrove_planks": "Tablones de mangle", "block.minecraft.mangrove_pressure_plate": "Placa de presión de mangle", "block.minecraft.mangrove_propagule": "Propágulo de mangle", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON> man<PERSON>lar", "block.minecraft.mangrove_sign": "<PERSON><PERSON>", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_stairs": "Escaleres de manglar", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON> de manglar", "block.minecraft.mangrove_wall_hanging_sign": "Cartel de pared de mangle colgante", "block.minecraft.mangrove_wall_sign": "Cartel de pared de mangle", "block.minecraft.mangrove_wood": "<PERSON><PERSON> de mangle", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON>u mediu de ametista", "block.minecraft.melon": "Sandía", "block.minecraft.melon_stem": "<PERSON>u <PERSON>", "block.minecraft.moss_block": "Bloque de mofu", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON> de mofu", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON> mofoso", "block.minecraft.mossy_cobblestone_slab": "Llábana de llastra mofoso", "block.minecraft.mossy_cobblestone_stairs": "Pasales de llastra mofoso", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> de ll<PERSON>ra mofoso", "block.minecraft.mossy_stone_brick_slab": "Llábana de lladriyos de piedra mofosa", "block.minecraft.mossy_stone_brick_stairs": "Pasales de lladriyos de piedra mofosa", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de lladriyos de piedra mofosa", "block.minecraft.mossy_stone_bricks": "Lladri<PERSON>s de piedra mofosa", "block.minecraft.moving_piston": "<PERSON><PERSON> que se mueve", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Llábana de lladriyu de Barru", "block.minecraft.mud_brick_stairs": "Pasales de Lladriyu de Barru", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> Lladri<PERSON>", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON><PERSON> fangosu de mangle", "block.minecraft.mushroom_stem": "<PERSON><PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Valla de lladriyos del Nether", "block.minecraft.nether_brick_slab": "Llábana de lladriyos del Nether", "block.minecraft.nether_brick_stairs": "Pasales de lladriyos del Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> de lladri<PERSON>her", "block.minecraft.nether_bricks": "Lladriyos del <PERSON>her", "block.minecraft.nether_gold_ore": "Veta d'oru del Nether", "block.minecraft.nether_portal": "Portal al Nether", "block.minecraft.nether_quartz_ore": "Veta de cuarzu del Nether", "block.minecraft.nether_sprouts": "Brootes de l'infern", "block.minecraft.nether_wart": "Fungu del Nether", "block.minecraft.nether_wart_block": "Bloque d'úzara del Nether", "block.minecraft.netherite_block": "Bloque de netherita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloque de notes", "block.minecraft.oak_button": "Botón de <PERSON>", "block.minecraft.oak_door": "Puerta de carbayu", "block.minecraft.oak_fence": "Valla de carbayu", "block.minecraft.oak_fence_gate": "Portiella de carbayu", "block.minecraft.oak_hanging_sign": "Cartel colgante de branu", "block.minecraft.oak_leaves": "Fueyes de carbayu", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.oak_planks": "Tables de carbayu", "block.minecraft.oak_pressure_plate": "Placa de presión de carbayu", "block.minecraft.oak_sapling": "Biltu de carbayu", "block.minecraft.oak_sign": "Cartelu de <PERSON>bayu", "block.minecraft.oak_slab": "Llábana de carbayu", "block.minecraft.oak_stairs": "Pasales de carbayu", "block.minecraft.oak_trapdoor": "T<PERSON><PERSON><PERSON>u", "block.minecraft.oak_wall_hanging_sign": "Cartel colgante de branu de l'arriu", "block.minecraft.oak_wall_sign": "Cartelu de carbayu na parede", "block.minecraft.oak_wood": "Madera de carbayu con corteza", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "Xaroncalluz ocre", "block.minecraft.ominous_banner": "Estandarte siniestru", "block.minecraft.open_eyeblossom": "Miraflor abierta", "block.minecraft.orange_banner": "Estandarte naranxa", "block.minecraft.orange_bed": "Cama naranxa", "block.minecraft.orange_candle": "<PERSON><PERSON> naranxa", "block.minecraft.orange_candle_cake": "Tarta con una vela naranxa", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON> naranxa", "block.minecraft.orange_concrete": "Formigón naranxa", "block.minecraft.orange_concrete_powder": "Povisa de formigón naranxa", "block.minecraft.orange_glazed_terracotta": "Azulexu naranxa", "block.minecraft.orange_shulker_box": "Caxa naranxa de <PERSON>ker", "block.minecraft.orange_stained_glass": "Vid<PERSON> tintao en naranxa", "block.minecraft.orange_stained_glass_pane": "Panel de vidru tintao en naranxa", "block.minecraft.orange_terracotta": "Terracota naranxa", "block.minecraft.orange_tulip": "Tulipán naranxa", "block.minecraft.orange_wool": "<PERSON><PERSON>", "block.minecraft.oxeye_daisy": "Catasol grande", "block.minecraft.oxidized_chiseled_copper": "Cobru ciselláu oxidáu", "block.minecraft.oxidized_copper": "Cobre oxidao", "block.minecraft.oxidized_copper_bulb": "Bombiella de cobru oxidáu", "block.minecraft.oxidized_copper_door": "Puerta de cobru oxidáu", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON>a de cobru oxidáu", "block.minecraft.oxidized_copper_trapdoor": "Trampexa de cobru oxidáu", "block.minecraft.oxidized_cut_copper": "Cobre cortao oxidao", "block.minecraft.oxidized_cut_copper_slab": "Llábana de cobre cortao oxidada", "block.minecraft.oxidized_cut_copper_stairs": "Pasales de cobre cortao oxidaos", "block.minecraft.packed_ice": "Xelu compauto", "block.minecraft.packed_mud": "<PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON> p<PERSON> co<PERSON>", "block.minecraft.pale_moss_block": "Bloque de mofu pálido", "block.minecraft.pale_moss_carpet": "Alfombra de mofu pálido", "block.minecraft.pale_oak_button": "Botón de carbayu pálido", "block.minecraft.pale_oak_door": "Puerta de carbayu pálido", "block.minecraft.pale_oak_fence": "Valla de carbayu pálido", "block.minecraft.pale_oak_fence_gate": "Puerta de valla de carbayu pálido", "block.minecraft.pale_oak_hanging_sign": "Cartelu colgante de carbayu pálido", "block.minecraft.pale_oak_leaves": "Fueyes de carbayu pálido", "block.minecraft.pale_oak_log": "<PERSON><PERSON><PERSON> de car<PERSON> p<PERSON>", "block.minecraft.pale_oak_planks": "Tablones de carbayu pálido", "block.minecraft.pale_oak_pressure_plate": "Placa de presión de carbayu pálido", "block.minecraft.pale_oak_sapling": "Biltu de carbayu pálido", "block.minecraft.pale_oak_sign": "Cartelu de carbayu pálido", "block.minecraft.pale_oak_slab": "Llosa de carbayu pálido", "block.minecraft.pale_oak_stairs": "Escaleres de carbayu pálido", "block.minecraft.pale_oak_trapdoor": "Trapiella de carbayu pálido", "block.minecraft.pale_oak_wall_hanging_sign": "Cartelu colgante de carbayu pálido na paré", "block.minecraft.pale_oak_wall_sign": "Cartelu de carbayu pálido na paré", "block.minecraft.pale_oak_wood": "<PERSON><PERSON> de carbayu pá<PERSON>o", "block.minecraft.pearlescent_froglight": "Xaroncalluz nacarada", "block.minecraft.peony": "Peonia", "block.minecraft.petrified_oak_slab": "Llábana de carbayu petrificáu", "block.minecraft.piglin_head": "Cabeza de piglin", "block.minecraft.piglin_wall_head": "Cabeza de piglin na paré", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_bed": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle_cake": "Tarta con una vela rosa", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_concrete": "Formigón rosa", "block.minecraft.pink_concrete_powder": "Povisa de formigón rosa", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_petals": "Pétalos roses", "block.minecraft.pink_shulker_box": "<PERSON>axa rosa de s<PERSON>ker", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> tintao en rosa", "block.minecraft.pink_stained_glass_pane": "Panel de vidru tintao en rosa", "block.minecraft.pink_terracotta": "Terracota rosa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_wool": "<PERSON><PERSON> rosa", "block.minecraft.piston": "Pistón", "block.minecraft.piston_head": "Cabeza d'un pistón", "block.minecraft.pitcher_crop": "Cultivu de cán<PERSON>us", "block.minecraft.pitcher_plant": "Planta de cántaros", "block.minecraft.player_head": "Tiesta de xugador", "block.minecraft.player_head.named": "Tiesta de %s", "block.minecraft.player_wall_head": "Tiesta de xugador na parede", "block.minecraft.podzol": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pointed_dripstone": "Espeleotema puntiao", "block.minecraft.polished_andesite": "Andesita pulío", "block.minecraft.polished_andesite_slab": "Llábana d'andesita pulío", "block.minecraft.polished_andesite_stairs": "Pasales d'andesita pulío", "block.minecraft.polished_basalt": "Basaltu pul<PERSON>", "block.minecraft.polished_blackstone": "Nigrilitu pulío", "block.minecraft.polished_blackstone_brick_slab": "Llábana de lladriyos de nigrilitu pulío", "block.minecraft.polished_blackstone_brick_stairs": "Pasales de lladriyos de nigrilitu pulío", "block.minecraft.polished_blackstone_brick_wall": "Muria de lladriyos de nigrilitu pulío", "block.minecraft.polished_blackstone_bricks": "Lladriyos de nigrilitu pulío", "block.minecraft.polished_blackstone_button": "Botón de nigrilitu pulío", "block.minecraft.polished_blackstone_pressure_plate": "Placa de presión de nigrilitu pulío", "block.minecraft.polished_blackstone_slab": "Llábana de nigrilitu pulío", "block.minecraft.polished_blackstone_stairs": "Pasales de nigrilitu pulío", "block.minecraft.polished_blackstone_wall": "Muria de nigrilitu pulío", "block.minecraft.polished_deepslate": "Pizarra pulío", "block.minecraft.polished_deepslate_slab": "Llábana de pizarra pulío", "block.minecraft.polished_deepslate_stairs": "Pasales de pizarra pulío", "block.minecraft.polished_deepslate_wall": "Muria de pizarra pulío", "block.minecraft.polished_diorite": "Diorita pulío", "block.minecraft.polished_diorite_slab": "Llábana de diorita pulío", "block.minecraft.polished_diorite_stairs": "Pasales de diorita pulío", "block.minecraft.polished_granite": "Granitu pulío", "block.minecraft.polished_granite_slab": "Llábana de granitu pulío", "block.minecraft.polished_granite_stairs": "Pasales de granitu pulío", "block.minecraft.polished_tuff": "<PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "Solanca de tufu pulíu", "block.minecraft.polished_tuff_stairs": "Escaleres de tufu pulida", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON> de tufu pulía", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Pataques", "block.minecraft.potted_acacia_sapling": "Tiestu con un biltu d'alcacia", "block.minecraft.potted_allium": "Tiestu con un ayu bravu", "block.minecraft.potted_azalea_bush": "Tiestu con rododendru", "block.minecraft.potted_azure_bluet": "Tiestu con una houstonia caerulea", "block.minecraft.potted_bamboo": "Tiestu con bambú", "block.minecraft.potted_birch_sapling": "Tiestu con un biltu de bidul", "block.minecraft.potted_blue_orchid": "Tiestu con una orquídea azul", "block.minecraft.potted_brown_mushroom": "Tiestu con un xampiñón", "block.minecraft.potted_cactus": "Tiestu con un cactus", "block.minecraft.potted_cherry_sapling": "Planta de cereza en maceta", "block.minecraft.potted_closed_eyeblossom": "Tiestu con miraflor cerrada", "block.minecraft.potted_cornflower": "Tiestu con una clavelina", "block.minecraft.potted_crimson_fungus": "Tiestu con un fungu carmesín", "block.minecraft.potted_crimson_roots": "Tiestu con yerba carmesín", "block.minecraft.potted_dandelion": "Tiestu con una xiblata", "block.minecraft.potted_dark_oak_sapling": "Tiestu con un biltu de carbayu escuro", "block.minecraft.potted_dead_bush": "Matu muertu n'unu macetu", "block.minecraft.potted_fern": "Tiestu con un felechu", "block.minecraft.potted_flowering_azalea_bush": "Tiestu con rododendru florecío", "block.minecraft.potted_jungle_sapling": "Tiestu con un biltu de la xungla", "block.minecraft.potted_lily_of_the_valley": "Tiestu con un lliriu de los valles", "block.minecraft.potted_mangrove_propagule": "Propágulu de mangle nuna maceta", "block.minecraft.potted_oak_sapling": "Tiestu con un biltu de carbayu", "block.minecraft.potted_open_eyeblossom": "Tiestu con miraflor abierta", "block.minecraft.potted_orange_tulip": "Tiestu con un tulipán naranxa", "block.minecraft.potted_oxeye_daisy": "Tiestu con un catasol grande", "block.minecraft.potted_pale_oak_sapling": "Tiestu con biltu de carbayu pálido", "block.minecraft.potted_pink_tulip": "Tiestu con un tulipán rosa", "block.minecraft.potted_poppy": "Tiestu con una papola", "block.minecraft.potted_red_mushroom": "Tiestu con un xetu roxu", "block.minecraft.potted_red_tulip": "Tiestu con un tulipán coloráu", "block.minecraft.potted_spruce_sapling": "Tiestu con un biltu d'abetu", "block.minecraft.potted_torchflower": "<PERSON><PERSON> de flor de antorcha", "block.minecraft.potted_warped_fungus": "Tiestu con un fungu distorsionáu", "block.minecraft.potted_warped_roots": "Tiestu con yerba distorsionao", "block.minecraft.potted_white_tulip": "Tiestu con un tulipán blancu", "block.minecraft.potted_wither_rose": "Tiestu con una rosa de <PERSON>", "block.minecraft.powder_snow": "Ñeve en polvu", "block.minecraft.powder_snow_cauldron": "Caldera con nieve en polvu", "block.minecraft.powered_rail": "Vía puxadora", "block.minecraft.prismarine": "Prismarín", "block.minecraft.prismarine_brick_slab": "Llábana de lladriyos de prismarín", "block.minecraft.prismarine_brick_stairs": "Pasales de lladriyos de prismarín", "block.minecraft.prismarine_bricks": "Lladriyos de prismarín", "block.minecraft.prismarine_slab": "Llábana de prismarín", "block.minecraft.prismarine_stairs": "Pasales de prismarín", "block.minecraft.prismarine_wall": "Muria de prismarín", "block.minecraft.pumpkin": "Calabaza", "block.minecraft.pumpkin_stem": "Tallu de ca<PERSON>za", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.purple_bed": "Cama morada", "block.minecraft.purple_candle": "<PERSON><PERSON> morada", "block.minecraft.purple_candle_cake": "Tarta con una vela morada", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON> morada", "block.minecraft.purple_concrete": "Formigón morao", "block.minecraft.purple_concrete_powder": "Povisa de formigón morao", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.purple_shulker_box": "Caxa morada de <PERSON>ker", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> tintao en morao", "block.minecraft.purple_stained_glass_pane": "Panel de vidru tintao en morao", "block.minecraft.purple_terracotta": "Terracota morao", "block.minecraft.purple_wool": "<PERSON><PERSON> m<PERSON>o", "block.minecraft.purpur_block": "Bloque de purpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON> de purpur", "block.minecraft.purpur_slab": "Llábana de purpur", "block.minecraft.purpur_stairs": "Pasales de purpur", "block.minecraft.quartz_block": "Bloque de cuarzu", "block.minecraft.quartz_bricks": "Llad<PERSON><PERSON><PERSON>zu", "block.minecraft.quartz_pillar": "<PERSON><PERSON>", "block.minecraft.quartz_slab": "Llábana de cuarzu", "block.minecraft.quartz_stairs": "Pasales de cuarzu", "block.minecraft.rail": "Vía", "block.minecraft.raw_copper_block": "Bloque de cobre en bruto", "block.minecraft.raw_gold_block": "Bloque d'oru en bruto", "block.minecraft.raw_iron_block": "Bloque de fierro en bruto", "block.minecraft.red_banner": "Estandarte coloráu", "block.minecraft.red_bed": "Cama colorada", "block.minecraft.red_candle": "Vela colorada", "block.minecraft.red_candle_cake": "Tarta con una vela colorada", "block.minecraft.red_carpet": "Moqueta colorada", "block.minecraft.red_concrete": "Formigón colorao", "block.minecraft.red_concrete_powder": "Povisa de formigón colorao", "block.minecraft.red_glazed_terracotta": "Azulexu coloráu", "block.minecraft.red_mushroom": "Xetu roxu", "block.minecraft.red_mushroom_block": "Bloque de xetos roxos", "block.minecraft.red_nether_brick_slab": "Llábana de lladriyos coloraos del Nether", "block.minecraft.red_nether_brick_stairs": "Pasales de lladriyos coloraos del Nether", "block.minecraft.red_nether_brick_wall": "<PERSON>ria de lladriyos coloraos del Nether", "block.minecraft.red_nether_bricks": "Lladriyos del Nether coloraos", "block.minecraft.red_sand": "Sable colorao", "block.minecraft.red_sandstone": "Gres colorao", "block.minecraft.red_sandstone_slab": "Llábana de gres colorao", "block.minecraft.red_sandstone_stairs": "Pasales de gres colorao", "block.minecraft.red_sandstone_wall": "Muria de gres colorao", "block.minecraft.red_shulker_box": "Caxa colorada de shulker", "block.minecraft.red_stained_glass": "Vidru tintao en colorao", "block.minecraft.red_stained_glass_pane": "Panel de vidru tintao en colorao", "block.minecraft.red_terracotta": "Terracota colorao", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Bloque de redstone", "block.minecraft.redstone_lamp": "Llámpara de redstone", "block.minecraft.redstone_ore": "Veta de redstone", "block.minecraft.redstone_torch": "Antorcha de redstone", "block.minecraft.redstone_wall_torch": "Antor<PERSON> de redstone de parede", "block.minecraft.redstone_wire": "Cable de redstone", "block.minecraft.reinforced_deepslate": "Profundu fragu reinforciau", "block.minecraft.repeater": "Repitidor de redstone", "block.minecraft.repeating_command_block": "Bloque de comandos iterativu", "block.minecraft.resin_block": "Block of Resin", "block.minecraft.resin_brick_slab": "Resin Brick Slab", "block.minecraft.resin_brick_stairs": "Resin Brick Stairs", "block.minecraft.resin_brick_wall": "Resin Brick Wall", "block.minecraft.resin_bricks": "Resin Bricks", "block.minecraft.resin_clump": "<PERSON><PERSON>", "block.minecraft.respawn_anchor": "Bloque de remanecimientu", "block.minecraft.rooted_dirt": "Tierra arraigonao", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Sable", "block.minecraft.sandstone": "Gres", "block.minecraft.sandstone_slab": "Llábana de gres", "block.minecraft.sandstone_stairs": "Pasales de gres", "block.minecraft.sandstone_wall": "<PERSON>ria de <PERSON>res", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador sculk", "block.minecraft.sculk_sensor": "Sensor sculk", "block.minecraft.sculk_shrieker": "Glayador de Sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON> sculk", "block.minecraft.sea_lantern": "Llámpara marina", "block.minecraft.sea_pickle": "Pirosoma", "block.minecraft.seagrass": "<PERSON><PERSON> marino", "block.minecraft.set_spawn": "Afitóse l'aprucideru", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "Miera curta", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Craniu de Cadarma", "block.minecraft.skeleton_wall_skull": "Craniu na parede", "block.minecraft.slime_block": "Bloque de llimu", "block.minecraft.small_amethyst_bud": "Florecína ametista pequeño", "block.minecraft.small_dripleaf": "Plantaforma pequeña", "block.minecraft.smithing_table": "Mesa de ferrería", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON> dondo", "block.minecraft.smooth_quartz": "Bloque de cuarzu dondo", "block.minecraft.smooth_quartz_slab": "Llábana de cuarzu dondo", "block.minecraft.smooth_quartz_stairs": "Pasales de cuarzu dondo", "block.minecraft.smooth_red_sandstone": "Gres colorao dondo", "block.minecraft.smooth_red_sandstone_slab": "Llábana de gres colorao dondo", "block.minecraft.smooth_red_sandstone_stairs": "Pasales de gres colorao dondo", "block.minecraft.smooth_sandstone": "<PERSON><PERSON> dondo", "block.minecraft.smooth_sandstone_slab": "Llábana de gres dondo", "block.minecraft.smooth_sandstone_stairs": "Pasales de gres dondo", "block.minecraft.smooth_stone": "<PERSON><PERSON> donda", "block.minecraft.smooth_stone_slab": "Llábana de piedra donda", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON>'<PERSON><PERSON>", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Bloque de nieve", "block.minecraft.soul_campfire": "Foguera d'ánimes", "block.minecraft.soul_fire": "<PERSON><PERSON>", "block.minecraft.soul_lantern": "Llámpara d'ánimes", "block.minecraft.soul_sand": "Sable d'<PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "Tierra d'ánimes", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON> de muria d'al<PERSON>", "block.minecraft.spawn.not_valid": "Non tienes cama de llar, cama de resurreición cargada o foi obstruyida", "block.minecraft.spawner": "<PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Interactuar cola Güevu d'aportiu:", "block.minecraft.spawner.desc2": "Axusta'l tipu d'averá", "block.minecraft.sponge": "Esponxa", "block.minecraft.spore_blossom": "Flor de esporas", "block.minecraft.spruce_button": "Botón d'abetu", "block.minecraft.spruce_door": "Puerta d'abetu", "block.minecraft.spruce_fence": "Valla d'a<PERSON>u", "block.minecraft.spruce_fence_gate": "Portiella d'abetu", "block.minecraft.spruce_hanging_sign": "Cartel colgante de abetu", "block.minecraft.spruce_leaves": "Fueyes d'abetu", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON>'a<PERSON>", "block.minecraft.spruce_planks": "Tables d'abetu", "block.minecraft.spruce_pressure_plate": "Placa de presión d'abetu", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON> d'a<PERSON>u", "block.minecraft.spruce_sign": "Cartelu d'abetu", "block.minecraft.spruce_slab": "Llábana d'abetu", "block.minecraft.spruce_stairs": "Pasales d'abetu", "block.minecraft.spruce_trapdoor": "Trap<PERSON><PERSON> d'a<PERSON>", "block.minecraft.spruce_wall_hanging_sign": "Cartel colgante de pared de abetu", "block.minecraft.spruce_wall_sign": "Cartelu d'abetu na parede", "block.minecraft.spruce_wood": "Madera d'abetu con corteza", "block.minecraft.sticky_piston": "Pistón pegañón", "block.minecraft.stone": "Piedra", "block.minecraft.stone_brick_slab": "Llábana de lladriyos de piedra", "block.minecraft.stone_brick_stairs": "Pasales de lladriyos de piedra", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de lladriyos de piedra", "block.minecraft.stone_bricks": "Lladriyos de piedra", "block.minecraft.stone_button": "Botón de piedra", "block.minecraft.stone_pressure_plate": "Placa presión de piedra", "block.minecraft.stone_slab": "Llábana de piedra", "block.minecraft.stone_stairs": "Pasales de piedra", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON> <PERSON>'al<PERSON> ensin corteza", "block.minecraft.stripped_acacia_wood": "Madera d'alcacia ensin corteza", "block.minecraft.stripped_bamboo_block": "Bloque de bambú peláu", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON> de bidul ensin corteza", "block.minecraft.stripped_birch_wood": "Madera de bidul ensin corteza", "block.minecraft.stripped_cherry_log": "T<PERSON><PERSON>ín de cerru pel<PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON>ra de cer<PERSON> pel<PERSON>", "block.minecraft.stripped_crimson_hyphae": "Hífaes escarletes peladaes", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON> ensin corteza", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON> de carbayu escuro ensin corteza", "block.minecraft.stripped_dark_oak_wood": "Madera de carbayu escuro ensin corteza", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON> de madera ensin corteza de la xungla", "block.minecraft.stripped_jungle_wood": "Madera ensin corteza de la xungla", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON> de manglar pel<PERSON>u", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON> de manglar pel<PERSON>u", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON> de carbayu ensin corteza", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON> de carbayu ensin corteza", "block.minecraft.stripped_pale_oak_log": "Stripped Pale Oak Log", "block.minecraft.stripped_pale_oak_wood": "Stripped Pale Oak Wood", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON><PERSON> d'abetu ensin corteza", "block.minecraft.stripped_spruce_wood": "Madera d'abetu ensin corteza", "block.minecraft.stripped_warped_hyphae": "Hífaes xiries peladaes", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON> distors<PERSON><PERSON><PERSON> ensin corteza", "block.minecraft.structure_block": "Bloque d'estructures", "block.minecraft.structure_void": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sugar_cane": "Caña d'azucre", "block.minecraft.sunflower": "Xirasol", "block.minecraft.suspicious_gravel": "Gravilla sospechosa", "block.minecraft.suspicious_sand": "<PERSON><PERSON> sospechoses", "block.minecraft.sweet_berry_bush": "Arbuste de <PERSON> dulces", "block.minecraft.tall_dry_grass": "<PERSON>rba seco alto", "block.minecraft.tall_grass": "Yerba alto", "block.minecraft.tall_seagrass": "<PERSON><PERSON> alto", "block.minecraft.target": "<PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "Bloque de Prebes", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON> t<PERSON>ío", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Flor to<PERSON>", "block.minecraft.torchflower_crop": "Cosecha de flores de torcia", "block.minecraft.trapped_chest": "Bagul trampa", "block.minecraft.trial_spawner": "<PERSON><PERSON><PERSON>", "block.minecraft.tripwire": "Cable trampa", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "Coral de tubos", "block.minecraft.tube_coral_block": "Bloque de coral de tubos", "block.minecraft.tube_coral_fan": "Gorgonia de tubos", "block.minecraft.tube_coral_wall_fan": "Gorgonia de tubos de parede", "block.minecraft.tuff": "Toba", "block.minecraft.tuff_brick_slab": "Llosa de lladriyos de toba", "block.minecraft.tuff_brick_stairs": "Pasales de Lladriyos de Toba", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> de Lladriyos de <PERSON>ba", "block.minecraft.tuff_bricks": "<PERSON>ff Bricks", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "Pasales de Toba", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "Twisting Vines", "block.minecraft.twisting_vines_plant": "Twisting Vines Plant", "block.minecraft.vault": "Arca", "block.minecraft.verdant_froglight": "Xaroncalluz verde", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Aire del vacíu", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "Botón distorsionáu", "block.minecraft.warped_door": "Puerta distorsionada", "block.minecraft.warped_fence": "Valla distorsionada", "block.minecraft.warped_fence_gate": "Portiella distorsionada", "block.minecraft.warped_fungus": "Fungu distorsionáu", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON> co<PERSON> to<PERSON>", "block.minecraft.warped_hyphae": "Hyphae torc<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON> distorsion<PERSON>", "block.minecraft.warped_planks": "Tables distorsionaes", "block.minecraft.warped_pressure_plate": "Placa distorsionada de presión", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON> distorsion<PERSON>", "block.minecraft.warped_sign": "Cartelu distorsionáu", "block.minecraft.warped_slab": "Llábana distorsionada", "block.minecraft.warped_stairs": "Pasales distorsionaos", "block.minecraft.warped_stem": "<PERSON><PERSON> distorsionao", "block.minecraft.warped_trapdoor": "Trapiella distorsionada", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante to<PERSON> de pared", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> de pared torcidu", "block.minecraft.warped_wart_block": "Bloque d'úzares distorsionaes", "block.minecraft.water": "Agua", "block.minecraft.water_cauldron": "Caldera con agua", "block.minecraft.waxed_chiseled_copper": "Cobre cincelado encerado", "block.minecraft.waxed_copper_block": "Bloque de cobre enceráu", "block.minecraft.waxed_copper_bulb": "Bombilla de cobre encerada", "block.minecraft.waxed_copper_door": "Puerta de cobre encerada", "block.minecraft.waxed_copper_grate": "Reixa de cobre encerada", "block.minecraft.waxed_copper_trapdoor": "Trampa de cobre encerada", "block.minecraft.waxed_cut_copper": "Cobre cortao encerao", "block.minecraft.waxed_cut_copper_slab": "Llábana de cobre cortao encerada", "block.minecraft.waxed_cut_copper_stairs": "Pasales de cobre cortao enceraos", "block.minecraft.waxed_exposed_chiseled_copper": "Cobre esculpiu espuesto encerado", "block.minecraft.waxed_exposed_copper": "Cobre al descubierto encerado", "block.minecraft.waxed_exposed_copper_bulb": "Bombilla de cobre al descubierto encerado", "block.minecraft.waxed_exposed_copper_door": "Puerta de cobre al descubierto encerado", "block.minecraft.waxed_exposed_copper_grate": "Reja de cobre al descubierto encerada", "block.minecraft.waxed_exposed_copper_trapdoor": "Trampilla de cobre al descubierto encerada", "block.minecraft.waxed_exposed_cut_copper": "Cobre expuesto cortado y encerado", "block.minecraft.waxed_exposed_cut_copper_slab": "Llancha de cobre cinceláu eshomáu", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escaleres de cobre cinceláu eshomáu", "block.minecraft.waxed_oxidized_chiseled_copper": "Cobre esfolláu oxidáu y manxáu", "block.minecraft.waxed_oxidized_copper": "Cobre oxidadu enceráu", "block.minecraft.waxed_oxidized_copper_bulb": "Cordón de coiro oxidadu enceráu", "block.minecraft.waxed_oxidized_copper_door": "Porta de cobre oxidáu enceráu", "block.minecraft.waxed_oxidized_copper_grate": "Reixu de cobre oxidáu enceráu", "block.minecraft.waxed_oxidized_copper_trapdoor": "Trampa de cobre oxidáu enceráu", "block.minecraft.waxed_oxidized_cut_copper": "Cobre cortáu oxidáu enceráu", "block.minecraft.waxed_oxidized_cut_copper_slab": "Placa d'oxídicu de cobre cortada con vierte cera", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escaleres de cobre cortaes oxidáu y enceráu", "block.minecraft.waxed_weathered_chiseled_copper": "Cobre chiseleáu envejecíu y enceráu", "block.minecraft.waxed_weathered_copper": "Cobre ensolbañado y enceráu", "block.minecraft.waxed_weathered_copper_bulb": "Bombilla de cobre envejecido y encerado", "block.minecraft.waxed_weathered_copper_door": "Puerta de cobre envejecido y encerado", "block.minecraft.waxed_weathered_copper_grate": "Re<PERSON>lla de cobre envejecido y encerado", "block.minecraft.waxed_weathered_copper_trapdoor": "Trampa de cobre envejecido y encerado", "block.minecraft.waxed_weathered_cut_copper": "Cobre cortáu envejecíu y enceráu", "block.minecraft.waxed_weathered_cut_copper_slab": "Tabla de cobre cortada envejecida y encerada", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escaleras de cobre cortado envejecido y encerado", "block.minecraft.weathered_chiseled_copper": "Cobre cincelado y envejecido", "block.minecraft.weathered_copper": "Cobre corroyío", "block.minecraft.weathered_copper_bulb": "Bombilla de cobre envejecido", "block.minecraft.weathered_copper_door": "Puerta de cobre envejecido", "block.minecraft.weathered_copper_grate": "Reixu de cobre envejecíu", "block.minecraft.weathered_copper_trapdoor": "Portella de cobre envejecíu", "block.minecraft.weathered_cut_copper": "Cobre cortao corroyío", "block.minecraft.weathered_cut_copper_slab": "Llábana de cobre cortao corroyida", "block.minecraft.weathered_cut_copper_stairs": "Pasales de cobre cortao corroyíos", "block.minecraft.weeping_vines": "Lloriqueo de les plantes", "block.minecraft.weeping_vines_plant": "Planta de enredadera llorona", "block.minecraft.wet_sponge": "Esponxa moyada", "block.minecraft.wheat": "Collecha de t<PERSON>u", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON> blan<PERSON>", "block.minecraft.white_bed": "Cama blanca", "block.minecraft.white_candle": "<PERSON>ela blanca", "block.minecraft.white_candle_cake": "Tarta con una vela blanca", "block.minecraft.white_carpet": "Moqueta blanca", "block.minecraft.white_concrete": "Formigón blanco", "block.minecraft.white_concrete_powder": "Povisa de formigón blanco", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_shulker_box": "Caxa blanca de <PERSON>ker", "block.minecraft.white_stained_glass": "Vid<PERSON> tintao en blanco", "block.minecraft.white_stained_glass_pane": "Panel de vidru tintao en blanco", "block.minecraft.white_terracotta": "Terracota blanco", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blan<PERSON>", "block.minecraft.white_wool": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.wildflowers": "Flores Monteses", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON><PERSON> de wither", "block.minecraft.wither_skeleton_wall_skull": "Craniu de wither na parede", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON> mariella", "block.minecraft.yellow_candle": "<PERSON><PERSON> mariella", "block.minecraft.yellow_candle_cake": "Tarta con una vela mariella", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON> mariella", "block.minecraft.yellow_concrete": "Formigón mariello", "block.minecraft.yellow_concrete_powder": "Povisa de formigón mariello", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> tintao en mariello", "block.minecraft.yellow_stained_glass_pane": "Panel de vidru tintao en mariello", "block.minecraft.yellow_terracotta": "Terracota mariello", "block.minecraft.yellow_wool": "<PERSON><PERSON>", "block.minecraft.zombie_head": "Tiesta de zombi", "block.minecraft.zombie_wall_head": "Tiesta de zombi na parede", "book.byAuthor": "por %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Titula'l llibru:", "book.finalizeButton": "<PERSON><PERSON> y zarrar", "book.finalizeWarning": "¡Nota! <PERSON><PERSON>do robles el llibru yá nun va poder editase.", "book.generation.0": "Orixinal", "book.generation.1": "Copia del orixinal", "book.generation.2": "Copia d'una copia", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Etiqueta de llibru inválida *", "book.pageIndicator": "Páxina %1$s de %2$s", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "<PERSON><PERSON>", "book.view.title": "Book View Screen", "build.tooHigh": "La llende d'altor pa la construcción ye %s", "chat.cannotSend": "Nun se pue unviar el mensaxe", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Calca pa teletresportate", "chat.copy": "Copialu", "chat.copy.click": "Calca pa copiar al cartafueyu", "chat.deleted_marker": "<PERSON>sti mensaxe de chat foi esborráu pol servidor.", "chat.disabled.chain_broken": "Conversación desactivada por cadenamu rotu. Por favor, inténtalo a conectar.", "chat.disabled.expiredProfileKey": "Conversación desactivada pola clave pública del perfil caducada. Por favor, intentalo a conectar.", "chat.disabled.invalid_command_signature": "La comanda tenía signatures d'argumentu de comanda inesperaes o ausentes.", "chat.disabled.invalid_signature": "La conversación tenía una firma inválida. Por favor, inténtalo volver a conectar.", "chat.disabled.launcher": "Conversación desactivada pola opción del llanzador. Non se pue unviar mensaxe.", "chat.disabled.missingProfileKey": "Conversación desactivada pola clave pública del perfil ausente. Por favor, intentalo a conectar.", "chat.disabled.options": "Chat desconetau neles opciones de cliente\"\"", "chat.disabled.out_of_order_chat": "Conversación recibida fora d'orde. ¿Camudó la hora del to sistema?", "chat.disabled.profile": "La conversación nun ta permitida pola configuración de la cuenta. Preme '%s' de nuevu pa más información.", "chat.disabled.profile.moreInfo": "Les conversaciones nun tán permitíes poles configuraciones de la cuenta. Nun se puen unviar nin ver mensaxes.", "chat.editBox": "charra", "chat.filtered": "Filtráu pol servidor.", "chat.filtered_full": "El servidor oxultó'l to mensaxe pa dalgunos xugadores.", "chat.link.confirm": "¿De xuru que quies abrir el sitiu web de darréu?", "chat.link.confirmTrusted": "¿Quies abrir esti enllaz o copialu al cartafueyu?", "chat.link.open": "<PERSON><PERSON><PERSON>", "chat.link.warning": "¡Nun abras los enllaces de xente na que nun t'enfotes!", "chat.queue": "[+%s llinies pendientes]", "chat.square_brackets": "[%s]", "chat.tag.error": "El servidor envió un mensaxe inválido.", "chat.tag.modified": "Mensaxe modificáu pol servidor. Orixinal:", "chat.tag.not_secure": "Mensaxe non verificado. Non pue ser informáu.", "chat.tag.system": "Mensaxe del servidor. Non pue ser informáu.", "chat.tag.system_single_player": "Mensaxe del servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s completó'l retu %s", "chat.type.advancement.goal": "%s algamó la meta %s", "chat.type.advancement.task": "%s fizo l'avance %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Unviar un mensaxe al equipu", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s diz %s", "chat.validation_error": "Error de validación de la conversación", "chat_screen.message": "Mensaxe a unviar: %s", "chat_screen.title": "<PERSON><PERSON><PERSON>", "chat_screen.usage": "Introduz un mensaxe y primi Intro pa unviar", "chunk.toast.checkLog": "Consulta'l rexistru pa más detalles", "chunk.toast.loadFailure": "Fallu al cargar los fragmentos en %s", "chunk.toast.lowDiskSpace": "Espaciu de discu baxu!", "chunk.toast.lowDiskSpace.description": "Quiciáis nun se pueda guardar el mundu.", "chunk.toast.saveFailure": "Fallu al guardar chunk en %s", "clear.failed.multiple": "<PERSON>un s'<PERSON>aron oxe<PERSON> en %s xugadores", "clear.failed.single": "Nun s'<PERSON>aron oxetos nel xugador %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Azul", "color.minecraft.brown": "<PERSON>r<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON><PERSON>", "color.minecraft.green": "Verde", "color.minecraft.light_blue": "<PERSON><PERSON>l claro", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON> claro", "color.minecraft.lime": "Llima", "color.minecraft.magenta": "<PERSON>ent<PERSON>", "color.minecraft.orange": "Naranxa", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.red": "Coloráu", "color.minecraft.white": "<PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[EQUÍ]", "command.context.parse_error": "%s na posición %s: %s", "command.exception": "Nun pudo analizase'l comandu: %s", "command.expected.separator": "Esperábase un espaciu en blancu p'acabar un argumentu mas atopáronse datos ensin separtar", "command.failed": "Asocedió un fallu al tentar d'executar esi comandu", "command.forkLimit": "Númberu máximu de contestos (%s) algamáu", "command.unknown.argument": "L'argumentu d'esti comandu ye correutu", "command.unknown.command": "Desconozse'l comandu o ta incompletu, mira'l fallu de darr<PERSON>u", "commands.advancement.criterionNotFound": "L'avance «%1$s» nun contién el criteriu «%2$s»", "commands.advancement.grant.criterion.to.many.failure": "Nun pudo concedese'l criteriu «%s» del avance %s a %s xugadores darréu que yá lu tienen", "commands.advancement.grant.criterion.to.many.success": "Concedióse'l criteriu «%s» del avance %s a %s xugadores", "commands.advancement.grant.criterion.to.one.failure": "Nun pudo concedese'l criteriu «%s» del avance %s a %s darréu que yá lu tien", "commands.advancement.grant.criterion.to.one.success": "Concedióse'l criteriu «%s» del avance %s a %s", "commands.advancement.grant.many.to.many.failure": "Nun se pudieron conceder %s avances a %s xugadores darréu que yá los tienen", "commands.advancement.grant.many.to.many.success": "Concediéronse %s avances a %s xugadores", "commands.advancement.grant.many.to.one.failure": "Nun se pudieron conceder %s avances a %s darréu que yá los tienen", "commands.advancement.grant.many.to.one.success": "Concediéronse %s avances a %s", "commands.advancement.grant.one.to.many.failure": "<PERSON>un pudo concedese l'avance %s a %s xugadores darréu que yá lu tienen", "commands.advancement.grant.one.to.many.success": "Concedióse l'avance %s a %s xugadores", "commands.advancement.grant.one.to.one.failure": "<PERSON>un pudo concedese l'avance %s a %s darréu que yá lu tienen", "commands.advancement.grant.one.to.one.success": "Concedióse l'avance %s a %s", "commands.advancement.revoke.criterion.to.many.failure": "Nun pudo revocase'l criteriu «%s» del avance %s de %s xugadores darréu que nun lu tienen", "commands.advancement.revoke.criterion.to.many.success": "Revocóse'l criteriu «%s» del avance %s de %s xugadores", "commands.advancement.revoke.criterion.to.one.failure": "Nun pudo revocase'l criteriu «%s» del avance %s de %s darréu que nun lu tienen", "commands.advancement.revoke.criterion.to.one.success": "Revocóse'l criteriu «%s» del avance %s de %s", "commands.advancement.revoke.many.to.many.failure": "Nun se pudieron revocar %s avances de %s xugadores darréu que nun los tienen", "commands.advancement.revoke.many.to.many.success": "Revocáronse %s avances de %s xugadores", "commands.advancement.revoke.many.to.one.failure": "Nun se pudieron revocar %s avances de %s darréu que nun los tien", "commands.advancement.revoke.many.to.one.success": "Revocáronse %s avances de %s", "commands.advancement.revoke.one.to.many.failure": "Nun pudo revocase l'avance %s de %s xugadores darréu que nun lu tienen", "commands.advancement.revoke.one.to.many.success": "Revocóse l'avance %s de %s xugadores", "commands.advancement.revoke.one.to.one.failure": "Nun pudo revocase l'avance %s de %s darréu que nun lu tien", "commands.advancement.revoke.one.to.one.success": "Revocóse l'avance %s de %s", "commands.attribute.base_value.get.success": "El valor base del atributu «%s» de la entidá %s ye %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Base value for attribute %s for entity %s set to %s", "commands.attribute.failed.entity": "%s is not a valid entity for this command", "commands.attribute.failed.modifier_already_present": "Modifier %s is already present on attribute %s for entity %s", "commands.attribute.failed.no_attribute": "La entidá «%s» nun tien nengún atributu «%s»", "commands.attribute.failed.no_modifier": "Attribute %s for entity %s has no modifier %s", "commands.attribute.modifier.add.success": "Added modifier %s to attribute %s for entity %s", "commands.attribute.modifier.remove.success": "Removed modifier %s from attribute %s for entity %s", "commands.attribute.modifier.value.get.success": "El valor del modificador %s nel atributu «%s» de la entidá %s ye %s", "commands.attribute.value.get.success": "El valor del atributu «%s» de la entidá %s ye %s", "commands.ban.failed": "<PERSON>un camudó nada. El xugador yá ta espulsáu", "commands.ban.success": "Espulsóse a %s: %s", "commands.banip.failed": "<PERSON>un camudó nada. Esa IP yá ta espulsada", "commands.banip.info": "This ban affects %s player(s): %s", "commands.banip.invalid": "La direición IP nun ye válida o nun se conoz al xugador", "commands.banip.success": "IP espulsada %s: %s", "commands.banlist.entry": "%2$s espulsó a %1$s: %3$s", "commands.banlist.entry.unknown": "(Desconocío)", "commands.banlist.list": "There are %s ban(s):", "commands.banlist.none": "<PERSON>un hai espulsiones", "commands.bossbar.create.failed": "Ya esiste una barra de xefe cola ID «%s»", "commands.bossbar.create.success": "Creóse la barra de xefe personalizada %s", "commands.bossbar.get.max": "La barra de xefe personalizada %s tien un máximu de %s", "commands.bossbar.get.players.none": "Anguaño la barra de xefe personalizada %s nun tien xugadores en llinia", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "La barra de xefe personalizada %s tien el valor de %s", "commands.bossbar.get.visible.hidden": "Anguaño la barra de xefe personalizada %s ta anubrida", "commands.bossbar.get.visible.visible": "Anguaño amúesase la barra de xefe personalizada %s", "commands.bossbar.list.bars.none": "<PERSON>un hai barres de xefe actives", "commands.bossbar.list.bars.some": "There are %s custom bossbar(s) active: %s", "commands.bossbar.remove.success": "Desanicióse la barra de xefe personalizada %s", "commands.bossbar.set.color.success": "Camudó'l color de la barra de xefe personalizada %s", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON> camudó nada. Esi yá ye'l color d'esta barra de xefe", "commands.bossbar.set.max.success": "La barra de xefe personalizada %s camudó'l máximu a %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON> camudó nada. Esi yá ye'l máximu d'esta barra de xefe", "commands.bossbar.set.name.success": "Renomóse la barra de xefe personalizada %s", "commands.bossbar.set.name.unchanged": "<PERSON>un camudó nada. Esi yá ye'l nome d'esta barra de xefe", "commands.bossbar.set.players.success.none": "La barra de xefe personalizada %s yá nun tien xugadores", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "<PERSON>un camudó nada. Esos xugadores yá tán na barra de xefe ensin naide p'amestar o desaniciar", "commands.bossbar.set.style.success": "Camudó l'estilu de la barra de xefe personalizada %s", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON> camudó nada. Esi yá ye l'estilu d'esta barra de xefe", "commands.bossbar.set.value.success": "La barra de xefe personalizada %s camudó'l valor a %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON> camudó nada. Esi yá ye'l valor d'esta barra de xefe", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON> camudó nada. La barra de xefe yá ta anubrida", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON> camudó nada. La barra de xefe yá ye visible", "commands.bossbar.set.visible.success.hidden": "Agora la barra de xefe %s ta anubrida", "commands.bossbar.set.visible.success.visible": "Agora la barra de xefe %s ye visible", "commands.bossbar.unknown": "Nun esiste nenguna barra de xefe cola ID «%s»", "commands.clear.success.multiple": "Removed %s item(s) from %s players", "commands.clear.success.single": "Removed %s item(s) from player %s", "commands.clear.test.multiple": "Found %s matching item(s) on %s players", "commands.clear.test.single": "Found %s matching item(s) on player %s", "commands.clone.failed": "Nun se clonaron bloques", "commands.clone.overlap": "The source and destination areas cannot overlap", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Hai milenta bloques nel área especificáu (el maximu ye %s, el númberu especificáu foi %s)", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied %s damage to %s", "commands.data.block.get": "%s nel bloque en %s, %s, %s dempués d'escalar el fautor de %s ye %s", "commands.data.block.invalid": "El bloque de destín nun ye una entidá de bloques", "commands.data.block.modified": "Modificáronse los datos del bloque en %s %s %s", "commands.data.block.query": "El bloque en %s %s %s tien los datos de darréu: %s", "commands.data.entity.get": "%s en %s dempués d'escalar el fautor de %s ye %s", "commands.data.entity.invalid": "Unable to modify player data", "commands.data.entity.modified": "Modificáronse los datos de la entidá %s", "commands.data.entity.query": "La entidá de %s tien los datos de darréu: %s", "commands.data.get.invalid": "Nun pue consiguise %s, namás se permiten etiquetes numbériques", "commands.data.get.multiple": "This argument accepts a single NBT value", "commands.data.get.unknown": "Nun pue consiguise %s, la etiqueta nun esiste", "commands.data.merge.failed": "<PERSON><PERSON> camudó nada. Les propiedaes especificaes yá tienen esos valores", "commands.data.modify.expected_list": "Expected list, got: %s", "commands.data.modify.expected_object": "Expected object, got: %s", "commands.data.modify.expected_value": "Expected value, got: %s", "commands.data.modify.invalid_index": "Invalid list index: %s", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": "%s nel contenedor %s, tres un factor escala de %s, ye %s", "commands.data.storage.modified": "Modificóse l'almacenamientu %s", "commands.data.storage.query": "L'almacenamientu %s tien el conteníu de darréu: %s", "commands.datapack.create.already_exists": "Ya esiste un paquete con ese nome: %s", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Caracteres inválidus nel nuevo nome del paquete: %s", "commands.datapack.create.io_failure": "No se pudo crear un paquete col nome «%s», revisa los rexistros", "commands.datapack.create.metadata_encode_failure": "Fallu al codificar los metadatus del paquete col nome «%s»: %s", "commands.datapack.create.success": "Creóse un nuevu paquete vacío col nome «%s»", "commands.datapack.disable.failed": "¡El paquete «%s» nun ta activáu!", "commands.datapack.disable.failed.feature": "¡El paquete de datos «%s» nun pue desactivase porque ye parte d'una de les variables actives!", "commands.datapack.enable.failed": "¡El paquete «%s» yá ta activáu!", "commands.datapack.enable.failed.no_flags": "Pack '%s' cannot be enabled, since required flags are not enabled in this world: %s!", "commands.datapack.list.available.none": "Nun hai más paquetes de datos disponibles", "commands.datapack.list.available.success": "There are %s data pack(s) available: %s", "commands.datapack.list.enabled.none": "<PERSON>un hai paquetes de datos disponibles", "commands.datapack.list.enabled.success": "There are %s data pack(s) enabled: %s", "commands.datapack.modify.disable": "Desactivando'l paquete de datos %s", "commands.datapack.modify.enable": "Activando'l paquete de datos %s", "commands.datapack.unknown": "Desconozse'l paquete de datos «%s»", "commands.debug.alreadyRunning": "The tick profiler is already started", "commands.debug.function.noRecursion": "Can't trace from inside of function", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "Traced %s command(s) from %s functions to output file %s", "commands.debug.function.success.single": "Traced %s command(s) from function '%s' to output file %s", "commands.debug.function.traceFailed": "Failed to trace function", "commands.debug.notRunning": "The tick profiler hasn't started", "commands.debug.started": "Started tick profiling", "commands.debug.stopped": "Stopped tick profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.defaultgamemode.success": "Agora'l mou de xuegu predetermináu ye %s", "commands.deop.failed": "<PERSON><PERSON> camudó nada. El xugador nun ye un operador", "commands.deop.success": "%s yá nun ye un operador del sirvidor", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "La dificultá nun camudó, yá s'afitó a %s", "commands.difficulty.query": "La dificultá ye %s", "commands.difficulty.success": "La dificultá afitóse a %s", "commands.drop.no_held_items": "La entidá nun pue llevar nengún oxetu", "commands.drop.no_loot_table": "Entity %s has no loot table", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": "Dropped %s items", "commands.drop.success.multiple_with_table": "Dropped %s items from loot table %s", "commands.drop.success.single": "Dropped %s %s", "commands.drop.success.single_with_table": "Dropped %s %s from loot table %s", "commands.effect.clear.everything.failed": "L'oxetivu nun tien efeutos a desaniciar", "commands.effect.clear.everything.success.multiple": "Desanicióse cada efeutu de %s oxetivos", "commands.effect.clear.everything.success.single": "Desanicióse cada efeutu de %s", "commands.effect.clear.specific.failed": "L'oxetivu nun tien l'efeutu solicitáu", "commands.effect.clear.specific.success.multiple": "Desanicióse l'efeutu %s de %s oxetivos", "commands.effect.clear.specific.success.single": "Desanicióse l'efeutu %s de %s", "commands.effect.give.failed": "Nun pue aplicase esti efeutu (l'oxetivu ye inmune a los efeutos o tien daqué más fuerte)", "commands.effect.give.success.multiple": "Aplicóse l'efeutu %s a %s oxetivos", "commands.effect.give.success.single": "Aplicóse l'efeutu %s a %s", "commands.enchant.failed": "<PERSON><PERSON> camudó nada. Los oxetivos nun tienen dengún oxetu nes manes o l'encantexu nun pudo aplicase", "commands.enchant.failed.entity": "%s is not a valid entity for this command", "commands.enchant.failed.incompatible": "%s nun sofita esi encantexu", "commands.enchant.failed.itemless": "%s nun lleva ne<PERSON> oxe<PERSON>", "commands.enchant.failed.level": "%s is higher than the maximum level of %s supported by that enchantment", "commands.enchant.success.multiple": "Aplicóse l'encantexu %s a %s entidaes", "commands.enchant.success.single": "Aplicóse l'encantexu %s al oxetu de %s", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.execute.conditional.fail": "Falló la prueba", "commands.execute.conditional.fail_count": "Test failed, count: %s", "commands.execute.conditional.pass": "Superóse la prueba", "commands.execute.conditional.pass_count": "Test passed, count: %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "Diéronse %s niveles d'esperiencia a %s xugadores", "commands.experience.add.levels.success.single": "Diéronse %s niveles d'esperiancia a %s", "commands.experience.add.points.success.multiple": "Diéronse %s puntos d'esperiencia a %s xugadores", "commands.experience.add.points.success.single": "Diéronse %s puntos d'esperiencia a %s", "commands.experience.query.levels": "%s tien %s niveles d'esperiencia", "commands.experience.query.points": "%s tien %s puntos d'esperiencia", "commands.experience.set.levels.success.multiple": "Afitáronse %s niveles d'esperiencia en %s xugadores", "commands.experience.set.levels.success.single": "Afitáronse %s niveles d'esperiencia en %s", "commands.experience.set.points.invalid": "Nun se puen afitar los puntos d'esperiencia penriba'l máximu de puntos del nivel actual del xugador", "commands.experience.set.points.success.multiple": "Afitáronse %s puntos d'esperiencia en %s xugadores", "commands.experience.set.points.success.single": "Afitáronse %s puntos d'esperiencia en %s", "commands.fill.failed": "No blocks were filled", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.fillbiome.success": "Biomes set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": "%s biome entry/entries set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum %s, specified %s)", "commands.forceload.added.failure": "Nun se marcaron chunks pa cargalos a la fuercia", "commands.forceload.added.multiple": "Marcáronse %s chunks en %s dende %s hasta %s pa cargalos a la fuercia", "commands.forceload.added.none": "Nun s'atoparon chunks cargaos a la fuercia en %s", "commands.forceload.added.single": "Marcóse'l chunk %s en %s pa cargalu a la fuercia", "commands.forceload.list.multiple": "Atopáronse %s chunks cargaos a la fuercia dientro de %s en: %s", "commands.forceload.list.single": "Atopóse un chunk cargáu a la fuercia dientro de %s en: %s", "commands.forceload.query.failure": "El chunk en %s dientro de %s nun ta marcáu pa cargalu a la fuercia", "commands.forceload.query.success": "El chunk en %s dientro de %s ta marcáu pa cargalu a la fuercia", "commands.forceload.removed.all": "Desmarcáronse tolos chunks cargaos a la fuercia en %s", "commands.forceload.removed.failure": "Nun se desaniciaron chunks de la carga a la fuercia", "commands.forceload.removed.multiple": "Desmarcáronse %s chunks en %s dende %s hasta %s pa cargalos a la fuercia", "commands.forceload.removed.single": "Desmarcóse'l chunk %s en %s pa cargalu a la fuercia", "commands.forceload.toobig": "Hai milenta chunks nel área especificáu (el máximu ye %s, el númberu especificáu foi %s)", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "Afitóse'l mou de xuegu de %s a %s", "commands.gamemode.success.self": "Afitóse'l to mou de xuegu a %s", "commands.gamerule.query": "Anguaño la regla de xuegu %s ta afitada a %s", "commands.gamerule.set": "Agora la regla de xuegu %s ta afitada a: %s", "commands.give.failed.toomanyitems": "Can't give more than %s of %s", "commands.give.success.multiple": "Dióse %s de %s a %s xugadores", "commands.give.success.single": "Dióse %s de %s a %s", "commands.help.failed": "Desconozse'l comandu o nun tienes abondos permisos", "commands.item.block.set.success": "Trocóse una ralura en %s, %s, %s con %s", "commands.item.entity.set.success.multiple": "Trocóse una ralura en %s entidaes con %s", "commands.item.entity.set.success.single": "Trocóse una ralura en %s con %s", "commands.item.source.no_such_slot": "L'orixe nun tien la ralura %s", "commands.item.source.not_a_container": "La posición d'orixe %s, %s, %s nun ye un contenedor", "commands.item.target.no_changed.known_item": "No targets accepted item %s into slot %s", "commands.item.target.no_changes": "No targets accepted item into slot %s", "commands.item.target.no_such_slot": "L'oxetivu nun tien la ralura %s", "commands.item.target.not_a_container": "La posición de destín %s, %s, %s nun ye un contenedor", "commands.jfr.dump.failed": "Failed to dump JFR recording: %s", "commands.jfr.start.failed": "Failed to start JFR profiling", "commands.jfr.started": "JFR profiling started", "commands.jfr.stopped": "JFR profiling stopped and dumped to %s", "commands.kick.owner.failed": "Cannot kick server owner in LAN game", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "Echóse a %s: %s", "commands.kill.success.multiple": "Matáronse a %s entidaes", "commands.kill.success.single": "Matóse a %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hai %s d'un máximu de %s xugadores en llinia: %s", "commands.locate.biome.not_found": "Nun pudo atopase'l bioma del tipu «%s» dientro d'una distancia razonable", "commands.locate.biome.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.poi.not_found": "Could not find a point of interest of type \"%s\" within reasonable distance", "commands.locate.poi.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.structure.invalid": "There is no structure with type \"%s\"", "commands.locate.structure.not_found": "Could not find a structure of type \"%s\" nearby", "commands.locate.structure.success": "La estructura %s más cercana ta en %s (a %s bloques de distancia)", "commands.message.display.incoming": "%s burbusóte: %s", "commands.message.display.outgoing": "Burbusésti-y a %s: %s", "commands.op.failed": "<PERSON><PERSON> camudó nada. El xugador yá ye un operador", "commands.op.success": "Fízose a %s un operador del sirvidor", "commands.pardon.failed": "<PERSON>un camudó nada. El xugador nun ta espulsáu", "commands.pardon.success": "Realmitióse a %s", "commands.pardonip.failed": "<PERSON>un camudó nada. Esa IP nun ta espulsada", "commands.pardonip.invalid": "Direición IP non válida", "commands.pardonip.success": "IP realmitida %s", "commands.particle.failed": "La partícula nun foi visible pa naide", "commands.particle.success": "Amosando partícula %s", "commands.perf.alreadyRunning": "The performance profiler is already started", "commands.perf.notRunning": "The performance profiler hasn't started", "commands.perf.reportFailed": "Fallu al crear l'informe de depuración", "commands.perf.reportSaved": "Creóse un informe de depuración en %s", "commands.perf.started": "Started 10 second performance profiling run (use '/perf stop' to stop early)", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "Failed to place feature", "commands.place.feature.invalid": "There is no feature with type \"%s\"", "commands.place.feature.success": "Colocóse «%s» en %s, %s, %s", "commands.place.jigsaw.failed": "Failed to generate jigsaw", "commands.place.jigsaw.invalid": "There is no template pool with type \"%s\"", "commands.place.jigsaw.success": "Generated jigsaw at %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON><PERSON> al xenerar estructura", "commands.place.structure.invalid": "There is no structure with type \"%s\"", "commands.place.structure.success": "Generated structure \"%s\" at %s, %s, %s", "commands.place.template.failed": "Failed to place template", "commands.place.template.invalid": "Nun hai nenguna plantía cola ID «%s»", "commands.place.template.success": "Loaded template \"%s\" at %s, %s, %s", "commands.playsound.failed": "El soníu ta mui lloñe como pa oyelu", "commands.playsound.success.multiple": "Reprodúxose'l soníu %s a %s xugadores", "commands.playsound.success.single": "Reprodúxose'l soníu %s a %s", "commands.publish.alreadyPublished": "Multiplayer game is already hosted on port %s", "commands.publish.failed": "Nun ye posible agospiar la partida llocal", "commands.publish.started": "Esta partida llocal ta agospiándose nel puertu %s", "commands.publish.success": "Agora la partida de dellos xugadores agóspiase nel puertu %s", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "Nun se deprendieron recetes nueves", "commands.recipe.give.success.multiple": "Desbloquiáronse %s recetes pa %s xugadores", "commands.recipe.give.success.single": "Desbloquiáronse %s recetes pa %s", "commands.recipe.take.failed": "Nun pudo escaecése nenguna receta", "commands.recipe.take.success.multiple": "Coyéronse %s recetes de %s xugadores", "commands.recipe.take.success.single": "Coyéronse %s recetes de %s", "commands.reload.failure": "Reload failed; keeping old data", "commands.reload.success": "¡Recargando!", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s stopped riding %s", "commands.ride.mount.failure.cant_ride_players": "Players can't be ridden", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "Can't mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Yá se desactivó'l guardáu", "commands.save.alreadyOn": "Yá s'activó'l guardáu", "commands.save.disabled": "Agora'l guardáu automáticu ta desactiváu", "commands.save.enabled": "Agora'l guardáu automáticu ta activáu", "commands.save.failed": "Nun pue guardase la partida (¿hai abondu espaciu nel discu?)", "commands.save.saving": "Guardando la partida (¡esto pue tardar un momentu!)", "commands.save.success": "Guardóse la partida", "commands.schedule.cleared.failure": "Nun hai planificaciones cola ID %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Scheduled tag '%s' in %s tick(s) at gametime %s", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "<PERSON>un pue programase'l tick actual", "commands.scoreboard.objectives.add.duplicate": "Yá esiste un oxetivu con esi nome", "commands.scoreboard.objectives.add.success": "Creóse l'oxetivu %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nothing changed. That display slot is already empty", "commands.scoreboard.objectives.display.alreadySet": "Nothing changed. That display slot is already showing that objective", "commands.scoreboard.objectives.display.cleared": "Llimpióse cualesquier oxetivu na ralura d'amuesa %s", "commands.scoreboard.objectives.display.set": "Afitóse la ralura d'amuesa %s p'amosar l'oxetivu %s", "commands.scoreboard.objectives.list.empty": "<PERSON>un hai oxetivos", "commands.scoreboard.objectives.list.success": "There are %s objective(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": "Camudó'l nome a amosar de %s a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": "Changed the render type of objective %s", "commands.scoreboard.objectives.remove.success": "Desanicióse l'oxetivu %s", "commands.scoreboard.players.add.success.multiple": "Amestóse %s a %s pa %s entidaes", "commands.scoreboard.players.add.success.single": "Amestóse %s a %s pa %s (agora %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "<PERSON>un camudó nada. Esi aicionador yá ta activáu", "commands.scoreboard.players.enable.invalid": "Enable only works on trigger-objectives", "commands.scoreboard.players.enable.success.multiple": "Activóse l'aicionador %s pa %s entidaes", "commands.scoreboard.players.enable.success.single": "Habilitóse l'aicionador %s pa %s", "commands.scoreboard.players.get.null": "Can't get value of %s for %s; none is set", "commands.scoreboard.players.get.success": "%s tien %s %s", "commands.scoreboard.players.list.empty": "Nun hai entidaes rastrexaes", "commands.scoreboard.players.list.entity.empty": "%s nun tien puntuaciones p'amosar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s Tien %spuntu(s):", "commands.scoreboard.players.list.success": "Hai %s entidá(es) rexistra(es): %s", "commands.scoreboard.players.operation.success.multiple": "Anovóse %s pa %s entidaes", "commands.scoreboard.players.operation.success.single": "Cambióse la puntuación del oxetivu %s pa %s a %s", "commands.scoreboard.players.remove.success.multiple": "Desaniciśoe %s de %s pa %s entidaes", "commands.scoreboard.players.remove.success.single": "Desanicióse %s de %s pa %s (agora %s)", "commands.scoreboard.players.reset.all.multiple": "Reaniciáronse toles puntuaciones de %s entidaes", "commands.scoreboard.players.reset.all.single": "Reaniciáronse toles puntuaciones de %s", "commands.scoreboard.players.reset.specific.multiple": "Reafitóse %s pa %s entidaes", "commands.scoreboard.players.reset.specific.single": "Reafitóse %s pa %s", "commands.scoreboard.players.set.success.multiple": "Afitóse %s pa %s entidaes a %s", "commands.scoreboard.players.set.success.single": "Afitóse %s pa %s a %s", "commands.seed.success": "Semiente: %s", "commands.setblock.failed": "<PERSON>un pudo afitase'l bloque", "commands.setblock.success": "Camudóse'l bloque en %s, %s, %s", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Namás se pue colocar el puntu de spawn del mundu", "commands.setworldspawn.success": "Afitóse l'aprucideru del mundu a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Establecer el puntu de spawn en %s, %s, %s [%s] en %s pa %s xugadores", "commands.spawnpoint.success.single": "Establecer el puntu de spawn en %s, %s, %s [%s] en %s pa %s", "commands.spectate.not_spectator": "%s nun ta nel mou d'espectador", "commands.spectate.self": "<PERSON>un pues espectate a ti mesmu", "commands.spectate.success.started": "Espectando a %s", "commands.spectate.success.stopped": "<PERSON><PERSON> nun tas espectando a una entidá", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "Invalid maxHeight %s; expected higher than world minimum %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s entity/entities around %s, %s with an average distance of %s block(s) apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s block(s) apart", "commands.stop.stopping": "Parando'l sirvidor", "commands.stopsound.success.source.any": "Paráronse tolos soníos de «%s»", "commands.stopsound.success.source.sound": "Parós<PERSON>'l soníu «%s» na fonte «%s»", "commands.stopsound.success.sourceless.any": "Par<PERSON><PERSON><PERSON> tolos soníos", "commands.stopsound.success.sourceless.sound": "Parós<PERSON>'l soníu «%s»", "commands.summon.failed": "Nun pue invocase a la entidá", "commands.summon.failed.uuid": "Nun ye posible invocar la entidá pola mor d'UUIDs duplicaes", "commands.summon.invalidPosition": "Invalid position for summon", "commands.summon.success": "Invocóse %s", "commands.tag.add.failed": "L'oxetivu yá tien la etiqueta o tien milenta", "commands.tag.add.success.multiple": "Amestóse la etiqueta «%s» a %s entidaes", "commands.tag.add.success.single": "Amestóse la etiqueta «%s» a %s", "commands.tag.list.multiple.empty": "Nun hai etiquetes nes %s entidaes", "commands.tag.list.multiple.success": "Les %s entidaes tienen %s etiquetes en total: %s", "commands.tag.list.single.empty": "%s nun tien etiquetes", "commands.tag.list.single.success": "%s tien %s etiques: %s", "commands.tag.remove.failed": "El destín nun tien esta etiqueta", "commands.tag.remove.success.multiple": "Desanicióse la etiqueta «%s» de les %s entidaes", "commands.tag.remove.success.single": "Desanicióse la etiqueta «%s» de %s", "commands.team.add.duplicate": "Yá esiste un equipu con esti nome", "commands.team.add.success": "Creóse l'equipu %s", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "<PERSON><PERSON> camudó nada. Esi equipu yá ta baleru", "commands.team.join.success.multiple": "Amestáronse a %s al equipu %s", "commands.team.join.success.single": "Amestóse a %s al equipu %s", "commands.team.leave.success.multiple": "Quitáronse a %s miembros de tolos equipos", "commands.team.leave.success.single": "Quitóse a %s de tolos equipos", "commands.team.list.members.empty": "Nun hai miembros nel equipu %s", "commands.team.list.members.success": "Team %s has %s member(s): %s", "commands.team.list.teams.empty": "<PERSON>un hai equipos", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "Agora la regla de colisiones pal equipu %s ye «%s»", "commands.team.option.collisionRule.unchanged": "Nothing changed. Collision rule is already that value", "commands.team.option.color.success": "Anovóse'l color del equipu %s a %s", "commands.team.option.color.unchanged": "<PERSON>un camudó nada. Esi equipu ta tien esi color", "commands.team.option.deathMessageVisibility.success": "Agora la visibilidá del mensaxe de muerte pal equipu %s ye de «%s»", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON>un camudó nada. La visibilidá del mensaxe de muerte yá tien esi valor", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON>un camudó nada. El fueu amigu yá ta desactiváu pa esti equipu", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON>un camudó nada. El fueu amigu yá ta activáu pa esi equipu", "commands.team.option.friendlyfire.disabled": "Desactivóse'l fueu amigu pal equipu %s", "commands.team.option.friendlyfire.enabled": "Activóse'l fueu amigu pal equipu %s", "commands.team.option.name.success": "Anovóse'l nome del equipu %s", "commands.team.option.name.unchanged": "<PERSON>un camudó nada. L'equipu yá tien esi nome", "commands.team.option.nametagVisibility.success": "Agora la visibilidá de la etiqueta del equipu %s ye «%s»", "commands.team.option.nametagVisibility.unchanged": "<PERSON>un camudó nada. La visibilidá de la etiqueta yá tien esi valor", "commands.team.option.prefix.success": "Team prefix set to %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON>un camudó nada. Esi equipu yá nun pue ver a los compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON>un camudó nada. Esi equipu yá pue ver a los compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "L'equipu %s yá nun pue ver a compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "Agora l'equipu %s pue ver a compañeros invisibles", "commands.team.option.suffix.success": "Team suffix set to %s", "commands.team.remove.success": "Desanicióse l'equipu %s", "commands.teammsg.failed.noteam": "Has tar nun equipu pa unviar un mensaxe al de to", "commands.teleport.invalidPosition": "Invalid position for teleport", "commands.teleport.success.entity.multiple": "Teletresportáronse %s entidaes a %s", "commands.teleport.success.entity.single": "Teletresportóse %s hasta %s", "commands.teleport.success.location.multiple": "Teletresportáronse %s entidaes a %s, %s, %s", "commands.teleport.success.location.single": "Teletresportóse %s hasta %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "<PERSON><PERSON> s'<PERSON><PERSON> nenguna prueba", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "Superáronse toles pruebes riquíes :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiles: P50: %sms P95: %sms P99: %sms, sample: %s", "commands.tick.query.rate.running": "Target tick rate: %s per second.\nAverage time per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick rate: %s per second (ignored, reference only).\nAverage time per tick: %sms", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": "The game is running, but can't keep up with the target tick rate", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "La hora ye %s ticks", "commands.time.set": "Afitóse'l tiempu a %s", "commands.title.cleared.multiple": "Llimpiáronse los títulos pa %s xuagores", "commands.title.cleared.single": "Llimpiáronse los títulos pa %s", "commands.title.reset.multiple": "Reafitáronse les opciones del títulu pa %s xugadores", "commands.title.reset.single": "Reafitáronse les opciones del títulu pa %s", "commands.title.show.actionbar.multiple": "Amosando'l títulu de la barra d'aiciones nueva pa %s xugadores", "commands.title.show.actionbar.single": "Amosando'l títulu de la barra nueva d'aiciones pa %s", "commands.title.show.subtitle.multiple": "Amosando'l sotítulu nuevu pa %s xugadores", "commands.title.show.subtitle.single": "Amosando'l sotítulu nuevu pa %s", "commands.title.show.title.multiple": "Amosando'l títulu nuevu pa %s xugadores", "commands.title.show.title.single": "Amosand'l títulu nuevu pa %s", "commands.title.times.multiple": "Camudáronse les vegaes d'amuesa del títulu pa %s xugadores", "commands.title.times.single": "Camudáronse les vegaes d'amuesa del títulu pa %s", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "Aicionóse %s (amestóse %s al valor)", "commands.trigger.failed.invalid": "Namás pues aicionar los oxetivos que son del tipu «trigger»", "commands.trigger.failed.unprimed": "Entá nun pues aicionar esti oxetivu", "commands.trigger.set.success": "Aicionóse %s (afitóse'l valor a %s)", "commands.trigger.simple.success": "Aicionóse %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Afitóse'l clima a Estenao", "commands.weather.set.rain": "Afitóse'l clima a Lloviu", "commands.weather.set.thunder": "Afitóse'l clima a Tormenta", "commands.whitelist.add.failed": "El xugador yá ta na llista blanca", "commands.whitelist.add.success": "Amestóse a %s a la llista blanca", "commands.whitelist.alreadyOff": "La llista blanca yá ta desactivada", "commands.whitelist.alreadyOn": "La llista blanca yá ta activada", "commands.whitelist.disabled": "Agora la llista blanca ta desactivada", "commands.whitelist.enabled": "Agora la llista blanca ta activada", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "Nun hai xugadores na llista blanca", "commands.whitelist.reloaded": "Recargóse la llista blanca", "commands.whitelist.remove.failed": "El xugador nun ta na llista blanca", "commands.whitelist.remove.success": "Desanicióse a %s de la llista blanca", "commands.worldborder.center.failed": "<PERSON>un camudó nada. El berbesu del mundu yá ta centráu ehí", "commands.worldborder.center.success": "Afitóse'l centru del mundu a %s, %s", "commands.worldborder.damage.amount.failed": "Nothing changed. The world border damage is already that amount", "commands.worldborder.damage.amount.success": "Set the world border damage to %s per block each second", "commands.worldborder.damage.buffer.failed": "Nothing changed. The world border damage buffer is already that distance", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to %s block(s)", "commands.worldborder.get": "The world border is currently %s block(s) wide", "commands.worldborder.set.failed.big": "El berbesu del mundu nun pue tener más de %s bloques de llargor", "commands.worldborder.set.failed.far": "World border cannot be further out than %s blocks", "commands.worldborder.set.failed.nochange": "<PERSON>un camudó nada. El berbesu del mundu yá ye d'esi tamañu", "commands.worldborder.set.failed.small": "El berbesu del mundu nun pue tener un llargor de menos de 1 bloque", "commands.worldborder.set.grow": "Aumentando'l berbesu del mundu a %s bloques de distancia en %s segundos", "commands.worldborder.set.immediate": "Set the world border to %s block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "Nothing changed. The world border warning is already that distance", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "Nothing changed. The world border warning is already that amount of time", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": "You've been playing for greater than 24 hours", "compliance.playtime.hours": "You've been playing for %s hour(s)", "compliance.playtime.message": "Excessive gaming may interfere with normal daily life", "connect.aborted": "Albortóse", "connect.authorizing": "<PERSON><PERSON><PERSON><PERSON>…", "connect.connecting": "<PERSON>ec<PERSON>do col sirvidor…", "connect.encrypting": "<PERSON><PERSON><PERSON><PERSON>…", "connect.failed": "Fallu al conectar col sirvidor", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "Xuniéndote al mundu…", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON>…", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "Reconfiguring...", "connect.transferring": "Transferring to new server...", "container.barrel": "Barril", "container.beacon": "Baliza", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "Horno de altos hornos", "container.brewing": "Soporte de elaboración de pociones", "container.cartography_table": "Mesa de cartografía", "container.chest": "Cofre", "container.chestDouble": "Bagul grande", "container.crafter": "<PERSON><PERSON><PERSON>", "container.crafting": "Ellaboración", "container.creative": "<PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "Dispensador", "container.dropper": "<PERSON><PERSON><PERSON>", "container.enchant": "Encantexu", "container.enchant.clue": "¿%s . . . ?", "container.enchant.lapis.many": "%s de llapislázuli", "container.enchant.lapis.one": "1 de llapislázuli", "container.enchant.level.many": "%s niveles", "container.enchant.level.one": "1 nivel", "container.enchant.level.requirement": "Level Requirement: %s", "container.enderchest": "<PERSON><PERSON><PERSON> ender", "container.furnace": "<PERSON><PERSON>", "container.grindstone_title": "Iguar y desencantexar", "container.hopper": "Embudu", "container.inventory": "Inventario", "container.isLocked": "¡El %s ta peslláu!", "container.lectern": "Atril", "container.loom": "Telar", "container.repair": "<PERSON><PERSON><PERSON> y nomar", "container.repair.cost": "Costu d'encantexu: %1$s", "container.repair.expensive": "¡<PERSON>caru!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "y %s más…", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Smoker", "container.spectatorCantOpen": "<PERSON>un pue abrise. Entá nun se xeneró'l botín.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Upgrade Gear", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "Add <PERSON> Template", "controls.keybinds": "Arreyos de tecles…", "controls.keybinds.duplicateKeybinds": "Esta clave tamién s'usa pa:\n%s", "controls.keybinds.title": "Arreyos de tecles", "controls.reset": "Reafitar", "controls.resetAll": "Reafitar les tecles", "controls.title": "Controles", "createWorld.customize.buffet.biome": "Esbilla un bioma", "createWorld.customize.buffet.title": "Personalización del mundu a la carta", "createWorld.customize.flat.height": "<PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Llende baxera - %s", "createWorld.customize.flat.layer.top": "Llende cimera - %s", "createWorld.customize.flat.removeLayer": "Desaniciar la capa", "createWorld.customize.flat.tile": "Capes de material", "createWorld.customize.flat.title": "Personalización d'un mundu superplanu", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ¡equí hai daqué que ficiéremos enantes!", "createWorld.customize.presets.select": "Usar el preaxuste", "createWorld.customize.presets.share": "¿Quies compartir el preaxuste con daquién? ¡Usa la caxa d'abaxo!", "createWorld.customize.presets.title": "Esbilla d'un preaxuste", "createWorld.preparing": "Tresnando la creación del mundu...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Más", "createWorld.tab.world.title": "Mu", "credits_and_attribution.button.attribution": "Atribución", "credits_and_attribution.button.credits": "Creitos", "credits_and_attribution.button.licenses": "Llicencies", "credits_and_attribution.screen.title": "Creitos y Atribución", "dataPack.bundle.description": "Enables experimental Bundle item", "dataPack.bundle.name": "Bundles", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Movimientu am<PERSON> pa Minecarts", "dataPack.minecart_improvements.name": "Ameyoramientos pa Minecarts", "dataPack.redstone_experiments.description": "Cambeos Esperimentales de Redstone", "dataPack.redstone_experiments.name": "Esperimentos de Redstone", "dataPack.title": "Esbilla de paquetes de datos", "dataPack.trade_rebalance.description": "Updated trades for Villagers", "dataPack.trade_rebalance.name": "Reequilibriu del trueque de los vilanos", "dataPack.update_1_20.description": "Nuevas carauterístiques y conteníu pa Minecraft 1.20", "dataPack.update_1_20.name": "Actualización 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "Actualización 1.21", "dataPack.validation.back": "<PERSON>r <PERSON>'<PERSON><PERSON><PERSON>", "dataPack.validation.failed": "¡La validación del paquete de datos falló!", "dataPack.validation.reset": "Reafitar", "dataPack.validation.working": "Validando los paquetes de datos seleicionaos...", "dataPack.vanilla.description": "Los datos predeterminaos de Minecraft", "dataPack.vanilla.name": "Por defecto", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "Este mundo contiene datos de guardado inválidos o corruptos.", "datapackFailure.safeMode.failed.title": "Error al cargar el mundo en Modo Seguro.", "datapackFailure.title": "Los erros nos paquetes de datos seleicionaos impiden que'l mundu se carregue.\nPue los cargar namá cola paquete de datos de serie (\"en modo seguráu\") o volver a la pantalla principal y arreglar la situación de manera manual.", "death.attack.anvil": "Una xunca que cayía estartó a %1$s", "death.attack.anvil.player": "Una xunca que cayía estartó a %1$s mentanto lluchaba escontra %2$s", "death.attack.arrow": "%2$s disparó a %1$s", "death.attack.arrow.item": "%2$s disparó a %1$s usando %3$s", "death.attack.badRespawnPoint.link": "Diseñu intencionáu del xuegu", "death.attack.badRespawnPoint.message": "%2$s mató a %1$s", "death.attack.cactus": "%1$s morrió ente escayos", "death.attack.cactus.player": "%1$s apaincó haza un cactus mentanto %2$s lu escorría", "death.attack.cramming": "%1$s achapló un poco muncho", "death.attack.cramming.player": "%2$s estartó a %1$s", "death.attack.dragonBreath": "%1$s foi asaeteáu pola follestïa del dragón", "death.attack.dragonBreath.player": "%1$s foi asaeteáu na follestïa del dragón por %2$s", "death.attack.drown": "%1$s afogóse", "death.attack.drown.player": "%1$s afogóse mentanto tentaba d'afuxir de %2$s", "death.attack.dryout": "%1$s morrió pola deshidratación", "death.attack.dryout.player": "%1$s morrió pola deshidratación mentanto tentaba d'afuxir de %2$s", "death.attack.even_more_magic": "Más maxa mató a %1$s", "death.attack.explosion": "%1$s voló pelos aires", "death.attack.explosion.player": "%2$s fizo volar pelos aires a %1$s", "death.attack.explosion.player.item": "%2$s fizo volar pelos aires a %1$s usando %3$s", "death.attack.fall": "%1$s pegóse un gochazu perfuerte", "death.attack.fall.player": "%1$s pegóse un bon gochazu mentanto tentaba d'afuxir de %2$s", "death.attack.fallingBlock": "Un bloque que cayía estartó a %1$s", "death.attack.fallingBlock.player": "Un bloque que cayía estartó a %1$s mentanto lluchaba escontra %2$s", "death.attack.fallingStalactite": "%1$s foi espetáu por un estalactito que caió", "death.attack.fallingStalactite.player": "%1$s foi espetáu por un estalactito que caió mentres luuchaba contra %2$s", "death.attack.fireball": "%1$s morrió por una bola de fueu que-y aventó %2$s", "death.attack.fireball.item": "%1$s morrió por una bola de fueu que-y aventó %2$s usando %3$s", "death.attack.fireworks": "%1$s pegó un españíu", "death.attack.fireworks.item": "%1$s explotó con un estuoiu por una boiñia tirada dende %3$s por %2$s", "death.attack.fireworks.player": "%1$s convirtióse en volador mentanto lluchaba escontra %2$s", "death.attack.flyIntoWall": "%1$s esperimentó enerxía cinética", "death.attack.flyIntoWall.player": "%1$s esperimentó enerxía cinética mentanto tentaba d'afuxir de %2$s", "death.attack.freeze": "%1$s morrió por conxelamientu", "death.attack.freeze.player": "%2$s mató por conxelamientu a %1$s", "death.attack.generic": "%1$s morrió", "death.attack.generic.player": "%1$s morrió pola mor de %2$s", "death.attack.genericKill": "%1$s foi asesináu", "death.attack.genericKill.player": "%1$s foi asesináu mentres luuchaba contra %2$s", "death.attack.hotFloor": "%1$s descubrió que'l suelu yera llava", "death.attack.hotFloor.player": "%1$s entró na zona de peligru por mor de %2$s", "death.attack.inFire": "%1$s amburó", "death.attack.inFire.player": "%1$s caminó sobre fueu mentanto lluchaba escontra %2$s", "death.attack.inWall": "%1$s afogóse nuna parede", "death.attack.inWall.player": "%1$s afogóse nuna parede mentanto lluchaba escontra %2$s", "death.attack.indirectMagic": "%2$s mató a %1$s col usu de maxa", "death.attack.indirectMagic.item": "%2$s mató a %1$s usando %3$s", "death.attack.lava": "%1$s tentó de nalar na llava", "death.attack.lava.player": "%1$s tentó de nalar na llava p'afuxir de %2$s", "death.attack.lightningBolt": "Un rayu dio en %1$s", "death.attack.lightningBolt.player": "Un rayu dió en %1$s mentanto lluchaba escontra %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "La maxa mató a %1$s", "death.attack.magic.player": "%1$s morrió pola maxa mentanto tentaba d'afuxir de %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON><PERSON>, mensaxe yera demostráu ser muncho llargu pa ser entregáu por completu. ¡Perdón! Equí tien una versión afayada: %s", "death.attack.mob": "%2$s asesinó a %1$s", "death.attack.mob.item": "%2$s asesinó a %1$s usando %3$s", "death.attack.onFire": "%1$s morrió por quemadures", "death.attack.onFire.item": "%1$s foi asadíu hasta quedase braxáu mentes lluchaba contra %2$s, blandiendo %3$s", "death.attack.onFire.player": "%1$s convirtióse en ceniza mentanto lluchaba escontra %2$s", "death.attack.outOfWorld": "%1$s cayó del mundu", "death.attack.outOfWorld.player": "%1$s nun quería vivir nel mesmu mundu que %2$s", "death.attack.outsideBorder": "%1$s dexó los llindes d'esti mundu", "death.attack.outsideBorder.player": "%1$s dexó los llindes d'esti mundu mentes lluchaba contra %2$s", "death.attack.player": "%1$s foi asasináu por %2$s", "death.attack.player.item": "%1$s foi esguinzado por %2$s usando %3$s", "death.attack.sonic_boom": "%1$s foi aniquilado por un chillíu cargáu de soníos", "death.attack.sonic_boom.item": "%1$s foi aniquilado por un chillíu cargáu de soníos mientres trataba d'escapar de %2$s esgrimiendo %3$s", "death.attack.sonic_boom.player": "%1$s foi aniquilado por un chillíu cargáu de soníos mientres trataba d'escapar de %2$s", "death.attack.stalagmite": "%1$s foi empalado numa estalagmite", "death.attack.stalagmite.player": "%1$s foi empalado numa estalagmite mientres lluchaba contra %2$s", "death.attack.starve": "%1$s morrió de fame", "death.attack.starve.player": "%1$s morrió de fame mentanto lluchaba escontra %2$s", "death.attack.sting": "%1$s morrió por picadures", "death.attack.sting.item": "%1$s foi picado até à morte por %2$s usando %3$s", "death.attack.sting.player": "%1$s morrió poles picadures de %2$s", "death.attack.sweetBerryBush": "%1$s foi picado até a morte por um arbusto de bagas doces", "death.attack.sweetBerryBush.player": "%1$s foi picado até a muerte por un arbustu de bayes dulces mientres intentaba escaper de %2$s", "death.attack.thorns": "%1$s mat<PERSON>e tentado de mancar a %2$s", "death.attack.thorns.item": "%3$s mató a %1$s tentando de mancar a %2$s", "death.attack.thrown": "%2$s aporrió a %1$s", "death.attack.thrown.item": "%2$s tundió a %1$s col usu de %3$s", "death.attack.trident": "%2$s fincó'l tridente en %1$s", "death.attack.trident.item": "%2$s fincó'l tridente en %1$s con %3$s", "death.attack.wither": "%1$s escayó", "death.attack.wither.player": "%1$s escayó mentanto lluchaba escontra %2$s", "death.attack.witherSkull": "%1$s foi atingido por um tiro de um crânio de %2$s", "death.attack.witherSkull.item": "%1$s was shot by a skull from %2$s using %3$s", "death.fell.accident.generic": "%1$s cayó d'un llugar altu", "death.fell.accident.ladder": "%1$s cayó d'una escalera de mano", "death.fell.accident.other_climbable": "%1$s cayó mientres escalaba", "death.fell.accident.scaffolding": "%1$s cayó d'un andamiu", "death.fell.accident.twisting_vines": "%1$s cayó dalgunes vides torcies", "death.fell.accident.vines": "%1$s cayó del enromadoriu", "death.fell.accident.weeping_vines": "%1$s cayó dalgunes vides lloroses", "death.fell.assist": "%1$s taba condergáu a cayer por %2$s", "death.fell.assist.item": "%1$s taba condergáu a cayer por %2$s usando %3$s", "death.fell.finish": "%1$s cayó perlloñe y %2$s rematólu", "death.fell.finish.item": "%1$s cayó perlloñe y %2$s rematólu usando %3$s", "death.fell.killer": "%1$s taba condergáu a cayer", "deathScreen.quit.confirm": "¿De xuru que quies colar?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuación", "deathScreen.score.value": "Puntuación: %s", "deathScreen.spectate": "<PERSON><PERSON> Mu<PERSON>u", "deathScreen.title": "¡Morriesti!", "deathScreen.title.hardcore": "¡Partida finada!", "deathScreen.titleScreen": "Pantalla de títulos", "debug.advanced_tooltips.help": "F3 + H = Descripciones avanzaes", "debug.advanced_tooltips.off": "Anúbrense les descripciones avanzaes", "debug.advanced_tooltips.on": "Amuésense les descripciones avanzaes", "debug.chunk_boundaries.help": "F3 + G - Amues<PERSON> les llendes de los chunks", "debug.chunk_boundaries.off": "Anúbrense los berbesos de los chunks", "debug.chunk_boundaries.on": "Amuésense los berbesos de los chunks", "debug.clear_chat.help": "F3 + D - Llimpia la charra", "debug.copy_location.help": "F3 + C = Copy location as /tp command, hold F3 + C to crash the game", "debug.copy_location.message": "Copióse l'allugamientu al cartafueyu", "debug.crash.message": "F3 + C is held down. This will crash the game unless released.", "debug.crash.warning": "Cascando en %s…", "debug.creative_spectator.error": "Nun ye posible cambiar el mou de xuegu porque nun tienes permisu", "debug.creative_spectator.help": "F3 + N = Cycle previous game mode <-> spectator", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": "Unable to open game mode switcher; no permission", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON><PERSON> el conmutador de moos de xuegu", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s <PERSON><PERSON><PERSON><PERSON>", "debug.help.help": "F3 + Q - Amuesa esta llista", "debug.help.message": "Arreyos de tecles:", "debug.inspect.client.block": "Copiáronse al cartafueyu los datos de bloques del llau del veceru", "debug.inspect.client.entity": "Copiáronse al cartafueyu los datos d'entidaes del llau del veceru", "debug.inspect.help": "F3 + I = Copia los datos d'entidá o bloque al cartafueyu", "debug.inspect.server.block": "Copiáronse al cartafueyu los datos de bloques del llau del sirvidor", "debug.inspect.server.entity": "Copiáronse al cartafueyu los datos d'entidaes del llau del sirvidor", "debug.pause.help": "F3 + Esc - <PERSON>sa ensin el menú de posa (si ye posible)", "debug.pause_focus.help": "F3 + P - Posa na perda del focu de la ventana", "debug.pause_focus.off": "Desactivóse la posa al perder el focu", "debug.pause_focus.on": "Activóse la posa al perder el focu", "debug.prefix": "[Depuración]:", "debug.profiling.help": "F3 + L = Start/stop profiling", "debug.profiling.start": "Profiling started for %s seconds. Use F3 + L to stop early", "debug.profiling.stop": "Profiling ended. Saved results to %s", "debug.reload_chunks.help": "F3 + A - Recarga los chunks", "debug.reload_chunks.message": "Recargando tolos chunks", "debug.reload_resourcepacks.help": "F3 + T - Recar<PERSON> los paquetes de recursos", "debug.reload_resourcepacks.message": "Recargáronse los paquetes de recursos", "debug.show_hitboxes.help": "F3 + B = Amuesa les caxes de colisión", "debug.show_hitboxes.off": "Caxes de colisión: anubríes", "debug.show_hitboxes.on": "Caxes de colisión: amosaes", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Esta demo va durar 5 díes de xuegu, ¡esfórciate!", "demo.day.2": "<PERSON><PERSON> dos", "demo.day.3": "Día tres", "demo.day.4": "Día cuatro", "demo.day.5": "¡Esti ye l'últimu día!", "demo.day.6": "Pasesti'l quintu día, usa %s pa facer una semeya de la to creación.", "demo.day.warning": "¡El tiempu va escosar ceo!", "demo.demoExpired": "¡Escosó'l tiempu de la demo!", "demo.help.buy": "¡Comprar agora!", "demo.help.fullWrapped": "Esta demo va durar 5 díes (al redor d'una hora y 40 minutos na vida real). ¡Comprueba los avances pa tener pistes y diviértite!", "demo.help.inventory": "Usa %1$s p'abrir l'inventariu", "demo.help.jump": "Blinca primiendo la tecla %1$s", "demo.help.later": "¡Siguir xugando!", "demo.help.movement": "Usa les tecles %1$s, %2$s, %3$s, %4$s y el mur pa movete pela redolada", "demo.help.movementMouse": "Mira al redor usando'l mur", "demo.help.movementShort": "Móvite primiendo %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Mou demo de Minecraft", "demo.remainingTime": "Tiempu restante: %s", "demo.reminder": "Escosó'l tiempu de la demo. ¡Compra'l xuegu pa siguir o entama un mundu nuevu!", "difficulty.lock.question": "¿De xuru que quies bloquiar la dificultá d'esti mundu? Esto va facer que la dificultá seya siempres %1$s y nun vas ser a camudala de nueves.", "difficulty.lock.title": "Bloquéu de la dificultá del mundu", "disconnect.endOfStream": "Fin de la tresmisión", "disconnect.exceeded_packet_rate": "Kicked for exceeding packet rate limit", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.loginFailedInfo": "Fallu al aniciar sesión: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El mou multijugador ta desactiváu. Comprueba los axustes de la to cuenta de Microsoft, por favor.", "disconnect.loginFailedInfo.invalidSession": "La sesión nun ye válida. Prueba a reaniciar el xuegu y el llanzador", "disconnect.loginFailedInfo.serversUnavailable": "Los sirvidores d'autenticación nun tán disponibles. <PERSON><PERSON>alo, por favor.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": "Perdióse la conexón", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "Echáronte por facer spam", "disconnect.timeout": "Escosó'l tiempu d'espera", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "Agospiador desconocíu", "download.pack.failed": "%s out of %s pack(s) failed to download", "download.pack.progress.bytes": "Progress: %s (total size unknown)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Downloading resource pack %s/%s", "editGamerule.default": "Por defeutu: %s", "editGamerule.title": "Edición de les regles de la partida", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorción", "effect.minecraft.bad_omen": "Presaxu malu", "effect.minecraft.blindness": "Cegadura", "effect.minecraft.conduit_power": "Conduit Power", "effect.minecraft.darkness": "Escuridá", "effect.minecraft.dolphins_grace": "Honor de toliña", "effect.minecraft.fire_resistance": "Resistencia al fueu", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON><PERSON>", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON> de <PERSON>", "effect.minecraft.hero_of_the_village": "Héroe popular", "effect.minecraft.hunger": "Fame", "effect.minecraft.infested": "Infested", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON> nel intre", "effect.minecraft.instant_health": "Curación nel intre", "effect.minecraft.invisibility": "Invisivilidá", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "Llevitación", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON> minera", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Visión nocherniego", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "Envelenamientu", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "Rexeneración", "effect.minecraft.resistance": "Resistencia", "effect.minecraft.saturation": "Saturación", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitú", "effect.minecraft.speed": "Velocidá", "effect.minecraft.strength": "Fuercia", "effect.minecraft.trial_omen": "Trial Omen", "effect.minecraft.unluck": "<PERSON><PERSON> mala", "effect.minecraft.water_breathing": "<PERSON><PERSON> a<PERSON>", "effect.minecraft.weakness": "Badea", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Charged", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON><PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinidá acuático", "enchantment.minecraft.bane_of_arthropods": "Velea artrópoda", "enchantment.minecraft.binding_curse": "Maldición d'atadura", "enchantment.minecraft.blast_protection": "Antiespañíos", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Channeling", "enchantment.minecraft.density": "Densidá", "enchantment.minecraft.depth_strider": "Axilidá acuático", "enchantment.minecraft.efficiency": "Eficiencia", "enchantment.minecraft.feather_falling": "Lliviandá", "enchantment.minecraft.fire_aspect": "Aspeutu amburador", "enchantment.minecraft.fire_protection": "Proteición escontra'l fueu", "enchantment.minecraft.flame": "Fleches amburadores", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "<PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Infinidá", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "Llealtá", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON> de <PERSON>", "enchantment.minecraft.lure": "Cebu", "enchantment.minecraft.mending": "Igua", "enchantment.minecraft.multishot": "Multitiru", "enchantment.minecraft.piercing": "Perforación", "enchantment.minecraft.power": "Fuercia", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "Proteición", "enchantment.minecraft.punch": "Truñada", "enchantment.minecraft.quick_charge": "Carga rápida", "enchantment.minecraft.respiration": "Aliendu", "enchantment.minecraft.riptide": "Impul<PERSON> a<PERSON>", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Toque de seda", "enchantment.minecraft.smite": "Redención", "enchantment.minecraft.soul_speed": "Velocidá d'ánimes", "enchantment.minecraft.sweeping": "Filu atropador", "enchantment.minecraft.sweeping_edge": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "Swift Sneak", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Durabilidá", "enchantment.minecraft.vanishing_curse": "Maldición de desapaición", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Barca d'Alcacia", "entity.minecraft.acacia_chest_boat": "Barca d'Alcacia con Bagul", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Ñube d'efeutos n'área", "entity.minecraft.armadillo": "Armadillu", "entity.minecraft.armor_stand": "Armor Stand", "entity.minecraft.arrow": "Fle<PERSON>", "entity.minecraft.axolotl": "Axolote", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON> Bambú", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "Barca de Bidul", "entity.minecraft.birch_chest_boat": "Barca de Bidul con Bagul", "entity.minecraft.blaze": "Espírit<PERSON> ll<PERSON>", "entity.minecraft.block_display": "Block Display", "entity.minecraft.boat": "Barca", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Wind Charge", "entity.minecraft.camel": "Camel", "entity.minecraft.cat": "Gatu", "entity.minecraft.cave_spider": "<PERSON><PERSON>", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minecart with Chest", "entity.minecraft.chicken": "Pita", "entity.minecraft.cod": "Bacalláu", "entity.minecraft.command_block_minecart": "Minecart with Command Block", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Toliña", "entity.minecraft.donkey": "Pollín", "entity.minecraft.dragon_fireball": "Bola de fueu de cuélebre", "entity.minecraft.drowned": "Afogáu", "entity.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON> vieyu", "entity.minecraft.end_crystal": "End Crystal", "entity.minecraft.ender_dragon": "Cuélebre d'Ender", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON> d<PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Invocador", "entity.minecraft.evoker_fangs": "Caniles d'Invocador", "entity.minecraft.experience_bottle": "T<PERSON><PERSON> Bottle o' Enchanting", "entity.minecraft.experience_orb": "Bola d'esperiencia", "entity.minecraft.eye_of_ender": "Eye of <PERSON>er", "entity.minecraft.falling_block": "Bloque que cai", "entity.minecraft.falling_block_type": "Falling %s", "entity.minecraft.fireball": "<PERSON><PERSON> de <PERSON>ueu", "entity.minecraft.firework_rocket": "Firework Rocket", "entity.minecraft.fishing_bobber": "Fishing Bobber", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Xaronca", "entity.minecraft.furnace_minecart": "Minecart with Furnace", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Xigante", "entity.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardián", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minecart with <PERSON>", "entity.minecraft.horse": "Caballu", "entity.minecraft.husk": "<PERSON><PERSON>", "entity.minecraft.illusioner": "Ilusionista", "entity.minecraft.interaction": "Interaction", "entity.minecraft.iron_golem": "Gólem de fierro", "entity.minecraft.item": "Oxe<PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "El coneyu asesín", "entity.minecraft.leash_knot": "<PERSON><PERSON><PERSON>", "entity.minecraft.lightning_bolt": "<PERSON><PERSON>", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON> ll<PERSON>", "entity.minecraft.magma_cube": "<PERSON><PERSON><PERSON> ma<PERSON>", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "Minecart", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Spawner de oxetos siniestros", "entity.minecraft.painting": "Painting", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> brutu", "entity.minecraft.pillager": "Saquiador", "entity.minecraft.player": "Xugador", "entity.minecraft.polar_bear": "Osu polar", "entity.minecraft.potion": "Potion", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Salmón", "entity.minecraft.sheep": "Oveya", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Proyeutil de shulker", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Cadarma", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>", "entity.minecraft.slime": "<PERSON><PERSON><PERSON>", "entity.minecraft.small_fireball": "Bola de fueu pequeña", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Gólem de nieve", "entity.minecraft.snowball": "Snowball", "entity.minecraft.spawner_minecart": "Vagoneta con spawner de monstruos", "entity.minecraft.spectral_arrow": "Spectral Arrow", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "Vagamundu", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Cucharapa", "entity.minecraft.text_display": "Text Display", "entity.minecraft.tnt": "TNT activada", "entity.minecraft.tnt_minecart": "Minecart with TNT", "entity.minecraft.trader_llama": "Llama de viaxante", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Tropical Fish", "entity.minecraft.tropical_fish.predefined.0": "Amphiprion chrysopterus", "entity.minecraft.tropical_fish.predefined.1": "Zebrasoma rostratum", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON> cornutus", "entity.minecraft.tropical_fish.predefined.11": "Chaetodon ornatissimus", "entity.minecraft.tropical_fish.predefined.12": "Scaridae", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON> ciliaris", "entity.minecraft.tropical_fish.predefined.14": "Cichlidae", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "Red Snapper", "entity.minecraft.tropical_fish.predefined.17": "Polynemidae", "entity.minecraft.tropical_fish.predefined.18": "Amphi<PERSON><PERSON> frenatus", "entity.minecraft.tropical_fish.predefined.19": "Balistidae", "entity.minecraft.tropical_fish.predefined.2": "Paracanth<PERSON> hepatus", "entity.minecraft.tropical_fish.predefined.20": "Scaridae", "entity.minecraft.tropical_fish.predefined.21": "Zebras<PERSON> flavescens", "entity.minecraft.tropical_fish.predefined.3": "Chaetodontidae", "entity.minecraft.tropical_fish.predefined.4": "Cichlidae", "entity.minecraft.tropical_fish.predefined.5": "Pexe payasu", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON> splendens", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>e", "entity.minecraft.tropical_fish.predefined.9": "Mullidae", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blockfish", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Clayfish", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitter", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "Spotty", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "Tortuga", "entity.minecraft.vex": "Picuscayu", "entity.minecraft.villager": "Villager", "entity.minecraft.villager.armorer": "Ferreru d'armadures", "entity.minecraft.villager.butcher": "Carniceru", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "Granxeru", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotecariu", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Folganzán", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "Oveyeru", "entity.minecraft.villager.toolsmith": "Ferreru de pre<PERSON>os", "entity.minecraft.villager.weaponsmith": "Armeru", "entity.minecraft.vindicator": "Vindicador", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Carga de vientu", "entity.minecraft.witch": "Bruxa", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON> de wither", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON> de wither", "entity.minecraft.wolf": "Llobu", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "C<PERSON><PERSON><PERSON> zombi", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> z<PERSON>i", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "Invasión", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Raid - Defeat", "event.minecraft.raid.raiders_remaining": "Saquiadores restantes: %s", "event.minecraft.raid.victory": "Victoria", "event.minecraft.raid.victory.full": "Raid - Victory", "filled_map.buried_treasure": "Mapa d'ayalga soterrada", "filled_map.explorer_jungle": "Jungle Explorer Map", "filled_map.explorer_swamp": "Swamp Explorer Map", "filled_map.id": "ID #%s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Locked", "filled_map.mansion": "Mapa d'esplorador de viesques", "filled_map.monument": "Mapa d'esplorador oceánicu", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Trial Explorer Map", "filled_map.unknown": "Mapa desconocíu", "filled_map.village_desert": "Desert Village Map", "filled_map.village_plains": "Plains Village Map", "filled_map.village_savanna": "Savanna Village Map", "filled_map.village_snowy": "Snowy Village Map", "filled_map.village_taiga": "Taiga Village Map", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON> ensin fondu", "flat_world_preset.minecraft.classic_flat": "Llanada clásica", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Superficie", "flat_world_preset.minecraft.redstone_ready": "Perfeuto pa redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "El vacíu", "flat_world_preset.minecraft.tunnelers_dream": "Suañu del mineru", "flat_world_preset.minecraft.water_world": "Mundu d'agua", "flat_world_preset.unknown": "¿¿??", "gameMode.adventure": "<PERSON><PERSON> a<PERSON>", "gameMode.changed": "El to mou de xuegu anovóse a %s", "gameMode.creative": "<PERSON><PERSON> creativu", "gameMode.hardcore": "¡Mou perdifícil!", "gameMode.spectator": "<PERSON><PERSON> espectador", "gameMode.survival": "Mou sobrevivencia", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "Anunciar los avances", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Miscellaneous", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "<PERSON><PERSON><PERSON>", "gamerule.category.updates": "Anovamientos del mundu", "gamerule.commandBlockOutput": "Broadcast command block output", "gamerule.commandModificationBlockLimit": "Command modification block limit", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": "Disable elytra movement check", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "Desactivar les invasiones", "gamerule.doDaylightCycle": "Advance time of day", "gamerule.doEntityDrops": "Drop entity equipment", "gamerule.doEntityDrops.description": "Controls drops from minecarts (including inventories), item frames, boats, etc.", "gamerule.doFireTick": "<PERSON><PERSON>r el fueu", "gamerule.doImmediateRespawn": "Remanecimientu nel intre", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON> pantas<PERSON>", "gamerule.doLimitedCrafting": "<PERSON><PERSON><PERSON><PERSON> recetes pa ellaborar", "gamerule.doLimitedCrafting.description": "If enabled, players will be able to craft only unlocked recipes.", "gamerule.doMobLoot": "Drop mob loot", "gamerule.doMobLoot.description": "Controls resource drops from mobs, including experience orbs.", "gamerule.doMobSpawning": "Aprucir creatures", "gamerule.doMobSpawning.description": "Some entities might have separate rules.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON>", "gamerule.doTileDrops": "Drop blocks", "gamerule.doTileDrops.description": "Controls resource drops from blocks, including experience orbs.", "gamerule.doTraderSpawning": "<PERSON><PERSON>r vendedores errantes", "gamerule.doVinesSpread": "Vines spread", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other types of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "Spawn guardianes", "gamerule.doWeatherCycle": "Anovar el clima", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON> por afogamientu", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender <PERSON> vanish on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether Ender <PERSON> thrown by a player vanish when that player dies.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "<PERSON><PERSON><PERSON> por cayer", "gamerule.fireDamage": "<PERSON><PERSON><PERSON> por quemase", "gamerule.forgiveDeadPlayers": "Perdonar a los xugadores muertos", "gamerule.forgiveDeadPlayers.description": "Les creatures neutrales que tean enfadaes van dexar de talo cuando'l xugador qu'escuerran muerra cierca.", "gamerule.freezeDamage": "<PERSON><PERSON><PERSON> por conxelamientu", "gamerule.globalSoundEvents": "Global sound events", "gamerule.globalSoundEvents.description": "When certain game events happen, like a boss spawning, the sound is heard everywhere.", "gamerule.keepInventory": "Caltener l'inventariu al morrer", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "When flowing lava is surrounded on two sides by lava sources it converts into a source.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Broadcast admin commands", "gamerule.maxCommandChainLength": "Command chain size limit", "gamerule.maxCommandChainLength.description": "Applies to command block chains and functions.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": "<PERSON><PERSON><PERSON> d'entidaes per bloque", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "In mob explosions, some blocks won't drop their loot", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": "Allow destructive mob actions", "gamerule.naturalRegeneration": "Rexeneración de la salú", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "Sleep percentage", "gamerule.playersSleepingPercentage.description": "El porcentaxe de xugadores qu'han dormir pa saltar la nueche.", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": "Frecuencia de los tics al debalu", "gamerule.reducedDebugInfo": "Reduce debug info", "gamerule.reducedDebugInfo.description": "Limits contents of debug screen.", "gamerule.sendCommandFeedback": "Send command feedback", "gamerule.showDeathMessages": "Amosar los mensaxes de muerte", "gamerule.snowAccumulationHeight": "Snow accumulation height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow form on the ground up to at most this number of layers.", "gamerule.spawnChunkRadius": "Radio del fragmentu de xeneración", "gamerule.spawnChunkRadius.description": "Cantidá de fragmentos que queden cargaos alredor de la posición de xeneración del mundu.", "gamerule.spawnRadius": "<PERSON><PERSON><PERSON> aprucideru", "gamerule.spawnRadius.description": "Controla'l tamañu de la zona alredor del punto de xeneración onde los xugadores pueden apaecer.", "gamerule.spectatorsGenerateChunks": "Los espectadores xeneren terrén", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "In TNT explosions, some blocks won't drop their loot", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": "Enfadu universal", "gamerule.universalAnger.description": "Les creatures neutrales que tean enfadaes van atacar a tolos xugadores averaos. Funciona meyor si se desactiva forgiveDeadPlayers.", "gamerule.waterSourceConversion": "Water converts to source", "gamerule.waterSourceConversion.description": "When flowing water is surrounded on two sides by water sources it converts into a source.", "generator.custom": "Personalizada", "generator.customized": "Old Customized", "generator.minecraft.amplified": "AMPLIFICÁU", "generator.minecraft.amplified.info": "Avisu: namás pa esfrutar, ríquese un ordenador potente.", "generator.minecraft.debug_all_block_states": "Mou depuración", "generator.minecraft.flat": "Superplanu", "generator.minecraft.large_biomes": "Biomes grandes", "generator.minecraft.normal": "<PERSON><PERSON> defeutu", "generator.minecraft.single_biome_surface": "Bioma únicu", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Islles flotantes", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "¿Quies siguir cola edición del informe esistente o quies escartalu y crear otru?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Problem sending your report", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogues o alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Da<PERSON>én fala de o da puxu a cometer actos indecentes con menores.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging your or someone else's reputation, for example sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "Descripción:", "gui.abuseReport.reason.false_reporting": "False Reporting", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "Harassment or bullying", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Hate speech", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismu o violencia estremista", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON><PERSON> fala <PERSON>, da puxu a o amenaza con cometer actos terroristes o estremistes por motivos políticos, relixosos, ideolóxicos o d'otru tipu.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "Devolvióse un fallu mentanto s'unviaba l'informe:\n«%s»", "gui.abuseReport.send.generic_error": "Atopóse un fallu inesperáu mentanto s'unviaba l'informe.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "Unviando l'informe…", "gui.abuseReport.sent.title": "Report sent", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Nome de xugador", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": "Reconocer", "gui.advancements": "Advancements", "gui.all": "Too", "gui.back": "Atrás", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "Espulsáronte permanentemente, lo que significa que nun pues xugar en llinia nin xunite a Realms.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s - %s", "gui.banned.description.temporary": "%s Until then, you can't play online or join Realms.", "gui.banned.description.temporary.duration": "Your account is temporarily suspended and will be reactivated in %s.", "gui.banned.description.unknownreason": "Apocayá recibimos un informe por mal comportamientu de la to cuenta. Los nuesos moderadores revisaron el casu ya identificaron qu'incumples los Estándares de la Comunidá de Minecraft.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Llinguaxe abusivu gastáu de xeitu dirixíu y dañible", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Espulsión permanente", "gui.banned.title.temporary": "Cuenta suspendida temporalmente", "gui.cancel": "Encaboxar", "gui.chatReport.comments": "Comments", "gui.chatReport.describe": "Sharing details will help us make a well-informed decision.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "Leave and Discard Report", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Continue Editing", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "¿Quies siguir cola edición del informe esistente o quies escartalu y crear otru?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Continue Editing", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "Please describe what happened:", "gui.chatReport.observed_what": "Why are you reporting this?", "gui.chatReport.read_info": "Learn About Reporting", "gui.chatReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Select Chat Messages to Report", "gui.chatReport.select_reason": "Select Report Category", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "Send Report", "gui.chatReport.send.comments_too_long": "Please shorten the comment", "gui.chatReport.send.no_reason": "Please select a report category", "gui.chatReport.send.no_reported_messages": "Please select at least one chat message to report", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "Report Player Chat", "gui.chatSelection.context": "Messages surrounding this selection will be included to provide additional context", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s said: %s at %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "<PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copy Link to Clipboard", "gui.days": "%s day(s)", "gui.done": "<PERSON><PERSON>", "gui.down": "Abaxo", "gui.entity_tooltip.type": "Type: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s hour(s)", "gui.loadingMinecraft": "Loading Minecraft", "gui.minutes": "%s minute(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Botón %s", "gui.narrate.editBox": "%s edit box: %s", "gui.narrate.slider": "Eslizador %s", "gui.narrate.tab": "%s tab", "gui.no": "Non", "gui.none": "<PERSON><PERSON>", "gui.ok": "Aceptar", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "<PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "<PERSON><PERSON> derechu pa más", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Buscar…", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> too", "gui.recipebook.toggleRecipes.blastable": "Amosando lo esplosivo", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON>o lo que s'ellabora", "gui.recipebook.toggleRecipes.smeltable": "Amosando lo que se funde", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON> lo que se s'afuma", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Xestionar con una cuenta de Microsoft", "gui.socialInteractions.empty_blocked": "Nun hai xugadores bloquiaos na charra", "gui.socialInteractions.empty_hidden": "Nun hai xugadores anubríos na charra", "gui.socialInteractions.hidden_in_chat": "Van anubrise los mensaxes de %s", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON><PERSON> na charra", "gui.socialInteractions.narration.hide": "Hide messages from %s", "gui.socialInteractions.narration.report": "Report player %s", "gui.socialInteractions.narration.show": "Show messages from %s", "gui.socialInteractions.report": "Report", "gui.socialInteractions.search_empty": "Nun se pudieron atopar xugadores con esi nome", "gui.socialInteractions.search_hint": "Buscar…", "gui.socialInteractions.server_label.multiple": "%s - %s xugadores", "gui.socialInteractions.server_label.single": "%s - %s xugador", "gui.socialInteractions.show": "<PERSON>ar na charra", "gui.socialInteractions.shown_in_chat": "Van amosase los mensaxes de %s", "gui.socialInteractions.status_blocked": "Blocked", "gui.socialInteractions.status_blocked_offline": "Blocked - Offline", "gui.socialInteractions.status_hidden": "Hidden", "gui.socialInteractions.status_hidden_offline": "Hidden - Offline", "gui.socialInteractions.status_offline": "Desconectóse", "gui.socialInteractions.tab_all": "Too", "gui.socialInteractions.tab_blocked": "Bloquióse", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Interaiciones sociales", "gui.socialInteractions.tooltip.hide": "Hide messages", "gui.socialInteractions.tooltip.report": "Report player", "gui.socialInteractions.tooltip.report.disabled": "The reporting service is unavailable", "gui.socialInteractions.tooltip.report.no_messages": "No reportable messages from player %s", "gui.socialInteractions.tooltip.report.not_reportable": "This player can't be reported, because their chat messages can't be verified on this server", "gui.socialInteractions.tooltip.show": "Show messages", "gui.stats": "Estadístiques", "gui.toMenu": "Volver pa la llista de sirvidores", "gui.toRealms": "Back to Realms List", "gui.toTitle": "Volver pa la pantalla d'aniciu", "gui.toWorld": "Back to World List", "gui.togglable_slot": "Click to disable slot", "gui.up": "Arriba", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Sí", "hanging_sign.edit": "Edit Hanging Sign Message", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Call", "instrument.minecraft.dream_goat_horn": "Dream", "instrument.minecraft.feel_goat_horn": "Feel", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Seek", "instrument.minecraft.sing_goat_horn": "Sing", "instrument.minecraft.yearn_goat_horn": "Yearn", "inventory.binSlot": "<PERSON><PERSON><PERSON><PERSON>", "inventory.hotbarInfo": "Save hotbar with %1$s+%2$s", "inventory.hotbarSaved": "Item hotbar saved (restore with %1$s+%2$s)", "item.canBreak": "Pue romper:", "item.canPlace": "Pue asitiase en:", "item.canUse.unknown": "Unknown", "item.color": "Color: %s", "item.components": "%s component(s)", "item.disabled": "Disabled item", "item.durability": "Durabilidá: %s / %s", "item.dyed": "Tiñíu", "item.minecraft.acacia_boat": "Barca d'alcacia", "item.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "item.minecraft.allay_spawn_egg": "Allay Spawn Egg", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON> d'amatista", "item.minecraft.angler_pottery_shard": "Angler Pottery Shard", "item.minecraft.angler_pottery_sherd": "Angler Pottery Sherd", "item.minecraft.apple": "<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Archer Pottery Shard", "item.minecraft.archer_pottery_sherd": "Archer <PERSON>y Sherd", "item.minecraft.armadillo_scute": "Escudu d'Armadillu", "item.minecraft.armadillo_spawn_egg": "Armadillo Spawn Egg", "item.minecraft.armor_stand": "Maniquín", "item.minecraft.arms_up_pottery_shard": "Arms Up Pottery Shard", "item.minecraft.arms_up_pottery_sherd": "Arms Up Pottery Sherd", "item.minecraft.arrow": "Fle<PERSON>", "item.minecraft.axolotl_bucket": "<PERSON>u con axolote", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un axolote", "item.minecraft.baked_potato": "Pataca enforniada", "item.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "item.minecraft.bamboo_raft": "Balsa de bambú", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un esperteyu", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a una abeya", "item.minecraft.beef": "<PERSON><PERSON><PERSON> crudu", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Semiente de remolacha", "item.minecraft.beetroot_soup": "Sopa de remolacha", "item.minecraft.birch_boat": "Barca de bidul", "item.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Blade Pottery Shard", "item.minecraft.blade_pottery_sherd": "Blade Pottery Sherd", "item.minecraft.blaze_powder": "Povisa d'espíritu llumiegu", "item.minecraft.blaze_rod": "Vara d'espíritu llumiegu", "item.minecraft.blaze_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un espíritu llumiegu", "item.minecraft.blue_bundle": "Blue Bundle", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>l", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "<PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Breeze Spawn Egg", "item.minecraft.brewer_pottery_shard": "Brewer Pottery Shard", "item.minecraft.brewer_pottery_sherd": "Brewer Pottery Sherd", "item.minecraft.brewing_stand": "Brewing Stand", "item.minecraft.brick": "Lladriyu", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Cepiyu", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "Fardela", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed stack of items", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Burn Pottery Shard", "item.minecraft.burn_pottery_sherd": "Burn Pottery Sherd", "item.minecraft.camel_spawn_egg": "Camel Spawn Egg", "item.minecraft.carrot": "Cenahoria", "item.minecraft.carrot_on_a_stick": "Cenahoria nuna caña", "item.minecraft.cat_spawn_egg": "Güevu qu'apruz a un gatu", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Gü<PERSON>u qu'apruz a una araña de cueva", "item.minecraft.chainmail_boots": "Botes de cota malla", "item.minecraft.chainmail_chestplate": "Petu de cota malla", "item.minecraft.chainmail_helmet": "Cascu de cota malla", "item.minecraft.chainmail_leggings": "Pantalones de cota malla", "item.minecraft.charcoal": "Carbón vexetal", "item.minecraft.cherry_boat": "<PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON> de <PERSON> con cofre", "item.minecraft.chest_minecart": "Vagoneta con un bagul", "item.minecraft.chicken": "<PERSON>a cruda", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una pita", "item.minecraft.chorus_fruit": "Fruta del Coru", "item.minecraft.clay_ball": "<PERSON>la de bar<PERSON> santo", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbón", "item.minecraft.coast_armor_trim_smithing_template": "Plantiya de ferrería", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON><PERSON> crudu", "item.minecraft.cod_bucket": "<PERSON>u con bacalláu", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un bacalláu", "item.minecraft.command_block_minecart": "Vagoneta con un bloque de comandos", "item.minecraft.compass": "Brúxula", "item.minecraft.cooked_beef": "Filete", "item.minecraft.cooked_chicken": "Pita cocinada", "item.minecraft.cooked_cod": "Bacalláu <PERSON>cin<PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_porkchop": "Chuleta de gochu cocinada", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "Galleta", "item.minecraft.copper_ingot": "Llingote de cobre", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una vaca", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "Patrón <PERSON>'estanda<PERSON>", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON> de creeper", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un creeper", "item.minecraft.crossbow": "Ballesta", "item.minecraft.crossbow.projectile": "Proyeutil:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Proyeutil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Danger Pottery Shard", "item.minecraft.danger_pottery_sherd": "Danger Pottery Sherd", "item.minecraft.dark_oak_boat": "Barca de carbayu escuro", "item.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "item.minecraft.debug_stick": "Palu de depuración", "item.minecraft.debug_stick.empty": "%s nun tien propiedaes", "item.minecraft.debug_stick.select": "selected \"%s\" (%s)", "item.minecraft.debug_stick.update": "«%s» camudó a %s", "item.minecraft.diamond": "Diamante", "item.minecraft.diamond_axe": "<PERSON><PERSON> <PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON> de diamante", "item.minecraft.diamond_chestplate": "<PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Fesoria de diamante", "item.minecraft.diamond_horse_armor": "Armadura caballuna de diamante", "item.minecraft.diamond_leggings": "Pantalones de diamante", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Espada de diamante", "item.minecraft.disc_fragment_5": "Disc Fragment", "item.minecraft.disc_fragment_5.desc": "Vinilu - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una toliña", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un pollín", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON> con aliendu de cuélebre", "item.minecraft.dried_kelp": "Laminarial seco", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un afogáu", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "Fragmentu d'ecu", "item.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un guardián vieyu", "item.minecraft.elytra": "Élitru", "item.minecraft.emerald": "Esm<PERSON><PERSON>", "item.minecraft.enchanted_book": "Llibru encantexáu", "item.minecraft.enchanted_golden_apple": "<PERSON>zana d'oru encantexada", "item.minecraft.end_crystal": "Cristal del End", "item.minecraft.ender_dragon_spawn_egg": "Ender Dragon Spawn Egg", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un enderman", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un endermite", "item.minecraft.evoker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un evocador", "item.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> encantexada", "item.minecraft.explorer_pottery_shard": "Explorer <PERSON><PERSON> Shard", "item.minecraft.explorer_pottery_sherd": "Explorer <PERSON><PERSON> She<PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "G<PERSON>eyu d'araña <PERSON>", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Carga de fueu", "item.minecraft.firework_rocket": "Volador", "item.minecraft.firework_rocket.flight": "Duración del vuelu:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Volador d'estrella", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "Azul", "item.minecraft.firework_star.brown": "<PERSON>r<PERSON>", "item.minecraft.firework_star.custom_color": "Custom", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON>s<PERSON>ez a", "item.minecraft.firework_star.flicker": "Relluz", "item.minecraft.firework_star.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.green": "Verde", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON>l claro", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON> claro", "item.minecraft.firework_star.lime": "Llima", "item.minecraft.firework_star.magenta": "<PERSON>ent<PERSON>", "item.minecraft.firework_star.orange": "Naranxa", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "Coloráu", "item.minecraft.firework_star.shape": "Forma desconocida", "item.minecraft.firework_star.shape.burst": "Con españíu", "item.minecraft.firework_star.shape.creeper": "En forma de Creeper", "item.minecraft.firework_star.shape.large_ball": "Bola grande", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON> pe<PERSON>", "item.minecraft.firework_star.shape.star": "En forma d'estrella", "item.minecraft.firework_star.trail": "<PERSON><PERSON> r<PERSON>ru", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "Caña de pescar", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Chisqueru", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "<PERSON>", "item.minecraft.flow_banner_pattern.desc": "Flow", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "<PERSON>", "item.minecraft.flower_banner_pattern.desc": "Dibu<PERSON><PERSON> de flor", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "Flower Pot", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un raposu", "item.minecraft.friend_pottery_shard": "<PERSON> <PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON> <PERSON>", "item.minecraft.frog_spawn_egg": "Frog Spawn Egg", "item.minecraft.furnace_minecart": "Vagoneta con un fornu", "item.minecraft.ghast_spawn_egg": "Gü<PERSON>u qu'apruz a un ghast", "item.minecraft.ghast_tear": "Llá<PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Cachu de <PERSON> re<PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON>", "item.minecraft.globe_banner_pattern.desc": "Planeta", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "Bagues dulces rellumantes", "item.minecraft.glow_ink_sac": "Sacu de tinta rellumante", "item.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "item.minecraft.glow_squid_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un calamar rellumante", "item.minecraft.glowstone_dust": "Povisa de piedra rellumante", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una cabra", "item.minecraft.gold_ingot": "Llingote d'oru", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> d'oru", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>'oru", "item.minecraft.golden_axe": "<PERSON><PERSON> d'oru", "item.minecraft.golden_boots": "Botes d'oru", "item.minecraft.golden_carrot": "Cenahoria d'oru", "item.minecraft.golden_chestplate": "<PERSON><PERSON> d'oru", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Fesoria d'oru", "item.minecraft.golden_horse_armor": "Armadura caballuna d'oru", "item.minecraft.golden_leggings": "Pantalones d'oru", "item.minecraft.golden_pickaxe": "<PERSON><PERSON>", "item.minecraft.golden_shovel": "Pala d'oru", "item.minecraft.golden_sword": "Espada d'oru", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON> buxu", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "<PERSON>i<PERSON>e verde", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un guardián", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Corazón de la mar", "item.minecraft.heart_pottery_shard": "Heart Pottery Shard", "item.minecraft.heart_pottery_sherd": "Heart Pottery Sherd", "item.minecraft.heartbreak_pottery_shard": "Heartbreak Pottery Shard", "item.minecraft.heartbreak_pottery_sherd": "Heartbreak Pottery Sherd", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON> con miel", "item.minecraft.honeycomb": "Caxellar", "item.minecraft.hopper_minecart": "Vagoneta con embudu", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un caballu", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Howl Pottery Shard", "item.minecraft.howl_pottery_sherd": "Howl Pottery Sherd", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una momia", "item.minecraft.ink_sac": "<PERSON><PERSON> de tinta", "item.minecraft.iron_axe": "<PERSON><PERSON> <PERSON>ro", "item.minecraft.iron_boots": "<PERSON><PERSON> de fi<PERSON>ro", "item.minecraft.iron_chestplate": "<PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Iron Golem Spawn Egg", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "Fesoria de fi<PERSON>ro", "item.minecraft.iron_horse_armor": "Armadura caballuna de fierro", "item.minecraft.iron_ingot": "Llingote de fi<PERSON>ro", "item.minecraft.iron_leggings": "Pantalones de fierro", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON>", "item.minecraft.iron_sword": "Espada de fi<PERSON>ro", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "Barca de madera de la xungla", "item.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "item.minecraft.knowledge_book": "Llibru de conocencia", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON>u con llava", "item.minecraft.lead": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON> de cueru", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON> de <PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Leather Horse Armor", "item.minecraft.leather_leggings": "Pantalones de cueru", "item.minecraft.light_blue_bundle": "Light Blue Bundle", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON> azul claro", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON><PERSON> buxu claro", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Lime Bundle", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON> llima", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Brebaxe prollongáu", "item.minecraft.lingering_potion.effect.awkward": "Brebaxe prollongáu raru", "item.minecraft.lingering_potion.effect.empty": "Brebaxe prollongáu non ellaborable", "item.minecraft.lingering_potion.effect.fire_resistance": "Brebaxe prollongáu de resistencia al fueu", "item.minecraft.lingering_potion.effect.harming": "Brebaxe prollong<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.healing": "Brebaxe prollongáu de curación", "item.minecraft.lingering_potion.effect.infested": "Lingering Potion of Infestation", "item.minecraft.lingering_potion.effect.invisibility": "Brebaxe prollongáu d'invisibilidá", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> prollong<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.levitation": "Brebaxe prollongáu de llevitación", "item.minecraft.lingering_potion.effect.luck": "Brebaxe prollongáu de suerte", "item.minecraft.lingering_potion.effect.mundane": "Brebaxe vulgar prollongáu", "item.minecraft.lingering_potion.effect.night_vision": "Brebaxe prollongáu de visión nocherniego", "item.minecraft.lingering_potion.effect.oozing": "Lingering Potion of Oozing", "item.minecraft.lingering_potion.effect.poison": "Brebaxe prollongáu de velenu", "item.minecraft.lingering_potion.effect.regeneration": "Brebaxe prollongáu de rexeneración", "item.minecraft.lingering_potion.effect.slow_falling": "Brebaxe prollongáu de cayida lenta", "item.minecraft.lingering_potion.effect.slowness": "Brebaxe prollongáu de lentitú", "item.minecraft.lingering_potion.effect.strength": "Brebaxe prollongáu de fuercia", "item.minecraft.lingering_potion.effect.swiftness": "Brebaxe prollongáu de rapidez", "item.minecraft.lingering_potion.effect.thick": "Brebaxe prollongáu espesu", "item.minecraft.lingering_potion.effect.turtle_master": "Brebaxe prollongáu del maestru de tortugues", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON> con agua", "item.minecraft.lingering_potion.effect.water_breathing": "Brebaxe prollongáu d'aliendu acuáticu", "item.minecraft.lingering_potion.effect.weakness": "Brebaxe prollongáu de badea", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Potion of Wind Charging", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una llama", "item.minecraft.lodestone_compass": "<PERSON><PERSON><PERSON><PERSON><PERSON> imantada", "item.minecraft.mace": "Mace", "item.minecraft.magenta_bundle": "Magenta Bundle", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Crema magmática", "item.minecraft.magma_cube_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un llimu magmáticu", "item.minecraft.mangrove_boat": "Mangrove Boat", "item.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "item.minecraft.map": "Mapa baleru", "item.minecraft.melon_seeds": "Semiente de sandía", "item.minecraft.melon_slice": "Cachu de <PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON> con lleche", "item.minecraft.minecart": "Vagoneta", "item.minecraft.miner_pottery_shard": "Miner <PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "Miner <PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Llogotipu", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un mooshroom", "item.minecraft.mourner_pottery_shard": "Mourner Pottery Shard", "item.minecraft.mourner_pottery_sherd": "Mourner Pottery Sherd", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una mula", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Vinil<PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Vinil<PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Vinil<PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Vinil<PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Vinil<PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Vinil<PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Vinil<PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Vinil<PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Vinil<PERSON>", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Vinil<PERSON>", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Vinil<PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Vinil<PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Vinil<PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Vinil<PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Vinil<PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Vinil<PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Vinil<PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Vinil<PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Vinil<PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON> crudu", "item.minecraft.name_tag": "Etiqueta", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON> na<PERSON>", "item.minecraft.nether_brick": "Llad<PERSON><PERSON>her", "item.minecraft.nether_star": "Estrella del Nether", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "<PERSON><PERSON> de netherita", "item.minecraft.netherite_boots": "Botes de netherita", "item.minecraft.netherite_chestplate": "<PERSON><PERSON> de netherita", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_hoe": "Fesoria de netherita", "item.minecraft.netherite_ingot": "Llingote de netherita", "item.minecraft.netherite_leggings": "Pantalones de netherita", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON> de <PERSON>herita", "item.minecraft.netherite_scrap": "Escoria de netherita", "item.minecraft.netherite_shovel": "<PERSON>la de netherita", "item.minecraft.netherite_sword": "Espada de netherita", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "Barca de carbayu", "item.minecraft.oak_chest_boat": "Oak Boat with Chest", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un ocelote", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Orange Bundle", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "Cuadru", "item.minecraft.pale_oak_boat": "Pale Oak Boat", "item.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un panda", "item.minecraft.paper": "Papel", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un loru", "item.minecraft.phantom_membrane": "Membrana de pantasma", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un pantasma", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un gochu", "item.minecraft.piglin_banner_pattern": "<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "Focic<PERSON>", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "Piglin Brute Spawn Egg", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un piglin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un saquiador", "item.minecraft.pink_bundle": "Pink Bundle", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON><PERSON> rosa", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Pitcher Plant", "item.minecraft.pitcher_pod": "Pitcher Pod", "item.minecraft.plenty_pottery_shard": "Plenty Pottery Shard", "item.minecraft.plenty_pottery_sherd": "Plenty Pottery Sherd", "item.minecraft.poisonous_potato": "Pataca velenosa", "item.minecraft.polar_bear_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un osu polar", "item.minecraft.popped_chorus_fruit": "Fruta del Coru cocinada", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON> de gochu cruda", "item.minecraft.potato": "Pataca", "item.minecraft.potion": "Brebaxe", "item.minecraft.potion.effect.awkward": "Brebaxe raru", "item.minecraft.potion.effect.empty": "Brebaxe non ellaborable", "item.minecraft.potion.effect.fire_resistance": "Brebaxe de resistencia al fueu", "item.minecraft.potion.effect.harming": "Brebaxe de dañu", "item.minecraft.potion.effect.healing": "Brebaxe de curación", "item.minecraft.potion.effect.infested": "Potion of Infestation", "item.minecraft.potion.effect.invisibility": "Brebaxe d'invisibilidá", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> de blincu", "item.minecraft.potion.effect.levitation": "Brebaxe de llevitación", "item.minecraft.potion.effect.luck": "Brebaxe de suerte", "item.minecraft.potion.effect.mundane": "Brebaxe vulgar", "item.minecraft.potion.effect.night_vision": "Brebaxe de visión nocherniego", "item.minecraft.potion.effect.oozing": "Potion of Oozing", "item.minecraft.potion.effect.poison": "Brebaxe de velenu", "item.minecraft.potion.effect.regeneration": "Brebaxe de rexeneración", "item.minecraft.potion.effect.slow_falling": "Brebaxe de cayida lenta", "item.minecraft.potion.effect.slowness": "Brebaxe de lentitú", "item.minecraft.potion.effect.strength": "Brebaxe de fuercia", "item.minecraft.potion.effect.swiftness": "Brebaxe de rapidez", "item.minecraft.potion.effect.thick": "Brebaxe espesu", "item.minecraft.potion.effect.turtle_master": "Brebaxe del maestru de tortugues", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "Brebaxe d'aliendu acuáticu", "item.minecraft.potion.effect.weakness": "Brebaxe de badea", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "Archer Pottery Shard", "item.minecraft.pottery_shard_arms_up": "Arms Up Pottery Shard", "item.minecraft.pottery_shard_prize": "Prize <PERSON><PERSON> Shard", "item.minecraft.pottery_shard_skull": "Skull Pottery Shard", "item.minecraft.powder_snow_bucket": "Powder Snow Bucket", "item.minecraft.prismarine_crystals": "Cristales de prismarín", "item.minecraft.prismarine_shard": "Cachu de prismarín", "item.minecraft.prize_pottery_shard": "Prize <PERSON><PERSON> Shard", "item.minecraft.prize_pottery_sherd": "Prize <PERSON><PERSON> Sherd", "item.minecraft.pufferfish": "Pexe globu", "item.minecraft.pufferfish_bucket": "Calderu con pexe globu", "item.minecraft.pufferfish_spawn_egg": "Güevu qu'apruz a un pexe globu", "item.minecraft.pumpkin_pie": "Pastel de calabaza", "item.minecraft.pumpkin_seeds": "Semiente de calabaza", "item.minecraft.purple_bundle": "<PERSON><PERSON> m<PERSON>", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> crudu", "item.minecraft.rabbit_foot": "<PERSON> de coneyu", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un coneyu", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un afarador", "item.minecraft.raw_copper": "Cobre en bruto", "item.minecraft.raw_gold": "Oru en bruto", "item.minecraft.raw_iron": "Fierro en bruto", "item.minecraft.recovery_compass": "Recovery Compass", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Povisa de redstone", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.resin_clump": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON>ne a<PERSON>", "item.minecraft.saddle": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON> crudu", "item.minecraft.salmon_bucket": "Calderu con salmón", "item.minecraft.salmon_spawn_egg": "<PERSON>ü<PERSON>u qu'apruz a un salmón", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "Escudu", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "Tisoria", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una oveya", "item.minecraft.shelter_pottery_shard": "Shelter Pottery Shard", "item.minecraft.shelter_pottery_sherd": "<PERSON>lter Pottery Sherd", "item.minecraft.shield": "Escudu", "item.minecraft.shield.black": "Escudu prietu", "item.minecraft.shield.blue": "Escudu azul", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "<PERSON>s<PERSON><PERSON> cianu", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON><PERSON> buxu", "item.minecraft.shield.green": "Escudu verde", "item.minecraft.shield.light_blue": "Escudu azul claro", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON> buxu claro", "item.minecraft.shield.lime": "<PERSON>s<PERSON><PERSON> llima", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta", "item.minecraft.shield.orange": "Escudu naranxa", "item.minecraft.shield.pink": "Escu<PERSON> rosa", "item.minecraft.shield.purple": "Escu<PERSON> mor<PERSON>u", "item.minecraft.shield.red": "Escudu coloráu", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON> blan<PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un shulker", "item.minecraft.sign": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una xibiella", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un caballu esqueléticu", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una cadarma", "item.minecraft.skull_banner_pattern": "<PERSON>", "item.minecraft.skull_banner_pattern.desc": "Dibuxu de craniu", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Skull Pottery Shard", "item.minecraft.skull_pottery_sherd": "Skull Pottery Sherd", "item.minecraft.slime_ball": "<PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un llimu", "item.minecraft.smithing_template": "<PERSON><PERSON> Template", "item.minecraft.smithing_template.applies_to": "Aplícase a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON><PERSON><PERSON> un llingote o cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "<PERSON><PERSON><PERSON> una pieza d'armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Llingotes y cristales", "item.minecraft.smithing_template.ingredients": "Ingredientes:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON><PERSON> de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipamientu de diamante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON><PERSON> una armadura, arma o ferramienta de diamante", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Llingote de netherita", "item.minecraft.smithing_template.upgrade": "Meyora: ", "item.minecraft.sniffer_spawn_egg": "Sniffer Spawn Egg", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> Pottery Sherd", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Snow Golem Spawn Egg", "item.minecraft.snowball": "<PERSON><PERSON>", "item.minecraft.spectral_arrow": "Flecha espectral", "item.minecraft.spider_eye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una araña", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "Brebaxe refundizu", "item.minecraft.splash_potion.effect.awkward": "Brebaxe refundizu raru", "item.minecraft.splash_potion.effect.empty": "Brebaxe refundizu non ellaborable", "item.minecraft.splash_potion.effect.fire_resistance": "Brebaxe refundizu de resistencia al fueu", "item.minecraft.splash_potion.effect.harming": "Brebaxe refundizu de da<PERSON>", "item.minecraft.splash_potion.effect.healing": "Brebaxe refundizu de curación", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "Brebaxe refundizu d'invisibilidá", "item.minecraft.splash_potion.effect.leaping": "Brebaxe refundizu de blincu", "item.minecraft.splash_potion.effect.levitation": "Brebaxe refundizu de llevitación", "item.minecraft.splash_potion.effect.luck": "Brebaxe refundizu de suerte", "item.minecraft.splash_potion.effect.mundane": "Brebaxe vulgar refundizu", "item.minecraft.splash_potion.effect.night_vision": "Brebaxe refundizu de visión nocherniego", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "Brebaxe refundizu de velenu", "item.minecraft.splash_potion.effect.regeneration": "Brebaxe refundizu de rexeneración", "item.minecraft.splash_potion.effect.slow_falling": "Brebaxe refundizu de cayida lenta", "item.minecraft.splash_potion.effect.slowness": "Brebaxe refundizu de lentitú", "item.minecraft.splash_potion.effect.strength": "Brebaxe refundizu de fuercia", "item.minecraft.splash_potion.effect.swiftness": "Brebaxe refundizu de rapidez", "item.minecraft.splash_potion.effect.thick": "Brebaxe espesu refundizu", "item.minecraft.splash_potion.effect.turtle_master": "Brebaxe refundizu del maestru de tortugues", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON> refundizu con agua", "item.minecraft.splash_potion.effect.water_breathing": "Brebaxe refundizu d'aliendu acuáticu", "item.minecraft.splash_potion.effect.weakness": "Brebaxe refundizu de badea", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "Barca d'abetu", "item.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "item.minecraft.spyglass": "Spyglass", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un calamar", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON> de piedra", "item.minecraft.stone_hoe": "Fesoria de piedra", "item.minecraft.stone_pickaxe": "<PERSON><PERSON>", "item.minecraft.stone_shovel": "Pala de piedra", "item.minecraft.stone_sword": "Espada de piedra", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un vagamundu", "item.minecraft.strider_spawn_egg": "Strider Spawn Egg", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "Azucre", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON> dulces", "item.minecraft.tadpole_bucket": "Bucket of Tadpole", "item.minecraft.tadpole_spawn_egg": "Tadpole Spawn Egg", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "<PERSON>lecha calistrada", "item.minecraft.tipped_arrow.effect.awkward": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.empty": "Flecha non ellaborable", "item.minecraft.tipped_arrow.effect.fire_resistance": "Flecha de resistencia al fueu", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Flecha de curación", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Infestation", "item.minecraft.tipped_arrow.effect.invisibility": "Flecha d'invisibilidá", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.levitation": "Flecha de llevitación", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.night_vision": "Flecha de visión nocherniego", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> velenosa", "item.minecraft.tipped_arrow.effect.regeneration": "Flecha de rexeneración", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON><PERSON> de cayida lenta", "item.minecraft.tipped_arrow.effect.slowness": "Flecha de lentitú", "item.minecraft.tipped_arrow.effect.strength": "Flecha de fuercia", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.thick": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.turtle_master": "Flecha del maestru de tortugues", "item.minecraft.tipped_arrow.effect.water": "Flecha de chiscadura", "item.minecraft.tipped_arrow.effect.water_breathing": "Flecha d'aliendu acuáticu", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON> bad<PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "Vagoneta con TNT", "item.minecraft.torchflower_seeds": "Torchflower Seeds", "item.minecraft.totem_of_undying": "Tótem d'inmortalidá", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a una llama de viaxante", "item.minecraft.trial_key": "Trial Key", "item.minecraft.trident": "Tridente", "item.minecraft.tropical_fish": "Pexe tropical", "item.minecraft.tropical_fish_bucket": "Calderu con pexe tropical", "item.minecraft.tropical_fish_spawn_egg": "Gü<PERSON>u qu'apruz a un pexe tropical", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.turtle_scute": "Escudu de Tortuga", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una tortuga", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "Güevu qu'apruz a un picuscayu", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un aldeanu", "item.minecraft.vindicator_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un vindicador", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un viaxante", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Warden Spawn Egg", "item.minecraft.warped_fungus_on_a_stick": "Fungu distorsionáu nuna caña", "item.minecraft.water_bucket": "Calderu con agua", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "Trigu", "item.minecraft.wheat_seeds": "Semiente de trigu", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Wind Charge", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una bruxa", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a una cadarma de wither", "item.minecraft.wither_spawn_egg": "Wither Spawn Egg", "item.minecraft.wolf_armor": "<PERSON>or", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un llobu", "item.minecraft.wooden_axe": "<PERSON><PERSON> <PERSON>", "item.minecraft.wooden_hoe": "Fesoria de madera", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON>", "item.minecraft.wooden_sword": "Espada de madera", "item.minecraft.writable_book": "Llibru y pluma", "item.minecraft.written_book": "Llibru escritu", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un zoglin", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un caballu zombi", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un zombi", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> qu'apruz a un aldeanu zombi", "item.minecraft.zombified_piglin_spawn_egg": "<PERSON><PERSON><PERSON>u qu'apruz a un piglin zombi", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "When on Chest:", "item.modifiers.feet": "When on Feet:", "item.modifiers.hand": "Na mano:", "item.modifiers.head": "When on Head:", "item.modifiers.legs": "When on Legs:", "item.modifiers.mainhand": "When in Main Hand:", "item.modifiers.offhand": "When in Off Hand:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s etiqueta(es)", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "Indestruyible", "itemGroup.buildingBlocks": "Bloques de construcción", "itemGroup.coloredBlocks": "Bloques de colores", "itemGroup.combat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Crafting", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> y b<PERSON><PERSON><PERSON>", "itemGroup.functional": "Functional Blocks", "itemGroup.hotbar": "Saved Hotbars", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Inventariu de sobrevivencia", "itemGroup.natural": "Natural Blocks", "itemGroup.op": "Operator Utilities", "itemGroup.redstone": "Redstone Blocks", "itemGroup.search": "Busca:", "itemGroup.spawnEggs": "Spawn Eggs", "itemGroup.tools": "Tools & Utilities", "item_modifier.unknown": "Unknown item modifier: %s", "jigsaw_block.final_state": "Conviértese en:", "jigsaw_block.generate": "Generate", "jigsaw_block.joint.aligned": "Aligned", "jigsaw_block.joint.rollable": "Rollable", "jigsaw_block.joint_label": "Tipu de xuntura:", "jigsaw_block.keep_jigsaws": "Keep Jigsaws", "jigsaw_block.levels": "Niveles: %s", "jigsaw_block.name": "Name:", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "Target Pool:", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "Target Name:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (Caxa de música)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Avances", "key.attack": "Atacar/Destruyir", "key.back": "Caminar p'atrás", "key.categories.creative": "Modu creativu", "key.categories.gameplay": "Xuegu", "key.categories.inventory": "Inventariu", "key.categories.misc": "Miscelánea", "key.categories.movement": "Movición", "key.categories.multiplayer": "Multixugador", "key.categories.ui": "Interfaz del xuegu", "key.chat": "Abrir la charra", "key.command": "Abrir la llinia de comandos", "key.drop": "Soltar un oxetu seleicionáu", "key.forward": "Caminar p'alantre", "key.fullscreen": "Alternar la pantalla completa", "key.hotbar.1": "Atayu 1", "key.hotbar.2": "Atayu 2", "key.hotbar.3": "Atayu 3", "key.hotbar.4": "Atayu 4", "key.hotbar.5": "Atayu 5", "key.hotbar.6": "Atayu 6", "key.hotbar.7": "Atayu 7", "key.hotbar.8": "Atayu 8", "key.hotbar.9": "Atayu 9", "key.inventory": "Abrir/Zarrar l'inventariu", "key.jump": "<PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Retrocesu", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.down": "Flecha a<PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "Intro", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON><PERSON>", "key.keyboard.insert": "Inxertar", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": "Puntu decimal (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Intro (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "<PERSON><PERSON><PERSON> es<PERSON>a", "key.keyboard.left.alt": "Alt esquierdu", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl esq.", "key.keyboard.left.shift": "<PERSON><PERSON> esq.", "key.keyboard.left.win": "<PERSON><PERSON><PERSON> es<PERSON>", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Blo<PERSON>", "key.keyboard.page.down": "<PERSON>", "key.keyboard.page.up": "Av Páx", "key.keyboard.pause": "Posa", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON>", "key.keyboard.right": "<PERSON><PERSON><PERSON> derecha", "key.keyboard.right.alt": "Alt derechu", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl derechu", "key.keyboard.right.shift": "<PERSON><PERSON> der<PERSON>", "key.keyboard.right.win": "<PERSON><PERSON><PERSON> der<PERSON>u", "key.keyboard.scroll.lock": "B<PERSON>q <PERSON>", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tabulador", "key.keyboard.unknown": "<PERSON><PERSON>", "key.keyboard.up": "Flecha arriba", "key.keyboard.world.1": "Mundu 1", "key.keyboard.world.2": "Mundu 2", "key.left": "Desplazamientu llateral esquierdu", "key.loadToolbarActivator": "Load Hotbar Activator", "key.mouse": "Botón %1$s", "key.mouse.left": "Botón esq.", "key.mouse.middle": "Botón central", "key.mouse.right": "<PERSON><PERSON><PERSON> derechu", "key.pickItem": "Coyer un bloque", "key.playerlist": "Llistar <PERSON>", "key.quickActions": "Quick Actions", "key.right": "Desplazamientu llateral derechu", "key.saveToolbarActivator": "Save Hotbar Activator", "key.screenshot": "Capturar la pantalla", "key.smoothCamera": "Alternar la cámara cinematográfica", "key.sneak": "Encuxar", "key.socialInteractions": "Pantalla d'interaiciones sociales", "key.spectatorOutlines": "Rescamplar x<PERSON>dor<PERSON> (espectadores)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Cambiar un oxetu de manes", "key.togglePerspective": "Alternar la perspeutiva", "key.use": "Usar un oxetu/Asitiar un bloque", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "Comunidá", "known_server_link.community_guidelines": "Normes de la comunidá", "known_server_link.feedback": "Opiniones", "known_server_link.forums": "For<PERSON>", "known_server_link.news": "Novedaes", "known_server_link.report_bug": "Informar d'erru nel servidor", "known_server_link.status": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.support": "Soporte", "known_server_link.website": "Sitiu web", "lanServer.otherPlayers": "Axustes pa otros xugadores", "lanServer.port": "Númberu de puertu", "lanServer.port.invalid": "Puertu non válidu.\nDexa la caxa d'edición vacida o escueye un númberu ente 1024 y 65535.", "lanServer.port.invalid.new": "Puertu non válidu.\nDexa la caxa d'edición vacida o introduz un númberu entre %s y %s.", "lanServer.port.unavailable": "Puertu non disponible.\nDexa la caxa d'edición vacida o escueye un númberu distintu ente 1024 y 65535.", "lanServer.port.unavailable.new": "El puertu nun ta disponible.\nDexa baleru'l cuadru d'edición o introduz otru númberu ente %s y %s.", "lanServer.scanning": "Buscando partíes na rede llocal", "lanServer.start": "Aniciar el mundu na LAN", "lanServer.title": "Mundu na LAN", "language.code": "ast_ES", "language.name": "<PERSON><PERSON><PERSON><PERSON>", "language.region": "Asturies", "lectern.take_book": "Coyer el llibru", "loading.progress": "%s%%", "mco.account.privacy.info": "L<PERSON>r más tocante a Mojang y les lleis de privacidá", "mco.account.privacy.info.button": "Lleer más sobre'l RGPD", "mco.account.privacy.information": "Mojang implementa ciertos procedimientos para ayudar a protexer a los menores y la so privacidá, que consisten en cumplir cola Llei de Protección de Privacidá Infantil n'Internet (COPPA) y el Reglamentu Xeneral de Protección de Datos (RGPD).\n\nTienes de tener consentimientu parental antes de poder tener accesu a la to cuenta de Realms.", "mco.account.privacyinfo": "Mojang implementa ciertos procedimientos p'ayudar a protexer a los menores y la so privacidá que consisten nel cumplimientu del COPPA y la GDPR.\n\nYe posible que tengas de consiguir el consentimientu parental enantes d'acceder a la to cuenta de Realms.\n\nSi tienes una cuenta vieya de Minecraft (anicies sesión con un nome d'usuariu), tienes que la migrar a una cuenta Mojang p'acceder a Realms.", "mco.account.update": "<PERSON><PERSON><PERSON> la cuenta", "mco.activity.noactivity": "Ensin actividá dende %s día(s)", "mco.activity.title": "Actividá del xugador", "mco.backup.button.download": "Baxar la última", "mco.backup.button.reset": "<PERSON><PERSON><PERSON> mundu", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "<PERSON>gar un mundu", "mco.backup.changes.tooltip": "Cambeos", "mco.backup.entry": "Copia de seguridá. (%s)", "mco.backup.entry.description": "Descripción", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON>(s) activ<PERSON>u(s)", "mco.backup.entry.gameDifficulty": "Dificultá del xuegu", "mco.backup.entry.gameMode": "<PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Versión del servidor de xuegu", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Template Name", "mco.backup.entry.undefined": "Undefined Change", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "World Type", "mco.backup.generate.world": "<PERSON><PERSON><PERSON> mundu", "mco.backup.info.title": "Changes From Last Backup", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "Anguaño esti realm nun tien nenguna copia de seguranza.", "mco.backup.restoring": "Restaurando'l to realm", "mco.backup.unknown": "UNKNOWN", "mco.brokenworld.download": "Baxar", "mco.brokenworld.downloaded": "Baxáu", "mco.brokenworld.message.line1": "Reafita o seleiciona otru mundu, por favor.", "mco.brokenworld.message.line2": "<PERSON><PERSON><PERSON> pues escoyer la descarga del mundu al mou d'un xugador.", "mco.brokenworld.minigame.title": "<PERSON>sti minixuegu yá nun ye compatible", "mco.brokenworld.nonowner.error": "Espera a que'l dueñu'l realm reafite'l mundu, por favor", "mco.brokenworld.nonowner.title": "El mundu ta ensin anovar", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Reset", "mco.brokenworld.title": "El mundu actual yá nun ye compatible", "mco.client.incompatible.msg.line1": "El veceru nun ye compatible con Realms.", "mco.client.incompatible.msg.line2": "Usa la versión más recién de Minecraft.", "mco.client.incompatible.msg.line3": "Realms nun ye compatible coles versiones snapshot.", "mco.client.incompatible.title": "¡Veceru incompatible!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Non puede verificase la compatibilidá", "mco.compatibility.upgrade": "Actualizar", "mco.compatibility.upgrade.description": "Esti mundu xugose por última vez na versión %s. Tas na versión %s.\n\nVa guardase una copia de seguridá del to mundu en «Copies del mundu».\n\nRestaura'l to mundu si ye necesariu.", "mco.compatibility.upgrade.friend.description": "Esti mundu xugose por última vez na versión %s. Tas na versión %s.\n\nVa guardase una copia de seguridá del mundu en «Copies del mundu».\n\nEl dueñu del realm puede restaurar el mundu si ye necesariu.", "mco.compatibility.upgrade.title": "¿Se<PERSON>ro que quies actualizar esti mundu?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "El feed de xugadores ta desactiváu temporalmente", "mco.configure.world.backup": "Copies", "mco.configure.world.buttons.activity": "Actividá de xugadores", "mco.configure.world.buttons.close": "Cerrar Realm", "mco.configure.world.buttons.delete": "Delete", "mco.configure.world.buttons.done": "<PERSON><PERSON>", "mco.configure.world.buttons.edit": "<PERSON><PERSON> mundu", "mco.configure.world.buttons.invite": "Convidar a un xugador", "mco.configure.world.buttons.moreoptions": "Más opciones", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Abrir realm", "mco.configure.world.buttons.options": "Opciones", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Reafitar el mundu", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Soscripción", "mco.configure.world.buttons.switchminigame": "Otru minixuegu", "mco.configure.world.close.question.line1": "El realm va dexar de tar disponible.", "mco.configure.world.close.question.line2": "¿Tas seguru que quies faer esto?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Zarrando'l realm…", "mco.configure.world.commandBlocks": "Bloqs. de comandos", "mco.configure.world.delete.button": "Desaniciar el realm", "mco.configure.world.delete.question.line1": "El realm va desaniciase da<PERSON>chu", "mco.configure.world.delete.question.line2": "¿Tas seguru que quies siguir?", "mco.configure.world.description": "Descripción del realm", "mco.configure.world.edit.slot.name": "Nome del mundu", "mco.configure.world.edit.subscreen.adventuremap": "Desactiváronse dalgunos axustes darréu que'l mundu actual ye una aventura", "mco.configure.world.edit.subscreen.experience": "Desactiváronse dalgunos axustes darréu que'l mundu actual ye una esperiencia", "mco.configure.world.edit.subscreen.inspiration": "Desactiváronse dalgunos axustes darréu que'l mundu actual ye una inspiración", "mco.configure.world.forceGameMode": "Forciar mou <PERSON>uegu", "mco.configure.world.invite.narration": "Tienes nueves invitaciones: %s", "mco.configure.world.invite.profile.name": "Nome", "mco.configure.world.invited": "Persones convidaes", "mco.configure.world.invited.number": "Invitao (%s)", "mco.configure.world.invites.normal.tooltip": "Usuariu normal", "mco.configure.world.invites.ops.tooltip": "Operador", "mco.configure.world.invites.remove.tooltip": "Desaniciar", "mco.configure.world.leave.question.line1": "Si coles d'esti realm nun vas velu a nun ser que vuelvan convidate", "mco.configure.world.leave.question.line2": "Are you sure you want to continue?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "Allugamientu", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nome del realm", "mco.configure.world.opening": "Abriendo'l realm…", "mco.configure.world.players.error": "<PERSON>un esiste nengún xugador col nome apurríu", "mco.configure.world.players.inviting": "<PERSON><PERSON><PERSON><PERSON>...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "El mundu va xenerase de nueves y l'actual va perdese", "mco.configure.world.reset.question.line2": "¿De xuru que quies siguir?", "mco.configure.world.resourcepack.question": "Precises un paquete de recursos personalizáu pa xugar nesti realm\n\n¿Quies descargalo y xugar?", "mco.configure.world.resourcepack.question.line1": "Precises un paquete de recursos personalizáu pa xugar nesti realm", "mco.configure.world.resourcepack.question.line2": "¿Quies baxalu y xugar?", "mco.configure.world.restore.download.question.line1": "El mundu va baxase y amestase a los tos mundos d'un xugador.", "mco.configure.world.restore.download.question.line2": "¿Quies siguir?", "mco.configure.world.restore.question.line1": "El to mundu va restaurase a la data «%s» (%s)", "mco.configure.world.restore.question.line2": "¿De xuru que quies siguir?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Configuración", "mco.configure.world.slot": "Mundu %s", "mco.configure.world.slot.empty": "Balero", "mco.configure.world.slot.switch.question.line1": "El realm va cambiar a otru mundu", "mco.configure.world.slot.switch.question.line2": "¿De xuru que quies siguir?", "mco.configure.world.slot.tooltip": "Calca pa cambiar al mundu", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "Cambiar al minixuegu", "mco.configure.world.spawnAnimals": "<PERSON>ucir animales", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Aprucir creatures", "mco.configure.world.spawnProtection": "Protexer l'aprucideru", "mco.configure.world.spawn_toggle.message": "Si desactives esta opción, vas esaniciar toles entidaes d'esi tipu", "mco.configure.world.spawn_toggle.message.npc": "Si desactives esta opción, vas esaniciar toles entidaes d'esi tipu, por casu, los aldeanos", "mco.configure.world.spawn_toggle.title": "¡Cuidao!", "mco.configure.world.status": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "día", "mco.configure.world.subscription.days": "d<PERSON><PERSON>", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Allargar la soscripción", "mco.configure.world.subscription.less_than_a_day": "Menos d'un día", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "meses", "mco.configure.world.subscription.recurring.daysleft": "Renuévase automáticamente en", "mco.configure.world.subscription.recurring.info": "Los cambios na to suscripción de Realms, como l'ampliación del periodu de suscripción o la desactivación de la renovación automática, nun van aplicase hasta la próxima fecha de facturación.", "mco.configure.world.subscription.remaining.days": "%1$s día(s)", "mco.configure.world.subscription.remaining.months": "%1$s mes(es)", "mco.configure.world.subscription.remaining.months.days": "%1$s mes(es), %2$s día(s)", "mco.configure.world.subscription.start": "Data d'aniciu", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Tiempu que falta", "mco.configure.world.subscription.title": "Información de la soscripción", "mco.configure.world.subscription.unknown": "Desconozse", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> mundu", "mco.configure.world.switch.slot.subtitle": "Esti mundu ta baleru, escueyi qué quies facer", "mco.configure.world.title": "Configuración del realm:", "mco.configure.world.uninvite.player": "¿Seguru que quies anular la invitación a %s?", "mco.configure.world.uninvite.question": "¿De xuru que quies anular la invitación de", "mco.configure.worlds.title": "Mundos", "mco.connect.authorizing": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "mco.connect.connecting": "Conectando col realm…", "mco.connect.failed": "Fallu al conectase col realm", "mco.connect.region": "Server region: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "¡Has introducir un nome!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON>'l mundu…", "mco.create.world.skip": "Saltar", "mco.create.world.subtitle": "Si quies, seleiciona'l mundu que quieras poner nel realm nuevu", "mco.create.world.wait": "Creando'l realm…", "mco.download.cancelled": "Descarga encaboxada", "mco.download.confirmation.line1": "El mundu que vas baxar supera los %s", "mco.download.confirmation.line2": "Nun vas ser a xubir de nueves esti mundu al realm", "mco.download.confirmation.oversized": "El mundu qu'intentes descargar supera los %s\n\nNun vas poder volver xubir esti mundu al to realm", "mco.download.done": "<PERSON><PERSON><PERSON> fecha", "mco.download.downloading": "<PERSON><PERSON><PERSON>", "mco.download.extracting": "Estrayendo", "mco.download.failed": "<PERSON><PERSON><PERSON> fallida", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON><PERSON>", "mco.download.resourcePack.fail": "¡Erru al descargar el paquete de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Descarga del últimu mundu", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON> a reaniciar Minecraft, por favor", "mco.error.invalid.session.title": "La sesión nun ye válida", "mco.errorMessage.6001": "El veceru ta ensin anovar", "mco.errorMessage.6002": "Refugáronse los términos del serviciu", "mco.errorMessage.6003": "Algamóse la llende de descargues", "mco.errorMessage.6004": "Algamóse la llende de xubes", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6006": "Mundu desact<PERSON>", "mco.errorMessage.6007": "L'usuariu ta en demasiaos realms", "mco.errorMessage.6008": "Nome de realm non válidu", "mco.errorMessage.6009": "Descripción del realm non válida", "mco.errorMessage.connectionFailure": "Asocedió un fallu, volvi tentalo dempués.", "mco.errorMessage.generic": "Prodúxose un error: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "<PERSON><PERSON> s'apur<PERSON>on detalles del erru", "mco.errorMessage.realmsService": "Prodúxose un error (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Nun se pudo conectar a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Nun se pudo comprobar la versión compatible, llogrose la respuesta: %s", "mco.errorMessage.retry": "Reintentar operación", "mco.errorMessage.serviceBusy": "<PERSON><PERSON><PERSON> momentu, Realms ta ocupáu.\n<PERSON><PERSON> tentar de conectate al to Realm nunos minutos.", "mco.gui.button": "Botón", "mco.gui.ok": "Aceptar", "mco.info": "Info!", "mco.invited.player.narration": "Invited player %s", "mco.invites.button.accept": "Aceptar", "mco.invites.button.reject": "Refugar", "mco.invites.nopending": "¡Nun hai invitaciones pendientes!", "mco.invites.pending": "New invite(s)!", "mco.invites.title": "Invitaciones pendientes", "mco.minigame.world.changeButton": "Esbilla otru mini-xuegu", "mco.minigame.world.info.line1": "¡Esto va trocar temporalmente'l mundu por un minixuegu!", "mco.minigame.world.info.line2": "Dempués vas poder volver al mundu orixinal ensin perder nada.", "mco.minigame.world.noSelection": "Seleiciona", "mco.minigame.world.restore": "Finando'l minixuegu…", "mco.minigame.world.restore.question.line1": "El minixuegu va finar y el realm va restaurase.", "mco.minigame.world.restore.question.line2": "¿De xuru que quies siguir?", "mco.minigame.world.selected": "Minixuegu seleicionáu:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON> de mundu…", "mco.minigame.world.startButton": "Cambiar", "mco.minigame.world.starting.screen.title": "Aniciando'l minixuegu…", "mco.minigame.world.stopButton": "Finar mini-xuegu", "mco.minigame.world.switch.new": "¿Esbillar otru minixuegu?", "mco.minigame.world.switch.title": "Switch Minigame", "mco.minigame.world.title": "Camudar realm a Mini-xuegu", "mco.news": "Noticies de Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Open Link", "mco.notification.visitUrl.message.default": "Please visit the link below", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "Entruga", "mco.reset.world.adventure": "Aventures", "mco.reset.world.experience": "Esperiencies", "mco.reset.world.generate": "<PERSON><PERSON><PERSON> nuevu", "mco.reset.world.inspiration": "Inspiración", "mco.reset.world.resetting.screen.title": "Reafitando'l mundu…", "mco.reset.world.seed": "Semiente (opcional)", "mco.reset.world.template": "Plant<PERSON><PERSON>", "mco.reset.world.title": "Reset World", "mco.reset.world.upload": "Xubir un mundu", "mco.reset.world.warning": "Esto va trocar el mundu actual del realm", "mco.selectServer.buy": "Comprar un realm", "mco.selectServer.close": "Z<PERSON><PERSON>", "mco.selectServer.closed": "El realm zarró", "mco.selectServer.closeserver": "Zarrar realm", "mco.selectServer.configure": "Configurar", "mco.selectServer.configureRealm": "Configure Realm", "mco.selectServer.create": "Crear realm", "mco.selectServer.create.subtitle": "Select what world to put on your new Realm", "mco.selectServer.expired": "Realm caducáu", "mco.selectServer.expiredList": "Caducó la soscripción", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Soscribise", "mco.selectServer.expiredTrial": "Finó'l periodu de prueba", "mco.selectServer.expires.day": "Caduca nun día", "mco.selectServer.expires.days": "Caduca en %s díes", "mco.selectServer.expires.soon": "Caduca ceo", "mco.selectServer.leave": "Colar del realm", "mco.selectServer.loading": "Loading Realms List", "mco.selectServer.mapOnlySupportedForVersion": "Esti mapa nun ye compatible cola versión %s", "mco.selectServer.minigame": "Minixuegu:", "mco.selectServer.minigameName": "Minigame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Nun pues xugar esti minixuegu na %s", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Realm abiertu", "mco.selectServer.openserver": "Open Realm", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Realms ye'l mou seguru y cenciellu d'esfrutar de Minecraft en llinia (hasta 10 collacios al empar). ¡Sofita milenta minixuegos y mundos personalizaos! El propietariu ye'l qu'ha pagar.", "mco.selectServer.purchase": "Amestar un realm", "mco.selectServer.trial": "¡Probar!", "mco.selectServer.uninitialized": "¡Calca pa entamar el realm!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Realms ta disponible nes versiones Snapshot dende la 23w41a. Cada soscripción a Realms inclúi un Realm Snapshot de baldre, independiente del to Realm de Java.", "mco.snapshotRealmsPopup.title": "Realms ta disponible nes Snapshots", "mco.snapshotRealmsPopup.urlText": "Saber más", "mco.template.button.publisher": "E<PERSON><PERSON><PERSON><PERSON>or", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Plantía del mundu", "mco.template.info.tooltip": "Sitiu web del espublizador", "mco.template.name": "Plantía", "mco.template.select.failure": "Nun pudimos recuperar la tabla de conteníos pa esta estaya.\nComprueba la conexón a internet o volvi tentalo dempués.", "mco.template.select.narrate.authors": "Autores: %s", "mco.template.select.narrate.version": "versión %s", "mco.template.select.none": "Me<PERSON>, paez qu'anguaño esta estaya de conteníu ta balera.\n<PERSON><PERSON> dem<PERSON>és pa ver les novedaes. Si yes creador,\n%s.", "mco.template.select.none.linkTitle": "piensa en xubir da<PERSON>", "mco.template.title": "World templates", "mco.template.title.minigame": "Minixuegos", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON>a", "mco.terms.buttons.agree": "Sí", "mco.terms.buttons.disagree": "Non", "mco.terms.sentence.1": "Toi acordies con Realms de Minecraft", "mco.terms.sentence.2": "Términos del serviciu", "mco.terms.title": "Términos del serviciu de Realms", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.now": "right now", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.trial.message.line1": "¿Quies consiguir el to realm?", "mco.trial.message.line2": "¡Calca equí pa consiguir más información!", "mco.upload.button.name": "<PERSON><PERSON>", "mco.upload.cancelled": "Xuba encaboxada", "mco.upload.close.failure": "<PERSON>un pudo zarrase'l realm, volvi tentalo dempués", "mco.upload.done": "<PERSON>ba fecha", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "¡La xuba falló! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "World too big", "mco.upload.hardcore": "¡Los mundos perdifíciles nun puen xubise!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mundu", "mco.upload.select.world.none": "¡Nun s'atoparon mundos d'un xugador!", "mco.upload.select.world.subtitle": "<PERSON>s<PERSON><PERSON>'l mundu d'un xugador a xubir, por favor", "mco.upload.select.world.title": "Upload World", "mco.upload.size.failure.line1": "¡«%s» ye pergrande!", "mco.upload.size.failure.line2": "Ye de %s. El tamañu máximu almitíu ye %s.", "mco.upload.uploading": "Xubiendo «%s»", "mco.upload.verifying": "Verificando'l mundu", "mco.version": "Version: %s", "mco.warning": "Warning!", "mco.worldSlot.minigame": "Minigame", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Desconectase", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Menú del xuegu", "menu.modded": " (Modificóse)", "menu.multiplayer": "Multixugador", "menu.online": "Minecraft Realms", "menu.options": "Opciones…", "menu.paused": "Xuegu en posa", "menu.playdemo": "<PERSON>gar nun mundu demo", "menu.playerReporting": "Player Reporting", "menu.preparingSpawn": "Tresnando l'área d'aprucida: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "Colar del xuegu", "menu.reportBugs": "Informar de fallos", "menu.resetdemo": "Reaniciar el mundu demo", "menu.returnToGame": "Volver pa la partida", "menu.returnToMenu": "Guardar y colar al menú d'aniciu", "menu.savingChunks": "Guardando los chunks", "menu.savingLevel": "Guardando'l mundu", "menu.sendFeedback": "Opinar", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "Abrir na LAN", "menu.singleplayer": "Un xugador", "menu.working": "Trabayando…", "merchant.deprecated": "Los aldeanos reabastécense dos vegaes al día.", "merchant.level.1": "Novatu", "merchant.level.2": "Aprendiz", "merchant.level.3": "Oficial", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "Maestru", "merchant.title": "%s - %s", "merchant.trades": "Ufiertes", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Press %1$s to Dismount", "multiplayer.applyingPack": "Aplicando'l paquete de recursos", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Sentímoslo mas los sirvidores d'autenticación tán cayíos. Volvi tentalo dempués, por favor.", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Espulsáronte d'esti sirvidor", "multiplayer.disconnect.banned.expiration": "\nVan quitate la espulsión el %s", "multiplayer.disconnect.banned.reason": "Espulsáronte d'esti sirvidor.\nRazón: %s", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned_ip.reason": "Espulsaron a la to IP del sirvidor.\nRazón: %s", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "Aniciesti sesión n'otru allugamientu", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": "El volar nun s'activó nesti sirvidor", "multiplayer.disconnect.generic": "Desconectáu", "multiplayer.disconnect.idling": "¡Tuviesti ausente munchu tiempu!", "multiplayer.disconnect.illegal_characters": "Hai caráuteres illegales na charra", "multiplayer.disconnect.incompatible": "¡El veceru nun ye compatible! Usa la versión %s", "multiplayer.disconnect.invalid_entity_attacked": "Ten<PERSON><PERSON> d'atacar a una entidá non válida", "multiplayer.disconnect.invalid_packet": "El sirvidor unvió un paquete que nun ye válidu", "multiplayer.disconnect.invalid_player_data": "Invalid player data", "multiplayer.disconnect.invalid_player_movement": "Recibióse un paquete de movición non válidu del xugador", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Recibióse un paquete de movición non válidu de vehículu", "multiplayer.disconnect.ip_banned": "You have been IP banned from this server", "multiplayer.disconnect.kicked": "Echóte un operador", "multiplayer.disconnect.missing_tags": "Recibióse un conxuntu d'etiquetes incompletu.\nContauta col operador del sirvidor, por favor.", "multiplayer.disconnect.name_taken": "Esi nome yá ta coyíu", "multiplayer.disconnect.not_whitelisted": "¡Nun tas na llista blanca d'esti sirvidor!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": "¡El sirvidor ta enllén!", "multiplayer.disconnect.server_shutdown": "El sirvidor zarró", "multiplayer.disconnect.slow_login": "Entardóse muncho n'aniciar sesión", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Nun s'esperaben los datos personalizaos del veceru", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "¡Fallu al verificar el nome d'usuariu!", "multiplayer.downloadingStats": "Recuperando les estadístiques…", "multiplayer.downloadingTerrain": "Cargan<PERSON>'l terrén…", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": "Nun pue entregase'l mensaxe de charra, comprueba'l rexistru del sirvidor: %s", "multiplayer.player.joined": "%s xunióse a la partida", "multiplayer.player.joined.renamed": "%s (enantes conocíu como %s) xunióse a la partida", "multiplayer.player.left": "%s coló de la partida", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Online players: %s", "multiplayer.requiredTexturePrompt.disconnect": "El sirvidor rique un paquete de recursos personalizáu", "multiplayer.requiredTexturePrompt.line1": "<PERSON><PERSON>i sirvidor rique l'usu d'un paquete de recursos personalizáu.", "multiplayer.requiredTexturePrompt.line2": "Refugar esti paquete de recursos personalizáu va desconectate del sirvidor.", "multiplayer.socialInteractions.not_available": "Les interaiciones sociales namás tán disponibles nos mundos multixugador", "multiplayer.status.and_more": "… y %s más …", "multiplayer.status.cancelled": "Encaboxóse", "multiplayer.status.cannot_connect": "Nun se pue conectar col sirvidor", "multiplayer.status.cannot_resolve": "Nun se pue resolver el nome del agospiador", "multiplayer.status.finished": "<PERSON><PERSON>", "multiplayer.status.incompatible": "¡Versión incompatible!", "multiplayer.status.motd.narration": "Message of the day: %s", "multiplayer.status.no_connection": "(ensin cone<PERSON>)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "Solicitando l'estáu'l sirvidor…", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s out of %s players online", "multiplayer.status.quitting": "Colando", "multiplayer.status.request_handled": "Status request has been handled", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Recibióse un estáu non solicitáu", "multiplayer.status.version.narration": "Server version: %s", "multiplayer.stopSleeping": "Llevantase", "multiplayer.texturePrompt.failure.line1": "<PERSON>un pudo aplicase'l paquete de recursos del sirvidor", "multiplayer.texturePrompt.failure.line2": "Any functionality that requires custom resources might not work as expected", "multiplayer.texturePrompt.line1": "<PERSON><PERSON>i sirvidor aconseya l'usu d'un paquete de recursos personalizáu.", "multiplayer.texturePrompt.line2": "¿Quies baxalu ya instalalu automáxicamente?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMensaxe del sirvidor:\n%s", "multiplayer.title": "Xuegu nel mou multixugador", "multiplayer.unsecureserver.toast": "Ye posible que se modifiquen los mensaxes que s'unvien nesti sirvidor y nun reflexen el mensaxe orixinal", "multiplayer.unsecureserver.toast.title": "Nun se puen verificar los mensaxes de la charra", "multiplayerWarning.check": "<PERSON>un amosar más esta pantalla", "multiplayerWarning.header": "El xuegu en llinia ye de terceros", "multiplayerWarning.message": "<PERSON><PERSON><PERSON><PERSON>, el xuegu en llinia úfrenlu sirvidores de terceros que nin Mojang Studios o Microsoft poseyen, remanen o supervisen. Demientres xuegues en llinia pues tar espuestu a mensaxes de charra y a otru tipu de conteníu xeneráu polos usuarios que nun se llendó y pue nun ser afayadizu pa tol mundu.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botón %s", "narration.button.usage.focused": "Primi Intro p'activar", "narration.button.usage.hovered": "Calca'l botón esquierdu del mur p'activar", "narration.checkbox": "Caxellu: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON><PERSON> p'alternar", "narration.checkbox.usage.hovered": "Calca'l botón esquierdu del mur p'alternar", "narration.component_list.usage": "Primi Tabulador pa navegar hasta l'elementu siguiente", "narration.cycle_button.usage.focused": "Primi Intro pa cambiar pa %s", "narration.cycle_button.usage.hovered": "Left click to switch to %s", "narration.edit_box": "Caxa d'edición: %s", "narration.item": "Item: %s", "narration.recipe": "Receta pa %s", "narration.recipe.usage": "Calca'l botón esquierdu del mur pa seleicionar", "narration.recipe.usage.more": "Calca'l botón derechu del mur p'amosar más recetes", "narration.selection.usage": "Primi los botones d'arriba y abaxo pa dir pa otra entrada", "narration.slider.usage.focused": "Primi les tecles d'esquierda y derecha pa camudar el valor", "narration.slider.usage.hovered": "<PERSON><PERSON> l'eslizador pa camudar el valor", "narration.suggestion": "Esbillóse la suxerencia %s de %s: %s", "narration.suggestion.tooltip": "Esbillóse la suxerencia %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "Accesibilidá", "narrator.button.difficulty_lock": "Bloquéu de la dificultá", "narrator.button.difficulty_lock.locked": "Bloquióse", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "Llingua", "narrator.controls.bound": "%s ta arreyáu a %s", "narrator.controls.reset": "Reafitar el botón %s", "narrator.controls.unbound": "%s nun s'a<PERSON><PERSON>", "narrator.joining": "<PERSON><PERSON><PERSON><PERSON>", "narrator.loading": "Cargando: %s", "narrator.loading.done": "<PERSON><PERSON>", "narrator.position.list": "Selected list row %s out of %s", "narrator.position.object_list": "Selected row element %s out of %s", "narrator.position.screen": "Screen element %s out of %s", "narrator.position.tab": "Selected tab %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "Pantalla d'an<PERSON>u", "narrator.screen.usage": "Use mouse cursor or Tab button to select element", "narrator.select": "Esbillóse: %s", "narrator.select.world": "Esbillóse %s que se xugó'l %s per ultima vegada na dificultá %s, coles %s y na versión %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "Desactivóse'l narrador", "narrator.toast.enabled": "Activóse'l narrador", "optimizeWorld.confirm.description": "Esto va tentar d'optimizar el mundu asegurándose de que tolos datos tán atroxaos nel formatu más recién. Esto pue tardar muncho según el mundu. Namás se faiga, el mundu podría funcionar más rápido o dexar de facelo con versiones vieyes del xuegu. ¿De xuru que deseyes siguir?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Optimizar el mundu", "optimizeWorld.info.converted": "Chunks anovaos: %s", "optimizeWorld.info.skipped": "Chunks saltaos: %s", "optimizeWorld.info.total": "Chunks totales: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Contando los chunks…", "optimizeWorld.stage.failed": "¡Falló! :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Optimizando'l mundu «%s»", "options.accessibility": "Accessibility Settings...", "options.accessibility.high_contrast": "Contraste altu", "options.accessibility.high_contrast.error.tooltip": "El paquete de recursos en contraste altu nun ta disponible", "options.accessibility.high_contrast.tooltip": "Ameyora'l contraste de los elementos de la IU", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Guía de l'accesibilidá", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "Fondu del testu", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON> sit<PERSON>", "options.accessibility.text_background_opacity": "Opacidá'l fondu'l testu", "options.accessibility.title": "Configuración de l'accesibilidá", "options.allowServerListing": "Allow Server Listings", "options.allowServerListing.tooltip": "Servers may list online players as part of their public status.\nWith this option off your name will not show up in such lists.", "options.ao": "Allumáu nidio", "options.ao.max": "Máximo", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "OFF", "options.attack.crosshair": "Na mira", "options.attack.hotbar": "Barra d'atayos", "options.attackIndicator": "Indicador d'ataque", "options.audioDevice": "Preséu", "options.audioDevice.default": "System Default", "options.autoJump": "Saltu automáticu", "options.autoSuggestCommands": "Suxerencies de comandos", "options.autosaveIndicator": "Indicador d'autoguardáu", "options.biomeBlendRadius": "Transición de biomes", "options.biomeBlendRadius.1": "NON (la más rápida)", "options.biomeBlendRadius.11": "11x11 (peralta)", "options.biomeBlendRadius.13": "13x13 (la más alta)", "options.biomeBlendRadius.15": "15x15 (la máxima)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (normal)", "options.biomeBlendRadius.9": "9x9 (alta)", "options.chat": "Cha<PERSON>s...", "options.chat.color": "Colores", "options.chat.delay": "<PERSON>t <PERSON>: %s second(s)", "options.chat.delay_none": "<PERSON><PERSON>: None", "options.chat.height.focused": "<PERSON><PERSON>", "options.chat.height.unfocused": "<PERSON><PERSON>", "options.chat.line_spacing": "Espaciu ente llinies", "options.chat.links": "Enllaces web", "options.chat.links.prompt": "Suxerir nos enllaces", "options.chat.opacity": "Opacidá'l testu la charra", "options.chat.scale": "<PERSON><PERSON><PERSON><PERSON>'l testu la charra", "options.chat.title": "Configuración de la charra…", "options.chat.visibility": "Charra", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "Hidden", "options.chat.visibility.system": "Namás comandos", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Fancy", "options.clouds.fast": "Fast", "options.controls": "Controles…", "options.credits_and_attribution": "Creitos ya atribución…", "options.damageTiltStrength": "Damage Tilt", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Logotipu monocromáu", "options.darkMojangStudiosBackgroundColor.tooltip": "Camuda'l fondu de la pantalla de carga de Mojang Studios a prietu.", "options.darknessEffectScale": "Pulsu d'Escuridá", "options.darknessEffectScale.tooltip": "Controla cuanto pulsia l'efectu d'Escuridá cuando un Warden o Glayador de Sculk te lo aplica.", "options.difficulty": "Dificultá", "options.difficulty.easy": "F<PERSON><PERSON>l", "options.difficulty.easy.info": "Hostile mobs spawn but deal less damage. Hunger bar depletes and drains health down to 5 hearts.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Hostile mobs spawn and deal more damage. Hunger bar depletes and drains all health.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Hostile mobs spawn and deal standard damage. Hunger bar depletes and drains health down to half a heart.", "options.difficulty.online": "Dificultá del sirvidor", "options.difficulty.peaceful": "Pacífica", "options.difficulty.peaceful.info": "No hostile mobs and only some neutral mobs spawn. Hunger bar doesn't deplete and health replenishes over time.", "options.directionalAudio": "Directional Audio", "options.directionalAudio.off.tooltip": "Classic Stereo sound.", "options.directionalAudio.on.tooltip": "Uses HRTF-based directional audio to improve the simulation of 3D sound. Requires HRTF compatible audio hardware, and is best experienced with headphones.", "options.discrete_mouse_scroll": "Desplazamientu estremáu", "options.entityDistanceScaling": "Distancia de les entidaes", "options.entityShadows": "Solombres de les entidaes", "options.font": "Configuración de la fonte…", "options.font.title": "Configuración de la fonte", "options.forceUnicodeFont": "Fonte Unicode", "options.fov": "Campu de visión", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Efeutos del campu de visión", "options.fovEffectScale.tooltip": "Controls how much the field of view can change with gameplay effects.", "options.framerate": "%s fps", "options.framerateLimit": "FPS máximos", "options.framerateLimit.max": "<PERSON><PERSON> ll<PERSON>e", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "L'actual", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Resolución a pantalla completa", "options.fullscreen.unavailable": "La opción nun ta disponible", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Escuro", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON>", "options.glintSpeed.tooltip": "Controls how fast the visual glint shimmers across enchanted items.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Controls how transparent the visual glint is on enchanted items.", "options.graphics": "Grá<PERSON><PERSON>", "options.graphics.fabulous": "¡Una maraviya!", "options.graphics.fabulous.tooltip": "«%s» usa asolombradores pa dibuxar el clima, les nubes ya partícules darrera d'agua ya bloques tresllúcidos.\nEsto pue afeutar muncho al rindimientu en preseos portátiles ya pantalles 4K.", "options.graphics.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "«Detallaos» equilibra'l rindimientu ya la calidá na mayoría de máquines.\nYe posible que'l clima, les nubes ya les partícules nun apaezan darrera d'agua o bloques tresllúcidos.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "«Rápidos» amenorga la cantidá de lloviu ya nieve visibles.\nDesactívense los efeutos de tresparencia en dalgunos bloques como les fueyes de los árboles.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Take Me Back", "options.graphics.warning.message": "Detectemos que la to tarxeta gráfica nun sofita la opción gráfica %s.\n\n<PERSON>ues inorar esto y siguir, por embargu nun va ufrísete sofitu pal preséu si escueyes usar la opción %s.", "options.graphics.warning.renderer": "Renderizador detect<PERSON>u: [%s]", "options.graphics.warning.title": "Graphics Device Unsupported", "options.graphics.warning.vendor": "Vendedor <PERSON>: [%s]", "options.graphics.warning.version": "Versión d'OpenGL detectada: [%s]", "options.guiScale": "Escala de la interfaz", "options.guiScale.auto": "Auto", "options.hidden": "Hidden", "options.hideLightningFlashes": "Hide Lightning Flashes", "options.hideLightningFlashes.tooltip": "Prevents Lightning Bolts from making the sky flash. The bolts themselves will still be visible.", "options.hideMatchedNames": "Hide Matched Names", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "Invertir el mur", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Alternar", "options.language": "Llingua…", "options.language.title": "Llingua", "options.languageAccuracyWarning": "(Ye posible que les traducciones nun seyan 100%% correutes)", "options.languageWarning": "Ye posible que les traducciones nun seyan 100%% correutes", "options.mainHand": "<PERSON><PERSON> principal", "options.mainHand.left": "Manzorga", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Niveles del minimapa", "options.modelPart.cape": "Capa", "options.modelPart.hat": "Cap<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON>a esquierda", "options.modelPart.left_sleeve": "Manga esquierda", "options.modelPart.right_pants_leg": "Pernera derecha", "options.modelPart.right_sleeve": "Manga derecha", "options.mouseWheelSensitivity": "Sensibilidá del desplazamientu", "options.mouse_settings": "Configuración del mur…", "options.mouse_settings.title": "Configuración del mur", "options.multiplayer.title": "Conf. del mou multixugador…", "options.multiplier": "x%s", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "Narra too", "options.narrator.chat": "Narra la charra", "options.narrator.notavailable": "Nun ta disponible", "options.narrator.off": "NON", "options.narrator.system": "Na<PERSON>'l sistema", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "NON", "options.off.composed": "%s: NON", "options.on": "SÍ", "options.on.composed": "%s: SÍ", "options.online": "<PERSON><PERSON> en llinia…", "options.online.title": "Opciones del mou en llinia", "options.onlyShowSecureChat": "Only Show Secure Chat", "options.onlyShowSecureChat.tooltip": "Only display messages from other players that can be verified to have been sent by that player, and have not been modified.", "options.operatorItemsTab": "Operator Items Tab", "options.particles": "Partícules", "options.particles.all": "All", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Mínimes", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Creador de chunks", "options.prioritizeChunkUpdates.byPlayer": "Bloques incompletos", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Some actions within a chunk will recompile the chunk immediately. This includes block placing & destroying.", "options.prioritizeChunkUpdates.nearby": "Bloques completos", "options.prioritizeChunkUpdates.nearby.tooltip": "Nearby chunks are always compiled immediately. This may impact game performance when blocks are placed or destroyed.", "options.prioritizeChunkUpdates.none": "Per filos", "options.prioritizeChunkUpdates.none.tooltip": "Nearby chunks are compiled in parallel threads. This may result in brief visual holes when blocks are destroyed.", "options.rawMouseInput": "Entrada en bruto", "options.realmsNotifications": "Noticies ya invitaciones de Realms", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "Menos info. de depuración", "options.renderClouds": "Nubes", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Visibilidá", "options.resourcepack": "Paquetes de recursos…", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Efeutos de distorsión", "options.screenEffectScale.tooltip": "Aumenta los efeutos de la distorsión visual de los portales del Nether.\nEn valores baxos, l'efeutu de voltura sustitúise por una superposición verde.", "options.sensitivity": "Sensibilidá", "options.sensitivity.max": "¡¡¡HIPERVELOCIDÁ!!!", "options.sensitivity.min": "*zZzZz*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Amosar los sotítulos", "options.simulationDistance": "Distancia de la simulación", "options.skinCustomisation": "<PERSON><PERSON> <PERSON>…", "options.skinCustomisation.title": "Personalización del aspeutu", "options.sounds": "Música ya soníos…", "options.sounds.title": "Opciones de la música ya'l soníu", "options.telemetry": "Datos de la telemetría…", "options.telemetry.button": "Recoyida de <PERSON>", "options.telemetry.button.tooltip": "«%s» inclúi namás los datos riquíos.\n«%s» inclúi los datos opcionales, ya tamién, los riquíos.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "Completa", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "Nenguna", "options.title": "Opciones", "options.touchscreen": "<PERSON><PERSON> t<PERSON>il", "options.video": "Axustes de videu…", "options.videoTitle": "Axustes de videu", "options.viewBobbing": "Balancéu de la visión", "options.visible": "<PERSON><PERSON><PERSON>", "options.vsync": "Sincronización vertical", "outOfMemory.message": "Minecraft escosó la memoria.\n\nEsti problema pudo causalu un fallu nel xuegu o la Máquina Virtual de Java por nun asignar abonda memoria.\n\nPa evitar la corrupción de niveles, zarróse la partida. Tentemos de lliberar abonda memoria pa dexar que vuelvas al menú principal y volver xugar, mas ye posible que nun funcionare.\n\nReanicia'l xuegu si vuelves ver esti mensaxe.", "outOfMemory.title": "¡Ensin memoria!", "pack.available.title": "Lo disponible", "pack.copyFailure": "Failed to copy packs", "pack.dropConfirm": "¿De xuru que quies amestar los paquetes de darréu a Minecraft?", "pack.dropInfo": "Arrastra y suelta ficheros nesta ventana p'amestar paquetes", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(Posa equí los paquetes)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "<PERSON><PERSON><PERSON> paquete fízose pa una versión nueva de Minecraft y quiciabes yá nun funcione correutamente.", "pack.incompatible.confirm.old": "<PERSON><PERSON><PERSON> paquete fízose pa una versión vieya de Minecraft y quiciabes yá nun funcione correutamente.", "pack.incompatible.confirm.title": "¿De xuru que quies cargar esti paquete?", "pack.incompatible.new": "(Fe<PERSON> pa una versión nueva de Minecraft)", "pack.incompatible.old": "(<PERSON><PERSON> pa una versión vieya de Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "A<PERSON><PERSON> carpeta contenedora", "pack.selected.title": "Lo seleicionao", "pack.source.builtin": "built-in", "pack.source.feature": "feature", "pack.source.local": "llocal", "pack.source.server": "sir<PERSON><PERSON>", "pack.source.world": "world", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Random variant", "parsing.bool.expected": "Ríquese un valor booleano", "parsing.bool.invalid": "<PERSON>or booleano non válidu: \"%s\" nun ye \"true\" nin \"false\"", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON> un valor doble", "parsing.double.invalid": "Valor doble non válidu: %s", "parsing.expected": "Esperábase «%s»", "parsing.float.expected": "Rí<PERSON>e un valor float", "parsing.float.invalid": "Valor float non válidu: %s", "parsing.int.expected": "Esperábase un enteru", "parsing.int.invalid": "Númberu enteru non válidu: %s", "parsing.long.expected": "Expected long", "parsing.long.invalid": "El valor llongu «%s» nun ye válidu", "parsing.quote.escape": "La secuencia d'escape \"\\%s\" nun ye válida n'una cadena con comillas", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Desconozse la particula %s", "permissions.requires.entity": "Ríquese una entidá pa executar esti comandu equí", "permissions.requires.player": "Ríquese un xugador pa executar esti comandu equí", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Al aplicase:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Desconozse'l predicáu: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Anguaño Realms nun ta sofitáu nes snapshots", "recipe.notFound": "Desconozse la receta: %s", "recipe.toast.description": "Mira'l recetariu", "recipe.toast.title": "¡Hai recetes nueves!", "record.nowPlaying": "Reproduciendo: %s", "recover_world.bug_tracker": "Report a Bug", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "unknown", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "BROKEN ASSETS DETECTED", "resourcePack.high_contrast.name": "Contraste altu", "resourcePack.load_fail": "Falló la recarga del recursu", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "Recursos específicos del mundu", "resourcePack.title": "Esbilla de paquetes de recursos", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Descarga d'un paquete de recursos", "resourcepack.progress": "Baxando %s MB…", "resourcepack.requesting": "Executando la solicitú…", "screenshot.failure": "Nun pudo atroxase la captura: %s", "screenshot.success": "Atroxóse la captura de pantalla como %s", "selectServer.add": "Ames<PERSON> sirvidor", "selectServer.defaultName": "<PERSON><PERSON><PERSON>", "selectServer.delete": "Delete", "selectServer.deleteButton": "Delete", "selectServer.deleteQuestion": "¿De xuru que quies desaniciar esti sirvidor?", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "Conexón direuta", "selectServer.edit": "Edit", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON>)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Xunise al sirvidor", "selectWorld.access_failure": "Hebo un fallu al acceder al mundu", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON> trampes", "selectWorld.allowCommands.info": "/gamemode, /experience, etc.", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "Desaniciar los datos de la caché", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON>r una copia y cargar", "selectWorld.backupJoinSkipButton": "¡Sé lo que toi faciendo!", "selectWorld.backupQuestion.customized": "<PERSON>á nun se sofiten los mundos personalizaos", "selectWorld.backupQuestion.downgrade": "Downgrading a world is not supported", "selectWorld.backupQuestion.experimental": "Los mundos qu'usen axustes esperimentales nun tán sofitaos", "selectWorld.backupQuestion.snapshot": "Do you really want to load this world?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nun sofitamos mundos personalizaos nesta versión de Minecraft. Entá podemos cargar esti mundu y caltener too como taba mas cualesquier terrenu nuevu xeneráu yá nun va ser personalizáu. ¡Disculpa les molesties!", "selectWorld.backupWarning.downgrade": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work. If you still want to continue, please make a backup.", "selectWorld.backupWarning.experimental": "Esti mundu usa axustes esperimentales que podríen dexar de funcionar en cualesquier momentu. Nun podemos garantizar la so carga o funcionamientu. Anda pelo segao, ¿acuéi?", "selectWorld.backupWarning.snapshot": "Esti mundu xugóse per última vegada na versión %s, tas na %s. ¡Fai una copia de seguranza nel casu de que toya!", "selectWorld.bonusItems": "Bagul inicial", "selectWorld.cheats": "Tram<PERSON>", "selectWorld.commands": "Commands", "selectWorld.conversion": "¡Ha convertise!", "selectWorld.conversion.tooltip": "Esti mundu ha abrise nuna versión anterior (como la 1.6.4) pa convertise con seguranza", "selectWorld.create": "<PERSON><PERSON><PERSON> un mundu", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "Paquetes de <PERSON>", "selectWorld.data_read": "L<PERSON>do los datos del mundu...", "selectWorld.delete": "Desaniciar", "selectWorld.deleteButton": "Delete", "selectWorld.deleteQuestion": "¿De xuru que quies desaniciar esti mundu?", "selectWorld.deleteWarning": "¡«%s» va perdese… pa siempres!", "selectWorld.delete_failure": "Hebo un fallu al desaniciar el mundu", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "Facer una copia de seguranza", "selectWorld.edit.backupCreated": "Respaldóse: %s", "selectWorld.edit.backupFailed": "Falló la copia de seguranza", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON><PERSON> de respaldo<PERSON>", "selectWorld.edit.backupSize": "Tamañu: %sMB", "selectWorld.edit.export_worldgen_settings": "Esportar los axustes de xeneración", "selectWorld.edit.export_worldgen_settings.failure": "Falló la esportación", "selectWorld.edit.export_worldgen_settings.success": "Esportóse", "selectWorld.edit.openFolder": "<PERSON><PERSON>r la carpeta de mundos", "selectWorld.edit.optimize": "Optimize World", "selectWorld.edit.resetIcon": "Reafitar l'iconu", "selectWorld.edit.save": "Guardar", "selectWorld.edit.title": "Edición del mundu", "selectWorld.enterName": "Nome del mundu", "selectWorld.enterSeed": "Semiente pal xenerador de mundos", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "Asocedió daqué malo mentanto se tentaba de cargar un mundu d'una versión nueva. De primeres, esto foi una operación arriesgada, sent<PERSON><PERSON><PERSON> si nun funcionó.", "selectWorld.futureworld.error.title": "¡Asocedió un fallu!", "selectWorld.gameMode": "<PERSON><PERSON>", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": "Como'l mou sobrevivencia mas los bloques", "selectWorld.gameMode.adventure.line2": "puen amestase o desaniciase bloques", "selectWorld.gameMode.creative": "Creativu", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, constru<PERSON>r y eslorar sin llímites. <PERSON><PERSON><PERSON> volar, tienes materiales ensin fin y nun puedes ser feíu por monstruos.", "selectWorld.gameMode.creative.line1": "<PERSON>un hai llende de recursos, el vuelu ye", "selectWorld.gameMode.creative.line2": "llibre y los bloques destrúinse nel intre", "selectWorld.gameMode.hardcore": "Perdifícil", "selectWorld.gameMode.hardcore.info": "Survival Mode locked to 'Hard' difficulty. You can't respawn if you die.", "selectWorld.gameMode.hardcore.line1": "Como'l mou sobrevivencia mas cola", "selectWorld.gameMode.hardcore.line2": "dificultá al máximu y namás una vida", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "You can look but don't touch.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON> mirar mas nun tocar", "selectWorld.gameMode.survival": "Sobrevivencia", "selectWorld.gameMode.survival.info": "Esplorar un mundu misteriosu onde construyes, colleres, creas y lluches xunto a monstruos.", "selectWorld.gameMode.survival.line1": "Busca recursos, ellabora coses,", "selectWorld.gameMode.survival.line2": "xubi niveles, comi y sobrevivi", "selectWorld.gameRules": "Regles de la partida", "selectWorld.import_worldgen_settings": "Importar axustes", "selectWorld.import_worldgen_settings.failure": "Hebo un fallu al importar los axustes", "selectWorld.import_worldgen_settings.select_file": "Esbilla d'un ficheru d'axustes (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "Zombie", "selectWorld.load_folder_access": "¡Nun pue lleese o accedese a la carpeta onde se guarden los mundos!", "selectWorld.loading_list": "Loading World List", "selectWorld.locked": "Bloquiáu por otra instancia de Minecraft n'execución", "selectWorld.mapFeatures": "Xenerar estructures", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "Tipu del mundu", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Más opciones del mundu…", "selectWorld.newWorld": "<PERSON><PERSON><PERSON> nuevu", "selectWorld.recreate": "Recrear", "selectWorld.recreate.customized.text": "Vamos tentar de recrear el mundu cola mesma semiente y propiedaes mas va perdese cualesquier personalización del terrén. ¡Disculpa les molesties!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "Asocedió daqué malo mentanto se tentaba de recrear un mundu.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "Va guardase en:", "selectWorld.search": "search for worlds", "selectWorld.seedInfo": "Dexar baleru pa semiente al debalu", "selectWorld.select": "Xugar nel mundu seleicionáu", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": "Esbilla d'un mundu", "selectWorld.tooltip.fromNewerVersion1": "¡El mundu guardóse nuna versión nueva,", "selectWorld.tooltip.fromNewerVersion2": "cargalu quiciabes cause problemes!", "selectWorld.tooltip.snapshot1": "<PERSON>un escaezas facer una copia de seguranza d'esti", "selectWorld.tooltip.snapshot2": "mundu enantes de cargalu nesta snapshot.", "selectWorld.unable_to_load": "<PERSON>un se puen cargar los mundos", "selectWorld.version": "Versión:", "selectWorld.versionJoinButton": "<PERSON><PERSON> de toes toes", "selectWorld.versionQuestion": "¿De xuru que quies cargar esti mundu?", "selectWorld.versionUnknown": "desconoz<PERSON>", "selectWorld.versionWarning": "¡Esti mundu xugóse per última vegada na versión %s y cargalu nesta podría toyelu!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Warning! These settings are using experimental features", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Mundu", "sign.edit": "Edit Sign Message", "sleep.not_possible": "No amount of rest can pass this night", "sleep.players_sleeping": "%s/%s xugadores dormiendo", "sleep.skipping_night": "Sleeping through this night", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Desconozse la ralura «%s»", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Ambiente/Redolada", "soundCategory.block": "Bloques", "soundCategory.hostile": "Creatures fosqueres", "soundCategory.master": "Volume principal", "soundCategory.music": "Música", "soundCategory.neutral": "Creatures amistoses", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Bloques qu'emiten música", "soundCategory.ui": "UI", "soundCategory.voice": "Voz/Fala", "soundCategory.weather": "Clima", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "Next Page", "spectatorMenu.previous_page": "Previous Page", "spectatorMenu.root.prompt": "Primi una tecla pa seleicionar un comandu, y volvi primir pa usalu.", "spectatorMenu.team_teleport": "Teletresportase a miembru del equipu", "spectatorMenu.team_teleport.prompt": "Esbilla l'equipu al que teletresportase", "spectatorMenu.teleport": "Teletresportase al xugador", "spectatorMenu.teleport.prompt": "Esbilla'l xugador al que teletresportase", "stat.generalButton": "Xeneral", "stat.itemsButton": "Oxetos", "stat.minecraft.animals_bred": "<PERSON>es c<PERSON>", "stat.minecraft.aviate_one_cm": "Distancia n'élitru", "stat.minecraft.bell_ring": "Campanes tocaes", "stat.minecraft.boat_one_cm": "Distancia en barca", "stat.minecraft.clean_armor": "Pieces d'armadura llim<PERSON>es", "stat.minecraft.clean_banner": "Estandartes llimpiaos", "stat.minecraft.clean_shulker_box": "Shulker Boxes Cleaned", "stat.minecraft.climb_one_cm": "Distancia esguilada", "stat.minecraft.crouch_one_cm": "Distancia encuxáu", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> blo<PERSON>u polos escudos", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON> fechu", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON>u fechu que s'absorbió", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON>u fechu que se resistió", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON> reci<PERSON><PERSON>u", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON><PERSON> de muer<PERSON>", "stat.minecraft.drop": "<PERSON><PERSON><PERSON> so<PERSON>", "stat.minecraft.eat_cake_slice": "Cachos de tarta comíos", "stat.minecraft.enchant_item": "Oxetos encantexaos", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON> cayida", "stat.minecraft.fill_cauldron": "<PERSON><PERSON>", "stat.minecraft.fish_caught": "Pexes pescaos", "stat.minecraft.fly_one_cm": "Distancia esnalada", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Distancia en caballu", "stat.minecraft.inspect_dispenser": "Llanzadores buscaos", "stat.minecraft.inspect_dropper": "Apurridores buscaos", "stat.minecraft.inspect_hopper": "Embu<PERSON>", "stat.minecraft.interact_with_anvil": "Interaiciones con xuncles", "stat.minecraft.interact_with_beacon": "Interaiciones con balices", "stat.minecraft.interact_with_blast_furnace": "Interaciones con altos fornos", "stat.minecraft.interact_with_brewingstand": "Interaiciones con destiladores", "stat.minecraft.interact_with_campfire": "Interaiciones con fogueres", "stat.minecraft.interact_with_cartography_table": "Interaiciones con meses de cartografía", "stat.minecraft.interact_with_crafting_table": "Interaiciones con meses d'ellaboración", "stat.minecraft.interact_with_furnace": "Interaiciones con fornos", "stat.minecraft.interact_with_grindstone": "Interaiciones con piedres p'amolar", "stat.minecraft.interact_with_lectern": "Interaiciones con atriles", "stat.minecraft.interact_with_loom": "Interaiciones con telares", "stat.minecraft.interact_with_smithing_table": "Interaiciones con meses de ferrería", "stat.minecraft.interact_with_smoker": "Interaiciones con afumadores", "stat.minecraft.interact_with_stonecutter": "Interaiciones con cortapiedres", "stat.minecraft.jump": "Blincos", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON>", "stat.minecraft.minecart_one_cm": "Distancia en vagoneta", "stat.minecraft.mob_kills": "Creatures mataes", "stat.minecraft.open_barrel": "<PERSON><PERSON> abiertos", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> abiertos", "stat.minecraft.open_enderchest": "Ba<PERSON><PERSON> <PERSON>'<PERSON><PERSON> abie<PERSON>", "stat.minecraft.open_shulker_box": "Caxes de shulkers abiertes", "stat.minecraft.pig_one_cm": "Distancia en gochu", "stat.minecraft.play_noteblock": "Bloques musicales usaos", "stat.minecraft.play_record": "Vin<PERSON>s reproducíos", "stat.minecraft.play_time": "Tiempu de xuegu", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> matao<PERSON>", "stat.minecraft.pot_flower": "Plantes plantaes", "stat.minecraft.raid_trigger": "Invasiones aicionaes", "stat.minecraft.raid_win": "Invasiones ganaes", "stat.minecraft.sleep_in_bed": "Vegaes dormíes nuna cama", "stat.minecraft.sneak_time": "Tiempu encuxáu", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> corrida", "stat.minecraft.strider_one_cm": "Distance by Strider", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> nalada", "stat.minecraft.talked_to_villager": "Falao colos al<PERSON>", "stat.minecraft.target_hit": "Targets Hit", "stat.minecraft.time_since_death": "Tiempu dende la última muerte", "stat.minecraft.time_since_rest": "Tiempu dende l'últimu descansu", "stat.minecraft.total_world_time": "Tiempu col mundu abiertu", "stat.minecraft.traded_with_villager": "Intercambeos colos al<PERSON>", "stat.minecraft.trigger_trapped_chest": "Bagules trampa aicionaos", "stat.minecraft.tune_noteblock": "Bloques musicales afinaos", "stat.minecraft.use_cauldron": "Agua coyío de calderos", "stat.minecraft.walk_on_water_one_cm": "Distancia caminada sobro l'agua", "stat.minecraft.walk_one_cm": "Distancia caminada", "stat.minecraft.walk_under_water_one_cm": "Distancia caminada sol agua", "stat.mobsButton": "Creatures", "stat_type.minecraft.broken": "Vegaes que rompió", "stat_type.minecraft.crafted": "Vegaes que s'ellaboró", "stat_type.minecraft.dropped": "Vegaes que se soltó", "stat_type.minecraft.killed": "Matesti a %1$s", "stat_type.minecraft.killed.none": "Enxamás nun matesti a esta creatura", "stat_type.minecraft.killed_by": "Matóte %2$s vegada(es)", "stat_type.minecraft.killed_by.none": "Enxamás nun te mató", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON><PERSON> minao", "stat_type.minecraft.picked_up": "Vegaes que se pañó", "stat_type.minecraft.used": "Vegaes que s'usó", "stats.none": "-", "structure_block.button.detect_size": "DETEUTAR", "structure_block.button.load": "CARGAR", "structure_block.button.save": "GUARDAR", "structure_block.custom_data": "Nome d'etiqueta de datos personalizáu", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Esquina: %s", "structure_block.hover.data": "Datos: %s", "structure_block.hover.load": "Cargar: %s", "structure_block.hover.save": "Guardar: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Integridá de cadarmes y semiente", "structure_block.integrity.integrity": "Structure Integrity", "structure_block.integrity.seed": "Structure Seed", "structure_block.invalid_structure_name": "«%s» ye un nome que nun ye válidu", "structure_block.load_not_found": "La cadarma «%s» nun ta disponible ", "structure_block.load_prepare": "Tresnóse la posición de la cadarma «%s»", "structure_block.load_success": "Cargóse la cadarma dende «%s»", "structure_block.mode.corner": "Esquina", "structure_block.mode.data": "Datos", "structure_block.mode.load": "Cargáu", "structure_block.mode.save": "Save", "structure_block.mode_info.corner": "Corner Mode - Placement and size marker", "structure_block.mode_info.data": "Data Mode - Game logic marker", "structure_block.mode_info.load": "Load Mode - Load from file", "structure_block.mode_info.save": "Save Mode - Write to file", "structure_block.position": "Posición rellativa", "structure_block.position.x": "relative Position x", "structure_block.position.y": "relative position y", "structure_block.position.z": "relative position z", "structure_block.save_failure": "Nun pue guardase la estrcutura «%s»", "structure_block.save_success": "La estructura guardóse como «%s»", "structure_block.show_air": "Show Invisible Blocks:", "structure_block.show_boundingbox": "Show Bounding Box:", "structure_block.size": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.size.x": "structure size x", "structure_block.size.y": "structure size y", "structure_block.size.z": "structure size z", "structure_block.size_failure": "Nun pue detectase'l tamañu de la estructura. Amiesta esquines con nomes que concasen", "structure_block.size_success": "Detectóse'l tamañu con ésitu pa «%s»", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Nome", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "Amethyst chimes", "subtitles.block.amethyst_block.resonate": "Amethyst resonates", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON> des<PERSON>", "subtitles.block.anvil.land": "Xuncla que cayó", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON> usada", "subtitles.block.barrel.close": "Barril que s'abre", "subtitles.block.barrel.open": "Barril que se zarra", "subtitles.block.beacon.activate": "Baliza que s'activó", "subtitles.block.beacon.ambient": "Beacon hums", "subtitles.block.beacon.deactivate": "Baliza que se desactivó", "subtitles.block.beacon.power_select": "Beacon power selected", "subtitles.block.beehive.drip": "<PERSON><PERSON> que pinga", "subtitles.block.beehive.enter": "<PERSON><PERSON> qu'entra nuna colmena", "subtitles.block.beehive.exit": "<PERSON><PERSON> que sal d'una colmena", "subtitles.block.beehive.shear": "E<PERSON><PERSON><PERSON>", "subtitles.block.beehive.work": "Abeyes que trabayen", "subtitles.block.bell.resonate": "Bell resonates", "subtitles.block.bell.use": "Bell rings", "subtitles.block.big_dripleaf.tilt_down": "Dripleaf tilts down", "subtitles.block.big_dripleaf.tilt_up": "Dripleaf tilts up", "subtitles.block.blastfurnace.fire_crackle": "Blast Furnace crackles", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON> qu'aborboya", "subtitles.block.bubble_column.bubble_pop": "Borboyos qu'españen", "subtitles.block.bubble_column.upwards_ambient": "Borboyos que flúin", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON> ascendiente", "subtitles.block.bubble_column.whirlpool_ambient": "Remolín <PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> descendiente", "subtitles.block.button.click": "Clic de botón", "subtitles.block.cake.add_candle": "Cake squishes", "subtitles.block.campfire.crackle": "Campfire crackles", "subtitles.block.candle.crackle": "Candle crackles", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.locked": "Bagul p<PERSON>lláu", "subtitles.block.chest.open": "Apertura de bagul", "subtitles.block.chorus_flower.death": "Flor del Coru qu'amostalga", "subtitles.block.chorus_flower.grow": "Flor del Coru que crez", "subtitles.block.comparator.click": "Clic de comparador", "subtitles.block.composter.empty": "Baleróse un compostador", "subtitles.block.composter.fill": "Rellenóse un compostador", "subtitles.block.composter.ready": "Compostador que compostó", "subtitles.block.conduit.activate": "Conduit activates", "subtitles.block.conduit.ambient": "Conduit pulses", "subtitles.block.conduit.attack.target": "Conduit attacks", "subtitles.block.conduit.deactivate": "Conduit deactivates", "subtitles.block.copper_bulb.turn_off": "Copper Bulb turns off", "subtitles.block.copper_bulb.turn_on": "Copper Bulb turns on", "subtitles.block.copper_trapdoor.close": "Trapdoor closes", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter fails crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "Decorated Pot shatters", "subtitles.block.dispenser.dispense": "<PERSON><PERSON><PERSON> ll<PERSON>", "subtitles.block.dispenser.fail": "Fallu del llanzador", "subtitles.block.door.toggle": "Restallu de puerta", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Enchanting Table used", "subtitles.block.end_portal.spawn": "End Portal opens", "subtitles.block.end_portal_frame.fill": "Eye of <PERSON><PERSON> attaches", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "Portiella que restalla", "subtitles.block.fire.ambient": "Chisporrotazu de fueu", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Tadpole hatches", "subtitles.block.furnace.fire_crackle": "Chisporrotazu de fornu", "subtitles.block.generic.break": "B<PERSON>que rotu", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "Pasos", "subtitles.block.generic.hit": "Rotura de bloque", "subtitles.block.generic.place": "Asitiamientu de bloque", "subtitles.block.grindstone.use": "Piedra p'amolar que s'usó", "subtitles.block.growing_plant.crop": "Plant cropped", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON> es<PERSON> per un bloque de miel", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON>", "subtitles.block.iron_trapdoor.open": "Apertura de trapiella", "subtitles.block.lava.ambient": "Borbotu de llava", "subtitles.block.lava.extinguish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.lever.click": "Clic de palanca", "subtitles.block.note_block.note": "Bloque musical sonando", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "Movición de pistón", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> que pinga", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava drips into Cauldron", "subtitles.block.pointed_dripstone.drip_water": "Agua que pinga", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Water drips into Cauldron", "subtitles.block.pointed_dripstone.land": "Stalactite crashes down", "subtitles.block.portal.ambient": "Zumbíu de portal", "subtitles.block.portal.travel": "Portal noise fades", "subtitles.block.portal.trigger": "Portal noise intensifies", "subtitles.block.pressure_plate.click": "Clic de placa presión", "subtitles.block.pumpkin.carve": "Shears carve", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON><PERSON><PERSON>'<PERSON>", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> whooshes", "subtitles.block.respawn_anchor.charge": "Bloque de remanecimientu cargáu", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON><PERSON> depletes", "subtitles.block.respawn_anchor.set_spawn": "Bloque de remanecimientu qu'afita'l puntu d'aprucida", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Burbuyes sculk", "subtitles.block.sculk.spread": "Sculk medra", "subtitles.block.sculk_catalyst.bloom": "Catalizador de Sculk florez", "subtitles.block.sculk_sensor.clicking": "Sensor de Sculk trisca", "subtitles.block.sculk_sensor.clicking_stop": "Sensor de Sculk dexa de triscar", "subtitles.block.sculk_shrieker.shriek": "Glayador de Sculk glaya", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON>", "subtitles.block.shulker_box.open": "Apertura de xúlquer", "subtitles.block.sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.smithing_table.use": "Smithing Table used", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON><PERSON> qu'a<PERSON><PERSON>", "subtitles.block.sniffer_egg.crack": "Sniffer Egg cracks", "subtitles.block.sniffer_egg.hatch": "Sniffer Egg hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "Sponge sucks", "subtitles.block.sweet_berry_bush.pick_berries": "Berries pop", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Trial Spawner closes", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Trial Spawner opens", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Trial Spawner spawns a mob", "subtitles.block.tripwire.attach": "Cable armadiella conectáu", "subtitles.block.tripwire.click": "Clic de cable armadiella", "subtitles.block.tripwire.detach": "Cable armadiella desconectáu", "subtitles.block.vault.activate": "Vault ignites", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Vault closes", "subtitles.block.vault.deactivate": "<PERSON>ault extinguishes", "subtitles.block.vault.eject_item": "Vault ejects item", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Vault opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "Agua que flúi", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "Book placed", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "Book taken", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "Escayos que pinchen", "subtitles.entity.allay.ambient_with_item": "Allay seeks", "subtitles.entity.allay.ambient_without_item": "Allay yearns", "subtitles.entity.allay.death": "<PERSON><PERSON> dies", "subtitles.entity.allay.hurt": "Allay hurts", "subtitles.entity.allay.item_given": "<PERSON>ay chortles", "subtitles.entity.allay.item_taken": "Allay allays", "subtitles.entity.allay.item_thrown": "Allay tosses", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> grunts", "subtitles.entity.armadillo.brush": "Los Escudos se desprienden al cepiyase", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "Armadillo lands", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "Armadillo rolls up", "subtitles.entity.armadillo.scute_drop": "L'Armadillu despriende escudos", "subtitles.entity.armadillo.unroll_finish": "Armadillo unrolls", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON><PERSON>", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON> qu'impautó", "subtitles.entity.arrow.hit_player": "G<PERSON>elpe a xugador", "subtitles.entity.arrow.shoot": "Disparu de flecha", "subtitles.entity.axolotl.attack": "Axolote qu'ataca", "subtitles.entity.axolotl.death": "Axolote que muerre", "subtitles.entity.axolotl.hurt": "Axolote que fire", "subtitles.entity.axolotl.idle_air": "Axolotl chirps", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "Axolote que chisca", "subtitles.entity.axolotl.swim": "Axolote que nala", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON>'es<PERSON>", "subtitles.entity.bat.death": "Esperteyu que muerre", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Esperteyu que llevanta'l vuelu", "subtitles.entity.bee.ambient": "<PERSON>ya que runfa", "subtitles.entity.bee.death": "<PERSON><PERSON> que muerre", "subtitles.entity.bee.hurt": "<PERSON><PERSON> man<PERSON>", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": "Abeya enfadada que runfa", "subtitles.entity.bee.pollinate": "<PERSON>ya feliz que runfa", "subtitles.entity.bee.sting": "Abeya que pica", "subtitles.entity.blaze.ambient": "E<PERSON><PERSON>ritu llumiegu qu'alienda", "subtitles.entity.blaze.burn": "Chisporrotazu d'espíritu llumiegu", "subtitles.entity.blaze.death": "Espíritu llumiegu que muerre", "subtitles.entity.blaze.hurt": "Esp<PERSON>rit<PERSON> ll<PERSON>", "subtitles.entity.blaze.shoot": "Disparos d'espíritu llumiegu", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "Breeze charges", "subtitles.entity.breeze.death": "<PERSON><PERSON> dies", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> deflects", "subtitles.entity.breeze.hurt": "Breeze hurts", "subtitles.entity.breeze.idle_air": "Breeze flies", "subtitles.entity.breeze.idle_ground": "Breeze whirs", "subtitles.entity.breeze.inhale": "Breeze inhales", "subtitles.entity.breeze.jump": "<PERSON><PERSON> jumps", "subtitles.entity.breeze.land": "Breeze lands", "subtitles.entity.breeze.shoot": "Breeze shoots", "subtitles.entity.breeze.slide": "Breeze slides", "subtitles.entity.breeze.whirl": "Breeze whirls", "subtitles.entity.breeze.wind_burst": "Wind Charge bursts", "subtitles.entity.camel.ambient": "Camel grunts", "subtitles.entity.camel.dash": "Camel yeets", "subtitles.entity.camel.dash_ready": "Camel recovers", "subtitles.entity.camel.death": "Camel dies", "subtitles.entity.camel.eat": "Camel eats", "subtitles.entity.camel.hurt": "Camel hurts", "subtitles.entity.camel.saddle": "Saddle equips", "subtitles.entity.camel.sit": "Camel sits down", "subtitles.entity.camel.stand": "Camel stands up", "subtitles.entity.camel.step": "Camel steps", "subtitles.entity.camel.step_sand": "Camel sands", "subtitles.entity.cat.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "Cat begs", "subtitles.entity.cat.death": "Gatu que muerre", "subtitles.entity.cat.eat": "Gatu que come", "subtitles.entity.cat.hiss": "Gatu que bufa", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.chicken.ambient": "Escacarexu", "subtitles.entity.chicken.death": "Pita que muerre", "subtitles.entity.chicken.egg": "Pita que punxo un güevu", "subtitles.entity.chicken.hurt": "Pita mancada", "subtitles.entity.cod.death": "Bacalláu que muerre", "subtitles.entity.cod.flop": "Bacalláu qu'aletia", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "Mu de vaca", "subtitles.entity.cow.death": "Vaca que muerre", "subtitles.entity.cow.hurt": "Vaca mancada", "subtitles.entity.cow.milk": "Vaca mucida", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient": "Dolphin chirps", "subtitles.entity.dolphin.ambient_water": "Toliña que xibla", "subtitles.entity.dolphin.attack": "Toliña qu'ataca", "subtitles.entity.dolphin.death": "Toliña que muerre", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> que come", "subtitles.entity.dolphin.hurt": "Toliña mancada", "subtitles.entity.dolphin.jump": "Toliña que salta", "subtitles.entity.dolphin.play": "Toliña que xuega", "subtitles.entity.dolphin.splash": "Toliña que chisca", "subtitles.entity.dolphin.swim": "Toliña que nala", "subtitles.entity.donkey.ambient": "Pollín que rincha", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON> de <PERSON>", "subtitles.entity.donkey.chest": "Equipamientu de bagul de pullu", "subtitles.entity.donkey.death": "<PERSON><PERSON> que muerre", "subtitles.entity.donkey.eat": "<PERSON><PERSON> que come", "subtitles.entity.donkey.hurt": "<PERSON><PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Afogáu que gargarexa", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "Afogáu que muerre", "subtitles.entity.drowned.hurt": "A<PERSON>g<PERSON><PERSON>", "subtitles.entity.drowned.shoot": "Afogáu que llanzó un tridente", "subtitles.entity.drowned.step": "Afog<PERSON>u qu'anda", "subtitles.entity.drowned.swim": "Afogáu que nala", "subtitles.entity.egg.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON> vieyu", "subtitles.entity.elder_guardian.ambient_land": "Aletéu de Guardián vieyu", "subtitles.entity.elder_guardian.curse": "Maldición de guardián vieyu", "subtitles.entity.elder_guardian.death": "Guardián vieyu que muerre", "subtitles.entity.elder_guardian.flop": "Chaplotazu de Guardián vieyu", "subtitles.entity.elder_guardian.hurt": "Guardián vieyu man<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "Esperteyu que muerre", "subtitles.entity.ender_dragon.flap": "Aletéu de cuélebre", "subtitles.entity.ender_dragon.growl": "Gruñíu de cuélebre", "subtitles.entity.ender_dragon.hurt": "Cuélebre <PERSON>", "subtitles.entity.ender_dragon.shoot": "Disparu d'esperteyu", "subtitles.entity.ender_eye.death": "Eye of <PERSON><PERSON> falls", "subtitles.entity.ender_eye.launch": "Disparu de güeyu <PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON> <PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> que muerre", "subtitles.entity.enderman.hurt": "<PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.teleport": "Teletresporte d'Enderman", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON><PERSON> que muerre", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "Evocador llanza'l fechizu", "subtitles.entity.evoker.celebrate": "Evocador que cellebra", "subtitles.entity.evoker.death": "Evocador que muerre", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "Evocador tresna l'ataque", "subtitles.entity.evoker.prepare_summon": "Evocador tresna invocación", "subtitles.entity.evoker.prepare_wololo": "Evocador tresna fechizu", "subtitles.entity.evoker_fangs.attack": "Francedura de caniles", "subtitles.entity.experience_orb.pickup": "Esperiencia ganada", "subtitles.entity.firework_rocket.blast": "Españíos de voladores", "subtitles.entity.firework_rocket.launch": "Llanzamientos de voladores", "subtitles.entity.firework_rocket.twinkle": "Rellumu de voladores", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON> retrieved", "subtitles.entity.fishing_bobber.splash": "Chiscadura del cebu de la caña de pescar", "subtitles.entity.fishing_bobber.throw": "Caña de pescar llanciada", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> en<PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fox.bite": "Raposu que taragaña", "subtitles.entity.fox.death": "Raposu que muerre", "subtitles.entity.fox.eat": "Raposu que come", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> qu'aulla", "subtitles.entity.fox.sleep": "Raposu que ronca", "subtitles.entity.fox.sniff": "Raposu que golifatia", "subtitles.entity.fox.spit": "Raposu que cuspia", "subtitles.entity.fox.teleport": "Raposu que se teletresporta", "subtitles.entity.frog.ambient": "Frog croaks", "subtitles.entity.frog.death": "<PERSON> dies", "subtitles.entity.frog.eat": "Frog eats", "subtitles.entity.frog.hurt": "Frog hurts", "subtitles.entity.frog.lay_spawn": "Frog lays spawn", "subtitles.entity.frog.long_jump": "Frog jumps", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Quema", "subtitles.entity.generic.death": "Pasamentu", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Españíu", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON><PERSON> fire", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.splash": "Chiscazu", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> que llora", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "Disparu d'un ghast", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Glow Item <PERSON> fills", "subtitles.entity.glow_item_frame.break": "Glow Item Frame broken", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON> <PERSON><PERSON> placed", "subtitles.entity.glow_item_frame.remove_item": "Glow Item Frame empties", "subtitles.entity.glow_item_frame.rotate_item": "Glow Item Frame clicks", "subtitles.entity.glow_squid.ambient": "Glow Squid swims", "subtitles.entity.glow_squid.death": "Calamar re<PERSON>ante que muerre", "subtitles.entity.glow_squid.hurt": "Glow Squid hurts", "subtitles.entity.glow_squid.squirt": "Glow Squid shoots ink", "subtitles.entity.goat.ambient": "<PERSON><PERSON> bleats", "subtitles.entity.goat.death": "<PERSON>abra que muerre", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> que come", "subtitles.entity.goat.horn_break": "<PERSON><PERSON> breaks off", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> man<PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> leaps", "subtitles.entity.goat.milk": "<PERSON><PERSON> gets milked", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stomps", "subtitles.entity.goat.ram_impact": "Goat rams", "subtitles.entity.goat.screaming.ambient": "Goat bellows", "subtitles.entity.goat.step": "Goat steps", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.ambient_land": "Aletéu de Guardián", "subtitles.entity.guardian.attack": "Disparu de guardián", "subtitles.entity.guardian.death": "Guardián que muerre", "subtitles.entity.guardian.flop": "Chaplotazu de guardián", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON><PERSON> d'un hoglin", "subtitles.entity.hoglin.angry": "<PERSON><PERSON>n enfadáu qu'urnia", "subtitles.entity.hoglin.attack": "<PERSON><PERSON>n qu'ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON><PERSON>lin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "Hoglin retreats", "subtitles.entity.hoglin.step": "<PERSON><PERSON>n qu'anda", "subtitles.entity.horse.ambient": "Rinchida de caballu", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Equipamientu d'armadura caballuna", "subtitles.entity.horse.breathe": "<PERSON><PERSON> caball<PERSON>", "subtitles.entity.horse.death": "Caballu que muerre", "subtitles.entity.horse.eat": "Caballu que come", "subtitles.entity.horse.gallop": "Caballu que galopia", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.horse.jump": "Caballu que salta", "subtitles.entity.horse.saddle": "Equipamientu de si<PERSON>ín", "subtitles.entity.husk.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.husk.converted_to_zombie": "Husk converts to Zombie", "subtitles.entity.husk.death": "<PERSON>ia que muerre", "subtitles.entity.husk.hurt": "<PERSON><PERSON> man<PERSON>da", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON> d'ilusionista", "subtitles.entity.illusioner.cast_spell": "Ilusionista llanza'l fechizu", "subtitles.entity.illusioner.death": "Ilusionista que muerre", "subtitles.entity.illusioner.hurt": "Ilusionista mancáu", "subtitles.entity.illusioner.mirror_move": "Duplicación d'Ilusionista", "subtitles.entity.illusioner.prepare_blindness": "Ilusionista tresna ceguera", "subtitles.entity.illusioner.prepare_mirror": "Ilusionista tresna imaxe espeyu", "subtitles.entity.iron_golem.attack": "Ataque de gólem de fierro", "subtitles.entity.iron_golem.damage": "Gólem de fierro que fraña", "subtitles.entity.iron_golem.death": "Gólem de fierro que muerre", "subtitles.entity.iron_golem.hurt": "Gólem de fierro man<PERSON>áu", "subtitles.entity.iron_golem.repair": "Gólem de fierru iguáu", "subtitles.entity.item.break": "Rotura d'oxetu", "subtitles.entity.item.pickup": "Pañadura <PERSON>'<PERSON>", "subtitles.entity.item_frame.add_item": "Colocadura d'oxetu nel marcu", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON> as<PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON> bale<PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON> de marcu d'o<PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.llama.angry": "Balido de llapada furiosa", "subtitles.entity.llama.chest": "Llama equipada con bagul", "subtitles.entity.llama.death": "<PERSON>lama que muerre", "subtitles.entity.llama.eat": "<PERSON><PERSON>a que come", "subtitles.entity.llama.hurt": "Llama man<PERSON>", "subtitles.entity.llama.spit": "<PERSON><PERSON><PERSON><PERSON> ll<PERSON>", "subtitles.entity.llama.step": "<PERSON><PERSON>a qu'anda", "subtitles.entity.llama.swag": "Llama decorada", "subtitles.entity.magma_cube.death": "<PERSON><PERSON>u magmáticu que muerre", "subtitles.entity.magma_cube.hurt": "<PERSON><PERSON><PERSON> mag<PERSON><PERSON> man<PERSON>", "subtitles.entity.magma_cube.squish": "<PERSON><PERSON> de llimu magm<PERSON>u", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Vagoneta en movimientu", "subtitles.entity.mooshroom.convert": "Mooshroom transforms", "subtitles.entity.mooshroom.eat": "Mooshroom que pastia", "subtitles.entity.mooshroom.milk": "Mooshroom mucida", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom mucida sospechosamente", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON><PERSON> de mula", "subtitles.entity.mule.angry": "<PERSON><PERSON> neighs", "subtitles.entity.mule.chest": "Mula equipada con bagul", "subtitles.entity.mule.death": "<PERSON><PERSON> que muerre", "subtitles.entity.mule.eat": "<PERSON><PERSON> que come", "subtitles.entity.mule.hurt": "<PERSON><PERSON> man<PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "Cuadru descolgáu", "subtitles.entity.painting.place": "Cuadru colgáu", "subtitles.entity.panda.aggressive_ambient": "Panda huffs", "subtitles.entity.panda.ambient": "Panda pants", "subtitles.entity.panda.bite": "Panda que taragaña", "subtitles.entity.panda.cant_breed": "Panda bleats", "subtitles.entity.panda.death": "Panda que muerre", "subtitles.entity.panda.eat": "Panda que come", "subtitles.entity.panda.hurt": "<PERSON><PERSON>", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON>'s nose tickles", "subtitles.entity.panda.sneeze": "<PERSON><PERSON> sneezes", "subtitles.entity.panda.step": "Panda qu'anda", "subtitles.entity.panda.worried_ambient": "Panda whimpers", "subtitles.entity.parrot.ambient": "Loru que fala", "subtitles.entity.parrot.death": "<PERSON><PERSON> que muerre", "subtitles.entity.parrot.eats": "<PERSON><PERSON> que come", "subtitles.entity.parrot.fly": "Parrot flutters", "subtitles.entity.parrot.hurts": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON> qu'alienda", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "Parrot whirs", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON> que xibla", "subtitles.entity.parrot.imitate.drowned": "Loru que gargarexa", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.ender_dragon": "Loru que ruxe", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.evoker": "Loru que marmulla", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON> que llora", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON> qu'urnia", "subtitles.entity.parrot.imitate.husk": "<PERSON>ru que xime", "subtitles.entity.parrot.imitate.illusioner": "Parrot murmurs", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.pillager": "Parrot murmurs", "subtitles.entity.parrot.imitate.ravager": "<PERSON>ru que gruñe", "subtitles.entity.parrot.imitate.shulker": "<PERSON>ru que ta al osma", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Parrot rattles", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON> cafiando", "subtitles.entity.parrot.imitate.vindicator": "Loru que marmulla", "subtitles.entity.parrot.imitate.warden": "Parrot whines", "subtitles.entity.parrot.imitate.witch": "Loru que ri", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>rro<PERSON> growls", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.phantom.ambient": "<PERSON>ll<PERSON><PERSON> de <PERSON>", "subtitles.entity.phantom.bite": "Pantasma que taragaña", "subtitles.entity.phantom.death": "Pantasma que muerre", "subtitles.entity.phantom.flap": "Pantasma qu'aletia", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.swoop": "Phantom swoops", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON> de go<PERSON>", "subtitles.entity.pig.death": "Gochu que muerre", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "Saddle equips", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> repara nun oxetu", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> qu'urnia", "subtitles.entity.piglin.angry": "<PERSON><PERSON> enfadáu qu'urnia", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> que cellebra", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> convertíu en piglin zombi", "subtitles.entity.piglin.death": "<PERSON><PERSON> que muerre", "subtitles.entity.piglin.hurt": "<PERSON><PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> celosu qu'urnia", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> retreats", "subtitles.entity.piglin.step": "<PERSON><PERSON> qu'anda", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> snorts angrily", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON>lin Brute converts to Zombified Piglin", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> dies", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> hurts", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> steps", "subtitles.entity.pillager.ambient": "Saquiador que marmulla", "subtitles.entity.pillager.celebrate": "Saquiador que cellebra", "subtitles.entity.pillager.death": "Saqui<PERSON> que muerre", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.crit": "Ataque cr<PERSON>u", "subtitles.entity.player.attack.knockback": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.strong": "Ataque fuerte", "subtitles.entity.player.attack.sweep": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.weak": "Ataque feble", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Xu<PERSON>r que muerre", "subtitles.entity.player.freeze_hurt": "Xugador que se conxela", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "Player drowning", "subtitles.entity.player.hurt_on_fire": "Player burns", "subtitles.entity.player.levelup": "Xuba de nivel de x<PERSON>dor", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.death": "<PERSON>su polar que muerre", "subtitles.entity.polar_bear.hurt": "Osu polar mancáu", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON> que rompió", "subtitles.entity.potion.throw": "Llanzam<PERSON><PERSON> de f<PERSON>cu", "subtitles.entity.puffer_fish.blow_out": "Pexe globu que se deshincha", "subtitles.entity.puffer_fish.blow_up": "Pexe globu que s'hincha", "subtitles.entity.puffer_fish.death": "Pexe globu que muerre", "subtitles.entity.puffer_fish.flop": "Pufferfish flops", "subtitles.entity.puffer_fish.hurt": "Pexe globu mancáu", "subtitles.entity.puffer_fish.sting": "Pexe globu que pica", "subtitles.entity.rabbit.ambient": "Coneyu que chilla", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>u qu'ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON>u que muerre", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "Coneyu que blinca", "subtitles.entity.ravager.ambient": "Afarador que gruñe", "subtitles.entity.ravager.attack": "Afarador que taragaña", "subtitles.entity.ravager.celebrate": "Afarador que cellebra", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON>ador que ruxe", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON> qu'anda", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.salmon.death": "Salmón que muerre", "subtitles.entity.salmon.flop": "Salmon flops", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.ambient": "Bee d'oveya", "subtitles.entity.sheep.death": "Oveya que muerre", "subtitles.entity.sheep.hurt": "Oveya mancada", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "Disparu de xúlquer", "subtitles.entity.shulker.teleport": "Teletresporte de xúlquer", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> Bullet explodes", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> Bullet breaks", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.death": "Xibiella que muerre", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON> man<PERSON>da", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON> de Cadarma", "subtitles.entity.skeleton.converted_to_stray": "Skeleton converts to Stray", "subtitles.entity.skeleton.death": "Cadarma que muerre", "subtitles.entity.skeleton.hurt": "Cadarma mancada", "subtitles.entity.skeleton.shoot": "Disparu de cardarma", "subtitles.entity.skeleton_horse.ambient": "Lloríu de caballu de cadarma", "subtitles.entity.skeleton_horse.death": "Muerte de caballu esquel<PERSON>u", "subtitles.entity.skeleton_horse.hurt": "C<PERSON>ll<PERSON> man<PERSON> de ca<PERSON>", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Skeleton Horse swims", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON> qu'ataca", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON> ll<PERSON>", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.sniffer.digging": "Sniffer digs", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> stands up", "subtitles.entity.sniffer.drop_seed": "Sniffer drops seed", "subtitles.entity.sniffer.eat": "Sniffer eats", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Egg hatches", "subtitles.entity.sniffer.happy": "Sniffer delights", "subtitles.entity.sniffer.hurt": "Sniffer hurts", "subtitles.entity.sniffer.idle": "Sniffer grunts", "subtitles.entity.sniffer.scenting": "Sniffer scents", "subtitles.entity.sniffer.searching": "Sniffer searches", "subtitles.entity.sniffer.sniffing": "Sniffer sniffs", "subtitles.entity.sniffer.step": "Sniffer steps", "subtitles.entity.snow_golem.death": "Gólem de nieve que muerre", "subtitles.entity.snow_golem.hurt": "Go<PERSON> de nieve man<PERSON>", "subtitles.entity.snowball.throw": "<PERSON>ella arrefundida", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.spider.death": "Araña que muerre", "subtitles.entity.spider.hurt": "Araña mancada", "subtitles.entity.squid.ambient": "Calamar que nala", "subtitles.entity.squid.death": "Calamar que muerre", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.squirt": "Calamar que dispara tinta", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON> de v<PERSON>mund<PERSON>", "subtitles.entity.stray.death": "Vagamundu que muerre", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.strider.happy": "Strider warbles", "subtitles.entity.strider.hurt": "Strider hurts", "subtitles.entity.strider.idle": "Strider chirps", "subtitles.entity.strider.retreat": "Strider retreats", "subtitles.entity.tadpole.death": "<PERSON><PERSON> dies", "subtitles.entity.tadpole.flop": "Tadpole flops", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "Tadpole hurts", "subtitles.entity.tnt.primed": "Xiblíu de TNT", "subtitles.entity.tropical_fish.death": "Pexe tropical que muerre", "subtitles.entity.tropical_fish.flop": "Tropical Fish flops", "subtitles.entity.tropical_fish.hurt": "Tropical Fish hurts", "subtitles.entity.turtle.ambient_land": "Turtle chirps", "subtitles.entity.turtle.death": "Tortuga que muerre", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON><PERSON> bebé que muerre", "subtitles.entity.turtle.egg_break": "<PERSON> breaks", "subtitles.entity.turtle.egg_crack": "Turtle Egg cracks", "subtitles.entity.turtle.egg_hatch": "Turtle Egg hatches", "subtitles.entity.turtle.hurt": "Tortuga mancada", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON><PERSON> bebé mancada", "subtitles.entity.turtle.lay_egg": "Tortuga que punxo un güevu", "subtitles.entity.turtle.shamble": "Turtle shambles", "subtitles.entity.turtle.shamble_baby": "Baby <PERSON> shambles", "subtitles.entity.turtle.swim": "Tortuga que nala", "subtitles.entity.vex.ambient": "Picuscayu cafiando", "subtitles.entity.vex.charge": "Glayíu de <PERSON>", "subtitles.entity.vex.death": "Muerte de p<PERSON>cayu", "subtitles.entity.vex.hurt": "Picuscayu man<PERSON>áu", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "Aldeanu que cellebra", "subtitles.entity.villager.death": "Aldeanu que muerre", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.no": "Aldeanu refugando", "subtitles.entity.villager.trade": "Aldeanu comerciando", "subtitles.entity.villager.work_armorer": "Ferreru d'armadures que trabaya", "subtitles.entity.villager.work_butcher": "Carniceru que trabaya", "subtitles.entity.villager.work_cartographer": "Cartógrafu que trabaya", "subtitles.entity.villager.work_cleric": "C<PERSON>rigu que trabaya", "subtitles.entity.villager.work_farmer": "Granxeru que trabaya", "subtitles.entity.villager.work_fisherman": "Pescador que trabaya", "subtitles.entity.villager.work_fletcher": "Flecheru que trabaya", "subtitles.entity.villager.work_leatherworker": "Curtidor que trabaya", "subtitles.entity.villager.work_librarian": "Bibliotecariu que trabaya", "subtitles.entity.villager.work_mason": "Albañil que trabaya", "subtitles.entity.villager.work_shepherd": "Oveyeru que trabaya", "subtitles.entity.villager.work_toolsmith": "Ferreru de preseos que trabaya", "subtitles.entity.villager.work_weaponsmith": "Armeru que trabaya", "subtitles.entity.villager.yes": "Aldeanu aceptando", "subtitles.entity.vindicator.ambient": "Marmullu de Vindicador", "subtitles.entity.vindicator.celebrate": "Vindicador que cellebra", "subtitles.entity.vindicator.death": "Muerte de Vindicador", "subtitles.entity.vindicator.hurt": "Vindicador mancáu", "subtitles.entity.wandering_trader.ambient": "Viaxante que marmulla", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.wandering_trader.disappeared": "Viaxante que desapaez", "subtitles.entity.wandering_trader.drink_milk": "Viaxante que bebe lleche", "subtitles.entity.wandering_trader.drink_potion": "Viaxante que bebe un brebaxe", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.no": "Viaxante que refuga", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON> qu'apaez", "subtitles.entity.wandering_trader.trade": "Viaxante que comercia", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON> qu'acepta", "subtitles.entity.warden.agitated": "Warden groans angrily", "subtitles.entity.warden.ambient": "Warden whines", "subtitles.entity.warden.angry": "Warden rages", "subtitles.entity.warden.attack_impact": "Warden lands hit", "subtitles.entity.warden.death": "Warden dies", "subtitles.entity.warden.dig": "Warden digs", "subtitles.entity.warden.emerge": "Warden emerges", "subtitles.entity.warden.heartbeat": "Warden's heart beats", "subtitles.entity.warden.hurt": "Warden hurts", "subtitles.entity.warden.listening": "Warden takes notice", "subtitles.entity.warden.listening_angry": "Warden takes notice angrily", "subtitles.entity.warden.nearby_close": "Warden approaches", "subtitles.entity.warden.nearby_closer": "Warden advances", "subtitles.entity.warden.nearby_closest": "<PERSON> draws close", "subtitles.entity.warden.roar": "Warden roars", "subtitles.entity.warden.sniff": "Warden sniffs", "subtitles.entity.warden.sonic_boom": "Warden booms", "subtitles.entity.warden.sonic_charge": "Warden charges", "subtitles.entity.warden.step": "Warden steps", "subtitles.entity.warden.tendril_clicks": "Warden's tendrils click", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON>", "subtitles.entity.witch.celebrate": "Bruxa que cellebra", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> bebiendo", "subtitles.entity.witch.hurt": "Bruxa mancada", "subtitles.entity.witch.throw": "Bruxa que llancia", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON>er que muerre", "subtitles.entity.wither.hurt": "<PERSON><PERSON>", "subtitles.entity.wither.shoot": "Ataque del wither", "subtitles.entity.wither.spawn": "Lliberación del wither", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON> de cadarma de wither", "subtitles.entity.wither_skeleton.death": "Cadarma de wither que muerre", "subtitles.entity.wither_skeleton.hurt": "Cadarma de wither mancada", "subtitles.entity.wolf.ambient": "L<PERSON>bu qu'aflana", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "Llobu que muerre", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "Llobu recudiéndose", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON><PERSON> d'un zoglin", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> enfadáu qu'urnia", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> qu'ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> que muerre", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> qu'anda", "subtitles.entity.zombie.ambient": "Zombi que xime", "subtitles.entity.zombie.attack_wooden_door": "Door shakes", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON>ta que rompió", "subtitles.entity.zombie.converted_to_drowned": "Zombi convertíu n'afogáu", "subtitles.entity.zombie.death": "Zombi que muerre", "subtitles.entity.zombie.destroy_egg": "<PERSON> stomped", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.infect": "Zombi qu'infesta", "subtitles.entity.zombie_horse.ambient": "Llor<PERSON>u de caballu zombi", "subtitles.entity.zombie_horse.death": "Caballu zombi que muerre", "subtitles.entity.zombie_horse.hurt": "Caballu zombi man<PERSON>", "subtitles.entity.zombie_villager.ambient": "<PERSON><PERSON><PERSON><PERSON>i", "subtitles.entity.zombie_villager.converted": "Zombie Villager vociferates", "subtitles.entity.zombie_villager.cure": "Zombie Villager snuffles", "subtitles.entity.zombie_villager.death": "Aldeanu zombi que muerre", "subtitles.entity.zombie_villager.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> zombi que gruñe", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON> zombi enfadáu que gruñe", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombi que muerre", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> zombi man<PERSON>", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "Ominous horn blares", "subtitles.item.armor.equip": "Gear equips", "subtitles.item.armor.equip_chain": "R<PERSON><PERSON>u d'armadura de cota de malla", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'arm<PERSON>", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> rustle", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON><PERSON> d'armadura d'oru", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON> d'arm<PERSON><PERSON> de <PERSON>erro", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'arm<PERSON><PERSON> de <PERSON>", "subtitles.item.armor.equip_netherite": "<PERSON><PERSON><PERSON><PERSON> d'armad<PERSON> de netherita", "subtitles.item.armor.equip_turtle": "Turtle Shell thunks", "subtitles.item.armor.equip_wolf": "Wolf Armor is fastened", "subtitles.item.armor.unequip_wolf": "Wolf Armor snips away", "subtitles.item.axe.scrape": "Axe scrapes", "subtitles.item.axe.strip": "Axe strips", "subtitles.item.axe.wax_off": "Wax off", "subtitles.item.bone_meal.use": "Bone Meal crinkles", "subtitles.item.book.page_turn": "Page rustles", "subtitles.item.book.put": "Book thumps", "subtitles.item.bottle.empty": "Bottle empties", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "Brushing", "subtitles.item.brush.brushing.gravel": "Brushing Gravel", "subtitles.item.brush.brushing.gravel.complete": "Brushing Gravel completed", "subtitles.item.brush.brushing.sand": "Brushing Sand", "subtitles.item.brush.brushing.sand.complete": "Brushing Sand completed", "subtitles.item.bucket.empty": "Vaciáu de calderu", "subtitles.item.bucket.fill": "En<PERSON>a de calderu", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> scooped", "subtitles.item.bucket.fill_fish": "Fish captured", "subtitles.item.bucket.fill_tadpole": "Tadpole captured", "subtitles.item.bundle.drop_contents": "Bundle empties", "subtitles.item.bundle.insert": "Item packed", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "Item unpacked", "subtitles.item.chorus_fruit.teleport": "Teletresportación de xugador", "subtitles.item.crop.plant": "Crop planted", "subtitles.item.crossbow.charge": "Crossbow charges up", "subtitles.item.crossbow.hit": "Arrow hits", "subtitles.item.crossbow.load": "Crossbow loads", "subtitles.item.crossbow.shoot": "Ballesta que dispara", "subtitles.item.dye.use": "Dye stains", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Zumbíu de bola de fueu", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "Glow Ink Sac splotches", "subtitles.item.goat_horn.play": "<PERSON><PERSON> plays", "subtitles.item.hoe.till": "Fesoria llabrando", "subtitles.item.honey_bottle.drink": "Tragos", "subtitles.item.honeycomb.wax_on": "Wax on", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Ink Sac splotches", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "<PERSON><PERSON><PERSON><PERSON><PERSON> una brúxula", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Crop planted", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Chasquíu de tisories", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Bloquéu con escudu", "subtitles.item.shovel.flatten": "Pala aplanando", "subtitles.item.spyglass.stop_using": "Spyglass retracts", "subtitles.item.spyglass.use": "Spyglass expands", "subtitles.item.totem.use": "Tótem que s'activó", "subtitles.item.trident.hit": "Trident stabs", "subtitles.item.trident.hit_ground": "Trident vibrates", "subtitles.item.trident.return": "Trident returns", "subtitles.item.trident.riptide": "Trident zooms", "subtitles.item.trident.throw": "Trident clangs", "subtitles.item.trident.thunder": "Trident thunder cracks", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Wolf Armor takes damage", "subtitles.item.wolf_armor.repair": "Wolf Armor is repaired", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON> qu'afuxen", "subtitles.ui.cartography_table.take_result": "Map drawn", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "Loom used", "subtitles.ui.stonecutter.take_result": "<PERSON>rtapiedra que s'usó", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "More Information", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "Always", "team.collision.never": "Never", "team.collision.pushOtherTeams": "Emburriar otros equipos", "team.collision.pushOwnTeam": "Emburriar nel propiu equipu", "team.notFound": "Desconozse l'equipu «%s»", "team.visibility.always": "Siempres", "team.visibility.hideForOtherTeams": "An<PERSON><PERSON>r pa otros equipos", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON><PERSON> pal propiu equipu", "team.visibility.never": "Enxamás", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Optional)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Requeríu)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Advancement ID", "telemetry.property.client_id.title": "ID del veceru", "telemetry.property.client_modded.title": "Veceru modificáu", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicada (KB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "Game Mode", "telemetry.property.game_version.title": "Game Version", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "New World", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Sistema operativu", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Render Distance", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Server Type", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "Opinar", "telemetry_info.button.privacy_statement": "Privacy Statement", "telemetry_info.button.show_data": "Abrir los datos", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "<PERSON>tos incluy<PERSON>", "telemetry_info.screen.description": "La recoyida d'estos datos ayúdanos a ameyorar Minecraft indicando la direición que ye relevante pa los xugadores. Tamién pues unviar más opiniones p'ayudanos a siguir ameyorando Minecraft.", "telemetry_info.screen.title": "Recoyida de datos de telemetría", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit system detected: this may prevent you from playing in the future as a 64-bit system will be required!", "title.32bit.deprecation.realms": "Minecraft will soon require a 64-bit system, which will prevent you from playing or using Realms on this device. You will need to manually cancel any Realms subscription.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "32-bit system detected", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "Multixugador (LAN)", "title.multiplayer.other": "Multixugador (sirvidor de terceros)", "title.multiplayer.realms": "Multixugador (Realms)", "title.singleplayer": "Singleplayer", "translation.test.args": "%s %s", "translation.test.complex": "Prefixu, ¡%s%2$s de nueves %s y %1$s finalmente %s y tamién %1$s de nueves!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hola %", "translation.test.invalid2": "hola %s", "translation.test.none": "¡Hola, mundu!", "translation.test.world": "mundu", "trim_material.minecraft.amethyst": "Amethyst Material", "trim_material.minecraft.copper": "Copper Material", "trim_material.minecraft.diamond": "Diamond Material", "trim_material.minecraft.emerald": "Emerald Material", "trim_material.minecraft.gold": "Gold Material", "trim_material.minecraft.iron": "Iron Material", "trim_material.minecraft.lapis": "Lapis Material", "trim_material.minecraft.netherite": "Netherite Material", "trim_material.minecraft.quartz": "Quartz Material", "trim_material.minecraft.redstone": "Redstone Material", "trim_material.minecraft.resin": "Resin Material", "trim_pattern.minecraft.bolt": "Bolt Armor Trim", "trim_pattern.minecraft.coast": "Coast Armor Trim", "trim_pattern.minecraft.dune": "<PERSON>ne Armor <PERSON>", "trim_pattern.minecraft.eye": "Eye Armor Trim", "trim_pattern.minecraft.flow": "Flow Armor Trim", "trim_pattern.minecraft.host": "Host <PERSON><PERSON>", "trim_pattern.minecraft.raiser": "Raiser Armor Trim", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "Sentry Armor Trim", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON> Armor <PERSON>", "trim_pattern.minecraft.silence": "Silence Armor Trim", "trim_pattern.minecraft.snout": "Snout Armor Trim", "trim_pattern.minecraft.spire": "Spire Arm<PERSON>", "trim_pattern.minecraft.tide": "Tide Armor Trim", "trim_pattern.minecraft.vex": "<PERSON><PERSON><PERSON><PERSON> <PERSON>'arm<PERSON> de Vex", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Trim", "trim_pattern.minecraft.wild": "Wild Armor Trim", "tutorial.bundleInsert.description": "Right Click to add items", "tutorial.bundleInsert.title": "Use a Bundle", "tutorial.craft_planks.description": "El recetariu pue ayudate", "tutorial.craft_planks.title": "Fai tables de madera", "tutorial.find_tree.description": "Gólpialu pa recoyer madera", "tutorial.find_tree.title": "Atopa un árbole", "tutorial.look.description": "mur pa xirar", "tutorial.look.title": "Mira alredor usando'l", "tutorial.move.description": "Blinca con %s", "tutorial.move.title": "Móvite con %s, %s, %s y %s", "tutorial.open_inventory.description": "Primi %s", "tutorial.open_inventory.title": "Abri l'inventariu", "tutorial.punch_tree.description": "Col %s", "tutorial.punch_tree.title": "Destrúi l'árbole", "tutorial.socialInteractions.description": "Primi %s p'abrir", "tutorial.socialInteractions.title": "Social Interactions", "upgrade.minecraft.netherite_upgrade": "Meyorador de Netherita"}