{"accessibility.onboarding.accessibility.button": "Instellningger fer te kumb að ymse...", "accessibility.onboarding.screen.narrator": "Tryck på Retur för att aktivera uppläsaren", "accessibility.onboarding.screen.title": "Välkommen till Minecraft!\n\nVill du aktivera uppläsaren eller gå till tillgänglighetsinställningarna?", "addServer.add": "<PERSON><PERSON><PERSON><PERSON>", "addServer.enterIp": "Serveradress", "addServer.enterName": "<PERSON><PERSON><PERSON>", "addServer.resourcePack": "Serverresurspakiet", "addServer.resourcePack.disabled": "Inaktivirað", "addServer.resourcePack.enabled": "Aktivirað", "addServer.resourcePack.prompt": "Påminnels", "addServer.title": "Redigir serverinfo", "advMode.command": "Konsollkommando", "advMode.mode": "Stellningg", "advMode.mode.auto": "Uppriepningg", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "advMode.mode.conditional": "Willkorað", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Byöver Redstone", "advMode.mode.sequence": "Tjaia", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON> will<PERSON>", "advMode.notAllowed": "Du får luv wårå ien operatör i kretiv-moðe", "advMode.notEnabled": "Går it bruk kommandoblokk ą̊ issn-jär serverem", "advMode.previousOutput": "Tiðugera autdata", "advMode.setCommand": "Bistemm spilbuordskommando för blokk", "advMode.setCommand.success": "Bistemm kommando: %s", "advMode.trackOutput": "Spuorå autmatningg", "advMode.triggering": "<PERSON><PERSON><PERSON><PERSON>", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Uokunnut framstig: %s", "advancements.adventure.adventuring_time.description": "Upptettj ollu biom", "advancements.adventure.adventuring_time.title": "Tið på fer äventyr", "advancements.adventure.arbalistic.description": "Ev ijel fem unik mobber min iett armbuostkuott", "advancements.adventure.arbalistic.title": "Armbuoststjytt", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON><PERSON><PERSON> dig nęr ien sculksensor elld ien wakt so dier sją̊ it dig", "advancements.adventure.avoid_vibration.title": "Mją̊kligiet 100", "advancements.adventure.blowback.description": "Döda ett vindväsen med en blockerad vindattack från ett vindväsen", "advancements.adventure.blowback.title": "Vilken blåsning", "advancements.adventure.brush_armadillo.description": "Få hornplåtar från ett bältdjur med en borste", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON>, b<PERSON><PERSON>!", "advancements.adventure.bullseye.description": "Itt ǫ mitt ǫ iett målblokk frǫ dǫfel 30 mieter", "advancements.adventure.bullseye.title": "Mitt i", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Dj<PERSON>ri ien målaðtjeramikkruk ov fjuorę tjermikskuoler", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON> upp<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Var nära en tillverkare när den tillverkar en tillverkare", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON><PERSON> tillverkar tillverkare", "advancements.adventure.fall_from_world_height.description": "Foll frą̊ upi tullam åv wärdn (bygggrensn) nið að buottnem og yvyrliv", "advancements.adventure.fall_from_world_height.title": "Grottur & Skerer", "advancements.adventure.heart_transplanter.description": "Placera ett knarrhj<PERSON>rta mellan två bleka ekstamsblock i samma riktning", "advancements.adventure.heart_transplanter.title": "Hjärttransplantation", "advancements.adventure.hero_of_the_village.description": "<PERSON><PERSON><PERSON><PERSON> ienn by frą̊ iett anfoll min framgaungg", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON>j<PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Upp in iett onunggsblokk fer te demp folleð dett", "advancements.adventure.honey_block_slide.title": "<PERSON> klistreð", "advancements.adventure.kill_a_mob.description": "El uonde uk fiendmonster", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "<PERSON> iett åv wert fiendmonster", "advancements.adventure.kill_all_mobs.title": "Monster werd jagaðer", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "El ienum fugat i nerietn åv ien sculksensor", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON> spriðer sig", "advancements.adventure.lighten_up.description": "Skrapa en kopparlampa med en yxa för att få den att lysa starkare", "advancements.adventure.lighten_up.title": "Upplysande", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Frið<PERSON> ien byskall frą̊ ienum sjokke an will it åvå gainum te djär upp ien jäld", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON><PERSON><PERSON> fer yvy<PERSON>g", "advancements.adventure.minecraft_trials_edition.description": "Kliv in i en utmaningskammare", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> minn ienum armbuost", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.overoverkill.description": "Utdela 50 hjärtan i skada med en enda träff från en stridsklubba", "advancements.adventure.overoverkill.title": "Grovt övervåld", "advancements.adventure.play_jukebox_in_meadows.description": "<PERSON><PERSON><PERSON><PERSON> så loter byres ą̊ livå åv musitjin frą̊ ienum jukeboks", "advancements.adventure.play_jukebox_in_meadows.title": "Musitjin äres", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Les åv kraptsignaln frą̊ ien mäjslaðbuokill min ien jämfyörer", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Kraftn i byökum", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON> upp ett oroväckande kassaskåp med en oroväckande utmaningsnyckel", "advancements.adventure.revaulting.title": "Kassaskåpsknäckare", "advancements.adventure.root.description": "Äventyr, autfuoskningg og strið", "advancements.adventure.root.title": "Äventyr", "advancements.adventure.salvage_sherd.description": "Suop åv iet blokk du ir fundirsam yvyr fer te få ien stjyrvel åv tjeramitjin", "advancements.adventure.salvage_sherd.title": "<PERSON><PERSON><PERSON><PERSON> lievur", "advancements.adventure.shoot_arrow.description": "St<PERSON>ot noð min ienum kuove", "advancements.adventure.shoot_arrow.title": "Sykta", "advancements.adventure.sleep_in_bed.description": "Sov i ien saingg fer te ender återuppstondspunktn denn", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "<PERSON> iett bįeraunggel frą̊ minst 50 mieter", "advancements.adventure.sniper_duel.title": "Prikkstjyttduell", "advancements.adventure.spyglass_at_dragon.description": "<PERSON>og<PERSON> ą̊ Ender-dratjin gainum ien tjiker", "advancements.adventure.spyglass_at_dragon.title": "Ir eð ien fl<PERSON>?", "advancements.adventure.spyglass_at_ghast.description": "Kuogä ą̊ noð skromt gainum ien tjiker", "advancements.adventure.spyglass_at_ghast.title": "Ir eð ien ballongg?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON><PERSON> ą̊ ien papegoj gainum ien tjiker", "advancements.adventure.spyglass_at_parrot.title": "Ir eð ien fugel?", "advancements.adventure.summon_iron_golem.description": "Fuork ien Ienngolem te jåp til friðå ien by", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Wind iett ljuoster ą̊ noð. \nÄr ą̊: Te wind brott iendað wapneð dett ir int noð bra.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON> wind<PERSON>", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON> iett totem fer te fǫ laiveð att fer te lur ðoðn", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON> doðn", "advancements.adventure.trade.description": "Lykkað bytesandel min ienum Byskalle", "advancements.adventure.trade.title": "Uan, uk fin affär!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON> min ienum byskall laingst upi nest bydj-grensem", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON> issu-jär smið<PERSON>jer iessn ukað so ir: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Silence, Vex, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON><PERSON> min styl", "advancements.adventure.trim_with_any_armor_pattern.description": "D<PERSON><PERSON><PERSON>ð rustningsditalja nest ien smið<PERSON>", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ien nyan stil", "advancements.adventure.two_birds_one_arrow.description": "Ev ijel tau spötjer min ienum kuov så går rett gainum", "advancements.adventure.two_birds_one_arrow.title": "Twer fugler min ienum kuove", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON><PERSON> upp ett kassaskåp med en utmaningsnyckel", "advancements.adventure.under_lock_and_key.title": "Bakom lås och bom", "advancements.adventure.use_lodestone.description": "Använd en kompass på magnetit", "advancements.adventure.use_lodestone.title": "Led mig hem, magnetit", "advancements.adventure.very_very_frightening.description": "Itt ǫ ien byskall min lysun", "advancements.adventure.very_very_frightening.title": "<PERSON> uan, ur r<PERSON><PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "El ien ien kaptien i itt anfoll. \nKast<PERSON> an edd int ulað far in i någer bye just nų...", "advancements.adventure.voluntary_exile.title": "Friwillut i landsflykt", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Wvel ą̊ duossniuo... autą̊ te sikk nið", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON><PERSON> sos erin", "advancements.adventure.who_needs_rockets.description": "Använd en vindladdning för att skicka upp dig 8 block i luften", "advancements.adventure.who_needs_rockets.title": "Vem behöver raketer?", "advancements.adventure.whos_the_pillager_now.description": "Djäv ien röver ov iegn midisin", "advancements.adventure.whos_the_pillager_now.title": "<PERSON><PERSON> ir rövern nų?", "advancements.empty": "Eð sir int aut eð ir noð jär...", "advancements.end.dragon_breath.description": "<PERSON><PERSON>t ijuop dr<PERSON>å-andu i ien glasputell", "advancements.end.dragon_breath.title": "<PERSON><PERSON><PERSON> ien <PERSON>", "advancements.end.dragon_egg.description": "Old i drakeddję", "advancements.end.dragon_egg.title": "Nesta djenerasiuon", "advancements.end.elytra.description": "Finn att elytra", "advancements.end.elytra.title": "Imiln ir grensn", "advancements.end.enter_end_gateway.description": "Fly frą̊ enę", "advancements.end.enter_end_gateway.title": "Gemstell launggt brotte", "advancements.end.find_end_city.description": "<PERSON><PERSON><PERSON>ið ini, wen beller enda?", "advancements.end.find_end_city.title": "Staðn i slutę ov spilę", "advancements.end.kill_dragon.description": "Lykka til", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Lypt upp dig end upi 50 blokk frą̊ ien shul<PERSON>kk", "advancements.end.levitate.title": "An sir waiðumt frǫ jär upą̊", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON><PERSON> um ender-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.end.respawn_dragon.title": "Sluteð... Iessn til...", "advancements.end.root.description": "<PERSON><PERSON>?", "advancements.end.root.title": "<PERSON>lut<PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Sją̊ til ien jåpsam bö<PERSON><PERSON>l ev dait ien tårtu ą̊ ien Note Block", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "Sją̊ til ien jåpsam spötję djäv dig föremą̊l", "advancements.husbandry.allay_deliver_item_to_player.title": "Ig ir kommpisn den", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON>g ien aks<PERSON>l i ien kruku", "advancements.husbandry.axolotl_in_a_bucket.title": "Guttugest ruovkrytyreð", "advancements.husbandry.balanced_diet.description": "<PERSON>ät ollt an dug j<PERSON>, uen de um eð ir bra fer dig elld inte", "advancements.husbandry.balanced_diet.title": "Ien diet so ar ollt", "advancements.husbandry.breed_all_animals.description": "Fuol upp oll krytyre!", "advancements.husbandry.breed_all_animals.title": "Twå og twå", "advancements.husbandry.breed_an_animal.description": "Fuol upp tau krytyr i lag", "advancements.husbandry.breed_an_animal.title": "Papigåjur og leðrer", "advancements.husbandry.complete_catalogue.description": "Tem oll massvarianter!", "advancements.husbandry.complete_catalogue.title": "<PERSON>en pris<PERSON>nt so ir komplett", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON><PERSON> ien s<PERSON>", "advancements.husbandry.feed_snifflet.title": "Smą̊snuserer", "advancements.husbandry.fishy_business.description": "Faungg ien fisk", "advancements.husbandry.fishy_business.title": "Fiskisk", "advancements.husbandry.froglights.description": "Åvå oll tuosk<PERSON>uos i baurę dett", "advancements.husbandry.froglights.title": "Minn kraptum uorum i lag!", "advancements.husbandry.kill_axolotl_target.description": "Gokk i lag min ienum aksolotl og wið dar du raivs", "advancements.husbandry.kill_axolotl_target.title": "<PERSON><PERSON><PERSON><PERSON>n so werd dar an ir kompiser!", "advancements.husbandry.leash_all_frog_variants.description": "Tagi oll tuoskvariantär i ien band", "advancements.husbandry.leash_all_frog_variants.title": "Dar ligą upper ini staðn", "advancements.husbandry.make_a_sign_glow.description": "Få texten på en valfri typ av skylt att glöda", "advancements.husbandry.make_a_sign_glow.title": "<PERSON>ys og sjå!", "advancements.husbandry.netherite_hoe.description": "Bruk ien netherittakk fer te fesk ǫ ien fl<PERSON>akk, sę beller du fundir ǫ um du ar gart rett wal i liwę", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON><PERSON> intresse", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON>t att iet snuseregg", "advancements.husbandry.obtain_sniffer_egg.title": "Lupter intressant", "advancements.husbandry.place_dried_ghast_in_water.description": "Placera en uttorkad ghast i vatten", "advancements.husbandry.place_dried_ghast_in_water.title": "Två liter om dagen!", "advancements.husbandry.plant_any_sniffer_seed.description": "Plantir uonde uk snusfri", "advancements.husbandry.plant_any_sniffer_seed.title": "Plantirer frą̊ attri tiðn", "advancements.husbandry.plant_seed.description": "Plantir iet fri og sjǫ ur eð wekser", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON> weks eð lju<PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Ta bort rustningen från en varg med en sax", "advancements.husbandry.remove_wolf_armor.title": "Klipp, klipp!", "advancements.husbandry.repair_wolf_armor.description": "Reparera en skadad vargrustning helt och hållet med hornplåtar från ett bältdjur", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> gott som ny", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Upp i båtn og sigel", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON> ir fel du aut og sigler!", "advancements.husbandry.root.description": "Is<PERSON><PERSON><PERSON>r wärdn ir full i wenner og jätå", "advancements.husbandry.root.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Bruk jäldn te sambel in onundjin frą̊ ien bikup i ien glasputil autǫ te frutt upp biner", "advancements.husbandry.safely_harvest_honey.title": "Bia bykalln uor", "advancements.husbandry.silk_touch_nest.description": "Flytt ǫ ien bi<PERSON>, min trai bia ini, men wa fesiktun", "advancements.husbandry.silk_touch_nest.title": "Flyttningg autǫ bieffekter", "advancements.husbandry.tactical_fishing.description": "Faungg ien fisk... auto spuoð!", "advancements.husbandry.tactical_fishing.title": "Taktikfistje", "advancements.husbandry.tadpole_in_a_bucket.description": "Faungg ien pringgel i ien kruku", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON><PERSON> tuosker, små tuosker", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON> iett k<PERSON>tyr", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON><PERSON> best kompiser", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON> wakseð frą̊ ien kupper<PERSON>!", "advancements.husbandry.wax_off.title": "Tag dieðo wakseð", "advancements.husbandry.wax_on.description": "Ev ą̊ biwaks ą̊ ien kup<PERSON>!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON> waksa", "advancements.husbandry.whole_pack.description": "Tämj alla vargvarianter", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON>", "advancements.nether.all_effects.description": "<PERSON><PERSON><PERSON> oll effekter ą̊ summu gangg", "advancements.nether.all_effects.title": "Ur kamum wið juot?", "advancements.nether.all_potions.description": "Sją̊ til ollu effekter frą̊ drykkn kumo ą̊ summu gangg", "advancements.nether.all_potions.title": "<PERSON><PERSON> effekt<PERSON> b<PERSON>", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> ien <PERSON>k", "advancements.nether.brew_potion.title": "Lok<PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "Leð iet <PERSON><PERSON><PERSON><PERSON> so mitjið eð bar gor", "advancements.nether.charge_respawn_anchor.title": "Int riktut \"niu\" liw", "advancements.nether.create_beacon.description": "Byddj og plasir aut ien fyr", "advancements.nether.create_beacon.title": "<PERSON><PERSON><PERSON> iem fyrn", "advancements.nether.create_full_beacon.description": "Fest i fyrn för fullt", "advancements.nether.create_full_beacon.title": "Fyrwakter", "advancements.nether.distract_piglin.description": "<PERSON>r krigsgraiser min gullę", "advancements.nether.distract_piglin.title": "Au au au ur eð skain", "advancements.nether.explore_nether.description": "Undersyötj oll miljöer i Nether", "advancements.nether.explore_nether.title": "Gäver turistmål", "advancements.nether.fast_travel.description": "Bruk Nether te fårå sju km i yvyrwärdn", "advancements.nether.fast_travel.title": "Subrymdsbubbel", "advancements.nether.find_bastion.description": "Gokk in i lemmningger etter ienum skanse", "advancements.nether.find_bastion.title": "Eð war dier dar dåg<PERSON>", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON>t dig in i ien <PERSON>-skans", "advancements.nether.find_fortress.title": "<PERSON><PERSON> u<PERSON>t iemsk skans", "advancements.nether.get_wither_skull.description": "Fǫ skolln frą̊ iett wither-bįeraunggel", "advancements.nether.get_wither_skull.title": "<PERSON><PERSON> skromtbįeraunggel", "advancements.nether.loot_bastion.description": "Plunder ien tjistu i skanslemmninggum", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Sjǫ til fǫ i ien komplett netheritrustningg", "advancements.nether.netherite_armor.title": "Tätj mig min rapplę", "advancements.nether.obtain_ancient_debris.description": "Fǫ i ljutgamblų rappel", "advancements.nether.obtain_ancient_debris.title": "<PERSON><PERSON>d niði dju<PERSON>ð noger", "advancements.nether.obtain_blaze_rod.description": "Tag ien stor frǫ ien brenner", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON> jä<PERSON>n", "advancements.nether.obtain_crying_obsidian.description": "Sjǫ til fǫ i grainend obsidianblokk", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON> stjär lotjin?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> ien ghast minn jäldbollem", "advancements.nether.return_to_sender.title": "Atter að onum so stjikkeð", "advancements.nether.ride_strider.description": "Raið ǫ ienum straiðer min ienum warpaðsopp ǫ ienum pinne", "advancements.nether.ride_strider.title": "Issn<PERSON><PERSON><PERSON><PERSON> b<PERSON>n ar fuoter", "advancements.nether.ride_strider_in_overworld_lava.description": "<PERSON><PERSON> liu<PERSON>gt ą̊ ienum Striðer yvyr ien lava<PERSON>ju i Yvyrwärdn", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON> sos iema", "advancements.nether.root.description": "Tag minn dig somorklauter", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON> fram <PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON> ieg<PERSON>t frą̊ <PERSON>her, tag iem eð að yvyerwärdem... og se el eð", "advancements.nether.uneasy_alliance.title": "Ųodäl allians", "advancements.nether.use_lodestone.description": "Bruk ien kompass ǫ ien magnetitstien", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> mig iem", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "<PERSON><PERSON><PERSON><PERSON> ien sombibyskall swagera og sę buot om", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Blokkir ien prujektil min ienum stjölde", "advancements.story.deflect_arrow.title": "Int i ðag, <PERSON>j<PERSON>r tokk fer", "advancements.story.enchant_item.description": "Ugs iett föremål upą̊ iett fetrullningsbuord", "advancements.story.enchant_item.title": "T<PERSON>llkall", "advancements.story.enter_the_end.description": "Klaiv in i Endportaln", "advancements.story.enter_the_end.title": "Sluteð?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON>, fest i og far inn i ien Neðerportal", "advancements.story.enter_the_nether.title": "<PERSON><PERSON><PERSON> luv får<PERSON> diuoper", "advancements.story.follow_ender_eye.description": "<PERSON>y <PERSON> iett enderoga", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.form_obsidian.description": "<PERSON>jǫ til fǫ i ett obsidianblokk", "advancements.story.form_obsidian.title": "Aiskrukautmaningg", "advancements.story.iron_tools.description": "Uppgraðir akköksn den", "advancements.story.iron_tools.title": "Ir eð it įe iennakk", "advancements.story.lava_bucket.description": "Fyll upp įe kruk min lava", "advancements.story.lava_bucket.title": "Warmgr<PERSON><PERSON>", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "<PERSON>amanter!", "advancements.story.mine_stone.description": "B<PERSON>uot stien minn nykköksem den", "advancements.story.mine_stone.title": "Stįeåldern", "advancements.story.obtain_armor.description": "Fridå ðig minn ienn<PERSON>", "advancements.story.obtain_armor.title": "Sett ą̊ ðig klautą", "advancements.story.root.description": "Järtą og andlingg i spilę", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamantrustninngg redder laiv", "advancements.story.shiny_gear.title": "<PERSON><PERSON><PERSON> yvyr mig min diamantum", "advancements.story.smelt_iron.description": "<PERSON><PERSON>t nið ien ien<PERSON>k", "advancements.story.smelt_iron.title": "Skaff dig gräjur", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON><PERSON> ien wildera akköks", "advancements.story.upgrade_tools.title": "<PERSON>ǫ uppgraðiringg", "advancements.toast.challenge": "Uppdraeð ir autfyört!", "advancements.toast.goal": "Du ar kumið að målę!", "advancements.toast.task": "Eð går sakt framter!", "argument.anchor.invalid": "%s ir ien <PERSON> ankerposisiuon fer ieniet", "argument.angle.incomplete": "Ųogar (<PERSON> fǫr luv <PERSON> 1 winkel)", "argument.angle.invalid": "Ųoðjiltug winkel", "argument.block.id.invalid": "Ųokunnug blokktyp '%s'", "argument.block.property.duplicate": "Iegenskapn '%s' går bar bistemm iessn fer blokk %s", "argument.block.property.invalid": "Blokk %s går it minn ǫ '%s' fer iegenskapn %s", "argument.block.property.novalue": "Fewentað wärde för iegenskapn '%s' ą̊ blokk %s", "argument.block.property.unclosed": "Fewentað slutparantes ) for luv brukas fer blokkstatus", "argument.block.property.unknown": "Blotjeð %s ar int iegenskapn '%s'", "argument.block.tag.disallowed": "Etiketter får it brukes jän, bar riktug blokk", "argument.color.invalid": "Ųokunnug ferga '%s'", "argument.component.invalid": "Ųodjiltun tjattkomponent: %s", "argument.criteria.invalid": "Ųokunnut kriterium '%s'", "argument.dimension.invalid": "Ųokunnun dimensiuon '%s'", "argument.double.big": "Dubbeln dug it wår<PERSON> mier elld %s, fann att %s", "argument.double.low": "Dubbeln dug it wårå minner elld %s, %s itteðs ą̊", "argument.entity.invalid": "Ųodjiltut nammen elld UUID", "argument.entity.notfound.entity": "Fann it att indjin ieniet", "argument.entity.notfound.player": "Fann it att indjin spiler", "argument.entity.options.advancements.description": "Spilerer minn framstig", "argument.entity.options.distance.description": "A<PERSON><PERSON>d dait-að i<PERSON>m", "argument.entity.options.distance.negative": "Avstond dug int wårå negativt", "argument.entity.options.dx.description": "Ienieter millǫ x og x + dx", "argument.entity.options.dy.description": "Ienieter millǫ y og y + dy", "argument.entity.options.dz.description": "Ienieter millǫ z og z + dz", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON> min spilläge", "argument.entity.options.inapplicable": "<PERSON><PERSON> gǫr int bruk alternativę '%s' jän", "argument.entity.options.level.description": "Erfarenietsnivå", "argument.entity.options.level.negative": "<PERSON><PERSON><PERSON><PERSON> edd int ulað wårå negativ", "argument.entity.options.limit.description": "Maks antal ienieter fer te go etbaker", "argument.entity.options.limit.toosmall": "Grensn for luv wårå 1 elld mier", "argument.entity.options.mode.invalid": "Ųokunnut elld <PERSON>ǫd<PERSON>ltut spilläge '%s'", "argument.entity.options.name.description": "Ienietsnammen", "argument.entity.options.nbt.description": "Ienieter min NBT", "argument.entity.options.predicate.description": "Anpassað preðikat", "argument.entity.options.scores.description": "<PERSON><PERSON><PERSON> minn poäng", "argument.entity.options.sort.description": "<PERSON>rtir i<PERSON>", "argument.entity.options.sort.irreversible": "Ųokunnun elld <PERSON>un sortiringgstyp '%s'", "argument.entity.options.tag.description": "<PERSON><PERSON><PERSON> minn brikku", "argument.entity.options.team.description": "Ienieter å lae", "argument.entity.options.type.description": "Ienieter ov typ", "argument.entity.options.type.invalid": "Ųįodjiltun elld ųokunnun ienietstyp '%s'", "argument.entity.options.unknown": "Ųokunnut alternativ '%s'", "argument.entity.options.unterminated": "<PERSON><PERSON><PERSON><PERSON> slut ą̊ wal", "argument.entity.options.valueless": "Fewentað wärde fer alternativę '%s'", "argument.entity.options.x.description": "x possisiuon", "argument.entity.options.x_rotation.description": "Ienieteres x-rotasiuon", "argument.entity.options.y.description": "y possisiuon", "argument.entity.options.y_rotation.description": "Ienieteres y-rotasiuon", "argument.entity.options.z.description": "z possisiuon", "argument.entity.selector.allEntities": "<PERSON><PERSON> i<PERSON><PERSON>", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON>", "argument.entity.selector.missing": "Welertyp sakknes", "argument.entity.selector.nearestEntity": "Närmaste entitet", "argument.entity.selector.nearestPlayer": "Spilern so ir nemmest", "argument.entity.selector.not_allowed": "Weler for int brukas", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON> ukin spiler", "argument.entity.selector.self": "An ienietn so ir nų", "argument.entity.selector.unknown": "Ųokunnun welertyp '%s'", "argument.entity.toomany": "Bar ien ieniet ir luv<PERSON>, men an welern so ir and<PERSON><PERSON> guoðtar flierer", "argument.enum.invalid": "Ųodjiltut wärde \"%s\"", "argument.float.big": "Flyttaled får int wårå oger elld %s, itteð %s", "argument.float.low": "Flyttaleð får int wårå minner elld %s, itteð %s", "argument.gamemode.invalid": "Ųokunnut spilläge: %s", "argument.hexcolor.invalid": "\"%s\" är en ogiltig hexadecimal färgkod", "argument.id.invalid": "Ųodjiltun ID", "argument.id.unknown": "Uokunnut ID: %s", "argument.integer.big": "Ieltaleð får inte wårå mier elld %s, itteð %s", "argument.integer.low": "Ieltaleð får int wårå minner elld %s, itted %s", "argument.item.id.invalid": "Ųokunnut föremǫl '%s'", "argument.item.tag.disallowed": "<PERSON><PERSON><PERSON><PERSON> for int brukas jän, bar riktug gräjur", "argument.literal.incorrect": "Fewentað konstant %s", "argument.long.big": "Longg-wärdeð får int wårå oger elld %s, itteð %s", "argument.long.low": "Longg-wärdeð får int wårå minner elld %s, itteð %s", "argument.message.too_long": "Chattleddelandet var för långt (%s > maximalt %s tecken)", "argument.nbt.array.invalid": "Ųodjiltugn uppstellningstyp '%s'", "argument.nbt.array.mixed": "Går int ev ini %s i %s", "argument.nbt.expected.compound": "Sammansatt tagg förväntades", "argument.nbt.expected.key": "Wen<PERSON>ð nytjil", "argument.nbt.expected.value": "<PERSON><PERSON><PERSON> wärde", "argument.nbt.list.mixed": "Går int ev ini %s ini list ov %s", "argument.nbt.trailing": "Ųowentað data", "argument.player.entities": "Bar spilerer werd påwerkaðer ov ittað-jär kommando, men welern so brukes inkluð<PERSON>r ienieter", "argument.player.toomany": "Bar ien spiler ir luv<PERSON>, men an welern so brukes guoðtar flierer", "argument.player.unknown": "An-dar spilern ir it til", "argument.pos.missing.double": "<PERSON><PERSON> k<PERSON>", "argument.pos.missing.int": "<PERSON><PERSON> for luv wårå ien blokkposisuon", "argument.pos.mixed": "<PERSON><PERSON> går int bland wärdskoordinater og lokal koordinater (olltijuop får luv bruk iettdier ^ elld og inte)", "argument.pos.outofbounds": "An dar posisiuon ir autfer ramum so irå ok.", "argument.pos.outofworld": "An-dar posis<PERSON>on ir i ien eller wärd!", "argument.pos.unloaded": "An-dar posis<PERSON>ion ir int inlesin", "argument.pos2d.incomplete": "Ųogar (twer koordinater by<PERSON><PERSON>)", "argument.pos3d.incomplete": "Ųogar (trair koor<PERSON><PERSON> by<PERSON><PERSON>)", "argument.range.empty": "Ferwentað wärd elld intrevall ov wärde", "argument.range.ints": "Bar ieltal iro luvliger, int desimaler", "argument.range.swapped": "Minimum får int wårå minnera elld maksimum", "argument.resource.invalid_type": "Elementet \"%s\" har den felaktiga typen \"%s\" (\"%s\" förväntades)", "argument.resource.not_found": "Kan inte hitta elementet \"%s\" av typen \"%s\"", "argument.resource_or_id.failed_to_parse": "Misslyckades att tolka strukturen: %s", "argument.resource_or_id.invalid": "Ogiltigt id eller ogiltig tagg", "argument.resource_or_id.no_such_element": "Kan inte hitta elementet \"%s\" i registret \"%s\"", "argument.resource_selector.not_found": "Inga matchningar för väljaren \"%s\" av typen \"%s\"", "argument.resource_tag.invalid_type": "Taggen \"%s\" har den felaktiga typen \"%s\" (\"%s\" förväntades)", "argument.resource_tag.not_found": "Kan inte hitta taggen \"%s\" av typen \"%s\"", "argument.rotation.incomplete": "Ųofullstendut (an for luv bruk t<PERSON>o koor<PERSON>ter)", "argument.scoreHolder.empty": "<PERSON><PERSON> djikk int finn att ien brukbar poängollder", "argument.scoreboardDisplaySlot.invalid": "<PERSON><PERSON><PERSON><PERSON> waisningssspryndj: '%s'", "argument.style.invalid": "Ųodjiltug stil: %s", "argument.time.invalid_tick_count": "<PERSON>ik<PERSON>-antal for luv wårå ųo-negativ", "argument.time.invalid_unit": "Ųodjiltun ieniet", "argument.time.tick_count_too_low": "Antalet tickningar får inte vara färre än %s, %s hittades", "argument.uuid.invalid": "Ųdjiltug UUID", "argument.waypoint.invalid": "Den valda entiteten är inte en vägpunkt", "arguments.block.tag.unknown": "Uokunnug blokketikiett '%s'", "arguments.function.tag.unknown": "Ųokunnun fuksiuonsetikiett '%s'", "arguments.function.unknown": "Ųokunnun funksiuon %s", "arguments.item.component.expected": "Föremålskomponent förväntades", "arguments.item.component.malformed": "\"%2$s\" är en ogiltig komponent av typen \"%1$s\"", "arguments.item.component.repeated": "Föremålskomponenten \"%s\" upprepades, men endast en kan anges", "arguments.item.component.unknown": "\"%s\" är en okänd föremålskomponent", "arguments.item.malformed": "\"%s\" är ett ogiltigt föremål", "arguments.item.overstacked": "%s dug bar feskes ǫ upp að %s", "arguments.item.predicate.malformed": "\"%2$s\" är ett ogiltigt predikat av typen \"%1$s\"", "arguments.item.predicate.unknown": "\"%s\" är ett okänt föremålspredikat", "arguments.item.tag.unknown": "Ųokunnun föremǫlsettikiett '%s'", "arguments.nbtpath.node.invalid": "Ųodjiltut syökwegselement fer NBT", "arguments.nbtpath.nothing_found": "Fann it att inngu grejur so passer ijuop min %s", "arguments.nbtpath.too_deep": "Resulterande NBT är för djupt inbäddat", "arguments.nbtpath.too_large": "Resulterande NBT är för stort", "arguments.objective.notFound": "'%s' ir iett <PERSON>ǫkunnut föremǫl ą̊ poängtavlun", "arguments.objective.readonly": "Måleð '%s' ą̊ poängtavlun går bar leså", "arguments.operation.div0": "<PERSON><PERSON><PERSON> int diel min noll", "arguments.operation.invalid": "Ųodjiltun farningg", "arguments.swizzle.invalid": "Issn-jär akselkombinasiuon funggirer it, fewentad kombinasiuon ov 'x', 'y' and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "rustning", "attribute.name.armor_toughness": "rustningshårdhet", "attribute.name.attack_damage": "attackskada", "attribute.name.attack_knockback": "<PERSON>k<PERSON>ff", "attribute.name.attack_speed": "attack<PERSON><PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "blockbrytningshastighet", "attribute.name.block_interaction_range": "räckvidd för blockinteraktion", "attribute.name.burning_time": "brinn<PERSON>d", "attribute.name.camera_distance": "kameraavstånd", "attribute.name.entity_interaction_range": "räckvidd för entitetsinteraktion", "attribute.name.explosion_knockback_resistance": "knuff<PERSON><PERSON><PERSON> för explosioner", "attribute.name.fall_damage_multiplier": "multiplikator fö<PERSON>", "attribute.name.flying_speed": "<PERSON>gh<PERSON><PERSON><PERSON>", "attribute.name.follow_range": "förföljelseräckvidd för monster", "attribute.name.generic.armor": "Rustningg", "attribute.name.generic.armor_toughness": "Rustninggsstyrka", "attribute.name.generic.attack_damage": "Atakkskåðå", "attribute.name.generic.attack_knockback": "Rek <PERSON> fer te attakira", "attribute.name.generic.attack_speed": "Atakkfart", "attribute.name.generic.block_interaction_range": "räckvidd för blockinteraktion", "attribute.name.generic.burning_time": "brinn<PERSON>d", "attribute.name.generic.entity_interaction_range": "räckvidd för entitetsinteraktion", "attribute.name.generic.explosion_knockback_resistance": "knuff<PERSON><PERSON><PERSON> för explosioner", "attribute.name.generic.fall_damage_multiplier": "multiplikator fö<PERSON>", "attribute.name.generic.flying_speed": "Fliuogastigiet", "attribute.name.generic.follow_range": "<PERSON>r langgt fugater dug fy etter", "attribute.name.generic.gravity": "Gravitasiuon", "attribute.name.generic.jump_strength": "Krapt te uppa", "attribute.name.generic.knockback_resistance": "Muotstånd muot tilbukkningg", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON><PERSON>gni<PERSON>", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "rörelseeffektivitet", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "syrebonus", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON>ker öjd te folla", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Öjdn ą̊ trampum", "attribute.name.generic.water_movement_efficiency": "rörelseeffektivitet i vatten", "attribute.name.gravity": "gravitation", "attribute.name.horse.jump_strength": "Styrk fer estuppningg", "attribute.name.jump_strength": "hoppstyrka", "attribute.name.knockback_resistance": "knuffresistens", "attribute.name.luck": "tur", "attribute.name.max_absorption": "maxabsorbering", "attribute.name.max_health": "maxh<PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "gräveffektivitet", "attribute.name.movement_efficiency": "rörelseeffektivitet", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "syrebonus", "attribute.name.player.block_break_speed": "Ur strai´tt an bryot blokk", "attribute.name.player.block_interaction_range": "räckvidd för blockinteraktion", "attribute.name.player.entity_interaction_range": "räckvidd för entitetsinteraktion", "attribute.name.player.mining_efficiency": "gräveffektivitet", "attribute.name.player.sneaking_speed": "s<PERSON>gh<PERSON><PERSON><PERSON>", "attribute.name.player.submerged_mining_speed": "grävhastighet under vatten", "attribute.name.player.sweeping_damage_ratio": "förhållande till svepande skada", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.scale": "skala", "attribute.name.sneaking_speed": "s<PERSON>gh<PERSON><PERSON><PERSON>", "attribute.name.spawn_reinforcements": "zombieförstärkningar", "attribute.name.step_height": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.submerged_mining_speed": "grävhastighet under vatten", "attribute.name.sweeping_damage_ratio": "förhållande till svepande skada", "attribute.name.tempt_range": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fö<PERSON>", "attribute.name.water_movement_efficiency": "rörelseeffektivitet i vatten", "attribute.name.waypoint_receive_range": "detektionsavstånd för vägpunkt", "attribute.name.waypoint_transmit_range": "transmissionsavstånd för vägpunkt", "attribute.name.zombie.spawn_reinforcements": "Zombiförstärkningger", "biome.minecraft.badlands": "Stįeöken", "biome.minecraft.bamboo_jungle": "Bambujunggel", "biome.minecraft.basalt_deltas": "Basalt-delta", "biome.minecraft.beach": "Strand", "biome.minecraft.birch_forest": "Byörkskuog", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "Kollt av", "biome.minecraft.crimson_forest": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "Mörkskuog", "biome.minecraft.deep_cold_ocean": "Diuopt kollt av", "biome.minecraft.deep_dark": "Diuopmörknę", "biome.minecraft.deep_frozen_ocean": "Djuopt työloð av", "biome.minecraft.deep_lukewarm_ocean": "Djuopt lytt av", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON> o<PERSON>n", "biome.minecraft.desert": "Öken", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_barrens": "Autkantn ov End-ä", "biome.minecraft.end_highlands": "End-äð", "biome.minecraft.end_midlands": "Slut ą̊ Midlands", "biome.minecraft.eroded_badlands": "Eroðirað stįeöken", "biome.minecraft.flower_forest": "Bliuomskuog", "biome.minecraft.forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "Työlåð öve", "biome.minecraft.grove": "Skuogstapp", "biome.minecraft.ice_spikes": "Aisspaiker", "biome.minecraft.jagged_peaks": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "Dşunggel", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON> so ir lytt", "biome.minecraft.lush_caves": "Angripner grottur", "biome.minecraft.mangrove_swamp": "Mangrovemåyr", "biome.minecraft.meadow": "<PERSON><PERSON> ain<PERSON>je", "biome.minecraft.mushroom_fields": "Sopplot", "biome.minecraft.nether_wastes": "Netherödemark", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Gamtbjörkskuog", "biome.minecraft.old_growth_pine_taiga": "Gamt-tollskuog", "biome.minecraft.old_growth_spruce_taiga": "Gamtgrą̊skuog", "biome.minecraft.pale_garden": "Blek trädgård", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Ö<PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "Små End-<PERSON><PERSON>", "biome.minecraft.snowy_beach": "Strand min sniuom", "biome.minecraft.snowy_plains": "Snioųg slietur", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "biome.minecraft.snowy_taiga": "Sniuofelld taiga", "biome.minecraft.soul_sand_valley": "Själsandsdal", "biome.minecraft.sparse_jungle": "<PERSON><PERSON> jung<PERSON>", "biome.minecraft.stony_peaks": "Stienug bjärrstup<PERSON>", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON> strand", "biome.minecraft.sunflower_plains": "Suolruosslietur", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON>lut<PERSON><PERSON>", "biome.minecraft.the_void": "Snǫðligietn", "biome.minecraft.warm_ocean": "Warm osean", "biome.minecraft.warped_forest": "Fervrängd skuog", "biome.minecraft.windswept_forest": "Windpainað skuog", "biome.minecraft.windswept_gravelly_hills": "Wind<PERSON><PERSON> grusuoler", "biome.minecraft.windswept_hills": "Wind<PERSON><PERSON><PERSON> u<PERSON>r", "biome.minecraft.windswept_savanna": "Windpainað savann", "biome.minecraft.wooded_badlands": "Stięöken min trainnum", "block.minecraft.acacia_button": "Akasiaknapp", "block.minecraft.acacia_door": "Acasiadörer", "block.minecraft.acacia_fence": "Akasiastakiet", "block.minecraft.acacia_fence_gate": "Grind i akasiastakiet", "block.minecraft.acacia_hanging_sign": "Hängande akaciaskylt", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_planks": "Akasiaplau<PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "Akasiapressplatt", "block.minecraft.acacia_sapling": "Acasiaple<PERSON><PERSON>", "block.minecraft.acacia_sign": "Akasiastjylt", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Akasiatrampur", "block.minecraft.acacia_trapdoor": "Akasiafollukku", "block.minecraft.acacia_wall_hanging_sign": "Hängande akaciaskylt på vägg", "block.minecraft.acacia_wall_sign": "Akasiaweggstjylt", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON>wi<PERSON><PERSON>", "block.minecraft.activator_rail": "<PERSON><PERSON> min aktivator", "block.minecraft.air": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.allium": "Graslok", "block.minecraft.amethyst_block": "Ametistblokk", "block.minecraft.amethyst_cluster": "Ametistuop", "block.minecraft.ancient_debris": "Ljuotgåmålt rappel", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Andesitstjiv", "block.minecraft.andesite_stairs": "Andesittrampur", "block.minecraft.andesite_wall": "Andesitwegg", "block.minecraft.anvil": "Smiðsteð", "block.minecraft.attached_melon_stem": "Ą̊sett me<PERSON><PERSON><PERSON>k", "block.minecraft.attached_pumpkin_stem": "Påklistrað pumpstiuok", "block.minecraft.azalea": "Asalea", "block.minecraft.azalea_leaves": "Asalealover", "block.minecraft.azure_bluet": "Poslinsbliuomme", "block.minecraft.bamboo": "Bambu", "block.minecraft.bamboo_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_button": "Bambuknapp", "block.minecraft.bamboo_door": "Ba<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_fence": "Bambustaket", "block.minecraft.bamboo_fence_gate": "Bambugrind", "block.minecraft.bamboo_hanging_sign": "Hängande bambuskylt", "block.minecraft.bamboo_mosaic": "Bambumosaik", "block.minecraft.bamboo_mosaic_slab": "Bambumosaikplatta", "block.minecraft.bamboo_mosaic_stairs": "Bambumosaik<PERSON>ppa", "block.minecraft.bamboo_planks": "Bambuplankor", "block.minecraft.bamboo_pressure_plate": "Bambutryckplatta", "block.minecraft.bamboo_sapling": "Bambunylaðir", "block.minecraft.bamboo_sign": "Bambuskylt", "block.minecraft.bamboo_slab": "Bambuplatta", "block.minecraft.bamboo_stairs": "Bamb<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_trapdoor": "Bambufallucka", "block.minecraft.bamboo_wall_hanging_sign": "Hängande bambuskylt på vägg", "block.minecraft.bamboo_wall_sign": "Bambuskylt på vägg", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.base.blue": "Ielblått umråð", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> områð", "block.minecraft.banner.base.cyan": "Ielsyanfergað områd", "block.minecraft.banner.base.gray": "Ielgr<PERSON><PERSON><PERSON> um<PERSON>", "block.minecraft.banner.base.green": "<PERSON>elg<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON>t liuosdblå<PERSON>tt umdråð", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON> l<PERSON>g<PERSON><PERSON>tt områð", "block.minecraft.banner.base.lime": "<PERSON><PERSON><PERSON> laimfergað umråð", "block.minecraft.banner.base.magenta": "<PERSON><PERSON>t magentafergað umråð", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON> guo<PERSON><PERSON> umr<PERSON>", "block.minecraft.banner.base.pink": "Ielstj<PERSON>rt umråð", "block.minecraft.banner.base.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> um<PERSON>", "block.minecraft.banner.base.yellow": "Ielguolt områð", "block.minecraft.banner.border.black": "Swart inrazmningg", "block.minecraft.banner.border.blue": "Blå inramningg", "block.minecraft.banner.border.brown": "<PERSON><PERSON>", "block.minecraft.banner.border.cyan": "Syanfergað inramningg", "block.minecraft.banner.border.gray": "Grå inramningg", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "Ljuosblå inramningg", "block.minecraft.banner.border.light_gray": "Liuosgrå inramningg", "block.minecraft.banner.border.lime": "Laimgryönfergað inramningg", "block.minecraft.banner.border.magenta": "Magenta in<PERSON>g", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "Wait inramningg", "block.minecraft.banner.border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON>tt tigelmömster", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.cyan": "Syanfergað tigelmömster", "block.minecraft.banner.bricks.gray": "Grå tigelmömster", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "Ljuosblått tigelmömster", "block.minecraft.banner.bricks.light_gray": "Ljuosgrå tigelmömster", "block.minecraft.banner.bricks.lime": "Laimfergað tigelmömster", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON> tige<PERSON>", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON><PERSON> tigelmömster", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON><PERSON> tigelmömster", "block.minecraft.banner.bricks.purple": "<PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON> tigelmömster", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON>", "block.minecraft.banner.circle.blue": "Blå krindjel", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Syanfergað krindjel", "block.minecraft.banner.circle.gray": "Grå krindjel", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Ljuosblå krindjel", "block.minecraft.banner.circle.light_gray": "Ljuosgrå krindjel", "block.minecraft.banner.circle.lime": "Laimgryönfergað krindjel", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>d<PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON> krind<PERSON>l", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Swart creeper-leðning", "block.minecraft.banner.creeper.blue": "Blå creeper-leðningg", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON> creeper-leð<PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Cyanfergað creeper-leðningg", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON> creeper-leðningg", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON> creeper-leð<PERSON>g", "block.minecraft.banner.creeper.light_blue": "Ljuosblå creeper-leðningg", "block.minecraft.banner.creeper.light_gray": "Ljuosgrå creeper-leðningg", "block.minecraft.banner.creeper.lime": "Laimgryönfergað creeper-leðningg", "block.minecraft.banner.creeper.magenta": "Ma<PERSON>a creeper-leð<PERSON>g", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON><PERSON> creeper-leð<PERSON>g", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON><PERSON> creeper-leð<PERSON>g", "block.minecraft.banner.creeper.purple": "<PERSON> creeper-leð<PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON> creeper-leð<PERSON>g", "block.minecraft.banner.creeper.white": "Wait creeper-leðningg", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON> creeper-leð<PERSON><PERSON>", "block.minecraft.banner.cross.black": "Swartkross", "block.minecraft.banner.cross.blue": "Blåkross", "block.minecraft.banner.cross.brown": "Brunkross", "block.minecraft.banner.cross.cyan": "Syanfergaðkross", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.cross.green": "Gryönkross", "block.minecraft.banner.cross.light_blue": "Ljusblått kross", "block.minecraft.banner.cross.light_gray": "Ljuosgr<PERSON><PERSON> kross", "block.minecraft.banner.cross.lime": "Laimfergaðkross", "block.minecraft.banner.cross.magenta": "Magentakross", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON><PERSON> kross", "block.minecraft.banner.cross.pink": "<PERSON>jt<PERSON>rt kross", "block.minecraft.banner.cross.purple": "Lilakross", "block.minecraft.banner.cross.red": "Roðkross", "block.minecraft.banner.cross.white": "Waitkross", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> kross", "block.minecraft.banner.curly_border.black": "Swart taggun inramningg", "block.minecraft.banner.curly_border.blue": "Blå taggun inramningg", "block.minecraft.banner.curly_border.brown": "<PERSON>run taggun inramningg", "block.minecraft.banner.curly_border.cyan": "Syanfergað taggun inramningg", "block.minecraft.banner.curly_border.gray": "Grå taggun inramningg", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON> taggun inramningg", "block.minecraft.banner.curly_border.light_blue": "Ljuosblå taggun inramningg", "block.minecraft.banner.curly_border.light_gray": "Ljuosgrå taggun inramningg", "block.minecraft.banner.curly_border.lime": "Laimgryönfergað taggun inramningg", "block.minecraft.banner.curly_border.magenta": "Magenta taggun inramningg", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON><PERSON> taggun inramningg", "block.minecraft.banner.curly_border.pink": "St<PERSON><PERSON><PERSON> taggun inramningg", "block.minecraft.banner.curly_border.purple": "<PERSON> taggun inramningg", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> taggun inramningg", "block.minecraft.banner.curly_border.white": "Wait taggun inramningg", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> taggun inramningg", "block.minecraft.banner.diagonal_left.black": "Swart avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.blue": "Blått avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON>t avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.cyan": "Syanfergað avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.gray": "Grått avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.green": "G<PERSON>önt avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.light_blue": "Ljuosblått avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.light_gray": "Ljuosgrå avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.lime": "Laimgryönfergað avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.magenta": "Magenta avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.orange": "Guolrott avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.pink": "Stj<PERSON>rt avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.purple": "Lila avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.red": "Ro'tt avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.white": "Waitt avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_left.yellow": "Guolt avut kroppstrik uvoni að wįster", "block.minecraft.banner.diagonal_right.black": "Swart trianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.blue": "Blåtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON>gel uvoni að yöger", "block.minecraft.banner.diagonal_right.cyan": "Syanfergadtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.gray": "Gråtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.diagonal_right.light_blue": "Liuosblåtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.light_gray": "Ljuosgrå trianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.lime": "Laimfergaðtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.magenta": "Magentatrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.orange": "Guolroðtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.diagonal_right.purple": "Lilatrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.red": "R<PERSON>ðtrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.white": "Waittrianggel uvoni að yöger", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "Blå underåva", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "Syanfergað underåva", "block.minecraft.banner.diagonal_up_left.gray": "Grå underåva", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Liuosblå underåva", "block.minecraft.banner.diagonal_up_left.light_gray": "Liuosgrå underåva", "block.minecraft.banner.diagonal_up_left.lime": "Laimgryönfergað underåva", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON>", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.white": "<PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.blue": "Blå underåva", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "Syanfergad underåva", "block.minecraft.banner.diagonal_up_right.gray": "Grå underåva", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.light_blue": "Ljuosblå underåva", "block.minecraft.banner.diagonal_up_right.light_gray": "Ljuosgrå underåva", "block.minecraft.banner.diagonal_up_right.lime": "Laimgryönfergad underåva", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON>", "block.minecraft.banner.diagonal_up_right.red": "<PERSON>", "block.minecraft.banner.diagonal_up_right.white": "<PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> virvel", "block.minecraft.banner.flow.blue": "Blå virvel", "block.minecraft.banner.flow.brown": "<PERSON><PERSON> virvel", "block.minecraft.banner.flow.cyan": "<PERSON>rk<PERSON> virvel", "block.minecraft.banner.flow.gray": "Gr<PERSON> virvel", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flow.light_blue": "Ljusblå virvel", "block.minecraft.banner.flow.light_gray": "Ljusgrå virvel", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> virvel", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.orange": "Orange virvel", "block.minecraft.banner.flow.pink": "<PERSON> virvel", "block.minecraft.banner.flow.purple": "<PERSON> virvel", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "Vit virvel", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> virvel", "block.minecraft.banner.flower.black": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.blue": "Blå bljuomme", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "Syanfergað bljuomme", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON> bljuomme", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "Ljuosblå bljomme", "block.minecraft.banner.flower.light_gray": "Liuosgrå bljuomme", "block.minecraft.banner.flower.lime": "Laimgryönfergað bljomme", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON> bl<PERSON><PERSON>", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON><PERSON> bl<PERSON><PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.flower.purple": "<PERSON> b<PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.white": "<PERSON> bljomme", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "<PERSON><PERSON>", "block.minecraft.banner.globe.blue": "Blå krindjel", "block.minecraft.banner.globe.brown": "<PERSON><PERSON>", "block.minecraft.banner.globe.cyan": "Syanfergað krindjel", "block.minecraft.banner.globe.gray": "Grå krindjel", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.light_blue": "Ljuosblå krindjel", "block.minecraft.banner.globe.light_gray": "Liuosgrå krindjel", "block.minecraft.banner.globe.lime": "Laimfergað krindjel", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.orange": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>d<PERSON>", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.purple": "<PERSON>", "block.minecraft.banner.globe.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.white": "<PERSON> krind<PERSON>l", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON>", "block.minecraft.banner.gradient.black": "<PERSON>wart y<PERSON><PERSON> uvoter", "block.minecraft.banner.gradient.blue": "Blå yvyrgangg uvoter", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON> y<PERSON><PERSON> uvoter", "block.minecraft.banner.gradient.cyan": "Syanfergad yvyrgangg uvoter", "block.minecraft.banner.gradient.gray": "Grå yvy<PERSON>gg uvoter", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>", "block.minecraft.banner.gradient.light_blue": "Ljuosblå yvy<PERSON>gg uv<PERSON>ter", "block.minecraft.banner.gradient.light_gray": "Ljuosgrå yvyrgangg uvoter", "block.minecraft.banner.gradient.lime": "Laimgryönfergað y<PERSON><PERSON><PERSON> uvǫter", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON> y<PERSON> uv<PERSON>", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON> uv<PERSON>ter", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON> y<PERSON> uvoter", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON> y<PERSON><PERSON> uvoter", "block.minecraft.banner.gradient.white": "<PERSON> y<PERSON><PERSON><PERSON> uvǫter", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON> uv<PERSON>", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON> y<PERSON> ni<PERSON>", "block.minecraft.banner.gradient_up.blue": "Blå yvy<PERSON>gg niðǫ<PERSON>", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON> y<PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.cyan": "Syanfergað y<PERSON>gg niðǫter", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON> y<PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.light_blue": "Liuosblå yvy<PERSON>gg nið<PERSON>", "block.minecraft.banner.gradient_up.light_gray": "Liuosgrå y<PERSON><PERSON>gg nið<PERSON>", "block.minecraft.banner.gradient_up.lime": "Laimgryönfergad y<PERSON>gg niðǫter", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON> y<PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.purple": "<PERSON> y<PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.gradient_up.white": "<PERSON> y<PERSON><PERSON>gg nið<PERSON>ter", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.guster.black": "Svart vindväsen", "block.minecraft.banner.guster.blue": "Blått vindväsen", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.guster.cyan": "Turkost vindväsen", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON><PERSON> vindväsen", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "Ljusblått vindväsen", "block.minecraft.banner.guster.light_gray": "Ljusgrått vindväsen", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Orange vindväsen", "block.minecraft.banner.guster.pink": "<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON> vind<PERSON>sen", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.blue": "Blå uvoniåva", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "Syanfergað uvoniåva", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Ljuosblå uvoniåva", "block.minecraft.banner.half_horizontal.light_gray": "Liuosgr<PERSON> uvoniå<PERSON>", "block.minecraft.banner.half_horizontal.lime": "Laimgryönfergað uvoniåva", "block.minecraft.banner.half_horizontal.magenta": "Magentafergað uvoniåva", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "Laimfergað uvoniåva", "block.minecraft.banner.half_horizontal.purple": "<PERSON>", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "<PERSON>", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "Blå underåva", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "Syanfergað underåva", "block.minecraft.banner.half_horizontal_bottom.gray": "Grå underåva", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Ljuosblå underåva", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Ljuosgrå underåva", "block.minecraft.banner.half_horizontal_bottom.lime": "Laimfergað underåva", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magentafergað underåva", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.black": "Swart wįsteråva", "block.minecraft.banner.half_vertical.blue": "Blå wįsteråva", "block.minecraft.banner.half_vertical.brown": "B<PERSON> wįsteråva", "block.minecraft.banner.half_vertical.cyan": "Syanfergað wįsteråva", "block.minecraft.banner.half_vertical.gray": "Grå wįsteråva", "block.minecraft.banner.half_vertical.green": "Gryön wįsteråva", "block.minecraft.banner.half_vertical.light_blue": "Ljuosblå wiįsteråva", "block.minecraft.banner.half_vertical.light_gray": "Ljuosgrå wįsteråva", "block.minecraft.banner.half_vertical.lime": "Laimgryönfergað wiįsteråva", "block.minecraft.banner.half_vertical.magenta": "Magenta wiįsteråva", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON><PERSON> wiįsteråva", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON><PERSON> wįsteråva", "block.minecraft.banner.half_vertical.purple": "<PERSON> wįsteråva", "block.minecraft.banner.half_vertical.red": "Roð wįsteråva", "block.minecraft.banner.half_vertical.white": "Wait wiįsteråva", "block.minecraft.banner.half_vertical.yellow": "<PERSON>l wiįsteråva", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.blue": "Blå yögeråva", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Syanfeergað yögeråva", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON> y<PERSON>", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Ljuosblå yögeråva", "block.minecraft.banner.half_vertical_right.light_gray": "Ljuosgrå yögeråva", "block.minecraft.banner.half_vertical_right.lime": "Laimgryönfergað yögeråva", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON> moje<PERSON>g", "block.minecraft.banner.mojang.blue": "Blå pisyl", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON> pisyl", "block.minecraft.banner.mojang.cyan": "Syanfergað pisyl", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON> mojengg", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON> mo<PERSON>", "block.minecraft.banner.mojang.light_blue": "Liuosblå mojengg", "block.minecraft.banner.mojang.light_gray": "Liuosgrå mojengg", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> moje<PERSON>", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON> moje<PERSON>", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON><PERSON> moje<PERSON>g", "block.minecraft.banner.mojang.pink": "<PERSON> mo<PERSON>", "block.minecraft.banner.mojang.purple": "Lila pisyl", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON> mojengg", "block.minecraft.banner.mojang.white": "<PERSON> mojengg", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> mo<PERSON>", "block.minecraft.banner.piglin.black": "Swartsnaut", "block.minecraft.banner.piglin.blue": "Blåsnaut", "block.minecraft.banner.piglin.brown": "Brunsnaut", "block.minecraft.banner.piglin.cyan": "Syanfergadsnaut", "block.minecraft.banner.piglin.gray": "Gråsnaut", "block.minecraft.banner.piglin.green": "Gryönsnaut", "block.minecraft.banner.piglin.light_blue": "Liuosblåsnaut", "block.minecraft.banner.piglin.light_gray": "Ljuousgråsnaut", "block.minecraft.banner.piglin.lime": "Laimgryönsnaut", "block.minecraft.banner.piglin.magenta": "Magentasnaut", "block.minecraft.banner.piglin.orange": "Guo<PERSON>roðsnaut", "block.minecraft.banner.piglin.pink": "Stjärsnaut", "block.minecraft.banner.piglin.purple": "Lilasnaut", "block.minecraft.banner.piglin.red": "R<PERSON>ð<PERSON>naut", "block.minecraft.banner.piglin.white": "Waitsnaut", "block.minecraft.banner.piglin.yellow": "Guolsnaut", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON> diamant", "block.minecraft.banner.rhombus.blue": "Blå diamant", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "Syanfergað diamant", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> diamant", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "Ljuosblå diamant", "block.minecraft.banner.rhombus.light_gray": "Ljuosgrå diamant", "block.minecraft.banner.rhombus.lime": "Laimgryönfergað diamant", "block.minecraft.banner.rhombus.magenta": "Magentafergað diamant", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON> diamant", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON>", "block.minecraft.banner.skull.black": "<PERSON><PERSON>", "block.minecraft.banner.skull.blue": "Blå doðskoll", "block.minecraft.banner.skull.brown": "<PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "Syanfergað doðskoll", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON> doðskoll", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "Liuosblå doðskoll", "block.minecraft.banner.skull.light_gray": "Liusgrå doðskoll", "block.minecraft.banner.skull.lime": "Laimgryönfergað doðskoll", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.purple": "<PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON> do<PERSON><PERSON>ll", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "Blårandun", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "Cyanfergaðrandun", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.green": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_blue": "Ljuosblårandun", "block.minecraft.banner.small_stripes.light_gray": "Ljuosgrårandun", "block.minecraft.banner.small_stripes.lime": "Laimgryönrandun", "block.minecraft.banner.small_stripes.magenta": "Magent<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.pink": "St<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.black": "Swart nið<PERSON>kant að wįster", "block.minecraft.banner.square_bottom_left.blue": "Blå niðerkant að wįster", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON> niðerkant að wįster", "block.minecraft.banner.square_bottom_left.cyan": "Cyanfergað niderkant að wįster", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> niðerkant að wįster", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON>n niderkant ad wįster", "block.minecraft.banner.square_bottom_left.light_blue": "Ljuosblå niðerkant að wįster", "block.minecraft.banner.square_bottom_left.light_gray": "Ljuosgrå niðerkant að wįster", "block.minecraft.banner.square_bottom_left.lime": "Laimfergað niðerkant að wįster", "block.minecraft.banner.square_bottom_left.magenta": "Magentafergað niðerkant að wįster", "block.minecraft.banner.square_bottom_left.orange": "<PERSON><PERSON><PERSON> ni<PERSON> að wįster", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>kant að wįster", "block.minecraft.banner.square_bottom_left.purple": "<PERSON> nið<PERSON>kant að wįster", "block.minecraft.banner.square_bottom_left.red": "<PERSON>oð niðerkant að wįster", "block.minecraft.banner.square_bottom_left.white": "Wait niðerkant að wįster", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON> nið<PERSON>kant að wįster", "block.minecraft.banner.square_bottom_right.black": "Swart nidoni að yöger", "block.minecraft.banner.square_bottom_right.blue": "Blå niðoni að yöger", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON> nið<PERSON> að yöger", "block.minecraft.banner.square_bottom_right.cyan": "Syanfergað niðoni að yöger", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON> niðoni að yöger", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON> niðoni að yöger", "block.minecraft.banner.square_bottom_right.light_blue": "Ljuiosblå niðoni að yöger", "block.minecraft.banner.square_bottom_right.light_gray": "Ljuosgrå niðoni að yöger", "block.minecraft.banner.square_bottom_right.lime": "Laimgryönfergað niðoni að yöger", "block.minecraft.banner.square_bottom_right.magenta": "Magentafergað niðoni að yöger", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON><PERSON> ni<PERSON> að yöger", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON> að yöger", "block.minecraft.banner.square_bottom_right.purple": "<PERSON> niðoni að yöger", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON>ð niðoni að yöger", "block.minecraft.banner.square_bottom_right.white": "Wait niðoni að yöger", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON> nið<PERSON> að yöger", "block.minecraft.banner.square_top_left.black": "Swart yvyrkant að wįster", "block.minecraft.banner.square_top_left.blue": "Blå yvyrkant að wįster", "block.minecraft.banner.square_top_left.brown": "<PERSON>run yvyrkant að wįster", "block.minecraft.banner.square_top_left.cyan": "Syanfergað yvyrkant að wįster", "block.minecraft.banner.square_top_left.gray": "Grå yvyrkant að wįster", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON>n yvyrkant að wįster", "block.minecraft.banner.square_top_left.light_blue": "Ljuosblå yvyrkant að wįster", "block.minecraft.banner.square_top_left.light_gray": "Ljuosgrå yvyrkant að wįster", "block.minecraft.banner.square_top_left.lime": "Laimgryönfergað yvyrkant að wįster", "block.minecraft.banner.square_top_left.magenta": "Magentafergad yvyrkant að wįster", "block.minecraft.banner.square_top_left.orange": "Guo<PERSON><PERSON>ð yvyrkant að wįster", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON><PERSON><PERSON> yvyrkant að wįster", "block.minecraft.banner.square_top_left.purple": "Lila yvyrkant að wįster", "block.minecraft.banner.square_top_left.red": "Roð yvyrkant að wįster", "block.minecraft.banner.square_top_left.white": "Wait yvyrkant að wįster", "block.minecraft.banner.square_top_left.yellow": "<PERSON>l yvyrkant að wįster", "block.minecraft.banner.square_top_right.black": "Swart uvoni að yöger", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON>å uvoni að yöger", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.cyan": "Syanfergað uvoni að yöger", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.light_blue": "Ljuosblå uvoni að yöger", "block.minecraft.banner.square_top_right.light_gray": "Ljuosgrå uvoni að yöger", "block.minecraft.banner.square_top_right.lime": "Laimgryönfergað uvoni að yöger", "block.minecraft.banner.square_top_right.magenta": "Ma<PERSON>a uvoni að yöger", "block.minecraft.banner.square_top_right.orange": "<PERSON><PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.purple": "<PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.white": "<PERSON> uvoni að yöger", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON> uvoni að yöger", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.blue": "Blått kross", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Cyanfergað kross", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.light_blue": "Liuosbl<PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.light_gray": "Ljuosgrå'tt kross", "block.minecraft.banner.straight_cross.lime": "Laimgryönfergað kross", "block.minecraft.banner.straight_cross.magenta": "Magenta kross", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.purple": "<PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON> kross", "block.minecraft.banner.straight_cross.white": "<PERSON> kross", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON> kross", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "Blå niðerkant", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Syanfer<PERSON>d ni<PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON> niðerkant", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Ljuosblå niðerkant", "block.minecraft.banner.stripe_bottom.light_gray": "Ljuosgrå niðerkant", "block.minecraft.banner.stripe_bottom.lime": "Laimgryönfergað niðerkant", "block.minecraft.banner.stripe_bottom.magenta": "Magentafergad ni<PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON> nið<PERSON><PERSON>t", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "Swartstrik", "block.minecraft.banner.stripe_center.blue": "Blått strik", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON> strik", "block.minecraft.banner.stripe_center.cyan": "Cyanfergad strik", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON><PERSON> strik", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON> strik", "block.minecraft.banner.stripe_center.light_blue": "Ljuosblå strik", "block.minecraft.banner.stripe_center.light_gray": "Ljuosgr<PERSON>tt strik", "block.minecraft.banner.stripe_center.lime": "Laimgryönfergað strik", "block.minecraft.banner.stripe_center.magenta": "Magenta strik", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON> strik", "block.minecraft.banner.stripe_center.pink": "Stjätr strik", "block.minecraft.banner.stripe_center.purple": "<PERSON> strik", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "Wait strik", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> strik", "block.minecraft.banner.stripe_downleft.black": "Swart avut kroppstrik", "block.minecraft.banner.stripe_downleft.blue": "Blått avut kroppstrik", "block.minecraft.banner.stripe_downleft.brown": "Brunt avut kroppstrik", "block.minecraft.banner.stripe_downleft.cyan": "Syanfergað avut kroppstrik", "block.minecraft.banner.stripe_downleft.gray": "Grått avut kroppstrik", "block.minecraft.banner.stripe_downleft.green": "Gryönt avut kroppstrik", "block.minecraft.banner.stripe_downleft.light_blue": "Ljuosblått kroppstrik", "block.minecraft.banner.stripe_downleft.light_gray": "Ljuosgrått avut kroppstrik", "block.minecraft.banner.stripe_downleft.lime": "Laimgryönfergað avut kroppstrik", "block.minecraft.banner.stripe_downleft.magenta": "Magenta kroppstrik", "block.minecraft.banner.stripe_downleft.orange": "Guolroð kroppstrik", "block.minecraft.banner.stripe_downleft.pink": "Stjärt avut kroppstrik", "block.minecraft.banner.stripe_downleft.purple": "Lila avut kroppstrik", "block.minecraft.banner.stripe_downleft.red": "Rott avut kroppstrik", "block.minecraft.banner.stripe_downleft.white": "Wait kroppstrik", "block.minecraft.banner.stripe_downleft.yellow": "Guolt avut kroppstrik", "block.minecraft.banner.stripe_downright.black": "Swart kroppstrik", "block.minecraft.banner.stripe_downright.blue": "Blå'tt kroppsrtrik", "block.minecraft.banner.stripe_downright.brown": "Brunt kroppstrik", "block.minecraft.banner.stripe_downright.cyan": "Syanfgergad kroppstrik", "block.minecraft.banner.stripe_downright.gray": "Grått kroppstrik", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON>nt kroppstrik", "block.minecraft.banner.stripe_downright.light_blue": "Ljusblått kroppstrik", "block.minecraft.banner.stripe_downright.light_gray": "Ljuosgrå kroppstrik", "block.minecraft.banner.stripe_downright.lime": "Laimfergað kroppstrik", "block.minecraft.banner.stripe_downright.magenta": "Magenta kroppstrik", "block.minecraft.banner.stripe_downright.orange": "Guolrott kroppstrik", "block.minecraft.banner.stripe_downright.pink": "Stjärt kroppstrik", "block.minecraft.banner.stripe_downright.purple": "<PERSON>", "block.minecraft.banner.stripe_downright.red": "Rott kroppstrik", "block.minecraft.banner.stripe_downright.white": "Wait kroppstrik", "block.minecraft.banner.stripe_downright.yellow": "Guolt kroppstrik", "block.minecraft.banner.stripe_left.black": "Swart að wįster", "block.minecraft.banner.stripe_left.blue": "Blå að wįster", "block.minecraft.banner.stripe_left.brown": "<PERSON>run að wįster", "block.minecraft.banner.stripe_left.cyan": "Syanfergað að wįster", "block.minecraft.banner.stripe_left.gray": "Grå að wįster", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON><PERSON> að wįster", "block.minecraft.banner.stripe_left.light_blue": "Ljuosblå að wįster", "block.minecraft.banner.stripe_left.light_gray": "Ljuosgrå að wįster", "block.minecraft.banner.stripe_left.lime": "Laimgryönfergad að wįster", "block.minecraft.banner.stripe_left.magenta": "Magentafergað að wįster", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON><PERSON> að wįster", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON><PERSON> að wįster", "block.minecraft.banner.stripe_left.purple": "<PERSON> að wįster", "block.minecraft.banner.stripe_left.red": "Roð að wįster", "block.minecraft.banner.stripe_left.white": "Wait að wįster", "block.minecraft.banner.stripe_left.yellow": "<PERSON>l að wįster", "block.minecraft.banner.stripe_middle.black": "Swart liggend strik", "block.minecraft.banner.stripe_middle.blue": "Blått liggend strik", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON> liggend strik", "block.minecraft.banner.stripe_middle.cyan": "Cyanfergad liggend strik", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>tt liggend strik", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON> liggend strik", "block.minecraft.banner.stripe_middle.light_blue": "Ljuosblått liggend strik", "block.minecraft.banner.stripe_middle.light_gray": "Ljuosgrått liggend strik", "block.minecraft.banner.stripe_middle.lime": "Laimgrytönfergað liggend strik", "block.minecraft.banner.stripe_middle.magenta": "Magentafergað liggend strik", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON>rott liggend strik", "block.minecraft.banner.stripe_middle.pink": "Stjärtt liggend strik", "block.minecraft.banner.stripe_middle.purple": "<PERSON> liggend strik", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON> liggend strik", "block.minecraft.banner.stripe_middle.white": "Wai'tt liggend strik", "block.minecraft.banner.stripe_middle.yellow": "<PERSON>lt liggend strik", "block.minecraft.banner.stripe_right.black": "<PERSON>wart að yöger", "block.minecraft.banner.stripe_right.blue": "Blå að yöger", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_right.cyan": "Syanfergað að yöger", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_right.light_blue": "Ljuosblå að yöger", "block.minecraft.banner.stripe_right.light_gray": "Ljuosgrå að yöger", "block.minecraft.banner.stripe_right.lime": "Laimfergad að yöger", "block.minecraft.banner.stripe_right.magenta": "Magenta að yöger", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON><PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_right.purple": "<PERSON> að yöger", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_right.white": "Wait að yöger", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> að yöger", "block.minecraft.banner.stripe_top.black": "<PERSON>wart yvyrkant", "block.minecraft.banner.stripe_top.blue": "Blå yvyrkant", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "Syanfergað yvyrkant", "block.minecraft.banner.stripe_top.gray": "Grå yvyrkant", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Ljusblå yvyrkant", "block.minecraft.banner.stripe_top.light_gray": "Ljuosgrå yvyrkant", "block.minecraft.banner.stripe_top.lime": "Laimgryönfergað yvyrkant", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON> y<PERSON>nt", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> y<PERSON>nt", "block.minecraft.banner.stripe_top.white": "<PERSON> y<PERSON>rkant", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> niðoni", "block.minecraft.banner.triangle_bottom.blue": "Blåtrianggel niðoni", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Syanfergaðtrianggel niðoni", "block.minecraft.banner.triangle_bottom.gray": "Grå<PERSON><PERSON><PERSON> niðoni", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Ljuosblåtrianggel niðoni", "block.minecraft.banner.triangle_bottom.light_gray": "Ljuosgråtrianggel niðoni", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "Magentawinkel ni<PERSON>", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> niðoni", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.black": "Swart avun trianggel", "block.minecraft.banner.triangle_top.blue": "Blå avuntrianggel u<PERSON>i", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.cyan": "Syanfergað avutriangel uvoni", "block.minecraft.banner.triangle_top.gray": "Gr<PERSON> avunt<PERSON><PERSON><PERSON>i", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON>n avun trianggel uvoni", "block.minecraft.banner.triangle_top.light_blue": "Ljuosblåav<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.light_gray": "Ljuosgrå avuntrianggel u<PERSON>i", "block.minecraft.banner.triangle_top.lime": "Laimfergaðav<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.magenta": "Magentafergað avunt<PERSON><PERSON><PERSON> uvoni", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON><PERSON><PERSON> avu<PERSON><PERSON><PERSON><PERSON> u<PERSON>i", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.purple": "<PERSON> a<PERSON>i", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON> avun trianggel", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON>yanfer<PERSON>ð<PERSON><PERSON> niðoni", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Ljuos<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.light_gray": "Ljuos<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON>fer<PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> nið<PERSON>", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON> ni<PERSON>", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.cyan": "<PERSON>yafer<PERSON>ð<PERSON><PERSON> u<PERSON>", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON>os<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_gray": "L<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.barrel": "<PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Fyr", "block.minecraft.beacon.primary": "Fuostkraft", "block.minecraft.beacon.secondary": "Oðerkraftn", "block.minecraft.bed.no_sleep": "<PERSON> belelr bar såvå um not og dar eð å<PERSON>r", "block.minecraft.bed.not_safe": "Du beller it wail dig nų, monstrer iro jär noger", "block.minecraft.bed.obstructed": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir blokki<PERSON>ð", "block.minecraft.bed.occupied": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> ir upptaið", "block.minecraft.bed.too_far_away": "Du beller it wail dig nų, sandję ir uvlaunggt brott", "block.minecraft.bedrock": "Bjärggrund", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "Rindjklukk", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "Stur druplovsstiuok", "block.minecraft.birch_button": "Byörkknapp", "block.minecraft.birch_door": "Byörkdörer", "block.minecraft.birch_fence": "Byörkstakiet", "block.minecraft.birch_fence_gate": "Grind i byörkstakiet", "block.minecraft.birch_hanging_sign": "Hängande björkskylt", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Byörkkabb", "block.minecraft.birch_planks": "Byörkplaunkur", "block.minecraft.birch_pressure_plate": "Byörkpressplatt", "block.minecraft.birch_sapling": "Byörkplemta", "block.minecraft.birch_sign": "Byörkstjylt", "block.minecraft.birch_slab": "Byörkstjiv", "block.minecraft.birch_stairs": "Byörktrampur", "block.minecraft.birch_trapdoor": "Byörkfollukku", "block.minecraft.birch_wall_hanging_sign": "Hängande björkskylt på vägg", "block.minecraft.birch_wall_sign": "Byörkweggstjylt", "block.minecraft.birch_wood": "Byörkwiðn", "block.minecraft.black_banner": "Swart fana", "block.minecraft.black_bed": "<PERSON><PERSON> sa<PERSON>g", "block.minecraft.black_candle": "Swart <PERSON><PERSON>", "block.minecraft.black_candle_cake": "Tårt min swar´tt liuos", "block.minecraft.black_carpet": "<PERSON>wart matta", "block.minecraft.black_concrete": "<PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "Swartglasirað terrakotta", "block.minecraft.black_shulker_box": "<PERSON><PERSON>", "block.minecraft.black_stained_glass": "S<PERSON>fle<PERSON>ðg<PERSON>", "block.minecraft.black_stained_glass_pane": "Swartflekkaðglasrut", "block.minecraft.black_terracotta": "Swart terrakotta", "block.minecraft.black_wool": "<PERSON><PERSON> ull", "block.minecraft.blackstone": "Swartstien", "block.minecraft.blackstone_slab": "Swartstįestjiv", "block.minecraft.blackstone_stairs": "Swartstįetrampur", "block.minecraft.blackstone_wall": "Swartstįemuor", "block.minecraft.blast_furnace": "Blästungen", "block.minecraft.blue_banner": "Blå fana", "block.minecraft.blue_bed": "Blå saingg", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.blue_candle_cake": "Tårt min blå<PERSON>tt liuos", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON> matta", "block.minecraft.blue_concrete": "Blå bitongg", "block.minecraft.blue_concrete_powder": "Blå bitonggpulver", "block.minecraft.blue_glazed_terracotta": "Blåglasirað terrakotta", "block.minecraft.blue_ice": "Blåaisn", "block.minecraft.blue_orchid": "Blå uortjiði", "block.minecraft.blue_shulker_box": "Blå shulkerlåð", "block.minecraft.blue_stained_glass": "Blåflekkaðglas", "block.minecraft.blue_stained_glass_pane": "Blåflekkaðglasrut", "block.minecraft.blue_terracotta": "Blå terrakotta", "block.minecraft.blue_wool": "Blå ull", "block.minecraft.bone_block": "Bįeblokk", "block.minecraft.bookshelf": "Buokill", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "Iennkorallblokk", "block.minecraft.brain_coral_fan": "Iennkorallflekt", "block.minecraft.brain_coral_wall_fan": "Iennkorallväggsflekt", "block.minecraft.brewing_stand": "Bryggstellningg", "block.minecraft.brick_slab": "Tigelstjiv", "block.minecraft.brick_stairs": "Tigelstįetrampur", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON>tiewegg", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON>a", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Tårt min brunt liuos", "block.minecraft.brown_carpet": "<PERSON><PERSON> matta", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "Brunglasirað terrakotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Brunflekkaðglasrut", "block.minecraft.brown_terracotta": "<PERSON><PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>l", "block.minecraft.bubble_column": "Bubbelpilår", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON>kor<PERSON>", "block.minecraft.bubble_coral_block": "Bubbelkorallblokk", "block.minecraft.bubble_coral_fan": "Bubbelkorallflekt", "block.minecraft.bubble_coral_wall_fan": "Bubbelkorallweggsflekt", "block.minecraft.budding_amethyst": "Knuppend ametist", "block.minecraft.bush": "<PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cake": "Tårta", "block.minecraft.calcite": "Kalkspat", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON> sculk<PERSON>", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "Stiarinljuos", "block.minecraft.candle_cake": "Stiariniuostårt", "block.minecraft.carrots": "M<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "Grottweðreð", "block.minecraft.cave_vines": "Grottkleðerwekst", "block.minecraft.cave_vines_plant": "Grottkleðerwekstplempta", "block.minecraft.chain": "Tjaia", "block.minecraft.chain_command_block": "Umtagend kommandoblokk", "block.minecraft.cherry_button": "K<PERSON><PERSON>bärsknapp", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d<PERSON><PERSON>", "block.minecraft.cherry_fence": "Körsbärsstaket", "block.minecraft.cherry_fence_gate": "K<PERSON>rsbärsgrind", "block.minecraft.cherry_hanging_sign": "Hängande körsbärsskylt", "block.minecraft.cherry_leaves": "K<PERSON><PERSON>bärslöv", "block.minecraft.cherry_log": "Körsbärsstam", "block.minecraft.cherry_planks": "K<PERSON>rsbärsplankor", "block.minecraft.cherry_pressure_plate": "K<PERSON>rsbärstryckplatta", "block.minecraft.cherry_sapling": "K<PERSON>rsbärsplanta", "block.minecraft.cherry_sign": "K<PERSON>rsbärsskylt", "block.minecraft.cherry_slab": "K<PERSON>rsbärsplatta", "block.minecraft.cherry_stairs": "K<PERSON>rsbärstrappa", "block.minecraft.cherry_trapdoor": "K<PERSON>rsbärsfallucka", "block.minecraft.cherry_wall_hanging_sign": "Hängande körsbärsskylt på vägg", "block.minecraft.cherry_wall_sign": "Körsbärsskylt på vägg", "block.minecraft.cherry_wood": "K<PERSON>rsbärsträ", "block.minecraft.chest": "<PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON>t smiðsteð", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_polished_blackstone": "Mejslad polirað swartstien", "block.minecraft.chiseled_quartz_block": "Mejslað kwartsblokk", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON><PERSON> roð<PERSON>", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON><PERSON> harts<PERSON>", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "Mejslað stįetigelstie", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON> tuff", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.chorus_flower": "Körbljomme", "block.minecraft.chorus_plant": "K<PERSON><PERSON><PERSON>t", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Stängd ögonblomma", "block.minecraft.coal_block": "Kuolblokk", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON><PERSON> muld", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "Stjiv ov Diuopstjifferstjiv", "block.minecraft.cobbled_deepslate_stairs": "Trampur åv kullerstiensdiuopstjiffer", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> ov kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Gatusįestjiv", "block.minecraft.cobblestone_stairs": "Gatustįetrampur", "block.minecraft.cobblestone_wall": "Gatustįewegg", "block.minecraft.cobweb": "Dyörgnęt", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Kommandoblokk", "block.minecraft.comparator": "Redstonejemfyörer", "block.minecraft.composter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.conduit": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Kupperblokk", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Kuppärmåm", "block.minecraft.copper_trapdoor": "Kuppärfollukku", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Sprutjin diuopstjifferplatt muorstiener", "block.minecraft.cracked_deepslate_tiles": "Sprutjin diuopstjifferkakel", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "Sprukkner swartstįestiener", "block.minecraft.cracked_stone_bricks": "Klumnað st stįetigelstie", "block.minecraft.crafter": "Tilwerker", "block.minecraft.crafting_table": "Arbietsbainkk", "block.minecraft.creaking_heart": "Knarrhjärta", "block.minecraft.creeper_head": "Creeper-ovuð", "block.minecraft.creeper_wall_head": "Creeperweggsovuð", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> knapp", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> dörer", "block.minecraft.crimson_fence": "Mörkrott stakiet", "block.minecraft.crimson_fence_gate": "Mörkroð dörer i stakiet", "block.minecraft.crimson_fungus": "Karmosinsopp", "block.minecraft.crimson_hanging_sign": "Hängande karmosinskylt", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "Karmosinnylium", "block.minecraft.crimson_planks": "Mörk<PERSON>ð plankur", "block.minecraft.crimson_pressure_plate": "Mörkroð trykkstjiv", "block.minecraft.crimson_roots": "Karmosinruotur", "block.minecraft.crimson_sign": "Mörkroð stjylt", "block.minecraft.crimson_slab": "Mörkroð stjiva", "block.minecraft.crimson_stairs": "Mörkroð trampur", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "Mörkroð follukk", "block.minecraft.crimson_wall_hanging_sign": "Hängande karmosinskylt på vägg", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> we<PERSON>lt", "block.minecraft.crying_obsidian": "Grainend obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.cut_copper_stairs": "Trampur åv skurin k<PERSON>", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.cut_red_sandstone_slab": "Klipptroðsandstiestjiv", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_banner": "Syanfergað fana", "block.minecraft.cyan_bed": "Syansaingg", "block.minecraft.cyan_candle": "Syanfergað stiarinljuos", "block.minecraft.cyan_candle_cake": "Tårt min syanfergað liuos", "block.minecraft.cyan_carpet": "Syanfergað matta", "block.minecraft.cyan_concrete": "Syanfergað bitongg", "block.minecraft.cyan_concrete_powder": "Syanfergað bitonggpulver", "block.minecraft.cyan_glazed_terracotta": "Syanfergað glasirað terrakotta", "block.minecraft.cyan_shulker_box": "Cyanfergað shulkerlåð", "block.minecraft.cyan_stained_glass": "Syanfle<PERSON>ðg<PERSON>", "block.minecraft.cyan_stained_glass_pane": "Cyanflekkaðglasrut", "block.minecraft.cyan_terracotta": "Cyanfergað terrakotta", "block.minecraft.cyan_wool": "Syanfer<PERSON><PERSON> ull", "block.minecraft.damaged_anvil": "Skåðåð smiðsteð", "block.minecraft.dandelion": "Smyörbljomm", "block.minecraft.dark_oak_button": "Mörkiekknapp", "block.minecraft.dark_oak_door": "Mörkiekdörer", "block.minecraft.dark_oak_fence": "Mörkiekstakiet", "block.minecraft.dark_oak_fence_gate": "Grind i mörkiekstakiet", "block.minecraft.dark_oak_hanging_sign": "Hängande mörk ekskylt", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON>iek<PERSON><PERSON>", "block.minecraft.dark_oak_planks": "Mörkiekplaunkur", "block.minecraft.dark_oak_pressure_plate": "Mörkiekpressplatt", "block.minecraft.dark_oak_sapling": "Mörkiekplemta", "block.minecraft.dark_oak_sign": "Mörkiekstjylt", "block.minecraft.dark_oak_slab": "Mörkiekstjiv", "block.minecraft.dark_oak_stairs": "Mörkiektrampur", "block.minecraft.dark_oak_trapdoor": "Mörkiekfollukku", "block.minecraft.dark_oak_wall_hanging_sign": "Hängande mörk ekskylt på vägg", "block.minecraft.dark_oak_wall_sign": "Mörkiekweggstjylt", "block.minecraft.dark_oak_wood": "Mörkiekwiðn", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Mörk prismarinstjiv", "block.minecraft.dark_prismarine_stairs": "Mörk prismarintrampur", "block.minecraft.daylight_detector": "Dagsljuosdetektor", "block.minecraft.dead_brain_coral": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON>l<PERSON>lo<PERSON>", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON>ggsfle<PERSON>", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> b<PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON> bubbell<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON> b<PERSON>", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON> b<PERSON>ggsfle<PERSON>", "block.minecraft.dead_bush": "<PERSON><PERSON> buoske", "block.minecraft.dead_fire_coral": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON>allblok<PERSON>", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON>all<PERSON>ggsflekt", "block.minecraft.dead_horn_coral": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON>lo<PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON>ggs<PERSON>", "block.minecraft.dead_tube_coral": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "Dod rörkorallblokk", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_wall_fan": "Doð r<PERSON>orallweggsflekt", "block.minecraft.decorated_pot": "Dekorerad keramikkruka", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "Platt åv diuopstjiffertigelstiener", "block.minecraft.deepslate_brick_stairs": "Trampur åv diuopstjiffertigelstiener", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON>pstjiffertigelstiemuor", "block.minecraft.deepslate_bricks": "Djuopstjiffertigelstie", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>k<PERSON>åð<PERSON>", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>ffer<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>smaragdåðer", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.deepslate_tile_slab": "Platt åv diuopstjifferkakel", "block.minecraft.deepslate_tile_stairs": "Trampur åv djuopsstjifferkakelplattur", "block.minecraft.deepslate_tile_wall": "Wegg åv djuopstjifferkakelplattur", "block.minecraft.deepslate_tiles": "Kaelplattur åv diuo<PERSON>jiffer", "block.minecraft.detector_rail": "<PERSON><PERSON> min detektor", "block.minecraft.diamond_block": "Diamantblokk", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "Diroit", "block.minecraft.diorite_slab": "Diroitstjiv", "block.minecraft.diorite_stairs": "Diorittrampur", "block.minecraft.diorite_wall": "Dioritwegg", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "Staig", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "Drake<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "Drakovuð", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "Uttorkad ghast", "block.minecraft.dried_kelp_block": "Blokk ov torkað alg", "block.minecraft.dripstone_block": "Druppstiensblokk", "block.minecraft.dropper": "Autmäter", "block.minecraft.emerald_block": "Smaragdblokk", "block.minecraft.emerald_ore": "<PERSON>marag<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "Fertrullninggsbuord", "block.minecraft.end_gateway": "Slutingangg", "block.minecraft.end_portal": "Slutportal", "block.minecraft.end_portal_frame": "Endportalram", "block.minecraft.end_rod": "Slutkevil", "block.minecraft.end_stone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Endtigelsstįestjiv", "block.minecraft.end_stone_brick_stairs": "Endstįetigelstįetrampur", "block.minecraft.end_stone_brick_wall": "Slutstietigelstįewegg", "block.minecraft.end_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Eksponirað mejslað kup<PERSON>är", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON> so synes", "block.minecraft.exposed_copper_bulb": "Eksponirað kuppärlampel", "block.minecraft.exposed_copper_door": "Eksponirað kuppärdörr", "block.minecraft.exposed_copper_grate": "Eksponirað kup<PERSON>ä<PERSON>ller", "block.minecraft.exposed_copper_trapdoor": "Eksponirað kuppärfollukku", "block.minecraft.exposed_cut_copper": "Eksponirað skurin kupär", "block.minecraft.exposed_cut_copper_slab": "Eksponirað kupärstjiv", "block.minecraft.exposed_cut_copper_stairs": "Eksponirað skurin k<PERSON>pur", "block.minecraft.farmland": "Åkerjuord", "block.minecraft.fern": "Uormbunk", "block.minecraft.fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Jäldkorallblokk", "block.minecraft.fire_coral_fan": "Jäldkorallflekt", "block.minecraft.fire_coral_wall_fan": "Jäldkorallweggsflekt", "block.minecraft.firefly_bush": "Eldflugsbuske", "block.minecraft.fletching_table": "Fletchinbuord", "block.minecraft.flower_pot": "Bljommkruk", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON> so bljommer", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON><PERSON><PERSON> so bljommer", "block.minecraft.frogspawn": "Lunginrunned", "block.minecraft.frosted_ice": "<PERSON><PERSON> ais", "block.minecraft.furnace": "Smeltungen", "block.minecraft.gilded_blackstone": "Gullsmitåð swartstien", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glasrut", "block.minecraft.glow_lichen": "<PERSON><PERSON> so gly<PERSON>ðer", "block.minecraft.glowstone": "Glyöðstien", "block.minecraft.gold_block": "Gullblokk", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "Granitstjiv", "block.minecraft.granite_stairs": "Granittrampur", "block.minecraft.granite_wall": "Granitwegg", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Grasblokk", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>ð", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>a", "block.minecraft.gray_bed": "G<PERSON><PERSON>g", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "Tårt min grått liuos", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON> matta", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON> bitonggpulver", "block.minecraft.gray_glazed_terracotta": "Gråglasirað terrakotta", "block.minecraft.gray_shulker_box": "Grå shulkerlåð", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "Gråflekkaðglasrut", "block.minecraft.gray_terracotta": "Grå terrakotta", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>l", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "Tårt min gryönt liuos", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>ta", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "Gryönglasirað terrakotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "Gryönfle<PERSON>", "block.minecraft.green_stained_glass_pane": "Gryönflekkaðglasrut", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>l", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON><PERSON> ruotur", "block.minecraft.hay_block": "Öbal", "block.minecraft.heavy_core": "<PERSON>ng kärna", "block.minecraft.heavy_weighted_pressure_plate": "Tungg pressplatt", "block.minecraft.honey_block": "Onunggsblokk", "block.minecraft.honeycomb_block": "Wakskakublokk", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral_block": "Uonnkorallblokk", "block.minecraft.horn_coral_fan": "Uonnkorallflekt", "block.minecraft.horn_coral_wall_fan": "Uonnkorallweggsflekt", "block.minecraft.ice": "Aisn", "block.minecraft.infested_chiseled_stone_bricks": "Smit<PERSON>ð mejslað stįetigelstie", "block.minecraft.infested_cobblestone": "Smittað gatustįe", "block.minecraft.infested_cracked_stone_bricks": "Smittað sprutjintigelstie", "block.minecraft.infested_deepslate": "Infektirað di<PERSON>jiffer", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON> stįetigelstie", "block.minecraft.infested_stone": "<PERSON><PERSON><PERSON><PERSON> stie", "block.minecraft.infested_stone_bricks": "Smittað stįetigelstie", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Iennblokk", "block.minecraft.iron_door": "<PERSON><PERSON>nd<PERSON>rer", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Iennfollukku", "block.minecraft.jack_o_lantern": "Lykta, gard ov pumpu", "block.minecraft.jigsaw": "Pusselblokk", "block.minecraft.jukebox": "Musiklåð", "block.minecraft.jungle_button": "Jungelknap<PERSON>", "block.minecraft.jungle_door": "Junggeldörer", "block.minecraft.jungle_fence": "Junggelstakiet", "block.minecraft.jungle_fence_gate": "Grind i jungelstakiet", "block.minecraft.jungle_hanging_sign": "Hängande djungelskylt", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "Junggelkabb", "block.minecraft.jungle_planks": "Djunggelplaunkur", "block.minecraft.jungle_pressure_plate": "Junggelpressplatt", "block.minecraft.jungle_sapling": "Djunggelplemta", "block.minecraft.jungle_sign": "Junggelstjylt", "block.minecraft.jungle_slab": "Junggelstjiv", "block.minecraft.jungle_stairs": "Junggeltrampur", "block.minecraft.jungle_trapdoor": "Junggelluk<PERSON>", "block.minecraft.jungle_wall_hanging_sign": "Hängande djungelskylt på vägg", "block.minecraft.jungle_wall_sign": "Junggelweggstjylt", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.kelp": "Alg", "block.minecraft.kelp_plant": "Algplemtur", "block.minecraft.ladder": "<PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Lykta", "block.minecraft.lapis_block": "Blokk åv Lapis Lazuli", "block.minecraft.lapis_ore": "<PERSON><PERSON> lazuli <PERSON>", "block.minecraft.large_amethyst_bud": "Stur ametistknupp", "block.minecraft.large_fern": "Sturuormbunk", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavaketil", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "Liuosblå fana", "block.minecraft.light_blue_bed": "Liuosblå saingg", "block.minecraft.light_blue_candle": "Ljuosbl<PERSON>tt s<PERSON>", "block.minecraft.light_blue_candle_cake": "Tårt min liuosblå liuos", "block.minecraft.light_blue_carpet": "Liuos<PERSON><PERSON><PERSON> matta", "block.minecraft.light_blue_concrete": "Liuosblå bitongg", "block.minecraft.light_blue_concrete_powder": "Liuosblå bitonggpulver", "block.minecraft.light_blue_glazed_terracotta": "Ljuosblåglasirað terrakotta", "block.minecraft.light_blue_shulker_box": "Liuosblå shulkerlåða", "block.minecraft.light_blue_stained_glass": "Ljuosblåflekkaðglas", "block.minecraft.light_blue_stained_glass_pane": "Ljuosblåflekkaðglasrut", "block.minecraft.light_blue_terracotta": "Ljuosblå terrakotta", "block.minecraft.light_blue_wool": "Liu<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.light_gray_banner": "Liuosgr<PERSON> fana", "block.minecraft.light_gray_bed": "Liuosgrå saingg", "block.minecraft.light_gray_candle": "Lju<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle_cake": "Tårt min liuosgrått liuos", "block.minecraft.light_gray_carpet": "<PERSON>os<PERSON><PERSON><PERSON> matta", "block.minecraft.light_gray_concrete": "Liuosgrå bitongg", "block.minecraft.light_gray_concrete_powder": "Liuosgrå bitonggpulver", "block.minecraft.light_gray_glazed_terracotta": "Syanglasirað terrakotta", "block.minecraft.light_gray_shulker_box": "Liuosgrå shulkerlåð", "block.minecraft.light_gray_stained_glass": "Ljuosgråflekkað<PERSON>las", "block.minecraft.light_gray_stained_glass_pane": "Ljuosgråflekkaðglasrut", "block.minecraft.light_gray_terracotta": "Ljuosgrå terrakotta", "block.minecraft.light_gray_wool": "Liu<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.light_weighted_pressure_plate": "Litt pressplatt", "block.minecraft.lightning_rod": "Åsk<PERSON>ð<PERSON>", "block.minecraft.lilac": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "Bukkblaðsbljomm", "block.minecraft.lily_pad": "Lerblað", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle": "Laimfergað stiarinljuos", "block.minecraft.lime_candle_cake": "Tårt min laimfergað liuos", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> matta", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "Laimfergað glasirað terrakotta", "block.minecraft.lime_shulker_box": "Laimfergað shulkerlåð", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "Kalkflekkaðglasrut", "block.minecraft.lime_terracotta": "Limeferagðterrakott", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Wevst<PERSON><PERSON>r", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "Magentafergað stiarinljuos", "block.minecraft.magenta_candle_cake": "Tårt min lila liuos", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON> matta", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_glazed_terracotta": "Magentaglasirað terrakotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "Magentaflekkaðglas", "block.minecraft.magenta_stained_glass_pane": "Magentaflekkaðglasrut", "block.minecraft.magenta_terracotta": "Magenta terrakotta", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.magma_block": "Magmablokk", "block.minecraft.mangrove_button": "Mangroveknapp", "block.minecraft.mangrove_door": "Mangrovedörär", "block.minecraft.mangrove_fence": "Mangrovestakiet", "block.minecraft.mangrove_fence_gate": "Mangrovestakietgrind", "block.minecraft.mangrove_hanging_sign": "Hängande mangroveskylt", "block.minecraft.mangrove_leaves": "Mangrovelover", "block.minecraft.mangrove_log": "Mangrovekabbe", "block.minecraft.mangrove_planks": "Mangroveplaunkur", "block.minecraft.mangrove_pressure_plate": "Trykkplatt ov Mangrove", "block.minecraft.mangrove_propagule": "Mangrovestrott", "block.minecraft.mangrove_roots": "Mangroveruotur", "block.minecraft.mangrove_sign": "Mangrovestjylt", "block.minecraft.mangrove_slab": "Mangrovestjiv", "block.minecraft.mangrove_stairs": "Mangrovetrampur", "block.minecraft.mangrove_trapdoor": "Mangrove-fallukka", "block.minecraft.mangrove_wall_hanging_sign": "Hängande mangroveskylt på vägg", "block.minecraft.mangrove_wall_sign": "Mangroveweggstjylt", "block.minecraft.mangrove_wood": "Mangroveyrtje", "block.minecraft.medium_amethyst_bud": "Millostur ametistknupp", "block.minecraft.melon": "<PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "Melonstiuok", "block.minecraft.moss_block": "Muossblokk", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON> gatustįetrampur", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> gatustįewegg", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON> stietigelstįestjiv", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON> tigelstįetrampur", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON> stįetigelstįewegg", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON> stįetigelstie", "block.minecraft.moving_piston": "Brägend kolv", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Liertigelstjiv", "block.minecraft.mud_brick_stairs": "Liertigeltrampur", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "Lierug mangroveruotur", "block.minecraft.mushroom_stem": "Soppstjälk", "block.minecraft.mycelium": "<PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Nethertigelstįestakiet", "block.minecraft.nether_brick_slab": "Nethertigelstjiv", "block.minecraft.nether_brick_stairs": "Nethertigelstįetrampur", "block.minecraft.nether_brick_wall": "Nethertigelstįewegg", "block.minecraft.nether_bricks": "Nethertigelstiener", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_portal": "Netherportal", "block.minecraft.nether_quartz_ore": "<PERSON>herk<PERSON>s<PERSON><PERSON>er", "block.minecraft.nether_sprouts": "Nethernylaðir", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Netherwårtblokk", "block.minecraft.netherite_block": "Blokk ov netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Meddielendblokk", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_door": "Iekdörer", "block.minecraft.oak_fence": "Iekstakiet", "block.minecraft.oak_fence_gate": "Grind i iekstakiet", "block.minecraft.oak_hanging_sign": "Hängande ekskylt", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "Iekplaunk<PERSON>", "block.minecraft.oak_pressure_plate": "Iekpressplatt", "block.minecraft.oak_sapling": "<PERSON>ek<PERSON><PERSON><PERSON>", "block.minecraft.oak_sign": "Iekstjylt", "block.minecraft.oak_slab": "Iekstjiv", "block.minecraft.oak_stairs": "Iektrampur", "block.minecraft.oak_trapdoor": "Iekfollukku", "block.minecraft.oak_wall_hanging_sign": "Hängande ekskylt på vägg", "block.minecraft.oak_wall_sign": "Iekweggstjylt", "block.minecraft.oak_wood": "<PERSON>ek<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Observatör", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "Okkrafergað tuoskliuos", "block.minecraft.ominous_banner": "U<PERSON><PERSON> fana", "block.minecraft.open_eyeblossom": "Öppen ögonblomma", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_candle": "Brandguol stiarinljuos", "block.minecraft.orange_candle_cake": "Tårt min brandguolų ljuos", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON><PERSON> matta", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "Orangeglasirað terrakotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass": "Orandjfle<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Orandjflekkaðglasrut", "block.minecraft.orange_terracotta": "Guo<PERSON><PERSON>ð terrakotta", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON><PERSON> tulpan", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Oksiðirað mejslað kup<PERSON>r", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON><PERSON> so ar rostað", "block.minecraft.oxidized_copper_bulb": "Oksiðirað kuppärlampel", "block.minecraft.oxidized_copper_door": "Oksiðirað kuppärd<PERSON>rr", "block.minecraft.oxidized_copper_grate": "Oksiðirað <PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Oksiðirað kuppärfollukku", "block.minecraft.oxidized_cut_copper": "Oksiðirað skurin kup<PERSON>r", "block.minecraft.oxidized_cut_copper_slab": "Oksiðirað skurin k<PERSON>v", "block.minecraft.oxidized_cut_copper_stairs": "Oksiðirað trampur ov skurin kupär", "block.minecraft.packed_ice": "Pakkað ais", "block.minecraft.packed_mud": "<PERSON><PERSON> liera", "block.minecraft.pale_hanging_moss": "Hängande blek mossa", "block.minecraft.pale_moss_block": "Blekt mossblock", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON> moss<PERSON>ta", "block.minecraft.pale_oak_button": "Blek ekknapp", "block.minecraft.pale_oak_door": "Blek ekdörr", "block.minecraft.pale_oak_fence": "Blekt ekstaket", "block.minecraft.pale_oak_fence_gate": "Blek ekgrind", "block.minecraft.pale_oak_hanging_sign": "Hängande blek ekskylt", "block.minecraft.pale_oak_leaves": "Bleka eklöv", "block.minecraft.pale_oak_log": "Blek ekstam", "block.minecraft.pale_oak_planks": "Bleka ekplankor", "block.minecraft.pale_oak_pressure_plate": "Blek ektryckplatta", "block.minecraft.pale_oak_sapling": "Blek ekplanta", "block.minecraft.pale_oak_sign": "Blek ekskylt", "block.minecraft.pale_oak_slab": "Blek ekplatta", "block.minecraft.pale_oak_stairs": "Blek ektrappa", "block.minecraft.pale_oak_trapdoor": "Blek ekfallucka", "block.minecraft.pale_oak_wall_hanging_sign": "Hängande blek ekskylt på vägg", "block.minecraft.pale_oak_wall_sign": "Blek ekskylt på vägg", "block.minecraft.pale_oak_wood": "Blekt ekträ", "block.minecraft.pearlescent_froglight": "Pärlemorlysend tuosk<PERSON>uos", "block.minecraft.peony": "<PERSON><PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "Ferstienaðiekstjiv", "block.minecraft.piglin_head": "Piglinhuvud", "block.minecraft.piglin_wall_head": "Piglinhuvud på vägg", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON>rt min rosa liuos", "block.minecraft.pink_carpet": "<PERSON> matta", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_glazed_terracotta": "Stjärglasirað terrakotta", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_stained_glass": "St<PERSON>ä<PERSON>lekkaðglas", "block.minecraft.pink_stained_glass_pane": "Stjärflekkaðglasrut", "block.minecraft.pink_terracotta": "St<PERSON>är terrakot<PERSON>", "block.minecraft.pink_tulip": "Rosatulpan", "block.minecraft.pink_wool": "<PERSON>l", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "Kolvovuð", "block.minecraft.pitcher_crop": "Kannrįestiena", "block.minecraft.pitcher_plant": "Kannrįes", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "%ss skolle ", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.polished_andesite": "Pol<PERSON><PERSON> and<PERSON>t", "block.minecraft.polished_andesite_slab": "Polirað andesitstjiv", "block.minecraft.polished_andesite_stairs": "Poliraðer andesittrampur", "block.minecraft.polished_basalt": "Polirað basalt", "block.minecraft.polished_blackstone": "Polirað swartstien", "block.minecraft.polished_blackstone_brick_slab": "Stjiv ov polirað swartstįestiener", "block.minecraft.polished_blackstone_brick_stairs": "Trampur ov polirað swartstįestiener", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> ov poliraðer swartstįestiener", "block.minecraft.polished_blackstone_bricks": "Poliraðer swartstįestiener", "block.minecraft.polished_blackstone_button": "Polirað swartstįeknapp", "block.minecraft.polished_blackstone_pressure_plate": "Poilirað trykkplatt ov swartstien", "block.minecraft.polished_blackstone_slab": "Polirað stjiv ov swartstien", "block.minecraft.polished_blackstone_stairs": "Polirað trampur ov swartstien", "block.minecraft.polished_blackstone_wall": "Polirað muor ov swartstien", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Polirað diuopstjifferplatta", "block.minecraft.polished_deepslate_stairs": "Trampur åv diuo<PERSON>jiffer", "block.minecraft.polished_deepslate_wall": "Polirað muor åv <PERSON>uo<PERSON>", "block.minecraft.polished_diorite": "Polirað dorit", "block.minecraft.polished_diorite_slab": "Polirað dioritstjiv", "block.minecraft.polished_diorite_stairs": "Poliraðer diorittrampur", "block.minecraft.polished_granite": "<PERSON><PERSON><PERSON><PERSON> granit", "block.minecraft.polished_granite_slab": "Polirað granitstjiv", "block.minecraft.polished_granite_stairs": "Poliraðer granittrampur", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON> tuff", "block.minecraft.polished_tuff_slab": "Polirað tuffplatta", "block.minecraft.polished_tuff_stairs": "Polirað tufftrampur", "block.minecraft.polished_tuff_wall": "Polirað tuffwegg", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Pärur", "block.minecraft.potted_acacia_sapling": "Akasiaplemt i kruku", "block.minecraft.potted_allium": "Graslok i kruku", "block.minecraft.potted_azalea_bush": "Asalea i kruku", "block.minecraft.potted_azure_bluet": "Posslinsstienn i kruku", "block.minecraft.potted_bamboo": "Bambu i kruku", "block.minecraft.potted_birch_sapling": "Byörkplemt i kruku", "block.minecraft.potted_blue_orchid": "Blå uortjiði i kruku", "block.minecraft.potted_brown_mushroom": "Brunsopp i kruku", "block.minecraft.potted_cactus": "Kaktus i kruku", "block.minecraft.potted_cherry_sapling": "Körsbärsplanta i kruka", "block.minecraft.potted_closed_eyeblossom": "Stängd ögonblomma i kruka", "block.minecraft.potted_cornflower": "Rugbljomm i kruku", "block.minecraft.potted_crimson_fungus": "Mörkroð sopp i kruku", "block.minecraft.potted_crimson_roots": "Mörkroð ruotur i kruku", "block.minecraft.potted_dandelion": "Smyörbljuomm i kruku", "block.minecraft.potted_dark_oak_sapling": "Mörkiekplemt i kruku", "block.minecraft.potted_dead_bush": "Dod buosk i kruku", "block.minecraft.potted_fern": "Uormbunk i kruku", "block.minecraft.potted_flowering_azalea_bush": "Asale<PERSON> so bljuommer i kruku", "block.minecraft.potted_jungle_sapling": "Junggelplemt i kruku", "block.minecraft.potted_lily_of_the_valley": "Bukkblad i kruku", "block.minecraft.potted_mangrove_propagule": "Mangrovestrott i kruku", "block.minecraft.potted_oak_sapling": "Iekplemt i kruku", "block.minecraft.potted_open_eyeblossom": "Öppen ögonblomma i kruka", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON><PERSON><PERSON> tulpan i kruku", "block.minecraft.potted_oxeye_daisy": "Waitkull i kruku", "block.minecraft.potted_pale_oak_sapling": "Blek ekplanta i kruka", "block.minecraft.potted_pink_tulip": "<PERSON> tulpan i kruku", "block.minecraft.potted_poppy": "Wallmobljuomm i kruku", "block.minecraft.potted_red_mushroom": "Roðsopp i kruku", "block.minecraft.potted_red_tulip": "Roðtulpan i kruku", "block.minecraft.potted_spruce_sapling": "Gråplemt i kruku", "block.minecraft.potted_torchflower": "Fackellilja i kruka", "block.minecraft.potted_warped_fungus": "Fevrengd sopp i kruku", "block.minecraft.potted_warped_roots": "Fevrengd routur i kruku", "block.minecraft.potted_white_tulip": "Waittulpan i kruku", "block.minecraft.potted_wither_rose": "Wissnaðruos i kruku", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON> fer puð<PERSON>n", "block.minecraft.powered_rail": "Rels min kraft", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarintigelstjiv", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "Prismarinstjiv", "block.minecraft.prismarine_stairs": "Prismarintrampur", "block.minecraft.prismarine_wall": "<PERSON><PERSON>ari<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Pump<PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "<PERSON> fana", "block.minecraft.purple_bed": "<PERSON> sa<PERSON>", "block.minecraft.purple_candle": "<PERSON>", "block.minecraft.purple_candle_cake": "Tårt min lila liuos", "block.minecraft.purple_carpet": "<PERSON> matta", "block.minecraft.purple_concrete": "<PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON>", "block.minecraft.purple_glazed_terracotta": "Purpurglasirad terrakotta", "block.minecraft.purple_shulker_box": "<PERSON>", "block.minecraft.purple_stained_glass": "Lila<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "Lilaflekkaðglasrut", "block.minecraft.purple_terracotta": "<PERSON> terra<PERSON>", "block.minecraft.purple_wool": "<PERSON> ull", "block.minecraft.purpur_block": "Purpurblokk", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_stairs": "Purpurtrampur", "block.minecraft.quartz_block": "Kwartsblokk", "block.minecraft.quartz_bricks": "Tigelstiener ov kwarts", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "Kwartsstjiv", "block.minecraft.quartz_stairs": "Kwartstrampur", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Råkuppärblokk", "block.minecraft.raw_gold_block": "Rågullblokk", "block.minecraft.raw_iron_block": "Råiennblokk", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> fana", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON>", "block.minecraft.red_candle_cake": "Tårt min roð ljuos", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> matta", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "Roðglasirað terrakotta", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>k", "block.minecraft.red_nether_brick_slab": "Rod nethertigelstįestjiv", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> Nethertigelstįetrampur", "block.minecraft.red_nether_brick_wall": "Roð nethertigelstįewegg", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone": "Roðsandstie", "block.minecraft.red_sandstone_slab": "Roðsandstiestiv", "block.minecraft.red_sandstone_stairs": "Roðsandstįetrampur", "block.minecraft.red_sandstone_wall": "Roð sandstįewegg", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass_pane": "Rodflekkaðglasrut", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON> terra<PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.redstone_block": "Redstoneblokk", "block.minecraft.redstone_lamp": "Redstonelampa", "block.minecraft.redstone_ore": "Redstoneåðer", "block.minecraft.redstone_torch": "Redstonestikkliuos", "block.minecraft.redstone_wall_torch": "Redstoneweggstikkljuos", "block.minecraft.redstone_wire": "Redstonetråð", "block.minecraft.reinforced_deepslate": "Försterkt djuopstjiffer", "block.minecraft.repeater": "Redstonestutterer", "block.minecraft.repeating_command_block": "Repetirend kommandoblokk", "block.minecraft.resin_block": "Harts<PERSON>", "block.minecraft.resin_brick_slab": "Hartstegelplatta", "block.minecraft.resin_brick_stairs": "Hartstegeltrappa", "block.minecraft.resin_brick_wall": "Hartstegelmur", "block.minecraft.resin_bricks": "Hart<PERSON>gel", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON> för <PERSON>", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>bu<PERSON><PERSON>", "block.minecraft.sand": "<PERSON><PERSON>", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Sandstiestjiv", "block.minecraft.sandstone_stairs": "Sandstįetrampur", "block.minecraft.sandstone_wall": "Sanstįewegg", "block.minecraft.scaffolding": "Byggstellningg", "block.minecraft.sculk": "Skulk", "block.minecraft.sculk_catalyst": "Skulkkatalysator", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_shrieker": "Skulkremer", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_lantern": "Sjulantern", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Återupplivninggspunkt bistemmd", "block.minecraft.short_dry_grass": "<PERSON><PERSON> torrt gräs", "block.minecraft.short_grass": "Stuttgraseð", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Shulkerlåð", "block.minecraft.skeleton_skull": "Skelettskoll", "block.minecraft.skeleton_wall_skull": "Skelettweggskoll", "block.minecraft.slime_block": "Kliemblokk", "block.minecraft.small_amethyst_bud": "Litn ametistknupp", "block.minecraft.small_dripleaf": "Små druplover", "block.minecraft.smithing_table": "Smiðbuord", "block.minecraft.smoker": "Rätjungen", "block.minecraft.smooth_basalt": "Blotbasalt", "block.minecraft.smooth_quartz": "Blot kwartsstjiv", "block.minecraft.smooth_quartz_slab": "Blot kwartsstjiv", "block.minecraft.smooth_quartz_stairs": "Blot kwartstrampur", "block.minecraft.smooth_red_sandstone": "Blotroðsandstie", "block.minecraft.smooth_red_sandstone_slab": "Blot roð sandstįestjiv", "block.minecraft.smooth_red_sandstone_stairs": "Blot roðsandstįetrampur", "block.minecraft.smooth_sandstone": "Blotsandstie", "block.minecraft.smooth_sandstone_slab": "Blot sandstįestjiv", "block.minecraft.smooth_sandstone_stairs": "Blot sandstįetrampur", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "Blotstiestjiv", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow": "Sniųo", "block.minecraft.snow_block": "Sniųoblokk", "block.minecraft.soul_campfire": "S<PERSON><PERSON><PERSON>j<PERSON>ldn", "block.minecraft.soul_fire": "S<PERSON><PERSON><PERSON>j<PERSON>ldn", "block.minecraft.soul_lantern": "Själsllykta", "block.minecraft.soul_sand": "Själsand", "block.minecraft.soul_soil": "Sj<PERSON>lsmulld", "block.minecraft.soul_torch": "Själsstikkliuos", "block.minecraft.soul_wall_torch": "Själsweggstikkliuos", "block.minecraft.spawn.not_valid": "Du ar it ingg i<PERSON>aingg elld standuppanker, elld og ir eð festört", "block.minecraft.spawner": "Monsterskapare", "block.minecraft.spawner.desc1": "Interaktion med skapelseägg:", "block.minecraft.spawner.desc2": "<PERSON><PERSON><PERSON>", "block.minecraft.sponge": "Twåsopp", "block.minecraft.spore_blossom": "Sporbljomningg", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_door": "Grą̊dörer", "block.minecraft.spruce_fence": "Grǫstakiet", "block.minecraft.spruce_fence_gate": "Grind i grą̊stakiet", "block.minecraft.spruce_hanging_sign": "Hängande granskylt", "block.minecraft.spruce_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "Grą̊plaunkur", "block.minecraft.spruce_pressure_plate": "Grǫpressplant", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_slab": "Gråstjiv", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "Grą̊follukku", "block.minecraft.spruce_wall_hanging_sign": "Hängande granskylt på vägg", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON>ggstj<PERSON>t", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "Kliemun kolv", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Stietigelstjiv", "block.minecraft.stone_brick_stairs": "Stįetigelstietrampur", "block.minecraft.stone_brick_wall": "Stįetigelstįewegg", "block.minecraft.stone_bricks": "Stįetigelstie", "block.minecraft.stone_button": "Stįeknapp", "block.minecraft.stone_pressure_plate": "Stįepressplatt", "block.minecraft.stone_slab": "Stiestjiv", "block.minecraft.stone_stairs": "Stįetrampur", "block.minecraft.stonecutter": "Stiestjärer", "block.minecraft.stripped_acacia_log": "Barkað akasiakabb", "block.minecraft.stripped_acacia_wood": "Barkað akasiawiðn", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON><PERSON> bambu<PERSON>", "block.minecraft.stripped_birch_log": "Barka<PERSON>", "block.minecraft.stripped_birch_wood": "Barkað byörkwiðn", "block.minecraft.stripped_cherry_log": "Avbarkad körsbärsstam", "block.minecraft.stripped_cherry_wood": "Avbarkat körsbärsträ", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON> karm<PERSON>", "block.minecraft.stripped_dark_oak_log": "Barkað mörkiekkabb", "block.minecraft.stripped_dark_oak_wood": "Barkað mörkiekwiðn", "block.minecraft.stripped_jungle_log": "Barka<PERSON> jung<PERSON>", "block.minecraft.stripped_jungle_wood": "Bar<PERSON><PERSON> jung<PERSON>ðn", "block.minecraft.stripped_mangrove_log": "Barkað mangrovekabb", "block.minecraft.stripped_mangrove_wood": "Barkað magrovyrtje", "block.minecraft.stripped_oak_log": "Barkað iek<PERSON>bb", "block.minecraft.stripped_oak_wood": "Barkað iekwiðn", "block.minecraft.stripped_pale_oak_log": "Avbarkad blek ekstam", "block.minecraft.stripped_pale_oak_wood": "Avbarkat blekt ekträ", "block.minecraft.stripped_spruce_log": "Barkað grǫ<PERSON>bb", "block.minecraft.stripped_spruce_wood": "Barkað grǫwi<PERSON>n", "block.minecraft.stripped_warped_hyphae": "Åvbarkað fevränggd yf", "block.minecraft.stripped_warped_stem": "Åvbarkað fevränggd stam", "block.minecraft.structure_block": "Strukturblokk", "block.minecraft.structure_void": "Stukrturtuomruom", "block.minecraft.sugar_cane": "Sokkerrör", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "Misstainkt grus", "block.minecraft.suspicious_sand": "Misstänksam sand", "block.minecraft.sweet_berry_bush": "Syötberbuosk", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON> to<PERSON>t gr<PERSON>s", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tall_seagrass": "<PERSON><PERSON> s<PERSON>", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Testblock", "block.minecraft.test_instance_block": "Testinstansblock", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tnt": "Dynamit", "block.minecraft.tnt.disabled": "Dynamitexplosioner är inaktiverade", "block.minecraft.torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Fackellilja", "block.minecraft.torchflower_crop": "Fackelliljeplanta", "block.minecraft.trapped_chest": "Instainggd tjist", "block.minecraft.trial_spawner": "Utmaningsskapare", "block.minecraft.tripwire": "Styppeltråð", "block.minecraft.tripwire_hook": "Styppeltråðkruok", "block.minecraft.tube_coral": "Rörk<PERSON><PERSON>", "block.minecraft.tube_coral_block": "Rörkorallblokk", "block.minecraft.tube_coral_fan": "Rörkorallflekt", "block.minecraft.tube_coral_wall_fan": "Rörkorallsväggflekt", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Platt åv tufftigelstiener", "block.minecraft.tuff_brick_stairs": "Tufftiegelstientrampur", "block.minecraft.tuff_brick_wall": "Tufftiegelstienwegg", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_slab": "Tuffplatta", "block.minecraft.tuff_stairs": "Tufftrampur", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Stjöldkluossegg", "block.minecraft.twisting_vines": "Rwaiðend kleðerwekst", "block.minecraft.twisting_vines_plant": "Rwaiðend kleðervekstplemptur", "block.minecraft.vault": "Kassaskåp", "block.minecraft.verdant_froglight": "Gryönskend tuoskliuos", "block.minecraft.vine": "Kleðerwekst", "block.minecraft.void_air": "Tuomweð<PERSON>ð", "block.minecraft.wall_torch": "Weggstikkljuos", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON> knapp", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "Fevrängt stakiet", "block.minecraft.warped_fence_gate": "Fevrengd grind", "block.minecraft.warped_fungus": "Fevrä<PERSON><PERSON> sopp", "block.minecraft.warped_hanging_sign": "Hängande förvrängd skylt", "block.minecraft.warped_hyphae": "Fevrängd yfer", "block.minecraft.warped_nylium": "Förvrängd nylium", "block.minecraft.warped_planks": "Fevrängd plaunk<PERSON>", "block.minecraft.warped_pressure_plate": "Fevrängd trykkstjiv", "block.minecraft.warped_roots": "Fervrängd ruotur", "block.minecraft.warped_sign": "Fevrengd stjylt", "block.minecraft.warped_slab": "Fevrängd stjiva", "block.minecraft.warped_stairs": "Fevrengd trampur", "block.minecraft.warped_stem": "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON> stam", "block.minecraft.warped_trapdoor": "Fevrengd follukku", "block.minecraft.warped_wall_hanging_sign": "Hängande förvrängd skylt på vägg", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON> we<PERSON>", "block.minecraft.warped_wart_block": "Neðerwartblokk", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_chiseled_copper": "Waksað mejslað kup<PERSON>r", "block.minecraft.waxed_copper_block": "Kuppärblokk so ir waksað", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_door": "Waksað oksiðirað kuppärdörr", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_trapdoor": "Waksað kup<PERSON>ärfollukku", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON><PERSON> skurin k<PERSON>", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> skurin k<PERSON>", "block.minecraft.waxed_cut_copper_stairs": "Waksaðer kuppärtrampur", "block.minecraft.waxed_exposed_chiseled_copper": "Waksað eksponirað mejslað kuppär", "block.minecraft.waxed_exposed_copper": "Waksað eksponiaraðkupär", "block.minecraft.waxed_exposed_copper_bulb": "Waksað eksponirað kuppärlampel", "block.minecraft.waxed_exposed_copper_door": "Waksað eksponirað kup<PERSON>ärdörr", "block.minecraft.waxed_exposed_copper_grate": "Waksað eksponirað kup<PERSON>", "block.minecraft.waxed_exposed_copper_trapdoor": "Waksað eksponirað kuppärfollukku", "block.minecraft.waxed_exposed_cut_copper": "Waksað eksponirað skurin kupär", "block.minecraft.waxed_exposed_cut_copper_slab": "Waksað eksponirað skurin kupärstjiv", "block.minecraft.waxed_exposed_cut_copper_stairs": "Waksað eksponirað kupärtrampur", "block.minecraft.waxed_oxidized_chiseled_copper": "Waksað oksiðirað mejslað kuppär", "block.minecraft.waxed_oxidized_copper": "Waksað oksiðraðkupär", "block.minecraft.waxed_oxidized_copper_bulb": "Waksað oksiðirað kuppärlampel", "block.minecraft.waxed_oxidized_copper_door": "Waksað oksiðirað kuppärdörr", "block.minecraft.waxed_oxidized_copper_grate": "Waksað oksiðirað kup<PERSON>ller", "block.minecraft.waxed_oxidized_copper_trapdoor": "Waksað oksiðirað kuppärfollukku", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON> waksað oksiðirað<PERSON>ppä<PERSON>", "block.minecraft.waxed_oxidized_cut_copper_slab": "Waksað oksiðirað skurin kup<PERSON>ärstjiv", "block.minecraft.waxed_oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON> waksað oksiðiraðkuppärtrampur", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> weðerne<PERSON>tt mejslað kuppär", "block.minecraft.waxed_weathered_copper": "Wederne´tt waksað kuppär", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> weð<PERSON> kup<PERSON>ärlampel", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> weð<PERSON> kup<PERSON><PERSON>rr", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON> weð<PERSON> kup<PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "Waksað weðerne<PERSON>tt kuppärfollukku", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON> weðerne<PERSON>tt skurin kupär", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON>aks<PERSON><PERSON> weðerne<PERSON>tt skurin kupärstjiv", "block.minecraft.waxed_weathered_cut_copper_stairs": "Waks<PERSON>ð weðerne´tt trampur åv skurin kupär", "block.minecraft.weathered_chiseled_copper": "Weðerne<PERSON>tt mejslað kup<PERSON>är", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON> so ir weð<PERSON><PERSON>in", "block.minecraft.weathered_copper_bulb": "Weðerne<PERSON>tt kuppärlampel", "block.minecraft.weathered_copper_door": "<PERSON>ð<PERSON><PERSON><PERSON> k<PERSON><PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.weathered_copper_trapdoor": "Weðerne´tt kuppärfollukku", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> skurin k<PERSON>r", "block.minecraft.weathered_cut_copper_slab": "Wederne´tt skurin k<PERSON>ärstjiv", "block.minecraft.weathered_cut_copper_stairs": "Weðerne´tt trampur åv skurin kup<PERSON>r", "block.minecraft.weeping_vines": "Druppend kleðerwekst", "block.minecraft.weeping_vines_plant": "Druppend kleðerwekstplemt", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON> tw<PERSON><PERSON><PERSON>", "block.minecraft.wheat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_banner": "<PERSON> fana", "block.minecraft.white_bed": "Waitsaingg", "block.minecraft.white_candle": "<PERSON>", "block.minecraft.white_candle_cake": "<PERSON><PERSON>rt min ljuoser i", "block.minecraft.white_carpet": "<PERSON> matta", "block.minecraft.white_concrete": "<PERSON> bit<PERSON>g", "block.minecraft.white_concrete_powder": "<PERSON>ong<PERSON>pulver", "block.minecraft.white_glazed_terracotta": "Waitglasirað terrakotta", "block.minecraft.white_shulker_box": "<PERSON>", "block.minecraft.white_stained_glass": "Waitfle<PERSON>ðg<PERSON>", "block.minecraft.white_stained_glass_pane": "Waitflekkaðglasrut", "block.minecraft.white_terracotta": "Waitt terrakotta", "block.minecraft.white_tulip": "Waittulpan", "block.minecraft.white_wool": "<PERSON> ull", "block.minecraft.wildflowers": "Vildblommor", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Wither bįeraunggelskoll", "block.minecraft.wither_skeleton_wall_skull": "Witherbįeraunggel weggskoll", "block.minecraft.yellow_banner": "<PERSON><PERSON>a", "block.minecraft.yellow_bed": "<PERSON><PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "Tårt min gult liuos", "block.minecraft.yellow_carpet": "<PERSON><PERSON>ta", "block.minecraft.yellow_concrete": "<PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Guolglasirað terrakotta", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Guolflekkaðglasrut", "block.minecraft.yellow_terracotta": "<PERSON><PERSON> terrakot<PERSON>", "block.minecraft.yellow_wool": "<PERSON><PERSON> ull", "block.minecraft.zombie_head": "Sombiovuð", "block.minecraft.zombie_wall_head": "Sombiweggov<PERSON>ð", "book.byAuthor": "åv %1$s", "book.edit.title": "Red<PERSON><PERSON><PERSON> för bok", "book.editTitle": "Seg åv buoktitel:", "book.finalizeButton": "Skriev ą̊ og tepp att", "book.finalizeWarning": "Ärą̊! Bar du skriever ą̊ buotję, går eð it djär um tekstn noð mier.", "book.generation.0": "Orginal", "book.generation.1": "Kopia ov original", "book.generation.2": "Kopia ov kopių", "book.generation.3": "<PERSON><PERSON>", "book.invalid.tag": "Buokmerkningg so ir ųodj<PERSON>un", "book.pageIndicator": "Saiða %1$s åv %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON> sida", "book.page_button.previous": "Föregående sida", "book.sign.title": "Signering<PERSON><PERSON> för bok", "book.sign.titlebox": "Titel", "book.signButton": "Skriev ǫ", "book.view.title": "<PERSON><PERSON><PERSON><PERSON> för bok", "build.tooHigh": "Ogest öjdn fer byggnaðer ir %s", "chat.cannotSend": "Dug it stjikk åv tjatt-meddielend", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Trytj fer te teletransportir", "chat.copy": "<PERSON><PERSON><PERSON> að y<PERSON>", "chat.copy.click": "<PERSON><PERSON>j fer te kopiir að \"urklipp\"", "chat.deleted_marker": "<PERSON><PERSON> chatt<PERSON>delande har raderats av servern.", "chat.disabled.chain_broken": "Chatt har inaktiverats p. g. a. trasig meddelandekedja. Försök återanslut.", "chat.disabled.expiredProfileKey": "<PERSON>tt har inaktiverats då profilens offentliga nyckel upphörde. Försök återansluta.", "chat.disabled.invalid_command_signature": "Kommandot hade en oväntad eller saknad signatur i kommandoargumentet.", "chat.disabled.invalid_signature": "<PERSON>tten hade en ogiltig signatur. Försök att återansluta.", "chat.disabled.launcher": "Chatt har inaktiverats i programstartarens alternativ. Kan inte skicka meddelande.", "chat.disabled.missingProfileKey": "<PERSON>tt har inaktiverats då profilens offentliga nyckel saknas. Försök återansluta.", "chat.disabled.options": "Chatt har inaktiverats i klientens alternativ.", "chat.disabled.out_of_order_chat": "Chattmeddelanden togs emot i fel ordning. Har din systemtid ändra<PERSON>?", "chat.disabled.profile": "Kontoinställningarna tillåter ingen chatt. Tryck på \"%s\" igen för mer information.", "chat.disabled.profile.moreInfo": "Instellningger djär du dug int sją̊ elld stjikk åv mieddielend nų.", "chat.editBox": "<PERSON><PERSON><PERSON>", "chat.filtered": "Filtrerad av servern.", "chat.filtered_full": "<PERSON>n ar gemt mieðdielender dainer fer summ spilerer.", "chat.link.confirm": "Ir du säker du will ev upp nesta web-siaða?", "chat.link.confirmTrusted": "Edd du wilað ev upp isn-jär lettji<PERSON> og djär<PERSON> įe kopi að yrklipp?", "chat.link.open": "Ev upp i browsern", "chat.link.warning": "Du al it ev ev upp lekker frą̊ ien du laiter it ą̊!", "chat.queue": "[+%s räðir so baiðer]", "chat.square_brackets": "[%s]", "chat.tag.error": "<PERSON>n skickade ett ogiltigt meddelande.", "chat.tag.modified": "Meddelandet har modifierats av servern. Original:", "chat.tag.not_secure": "Overifierat meddelande. Kan inte anmälas.", "chat.tag.system": "Servermeddelande. Kan inte anmälas.", "chat.tag.system_single_player": "Servermeddelande.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ar gart rieð autmanindje %s", "chat.type.advancement.goal": "%s ar kummið að målę %s", "chat.type.advancement.task": "%s ar gart framsdtieð %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Meddielend-lag", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s ser %s", "chat.validation_error": "Fel vid validering av chatt", "chat_screen.message": "Meðdielend te stikk åv: %s", "chat_screen.title": "<PERSON><PERSON>j<PERSON><PERSON>", "chat_screen.usage": "Skriev in meðdielend og trytj ą̊ Enter fer te stikk åv", "chunk.toast.checkLog": "Se loggen för mer information", "chunk.toast.loadFailure": "Misslyckades att läsa in datablocket vid %s", "chunk.toast.lowDiskSpace": "<PERSON><PERSON>r lite diskutrymme!", "chunk.toast.lowDiskSpace.description": "Kanske inte kan spara världen.", "chunk.toast.saveFailure": "Misslyckades att spara datablocket vid %s", "clear.failed.multiple": "Itteð it nod föremǫl ǫ %s spilerum", "clear.failed.single": "Itteð int noð föremǫl ǫ spileram %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "Blå", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "Syanferagað", "color.minecraft.gray": "Grå", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Liuosblå", "color.minecraft.light_gray": "Liuosgrå", "color.minecraft.lime": "Limegryön", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.purple": "<PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "Wait", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<--[JENĘ]", "command.context.parse_error": "%s nest posisiuon %s: %s", "command.exception": "Dugd int bigrip kommando: %s", "command.expected.separator": "Iett blankstig war wentað fer te avslut iet argument. men mier data war dar", "command.failed": "Eð wart iett <PERSON>ð fil ą̊ komando", "command.forkLimit": "Maximalt antal sammanhang (%s) har uppnåtts", "command.unknown.argument": "Tuokut argument fer kommando", "command.unknown.command": "Ittað-jär kommando ir ųokunnut elld it komplett, sjǫ filrapportn jär niðoni", "commands.advancement.criterionNotFound": "Kritirieð '%2$s' finnas int i framstię %1$s", "commands.advancement.grant.criterion.to.many.failure": "Eð går it djävå kriterię '%s' fer framstig %s að %s ettersos dier åvå diem rieða", "commands.advancement.grant.criterion.to.many.success": "Spilerer fikk kriterię '%s' fer framstig %s að %s", "commands.advancement.grant.criterion.to.one.failure": "Eð går it djävå kriterię '%s' fer framtig %s að %s ettersos dier åvå diem rieða", "commands.advancement.grant.criterion.to.one.success": "Spilerer fikk kriterię '%s' fer framtig %s að %s", "commands.advancement.grant.many.to.many.failure": "Eð går it djävå framstię %s að %s ettersos dier åvå diem rieða", "commands.advancement.grant.many.to.many.success": "%s spilerer ar faið framstię %s", "commands.advancement.grant.many.to.one.failure": "Eð går nit djävå framstię %s að %s ettersos dier åvå diem rieða", "commands.advancement.grant.many.to.one.success": "Eð ir ok min framstię %s að %s spilerer", "commands.advancement.grant.one.to.many.failure": "Eð går it djävå framstię %s að %s spilerer ettersos dier åvå eð rieða", "commands.advancement.grant.one.to.many.success": "Eð ir ok min framstię %s að %s spilerer", "commands.advancement.grant.one.to.one.failure": "Spilern ar rieð framstię %s að %s", "commands.advancement.grant.one.to.one.success": "Framstieð %s að %s wart bewiljað", "commands.advancement.revoke.criterion.to.many.failure": "Eð djikk int tag att kriterię '%s' fer framstig %s frǫ %s spilerer ettersos dier åvå int eð", "commands.advancement.revoke.criterion.to.many.success": "Tuog att kriterię '%s' fer framstig %s frǫ %s spilerer", "commands.advancement.revoke.criterion.to.one.failure": "Eð djikk int tag att %s kriterię %s fer framstig %s frǫ ettersos dier åvå int eð", "commands.advancement.revoke.criterion.to.one.success": "Tuog att kriterię '%s' fer framstig %s frǫ %s", "commands.advancement.revoke.many.to.many.failure": "<PERSON><PERSON> djikk int tag att %s framstig frǫ %s spilerer ettersos dier åvå int diem", "commands.advancement.revoke.many.to.many.success": "Tuog att framstię %s frǫ %s spilerer", "commands.advancement.revoke.many.to.one.failure": "Eð djikk int tag att %s framstię frǫ %s ettersos dier åvå int eð", "commands.advancement.revoke.many.to.one.success": "Tuog att %s framstię frǫ %s", "commands.advancement.revoke.one.to.many.failure": "Eð djikk int tag att framstię %s frǫ %s spilerer ettersos dier åvå int eð", "commands.advancement.revoke.one.to.many.success": "Tuog att framstię %s frǫ %s spilerer", "commands.advancement.revoke.one.to.one.failure": "Eð djikk int tag att framstię %s frǫ %s ettersos dier åvå int eð", "commands.advancement.revoke.one.to.one.success": "Tuog att framstię %s frǫ %s", "commands.attribute.base_value.get.success": "Grundwärdeð ov attributę %s fer ienietn %s ir %s", "commands.attribute.base_value.reset.success": "Återställde grundvärdet för attributet %s för entiteten %s till standardvärdet %s", "commands.attribute.base_value.set.success": "Grundwärdeð fer attributę %s fer ienietn %s ir bistemmt að %s", "commands.attribute.failed.entity": "%s ir int ien ieniet so ir djiltun fer ittað-jär kommando", "commands.attribute.failed.modifier_already_present": "Modifiirer %s finnas rieð ǫ attributę %s fer ienietn %s", "commands.attribute.failed.no_attribute": "Ienieten %s ar it ingg attribut %s", "commands.attribute.failed.no_modifier": "Attributeð %s fer ienietn %s ar it modifiirern %s", "commands.attribute.modifier.add.success": "Laggd að modifiirern %s að attributę %s fer ienietn %s", "commands.attribute.modifier.remove.success": "Tuog dieðǫ moðifiirern %s frǫ attributę %s fer ienietn %s", "commands.attribute.modifier.value.get.success": "Wärdeð ov moðifiirern %s ǫ attributę %s fer ienietn %s ir %s", "commands.attribute.value.get.success": "Wärdeð fer attributę %s fer ienietn %s ir %s", "commands.ban.failed": "<PERSON><PERSON><PERSON> för, <PERSON><PERSON><PERSON> ir rieð ban<PERSON>", "commands.ban.success": "Bannlyst %s: %s", "commands.banip.failed": "P<PERSON><PERSON> sos för. An-dar <PERSON> ir bannlyst", "commands.banip.info": "Denna bannlysning påverkar %s spelare: %s", "commands.banip.invalid": "Ųodjiltun IP-adress elld <PERSON> spiler", "commands.banip.success": "Bannlyst IP-adressn %s: %s", "commands.banlist.entry": "%s wart bannlyst ov %s: %s", "commands.banlist.entry.unknown": "(Okänd)", "commands.banlist.list": "Det finns %s bannlysningar:", "commands.banlist.none": "<PERSON>ð ir int nog bannly<PERSON>ningger", "commands.bossbar.create.failed": "<PERSON><PERSON> min ID '%s' finns reiða", "commands.bossbar.create.success": "<PERSON><PERSON><PERSON>rd ien anpassað bosmęler %s", "commands.bossbar.get.max": "Anpassaðerbossmęlern %s ar iett makswerd ą̊ %s", "commands.bossbar.get.players.none": "Anpassaðerbossmęlern %s ar it nog spilerer so iro ą̊ trådem nu", "commands.bossbar.get.players.some": "Den anpassade bossmätaren %s har för tillfället %s anslutna spelare: %s", "commands.bossbar.get.value": "Anpassaðerbossmęlern %s ir werd %s", "commands.bossbar.get.visible.hidden": "Anpassaðerbossmęlern %s ir attgemd just nų", "commands.bossbar.get.visible.visible": "Anpassaðerbossmęlern %s waises just nų", "commands.bossbar.list.bars.none": "<PERSON><PERSON> finns int nog anpassaðer bossmęlerer so iro aktiver", "commands.bossbar.list.bars.some": "Det finns %s aktiva anpassade bossmätare: %s", "commands.bossbar.remove.success": "Tuog dieðǫ anpassaðbossmęlern %s", "commands.bossbar.set.color.success": "Anpassaðerbossmęlern %s ar faið ny ferg", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON> sos för. Boss<PERSON><PERSON><PERSON> ar rieð isų-jär färgur", "commands.bossbar.set.max.success": "Anpassaðerbossmęlern %s ar endrad maksimum að %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON> sos för. Ittað-jär ir rieð maks ov Boss<PERSON>ę<PERSON>n", "commands.bossbar.set.name.success": "Anpassaðerbossmęlern %s ar faið nytt nammen", "commands.bossbar.set.name.unchanged": "P<PERSON><PERSON> sos för. Eð-dar ir rieð namneð ą̊ isų-jär <PERSON>n", "commands.bossbar.set.players.success.none": "Anpassaðbossmęlern %s ar it laingger nog spilerer", "commands.bossbar.set.players.success.some": "Den anpassade bossmätaren %s har nu %s spelare: %s", "commands.bossbar.set.players.unchanged": "Prisiss sos för. Issų-jär spilerer iro rieð i bossmęlern og eð gor it legg að eller tag dieðǫ noger", "commands.bossbar.set.style.success": "Anpassaðerbossmęlern %s ar faið ny fasuon", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON> sos för. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n ar rieð issn-jär stiln", "commands.bossbar.set.value.success": "Anpassaðerbossmęlern %s ar endrad värde að %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON> sos för. <PERSON>-<PERSON><PERSON><PERSON> ar rieð ittað-jär wärde", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON> sos för. <PERSON>-<PERSON><PERSON><PERSON> ir rieð attgemd", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON> sos för. <PERSON><PERSON><PERSON><PERSON><PERSON> synes rieða", "commands.bossbar.set.visible.success.hidden": "Anpassaðerbossmęlern %s ir attgemd nų", "commands.bossbar.set.visible.success.visible": "Anpassaðerbossmęlern %s går te sjǫ nų", "commands.bossbar.unknown": "<PERSON><PERSON> finnnes int ingg bossmeler min ID '%s'", "commands.clear.success.multiple": "Tog bort %s föremål från %s spelare", "commands.clear.success.single": "Tog bort %s föremål från spelaren %s", "commands.clear.test.multiple": "Hittade %s matchande föremål på %s spelare", "commands.clear.test.single": "Hittade %s matchande föremål på spelaren %s", "commands.clone.failed": "Int noger blokk wart klonaðer", "commands.clone.overlap": "Kelldområð og målområð får it gå yvyr weroðrer", "commands.clone.success": "Klonade %s block", "commands.clone.toobig": "Uvmikkel blokk i områðę (%s ir maksimalt, %s ir spesifisiraðer)", "commands.damage.invulnerable": "Målet är immunt mot den angivna skadetypen", "commands.damage.success": "Tillämpade %s skada till %s", "commands.data.block.get": "%s ǫblokk %s, %s, %s etter skalninggsfaktro ǫ %s ir %s", "commands.data.block.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir int ien blokkieniet", "commands.data.block.modified": "Endreð blokkdata ov %s,%s,%s", "commands.data.block.query": "%s, %s, %s ovo isų-jär blokkdata: %s", "commands.data.entity.get": "%s ą̊ %s etter skalninggsfaktor ą̊ %s ir %s", "commands.data.entity.invalid": "<PERSON><PERSON><PERSON><PERSON> gor int ender", "commands.data.entity.modified": "Edreð ienietsdata ov %s", "commands.data.entity.query": "%s ar issu-jär ienietsdata: %s", "commands.data.get.invalid": "Dug it fą̊ i %s, bar numerisk etikietter får brukas", "commands.data.get.multiple": "Ittað-jär argumenteð går minn ǫ bar iett NBT-wärde", "commands.data.get.unknown": "F<PERSON>r int i %s; ietikiettn finnes int", "commands.data.merge.failed": "<PERSON><PERSON><PERSON> sos för, and<PERSON><PERSON><PERSON> iegenskaper ar rieð is<PERSON>-jär wärd<PERSON>", "commands.data.modify.expected_list": "Fewenteð lista, fikk %s", "commands.data.modify.expected_object": "Fewenteð föremǫl, fikk: %s", "commands.data.modify.expected_value": "<PERSON>ärde förväntades, hittade: %s", "commands.data.modify.invalid_index": "Ųodjiltut listindeks: %s", "commands.data.modify.invalid_substring": "Ogiltiga index för delsträngar: %s till %s", "commands.data.storage.get": "%s i lagrindjin %s etter skalfaktor ǫ %s ir %s", "commands.data.storage.modified": "Endreð lagringg %s", "commands.data.storage.query": "Laggrindję %s ar ittad-jär i sig: %s", "commands.datapack.create.already_exists": "Ett datapaket med namnet \"%s\" finns redan", "commands.datapack.create.invalid_full_name": "Namnet för det nya paketet \"%s\" är ogiltigt", "commands.datapack.create.invalid_name": "Namnet för det nya datapaketet \"%s\" innehåller ogiltiga tecken", "commands.datapack.create.io_failure": "Kan inte skapa ett datapaket med namnet \"%s\", se loggarna", "commands.datapack.create.metadata_encode_failure": "Misslyckades att koda metadata för paketet med namnet \"%s\": %s", "commands.datapack.create.success": "Skapade ett nytt tomt datapaket med namnet \"%s\"", "commands.datapack.disable.failed": "Pakieted '%s' ir int aktivirad!", "commands.datapack.disable.failed.feature": "Paketet \"%s\" kan inte inaktiveras eftersom det är en del av en aktiverad flagga!", "commands.datapack.enable.failed": "Pakieteð %s ir rieð aktivirað!", "commands.datapack.enable.failed.no_flags": "Paketet \"%s\" kan inte aktiveras eftersom de nödvändiga flaggorna inte har aktiverats i den här världen: %s!", "commands.datapack.list.available.none": "Eð ir int nog mier datapaktiet so gor bruka", "commands.datapack.list.available.success": "Det finns %s tillgängliga datapaket: %s", "commands.datapack.list.enabled.none": "<PERSON>ð ir int nog datapakiet aktiviraðer", "commands.datapack.list.enabled.success": "Det finns %s aktiverade datapaket: %s", "commands.datapack.modify.disable": "Djär so datapakiet %s int funggirer", "commands.datapack.modify.enable": "Djär datapakiet %s aktivt", "commands.datapack.unknown": "Ųokunnut datapakiet '%s'", "commands.debug.alreadyRunning": "Tikkprofilirern ar startad rieða", "commands.debug.function.noRecursion": "Dug it spuorå frǫ inni ien funksiuon", "commands.debug.function.noReturnRun": "Spårning kan inte användas med /return run", "commands.debug.function.success.multiple": "Spårade %s kommandon från %s funktioner till utdatafilen %s", "commands.debug.function.success.single": "Spårade %s kommandon från funktionen \"%s\" till utdatafilen %s", "commands.debug.function.traceFailed": "Eð djikk it spuorå funksiuon", "commands.debug.notRunning": "Tikkprofilirern dugd it byr ą̊", "commands.debug.started": "Evd igangg tikkninggsprofiliringg", "commands.debug.stopped": "Stopppeð tikkninggsprofiliringg etter %s sikunder og %s tikk %s tikk per sikund)", "commands.defaultgamemode.success": "Weld spilermetuoð ir nų %s", "commands.deop.failed": "Pris<PERSON> sos för. <PERSON><PERSON><PERSON> ir it ien operatör", "commands.deop.success": "<PERSON><PERSON><PERSON><PERSON> so %s ir int serveroperatör no laingger", "commands.dialog.clear.multiple": "Rensade dialog för %s spelare", "commands.dialog.clear.single": "Rensade dialog för %s", "commands.dialog.show.multiple": "Visade dialog för %s spelare", "commands.dialog.show.single": "Visade dialog för %s", "commands.difficulty.failure": "Swårigietsgraðn endreðs it, an ir rieð að %s", "commands.difficulty.query": "Swårugietn ir %s", "commands.difficulty.success": "Swårugiet ir bestemd að %s", "commands.drop.no_held_items": "Ienietn dug it olld i nogu föremǫl", "commands.drop.no_loot_table": "Ienietn %s ar it noð ruovbuord", "commands.drop.no_loot_table.block": "Blocket %s har ingen skattabell", "commands.drop.success.multiple": "Sleppt %s föremǫl", "commands.drop.success.multiple_with_table": "Sleppt %s föremǫl frǫ ruovbuordę %s", "commands.drop.success.single": "Slepppt %s %s", "commands.drop.success.single_with_table": "Sleppt %s*%s frǫ ruovbuordę %s", "commands.effect.clear.everything.failed": "Eð finns int noð effekter te tag dieðǫ", "commands.effect.clear.everything.success.multiple": "<PERSON><PERSON> dieðǫ oll effekter frǫ %s mål", "commands.effect.clear.everything.success.single": "<PERSON><PERSON> dieðǫ oll effekter frǫ %s", "commands.effect.clear.specific.failed": "Måleð ar int an effekten so ir bidjärd", "commands.effect.clear.specific.success.multiple": "Tuog diðǫ effekter %s frǫ %s mål", "commands.effect.clear.specific.success.single": "Tuog dieðǫ effekter %s frǫ %s", "commands.effect.give.failed": "<PERSON><PERSON> går int legg til issn-jär effektn (ietdier ir målę immunt elld og ar eð noð so ir starker)", "commands.effect.give.success.multiple": "Brukeð effektn %s að %s måler", "commands.effect.give.success.single": "Brukeð effektn %s að %s", "commands.enchant.failed": "Prisiss sos för. Ietdier ar int måleð ing föremål i nevon elld og går eð int fertryll eð", "commands.enchant.failed.entity": "%s ir int ien ieniet so ir djiltun fer ittað-jär kommando", "commands.enchant.failed.incompatible": "%s styöðer int issu-jär fertryllningg", "commands.enchant.failed.itemless": "%s olld it i noð föremǫl", "commands.enchant.failed.level": "%s ir oger elld maksnivån ov %s so styöðes ov issu-jär fert<PERSON>g", "commands.enchant.success.multiple": "Brukeð fertryllningg %s að %s ienieter", "commands.enchant.success.single": "Brukeð fertryllningg %s að %s's föremǫl", "commands.execute.blocks.toobig": "Uvmikkel blokk i områðę (maksimum %s, du węld %s)", "commands.execute.conditional.fail": "<PERSON><PERSON>ð <PERSON>", "commands.execute.conditional.fail_count": "Tested misslykkeðs: rekken: %s", "commands.execute.conditional.pass": "<PERSON><PERSON><PERSON><PERSON> testeð", "commands.execute.conditional.pass_count": "<PERSON><PERSON><PERSON> klare<PERSON>, rekken: %s", "commands.execute.function.instantiationFailure": "Misslyckades att instantiera funktionen %s: %s", "commands.experience.add.levels.success.multiple": "Gav %s erfarenietsnivåer að %s spilerer", "commands.experience.add.levels.success.single": "Gav %s erfarenietsnivåer að %s", "commands.experience.add.points.success.multiple": "Gav %s erfarenietspoäng að %s spilerer", "commands.experience.add.points.success.single": "Gav %s erfarenietspoäng að %s", "commands.experience.query.levels": "%s ar %s erfarenietsnivåer", "commands.experience.query.points": "%s ar %s erfarenietspoäng", "commands.experience.set.levels.success.multiple": "Bistem %s erfarenietsnivåer ǫ %s spilerer", "commands.experience.set.levels.success.single": "Bistem %s erfarenietsnivåer ǫ %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> går int skeft ą̊ erfarenietspoäng uvo makspoäng an spilern ar", "commands.experience.set.points.success.multiple": "Bistem %s erfarenietspoäng ǫ %s spilerer", "commands.experience.set.points.success.single": "Endreð %s erfarenietspoäng ǫ %s", "commands.fill.failed": "Int noger blokk wart ifyllder", "commands.fill.success": "Fyllde i %s block", "commands.fill.toobig": "Uvmikkel blokk i områðę (maksimum %s, spesifisirað %s)", "commands.fillbiome.success": "Ändrade biomer mellan %s, %s, %s och %s, %s, %s", "commands.fillbiome.success.count": "Ändrade %s biomområden mellan %s, %s, %s och %s, %s, %s", "commands.fillbiome.toobig": "<PERSON><PERSON>r många block i den angivna rymden (%s är maximalt, %s är angivet)", "commands.forceload.added.failure": "Indjin sturbit wart markirað fer twinggaðinlesningg", "commands.forceload.added.multiple": "Markiraðer %s sturbiter i %s frǫ %s að %s werd twinggað<PERSON>lesner", "commands.forceload.added.none": "Int noð datablokk fer twinggaðinleðningg ittes ą̊ i %s", "commands.forceload.added.single": "Markirad sturbit %s i %s werd twinggaðinlesin", "commands.forceload.list.multiple": "%s twingaðleðað datablokk fanns i %s nest: %s", "commands.forceload.list.single": "<PERSON><PERSON> twingaðleðað datablokk fanns i %s nest: %s", "commands.forceload.query.failure": "Datablotjeð nest %s i %s ir int markirað fer twinggad leðningg", "commands.forceload.query.success": "Datablotjeð nest %s i %s ir markirað fer twinggad leðningg", "commands.forceload.removed.all": "Tuog brott markirindję ǫ ollu datablokk fer twinggaðledningg i %s", "commands.forceload.removed.failure": "Inggu datablokk wart brotttaiðner frǫ twinggaðleðningg", "commands.forceload.removed.multiple": "Uomarkiraðer %s datablokk i %s frǫ %s að %s fer twinggaðleðningg", "commands.forceload.removed.single": "Uomarkirað datablokk %s i %s fer twinggaðleðningg", "commands.forceload.toobig": "Uvmikkel datablokk i markiraðområðę (int mier elld %s, spesifisirað %s)", "commands.function.error.argument_not_compound": "\"%s\" är en ogiltig argumenttyp, sammansatt tagg förväntades", "commands.function.error.missing_argument": "Argumentet %2$s saknas till funktionen %1$s", "commands.function.error.missing_arguments": "Argument saknas till funktionen %s", "commands.function.error.parse": "<PERSON>är makrot %s instantierades orsakade kommandot \"%s\" ett fel: %s", "commands.function.instantiationFailure": "Misslyckades att instantiera funktionen %s: %s", "commands.function.result": "Funktionen %s returnerade %s", "commands.function.scheduled.multiple": "K<PERSON>r funk<PERSON>a %s", "commands.function.scheduled.no_functions": "Kan inte hitta någon funktion med namnet %s", "commands.function.scheduled.single": "Kör funk<PERSON>en %s", "commands.function.success.multiple": "Utförde %s kommandon från %s funktioner", "commands.function.success.multiple.result": "Utförde %s funktioner", "commands.function.success.single": "Utförde %s kommandon från funktionen \"%s\"", "commands.function.success.single.result": "Funktionen \"%2$s\" returnerade %1$s", "commands.gamemode.success.other": "Ender spilläge fer %s till %s", "commands.gamemode.success.self": "Bistemm ieget spilsett að %s", "commands.gamerule.query": "Spilriegeln %s ir just nų bistemd að: %s", "commands.gamerule.set": "Spilriegeln %s ir nų bistemd að: %s", "commands.give.failed.toomanyitems": "Går it djävå mier elld %s ov %s", "commands.give.success.multiple": "Gav %s %s að %s spilerer", "commands.give.success.single": "Gav %s %s að %s", "commands.help.failed": "Ųokunnut kommando elld till<PERSON>ånd so byyöves", "commands.item.block.set.success": "Bytt aut ien plass ǫ %s, %s, %s muot %s", "commands.item.entity.set.success.multiple": "Bytt aut ien plass ǫ %s enieter muot %s", "commands.item.entity.set.success.single": "Bytt aut ien plass ǫ %s muot %s", "commands.item.source.no_such_slot": "Kelldų ar int ing spryndja %s", "commands.item.source.not_a_container": "Blotjeð nest %s, %s, %s ir int iett ilåt", "commands.item.target.no_changed.known_item": "Int noð mål akseptireð föremą̊l %s ini spryndjų %s", "commands.item.target.no_changes": "Int noð mål akseptireð föremą̊l ini spryndjų %s", "commands.item.target.no_such_slot": "Måleð ar int ing spryndja %s", "commands.item.target.not_a_container": "Blotjeð nest %s, %s, %s ir it iett il<PERSON>t", "commands.jfr.dump.failed": "JFR-inspelningg djikk int få brott: %s", "commands.jfr.start.failed": "<PERSON><PERSON> djikk int sett igangg JFR-profiliringg", "commands.jfr.started": "<PERSON><PERSON><PERSON> i<PERSON>gg <PERSON>-profiliringg", "commands.jfr.stopped": "JFR-profiuliringg sluteð og fjäll að %s", "commands.kick.owner.failed": "Kan inte sparka ut serverägaren från LAN-spel", "commands.kick.singleplayer.failed": "Kan inte sparka ut spelaren från ett frånkopplat spel i enspelarläget", "commands.kick.success": "Spann%s: %s", "commands.kill.success.multiple": "Eleð %s ienieter", "commands.kill.success.single": "Eleð %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Eð ir %s ov maksimalt %s uppkopplaðspilerer ǫ tråðem: %s", "commands.locate.biome.not_found": "Dug it finn ien miljö ov typn \"%s\" i nerietn", "commands.locate.biome.success": "Närmaste %s finns vid %s (%s block bort)", "commands.locate.poi.not_found": "Dug it finn att ien miljö ov typn \"%s\" i nerietn", "commands.locate.poi.success": "Närmaste %s finns vid %s (%s block bort)", "commands.locate.structure.invalid": "Eð ir int indjin struktur sos \"%s\"", "commands.locate.structure.not_found": "Fannum int att noger struktur sos \"%s\" i närietn", "commands.locate.structure.success": "Nemmest %s ir nest %s (%s blokk laingger brott)", "commands.message.display.incoming": "%s viskar till dig: %s", "commands.message.display.outgoing": "Du viskar till %s: %s", "commands.op.failed": "<PERSON><PERSON><PERSON> sos för. <PERSON><PERSON><PERSON> ir rieð operatör", "commands.op.success": "Djärd %s serveroperatör", "commands.pardon.failed": "Prisiss sos för. <PERSON><PERSON>rn ir it bannlyst", "commands.pardon.success": "%s ir int bannlyst laingger", "commands.pardonip.failed": "Prisiss sos för. Issn-jär IP-adressn ir it bannlyst", "commands.pardonip.invalid": "Ųodjiltun IP-adress", "commands.pardonip.success": "<PERSON><PERSON> dieðo bannlysnindję fer IP-adressn %s", "commands.particle.failed": "Partikeln synes int fer indjin", "commands.particle.success": "Waiser partikeln %s", "commands.perf.alreadyRunning": "Prestandaprofilirern har kumið i gangg rieða", "commands.perf.notRunning": "Prestandaprofilirern ar int kummið i gangg", "commands.perf.reportFailed": "Klareð int ov te djärå lausrapport", "commands.perf.reportSaved": "<PERSON><PERSON><PERSON><PERSON> ien lausrapport i %s", "commands.perf.started": "Evd i gangg ien prestandaprofiliringg ą̊ tiu sikunder (bruk '/perf stop' fer te stan tidugera)", "commands.perf.stopped": "Stoppade prestandaprofileringen efter %s sekunder och %s tickningar (%s tickningar per sekund)", "commands.place.feature.failed": "<PERSON><PERSON> djikk int plasir funksiuon", "commands.place.feature.invalid": "<PERSON>ð ir int indjin funktion åv typn \"%s\"", "commands.place.feature.success": "Add aut \"%s\" nest %s, %s, s%s", "commands.place.jigsaw.failed": "<PERSON><PERSON><PERSON>ð it åv te djärå ien pusselblokk", "commands.place.jigsaw.invalid": "Ed ir int indjin mallpool min typem %s\"", "commands.place.jigsaw.success": "<PERSON><PERSON><PERSON><PERSON> ien pusselblokk nest %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON> djikk int plasir struktur", "commands.place.structure.invalid": "Det finns ingen struktur med typen \"%s\"", "commands.place.structure.success": "<PERSON><PERSON><PERSON><PERSON> struktur \"%s\" nest %s, %s, %s", "commands.place.template.failed": "<PERSON>ð djikk int plasir ien mall", "commands.place.template.invalid": "Ed ir int indjin mall min typem %s\"", "commands.place.template.success": "Leddeð in malln %s nest %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir uvlanggt brott fer te äras", "commands.playsound.success.multiple": "Spileð liuoð %s að %s spilerer", "commands.playsound.success.single": "Spileð liuoð %s að %s", "commands.publish.alreadyPublished": "Flierspilerspil ir rieð igangg ǫ serverport %s", "commands.publish.failed": "Eð djikk it åvå iett lokalt spil", "commands.publish.started": "Lokalt spel ir ǫ port %s", "commands.publish.success": "Flierspilererspil ir nų ǫ port %s", "commands.random.error.range_too_large": "Intervallet får inte vara mindre än 2147483646 slumpmässiga värden", "commands.random.error.range_too_small": "Intervallet får inte vara mindre än 2 slumpmässiga värden", "commands.random.reset.all.success": "Återställde %s slumpmässiga sekvenser", "commands.random.reset.success": "Återställde slumpmässiga sekvensen %s", "commands.random.roll": "%s slog %s (mellan %s och %s)", "commands.random.sample.success": "Slumpmässigt värde: %s", "commands.recipe.give.failed": "Int nog ny risept wart lärder", "commands.recipe.give.success.multiple": "Lęst upp %s risept för %s spilerer", "commands.recipe.give.success.single": "Lęst upp %s risept för %s", "commands.recipe.take.failed": "<PERSON><PERSON> gor int glemm ov inggų resept", "commands.recipe.take.success.multiple": "Tuog %s risept frǫ %s spilerer", "commands.recipe.take.success.single": "Tuog %s risept frǫ %s", "commands.reload.failure": "Eð djikk it leð um et nyes, beolld gamtdata", "commands.reload.success": "Le<PERSON>er um!", "commands.ride.already_riding": "%s är redan placerad på %s", "commands.ride.dismount.success": "%s avlägsnades från %s", "commands.ride.mount.failure.cant_ride_players": "Kan inte placera entiteter på spelare", "commands.ride.mount.failure.generic": "%s kunde inte placeras på %s", "commands.ride.mount.failure.loop": "Kan inte placera entiteten på sig själv eller någon av dess passagerare", "commands.ride.mount.failure.wrong_dimension": "Kan inte placeras på en entitet i en annan dimension", "commands.ride.mount.success": "%s placerades på %s", "commands.ride.not_riding": "%s är inte placerad på någon entitet", "commands.rotate.success": "%s roterades", "commands.save.alreadyOff": "Spårå ir åvstainggd reiða", "commands.save.alreadyOn": "Spårå i rieð igangg", "commands.save.disabled": "<PERSON><PERSON> går int automatspårå nų", "commands.save.enabled": "Nų går eð automatspårå", "commands.save.failed": "Eð djikk int spårå spilę (ir eð autryöm å distjin so eð rekk til?)", "commands.save.saving": "<PERSON><PERSON><PERSON><PERSON> spilę (eð tuol säkert ien styöra)", "commands.save.success": "Spä<PERSON>ð spileð", "commands.schedule.cleared.failure": "Int noð sjema min id %s", "commands.schedule.cleared.success": "Tog bort %s scheman med ID %s", "commands.schedule.created.function": "Schemalade funktionen \"%s\" om %s tickningar vid speltiden %s", "commands.schedule.created.tag": "Sjemalagd etikett '%s' dar %s klikker i spilrtiðę %s", "commands.schedule.macro": "Kan inte schemalägga ett makro", "commands.schedule.same_tick": "Gåt it sjemalegg för issu-jär klik<PERSON>ger", "commands.scoreboard.objectives.add.duplicate": "Eð finns rieð ien gräj min eð-dar namnę", "commands.scoreboard.objectives.add.success": "<PERSON><PERSON><PERSON><PERSON> nytt mǫl %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Pris<PERSON> sos för. Waisninggsplassen ir tuom rieða", "commands.scoreboard.objectives.display.alreadySet": "Pris<PERSON> sos för. Waisninggsplassn waiser eð-ðar föremålę rieða", "commands.scoreboard.objectives.display.cleared": "<PERSON><PERSON><PERSON> dieðǫ ollu mǫl frǫ listun %s", "commands.scoreboard.objectives.display.set": "Bistemmd listų %s fer te wais mǫler %s", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON> ir int ingg<PERSON> mǫl", "commands.scoreboard.objectives.list.success": "Det finns %s mål: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Inaktiverade automatisk uppdatering av visad data för målet %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Aktiverade automatisk uppdatering av visad data för målet %s", "commands.scoreboard.objectives.modify.displayname": "Endreð waisningsnamneð fer %s að %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Rensade standardnummerformatet för målet %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Ändrade standardnummerformatet för målet %s", "commands.scoreboard.objectives.modify.rendertype": "Endreð rendiringstypn ov objekt %s", "commands.scoreboard.objectives.remove.success": "Tuog dieðǫ mǫlę %s", "commands.scoreboard.players.add.success.multiple": "Laggd að %s að %s fer %s ienieter", "commands.scoreboard.players.add.success.single": "Laggd að %s að %s fer %s (nų %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Rensade visningsnamnet för %s entiteter i %s", "commands.scoreboard.players.display.name.clear.success.single": "Rensade visningsnamnet för %s i %s", "commands.scoreboard.players.display.name.set.success.multiple": "Ändrade visningsnamnet för %2$s entiteter i %3$s till %1$s", "commands.scoreboard.players.display.name.set.success.single": "Ändrade visningsnamnet för %2$s i %3$s till %1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Rensade nummerformatet för %s entiteter i %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Rensade nummerformatet för %s i %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Ändrade nummerformatet för %s entiteter i %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Ändrade nummerformatet för %s i %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON> sos för. Avtrykkern ir rieð aktivirað", "commands.scoreboard.players.enable.invalid": "Funggirer bar ą̊ föremǫl ov autlösertyp", "commands.scoreboard.players.enable.success.multiple": "Aktivireð autlösern %s fer %s ienieter", "commands.scoreboard.players.enable.success.single": "Aktivireð autlösern %s fer %s", "commands.scoreboard.players.get.null": "Dug it få i wärde %s fer %s, eð ir int bistemmt", "commands.scoreboard.players.get.success": "%s ar %s %s", "commands.scoreboard.players.list.empty": "<PERSON><PERSON> finns ingg ienieter so iro spåraðer", "commands.scoreboard.players.list.entity.empty": "%s ar int inggu poäng te waisa", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s har %s poäng:", "commands.scoreboard.players.list.success": "Det finns %s spårade entiteter: %s", "commands.scoreboard.players.operation.success.multiple": "Uppdatireð %s fer %s ienieter", "commands.scoreboard.players.operation.success.single": "Stell in %s fer %s að %s", "commands.scoreboard.players.remove.success.multiple": "Tuog dieðǫ %s frǫ %s fer %s ienieter", "commands.scoreboard.players.remove.success.single": "Tuog dieðǫ %s frǫ %s fer %s (nų %s)", "commands.scoreboard.players.reset.all.multiple": "Återstelld ollu päng fer %s ienieter", "commands.scoreboard.players.reset.all.single": "Återstelld ollu poäng fer %s", "commands.scoreboard.players.reset.specific.multiple": "Återstelld %s fer %s ienieter", "commands.scoreboard.players.reset.specific.single": "Återstelld%s fer %s", "commands.scoreboard.players.set.success.multiple": "Bistemm %s fer %s ienieter að %s", "commands.scoreboard.players.set.success.single": "Bistemm %s fer %s að %s", "commands.seed.success": "Frie:%s", "commands.setblock.failed": "Eð djikk int ev dait blotsjeð", "commands.setblock.success": "Endreð blokkę nest %s, %s, %s", "commands.setidletimeout.success": "Tidsgränsen för spelarinaktivitet är nu %s minuter", "commands.setidletimeout.success.disabled": "Tidsgränsen för spelarinaktivitet är nu inaktiverad", "commands.setworldspawn.failure.not_overworld": "Världens återuppståndelsepunkt kan endast ändras till en plats i Övervärlden", "commands.setworldspawn.success": "Bistemm wärdenes umdjäringspunkt að %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Bistemm umdjäringspuktn að %s, %s, %s [%s] i %s for %s spilerer", "commands.spawnpoint.success.single": "Bistemm umdjäringspunktn að %s, %s, %s [%s] i %s fer %s", "commands.spectate.not_spectator": "%s ir it i kuogostellningg", "commands.spectate.self": "Dug it kuoge ǫ dig sjuov", "commands.spectate.success.started": "<PERSON><PERSON><PERSON> n<PERSON> %s", "commands.spectate.success.stopped": "<PERSON><PERSON><PERSON> it <PERSON> ien ieniet la<PERSON>ger", "commands.spreadplayers.failed.entities": "Kunde inte sprida ut %s entiteter runtomkring %s, %s (för många entiteter för utrymmet – försök att sprida ut med minst %s)", "commands.spreadplayers.failed.invalid.height": "Ųodjiltut wärde fer mqaxHeight %s: fikkk luv wårå ogera elld wärdenes minimiöjdn %s", "commands.spreadplayers.failed.teams": "Kunde inte sprida ut %s lag runtomkring %s, %s (för många entiteter för utrymmet – försök att sprida ut med minst %s)", "commands.spreadplayers.success.entities": "Spred ut %s spelare runtomkring %s, %s med ett medelavstånd på %s block if<PERSON><PERSON><PERSON> varandra", "commands.spreadplayers.success.teams": "Spred ut %s lag runtomkring %s, %s med ett medelavstånd på %s block if<PERSON><PERSON><PERSON> varandra", "commands.stop.stopping": "<PERSON><PERSON>n", "commands.stopsound.success.source.any": "Stoppeð ollu '%s' liuoð", "commands.stopsound.success.source.sound": "Stoppeð liuoð '%s' ǫ kelldun '%s'", "commands.stopsound.success.sourceless.any": "Stoppeð ollu liuoð", "commands.stopsound.success.sourceless.sound": "Stoppeð ljuoð '%s'", "commands.summon.failed": "<PERSON><PERSON> djikk int djärå ienieter", "commands.summon.failed.uuid": "Eð djikk it dj<PERSON>rå entitet bar för dyö UUID: er iro laiker", "commands.summon.invalidPosition": "Ųodjiltun plass sambles", "commands.summon.success": "Sambleð ijuop ny %s", "commands.tag.add.failed": "<PERSON><PERSON><PERSON><PERSON> ar ietdier etikiettn elld og uvmikkel etikietter", "commands.tag.add.success.multiple": "Lagd að etikettn '%s' to %s ienieter", "commands.tag.add.success.single": "Lagd að etikettn '%s' to %s", "commands.tag.list.multiple.empty": "Eð ir int nog etikietter ą̊ %s ienieter", "commands.tag.list.multiple.success": "%s ienieter åvå %s total etikietter: %s", "commands.tag.list.single.empty": "%s ar int nog etiketter", "commands.tag.list.single.success": "%s ar %s etikietter: %s", "commands.tag.remove.failed": "M<PERSON><PERSON><PERSON> ar int issn-jär et<PERSON>n", "commands.tag.remove.success.multiple": "Tuog dieðǫ etikiettn '%s' frǫ %s ienieter", "commands.tag.remove.success.single": "Tuog dieðǫ etikettn etikettn '%s' að %s", "commands.team.add.duplicate": "<PERSON>ð finns rieð iett lag min summu nammen", "commands.team.add.success": "Djärd lag %s", "commands.team.empty.success": "Tog bort %s medlemmar från laget %s", "commands.team.empty.unchanged": "P<PERSON><PERSON> sos för. <PERSON><PERSON>-ðar laeð ir rieð tuomt", "commands.team.join.success.multiple": "Laggd að %s mieðlemmer i lag %s", "commands.team.join.success.single": "Laggd að %s i laę %s", "commands.team.leave.success.multiple": "<PERSON><PERSON> dieðǫ %s mieð<PERSON>mer frǫ uonde uk lag", "commands.team.leave.success.single": "<PERSON>og dieðǫ %s frǫ uonde uk lag", "commands.team.list.members.empty": "Eð ir int nog mieðlemmer i laę %s", "commands.team.list.members.success": "Laget %s har %s medlemmar: %s", "commands.team.list.teams.empty": "Eð finns int nogu lag", "commands.team.list.teams.success": "Det finns %s lag: %s", "commands.team.option.collisionRule.success": "Kollisjuonsriegeln fer lag %s ir nų \"%s\"", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON> sos för. Krokkriegler ar rieð eð-ðar wärdeð", "commands.team.option.color.success": "Uppdatireð lagferdję fer lag %s að %s", "commands.team.option.color.unchanged": "Pris<PERSON> sos för. Laeð ar rieð ǫ-ðar fergą", "commands.team.option.deathMessageVisibility.success": "Ur laeð sir dö<PERSON><PERSON><PERSON><PERSON><PERSON> fer laeð %s ir nų \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Prisiss sos för. Synligietn fer dödsmieddelend ar rieð eð-ðar wärdeð", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON> sos för. Eð gor int stjuot kompiser fer eð-ðar laę", "commands.team.option.friendlyfire.alreadyEnabled": "P<PERSON><PERSON> sos för. Eð gor rieð stiuot kompiser fer eð-ðar laę", "commands.team.option.friendlyfire.disabled": "Inaktivired frekåjäldn fer lag %s", "commands.team.option.friendlyfire.enabled": "Aktivireð frekåjäldn fer lag %s", "commands.team.option.name.success": "Feskeð ǫ lagnamneð %s", "commands.team.option.name.unchanged": "Pris<PERSON> sos för. Laeð ar rieð eð-dar namneð", "commands.team.option.nametagVisibility.success": "Ur laeð %s sir nam<PERSON><PERSON><PERSON><PERSON> ir n<PERSON> \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Pris<PERSON> sos för. Synligietn fer namnstylter ar rieð eð-ðar wärdeð", "commands.team.option.prefix.success": "Lagprefiks ir bistemmd að %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Pris<PERSON> sos för. <PERSON><PERSON><PERSON><PERSON><PERSON> laeð dug int sjǫ lagkamratum so iro uosynliger rieða", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Pris<PERSON> sos för. <PERSON><PERSON><PERSON><PERSON><PERSON> laeð dug rieð sjǫ lagkamratuer so iro u<PERSON>ger", "commands.team.option.seeFriendlyInvisibles.disabled": "Lag %s dug int sją̊ ųosynlig lagmiðlemmer laingger", "commands.team.option.seeFriendlyInvisibles.enabled": "Lag %s dug sjå ųosynlig lagmiðlemmer nų", "commands.team.option.suffix.success": "Lagsuffiks ir bistemmd að %s", "commands.team.remove.success": "<PERSON>og dieðǫ lag %s", "commands.teammsg.failed.noteam": "Du får luv wå<PERSON><PERSON> min i iett lag fer te mieðdiel dig að ðiem", "commands.teleport.invalidPosition": "Ųodjiltun plass te teleportir að", "commands.teleport.success.entity.multiple": "Teleportireð %s ienieter að to %s", "commands.teleport.success.entity.single": "Teleportireð %s to %s", "commands.teleport.success.location.multiple": "Teleportired %s ienieter að %s, %s, %s", "commands.teleport.success.location.single": "Teleportired %s að %s, %s, %s", "commands.test.batch.starting": "Startar miljön %s, datamängd %s", "commands.test.clear.error.no_tests": "<PERSON>nde inte hitta något test att rensa", "commands.test.clear.success": "Rensade %s strukturer", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON>licka för att kopiera till urklipp", "commands.test.create.success": "Skapade testkonfigurering för testet %s", "commands.test.error.no_test_containing_pos": "Kan inte hitta en testinstans som innehåller %s, %s, %s", "commands.test.error.no_test_instances": "Hittade inga testinstanser", "commands.test.error.non_existant_test": "Testet %s kunde inte hittas", "commands.test.error.structure_not_found": "Teststrukturen %s kunde inte hittas", "commands.test.error.test_instance_not_found": "Entiteten för testinstansblocket kunde inte hittas", "commands.test.error.test_instance_not_found.position": "Entiteten för testinstansblocket kunde inte hittas under testkörningen vid %s, %s, %s", "commands.test.error.too_large": "Strukturens storlek måste vara mindre än %s block i varje axelriktning", "commands.test.locate.done": "Sökningen slutfördes. %s strukturer hittades", "commands.test.locate.found": "Struktur hittades vid: %s (avstånd: %s)", "commands.test.locate.started": "Startade sökning efter teststrukturer. Detta kan ta en stund...", "commands.test.no_tests": "Inga tester att köra", "commands.test.relative_position": "Position relativ till %s: %s", "commands.test.reset.error.no_tests": "<PERSON>nde inte hitta några tester att återställa", "commands.test.reset.success": "Återställde %s strukturer", "commands.test.run.no_tests": "<PERSON>ga tester hittades", "commands.test.run.running": "<PERSON><PERSON><PERSON> %s tester...", "commands.test.summary": "Speltest slutfördes! %s tester kördes", "commands.test.summary.all_required_passed": "Alla nödvändiga tester genomfördes :)", "commands.test.summary.failed": "%s nödvändiga tester misslyckades :(", "commands.test.summary.optional_failed": "%s valfria tester misslyck<PERSON>", "commands.tick.query.percentiles": "Percentiler: P50: %s ms, P95: %s ms, P99: %s ms, stickprov: %s", "commands.tick.query.rate.running": "Målvärde för tickningshastighet: %s per sekund.\nGenomsnittlig tid per tickning: %s ms (mål: %s ms)", "commands.tick.query.rate.sprinting": "Målvärde för tickningshastighet: %s per sekund (ignoreras, endast för referens).\nGenomsnittlig tid per tickning: %s ms", "commands.tick.rate.success": "Ändrar målvärdet för tickningshastigheten till %s per sekund", "commands.tick.sprint.report": "Sprint slutfördes med %s tickningar per sekund eller %s ms per tickning", "commands.tick.sprint.stop.fail": "Ingen pågående tickningssprint", "commands.tick.sprint.stop.success": "Avbröt den nuvarande tickningssprinten", "commands.tick.status.frozen": "Spelet har frysts", "commands.tick.status.lagging": "Spelet körs men kan inte uppnå målvärdet för tickningshastigheten", "commands.tick.status.running": "Spelet körs normalt", "commands.tick.status.sprinting": "Spelet sprintar", "commands.tick.step.fail": "Kunde inte stega spelet framåt – spelet måste frysas först", "commands.tick.step.stop.fail": "Inget pågående tickningssteg", "commands.tick.step.stop.success": "Avbröt det nuvarande tickningssteget", "commands.tick.step.success": "Stegar framåt %s tickningar", "commands.time.query": "Spiltiðn ir %s", "commands.time.set": "Endreð tiðę að %s", "commands.title.cleared.multiple": "Ren<PERSON>ð titler fer %s spilerer", "commands.title.cleared.single": "Renseð titler fer %s", "commands.title.reset.multiple": "Återstell titelalternativ fer %s spilerer", "commands.title.reset.single": "Återstell titelalternativ fer %s", "commands.title.show.actionbar.multiple": "Waiser ny titel i åtgärdsfelt fer %s spiler", "commands.title.show.actionbar.single": "Waiser ny titel i åtgärdsfelt fer %s", "commands.title.show.subtitle.multiple": "Waiser ny underrubrik fer %s spilerer", "commands.title.show.subtitle.single": "Waiser ny underrubrik fer %s", "commands.title.show.title.multiple": "Waiser ny titel fer %s spilerer", "commands.title.show.title.single": "Waiser ny titel fer %s", "commands.title.times.multiple": "<PERSON><PERSON><PERSON> waisninggstiðę fer titler fer %s spilerer", "commands.title.times.single": "<PERSON><PERSON><PERSON> waisninggstiðę fer titler fer %s", "commands.transfer.error.no_players": "Du måste ange minst en spelare att överföra", "commands.transfer.success.multiple": "Överför %s spelare till %s:%s", "commands.transfer.success.single": "Överför %s till %s:%s", "commands.trigger.add.success": "Löst aut %s (laggd að %s að wärdę)", "commands.trigger.failed.invalid": "Du dug bar autlös fö<PERSON> so iro ov autlösertyp", "commands.trigger.failed.unprimed": "<PERSON> dug int autlös ittað-jär måleð enn", "commands.trigger.set.success": "Löst aut %s (bistemmd wärdę ad %s)", "commands.trigger.simple.success": "Löst aut %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Inga vägpunkter i %s", "commands.waypoint.list.success": "%s vägpunkter i %s: %s", "commands.waypoint.modify.color": "Vägpunktens färg är nu %s", "commands.waypoint.modify.color.reset": "Återställde vägpunktens färg", "commands.waypoint.modify.style": "Vägpunktens stil ändrades", "commands.weather.set.clear": "Stell in weðreð að klårt", "commands.weather.set.rain": "Stell in wedreð ą̊ raingen", "commands.weather.set.thunder": "Bistemm weðreð að rainggnę og åskun", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON> ir waitlist<PERSON><PERSON> rieða", "commands.whitelist.add.success": "%s aðlaggd ǫ waitlistun", "commands.whitelist.alreadyOff": "Waitlistų ir åvstainggd rieða", "commands.whitelist.alreadyOn": "Waitlist<PERSON> ir igangg rieða", "commands.whitelist.disabled": "Waitlistų ir nu åvstainggd", "commands.whitelist.enabled": "Waitlistų ir nų aktivirað", "commands.whitelist.list": "Det finns %s spelare i vitlistan: %s", "commands.whitelist.none": "Eð ir it nog waitlistað spilerer", "commands.whitelist.reloaded": "Leðeð um waitlistų", "commands.whitelist.remove.failed": "Spilern ir it waitlistað", "commands.whitelist.remove.success": "Brotttaið %s frǫ waitlistun", "commands.worldborder.center.failed": "P<PERSON><PERSON> sos för. Wärdsgrensn ir rieð sentrirað ðar", "commands.worldborder.center.success": "Bistemm mittn ov wärdsgrenser að %s, %s", "commands.worldborder.damage.amount.failed": "P<PERSON><PERSON> sos för. Skåðåmaingdn fer wärdsgrensn ar rieð issų-jär main<PERSON>", "commands.worldborder.damage.amount.success": "Bistemm wärdsgrensenes skåðå að %s per blokk werr sikund", "commands.worldborder.damage.buffer.failed": "Pris<PERSON> sos för. Skåðåbuffertn fer wärdsgrensn ar rieð ittað-jär avstondę", "commands.worldborder.damage.buffer.success": "Ändrade världsgränsens skadebuffert till %s block", "commands.worldborder.get": "Världsgränsens bredd är just nu %s block", "commands.worldborder.set.failed.big": "Wärdgrenser beller it wårå styörr elld %s blokk brieddem", "commands.worldborder.set.failed.far": "Wärdsgrensn dug it wårå laingger brott elld %s blokk", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON> sos för. <PERSON><PERSON>rdsgrensn ir rieð son stur", "commands.worldborder.set.failed.small": "Wärdgrensn dug it w<PERSON>r<PERSON> minner elld ett blokk brieð", "commands.worldborder.set.grow": "Oker aut wädsgrensn að %s blokk ǫ brieddem ą̊ %s sikunder", "commands.worldborder.set.immediate": "Ändrade världsgränsens bredd till %s block", "commands.worldborder.set.shrink": "Förminskar världsgränsens bredd till %s block över %s sekunder", "commands.worldborder.warning.distance.failed": "P<PERSON><PERSON> sos för. <PERSON><PERSON>rdsgrenswarnindsję ir rieð eð-ðar avstondę", "commands.worldborder.warning.distance.success": "Ändrade världsgränsens varningsavstånd till %s block", "commands.worldborder.warning.time.failed": "Pris<PERSON> sos för. <PERSON><PERSON><PERSON>grenswarnindsję ir rieð so langg tið", "commands.worldborder.warning.time.success": "Ändrade världsgränsens varningstid till %s sekunder", "compliance.playtime.greaterThan24Hours": "Du ar spiloð i mier elld 24 taim nų", "compliance.playtime.hours": "Du ar spiloð i %s taime(r)", "compliance.playtime.message": "Um du spiler uvmitjið funngirer du int so bra i wenndagsliwę", "connect.aborted": "Åvbruteð", "connect.authorizing": "Logger in...", "connect.connecting": "<PERSON><PERSON><PERSON> ijuop min servern...", "connect.encrypting": "Kryptirer...", "connect.failed": "K<PERSON>eð it åv te koppel ijuop min server", "connect.failed.transfer": "Anslutningen misslyckades vid överföring till servern", "connect.joining": "<PERSON><PERSON><PERSON> min i wärd...", "connect.negotiating": "<PERSON><PERSON><PERSON>...", "connect.reconfiging": "Omkonfigurerar...", "connect.reconfiguring": "Omkonfigurerar...", "connect.transferring": "Överförs till en ny server...", "container.barrel": "<PERSON><PERSON>", "container.beacon": "Fyrbåk", "container.beehive.bees": "Bin: %s/%s", "container.beehive.honey": "Honung: %s/%s", "container.blast_furnace": "Sprainggsmelltungen", "container.brewing": "Bryggstellningg", "container.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.chest": "Tşista", "container.chestDouble": "<PERSON><PERSON><PERSON><PERSON>", "container.crafter": "Tillverkare", "container.crafting": "Tilwerk", "container.creative": "Föremǫlsortiment", "container.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "container.dropper": "<PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapis <PERSON>", "container.enchant.lapis.one": "1 Lapis Lazuli", "container.enchant.level.many": "%s Ugsningsnivåer", "container.enchant.level.one": "Ugsningssnivå", "container.enchant.level.requirement": "Niv<PERSON> an får luv åvå: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Ungen", "container.grindstone_title": "Reparira & tjusa", "container.hopper": "Mojengtratt", "container.inventory": "Autrustningg", "container.isLocked": "%s ir atlęst!", "container.lectern": "<PERSON><PERSON><PERSON>", "container.loom": "Vävstol", "container.repair": "Lågå & namndjäv", "container.repair.cost": "Ugsningg kuoster: %1$s", "container.repair.expensive": "Uvdyrt!", "container.shulkerBox": "Shulkerlåða", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "og %s flierer...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Ry<PERSON>ker", "container.spectatorCantOpen": "Går it ev upp. Ruoveð ir it generirað enn.", "container.stonecutter": "Stįeklipper", "container.upgrade": "Fessk ǫ grejur", "container.upgrade.error_tooltip": "Föremålet kan inte uppgraderas på detta sätt", "container.upgrade.missing_template_tooltip": "Lägg till smidesmall", "controls.keybinds": "Stuttkommandoer...", "controls.keybinds.duplicateKeybinds": "Denna tangent används även för:\n%s", "controls.keybinds.title": "Stuttkommandoer", "controls.reset": "Nollstell", "controls.resetAll": "Nollstell nykkler", "controls.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Warið frek og wel iet biosytiem", "createWorld.customize.buffet.title": "Buffertwärdsanpassning", "createWorld.customize.flat.height": "Öj<PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Lainggst niði - %s", "createWorld.customize.flat.layer.top": "I yvst endam - %s", "createWorld.customize.flat.removeLayer": "Tag dieðå stjipt", "createWorld.customize.flat.tile": "Stjiptmaterial", "createWorld.customize.flat.title": "Sturflatanpassningg", "createWorld.customize.presets": "Mömster", "createWorld.customize.presets.list": "Du edd sakt bellt bruk noger åv diem am gart för!", "createWorld.customize.presets.select": "Bruk mömster", "createWorld.customize.presets.share": "Will du diel malln denn min nogum? <PERSON><PERSON> felteð jär niðonað!", "createWorld.customize.presets.title": "<PERSON>l iet mö<PERSON>ter", "createWorld.preparing": "Olld ǫ förberieð te fǫ til įe wärd...", "createWorld.tab.game.title": "Spel", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "Värld", "credits_and_attribution.button.attribution": "Attribution", "credits_and_attribution.button.credits": "Medverkande", "credits_and_attribution.button.licenses": "Licenser", "credits_and_attribution.screen.title": "Tillskrivningar", "dataPack.bundle.description": "Aktiverar det experimentella påsföremålet", "dataPack.bundle.name": "P<PERSON><PERSON>", "dataPack.locator_bar.description": "Visa i vilken riktning som andra spelare befinner sig i flerspelarläge", "dataPack.locator_bar.name": "Navigeringsfält", "dataPack.minecart_improvements.description": "Förbättrade gruvvagnsrörelser", "dataPack.minecart_improvements.name": "Gruvvagnsförbättringar", "dataPack.redstone_experiments.description": "Experimentella redstoneförändringar", "dataPack.redstone_experiments.name": "Redstoneexperiment", "dataPack.title": "Wel data-pakiet", "dataPack.trade_rebalance.description": "Upp<PERSON><PERSON> byten för bybor", "dataPack.trade_rebalance.name": "Ombalanserade bybobyten", "dataPack.update_1_20.description": "<PERSON><PERSON> funk<PERSON>er och innehåll för Minecraft 1.20", "dataPack.update_1_20.name": "Uppdatering 1.20", "dataPack.update_1_21.description": "<PERSON><PERSON> funk<PERSON>er och innehåll för Minecraft 1.21", "dataPack.update_1_21.name": "Uppdatering 1.21", "dataPack.validation.back": "Gokk etbaker", "dataPack.validation.failed": "Eð djikk it guoðtjenn datapakiet!", "dataPack.validation.reset": "Djär um wärdn að standard", "dataPack.validation.working": "Guoðtjenner weld data-pakiet...", "dataPack.vanilla.description": "Standarddata fer Minecraft", "dataPack.vanilla.name": "Standard", "dataPack.winter_drop.description": "<PERSON><PERSON> funk<PERSON>er och innehåll för vint<PERSON>l<PERSON>t", "dataPack.winter_drop.name": "<PERSON><PERSON>l<PERSON><PERSON>", "datapackFailure.safeMode": "Säkereietsstellningg", "datapackFailure.safeMode.failed.description": "Denna värld innehåller sparad data som är ogiltig eller skadad.", "datapackFailure.safeMode.failed.title": "Misslyckades att läsa in världen i säkert läge.", "datapackFailure.title": "Fel i valda datapaket förhindrade världen från att läsas in.\nDu kan antingen försöka att endast läsa in inbyggda datapaket (\"säkert läge\") eller gå tillbaka till huvudmenyn och åtgärda det manuellt.", "death.attack.anvil": "%1$s wart muosað ov iet follend steðe", "death.attack.anvil.player": "%1$s wart klemmd ov iet follend steð mes an sluoss min %2$s", "death.attack.arrow": "%1$s wart skuotin ov %2$s", "death.attack.arrow.item": "%1$s wart skuotin ov %2$s so brukeð %3$s", "death.attack.badRespawnPoint.link": "Planirað spilautformningg", "death.attack.badRespawnPoint.message": "%1$s wart elað ov %2$s", "death.attack.cactus": "%1$s wart ijelpikkað", "death.attack.cactus.player": "%1$s djikk in i ien kaktus mes an boð til fly frǫ %2$s", "death.attack.cramming": "%1$s Wart uvmitjið klemmd", "death.attack.cramming.player": "%1$s wart klemmd ov %2$s", "death.attack.dragonBreath": "%1$s grillades i drakeld", "death.attack.dragonBreath.player": "%1$s grillades i drakeld av %2$s", "death.attack.drown": "%1$s drukkneð", "death.attack.drown.player": "%1$s drukkneð mes an boð til kumo undǫ %2$s", "death.attack.dryout": "%1$s duo åv auttorkningg", "death.attack.dryout.player": "%1$s duo åv auttorkningg dar an boð till kåyt frą̊ %2$s", "death.attack.even_more_magic": "%1$s wart elað min endo mier trullduom", "death.attack.explosion": "%1$s eksploðireð", "death.attack.explosion.player": "%1$s eksploðired på grund av %2$s", "death.attack.explosion.player.item": "%1$s wart ijelsprainggd ov %2$s so brukeð %3$s", "death.attack.fall": "%1$s sluog i bokkan uvårt", "death.attack.fall.player": "%1$s sluog i bokkan uvårt mes an boð til kum undå %2$s", "death.attack.fallingBlock": "%1$s wart muosað ov iett follend blokk", "death.attack.fallingBlock.player": "%1$s wart muosað ov iet follend blokk mes an sluoss min %2$s", "death.attack.fallingStalactite": "%1$s wart spiessað åv ien stalaktit so fjäll nið", "death.attack.fallingStalactite.player": "%1$s wart spissað ov ien stlaktit så fjäll, mes an rievs min %2$s", "death.attack.fireball": "%1$s wart jäldbollað ov %2$s", "death.attack.fireball.item": "%1$s wart jäldbollað ov %2$s so brukeð %3$s", "death.attack.fireworks": "%1$s feswann min ienum smelle", "death.attack.fireworks.item": "%1$s ekspoðireð min ienum smelle bar för dyö ien rakiet skots iweg frǫ %3$s ov %2$s", "death.attack.fireworks.player": "%1$s feswann min ienum smelle mes an sluos muot %2$s", "death.attack.flyIntoWall": "%1$s war min um brågåkraptn", "death.attack.flyIntoWall.player": "%1$s war min um brågåkraptn mes an boð til kumå undo %2$s", "death.attack.freeze": "%1$s työlåð ijel", "death.attack.freeze.player": "%1$s wart ijeltyölåð åv %2$s", "death.attack.generic": "%1$s duog", "death.attack.generic.player": "%1$s duog på grund åv %2$s", "death.attack.genericKill": "%1$s dödades", "death.attack.genericKill.player": "%1$s dödades under en strid med %2$s", "death.attack.hotFloor": "%1$s wart i warį guoveð war lava", "death.attack.hotFloor.player": "%1$s gick in i farozonen på grund av %2$s", "death.attack.inFire": "%1$s flusseð bar til", "death.attack.inFire.player": "%1$s fuor ini jäldn mes an rievs %2$s", "death.attack.inWall": "%1$s wart kwevd i ien wegg", "death.attack.inWall.player": "%1$s wart kwevd i ien wegg mes an sluoss min %2$s", "death.attack.indirectMagic": "%1$s wart elað ov %2$s so brukeð trullduomin", "death.attack.indirectMagic.item": "%1$s wart elað ov %2$s so brukeð %3$s", "death.attack.lava": "%1$s boð til simm i lavu", "death.attack.lava.player": "%1$s boð til simm i lau fer te kum undą̊ %2$s", "death.attack.lightningBolt": "%1$s wart treffað ov lysun", "death.attack.lightningBolt.player": "%1$s wart treffað ov lysun mes rievs %2$s", "death.attack.mace_smash": "%1$s mosades av %2$s", "death.attack.mace_smash.item": "%1$s mosades av %2$s med %3$s", "death.attack.magic": "%1$s wart elað min trullduomin", "death.attack.magic.player": "%1$s wart elað mes an buoð til kum undǫ %2$s", "death.attack.message_too_long": "Meddelandet var tyvärr för långt för att levereras helt och hållet. Ledsen! Här är en nedkortad version: %s", "death.attack.mob": "%1$s wart elað ov %2$s", "death.attack.mob.item": "%1$s wart elað ov %2$s so brukeð %3$s", "death.attack.onFire": "%1$s ijelbrennd", "death.attack.onFire.item": "%1$s wart brennd að askų mes an rievs min %2$s so add %3$s", "death.attack.onFire.player": "%1$s wart brennd að askų mes an rievs min %2$s", "death.attack.outOfWorld": "%1$s fjäll autyr wärdn", "death.attack.outOfWorld.player": "%1$s willd it livå i summu wärd og %2$s", "death.attack.outsideBorder": "%1$s hamnade utanför världsgränsen", "death.attack.outsideBorder.player": "%1$s hamnade utanför världsgränsen under en strid med %2$s", "death.attack.player": "%1$s wart elað ov %2$s", "death.attack.player.item": "%1$s wart elað ov %2$s so brukeð %3$s", "death.attack.sonic_boom": "%1$s wart autplånað åv iet liuotstarkt skwäl", "death.attack.sonic_boom.item": "%1$s wart autplånað åv iet liutstarkt skwäl mes an boð til kåyt frą̊ %2$s biwäpnað min %3$s", "death.attack.sonic_boom.player": "%1$s wart autplånað åv iet liutstarkt skwäl mes an boð til kåy frą̊ %2$s", "death.attack.stalagmite": "%1$s wart spietsað ą̊ ienum stalagmite", "death.attack.stalagmite.player": "%1$s wart spietsað ą̊ ienum stalagmite mes an rievs min %2$s", "death.attack.starve": "%1$s swalt ijel", "death.attack.starve.player": "%1$s swalt ijel mes an sluoss minn %2$s", "death.attack.sting": "%1$s wart ijelpikkað", "death.attack.sting.item": "Wart ijelpikkað åv %2$s min ien %3$s", "death.attack.sting.player": "%1$s wart ijelpikkað ov %2$s", "death.attack.sweetBerryBush": "%1$s wart pikkad ov ienum syötbersbuosk tast an duog", "death.attack.sweetBerryBush.player": "%1$s wart pikkað tast an duog ov ienum syötbersbuosk mes an boð til te kum undå %2$s", "death.attack.thorns": "%1$s wart elað mes an boð til skåðå %2$s", "death.attack.thorns.item": "%1$s wart elað ov %3$s mes an boð til skåðå %2$s", "death.attack.thrown": "%1$s wart slain ov %2$s", "death.attack.thrown.item": "%1$s wart slain ov %2$s so bruked %3$s", "death.attack.trident": "%1$s wart spietsað ov %2$s", "death.attack.trident.item": "%1$s wart spietsað ov %2$s min %3$s", "death.attack.wither": "%1$s wissneð brott", "death.attack.wither.player": "%1$s wissneð brott mes an sluoss min %2$s", "death.attack.witherSkull": "%1$s wart skuotin åv ienum skoll frǫ %2$s", "death.attack.witherSkull.item": "%1$s wart skuotin åv ienum skoll frǫ %2$s so brukeð %3$s", "death.fell.accident.generic": "%1$s fjäll nið frą̊ ogt upi", "death.fell.accident.ladder": "%1$s tyleð nið frą̊ stikkom", "death.fell.accident.other_climbable": "%1$s fjälll nið mes an kleðreð", "death.fell.accident.scaffolding": "%1$s räseð nið frǫ byggnadsstellnindsjin", "death.fell.accident.twisting_vines": "%1$s fjäll nið frǫ nogu rwaiðend kleðerwekst", "death.fell.accident.vines": "%1$s fjäll nið frą̊ ien kläðerwekst", "death.fell.accident.weeping_vines": "%1$s fjäll nið frǫ non druopend kleðerwekst", "death.fell.assist": "%1$s war dyömd te foll ov %2$s", "death.fell.assist.item": "%1$s wsar dyömd te foll ov %2$s minn %3$s", "death.fell.finish": "%1$s fjäll uvlanggt og wart elað ov %2$s", "death.fell.finish.item": "%1$s fjäll uvlanggt og wart elað ov %2$s minn %3$s", "death.fell.killer": "%1$s war dyömd te folla", "deathScreen.quit.confirm": "Ir ðu säker ðu will sluta?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON> um", "deathScreen.score": "Poengg", "deathScreen.score.value": "Poäng: %s", "deathScreen.spectate": "<PERSON><PERSON>rd te kuogo ą̊", "deathScreen.title": "Du ar däeð!", "deathScreen.title.hardcore": "Spileð ir slut!", "deathScreen.titleScreen": "Hu<PERSON>dmen<PERSON>", "debug.advanced_tooltips.help": "F3 + H = avansirað wertygstips", "debug.advanced_tooltips.off": "Avansirað werktygstips: gemdas", "debug.advanced_tooltips.on": "Avansirað werktygstips: waises", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON>", "debug.chunk_boundaries.off": "Chunkgrenser: gemder", "debug.chunk_boundaries.on": "Chunkgrenser: waisa<PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = Ryökt chat", "debug.copy_location.help": "F3 + C = <PERSON><PERSON><PERSON> plasser sos /tp command, olld nið F3 + C fer te festör spilę", "debug.copy_location.message": "<PERSON><PERSON><PERSON> so iro kop<PERSON>r að yrk<PERSON>p", "debug.crash.message": "Du olld nið F3 + <PERSON><PERSON> kumb te festöras um du slepp int diem.", "debug.crash.warning": "Werd festört um %s...", "debug.creative_spectator.error": "Kunde inte byta spelläge; ingen beh<PERSON>rig<PERSON>t", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON><PERSON><PERSON> mellan föregående spelläge <-> åskådare", "debug.dump_dynamic_textures": "Sparade dynamiska texturer till %s", "debug.dump_dynamic_textures.help": "F3 + S = Dumpa dynamiska texturer", "debug.gamemodes.error": "E djikk it ev upp spilwariantendrer, int noð tillstond", "debug.gamemodes.help": "Ev upp spi<PERSON><PERSON>antendrer", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Nesta", "debug.help.help": "F3 + Q = Wais issu-jär listų", "debug.help.message": "Stuttkommandon:", "debug.inspect.client.block": "Ku<PERSON><PERSON>ð blokkdata frą̊ klientem að yrklipp", "debug.inspect.client.entity": "Ku<PERSON><PERSON>ð i<PERSON>etsdata frą̊ klientem að yrklipp", "debug.inspect.help": "F3 + I = <PERSON><PERSON><PERSON> sak- elld blokkdata að yrklipp", "debug.inspect.server.block": "Kupiiereð blokkdata frą̊ servern að yrklipp", "debug.inspect.server.entity": "Ku<PERSON><PERSON>ð ienietsdata frą̊ servern að yrklipp", "debug.pause.help": "F3 + Esc = Minut auto pausmeny (um eð går minuta)", "debug.pause_focus.help": "F3 + P = minut um du tapper fokus", "debug.pause_focus.off": "Paus bar fokus tappes: yrkopplað", "debug.pause_focus.on": "Paus bar fokus tappes: funggirer", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = start/slut ender profiln", "debug.profiling.start": "Profiliringg started fer %s sikunder. Bruk F3+L fer te sa´nn tidugt", "debug.profiling.stop": "Profiliring åvsluteð. Risultater wart spåråðer i %s", "debug.reload_chunks.help": "F3 + A = Leð um datablokk", "debug.reload_chunks.message": "Leðer um oll chunker", "debug.reload_resourcepacks.help": "F3 + T = Leð um resurspakiet", "debug.reload_resourcepacks.message": "Umleðað<PERSON> r<PERSON>", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON> trefflåður", "debug.show_hitboxes.off": "Trefflåður: gemder", "debug.show_hitboxes.on": "Trefflåða: waisa<PERSON><PERSON>", "debug.version.header": "Klientens versionsinformation:", "debug.version.help": "F3 + V = Klientens versionsinformation", "demo.day.1": "Ittað-jär demo kumb te old i fem spildågå. Dj<PERSON>r best du dug!", "demo.day.2": "Dag twå", "demo.day.3": "Dag tri", "demo.day.4": "<PERSON>g fyra", "demo.day.5": "<PERSON><PERSON><PERSON>-jär ir sienest dan denn!", "demo.day.6": "Du ar spilåð i fem dågå. Bruk %s fer te djärå ien stjärmbild åv wärdn denn.", "demo.day.warning": "Tiðn denn ir slut snart!", "demo.demoExpired": "Eð ir slut ą̊ demo-tiðn!", "demo.help.buy": "Andel nu!", "demo.help.fullWrapped": "Ittað-jär-jär demo wärer fem spildågå (undjefär 1 taim og 40 minuter i tið) Kontrollir framstig fer te få tips! Avið så ruolit!", "demo.help.inventory": "Trytj ą̊ %1$s fer te ev upp för<PERSON><PERSON><PERSON> dett", "demo.help.jump": "Trytj ą̊ %1$s fer te uppa", "demo.help.later": "Fuortsett spilå!", "demo.help.movement": "Bruk %1$s, %2$s, %3$s, %4$s og mausn te fårå runt", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> runt min mausę", "demo.help.movementShort": "Trytj ą̊ %1$s, %2$s, %3$s, %4$s te brågå", "demo.help.title": "Minecraft demoläge", "demo.remainingTime": "Tið so ir kwer: %s", "demo.reminder": "Demo-tidn ar gaið aut, du får luv tyjöp spilę fer te djärå ien ny wärd!", "difficulty.lock.question": "Ir ðu granl säker du will lęs swårig<PERSON>en i issų-jär wärdę? Um du djär så issų-jär wärdę kumb te wårå %1$s, og du dug older ender eð noð mier.", "difficulty.lock.title": "<PERSON><PERSON><PERSON> att wärdswårigiet", "disconnect.endOfStream": "Datatrafitjin åvbrutin", "disconnect.exceeded_packet_rate": "Autspuonn fer te gǫ yvyr datapakietgrensn", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorerar statusbegäran", "disconnect.loginFailedInfo": "Eð djikk it logg in: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Flierspilerstellningg funggirer it, tjäre: sją̊ yvyr instellningger fer Micrsoftkonto dett.", "disconnect.loginFailedInfo.invalidSession": "Issn-j<PERSON>r session djäller it. (<PERSON><PERSON><PERSON> til start spileð dett iessn til og eð-dar so sett igangg programmeð)", "disconnect.loginFailedInfo.serversUnavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON> fer autentisiringg går it kumo að just nų. Bjuoð til et nyes sienera.", "disconnect.loginFailedInfo.userBanned": "Du fą̊r it spilå ą̊ netį noð mier", "disconnect.lost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g feswann", "disconnect.packetError": "Fel i nätverksprotokollet", "disconnect.spam": "Spunn för spam<PERSON>g", "disconnect.timeout": "<PERSON><PERSON> tuold så laindj så du tappeð kontaktn", "disconnect.transfer": "Överförd till en annan server", "disconnect.unknownHost": "<PERSON><PERSON><PERSON><PERSON> wärd", "download.pack.failed": "%s av %s paket misslyckades att laddas ned", "download.pack.progress.bytes": "Status: %s (total storlek okänd)", "download.pack.progress.percent": "Status: %s %%", "download.pack.title": "Laddar ned resurspaket %s/%s", "editGamerule.default": "Standard: %s", "editGamerule.title": "<PERSON><PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Uppsaugningg", "effect.minecraft.bad_omen": "Klient warsel", "effect.minecraft.blindness": "Blindiet", "effect.minecraft.conduit_power": "Fliuotendkrapt", "effect.minecraft.darkness": "Mörknę", "effect.minecraft.dolphins_grace": "Delfineres elegans", "effect.minecraft.fire_resistance": "Jäldmųotstond", "effect.minecraft.glowing": "Glyöðend", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Okningg ov elsų", "effect.minecraft.hero_of_the_village": "Bysjelte", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_damage": "Skåðå rað weg", "effect.minecraft.instant_health": "<PERSON> rað weg", "effect.minecraft.invisibility": "Ųosynligiet", "effect.minecraft.jump_boost": "Uppförbettringg", "effect.minecraft.levitation": "Te fliuoga", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON> eter gru<PERSON>t", "effect.minecraft.nausea": "Ųolågåð", "effect.minecraft.night_vision": "S<PERSON>ǫ i mörknę", "effect.minecraft.oozing": "Slemmig", "effect.minecraft.poison": "Dşipt", "effect.minecraft.raid_omen": "Föraning om räd", "effect.minecraft.regeneration": "Kumå til et nyes", "effect.minecraft.resistance": "<PERSON><PERSON>otstond", "effect.minecraft.saturation": "Mettnað", "effect.minecraft.slow_falling": "Foll små'tt", "effect.minecraft.slowness": "Makkligiet", "effect.minecraft.speed": "Astugiet", "effect.minecraft.strength": "Styrka", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON> om utmaning", "effect.minecraft.unluck": "Ųotur", "effect.minecraft.water_breathing": "<PERSON> niði wattnę", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Vindladdad", "effect.minecraft.wither": "<PERSON><PERSON>", "effect.none": "<PERSON><PERSON> effekter", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Laik wattneð", "enchantment.minecraft.bane_of_arthropods": "Djįpt", "enchantment.minecraft.binding_curse": "Ugsningg um bindningg", "enchantment.minecraft.blast_protection": "Stj<PERSON>d muot eksplosiuon", "enchantment.minecraft.breach": "Genombrytning", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON><PERSON> rennur", "enchantment.minecraft.density": "Densitet", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Effektivitiet", "enchantment.minecraft.feather_falling": "<PERSON><PERSON><PERSON> fjäðer", "enchantment.minecraft.fire_aspect": "Jäldautsdjåend", "enchantment.minecraft.fire_protection": "Jäldstjydd", "enchantment.minecraft.flame": "Lugå", "enchantment.minecraft.fortune": "Raitjduom", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "Pikk ini", "enchantment.minecraft.infinity": "Ųoendligiet", "enchantment.minecraft.knockback": "Rekå", "enchantment.minecraft.looting": "Plundringg", "enchantment.minecraft.loyalty": "Truogeniet", "enchantment.minecraft.luck_of_the_sea": "Fistjelukk", "enchantment.minecraft.lure": "Biete", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "Djärå uol i kruppem", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON> muot prosektil", "enchantment.minecraft.protection": "Stjydd", "enchantment.minecraft.punch": "Slag", "enchantment.minecraft.quick_charge": "Snabbladdningg", "enchantment.minecraft.respiration": "Andningg", "enchantment.minecraft.riptide": "Tiðwattuwåg", "enchantment.minecraft.sharpness": "<PERSON>r wasst eð ir", "enchantment.minecraft.silk_touch": "Tag i warlit", "enchantment.minecraft.smite": "St<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "<PERSON>r straitt själn far", "enchantment.minecraft.sweeping": "Swepend swärdsegg", "enchantment.minecraft.sweeping_edge": "Svepande svärdsegg", "enchantment.minecraft.swift_sneak": "Strai'tt stjälend", "enchantment.minecraft.thorns": "Tagg", "enchantment.minecraft.unbreaking": "Far it sund", "enchantment.minecraft.vanishing_curse": "Ugsningg för te förswinna", "enchantment.minecraft.wind_burst": "Vindexplosion", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_chest_boat": "Akaciabåt med kista", "entity.minecraft.allay": "<PERSON><PERSON><PERSON><PERSON> bö<PERSON>ö<PERSON>", "entity.minecraft.area_effect_cloud": "Areaeffektmuoln", "entity.minecraft.armadillo": "Bältdjur", "entity.minecraft.armor_stand": "Rustninggsstellningg", "entity.minecraft.arrow": "<PERSON><PERSON>", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambuflotte med kista", "entity.minecraft.bamboo_raft": "Bambuflotte", "entity.minecraft.bat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Bia", "entity.minecraft.birch_boat": "Björkbåt", "entity.minecraft.birch_chest_boat": "Björkbåt med kista", "entity.minecraft.blaze": "Lugå", "entity.minecraft.block_display": "Blockvisare", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON> skelett", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON>tta<PERSON>", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Gr<PERSON>ttdjörg", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON>bärsbåt med kista", "entity.minecraft.chest_boat": "<PERSON><PERSON>t med kista", "entity.minecraft.chest_minecart": "Gruvvangen min tjistu", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Tosk", "entity.minecraft.command_block_minecart": "Gruvvangen min kommandoblock", "entity.minecraft.cow": "Tşyr", "entity.minecraft.creaking": "Knarrträd", "entity.minecraft.creaking_transient": "Knarrträd", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Mörk <PERSON>", "entity.minecraft.dark_oak_chest_boat": "Mörk ekbåt med kista", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "Åsna", "entity.minecraft.dragon_fireball": "Drakjäldboll", "entity.minecraft.drowned": "Drukknað", "entity.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.elder_guardian": "Gamblinggswakt", "entity.minecraft.end_crystal": "Endkristall", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> end<PERSON>l", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Fą̊ fram", "entity.minecraft.evoker_fangs": "Fą̊ framm oggtenner", "entity.minecraft.experience_bottle": "Fertryllninggsputell so ir windað", "entity.minecraft.experience_orb": "Erfarenietsboll", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Follendblokk", "entity.minecraft.falling_block_type": "%s som faller", "entity.minecraft.fireball": "Jäldboll", "entity.minecraft.firework_rocket": "Fyrwerkerirakiet", "entity.minecraft.fishing_bobber": "Fiskflåtå", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Tuosk", "entity.minecraft.furnace_minecart": "Gruvvangen min smeltungen", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Risi", "entity.minecraft.glow_item_frame": "Glyöðend föremǫlsram", "entity.minecraft.glow_squid": "Glyöðend blikfisk", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Biwaker", "entity.minecraft.happy_ghast": "<PERSON> ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Gruvwangen min tratt", "entity.minecraft.horse": "Est", "entity.minecraft.husk": "Husksombi", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "entity.minecraft.interaction": "Interaktiv entitet", "entity.minecraft.iron_golem": "Ienngolem", "entity.minecraft.item": "<PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Föremålsvisare", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Djungelbåt", "entity.minecraft.jungle_chest_boat": "Djungelbåt med kista", "entity.minecraft.killer_bunny": "M<PERSON><PERSON>rerǫ", "entity.minecraft.leash_knot": "Rakkbandsknaut", "entity.minecraft.lightning_bolt": "Bliksterstįin", "entity.minecraft.lingering_potion": "Kvardröjande brygd", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_boat": "Mangrovebåt", "entity.minecraft.mangrove_chest_boat": "Mangrovebåt med kista", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Gruvwangen", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Åsna", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "Ekbåt med kista", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Oroväckande föremålsskapare", "entity.minecraft.painting": "<PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Blek e<PERSON>båt", "entity.minecraft.pale_oak_chest_boat": "Blek ekbåt med kista", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglinfugat", "entity.minecraft.pillager": "<PERSON>lund<PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Aisbyönn", "entity.minecraft.potion": "Drykk", "entity.minecraft.pufferfish": "Pufferfisk", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Orað", "entity.minecraft.sheep": "Soð", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Silverfisk", "entity.minecraft.skeleton": "Skelett", "entity.minecraft.skeleton_horse": "Skelettest", "entity.minecraft.slime": "Kliem", "entity.minecraft.small_fireball": "Litn jäldboll", "entity.minecraft.sniffer": "Sniffare", "entity.minecraft.snow_golem": "Sniuogolem", "entity.minecraft.snowball": "Sniųoboll", "entity.minecraft.spawner_minecart": "Gruvvagn med monsterskapare", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spider": "Dyörg", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON> brygd", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "Granbåt med kista", "entity.minecraft.squid": "Blekkfisk", "entity.minecraft.stray": "<PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Textvisare", "entity.minecraft.tnt": "Fylld ą̊ TNT", "entity.minecraft.tnt_minecart": "Gruvwangen min TNT", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.trident": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish": "Tropisk fisk", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "<PERSON>wart t<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.10": "Morisk idol", "entity.minecraft.tropical_fish.predefined.11": "Krusað fyöryolderfisk", "entity.minecraft.tropical_fish.predefined.12": "Papegojfisk", "entity.minecraft.tropical_fish.predefined.13": "Drottningsavsaindjel", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON> siklið", "entity.minecraft.tropical_fish.predefined.15": "Roðleppað Blenny", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON> baiter", "entity.minecraft.tropical_fish.predefined.17": "Trådfien", "entity.minecraft.tropical_fish.predefined.18": "Tomatclownfisk", "entity.minecraft.tropical_fish.predefined.19": "Trykkerfisk", "entity.minecraft.tropical_fish.predefined.2": "Blå tjirurg", "entity.minecraft.tropical_fish.predefined.20": "Papegojfisk min guolrump", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "Fyörolderfisk", "entity.minecraft.tropical_fish.predefined.4": "Ciklið", "entity.minecraft.tropical_fish.predefined.5": "Klownfisk", "entity.minecraft.tropical_fish.predefined.6": "Blårosa kampfisk", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> baiter", "entity.minecraft.tropical_fish.predefined.9": "Djįetfisk", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blokkfisk", "entity.minecraft.tropical_fish.type.brinely": "Saltfisk", "entity.minecraft.tropical_fish.type.clayfish": "Lierfisk", "entity.minecraft.tropical_fish.type.dasher": "Skwekter", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitterfisk", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Brygdpspjäti", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Randugfisk", "entity.minecraft.tropical_fish.type.sunstreak": "Suolstrik", "entity.minecraft.turtle": "Stjöldkluoss", "entity.minecraft.vex": "Biswerskupp", "entity.minecraft.villager": "Byskall", "entity.minecraft.villager.armorer": "Rustninggsmið", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON>st", "entity.minecraft.villager.farmer": "Buonde", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Kuovmäker", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotieksarbieter", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Byskall", "entity.minecraft.villager.shepherd": "G<PERSON><PERSON>lkall", "entity.minecraft.villager.toolsmith": "Andkuostsmið", "entity.minecraft.villager.weaponsmith": "Wapensmið", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Wak<PERSON>", "entity.minecraft.wind_charge": "Vindladdning", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherbieraunggel", "entity.minecraft.wither_skull": "Witherskolle", "entity.minecraft.wolf": "<PERSON>g", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Sombiest", "entity.minecraft.zombie_villager": "Sombibyskall", "entity.minecraft.zombified_piglin": "Sombifiirað piglin", "entity.not_summonable": "Kan inte skapa entitet av typen %s", "event.minecraft.raid": "Invaðira", "event.minecraft.raid.defeat": "Felust", "event.minecraft.raid.defeat.full": "Räd – förlust", "event.minecraft.raid.raiders_remaining": "Invadirerer so iro kwere: %s", "event.minecraft.raid.victory": "Sieger", "event.minecraft.raid.victory.full": "R<PERSON><PERSON> – seger", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "filled_map.explorer_jungle": "Jungelutforskarkarta", "filled_map.explorer_swamp": "Träskutforskarkarta", "filled_map.id": "Id #%s", "filled_map.level": "(Nivå %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON>", "filled_map.mansion": "<PERSON>rt yvyr sku<PERSON>um", "filled_map.monument": "<PERSON>rt yvyr avum", "filled_map.scale": "Festuror að 1:%s", "filled_map.trial_chambers": "Utmaningsutforskarkarta", "filled_map.unknown": "Uokunnug karta", "filled_map.village_desert": "Ökenbykarta", "filled_map.village_plains": "Slättbykarta", "filled_map.village_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_snowy": "Snöbykarta", "filled_map.village_taiga": "<PERSON>j<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.bottomless_pit": "Buottnlost uol", "flat_world_preset.minecraft.classic_flat": "Klassisk flat", "flat_world_preset.minecraft.desert": "Öken", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "<PERSON><PERSON><PERSON><PERSON> fer Reds<PERSON>", "flat_world_preset.minecraft.snowy_kingdom": "Sniųoritje", "flat_world_preset.minecraft.the_void": "Snǫðligiet", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON><PERSON> dröm", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "Äventyrsstellningg", "gameMode.changed": "Spilstellnindsję dai ir uppgraðirað að %s", "gameMode.creative": "Kreativstellningg", "gameMode.hardcore": "Hardcore-stellningg!", "gameMode.spectator": "Åskåðerstellningg", "gameMode.survival": "Yvyrlievnaðsstellningg", "gamerule.allowFireTicksAwayFromPlayer": "<PERSON><PERSON><PERSON> eld sprida sig på avstånd från spelare", "gamerule.allowFireTicksAwayFromPlayer.description": "Styr huruvida eld kan sprida sig på avstånd längre bort än 8 datablock från en spelare", "gamerule.announceAdvancements": "<PERSON><PERSON> frams<PERSON>", "gamerule.blockExplosionDropDecay": "Förlora föremål vid explosioner orsakade av blockinteraktioner", "gamerule.blockExplosionDropDecay.description": "När block förstörs av explosioner orsakade av blockinteraktioner förloras en del av föremålen i explosionen.", "gamerule.category.chat": "<PERSON><PERSON>", "gamerule.category.drops": "<PERSON><PERSON><PERSON>", "gamerule.category.misc": "Blandat", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Te skapa", "gamerule.category.updates": "Fesk ǫ wärder", "gamerule.commandBlockOutput": "Wais autdata frǫ kommandoblokk", "gamerule.commandModificationBlockLimit": "Blockgräns för terrängmodifierande kommandon", "gamerule.commandModificationBlockLimit.description": "Det maximala antalet block som kan ändras med ett kommando samtidi<PERSON>, t. ex. /fill eller /clone.", "gamerule.disableElytraMovementCheck": "Staindj ov rörelsekontroll fer elytra", "gamerule.disablePlayerMovementCheck": "Inaktivera rörelsekontroll för spelare", "gamerule.disableRaids": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> räðer", "gamerule.doDaylightCycle": "Te kumo laingger fram ǫ daem", "gamerule.doEntityDrops": "Slepp inietsautrustningg", "gamerule.doEntityDrops.description": "Kont<PERSON>irer um gruvwangen tapper (iklusive inventarier), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON> og ymse.", "gamerule.doFireTick": "Fesk ǫ jäldn", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON>r um dig rað weg", "gamerule.doInsomnia": "<PERSON>j<PERSON>r um spötjer", "gamerule.doLimitedCrafting": "<PERSON><PERSON><PERSON> til du får risept fer te djärǫ grejur", "gamerule.doLimitedCrafting.description": "Om detta aktiveras kommer spelare endast kunna tillverka med upplåsta recept.", "gamerule.doMobLoot": "S<PERSON>pp röverbyte", "gamerule.doMobLoot.description": "<PERSON><PERSON> om du får resurser frå<PERSON> var<PERSON>, inklusive erfarenhetsklot.", "gamerule.doMobSpawning": "Tillwerk fugater", "gamerule.doMobSpawning.description": "En del entiteter kan ha separata regler.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON><PERSON> plund<PERSON><PERSON><PERSON><PERSON>er", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON> blokk", "gamerule.doTileDrops.description": "Styr om du får resurser från block, inklusive erfarenhetsklot.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doVinesSpread": "<PERSON><PERSON><PERSON> k<PERSON>ätterväxter sprida sig", "gamerule.doVinesSpread.description": "Styr huruvida klätterväxter slumpmässigt kan sprida sig till intilliggande block eller inte. Detta påverkar inte andra typer av klätterväxter, t. ex. tårklätterväxter, vridande klätterväxter etc.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON><PERSON> wakter", "gamerule.doWeatherCycle": "Fesk ǫ wedreð", "gamerule.drowningDamage": "<PERSON><PERSON> bar an drukkner", "gamerule.enderPearlsVanishOnDeath": "Kastade enderpärlor försvinner vid dödsfall", "gamerule.enderPearlsVanishOnDeath.description": "Huruvida enderpärlor som kastats av en spelare försvinner när spelaren dör.", "gamerule.entitiesWithPassengersCanUsePortals": "Entiteter med passagerare får använda portaler", "gamerule.entitiesWithPassengersCanUsePortals.description": "<PERSON><PERSON><PERSON> entiteter med passagerare teleporteras med netherportaler, endportaler eller endpassager.", "gamerule.fallDamage": "<PERSON><PERSON> foll-s<PERSON><PERSON><PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON><PERSON> spilerer so iro doðer", "gamerule.forgiveDeadPlayers.description": "Neutral figurer so ir<PERSON> uppfr<PERSON><PERSON><PERSON>er sluter wår<PERSON> jä<PERSON><PERSON>ker dar spilern dir jag där i nerietn.", "gamerule.freezeDamage": "<PERSON><PERSON>", "gamerule.globalSoundEvents": "Spela upp händelseljud globalt", "gamerule.globalSoundEvents.description": "Ljud från vissa s<PERSON><PERSON>, t. ex. när en boss ska<PERSON>, spelas upp överallt i världen.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON> fö<PERSON>åð etter doðn", "gamerule.lavaSourceConversion": "<PERSON><PERSON>t lava skapa källblock", "gamerule.lavaSourceConversion.description": "När rinnande lava omges av lavakällor från två sidor omvandlas blocket till en källa.", "gamerule.locatorBar": "Aktivera navigeringsfält för spelare", "gamerule.locatorBar.description": "<PERSON><PERSON>r detta aktiveras visar en mätare på skärmen i vilken riktning som andra spelare befinner sig.", "gamerule.logAdminCommands": "Wais admin-kommando", "gamerule.maxCommandChainLength": "Wais grensn fer sturlietjin ǫ tjaiun", "gamerule.maxCommandChainLength.description": "G<PERSON>ller ihopkopplade kommandoblock och funktioner.", "gamerule.maxCommandForkCount": "<PERSON><PERSON><PERSON><PERSON> för kom<PERSON>", "gamerule.maxCommandForkCount.description": "Maximalt antal sammanhang som kan användas av kommandon som \"execute as\".", "gamerule.maxEntityCramming": "So mikkel ienieter ryömes sos mjäst", "gamerule.minecartMaxSpeed": "<PERSON><PERSON> has<PERSON> för <PERSON>", "gamerule.minecartMaxSpeed.description": "Maximal standardhastighet en gruvvagn kan uppnå på land", "gamerule.mobExplosionDropDecay": "Förlora föremål vid explosioner orsakade av varelser", "gamerule.mobExplosionDropDecay.description": "När block förstörs av explosioner orsakade av varelser förloras en del av föremålen i explosionen.", "gamerule.mobGriefing": "Lat fugater djärå slaikt so ir destruktivt", "gamerule.naturalRegeneration": "Fesk ǫ elsu", "gamerule.playersNetherPortalCreativeDelay": "Netherportalsfördröjning för spelare som är i kreativt läge", "gamerule.playersNetherPortalCreativeDelay.description": "Tid (i tickningar) en spelare som är i kreativt läge måste stå i en Netherportal innan spelaren färdas till den andra dimensionen.", "gamerule.playersNetherPortalDefaultDelay": "Netherportalsfördröjning för spelare som inte är i kreativt läge", "gamerule.playersNetherPortalDefaultDelay.description": "Tid (i tickningar) en spelare som inte är i kreativt läge måste stå i en Netherportal innan spelaren byter dimension.", "gamerule.playersSleepingPercentage": "<PERSON>we<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.playersSleepingPercentage.description": "<PERSON><PERSON> mikkel prosent spilerer so fą̊r luv såvå te kumb frą̊ notę.", "gamerule.projectilesCanBreakBlocks": "Projektiler kan ta sönder block", "gamerule.projectilesCanBreakBlocks.description": "Styr huruvida projektilers nedslag kan ta sönder block som går att ha sönder.", "gamerule.randomTickSpeed": "Int noð bistemt ur strait eð al tikkas", "gamerule.reducedDebugInfo": "Minsk nið ǫ åvlausinformasiuon", "gamerule.reducedDebugInfo.description": "Begränsar innehållet på felsökningsskärmen.", "gamerule.sendCommandFeedback": "Stjikk iweg swar ǫ kommando", "gamerule.showDeathMessages": "Wais mieðdielend um ðoðą", "gamerule.snowAccumulationHeight": "<PERSON><PERSON> antal ansa<PERSON> sn<PERSON>lager", "gamerule.snowAccumulationHeight.description": "Det maximala antalet snölager som kan bildas på marken när det snöar.", "gamerule.spawnChunkRadius": "Radie för datablocksinläsning vid världens återuppståndelsepunkt", "gamerule.spawnChunkRadius.description": "Antalet datablock som förblir inlästa runtomkring Övervärldens återuppståndelsepunkt.", "gamerule.spawnRadius": "Ra<PERSON><PERSON> fer återupplivningspunkt", "gamerule.spawnRadius.description": "Styr storleken på området runtomkring återuppståndelsepunkten som spelare kan uppstå i.", "gamerule.spectatorsGenerateChunks": "Lat åskåðåðer te djärå terräng", "gamerule.tntExplodes": "<PERSON><PERSON><PERSON> dynamit <PERSON><PERSON><PERSON> och <PERSON>ra", "gamerule.tntExplosionDropDecay": "Förlora föremål vid explosioner orsakade av dynamit", "gamerule.tntExplosionDropDecay.description": "När block förstörs av explosioner orsakade av dynamit förloras en del av föremålen i explosionen.", "gamerule.universalAnger": "Universal ilska", "gamerule.universalAnger.description": "Neutral figurer so iro uppfrǫaðer anfoll oll spilrer i nerietn, it bar diem so ovo froað upp diem. Ittað-jär funggirer best um eð gor it felåt doð spilerer.", "gamerule.waterSourceConversion": "<PERSON><PERSON><PERSON> vatten skapa källblock", "gamerule.waterSourceConversion.description": "<PERSON><PERSON>r rinnande vatten omges av vattenkällor från två sidor omvandlas blocket till en källa.", "generator.custom": "Spesialgard", "generator.customized": "G<PERSON>m<PERSON><PERSON> an<PERSON>", "generator.minecraft.amplified": "Festurað", "generator.minecraft.amplified.info": "Obs! Bar för ð<PERSON> eð i ruolit, an får luv åvå ien starkan dator.", "generator.minecraft.debug_all_block_states": "Lausstellningg", "generator.minecraft.flat": "Superflå", "generator.minecraft.large_biomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.normal": "Standard", "generator.minecraft.single_biome_surface": "<PERSON><PERSON><PERSON><PERSON>l<PERSON>", "generator.single_biome_caves": "Grottor", "generator.single_biome_floating_islands": "Flju<PERSON>nd äer", "gui.abuseReport.attestation": "Genom att skicka in denna anmälan bekräftar du att den information du har lämnat är korrekt och fullständig efter din bästa förmåga.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Detaljer hjälper oss att ta välgrundade beslut.", "gui.abuseReport.discard.content": "Om du lämnar kommer den här anmälan och dina kommentarer att gå förlorade.\nÄr du säker på att du vill lämna?", "gui.abuseReport.discard.discard": "Lämna och släng anmälan", "gui.abuseReport.discard.draft": "Spara som utkast", "gui.abuseReport.discard.return": "Fortsätt redigera", "gui.abuseReport.discard.title": "Släng anm<PERSON>lan och kommentarer?", "gui.abuseReport.draft.content": "Vill du fortsätta att redigera utkastet till anmä<PERSON> eller slänga det och skapa ett nytt?", "gui.abuseReport.draft.discard": "Släng", "gui.abuseReport.draft.edit": "Fortsätt redigera", "gui.abuseReport.draft.quittotitle.content": "Vill du fortsätta att redigera eller slänga det?", "gui.abuseReport.draft.quittotitle.title": "Du har ett utkast till en chattanmälan som kommer att gå förlorat om du lämnar", "gui.abuseReport.draft.title": "Redigera utkast till chattanmälan?", "gui.abuseReport.error.title": "Finggum probliem stjikk åv rapportn den", "gui.abuseReport.message": "Var observerade du det dåliga beteendet?\nDetta kommer att hjälpa oss att undersöka ditt ärende.", "gui.abuseReport.more_comments": "Beskriv vad som har inträffat:", "gui.abuseReport.name.comment_box_label": "Besk<PERSON>v varför du vill anmäla detta namn:", "gui.abuseReport.name.reporting": "<PERSON> håller på att anmäla \"%s\".", "gui.abuseReport.name.title": "An<PERSON><PERSON><PERSON> olämpligt spelar<PERSON>", "gui.abuseReport.observed_what": "Var<PERSON><PERSON>r vill du an<PERSON>la detta?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON> mer om an<PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON><PERSON><PERSON> el<PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Noger biuoð til fą̊ min sig flierum undduomum te old ą̊ min drogum elld alkohol.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Seksuellt autnyttjend åv elld övergepp muot krippą", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Noger föreslår noð um krippą wið likum it.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Fetal, identitetsstöld elld falskinformasiuon", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Noger språker klient um dig elld um någum eller, luss wår<PERSON> nån an ir int elld dieler falskauppdjiptą.", "gui.abuseReport.reason.description": "Beskrievningg:", "gui.abuseReport.reason.false_reporting": "Tuokug rapportiringg", "gui.abuseReport.reason.generic": "Jag vill anmäla dem", "gui.abuseReport.reason.generic.description": "Jag är irriterad på dem / de har gjort någonting som jag inte tycker om.", "gui.abuseReport.reason.harassment_or_bullying": "Trakassiringg elld mobbningg", "gui.abuseReport.reason.harassment_or_bullying.description": "Noger far klient min dig elld min någum eller. Elld um eð ir noger so bjiuoð til fą̊ i dig, elld någum eller, mikkel gaungga elld dieler priwat uppdjipta um dig ell um någum eller, autå te fą̊ luv (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Glam um at", "gui.abuseReport.reason.hate_speech.description": "Noger fliuog ą̊ dig elld noger eller um guðeligiet, identitet, ras elld seksualitiet.", "gui.abuseReport.reason.imminent_harm": "Akut uot elld um eð ir noger so uoter någum", "gui.abuseReport.reason.imminent_harm.description": "Noger uoter te djär ill dig elld noger eller i werkligietn.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Bilder åv wåldtekt og slaikt", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON>ger språker um, dieler elld föruorder privat- elld intimbilada.", "gui.abuseReport.reason.self_harm_or_suicide": "Akutwåld - sj<PERSON>vskaða elld uot te djär end ą̊ sig", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON>ger språker um te skåðå sig, elld dig, i werkligietn.", "gui.abuseReport.reason.sexually_inappropriate": "Utseende av sexuell karaktär", "gui.abuseReport.reason.sexually_inappropriate.description": "Utseenden som anspelar på sexuella <PERSON>ar, k<PERSON><PERSON><PERSON> och sexuellt våld.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism elld ekstremt wåld", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> språker um, föru<PERSON>er elld uoter min terorism, elld wåld för dyö dier iro gu<PERSON>, idiologisker elld noð eller.", "gui.abuseReport.reason.title": "Wel rapport<PERSON><PERSON><PERSON>", "gui.abuseReport.report_sent_msg": "Vi har mottagit din anmälan. Tack!\n\nVår arbetsgrupp kommer att granska den så snart som möjligt.", "gui.abuseReport.select_reason": "<PERSON><PERSON><PERSON>j <PERSON>älningskategori", "gui.abuseReport.send": "<PERSON><PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "Korta ned kommentaren", "gui.abuseReport.send.error_message": "Eð wart noð tuokut mes ulldum stjikk åv raportn den: %s'", "gui.abuseReport.send.generic_error": "Eð war noð so wart tuokut bar rapportn ulld stjikkas åv.", "gui.abuseReport.send.http_error": "Eð war noð so wart tuokut min HTTP bar rapportn den ulld stjikkas åv.", "gui.abuseReport.send.json_error": "Eð wart noð tuokut bar rapportn den ulld stjikkas åv.", "gui.abuseReport.send.no_reason": "Välj en anmälningskategori", "gui.abuseReport.send.not_attested": "<PERSON><PERSON><PERSON> texten ovan och markera kryssrutan för att kunna skicka an<PERSON>älan", "gui.abuseReport.send.service_unavailable": "Eð djikk it fą̊ i anmälningstjänstn nų. Sją̊ etter du ir anslutn og biuoð til iessn.", "gui.abuseReport.sending.title": "Stjikker åv rapportn den...", "gui.abuseReport.sent.title": "Rapportn ir stji<PERSON>ð", "gui.abuseReport.skin.title": "Anmäl spelarutseende", "gui.abuseReport.title": "<PERSON><PERSON><PERSON><PERSON> spelare", "gui.abuseReport.type.chat": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.type.name": "Spelarnamn", "gui.abuseReport.type.skin": "Spelarutseende", "gui.acknowledge": "G<PERSON> wið", "gui.advancements": "Framstig", "gui.all": "Oll", "gui.back": "Etbaker", "gui.banned.description": "%s \n%s \n<PERSON><PERSON>r dig mier ą̊ issnjär lettjin: %s", "gui.banned.description.permanent": "Konto dett ir åvsta<PERSON>t fer oll tið. Eð bityðer du du it spilå ą̊ netį elld gą̊ minn i Realms.", "gui.banned.description.reason": "Finggum ien rapport um uobörg uppfyörend frą̊ konto dett. Moðeratorer åvå sit yvyr folleð dett og eð ir %s, og eð ir muot rieglą i Minecraft Community Standards. Så får an it fårå.", "gui.banned.description.reason_id": "Kod: %s", "gui.banned.description.reason_id_message": "Kod: %s -%s", "gui.banned.description.temporary": "%s tast dą̊, dug du it spilå ą̊ netį elld gå minn i Realms.", "gui.banned.description.temporary.duration": "Konto dett ir åvstaingt iett tag og kumb igangg atten um %s.", "gui.banned.description.unknownreason": "Finggum ien rapport um uobörg uppfyörend frą̊ konto dett. Moðeratorer åvå sit yvyr folleð dett og eð ir, og eð ir muot rieglą i Minecraft Community Standards. Så får an it fårå.", "gui.banned.name.description": "Ditt nuvarande namn \"%s\" bryter mot våra community-standarder. Du kan fortfarande spela i enspelarläget men måste ändra ditt namn för att spela i flerspelarläget.\n\n<PERSON><PERSON><PERSON> mer eller skicka in ärendet för bedömning på följande länk: %s", "gui.banned.name.title": "Namnet tillåts inte i flerspelarläget", "gui.banned.reason.defamation_impersonation_false_information": "Identitetsstöld eller delning av information i syfte att utnyttja eller vilseleda andra", "gui.banned.reason.drugs": "Referenser till olagliga droger", "gui.banned.reason.extreme_violence_or_gore": "Skildringar av grovt våld eller stympning i verkligheten", "gui.banned.reason.false_reporting": "Omfattande mängd falska eller felaktiga anmälningar", "gui.banned.reason.fraud": "Otillåten införskaffning eller användning av innehåll", "gui.banned.reason.generic_violation": "Överträdelse av community-standarder", "gui.banned.reason.harassment_or_bullying": "Kränkande språk uttryckt på ett riktat och skadligt sätt", "gui.banned.reason.hate_speech": "Hets mot folkgrupp eller diskriminering", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON> till hatg<PERSON><PERSON>, terroristorganisationer eller ökända personer", "gui.banned.reason.imminent_harm_to_person_or_property": "Avsikt att orsaka skada på personer eller egendom i verkligheten", "gui.banned.reason.nudity_or_pornography": "Visande av obscent eller pornografiskt material", "gui.banned.reason.sexually_inappropriate": "<PERSON><PERSON> eller innehåll av sexuell karaktär", "gui.banned.reason.spam_or_advertising": "Spam eller marknadsföring", "gui.banned.skin.description": "Ditt nuvarande utseende bryter mot våra community-standarder. Du kan fortfarande spela med ett standardutseende eller välja ett nytt.\n\n<PERSON><PERSON><PERSON> mer eller skicka in ärendet för bedömning på följande länk: %s", "gui.banned.skin.title": "Utseendet tillåts inte", "gui.banned.title.permanent": "Konto ir <PERSON> åv<PERSON>", "gui.banned.title.temporary": "<PERSON><PERSON> ir baiðend ien s<PERSON>ra", "gui.cancel": "B<PERSON><PERSON><PERSON> åv", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Um du ser åv wenn so ennd so nuog du dug jäp du uoss te tågå beslut so iro riktuger.", "gui.chatReport.discard.content": "Tepp du att nų, feswið kommentarer dainer. Ir du säker du will tepp att nų?", "gui.chatReport.discard.discard": "Tepp att og wind iweg rapportn", "gui.chatReport.discard.draft": "<PERSON><PERSON><PERSON><PERSON> sos autkast", "gui.chatReport.discard.return": "Fuortsett redigira", "gui.chatReport.discard.title": "Tepp att rapport og kommentara?", "gui.chatReport.draft.content": "Vill du fortsätta att redigera utkastet till anmä<PERSON> eller slänga det och skapa ett nytt?", "gui.chatReport.draft.discard": "Släng", "gui.chatReport.draft.edit": "Fortsätt redigera", "gui.chatReport.draft.quittotitle.content": "Vill du fortsätta att redigera eller slänga det?", "gui.chatReport.draft.quittotitle.title": "Du har ett utkast till en chattanmälan som kommer att gå förlorat om du lämnar", "gui.chatReport.draft.title": "Redigera utkast till chattanmälan?", "gui.chatReport.more_comments": "Seg åv wenn so ennde:", "gui.chatReport.observed_what": "Wiso rapportirer du ittað?", "gui.chatReport.read_info": "<PERSON><PERSON><PERSON> dig ur an rapportirer", "gui.chatReport.report_sent_msg": "Amm faið rapportn den, tjär tokk fer! Kuogum ą̊ an snjäst irum liðuger.", "gui.chatReport.select_chat": "Wel aut tjattmieddeland fer te rapportira", "gui.chatReport.select_reason": "Wel aut rapportkategori", "gui.chatReport.selected_chat": "%s tjattmieðdielnd autwelt te rapportira", "gui.chatReport.send": "Stikk i weg rapport", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON><PERSON><PERSON>, stutt åv kommentarn", "gui.chatReport.send.no_reason": "Wel aut ien rapportkategori", "gui.chatReport.send.no_reported_messages": "Tjäre: wel aut ien rapport uka so ir fer te rapportira", "gui.chatReport.send.too_many_messages": "Du ar uvmikkel mieðdielend i rapportem", "gui.chatReport.title": "Rapportir spiler", "gui.chatSelection.context": "Mieðdielend firi og attą̊ diem so iro markiraðer fyer min fer te fą̊ mier mieningg", "gui.chatSelection.fold": "%s dolda medd<PERSON>", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s ans<PERSON><PERSON>t sig till chatten", "gui.chatSelection.message.narrate": "%s saggde: %s mes klukka war %s", "gui.chatSelection.selected": "%s/%s mieddielnd autwelder", "gui.chatSelection.title": "Wel aut tjatt mi<PERSON> du al rapportira", "gui.continue": "<PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Kopiera länk till urklipp", "gui.days": "%s dag(daer)", "gui.done": "<PERSON><PERSON><PERSON><PERSON>", "gui.down": "<PERSON><PERSON>", "gui.entity_tooltip.type": "Typ: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s filer nekades", "gui.fileDropFailure.title": "Misslyckades att lägga till filer", "gui.hours": "%s taime(er)", "gui.loadingMinecraft": "<PERSON><PERSON><PERSON> in Minecraft", "gui.minutes": "%s minut(er)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s Knapp", "gui.narrate.editBox": "%s Regliringgslåð: %s", "gui.narrate.slider": "Reglas %s", "gui.narrate.tab": "Flik %s", "gui.no": "Näj", "gui.none": "Indjin", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Öppna rapportmappen", "gui.proceed": "<PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "Yögerklikk fer mier", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "<PERSON><PERSON> etter...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> ollt", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON> spra<PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON>kt so gǫr dj<PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON> smeltbarer", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON>", "gui.report_to_server": "Rapportera till servern", "gui.socialInteractions.blocking_hint": "Bruk min iett <PERSON>", "gui.socialInteractions.empty_blocked": "<PERSON>ð ir int noger blokkiraðspilererer i tjattem", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON> spilerer iro gemder i tjattem", "gui.socialInteractions.hidden_in_chat": "Tjattmideielender frą̊ %s kumo te gemas", "gui.socialInteractions.hide": "Gem ini tjattem", "gui.socialInteractions.narration.hide": "Gem undǫ mieðdielend frǫ %s", "gui.socialInteractions.narration.report": "Rapportir spiler %s", "gui.socialInteractions.narration.show": "Wais mieðdielend frą̊ %s", "gui.socialInteractions.report": "Rapportira", "gui.socialInteractions.search_empty": "<PERSON>n int att nog spilerer so ietteð so", "gui.socialInteractions.search_hint": "<PERSON><PERSON> etter...", "gui.socialInteractions.server_label.multiple": "%s - %s spilerer", "gui.socialInteractions.server_label.single": "%s - %s spiler", "gui.socialInteractions.show": "Wais ini tjattem", "gui.socialInteractions.shown_in_chat": "Tjattmieðdeielander frą̊ %s kumb te waisas", "gui.socialInteractions.status_blocked": "Blokkirað", "gui.socialInteractions.status_blocked_offline": "Blokkirað - frą̊kopplað", "gui.socialInteractions.status_hidden": "Gemd", "gui.socialInteractions.status_hidden_offline": "Gemd-Frą̊kopplað", "gui.socialInteractions.status_offline": "Frą̊kopplað", "gui.socialInteractions.tab_all": "Ollt so ir", "gui.socialInteractions.tab_blocked": "Blokkirað", "gui.socialInteractions.tab_hidden": "Gemd", "gui.socialInteractions.title": "Sosial interaksiuoner", "gui.socialInteractions.tooltip.hide": "Gem undą̊ mieðdielend", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON><PERSON> spiler", "gui.socialInteractions.tooltip.report.disabled": "Eð gor int anmäl noð just nų", "gui.socialInteractions.tooltip.report.no_messages": "Inte noger mieðdielander soo gor rapportira frą̊ spiuler %s", "gui.socialInteractions.tooltip.report.not_reportable": "Issn-j<PERSON><PERSON> spilern beller an it rapportira ettersos dugum iint verifiir chatmieðdielander onumes jär", "gui.socialInteractions.tooltip.show": "Wais mieðdielend", "gui.stats": "Statistik", "gui.toMenu": "Etbaker að serverlistun", "gui.toRealms": "Tillbaka till Realms-listan", "gui.toTitle": "Etbaker að ovuðmatsailem", "gui.toWorld": "Tillbaka till världslistan", "gui.togglable_slot": "<PERSON><PERSON><PERSON> för att inaktivera plats", "gui.up": "Upp", "gui.waitingForResponse.button.inactive": "Tillbaka (%s s)", "gui.waitingForResponse.title": "Väntar på servern", "gui.yes": "<PERSON>a", "hanging_sign.edit": "<PERSON><PERSON><PERSON> skylt<PERSON>", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "Drö<PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON> ą̊", "instrument.minecraft.ponder_goat_horn": "Funndiringg", "instrument.minecraft.seek_goat_horn": "Syötja", "instrument.minecraft.sing_goat_horn": "Saungg", "instrument.minecraft.yearn_goat_horn": "Tråningg", "inventory.binSlot": "<PERSON><PERSON><PERSON>", "inventory.hotbarInfo": "Spårå matsailn min %1$s+%2$s", "inventory.hotbarSaved": "Spåråðer föremǫlsmatsailer (reparir diem min %1$s+%2$s)", "item.canBreak": "Beller fa sund:", "item.canPlace": "<PERSON><PERSON><PERSON> ev grejur <PERSON><PERSON>:", "item.canUse.unknown": "Okä<PERSON>", "item.color": "Ferg: %s", "item.components": "%s komponenter", "item.disabled": "Inaktiverat föremål", "item.durability": "Olldbariet: %s / %s", "item.dyed": "Fergað", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "Akasiabåt min tjistų", "item.minecraft.allay_spawn_egg": "Egg fer te djärå jåpsamböbökalla", "item.minecraft.amethyst_shard": "Ametiststjyrvel", "item.minecraft.angler_pottery_shard": "Keramikskärva med fiskespö", "item.minecraft.angler_pottery_sherd": "Keramikskärva med fiskespö", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Keramikskärva med pilbåge", "item.minecraft.archer_pottery_sherd": "Keramikskärva med pilbåge", "item.minecraft.armadillo_scute": "Bältdjurshornplåt", "item.minecraft.armadillo_spawn_egg": "Skapelseägg för bältdjur", "item.minecraft.armor_stand": "Rustningsstellningg", "item.minecraft.arms_up_pottery_shard": "Keramikskärva med uppsträckta armar", "item.minecraft.arms_up_pottery_sherd": "Keramikskärva med uppsträckta armar", "item.minecraft.arrow": "<PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Įe kruk min Axolotl", "item.minecraft.axolotl_spawn_egg": "Axolotl yndjegg", "item.minecraft.baked_potato": "Båkåðpära", "item.minecraft.bamboo_chest_raft": "Bambuflotte med kista", "item.minecraft.bamboo_raft": "Bambuflotte", "item.minecraft.bat_spawn_egg": "Egg för te djärå leðrer", "item.minecraft.bee_spawn_egg": "Egg fer te djärå bi minn", "item.minecraft.beef": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Roðbietfrie", "item.minecraft.beetroot_soup": "Roðbietssuoð", "item.minecraft.birch_boat": "Byörkbåt", "item.minecraft.birch_chest_boat": "Byörkbåt min tjistų", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON> på<PERSON>", "item.minecraft.black_dye": "Swart ferg", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON> sele", "item.minecraft.blade_pottery_shard": "Keramikskärva med svärd", "item.minecraft.blade_pottery_sherd": "Keramikskärva med svärd", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Egg fer te djärå lugo", "item.minecraft.blue_bundle": "Blå påse", "item.minecraft.blue_dye": "Blå ferg", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "Blå sele", "item.minecraft.bogged_spawn_egg": "Skapelseägg för sumpigt skelett", "item.minecraft.bolt_armor_trim_smithing_template": "Smidesmall", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bultförsedda rustningsdetaljer", "item.minecraft.bone": "Bien", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.book": "Buok", "item.minecraft.bordure_indented_banner_pattern": "Fan<PERSON>önst<PERSON> för taggig ram", "item.minecraft.bow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bowl": "Sk<PERSON>l", "item.minecraft.bread": "Bröð", "item.minecraft.breeze_rod": "Vindstav", "item.minecraft.breeze_spawn_egg": "Skapelseägg för vindväsen", "item.minecraft.brewer_pottery_shard": "Keramikskärva med brygd", "item.minecraft.brewer_pottery_sherd": "Keramikskärva med brygd", "item.minecraft.brewing_stand": "Bryggstellningg", "item.minecraft.brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON> ferg", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON>le", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Bynde", "item.minecraft.bundle.empty": "<PERSON>", "item.minecraft.bundle.empty.description": "Kan förvara 64 föremål av olika slag", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Keramikskärva med eldslåga", "item.minecraft.burn_pottery_sherd": "Keramikskärva med eldslåga", "item.minecraft.camel_spawn_egg": "Skapelseägg för kamel", "item.minecraft.carrot": "Mųoruot", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON> ą̊ ien pinne", "item.minecraft.cat_spawn_egg": "Egg fer te djär<PERSON> katter", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Egg fer te djärå gråttdyörger", "item.minecraft.chainmail_boots": "Ringbrynsstyvler", "item.minecraft.chainmail_chestplate": "Ringbrynsbreststjiv", "item.minecraft.chainmail_helmet": "Ringb<PERSON>sjelm", "item.minecraft.chainmail_leggings": "Ringbrynsuosur", "item.minecraft.charcoal": "Träkuol", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON>bärsbåt med kista", "item.minecraft.chest_minecart": "Gruvvangin min lådų", "item.minecraft.chicken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Egg fer te djärå kykklingger", "item.minecraft.chorus_fruit": "Körfrukt", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clock": "Klukka", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Smidesmall", "item.minecraft.coast_armor_trim_smithing_template.new": "Kustutsmyckade rustningsdetaljer", "item.minecraft.cocoa_beans": "Soklaðbyönur", "item.minecraft.cod": "<PERSON><PERSON> tosk", "item.minecraft.cod_bucket": "Kruk min tosk", "item.minecraft.cod_spawn_egg": "Egg fer te djärå tosker", "item.minecraft.command_block_minecart": "Gruvvangin min kommandoblokk", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Stiek", "item.minecraft.cooked_chicken": "Kuokaðyöna", "item.minecraft.cooked_cod": "Kuokað tosk", "item.minecraft.cooked_mutton": "Kuokaðtakktyöt", "item.minecraft.cooked_porkchop": "Stiekt fleskkotlett", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON> eri", "item.minecraft.cooked_salmon": "Ku<PERSON>ð orað", "item.minecraft.cookie": "<PERSON><PERSON>", "item.minecraft.copper_ingot": "Kuppärtakk", "item.minecraft.cow_spawn_egg": "Egg fer te djärå tjyrner", "item.minecraft.creaking_spawn_egg": "Skapelseägg för knar<PERSON>r<PERSON>d", "item.minecraft.creeper_banner_pattern": "Fanmömster", "item.minecraft.creeper_banner_pattern.desc": "Creeper-le<PERSON><PERSON>g", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON> för creeper", "item.minecraft.creeper_spawn_egg": "Egg fer te djär<PERSON> creeper", "item.minecraft.crossbow": "Armbuost", "item.minecraft.crossbow.projectile": "Prosjektil:", "item.minecraft.crossbow.projectile.multiple": "Laddade projektiler: %s × %s", "item.minecraft.crossbow.projectile.single": "Laddad projektil: %s", "item.minecraft.cyan_bundle": "<PERSON>rk<PERSON> p<PERSON>", "item.minecraft.cyan_dye": "<PERSON>yan ferg", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Keramikskärva med creeper", "item.minecraft.danger_pottery_sherd": "Keramikskärva med creeper", "item.minecraft.dark_oak_boat": "M<PERSON><PERSON>iekb<PERSON><PERSON>", "item.minecraft.dark_oak_chest_boat": "Mörkiekbåt min tjistų", "item.minecraft.debug_stick": "<PERSON><PERSON><PERSON>", "item.minecraft.debug_stick.empty": "%s ieg int noð", "item.minecraft.debug_stick.select": "weld \"%s\" (%s)", "item.minecraft.debug_stick.update": "%s\" að %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Diamantöks<PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "Diamantbreststjiv", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Diamantflåakk", "item.minecraft.diamond_horse_armor": "Diamantestrustningg", "item.minecraft.diamond_leggings": "Diamantuosur", "item.minecraft.diamond_pickaxe": "Diamantakka", "item.minecraft.diamond_shovel": "Diamantreku", "item.minecraft.diamond_sword": "Diamantswärd", "item.minecraft.disc_fragment_5": "Stjivfragment", "item.minecraft.disc_fragment_5.desc": "Musikstjiv -5", "item.minecraft.dolphin_spawn_egg": "Egg fer te djärå delfiner", "item.minecraft.donkey_spawn_egg": "Egg fer te djärå åsnur", "item.minecraft.dragon_breath": "Drakande", "item.minecraft.dried_kelp": "Tor<PERSON><PERSON> sjugras", "item.minecraft.drowned_spawn_egg": "Drukknað tillwerkningsegg", "item.minecraft.dune_armor_trim_smithing_template": "Smidesmall", "item.minecraft.dune_armor_trim_smithing_template.new": "Ökenartade rustningsdetaljer", "item.minecraft.echo_shard": "Ekostjyrvel", "item.minecraft.egg": "Egg", "item.minecraft.elder_guardian_spawn_egg": "Egg fer te djärå gamblinggswakter", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Fertryllaðbuok", "item.minecraft.enchanted_golden_apple": "Fertryllað gull<PERSON>pel", "item.minecraft.end_crystal": "Endkristall", "item.minecraft.ender_dragon_spawn_egg": "Skapelseägg för end<PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "Enderpä<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Egg fer te djärǫ enderkall", "item.minecraft.endermite_spawn_egg": "Egg fer te djärå endermit", "item.minecraft.evoker_spawn_egg": "Egg fer te djärå evoker", "item.minecraft.experience_bottle": "Fertryllninggsputell", "item.minecraft.explorer_pottery_shard": "Keramikskärva med karta", "item.minecraft.explorer_pottery_sherd": "Keramikskärva med karta", "item.minecraft.eye_armor_trim_smithing_template": "Smidesmall", "item.minecraft.eye_armor_trim_smithing_template.new": "Ögondekorerade rustningsdetaljer", "item.minecraft.feather": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "Fanemönster för te<PERSON>", "item.minecraft.filled_map": "Karta", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g", "item.minecraft.firework_rocket": "Fyrwerkeriraktiet", "item.minecraft.firework_rocket.flight": "Tið upi weðrę:", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Fyrwerkeristienn", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blå", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Spesialgard", "item.minecraft.firework_star.cyan": "Syanfergað", "item.minecraft.firework_star.fade_to": "Blikn að", "item.minecraft.firework_star.flicker": "Blakka", "item.minecraft.firework_star.gray": "Grå", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Liuosblå", "item.minecraft.firework_star.light_gray": "Liuosgrå", "item.minecraft.firework_star.lime": "Limegryön", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "<PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Uokunnun form", "item.minecraft.firework_star.shape.burst": "Eksplusiuon", "item.minecraft.firework_star.shape.creeper": "Creeper-formað", "item.minecraft.firework_star.shape.large_ball": "Sturboll", "item.minecraft.firework_star.shape.small_ball": "Litn boll", "item.minecraft.firework_star.shape.star": "Stiennformað", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "Wait", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "Fiskspuoð", "item.minecraft.flint": "<PERSON><PERSON>", "item.minecraft.flint_and_steel": "Jäldfyör", "item.minecraft.flow_armor_trim_smithing_template": "Smidesmall", "item.minecraft.flow_armor_trim_smithing_template.new": "Virvelformade rustningsdetaljer", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "Fanemönster för virvel", "item.minecraft.flow_pottery_sherd": "Keramikskärva med virvel", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "B<PERSON>uomleðningg", "item.minecraft.flower_banner_pattern.new": "Fan<PERSON>önst<PERSON> för blomma", "item.minecraft.flower_pot": "Bljommkruk", "item.minecraft.fox_spawn_egg": "Egg fer te djärå rövir", "item.minecraft.friend_pottery_shard": "Keramikskärva med vänligt ansikte", "item.minecraft.friend_pottery_sherd": "Keramikskärva med vänligt ansikte", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.furnace_minecart": "Gruvvangin min smeltungen", "item.minecraft.ghast_spawn_egg": "Egg fer te djärå ghast", "item.minecraft.ghast_tear": "Ghasttår", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON>d <PERSON>", "item.minecraft.globe_banner_pattern": "Fanmömster", "item.minecraft.globe_banner_pattern.desc": "Glob", "item.minecraft.globe_banner_pattern.new": "Fanemönster för glob", "item.minecraft.glow_berries": "Glyöðendber", "item.minecraft.glow_ink_sac": "Glyöðend bliksjäkk", "item.minecraft.glow_item_frame": "Glyöðend föremǫlsram", "item.minecraft.glow_squid_spawn_egg": "Glyöðend egg fer te djärå blikfistjin", "item.minecraft.glowstone_dust": "Glyöðstįedamm", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Skapelseddjer fer dijetum", "item.minecraft.gold_ingot": "Gulltakka", "item.minecraft.gold_nugget": "Gullklimp", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Gullbreststjiv", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Gullflåakk", "item.minecraft.golden_horse_armor": "G<PERSON><PERSON><PERSON>ningg", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "Grå påse", "item.minecraft.gray_dye": "Grå ferg", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>le", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "Gryönferg", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Egg fer te djärå wakter", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Fanemönster för v<PERSON>v<PERSON>", "item.minecraft.guster_pottery_sherd": "Keramikskärva med vindväsen", "item.minecraft.happy_ghast_spawn_egg": "Skapelseägg för glad ghast", "item.minecraft.harness": "<PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Sjujärta", "item.minecraft.heart_pottery_shard": "Keramikskärva med hjärta", "item.minecraft.heart_pottery_sherd": "Keramikskärva med hjärta", "item.minecraft.heartbreak_pottery_shard": "Keramikskärva med krossat hjärta", "item.minecraft.heartbreak_pottery_sherd": "Keramikskärva med krossat hjärta", "item.minecraft.hoglin_spawn_egg": "Skapelseegg fer Hoglin", "item.minecraft.honey_bottle": "Onunggsputell", "item.minecraft.honeycomb": "Waks<PERSON><PERSON>", "item.minecraft.hopper_minecart": "Gruv<PERSON>gin min tratt", "item.minecraft.horse_spawn_egg": "Egg fer te djärå ester", "item.minecraft.host_armor_trim_smithing_template": "Smidesmall", "item.minecraft.host_armor_trim_smithing_template.new": "Gästgivande rustningsdetaljer", "item.minecraft.howl_pottery_shard": "Keramikskärva med varg", "item.minecraft.howl_pottery_sherd": "Keramikskärva med varg", "item.minecraft.husk_spawn_egg": "Egg fer te djärå skälerer", "item.minecraft.ink_sac": "Blekksjäk", "item.minecraft.iron_axe": "<PERSON><PERSON>n<PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "Iennbreststjiv", "item.minecraft.iron_golem_spawn_egg": "Skapelseägg för järngolem", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "Iennflåakk", "item.minecraft.iron_horse_armor": "Iennest<PERSON>ningg", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "<PERSON><PERSON>nsw<PERSON><PERSON>", "item.minecraft.item_frame": "<PERSON> að grejum", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "Jungelbåt min tjistų", "item.minecraft.knowledge_book": "Kunnskapsbuok", "item.minecraft.lapis_lazuli": "<PERSON><PERSON> lazuli", "item.minecraft.lava_bucket": "Lavakruk", "item.minecraft.lead": "Band", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "Leðertunika", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Leðerestrustningg", "item.minecraft.leather_leggings": "Leð<PERSON>b<PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Ljusblå påse", "item.minecraft.light_blue_dye": "Liuosblå ferg", "item.minecraft.light_blue_harness": "Ljusblå sele", "item.minecraft.light_gray_bundle": "Ljusgrå påse", "item.minecraft.light_gray_dye": "Liuosgrå ferg", "item.minecraft.light_gray_harness": "Ljusgrå sele", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> på<PERSON>", "item.minecraft.lime_dye": "<PERSON>eg<PERSON><PERSON>n ferg", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "Brygd så wärer laindje", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON> la<PERSON>k", "item.minecraft.lingering_potion.effect.empty": "Langgsam drykk eð går it djärå", "item.minecraft.lingering_potion.effect.fire_resistance": "Langgsam drykk an tuol jäldn ov", "item.minecraft.lingering_potion.effect.harming": "Langgsam drykk an werd skåðåð ov", "item.minecraft.lingering_potion.effect.healing": "Langgsam drykk an werd wildera ov", "item.minecraft.lingering_potion.effect.infested": "Kvardröjande infestationsbrygd", "item.minecraft.lingering_potion.effect.invisibility": "Langgsam drykk an werd uosynlin ov", "item.minecraft.lingering_potion.effect.leaping": "Langgsam drykk fer te uppa", "item.minecraft.lingering_potion.effect.levitation": "Langgsam drykk te fliuog min", "item.minecraft.lingering_potion.effect.luck": "Langgsam drykk fer lukka", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON> lang<PERSON> drykk", "item.minecraft.lingering_potion.effect.night_vision": "Langgsam drykk an sir i mörk<PERSON>ę minn", "item.minecraft.lingering_potion.effect.oozing": "Kvardröjande slemmighetsbrygd", "item.minecraft.lingering_potion.effect.poison": "Langgsam drykk fer te kryð ijel min", "item.minecraft.lingering_potion.effect.regeneration": "Langgsam drykk fer te kumå til et nyes", "item.minecraft.lingering_potion.effect.slow_falling": "Langgsam drykk fer te foll smått", "item.minecraft.lingering_potion.effect.slowness": "Langgsam drykk eð går smått ov", "item.minecraft.lingering_potion.effect.strength": "Langgsam drykk an werd stark ov", "item.minecraft.lingering_potion.effect.swiftness": "Langgsam drykk an werd snabb ov", "item.minecraft.lingering_potion.effect.thick": "Staðug langgsam drykk", "item.minecraft.lingering_potion.effect.turtle_master": "Langgsam drykk ov Stjöldkluossmiestern", "item.minecraft.lingering_potion.effect.water": "Langsam wa<PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON> drykk so an dug andes niði wattnę", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON> drykk an werd wisel ov", "item.minecraft.lingering_potion.effect.weaving": "Kvardröjande spindelnätsbrygd", "item.minecraft.lingering_potion.effect.wind_charged": "Kvardröjande vindladdningsbrygd", "item.minecraft.llama_spawn_egg": "Egg fer te djärå lamur", "item.minecraft.lodestone_compass": "Magnetitkompass", "item.minecraft.mace": "Stridsklubba", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON> ferg", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Lavakrem", "item.minecraft.magma_cube_spawn_egg": "Egg fer te djärå lavakuber", "item.minecraft.mangrove_boat": "Mangrovebåt", "item.minecraft.mangrove_chest_boat": "Mangrovebåt min tjistų", "item.minecraft.map": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "Melonstjiv", "item.minecraft.milk_bucket": "Mjokkruk", "item.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Keramikskärva med hacka", "item.minecraft.miner_pottery_sherd": "Keramikskärva med hacka", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "Fanemönster för mojäng", "item.minecraft.mooshroom_spawn_egg": "Egg fer te djärå mooshroom", "item.minecraft.mourner_pottery_shard": "Keramikskärva med vakt", "item.minecraft.mourner_pottery_sherd": "Keramikskärva med vakt", "item.minecraft.mule_spawn_egg": "Egg fer te djärå mulur", "item.minecraft.mushroom_stew": "Soppstuvningg", "item.minecraft.music_disc_11": "Musikstjiv", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Musikstjiv", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Musikskiva", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Musikstjiv", "item.minecraft.music_disc_blocks.desc": "C418 - block", "item.minecraft.music_disc_cat": "Musikstjiv", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Musikstjiv", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Musikskiva", "item.minecraft.music_disc_creator.desc": "<PERSON> – <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Musikskiva", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> – C<PERSON> (speldosa)", "item.minecraft.music_disc_far": "Musikstjiv", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Musikskiva", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions – Lava Chicken", "item.minecraft.music_disc_mall": "Musikstjiv", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Musikstjiv", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Musikstjiv", "item.minecraft.music_disc_otherside.desc": "<PERSON>", "item.minecraft.music_disc_pigstep": "Musikskiva", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Musikskiva", "item.minecraft.music_disc_precipice.desc": "<PERSON> – Precipice", "item.minecraft.music_disc_relic": "Musikskiva", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON><PERSON>", "item.minecraft.music_disc_stal": "Musikstjiv", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Musikstjiv", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Musikskiva", "item.minecraft.music_disc_tears.desc": "<PERSON> – Tears", "item.minecraft.music_disc_wait": "Musikstjiv", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Musikstjiv", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Råtakktjyöt", "item.minecraft.name_tag": "Namnstşylt", "item.minecraft.nautilus_shell": "Nautilusskal", "item.minecraft.nether_brick": "Nethertigelstie", "item.minecraft.nether_star": "Nethers<PERSON><PERSON>", "item.minecraft.nether_wart": "Netherwårt", "item.minecraft.netherite_axe": "Netheriteöks", "item.minecraft.netherite_boots": "Netheritstyvler", "item.minecraft.netherite_chestplate": "Netheritharnesk", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_hoe": "Netheriteflåakk", "item.minecraft.netherite_ingot": "Netherittakka", "item.minecraft.netherite_leggings": "Netheritebruok", "item.minecraft.netherite_pickaxe": "Netheriteakka", "item.minecraft.netherite_scrap": "Netheritskruot", "item.minecraft.netherite_shovel": "Netheritestjyffel", "item.minecraft.netherite_sword": "Netheriteswärd", "item.minecraft.netherite_upgrade_smithing_template": "Smidesmall", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherituppgradering", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "Iekbåt min tjistų", "item.minecraft.ocelot_spawn_egg": "Egg fer te djärå oceloter", "item.minecraft.ominous_bottle": "Oroväckande flaska", "item.minecraft.ominous_trial_key": "Oroväckande utmaningsnyckel", "item.minecraft.orange_bundle": "Orange påse", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON><PERSON> ferg", "item.minecraft.orange_harness": "Orange sele", "item.minecraft.painting": "<PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Blek e<PERSON>båt", "item.minecraft.pale_oak_chest_boat": "Blek ekbåt med kista", "item.minecraft.panda_spawn_egg": "Egg fer te djärå pandur", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Egg fer te djärå papegojur", "item.minecraft.phantom_membrane": "Sk<PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Egg fer te djärå spötjer", "item.minecraft.pig_spawn_egg": "Egg fer te djärå graiser", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "Snauta", "item.minecraft.piglin_banner_pattern.new": "Fanemönster för tryne", "item.minecraft.piglin_brute_spawn_egg": "Skapelseegg fer Piglinfugat", "item.minecraft.piglin_spawn_egg": "Skapelseegg fer Piglin", "item.minecraft.pillager_spawn_egg": "Egg fer te djärå röverer", "item.minecraft.pink_bundle": "<PERSON>", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON> ferg", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Kannrankeskida", "item.minecraft.plenty_pottery_shard": "Keramikskärva med kista", "item.minecraft.plenty_pottery_sherd": "Keramikskärva med kista", "item.minecraft.poisonous_potato": "Djiftugpära", "item.minecraft.polar_bear_spawn_egg": "Egg fer te djärå aisbyönner", "item.minecraft.popped_chorus_fruit": "Autslaiðn kö<PERSON>", "item.minecraft.porkchop": "<PERSON><PERSON> fleskkotlett", "item.minecraft.potato": "<PERSON><PERSON><PERSON>", "item.minecraft.potion": "B<PERSON>gd", "item.minecraft.potion.effect.awkward": "Avug-drykk", "item.minecraft.potion.effect.empty": "Drykk eð går it djärå", "item.minecraft.potion.effect.fire_resistance": "Drykk an tuol jäldn åv", "item.minecraft.potion.effect.harming": "Skåðådrykk", "item.minecraft.potion.effect.healing": "Drykk an limner wið ov", "item.minecraft.potion.effect.infested": "Infestationsbrygd", "item.minecraft.potion.effect.invisibility": "Drykk an werd uosynlin ov", "item.minecraft.potion.effect.leaping": "Drykk an upper bra ov", "item.minecraft.potion.effect.levitation": "Drykk fer te fliuoga", "item.minecraft.potion.effect.luck": "Drykk so djäv lukka", "item.minecraft.potion.effect.mundane": "Banal brygd", "item.minecraft.potion.effect.night_vision": "Drykk an sir i mörk<PERSON>ę minn", "item.minecraft.potion.effect.oozing": "Slemmighetsbrygd", "item.minecraft.potion.effect.poison": "Kryðijeldrykk", "item.minecraft.potion.effect.regeneration": "Drykk fer te kumå til et nyes", "item.minecraft.potion.effect.slow_falling": "Drykk fer te foll smått", "item.minecraft.potion.effect.slowness": "Drykk eð går smått ov", "item.minecraft.potion.effect.strength": "Styrkedrykk", "item.minecraft.potion.effect.swiftness": "Drykk fer te werd snabb", "item.minecraft.potion.effect.thick": "Staðugdrykk", "item.minecraft.potion.effect.turtle_master": "Drykk fer Stjöldkluossmiestern", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON>k<PERSON> so an dug andes i wattnę ov", "item.minecraft.potion.effect.weakness": "Drykk an werd wisel ov", "item.minecraft.potion.effect.weaving": "Spindelnätsbrygd", "item.minecraft.potion.effect.wind_charged": "Vindladdningsbrygd", "item.minecraft.pottery_shard_archer": "Keramikskärva med pilbåge", "item.minecraft.pottery_shard_arms_up": "Keramikskärva med uppsträckta armar", "item.minecraft.pottery_shard_prize": "Keramikskärva med ädelsten", "item.minecraft.pottery_shard_skull": "Keramikskärva med dödskalle", "item.minecraft.powder_snow_bucket": "Kruk fer puð<PERSON>n", "item.minecraft.prismarine_crystals": "Prismarinkristaller", "item.minecraft.prismarine_shard": "Prismari<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "Keramikskärva med ädelsten", "item.minecraft.prize_pottery_sherd": "Keramikskärva med ädelsten", "item.minecraft.pufferfish": "Puff-fisk", "item.minecraft.pufferfish_bucket": "Kruk min puff-fiskum", "item.minecraft.pufferfish_spawn_egg": "Egg fer te djärå puff-fisker", "item.minecraft.pumpkin_pie": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "<PERSON>", "item.minecraft.purple_dye": "Lila ferg", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON> eri", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "Eråstjinn", "item.minecraft.rabbit_spawn_egg": "Egg fer te djärå erir", "item.minecraft.rabbit_stew": "Eråstuvningg", "item.minecraft.raiser_armor_trim_smithing_template": "Smidesmall", "item.minecraft.raiser_armor_trim_smithing_template.new": "Uppfödande rustningsdetaljer", "item.minecraft.ravager_spawn_egg": "Egg fer te djärå festörerer", "item.minecraft.raw_copper": "Råkuppär", "item.minecraft.raw_gold": "R<PERSON><PERSON><PERSON>ð", "item.minecraft.raw_iron": "Råienn", "item.minecraft.recovery_compass": "Tillrettend kompass", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Redstonedamb", "item.minecraft.resin_brick": "K<PERSON>dtegelsten", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Smidesmall", "item.minecraft.rib_armor_trim_smithing_template.new": "Revbensrandiga rustningsdetaljer", "item.minecraft.rotten_flesh": "Rotnaðflestjeð", "item.minecraft.saddle": "Sa’ll", "item.minecraft.salmon": "R<PERSON> orað", "item.minecraft.salmon_bucket": "<PERSON><PERSON> min oraðum", "item.minecraft.salmon_spawn_egg": "Egg fer te djärå oraðir", "item.minecraft.scrape_pottery_sherd": "Keramikskärva med yxa", "item.minecraft.scute": "Skalbaiter", "item.minecraft.sentry_armor_trim_smithing_template": "Smidesmall", "item.minecraft.sentry_armor_trim_smithing_template.new": "Värnande rustningsdetaljer", "item.minecraft.shaper_armor_trim_smithing_template": "Smidesmall", "item.minecraft.shaper_armor_trim_smithing_template.new": "Formgivande rustningsdetaljer", "item.minecraft.sheaf_pottery_shard": "Keramikskärva med vetekärve", "item.minecraft.sheaf_pottery_sherd": "Keramikskärva med vetekärve", "item.minecraft.shears": "Saks", "item.minecraft.sheep_spawn_egg": "Egg fer te djärå takkur", "item.minecraft.shelter_pottery_shard": "Keramikskärva med träd", "item.minecraft.shelter_pottery_sherd": "Keramikskärva med träd", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON> s<PERSON><PERSON>", "item.minecraft.shield.blue": "Blå stjöld", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "Syanfergað stjöld", "item.minecraft.shield.gray": "Grå stjöld", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON> stj<PERSON>ld", "item.minecraft.shield.light_blue": "Liuosblå stjöld", "item.minecraft.shield.light_gray": "Liuosgrå stjöld", "item.minecraft.shield.lime": "Limegryön stjöld", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>j<PERSON>ld", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON> stj<PERSON>ld", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON>j<PERSON>", "item.minecraft.shield.purple": "<PERSON> s<PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON> stj<PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON> stj<PERSON>ld", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Egg fer te djärå shulker", "item.minecraft.sign": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Smidesmall", "item.minecraft.silence_armor_trim_smithing_template.new": "Ljudlösa rustningsdetaljer", "item.minecraft.silverfish_spawn_egg": "Egg fer te djärå silverfisker", "item.minecraft.skeleton_horse_spawn_egg": "Egg fer te djärå skelletester", "item.minecraft.skeleton_spawn_egg": "Egg fer te djärå skeletter", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "Skoll-leðningg", "item.minecraft.skull_banner_pattern.new": "Fanemönster för <PERSON>", "item.minecraft.skull_pottery_shard": "Keramikskärva med dödskalle", "item.minecraft.skull_pottery_sherd": "Keramikskärva med dödskalle", "item.minecraft.slime_ball": "<PERSON><PERSON><PERSON>ll", "item.minecraft.slime_spawn_egg": "Egg fer te djärå kliem", "item.minecraft.smithing_template": "Smidesmall", "item.minecraft.smithing_template.applies_to": "Kan tillä<PERSON> på:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON><PERSON><PERSON> till tacka eller ädelsten", "item.minecraft.smithing_template.armor_trim.applies_to": "Rustningsdelar", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Lägg till rustningsdel", "item.minecraft.smithing_template.armor_trim.ingredients": "<PERSON><PERSON><PERSON> och ädelsten<PERSON>", "item.minecraft.smithing_template.ingredients": "Ingrediens(er):", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Lägg till netherittacka", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamantutrustning", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Lägg till valfri typ av diamantutrustning", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherittacka", "item.minecraft.smithing_template.upgrade": "Uppgradering: ", "item.minecraft.sniffer_spawn_egg": "Skapelseägg för sniff<PERSON>", "item.minecraft.snort_pottery_shard": "Keramikskärva med sniffare", "item.minecraft.snort_pottery_sherd": "Keramikskärva med sniffare", "item.minecraft.snout_armor_trim_smithing_template": "Smidesmall", "item.minecraft.snout_armor_trim_smithing_template.new": "Trynformade rustningsdetaljer", "item.minecraft.snow_golem_spawn_egg": "Skapelseägg för snögolem", "item.minecraft.snowball": "Sniųoboll", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_eye": "Dyörgoga", "item.minecraft.spider_spawn_egg": "Egg fer te djärå dyörger", "item.minecraft.spire_armor_trim_smithing_template": "Smidesmall", "item.minecraft.spire_armor_trim_smithing_template.new": "Spiraformade rustningsdetaljer", "item.minecraft.splash_potion": "Skwektbrygd", "item.minecraft.splash_potion.effect.awkward": "Skwektend avudrykk", "item.minecraft.splash_potion.effect.empty": "Skwekkend drykk eð går it djärå", "item.minecraft.splash_potion.effect.fire_resistance": "Tweskend drykk an tuol jäldn åv", "item.minecraft.splash_potion.effect.harming": "Tweskend drykk an werd skåðåð ov", "item.minecraft.splash_potion.effect.healing": "Tweskend drykk an werd wildera ov", "item.minecraft.splash_potion.effect.infested": "Kastbar infestationsbrygd", "item.minecraft.splash_potion.effect.invisibility": "Tweskend drykk an werd uosynlin ov", "item.minecraft.splash_potion.effect.leaping": "Tweskend drykk an werd duktin upp ov", "item.minecraft.splash_potion.effect.levitation": "Tweskend drykk te fliuog min", "item.minecraft.splash_potion.effect.luck": "Tweskend drykk ov lukka", "item.minecraft.splash_potion.effect.mundane": "Skwektend ienkel dry<PERSON>k", "item.minecraft.splash_potion.effect.night_vision": "Tweskend drykk an sir i mörk<PERSON>ę minn", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON>brygd", "item.minecraft.splash_potion.effect.poison": "Tweskend drykk te kryd ijel min", "item.minecraft.splash_potion.effect.regeneration": "Tweskend drykk fer te kumå til et nyes", "item.minecraft.splash_potion.effect.slow_falling": "Tweskend drykk fer te foll smått", "item.minecraft.splash_potion.effect.slowness": "Tweskend drykk eð går smått ov", "item.minecraft.splash_potion.effect.strength": "Tweskend brygd te werd stark ov", "item.minecraft.splash_potion.effect.swiftness": "Tweskend rykk te werd snabb ov", "item.minecraft.splash_potion.effect.thick": "Skwektend digerdrykk", "item.minecraft.splash_potion.effect.turtle_master": "Tweskend drykk ov stjöldkluossmiestern", "item.minecraft.splash_potion.effect.water": "Skwektend watuflask", "item.minecraft.splash_potion.effect.water_breathing": "Tweskend drykk so an dug andes i wattnę ov", "item.minecraft.splash_potion.effect.weakness": "Tweskend drykk te werd wisel ov", "item.minecraft.splash_potion.effect.weaving": "Kastbar spindelnätsbrygd", "item.minecraft.splash_potion.effect.wind_charged": "Kastbar vindladdningsbrygd", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "Grą̊båt min tjistų", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Egg fer te djärå blikfisker", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "Stįeökse", "item.minecraft.stone_hoe": "Stįeflåakk", "item.minecraft.stone_pickaxe": "Stįeakka", "item.minecraft.stone_shovel": "Stįesreku", "item.minecraft.stone_sword": "Stįeswärd", "item.minecraft.stray_spawn_egg": "Egg fer te djär<PERSON> wavlerer", "item.minecraft.strider_spawn_egg": "Skapelseegg fer wandrer", "item.minecraft.string": "Tråð", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "Misstainkt stuvningg", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON> ber", "item.minecraft.tadpole_bucket": "Kruk min pringglur", "item.minecraft.tadpole_spawn_egg": "Egg fer te djäråpringglur", "item.minecraft.tide_armor_trim_smithing_template": "Smidesmall", "item.minecraft.tide_armor_trim_smithing_template.new": "Tidvattenvågsprydda rustningsdetaljer", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "Fetryllað kuov", "item.minecraft.tipped_arrow.effect.empty": "Weltað<PERSON><PERSON> so gor it djär<PERSON>", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON> an tuol jäldn åv", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON> so skäðer", "item.minecraft.tipped_arrow.effect.healing": "Kuov te werd wildera ov", "item.minecraft.tipped_arrow.effect.infested": "Infestationspil", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON> an werd uosynlin ov", "item.minecraft.tipped_arrow.effect.leaping": "Kuov te upp min", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON> an fliuog min", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON> fer lukku", "item.minecraft.tipped_arrow.effect.mundane": "Fetryllað kuov", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON> an sir i mörk<PERSON>ę minn", "item.minecraft.tipped_arrow.effect.oozing": "Slemmighetspil", "item.minecraft.tipped_arrow.effect.poison": "Kuov ov drykk", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON> fer te kumå til et nyes", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON> fer te foll smått", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON> eð går smått ov", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON>ov an werd snabb ov", "item.minecraft.tipped_arrow.effect.thick": "Fetryllað kuov", "item.minecraft.tipped_arrow.effect.turtle_master": "Kuov fer stjöldkluossmiestern", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON> so skwekter", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON>ov fer te węs nidi wattnę", "item.minecraft.tipped_arrow.effect.weakness": "Wiselkuov", "item.minecraft.tipped_arrow.effect.weaving": "Spindelnätspil", "item.minecraft.tipped_arrow.effect.wind_charged": "Vindladdningspil", "item.minecraft.tnt_minecart": "Gruvvanging min TNT", "item.minecraft.torchflower_seeds": "Fackelliljefrön", "item.minecraft.totem_of_undying": "Uoðoð-totem", "item.minecraft.trader_llama_spawn_egg": "Egg fer te djärå andelsmanlamur", "item.minecraft.trial_key": "Utmaningsnyckel", "item.minecraft.trident": "<PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "Tropisk fisk", "item.minecraft.tropical_fish_bucket": "Kruk min tropikfiskum", "item.minecraft.tropical_fish_spawn_egg": "Egg fer te djärå tropikfisker", "item.minecraft.turtle_helmet": "Stjöldkluossskal", "item.minecraft.turtle_scute": "Sköldpaddsskal", "item.minecraft.turtle_spawn_egg": "Egg fer te djärå stjöldkluossur", "item.minecraft.vex_armor_trim_smithing_template": "Smidesmall", "item.minecraft.vex_armor_trim_smithing_template.new": "Osaliga rustningsdetaljer", "item.minecraft.vex_spawn_egg": "Egg fer te djärå biswerskupper", "item.minecraft.villager_spawn_egg": "Egg fer te djärå byskaller", "item.minecraft.vindicator_spawn_egg": "Egg fer te djärå förswarerer", "item.minecraft.wandering_trader_spawn_egg": "Egg fe te djärå gråer", "item.minecraft.ward_armor_trim_smithing_template": "Smidesmall", "item.minecraft.ward_armor_trim_smithing_template.new": "Vaktande rustningsdetaljer", "item.minecraft.warden_spawn_egg": "Egg fer te djärå wakter", "item.minecraft.warped_fungus_on_a_stick": "Fevrenggd sopp ǫ ienum pinne", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "Smidesmall", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Vägvisande rustningsdetaljer", "item.minecraft.wheat": "<PERSON><PERSON>", "item.minecraft.wheat_seeds": "Waitkuonn", "item.minecraft.white_bundle": "<PERSON>it påse", "item.minecraft.white_dye": "Wait ferg", "item.minecraft.white_harness": "Vit sele", "item.minecraft.wild_armor_trim_smithing_template": "Smidesmall", "item.minecraft.wild_armor_trim_smithing_template.new": "Vildsinta rustningsdetaljer", "item.minecraft.wind_charge": "Vindladdning", "item.minecraft.witch_spawn_egg": "Egg fer te djärå eksur", "item.minecraft.wither_skeleton_spawn_egg": "Egg fer te djärå witherbįeraunggel", "item.minecraft.wither_spawn_egg": "Skapelseägg fö<PERSON>", "item.minecraft.wolf_armor": "Vargrustning", "item.minecraft.wolf_spawn_egg": "Egg fer te djärå warger", "item.minecraft.wooden_axe": "<PERSON><PERSON>ð<PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "Wiðåflåakk", "item.minecraft.wooden_pickaxe": "Wiðåakka", "item.minecraft.wooden_shovel": "<PERSON><PERSON>ð<PERSON>spað<PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "Buok og fjäðerpenn", "item.minecraft.written_book": "Skrievað buok", "item.minecraft.yellow_bundle": "<PERSON><PERSON>å<PERSON>", "item.minecraft.yellow_dye": "<PERSON><PERSON> ferg", "item.minecraft.yellow_harness": "<PERSON><PERSON> <PERSON>", "item.minecraft.zoglin_spawn_egg": "Skapelseegg fer Zoglin", "item.minecraft.zombie_horse_spawn_egg": "Egg fer te djärå sombiester", "item.minecraft.zombie_spawn_egg": "Egg fer te djärå sombier", "item.minecraft.zombie_villager_spawn_egg": "Egg fer te djärå sombibyskaller", "item.minecraft.zombified_piglin_spawn_egg": "Skapelseegg fer sombifiirað Piglin", "item.modifiers.any": "Påsatt:", "item.modifiers.armor": "Påsatt:", "item.modifiers.body": "Påsatt:", "item.modifiers.chest": "Bar ðu ar ą̊ kruppem:", "item.modifiers.feet": "Bar ðu ar ą̊ fuotum:", "item.modifiers.hand": "I handen:", "item.modifiers.head": "Bar ðu ar ą̊ skollam:", "item.modifiers.legs": "Bar ðu ar ą̊ fuotum:", "item.modifiers.mainhand": "Bar ðu ar i nevån du bruker mjäst ov:", "item.modifiers.offhand": "Bar ðu ar i nevån du bruker minst:", "item.modifiers.saddle": "Sadlad:", "item.nbt_tags": "NBT: %s tag(s)", "item.op_block_warning.line1": "Varning:", "item.op_block_warning.line2": "<PERSON><PERSON><PERSON> kan komma att köras om detta föremål används", "item.op_block_warning.line3": "Använd endast om du känner till det exakta innehållet!", "item.unbreakable": "<PERSON><PERSON><PERSON> it ev sund", "itemGroup.buildingBlocks": "Byggblokk", "itemGroup.coloredBlocks": "Färgade block", "itemGroup.combat": "Straið", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Tillverkning", "itemGroup.foodAndDrink": "<PERSON> och drycker", "itemGroup.functional": "Funktionella block", "itemGroup.hotbar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.ingredients": "<PERSON>g<PERSON><PERSON><PERSON>", "itemGroup.inventory": "Yvyrlievnaðsgrejur", "itemGroup.natural": "Naturliga block", "itemGroup.op": "Operativa verktyg", "itemGroup.redstone": "Redstoneblock", "itemGroup.search": "Liet att gräjur", "itemGroup.spawnEggs": "Skapelseägg", "itemGroup.tools": "Verktyg och redskap", "item_modifier.unknown": "Ųokunnun föremålsendrer: %s", "jigsaw_block.final_state": "Few<PERSON><PERSON> að:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Sjustirað", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON> te tyla", "jigsaw_block.joint_label": "<PERSON>p fer te slå ijuop:", "jigsaw_block.keep_jigsaws": "Av kwer pusselblokk", "jigsaw_block.levels": "Nivåer:%s", "jigsaw_block.name": "Nammen:", "jigsaw_block.placement_priority": "Placeringsprioritet:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON>r det här pusselbitsblocket ansluts till en bit är det i denna ordning som biten bearbetas för anslutningar i den större strukturen.\n\nBitar bearbetas i fallande prioritetsordning där insättningordningen bryter sammankopplingar.", "jigsaw_block.pool": "Målförråð:", "jigsaw_block.selection_priority": "Valprioritet:", "jigsaw_block.selection_priority.tooltip": "När den överordnade pusselbiten bearbetas för anslutningar är det i denna ordning som det här pusselbitsblocket försöker ansluta till sin målbit.\n\nPusselbitar bearbetas i fallande prioritetsordning där slumpad ordning bryter sammankopplingar.", "jigsaw_block.target": "Mǫlnammen:", "jukebox_song.minecraft.11": "C418 – 11", "jukebox_song.minecraft.13": "C418 – 13", "jukebox_song.minecraft.5": "<PERSON> – 5", "jukebox_song.minecraft.blocks": "C418 – blocks", "jukebox_song.minecraft.cat": "C418 – cat", "jukebox_song.minecraft.chirp": "C418 – chirp", "jukebox_song.minecraft.creator": "<PERSON> – <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> – C<PERSON> (speldosa)", "jukebox_song.minecraft.far": "C418 – far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions – Lava Chicken", "jukebox_song.minecraft.mall": "C418 – mall", "jukebox_song.minecraft.mellohi": "C418 – mellohi", "jukebox_song.minecraft.otherside": "<PERSON> – <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> – Precipice", "jukebox_song.minecraft.relic": "<PERSON> <PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 – stal", "jukebox_song.minecraft.strad": "C418 – strad", "jukebox_song.minecraft.tears": "<PERSON> – Tears", "jukebox_song.minecraft.wait": "C418 – wait", "jukebox_song.minecraft.ward": "C418 – ward", "key.advancements": "Framstieg", "key.attack": "Attakir/Ev sund", "key.back": "Gå avut", "key.categories.creative": "Krea<PERSON>v metoð", "key.categories.gameplay": "<PERSON><PERSON><PERSON>", "key.categories.inventory": "Autrustningg", "key.categories.misc": "<PERSON><PERSON><PERSON>", "key.categories.movement": "Ryörels", "key.categories.multiplayer": "<PERSON><PERSON> spilerer", "key.categories.ui": "Grensnitt ą̊ spilę", "key.chat": "<PERSON><PERSON><PERSON> tjatt", "key.command": "Ev upp kommando", "key.drop": "<PERSON><PERSON><PERSON> eð du ar welt", "key.forward": "<PERSON><PERSON> f<PERSON>", "key.fullscreen": "<PERSON><PERSON><PERSON> fö<PERSON>", "key.hotbar.1": "Plass 1 för grejur", "key.hotbar.2": "Plass 2 för grejur", "key.hotbar.3": "Plass 3 för grejur", "key.hotbar.4": "Plass 4 för grejur", "key.hotbar.5": "Plass 5 för grejur", "key.hotbar.6": "Plass 6 för grejur", "key.hotbar.7": "Plass 7 för grejur", "key.hotbar.8": "Plass 8 för grejur", "key.hotbar.9": "Plass 9 för grejur", "key.inventory": "Tepp upp/tepp att matsailn", "key.jump": "Uppe", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON><PERSON>", "key.keyboard.down": "Nidyvyr-kuov", "key.keyboard.end": "Slut", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON>em", "key.keyboard.insert": "Ev ini", "key.keyboard.keypad.0": "Knapp O", "key.keyboard.keypad.1": "Knapp 1", "key.keyboard.keypad.2": "Knapp 2", "key.keyboard.keypad.3": "Knapp 3", "key.keyboard.keypad.4": "Knapp 4", "key.keyboard.keypad.5": "Knapp 5", "key.keyboard.keypad.6": "Knapp 6", "key.keyboard.keypad.7": "Knapp 7", "key.keyboard.keypad.8": "Knapp 8", "key.keyboard.keypad.9": "Knapp 9", "key.keyboard.keypad.add": "Knapp +", "key.keyboard.keypad.decimal": "Desimalknapp", "key.keyboard.keypad.divide": "Knapp /", "key.keyboard.keypad.enter": "Enterknapp", "key.keyboard.keypad.equal": "Knapp =", "key.keyboard.keypad.multiply": "<PERSON><PERSON><PERSON> *", "key.keyboard.keypad.subtract": "Knapp -", "key.keyboard.left": "Wister-kuov", "key.keyboard.left.alt": "Wįster Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Wįster Ctrl", "key.keyboard.left.shift": "Wįster Shift", "key.keyboard.left.win": "Wįster Win", "key.keyboard.menu": "<PERSON><PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "<PERSON><PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "Skriev aut stjärmin", "key.keyboard.right": "Yöger-kuov", "key.keyboard.right.alt": "<PERSON><PERSON><PERSON>", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON><PERSON><PERSON>", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.right.win": "<PERSON><PERSON><PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Millǫslag", "key.keyboard.tab": "<PERSON><PERSON>", "key.keyboard.unknown": "Inte bunden", "key.keyboard.up": "Uppyvyr-kuov", "key.keyboard.world.1": "Wärd 1", "key.keyboard.world.2": "Wärd 2", "key.left": "<PERSON><PERSON><PERSON> að wįster", "key.loadToolbarActivator": "<PERSON><PERSON>", "key.mouse": "Knapp %1$s", "key.mouse.left": "Wįster knapp", "key.mouse.middle": "<PERSON><PERSON><PERSON>knap<PERSON>", "key.mouse.right": "<PERSON><PERSON><PERSON> knapp", "key.pickItem": "Wel blokk", "key.playerlist": "<PERSON>r so spil<PERSON>", "key.quickActions": "Snabbåtgärder", "key.right": "<PERSON><PERSON><PERSON> að yöger", "key.saveToolbarActivator": "<PERSON><PERSON>", "key.screenshot": "Tag ien stjärmbild", "key.smoothCamera": "<PERSON><PERSON><PERSON> f<PERSON><PERSON>", "key.sneak": "St<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.socialInteractions": "St<PERSON><PERSON>rm för sosial interaksiuoner", "key.spectatorOutlines": "<PERSON><PERSON> <PERSON><PERSON> spilerer(åskåðerer)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "<PERSON>t förem<PERSON>l min oðer nevon", "key.togglePerspective": "<PERSON><PERSON><PERSON> fer perspektiv", "key.use": "Bruk eð-ðar du ar/ev aut blokk", "known_server_link.announcements": "Meddelanden", "known_server_link.community": "Gemenskapen", "known_server_link.community_guidelines": "Gemenskapsrik<PERSON><PERSON>jer", "known_server_link.feedback": "Åter<PERSON><PERSON>ling", "known_server_link.forums": "Forum", "known_server_link.news": "Nyheter", "known_server_link.report_bug": "Rapportera serverbugg", "known_server_link.status": "Status", "known_server_link.support": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.website": "Webbp<PERSON>s", "lanServer.otherPlayers": "Instellningger för oðer s<PERSON>rer", "lanServer.port": "Port nummer", "lanServer.port.invalid": "Inte en giltig port.\nLämna redigeringsfältet tomt eller ange ett nummer mellan 1024 och 65535.", "lanServer.port.invalid.new": "Inte en giltig port.\nLämna redigeringsfältet tomt eller ange ett nummer mellan %s och %s.", "lanServer.port.unavailable": "Porten är inte tillgänglig.\nLämna redigeringsfältet tomt eller ange ett annat nummer mellan 1024 och 65535.", "lanServer.port.unavailable.new": "Porten är inte tillgänglig.\nLämna redigeringsfältet tomt eller ange ett annat nummer mellan %s och %s.", "lanServer.scanning": "<PERSON><PERSON><PERSON> etter spil ą̊ dett lokal nätwerk", "lanServer.start": "Sett igangg LAN-wärd", "lanServer.title": "LAN-wärd", "language.code": "ovd_SE", "language.name": "Övdalska", "language.region": "Swerre", "lectern.take_book": "Tag buotję", "loading.progress": "%s%%", "mco.account.privacy.info": "Les mier um Mojang og integritetslaer", "mco.account.privacy.info.button": "<PERSON><PERSON><PERSON> mer om GDPR", "mco.account.privacy.information": "Mojang Studios genomför vissa procedurer för att skydda barn och deras integritet inklusive att följa Children’s Online Privacy Protection Act (COPPA) och dataskyddsförordningen (DSF / GDPR).\n\nDu kanske behöver få tillåtelse från en vårdnadshavare innan du kan komma åt ditt Realms-konto.", "mco.account.privacyinfo": "Mojang genomför vissa procedurer för att hjälpa till att skydda barn och deras integritet inklusive att följa Children’s Online Privacy Protection Act (COPPA) och allmänna dataskyddsförordningen (DSF / GDPR).\n\nDu kanske behöver få tillåtelse från en förälder innan du kan komma åt ditt Realms-konto.\n\nOm du har ett äldre Minecraft-konto (du loggar in med ditt användarnamn) måste du migrera kontot till ett Mojang-konto för att komma åt Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON><PERSON> konto", "mco.activity.noactivity": "Ingen aktivitet under %s dygn", "mco.activity.title": "Spileraktivitiet", "mco.backup.button.download": "<PERSON>d nið siene<PERSON>ð", "mco.backup.button.reset": "Sett igangg wärden et nyes", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON> um", "mco.backup.button.upload": "Leð up wärd", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "Säkerhetskopia (%s)", "mco.backup.entry.description": "Beskrivning", "mco.backup.entry.enabledPack": "Aktiverade paket", "mco.backup.entry.gameDifficulty": "Spelets svårighetsgrad", "mco.backup.entry.gameMode": "Spelläge", "mco.backup.entry.gameServerVersion": "Spelserverns version", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "Frö", "mco.backup.entry.templateName": "<PERSON><PERSON><PERSON>", "mco.backup.entry.undefined": "Odefinierad ändring", "mco.backup.entry.uploaded": "Uppladdade", "mco.backup.entry.worldType": "Världstyp", "mco.backup.generate.world": "<PERSON><PERSON><PERSON>", "mco.backup.info.title": "Ändringar från senaste säkerhetskopian", "mco.backup.narration": "Säkerhetskopia från %s", "mco.backup.nobackups": "Itta-jär ritjeð ar int noð stjyddskopiur just nų.", "mco.backup.restoring": "<PERSON><PERSON><PERSON>r um ritjeð dett", "mco.backup.unknown": "OKÄND", "mco.brokenworld.download": "<PERSON>ð nið", "mco.brokenworld.downloaded": "Niðleddað", "mco.brokenworld.message.line1": "<PERSON><PERSON><PERSON><PERSON> um, elld wel ien eller wärd.", "mco.brokenworld.message.line2": "Du beller fel og wel te ledd nið wärdn að ienspiler.", "mco.brokenworld.minigame.title": "Itta-jär minispileð funggirer int noð laingger", "mco.brokenworld.nonowner.error": "War frek og baið tast iegern ov ritjeð starter um wärdn", "mco.brokenworld.nonowner.title": "<PERSON><PERSON><PERSON><PERSON> ir uvgåmål", "mco.brokenworld.play": "S<PERSON>l<PERSON>", "mco.brokenworld.reset": "Nollstell", "mco.brokenworld.title": "An wärdn du bruker nų funggirer it laingger", "mco.client.incompatible.msg.line1": "<PERSON><PERSON>n den ir uvgåmål og går it i juop min ritję.", "mco.client.incompatible.msg.line2": "War frek bruk sinest wersiuon åv Minecraft.", "mco.client.incompatible.msg.line3": "R<PERSON><PERSON><PERSON> går int ijuop min snabbversiuoner.", "mco.client.incompatible.title": "Klientn ir it kompatibel!", "mco.client.outdated.stable.version": "<PERSON> k<PERSON>version (%s) är inte kompatibel med Realms.\n\nAnvänd den senaste versionen av Minecraft.", "mco.client.unsupported.snapshot.version": "<PERSON> klientversion (%s) är inte kompatibel med Realms.\n\nRealms är inte tillgänglig för denna experimentversion.", "mco.compatibility.downgrade": "Nedgradera", "mco.compatibility.downgrade.description": "Den här världen spelades senast i version %s och du använder version %s. Om en värld nedgraderas kan den skadas – vi kan inte garantera att den kommer att läsas in eller fungera.\n\nEn säkerhetskopia av din värld kommer att sparas under \"Säkerhetskopior\". Återställ den vid behov.", "mco.compatibility.incompatible.popup.title": "Inkompatibel version", "mco.compatibility.incompatible.releaseType.popup.message": "Världen du försöker ansluta till är inkompatibel med versionen du använder.", "mco.compatibility.incompatible.series.popup.message": "Den här världen spelades senast i version %s och du använder version %s.\n\nDessa serier är inte kompatibla med varandra. En ny värld krävs för att kunna spela på den här versionen.", "mco.compatibility.unverifiable.message": "Versionen som denna värld senast spelades i kunde inte verifieras. Om världen upp- eller nedgraderas kommer en säkerhetskopia automatiskt att skapas och sparas under \"Säkerhetskopior\".", "mco.compatibility.unverifiable.title": "Kompatibilitet kan inte verifieras", "mco.compatibility.upgrade": "Uppgradera", "mco.compatibility.upgrade.description": "Den här världen spelades senast i version %s och du använder version %s.\n\nEn säkerhetskopia av din värld kommer att sparas under \"Säkerhetskopior\".\n\nÅterställ den vid behov.", "mco.compatibility.upgrade.friend.description": "Den här världen spelades senast i version %s och du använder version %s.\n\nEn säkerhetskopia av världen kommer att sparas under \"Säkerhetskopior\".\n\nRealmens ägare kan återställa den vid behov.", "mco.compatibility.upgrade.title": "Vill du verkligen uppgradera den här världen?", "mco.configure.current.minigame": "<PERSON><PERSON> so ir nų", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON> går it m<PERSON><PERSON><PERSON> spilern just nų, men sę", "mco.configure.world.backup": "Wärdstyöð", "mco.configure.world.buttons.activity": "Spileraktivitiet", "mco.configure.world.buttons.close": "<PERSON><PERSON> att områð", "mco.configure.world.buttons.delete": "Tag brott", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Instellningger", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON><PERSON> in spiler", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON> wal", "mco.configure.world.buttons.newworld": "Ny värld", "mco.configure.world.buttons.open": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.options": "Wal i wärdn", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Välj region...", "mco.configure.world.buttons.resetworld": "Byr ą̊ wärd et nyes", "mco.configure.world.buttons.save": "Spara", "mco.configure.world.buttons.settings": "Instellningger", "mco.configure.world.buttons.subscription": "Prenumerasjuon", "mco.configure.world.buttons.switchminigame": "Byt minispil", "mco.configure.world.close.question.line1": "Du kumb it te kumb að wärden denn.", "mco.configure.world.close.question.line2": "Ir du säker du will fuortsett?", "mco.configure.world.close.question.title": "Behöver du göra ändringar utan att bli störd?", "mco.configure.world.closing": "Staindjer områðę...", "mco.configure.world.commandBlocks": "Kommandoblokk", "mco.configure.world.delete.button": "Tag dieðǫ ritję", "mco.configure.world.delete.question.line1": "<PERSON><PERSON><PERSON><PERSON> dett kumb te tågås dieðǫ ferr oll tið", "mco.configure.world.delete.question.line2": "Ir ðu säker du will fuortsett?", "mco.configure.world.description": "Beskrivning yvyr om<PERSON>åðę", "mco.configure.world.edit.slot.name": "W<PERSON>rdsnamneð", "mco.configure.world.edit.subscreen.adventuremap": "Summ instellningger funggirer it ettersos an wärdn du ir i nų ir iett äventyr", "mco.configure.world.edit.subscreen.experience": "Summ instellningger funggirer it ettersos an wärdn du ir i nų ir ien lärowärd", "mco.configure.world.edit.subscreen.inspiration": "Summ instellningger funggirer it ettersos an wärdn du ir i nų ir ien inspiration", "mco.configure.world.forceGameMode": "<PERSON>gg fram spi<PERSON><PERSON>", "mco.configure.world.invite.narration": "Du har %s nya inbjudningar", "mco.configure.world.invite.profile.name": "Nam<PERSON>", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Inbjudna (%s)", "mco.configure.world.invites.normal.tooltip": "Normalbruker", "mco.configure.world.invites.ops.tooltip": "An so ar and um eð", "mco.configure.world.invites.remove.tooltip": "<PERSON> dieðǫ", "mco.configure.world.leave.question.line1": "Um du far autyr issn-jär wärdn kumb du it te sją̊ an noð mier um du werd it inbuoðin", "mco.configure.world.leave.question.line2": "Ir du säker du wil fuortsett?", "mco.configure.world.loading": "<PERSON><PERSON>ser in Realm", "mco.configure.world.location": "Plass", "mco.configure.world.minigame": "Nuvarande: %s", "mco.configure.world.name": "Nammin ą̊ områðeð", "mco.configure.world.opening": "Yppner områðę...", "mco.configure.world.players.error": "<PERSON>en spiler min dyö-dar namnę finnas it", "mco.configure.world.players.inviting": "Bjuder in spelare...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Regionsinställningar", "mco.configure.world.region_preference.title": "Välj region", "mco.configure.world.reset.question.line1": "Wärdn den kumb te werd uppdatirad og an wärdn du ar nų kumb te feswið", "mco.configure.world.reset.question.line2": "Ir du säker du will fuortsett?", "mco.configure.world.resourcepack.question": "Du behöver ett anpassat resurspaket för att spela på denna Realm\n\nVill du ladda ned det och spela?", "mco.configure.world.resourcepack.question.line1": "Du får luv åvå iet bistellt resusspakiet fer te spilå i issnjär wärdem", "mco.configure.world.resourcepack.question.line2": "Will du leð nið og spilå?", "mco.configure.world.restore.download.question.line1": "<PERSON><PERSON><PERSON><PERSON> kumb te werd niðleðað og plasirad i lag min ienspilerw<PERSON>rder dainer.", "mco.configure.world.restore.download.question.line2": "Edd du wilað fuortsett?", "mco.configure.world.restore.question.line1": "W<PERSON>rd<PERSON> den kumb te werd återstelld að tiðę '%s' (%s)", "mco.configure.world.restore.question.line2": "Ir ðu säker du will fuortsett?", "mco.configure.world.settings.expired": "Du kan inte redigera inställningarna för en utgången Realm", "mco.configure.world.settings.title": "Instellningger", "mco.configure.world.slot": "Wärd %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dett kumb te werd endrað að ienum eller wärde", "mco.configure.world.slot.switch.question.line2": "Ir ðu säker du will fuortsett?", "mco.configure.world.slot.tooltip": "Byt að ien wärde", "mco.configure.world.slot.tooltip.active": "<PERSON>y minn", "mco.configure.world.slot.tooltip.minigame": "Byt að minispil", "mco.configure.world.spawnAnimals": "Djärå krytyrer", "mco.configure.world.spawnMonsters": "Djärå monstrer", "mco.configure.world.spawnNPCs": "Djärå NPCer", "mco.configure.world.spawnProtection": "Djärå stjydd", "mco.configure.world.spawn_toggle.message": "Om detta alternativ stängs av kommer ALLA befintliga entiteter av den typen att försvinna", "mco.configure.world.spawn_toggle.message.npc": "Om detta alternativ stängs av kommer ALLA befintliga entiteter av den typen att försvinna, t. ex. bybor", "mco.configure.world.spawn_toggle.title": "Varning!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "<PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON>", "mco.configure.world.subscription.expired": "Ir slut", "mco.configure.world.subscription.extend": "Fesk ą̊ prenumerasjuon", "mco.configure.world.subscription.less_than_a_day": "Int ien dag dyö", "mco.configure.world.subscription.month": "Monað", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Werd fernyað automatiskt i", "mco.configure.world.subscription.recurring.info": "Ändringar som görs i din prenumeration på Realms, t.ex. att förlänga tiden eller inaktivera återkommande fakturering, kommer inte att återspeglas förrän på nästa faktureringsdatum.", "mco.configure.world.subscription.remaining.days": "%1$s dagar", "mco.configure.world.subscription.remaining.months": "%1$s månader", "mco.configure.world.subscription.remaining.months.days": "%1$s månader, %2$s dagar", "mco.configure.world.subscription.start": "Datum eð byrd ą̊", "mco.configure.world.subscription.tab": "Abonnemang", "mco.configure.world.subscription.timeleft": "Tið so ir kwere", "mco.configure.world.subscription.title": "Prenumeratiuon den", "mco.configure.world.subscription.unknown": "Ųokunnun", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON><PERSON> ien wärd", "mco.configure.world.switch.slot.subtitle": "Issn-jär wärdn ir tuom, wel ur te djå<PERSON>r<PERSON> wärdn denn", "mco.configure.world.title": "Konfigurir o<PERSON>åð:", "mco.configure.world.uninvite.player": "Är du säker att du vill dra tillbaka inbjudan för \"%s\"?", "mco.configure.world.uninvite.question": "<PERSON>r <PERSON>u säker du will tag dieðo in<PERSON><PERSON><PERSON>an", "mco.configure.worlds.title": "Wärder", "mco.connect.authorizing": "Logger in...", "mco.connect.connecting": "<PERSON><PERSON><PERSON><PERSON> að ritję...", "mco.connect.failed": "<PERSON><PERSON><PERSON><PERSON> int åv gå min i ritjeð", "mco.connect.region": "Serverregion: %s", "mco.connect.success": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "Du får luv skriev iett nammen!", "mco.create.world.failed": "Misslyckades att skapa värld!", "mco.create.world.reset.title": "Djärå wärdn...", "mco.create.world.skip": "<PERSON>t w<PERSON>", "mco.create.world.subtitle": "Wel sjuov ukk wärd wärd du will åvå i nyområðę dett", "mco.create.world.wait": "Djärå områðę...", "mco.download.cancelled": "Nidleddningg åvbrutin", "mco.download.confirmation.line1": "W<PERSON>rdn du al leð nið ir styörr elld %s", "mco.download.confirmation.line2": "<PERSON><PERSON> går int leð upp issn-jär wärdn að ritję dett atten", "mco.download.confirmation.oversized": "Världen du håller på att ladda ned är större än %s\n\nDu kommer inte att kunna ladda upp den här världen till din Realm igen", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rieðu", "mco.download.downloading": "<PERSON><PERSON><PERSON> og leðer nid", "mco.download.extracting": "Drar yr", "mco.download.failed": "Niðleddningg djikk int noð bra", "mco.download.percent": "%s %%", "mco.download.preparing": "Förberieðer nið<PERSON>", "mco.download.resourcePack.fail": "<PERSON><PERSON><PERSON>ades att ladda ned resurspaket!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "<PERSON><PERSON><PERSON> nið sienestað wärdn", "mco.error.invalid.session.message": "War frek bjuoð til start um Minecraft", "mco.error.invalid.session.title": "Uodjiltun session", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON> wart uvgåmål", "mco.errorMessage.6002": "<PERSON><PERSON><PERSON> för servis iro it ok", "mco.errorMessage.6003": "Niðleddninggsnivå ir nå'dd", "mco.errorMessage.6004": "Uppleddningsnivå ir nå'dd", "mco.errorMessage.6005": "Världen är låst", "mco.errorMessage.6006": "Världen är utdaterad", "mco.errorMessage.6007": "Användaren är i för många Realms", "mco.errorMessage.6008": "Ogiltigt namn på Realm", "mco.errorMessage.6009": "Ogiltig beskrivning på Realm", "mco.errorMessage.connectionFailure": "<PERSON>ð wart noð fil, war frek bjuod til iessn, lit sinera.", "mco.errorMessage.generic": "<PERSON>tt fel uppstod: ", "mco.errorMessage.initialize.failed": "Misslyckades att initiera Realm", "mco.errorMessage.noDetails": "Ingen felinformation har angetts", "mco.errorMessage.realmsService": "Ett fel uppstod (%s):", "mco.errorMessage.realmsService.configurationError": "Ett oväntat fel uppstod när världsalternativ redigerades", "mco.errorMessage.realmsService.connectivity": "Kunde inte ansluta till Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kunde inte kontrollera kompatibel version, fick svar: %s", "mco.errorMessage.retry": "Försök igen", "mco.errorMessage.serviceBusy": "Realms irå upptaiðner nų.\nTjäre, biuoð til et nyes um ien styöra.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Bjöd in spelaren %s", "mco.invites.button.accept": "Guoðtågå", "mco.invites.button.reject": "<PERSON><PERSON>", "mco.invites.nopending": "Ingg inbjuoðningger!", "mco.invites.pending": "<PERSON>ya inbjudningar!", "mco.invites.title": "<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON> so baiðer", "mco.minigame.world.changeButton": "Wel iet eller minispil", "mco.minigame.world.info.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON> byter aut wärdn den muot iet minispil, men bar tillfällut!", "mco.minigame.world.info.line2": "Du beller sakt gå att að orinalw<PERSON>rdem sę, auto te tapp noð.", "mco.minigame.world.noSelection": "Tj<PERSON><PERSON>! War frek og wel noð", "mco.minigame.world.restore": "Avsluter minispil...", "mco.minigame.world.restore.question.line1": "Minispileð kumd te tag slut og områð dett kumb te werd restaurirað.", "mco.minigame.world.restore.question.line2": "Ir du säker du will fuortsett?", "mco.minigame.world.selected": "Welt minispil:", "mco.minigame.world.slot.screen.title": "Byter wärd...", "mco.minigame.world.startButton": "Byt", "mco.minigame.world.starting.screen.title": "Minispil starter...", "mco.minigame.world.stopButton": "Slut ą̊v minispileð", "mco.minigame.world.switch.new": "Wel iet eller minispil?", "mco.minigame.world.switch.title": "Byt minispil", "mco.minigame.world.title": "Byt að minispil", "mco.news": "Riks<PERSON><PERSON><PERSON>", "mco.notification.dismiss": "Stäng", "mco.notification.transferSubscription.buttonText": "Överför nu", "mco.notification.transferSubscription.message": "Prenumerationer på Java Realms flyttas till Microsoft Store. Låt inte din prenumeration löpa ut!\nÖverför nu och få 30 dagars kostnadsfri Realms.\nGå till din profil på minecraft.net för att överföra din prenumeration.", "mco.notification.visitUrl.buttonText.default": "Öppna länk", "mco.notification.visitUrl.message.default": "Besök länken nedan", "mco.onlinePlayers": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>are", "mco.play.button.realm.closed": "Realmen är stängd", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Äventyr", "mco.reset.world.experience": "Slaikt an lårt sig", "mco.reset.world.generate": "<PERSON><PERSON>", "mco.reset.world.inspiration": "Inspirasiuon", "mco.reset.world.resetting.screen.title": "Byr ą̊ min wärdem et nyes...", "mco.reset.world.seed": "<PERSON><PERSON> (um du will)", "mco.reset.world.template": "Wärdsmaller", "mco.reset.world.title": "Start um wärdn", "mco.reset.world.upload": "Ledd up wärd", "mco.reset.world.warning": "Ittað-jär byter aut an wärdn du ar nu i områ<PERSON><PERSON> dett", "mco.selectServer.buy": "<PERSON><PERSON> iet områð!", "mco.selectServer.close": "<PERSON><PERSON> att", "mco.selectServer.closed": "Stainggt områð", "mco.selectServer.closeserver": "<PERSON><PERSON> att områð", "mco.selectServer.configure": "Kon<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.configureRealm": "Konfigurera Realm", "mco.selectServer.create": "Djärå områð", "mco.selectServer.create.subtitle": "Välj vilken värld du ska ha på din nya Realm", "mco.selectServer.expired": "<PERSON><PERSON><PERSON><PERSON><PERSON> so ar taið slut", "mco.selectServer.expiredList": "Prenumeratiuon den ir slut", "mco.selectServer.expiredRenew": "Fesk ą̊", "mco.selectServer.expiredSubscribe": "Prenumerira", "mco.selectServer.expiredTrial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir slut", "mco.selectServer.expires.day": "Tar slut um ien dag", "mco.selectServer.expires.days": "Tar slut um %s dågå", "mco.selectServer.expires.soon": "Tar slut snart", "mco.selectServer.leave": "<PERSON><PERSON>", "mco.selectServer.loading": "<PERSON><PERSON><PERSON> in Realms-listan", "mco.selectServer.mapOnlySupportedForVersion": "Issu-jär kart ar int noð styöð i %s", "mco.selectServer.minigame": "Minispil:", "mco.selectServer.minigameName": "Minispel: %s", "mco.selectServer.minigameNotSupportedInVersion": "Går int spilå ittað-jär minispåileð i %s", "mco.selectServer.noRealms": "Du verkar inte äga en Realm. Lägg till en för att spela tillsammans med dina vänner.", "mco.selectServer.note": "Är ą̊:", "mco.selectServer.open": "Yppenområð", "mco.selectServer.openserver": "<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>", "mco.selectServer.play": "Spilå", "mco.selectServer.popup": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir i<PERSON>, i<PERSON><PERSON><PERSON> sett te wårå min i ien online Minecraft-wärd i lag minn so mikkel sos ti kompisum ą̊ summu gaungg. E<PERSON> går leð in minispil og noð mikkel normalwärder. Eð ir bara an sǫ ieg områðeð so får luv bitå<PERSON>.", "mco.selectServer.purchase": "<PERSON><PERSON> til <PERSON>", "mco.selectServer.trial": "<PERSON><PERSON>ove!", "mco.selectServer.uninitialized": "Trytj te byr ǫ nyumoråð dett!", "mco.snapshot.createSnapshotPopup.text": "<PERSON> håller på att skapa en kostnadsfri experimentversionsrealm som sammankopplas med din betalda prenumeration på Realms. Denna nya experimentversionsrealm är tillgänglig så länge den betalda prenumerationen är aktiv. Din betalda Realm kommer inte att påverkas", "mco.snapshot.createSnapshotPopup.title": "Skapa experimentversionsrealm?", "mco.snapshot.creating": "Skapar experimentversionsrealm...", "mco.snapshot.description": "Sammankopplad med \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Du måste använda version %s för att ansluta till denna Realm", "mco.snapshot.friendsRealm.upgrade": "%s måste uppgradera sin Realm innan du kan spela från denna version", "mco.snapshot.paired": "Denna experimentversionsrealm är sammankopplad med \"%s\"", "mco.snapshot.parent.tooltip": "Anv<PERSON><PERSON> den senaste utgåvan av Minecraft för att spela på denna Realm", "mco.snapshot.start": "Starta kostnadsfri experimentversionsrealm", "mco.snapshot.subscription.info": "Detta är en experimentversionsrealm som är sammankopplad till prenumerationen av din Realm \"%s\". Den är aktiv lika länge som dess sammankopplade Realm.", "mco.snapshot.tooltip": "Använd experimentversionsrealms för att få en smygtitt på kommande versioner av Minecraft, som kan innehålla nya funktioner och andra <PERSON>.\n\nDu kan hitta dina vanliga Realms i spelets utgivningsversion.", "mco.snapshotRealmsPopup.message": "Realms är nu tillgänglig i experimentversioner från och med 23w41a. Alla prenumerationer på Realms inkluderar en kostnadsfri Realm för experimentversioner som är separat från din vanliga Realm för Java Edition!", "mco.snapshotRealmsPopup.title": "Realms är nu tillgänglig i experimentversioner", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON>r", "mco.template.button.publisher": "Autdjiver", "mco.template.button.select": "Wel", "mco.template.button.trailer": "Förandskuogå", "mco.template.default.name": "Wärdsschabloner", "mco.template.info.tooltip": "Autdjiveres webbsaiða", "mco.template.name": "<PERSON><PERSON><PERSON>", "mco.template.select.failure": "Dugdum it fo i listų ov eð so ir i issu-jär avdielnind<PERSON>. Kontrollir internetansklutnindję og bjuoð til et nyes sienera.", "mco.template.select.narrate.authors": "Fefatter: %s", "mco.template.select.narrate.version": "versiuon %s", "mco.template.select.none": "Oppsan! Eð sir aut issu-jär avdielnindję ir tuom just nų. Kumb att sienera og sjå wenn so finnas jär, elld um du ir ien skaper, %s.", "mco.template.select.none.linkTitle": "funndir ą̊ um du edd bellt biðrågå min noð boð ðu og", "mco.template.title": "Wärdsschabloner", "mco.template.title.minigame": "Minispil", "mco.template.trailer.tooltip": "Förhandswisning ov kartų", "mco.terms.buttons.agree": "Olld min", "mco.terms.buttons.disagree": "Old it min", "mco.terms.sentence.1": "Ig går min ą̊ områðesriegler Minecraft", "mco.terms.sentence.2": "Riegler fer service", "mco.terms.title": "Omåðesriegler fer service", "mco.time.daysAgo": "%1$s dagar sedan", "mco.time.hoursAgo": "%1$s timmar sedan", "mco.time.minutesAgo": "%1$s minuter sedan", "mco.time.now": "nu", "mco.time.secondsAgo": "%1$s sekunder sedan", "mco.trial.message.line1": "Edd du wilað åvå iett ieget ritje?", "mco.trial.message.line2": "<PERSON><PERSON><PERSON> jär fer te fą̊ mier informasjuon!", "mco.upload.button.name": "<PERSON><PERSON> upp", "mco.upload.cancelled": "Uppleðningg brottaið", "mco.upload.close.failure": "Eð djikk it tepp att ritj<PERSON> dett, war frek bjuoð til sienera", "mco.upload.done": "Uppleðning rieð", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Uppleðningg funggireð it! (%s)", "mco.upload.failed.too_big.description": "Den valda världen är för stor. Den maximalt tillåtna storleken är %s.", "mco.upload.failed.too_big.title": "Världen är för stor", "mco.upload.hardcore": "W<PERSON><PERSON><PERSON> sos Hardcore går int leð upp!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Förberieð wärdę dai", "mco.upload.select.world.none": "Eð djikk int finn att nog ienspilerwärder!", "mco.upload.select.world.subtitle": "War frek og wel ien ienspilerwärd te leð upp", "mco.upload.select.world.title": "<PERSON><PERSON> upp wärd", "mco.upload.size.failure.line1": "'%s' ir uvstur!", "mco.upload.size.failure.line2": "<PERSON>ð ir %s. Styösst luvlin sturliek ir %s.", "mco.upload.uploading": "<PERSON><PERSON>er upp %s'", "mco.upload.verifying": "Seg ja að wärdę dai", "mco.version": "Version: %s", "mco.warning": "Varning!", "mco.worldSlot.minigame": "Minispel", "menu.custom_options": "Anpassade alternativ...", "menu.custom_options.title": "Anpassade alternativ", "menu.custom_options.tooltip": "OBS: Anpassade alternativ tillhandahålls av servrar och/eller innehåll från tredje part.\nVar försiktig!", "menu.custom_screen_info.button_narration": "Detta är en anpassad meny. Läs mer.", "menu.custom_screen_info.contents": "Innehållet i denna meny styrs av tredjepartsservrar och kartor som varken ägs, hanteras eller övervakas av Mojang Studios eller Microsoft.\n\nVar alltid försiktig när du klickar på länkar och ge aldrig bort privat information, inklusive inloggningsuppgifter.\n\nOm denna meny förhindrar dig från att spela kan du även koppla från den nuvarande servern med hjälp av knappen nedan.", "menu.custom_screen_info.disconnect": "Anpass<PERSON> meny nekades", "menu.custom_screen_info.title": "Information om anpassade menyer", "menu.custom_screen_info.tooltip": "<PERSON>ta är en anpassad meny. Klicka här om du vill läsa mer.", "menu.disconnect": "Lieva server", "menu.feedback": "Å<PERSON><PERSON><PERSON>ling...", "menu.feedback.title": "Åter<PERSON><PERSON>ling", "menu.game": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.modded": " (Modded)", "menu.multiplayer": "Flier spilerär", "menu.online": "Minecraft Realms", "menu.options": "Alternativ...", "menu.paused": "Spileð stand kwere", "menu.playdemo": "Spilå demowärd", "menu.playerReporting": "Spilerrapportiringg", "menu.preparingSpawn": "Förbirieð tillwerkningsområð: %s%%", "menu.quick_actions": "Snabbåtgärder...", "menu.quick_actions.title": "Snabbåtgärder", "menu.quit": "<PERSON><PERSON> <PERSON><PERSON>", "menu.reportBugs": "<PERSON><PERSON> so ir tuokut ir", "menu.resetdemo": "<PERSON><PERSON><PERSON><PERSON> demowärd et nyes", "menu.returnToGame": "<PERSON><PERSON> <PERSON> spilå atten", "menu.returnToMenu": "Spårå og far att að ovuðmatsailem", "menu.savingChunks": "Spärer datablåkk", "menu.savingLevel": "<PERSON><PERSON><PERSON><PERSON> wärd", "menu.sendFeedback": "<PERSON>r eð funggireð", "menu.server_links": "Serverlänkar...", "menu.server_links.title": "Serverlänkar", "menu.shareToLan": "Ev upp fer LAN", "menu.singleplayer": "<PERSON><PERSON>", "menu.working": "Jobber ǫ...", "merchant.deprecated": "<PERSON><PERSON><PERSON> fyll ą̊ förråðę iett par gangger fer dag.", "merchant.level.1": "Nybörjer", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON> so ar lärt sig mjäst ollt so ir", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "Miester", "merchant.title": "%s - %s", "merchant.trades": "Byte", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Tryttj ą̊ %1$s fer te klaiv åv", "multiplayer.applyingPack": "<PERSON><PERSON><PERSON>", "multiplayer.confirm_command.parse_errors": "Du försöker köra ett okänt eller ogiltigt kommando.\nÄr du säker?\nKommando: %s", "multiplayer.confirm_command.permissions_required": "Du försöker köra ett kommando som kräver förhöjda behörigheter.\nDet skulle kunna påverka ditt spel negativt.\nÄr du säker?\nKommando: %s", "multiplayer.confirm_command.title": "Bekräfta körning av kommando", "multiplayer.disconnect.authservers_down": "<PERSON><PERSON><PERSON> fer te kontrollir riktugietn iro staingder. War frek bjuoð til sienera!", "multiplayer.disconnect.bad_chat_index": "Identifierade chattmeddelanden som saknats eller inkommit i fel ordning från servern", "multiplayer.disconnect.banned": "Du får it wår<PERSON> ą̊ issnjär servern noð mier", "multiplayer.disconnect.banned.expiration": "\nFörbuðeð dett kumb te tågås brott ą̊ %s", "multiplayer.disconnect.banned.reason": "Du får it wå<PERSON><PERSON> ą̊ issnjär server, bar för ðyö: %s", "multiplayer.disconnect.banned_ip.expiration": "\nFörbuðeð dett kumb te tågås brott ą̊ %s", "multiplayer.disconnect.banned_ip.reason": "IP-adressn den ir ferbuoðin å issnjär servern.\nBar för ðyö: %s", "multiplayer.disconnect.chat_validation_failed": "Probliem te vialiðir tjattmieðdielend", "multiplayer.disconnect.duplicate_login": "<PERSON> loggeð in frą̊ ien eller plass", "multiplayer.disconnect.expired_public_key": "Profilens offentliga nyckel har upphört att gälla. Kontrollera att din systemtid är synkroniserad och försök att starta om ditt spel.", "multiplayer.disconnect.flying": "Eð går it fliuog noð ą̊ iss-jär servern", "multiplayer.disconnect.generic": "Brottkopplað", "multiplayer.disconnect.idling": "Du ar werið lat uvlaindj!", "multiplayer.disconnect.illegal_characters": "Uolaglig buokstävir i tjattem", "multiplayer.disconnect.incompatible": "Inkompatibel klient. Tjäre, bruk %s", "multiplayer.disconnect.invalid_entity_attacked": "<PERSON><PERSON><PERSON> til attakir ien uodjiltug gräj", "multiplayer.disconnect.invalid_packet": "<PERSON>n stjikked åv iett pakiet so funggirer it", "multiplayer.disconnect.invalid_player_data": "Spileruppdjiper so int funggirer", "multiplayer.disconnect.invalid_player_movement": "Uodjiltug pakiet fer te flytt ą̊ spilern wart muottagner", "multiplayer.disconnect.invalid_public_key_signature": "Profilemes offentlig nytjyl funggirer inte. Bjuoð til start spileð dett et nyes.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ogiltig signatur för profilens offentliga nyckel.\nFörsök att starta om spelet.", "multiplayer.disconnect.invalid_vehicle_movement": "Uodjiltug pakiet fer te flytt ą̊ akuduon wart muottaið", "multiplayer.disconnect.ip_banned": "IPn denn får it wårå ą̊ issnjär servern", "multiplayer.disconnect.kicked": "Autwindað åv ien operatör", "multiplayer.disconnect.missing_tags": "<PERSON><PERSON><PERSON><PERSON> tagger kam frǫ serverem. <PERSON><PERSON><PERSON><PERSON>, sjǫ til du fǫr i an so ar um and servern.", "multiplayer.disconnect.name_taken": "Eð-ðar namneð ir rieð upptaið", "multiplayer.disconnect.not_whitelisted": "Du ir it wait-listað ą̊ issnjär servern!", "multiplayer.disconnect.out_of_order_chat": "Tjattpa<PERSON>t wart tuokut. Ar du rett systiemtið?", "multiplayer.disconnect.outdated_client": "Inkompatibel klient! Tjäre, bruk %s", "multiplayer.disconnect.outdated_server": "Inkompatibel klient. Tjäre, bruk %s", "multiplayer.disconnect.server_full": "Servern ir full!", "multiplayer.disconnect.server_shutdown": "Servern wart åvstainggd", "multiplayer.disconnect.slow_login": "<PERSON>ð tuold uvlaindj te log ini", "multiplayer.disconnect.too_many_pending_chats": "Uvmikkel tjattmieðdielend so int iro riktuger", "multiplayer.disconnect.transfers_disabled": "Servern accepterar inte överföringar", "multiplayer.disconnect.unexpected_query_response": "Uowentað kund-data frą̊ klientem", "multiplayer.disconnect.unsigned_chat": "Finggum iet signaturlost tjattpakiet elld iet autǫ signatur.", "multiplayer.disconnect.unverified_username": "<PERSON><PERSON><PERSON>ð it åv te kontollir brukernamnę!", "multiplayer.downloadingStats": "Far etter statistik...", "multiplayer.downloadingTerrain": "<PERSON><PERSON><PERSON> milj<PERSON>...", "multiplayer.lan.server_found": "Ny server hittades: %s", "multiplayer.message_not_delivered": "Dug it stji<PERSON><PERSON> tjatter, sjǫ serverlogger: %s", "multiplayer.player.joined": "%s kam in i spileð", "multiplayer.player.joined.renamed": "%s (kennd sos %s för) kam in i spileð", "multiplayer.player.left": "%s djikk yr spilę", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Ans<PERSON>na spelare: %s", "multiplayer.requiredTexturePrompt.disconnect": "Servern fą̊r luv åvå iet anpassað resurspakiet", "multiplayer.requiredTexturePrompt.line1": "Issnjär servern will du bruker iet anpassað resurspakiet.", "multiplayer.requiredTexturePrompt.line2": "Um du naiker te bruk iet anpassað resurspakiet werd du brottkopplað frą̊ serverem.", "multiplayer.socialInteractions.not_available": "Eð gą̊r bar bruk sosial interaksiuoner i multispilerwärder", "multiplayer.status.and_more": "... og %s flierer...", "multiplayer.status.cancelled": "Åvbrutið", "multiplayer.status.cannot_connect": "Dug it koppel ijuop min serverem", "multiplayer.status.cannot_resolve": "Dug it bistemm wärdnamneð", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Inkompatibel versiuon!", "multiplayer.status.motd.narration": "Serverbeskrivning: %s", "multiplayer.status.no_connection": "(<PERSON><PERSON>r it kontakt)", "multiplayer.status.old": "G<PERSON>m<PERSON><PERSON>", "multiplayer.status.online": "Ans<PERSON>en", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping: %s millisekunder", "multiplayer.status.pinging": "Pingger...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s av %s spelare är anslutna", "multiplayer.status.quitting": "Sluter yr", "multiplayer.status.request_handled": "Bidj<PERSON>ran um status ir gard", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Fikk data so war it bistelld", "multiplayer.status.version.narration": "Serverversion: %s", "multiplayer.stopSleeping": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> sa<PERSON>", "multiplayer.texturePrompt.failure.line1": "Eð djikk it bruk serveres risurspakiet", "multiplayer.texturePrompt.failure.line2": "Ollt an biyöver anpassaðresurser að katji funggirer it sos du add tainkt", "multiplayer.texturePrompt.line1": "Issnjär servern will du bruker iet anpassað resurspakiet.", "multiplayer.texturePrompt.line2": "Edd du wilað leð nið og installir eð automagiskt?", "multiplayer.texturePrompt.serverPrompt": "%s \nMieðdielend frǫ server: \n%s", "multiplayer.title": "<PERSON><PERSON><PERSON><PERSON> flieru spilerer", "multiplayer.unsecureserver.toast": "Mieðdielend stikkaðer ą̊ isnnjär servern kastji ir endraðer frą̊ orginalę", "multiplayer.unsecureserver.toast.title": "Dugum int verifier tjattmieðdielend", "multiplayerWarning.check": "Wais it issn-jär stj<PERSON><PERSON>in atten", "multiplayerWarning.header": "Sjǫ upp! Ien trið spiler ir min", "multiplayerWarning.message": "Sjǫ upp: <PERSON><PERSON> eller server, so Mojang or Microsoft ieg it, it bruker eld bistemm yvyr, luver aut online-spil. Eð beller wårå du fǫr tjatt-mi<PERSON><PERSON>end elld noð eller so passer it að ollum.", "music.game.a_familiar_room": "<PERSON> – A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> – An Ordinary Day", "music.game.ancestry": "<PERSON> Ancestry", "music.game.below_and_above": "<PERSON> – Below and Above", "music.game.broken_clocks": "<PERSON> – Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> – Comforting Memories", "music.game.creative.aria_math": "C418 – Aria Math", "music.game.creative.biome_fest": "C418 – Biome Fest", "music.game.creative.blind_spots": "C418 – Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 – Taswell", "music.game.crescent_dunes": "<PERSON> – Crescent Dunes", "music.game.danny": "C418 – <PERSON>", "music.game.deeper": "<PERSON> – <PERSON>er", "music.game.dry_hands": "C418 – Dry Hands", "music.game.echo_in_the_wind": "<PERSON> – Echo in the Wind", "music.game.eld_unknown": "<PERSON> – <PERSON><PERSON>", "music.game.end.alpha": "C418 – Alpha", "music.game.end.boss": "C418 – <PERSON>", "music.game.end.the_end": "C418 – The End", "music.game.endless": "<PERSON> – End<PERSON>", "music.game.featherfall": "<PERSON> – Featherfall", "music.game.fireflies": "<PERSON> – Fireflies", "music.game.floating_dream": "<PERSON><PERSON> – Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> – Infinite Amethyst", "music.game.key": "C418 – Key", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> – Left to Bloom", "music.game.lilypad": "<PERSON> – Lily<PERSON>", "music.game.living_mice": "C418 – <PERSON> Mice", "music.game.mice_on_venus": "C418 – <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 – Mine<PERSON>", "music.game.nether.ballad_of_the_cats": "C418 – Ballad of the Cats", "music.game.nether.concrete_halls": "C418 – Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 – <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> – <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> – So Below", "music.game.nether.warmth": "C418 – Warmth", "music.game.one_more_day": "<PERSON> – One More Day", "music.game.os_piano": "<PERSON> – O's Piano", "music.game.oxygene": "C418 – Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> Puzzlebox", "music.game.stand_tall": "<PERSON> – Stand Tall", "music.game.subwoofer_lullaby": "C418 – Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> – Firebugs", "music.game.swamp.labyrinthine": "<PERSON> – Labyrinthine", "music.game.sweden": "C418 – Sweden", "music.game.watcher": "<PERSON> – Watcher", "music.game.water.axolotl": "C418 – Axolotl", "music.game.water.dragon_fish": "C418 – <PERSON> Fish", "music.game.water.shuniji": "C418 – <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON> – <PERSON>", "music.game.wet_hands": "C418 – <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> yakusoku", "music.menu.beginning_2": "C418 – Beginning 2", "music.menu.floating_trees": "C418 – Floating Trees", "music.menu.moog_city_2": "C418 – Moog City 2", "music.menu.mutation": "C418 – Mutation", "narration.button": "Knapp: %s", "narration.button.usage.focused": "<PERSON><PERSON><PERSON> nið <PERSON>ter fer te aktivira", "narration.button.usage.hovered": "Wisterklikk fer te fą̊ igangg", "narration.checkbox": "Krossruta: %s", "narration.checkbox.usage.focused": "Trytj ą̊ Enter fer te markira", "narration.checkbox.usage.hovered": "Trytj ą̊ wister fer te markira", "narration.component_list.usage": "Tryttj ą̊ Tab fer te kumo að nesta plass", "narration.cycle_button.usage.focused": "Tryttj ą̊ Enter fer te byt að %s", "narration.cycle_button.usage.hovered": "Wisterklikk fer te byt að %s", "narration.edit_box": "Redigiringgslåð: %s", "narration.item": "Föremål: %s", "narration.recipe": "Risept fer %s", "narration.recipe.usage": "Wįsterklikk fer te wela", "narration.recipe.usage.more": "Yögerklikk fer te wel flieru riseft", "narration.selection.usage": "Trytj ą̊ kanpper fer upp og nið fer te kumo nogum eller stass", "narration.slider.usage.focused": "Trytj ą̊ wįster- el<PERSON> yöger tand<PERSON>nap<PERSON> fer te ender wärde", "narration.slider.usage.hovered": "Tag i reglaseð og drag fer te ender wärde", "narration.suggestion": "Węlt feslag %s ov %s: %s", "narration.suggestion.tooltip": "Weld feslag %s ov %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Tryck på Tabb för att gå till nästa förslag", "narration.suggestion.usage.cycle.hidable": "Tryck på Tabb för att gå till nästa förslag, eller Escape för att inaktivera förslag", "narration.suggestion.usage.fill.fixed": "Tryck på Tabb för att aktivera förslag", "narration.suggestion.usage.fill.hidable": "Tryck på Tabb för att aktivera förslag, eller Escape för att inaktivera förslag", "narration.tab_navigation.usage": "<PERSON>ck på Ctrl och Tabb för att växla mellan flikar", "narrator.button.accessibility": "Ur eð går fą̊ i eð", "narrator.button.difficulty_lock": "Knevellą̊s", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Upplęst", "narrator.button.language": "Glam", "narrator.controls.bound": "%s <PERSON><PERSON> að %s", "narrator.controls.reset": "Återstell knappin fer %s", "narrator.controls.unbound": "%s ärer it iuop min noð", "narrator.joining": "<PERSON><PERSON><PERSON> minn", "narrator.loading": "Leðer: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON><PERSON>", "narrator.position.list": "Weld rað i listun %s åv %s", "narrator.position.object_list": "Weld raðelement %s åv %s", "narrator.position.screen": "Sjärmdieler %s ą̊v %s", "narrator.position.tab": "Valde flik %s av %s", "narrator.ready_to_play": "Rieð te spilå", "narrator.screen.title": "<PERSON><PERSON>ð<PERSON><PERSON><PERSON>", "narrator.screen.usage": "Bruk mauspiekern eld trytj Tab fer te wel dieler", "narrator.select": "Weld:%s", "narrator.select.world": "Weld %s, vart spiloð sienest: %s, %s, %s, versiuon: %s", "narrator.select.world_info": "Valde %s, spelades senast: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON> åvstainggd", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> ą̊sett", "optimizeWorld.confirm.description": "Ittað-jä<PERSON> bju<PERSON>ð til dj<PERSON><PERSON><PERSON> wärdn den oll best för-ðyö eð spärer oll data i sienestað spilformateð. Ed beller wårå są̊ eð tuol noð laindje um wärdn den ir komplisirað. Bar ðu ir rieð dug du spilå wärdn den straiðera, men passer it ijuop min gamblera versiuoner åv spileð. Ir du säker du will fuortsett?", "optimizeWorld.confirm.proceed": "Säkerhetskopiera o. optimera", "optimizeWorld.confirm.title": "<PERSON><PERSON><PERSON><PERSON> wä<PERSON>n oll best", "optimizeWorld.info.converted": "Datablokk so so wart uppgraðiraðer: %s", "optimizeWorld.info.skipped": "Datablokk so uppes yvyr: %s", "optimizeWorld.info.total": "So mikkel blokk ir eð: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Rekken datablokk...", "optimizeWorld.stage.failed": "<PERSON>lar<PERSON>ð it åv eð! :(", "optimizeWorld.stage.finished": "Slutför...", "optimizeWorld.stage.finished.chunks": "Slutför uppgradering av datablock...", "optimizeWorld.stage.finished.entities": "Slutför uppgradering av entiteter...", "optimizeWorld.stage.finished.poi": "Slutför uppgradering av intressepunkter...", "optimizeWorld.stage.upgrading": "Uppgraderar alla datablock...", "optimizeWorld.stage.upgrading.chunks": "Uppgraderar alla datablock...", "optimizeWorld.stage.upgrading.entities": "Uppgraderar alla entiteter...", "optimizeWorld.stage.upgrading.poi": "Uppgraderar alla intressepunkter...", "optimizeWorld.title": "Optimir wärd %s", "options.accessibility": "Tillgänglighetsinställningar...", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON> k<PERSON>", "options.accessibility.high_contrast.error.tooltip": "Resurspaketet för hög kontrast är inte tillgängligt", "options.accessibility.high_contrast.tooltip": "Förstärker kontrasten hos element i gränssnittet", "options.accessibility.high_contrast_block_outline": "Tydligare blockkonturer", "options.accessibility.high_contrast_block_outline.tooltip": "Förstärker konturens kontrast för blocket du tittar på.", "options.accessibility.link": "Tillgänglighetsguide", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON><PERSON>", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON><PERSON> hur o<PERSON><PERSON> b<PERSON>den ska vara hos menyer.", "options.accessibility.narrator_hotkey": "Kortkomm. för uppläsare", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON><PERSON><PERSON> det möjligt att aktivera eller inaktivera uppläsaren med kortkommandot \"Kmd + B\".", "options.accessibility.narrator_hotkey.tooltip": "<PERSON><PERSON><PERSON> det möjligt att aktivera eller inaktivera uppläsaren med kortkommandot \"Ctrl + B\".", "options.accessibility.panorama_speed": "Panoramahastighet", "options.accessibility.text_background": "Bakgrund fer tekst", "options.accessibility.text_background.chat": "<PERSON><PERSON><PERSON>", "options.accessibility.text_background.everywhere": "Ollsta'ss", "options.accessibility.text_background_opacity": "Son sir an gainum tekstn", "options.accessibility.title": "Tillgängligietsinstellningger...", "options.allowServerListing": "Luvlig serverlistningger", "options.allowServerListing.tooltip": "Ser<PERSON><PERSON> kann list uppkopplað-spilerer i dierases offentlig status. Bar du staindjer åv ittað alternativeð suynes int namneð dett i slaikų listur.", "options.ao": "Blot-ljuos", "options.ao.max": "So mitjið eð går", "options.ao.min": "So liteð ed går", "options.ao.off": "ÅV", "options.attack.crosshair": "Årkross", "options.attack.hotbar": "Hotbar", "options.attackIndicator": "Attakkmieter", "options.audioDevice": "<PERSON><PERSON><PERSON>", "options.audioDevice.default": "Systemstandard", "options.autoJump": "Upp autumatiskt", "options.autoSuggestCommands": "Föslag að kommando", "options.autosaveIndicator": "<PERSON><PERSON><PERSON> dar autospärern iir igangg", "options.biomeBlendRadius": "Ekosystiemblandningg", "options.biomeBlendRadius.1": "Off (Straiðest)", "options.biomeBlendRadius.11": "11x11 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.13": "13x13 (fer slaikker so wil wais sig duktigest)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (straitt)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (ogt)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON><PERSON><PERSON>)", "options.chat": "Chattinställningar...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Tjattferdröjningg: %s sikunder", "options.chat.delay_none": "Tjattferdröjningg: Indjin", "options.chat.height.focused": "Fokusirað öjd", "options.chat.height.unfocused": "Uofokusirað öjd", "options.chat.line_spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON> millǫ raðum", "options.chat.links": "Web-lekker", "options.chat.links.prompt": "<PERSON><PERSON> etter um lekker", "options.chat.opacity": "<PERSON><PERSON><PERSON> gainum tekstn", "options.chat.scale": "Sturliek ǫ buokstävir i tjattum", "options.chat.title": "Tjattinstellningger...", "options.chat.visibility": "<PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Waisað", "options.chat.visibility.hidden": "Gemd", "options.chat.visibility.system": "Bar orderer", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s sturbiter", "options.clouds.fancy": "S<PERSON>gg", "options.clouds.fast": "Straið", "options.controls": "<PERSON><PERSON><PERSON>er...", "options.credits_and_attribution": "Tillskrivningar...", "options.damageTiltStrength": "Vyskakning vid skada", "options.damageTiltStrength.tooltip": "<PERSON>r mycket kameran skakar när du tar skada.", "options.darkMojangStudiosBackgroundColor": "Monokrom logotyp", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON> bakkgrundsferdjin Mojang Studios leddninggsstjerm að swart.", "options.darknessEffectScale": "Mörkneð så pulsirer", "options.darknessEffectScale.tooltip": "Bistemm ur mitjið mörkineffektn al pulsir dar du får an min ien wakt elld ien skulkkuler.", "options.difficulty": "Swårugiet", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Fientliga varelser skapas men orsakar mindre skada. Hungermätaren sjunker och tär på hälsan ned till fem hjärtan.", "options.difficulty.hard": "Swår", "options.difficulty.hard.info": "Fientliga varelser skapas och orsakar mer skada. Hungermätaren sjunker och tär på all hälsa.", "options.difficulty.hardcore": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Fientliga varelser skapas och orsakar normal skada. Hungermätaren sjunker och tär på hälsan ned till ett halvt hjärta.", "options.difficulty.online": "<PERSON>r swår servern al wårå", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Inga fientliga varelser och endast några neutrala varelser skapas. Hungermätaren sjunker inte och hälsa återhämtas över tid.", "options.directionalAudio": "<PERSON><PERSON> liu<PERSON>ð so går daiter an sykter", "options.directionalAudio.off.tooltip": "Klassisk stereoliuoð", "options.directionalAudio.on.tooltip": "Bruker HRTF-basirað riktaðliuoð fer te djärå simulasiuon ov 3D-liuoð wildera. An får luv åvå HRTF-kompatibel liuoðårdwaru og funggirer wildest min ärlurą.", "options.discrete_mouse_scroll": "Fesiktug tyllningg", "options.entityDistanceScaling": "Ienietsavstond", "options.entityShadows": "Skromtskuggur", "options.font": "Typsnittsinställningar...", "options.font.title": "Typsnittsinställningar", "options.forceUnicodeFont": "Bruk Unicodetypsnitt", "options.fov": "FOV", "options.fov.max": "Skåvproffs", "options.fov.min": "Normal", "options.fovEffectScale": "FOV-effekter", "options.fovEffectScale.tooltip": "Ittað styrer ur mitjið synfelteð endras min effektum i spilę.", "options.framerate": "%s fps", "options.framerateLimit": "<PERSON><PERSON> bild<PERSON>t", "options.framerateLimit.max": "Ųobegrensað", "options.fullscreen": "Sturstjärm", "options.fullscreen.current": "Slaikt so ir nų", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Sturstjärmsupplösning, instellningger", "options.fullscreen.unavailable": "Instellningger för fullstjärm går it fą̊ i", "options.gamma": "<PERSON><PERSON> <PERSON><PERSON> l<PERSON>", "options.gamma.default": "Standard", "options.gamma.max": "<PERSON><PERSON>", "options.gamma.min": "Skumt", "options.generic_value": "%s: %s", "options.glintSpeed": "Skimmerhastighet", "options.glintSpeed.tooltip": "Styr hur snabbt det synliga skimret glänser över förtrollade föremål.", "options.glintStrength": "Skimmerstyrka", "options.glintStrength.tooltip": "Styr hur transparent det synliga skimret på förtrollade föremål är.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "Ųogråtteli!", "options.graphics.fabulous.tooltip": "%s grafik bruk stj<PERSON>rmskugger fer te rit weðer, muolner og gräjur attonað blokk eð går sjǫ gainum og wattneð. \nIttað-jär beller djäv stur probliem fer bärbar ienieter og 4K-stjärmer.", "options.graphics.fancy": "S<PERSON>gg", "options.graphics.fancy.tooltip": "Ljuotgrann grafik balansirer prestanda og kwalitet fer mjäst oll ienieter. E<PERSON> beller werd so an sir it weðer, muolner og elelr gr<PERSON><PERSON>r attonað blokk an dug sjǫ gainum elld wattneð.", "options.graphics.fast": "Straið", "options.graphics.fast.tooltip": "Straið grafik drar nið ǫ ur mitjið ov raingnę og sniuon an sir. Ur mitjið an sir gainum ir åvstainggt fer uolaiku blokk sos lover upi traim.", "options.graphics.warning.accept": "Fortsätt utan stöd", "options.graphics.warning.cancel": "Gå tillbaka", "options.graphics.warning.message": "Wartum iwari grafikienietn den ar it noð styöð fer %s alternativeð. \nDu beller sakt int uonda eð og fuortsett, men du dug it fǫ non jåp för ienietn den um du wel te bruk %s grafitjin.", "options.graphics.warning.renderer": "Upptekt renderer: [%s]", "options.graphics.warning.title": "Grafikienietn ar it noð styöð", "options.graphics.warning.vendor": "Upptekt leverantör: [%s]", "options.graphics.warning.version": "YppenGL-versiuon upptekt: [%s]", "options.guiScale": "Grenssnittsskala", "options.guiScale.auto": "Automatisk", "options.hidden": "Gemd", "options.hideLightningFlashes": "Gem undą̊ lysur så blakker", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON> blix<PERSON><PERSON><PERSON><PERSON> från att lysa upp himlen. <PERSON>j<PERSON><PERSON>va blixten kommer fortfarande vara synlig.", "options.hideMatchedNames": "<PERSON>em nammner so iro la<PERSON>d", "options.hideMatchedNames.tooltip": "Tredjepartsservrar kan skicka chattmeddelanden som inte är i standardformatet.\nNär detta aktiveras kommer dolda spelare paras ihop baserat på avsändarnamnet.", "options.hideSplashTexts": "<PERSON><PERSON><PERSON><PERSON> splash-text", "options.hideSplashTexts.tooltip": "<PERSON><PERSON><PERSON><PERSON> den gula splash-texten i huvudmenyn.", "options.inactivityFpsLimit": "<PERSON>änk bildfrek.", "options.inactivityFpsLimit.afk": "Vid inaktivitet", "options.inactivityFpsLimit.afk.tooltip": "Begränsar bildfrekvensen till 30 bilder per sekund när spelet inte mottar någon inmatning på över en minut. Bildfrekvensen begränsas efter ytterligare 9 minuter till 10 bilder per sekund.", "options.inactivityFpsLimit.minimized": "Vid minimering", "options.inactivityFpsLimit.minimized.tooltip": "Begränsar bildfrekvensen endast när spelfönstret är minimerat.", "options.invertMouse": "Invertir mausę", "options.japaneseGlyphVariants": "Japanska skrivtecken", "options.japaneseGlyphVariants.tooltip": "Använder japanska varianter av CJK-tecken i standardtypsnittet.", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Umkoppler", "options.language": "Glam...", "options.language.title": "Språk", "options.languageAccuracyWarning": "(Översättningarna är möjligtvis inte 100 %% korrekta)", "options.languageWarning": "Yvyrsettningger kastji iro it 100%% riktuger", "options.mainHand": "Fuost nevą̊", "options.mainHand.left": "Wįster", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap-niv<PERSON><PERSON>", "options.modelPart.cape": "Mantel", "options.modelPart.hat": "Att", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Wįster bruokuosu", "options.modelPart.left_sleeve": "Wįster erme", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON> br<PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON> erme", "options.mouseWheelSensitivity": "Tjenslugiet för ty'llning", "options.mouse_settings": "Mausinstellningger...", "options.mouse_settings.title": "Mausinstellningger", "options.multiplayer.title": "Flierspilerinstellningger...", "options.multiplier": "%sx", "options.music_frequency": "Musikuppspelning", "options.music_frequency.constant": "<PERSON><PERSON>n", "options.music_frequency.default": "Standard", "options.music_frequency.frequent": "Ofta", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON> hur ofta musik spelas i en spelvärld.", "options.narrator": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.all": "Ollt werd upplesið", "options.narrator.chat": "<PERSON> upp tjatt", "options.narrator.notavailable": "Gǫr it fǫ i", "options.narrator.off": "ÅV", "options.narrator.system": "<PERSON> upp systiem", "options.notifications.display_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> för notiser", "options.notifications.display_time.tooltip": "Påverkar hur länge notiser stannar kvar på skärmen.", "options.off": "ÅVSTAINGGD", "options.off.composed": "%s: Atte", "options.on": "Ą̊SETT", "options.on.composed": "%s: Ǫsett", "options.online": "Uppkopplað...", "options.online.title": "Uppkopplað-alternativ", "options.onlyShowSecureChat": "Wais bar säker tjatt", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON><PERSON><PERSON> upp bar mieðdielend frą̊ spilerer so irå guoðtjenner og ir int endraðer.", "options.operatorItemsTab": "Operativ föremålsflik", "options.particles": "Smą̊dieler", "options.particles.all": "Ollt so ir", "options.particles.decreased": "Ferminskað", "options.particles.minimal": "So litn eð gą̊r", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Datablokksbygger", "options.prioritizeChunkUpdates.byPlayer": "Åv-blok<PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Noð du djär i ienum datablokkę kastji uppdatirer dataklokkeð. Eð kann ennd dar an ev aut datablokk elld förstörer diem.", "options.prioritizeChunkUpdates.nearby": "Ielstuppað", "options.prioritizeChunkUpdates.nearby.tooltip": "Datablokk i nerietn werd olltiett uppdatiraðer rað weg. Kast<PERSON> eð påwerker ur spileð funggirer dar an ev aut datablokk elld förstörer diem.", "options.prioritizeChunkUpdates.none": "Tråðeð", "options.prioritizeChunkUpdates.none.tooltip": "Datablokk i nerietn werd uppdatiraðer i parallelltråðum. Kanenda eð werd tilfelluger uol er dar blokk werd festörder.", "options.rawMouseInput": "Rå inmätningg", "options.realmsNotifications": "Realms-upp<PERSON><PERSON>ar", "options.realmsNotifications.tooltip": "Hämtar nyheter och inbjudningar för Realms på huvudmenyn och visar respektive ikon på Realms-knappen.", "options.reducedDebugInfo": "<PERSON><PERSON><PERSON>formasiuo<PERSON>", "options.renderClouds": "<PERSON><PERSON><PERSON>", "options.renderCloudsDistance": "Molnavstånd", "options.renderDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> sos synes", "options.resourcepack": "Resurspakiet...", "options.rotateWithMinecart": "Rotera vy med gruvvagn", "options.rotateWithMinecart.tooltip": "Huruvida spelarens vy ska rotera tillsammans med gruvvagnen. Endast tillgängligt i världar där den experimentella inställningen \"Gruvvagnsförbättringar\" är aktiverad.", "options.screenEffectScale": "Few<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.screenEffectScale.tooltip": "<PERSON>r stark stjärmförwrenggningseffektn og ur klien an kenner sig. Um minnera: werd eð ien g<PERSON>ön dimb i stelleð.", "options.sensitivity": "Tjenslugiet", "options.sensitivity.max": "<PERSON><PERSON><PERSON>t!!!", "options.sensitivity.min": "*jä<PERSON>*", "options.showNowPlayingToast": "Visa musikavisering", "options.showNowPlayingToast.tooltip": "Visar en avisering när en låt börjar spelas. Samma avisering visas hela tiden i spelets pausmeny medan låten spelas.", "options.showSubtitles": "<PERSON><PERSON> tekster niðoni", "options.simulationDistance": "Djiet -avstond", "options.skinCustomisation": "Wel ur skaleð al sjå aut...", "options.skinCustomisation.title": "Wel ur skaleð al sjå aut", "options.sounds": "Musik og liuoð...", "options.sounds.title": "Musik- og ljuoðwal", "options.telemetry": "Telemetridata...", "options.telemetry.button": "Datainsamling", "options.telemetry.button.tooltip": "\"%s\" inkluderar endast nödvändig data.\n\"%s\" inkluderar både valfri och nödvändig data.", "options.telemetry.disabled": "Telemetridata är inaktiverat.", "options.telemetry.state.all": "Allting", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "Ingen", "options.title": "Alternativ", "options.touchscreen": "Arp ą̊-läge", "options.video": "Videoinstellningger...", "options.videoTitle": "Videoinstellningger", "options.viewBobbing": "<PERSON><PERSON> stuss<PERSON>g", "options.visible": "Waisað", "options.vsync": "VSync", "outOfMemory.message": "Minecraft ar faið slut ą̊ minne. \nKanenda eð ir ien laus so stellt til ed elld og add Java Virtual masin int minne so eð rakk til. \nSpileð wart åvbutið för dyö dain spilfiler ulld it skåðås. Amm buoðið til fą̊ fram mier minne fer dig te kumo etbaker að ovuðmenyn, men kastji eð funggireð it. \nTjäre ev igangg spileð et nyes um du sir itað-jär mieðdielend atten.", "outOfMemory.title": "Slut ą̊ minne!", "pack.available.title": "G<PERSON>r fǫ i", "pack.copyFailure": "Eð djikk it kopiir pakiet", "pack.dropConfirm": "Will du legg till issu-j<PERSON>rpakieter i Minecraft?", "pack.dropInfo": "Drag og slepp filer ini ittað-jär glaseð te legg til pakiet", "pack.dropRejected.message": "Följande poster var inte giltiga paket och kopierades inte:\n %s", "pack.dropRejected.title": "<PERSON>er som inte är paket", "pack.folderInfo": "(<PERSON>v pakiet-filer jän)", "pack.incompatible": "Ittað stemmer it noð", "pack.incompatible.confirm.new": "Ittað-jär r<PERSON> ir gart fer ien nyera Mainkraftversiuon og funggirer kanstji it sos eð al.", "pack.incompatible.confirm.old": "Ittað-j<PERSON><PERSON> paki<PERSON> ir gart fer ien gamblera versiuon ov Minecraft og kastji fungirer it riktugt noð nų.", "pack.incompatible.confirm.title": "Ir du säker du will leð in ittað-jär pakiteteð?", "pack.incompatible.new": "(<PERSON><PERSON> fer ien nyera Mainkraftversiuon)", "pack.incompatible.old": "(<PERSON><PERSON> fer ien gamblera Mainkraftversiuon)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Ev upp mappin fer pakiet", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "inbyggt", "pack.source.feature": "funktion", "pack.source.local": "lokal", "pack.source.server": "server", "pack.source.world": "värld", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Alban", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Bakgård", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON> har bombats", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Dödskalle i lågor", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Byst", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Grottfågel", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Skymd kustvy", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON> bossen", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Ormbunke", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Kampsport", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON>d", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Ödmjukhet", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Tändsticka", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativ", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Ljusklot", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Uggla med citroner", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Grisporträtt", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Utpekad", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON><PERSON>", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Jordiska bojor", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Dödskalle och rosor", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON>n är din", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Solrosor", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Uppackad", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Ödemark", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Vatten", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Slumpmässig variant", "parsing.bool.expected": "Fewentað booleskt wärde", "parsing.bool.invalid": "Ųodjiltut boolesk-wärde, wenteð 'sant' elld 'falskt' men itted %s", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON>", "parsing.double.invalid": "Ųodjiltut dubbeltal '%s'", "parsing.expected": "Fewentad '%s'", "parsing.float.expected": "Fewentað fliuottal", "parsing.float.invalid": "Odjiltut fliuottal '%s'", "parsing.int.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "parsing.int.invalid": "Ųodjiltut ieltal %s", "parsing.long.expected": "<PERSON><PERSON><PERSON><PERSON> long-wärde", "parsing.long.invalid": "Ųodjiltut loig-wärde '%s'", "parsing.quote.escape": "Ųodjiltug undǫtagssekvens '\\%s' i tråðn so war andjivin", "parsing.quote.expected.end": "Tråðn ar int noð sitattekkn ą̊ slutę", "parsing.quote.expected.start": "Sitasjuonstekkneð edd ulað byrą̊ ien tråð", "particle.invalidOptions": "Kan inte tolka partikelalternativ: %s", "particle.notFound": "Ųokunnun partikkel: %s", "permissions.requires.entity": "<PERSON><PERSON> får luv wårå ien ieniet fer te bruk ittað-jär kommando jär", "permissions.requires.player": "<PERSON><PERSON> får luv wår<PERSON> ien spiler fer te bruk ittað-jär kommando jär", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Mes an brukes:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Ųokunnut predikat: %s", "quickplay.error.invalid_identifier": "Kunde inte hitta en värld med den angivna identifieraren", "quickplay.error.realm_connect": "Kunde inte ansluta till Realm", "quickplay.error.realm_permission": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON><PERSON>t att ansluta till denna Realm", "quickplay.error.title": "Direktinläsningen misslyckades", "realms.configuration.region.australia_east": "New South Wales, Australien", "realms.configuration.region.australia_southeast": "Victoria, Australien", "realms.configuration.region.brazil_south": "Brasilien", "realms.configuration.region.central_india": "Indien", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Japan, östra", "realms.configuration.region.japan_west": "Japan, västra", "realms.configuration.region.korea_central": "Sydkorea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sverige", "realms.configuration.region.uae_north": "Förenade arabemiraten", "realms.configuration.region.uk_south": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Nederländerna", "realms.configuration.region.west_us": "Kalifornien, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatiskt (Realmens ägare)", "realms.configuration.region_preference.automatic_player": "Automatiskt (första anslutna spelaren)", "realms.missing.snapshot.error.text": "<PERSON><PERSON> går it dj<PERSON>rå snettbilder ov Realm just nų", "recipe.notFound": "Uokunnut risept: %s", "recipe.toast.description": "<PERSON><PERSON><PERSON> i buotsję för resept", "recipe.toast.title": "Nyu resept upplęster!", "record.nowPlaying": "Spiler nų: %s", "recover_world.bug_tracker": "Rapportera bugg", "recover_world.button": "Försök återskapa", "recover_world.done.failed": "Misslyckades att återskapa från ett tidigare tillstånd.", "recover_world.done.success": "Världen återskapades!", "recover_world.done.title": "Återskapande slutfördes", "recover_world.issue.missing_file": "fil saknas", "recover_world.issue.none": "inga fel", "recover_world.message": "Följande fel uppstod när världsmappen \"%s\" lästes in.\nDet kan vara möjligt att återskapa världen från ett äldre tillstånd eller så kan du rapportera detta fel som en bugg.", "recover_world.no_fallback": "Inga tillgängliga tillstånd att återskapa från", "recover_world.restore": "Försök återställa", "recover_world.restoring": "Försöker återställa värld...", "recover_world.state_entry": "Tillstånd från %s: ", "recover_world.state_entry.unknown": "<PERSON><PERSON><PERSON>", "recover_world.title": "Misslyckades att läsa in världen", "recover_world.warning": "Misslyckades att läsa in världsbeskrivning", "resourcePack.broken_assets": "<PERSON><PERSON><PERSON> so ar feð sund iro upptekkter", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON> k<PERSON>", "resourcePack.load_fail": "Risussą̊fesskningg funggireð it", "resourcePack.programmer_art.name": "Programmeringskonst", "resourcePack.runtime_failure": "<PERSON>l upptäcktes i resurspaketet", "resourcePack.server.name": "Resusser fer uolaiku wärder", "resourcePack.title": "Wel iet risuss<PERSON>t", "resourcePack.vanilla.description": "Minecrafts standardutseende och -känsla", "resourcePack.vanilla.name": "Standard", "resourcepack.downloading": "<PERSON><PERSON><PERSON> nið ris<PERSON><PERSON>t", "resourcepack.progress": "<PERSON><PERSON>er nid fil (%s MB)...", "resourcepack.requesting": "Stji<PERSON> åv bidjäran...", "screenshot.failure": "Dugd int spårå stjärmbild: %s", "screenshot.success": "Späreð stjärmbild sos %s", "selectServer.add": "Legg að server", "selectServer.defaultName": "Minecraftserver", "selectServer.delete": "Tag dieðå", "selectServer.deleteButton": "Tag dieðå", "selectServer.deleteQuestion": "<PERSON>r du säker du will tag dieðo isn-jär <PERSON><PERSON>?", "selectServer.deleteWarning": "'%s' kumb te feswið fer olltiett! (Uvljuo'tt ljuotlaindj!)", "selectServer.direct": "Direktanslutningg", "selectServer.edit": "Redigira", "selectServer.hiddenAddress": "(Gemd)", "selectServer.refresh": "Fesk ą̊", "selectServer.select": "Go minn i servern", "selectWorld.access_failure": "Kam it in i wärdę", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON> fuska", "selectWorld.allowCommands.info": "Kommandon sos /spilstellningg/erfareniet", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON> kommandon", "selectWorld.backupEraseCache": "Tag brott data du ar spåråð", "selectWorld.backupJoinConfirmButton": "Säkerhetskopiira och les in", "selectWorld.backupJoinSkipButton": "Ig wet wen ig djär!", "selectWorld.backupQuestion.customized": "Anpassaðwärder går it bruk laingger", "selectWorld.backupQuestion.downgrade": "<PERSON><PERSON> går int graðir nið ien wärd", "selectWorld.backupQuestion.experimental": "Wärder so bruker eksperimentinstellningger går it bruka", "selectWorld.backupQuestion.snapshot": "Will du kanend les in isų-jär wärd<PERSON>?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON>, men styöðum it anpassadwärder i issn-jär wersiuon ov Minecraft. Dugum sakt leð in an og biwårå ollt sos eð war, men oll terräng du gard nylit kumb it te funggir. Liessn eð wart knevlut að dig!", "selectWorld.backupWarning.downgrade": "Sienset issu-jär wärdę wart spilåð war eð i versiuon %s; du ar versiuon %s. Um an graðirer nið ien wärd kann eð werd probliem, - dugum it luvå eð går ladda elld eð funggirer sę. Um du al fuortsett, wari frek og djär dig ien säkerietskopia!", "selectWorld.backupWarning.experimental": "Issu-jär wärdę bruker eksperimentell instellningger so beller slut funggir fram og fram. Dugum it djäv garantier eð gor leð nið elld funggirer. War fesiktun!", "selectWorld.backupWarning.snapshot": "Issų-jär wärdę wart spilåð i wersiuon %s sienest; du ir åi wersiuon %s. War frek og djär įe säkerietskopi um du werd i wari någu skåðå i wärdn!", "selectWorld.bonusItems": "Bonus<PERSON>ji<PERSON>", "selectWorld.cheats": "fusk", "selectWorld.commands": "kommandon", "selectWorld.conversion": "An fą̊r luv konvertir ittað!", "selectWorld.conversion.tooltip": "An får luv ev upp issu-jär wärd<PERSON> i ien gamblera versiuon (Kastji 1.6.4) fer dyö eð al werd riktut", "selectWorld.create": "<PERSON><PERSON><PERSON>r įe ny wärd", "selectWorld.customizeType": "Anpass", "selectWorld.dataPacks": "Datapakiet", "selectWorld.data_read": "Leser wärdsdata...", "selectWorld.delete": "Tag brott", "selectWorld.deleteButton": "NEaeae", "selectWorld.deleteQuestion": "Ir ðu säker du will tag brott issu-jär wärd<PERSON>?", "selectWorld.deleteWarning": "'%s' kumb te feswið fer evigiet! (Uvliuo'tt laindje!)", "selectWorld.delete_failure": "Dugd it tag dieðo wärdn", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backupCreated": "\"Säkerietskopireð\": %s", "selectWorld.edit.backupFailed": "Eð djikk it noð bra djärå säkerietskopia", "selectWorld.edit.backupFolder": "Ev upp mappin min säkerietskopium", "selectWorld.edit.backupSize": "sturliek: %s MB", "selectWorld.edit.export_worldgen_settings": "Eksportir instellningger fer te djäro wärder", "selectWorld.edit.export_worldgen_settings.failure": "Eð djikk it noð bra eksportira", "selectWorld.edit.export_worldgen_settings.success": "Eksportiraðer", "selectWorld.edit.openFolder": "Ev upp wä<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.optimize": "<PERSON><PERSON><PERSON><PERSON> wä<PERSON>n oll best", "selectWorld.edit.resetIcon": "Sett igangg ikon et nyes", "selectWorld.edit.save": "Spårå", "selectWorld.edit.title": "<PERSON><PERSON>", "selectWorld.enterName": "<PERSON><PERSON><PERSON> wärd<PERSON>", "selectWorld.enterSeed": "Frie að wärds<PERSON>em", "selectWorld.experimental": "Experimentell", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Experimentella funktioner som krävs: %s", "selectWorld.experimental.details.title": "<PERSON><PERSON><PERSON> för experimentella funktioner", "selectWorld.experimental.message": "Var försiktig!\nDenna konfiguration kräver funktioner som fortfarande är under utveckling. Din värld kan krascha, bli korrupt eller inte fungera med framtida uppdateringar.", "selectWorld.experimental.title": "Varning om experimentella funktioner", "selectWorld.experiments": "Experiment", "selectWorld.experiments.info": "Experiment är nya potentiella funktioner. Var försiktig då saker kan gå sönder. Experiment kan inte stängas av efter att världen har skapats.", "selectWorld.futureworld.error.text": "Ed war noð so wart tuokut mes ðu boð til leð ini įe wärd frą̊ ien wersiuon so it finnas än. Itta-jär war ien farlig operatiuon frą̊ byårnendam. Ig ir liessn eð funggired it.", "selectWorld.futureworld.error.title": "Eð wart noð fil!", "selectWorld.gameMode": "Spilstellningg", "selectWorld.gameMode.adventure": "Äventyr", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON> som överlevnadsläget, men block kan inte placeras eller förstöras.", "selectWorld.gameMode.adventure.line1": "Summu og yvyrlievnadsstellningg, men blokk dugo it", "selectWorld.gameMode.adventure.line2": "legg til elld tag brott", "selectWorld.gameMode.creative": "K<PERSON><PERSON>v", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, bygg och utforska helt utan gränser. <PERSON> kan flyga, använda obegränsat med resurser och skadas inte av monster.", "selectWorld.gameMode.creative.line1": "Obegrä<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fri fljuogningg og", "selectWorld.gameMode.creative.line2": "fest<PERSON>r blokk rað weg", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Överlevnadsläget är låst till svårighetsgraden \"Svår\". Du kan inte återuppstå om du dör.", "selectWorld.gameMode.hardcore.line1": "Summu og yvyrlievnadsstellningg, lęst ą̊ skarpest", "selectWorld.gameMode.hardcore.line2": "swårigiet og bar iett liw", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "Du kan se men inte röra.", "selectWorld.gameMode.spectator.line1": "Du dug sją̊ men it ryöra", "selectWorld.gameMode.survival": "Yvyrlievnað", "selectWorld.gameMode.survival.info": "Utforska en mystisk värld där du bygger, sam<PERSON>, till<PERSON><PERSON> och bekämpar monster.", "selectWorld.gameMode.survival.line1": "<PERSON>t etter resursum, skapa, winna", "selectWorld.gameMode.survival.line2": "<PERSON><PERSON><PERSON><PERSON>, elsa och unggern", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Importir instellningger", "selectWorld.import_worldgen_settings.failure": "<PERSON>ð wart nod fil med instellningger wart importiraðer", "selectWorld.import_worldgen_settings.select_file": "Wel instellninggsfil (.json)", "selectWorld.incompatible.description": "Den här världen kan inte öppnas i den här versionen.\nVärlden spelades senast i version %s.", "selectWorld.incompatible.info": "Inkompatibel version: %s", "selectWorld.incompatible.title": "Inkompatibel version", "selectWorld.incompatible.tooltip": "Den här världen kan inte öppnas eftersom den skapades i en inkompatibel version.", "selectWorld.incompatible_series": "<PERSON><PERSON><PERSON><PERSON> min ien versiion so int går bruka", "selectWorld.load_folder_access": "<PERSON> djikk it leså eld kum að mappin dar spilwärder irå spåråðer!", "selectWorld.loading_list": "<PERSON><PERSON> wärd", "selectWorld.locked": "<PERSON>ęst att ien elller instans ov Minecraft", "selectWorld.mapFeatures": "Djärå strukturer", "selectWorld.mapFeatures.info": "Byar, skeppsvrak etc.", "selectWorld.mapType": "Wärdenes typ", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Flierų wärdsalternativ...", "selectWorld.newWorld": "<PERSON><PERSON>", "selectWorld.recreate": "Djär um wärdn", "selectWorld.recreate.customized.text": "Anpassaðwärder får it noð styöð laingger i issn-jär versiuonem ov Minecraft. Bellum bjuoð til djär um wärdn et nyes min summu frie och iegenskaper. men anpassningger ov terränddję kumb te feswinna. Irum lissner för ollt biswär!", "selectWorld.recreate.customized.title": "Anpassaðwärder går it bruk laingger", "selectWorld.recreate.error.text": "Noð wart tuokut dar an boð til djärå įe wärd et nyes.", "selectWorld.recreate.error.title": "Eð wart noð fil!", "selectWorld.resource_load": "Förbereder resurser...", "selectWorld.resultFolder": "Kumb te spårås ini:", "selectWorld.search": "liet etter wärder", "selectWorld.seedInfo": "Lat wårå tuämt fer iet slumpmessut säðskuonn", "selectWorld.select": "Spilå an wärdn du ar welt", "selectWorld.targetFolder": "Sparas i mappen: %s", "selectWorld.title": "<PERSON><PERSON> wärd", "selectWorld.tooltip.fromNewerVersion1": "<PERSON><PERSON><PERSON><PERSON> wart spåråð i ien nyera wersiuon,", "selectWorld.tooltip.fromNewerVersion2": "te leð in isų-jär wärdę beller djäv dig prubliem!", "selectWorld.tooltip.snapshot1": "Glem it åv te djärå säkerietskopi ov issu-jär wärdę", "selectWorld.tooltip.snapshot2": "inno ðu leð in on i issn-jär eksperimentwersiuon.", "selectWorld.unable_to_load": "Dugd it leð wärder", "selectWorld.version": "wersiuon:", "selectWorld.versionJoinButton": "Les in endą̊", "selectWorld.versionQuestion": "Will du kanend les in isų-jär wärd<PERSON>?", "selectWorld.versionUnknown": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.versionWarning": "Sienest isų-j<PERSON>r wärd<PERSON> wart spilåð war eð i wersiuon '%s' og um du les in issn wersiuon beller eð werd upi tuok!", "selectWorld.warning.deprecated.question": "Vissa funktioner som används är inaktuella och kommer sluta fungera i framtiden. Vill du fortsätta?", "selectWorld.warning.deprecated.title": "Varning! Dessa inställningar använder inaktuella funktioner", "selectWorld.warning.experimental.question": "Dessa inställningar är experimentella och kan sluta fungera i framtiden. Vill du fortsätta?", "selectWorld.warning.experimental.title": "Varning! Dessa inställningar använder experimentella funktioner", "selectWorld.warning.lowDiskSpace.description": "Det finns inte mycket utrymme kvar på din enhet.\nOm diskutrymmet tar slut medan du spelar kan det leda till att din värld skadas.", "selectWorld.warning.lowDiskSpace.title": "Varning! För lite diskutrymme!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "Dj<PERSON>r um mi<PERSON>end ǫ stjyltem", "sleep.not_possible": "Notę feswið it ur mitjið enn eð wailes", "sleep.players_sleeping": "%s/%s spilerer so såvå", "sleep.skipping_night": "<PERSON><PERSON><PERSON> iel notę", "slot.only_single_allowed": "Endast en plats är till<PERSON>, \"%s\" hittades", "slot.unknown": "Ųokunnun spryndja: '%s'", "snbt.parser.empty_key": "Nyckeln kan inte vara tom", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON> bin<PERSON>rt tal förväntades", "snbt.parser.expected_decimal_numeral": "Ett decimaltal förväntades", "snbt.parser.expected_float_type": "Ett flyttal förväntades", "snbt.parser.expected_hex_escape": "En teckensekvens med längden %s förväntades", "snbt.parser.expected_hex_numeral": "Ett hexadecimalt tal förväntades", "snbt.parser.expected_integer_type": "<PERSON><PERSON> he<PERSON>l förvä<PERSON>", "snbt.parser.expected_non_negative_number": "<PERSON><PERSON> i<PERSON>-negativt tal förväntades", "snbt.parser.expected_number_or_boolean": "Ett tal eller booleskt värde förväntades", "snbt.parser.expected_string_uuid": "En sträng som motsvarar ett giltigt UUID förväntades", "snbt.parser.expected_unquoted_string": "En giltig sträng utan citation<PERSON>cken förväntades", "snbt.parser.infinity_not_allowed": "Endast ändliga tal är tillåtna", "snbt.parser.invalid_array_element_type": "Ogiltig typ av arrayelement", "snbt.parser.invalid_character_name": "Ogiltigt namn på Unicode-tecken", "snbt.parser.invalid_codepoint": "%s är ett ogiltigt värde för Unicode-tecken", "snbt.parser.invalid_string_contents": "Ogiltigt stränginnehåll", "snbt.parser.invalid_unquoted_start": "Strängar utan <PERSON>n kan inte börja med siffrorna 0–9, + el<PERSON> -", "snbt.parser.leading_zero_not_allowed": "Decimaltal kan inte börja med 0", "snbt.parser.no_such_operation": "Ingen sådan åtgärd hittades: %s", "snbt.parser.number_parse_failure": "Misslyckades att tolka talet: %s", "snbt.parser.undescore_not_allowed": "<PERSON><PERSON> får inte bö<PERSON><PERSON> eller sluta med understreck", "soundCategory.ambient": "Slaikt so ir i nerietn/milj<PERSON>", "soundCategory.block": "Blokk", "soundCategory.hostile": "Jälokkrytyr", "soundCategory.master": "Ovudvolym", "soundCategory.music": "Mu<PERSON>", "soundCategory.neutral": "Frekåkrytyr", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Spilerer/skriervbuok", "soundCategory.ui": "Gränssnitt", "soundCategory.voice": "Mǫl/glam", "soundCategory.weather": "<PERSON><PERSON><PERSON>", "spectatorMenu.close": "Tepp att meny", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON> so kumb", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.root.prompt": "Tryttj ą̊ ien knapp te wel kommando og iessn til fer te bruk eð.", "spectatorMenu.team_teleport": "Teleport till lagkompis", "spectatorMenu.team_teleport.prompt": "Wel aut iet lag te teleportir að", "spectatorMenu.teleport": "Teleportir að spileram", "spectatorMenu.teleport.prompt": "Wel aut ien spiler te teleportir að", "stat.generalButton": "<PERSON><PERSON>", "stat.itemsButton": "<PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "<PERSON><PERSON><PERSON><PERSON> du ar taið fram", "stat.minecraft.aviate_one_cm": "Elytraavstånd", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON><PERSON> so å<PERSON>å bambað", "stat.minecraft.boat_one_cm": "<PERSON><PERSON> langgt båten ar ferið", "stat.minecraft.clean_armor": "<PERSON><PERSON><PERSON> rustninggsdieler", "stat.minecraft.clean_banner": "<PERSON><PERSON><PERSON>", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON>", "stat.minecraft.climb_one_cm": "<PERSON>r langgt du ar kleðrað", "stat.minecraft.crouch_one_cm": "<PERSON>r langgt du ar gryv<PERSON>ð", "stat.minecraft.damage_absorbed": "Skåðå absorbirað", "stat.minecraft.damage_blocked_by_shield": "Skåðå blokkirað min stjöld", "stat.minecraft.damage_dealt": "Autdielað skåðå", "stat.minecraft.damage_dealt_absorbed": "Autdielað skåðå (Asorbirað)", "stat.minecraft.damage_dealt_resisted": "Autdielað <PERSON>k<PERSON> (muotstådd)", "stat.minecraft.damage_resisted": "Skåðå muostendeð", "stat.minecraft.damage_taken": "Skåðå an ar faið", "stat.minecraft.deaths": "So mikkel gangger ar an däer", "stat.minecraft.drop": "Tappaðgrejur", "stat.minecraft.eat_cake_slice": "Uppj<PERSON><PERSON><PERSON> tårtbiter", "stat.minecraft.enchant_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> grejur", "stat.minecraft.fall_one_cm": "So langgt ar ðu fellið", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON> ketiller", "stat.minecraft.fish_caught": "Fanggað fisk", "stat.minecraft.fly_one_cm": "<PERSON>r langgt du ar flutið", "stat.minecraft.happy_ghast_one_cm": "<PERSON><PERSON><PERSON> med glad ghast", "stat.minecraft.horse_one_cm": "<PERSON>r langgt du ar riðið", "stat.minecraft.inspect_dispenser": "Uppkollað automater", "stat.minecraft.inspect_dropper": "Upp<PERSON><PERSON><PERSON> pipetter", "stat.minecraft.inspect_hopper": "Upp<PERSON><PERSON><PERSON> tratter", "stat.minecraft.interact_with_anvil": "Samarbiet min steð", "stat.minecraft.interact_with_beacon": "Samarbiet min fyrer", "stat.minecraft.interact_with_blast_furnace": "Samarbiet min smeltungnum", "stat.minecraft.interact_with_brewingstand": "Samarbiet min bryggdstellninggär", "stat.minecraft.interact_with_campfire": "Samarbiet min lägerjäldem", "stat.minecraft.interact_with_cartography_table": "Samarbiet min kartografbuordę", "stat.minecraft.interact_with_crafting_table": "Samarbiet min arbietsbaintjin", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON><PERSON> min ungum", "stat.minecraft.interact_with_grindstone": "<PERSON><PERSON><PERSON><PERSON> min slipstie", "stat.minecraft.interact_with_lectern": "Samarbiet min katedrem", "stat.minecraft.interact_with_loom": "Samarbiet min wevstoulum", "stat.minecraft.interact_with_smithing_table": "Samarbiet min smiðbuord", "stat.minecraft.interact_with_smoker": "Samarbiet min ryöker", "stat.minecraft.interact_with_stonecutter": "Samarbiet min stįeogger", "stat.minecraft.jump": "Upp", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spiler", "stat.minecraft.minecart_one_cm": "<PERSON>r langt an ar ferið min gruvwangen", "stat.minecraft.mob_kills": "<PERSON><PERSON>", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_chest": "Yppnadtjistur", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_shulker_box": "Ypp<PERSON>ð shulkerlådur", "stat.minecraft.pig_one_cm": "<PERSON>r langgt du ar ferið ą̊ graisem", "stat.minecraft.play_noteblock": "Antekkninggsblokk spilåðer", "stat.minecraft.play_record": "Musikst<PERSON><PERSON><PERSON>", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> tið", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "Krukplantirað wekster", "stat.minecraft.raid_trigger": "Plundringger autlöster", "stat.minecraft.raid_win": "Plundringger uänneð", "stat.minecraft.sleep_in_bed": "<PERSON>ann<PERSON> an ar sovið i ien sa<PERSON>g", "stat.minecraft.sneak_time": "Smygtið", "stat.minecraft.sprint_one_cm": "Sigelkautdistans", "stat.minecraft.strider_one_cm": "<PERSON>r langgt du ar riðið upǫ ien striðer", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> simma<PERSON>", "stat.minecraft.talked_to_villager": "Akkudireð min byskallum", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> so iro treffaðer", "stat.minecraft.time_since_death": "Tið ses an duog sienest", "stat.minecraft.time_since_rest": "Tið ses sienest återstellningg", "stat.minecraft.total_world_time": "Tið dar wärdn ir yppin", "stat.minecraft.traded_with_villager": "<PERSON><PERSON><PERSON> min byskallum", "stat.minecraft.trigger_trapped_chest": "Aktivirað tjistfellur", "stat.minecraft.tune_noteblock": "Nuotblokk so iro stemder", "stat.minecraft.use_cauldron": "Watt<PERSON>ð frǫ ienum kessle", "stat.minecraft.walk_on_water_one_cm": "Ur langgt du ar gaið ą̊ wattnę", "stat.minecraft.walk_one_cm": "So langgt ar ðu gaið", "stat.minecraft.walk_under_water_one_cm": "So langgt ar ðu gaið niði wattnę", "stat.mobsButton": "Fugatstattjin", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON> so mikkel gangger", "stat_type.minecraft.crafted": "Tilwerkað so mikkel gannger", "stat_type.minecraft.dropped": "Tappað", "stat_type.minecraft.killed": "Du eleð %s %s", "stat_type.minecraft.killed.none": "Du ar older elað %s", "stat_type.minecraft.killed_by": "%s eleð ðig %s gangg(-er)", "stat_type.minecraft.killed_by.none": "Du ar older wurtið elað ov %s", "stat_type.minecraft.mined": "B<PERSON><PERSON>ð so mikkel gannger", "stat_type.minecraft.picked_up": "Uppiemtað", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON> so mikkel gagger", "stats.none": "–", "structure_block.button.detect_size": "ITTA", "structure_block.button.load": "LEÐA", "structure_block.button.save": "SPÅRÅ", "structure_block.custom_data": "Nammen ą̊ bestelld datanammen", "structure_block.detect_size": "Identifiera storlek och position:", "structure_block.hover.corner": "Örn: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Leða: '%s'", "structure_block.hover.save": "Spårå: %s", "structure_block.include_entities": "Inkludera entiteter:", "structure_block.integrity": "Mömsterintegritiet og frie", "structure_block.integrity.integrity": "Mömsterintegritiet", "structure_block.integrity.seed": "Mömsterfrie", "structure_block.invalid_structure_name": "Odjiltut strukturnamen '%s'", "structure_block.load_not_found": "Struktur '%s' går it få i", "structure_block.load_prepare": "Struktur '%s' position förberiedd", "structure_block.load_success": "Struktur leðað frą̊ '%s'", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "Data", "structure_block.mode.load": "<PERSON><PERSON><PERSON>", "structure_block.mode.save": "Spårå", "structure_block.mode_info.corner": "Örnmetuoð - plasiringg og sturlieksmarkiringg", "structure_block.mode_info.data": "Datametuoð - spillogikmarkiringg", "structure_block.mode_info.load": "Leðninggsmetuoð - leð frą̊ fil", "structure_block.mode_info.save": "Spårå-läge - Skriev að fil", "structure_block.position": "Relativ position", "structure_block.position.x": "relativ position x", "structure_block.position.y": "relativ position y", "structure_block.position.z": "relativ position z", "structure_block.save_failure": "Dugd it spårå struktur '%s'", "structure_block.save_success": "Struktur späreð sos '%s'", "structure_block.show_air": "<PERSON><PERSON> blokk an dug it sjå:", "structure_block.show_boundingbox": "W<PERSON> uppend låða:", "structure_block.size": "Mömstersturliek", "structure_block.size.x": "mömstersturliek x", "structure_block.size.y": "mömstersturliek y", "structure_block.size.z": "mömstersturliek z", "structure_block.size_failure": "E<PERSON> djikk it werd i wari struktursturliek. Legg til örne min strukturnamen so passer", "structure_block.size_success": "Eð djikk bra werd i wari sturliek fer '%s'", "structure_block.strict": "Strikt placering:", "structure_block.structure_name": "Strukturnammen", "subtitles.ambient.cave": "Iemskt ljuoð", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON><PERSON> ljud", "subtitles.block.amethyst_block.chime": "Ametist klingger", "subtitles.block.amethyst_block.resonate": "Ametist gen<PERSON><PERSON><PERSON>", "subtitles.block.anvil.destroy": "Smiðsteð so ir sunt", "subtitles.block.anvil.land": "<PERSON>e<PERSON> so landeð", "subtitles.block.anvil.use": "Steð so brukes", "subtitles.block.barrel.close": "Tunna so werd stainggd", "subtitles.block.barrel.open": "Tunna so yppnes", "subtitles.block.beacon.activate": "Fyr werd aktivirað", "subtitles.block.beacon.ambient": "F<PERSON> frunder", "subtitles.block.beacon.deactivate": "Fyr werd ovstainggd", "subtitles.block.beacon.power_select": "Kraft að fyrn węles", "subtitles.block.beehive.drip": "<PERSON><PERSON>g drupper", "subtitles.block.beehive.enter": "Bia far ini kupu", "subtitles.block.beehive.exit": "Bia far frǫ kupun", "subtitles.block.beehive.shear": "Saks skräper", "subtitles.block.beehive.work": "<PERSON><PERSON> jobber", "subtitles.block.bell.resonate": "Klukk so lämär", "subtitles.block.bell.use": "Klukk so bamber", "subtitles.block.big_dripleaf.tilt_down": "Drup<PERSON> so lauter nið", "subtitles.block.big_dripleaf.tilt_up": "Druplover so lauter upp", "subtitles.block.blastfurnace.fire_crackle": "Smeltungen spräker", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON>ingg so bubbler", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON><PERSON><PERSON> brussner", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> lopa", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> fuså", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "subtitles.block.button.click": "<PERSON><PERSON><PERSON> klikker", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON> twes<PERSON>", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON><PERSON> spräker", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rimner", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "Tjist so werd atteppt", "subtitles.block.chest.locked": "Tjist so werd attlęst", "subtitles.block.chest.open": "Tjist so yppnes", "subtitles.block.chorus_flower.death": "Körbljuomm wissner", "subtitles.block.chorus_flower.grow": "Körbljuomm wekser", "subtitles.block.comparator.click": "Iemfyörer klikker", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON> työ<PERSON>", "subtitles.block.composter.fill": "Kompost feskes ǫ", "subtitles.block.composter.ready": "Ko<PERSON>st kompostirer", "subtitles.block.conduit.activate": "Flöðer werd aktivirad", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON><PERSON> werd åv<PERSON>ingd", "subtitles.block.copper_bulb.turn_off": "Kuppärlampel slökkär av", "subtitles.block.copper_bulb.turn_on": "Kuppärlampel glinnär på", "subtitles.block.copper_trapdoor.close": "Follukku teppes att", "subtitles.block.copper_trapdoor.open": "Follukku yppnes", "subtitles.block.crafter.craft": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.hurt": "Knarrhj<PERSON><PERSON> knorrar", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON> ljud", "subtitles.block.creaking_heart.spawn": "Knarrhjärta vaknar", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON> buske knastrar", "subtitles.block.decorated_pot.insert": "Dekorativ kruka fylls", "subtitles.block.decorated_pot.insert_fail": "Dekorativ kruka gungar", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.dispenser.dispense": "Autdielað föremål", "subtitles.block.dispenser.fail": "Automat funggirer it", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Någonting susar", "subtitles.block.dried_ghast.ambient_water": "Uttorkad ghast återfuktas", "subtitles.block.dried_ghast.place_in_water": "Uttorkad ghast läggs i blöt", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON><PERSON> ghast mår b<PERSON>", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON> susar", "subtitles.block.enchantment_table.use": "Fertryllningsbuord brukes", "subtitles.block.end_portal.spawn": "End portal yppnes", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON> werd <PERSON>", "subtitles.block.eyeblossom.close": "Ögonblomma stängs", "subtitles.block.eyeblossom.idle": "Ögonblomma viskar", "subtitles.block.eyeblossom.open": "Ögonblomma öppnas", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON>d gniler", "subtitles.block.fire.ambient": "J<PERSON>lden spräker", "subtitles.block.fire.extinguish": "<PERSON><PERSON><PERSON><PERSON> ir slekkt", "subtitles.block.firefly_bush.idle": "Eldflug<PERSON> surrar", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.furnace.fire_crackle": "Smeltungen spräker", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON><PERSON> so ir sund", "subtitles.block.generic.fall": "Någonting landar på ett block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Blokk so brussner", "subtitles.block.generic.place": "Blokk so werd plasirað", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "P<PERSON>t so werd beskurin", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON>lt gungar", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON> nið<PERSON><PERSON>r upǫ iett onung<PERSON>blo<PERSON>k", "subtitles.block.iron_trapdoor.close": "Follukku teppes att", "subtitles.block.iron_trapdoor.open": "Follukku yppnes", "subtitles.block.lava.ambient": "Lava popper", "subtitles.block.lava.extinguish": "<PERSON><PERSON> f<PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.note_block.note": "Antekkninggsblokk spiles", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON><PERSON> ljud", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON>va druper niði ien ketil", "subtitles.block.pointed_dripstone.drip_water": "Watudrupir", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON>ð drupper niði ien ketil", "subtitles.block.pointed_dripstone.land": "Stalaktit bräker nið", "subtitles.block.portal.ambient": "Portal twäser", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON><PERSON> frǫ portlem werd swagera", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON><PERSON><PERSON> oker", "subtitles.block.pressure_plate.click": "Trykkplatt klikker", "subtitles.block.pumpkin.carve": "Saks karver", "subtitles.block.redstone_torch.burnout": "Stik<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.respawn_anchor.ambient": "Portal susar", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> leðes", "subtitles.block.respawn_anchor.deplete": "Återuppstondelsanker työmes", "subtitles.block.respawn_anchor.set_spawn": "Återuppstondelsanker bistemmer ny punkt", "subtitles.block.sand.idle": "Vind virvlar upp sand", "subtitles.block.sand.wind": "<PERSON><PERSON> susar", "subtitles.block.sculk.charge": "<PERSON>en sculk puoller", "subtitles.block.sculk.spread": "<PERSON>en sculk sprieðer sig", "subtitles.block.sculk_catalyst.bloom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.block.sculk_sensor.clicking": "Sculkupptekker far pjätå", "subtitles.block.sculk_sensor.clicking_stop": "Sculkupptekker far slut pjätå", "subtitles.block.sculk_shrieker.shriek": "Sculkkulerer kuler", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> werd atteppt", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> yppnes", "subtitles.block.sign.waxed_interact_fail": "<PERSON>lt gungar", "subtitles.block.smithing_table.use": "Smiðbuord brukes", "subtitles.block.smoker.smoke": "Ryökungen ryötjer", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON>ff<PERSON><PERSON><PERSON> spricker", "subtitles.block.sniffer_egg.hatch": "Sniffarägg kläcks", "subtitles.block.sniffer_egg.plop": "Sniffare lägger ägg", "subtitles.block.sponge.absorb": "Svamp suger upp", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.close": "Fallucka stängs", "subtitles.block.trapdoor.open": "Fallucka <PERSON>", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>er", "subtitles.block.trial_spawner.about_to_spawn_item": "Oroväckande föremål förbereds", "subtitles.block.trial_spawner.ambient": "Utmaningsskapare knastrar", "subtitles.block.trial_spawner.ambient_charged": "Orov<PERSON><PERSON><PERSON><PERSON> knast<PERSON>e", "subtitles.block.trial_spawner.ambient_ominous": "Orov<PERSON><PERSON><PERSON><PERSON> knast<PERSON>e", "subtitles.block.trial_spawner.charge_activate": "Föraning lägger sig över utmaningsskapare", "subtitles.block.trial_spawner.close_shutter": "Utmaningsskapare stängs", "subtitles.block.trial_spawner.detect_player": "Utmaningsskapare laddar upp", "subtitles.block.trial_spawner.eject_item": "Utmaningsskapare skjuter ut föremål", "subtitles.block.trial_spawner.ominous_activate": "Föraning lägger sig över utmaningsskapare", "subtitles.block.trial_spawner.open_shutter": "Utmaningsskapare öppnas", "subtitles.block.trial_spawner.spawn_item": "Oroväckande föremål släpps", "subtitles.block.trial_spawner.spawn_item_begin": "Oroväckande föremål dyker upp", "subtitles.block.trial_spawner.spawn_mob": "Utmaningsskapare skapar varelse", "subtitles.block.tripwire.attach": "St<PERSON>ppeltr<PERSON>ð kopples in", "subtitles.block.tripwire.click": "Styppeltråð klikker", "subtitles.block.tripwire.detach": "Styppeltråð lossner", "subtitles.block.vault.activate": "Kassaskåp tänds", "subtitles.block.vault.ambient": "Kassaskåp knastrar", "subtitles.block.vault.close_shutter": "Kassaskåp stängs", "subtitles.block.vault.deactivate": "Kassaskåp slocknar", "subtitles.block.vault.eject_item": "Kassaskåp skjuter ut föremål", "subtitles.block.vault.insert_item": "Kassaskåp låses upp", "subtitles.block.vault.insert_item_fail": "Kassaskåp nekar föremål", "subtitles.block.vault.open_shutter": "Kassaskåp öppnas", "subtitles.block.vault.reject_rewarded_player": "Ka<PERSON>sk<PERSON><PERSON> ne<PERSON> spelare", "subtitles.block.water.ambient": "Wattneð loop", "subtitles.block.wet_sponge.dries": "Svamp torkar", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON> läggs in", "subtitles.chiseled_bookshelf.insert_enchanted": "Förtrollad bok läggs in", "subtitles.chiseled_bookshelf.take": "Bok plockas ut", "subtitles.chiseled_bookshelf.take_enchanted": "Förtrollad bok plockas ut", "subtitles.enchant.thorns.hit": "<PERSON>ger pikkas", "subtitles.entity.allay.ambient_with_item": "Jåpsamböbökall lieter", "subtitles.entity.allay.ambient_without_item": "Jåpsamböbö<PERSON><PERSON>", "subtitles.entity.allay.death": "Jåpsamböbökall där", "subtitles.entity.allay.hurt": "Jåpsamböbökall werd skåðåð", "subtitles.entity.allay.item_given": "Jåpsamböbökall tiser", "subtitles.entity.allay.item_taken": "Jåpsamböbökall werd faingen", "subtitles.entity.allay.item_thrown": "Jåpsamböbökal<PERSON> winder", "subtitles.entity.armadillo.ambient": "Bältdjur grymtar", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bort", "subtitles.entity.armadillo.death": "Bältdjur dör", "subtitles.entity.armadillo.eat": "Bältdjur äter", "subtitles.entity.armadillo.hurt": "Bältdjur skadas", "subtitles.entity.armadillo.hurt_reduced": "Bältdjur skyddar sig", "subtitles.entity.armadillo.land": "Bältdjur landar", "subtitles.entity.armadillo.peek": "Bältdjur tittar fram", "subtitles.entity.armadillo.roll": "Bältdjur rullar ihop sig", "subtitles.entity.armadillo.scute_drop": "Bältdjur sl<PERSON><PERSON>", "subtitles.entity.armadillo.unroll_finish": "Bältdjur vecklar ut sig", "subtitles.entity.armadillo.unroll_start": "Bältdjur tittar fram", "subtitles.entity.armor_stand.fall": "Eð war noð so räseð", "subtitles.entity.arrow.hit": "<PERSON><PERSON> itter ǫ", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.arrow.shoot": "<PERSON><PERSON> wart skuotin", "subtitles.entity.axolotl.attack": "Axolotl attakirer", "subtitles.entity.axolotl.death": "Axolotl där", "subtitles.entity.axolotl.hurt": "Axolotl werd skåðåð", "subtitles.entity.axolotl.idle_air": "Axolotl spiler", "subtitles.entity.axolotl.idle_water": "Axolotl spiler", "subtitles.entity.axolotl.splash": "Axolotl skuoler", "subtitles.entity.axolotl.swim": "Axolotl simmer", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON> pįstrer", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON> fliuog iweg", "subtitles.entity.bee.ambient": "<PERSON> frunder", "subtitles.entity.bee.death": "<PERSON><PERSON> d<PERSON>", "subtitles.entity.bee.hurt": "Bia werd skåðåð", "subtitles.entity.bee.loop": "<PERSON><PERSON> frunder", "subtitles.entity.bee.loop_aggressive": "Bia frunder jälokt", "subtitles.entity.bee.pollinate": "Bia frunder lykklit", "subtitles.entity.bee.sting": "Bia pikkes", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON> weser", "subtitles.entity.blaze.burn": "<PERSON><PERSON>ner spräker", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Sumpigt skelett skallrar", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON> skelett dör", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON><PERSON> skelett skadas", "subtitles.entity.breeze.charge": "Vindväsen laddar upp", "subtitles.entity.breeze.death": "Vind<PERSON><PERSON><PERSON> dör", "subtitles.entity.breeze.deflect": "Vindväsen blockerar", "subtitles.entity.breeze.hurt": "Vind<PERSON><PERSON><PERSON> skadas", "subtitles.entity.breeze.idle_air": "Vindväsen svävar", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON><PERSON><PERSON> susar", "subtitles.entity.breeze.inhale": "Vindväsen samlar luft", "subtitles.entity.breeze.jump": "Vindväsen hoppar", "subtitles.entity.breeze.land": "Vindv<PERSON><PERSON> landar", "subtitles.entity.breeze.shoot": "Vindväsen skjuter", "subtitles.entity.breeze.slide": "Vindväsen glider", "subtitles.entity.breeze.whirl": "Vindväsen virvlar", "subtitles.entity.breeze.wind_burst": "Vin<PERSON>tta<PERSON> exploderar", "subtitles.entity.camel.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON> hoppar", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON>mtar sig", "subtitles.entity.camel.death": "<PERSON><PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON> sätts på", "subtitles.entity.camel.sit": "<PERSON><PERSON> lägger sig ner", "subtitles.entity.camel.stand": "<PERSON><PERSON> reser sig upp", "subtitles.entity.camel.step": "<PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON> går i sand", "subtitles.entity.cat.ambient": "<PERSON>e jamer", "subtitles.entity.cat.beg_for_food": "Masse wines", "subtitles.entity.cat.death": "<PERSON><PERSON> där", "subtitles.entity.cat.eat": "Masse jät", "subtitles.entity.cat.hiss": "Masse fręser", "subtitles.entity.cat.hurt": "<PERSON>e werd skåðåð", "subtitles.entity.cat.purr": "<PERSON><PERSON> kurrer", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> legg edję", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON> sprattler", "subtitles.entity.cod.hurt": "Tuosk werd skåðåð", "subtitles.entity.cow.ambient": "<PERSON><PERSON><PERSON> remer", "subtitles.entity.cow.death": "<PERSON><PERSON><PERSON> d<PERSON>r", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.cow.milk": "<PERSON><PERSON><PERSON> werd mjok<PERSON>ð", "subtitles.entity.creaking.activate": "K<PERSON><PERSON>r<PERSON><PERSON> ser spelare", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lugnar ned sig", "subtitles.entity.creaking.death": "Knarrtr<PERSON><PERSON>", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stannar upp", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.sway": "K<PERSON><PERSON><PERSON><PERSON><PERSON> trä<PERSON>", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> till", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rör sig", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.creeper.primed": "Creeper fręser", "subtitles.entity.dolphin.ambient": "Delfin klikker", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON> wissler", "subtitles.entity.dolphin.attack": "<PERSON><PERSON> atta<PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON> d<PERSON>", "subtitles.entity.dolphin.eat": "Delfin jät", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON> werd skåðåð", "subtitles.entity.dolphin.jump": "Delfin upper", "subtitles.entity.dolphin.play": "<PERSON><PERSON>", "subtitles.entity.dolphin.splash": "Delfin twesker", "subtitles.entity.dolphin.swim": "Delfin simmer", "subtitles.entity.donkey.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON> fǫr t<PERSON>", "subtitles.entity.donkey.death": "Åsn där", "subtitles.entity.donkey.eat": "Åsna jät", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.donkey.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> gurgler", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON>k<PERSON><PERSON> gurglar", "subtitles.entity.drowned.death": "Drukk<PERSON><PERSON> där", "subtitles.entity.drowned.hurt": "Dr<PERSON><PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> winder lju<PERSON>er", "subtitles.entity.drowned.step": "Drukknað går", "subtitles.entity.drowned.swim": "Drukknað simmer", "subtitles.entity.egg.throw": "Egg fliuog", "subtitles.entity.elder_guardian.ambient": "Gambelwakt gnyy̨öksler", "subtitles.entity.elder_guardian.ambient_land": "Gambelwakt flakser", "subtitles.entity.elder_guardian.curse": "Gambelwakt ugser", "subtitles.entity.elder_guardian.death": "Gambelwakt där", "subtitles.entity.elder_guardian.flop": "Gambelwakt sprattler", "subtitles.entity.elder_guardian.hurt": "Gambelwakt werd skåðåð", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON> kuler", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON> flakser", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON>ur<PERSON>", "subtitles.entity.ender_dragon.hurt": "Dratji werd skåðåð", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_eye.death": "Enderog foll", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.ambient": "Enderkall vwoper", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.enderman.scream": "<PERSON><PERSON> skriker", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON> remer", "subtitles.entity.enderman.teleport": "Enderkall teleportirer", "subtitles.entity.endermite.ambient": "Ender<PERSON> druser", "subtitles.entity.endermite.death": "Ender<PERSON> där", "subtitles.entity.endermite.hurt": "Endermite werd skåðåð", "subtitles.entity.evoker.ambient": "Evoker mumbler", "subtitles.entity.evoker.cast_spell": "Evoker ugser", "subtitles.entity.evoker.celebrate": "Evoker urrer", "subtitles.entity.evoker.death": "<PERSON>voke<PERSON> där", "subtitles.entity.evoker.hurt": "Evoker werd skå<PERSON>ð", "subtitles.entity.evoker.prepare_attack": "Evoker f<PERSON><PERSON><PERSON><PERSON><PERSON> anfall", "subtitles.entity.evoker.prepare_summon": "Evoker f<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_wololo": "Evoker förbirieð stjarmningg", "subtitles.entity.evoker_fangs.attack": "Oggtenner slår att", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON><PERSON> muottaiðn", "subtitles.entity.firework_rocket.blast": "Fyrwerkeri smeller", "subtitles.entity.firework_rocket.launch": "Fyrwerkeri stjikkes iweg", "subtitles.entity.firework_rocket.twinkle": "Fyrwerkeri blakker", "subtitles.entity.fish.swim": "Någonting plaskar", "subtitles.entity.fishing_bobber.retrieve": "Flota far upyr", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON><PERSON> windes aut", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> werd jälok", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON> skeller", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> baiter", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> remer", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> sputter", "subtitles.entity.fox.teleport": "Röv teleportirer", "subtitles.entity.frog.ambient": "Tuosk kwätjer", "subtitles.entity.frog.death": "Tuosk däär", "subtitles.entity.frog.eat": "Tuosk jät", "subtitles.entity.frog.hurt": "Tuosk werd skåðåð", "subtitles.entity.frog.lay_spawn": "Tuosk legg runneð", "subtitles.entity.frog.long_jump": "Tuosk upper", "subtitles.entity.generic.big_fall": "Ed war noð so räseð", "subtitles.entity.generic.burn": "Noð so brið", "subtitles.entity.generic.death": "Noð so där", "subtitles.entity.generic.drink": "Noð so drikk", "subtitles.entity.generic.eat": "Noð so jät", "subtitles.entity.generic.explode": "<PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.hurt": "Noð so djär sårt", "subtitles.entity.generic.small_fall": "Noð so stypples", "subtitles.entity.generic.splash": "Noð so twesk", "subtitles.entity.generic.swim": "Noð so simmer", "subtitles.entity.generic.wind_burst": "Vin<PERSON>tta<PERSON> exploderar", "subtitles.entity.ghast.ambient": "Ghast grainer", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> stiuot", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> kuttrar", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dör", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON> skadas", "subtitles.entity.ghastling.spawn": "Ghastling dyker upp", "subtitles.entity.glow_item_frame.add_item": "Glyöðend föremǫlsram werd fylld", "subtitles.entity.glow_item_frame.break": "Glyöðend föremǫ<PERSON><PERSON> brustner", "subtitles.entity.glow_item_frame.place": "Glyöðend föremǫlsram plasirað", "subtitles.entity.glow_item_frame.remove_item": "Glyöðend föremǫlsram werd työmd", "subtitles.entity.glow_item_frame.rotate_item": "Glyöðend föremǫlsram klikker", "subtitles.entity.glow_squid.ambient": "Glyöðend blikfisk simmer", "subtitles.entity.glow_squid.death": "Glyöðend blikfisk där", "subtitles.entity.glow_squid.hurt": "Glyöðend blikfisk werd skåðåð", "subtitles.entity.glow_squid.squirt": "Glyöðend blikfisk stiuot iweg blitjeð", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> so <PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> so där", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> so jät", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON> bruss<PERSON>", "subtitles.entity.goat.hurt": "Djiet werd skåðåð", "subtitles.entity.goat.long_jump": "Dji<PERSON> so upper", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> so mj<PERSON><PERSON>", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON> so tramper", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON> so negges", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON> so wäler", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> so går", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> gny̨öksler", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON> flakser", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> sprattler", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.happy_ghast.ambient": "Glad ghast gnyr glatt", "subtitles.entity.happy_ghast.death": "<PERSON> ghast dör", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON> s<PERSON> på", "subtitles.entity.happy_ghast.harness_goggles_down": "Glad ghast är redo", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> ghast stannar", "subtitles.entity.happy_ghast.hurt": "Glad ghast skadas", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON> tas av", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> gry<PERSON>ter", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> g<PERSON> j<PERSON>", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "Hoglin konvertirer að <PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> djäv upp", "subtitles.entity.hoglin.step": "<PERSON><PERSON>n gor", "subtitles.entity.horse.ambient": "<PERSON>st gnegger", "subtitles.entity.horse.angry": "<PERSON>st gnegger", "subtitles.entity.horse.armor": "<PERSON><PERSON>n får rust<PERSON>", "subtitles.entity.horse.breathe": "Est weser", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.horse.eat": "Estn jät", "subtitles.entity.horse.gallop": "Estn kåyter", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.horse.jump": "Estn upper", "subtitles.entity.horse.saddle": "<PERSON><PERSON>n får <PERSON>'lln", "subtitles.entity.husk.ambient": "Torkaðsombi gny̨öksler", "subtitles.entity.husk.converted_to_zombie": "Auttorkað<PERSON><PERSON>i werd ien sombi", "subtitles.entity.husk.death": "Tor<PERSON>ð<PERSON><PERSON><PERSON> där", "subtitles.entity.husk.hurt": "Torkaðsomb<PERSON> werd skåðåð", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> mumbler", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON><PERSON><PERSON> ugser", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> där", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.illusioner.mirror_move": "Trull<PERSON><PERSON> autbytt", "subtitles.entity.illusioner.prepare_blindness": "Trullkall förbirieð blindiet", "subtitles.entity.illusioner.prepare_mirror": "Trullkall förbirieð spailbild", "subtitles.entity.iron_golem.attack": "Ienngolem attakirer", "subtitles.entity.iron_golem.damage": "Ienngolem far sund", "subtitles.entity.iron_golem.death": "Ienngolem där", "subtitles.entity.iron_golem.hurt": "Ienngolem werd skåðåd", "subtitles.entity.iron_golem.repair": "Ienngolem werd lågåð", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> far sund", "subtitles.entity.item.pickup": "Föremą̊l plopper", "subtitles.entity.item_frame.add_item": "Föremålsram autfylld", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON> far sund", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ð", "subtitles.entity.item_frame.remove_item": "Föremǫlsram werd työmd", "subtitles.entity.item_frame.rotate_item": "Föremålsram klikker", "subtitles.entity.leash_knot.break": "Repknop lossas", "subtitles.entity.leash_knot.place": "Repknop knyts", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON> ą̊", "subtitles.entity.lightning_bolt.thunder": "Åsk<PERSON> duner", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "Lama skw<PERSON> jälo<PERSON>", "subtitles.entity.llama.chest": "<PERSON> får ien tjistu", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON>", "subtitles.entity.llama.hurt": "<PERSON> werd skå<PERSON>ð", "subtitles.entity.llama.spit": "Lama sputter", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON> werd <PERSON>", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>r", "subtitles.entity.magma_cube.hurt": "Magma<PERSON>b werd skåðåð", "subtitles.entity.magma_cube.squish": "Magmakub kwåstrer", "subtitles.entity.minecart.inside": "Gruvvagn skramlar", "subtitles.entity.minecart.inside_underwater": "Gruvvagn skramlar under vatten", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruller", "subtitles.entity.mooshroom.convert": "Mooshroom fewandles", "subtitles.entity.mooshroom.eat": "Mooshroom jät", "subtitles.entity.mooshroom.milk": "Mooshroom mjokes", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom mjokes illwarlit", "subtitles.entity.mule.ambient": "<PERSON><PERSON> gnegger", "subtitles.entity.mule.angry": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON> får ien tjist", "subtitles.entity.mule.death": "<PERSON><PERSON> d<PERSON>r", "subtitles.entity.mule.eat": "<PERSON><PERSON>", "subtitles.entity.mule.hurt": "<PERSON>l werd skå<PERSON>ð", "subtitles.entity.mule.jump": "<PERSON><PERSON> hoppar", "subtitles.entity.painting.break": "Tavel far sund", "subtitles.entity.painting.place": "Tavel werd uppainggd", "subtitles.entity.panda.aggressive_ambient": "Panda rymsk sig", "subtitles.entity.panda.ambient": "Panda kaiper", "subtitles.entity.panda.bite": "Panda baiter", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON>", "subtitles.entity.panda.death": "<PERSON><PERSON> där", "subtitles.entity.panda.eat": "Panda jät", "subtitles.entity.panda.hurt": "Panda werd skåðåð", "subtitles.entity.panda.pre_sneeze": "Pandasnaut <PERSON>", "subtitles.entity.panda.sneeze": "Panda niųos", "subtitles.entity.panda.step": "Panda spasirer", "subtitles.entity.panda.worried_ambient": "Panda gn<PERSON>ler", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON><PERSON><PERSON> swiver", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd skå<PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.bogged": "Papegoja skallrar", "subtitles.entity.parrot.imitate.breeze": "Papegoja susar", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON><PERSON> knarrar", "subtitles.entity.parrot.imitate.creeper": "Papig<PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON> gurgler", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON>g<PERSON><PERSON> gny̨öksler", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON><PERSON><PERSON> mumbler", "subtitles.entity.parrot.imitate.ghast": "Papig<PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON>g<PERSON><PERSON> gny̨öksler", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON><PERSON><PERSON> murrer", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON>g<PERSON><PERSON> gny̨öksler", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON><PERSON><PERSON> mumbler", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON><PERSON><PERSON> smatter", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.piglin": "Papigåj fnåiser", "subtitles.entity.parrot.imitate.piglin_brute": "Papegoja fnyser", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON><PERSON><PERSON> mumbler", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "Papig<PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON><PERSON><PERSON> smatter", "subtitles.entity.parrot.imitate.spider": "Papig<PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd ferturvað", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON><PERSON><PERSON> muser", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd j<PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON><PERSON><PERSON> murrer", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON><PERSON><PERSON> brummer", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON><PERSON><PERSON> brummer", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON><PERSON><PERSON> baiter", "subtitles.entity.phantom.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.phantom.flap": "<PERSON><PERSON><PERSON><PERSON><PERSON> flakser", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.phantom.swoop": "<PERSON><PERSON><PERSON><PERSON><PERSON> swiper", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>", "subtitles.entity.pig.saddle": "Sa'll autrustes", "subtitles.entity.piglin.admiring_item": "<PERSON>lin biundrer föremǫl", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> grymter", "subtitles.entity.piglin.angry": "<PERSON><PERSON> gry<PERSON> j<PERSON>", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> firer", "subtitles.entity.piglin.converted_to_zombified": "<PERSON>lin konvertirer að sombifierað <PERSON>lin", "subtitles.entity.piglin.death": "<PERSON><PERSON> d<PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> werd skåðåð", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> grymter an<PERSON>", "subtitles.entity.piglin.retreat": "<PERSON>lin djäv upp", "subtitles.entity.piglin.step": "<PERSON><PERSON> gor", "subtitles.entity.piglin_brute.ambient": "Piglinfugat fnåiser", "subtitles.entity.piglin_brute.angry": "Piglinfugat fnåis fault", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglinfugat werd sombifiirað piglin", "subtitles.entity.piglin_brute.death": "Piglinfugat där", "subtitles.entity.piglin_brute.hurt": "Piglinfugat werd skåðåð", "subtitles.entity.piglin_brute.step": "Piglinfugat gor", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON> mumbler", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.player.attack.crit": "Kritisk attakk", "subtitles.entity.player.attack.knockback": "Rek ǫ-attakk", "subtitles.entity.player.attack.strong": "Stark attakk", "subtitles.entity.player.attack.sweep": "Swipend attakk", "subtitles.entity.player.attack.weak": "Swag attakk", "subtitles.entity.player.burp": "<PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.freeze_hurt": "Spiler friuos", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.player.hurt_drown": "Spiler d<PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> brið", "subtitles.entity.player.levelup": "S<PERSON><PERSON><PERSON>r far upi nivå", "subtitles.entity.player.teleport": "Spelare teleporterar", "subtitles.entity.polar_bear.ambient": "Aisbyönn brummer", "subtitles.entity.polar_bear.ambient_baby": "Aisbyönn gnǫler", "subtitles.entity.polar_bear.death": "Aisbyönn där", "subtitles.entity.polar_bear.hurt": "Aisbyönn werd skåðåð", "subtitles.entity.polar_bear.warning": "Aisbyönn riuoter", "subtitles.entity.potion.splash": "<PERSON><PERSON> knäser sund", "subtitles.entity.potion.throw": "<PERSON><PERSON> windes", "subtitles.entity.puffer_fish.blow_out": "Uppustaðfistjin krymper", "subtitles.entity.puffer_fish.blow_up": "Uppustaðfistjin puster upp sig", "subtitles.entity.puffer_fish.death": "Uppustaðfistjin där", "subtitles.entity.puffer_fish.flop": "Uppus<PERSON>ð<PERSON><PERSON><PERSON> sprattler", "subtitles.entity.puffer_fish.hurt": "Uppustaðfistjin werd skåðåð", "subtitles.entity.puffer_fish.sting": "Uppustaðfi<PERSON><PERSON> pikker", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> werd skå<PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON> upper", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON> gry<PERSON>", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON> baiter", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.ravager.step": "Fest<PERSON>rer går", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON> werd sjokkað", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON> sprattler", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> soð br<PERSON>", "subtitles.entity.sheep.death": "<PERSON>en soð där", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> soð werd skåðåð", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> tepes att", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> yppnes", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> stiuot", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleportirer", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON> kaul eksploðirer", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON> kaul far sund", "subtitles.entity.silverfish.ambient": "Silverfisk fręser", "subtitles.entity.silverfish.death": "Silverfisk där", "subtitles.entity.silverfish.hurt": "Silverfisk werd skåðåð", "subtitles.entity.skeleton.ambient": "Bįeraunggel skoldrer", "subtitles.entity.skeleton.converted_to_stray": "Bieraunggel djär um sig að ienum luffer", "subtitles.entity.skeleton.death": "Bįeraunggel där", "subtitles.entity.skeleton.hurt": "Bįeraunggel werd skåðåð", "subtitles.entity.skeleton.shoot": "Bįeraunggel sjtuot", "subtitles.entity.skeleton_horse.ambient": "Bįeraunggelest grainer", "subtitles.entity.skeleton_horse.death": "Bįeraunggelest där", "subtitles.entity.skeleton_horse.hurt": "Bįeraunggelest werd skåðåð", "subtitles.entity.skeleton_horse.jump_water": "Skeletthäst hoppar", "subtitles.entity.skeleton_horse.swim": "Bįeraunggelest simmer", "subtitles.entity.slime.attack": "K<PERSON><PERSON>ą attakirer", "subtitles.entity.slime.death": "Kliemą där", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.slime.squish": "Kliemą kwåstrer", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.sniffer.digging": "Sniffare gräver", "subtitles.entity.sniffer.digging_stop": "Sniffare reser sig upp", "subtitles.entity.sniffer.drop_seed": "Sniffare hittar frö", "subtitles.entity.sniffer.eat": "<PERSON><PERSON>ff<PERSON> ä<PERSON>", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON>ff<PERSON><PERSON><PERSON> spricker", "subtitles.entity.sniffer.egg_hatch": "Sniffarägg kläcks", "subtitles.entity.sniffer.happy": "Sniffare glädjer sig", "subtitles.entity.sniffer.hurt": "Sniffare skåðåð", "subtitles.entity.sniffer.idle": "Sniffare grymtar", "subtitles.entity.sniffer.scenting": "<PERSON>ni<PERSON><PERSON> sniffar", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.sniffer.sniffing": "Sniffare vädrar", "subtitles.entity.sniffer.step": "Sniffare går", "subtitles.entity.snow_golem.death": "Sniųogolem där", "subtitles.entity.snow_golem.hurt": "Sniųogolem werd skåðåð", "subtitles.entity.snowball.throw": "Sniųboll fliuog", "subtitles.entity.spider.ambient": "Dyörg f<PERSON>", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON><PERSON> werd skåð<PERSON>ð", "subtitles.entity.squid.ambient": "Blikfisk simmer", "subtitles.entity.squid.death": "Blikfisk där", "subtitles.entity.squid.hurt": "Blikfisk werd skåðåð", "subtitles.entity.squid.squirt": "Bikfik stiuot i weg blitjeð", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON> skoldrer", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON> tral<PERSON>", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "<PERSON><PERSON>r djäv upp", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.tadpole.flop": "Pringgel sprettas", "subtitles.entity.tadpole.grow_up": "Grodyngel växer upp", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.tnt.primed": "Dynamit f<PERSON>", "subtitles.entity.tropical_fish.death": "Tropisk fisk där", "subtitles.entity.tropical_fish.flop": "Tropisk fisk sprattler", "subtitles.entity.tropical_fish.hurt": "Tropisk fisk werd skåðåð", "subtitles.entity.turtle.ambient_land": "Stjölkluoss gnǫler", "subtitles.entity.turtle.death": "Stjölkluoss där", "subtitles.entity.turtle.death_baby": "Stjöldkluossungg där", "subtitles.entity.turtle.egg_break": "Stjölkluoss<PERSON><PERSON><PERSON> får<PERSON> sund", "subtitles.entity.turtle.egg_crack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rimner", "subtitles.entity.turtle.egg_hatch": "Stjölkluoss ligg ǫ eggum", "subtitles.entity.turtle.hurt": "Stjölkluoss werd skåðåð", "subtitles.entity.turtle.hurt_baby": "Stjölkluossungg werd skåðåð", "subtitles.entity.turtle.lay_egg": "Stjölkluoss legg edję", "subtitles.entity.turtle.shamble": "Stjöldkluoss gryvler", "subtitles.entity.turtle.shamble_baby": "Stjöldkluossungg gryvler", "subtitles.entity.turtle.swim": "Stjölldkluoss simmer", "subtitles.entity.vex.ambient": "Biswerskupp irritirer", "subtitles.entity.vex.charge": "Biswerskupp skrier", "subtitles.entity.vex.death": "Biswerskupp där", "subtitles.entity.vex.hurt": "Biswerskupp werd skåðåð", "subtitles.entity.villager.ambient": "By<PERSON>ll mumbler", "subtitles.entity.villager.celebrate": "By<PERSON>ll urrer", "subtitles.entity.villager.death": "Byskall där", "subtitles.entity.villager.hurt": "By<PERSON>ll werd skåðåð", "subtitles.entity.villager.no": "By<PERSON>ll olld it minn", "subtitles.entity.villager.trade": "<PERSON><PERSON><PERSON> and<PERSON>", "subtitles.entity.villager.work_armorer": "Rustninggssmið jobber", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_cleric": "Prest jobber", "subtitles.entity.villager.work_farmer": "Buond jobber", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_fletcher": "Kuovmäker jobber", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_librarian": "Bibliutiker jobber", "subtitles.entity.villager.work_mason": "<PERSON><PERSON> jobber", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_toolsmith": "Rieskaps<PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON>-smið jobber", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON> olld minn", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.hurt": "<PERSON>s<PERSON><PERSON> werd skå<PERSON>", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON> mumbler", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON><PERSON><PERSON> fes<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON><PERSON> drikk mjo<PERSON>", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON><PERSON> drikk <PERSON>k", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON><PERSON> olld it minn", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON><PERSON> waiser sig", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON><PERSON> byter að sig", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON> olld minn", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.ambient": "Wakt gną̊ler", "subtitles.entity.warden.angry": "<PERSON>akt werd j<PERSON>k", "subtitles.entity.warden.attack_impact": "Waktatakk itter ą̊", "subtitles.entity.warden.death": "Wakt d<PERSON>'är", "subtitles.entity.warden.dig": "Wakt gräver", "subtitles.entity.warden.emerge": "Wakt klaiv upp", "subtitles.entity.warden.heartbeat": "Waktjärtą slår", "subtitles.entity.warden.hurt": "Wakt werd skåð<PERSON>ð", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> ly<PERSON>er", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> lyðer j<PERSON>", "subtitles.entity.warden.nearby_close": "Wakt kumb nemmera", "subtitles.entity.warden.nearby_closer": "Wakt far framter", "subtitles.entity.warden.nearby_closest": "Wakt ir nęr nų", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON> r<PERSON>ot", "subtitles.entity.warden.sniff": "Wakt weðre", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> remer", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> led<PERSON> upp", "subtitles.entity.warden.step": "Wakt gą̊r", "subtitles.entity.warden.tendril_clicks": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.wind_charge.throw": "Vin<PERSON><PERSON>dning flyger", "subtitles.entity.wind_charge.wind_burst": "Vindladdning exploderar", "subtitles.entity.witch.ambient": "Trullkelingg fnittrer", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> urrer", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>g där", "subtitles.entity.witch.drink": "T<PERSON>llkelingg drikk", "subtitles.entity.witch.hurt": "T<PERSON><PERSON>kelingg werd skåðåð", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON> winder", "subtitles.entity.wither.ambient": "<PERSON><PERSON> werd j<PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON>er werd skå<PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "Witherbįeraunggel skolder", "subtitles.entity.wither_skeleton.death": "Witherbįeraunggel där", "subtitles.entity.wither_skeleton.hurt": "Witherbįeraunggel werd skåðåð", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON> murrer", "subtitles.entity.wolf.hurt": "<PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.shake": "<PERSON><PERSON> rusker sig", "subtitles.entity.wolf.whine": "<PERSON>arg g<PERSON>r", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> grymter", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> grymter j<PERSON>", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> werd skåðåð", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> gor", "subtitles.entity.zombie.ambient": "Sombi iember sig", "subtitles.entity.zombie.attack_wooden_door": "Döråpar s<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> br<PERSON>", "subtitles.entity.zombie.converted_to_drowned": "Sombi werd ien drukknað sombi", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> där", "subtitles.entity.zombie.destroy_egg": "Stjöldkluossegg sundtrampað", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> werd skå<PERSON>ð", "subtitles.entity.zombie.infect": "Sombi infektirer", "subtitles.entity.zombie_horse.ambient": "Sombiest remer", "subtitles.entity.zombie_horse.death": "So<PERSON><PERSON> där", "subtitles.entity.zombie_horse.hurt": "Sombiest werd skåðåð", "subtitles.entity.zombie_villager.ambient": "Sombibyskall stöner", "subtitles.entity.zombie_villager.converted": "Sombibyskall remer", "subtitles.entity.zombie_villager.cure": "Sombibys<PERSON><PERSON> snurvler", "subtitles.entity.zombie_villager.death": "Sombibyskall där", "subtitles.entity.zombie_villager.hurt": "Sombibyskall werd skåðåð", "subtitles.entity.zombified_piglin.ambient": "Sombifiirað piglin grymter", "subtitles.entity.zombified_piglin.angry": "Sombifiirað piglin grymter l<PERSON>j<PERSON>", "subtitles.entity.zombified_piglin.death": "Sombifiirað piglin där", "subtitles.entity.zombified_piglin.hurt": "Sombifiirað piglin werd skåðåð", "subtitles.event.mob_effect.bad_omen": "Föraning växer sig starkare", "subtitles.event.mob_effect.raid_omen": "<PERSON><PERSON><PERSON> för sig nära", "subtitles.event.mob_effect.trial_omen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> utmaning för sig nära", "subtitles.event.raid.horn": "Spilåuonn låt iemskt", "subtitles.item.armor.equip": "Autrust<PERSON>g settes ą̊", "subtitles.item.armor.equip_chain": "Ringgbrynja klingger", "subtitles.item.armor.equip_diamond": "Diamantrustningg bamber", "subtitles.item.armor.equip_elytra": "Elytra skrävär", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON><PERSON><PERSON> klingger", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>g skrambler", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gnurker", "subtitles.item.armor.equip_netherite": "Netheriterustningg skrambler", "subtitles.item.armor.equip_turtle": "St<PERSON><PERSON>ldkluossskal dunker", "subtitles.item.armor.equip_wolf": "Vargrustning sätts på", "subtitles.item.armor.unequip_wolf": "Vargrustning tas av", "subtitles.item.axe.scrape": "<PERSON>en ö<PERSON> skräver", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> so barker", "subtitles.item.axe.wax_off": "<PERSON><PERSON> tå<PERSON><PERSON> die<PERSON>", "subtitles.item.bone_meal.use": "Biemyöleð werd autspri´tt", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON>", "subtitles.item.book.put": "Buok eves in", "subtitles.item.bottle.empty": "<PERSON><PERSON> ty<PERSON>", "subtitles.item.bottle.fill": "<PERSON><PERSON> fylles <PERSON>", "subtitles.item.brush.brushing.generic": "Någonting borstar", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON> borstas", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON> borstas färdigt", "subtitles.item.brush.brushing.sand": "Sand borstas", "subtitles.item.brush.brushing.sand.complete": "Sand borstas färdigt", "subtitles.item.bucket.empty": "Kruk työmes", "subtitles.item.bucket.fill": "Kruk fylles ǫ", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> fangg<PERSON>", "subtitles.item.bucket.fill_fish": "Fisk faunggað", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON> werd faunggað", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON> työmes", "subtitles.item.bundle.insert": "Föremål duones i", "subtitles.item.bundle.insert_fail": "Föremål ryms inte", "subtitles.item.bundle.remove_one": "Föremål duones yr", "subtitles.item.chorus_fruit.teleport": "Spiler teleportires", "subtitles.item.crop.plant": "Stjörd plantires", "subtitles.item.crossbow.charge": "Armbuost spennes", "subtitles.item.crossbow.hit": "Kuov itter ą̊", "subtitles.item.crossbow.load": "Armbuost leðes", "subtitles.item.crossbow.shoot": "Armbuost stjuotes", "subtitles.item.dye.use": "Färgaðflogur", "subtitles.item.elytra.flying": "Någonting svischar", "subtitles.item.firecharge.use": "Jäldboll fręser", "subtitles.item.flintandsteel.use": "Tendstål äres", "subtitles.item.glow_ink_sac.use": "Glyöðend bliksjäkk skwäkter", "subtitles.item.goat_horn.play": "Djietuonn spiler", "subtitles.item.hoe.till": "Pärgravu brukes", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "Waks eves å", "subtitles.item.horse_armor.unequip": "Hästrustning tas av", "subtitles.item.ink_sac.use": "Glyöðend bliksjäkk skwäter", "subtitles.item.lead.break": "<PERSON><PERSON> går av", "subtitles.item.lead.tied": "<PERSON><PERSON> knyts fast", "subtitles.item.lead.untied": "<PERSON><PERSON> knyts upp", "subtitles.item.llama_carpet.unequip": "Matta tas av", "subtitles.item.lodestone_compass.lock": "Magnetitkompass syktes ǫ magnetit", "subtitles.item.mace.smash_air": "Stridsklubba slår", "subtitles.item.mace.smash_ground": "Stridsklubba slår ned", "subtitles.item.nether_wart.plant": "Stjörd plantires", "subtitles.item.ominous_bottle.dispose": "Flaska förstörs", "subtitles.item.saddle.unequip": "Sadel tas av", "subtitles.item.shears.shear": "Saks klipper", "subtitles.item.shears.snip": "Sax knipsar", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "Spaði platter til", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> smuk<PERSON>", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> waiðges", "subtitles.item.totem.use": "Totem werd aktivirad", "subtitles.item.trident.hit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.trident.hit_ground": "Liuostervibrasjuoner", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON> kumb att", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON> so<PERSON>", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON><PERSON> kling<PERSON>", "subtitles.item.trident.thunder": "Liuosteråsksmell", "subtitles.item.wolf_armor.break": "Vargrustning går sönder", "subtitles.item.wolf_armor.crack": "Vargrustning spricker", "subtitles.item.wolf_armor.damage": "Vargrustning tar skada", "subtitles.item.wolf_armor.repair": "Vargrustning repareras", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "Kart werd ritað", "subtitles.ui.hud.bubble_pop": "Luftmätare sjunker", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> bruka<PERSON>", "subtitles.ui.stonecutter.take_result": "Stįestjärer brukes", "subtitles.weather.rain": "<PERSON><PERSON>", "symlink_warning.message": "Inläsning av världar från mappar med symboliska länkar kan vara riskabelt om du inte vet exakt vad du gör. Besök %s för att lära dig mer.", "symlink_warning.message.pack": "Inläsning av paket med symboliska länkar kan vara riskabelt om du inte vet exakt vad du gör. Besök %s för att lära dig mer.", "symlink_warning.message.world": "Inläsning av världar från mappar med symboliska länkar kan vara riskabelt om du inte vet exakt vad du gör. Besök %s för att lära dig mer.", "symlink_warning.more_info": "Mer information", "symlink_warning.title": "Mappen för världar innehåller symboliska länkar", "symlink_warning.title.pack": "Tillagda paket innehåller symboliska länkar", "symlink_warning.title.world": "Världsmappen innehåller symboliska länkar", "team.collision.always": "<PERSON><PERSON><PERSON><PERSON>", "team.collision.never": "Ollder i wärdn", "team.collision.pushOtherTeams": "<PERSON><PERSON> <PERSON> noð eller lag", "team.collision.pushOwnTeam": "Rek <PERSON> ieget lag", "team.notFound": "Uokunnut lag: '%s'", "team.visibility.always": "<PERSON><PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Gem dig fer oðer lag", "team.visibility.hideForOwnTeam": "Gem dig för ieget lag", "team.visibility.never": "Ollder i wärdn", "telemetry.event.advancement_made.description": "Att förstå orsaken till varför spelare genomför ett visst framsteg kan hjälpa oss att bättre förstå och förbättra spelets utveckling.", "telemetry.event.advancement_made.title": "Genomförande av framsteg", "telemetry.event.game_load_times.description": "<PERSON>na händelse kan hjälpa oss ta reda på vilka förbättringar av uppstartsprestandan som behövs genom att mäta körningstider för uppstartsfaserna.", "telemetry.event.game_load_times.title": "Spelets inläsningstid", "telemetry.event.optional": "%s (valfri)", "telemetry.event.optional.disabled": "%s (valfri) – inaktiverad", "telemetry.event.performance_metrics.description": "Att känna till Minecrafts övergripande prestandaprofil hjälper oss att justera och optimera spelet mot en bredare grupp datorspecifikationer och operativsystem. \nSpelversionen inkluderas för att hjälpa oss att jämföra prestandaprofilen mot nya versioner av Minecraft.", "telemetry.event.performance_metrics.title": "Prestandamätning", "telemetry.event.required": "%s (nödvändig)", "telemetry.event.world_load_times.description": "Det är viktigt för oss att förstå hur lång tid det tar att ansluta till en värld och hur detta förändras över tid. När vi till exempel lägger till nya funktioner eller utför större tekniska förändringar måste vi se vilken inverkan det har på inläsningstider.", "telemetry.event.world_load_times.title": "Världens inläsningstid", "telemetry.event.world_loaded.description": "Att känna till hur Minecraft spelas (t. ex. vilka spellägen, moddade klienter eller servrar och spelversioner som används) till<PERSON>ter oss fokusera på speluppdateringar för att förbättra de delar som spelare bryr sig mest om.\nHändelserna för världens inläsning och avlastning är sammankopplade för att beräkna hur lång tid spelsessionen har pågått.", "telemetry.event.world_loaded.title": "Inläsning av världen", "telemetry.event.world_unloaded.description": "<PERSON>na händelse är sammankopplad med händelsen för inläsning för att beräkna hur lång tid världssessionen har pågått.\nTiden (i sekunder och tickningar) mäts när en världssession har avslutats (när du återgår till huvudmenyn eller lämnar en server).", "telemetry.event.world_unloaded.title": "Avlastning av världen", "telemetry.property.advancement_game_time.title": "Speltid (tickningar)", "telemetry.property.advancement_id.title": "Framstegs-ID", "telemetry.property.client_id.title": "Klient-ID", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON> klient", "telemetry.property.dedicated_memory_kb.title": "Dedikerat minne (kB)", "telemetry.property.event_timestamp_utc.title": "Tidsstämpel för h<PERSON> (UTC)", "telemetry.property.frame_rate_samples.title": "Stick<PERSON>rov av bildfrekvens (bildrutor per sekund)", "telemetry.property.game_mode.title": "Spelläge", "telemetry.property.game_version.title": "Spelversion", "telemetry.property.launcher_name.title": "Programstartarens namn", "telemetry.property.load_time_bootstrap_ms.title": "Uppstartstid (millisekunder)", "telemetry.property.load_time_loading_overlay_ms.title": "Tid på inläsningsskärmen (millisekunder)", "telemetry.property.load_time_pre_window_ms.title": "Tid innan fönstret öppnas (millisekunder)", "telemetry.property.load_time_total_time_ms.title": "Total inläsningstid (millisekunder)", "telemetry.property.minecraft_session_id.title": "Minecrafts sessions-ID", "telemetry.property.new_world.title": "Ny värld", "telemetry.property.number_of_samples.title": "<PERSON><PERSON>", "telemetry.property.operating_system.title": "Operativsystem", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Innehåll i Realms-kartor (minispelsnamn)", "telemetry.property.render_distance.title": "Renderingsavstånd", "telemetry.property.render_time_samples.title": "Stickprov av renderingstid", "telemetry.property.seconds_since_load.title": "Tid sedan inläsning (sekunder)", "telemetry.property.server_modded.title": "Moddad server", "telemetry.property.server_type.title": "Servertyp", "telemetry.property.ticks_since_load.title": "Tid sedan inläsning (tickningar)", "telemetry.property.used_memory_samples.title": "<PERSON>-minne som används", "telemetry.property.user_id.title": "Användar-ID", "telemetry.property.world_load_time_ms.title": "Världens inläsningstid (millisekunder)", "telemetry.property.world_session_id.title": "Världens sessions-ID", "telemetry_info.button.give_feedback": "Ge återkoppling", "telemetry_info.button.privacy_statement": "Sekretesspolicy", "telemetry_info.button.show_data": "Visa min data", "telemetry_info.opt_in.description": "<PERSON><PERSON> samtycker till att skicka valfri telemetridata", "telemetry_info.property_title": "Data som inkluderas", "telemetry_info.screen.description": "Att samla in denna data hjälper oss förbättra Minecraft genom att visa vad som är mest relevant för våra spelare.\nDu kan även skicka in ytterligare återkoppling för att hjälpa oss förbättra Minecraft.", "telemetry_info.screen.title": "Insamling av telemetridata", "test.error.block_property_mismatch": "Egenskapen %s förväntades vara %s, men %s hittades", "test.error.block_property_missing": "Blockegenskap saknas. Egenskapen %s förväntades vara %s", "test.error.entity_property": "Entiteten %s gjorde att testet misslyckades: %s", "test.error.entity_property_details": "Entiteten %s gjorde att testet misslyckades: %s, %s förväntades, %s hittades", "test.error.expected_block": "Blocket %s förväntades, men %s hittades", "test.error.expected_block_tag": "Ett block i #%s förväntades, men %s hittades", "test.error.expected_container_contents": "Behållaren bör endast innehålla föremålen: %s", "test.error.expected_container_contents_single": "Behållaren bör endast innehålla föremålet: %s", "test.error.expected_empty_container": "<PERSON><PERSON><PERSON><PERSON><PERSON> bör vara tom", "test.error.expected_entity": "%s förväntades", "test.error.expected_entity_around": "%s förväntades finnas runtomkring %s, %s, %s", "test.error.expected_entity_count": "%s entiteter av typen %s förväntades, men %s hittades", "test.error.expected_entity_data": "Entitetsdata förväntades vara %s, men %s hittades", "test.error.expected_entity_data_predicate": "Entitetsdata matchar inte %s", "test.error.expected_entity_effect": "%s förväntades ha effekten %s %s", "test.error.expected_entity_having": "Entitetens förråd bör innehålla %s", "test.error.expected_entity_holding": "Entiteten bör hålla i %s", "test.error.expected_entity_in_test": "%s förväntades finnas i testet", "test.error.expected_entity_not_touching": "%s förväntades inte vidröra %s, %s, %s (relativt: %s, %s, %s)", "test.error.expected_entity_touching": "%s förväntades vidröra %s, %s, %s (relativt: %s, %s, %s)", "test.error.expected_item": "Föremål av typen %s förväntades", "test.error.expected_items_count": "%s föremål av typen %s förväntades, men %s hittades", "test.error.fail": "Villkor<PERSON> för att misslyckas har uppnåtts", "test.error.invalid_block_type": "Oväntad blocktyp hittades: %s", "test.error.missing_block_entity": "Blockentitet saknas", "test.error.position": "%s vid %s, %s, %s (relativt: %s, %s, %s) på tickning %s", "test.error.sequence.condition_already_triggered": "Villkoret har redan utlösts vid %s", "test.error.sequence.condition_not_triggered": "Villkoret har inte utlösts", "test.error.sequence.invalid_tick": "Genomfördes på ogiltig tickning: %s förväntades", "test.error.sequence.not_completed": "Testets tidsgräns uppnåddes innan sekvensen slutfördes", "test.error.set_biome": "Misslyckades att ändra biom under testkörningen", "test.error.spawn_failure": "Misslyckades att skapa entiteten %s", "test.error.state_not_equal": "Felaktigt tillstånd. %s förväntades, men %s hittades", "test.error.structure.failure": "Misslyckades att placera teststrukturen för %s", "test.error.tick": "%s på tickningen %s", "test.error.ticking_without_structure": "Testet tickade innan strukturen placerades", "test.error.timeout.no_result": "Varken genomfördes eller misslyckades inom %s tickningar", "test.error.timeout.no_sequences_finished": "Inga sekvenser slutfördes inom %s tickningar", "test.error.too_many_entities": "Endast en %s förväntades finnas runtomkring %s, %s, %s men %s hittades", "test.error.unexpected_block": "Blocket förväntades inte vara %s", "test.error.unexpected_entity": "%s förväntades inte att finnas", "test.error.unexpected_item": "Föremål av typen %s förväntades inte", "test.error.unknown": "Okänt internt fel: %s", "test.error.value_not_equal": "%s förväntades vara %s, men %s hittades", "test.error.wrong_block_entity": "Fel typ av blockentitet: %s", "test_block.error.missing": "Teststrukturen saknar block av typen \"%s\"", "test_block.error.too_many": "För m<PERSON>nga block av typen \"%s\"", "test_block.invalid_timeout": "Ogiltig tidsgräns (%s) – måste vara ett positivt antal tickningar", "test_block.message": "Meddelande:", "test_block.mode.accept": "Godkä<PERSON>", "test_block.mode.fail": "Miss.", "test_block.mode.log": "Loggning", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Godkännandeläge – acceptera att ett test (delvis) lyckas", "test_block.mode_info.fail": "Misslyckandeläge – gör att testet misslyckas", "test_block.mode_info.log": "Loggningsläge – logga ett meddelande", "test_block.mode_info.start": "Startläge – utgångspunkten för ett test", "test_instance.action.reset": "<PERSON><PERSON>t<PERSON><PERSON> och läs in", "test_instance.action.run": "<PERSON><PERSON><PERSON> in och kör", "test_instance.action.save": "<PERSON>ra struktur", "test_instance.description.batch": "Datamängd: %s", "test_instance.description.failed": "Misslyckades: %s", "test_instance.description.function": "Funktion: %s", "test_instance.description.invalid_id": "Ogiltigt test-ID", "test_instance.description.no_test": "<PERSON>get sådant test hittades", "test_instance.description.structure": "Struktur: %s", "test_instance.description.type": "Typ: %s", "test_instance.type.block_based": "Blockbaserat test", "test_instance.type.function": "Inbyggt funktionstest", "test_instance_block.entities": "Entiteter:", "test_instance_block.error.no_test": "Kunde inte köra testinstansen vid %s, %s, %s eftersom den har ett odefinierat test", "test_instance_block.error.no_test_structure": "Kunde inte köra testinstansen vid %s, %s, %s eftersom den saknar teststruktur", "test_instance_block.error.unable_to_save": "Kunde inte spara teststrukturmall för testinstansen vid %s, %s, %s", "test_instance_block.invalid": "[ogiltig]", "test_instance_block.reset_success": "Lyckades återställa testet: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Teststrukturens storlek", "test_instance_block.starting": "Startar testet %s", "test_instance_block.test_id": "Testinstansens ID", "title.32bit.deprecation": "Upptäktum 32-bitssytiem: <PERSON><PERSON> kann end du dug it spilå i framtiðę ettersos an fą̊ luv åvå 64-biters!", "title.32bit.deprecation.realms": "Snart fą̊r an luv å<PERSON><PERSON> 64-biters systiem dar an spiler Minecraft, og då dug du int bruk Realms ą̊ issn-jär datorem. Sę fą̊r du luv avslut ollu Realms-prenumerasiuoner fer and.", "title.32bit.deprecation.realms.check": "Visa inte detta meddelande igen", "title.32bit.deprecation.realms.header": "Upptäktum 34-biers systiem", "title.credits": "Upphovsrätt Mojang AB. Distribuera inte!", "title.multiplayer.disabled": "Flierspilerstellningg funggirer it, tjäre: sją̊ yvyr instellningger fer Micrsoftkonto dett.", "title.multiplayer.disabled.banned.name": "Du måste ändra ditt namn innan du kan spela i flerspelarläget", "title.multiplayer.disabled.banned.permanent": "Konto dett ir åvstaingt permanent fer te spilå ą̊ netį", "title.multiplayer.disabled.banned.temporary": "Konto dett ir tillfällut åvstaingt fer te spilå ą̊ netį", "title.multiplayer.lan": "Fliers<PERSON>rer (LAN)", "title.multiplayer.other": "Flierspilerer (Triðpartsserver)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Enspelarläge", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s att %s og %1$s að slutę %s boð og %1$s atte!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "häj %", "translation.test.invalid2": "häj %s", "translation.test.none": "Allo, wärdn!", "translation.test.world": "w<PERSON>rd", "trim_material.minecraft.amethyst": "Ametistmaterial", "trim_material.minecraft.copper": "Kopparmaterial", "trim_material.minecraft.diamond": "Diamantmaterial", "trim_material.minecraft.emerald": "Smaragdmaterial", "trim_material.minecraft.gold": "Guldmaterial", "trim_material.minecraft.iron": "Järnmaterial", "trim_material.minecraft.lapis": "Lapis lazuli-material", "trim_material.minecraft.netherite": "Netheritmaterial", "trim_material.minecraft.quartz": "Kvartsmaterial", "trim_material.minecraft.redstone": "Redstonematerial", "trim_material.minecraft.resin": "Hartsmaterial", "trim_pattern.minecraft.bolt": "Bultförsedda rustningsdetaljer", "trim_pattern.minecraft.coast": "Kustutsmyckat rustningsornament", "trim_pattern.minecraft.dune": "Ökenartat rustningsornament", "trim_pattern.minecraft.eye": "Ögondekorerat rustningsornament", "trim_pattern.minecraft.flow": "Virvelformade rustningsdetaljer", "trim_pattern.minecraft.host": "Gästgivande rustningsdetaljer", "trim_pattern.minecraft.raiser": "Uppfödande rustningsdetaljer", "trim_pattern.minecraft.rib": "Revbensrandigt rustningsornament", "trim_pattern.minecraft.sentry": "Värnande rustningsornament", "trim_pattern.minecraft.shaper": "Formgivande rustningsdetaljer", "trim_pattern.minecraft.silence": "Ljudlösa rustningsdetaljer", "trim_pattern.minecraft.snout": "Trynformat rustningsornament", "trim_pattern.minecraft.spire": "Spiraformat rustningsornament", "trim_pattern.minecraft.tide": "Porlande rustningsornament", "trim_pattern.minecraft.vex": "Osaligt rustningsornament", "trim_pattern.minecraft.ward": "Vaktande rustningsornament", "trim_pattern.minecraft.wayfinder": "Vägvisande rustningsdetaljer", "trim_pattern.minecraft.wild": "Vilt rustningsornament", "tutorial.bundleInsert.description": "Yögerklikk fer te legg til föremǫl", "tutorial.bundleInsert.title": "Bruk ien sjäkk", "tutorial.craft_planks.description": "Buotję min resept edd bellt j<PERSON>pa", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON><PERSON>", "tutorial.find_tree.description": "Flät til að dyö te sambel ą̊ ðig wiðn", "tutorial.find_tree.title": "<PERSON>tt att iett trai", "tutorial.look.description": "Bruk mausę te wenda", "tutorial.look.title": "<PERSON><PERSON><PERSON> um<PERSON>g dig", "tutorial.move.description": "Bruk %s te uppa", "tutorial.move.title": "Bruk %s, %s, %s and %s fer te brågå", "tutorial.open_inventory.description": "Klemm %s", "tutorial.open_inventory.title": "Ev upp matsailn den", "tutorial.punch_tree.description": "Klemm nið %s", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON>", "tutorial.socialInteractions.description": "Tryttj %s fer te ev upp", "tutorial.socialInteractions.title": "Sosial interaksiuoner", "upgrade.minecraft.netherite_upgrade": "Netherituppgradering"}