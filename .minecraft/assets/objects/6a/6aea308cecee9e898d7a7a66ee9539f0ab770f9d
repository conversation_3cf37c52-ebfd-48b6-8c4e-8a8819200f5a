{"accessibility.onboarding.accessibility.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seaded...", "accessibility.onboarding.screen.narrator": "Jutustaja käivitamiseks vajuta enter", "accessibility.onboarding.screen.title": "Tere tulemast Minecrafti!\n\nKas soovid käivitada Jutustaja või külastada hõlbustuse seadeid?", "addServer.add": "Val<PERSON>", "addServer.enterIp": "<PERSON><PERSON> a<PERSON>", "addServer.enterName": "<PERSON><PERSON> nimi", "addServer.resourcePack": "<PERSON><PERSON>", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "lubatud", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Muuda serveri infot", "advMode.command": "Konsooli käsklus", "advMode.mode": "Režiim", "advMode.mode.auto": "<PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Alati aktiivne", "advMode.mode.conditional": "Tingimuslik", "advMode.mode.redstone": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Vajab redstone'i", "advMode.mode.sequence": "<PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "Tingi<PERSON><PERSON><PERSON>", "advMode.notAllowed": "Pead olema <PERSON>žiimis operaator", "advMode.notEnabled": "Käsuplokid pole selles serveris lubatud", "advMode.previousOutput": "<PERSON><PERSON>mine väljund", "advMode.setCommand": "<PERSON><PERSON><PERSON><PERSON> konsooli käsklus ploki jaoks", "advMode.setCommand.success": "Käsklus seatud: %s", "advMode.trackOutput": "Jälgi väljundit", "advMode.triggering": "Käivitamine", "advMode.type": "<PERSON><PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "<PERSON>ndmatu ed<PERSON>: %s", "advancements.adventure.adventuring_time.description": "Avasta iga bioom", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON>", "advancements.adventure.arbalistic.description": "Tapa ühe ammulasuga viis erinevat elukat", "advancements.adventure.arbalistic.title": "<PERSON><PERSON><PERSON><PERSON> ambur", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON> sculk-sensori või valvuri lähedal, et vältida sellel su kuulmist", "advancements.adventure.avoid_vibration.title": "Hiilimine 100", "advancements.adventure.blowback.description": "Tapa üks briis briisi-lastud p<PERSON> tuule<PERSON>uga", "advancements.adventure.blowback.title": "Tagasipõrge", "advancements.adventure.brush_armadillo.description": "Hangi vöölaselt harja kasutades vöölassoomuseid", "advancements.adventure.brush_armadillo.title": "Vöölase vöömaterjal", "advancements.adventure.bullseye.description": "Taba noolega märklaua keskkohta vähemalt 30 meetri kauguselt", "advancements.adventure.bullseye.title": "Täistabamus", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "<PERSON><PERSON> nel<PERSON>t potiki<PERSON>t kaunistatud pott", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON> taastamine", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON> meisterdaja l<PERSON>al, kui see meisterdab meisterdaja", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> meisterdamas meisterda<PERSON>", "advancements.adventure.fall_from_world_height.description": "Vabalange maailma tipust (ehitamise <PERSON>) maailma allserva ning jää sealjuures ellu", "advancements.adventure.fall_from_world_height.title": "<PERSON><PERSON><PERSON> ja kaljud", "advancements.adventure.heart_transplanter.description": "Aseta kriiksuv süda õiget pidi kahe kahvatust tammest palgi vahele", "advancements.adventure.heart_transplanter.title": "Südamesiirdaja", "advancements.adventure.hero_of_the_village.description": "<PERSON><PERSON><PERSON> küla edukalt reidi eest", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON> ka<PERSON>", "advancements.adventure.honey_block_slide.description": "<PERSON><PERSON><PERSON>, et kukku<PERSON> pehmendada", "advancements.adventure.honey_block_slide.title": "Kleepuv olukord", "advancements.adventure.kill_a_mob.description": "Tapa mõni v<PERSON><PERSON><PERSON> kole<PERSON>", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON>ise<PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "Tapa üks igast vaenulikust koletisest", "advancements.adventure.kill_all_mobs.title": "Koletised kütitud", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Tapa üks elukas sculk-katalüsaatori lähedal", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "See levib", "advancements.adventure.lighten_up.description": "<PERSON><PERSON><PERSON> k<PERSON> v<PERSON>, et see eredamaks muuta", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Kaitse külaelanikku ebameeldivast šokist ilma tuld alustamata", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Ülepingekaitse", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON> katsu<PERSON><PERSON><PERSON> sisse", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: proovi(lepaneku) versioon", "advancements.adventure.ol_betsy.description": "Lase ammust üks nool", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON> hea <PERSON>", "advancements.adventure.overoverkill.description": "Põhjusta oganuia ühe löögiga 50 südame võrra kahju", "advancements.adventure.overoverkill.title": "Ü<PERSON><PERSON><PERSON><PERSON>ping<PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Ärata aasad ellu plaadimängija he<PERSON> muusikaga", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON><PERSON> komparaatori abil tahutud raa<PERSON><PERSON><PERSON><PERSON> vool<PERSON>i", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Raamatute võim", "advancements.adventure.revaulting.description": "Ava kurjakuulutav varahoidla kurjakuulutava katsumuse võtmega", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON><PERSON> varandus", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON>, avastamine ja võitlus", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "<PERSON><PERSON><PERSON> ka<PERSON> plokki potikillu sa<PERSON>ks", "advancements.adventure.salvage_sherd.title": "Jäänuste austamine", "advancements.adventure.shoot_arrow.description": "<PERSON>e midagi noolega", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "<PERSON>ga oma ta<PERSON> muutmise<PERSON> voodis", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> unenägusid", "advancements.adventure.sniper_duel.description": "Tapa üks luukere vähemalt 50 meetri kauguselt", "advancements.adventure.sniper_duel.title": "Snai<PERSON>rite duell", "advancements.adventure.spyglass_at_dragon.description": "Vaata enderdraakonit läbi pik<PERSON>ilma", "advancements.adventure.spyglass_at_dragon.title": "On see lennuk?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON><PERSON> g<PERSON> lä<PERSON> p<PERSON>", "advancements.adventure.spyglass_at_ghast.title": "On see <PERSON><PERSON><PERSON><PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON><PERSON> papa<PERSON><PERSON> lä<PERSON> p<PERSON>", "advancements.adventure.spyglass_at_parrot.title": "On see lind?", "advancements.adventure.summon_iron_golem.description": "<PERSON><PERSON><PERSON>, et aidata k<PERSON>la ka<PERSON>ta", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON><PERSON> abiline", "advancements.adventure.throw_trident.description": "Viska kolmhark millegi suunas.\nMärkus: <PERSON><PERSON> ainsa relva ära viskamine ei ole hea mõte.", "advancements.adventure.throw_trident.title": "Kaigas kodarasse", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON><PERSON> surma petmiseks ebasurma tootemit", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON> p<PERSON>t surma", "advancements.adventure.trade.description": "Vaheta külaelanikuga edukalt kaupa", "advancements.adventure.trade.title": "Hea tehing!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON><PERSON> k<PERSON>ela<PERSON>uga ehitamise kõrguspiirang<PERSON> tasemel", "advancements.adventure.trade_at_world_height.title": "Tippkaupleja", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON> rüüle järgnevaid sepistusmalle vähemalt üks kord: keer<PERSON>, k<PERSON><PERSON><PERSON>, ribi, eestkostja, vai<PERSON>, p<PERSON><PERSON><PERSON>, loode ja teele<PERSON>ja", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Stiilne sepistamine", "advancements.adventure.trim_with_any_armor_pattern.description": "Meisterda sepistuslaual ornamendiga rüü", "advancements.adventure.trim_with_any_armor_pattern.title": "Uue välimuse loomine", "advancements.adventure.two_birds_one_arrow.description": "Tapa läbistava noolega kaks fantoomi", "advancements.adventure.two_birds_one_arrow.title": "Ka<PERSON> kärbest ühe noolega", "advancements.adventure.under_lock_and_key.description": "Ava <PERSON> katsumuse võtmega", "advancements.adventure.under_lock_and_key.title": "<PERSON><PERSON>", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON><PERSON> kompassi teekivil", "advancements.adventure.use_lodestone.title": "<PERSON>s on minu koduke", "advancements.adventure.very_very_frightening.description": "Taba välguga külaelanikku", "advancements.adventure.very_very_frightening.title": "<PERSON>rm naha vahel", "advancements.adventure.voluntary_exile.description": "Ta<PERSON> reidi kapten.\nVõib-olla tasuks mõnda aega kü<PERSON>t eemale hoida...", "advancements.adventure.voluntary_exile.title": "Vabatahtlik pagendus", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON><PERSON><PERSON><PERSON> puuderlumel... ilma sisse vajumata", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON> k<PERSON>", "advancements.adventure.who_needs_rockets.description": "<PERSON><PERSON><PERSON>, et end 8 plokki <PERSON>les lasta", "advancements.adventure.who_needs_rockets.title": "Kes rakette vajab?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>, mida tema teeb teistele", "advancements.adventure.whos_the_pillager_now.title": "<PERSON><PERSON> rüü<PERSON> nüüd?", "advancements.empty": "Siin ei paista midagi olevat...", "advancements.end.dragon_breath.description": "<PERSON><PERSON> dra<PERSON>oni hinge<PERSON><PERSON> k<PERSON>", "advancements.end.dragon_breath.title": "Sa vajad p<PERSON>", "advancements.end.dragon_egg.description": "<PERSON><PERSON> käes draakoni<PERSON>na", "advancements.end.dragon_egg.title": "Järgmine põlvkond", "advancements.end.elytra.description": "<PERSON><PERSON>", "advancements.end.elytra.title": "<PERSON><PERSON><PERSON> on piiriks", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON><PERSON>", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON>", "advancements.end.find_end_city.description": "Mine edasi, mis ikka juhtuda saab?", "advancements.end.find_end_city.title": "<PERSON><PERSON> k<PERSON>", "advancements.end.kill_dragon.description": "Õnn kaasa", "advancements.end.kill_dragon.title": "Vabasta End", "advancements.end.levitate.description": "Kerki šulkeri ründe tagajärjel 50 ploki kõrgusele", "advancements.end.levitate.title": "<PERSON><PERSON> avaneb hea vaade", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.respawn_dragon.title": "<PERSON><PERSON><PERSON> see End!", "advancements.end.root.description": "<PERSON><PERSON>i hoopis lend?", "advancements.end.root.title": "End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON><PERSON> r<PERSON><PERSON>l visata kook noodiploki juurde", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Sünnipäevalaul", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON><PERSON> rahu<PERSON>l omale esemeid tuua", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON> olen sinu sõber", "advancements.husbandry.axolotl_in_a_bucket.description": "Püüa a<PERSON>", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON><PERSON> k<PERSON>, mi<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, isegi kui see pole tervislik", "advancements.husbandry.balanced_diet.title": "Tasakaalustatud toitumine", "advancements.husbandry.breed_all_animals.description": "Pa<PERSON>ta kõiki loomi!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON> kaupa", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON><PERSON> kahte looma", "advancements.husbandry.breed_an_animal.title": "<PERSON><PERSON><PERSON> toob lapsi", "advancements.husbandry.complete_catalogue.description": "Kodusta kõik kassiliigid!", "advancements.husbandry.complete_catalogue.title": "Kass tõesti kõik?!", "advancements.husbandry.feed_snifflet.description": "<PERSON>", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON><PERSON> nuusutused", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON><PERSON><PERSON> kala", "advancements.husbandry.fishy_business.title": "Kes ei tööta, see ei söö", "advancements.husbandry.froglights.description": "<PERSON><PERSON> se<PERSON> kõiki konnavalguseid", "advancements.husbandry.froglights.title": "Ühisel jõul!", "advancements.husbandry.kill_axolotl_target.description": "Võta aksolotl oma paariliseks ning võida võitlus", "advancements.husbandry.kill_axolotl_target.title": "Sõpruse tervendav jõud!", "advancements.husbandry.leash_all_frog_variants.description": "Püüa iga konnavariant köie otsa", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON>i jagu linna h<PERSON>", "advancements.husbandry.make_a_sign_glow.description": "Pane mistahes sildi tekst hõõguma", "advancements.husbandry.make_a_sign_glow.title": "Hiilgav jutt!", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON><PERSON> k<PERSON> täiendamiseks netheriidikangi, see<PERSON><PERSON><PERSON> hinda ümber oma elu valikud", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON><PERSON> pühend<PERSON>ne", "advancements.husbandry.obtain_sniffer_egg.description": "Hangi nuuskija muna", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.place_dried_ghast_in_water.description": "<PERSON><PERSON> kui<PERSON> ghasti plokk vette", "advancements.husbandry.place_dried_ghast_in_water.title": "Niisuta ennast!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON><PERSON> mistahes nuuskija seeme", "advancements.husbandry.plant_any_sniffer_seed.title": "Mineviku istutamine", "advancements.husbandry.plant_seed.description": "Istuta <PERSON>ks seeme ja jälgi selle kasva<PERSON>", "advancements.husbandry.plant_seed.title": "Tärkav seeme", "advancements.husbandry.remove_wolf_armor.description": "Eemalda hundilt kääre kasutades hundirüü", "advancements.husbandry.remove_wolf_armor.title": "Üheksa korda mõõ<PERSON>", "advancements.husbandry.repair_wolf_armor.description": "Paranda kahjustunud hundirü<PERSON> t<PERSON>, kasutades vöölassoomuseid", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> uus", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON><PERSON> paati ja seila koos kitsega", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Aita kits üle vee!", "advancements.husbandry.root.description": "Ma<PERSON><PERSON> on täis sõpru ja toitu", "advancements.husbandry.root.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.safely_harvest_honey.description": "<PERSON><PERSON><PERSON>, et koguda pudeliga mesitarust mett mesilasi ärritamata", "advancements.husbandry.safely_harvest_honey.title": "Kost<PERSON> end meega", "advancements.husbandry.silk_touch_nest.description": "Liiguta 3 mesilasega mesilaspesa või mesitaru, ka<PERSON><PERSON><PERSON> siid<PERSON>t puudutust", "advancements.husbandry.silk_touch_nest.title": "Su<PERSON>v <PERSON>mber<PERSON>lim<PERSON>", "advancements.husbandry.tactical_fishing.description": "<PERSON><PERSON><PERSON>a kala... ilma <PERSON>!", "advancements.husbandry.tactical_fishing.title": "Taktikaline kalastamine", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON><PERSON><PERSON><PERSON> kull<PERSON> ämb<PERSON>", "advancements.husbandry.tadpole_in_a_bucket.title": "Krooksuv ämber", "advancements.husbandry.tame_an_animal.description": "Kodusta üks loom", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON>lo<PERSON> vaha maha!", "advancements.husbandry.wax_off.title": "<PERSON>aha maha", "advancements.husbandry.wax_on.description": "Aseta meekärg vaseploki peale!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON>a peale", "advancements.husbandry.whole_pack.description": "Kodusta kõik hundiliigid", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> kari koos", "advancements.nether.all_effects.description": "<PERSON><PERSON> samal ajal kõ<PERSON> mõ<PERSON>", "advancements.nether.all_effects.title": "Kuidas me siia saime?", "advancements.nether.all_potions.description": "Ole korraga kõigi võlujookide mõju all", "advancements.nether.all_potions.title": "Raevukas kokteil", "advancements.nether.brew_potion.description": "Pruuli üks võlujook", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" elu", "advancements.nether.create_beacon.description": "Konstrueeri ja aseta üks majakas", "advancements.nether.create_beacon.title": "Too rikkus majakasse", "advancements.nether.create_full_beacon.description": "<PERSON>ii majakas tä<PERSON>v<PERSON>ims<PERSON>", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON> pig<PERSON> kull<PERSON>", "advancements.nether.distract_piglin.title": "Oh, s<PERSON>rav", "advancements.nether.explore_nether.description": "Avasta kõik Netheri bioomid", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON>", "advancements.nether.fast_travel.description": "<PERSON><PERSON><PERSON>, et reisida 7 km Pealmaailmas", "advancements.nether.fast_travel.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mull", "advancements.nether.find_bastion.description": "Sisene bastionijäänustesse", "advancements.nether.find_bastion.title": "<PERSON><PERSON>, olid majad...", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.find_fortress.title": "<PERSON><PERSON><PERSON><PERSON> kindlus", "advancements.nether.get_wither_skull.description": "<PERSON><PERSON> wither-luukere pealuu", "advancements.nether.get_wither_skull.title": "Õudne hirmutav luukere", "advancements.nether.loot_bastion.description": "Rüüsta bastionijäänustes olevat kirstu", "advancements.nether.loot_bastion.title": "Sõjasead", "advancements.nether.netherite_armor.description": "Hangi terve netheriitrüü komplekt", "advancements.nether.netherite_armor.title": "<PERSON>a mind rusudega", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.title": "Sügavustesse peidetud", "advancements.nether.obtain_blaze_rod.description": "Vabasta lõõm tema vardast", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON> nuttev obsidiaan", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON> l<PERSON> si<PERSON>?", "advancements.nether.return_to_sender.description": "Tapa tulekeraga üks ghast", "advancements.nether.return_to_sender.title": "Tag<PERSON><PERSON> sa<PERSON>", "advancements.nether.ride_strider.description": "<PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON> kentsakat seent pulga otsas", "advancements.nether.ride_strider.title": "<PERSON><PERSON><PERSON> paadil on jalad", "advancements.nether.ride_strider_in_overworld_lava.description": "Vii tatsaja piiiikale sõidule Pealmaailma laavajärves", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON>gu oma kodus", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> kaasa suveriided", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Kutsu välja wither", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON> kui lill", "advancements.nether.uneasy_alliance.description": "Päästa Netherist üks ghast, too see turvaliselt Pealmaailma... ja seej<PERSON><PERSON> tapa see", "advancements.nether.uneasy_alliance.title": "Ebakindel liit", "advancements.nether.use_lodestone.description": "<PERSON><PERSON><PERSON> kompassi teekivil", "advancements.nether.use_lodestone.title": "<PERSON>s on minu koduke", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Nõrgenda ja see<PERSON><PERSON><PERSON> ravi z<PERSON>-külaelanik<PERSON>", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Põrgata lend<PERSON>ha kilbiga tagasi", "advancements.story.deflect_arrow.title": "<PERSON><PERSON> t<PERSON>, a<PERSON><PERSON><PERSON>", "advancements.story.enchant_item.description": "Loitsi üks ese loitsimislaual", "advancements.story.enchant_item.title": "Loitsija", "advancements.story.enter_the_end.description": "<PERSON><PERSON><PERSON> End<PERSON> portaali", "advancements.story.enter_the_end.title": "Lõpp?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, s<PERSON><PERSON><PERSON> ja sisene <PERSON> portaali", "advancements.story.enter_the_nether.title": "Me peame sügavamale minema", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON><PERSON> enderi silma", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON> silm", "advancements.story.form_obsidian.description": "Hangi plokk obsidiaani", "advancements.story.form_obsidian.title": "Jää-ämbri väljakutse", "advancements.story.iron_tools.description": "Täienda oma kirkat", "advancements.story.iron_tools.title": "<PERSON><PERSON><PERSON> trio", "advancements.story.lava_bucket.description": "Täida ämber laavaga", "advancements.story.lava_bucket.title": "<PERSON><PERSON> ka<PERSON>", "advancements.story.mine_diamond.description": "Omanda teemante", "advancements.story.mine_diamond.title": "Teemandid!", "advancements.story.mine_stone.description": "<PERSON><PERSON><PERSON> kivi oma uue kirkaga", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "Kaitse end mõne r<PERSON> osaga", "advancements.story.obtain_armor.title": "Pane riidesse", "advancements.story.root.description": "<PERSON><PERSON><PERSON><PERSON> süda ja lugu", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Teemantrüü päästab elusid", "advancements.story.shiny_gear.title": "Kata mind teemantidega", "advancements.story.smelt_iron.description": "<PERSON><PERSON> raud kangiks", "advancements.story.smelt_iron.title": "<PERSON><PERSON> r<PERSON>a", "advancements.story.upgrade_tools.description": "Konstrueeri omale parem kirka", "advancements.story.upgrade_tools.title": "Täienduse saamine", "advancements.toast.challenge": "Väljakutse täidetud!", "advancements.toast.goal": "Eesmärk saavutatud!", "advancements.toast.task": "<PERSON><PERSON><PERSON><PERSON> edasi!", "argument.anchor.invalid": "Sobimatu olemi ankurkoht %s", "argument.angle.incomplete": "Mittetäielik (oodati 1 vaatenurka)", "argument.angle.invalid": "Sobimatu nurk", "argument.block.id.invalid": "Tundmatu plokitüüp \"%s\"", "argument.block.property.duplicate": "Atribuuti \"%s\" saab määrata plokile %s ainult üks kord", "argument.block.property.invalid": "Plokk %$1s ei võta %$3s atribuudiks vastu väärtust \"%$2s\"", "argument.block.property.novalue": "Plokil %2$s oodati atribuudile \"%1$s\" väärtust", "argument.block.property.unclosed": "Ploki oleku atribuutidel oodati sulgevat märki ]", "argument.block.property.unknown": "Plokil %s pole atribuuti \"%s\"", "argument.block.tag.disallowed": "Sildid pole siin lubatud, lubatud on ainult tegelikud plokid", "argument.color.invalid": "Tundmatu värv \"%s\"", "argument.component.invalid": "Sobimatu vestluskomponent: %s", "argument.criteria.invalid": "Tundmatu kriteerium \"%s\"", "argument.dimension.invalid": "Tundmatu dimensioon \"%s\"", "argument.double.big": "Double-arv ei saa olla suurem kui %s, leiti %s", "argument.double.low": "Double-arv ei saa olla väiksem kui %s, leiti %s", "argument.entity.invalid": "Sobimatu nimi või UUID", "argument.entity.notfound.entity": "Olemeid ei leitud", "argument.entity.notfound.player": "Ühtegi mängijat ei leitud", "argument.entity.options.advancements.description": "Mängijad edasij<PERSON>a", "argument.entity.options.distance.description": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.distance.negative": "Vah<PERSON>a ei saa olla negatiivne", "argument.entity.options.dx.description": "Olemid x ja x + dx vahel", "argument.entity.options.dy.description": "Olemid y ja y + dy vahel", "argument.entity.options.dz.description": "Olemid z ja z + dz vahel", "argument.entity.options.gamemode.description": "Mängijad mängurežiimiga", "argument.entity.options.inapplicable": "<PERSON>ik \"%s\" pole siin rakendatav", "argument.entity.options.level.description": "Kogemustase", "argument.entity.options.level.negative": "Tase ei peaks olema negatiivne", "argument.entity.options.limit.description": "Maksimaalne tagastatavate olemite arv", "argument.entity.options.limit.toosmall": "Piirang peab olema vähemalt 1", "argument.entity.options.mode.invalid": "Sobimatu või tundmatu mängurežiim \"%s\"", "argument.entity.options.name.description": "<PERSON><PERSON> nimi", "argument.entity.options.nbt.description": "Olemid NBTga", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON><PERSON> predikaat", "argument.entity.options.scores.description": "<PERSON><PERSON><PERSON> tule<PERSON>", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON>", "argument.entity.options.sort.irreversible": "Sobimatu või tundmatu sorteerimistüüp \"%s\"", "argument.entity.options.tag.description": "Olemid sildiga", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON>", "argument.entity.options.type.description": "<PERSON><PERSON><PERSON>", "argument.entity.options.type.invalid": "Sobimatu või tundmatu olemitüüp \"%s\"", "argument.entity.options.unknown": "<PERSON><PERSON><PERSON><PERSON> valik \"%s\"", "argument.entity.options.unterminated": "<PERSON><PERSON>ti valikute lõppu", "argument.entity.options.valueless": "Valikule \"%s\" oodati väärtust", "argument.entity.options.x.description": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.x_rotation.description": "<PERSON>mi x-pööre", "argument.entity.options.y.description": "y-<PERSON><PERSON><PERSON>t", "argument.entity.options.y_rotation.description": "<PERSON><PERSON> y<PERSON>", "argument.entity.options.z.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.allEntities": "<PERSON><PERSON><PERSON>", "argument.entity.selector.allPlayers": "Kõik mängijad", "argument.entity.selector.missing": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON>", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON>", "argument.entity.selector.not_allowed": "<PERSON>ija pole lubatud", "argument.entity.selector.randomPlayer": "Juhuslik mängija", "argument.entity.selector.self": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.unknown": "Tundmatu valijatüüp \"%s\"", "argument.entity.toomany": "<PERSON>ult üks olem on lubatud, ent sisestatud valija lubab rohkem kui üht olemit", "argument.enum.invalid": "Sobimatu väärtus \"%s\"", "argument.float.big": "Ujukomaarv ei saa olla suurem kui %s, leiti %s", "argument.float.low": "Ujukomaarv ei saa olla väiksem kui %s, leiti %s", "argument.gamemode.invalid": "Tundmatu mängurežiim: %s", "argument.hexcolor.invalid": "Kehtetu kuueteistkümnendvärviline kood \"%s\"", "argument.id.invalid": "Sobimatu ID", "argument.id.unknown": "Tundmatu ID: %s", "argument.integer.big": "Täisarv ei saa olla suurem kui %s, leiti %s", "argument.integer.low": "Täisarv ei saa olla väiksem kui %s, leiti %s", "argument.item.id.invalid": "Tundmatu ese \"%s\"", "argument.item.tag.disallowed": "Sildid pole siin lubatud, lubatud on ainult tegelikud esemed", "argument.literal.incorrect": "Oodati sõnasõnalist \"%s\"", "argument.long.big": "Pikk täisarv ei saa olla suurem kui %s, leiti %s", "argument.long.low": "Pikk täisarv ei saa olla väiksem kui %s, leiti %s", "argument.message.too_long": "Vestlussõnum oli liiga pikk (%s > maksimum %s tähemärki)", "argument.nbt.array.invalid": "Sobimatu massiivitüüp \"%s\"", "argument.nbt.array.mixed": "%s ei saa sisestada kohta %s", "argument.nbt.expected.compound": "<PERSON><PERSON>ti Compund-silti", "argument.nbt.expected.key": "Oodati võtit", "argument.nbt.expected.value": "Oodati väärtust", "argument.nbt.list.mixed": "%s ei saa sisestada %s nimekirja", "argument.nbt.trailing": "<PERSON><PERSON><PERSON><PERSON>", "argument.player.entities": "See käsk<PERSON> peab mõju<PERSON>a ainult mäng<PERSON>id, ent sisestatud valija hõlmab ka olemeid", "argument.player.toomany": "Ainult üks mängija on lubatud, ent sisestatud valija lubab rohkem kui ühte mängijat", "argument.player.unknown": "<PERSON><PERSON> mängijat pole olemas", "argument.pos.missing.double": "<PERSON><PERSON><PERSON>", "argument.pos.missing.int": "<PERSON><PERSON><PERSON> p<PERSON>", "argument.pos.mixed": "Maailma- ja kohalikke koordinaate ei saa omavahel segada (kõik peavad kas kasutama märki ^ või mitte kasutama)", "argument.pos.outofbounds": "See as<PERSON><PERSON><PERSON> on lubatud piiridest väljas.", "argument.pos.outofworld": "See as<PERSON><PERSON>t on sellest maailmast väljas!", "argument.pos.unloaded": "See asukoht ei ole la<PERSON>d", "argument.pos2d.incomplete": "Mittetäielik (oodati kahte koordinaati)", "argument.pos3d.incomplete": "Mittetäielik (oodati kolme koordinaati)", "argument.range.empty": "Oodati väärtust või väärtuste vahemikku", "argument.range.ints": "<PERSON><PERSON> t<PERSON><PERSON>, mitte mur<PERSON><PERSON> on lubatud", "argument.range.swapped": "Miini<PERSON> ei saa maksimumist suurem olla", "argument.resource.invalid_type": "Elemendil \"%s\" on vale tüüp \"%s\" (eeldatud \"%s\")", "argument.resource.not_found": "\"%2$s\"-tüüpi elementi \"%1$s\" ei leitud", "argument.resource_or_id.failed_to_parse": "Struktuuri töötlemine ebaõnnestus: %s", "argument.resource_or_id.invalid": "Sobimatu ID või silt", "argument.resource_or_id.no_such_element": "Elementi \"%s\" ei leitud registrist \"%s\"", "argument.resource_selector.not_found": "\"%2$s\"-tüü<PERSON> valijale \"%1$s\" ei leitud vasteid", "argument.resource_tag.invalid_type": "Sildil \"%s\" on vale tüüp \"%s\" (eeldatud \"%s\")", "argument.resource_tag.not_found": "\"%2$s\"-tüüpi silti \"%1$s\" ei leitud", "argument.rotation.incomplete": "Mittetäielik (oodati kahte koordinaati)", "argument.scoreHolder.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> tulemusehoidjaid ei leitud", "argument.scoreboardDisplaySlot.invalid": "Tundmatu kuvalahter \"%s\"", "argument.style.invalid": "Sobimatu stiil: %s", "argument.time.invalid_tick_count": "Tiksude arv peab olema mittenegatiivne", "argument.time.invalid_unit": "<PERSON><PERSON><PERSON><PERSON>", "argument.time.tick_count_too_low": "Tiksuarv ei tohi olla madalam kui %s, leiti %s", "argument.uuid.invalid": "Sobimatu UUID", "argument.waypoint.invalid": "<PERSON>itud olem ei ole teepunkt", "arguments.block.tag.unknown": "Tundmatu plokisilt \"%s\"", "arguments.function.tag.unknown": "Tundmatu funktsioonisilt \"%s\"", "arguments.function.unknown": "Tundmatu funktsioon %s", "arguments.item.component.expected": "Oodati eseme komponenti", "arguments.item.component.malformed": "Kahjustunud \"%s\" komponent: \"%s\"", "arguments.item.component.repeated": "Eseme komponent \"%s\" kordus, kuid määrata saab vaid ühte väärtust", "arguments.item.component.unknown": "Tundmatu eseme komponent \"%s\"", "arguments.item.malformed": "Ka<PERSON>ju<PERSON><PERSON><PERSON> ese: \"%s\"", "arguments.item.overstacked": "%s suurim kuhja suurus on %s", "arguments.item.predicate.malformed": "Kahjustunud \"%s\" predikaat: \"%s\"", "arguments.item.predicate.unknown": "Tundmatu esemepredikaat: \"%s''", "arguments.item.tag.unknown": "Tundmatu esemesilt \"%s\"", "arguments.nbtpath.node.invalid": "Sobimatu NBT-tee element", "arguments.nbtpath.nothing_found": "Ükski element ei vasta NBT-teele %s", "arguments.nbtpath.too_deep": "Tulenev NBT on liiga sügavate harudega", "arguments.nbtpath.too_large": "Tulenev NBT on liiga suur", "arguments.objective.notFound": "Tundmatu tulemustabeli siht \"%s\"", "arguments.objective.readonly": "Tulemustabeli siht \"%s\" on kirjutuskaitstud", "arguments.operation.div0": "Nulliga ei saa jagada", "arguments.operation.invalid": "<PERSON><PERSON><PERSON><PERSON> toiming", "arguments.swizzle.invalid": "So<PERSON>mat<PERSON> tel<PERSON>, o<PERSON><PERSON> \"x\", \"y\" ja \"z\" komb<PERSON><PERSON><PERSON>", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "rüütugevust", "attribute.name.attack_damage": "löögikahju", "attribute.name.attack_knockback": "<PERSON><PERSON><PERSON>", "attribute.name.attack_speed": "ründekiirust", "attribute.name.block_break_speed": "ploki lõhkumiskiirust", "attribute.name.block_interaction_range": "plokitoimingute kaugust", "attribute.name.burning_time": "põlemisaega", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON> ka<PERSON>", "attribute.name.entity_interaction_range": "olemitoimingute kaugust", "attribute.name.explosion_knockback_resistance": "plahvatuse tagasilö<PERSON>", "attribute.name.fall_damage_multiplier": "kuk<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>t", "attribute.name.flying_speed": "lendamiskiirust", "attribute.name.follow_range": "eluka<PERSON> j<PERSON><PERSON>", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "rüütugevust", "attribute.name.generic.attack_damage": "löögikahju", "attribute.name.generic.attack_knockback": "r<PERSON><PERSON> tagasilöök", "attribute.name.generic.attack_speed": "ründekiir<PERSON>", "attribute.name.generic.block_interaction_range": "plokitoimingute kaugust", "attribute.name.generic.burning_time": "põlemisaega", "attribute.name.generic.entity_interaction_range": "olemitoimingute kaugust", "attribute.name.generic.explosion_knockback_resistance": "plahvatuse tagasilö<PERSON>", "attribute.name.generic.fall_damage_multiplier": "kuk<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>t", "attribute.name.generic.flying_speed": "lendamiskiirus", "attribute.name.generic.follow_range": "eluka<PERSON> j<PERSON><PERSON>", "attribute.name.generic.gravity": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "<PERSON><PERSON><PERSON><PERSON><PERSON>vu<PERSON>", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "maks<PERSON><PERSON><PERSON> neeldumist", "attribute.name.generic.max_health": "maks<PERSON><PERSON><PERSON> tervist", "attribute.name.generic.movement_efficiency": "liikumistõhusust", "attribute.name.generic.movement_speed": "kiirust", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "turvalist kukkumiskõrgust", "attribute.name.generic.scale": "ska<PERSON>t", "attribute.name.generic.step_height": "<PERSON><PERSON><PERSON>", "attribute.name.generic.water_movement_efficiency": "vees liiku<PERSON> t<PERSON>", "attribute.name.gravity": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "ho<PERSON><PERSON> hüppe<PERSON>gevust", "attribute.name.jump_strength": "<PERSON><PERSON><PERSON><PERSON><PERSON>vu<PERSON>", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "maks<PERSON><PERSON><PERSON> neeldumist", "attribute.name.max_health": "maks<PERSON><PERSON><PERSON> tervist", "attribute.name.mining_efficiency": "kaevandustõhusust", "attribute.name.movement_efficiency": "liikumistõhusust", "attribute.name.movement_speed": "kiirust", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "ploki lõhkumiskiirust", "attribute.name.player.block_interaction_range": "plokitoimingute kaugust", "attribute.name.player.entity_interaction_range": "olemitoimingute kaugust", "attribute.name.player.mining_efficiency": "kaevandustõhusust", "attribute.name.player.sneaking_speed": "hiilimiskiirust", "attribute.name.player.submerged_mining_speed": "veealust kaevandustõhusust", "attribute.name.player.sweeping_damage_ratio": "pühkiva löögi kah<PERSON>t", "attribute.name.safe_fall_distance": "turvalist kukkumiskõrgust", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "hiilimiskiirust", "attribute.name.spawn_reinforcements": "zombi abivägesid", "attribute.name.step_height": "astumisk<PERSON><PERSON><PERSON>", "attribute.name.submerged_mining_speed": "veealust kaevandustõhusust", "attribute.name.sweeping_damage_ratio": "pühkiva löögi kah<PERSON>t", "attribute.name.tempt_range": "<PERSON><PERSON>", "attribute.name.water_movement_efficiency": "vees liiku<PERSON> t<PERSON>", "attribute.name.waypoint_receive_range": "teepunkti vastuvõtmisulatust", "attribute.name.waypoint_transmit_range": "<PERSON><PERSON><PERSON><PERSON> ed<PERSON>", "attribute.name.zombie.spawn_reinforcements": "zombi abiväge", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Bamb<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.basalt_deltas": "<PERSON><PERSON><PERSON>", "biome.minecraft.beach": "Rand", "biome.minecraft.birch_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "Karmiinpunane mets", "biome.minecraft.dark_forest": "<PERSON><PERSON> mets", "biome.minecraft.deep_cold_ocean": "Sügav külm <PERSON>an", "biome.minecraft.deep_dark": "Sügav pimedus", "biome.minecraft.deep_frozen_ocean": "Sügav külmunud o<PERSON>an", "biome.minecraft.deep_lukewarm_ocean": "Sügav leige o<PERSON>an", "biome.minecraft.deep_ocean": "Sügav ookean", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Tilkekivi koopad", "biome.minecraft.end_barrens": "<PERSON>i viljatu maa", "biome.minecraft.end_highlands": "<PERSON><PERSON>", "biome.minecraft.end_midlands": "<PERSON><PERSON>", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.forest": "Mets", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON><PERSON> mä<PERSON>d", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "biome.minecraft.grove": "<PERSON><PERSON>", "biome.minecraft.ice_spikes": "Jääogad", "biome.minecraft.jagged_peaks": "Sakilised mäetipud", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON> o<PERSON>an", "biome.minecraft.lush_caves": "Lopsakad koopad", "biome.minecraft.mangrove_swamp": "Mangroovisoo", "biome.minecraft.meadow": "Aas", "biome.minecraft.mushroom_fields": "Seenev<PERSON>ljak", "biome.minecraft.nether_wastes": "<PERSON><PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_pine_taiga": "Ürg-männimets", "biome.minecraft.old_growth_spruce_taiga": "Ürg-kuusemets", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON> aed", "biome.minecraft.plains": "Tasandik", "biome.minecraft.river": "J<PERSON>gi", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Savanniplat<PERSON>", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_beach": "<PERSON><PERSON> rand", "biome.minecraft.snowy_plains": "<PERSON><PERSON>", "biome.minecraft.snowy_slopes": "Lumised <PERSON>", "biome.minecraft.snowy_taiga": "Lumine taiga", "biome.minecraft.soul_sand_valley": "Hingeliivaorg", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.stony_peaks": "Kivised mäetipud", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Päevalilletasandik", "biome.minecraft.swamp": "<PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON> o<PERSON>an", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON> mets", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON> mets", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON> k<PERSON> k<PERSON>d", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "<PERSON><PERSON>", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "Akaatsiast uks", "block.minecraft.acacia_fence": "Akaatsiast aed", "block.minecraft.acacia_fence_gate": "Akaatsiast aiavärav", "block.minecraft.acacia_hanging_sign": "Akaatsiast rippuv silt", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Akaatsiapalk", "block.minecraft.acacia_planks": "Akaatsialauad", "block.minecraft.acacia_pressure_plate": "Akaatsiast surveplaat", "block.minecraft.acacia_sapling": "Akaatsiaistik", "block.minecraft.acacia_sign": "Akaatsiast silt", "block.minecraft.acacia_slab": "Akaatsiaplaat", "block.minecraft.acacia_stairs": "Akaatsiatrepp", "block.minecraft.acacia_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_wall_hanging_sign": "Akaatsiast seinal rippuv silt", "block.minecraft.acacia_wall_sign": "Akaatsiast seinasilt", "block.minecraft.acacia_wood": "Akaatsiapuit", "block.minecraft.activator_rail": "Käivitusrööpad", "block.minecraft.air": "Õhk", "block.minecraft.allium": "Lau<PERSON>", "block.minecraft.amethyst_block": "Ametüstiplokk", "block.minecraft.amethyst_cluster": "Am<PERSON>ü<PERSON><PERSON><PERSON>", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite": "Andesiit", "block.minecraft.andesite_slab": "Andesiitplaat", "block.minecraft.andesite_stairs": "Andesiit-trepp", "block.minecraft.andesite_wall": "Andesiitmüür", "block.minecraft.anvil": "<PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Kinnitunud melonivars", "block.minecraft.attached_pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azalea": "Asalea", "block.minecraft.azalea_leaves": "Asalealehed", "block.minecraft.azure_bluet": "<PERSON>ine inglisilm", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambuseplokk", "block.minecraft.bamboo_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_door": "Bambusest uks", "block.minecraft.bamboo_fence": "Bambusest aed", "block.minecraft.bamboo_fence_gate": "Bambusest aiavärav", "block.minecraft.bamboo_hanging_sign": "Bambusest rippuv silt", "block.minecraft.bamboo_mosaic": "Bambusmosaiik", "block.minecraft.bamboo_mosaic_slab": "Bambusmosaiigi plaat", "block.minecraft.bamboo_mosaic_stairs": "Bambus<PERSON><PERSON><PERSON><PERSON> trepp", "block.minecraft.bamboo_planks": "Bambuslauad", "block.minecraft.bamboo_pressure_plate": "Bambusest surveplaat", "block.minecraft.bamboo_sapling": "Bambusevõrse", "block.minecraft.bamboo_sign": "Bambusest silt", "block.minecraft.bamboo_slab": "Bambusplaat", "block.minecraft.bamboo_stairs": "Bambustrepp", "block.minecraft.bamboo_trapdoor": "Bambusluuk", "block.minecraft.bamboo_wall_hanging_sign": "Bambusest seinal rippuv silt", "block.minecraft.bamboo_wall_sign": "Bambusest seinasilt", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> musta täitega", "block.minecraft.banner.base.blue": "Täielikult sinise täitega", "block.minecraft.banner.base.brown": "Täielikult pruuni täitega", "block.minecraft.banner.base.cyan": "Täielikult tsüaansinise täitega", "block.minecraft.banner.base.gray": "Täielikult halli täitega", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> roh<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.base.light_blue": "Täielikult helesinise täitega", "block.minecraft.banner.base.light_gray": "Täielikult helehalli täitega", "block.minecraft.banner.base.lime": "Täielikult laim<PERSON><PERSON><PERSON> tä<PERSON>ga", "block.minecraft.banner.base.magenta": "Täielikult magentapunase täitega", "block.minecraft.banner.base.orange": "Täielikult oranži t<PERSON>itega", "block.minecraft.banner.base.pink": "Täielikult roosa t<PERSON>ga", "block.minecraft.banner.base.purple": "Täielikult lilla täitega", "block.minecraft.banner.base.red": "Täielikult punase täitega", "block.minecraft.banner.base.white": "Täielikult valge täitega", "block.minecraft.banner.base.yellow": "Täielikult kollase täitega", "block.minecraft.banner.border.black": "<PERSON><PERSON>", "block.minecraft.banner.border.blue": "Sinise poordiga", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.cyan": "Tsüaansinise poordiga", "block.minecraft.banner.border.gray": "<PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "Helesinise poordiga", "block.minecraft.banner.border.light_gray": "Helehalli poordiga", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "Magentapunase <PERSON>", "block.minecraft.banner.border.orange": "Oranži <PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "Valge poordiga", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "Mustade telliste täitega", "block.minecraft.banner.bricks.blue": "Siniste telliste täitega", "block.minecraft.banner.bricks.brown": "Pruunide telliste täitega", "block.minecraft.banner.bricks.cyan": "Tsüaansiniste telliste täitega", "block.minecraft.banner.bricks.gray": "Hallide telliste täitega", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON> tell<PERSON> t<PERSON>", "block.minecraft.banner.bricks.light_blue": "Helesiniste telliste täitega", "block.minecraft.banner.bricks.light_gray": "Helehallide telliste täitega", "block.minecraft.banner.bricks.lime": "Laimiroheliste tell<PERSON> t<PERSON>ga", "block.minecraft.banner.bricks.magenta": "Magentapunaste telliste täitega", "block.minecraft.banner.bricks.orange": "Oranžide telliste täitega", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> telliste t<PERSON>ga", "block.minecraft.banner.bricks.purple": "<PERSON>lade telliste t<PERSON>itega", "block.minecraft.banner.bricks.red": "Punaste telliste täitega", "block.minecraft.banner.bricks.white": "Valgete telliste täitega", "block.minecraft.banner.bricks.yellow": "Kollaste telliste täitega", "block.minecraft.banner.circle.black": "<PERSON><PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Tsüaansinise s<PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Helesinise sõõriga", "block.minecraft.banner.circle.light_gray": "Helehalli sõõriga", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "Magentapunase sõõriga", "block.minecraft.banner.circle.orange": "Oranži sõõriga", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON>ge <PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Musta creeperiembleemiga", "block.minecraft.banner.creeper.blue": "Sinise creeperiembleemiga", "block.minecraft.banner.creeper.brown": "Pruuni creeperiembleemiga", "block.minecraft.banner.creeper.cyan": "Tsüaansinise creeperiembleemiga", "block.minecraft.banner.creeper.gray": "Halli creeperiembleemiga", "block.minecraft.banner.creeper.green": "Rohelise creeperiembleemiga", "block.minecraft.banner.creeper.light_blue": "Helesinise creeperiembleemiga", "block.minecraft.banner.creeper.light_gray": "Helehalli creeperiembleemiga", "block.minecraft.banner.creeper.lime": "Laimirohelise creeperiembleemiga", "block.minecraft.banner.creeper.magenta": "Magentapunase creeperiembleemiga", "block.minecraft.banner.creeper.orange": "Oranži creeperiembleemiga", "block.minecraft.banner.creeper.pink": "Roosa creeperiembleemiga", "block.minecraft.banner.creeper.purple": "Lilla creeperiembleemiga", "block.minecraft.banner.creeper.red": "Punase creeperiembleemiga", "block.minecraft.banner.creeper.white": "Valge creeperiembleemiga", "block.minecraft.banner.creeper.yellow": "Kollase creeperiembleemiga", "block.minecraft.banner.cross.black": "Musta Andrease ristiga", "block.minecraft.banner.cross.blue": "Sinise Andrease ristiga", "block.minecraft.banner.cross.brown": "Pruuni Andrease ristiga", "block.minecraft.banner.cross.cyan": "Tsüaansinise Andrease ristiga", "block.minecraft.banner.cross.gray": "Halli Andrease ristiga", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "Helesinise Andrease ristiga", "block.minecraft.banner.cross.light_gray": "Helehalli Andrease ristiga", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "Magentapunase Andrease ristiga", "block.minecraft.banner.cross.orange": "Oranži Andrease ristiga", "block.minecraft.banner.cross.pink": "<PERSON><PERSON>a Andreas<PERSON> rist<PERSON>", "block.minecraft.banner.cross.purple": "Lilla Andrease ristiga", "block.minecraft.banner.cross.red": "Punase Andrease ristiga", "block.minecraft.banner.cross.white": "Valge Andrease ristiga", "block.minecraft.banner.cross.yellow": "Kollase Andrease ristiga", "block.minecraft.banner.curly_border.black": "Musta sakkidega poordiga", "block.minecraft.banner.curly_border.blue": "Sinise sakkidega poordiga", "block.minecraft.banner.curly_border.brown": "Pruuni sakkidega poordiga", "block.minecraft.banner.curly_border.cyan": "Tsüaansinise sakkidega poordiga", "block.minecraft.banner.curly_border.gray": "<PERSON>i sakkidega poordiga", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "Helesinise sakkidega poordiga", "block.minecraft.banner.curly_border.light_gray": "Helehalli sakkidega poordiga", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "block.minecraft.banner.curly_border.magenta": "Magentapunase sakkidega poordiga", "block.minecraft.banner.curly_border.orange": "Oranži sakkidega poordiga", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> sa<PERSON>ga", "block.minecraft.banner.curly_border.purple": "<PERSON>la sakkidega poor<PERSON>ga", "block.minecraft.banner.curly_border.red": "Punase sakkidega poordiga", "block.minecraft.banner.curly_border.white": "Valge sakkidega poordiga", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON>e sakkidega poordiga", "block.minecraft.banner.diagonal_left.black": "Musta täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.blue": "Sinise täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.brown": "Pruuni täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.cyan": "Tsüaansinise täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.gray": "Halli täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>tud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.light_blue": "Helesinise täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.light_gray": "Helehalli täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.lime": "Laimiroh<PERSON>se t<PERSON>idetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.magenta": "Magentapunase täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.orange": "Oranži täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.pink": "R<PERSON>a täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.purple": "Lilla täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.red": "Punase täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.white": "Valge täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_left.yellow": "Kollase täidetud diagonaaliga üleval paremal", "block.minecraft.banner.diagonal_right.black": "Musta täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.blue": "Sinise täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.brown": "Pruuni täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.cyan": "Tsüaansinise täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.gray": "Halli täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.green": "R<PERSON><PERSON>se täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.light_blue": "Helesinise täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.light_gray": "Helehalli täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.lime": "Laimirohelise täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.magenta": "Magentapunase täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.orange": "Oranži täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.pink": "Roosa täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.purple": "Lilla täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.red": "Punase täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.white": "Valge täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_right.yellow": "Kollase täidetud diagonaaliga üleval vasakul", "block.minecraft.banner.diagonal_up_left.black": "Musta täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.blue": "Sinise täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.brown": "Pruuni täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.cyan": "Tsüaansinise täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.gray": "Halli täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>idetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.light_blue": "Helesinise täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.light_gray": "Helehalli täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.lime": "Laimirohelise täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.magenta": "Magentapunase täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.orange": "Oranži täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.pink": "Roosa täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.purple": "Lilla täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.red": "Punase täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.white": "Valge täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_left.yellow": "Kollase täidetud diagonaaliga all paremal", "block.minecraft.banner.diagonal_up_right.black": "Musta täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.blue": "Sinise täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.brown": "Pruuni täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.cyan": "Tsüaansinise täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.gray": "Halli täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.green": "R<PERSON><PERSON>se täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.light_blue": "Helesinise täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.light_gray": "Helehalli täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.lime": "Laimirohelise täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.magenta": "Magentapunase täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.orange": "Oranži täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.pink": "Roosa täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.purple": "Lilla täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.red": "Punase täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.white": "Valge täidetud diagonaaliga all vasakul", "block.minecraft.banner.diagonal_up_right.yellow": "Kollase täidetud diagonaaliga all vasakul", "block.minecraft.banner.flow.black": "<PERSON>a vooga", "block.minecraft.banner.flow.blue": "Sinise vooga", "block.minecraft.banner.flow.brown": "Pruuni vooga", "block.minecraft.banner.flow.cyan": "Tsüaansinise vooga", "block.minecraft.banner.flow.gray": "<PERSON><PERSON> vooga", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>ga", "block.minecraft.banner.flow.light_blue": "Helesinise vooga", "block.minecraft.banner.flow.light_gray": "Helehalli vooga", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>ga", "block.minecraft.banner.flow.magenta": "Magentapunase vooga", "block.minecraft.banner.flow.orange": "Oranži vooga", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON> vooga", "block.minecraft.banner.flow.purple": "<PERSON><PERSON> vooga", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON> vooga", "block.minecraft.banner.flow.white": "Valge vooga", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON> vooga", "block.minecraft.banner.flower.black": "Musta lilleembleemiga", "block.minecraft.banner.flower.blue": "Sinise lilleembleemiga", "block.minecraft.banner.flower.brown": "Pruuni lilleembleemiga", "block.minecraft.banner.flower.cyan": "Tsüaansinise lilleembleemiga", "block.minecraft.banner.flower.gray": "Halli lilleembleemiga", "block.minecraft.banner.flower.green": "Rohelise lilleembleemiga", "block.minecraft.banner.flower.light_blue": "Helesinise lilleembleemiga", "block.minecraft.banner.flower.light_gray": "Helehalli lilleembleemiga", "block.minecraft.banner.flower.lime": "Laimirohelise lilleembleemiga", "block.minecraft.banner.flower.magenta": "Magentapunase lilleembleemiga", "block.minecraft.banner.flower.orange": "Oranži lilleembleemiga", "block.minecraft.banner.flower.pink": "Roosa lilleembleemiga", "block.minecraft.banner.flower.purple": "Lilla lilleembleemiga", "block.minecraft.banner.flower.red": "Punase lilleembleemiga", "block.minecraft.banner.flower.white": "Valge lilleembleemiga", "block.minecraft.banner.flower.yellow": "Kollase lilleembleemiga", "block.minecraft.banner.globe.black": "Musta gloobusega", "block.minecraft.banner.globe.blue": "Sinise gloobusega", "block.minecraft.banner.globe.brown": "Pruuni gloobusega", "block.minecraft.banner.globe.cyan": "Tsüaansinise gloobusega", "block.minecraft.banner.globe.gray": "Halli glo<PERSON>usega", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.globe.light_blue": "Helesinise gloobusega", "block.minecraft.banner.globe.light_gray": "Helehalli gloobusega", "block.minecraft.banner.globe.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.globe.magenta": "Magentapunase gloobusega", "block.minecraft.banner.globe.orange": "Oranži gloobusega", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON> glo<PERSON>ga", "block.minecraft.banner.globe.purple": "<PERSON><PERSON> glo<PERSON>", "block.minecraft.banner.globe.red": "Punase glo<PERSON>usega", "block.minecraft.banner.globe.white": "Valge gloobusega", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON><PERSON> glo<PERSON>", "block.minecraft.banner.gradient.black": "<PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "Sinise <PERSON>", "block.minecraft.banner.gradient.brown": "P<PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Tsüaansinise üleminekuga", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Helesinise üleminekuga", "block.minecraft.banner.gradient.light_gray": "Helehalli üleminekuga", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.magenta": "Magentapunase üleminekuga", "block.minecraft.banner.gradient.orange": "Oranži <PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient.red": "Punase ülem<PERSON>kuga", "block.minecraft.banner.gradient.white": "Valge üleminekuga", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.gradient_up.blue": "Sinise pööratud ü<PERSON>", "block.minecraft.banner.gradient_up.brown": "Pruuni pööratud <PERSON>", "block.minecraft.banner.gradient_up.cyan": "Tsüaansinise pööratud üleminekuga", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_blue": "Helesinise pööratud üleminekuga", "block.minecraft.banner.gradient_up.light_gray": "Helehalli pööratud ülem<PERSON>kuga", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.magenta": "Magentapunase pööratud üleminekuga", "block.minecraft.banner.gradient_up.orange": "Oranži pööratud üleminekuga", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.red": "Punase pööratud ülem<PERSON>ku<PERSON>", "block.minecraft.banner.gradient_up.white": "Valge pööratud üleminekuga", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.guster.black": "<PERSON><PERSON> pagi<PERSON>ga", "block.minecraft.banner.guster.blue": "<PERSON><PERSON> pagijaga", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> pagijaga", "block.minecraft.banner.guster.cyan": "Tsüaansinise pagijaga", "block.minecraft.banner.guster.gray": "<PERSON><PERSON> pagi<PERSON>ga", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON><PERSON> pagijaga", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON> pagijaga", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "Magentapunase pagijaga", "block.minecraft.banner.guster.orange": "Oranži pagi<PERSON>ga", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON> pagijaga", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON> ülem<PERSON> pool<PERSON>a", "block.minecraft.banner.half_horizontal.blue": "Sinise ülemise poolega", "block.minecraft.banner.half_horizontal.brown": "Pruuni ülemise pool<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "Tsüaansinise ülemise poolega", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON> ülem<PERSON> pool<PERSON>a", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Helesinise ülemise poolega", "block.minecraft.banner.half_horizontal.light_gray": "Helehalli ülemise pool<PERSON>a", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.magenta": "Magentapunase ülemise poolega", "block.minecraft.banner.half_horizontal.orange": "Oranži ülemise poolega", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON> ü<PERSON>", "block.minecraft.banner.half_horizontal.red": "Punase ülemise pool<PERSON>a", "block.minecraft.banner.half_horizontal.white": "Valge ülemise poolega", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON> ü<PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "Musta alumise poolega", "block.minecraft.banner.half_horizontal_bottom.blue": "Sinise alumise poolega", "block.minecraft.banner.half_horizontal_bottom.brown": "Pruuni alumise poolega", "block.minecraft.banner.half_horizontal_bottom.cyan": "Tsüaansinise alumise poolega", "block.minecraft.banner.half_horizontal_bottom.gray": "Halli alumise poolega", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON>se alumise pool<PERSON>a", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Helesinise alumise poolega", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Helehalli alumise poolega", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> alumise poolega", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magentapunase alumise poolega", "block.minecraft.banner.half_horizontal_bottom.orange": "Oranži alumise poolega", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON>oosa alumise poolega", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON>la alumise poolega", "block.minecraft.banner.half_horizontal_bottom.red": "Punase alumise poolega", "block.minecraft.banner.half_horizontal_bottom.white": "Valge alumise poolega", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON>e alumise poolega", "block.minecraft.banner.half_vertical.black": "<PERSON>a parema poolega", "block.minecraft.banner.half_vertical.blue": "<PERSON>ise parema poolega", "block.minecraft.banner.half_vertical.brown": "Pruuni parema poolega", "block.minecraft.banner.half_vertical.cyan": "Tsüaansinise parema poolega", "block.minecraft.banner.half_vertical.gray": "<PERSON>i parema poolega", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON><PERSON> parema <PERSON>a", "block.minecraft.banner.half_vertical.light_blue": "Helesinise parema poolega", "block.minecraft.banner.half_vertical.light_gray": "Helehalli parema poolega", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> parema pool<PERSON>a", "block.minecraft.banner.half_vertical.magenta": "Magentapunase parema poolega", "block.minecraft.banner.half_vertical.orange": "Oranži parema poolega", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON>a parema poolega", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON> parema poolega", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> parema poolega", "block.minecraft.banner.half_vertical.white": "Valge parema poolega", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON> parema poolega", "block.minecraft.banner.half_vertical_right.black": "<PERSON>a vasaku poolega", "block.minecraft.banner.half_vertical_right.blue": "Sinise vasaku poolega", "block.minecraft.banner.half_vertical_right.brown": "P<PERSON><PERSON> vasaku poolega", "block.minecraft.banner.half_vertical_right.cyan": "Tsüaansinise vasaku poolega", "block.minecraft.banner.half_vertical_right.gray": "<PERSON>i vasaku poolega", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>a", "block.minecraft.banner.half_vertical_right.light_blue": "Helesinise vasaku poolega", "block.minecraft.banner.half_vertical_right.light_gray": "Helehalli vasaku poolega", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> vasa<PERSON>a", "block.minecraft.banner.half_vertical_right.magenta": "Magentapunase vasaku poolega", "block.minecraft.banner.half_vertical_right.orange": "Oranži vasaku poolega", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON> vasaku poolega", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> vasaku poolega", "block.minecraft.banner.half_vertical_right.red": "P<PERSON>se vasaku poolega", "block.minecraft.banner.half_vertical_right.white": "Valge vasaku poolega", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON><PERSON> vasaku poolega", "block.minecraft.banner.mojang.black": "<PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Tsüaan<PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "Magentap<PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.white": "<PERSON><PERSON> as<PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.black": "<PERSON><PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "Tsüaansinise kärsaga", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Helesinise kärsaga", "block.minecraft.banner.piglin.light_gray": "Helehalli kärsaga", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.magenta": "Magentapunase k<PERSON>ga", "block.minecraft.banner.piglin.orange": "Oranži k<PERSON>ga", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.white": "Valge k<PERSON>ga", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "Musta rombiga", "block.minecraft.banner.rhombus.blue": "Sinise rombiga", "block.minecraft.banner.rhombus.brown": "Pruuni rombiga", "block.minecraft.banner.rhombus.cyan": "Tsüaansinise rombiga", "block.minecraft.banner.rhombus.gray": "Halli rombiga", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.rhombus.light_blue": "Helesinise rombiga", "block.minecraft.banner.rhombus.light_gray": "Helehalli rombiga", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.rhombus.magenta": "Magentapunase rombiga", "block.minecraft.banner.rhombus.orange": "Oranži rombiga", "block.minecraft.banner.rhombus.pink": "<PERSON>oosa rombiga", "block.minecraft.banner.rhombus.purple": "Lilla rombiga", "block.minecraft.banner.rhombus.red": "Punase rombiga", "block.minecraft.banner.rhombus.white": "Valge rombiga", "block.minecraft.banner.rhombus.yellow": "Kollase rombiga", "block.minecraft.banner.skull.black": "Musta pealuuembleemiga", "block.minecraft.banner.skull.blue": "Sinise pealuuembleemiga", "block.minecraft.banner.skull.brown": "Pruuni pealuuembleemiga", "block.minecraft.banner.skull.cyan": "Tsüaansinise pealuuembleemiga", "block.minecraft.banner.skull.gray": "Halli pealuuembleemiga", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "Helesinise pealuuembleemiga", "block.minecraft.banner.skull.light_gray": "Helehalli pealuuembleemiga", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "Magentapunase pealuuembleemiga", "block.minecraft.banner.skull.orange": "Oranži pealuuembleemiga", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.skull.purple": "Lilla p<PERSON>uuembleem<PERSON>", "block.minecraft.banner.skull.red": "Punase pealuuembleemiga", "block.minecraft.banner.skull.white": "Valge pealuuembleemiga", "block.minecraft.banner.skull.yellow": "Ko<PERSON>e pealuuembleemiga", "block.minecraft.banner.small_stripes.black": "Mustade vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.blue": "Siniste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.brown": "Pruunide vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.cyan": "Tsüaansiniste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.gray": "Hallide vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.green": "Roheliste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.light_blue": "Helesiniste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.light_gray": "Helehallide vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.lime": "Laimiroheliste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.magenta": "Magentapunaste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.orange": "Oranžide vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.pink": "R<PERSON>ade vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.purple": "Lillade vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.red": "Punaste vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.white": "Valgete vertikaalsete viirgudega", "block.minecraft.banner.small_stripes.yellow": "Kollaste vertikaalsete viirgudega", "block.minecraft.banner.square_bottom_left.black": "Musta ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.blue": "Sinise ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.brown": "Pruuni ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.cyan": "Tsüaansinise ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.gray": "Halli ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON><PERSON> ru<PERSON>uga all paremas nurgas", "block.minecraft.banner.square_bottom_left.light_blue": "Helesinise ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.light_gray": "Helehalli ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.magenta": "Magentapunase ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.orange": "Oranži ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON>a ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.purple": "Lilla ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.red": "Punase ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.white": "Valge ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_left.yellow": "Kollase ruuduga all paremas nurgas", "block.minecraft.banner.square_bottom_right.black": "Musta ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.blue": "Sinise ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.brown": "Pruuni ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.cyan": "Tsüaansinise ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.gray": "Halli ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON> ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.light_blue": "Helesinise ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.light_gray": "Helehalli ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.magenta": "Magentapunase ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.orange": "Oranži ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON>a ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.purple": "Lilla ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.red": "Punase ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.white": "Valge ruuduga all vasakus nurgas", "block.minecraft.banner.square_bottom_right.yellow": "Kollase ruuduga all vasakus nurgas", "block.minecraft.banner.square_top_left.black": "Musta ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.blue": "Sinise ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.brown": "Pruuni ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.cyan": "Tsüaansinise ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.gray": "<PERSON>i ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> paremas nurgas", "block.minecraft.banner.square_top_left.light_blue": "Helesinise ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.light_gray": "Helehalli ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ru<PERSON>al paremas nurgas", "block.minecraft.banner.square_top_left.magenta": "Magentapunase ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.orange": "Oranži ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON><PERSON> ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.purple": "<PERSON><PERSON> ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.red": "Punase ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.white": "Valge ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON><PERSON> ruuduga üleval paremas nurgas", "block.minecraft.banner.square_top_right.black": "Musta ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.blue": "Sinise ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.brown": "Pruuni ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.cyan": "Tsüaansinise ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.gray": "Halli ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON><PERSON> ru<PERSON> vasakus nurgas", "block.minecraft.banner.square_top_right.light_blue": "Helesinise ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.light_gray": "Helehalli ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.magenta": "Magentapunase ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.orange": "Oranži ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON><PERSON> ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.purple": "Lilla ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.red": "Punase ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.white": "Valge ruuduga üleval vasakus nurgas", "block.minecraft.banner.square_top_right.yellow": "Kollase ruuduga üleval vasakus nurgas", "block.minecraft.banner.straight_cross.black": "Musta ristiga", "block.minecraft.banner.straight_cross.blue": "Sinise ristiga", "block.minecraft.banner.straight_cross.brown": "Pruuni ristiga", "block.minecraft.banner.straight_cross.cyan": "Tsüaansinise ristiga", "block.minecraft.banner.straight_cross.gray": "Halli ristiga", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Helesinise ristiga", "block.minecraft.banner.straight_cross.light_gray": "Helehalli ristiga", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.straight_cross.magenta": "Magentapunase ristiga", "block.minecraft.banner.straight_cross.orange": "Oranži ristiga", "block.minecraft.banner.straight_cross.pink": "Roosa ristiga", "block.minecraft.banner.straight_cross.purple": "Lilla rist<PERSON>", "block.minecraft.banner.straight_cross.red": "Punase ristiga", "block.minecraft.banner.straight_cross.white": "Valge ristiga", "block.minecraft.banner.straight_cross.yellow": "Kollase ristiga", "block.minecraft.banner.stripe_bottom.black": "Musta joonega all", "block.minecraft.banner.stripe_bottom.blue": "Sinise joonega all", "block.minecraft.banner.stripe_bottom.brown": "P<PERSON><PERSON> joonega all", "block.minecraft.banner.stripe_bottom.cyan": "Tsüaansinise joonega all", "block.minecraft.banner.stripe_bottom.gray": "<PERSON>i joonega all", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON> joon<PERSON>a all", "block.minecraft.banner.stripe_bottom.light_blue": "Helesinise joonega all", "block.minecraft.banner.stripe_bottom.light_gray": "Helehalli joonega all", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> joon<PERSON>a all", "block.minecraft.banner.stripe_bottom.magenta": "Magentapunase joonega all", "block.minecraft.banner.stripe_bottom.orange": "Oranži joonega all", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON>a joonega all", "block.minecraft.banner.stripe_bottom.purple": "<PERSON>la joonega all", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON>se joonega all", "block.minecraft.banner.stripe_bottom.white": "Valge joonega all", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON> joonega all", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON> vertika<PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.blue": "<PERSON>ise vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON> vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.cyan": "Tsüaansinise vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON> vertika<PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> joonega keskel", "block.minecraft.banner.stripe_center.light_blue": "Helesinise vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.light_gray": "Helehalli vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.magenta": "Magentapunase vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.orange": "Oranži vertikaalse joonega keskel", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON> vert<PERSON><PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON> vert<PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON> vert<PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.white": "<PERSON>ge vertika<PERSON>e joonega keskel", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON> vert<PERSON>e joonega keskel", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON> p<PERSON> loogega", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON> p<PERSON> loogega", "block.minecraft.banner.stripe_downleft.brown": "Pru<PERSON> pöö<PERSON>ud loogega", "block.minecraft.banner.stripe_downleft.cyan": "Tsüaansinise pöö<PERSON>ud loogega", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON> p<PERSON> loogega", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON><PERSON> loog<PERSON>a", "block.minecraft.banner.stripe_downleft.light_blue": "Helesinise pööratud loogega", "block.minecraft.banner.stripe_downleft.light_gray": "Helehalli pööratud loogega", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> loog<PERSON>a", "block.minecraft.banner.stripe_downleft.magenta": "Magentapunase pööratud loogega", "block.minecraft.banner.stripe_downleft.orange": "Oranži pööratud loogega", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON> p<PERSON> loogega", "block.minecraft.banner.stripe_downleft.white": "Valge pööratud loogega", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON><PERSON> p<PERSON> loogega", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.cyan": "Tsüaansinise loogega", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>a", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON>inise loogega", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>a", "block.minecraft.banner.stripe_downright.magenta": "Magentapunase loogega", "block.minecraft.banner.stripe_downright.orange": "Oranži loogega", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON> loogega", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON> loog<PERSON>a", "block.minecraft.banner.stripe_downright.white": "Valge loogega", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON><PERSON> loog<PERSON>a", "block.minecraft.banner.stripe_left.black": "Musta flangiga paremal", "block.minecraft.banner.stripe_left.blue": "Sinise flangiga paremal", "block.minecraft.banner.stripe_left.brown": "Pruuni flangiga paremal", "block.minecraft.banner.stripe_left.cyan": "Tsüaansinise flangiga paremal", "block.minecraft.banner.stripe_left.gray": "Halli flangiga paremal", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON>se flangiga paremal", "block.minecraft.banner.stripe_left.light_blue": "Helesinise flangiga paremal", "block.minecraft.banner.stripe_left.light_gray": "Helehalli flangiga paremal", "block.minecraft.banner.stripe_left.lime": "Laimirohelise flangiga paremal", "block.minecraft.banner.stripe_left.magenta": "Magentapunase flangiga paremal", "block.minecraft.banner.stripe_left.orange": "Oranži flangiga paremal", "block.minecraft.banner.stripe_left.pink": "Roosa flangiga paremal", "block.minecraft.banner.stripe_left.purple": "Lilla flangiga paremal", "block.minecraft.banner.stripe_left.red": "Punase flangiga paremal", "block.minecraft.banner.stripe_left.white": "Valge flangiga paremal", "block.minecraft.banner.stripe_left.yellow": "Kollase flangiga paremal", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON> horisont<PERSON>se joonega keskel", "block.minecraft.banner.stripe_middle.blue": "Sinise horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.brown": "Pruuni horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.cyan": "Tsüaansinise horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.gray": "<PERSON>i horisont<PERSON>se joonega keskel", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> joonega keskel", "block.minecraft.banner.stripe_middle.light_blue": "Helesinise horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.light_gray": "Helehalli horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> horisont<PERSON>se joonega keskel", "block.minecraft.banner.stripe_middle.magenta": "Magentapunase horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.orange": "Oranži ho<PERSON>ontaalse joonega keskel", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> ho<PERSON> joonega keskel", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON> ho<PERSON> joonega keskel", "block.minecraft.banner.stripe_middle.red": "Punase horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.white": "Valge horisontaalse joonega keskel", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> horisontaalse joonega keskel", "block.minecraft.banner.stripe_right.black": "Musta flangiga vasakul", "block.minecraft.banner.stripe_right.blue": "Sinise flangiga vasakul", "block.minecraft.banner.stripe_right.brown": "Pruuni flangiga vasakul", "block.minecraft.banner.stripe_right.cyan": "Tsüaansinise flangiga vasakul", "block.minecraft.banner.stripe_right.gray": "Halli flangiga vasakul", "block.minecraft.banner.stripe_right.green": "Rohelise flangiga vasakul", "block.minecraft.banner.stripe_right.light_blue": "Helesinise flangiga vasakul", "block.minecraft.banner.stripe_right.light_gray": "Helehalli flangiga vasakul", "block.minecraft.banner.stripe_right.lime": "Laimirohelise flangiga vasakul", "block.minecraft.banner.stripe_right.magenta": "Magentapunase flangiga vasakul", "block.minecraft.banner.stripe_right.orange": "Oranži flangiga vasakul", "block.minecraft.banner.stripe_right.pink": "Roosa flangiga vasakul", "block.minecraft.banner.stripe_right.purple": "Lilla flangiga vasakul", "block.minecraft.banner.stripe_right.red": "Punase flangiga vasakul", "block.minecraft.banner.stripe_right.white": "Valge flangiga vasakul", "block.minecraft.banner.stripe_right.yellow": "Kollase flangiga vasakul", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON> joon<PERSON>a <PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON>ise joonega <PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.banner.stripe_top.cyan": "Tsüaansinise joon<PERSON>a <PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON> joon<PERSON>a <PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Helesinise joon<PERSON>a <PERSON>", "block.minecraft.banner.stripe_top.light_gray": "Helehalli joon<PERSON>a <PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.banner.stripe_top.magenta": "Magentapunase joon<PERSON>a <PERSON>", "block.minecraft.banner.stripe_top.orange": "Oranži j<PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON> j<PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON> j<PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> joon<PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON> joonega <PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Tsüaansinise k<PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ga", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "Magentapunase k<PERSON>", "block.minecraft.banner.triangle_bottom.orange": "Or<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON> p<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON> p<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON><PERSON> p<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.cyan": "Tsüaansinise pöö<PERSON> k<PERSON>ga", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON> p<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.light_blue": "Helesinise pööratud k<PERSON>", "block.minecraft.banner.triangle_top.light_gray": "Helehalli pöö<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.magenta": "Magentapunase pöö<PERSON>ud k<PERSON>", "block.minecraft.banner.triangle_top.orange": "Oranži pöö<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON> p<PERSON> k<PERSON>", "block.minecraft.banner.triangle_top.white": "Valge pööratud k<PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON><PERSON> p<PERSON> k<PERSON>", "block.minecraft.banner.triangles_bottom.black": "Mustade alumiste sakkidega", "block.minecraft.banner.triangles_bottom.blue": "Siniste alumiste sakkidega", "block.minecraft.banner.triangles_bottom.brown": "Pruunide alumiste sakkidega", "block.minecraft.banner.triangles_bottom.cyan": "Tsüaansiniste alumiste sakkidega", "block.minecraft.banner.triangles_bottom.gray": "Hallide alumiste sakkidega", "block.minecraft.banner.triangles_bottom.green": "Roheliste alumiste sakkidega", "block.minecraft.banner.triangles_bottom.light_blue": "Helesiniste alumiste sakkidega", "block.minecraft.banner.triangles_bottom.light_gray": "Helehallide alumiste sakkidega", "block.minecraft.banner.triangles_bottom.lime": "Laimiroheliste alumiste sakkidega", "block.minecraft.banner.triangles_bottom.magenta": "Magentapunase alumiste sakkidega", "block.minecraft.banner.triangles_bottom.orange": "Oranžide alumiste sakkidega", "block.minecraft.banner.triangles_bottom.pink": "Roosade alumiste sakkidega", "block.minecraft.banner.triangles_bottom.purple": "Lillade alumiste sakkidega", "block.minecraft.banner.triangles_bottom.red": "Punaste alumiste sakkidega", "block.minecraft.banner.triangles_bottom.white": "Valgete alumiste sakkidega", "block.minecraft.banner.triangles_bottom.yellow": "Kollaste alumiste sakkidega", "block.minecraft.banner.triangles_top.black": "Mustade ülemiste sakkidega", "block.minecraft.banner.triangles_top.blue": "Siniste ülemiste sakkidega", "block.minecraft.banner.triangles_top.brown": "Pruunide ülemiste sakkidega", "block.minecraft.banner.triangles_top.cyan": "Tsüaansiniste ülemiste sakkidega", "block.minecraft.banner.triangles_top.gray": "Hallide ülemiste sakkidega", "block.minecraft.banner.triangles_top.green": "R<PERSON><PERSON>ste ülemiste sakkidega", "block.minecraft.banner.triangles_top.light_blue": "Helesiniste ülemiste sakkidega", "block.minecraft.banner.triangles_top.light_gray": "Helehallide ülemiste sakkidega", "block.minecraft.banner.triangles_top.lime": "Laimiroheliste ülemiste sakkidega", "block.minecraft.banner.triangles_top.magenta": "Magentapunase ülemiste sakkidega", "block.minecraft.banner.triangles_top.orange": "Oranžide ülemiste sakkidega", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON>kidega", "block.minecraft.banner.triangles_top.purple": "Lillade ülemiste sakkidega", "block.minecraft.banner.triangles_top.red": "Punaste ülemiste sakkidega", "block.minecraft.banner.triangles_top.white": "Valgete ülemiste sakkidega", "block.minecraft.banner.triangles_top.yellow": "Kollaste ülemiste sakkidega", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "Te<PERSON>e mõju", "block.minecraft.bed.no_sleep": "Sa saad magada ainult öösiti või äikesetormide ajal", "block.minecraft.bed.not_safe": "Sa ei saa hetkel puhata, lähed<PERSON> on koletisi", "block.minecraft.bed.obstructed": "See voodi on tõ<PERSON><PERSON>ud", "block.minecraft.bed.occupied": "See voodi on hõivatud", "block.minecraft.bed.too_far_away": "Sa ei saa hetkel puhata, voodi on liiga kaugel", "block.minecraft.bedrock": "Aluskivi", "block.minecraft.bee_nest": "Mesilaspesa", "block.minecraft.beehive": "Mesitar<PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON> til<PERSON> vars", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_door": "Kasest uks", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON> aed", "block.minecraft.birch_fence_gate": "Kasest aiavärav", "block.minecraft.birch_hanging_sign": "Kasest rippuv silt", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_planks": "Kaselauad", "block.minecraft.birch_pressure_plate": "Kasest surveplaat", "block.minecraft.birch_sapling": "Kaseistik", "block.minecraft.birch_sign": "Kasest silt", "block.minecraft.birch_slab": "Kaseplaat", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON>t seinal rippuv silt", "block.minecraft.birch_wall_sign": "Kasest seinasilt", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "<PERSON> plakat", "block.minecraft.black_bed": "<PERSON> voodi", "block.minecraft.black_candle": "<PERSON>", "block.minecraft.black_candle_cake": "<PERSON>a k<PERSON>ü<PERSON>a kook", "block.minecraft.black_carpet": "Must vaip", "block.minecraft.black_concrete": "Must betoon", "block.minecraft.black_concrete_powder": "Must betoonipulber", "block.minecraft.black_glazed_terracotta": "Must glasuuritud terrakota", "block.minecraft.black_shulker_box": "<PERSON>", "block.minecraft.black_stained_glass": "Musta värviga klaas", "block.minecraft.black_stained_glass_pane": "Musta värviga k<PERSON>paneel", "block.minecraft.black_terracotta": "Must terrakota", "block.minecraft.black_wool": "Must vill", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Mustkiviplaat", "block.minecraft.blackstone_stairs": "Must<PERSON>vitrepp", "block.minecraft.blackstone_wall": "Mustkivimü<PERSON>r", "block.minecraft.blast_furnace": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "<PERSON><PERSON> p<PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON> voodi", "block.minecraft.blue_candle": "<PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON>ise k<PERSON>a kook", "block.minecraft.blue_carpet": "<PERSON><PERSON> vaip", "block.minecraft.blue_concrete": "Sinine betoon", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON> betoonipulber", "block.minecraft.blue_glazed_terracotta": "Sinine glas<PERSON>uritud terrakota", "block.minecraft.blue_ice": "<PERSON><PERSON> j<PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON>e", "block.minecraft.blue_shulker_box": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass": "Sinise värviga klaas", "block.minecraft.blue_stained_glass_pane": "Sinise värviga k<PERSON>aspaneel", "block.minecraft.blue_terracotta": "Sinine terrakota", "block.minecraft.blue_wool": "Sinine vill", "block.minecraft.bone_block": "Kondiplokk", "block.minecraft.bookshelf": "Raamaturiiul", "block.minecraft.brain_coral": "Ajukorall", "block.minecraft.brain_coral_block": "Ajukoralli plokk", "block.minecraft.brain_coral_fan": "Ajukoralli <PERSON>", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Tellisplaat", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Tellised", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON> plakat", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON> voodi", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON><PERSON> k<PERSON>ünlaga kook", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> vaip", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON> betoon", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON> betoon<PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON> glas<PERSON>tud terrakota", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON> seen", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Pruuni värviga klaas", "block.minecraft.brown_stained_glass_pane": "Pruuni värviga klaaspaneel", "block.minecraft.brown_terracotta": "<PERSON><PERSON><PERSON> terrakota", "block.minecraft.brown_wool": "<PERSON><PERSON><PERSON> vill", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Mullkoralli plokk", "block.minecraft.bubble_coral_fan": "Mullkoralli lehvik", "block.minecraft.bubble_coral_wall_fan": "Mullkor<PERSON><PERSON>", "block.minecraft.budding_amethyst": "Tärkav ametüst", "block.minecraft.bush": "Põõ<PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Ka<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Kalibreeritud sculk-sensor", "block.minecraft.campfire": "Lõkketuli", "block.minecraft.candle": "K<PERSON><PERSON>nal", "block.minecraft.candle_cake": "Küünlaga kook", "block.minecraft.carrots": "Porgandid", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cauldron": "Pada", "block.minecraft.cave_air": "Koopaõhk", "block.minecraft.cave_vines": "Koopaväädid", "block.minecraft.cave_vines_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> taim", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "Ketistatud käsuplokk", "block.minecraft.cherry_button": "Kirsipuidust nupp", "block.minecraft.cherry_door": "Kirsipuidust uks", "block.minecraft.cherry_fence": "Kirsipuidust aed", "block.minecraft.cherry_fence_gate": "Kirsipuidust aiavärav", "block.minecraft.cherry_hanging_sign": "Kirsipuidust rippuv silt", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON><PERSON><PERSON> palk", "block.minecraft.cherry_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON> lauad", "block.minecraft.cherry_pressure_plate": "Kirsipuidust surveplaat", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON> istik", "block.minecraft.cherry_sign": "Kirsipuidust silt", "block.minecraft.cherry_slab": "Kirsipuidust plaat", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> trepp", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON> luuk", "block.minecraft.cherry_wall_hanging_sign": "Kirsipuidust seinal rippuv silt", "block.minecraft.cherry_wall_sign": "Kirsipuidust seinasilt", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "Täkkega alasi", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> vask", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON><PERSON> nether-tell<PERSON>", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON><PERSON><PERSON> lih<PERSON>tud <PERSON>", "block.minecraft.chiseled_quartz_block": "Tahu<PERSON>d kvartsplokk", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON><PERSON> punane liiva<PERSON>vi", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON><PERSON> vai<PERSON>lised", "block.minecraft.chiseled_sandstone": "Tahutud liivakivi", "block.minecraft.chiseled_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON> tuff", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON><PERSON> tufitellised", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Kooritaim", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON>", "block.minecraft.coal_block": "Söeplokk", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON><PERSON> muld", "block.minecraft.cobbled_deepslate": "Sillutatud sügavkilt", "block.minecraft.cobbled_deepslate_slab": "Sillutatud sügavkildast plaat", "block.minecraft.cobbled_deepslate_stairs": "Sillutatud sügavkildast trepp", "block.minecraft.cobbled_deepslate_wall": "Sillutatud sügavkildast müür", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Munakiviplaat", "block.minecraft.cobblestone_stairs": "Munakivitrepp", "block.minecraft.cobblestone_wall": "Munakivimüür", "block.minecraft.cobweb": "Ämblikuvõrk", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Käsuplokk", "block.minecraft.comparator": "Redstone'i komparaator", "block.minecraft.composter": "Ko<PERSON>ster", "block.minecraft.conduit": "Siire", "block.minecraft.copper_block": "Vaseplokk", "block.minecraft.copper_bulb": "Vaskpirn", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "Vaskvõre", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Vaskluuk", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>tellised", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> nether-tellised", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> lih<PERSON>d <PERSON> tellised", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.crafter": "Meisterdaja", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creaking_heart": "Kriiksuv süda", "block.minecraft.creeper_head": "Creeperi pea", "block.minecraft.creeper_wall_head": "Creeperi seina<PERSON>a", "block.minecraft.crimson_button": "Karm<PERSON>n<PERSON>nan<PERSON> nupp", "block.minecraft.crimson_door": "Karmiinpunane uks", "block.minecraft.crimson_fence": "Karmiinpunane aed", "block.minecraft.crimson_fence_gate": "Karmiinpunane aiavärav", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seen", "block.minecraft.crimson_hanging_sign": "Karmiinpunane rippuv silt", "block.minecraft.crimson_hyphae": "Karmiinpunased hüü<PERSON>d", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nets<PERSON>", "block.minecraft.crimson_planks": "Karmiinpu<PERSON><PERSON> lauad", "block.minecraft.crimson_pressure_plate": "Karmiinpunane surveplaat", "block.minecraft.crimson_roots": "Karmiinpu<PERSON><PERSON> juured", "block.minecraft.crimson_sign": "Karmiinpunane silt", "block.minecraft.crimson_slab": "Karmiinpunane plaat", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trepp", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vars", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luuk", "block.minecraft.crimson_wall_hanging_sign": "Karmiinpunane seinal rippuv silt", "block.minecraft.crimson_wall_sign": "Karm<PERSON>n<PERSON>nane se<PERSON>", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.cut_copper": "Lõigatud vask", "block.minecraft.cut_copper_slab": "Lõigatud vasest plaat", "block.minecraft.cut_copper_stairs": "Lõigatud vasest trepp", "block.minecraft.cut_red_sandstone": "Lõigatud punane liivakivi", "block.minecraft.cut_red_sandstone_slab": "Lõigatud punasest liivakivist plaat", "block.minecraft.cut_sandstone": "Lõigatud liivakivi", "block.minecraft.cut_sandstone_slab": "Lõigatud liivakivist plaat", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> voodi", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "Tsüaansinise küünlaga kook", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vaip", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> betoon", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>tud terrakota", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "Tsüaansinise värviga klaas", "block.minecraft.cyan_stained_glass_pane": "Tsüaansinise värviga klaaspaneel", "block.minecraft.cyan_terracotta": "Ts<PERSON>aansinine terrakota", "block.minecraft.cyan_wool": "Tsüaansinine vill", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Tumedast tammest nupp", "block.minecraft.dark_oak_door": "Tumedast tammest uks", "block.minecraft.dark_oak_fence": "Tumedast tammest aed", "block.minecraft.dark_oak_fence_gate": "Tumedast tammest aiavärav", "block.minecraft.dark_oak_hanging_sign": "Tumedast tammest rippuv silt", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON>a tamme lehed", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON> tamme palk", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON> tamme lauad", "block.minecraft.dark_oak_pressure_plate": "Tumedast tammest surveplaat", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON> tamme istik", "block.minecraft.dark_oak_sign": "Tumedast tammest silt", "block.minecraft.dark_oak_slab": "<PERSON>meda tamme plaat", "block.minecraft.dark_oak_stairs": "Tumedast tammest trepp", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON> tammest luuk", "block.minecraft.dark_oak_wall_hanging_sign": "Tumedast tammest seinal rippuv silt", "block.minecraft.dark_oak_wall_sign": "Tumedast tammest seinasilt", "block.minecraft.dark_oak_wood": "<PERSON><PERSON>a tamme puit", "block.minecraft.dark_prismarine": "<PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Tumedast prismariinist plaat", "block.minecraft.dark_prismarine_stairs": "Tumedast prismariinist trepp", "block.minecraft.daylight_detector": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and<PERSON>", "block.minecraft.dead_brain_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON> plokk", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON> m<PERSON> plokk", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON> tuli<PERSON>alli plokk", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON> tuli<PERSON> le<PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> tuli<PERSON>", "block.minecraft.dead_horn_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON> sa<PERSON>all<PERSON> plokk", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON> plokk", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.decorated_pot": "Kaunistatud pott", "block.minecraft.deepslate": "Sügavkilt", "block.minecraft.deepslate_brick_slab": "Sügavkilda tellisplaat", "block.minecraft.deepslate_brick_stairs": "<PERSON>ügav<PERSON><PERSON> tellistrepp", "block.minecraft.deepslate_brick_wall": "Sügavkildatellistest müür", "block.minecraft.deepslate_bricks": "Sügavkildatellised", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "Sügavkilda redstone-maak", "block.minecraft.deepslate_tile_slab": "Sügavkildakahlist plaat", "block.minecraft.deepslate_tile_stairs": "Sügavkildakahlist trepp", "block.minecraft.deepslate_tile_wall": "Sügavkildakahlist müür", "block.minecraft.deepslate_tiles": "Sügavkildakahlid", "block.minecraft.detector_rail": "Andurrööpad", "block.minecraft.diamond_block": "Teemantplokk", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "Dioriit", "block.minecraft.diorite_slab": "Dioriitplaat", "block.minecraft.diorite_stairs": "Dioriit-trepp", "block.minecraft.diorite_wall": "Dioriitmü<PERSON><PERSON>", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "Mullarada", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "Draa<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON> pea", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON> se<PERSON><PERSON>a", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>t", "block.minecraft.dried_kelp_block": "Kuivatatud vetikaplokk", "block.minecraft.dripstone_block": "Tilkekivi plokk", "block.minecraft.dropper": "<PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Smaragdiplokk", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_gateway": "<PERSON><PERSON> lä<PERSON>", "block.minecraft.end_portal": "<PERSON><PERSON> portaal", "block.minecraft.end_portal_frame": "<PERSON><PERSON> portaali raam", "block.minecraft.end_rod": "Endivarras", "block.minecraft.end_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Endikivi tellisplaat", "block.minecraft.end_stone_brick_stairs": "<PERSON><PERSON><PERSON> tell<PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_wall": "Endikivi tellistest müür", "block.minecraft.end_stone_bricks": "<PERSON><PERSON><PERSON> tell<PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> tahutud vask", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON>ud vask", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>pi<PERSON>", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.exposed_copper_grate": "Pleekinud vaskvõre", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.exposed_cut_copper": "Pleekinud lõigatud vask", "block.minecraft.exposed_cut_copper_slab": "Pleekinud lõigatud vasest plaat", "block.minecraft.exposed_cut_copper_stairs": "Pleekinud lõigatud vasest trepp", "block.minecraft.farmland": "Põllumaa", "block.minecraft.fern": "Sõnajalg", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "Tulikorall", "block.minecraft.fire_coral_block": "Tulikoralli plokk", "block.minecraft.fire_coral_fan": "Tulik<PERSON>li <PERSON>", "block.minecraft.fire_coral_wall_fan": "Tulik<PERSON><PERSON>", "block.minecraft.firefly_bush": "Jaanimardikapõõsas", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea": "Õitsev asalea", "block.minecraft.flowering_azalea_leaves": "Õitseva asalea lehed", "block.minecraft.frogspawn": "Konnakudu", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON><PERSON><PERSON> jää", "block.minecraft.furnace": "<PERSON>i", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glow_lichen": "Hõõgsamblik", "block.minecraft.glowstone": "Hõõgkivi", "block.minecraft.gold_block": "Kullaplokk", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite": "Graniit", "block.minecraft.granite_slab": "Graniitplaat", "block.minecraft.granite_stairs": "Graniit-trepp", "block.minecraft.granite_wall": "Graniitmüür", "block.minecraft.grass": "<PERSON><PERSON>", "block.minecraft.grass_block": "Muruplokk", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "Hall plakat", "block.minecraft.gray_bed": "<PERSON> voodi", "block.minecraft.gray_candle": "<PERSON>üünal", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> k<PERSON>a kook", "block.minecraft.gray_carpet": "Hall vaip", "block.minecraft.gray_concrete": "Hall betoon", "block.minecraft.gray_concrete_powder": "Hall betoonipulber", "block.minecraft.gray_glazed_terracotta": "Hall glasuuritud terrakota", "block.minecraft.gray_shulker_box": "<PERSON>", "block.minecraft.gray_stained_glass": "Halli värviga klaas", "block.minecraft.gray_stained_glass_pane": "Halli värviga k<PERSON>eel", "block.minecraft.gray_terracotta": "Hall terrakota", "block.minecraft.gray_wool": "Hall vill", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON> voodi", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON><PERSON><PERSON> kook", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>p", "block.minecraft.green_concrete": "R<PERSON>eline betoon", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakota", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> k<PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON> terra<PERSON>a", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON> vill", "block.minecraft.grindstone": "Käiakivi", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON> ju<PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "Raske tuum", "block.minecraft.heavy_weighted_pressure_plate": "Raskekaaluline surveplaat", "block.minecraft.honey_block": "Meeplokk", "block.minecraft.honeycomb_block": "Meekärjeplokk", "block.minecraft.hopper": "Püü<PERSON><PERSON>", "block.minecraft.horn_coral": "Sa<PERSON>vkorall", "block.minecraft.horn_coral_block": "Sarvkoralli plokk", "block.minecraft.horn_coral_fan": "Sarvkoralli lehvik", "block.minecraft.horn_coral_wall_fan": "Sarvkoralli <PERSON>", "block.minecraft.ice": "Jää", "block.minecraft.infested_chiseled_stone_bricks": "Parasi<PERSON><PERSON> ta<PERSON> k<PERSON>ed", "block.minecraft.infested_cobblestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_cracked_stone_bricks": "Parasiitsed pragunenud kiv<PERSON>ed", "block.minecraft.infested_deepslate": "Parasiitne sügavkilt", "block.minecraft.infested_mossy_stone_bricks": "Parasi<PERSON><PERSON> sa<PERSON><PERSON><PERSON><PERSON> kivitellised", "block.minecraft.infested_stone": "Parasiitne kivi", "block.minecraft.infested_stone_bricks": "Parasi<PERSON>ed kivite<PERSON>ed", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Rauaplokk", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jack_o_lantern": "K<PERSON>rvitsalatern", "block.minecraft.jigsaw": "Pusleplokk", "block.minecraft.jukebox": "Plaadimängija", "block.minecraft.jungle_button": "Džunglipuidust nupp", "block.minecraft.jungle_door": "Džunglipuidust uks", "block.minecraft.jungle_fence": "Džunglipuidust aed", "block.minecraft.jungle_fence_gate": "Džunglipuidust aiavärav", "block.minecraft.jungle_hanging_sign": "Džunglipuidust rippuv silt", "block.minecraft.jungle_leaves": "Džunglipu<PERSON> lehed", "block.minecraft.jungle_log": "Džunglipuu palk", "block.minecraft.jungle_planks": "Džunglipuidust lauad", "block.minecraft.jungle_pressure_plate": "Džunglipuidust surveplaat", "block.minecraft.jungle_sapling": "Džunglipuu istik", "block.minecraft.jungle_sign": "Džunglipuidust silt", "block.minecraft.jungle_slab": "Džunglipuidust plaat", "block.minecraft.jungle_stairs": "Džunglipuidust trepp", "block.minecraft.jungle_trapdoor": "Džunglipuidust luuk", "block.minecraft.jungle_wall_hanging_sign": "Džunglipuidust seinal rippuv silt", "block.minecraft.jungle_wall_sign": "Džunglipuidust seinasilt", "block.minecraft.jungle_wood": "Džunglipuit", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ladder": "<PERSON><PERSON>", "block.minecraft.lantern": "<PERSON>n", "block.minecraft.lapis_block": "Lasuriidiplokk", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "Suur ametüstipung", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON>", "block.minecraft.lava": "Laava", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "Kõnepult", "block.minecraft.lever": "<PERSON>", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON><PERSON> plakat", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON><PERSON> voodi", "block.minecraft.light_blue_candle": "He<PERSON><PERSON>ne <PERSON>", "block.minecraft.light_blue_candle_cake": "Helesinise küünlaga kook", "block.minecraft.light_blue_carpet": "He<PERSON>inine vaip", "block.minecraft.light_blue_concrete": "He<PERSON><PERSON>ne betoon", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> betoonipulber", "block.minecraft.light_blue_glazed_terracotta": "Helesinine glasuuritud terrakota", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass": "Helesinise värviga klaas", "block.minecraft.light_blue_stained_glass_pane": "Helesinise värviga klaaspaneel", "block.minecraft.light_blue_terracotta": "Helesinine terrakota", "block.minecraft.light_blue_wool": "Helesinine vill", "block.minecraft.light_gray_banner": "Helehall plakat", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON> voodi", "block.minecraft.light_gray_candle": "Helehall küünal", "block.minecraft.light_gray_candle_cake": "Helehalli küünlaga kook", "block.minecraft.light_gray_carpet": "Helehall vaip", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON> betoon", "block.minecraft.light_gray_concrete_powder": "Helehall betoonipulber", "block.minecraft.light_gray_glazed_terracotta": "Helehall glasuuritud terrakota", "block.minecraft.light_gray_shulker_box": "Helehall šulkerikast", "block.minecraft.light_gray_stained_glass": "Helehalli värviga klaas", "block.minecraft.light_gray_stained_glass_pane": "Helehalli värviga klaaspaneel", "block.minecraft.light_gray_terracotta": "Helehall terrakota", "block.minecraft.light_gray_wool": "Helehall vill", "block.minecraft.light_weighted_pressure_plate": "Kergekaaluline surveplaat", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lilac": "<PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "Vesiroosile<PERSON>", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> voodi", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> kook", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>p", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON> betoon", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> terrakota", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON> värvi<PERSON> k<PERSON>as", "block.minecraft.lime_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON> vä<PERSON><PERSON><PERSON> k<PERSON>paneel", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> terra<PERSON>", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> vill", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Kangasteljed", "block.minecraft.magenta_banner": "Magentap<PERSON><PERSON> plakat", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> voodi", "block.minecraft.magenta_candle": "Magentapunane <PERSON>", "block.minecraft.magenta_candle_cake": "Magentapunase küünlaga kook", "block.minecraft.magenta_carpet": "Magent<PERSON><PERSON><PERSON> vaip", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON> betoon", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_glazed_terracotta": "Magentapunane g<PERSON>tud terrakota", "block.minecraft.magenta_shulker_box": "<PERSON>gent<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "Magentapunase värviga klaas", "block.minecraft.magenta_stained_glass_pane": "Magentapunase värviga klaaspaneel", "block.minecraft.magenta_terracotta": "Magentapunane terrakota", "block.minecraft.magenta_wool": "Magentapunane vill", "block.minecraft.magma_block": "Magmaplokk", "block.minecraft.mangrove_button": "Man<PERSON>ovinup<PERSON>", "block.minecraft.mangrove_door": "Mangroovist uks", "block.minecraft.mangrove_fence": "Mangroovist aed", "block.minecraft.mangrove_fence_gate": "Mangroovist aiav<PERSON>rav", "block.minecraft.mangrove_hanging_sign": "Mangroovist rippuv silt", "block.minecraft.mangrove_leaves": "Mangroovilehed", "block.minecraft.mangrove_log": "Mangroovipalk", "block.minecraft.mangrove_planks": "Mangroovilauad", "block.minecraft.mangrove_pressure_plate": "Mangroovist surveplaat", "block.minecraft.mangrove_propagule": "Mangroovi sigipung", "block.minecraft.mangrove_roots": "Mangroovijuured", "block.minecraft.mangrove_sign": "Mangroovist silt", "block.minecraft.mangrove_slab": "Mangrooviplaat", "block.minecraft.mangrove_stairs": "Mangroovitrepp", "block.minecraft.mangrove_trapdoor": "Mangrooviluuk", "block.minecraft.mangrove_wall_hanging_sign": "Mangroovist seinal rippuv silt", "block.minecraft.mangrove_wall_sign": "Mangroovist se<PERSON><PERSON>", "block.minecraft.mangrove_wood": "Mangroovipuit", "block.minecraft.medium_amethyst_bud": "Keskmine ametüstipung", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Samblaplokk", "block.minecraft.moss_carpet": "Samblavaip", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone_slab": "Sammal<PERSON><PERSON>d muna<PERSON>p<PERSON>at", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_slab": "Sammal<PERSON><PERSON>d k<PERSON>", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> m<PERSON>ür", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.moving_piston": "Liikuv kolb", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Mudatellisplaat", "block.minecraft.mud_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON> müür", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Nether-tellistest aed", "block.minecraft.nether_brick_slab": "Nether-tellisplaat", "block.minecraft.nether_brick_stairs": "Nether-tell<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_wall": "Nether-tellistest müür", "block.minecraft.nether_bricks": "Nether-tellised", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_portal": "Netheri portaal", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_sprouts": "Nether-võrsed", "block.minecraft.nether_wart": "Nethertüügas", "block.minecraft.nether_wart_block": "Nethertüüka plokk", "block.minecraft.netherite_block": "Netheriitplokk", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Noodiplokk", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_door": "Tammest uks", "block.minecraft.oak_fence": "Tammest aed", "block.minecraft.oak_fence_gate": "Tammest aiavärav", "block.minecraft.oak_hanging_sign": "Tammest rippuv silt", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Tammepalk", "block.minecraft.oak_planks": "Tammelauad", "block.minecraft.oak_pressure_plate": "Tammest surveplaat", "block.minecraft.oak_sapling": "Tammeistik", "block.minecraft.oak_sign": "Tammest silt", "block.minecraft.oak_slab": "Tammeplaat", "block.minecraft.oak_stairs": "Tammetrepp", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_hanging_sign": "Tammest seinal rippuv silt", "block.minecraft.oak_wall_sign": "Tammest seinasilt", "block.minecraft.oak_wood": "Tammepuit", "block.minecraft.observer": "Vaatleja", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON> konnavalgus", "block.minecraft.ominous_banner": "Kurjakuulutav plakat", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_candle_cake": "Oranži küünlaga kook", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON> vai<PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON> betoon", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON>ž g<PERSON>uuritud terrakota", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass": "Oranži värviga klaas", "block.minecraft.orange_stained_glass_pane": "Oranži värviga klaaspaneel", "block.minecraft.orange_terracotta": "Oranž terrakota", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> tulp", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON> vill", "block.minecraft.oxeye_daisy": "<PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Oksüdeerunud tahutud vask", "block.minecraft.oxidized_copper": "Oksüdeerunud vask", "block.minecraft.oxidized_copper_bulb": "Oksüdeerunud vaskpirn", "block.minecraft.oxidized_copper_door": "Oksüdeerunud vaskuks", "block.minecraft.oxidized_copper_grate": "Oksüdeerunud vaskvõre", "block.minecraft.oxidized_copper_trapdoor": "Oksüdeerunud vaskluuk", "block.minecraft.oxidized_cut_copper": "Oksüdeerunud lõigatud vask", "block.minecraft.oxidized_cut_copper_slab": "Oksüdeerunud lõigatud vasest plaat", "block.minecraft.oxidized_cut_copper_stairs": "Oksüdeerunud lõigatud vasest trepp", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON>a", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON> rippuv sammal", "block.minecraft.pale_moss_block": "Kahvatu sambla plokk", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON><PERSON> sambla vaip", "block.minecraft.pale_oak_button": "Kah<PERSON><PERSON> tammest nupp", "block.minecraft.pale_oak_door": "Kahvatust tammest uks", "block.minecraft.pale_oak_fence": "<PERSON><PERSON><PERSON><PERSON> tammest aed", "block.minecraft.pale_oak_fence_gate": "Kahvatust tammest aiavärav", "block.minecraft.pale_oak_hanging_sign": "Kahvatust tammest rippuv silt", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON> tamme lehed", "block.minecraft.pale_oak_log": "Ka<PERSON>vat<PERSON> tammest palk", "block.minecraft.pale_oak_planks": "<PERSON><PERSON><PERSON><PERSON> tamme lauad", "block.minecraft.pale_oak_pressure_plate": "Kahvatust tammest surveplaat", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON> tamme istik", "block.minecraft.pale_oak_sign": "Kahvatust tammest silt", "block.minecraft.pale_oak_slab": "<PERSON><PERSON><PERSON>u tamme plaat", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON><PERSON> tammest trepp", "block.minecraft.pale_oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON> tammest luuk", "block.minecraft.pale_oak_wall_hanging_sign": "Kahvatust tammest seinal rippuv silt", "block.minecraft.pale_oak_wall_sign": "Kahvatust tammest seinasilt", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON>u tamme puit", "block.minecraft.pearlescent_froglight": "Pärlmu<PERSON><PERSON> konna<PERSON>", "block.minecraft.peony": "Pojeng", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON><PERSON><PERSON> tamme<PERSON>", "block.minecraft.piglin_head": "<PERSON><PERSON> pea", "block.minecraft.piglin_wall_head": "<PERSON><PERSON> se<PERSON>a", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON> voodi", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON><PERSON> k<PERSON>a kook", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON> v<PERSON>p", "block.minecraft.pink_concrete": "<PERSON><PERSON>a betoon", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON>d terrakota", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> v<PERSON> k<PERSON>as", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.pink_terracotta": "Roosa terrakota", "block.minecraft.pink_tulip": "<PERSON><PERSON>a tulp", "block.minecraft.pink_wool": "<PERSON><PERSON>a vill", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "Kolvipea", "block.minecraft.pitcher_crop": "<PERSON><PERSON><PERSON><PERSON> saak", "block.minecraft.pitcher_plant": "Kanntaim", "block.minecraft.player_head": "Mängija pea", "block.minecraft.player_head.named": "Mängija %s pea", "block.minecraft.player_wall_head": "Mängija seina<PERSON>a", "block.minecraft.podzol": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "Lihvitud andesiit", "block.minecraft.polished_andesite_slab": "Lihvitud andesiidist plaat", "block.minecraft.polished_andesite_stairs": "Lihvitud andesiidist trepp", "block.minecraft.polished_basalt": "Lihvitud basalt", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> tellisplaat", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> tellist<PERSON>pp", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> must<PERSON>vi tellistest müür", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> tellised", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> nupp", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON> surveplaat", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> plaat", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> trepp", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.polished_deepslate": "Lihvitud sügavkilt", "block.minecraft.polished_deepslate_slab": "Lihvitud sügavkildast plaat", "block.minecraft.polished_deepslate_stairs": "Lihvitud sügavkildast trepp", "block.minecraft.polished_deepslate_wall": "Lihvitud sügavkildast müür", "block.minecraft.polished_diorite": "Lihvi<PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Lihvitud dioriidist plaat", "block.minecraft.polished_diorite_stairs": "Lihvitud dioriidist trepp", "block.minecraft.polished_granite": "Lih<PERSON><PERSON><PERSON> grani<PERSON>", "block.minecraft.polished_granite_slab": "Lihvitud graniidist plaat", "block.minecraft.polished_granite_stairs": "<PERSON><PERSON><PERSON><PERSON>d graniidist trepp", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuff", "block.minecraft.polished_tuff_slab": "Lihvitud tufip<PERSON>at", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> tufit<PERSON>pp", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Akaatsiaistik potis", "block.minecraft.potted_allium": "<PERSON><PERSON> potis", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON><PERSON> potis", "block.minecraft.potted_azure_bluet": "Sinine inglisilm potis", "block.minecraft.potted_bamboo": "<PERSON><PERSON><PERSON> potis", "block.minecraft.potted_birch_sapling": "Kaseistik potis", "block.minecraft.potted_blue_orchid": "<PERSON>ine orhidee potis", "block.minecraft.potted_brown_mushroom": "<PERSON><PERSON><PERSON> seen potis", "block.minecraft.potted_cactus": "<PERSON><PERSON><PERSON> potis", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON> istik potis", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON><PERSON> silma<PERSON> potis", "block.minecraft.potted_cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON> potis", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seen potis", "block.minecraft.potted_crimson_roots": "<PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON> juured potis", "block.minecraft.potted_dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_dark_oak_sapling": "<PERSON><PERSON><PERSON> tamme istik potis", "block.minecraft.potted_dead_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_fern": "Sõnajalg potis", "block.minecraft.potted_flowering_azalea_bush": "Õitsev asalea potis", "block.minecraft.potted_jungle_sapling": "Džunglipuu istik potis", "block.minecraft.potted_lily_of_the_valley": "<PERSON><PERSON><PERSON> potis", "block.minecraft.potted_mangrove_propagule": "Mangroovi sigipung potis", "block.minecraft.potted_oak_sapling": "Tammeistik potis", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON><PERSON> si<PERSON> potis", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON><PERSON> tulp potis", "block.minecraft.potted_oxeye_daisy": "<PERSON><PERSON> h<PERSON> potis", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON> tamme istik potis", "block.minecraft.potted_pink_tulip": "<PERSON><PERSON>a tulp potis", "block.minecraft.potted_poppy": "<PERSON><PERSON> potis", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON><PERSON> seen potis", "block.minecraft.potted_red_tulip": "<PERSON><PERSON><PERSON> tulp potis", "block.minecraft.potted_spruce_sapling": "Kuuseistik potis", "block.minecraft.potted_torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_warped_fungus": "<PERSON><PERSON><PERSON> seen potis", "block.minecraft.potted_warped_roots": "<PERSON><PERSON><PERSON><PERSON> ju<PERSON> potis", "block.minecraft.potted_white_tulip": "Valge tulp potis", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON><PERSON> potis", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada", "block.minecraft.powered_rail": "Jõurööpad", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON><PERSON> tell<PERSON>", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> tell<PERSON>", "block.minecraft.prismarine_bricks": "Prismariinitellised", "block.minecraft.prismarine_slab": "Prismariiniplaat", "block.minecraft.prismarine_stairs": "Prismariinitrepp", "block.minecraft.prismarine_wall": "Prismariinimüür", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "K<PERSON>rvitsavars", "block.minecraft.purple_banner": "<PERSON><PERSON>", "block.minecraft.purple_bed": "<PERSON><PERSON> v<PERSON>i", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> k<PERSON>a kook", "block.minecraft.purple_carpet": "<PERSON><PERSON> v<PERSON>p", "block.minecraft.purple_concrete": "<PERSON><PERSON> betoon", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON> bet<PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> g<PERSON>tud terrakota", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Lilla värviga klaas", "block.minecraft.purple_stained_glass_pane": "Lilla värviga k<PERSON>aspaneel", "block.minecraft.purple_terracotta": "<PERSON>la terrakota", "block.minecraft.purple_wool": "<PERSON><PERSON> vill", "block.minecraft.purpur_block": "Purpurplokk", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_slab": "Purpurplaat", "block.minecraft.purpur_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_block": "Kvartsplokk", "block.minecraft.quartz_bricks": "Kvar<PERSON>tellised", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "Kvartsplaat", "block.minecraft.quartz_stairs": "Kvartstrepp", "block.minecraft.rail": "Rööpad", "block.minecraft.raw_copper_block": "Toorvase plokk", "block.minecraft.raw_gold_block": "Toorkulla plokk", "block.minecraft.raw_iron_block": "Toorraua plokk", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON> voodi", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON><PERSON> k<PERSON>a kook", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> vaip", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON> betoon", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON> terrakota", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON><PERSON> nether-tell<PERSON>p<PERSON><PERSON>", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON> nether-tell<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_wall": "<PERSON>unastest nether-tellistest müür", "block.minecraft.red_nether_bricks": "Punased nether-tellised", "block.minecraft.red_sand": "Punane liiv", "block.minecraft.red_sandstone": "Punane lii<PERSON>kivi", "block.minecraft.red_sandstone_slab": "Punane liivakiviplaat", "block.minecraft.red_sandstone_stairs": "Punane lii<PERSON>trepp", "block.minecraft.red_sandstone_wall": "Punasest liivakivist müür", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "Punase värviga k<PERSON>as", "block.minecraft.red_stained_glass_pane": "Punase värviga k<PERSON>aspaneel", "block.minecraft.red_terracotta": "<PERSON><PERSON>ne terrakota", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> tulp", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON> vill", "block.minecraft.redstone_block": "Redstone-plokk", "block.minecraft.redstone_lamp": "Redstone-lamp", "block.minecraft.redstone_ore": "Redstone-maak", "block.minecraft.redstone_torch": "Redstone-tõrvik", "block.minecraft.redstone_wall_torch": "Redstone-seinatõrvik", "block.minecraft.redstone_wire": "Redstone-juhe", "block.minecraft.reinforced_deepslate": "Tugevdatud sügavkilt", "block.minecraft.repeater": "Redstone'i järgur", "block.minecraft.repeating_command_block": "Kordav käsuplokk", "block.minecraft.resin_block": "Vaiguplokk", "block.minecraft.resin_brick_slab": "Vaigutellisplaat", "block.minecraft.resin_brick_stairs": "Vai<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_brick_wall": "Vaigutellistest müür", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_clump": "Vaiguklomp", "block.minecraft.respawn_anchor": "Taastekkeankur", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON> muld", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "Liiv", "block.minecraft.sandstone": "Liiva<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Liivakiviplaat", "block.minecraft.sandstone_stairs": "Liivakivitrepp", "block.minecraft.sandstone_wall": "Liivakivimüür", "block.minecraft.scaffolding": "Telling", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-katalüsaator", "block.minecraft.sculk_sensor": "Sculk-sensor", "block.minecraft.sculk_shrieker": "Sculk-kisendaja", "block.minecraft.sculk_vein": "Sculk-veen", "block.minecraft.sea_lantern": "Merelatern", "block.minecraft.sea_pickle": "Merikurk", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Taastekkekoht määratud", "block.minecraft.short_dry_grass": "<PERSON><PERSON> kuiv muru", "block.minecraft.short_grass": "<PERSON><PERSON> muru", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Šulker<PERSON><PERSON>", "block.minecraft.skeleton_skull": "<PERSON><PERSON><PERSON> pealuu", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON><PERSON>", "block.minecraft.slime_block": "Limaplokk", "block.minecraft.small_amethyst_bud": "Väike ametüstipung", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Sile basalt", "block.minecraft.smooth_quartz": "Siledast kvartsist plokk", "block.minecraft.smooth_quartz_slab": "Siledast kvartsist plaat", "block.minecraft.smooth_quartz_stairs": "Siledast kvartsist trepp", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> punane liivakivi", "block.minecraft.smooth_red_sandstone_slab": "Siledast punasest liivakivist plaat", "block.minecraft.smooth_red_sandstone_stairs": "Siledast punasest liivakivist trepp", "block.minecraft.smooth_sandstone": "Sile liivakivi", "block.minecraft.smooth_sandstone_slab": "Siledast liivakivist plaat", "block.minecraft.smooth_sandstone_stairs": "Siledast liivakivist trepp", "block.minecraft.smooth_stone": "<PERSON><PERSON> kivi", "block.minecraft.smooth_stone_slab": "Siledast kivist plaat", "block.minecraft.sniffer_egg": "Nuuskijamuna", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Lumeplokk", "block.minecraft.soul_campfire": "Hinge lõkketuli", "block.minecraft.soul_fire": "Hinge<PERSON><PERSON>", "block.minecraft.soul_lantern": "Hingelatern", "block.minecraft.soul_sand": "Hingeliiv", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "Hingetõrvik", "block.minecraft.soul_wall_torch": "Hinge seinatõrvik", "block.minecraft.spawn.not_valid": "Sul pole kodust voodit ega laetud taastekkeankurit või see oli takistatud", "block.minecraft.spawner": "Koletiste tekitaja", "block.minecraft.spawner.desc1": "<PERSON><PERSON><PERSON> peal sünn<PERSON><PERSON>:", "block.minecraft.spawner.desc2": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sponge": "Käsn", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_door": "Kuusest uks", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON> aed", "block.minecraft.spruce_fence_gate": "Kuusest aiavärav", "block.minecraft.spruce_hanging_sign": "Kuusest rippuv silt", "block.minecraft.spruce_leaves": "Kuuselehed", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "Kuusest surveplaat", "block.minecraft.spruce_sapling": "Kuuseistik", "block.minecraft.spruce_sign": "<PERSON><PERSON>t silt", "block.minecraft.spruce_slab": "Kuuseplaat", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON>t seinal rippuv silt", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON> se<PERSON>ilt", "block.minecraft.spruce_wood": "Kuusepuit", "block.minecraft.sticky_piston": "Kleepuv kolb", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Kivitellisplaat", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_wall": "Kivitellistest müür", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON> surveplaat", "block.minecraft.stone_slab": "Kiviplaat", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "Kivilõikur", "block.minecraft.stripped_acacia_log": "Kooritud akaatsiapalk", "block.minecraft.stripped_acacia_wood": "Kooritud akaatsiapuit", "block.minecraft.stripped_bamboo_block": "Kooritud bambuse plokk", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> palk", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.stripped_crimson_hyphae": "Kooritud karmiinpunased hüüfid", "block.minecraft.stripped_crimson_stem": "Kooritud karmii<PERSON> vars", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON>d tumeda tamme palk", "block.minecraft.stripped_dark_oak_wood": "Ko<PERSON>tud tumeda tamme puit", "block.minecraft.stripped_jungle_log": "Kooritud <PERSON> palk", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>", "block.minecraft.stripped_oak_log": "Ko<PERSON><PERSON>d tammepalk", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON>d tamme<PERSON>it", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON><PERSON> kahvatu tamme palk", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON> kahvatu tamme puit", "block.minecraft.stripped_spruce_log": "Kooritud k<PERSON>", "block.minecraft.stripped_spruce_wood": "Kooritud ku<PERSON>", "block.minecraft.stripped_warped_hyphae": "Kooritud k<PERSON>akad h<PERSON>", "block.minecraft.stripped_warped_stem": "Kooritud k<PERSON> vars", "block.minecraft.structure_block": "Struktuuriplokk", "block.minecraft.structure_void": "Struk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sugar_cane": "Suhkruroog", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.suspicious_sand": "Kahtlane liiv", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON> ma<PERSON><PERSON>", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON> kuiv muru", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON> muru", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terrakota", "block.minecraft.test_block": "Testplokk", "block.minecraft.test_instance_block": "Testeksemplari plokk", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.tnt": "Dünamiit", "block.minecraft.tnt.disabled": "Dünamiidiplahvatused on keelatud", "block.minecraft.torch": "Tõrvik", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.trapped_chest": "Püüniskirst", "block.minecraft.trial_spawner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire": "Traadilõks", "block.minecraft.tripwire_hook": "Traadilõksu konks", "block.minecraft.tube_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_block": "Torukoralli plokk", "block.minecraft.tube_coral_fan": "Toruk<PERSON><PERSON>", "block.minecraft.tube_coral_wall_fan": "<PERSON>uk<PERSON><PERSON>", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tufitellisplaat", "block.minecraft.tuff_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_wall": "Tufitellistest müür", "block.minecraft.tuff_bricks": "Tufitellised", "block.minecraft.tuff_slab": "Tufiplaat", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "Väänduvad väädid", "block.minecraft.twisting_vines_plant": "Väänduvate väätide taim", "block.minecraft.vault": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "Haljendav konnavalgus", "block.minecraft.vine": "Väädid", "block.minecraft.void_air": "Tühjuseõhk", "block.minecraft.wall_torch": "Seinatõrvik", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON> nupp", "block.minecraft.warped_door": "Kentsakas uks", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON> aed", "block.minecraft.warped_fence_gate": "Kent<PERSON><PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.warped_hanging_sign": "Kentsakas rippuv silt", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "Kentsakas surveplaat", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON> ju<PERSON>", "block.minecraft.warped_sign": "Kentsakas silt", "block.minecraft.warped_slab": "Kentsakas plaat", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON> trepp", "block.minecraft.warped_stem": "Kent<PERSON><PERSON> vars", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON> luuk", "block.minecraft.warped_wall_hanging_sign": "Kentsakas seinal rippuv silt", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> se<PERSON>", "block.minecraft.warped_wart_block": "Kentsaka tüüka plokk", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "Veepada", "block.minecraft.waxed_chiseled_copper": "Vahatatud tahutud vask", "block.minecraft.waxed_copper_block": "Vahatatud vaseplokk", "block.minecraft.waxed_copper_bulb": "Vahatatud vaskpirn", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_grate": "Vahatatud vaskvõre", "block.minecraft.waxed_copper_trapdoor": "Vahata<PERSON><PERSON>", "block.minecraft.waxed_cut_copper": "Vahatatud lõigatud vask", "block.minecraft.waxed_cut_copper_slab": "Vahatatud lõigatud vasest plaat", "block.minecraft.waxed_cut_copper_stairs": "Vahatatud lõigatud vasest trepp", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> pleekinud tahutud vask", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> vask", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> v<PERSON>", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> v<PERSON>", "block.minecraft.waxed_exposed_copper_grate": "Vah<PERSON><PERSON><PERSON> p<PERSON> vaskvõre", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> v<PERSON>", "block.minecraft.waxed_exposed_cut_copper": "Vahatatud pleekinud lõigatud vask", "block.minecraft.waxed_exposed_cut_copper_slab": "Vahatatud pleekinud lõigatud vasest plaat", "block.minecraft.waxed_exposed_cut_copper_stairs": "Vahatatud pleekinud lõigatud vasest trepp", "block.minecraft.waxed_oxidized_chiseled_copper": "Vahatatud ok<PERSON>üdeerunud tahutud vask", "block.minecraft.waxed_oxidized_copper": "Vahatatud oksüdeerunud vask", "block.minecraft.waxed_oxidized_copper_bulb": "Vahatatud oksüdeerunud vaskpirn", "block.minecraft.waxed_oxidized_copper_door": "Vahatatud oksüdeerunud vaskuks", "block.minecraft.waxed_oxidized_copper_grate": "Vahatatud oksüdeerunud vaskvõre", "block.minecraft.waxed_oxidized_copper_trapdoor": "Vahatatud oksüdeerunud vaskluuk", "block.minecraft.waxed_oxidized_cut_copper": "Vahatatud oksüdeerunud lõigatud vask", "block.minecraft.waxed_oxidized_cut_copper_slab": "Vahatatud oksüdeerunud lõigatud vasest plaat", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Vahatatud oksüdeerunud lõigatud vasest trepp", "block.minecraft.waxed_weathered_chiseled_copper": "Vahata<PERSON>d kulunud tahutud vask", "block.minecraft.waxed_weathered_copper": "Vah<PERSON><PERSON>d kulunud vask", "block.minecraft.waxed_weathered_copper_bulb": "Vahatatud kulunud v<PERSON>", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> kulunud v<PERSON>", "block.minecraft.waxed_weathered_copper_grate": "Vahatatud kulunud vaskvõre", "block.minecraft.waxed_weathered_copper_trapdoor": "Vahata<PERSON>d kulunud v<PERSON>", "block.minecraft.waxed_weathered_cut_copper": "Vahatatud kulunud lõigatud vask", "block.minecraft.waxed_weathered_cut_copper_slab": "Vahatatud kulunud lõigatud vasest plaat", "block.minecraft.waxed_weathered_cut_copper_stairs": "Vahatatud kulunud lõigatud vasest trepp", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> vask", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON> vask", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON> vaskvõre", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_cut_copper": "Kulunud lõigatud vask", "block.minecraft.weathered_cut_copper_slab": "Kulunud lõigatud vasest plaat", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON>d lõigatud vasest trepp", "block.minecraft.weeping_vines": "Nutvad väädid", "block.minecraft.weeping_vines_plant": "Nutvate väätide taim", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.wheat": "<PERSON><PERSON><PERSON>", "block.minecraft.white_banner": "Valge plakat", "block.minecraft.white_bed": "<PERSON><PERSON> voodi", "block.minecraft.white_candle": "Valge küünal", "block.minecraft.white_candle_cake": "Valge k<PERSON>ünlaga kook", "block.minecraft.white_carpet": "Valge vaip", "block.minecraft.white_concrete": "Valge betoon", "block.minecraft.white_concrete_powder": "Valge betoonipulber", "block.minecraft.white_glazed_terracotta": "Valge glasuuritud terrakota", "block.minecraft.white_shulker_box": "Valge šulkerika<PERSON>", "block.minecraft.white_stained_glass": "Valge värviga klaas", "block.minecraft.white_stained_glass_pane": "Valge värviga klaaspaneel", "block.minecraft.white_terracotta": "Valge terrakota", "block.minecraft.white_tulip": "Valge tulp", "block.minecraft.white_wool": "Valge vill", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "Witheriroos", "block.minecraft.wither_skeleton_skull": "Wither-lu<PERSON><PERSON> pealuu", "block.minecraft.wither_skeleton_wall_skull": "Wither-lu<PERSON><PERSON> se<PERSON>u", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON> vood<PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON><PERSON> k<PERSON>a kook", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON> v<PERSON>p", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON> betoon", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON> terrakota", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "Kollase värviga klaas", "block.minecraft.yellow_stained_glass_pane": "Kollase värviga k<PERSON>aspaneel", "block.minecraft.yellow_terracotta": "Kollane terrakota", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> vill", "block.minecraft.zombie_head": "Zombi pea", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON> se<PERSON>a", "book.byAuthor": "autorilt %1$s", "book.edit.title": "<PERSON><PERSON><PERSON>", "book.editTitle": "<PERSON><PERSON><PERSON> raamatu p<PERSON>:", "book.finalizeButton": "Allkirjasta ja sule", "book.finalizeWarning": "NB! Allkirjastatud raamatut ei saa hiljem muuta.", "book.generation.0": "Originaalne", "book.generation.1": "<PERSON><PERSON><PERSON> koopia", "book.generation.2": "<PERSON><PERSON><PERSON> koopia", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* <PERSON><PERSON><PERSON><PERSON> raamat<PERSON> *", "book.pageIndicator": "Leht %1$s/%2$s", "book.page_button.next": "<PERSON><PERSON><PERSON><PERSON> lehe", "book.page_button.previous": "<PERSON><PERSON><PERSON> lehe", "book.sign.title": "<PERSON><PERSON><PERSON>", "book.sign.titlebox": "Peal<PERSON><PERSON><PERSON>", "book.signButton": "Allkirjasta", "book.view.title": "<PERSON><PERSON><PERSON>", "build.tooHigh": "Ehitamise kõrguspiirang on %s", "chat.cannotSend": "Vestlussõnumit ei saa saata", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klõpsa teleporteerumiseks", "chat.copy": "<PERSON><PERSON><PERSON>", "chat.copy.click": "Klõpsa lõikelauale kopeerimiseks", "chat.deleted_marker": "See vestlussõnum on serveri poolt kustutatud.", "chat.disabled.chain_broken": "Purunenud keti tõttu on vestlus keelatud. <PERSON><PERSON><PERSON> proovi ta<PERSON>.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON><PERSON> avaliku võtme aegumise tõttu on vestlus keelatud. <PERSON><PERSON>n proovi ta<PERSON>.", "chat.disabled.invalid_command_signature": "<PERSON><PERSON><PERSON><PERSON><PERSON> oli ootamatuid või puuduvaid käskluse argumendi signatuure.", "chat.disabled.invalid_signature": "Vestlusel oli kehtetu signatuur. <PERSON><PERSON>n proovi ta<PERSON>.", "chat.disabled.launcher": "Vestlus on Launcheri valiku kaudu keelatud. Sõnumit ei saa saata.", "chat.disabled.missingProfileKey": "<PERSON><PERSON><PERSON> avalik<PERSON> võtme puudumise tõttu on vestlus keelatud. <PERSON><PERSON>n proovi ta<PERSON>.", "chat.disabled.options": "Vestlus on mängu valikutes keelatud.", "chat.disabled.out_of_order_chat": "Vestlus võeti vastu vales järjekorras. Kas su süsteemikell muutus?", "chat.disabled.profile": "Vestlus pole konto seadetes lubatud. Rohkema teabe saamiseks vajuta uuesti \"%s\".", "chat.disabled.profile.moreInfo": "Vestlus pole konto seadetes lubatud. Sõnumeid ei saa saata ega vaadata.", "chat.editBox": "vestlus", "chat.filtered": "<PERSON><PERSON> poolt filtreeritud.", "chat.filtered_full": "Server on sinu sõnumi teatud mängijatele peitnud.", "chat.link.confirm": "<PERSON><PERSON> oled kindel, et soovid avada järgnevat veebilehte?", "chat.link.confirmTrusted": "Kas soovid selle lingi avada või kopeerida lõikelauale?", "chat.link.open": "<PERSON>", "chat.link.warning": "<PERSON>ra kunagi ava linke inimestelt, keda sa ei usalda!", "chat.queue": "[+%s ootel rida]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sa<PERSON><PERSON> k<PERSON> s<PERSON>.", "chat.tag.modified": "Sõnum serveri poolt muudetud. Originaal:", "chat.tag.not_secure": "Kinnitamata sõnum. Ei saa raporteerida.", "chat.tag.system": "Serveri sõnum. Ei saa raporteerida.", "chat.tag.system_single_player": "<PERSON>i s<PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s täitis väljakutse %s", "chat.type.advancement.goal": "%s saavutas eesmärgi %s", "chat.type.advancement.task": "%s jõ<PERSON>s edasi <PERSON>t %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Saada meeskonnale sõnum", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s ütleb %s", "chat.validation_error": "Vestluse valideerimise viga", "chat_screen.message": "Saadetav sõnum: %s", "chat_screen.title": "Vestlusaken", "chat_screen.usage": "Sisesta sõnum ja vajuta saatmiseks Enter", "chunk.toast.checkLog": "Rohkema teabe saamiseks vaata logi", "chunk.toast.loadFailure": "Koordinatidel %s ei õnnes<PERSON>ud kamakat laadida", "chunk.toast.lowDiskSpace": "<PERSON><PERSON><PERSON><PERSON> on vähe!", "chunk.toast.lowDiskSpace.description": "Maailma salvestamine ei pruugi õnnestuda.", "chunk.toast.saveFailure": "Koordinaatidel %s oleva kamaka salvestamine ebaõnnestus", "clear.failed.multiple": "%s mängijalt ei leitud esemeid", "clear.failed.single": "Mängijalt %s ei leitud esemeid", "color.minecraft.black": "must", "color.minecraft.blue": "sinine", "color.minecraft.brown": "pruun", "color.minecraft.cyan": "ts<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.gray": "hall", "color.minecraft.green": "roheline", "color.minecraft.light_blue": "helesinine", "color.minecraft.light_gray": "he<PERSON><PERSON>", "color.minecraft.lime": "laim<PERSON><PERSON>ine", "color.minecraft.magenta": "magentapunane", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "roosa", "color.minecraft.purple": "lilla", "color.minecraft.red": "punane", "color.minecraft.white": "valge", "color.minecraft.yellow": "kollane", "command.context.here": "<--[SIIN]", "command.context.parse_error": "%s asukohas %s: %s", "command.exception": "Käsklust ei saanud töödelda: %s", "command.expected.separator": "Oodati tühikut ühe argumendi lõpetamiseks, ent leiti lõpuandmeid", "command.failed": "Käskluse käivitamisel ilmnes ootamatu tõrge", "command.forkLimit": "Maksimaalne kontekstide arv (%s) saavutatud", "command.unknown.argument": "Sobimatu argument käsklusele", "command.unknown.command": "Tundmatu või mittetäielik käsklus, vaata alt viga", "commands.advancement.criterionNotFound": "Edasijõudmine %1$s ei sisalda kriteeriumit \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "%3$s mängijale ei saanud anda edasij<PERSON>mise %2$s kriteeriumit \"%1$s\", kuna neil juba on see", "commands.advancement.grant.criterion.to.many.success": "%3$s mäng<PERSON>le on antud edasij<PERSON>mise %2$s kriteerium \"%1$s\"", "commands.advancement.grant.criterion.to.one.failure": "Mängijale %3$s ei saanud anda edasij<PERSON> %2$s kriteeriumit \"%1$s\", kuna tal juba on see", "commands.advancement.grant.criterion.to.one.success": "Mängijale %3$s on antud edasij<PERSON>udmise %2$s kriteerium \"%1$s\"", "commands.advancement.grant.many.to.many.failure": "%2$s mäng<PERSON>le ei saadud anda %1$s edasi<PERSON><PERSON><PERSON><PERSON>, kuna neil on juba kõik edasijõudmised", "commands.advancement.grant.many.to.many.success": "%s edasij<PERSON>udmist antud %s mängijale", "commands.advancement.grant.many.to.one.failure": "Mängijale %2$s ei saadud anda %1$s edasi<PERSON><PERSON><PERSON><PERSON>, kuna tal on juba kõik edasijõudmised", "commands.advancement.grant.many.to.one.success": "%s edasijõud<PERSON> ant<PERSON> mä<PERSON> %s", "commands.advancement.grant.one.to.many.failure": "Edasijõudmist %s ei saanud anda %s m<PERSON><PERSON><PERSON><PERSON>, kuna neil juba on see", "commands.advancement.grant.one.to.many.success": "%2$s mäng<PERSON>le on antud edasij<PERSON>ud<PERSON> %1$s", "commands.advancement.grant.one.to.one.failure": "Edasijõudmist %s ei saanud anda mäng<PERSON> %s, kuna tal on see", "commands.advancement.grant.one.to.one.success": "Mängijale %2$s on antud edasij<PERSON>udmine %1$s", "commands.advancement.revoke.criterion.to.many.failure": "%3$s mängijalt ei saanud eemaldada edasijõudmise %2$s kriteeriumit \"%1$s\", kuna neil pole seda", "commands.advancement.revoke.criterion.to.many.success": "%3$s mängijalt on eemaldatud edasijõudmise %2$s kriteerium \"%1$s\"", "commands.advancement.revoke.criterion.to.one.failure": "Mängijalt %3$s ei saanud eemaldada edasijõudmise %2$s kriteeriumit \"%1$s\", kuna tal pole seda", "commands.advancement.revoke.criterion.to.one.success": "Mängijalt %3$s on eemaldatud edasijõudmise %2$s kriteerium \"%1$s\"", "commands.advancement.revoke.many.to.many.failure": "%2$s mängijalt ei saanud eemaldada %1$s ed<PERSON><PERSON><PERSON><PERSON><PERSON>, kuna neil pole neid", "commands.advancement.revoke.many.to.many.success": "%s edasijõudmist eemaldatud %s mängijalt", "commands.advancement.revoke.many.to.one.failure": "Mängijalt %2$s ei saanud eemaldada %1$s ed<PERSON><PERSON><PERSON><PERSON><PERSON>, kuna tal pole neid", "commands.advancement.revoke.many.to.one.success": "%s edasijõudmist eemaldatud mängijalt %s", "commands.advancement.revoke.one.to.many.failure": "Edasijõudmist %s ei saanud eemaldada %s mäng<PERSON>lt, kuna neil pole seda", "commands.advancement.revoke.one.to.many.success": "Edasijõudmine %s eemaldatud %s mängijalt", "commands.advancement.revoke.one.to.one.failure": "Mängijalt %2$s ei saanud eemaldada edasijõudmist %1$s, kuna tal pole seda", "commands.advancement.revoke.one.to.one.success": "Edasijõudmine %s eemaldatud mängijalt %s", "commands.attribute.base_value.get.success": "Algväärtus atribuudil %s olemile %s on %s", "commands.attribute.base_value.reset.success": "Algväärtus atribuudil %s olemile %s lähtestatud: %s", "commands.attribute.base_value.set.success": "Algväärtus atribuudil %s olemile %s on määratud: %s", "commands.attribute.failed.entity": "%s ei ole sellele käsklusele sobiv olem", "commands.attribute.failed.modifier_already_present": "Modifikaator %s on juba olemas atribuudil %s olemile %s", "commands.attribute.failed.no_attribute": "Olemil %s puudub atribuut %s", "commands.attribute.failed.no_modifier": "Atribuudil %s olemile %s puudub modifikaator %s", "commands.attribute.modifier.add.success": "Modifikaator %s lisatud atribuudile %s olemi %s jaoks", "commands.attribute.modifier.remove.success": "Modifikaator %s eemaldatud atribuudilt %s olemi %s jaoks", "commands.attribute.modifier.value.get.success": "Modifikaatori väärtus %s atribuudil %s olemi %s jaoks on %s", "commands.attribute.value.get.success": "Väärtus atribuudil %s olemile %s on %s", "commands.ban.failed": "Midagi ei muutunud. See mäng<PERSON> on juba blokeeritud", "commands.ban.success": "%s on blokeeritud: %s", "commands.banip.failed": "Midagi ei muutunud. See IP on juba blokeeritud", "commands.banip.info": "See blokeering mõjutab %s mängijat: %s", "commands.banip.invalid": "Sobimatu IP-aadress või tundmatu mängija", "commands.banip.success": "IP %s on blokeeritud: %s", "commands.banlist.entry": "%s blokeeriti %s poolt: %s", "commands.banlist.entry.unknown": "(Teadmata)", "commands.banlist.list": "Blokeeringuid on %s:", "commands.banlist.none": "Blokeeringuid ei ole", "commands.bossbar.create.failed": "Bossiriba IDga \"%s\" on juba olemas", "commands.bossbar.create.success": "Kohandatud bossiriba %s loodud", "commands.bossbar.get.max": "Kohandatud bossiriba %s omab maks<PERSON> %s", "commands.bossbar.get.players.none": "Kohandatud bossiribal %s pole võrgus olev<PERSON> mä<PERSON>", "commands.bossbar.get.players.some": "Kohandatud bossiribal %s on %s v<PERSON><PERSON> o<PERSON>(at) mängija(t): %s", "commands.bossbar.get.value": "Kohandatud bossiriba %s omab väärtust %s", "commands.bossbar.get.visible.hidden": "Kohandatud bossiriba %s on hetkel peidetud", "commands.bossbar.get.visible.visible": "Kohandatud bossiriba %s on hetkel nähtav", "commands.bossbar.list.bars.none": "Ükski kohandatud bossiriba pole aktiivne", "commands.bossbar.list.bars.some": "%s kohandatud <PERSON> on aktiivne/aktiivsed: %s", "commands.bossbar.remove.success": "Kohandatud bossiriba %s eemaldatud", "commands.bossbar.set.color.success": "Kohandatud bossiriba %s värv on muutunud", "commands.bossbar.set.color.unchanged": "Midagi ei muutunud. See on juba bossiriba värv", "commands.bossbar.set.max.success": "Kohandatud bossiriba %s maksimum on muutunud: %s", "commands.bossbar.set.max.unchanged": "Midagi ei muutunud. See on juba bossiriba maksimum", "commands.bossbar.set.name.success": "Kohandatud bossiriba %s on ümber nimetatud", "commands.bossbar.set.name.unchanged": "Midagi ei muutunud. See on juba bossiriba nimi", "commands.bossbar.set.players.success.none": "Kohandatud bossiribal %s pole enam <PERSON>ht<PERSON> mäng<PERSON>t", "commands.bossbar.set.players.success.some": "Kohandatud bossiribal %s on nüüd %s mängija(t): %s", "commands.bossbar.set.players.unchanged": "Midagi ei muutunud. <PERSON> mängijad on juba bossiribal - pole kedagi lisada ega eemaldada", "commands.bossbar.set.style.success": "Kohandatud bossiriba %s stiil on muutunud", "commands.bossbar.set.style.unchanged": "Midagi ei muutunud. See on juba bossiriba stiil", "commands.bossbar.set.value.success": "Kohandatud bossiriba %s väärtus on muutunud: %s", "commands.bossbar.set.value.unchanged": "Midagi ei muutunud. See on juba bossiriba väärtus", "commands.bossbar.set.visibility.unchanged.hidden": "Midagi ei muutunud. <PERSON><PERSON><PERSON> on juba peidetud", "commands.bossbar.set.visibility.unchanged.visible": "Midagi ei muutunud. <PERSON><PERSON><PERSON> on juba nähtav", "commands.bossbar.set.visible.success.hidden": "Kohandatud bossiriba %s on nüüd peidetud", "commands.bossbar.set.visible.success.visible": "Kohandatud bossiriba %s on nüüd nähtav", "commands.bossbar.unknown": "Bossiriba IDga \"%s\" pole olemas", "commands.clear.success.multiple": "%s ese(t) eemaldatud %s mängijalt", "commands.clear.success.single": "%s ese(t) eemaldatud mängijalt %s", "commands.clear.test.multiple": "%2$s mängijalt leiti %1$s sobiv(at) ese(t)", "commands.clear.test.single": "Mängijalt %2$s leiti %1$s sobiv(at) ese(t)", "commands.clone.failed": "Ühtegi plokki ei kloonitud", "commands.clone.overlap": "Allika- ja sihtalad ei tohi kattuda", "commands.clone.success": "%s plokk(i) edukalt kloonitud", "commands.clone.toobig": "Määratud piirkonnas on liiga palju plokke (maksimum %s, määratud %s)", "commands.damage.invulnerable": "Sihtolem on ant<PERSON> ka<PERSON> ha<PERSON>matu", "commands.damage.success": "Olemile %2$s rakendati %1$s kahju", "commands.data.block.get": "%s plokil %s, %s, %s pärast %sx skaalafaktorit on %s", "commands.data.block.invalid": "Sihtplokk ei ole plo<PERSON>olem", "commands.data.block.modified": "%s, %s, %s plokiandmed muudetud", "commands.data.block.query": "%s, %s, %s omab järgnevaid plokiandmeid: %s", "commands.data.entity.get": "%s olemil %s pärast %sx skaalafaktorit on %s", "commands.data.entity.invalid": "<PERSON><PERSON><PERSON><PERSON> and<PERSON><PERSON> muutmine e<PERSON>", "commands.data.entity.modified": "%s o<PERSON><PERSON><PERSON><PERSON> mu<PERSON>d", "commands.data.entity.query": "%s omab j<PERSON>rgne<PERSON><PERSON> olemiandmeid: %s", "commands.data.get.invalid": "%s ei saa hankida, lubatud on ainult arvulised sildid", "commands.data.get.multiple": "See argument võtab vastu ühe NBT-väärtuse", "commands.data.get.unknown": "%s ei saa hankida, silti pole olemas", "commands.data.merge.failed": "Midagi ei muutunud. Valitud atribuudid juba omavad neid väärtusi", "commands.data.modify.expected_list": "<PERSON><PERSON><PERSON>, saadi: %s", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON> obje<PERSON>, saadi: %s", "commands.data.modify.expected_value": "Oodati väärtust, saadi: %s", "commands.data.modify.invalid_index": "Sobimatu nimekirjaindeks: %s", "commands.data.modify.invalid_substring": "Valed alamsõne indeksid: %s kuni %s", "commands.data.storage.get": "%1$s mäluruumis %2$s on %4$s pärast %3$s skaalafaktorit", "commands.data.storage.modified": "Mäluruum %s muudetud", "commands.data.storage.query": "Mäluruum %s sisaldab järgnevat: %s", "commands.datapack.create.already_exists": "Pakk nimega \"%s\" on juba olemas", "commands.datapack.create.invalid_full_name": "<PERSON><PERSON><PERSON><PERSON> uus paki nimi \"%s\"", "commands.datapack.create.invalid_name": "<PERSON>ue paki nimes \"%s\" on sobimatud märgid", "commands.datapack.create.io_failure": "Pak<PERSON> nimega '%s\" ei saa luua, kontrolli logisid", "commands.datapack.create.metadata_encode_failure": "Paki \"%s\" metaandmete kodeerimine ebaõnnestus: %s", "commands.datapack.create.success": "Uus tühi pakk nimega \"%s\" on loodud", "commands.datapack.disable.failed": "Pakk \"%s\" ei ole lubatud!", "commands.datapack.disable.failed.feature": "<PERSON><PERSON> \"%s\" ei saa keelata, kuna see on osa lubatud lipust!", "commands.datapack.enable.failed": "Pakk \"%s\" on juba lubatud!", "commands.datapack.enable.failed.no_flags": "<PERSON><PERSON> \"%s\" ei saa lubada, kuna nõutud lipud pole siin ma<PERSON>mas lubatud: %s!", "commands.datapack.list.available.none": "<PERSON><PERSON><PERSON><PERSON> and<PERSON><PERSON><PERSON><PERSON> pole saadaval", "commands.datapack.list.available.success": "Saadaval on %s andmepakk(i): %s", "commands.datapack.list.enabled.none": "<PERSON><PERSON> andmepakk pole sisse lülitatud", "commands.datapack.list.enabled.success": "%s andmepakk(i) on sisse lülitatud: %s", "commands.datapack.modify.disable": "Andmepaki %s keelamine", "commands.datapack.modify.enable": "Andmepaki %s lubamine", "commands.datapack.unknown": "Tundmatu andmepakk \"%s\"", "commands.debug.alreadyRunning": "Tiksuprofiiler on juba käivitunud", "commands.debug.function.noRecursion": "Funktsiooni seest ei saa j<PERSON>ada", "commands.debug.function.noReturnRun": "Jälitamist ei saa return run-iga kasutada", "commands.debug.function.success.multiple": "%s käsklus(t) %s funktsioonist jälitatud väljundfaili %s", "commands.debug.function.success.single": "%s käsklus(t) funktsioonist \"%s\" jälitatud väljundfaili %s", "commands.debug.function.traceFailed": "Funktsiooni j<PERSON>lit<PERSON> e<PERSON>", "commands.debug.notRunning": "Tiksuprofiiler pole käivitunud", "commands.debug.started": "Tiksuprofileerimine on alustatud", "commands.debug.stopped": "Tiksuprofileerimine lõpetatud pärast %s sekundit ja %s tiksu (%s tiksu sekundis)", "commands.defaultgamemode.success": "Vaikimisi mängurežiim on nüüd %s", "commands.deop.failed": "Midagi ei muutunud. See mängija ei ole operaator", "commands.deop.success": "%s ei ole enam serveri operaator", "commands.dialog.clear.multiple": "%s mängija dialoog tühjendatud", "commands.dialog.clear.single": "%s dialoog tühjendatud", "commands.dialog.show.multiple": "Dialoog kuvatud %s mängijale", "commands.dialog.show.single": "Dialoog kuvatud mängijale %s", "commands.difficulty.failure": "Raskusaste ei muutunud, see on juba %s", "commands.difficulty.query": "Raskusaste on %s", "commands.difficulty.success": "Raskusastmeks on määratud %s", "commands.drop.no_held_items": "Olem ei saa esemeid hoida", "commands.drop.no_loot_table": "Olemil %s pole saagitabelit", "commands.drop.no_loot_table.block": "Plokil %s pole saagitabelit", "commands.drop.success.multiple": "%s eset visati maha", "commands.drop.success.multiple_with_table": "%s eset visati saagitabelist %s maha", "commands.drop.success.single": "%s %s on maha visatud", "commands.drop.success.single_with_table": "%s %s on saagitabelist %s maha visatud", "commands.effect.clear.everything.failed": "Sihtolemil ei ole eemaldatavaid mõjusid", "commands.effect.clear.everything.success.multiple": "Kõik mõjud eemaldatud %s olemilt", "commands.effect.clear.everything.success.single": "Kõik mõjud eemaldatud mängijalt %s", "commands.effect.clear.specific.failed": "Sihtolemil ei ole soovitud mõju", "commands.effect.clear.specific.success.multiple": "Mõju %s eemaldatud %s olemilt", "commands.effect.clear.specific.success.single": "Mõju %s eemaldatud mängijalt %s", "commands.effect.give.failed": "Seda mõju ei saanud rakendada (sihtolem on kas mõjudele immuunne või omab midagi tugevamat)", "commands.effect.give.success.multiple": "Mõju %s rakendatud %s olemile", "commands.effect.give.success.single": "Mõju %s rakendatud olemile %s", "commands.enchant.failed": "Midagi ei muutunud. Sihtolemite<PERSON> kas pole eset kätes või loitsu ei saanud rakendada", "commands.enchant.failed.entity": "%s ei ole sobiv olem selle käskluse jaoks", "commands.enchant.failed.incompatible": "%s ei toeta seda loitsu", "commands.enchant.failed.itemless": "%s ei hoia ühtegi eset", "commands.enchant.failed.level": "%s on kõrgem, kui selle loitsu maksimaalne tase %s", "commands.enchant.success.multiple": "%2$s olemile rakendati loits %1$s", "commands.enchant.success.single": "%2$s esemele rakendati loits %1$s", "commands.execute.blocks.toobig": "Määratud piirkonnas on liiga palju plokke (maksimum %s, määratud %s)", "commands.execute.conditional.fail": "Test ebaõnnestus", "commands.execute.conditional.fail_count": "Test ebaõnnestus, arv: %s", "commands.execute.conditional.pass": "Test läbitud", "commands.execute.conditional.pass_count": "Test läbitud, arv: %s", "commands.execute.function.instantiationFailure": "Funktsiooni %s isendus e<PERSON>: %s", "commands.experience.add.levels.success.multiple": "%s kogemustaset on antud %s mängijale", "commands.experience.add.levels.success.single": "%s kogemustaset antud mäng<PERSON> %s", "commands.experience.add.points.success.multiple": "%s kog<PERSON><PERSON><PERSON><PERSON><PERSON> on antud %s mängijale", "commands.experience.add.points.success.single": "%s kogemuspunkti antud mäng<PERSON> %s", "commands.experience.query.levels": "Mängijal %s on %s kogemustaset", "commands.experience.query.points": "Mängijal %s on %s kogemuspunkti", "commands.experience.set.levels.success.multiple": "Määratud %s kogemustaset %s mängijale", "commands.experience.set.levels.success.single": "Määratud %s kogemustaset mängijal %s", "commands.experience.set.points.invalid": "Kogemuspunkte ei saa määrata maksimumpunktidest rohkem mängija praeguse taseme puhul", "commands.experience.set.points.success.multiple": "Määratud %s kogemuspunkti %s mängijale", "commands.experience.set.points.success.single": "Määratud %s kogemuspunkti mängijal %s", "commands.fill.failed": "Ühtegi plokki ei tä<PERSON>tud", "commands.fill.success": "%s plokk(i) edukalt täidetud", "commands.fill.toobig": "Määratud piirkonnas on liiga palju plokke (maksimum %s, määratud %s)", "commands.fillbiome.success": "Bioomid vahemikus %s, %s, %s kuni %s, %s, %s on määratud", "commands.fillbiome.success.count": "%s bioomikirje(t) määratud vahemikus %s, %s, %s ja %s, %s, %s", "commands.fillbiome.toobig": "Määratud piirkonnas on liiga palju plokke (maksimum %s, määratud %s)", "commands.forceload.added.failure": "Sundlaadimiseks ei määratud ühtegi kamakat", "commands.forceload.added.multiple": "%s kamakat dimensioonis %s - %s kuni %s on määratud sundlaadima", "commands.forceload.added.none": "Sundlaa<PERSON><PERSON> kamakaid ei leitud dimensioonist %s", "commands.forceload.added.single": "Kamakas %s dimensioonis %s on määratud sundlaadima", "commands.forceload.list.multiple": "%s sundlaa<PERSON>ud kamakat leiti dimensioonist %s asuk<PERSON><PERSON>: %s", "commands.forceload.list.single": "Sundlaa<PERSON>ud kamakas leitud dimensioonist %s asukohas: %s", "commands.forceload.query.failure": "Kamakas asuk<PERSON>as %s, dimensioonis %s ei ole mää<PERSON><PERSON> sundl<PERSON>", "commands.forceload.query.success": "Kamakas asukohas %s, dimensioonis %s on määratud sundlaadima", "commands.forceload.removed.all": "Kõik sundlaaditud kamakad dimensioonis %s on sundlaadimisest eemaldatud", "commands.forceload.removed.failure": "Ühtegi kamakat ei eemaldatud sundlaadimisest", "commands.forceload.removed.multiple": "%s kamakat dimensioonis %s - %s kuni %s on sundlaadimisest eemaldatud", "commands.forceload.removed.single": "Kamakas %s dimensioonis %s on sundlaadimisest eemaldatud", "commands.forceload.toobig": "Määratud piirkonnas on liiga palju kamakaid (maksimum %s, määratud %s)", "commands.function.error.argument_not_compound": "Sobimatu argumenditüüp: %s, oodati Compound", "commands.function.error.missing_argument": "Funktsioonis %1$s puudub argument %2$s", "commands.function.error.missing_arguments": "Funktsioonis %s puuduvad argumendid", "commands.function.error.parse": "Makro %s instantsi loomisel: käsklus \"%s\" põhjustas vea: %s", "commands.function.instantiationFailure": "Funktsiooni %s isendus e<PERSON>: %s", "commands.function.result": "Funktsioon %s tagastas %s", "commands.function.scheduled.multiple": "%s funktsiooni käitamine", "commands.function.scheduled.no_functions": "Ühtegi funktsiooni nimega %s ei leitud", "commands.function.scheduled.single": "Funktsiooni %s käitamine", "commands.function.success.multiple": "%s käsklus(t) %s funktsioonist käivitatud", "commands.function.success.multiple.result": "%s funktsiooni käivitatud", "commands.function.success.single": "%s käsklus(t) funktsioonist \"%s\" käivitatud", "commands.function.success.single.result": "Funktsioon \"%2$s\" tagastas %1$s", "commands.gamemode.success.other": "%s mängurežiimiks on seatud %s", "commands.gamemode.success.self": "<PERSON><PERSON> m<PERSON>ngurežiimiks on seatud %s", "commands.gamerule.query": "Mängureegli %s väärtus on hetkel %s", "commands.gamerule.set": "Mängureegli %s väärtuseks on seatud %s", "commands.give.failed.toomanyitems": "Üle %s %s ei saa anda", "commands.give.success.multiple": "%3$s mängijale on antud %1$s %2$s", "commands.give.success.single": "%s %s antud mä<PERSON> %s", "commands.help.failed": "Tundmatu käsklus või puuduvad õ<PERSON>", "commands.item.block.set.success": "<PERSON><PERSON><PERSON> asukohas %s, %s, %s vahetati %s vastu", "commands.item.entity.set.success.multiple": "%s olemi lahter vahetati %s vastu", "commands.item.entity.set.success.single": "Olemi %s lahter vahetati %s vastu", "commands.item.source.no_such_slot": "Lähteolemil/-plokil puudub lahter %s", "commands.item.source.not_a_container": "Lähteasukoht %s, %s, %s ei ole anum", "commands.item.target.no_changed.known_item": "Ükski sihtolem/-plokk ei võta lahtrisse %2$s eset %1$s vastu", "commands.item.target.no_changes": "Ükski sihtolem/-plokk ei võta lahtrisse %s eset vastu", "commands.item.target.no_such_slot": "Sihtolemil/-plokil puudub %s", "commands.item.target.not_a_container": "Sihtasukoht %s, %s, %s ei ole anum", "commands.jfr.dump.failed": "JFR-tulemuse salvestamine ebaõnnestus: %s", "commands.jfr.start.failed": "JFR-profileerimise alustamine e<PERSON>", "commands.jfr.started": "JFR-profileerimine on alustatud", "commands.jfr.stopped": "JFR-profileerimine on lõpetatud ning tulemused on salvestatud: %s", "commands.kick.owner.failed": "Omanikku ei saa LAN-mängus välja visata", "commands.kick.singleplayer.failed": "Võrguta üksikmängu ma<PERSON>mas ei saa välja visata", "commands.kick.success": "%s on välja visatud: %s", "commands.kill.success.multiple": "%s olemit h<PERSON>d", "commands.kill.success.single": "%s on hukatud", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Võrgus on %s/%s mängijat: %s", "commands.locate.biome.not_found": "\"%s\"-tüüpi bioomi mõistlikust kaugusest ei leitud", "commands.locate.biome.success": "Lähim %s on asukohas %s (%s ploki kaugusel)", "commands.locate.poi.not_found": "\"%s\"-t<PERSON><PERSON><PERSON> vaatamisväärsust mõistlikust kaugusest ei leitud", "commands.locate.poi.success": "Lähim %s on asukohas %s (%s ploki kaugusel)", "commands.locate.structure.invalid": "Struktuur tüübiga \"%s\" puudub", "commands.locate.structure.not_found": "\"%s\"-t<PERSON><PERSON><PERSON> struktuuri lähedalt ei leitud", "commands.locate.structure.success": "Lähim %s on asukohas %s (%s ploki kaugusel)", "commands.message.display.incoming": "%s sosistab sulle: %s", "commands.message.display.outgoing": "Sosistad mängijale %s: %s", "commands.op.failed": "Midagi ei muutunud. See mäng<PERSON> on juba operaator", "commands.op.success": "%s on tehtud serveri operaatoriks", "commands.pardon.failed": "Midagi ei muutunud. See mängija ei ole blokeeritud", "commands.pardon.success": "%s blokeering on eemaldatud", "commands.pardonip.failed": "Midagi ei muutunud. See IP ei ole blokeeritud", "commands.pardonip.invalid": "Sobimatu IP-aadress", "commands.pardonip.success": "IP %s blokeering eemaldatud", "commands.particle.failed": "Osake ei olnud kellelegi n<PERSON>htav", "commands.particle.success": "Kuvan osakest %s", "commands.perf.alreadyRunning": "J<PERSON>udlusprofiiler on juba käivitunud", "commands.perf.notRunning": "J<PERSON>udlusprofiiler pole käivitunud", "commands.perf.reportFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> loomi<PERSON> e<PERSON>", "commands.perf.reportSaved": "Silumisraport loodud asukohta %s", "commands.perf.started": "10 sekundi jõudlusprofileerimise protsess alustatud (varakult lõpetamiseks kasuta \"/perf stop\")", "commands.perf.stopped": "Jõudlusprofileerimine lõpetatud pärast %s sekundit ja %s tiksu (%s tiks(u) sekundis)", "commands.place.feature.failed": "<PERSON>nnuse asetamine e<PERSON>", "commands.place.feature.invalid": "<PERSON><PERSON><PERSON>üü<PERSON> \"%s\" puudub", "commands.place.feature.success": "\"%s\" on asetatud asukohta %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON><PERSON> gene<PERSON><PERSON>", "commands.place.jigsaw.invalid": "Mallihulk tüübiga \"%s\" puudub", "commands.place.jigsaw.success": "Pusle genereeritud asukohta %s, %s, %s", "commands.place.structure.failed": "Struktuuri asetamine e<PERSON>", "commands.place.structure.invalid": "Struktuur tüübiga \"%s\" puudub", "commands.place.structure.success": "Struktuur \"%s\" genereeritud asukohta %s, %s, %s", "commands.place.template.failed": "Malli asetamine e<PERSON>", "commands.place.template.invalid": "Mall IDga \"%s\" puudub", "commands.place.template.success": "Mall \"%s\" laaditud asukohta %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON> on liiga kaugel, et olla kuuldav", "commands.playsound.success.multiple": "%2$s mängijale on esitatud heli %1$s", "commands.playsound.success.single": "Mängijale %2$s on esitatud heli %1$s", "commands.publish.alreadyPublished": "Mitmikmäng on juba hostitud pordil %s", "commands.publish.failed": "<PERSON><PERSON><PERSON> mängu ü<PERSON>eadmine e<PERSON>õnnestus", "commands.publish.started": "<PERSON><PERSON><PERSON> m<PERSON> on üles seatud pordil %s", "commands.publish.success": "Mitmikmäng on nüüd hostitud pordil %s", "commands.random.error.range_too_large": "Juhusliku väärtuse vahemik peab olema maksimaalselt 2147483646", "commands.random.error.range_too_small": "Juhusliku väärtuse vahemik peab olema minimaalselt 2", "commands.random.reset.all.success": "%s juhuslik(ku) jada lähtestatud", "commands.random.reset.success": "Juhuslik jada %s lähtestatud", "commands.random.roll": "%s veeretas arvu %s (vahemikus %s ja %s)", "commands.random.sample.success": "Juhuslik väärtus: %s", "commands.recipe.give.failed": "<PERSON>us<PERSON> retsepte ei <PERSON>", "commands.recipe.give.success.multiple": "%2$s mängijale on lahti lukustatud %1$s retsept(i)", "commands.recipe.give.success.single": "Mängijale %2$s on lahti lukustatud %1$s retsept(i)", "commands.recipe.take.failed": "Ühtegi retsepti ei saanud unustada", "commands.recipe.take.success.multiple": "%2$s mängijalt on võetud %1$s retsept(i)", "commands.recipe.take.success.single": "Mängijalt %2$s on võetud %1$s retsept(i)", "commands.reload.failure": "Uuestilaadimine <PERSON>, j<PERSON><PERSON> and<PERSON>eg<PERSON>", "commands.reload.success": "<PERSON><PERSON><PERSON>!", "commands.ride.already_riding": "%s juba sõidab %s peal", "commands.ride.dismount.success": "%s lõpetas %s peal sõitmise", "commands.ride.mount.failure.cant_ride_players": "Mängijate seljas ei saa sõita", "commands.ride.mount.failure.generic": "%s ei saanud %s peal sõita", "commands.ride.mount.failure.loop": "Olem ei saa ratsutada enda ega ühegi temal ratsutava olemi peal", "commands.ride.mount.failure.wrong_dimension": "Teises dimensioonis olemiga ei saa ratsutada", "commands.ride.mount.success": "%s alustas %s peal sõitmist", "commands.ride.not_riding": "%s ei sõida ühelgi sõidukil", "commands.rotate.success": "%s pö<PERSON>ratud", "commands.save.alreadyOff": "Salvestamine on juba välja lülitatud", "commands.save.alreadyOn": "Salvestamine on juba sisse lülitatud", "commands.save.disabled": "Automaatne salvestamine on n<PERSON><PERSON>d keelatud", "commands.save.enabled": "Automaatne salvestamine on nüüd lubatud", "commands.save.failed": "<PERSON><PERSON><PERSON>u ei saanud salvestada (kas on pii<PERSON><PERSON>t k<PERSON>?)", "commands.save.saving": "Salvestan mängu (see võib võtta hetke!)", "commands.save.success": "<PERSON><PERSON><PERSON>", "commands.schedule.cleared.failure": "Ajastused IDga %s puuduvad", "commands.schedule.cleared.success": "%s ajastus(t) IDga %s eemaldatud", "commands.schedule.created.function": "Funktsioon \"%s\" ajastatud %s tiksule mänguajas %s", "commands.schedule.created.tag": "Silt \"%s\" ajastatud %s tiksule mänguajas %s", "commands.schedule.macro": "Makrot ei saa ajastada", "commands.schedule.same_tick": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> ei saa ajastada", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON> nimega siht on juba olemas", "commands.scoreboard.objectives.add.success": "Uus siht %s on loodud", "commands.scoreboard.objectives.display.alreadyEmpty": "Midagi ei muutunud. See k<PERSON><PERSON><PERSON><PERSON> on juba tühi", "commands.scoreboard.objectives.display.alreadySet": "Midagi ei muutunud. See ku<PERSON><PERSON><PERSON> juba kuvab seda sihti", "commands.scoreboard.objectives.display.cleared": "Mistahes sihid tühjendatud kuvalahtrist %s", "commands.scoreboard.objectives.display.set": "Kuvalahter %s on määratud näitama sihti %s", "commands.scoreboard.objectives.list.empty": "Sihte ei ole", "commands.scoreboard.objectives.list.success": "Sihte on %s: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Sihi %s automaatsed kuvauuendused on keelatud", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Sihi %s automaatsed kuvauuendused on lubatud", "commands.scoreboard.objectives.modify.displayname": "%s kuvatav nimi uuendatud: %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Sihi %s vaikimisi numbriformaat on tühjendatud", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Sihi %s vaikimisi numbriformaat on muudetud", "commands.scoreboard.objectives.modify.rendertype": "Sihi %s renderdustüüp muudetud", "commands.scoreboard.objectives.remove.success": "Siht %s eemaldatud", "commands.scoreboard.players.add.success.multiple": "%3$s olemil lisati %1$s sihile %2$s", "commands.scoreboard.players.add.success.single": "Mängijal %3$s lisati %1$s sihile %2$s (nüüd %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "%s o<PERSON>i kuvatav nimi tulemus<PERSON>belis %s on tühjendatud", "commands.scoreboard.players.display.name.clear.success.single": "%s ku<PERSON>av nimi tule<PERSON> %s on tühjendatud", "commands.scoreboard.players.display.name.set.success.multiple": "%2$s olemi numbriformaat tulemustabelis %3$s on muudetud: %1$s", "commands.scoreboard.players.display.name.set.success.single": "%2$s numbriformaat tulemustabelis %3$s on muudetud: %1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "%s olemi numbriformaat tulemustabelis %s on tühjendatud", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Sihi %s numbriformaat tulemustabelis %s on tühjendatud", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "%s olemi numbriformaat tulemustabelis %s on muudetud", "commands.scoreboard.players.display.numberFormat.set.success.single": "Sihi %s numbriformaat tulemustabelis %s on muudetud", "commands.scoreboard.players.enable.failed": "Midagi ei muutunud. See k<PERSON><PERSON><PERSON><PERSON> on juba lubatud", "commands.scoreboard.players.enable.invalid": "Lu<PERSON><PERSON> t<PERSON><PERSON>b ainult \"trigger\"-<PERSON><PERSON><PERSON><PERSON>", "commands.scoreboard.players.enable.success.multiple": "Käivitaja %s lubatud %s olemi jaoks", "commands.scoreboard.players.enable.success.single": "Käivitaja %s lubatud mängija %s jaoks", "commands.scoreboard.players.get.null": "%s väärtust ei saa %s pu<PERSON> hankida, seda pole määratud", "commands.scoreboard.players.get.success": "Mängijal %s on %s%s", "commands.scoreboard.players.list.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>id puudu<PERSON>d", "commands.scoreboard.players.list.entity.empty": "%s ei oma tulemusi, mida näidata", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s omab %s tulemust:", "commands.scoreboard.players.list.success": "Jälgitavaid olemeid on %s: %s", "commands.scoreboard.players.operation.success.multiple": "%s uuendatud %s olemi jaoks", "commands.scoreboard.players.operation.success.single": "%2$s %1$s määratud väärtuseks %3$s", "commands.scoreboard.players.remove.success.multiple": "%3$s olemil <PERSON> %1$s sihilt %2$s", "commands.scoreboard.players.remove.success.single": "Mängijal %3$s eemaldati %1$s sihilt %2$s (nüüd %4$s)", "commands.scoreboard.players.reset.all.multiple": "Kõik tulemused lähtestatud %s mängija jaoks", "commands.scoreboard.players.reset.all.single": "Kõik tulemused lähtestatud mängija %s jaoks", "commands.scoreboard.players.reset.specific.multiple": "%s lähtestatud %s olemi jaoks", "commands.scoreboard.players.reset.specific.single": "%s lähtestatud %s jaoks", "commands.scoreboard.players.set.success.multiple": "%2$s olemil on määratud %1$s väärtuseks %3$s", "commands.scoreboard.players.set.success.single": "%2$s %1$s määratud väärtuseks %3$s", "commands.seed.success": "Seeme: %s", "commands.setblock.failed": "Plokki ei saanud mä<PERSON>rata", "commands.setblock.success": "Plokk asukohas %s, %s, %s on muudetud", "commands.setidletimeout.success": "Mängija eemaloleku a<PERSON>l<PERSON>pp on nüüd %s minut(it)", "commands.setidletimeout.success.disabled": "Mängija eemaloleku a<PERSON> on nüüd keelatud", "commands.setworldspawn.failure.not_overworld": "Maailma tekkekohta saab vaid Pealmaailmas määrata", "commands.setworldspawn.success": "Maailma tekkekohaks on seatud %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "%6$s mängija tekkekohaks on dimensioonis %5$s seatud %1$s, %2$s, %3$s [%4$s]", "commands.spawnpoint.success.single": "Mängija %6$s tekkekohaks on dimensioonis %5$s seatud %1$s, %2$s, %3$s [%4$s]", "commands.spectate.not_spectator": "%s ei ole vaatlusrežiimis", "commands.spectate.self": "Iseennast ei saa vaadelda", "commands.spectate.success.started": "Nüüd <PERSON>: %s", "commands.spectate.success.stopped": "<PERSON><PERSON> enam ei vaadelda", "commands.spreadplayers.failed.entities": "Ei saanud levitada %s olemit ümber %s, %s (liiga palju olemeid ala kohta - proovi kasutada levituskaugust kuni %s)", "commands.spreadplayers.failed.invalid.height": "Sobimatu maxHeight %s; eeldatud maailma miinimumist %s k<PERSON><PERSON>m", "commands.spreadplayers.failed.teams": "Ei saanud levitada %s meeskonda ümber %s, %s (liiga palju olemeid ala kohta - proovi kasutada levituskaugust kuni %s)", "commands.spreadplayers.success.entities": "%s olemi levitamine ümber %s, %s keskmise vahemaaga %s plokk(i)", "commands.spreadplayers.success.teams": "%s meeskonna levitamine ümber %s, %s keskmise vahemaaga %s plokk(i)", "commands.stop.stopping": "<PERSON>i peatamine", "commands.stopsound.success.source.any": "Kõik \"%s\" helid peatatud", "commands.stopsound.success.source.sound": "He<PERSON> \"%s\" peatatud allikas \"%s\"", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON> helid peat<PERSON>ud", "commands.stopsound.success.sourceless.sound": "Heli \"%s\" peatatud", "commands.summon.failed": "<PERSON><PERSON> lo<PERSON>", "commands.summon.failed.uuid": "<PERSON>mi loomi<PERSON> e<PERSON> korduvate UUIDide tõttu", "commands.summon.invalidPosition": "<PERSON><PERSON><PERSON><PERSON>", "commands.summon.success": "Uus %s on välja kutsutud", "commands.tag.add.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> kas on juba see silt või on liiga palju silte", "commands.tag.add.success.multiple": "Silt \"%s\" lisatud %s olemile", "commands.tag.add.success.single": "Silt \"%s\" lisatud olemile %s", "commands.tag.list.multiple.empty": "%s olemil pole silte", "commands.tag.list.multiple.success": "%s olemil on kokku %s silti: %s", "commands.tag.list.single.empty": "%s ei oma silte", "commands.tag.list.single.success": "%s omab %s silti: %s", "commands.tag.remove.failed": "Sihtolemil ei ole seda silti", "commands.tag.remove.success.multiple": "Silt \"%s\" eemaldatud %s olemilt", "commands.tag.remove.success.single": "Silt \"%s\" eemaldatud olemilt %s", "commands.team.add.duplicate": "<PERSON><PERSON> nimega meeskond on juba olemas", "commands.team.add.success": "Meeskond %s on loodud", "commands.team.empty.success": "Meeskonnast %2$s eemaldati %1$s liige(t)", "commands.team.empty.unchanged": "Midagi ei muutunud. See meeskond on juba tühi", "commands.team.join.success.multiple": "%s liiget lisatud meeskonda %s", "commands.team.join.success.single": "Mängija %s lisatud meeskonda %s", "commands.team.leave.success.multiple": "%s liiget eemaldatud mistah<PERSON> me<PERSON>", "commands.team.leave.success.single": "%s on eemaldatud mistahes me<PERSON><PERSON>", "commands.team.list.members.empty": "Meeskonnas %s pole liikmeid", "commands.team.list.members.success": "Meeskonnas %s on %s liige(t): %s", "commands.team.list.teams.empty": "Meeskondi ei ole", "commands.team.list.teams.success": "Meeskondi on %s: %s", "commands.team.option.collisionRule.success": "Kokkupõrkereegel meeskonnal %s on nüüd \"%s\"", "commands.team.option.collisionRule.unchanged": "Midagi ei muutunud. Kokkupõrkereegel on juba selle väärtusega", "commands.team.option.color.success": "Meeskonna %s värv on nüüd %s", "commands.team.option.color.unchanged": "Midagi ei muutunud. See meeskond juba omab seda värvi", "commands.team.option.deathMessageVisibility.success": "Surmasõnumi nähtavus meeskonnal %s on nüüd \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Midagi ei muutunud. Surmasõnumi nähtavus on juba selle väärtusega", "commands.team.option.friendlyfire.alreadyDisabled": "Midagi ei muutunud. Sõbralik tuli on juba sellele meeskonnale keelatud", "commands.team.option.friendlyfire.alreadyEnabled": "Midagi ei muutunud. Sõbralik tuli on juba sellele meeskonnale lubatud", "commands.team.option.friendlyfire.disabled": "Sõbralik tuli keelatud meeskonnal %s", "commands.team.option.friendlyfire.enabled": "Sõbralik tuli lubatud meeskonnale %s", "commands.team.option.name.success": "Meeskonna %s nimi uuendatud", "commands.team.option.name.unchanged": "Midagi ei muutunud. See meeskond juba omab seda nime", "commands.team.option.nametagVisibility.success": "Nimesildi nähtavus meeskonnal %s on nüüd \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Midagi ei muutunud. <PERSON><PERSON><PERSON><PERSON> n<PERSON> on juba selle väärtusega", "commands.team.option.prefix.success": "Meeskonna eesliiteks on seatud %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Midagi ei muutunud. See meeskond juba ei näe nähtamatuid meeskonnakaaslasi", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Midagi ei muutunud. See meeskond juba näeb nähtamatuid meeskonna<PERSON>lasi", "commands.team.option.seeFriendlyInvisibles.disabled": "Meeskond %s ei näe enam nähtamatuid liikmeid", "commands.team.option.seeFriendlyInvisibles.enabled": "Meeskond %s näeb nüüd nähtamatuid liikmeid", "commands.team.option.suffix.success": "Meeskonna järelliiteks on seatud %s", "commands.team.remove.success": "Eemaldati meeskond %s", "commands.teammsg.failed.noteam": "Meeskonnale sõnumite saatmiseks pead ise ühes olema", "commands.teleport.invalidPosition": "Sobimatu teleporteerumiskoht", "commands.teleport.success.entity.multiple": "%s olemit teleporteeritud %s juurde", "commands.teleport.success.entity.single": "%s teleporteeriti %s juurde", "commands.teleport.success.location.multiple": "%s olemit teleporteeritud asukohta %s, %s, %s", "commands.teleport.success.location.single": "%s on teleporteeritud asukohta %s, %s, %s", "commands.test.batch.starting": "Keskkonna %s kogumi %s käivitamine", "commands.test.clear.error.no_tests": "Ei leidnud teste mida tühjendada", "commands.test.clear.success": "%s struktuur(i) tühjendatud", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klõpsa lõikelauale kopeerimiseks", "commands.test.create.success": "Testseadistus loodud testi %s jaoks", "commands.test.error.no_test_containing_pos": "Testeksemplari, mis sisaldab %s, %s, %s, ei leitud", "commands.test.error.no_test_instances": "Testeksemplare ei leitud", "commands.test.error.non_existant_test": "Testi \"%s\" ei leitud", "commands.test.error.structure_not_found": "Teststruktuuri %s ei leitud", "commands.test.error.test_instance_not_found": "Testeksemplari plokiolemit ei leitud", "commands.test.error.test_instance_not_found.position": "Testeksemplari plokiolemit ei leitud testimiseks asukohast %s, %s, %s", "commands.test.error.too_large": "Struktuuri suurus peab olema iga telje lõikes väiksem kui %s plokki", "commands.test.locate.done": "<PERSON><PERSON><PERSON><PERSON>, leiti %s struktuur(i)", "commands.test.locate.found": "Struktuur leitud asukohast: %s (kaugus: %s)", "commands.test.locate.started": "Testist<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON> al<PERSON>, see võib aega võtta...", "commands.test.no_tests": "K<PERSON><PERSON>tava<PERSON> testid puuduvad", "commands.test.relative_position": "Asukoht %s suhtes suhteline: %s", "commands.test.reset.error.no_tests": "Ei leidnud teste mida lähtestada", "commands.test.reset.success": "Lähtesta %s struktuur(i)", "commands.test.run.no_tests": "Teste ei leitud", "commands.test.run.running": "%s testi käitamine...", "commands.test.summary": "Mängutest lõpetatud! Käitati %s test(i)", "commands.test.summary.all_required_passed": "<PERSON><PERSON><PERSON> nõutud testid läbitud :)", "commands.test.summary.failed": "%s nõutud test(i) ebaõnnestus(id) :(", "commands.test.summary.optional_failed": "%s valikulist test(i) ebaõnnestus(id)", "commands.tick.query.percentiles": "Protsentiilid: P50: %sms P95: %sms P99: %sms, näidis: %s", "commands.tick.query.rate.running": "Tiksude sihtmäär: %s tiksu sekundis.\nKeskmiselt aega tiksu kohta: %sms (Siht: %sms)", "commands.tick.query.rate.sprinting": "Tiksude sihtmäär: %s tiksu sekundis (ignoreeritud, ainult infoks).\nKeskmiselt aega tiksu kohta: %sms", "commands.tick.rate.success": "Tiksude sihtmääraks on seatud %s tiksu sekundis", "commands.tick.sprint.report": "Sprint lõpetatud %s tiksuga sekundis, või %s ms tiksu kohta", "commands.tick.sprint.stop.fail": "<PERSON>ht<PERSON> tiksusprinti pole toimumas", "commands.tick.sprint.stop.success": "Tiksusprint katkestatud", "commands.tick.status.frozen": "Mäng on külmutatud", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON> k<PERSON>, aga ei suuda siht-tiksumääraga sammu pidada", "commands.tick.status.running": "<PERSON><PERSON><PERSON> jook<PERSON><PERSON> ta<PERSON>", "commands.tick.status.sprinting": "<PERSON><PERSON><PERSON> jook<PERSON><PERSON> sprint<PERSON>", "commands.tick.step.fail": "Pole võimalik mängu tiksu võrra liigutada - mäng peab olema varasemalt külmutatud", "commands.tick.step.stop.fail": "<PERSON><PERSON><PERSON> t<PERSON>ppi pole toimumas", "commands.tick.step.stop.success": "Tiksuetapp katkestatud", "commands.tick.step.success": "Läbitakse %s tiksuetapp(i)", "commands.time.query": "Kell on %s", "commands.time.set": "<PERSON><PERSON>s on seatud %s", "commands.title.cleared.multiple": "%s mäng<PERSON> pealkir<PERSON>d t<PERSON>d", "commands.title.cleared.single": "Pealkirjad tühjendatud mängija %s jaoks", "commands.title.reset.multiple": "Pealkirjavalikud lähtestatud %s mängija jaoks", "commands.title.reset.single": "Pealkirjavalikud lähtestatud mängija %s jaoks", "commands.title.show.actionbar.multiple": "<PERSON><PERSON> uut tegumiriba pealkirja mängijale %s", "commands.title.show.actionbar.single": "<PERSON><PERSON> uut tegumiriba pealkirja mängijale %s", "commands.title.show.subtitle.multiple": "<PERSON><PERSON> uut alapealkirja %s mängijale", "commands.title.show.subtitle.single": "<PERSON><PERSON> uut alapealkirja mängijale %s", "commands.title.show.title.multiple": "<PERSON><PERSON> uut pealkirja %s mängijale", "commands.title.show.title.single": "<PERSON><PERSON> uut pealkirja mängijale %s", "commands.title.times.multiple": "Pealkirja kuvamise ajad muudetud %s mängija jaoks", "commands.title.times.single": "Pealkirja kuvamise ajad muudetud mängija %s jaoks", "commands.transfer.error.no_players": "Üleviimiseks peab valima vähemalt ühe mängija", "commands.transfer.success.multiple": "%s mängija üleviimine serverisse %s: %s", "commands.transfer.success.single": "%s üleviimine serverisse %s: %s", "commands.trigger.add.success": "%s käivitatud (väärtusele lisatud %s)", "commands.trigger.failed.invalid": "Sa saad käivitada ainult \"trigger\"-t<PERSON><PERSON><PERSON> si<PERSON>e", "commands.trigger.failed.unprimed": "Sa ei saa seda sihti veel käivitada", "commands.trigger.set.success": "%s käivitatud (väärtuseks seatud %s)", "commands.trigger.simple.success": "%s käivitatud", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Teepunktid puuduvad loendis %s", "commands.waypoint.list.success": "%s teepunkt(i) loendis %s: %s", "commands.waypoint.modify.color": "Teepunkti värv on nüüd %s", "commands.waypoint.modify.color.reset": "Teepunkti värv lähtestatud", "commands.waypoint.modify.style": "<PERSON><PERSON><PERSON><PERSON> sti<PERSON> mu<PERSON>d", "commands.weather.set.clear": "Ilm on selgeks muudetud", "commands.weather.set.rain": "Ilm on vihmaseks muudetud", "commands.weather.set.thunder": "Ilm on müristavaks ja vihmaseks muudetud", "commands.whitelist.add.failed": "<PERSON>ä<PERSON><PERSON> on juba valges nimekirjas", "commands.whitelist.add.success": "%s on lisatud valgesse nimekirja", "commands.whitelist.alreadyOff": "<PERSON>ge nimekiri on juba välja lülitatud", "commands.whitelist.alreadyOn": "<PERSON><PERSON> nimekiri on juba sisse lülitatud", "commands.whitelist.disabled": "Valge nimekiri on nüüd välja lülitatud", "commands.whitelist.enabled": "Valge nimekiri on nüüd sisse lülitatud", "commands.whitelist.list": "Valges nimekirjas on %s mängija(t): %s", "commands.whitelist.none": "Valges nimekirjas pole mäng<PERSON>id", "commands.whitelist.reloaded": "<PERSON><PERSON> ni<PERSON>", "commands.whitelist.remove.failed": "Mängija ei ole valges nimekirjas", "commands.whitelist.remove.success": "%s on eemaldatud valgest nimekirjast", "commands.worldborder.center.failed": "<PERSON>agi ei muutunud. <PERSON><PERSON><PERSON><PERSON><PERSON> on juba sinna keskendatud", "commands.worldborder.center.success": "Maailmapiiri keskkohaks on määratud %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON>agi ei muutunud. <PERSON><PERSON><PERSON><PERSON><PERSON> vigastus on juba see kogus", "commands.worldborder.damage.amount.success": "Ma<PERSON><PERSON><PERSON><PERSON> kahju su<PERSON>s on määratud %s ploki kohta sekundis", "commands.worldborder.damage.buffer.failed": "<PERSON>agi ei muutunud. <PERSON><PERSON><PERSON><PERSON><PERSON> viga<PERSON><PERSON><PERSON><PERSON> on juba see vahemaa", "commands.worldborder.damage.buffer.success": "Maailma<PERSON><PERSON> ka<PERSON> on määratud %s plokk(i)", "commands.worldborder.get": "Maailmapiir on hetkel %s plokk(i) lai", "commands.worldborder.set.failed.big": "Maailmapiir ei saa olla suurem kui %s plokki lai", "commands.worldborder.set.failed.far": "Maailmapiir ei saa olla kaugemal kui %s plokki", "commands.worldborder.set.failed.nochange": "<PERSON>agi ei muutunud. <PERSON><PERSON><PERSON><PERSON><PERSON> on juba selle suurusega", "commands.worldborder.set.failed.small": "Maailmapiir ei saa olla väiksem kui üks plokk lai", "commands.worldborder.set.grow": "Ka<PERSON><PERSON><PERSON> %s ploki laiuseks %s sekundi jooksul", "commands.worldborder.set.immediate": "Maailmapiir on määratud %s ploki laiuseks", "commands.worldborder.set.shrink": "<PERSON><PERSON><PERSON> %s ploki laiuseks %s sekundi jooksul", "commands.worldborder.warning.distance.failed": "Midagi ei muutunud. <PERSON><PERSON><PERSON><PERSON><PERSON> hoiatus on juba see vahemaa", "commands.worldborder.warning.distance.success": "Maailmapiiri hoiatuskauguseks on määratud %s plokk(i)", "commands.worldborder.warning.time.failed": "Midagi ei muutunud. Maailmapi<PERSON> hoiatus on juba see aeg", "commands.worldborder.warning.time.success": "Maailmapiiri hoiatusajaks on määratud %s sekund(it)", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON> mäng<PERSON>ud rohkem kui 24 tundi", "compliance.playtime.hours": "Oled mä<PERSON>ud %s tund(i)", "compliance.playtime.message": "Liigne mängim<PERSON> võib häirida tavalist igapäevaelu", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Sisselogimine...", "connect.connecting": "Serveriga ühendumine...", "connect.encrypting": "Krüpteerimine...", "connect.failed": "Serveriga ühendumine ebaõnnestus", "connect.failed.transfer": "Serverisse üleviimisel ühendus e<PERSON>", "connect.joining": "Maailmaga ühinemine...", "connect.negotiating": "Kooskõlastamine...", "connect.reconfiging": "Taasseadistamine...", "connect.reconfiguring": "Taasseadistamine...", "connect.transferring": "Uude serverisse suunamine...", "container.barrel": "<PERSON><PERSON><PERSON>", "container.beacon": "<PERSON><PERSON><PERSON>", "container.beehive.bees": "Mesilasi: %s / %s", "container.beehive.honey": "Mett: %s / %s", "container.blast_furnace": "<PERSON><PERSON><PERSON><PERSON>", "container.brewing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON> kirst", "container.crafter": "Meisterdaja", "container.crafting": "Meisterdamine", "container.creative": "<PERSON><PERSON><PERSON> v<PERSON>", "container.dispenser": "<PERSON><PERSON><PERSON>", "container.dropper": "<PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lasuriiti", "container.enchant.lapis.one": "1 lasuriit", "container.enchant.level.many": "%s loitsimistaset", "container.enchant.level.one": "1 loitsimistase", "container.enchant.level.requirement": "<PERSON><PERSON><PERSON><PERSON> tasemeid: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON>", "container.furnace": "<PERSON>i", "container.grindstone_title": "<PERSON>nda ja e<PERSON>lda loitsud", "container.hopper": "Esemepüüdja", "container.inventory": "<PERSON><PERSON><PERSON><PERSON>", "container.isLocked": "%s on lukus!", "container.lectern": "Kõnepult", "container.loom": "Kangasteljed", "container.repair": "Paranda ja nimeta", "container.repair.cost": "Loitsimise hind: %1$s", "container.repair.expensive": "Liiga kallis!", "container.shulkerBox": "Šulker<PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "ja veel %s...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "<PERSON>i saa avada, saaki pole veel genereeritud.", "container.stonecutter": "Kivilõikur", "container.upgrade": "Täienda varustust", "container.upgrade.error_tooltip": "Eset ei saa sel viisil täiendada", "container.upgrade.missing_template_tooltip": "<PERSON>", "controls.keybinds": "<PERSON><PERSON><PERSON><PERSON>...", "controls.keybinds.duplicateKeybinds": "Seda k<PERSON>vi kasutatakse ka:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON><PERSON>", "controls.reset": "Lähtesta", "controls.resetAll": "Lähtesta klahvid", "controls.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "<PERSON><PERSON><PERSON> vali bioom", "createWorld.customize.buffet.title": "Üksiku bioomi kohandamine", "createWorld.customize.flat.height": "Kõrgus", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Alumine - %s", "createWorld.customize.flat.layer.top": "Ülemine - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> kiht", "createWorld.customize.flat.tile": "<PERSON><PERSON>", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON><PERSON> kohanda<PERSON>", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON><PERSON> korral on siin mõned, mis me eelne<PERSON>t valmis tegime!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON>ead<PERSON>", "createWorld.customize.presets.share": "<PERSON><PERSON> oma eelseadistust kellegagi jagada? <PERSON><PERSON><PERSON> allpool as<PERSON><PERSON>t kasti!", "createWorld.customize.presets.title": "<PERSON><PERSON> e<PERSON>us", "createWorld.preparing": "Maailma loomiseks ettevalmistumine...", "createWorld.tab.game.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.more.title": "Rohkem", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Omistamine", "credits_and_attribution.button.credits": "Tiitrid", "credits_and_attribution.button.licenses": "Litsentsid", "credits_and_attribution.screen.title": "<PERSON><PERSON><PERSON><PERSON> ja o<PERSON>", "dataPack.bundle.description": "Lubab katsetusjä<PERSON> kompsueseme", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "<PERSON><PERSON> mitmikmängus teiste mängijate suunda", "dataPack.locator_bar.name": "Lokaatorriba", "dataPack.minecart_improvements.description": "Kaevanduskärude parendatud liikumine", "dataPack.minecart_improvements.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> parend<PERSON>", "dataPack.redstone_experiments.description": "Katsetusjä<PERSON> redstone-muudatused", "dataPack.redstone_experiments.name": "Redstone-kats<PERSON>ed", "dataPack.title": "Vali andmepakid", "dataPack.trade_rebalance.description": "Külaelanike uuendatud kaubavahetusesüsteem", "dataPack.trade_rebalance.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> ta<PERSON>-tasakaalust<PERSON>", "dataPack.update_1_20.description": "Minecraft 1.20 uued funktsioonid ja sisu", "dataPack.update_1_20.name": "1.20 uuendus", "dataPack.update_1_21.description": "Minecraft 1.21 uued funktsioonid ja sisu", "dataPack.update_1_21.name": "1.21 uuendus", "dataPack.validation.back": "Mine tagasi", "dataPack.validation.failed": "Andmepaki valid<PERSON><PERSON><PERSON> e<PERSON>!", "dataPack.validation.reset": "Lähtesta vaikeseadetele", "dataPack.validation.working": "<PERSON><PERSON><PERSON> andmepakkide valideerimine...", "dataPack.vanilla.description": "Vaikimisi Minecrafti andmed", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "Talvevärskenduse uued funktsioonid ja sisu", "dataPack.winter_drop.name": "Talvevärskendus", "datapackFailure.safeMode": "Turvarežiim", "datapackFailure.safeMode.failed.description": "<PERSON><PERSON><PERSON> sisal<PERSON>b sobimatuid või k<PERSON> and<PERSON>d.", "datapackFailure.safeMode.failed.title": "Maailma turvarežiimis laadimine e<PERSON>.", "datapackFailure.title": "<PERSON><PERSON><PERSON> valitud and<PERSON><PERSON><PERSON><PERSON><PERSON> on vead, mis takistasid maailma laadimist.\nSa võid kas proovida seda laadida ainult vanilli andmepakiga (\"turvarežiim\") või lähed tagasi avamenüüsse ja parandad selle käsitsi.", "death.attack.anvil": "%1$s lömastati kukkuva alasi poolt", "death.attack.anvil.player": "%1$s lömastati kukkuva alasi poolt, kui too võitles %2$s-ga", "death.attack.arrow": "%1$s lasti %2$s poolt maha", "death.attack.arrow.item": "%1$s lasti %2$s poolt maha, kasutades %3$s", "death.attack.badRespawnPoint.link": "ta<PERSON>liku mängudisaini", "death.attack.badRespawnPoint.message": "%1$s tapeti %2$s poolt", "death.attack.cactus": "%1$s torgiti surnuks", "death.attack.cactus.player": "%1$s jooksis %2$s eest põgenedes kaktusesse", "death.attack.cramming": "%1$s litsuti laiaks", "death.attack.cramming.player": "%1$s lömastati %2$s poolt", "death.attack.dragonBreath": "%1$s röstiti draakoni hingeõhus", "death.attack.dragonBreath.player": "%1$s röstiti %2$s poolt draakoni hingeõhus", "death.attack.drown": "%1$s uppus ära", "death.attack.drown.player": "%1$s uppus %2$s eest põgenedes ära", "death.attack.dryout": "%1$s suri veepuudusesse", "death.attack.dryout.player": "%1$s suri %2$s eest põgenedes veepuudusesse", "death.attack.even_more_magic": "%1$s tapeti veel rohkema maagiaga", "death.attack.explosion": "%1$s lendas õhku", "death.attack.explosion.player": "%1$s lasti %2$s poolt õhku", "death.attack.explosion.player.item": "%1$s lasti %2$s poolt õh<PERSON>, kasutades %3$s", "death.attack.fall": "%1$s lõi end vastu maad <PERSON>ra", "death.attack.fall.player": "%1$s lõi end %2$s eest põgenedes vastu maad <PERSON>ra", "death.attack.fallingBlock": "%1$s lömastati kukkuva ploki poolt", "death.attack.fallingBlock.player": "%1$s lömastati kukkuva ploki poolt, kui too võitles %2$s-ga", "death.attack.fallingStalactite": "%1$s läbistati kukkuva stalaktiidi poolt", "death.attack.fallingStalactite.player": "%1$s läbistati %2$s-ga võideldes kukkuva stalaktiidi poolt", "death.attack.fireball": "%1$s tapeti %2$s poolt tulekeraga", "death.attack.fireball.item": "%1$s tapeti %2$s poolt tulekeraga, kasutades %3$s", "death.attack.fireworks": "%1$s lahkus pauguga", "death.attack.fireworks.item": "%1$s lahkus pauguga tänu %3$s ilutulestikule, mida lasi %2$s", "death.attack.fireworks.player": "%1$s lahkus %2$s-ga võideldes pauguga", "death.attack.flyIntoWall": "%1$s koges kineetilist energiat", "death.attack.flyIntoWall.player": "%1$s koges %2$s eest põgenedes kineetilist energiat", "death.attack.freeze": "%1$s külmus surnuks", "death.attack.freeze.player": "%1$s külmus %2$s tõttu surnuks", "death.attack.generic": "%1$s suri", "death.attack.generic.player": "%1$s suri %2$s tõttu", "death.attack.genericKill": "%1$s tapeti", "death.attack.genericKill.player": "%1$s tapeti v<PERSON><PERSON> %2$s-ga", "death.attack.hotFloor": "%1$s avastas, et põrand on laava", "death.attack.hotFloor.player": "%1$s kõndis oh<PERSON>ooni %2$s tõttu", "death.attack.inFire": "%1$s lahvatas leekidesse", "death.attack.inFire.player": "%1$s kõndis %2$s-ga võideldes tulle", "death.attack.inWall": "%1$s lämbus seinas", "death.attack.inWall.player": "%1$s lämbus %2$s-ga võideldes seinas", "death.attack.indirectMagic": "%1$s tapeti %2$s poolt, kasutades maagiat", "death.attack.indirectMagic.item": "%1$s tapeti %2$s poolt, kasutades %3$s", "death.attack.lava": "%1$s p<PERSON><PERSON><PERSON> la<PERSON>s u<PERSON>da", "death.attack.lava.player": "%1$s püüdis %2$s eest põgenedes laavas ujuda", "death.attack.lightningBolt": "%1$s sai välgutabamuse", "death.attack.lightningBolt.player": "%1$s sai %2$s-ga võideldes välgutabamuse", "death.attack.mace_smash": "%1$s purustati %2$s poolt", "death.attack.mace_smash.item": "%1$s purustati %2$s poolt, kasutades %3$s", "death.attack.magic": "%1$s tapeti maagiaga", "death.attack.magic.player": "%1$s tapeti %2$s eest põgenedes maagiaga ära", "death.attack.message_too_long": "Tegelikult oli sõnum liiga pikk, et täielikult edastada. Vabandame! Siin on piiratud versioon: %s", "death.attack.mob": "%1$s löödi %2$s poolt maha", "death.attack.mob.item": "%1$s löödi %2$s poolt maha, kasutades %3$s", "death.attack.onFire": "%1$s põles surnuks", "death.attack.onFire.item": "%1$s küpses %2$s-ga võideldes krõbedaks, hoides käes %3$s", "death.attack.onFire.player": "%1$s küpses %2$s-ga võideldes krõbedaks", "death.attack.outOfWorld": "%1$s kukkus maailmast välja", "death.attack.outOfWorld.player": "%1$s ei soovinud elada %2$s-ga samas maailmas", "death.attack.outsideBorder": "%1$s lahkus selle maailma piiridest", "death.attack.outsideBorder.player": "%1$s lahkus %2$s-ga võideldes selle maailma piiridest", "death.attack.player": "%1$s löödi %2$s poolt maha", "death.attack.player.item": "%1$s löödi %2$s poolt maha, kasutades %3$s", "death.attack.sonic_boom": "%1$s hävitati üleheliliselt laetud kisendusega", "death.attack.sonic_boom.item": "%1$s hävitati %2$s eest põgenedes üleheliliselt laetud kisendusega, hoides käes %3$s", "death.attack.sonic_boom.player": "%1$s hävitati %2$s eest põgenedes üleheliliselt laetud kisendusega", "death.attack.stalagmite": "%1$s aeti stalagmiidile teibasse", "death.attack.stalagmite.player": "%1$s aeti %2$s-ga võideldes stalagmiidile teibasse", "death.attack.starve": "%1$s nälgis surnuks", "death.attack.starve.player": "%1$s nälgis %2$s-ga võideldes surnuks", "death.attack.sting": "%1$s nõelati surnuks", "death.attack.sting.item": "%1$s nõelati %2$s poolt surnuks, kasutades %3$s", "death.attack.sting.player": "%1$s nõelati %2$s poolt surnuks", "death.attack.sweetBerryBush": "%1$s torgati magusate mar<PERSON><PERSON> p<PERSON><PERSON><PERSON> poolt surnuks", "death.attack.sweetBerryBush.player": "%1$s torgati %2$s eest põgenedes magusate marjade põ<PERSON>sa poolt surnuks", "death.attack.thorns": "%1$s tapeti, kui ta proovis teha haiget %2$s-le", "death.attack.thorns.item": "%1$s tapeti %3$s-ga, üritades vigastada %2$s", "death.attack.thrown": "%1$s kolgiti %2$s poolt läbi", "death.attack.thrown.item": "%1$s kolgiti %2$s poolt läbi, kasutades %3$s", "death.attack.trident": "%1$s aeti %2$s poolt teibasse", "death.attack.trident.item": "%1$s aeti %2$s poolt teibasse, kasutades %3$s", "death.attack.wither": "%1$s närbus ära", "death.attack.wither.player": "%1$s närbus %2$s-ga võideldes ära", "death.attack.witherSkull": "%1$s lasti %2$s-pealuuga maha", "death.attack.witherSkull.item": "%1$s lasti %2$s-pealuuga maha, kasutades %3$s", "death.fell.accident.generic": "%1$s kukkus kõrgelt alla", "death.fell.accident.ladder": "%1$s kukkus redelilt maha", "death.fell.accident.other_climbable": "%1$s kukkus ronides maha", "death.fell.accident.scaffolding": "%1$s kukkus tellingutelt maha", "death.fell.accident.twisting_vines": "%1$s kukkus väänduvatelt väätidelt maha", "death.fell.accident.vines": "%1$s kukkus väätidelt maha", "death.fell.accident.weeping_vines": "%1$s kukkus nutvatelt väätidelt maha", "death.fell.assist": "%1$s oli %2$s poolt määratud kukkuma", "death.fell.assist.item": "%1$s oli %2$s poolt mää<PERSON><PERSON> k<PERSON>, kasutades %3$s", "death.fell.finish": "%1$s kukkus liiga kaugele ja ta elu lõpetas %2$s", "death.fell.finish.item": "%1$s kukkus liiga kaugele ja ta elu lõpetas %2$s, kasutades %3$s", "death.fell.killer": "%1$s oli mä<PERSON><PERSON><PERSON> kukkuma", "deathScreen.quit.confirm": "<PERSON><PERSON> oled kindel, et soovid väljuda?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "<PERSON><PERSON><PERSON>", "deathScreen.score.value": "Tulemus: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON>", "deathScreen.title": "Sa surid!", "deathScreen.title.hardcore": "<PERSON>äng läbi!", "deathScreen.titleScreen": "Avamenüü", "debug.advanced_tooltips.help": "F3 + H = <PERSON><PERSON><PERSON><PERSON> näpunäited", "debug.advanced_tooltips.off": "Täpsemad näpunäited: pei<PERSON><PERSON>", "debug.advanced_tooltips.on": "Täpsemad näpunäited: ku<PERSON><PERSON>", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON> ka<PERSON>ded", "debug.chunk_boundaries.off": "Kamakapiirded: p<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.on": "Kamakapiirded: ku<PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON><PERSON><PERSON> vestlus", "debug.copy_location.help": "F3 + C = <PERSON><PERSON><PERSON>uk<PERSON>t /tp käsklusena; hoia F3 + C, et jooksutada mäng kokku", "debug.copy_location.message": "As<PERSON><PERSON>t kopeeritud lõikelauale", "debug.crash.message": "Hoiad all klahve F3 + <PERSON><PERSON> <PERSON>i sa neid lahti ei lase, jookseb mäng kokku.", "debug.crash.warning": "Kokkujooksmine pärast %s...", "debug.creative_spectator.error": "Mängurežiimi ei saanud vah<PERSON>da, puudub luba", "debug.creative_spectator.help": "F3 + N = Vaheta eelmine mängurežiim <-> vaatlus<PERSON>žiim", "debug.dump_dynamic_textures": "Dünaamilised tekstuurid salvestatud faili %s", "debug.dump_dynamic_textures.help": "F3 + S = Väljasta dünaamilised tekstuurid", "debug.gamemodes.error": "Mängurežiimi vahetajat ei saanud avada, puudub luba", "debug.gamemodes.help": "F3 + F4 = Ava mängurežiimi vahetaja", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Järgmine", "debug.help.help": "F3 + Q = <PERSON><PERSON> see ni<PERSON><PERSON><PERSON>", "debug.help.message": "<PERSON><PERSON><PERSON><PERSON>:", "debug.inspect.client.block": "Mängupoolsed plokiandmed on kopeeritud lõikelauale", "debug.inspect.client.entity": "Mängupoolsed o<PERSON><PERSON>dmed on kopeeritud lõikelauale", "debug.inspect.help": "F3 + I = <PERSON><PERSON><PERSON>- v<PERSON><PERSON> plo<PERSON><PERSON><PERSON> l<PERSON>", "debug.inspect.server.block": "Serveripoolsed plokiandmed on kopeeritud lõikelauale", "debug.inspect.server.entity": "Serveripoolsed olemiandmed on kopeeritud lõikelauale", "debug.pause.help": "F3 + Esc = <PERSON><PERSON><PERSON> il<PERSON> paus<PERSON> (kui saab pausida)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON><PERSON> kaota<PERSON>l peata mäng", "debug.pause_focus.off": "<PERSON><PERSON><PERSON> fookuse kaotamisel: ei", "debug.pause_focus.on": "<PERSON><PERSON><PERSON> fookuse kaotamisel: jah", "debug.prefix": "[Silur]:", "debug.profiling.help": "F3 + L = Alusta/lõpeta profileerimist", "debug.profiling.start": "Profileerimine %s sekundiks alustatud. Varakult lõpetamiseks kasuta F3 + L", "debug.profiling.stop": "Profileerimine lõpetatud. Tulemused salvestatud faili %s", "debug.reload_chunks.help": "F3 + A = <PERSON><PERSON> ka<PERSON> u<PERSON>i", "debug.reload_chunks.message": "<PERSON><PERSON><PERSON> k<PERSON> kamakad uuesti", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON> ress<PERSON><PERSON><PERSON><PERSON>", "debug.reload_resourcepacks.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON> l<PERSON>", "debug.show_hitboxes.off": "Löögialad: p<PERSON><PERSON><PERSON>", "debug.show_hitboxes.on": "Löögialad: ku<PERSON><PERSON>", "debug.version.header": "Kliendi versiooniinfo:", "debug.version.help": "F3 + V = Kliendi versiooni info", "demo.day.1": "Proovirežiim kestab viis m<PERSON>. Anna endast parim!", "demo.day.2": "<PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON><PERSON>", "demo.day.5": "See on sinu viimane päev!", "demo.day.6": "Oled läbinud viienda päeva. Vajuta %s, et luua oma <PERSON>ust kuvatõmmis.", "demo.day.warning": "Sinu aeg on peaaegu läbi!", "demo.demoExpired": "Prooviaeg on läbi!", "demo.help.buy": "Osta nüüd!", "demo.help.fullWrapped": "See prooviversioon kestab 5 mängusisest päeva (päriselus umbes 1 tund ja 40 minutit). Vaata vihjete saamiseks edasijõudmisi! Lõbutse hästi!", "demo.help.inventory": "Seljakoti avamiseks kasuta klahvi %1$s", "demo.help.jump": "<PERSON><PERSON><PERSON>, vajutades klahvi %1$s", "demo.help.later": "Jätka mängu!", "demo.help.movement": "Kasuta ringi liikumiseks klahve %1$s, %2$s, %3$s, %4$s ja hiirt", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> ringi, ka<PERSON><PERSON><PERSON> hiirt", "demo.help.movementShort": "<PERSON><PERSON><PERSON>, vajuta<PERSON> klahve %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecrafti proovirežiim", "demo.remainingTime": "Järelejäänud aeg: %s", "demo.reminder": "Prooviaeg on möödas. Jätkamiseks osta mäng või alusta uut maailma!", "difficulty.lock.question": "<PERSON><PERSON> oled kindel, et soovid lukustada selle maailma raskusastme? Maailma raskusastmeks jääb %1$s ning sa ei saa seda enam kunagi muuta.", "difficulty.lock.title": "Lukusta maailma rask<PERSON>", "disconnect.endOfStream": "Andmevoo lõpp", "disconnect.exceeded_packet_rate": "Vä<PERSON>ja visatud pakettide piirmäära ületamise eest", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Olekutaotluse ignoreerimine", "disconnect.loginFailedInfo": "<PERSON>sselog<PERSON><PERSON> e<PERSON>: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Mit<PERSON>kmäng on keelatud. <PERSON>lun kontrolli oma Microsofti konto seadeid.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON><PERSON> seanss (Proovi oma mäng ja Launcher taaskäivitada)", "disconnect.loginFailedInfo.serversUnavailable": "Autentimisserverid ei ole hetkel kättesaadavad. <PERSON><PERSON>n proovi uuesti.", "disconnect.loginFailedInfo.userBanned": "Sind on võrgus mängimisest blokeeritud", "disconnect.lost": "<PERSON><PERSON>us katkes", "disconnect.packetError": "Võrguprotokolli viga", "disconnect.spam": "Teksti kordamise eest välja visatud", "disconnect.timeout": "<PERSON><PERSON>us aegus", "disconnect.transfer": "Suunatud teise serverisse", "disconnect.unknownHost": "Tundmatu host", "download.pack.failed": "%s/%s paki allalaadimine ebaõnnestus", "download.pack.progress.bytes": "Progress: %s (kogusuurus teadmata)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Ressursipaki allalaadimine %s/%s", "editGamerule.default": "Vaikimisi: %s", "editGamerule.title": "<PERSON><PERSON> mä<PERSON><PERSON><PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Neeldumine", "effect.minecraft.bad_omen": "<PERSON><PERSON> enne", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Delfi<PERSON> arm", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Nobedus", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON> ka<PERSON>", "effect.minecraft.hunger": "Nälg", "effect.minecraft.infested": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON> tervis", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "Õhkukerkimine", "effect.minecraft.luck": "Õnn", "effect.minecraft.mining_fatigue": "Ka<PERSON><PERSON>mis<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.poison": "Mürk", "effect.minecraft.raid_omen": "<PERSON><PERSON> enne", "effect.minecraft.regeneration": "Taastumine", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "<PERSON><PERSON>lane langemine", "effect.minecraft.slowness": "Aeglus", "effect.minecraft.speed": "<PERSON><PERSON><PERSON>", "effect.minecraft.strength": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON> enne", "effect.minecraft.unluck": "<PERSON><PERSON>", "effect.minecraft.water_breathing": "V<PERSON>hingamine", "effect.minecraft.weakness": "Nõ<PERSON>us", "effect.minecraft.weaving": "Põimumine", "effect.minecraft.wind_charged": "<PERSON><PERSON><PERSON>etus", "effect.minecraft.wither": "Närbumine", "effect.none": "Pole mõju", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Veetõmme", "enchantment.minecraft.bane_of_arthropods": "Lülijalgsete kirstunael", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON> needus", "enchantment.minecraft.blast_protection": "<PERSON>lahvatuskait<PERSON>", "enchantment.minecraft.breach": "Läbimurdmine", "enchantment.minecraft.channeling": "Kanaliseerimine", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Sügavusliugleja", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "Sulgkerge kukkumine", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Tulekaitse", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Fortuuna", "enchantment.minecraft.frost_walker": "Härmatiskõndija", "enchantment.minecraft.impaling": "Teibasseajamine", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Tagasilöök", "enchantment.minecraft.looting": "Saagi<PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON>", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Paikamine", "enchantment.minecraft.multishot": "Mitmiklask", "enchantment.minecraft.piercing": "Läbistamine", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>e", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Löök", "enchantment.minecraft.quick_charge": "Kiirlaadimine", "enchantment.minecraft.respiration": "Hingamine", "enchantment.minecraft.riptide": "Tagasivool", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON> puudutus", "enchantment.minecraft.smite": "<PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Hingekiirus", "enchantment.minecraft.sweeping": "Pühkiv serv", "enchantment.minecraft.sweeping_edge": "Pühkiv äär", "enchantment.minecraft.swift_sneak": "Vilgas hiilimine", "enchantment.minecraft.thorns": "Okkad", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Hääbumise needus", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akaatsiast paat", "entity.minecraft.acacia_chest_boat": "Kirstuga akaatsiast paat", "entity.minecraft.allay": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.area_effect_cloud": "Mõjupilve ala", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "Nool", "entity.minecraft.axolotl": "Aks<PERSON>tl", "entity.minecraft.bamboo_chest_raft": "Kirstuga bambusest parv", "entity.minecraft.bamboo_raft": "Bambusest parv", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Mesilane", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON> paat", "entity.minecraft.birch_chest_boat": "Kirstuga kasest paat", "entity.minecraft.blaze": "Lõõm", "entity.minecraft.block_display": "Plo<PERSON> kuva", "entity.minecraft.boat": "Paat", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Koopaämblik", "entity.minecraft.cherry_boat": "Kirsipuidust paat", "entity.minecraft.cherry_chest_boat": "Kirstuga kirsipuidust paat", "entity.minecraft.chest_boat": "Kirstuga paat", "entity.minecraft.chest_minecart": "Kirstuga kaevanduskäru", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "Tursk", "entity.minecraft.command_block_minecart": "Käsuplokiga kaevanduskäru", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Tumedast tammest paat", "entity.minecraft.dark_oak_chest_boat": "Kirstuga tumedast tammest paat", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON><PERSON>", "entity.minecraft.dragon_fireball": "<PERSON><PERSON><PERSON><PERSON> tule<PERSON>a", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Visatud muna", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "End-kristall", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "Visatud enderipärl", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker": "Ilmu<PERSON><PERSON>", "entity.minecraft.evoker_fangs": "Il<PERSON><PERSON><PERSON> k<PERSON>", "entity.minecraft.experience_bottle": "Visatud loitsimispudel", "entity.minecraft.experience_orb": "Kogemuskera", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>", "entity.minecraft.falling_block": "Langev plokk", "entity.minecraft.falling_block_type": "Kukkuv %s", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "Ilutulestiku rakett", "entity.minecraft.fishing_bobber": "Õngekork", "entity.minecraft.fox": "Rebane", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Hõõg-esemeraam", "entity.minecraft.glow_squid": "Hõõgkalmaar", "entity.minecraft.goat": "Kits", "entity.minecraft.guardian": "Kai<PERSON><PERSON>", "entity.minecraft.happy_ghast": "Õnnelik ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Püüd<PERSON><PERSON>uskä<PERSON>", "entity.minecraft.horse": "Hobune", "entity.minecraft.husk": "<PERSON><PERSON><PERSON>", "entity.minecraft.illusioner": "Trikitaja", "entity.minecraft.interaction": "Interaktsioon", "entity.minecraft.iron_golem": "Raudgolem", "entity.minecraft.item": "Ese", "entity.minecraft.item_display": "<PERSON><PERSON><PERSON> k<PERSON>va", "entity.minecraft.item_frame": "Esemeraam", "entity.minecraft.jungle_boat": "Džunglipuidust paat", "entity.minecraft.jungle_chest_boat": "Kirstuga džunglipuidust paat", "entity.minecraft.killer_bunny": "Tapjajä<PERSON>", "entity.minecraft.leash_knot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.lightning_bolt": "Välgunool", "entity.minecraft.lingering_potion": "Pikaldane võlujook", "entity.minecraft.llama": "Laama", "entity.minecraft.llama_spit": "Laama sülg", "entity.minecraft.magma_cube": "Magmakuup", "entity.minecraft.mangrove_boat": "Mangroovist paat", "entity.minecraft.mangrove_chest_boat": "Kirstuga mangroovist paat", "entity.minecraft.marker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Kaevanduskäru", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Tam<PERSON>t paat", "entity.minecraft.oak_chest_boat": "Kirstuga tammest paat", "entity.minecraft.ocelot": "Otselot", "entity.minecraft.ominous_item_spawner": "Kurjakuulutav esemetekitaja", "entity.minecraft.painting": "<PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON><PERSON> tammest paat", "entity.minecraft.pale_oak_chest_boat": "Kirstuga kahvatust tammest paat", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "Fantoom", "entity.minecraft.pig": "Siga", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON> piglin", "entity.minecraft.pillager": "Rüüstaja", "entity.minecraft.player": "Mängija", "entity.minecraft.polar_bear": "J<PERSON>ä<PERSON><PERSON>", "entity.minecraft.potion": "Võlujook", "entity.minecraft.pufferfish": "Kerakala", "entity.minecraft.rabbit": "Kü<PERSON>lik", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Šulkeri kuul", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "Luukerehobune", "entity.minecraft.slime": "<PERSON><PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON><PERSON> tule<PERSON>", "entity.minecraft.sniffer": "Nuuskija", "entity.minecraft.snow_golem": "Lumegolem", "entity.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spawner_minecart": "Koletiste tekitajaga kaevanduskäru", "entity.minecraft.spectral_arrow": "Spektraalne nool", "entity.minecraft.spider": "Ämblik", "entity.minecraft.splash_potion": "Pritsitav võlujook", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON> paat", "entity.minecraft.spruce_chest_boat": "Kirstuga kuusest paat", "entity.minecraft.squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.stray": "Eksleja", "entity.minecraft.strider": "<PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON>", "entity.minecraft.text_display": "<PERSON><PERSON><PERSON> kuva", "entity.minecraft.tnt": "Süüdatud dünamiit", "entity.minecraft.tnt_minecart": "Dünamiidiga kaevanduskäru", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Kolmhark", "entity.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> kala", "entity.minecraft.tropical_fish.predefined.0": "Anemoon", "entity.minecraft.tropical_fish.predefined.1": "Must sebrasoom", "entity.minecraft.tropical_fish.predefined.10": "Viirkala", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "Diadeem-me<PERSON>inglike", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Punaste huultega ebal<PERSON>a", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "Tomativ<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Ogaselglane", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON> sebra<PERSON>om", "entity.minecraft.tropical_fish.predefined.20": "Kollasaba papago<PERSON>la", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Suhkruvati värvi <PERSON>a", "entity.minecraft.tropical_fish.predefined.7": "Palettkala", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Plokk-kala", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Savikala", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Plärtsataja", "entity.minecraft.tropical_fish.type.glitter": "Sätendaja", "entity.minecraft.tropical_fish.type.kob": "Kotkaskala", "entity.minecraft.tropical_fish.type.snooper": "Nuuskija", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Triibikkala", "entity.minecraft.tropical_fish.type.sunstreak": "Päiksevööt", "entity.minecraft.turtle": "Kilpkonn", "entity.minecraft.vex": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager": "K<PERSON><PERSON>elanik", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Kartograaf", "entity.minecraft.villager.cleric": "Vaimulik", "entity.minecraft.villager.farmer": "Talunik", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Nahatöötleja", "entity.minecraft.villager.librarian": "Raamatukoguhoidja", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "K<PERSON><PERSON>elanik", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Tööriistasepp", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "Õigustaja", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Valvur", "entity.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON>-lu<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON> p<PERSON>uu", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Zombihobune", "entity.minecraft.zombie_villager": "Zombi-külaelanik", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON><PERSON> piglin", "entity.not_summonable": "%s-tüüpi olemit ei saa luua", "event.minecraft.raid": "<PERSON>", "event.minecraft.raid.defeat": "kaotus", "event.minecraft.raid.defeat.full": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "ründajaid jäänud: %s", "event.minecraft.raid.victory": "võit", "event.minecraft.raid.victory.full": "<PERSON> <PERSON> v<PERSON><PERSON>", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON> a<PERSON>e kaart", "filled_map.explorer_jungle": "<PERSON><PERSON><PERSON>lia<PERSON><PERSON><PERSON> ka<PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "filled_map.id": "ID %s", "filled_map.level": "(Tase %s/%s)", "filled_map.locked": "<PERSON><PERSON>", "filled_map.mansion": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "filled_map.monument": "O<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "filled_map.scale": "Mõõtkava 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "filled_map.unknown": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "filled_map.village_desert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_snowy": "<PERSON><PERSON> k<PERSON> ka<PERSON>", "filled_map.village_taiga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.bottomless_pit": "Põ<PERSON><PERSON><PERSON> auk", "flat_world_preset.minecraft.classic_flat": "Klassikaliselt sile", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "P<PERSON>maail<PERSON>", "flat_world_preset.minecraft.redstone_ready": "Redstone'iks valmis", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON> unistus", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "Seiklusrežiim", "gameMode.changed": "Sinu mängurežiim on nüüd %s", "gameMode.creative": "Loomingurežiim", "gameMode.hardcore": "Halastamatu režiim", "gameMode.spectator": "Vaatlusrežiim", "gameMode.survival": "Ellujäämisrežiim", "gamerule.allowFireTicksAwayFromPlayer": "T<PERSON><PERSON> mängijatest eemal olevat tuld", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON>, kas tuli ja laava peaksid tiksuma mistahes mängijast kaugemale kui 8 kamakat", "gamerule.announceAdvancements": "Edasijõudmiste ku<PERSON>", "gamerule.blockExplosionDropDecay": "Plokitegevuste plahvatustes ei kukuta mõned plokid oma saaki", "gamerule.blockExplosionDropDecay.description": "Mõned plokid, mida lõhkusid plokkide põhjustatud plahvatused, lähevad plahvatuses kaduma.", "gamerule.category.chat": "Vestlus", "gamerule.category.drops": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.misc": "Varia", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "Mängija", "gamerule.category.spawning": "Tekitamine", "gamerule.category.updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.commandBlockOutput": "Käsuploki väljundi kuulutamine", "gamerule.commandModificationBlockLimit": "Käskluste plokimuudatuste piirang", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> arv, mida saab korraga ühe käsklusega, näiteks /fill või /clone, muuta.", "gamerule.disableElytraMovementCheck": "Kattetiibade liiku<PERSON> kontrolli keeld", "gamerule.disablePlayerMovementCheck": "Mängija liikumise kontrolli keeld", "gamerule.disableRaids": "<PERSON><PERSON> keeld", "gamerule.doDaylightCycle": "Ööpäevatsükkel", "gamerule.doEntityDrops": "Olemite varustuse kukutamine", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON> (ja nende konteinerite), <PERSON><PERSON><PERSON><PERSON><PERSON>, paatide jms kukutamist", "gamerule.doFireTick": "<PERSON><PERSON> le<PERSON> ja kustumine", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON>", "gamerule.doInsomnia": "Fantoomide teke", "gamerule.doLimitedCrafting": "Meisterdamiseks retseptide nõudmine", "gamerule.doLimitedCrafting.description": "Lu<PERSON><PERSON><PERSON> saavad mängijad meisterdada vaid avastatud retsepte.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON> saagi kuku<PERSON>", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON> elukate ressurs<PERSON> kukutamist, sealhulgas kogemuskerad.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON> teke", "gamerule.doMobSpawning.description": "Teatud olemitel võivad olla teised reeglid.", "gamerule.doPatrolSpawning": "Rüü<PERSON>jate patrullide teke", "gamerule.doTileDrops": "Plokkide kukutamine", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON> plo<PERSON><PERSON> ressursside kukutamist, sealhulgas kogemuskerad.", "gamerule.doTraderSpawning": "Uitavate kauplejate teke", "gamerule.doVinesSpread": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doVinesSpread.description": "Juhib väädiploki juhuslikku levimist lähiplokkide külgedele. Ei mõjuta teist tüüpi väädiplokke, nagu nutvad väädid, väänduvad väädid jne.", "gamerule.doWardenSpawning": "Valvurite teke", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.drowningDamage": "Uppumisvigastuste tegemine", "gamerule.enderPearlsVanishOnDeath": "Visatud end<PERSON><PERSON><PERSON><PERSON> kaovad surma korral", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON>s mängija poolt visatud enderi<PERSON><PERSON><PERSON><PERSON> ka<PERSON>d, kui see mängija sureb.", "gamerule.entitiesWithPassengersCanUsePortals": "<PERSON><PERSON><PERSON>, kelle peal <PERSON><PERSON>, saavad portaale kasutada", "gamerule.entitiesWithPassengersCanUsePortals.description": "<PERSON><PERSON>, kelle peal <PERSON>, teleport<PERSON><PERSON> läbi Netheri portaalide, <PERSON><PERSON> portaalide ja Endi läbipääsude.", "gamerule.fallDamage": "Kukkumisvigastuste tegemine", "gamerule.fireDamage": "Tulevigastuste tegemine", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON> m<PERSON>esta<PERSON>", "gamerule.forgiveDeadPlayers.description": "<PERSON><PERSON><PERSON> aetud neutra<PERSON>ed elukad kaotavad oma viha, kui si<PERSON>ud mängija <PERSON> l<PERSON>hedal.", "gamerule.freezeDamage": "Külmavigastuste tegemine", "gamerule.globalSoundEvents": "Ülemaailms<PERSON> helid", "gamerule.globalSoundEvents.description": "Teatud m<PERSON><PERSON><PERSON><PERSON>, näiteks bossi teki<PERSON>, on helis<PERSON> kõik<PERSON><PERSON> kuulda.", "gamerule.keepInventory": "Seljakoti säilitamine peale surma", "gamerule.lavaSourceConversion": "Laava muundub lähteplokiks", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON> voolav laava on ka<PERSON><PERSON> k<PERSON> pii<PERSON> laavaall<PERSON>, muund<PERSON> seegi laavaall<PERSON>.", "gamerule.locatorBar": "Mängija lokaatorriba", "gamerule.locatorBar.description": "Luba<PERSON>l kuvab lokaatorriba, mis näitab teiste mängijate suunda.", "gamerule.logAdminCommands": "Administraatorikäskluste kuulutamine", "gamerule.maxCommandChainLength": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.maxCommandChainLength.description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>suplokki<PERSON> ahela<PERSON>e ja funktsioonidele.", "gamerule.maxCommandForkCount": "Käskluse konteksti piirang", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kontekstide arv, mida saab kasutada käsk<PERSON>te nagu \"execute as\" jaoks.", "gamerule.maxEntityCramming": "Olemite kõrvutamise ülempiir", "gamerule.minecartMaxSpeed": "Kaevanduskäru maksimaalne kiirus", "gamerule.minecartMaxSpeed.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> maa peal liikuva kaevanduskäru vaikimisi kiirus.", "gamerule.mobExplosionDropDecay": "Elukaplahvatustes ei kukuta mõned plokid oma saaki", "gamerule.mobExplosionDropDecay.description": "Mõned plokid, mida lõhkusid elukate põhjustatud plahvatused, lähevad plahvatuses kaduma.", "gamerule.mobGriefing": "Olemite laastavad tegevused", "gamerule.naturalRegeneration": "<PERSON><PERSON>vise ta<PERSON>ne", "gamerule.playersNetherPortalCreativeDelay": "Mängija Nether-portaali viide loomingurežiimis", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (tiksudes), ka<PERSON> <PERSON><PERSON>žiimis mängija peab enne dimensioonivahetust Nether-portaalis seisma.", "gamerule.playersNetherPortalDefaultDelay": "Mängija <PERSON>-portaali viide mitte-loomingurežiimis", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (tiksudes), kaua mitte-loomingurežiimis mängija peab enne dimensioonivahetust Nether-portaalis seisma.", "gamerule.playersSleepingPercentage": "Magamisp<PERSON>ent", "gamerule.playersSleepingPercentage.description": "M<PERSON>ng<PERSON><PERSON> arv, kes peavad <PERSON> vahelejätmiseks magama.", "gamerule.projectilesCanBreakBlocks": "Lendkehad saavad plokke lõhkuda", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON>, kas <PERSON><PERSON><PERSON> saavad kok<PERSON>põrk<PERSON> neid plokke lõhkuda, mis on neile lõhkumiseks määratud.", "gamerule.randomTickSpeed": "Juhusliku tiksu<PERSON>use määr", "gamerule.reducedDebugInfo": "Silumisteabe vähendamine", "gamerule.reducedDebugInfo.description": "<PERSON><PERSON><PERSON> silum<PERSON> sisu.", "gamerule.sendCommandFeedback": "Käskluste tagasiside saatmine", "gamerule.showDeathMessages": "Suremiss<PERSON><PERSON>", "gamerule.snowAccumulationHeight": "<PERSON><PERSON> akumuleerumiskõrgus", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON> lund sajab, <PERSON><PERSON><PERSON> ma<PERSON> maksima<PERSON>t nii palju lumekihte.", "gamerule.spawnChunkRadius": "Tekkekamakate raadius", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON><PERSON> arv, mis jäävad <PERSON>maail<PERSON> tekkekoha ümber laadituks.", "gamerule.spawnRadius": "<PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON>a raa<PERSON>", "gamerule.spawnRadius.description": "<PERSON><PERSON><PERSON> tek<PERSON><PERSON>ha ümber oleva ala <PERSON>, kus mängijad tekkida saavad.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON><PERSON><PERSON> gene<PERSON><PERSON> vaa<PERSON>t", "gamerule.tntExplodes": "Luba dünamiidi aktiveerimist ja plahvatumist", "gamerule.tntExplosionDropDecay": "Dünamiidiplahvatustes ei kukuta mõned plokid oma saaki", "gamerule.tntExplosionDropDecay.description": "Mõned plokid, mida lõhkusid dünamiidi põhjustatud plahvatused, lähevad plahvatuses kaduma.", "gamerule.universalAnger": "<PERSON><PERSON><PERSON> viha", "gamerule.universalAnger.description": "Vihale aetud neutraalsed elukad ründa<PERSON>d mistahes lähedal asuvat mäng<PERSON>t, mitte ainult seda, kes neid vihale ajas. Töötab kõ<PERSON> paremini, kui forgiveDeadPlayers on keelatud.", "gamerule.waterSourceConversion": "<PERSON><PERSON> muundub lähteplokiks", "gamerule.waterSourceConversion.description": "<PERSON>i voolav vesi on ka<PERSON>t k<PERSON>l<PERSON> piirit<PERSON> veeallik<PERSON>ga, muundub seegi veeallik<PERSON>.", "generator.custom": "kohandatud", "generator.customized": "<PERSON><PERSON> k<PERSON>", "generator.minecraft.amplified": "VÕIMENDATUD", "generator.minecraft.amplified.info": "Teade: <PERSON><PERSON>! Nõuab lihakat arvutit.", "generator.minecraft.debug_all_block_states": "silumisrežiim", "generator.minecraft.flat": "ülitasane", "generator.minecraft.large_biomes": "suured bioomid", "generator.minecraft.normal": "v<PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "üks bioom", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "gui.abuseReport.attestation": "Selle raporti saatmisel kinnitad, et sisestatud info on täpne ning sinu teadmiste kohaselt täielik.", "gui.abuseReport.comments": "Kommentaarid", "gui.abuseReport.describe": "Üksikas<PERSON><PERSON> jagamine aitab meil teha teadliku otsuse.", "gui.abuseReport.discard.content": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaotad selle raporti ja oma kommentaarid.\nKas soovid kindlasti lahkuda?", "gui.abuseReport.discard.discard": "<PERSON><PERSON><PERSON> ja loobu raportist", "gui.abuseReport.discard.draft": "Sal<PERSON><PERSON> must<PERSON>", "gui.abuseReport.discard.return": "Jätka muutmist", "gui.abuseReport.discard.title": "Loobud raportist ja komment<PERSON>t?", "gui.abuseReport.draft.content": "<PERSON><PERSON> soovid o<PERSON>oleva raporti muutmist jätkata või sellest loobuda ja uue luua?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Jätka muutmist", "gui.abuseReport.draft.quittotitle.content": "Ka<PERSON> soovid selle muutmist jätkata või sellest loobuda?", "gui.abuseReport.draft.quittotitle.title": "Sul on vest<PERSON><PERSON><PERSON><PERSON> mustand, mis lahkudes kaob", "gui.abuseReport.draft.title": "<PERSON><PERSON><PERSON> mustandit?", "gui.abuseReport.error.title": "Sinu raporti saatmisel esines probleem", "gui.abuseReport.message": "Kus sa halba käitumist märkasid?\nSee aitab meil juhtumit paremini uurida.", "gui.abuseReport.more_comments": "<PERSON><PERSON><PERSON>, mis juhtus:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON><PERSON>, miks soovid sellest nimest teatada:", "gui.abuseReport.name.reporting": "Raporteerid mängijat \"%s\".", "gui.abuseReport.name.title": "Teata ebasobivast mängijanimest", "gui.abuseReport.observed_what": "Miks sa seda raporteerid?", "gui.abuseReport.read_info": "Loe teavitamisest lähemalt", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkootikumid või alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Keegi innustab teisi ebaseaduslikes narkootikumidega seotud tegevustes osalema või julgustab alaealisi alkoholi tarvitama.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Lapse seksuaalne ärakasutamine või väärkohtlemine", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Keegi räägib lastega seotud ebasündsast käitumisest või reklaamib seda muul viisil.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Laimamine", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Keegi kahjustab sinu või kellegi teise mainet, näiteks jagades valeinfot eesmärgiga teisi ära kasutada või eksitada.", "gui.abuseReport.reason.description": "Kirjeldus:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.generic": "Ma tahan temast teatada", "gui.abuseReport.reason.generic.description": "Ma olen ta peale pahane / ta on teinud midagi, mis mulle ei meeldi.", "gui.abuseReport.reason.harassment_or_bullying": "Ahistamine või kiusamine", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON><PERSON> h<PERSON>, r<PERSON><PERSON><PERSON> või kiusab sind või kedagi teist. See hõlma<PERSON> ka seda, kui keegi üritab korduvalt ja ilma nõusolekuta sinu või kellegi teisega ühendust võtta või kui ta avaldab sinu või kellegi teise kohta ilma nõusolekuta isiklikku teavet (\"doksimine\").", "gui.abuseReport.reason.hate_speech": "Vihakõne", "gui.abuseReport.reason.hate_speech.description": "Keegi ründab sind või teist mängijat nende identiteedi omaduste p<PERSON>t, nagu usund, rass või seksuaalsus.", "gui.abuseReport.reason.imminent_harm": "<PERSON>t teisi kah<PERSON>a", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON><PERSON> sind või kedagi teist pärise<PERSON> kah<PERSON>.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> intiimsed pildid", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON>egi räägib privaatsetest ja intiimsetest piltidest, jagab või muul moel rekla<PERSON>b neid.", "gui.abuseReport.reason.self_harm_or_suicide": "Enesevigastamine või enesetapp", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON><PERSON>ab ennast päriselus kahjustada või räägib enda päriselus kahjustamisest.", "gui.abuseReport.reason.sexually_inappropriate": "Seksuaalselt ebasobiv", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON><PERSON><PERSON><PERSON>, mis kujutavad graafiliselt seksuaalakte, seksuaalorganeid ja seksuaalset vägivalda.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism või vägivaldne ekstremism", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Keegi räägib terrorismist või vägivaldsest ekstremismist, reklaamib seda või ähvardab teostada usulistel, ideoloogilistel või muudel põhjustel.", "gui.abuseReport.reason.title": "Vali raporti kategooria", "gui.abuseReport.report_sent_msg": "Oleme sinu raporti edukalt kätte saanud. Täname!\n\nMeie meeskond vaatab selle üle nii ruttu, kui võ<PERSON>.", "gui.abuseReport.select_reason": "Vali raporti kategooria", "gui.abuseReport.send": "Saada raport", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON><PERSON> l<PERSON> kom<PERSON>i", "gui.abuseReport.send.error_message": "Sinu raporti saatmisel esines viga:\n\"%s\"", "gui.abuseReport.send.generic_error": "Raporti saamisel esines ootamatu viga.", "gui.abuseReport.send.http_error": "Raporti saamisel esines ootamatu HTTP-viga.", "gui.abuseReport.send.json_error": "Raporti sa<PERSON> esines kahjustunud infovoog.", "gui.abuseReport.send.no_reason": "<PERSON><PERSON><PERSON> vali raporti katego<PERSON>", "gui.abuseReport.send.not_attested": "<PERSON><PERSON><PERSON> loe ülalolevat teksti ning märgi raporti saatmiseks kasti linnuke", "gui.abuseReport.send.service_unavailable": "Väärkasutusest teatamise teenusega ei saa ühendust. Veendu, et oled internetti ühendatud ning proovi uuesti.", "gui.abuseReport.sending.title": "Sinu raporti saatmine...", "gui.abuseReport.sent.title": "Raport sa<PERSON>tud", "gui.abuseReport.skin.title": "Teata mängija välimusest", "gui.abuseReport.title": "<PERSON><PERSON> m<PERSON>", "gui.abuseReport.type.chat": "Vestlussõnumid", "gui.abuseReport.type.name": "<PERSON><PERSON><PERSON><PERSON> nimi", "gui.abuseReport.type.skin": "Mängija välimus", "gui.acknowledge": "<PERSON><PERSON> aru", "gui.advancements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.back": "Tagasi", "gui.banned.description": "%s\n\n%s\n\nLoe lisaks järgnevalt lingilt: %s", "gui.banned.description.permanent": "<PERSON>u konto on igaveseks blokeeritud, mis tä<PERSON><PERSON>, et sa ei saa võrgus mängida ega Realmsiga liituda.", "gui.banned.description.reason": "<PERSON><PERSON> hiljuti teate sinu konto poolt toimunud halva käitumise kohta. <PERSON><PERSON> moderaatorid on juhtumi üle vaadanud ning tuvastanud selle kui %s, mis on vastuolus Minecrafti Kogukonnastandarditega.", "gui.banned.description.reason_id": "Kood: %s", "gui.banned.description.reason_id_message": "Kood: %s - %s", "gui.banned.description.temporary": "%s Kuni selle ajani ei saa sa võrgus mängida ega Realmsiga liituda.", "gui.banned.description.temporary.duration": "Sinu konto on ajutiselt peatatud ning aktiveeritakse taas %s.", "gui.banned.description.unknownreason": "<PERSON><PERSON> hiljuti teate sinu konto poolt toimunud halva käitumise kohta. <PERSON><PERSON> moderaatorid on juhtumi üle vaadanud ning tuvastanud, et see on vastuolus Minecrafti Kogukonnastandarditega.", "gui.banned.name.description": "<PERSON>u praegune nimi - \"%s\" - rikub meie kogukonnastandardeid. Sa võid mängida üksikmängus, kuid võrgus mängimiseks pead oma nime muutma.\n\nLoe lähemalt või esita juhtumi ülevaade järgmisel lingil: %s", "gui.banned.name.title": "Nimi ei ole mitmikmängus lubatud", "gui.banned.reason.defamation_impersonation_false_information": "Ke<PERSON>gi teisena esinemine või teabe jagamine teiste ärakasutamiseks või eksitamiseks", "gui.banned.reason.drugs": "Ebaseaduslikele narkootikumidele viitamine", "gui.banned.reason.extreme_violence_or_gore": "Ülemäärase päriselu vägivalla või vigastuste kujutamine", "gui.banned.reason.false_reporting": "Ülemäärane väärate või ebatäpsete raportite saatmine", "gui.banned.reason.fraud": "Sisu pettuslik omandamine või kasutamine", "gui.banned.reason.generic_violation": "Kogukonnastandardite rikkumine", "gui.banned.reason.harassment_or_bullying": "Solvava sõnavara suunatud ja kahjulikul viisil kasu<PERSON>mine", "gui.banned.reason.hate_speech": "Vihakõne või diskrimineerimise levitamine", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, terroristlikele organisatsioonidele või kurikuulsatele isikutele viitamine", "gui.banned.reason.imminent_harm_to_person_or_property": "Päriselu isiku või omandi kahjustamise kavat<PERSON>mine", "gui.banned.reason.nudity_or_pornography": "<PERSON><PERSON><PERSON> ja või pornograafilise materjali kuvamine", "gui.banned.reason.sexually_inappropriate": "Seksuaalse iseloomuga teemade või sisu arutamine", "gui.banned.reason.spam_or_advertising": "Spämmimine või reklaamimine", "gui.banned.skin.description": "Sinu praegune välimus rikub meie kogukonnastandardeid. Sa võid mängida vaikimisi välimusega või valida uue välimuse.\n\nLoe lähemalt või esita juhtumi ülevaade järgmisel lingil: %s", "gui.banned.skin.title": "<PERSON><PERSON><PERSON><PERSON> pole lubatud", "gui.banned.title.permanent": "Konto igaveseks blokeeritud", "gui.banned.title.temporary": "<PERSON><PERSON> a<PERSON>lt peat<PERSON>ud", "gui.cancel": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Kommentaarid", "gui.chatReport.describe": "Üksikas<PERSON><PERSON> jagamine aitab meil teha teadliku otsuse.", "gui.chatReport.discard.content": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaotad selle raporti ja oma kommentaarid.\nKas soovid kindlasti lahkuda?", "gui.chatReport.discard.discard": "<PERSON><PERSON><PERSON> ja loobu raportist", "gui.chatReport.discard.draft": "Sal<PERSON><PERSON> must<PERSON>", "gui.chatReport.discard.return": "Jätka muutmist", "gui.chatReport.discard.title": "Loobud raportist ja komment<PERSON>t?", "gui.chatReport.draft.content": "<PERSON><PERSON> soovid o<PERSON>oleva raporti muutmist jätkata või sellest loobuda ja uue luua?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Jätka muutmist", "gui.chatReport.draft.quittotitle.content": "Ka<PERSON> soovid selle muutmist jätkata või sellest loobuda?", "gui.chatReport.draft.quittotitle.title": "Sul on vest<PERSON><PERSON><PERSON><PERSON> mustand, mis lahkudes kaob", "gui.chatReport.draft.title": "<PERSON><PERSON><PERSON> mustandit?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON>, mis juhtus:", "gui.chatReport.observed_what": "Miks sa seda raporteerid?", "gui.chatReport.read_info": "Loe teavitamisest lähemalt", "gui.chatReport.report_sent_msg": "Oleme edukalt sinu raporti kätte saanud. Täname!\n\nMeie meeskond vaatab selle üle nii ruttu, kui võ<PERSON>.", "gui.chatReport.select_chat": "Vali raporteeritava<PERSON>", "gui.chatReport.select_reason": "Vali raporti kategooria", "gui.chatReport.selected_chat": "%s vestlussõnum(it) raporteerimiseks valitud", "gui.chatReport.send": "Saada raport", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON><PERSON> l<PERSON> kom<PERSON>i", "gui.chatReport.send.no_reason": "<PERSON><PERSON><PERSON> vali raporti katego<PERSON>", "gui.chatReport.send.no_reported_messages": "<PERSON>lun vali raporteerimiseks vähemalt üks vestlussõnum", "gui.chatReport.send.too_many_messages": "Proovid liiga palju sõnumeid raportisse lisada", "gui.chatReport.title": "Teata mängija vestlusest", "gui.chatSelection.context": "Valiku ümber olevad sõnumid lisatakse täiendava konteksti saamiseks kaasa", "gui.chatSelection.fold": "%s sõnum(it) peidetud", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s liitus vest<PERSON>a", "gui.chatSelection.message.narrate": "%s ütles: %s kell %s", "gui.chatSelection.selected": "%s/%s sõnum(it) valitud", "gui.chatSelection.title": "Vali raporteeritava<PERSON>", "gui.continue": "Jätka", "gui.copy_link_to_clipboard": "Kopeeri link lõikelauale", "gui.days": "%s päev(a)", "gui.done": "Val<PERSON>", "gui.down": "<PERSON>a", "gui.entity_tooltip.type": "Tüüp: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s <PERSON>i tag<PERSON>", "gui.fileDropFailure.title": "<PERSON><PERSON><PERSON> lisamine e<PERSON>", "gui.hours": "%s tund(i)", "gui.loadingMinecraft": "Minecrafti laadimine", "gui.minutes": "%s minut(it)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s nupp", "gui.narrate.editBox": "%s tekstiväli: %s", "gui.narrate.slider": "%s liugur", "gui.narrate.tab": "<PERSON><PERSON><PERSON>art \"%s\"", "gui.no": "<PERSON>i", "gui.none": "<PERSON><PERSON><PERSON><PERSON>", "gui.ok": "Ok", "gui.open_report_dir": "Ava raporteerimiskaust", "gui.proceed": "Jätka", "gui.recipebook.moreRecipes": "Rohkema nägemiseks paremklõpsa", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Otsi...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> k<PERSON>", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON>", "gui.report_to_server": "Teata serverile", "gui.socialInteractions.blocking_hint": "Halda Microsofti kontoga", "gui.socialInteractions.empty_blocked": "Vestlusest pole ühtegi mängijat blokeeritud", "gui.socialInteractions.empty_hidden": "Vestlusest pole ühtegi mängijat peidetud", "gui.socialInteractions.hidden_in_chat": "Mängija %s vestlussõnumid peidetakse", "gui.socialInteractions.hide": "peida vest<PERSON>", "gui.socialInteractions.narration.hide": "Peida mängija %s sõnumid", "gui.socialInteractions.narration.report": "Teata mängija %s kohta", "gui.socialInteractions.narration.show": "Kuva mängija %s sõnumid", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON>lle nimega mängijaid ei leitud", "gui.socialInteractions.search_hint": "Otsi...", "gui.socialInteractions.server_label.multiple": "%s - %s mängijat", "gui.socialInteractions.server_label.single": "%s - %s mängija", "gui.socialInteractions.show": "n<PERSON><PERSON> vestluses", "gui.socialInteractions.shown_in_chat": "Mängija %s vestlussõnumid kuvatakse", "gui.socialInteractions.status_blocked": "Blokeeritud", "gui.socialInteractions.status_blocked_offline": "Blokeeritud - võrgust väljas", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Peidetud - võrgust väljas", "gui.socialInteractions.status_offline": "Võrgust väljas", "gui.socialInteractions.tab_all": "Kõik", "gui.socialInteractions.tab_blocked": "Blokeeritud", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON> m<PERSON>", "gui.socialInteractions.tooltip.report.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kättesaadav", "gui.socialInteractions.tooltip.report.no_messages": "Mängija %s pole raport<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> sa<PERSON>d", "gui.socialInteractions.tooltip.report.not_reportable": "Seda mängijat ei saa raporteerida, kuna tema vestlussõnumeid ei saa selles serveris kinnitada", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON>", "gui.stats": "Statistika", "gui.toMenu": "Tagasi serverite nimekirja", "gui.toRealms": "Tagasi Realmsi nimekirja", "gui.toTitle": "Tagasi avamenüüsse", "gui.toWorld": "Tagasi ma<PERSON> ni<PERSON>", "gui.togglable_slot": "Klõpsa lahtri keelamiseks", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Tagasi (%ss)", "gui.waitingForResponse.title": "<PERSON>i vastuse ootamine", "gui.yes": "<PERSON><PERSON>", "hanging_sign.edit": "<PERSON><PERSON> rippuva sildi teksti", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Kutsu", "instrument.minecraft.dream_goat_horn": "Unista", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Igatse", "inventory.binSlot": "Hävita ese", "inventory.hotbarInfo": "Salvesta plokiriba klahvidega %1$s+%2$s", "inventory.hotbarSaved": "Plokiriba salvestatud (taasta klahvidega %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON> l<PERSON>ku<PERSON>:", "item.canPlace": "Saab asetada plokkidele:", "item.canUse.unknown": "Teadmata", "item.color": "Värv: %s", "item.components": "%s komponent(i)", "item.disabled": "Keelatud ese", "item.durability": "Vastupidavus: %s / %s", "item.dyed": "Värvitud", "item.minecraft.acacia_boat": "Akaatsiast paat", "item.minecraft.acacia_chest_boat": "Kirstuga akaatsiast paat", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.amethyst_shard": "Ametüstikild", "item.minecraft.angler_pottery_shard": "Õngitseja potikild", "item.minecraft.angler_pottery_sherd": "Õngitseja potikild", "item.minecraft.apple": "Õun", "item.minecraft.archer_pottery_shard": "Vibulaskja potikild", "item.minecraft.archer_pottery_sherd": "Vibulaskja potikild", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Vöölase sünnimuna", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON> üleval potikild", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON> üleval potikild", "item.minecraft.arrow": "Nool", "item.minecraft.axolotl_bucket": "<PERSON>ks<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_spawn_egg": "<PERSON>ks<PERSON><PERSON>i <PERSON>", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bamboo_chest_raft": "Kirstuga bambusest parv", "item.minecraft.bamboo_raft": "Bambusest parv", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "Mesilase sünnimuna", "item.minecraft.beef": "<PERSON><PERSON> ve<PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Peediseemned", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON> paat", "item.minecraft.birch_chest_boat": "Kirstuga kasest paat", "item.minecraft.black_bundle": "Must komps", "item.minecraft.black_dye": "Must värvaine", "item.minecraft.black_harness": "<PERSON><PERSON> rakmed", "item.minecraft.blade_pottery_shard": "Mõõgatera potikild", "item.minecraft.blade_pottery_sherd": "Mõõgatera potikild", "item.minecraft.blaze_powder": "Lõõmapuuder", "item.minecraft.blaze_rod": "Lõõmavar<PERSON>", "item.minecraft.blaze_spawn_egg": "Lõõma s<PERSON>nnim<PERSON>", "item.minecraft.blue_bundle": "<PERSON><PERSON> komps", "item.minecraft.blue_dye": "Sinine värvaine", "item.minecraft.blue_egg": "Sinine muna", "item.minecraft.blue_harness": "Sinised rakmed", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Pol<PERSON> r<PERSON>", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Sakkidega poordi plakatimuster", "item.minecraft.bow": "Vibu", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON>", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON> komps", "item.minecraft.brown_dye": "Pruun värvaine", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON> muna", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.brush": "<PERSON>", "item.minecraft.bucket": "Ämber", "item.minecraft.bundle": "<PERSON><PERSON>", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Saab hoida esemete segakuhja", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Põlem<PERSON>", "item.minecraft.burn_pottery_sherd": "Põlem<PERSON>", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Porgand pulga otsas", "item.minecraft.cat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.cauldron": "Pada", "item.minecraft.cave_spider_spawn_egg": "Koopaämbliku sünnimuna", "item.minecraft.chainmail_boots": "Rõngassaapad", "item.minecraft.chainmail_chestplate": "Rõngassärk", "item.minecraft.chainmail_helmet": "Rõngaskiiver", "item.minecraft.chainmail_leggings": "Rõngassäärised", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "Kirsipuidust paat", "item.minecraft.cherry_chest_boat": "Kirstuga kirsipuidust paat", "item.minecraft.chest_minecart": "Kirstuga kaevanduskäru", "item.minecraft.chicken": "<PERSON><PERSON> kana", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON>", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON> tursk", "item.minecraft.cod_bucket": "Tursaämber", "item.minecraft.cod_spawn_egg": "Tursa sünnimuna", "item.minecraft.command_block_minecart": "Käsuplokiga kaevanduskäru", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON><PERSON><PERSON> veise<PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON><PERSON><PERSON> kana", "item.minecraft.cooked_cod": "Küpsetatud tursk", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON><PERSON><PERSON> lamba<PERSON>", "item.minecraft.cooked_porkchop": "Küpse<PERSON><PERSON> seal<PERSON>a", "item.minecraft.cooked_rabbit": "Küpsetatud küülik", "item.minecraft.cooked_salmon": "Küpsetatud lõhe", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Vasekang", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.creaking_spawn_egg": "K<PERSON>ik<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern": "Plakatimuster", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON><PERSON>iembleem", "item.minecraft.creeper_banner_pattern.new": "Creeperiembleemi plak<PERSON>", "item.minecraft.creeper_spawn_egg": "Creeperi sünn<PERSON>", "item.minecraft.crossbow": "Amb", "item.minecraft.crossbow.projectile": "Lendkeha:", "item.minecraft.crossbow.projectile.multiple": "Lendkeha: %s x %s", "item.minecraft.crossbow.projectile.single": "Lendkeha: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komps", "item.minecraft.cyan_dye": "Tsüaansinine värvaine", "item.minecraft.cyan_harness": "Tsüaansinised rakmed", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON> potikild", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON> potikild", "item.minecraft.dark_oak_boat": "Tumedast tammest paat", "item.minecraft.dark_oak_chest_boat": "Kirstuga tumedast tammest paat", "item.minecraft.debug_stick": "Siluripulk", "item.minecraft.debug_stick.empty": "%s ei oma atribuute", "item.minecraft.debug_stick.select": "\"%s\" (%s) valitud", "item.minecraft.debug_stick.update": "\"%s\" väärtus on nüüd %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "Teemantsaapad", "item.minecraft.diamond_chestplate": "Teemantrinnaplaat", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Teemantkõblas", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON> tee<PERSON>üü", "item.minecraft.diamond_leggings": "Teemantsäärised", "item.minecraft.diamond_pickaxe": "Teemantkirka", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Teemantmõõk", "item.minecraft.disc_fragment_5": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5.desc": "Muusikaplaat - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "Kuivatatud vetikas", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.echo_shard": "Kajakild", "item.minecraft.egg": "<PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "Vanemkaitsja sünnimuna", "item.minecraft.elytra": "Kattetiivad", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "<PERSON><PERSON><PERSON><PERSON> raamat", "item.minecraft.enchanted_golden_apple": "Loitsitud k<PERSON>", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "Enderipärl", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.evoker_spawn_egg": "Ilmu<PERSON>ja <PERSON>", "item.minecraft.experience_bottle": "Loitsimispudel", "item.minecraft.explorer_pottery_shard": "Avastaja potikild", "item.minecraft.explorer_pottery_sherd": "Avastaja potikild", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Käärinud ämblikusilm", "item.minecraft.field_masoned_banner_pattern": "Telliste täite plakatimuster", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Ilutulestiku rakett", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON> kestus:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Ilutulestiku täht", "item.minecraft.firework_star.black": "must", "item.minecraft.firework_star.blue": "sinine", "item.minecraft.firework_star.brown": "pruun", "item.minecraft.firework_star.custom_color": "kohandatud", "item.minecraft.firework_star.cyan": "ts<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "hajuv", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "hall", "item.minecraft.firework_star.green": "roheline", "item.minecraft.firework_star.light_blue": "helesinine", "item.minecraft.firework_star.light_gray": "he<PERSON><PERSON>", "item.minecraft.firework_star.lime": "laim<PERSON><PERSON>ine", "item.minecraft.firework_star.magenta": "magentapunane", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "roosa", "item.minecraft.firework_star.purple": "lilla", "item.minecraft.firework_star.red": "punane", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON><PERSON> kuju", "item.minecraft.firework_star.shape.burst": "Lõ<PERSON>kev", "item.minecraft.firework_star.shape.creeper": "Creeperi-kujuline", "item.minecraft.firework_star.shape.large_ball": "<PERSON>ur pall", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON> pall", "item.minecraft.firework_star.shape.star": "Tähekujuline", "item.minecraft.firework_star.trail": "rada", "item.minecraft.firework_star.white": "valge", "item.minecraft.firework_star.yellow": "kollane", "item.minecraft.fishing_rod": "Õng", "item.minecraft.flint": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.flow_banner_pattern": "Plakatimuster", "item.minecraft.flow_banner_pattern.desc": "Voog", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON> plak<PERSON>", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON> potikild", "item.minecraft.flower_banner_pattern": "Plakatimuster", "item.minecraft.flower_banner_pattern.desc": "Lille embleem", "item.minecraft.flower_banner_pattern.new": "Lille embleemi plakatimuster", "item.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "Rebase sünnimuna", "item.minecraft.friend_pottery_shard": "Sõbra potikild", "item.minecraft.friend_pottery_sherd": "Sõbra potikild", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON>", "item.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "Klaaspu<PERSON>", "item.minecraft.glistering_melon_slice": "Sätendav meloniviil", "item.minecraft.globe_banner_pattern": "Plakatimuster", "item.minecraft.globe_banner_pattern.desc": "Gloob<PERSON>", "item.minecraft.globe_banner_pattern.new": "Gloobuse plakatimuster", "item.minecraft.glow_berries": "Hõõgmarjad", "item.minecraft.glow_ink_sac": "Hõõg-tindikott", "item.minecraft.glow_item_frame": "Hõõg-esemeraam", "item.minecraft.glow_squid_spawn_egg": "Hõõgkalmaari sünnimuna", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tolm", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.gold_ingot": "Kullakang", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "Kuldsa<PERSON>d", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON> porgand", "item.minecraft.golden_chestplate": "Kuldrinnaplaat", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Kuldkõ<PERSON><PERSON>", "item.minecraft.golden_horse_armor": "Hobuse kuldrüü", "item.minecraft.golden_leggings": "Kuldsäärised", "item.minecraft.golden_pickaxe": "Kuldkirka", "item.minecraft.golden_shovel": "<PERSON><PERSON><PERSON>bid<PERSON>", "item.minecraft.golden_sword": "Kuldmõõk", "item.minecraft.gray_bundle": "Hall komps", "item.minecraft.gray_dye": "Hall värvaine", "item.minecraft.gray_harness": "<PERSON><PERSON> rakmed", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON> komps", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON> värvaine", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON> rakmed", "item.minecraft.guardian_spawn_egg": "Kaitsja <PERSON>im<PERSON>", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "Plakatimuster", "item.minecraft.guster_banner_pattern.desc": "Pagija", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Õnneliku ghasti s<PERSON>", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "Südame potikild", "item.minecraft.heart_pottery_sherd": "Südame potikild", "item.minecraft.heartbreak_pottery_shard": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> pot<PERSON>", "item.minecraft.heartbreak_pottery_sherd": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> pot<PERSON>", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hopper_minecart": "Püüd<PERSON><PERSON>uskä<PERSON>", "item.minecraft.horse_spawn_egg": "Hobuse sünnimuna", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Võõrustaja rüüornament", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> pot<PERSON>ld", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> pot<PERSON>ld", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ink_sac": "Tindikott", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "Raudrinnaplaat", "item.minecraft.iron_golem_spawn_egg": "<PERSON>ud<PERSON><PERSON><PERSON>", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_ingot": "Rauakang", "item.minecraft.iron_leggings": "Raudsäärised", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "Raudmõõk", "item.minecraft.item_frame": "Esemeraam", "item.minecraft.jungle_boat": "Džunglipuidust paat", "item.minecraft.jungle_chest_boat": "Kirstuga džunglipuidust paat", "item.minecraft.knowledge_book": "<PERSON><PERSON><PERSON><PERSON> raa<PERSON>", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Laavaämber", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "Nahk", "item.minecraft.leather_boots": "Nahksaapad", "item.minecraft.leather_chestplate": "Nahktuunika", "item.minecraft.leather_helmet": "Nahkmüts", "item.minecraft.leather_horse_armor": "Hobuse nahkrüü", "item.minecraft.leather_leggings": "Nahkpüksid", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON>ne komps", "item.minecraft.light_blue_dye": "Helesinine värvaine", "item.minecraft.light_blue_harness": "Helesinised rakmed", "item.minecraft.light_gray_bundle": "Helehall komps", "item.minecraft.light_gray_dye": "Helehall värvaine", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON><PERSON> rakmed", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> komps", "item.minecraft.lime_dye": "Laimiroheline värvaine", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> rakmed", "item.minecraft.lingering_potion": "Pikaldane võlujook", "item.minecraft.lingering_potion.effect.awkward": "Pikaldane kohatu võlujook", "item.minecraft.lingering_potion.effect.empty": "Pikaldane meisterdamatu võlujook", "item.minecraft.lingering_potion.effect.fire_resistance": "Pikaldane tulekindluse võlujook", "item.minecraft.lingering_potion.effect.harming": "Pikaldane kahjustav võlujook", "item.minecraft.lingering_potion.effect.healing": "Pikaldane tervendav võlujook", "item.minecraft.lingering_potion.effect.infested": "Pikaldane parasiitsuse võlujook", "item.minecraft.lingering_potion.effect.invisibility": "Pikaldane nähtamatuse võlujook", "item.minecraft.lingering_potion.effect.leaping": "Pikaldane kargamise võlujook", "item.minecraft.lingering_potion.effect.levitation": "Pikaldane õhkukerkimise võlujook", "item.minecraft.lingering_potion.effect.luck": "Pikaldane õnne võlujook", "item.minecraft.lingering_potion.effect.mundane": "Pikaldane ilmalik võlujook", "item.minecraft.lingering_potion.effect.night_vision": "Pikaldane öös nägemise võlujook", "item.minecraft.lingering_potion.effect.oozing": "Pikaldane immitsuse võlujook", "item.minecraft.lingering_potion.effect.poison": "Pikaldane mürgitav võlujook", "item.minecraft.lingering_potion.effect.regeneration": "Pikaldane taastav võlujook", "item.minecraft.lingering_potion.effect.slow_falling": "Pikaldane aeglase langemise võlujook", "item.minecraft.lingering_potion.effect.slowness": "Pikaldane aeglustav võlujook", "item.minecraft.lingering_potion.effect.strength": "Pikaldane tugevdav võlujook", "item.minecraft.lingering_potion.effect.swiftness": "Pikaldane ruttamise võlujook", "item.minecraft.lingering_potion.effect.thick": "Pikaldane tihke võlujook", "item.minecraft.lingering_potion.effect.turtle_master": "Pikaldane kilpkonnameistri võlujook", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>l", "item.minecraft.lingering_potion.effect.water_breathing": "Pikaldane vees hinga<PERSON> võlujook", "item.minecraft.lingering_potion.effect.weakness": "Pikaldane nõrgestav võlujook", "item.minecraft.lingering_potion.effect.weaving": "Pikaldane põimumise võlujook", "item.minecraft.lingering_potion.effect.wind_charged": "Pikaldane tuulelaetuse võlujook", "item.minecraft.llama_spawn_egg": "Laama sünn<PERSON>una", "item.minecraft.lodestone_compass": "Teekivi kompass", "item.minecraft.mace": "Ogan<PERSON>", "item.minecraft.magenta_bundle": "Magentap<PERSON>ne komps", "item.minecraft.magenta_dye": "Magentapunane värvaine", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> rakmed", "item.minecraft.magma_cream": "Magmakreem", "item.minecraft.magma_cube_spawn_egg": "Magmakuubi sünnimuna", "item.minecraft.mangrove_boat": "Mangroovist paat", "item.minecraft.mangrove_chest_boat": "Kirstuga mangroovist paat", "item.minecraft.map": "<PERSON><PERSON><PERSON>", "item.minecraft.melon_seeds": "Meloniseemned", "item.minecraft.melon_slice": "Meloniviil", "item.minecraft.milk_bucket": "Piimaämber", "item.minecraft.minecart": "Kaevanduskäru", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "Kaevandaja potikild", "item.minecraft.mojang_banner_pattern": "Plakatimuster", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "Mooshroomi sünnimuna", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON> s<PERSON>", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Muusikaplaat", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Muusikaplaat", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Muusikaplaat", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Muusikaplaat", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Muusikaplaat", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Muusikaplaat", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Muusikaplaat", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Muusikaplaat", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (mängutoos)", "item.minecraft.music_disc_far": "Muusikaplaat", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Muusikaplaat", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Muusikaplaat", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Muusikaplaat", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Muusikaplaat", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Muusikaplaat", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Muusikaplaat", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Muusikaplaat", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Muusikaplaat", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Muusikaplaat", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Muusikaplaat", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Muusikaplaat", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Muusikaplaat", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON> la<PERSON>", "item.minecraft.name_tag": "Nimesilt", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON><PERSON> karp", "item.minecraft.nether_brick": "<PERSON>her-tellis", "item.minecraft.nether_star": "Netheritäht", "item.minecraft.nether_wart": "Nethertüügas", "item.minecraft.netherite_axe": "Netheriitkirves", "item.minecraft.netherite_boots": "Netheriitsaapad", "item.minecraft.netherite_chestplate": "Netheriitrinnaplaat", "item.minecraft.netherite_helmet": "Net<PERSON><PERSON>t<PERSON>iver", "item.minecraft.netherite_hoe": "Netheriitkõblas", "item.minecraft.netherite_ingot": "Netheriidikang", "item.minecraft.netherite_leggings": "Netheriitsäärised", "item.minecraft.netherite_pickaxe": "Netheriitkirka", "item.minecraft.netherite_scrap": "Netheriidijäägid", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_sword": "Netheriitmõõk", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Netheriiditäiendus", "item.minecraft.oak_boat": "Tam<PERSON>t paat", "item.minecraft.oak_chest_boat": "Kirstuga tammest paat", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>nn<PERSON>", "item.minecraft.ominous_bottle": "Ku<PERSON><PERSON><PERSON><PERSON><PERSON>v pudel", "item.minecraft.ominous_trial_key": "Kurjakuulutav katsumuse võti", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> komps", "item.minecraft.orange_dye": "Oranž värvaine", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.painting": "<PERSON><PERSON>", "item.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON><PERSON> tammest paat", "item.minecraft.pale_oak_chest_boat": "Kirstuga kahvatust tammest paat", "item.minecraft.panda_spawn_egg": "Panda sünn<PERSON>una", "item.minecraft.paper": "<PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.pig_spawn_egg": "Sea sünnimuna", "item.minecraft.piglin_banner_pattern": "Plakatimuster", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Kärsa plakatimuster", "item.minecraft.piglin_brute_spawn_egg": "Jõhkra piglini sünnimuna", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> s<PERSON>", "item.minecraft.pillager_spawn_egg": "Rüüstaja sünnimuna", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON> komps", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON> vä<PERSON>", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON> rakmed", "item.minecraft.pitcher_plant": "Kanntaim", "item.minecraft.pitcher_pod": "<PERSON><PERSON><PERSON><PERSON> kaun", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON> kartul", "item.minecraft.polar_bear_spawn_egg": "Jää<PERSON><PERSON>", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.porkchop": "<PERSON><PERSON> seal<PERSON>a", "item.minecraft.potato": "<PERSON><PERSON><PERSON>", "item.minecraft.potion": "Võlujook", "item.minecraft.potion.effect.awkward": "Kohatu võlujook", "item.minecraft.potion.effect.empty": "Meisterdamatu võlujook", "item.minecraft.potion.effect.fire_resistance": "Tulekindluse võlujook", "item.minecraft.potion.effect.harming": "Kahjustav võlujook", "item.minecraft.potion.effect.healing": "Tervendav võlujook", "item.minecraft.potion.effect.infested": "Parasiitsuse võlujook", "item.minecraft.potion.effect.invisibility": "Nähtamatuse võlujook", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON> võlujook", "item.minecraft.potion.effect.levitation": "Õhkukerkimise võlujook", "item.minecraft.potion.effect.luck": "Õnne võlujook", "item.minecraft.potion.effect.mundane": "Ilmalik võlujook", "item.minecraft.potion.effect.night_vision": "Öös nägemise võlujook", "item.minecraft.potion.effect.oozing": "Immitsuse võlujook", "item.minecraft.potion.effect.poison": "Mürgitav võlujook", "item.minecraft.potion.effect.regeneration": "<PERSON>astav <PERSON>", "item.minecraft.potion.effect.slow_falling": "Aeglase langemise võlujook", "item.minecraft.potion.effect.slowness": "Aeglustav võlujook", "item.minecraft.potion.effect.strength": "Tugevdav võlujook", "item.minecraft.potion.effect.swiftness": "Ruttamise võlujook", "item.minecraft.potion.effect.thick": "Tihke võlujook", "item.minecraft.potion.effect.turtle_master": "Kilpkonnameistri võlujook", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON> hinga<PERSON> võlujook", "item.minecraft.potion.effect.weakness": "Nõrgestav võlujook", "item.minecraft.potion.effect.weaving": "Põimumise võlujook", "item.minecraft.potion.effect.wind_charged": "Tuulelaetuse võlujook", "item.minecraft.pottery_shard_archer": "Vibulaskja potikild", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON><PERSON> üleval potikild", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON><PERSON><PERSON> potikild", "item.minecraft.powder_snow_bucket": "Puuderlume ämber", "item.minecraft.prismarine_crystals": "Prismari<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Prismariinikild", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.pufferfish": "Kerakala", "item.minecraft.pufferfish_bucket": "Kerakalaämber", "item.minecraft.pufferfish_spawn_egg": "Kerakala sünnimuna", "item.minecraft.pumpkin_pie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "Kõrvitsaseemned", "item.minecraft.purple_bundle": "<PERSON><PERSON> komps", "item.minecraft.purple_dye": "Lilla värvaine", "item.minecraft.purple_harness": "<PERSON><PERSON> rakmed", "item.minecraft.quartz": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON> k<PERSON>", "item.minecraft.rabbit_foot": "Küüliku jalg", "item.minecraft.rabbit_hide": "Küülikunahk", "item.minecraft.rabbit_spawn_egg": "Küüliku sünnimuna", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Tõstja rüüorn<PERSON>", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_copper": "Toorvask", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Taastuskompass", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> komps", "item.minecraft.red_dye": "Punane värvaine", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> rakmed", "item.minecraft.redstone": "Redstone-tolm", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.resin_clump": "Vaiguklomp", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON><PERSON><PERSON> liha", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON> l<PERSON>", "item.minecraft.salmon_bucket": "Lõheämber", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "Voolija rüüornament", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON><PERSON> pot<PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON> pot<PERSON>", "item.minecraft.shears": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON>", "item.minecraft.shelter_pottery_shard": "Varjupaiga potikild", "item.minecraft.shelter_pottery_sherd": "Varjupaiga potikild", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "Must kilp", "item.minecraft.shield.blue": "<PERSON><PERSON> kilp", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> kilp", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>ine kilp", "item.minecraft.shield.gray": "Hall kilp", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON>e kilp", "item.minecraft.shield.light_blue": "Helesinine kilp", "item.minecraft.shield.light_gray": "Helehall kilp", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> kilp", "item.minecraft.shield.magenta": "Magentapunane kilp", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON> kilp", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON> kilp", "item.minecraft.shield.purple": "<PERSON><PERSON> kilp", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON> kilp", "item.minecraft.shield.white": "Valge kilp", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON> kilp", "item.minecraft.shulker_shell": "Šulkeri kest", "item.minecraft.shulker_spawn_egg": "Šulkeri sünnimuna", "item.minecraft.sign": "Silt", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "Vaikuse rüüornament", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "Luukerehobuse sünnimuna", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern": "Plakatimuster", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON> embleem", "item.minecraft.skull_banner_pattern.new": "<PERSON><PERSON><PERSON>u embleemi plakatimuster", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> potikild", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> potikild", "item.minecraft.slime_ball": "Limapal<PERSON>", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON> s<PERSON>", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "Rakendub:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON> kang või kristall", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "<PERSON> osa", "item.minecraft.smithing_template.armor_trim.ingredients": "<PERSON><PERSON> ja kristallid", "item.minecraft.smithing_template.ingredients": "Koostisosad:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON>", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Teemantvarustusele", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON>, -<PERSON><PERSON><PERSON><PERSON> või -t<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netheriidikang", "item.minecraft.smithing_template.upgrade": "Täiendus: ", "item.minecraft.sniffer_spawn_egg": "Nuuskija sünnimuna", "item.minecraft.snort_pottery_shard": "Nortsatuse potikild", "item.minecraft.snort_pottery_sherd": "Nortsatuse potikild", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Kärsa rüüornament", "item.minecraft.snow_golem_spawn_egg": "Lumegolemi sünnimuna", "item.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spectral_arrow": "Spektraalne nool", "item.minecraft.spider_eye": "Ämblikusilm", "item.minecraft.spider_spawn_egg": "Ämbliku sünnimuna", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "Pritsitav võlujook", "item.minecraft.splash_potion.effect.awkward": "Pritsitav kohatu võlujook", "item.minecraft.splash_potion.effect.empty": "Pritsitav meisterdamatu võlujook", "item.minecraft.splash_potion.effect.fire_resistance": "Pritsitav tulekindluse võlujook", "item.minecraft.splash_potion.effect.harming": "Pritsitav kahjustav võlujook", "item.minecraft.splash_potion.effect.healing": "Pritsitav tervendav võlujook", "item.minecraft.splash_potion.effect.infested": "Pritsitav parasiitsuse võlujook", "item.minecraft.splash_potion.effect.invisibility": "Pritsitav nähtamatuse võlujook", "item.minecraft.splash_potion.effect.leaping": "Pritsitav kargamise võlujook", "item.minecraft.splash_potion.effect.levitation": "Pritsitav õhkukerkimise võlujook", "item.minecraft.splash_potion.effect.luck": "Pritsitav õnne võlujook", "item.minecraft.splash_potion.effect.mundane": "Pritsitav ilmalik võlujook", "item.minecraft.splash_potion.effect.night_vision": "Pritsitav öös nägemise võlujook", "item.minecraft.splash_potion.effect.oozing": "Pritsitav immitsuse võlujook", "item.minecraft.splash_potion.effect.poison": "Pritsitav mürgitav võlujook", "item.minecraft.splash_potion.effect.regeneration": "Pritsitav taastav võlujook", "item.minecraft.splash_potion.effect.slow_falling": "Pritsitav aeglase langemise võlujook", "item.minecraft.splash_potion.effect.slowness": "Pritsitav aeglustav võlujook", "item.minecraft.splash_potion.effect.strength": "Pritsitav tugevdav võlujook", "item.minecraft.splash_potion.effect.swiftness": "Pritsitav ruttamise võlujook", "item.minecraft.splash_potion.effect.thick": "Pritsitav tihke võlujook", "item.minecraft.splash_potion.effect.turtle_master": "Pritsitav kilpkonnameistri võlujook", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>l", "item.minecraft.splash_potion.effect.water_breathing": "Pritsitav vees hinga<PERSON> võlujook", "item.minecraft.splash_potion.effect.weakness": "Pritsitav nõrgestav võlujook", "item.minecraft.splash_potion.effect.weaving": "Pritsitav põimumise võlujook", "item.minecraft.splash_potion.effect.wind_charged": "Pritsitav tuulelaetuse võlujook", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON> paat", "item.minecraft.spruce_chest_boat": "Kirstuga kuusest paat", "item.minecraft.spyglass": "Pikksilm", "item.minecraft.squid_spawn_egg": "Kalmaari s<PERSON>", "item.minecraft.stick": "Pulk", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "Kivik<PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "Kivikirka", "item.minecraft.stone_shovel": "Kivilabid<PERSON>", "item.minecraft.stone_sword": "Kivimõõk", "item.minecraft.stray_spawn_egg": "Eksleja sünnimuna", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.string": "Nöör", "item.minecraft.sugar": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON> hautis", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON>", "item.minecraft.tadpole_bucket": "<PERSON><PERSON>eämber", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "Loode rüüornament", "item.minecraft.tipped_arrow": "Mõjuga nool", "item.minecraft.tipped_arrow.effect.awkward": "Mõjuga nool", "item.minecraft.tipped_arrow.effect.empty": "Meisterdamatu mõjuga nool", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON>e nool", "item.minecraft.tipped_arrow.effect.harming": "Kahjustav nool", "item.minecraft.tipped_arrow.effect.healing": "Tervendav nool", "item.minecraft.tipped_arrow.effect.infested": "Parasiitsuse nool", "item.minecraft.tipped_arrow.effect.invisibility": "Nähtamatuse nool", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON> nool", "item.minecraft.tipped_arrow.effect.levitation": "Õhkukerkimise nool", "item.minecraft.tipped_arrow.effect.luck": "Õnne nool", "item.minecraft.tipped_arrow.effect.mundane": "Mõjuga nool", "item.minecraft.tipped_arrow.effect.night_vision": "Ö<PERSON>s nägemise nool", "item.minecraft.tipped_arrow.effect.oozing": "Immitsuse nool", "item.minecraft.tipped_arrow.effect.poison": "Mürgitav nool", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "Aeglase langemise nool", "item.minecraft.tipped_arrow.effect.slowness": "Aeglustav nool", "item.minecraft.tipped_arrow.effect.strength": "Tugevdav nool", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON>se nool", "item.minecraft.tipped_arrow.effect.thick": "Mõjuga nool", "item.minecraft.tipped_arrow.effect.turtle_master": "Kilpkonnameistri nool", "item.minecraft.tipped_arrow.effect.water": "Plärtsatuse nool", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON> hinga<PERSON> nool", "item.minecraft.tipped_arrow.effect.weakness": "Nõrgendav nool", "item.minecraft.tipped_arrow.effect.weaving": "Põimumise nool", "item.minecraft.tipped_arrow.effect.wind_charged": "Tuulelaetuse nool", "item.minecraft.tnt_minecart": "Dünamiidiga kaevanduskäru", "item.minecraft.torchflower_seeds": "T<PERSON>r<PERSON><PERSON><PERSON> seemned", "item.minecraft.totem_of_undying": "Ebasurma tootem", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> laama sünn<PERSON>una", "item.minecraft.trial_key": "Katsumus<PERSON>", "item.minecraft.trident": "Kolmhark", "item.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> kala", "item.minecraft.tropical_fish_bucket": "Troopilise kala ämber", "item.minecraft.tropical_fish_spawn_egg": "Troopilise kala sünnimuna", "item.minecraft.turtle_helmet": "Kilpkonna kilp", "item.minecraft.turtle_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Pahandaja <PERSON>", "item.minecraft.vex_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.villager_spawn_egg": "K<PERSON><PERSON>elanik<PERSON>", "item.minecraft.vindicator_spawn_egg": "Õigustaja sünnimuna", "item.minecraft.wandering_trader_spawn_egg": "Uitava kaupleja s<PERSON>nn<PERSON>", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "Eestkostja rüüornament", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON><PERSON> seen pulga otsas", "item.minecraft.water_bucket": "Veeämber", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Teeleid<PERSON>", "item.minecraft.wheat": "<PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON>ned", "item.minecraft.white_bundle": "<PERSON>ge komps", "item.minecraft.white_dye": "Valge värvaine", "item.minecraft.white_harness": "Valged rakmed", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "Metsik rüüornament", "item.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "Nõia s<PERSON>nn<PERSON>", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON>-lu<PERSON><PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Hundi sünnimuna", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "Puitkõblas", "item.minecraft.wooden_pickaxe": "Puitkirka", "item.minecraft.wooden_shovel": "Puitlabid<PERSON>", "item.minecraft.wooden_sword": "Puitmõõk", "item.minecraft.writable_book": "<PERSON><PERSON><PERSON> ja kir<PERSON>g", "item.minecraft.written_book": "<PERSON><PERSON><PERSON><PERSON><PERSON> raa<PERSON>", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> komps", "item.minecraft.yellow_dye": "Kollane värvaine", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON> rakmed", "item.minecraft.zoglin_spawn_egg": "Zoglini sünnimuna", "item.minecraft.zombie_horse_spawn_egg": "Zombihobuse sünnimuna", "item.minecraft.zombie_spawn_egg": "<PERSON>omb<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Zombi-külaelaniku sü<PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> pig<PERSON> s<PERSON>nn<PERSON>", "item.modifiers.any": "<PERSON><PERSON>:", "item.modifiers.armor": "<PERSON><PERSON> kantud:", "item.modifiers.body": "<PERSON><PERSON>:", "item.modifiers.chest": "<PERSON><PERSON>:", "item.modifiers.feet": "<PERSON><PERSON> j<PERSON>:", "item.modifiers.hand": "<PERSON><PERSON> käes:", "item.modifiers.head": "Olles peas:", "item.modifiers.legs": "<PERSON><PERSON> s<PERSON>l:", "item.modifiers.mainhand": "<PERSON><PERSON> peamises käes:", "item.modifiers.offhand": "<PERSON>lles teisases käes:", "item.modifiers.saddle": "<PERSON><PERSON> sad<PERSON><PERSON>:", "item.nbt_tags": "NBT: %s silt(i)", "item.op_block_warning.line1": "Hoiatus:", "item.op_block_warning.line2": "<PERSON>lle eseme kasutamine võib põhjustada käskluste käivitamist", "item.op_block_warning.line3": "Enne kasutamist ole kindel, et tead sisu!", "item.unbreakable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.buildingBlocks": "Ehitusplokid", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON> plokid", "itemGroup.combat": "Võitlemine", "itemGroup.consumables": "Tarbitavad", "itemGroup.crafting": "Meisterdamine", "itemGroup.foodAndDrink": "Toit ja joogid", "itemGroup.functional": "Funktsionaalsed plokid", "itemGroup.hotbar": "Salvestatud plokiribad", "itemGroup.ingredients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.inventory": "Ellujää<PERSON> se<PERSON>", "itemGroup.natural": "Looduslikud plokid", "itemGroup.op": "Operaatoritööriistad", "itemGroup.redstone": "Redstone-plokid", "itemGroup.search": "<PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "Sünnimunad", "itemGroup.tools": "Tööriistad ja abiva<PERSON>", "item_modifier.unknown": "Tundmatu eseme modifikaator: %s", "jigsaw_block.final_state": "Muutub plokiks:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "keeratav", "jigsaw_block.joint_label": "Tüki liik:", "jigsaw_block.keep_jigsaws": "P<PERSON>lede säilit.", "jigsaw_block.levels": "Tasemeid: %s", "jigsaw_block.name": "<PERSON><PERSON>:", "jigsaw_block.placement_priority": "Asetuse prioriteet:", "jigsaw_block.placement_priority.tooltip": "Kui see pusleplokk ühendub pu<PERSON>, kasutatakse seda järjekorda tüki ühenduste töötlemisel laiemas struktuuris.\n\nTükke töödeldakse kahaneva prioriteedi alusel, milles sisestuse järjekord murrab viigid.", "jigsaw_block.pool": "Sihthulk:", "jigsaw_block.selection_priority": "Valiku prioriteet:", "jigsaw_block.selection_priority.tooltip": "Kui ülemtükki töödeldakse ühenduste tarbeks, on see järjekord, milles pusleplokk proovib oma sihttükiga ühenduda.\n\nPusleplokke töödeldakse kahaneva prioriteedi al<PERSON>l, milles juhuslik järjekord murrab viigid.", "jigsaw_block.target": "<PERSON><PERSON> nimi:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (mängutoos)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.attack": "Ründa/hävita", "key.back": "<PERSON><PERSON><PERSON><PERSON> tag<PERSON>", "key.categories.creative": "Loomingurežiim", "key.categories.gameplay": "Mäng<PERSON>ine", "key.categories.inventory": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.misc": "Varia", "key.categories.movement": "Liikumine", "key.categories.multiplayer": "Mitmikmäng", "key.categories.ui": "Mängu liides", "key.chat": "<PERSON>lus", "key.command": "Ava käsklus", "key.drop": "Viska hoitav ese", "key.forward": "<PERSON><PERSON><PERSON><PERSON> edasi", "key.fullscreen": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>kraanile", "key.hotbar.1": "Plokiriba lahter 1", "key.hotbar.2": "Plokiriba lahter 2", "key.hotbar.3": "Plokiriba lahter 3", "key.hotbar.4": "Plokiriba lahter 4", "key.hotbar.5": "Plokiriba lahter 5", "key.hotbar.6": "Plokiriba <PERSON> 6", "key.hotbar.7": "Plokiriba lahter 7", "key.hotbar.8": "Plokiriba la<PERSON>er 8", "key.hotbar.9": "Plokiriba la<PERSON>er 9", "key.inventory": "Ava/sulge seljak<PERSON>", "key.jump": "<PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "suurtähelukk", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "allanool", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "numbrilaua 0", "key.keyboard.keypad.1": "numbrilaua 1", "key.keyboard.keypad.2": "numbrilaua 2", "key.keyboard.keypad.3": "numbrilaua 3", "key.keyboard.keypad.4": "numbrilaua 4", "key.keyboard.keypad.5": "numbrilaua 5", "key.keyboard.keypad.6": "numbrilaua 6", "key.keyboard.keypad.7": "numbrilaua 7", "key.keyboard.keypad.8": "numbrilaua 8", "key.keyboard.keypad.9": "numbrilaua 9", "key.keyboard.keypad.add": "numbrilaua +", "key.keyboard.keypad.decimal": "numbrilaua .", "key.keyboard.keypad.divide": "numbrilaua /", "key.keyboard.keypad.enter": "numb<PERSON><PERSON><PERSON>", "key.keyboard.keypad.equal": "numbrilaua =", "key.keyboard.keypad.multiply": "numbrilaua *", "key.keyboard.keypad.subtract": "numbrilaua -", "key.keyboard.left": "vasaknool", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "vasak Ctrl", "key.keyboard.left.shift": "vasak Shift", "key.keyboard.left.win": "vasak Win", "key.keyboard.menu": "menüüklahv", "key.keyboard.minus": "-", "key.keyboard.num.lock": "num. lukk", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "paremnool", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "parem <PERSON>l", "key.keyboard.right.shift": "parem <PERSON>", "key.keyboard.right.win": "parem <PERSON>", "key.keyboard.scroll.lock": "kerimislukk", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "t<PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "määramata", "key.keyboard.up": "ülesnool", "key.keyboard.world.1": "lisaklahv 1", "key.keyboard.world.2": "lisaklahv 2", "key.left": "<PERSON><PERSON><PERSON> vasakule", "key.loadToolbarActivator": "Plokiriba laadimiskäiviti", "key.mouse": "Klahv %1$s", "key.mouse.left": "vasak klahv", "key.mouse.middle": "keskmine klahv", "key.mouse.right": "parem k<PERSON>v", "key.pickItem": "Vali plokk", "key.playerlist": "<PERSON><PERSON> m<PERSON>", "key.quickActions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.right": "<PERSON><PERSON><PERSON>", "key.saveToolbarActivator": "Plokiriba salvestuskäiviti", "key.screenshot": "<PERSON><PERSON><PERSON>", "key.smoothCamera": "<PERSON><PERSON><PERSON>a kinemaatilist <PERSON><PERSON><PERSON>", "key.sneak": "<PERSON><PERSON>", "key.socialInteractions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.spectatorOutlines": "Too mängijad esile (vaatlejad)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Vaheta ese teisase k<PERSON>ega", "key.togglePerspective": "Vaheta perspektiivi", "key.use": "<PERSON><PERSON><PERSON> eset/aseta plokk", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Kogukond", "known_server_link.community_guidelines": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.feedback": "Tagasiside", "known_server_link.forums": "Foorumid", "known_server_link.news": "Uudised", "known_server_link.report_bug": "Teata serveri veast", "known_server_link.status": "Olek", "known_server_link.support": "<PERSON><PERSON>", "known_server_link.website": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lanServer.otherPlayers": "Seaded teistele mäng<PERSON>e", "lanServer.port": "Pordinumber", "lanServer.port.invalid": "Ebasobiv port.\nJäta tekstiväli tühjaks või sisesta teine arv vahemikus 1024 ja 65535.", "lanServer.port.invalid.new": "Ebasobiv port.\nJäta tekstiväli tühjaks või sisesta teine arv vahemikus %s ja %s.", "lanServer.port.unavailable": "Port pole saadaval.\nJäta tekstiväli tühjaks või sisesta teine arv vahemikus 1024 ja 65535.", "lanServer.port.unavailable.new": "Port pole saadaval.\nJäta tekstiväli tühjaks või sisesta teine arv vahemikus %s ja %s.", "lanServer.scanning": "<PERSON><PERSON><PERSON> sinu LAN-is olevaid mänge", "lanServer.start": "<PERSON>usta <PERSON>-ma<PERSON>", "lanServer.title": "LAN-maailm", "language.code": "est_EE", "language.name": "<PERSON><PERSON><PERSON> keel", "language.region": "<PERSON><PERSON><PERSON>", "lectern.take_book": "<PERSON><PERSON><PERSON> raa<PERSON>", "loading.progress": "%s%%", "mco.account.privacy.info": "<PERSON><PERSON> veel <PERSON> ja privaatsusseaduste kohta", "mco.account.privacy.info.button": "Loe GDPRi kohta lisaks", "mco.account.privacy.information": "Mojang rakendab teatud protseduure, kaitsmaks lapsi ja nende privaatsust, sealhulgas täites seadusi USA laste võrguprivaatsuse kaitse seadus (COPPA) ja EL isikuandmete kaitse üldmäärus (GDPR).\n\nSul võib olla vaja hankida vanema nõusolek, enne kui saad oma <PERSON> kontole ligi.", "mco.account.privacyinfo": "Mojang rakendab teatud protseduure, kaitsmaks lapsi ja nende privaatsust, sealhulgas täites seadusi USA laste võrguprivaatsuse kaitse seadus (COPPA) ja EL isikuandmete kaitse üldmäärus (GDPR).\n\nSul võib olla vaja hankida vanema nõusolek, enne kui saad oma Realmsi kontole ligi.\n\nKui sa kasutad vanemat Minecrafti kontot (logid sisse oma kasutajanimega), pead sa selle enne Mojangi kontoks tegema, et Realmsile ligi pääseda.", "mco.account.update": "Uuenda kontot", "mco.activity.noactivity": "Viimase %s päeva jooksul ei ole tegevusi", "mco.activity.title": "Mäng<PERSON> tegevu<PERSON>", "mco.backup.button.download": "Laadi alla uusim", "mco.backup.button.reset": "Lähtesta maailm", "mco.backup.button.restore": "<PERSON><PERSON><PERSON>", "mco.backup.button.upload": "<PERSON><PERSON> ma<PERSON>", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "Varukoopia (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON> pak(id)", "mco.backup.entry.gameDifficulty": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "mco.backup.entry.gameMode": "Mängurežiim", "mco.backup.entry.gameServerVersion": "Mänguserveri versioon", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "<PERSON><PERSON> nimi", "mco.backup.entry.undefined": "<PERSON><PERSON><PERSON><PERSON><PERSON> muudatus", "mco.backup.entry.uploaded": "<PERSON><PERSON>", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON>", "mco.backup.generate.world": "<PERSON><PERSON><PERSON>", "mco.backup.info.title": "<PERSON><PERSON><PERSON><PERSON> vii<PERSON><PERSON>", "mco.backup.narration": "Varundus k<PERSON>upäevast %s", "mco.backup.nobackups": "<PERSON><PERSON><PERSON> ei ole hetkel ühtegi varundust.", "mco.backup.restoring": "Sinu <PERSON> ta<PERSON>amine", "mco.backup.unknown": "TUNDMATU", "mco.brokenworld.download": "<PERSON>adi alla", "mco.brokenworld.downloaded": "Allalaaditud", "mco.brokenworld.message.line1": "<PERSON><PERSON><PERSON> l<PERSON> või vali teine ma<PERSON>m.", "mco.brokenworld.message.line2": "Sa võid ka maailma ü<PERSON>ängu allala<PERSON>da.", "mco.brokenworld.minigame.title": "Seda minimängu enam ei toetata", "mco.brokenworld.nonowner.error": "<PERSON><PERSON><PERSON> o<PERSON>, kuni Realmi omanik lähtestab maailma", "mco.brokenworld.nonowner.title": "Maailm on aegunud", "mco.brokenworld.play": "Mäng<PERSON>", "mco.brokenworld.reset": "Lähesta", "mco.brokenworld.title": "Sinu praegune maailm ei ole enam toetatud", "mco.client.incompatible.msg.line1": "Sinu mäng ei ühildu Realmsiga.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON><PERSON> ka<PERSON>ta <PERSON> uusimat versiooni.", "mco.client.incompatible.msg.line3": "Realms ei ühildu katsetusversioonidega.", "mco.client.incompatible.title": "Mäng ei ühildu!", "mco.client.outdated.stable.version": "Sinu mänguversioon (%s) ei ühildu Realms'iga.\n\n<PERSON><PERSON><PERSON> kasuta Minecraft'i viimast versiooni.", "mco.client.unsupported.snapshot.version": "Sinu mänguversioon (%s) ei ühildu Realms'iga.\n\nRealms ei ole selles katsetusversioonis saadaval.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON>", "mco.compatibility.downgrade.description": "Seda maailma mängiti viimati versioonis %s, sina kasutad versiooni %s. Maailma madaldamine võib põhjustada korrumpeerumist - me ei saa garanteerida, et see laadib või toimib.\n\nSinu maailma varundus salvestatakse \"Maailma varunduste\" menüüsse. <PERSON><PERSON><PERSON> taasta oma maail<PERSON>, kui see on vajalik.", "mco.compatibility.incompatible.popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> versioon", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON><PERSON>, millega proovid <PERSON>, on ühildumatu sinu mänguversiooniga.", "mco.compatibility.incompatible.series.popup.message": "Seda maailma mängiti viimati versioonis %s, sina kasutad versiooni %s.\n\nNeed ei ole teineteisega ühilduvad. Selles versioonis mängimiseks on vajalik uus maailm.", "mco.compatibility.unverifiable.message": "<PERSON><PERSON><PERSON><PERSON>, kus seda maailma vii<PERSON>i mä<PERSON>, ei saadud kontrollida. <PERSON>i maailm saab uuendatud või madaldat<PERSON>, lu<PERSON><PERSON>e automaats<PERSON>t varundus, mille leiad \"Maailma varunduste\" <PERSON><PERSON><PERSON><PERSON>.", "mco.compatibility.unverifiable.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>v", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Seda maailma mängiti viimati versioonis %s, sina kasutad versiooni %s.\n\nSinu maailma varundus salvestatakse \"Maailma varunduste\" menüüsse.\n\n<PERSON><PERSON><PERSON> taasta oma maail<PERSON>, kui see on vajalik.", "mco.compatibility.upgrade.friend.description": "Seda maailma mängiti viimati versioonis %s, sina kasutad versiooni %s.\n\nSinu maailma varundus salvestatakse \"Maailma varunduste\" menüüsse.\n\nRealmi omanik saab selle maailma vajadusel taastada.", "mco.compatibility.upgrade.title": "Kas soovid tõesti seda maailma uuendada?", "mco.configure.current.minigame": "Praegune", "mco.configure.world.activityfeed.disabled": "Mängija voog ajutiselt keelatud", "mco.configure.world.backup": "<PERSON><PERSON><PERSON> varundused", "mco.configure.world.buttons.activity": "Mäng<PERSON> tegevu<PERSON>", "mco.configure.world.buttons.close": "Sulge Realm ajutiselt", "mco.configure.world.buttons.delete": "Kustuta", "mco.configure.world.buttons.done": "Val<PERSON>", "mco.configure.world.buttons.edit": "Seaded", "mco.configure.world.buttons.invite": "Kutsu mängija", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON><PERSON> valikuid", "mco.configure.world.buttons.newworld": "<PERSON><PERSON> ma<PERSON>", "mco.configure.world.buttons.open": "Taasava Realm", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON> valikud", "mco.configure.world.buttons.players": "Mängijad", "mco.configure.world.buttons.region_preference": "Vali regioon...", "mco.configure.world.buttons.resetworld": "Lähtesta maailm", "mco.configure.world.buttons.save": "Salvesta", "mco.configure.world.buttons.settings": "Seaded", "mco.configure.world.buttons.subscription": "<PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "Vaheta minimängu", "mco.configure.world.close.question.line1": "Sa võid ajutiselt oma Realmi sulgeda, vältimaks mängijaid muudatuste tegemise ajal. Ava see taas, kui oled valmis. \n\nSee ei tühista sinu Realmsi tellimust.", "mco.configure.world.close.question.line2": "Kas oled kindel, et soovid jätkata?", "mco.configure.world.close.question.title": "<PERSON><PERSON> ka<PERSON> muudatusi teha?", "mco.configure.world.closing": "Realmi ajutine sulgemine...", "mco.configure.world.commandBlocks": "Käsuplokid", "mco.configure.world.delete.button": "Kustuta Realm", "mco.configure.world.delete.question.line1": "Sinu Realm kustutatakse jäädavalt", "mco.configure.world.delete.question.line2": "Kas oled kindel, et soovid jätkata?", "mco.configure.world.description": "<PERSON><PERSON> k<PERSON>", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON> nimi", "mco.configure.world.edit.subscreen.adventuremap": "Mõned seadistused on keelatud, kuna sinu praegune maailm on seiklus", "mco.configure.world.edit.subscreen.experience": "Mõned seadistused on keelatud, kuna sinu praegune maailm on kogemus", "mco.configure.world.edit.subscreen.inspiration": "Mõned seadistused on keelatud, kuna sinu praegune maailm on inspiratsioon", "mco.configure.world.forceGameMode": "Sunnitud m<PERSON>ng<PERSON>žiim", "mco.configure.world.invite.narration": "Sul on %s uus/uut kutse(t)", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Kutsutud", "mco.configure.world.invited.number": "Kutsutud (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Operaator", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "<PERSON>i sa lahkud sellest Realmist, siis sa ei näe seda, kuni oled sinna uuesti kutsutud", "mco.configure.world.leave.question.line2": "Kas oled kindel, et soovid jätkata?", "mco.configure.world.loading": "<PERSON><PERSON> la<PERSON>", "mco.configure.world.location": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.minigame": "Praegune: %s", "mco.configure.world.name": "<PERSON><PERSON> nimi", "mco.configure.world.opening": "Realmi avamine...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON> nimega mäng<PERSON>t pole olemas", "mco.configure.world.players.inviting": "Mängija kutsumine...", "mco.configure.world.players.title": "Mängijad", "mco.configure.world.pvp": "Mängijate võitlus", "mco.configure.world.region_preference": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.region_preference.title": "Regiooni eeli<PERSON> valik", "mco.configure.world.reset.question.line1": "Su maailm taasluuakse ja praegune maailm kaob", "mco.configure.world.reset.question.line2": "Kas oled kindel, et soovid jätkata?", "mco.configure.world.resourcepack.question": "Selles Realmis mängimiseks vajad kohandatud ressursipakki\n\nKas soovid selle allalaadida ning mängida?", "mco.configure.world.resourcepack.question.line1": "Sa vajad selles Realmis mängimiseks kohandatud ressursipakki", "mco.configure.world.resourcepack.question.line2": "Kas soovid seda alla laadida ja mängida?", "mco.configure.world.restore.download.question.line1": "Maailm laaditakse alla ning lisatakse sinu üksikmängu maailmade juurde.", "mco.configure.world.restore.download.question.line2": "Kas soovid j<PERSON>?", "mco.configure.world.restore.question.line1": "Su maailm taastatakse kuupäevale \"%s\" (%s)", "mco.configure.world.restore.question.line2": "Kas oled kindel, et soovid jätkata?", "mco.configure.world.settings.expired": "<PERSON><PERSON><PERSON><PERSON>i seadeid ei saa muuta", "mco.configure.world.settings.title": "Seaded", "mco.configure.world.slot": "%s. ma<PERSON>m", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Sinu Realm vahetatakse teise maailma vastu", "mco.configure.world.slot.switch.question.line2": "Kas oled kindel, et soovid jätkata?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.active": "Ühine", "mco.configure.world.slot.tooltip.minigame": "Vaheta minimängule", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON> teke", "mco.configure.world.spawnMonsters": "Koletiste teke", "mco.configure.world.spawnNPCs": "Külaelanike teke", "mco.configure.world.spawnProtection": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>e", "mco.configure.world.spawn_toggle.message": "Selle valiku väljalülitamine eemaldab kõik olemasolevad seda tüüpi olemid", "mco.configure.world.spawn_toggle.message.npc": "Selle valiku väljalülitamine eemaldab kõik olemasolevad seda tüüpi olemid, näiteks külaelanikud", "mco.configure.world.spawn_toggle.title": "<PERSON><PERSON><PERSON>!", "mco.configure.world.status": "Olek", "mco.configure.world.subscription.day": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Pikenda tellimust", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> <PERSON><PERSON>eva", "mco.configure.world.subscription.month": "kuu", "mco.configure.world.subscription.months": "kuud", "mco.configure.world.subscription.recurring.daysleft": "Pikendatakse automaatselt:", "mco.configure.world.subscription.recurring.info": "Realmsi tellimus<PERSON> muudatused, sealhulgas aja virnastamine ning korduvarvete väljalülitamine ei kajastu enne järg<PERSON> arve<PERSON><PERSON><PERSON>eva.", "mco.configure.world.subscription.remaining.days": "%1$s päev(a)", "mco.configure.world.subscription.remaining.months": "%1$s kuu(d)", "mco.configure.world.subscription.remaining.months.days": "%1$s kuu(d), %2$s päev(a)", "mco.configure.world.subscription.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.tab": "<PERSON><PERSON>", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON><PERSON> j<PERSON>", "mco.configure.world.subscription.title": "<PERSON><PERSON> tellimus", "mco.configure.world.subscription.unknown": "Teadmata", "mco.configure.world.switch.slot": "<PERSON><PERSON> ma<PERSON>m", "mco.configure.world.switch.slot.subtitle": "See maailm on t<PERSON>hi, vali kuidas su maailma luua", "mco.configure.world.title": "Seadista Realmi:", "mco.configure.world.uninvite.player": "Oled kindel, et soovid mängija \"%s\" kutse tühistada?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> soovid kindlasti tühistada kutse mängijal", "mco.configure.worlds.title": "<PERSON><PERSON><PERSON>", "mco.connect.authorizing": "Sisselogimine...", "mco.connect.connecting": "Realmiga ühendumine...", "mco.connect.failed": "Realmiga ühendumine nurjus", "mco.connect.region": "Serveri regioon: %s", "mco.connect.success": "Val<PERSON>", "mco.create.world": "Loo", "mco.create.world.error": "Sa pead sisestama nime!", "mco.create.world.failed": "Maailma loomine e<PERSON>!", "mco.create.world.reset.title": "Maailma loomine...", "mco.create.world.skip": "<PERSON><PERSON><PERSON> v<PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON> k<PERSON> vali, mis maailma soovid kasutada oma u<PERSON>is", "mco.create.world.wait": "Realmi loomine...", "mco.download.cancelled": "Allalaadimine tühistati", "mco.download.confirmation.line1": "<PERSON><PERSON><PERSON>, mille alla laadid on suurem kui %s", "mco.download.confirmation.line2": "Sa ei saa seda maailma oma <PERSON>i taas üles la<PERSON>da", "mco.download.confirmation.oversized": "<PERSON><PERSON><PERSON>, mida alla laadid, on suurem kui %s\n\nSa ei saa seda maailma oma Realmi taas üles laadida", "mco.download.done": "Allala<PERSON><PERSON>", "mco.download.downloading": "Allalaadimine", "mco.download.extracting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.failed": "Allalaadimine nurjus", "mco.download.percent": "%s %%", "mco.download.preparing": "Allalaadimise ettevalmistamine", "mco.download.resourcePack.fail": "Ressursipaki allalaadimine ebaõnnestus!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "<PERSON><PERSON><PERSON><PERSON> maailma allalaadimine", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON> proovi Minecraft taaskäivitada", "mco.error.invalid.session.title": "Sobimatu seanss", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON> on aegunud", "mco.errorMessage.6002": "Teenusetingimustega ei nõustutud", "mco.errorMessage.6003": "Allalaadimiste piir on täis", "mco.errorMessage.6004": "Üleslaadimiste piir on täis", "mco.errorMessage.6005": "Maailm lukustatud", "mco.errorMessage.6006": "Maailm on aegunud", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON><PERSON> on liiga paljudes Realmides", "mco.errorMessage.6008": "Vale Realmi nimi", "mco.errorMessage.6009": "Vale Realmi kirjeldus", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON> t<PERSON>, palun proovi hiljem u<PERSON>.", "mco.errorMessage.generic": "Ilmnes tõrge: ", "mco.errorMessage.initialize.failed": "Realmi algatamine eba<PERSON>", "mco.errorMessage.noDetails": "Vea üksikasjad ei ole sa<PERSON>val", "mco.errorMessage.realmsService": "Ilmnes tõrge (%s):", "mco.errorMessage.realmsService.configurationError": "Maailma valikute muutmisel esines ootamatu viga", "mco.errorMessage.realmsService.connectivity": "Realmsiga ei sa<PERSON>ud <PERSON>: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Ühilduvat versiooni ei õnnestunud kontrollida, sa<PERSON>ud vastus: %s", "mco.errorMessage.retry": "Proovi tegevust uuesti", "mco.errorMessage.serviceBusy": "Realms on hetkel hõivatud.\n<PERSON><PERSON>n proovi paari minuti pärast oma realmi uuesti ühenduda.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Mängija %s kutsuti", "mco.invites.button.accept": "Nõustu", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON><PERSON><PERSON> kutseid pole!", "mco.invites.pending": "<PERSON><PERSON>/uued kutse(d)!", "mco.invites.title": "Ootel kutsed", "mco.minigame.world.changeButton": "<PERSON>i teine minim<PERSON>ng", "mco.minigame.world.info.line1": "See asendab sinu maailma ajutiselt minimänguga!", "mco.minigame.world.info.line2": "Sa saad hiljem naasta oma e<PERSON><PERSON>t loodud maailmale ilma midagi kaotamata.", "mco.minigame.world.noSelection": "<PERSON><PERSON><PERSON> tee valik", "mco.minigame.world.restore": "Minimängu lõ<PERSON>...", "mco.minigame.world.restore.question.line1": "Minimäng lõpetatakse ning su Realm taastatakse.", "mco.minigame.world.restore.question.line2": "Kas oled kindel, et soovid seda teha?", "mco.minigame.world.selected": "Valitud minimäng:", "mco.minigame.world.slot.screen.title": "Maailma vahetamine...", "mco.minigame.world.startButton": "Vaheta", "mco.minigame.world.starting.screen.title": "Minimängu käivitamine...", "mco.minigame.world.stopButton": "Lõpeta minimäng", "mco.minigame.world.switch.new": "Valid teise minimängu?", "mco.minigame.world.switch.title": "Vaheta minimängu", "mco.minigame.world.title": "Vaheta Realm minimängu vastu", "mco.news": "<PERSON><PERSON> uudised", "mco.notification.dismiss": "Sulge", "mco.notification.transferSubscription.buttonText": "<PERSON>ii <PERSON><PERSON> kohe", "mco.notification.transferSubscription.message": "Java Realmsi tellimused liiguvad üle Microsoft Store'i. Ära lase oma tellimusel aeguda!\nVii oma tellimus üle kohe ja saa tasuta 30 päeva Realmsi kasutusõigust.\nTellimuse üleviimiseks mine minecraft.net lehel menüüsse Profile.", "mco.notification.visitUrl.buttonText.default": "Ava link", "mco.notification.visitUrl.message.default": "<PERSON><PERSON><PERSON> k<PERSON> allole<PERSON> linki", "mco.onlinePlayers": "Võrgusolevad mängijad", "mco.play.button.realm.closed": "Realm on suletud", "mco.question": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Kogemused", "mco.reset.world.generate": "<PERSON><PERSON> ma<PERSON>", "mco.reset.world.inspiration": "Inspiratsioon", "mco.reset.world.resetting.screen.title": "Lähtestan ma<PERSON>...", "mco.reset.world.seed": "Seeme (valikuline)", "mco.reset.world.template": "<PERSON><PERSON><PERSON> mallid", "mco.reset.world.title": "Lähtesta maailm", "mco.reset.world.upload": "<PERSON><PERSON> ma<PERSON>", "mco.reset.world.warning": "See asendab sinu Realmi praeguse maailma", "mco.selectServer.buy": "Osta üks Realm!", "mco.selectServer.close": "Sulge", "mco.selectServer.closed": "Desaktiveeritud Realm", "mco.selectServer.closeserver": "Sulge Realm", "mco.selectServer.configure": "Seadista", "mco.selectServer.configureRealm": "Seadista Realmi", "mco.selectServer.create": "Loo Realm", "mco.selectServer.create.subtitle": "<PERSON><PERSON>, milline maailm oma uude <PERSON> panna", "mco.selectServer.expired": "Aegunud Realm", "mco.selectServer.expiredList": "<PERSON><PERSON> tellimus on aegunud", "mco.selectServer.expiredRenew": "Pikenda", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON>", "mco.selectServer.expiredTrial": "<PERSON><PERSON> prooviaeg on l<PERSON><PERSON>nud", "mco.selectServer.expires.day": "Aegub ü<PERSON> päeva pärast", "mco.selectServer.expires.days": "Aegub %s päeva pärast", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>", "mco.selectServer.leave": "Lahku Realmist", "mco.selectServer.loading": "Realmide loendi laadimine", "mco.selectServer.mapOnlySupportedForVersion": "See maailm pole toetatud %s-s", "mco.selectServer.minigame": "Minimäng:", "mco.selectServer.minigameName": "Minimäng: %s", "mco.selectServer.minigameNotSupportedInVersion": "Seda minimängu ei saa mängida %s-s", "mco.selectServer.noRealms": "Sul ei paista ühtegi Realmi olevat. <PERSON>, et sõpradega koos mängida.", "mco.selectServer.note": "Märkus:", "mco.selectServer.open": "Avatud Realm", "mco.selectServer.openserver": "Ava Realm", "mco.selectServer.play": "Mäng<PERSON>", "mco.selectServer.popup": "Realms on ohutu, lihtne viis nautida võrgupõhist Minecrafti maailma koos kuni kümne sõbraga. See toetab paljusid minimänge ja kohandatud maailmu! Ainult Realmi omanik peab maksma.", "mco.selectServer.purchase": "<PERSON>", "mco.selectServer.trial": "Hangi prooviaeg!", "mco.selectServer.uninitialized": "<PERSON><PERSON><PERSON><PERSON><PERSON>, et luua oma uus Realm!", "mco.snapshot.createSnapshotPopup.text": "Hakkad looma tasuta katsetusversiooni Realmi, mis paaritatakse sinu tasulise Realmsi tellimusega. See uus katsetusversiooni Realm jääb kättesaadavaks senikauaks, kuni tasuline tellimus on aktiivne. Sinu tasulist Realmi see ei mõjuta.", "mco.snapshot.createSnapshotPopup.title": "Loo katsetusversiooni Realm?", "mco.snapshot.creating": "Katsetusversiooni Realmi loomine...", "mco.snapshot.description": "\"%s\"-ga paar<PERSON><PERSON>d", "mco.snapshot.friendsRealm.downgrade": "Selle Realmiga liitumiseks pead olema versioonis %s", "mco.snapshot.friendsRealm.upgrade": "<PERSON><PERSON>, kui saad selles versioonis m<PERSON>, peab %s oma Realmi uuendama", "mco.snapshot.paired": "See katsetusversiooni Realm on paaritatud \"%s\"-ga", "mco.snapshot.parent.tooltip": "Selles Realmis mängimiseks kasuta Minecrafti viimast väljalaskeversiooni", "mco.snapshot.start": "<PERSON><PERSON><PERSON> tasuta <PERSON>iooni <PERSON>i", "mco.snapshot.subscription.info": "See on katsetusversiooni Realm, mis on paaritatud sinu Realmi \"%s\" tellimusega. See püsib aktiivne nii kaua, kuni selle paaritatud Realm on aktiivne.", "mco.snapshot.tooltip": "<PERSON><PERSON><PERSON>oni Realmsi, et saada pilguheit tulevastesse Minecrafti versioonidesse, mis võivad sisaldada uusi funktsioone ja teisi muudatusi.\n\nTavalise Realmsi leiad mängu väljalaskeversioonist.", "mco.snapshotRealmsPopup.message": "Realms on n<PERSON>üd sa<PERSON> ka katsetusversioonides, alustades versioonist 23w41a. Iga Realmsi tellimus tuleb tasuta katsetusversiooni Realmiga, mis on eraldatud sinu tavalisest Java Realmist!", "mco.snapshotRealmsPopup.title": "Realms nüüd sa<PERSON> ka katsetusversioonides", "mco.snapshotRealmsPopup.urlText": "<PERSON>e lisaks", "mco.template.button.publisher": "Väljaandja", "mco.template.button.select": "Vali", "mco.template.button.trailer": "<PERSON><PERSON><PERSON>", "mco.template.default.name": "Maailma mall", "mco.template.info.tooltip": "Väljaandja koduleht", "mco.template.name": "Mall", "mco.template.select.failure": "Me ei saanud hankida selle kategooria sisunimekirja. Pa<PERSON>n kontrolli oma internetiühendust või proovi hiljem uuesti.", "mco.template.select.narrate.authors": "Autorid: %s", "mco.template.select.narrate.version": "versioon %s", "mco.template.select.none": "Ups, tundub, et see sisukategooria on hetkel tühi.\n<PERSON><PERSON><PERSON> vaata uue sisu saamiseks hiljem uuesti või kui sa oled looja,\n%s.", "mco.template.select.none.linkTitle": "kaalu ise millegi saatmist", "mco.template.title": "<PERSON><PERSON><PERSON> mallid", "mco.template.title.minigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.trailer.tooltip": "Mänguma<PERSON><PERSON> treiler", "mco.terms.buttons.agree": "N<PERSON>ust<PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON> nõustu", "mco.terms.sentence.1": "<PERSON>õustun Minecraft Realmsi", "mco.terms.sentence.2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.title": "<PERSON><PERSON> ka<PERSON>", "mco.time.daysAgo": "%1$s päev(a) tagasi", "mco.time.hoursAgo": "%1$s tund(i) tagasi", "mco.time.minutesAgo": "%1$s minut(it) tagasi", "mco.time.now": "praegu", "mco.time.secondsAgo": "%1$s sekund(it) tagasi", "mco.trial.message.line1": "<PERSON><PERSON> end<PERSON>?", "mco.trial.message.line2": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>, et näha rohkem infot!", "mco.upload.button.name": "<PERSON><PERSON>", "mco.upload.cancelled": "Üleslaadimine tü<PERSON>tati", "mco.upload.close.failure": "<PERSON>i saanud su <PERSON>i su<PERSON>, palun proovi hiljem u<PERSON>i", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Üleslaadimine nurjus! (%s)", "mco.upload.failed.too_big.description": "<PERSON><PERSON><PERSON> ma<PERSON> on liiga suur. Maksimaalne lubatud suurus on %s.", "mco.upload.failed.too_big.title": "Maailm liiga suur", "mco.upload.hardcore": "Halastamatu režiimiga maailmu pole võimalik üles laadida!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON> ma<PERSON> et<PERSON>", "mco.upload.select.world.none": "Üksikmängu maailmu ei leitud!", "mco.upload.select.world.subtitle": "<PERSON><PERSON><PERSON> vali <PERSON> ma<PERSON>, mida <PERSON><PERSON>", "mco.upload.select.world.title": "<PERSON><PERSON> ma<PERSON>", "mco.upload.size.failure.line1": "\"%s\" on liiga suur!", "mco.upload.size.failure.line2": "See on %s. <PERSON><PERSON> lubatud suurus on %s.", "mco.upload.uploading": "\"%s\" üleslaadimine", "mco.upload.verifying": "<PERSON><PERSON> ma<PERSON>ma kontroll<PERSON>", "mco.version": "Versioon: %s", "mco.warning": "<PERSON><PERSON><PERSON>!", "mco.worldSlot.minigame": "Minimäng", "menu.custom_options": "<PERSON><PERSON><PERSON><PERSON> valikud...", "menu.custom_options.title": "<PERSON><PERSON><PERSON><PERSON> valikud", "menu.custom_options.tooltip": "Märkus: <PERSON><PERSON><PERSON><PERSON> valikuid pakuvad kolmanda osapoole serverid ja/või sisu.\nOle ettevaatlik!", "menu.custom_screen_info.button_narration": "See on kohandatud kuva. Loe lisaks.", "menu.custom_screen_info.contents": "Selle kuva sisu juhivad kolmandate osapoolte serverid ja mänguma<PERSON>mad, mis ei kuulu Mojang Studiosile ega Microsoftile, pole nende poolt hallatud ega üle vaadatud.\n\nOle ettevaatlik! Kontrolli linke hoolikalt ja ära kunagi avalda enda isiklikku teavet, sealhulgas sisselogimisandmeid.\n\nKui see kuva takistab sul mängimist, võid alloleva nupuga serverist lah<PERSON><PERSON>.", "menu.custom_screen_info.disconnect": "Ko<PERSON>atud kuva tagasil<PERSON>", "menu.custom_screen_info.title": "Märkus kohandatud kuvade kohta", "menu.custom_screen_info.tooltip": "See on kohandatud kuva. <PERSON><PERSON><PERSON><PERSON><PERSON>, et lugeda lisaks.", "menu.disconnect": "<PERSON><PERSON><PERSON>", "menu.feedback": "Tagasiside...", "menu.feedback.title": "Tagasiside", "menu.game": "Mängumenüü", "menu.modded": " (moditud)", "menu.multiplayer": "Mitmikmäng", "menu.online": "Minecraft Realms", "menu.options": "Val<PERSON><PERSON>...", "menu.paused": "<PERSON><PERSON><PERSON> pausil", "menu.playdemo": "<PERSON><PERSON><PERSON><PERSON>", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Tekkeala ettevalmistamine: %s%%", "menu.quick_actions": "Ki<PERSON><PERSON><PERSON>ud...", "menu.quick_actions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.quit": "Lahku mängust", "menu.reportBugs": "Teata vigadest", "menu.resetdemo": "<PERSON><PERSON><PERSON>i", "menu.returnToGame": "Tagasi mängu", "menu.returnToMenu": "Salvesta ja naase <PERSON>", "menu.savingChunks": "Kamakate salvestamine", "menu.savingLevel": "<PERSON><PERSON><PERSON> salves<PERSON>", "menu.sendFeedback": "<PERSON>", "menu.server_links": "Serverilingid...", "menu.server_links.title": "Server<PERSON><PERSON>", "menu.shareToLan": "Ava LAN-ile", "menu.singleplayer": "Üksikmäng", "menu.working": "Töötamine...", "merchant.deprecated": "Külaelanikud varustavad end kuni 2x päevas.", "merchant.level.1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "sell", "merchant.level.4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.5": "meister", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "<PERSON><PERSON> hü<PERSON>ks vajuta nuppu %1$s", "multiplayer.applyingPack": "Ressursip<PERSON> rakendamine", "multiplayer.confirm_command.parse_errors": "Proovid käivitada tundmatut või sobimatut käsklust.\nKas oled kindel?\nKäsklus: %s", "multiplayer.confirm_command.permissions_required": "Üritad kasutada käsklust, mis nõuab kõrgendatud õigusi.\nSee võib mängu negatiivselt mõjutada.\nKas oled kindel?\nKäsklus: %s", "multiplayer.confirm_command.title": "Kinnita käskluse käivitamist", "multiplayer.disconnect.authservers_down": "Autentimisserverid on maas. <PERSON><PERSON><PERSON><PERSON>, palun proovi hiljem uuesti!", "multiplayer.disconnect.bad_chat_index": "Serverist tuvastati vahelejäetud või ümberjärjestatud vestlussõnum", "multiplayer.disconnect.banned": "Sa oled sellest serverist blokeeri<PERSON>d", "multiplayer.disconnect.banned.expiration": "\nSinu blokeering eemaldatakse %s", "multiplayer.disconnect.banned.reason": "Sa oled sellest serverist blokeeritud.\nPõhjus: %s", "multiplayer.disconnect.banned_ip.expiration": "\nSinu blokeering eemaldatakse %s", "multiplayer.disconnect.banned_ip.reason": "Sinu IP-aadress on sellest serverist blokeeritud.\nPõhjus: %s", "multiplayer.disconnect.chat_validation_failed": "Vestlussõnumi valideerim<PERSON> e<PERSON>", "multiplayer.disconnect.duplicate_login": "Sa logisid sisse teisest kohast", "multiplayer.disconnect.expired_public_key": "<PERSON><PERSON><PERSON> a<PERSON> v<PERSON> on aegunud. <PERSON><PERSON><PERSON>, et sinu sü<PERSON> on ajakohane ning proovi mäng taaskäivitada.", "multiplayer.disconnect.flying": "Lendamine ei ole selles serveris lubatud", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON>", "multiplayer.disconnect.idling": "Sa olid liiga kaua eemal!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.incompatible": "Ühildumatu mäng! Palun kasuta versiooni %s", "multiplayer.disconnect.invalid_entity_attacked": "Üritasid rünnata sobimatut olemit", "multiplayer.disconnect.invalid_packet": "Server sa<PERSON><PERSON> keh<PERSON> paketi", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "multiplayer.disconnect.invalid_player_movement": "Sobimatu mängija liikumise pakett vastuvõetud", "multiplayer.disconnect.invalid_public_key_signature": "Prof<PERSON><PERSON> a<PERSON>ikul võtmel on sobimatu signatuur.\nProovi oma mäng taaskäivitada.", "multiplayer.disconnect.invalid_public_key_signature.new": "Prof<PERSON><PERSON> a<PERSON>ikul võtmel on sobimatu signatuur.\nProovi oma mäng taaskäivitada.", "multiplayer.disconnect.invalid_vehicle_movement": "Sobimatu sõiduki liikumise pakett vastuvõetud", "multiplayer.disconnect.ip_banned": "Sinu IP on sellest serverist blokeeritud", "multiplayer.disconnect.kicked": "Operaatori poolt välja visatud", "multiplayer.disconnect.missing_tags": "Serverist v<PERSON><PERSON> vastu puudulik siltide kogu.\nPalun kontakteeru serveri operaatoriga.", "multiplayer.disconnect.name_taken": "See nimi on juba võetud", "multiplayer.disconnect.not_whitelisted": "Sa ei ole selle serveri valges nimekirjas!", "multiplayer.disconnect.out_of_order_chat": "Vastu võeti vales järjekorras vestluspakett. Kas su süsteemikell muutus?", "multiplayer.disconnect.outdated_client": "Ühildumatu mäng! Palun kasuta versiooni %s", "multiplayer.disconnect.outdated_server": "Ühildumatu mäng! Palun kasuta versiooni %s", "multiplayer.disconnect.server_full": "Server on täis!", "multiplayer.disconnect.server_shutdown": "Server suletud", "multiplayer.disconnect.slow_login": "Sisselogimine võttis liiga kaua aega", "multiplayer.disconnect.too_many_pending_chats": "Liiga palju mittenähtud vestlussõnumeid", "multiplayer.disconnect.transfers_disabled": "Server ei v<PERSON><PERSON>u", "multiplayer.disconnect.unexpected_query_response": "<PERSON><PERSON><PERSON><PERSON> kohanda<PERSON> and<PERSON> m<PERSON> poolt", "multiplayer.disconnect.unsigned_chat": "Vastu võetud puuduva või sobimatu signatuuriga vestluspakett.", "multiplayer.disconnect.unverified_username": "<PERSON><PERSON><PERSON><PERSON><PERSON> kinnitamine ebaõnnestus!", "multiplayer.downloadingStats": "Statist<PERSON> toomine...", "multiplayer.downloadingTerrain": "Ma<PERSON><PERSON><PERSON> la<PERSON>...", "multiplayer.lan.server_found": "Leiti uus server: %s", "multiplayer.message_not_delivered": "Sõnumit ei saa edastada, kontrolli serverilogisid: %s", "multiplayer.player.joined": "%s liitus mä<PERSON>uga", "multiplayer.player.joined.renamed": "%s (varem tuntud kui %s) liitus mänguga", "multiplayer.player.left": "%s lahkus mängust", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Võrgus olevad mängijad: %s", "multiplayer.requiredTexturePrompt.disconnect": "Server n<PERSON><PERSON><PERSON> kohandatud ressursipakki", "multiplayer.requiredTexturePrompt.line1": "See server k<PERSON><PERSON><PERSON> kasutada kohandatud ressursipakki.", "multiplayer.requiredTexturePrompt.line2": "<PERSON><PERSON><PERSON> kohandatud ressursipakist keeldumine eemaldab su sellest serverist.", "multiplayer.socialInteractions.not_available": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on saadaval vaid mitmikmängu maailmades", "multiplayer.status.and_more": "...ja veel %s...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Serveriga ei saa ühenduda", "multiplayer.status.cannot_resolve": "Hostinime ei saa lahendada", "multiplayer.status.finished": "Lõpetatud", "multiplayer.status.incompatible": "<PERSON><PERSON><PERSON>matu versioon!", "multiplayer.status.motd.narration": "Päevasõnum: %s", "multiplayer.status.no_connection": "(<PERSON><PERSON><PERSON> puudub)", "multiplayer.status.old": "vana", "multiplayer.status.online": "Võrgus", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisekundit", "multiplayer.status.pinging": "Pingimine...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "Võrgus on %s mängijat koguarvust %s", "multiplayer.status.quitting": "Väljun", "multiplayer.status.request_handled": "Olekutaotlus on käsitletud", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Taotlemata o<PERSON>", "multiplayer.status.version.narration": "Serveri versioon: %s", "multiplayer.stopSleeping": "<PERSON><PERSON><PERSON> voodist", "multiplayer.texturePrompt.failure.line1": "<PERSON>i ressursipakki ei saadud rakendada", "multiplayer.texturePrompt.failure.line2": "Kohandatud ressurssi nõudev funktsionaalsus ei pruugi oodatult töötada", "multiplayer.texturePrompt.line1": "See server soov<PERSON><PERSON> kasutada kohandatud ressursipakki.", "multiplayer.texturePrompt.line2": "Kas soovid selle automaagiliselt alla laadida ja paigaldada?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nSõnum serverilt:\n%s", "multiplayer.title": "Mängi mitmikmängu", "multiplayer.unsecureserver.toast": "Selles serveris saadetud sõnumid võivad olla muudetud ning ei pruugi originaalsõnumit kajastada", "multiplayer.unsecureserver.toast.title": "Vestlussõnumeid ei saa kinnitada", "multiplayerWarning.check": "<PERSON>ra kuva seda ekraani uuesti", "multiplayerWarning.header": "Hoiatus: kolmanda osapoolega võrgus mäng<PERSON>ine", "multiplayerWarning.message": "Hoiatus: <PERSON><PERSON><PERSON>umäng<PERSON> pakuvad kolmanda osapoole serverid, mida ei oma, halda ega jälgi Mojang Studios või Microsoft. Võrgumängus võid sa näha töötlemata vestlussõnumeid või teist sorti kasutaja-loodud sisu, mis ei pruugi kõigile sobilik olla.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Nupp: %s", "narration.button.usage.focused": "Aktiveerimiseks vajuta Enter", "narration.button.usage.hovered": "Aktiveerimiseks tee vasakklõps", "narration.checkbox": "Märkeruut: %s", "narration.checkbox.usage.focused": "Lülitamiseks vajuta Enter", "narration.checkbox.usage.hovered": "Lülitamiseks tee vasakklõps", "narration.component_list.usage": "Vajuta järgmise üksuse juurde liikumiseks nuppu Tab", "narration.cycle_button.usage.focused": "%s peale lülitamiseks vajuta Enter", "narration.cycle_button.usage.hovered": "%s peale lülitamiseks tee vasakklõps", "narration.edit_box": "Tekstiväli: %s", "narration.item": "Ese: %s", "narration.recipe": "%s, retsept", "narration.recipe.usage": "Valimiseks tee vasakklõps", "narration.recipe.usage.more": "Paremklõpsa rohkemate retseptide nägemiseks", "narration.selection.usage": "Vajuta järgmise üksuse juurde liikumiseks ülesnuppu ja allanuppu", "narration.slider.usage.focused": "Vajuta väärtuse muutmiseks vasakut või paremat klaviatuurinuppu", "narration.slider.usage.hovered": "Sikuta väärtuse muutmiseks hooba", "narration.suggestion": "Valitud %s. soovitus %s hulgast: %s", "narration.suggestion.tooltip": "Valitud %s. soovitus %s hulgast: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Järgmise soovituse juurde liikumiseks vajuta Tab", "narration.suggestion.usage.cycle.hidable": "Järgmise soovituse juurde liikumiseks vajuta Tab, soovitustest lahkumiseks vajuta Escape", "narration.suggestion.usage.fill.fixed": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> vajuta <PERSON>", "narration.suggestion.usage.fill.hidable": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> vajuta Tab, soovitustest lahkumiseks vajuta Escape", "narration.tab_navigation.usage": "Vahekaartide vahel lülitamiseks vajuta Ctrl ja Tab", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Raskusastme lukk", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Lukust lahti", "narrator.button.language": "<PERSON><PERSON>", "narrator.controls.bound": "%s nupuks on %s", "narrator.controls.reset": "Lähtesta %s nupp", "narrator.controls.unbound": "%s nupp on määramata", "narrator.joining": "Liitumine", "narrator.loading": "Laadimine: %s", "narrator.loading.done": "Val<PERSON>", "narrator.position.list": "Valitud %s. nimekirja rida %s seast", "narrator.position.object_list": "Valitud %s. reaelement %s seast", "narrator.position.screen": "%s. ekraanielement %s seast", "narrator.position.tab": "Valitud %s. v<PERSON><PERSON><PERSON> koguarvust %s", "narrator.ready_to_play": "Mängimiseks valmis", "narrator.screen.title": "Avamenüü", "narrator.screen.usage": "Elemendi valimiseks kasuta hiireku<PERSON>orit või nuppu Tab", "narrator.select": "Valitud: %s", "narrator.select.world": "Valitud %s, viimati mängitud %s, %s, %s, versioon: %s", "narrator.select.world_info": "Valitud %s, viimati mängitud %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON><PERSON>", "narrator.toast.enabled": "Jutus<PERSON>ja luba<PERSON>", "optimizeWorld.confirm.description": "See üritab sinu maailma optimeerida, veendudes, et kõik andmed on salvestatud kõige viimasesse mänguformaati. Sõltuvalt sinu maailmast võib see võtta väga kaua aega. <PERSON><PERSON> teht<PERSON>, võib su maailm mängida kiiremini, ent ei pruugi ühilduda mängu vanemate versioonidega. Kas soovid kindlasti jätkata?", "optimizeWorld.confirm.proceed": "<PERSON>o varundus ja op<PERSON>eri", "optimizeWorld.confirm.title": "<PERSON><PERSON><PERSON>", "optimizeWorld.info.converted": "Uuendatud kamakaid: %s", "optimizeWorld.info.skipped": "Vah<PERSON>j<PERSON><PERSON><PERSON> kamakaid: %s", "optimizeWorld.info.total": "Kamakaid kokku: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Kamakate loendamine...", "optimizeWorld.stage.failed": "Ebaõnnestus! :(", "optimizeWorld.stage.finished": "Lõpetamine...", "optimizeWorld.stage.finished.chunks": "Kamakate uuendamise lõpetamine...", "optimizeWorld.stage.finished.entities": "Olemite uuendamise lõpetamine...", "optimizeWorld.stage.finished.poi": "Huviobjektide uuendamise lõpetamine...", "optimizeWorld.stage.upgrading": "Kõikide kamakate uuendamine...", "optimizeWorld.stage.upgrading.chunks": "Kõikide kamakate uuendamine...", "optimizeWorld.stage.upgrading.entities": "Kõikide olemite uuendamine...", "optimizeWorld.stage.upgrading.poi": "Kõikide huvipunktide uuendamine...", "optimizeWorld.title": "Maailma \"%s\" optimeerimine", "options.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seaded...", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "<PERSON><PERSON><PERSON> k<PERSON> re<PERSON><PERSON> pole saadaval", "options.accessibility.high_contrast.tooltip": "Täiustab liidese elementide kontrasti", "options.accessibility.high_contrast_block_outline": "<PERSON><PERSON><PERSON> kontrastiga plokipiir<PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "<PERSON><PERSON><PERSON><PERSON> ploki pii<PERSON><PERSON><PERSON> k<PERSON>.", "options.accessibility.link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON><PERSON> ta<PERSON>", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON>b menüü taustade hägus<PERSON>.", "options.accessibility.narrator_hotkey": "Jutustaja k<PERSON>rklahv", "options.accessibility.narrator_hotkey.mac.tooltip": "Võimaldab klahvikombinatsiooniga \"Cmd+B\" jutustaja sisse- ja väljalülitamist", "options.accessibility.narrator_hotkey.tooltip": "Võimaldab klahvikombinatsiooniga \"Ctrl+B\" jutustaja sisse- ja väljalülitamist", "options.accessibility.panorama_speed": "Panoraami kerimiskiirus", "options.accessibility.text_background": "<PERSON><PERSON><PERSON> taust", "options.accessibility.text_background.chat": "vestluses", "options.accessibility.text_background.everywhere": "k<PERSON>ik<PERSON>l", "options.accessibility.text_background_opacity": "<PERSON><PERSON><PERSON> tausta n<PERSON>", "options.accessibility.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seaded", "options.allowServerListing": "Serveriloendid", "options.allowServerListing.tooltip": "Serverid võivad kuvada võrgus olevate mängijate nimekirja nende avaliku olekuna.\nSelle valiku keelamisel ei kuvata sellistes nimekirjades sinu nime.", "options.ao": "<PERSON><PERSON><PERSON> valgustus", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "minimaalne", "options.ao.off": "VÄLJAS", "options.attack.crosshair": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attack.hotbar": "plokiribal", "options.attackIndicator": "Ründeindikaator", "options.audioDevice": "<PERSON>de", "options.audioDevice.default": "s<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "options.autoJump": "Automaatne hüpe", "options.autoSuggestCommands": "Käskluste soovitused", "options.autosaveIndicator": "Autom.salv. indikaator", "options.biomeBlendRadius": "B<PERSON>omisul<PERSON><PERSON>", "options.biomeBlendRadius.1": "VÄLJAS (kiireim)", "options.biomeBlendRadius.11": "11x11 (ekstreemne)", "options.biomeBlendRadius.13": "13x13 (eputaja)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (kiire)", "options.biomeBlendRadius.5": "5x5 (tavaline)", "options.biomeBlendRadius.7": "7x7 (kõrge)", "options.biomeBlendRadius.9": "9x9 (v<PERSON><PERSON> k<PERSON>)", "options.chat": "Vestluse seaded...", "options.chat.color": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.delay": "Vestluse viide: %s sekundit", "options.chat.delay_none": "Vestluse viide: puudub", "options.chat.height.focused": "Fookustatud kõrgus", "options.chat.height.unfocused": "Fookustamata kõrgus", "options.chat.line_spacing": "Ridade vahe", "options.chat.links": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.links.prompt": "<PERSON><PERSON><PERSON> linkide korral", "options.chat.opacity": "Vestluse teksti nähtavus", "options.chat.scale": "Vestluse teksti suurus", "options.chat.title": "Vestluse seaded", "options.chat.visibility": "Vestlus", "options.chat.visibility.full": "näidatud", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON> käsklused", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s kamakat", "options.clouds.fancy": "uhked", "options.clouds.fast": "kiired", "options.controls": "Ju<PERSON><PERSON><PERSON>d...", "options.credits_and_attribution": "<PERSON><PERSON><PERSON>d ja omistamine...", "options.damageTiltStrength": "Vigastuse kalle", "options.damageTiltStrength.tooltip": "Haiget saamisel vaatevälja raputuse tugevus.", "options.darkMojangStudiosBackgroundColor": "Mustvalge logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON>b <PERSON>g Studios'i laadimiskuva tausta mustaks.", "options.darknessEffectScale": "Tumeduse pulseering", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON> tumeduse efekti pulseerimise ulatust, kui valvur või sculk-kisendaja selle sulle annab.", "options.difficulty": "Raskusaste", "options.difficulty.easy": "kerge", "options.difficulty.easy.info": "Vaenulikud elukad te<PERSON>, aga p<PERSON>hjustavad vä<PERSON> kahju. Nälg kasvab ning kurnab tervise 5 südame peale.", "options.difficulty.hard": "raske", "options.difficulty.hard.info": "Vaenulikud elukad tekivad ning põhjustavad rohkem kahju. Nälg kasvab ning kurnab tervise täiesti nulli.", "options.difficulty.hardcore": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal": "keskmine", "options.difficulty.normal.info": "Vaenulikud elukad tekivad ning põhjustavad standardset kahju. Nälg kasvab ning kurnab tervise poole südame peale.", "options.difficulty.online": "<PERSON><PERSON>", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Vaenulikke koletisi ei teki, tekivad vaid mõned neutraalsed. Nälg ei kasva ning tervis tuleb aja jook<PERSON> tagasi.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON> heli", "options.directionalAudio.off.tooltip": "<PERSON><PERSON><PERSON><PERSON>.", "options.directionalAudio.on.tooltip": "Kasutab HRTF-p<PERSON><PERSON><PERSON>, et parandada 3D-heli simulat<PERSON>. Nõuab HRTF-iga ühilduvat heliriistvara ning seda on kõige parem kasutada kõrvaklappidega.", "options.discrete_mouse_scroll": "Diskreetne kerimine", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.entityShadows": "Olemite varjud", "options.font": "Fondiseaded...", "options.font.title": "Fondiseaded", "options.forceUnicodeFont": "Unicode-font", "options.fov": "Vaateväli", "options.fov.max": "Quake'i proff", "options.fov.min": "keskmine", "options.fovEffectScale": "Vaatevälja efektid", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON>, kui palju saab vaatev<PERSON>li mängumõjudega muutuda.", "options.framerate": "%s k/s", "options.framerateLimit": "<PERSON><PERSON><PERSON>", "options.framerateLimit.max": "p<PERSON><PERSON><PERSON>", "options.fullscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.current": "praegune", "options.fullscreen.entry": "%sx%s@%s (%s-bit)", "options.fullscreen.resolution": "Täisekraani resolutsioon", "options.fullscreen.unavailable": "<PERSON><PERSON> pole saadaval", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "v<PERSON><PERSON><PERSON>", "options.gamma.max": "hele", "options.gamma.min": "tu<PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON> kiirus", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON>, kui kiiresti liigub visuaalne sära üle loitsitud esemete.", "options.glintStrength": "<PERSON><PERSON><PERSON>", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> on visuaalne sära loitsitud esemetel.", "options.graphics": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fabulous": "vapustav!", "options.graphics.fabulous.tooltip": "Graafikasäte \"%s\" ka<PERSON><PERSON><PERSON>, et joonistada poolläbipaistvate plokkide ja vee taha ilma, pilvi ja osakesi.\nSee võib jõudlust oluliselt mõjutada kaasaskantavate seadmete ja 4K-ekraanide puhul.", "options.graphics.fancy": "uhke", "options.graphics.fancy.tooltip": "Uh<PERSON> graafika tasakaalustab jõudlust ja kvaliteeti enamus masinate jaoks.\nIlm, pilved ja osakesed ei pruugi olla poolläbipaistvate plokkide või vee tagant nähtavad.", "options.graphics.fast": "kiire", "options.graphics.fast.tooltip": "Kiire graafika piirab nä<PERSON>va vihma ja lume hulka.\nLäbipaistvusefektid on mitmete plokkide jaoks keelatud, seal<PERSON><PERSON> puulehed.", "options.graphics.warning.accept": "Jätka ilma toeta", "options.graphics.warning.cancel": "Vii mind tagasi", "options.graphics.warning.message": "Sinu graafikaseade on tuvastatud kui graafikasätte \"%s\" jaoks ebasobiv.\n\nSa võid seda ignoreerida ja jätkata, kuid sätte \"%s\" kasutamise jätkamisel ei pakuta sinu seadmele tuge.", "options.graphics.warning.renderer": "<PERSON><PERSON><PERSON><PERSON>: [%s]", "options.graphics.warning.title": "Mittetoetatud graafikaseade", "options.graphics.warning.vendor": "<PERSON><PERSON><PERSON><PERSON>: [%s]", "options.graphics.warning.version": "Tuvastatud OpenGL versioon: [%s]", "options.guiScale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> suurus", "options.guiScale.auto": "autom.", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON> vilgutamise keeld", "options.hideLightningFlashes.tooltip": "Väld<PERSON> välku lüües taeva vilgutamist. Välgunooled jäävad siiski nähtavaks.", "options.hideMatchedNames": "<PERSON><PERSON> j<PERSON>rgi peit<PERSON>", "options.hideMatchedNames.tooltip": "<PERSON><PERSON><PERSON> osapoole serverid võivad saata mittestandardseid vestlussõnumeid.\nSelle valiku lubamisel otsitakse vestluses peidetud mängijaid sõnumisaatja nimede kaudu.", "options.hideSplashTexts": "Peida naljatekstid", "options.hideSplashTexts.tooltip": "Peidab avamenüüst kollased naljatekstid.", "options.inactivityFpsLimit": "<PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.afk": "eemal o<PERSON>", "options.inactivityFpsLimit.afk.tooltip": "<PERSON><PERSON><PERSON> ka<PERSON>use 30 kaadrile sekundis, kui mängija ei tegutse mängus kauem kui minut aega. Pärast 9 minutit piirab 10 kaadrile sekundis.", "options.inactivityFpsLimit.minimized": "minimeeritult", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON><PERSON><PERSON> vaid siis, kui mä<PERSON> on minimeeritud.", "options.invertMouse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.japaneseGlyphVariants": "Jaapani glüüfivariandid", "options.japaneseGlyphVariants.tooltip": "Kasutab vaikefondis CJK-märkide jaapani variante.", "options.key.hold": "hoidmine", "options.key.toggle": "<PERSON><PERSON><PERSON><PERSON>", "options.language": "Keel...", "options.language.title": "<PERSON><PERSON>", "options.languageAccuracyWarning": "(T<PERSON><PERSON><PERSON> ei pruugi olla 100%% täpsed)", "options.languageWarning": "Tõlked ei pruugi olla 100%% täpsed", "options.mainHand": "<PERSON><PERSON><PERSON> k<PERSON>", "options.mainHand.left": "vasak", "options.mainHand.right": "parem", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON><PERSON> tasemeid", "options.modelPart.cape": "Keep", "options.modelPart.hat": "<PERSON><PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Vasak püksisäär", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON>", "options.mouseWheelSensitivity": "Kerimistundlikkus", "options.mouse_settings": "<PERSON><PERSON> seaded...", "options.mouse_settings.title": "<PERSON><PERSON> seaded", "options.multiplayer.title": "Mitmikmängu seaded...", "options.multiplier": "%sx", "options.music_frequency": "<PERSON><PERSON><PERSON> sagedus", "options.music_frequency.constant": "pidev", "options.music_frequency.default": "v<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "tihe", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON><PERSON>, kui tihti mäng<PERSON> olles muusikat esitatakse.", "options.narrator": "Jutustaja", "options.narrator.all": "jut<PERSON><PERSON> k<PERSON>", "options.narrator.chat": "jutustab vest<PERSON>t", "options.narrator.notavailable": "pole saadaval", "options.narrator.off": "VÄLJAS", "options.narrator.system": "jut<PERSON><PERSON>", "options.notifications.display_time": "Teavituste aeg", "options.notifications.display_time.tooltip": "Mõjutab kõikide teatiste kuvamisaega ekraanil.", "options.off": "VÄLJAS", "options.off.composed": "%s: VÄLJAS", "options.on": "SEES", "options.on.composed": "%s: SEES", "options.online": "Võrgus...", "options.online.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.onlyShowSecureChat": "Vaid turvaline vestlus", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON> teistelt mängijatelt vaid sõ<PERSON>id, mille puhul saab veenduda, et need on selle mängija poolt saadetud muutmata kujul.", "options.operatorItemsTab": "Op.esemete v<PERSON><PERSON>", "options.particles": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.decreased": "vähem", "options.particles.minimal": "minimaalselt", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "osal. blo<PERSON>v", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Mõned kamakal tehtavad tegevused taaskompileerivad selle koheselt. Selle hulka kuuluvad plokkide asetamine ja lõhkumine.", "options.prioritizeChunkUpdates.nearby": "täiel. blokeeriv", "options.prioritizeChunkUpdates.nearby.tooltip": "Lähedalasuvad kamakad kompileeritakse alati koheselt. See võib plokkide lõhkumisel või asetamisel mõjutada mängu jõudlust.", "options.prioritizeChunkUpdates.none": "lõimeline", "options.prioritizeChunkUpdates.none.tooltip": "Lähedalasuvad kamakad kompileeritakse paralleellõimedes. See võib plokkide lõhkumisel põhjustada lühiajalisi visuaalseid auke.", "options.rawMouseInput": "Toorsisend", "options.realmsNotifications": "<PERSON><PERSON> uudised ja kutsed", "options.realmsNotifications.tooltip": "<PERSON><PERSON>-uudiseid ja kutseid ning kuvab nende ikooni Realmsi nupul.", "options.reducedDebugInfo": "Vähendatud silumisinfo", "options.renderClouds": "Pilved", "options.renderCloudsDistance": "Pilvekaugus", "options.renderDistance": "Nähtavuskaugus", "options.resourcepack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "options.rotateWithMinecart": "Kaevanduskäruga pööramine", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON><PERSON>, kas mäng<PERSON> vaade peaks pöörama koos pöörava kaevanduskäruga. <PERSON><PERSON><PERSON> vaid ma<PERSON>made<PERSON>, kus on lubatud katsetusjärgus valik \"Kaevanduskärude parendused\".", "options.screenEffectScale": "Moonutusefektid", "options.screenEffectScale.tooltip": "Iivelduse mõju ja Netheri portaali ekraanimoonutuste tugevus.\nMadalatel väärtustel asendatakse iiveldus roh<PERSON>.", "options.sensitivity": "Tund<PERSON><PERSON>", "options.sensitivity.max": "HÜPERKIIRUS!!!", "options.sensitivity.min": "*haigutus*", "options.showNowPlayingToast": "<PERSON><PERSON><PERSON> h<PERSON>", "options.showNowPlayingToast.tooltip": "<PERSON><PERSON><PERSON> hüpikut uue laulu algamisel. Sama hüpikut kuvatakse püsivalt mängu paus<PERSON>, kuni laul mängib.", "options.showSubtitles": "Subtiitrid", "options.simulationDistance": "Simuleerimiskaugus", "options.skinCustomisation": "<PERSON><PERSON><PERSON>use kohandamine...", "options.skinCustomisation.title": "Vä<PERSON>use kohandamine", "options.sounds": "<PERSON><PERSON><PERSON> ja helid...", "options.sounds.title": "<PERSON><PERSON><PERSON>- ja <PERSON>", "options.telemetry": "<PERSON><PERSON><PERSON><PERSON><PERSON> andmed...", "options.telemetry.button": "<PERSON><PERSON><PERSON> kogumine", "options.telemetry.button.tooltip": "\"%s\" sisaldab vaid nõutud andmeid.\n\"%s\" sisaldab nii valikulisi kui ka nõutud andmeid.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> on keelatud.", "options.telemetry.state.all": "täielik", "options.telemetry.state.minimal": "minimaalne", "options.telemetry.state.none": "puudub", "options.title": "<PERSON><PERSON><PERSON>", "options.touchscreen": "Puutetundlik režiim", "options.video": "<PERSON><PERSON><PERSON><PERSON><PERSON>ded...", "options.videoTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.viewBobbing": "<PERSON><PERSON><PERSON>", "options.visible": "näidatud", "options.vsync": "VSync", "outOfMemory.message": "Minecrafti kasutatav mälu on täis saanud.\n\nSee võib olla põhjustatud mänguveast või Java Virtual Machine'i ebapiisavast mälueraldusest.\n\nMaailma korrumpeerumise vältimiseks väljutasime su mängust. Proovisime mälu piisavalt vabastada, et võimaldada sul avamenüüsse naasta ning edasi mängida, kuid on võimalik, et see ei aidanud.\n\nKui näed seda sõnumit uuesti, palun taaskäivita mäng.", "outOfMemory.title": "<PERSON><PERSON><PERSON> on täis!", "pack.available.title": "<PERSON><PERSON><PERSON>", "pack.copyFailure": "Pak<PERSON>de kopeerimine e<PERSON>", "pack.dropConfirm": "<PERSON><PERSON> soovid järg<PERSON><PERSON>d pakid <PERSON>i lisada?", "pack.dropInfo": "<PERSON><PERSON><PERSON> pakkide lisamiseks nende failid siia aknasse", "pack.dropRejected.message": "Järgnevad kirjed ei olnud kehtivad pakid ning neid ei kopeeritud:\n %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kirjed", "pack.folderInfo": "(<PERSON><PERSON> paki<PERSON>ili<PERSON>)", "pack.incompatible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.incompatible.confirm.new": "See pakk on tehtud Minecrafti uuemale versioonile ja ei pruugi enam korralik<PERSON> töötada.", "pack.incompatible.confirm.old": "See pakk on tehtud Minecrafti vanemale versioonile ja ei pruugi enam korra<PERSON> töötada.", "pack.incompatible.confirm.title": "Kas soovid kindlalt seda pakki laadida?", "pack.incompatible.new": "(<PERSON><PERSON><PERSON> uuemale versioonile)", "pack.incompatible.old": "(<PERSON><PERSON><PERSON> vanemale versioonile)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON> pakkide kaust", "pack.selected.title": "<PERSON><PERSON><PERSON>", "pack.source.builtin": "sisseehitatud", "pack.source.feature": "t<PERSON>iendus", "pack.source.local": "kohalik", "pack.source.server": "server", "pack.source.world": "ma<PERSON>m", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Tagahoov", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Barokk", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Sihtmärk edukalt pommitatud", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON><PERSON> kol<PERSON>", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Muutumine", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON><PERSON>, härra Courbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Maa", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Lõpubos<PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Sõnajalg", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Võitlejad", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Le<PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Alandlik", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "<PERSON><PERSON><PERSON> kolme pepperoniga", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Tuletikk", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatiivne", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Öökudrunid", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Läbipääs", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Seastseen", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradiisipuu", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Tiik", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON><PERSON>", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Preeriasõit", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Mereäärne", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON> m<PERSON>u pundar", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON><PERSON> ja roosid", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON><PERSON> on seatud", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Päevalilled", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Looded", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "<PERSON><PERSON><PERSON> paki<PERSON>d", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Juhuslik variant", "parsing.bool.expected": "Oodati tõeväärtust", "parsing.bool.invalid": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, o<PERSON><PERSON> \"true\" või \"false\" aga leiti \"%s\"", "parsing.double.expected": "<PERSON><PERSON><PERSON> double-arvu", "parsing.double.invalid": "Sobimatu double-arv \"%s\"", "parsing.expected": "Oodati \"%s\"", "parsing.float.expected": "<PERSON><PERSON><PERSON>", "parsing.float.invalid": "Sobimatu ujukomaarv \"%s\"", "parsing.int.expected": "<PERSON><PERSON><PERSON>", "parsing.int.invalid": "Sobimatu täisarv \"%s\"", "parsing.long.expected": "<PERSON><PERSON><PERSON> pikka t<PERSON>", "parsing.long.invalid": "Sobimatu pikk täisarv \"%s\"", "parsing.quote.escape": "Jutumärkides sõnes on sobimatu paojada \"\\%s\"", "parsing.quote.expected.end": "Sulgemata jut<PERSON><PERSON> s<PERSON>", "parsing.quote.expected.start": "Sõne alustamiseks oodati jutumärki", "particle.invalidOptions": "Osakese valikuid ei saa töötleda: %s", "particle.notFound": "Tundmatu osake: %s", "permissions.requires.entity": "<PERSON><PERSON> k<PERSON>e siin käivitamise<PERSON> on vajalik olemi olemasolu", "permissions.requires.player": "<PERSON><PERSON> kä<PERSON>e siin käivitamise<PERSON> on vajalik mängija olemasolu", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Kui tarvitatud:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Tundmatu predikaat: %s", "quickplay.error.invalid_identifier": "Esitatud nimega maailma ei leitud", "quickplay.error.realm_connect": "Realmiga ei sa<PERSON>ud <PERSON>", "quickplay.error.realm_permission": "Selle Realmiga ühendumiseks vajalik luba puudub", "quickplay.error.title": "Kiirmängu käivitamine ebaõnnestus", "realms.configuration.region.australia_east": "Uus-Lõuna-Wales, Austraalia", "realms.configuration.region.australia_southeast": "Victoria, Austraalia", "realms.configuration.region.brazil_south": "Brasiilia", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "Põhja-Carolina, USA", "realms.configuration.region.france_central": "Prantsusmaa", "realms.configuration.region.japan_east": "Ida<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.japan_west": "Lääne-Jaapan", "realms.configuration.region.korea_central": "Lõuna-Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON>", "realms.configuration.region.uae_north": "<PERSON><PERSON><PERSON> (AÜE)", "realms.configuration.region.uk_south": "Lõuna-Inglismaa", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Holland", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automaatne (Realmi omanikule lähim)", "realms.configuration.region_preference.automatic_player": "Automaatne (esimese liituja järgi)", "realms.missing.snapshot.error.text": "Realms ei ole hetkel katsetusversioonides toetatud", "recipe.notFound": "Tundmatu retsept: %s", "recipe.toast.description": "<PERSON><PERSON><PERSON>", "recipe.toast.title": "<PERSON><PERSON>(ed) retsept(id) avatud!", "record.nowPlaying": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>: %s", "recover_world.bug_tracker": "<PERSON>ta veast", "recover_world.button": "<PERSON><PERSON><PERSON>", "recover_world.done.failed": "Viimasest olekust taastumine ebaõnnestus.", "recover_world.done.success": "<PERSON><PERSON><PERSON> oli edukas!", "recover_world.done.title": "Taast<PERSON> valmis", "recover_world.issue.missing_file": "puuduv fail", "recover_world.issue.none": "probleemideta", "recover_world.message": "Maailmakausta \"%s\" lugemisel esinesid järgnevad vead.\nMaailma võib olla võimalik varasemast olekust taastada, või võid selle vea vigade loendisse teatada.", "recover_world.no_fallback": "Taastatav olek pole saadaval", "recover_world.restore": "<PERSON><PERSON>", "recover_world.restoring": "Ma<PERSON>ma taastamise üritamine...", "recover_world.state_entry": "Olek kuupäeval %s: ", "recover_world.state_entry.unknown": "teadmata", "recover_world.title": "<PERSON><PERSON><PERSON> laadi<PERSON> e<PERSON>", "recover_world.warning": "Maailma k<PERSON>kuv<PERSON>tte laadi<PERSON> e<PERSON>", "resourcePack.broken_assets": "TUVASTATI KATKISED FAILID", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON>", "resourcePack.load_fail": "Ressursi uuesti laadimine e<PERSON>nnestus", "resourcePack.programmer_art.name": "Programmeerijakunst", "resourcePack.runtime_failure": "Tuvastati ressursipaki viga", "resourcePack.server.name": "Ma<PERSON><PERSON>p<PERSON><PERSON><PERSON> ressursid", "resourcePack.title": "<PERSON><PERSON>", "resourcePack.vanilla.description": "Minecrafti vaikimisi välimus ja tunnetus", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON>", "resourcepack.downloading": "Ressursipaki allalaadimine", "resourcepack.progress": "Faili allalaadimine (%s MB)...", "resourcepack.requesting": "Taotluse tegemine...", "screenshot.failure": "Kuvatõmmist ei sa<PERSON>ud salves<PERSON>: %s", "screenshot.success": "Kuvat<PERSON><PERSON><PERSON> sa<PERSON> nimega %s", "selectServer.add": "Lisa server", "selectServer.defaultName": "Minecrafti server", "selectServer.delete": "Kustuta", "selectServer.deleteButton": "Kustuta", "selectServer.deleteQuestion": "Kas oled kindel, et soovid selle serveri eemaldada?", "selectServer.deleteWarning": "\"%s\" jääb igaveseks kadunuks! (Väga pikaks ajaks!)", "selectServer.direct": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(p<PERSON><PERSON><PERSON>)", "selectServer.refresh": "Värskenda", "selectServer.select": "Liitu serveriga", "selectWorld.access_failure": "Maailmale ligipääsemine e<PERSON>õnnestus", "selectWorld.allowCommands": "So<PERSON><PERSON>mine", "selectWorld.allowCommands.info": "Käsklused nagu /gamemode, /experience", "selectWorld.allowCommands.new": "Käsklused", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON><PERSON><PERSON> andmed", "selectWorld.backupJoinConfirmButton": "<PERSON>o varundus ja laadi", "selectWorld.backupJoinSkipButton": "Ma tean, mida ma teen!", "selectWorld.backupQuestion.customized": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>mad ei ole enam toetatud", "selectWorld.backupQuestion.downgrade": "Ma<PERSON>ma madaldamine ei ole toe<PERSON>ud", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON><PERSON><PERSON>, mis kasutavad kats<PERSON>j<PERSON>rgus seadeid ei toetata", "selectWorld.backupQuestion.snapshot": "Kas soovid tõesti seda maailma laadida?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON> me ei toeta selles Minecrafti versioonis kohandatud maailmu. Me võime siiski selle maailma laadida ja jätta kõik selliseks nagu on, ent mistahes juurde genereeritud maastik ei ole enam kohandatud. Vabandame ebamugavuste pärast!", "selectWorld.backupWarning.downgrade": "Seda maailma mängiti viimati versioonis %s, sina kasutad versiooni %s. Maailma madaldamine võib põhjustada korrumpeerumist - me ei saa garanteerida, et see laadib või toimib. Kui soovid siiski j<PERSON>kata, palun loo varukoop<PERSON>.", "selectWorld.backupWarning.experimental": "See ma<PERSON>m kasutab kats<PERSON><PERSON><PERSON><PERSON> seadeid, mis võivad iga hetk lakata töötamast. Me ei saa garanteerida, et see laeb või töötab. <PERSON><PERSON> karta, kui kahetseda!", "selectWorld.backupWarning.snapshot": "Seda maailma mängiti viimati versioonis %s, sina kasutad versiooni %s. <PERSON><PERSON><PERSON> loo varuk<PERSON> juhuk<PERSON>, kui ma<PERSON><PERSON> k<PERSON>.", "selectWorld.bonusItems": "Boonuskirst", "selectWorld.cheats": "sohitegemine", "selectWorld.commands": "Käsklused", "selectWorld.conversion": "<PERSON><PERSON><PERSON> konverteer<PERSON>!", "selectWorld.conversion.tooltip": "Ohutuks konverteerimiseks peab selle maailma avama vanemas vers<PERSON>, näiteks 1.6.4", "selectWorld.create": "<PERSON><PERSON> uus ma<PERSON>m", "selectWorld.customizeType": "<PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Andmepakid", "selectWorld.data_read": "<PERSON><PERSON><PERSON> and<PERSON>e lugemine...", "selectWorld.delete": "Kustuta", "selectWorld.deleteButton": "Kustuta", "selectWorld.deleteQuestion": "Kas oled kindel, et soovid selle maailma kustutada?", "selectWorld.deleteWarning": "\"%s\" jääb igaveseks kadunuks! (Väga pikaks ajaks!)", "selectWorld.delete_failure": "Maailma kustutamine e<PERSON>", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON> var<PERSON>", "selectWorld.edit.backupCreated": "%s varundatud", "selectWorld.edit.backupFailed": "Varundamine e<PERSON>", "selectWorld.edit.backupFolder": "Ava varunduste kaust", "selectWorld.edit.backupSize": "suurus: %s MB", "selectWorld.edit.export_worldgen_settings": "Ekspordi maailmageneratsiooni seaded", "selectWorld.edit.export_worldgen_settings.failure": "Eksportimine e<PERSON>nnestus", "selectWorld.edit.export_worldgen_settings.success": "Eksporditud", "selectWorld.edit.openFolder": "<PERSON> ma<PERSON>ma kaust", "selectWorld.edit.optimize": "<PERSON><PERSON><PERSON>", "selectWorld.edit.resetIcon": "Lähtesta ikoon", "selectWorld.edit.save": "Salvesta", "selectWorld.edit.title": "<PERSON><PERSON> ma<PERSON>", "selectWorld.enterName": "<PERSON><PERSON><PERSON> nimi", "selectWorld.enterSeed": "<PERSON><PERSON>", "selectWorld.experimental": "katsetusjärgus", "selectWorld.experimental.details": "Üksikasjad", "selectWorld.experimental.details.entry": "Nõutud katsetusjärgus funktsioonid: %s", "selectWorld.experimental.details.title": "Katsetusjärgus funktsioonide nõuded", "selectWorld.experimental.message": "Ole ettevaatlik!\nSee seadistus nõuab funk<PERSON>, mis on veel arendusjärgus. Sinu maailm võib krah<PERSON>, puruneda või tulevaste uuendustega mitte töötada.", "selectWorld.experimental.title": "Katsetusjärgus funk<PERSON>onide hoiatus", "selectWorld.experiments": "Eksperimendid", "selectWorld.experiments.info": "Eksperimendid on potentsiaalsed uued funktsioonid. <PERSON>, sest asjad võivad katki minna. Eksperimente ei saa pärast maailma loomist välja lülitada.", "selectWorld.futureworld.error.text": "Midagi läks valesti tulevikuversiooni maailma laadimisega. See oli algusest peale risk<PERSON>ne te<PERSON>, kah<PERSON><PERSON> see ei toiminud.", "selectWorld.futureworld.error.title": "Ilmnes tõrge!", "selectWorld.gameMode": "Mängurežiim", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON><PERSON><PERSON>, kuid plokke ei saa lisada ega eemaldada.", "selectWorld.gameMode.adventure.line1": "Sarnaneb <PERSON>, kuid plokke ei saa", "selectWorld.gameMode.adventure.line2": "lisada ega eemaldada", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, ehita ja avasta ilma p<PERSON>. <PERSON> saad le<PERSON>, o<PERSON> l<PERSON> materjale ning koletised ei saa sulle haiget teha.", "selectWorld.gameMode.creative.line1": "Piira<PERSON><PERSON> ressursid, vaba lendamine", "selectWorld.gameMode.creative.line2": "ning võ<PERSON>lus lõhkuda plokke koheselt", "selectWorld.gameMode.hardcore": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "Ellujäämisrežiim lukustatud \"raskele\" raskusastmele. Surres ei saa end taastekitada.", "selectWorld.gameMode.hardcore.line1": "<PERSON>rnaneb <PERSON>ž<PERSON>ga, kuid on lukustatud", "selectWorld.gameMode.hardcore.line2": "ainult ühe eluga raskele raskusastmele", "selectWorld.gameMode.spectator": "vaatlemine", "selectWorld.gameMode.spectator.info": "Sa võid vaadata, aga ära puutu.", "selectWorld.gameMode.spectator.line1": "Sa võid vaadata, aga ära puutu", "selectWorld.gameMode.survival": "ellujäämine", "selectWorld.gameMode.survival.info": "<PERSON><PERSON> sa<PERSON>, kus sa<PERSON> eh<PERSON>, k<PERSON><PERSON>, meister<PERSON>a ja koletistega v<PERSON>.", "selectWorld.gameMode.survival.line1": "<PERSON>tsi ress<PERSON>, meisterda, saavuta", "selectWorld.gameMode.survival.line2": "<PERSON><PERSON><PERSON><PERSON>, tervis ja nälgimine", "selectWorld.gameRules": "Mängureeglid", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON> seaded", "selectWorld.import_worldgen_settings.failure": "Seadete importimine e<PERSON>", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON> seadete fail (.json)", "selectWorld.incompatible.description": "Seda maailma ei saa selles versioonis avada.\nSeda mängiti viimati versioonis %s.", "selectWorld.incompatible.info": "Ühildumatu versioon: %s", "selectWorld.incompatible.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> versioon", "selectWorld.incompatible.tooltip": "Seda maailma ei saa avada, kuna see loodi ü<PERSON>dumatu versio<PERSON> poolt.", "selectWorld.incompatible_series": "<PERSON><PERSON><PERSON> vers<PERSON><PERSON>t", "selectWorld.load_folder_access": "Ei saa lugeda või ligi pää<PERSON>a kaustale, kus mängumaailmu salvestatakse!", "selectWorld.loading_list": "<PERSON><PERSON><PERSON> loendi laadimine", "selectWorld.locked": "Lukustatud teise avatud <PERSON> tõttu", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON><PERSON> loomine", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, la<PERSON><PERSON><PERSON><PERSON> jne", "selectWorld.mapType": "<PERSON><PERSON><PERSON>", "selectWorld.mapType.normal": "tavaline", "selectWorld.moreWorldOptions": "<PERSON><PERSON> ma<PERSON>ma valikuid...", "selectWorld.newWorld": "<PERSON><PERSON> ma<PERSON>", "selectWorld.recreate": "Taasloo", "selectWorld.recreate.customized.text": "Kohandatud maailmad ei ole enam selles Minecrafti versioonis toetatud. Me võime proovida selle taasluua sama seemne ja omadustega, ent mistahes maastikukohandused kaovad. Vabandame ebamugavuste pärast!", "selectWorld.recreate.customized.title": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>mad ei ole enam toetatud", "selectWorld.recreate.error.text": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON><PERSON>l läks midagi valesti.", "selectWorld.recreate.error.title": "Ilmnes tõrge!", "selectWorld.resource_load": "Ressursside ettevalmistamine...", "selectWorld.resultFolder": "Salvestatakse kausta:", "selectWorld.search": "otsi ma<PERSON>mu", "selectWorld.seedInfo": "<PERSON><PERSON><PERSON> juh<PERSON>u seemne jaoks t<PERSON>s", "selectWorld.select": "<PERSON><PERSON><PERSON><PERSON> valitud ma<PERSON>mas", "selectWorld.targetFolder": "Salvestuskaust: %s", "selectWorld.title": "<PERSON><PERSON> ma<PERSON>m", "selectWorld.tooltip.fromNewerVersion1": "Maailm salvestati uuemas vers<PERSON>,", "selectWorld.tooltip.fromNewerVersion2": "selle maailma laadimine võib põhjustada probleeme!", "selectWorld.tooltip.snapshot1": "<PERSON>ra unusta seda maailma varundada", "selectWorld.tooltip.snapshot2": "enne selles katsetusversioonis laadi<PERSON>.", "selectWorld.unable_to_load": "<PERSON>i suutnud ma<PERSON>mu la<PERSON>da", "selectWorld.version": "versioon:", "selectWorld.versionJoinButton": "<PERSON><PERSON>", "selectWorld.versionQuestion": "Kas soovid tõesti seda maailma laadida?", "selectWorld.versionUnknown": "teadmata", "selectWorld.versionWarning": "Seda maailma mängiti viimati versioonis %s ning praeguses versioonis laadimine võib põhjustada korruptsiooni!", "selectWorld.warning.deprecated.question": "M<PERSON>ned funktsioonid on iganenud ning lakkavad tulevikus töötamast. Kas soovid jätkata?", "selectWorld.warning.deprecated.title": "Hoiatus! Need seaded kasutavad iganenud funktsioone", "selectWorld.warning.experimental.question": "Need seaded on katsetusjärgus ning võivad ühel päeval lakata töötamast. Kas soovid jätkata?", "selectWorld.warning.experimental.title": "Hoiatus! Need seaded kasutavad kats<PERSON><PERSON><PERSON><PERSON>", "selectWorld.warning.lowDiskSpace.description": "Sinu seadmes ei ole palju kettaruumi järel.\nMängu ajal kettaruumi täitumine võib kahjustada sinu maailma.", "selectWorld.warning.lowDiskSpace.title": "Hoia<PERSON>! <PERSON><PERSON><PERSON><PERSON> on vähe!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "<PERSON><PERSON> sildi teksti", "sleep.not_possible": "Mistahes magamine ei jäta seda öö<PERSON> vahele", "sleep.players_sleeping": "%s/%s mängijat magavad", "sleep.skipping_night": "Magamine läbi selle öö", "slot.only_single_allowed": "<PERSON><PERSON><PERSON> on ainult <PERSON><PERSON> la<PERSON>, saadi \"%s\"", "slot.unknown": "<PERSON><PERSON><PERSON><PERSON> lahter \"%s\"", "snbt.parser.empty_key": "<PERSON><PERSON><PERSON> ei saa olla tühi", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON><PERSON>", "snbt.parser.expected_decimal_numeral": "<PERSON><PERSON><PERSON>", "snbt.parser.expected_float_type": "<PERSON><PERSON><PERSON>", "snbt.parser.expected_hex_escape": "Oodati tähemärgijada pikkusega %s märki", "snbt.parser.expected_hex_numeral": "<PERSON><PERSON><PERSON> k<PERSON>ü<PERSON>dar<PERSON>", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON>", "snbt.parser.expected_non_negative_number": "<PERSON><PERSON><PERSON> mittenegatiiv<PERSON> arvu", "snbt.parser.expected_number_or_boolean": "Oodati arvu või tõeväärtust", "snbt.parser.expected_string_uuid": "<PERSON><PERSON><PERSON>, mis väljendab kehtivat UUID-d", "snbt.parser.expected_unquoted_string": "Oodati korrektset jutumärkideta sõnet", "snbt.parser.infinity_not_allowed": "Mittelõplikud arvud ei ole lubatud", "snbt.parser.invalid_array_element_type": "Sobimatu massiivielemendi tüüp", "snbt.parser.invalid_character_name": "Sobimatu Unicode-märgi nimi", "snbt.parser.invalid_codepoint": "Sobimatu Unicode-märgi väärtus: %s", "snbt.parser.invalid_string_contents": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ne sisu", "snbt.parser.invalid_unquoted_start": "Jutumärgita sõned ei saa alata arvudega 0-9, märkidega + või -", "snbt.parser.leading_zero_not_allowed": "Kümnendarvud ei saa alata 0-ga", "snbt.parser.no_such_operation": "Järgnev toiming puudub: %s", "snbt.parser.number_parse_failure": "<PERSON><PERSON><PERSON> t<PERSON>mine e<PERSON>õnnestus: %s", "snbt.parser.undescore_not_allowed": "Alakriipsud ei ole arvu alguses ega lõpus lubatud", "soundCategory.ambient": "Õhustik/keskkond", "soundCategory.block": "Plokid", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON><PERSON> elukad", "soundCategory.master": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.music": "Muusika", "soundCategory.neutral": "Sõbralikud elukad", "soundCategory.player": "Mängijad", "soundCategory.record": "Plaadimängija/noodiplokid", "soundCategory.ui": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.voice": "Hääl/kõne", "soundCategory.weather": "Ilm", "spectatorMenu.close": "<PERSON><PERSON>", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.root.prompt": "Vajuta mõnda klahvi käskluse valimiseks ning uuesti selle kasutamiseks.", "spectatorMenu.team_teleport": "Teleporteeru me<PERSON>konnaliikme juurde", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON> me<PERSON>, mille juurde teleporteeruda", "spectatorMenu.teleport": "Teleporteeru mängija juurde", "spectatorMenu.teleport.prompt": "<PERSON><PERSON>, kelle juurde teleporteer<PERSON>", "stat.generalButton": "<PERSON><PERSON><PERSON>", "stat.itemsButton": "<PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Loomi p<PERSON>d", "stat.minecraft.aviate_one_cm": "Kattetiibadega läbitud vahemaa", "stat.minecraft.bell_ring": "Kellukesi helistatud", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON> l<PERSON> vahemaa", "stat.minecraft.clean_armor": "<PERSON><PERSON><PERSON> o<PERSON>", "stat.minecraft.clean_banner": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.clean_shulker_box": "Šulkerika<PERSON>ud", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON> v<PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "Kilbiga blokeeritud kahju", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_dealt_absorbed": "(<PERSON><PERSON><PERSON><PERSON><PERSON>) kahju p<PERSON>hjusta<PERSON>d", "stat.minecraft.damage_dealt_resisted": "(Tõkestatud) kahju põhjustatud", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON><PERSON> kahju", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON>", "stat.minecraft.drop": "Esemeid maha visatud", "stat.minecraft.eat_cake_slice": "Koog<PERSON><PERSON><PERSON> söödud", "stat.minecraft.enchant_item": "Esemeid loitsitud", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON><PERSON> vah<PERSON>a", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON> t<PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON>", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON> v<PERSON>", "stat.minecraft.happy_ghast_one_cm": "Õnneliku ghastiga läbitud vahemaa", "stat.minecraft.horse_one_cm": "Hobusega läbitud vahemaa", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON> lä<PERSON> o<PERSON>", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lä<PERSON>", "stat.minecraft.interact_with_anvil": "Alasiga <PERSON>", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_blast_furnace": "Kõrgahjuga tegutsemisi", "stat.minecraft.interact_with_brewingstand": "Pruulimisal<PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Lõkketulega tegutsemisi", "stat.minecraft.interact_with_cartography_table": "Kartograafialauaga tegutsemisi", "stat.minecraft.interact_with_crafting_table": "Meister<PERSON><PERSON><PERSON> te<PERSON>", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_grindstone": "Käiakiviga tegutsemisi", "stat.minecraft.interact_with_lectern": "Kõnepuldiga tegutsemisi", "stat.minecraft.interact_with_loom": "Kangastelgedega <PERSON>", "stat.minecraft.interact_with_smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smoker": "Suit<PERSON>ah<PERSON><PERSON>", "stat.minecraft.interact_with_stonecutter": "Kivilõikuriga tegutsemisi", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "Mängust lahkumisi", "stat.minecraft.minecart_one_cm": "Kaevanduskäruga läbitud vahemaa", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON> tape<PERSON>d", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.open_enderchest": "Enderikirste avatud", "stat.minecraft.open_shulker_box": "Šulkerikaste avatud", "stat.minecraft.pig_one_cm": "Seaga lä<PERSON>ud vahemaa", "stat.minecraft.play_noteblock": "Noodiplokke mängitud", "stat.minecraft.play_record": "Muusikaplaate mängitud", "stat.minecraft.play_time": "Mängitud aeg", "stat.minecraft.player_kills": "Mängijaid tapetud", "stat.minecraft.pot_flower": "<PERSON><PERSON> potti istutatud", "stat.minecraft.raid_trigger": "<PERSON><PERSON>", "stat.minecraft.raid_win": "Reide võidetud", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON><PERSON> ma<PERSON> kordi", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON>d aeg", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "Tatsajaga läbitud vahemaa", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "stat.minecraft.talked_to_villager": "Külaelanikega räägitud", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.time_since_death": "Aega viimasest surmast", "stat.minecraft.time_since_rest": "Aega viimasest magamisest", "stat.minecraft.total_world_time": "<PERSON><PERSON><PERSON> laht<PERSON>u aeg", "stat.minecraft.traded_with_villager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.trigger_trapped_chest": "Püüniskirste käivitatud", "stat.minecraft.tune_noteblock": "Noodiplokke häälestatud", "stat.minecraft.use_cauldron": "<PERSON><PERSON> paja<PERSON> v<PERSON><PERSON><PERSON>", "stat.minecraft.walk_on_water_one_cm": "<PERSON>ee peal kõnnitud vahemaa", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> vah<PERSON>a", "stat.minecraft.walk_under_water_one_cm": "Vee all kõnnitud vahemaa", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Lõhkumiskorrad", "stat_type.minecraft.crafted": "Meisterduskorrad", "stat_type.minecraft.dropped": "<PERSON><PERSON> visatud", "stat_type.minecraft.killed": "Sa tapsid %s %s", "stat_type.minecraft.killed.none": "Sa pole kunagi %s tapnud", "stat_type.minecraft.killed_by": "%s tappis su %s kord(a)", "stat_type.minecraft.killed_by.none": "%s <PERSON> kunagi sind tapnud", "stat_type.minecraft.mined": "Kaevandamiskorrad", "stat_type.minecraft.picked_up": "<PERSON><PERSON>", "stat_type.minecraft.used": "Kasutuskorrad", "stats.none": "-", "structure_block.button.detect_size": "TUVASTA", "structure_block.button.load": "LAADI", "structure_block.button.save": "SALVESTA", "structure_block.custom_data": "<PERSON><PERSON><PERSON><PERSON> and<PERSON><PERSON><PERSON> nimi", "structure_block.detect_size": "<PERSON><PERSON><PERSON> struk<PERSON><PERSON> suurus ja asuk<PERSON>t:", "structure_block.hover.corner": "Nurk: %s", "structure_block.hover.data": "Andmed: %s", "structure_block.hover.load": "Laadi: %s", "structure_block.hover.save": "Salvesta: %s", "structure_block.include_entities": "Olemite kaasamine:", "structure_block.integrity": "Struktuuri terviklikkus ja seeme", "structure_block.integrity.integrity": "Struktuuri ter<PERSON>", "structure_block.integrity.seed": "<PERSON><PERSON><PERSON><PERSON>e", "structure_block.invalid_structure_name": "Sobimatu struk<PERSON> \"%s\"", "structure_block.load_not_found": "Struktuur \"%s\" pole saadaval", "structure_block.load_prepare": "Struktuuri \"%s\" asukoht ettevalmistatud", "structure_block.load_success": "Struktuur laaditud failist \"%s\"", "structure_block.mode.corner": "Nurk", "structure_block.mode.data": "And<PERSON>", "structure_block.mode.load": "Laadi", "structure_block.mode.save": "Salvesta", "structure_block.mode_info.corner": "Nurgarežiim - paigutuse ja suuruse märgistaja", "structure_block.mode_info.data": "Andmerežiim - mänguloogika märgistaja", "structure_block.mode_info.load": "Laadimisrežiim - laadi failist", "structure_block.mode_info.save": "Salvestusrežiim - kirjuta faili", "structure_block.position": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.position.x": "suhteline x-<PERSON><PERSON><PERSON>t", "structure_block.position.y": "suhteline y-<PERSON><PERSON><PERSON>t", "structure_block.position.z": "<PERSON><PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON><PERSON>t", "structure_block.save_failure": "Struktuuri \"%s\" ei saanud salvestada", "structure_block.save_success": "Struktuur salvestatud nimega \"%s\"", "structure_block.show_air": "Nähtamatute plokkide kuva:", "structure_block.show_boundingbox": "Piirdekast:", "structure_block.size": "<PERSON><PERSON><PERSON><PERSON> suurus", "structure_block.size.x": "struk<PERSON>uri x-suurus", "structure_block.size.y": "struk<PERSON><PERSON> y-suurus", "structure_block.size.z": "struk<PERSON><PERSON> z-suurus", "structure_block.size_failure": "<PERSON><PERSON><PERSON><PERSON> suurust ei õ<PERSON>ud tuvastada. <PERSON> n<PERSON> struktuurinimedega", "structure_block.size_success": "Suurus edukalt tuvastatud struktuurile \"%s\"", "structure_block.strict": "Range paigutus:", "structure_block.structure_name": "<PERSON><PERSON><PERSON><PERSON> nimi", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON>", "subtitles.block.amethyst_block.chime": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> kaasa", "subtitles.block.anvil.destroy": "<PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON> ma<PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON> sai kasutatud", "subtitles.block.barrel.close": "<PERSON><PERSON><PERSON>b", "subtitles.block.barrel.open": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.power_select": "<PERSON><PERSON> j<PERSON> sai valitud", "subtitles.block.beehive.drip": "<PERSON><PERSON>", "subtitles.block.beehive.enter": "Mesilane siseneb tarusse", "subtitles.block.beehive.exit": "Mesilane väljub tarust", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.beehive.work": "Mesilased töötavad", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON> v<PERSON> kaasa", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_down": "<PERSON>il<PERSON><PERSON>t kaldub alla", "subtitles.block.big_dripleaf.tilt_up": "Tilkeleht kaldub üles", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mullitab", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> v<PERSON>d", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "<PERSON><PERSON> lirtsub", "subtitles.block.campfire.crackle": "Lõkketuli pragiseb", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON><PERSON> pragiseb", "subtitles.block.candle.extinguish": "Küünal kustub", "subtitles.block.chest.close": "<PERSON><PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON> lukus", "subtitles.block.chest.open": "<PERSON><PERSON>", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.comparator.click": "Komparaator klõpsub", "subtitles.block.composter.empty": "Komposter tühjendatud", "subtitles.block.composter.fill": "Komposter täidetud", "subtitles.block.composter.ready": "Komposter kompostib", "subtitles.block.conduit.activate": "Siire aktiveerub", "subtitles.block.conduit.ambient": "<PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON><PERSON> rü<PERSON>", "subtitles.block.conduit.deactivate": "Siire desak<PERSON>", "subtitles.block.copper_bulb.turn_off": "Vaskpirn lülitub välja", "subtitles.block.copper_bulb.turn_on": "Vask<PERSON>rn lülitub sisse", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON>", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON>", "subtitles.block.crafter.craft": "Meisterdaja meisterdab", "subtitles.block.crafter.fail": "Meisterdaja meisterdamine e<PERSON>", "subtitles.block.creaking_heart.hurt": "Kriiksuv süda nuriseb", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.spawn": "Kriiksuv süda ärkab", "subtitles.block.deadbush.idle": "Kuivad helid", "subtitles.block.decorated_pot.insert": "Kaunistatud pott täitub", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON><PERSON><PERSON><PERSON> pott rapub", "subtitles.block.decorated_pot.shatter": "Kaunistatud pott puruneb", "subtitles.block.dispenser.dispense": "<PERSON><PERSON>", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "subtitles.block.door.toggle": "Uks kriiksub", "subtitles.block.dried_ghast.ambient": "Kuivuse helid", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON><PERSON> ghast saab ni<PERSON>uta<PERSON>d", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON><PERSON> ghast vettib", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON><PERSON> ghast tunneb end paremini", "subtitles.block.dry_grass.ambient": "Tuulised helid", "subtitles.block.enchantment_table.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sai kasu<PERSON>", "subtitles.block.end_portal.spawn": "Endi portaal avaneb", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON>", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON><PERSON> sul<PERSON>b", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.open": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.fence_gate.toggle": "<PERSON>av<PERSON><PERSON><PERSON>", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON> k<PERSON>", "subtitles.block.firefly_bush.idle": "Jaanimardikad sumisevad", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON> k<PERSON>", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> pragiseb", "subtitles.block.generic.break": "Plokk purunes", "subtitles.block.generic.fall": "<PERSON><PERSON> kukub plokile", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Plokk puruneb", "subtitles.block.generic.place": "Plokk asetatud", "subtitles.block.grindstone.use": "Käiakivi sai kasutatud", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> k<PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON>lt rapub", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>l libisemine", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON>", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON>", "subtitles.block.lava.ambient": "Laava plumpsatab", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON>", "subtitles.block.note_block.note": "Noodiplokk mängib", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON> liigub", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> til<PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> til<PERSON>b patta", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON> patta", "subtitles.block.pointed_dripstone.land": "Stalaktiit lendab alla", "subtitles.block.portal.ambient": "Portaal suhiseb", "subtitles.block.portal.travel": "Portaali müra hääbub", "subtitles.block.portal.trigger": "Portaali müra val<PERSON>b", "subtitles.block.pressure_plate.click": "Surveplaat klõpsub", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON><PERSON> susiseb", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.respawn_anchor.charge": "Taastek<PERSON><PERSON><PERSON><PERSON> saab laetud", "subtitles.block.respawn_anchor.deplete": "Taastekkeankur kulub", "subtitles.block.respawn_anchor.set_spawn": "Taastekkeankur määrab tek<PERSON>", "subtitles.block.sand.idle": "Liivased helid", "subtitles.block.sand.wind": "Tuulised helid", "subtitles.block.sculk.charge": "Sculk mullitab", "subtitles.block.sculk.spread": "Sculk levib", "subtitles.block.sculk_catalyst.bloom": "Sculk-katalüsaator õitseb", "subtitles.block.sculk_sensor.clicking": "Sculk-sensor klõpsib", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-sensor lõpetab klõpsimise", "subtitles.block.sculk_shrieker.shriek": "Sculk-kisenda<PERSON> kisendab", "subtitles.block.shulker_box.close": "Šulkerikast sulgub", "subtitles.block.shulker_box.open": "Šulkerikast avaneb", "subtitles.block.sign.waxed_interact_fail": "<PERSON>lt rapub", "subtitles.block.smithing_table.use": "<PERSON><PERSON><PERSON><PERSON><PERSON> sai ka<PERSON>", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.crack": "<PERSON>uusk<PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.hatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.plop": "Nuuskija mulksatab", "subtitles.block.sponge.absorb": "<PERSON><PERSON><PERSON>n imab", "subtitles.block.sweet_berry_bush.pick_berries": "Marjad vupsavad välja", "subtitles.block.trapdoor.close": "<PERSON><PERSON>", "subtitles.block.trapdoor.open": "<PERSON><PERSON>", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Kurjakuulutav ese valmistub", "subtitles.block.trial_spawner.ambient": "<PERSON><PERSON><PERSON><PERSON> pragiseb", "subtitles.block.trial_spawner.ambient_charged": "Kurjakuulutav pragisemine", "subtitles.block.trial_spawner.ambient_ominous": "Kurjakuulutav pragisemine", "subtitles.block.trial_spawner.charge_activate": "<PERSON><PERSON> katsu<PERSON>", "subtitles.block.trial_spawner.close_shutter": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> sulgub", "subtitles.block.trial_spawner.detect_player": "<PERSON><PERSON><PERSON><PERSON> tekitaja la<PERSON>b end <PERSON>", "subtitles.block.trial_spawner.eject_item": "Katsumuse tekitaja väljastab esemeid", "subtitles.block.trial_spawner.ominous_activate": "<PERSON><PERSON> katsu<PERSON>", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.trial_spawner.spawn_item": "Kurjakuulutav ese kukub", "subtitles.block.trial_spawner.spawn_item_begin": "Kurjakuulutav ese ilmub", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON><PERSON><PERSON> tekitaja tekitab eluka", "subtitles.block.tripwire.attach": "Traadilõks kinnitub", "subtitles.block.tripwire.click": "Traadilõks klõpsub", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> era<PERSON>", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> pragiseb", "subtitles.block.vault.close_shutter": "Vara<PERSON><PERSON><PERSON> sulgub", "subtitles.block.vault.deactivate": "Varahoidla kustutatud", "subtitles.block.vault.eject_item": "Varahoidla väljutab eseme", "subtitles.block.vault.insert_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> luku<PERSON> lahti", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON><PERSON><PERSON> es<PERSON>est", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.water.ambient": "<PERSON>esi voolab", "subtitles.block.wet_sponge.dries": "<PERSON><PERSON><PERSON><PERSON> kuivab", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "<PERSON><PERSON><PERSON><PERSON> raa<PERSON>", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.take_enchanted": "Loitsitud raamat v<PERSON>ud", "subtitles.enchant.thorns.hit": "Okkad torgivad", "subtitles.entity.allay.ambient_with_item": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.ambient_without_item": "Rahus<PERSON><PERSON>", "subtitles.entity.allay.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.allay.item_given": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_taken": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.armadillo.brush": "<PERSON><PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.armadillo.hurt": "Vö<PERSON><PERSON><PERSON> valutab", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> ennast", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "subtitles.entity.armadillo.peek": "Vöölane piilub", "subtitles.entity.armadillo.roll": "Vöölane keerdub kokku", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON><PERSON><PERSON> kukutab soomuse", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> lahti", "subtitles.entity.armadillo.unroll_start": "Vöölane piilub", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON> kukkus", "subtitles.entity.arrow.hit": "<PERSON><PERSON> tabab", "subtitles.entity.arrow.hit_player": "Mäng<PERSON>t ta<PERSON>akse", "subtitles.entity.arrow.shoot": "Nool lennutatud", "subtitles.entity.axolotl.attack": "<PERSON>ks<PERSON><PERSON> rü<PERSON>", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON>b", "subtitles.entity.axolotl.hurt": "<PERSON>ks<PERSON><PERSON><PERSON> valutab", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.splash": "Aksolotl pladistab", "subtitles.entity.axolotl.swim": "Aksolotl uju<PERSON>", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON> vigi<PERSON>b", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "subtitles.entity.bee.ambient": "Mesilane sumiseb", "subtitles.entity.bee.death": "Mesilane sureb", "subtitles.entity.bee.hurt": "Mesilasel valutab", "subtitles.entity.bee.loop": "Mesilane sumiseb", "subtitles.entity.bee.loop_aggressive": "Mesilane sumiseb vihaselt", "subtitles.entity.bee.pollinate": "Mesilane sumiseb rõõmsalt", "subtitles.entity.bee.sting": "Mesilane nõelab", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "subtitles.entity.blaze.burn": "<PERSON><PERSON><PERSON><PERSON> prag<PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.blaze.hurt": "Lõõmal valutab", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON><PERSON> tulistab", "subtitles.entity.boat.paddle_land": "Sõudmine", "subtitles.entity.boat.paddle_water": "Sõudmine", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.hurt": "Rabatul valutab", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON> tuli<PERSON>", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON>asi", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON> hi<PERSON> sisse", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON> tuli<PERSON>", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON> libi<PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON><PERSON><PERSON> purskub", "subtitles.entity.camel.ambient": "<PERSON><PERSON>", "subtitles.entity.camel.dash": "Kaamel silkab", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON>b", "subtitles.entity.camel.eat": "<PERSON><PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "Sadul läheb selga", "subtitles.entity.camel.sit": "<PERSON><PERSON> istub maha", "subtitles.entity.camel.stand": "<PERSON><PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON> lii<PERSON>", "subtitles.entity.cat.ambient": "<PERSON><PERSON> n<PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> palub", "subtitles.entity.cat.death": "<PERSON><PERSON> sureb", "subtitles.entity.cat.eat": "<PERSON><PERSON>", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.cat.purr": "<PERSON><PERSON> nurrub", "subtitles.entity.chicken.ambient": "<PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON> <PERSON>b", "subtitles.entity.chicken.egg": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> valutab", "subtitles.entity.cod.death": "Tursk sureb", "subtitles.entity.cod.flop": "Tursk potsatab", "subtitles.entity.cod.hurt": "Tursal valutab", "subtitles.entity.cow.ambient": "<PERSON><PERSON> am<PERSON>b", "subtitles.entity.cow.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON> saab l<PERSON>", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> variseb kokku", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON><PERSON> on pihta saanud", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON>ik<PERSON><PERSON> liigub", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.creeper.hurt": "Creeperil valutab", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> vilis<PERSON>b", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.hurt": "Del<PERSON><PERSON>l valutab", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> pl<PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.ambient": "<PERSON><PERSON><PERSON> ii<PERSON>ab", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.chest": "<PERSON><PERSON> l<PERSON><PERSON> e<PERSON><PERSON><PERSON> se<PERSON>ga", "subtitles.entity.donkey.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.donkey.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> <PERSON>b", "subtitles.entity.drowned.hurt": "Uppunul valutab", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> viskab kolm<PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON> astub", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> u<PERSON>", "subtitles.entity.egg.throw": "<PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "Vanemkaitsja oigab", "subtitles.entity.elder_guardian.ambient_land": "Vanemkaitsja la<PERSON>utab", "subtitles.entity.elder_guardian.curse": "Vanemkaitsja neab", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.flop": "Vanemkaitsja potsatab", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON><PERSON> val<PERSON>b", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> tuli<PERSON>b", "subtitles.entity.ender_eye.death": "<PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> si<PERSON> tulistab", "subtitles.entity.ender_pearl.throw": "Enderi<PERSON><PERSON><PERSON>ab", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> uopib", "subtitles.entity.enderman.death": "<PERSON><PERSON> sureb", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.enderman.scream": "<PERSON><PERSON>", "subtitles.entity.enderman.stare": "<PERSON><PERSON> kisendab", "subtitles.entity.enderman.teleport": "<PERSON><PERSON> teleporteerub", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON><PERSON> vudib", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON><PERSON> sureb", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON><PERSON> he<PERSON> lo<PERSON>u", "subtitles.entity.evoker.celebrate": "Ilmu<PERSON><PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.evoker.prepare_attack": "Ilmu<PERSON>ja valmistub rünna<PERSON>ks", "subtitles.entity.evoker.prepare_summon": "Ilmutaja valmistub tekitamise<PERSON>", "subtitles.entity.evoker.prepare_wololo": "Ilmutaja valmistub võlumiseks", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.blast": "Ilutulestik plahvatab", "subtitles.entity.firework_rocket.launch": "Ilutulestik käivitub", "subtitles.entity.firework_rocket.twinkle": "Ilutulestik välgub", "subtitles.entity.fish.swim": "Sulpsumine", "subtitles.entity.fishing_bobber.retrieve": "Õngekork toodud", "subtitles.entity.fishing_bobber.splash": "Õngekork plärtsatab", "subtitles.entity.fishing_bobber.throw": "Õngekork visatud", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON> piiks<PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> hammustab", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.hurt": "Rebasel valutab", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> kriiskab", "subtitles.entity.fox.sleep": "Rebane norskab", "subtitles.entity.fox.sniff": "Rebane nuusib", "subtitles.entity.fox.spit": "Re<PERSON>e sülitab", "subtitles.entity.fox.teleport": "Rebane teleporteerub", "subtitles.entity.frog.ambient": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.frog.death": "<PERSON><PERSON> sureb", "subtitles.entity.frog.eat": "<PERSON><PERSON>", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON> kudeb", "subtitles.entity.frog.long_jump": "<PERSON><PERSON>", "subtitles.entity.generic.big_fall": "<PERSON><PERSON> kukkus", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Suremine", "subtitles.entity.generic.drink": "Rüüpamine", "subtitles.entity.generic.eat": "Söömine", "subtitles.entity.generic.explode": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON> komi<PERSON>", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "Ujumine", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON><PERSON> purskub", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> <PERSON>b", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> tuli<PERSON>", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> sureb", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON><PERSON> ilmub", "subtitles.entity.glow_item_frame.add_item": "Hõõg-esemeraam täitub", "subtitles.entity.glow_item_frame.break": "Hõõg-esemeraam puruneb", "subtitles.entity.glow_item_frame.place": "Hõõg-esemeraam asetatud", "subtitles.entity.glow_item_frame.remove_item": "Hõõg-esemeraam tü<PERSON>neb", "subtitles.entity.glow_item_frame.rotate_item": "Hõõg-esemeraam klõpsub", "subtitles.entity.glow_squid.ambient": "Hõõgkalmaar ujub", "subtitles.entity.glow_squid.death": "Hõõgkalmaar sureb", "subtitles.entity.glow_squid.hurt": "Hõõgkalmaaril valutab", "subtitles.entity.glow_squid.squirt": "Hõõgkalmaar <PERSON>", "subtitles.entity.goat.ambient": "<PERSON><PERSON> m<PERSON>b", "subtitles.entity.goat.death": "<PERSON>s sureb", "subtitles.entity.goat.eat": "<PERSON><PERSON>", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON><PERSON> mur<PERSON><PERSON>", "subtitles.entity.goat.hurt": "<PERSON><PERSON> valutab", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> kargab", "subtitles.entity.goat.milk": "<PERSON>s saab lüpstud", "subtitles.entity.goat.prepare_ram": "Kits trambib", "subtitles.entity.goat.ram_impact": "<PERSON>s rammib", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> röö<PERSON>", "subtitles.entity.goat.step": "Kits astub", "subtitles.entity.guardian.ambient": "Kaitsja oigab", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON> tulistab", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.happy_ghast.ambient": "Õnnelik ghast ü<PERSON>b", "subtitles.entity.happy_ghast.death": "Õnnelik ghast sureb", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "Õnnelik ghast on valmis", "subtitles.entity.happy_ghast.harness_goggles_up": "Õnnelik ghast peatub", "subtitles.entity.happy_ghast.hurt": "Õnnelikul ghastil valutab", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> u<PERSON>b", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> u<PERSON> vihaselt", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> muundus zogliniks", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> ta<PERSON>ub", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> as<PERSON>", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> hirn<PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> hirn<PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON> l<PERSON>b ho<PERSON> selga", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON>b", "subtitles.entity.horse.eat": "Hobune sö<PERSON>", "subtitles.entity.horse.gallop": "Hobune galop<PERSON>", "subtitles.entity.horse.hurt": "Hobusel valutab", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.saddle": "Sadul läheb selga", "subtitles.entity.husk.ambient": "<PERSON><PERSON><PERSON> oh<PERSON>", "subtitles.entity.husk.converted_to_zombie": "Kuivik muundus zombiks", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.hurt": "Kuivikul valutab", "subtitles.entity.illusioner.ambient": "Trikitaja pomiseb", "subtitles.entity.illusioner.cast_spell": "Triki<PERSON><PERSON> he<PERSON> lo<PERSON>u", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.hurt": "Trikitaja<PERSON> valutab", "subtitles.entity.illusioner.mirror_move": "Trikitaja p<PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Trikitaja valmistab ette pimedust", "subtitles.entity.illusioner.prepare_mirror": "Trikitaja valmistab ette p<PERSON>pilti", "subtitles.entity.iron_golem.attack": "Raudgolem ründab", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.entity.iron_golem.death": "<PERSON><PERSON><PERSON><PERSON>b", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.iron_golem.repair": "Raudgolem sai parandatud", "subtitles.entity.item.break": "<PERSON><PERSON>", "subtitles.entity.item.pickup": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.item_frame.add_item": "<PERSON>semer<PERSON><PERSON> täitub", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.entity.item_frame.place": "Esemeraam asetatud", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.rotate_item": "Esemeraam k<PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.place": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.impact": "Välk tabab", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.llama.ambient": "Laama mö<PERSON>b", "subtitles.entity.llama.angry": "Laama mökitab vihaselt", "subtitles.entity.llama.chest": "<PERSON><PERSON><PERSON> se<PERSON>ga", "subtitles.entity.llama.death": "<PERSON><PERSON> sureb", "subtitles.entity.llama.eat": "Laama sööb", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.llama.spit": "Laama sülitab", "subtitles.entity.llama.step": "Laama astub", "subtitles.entity.llama.swag": "<PERSON><PERSON> sa<PERSON> ku<PERSON>", "subtitles.entity.magma_cube.death": "Ma<PERSON><PERSON><PERSON> <PERSON>b", "subtitles.entity.magma_cube.hurt": "Magmakuubil valutab", "subtitles.entity.magma_cube.squish": "Magmakuup lirtsub", "subtitles.entity.minecart.inside": "Kaevand<PERSON><PERSON><PERSON>", "subtitles.entity.minecart.inside_underwater": "Kaevanduskäru ragiseb vee all", "subtitles.entity.minecart.riding": "Kaevand<PERSON><PERSON><PERSON> veereb", "subtitles.entity.mooshroom.convert": "Mooshroom muundub", "subtitles.entity.mooshroom.eat": "Mooshroom sööb", "subtitles.entity.mooshroom.milk": "Mooshroom saab lüpstud", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom saab kahtlaselt lüpstud", "subtitles.entity.mule.ambient": "<PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON> mu<PERSON> se<PERSON>ga", "subtitles.entity.mule.death": "<PERSON><PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON>", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.mule.jump": "<PERSON><PERSON>", "subtitles.entity.painting.break": "<PERSON><PERSON>", "subtitles.entity.painting.place": "<PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "<PERSON>da puhib", "subtitles.entity.panda.ambient": "<PERSON><PERSON> hingeldab", "subtitles.entity.panda.bite": "Panda hammustab", "subtitles.entity.panda.cant_breed": "Panda m<PERSON>", "subtitles.entity.panda.death": "<PERSON><PERSON> sureb", "subtitles.entity.panda.eat": "<PERSON><PERSON>", "subtitles.entity.panda.hurt": "Pandal valutab", "subtitles.entity.panda.pre_sneeze": "<PERSON>da nina <PERSON>", "subtitles.entity.panda.sneeze": "Panda aevastab", "subtitles.entity.panda.step": "<PERSON><PERSON> astub", "subtitles.entity.panda.worried_ambient": "Panda nuuksub", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> hi<PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "Papagoi o<PERSON>b", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> vudib", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> pomi<PERSON>b", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON> nutab", "subtitles.entity.parrot.imitate.guardian": "Papagoi o<PERSON>b", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> ohib", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON> pomi<PERSON>b", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON> l<PERSON>b", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON> pomi<PERSON>b", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON> var<PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON> l<PERSON>b", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> pob<PERSON>b", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> vingub", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> itsitab", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> ohib", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> ohib", "subtitles.entity.phantom.ambient": "Fantoom kriiskab", "subtitles.entity.phantom.bite": "Fantoom hammustab", "subtitles.entity.phantom.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.phantom.flap": "Fantoom laksutab", "subtitles.entity.phantom.hurt": "Fantoomil valutab", "subtitles.entity.phantom.swoop": "Fantoom sööstab", "subtitles.entity.pig.ambient": "<PERSON>ga röhib", "subtitles.entity.pig.death": "<PERSON><PERSON> sureb", "subtitles.entity.pig.hurt": "Seal valutab", "subtitles.entity.pig.saddle": "Sadul läheb selga", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> imetleb eset", "subtitles.entity.piglin.ambient": "<PERSON>lin norskab", "subtitles.entity.piglin.angry": "Piglin norskab vihaselt", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> muundus zombistunud pigliniks", "subtitles.entity.piglin.death": "<PERSON><PERSON> sureb", "subtitles.entity.piglin.hurt": "Pig<PERSON>l valutab", "subtitles.entity.piglin.jealous": "<PERSON>lin norskab kadedalt", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> taandub", "subtitles.entity.piglin.step": "<PERSON><PERSON> as<PERSON>", "subtitles.entity.piglin_brute.ambient": "Jõhker piglin norskab", "subtitles.entity.piglin_brute.angry": "Jõhker piglin norskab vihaselt", "subtitles.entity.piglin_brute.converted_to_zombified": "Jõhker piglin muundus zombistunud pigliniks", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON> piglin sureb", "subtitles.entity.piglin_brute.hurt": "Jõh<PERSON><PERSON> piglinil valutab", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON> piglin astub", "subtitles.entity.pillager.ambient": "Rüüstaja pomiseb", "subtitles.entity.pillager.celebrate": "Rüüstaja hõiskab", "subtitles.entity.pillager.death": "Rüü<PERSON><PERSON> sure<PERSON>", "subtitles.entity.pillager.hurt": "Rüü<PERSON><PERSON><PERSON> valutab", "subtitles.entity.player.attack.crit": "Kriitiline r<PERSON>", "subtitles.entity.player.attack.knockback": "Tagasilööv rünne", "subtitles.entity.player.attack.strong": "<PERSON><PERSON><PERSON> rü<PERSON>", "subtitles.entity.player.attack.sweep": "Pühkiv rünne", "subtitles.entity.player.attack.weak": "Nõrk rünne", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "Mängijal valutab", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON> up<PERSON>", "subtitles.entity.player.hurt_on_fire": "Mängija <PERSON>", "subtitles.entity.player.levelup": "Mängija kiliseb", "subtitles.entity.player.teleport": "Mängija telepordib", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> ohib", "subtitles.entity.polar_bear.ambient_baby": "Jääkarubeebi ümiseb", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.hurt": "Jääkarul valutab", "subtitles.entity.polar_bear.warning": "Jääkaru mö<PERSON>gab", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON> visa<PERSON>d", "subtitles.entity.puffer_fish.blow_out": "Kerakala l<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_up": "Kerakala lä<PERSON>b <PERSON> täis", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON>b", "subtitles.entity.puffer_fish.flop": "Kerakala potsatab", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.puffer_fish.sting": "Kerakala nõelab", "subtitles.entity.rabbit.ambient": "Küülik piiksub", "subtitles.entity.rabbit.attack": "Küülik ründab", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON><PERSON>b", "subtitles.entity.rabbit.hurt": "Küülikul valutab", "subtitles.entity.rabbit.jump": "K<PERSON><PERSON><PERSON> hü<PERSON>b", "subtitles.entity.ravager.ambient": "<PERSON>ast<PERSON>", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON> hammust<PERSON>", "subtitles.entity.ravager.celebrate": "Laastaja hõiskab", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON> ta<PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.sheep.ambient": "<PERSON><PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> valutab", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> sul<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.shulker.hurt": "Šulkeril valutab", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> tulistab", "subtitles.entity.shulker.teleport": "Šulker teleporteerub", "subtitles.entity.shulker_bullet.hit": "Šulkeri kuul p<PERSON>vatab", "subtitles.entity.shulker_bullet.hurt": "Šulkeri kuul pu<PERSON>eb", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>b", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON> mu<PERSON>", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON>b", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.skeleton_horse.ambient": "Luukerehobune nutab", "subtitles.entity.skeleton_horse.death": "Luukerehobune sureb", "subtitles.entity.skeleton_horse.hurt": "Luukerehobusel valutab", "subtitles.entity.skeleton_horse.jump_water": "Luukerehobune hü<PERSON>", "subtitles.entity.skeleton_horse.swim": "Luukerehobune ujub", "subtitles.entity.slime.attack": "<PERSON><PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON>b", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.slime.squish": "<PERSON><PERSON> lirt<PERSON>b", "subtitles.entity.sniffer.death": "Nuusk<PERSON>", "subtitles.entity.sniffer.digging": "Nuuskija ka<PERSON>b", "subtitles.entity.sniffer.digging_stop": "Nuuskija tõuseb püsti", "subtitles.entity.sniffer.drop_seed": "Nuuskija kukutab seemne", "subtitles.entity.sniffer.eat": "Nuuskija sööb", "subtitles.entity.sniffer.egg_crack": "<PERSON>uusk<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.happy": "Nuuskija naudib", "subtitles.entity.sniffer.hurt": "Nuuskijal valutab", "subtitles.entity.sniffer.idle": "Nuuskija uriseb", "subtitles.entity.sniffer.scenting": "Nuuskija tunneb l<PERSON>a", "subtitles.entity.sniffer.searching": "Nuuskija otsib", "subtitles.entity.sniffer.sniffing": "Nuuskija nuusutab", "subtitles.entity.sniffer.step": "Nuuskija astub", "subtitles.entity.snow_golem.death": "Lumegolem sureb", "subtitles.entity.snow_golem.hurt": "Lumegolemil valutab", "subtitles.entity.snowball.throw": "<PERSON><PERSON><PERSON><PERSON> lendab", "subtitles.entity.spider.ambient": "Ämblik sisiseb", "subtitles.entity.spider.death": "Ämblik sureb", "subtitles.entity.spider.hurt": "Ämblikul valutab", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON>ti", "subtitles.entity.stray.ambient": "Eksleja kõristab", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON> trillerdab", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.death": "<PERSON><PERSON>", "subtitles.entity.tadpole.flop": "<PERSON><PERSON>", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON> ka<PERSON> su<PERSON>ks", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.tnt.primed": "Dünamiit susiseb", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.tropical_fish.flop": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.tropical_fish.hurt": "Troopilisel kalal valutab", "subtitles.entity.turtle.ambient_land": "Kilpkonn siristab", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> sureb", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>b", "subtitles.entity.turtle.egg_break": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> puruneb", "subtitles.entity.turtle.egg_crack": "Kilpkonnamuna praguneb", "subtitles.entity.turtle.egg_hatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> koorub", "subtitles.entity.turtle.hurt": "Kilpkonnal valutab", "subtitles.entity.turtle.hurt_baby": "Kilpkonnabeebil valutab", "subtitles.entity.turtle.lay_egg": "Kilpkonn muneb muna", "subtitles.entity.turtle.shamble": "Kilpkonn päntab", "subtitles.entity.turtle.shamble_baby": "Kilpkonnabeebi päntab", "subtitles.entity.turtle.swim": "Kilpkonn ujub", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON><PERSON> valutab", "subtitles.entity.villager.ambient": "Külaelanik mõmiseb", "subtitles.entity.villager.celebrate": "Külaelanik hõiskab", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> sureb", "subtitles.entity.villager.hurt": "Külaelanikul valutab", "subtitles.entity.villager.no": "Külaelanik keeldub", "subtitles.entity.villager.trade": "Külaelanik kaupleb", "subtitles.entity.villager.work_armorer": "Rü<PERSON><PERSON> töö<PERSON>b", "subtitles.entity.villager.work_butcher": "Lihunik töö<PERSON>b", "subtitles.entity.villager.work_cartographer": "Kartograaf töötab", "subtitles.entity.villager.work_cleric": "Vaimulik töötab", "subtitles.entity.villager.work_farmer": "Talunik töötab", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "subtitles.entity.villager.work_leatherworker": "Nahatöötleja töötab", "subtitles.entity.villager.work_librarian": "Raamatukoguhoidja töötab", "subtitles.entity.villager.work_mason": "Müürsepp töötab", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_toolsmith": "Tööriistasepp töötab", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.yes": "Külaelanik nõustub", "subtitles.entity.vindicator.ambient": "Õigustaja pobiseb", "subtitles.entity.vindicator.celebrate": "Õigustaja hõiskab", "subtitles.entity.vindicator.death": "Õigustaja sureb", "subtitles.entity.vindicator.hurt": "Õigustajal valutab", "subtitles.entity.wandering_trader.ambient": "Uitav kaup<PERSON>ja mõ<PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON> ka<PERSON><PERSON>", "subtitles.entity.wandering_trader.disappeared": "U<PERSON>v kaupleja ha<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON> kaupleja joob piima", "subtitles.entity.wandering_trader.drink_potion": "Uitav kaupleja joob võlujooki", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON> ka<PERSON>ja ilmub", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON> kaupleja kaupleb", "subtitles.entity.wandering_trader.yes": "Uitav kaupleja nõustub", "subtitles.entity.warden.agitated": "Valvur ohib vihaselt", "subtitles.entity.warden.ambient": "Valvur vingub", "subtitles.entity.warden.angry": "Valvu<PERSON> vihastab", "subtitles.entity.warden.attack_impact": "Valvur tabab löögi", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.warden.emerge": "Valvur kerkib pinnale", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> s<PERSON> t<PERSON>", "subtitles.entity.warden.hurt": "Valvuril valutab", "subtitles.entity.warden.listening": "Valvur mä<PERSON>b", "subtitles.entity.warden.listening_angry": "Valvur märkab vihaselt", "subtitles.entity.warden.nearby_close": "Valvur läheneb", "subtitles.entity.warden.nearby_closer": "Valvur liigub edasi", "subtitles.entity.warden.nearby_closest": "Valvur jõuab lähedale", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON> mö<PERSON>", "subtitles.entity.warden.sniff": "Valvur nuusib", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> paugutab", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> la<PERSON>", "subtitles.entity.warden.step": "Valvur astub", "subtitles.entity.warden.tendril_clicks": "Valvuri kõõlused klõpsuvad", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON><PERSON><PERSON> lendab", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON><PERSON><PERSON> purskub", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> joob", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON> viskab", "subtitles.entity.wither.ambient": "<PERSON><PERSON> v<PERSON>", "subtitles.entity.wither.death": "Wither sureb", "subtitles.entity.wither.hurt": "<PERSON><PERSON><PERSON> valutab", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON><PERSON> v<PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON>-l<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON>-lu<PERSON><PERSON> sureb", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON>-lu<PERSON><PERSON> valutab", "subtitles.entity.wolf.ambient": "<PERSON> hinge<PERSON>", "subtitles.entity.wolf.bark": "<PERSON> haugub", "subtitles.entity.wolf.death": "<PERSON>b", "subtitles.entity.wolf.growl": "<PERSON> u<PERSON>b", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> val<PERSON>b", "subtitles.entity.wolf.pant": "<PERSON> hinge<PERSON>", "subtitles.entity.wolf.shake": "<PERSON>", "subtitles.entity.wolf.whine": "<PERSON> vingub", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> u<PERSON>b vihaselt", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ründab", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.zoglin.hurt": "Zoglinil valutab", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> as<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> ohib", "subtitles.entity.zombie.attack_wooden_door": "Uks väriseb", "subtitles.entity.zombie.break_wooden_door": "Uks puruneb", "subtitles.entity.zombie.converted_to_drowned": "<PERSON>omb<PERSON> muundus uppunuks", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> sureb", "subtitles.entity.zombie.destroy_egg": "Kilpkonnamuna saab laiaks astutud", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> val<PERSON>", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON> nakatab", "subtitles.entity.zombie_horse.ambient": "Zombihobune nutab", "subtitles.entity.zombie_horse.death": "Zombihobune sureb", "subtitles.entity.zombie_horse.hurt": "Zombihobusel valutab", "subtitles.entity.zombie_villager.ambient": "Zombi-külaelanik ohib", "subtitles.entity.zombie_villager.converted": "Zombi-külaelanik lärmab", "subtitles.entity.zombie_villager.cure": "Zombi-külaelanik vingub", "subtitles.entity.zombie_villager.death": "Zombi-külaela<PERSON> sureb", "subtitles.entity.zombie_villager.hurt": "Zombi-külaelanikul valutab", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> piglin l<PERSON>", "subtitles.entity.zombified_piglin.angry": "Zombistunud piglin l<PERSON> vihaselt", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> piglin sureb", "subtitles.entity.zombified_piglin.hurt": "<PERSON>omb<PERSON><PERSON><PERSON> piglinil valutab", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON> v<PERSON>", "subtitles.event.mob_effect.raid_omen": "<PERSON> teren<PERSON> lä<PERSON>", "subtitles.event.mob_effect.trial_omen": "Kurjakuulutav katsumus terendab läheduses", "subtitles.event.raid.horn": "Kurjakuulutav sõjasarv pasundab", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> se<PERSON>ga", "subtitles.item.armor.equip_chain": "Rõngasrüü kõ<PERSON>eb", "subtitles.item.armor.equip_diamond": "Teemantrüü kolksub", "subtitles.item.armor.equip_elytra": "Kattetiivad krabisevad", "subtitles.item.armor.equip_gold": "Kuldr<PERSON><PERSON> kilksub", "subtitles.item.armor.equip_iron": "Raud<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_leather": "Nahkrüü krabiseb", "subtitles.item.armor.equip_netherite": "Netheriitrüü k<PERSON>b", "subtitles.item.armor.equip_turtle": "Kilpkonna kilp mütsatab", "subtitles.item.armor.equip_wolf": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> selga", "subtitles.item.armor.unequip_wolf": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuleb sel<PERSON>t <PERSON>ra", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.item.axe.wax_off": "<PERSON><PERSON>a tuleb maha", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.book.page_turn": "<PERSON><PERSON> krab<PERSON>b", "subtitles.item.book.put": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON> harja<PERSON>", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON><PERSON> har<PERSON><PERSON>", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON> harjamine", "subtitles.item.brush.brushing.sand.complete": "Liiva harjamine lõ<PERSON>", "subtitles.item.bucket.empty": "<PERSON><PERSON> t<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON> täitub", "subtitles.item.bucket.fill_axolotl": "Aksolotl üleskorjatud", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON> püütud", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON>", "subtitles.item.bundle.insert": "<PERSON><PERSON> saab pakitud", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON> on täis", "subtitles.item.bundle.remove_one": "<PERSON>se saab lahti pakitud", "subtitles.item.chorus_fruit.teleport": "Mängija teleporteerub", "subtitles.item.crop.plant": "<PERSON><PERSON>", "subtitles.item.crossbow.charge": "Ambu laetaks<PERSON>", "subtitles.item.crossbow.hit": "<PERSON><PERSON> tabab", "subtitles.item.crossbow.load": "<PERSON><PERSON> sa<PERSON> la<PERSON>", "subtitles.item.crossbow.shoot": "<PERSON><PERSON>", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "Hõõg-tindikott määrib", "subtitles.item.goat_horn.play": "Kitsesarv mängib", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>b maad", "subtitles.item.honey_bottle.drink": "Neelatumine", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> peale", "subtitles.item.horse_armor.unequip": "Hobuserüü tuleb seljast ära", "subtitles.item.ink_sac.use": "Tindikott määrib", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON><PERSON>", "subtitles.item.lead.untied": "<PERSON><PERSON><PERSON> lahti se<PERSON>ud", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON><PERSON> tuleb se<PERSON>", "subtitles.item.lodestone_compass.lock": "<PERSON><PERSON><PERSON><PERSON> kompass lukustub teekivile", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON> virutab", "subtitles.item.mace.smash_ground": "<PERSON><PERSON><PERSON> virutab", "subtitles.item.nether_wart.plant": "<PERSON><PERSON>", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON>", "subtitles.item.saddle.unequip": "<PERSON><PERSON> tuleb sel<PERSON>", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "Labidas tasandab", "subtitles.item.spyglass.stop_using": "Pikksilm kitseneb", "subtitles.item.spyglass.use": "Pikksilm la<PERSON>eb", "subtitles.item.totem.use": "Tootem aktiveerub", "subtitles.item.trident.hit": "Kolmhark torkab", "subtitles.item.trident.hit_ground": "Kolmhark vibreerib", "subtitles.item.trident.return": "Kolmhark pöördub tagasi", "subtitles.item.trident.riptide": "Kolmhark vilksab", "subtitles.item.trident.throw": "Kolmhark kolksub", "subtitles.item.trident.thunder": "<PERSON><PERSON><PERSON><PERSON> kõu raks<PERSON>b", "subtitles.item.wolf_armor.break": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.wolf_armor.crack": "Hu<PERSON><PERSON><PERSON><PERSON> mõ<PERSON>", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON><PERSON><PERSON> saab kahju", "subtitles.item.wolf_armor.repair": "Hundirüü sai parandatud", "subtitles.particle.soul_escape": "<PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON> j<PERSON>", "subtitles.ui.hud.bubble_pop": "Õhuindikaator väheneb", "subtitles.ui.loom.take_result": "<PERSON><PERSON>el<PERSON><PERSON> said ka<PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "Kivilõikur sai kasutatud", "subtitles.weather.rain": "<PERSON><PERSON><PERSON> sajab", "symlink_warning.message": "Maailmade laadimine ka<PERSON>, kus on sümboolsed lingid ei pruugi olla turvaline, kui sa ei tea täpselt, mida teed. Lisainfo jaoks palun külasta %s.", "symlink_warning.message.pack": "Sümboolsete linkidega pakkide laadimine ei pruugi olla turvaline, kui sa ei tea täpselt, mida teed. Lisainfo jaoks palun külasta %s.", "symlink_warning.message.world": "Maailmade laadimine ka<PERSON>, kus on sümboolsed lingid ei pruugi olla turvaline, kui sa ei tea täpselt, mida teed. Lisainfo jaoks palun külasta %s.", "symlink_warning.more_info": "Rohkem teavet", "symlink_warning.title": "Maailma ka<PERSON> sisaldab sümboolseid linke", "symlink_warning.title.pack": "<PERSON><PERSON><PERSON> pak<PERSON>/pakid sisal<PERSON>/sisalda<PERSON>d sü<PERSON>eid linke", "symlink_warning.title.world": "Maailma ka<PERSON> sisaldab sümboolseid linke", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON> k<PERSON>", "team.collision.pushOtherTeams": "Lükka teisi meeskondi", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON> oma me<PERSON>da", "team.notFound": "Tundmatu meeskond \"%s\"", "team.visibility.always": "<PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Peida teiste meeskondade jaoks", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON> oma me<PERSON>konna jaoks", "team.visibility.never": "<PERSON><PERSON> k<PERSON>", "telemetry.event.advancement_made.description": "Edasijõudmise hankimise konteksti mõistmine aitab meil mängu arengut paremini mõista ja parendada.", "telemetry.event.advancement_made.title": "Ed<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "telemetry.event.game_load_times.description": "See sünd<PERSON> aitab meil teada saada, mida tuleb käivitusjõudluses parendada, mõõtes käivitusfaaside täitmisaegu.", "telemetry.event.game_load_times.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.optional": "%s (valikuline)", "telemetry.event.optional.disabled": "%s (valikuline) - keelatud", "telemetry.event.performance_metrics.description": "Minecrafti üldise jõudlusprofiili tundmine aitab meil häälestada ja optimeerida mängu erinevate seadmete ja operatsioonisüsteemide jaoks.\nMänguversioon on lisatud, aitamaks meil võrrelda Minecrafti uute versioonide jõudlusprofiili.", "telemetry.event.performance_metrics.title": "Jõudlusnäitajad", "telemetry.event.required": "%s (n<PERSON><PERSON>ud)", "telemetry.event.world_load_times.description": "<PERSON><PERSON> j<PERSON> on o<PERSON><PERSON> mõista, kui kaua kestab maailmaga liitumine ja kuidas see aja jooksul muutub. Näiteks, kui me lisame uusi funktsioone või teeme suuremaid tehnilisi muudatusi, peame nägema millist mõ<PERSON> see laadimisaegadele avaldab.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "Teades, kuidas mängijad Minecrafti mängivad (näiteks mängurežiim, moditud klient või server ja mänguversioon), saame mängu uuendused keskenduda valdkondadele, mis mängijatele kõige rohkem korda lähevad.\nSündmus \"maailm laaditud\" on paaris sündmusega \"maailm laadimata\", ar<PERSON><PERSON>aks, kui kaua mänguseanss on kestnud.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_unloaded.description": "See sünd<PERSON> on paaris sündmusega \"maailm laaditud\", a<PERSON><PERSON><PERSON><PERSON>, kui kaua mänguse<PERSON> on kestnud.\n<PERSON><PERSON><PERSON> (nii sekundites kui tiksudes) arvestataks<PERSON> ma<PERSON><PERSON><PERSON> l<PERSON> (avamenüüsse naasmine, serverist lahkumine).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON> aeg (tiksudes)", "telemetry.property.advancement_id.title": "Edasijõudmise ID", "telemetry.property.client_id.title": "Kliendi-ID", "telemetry.property.client_modded.title": "<PERSON><PERSON> k<PERSON> on moditud", "telemetry.property.dedicated_memory_kb.title": "Eraldatud mälu (kB)", "telemetry.property.event_timestamp_utc.title": "Sündmuse ajatempel (UTC)", "telemetry.property.frame_rate_samples.title": "Kaadrisageduse näidised (FPS)", "telemetry.property.game_mode.title": "Mängurežiim", "telemetry.property.game_version.title": "Mänguversioon", "telemetry.property.launcher_name.title": "<PERSON><PERSON> nimi", "telemetry.property.load_time_bootstrap_ms.title": "Eellaadimise aeg (millisekundites)", "telemetry.property.load_time_loading_overlay_ms.title": "<PERSON><PERSON> (millisekundites)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON><PERSON> enne akna a<PERSON> (millisekundites)", "telemetry.property.load_time_total_time_ms.title": "<PERSON><PERSON> (millisekundites)", "telemetry.property.minecraft_session_id.title": "Minecrafti seansi-ID", "telemetry.property.new_world.title": "<PERSON><PERSON> on uus maailm", "telemetry.property.number_of_samples.title": "Näidiste arv", "telemetry.property.operating_system.title": "Operatsioonisüsteem", "telemetry.property.opt_in.title": "<PERSON><PERSON> seda liiki and<PERSON><PERSON> j<PERSON>", "telemetry.property.platform.title": "Platvorm", "telemetry.property.realms_map_content.title": "<PERSON><PERSON> ma<PERSON>ma sisu (minimängu nimi)", "telemetry.property.render_distance.title": "Nähtavuskaugus", "telemetry.property.render_time_samples.title": "Renderdusaja näidised", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON><PERSON> la<PERSON> (sekundit)", "telemetry.property.server_modded.title": "Kas server on moditud", "telemetry.property.server_type.title": "<PERSON><PERSON>", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON><PERSON> (tiksu)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON><PERSON><PERSON>u<PERSON> (RAM)", "telemetry.property.user_id.title": "Kasutaja-ID", "telemetry.property.world_load_time_ms.title": "<PERSON><PERSON><PERSON> (millisekundit)", "telemetry.property.world_session_id.title": "Maailma seansi-ID", "telemetry_info.button.give_feedback": "<PERSON>", "telemetry_info.button.privacy_statement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry_info.button.show_data": "<PERSON><PERSON><PERSON> oma and<PERSON>d", "telemetry_info.opt_in.description": "Nõustun valikuliste telemeetriaandmete saatmisega", "telemetry_info.property_title": "<PERSON><PERSON><PERSON><PERSON><PERSON> and<PERSON>", "telemetry_info.screen.description": "<PERSON><PERSON><PERSON> and<PERSON><PERSON> kogumine aitab meil Minecrafti täiustada, su<PERSON>s meid mängijate jaoks olulistesse suundadesse.\nVõid ka täiendavat tagasisidet saata, et aidata meil pidevalt Minecrafti täiustada.", "telemetry_info.screen.title": "Telemee<PERSON>a and<PERSON>", "test.error.block_property_mismatch": "<PERSON><PERSON><PERSON>, et atribuut %s on %s aga oli %s", "test.error.block_property_missing": "Plokiatribuut puudub, oodati et atribuut %s on %s", "test.error.entity_property": "Olem %s eba<PERSON><PERSON>tus testis: %s", "test.error.entity_property_details": "Olem %s ebaõnnestus testis: %s, oodati: %s, oli: %s", "test.error.expected_block": "Oodati plokki %s, saadi %s", "test.error.expected_block_tag": "Oodati plokki sildiga #%s, saadi %s", "test.error.expected_container_contents": "Konteiner peaks sisaldama: %s", "test.error.expected_container_contents_single": "Konteiner peaks sisaldama ühte: %s", "test.error.expected_empty_container": "Konteiner peaks olema tühi", "test.error.expected_entity": "Oodati %s", "test.error.expected_entity_around": "Oodati %s olemasolu ümber %s, %s, %s", "test.error.expected_entity_count": "Oodati %s %s-tüüpi olemit, leiti %s", "test.error.expected_entity_data": "<PERSON><PERSON><PERSON>, et olemiandmed on: %s aga olid: %s", "test.error.expected_entity_data_predicate": "Olemi %s andmed ei klapi", "test.error.expected_entity_effect": "<PERSON><PERSON><PERSON>, et %s omab mõju %s %s", "test.error.expected_entity_having": "<PERSON><PERSON> peaks sisaldama %s", "test.error.expected_entity_holding": "Olem peaks hoidma %s", "test.error.expected_entity_in_test": "Oodati %s olemasolu testis", "test.error.expected_entity_not_touching": "Ei oodanud %s puutumast %s, %s, %s (suhteline: %s, %s, %s)", "test.error.expected_entity_touching": "Oodati %s puutumast %s, %s, %s (suhteline: %s, %s, %s)", "test.error.expected_item": "Oodati %s-tüüpi eset", "test.error.expected_items_count": "Oodati %s %s-tüüpi eset, leiti %s", "test.error.fail": "Ebaõnnestumise tingimused tä<PERSON>d", "test.error.invalid_block_type": "Leiti ootamatu plokitüüp: %s", "test.error.missing_block_entity": "Puuduv plokiolem", "test.error.position": "%s asukohas %s, %s, %s (suhteline: %s, %s, %s) tiksul %s", "test.error.sequence.condition_already_triggered": "Tingimus juba käivitatud asukohas %s", "test.error.sequence.condition_not_triggered": "Tingimust ei käivitatud", "test.error.sequence.invalid_tick": "Sobimatu tiks õnnestus: oodati %s", "test.error.sequence.not_completed": "Test aegus enne jada l<PERSON>", "test.error.set_biome": "Testi jaoks bioomi määramine e<PERSON>nnestus", "test.error.spawn_failure": "Olemi %s loomine e<PERSON>", "test.error.state_not_equal": "Ebasobiv olek. Oodati %s aga oli %s", "test.error.structure.failure": "Teststruktuuri asetamine %s jaoks ebaõnnestus", "test.error.tick": "%s tiksul %s", "test.error.ticking_without_structure": "Tiksumise test enne struktuuri asetamist", "test.error.timeout.no_result": "Ei <PERSON>ud ega ebaõnnestunud %s tik<PERSON> jooksul", "test.error.timeout.no_sequences_finished": "Ühtegi jada ei lõpetatud %s tiksu jooksul", "test.error.too_many_entities": "Oodati vaid ühe %s olemasolu ümber %s, %s, %s aga leiti %s", "test.error.unexpected_block": "<PERSON><PERSON> o<PERSON>, et plokk on %s", "test.error.unexpected_entity": "Ei oodanud %s olemasolu", "test.error.unexpected_item": "Ei oodanud %s-tüüpi eset", "test.error.unknown": "Tundmatu sisemine viga: %s", "test.error.value_not_equal": "<PERSON><PERSON><PERSON>, et %s on %s aga oli %s", "test.error.wrong_block_entity": "Vale plokiolemi tüüp: %s", "test_block.error.missing": "Teststruktuurist puudub plokk %s", "test_block.error.too_many": "Liiga palju %s-plokke", "test_block.invalid_timeout": "Sobimatu <PERSON> (%s) - peab olema positiivne arv tikse", "test_block.message": "Sõnum:", "test_block.mode.accept": "Nõustu", "test_block.mode.fail": "<PERSON><PERSON><PERSON>", "test_block.mode.log": "<PERSON><PERSON>", "test_block.mode.start": "<PERSON><PERSON><PERSON>", "test_block.mode_info.accept": "Nõustumisrežiim - nõustu testi (osalise) eduga", "test_block.mode_info.fail": "Nurjamisrežiim - kukuta test läbi", "test_block.mode_info.log": "Logimisrežiim - logi üks sõnum", "test_block.mode_info.start": "Alustusrežiim - testi al<PERSON>", "test_instance.action.reset": "Lähtesta ja laadi", "test_instance.action.run": "<PERSON><PERSON> ja käivita", "test_instance.action.save": "Sal<PERSON><PERSON> struk<PERSON>", "test_instance.description.batch": "Kogum: %s", "test_instance.description.failed": "Ebaõnnestus: %s", "test_instance.description.function": "Funktsioon: %s", "test_instance.description.invalid_id": "Sobimatu testi-ID", "test_instance.description.no_test": "<PERSON><PERSON><PERSON> testi ei leidu", "test_instance.description.structure": "Struktuur: %s", "test_instance.description.type": "Tüüp: %s", "test_instance.type.block_based": "Plokipõhine test", "test_instance.type.function": "Sisseehitatud funktsiooni test", "test_instance_block.entities": "Olemid:", "test_instance_block.error.no_test": "Testeksemplari asukohas %s, %s, %s ei saa käitada, kuna omab määramata testi", "test_instance_block.error.no_test_structure": "Testeksemplari asukohas %s, %s, %s ei saa käitada, kuna sel puudub testistruktuur", "test_instance_block.error.unable_to_save": "Testistruktuuri malli ei saa testeksemplari jaoks asukohas %s, %s, %s salvestada", "test_instance_block.invalid": "[sobimatu]", "test_instance_block.reset_success": "Lähtestamine õnnestus testi jaoks: %s", "test_instance_block.rotation": "Pööre:", "test_instance_block.size": "<PERSON><PERSON>uk<PERSON><PERSON> suurus", "test_instance_block.starting": "Testi %s käivitamine", "test_instance_block.test_id": "Testeksemplari ID", "title.32bit.deprecation": "32-bitine süsteem tuvastatud: see v<PERSON><PERSON> takistada sul tule<PERSON>us mäng<PERSON>, kuna 64-bitine süsteem muutub kohustuslikuks!", "title.32bit.deprecation.realms": "Minecraft nõuab varsti 64-bitist <PERSON><PERSON><PERSON><PERSON><PERSON>, mis takistab sul selles seadmes mängimist ni<PERSON> <PERSON><PERSON> kasutamist. Mistahes Realmsi tellimused pead tühistama käsitsi.", "title.32bit.deprecation.realms.check": "<PERSON>ra kuva seda ekraani uuesti", "title.32bit.deprecation.realms.header": "32-bit<PERSON> s<PERSON><PERSON> tuvastatud", "title.credits": "Autoriõigus Mojang AB. Mitte levitada!", "title.multiplayer.disabled": "Mit<PERSON>kmäng on keelatud. <PERSON>lun kontrolli oma Microsofti konto seadeid.", "title.multiplayer.disabled.banned.name": "Sa pead oma nime <PERSON>ra muutma, enne kui saad võ<PERSON> mängida", "title.multiplayer.disabled.banned.permanent": "Sinu konto on igaveseks võrgumängust blokeeritud", "title.multiplayer.disabled.banned.temporary": "Sinu konto on ajutiselt võrgumängust blokeeritud", "title.multiplayer.lan": "mitmikmäng (LAN)", "title.multiplayer.other": "mitmikmäng (3. osapoole server)", "title.multiplayer.realms": "mitmikmäng (Realms)", "title.singleplayer": "Üksikmäng", "translation.test.args": "%s %s", "translation.test.complex": "Eesliide, %s%2$s uuesti %s ja %1$s, lõpuks %s ja lisaks %1$s uuesti!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "tere %", "translation.test.invalid2": "tere %s", "translation.test.none": "<PERSON>re, ma<PERSON>m!", "translation.test.world": "ma<PERSON>m", "trim_material.minecraft.amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Netheriidimate<PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstone-materjal", "trim_material.minecraft.resin": "Vaigumaterjal", "trim_pattern.minecraft.bolt": "Pol<PERSON> r<PERSON>", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON>", "trim_pattern.minecraft.flow": "<PERSON><PERSON>", "trim_pattern.minecraft.host": "Võõrustaja rüüornament", "trim_pattern.minecraft.raiser": "Tõstja rüüorn<PERSON>", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.shaper": "Voolija rüüornament", "trim_pattern.minecraft.silence": "Vaikuse rüüornament", "trim_pattern.minecraft.snout": "Kärsa rüüornament", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "Loode rüüornament", "trim_pattern.minecraft.vex": "Pahandaja <PERSON>", "trim_pattern.minecraft.ward": "Eestkostja rüüornament", "trim_pattern.minecraft.wayfinder": "Teeleid<PERSON>", "trim_pattern.minecraft.wild": "Metsik rüüornament", "tutorial.bundleInsert.description": "Esemete lisamiseks paremklõpsa", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON> kompsu", "tutorial.craft_planks.description": "Retsept<PERSON><PERSON><PERSON> a<PERSON>b", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON> puulaudu", "tutorial.find_tree.description": "<PERSON><PERSON><PERSON> seda, et koguda puitu", "tutorial.find_tree.title": "Leia üks puu", "tutorial.look.description": "<PERSON><PERSON><PERSON><PERSON>ks hiirt", "tutorial.look.title": "<PERSON><PERSON><PERSON> ringi", "tutorial.move.description": "Hüppa klahviga %s", "tutorial.move.title": "Liigu klahvidega %s, %s, %s, %s", "tutorial.open_inventory.description": "Vajuta %s", "tutorial.open_inventory.title": "<PERSON> o<PERSON>", "tutorial.punch_tree.description": "Hoia klahvi %s", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON><PERSON> puu", "tutorial.socialInteractions.description": "Avamiseks vajuta %s", "tutorial.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upgrade.minecraft.netherite_upgrade": "Netheriiditäiendus"}