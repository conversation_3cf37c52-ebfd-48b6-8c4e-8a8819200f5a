{"accessibility.onboarding.accessibility.button": "tiky<PERSON> lo su'u lo pilno ku frili...", "accessibility.onboarding.screen.narrator": "lai enter ku tekla ku cumgau lo tcidu", "accessibility.onboarding.screen.title": "fi'i'e lai Minecraft\n\nxu do cumgau lo pru'uxfri .i xu do stuvi'e fi lo tikygau", "addServer.add": "mulno", "addServer.enterIp": ".i samci'ejudri li", "addServer.enterName": ".i le samci'ejudri ku se cmene zo'oi", "addServer.resourcePack": "da<PERSON><PERSON> xragri", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "kakne", "addServer.resourcePack.prompt": "detyselyla<PERSON>ri", "addServer.title": ".i ko tikygau le samci'ejudri datni", "advMode.command": "Console Command", "advMode.mode": "tadji", "advMode.mode.auto": "rapli", "advMode.mode.autoexec.bat": "Always Active", "advMode.mode.conditional": "cnata", "advMode.mode.redstone": "mokca", "advMode.mode.redstoneTriggered": "nitcu me la redston.", "advMode.mode.sequence": "se<PERSON><PERSON>'e", "advMode.mode.unconditional": "na cnata", "advMode.notAllowed": "Must be an opped player in creative mode", "advMode.notEnabled": "Command blocks are not enabled on this server", "advMode.previousOutput": "Previous Output", "advMode.setCommand": "Set Console Command for Block", "advMode.setCommand.success": "Command set: %s", "advMode.trackOutput": "Track output", "advMode.triggering": "Triggering", "advMode.type": "klesi", "advancement.advancementNotFound": "na djuno fi lo so'i nandu %s", "advancements.adventure.adventuring_time.description": ".i facki ro lo rarvanbi", "advancements.adventure.adventuring_time.title": "capli'u temci", "advancements.adventure.arbalistic.description": ".i ko catra lu'o lu'a mu danlu klesi ta'i tu'a pa danti be lo seljicyce'a", "advancements.adventure.arbalistic.title": "certu celga'a", "advancements.adventure.avoid_vibration.description": ".i ko mipydzu co jibni jenai se ganse lo .skylk. zei ganse .a lo cnomanbadypre", "advancements.adventure.avoid_vibration.title": "lo mipygre ku se vamji 100", "advancements.adventure.blowback.description": "do catra lo bifyda'u ku lo nu do minra lo brife bakfu", "advancements.adventure.blowback.title": "xruii brife do", "advancements.adventure.brush_armadillo.description": "do pilno lo burcu ku lo nu do jajgau lo dapsodi piltapla ku lo dapsodi", "advancements.adventure.brush_armadillo.title": "xu se ranti jdari lo dapsodi piltapla", "advancements.adventure.bullseye.description": ".i darxi lo midju be lo teryre'o bliku be vu lo gutci be li su'o 30", "advancements.adventure.bullseye.title": "nu satci cecla", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": ".i ko zbasu lo selja'i patxu loi vo staku selpo'i", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "racy<PERSON>'i nuncikre", "advancements.adventure.crafters_crafting_crafters.description": "do jibni lo nu lo zbasu ku zbasu lo zbasu", "advancements.adventure.crafters_crafting_crafters.title": "ref<PERSON>i finti", "advancements.adventure.fall_from_world_height.description": ".i ko farlu lo dziraipau po'e lo munje goi ko'a lo galraipau po'e ko'a ja'e lo nu do jmive", "advancements.adventure.fall_from_world_height.title": "kevna .e rajroksfe", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": ".i ko snada co bandu lo so<PERSON>zda lo <PERSON>gunta", "advancements.adventure.hero_of_the_village.title": "sosyzda vudvri", "advancements.adventure.honey_block_slide.description": ".i sakli lo melmi bliku", "advancements.adventure.honey_block_slide.title": "nu snipa tcini", "advancements.adventure.kill_a_mob.description": ".i catra lo cizda'u bradi", "advancements.adventure.kill_a_mob.title": "cizda'u kalte", "advancements.adventure.kill_all_mobs.description": ".i catra pa lo cizda'u bradi", "advancements.adventure.kill_all_mobs.title": ".i lo cizda'u cu selkalte", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "catra cizda'u va lo sculk blimajgau", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "sculk preja", "advancements.adventure.lighten_up.description": "do gasnu lo nu lo ka'amru ku guska lo tunka tergu'i kei ko .i go'i du'u banro gusni", "advancements.adventure.lighten_up.title": "<PERSON><PERSON> g<PERSON>ni", "advancements.adventure.lightning_rod_with_villager_no_fire.description": ".i ko fanta lo nu nardji jenca lo selsosyzda kei secau lo nu jelca", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "sukydicybandu", "advancements.adventure.minecraft_trials_edition.description": "Step foot in a Trial Chamber", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Trial(s) Edition", "advancements.adventure.ol_betsy.description": ".i ko celgau lo seljicyce'a", "advancements.adventure.ol_betsy.title": "<PERSON>u zei .betsis.", "advancements.adventure.overoverkill.description": "Deal 50 hearts of damage in a single hit using the Mace", "advancements.adventure.overoverkill.title": "Over-Overkill", "advancements.adventure.play_jukebox_in_meadows.description": ".i ko mivgau lo sasfoi ri'a lo nu zgike lo selzgi bliku", "advancements.adventure.play_jukebox_in_meadows.title": "zgi<PERSON>'i sance", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "ko merli lo ni sinxa tsali pe lo selja'i ckukajna sepi'o lo karbi", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "ka cukta vlipa", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": ".i nu capli'u gi'e facki gi'e damba", "advancements.adventure.root.title": "nuncapli'u", "advancements.adventure.salvage_sherd.description": ".i ko brulu'i lo selsenpi bliku tezu'e lo nu cpacu lo staku selpo'i", "advancements.adventure.salvage_sherd.title": "sinma lo pu selklu", "advancements.adventure.shoot_arrow.description": ".i ko celgunta fu lo celga'a", "advancements.adventure.shoot_arrow.title": ".i lo nu fargau", "advancements.adventure.sleep_in_bed.description": ".i ko sipna sepi'o lo ckana tezu'e lo nu galfi lo veljbe", "advancements.adventure.sleep_in_bed.title": ".i lo titysne", "advancements.adventure.sniper_duel.description": ".i ko catra lo bogygreku poi darno do la'u lo mitre be li su'omuno", "advancements.adventure.sniper_duel.title": ".i sat<PERSON><PERSON>ta", "advancements.adventure.spyglass_at_dragon.description": ".i ko catlu lo .endr. zei drakono sepi'o lo zgablaci", "advancements.adventure.spyglass_at_dragon.title": "xu tu vinji", "advancements.adventure.spyglass_at_ghast.description": ".i ko catlu la .gast. sepi'o lo zgablaci", "advancements.adventure.spyglass_at_ghast.title": "xu tu var<PERSON><PERSON><PERSON>i", "advancements.adventure.spyglass_at_parrot.description": ".i ko catlu lo spitaki sepi'o lo zgablaci", "advancements.adventure.spyglass_at_parrot.title": "xu tu cipni", "advancements.adventure.summon_iron_golem.description": ".i ko zukte lo nu klacpe lo tirse remsmimi'i kei lo nu sidju fi lo nu bandu lo sosyzda", "advancements.adventure.summon_iron_golem.title": ".i pilno lo sidju", "advancements.adventure.throw_trident.description": ".i ko renro darxi lo cibyterfroxa'i\nto .e'unai do cliva renro lo po'o do xarci", "advancements.adventure.throw_trident.title": ".i selre'o selxamsku", "advancements.adventure.totem_of_undying.description": ".i ko rivbi lo nunmro tu'a lo nalmro nindode", "advancements.adventure.totem_of_undying.title": ".i pagre lo zilmro", "advancements.adventure.trade.description": ".i snada canja fo lo sosyselzda", "advancements.adventure.trade.title": ".ui ti xamgu nunve'u", "advancements.adventure.trade_at_world_height.description": ".i ko punji jimte galtu gi'e canja fo lo selsosyzda", "advancements.adventure.trade_at_world_height.title": "tarci. canja", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": ".i ko zbasu fi lo jimyzba termo'a pe lo dabycalku jadni pe lo kondrudi'u .e lo xajyzbi .e lo cutybo'u .e lo pifydi'u .e lo kamsma .e lo cmapalci .e lo ctaru .e lo lutygi'a", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Smithing with Style", "advancements.adventure.trim_with_any_armor_pattern.description": ".i ko zbasu lo selja'i dabycalku sepi'o lo t<PERSON>zbajbu", "advancements.adventure.trim_with_any_armor_pattern.title": "Crafting a New Look", "advancements.adventure.two_birds_one_arrow.description": ".i ko catra loi re cteru'i sepi'o pa pagre celga'a", "advancements.adventure.two_birds_one_arrow.title": "co'e loi re cipni lo pa danti", "advancements.adventure.under_lock_and_key.description": "Unlock a Vault with a Trial Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.adventure.use_lodestone.title": "Country Lode, Take Me Home", "advancements.adventure.very_very_frightening.description": ".i lidgau pa lo selsosyzda", "advancements.adventure.very_very_frightening.title": ".i selte'a tcetce", "advancements.adventure.voluntary_exile.description": ".i ko catra lo jatna po'e lo gutybe'e\n.inaja .e'uru'e do ze'aru'i darno lo sosyzda", "advancements.adventure.voluntary_exile.title": "sezlivbai", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": ".i ko lo pumsi'e cu cadzu .y. gi'enai setca be fa zi'o", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "simsa lo ractu lo ka linto", "advancements.adventure.who_needs_rockets.description": "Use a Wind Charge to launch yourself upward 8 blocks", "advancements.adventure.who_needs_rockets.title": "Who Needs Rockets?", "advancements.adventure.whos_the_pillager_now.description": ".i ko sfasa lo vilcpapre fi'o tratadji ri", "advancements.adventure.whos_the_pillager_now.title": "do ji tu vilcpepre", "advancements.empty": "There doesn't seem to be anything here...", "advancements.end.dragon_breath.description": ".i ko clugau lo blaci botpi loi drakono selva'u", "advancements.end.dragon_breath.title": "You Need a Mint", "advancements.end.dragon_egg.description": ".i ko cpacu lo drakono sovda", "advancements.end.dragon_egg.title": "The Next Generation", "advancements.end.elytra.description": ".i ko facki fi lo gairna'i", "advancements.end.elytra.title": ".i lo tsani cu jimtre", "advancements.end.enter_end_gateway.description": ".i ko rivli'a lo daplu", "advancements.end.enter_end_gateway.title": "Remote Getaway", "advancements.end.find_end_city.description": ".i ko nerkla .ije ma ba fasnu", "advancements.end.find_end_city.title": "The City at the End of the Game", "advancements.end.kill_dragon.description": "di'ai", "advancements.end.kill_dragon.title": ".i zifygau lo .endre munje", "advancements.end.levitate.description": ".i ko se gunta la .cylkr. gi'e mo'iga'u fulta la'u lo muno bliku", "advancements.end.levitate.title": ".i zanji'u ga'u ta", "advancements.end.respawn_dragon.description": ".i ko rapmivygau la .endre drakono", "advancements.end.respawn_dragon.title": "The End... Again...", "advancements.end.root.description": ".ijonai nuncfa xu", "advancements.end.root.title": "la me .end", "advancements.husbandry.allay_deliver_cake_to_note_block.description": ".i ko falpujgau lo djuru'i lo titnanba lo tonga bliku", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Birthday Song", "advancements.husbandry.allay_deliver_item_to_player.description": ".i lo djuru'i cu bevri lo dacti ko", "advancements.husbandry.allay_deliver_item_to_player.title": "do ja'a se pendo mi", "advancements.husbandry.axolotl_in_a_bucket.description": ".i ko kavbu lo .axlte sepi'o lo baktu", "advancements.husbandry.axolotl_in_a_bucket.title": "The Cutest Predator", "advancements.husbandry.balanced_diet.description": ".i ko citka lu'a ro cidja klesi .iju ri xamgu do", "advancements.husbandry.balanced_diet.title": "A Balanced Diet", "advancements.husbandry.breed_all_animals.description": ".i ko rorgau lu'a ro danlu klesi", "advancements.husbandry.breed_all_animals.title": ".i relsi'u", "advancements.husbandry.breed_an_animal.description": ".i ko rorgau lo danlu", "advancements.husbandry.breed_an_animal.title": ".i lo spitaki joi lo volratcu", "advancements.husbandry.complete_catalogue.description": ".i ko tolcicygau lo mlatu be roda", "advancements.husbandry.complete_catalogue.title": "A Complete Catalogue", "advancements.husbandry.feed_snifflet.description": ".i ko ctigau lo cifyzbisakci", "advancements.husbandry.feed_snifflet.title": "Little Sniffs", "advancements.husbandry.fishy_business.description": ".i kavbu lo finpe", "advancements.husbandry.fishy_business.title": ".i xlamu'ijijnu terkagni", "advancements.husbandry.froglights.description": ".i ko setca lu'o lu'a ro pipybanfytergu'i klesi le do sorcu", "advancements.husbandry.froglights.title": "With Our Powers Combined!", "advancements.husbandry.kill_axolotl_target.description": ".i ko joi lo .axlte cu damba gi'e jinga", "advancements.husbandry.kill_axolotl_target.title": "The Healing Power of Friendship!", "advancements.husbandry.leash_all_frog_variants.description": ".i ko lasna lo pipybanfi be roda", "advancements.husbandry.leash_all_frog_variants.title": "When the Squad Hops into Town", "advancements.husbandry.make_a_sign_glow.description": ".i ko camgau lo selci'a be fi lu'a lo lertapla klesi po'u do'au", "advancements.husbandry.make_a_sign_glow.title": "Glow and Behold!", "advancements.husbandry.netherite_hoe.description": ".i ko zbasu fi lo terplixa ku joi lo lingote be la .nedyrit. fe lo xagmau gi'e krefu pensi lo se cuxna be fi lo'i do nunji'e", "advancements.husbandry.netherite_hoe.title": "Serious Dedication", "advancements.husbandry.obtain_sniffer_egg.description": ".i ko cpacu lo zbisakci sovda", "advancements.husbandry.obtain_sniffer_egg.title": "Smells Interesting", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Stay Hydrated!", "advancements.husbandry.plant_any_sniffer_seed.description": ".i ko sombo lo zbisakci tsiju", "advancements.husbandry.plant_any_sniffer_seed.title": "Planting the Past", "advancements.husbandry.plant_seed.description": ".i ko sombo lo tsiju gi'e zgana lo nu ri banro", "advancements.husbandry.plant_seed.title": "A Seedy Place", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "sekrefu citno", "advancements.husbandry.ride_a_boat_with_a_goat.description": ".i ko joi lo kanba cu selblo gi'e fulta", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Whatever Floats Your Goat!", "advancements.husbandry.root.description": ".i le munje zo'u joigi so'i da pendo gi so'i de cidja", "advancements.husbandry.root.title": "Husbandry", "advancements.husbandry.safely_harvest_honey.description": ".i ko pilno lo gikfagri lo nu do fegygau lo bifce nagi'e cpacu lo melmi lo bicyzda sepi'o lo blaci botpi", "advancements.husbandry.safely_harvest_honey.title": "nu vitke lo bifce", "advancements.husbandry.silk_touch_nest.description": ".i ko muvgau lo bicyzda poi se nenri ci bifce ku'o sepi'o lo milxe pencu", "advancements.husbandry.silk_touch_nest.title": "nu bifce zdacne", "advancements.husbandry.tactical_fishing.description": ".i ko kavbu lo finpe .y. secau lo fipkalte grana", "advancements.husbandry.tactical_fishing.title": "Tactical Fishing", "advancements.husbandry.tadpole_in_a_bucket.description": ".i ko kavbu lo verbanfi sepi'o lo baktu", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": ".i tolcicgau pa lo danlu", "advancements.husbandry.tame_an_animal.title": ".i ba ze'e lo raixau pendo", "advancements.husbandry.wax_off.description": ".i ko guska lo lakse lo tunka bliku", "advancements.husbandry.wax_off.title": "Wax Off", "advancements.husbandry.wax_on.description": ".i ko punji lo lakse lo tunka bliku", "advancements.husbandry.wax_on.title": "Wax On", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": ".i loi ro cnetcini cu tcini ko", "advancements.nether.all_effects.title": ".i mi'o klama ti ti'a ma", "advancements.nether.all_potions.description": ".i loi ro mafsodva cnetcini cu tcini ko", "advancements.nether.all_potions.title": "<PERSON><PERSON>", "advancements.nether.brew_potion.description": ".i ko jukpa lo mafsodva", "advancements.nether.brew_potion.title": "zvajbi xum<PERSON>zai", "advancements.nether.charge_respawn_anchor.description": ".i ro va'e nendu'a lo rapyjbe tutci", "advancements.nether.charge_respawn_anchor.title": "ka me'i so roi jmive", "advancements.nether.create_beacon.description": ".i ko zbasu gi'e punji vau lo barda tergu'i", "advancements.nether.create_beacon.title": "ca'e gusni", "advancements.nether.create_full_beacon.description": ".i ko gasnu lo nu lo barda tergu'i cu traji lo ni vlipa", "advancements.nether.create_full_beacon.title": "Beaconator", "advancements.nether.distract_piglin.description": ".i xlura lo xajypre fo lo solji", "advancements.nether.distract_piglin.title": "nu se xlura lo mircai", "advancements.nether.explore_nether.description": ".i facki fi ro dzimu'e mivy<PERSON>bi", "advancements.nether.explore_nether.title": "nu vitke fi lo glare", "advancements.nether.fast_travel.description": ".i ko litru la .nedr. la'u lo mitre be li zeki'o bei fo le sfemu'e", "advancements.nether.fast_travel.title": "Subspace Bubble", "advancements.nether.find_bastion.description": ".i nerkla lo mradi'u festi", "advancements.nether.find_bastion.title": "nu vedli lo tcepru", "advancements.nether.find_fortress.description": ".i ko gutnerkla lo .nedr. zei badydi'u", "advancements.nether.find_fortress.title": ".i pa lo x<PERSON>ce badydi'u", "advancements.nether.get_wither_skull.description": ".i ko cpacu lo sedbo'u be lo .uidr. zei bogy<PERSON>u", "advancements.nether.get_wither_skull.title": "pruxi je selte'a bogygreku", "advancements.nether.loot_bastion.description": ".i ko lebna fi lo vasru po lo mradi'u festi", "advancements.nether.loot_bastion.title": "ka se bradi lo xarju", "advancements.nether.netherite_armor.description": ".i zbasu lo mulno ke dzimujyjinme badycalku", "advancements.nether.netherite_armor.title": "ka se gacri lo tcepru", "advancements.nether.obtain_ancient_debris.description": ".i cpacu lo tcepru spisa", "advancements.nether.obtain_ancient_debris.title": "ka zvafa'i lo selmipri", "advancements.nether.obtain_blaze_rod.description": ".i ko lebna lo fagri grana lo fagda'u", "advancements.nether.obtain_blaze_rod.title": ".i kelci lo fagri", "advancements.nether.obtain_crying_obsidian.description": ".i cpacu lo klaku fagblaci", "advancements.nether.obtain_crying_obsidian.title": "ka cortu lo kanla", "advancements.nether.return_to_sender.description": ".i ko jai daspo la gast. sepi'o lo fagri bolci", "advancements.nether.return_to_sender.title": ".i minra fi lo benji", "advancements.nether.ride_strider.description": ".i se marce lo fagdzu sepi'o lo cizra bo gumri joi grana", "advancements.nether.ride_strider.title": "ka cpana lo tuple bloti", "advancements.nether.ride_strider_in_overworld_lava.description": ".i ko ba'e ba'e ba'e ve'u vi'i litru lo likro'i lalxu pe le sfemu'e lo fagdzu", "advancements.nether.ride_strider_in_overworld_lava.title": "do morji terdi", "advancements.nether.root.description": ".i ko klagau lo crisa taxfu", "advancements.nether.root.title": "neter", "advancements.nether.summon_wither.description": ".i ko klacpe la .uidr.", "advancements.nether.summon_wither.title": "Withering Heights", "advancements.nether.uneasy_alliance.description": ".i ko la .gast. cu nurxru la .nedr. gi'e snura bevri le sfemu'e .y. gi'e catra", "advancements.nether.uneasy_alliance.title": "narbirti jambe'e", "advancements.nether.use_lodestone.description": ".i ko pilno lo makfartci tu'a lo maksi", "advancements.nether.use_lodestone.title": "nu maksi", "advancements.progress": "%s/%s", "advancements.sad_label": ".ui nai", "advancements.story.cure_zombie_villager.description": "ko gasnu lo nu lo mivmro selsosyzda goi ko'a ruble kei gi'e mikce ko'a", "advancements.story.cure_zombie_villager.title": ".i mivym<PERSON><PERSON>'i mikce", "advancements.story.deflect_arrow.description": "lo badgau cu bandu ko lo danti", "advancements.story.deflect_arrow.title": ".i fau'u nalfau fi'o cabdei", "advancements.story.enchant_item.description": "makfa lo dacti ko sepi'o lo makfa jubme", "advancements.story.enchant_item.title": ".i termafyze'a prenu", "advancements.story.enter_the_end.description": "ko nerkla lo me la .end. vorme", "advancements.story.enter_the_end.title": ".i fanmo xu", "advancements.story.enter_the_nether.description": "ko zbasu gi'e punji lo fagri gi'e nerkla vau lo me la .nedr. vorme", "advancements.story.enter_the_nether.title": ".i ma'a conkla<PERSON>cu", "advancements.story.follow_ender_eye.description": ".i jersi lo .endre kanla", "advancements.story.follow_ender_eye.title": ".i bimgre kanla", "advancements.story.form_obsidian.description": "ko cpacu lo xek<PERSON>blaci", "advancements.story.form_obsidian.title": ".i bisybaktu nuntalsa", "advancements.story.iron_tools.description": "ko xagzengau lo tunmru", "advancements.story.iron_tools.title": ".i ti tirse tunymru xunai", "advancements.story.lava_bucket.description": "ko clugau lo baktu lo likro'i", "advancements.story.lava_bucket.title": ".i ti glacte", "advancements.story.mine_diamond.description": "se cpacu tabjme", "advancements.story.mine_diamond.title": ".i .ue tabjme", "advancements.story.mine_stone.description": "ko kalte lo rokci sepi'o lo cnino tunmru", "advancements.story.mine_stone.title": "lo se cedra rokci", "advancements.story.obtain_armor.description": "lo dabycalku be fi lo tirse cu bandu ko", "advancements.story.obtain_armor.title": "ko dasni", "advancements.story.root.description": "lo midju joi lo lisri vu'o po'e le nunkei", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "lo dabycalku be fi lo tabjme cu nurxru lo kamji'e", "advancements.story.shiny_gear.title": "ko gasnu lo nu calku mi lo tabjme", "advancements.story.smelt_iron.description": ".i majgau lo lingote tirse", "advancements.story.smelt_iron.title": "ko cpacu lo tutci", "advancements.story.upgrade_tools.description": "ko lo tsamau tunymru ku zbasu", "advancements.story.upgrade_tools.title": "cpacu lo se xagzengau", "advancements.toast.challenge": "Challenge Complete!", "advancements.toast.goal": "Goal Reached!", "advancements.toast.task": "ju'i ruc<PERSON><PERSON> tadji", "argument.anchor.invalid": "Invalid entity anchor position %s", "argument.angle.incomplete": ".i na mulno .i kanpe tu'a lu'e lo pa jganu", "argument.angle.invalid": "na<PERSON>ra j<PERSON>u", "argument.block.id.invalid": ".i nalselju'o blilei fa %s", "argument.block.property.duplicate": "Property '%s' can only be set once for block %s", "argument.block.property.invalid": "Block %s does not accept '%s' for %s property", "argument.block.property.novalue": "Expected value for property '%s' on block %s", "argument.block.property.unclosed": "Expected closing ] for block state properties", "argument.block.property.unknown": "Block %s does not have property '%s'", "argument.block.tag.disallowed": "Tags aren't allowed here, only actual blocks", "argument.color.invalid": "Unknown color '%s'", "argument.component.invalid": "Invalid chat component: %s", "argument.criteria.invalid": ".i zo'oi %s cmene no ve farvi selkai", "argument.dimension.invalid": "Unknown dimension '%s'", "argument.double.big": "Double must not be more than %s, found %s", "argument.double.low": "Double must not be less than %s, found %s", "argument.entity.invalid": "Invalid name or UUID", "argument.entity.notfound.entity": "pu sispe'i no lo dacti", "argument.entity.notfound.player": "pu sispe'i no lo dracykei", "argument.entity.options.advancements.description": "Players with advancements", "argument.entity.options.distance.description": "Distance to entity", "argument.entity.options.distance.negative": "Distance cannot be negative", "argument.entity.options.dx.description": "Entities between x and x + dx", "argument.entity.options.dy.description": "Entities between y and y + dy", "argument.entity.options.dz.description": "Entities between z and z + dz", "argument.entity.options.gamemode.description": "Players with game mode", "argument.entity.options.inapplicable": "Option '%s' isn't applicable here", "argument.entity.options.level.description": "Experience level", "argument.entity.options.level.negative": "Level shouldn't be negative", "argument.entity.options.limit.description": "Maximum number of entities to return", "argument.entity.options.limit.toosmall": "Limit must be at least 1", "argument.entity.options.mode.invalid": "Invalid or unknown game mode '%s'", "argument.entity.options.name.description": "Entity name", "argument.entity.options.nbt.description": "Entities with NBT", "argument.entity.options.predicate.description": "selfi'i selbri", "argument.entity.options.scores.description": "Entities with scores", "argument.entity.options.sort.description": "Sort the entities", "argument.entity.options.sort.irreversible": "Invalid or unknown sort type '%s'", "argument.entity.options.tag.description": "Entities with tag", "argument.entity.options.team.description": "Entities on team", "argument.entity.options.type.description": "Entities of type", "argument.entity.options.type.invalid": "Invalid or unknown entity type '%s'", "argument.entity.options.unknown": "Unknown option '%s'", "argument.entity.options.unterminated": "Expected end of options", "argument.entity.options.valueless": "Expected value for option '%s'", "argument.entity.options.x.description": "xy diklo", "argument.entity.options.x_rotation.description": "<PERSON><PERSON><PERSON>'s x rotation", "argument.entity.options.y.description": "ybu diklo", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON>'s y rotation", "argument.entity.options.z.description": "zy diklo", "argument.entity.selector.allEntities": "All entities", "argument.entity.selector.allPlayers": "ro lo kelci", "argument.entity.selector.missing": "Missing selector type", "argument.entity.selector.nearestEntity": "Nearest entity", "argument.entity.selector.nearestPlayer": "j<PERSON><PERSON> kelci", "argument.entity.selector.not_allowed": "Selector not allowed", "argument.entity.selector.randomPlayer": "cunso kelci", "argument.entity.selector.self": "Current entity", "argument.entity.selector.unknown": "Unknown selector type '%s'", "argument.entity.toomany": "Only one entity is allowed, but the provided selector allows more than one", "argument.enum.invalid": "Invalid value \"%s\"", "argument.float.big": "Float must not be more than %s, found %s", "argument.float.low": "Float must not be less than %s, found %s", "argument.gamemode.invalid": "Unknown game mode: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "Invalid ID", "argument.id.unknown": "Unknown ID: %s", "argument.integer.big": "Integer must not be more than %s, found %s", "argument.integer.low": "Integer must not be less than %s, found %s", "argument.item.id.invalid": ".i nalselju'o dacti fa %s", "argument.item.tag.disallowed": "Tags aren't allowed here, only actual items", "argument.literal.incorrect": "Expected literal %s", "argument.long.big": "Long must not be more than %s, found %s", "argument.long.low": "Long must not be less than %s, found %s", "argument.message.too_long": "Chat message was too long (%s > maximum %s characters)", "argument.nbt.array.invalid": "Invalid array type '%s'", "argument.nbt.array.mixed": "na setca %s nenri %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "Expected key", "argument.nbt.expected.value": "Expected value", "argument.nbt.list.mixed": "na setca %s stekoni %s", "argument.nbt.trailing": "Unexpected trailing data", "argument.player.entities": "Only players may be affected by this command, but the provided selector includes entities", "argument.player.toomany": "Only one player is allowed, but the provided selector allows more than one", "argument.player.unknown": "That player does not exist", "argument.pos.missing.double": "Expected a coordinate", "argument.pos.missing.int": "Expected a block position", "argument.pos.mixed": "Cannot mix world & local coordinates (everything must either use ^ or not)", "argument.pos.outofbounds": "That position is outside the allowed boundaries.", "argument.pos.outofworld": "That position is out of this world!", "argument.pos.unloaded": "That position is not loaded", "argument.pos2d.incomplete": "Incomplete (expected 2 coordinates)", "argument.pos3d.incomplete": "Incomplete (expected 3 coordinates)", "argument.range.empty": "Expected value or range of values", "argument.range.ints": "Only whole numbers allowed, not decimals", "argument.range.swapped": "Min cannot be bigger than max", "argument.resource.invalid_type": "Element '%s' has wrong type '%s' (expected '%s')", "argument.resource.not_found": "Can't find element '%s' of type '%s'", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: %s", "argument.resource_or_id.invalid": "Invalid id or tag", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Tag '%s' has wrong type '%s' (expected '%s')", "argument.resource_tag.not_found": "Can't find tag '%s' of type '%s'", "argument.rotation.incomplete": "Incomplete (expected 2 coordinates)", "argument.scoreHolder.empty": "No relevant score holders could be found", "argument.scoreboardDisplaySlot.invalid": "Unknown display slot '%s'", "argument.style.invalid": "Invalid style: %s", "argument.time.invalid_tick_count": "The tick count must be non-negative", "argument.time.invalid_unit": "Invalid unit", "argument.time.tick_count_too_low": "The tick count must not be less than %s, found %s", "argument.uuid.invalid": "jax<PERSON><PERSON>ra nacme'e", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": ".i nalselju'o blitcita fa %s", "arguments.function.tag.unknown": "Unknown function tag '%s'", "arguments.function.unknown": "Unknown function %s", "arguments.item.component.expected": "Expected item component", "arguments.item.component.malformed": "Malformed '%s' component: '%s'", "arguments.item.component.repeated": "Item component '%s' was repeated, but only one value can be specified", "arguments.item.component.unknown": "Unknown item component '%s'", "arguments.item.malformed": "Malformed item: '%s'", "arguments.item.overstacked": "%s can only stack up to %s", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": ".i nalselju'o dairtcita fa %s", "arguments.nbtpath.node.invalid": "Invalid NBT path element", "arguments.nbtpath.nothing_found": "Found no elements matching %s", "arguments.nbtpath.too_deep": "Resulting NBT too deeply nested", "arguments.nbtpath.too_large": "Resulting NBT too large", "arguments.objective.notFound": "Unknown scoreboard objective '%s'", "arguments.objective.readonly": "Scoreboard objective '%s' is read-only", "arguments.operation.div0": "Cannot divide by zero", "arguments.operation.invalid": "Invalid operation", "arguments.swizzle.invalid": "Invalid swizzle, expected combination of 'x', 'y' and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Armor", "attribute.name.armor_toughness": "<PERSON><PERSON>", "attribute.name.attack_damage": "Attack Damage", "attribute.name.attack_knockback": "Attack Knockback", "attribute.name.attack_speed": "Attack Speed", "attribute.name.block_break_speed": "Block Break Speed", "attribute.name.block_interaction_range": "Block Interaction Range", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Entity Interaction Range", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.flying_speed": "Flying Speed", "attribute.name.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.armor": "da<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON> kamtsali", "attribute.name.generic.attack_damage": "gunta ka<PERSON> ", "attribute.name.generic.attack_knockback": "gunta niltikpa", "attribute.name.generic.attack_speed": "gunta kams<PERSON>ra", "attribute.name.generic.block_interaction_range": "ni bliku jitro darno", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "ni muvdai jitro darno", "attribute.name.generic.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.generic.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.generic.flying_speed": "vofli nilsutra", "attribute.name.generic.follow_range": "mivje'i nilcla", "attribute.name.generic.gravity": "Gravity", "attribute.name.generic.jump_strength": "Jump Strength", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON> muvdu <PERSON>", "attribute.name.generic.luck": "Luck", "attribute.name.generic.max_absorption": "Max Absorption", "attribute.name.generic.max_health": "bramau nilk<PERSON>'o", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "Speed", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "Safe Fall Distance", "attribute.name.generic.scale": "nilbra", "attribute.name.generic.step_height": "ni cadzu ke galtu frica", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "Gravity", "attribute.name.horse.jump_strength": "xirma plipe ka<PERSON>", "attribute.name.jump_strength": "Jump Strength", "attribute.name.knockback_resistance": "Knockback Resistance", "attribute.name.luck": "Luck", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Max Health", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Movement Efficiency", "attribute.name.movement_speed": "Speed", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Block Break Speed", "attribute.name.player.block_interaction_range": "Block Interaction Range", "attribute.name.player.entity_interaction_range": "Entity Interaction Range", "attribute.name.player.mining_efficiency": "Mining Efficiency", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "Scale", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "Zombie Reinforcements", "biome.minecraft.badlands": "xlatu'a", "biome.minecraft.bamboo_jungle": "bambyfoi", "biome.minecraft.basalt_deltas": "fagma'a tumla", "biome.minecraft.beach": "xaskoi", "biome.minecraft.birch_forest": "ric<PERSON>i eptula", "biome.minecraft.cherry_grove": "r<PERSON><PERSON><PERSON><PERSON> ric<PERSON>i", "biome.minecraft.cold_ocean": "lenku xamsi", "biome.minecraft.crimson_forest": "zirxu'e ricfoi", "biome.minecraft.dark_forest": "manku ricfoi", "biome.minecraft.deep_cold_ocean": "lenku conxamsi", "biome.minecraft.deep_dark": "nity<PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "dunja conxamsi", "biome.minecraft.deep_lukewarm_ocean": "mlilen<PERSON> conxamsi", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.desert": "sudy<PERSON>'a", "biome.minecraft.dripstone_caves": "le kevzda digyligykamju", "biome.minecraft.end_barrens": "end sudypintumla", "biome.minecraft.end_highlands": "fanmo galtu'a", "biome.minecraft.end_midlands": "fanmo mij<PERSON>'a", "biome.minecraft.eroded_badlands": "kalsa xlatu'a", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON> r<PERSON>", "biome.minecraft.forest": "ric<PERSON><PERSON>", "biome.minecraft.frozen_ocean": "dunja xamsi", "biome.minecraft.frozen_peaks": "bisli cmana", "biome.minecraft.frozen_river": "dunja rirxe", "biome.minecraft.grove": "snime tumla r<PERSON>i", "biome.minecraft.ice_spikes": "bistu'a", "biome.minecraft.jagged_peaks": "kinli <PERSON>ana", "biome.minecraft.jungle": "cicricfoi", "biome.minecraft.lukewarm_ocean": "mlilenku xamsi", "biome.minecraft.lush_caves": "le kevzda so'i spati", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "sasfoi", "biome.minecraft.mushroom_fields": "<PERSON>ri tumla", "biome.minecraft.nether_wastes": "dzi<PERSON>'e cicfoi", "biome.minecraft.ocean": "xamsi", "biome.minecraft.old_growth_birch_forest": "rictu'a clani ricrbetula", "biome.minecraft.old_growth_pine_taiga": "rictu'a galtu konku'u", "biome.minecraft.old_growth_spruce_taiga": "rictu'a tricrpino", "biome.minecraft.pale_garden": "Pale Garden", "biome.minecraft.plains": "pintu'a", "biome.minecraft.river": "rirxe", "biome.minecraft.savanna": "glare pinloi", "biome.minecraft.savanna_plateau": "glama'a", "biome.minecraft.small_end_islands": "cmalu fanmo daplu", "biome.minecraft.snowy_beach": "snime x<PERSON>oi", "biome.minecraft.snowy_plains": "snimypintu'a", "biome.minecraft.snowy_slopes": "sni<PERSON><PERSON>'o", "biome.minecraft.snowy_taiga": "snime ke kondu'u ricfoi", "biome.minecraft.soul_sand_valley": "pruxi <PERSON><PERSON>i", "biome.minecraft.sparse_jungle": "sanra cic<PERSON><PERSON>i", "biome.minecraft.stony_peaks": "kin<PERSON> r<PERSON>", "biome.minecraft.stony_shore": "r<PERSON><PERSON> xaskoi", "biome.minecraft.sunflower_plains": "solspa pintu'a", "biome.minecraft.swamp": "cimtu'a", "biome.minecraft.taiga": "ku'u<PERSON>oi", "biome.minecraft.the_end": "me la .end.", "biome.minecraft.the_void": "kunti", "biome.minecraft.warm_ocean": "mli<PERSON><PERSON> xamsi", "biome.minecraft.warped_forest": "cizra r<PERSON>i", "biome.minecraft.windswept_forest": "cunso tumla r<PERSON>i", "biome.minecraft.windswept_gravelly_hills": "cunso cmaro'i cmana", "biome.minecraft.windswept_hills": "cunso <PERSON>", "biome.minecraft.windswept_savanna": "cunso <PERSON> pinloi", "biome.minecraft.wooded_badlands": "rictu'a xlatu'a", "block.minecraft.acacia_button": ".atka<PERSON>ia batke", "block.minecraft.acacia_door": "atkaciia vrogai", "block.minecraft.acacia_fence": "atkaciia garbi'u", "block.minecraft.acacia_fence_gate": "atkaciia gar<PERSON>mvrogai", "block.minecraft.acacia_hanging_sign": ".atkaciia ke dandu lertapla", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.acacia_log": ".atka<PERSON>ia stani", "block.minecraft.acacia_planks": "atkaciia mudri tanbo", "block.minecraft.acacia_pressure_plate": "atkaciia da<PERSON>ry<PERSON>la", "block.minecraft.acacia_sapling": "atkaci<PERSON> c<PERSON>u", "block.minecraft.acacia_sign": "at<PERSON><PERSON><PERSON> sinxa", "block.minecraft.acacia_slab": ".at<PERSON><PERSON><PERSON> x<PERSON>li", "block.minecraft.acacia_stairs": "atka<PERSON><PERSON> serti", "block.minecraft.acacia_trapdoor": ".atka<PERSON><PERSON>", "block.minecraft.acacia_wall_hanging_sign": "bitmu ke .atkaciia ke dandu lertapla", "block.minecraft.acacia_wall_sign": "atkaciia ke bitmu sinxa", "block.minecraft.acacia_wood": "atka<PERSON><PERSON> mudri", "block.minecraft.activator_rail": "actigau garna", "block.minecraft.air": "vacri", "block.minecraft.allium": "citsluni", "block.minecraft.amethyst_block": ".amtisti b<PERSON>u", "block.minecraft.amethyst_cluster": ".amtisti girzu", "block.minecraft.ancient_debris": "tcepru spisa", "block.minecraft.andesite": "rok<PERSON><PERSON><PERSON>'i", "block.minecraft.andesite_slab": "r<PERSON><PERSON><PERSON><PERSON>'i xabybli", "block.minecraft.andesite_stairs": "rok<PERSON>rand<PERSON>'i serti", "block.minecraft.andesite_wall": "rok<PERSON><PERSON><PERSON>'i bitmu", "block.minecraft.anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Attached Melon Stem", "block.minecraft.attached_pumpkin_stem": "Attached P<PERSON><PERSON> Stem", "block.minecraft.azalea": "r<PERSON><PERSON><PERSON>odend<PERSON>", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "lo cmablabyxrula", "block.minecraft.bamboo": "bambu", "block.minecraft.bamboo_block": "bambu bliku", "block.minecraft.bamboo_button": "bambu batke", "block.minecraft.bamboo_door": "bambu vrogai", "block.minecraft.bamboo_fence": "bambu garbi'u", "block.minecraft.bamboo_fence_gate": "bambu gar<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_hanging_sign": "bambu ke dandu lertapla", "block.minecraft.bamboo_mosaic": "morna ke bambu bliku", "block.minecraft.bamboo_mosaic_slab": "morna ke bambu xabybli", "block.minecraft.bamboo_mosaic_stairs": "morna ke bambu serti", "block.minecraft.bamboo_planks": "bambu tanbo", "block.minecraft.bamboo_pressure_plate": "bambu da<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_sapling": "cifybambu", "block.minecraft.bamboo_sign": "bambu le<PERSON>la", "block.minecraft.bamboo_slab": "bambu x<PERSON>bli", "block.minecraft.bamboo_stairs": "bambu serti", "block.minecraft.bamboo_trapdoor": "bambu lo<PERSON>vrogai", "block.minecraft.bamboo_wall_hanging_sign": "bitmu ke bambu ke dandu lertapla", "block.minecraft.bamboo_wall_sign": "bitmu ke bambu lertapla", "block.minecraft.banner.base.black": "x<PERSON><PERSON> lanci", "block.minecraft.banner.base.blue": "blanu lanci", "block.minecraft.banner.base.brown": "bunre lanci", "block.minecraft.banner.base.cyan": "cicna lanci", "block.minecraft.banner.base.gray": "grusi lanci", "block.minecraft.banner.base.green": "crino lanci", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON> lanci", "block.minecraft.banner.base.light_gray": "labyrus lanci", "block.minecraft.banner.base.lime": "pel<PERSON>'o lanci", "block.minecraft.banner.base.magenta": "nuki lanci", "block.minecraft.banner.base.orange": "narju lanci", "block.minecraft.banner.base.pink": "xun<PERSON><PERSON> lanci", "block.minecraft.banner.base.purple": "zirpu lanci", "block.minecraft.banner.base.red": "x<PERSON>re lanci", "block.minecraft.banner.base.white": "blabi lanci", "block.minecraft.banner.base.yellow": "pelxu lanci", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.border.blue": "blanu kurgreku", "block.minecraft.banner.border.brown": "bunre kurgreku", "block.minecraft.banner.border.cyan": "c<PERSON>na k<PERSON>u", "block.minecraft.banner.border.gray": "g<PERSON>i k<PERSON>u", "block.minecraft.banner.border.green": "crino kurgreku", "block.minecraft.banner.border.light_blue": "labybla kurgreku", "block.minecraft.banner.border.light_gray": "lab<PERSON><PERSON><PERSON> kurgreku", "block.minecraft.banner.border.lime": "p<PERSON><PERSON>'o kurgreku", "block.minecraft.banner.border.magenta": "nukni k<PERSON>u", "block.minecraft.banner.border.orange": "nar<PERSON> k<PERSON>u", "block.minecraft.banner.border.pink": "<PERSON>ka kurgreku", "block.minecraft.banner.border.purple": "zir<PERSON> kurgreku", "block.minecraft.banner.border.red": "x<PERSON><PERSON> korbi", "block.minecraft.banner.border.white": "blabi kurgreku", "block.minecraft.banner.border.yellow": "pelxu kurgreku", "block.minecraft.banner.bricks.black": "morna lo'e xekri bliku", "block.minecraft.banner.bricks.blue": "morna lo'e blanu bliku", "block.minecraft.banner.bricks.brown": "morna lo'e bunre bliku", "block.minecraft.banner.bricks.cyan": "morna lo'e cicna bliku", "block.minecraft.banner.bricks.gray": "morna lo'e grusi bliku", "block.minecraft.banner.bricks.green": "morna lo'e crino bliku", "block.minecraft.banner.bricks.light_blue": "morna lo'e labybla bliku", "block.minecraft.banner.bricks.light_gray": "morna lo'e labgrusi bliku", "block.minecraft.banner.bricks.lime": "morna lo'e pelri'o bliku", "block.minecraft.banner.bricks.magenta": "morna lo'e nukni bliku", "block.minecraft.banner.bricks.orange": "morna lo'e narju bliku", "block.minecraft.banner.bricks.pink": "morna lo'e penka bliku", "block.minecraft.banner.bricks.purple": "morna lo'e zirpu bliku", "block.minecraft.banner.bricks.red": "morna lo'e xunre bliku", "block.minecraft.banner.bricks.white": "morna lo'e blabi bliku", "block.minecraft.banner.bricks.yellow": "morna lo'e pelxu bliku", "block.minecraft.banner.circle.black": "x<PERSON><PERSON>", "block.minecraft.banner.circle.blue": "blanu cukla", "block.minecraft.banner.circle.brown": "bunre cukla", "block.minecraft.banner.circle.cyan": "cicna cu<PERSON>", "block.minecraft.banner.circle.gray": "grusi cu<PERSON>", "block.minecraft.banner.circle.green": "crino cukla", "block.minecraft.banner.circle.light_blue": "labybla cukla", "block.minecraft.banner.circle.light_gray": "labgrusi cukla", "block.minecraft.banner.circle.lime": "pelri'o cukla", "block.minecraft.banner.circle.magenta": "nukni cu<PERSON>", "block.minecraft.banner.circle.orange": "narju <PERSON>", "block.minecraft.banner.circle.pink": "penka cukla", "block.minecraft.banner.circle.purple": "zirpu cukla", "block.minecraft.banner.circle.red": "x<PERSON>re cukla", "block.minecraft.banner.circle.white": "blabi cukla", "block.minecraft.banner.circle.yellow": "pelxu cukla", "block.minecraft.banner.creeper.black": "lo xekri ri'orfrupo'a flira", "block.minecraft.banner.creeper.blue": "lo blanu ri'orfrupo'a flira", "block.minecraft.banner.creeper.brown": "lo bunre ri'orfrupo'a flira", "block.minecraft.banner.creeper.cyan": "lo cicna ri'orfrupo'a flira", "block.minecraft.banner.creeper.gray": "lo grusi ri'orfrupo'a flira", "block.minecraft.banner.creeper.green": "lo crino ri'orfrupo'a flira", "block.minecraft.banner.creeper.light_blue": "lo labybla ri'orfrupo'a flira", "block.minecraft.banner.creeper.light_gray": "lo labyrus ri'orfrupo'a flira", "block.minecraft.banner.creeper.lime": "lo pelri'o ri'orfrupo'a flira", "block.minecraft.banner.creeper.magenta": "lo nukni ri'orfrupo'a flira", "block.minecraft.banner.creeper.orange": "lo narju ri'orfrupo'a flira", "block.minecraft.banner.creeper.pink": "lo xunblabi ri'orfrupo'a flira", "block.minecraft.banner.creeper.purple": "lo zirpu ri'orfrupo'a flira", "block.minecraft.banner.creeper.red": "lo xunre ri'orfrupo'a flira", "block.minecraft.banner.creeper.white": "lo blabi ri'orfrupo'a flira", "block.minecraft.banner.creeper.yellow": "lo pelxu ri'orfrupo'a flira", "block.minecraft.banner.cross.black": "x<PERSON><PERSON> salpo k<PERSON>tai", "block.minecraft.banner.cross.blue": "blanu salpo kuctai", "block.minecraft.banner.cross.brown": "bunre salpo kuctai", "block.minecraft.banner.cross.cyan": "cicna salpo k<PERSON>tai", "block.minecraft.banner.cross.gray": "grusi salpo kuctai", "block.minecraft.banner.cross.green": "crino salpo kuctai", "block.minecraft.banner.cross.light_blue": "labybla salpo k<PERSON>tai", "block.minecraft.banner.cross.light_gray": "labgrusi salpo kuctai", "block.minecraft.banner.cross.lime": "pel<PERSON>'o salpo kuctai", "block.minecraft.banner.cross.magenta": "nukni salpo kuctai", "block.minecraft.banner.cross.orange": "narju salpo k<PERSON>tai", "block.minecraft.banner.cross.pink": "penka salpo kuctai", "block.minecraft.banner.cross.purple": "zirpu salpo kuctai", "block.minecraft.banner.cross.red": "x<PERSON><PERSON> salpo kuctai", "block.minecraft.banner.cross.white": "blabi salpo kuctai", "block.minecraft.banner.cross.yellow": "pelxu salpo kuctai", "block.minecraft.banner.curly_border.black": "morna lo'e xek<PERSON> k<PERSON>", "block.minecraft.banner.curly_border.blue": "morna lo'e blanu kor<PERSON>u", "block.minecraft.banner.curly_border.brown": "morna lo'e bunre kor<PERSON>u", "block.minecraft.banner.curly_border.cyan": "morna lo'e cicna k<PERSON>u", "block.minecraft.banner.curly_border.gray": "morna lo'e grusi k<PERSON>u", "block.minecraft.banner.curly_border.green": "morna lo'e crino kor<PERSON>u", "block.minecraft.banner.curly_border.light_blue": "morna lo'e labybla korbyru<PERSON>u", "block.minecraft.banner.curly_border.light_gray": "morna lo'e labgrusi korby<PERSON>u", "block.minecraft.banner.curly_border.lime": "morna lo'e pelri'o kor<PERSON><PERSON>u", "block.minecraft.banner.curly_border.magenta": "morna lo'e nukni k<PERSON>u", "block.minecraft.banner.curly_border.orange": "morna lo'e narju k<PERSON>u", "block.minecraft.banner.curly_border.pink": "morna lo'e penka korbyru<PERSON>u", "block.minecraft.banner.curly_border.purple": "morna lo'e zirpu kor<PERSON>u", "block.minecraft.banner.curly_border.red": "morna lo'e xunre k<PERSON>u", "block.minecraft.banner.curly_border.white": "morna lo'e blabi kor<PERSON><PERSON>u", "block.minecraft.banner.curly_border.yellow": "morna lo'e pelxu korbyrufsu", "block.minecraft.banner.diagonal_left.black": "x<PERSON><PERSON> x<PERSON>ba zu<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_left.blue": "blanu xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.brown": "bunre xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.cyan": "cicna xadba zu<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_left.gray": "g<PERSON>i xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.green": "crino xadba zuls<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.light_blue": "labybla xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.light_gray": "labgrusi xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.lime": "p<PERSON><PERSON>'o xadba zulsa'osri", "block.minecraft.banner.diagonal_left.magenta": "nukni xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.orange": "narju x<PERSON>ba zu<PERSON>ri", "block.minecraft.banner.diagonal_left.pink": "penka xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.purple": "zir<PERSON> xadba zu<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_left.red": "x<PERSON><PERSON> xadba zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_left.white": "blabi xadba zu<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_left.yellow": "pelxu xadba zuls<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.black": "x<PERSON><PERSON> x<PERSON>ri", "block.minecraft.banner.diagonal_right.blue": "blanu x<PERSON><PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_right.brown": "bunre x<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.cyan": "c<PERSON>na x<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_right.gray": "grusi x<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.green": "crino x<PERSON><PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.light_blue": "labybla x<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.light_gray": "labgrusi x<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.lime": "pelri'o x<PERSON><PERSON>'osri", "block.minecraft.banner.diagonal_right.magenta": "nukni x<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_right.orange": "narju x<PERSON>ri", "block.minecraft.banner.diagonal_right.pink": "penka x<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_right.purple": "zir<PERSON> x<PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_right.red": "x<PERSON><PERSON> x<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_right.white": "blabi x<PERSON><PERSON>o<PERSON>ri", "block.minecraft.banner.diagonal_right.yellow": "pelxu xab<PERSON>'o<PERSON>ri", "block.minecraft.banner.diagonal_up_left.black": "xekri pritu d<PERSON>rai<PERSON>u cib<PERSON>i", "block.minecraft.banner.diagonal_up_left.blue": "blanu pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.brown": "bunre pritu dziraipau cib<PERSON>i", "block.minecraft.banner.diagonal_up_left.cyan": "cicna pritu dziraipau cib<PERSON>i", "block.minecraft.banner.diagonal_up_left.gray": "grusi pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.green": "crino pritu dziraipau cibjgatai", "block.minecraft.banner.diagonal_up_left.light_blue": "labybla pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.light_gray": "labgrusi pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.lime": "pelri'o pritu d<PERSON>raipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.magenta": "nukni pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.orange": "narju pritu dziraipau cib<PERSON>i", "block.minecraft.banner.diagonal_up_left.pink": "penka pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.purple": "zirpu pritu dziraipau cibj<PERSON>i", "block.minecraft.banner.diagonal_up_left.red": "xunre pritu dziraipau cib<PERSON>i", "block.minecraft.banner.diagonal_up_left.white": "blabi pritu dziraipau cib<PERSON>i", "block.minecraft.banner.diagonal_up_left.yellow": "pelxu pritu dziraipau cibjgatai", "block.minecraft.banner.diagonal_up_right.black": "x<PERSON><PERSON> zun<PERSON> d<PERSON> cib<PERSON>i", "block.minecraft.banner.diagonal_up_right.blue": "blanu zunle d<PERSON>rai<PERSON>u cib<PERSON>i", "block.minecraft.banner.diagonal_up_right.brown": "bunre zunle d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.cyan": "cicna zunle d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.gray": "grusi zunle d<PERSON>rai<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.green": "crino zunle d<PERSON>u cibjgatai", "block.minecraft.banner.diagonal_up_right.light_blue": "labybla zunle d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.light_gray": "labg<PERSON>i zunle d<PERSON>raipau cibjgatai", "block.minecraft.banner.diagonal_up_right.lime": "p<PERSON><PERSON>'o zunle d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.magenta": "nukni zunle d<PERSON>rai<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.orange": "narju zun<PERSON> d<PERSON> cib<PERSON>i", "block.minecraft.banner.diagonal_up_right.pink": "zirpu zunle d<PERSON>rai<PERSON>u cibjgatai", "block.minecraft.banner.diagonal_up_right.purple": "zirpu zunle d<PERSON>rai<PERSON>u cibjgatai", "block.minecraft.banner.diagonal_up_right.red": "x<PERSON><PERSON> zunle d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.white": "blabi zunle d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.diagonal_up_right.yellow": "pelxu zunle dziraipau cibj<PERSON>i", "block.minecraft.banner.flow.black": "Black Flow", "block.minecraft.banner.flow.blue": "Blue Flow", "block.minecraft.banner.flow.brown": "Brown Flow", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Gray Flow", "block.minecraft.banner.flow.green": "Green Flow", "block.minecraft.banner.flow.light_blue": "Light Blue Flow", "block.minecraft.banner.flow.light_gray": "Light Gray Flow", "block.minecraft.banner.flow.lime": "Lime Flow", "block.minecraft.banner.flow.magenta": "Magenta Flow", "block.minecraft.banner.flow.orange": "Orange Flow", "block.minecraft.banner.flow.pink": "Pink Flow", "block.minecraft.banner.flow.purple": "Purple Flow", "block.minecraft.banner.flow.red": "Red Flow", "block.minecraft.banner.flow.white": "White Flow", "block.minecraft.banner.flow.yellow": "Yellow Flow", "block.minecraft.banner.flower.black": "x<PERSON><PERSON> x<PERSON>la tarmi", "block.minecraft.banner.flower.blue": "blanu xrula tarmi", "block.minecraft.banner.flower.brown": "bunre x<PERSON>la tarmi", "block.minecraft.banner.flower.cyan": "cicna xrula tarmi", "block.minecraft.banner.flower.gray": "g<PERSON>i xrula tarmi", "block.minecraft.banner.flower.green": "crino x<PERSON>la tarmi", "block.minecraft.banner.flower.light_blue": "labybla x<PERSON>la tarmi", "block.minecraft.banner.flower.light_gray": "labyrus x<PERSON>la tarmi", "block.minecraft.banner.flower.lime": "p<PERSON><PERSON>'o x<PERSON>la tarmi", "block.minecraft.banner.flower.magenta": "nukni x<PERSON>la tarmi", "block.minecraft.banner.flower.orange": "narju x<PERSON>la tarmi", "block.minecraft.banner.flower.pink": "xun<PERSON>bi xrula tarmi", "block.minecraft.banner.flower.purple": "zirpu xrula tarmi", "block.minecraft.banner.flower.red": "x<PERSON><PERSON> xrula tarmi", "block.minecraft.banner.flower.white": "blabi x<PERSON>la tarmi", "block.minecraft.banner.flower.yellow": "pelxu xrula tarmi", "block.minecraft.banner.globe.black": "x<PERSON><PERSON> bolci tarmi", "block.minecraft.banner.globe.blue": "blanu bolci tarmi", "block.minecraft.banner.globe.brown": "bunre bolci tarmi", "block.minecraft.banner.globe.cyan": "cicna bolci tarmi", "block.minecraft.banner.globe.gray": "grusi bolci tarmi", "block.minecraft.banner.globe.green": "crino bolci tarmi", "block.minecraft.banner.globe.light_blue": "labybla bolci tarmi", "block.minecraft.banner.globe.light_gray": "lab<PERSON><PERSON><PERSON> bolci tarmi", "block.minecraft.banner.globe.lime": "pelri'o bolci tarmi", "block.minecraft.banner.globe.magenta": "nukni bolci tarmi", "block.minecraft.banner.globe.orange": "narju bolci tarmi", "block.minecraft.banner.globe.pink": "xun<PERSON>bi bolci tarmi", "block.minecraft.banner.globe.purple": "zirpu bolci tarmi", "block.minecraft.banner.globe.red": "x<PERSON><PERSON> sel<PERSON>i", "block.minecraft.banner.globe.white": "blabi bolci tarmi", "block.minecraft.banner.globe.yellow": "pelxu bolci tarmi", "block.minecraft.banner.gradient.black": "le xekri ca zenba lo ka ruble", "block.minecraft.banner.gradient.blue": "le blanu ca zenba lo ka ruble", "block.minecraft.banner.gradient.brown": "le bunre ca zenba lo ka ruble", "block.minecraft.banner.gradient.cyan": "le cicna ca zenba lo ka ruble", "block.minecraft.banner.gradient.gray": "le grusi ca zenba lo ka ruble", "block.minecraft.banner.gradient.green": "le crino ca zenba lo ka ruble", "block.minecraft.banner.gradient.light_blue": "le labybla ca zenba lo ka ruble", "block.minecraft.banner.gradient.light_gray": "le labrus ca zenba lo ka ruble", "block.minecraft.banner.gradient.lime": "le pelri'o ca zenba lo ka ruble", "block.minecraft.banner.gradient.magenta": "le nukni ca zenba lo ka ruble", "block.minecraft.banner.gradient.orange": "le narju ca zenba lo ka ruble", "block.minecraft.banner.gradient.pink": "le xunblabi ca zenba lo ka ruble", "block.minecraft.banner.gradient.purple": "le zirpu ca zenba lo ka ruble", "block.minecraft.banner.gradient.red": "le xunre ca zenba lo ka ruble", "block.minecraft.banner.gradient.white": "le blabi ca zenba lo ka ruble", "block.minecraft.banner.gradient.yellow": "le pelxu ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.black": "le xekri ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.blue": "le blanu ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.brown": "le bunre ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.cyan": "le cicna ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.gray": "le grusi ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.green": "le crino ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.light_blue": "le labybla ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.light_gray": "le labrus ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.lime": "le prelri'o ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.magenta": "le nukni ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.orange": "le narju ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.pink": "le xunblabi ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.purple": "le zirpu ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.red": "le xunre ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.white": "le blabi ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.gradient_up.yellow": "le pelxu ne le dizlo ca zenba lo ka ruble", "block.minecraft.banner.guster.black": "Black Guster", "block.minecraft.banner.guster.blue": "Blue Guster", "block.minecraft.banner.guster.brown": "<PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON>", "block.minecraft.banner.guster.green": "<PERSON> Guster", "block.minecraft.banner.guster.light_blue": "Light Blue Guster", "block.minecraft.banner.guster.light_gray": "Light Gray Guster", "block.minecraft.banner.guster.lime": "<PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Orange Guster", "block.minecraft.banner.guster.pink": "<PERSON> Guster", "block.minecraft.banner.guster.purple": "Purple Guster", "block.minecraft.banner.guster.red": "<PERSON> Gus<PERSON>", "block.minecraft.banner.guster.white": "White Guster", "block.minecraft.banner.guster.yellow": "Yellow Guster", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.blue": "blanu <PERSON>", "block.minecraft.banner.half_horizontal.brown": "b<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "c<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "g<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.green": "crino xab<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "labybla <PERSON>", "block.minecraft.banner.half_horizontal.light_gray": "labg<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.lime": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.magenta": "nukni <PERSON>", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.red": "x<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "blabi <PERSON>", "block.minecraft.banner.half_horizontal.yellow": "pelxu x<PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "x<PERSON><PERSON> d<PERSON><PERSON><PERSON> xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.blue": "blanu dziraipau xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.brown": "bunre d<PERSON><PERSON><PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.cyan": "cicna d<PERSON><PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.gray": "grusi d<PERSON><PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.green": "crino dziraipau xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.light_blue": "labybla dziraipau xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.light_gray": "labgrusi dziraipau xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.lime": "p<PERSON><PERSON><PERSON>o d<PERSON><PERSON><PERSON> xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.magenta": "nukni d<PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.orange": "narju d<PERSON><PERSON><PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.pink": "penka dziraipau xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.purple": "zir<PERSON> d<PERSON>rai<PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.red": "x<PERSON><PERSON> d<PERSON><PERSON><PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.white": "blabi d<PERSON><PERSON><PERSON>u xadba kurfa", "block.minecraft.banner.half_horizontal_bottom.yellow": "pelxu dziraipau xadba kurfa", "block.minecraft.banner.half_vertical.black": "x<PERSON>ri pritu x<PERSON><PERSON>a", "block.minecraft.banner.half_vertical.blue": "blanu pritu xab<PERSON><PERSON>a", "block.minecraft.banner.half_vertical.brown": "bunre pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.cyan": "cicna pritu x<PERSON>a", "block.minecraft.banner.half_vertical.gray": "grusi pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.green": "crino pritu xab<PERSON><PERSON>a", "block.minecraft.banner.half_vertical.light_blue": "labybla pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.light_gray": "labgrusi pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.lime": "pelri'o pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.magenta": "nukni pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.orange": "narju pritu x<PERSON>a", "block.minecraft.banner.half_vertical.pink": "penka pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.purple": "zirpu pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.red": "xunre pritu xab<PERSON><PERSON>a", "block.minecraft.banner.half_vertical.white": "blabi pritu xab<PERSON>a", "block.minecraft.banner.half_vertical.yellow": "pelxu pritu xabkurfa", "block.minecraft.banner.half_vertical_right.black": "x<PERSON><PERSON> zun<PERSON> xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.blue": "blanu zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.brown": "bunre zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.cyan": "cicna zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.gray": "grusi zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.green": "crino zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.light_blue": "labybla zunle xadba cibjgatai", "block.minecraft.banner.half_vertical_right.light_gray": "labg<PERSON>i zunle xadba cibjgatai", "block.minecraft.banner.half_vertical_right.lime": "p<PERSON><PERSON>'o zunle xadba cibjgatai", "block.minecraft.banner.half_vertical_right.magenta": "nukni zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.orange": "narju zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.pink": "penka zunle xadba cibjgatai", "block.minecraft.banner.half_vertical_right.purple": "zir<PERSON> zunle xadba cibjgatai", "block.minecraft.banner.half_vertical_right.red": "x<PERSON>re zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.white": "blabi zunle xadba cibj<PERSON>i", "block.minecraft.banner.half_vertical_right.yellow": "pelxu zunle xadba cibjgatai", "block.minecraft.banner.mojang.black": "x<PERSON>ri sumdai", "block.minecraft.banner.mojang.blue": "blanu sumdai", "block.minecraft.banner.mojang.brown": "bunre sumdai", "block.minecraft.banner.mojang.cyan": "cicna sumdai", "block.minecraft.banner.mojang.gray": "grusi sumdai", "block.minecraft.banner.mojang.green": "crino sumdai", "block.minecraft.banner.mojang.light_blue": "labybla sumdai", "block.minecraft.banner.mojang.light_gray": "labg<PERSON><PERSON> sumdai", "block.minecraft.banner.mojang.lime": "pel<PERSON>'o sumdai", "block.minecraft.banner.mojang.magenta": "nukni sumdai", "block.minecraft.banner.mojang.orange": "narju sumdai", "block.minecraft.banner.mojang.pink": "kuctai sumdai", "block.minecraft.banner.mojang.purple": "zirpu sumdai", "block.minecraft.banner.mojang.red": "xunre sumdai", "block.minecraft.banner.mojang.white": "blabi sumdai", "block.minecraft.banner.mojang.yellow": "pelxu sumdai", "block.minecraft.banner.piglin.black": ".i xekri x<PERSON>", "block.minecraft.banner.piglin.blue": ".i blanu x<PERSON>ai", "block.minecraft.banner.piglin.brown": ".i bunre x<PERSON>z<PERSON>ai", "block.minecraft.banner.piglin.cyan": ".i cicna xaj<PERSON>zbitai", "block.minecraft.banner.piglin.gray": ".i grusi xajyzbitai", "block.minecraft.banner.piglin.green": ".i crino xajyzbitai", "block.minecraft.banner.piglin.light_blue": ".i labybla xajyzbitai", "block.minecraft.banner.piglin.light_gray": ".i labgrusi xajyzbitai", "block.minecraft.banner.piglin.lime": ".i pelri'o x<PERSON>ai", "block.minecraft.banner.piglin.magenta": ".i nukni xajyzbitai", "block.minecraft.banner.piglin.orange": ".i narju xaj<PERSON>z<PERSON>ai", "block.minecraft.banner.piglin.pink": ".i penku xaj<PERSON>zbitai", "block.minecraft.banner.piglin.purple": ".i zirpu xajyzbitai", "block.minecraft.banner.piglin.red": "x<PERSON><PERSON> x<PERSON>", "block.minecraft.banner.piglin.white": ".i blabi xaj<PERSON>zbitai", "block.minecraft.banner.piglin.yellow": ".i pelxu xajyzbitai", "block.minecraft.banner.rhombus.black": "x<PERSON><PERSON>'<PERSON>", "block.minecraft.banner.rhombus.blue": "blanu sa'or<PERSON>a", "block.minecraft.banner.rhombus.brown": "bunre sa'or<PERSON><PERSON>a", "block.minecraft.banner.rhombus.cyan": "cicna <PERSON>'or<PERSON>a", "block.minecraft.banner.rhombus.gray": "grusi <PERSON>'<PERSON>a", "block.minecraft.banner.rhombus.green": "crino sa'orkurfa", "block.minecraft.banner.rhombus.light_blue": "labybla sa'orkurfa", "block.minecraft.banner.rhombus.light_gray": "labgrusi <PERSON>'or<PERSON>a", "block.minecraft.banner.rhombus.lime": "p<PERSON><PERSON>'o sa'or<PERSON><PERSON>a", "block.minecraft.banner.rhombus.magenta": "nukni sa'or<PERSON>a", "block.minecraft.banner.rhombus.orange": "narju <PERSON>'<PERSON><PERSON>a", "block.minecraft.banner.rhombus.pink": "penka sa'orkurfa", "block.minecraft.banner.rhombus.purple": "zirpu sa'or<PERSON>rfa", "block.minecraft.banner.rhombus.red": "x<PERSON><PERSON> sa'or<PERSON>a", "block.minecraft.banner.rhombus.white": "blabi sa'or<PERSON><PERSON>a", "block.minecraft.banner.rhombus.yellow": "pelxu sa'orkurfa", "block.minecraft.banner.skull.black": "x<PERSON><PERSON> sed<PERSON><PERSON><PERSON> tarmi", "block.minecraft.banner.skull.blue": "blanu sed<PERSON>'u tarmi", "block.minecraft.banner.skull.brown": "bunre sed<PERSON>'u tarmi", "block.minecraft.banner.skull.cyan": "cicna sed<PERSON>'u tarmi", "block.minecraft.banner.skull.gray": "grusi sed<PERSON>'u tarmi", "block.minecraft.banner.skull.green": "crino sedbo'u tarmi", "block.minecraft.banner.skull.light_blue": "labybla sedbo'u tarmi", "block.minecraft.banner.skull.light_gray": "labyrus sed<PERSON>'u tarmi", "block.minecraft.banner.skull.lime": "pelri'o sedbo'u tarmi", "block.minecraft.banner.skull.magenta": "nukni sed<PERSON>'u tarmi", "block.minecraft.banner.skull.orange": "narju sed<PERSON><PERSON><PERSON> tarmi", "block.minecraft.banner.skull.pink": "xun<PERSON>bi sed<PERSON>'u tarmi", "block.minecraft.banner.skull.purple": "zirpu sed<PERSON>'u tarmi", "block.minecraft.banner.skull.red": "xunre sed<PERSON>'u tarmi", "block.minecraft.banner.skull.white": "blabi sed<PERSON>'u tarmi", "block.minecraft.banner.skull.yellow": "pelxu sedbo'u tarmi", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "blanu r<PERSON>", "block.minecraft.banner.small_stripes.brown": "b<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.small_stripes.cyan": "c<PERSON>na <PERSON>", "block.minecraft.banner.small_stripes.gray": "g<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.small_stripes.green": "crino rajytegysri", "block.minecraft.banner.small_stripes.light_blue": "labybla rajytegysri", "block.minecraft.banner.small_stripes.light_gray": "labg<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.small_stripes.lime": "p<PERSON><PERSON>'o rajytegysri", "block.minecraft.banner.small_stripes.magenta": "nukni r<PERSON>", "block.minecraft.banner.small_stripes.orange": "nar<PERSON>", "block.minecraft.banner.small_stripes.pink": "penka rajytegysri", "block.minecraft.banner.small_stripes.purple": "zirpu rajytegysri", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "blabi r<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.yellow": "pelxu rajytegysri", "block.minecraft.banner.square_bottom_left.black": "x<PERSON><PERSON> d<PERSON><PERSON><PERSON>u pritu kurfa", "block.minecraft.banner.square_bottom_left.blue": "blanu dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.brown": "bunre dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.cyan": "cicna dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.gray": "grusi dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.green": "crino dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.light_blue": "labybla dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.light_gray": "labgrusi dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.lime": "p<PERSON><PERSON>'o dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.magenta": "nukni dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.orange": "narju d<PERSON><PERSON><PERSON>u pritu kurfa", "block.minecraft.banner.square_bottom_left.pink": "penka dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.purple": "zirpu dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.red": "x<PERSON>re dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.white": "blabi dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_left.yellow": "pelxu dziraipau pritu kurfa", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.blue": "blanu d<PERSON><PERSON><PERSON><PERSON> zu<PERSON>", "block.minecraft.banner.square_bottom_right.brown": "g<PERSON>i d<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "c<PERSON>na d<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.gray": "g<PERSON>i d<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.green": "crino d<PERSON><PERSON><PERSON><PERSON> zu<PERSON>", "block.minecraft.banner.square_bottom_right.light_blue": "labybla dziraipa<PERSON> zu<PERSON>", "block.minecraft.banner.square_bottom_right.light_gray": "labgrusi d<PERSON><PERSON><PERSON> zu<PERSON>", "block.minecraft.banner.square_bottom_right.lime": "p<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "nukni d<PERSON><PERSON> zu<PERSON>", "block.minecraft.banner.square_bottom_right.orange": "nar<PERSON> d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.pink": "penka dziraipau zu<PERSON>", "block.minecraft.banner.square_bottom_right.purple": "zir<PERSON> d<PERSON><PERSON><PERSON> zu<PERSON>", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.white": "blabi d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "pelxu dzi<PERSON><PERSON>u zu<PERSON>", "block.minecraft.banner.square_top_left.black": "x<PERSON>ri galraipau pritu kurfa", "block.minecraft.banner.square_top_left.blue": "blanu galraipau pritu kurfa", "block.minecraft.banner.square_top_left.brown": "bunre galraipau pritu kurfa", "block.minecraft.banner.square_top_left.cyan": "cicna galraipau pritu kurfa", "block.minecraft.banner.square_top_left.gray": "grusi galraipau pritu kurfa", "block.minecraft.banner.square_top_left.green": "crino d<PERSON><PERSON><PERSON><PERSON> zu<PERSON>", "block.minecraft.banner.square_top_left.light_blue": "labybla galraipau pritu kurfa", "block.minecraft.banner.square_top_left.light_gray": "labgrusi galraipau pritu kurfa", "block.minecraft.banner.square_top_left.lime": "pel<PERSON>'o galraipau pritu kurfa", "block.minecraft.banner.square_top_left.magenta": "nukni galraipau pritu kurfa", "block.minecraft.banner.square_top_left.orange": "narju galraipau pritu kurfa", "block.minecraft.banner.square_top_left.pink": "penka galraipau pritu kurfa", "block.minecraft.banner.square_top_left.purple": "zirpu galraipau pritu kurfa", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_left.white": "blabi galraipau pritu kurfa", "block.minecraft.banner.square_top_left.yellow": "pelxu galraipau pritu kurfa", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.blue": "blanu galraipau zu<PERSON>", "block.minecraft.banner.square_top_right.brown": "bunre galrai<PERSON>u zu<PERSON>", "block.minecraft.banner.square_top_right.cyan": "cicna gal<PERSON>", "block.minecraft.banner.square_top_right.gray": "grusi galrai<PERSON>u zu<PERSON>", "block.minecraft.banner.square_top_right.green": "crino galraipau zu<PERSON>", "block.minecraft.banner.square_top_right.light_blue": "labybla galraipau zu<PERSON>", "block.minecraft.banner.square_top_right.light_gray": "labgrusi galraipau zu<PERSON>", "block.minecraft.banner.square_top_right.lime": "p<PERSON><PERSON>'o galrai<PERSON> zu<PERSON>", "block.minecraft.banner.square_top_right.magenta": "nukni gal<PERSON>u zu<PERSON>", "block.minecraft.banner.square_top_right.orange": "narju <PERSON><PERSON>", "block.minecraft.banner.square_top_right.pink": "penka galraipau zu<PERSON>", "block.minecraft.banner.square_top_right.purple": "zirpu galraipau zu<PERSON>", "block.minecraft.banner.square_top_right.red": "x<PERSON><PERSON> galrai<PERSON>u zu<PERSON>", "block.minecraft.banner.square_top_right.white": "blabi galrai<PERSON>u zu<PERSON>", "block.minecraft.banner.square_top_right.yellow": "pelxu galraipau zulk<PERSON>fa", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.blue": "blanu kuctai", "block.minecraft.banner.straight_cross.brown": "bunre kuctai", "block.minecraft.banner.straight_cross.cyan": "c<PERSON>na k<PERSON>", "block.minecraft.banner.straight_cross.gray": "grusi k<PERSON>tai", "block.minecraft.banner.straight_cross.green": "crino kuctai", "block.minecraft.banner.straight_cross.light_blue": "labybla kuctai", "block.minecraft.banner.straight_cross.light_gray": "labgrusi kuctai", "block.minecraft.banner.straight_cross.lime": "p<PERSON><PERSON>'o kuctai", "block.minecraft.banner.straight_cross.magenta": "nukni k<PERSON>tai", "block.minecraft.banner.straight_cross.orange": "narju k<PERSON>tai", "block.minecraft.banner.straight_cross.pink": "penka kuctai", "block.minecraft.banner.straight_cross.purple": "zirpu kuctai", "block.minecraft.banner.straight_cross.red": "x<PERSON><PERSON> kuctai", "block.minecraft.banner.straight_cross.white": "blabi kuctai", "block.minecraft.banner.straight_cross.yellow": "pelxu kuctai", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "blanu d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "b<PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "c<PERSON>na d<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "g<PERSON>i d<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "crino d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "labybla d<PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "labgrusi d<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "p<PERSON><PERSON>'o d<PERSON><PERSON><PERSON><PERSON>ri", "block.minecraft.banner.stripe_bottom.magenta": "nukni d<PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON>r<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "penka d<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "zir<PERSON> d<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "blabi d<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "pelxu dzi<PERSON><PERSON><PERSON>ri", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "blanu r<PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "b<PERSON><PERSON> r<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "c<PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "g<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.stripe_center.green": "crino raj<PERSON>ri", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.stripe_center.lime": "p<PERSON><PERSON><PERSON>o raj<PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "nukni r<PERSON>", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON>ka raj<PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "blabi raj<PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "pelxu raj<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON><PERSON> zu<PERSON>o<PERSON>ri", "block.minecraft.banner.stripe_downleft.blue": "blanu zuls<PERSON>'osri", "block.minecraft.banner.stripe_downleft.brown": "bunre zuls<PERSON>'osri", "block.minecraft.banner.stripe_downleft.cyan": "cicna zu<PERSON>o<PERSON>ri", "block.minecraft.banner.stripe_downleft.gray": "grusi zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downleft.green": "crino zulsa'osri", "block.minecraft.banner.stripe_downleft.light_blue": "labybla zuls<PERSON>'osri", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON><PERSON> zuls<PERSON>'osri", "block.minecraft.banner.stripe_downleft.lime": "pelri'o zulsa'osri", "block.minecraft.banner.stripe_downleft.magenta": "nukni zu<PERSON>o<PERSON>ri", "block.minecraft.banner.stripe_downleft.orange": "narju <PERSON>ri", "block.minecraft.banner.stripe_downleft.pink": "penka zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downleft.purple": "zir<PERSON> zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downleft.red": "x<PERSON><PERSON> zu<PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downleft.white": "blabi zuls<PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downleft.yellow": "pelxu zuls<PERSON>'osri", "block.minecraft.banner.stripe_downright.black": "x<PERSON><PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downright.blue": "blanu sa'osri", "block.minecraft.banner.stripe_downright.brown": "bunre sa'osri", "block.minecraft.banner.stripe_downright.cyan": "cicna sa'osri", "block.minecraft.banner.stripe_downright.gray": "grusi sa'osri", "block.minecraft.banner.stripe_downright.green": "crino sa'osri", "block.minecraft.banner.stripe_downright.light_blue": "labybla sa'osri", "block.minecraft.banner.stripe_downright.light_gray": "labgrusi sa'osri", "block.minecraft.banner.stripe_downright.lime": "pelri'o sa'osri", "block.minecraft.banner.stripe_downright.magenta": "nukni sa'o<PERSON>ri", "block.minecraft.banner.stripe_downright.orange": "narju <PERSON>'o<PERSON>ri", "block.minecraft.banner.stripe_downright.pink": "penka sa'osri", "block.minecraft.banner.stripe_downright.purple": "zirpu sa'osri", "block.minecraft.banner.stripe_downright.red": "x<PERSON><PERSON> sa'osri", "block.minecraft.banner.stripe_downright.white": "blabi sa'osri", "block.minecraft.banner.stripe_downright.yellow": "pelxu sa'osri", "block.minecraft.banner.stripe_left.black": "x<PERSON>ri pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.blue": "blanu pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.brown": "bunre pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.cyan": "cicna pritu raj<PERSON>", "block.minecraft.banner.stripe_left.gray": "grusi pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.green": "crino pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.light_blue": "labybla pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.light_gray": "labgrusi pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.lime": "pelri'o pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.magenta": "nukni pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.orange": "narju pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.pink": "penka pritu rajysri", "block.minecraft.banner.stripe_left.purple": "zirpu pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.red": "xunre pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.white": "blabi pritu raj<PERSON>ri", "block.minecraft.banner.stripe_left.yellow": "pelxu pritu raj<PERSON>ri", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "blanu mi<PERSON>", "block.minecraft.banner.stripe_middle.brown": "g<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "c<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "g<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "crino mij<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "lab<PERSON>bla <PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "p<PERSON><PERSON><PERSON>o mi<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "nukni <PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON> mi<PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "blabi <PERSON>", "block.minecraft.banner.stripe_middle.yellow": "pelxu mi<PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.blue": "blanu <PERSON>", "block.minecraft.banner.stripe_right.brown": "b<PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "c<PERSON><PERSON>", "block.minecraft.banner.stripe_right.gray": "g<PERSON><PERSON>", "block.minecraft.banner.stripe_right.green": "crino <PERSON>", "block.minecraft.banner.stripe_right.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.lime": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.magenta": "nukni <PERSON>", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.white": "blabi <PERSON>", "block.minecraft.banner.stripe_right.yellow": "pelxu <PERSON>", "block.minecraft.banner.stripe_top.black": "x<PERSON>ri gal<PERSON>", "block.minecraft.banner.stripe_top.blue": "blanu galrai<PERSON>u <PERSON>ri", "block.minecraft.banner.stripe_top.brown": "bunre galrai<PERSON><PERSON>ri", "block.minecraft.banner.stripe_top.cyan": "c<PERSON>na <PERSON>", "block.minecraft.banner.stripe_top.gray": "grusi galrai<PERSON>ri", "block.minecraft.banner.stripe_top.green": "crino galrai<PERSON><PERSON>ri", "block.minecraft.banner.stripe_top.light_blue": "labybla galrai<PERSON>", "block.minecraft.banner.stripe_top.light_gray": "labg<PERSON>i galrai<PERSON>ri", "block.minecraft.banner.stripe_top.lime": "pel<PERSON>'o galrai<PERSON>u <PERSON>ri", "block.minecraft.banner.stripe_top.magenta": "nukni gal<PERSON>", "block.minecraft.banner.stripe_top.orange": "narju <PERSON>", "block.minecraft.banner.stripe_top.pink": "penka galraipau <PERSON>ri", "block.minecraft.banner.stripe_top.purple": "zirpu galrai<PERSON>u <PERSON>ri", "block.minecraft.banner.stripe_top.red": "x<PERSON><PERSON> gal<PERSON>", "block.minecraft.banner.stripe_top.white": "blabi gal<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "pelxu galraipau <PERSON>ri", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_bottom.blue": "blanu dzirai<PERSON>u cib<PERSON>i", "block.minecraft.banner.triangle_bottom.brown": "bun<PERSON> d<PERSON><PERSON><PERSON><PERSON> cib<PERSON><PERSON>i", "block.minecraft.banner.triangle_bottom.cyan": "c<PERSON>na d<PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_bottom.gray": "grusi d<PERSON>u cibj<PERSON>i", "block.minecraft.banner.triangle_bottom.green": "crino dziraipau cibjgatai", "block.minecraft.banner.triangle_bottom.light_blue": "labybla d<PERSON>rai<PERSON>u cibj<PERSON>i", "block.minecraft.banner.triangle_bottom.light_gray": "labgrusi d<PERSON>rai<PERSON>u cibj<PERSON>i", "block.minecraft.banner.triangle_bottom.lime": "p<PERSON><PERSON>'o d<PERSON><PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_bottom.magenta": "nukni d<PERSON> cibj<PERSON>i", "block.minecraft.banner.triangle_bottom.orange": "narju d<PERSON><PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_bottom.pink": "penka dziraipau cibjgatai", "block.minecraft.banner.triangle_bottom.purple": "zirpu d<PERSON> cibjgatai", "block.minecraft.banner.triangle_bottom.red": "x<PERSON><PERSON> d<PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_bottom.white": "blabi d<PERSON><PERSON><PERSON><PERSON> cib<PERSON><PERSON>i", "block.minecraft.banner.triangle_bottom.yellow": "pelxu dziraipau cibjgatai", "block.minecraft.banner.triangle_top.black": "x<PERSON><PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_top.blue": "blanu galraipau cibj<PERSON>i", "block.minecraft.banner.triangle_top.brown": "bunre galraipau cib<PERSON><PERSON>i", "block.minecraft.banner.triangle_top.cyan": "cicna galrai<PERSON>u cib<PERSON><PERSON>i", "block.minecraft.banner.triangle_top.gray": "grusi galraipau cibj<PERSON>i", "block.minecraft.banner.triangle_top.green": "crino galraipau cibjgatai", "block.minecraft.banner.triangle_top.light_blue": "labybla galraipau cibj<PERSON>i", "block.minecraft.banner.triangle_top.light_gray": "labgrusi galraipau cibj<PERSON>i", "block.minecraft.banner.triangle_top.lime": "p<PERSON><PERSON>'o galrai<PERSON>u cibj<PERSON>i", "block.minecraft.banner.triangle_top.magenta": "nukni gal<PERSON>u cibj<PERSON>i", "block.minecraft.banner.triangle_top.orange": "narju gal<PERSON> cib<PERSON>i", "block.minecraft.banner.triangle_top.pink": "penka galraipau cibjgatai", "block.minecraft.banner.triangle_top.purple": "zirpu galraipau cibjgatai", "block.minecraft.banner.triangle_top.red": "x<PERSON><PERSON> galrai<PERSON>u cibj<PERSON>i", "block.minecraft.banner.triangle_top.white": "blabi galraipau cib<PERSON><PERSON>i", "block.minecraft.banner.triangle_top.yellow": "pelxu galraipau cibjgatai", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON> s<PERSON>'a<PERSON>pa", "block.minecraft.banner.triangles_bottom.blue": "blanu dzirai<PERSON>u s<PERSON>'akpa", "block.minecraft.banner.triangles_bottom.brown": "bunre d<PERSON><PERSON><PERSON>u s<PERSON>'akpa", "block.minecraft.banner.triangles_bottom.cyan": "cicna d<PERSON> s<PERSON>'a<PERSON>pa", "block.minecraft.banner.triangles_bottom.gray": "grusi d<PERSON>u s<PERSON>'akpa", "block.minecraft.banner.triangles_bottom.green": "crino dziraipau sra'akpa", "block.minecraft.banner.triangles_bottom.light_blue": "labybla dziraipau sra'akpa", "block.minecraft.banner.triangles_bottom.light_gray": "labgrusi d<PERSON>rai<PERSON>u sra'akpa", "block.minecraft.banner.triangles_bottom.lime": "p<PERSON><PERSON>'o d<PERSON><PERSON>u sra'akpa", "block.minecraft.banner.triangles_bottom.magenta": "nukni d<PERSON>u s<PERSON>'akpa", "block.minecraft.banner.triangles_bottom.orange": "narju d<PERSON><PERSON><PERSON> s<PERSON>'a<PERSON>pa", "block.minecraft.banner.triangles_bottom.pink": "penka dziraipau sra'akpa", "block.minecraft.banner.triangles_bottom.purple": "zirpu d<PERSON>u sra'akpa", "block.minecraft.banner.triangles_bottom.red": "x<PERSON><PERSON> d<PERSON>u s<PERSON>'a<PERSON>pa", "block.minecraft.banner.triangles_bottom.white": "blabi d<PERSON><PERSON><PERSON><PERSON> s<PERSON>'akpa", "block.minecraft.banner.triangles_bottom.yellow": "pelxu dziraipau sra'akpa", "block.minecraft.banner.triangles_top.black": "x<PERSON><PERSON> gal<PERSON> s<PERSON>'akpa", "block.minecraft.banner.triangles_top.blue": "blanu galraipau sra'akpa", "block.minecraft.banner.triangles_top.brown": "bunre galraipau sra'akpa", "block.minecraft.banner.triangles_top.cyan": "cicna galrai<PERSON>u sra'akpa", "block.minecraft.banner.triangles_top.gray": "grusi galraipau sra'akpa", "block.minecraft.banner.triangles_top.green": "crino galraipau sra'akpa", "block.minecraft.banner.triangles_top.light_blue": "labybla galraipau sra'akpa", "block.minecraft.banner.triangles_top.light_gray": "labgrusi galraipau sra'akpa", "block.minecraft.banner.triangles_top.lime": "pelri'o galraipau sra'akpa", "block.minecraft.banner.triangles_top.magenta": "nukni gal<PERSON>u sra'akpa", "block.minecraft.banner.triangles_top.orange": "narju gal<PERSON>u sra'akpa", "block.minecraft.banner.triangles_top.pink": "penka galraipau sra'akpa", "block.minecraft.banner.triangles_top.purple": "zirpu galraipau sra'akpa", "block.minecraft.banner.triangles_top.red": "x<PERSON>re galraipau sra'akpa", "block.minecraft.banner.triangles_top.white": "blabi galraipau sra'akpa", "block.minecraft.banner.triangles_top.yellow": "pelxu galraipau sra'akpa", "block.minecraft.barrel": "mudbaktu", "block.minecraft.barrier": "zun<PERSON>'u", "block.minecraft.basalt": "kamjyr<PERSON>'i", "block.minecraft.beacon": "barda tergu'i", "block.minecraft.beacon.primary": "pamai vlipa", "block.minecraft.beacon.secondary": "remoi vlipa", "block.minecraft.bed.no_sleep": ".i do ka'e sipna ca po'o lo nicte ja lo lidvilti'a", "block.minecraft.bed.not_safe": ".i na cumki fa lo nu sipna .i lo cizda'u cu jibni", "block.minecraft.bed.obstructed": "This bed is obstructed", "block.minecraft.bed.occupied": ".i le vi ckana ku canlu", "block.minecraft.bed.too_far_away": ".i na cumki fa lo nu sipna .i lo ckana cu dardu'e", "block.minecraft.bedrock": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bee_nest": "bifce zdani", "block.minecraft.beehive": "rutni bicyzda", "block.minecraft.beetroots": "spatrtensai", "block.minecraft.bell": "janbe", "block.minecraft.big_dripleaf": "barda sin<PERSON>", "block.minecraft.big_dripleaf_stem": "barda sin<PERSON>zli stani", "block.minecraft.birch_button": ".eptula batke", "block.minecraft.birch_door": ".eptula v<PERSON>ai", "block.minecraft.birch_fence": ".eptula garbi'u", "block.minecraft.birch_fence_gate": ".eptula gar<PERSON>", "block.minecraft.birch_hanging_sign": ".eptula ke dandu lertapla", "block.minecraft.birch_leaves": ".eptula p<PERSON>", "block.minecraft.birch_log": ".eptula stani", "block.minecraft.birch_planks": ".eptula mudri tanbo", "block.minecraft.birch_pressure_plate": "eptula dan<PERSON>ta<PERSON>la", "block.minecraft.birch_sapling": ".eptula c<PERSON>u", "block.minecraft.birch_sign": "bi'orka sinxa", "block.minecraft.birch_slab": ".eptula xabybli", "block.minecraft.birch_stairs": ".eptula serti", "block.minecraft.birch_trapdoor": ".eptula lo<PERSON>", "block.minecraft.birch_wall_hanging_sign": "bitmu ke .eptula ke dandu lertapla", "block.minecraft.birch_wall_sign": "bi'orka ke bitmu sinxa", "block.minecraft.birch_wood": ".eptula mudri", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.black_bed": "x<PERSON><PERSON> c<PERSON>a", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON>i", "block.minecraft.black_candle_cake": "x<PERSON><PERSON> lakt<PERSON>'i kenka", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.black_concrete_powder": "x<PERSON><PERSON> x<PERSON>o purmo", "block.minecraft.black_glazed_terracotta": "le xekri stakrterakota poi jelca", "block.minecraft.black_shulker_box": "xekri ke me la .cylkr. tanxe", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON> s<PERSON> blaci", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON><PERSON> skari canko blaci", "block.minecraft.black_terracotta": "x<PERSON><PERSON> s<PERSON>", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.blackstone": "x<PERSON><PERSON>'i", "block.minecraft.blackstone_slab": "xekro'i xabybli", "block.minecraft.blackstone_stairs": "x<PERSON><PERSON>'i serti", "block.minecraft.blackstone_wall": "x<PERSON><PERSON>'i bitmu", "block.minecraft.blast_furnace": "b<PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "blanu <PERSON>cu", "block.minecraft.blue_bed": "blanu c<PERSON>a", "block.minecraft.blue_candle": "blanu lakt<PERSON>'i", "block.minecraft.blue_candle_cake": "blanu lakter<PERSON>'i kenka", "block.minecraft.blue_carpet": "blanu buklo<PERSON>", "block.minecraft.blue_concrete": "blanu x<PERSON>o", "block.minecraft.blue_concrete_powder": "blanu xumrkonkreto purmo", "block.minecraft.blue_glazed_terracotta": "le blabi stakrterakota poi se fagri", "block.minecraft.blue_ice": "blanu bisli", "block.minecraft.blue_orchid": "spatrof<PERSON>", "block.minecraft.blue_shulker_box": "blanu ke me la .cylkr. tanxe", "block.minecraft.blue_stained_glass": "blanu skari blaci", "block.minecraft.blue_stained_glass_pane": "blanu skari canko blaci", "block.minecraft.blue_terracotta": "blanu sta<PERSON>", "block.minecraft.blue_wool": "blanu sunla", "block.minecraft.bone_block": "bongu bliku", "block.minecraft.bookshelf": "cukta kajna", "block.minecraft.brain_coral": "bentai ricnrit<PERSON>", "block.minecraft.brain_coral_block": "bentai ric<PERSON><PERSON><PERSON> bliku", "block.minecraft.brain_coral_fan": "bentai ric<PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_wall_fan": "bentai ric<PERSON><PERSON><PERSON> bi<PERSON>", "block.minecraft.brewing_stand": "sel<PERSON><PERSON> jukpa jubme", "block.minecraft.brick_slab": "kitybli xabybli", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON> serti", "block.minecraft.brick_wall": "kitybli bitmu", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "bun<PERSON> s<PERSON>ci", "block.minecraft.brown_bed": "bunre c<PERSON>a", "block.minecraft.brown_candle": "bunre lakter<PERSON>'i", "block.minecraft.brown_candle_cake": "bunre lakter<PERSON>'i kenka", "block.minecraft.brown_carpet": "bun<PERSON> buklo<PERSON>", "block.minecraft.brown_concrete": "bunre x<PERSON>o", "block.minecraft.brown_concrete_powder": "bunre xumrkonkreto purmo", "block.minecraft.brown_glazed_terracotta": "le bunre stakrterakota poi jelca", "block.minecraft.brown_mushroom": "bunre gumri", "block.minecraft.brown_mushroom_block": "bunre gumri bliku", "block.minecraft.brown_shulker_box": "bunre ke me la .cylkr. tanxe", "block.minecraft.brown_stained_glass": "bunre skari blaci", "block.minecraft.brown_stained_glass_pane": "bunre skari canko blaci", "block.minecraft.brown_terracotta": "bun<PERSON> sta<PERSON>", "block.minecraft.brown_wool": "bunre sunla", "block.minecraft.bubble_column": "Bubble Column", "block.minecraft.bubble_coral": "fom<PERSON> r<PERSON>", "block.minecraft.bubble_coral_block": "fomtai ric<PERSON><PERSON><PERSON> bliku", "block.minecraft.bubble_coral_fan": "fom<PERSON> r<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "fom<PERSON> r<PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "block.minecraft.budding_amethyst": ".amtisti ba'orgau", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "j<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Cactus Flower", "block.minecraft.cake": "titnanba", "block.minecraft.calcite": "bogtab", "block.minecraft.calibrated_sculk_sensor": "se xelm<PERSON><PERSON>u ke me la .skylk. ganse", "block.minecraft.campfire": "gikyfagri", "block.minecraft.candle": "lakt<PERSON><PERSON><PERSON>i", "block.minecraft.candle_cake": "lakter<PERSON>'i kenka", "block.minecraft.carrots": "najgenja", "block.minecraft.cartography_table": "catplajbu", "block.minecraft.carved_pumpkin": "se<PERSON><PERSON><PERSON> guzmrkukurbita", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "Cave Air", "block.minecraft.cave_vines": "Cave Vines", "block.minecraft.cave_vines_plant": "Cave Vines Plant", "block.minecraft.chain": ".iinsi", "block.minecraft.chain_command_block": ".iinsi ziltinbe bliku", "block.minecraft.cherry_button": "r<PERSON><PERSON><PERSON><PERSON> batke", "block.minecraft.cherry_door": "r<PERSON><PERSON><PERSON><PERSON> v<PERSON>ai", "block.minecraft.cherry_fence": "r<PERSON><PERSON><PERSON><PERSON> garbi'u", "block.minecraft.cherry_fence_gate": "r<PERSON><PERSON><PERSON><PERSON> gar<PERSON>", "block.minecraft.cherry_hanging_sign": "ricrceraso ke dandu lertapla", "block.minecraft.cherry_leaves": "r<PERSON><PERSON><PERSON><PERSON> p<PERSON>li", "block.minecraft.cherry_log": "r<PERSON><PERSON><PERSON><PERSON> stani", "block.minecraft.cherry_planks": "ric<PERSON><PERSON><PERSON> mudri tanbo", "block.minecraft.cherry_pressure_plate": "r<PERSON><PERSON><PERSON><PERSON> da<PERSON><PERSON>", "block.minecraft.cherry_sapling": "r<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.cherry_sign": "r<PERSON><PERSON><PERSON><PERSON> le<PERSON>", "block.minecraft.cherry_slab": "r<PERSON><PERSON><PERSON><PERSON> x<PERSON>li", "block.minecraft.cherry_stairs": "r<PERSON><PERSON><PERSON><PERSON> serti", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_hanging_sign": "bitmu ke ricrceraso ke dandu lertapla", "block.minecraft.cherry_wall_sign": "bitmu ke ricrceraso lertapla", "block.minecraft.cherry_wood": "r<PERSON><PERSON><PERSON><PERSON> mudri", "block.minecraft.chest": "vasru", "block.minecraft.chipped_anvil": "selxai to<PERSON> ji<PERSON>be", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON><PERSON><PERSON> ckukajna", "block.minecraft.chiseled_copper": "se<PERSON><PERSON><PERSON>i ke tunka bliku", "block.minecraft.chiseled_deepslate": "se rakpra cnoro'i", "block.minecraft.chiseled_nether_bricks": "se<PERSON><PERSON>'i ke dzimu'e kubli bliku", "block.minecraft.chiseled_polished_blackstone": "sel<PERSON>'i ke selspali xek<PERSON>'i", "block.minecraft.chiseled_quartz_block": "se rakpra ke krili bliku", "block.minecraft.chiseled_red_sandstone": "se rakpra ke xunre canro'i", "block.minecraft.chiseled_resin_bricks": "Chiseled Resin Bricks", "block.minecraft.chiseled_sandstone": "se rakpra ke canre rokci", "block.minecraft.chiseled_stone_bricks": "rak<PERSON> ke rokci bliku", "block.minecraft.chiseled_tuff": "selja'i fagma'apucro'i", "block.minecraft.chiseled_tuff_bricks": "selja'i ke fagma'apucro'i bliku", "block.minecraft.chorus_flower": "girza'a xrula", "block.minecraft.chorus_plant": "girza'a spati", "block.minecraft.clay": "kliti", "block.minecraft.closed_eyeblossom": "Closed Eyeblossom", "block.minecraft.coal_block": "kolme bliku", "block.minecraft.coal_ore": "kolme kunra", "block.minecraft.coarse_dirt": "ruf<PERSON> der<PERSON>", "block.minecraft.cobbled_deepslate": "spofu cnoro'i", "block.minecraft.cobbled_deepslate_slab": "spofu cnoro'i xadba", "block.minecraft.cobbled_deepslate_stairs": "spofu cnoro'i serti", "block.minecraft.cobbled_deepslate_wall": "spofu condi bo rocki bitmu", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "s<PERSON><PERSON><PERSON> xaby<PERSON>li", "block.minecraft.cobblestone_stairs": "s<PERSON><PERSON><PERSON> serti", "block.minecraft.cobblestone_wall": "s<PERSON><PERSON><PERSON> bitmu", "block.minecraft.cobweb": "juk<PERSON>zda", "block.minecraft.cocoa": "cakla", "block.minecraft.command_block": "ziltinbe bliku", "block.minecraft.comparator": "me la redston. karbi", "block.minecraft.composter": "rikfre pulce ke fusra minji", "block.minecraft.conduit": "xasycei kroxo", "block.minecraft.copper_block": "tunka bliku", "block.minecraft.copper_bulb": "tunka tergu'i", "block.minecraft.copper_door": "tunka vrogai", "block.minecraft.copper_grate": "tunka ga'avro", "block.minecraft.copper_ore": "tunka kunra", "block.minecraft.copper_trapdoor": "tunka lo<PERSON>v<PERSON>ai", "block.minecraft.cornflower": "centaureia", "block.minecraft.cracked_deepslate_bricks": "fenra c<PERSON><PERSON>'i kit<PERSON>li", "block.minecraft.cracked_deepslate_tiles": "fenra cnoro'i tapla", "block.minecraft.cracked_nether_bricks": "self<PERSON>ra ke dzimu'e kubli bliku", "block.minecraft.cracked_polished_blackstone_bricks": "self<PERSON>ra ke selspali xekro'i kubli bliku", "block.minecraft.cracked_stone_bricks": "fenra ke rokci bliku", "block.minecraft.crafter": "zbasu", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON> jubme", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "stedu be la .kripr.", "block.minecraft.creeper_wall_head": "stedu pe la .kriper. be ne'a bitmu", "block.minecraft.crimson_button": "zirxu'e batke", "block.minecraft.crimson_door": "zirxu'e vrogai", "block.minecraft.crimson_fence": "zirxu'e garbi'u", "block.minecraft.crimson_fence_gate": "zirxu'e garbimvrogai", "block.minecraft.crimson_fungus": "zirx<PERSON>'e gumri", "block.minecraft.crimson_hanging_sign": "zirxu'e ke dandu lertapla", "block.minecraft.crimson_hyphae": "zirx<PERSON>'e led<PERSON>'e", "block.minecraft.crimson_nylium": "zirxu'e ledyclika", "block.minecraft.crimson_planks": "zirxu'e tanbo", "block.minecraft.crimson_pressure_plate": "zirxu'e danrytapla", "block.minecraft.crimson_roots": "zirxu'e genja", "block.minecraft.crimson_sign": "zirxu'e lertapla", "block.minecraft.crimson_slab": "zirxu'e xabybli", "block.minecraft.crimson_stairs": "zirxu'e serti", "block.minecraft.crimson_stem": "zirxu'e stani", "block.minecraft.crimson_trapdoor": "zirxu'e lolvrogai", "block.minecraft.crimson_wall_hanging_sign": "bitmu ke zirxu'e ke dandu lertapla", "block.minecraft.crimson_wall_sign": "bimzva ke zirxu'e lertapla", "block.minecraft.crying_obsidian": "klaku <PERSON>", "block.minecraft.cut_copper": "selsraku ke tunka bliku", "block.minecraft.cut_copper_slab": "selsraku ke tunka xabybli", "block.minecraft.cut_copper_stairs": "selsraku ke tunka serti", "block.minecraft.cut_red_sandstone": "selka'a xunre canro'i", "block.minecraft.cut_red_sandstone_slab": "selka'a xunre bo canro'i xadba", "block.minecraft.cut_sandstone": "selka'a canro'i", "block.minecraft.cut_sandstone_slab": "selk<PERSON>'a can<PERSON>'i xadba", "block.minecraft.cyan_banner": "c<PERSON><PERSON>", "block.minecraft.cyan_bed": "c<PERSON>na c<PERSON>a", "block.minecraft.cyan_candle": "cicna la<PERSON>i", "block.minecraft.cyan_candle_cake": "cicna lakter<PERSON>'i kenka", "block.minecraft.cyan_carpet": "c<PERSON><PERSON> b<PERSON>", "block.minecraft.cyan_concrete": "cicna <PERSON>", "block.minecraft.cyan_concrete_powder": "cicna x<PERSON>onkreto purmo", "block.minecraft.cyan_glazed_terracotta": "le cicna stakrterakota poi se fagri", "block.minecraft.cyan_shulker_box": "cicna ke me la .cylkr. tanxe", "block.minecraft.cyan_stained_glass": "c<PERSON><PERSON> s<PERSON> blaci", "block.minecraft.cyan_stained_glass_pane": "c<PERSON>na skari canko blaci", "block.minecraft.cyan_terracotta": "cicna s<PERSON>", "block.minecraft.cyan_wool": "cicna <PERSON>la", "block.minecraft.damaged_anvil": "sel<PERSON>i ji<PERSON>", "block.minecraft.dandelion": "sparta<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "mancindu batke", "block.minecraft.dark_oak_door": "mancin<PERSON> vrogai", "block.minecraft.dark_oak_fence": "mancindu garbi'u", "block.minecraft.dark_oak_fence_gate": "mancin<PERSON> gar<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_hanging_sign": "mancindu ke dandu lertapla", "block.minecraft.dark_oak_leaves": "mancindu pezli", "block.minecraft.dark_oak_log": "mancindu stani", "block.minecraft.dark_oak_planks": "mancindu mudri tanbo", "block.minecraft.dark_oak_pressure_plate": "lo mancindu danrytapla", "block.minecraft.dark_oak_sapling": "mancindu c<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sign": "mancindu sinxa", "block.minecraft.dark_oak_slab": "mancin<PERSON> x<PERSON>li", "block.minecraft.dark_oak_stairs": "mancindu serti", "block.minecraft.dark_oak_trapdoor": "man<PERSON><PERSON> lo<PERSON>", "block.minecraft.dark_oak_wall_hanging_sign": "bitmu ke mancindu ke dandu lertapla", "block.minecraft.dark_oak_wall_sign": "xekri cindu ke bitmu sinxa", "block.minecraft.dark_oak_wood": "mancindu mudri", "block.minecraft.dark_prismarine": "manku be la prismarin.", "block.minecraft.dark_prismarine_slab": "manku xabybli be me la prismarin.", "block.minecraft.dark_prismarine_stairs": "manserti be me la prismarin.", "block.minecraft.daylight_detector": "tangu'i ganse", "block.minecraft.dead_brain_coral": "Dead Brain Coral", "block.minecraft.dead_brain_coral_block": "morsi ke bentai ric<PERSON><PERSON><PERSON> bliku", "block.minecraft.dead_brain_coral_fan": "morsi ke bentai ric<PERSON><PERSON><PERSON> tolfalnu", "block.minecraft.dead_brain_coral_wall_fan": "morsi ke bentai ric<PERSON><PERSON><PERSON> bim<PERSON>nu", "block.minecraft.dead_bubble_coral": "Dead Bubble Coral", "block.minecraft.dead_bubble_coral_block": "mrosi ke fomtai ric<PERSON><PERSON>rina bliku", "block.minecraft.dead_bubble_coral_fan": "morsi ke fomtai ric<PERSON><PERSON><PERSON> tolf<PERSON>nu", "block.minecraft.dead_bubble_coral_wall_fan": "morsi ke fomtai ric<PERSON><PERSON><PERSON> bi<PERSON>nu", "block.minecraft.dead_bush": "morsi demspa", "block.minecraft.dead_fire_coral": "Dead Fire Coral", "block.minecraft.dead_fire_coral_block": "morsi ke fagtai ric<PERSON><PERSON><PERSON> bliku", "block.minecraft.dead_fire_coral_fan": "morsi ke fagtai ric<PERSON><PERSON><PERSON> tolf<PERSON>nu", "block.minecraft.dead_fire_coral_wall_fan": "morsi ke fagtai ric<PERSON><PERSON><PERSON> bim<PERSON>nu", "block.minecraft.dead_horn_coral": "Dead Horn Coral", "block.minecraft.dead_horn_coral_block": "morsi ke jirnytai ricnritrina bliku", "block.minecraft.dead_horn_coral_fan": "morsi ke jirnytai ric<PERSON><PERSON><PERSON> to<PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "morsi ke jirnytai ric<PERSON><PERSON><PERSON> bi<PERSON>nu", "block.minecraft.dead_tube_coral": "Dead Tube Coral", "block.minecraft.dead_tube_coral_block": "morsi ke tu'urtai ric<PERSON><PERSON><PERSON> bliku", "block.minecraft.dead_tube_coral_fan": "morsi ke tu'urtai ric<PERSON><PERSON><PERSON> tolfalnu", "block.minecraft.dead_tube_coral_wall_fan": "morsi ke tu'urtai ric<PERSON><PERSON><PERSON> bim<PERSON>nu", "block.minecraft.decorated_pot": "se<PERSON><PERSON><PERSON>i patxu", "block.minecraft.deepslate": "cnoro'i", "block.minecraft.deepslate_brick_slab": "c<PERSON><PERSON><PERSON>i kity<PERSON>li xadba", "block.minecraft.deepslate_brick_stairs": "c<PERSON><PERSON><PERSON>i kitybli serti", "block.minecraft.deepslate_brick_wall": "cnoro'i kitybli bitmu", "block.minecraft.deepslate_bricks": "c<PERSON><PERSON><PERSON><PERSON> kit<PERSON><PERSON>li", "block.minecraft.deepslate_coal_ore": "c<PERSON><PERSON>'i ke kolme kunra", "block.minecraft.deepslate_copper_ore": "cnoro'i ke tunka kunra", "block.minecraft.deepslate_diamond_ore": "c<PERSON><PERSON>'i ke tabjme kunra", "block.minecraft.deepslate_emerald_ore": "cnor<PERSON>'i ke ri'ojme kunra", "block.minecraft.deepslate_gold_ore": "c<PERSON><PERSON>'i ke solji kunra", "block.minecraft.deepslate_iron_ore": "cnoro'i ke tirse kunra", "block.minecraft.deepslate_lapis_ore": "cnoro'i ke blaro'i kunra", "block.minecraft.deepslate_redstone_ore": "cnoro'i ke me la redston. kunra", "block.minecraft.deepslate_tile_slab": "c<PERSON><PERSON><PERSON>i tapla xadba", "block.minecraft.deepslate_tile_stairs": "c<PERSON><PERSON>'i tapla serti", "block.minecraft.deepslate_tile_wall": "cnoro'i tapla bitmu", "block.minecraft.deepslate_tiles": "c<PERSON><PERSON>'i tapla", "block.minecraft.detector_rail": "ganse teltrene", "block.minecraft.diamond_block": "tabjme bliku", "block.minecraft.diamond_ore": "tabjme kunra", "block.minecraft.diorite": "r<PERSON><PERSON><PERSON>'ori", "block.minecraft.diorite_slab": "r<PERSON><PERSON><PERSON>'ori xabybli", "block.minecraft.diorite_stairs": "rok<PERSON><PERSON>'ori serti", "block.minecraft.diorite_wall": "r<PERSON><PERSON><PERSON>'ori bitmu", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "der<PERSON>", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "drak<PERSON> sovda", "block.minecraft.dragon_head": "lo stedu be lo drakono", "block.minecraft.dragon_wall_head": "drakono sedbo'u be ne'a bitmu", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "se sudgau vraike bliku", "block.minecraft.dripstone_block": "lo digyligykamju cu bliku", "block.minecraft.dropper": "falcru", "block.minecraft.emerald_block": "ri'oj<PERSON> bliku", "block.minecraft.emerald_ore": "ri'ojme kunra", "block.minecraft.enchanting_table": "makfa jubme", "block.minecraft.end_gateway": ".endre vorme", "block.minecraft.end_portal": ".endre vorme", "block.minecraft.end_portal_frame": "end vorme greku", "block.minecraft.end_rod": ".endre grana", "block.minecraft.end_stone": ".endre r<PERSON>", "block.minecraft.end_stone_brick_slab": ".endre rokce bo bliku xabybli", "block.minecraft.end_stone_brick_stairs": "ke .endre rokci bliku ke'e serti", "block.minecraft.end_stone_brick_wall": ".endre rokci kitybli bitmu", "block.minecraft.end_stone_bricks": ".endre ke rokci bliku", "block.minecraft.ender_chest": ".endre vasru", "block.minecraft.exposed_chiseled_copper": "pafi'uci<PERSON>'e cicnybi'o ke selja'i ke tunka bliku", "block.minecraft.exposed_copper": "pafi'uciva'e cicnybi'o ke tunka bliku", "block.minecraft.exposed_copper_bulb": "pafi'uci<PERSON>'e cicnybi'o ke tunka tergu'i", "block.minecraft.exposed_copper_door": "pafi'uci<PERSON>'e cicnybi'o ke tunka vrogai", "block.minecraft.exposed_copper_grate": "pafi'uci<PERSON>'e cicnybi'o ke tunka ga'avro", "block.minecraft.exposed_copper_trapdoor": "pafi'uci<PERSON>'e cicnybi'o ke tunka lolvrogai", "block.minecraft.exposed_cut_copper": "pafi'uciva'e cicnybi'o ke selsraku ke tunka bliku", "block.minecraft.exposed_cut_copper_slab": "paf<PERSON>'uci<PERSON>'e cicnybi'o ke selsraku ke tunka xabybli", "block.minecraft.exposed_cut_copper_stairs": "pafi'uciva'e cicnybi'o ke selsraku ke tunka serti", "block.minecraft.farmland": "selcange", "block.minecraft.fern": "filcina", "block.minecraft.fire": "fagri", "block.minecraft.fire_coral": "fagtai r<PERSON>", "block.minecraft.fire_coral_block": "fagtai r<PERSON><PERSON><PERSON><PERSON> bliku", "block.minecraft.fire_coral_fan": "fag<PERSON> r<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_wall_fan": "fag<PERSON> r<PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "block.minecraft.firefly_bush": "Firefly Bush", "block.minecraft.fletching_table": "celga'azbajbu", "block.minecraft.flower_pot": "<PERSON><PERSON>la pat<PERSON>u", "block.minecraft.flowering_azalea": "rulselpau ricnrododendro", "block.minecraft.flowering_azalea_leaves": "r<PERSON>l<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frogspawn": "banfifipso'a", "block.minecraft.frosted_ice": "sel<PERSON><PERSON>nsa bisli", "block.minecraft.furnace": "<PERSON><PERSON><PERSON> to<PERSON>", "block.minecraft.gilded_blackstone": "slovau xekro'i", "block.minecraft.glass": "blaci", "block.minecraft.glass_pane": "canko blaci", "block.minecraft.glow_lichen": "<PERSON><PERSON> clika", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "solji bliku", "block.minecraft.gold_ore": "solji kunra", "block.minecraft.granite": "rokcrgranu", "block.minecraft.granite_slab": "rok<PERSON><PERSON><PERSON><PERSON> x<PERSON>li", "block.minecraft.granite_stairs": "rok<PERSON><PERSON><PERSON><PERSON> serti", "block.minecraft.granite_wall": "rokcrgranu bitmu", "block.minecraft.grass": "s<PERSON>u", "block.minecraft.grass_block": "s<PERSON>u bliku", "block.minecraft.gravel": "rok<PERSON> canre", "block.minecraft.gray_banner": "g<PERSON><PERSON> s<PERSON>", "block.minecraft.gray_bed": "g<PERSON>i c<PERSON>a", "block.minecraft.gray_candle": "grusi la<PERSON>i", "block.minecraft.gray_candle_cake": "grusi lakter<PERSON>'i kenka", "block.minecraft.gray_carpet": "g<PERSON>i b<PERSON>", "block.minecraft.gray_concrete": "grusi xumrkonk<PERSON>o", "block.minecraft.gray_concrete_powder": "grusi xumrkonkreto purmo", "block.minecraft.gray_glazed_terracotta": "le grusi stakrterakota poi jelca", "block.minecraft.gray_shulker_box": "grusi ke me la .cylkr. tanxe", "block.minecraft.gray_stained_glass": "g<PERSON><PERSON> s<PERSON> blaci", "block.minecraft.gray_stained_glass_pane": "g<PERSON>i skari canko blaci", "block.minecraft.gray_terracotta": "g<PERSON>i sta<PERSON>", "block.minecraft.gray_wool": "g<PERSON>i sunla", "block.minecraft.green_banner": "crino srilanci", "block.minecraft.green_bed": "crino ckana", "block.minecraft.green_candle": "crino lakter<PERSON>'i", "block.minecraft.green_candle_cake": "crino lakter<PERSON>'i kenka", "block.minecraft.green_carpet": "crino buklo<PERSON>", "block.minecraft.green_concrete": "crino xumrkonkreto", "block.minecraft.green_concrete_powder": "crino xumrkonkreto purmo", "block.minecraft.green_glazed_terracotta": "le crino stakrterakota poi se fagri", "block.minecraft.green_shulker_box": "crino ke me la .cylkr. tanxe", "block.minecraft.green_stained_glass": "crino skari blaci", "block.minecraft.green_stained_glass_pane": "crino skari canko blaci", "block.minecraft.green_terracotta": "crino stakrterakota", "block.minecraft.green_wool": "crino sunla", "block.minecraft.grindstone": "zal<PERSON>'u", "block.minecraft.hanging_roots": "dandu genja", "block.minecraft.hay_block": "su<PERSON><PERSON><PERSON><PERSON> bakfu", "block.minecraft.heavy_core": "Heavy Core", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON><PERSON> da<PERSON><PERSON>", "block.minecraft.honey_block": "melmi bliku", "block.minecraft.honeycomb_block": "biclakse bliku", "block.minecraft.hopper": "se'arko'u", "block.minecraft.horn_coral": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.horn_coral_block": "<PERSON><PERSON><PERSON><PERSON> ric<PERSON><PERSON><PERSON> bliku", "block.minecraft.horn_coral_fan": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "block.minecraft.ice": "bisli", "block.minecraft.infested_chiseled_stone_bricks": "cinki ke rakpra rokci kit<PERSON>li", "block.minecraft.infested_cobblestone": "spirocki se infeste", "block.minecraft.infested_cracked_stone_bricks": "cinki ke fenra rokci kit<PERSON>li", "block.minecraft.infested_deepslate": "malxa'u cnoro'i", "block.minecraft.infested_mossy_stone_bricks": "cinki ke clika rokci kitybli", "block.minecraft.infested_stone": "cinki rokci", "block.minecraft.infested_stone_bricks": "cinki ke rokci kitybli", "block.minecraft.iron_bars": "tirse garna", "block.minecraft.iron_block": "tirse bliku", "block.minecraft.iron_door": "tirse v<PERSON>ai", "block.minecraft.iron_ore": "tirse kunra", "block.minecraft.iron_trapdoor": "tirse lo<PERSON>", "block.minecraft.jack_o_lantern": "selte'a guzmrkukurbita", "block.minecraft.jigsaw": "stura kruca bliku", "block.minecraft.jukebox": "zgike bliku", "block.minecraft.jungle_button": "cicricfoi batke", "block.minecraft.jungle_door": "cicricfoi v<PERSON>ai", "block.minecraft.jungle_fence": "cicricfoi garbi'u", "block.minecraft.jungle_fence_gate": "cicricfoi gar<PERSON>v<PERSON>ai", "block.minecraft.jungle_hanging_sign": "cicricfoi ke dandu lerta<PERSON>la", "block.minecraft.jungle_leaves": "cicric<PERSON>i pezli", "block.minecraft.jungle_log": "cicric<PERSON>i stani", "block.minecraft.jungle_planks": "cicricfoi mudri tanbo", "block.minecraft.jungle_pressure_plate": "cicricfoi danryta<PERSON>la", "block.minecraft.jungle_sapling": "cicric<PERSON>i c<PERSON>u", "block.minecraft.jungle_sign": "cicricfoi mudri sinxa", "block.minecraft.jungle_slab": "cicricfoi xabybli", "block.minecraft.jungle_stairs": "cicricfoi serti", "block.minecraft.jungle_trapdoor": "cicricfoi lo<PERSON>", "block.minecraft.jungle_wall_hanging_sign": "bitmu ke cicricfoi ke dandu lertapla", "block.minecraft.jungle_wall_sign": "cicricfoi mudri ke bitmu sinxa", "block.minecraft.jungle_wood": "cicricfoi mudri", "block.minecraft.kelp": "vraike", "block.minecraft.kelp_plant": "spati vraike", "block.minecraft.ladder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lantern": "lanru", "block.minecraft.lapis_block": "Block of Lapis Lazuli", "block.minecraft.lapis_ore": "blaro'i kunra", "block.minecraft.large_amethyst_bud": "barda ke nalma'u .amtisti", "block.minecraft.large_fern": "barda filcina", "block.minecraft.lava": "rum<PERSON>'i", "block.minecraft.lava_cauldron": "<PERSON><PERSON>", "block.minecraft.leaf_litter": "Leaf Litter", "block.minecraft.lectern": "nunctuta'a jubme", "block.minecraft.lever": "vraga", "block.minecraft.light": "g<PERSON>ni", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON> s<PERSON>an<PERSON>", "block.minecraft.light_blue_bed": "labybla ckana", "block.minecraft.light_blue_candle": "labybla laktergu'i", "block.minecraft.light_blue_candle_cake": "labybla laktergu'i kenka", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON> buk<PERSON>", "block.minecraft.light_blue_concrete": "labybla xumrkonkreto", "block.minecraft.light_blue_concrete_powder": "labybla xumrkonkreto purmo", "block.minecraft.light_blue_glazed_terracotta": "le labybla stakrterakota poi se fagri", "block.minecraft.light_blue_shulker_box": "labybla ke me la .cylkr. tanxe", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> skari blaci", "block.minecraft.light_blue_stained_glass_pane": "labybla skari canko blaci", "block.minecraft.light_blue_terracotta": "labybla sta<PERSON>rter<PERSON>ota", "block.minecraft.light_blue_wool": "<PERSON><PERSON><PERSON>la", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.light_gray_bed": "lab<PERSON><PERSON><PERSON> c<PERSON>a", "block.minecraft.light_gray_candle": "labyrus lakt<PERSON><PERSON>'i", "block.minecraft.light_gray_candle_cake": "labyrus lakter<PERSON>'i kenka", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON><PERSON> buk<PERSON>", "block.minecraft.light_gray_concrete": "labgrusi xumrkonkreto", "block.minecraft.light_gray_concrete_powder": "labgrusi xumrkonkreto purmo", "block.minecraft.light_gray_glazed_terracotta": "le labyrus stakrterakota poi jelca", "block.minecraft.light_gray_shulker_box": "labgrusi ke me la .cylkr. tanxe", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON><PERSON> skari blaci", "block.minecraft.light_gray_stained_glass_pane": "lab<PERSON><PERSON><PERSON> skari canko blaci", "block.minecraft.light_gray_terracotta": "labg<PERSON><PERSON> sta<PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON><PERSON> sunla", "block.minecraft.light_weighted_pressure_plate": "lo linto danrytapla", "block.minecraft.lightning_rod": "se lindi grana", "block.minecraft.lilac": "tric<PERSON><PERSON>a", "block.minecraft.lily_of_the_valley": "lin<PERSON>", "block.minecraft.lily_pad": "nimfaia", "block.minecraft.lime_banner": "p<PERSON><PERSON>'o s<PERSON><PERSON>ci", "block.minecraft.lime_bed": "p<PERSON><PERSON>'o c<PERSON>a", "block.minecraft.lime_candle": "pelri'o lakter<PERSON>'i", "block.minecraft.lime_candle_cake": "pel<PERSON>'o lakter<PERSON>'i kenka", "block.minecraft.lime_carpet": "p<PERSON><PERSON>'o buklo<PERSON>", "block.minecraft.lime_concrete": "p<PERSON><PERSON>'o xumrkonkreto", "block.minecraft.lime_concrete_powder": "pel<PERSON>'o xumrkonkreto purmo", "block.minecraft.lime_glazed_terracotta": "le pelri'o stakrterakota poi jelca", "block.minecraft.lime_shulker_box": "pelri'o ke me la .cylkr. tanxe", "block.minecraft.lime_stained_glass": "p<PERSON><PERSON><PERSON>o skari blaci", "block.minecraft.lime_stained_glass_pane": "p<PERSON><PERSON><PERSON>o skari canko blaci", "block.minecraft.lime_terracotta": "pelri'o sta<PERSON>rter<PERSON>", "block.minecraft.lime_wool": "pel<PERSON>'o sunla", "block.minecraft.lodestone": "maksi", "block.minecraft.loom": "bukyzbami'i", "block.minecraft.magenta_banner": "nukni s<PERSON>", "block.minecraft.magenta_bed": "nukni c<PERSON>a", "block.minecraft.magenta_candle": "nukni la<PERSON>i", "block.minecraft.magenta_candle_cake": "nukni lakter<PERSON>'i kenka", "block.minecraft.magenta_carpet": "nukni buk<PERSON>", "block.minecraft.magenta_concrete": "nukni xumrkonkreto", "block.minecraft.magenta_concrete_powder": "nukni xumrkonkreto purmo", "block.minecraft.magenta_glazed_terracotta": "le nukni stakrterakota poi jelca", "block.minecraft.magenta_shulker_box": "nukni ke me la .cylkr. tanxe", "block.minecraft.magenta_stained_glass": "nukni s<PERSON> blaci", "block.minecraft.magenta_stained_glass_pane": "nukni skari canko blaci", "block.minecraft.magenta_terracotta": "nukni sta<PERSON>", "block.minecraft.magenta_wool": "nukni sunla", "block.minecraft.magma_block": "rum<PERSON>'i bliku", "block.minecraft.mangrove_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> batke", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>ai", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitmu", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON> gar<PERSON>", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON><PERSON>i ke dandu lerta<PERSON>la", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>li", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON><PERSON><PERSON> stani", "block.minecraft.mangrove_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON> mudri tanbo", "block.minecraft.mangrove_pressure_plate": "Mangrove Pressure Plate", "block.minecraft.mangrove_propagule": "Mangrove Propagule", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON> genja", "block.minecraft.mangrove_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> vinpa", "block.minecraft.mangrove_slab": "ji<PERSON>ric<PERSON>i xabybli", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON><PERSON>i serti", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "block.minecraft.mangrove_wall_hanging_sign": "bitmu ke jiryricfoi ke dandu lertapla", "block.minecraft.mangrove_wall_sign": "Mangrove Wall Sign", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON> mudri", "block.minecraft.medium_amethyst_bud": "cnano ke nalma'u .amtisti", "block.minecraft.melon": "guzme", "block.minecraft.melon_stem": "guzme stani", "block.minecraft.moss_block": "clika bliku", "block.minecraft.moss_carpet": "clika lolgai", "block.minecraft.mossy_cobblestone": "clika s<PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone_slab": "clika spirokci xabybli", "block.minecraft.mossy_cobblestone_stairs": "clika spirokci serti", "block.minecraft.mossy_cobblestone_wall": "clika spirok<PERSON> bitmu", "block.minecraft.mossy_stone_brick_slab": "clika rokci bo bliku xabybli", "block.minecraft.mossy_stone_brick_stairs": "clika rokci bo bliku serti", "block.minecraft.mossy_stone_brick_wall": "clika rokci bo bliku bitmu", "block.minecraft.mossy_stone_bricks": "clika ke rokci bliku", "block.minecraft.moving_piston": "muvdu ca'erslanu", "block.minecraft.mud": "lo derpesxu", "block.minecraft.mud_brick_slab": "Mud <PERSON> Slab", "block.minecraft.mud_brick_stairs": "Mud Brick Stairs", "block.minecraft.mud_brick_wall": "Mud Brick Wall", "block.minecraft.mud_bricks": "derpesxu kitybli", "block.minecraft.muddy_mangrove_roots": "Muddy Mangrove Roots", "block.minecraft.mushroom_stem": "<PERSON><PERSON> stani", "block.minecraft.mycelium": "mledi", "block.minecraft.nether_brick_fence": "la me neter. kitybli garbi'u", "block.minecraft.nether_brick_slab": "la me neter. xadba", "block.minecraft.nether_brick_stairs": "la me neter. kitybli serti", "block.minecraft.nether_brick_wall": "la me neter. kitybli bitmu", "block.minecraft.nether_bricks": "b<PERSON><PERSON>'eri", "block.minecraft.nether_gold_ore": "me la .ne'er. solji kunra", "block.minecraft.nether_portal": "palci canko", "block.minecraft.nether_quartz_ore": "kril<PERSON>'eri kunra", "block.minecraft.nether_sprouts": "dzimu'e cifspa", "block.minecraft.nether_wart": "me la neter. spati", "block.minecraft.nether_wart_block": "me la neter. spati bliku", "block.minecraft.netherite_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bliku", "block.minecraft.netherrack": "me la neterak.", "block.minecraft.note_block": "tonga bliku", "block.minecraft.oak_button": "cindu batke", "block.minecraft.oak_door": "cindu vrogai", "block.minecraft.oak_fence": "cindu garbi'u", "block.minecraft.oak_fence_gate": "cindu gar<PERSON>", "block.minecraft.oak_hanging_sign": "cindu ke dandu lertapla", "block.minecraft.oak_leaves": "cindu pezli", "block.minecraft.oak_log": "cindu stani", "block.minecraft.oak_planks": "cindu mudri tanbo", "block.minecraft.oak_pressure_plate": "lo cindu danrytapla", "block.minecraft.oak_sapling": "cindu c<PERSON>u", "block.minecraft.oak_sign": "cindu sinxa", "block.minecraft.oak_slab": "cindu xabybli", "block.minecraft.oak_stairs": "cindu serti", "block.minecraft.oak_trapdoor": "cindu lo<PERSON>", "block.minecraft.oak_wall_hanging_sign": "bitmu ke cindu ke dandu lertapla", "block.minecraft.oak_wall_sign": "cindu ke bitmu sinxa", "block.minecraft.oak_wood": "cindu mudri", "block.minecraft.observer": "zgagau", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "bur<PERSON><PERSON><PERSON> pip<PERSON>i", "block.minecraft.ominous_banner": "c<PERSON><PERSON> lanci", "block.minecraft.open_eyeblossom": "Open Eyeblossom", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_bed": "narju <PERSON><PERSON>a", "block.minecraft.orange_candle": "narju <PERSON>i", "block.minecraft.orange_candle_cake": "narji la<PERSON>i kenka", "block.minecraft.orange_carpet": "<PERSON>r<PERSON> b<PERSON>", "block.minecraft.orange_concrete": "narju x<PERSON>k<PERSON>o", "block.minecraft.orange_concrete_powder": "narju xumrkonkreto purmo", "block.minecraft.orange_glazed_terracotta": "le narju stakrterakota poi se fagri", "block.minecraft.orange_shulker_box": "narju ke me la .cylkr. tanxe", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON> s<PERSON> blaci", "block.minecraft.orange_stained_glass_pane": "nar<PERSON> skari canko blaci", "block.minecraft.orange_terracotta": "narju sta<PERSON>", "block.minecraft.orange_tulip": "narju tujli", "block.minecraft.orange_wool": "nar<PERSON>", "block.minecraft.oxeye_daisy": "sparb<PERSON>", "block.minecraft.oxidized_chiseled_copper": "cifi'uciva'e cicnybi'o ke selja'i ke tunka bliku", "block.minecraft.oxidized_copper": "cifi'uciva'e cicnybi'o ke tunka bliku", "block.minecraft.oxidized_copper_bulb": "cifi'uciva'e cicnybi'o ke tunka tergu'i", "block.minecraft.oxidized_copper_door": "cifi'uciva'e cicnybi'o ke tunka vrogai", "block.minecraft.oxidized_copper_grate": "cifi'uciva'e cicnybi'o ke tunka ga'avro", "block.minecraft.oxidized_copper_trapdoor": "cifi'uciva'e cicnybi'o ke tunka lolvrogai", "block.minecraft.oxidized_cut_copper": "cifi'uciva'e cicnybi'o ke selsraku ke tunka bliku", "block.minecraft.oxidized_cut_copper_slab": "cifi'uciva'e cicnybi'o ke selsraku ke tunka xabybli", "block.minecraft.oxidized_cut_copper_stairs": "cifi'uciva'e cicnybi'o ke selsraku ke tunka serti", "block.minecraft.packed_ice": "demri'a bisli", "block.minecraft.packed_mud": "lo demri'a derpesxu", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON>", "block.minecraft.pale_moss_block": "Pale Moss Block", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON> Moss Carpet", "block.minecraft.pale_oak_button": "Pale Oak Button", "block.minecraft.pale_oak_door": "Pale Oak Door", "block.minecraft.pale_oak_fence": "Pale Oak Fence", "block.minecraft.pale_oak_fence_gate": "Pale Oak Fence Gate", "block.minecraft.pale_oak_hanging_sign": "Pale Oak Hanging Sign", "block.minecraft.pale_oak_leaves": "Pale Oak Leaves", "block.minecraft.pale_oak_log": "Pale Oak Log", "block.minecraft.pale_oak_planks": "Pale Oak Planks", "block.minecraft.pale_oak_pressure_plate": "Pale Oak Pressure Plate", "block.minecraft.pale_oak_sapling": "Pale Oak Sapling", "block.minecraft.pale_oak_sign": "Pale Oak Sign", "block.minecraft.pale_oak_slab": "Pale Oak Slab", "block.minecraft.pale_oak_stairs": "Pale Oak Stairs", "block.minecraft.pale_oak_trapdoor": "Pale Oak Trapdoor", "block.minecraft.pale_oak_wall_hanging_sign": "Pale Oak Wall Hanging Sign", "block.minecraft.pale_oak_wall_sign": "Pale Oak Wall Sign", "block.minecraft.pale_oak_wood": "Pale Oak Wood", "block.minecraft.pearlescent_froglight": "cakyjmepi'a pipyban<PERSON>'i", "block.minecraft.peony": "xun<PERSON>bi xrula", "block.minecraft.petrified_oak_slab": "se rokygau cindu xabybli", "block.minecraft.piglin_head": "stedu be la .piglin.", "block.minecraft.piglin_wall_head": "stedu be la .piglin. bei ne'a lo bitmu", "block.minecraft.pink_banner": "kuc<PERSON> srilanci", "block.minecraft.pink_bed": "xun<PERSON>bi ckana", "block.minecraft.pink_candle": "penka lakter<PERSON>'i", "block.minecraft.pink_candle_cake": "penka laktergu'i kenka", "block.minecraft.pink_carpet": "kuctai buklolgai", "block.minecraft.pink_concrete": "xunblabi xumrkonkreto", "block.minecraft.pink_concrete_powder": "xunblabi xumrkonkreto purmo", "block.minecraft.pink_glazed_terracotta": "le xunblabi stakrterakota poi se fagri", "block.minecraft.pink_petals": "Pink Petals", "block.minecraft.pink_shulker_box": "labnukni ke me la .cylkr. tanxe", "block.minecraft.pink_stained_glass": "xun<PERSON><PERSON> skari blaci", "block.minecraft.pink_stained_glass_pane": "k<PERSON>tai skari canko blaci", "block.minecraft.pink_terracotta": "xunblabi stakrterakota", "block.minecraft.pink_tulip": "xun<PERSON>bi tujli", "block.minecraft.pink_wool": "<PERSON><PERSON><PERSON><PERSON> sunla", "block.minecraft.piston": "ca'erslanu", "block.minecraft.piston_head": "ca'er<PERSON>nu stedu", "block.minecraft.pitcher_crop": "Pitcher C<PERSON>", "block.minecraft.pitcher_plant": "Pitcher Plant", "block.minecraft.player_head": "stedu be lo kelci", "block.minecraft.player_head.named": "stedu be la'oi %s", "block.minecraft.player_wall_head": "kelci sedbo'u be ne'a bitmu", "block.minecraft.podzol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pointed_dripstone": "lo digyligykamju", "block.minecraft.polished_andesite": "se spali rok<PERSON>rande'i", "block.minecraft.polished_andesite_slab": "se spali rokcrande'i xabybli", "block.minecraft.polished_andesite_stairs": "se spali rokcrande'i serti", "block.minecraft.polished_basalt": "se spali kam<PERSON>'i", "block.minecraft.polished_blackstone": "sels<PERSON>i xekro'i", "block.minecraft.polished_blackstone_brick_slab": "selspali xekro'i kubli xabybli", "block.minecraft.polished_blackstone_brick_stairs": "selspali xekro'i kubli serti", "block.minecraft.polished_blackstone_brick_wall": "selspali xek<PERSON>'i kubli bitmu", "block.minecraft.polished_blackstone_bricks": "selspali xekro'i kubli bliku", "block.minecraft.polished_blackstone_button": "sels<PERSON>i xek<PERSON>'i batke", "block.minecraft.polished_blackstone_pressure_plate": "selspali xekro'i danrytapla", "block.minecraft.polished_blackstone_slab": "selspali xekro'i xabybli", "block.minecraft.polished_blackstone_stairs": "selspali xekro'i serti", "block.minecraft.polished_blackstone_wall": "sels<PERSON>i xek<PERSON>'i bitmu", "block.minecraft.polished_deepslate": "xutla cnoro'i", "block.minecraft.polished_deepslate_slab": "x<PERSON>la cnor<PERSON>'i xadba", "block.minecraft.polished_deepslate_stairs": "xutla cnoro'i serti", "block.minecraft.polished_deepslate_wall": "xutla cnoro'i bitmu", "block.minecraft.polished_diorite": "se spali rok<PERSON>rdi'ori", "block.minecraft.polished_diorite_slab": "se spali rok<PERSON>rdi'ori xabybli", "block.minecraft.polished_diorite_stairs": "se spali rok<PERSON>rdi'ori serti", "block.minecraft.polished_granite": "se spali rokcrgranu", "block.minecraft.polished_granite_slab": "se spali rokcrgranu xabybli", "block.minecraft.polished_granite_stairs": "se spali rokcrgranu serti", "block.minecraft.polished_tuff": "selspali fagma'apucro'i", "block.minecraft.polished_tuff_slab": "selspali fagma'apucro'i xabybli", "block.minecraft.polished_tuff_stairs": "selspali fagma'apucro'i serti", "block.minecraft.polished_tuff_wall": "selspali fagma'apucro'i bitmu", "block.minecraft.poppy": "ma<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "paxne'i ke .atkaciia ciftricu", "block.minecraft.potted_allium": "paxne'i citsluni", "block.minecraft.potted_azalea_bush": "spapatxu co vasru be lo ricnrododendro", "block.minecraft.potted_azure_bluet": "Potted Azure Bluet", "block.minecraft.potted_bamboo": "spapatxu co vasru lo bambu", "block.minecraft.potted_birch_sapling": "paxne'i ke .eptula ciftricu", "block.minecraft.potted_blue_orchid": "paxne'i ke blanu spatrofri", "block.minecraft.potted_brown_mushroom": "paxne'i ke bunre gumri", "block.minecraft.potted_cactus": "pax<PERSON><PERSON>i jes<PERSON>pa", "block.minecraft.potted_cherry_sapling": "spapatxu co vasru be lo ricrceraso ciftricu", "block.minecraft.potted_closed_eyeblossom": "Potted Closed Eyeblossom", "block.minecraft.potted_cornflower": "spapatxu co vasru lo centaureia", "block.minecraft.potted_crimson_fungus": "kabri joi ke zirxu'e gumri", "block.minecraft.potted_crimson_roots": "kabri joi ke zirxu'e genja", "block.minecraft.potted_dandelion": "paxne'i spartaraksaku", "block.minecraft.potted_dark_oak_sapling": "paxne'i ke mancindu c<PERSON>ricu", "block.minecraft.potted_dead_bush": "paxne'i ke morsi demspa", "block.minecraft.potted_fern": "paxne'i filcina", "block.minecraft.potted_flowering_azalea_bush": "spapatxu co vasru be lo rulselpau ricnrododendro", "block.minecraft.potted_jungle_sapling": "paxne'i ke cicricfoi ciftricu", "block.minecraft.potted_lily_of_the_valley": "spapatxu co vasru lo linlani", "block.minecraft.potted_mangrove_propagule": "Potted Mangrove Propagule", "block.minecraft.potted_oak_sapling": "paxne'i ke cindu ciftricu", "block.minecraft.potted_open_eyeblossom": "Potted Open Eyeblossom", "block.minecraft.potted_orange_tulip": "paxne'i ke narju tujli", "block.minecraft.potted_oxeye_daisy": "Potted Oxeye Daisy", "block.minecraft.potted_pale_oak_sapling": "Potted Pale Oak Sapling", "block.minecraft.potted_pink_tulip": "paxne'i ke penka tujli", "block.minecraft.potted_poppy": "p<PERSON><PERSON>'<PERSON> ma<PERSON>pi", "block.minecraft.potted_red_mushroom": "paxne'i ke xunre gumri", "block.minecraft.potted_red_tulip": "paxne'i ke xunre tujli", "block.minecraft.potted_spruce_sapling": "paxne'i ke konku'u ciftricu", "block.minecraft.potted_torchflower": "spapatxu co vasru be lo fagytergusxrula", "block.minecraft.potted_warped_fungus": "kabri joi ke cizra gumri", "block.minecraft.potted_warped_roots": "kabri joi ke cizra genja", "block.minecraft.potted_white_tulip": "paxne'i ke blabi tujli", "block.minecraft.potted_wither_rose": "spapatxu co vasru lo me la .uiter. xrula", "block.minecraft.powder_snow": "pumsi'e", "block.minecraft.powder_snow_cauldron": "brapatxu be lo pumsi'e", "block.minecraft.powered_rail": "dikca teltrene", "block.minecraft.prismarine": "me la prismarin.", "block.minecraft.prismarine_brick_slab": "kityxabybli be la prismarin.", "block.minecraft.prismarine_brick_stairs": "kitybli serti be la prismarin.", "block.minecraft.prismarine_bricks": "bliku be la prismarin.", "block.minecraft.prismarine_slab": "xabybli be me la prismarin.", "block.minecraft.prismarine_stairs": "serti be me la prismarin.", "block.minecraft.prismarine_wall": "me la prismarin. bitmu", "block.minecraft.pumpkin": "guzmrkukurbita", "block.minecraft.pumpkin_stem": "guzmrkukurbita stani", "block.minecraft.purple_banner": "zir<PERSON> s<PERSON>", "block.minecraft.purple_bed": "zirpu ckana", "block.minecraft.purple_candle": "zirpu lakt<PERSON>'i", "block.minecraft.purple_candle_cake": "zirpu laktergu'i kenka", "block.minecraft.purple_carpet": "zir<PERSON> buk<PERSON>", "block.minecraft.purple_concrete": "zirpu xumrkonkreto", "block.minecraft.purple_concrete_powder": "zirpu xumrkonkreto purmo", "block.minecraft.purple_glazed_terracotta": "le zirpu stakrterakota poi jelca", "block.minecraft.purple_shulker_box": "zirpu ke me la .cylkr. tanxe", "block.minecraft.purple_stained_glass": "zir<PERSON> skari blaci", "block.minecraft.purple_stained_glass_pane": "zir<PERSON> skari canko blaci", "block.minecraft.purple_terracotta": "zirpu sta<PERSON>rterakota", "block.minecraft.purple_wool": "zirpu sunla", "block.minecraft.purpur_block": "purpur bliku", "block.minecraft.purpur_pillar": "kamju ke purpur bliku", "block.minecraft.purpur_slab": "purpur xadba", "block.minecraft.purpur_stairs": "purpur serti", "block.minecraft.quartz_block": "krili bliku", "block.minecraft.quartz_bricks": "k<PERSON><PERSON>rtso kubli bliku", "block.minecraft.quartz_pillar": "ka<PERSON>ju k<PERSON>o", "block.minecraft.quartz_slab": "krili xadba", "block.minecraft.quartz_stairs": "krili serti", "block.minecraft.rail": "teltrene", "block.minecraft.raw_copper_block": "na<PERSON><PERSON><PERSON><PERSON><PERSON>'e tunka bliku", "block.minecraft.raw_gold_block": "nals<PERSON>ru<PERSON><PERSON>'e solji bliku", "block.minecraft.raw_iron_block": "na<PERSON><PERSON><PERSON><PERSON><PERSON>'e tirse bliku", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.red_bed": "x<PERSON>re c<PERSON>a", "block.minecraft.red_candle": "x<PERSON><PERSON> la<PERSON>i", "block.minecraft.red_candle_cake": "x<PERSON><PERSON> lakter<PERSON>'i kenka", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.red_concrete": "x<PERSON><PERSON> x<PERSON>o", "block.minecraft.red_concrete_powder": "x<PERSON>re xumrkonkreto purmo", "block.minecraft.red_glazed_terracotta": "le xunre stakrterakota poi jelca", "block.minecraft.red_mushroom": "x<PERSON><PERSON> gumri", "block.minecraft.red_mushroom_block": "x<PERSON><PERSON> gumri bliku", "block.minecraft.red_nether_brick_slab": "xunre me la neter. bo bliku xabybli", "block.minecraft.red_nether_brick_stairs": "xunre me la .neter. bo bliku serti", "block.minecraft.red_nether_brick_wall": "xunre me la neter. kitybli bitmu", "block.minecraft.red_nether_bricks": "la me neter.kitybli co xunre", "block.minecraft.red_sand": "xunre canre", "block.minecraft.red_sandstone": "x<PERSON>re canre rokci", "block.minecraft.red_sandstone_slab": "xunre canre rokci xadba", "block.minecraft.red_sandstone_stairs": "x<PERSON>re canre rokci serti", "block.minecraft.red_sandstone_wall": "xunre canro'i bitmu", "block.minecraft.red_shulker_box": "xunre ke me la .cylkr. tanxe", "block.minecraft.red_stained_glass": "x<PERSON><PERSON> s<PERSON> blaci", "block.minecraft.red_stained_glass_pane": "x<PERSON><PERSON> skari canko blaci", "block.minecraft.red_terracotta": "x<PERSON><PERSON> sta<PERSON>", "block.minecraft.red_tulip": "xunre tujli", "block.minecraft.red_wool": "x<PERSON><PERSON> sunla", "block.minecraft.redstone_block": "me la redston. bliku", "block.minecraft.redstone_lamp": "me la redston. tergu'i", "block.minecraft.redstone_ore": "me la redston. kunra", "block.minecraft.redstone_torch": "me la redston. fagytergu'i", "block.minecraft.redstone_wall_torch": "Redstone Wall Torch", "block.minecraft.redstone_wire": "me la redston cilta", "block.minecraft.reinforced_deepslate": "lo tsali condi rokci", "block.minecraft.repeater": "me la redston. bej<PERSON>li", "block.minecraft.repeating_command_block": "<PERSON>li ziltinbe bliku", "block.minecraft.resin_block": "Block of Resin", "block.minecraft.resin_brick_slab": "Resin Brick Slab", "block.minecraft.resin_brick_stairs": "Resin Brick Stairs", "block.minecraft.resin_brick_wall": "Resin Brick Wall", "block.minecraft.resin_bricks": "Resin Bricks", "block.minecraft.resin_clump": "<PERSON><PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON><PERSON> tutci", "block.minecraft.rooted_dirt": "genja <PERSON>", "block.minecraft.rose_bush": "d<PERSON><PERSON><PERSON> rozgu", "block.minecraft.sand": "canre", "block.minecraft.sandstone": "<PERSON><PERSON> rok<PERSON>", "block.minecraft.sandstone_slab": "canre rok<PERSON> xadba", "block.minecraft.sandstone_stairs": "canre rokci serti", "block.minecraft.sandstone_wall": "canro'i bitmu", "block.minecraft.scaffolding": "t<PERSON>a", "block.minecraft.sculk": "lo sculk", "block.minecraft.sculk_catalyst": "lo sculk blimajgau", "block.minecraft.sculk_sensor": "me la .skylk. ganse", "block.minecraft.sculk_shrieker": "lo sculk bliklacpe", "block.minecraft.sculk_vein": "lo sculk senta", "block.minecraft.sea_lantern": "xamsi gustci", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": ".i galfi lo veljbe", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "r<PERSON><PERSON><PERSON><PERSON><PERSON> srasu", "block.minecraft.shroomlight": "ledyte<PERSON><PERSON>'<PERSON>", "block.minecraft.shulker_box": "me la .cylkr. tanxe", "block.minecraft.skeleton_skull": "b<PERSON><PERSON><PERSON><PERSON> sed<PERSON>'u", "block.minecraft.skeleton_wall_skull": "bogyg<PERSON>u sedbo'u be ne'a bitmu", "block.minecraft.slime_block": "<PERSON><PERSON><PERSON><PERSON><PERSON> bliku", "block.minecraft.small_amethyst_bud": "cmalu ke nalma'u .amtisti", "block.minecraft.small_dripleaf": "cm<PERSON>u sin<PERSON>", "block.minecraft.smithing_table": "tcizbajbu", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "x<PERSON>la ka<PERSON>i", "block.minecraft.smooth_quartz": "x<PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "x<PERSON><PERSON> k<PERSON> xabybli", "block.minecraft.smooth_quartz_stairs": "x<PERSON>la k<PERSON>o serti", "block.minecraft.smooth_red_sandstone": "xutla ke xunre canre rokci", "block.minecraft.smooth_red_sandstone_slab": "xutla xunre bo can<PERSON>'i xadba", "block.minecraft.smooth_red_sandstone_stairs": "xutla xunre bo canro'i serti", "block.minecraft.smooth_sandstone": "xutla ke canre rokci", "block.minecraft.smooth_sandstone_slab": "x<PERSON>la can<PERSON>'i xadba", "block.minecraft.smooth_sandstone_stairs": "xutla canro'i serti", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.smooth_stone_slab": "x<PERSON>la r<PERSON> xabybli", "block.minecraft.sniffer_egg": "zbisakci sovda", "block.minecraft.snow": "snime", "block.minecraft.snow_block": "snime bliku", "block.minecraft.soul_campfire": "pruxi gikfagri", "block.minecraft.soul_fire": "ruxfag<PERSON>", "block.minecraft.soul_lantern": "pruxi dadytergu'i", "block.minecraft.soul_sand": "ruxse'i canre", "block.minecraft.soul_soil": "pruxi <PERSON><PERSON>", "block.minecraft.soul_torch": "pruxi fagytergu'i", "block.minecraft.soul_wall_torch": "bimzva ke pruxi fagytergu'i", "block.minecraft.spawn.not_valid": ".i na ku do ckini lo ckana ja lo rapyjbe tutci vu'o poi na jai se zunti", "block.minecraft.spawner": "cupra be lo cizda'u", "block.minecraft.spawner.desc1": "Interact with Spawn Egg:", "block.minecraft.spawner.desc2": "Sets Mob Type", "block.minecraft.sponge": "panje", "block.minecraft.spore_blossom": "Spore Blossom", "block.minecraft.spruce_button": "konku'u batke", "block.minecraft.spruce_door": "kon<PERSON>'u vrogai", "block.minecraft.spruce_fence": "konku'u garbi'u", "block.minecraft.spruce_fence_gate": "konku'u garbimvrogai", "block.minecraft.spruce_hanging_sign": "konku'u ke dandu lertapla", "block.minecraft.spruce_leaves": "konku'u pezli", "block.minecraft.spruce_log": "kon<PERSON>'u stani", "block.minecraft.spruce_planks": "konku'u mudri tanbo", "block.minecraft.spruce_pressure_plate": "lo konku'u danrytapla", "block.minecraft.spruce_sapling": "konku'u c<PERSON>ricu", "block.minecraft.spruce_sign": "konku'u sinxa", "block.minecraft.spruce_slab": "kon<PERSON>'u xabybli", "block.minecraft.spruce_stairs": "konku'u serti", "block.minecraft.spruce_trapdoor": "konku'u lolvrogai", "block.minecraft.spruce_wall_hanging_sign": "bitmu ke konku'u ke dandu lertapla", "block.minecraft.spruce_wall_sign": "konku'u ke bitmu sinxa", "block.minecraft.spruce_wood": "konku'u mudri", "block.minecraft.sticky_piston": "stali ca'erslanu", "block.minecraft.stone": "r<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "x<PERSON>la r<PERSON> xabybli", "block.minecraft.stone_brick_stairs": "rok<PERSON> serti", "block.minecraft.stone_brick_wall": "Stone Brick Wall", "block.minecraft.stone_bricks": "r<PERSON><PERSON> bliku", "block.minecraft.stone_button": "rok<PERSON> batke", "block.minecraft.stone_pressure_plate": "rok<PERSON> loldi batke", "block.minecraft.stone_slab": "r<PERSON><PERSON> xadba", "block.minecraft.stone_stairs": "rok<PERSON> serti", "block.minecraft.stonecutter": "rok<PERSON><PERSON>'armi'i", "block.minecraft.stripped_acacia_log": "se pilvi'u .atka<PERSON>ia", "block.minecraft.stripped_acacia_wood": "se pilvi'u ke .atkaciia mudri", "block.minecraft.stripped_bamboo_block": "se pilvi'u bambu bliku", "block.minecraft.stripped_birch_log": "se pilvi'u .eptula", "block.minecraft.stripped_birch_wood": "se pilvi'u ke .eptula mudri", "block.minecraft.stripped_cherry_log": "se pilvi'u ke ricrceraso stani", "block.minecraft.stripped_cherry_wood": "se pilvi'u ke ricrceraso mudri", "block.minecraft.stripped_crimson_hyphae": "pilcau ke zirxu'e ledju'e", "block.minecraft.stripped_crimson_stem": "pilcau ke zirxu'e stani", "block.minecraft.stripped_dark_oak_log": "se pilvi'u mancindu", "block.minecraft.stripped_dark_oak_wood": "se pilvi'u ke mancindu mudri", "block.minecraft.stripped_jungle_log": "se pilvi'u cicricfoi", "block.minecraft.stripped_jungle_wood": "se pilvi'u ke cicricfoi mudri", "block.minecraft.stripped_mangrove_log": "se pilvi'u ji<PERSON><PERSON>i", "block.minecraft.stripped_mangrove_wood": "se pilvi'u ke jiryricfoi mudri", "block.minecraft.stripped_oak_log": "se pilvi'u cindu", "block.minecraft.stripped_oak_wood": "se pilvi'u ke cindu mudri", "block.minecraft.stripped_pale_oak_log": "Stripped Pale Oak Log", "block.minecraft.stripped_pale_oak_wood": "Stripped Pale Oak Wood", "block.minecraft.stripped_spruce_log": "se pilvi'u konku'u", "block.minecraft.stripped_spruce_wood": "se pilvi'u ke konku'u mudri", "block.minecraft.stripped_warped_hyphae": "pilcau ke cizra ledju'e", "block.minecraft.stripped_warped_stem": "pilcau ke cizra stani", "block.minecraft.structure_block": "<PERSON><PERSON> bliku", "block.minecraft.structure_void": "dinju kunti", "block.minecraft.sugar_cane": "sakta stani", "block.minecraft.sunflower": "solspa", "block.minecraft.suspicious_gravel": "selsen<PERSON> cmaro'i", "block.minecraft.suspicious_sand": "sels<PERSON><PERSON> canre", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON> d<PERSON>", "block.minecraft.tall_dry_grass": "Tall Dry Grass", "block.minecraft.tall_grass": "r<PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "block.minecraft.tall_seagrass": "clani xas<PERSON>'as<PERSON>u", "block.minecraft.target": "teryre'o", "block.minecraft.terracotta": "stakrterakota", "block.minecraft.test_block": "Test Block", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "julne blaci", "block.minecraft.tnt": "j<PERSON><PERSON>", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "fagytergu'i", "block.minecraft.torchflower": "fagyte<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "fagyte<PERSON><PERSON><PERSON><PERSON> velcange", "block.minecraft.trapped_chest": "terkavbu vasru", "block.minecraft.trial_spawner": "talsa cupra be lo cizda'u", "block.minecraft.tripwire": "terkavbu skori", "block.minecraft.tripwire_hook": "terkavbu genxu", "block.minecraft.tube_coral": "tu'urtai r<PERSON><PERSON>", "block.minecraft.tube_coral_block": "tu'urtai r<PERSON><PERSON><PERSON><PERSON> bliku", "block.minecraft.tube_coral_fan": "tu'urtai r<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "block.minecraft.tube_coral_wall_fan": "tu'urtai r<PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "block.minecraft.tuff": "fagma'apucro'i", "block.minecraft.tuff_brick_slab": "fagma'apucro'i bliku xabybli", "block.minecraft.tuff_brick_stairs": "fagma'apucro'i bliku serti", "block.minecraft.tuff_brick_wall": "fagma'apucro'i bliku bitmu", "block.minecraft.tuff_bricks": "fagma'apucro'i bliku", "block.minecraft.tuff_slab": "fagma'apucro'i xabybli", "block.minecraft.tuff_stairs": "fagma'apucro'i serti", "block.minecraft.tuff_wall": "fagma'apuc<PERSON>'i bitmu", "block.minecraft.turtle_egg": "cakyrespa sovda", "block.minecraft.twisting_vines": "sarlu li'ante", "block.minecraft.twisting_vines_plant": "sarlu li'ante stani", "block.minecraft.vault": "<PERSON><PERSON>", "block.minecraft.verdant_froglight": "crino pipybanfy<PERSON><PERSON>i", "block.minecraft.vine": "skos<PERSON>", "block.minecraft.void_air": "Void Air", "block.minecraft.wall_torch": "bitmu fagytergu'i", "block.minecraft.warped_button": "cizra batke", "block.minecraft.warped_door": "cizra vrogai", "block.minecraft.warped_fence": "cizra garbi'u", "block.minecraft.warped_fence_gate": "cizra gar<PERSON>v<PERSON>", "block.minecraft.warped_fungus": "cizra gumri", "block.minecraft.warped_hanging_sign": "cizra ke dandu lertapla", "block.minecraft.warped_hyphae": "cizra <PERSON>e", "block.minecraft.warped_nylium": "cizra ledyclika", "block.minecraft.warped_planks": "cizra tanbo", "block.minecraft.warped_pressure_plate": "cizra danryta<PERSON>la", "block.minecraft.warped_roots": "cizra genja", "block.minecraft.warped_sign": "cizra le<PERSON>la", "block.minecraft.warped_slab": "cizra xabybli", "block.minecraft.warped_stairs": "cizra serti", "block.minecraft.warped_stem": "cizra stani", "block.minecraft.warped_trapdoor": "cizra lo<PERSON>", "block.minecraft.warped_wall_hanging_sign": "bitmu ke cizra ke dandu lertapla", "block.minecraft.warped_wall_sign": "bimzva ke cizra lertapla", "block.minecraft.warped_wart_block": "cizra malspa bliku", "block.minecraft.water": "d<PERSON><PERSON>", "block.minecraft.water_cauldron": "Water Cauldron", "block.minecraft.waxed_chiseled_copper": "lakter<PERSON>'i ke selja'i ke tunka bliku", "block.minecraft.waxed_copper_block": "lakt<PERSON><PERSON>'i ke tunka bliku", "block.minecraft.waxed_copper_bulb": "lakter<PERSON>'i ke tunka tergu'i", "block.minecraft.waxed_copper_door": "lakt<PERSON><PERSON>'i ke tunka vrogai", "block.minecraft.waxed_copper_grate": "lakter<PERSON>'i ke tunka ga'avro", "block.minecraft.waxed_copper_trapdoor": "lakter<PERSON>'i ke tunka lolvrogai", "block.minecraft.waxed_cut_copper": "lakter<PERSON>'i ke selsraku ke tunka bliku", "block.minecraft.waxed_cut_copper_slab": "lakter<PERSON>'i ke selsraku ke tunka xabybli", "block.minecraft.waxed_cut_copper_stairs": "lakter<PERSON>'i ke selsraku ke tunka serti", "block.minecraft.waxed_exposed_chiseled_copper": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke selja'i ke tunka bliku", "block.minecraft.waxed_exposed_copper": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke tunka bliku", "block.minecraft.waxed_exposed_copper_bulb": "lakt<PERSON><PERSON>'i ke pafi'uciva'e cicnybi'o ke tunka tergu'i", "block.minecraft.waxed_exposed_copper_door": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke tunka vrogai", "block.minecraft.waxed_exposed_copper_grate": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke tunka ga'avro", "block.minecraft.waxed_exposed_copper_trapdoor": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke tunka lolvrogai", "block.minecraft.waxed_exposed_cut_copper": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke selsraku ke tunka bliku", "block.minecraft.waxed_exposed_cut_copper_slab": "lakt<PERSON><PERSON>'i ke pafi'uciva'e cicnybi'o ke selsraku ke tunka xabybli", "block.minecraft.waxed_exposed_cut_copper_stairs": "lakter<PERSON>'i ke pafi'uciva'e cicnybi'o ke selsraku ke tunka serti", "block.minecraft.waxed_oxidized_chiseled_copper": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke selja'i ke tunka bliku", "block.minecraft.waxed_oxidized_copper": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke tunka bliku", "block.minecraft.waxed_oxidized_copper_bulb": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke tunka tergu'i", "block.minecraft.waxed_oxidized_copper_door": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke tunka vrogai", "block.minecraft.waxed_oxidized_copper_grate": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke tunka ga'avro", "block.minecraft.waxed_oxidized_copper_trapdoor": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke tunka lolvrogai", "block.minecraft.waxed_oxidized_cut_copper": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke selsraku ke tunka bliku", "block.minecraft.waxed_oxidized_cut_copper_slab": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke selsraku ke tunka xabybli", "block.minecraft.waxed_oxidized_cut_copper_stairs": "lakter<PERSON>'i ke cifi'uciva'e cicnybi'o ke selsraku ke tunka serti", "block.minecraft.waxed_weathered_chiseled_copper": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke selja'i ke tunka bliku", "block.minecraft.waxed_weathered_copper": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke tunka bliku", "block.minecraft.waxed_weathered_copper_bulb": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke tunka tergu'i", "block.minecraft.waxed_weathered_copper_door": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke tunka vrogai", "block.minecraft.waxed_weathered_copper_grate": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke tunka ga'avro", "block.minecraft.waxed_weathered_copper_trapdoor": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke tunka lolvrogai", "block.minecraft.waxed_weathered_cut_copper": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke selsraku ke tunka bliku", "block.minecraft.waxed_weathered_cut_copper_slab": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke selsraku ke tunka xabybli", "block.minecraft.waxed_weathered_cut_copper_stairs": "lakter<PERSON>'i ke refi'uciva'e cicnybi'o ke selsraku ke tunka serti", "block.minecraft.weathered_chiseled_copper": "refi'u<PERSON><PERSON>'e cicnybi'o ke selja'i ke tunka bliku", "block.minecraft.weathered_copper": "refi'uci<PERSON>'e cicnybi'o ke tunka bliku", "block.minecraft.weathered_copper_bulb": "refi'uci<PERSON>'e cicnybi'o ke tunka tergu'i", "block.minecraft.weathered_copper_door": "refi'uci<PERSON>'e cicnybi'o ke tunka vrogai", "block.minecraft.weathered_copper_grate": "refi'uci<PERSON>'e cicnybi'o ke tunka ga'avro", "block.minecraft.weathered_copper_trapdoor": "refi'uci<PERSON>'e cicnybi'o ke tunka lolvrogai", "block.minecraft.weathered_cut_copper": "refi'uci<PERSON>'e cicnybi'o ke selsraku ke tunka bliku", "block.minecraft.weathered_cut_copper_slab": "refi'uci<PERSON>'e cicnybi'o ke selsraku ke tunka xabybli", "block.minecraft.weathered_cut_copper_stairs": "refi'uci<PERSON>'e cicnybi'o ke selsraku ke tunka serti", "block.minecraft.weeping_vines": "klaku li'ante", "block.minecraft.weeping_vines_plant": "klaku li'ante stani", "block.minecraft.wet_sponge": "cilmo panje", "block.minecraft.wheat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_banner": "blabi s<PERSON>", "block.minecraft.white_bed": "blabi c<PERSON>a", "block.minecraft.white_candle": "blabi lakter<PERSON>'i", "block.minecraft.white_candle_cake": "blabi lakter<PERSON>'i kenka", "block.minecraft.white_carpet": "blabi buklo<PERSON>", "block.minecraft.white_concrete": "blabi x<PERSON>o", "block.minecraft.white_concrete_powder": "blabi xumrkonkreto purmo", "block.minecraft.white_glazed_terracotta": "le blabi stakrterakota poi se fagri", "block.minecraft.white_shulker_box": "blabi ke me la .cylkr. tanxe", "block.minecraft.white_stained_glass": "blabi skari blaci", "block.minecraft.white_stained_glass_pane": "blabi skari canko blaci", "block.minecraft.white_terracotta": "blabi sta<PERSON>", "block.minecraft.white_tulip": "blabi tujli", "block.minecraft.white_wool": "blabi sunla", "block.minecraft.wildflowers": "Wildflowers", "block.minecraft.wither_rose": "me la .uiter. xrula", "block.minecraft.wither_skeleton_skull": "la me witer. je stedu", "block.minecraft.wither_skeleton_wall_skull": "la me .uiter. je stedu be ne'a bitmu", "block.minecraft.yellow_banner": "pelxu srilanci", "block.minecraft.yellow_bed": "pelxu ckana", "block.minecraft.yellow_candle": "pelxu lakter<PERSON>'i", "block.minecraft.yellow_candle_cake": "pelxu laktergu'i kenka", "block.minecraft.yellow_carpet": "pelxu buklo<PERSON>", "block.minecraft.yellow_concrete": "pelxu xumrkonkreto", "block.minecraft.yellow_concrete_powder": "pelxu xumrkonkreto purmo", "block.minecraft.yellow_glazed_terracotta": "le pelxu stakrterakota poi jelca", "block.minecraft.yellow_shulker_box": "pelxu ke me la .cylkr. tanxe", "block.minecraft.yellow_stained_glass": "pelxu skari blaci", "block.minecraft.yellow_stained_glass_pane": "pelxu skari canko blaci", "block.minecraft.yellow_terracotta": "pelxu stakrterakota", "block.minecraft.yellow_wool": "pelxu sunla", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON><PERSON> stedu", "block.minecraft.zombie_wall_head": "mi<PERSON><PERSON><PERSON> sedbo'u be ne'a bitmu", "book.byAuthor": "cukta fi la'oi %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "ma <PERSON>ene le cukta", "book.finalizeButton": "sevzi tcitygau gi'e fanmo gasnu vau lo cukta", "book.finalizeWarning": ".o'idai .i do sevzi tcitygau fi lo cukta ri'a lonu lonu cusku'i ba'o to'e cumki", "book.generation.0": "nalf<PERSON>'i", "book.generation.1": "fukpi", "book.generation.2": "fukpi fukpi", "book.generation.3": "spofu", "book.invalid.tag": "* Invalid book tag *", "book.pageIndicator": "%1$s moi be lo'i %2$s papri", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "sevzi tcitygau", "book.view.title": "Book View Screen", "build.tooHigh": "Height limit for building is %s", "chat.cannotSend": "na kakne lo nu benji lo notci", "chat.coordinates": "%s %s %s", "chat.coordinates.tooltip": "Click to teleport", "chat.copy": ".i fukygau", "chat.copy.click": ".i ko kli'iki ja'e lo nu rejgau", "chat.deleted_marker": "This chat message has been deleted by the server.", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> disabled due to expired profile public key. Please try reconnecting.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Chat is not allowed by account settings. Press '%s' again for more information.", "chat.disabled.profile.moreInfo": "Chat is not allowed by account settings. Cannot send or view messages.", "chat.editBox": "samta'a", "chat.filtered": "Filtered by the server.", "chat.filtered_full": "The server has hidden your message for some players.", "chat.link.confirm": ".i xu do birti lo du'u do djica lo nu kibyklama le lamji kibystu", "chat.link.confirmTrusted": ".au dai do kibykla ji fukcpa le judri", "chat.link.open": "jonse fo lo brauzero", "chat.link.warning": ".i ko noroi kib<PERSON>lama le samci'ejudri ku pe lo cizra", "chat.queue": "li'o (to %s drata selsku toi)", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sent invalid message.", "chat.tag.modified": "Message modified by the server. Original:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "Server message.", "chat.type.admin": "[%s:%s]", "chat.type.advancement.challenge": "%s has completed the challenge %s", "chat.type.advancement.goal": "%s has reached the goal %s", "chat.type.advancement.task": "%s has made the advancement %s", "chat.type.announcement": "[%s]%s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "cusku fi lo bende", "chat.type.team.sent": "la %s zo'u la %s cu cusku lu %s li'u", "chat.type.team.text": "la %s zo'u la %s cu cusku lu %s li'u", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s cusku lu %s li'u", "chat.validation_error": "Chat validation error", "chat_screen.message": "Message to send: %s", "chat_screen.title": "Chat screen", "chat_screen.usage": "Input message and press Enter to send", "chunk.toast.checkLog": "See log for more details", "chunk.toast.loadFailure": "Failed to load chunk at %s", "chunk.toast.lowDiskSpace": "Low disk space!", "chunk.toast.lowDiskSpace.description": "Might not be able to save the world.", "chunk.toast.saveFailure": "Failed to save chunk at %s", "clear.failed.multiple": "No items were found on %s players", "clear.failed.single": "No items were found on player %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "blanu", "color.minecraft.brown": "bunre", "color.minecraft.cyan": "cicna", "color.minecraft.gray": "grusi", "color.minecraft.green": "crino", "color.minecraft.light_blue": "labybla", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.lime": "pelri'o", "color.minecraft.magenta": "nukni", "color.minecraft.orange": "narju", "color.minecraft.pink": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.purple": "zirpu", "color.minecraft.red": "xunre", "color.minecraft.white": "Blabi", "color.minecraft.yellow": "pelxu", "command.context.here": "<--[VI]", "command.context.parse_error": "%s fi'o judri li %s .i cfila fa la'e di'e %s", "command.exception": "Could not parse command: %s", "command.expected.separator": "Expected whitespace to end one argument, but found trailing data", "command.failed": "An unexpected error occurred trying to execute that command", "command.forkLimit": "Maximum number of contexts (%s) reached", "command.unknown.argument": "Incorrect argument for command", "command.unknown.command": ".i le samseltibvla cu jai toldra ja na mulno .i ko lanli lo z<PERSON>re", "commands.advancement.criterionNotFound": "The advancement %1$s does not contain the criterion '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Couldn't grant criterion '%s' of advancement %s to %s players as they already have it", "commands.advancement.grant.criterion.to.many.success": "Granted criterion '%s' of advancement %s to %s players", "commands.advancement.grant.criterion.to.one.failure": "Couldn't grant criterion '%s' of advancement %s to %s as they already have it", "commands.advancement.grant.criterion.to.one.success": "Granted criterion '%s' of advancement %s to %s", "commands.advancement.grant.many.to.many.failure": "Couldn't grant %s advancements to %s players as they already have them", "commands.advancement.grant.many.to.many.success": "Granted %s advancements to %s players", "commands.advancement.grant.many.to.one.failure": "Couldn't grant %s advancements to %s as they already have them", "commands.advancement.grant.many.to.one.success": "Granted %s advancements to %s", "commands.advancement.grant.one.to.many.failure": "Couldn't grant advancement %s to %s players as they already have it", "commands.advancement.grant.one.to.many.success": "Granted the advancement %s to %s players", "commands.advancement.grant.one.to.one.failure": "Couldn't grant advancement %s to %s as they already have it", "commands.advancement.grant.one.to.one.success": "Granted the advancement %s to %s", "commands.advancement.revoke.criterion.to.many.failure": "Couldn't revoke criterion '%s' of advancement %s from %s players as they don't have it", "commands.advancement.revoke.criterion.to.many.success": "Revoked criterion '%s' of advancement %s from %s players", "commands.advancement.revoke.criterion.to.one.failure": "Couldn't revoke criterion '%s' of advancement %s from %s as they don't have it", "commands.advancement.revoke.criterion.to.one.success": "Revoked criterion '%s' of advancement %s from %s", "commands.advancement.revoke.many.to.many.failure": "Couldn't revoke %s advancements from %s players as they don't have them", "commands.advancement.revoke.many.to.many.success": "Revoked %s advancements from %s players", "commands.advancement.revoke.many.to.one.failure": "Couldn't revoke %s advancements from %s as they don't have them", "commands.advancement.revoke.many.to.one.success": "Revoked %s advancements from %s", "commands.advancement.revoke.one.to.many.failure": "Couldn't revoke advancement %s from %s players as they don't have it", "commands.advancement.revoke.one.to.many.success": "Revoked the advancement %s from %s players", "commands.advancement.revoke.one.to.one.failure": "Couldn't revoke advancement %s from %s as they don't have it", "commands.advancement.revoke.one.to.one.success": "Revoked the advancement %s from %s", "commands.attribute.base_value.get.success": ".i lo ka %s kei be kai lo %s cu jicmu klani li %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": ".i galfi lo se jicmu klani be lo ka %s kei be kai lo %s li %s", "commands.attribute.failed.entity": "%s is not a valid entity for this command", "commands.attribute.failed.modifier_already_present": ".i lo sikta be me'e zo'oi %s za'o pagbu lo ka %s kei be kai lo %s", "commands.attribute.failed.no_attribute": ".i lo %s na ckaji lo ka %s", "commands.attribute.failed.no_modifier": ".i lo ka %s kei be kai lo %s cu claxu lo sikta be me'e zo'oi %s", "commands.attribute.modifier.add.success": ".i jmina lo sikta be me'e zo'oi %s lo ka %s kei be kai lo %s", "commands.attribute.modifier.remove.success": ".i vimcu lo sikta be me'e zo'oi %s lo ka %s kei be kai lo %s", "commands.attribute.modifier.value.get.success": ".i lo sikta be me'e zo'oi %s be lo ka %s kei be kai lo %s cu klani li %s", "commands.attribute.value.get.success": ".i lo ka %s kei be kai lo %s cu klani li %s", "commands.ban.failed": "Nothing changed. The player is already banned", "commands.ban.success": "Banned %s: %s", "commands.banip.failed": "Nothing changed. That IP is already banned", "commands.banip.info": "This ban affects %s player(s): %s", "commands.banip.invalid": "Invalid IP address or unknown player", "commands.banip.success": "Banned IP %s: %s", "commands.banlist.entry": "%s was banned by %s: %s", "commands.banlist.entry.unknown": "(Narselju'o)", "commands.banlist.list": "There are %s ban(s):", "commands.banlist.none": ".i no da to'e curmi", "commands.bossbar.create.failed": "A bossbar already exists with the ID '%s'", "commands.bossbar.create.success": "Created custom bossbar %s", "commands.bossbar.get.max": "Custom bossbar %s has a maximum of %s", "commands.bossbar.get.players.none": "Custom bossbar %s has no players currently online", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "Custom bossbar %s has a value of %s", "commands.bossbar.get.visible.hidden": "Custom bossbar %s is currently hidden", "commands.bossbar.get.visible.visible": "Custom bossbar %s is currently shown", "commands.bossbar.list.bars.none": "There are no custom bossbars active", "commands.bossbar.list.bars.some": "There are %s custom bossbar(s) active: %s", "commands.bossbar.remove.success": "Removed custom bossbar %s", "commands.bossbar.set.color.success": "Custom bossbar %s has changed color", "commands.bossbar.set.color.unchanged": "Nothing changed. That's already the color of this bossbar", "commands.bossbar.set.max.success": "Custom bossbar %s has changed maximum to %s", "commands.bossbar.set.max.unchanged": "Nothing changed. That's already the max of this bossbar", "commands.bossbar.set.name.success": "Custom bossbar %s has been renamed", "commands.bossbar.set.name.unchanged": "Nothing changed. That's already the name of this bossbar", "commands.bossbar.set.players.success.none": "Custom bossbar %s no longer has any players", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "Nothing changed. Those players are already on the bossbar with nobody to add or remove", "commands.bossbar.set.style.success": "Custom bossbar %s has changed style", "commands.bossbar.set.style.unchanged": "Nothing changed. That's already the style of this bossbar", "commands.bossbar.set.value.success": "Custom bossbar %s has changed value to %s", "commands.bossbar.set.value.unchanged": "Nothing changed. That's already the value of this bossbar", "commands.bossbar.set.visibility.unchanged.hidden": "Nothing changed. The bossbar is already hidden", "commands.bossbar.set.visibility.unchanged.visible": "Nothing changed. The bossbar is already visible", "commands.bossbar.set.visible.success.hidden": "Custom bossbar %s is now hidden", "commands.bossbar.set.visible.success.visible": "Custom bossbar %s is now visible", "commands.bossbar.unknown": "No bossbar exists with the ID '%s'", "commands.clear.success.multiple": "Removed %s item(s) from %s players", "commands.clear.success.single": "Removed %s item(s) from player %s", "commands.clear.test.multiple": "Found %s matching item(s) on %s players", "commands.clear.test.single": "Found %s matching item(s) on player %s", "commands.clone.failed": "No blocks were cloned", "commands.clone.overlap": "The source and destination areas cannot overlap", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied %s damage to %s", "commands.data.block.get": "%s on block %s, %s, %s after scale factor of %s is %s", "commands.data.block.invalid": "The target block is not a block entity", "commands.data.block.modified": "Modified block data of %s, %s, %s", "commands.data.block.query": "%s, %s, %s has the following block data: %s", "commands.data.entity.get": "%s on %s after scale factor of %s is %s", "commands.data.entity.invalid": "Unable to modify player data", "commands.data.entity.modified": "Modified entity data of %s", "commands.data.entity.query": "%s has the following entity data: %s", "commands.data.get.invalid": "Can't get %s; only numeric tags are allowed", "commands.data.get.multiple": "This argument accepts a single NBT value", "commands.data.get.unknown": "Can't get %s; tag doesn't exist", "commands.data.merge.failed": ".i no da cenba .i le selcu'a selkai xa'o ckini zo'e", "commands.data.modify.expected_list": "Expected list, got: %s", "commands.data.modify.expected_object": "Expected object, got: %s", "commands.data.modify.expected_value": "Expected value, got: %s", "commands.data.modify.invalid_index": "Invalid list index: %s", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": ".i la'oi %s pe lo sorcu be me'e zo'oi %s ge'u poi se pilji fi li %s cu du li %s", "commands.data.storage.modified": ".i galfi lo sorcu be me'e zo'oi %s", "commands.data.storage.query": ".i la'oi %s sorcu zo'oi %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "Pack '%s' is not enabled!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "Pack '%s' is already enabled!", "commands.datapack.enable.failed.no_flags": "Pack '%s' cannot be enabled, since required flags are not enabled in this world: %s!", "commands.datapack.list.available.none": "There are no more data packs available", "commands.datapack.list.available.success": "There are %s data pack(s) available: %s", "commands.datapack.list.enabled.none": "There are no data packs enabled", "commands.datapack.list.enabled.success": "There are %s data pack(s) enabled: %s", "commands.datapack.modify.disable": ".i co'u sazri lo dat<PERSON>gunma be me'e zo'oi %s", "commands.datapack.modify.enable": ".i co'a sazri lo datnygunma be me'e zo'oi %s", "commands.datapack.unknown": "Unknown data pack '%s'", "commands.debug.alreadyRunning": "The tick profiler is already started", "commands.debug.function.noRecursion": "Can't trace from inside of function", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "Traced %s command(s) from %s functions to output file %s", "commands.debug.function.success.single": "Traced %s command(s) from function '%s' to output file %s", "commands.debug.function.traceFailed": "Failed to trace function", "commands.debug.notRunning": "The tick profiler hasn't started", "commands.debug.started": "Started tick profiling", "commands.debug.stopped": "Stopped tick profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.defaultgamemode.success": "The default game mode is now %s", "commands.deop.failed": "Nothing changed. The player is not an operator", "commands.deop.success": "Made %s no longer a server operator", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "The difficulty did not change; it is already set to %s", "commands.difficulty.query": "The difficulty is %s", "commands.difficulty.success": "The difficulty has been set to %s", "commands.drop.no_held_items": "Entity can't hold any items", "commands.drop.no_loot_table": "Entity %s has no loot table", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": ".i falcru loi %s dacti", "commands.drop.success.multiple_with_table": "Dropped %s items from loot table %s", "commands.drop.success.single": ".i cupra %s %s", "commands.drop.success.single_with_table": ".i cupra %s %s ra'i lo selra'e liste be me'e zo'oi %s", "commands.effect.clear.everything.failed": "Target has no effects to remove", "commands.effect.clear.everything.success.multiple": "Removed every effect from %s targets", "commands.effect.clear.everything.success.single": "Removed every effect from %s", "commands.effect.clear.specific.failed": "Target doesn't have the requested effect", "commands.effect.clear.specific.success.multiple": "Removed effect %s from %s targets", "commands.effect.clear.specific.success.single": "Removed effect %s from %s", "commands.effect.give.failed": "Unable to apply this effect (target is either immune to effects, or has something stronger)", "commands.effect.give.success.multiple": "Applied effect %s to %s targets", "commands.effect.give.success.single": "Applied effect %s to %s", "commands.enchant.failed": "Nothing changed. Targets either have no item in their hands or the enchantment could not be applied", "commands.enchant.failed.entity": "%s is not a valid entity for this command", "commands.enchant.failed.incompatible": "%s cannot support that enchantment", "commands.enchant.failed.itemless": "%s is not holding any item", "commands.enchant.failed.level": "%s is higher than the maximum level of %s supported by that enchantment", "commands.enchant.success.multiple": "Applied enchantment %s to %s entities", "commands.enchant.success.single": "Applied enchantment %s to %s's item", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.execute.conditional.fail": "fliba cipra", "commands.execute.conditional.fail_count": "fliba cipra. kancu: %s", "commands.execute.conditional.pass": "cipra mulno", "commands.execute.conditional.pass_count": "cipra mulno. kancu: %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "Gave %s experience levels to %s players", "commands.experience.add.levels.success.single": "Gave %s experience levels to %s", "commands.experience.add.points.success.multiple": "Gave %s experience points to %s players", "commands.experience.add.points.success.single": "Gave %s experience points to %s", "commands.experience.query.levels": "%s has %s experience levels", "commands.experience.query.points": "%s has %s experience points", "commands.experience.set.levels.success.multiple": "Set %s experience levels on %s players", "commands.experience.set.levels.success.single": "Set %s experience levels on %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> set experience points above the maximum points for the player's current level", "commands.experience.set.points.success.multiple": "Set %s experience points on %s players", "commands.experience.set.points.success.single": "Set %s experience points on %s", "commands.fill.failed": "No blocks were filled", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.fillbiome.success": "Biomes set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": ".i mo'u galfi lo mivyvanbi pe lo %s terfendi be ne'i li jo'i %sboi %sboi %sboi bi'i li jo'i %sboi %sboi %sboi", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum %s, specified %s)", "commands.forceload.added.failure": "No chunks were marked for force loading", "commands.forceload.added.multiple": "Marked %s chunks in %s from %s to %s to be force loaded", "commands.forceload.added.none": "No force loaded chunks were found in %s", "commands.forceload.added.single": "Marked chunk %s in %s to be force loaded", "commands.forceload.list.multiple": "%s force loaded chunks were found in %s at: %s", "commands.forceload.list.single": "A force loaded chunk was found in %s at: %s", "commands.forceload.query.failure": "Chunk at %s in %s is not marked for force loading", "commands.forceload.query.success": "Chunk at %s in %s is marked for force loading", "commands.forceload.removed.all": "Unmarked all force loaded chunks in %s", "commands.forceload.removed.failure": "No chunks were removed from force loading", "commands.forceload.removed.multiple": "Unmarked %s chunks in %s from %s to %s for force loading", "commands.forceload.removed.single": "Unmarked chunk %s in %s for force loading", "commands.forceload.toobig": "Too many chunks in the specified area (maximum %s, specified %s)", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "Set %s's game mode to %s", "commands.gamemode.success.self": "Set own game mode to %s", "commands.gamerule.query": "Gamerule %s is currently set to: %s", "commands.gamerule.set": "Gamerule %s is now set to: %s", "commands.give.failed.toomanyitems": "Can't give more than %s of %s", "commands.give.success.multiple": ".i dunda %s %s fi %s lo kelci", "commands.give.success.single": ".i dunda %s %s fi %s", "commands.help.failed": "Unknown command or insufficient permissions", "commands.item.block.set.success": "Replaced a slot at %s, %s, %s with %s", "commands.item.entity.set.success.multiple": "Replaced a slot on %s entities with %s", "commands.item.entity.set.success.single": "Replaced a slot on %s with %s", "commands.item.source.no_such_slot": "The source does not have slot %s", "commands.item.source.not_a_container": "Source position %s, %s, %s is not a container", "commands.item.target.no_changed.known_item": "No targets accepted item %s into slot %s", "commands.item.target.no_changes": "No targets accepted item into slot %s", "commands.item.target.no_such_slot": "The target does not have slot %s", "commands.item.target.not_a_container": "Target position %s, %s, %s is not a container", "commands.jfr.dump.failed": "fliba klagau JFR vreji: %s", "commands.jfr.start.failed": "fliba cfari JFR", "commands.jfr.started": "JFR cfari", "commands.jfr.stopped": "JFR pu sisti .e klagau datni %s", "commands.kick.owner.failed": "Cannot kick server owner in LAN game", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "vimcu %s ", "commands.kill.success.multiple": "Killed %s entities", "commands.kill.success.single": ".i %s cu se catra", "commands.list.nameAndId": "%s (%s)", "commands.list.players": ".i ca cmima fa %s keipre .i sarji su'e %s keipre .i liste lo ca cmima fa zo'oi %s", "commands.locate.biome.not_found": ".i fliba lo nu va z<PERSON>'i le mivyvanbi po'u lo %s", "commands.locate.biome.success": ".i la'oi %2$s judri la'oi %1$s poi rai jibni to darno la'u lo %3$s bliku", "commands.locate.poi.not_found": "Could not find a point of interest of type \"%s\" within reasonable distance", "commands.locate.poi.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.structure.invalid": ".i no dinju cu cmima la'oi %s", "commands.locate.structure.not_found": ".i na va facki fi lo dinju poi cmima la'oi %s", "commands.locate.structure.success": ".i la'oi %2$s judri la'oi %1$s poi rai jibni to darno la'u lo %3$s bliku", "commands.message.display.incoming": "%s mliba'u do: %s", "commands.message.display.outgoing": "do mliba'u %s: %s", "commands.op.failed": "Nothing changed. The player already is an operator", "commands.op.success": "Made %s a server operator", "commands.pardon.failed": "Nothing changed. The player isn't banned", "commands.pardon.success": "Unbanned %s", "commands.pardonip.failed": "Nothing changed. That IP isn't banned", "commands.pardonip.invalid": "Invalid IP address", "commands.pardonip.success": "Unbanned IP %s", "commands.particle.failed": "The particle was not visible for anybody", "commands.particle.success": "Displaying particle %s", "commands.perf.alreadyRunning": "The performance profiler is already started", "commands.perf.notRunning": "The performance profiler hasn't started", "commands.perf.reportFailed": "Failed to create debug report", "commands.perf.reportSaved": "Created debug report in %s", "commands.perf.started": "Started 10 second performance profiling run (use '/perf stop' to stop early)", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "fliba punji tcila", "commands.place.feature.invalid": "There is no feature with type \"%s\"", "commands.place.feature.success": "pu punji \"%s\" stuzi %s, %s, %s", "commands.place.jigsaw.failed": "Failed to generate jigsaw", "commands.place.jigsaw.invalid": "There is no template pool with type \"%s\"", "commands.place.jigsaw.success": "Generated jigsaw at %s, %s, %s", "commands.place.structure.failed": ".i fliba lo nu punji lo dinju", "commands.place.structure.invalid": ".i no dinju cu cmima la'oi %s", "commands.place.structure.success": ".i mo'u punji lo dinju pe la'oi %s lo se judri be li jo'i %sboi %sboi %sboi", "commands.place.template.failed": "fliba punji greku", "commands.place.template.invalid": "There is no template with id \"%s\"", "commands.place.template.success": "Loaded template \"%s\" at %s, %s, %s", "commands.playsound.failed": "The sound is too far away to be heard", "commands.playsound.success.multiple": "Played sound %s to %s players", "commands.playsound.success.single": "Played sound %s to %s", "commands.publish.alreadyPublished": "Multiplayer game is already hosted on port %s", "commands.publish.failed": "Unable to host local game", "commands.publish.started": "Local game hosted on port %s", "commands.publish.success": "Multiplayer game is now hosted on port %s", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "No new recipes were learned", "commands.recipe.give.success.multiple": "Unlocked %s recipe(s) for %s players", "commands.recipe.give.success.single": "Unlocked %s recipe(s) for %s", "commands.recipe.take.failed": "No recipes could be forgotten", "commands.recipe.take.success.multiple": "Took %s recipe(s) from %s players", "commands.recipe.take.success.single": "Took %s recipe(s) from %s", "commands.reload.failure": ".i fliba lo nu rapcpa .i lo datni cu stodi", "commands.reload.success": ".i ko rapli samymo'i", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s stopped riding %s", "commands.ride.mount.failure.cant_ride_players": "Players can't be ridden", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "Can't mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Saving is already turned off", "commands.save.alreadyOn": "Saving is already turned on", "commands.save.disabled": "Automatic saving is now disabled", "commands.save.enabled": "Automatic saving is now enabled", "commands.save.failed": "Unable to save the game (is there enough disk space?)", "commands.save.saving": "Saving the game (this may take a moment!)", "commands.save.success": ".i pu rejgau lo nunkei", "commands.schedule.cleared.failure": ".i no selpla cu se nacme'e zo'oi %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Scheduled tag '%s' in %s tick(s) at gametime %s", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "Can't schedule for current tick", "commands.scoreboard.objectives.add.duplicate": "An objective already exists by that name", "commands.scoreboard.objectives.add.success": "Created new objective %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nothing changed. That display slot is already empty", "commands.scoreboard.objectives.display.alreadySet": "Nothing changed. That display slot is already showing that objective", "commands.scoreboard.objectives.display.cleared": "Cleared any objectives in display slot %s", "commands.scoreboard.objectives.display.set": "Set display slot %s to show objective %s", "commands.scoreboard.objectives.list.empty": "There are no objectives", "commands.scoreboard.objectives.list.success": "There are %s objective(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": ".i galfi lo selja'o cmene be lo laikle be me'e zo'oi %s zo'oi %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": ".i galfi lo jarcytadji be lo laikle be me'e zo'oi %s la'e zo'oi %s", "commands.scoreboard.objectives.remove.success": "Removed objective %s", "commands.scoreboard.players.add.success.multiple": "Added %s to %s for %s entities", "commands.scoreboard.players.add.success.single": "Added %s to %s for %s (now %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "Nothing changed. That trigger is already enabled", "commands.scoreboard.players.enable.invalid": "Enable only works on trigger-objectives", "commands.scoreboard.players.enable.success.multiple": "Enabled trigger %s for %s entities", "commands.scoreboard.players.enable.success.single": "Enabled trigger %s for %s", "commands.scoreboard.players.get.null": "Can't get value of %s for %s; none is set", "commands.scoreboard.players.get.success": "%s has %s %s", "commands.scoreboard.players.list.empty": "There are no tracked entities", "commands.scoreboard.players.list.entity.empty": "%s has no scores to show", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s has %s score(s):", "commands.scoreboard.players.list.success": "There are %s tracked entity/entities: %s", "commands.scoreboard.players.operation.success.multiple": "Updated %s for %s entities", "commands.scoreboard.players.operation.success.single": "Set %s for %s to %s", "commands.scoreboard.players.remove.success.multiple": "Removed %s from %s for %s entities", "commands.scoreboard.players.remove.success.single": "Removed %s from %s for %s (now %s)", "commands.scoreboard.players.reset.all.multiple": "Reset all scores for %s entities", "commands.scoreboard.players.reset.all.single": "Reset all scores for %s", "commands.scoreboard.players.reset.specific.multiple": "Reset %s for %s entities", "commands.scoreboard.players.reset.specific.single": "Reset %s for %s", "commands.scoreboard.players.set.success.multiple": "Set %s for %s entities to %s", "commands.scoreboard.players.set.success.single": "Set %s for %s to %s", "commands.seed.success": "fi'o tsiju %s", "commands.setblock.failed": "Could not set the block", "commands.setblock.success": "Changed the block at %s, %s, %s", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": ".i galfi lo kampu veljbe lo se judri be li %s pi'e %s pi'e %s", "commands.spawnpoint.success.multiple": ".i galfi lo veljbe be %5$s keipre lo se judri be li %1$s pi'e %2$s pi'e %3$s be ne'i la'oi %4$s", "commands.spawnpoint.success.single": ".i galfi lo veljbe be la'oi %5$s lo se judri be li %1$s pi'e %2$s pi'e %3$s be ne'i la'oi %4$s", "commands.spectate.not_spectator": ".i la'oi %s zo'u na ruxtadji", "commands.spectate.self": ".i no da ka'e pru'uxfri tu'a vo'a", "commands.spectate.success.started": ".i co'a pru'uxfri tu'a lo %s", "commands.spectate.success.stopped": ".i co'u pru'uxfri tu'a lo %s", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "Invalid maxHeight %s; expected higher than world minimum %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s entity/entities around %s, %s with an average distance of %s block(s) apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s block(s) apart", "commands.stop.stopping": "Stopping the server", "commands.stopsound.success.source.any": ".i dicra ro lo sance pe la'oi %s", "commands.stopsound.success.source.sound": "Stopped sound '%s' on source '%s'", "commands.stopsound.success.sourceless.any": ".i dicra ro lo sance", "commands.stopsound.success.sourceless.sound": ".i dicra lo sance pe la'oi %s", "commands.summon.failed": "Unable to summon entity", "commands.summon.failed.uuid": ".i fliba lo nu cupra le dzadza kei ki'u lo du'u lo nacme'e ka'e nai cmene re dzadza", "commands.summon.invalidPosition": ".i jai toldra stuzi be lo nu cupra lo dzadza", "commands.summon.success": "Summoned new %s", "commands.tag.add.failed": "Target either already has the tag or has too many tags", "commands.tag.add.success.multiple": "Added tag '%s' to %s entities", "commands.tag.add.success.single": "Added tag '%s' to %s", "commands.tag.list.multiple.empty": "There are no tags on the %s entities", "commands.tag.list.multiple.success": "The %s entities have %s total tags: %s", "commands.tag.list.single.empty": "%s ponse no lo tcita", "commands.tag.list.single.success": "%s has %s tags: %s", "commands.tag.remove.failed": "Target does not have this tag", "commands.tag.remove.success.multiple": "Removed tag '%s' from %s entities", "commands.tag.remove.success.single": "Removed tag '%s' from %s", "commands.team.add.duplicate": "A team already exists by that name", "commands.team.add.success": ".i finti lo girzu me'e %s", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "Nothing changed. That team is already empty", "commands.team.join.success.multiple": "Added %s members to team %s", "commands.team.join.success.single": "Added %s to team %s", "commands.team.leave.success.multiple": "Removed %s members from any team", "commands.team.leave.success.single": "Removed %s from any team", "commands.team.list.members.empty": "There are no members on team %s", "commands.team.list.members.success": "Team %s has %s member(s): %s", "commands.team.list.teams.empty": ".i no da girzu", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "Collision rule for team %s is now \"%s\"", "commands.team.option.collisionRule.unchanged": "Nothing changed. Collision rule is already that value", "commands.team.option.color.success": "Updated the color for team %s to %s", "commands.team.option.color.unchanged": "Nothing changed. That team already has that color", "commands.team.option.deathMessageVisibility.success": "Death message visibility for team %s is now \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nothing changed. Death message visibility is already that value", "commands.team.option.friendlyfire.alreadyDisabled": "Nothing changed. Friendly fire is already disabled for that team", "commands.team.option.friendlyfire.alreadyEnabled": "Nothing changed. Friendly fire is already enabled for that team", "commands.team.option.friendlyfire.disabled": "Disabled friendly fire for team %s", "commands.team.option.friendlyfire.enabled": "Enabled friendly fire for team %s", "commands.team.option.name.success": ".i lo bende be me'e zo'oi %s cu cenba lo ka cmene", "commands.team.option.name.unchanged": "Nothing changed. That team already has that name", "commands.team.option.nametagVisibility.success": "Nametag visibility for team %s is now \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nothing changed. Nametag visibility is already that value", "commands.team.option.prefix.success": "Team prefix set to %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": ".i no da cenba .i lo girzu cu nalviska'e lo nalselvi'a girzu", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": ".i no da cenba .i lo girzu cu viska'e lo nalselvi'a girzu", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s can no longer see invisible teammates", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s can now see invisible teammates", "commands.team.option.suffix.success": "Team suffix set to %s", "commands.team.remove.success": "Removed team %s", "commands.teammsg.failed.noteam": "You must be on a team to message your team", "commands.teleport.invalidPosition": ".i zo'e na ka'e se sukykla", "commands.teleport.success.entity.multiple": "Teleported %s entities to %s", "commands.teleport.success.entity.single": "Teleported %s to %s", "commands.teleport.success.location.multiple": "Teleported %s entities to %s, %s, %s", "commands.teleport.success.location.single": "Teleported %s to %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiles: P50: %sms P95: %sms P99: %sms, sample: %s", "commands.tick.query.rate.running": "Target tick rate: %s per second.\nAverage time per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick rate: %s per second (ignored, reference only).\nAverage time per tick: %sms", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": ".i ca'o sazri le nunkei .i ku'i na kakne lo nu tu'a lo terzu'e sletei cu ranji", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": ".i ti tcika %s", "commands.time.set": "Set the time to %s", "commands.title.cleared.multiple": "Cleared titles for %s players", "commands.title.cleared.single": "Cleared titles for %s", "commands.title.reset.multiple": "Reset title options for %s players", "commands.title.reset.single": "Reset title options for %s", "commands.title.show.actionbar.multiple": "Showing new actionbar title for %s players", "commands.title.show.actionbar.single": "Showing new actionbar title for %s", "commands.title.show.subtitle.multiple": "Showing new subtitle for %s players", "commands.title.show.subtitle.single": "Showing new subtitle for %s", "commands.title.show.title.multiple": "Showing new title for %s players", "commands.title.show.title.single": "Showing new title for %s", "commands.title.times.multiple": "Changed title display times for %s players", "commands.title.times.single": "Changed title display times for %s", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "Triggered %s (added %s to value)", "commands.trigger.failed.invalid": "You can only trigger objectives that are 'trigger' type", "commands.trigger.failed.unprimed": "You cannot trigger this objective yet", "commands.trigger.set.success": "Triggered %s (set value to %s)", "commands.trigger.simple.success": "Triggered %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": ".i gafygau lo cabyti'a lo solti'a", "commands.weather.set.rain": ".i gafygau lo cabyti'a lo cavyti'a", "commands.weather.set.thunder": ".i gafygau lo cabyti'a lo cavjoilidyti'a", "commands.whitelist.add.failed": ".i lo kelci cu ba'o sel<PERSON>rugau", "commands.whitelist.add.success": "Added %s to the whitelist", "commands.whitelist.alreadyOff": "Whitelist is already turned off", "commands.whitelist.alreadyOn": "Whitelist is already turned on", "commands.whitelist.disabled": "Whitelist is now turned off", "commands.whitelist.enabled": "Whitelist is now turned on", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "There are no whitelisted players", "commands.whitelist.reloaded": "Reloaded the whitelist", "commands.whitelist.remove.failed": ".i lo kelci cu na<PERSON>elcrugau", "commands.whitelist.remove.success": "Removed %s from the whitelist", "commands.worldborder.center.failed": "Nothing changed. The world border is already centered there", "commands.worldborder.center.success": "Set the center of the world border to %s, %s", "commands.worldborder.damage.amount.failed": "Nothing changed. The world border damage is already that amount", "commands.worldborder.damage.amount.success": ".i lo mujykoi co'a xrani se la'u lo ertso be li %s", "commands.worldborder.damage.buffer.failed": "Nothing changed. The world border damage buffer is already that distance", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to %s block(s)", "commands.worldborder.get": "The world border is currently %s block(s) wide", "commands.worldborder.set.failed.big": "World border cannot be bigger than %s blocks wide", "commands.worldborder.set.failed.far": "World border cannot be further out than %s blocks", "commands.worldborder.set.failed.nochange": "Nothing changed. The world border is already that size", "commands.worldborder.set.failed.small": "World border cannot be smaller than 1 block wide", "commands.worldborder.set.grow": "Growing the world border to %s blocks wide over %s seconds", "commands.worldborder.set.immediate": "Set the world border to %s block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "Nothing changed. The world border warning is already that distance", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "Nothing changed. The world border warning is already that amount of time", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": ".ca do kelci za'u re vo cacra", "compliance.playtime.hours": "ca do kelci %s cacra", "compliance.playtime.message": "Excessive gaming may interfere with normal daily life", "connect.aborted": "pu co'urde'a", "connect.authorizing": ".i japkla", "connect.connecting": ".i jongau do le samci'ejudri", "connect.encrypting": "ca mifyfanva", "connect.failed": ".i fliba lo nu jongau fi le samci'ejudri", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "ba zi jorne lo munje", "connect.negotiating": "ca snucpa", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "Reconfiguring...", "connect.transferring": "Transferring to new server...", "container.barrel": "mudbaktu", "container.beacon": "barda tergu'i", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "b<PERSON><PERSON><PERSON>", "container.brewing": "sel<PERSON><PERSON> jukpa jubme", "container.cartography_table": "cartu jubme", "container.chest": "vasru", "container.chestDouble": "barda vasru", "container.crafter": "zbasu", "container.crafting": "zbasu", "container.creative": "selcu'e", "container.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "container.dropper": "falcru", "container.enchant": "makfa zenba", "container.enchant.clue": "%s li'o", "container.enchant.lapis.many": "%s lo blaro'i", "container.enchant.lapis.one": "pa lo blaro'i", "container.enchant.level.many": "%s mafyze'a momven<PERSON>'e", "container.enchant.level.one": "1 Enchantment Level", "container.enchant.level.requirement": ".i nitcu lo maflai be li %s", "container.enderchest": ".endre vasru", "container.furnace": "<PERSON><PERSON><PERSON> to<PERSON>", "container.grindstone_title": "cikre je mafvi'u", "container.hopper": "<PERSON><PERSON>", "container.inventory": "sorcu", "container.isLocked": "%s cu telga'o", "container.lectern": "nunctuta'a jubme", "container.loom": "bukyzbami'i", "container.repair": "cikre je te cmene", "container.repair.cost": "mafyze'a jdima %1$s", "container.repair.expensive": "kargudu'e", "container.shulkerBox": "me la .cylkr. tanxe", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "and %s more...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Smoker", "container.spectatorCantOpen": "Unable to open. Loot not generated yet.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "xagzengau", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "Add <PERSON> Template", "controls.keybinds": "cuxna lo batkyuidje...", "controls.keybinds.duplicateKeybinds": "This key is also used for:\n%s", "controls.keybinds.title": "cuxna lo batkyuidje", "controls.reset": "kraga'igau", "controls.resetAll": "kraga'igau lo batke", "controls.title": "batke mapti", "createWorld.customize.buffet.biome": "ko cuxna pa lo rarvanbi", "createWorld.customize.buffet.title": "nuncenba la sezytamgau munje", "createWorld.customize.flat.height": "cisni", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "dziraipau - %s", "createWorld.customize.flat.layer.top": "lo galraipau - %s", "createWorld.customize.flat.removeLayer": "senta vimcu", "createWorld.customize.flat.tile": "senta marji", "createWorld.customize.flat.title": "nun<PERSON><PERSON><PERSON> balpinta", "createWorld.customize.presets": "selpla", "createWorld.customize.presets.list": ".i ta'o mi'a ba'o finti su'o drata", "createWorld.customize.presets.select": "pilno lo pu tcimi'e", "createWorld.customize.presets.share": ".i fau lo nu do djica lo nu fatrygau lo me do moi da kei ko pilno lo ni'a tanxe", "createWorld.customize.presets.title": "cuxna lo selpla", "createWorld.preparing": ".i bregau lo nu mujyzba", "createWorld.tab.game.title": "Game", "createWorld.tab.more.title": "More", "createWorld.tab.world.title": "World", "credits_and_attribution.button.attribution": "Attribution", "credits_and_attribution.button.credits": "Credits", "credits_and_attribution.button.licenses": "Licenses", "credits_and_attribution.screen.title": "Credits and Attribution", "dataPack.bundle.description": "Enables experimental Bundle item", "dataPack.bundle.name": "Bundles", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "cuxna lo datnygunma", "dataPack.trade_rebalance.description": "Updated trades for Villagers", "dataPack.trade_rebalance.name": "Villager Trade Rebalance", "dataPack.update_1_20.description": "New features and content for Minecraft 1.20", "dataPack.update_1_20.name": "Update 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "Update 1.21", "dataPack.validation.back": "dicra", "dataPack.validation.failed": ".i fliba lo nu cipcta lo datni", "dataPack.validation.reset": "xruti fi lo zmiku", "dataPack.validation.working": ".i cipcta lo selcu'a datnygunma", "dataPack.vanilla.description": "The default data for Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "snura samy<PERSON>'i", "datapackFailure.safeMode.failed.description": "This world contains invalid or corrupted save data.", "datapackFailure.safeMode.failed.title": "Failed to load world in Safe Mode.", "datapackFailure.title": "Errors in currently selected data packs prevented the world from loading.\nYou can either try to load it with only the vanilla data pack (\"safe mode\"), or go back to the title screen and fix it manually.", "death.attack.anvil": "%1$s was squashed by a falling anvil", "death.attack.anvil.player": "%1$s was squashed by a falling anvil while fighting %2$s", "death.attack.arrow": "la'o zy. %1$s .zy. te renro lo celga'a la'o zy. %2$s .zy.", "death.attack.arrow.item": "la'oi %1$s cu se celcatra la'oi %2$s se pi'o %3$s", "death.attack.badRespawnPoint.link": "Intentional Game Design", "death.attack.badRespawnPoint.message": ".i la'oi %1$s cu selcatra la'oi %2$s", "death.attack.cactus": "la'o zy. %1$s .zy. se catra lo jesyspa", "death.attack.cactus.player": "%1$s walked into a cactus while trying to escape %2$s", "death.attack.cramming": "%1$s was squished too much", "death.attack.cramming.player": ".i la'oi %1$s cu marxa selcatra jai la'oi %2$s", "death.attack.dragonBreath": "%1$s was roasted in dragon's breath", "death.attack.dragonBreath.player": "%1$s was roasted in dragon's breath by %2$s", "death.attack.drown": "la'oi %1$s jaurvasmro", "death.attack.drown.player": "%1$s drowned while trying to escape %2$s", "death.attack.dryout": "%1$s died from dehydration", "death.attack.dryout.player": "%1$s died from dehydration while trying to escape %2$s", "death.attack.even_more_magic": "%1$s was killed by even more magic", "death.attack.explosion": "la'o zy. %1$s .zy. spoja", "death.attack.explosion.player": "%1$s was blown up by %2$s", "death.attack.explosion.player.item": ".i la'oi %1$s bamselcatra ki'u jai la'oi %2$s be sepi'o la'oi %3$s", "death.attack.fall": "la'o zy. %1$s .zy. farlu fi lo dukse lo ka galtu", "death.attack.fall.player": "%1$s hit the ground too hard while trying to escape %2$s", "death.attack.fallingBlock": "la'oi %1$s se demri'a tu'a lo farlu bliku", "death.attack.fallingBlock.player": "%1$s was squashed by a falling block while fighting %2$s", "death.attack.fallingStalactite": "%1$s was skewered by a falling stalactite", "death.attack.fallingStalactite.player": "%1$s was skewered by a falling stalactite while fighting %2$s", "death.attack.fireball": ".i la'o zy. %1$s .zy. te fagygau la'o zy. %2$s .zy.", "death.attack.fireball.item": "la'oi %1$s cu fagboi se catra la'oi %2$s se pi'o %3$s", "death.attack.fireworks": "%1$s went off with a bang", "death.attack.fireworks.item": ".i la'oi %1$s cladu spoja ca'o lo nu damba la'oi %2$s noi pilno la'oi %3$s", "death.attack.fireworks.player": "%1$s went off with a bang while fighting %2$s", "death.attack.flyIntoWall": "la'oi %1$s lifri tu'a lo muvdu nejni", "death.attack.flyIntoWall.player": "%1$s experienced kinetic energy while trying to escape %2$s", "death.attack.freeze": "%1$s froze to death", "death.attack.freeze.player": "%1$s was frozen to death by %2$s", "death.attack.generic": "la'o zy. %1$s .zy. mrobi'o", "death.attack.generic.player": ".i %1$s cu morsi ki'u %2$s", "death.attack.genericKill": "%1$s was killed", "death.attack.genericKill.player": "%1$s was killed while fighting %2$s", "death.attack.hotFloor": "%1$s discovered the floor was lava", "death.attack.hotFloor.player": "%1$s walked into the danger zone due to %2$s", "death.attack.inFire": "la'oi %1$s se fagri", "death.attack.inFire.player": "la'oi %1$s dzukla lo fagri ca'o nu damba la'oi %2$s", "death.attack.inWall": "la'oi %1$s dirva'u lo bitmu", "death.attack.inWall.player": ".i la'oi %1$s dirva'u selcatra vi lo bitmu ca damba la'oi %2$s", "death.attack.indirectMagic": "la'o zy. %1$s .zy. makfa se catra la'o zy. %2$s .zy.", "death.attack.indirectMagic.item": "la'oi %1$s cu se catra la'oi %2$s se pi'o %3$s", "death.attack.lava": "la'oi %1$s troci lo nu limna lo rumro'i", "death.attack.lava.player": "%1$s tried to swim in lava to escape %2$s", "death.attack.lightningBolt": "la'oi %1$s te lindi", "death.attack.lightningBolt.player": "%1$s was struck by lightning while fighting %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "la'o zy. %1$s .zy. se catra fi lo makfa", "death.attack.magic.player": ".i la'oi %1$s morsi ri'a lo makfa ca'o lo nu damba la'oi %2$s", "death.attack.message_too_long": "Actually, the message was too long to deliver fully. Sorry! Here's a stripped version: %s", "death.attack.mob": "la'o zy. %1$s .zy. se catra la'o zy. %2$s .zy.", "death.attack.mob.item": "la'oi %1$s cu se catra la'oi %2$s se pi'o %3$s", "death.attack.onFire": "la'oi %1$s fagri mrobi'o", "death.attack.onFire.item": "%1$s was burned to a crisp while fighting %2$s wielding %3$s", "death.attack.onFire.player": "%1$s was burned to a crisp while fighting %2$s", "death.attack.outOfWorld": "la'o zy. %1$s .zy. farlu fi le munje", "death.attack.outOfWorld.player": "%1$s didn't want to live in the same world as %2$s", "death.attack.outsideBorder": "%1$s left the confines of this world", "death.attack.outsideBorder.player": "%1$s left the confines of this world while fighting %2$s", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s using %3$s", "death.attack.sonic_boom": "%1$s was obliterated by a sonically-charged shriek", "death.attack.sonic_boom.item": "%1$s was obliterated by a sonically-charged shriek while trying to escape %2$s wielding %3$s", "death.attack.sonic_boom.player": "%1$s was obliterated by a sonically-charged shriek while trying to escape %2$s", "death.attack.stalagmite": "%1$s was impaled on a stalagmite", "death.attack.stalagmite.player": "%1$s was impaled on a stalagmite while fighting %2$s", "death.attack.starve": "la'oi %1$s djacaumro", "death.attack.starve.player": ".i la'oi %1$s cu xagjymro ca damba la'oi %2$s", "death.attack.sting": ".i la'oi %1$s se bictunta", "death.attack.sting.item": "%1$s was stung to death by %2$s using %3$s", "death.attack.sting.player": ".i la'oi %1$s se bictunta gau la'oi %2$s", "death.attack.sweetBerryBush": ".i la'oi %1$s cu mrobi'o lonu tunta fa lo titla jbari dzitricu", "death.attack.sweetBerryBush.player": "la %1$s cu mrobi'o lonu tunta lo titla jbari dzitricu ca lonu troci co rivli'a la %2$s", "death.attack.thorns": "la'oi %1$s cu se catra gi'e ca bo troci lo ka crogau la'oi %2$s", "death.attack.thorns.item": ".i la'oi %1$s cu selcatra la'oi %3$s ca tocygunta la'oi %2$s", "death.attack.thrown": "la'o zy. %1$s .zy. se darxi la'o zy. %2$s .zy.", "death.attack.thrown.item": "%1$s was pummeled by %2$s using %3$s", "death.attack.trident": ".i %1$s cu mrobi'o se gregunta %2$s", "death.attack.trident.item": "%1$s was impaled by %2$s with %3$s", "death.attack.wither": "%1$s withered away", "death.attack.wither.player": "%1$s withered away while fighting %2$s", "death.attack.witherSkull": ".i la'oi %1$s mo'u mivjdika ri'a tu'a lo sedbo'u be la'oi %2$s", "death.attack.witherSkull.item": "%1$s was shot by a skull from %2$s using %3$s", "death.fell.accident.generic": "la'oi %1$s farlu fi lo galtu", "death.fell.accident.ladder": "la'oi %1$s ba'o farlu fi lo rajyserti", "death.fell.accident.other_climbable": ".i la'oi %1$s farlu ja'e lo nu cpare", "death.fell.accident.scaffolding": ".i la'oi %1$s farlu fi lo rajysarji", "death.fell.accident.twisting_vines": ".i la'oi %1$s farlu fi lo sarlu li'ante", "death.fell.accident.vines": "%1$s fell off some vines", "death.fell.accident.weeping_vines": ".i la'oi %1$s farlu fi lo klaku li'ante", "death.fell.assist": "%1$s was doomed to fall by %2$s", "death.fell.assist.item": "%1$s was doomed to fall by %2$s using %3$s", "death.fell.finish": "%1$s fell too far and was finished by %2$s", "death.fell.finish.item": "%1$s fell too far and was finished by %2$s using %3$s", "death.fell.killer": "%1$s was doomed to fall", "deathScreen.quit.confirm": ".i xu do barli'a ju'o dai", "deathScreen.respawn": "jmive binxo", "deathScreen.score": ".i ci'erkeilai li", "deathScreen.score.value": "Score: %s", "deathScreen.spectate": "pru'u<PERSON><PERSON><PERSON>", "deathScreen.title": ".i do morsi", "deathScreen.title.hardcore": ".i ba'o kelci", "deathScreen.titleScreen": "Title Screen", "debug.advanced_tooltips.help": "F3 + H = Advanced tooltips", "debug.advanced_tooltips.off": "Advanced tooltips: hidden", "debug.advanced_tooltips.on": "Advanced tooltips: shown", "debug.chunk_boundaries.help": "F3 + G = Show chunk boundaries", "debug.chunk_boundaries.off": "Chunk borders: hidden", "debug.chunk_boundaries.on": "Chunk borders: shown", "debug.clear_chat.help": "F3 + D = Clear chat", "debug.copy_location.help": "F3 + C = Copy location as /tp command, hold F3 + C to crash the game", "debug.copy_location.message": "Copied location to clipboard", "debug.crash.message": "F3 + C is held down. This will crash the game unless released.", "debug.crash.warning": "Crashing in %s...", "debug.creative_spectator.error": "Unable to switch game mode; no permission", "debug.creative_spectator.help": "F3 + N = Cycle previous game mode <-> spectator", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": ".i na curmi lo nu kargau lo keltadji uidje", "debug.gamemodes.help": ".i te'a'a F xi 3 boi jo'u F xi 4 zo'u kargau lo keltadji uidje", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s (to bavla'i toi)", "debug.help.help": "F3 + Q = Show this list", "debug.help.message": "Key bindings:", "debug.inspect.client.block": "Copied client-side block data to clipboard", "debug.inspect.client.entity": "Copied client-side entity data to clipboard", "debug.inspect.help": "F3 + I = Copy entity or block data to clipboard", "debug.inspect.server.block": "Copied server-side block data to clipboard", "debug.inspect.server.entity": "Copied server-side entity data to clipboard", "debug.pause.help": "F3 + Esc = Pause without pause menu (if pausing is possible)", "debug.pause_focus.help": "F3 + P = Pause on lost focus", "debug.pause_focus.off": "Pause on lost focus: disabled", "debug.pause_focus.on": "Pause on lost focus: enabled", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Start/stop profiling", "debug.profiling.start": "Profiling started for %s seconds. Use F3 + L to stop early", "debug.profiling.stop": "Profiling ended. Saved results to %s", "debug.reload_chunks.help": "F3 + A = Reload chunks", "debug.reload_chunks.message": "Reloading all chunks", "debug.reload_resourcepacks.help": "F3 + T = Reload resource packs", "debug.reload_resourcepacks.message": "Reloaded resource packs", "debug.show_hitboxes.help": "F3 + B = Show hitboxes", "debug.show_hitboxes.off": "Hitboxes: hidden", "debug.show_hitboxes.on": "Hitboxes: shown", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": ".i le cipra ba djedi li mu .i di'ai", "demo.day.2": ".i remoi djedi", "demo.day.3": ".i cimoi djedi", "demo.day.4": ".i vomoi djedi", "demo.day.5": ".i romoi djedi vau .o'i", "demo.day.6": ".i lo do djedi be li mu cu mulno .i .e'u do rejgau lo vidnyxra be lo se finti be do te'a'a %s", "demo.day.warning": ".i le nunkei be do ku pu'o sisti", "demo.demoExpired": "co'u cipra kelci", "demo.help.buy": "ko ca te vecnu", "demo.help.fullWrapped": ".i le cipra ba selkei djedi li mu (to ji'i zirli mentu li 100 toi) .i ko catlu lo ve farvi ja'e lo nu se anci .i zdile ko", "demo.help.inventory": ".i ko zgana le do sorcu seta'i lo nu iklki zo'oi %1$s", "demo.help.jump": ".i ko plipe seta'i lo nu iklki zo'oi %1$s", "demo.help.later": "di'a kelci", "demo.help.movement": ".i ko pilno zo'oi %1$s .e zo'oi %2$s .e zo'oi %3$s .e zo'oi %4$s .e le samxa'e seta'i lo nu muvdu", "demo.help.movementMouse": "do se farna se pi'o le samxa'e", "demo.help.movementShort": "jai muvgau do fa %s .e %s .e %s .e %s", "demo.help.title": "cipra kelci", "demo.remainingTime": "lo ve vimcu cu mentu li %s", "demo.reminder": ".i lo do temci cu mulno .i .e'u do ga te vecnu le selkei je cu di'a kelci gi z<PERSON>au lo cnino munje", "difficulty.lock.question": "Are you sure you want to lock the difficulty of this world? This will set this world to always be %1$s, and you will never be able to change that again.", "difficulty.lock.title": "Lock World Difficulty", "disconnect.endOfStream": ".i fanmo lo pu'u jorne .i sisti lo nu te benji", "disconnect.exceeded_packet_rate": ".i se livgau ki'u lo nu du'enzu lo ka sutra pilno lo datnybakfu", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.loginFailedInfo": "na japkla zo'oi %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Multiplayer is disabled. Please check your Microsoft account settings.", "disconnect.loginFailedInfo.invalidSession": "Invalid session (Try restarting your game and the launcher)", "disconnect.loginFailedInfo.serversUnavailable": "The authentication servers are currently not reachable. Please try again.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": " .i .uu lo za'i jorne kei ku jitfa binxo vau .u'u", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "pu vimcu do ki'u lo nu do kibmalmri", "disconnect.timeout": ".i temci fliba", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "Unknown host", "download.pack.failed": ".i fliba lo nu kibycpa %s lo %s ganvygunma", "download.pack.progress.bytes": "lo ni mulno cu klani li %s to lo mulno nilbra na se djuno", "download.pack.progress.percent": "lo ni mulno cu klani li %s ce'i", "download.pack.title": ".i ca'o kibycpa lo xragri to %s fi'u %s", "editGamerule.default": ".i fadni fa zo'oi %s", "editGamerule.title": "tikygau lo javni", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "na lifri", "effect.minecraft.bad_omen": "x<PERSON>i nuzba", "effect.minecraft.blindness": "nalviska'e", "effect.minecraft.conduit_power": "xasycei kamvli", "effect.minecraft.darkness": "ka manku", "effect.minecraft.dolphins_grace": "delfinu zandimna", "effect.minecraft.fire_resistance": "fagri bandu", "effect.minecraft.glowing": "mi<PERSON>i", "effect.minecraft.haste": "toltatpi", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "sosyzda vudvri", "effect.minecraft.hunger": "xagji", "effect.minecraft.infested": "Infested", "effect.minecraft.instant_damage": "cortu", "effect.minecraft.instant_health": "nalcro", "effect.minecraft.invisibility": "canci", "effect.minecraft.jump_boost": "<PERSON><PERSON> plipe", "effect.minecraft.levitation": "vofli", "effect.minecraft.luck": "se zanfu'a", "effect.minecraft.mining_fatigue": "kakpa tatpi", "effect.minecraft.nausea": "vindu lifri", "effect.minecraft.night_vision": "manku viska", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "vindu cortu", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.resistance": "bandu", "effect.minecraft.saturation": "carmi", "effect.minecraft.slow_falling": "snofa'u", "effect.minecraft.slowness": "masno", "effect.minecraft.speed": "sutli'u", "effect.minecraft.strength": "tsali", "effect.minecraft.trial_omen": "Trial Omen", "effect.minecraft.unluck": "se malfu'a", "effect.minecraft.water_breathing": "fin<PERSON>", "effect.minecraft.weakness": "ruble", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Charged", "effect.minecraft.wither": "tolba'o", "effect.none": "no da galfi", "enchantment.level.1": "se pamoi", "enchantment.level.10": "se daumoi", "enchantment.level.2": "reva'e", "enchantment.level.3": "civa'e", "enchantment.level.4": "vova'e", "enchantment.level.5": "muva'e", "enchantment.level.6": "xava'e", "enchantment.level.7": "se zemoi", "enchantment.level.8": "se bimoi", "enchantment.level.9": "se somoi", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "jukni <PERSON>", "enchantment.minecraft.binding_curse": "na<PERSON><PERSON><PERSON><PERSON> da<PERSON>a", "enchantment.minecraft.blast_protection": "spoja bandu", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "lindi kla<PERSON>", "enchantment.minecraft.density": "Density", "enchantment.minecraft.depth_strider": "sutra", "enchantment.minecraft.efficiency": "sutygu'a", "enchantment.minecraft.feather_falling": "farlu jabre", "enchantment.minecraft.fire_aspect": "fagri", "enchantment.minecraft.fire_protection": "fagri bandu", "enchantment.minecraft.flame": "fagri", "enchantment.minecraft.fortune": "zabna", "enchantment.minecraft.frost_walker": "bislunsa cadzu", "enchantment.minecraft.impaling": "gre<PERSON><PERSON>", "enchantment.minecraft.infinity": "cimni", "enchantment.minecraft.knockback": "catke", "enchantment.minecraft.looting": "crepu", "enchantment.minecraft.loyalty": "nu kraice", "enchantment.minecraft.luck_of_the_sea": "xamsi funca", "enchantment.minecraft.lure": "trisku", "enchantment.minecraft.mending": "lo nu cikre", "enchantment.minecraft.multishot": "za'um<PERSON> cecla", "enchantment.minecraft.piercing": "greke'a selri'a", "enchantment.minecraft.power": "tsali", "enchantment.minecraft.projectile_protection": "danti bandu", "enchantment.minecraft.protection": "bandu", "enchantment.minecraft.punch": "darxi", "enchantment.minecraft.quick_charge": "sutra ke skogarce'a bredi", "enchantment.minecraft.respiration": "vasxu", "enchantment.minecraft.riptide": "sutfle", "enchantment.minecraft.sharpness": "kinli", "enchantment.minecraft.silk_touch": "milxe pencu", "enchantment.minecraft.smite": "censa darxi", "enchantment.minecraft.soul_speed": "ruxse'i sutli'u", "enchantment.minecraft.sweeping": "brulu'i korbi", "enchantment.minecraft.sweeping_edge": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "sutra mipydzu", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "j<PERSON>i", "enchantment.minecraft.vanishing_curse": "nuncanci da<PERSON>a", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "blanu je vofli sidju", "entity.minecraft.area_effect_cloud": "kuspe ke cnetcini dilnu", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "badycakyzbe", "entity.minecraft.arrow": "celga'a", "entity.minecraft.axolotl": "ax<PERSON>e", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "bifce", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "fagda'u", "entity.minecraft.block_display": "bliku visygau", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "bify<PERSON>'u", "entity.minecraft.breeze_wind_charge": "brife bakfu", "entity.minecraft.camel": "rabykumte", "entity.minecraft.cat": "mlatu", "entity.minecraft.cave_spider": "kevju<PERSON>ni", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "ke baktu trene ke'e joi vasru", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "ke baktu trene ke'e joi ke ziltinbe bliku", "entity.minecraft.cow": "bakni", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "la .kriper.", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "delfinu", "entity.minecraft.donkey": "xasli", "entity.minecraft.dragon_fireball": "drak<PERSON> fagy<PERSON>i", "entity.minecraft.drowned": "j<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "selre'o sovda", "entity.minecraft.elder_guardian": "dzena bad<PERSON><PERSON>re", "entity.minecraft.end_crystal": "me la .end. krili", "entity.minecraft.ender_dragon": ".endre drakono", "entity.minecraft.ender_pearl": "sel<PERSON>'<PERSON> .endre boijme", "entity.minecraft.enderman": ".endre prenu", "entity.minecraft.endermite": ".endre miptera", "entity.minecraft.evoker": "pacykemklacpepre", "entity.minecraft.evoker_fangs": "packemklacpepre kilde'i", "entity.minecraft.experience_bottle": "selre'o ke makfa botpi", "entity.minecraft.experience_orb": "kelrau spisa", "entity.minecraft.eye_of_ender": "me la .endr. kanla", "entity.minecraft.falling_block": "farlu canre bliku", "entity.minecraft.falling_block_type": "farlu ke %s", "entity.minecraft.fireball": "bolci fagri", "entity.minecraft.firework_rocket": "mlefagri jakne", "entity.minecraft.fishing_bobber": "fi<PERSON><PERSON><PERSON> fulta", "entity.minecraft.fox": "lorxu", "entity.minecraft.frog": "p<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.furnace_minecart": "ke baktu trene ke'e joi ke zbasu toknu", "entity.minecraft.ghast": "me la gast.", "entity.minecraft.giant": "barda", "entity.minecraft.glow_item_frame": "carmi ke ja'ordai greku", "entity.minecraft.glow_squid": "<PERSON><PERSON> kalmari", "entity.minecraft.goat": "kanba", "entity.minecraft.guardian": "badypre", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "ke baktu trene ke'e joi se'arko'u", "entity.minecraft.horse": "xirma", "entity.minecraft.husk": "sudga <PERSON>", "entity.minecraft.illusioner": "gasticgau", "entity.minecraft.interaction": "<PERSON>tro ganse", "entity.minecraft.iron_golem": "tirse re<PERSON><PERSON><PERSON>'i", "entity.minecraft.item": "dacti", "entity.minecraft.item_display": "dacti visygau", "entity.minecraft.item_frame": "ja'or<PERSON> greku", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "lo mrori'a ractu", "entity.minecraft.leash_knot": "velxlu jgena", "entity.minecraft.lightning_bolt": "lindi", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "tcokmute", "entity.minecraft.llama_spit": "tcokumte selpu'u", "entity.minecraft.magma_cube": "lik<PERSON><PERSON>i bliku", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "barna", "entity.minecraft.minecart": "baktu trene", "entity.minecraft.mooshroom": "b<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mule": "xaslyxi'a", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "cicmlatu", "entity.minecraft.ominous_item_spawner": "Ominous <PERSON>em <PERSON>wner", "entity.minecraft.painting": "pixra", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "latcribe", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "ri'orkalvoi", "entity.minecraft.pig": "xarju", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "cilce xajypre", "entity.minecraft.pillager": "vilcpapre", "entity.minecraft.player": "kelci", "entity.minecraft.polar_bear": "bercribe", "entity.minecraft.potion": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "ractu", "entity.minecraft.ravager": "nunspokezgau", "entity.minecraft.salmon": "salmone", "entity.minecraft.sheep": "lanme", "entity.minecraft.shulker": "la cylkr.", "entity.minecraft.shulker_bullet": "danti pe la cylkr.", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "b<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "bog<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "<PERSON><PERSON><PERSON>'u", "entity.minecraft.small_fireball": "cmalu fagri bo bolci", "entity.minecraft.sniffer": "zbisak<PERSON>", "entity.minecraft.snow_golem": "snime remsmi<PERSON>'i", "entity.minecraft.snowball": "snime bolci", "entity.minecraft.spawner_minecart": "baktu trene joi cupra be lo cizda'u", "entity.minecraft.spectral_arrow": "gusni celga'a", "entity.minecraft.spider": "jukni", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "kalmari", "entity.minecraft.stray": "muvzva", "entity.minecraft.strider": "lo fagdzu", "entity.minecraft.tadpole": "verbanfi", "entity.minecraft.text_display": "selci'a visygau", "entity.minecraft.tnt": "seljelgau jbama", "entity.minecraft.tnt_minecart": "ke baktu trene ke'e joi j<PERSON>ma", "entity.minecraft.trader_llama": "tcokumte po'a lo ve canja", "entity.minecraft.trident": "cibyterfroxa'i", "entity.minecraft.tropical_fish": "tropika finpe", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.10": "Moorish Idol", "entity.minecraft.tropical_fish.predefined.11": "Ornate Butterflyfish", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> finpe", "entity.minecraft.tropical_fish.predefined.13": "Queen Angelfish", "entity.minecraft.tropical_fish.predefined.14": "Red Cichlid", "entity.minecraft.tropical_fish.predefined.15": "Red Lipped Blenny", "entity.minecraft.tropical_fish.predefined.16": "Red Snapper", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "Tomato Clownfish", "entity.minecraft.tropical_fish.predefined.19": "Triggerfish", "entity.minecraft.tropical_fish.predefined.2": "blanu finpe", "entity.minecraft.tropical_fish.predefined.20": "Yellowtail Parrotfish", "entity.minecraft.tropical_fish.predefined.21": "pelxu finpe", "entity.minecraft.tropical_fish.predefined.3": "Butterflyfish", "entity.minecraft.tropical_fish.predefined.4": "Cichlid", "entity.minecraft.tropical_fish.predefined.5": "fin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Cotton Candy <PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Emperor <PERSON>", "entity.minecraft.tropical_fish.predefined.9": "lantai finpe", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blockfish", "entity.minecraft.tropical_fish.type.brinely": "siln<PERSON><PERSON><PERSON>i", "entity.minecraft.tropical_fish.type.clayfish": "kliti fi<PERSON>i", "entity.minecraft.tropical_fish.type.dasher": "sutpi'e fiplei", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitter", "entity.minecraft.tropical_fish.type.kob": "rok<PERSON><PERSON> fi<PERSON>i", "entity.minecraft.tropical_fish.type.snooper": "<PERSON>znal<PERSON>'o <PERSON><PERSON>i", "entity.minecraft.tropical_fish.type.spotty": "selsor<PERSON>'a fiplei", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "solfi'e", "entity.minecraft.turtle": "cakyrespa", "entity.minecraft.vex": "cmapal<PERSON>", "entity.minecraft.villager": "sels<PERSON>zda", "entity.minecraft.villager.armorer": "dabycalku zbapre", "entity.minecraft.villager.butcher": "bakcatra", "entity.minecraft.villager.cartographer": "catyzba", "entity.minecraft.villager.cleric": "jdaca'i", "entity.minecraft.villager.farmer": "cagypre", "entity.minecraft.villager.fisherman": "fipkalte", "entity.minecraft.villager.fletcher": "celga'a zbapre", "entity.minecraft.villager.leatherworker": "skapi z<PERSON>re", "entity.minecraft.villager.librarian": "ckuzdacre", "entity.minecraft.villager.mason": "r<PERSON><PERSON> z<PERSON>u prenu", "entity.minecraft.villager.nitwit": "tolmencre", "entity.minecraft.villager.none": "sels<PERSON>zda", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "tutci z<PERSON>u prenu", "entity.minecraft.villager.weaponsmith": "xarci z<PERSON>u prenu", "entity.minecraft.vindicator": "pacy<PERSON>u", "entity.minecraft.wandering_trader": "muvzva ve canja", "entity.minecraft.warden": "le visnalka'e cizda'u", "entity.minecraft.wind_charge": "brife danti", "entity.minecraft.witch": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "entity.minecraft.wither": "la me uiter.", "entity.minecraft.wither_skeleton": "bogygreku me la uiter.", "entity.minecraft.wither_skull": "stedu pe la witer.", "entity.minecraft.wolf": "labno", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "miv<PERSON>ro x<PERSON>", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "nuns<PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "nunfli", "event.minecraft.raid.defeat.full": "Raid - Defeat", "event.minecraft.raid.raiders_remaining": ".i zilkancu lo za'o gunta li %s", "event.minecraft.raid.victory": "nunji'a", "event.minecraft.raid.victory.full": "Raid - Victory", "filled_map.buried_treasure": "Buried Treasure Map", "filled_map.explorer_jungle": "Jungle Explorer Map", "filled_map.explorer_swamp": "Swamp Explorer Map", "filled_map.id": "%smoi", "filled_map.level": "(Level %s/%s)", "filled_map.locked": "Locked", "filled_map.mansion": "fakli'u ricfoi cartu", "filled_map.monument": "fakli'u braxamsi cartu", "filled_map.scale": "Scaling at 1:%s", "filled_map.trial_chambers": "Trial Explorer Map", "filled_map.unknown": "nals<PERSON><PERSON><PERSON>o menkini", "filled_map.village_desert": "Desert Village Map", "filled_map.village_plains": "Plains Village Map", "filled_map.village_savanna": "Savanna Village Map", "filled_map.village_snowy": "Snowy Village Map", "filled_map.village_taiga": "Taiga Village Map", "flat_world_preset.minecraft.bottomless_pit": "lo <PERSON>arlu", "flat_world_preset.minecraft.classic_flat": "lo <PERSON><PERSON>ta", "flat_world_preset.minecraft.desert": "lo kutytu'a", "flat_world_preset.minecraft.overworld": "lo galraimu'e", "flat_world_preset.minecraft.redstone_ready": "lo munje be la redyston.", "flat_world_preset.minecraft.snowy_kingdom": "lo si'ermu'e", "flat_world_preset.minecraft.the_void": "lo farlumu'e", "flat_world_preset.minecraft.tunnelers_dream": "lo kakpa zamnu'e", "flat_world_preset.minecraft.water_world": "xu jac<PERSON>'e", "flat_world_preset.unknown": "narju'<PERSON><PERSON><PERSON>", "gameMode.adventure": "<PERSON><PERSON> tadji", "gameMode.changed": "lo kelci javni be ri'i do cu se stika fi %s", "gameMode.creative": "finti tadji", "gameMode.hardcore": "ckape .ii", "gameMode.spectator": "kinzga tadji", "gameMode.survival": "renvi tadji", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": ".i lo velfarvi notci", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "festi", "gamerule.category.misc": "Miscellaneous", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "dan<PERSON> nun<PERSON>", "gamerule.category.updates": "munje kamcne", "gamerule.commandBlockOutput": ".i lo samseltibvla bliku notci", "gamerule.commandModificationBlockLimit": "Command modification block limit", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": ".i na ku lo nalcrnelitro ke jezmu'u cipra", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": ".i na ku lo nungumgunta", "gamerule.doDaylightCycle": ".i lo jei tcika cenba", "gamerule.doEntityDrops": ".i lo dzadza festi", "gamerule.doEntityDrops.description": "(to jei lo mu'a trene ja lo dacti greku ja lo bloti se cau nai lo sorcu cu jai se festi toi)", "gamerule.doFireTick": ".i lo jei fagri cenba", "gamerule.doImmediateRespawn": ".i lo jei te<PERSON>u rapyjbe", "gamerule.doInsomnia": ".i lo jei cupra lo cteru'i", "gamerule.doLimitedCrafting": ".i lo jei z<PERSON> sa<PERSON>u", "gamerule.doLimitedCrafting.description": "If enabled, players will be able to craft only unlocked recipes.", "gamerule.doMobLoot": ".i lo danlu festi", "gamerule.doMobLoot.description": "Controls resource drops from mobs, including experience orbs.", "gamerule.doMobSpawning": ".i lo jei cupra lo danlu", "gamerule.doMobSpawning.description": "Some entities might have separate rules.", "gamerule.doPatrolSpawning": ".i lo jei cupra lo zerpre", "gamerule.doTileDrops": ".i lo bliku festi", "gamerule.doTileDrops.description": "Controls resource drops from blocks, including experience orbs.", "gamerule.doTraderSpawning": "Spawn Wandering Traders", "gamerule.doVinesSpread": "Vines spread", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other types of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "majgau lei visnalka'e cizda'u", "gamerule.doWeatherCycle": ".i lo jei tcima cenba", "gamerule.drowningDamage": ".i lo jei j<PERSON> xrani", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender <PERSON> vanish on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether Ender <PERSON> thrown by a player vanish when that player dies.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": ".i lo jei farlu xrani", "gamerule.fireDamage": ".i lo jei fagri xrani", "gamerule.forgiveDeadPlayers": ".i lo jei fraxu lo morsi", "gamerule.forgiveDeadPlayers.description": "(to jei lo fengu ke nutli danlu co'u fengu ca lo nu lo se fengu cu mrobi'o toi)", "gamerule.freezeDamage": ".i lo jei dunja xrani", "gamerule.globalSoundEvents": "Global sound events", "gamerule.globalSoundEvents.description": "When certain game events happen, like a boss spawning, the sound is heard everywhere.", "gamerule.keepInventory": ".i na ku lo jei ka'e cirko lo sorcu", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "When flowing lava is surrounded on two sides by lava sources it converts into a source.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": ".i lo sazypre samseltibvla notci", "gamerule.maxCommandChainLength": ".i samseltibvla rebla nilmei fa li su'e", "gamerule.maxCommandChainLength.description": "Applies to command block chains and functions.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": ".i danlu nildanre fa li su'e", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "In mob explosions, some blocks won't drop their loot", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": ".i lo danlu <PERSON>po", "gamerule.naturalRegeneration": ".i lo jei jinzi ka'ozma", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "klani sipna", "gamerule.playersSleepingPercentage.description": "The percentage of players who must be sleeping to skip the night.", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": ".i munje cenba nilcafne fa li", "gamerule.reducedDebugInfo": ".i lo mleca ke cipra datni", "gamerule.reducedDebugInfo.description": "Limits contents of debug screen.", "gamerule.sendCommandFeedback": ".i lo samseltibvla notci", "gamerule.showDeathMessages": ".i lo mronoi", "gamerule.snowAccumulationHeight": "Snow accumulation height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow form on the ground up to at most this number of layers.", "gamerule.spawnChunkRadius": "Spawn chunk radius", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": ".i veljbe canlu nilbra fa li", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": ".i lo jei lo pru'uxfri cu z<PERSON>au", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "In TNT explosions, some blocks won't drop their loot", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": ".i lo jei kampu kamfe'u", "gamerule.universalAnger.description": "(to jei lo fengu ke nutli danlu cu fengu ro keipre se ba'i lo ka ce'u fengu lo po'o keipre poi gunta ri toi)", "gamerule.waterSourceConversion": "Water converts to source", "gamerule.waterSourceConversion.description": "When flowing water is surrounded on two sides by water sources it converts into a source.", "generator.custom": "lo me do moi", "generator.customized": "la<PERSON>", "generator.minecraft.amplified": "barda", "generator.minecraft.amplified.info": "ju'i pilno fi lo xajmi .i nitcu lo sutra skami.", "generator.minecraft.debug_all_block_states": "cfikalte tadji", "generator.minecraft.flat": "bal<PERSON><PERSON>", "generator.minecraft.large_biomes": "barda ke jmive vanbi", "generator.minecraft.normal": "fadni", "generator.minecraft.single_biome_surface": "pav<PERSON>", "generator.single_biome_caves": "Caves", "generator.single_biome_floating_islands": "fulta da<PERSON>lu", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Problem sending your report", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "xukmi a xalka", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise promoting indecent behavior involving children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging your or someone else's reputation, for example sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "Velski:", "gui.abuseReport.reason.false_reporting": "False Reporting", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "Harassment or bullying", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "cnicro voksa", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s:%s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "te<PERSON><PERSON><PERSON>le a tcevlile", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, promoting, or threatening to commit acts of terrorism or violent extremism for political, religious, ideological, or other reasons.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "An error was returned while sending your report:\n'%s'", "gui.abuseReport.send.generic_error": "Encountered an unexpected error while sending your report.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "Sending your report...", "gui.abuseReport.sent.title": "zilbe'i ternoi", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Player Name", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": ".i radji'i", "gui.advancements": "so'i nandu", "gui.all": "ro kantu", "gui.back": "x<PERSON>ti", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "Your account is permanently banned, which means you can't play online or join Realms.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "banskami. %s", "gui.banned.description.reason_id_message": "banskami: %s - %s", "gui.banned.description.temporary": "%s Until then, you can't play online or join Realms.", "gui.banned.description.temporary.duration": "Your account is temporarily suspended and will be reactivated in %s.", "gui.banned.description.unknownreason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified that it goes against the Minecraft Community Standards.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Abusive language used in a directed, harmful manner", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Account permanently banned", "gui.banned.title.temporary": "Account temporarily suspended", "gui.cancel": "x<PERSON>ti", "gui.chatReport.comments": "Comments", "gui.chatReport.describe": "Sharing details will help us make a well-informed decision.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "do cliva e posystizu'e lo datnypelji", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Continue Editing", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Continue Editing", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "Please describe what happened:", "gui.chatReport.observed_what": "ki'a do ca ternoi ti", "gui.chatReport.read_info": "Learn About Reporting", "gui.chatReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Select Chat Messages to Report", "gui.chatReport.select_reason": "cuxna lo datnypelji klesi", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "benji lo dat<PERSON>ji", "gui.chatReport.send.comments_too_long": "Please shorten the comment", "gui.chatReport.send.no_reason": "e'o cuxna lo dat<PERSON>pel<PERSON> klesi", "gui.chatReport.send.no_reported_messages": "Please select at least one chat message to report", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "Report Player Chat", "gui.chatSelection.context": "Messages surrounding this selection will be included to provide additional context", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s pu cusku: %s %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "Continue", "gui.copy_link_to_clipboard": "Copy Link to Clipboard", "gui.days": "%s djedi", "gui.done": "mulno", "gui.down": "<PERSON><PERSON><PERSON>", "gui.entity_tooltip.type": "se klesi la'oi %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s cacra", "gui.loadingMinecraft": "Loading Minecraft", "gui.minutes": "%s mentu", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "lo %s cu batke", "gui.narrate.editBox": "%s edit box: %s", "gui.narrate.slider": "lo %s cu catke zei u<PERSON>je", "gui.narrate.tab": "%s tab", "gui.no": "na go'i", "gui.none": "no da", "gui.ok": "je'e", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "ko ranji", "gui.recipebook.moreRecipes": ".i .e'u do prityk<PERSON>'iki", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "sisku", "gui.recipebook.toggleRecipes.all": ".i jarco ro da", "gui.recipebook.toggleRecipes.blastable": ".i jarco tu'a lo tsatoknu", "gui.recipebook.toggleRecipes.craftable": ".i jarco tu'a lo z<PERSON>jbu", "gui.recipebook.toggleRecipes.smeltable": ".i jarco tu'a lo toknu", "gui.recipebook.toggleRecipes.smokable": ".i jarco tu'a lo damtoknu", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Manage with Microsoft account", "gui.socialInteractions.empty_blocked": "No blocked players in chat", "gui.socialInteractions.empty_hidden": "No players hidden in chat", "gui.socialInteractions.hidden_in_chat": "Chat messages from %s will be hidden", "gui.socialInteractions.hide": "Hide in Chat", "gui.socialInteractions.narration.hide": "Hide messages from %s", "gui.socialInteractions.narration.report": "Report player %s", "gui.socialInteractions.narration.show": "Show messages from %s", "gui.socialInteractions.report": "ternoi", "gui.socialInteractions.search_empty": "le bi'unai cmene", "gui.socialInteractions.search_hint": "Search...", "gui.socialInteractions.server_label.multiple": "%s - %s le za'u ci'erkei", "gui.socialInteractions.server_label.single": "%s - %s kelci", "gui.socialInteractions.show": "Show in Chat", "gui.socialInteractions.shown_in_chat": "Chat messages from %s will be shown", "gui.socialInteractions.status_blocked": "Blocked", "gui.socialInteractions.status_blocked_offline": "Blocked - Offline", "gui.socialInteractions.status_hidden": "Hidden", "gui.socialInteractions.status_hidden_offline": "Hidden - Offline", "gui.socialInteractions.status_offline": "na<PERSON><PERSON>jo'e", "gui.socialInteractions.tab_all": "Roda", "gui.socialInteractions.tab_blocked": "bliku", "gui.socialInteractions.tab_hidden": "mipri", "gui.socialInteractions.title": "Social Interactions", "gui.socialInteractions.tooltip.hide": "Hide messages", "gui.socialInteractions.tooltip.report": "Report player", "gui.socialInteractions.tooltip.report.disabled": "The reporting service is unavailable", "gui.socialInteractions.tooltip.report.no_messages": "No reportable messages from player %s", "gui.socialInteractions.tooltip.report.not_reportable": "This player can't be reported, because their chat messages can't be verified on this server", "gui.socialInteractions.tooltip.show": "Show messages", "gui.stats": "lo datni", "gui.toMenu": "xruti fi lo servero liste", "gui.toRealms": "Back to Realms List", "gui.toTitle": "xruti fi lo ralju liste", "gui.toWorld": "Back to World List", "gui.togglable_slot": "Click to disable slot", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "ja'a go'i", "hanging_sign.edit": "ciska fi lo dandu lertapla", "instrument.minecraft.admire_goat_horn": "sinma", "instrument.minecraft.call_goat_horn": "klac<PERSON>", "instrument.minecraft.dream_goat_horn": "senva", "instrument.minecraft.feel_goat_horn": "cinmo", "instrument.minecraft.ponder_goat_horn": "pensi", "instrument.minecraft.seek_goat_horn": "sisku", "instrument.minecraft.sing_goat_horn": "sanga", "instrument.minecraft.yearn_goat_horn": "djica", "inventory.binSlot": "das<PERSON>", "inventory.hotbarInfo": ".i rejgau le daigri te'a'a %1$s joi %2$s", "inventory.hotbarSaved": ".i rejgau ti'oi daigri (to samy<PERSON>'i te'a'a %1$s joi %2$s toi)", "item.canBreak": "ka'e porpi", "item.canPlace": "selpu'ika'e fi", "item.canUse.unknown": "narselju'o", "item.color": "lo selska cu %s", "item.components": "%s component(s)", "item.disabled": "Disabled item", "item.durability": "Durability: %s / %s", "item.dyed": "Dyed", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON> bloti", "item.minecraft.acacia_chest_boat": "le atkaciia bloti ku joi le gairvau", "item.minecraft.allay_spawn_egg": "le blapedvoi jbeso'a", "item.minecraft.amethyst_shard": ".amtisti selpo'i", "item.minecraft.angler_pottery_shard": "Angler Pottery Shard", "item.minecraft.angler_pottery_sherd": "fipkalte ke staku selpo'i", "item.minecraft.apple": "plise", "item.minecraft.archer_pottery_shard": "Archer Pottery Shard", "item.minecraft.archer_pottery_sherd": "bagycelpli ke staku selpo'i", "item.minecraft.armadillo_scute": "da<PERSON><PERSON>i piltapla", "item.minecraft.armadillo_spawn_egg": "dapsodi jbes<PERSON>'a", "item.minecraft.armor_stand": "badycakyzbe", "item.minecraft.arms_up_pottery_shard": "Arms Up Pottery Shard", "item.minecraft.arms_up_pottery_sherd": "birlafti ke staku selpo'i", "item.minecraft.arrow": "celga'a", "item.minecraft.axolotl_bucket": "Bucket of Axolotl", "item.minecraft.axolotl_spawn_egg": "Axolotl Spawn Egg", "item.minecraft.baked_potato": "se<PERSON><PERSON><PERSON><PERSON> patlu", "item.minecraft.bamboo_chest_raft": "bambu pinblo joi vasru", "item.minecraft.bamboo_raft": "bambu pinblo", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.bee_spawn_egg": "bifce jbeso'a", "item.minecraft.beef": "nalseljukpa bak<PERSON>'u", "item.minecraft.beetroot": "spatrtensai", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON> tsiju", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON> stasu", "item.minecraft.birch_boat": ".eptula bloti", "item.minecraft.birch_chest_boat": "le ricrbetula bloti ku joi le gairvau", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "x<PERSON><PERSON> xinmo", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Blade Pottery Shard", "item.minecraft.blade_pottery_sherd": "balre ke staku selpo'i", "item.minecraft.blaze_powder": "fagri purmo", "item.minecraft.blaze_rod": "fagri grana", "item.minecraft.blaze_spawn_egg": "fagda'u jbeso'a", "item.minecraft.blue_bundle": "Blue Bundle", "item.minecraft.blue_dye": "blanu xinmo", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "bongu", "item.minecraft.bone_meal": "bongu purmo", "item.minecraft.book": "cukta", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "bagyce'a", "item.minecraft.bowl": "<PERSON>bri", "item.minecraft.bread": "nanba", "item.minecraft.breeze_rod": "bifyda'u grana", "item.minecraft.breeze_spawn_egg": "bifyda'u jbeso'a", "item.minecraft.brewer_pottery_shard": "Brewer Pottery Shard", "item.minecraft.brewer_pottery_sherd": "birjyzba ke staku selpo'i", "item.minecraft.brewing_stand": "Brewing Stand", "item.minecraft.brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "bunre xinmo", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "burcu", "item.minecraft.bucket": "baktu", "item.minecraft.bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed stack of items", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Burn Pottery Shard", "item.minecraft.burn_pottery_sherd": "jelca ke staku selpo'i", "item.minecraft.camel_spawn_egg": "rabykumte j<PERSON>'a", "item.minecraft.carrot": "najgenja", "item.minecraft.carrot_on_a_stick": "najgenja co jorne be lo grana", "item.minecraft.cat_spawn_egg": "mlatu j<PERSON>'a", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "kevjukni j<PERSON>'a", "item.minecraft.chainmail_boots": "linsi <PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "linsi bad<PERSON>", "item.minecraft.chainmail_helmet": "linsi badmapku", "item.minecraft.chainmail_leggings": "linsi bad<PERSON>palku", "item.minecraft.charcoal": "fagytabno", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloti", "item.minecraft.cherry_chest_boat": "r<PERSON><PERSON><PERSON><PERSON> bloti joi vasru", "item.minecraft.chest_minecart": "baktu trene joi vasru", "item.minecraft.chicken": "nalseljukpa jip<PERSON>'u", "item.minecraft.chicken_spawn_egg": "ji<PERSON><PERSON> jbeso'a", "item.minecraft.chorus_fruit": "girza'a grute", "item.minecraft.clay_ball": "kityboi", "item.minecraft.clock": "junla", "item.minecraft.coal": "kolme", "item.minecraft.coast_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "ca<PERSON> dembi", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON> baktu", "item.minecraft.cod_spawn_egg": "fi<PERSON><PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.command_block_minecart": "baktu trene joi ziltinbe bliku", "item.minecraft.compass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "se<PERSON><PERSON>k<PERSON> bak<PERSON>'u", "item.minecraft.cooked_chicken": "se<PERSON>juk<PERSON> jip<PERSON>'u", "item.minecraft.cooked_cod": "se<PERSON>ju<PERSON><PERSON>", "item.minecraft.cooked_mutton": "seljukpa lanre'u", "item.minecraft.cooked_porkchop": "seljukpa xajre'u", "item.minecraft.cooked_rabbit": "seljukpa ractu", "item.minecraft.cooked_salmon": "seljukpa salmone", "item.minecraft.cookie": "cmalu titnanba", "item.minecraft.copper_ingot": "tunka tanbo", "item.minecraft.cow_spawn_egg": "bakni j<PERSON>'a", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "ri'orfrupo'a flira", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "jbeso'a be la kriper.", "item.minecraft.crossbow": "seljicyce'a", "item.minecraft.crossbow.projectile": "selce'a:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "cicna xinmo", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Danger Pottery Shard", "item.minecraft.danger_pottery_sherd": "ckape ke staku selpo'i", "item.minecraft.dark_oak_boat": "man<PERSON><PERSON> bloti", "item.minecraft.dark_oak_chest_boat": "le manku bo cindu bloti ku joi le botpi", "item.minecraft.debug_stick": "gafygaurga'a", "item.minecraft.debug_stick.empty": "%s has no properties", "item.minecraft.debug_stick.select": "selected \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" to %s", "item.minecraft.diamond": "tabjme", "item.minecraft.diamond_axe": "tabjme ka'amru", "item.minecraft.diamond_boots": "tabjme cutci", "item.minecraft.diamond_chestplate": "tabjme creka", "item.minecraft.diamond_helmet": "tabjme mapku", "item.minecraft.diamond_hoe": "tabjme terplixa", "item.minecraft.diamond_horse_armor": "tabjme xirbadycalku", "item.minecraft.diamond_leggings": "tabjme palku", "item.minecraft.diamond_pickaxe": "tabjme tunymru", "item.minecraft.diamond_shovel": "tabjme canpa", "item.minecraft.diamond_sword": "tabjme cladaxi", "item.minecraft.disc_fragment_5": "lo spisa jinplita", "item.minecraft.disc_fragment_5.desc": "Music Disc - 5", "item.minecraft.dolphin_spawn_egg": "delfinu jbeso'a", "item.minecraft.donkey_spawn_egg": "xasli j<PERSON>'a", "item.minecraft.dragon_breath": "drak<PERSON> selva'u ", "item.minecraft.dried_kelp": "se sudgau vraike", "item.minecraft.drowned_spawn_egg": "j<PERSON><PERSON><PERSON> mi<PERSON> j<PERSON>'a", "item.minecraft.dune_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "mirsna selpo'i", "item.minecraft.egg": "sovda", "item.minecraft.elder_guardian_spawn_egg": "dzebadypre jbes<PERSON>'a", "item.minecraft.elytra": "na'irnelitrono", "item.minecraft.emerald": "ri'ojme", "item.minecraft.enchanted_book": "mafcku", "item.minecraft.enchanted_golden_apple": "selma<PERSON><PERSON>'a sloplise", "item.minecraft.end_crystal": ".endre krili", "item.minecraft.ender_dragon_spawn_egg": "me la .endr. drakono jbeso'a", "item.minecraft.ender_eye": ".endre kanla", "item.minecraft.ender_pearl": ".end<PERSON> boijme", "item.minecraft.enderman_spawn_egg": ".endre prenu jbeso'a", "item.minecraft.endermite_spawn_egg": ".endre miptera jbeso'a", "item.minecraft.evoker_spawn_egg": "pacykemklacpepre jbes<PERSON>'a", "item.minecraft.experience_bottle": "makfa botpi", "item.minecraft.explorer_pottery_shard": "Explorer <PERSON><PERSON> Shard", "item.minecraft.explorer_pottery_sherd": "tumfakli'u ke staku selpo'i", "item.minecraft.eye_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "pimlu", "item.minecraft.fermented_spider_eye": "fusra ke jukni kanla", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "cartu", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "mlefagri jakne", "item.minecraft.firework_rocket.flight": "vofli snidu li", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "spoja tarmi fa", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "blanu", "item.minecraft.firework_star.brown": "bunre", "item.minecraft.firework_star.custom_color": "Custom", "item.minecraft.firework_star.cyan": "cicna", "item.minecraft.firework_star.fade_to": "se binxo skari fa", "item.minecraft.firework_star.flicker": "camsuksa", "item.minecraft.firework_star.gray": "grusi", "item.minecraft.firework_star.green": "crino", "item.minecraft.firework_star.light_blue": "labybla", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "pelri'o", "item.minecraft.firework_star.magenta": "nukni", "item.minecraft.firework_star.orange": "narju", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "zirpu", "item.minecraft.firework_star.red": "xunre", "item.minecraft.firework_star.shape": "lo nalsau", "item.minecraft.firework_star.shape.burst": "lo sukpo'a", "item.minecraft.firework_star.shape.creeper": "la .kripr.", "item.minecraft.firework_star.shape.large_ball": "barda bolci", "item.minecraft.firework_star.shape.small_ball": "cmalu bolci", "item.minecraft.firework_star.shape.star": "lo jinda", "item.minecraft.firework_star.trail": "te camlu'a", "item.minecraft.firework_star.white": "blabi", "item.minecraft.firework_star.yellow": "pelxu", "item.minecraft.fishing_rod": "fipkalte grana", "item.minecraft.flint": "fagro'i", "item.minecraft.flint_and_steel": "fagrokci fa'u gasta", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "<PERSON>", "item.minecraft.flow_banner_pattern.desc": "Flow", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "<PERSON>", "item.minecraft.flower_banner_pattern.desc": "x<PERSON>la tarmi", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "Flower Pot", "item.minecraft.fox_spawn_egg": "lorxu jbeso'a", "item.minecraft.friend_pottery_shard": "<PERSON> <PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "pendo ke staku selpo'i", "item.minecraft.frog_spawn_egg": "lo pipybanfi jbeso'a", "item.minecraft.furnace_minecart": "baktu trene joi zbasu toknu", "item.minecraft.ghast_spawn_egg": "jbeso'a be la gast.", "item.minecraft.ghast_tear": "la me gast. kalselvi'i", "item.minecraft.glass_bottle": "blaci botpi", "item.minecraft.glistering_melon_slice": "mi<PERSON>i z<PERSON>pa'o", "item.minecraft.globe_banner_pattern": "<PERSON>", "item.minecraft.globe_banner_pattern.desc": "boltai", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "<PERSON>mi j<PERSON>", "item.minecraft.glow_ink_sac": "carmi ke xinmo dakli", "item.minecraft.glow_item_frame": "carmi ke ja'ordai greku", "item.minecraft.glow_squid_spawn_egg": "carmi kalmari j<PERSON>'a", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON><PERSON> pulce", "item.minecraft.goat_horn": "kanba tabryjirna", "item.minecraft.goat_spawn_egg": "kanba jbeso'a", "item.minecraft.gold_ingot": "solji tanbo", "item.minecraft.gold_nugget": "solji spisa", "item.minecraft.golden_apple": "solji plise", "item.minecraft.golden_axe": "solji ka'amru", "item.minecraft.golden_boots": "so<PERSON><PERSON>", "item.minecraft.golden_carrot": "solji najgenja", "item.minecraft.golden_chestplate": "solji creka", "item.minecraft.golden_helmet": "solji mapku", "item.minecraft.golden_hoe": "solji terplixa", "item.minecraft.golden_horse_armor": "solji xir<PERSON>", "item.minecraft.golden_leggings": "solji palku", "item.minecraft.golden_pickaxe": "solji tunymru", "item.minecraft.golden_shovel": "solji canpa", "item.minecraft.golden_sword": "solji <PERSON>", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "grusi xinmo", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "crino xinmo", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "badypre jbeso'a", "item.minecraft.gunpowder": "bampu'o", "item.minecraft.guster_banner_pattern": "<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "x<PERSON><PERSON>na", "item.minecraft.heart_pottery_shard": "Heart Pottery Shard", "item.minecraft.heart_pottery_sherd": "cnise'i ke staku selpo'i", "item.minecraft.heartbreak_pottery_shard": "Heartbreak Pottery Shard", "item.minecraft.heartbreak_pottery_sherd": "dunku ke staku selpo'i", "item.minecraft.hoglin_spawn_egg": "balxarju j<PERSON>'a", "item.minecraft.honey_bottle": "me<PERSON><PERSON><PERSON><PERSON>i", "item.minecraft.honeycomb": "biclakse", "item.minecraft.hopper_minecart": "baktu trene joi se'arko'u", "item.minecraft.horse_spawn_egg": "xirma j<PERSON>'a", "item.minecraft.host_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Howl Pottery Shard", "item.minecraft.howl_pottery_sherd": "gerki'a ke staku selpo'i", "item.minecraft.husk_spawn_egg": "sudga mi<PERSON> j<PERSON>'a", "item.minecraft.ink_sac": "xinmo dakli", "item.minecraft.iron_axe": "tirse ka'amru", "item.minecraft.iron_boots": "tirse cutci", "item.minecraft.iron_chestplate": "tirse creka", "item.minecraft.iron_golem_spawn_egg": "tirse re<PERSON><PERSON><PERSON>'i jbeso'a", "item.minecraft.iron_helmet": "tirse mapku", "item.minecraft.iron_hoe": "tirse terplixa", "item.minecraft.iron_horse_armor": "tirse xir<PERSON>", "item.minecraft.iron_ingot": "tirse tanbo", "item.minecraft.iron_leggings": "tirse palku", "item.minecraft.iron_nugget": "tirse spisa", "item.minecraft.iron_pickaxe": "tirse tunymru", "item.minecraft.iron_shovel": "tirse canpa", "item.minecraft.iron_sword": "tirse clad<PERSON>i", "item.minecraft.item_frame": "ja'or<PERSON> greku", "item.minecraft.jungle_boat": "cic<PERSON><PERSON><PERSON> bloti", "item.minecraft.jungle_chest_boat": "le cicricfoi bloti ku joi le gairvau", "item.minecraft.knowledge_book": "sel<PERSON>'o cukta", "item.minecraft.lapis_lazuli": "blaro'i", "item.minecraft.lava_bucket": "lik<PERSON><PERSON><PERSON> baktu", "item.minecraft.lead": "velxlu", "item.minecraft.leather": "b<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "b<PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "b<PERSON><PERSON><PERSON> creka", "item.minecraft.leather_helmet": "bakskapi mapku", "item.minecraft.leather_horse_armor": "bakska<PERSON> ke xirma daby<PERSON>ku", "item.minecraft.leather_leggings": "b<PERSON><PERSON><PERSON> palku", "item.minecraft.light_blue_bundle": "Light Blue Bundle", "item.minecraft.light_blue_dye": "labybla xinmo", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "labygrusi xinmo", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Lime Bundle", "item.minecraft.lime_dye": "pel<PERSON>'o xinmo", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "ma<PERSON><PERSON><PERSON> ma<PERSON>'u<PERSON>ri", "item.minecraft.lingering_potion.effect.awkward": "Awkward Lingering Potion", "item.minecraft.lingering_potion.effect.empty": "Lingering Uncraftable Potion", "item.minecraft.lingering_potion.effect.fire_resistance": "Lingering Potion of Fire Resistance", "item.minecraft.lingering_potion.effect.harming": "Lingering Potion of Harming", "item.minecraft.lingering_potion.effect.healing": "Lingering Potion of Healing", "item.minecraft.lingering_potion.effect.infested": "Lingering Potion of Infestation", "item.minecraft.lingering_potion.effect.invisibility": "Lingering Potion of Invisibility", "item.minecraft.lingering_potion.effect.leaping": "Lingering Potion of Leaping", "item.minecraft.lingering_potion.effect.levitation": "Lingering Potion of Levitation", "item.minecraft.lingering_potion.effect.luck": "Lingering Potion of Luck", "item.minecraft.lingering_potion.effect.mundane": "Mu<PERSON>ne Lingering Potion", "item.minecraft.lingering_potion.effect.night_vision": "Lingering Potion of Night Vision", "item.minecraft.lingering_potion.effect.oozing": "Lingering Potion of Oozing", "item.minecraft.lingering_potion.effect.poison": "Lingering Potion of Poison", "item.minecraft.lingering_potion.effect.regeneration": "Lingering Potion of Regeneration", "item.minecraft.lingering_potion.effect.slow_falling": "Lingering Potion of Slow Falling", "item.minecraft.lingering_potion.effect.slowness": "Lingering Potion of Slowness", "item.minecraft.lingering_potion.effect.strength": "Lingering Potion of Strength", "item.minecraft.lingering_potion.effect.swiftness": "Lingering Potion of Swiftness", "item.minecraft.lingering_potion.effect.thick": "T<PERSON>k Lingering Potion", "item.minecraft.lingering_potion.effect.turtle_master": "Lingering Potion of the Turtle Master", "item.minecraft.lingering_potion.effect.water": "Lingering Water Bottle", "item.minecraft.lingering_potion.effect.water_breathing": "Lingering Potion of Water Breathing", "item.minecraft.lingering_potion.effect.weakness": "Lingering Potion of Weakness", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Potion of Wind Charging", "item.minecraft.llama_spawn_egg": "tcokumte jbeso'a", "item.minecraft.lodestone_compass": "maksi fartci", "item.minecraft.mace": "Mace", "item.minecraft.magenta_bundle": "Magenta Bundle", "item.minecraft.magenta_dye": "nukni xinmo", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "lik<PERSON>'i kruji", "item.minecraft.magma_cube_spawn_egg": "likrokybli jbes<PERSON>'a", "item.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloti", "item.minecraft.mangrove_chest_boat": "le jiryricfoi bloti ku joi le botpi", "item.minecraft.map": "kunti cartu", "item.minecraft.melon_seeds": "guzme tsiju", "item.minecraft.melon_slice": "guzme panlo", "item.minecraft.milk_bucket": "ladru baktu", "item.minecraft.minecart": "baktu trene", "item.minecraft.miner_pottery_shard": "Miner <PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "kunkakpa ke staku selpo'i", "item.minecraft.mojang_banner_pattern": "<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "zo'e", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "bak<PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.mourner_pottery_shard": "Mourner Pottery Shard", "item.minecraft.mourner_pottery_sherd": "mrodri ke staku selpo'i", "item.minecraft.mule_spawn_egg": "xasly<PERSON>'a jbeso'a", "item.minecraft.mushroom_stew": "<PERSON>ri stasu", "item.minecraft.music_disc_11": "zgike vreji", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "zgike vreji", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "zgike vreji", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "zgike vreji", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "zgike vreji", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "zgike vreji", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Music Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Music Disc", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "zgike vreji", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "zgike vreji", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "zgike vreji", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "zgike vreji", "item.minecraft.music_disc_otherside.desc": "la'o lena raine. ragve", "item.minecraft.music_disc_pigstep": "zgike vreji", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Music Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "zgike vreji", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "zgike vreji", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "zgike vreji", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "zgike vreji", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "zgike vreji", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "nalseljukpa lanr'eu", "item.minecraft.name_tag": "cm<PERSON> tcita", "item.minecraft.nautilus_shell": "ca<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nether_brick": "la me neter. kitybli", "item.minecraft.nether_star": "la me neter. tarci", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka'amru", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>a", "item.minecraft.netherite_helmet": "dzi<PERSON>jyjin<PERSON> mapku", "item.minecraft.netherite_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "item.minecraft.netherite_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>li", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> palku", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tun<PERSON>ru", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spisa", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> canpa", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "jimyzbatermo'a", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "cindu bloti", "item.minecraft.oak_chest_boat": "le cindu bloti ku joi le botpi", "item.minecraft.ocelot_spawn_egg": "cicmlatu j<PERSON>'a", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Orange Bundle", "item.minecraft.orange_dye": "narju xinmo", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "pixra", "item.minecraft.pale_oak_boat": "Pale Oak Boat", "item.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "latcribe jbeso'a", "item.minecraft.paper": "pelji", "item.minecraft.parrot_spawn_egg": "<PERSON>aki j<PERSON>'a", "item.minecraft.phantom_membrane": "cteru'i nalci", "item.minecraft.phantom_spawn_egg": "cteru'i jbeso'a", "item.minecraft.pig_spawn_egg": "xarju j<PERSON>'a", "item.minecraft.piglin_banner_pattern": "<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "xaj<PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "cilce xajypre jbeso'a", "item.minecraft.piglin_spawn_egg": "xajypre jbeso'a", "item.minecraft.pillager_spawn_egg": "vilcpapre jbeso'a", "item.minecraft.pink_bundle": "Pink Bundle", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Pitcher Plant", "item.minecraft.pitcher_pod": "Pitcher Pod", "item.minecraft.plenty_pottery_shard": "Plenty Pottery Shard", "item.minecraft.plenty_pottery_sherd": "kamcfu ke staku selpo'i", "item.minecraft.poisonous_potato": "vindu patla", "item.minecraft.polar_bear_spawn_egg": "bercribe jbeso'a", "item.minecraft.popped_chorus_fruit": "selpo'i girza'a grute", "item.minecraft.porkchop": "nalseljukpa xajre'u", "item.minecraft.potato": "<PERSON><PERSON>", "item.minecraft.potion": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "nal<PERSON>'e ma<PERSON>va", "item.minecraft.potion.effect.empty": "nalzbaka'e mafsodva", "item.minecraft.potion.effect.fire_resistance": "fagri xarnu <PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.potion.effect.healing": "kan<PERSON>", "item.minecraft.potion.effect.infested": "Potion of Infestation", "item.minecraft.potion.effect.invisibility": "selvisnalka'e ma<PERSON>odva", "item.minecraft.potion.effect.leaping": "p<PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "fulta ma<PERSON>", "item.minecraft.potion.effect.luck": "funca ma<PERSON>odva", "item.minecraft.potion.effect.mundane": "fadni <PERSON>", "item.minecraft.potion.effect.night_vision": "ctecpi ma<PERSON>", "item.minecraft.potion.effect.oozing": "Potion of Oozing", "item.minecraft.potion.effect.poison": "vindu ma<PERSON>", "item.minecraft.potion.effect.regeneration": "ka'or<PERSON>'o ma<PERSON>va", "item.minecraft.potion.effect.slow_falling": "snofa'u mafsodva", "item.minecraft.potion.effect.slowness": "ma<PERSON><PERSON>", "item.minecraft.potion.effect.strength": "t<PERSON>i ma<PERSON>", "item.minecraft.potion.effect.swiftness": "klama sutra mafsodva", "item.minecraft.potion.effect.thick": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.potion.effect.turtle_master": "cakrespycei mafsodva", "item.minecraft.potion.effect.water": "d<PERSON><PERSON> botpi", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "ruble ma<PERSON>va", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "Archer Pottery Shard", "item.minecraft.pottery_shard_arms_up": "Arms Up Pottery Shard", "item.minecraft.pottery_shard_prize": "Prize <PERSON><PERSON> Shard", "item.minecraft.pottery_shard_skull": "Skull Pottery Shard", "item.minecraft.powder_snow_bucket": "baktu be lo pumsi'e", "item.minecraft.prismarine_crystals": "Prismarine Crystals", "item.minecraft.prismarine_shard": "selpo'i me la prismarin.", "item.minecraft.prize_pottery_shard": "Prize <PERSON><PERSON> Shard", "item.minecraft.prize_pottery_sherd": "selji'a ke staku selpo'i", "item.minecraft.pufferfish": "finprtetraodontide", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> baktu", "item.minecraft.pufferfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.pumpkin_pie": "guzmrkukurb<PERSON> na<PERSON>palne", "item.minecraft.pumpkin_seeds": "guzmrkukurbita tsiju", "item.minecraft.purple_bundle": "Purple Bundle", "item.minecraft.purple_dye": "zirpu xinmo", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "k<PERSON><PERSON>'eri", "item.minecraft.rabbit": "nalseljukpa ractu", "item.minecraft.rabbit_foot": "jamfu be lo ractu", "item.minecraft.rabbit_hide": "skapi be lo ractu", "item.minecraft.rabbit_spawn_egg": "ratcu jbeso'a", "item.minecraft.rabbit_stew": "ractu stasu", "item.minecraft.raiser_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "nunspokezgau jbeso'a", "item.minecraft.raw_copper": "na<PERSON><PERSON><PERSON><PERSON><PERSON>'e tunka", "item.minecraft.raw_gold": "nalselrucyzu'e solji", "item.minecraft.raw_iron": "nals<PERSON><PERSON><PERSON><PERSON>'e tirse", "item.minecraft.recovery_compass": "<PERSON>aj<PERSON><PERSON><PERSON>", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "x<PERSON>re xinmo", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "me la redston. pulce", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.resin_clump": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "fusra rectu", "item.minecraft.saddle": "dan<PERSON> seltse", "item.minecraft.salmon": "nalseljukpa salmone", "item.minecraft.salmon_bucket": "salmone baktu", "item.minecraft.salmon_spawn_egg": "salmone jbeso'a", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "bakfu ke staku selpo'i", "item.minecraft.shears": "jinci", "item.minecraft.sheep_spawn_egg": "lanme jbeso'a", "item.minecraft.shelter_pottery_shard": "Shelter Pottery Shard", "item.minecraft.shelter_pottery_sherd": "marbi ke staku selpo'i", "item.minecraft.shield": "badgai", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> bad<PERSON>", "item.minecraft.shield.blue": "blanu badgai", "item.minecraft.shield.brown": "bunre badgai", "item.minecraft.shield.cyan": "c<PERSON>na badgai", "item.minecraft.shield.gray": "grusi badgai", "item.minecraft.shield.green": "crino badgai", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON> badgai", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON> badgai", "item.minecraft.shield.lime": "pelri'o badgai", "item.minecraft.shield.magenta": "nukni badgai", "item.minecraft.shield.orange": "narju badgai", "item.minecraft.shield.pink": "penka badgai", "item.minecraft.shield.purple": "zirpu badgai", "item.minecraft.shield.red": "x<PERSON>re badgai", "item.minecraft.shield.white": "blabi badgai", "item.minecraft.shield.yellow": "pelxu badgai", "item.minecraft.shulker_shell": "calku pe la cylkr.", "item.minecraft.shulker_spawn_egg": "jbeso'a be la cylkr.", "item.minecraft.sign": "Sign", "item.minecraft.silence_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "se<PERSON><PERSON><PERSON><PERSON>'u j<PERSON>o'a", "item.minecraft.skeleton_horse_spawn_egg": "bogrek<PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.skeleton_spawn_egg": "b<PERSON><PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.skull_banner_pattern": "<PERSON>", "item.minecraft.skull_banner_pattern.desc": "sed<PERSON><PERSON><PERSON> tarmi", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Skull Pottery Shard", "item.minecraft.skull_pottery_sherd": "sedbo'u ke staku selpo'i", "item.minecraft.slime_ball": "lo boljdu", "item.minecraft.slime_spawn_egg": "rigdulda'u jbeso'a", "item.minecraft.smithing_template": "jimyzbatermo'a", "item.minecraft.smithing_template.applies_to": "Applies to:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Add ingot or crystal", "item.minecraft.smithing_template.armor_trim.applies_to": "Armor", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Add a piece of armor", "item.minecraft.smithing_template.armor_trim.ingredients": "Ingots & Crystals", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Add Netherite Ingot", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamond Equipment", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Add diamond armor, weapon, or tool", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite Ingot", "item.minecraft.smithing_template.upgrade": "Upgrade: ", "item.minecraft.sniffer_spawn_egg": "zbisakci jbes<PERSON>'a", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "zbisakci ke staku selpo'i", "item.minecraft.snout_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "snime remsmimi'i jbeso'a", "item.minecraft.snowball": "snime bolci", "item.minecraft.spectral_arrow": "Spectral Arrow", "item.minecraft.spider_eye": "jukni kanla", "item.minecraft.spider_spawn_egg": "jukni j<PERSON>a", "item.minecraft.spire_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "sel<PERSON>'o ma<PERSON><PERSON>va", "item.minecraft.splash_potion.effect.awkward": "nalpa'e ke selre'o mafsodva", "item.minecraft.splash_potion.effect.empty": "nalz<PERSON><PERSON>'e ke selre'o mafsodva", "item.minecraft.splash_potion.effect.fire_resistance": "nunfagyxarnu ke selre'o mafsodva", "item.minecraft.splash_potion.effect.harming": "nunxai ke selre'o mafsodva", "item.minecraft.splash_potion.effect.healing": "nunka'o ke selre'o mafsodva", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "nunselvisnalka'e ke selre'o mafsodva", "item.minecraft.splash_potion.effect.leaping": "nunpi'e ke selre'o mafsodva", "item.minecraft.splash_potion.effect.levitation": "fulta ke selre'o mafsodva", "item.minecraft.splash_potion.effect.luck": "funca ke mafsodva mafsodva", "item.minecraft.splash_potion.effect.mundane": "fadni ke selre'o mafsodva", "item.minecraft.splash_potion.effect.night_vision": "nunman<PERSON>'a selre'o mafsodva", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "vindu ke selre'o mafsodva", "item.minecraft.splash_potion.effect.regeneration": "nunka'orbi'o ke selre'o mafsodva", "item.minecraft.splash_potion.effect.slow_falling": "nunsnofa'u ke selre'o mafsodva", "item.minecraft.splash_potion.effect.slowness": "nunklasno ke selre'o mafsodva", "item.minecraft.splash_potion.effect.strength": "nuntsa ke selre'o mafsodva", "item.minecraft.splash_potion.effect.swiftness": "nunklasutra ke selre'o mafsodva", "item.minecraft.splash_potion.effect.thick": "rotsu ke selre'o mafsodva", "item.minecraft.splash_potion.effect.turtle_master": "selre'o ke cakres<PERSON>cei mafsodva", "item.minecraft.splash_potion.effect.water": "selre'o jacy<PERSON>'i", "item.minecraft.splash_potion.effect.water_breathing": "nunja<PERSON><PERSON><PERSON>'u ke selre'o mafsodva", "item.minecraft.splash_potion.effect.weakness": "ruble ke selre'o mafsodva", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "konku'u bloti", "item.minecraft.spruce_chest_boat": "le ku'urpice'a bloti ku joi le gairvau", "item.minecraft.spyglass": "zgablaci", "item.minecraft.squid_spawn_egg": "kalmari j<PERSON>'a", "item.minecraft.stick": "grana", "item.minecraft.stone_axe": "rok<PERSON> ka'amru", "item.minecraft.stone_hoe": "r<PERSON><PERSON> terplixa", "item.minecraft.stone_pickaxe": "rok<PERSON> tunymru", "item.minecraft.stone_shovel": "rok<PERSON> canpa", "item.minecraft.stone_sword": "r<PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "muvzva jbeso'a", "item.minecraft.strider_spawn_egg": "fagdzu jbeso'a", "item.minecraft.string": "skori", "item.minecraft.sugar": "sakta", "item.minecraft.suspicious_stew": "se<PERSON><PERSON><PERSON> stasu", "item.minecraft.sweet_berries": "titla j<PERSON>", "item.minecraft.tadpole_bucket": "verban<PERSON> baktu", "item.minecraft.tadpole_spawn_egg": "lo verbanfi jbeso'a", "item.minecraft.tide_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "makfa calga'a", "item.minecraft.tipped_arrow.effect.awkward": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.empty": "Uncraftable Tipped Arrow", "item.minecraft.tipped_arrow.effect.fire_resistance": "fag<PERSON>arnu celga'a", "item.minecraft.tipped_arrow.effect.harming": "xrani celga'a", "item.minecraft.tipped_arrow.effect.healing": "kanro celga'a", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Infestation", "item.minecraft.tipped_arrow.effect.invisibility": "selvisnalka'e celga'a", "item.minecraft.tipped_arrow.effect.leaping": "plipe celga'a", "item.minecraft.tipped_arrow.effect.levitation": "fulta celga'a", "item.minecraft.tipped_arrow.effect.luck": "funca celga'a", "item.minecraft.tipped_arrow.effect.mundane": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.night_vision": "manvi'a celga'a", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "vindu celga'a", "item.minecraft.tipped_arrow.effect.regeneration": "ka'orbi'o celga'a", "item.minecraft.tipped_arrow.effect.slow_falling": "snofa'u celga'a", "item.minecraft.tipped_arrow.effect.slowness": "klasno celga'a", "item.minecraft.tipped_arrow.effect.strength": "tsali celga'a", "item.minecraft.tipped_arrow.effect.swiftness": "klasutra celga'a", "item.minecraft.tipped_arrow.effect.thick": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.turtle_master": "cakrespycei celga'a", "item.minecraft.tipped_arrow.effect.water": "sploici celga'a", "item.minecraft.tipped_arrow.effect.water_breathing": "j<PERSON><PERSON><PERSON><PERSON>'u celga'a", "item.minecraft.tipped_arrow.effect.weakness": "ruble celga'a", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "baktu trene joi jbama", "item.minecraft.torchflower_seeds": "fag<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.totem_of_undying": "na<PERSON>ro nindode", "item.minecraft.trader_llama_spawn_egg": "Trader <PERSON>lama Spawn Egg", "item.minecraft.trial_key": "talsa ckiku", "item.minecraft.trident": "ciby<PERSON>'erxa'i", "item.minecraft.tropical_fish": "tropika finpe", "item.minecraft.tropical_fish_bucket": "tropika finpe baktu", "item.minecraft.tropical_fish_spawn_egg": "tropika finpe j<PERSON>'a", "item.minecraft.turtle_helmet": "cakyrespa calku", "item.minecraft.turtle_scute": "cakyrespa piltapla", "item.minecraft.turtle_spawn_egg": "cakyrespa jbeso'a", "item.minecraft.vex_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "cmapalci jbeso'a", "item.minecraft.villager_spawn_egg": "selsosyzda jbeso'a", "item.minecraft.vindicator_spawn_egg": "pacybandu j<PERSON>'a", "item.minecraft.wandering_trader_spawn_egg": "Wandering Trader Spawn Egg", "item.minecraft.ward_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "le visnalka'e cizda'u jbeso'a", "item.minecraft.warped_fungus_on_a_stick": "cizra bo gumri joi grana", "item.minecraft.water_bucket": "d<PERSON><PERSON> baktu", "item.minecraft.wayfinder_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "maxri", "item.minecraft.wheat_seeds": "xritsi", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "blabi xinmo", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "jimyzbatermo'a", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Wind Charge", "item.minecraft.witch_spawn_egg": "termafyfe'i jbeso'a", "item.minecraft.wither_skeleton_spawn_egg": "me la .uiter. bogygreku jbeso'a", "item.minecraft.wither_spawn_egg": "jbeso'a be la .uidr.", "item.minecraft.wolf_armor": "labnybadycalku", "item.minecraft.wolf_spawn_egg": "labno jbeso'a", "item.minecraft.wooden_axe": "mudri ka'amru", "item.minecraft.wooden_hoe": "mudri terplixa", "item.minecraft.wooden_pickaxe": "mudri tunymru", "item.minecraft.wooden_shovel": "mudri canpa", "item.minecraft.wooden_sword": "mudri <PERSON>i", "item.minecraft.writable_book": "penbi joi cukta", "item.minecraft.written_book": "fanmo cukta", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "pelxu xinmo", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "mi<PERSON><PERSON>ro balxarju j<PERSON>'a", "item.minecraft.zombie_horse_spawn_egg": "mi<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.zombie_spawn_egg": "mi<PERSON><PERSON><PERSON> j<PERSON>'a", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> selsosyzda jbes<PERSON>'a", "item.minecraft.zombified_piglin_spawn_egg": "miv<PERSON>ro xajypre jbes<PERSON>'a", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": ".i lo cutne zo'u", "item.modifiers.feet": ".i lo jamfu zo'u", "item.modifiers.hand": "When held:", "item.modifiers.head": ".i lo stedu zo'u", "item.modifiers.legs": ".i lo tuple zo'u", "item.modifiers.mainhand": ".i lo ralxa'e zo'u", "item.modifiers.offhand": ".i lo nalralxa'e zo'u", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s tag(s)", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "nalpopyka'e", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON> bliku", "itemGroup.coloredBlocks": "skari bliku", "itemGroup.combat": "damba tutci", "itemGroup.consumables": "Consumables", "itemGroup.crafting": "Crafting", "itemGroup.foodAndDrink": "cidja joi dja<PERSON>", "itemGroup.functional": "plix<PERSON> bliku", "itemGroup.hotbar": "daigri poi se rejgau", "itemGroup.ingredients": "pagbu", "itemGroup.inventory": "renvi sorcu", "itemGroup.natural": "mi<PERSON><PERSON><PERSON><PERSON> bliku", "itemGroup.op": "admine plixaudai", "itemGroup.redstone": "me la .redyston. bliku", "itemGroup.search": "sisku", "itemGroup.spawnEggs": "jbeso'a", "itemGroup.tools": "tutci joi plix<PERSON>ai", "item_modifier.unknown": "Unknown item modifier: %s", "jigsaw_block.final_state": "lo basti zo'u", "jigsaw_block.generate": "zbasu", "jigsaw_block.joint.aligned": "sir<PERSON>", "jigsaw_block.joint.rollable": "gunro", "jigsaw_block.joint_label": "ter<PERSON>'e co", "jigsaw_block.keep_jigsaws": "lo kamsto zo'u", "jigsaw_block.levels": "%s senta", "jigsaw_block.name": "cmene be ti", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "tercu'a", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "cmene be lo seljo'e", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON>", "key.attack": "darxi", "key.back": "trixe", "key.categories.creative": "Creative Mode", "key.categories.gameplay": "nunkei", "key.categories.inventory": "sorcu", "key.categories.misc": "vrici", "key.categories.movement": "muvdu", "key.categories.multiplayer": "Multiplayer", "key.categories.ui": "selkei krati", "key.chat": "casnu", "key.command": "Open Command", "key.drop": "falcru lo se cuxna", "key.forward": "crane", "key.fullscreen": "stika lo mulvidni c<PERSON>'i", "key.hotbar.1": "sutpliterse'a pa moi", "key.hotbar.2": "sutpliterse'a re moi", "key.hotbar.3": "sutpliterse'a ci moi", "key.hotbar.4": "sutpliterse'a vo moi", "key.hotbar.5": "sutpliterse'a mu moi", "key.hotbar.6": "sutpliterse'a xa moi", "key.hotbar.7": "sutpliterse'a ze moi", "key.hotbar.8": "sutpliterse'a bi moi", "key.hotbar.9": "sutpliterse'a so moi", "key.inventory": "kalri onai ga'orgau selvensro", "key.jump": "plipe", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "la bakspeis.", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "ni'a farsni", "key.keyboard.end": "la .end.", "key.keyboard.enter": "samci'a", "key.keyboard.equal": "=", "key.keyboard.escape": "tekliv", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "la hom.", "key.keyboard.insert": "la .insErt.", "key.keyboard.keypad.0": "noncmalercu'aca'a", "key.keyboard.keypad.1": "pavycmaler<PERSON>'aca'a", "key.keyboard.keypad.2": "re<PERSON><PERSON><PERSON><PERSON><PERSON>'aca'a", "key.keyboard.keypad.3": "cibycmalercu'aca'a", "key.keyboard.keypad.4": "<PERSON><PERSON><PERSON><PERSON><PERSON>'a<PERSON>'a", "key.keyboard.keypad.5": "mum<PERSON><PERSON><PERSON><PERSON><PERSON>'aca'a", "key.keyboard.keypad.6": "xavycmalercu'aca'a", "key.keyboard.keypad.7": "zelcmalercu'aca'a", "key.keyboard.keypad.8": "bibycmalercu'aca'a", "key.keyboard.keypad.9": "sozycmalercu'aca'a", "key.keyboard.keypad.add": "Keypad +", "key.keyboard.keypad.decimal": "Keypad Decimal", "key.keyboard.keypad.divide": "Keypad /", "key.keyboard.keypad.enter": "Keypad Enter", "key.keyboard.keypad.equal": "Keypad =", "key.keyboard.keypad.multiply": "Keypad *", "key.keyboard.keypad.subtract": "Keypad -", "key.keyboard.left": "zunle farsni", "key.keyboard.left.alt": "zunle zo'oi Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "zunle zo'oi Control", "key.keyboard.left.shift": "zunle zo'oi <PERSON>", "key.keyboard.left.win": "zunle zo'oi Win", "key.keyboard.menu": "tercu'a", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "papri ni'a", "key.keyboard.page.up": "papri ga'u", "key.keyboard.pause": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "pritu farsni", "key.keyboard.right.alt": "pritu zo'oi <PERSON>", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "pritu zo'oi Control", "key.keyboard.right.shift": "pritu zo'oi <PERSON>", "key.keyboard.right.win": "pritu zo'oi <PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "canlu", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Not Bound", "key.keyboard.up": "ga'u farsni", "key.keyboard.world.1": "pa moi munje", "key.keyboard.world.2": "re moi munje", "key.left": "zunle", "key.loadToolbarActivator": "sa<PERSON><PERSON><PERSON>i lo daigri", "key.mouse": "batke pe zo'oi %1$s", "key.mouse.left": "zunle batke", "key.mouse.middle": "la midjus. batkes.", "key.mouse.right": "la pritus. batkes.", "key.pickItem": "bliku cuxna", "key.playerlist": "se nunkei liste", "key.quickActions": "Quick Actions", "key.right": "pritu", "key.saveToolbarActivator": "rej<PERSON> lo daigri", "key.screenshot": "Take Screenshot", "key.smoothCamera": "stika lo skina ctata'i", "key.sneak": "s<PERSON>ji cadzu", "key.socialInteractions": "lonu ro'a jikca kei vi'apapri", "key.spectatorOutlines": "camgau lo se nunkei", "key.sprint": "bajra", "key.swapOffhand": "xanbasygau", "key.togglePerspective": "stika lo ctata'i", "key.use": "pilno", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forums", "known_server_link.news": "News", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Status", "known_server_link.support": "Support", "known_server_link.website": "Website", "lanServer.otherPlayers": "se nunkei se stika", "lanServer.port": "Port Number", "lanServer.port.invalid": "Not a valid port.\nLeave the edit box empty or enter a number between 1024 and 65535.", "lanServer.port.invalid.new": "Not a valid port.\nLeave the edit box empty or enter a number between %s and %s.", "lanServer.port.unavailable": "Port not available.\nLeave the edit box empty or enter a different number between 1024 and 65535.", "lanServer.port.unavailable.new": "Port not available.\nLeave the edit box empty or enter a different number between %s and %s.", "lanServer.scanning": "cipcta le diklo se tcana lo ka sazri su'o munje", "lanServer.start": "cfasazri lo selklosamseltcana munje", "lanServer.title": "se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> munje", "language.code": "jbo", "language.name": "la .lojban.", "language.region": "la jbogu'e", "lectern.take_book": "cukta lebna", "loading.progress": "%s%%", "mco.account.privacy.info": ".i tidmau da ra'a la mo,ian. jo'u lo sivni flalu", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.", "mco.account.privacyinfo": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.\n\nIf you have an older Minecraft account (you log in with your username), you need to migrate the account to a Mojang account in order to access Realms.", "mco.account.update": "jaspu kam<PERSON>ne", "mco.activity.noactivity": "No activity for the past %s day(s)", "mco.activity.title": "Player activity", "mco.backup.button.download": "Download Latest", "mco.backup.button.reset": "Reset World", "mco.backup.button.restore": "cirpe'i", "mco.backup.button.upload": "Upload World", "mco.backup.changes.tooltip": "nuncenba", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "Enabled Pack(s)", "mco.backup.entry.gameDifficulty": "Game Difficulty", "mco.backup.entry.gameMode": "Game Mode", "mco.backup.entry.gameServerVersion": "Game Server Version", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Template Name", "mco.backup.entry.undefined": "Undefined Change", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "World Type", "mco.backup.generate.world": "Generate world", "mco.backup.info.title": "Changes From Last Backup", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "This Realm doesn't have any backups currently.", "mco.backup.restoring": "Restoring your Realm", "mco.backup.unknown": "narsel<PERSON><PERSON>o", "mco.brokenworld.download": "kibycpa", "mco.brokenworld.downloaded": "pu kib<PERSON>pa", "mco.brokenworld.message.line1": "Please reset or select another world.", "mco.brokenworld.message.line2": "You can also choose to download the world to singleplayer.", "mco.brokenworld.minigame.title": "This minigame is no longer supported", "mco.brokenworld.nonowner.error": "Please wait for the Realm owner to reset the world", "mco.brokenworld.nonowner.title": "World is out of date", "mco.brokenworld.play": "Play", "mco.brokenworld.reset": "Reset", "mco.brokenworld.title": "Your current world is no longer supported", "mco.client.incompatible.msg.line1": "Your client is not compatible with Realms.", "mco.client.incompatible.msg.line2": "Please use the most recent version of Minecraft.", "mco.client.incompatible.msg.line3": "Realms is not compatible with snapshot versions.", "mco.client.incompatible.title": ".i lo se servero cu tolsarxe", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "Current", "mco.configure.world.activityfeed.disabled": "Player feed temporarily disabled", "mco.configure.world.backup": "World Backups", "mco.configure.world.buttons.activity": "kelci zum<PERSON>u", "mco.configure.world.buttons.close": "Temporarily Close Realm", "mco.configure.world.buttons.delete": "Delete", "mco.configure.world.buttons.done": "Done", "mco.configure.world.buttons.edit": "Tikygau", "mco.configure.world.buttons.invite": "vi'ec<PERSON> le kelci", "mco.configure.world.buttons.moreoptions": "More options", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Reopen Realm", "mco.configure.world.buttons.options": "World Options", "mco.configure.world.buttons.players": "Players", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "kraga'igau lo munje", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Tikygau", "mco.configure.world.buttons.subscription": "Subscription", "mco.configure.world.buttons.switchminigame": "Switch Minigame", "mco.configure.world.close.question.line1": "You can temporarily close your Realm, preventing play while you make adjustments. Open it back up when you're ready. \n\nThis does not cancel your Realms Subscription.", "mco.configure.world.close.question.line2": "Are you sure you want to continue?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "ca ganlo la'oi Realm", "mco.configure.world.commandBlocks": "ziltinbe bliku", "mco.configure.world.delete.button": "vimcu la'oi <PERSON>", "mco.configure.world.delete.question.line1": "Your Realm will be permanently deleted", "mco.configure.world.delete.question.line2": "Are you sure you want to continue?", "mco.configure.world.description": "Realm velski", "mco.configure.world.edit.slot.name": "lo cmene be lo munje", "mco.configure.world.edit.subscreen.adventuremap": "Some settings are disabled since your current world is an adventure", "mco.configure.world.edit.subscreen.experience": "Some settings are disabled since your current world is an experience", "mco.configure.world.edit.subscreen.inspiration": ".i xo'e lo tik<PERSON>gau cu nalselp<PERSON> .i ki'u bo lo do flecu munje cu velfi'i", "mco.configure.world.forceGameMode": "Force Game Mode", "mco.configure.world.invite.narration": "You have %s new invite(s)", "mco.configure.world.invite.profile.name": "cmene", "mco.configure.world.invited": "Invited", "mco.configure.world.invited.number": "Invited (%s)", "mco.configure.world.invites.normal.tooltip": "Normal User", "mco.configure.world.invites.ops.tooltip": "admine", "mco.configure.world.invites.remove.tooltip": "vimcu", "mco.configure.world.leave.question.line1": "If you leave this Realm you won't see it unless you are invited again", "mco.configure.world.leave.question.line2": "Are you sure you want to continue?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "stuzi", "mco.configure.world.minigame": "Current: %s", "mco.configure.world.name": "la'oi Realm cu selme'e", "mco.configure.world.opening": "ca tolga'o la'oi Realm", "mco.configure.world.players.error": ".i lo kelci poi se cmene ti cu na zasti", "mco.configure.world.players.inviting": "Inviting player...", "mco.configure.world.players.title": "Players", "mco.configure.world.pvp": "la'oi PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Your world will be regenerated and your current world will be lost", "mco.configure.world.reset.question.line2": "Are you sure you want to continue?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "You need a custom resource pack to play on this Realm", "mco.configure.world.resourcepack.question.line2": ".i xu do djica kibycpa gi'e kelci", "mco.configure.world.restore.download.question.line1": "The world will be downloaded and added to your single player worlds.", "mco.configure.world.restore.download.question.line2": "Do you want to continue?", "mco.configure.world.restore.question.line1": "Your world will be restored to date '%s' (%s)", "mco.configure.world.restore.question.line2": "Are you sure you want to continue?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Tikygau", "mco.configure.world.slot": "munje %s", "mco.configure.world.slot.empty": "kunti", "mco.configure.world.slot.switch.question.line1": "Your Realm will be switched to another world", "mco.configure.world.slot.switch.question.line2": "Are you sure you want to continue?", "mco.configure.world.slot.tooltip": "Switch to world", "mco.configure.world.slot.tooltip.active": "Join", "mco.configure.world.slot.tooltip.minigame": "Switch to minigame", "mco.configure.world.spawnAnimals": "<PERSON>ra danlu", "mco.configure.world.spawnMonsters": "gutra cizda'u", "mco.configure.world.spawnNPCs": "<PERSON>ra xarju", "mco.configure.world.spawnProtection": "<PERSON>ra nunbandu", "mco.configure.world.spawn_toggle.message": "Turning this option off will remove all existing entities of that type", "mco.configure.world.spawn_toggle.message.npc": "Turning this option off will remove all existing entities of that type, like Villagers", "mco.configure.world.spawn_toggle.title": "Warning!", "mco.configure.world.status": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "djedi", "mco.configure.world.subscription.days": "djedi", "mco.configure.world.subscription.expired": "Expired", "mco.configure.world.subscription.extend": "Extend Subscription", "mco.configure.world.subscription.less_than_a_day": "Less than a day", "mco.configure.world.subscription.month": "masti", "mco.configure.world.subscription.months": "masti", "mco.configure.world.subscription.recurring.daysleft": "Renewed automatically in", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s day(s)", "mco.configure.world.subscription.remaining.months": "%1$s month(s)", "mco.configure.world.subscription.remaining.months.days": "%1$s month(s), %2$s day(s)", "mco.configure.world.subscription.start": "Start Date", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Time Left", "mco.configure.world.subscription.title": "Your Subscription", "mco.configure.world.subscription.unknown": "narselju'o", "mco.configure.world.switch.slot": "finti le munje", "mco.configure.world.switch.slot.subtitle": "This world is empty, choose how to create your world", "mco.configure.world.title": "tcimi'e la'oi Realm", "mco.configure.world.uninvite.player": "Are you sure that you want to uninvite '%s'?", "mco.configure.world.uninvite.question": ".i xu do barli'a ju'o dai", "mco.configure.worlds.title": "munje", "mco.connect.authorizing": "Logging in...", "mco.connect.connecting": "Connecting to the Realm...", "mco.connect.failed": "Failed to connect to the Realm", "mco.connect.region": "Server region: %s", "mco.connect.success": "Done", "mco.create.world": "zbasu", "mco.create.world.error": "You must enter a name!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "ca finti lo munje", "mco.create.world.skip": "to<PERSON><PERSON>'i", "mco.create.world.subtitle": "Optionally, select what world to put on your new Realm", "mco.create.world.wait": "ca finti lo <PERSON>", "mco.download.cancelled": "Download cancelled", "mco.download.confirmation.line1": "The world you are going to download is larger than %s", "mco.download.confirmation.line2": "You won't be able to upload this world to your Realm again", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "lo nu kibycpa cu mulno", "mco.download.downloading": "ca kibycpa", "mco.download.extracting": "tolse'a", "mco.download.failed": "lo nu kibycpa cu selfli", "mco.download.percent": "%s %%", "mco.download.preparing": "bregau lo kibycpa", "mco.download.resourcePack.fail": "Failed to download resource pack!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Downloading Latest World", "mco.error.invalid.session.message": "Please try restarting Minecraft", "mco.error.invalid.session.title": "Invalid Session", "mco.errorMessage.6001": "Client outdated", "mco.errorMessage.6002": "Terms of service not accepted", "mco.errorMessage.6003": "Download limit reached", "mco.errorMessage.6004": "Upload limit reached", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is out of date", "mco.errorMessage.6007": "User in too many Realms", "mco.errorMessage.6008": "Invalid Realm name", "mco.errorMessage.6009": "Invalid Realm description", "mco.errorMessage.connectionFailure": "An error occurred, please try again later.", "mco.errorMessage.generic": "An error occurred: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "An error occurred (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Could not connect to Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Retry operation", "mco.errorMessage.serviceBusy": "le baltutra ca na ka'e se pilno .i troci lo ka ba za pilno le baltutra", "mco.gui.button": "batke", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Invited player %s", "mco.invites.button.accept": "<PERSON><PERSON><PERSON><PERSON>i", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "No pending invites!", "mco.invites.pending": "New invite(s)!", "mco.invites.title": "Pending Invites", "mco.minigame.world.changeButton": "Select Another Minigame", "mco.minigame.world.info.line1": "This will temporarily replace your world with a minigame!", "mco.minigame.world.info.line2": "You can later return to your original world without losing anything.", "mco.minigame.world.noSelection": "Please make a selection", "mco.minigame.world.restore": "Ending Minigame...", "mco.minigame.world.restore.question.line1": "The minigame will end and your Realm will be restored.", "mco.minigame.world.restore.question.line2": "Are you sure you want to continue?", "mco.minigame.world.selected": "Selected Minigame:", "mco.minigame.world.slot.screen.title": "galfi lo munje...", "mco.minigame.world.startButton": "vrabatke", "mco.minigame.world.starting.screen.title": "Starting Minigame...", "mco.minigame.world.stopButton": "End Minigame", "mco.minigame.world.switch.new": "Select another minigame?", "mco.minigame.world.switch.title": "Switch Minigame", "mco.minigame.world.title": "Switch Realm to Minigame", "mco.news": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Open Link", "mco.notification.visitUrl.message.default": "Please visit the link below", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "Question", "mco.reset.world.adventure": "capli'u", "mco.reset.world.experience": "Experiences", "mco.reset.world.generate": "c<PERSON>o munje", "mco.reset.world.inspiration": "velfi'i", "mco.reset.world.resetting.screen.title": "ca kraga'igau lo munje", "mco.reset.world.seed": "Seed (Optional)", "mco.reset.world.template": "World Templates", "mco.reset.world.title": "Reset World", "mco.reset.world.upload": "kibdu'a lo munje", "mco.reset.world.warning": "This will replace the current world of your Realm", "mco.selectServer.buy": "ko terve'u la'oi Realm", "mco.selectServer.close": "ga'orgau", "mco.selectServer.closed": "pu ga'orgau la'oi Realm", "mco.selectServer.closeserver": "ga'orgau la'oi Realm", "mco.selectServer.configure": "tcimi'e la'oi Realm", "mco.selectServer.configureRealm": "Configure Realm", "mco.selectServer.create": "finti la'oi Realm", "mco.selectServer.create.subtitle": "Select what world to put on your new Realm", "mco.selectServer.expired": "Expired Realm", "mco.selectServer.expiredList": "Your subscription has expired", "mco.selectServer.expiredRenew": "ningau", "mco.selectServer.expiredSubscribe": "se<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": ".i do ba'o troci fanmo", "mco.selectServer.expires.day": "Expires in a day", "mco.selectServer.expires.days": "Expires in %s days", "mco.selectServer.expires.soon": "Expires soon", "mco.selectServer.leave": "cliva la'oi Realm", "mco.selectServer.loading": "Loading Realms List", "mco.selectServer.mapOnlySupportedForVersion": "This map is unsupported in %s", "mco.selectServer.minigame": "cmaci'er<PERSON>i", "mco.selectServer.minigameName": "Minigame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Can't play this minigame in %s", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "xatra:", "mco.selectServer.open": "toltelgau la'oi Realm", "mco.selectServer.openserver": "Open Realm", "mco.selectServer.play": "kelci", "mco.selectServer.popup": "Realms is a safe, simple way to enjoy an online Minecraft world with up to ten friends at a time. It supports loads of minigames and plenty of custom worlds! Only the owner of the realm needs to pay.", "mco.selectServer.purchase": "te vecnu le cnino baltutra", "mco.selectServer.trial": ".i ko troci", "mco.selectServer.uninitialized": "Click to start your new Realm!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Realms are now available in Snapshots starting with Snapshot 23w41a. Every Realms subscription comes with a free Snapshot Realm that is separate from your normal Java Realm!", "mco.snapshotRealmsPopup.title": "Realms now available in Snapshots", "mco.snapshotRealmsPopup.urlText": "Learn More", "mco.template.button.publisher": "Publisher", "mco.template.button.select": "plicu'a", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "World template", "mco.template.info.tooltip": "Publisher website", "mco.template.name": "Template", "mco.template.select.failure": "We couldn't retrieve the list of content for this category.\nPlease check your internet connection, or try again later.", "mco.template.select.narrate.authors": ".i se finti la'e zoi zoi. %s .zoi", "mco.template.select.narrate.version": "li %s versiio", "mco.template.select.none": "Oops, it looks like this content category is currently empty.\nPlease check back later for new content, or if you're a creator,\n%s.", "mco.template.select.none.linkTitle": "consider submitting something yourself", "mco.template.title": "World templates", "mco.template.title.minigame": "cmanunkei", "mco.template.trailer.tooltip": "Map trailer", "mco.terms.buttons.agree": "<PERSON><PERSON>", "mco.terms.buttons.disagree": "na tugni", "mco.terms.sentence.1": "mi tugni lo la'o gy. Minecraft Realms .gy.", "mco.terms.sentence.2": "tercru", "mco.terms.title": "tercru sera'a la'oi Realms", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.now": "right now", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.trial.message.line1": "Want to get your own Realm?", "mco.trial.message.line2": "Click here for more info!", "mco.upload.button.name": "kibdu'a", "mco.upload.cancelled": "Upload cancelled", "mco.upload.close.failure": "Could not close your Realm, please try again later", "mco.upload.done": "kib<PERSON>'a mulno", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Upload failed! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "World too big", "mco.upload.hardcore": "Hardcore worlds can't be uploaded!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparing your world", "mco.upload.select.world.none": "No singleplayer worlds found!", "mco.upload.select.world.subtitle": "Please select a singleplayer world to upload", "mco.upload.select.world.title": "Upload World", "mco.upload.size.failure.line1": ".i la'oi %s bradu'e", "mco.upload.size.failure.line2": "It is %s. The maximum allowed size is %s.", "mco.upload.uploading": "kibdu'a lu %s li'u", "mco.upload.verifying": "Verifying your world", "mco.version": "Version: %s", "mco.warning": "Warning!", "mco.worldSlot.minigame": "Minigame", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "sisti lo nunjo'e", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "selkei tercu'a", "menu.modded": " (to te galfi toi)", "menu.multiplayer": "kansa se nunkei", "menu.online": "lai Minecraft Realms ku", "menu.options": "tikygau", "menu.paused": ".i lo selkei cu cando", "menu.playdemo": "kelci lo mupli munje", "menu.playerReporting": "lo dracykei ca ternoi", "menu.preparingSpawn": "tu'a lo nu bregau lo spaune tumla cu klani li %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "sisti lo nu kelci", "menu.reportBugs": "ternoi lo srera", "menu.resetdemo": "ninga'igau lo mupli munje", "menu.returnToGame": "xruti fi lo nunkei", "menu.returnToMenu": "rejgau .i je xruti fi lo ralju liste", "menu.savingChunks": ".i rejgau lo mu'espi", "menu.savingLevel": ". i le munje ku jmaji", "menu.sendFeedback": "dunda lo te spuda", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "sa<PERSON><PERSON><PERSON><PERSON><PERSON> kargau", "menu.singleplayer": "pa kunkakpa", "menu.working": ".i ca'o gunka", "merchant.deprecated": "Villagers restock up to two times per day.", "merchant.level.1": "lo ninpre", "merchant.level.2": "lo tadyseljibri", "merchant.level.3": "li'upre", "merchant.level.4": "kaktcepre", "merchant.level.5": "ctupre", "merchant.title": "%s - %s", "merchant.trades": "nunve'u", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": ".i co'u selma'e te'a'a %1$s", "multiplayer.applyingPack": "Applying resource pack", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Authentication servers are down. Please try again later, sorry!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": ".i do to<PERSON><PERSON><PERSON><PERSON> lo ti kibyse'u", "multiplayer.disconnect.banned.expiration": ".i lo nu do tolsel<PERSON>ru cu ba selvi'u vi %s", "multiplayer.disconnect.banned.reason": ".i do tolsel<PERSON>ru lo ti samci'ejudri ki'u %s", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned_ip.reason": ".i lo do diklo be zo'oi IP cu seltolcru lo ti samci'ejudri .i ki'u bo %s", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "You logged in from another location", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": ".i lo nu vofli cu nals<PERSON>gau ne'i lo kibyse'u", "multiplayer.disconnect.generic": "lo nunjo'e fanmo", "multiplayer.disconnect.idling": "You have been idle for too long!", "multiplayer.disconnect.illegal_characters": "Illegal characters in chat", "multiplayer.disconnect.incompatible": "Incompatible client! Please use %s", "multiplayer.disconnect.invalid_entity_attacked": "Attempting to attack an invalid entity", "multiplayer.disconnect.invalid_packet": "Server sent an invalid packet", "multiplayer.disconnect.invalid_player_data": "Invalid player data", "multiplayer.disconnect.invalid_player_movement": "Invalid move player packet received", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Invalid move vehicle packet received", "multiplayer.disconnect.ip_banned": ".i lo do judrnporte cu jai tols<PERSON>cru lo ti kibyse'u", "multiplayer.disconnect.kicked": "do se vimcu lo admine lo kibyse'u", "multiplayer.disconnect.missing_tags": ".i lo tcita poi se benji fo lo servero cu to'e mulno .i .e'u do tavla lo sazri be le servero", "multiplayer.disconnect.name_taken": ".i lo cmene cu selpli", "multiplayer.disconnect.not_whitelisted": "do na ka'e pilno le selfu tcana", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": ".i lo samci'ejudri cu selclu .o'i", "multiplayer.disconnect.server_shutdown": ".i lo kibyse'u cu se ganlo", "multiplayer.disconnect.slow_login": "Took too long to log in", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Unexpected custom data from client", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Failed to verify username!", "multiplayer.downloadingStats": ".i cpacu lo kacydatni", "multiplayer.downloadingTerrain": ".i tumla samymo'i", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": ".i fliba lo nu benji le se cusku be do be'o (to ko cipcta lo servero plivei toi) no'u zo'oi %s", "multiplayer.player.joined": "la'oi %s co'a kansa", "multiplayer.player.joined.renamed": "la'oi %s noi pu se cmene zo'oi %s co'a kansa", "multiplayer.player.left": "la'oi %s cu sisti lo nu kelci", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Online players: %s", "multiplayer.requiredTexturePrompt.disconnect": "Server requires a custom resource pack", "multiplayer.requiredTexturePrompt.line1": "This server requires the use of a custom resource pack.", "multiplayer.requiredTexturePrompt.line2": "Rejecting this custom resource pack will disconnect you from this server.", "multiplayer.socialInteractions.not_available": "Social Interactions are only available in Multiplayer worlds", "multiplayer.status.and_more": "... and %s more ...", "multiplayer.status.cancelled": "Cancelled", "multiplayer.status.cannot_connect": "na kakne lonu co'a jorne lo samci'ejudri", "multiplayer.status.cannot_resolve": "Can't resolve hostname", "multiplayer.status.finished": "mulno", "multiplayer.status.incompatible": "ca to'ebo tugni fav<PERSON> .a'e", "multiplayer.status.motd.narration": "Message of the day: %s", "multiplayer.status.no_connection": "na kakne jorne", "multiplayer.status.old": "<PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "samrpingu itca", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s out of %s players online", "multiplayer.status.quitting": ".i cliva", "multiplayer.status.request_handled": ".i jitro lo t<PERSON>ynoi velcpe", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Received unrequested status", "multiplayer.status.version.narration": "Server version: %s", "multiplayer.stopSleeping": "cliva le ckana", "multiplayer.texturePrompt.failure.line1": "Server resource pack couldn't be applied", "multiplayer.texturePrompt.failure.line2": "Any functionality that requires custom resources might not work as expected", "multiplayer.texturePrompt.line1": "le samci'ejudri cu stidi tu'a lo sevzi ke sefta pixra gunma", "multiplayer.texturePrompt.line2": "xu do djica lonu kiby<PERSON>pa gi'e minji samganzu", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMessage from server:\n%s", "multiplayer.title": ".i .e'o ko kansa se nunkei", "multiplayer.unsecureserver.toast": "Messages sent on this server may be modified and might not reflect the original message", "multiplayer.unsecureserver.toast.title": "Chat messages can't be verified", "multiplayerWarning.check": ".i ro re'u jarco di'u", "multiplayerWarning.header": ".o'i", "multiplayerWarning.message": ".i lo samseltcana nunkei cu se friti lo servero poi na se ponse ja se sazri ja se catlu la .mojang. ja la .mikrosoft. .i ca lo samseltcana nunkei cu cumki fa lo nu do viska lo nu samta'a ku poi na se cipcta ku'o ja lo drata ganvi poi se finti lo kelci je poi na mapti ro da", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "batke zei uidje %s", "narration.button.usage.focused": "danre la'e linji bu", "narration.button.usage.hovered": "Lo zunle klik mu'i lo nu akti", "narration.checkbox": "Checkbox: %s", "narration.checkbox.usage.focused": "Ko danre la'oi Enter mu'i lo nu akti", "narration.checkbox.usage.hovered": "La zunle klik mu'i lo nu akti", "narration.component_list.usage": "Press Tab to navigate to next element", "narration.cycle_button.usage.focused": "Ko danre lo nerkla mu'i lo nu basti %s", "narration.cycle_button.usage.hovered": "Lo zunle klika tezu'e lo nu basti %s", "narration.edit_box": "Edit box: %s", "narration.item": "Item: %s", "narration.recipe": "algoritma %s", "narration.recipe.usage": "Lo zunle klik mu'i lo nu cuxna", "narration.recipe.usage.more": "Right click to show more recipes", "narration.selection.usage": "Press up and down buttons to move to another entry", "narration.slider.usage.focused": "Press left or right keyboard buttons to change value", "narration.slider.usage.hovered": "Drag slider to change value", "narration.suggestion": ".i cuxna lo %s moi be %s selti'i be'o no'u lo'u %s le'u", "narration.suggestion.tooltip": ".i cuxna lo %s moi be %s selti'i be'o no'u lo'u %s le'u (to lo'u %s le'u toi)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "lo se joska'e", "narrator.button.difficulty_lock": "nilnandu telgau", "narrator.button.difficulty_lock.locked": "se telgau", "narrator.button.difficulty_lock.unlocked": "nalseltelgaurbi'o", "narrator.button.language": "bangu", "narrator.controls.bound": "%s cu te batke fi %s", "narrator.controls.reset": ".i kraga'igau lo batke be fi %s", "narrator.controls.unbound": "no batke be fi %s", "narrator.joining": "ba zi jorne", "narrator.loading": "%s va'e lo samymo'i", "narrator.loading.done": "rejgau gi'e xruti", "narrator.position.list": "Lo selcu'e liste %s na %s", "narrator.position.object_list": "Selected row element %s out of %s", "narrator.position.screen": "Vidni elementu %s na %s", "narrator.position.tab": "Selected tab %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "xruti fi le ralju liste", "narrator.screen.usage": "Ko pilno lo smacu cursi gi lo Tab batke lo nu cuxna lo element", "narrator.select": "cuxna %s", "narrator.select.world": "Catlu %s, romoi se zgipli: %s, %s, %s, verzi: %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "Narrator Disabled", "narrator.toast.enabled": "Narrator Enabled", "optimizeWorld.confirm.description": "This will attempt to optimize your world by making sure all data is stored in the most recent game format. This can take a very long time, depending on your world. Once done, your world may play faster but will no longer be compatible with older versions of the game. Are you sure you wish to proceed?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "xagzengau lo munje samymo'i", "optimizeWorld.info.converted": "pu xauzmazba loi bliku: %s", "optimizeWorld.info.skipped": "loinu pu prubi'o bliku: %s", "optimizeWorld.info.total": "Total chunks: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "ca kancu loi bliku", "optimizeWorld.stage.failed": ".i fliba fau'u", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Optimizing World '%s'", "options.accessibility": "Accessibility Settings...", "options.accessibility.high_contrast": "High Contrast", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available.", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "lo ka jonse kei gidva", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "Text Background", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "vi ro da", "options.accessibility.text_background_opacity": "Text Background Opacity", "options.accessibility.title": "Accessibility Settings", "options.allowServerListing": "Allow Server Listings", "options.allowServerListing.tooltip": "Servers may list online players as part of their public status.\nWith this option off your name will not show up in such lists.", "options.ao": "lo xutla gusni ku", "options.ao.max": "nacyzmarai", "options.ao.min": "nacmecrai", "options.ao.off": "OFF", "options.attack.crosshair": "lo fargau", "options.attack.hotbar": "sutpliterse'a", "options.attackIndicator": "jarco be tu'a lo darxi", "options.audioDevice": "cabra", "options.audioDevice.default": "ciste tozbra", "options.autoJump": "zmiku plipe", "options.autoSuggestCommands": "ziltinbe selti'i", "options.autosaveIndicator": "jarco be tu'a lo zmirejgau", "options.biomeBlendRadius": "ni rarvanbi mixre", "options.biomeBlendRadius.1": "1 (to so'u toi)", "options.biomeBlendRadius.11": "11 (to so'a toi)", "options.biomeBlendRadius.13": "13 (to da'a toi)", "options.biomeBlendRadius.15": "15 (to ro toi)", "options.biomeBlendRadius.3": "3 (to so'o toi)", "options.biomeBlendRadius.5": "5 (to no'o toi)", "options.biomeBlendRadius.7": "7 (to so'i toi)", "options.biomeBlendRadius.9": "9 (to so'e toi)", "options.chat": "Cha<PERSON>s...", "options.chat.color": "skari", "options.chat.delay": "lo nilde'a zo'u snidu li %s", "options.chat.delay_none": "lo nilde'a zo'u snidu li no", "options.chat.height.focused": "Focused Height", "options.chat.height.unfocused": "Unfocused Height", "options.chat.line_spacing": "lo lerpinsle nilsei", "options.chat.links": "urli", "options.chat.links.prompt": "Prompt on Links", "options.chat.opacity": "Chat Text Opacity", "options.chat.scale": "lo lerfu nilbra", "options.chat.title": "lo samta'a cu selstika", "options.chat.visibility": "samta'a", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "Hidden", "options.chat.visibility.system": "Commands Only", "options.chat.width": "gantre", "options.chunks": "%s lo mu'espi", "options.clouds.fancy": "Fancy", "options.clouds.fast": "Fast", "options.controls": "batke maty<PERSON>", "options.credits_and_attribution": "Credits & Attribution...", "options.damageTiltStrength": "selxai selsa'o", "options.damageTiltStrength.tooltip": ".i ni lo kacma cu desku lo nu selxai", "options.darkMojangStudiosBackgroundColor": "<PERSON><PERSON><PERSON> joi blabi ka<PERSON>ni", "options.darkMojangStudiosBackgroundColor.tooltip": ".i jei xekri ti'a lo samymo'i pixra pe zoi zy. Mojang Studios zy.", "options.darknessEffectScale": "Darkness Pulsing", "options.darknessEffectScale.tooltip": "Controls how much the Darkness effect pulses when a Warden or Sculk Shrieker gives it to you.", "options.difficulty": "lo nunkei ku", "options.difficulty.easy": "sampu", "options.difficulty.easy.info": "lo danlu poi bradi cu jbena gi'eku'i xrani fo lo me'ino'o klani .i xaksu lo ni tolxagji .ije ri'a lo nu go'i cu jdika lo ni kanro ku ji'e mu rinsita", "options.difficulty.hard": "nandu", "options.difficulty.hard.info": "lo danlu poi bradi cu jbena gi'e xrani fo lo za'uno'o klani .i xaksu lo ni tolxagji .ije ri'a lo nu go'i cu mulno jdika lo ni kanro", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "lo danlu poi bradi cu jbena gi'e xrani fo lo no'o klani .i xaksu lo ni tolxagji .ije ri'a lo nu go'i cu jdika lo ni kanro ku ji'e lo xadba be lo rinsita", "options.difficulty.online": "samtcise'u fu'inai", "options.difficulty.peaceful": "<PERSON><PERSON>", "options.difficulty.peaceful.info": "ge lo danlu poi bradi na jbena gi lo danlu poi nutli zo'u cmima lo so'o klesi gi'o jbena .i na xaksu lo ni tolxagji .ije lo ni kanro cu tisna fau lo nu lo temci cu zenba", "options.directionalAudio": "selfa'a sance", "options.directionalAudio.off.tooltip": "lo klasiko selsnapra sance", "options.directionalAudio.on.tooltip": "Uses HRTF-based directional audio to improve the simulation of 3D sound. Requires HRTF compatible audio hardware, and is best experienced with headphones.", "options.discrete_mouse_scroll": "Discret<PERSON>ing", "options.entityDistanceScaling": "lo dzadza nilda'o", "options.entityShadows": "ctino dacti", "options.font": "Font Settings...", "options.font.title": "Font Settings", "options.forceUnicodeFont": "bapli tu'a lo ce'a zei ciste pe la'oi Unicode", "options.fov": "viska co", "options.fov.max": "darmau", "options.fov.min": "Normal", "options.fovEffectScale": "lo jvinu nilcne", "options.fovEffectScale.tooltip": "Controls how much the field of view can change with gameplay effects.", "options.framerate": "%s fps", "options.framerateLimit": "melbi", "options.framerateLimit.max": "rau", "options.fullscreen": "vidnyselclu", "options.fullscreen.current": "cabna", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Fullscreen Resolution", "options.fullscreen.unavailable": "Setting unavailable", "options.gamma": "vidni co", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "carmi", "options.gamma.min": "kandi", "options.generic_value": "%s: %s", "options.glintSpeed": "makfa kamcai nilsutra", "options.glintSpeed.tooltip": ".i stika lo te dikni be lo ni carmi bei lo dacti poi se makfa", "options.glintStrength": "makfa kamcai ni<PERSON>a", "options.glintStrength.tooltip": ".i stika lo ni lo dacti poi se makfa cu carmi", "options.graphics": "lo nu terxra ku", "options.graphics.fabulous": "zabna", "options.graphics.fabulous.tooltip": ".i lo %s ke samxra tadji cu pilno lo xraproga lo nu jarco lo tcima je lo dilnu je lo kantu vu'o poi trixe lo guskli bliku\n.i la'e di'u ka'e carmi jdikygau lo kamsutra va'o lo selbevyskami je lo barda vidni", "options.graphics.fancy": "melbi", "options.graphics.fancy.tooltip": ".i lo melbi ke samxra tadji cu lanxe lo kamsutra jo'u lo kamymle va'o so'a skami\n.i lo tcima je lo dilnu je lo kantu ka'e canci ti'a lo guskli bliku", "options.graphics.fast": "sutra", "options.graphics.fast.tooltip": ".i lo sutra ke samxra tadji cu jarco me'i jacycarvi ja snimycarvi\n.i ji'a so'o bliku mu'u lo pezli na guskli", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Take Me Back", "options.graphics.warning.message": ".i ganse lo du'u lo do xraskami na sarxe lo %s ke samxra tadji\n\n.i .e'a do toljundi la'e di'u .i ku'i mi'a ka'e nai sarji do fau lo nu do se nabmi ra'a lo %s samxra", "options.graphics.warning.renderer": ".i cmene lo selga'e xrapra fa zo'oi %s", "options.graphics.warning.title": "na sarji lo do xraskami", "options.graphics.warning.vendor": ".i cmene lo selga'e vecnu fa zo'oi %s", "options.graphics.warning.version": ".i cmene lo selga'e ke .opengl. versiio fa zo'oi %s", "options.guiScale": "lo sazycimde ku", "options.guiScale.auto": "zmiku cinsi", "options.hidden": "Hidden", "options.hideLightningFlashes": "lindi poj<PERSON>'i fanta", "options.hideLightningFlashes.tooltip": ".i jei fanta lo nu gusni lo tsani lo lindi .i viska ba'e lo lindi .iju go'e", "options.hideMatchedNames": "Hide Matched Names", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "le fatne samxa'e ku", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "ru'i ku", "options.key.toggle": "co'i ku", "options.language": "bangu tikygau", "options.language.title": "bangu", "options.languageAccuracyWarning": ".i to cumki fa lo nu lo xe fanva ku na drani kei ku .u'u", "options.languageWarning": ".i cumki fa lo nu lo xe fanva ku na drani kei ku .u'u", "options.mainHand": "ralju xance", "options.mainHand.left": "zunle", "options.mainHand.right": "pritu", "options.mipmapLevels": "Mipmap Levels", "options.modelPart.cape": "b<PERSON><PERSON><PERSON>", "options.modelPart.hat": "sedy<PERSON>'u", "options.modelPart.jacket": "barcreka", "options.modelPart.left_pants_leg": "zunle paktu'e", "options.modelPart.left_sleeve": "zunle birtafpau", "options.modelPart.right_pants_leg": "pritu paktu'e", "options.modelPart.right_sleeve": "pritu birtafpau", "options.mouseWheelSensitivity": "Scroll Sensitivity", "options.mouse_settings": "lo pevysmacu gaftercu'a", "options.mouse_settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.multiplayer.title": "Multiplayer Settings...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.all": "bacru ro da", "options.narrator.chat": "bacru lo se samsku", "options.narrator.notavailable": "na jai cumki", "options.narrator.off": "OFF", "options.narrator.system": "bacru lo notci", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "na selpli", "options.off.composed": "%s zo'u itka", "options.on": "selpli", "options.on.composed": "%s zo'u akti", "options.online": "xei'e.", "options.online.title": "Online Options", "options.onlyShowSecureChat": "Only Show Secure Chat", "options.onlyShowSecureChat.tooltip": "Only display messages from other players that can be verified to have been sent by that player, and have not been modified.", "options.operatorItemsTab": "Operator Items Tab", "options.particles": ".i pilno", "options.particles.all": "All", "options.particles.decreased": "so'o kantu", "options.particles.minimal": "so'u kantu", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "lo zbasu spisa mivmu'e", "options.prioritizeChunkUpdates.byPlayer": "Semi Blocking", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Some actions within a chunk will recompile the chunk immediately. This includes block placing & destroying.", "options.prioritizeChunkUpdates.nearby": "Fully Blocking", "options.prioritizeChunkUpdates.nearby.tooltip": "Nearby chunks are always compiled immediately. This may impact game performance when blocks are placed or destroyed.", "options.prioritizeChunkUpdates.none": "Threaded", "options.prioritizeChunkUpdates.none.tooltip": "Nearby chunks are compiled in parallel threads. This may result in brief visual holes when blocks are destroyed.", "options.rawMouseInput": "lo krasi selru'e", "options.realmsNotifications": "Realms News & Invites", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "Reduced Debug Info", "options.renderClouds": "lo dilnu ku", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "terxra co", "options.resourcepack": "xrag<PERSON>", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "lo sarlu nilcne", "options.screenEffectScale.tooltip": ".i ni lo vidni cu sarlybi'o va'o lo nu vatydji je lo nu pilno lo mafyvro\n.i lo cmana'u zo'u lo nu sarlu ku pe lo nu vatydji cu se basti lo nu crino", "options.sensitivity": "frati co", "options.sensitivity.max": "gusni sutra cai", "options.sensitivity.min": "snotce", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "snaselci'a jarco", "options.simulationDistance": "sa<PERSON><PERSON>'i lo fukpi mivmu'e", "options.skinCustomisation": "stika do dracykei", "options.skinCustomisation.title": "stika do dracykei", "options.sounds": "loi zgike joi loi sance", "options.sounds.title": "sance se stika", "options.telemetry": "Telemetry Data...", "options.telemetry.button": "Data Collection", "options.telemetry.button.tooltip": "\"%s\" includes only the required data.\n\"%s\" includes optional, as well as the required data.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "All", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "None", "options.title": "loi te cuxna", "options.touchscreen": "Touchscreen Mode", "options.video": "vidni tikygau", "options.videoTitle": ".i vidni tikygau", "options.viewBobbing": "lo nu kacma stapa ku", "options.visible": "se viska", "options.vsync": "la'o .VSync", "outOfMemory.message": "Minecraft has run out of memory.\n\nThis could be caused by a bug in the game or by the Java Virtual Machine not being allocated enough memory.\n\nTo prevent world corruption, the current game has quit. We've tried to free up enough memory to let you go back to the main menu and back to playing, but this may not have worked.\n\nPlease restart the game if you see this message again.", "outOfMemory.title": "na mo'isro", "pack.available.title": "se<PERSON><PERSON><PERSON>", "pack.copyFailure": ".i fliba lo nu fukpra", "pack.dropConfirm": ".i xu do djica lo nu jmina lo ti ganvygunma", "pack.dropInfo": ".i .e'u do klikmuga lo datnyveimei lo ni'a canko", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(to punji lo datnyvei ra toi)", "pack.incompatible": "na sarxe", "pack.incompatible.confirm.new": ".i le ti ganvygunma cu se finti fi lo ninmau versiio je ja'e bo ka'e spofu ci'e lo dei versiio", "pack.incompatible.confirm.old": ".i le ti ganvygunma cu se finti fi lo laldymau versiio je ja'e bo ka'e spofu ci'e lo dei versiio", "pack.incompatible.confirm.title": ".i xu do birti lo du'u do djica lo nu pilno ti", "pack.incompatible.new": "to lo cnino zmadu selkei cu jai te pilno toi", "pack.incompatible.old": "to lo tolni'o zmadu selkei cu jai te pilno toi", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "kargau lo datnyveimei", "pack.selected.title": "selcu'a", "pack.source.builtin": "jic<PERSON>", "pack.source.feature": "feature", "pack.source.local": "di<PERSON>lo", "pack.source.server": "samt<PERSON>'u", "pack.source.world": "world", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Random variant", "parsing.bool.expected": "Expected boolean", "parsing.bool.invalid": "Invalid boolean, expected 'true' or 'false' but found '%s'", "parsing.double.expected": "Expected double", "parsing.double.invalid": "Invalid double '%s'", "parsing.expected": "Expected '%s'", "parsing.float.expected": "Expected float", "parsing.float.invalid": "Invalid float '%s'", "parsing.int.expected": "Expected integer", "parsing.int.invalid": "Invalid integer '%s'", "parsing.long.expected": "Expected long", "parsing.long.invalid": "Invalid long '%s'", "parsing.quote.escape": "Invalid escape sequence '\\%s' in quoted string", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Unknown particle: %s", "permissions.requires.entity": "An entity is required to run this command here", "permissions.requires.player": "A player is required to run this command here", "potion.potency.1": "xire", "potion.potency.2": "xici", "potion.potency.3": "se vomoi", "potion.potency.4": "se mumoi", "potion.potency.5": "se xamoi", "potion.whenDrank": ".i ba nunri'a", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": ".i zo'oi %s cmene no bridi", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms is currently not supported in snapshots", "recipe.notFound": "Unknown recipe: %s", "recipe.toast.description": ".i ko cipcta lo do zbatadji cukta", "recipe.toast.title": ".i lo cnino zbatadji cu se telcaugau", "record.nowPlaying": ".i ca selzgi %s", "recover_world.bug_tracker": "Report a Bug", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "narselju'o", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "BROKEN ASSETS DETECTED", "resourcePack.high_contrast.name": "High Contrast", "resourcePack.load_fail": ".i fliba lo nu rapcpa lo ganvi", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "World Specific Resources", "resourcePack.title": "cuxna lo xragri", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Downloading Resource Pack", "resourcepack.progress": "Downloading file (%s MB)...", "resourcepack.requesting": "Making Request...", "screenshot.failure": ".i na ca'o rejgau lo vidnyxra be se me'e %s", "screenshot.success": ".i ca'o rejgau lo vidnyxra be se me'e %s", "selectServer.add": "sam<PERSON>'<PERSON><PERSON><PERSON><PERSON> jmina", "selectServer.defaultName": "sam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.delete": "Delete", "selectServer.deleteButton": "Delete", "selectServer.deleteQuestion": ".i xu do birti lo du'u do djica lo nu vimcu le se cuxna samci'ejudri po'u la", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "sampu jongau", "selectServer.edit": "Edit", "selectServer.hiddenAddress": "sivni", "selectServer.refresh": "refkibycpa", "selectServer.select": "j<PERSON><PERSON>", "selectWorld.access_failure": ".i fliba lo nu jonse le munje", "selectWorld.allowCommands": "lo ka toltinbe", "selectWorld.allowCommands.info": ".i mu'a ziltinbe fa zo'oi /gamemode .e zo'oi /experience", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "<PERSON>se Cached Data", "selectWorld.backupJoinConfirmButton": "fukpra je ba bo samy<PERSON>'i", "selectWorld.backupJoinSkipButton": "mi djuno ledu'u mi ca gasnu makau", "selectWorld.backupQuestion.customized": ".i lo zifyjdimu'e ku ba nalselpli", "selectWorld.backupQuestion.downgrade": "Downgrading a world is not supported", "selectWorld.backupQuestion.experimental": ".i lo nalca'i ke mujyzba selcu'a pu'o se sarji", "selectWorld.backupQuestion.snapshot": "Do you really want to load this world?", "selectWorld.backupWarning.customized": ".i fu'aunai mi'a nalpli lo zifyjdimu'e va'o lo ti versiio pe la'oi Minecraft .i mi'a ka'e samymo'i lo ti munje gi'e rejgau ro da .i ku'i ro lo nintu'a ku ba na'e zifseljdi .i fau'u sai ti nalxau", "selectWorld.backupWarning.downgrade": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work. If you still want to continue, please make a backup.", "selectWorld.backupWarning.experimental": ".i .o'i le ti munje cu pilno lo mujyzba selcu'a poi ca nalca'i je poi ka'e suksa spofu .i mi'a na nupre lo du'u snada lo nu samymo'i ja lo nu mujyzba", "selectWorld.backupWarning.snapshot": "This world was last played in version %s; you are on version %s. Please make a backup in case you experience world corruptions.", "selectWorld.bonusItems": "lo zilminxagvau", "selectWorld.cheats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.commands": "Commands", "selectWorld.conversion": ".i bilga lo nu binxo", "selectWorld.conversion.tooltip": "This world must be opened in an older version (like 1.6.4) to be safely converted", "selectWorld.create": "finti le munje", "selectWorld.customizeType": "tikygau", "selectWorld.dataPacks": "da<PERSON><PERSON><PERSON><PERSON>", "selectWorld.data_read": ".i tcidu lo munje datni", "selectWorld.delete": "vimcu", "selectWorld.deleteButton": "Delete", "selectWorld.deleteQuestion": ".i xu do birti lo du'u do djica lo nu vimcu lo se cuxna munje", "selectWorld.deleteWarning": ".i la'oi %s ba ze'u canci .o'i", "selectWorld.delete_failure": ".i fliba lo nu vimcu le munje", "selectWorld.edit": "tikygau", "selectWorld.edit.backup": ".i nurfu'izba", "selectWorld.edit.backupCreated": ".i nurfu'i fa %s", "selectWorld.edit.backupFailed": "Backup failed", "selectWorld.edit.backupFolder": ".i fargau lo snufu'i datnyveimei", "selectWorld.edit.backupSize": ".i nilbra fa %s fi la'oi MB", "selectWorld.edit.export_worldgen_settings": "barbe'i lo mujyzba selcu'a", "selectWorld.edit.export_worldgen_settings.failure": ".i fliba lo nu barbe'i", "selectWorld.edit.export_worldgen_settings.success": ".i mo'u barbe'i", "selectWorld.edit.openFolder": ".i kargau lo munje datnyveimei", "selectWorld.edit.optimize": "Optimize World", "selectWorld.edit.resetIcon": "kraga'igau lo pixra", "selectWorld.edit.save": "rejgau", "selectWorld.edit.title": "tikygau lo munje", "selectWorld.enterName": "le munje ku se cmene zoi zy.", "selectWorld.enterSeed": "ma poi pevytsi ku'o terzba lo munje", "selectWorld.experimental": "te cipra", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": ".i .u'u zilsre lo nu troci lo ka samymo'i lo munje be ci'e lo ninmau .i nu bu na'o ka'e se srera", "selectWorld.futureworld.error.title": "lo srera cu fasnu", "selectWorld.gameMode": "nunkei co", "selectWorld.gameMode.adventure": "litru", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": ".i se jicmu lo revytadji .i ku'i do na", "selectWorld.gameMode.adventure.line2": "na curmi lonu galfi lo bliku", "selectWorld.gameMode.creative": "<PERSON><PERSON>", "selectWorld.gameMode.creative.info": "Create, build, and explore without limits. You can fly, have endless materials, and can't be hurt by monsters.", "selectWorld.gameMode.creative.line1": ".i le te zbasu ku cimni .ije do vofli kakne", "selectWorld.gameMode.creative.line2": ".ije li no lo'o tcika lo nu bliku vimcu", "selectWorld.gameMode.hardcore": "ckape .ii", "selectWorld.gameMode.hardcore.info": "Survival Mode locked to 'Hard' difficulty. You can't respawn if you die.", "selectWorld.gameMode.hardcore.line1": ".i se jicmu lo revytadji je cu ku'i stodi", "selectWorld.gameMode.hardcore.line2": ".ije lo nu mrobi'o kei ku vitno", "selectWorld.gameMode.spectator": "mencti", "selectWorld.gameMode.spectator.info": "You can look but don't touch.", "selectWorld.gameMode.spectator.line1": "do ka'e ku catlu gi'e nai galfi", "selectWorld.gameMode.survival": "renvi", "selectWorld.gameMode.survival.info": "Explore a mysterious world where you build, collect, craft, and fight monsters.", "selectWorld.gameMode.survival.line1": ".i sisku tu'a lo dacti je cu kurji", "selectWorld.gameMode.survival.line2": ".ije zenba lo kelrau .ije mrobi'o kakne", "selectWorld.gameRules": "javni", "selectWorld.import_worldgen_settings": "nerbe'i lo selcu'a", "selectWorld.import_worldgen_settings.failure": ".i zilsre lo nu nerbe'i lo selcu'a", "selectWorld.import_worldgen_settings.select_file": ".i ko cuxna lo zo'oi .json datnyvei", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "fi'e lo tolmapti vercone", "selectWorld.load_folder_access": "Unable to read or access folder where game worlds are saved!", "selectWorld.loading_list": "Loading World List", "selectWorld.locked": "xa'o se jitro lo drata fukpi be le proga", "selectWorld.mapFeatures": "lo rutni stura", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "lo terdi klesi", "selectWorld.mapType.normal": "fadni", "selectWorld.moreWorldOptions": "drata selcu'a", "selectWorld.newWorld": "c<PERSON>o munje", "selectWorld.recreate": "za'u<PERSON>'<PERSON> finti", "selectWorld.recreate.customized.text": ".i lo zifyjdimu'e ku ba nalselpli va'o lo ti versiio pe la'oi Minecraft .i mi'a ka'e rapfi'i ru se pi'o lo mitytsi .e lo mityzilkai .i ku'i ro lo zifyseljditu'a ku ba canci .i fau'u sai ti nalxau", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": ".i zo'e srebi'o ca lonu munje rapfi'i troci", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": ".i ca'o bregau lo djukra", "selectWorld.resultFolder": ".zy. noi cmene la", "selectWorld.search": "ko mitysisku te ro lo plini", "selectWorld.seedInfo": ".i cliva lo nalselci'a mu'i lo cuntsi", "selectWorld.select": "kelci do'e le se cuxna munje", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": ".i ko cuxna fi lo munje", "selectWorld.tooltip.fromNewerVersion1": ".i lo proga poi ni'omau cu pu rejgau lo munje", "selectWorld.tooltip.fromNewerVersion2": "gi'e lo nabmi cu cumki lo nu to'e rejgau", "selectWorld.tooltip.snapshot1": "ko na tolmo'i lo ka snura rejgau le ti munje", "selectWorld.tooltip.snapshot2": "kei pu lo nu cpacu do'e ti poi cipra panra", "selectWorld.unable_to_load": "munje c<PERSON>cu na<PERSON>'e", "selectWorld.version": "versiio", "selectWorld.versionJoinButton": "cpacu .u roda", "selectWorld.versionQuestion": ".ai pei cpacu lo ti munje", "selectWorld.versionUnknown": "narselju'o", "selectWorld.versionWarning": ".i lo ti munje cu selkei fi'o %s versiio .ije lo nu cpacu ri cu la'a rinka lo nunpo'u .o'i", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Warning! These settings are using experimental features", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "munje", "sign.edit": "ciska fi lo lertapla", "sleep.not_possible": "No amount of rest can pass this night", "sleep.players_sleeping": "%s/%s players sleeping", "sleep.skipping_night": "sipna ca lo nicte", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Unknown slot '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "savru ja vanbi", "soundCategory.block": "bliku", "soundCategory.hostile": "le'onmo cizda'u", "soundCategory.master": "ralju nil<PERSON>u", "soundCategory.music": "zgike tikygau", "soundCategory.neutral": "intima cizda'u", "soundCategory.player": "kelci", "soundCategory.record": "cukla ve vreji ja zgike tanxe", "soundCategory.ui": "UI", "soundCategory.voice": "voksa", "soundCategory.weather": "tcima", "spectatorMenu.close": "li'avro", "spectatorMenu.next_page": "Next Page", "spectatorMenu.previous_page": "Previous Page", "spectatorMenu.root.prompt": "Press a key to select a command, and again to use it.", "spectatorMenu.team_teleport": "Teleport to Team Member", "spectatorMenu.team_teleport.prompt": "plicu'a lo keigri lo nu klama", "spectatorMenu.teleport": "klama lo se nunkei", "spectatorMenu.teleport.prompt": "plicu'a lo se nunkei lo nu klama", "stat.generalButton": "fadni", "stat.itemsButton": "dacti", "stat.minecraft.animals_bred": "Animals Bred", "stat.minecraft.aviate_one_cm": "Distance by Elytra", "stat.minecraft.bell_ring": "Bells Rung", "stat.minecraft.boat_one_cm": ".i lo bloti ku xe klama sela'u li", "stat.minecraft.clean_armor": "Armor Pieces Cleaned", "stat.minecraft.clean_banner": "le lanci pu se lumci", "stat.minecraft.clean_shulker_box": "lo tanxe pe la cylkr. cu pu jinsa", "stat.minecraft.climb_one_cm": ".i cpare sela'u li", "stat.minecraft.crouch_one_cm": "Distance Crouched", "stat.minecraft.damage_absorbed": "Damage Absorbed", "stat.minecraft.damage_blocked_by_shield": "ni bandu lo xrani be do", "stat.minecraft.damage_dealt": "lo risna ku poi do vimcu ku'o klani li", "stat.minecraft.damage_dealt_absorbed": "Damage Dealt (Absorbed)", "stat.minecraft.damage_dealt_resisted": "Damage Dealt (Resisted)", "stat.minecraft.damage_resisted": "Damage Resisted", "stat.minecraft.damage_taken": "lo rinsa ku poi do te vimcu ku'o klani li", "stat.minecraft.deaths": "lo nu do mrobi'o kei ku klani li", "stat.minecraft.drop": "lo se falcru ku klani li", "stat.minecraft.eat_cake_slice": "Cake Slices Eaten", "stat.minecraft.enchant_item": "lo dacti cu se mafyze'a", "stat.minecraft.fall_one_cm": ".i farlu sela'u li", "stat.minecraft.fill_cauldron": "lo patxu cu se clugau", "stat.minecraft.fish_caught": ".i lo finpe ku poi se kavbu ku'o klani li", "stat.minecraft.fly_one_cm": ".i vofli sela'u li", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Distance by Horse", "stat.minecraft.inspect_dispenser": "Dispensers Searched", "stat.minecraft.inspect_dropper": "Droppers Searched", "stat.minecraft.inspect_hopper": "Hoppers Searched", "stat.minecraft.interact_with_anvil": "ni pilno lo jimda'izbe", "stat.minecraft.interact_with_beacon": "Interactions with Beacon", "stat.minecraft.interact_with_blast_furnace": ".i nu simsikta lo baltoknu", "stat.minecraft.interact_with_brewingstand": "Interactions with Brewing Stand", "stat.minecraft.interact_with_campfire": ".i nu simsikta lo gikfa<PERSON>ri", "stat.minecraft.interact_with_cartography_table": "lo nu simsikta lo te nilce tumcartu", "stat.minecraft.interact_with_crafting_table": "lo nu simsikta lo zbajbu", "stat.minecraft.interact_with_furnace": "lo nu simsikta lo z<PERSON>oknu", "stat.minecraft.interact_with_grindstone": "ni pilno lo raktci", "stat.minecraft.interact_with_lectern": ".i nu simsikta lo nunctuta'a jubme", "stat.minecraft.interact_with_loom": "Interactions with Loom", "stat.minecraft.interact_with_smithing_table": "ni pilno lo jimda'ijbu", "stat.minecraft.interact_with_smoker": ".i nu simsikta lo damtoknu", "stat.minecraft.interact_with_stonecutter": "Interactions with Stone<PERSON>ter", "stat.minecraft.jump": "lo nu do plipe ku klani li", "stat.minecraft.leave_game": ".i cliva lo nunkei ku sela'u li", "stat.minecraft.minecart_one_cm": ".i lo baktu trene ku xe klama sela'u li", "stat.minecraft.mob_kills": "lo nu do catra lo bradi kei ku klani li", "stat.minecraft.open_barrel": "Barrels Opened", "stat.minecraft.open_chest": "lo vasru cu se kargau", "stat.minecraft.open_enderchest": "lo .endre vasru cu seltolga'o", "stat.minecraft.open_shulker_box": "lo tanxe pe la cylkr. cu se kargau", "stat.minecraft.pig_one_cm": ".i lo xarju ku xe klama sela'u li", "stat.minecraft.play_noteblock": "Note Blocks Played", "stat.minecraft.play_record": "Music Discs Played", "stat.minecraft.play_time": ".i lo nunkei be do ku mentu li", "stat.minecraft.player_kills": "lo nu do catra lo se nunkei kei ku klani li", "stat.minecraft.pot_flower": "lo spati cu selso'o fi lo patxu", "stat.minecraft.raid_trigger": "Raids Triggered", "stat.minecraft.raid_win": "Raids Won", "stat.minecraft.sleep_in_bed": "Times Slept in a Bed", "stat.minecraft.sneak_time": "mi<PERSON><PERSON><PERSON> temci", "stat.minecraft.sprint_one_cm": ".i bajra sela'u li", "stat.minecraft.strider_one_cm": "ni klama fu lo fagdzu", "stat.minecraft.swim_one_cm": ".i limna sela'u li", "stat.minecraft.talked_to_villager": "pu tavla lo selsosyzda", "stat.minecraft.target_hit": "ni teryre'o be fi do", "stat.minecraft.time_since_death": "ni za'i jmive", "stat.minecraft.time_since_rest": "ni za'i cikna", "stat.minecraft.total_world_time": "Time with World Open", "stat.minecraft.traded_with_villager": "pu canja fo lo selsosyzda", "stat.minecraft.trigger_trapped_chest": "Trapped Chests Triggered", "stat.minecraft.tune_noteblock": "Note Blocks Tuned", "stat.minecraft.use_cauldron": "lo djacu pu se lebna fi lo patxu", "stat.minecraft.walk_on_water_one_cm": "Distance Walked on Water", "stat.minecraft.walk_one_cm": ".i cadzu sela'u li", "stat.minecraft.walk_under_water_one_cm": "Distance Walked under Water", "stat.mobsButton": "cizda'u bende", "stat_type.minecraft.broken": "Times Broken", "stat_type.minecraft.crafted": ".i finti sela'u li", "stat_type.minecraft.dropped": "pu se falcru", "stat_type.minecraft.killed": ".i do catra %s %s", "stat_type.minecraft.killed.none": "do no roi catra %s", "stat_type.minecraft.killed_by": ".i %s catra do pi'i %s", "stat_type.minecraft.killed_by.none": "do no roi selcatra lo %s", "stat_type.minecraft.mined": ".i kunkakpa sela'u li", "stat_type.minecraft.picked_up": "to<PERSON><PERSON><PERSON><PERSON>i", "stat_type.minecraft.used": ".i pilno sela'u li", "stats.none": "-", "structure_block.button.detect_size": "vimcu", "structure_block.button.load": "sa<PERSON><PERSON><PERSON>i", "structure_block.button.save": "rejgau", "structure_block.custom_data": "Custom Data Tag Name", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Corner: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Load: %s", "structure_block.hover.save": "rejgau %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Structure Integrity and Seed", "structure_block.integrity.integrity": "Structure Integrity", "structure_block.integrity.seed": "Structure Seed", "structure_block.invalid_structure_name": "Invalid structure name '%s'", "structure_block.load_not_found": "Structure '%s' is not available", "structure_block.load_prepare": "Structure '%s' position prepared", "structure_block.load_success": "Structure loaded from '%s'", "structure_block.mode.corner": "kojna", "structure_block.mode.data": "datni", "structure_block.mode.load": "sa<PERSON><PERSON><PERSON>i", "structure_block.mode.save": "Save", "structure_block.mode_info.corner": ".i kojna tadji .i sinxa lo canlu", "structure_block.mode_info.data": ".i datni tadji .i sinxa fi lo proga", "structure_block.mode_info.load": ".i samymo'i tadji .i tcidu fi lo datnyvei", "structure_block.mode_info.save": ".i rejgau tadji .i ciska fi lo datnyvei", "structure_block.position": "Relative Position", "structure_block.position.x": "relative Position x", "structure_block.position.y": "relative position y", "structure_block.position.z": "relative position z", "structure_block.save_failure": "Unable to save structure '%s'", "structure_block.save_success": "Structure saved as '%s'", "structure_block.show_air": "jei jarco lo nalselviska bliku", "structure_block.show_boundingbox": "jei jarco lo canlu kurfa", "structure_block.size": "Structure Size", "structure_block.size.x": "structure size x", "structure_block.size.y": "structure size y", "structure_block.size.z": "structure size z", "structure_block.size_failure": "Unable to detect structure size. Add corners with matching structure names", "structure_block.size_success": "<PERSON><PERSON> successfully detected for '%s'", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Structure Name", "subtitles.ambient.cave": "tolkufygau sance", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "sance lo .amtisti", "subtitles.block.amethyst_block.resonate": "lo .amtisti cu slilu", "subtitles.block.anvil.destroy": "daspo lo jim<PERSON>'<PERSON>be", "subtitles.block.anvil.land": "lo jimda'izbe cu farlu", "subtitles.block.anvil.use": "pilno lo jimda'izbe", "subtitles.block.barrel.close": "lo mudbaktu cu se ga'orgau", "subtitles.block.barrel.open": "lo mudbaktu cu se kargau", "subtitles.block.beacon.activate": ".i lo gidytergu'i co'a akti", "subtitles.block.beacon.ambient": ".i lo gidytergu'i cu mlis<PERSON>na", "subtitles.block.beacon.deactivate": ".i lo gidytergu'i co'u akti", "subtitles.block.beacon.power_select": ".i cuxna lo se vlipa", "subtitles.block.beehive.drip": ".i lo melmi cu digyfa'u", "subtitles.block.beehive.enter": ".i lo bifce cu tsuku lo bicyzda", "subtitles.block.beehive.exit": ".i lo bifce cu cliva lo bicyzda", "subtitles.block.beehive.shear": ".i lo jinci cu sraku", "subtitles.block.beehive.work": ".i lo bifce cu gunka", "subtitles.block.bell.resonate": "lo janbe cu desku", "subtitles.block.bell.use": "janbe sance", "subtitles.block.big_dripleaf.tilt_down": "lo sinpezli co'a salpo", "subtitles.block.big_dripleaf.tilt_up": "lo sinpezli mo'u salpo", "subtitles.block.blastfurnace.fire_crackle": ".i lo tsatoknu cu kilvrupra", "subtitles.block.brewing_stand.brew": "lo selpinxe jukpa jubme cu zbasu lo fomsetyboi", "subtitles.block.bubble_column.bubble_pop": "lo varboi cu mlipo'a", "subtitles.block.bubble_column.upwards_ambient": "loi varboi mo'iga'u flecu", "subtitles.block.bubble_column.upwards_inside": "loi varboi mo'iga'u muvgau", "subtitles.block.bubble_column.whirlpool_ambient": "loi varboi mo'ini'a sarlu", "subtitles.block.bubble_column.whirlpool_inside": "loi varboi mo'ini'a muvgau", "subtitles.block.button.click": "sance lo batke", "subtitles.block.cake.add_candle": "lo titnanba cu te punji lo laktergu'i", "subtitles.block.campfire.crackle": "lo gikfagri cu v<PERSON>rap<PERSON>lpra", "subtitles.block.candle.crackle": "lo laktergu'i vrurapkilpra", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "lo vasru cu ganlo", "subtitles.block.chest.locked": "fanta tu'a le tanxe", "subtitles.block.chest.open": "lo vasru cu tolga'o", "subtitles.block.chorus_flower.death": "lo girza'a xrula cu mrobi'o", "subtitles.block.chorus_flower.grow": "lo girza'a xrula cu banro", "subtitles.block.comparator.click": "sance lo karbi", "subtitles.block.composter.empty": ".i lo furspa patxu cu se kutygau", "subtitles.block.composter.fill": ".i lo furspa patxu cu se clugau", "subtitles.block.composter.ready": ".i lo furspa patxu cu culno", "subtitles.block.conduit.activate": ".i lo xasygi'a co'a akti", "subtitles.block.conduit.ambient": ".i lo xasygi'a cu mliselsna", "subtitles.block.conduit.attack.target": ".i lo xasygi'a cu gunta", "subtitles.block.conduit.deactivate": ".i lo xasygi'a co'u akti", "subtitles.block.copper_bulb.turn_off": "lo tunka tergu'i co'u te gusni", "subtitles.block.copper_bulb.turn_on": "lo tunka tergu'i co'a te gusni", "subtitles.block.copper_trapdoor.close": "lo lolvrogai cu se ga'orgau", "subtitles.block.copper_trapdoor.open": "lo lolvrogai cu se kargau", "subtitles.block.crafter.craft": "lo zbasu cu zbasu", "subtitles.block.crafter.fail": "lo zbasu cu fliba lo nu zbasu", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "lo selja'i patxu co'a vasru", "subtitles.block.decorated_pot.insert_fail": "lo selja'i patxu co desku", "subtitles.block.decorated_pot.shatter": "lo selja'i patxu co porpi", "subtitles.block.dispenser.dispense": "lo dudzmi cu renro", "subtitles.block.dispenser.fail": "lo dudzmi cu fliba", "subtitles.block.door.toggle": "vorme vrumli", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": ".i lo mafyjbu cu se pilno", "subtitles.block.end_portal.spawn": ".i lo famymu'e mafyvro cu kalri", "subtitles.block.end_portal_frame.fill": ".i lo famymu'e kanla cu se punji", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "lo garbimvrogai cu vrukilpra", "subtitles.block.fire.ambient": "lo fagri cu vrurapkilpra", "subtitles.block.fire.extinguish": "fagystigau", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "lo verbanfi co'a karpo'ijbe", "subtitles.block.furnace.fire_crackle": "lo toknui cu vrurapkilpra", "subtitles.block.generic.break": "lo bliku cu selpofygau", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "cadzu sance", "subtitles.block.generic.hit": "lo bliku cu selpofygau", "subtitles.block.generic.place": "lo bliku cu selpu'i", "subtitles.block.grindstone.use": "lo zalxi'u cu se pilno", "subtitles.block.growing_plant.crop": "fanta spati", "subtitles.block.hanging_sign.waxed_interact_fail": "lo lertapla cu desku", "subtitles.block.honey_block.slide": ".i sakli lo melmi bliku", "subtitles.block.iron_trapdoor.close": "lo lolvrogai cu selga'o", "subtitles.block.iron_trapdoor.open": "lo lolvrogai cu seltolga'o", "subtitles.block.lava.ambient": "lik<PERSON>'i sance", "subtitles.block.lava.extinguish": "lo likro'i cu sibli", "subtitles.block.lever.click": "sance lo vraga", "subtitles.block.note_block.note": "lo tonga bliku gau te tonga", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "lo ca'erslanu cu muvdu", "subtitles.block.pointed_dripstone.drip_lava": "lo likro'i cu feizrme", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "cmalu likro'i farlu brapatxu", "subtitles.block.pointed_dripstone.drip_water": "lo djacu fei<PERSON>e", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "cmalu lo djacu farlu brapatxu", "subtitles.block.pointed_dripstone.land": "lo digyligykam<PERSON> janli loldi", "subtitles.block.portal.ambient": "sance lo munje vorme", "subtitles.block.portal.travel": ".i klama fu lo mafyvro", "subtitles.block.portal.trigger": ".i nerkla lo mafyvro", "subtitles.block.pressure_plate.click": "sance lo loldi batke", "subtitles.block.pumpkin.carve": ".i lo jinsi cu sraku", "subtitles.block.redstone_torch.burnout": "fagytergu'i sance", "subtitles.block.respawn_anchor.ambient": "lo rapyjbe tutci cu se sance", "subtitles.block.respawn_anchor.charge": ".i lo rapyjbe tutci cu nenze'a", "subtitles.block.respawn_anchor.deplete": ".i lo rapyjbe tutci cu nencau", "subtitles.block.respawn_anchor.set_spawn": ".i lo rapyjbe tutci cu galfi lo ve jbena", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "lo sculk sentyboi", "subtitles.block.sculk.spread": "lo sculk preja", "subtitles.block.sculk_catalyst.bloom": "lo sculk blimajgau ca preja", "subtitles.block.sculk_sensor.clicking": "lo me la .skylk. ganse co'a se sance", "subtitles.block.sculk_sensor.clicking_stop": "lo me la .skylk. ganse co'u se sance", "subtitles.block.sculk_shrieker.shriek": "lo sculk bliklacpe cu cmoni", "subtitles.block.shulker_box.close": "la cylkr. ganlo", "subtitles.block.shulker_box.open": "lo calku pe la cylkr. cu tolga'o", "subtitles.block.sign.waxed_interact_fail": "lo lertapla cu desku", "subtitles.block.smithing_table.use": ".i lo jimda'ijbu cu se pilno", "subtitles.block.smoker.smoke": "lo damtoknu cu jai sepi'o dam<PERSON>pa", "subtitles.block.sniffer_egg.crack": "lo zbisakci sovda cu se fenra", "subtitles.block.sniffer_egg.hatch": "lo z<PERSON>akci sovda cu karpo'i", "subtitles.block.sniffer_egg.plop": "lo z<PERSON>ci cu se jbena lo sovda", "subtitles.block.sponge.absorb": "lo panje cu cokcu", "subtitles.block.sweet_berry_bush.pick_berries": "loi jbari cu farlu", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "lo lolvrogai cu vrukilpra", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "lo talsa cupra be lo cizda'u cu vrurap<PERSON>lpra", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "lo talsa cupra be lo cizda'u cu ga'orbi'o", "subtitles.block.trial_spawner.detect_player": "lo talsa cupra be lo cizda'u cu tisna", "subtitles.block.trial_spawner.eject_item": "lo talsa cupra be lo cizda'u cu baryre'o lo dacti", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "lo talsa cupra be lo cizda'u cu karbi'o", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "lo danlu cu jbena lo talsa cupra be lo cizda'u", "subtitles.block.tripwire.attach": "lo terkavbu genxu cu jorne", "subtitles.block.tripwire.click": "sance lo terkavbu", "subtitles.block.tripwire.detach": "lo terkavbu genxu cu toljorne", "subtitles.block.vault.activate": "Vault ignites", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Vault closes", "subtitles.block.vault.deactivate": "<PERSON>ault extinguishes", "subtitles.block.vault.eject_item": "Vault ejects item", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Vault opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "flecu lo djacu", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "Book placed", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "Book taken", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "<PERSON><PERSON> prick", "subtitles.entity.allay.ambient_with_item": "le blapedvoi cu sisku", "subtitles.entity.allay.ambient_without_item": "le blapedvoi cu pacna", "subtitles.entity.allay.death": "le blapedvoi co'a morsi", "subtitles.entity.allay.hurt": "le blapedvoi co'a cortu", "subtitles.entity.allay.item_given": "le blapedvoi cu cmila", "subtitles.entity.allay.item_taken": "le blapedvoi cu lebna", "subtitles.entity.allay.item_thrown": "le blapedvoi cu renro", "subtitles.entity.armadillo.ambient": "lo dapsodi cu cmoni", "subtitles.entity.armadillo.brush": "lo dapsodi cu se brulu'i", "subtitles.entity.armadillo.death": "lo dapsodi cu mrobi'o", "subtitles.entity.armadillo.eat": "lo dapsodi cu citka", "subtitles.entity.armadillo.hurt": "lo dapsodi cu cortu", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "lo dapsodi mo'u farlu", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "lo dapsodi co'a bolci", "subtitles.entity.armadillo.scute_drop": "lo piltapla cu farlu", "subtitles.entity.armadillo.unroll_finish": "Armadillo unrolls", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "Something fell", "subtitles.entity.arrow.hit": "Arrow hits", "subtitles.entity.arrow.hit_player": "lo kelci cu darxi", "subtitles.entity.arrow.shoot": "cecla lo celga'a", "subtitles.entity.axolotl.attack": "lo axlte cu gunta", "subtitles.entity.axolotl.death": "lo axlte co'a morsi", "subtitles.entity.axolotl.hurt": "lo axlte cu cortu", "subtitles.entity.axolotl.idle_air": "lo axlte cu na'icmo", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "lo axlte cu sploici", "subtitles.entity.axolotl.swim": "lo axlte cu limna", "subtitles.entity.bat.ambient": "lo <PERSON><PERSON><PERSON> cu <PERSON>a", "subtitles.entity.bat.death": "lo volratcu cu mrobi'o", "subtitles.entity.bat.hurt": "lo volratcu cu cortu", "subtitles.entity.bat.takeoff": "lo volsmacu co'a vofli", "subtitles.entity.bee.ambient": ".i lo bifce cu na'isli", "subtitles.entity.bee.death": "lo bifce co'a morsi", "subtitles.entity.bee.hurt": ".i lo bifce cu cortu", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": ".i lo bifce cu fengu na'isli", "subtitles.entity.bee.pollinate": ".i lo bifce cu gleki na'isli", "subtitles.entity.bee.sting": ".i lo bifce cu tunta", "subtitles.entity.blaze.ambient": "lo fagda'u cu vasxu", "subtitles.entity.blaze.burn": "lo fagda'u cu vrurap<PERSON>lpra", "subtitles.entity.blaze.death": "lo fagda'u cu mrobi'o", "subtitles.entity.blaze.hurt": "lo fagda'u cu cortu", "subtitles.entity.blaze.shoot": "lo fagda'u cu celgunta", "subtitles.entity.boat.paddle_land": ".i blocpu", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "lo bifyda'u cu brebi'o", "subtitles.entity.breeze.death": "lo bifyda'u cu mrobi'o", "subtitles.entity.breeze.deflect": "lo bifyda'u cu minra", "subtitles.entity.breeze.hurt": "lo bifyda'u cu cortu", "subtitles.entity.breeze.idle_air": "lo bifyda'u cu vofli", "subtitles.entity.breeze.idle_ground": "lo bifyda'u cu cmoni", "subtitles.entity.breeze.inhale": "lo bifyda'u cu sakyva'u", "subtitles.entity.breeze.jump": "lo bifyda'u cu plipe", "subtitles.entity.breeze.land": "lo bifyda'u mo'u farlu", "subtitles.entity.breeze.shoot": "lo bifyda'u cu celgunta", "subtitles.entity.breeze.slide": "lo bifyda'u cu sakli", "subtitles.entity.breeze.whirl": "lo bifyda'u cu jai ru'u ke sarlu brife", "subtitles.entity.breeze.wind_burst": "sance lo brife bakfu", "subtitles.entity.camel.ambient": "lo rabykumte cu cmoni", "subtitles.entity.camel.dash": "lo rabykumte cu sutmu'u", "subtitles.entity.camel.dash_ready": "lo rabykumte co'a bredi lo nu sutmu'u", "subtitles.entity.camel.death": "lo rabykumte co'a morsi", "subtitles.entity.camel.eat": "lo rabykumte cu citka", "subtitles.entity.camel.hurt": "lo rabykumte cu cortu", "subtitles.entity.camel.saddle": "Saddle equips", "subtitles.entity.camel.sit": "lo rabykumte co'a zutse", "subtitles.entity.camel.stand": "lo rabykumte co'a sanli", "subtitles.entity.camel.step": "lo rabykumte cu stapa", "subtitles.entity.camel.step_sand": "lo rabykumte cu se toljmaji lo canre", "subtitles.entity.cat.ambient": "lo mlatu cu latcmo", "subtitles.entity.cat.beg_for_food": ".i lo mlatu cu pikci", "subtitles.entity.cat.death": "lo mlatu cu mrobi'o", "subtitles.entity.cat.eat": ".i lo mlatu cu citka", "subtitles.entity.cat.hiss": ".i lo mlatu cu cmoni", "subtitles.entity.cat.hurt": "lo mlatu cu cortu", "subtitles.entity.cat.purr": ".i lo mlatu cu cmoni", "subtitles.entity.chicken.ambient": "lo jipci cu jipcycmo", "subtitles.entity.chicken.death": "lo jipci cu mrobi'o", "subtitles.entity.chicken.egg": "Chicken plops", "subtitles.entity.chicken.hurt": "lo jipci cu cortu", "subtitles.entity.cod.death": "lo fiprgadusu cu mrobi'o", "subtitles.entity.cod.flop": "lo fiprgadusu cu plipe", "subtitles.entity.cod.hurt": "lo fiprgadusu cu cortu", "subtitles.entity.cow.ambient": "lo bakni cu bakcmo", "subtitles.entity.cow.death": "lo bakni cu mrobi'o", "subtitles.entity.cow.hurt": "lo bakni cu cortu", "subtitles.entity.cow.milk": "lo bakni cu se ladycpa", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "la kriper. mrobi'o", "subtitles.entity.creeper.hurt": "lo parkla cu cortu", "subtitles.entity.creeper.primed": "la kriper. cu selsna sibli", "subtitles.entity.dolphin.ambient": "lo delfinu cu na'icmo", "subtitles.entity.dolphin.ambient_water": "lo delfinu cu siclu", "subtitles.entity.dolphin.attack": "lo delfinu cu gunta", "subtitles.entity.dolphin.death": "lo delfinu cu mrobi'o", "subtitles.entity.dolphin.eat": "lo delfinu cu citka", "subtitles.entity.dolphin.hurt": "lo delfinu cu cortu", "subtitles.entity.dolphin.jump": "lo delfinu cu plipe", "subtitles.entity.dolphin.play": "lo delfinu cu kelci", "subtitles.entity.dolphin.splash": "lo delfinu cu sploici", "subtitles.entity.dolphin.swim": "lo delfinu cu limna", "subtitles.entity.donkey.ambient": "Donkey hee-haws", "subtitles.entity.donkey.angry": "Donkey neighs", "subtitles.entity.donkey.chest": "Donkey Chest equips", "subtitles.entity.donkey.death": "lo xasli cu mrobi'o", "subtitles.entity.donkey.eat": ".i lo xasli cu citka", "subtitles.entity.donkey.hurt": "lo xasli cu cortu", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Drowned gurgles", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "lo jaurvau mivymro cu morsi", "subtitles.entity.drowned.hurt": "lo jaurvau mivymro cu cortu", "subtitles.entity.drowned.shoot": "lo jaurvau mivymro cu cibyba'erxa'iryre'o", "subtitles.entity.drowned.step": "lo jaurvau mi<PERSON>ro cu klama", "subtitles.entity.drowned.swim": "lo jaurvau mivymro cu limna", "subtitles.entity.egg.throw": "lo sovda cu vofli", "subtitles.entity.elder_guardian.ambient": "Elder Guardian moans", "subtitles.entity.elder_guardian.ambient_land": "Elder Guardian flaps", "subtitles.entity.elder_guardian.curse": "Elder Guardian curses", "subtitles.entity.elder_guardian.death": "lo dzena badypre cu mrobi'o", "subtitles.entity.elder_guardian.flop": "Elder Guardian flops", "subtitles.entity.elder_guardian.hurt": "lo dzena badypre cu cortu", "subtitles.entity.ender_dragon.ambient": "Dragon roars", "subtitles.entity.ender_dragon.death": "lo drakono cu mrobi'o", "subtitles.entity.ender_dragon.flap": "Dragon flaps", "subtitles.entity.ender_dragon.growl": "Dragon growls", "subtitles.entity.ender_dragon.hurt": "lo drakono cu cortu", "subtitles.entity.ender_dragon.shoot": "lo drakono cu celgunta", "subtitles.entity.ender_eye.death": ".i lo famymu'e kanla cu porpi", "subtitles.entity.ender_eye.launch": "Eye of <PERSON><PERSON> shoots", "subtitles.entity.ender_pearl.throw": "lo .endre boijme cu vofli", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> vwoops", "subtitles.entity.enderman.death": "lo .endre prenu cu mrobi'o", "subtitles.entity.enderman.hurt": "lo .endre prenu cu cortu", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON> cries out", "subtitles.entity.enderman.teleport": "Enderman teleports", "subtitles.entity.endermite.ambient": "Endermite scuttles", "subtitles.entity.endermite.death": "lo .endre miptera cu mrobi'o", "subtitles.entity.endermite.hurt": "lo .endre miptera cu cortu", "subtitles.entity.evoker.ambient": "lo pacykemklacpepre cu tolylaurba'u", "subtitles.entity.evoker.cast_spell": "lo pacykemklacpepre cu mafre'o", "subtitles.entity.evoker.celebrate": "Evoker cheers", "subtitles.entity.evoker.death": "lo pacykemklacpepre cu mrobi'o", "subtitles.entity.evoker.hurt": "lo pacykemklacpepre cu cortu", "subtitles.entity.evoker.prepare_attack": "lo pacykemklacpepre cu gunta brebi'o", "subtitles.entity.evoker.prepare_summon": "lo pacykemklacpepre cu klacpe brebi'o", "subtitles.entity.evoker.prepare_wololo": "lo pacykemklacpepre mafygau brebi'o", "subtitles.entity.evoker_fangs.attack": "Fangs snap", "subtitles.entity.experience_orb.pickup": "Experience gained", "subtitles.entity.firework_rocket.blast": "Firework blasts", "subtitles.entity.firework_rocket.launch": "Firework launches", "subtitles.entity.firework_rocket.twinkle": "Firework twinkles", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": ".i lo fipkalte genxu cu se rapcpa", "subtitles.entity.fishing_bobber.splash": "Fishing Bobber splashes", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> thrown", "subtitles.entity.fox.aggro": ".i lo lorxu cu fegbi'o", "subtitles.entity.fox.ambient": ".i lo lorxu cu torbrato'agau", "subtitles.entity.fox.bite": ".i lo lorxu cu batci", "subtitles.entity.fox.death": ".i lo lorxu cu mrobi'o", "subtitles.entity.fox.eat": ".i lo lorxu cu citka", "subtitles.entity.fox.hurt": ".i lo lorxu cu cortu", "subtitles.entity.fox.screech": ".i lo lorxu cu mosyvrugau", "subtitles.entity.fox.sleep": ".i lo lorxu cu sipsavgau", "subtitles.entity.fox.sniff": ".i lo lorxu cu z<PERSON>ci", "subtitles.entity.fox.spit": ".i lo lorxu cu putre'o", "subtitles.entity.fox.teleport": ".i lo lorxu cu sukmu'u", "subtitles.entity.frog.ambient": "lo pipybanfi co'a consna", "subtitles.entity.frog.death": "lo pipybanfi co'a morsi", "subtitles.entity.frog.eat": "lo pipybanfi co'a citka", "subtitles.entity.frog.hurt": "lo pipybanfi cu cortu", "subtitles.entity.frog.lay_spawn": "lo pipybanfi co'a vrepu'i fipso'a", "subtitles.entity.frog.long_jump": "lo pipybanfi co'a nunpi'e", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "jelca sance", "subtitles.entity.generic.death": "ca mrobi'o", "subtitles.entity.generic.drink": "Sipping", "subtitles.entity.generic.eat": "citka sance", "subtitles.entity.generic.explode": "j<PERSON>ma sance", "subtitles.entity.generic.extinguish_fire": "Fire extinguishes", "subtitles.entity.generic.hurt": "cortu sance", "subtitles.entity.generic.small_fall": "tapflifa'u", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON>'o", "subtitles.entity.generic.swim": "limna sance", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "la gast. krixa", "subtitles.entity.ghast.death": "la gast. mrobi'o", "subtitles.entity.ghast.hurt": "la gast. cortu", "subtitles.entity.ghast.shoot": "la me gast. cu celgunta", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "lo carmi ke ja'ordai greku cu tisna", "subtitles.entity.glow_item_frame.break": "lo carmi ke ja'ordai greku cu porpi", "subtitles.entity.glow_item_frame.place": "lo carmi ke ja'ordai greku cu punji", "subtitles.entity.glow_item_frame.remove_item": "lo carmi ke ja'ordai greku cu toltisna", "subtitles.entity.glow_item_frame.rotate_item": "lo carmi ke ja'ordai greku cu se sance", "subtitles.entity.glow_squid.ambient": "lo carmi kalmari cu limna", "subtitles.entity.glow_squid.death": "lo carmi kalmari cu mrobi'o", "subtitles.entity.glow_squid.hurt": "lo carmi kalmari cu cortu", "subtitles.entity.glow_squid.squirt": "lo carmi kalmari cu cecla lo xinmo", "subtitles.entity.goat.ambient": "lo kanba cu rubykrixa", "subtitles.entity.goat.death": "lo kanba co'a morsi", "subtitles.entity.goat.eat": "lo kanba cu citka", "subtitles.entity.goat.horn_break": "jirna kanba porpi", "subtitles.entity.goat.hurt": "lo kanba cu cortu", "subtitles.entity.goat.long_jump": "lo kanba cu plipe", "subtitles.entity.goat.milk": "<PERSON><PERSON> gets milked", "subtitles.entity.goat.prepare_ram": "lo kanba cu maxydzu", "subtitles.entity.goat.ram_impact": "lo kanba cu vilbajra", "subtitles.entity.goat.screaming.ambient": "lo kanba cu conykrixa", "subtitles.entity.goat.step": "lo kanba cu stapa", "subtitles.entity.guardian.ambient": "Guardian moans", "subtitles.entity.guardian.ambient_land": "Guardian flaps", "subtitles.entity.guardian.attack": "lo badypre cu celgunta", "subtitles.entity.guardian.death": "lo badypre cu mrobi'o", "subtitles.entity.guardian.flop": "Guardian flops", "subtitles.entity.guardian.hurt": "lo badypre cu cortu", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": ".i lo balxarju cu cmoni", "subtitles.entity.hoglin.angry": ".i lo balxarju cu fe'ucmo", "subtitles.entity.hoglin.attack": ".i lo balxarju cu gunta", "subtitles.entity.hoglin.converted_to_zombified": ".i lo balxarju cu mivmrobi'o", "subtitles.entity.hoglin.death": ".i lo balxarju cu morsi", "subtitles.entity.hoglin.hurt": ".i lo balxarju cu cortu", "subtitles.entity.hoglin.retreat": ".i lo balxarju cu ti'ekla", "subtitles.entity.hoglin.step": ".i lo balxarju cu stapa", "subtitles.entity.horse.ambient": "xircmo sance", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Horse armor equips", "subtitles.entity.horse.breathe": "lo xirma cu vasxu", "subtitles.entity.horse.death": "lo xirma cu mrobi'o", "subtitles.entity.horse.eat": "lo xirma cu citka", "subtitles.entity.horse.gallop": "lo xirma cu glaupa", "subtitles.entity.horse.hurt": "lo xirma cu cortu", "subtitles.entity.horse.jump": "lo xirma cu plipe", "subtitles.entity.horse.saddle": "Saddle equips", "subtitles.entity.husk.ambient": "lo sudga mivymro cu cmoni", "subtitles.entity.husk.converted_to_zombie": ".i lo sudmro cu mivmrobi'o", "subtitles.entity.husk.death": "lo sudga mivymro cu mrobi'o", "subtitles.entity.husk.hurt": "lo sudga mivymro cu cortu", "subtitles.entity.illusioner.ambient": "Illusioner murmurs", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON> casts spell", "subtitles.entity.illusioner.death": "lo gasticgau cu mrobi'o", "subtitles.entity.illusioner.hurt": "lo gasticgau cu cortu", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON> displaces", "subtitles.entity.illusioner.prepare_blindness": "Il<PERSON><PERSON> prepares blindness", "subtitles.entity.illusioner.prepare_mirror": "Il<PERSON><PERSON> prepares mirror image", "subtitles.entity.iron_golem.attack": "lo tirse remsmimi'i cu gunta", "subtitles.entity.iron_golem.damage": ".i lo tirse remsmimi'i cu porpi", "subtitles.entity.iron_golem.death": "lo tirse remsmimi'i cu mrobi'o", "subtitles.entity.iron_golem.hurt": "lo tirse remsmimi'i cu cortu", "subtitles.entity.iron_golem.repair": ".i lo tirse remsmimi'i cu se cikre", "subtitles.entity.item.break": "lo dacti cu selpo'i", "subtitles.entity.item.pickup": "lo dacti cu selcpa", "subtitles.entity.item_frame.add_item": "lo ja'ordai greku cu tisna", "subtitles.entity.item_frame.break": "lo ja'ordai greku cu porpi", "subtitles.entity.item_frame.place": "lo ja'ordai greku cu punji", "subtitles.entity.item_frame.remove_item": "lo ja'ordai greku cu toltisna", "subtitles.entity.item_frame.rotate_item": "lo ja'ordai greku cu se sance", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Lightning strikes", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON> sance", "subtitles.entity.llama.ambient": "lo tcokumte cu rubykrixa", "subtitles.entity.llama.angry": "<PERSON><PERSON><PERSON> bleats angrily", "subtitles.entity.llama.chest": "Llama Chest equips", "subtitles.entity.llama.death": "lo tcokumte cu mrobi'o", "subtitles.entity.llama.eat": "lo tcokumte cu citka", "subtitles.entity.llama.hurt": "lo tcokumte cu cortu", "subtitles.entity.llama.spit": "lo tcokumte cu sputu", "subtitles.entity.llama.step": "lo tcokumte cu stapa", "subtitles.entity.llama.swag": "<PERSON><PERSON><PERSON> is decorated", "subtitles.entity.magma_cube.death": "lo likro'i bliku cu mrobi'o", "subtitles.entity.magma_cube.hurt": " lo likro'i bliku cu cortu", "subtitles.entity.magma_cube.squish": "Magma Cube squishes", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Minecart rolls", "subtitles.entity.mooshroom.convert": ".i lo bakygumri cu binxo", "subtitles.entity.mooshroom.eat": ".i lo bakygumri cu citka", "subtitles.entity.mooshroom.milk": "la .mucrum. te cpacu lo stasu", "subtitles.entity.mooshroom.suspicious_milk": "la .mucrum. te cpacu lo selsenpi stasu", "subtitles.entity.mule.ambient": "lo xaslyxi'a cu cmoni", "subtitles.entity.mule.angry": ".i lo xirjoixaslypanzi cu fe'ucmo", "subtitles.entity.mule.chest": "Mule Chest equips", "subtitles.entity.mule.death": "lo xaslyxi'a cu mrobi'o", "subtitles.entity.mule.eat": ".i lo xirjoixaslypanzi cu citka", "subtitles.entity.mule.hurt": "lo xaslyxi'a cu cortu", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "lo pixra cu porpi", "subtitles.entity.painting.place": "Painting placed", "subtitles.entity.panda.aggressive_ambient": "Panda huffs", "subtitles.entity.panda.ambient": "Panda pants", "subtitles.entity.panda.bite": "Panda bites", "subtitles.entity.panda.cant_breed": "Panda bleats", "subtitles.entity.panda.death": ".i lo latcribe cu mrobi'o", "subtitles.entity.panda.eat": ".i lo latcribe cu citka", "subtitles.entity.panda.hurt": ".i lo latcribe cu cortu", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON>'s nose tickles", "subtitles.entity.panda.sneeze": "<PERSON><PERSON> sneezes", "subtitles.entity.panda.step": "Panda steps", "subtitles.entity.panda.worried_ambient": "Panda whimpers", "subtitles.entity.parrot.ambient": "lo spitaki cu tavla", "subtitles.entity.parrot.death": "lo spitaki cu mrobi'o", "subtitles.entity.parrot.eats": "lo spitaki cu citka", "subtitles.entity.parrot.fly": ".i lo spitaki cu vofli", "subtitles.entity.parrot.hurts": "lo spitaki cu cortu", "subtitles.entity.parrot.imitate.blaze": "lo spitaki cu vasxu", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "spitaki se sance lo bifyda'u sance", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "lo spitaki cu selsna sibli", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> gurgles", "subtitles.entity.parrot.imitate.elder_guardian": "lo spitaki cu cmoni", "subtitles.entity.parrot.imitate.ender_dragon": "lo spitaki cu capsidba'u", "subtitles.entity.parrot.imitate.endermite": "lo spitaki cu sutstapa", "subtitles.entity.parrot.imitate.evoker": "lo spitaki cu smacmoni", "subtitles.entity.parrot.imitate.ghast": "lo spitaki cu crixa", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.hoglin": ".i lo spitaki cu fuktra lo balxarju", "subtitles.entity.parrot.imitate.husk": "lo spitaki cu cmoni", "subtitles.entity.parrot.imitate.illusioner": "Parrot murmurs", "subtitles.entity.parrot.imitate.magma_cube": "Parrot squishes", "subtitles.entity.parrot.imitate.phantom": "lo spitaki cu cladykrixa", "subtitles.entity.parrot.imitate.piglin": ".i lo spitaki cu fuktra lo xajy<PERSON>re", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.pillager": "Parrot murmurs", "subtitles.entity.parrot.imitate.ravager": "lo spitaki cu consutcmoni", "subtitles.entity.parrot.imitate.shulker": "lo spitaki cu cadycta", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "lo spitaki cu vrurapsut<PERSON>li", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Parrot rattles", "subtitles.entity.parrot.imitate.vex": "Parrot vexes", "subtitles.entity.parrot.imitate.vindicator": "lo spitaki cu convo'a", "subtitles.entity.parrot.imitate.warden": "lo spitaki cu bratogykrixa", "subtitles.entity.parrot.imitate.witch": "lo spitaki cu mi'amli", "subtitles.entity.parrot.imitate.wither": "lo spitaki cu fegbi'o", "subtitles.entity.parrot.imitate.wither_skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>rro<PERSON> growls", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.phantom.ambient": "lo ri'orkalvoi cu cladyk<PERSON>a", "subtitles.entity.phantom.bite": "lo cteru'i cu molgunta", "subtitles.entity.phantom.death": "lo cteru'i cu mrobi'o", "subtitles.entity.phantom.flap": "lo cteru'i cu na'idesku", "subtitles.entity.phantom.hurt": "lo cteru'i cu cortu", "subtitles.entity.phantom.swoop": "lo cteru'i cu dzigunta", "subtitles.entity.pig.ambient": "lo xarju cu bacru", "subtitles.entity.pig.death": "lo xarju cu mrobi'o", "subtitles.entity.pig.hurt": "lo xarju cu cortu", "subtitles.entity.pig.saddle": "Saddle equips", "subtitles.entity.piglin.admiring_item": ".i lo xajypre cu catlu lo dacti", "subtitles.entity.piglin.ambient": ".i lo xajypre cu cmoni", "subtitles.entity.piglin.angry": ".i lo xajypre cu fe'ucmo", "subtitles.entity.piglin.celebrate": ".i lo xajypre cu salci", "subtitles.entity.piglin.converted_to_zombified": ".i lo xajypre cu mivmrobi'o", "subtitles.entity.piglin.death": ".i lo xajypre cu morsi", "subtitles.entity.piglin.hurt": ".i lo xajypre cu cortu", "subtitles.entity.piglin.jealous": ".i lo xajypre cu jilra", "subtitles.entity.piglin.retreat": ".i lo xajypre cu ti'ekla", "subtitles.entity.piglin.step": ".i lo xajypre cu stapa", "subtitles.entity.piglin_brute.ambient": ".i lo cilce xajypre cu cmoni", "subtitles.entity.piglin_brute.angry": ".i lo cilce xajypre cu fe'ucmo", "subtitles.entity.piglin_brute.converted_to_zombified": ".i lo cilce xajypre cu mivmrobi'o", "subtitles.entity.piglin_brute.death": ".i lo cilce xajypre cu morsi", "subtitles.entity.piglin_brute.hurt": ".i lo cilce xajypre cu cortu", "subtitles.entity.piglin_brute.step": ".i lo cilce xajypre cu stapa", "subtitles.entity.pillager.ambient": "lo vilcpapre cu smacmoni", "subtitles.entity.pillager.celebrate": "lo vilcpapre cu geirki'a", "subtitles.entity.pillager.death": "lo vilcpapre cu mrobi'o", "subtitles.entity.pillager.hurt": "lo vilcpapre cu cortu", "subtitles.entity.player.attack.crit": ".i banli nunda'i", "subtitles.entity.player.attack.knockback": ".i tikpa nunda'i", "subtitles.entity.player.attack.strong": ".i tsali nunda'i", "subtitles.entity.player.attack.sweep": ".i brusmi nunda'i", "subtitles.entity.player.attack.weak": ".i ruble nunda'i", "subtitles.entity.player.burp": "befkafke sance", "subtitles.entity.player.death": "lo kelci cu mrobi'o", "subtitles.entity.player.freeze_hurt": "lo  kelci cu dunja", "subtitles.entity.player.hurt": "lo kelci cu cortu", "subtitles.entity.player.hurt_drown": ".i lo keipre cu vaxycau", "subtitles.entity.player.hurt_on_fire": ".i lo keipre cu se fagri", "subtitles.entity.player.levelup": "momvelru'e galgau sance", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "lo bercribe cu cmoni", "subtitles.entity.polar_bear.ambient_baby": "lo bercribe cu mliselsna", "subtitles.entity.polar_bear.death": "lo bercribe cu mrobi'o", "subtitles.entity.polar_bear.hurt": "lo bercribe cu cortu", "subtitles.entity.polar_bear.warning": "lo bercribe cu capsidba'u", "subtitles.entity.potion.splash": "Bottle smashes", "subtitles.entity.potion.throw": "lo botpi cu selre'o", "subtitles.entity.puffer_fish.blow_out": "lo fiprtetraudontidi cu cmabi'o", "subtitles.entity.puffer_fish.blow_up": "lo fiprtetraudontidi cu brabi'o", "subtitles.entity.puffer_fish.death": "lo fiprtetraudontidi cu mrobi'o", "subtitles.entity.puffer_fish.flop": "lo fiprtetraudontidi cu plipe", "subtitles.entity.puffer_fish.hurt": "lo fiprtetraudontidi cu cortu", "subtitles.entity.puffer_fish.sting": "lo fiprtetraudontidi cu kilgunta", "subtitles.entity.rabbit.ambient": "lo ractu cu torbrato'a", "subtitles.entity.rabbit.attack": "lo ractu cu gunta", "subtitles.entity.rabbit.death": "lo ractu cu mrobi'o", "subtitles.entity.rabbit.hurt": "lo ractu cu cortu", "subtitles.entity.rabbit.jump": "lo ractu cu plipe", "subtitles.entity.ravager.ambient": "lo nunspokezgau cu consutcmoni", "subtitles.entity.ravager.attack": "lo nunspokezgau cu molgunta", "subtitles.entity.ravager.celebrate": "lo nunspokezgau cu geirki'a", "subtitles.entity.ravager.death": "lo nunspokezgau cu mrobi'o", "subtitles.entity.ravager.hurt": "lo nunspokezgau cu cortu", "subtitles.entity.ravager.roar": "lo nunspokezgau cu capsidba'u", "subtitles.entity.ravager.step": "lo nunspokezgau cu stapa", "subtitles.entity.ravager.stunned": "lo nunspokezgau cu seljenca", "subtitles.entity.salmon.death": "lo salmone cu mrobi'o", "subtitles.entity.salmon.flop": "lo salmone cu plipe", "subtitles.entity.salmon.hurt": "lo salmone cu cortu", "subtitles.entity.sheep.ambient": "lo lanme cu lancmo", "subtitles.entity.sheep.death": "lo lanme cu mrobi'o", "subtitles.entity.sheep.hurt": "lo lanme cu cortu", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> lurks", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "la cylkr. mrobi'o", "subtitles.entity.shulker.hurt": "la cylkr. cortu", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "la cylkr. cecla", "subtitles.entity.shulker.teleport": "Shulker teleports", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> Bullet explodes", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> Bullet breaks", "subtitles.entity.silverfish.ambient": "lo selmipyda'u cu selsna sibli", "subtitles.entity.silverfish.death": "lo selmipyda'u cu mrobi'o", "subtitles.entity.silverfish.hurt": "lo selmipyda'u cu cortu", "subtitles.entity.skeleton.ambient": "lo bodygraku cu vrurapsutkinli", "subtitles.entity.skeleton.converted_to_stray": "Skeleton converts to Stray", "subtitles.entity.skeleton.death": "lo bogygreku cu mrobi'o", "subtitles.entity.skeleton.hurt": " lo bogygreku cu cortu", "subtitles.entity.skeleton.shoot": "lo bogygreku cu celgunta", "subtitles.entity.skeleton_horse.ambient": "lo bogygreku xirma cu krixa", "subtitles.entity.skeleton_horse.death": "lo bogygreku cu mrobi'o", "subtitles.entity.skeleton_horse.hurt": "lo bogygreku xirma cu cortu", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "lo bogygreku xirma cu limna", "subtitles.entity.slime.attack": "lo rigdulda'u cu gunta", "subtitles.entity.slime.death": "lo rigdulda'u cu mrobi'o", "subtitles.entity.slime.hurt": "lo rigdulda'u cu cortu", "subtitles.entity.slime.squish": "Slime squishes", "subtitles.entity.sniffer.death": "lo zbisakci cu mrobi'o", "subtitles.entity.sniffer.digging": "lo z<PERSON>akci cu kakpa", "subtitles.entity.sniffer.digging_stop": "lo zbisak<PERSON> co'a sanli", "subtitles.entity.sniffer.drop_seed": "lo zbisakci cu falcru lo tsiju", "subtitles.entity.sniffer.eat": "lo zbisakci cu citka", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Egg hatches", "subtitles.entity.sniffer.happy": "lo z<PERSON>ci cu tcegei", "subtitles.entity.sniffer.hurt": "lo zbisakci cu cortu", "subtitles.entity.sniffer.idle": "lo z<PERSON>ak<PERSON> cu cmoni", "subtitles.entity.sniffer.scenting": "lo zbisakci cu sumne", "subtitles.entity.sniffer.searching": "lo z<PERSON>akci cu sisku", "subtitles.entity.sniffer.sniffing": "lo zbisakci cu zbisakci", "subtitles.entity.sniffer.step": "Sniffer steps", "subtitles.entity.snow_golem.death": "lo snime remsmimi'i cu mrobi'o", "subtitles.entity.snow_golem.hurt": "lo snime remsmimi'i cu cortu", "subtitles.entity.snowball.throw": "lo snime bolci cu vofli", "subtitles.entity.spider.ambient": "lo jukni cu selsna sibli", "subtitles.entity.spider.death": "lo jukni cu mrobi'o", "subtitles.entity.spider.hurt": "lo jukni cu cortu", "subtitles.entity.squid.ambient": "lo kalmari cu limna", "subtitles.entity.squid.death": "lo kalmari cu mrobi'o", "subtitles.entity.squid.hurt": "lo kalmari cu cortu", "subtitles.entity.squid.squirt": "lo kalmari cu cecla lo xinmo", "subtitles.entity.stray.ambient": "lo lekybogygreku cu vrurapsutkinli", "subtitles.entity.stray.death": "lo muvzva cu mrobi'o", "subtitles.entity.stray.hurt": "lo muvzva cu cortu", "subtitles.entity.strider.death": ".i lo fagdzu cu morsi", "subtitles.entity.strider.eat": ".i lo fagdzu cu citka", "subtitles.entity.strider.happy": ".i lo fagdzu cu gekcmo", "subtitles.entity.strider.hurt": ".i lo fagdzu cu cortu", "subtitles.entity.strider.idle": ".i lo fagdzu cu cmoni", "subtitles.entity.strider.retreat": ".i lo fagdzu cu ti'ekla", "subtitles.entity.tadpole.death": "lo verbanfi co'a morsi", "subtitles.entity.tadpole.flop": "Tadpole flops", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "lo verbanfi cu cortu", "subtitles.entity.tnt.primed": "lo jbama cu jelca", "subtitles.entity.tropical_fish.death": ".i lo tropika finpe cu morsi", "subtitles.entity.tropical_fish.flop": ".i lo tropika finpe cu tikpa", "subtitles.entity.tropical_fish.hurt": ".i lo tropika finpe cu cortu", "subtitles.entity.turtle.ambient_land": "lo cakyrespa cu cmoni", "subtitles.entity.turtle.death": "lo cakyrespa cu mrobi'o", "subtitles.entity.turtle.death_baby": "lo cifnu cakyrespa cu mrobi'o", "subtitles.entity.turtle.egg_break": ".i lo sovda be lo cakyrespa cu porpi", "subtitles.entity.turtle.egg_crack": ".i lo sovda be lo cakyrespa cu se fenra", "subtitles.entity.turtle.egg_hatch": ".i lo sovda be lo cakyrespa cu karpo'i", "subtitles.entity.turtle.hurt": "lo cakyrespa cu cortu", "subtitles.entity.turtle.hurt_baby": "lo cifnu cakyrespa cu cortu", "subtitles.entity.turtle.lay_egg": "lo cakyrespa cu seljbe", "subtitles.entity.turtle.shamble": "lo cakyrespa cu dzidzu", "subtitles.entity.turtle.shamble_baby": "lo cifnu cakyrespa cu dzidzu", "subtitles.entity.turtle.swim": "lo cakyrespa cu limna", "subtitles.entity.vex.ambient": "Vex vexes", "subtitles.entity.vex.charge": "Vex shrieks", "subtitles.entity.vex.death": "lo cmapalci cu mrobi'o", "subtitles.entity.vex.hurt": "lo cmapalci cu cortu", "subtitles.entity.villager.ambient": "lo selsosyzda cu mliba'u", "subtitles.entity.villager.celebrate": "lo selsosyzda cu geirki'a", "subtitles.entity.villager.death": "lo selsosyzda cu mrobi'o", "subtitles.entity.villager.hurt": "lo selsosyzda cu cortu", "subtitles.entity.villager.no": "lo selsosyzda cu toltu'i", "subtitles.entity.villager.trade": "lo selsosyzda cu canja", "subtitles.entity.villager.work_armorer": ".i lo dabycakyzba cu gunka", "subtitles.entity.villager.work_butcher": "lo dalcatra cu gunka", "subtitles.entity.villager.work_cartographer": "lo catyzba cu gunka", "subtitles.entity.villager.work_cleric": "lo mafyzba cu gunka", "subtitles.entity.villager.work_farmer": ".i lo tercange cu gunka", "subtitles.entity.villager.work_fisherman": ".i lo fipkalte cu gunka", "subtitles.entity.villager.work_fletcher": "Fletcher works", "subtitles.entity.villager.work_leatherworker": ".i lo kapygukpre cu gunka", "subtitles.entity.villager.work_librarian": "lo ckuzdacre cu jibykai", "subtitles.entity.villager.work_mason": "Mason works", "subtitles.entity.villager.work_shepherd": "Shepherd works", "subtitles.entity.villager.work_toolsmith": ".i lo tcizba cu gunka", "subtitles.entity.villager.work_weaponsmith": ".i lo x<PERSON>ba cu gunka", "subtitles.entity.villager.yes": "lo selsosyzda cu tugni", "subtitles.entity.vindicator.ambient": "lo pacybandu cu selvo'a", "subtitles.entity.vindicator.celebrate": "Vindicator cheers", "subtitles.entity.vindicator.death": "lo pacybandu cu mrobi'o", "subtitles.entity.vindicator.hurt": "lo pacybandu cu cortu", "subtitles.entity.wandering_trader.ambient": "Wandering Trader mumbles", "subtitles.entity.wandering_trader.death": "Wandering Trader dies", "subtitles.entity.wandering_trader.disappeared": ".i lo litrycanja cu canci", "subtitles.entity.wandering_trader.drink_milk": ".i lo litrycanja cu pinxe lo ladru", "subtitles.entity.wandering_trader.drink_potion": ".i lo litrycanja cu pinxe lo zilpinxe", "subtitles.entity.wandering_trader.hurt": "Wandering Trader hurts", "subtitles.entity.wandering_trader.no": "Wandering Trader disagrees", "subtitles.entity.wandering_trader.reappeared": ".i lo litrycanja cu tolcanci", "subtitles.entity.wandering_trader.trade": "Wandering Trader trades", "subtitles.entity.wandering_trader.yes": "Wandering Trader agrees", "subtitles.entity.warden.agitated": "le visnalka'e cizda'u cu o'onai cmoni", "subtitles.entity.warden.ambient": "le visnalka'e cizda'u cu bratogykrixa", "subtitles.entity.warden.angry": "le visnalka'e cizda'u cu ka'enai jitro fengu", "subtitles.entity.warden.attack_impact": "Warden lands hit", "subtitles.entity.warden.death": "le visnalka'e cizda'u co'a morsi", "subtitles.entity.warden.dig": "le visnalka'e cizda'u co'a kakpa", "subtitles.entity.warden.emerge": "le visnalka'e cizda'u cu cpixa", "subtitles.entity.warden.heartbeat": "Warden's heart beats", "subtitles.entity.warden.hurt": "le visnalka'e cizda'u cu cortu", "subtitles.entity.warden.listening": "le visnalka'e cizda'u cu ganse", "subtitles.entity.warden.listening_angry": "le visnalka'e cizda'u cu o'onai ganse", "subtitles.entity.warden.nearby_close": "le visnalka'e cizda'u co'a jbize'a", "subtitles.entity.warden.nearby_closer": "le visnalka'e cizda'u cu jbize'a", "subtitles.entity.warden.nearby_closest": "le visnalka'e cizda'u cu jibni", "subtitles.entity.warden.roar": "le visnalka'e cizda'u co'a capsidba'u", "subtitles.entity.warden.sniff": "le visnalka'e cizda'u cu panzga", "subtitles.entity.warden.sonic_boom": "le visnalka'e cizda'u cu snagunta", "subtitles.entity.warden.sonic_charge": "le visnalka'e cizda'u cu le'o bajra", "subtitles.entity.warden.step": "le visnalka'e cizda'u cu stapa", "subtitles.entity.warden.tendril_clicks": "Warden's tendrils click", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "Witch giggles", "subtitles.entity.witch.celebrate": "Witch cheers", "subtitles.entity.witch.death": "lo termafyfe'i cu mrobi'o", "subtitles.entity.witch.drink": "lo termafyfe'i cu pinxe", "subtitles.entity.witch.hurt": "lo termafyfe'i cu cortu", "subtitles.entity.witch.throw": "lo termafyfe'i cu renro", "subtitles.entity.wither.ambient": "la me uiter. cu fengu", "subtitles.entity.wither.death": "la me uiter. mrobi'o", "subtitles.entity.wither.hurt": "la me uiter. cortu", "subtitles.entity.wither.shoot": "la me uiter. gunta", "subtitles.entity.wither.spawn": "la .uidr. co'a na cando", "subtitles.entity.wither_skeleton.ambient": "lo me la .uidr. bogygreku cu vrurapsut<PERSON>li", "subtitles.entity.wither_skeleton.death": "lo bogygreku me la uiter. cu mrobi'o", "subtitles.entity.wither_skeleton.hurt": "lo bogygreku me la uiter. cu cortu", "subtitles.entity.wolf.ambient": "Wolf pants", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "lo babno cu mrobi'o", "subtitles.entity.wolf.growl": "lo labno cu cmoni", "subtitles.entity.wolf.hurt": "lo labno cu cortu", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "Wolf shakes", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": ".i lo mivmro balxarju cu cmoni", "subtitles.entity.zoglin.angry": ".i lo mivmro balxarju cu fe'ucmo", "subtitles.entity.zoglin.attack": ".i lo mivmro balxarju cu gunta", "subtitles.entity.zoglin.death": ".i lo mivmro balxarju cu morsi", "subtitles.entity.zoglin.hurt": ".i lo mivmro balxarju cu cortu", "subtitles.entity.zoglin.step": ".i lo mivmro balxarju cu stapa", "subtitles.entity.zombie.ambient": "lo mivymro cu cmoni", "subtitles.entity.zombie.attack_wooden_door": ".i lo vorme cu desku", "subtitles.entity.zombie.break_wooden_door": ".i lo vorme cu porpi", "subtitles.entity.zombie.converted_to_drowned": ".i lo mivmro cu jacyjirm<PERSON>'o", "subtitles.entity.zombie.death": "lo mivymro cu mrobi'o", "subtitles.entity.zombie.destroy_egg": ".i lo sovda be lo cakyrespa cu porpi", "subtitles.entity.zombie.hurt": "lo mivymro cu cortu", "subtitles.entity.zombie.infect": "Zombie infects", "subtitles.entity.zombie_horse.ambient": "lo mivymro xirma cu krixa", "subtitles.entity.zombie_horse.death": "lo mivymro xirma cu mrobi'o", "subtitles.entity.zombie_horse.hurt": "lo mivymro xirma cu cortu", "subtitles.entity.zombie_villager.ambient": "lo mivymro selsosyzda cu cmoni", "subtitles.entity.zombie_villager.converted": ".i lo mivmro cmatcaxa'u cu se binxo", "subtitles.entity.zombie_villager.cure": ".i lo mivmro cmatcaxa'u cu binxo", "subtitles.entity.zombie_villager.death": "lo mivymro cu mrobi'o", "subtitles.entity.zombie_villager.hurt": "lo mivymro selsosyzda cu cortu", "subtitles.entity.zombified_piglin.ambient": ".i lo mivmro xajypre cu cmoni", "subtitles.entity.zombified_piglin.angry": ".i lo mivmro xajypre cu fe'ucmo", "subtitles.entity.zombified_piglin.death": ".i lo mivmro xajypre cu morsi", "subtitles.entity.zombified_piglin.hurt": ".i lo mivmro xajypre cu cortu", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "Ominous horn blares", "subtitles.item.armor.equip": "lo dabycalku cu seldasni", "subtitles.item.armor.equip_chain": "lo linsi dabycalku co'a se dasni", "subtitles.item.armor.equip_diamond": "lo tabjme dabycalku co'a se dasni", "subtitles.item.armor.equip_elytra": "lo gairna'i co'a se dasni", "subtitles.item.armor.equip_gold": "lo solji dabycalku co'a se dasni", "subtitles.item.armor.equip_iron": "lo tirse dabycalku co'a se dasni", "subtitles.item.armor.equip_leather": "lo revyskapi dabycalku co'a se dasni", "subtitles.item.armor.equip_netherite": ".i dasni lo dzimujyjinme badycalku", "subtitles.item.armor.equip_turtle": ".i dasni lo cakyrespa mapku", "subtitles.item.armor.equip_wolf": "lo labnybadycalku gau co'a se dasni", "subtitles.item.armor.unequip_wolf": "lo labnybadycalku gau co'u se dasni", "subtitles.item.axe.scrape": ".i lo ka'amru cu sraku", "subtitles.item.axe.strip": "lo ka'amru cu jai sepi'o pilvi'u", "subtitles.item.axe.wax_off": "lo lakse cu se guska", "subtitles.item.bone_meal.use": "lo bongu purmo cu se pilno", "subtitles.item.book.page_turn": "lo papri cu se fa'ergau", "subtitles.item.book.put": "lo cukta cu se punji", "subtitles.item.bottle.empty": ".i lo botpi cu kunti", "subtitles.item.bottle.fill": "lo botpi cu tisna", "subtitles.item.brush.brushing.generic": "ca'o brulu'i", "subtitles.item.brush.brushing.gravel": "ca'o brulu'i loi cmaro'i", "subtitles.item.brush.brushing.gravel.complete": "mo'u brulu'i loi cmaro'i", "subtitles.item.brush.brushing.sand": "ca'o brulu'i lo canre", "subtitles.item.brush.brushing.sand.complete": "mo'u brulu'i lo canre", "subtitles.item.bucket.empty": "lo batku cu se kutygau", "subtitles.item.bucket.fill": "lo baktu cu tisna", "subtitles.item.bucket.fill_axolotl": "lo axlte cu se setca", "subtitles.item.bucket.fill_fish": ".i lo finpe cu se kavbu", "subtitles.item.bucket.fill_tadpole": "kavbu lo verbanfi", "subtitles.item.bundle.drop_contents": "lo bakfu cu kunti", "subtitles.item.bundle.insert": "<PERSON><PERSON> dacti", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "lebna dacti", "subtitles.item.chorus_fruit.teleport": "lo kelci cu sukmu'u", "subtitles.item.crop.plant": "lo velcange cu se sombo", "subtitles.item.crossbow.charge": ".i lo seljicyce'a cu brebi'o", "subtitles.item.crossbow.hit": "lo celga'a cu tunta", "subtitles.item.crossbow.load": ".i lo seljicyce'a cu selplibre", "subtitles.item.crossbow.shoot": ".i lo seljicyce'a cu celbi'o", "subtitles.item.dye.use": "lo xinmo cu se punji", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "lo bolfagri cu se ke jelca sance", "subtitles.item.flintandsteel.use": "sance lo fagrokci fa'u gasta", "subtitles.item.glow_ink_sac.use": "lo carmi ke xinmo dakli cu se punji", "subtitles.item.goat_horn.play": "lo jirna kanba cu kelci", "subtitles.item.hoe.till": "lo terplixa cu se pilno", "subtitles.item.honey_bottle.drink": ".i tunlo", "subtitles.item.honeycomb.wax_on": "lo lakse cu se punji", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "lo xinmo dakli cu se punji", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": ".i lo fartci cu makybi'o", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "lo velcange cu se sombo", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "sance lo jinci", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "lo badgai cu se pilno", "subtitles.item.shovel.flatten": "lo canpa cu tolplo", "subtitles.item.spyglass.stop_using": "lo zgablaci cu tolba'o", "subtitles.item.spyglass.use": "lo zgablaci cu banro", "subtitles.item.totem.use": "lo nindode cu se pilno", "subtitles.item.trident.hit": "lo cibyterfroxa'i cu tunta", "subtitles.item.trident.hit_ground": "lo cibyba'erxa'i cu desku", "subtitles.item.trident.return": "lo cibyba'erxa'i cu selxru", "subtitles.item.trident.riptide": "lo cibyba'erxa'i cu sutmuvdu", "subtitles.item.trident.throw": "lo cibyba'erxa'i cu jimdaxsna", "subtitles.item.trident.thunder": "lo cibyterfroxa'i cu se lindi", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Wolf Armor takes damage", "subtitles.item.wolf_armor.repair": "Wolf Armor is repaired", "subtitles.particle.soul_escape": ".i lo pruxi cu cliva", "subtitles.ui.cartography_table.take_result": ".i ciska fi lo cartu", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": ".i lo jivbymi'i cu se pilno", "subtitles.ui.stonecutter.take_result": ".i lo rokyka'ami'i cu se pilno", "subtitles.weather.rain": "carvi sance", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "More Information", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "Always", "team.collision.never": "Never", "team.collision.pushOtherTeams": "Push other teams", "team.collision.pushOwnTeam": "<PERSON><PERSON> own team", "team.notFound": ".i nalselju'o gircu fa %s", "team.visibility.always": "roroi", "team.visibility.hideForOtherTeams": "Hide for other teams", "team.visibility.hideForOwnTeam": "Hide for own team", "team.visibility.never": "noroi", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Optional)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Advancement ID", "telemetry.property.client_id.title": "Client ID", "telemetry.property.client_modded.title": "<PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "Game Mode", "telemetry.property.game_version.title": "Game Version", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "New World", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Operating System", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Render Distance", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Server Type", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "<PERSON>", "telemetry_info.button.privacy_statement": "Privacy Statement", "telemetry_info.button.show_data": "View My Data", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Included Data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit system detected: this may prevent you from playing in the future as a 64-bit system will be required!", "title.32bit.deprecation.realms": "Minecraft will soon require a 64-bit system, which will prevent you from playing or using Realms on this device. You will need to manually cancel any Realms subscription.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "32-bit system detected", "title.credits": ".i la'o zy. Mojang AB zy. fukyzifpo'e .i .e'anai fairgau", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "sormei kelci co kloseltcana", "title.multiplayer.other": "sormei kelci co servero", "title.multiplayer.realms": "sormei kelci co .relmz.", "title.singleplayer": "Singleplayer", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "coi la %", "translation.test.invalid2": "coi la %s", "translation.test.none": "coi lo munje", "translation.test.world": "world", "trim_material.minecraft.amethyst": "Amethyst Material", "trim_material.minecraft.copper": "Copper Material", "trim_material.minecraft.diamond": "Diamond Material", "trim_material.minecraft.emerald": "Emerald Material", "trim_material.minecraft.gold": "Gold Material", "trim_material.minecraft.iron": "Iron Material", "trim_material.minecraft.lapis": "Lapis Material", "trim_material.minecraft.netherite": "Netherite Material", "trim_material.minecraft.quartz": "Quartz Material", "trim_material.minecraft.redstone": "Redstone Material", "trim_material.minecraft.resin": "Resin Material", "trim_pattern.minecraft.bolt": "Bolt Armor Trim", "trim_pattern.minecraft.coast": "xaskoi ke dabycalku jadni", "trim_pattern.minecraft.dune": "cancma<PERSON>'a ke dabycalku jadni", "trim_pattern.minecraft.eye": "kanla ke dabycalku jadni", "trim_pattern.minecraft.flow": "Flow Armor Trim", "trim_pattern.minecraft.host": "selse'u ke dabycalku jadni", "trim_pattern.minecraft.raiser": "ba'or<PERSON>'e ke dabycalku jadni", "trim_pattern.minecraft.rib": "cuty<PERSON>'u ke dabycalku jadni", "trim_pattern.minecraft.sentry": "zgaku'i ke dabycalku jadni", "trim_pattern.minecraft.shaper": "tamgau ke dabycalku jadni", "trim_pattern.minecraft.silence": "kamsma ke dabycalku jadni", "trim_pattern.minecraft.snout": "xaj<PERSON><PERSON><PERSON> ke dabycalku jadni", "trim_pattern.minecraft.spire": "kond<PERSON>i'u ke dabycalku jadni", "trim_pattern.minecraft.tide": "ctaru ke dabycalku jadni", "trim_pattern.minecraft.vex": "cmapalci ke dabycalku jadni", "trim_pattern.minecraft.ward": "pifydi'u ke dabycalku jadni", "trim_pattern.minecraft.wayfinder": "lutygi'a ke dabycalku jadni", "trim_pattern.minecraft.wild": "cilce ke dabycalku jadni", "tutorial.bundleInsert.description": "Right Click to add items", "tutorial.bundleInsert.title": ".i ko pilno lo bafydakli", "tutorial.craft_planks.description": "The recipe book can help", "tutorial.craft_planks.title": "Craft wooden planks", "tutorial.find_tree.description": ".i darxi da mu'i lo nu cpacu lo mudri", "tutorial.find_tree.title": ".i sisku lo tricu", "tutorial.look.description": ".i pilno lo do xanyuidje mu'i lo nu carna", "tutorial.look.title": ".i mo'ine'a catlu", "tutorial.move.description": "Jump with %s", "tutorial.move.title": "Move with %s, %s, %s and %s", "tutorial.open_inventory.description": "Press %s", "tutorial.open_inventory.title": "Open your inventory", "tutorial.punch_tree.description": "Hold down %s", "tutorial.punch_tree.title": ".i daspo lo tricu", "tutorial.socialInteractions.description": "ko danre %s sau lonu kalri", "tutorial.socialInteractions.title": "Social Interactions", "upgrade.minecraft.netherite_upgrade": "me la .nedyrit. xagzengau"}