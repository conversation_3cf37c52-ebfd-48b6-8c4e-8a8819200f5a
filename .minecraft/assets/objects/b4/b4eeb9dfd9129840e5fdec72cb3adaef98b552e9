{"accessibility.onboarding.accessibility.button": "Inpostasion de acesìbiłidà...", "accessibility.onboarding.screen.narrator": "Struca Invio par ativar el narador", "accessibility.onboarding.screen.title": "Benrivà so Minecraft!\n\nVutu ativar el Narador o vardar łe Inpostasion de acesibiłidà?", "addServer.add": "<PERSON><PERSON>", "addServer.enterIp": "Inderiso de'l server", "addServer.enterName": "Nome de'l server", "addServer.resourcePack": "<PERSON><PERSON><PERSON> server", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "Ativadi", "addServer.resourcePack.prompt": "Sora conferma", "addServer.title": "Modìfega łe informasion de'l server", "advMode.command": "Comando de'l bloco", "advMode.mode": "Modalità", "advMode.mode.auto": "Da novo", "advMode.mode.autoexec.bat": "Senpre ativo", "advMode.mode.conditional": "Condisional", "advMode.mode.redstone": "Inpulso", "advMode.mode.redstoneTriggered": "Ghe vol Redstone", "advMode.mode.sequence": "Cadena", "advMode.mode.unconditional": "Mìa condisional", "advMode.notAllowed": "Te ghè da èsar un operador in modałità Creativa", "advMode.notEnabled": "I blochi comando no i'é mia abiłitadi inte 'sto server", "advMode.previousOutput": "Output presedente", "advMode.setCommand": "Ségneghe un comando al bloco", "advMode.setCommand.success": "Comando segnà: %s", "advMode.trackOutput": "Vedi l'esito", "advMode.triggering": "Ativasion", "advMode.type": "Tipo", "advancement.advancementNotFound": "Progreso mìa conosesto: %s", "advancements.adventure.adventuring_time.description": "Descoverzi tuti i biomi", "advancements.adventure.adventuring_time.title": "Ora de aventure", "advancements.adventure.arbalistic.description": "Copa sincue mob difarente co un sol tiro de bałestra", "advancements.adventure.arbalistic.title": "<PERSON> da bałestrier", "advancements.adventure.avoid_vibration.description": "Cùciate visin a un Sensor de sculk o Sorvejante par no farte zgamar", "advancements.adventure.avoid_vibration.title": "Cucià 100", "advancements.adventure.blowback.description": "Copa on Breeze con un colpo devià de Carga de Vento", "advancements.adventure.blowback.title": "Contracolpo", "advancements.adventure.brush_armadillo.description": "Ciapa on scudo de armadillo da on armadillo doparando on penelo", "advancements.adventure.brush_armadillo.title": "Xeo mia on scudo?", "advancements.adventure.bullseye.description": "Brinca in pieno el çentro d'un bloco barsajo da almanco 30 metri de distansa", "advancements.adventure.bullseye.title": "Brincà in pieno", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fai un vaso decorao co 4 Tochi de Ceramica", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauro acc<PERSON>o", "advancements.adventure.crafters_crafting_crafters.description": "Sta visin a on costrutore quando el costruise on costrutore", "advancements.adventure.crafters_crafting_crafters.title": "Costrutori che costruise costrutori", "advancements.adventure.fall_from_world_height.description": "Casca da l'cel (limite de costrusion) al fondo de l'móndo e soravivi", "advancements.adventure.fall_from_world_height.title": "Grote e Scoglìere", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": "Defendi co suceso un viłazo da na incursion", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON> viła<PERSON>", "advancements.adventure.honey_block_slide.description": "Zbrisia łongo un bloco de mel par prevènjar el dano in cascada", "advancements.adventure.honey_block_slide.title": "Situasion tacołenta", "advancements.adventure.kill_a_mob.description": "Copa un mostro ostil", "advancements.adventure.kill_a_mob.title": "Casador de mostri", "advancements.adventure.kill_all_mobs.description": "Copa a'l manco un mostro ostil par sorta", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Copa o creatura visin a un Catalisatore de Sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "El se spande", "advancements.adventure.lighten_up.description": "Grata on bulbo de ràme co na menàra pa' farlo diventar pì lusido", "advancements.adventure.lighten_up.title": "Fame ciaro", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protezi un Paesan da na scossa indesiderada sensa inpissar un fogo", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Alta tension", "advancements.adventure.minecraft_trials_edition.description": "Meti pìe n'tela Camara delle Prove", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Edission dele Prove", "advancements.adventure.ol_betsy.description": "Tira co na Balestra", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON> ve<PERSON>", "advancements.adventure.overoverkill.description": "Fa 50 cori de dano co un colpo solo doparando la massa", "advancements.adventure.overoverkill.title": "Stra e stracopà", "advancements.adventure.play_jukebox_in_meadows.description": "Fa' revivere i Prà con el suono de mùzega utilisando un Jukebox", "advancements.adventure.play_jukebox_in_meadows.title": "Són de la mùzega", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Leggite la potensa del segnale de na Libreria usando el comparatore", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "La potensa de i libri", "advancements.adventure.revaulting.description": "Sbloca la cripta nefasta co na ciave della prova nefasta", "advancements.adventure.revaulting.title": "Nefastante", "advancements.adventure.root.description": "Aventura, esplorasion e conbatimento", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Rusa un bloco sospeto par otegner un tocheto de seràmega", "advancements.adventure.salvage_sherd.title": "Respeto par i resti", "advancements.adventure.shoot_arrow.description": "Ciapa calcosa co' na Fresa", "advancements.adventure.shoot_arrow.title": "Bona mira!", "advancements.adventure.sleep_in_bed.description": "Sonéca int'un Leto par canbiar el to ponto de spawn", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> ben seto!", "advancements.adventure.sniper_duel.description": "Copa un schèłetro da a'l manco 50 metri", "advancements.adventure.sniper_duel.title": "Dueło intra tiradori", "advancements.adventure.spyglass_at_dragon.description": "Varda l'Enderdrago co un Canocial", "advancements.adventure.spyglass_at_dragon.title": "<PERSON>elo un reoplan?", "advancements.adventure.spyglass_at_ghast.description": "Varda un Ghast traverso de un Canocial", "advancements.adventure.spyglass_at_ghast.title": "<PERSON>elo un balonç<PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "Varda un Papagal co un Canocial", "advancements.adventure.spyglass_at_parrot.title": "<PERSON><PERSON> un osel?", "advancements.adventure.summon_iron_golem.description": "Èvoga un gołem de fero par jutar deféndar el paeze", "advancements.adventure.summon_iron_golem.title": "Na man de juto", "advancements.adventure.throw_trident.description": "Tira ła Forca indoso a calcosa.\nN. B.: butar via ła to ùnega arma no l'é na gran pensada.", "advancements.adventure.throw_trident.title": "Un scherso da putełi", "advancements.adventure.totem_of_undying.description": "Dòpara un totem de l'inmortałidà par scanpar ła morte", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Scanbia co' sucesso co' un paesan", "advancements.adventure.trade.title": "Che afar!", "advancements.adventure.trade_at_world_height.description": "Scanbia con un Paezan al limite de costrusion", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Àplega 'sti modeli da fàvaro almanco na volta: ponta, grugno, costa, vardia, silensio, tormento, mar<PERSON>a, guida", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Forjar co stile", "advancements.adventure.trim_with_any_armor_pattern.description": "Indora na corasa so'l banco de'l fàvaro", "advancements.adventure.trim_with_any_armor_pattern.title": "Crèate na siera nova", "advancements.adventure.two_birds_one_arrow.description": "Copa do phantom co na Fresa parforante", "advancements.adventure.two_birds_one_arrow.title": "<PERSON> ozełi, na soła fresa", "advancements.adventure.under_lock_and_key.description": "Sbloca na camara blindà co na ciave dea prova", "advancements.adventure.under_lock_and_key.title": "Soto ciave e ciavistelo", "advancements.adventure.use_lodestone.description": "Dòpara na Bùssola su la Magnetite", "advancements.adventure.use_lodestone.title": "...che perdesta gavéa la tramontana...", "advancements.adventure.very_very_frightening.description": "Colpissi un paesan co' un sciantiso", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "Copa el capitan de 'na incursion.\nSarìa mejo considarar de restar distante da'l viłazo par el momento...", "advancements.adventure.voluntary_exile.title": "Ezìłeo vołontario", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camina su la Neve polvarosa... sensa sfondarghe drento", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Li<PERSON><PERSON> come 'na foja", "advancements.adventure.who_needs_rockets.description": "Dopra na Carga de Vento par lansarte in aria de 8 blochi", "advancements.adventure.who_needs_rockets.title": "Chi gà bisogno de missili?", "advancements.adventure.whos_the_pillager_now.description": "Daghe al sachejador el saor de ła so stesa medezina", "advancements.adventure.whos_the_pillager_now.title": "El sachejador sachejà", "advancements.empty": "Par che ghe sipia njente cuà...", "advancements.end.dragon_breath.description": "Arbina el Supio de Dragon inte n'Anpola", "advancements.end.dragon_breath.title": "Voto na mentina?", "advancements.end.dragon_egg.description": "<PERSON><PERSON> un ovo de dragon", "advancements.end.dragon_egg.title": "Ła nova zenarasion", "advancements.end.elytra.description": "Cata un Ely<PERSON>", "advancements.end.elytra.title": "Sol el siel l'é el łìmite", "advancements.end.enter_end_gateway.description": "Scanpa da ła ìzoła prinsipal", "advancements.end.enter_end_gateway.title": " Dov'eło el portal?", "advancements.end.find_end_city.description": "Va pur drento, 'sa podarìa mai sucédar?", "advancements.end.find_end_city.title": "Ła sità a ła fin del zugo", "advancements.end.kill_dragon.description": "Bona fortuna", "advancements.end.kill_dragon.title": "Ł<PERSON>bara <PERSON>", "advancements.end.levitate.description": "Łè<PERSON><PERSON> elto 50 blochi par i atachi de un shulker", "advancements.end.levitate.title": "Che vista da chi desora!", "advancements.end.respawn_dragon.description": "Èvoga da novo el Enderdragon", "advancements.end.respawn_dragon.title": "Ła fin... da novo...", "advancements.end.root.description": "O el prinsipio?", "advancements.end.root.title": "ŁA FIN", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fai butar na torta su un Bloco sonoro da n'Allay", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON> de compleano", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON>i portar n'ozeto da un Allay", "advancements.husbandry.allay_deliver_item_to_player.title": "Te ghe n'amico", "advancements.husbandry.axolotl_in_a_bucket.description": "Ciapa un Axolotl co' un Secio", "advancements.husbandry.axolotl_in_a_bucket.title": "El predator piassè c<PERSON>lo", "advancements.husbandry.balanced_diet.description": "<PERSON>ja tuto cuel che se pol mandar zo, anca se'l te fa mìa ben", "advancements.husbandry.balanced_diet.title": "<PERSON>uel che no st<PERSON><PERSON><PERSON>, ingrasa", "advancements.husbandry.breed_all_animals.description": "<PERSON><PERSON><PERSON> tute łe sorte de anemałi!", "advancements.husbandry.breed_all_animals.title": "A do a do", "advancements.husbandry.breed_an_animal.description": "Arleva do anemałi de ła stesa spesie", "advancements.husbandry.breed_an_animal.title": "I papagałi e i barbastrełi", "advancements.husbandry.complete_catalogue.description": "<PERSON>ès<PERSON>ga tute le sorte de Gati!", "advancements.husbandry.complete_catalogue.title": "Un gatałogo conpleto", "advancements.husbandry.feed_snifflet.description": "<PERSON>gh<PERSON> da magnar a un snasadorin", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "Pesca un pese", "advancements.husbandry.fishy_business.title": "Afari de pese", "advancements.husbandry.froglights.description": "Tieni tuti i tipi de Lumerana in tò inventario", "advancements.husbandry.froglights.title": "Cò i nostri poderi combinà!", "advancements.husbandry.kill_axolotl_target.description": "Arlèate co un Axolotl e vensi na bataja", "advancements.husbandry.kill_axolotl_target.title": "L'Amistà Cura Tuto!", "advancements.husbandry.leash_all_frog_variants.description": "Lega tuti i tipi de Rana cò'n zguinsajo", "advancements.husbandry.leash_all_frog_variants.title": "In sentro cò'i fioi", "advancements.husbandry.make_a_sign_glow.description": "Fa' briłar el testo de cualsesìa cartel", "advancements.husbandry.make_a_sign_glow.title": "Impisa sta luce!", "advancements.husbandry.netherite_hoe.description": "Dòpara un Lingoto de Netherite par mejorar na Sapa e refleti su le to sielte de vita", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON> pasion", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON> un Ovo de Snasador", "advancements.husbandry.obtain_sniffer_egg.title": "El sa da bon", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Bevi!", "advancements.husbandry.plant_any_sniffer_seed.description": "Pianta na somensa de snasador", "advancements.husbandry.plant_any_sniffer_seed.title": "Pianta del passà", "advancements.husbandry.plant_seed.description": "Pianta de ła somensa e vàrdeła crésar", "advancements.husbandry.plant_seed.title": "I crese cusì in presia...", "advancements.husbandry.remove_wolf_armor.description": "Cava n'armadura del lupo da on lupo doprando le forbexe", "advancements.husbandry.remove_wolf_armor.title": "Idea forbesante", "advancements.husbandry.repair_wolf_armor.description": "Ripara completamente n'Armadura del Lupo doparando on Scudo de Armadillo", "advancements.husbandry.repair_wolf_armor.title": "Come novo", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Monta in Barca co' na Cavara", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Fa Come Che Te Pare!", "advancements.husbandry.root.description": "El mondo l'é pien de amighi e de manjar", "advancements.husbandry.root.title": "E po tera... e po àcua", "advancements.husbandry.safely_harvest_honey.description": "Dopera un Falò per tirare sù el Miele da un Alveare usando na Botiglietta de Vero sensa farghe male ale Api", "advancements.husbandry.safely_harvest_honey.title": "Te zzzi el benvenjù!", "advancements.husbandry.silk_touch_nest.description": "Movi un Avearo con 3 Ave drento, doparando Toco de Seda", "advancements.husbandry.silk_touch_nest.title": "AVArésito catà un posto mejor?", "advancements.husbandry.tactical_fishing.description": "Pesca un Pese... sensa ła Cana da pesca!", "advancements.husbandry.tactical_fishing.title": "Pesca tàtega", "advancements.husbandry.tadpole_in_a_bucket.description": "Ciapa un Axolotl co' un Secio", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "Domèstega un anemal", "advancements.husbandry.tame_an_animal.title": "Mejori amighi par senpre", "advancements.husbandry.wax_off.description": "Cava ła cera da el bloco de rame!", "advancements.husbandry.wax_off.title": "Decerà", "advancements.husbandry.wax_on.description": "Dopara un favo so un bloco de rame!", "advancements.husbandry.wax_on.title": "Incerà", "advancements.husbandry.whole_pack.description": "Adoméstica ogni una de le varianti de lupo", "advancements.husbandry.whole_pack.title": "Tuto el s'ciapo", "advancements.nether.all_effects.description": "Abi tuti i efeti aplegadi a'l mèdemo tenpo", "advancements.nether.all_effects.title": "Come senti rivài chive?", "advancements.nether.all_potions.description": "Abi tuti i efeti de posion aplegadi inte'l steso tenpo", "advancements.nether.all_potions.title": "Un cocktail rabiozo", "advancements.nether.brew_potion.description": "Spina na posion", "advancements.nether.brew_potion.title": "Birarìa artizanal", "advancements.nether.charge_respawn_anchor.description": "Carga l'àncora de respawn a'l màsimo", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON><PERSON> come i gati!", "advancements.nether.create_beacon.description": "Costruisi e piasa un Faro", "advancements.nether.create_beacon.title": "Luxe sipia!", "advancements.nether.create_full_beacon.description": "Porta un Faro a'l poder màsimo", "advancements.nether.create_full_beacon.title": "Guardian de'l faro", "advancements.nether.distract_piglin.description": "Distrài un piglin co'l oro", "advancements.nether.distract_piglin.title": "Come Che'l Scianteza!", "advancements.nether.explore_nether.description": "Esplora tuti i biomi de'l Nether", "advancements.nether.explore_nether.title": "Che Posto de Bojo!", "advancements.nether.fast_travel.description": "Dòpara el Nether par spostarte de 7km inte l'Overworld", "advancements.nether.fast_travel.title": "Boła subspasial", "advancements.nether.find_bastion.description": "Intra in łe vestize d'un bastion", "advancements.nether.find_bastion.title": "I bei veci tenpi", "advancements.nether.find_fortress.description": "Fate strada inte na fortesa de'l Nether", "advancements.nether.find_fortress.title": "Na fortesa terìbiłe", "advancements.nether.get_wither_skull.description": "<PERSON><PERSON> el tescio de un schèłetro wither", "advancements.nether.get_wither_skull.title": "Schèłetri inte'l armar", "advancements.nether.loot_bastion.description": "Sacheza un Baùl in le vestize d'un bastion", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Otien na armadura conpleta in netherite", "advancements.nether.netherite_armor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.title": "<PERSON>ont<PERSON> in le fondesse", "advancements.nether.obtain_blaze_rod.description": "Lezarissi un blaze de la só verga", "advancements.nether.obtain_blaze_rod.title": "A se schersa mìa col fogo", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.title": "Chi Zeło Che Taja Sèołe?", "advancements.nether.return_to_sender.description": "Copa un ghast co' na bała de fogo", "advancements.nether.return_to_sender.title": "De volta a'l mandante", "advancements.nether.ride_strider.description": "Monta un caminador co' un bacheto e fongo strùpio", "advancements.nether.ride_strider.title": "'Sta Barca ła Ga łe Ganbe", "advancements.nether.ride_strider_in_overworld_lava.description": "Acarreja un Strider por un spassata su un lago de lava en el Sopramondo", "advancements.nether.ride_strider_in_overworld_lava.title": "El Pare Come A Caza", "advancements.nether.root.description": "Pòrtete i vestì da istà", "advancements.nether.root.title": "<PERSON>", "advancements.nether.summon_wither.description": "Èvoga el Wither", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Recùpara un ghast da'l <PERSON><PERSON>, méneło san e salvo inte l'Overworld... e po fało fora", "advancements.nether.uneasy_alliance.title": "Falsa <PERSON>", "advancements.nether.use_lodestone.description": "Dòpara 'na Bùsola su la mangnetite", "advancements.nether.use_lodestone.title": "No me gò 'ncora perso", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Indebolissi e po cura un paesan zonbi", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Desvia na palotola co uno Scudo", "advancements.story.deflect_arrow.title": "Ancó no, grasie", "advancements.story.enchant_item.description": "Incanta un ozeto su un banco da incantamenti", "advancements.story.enchant_item.title": "Incantador", "advancements.story.enter_the_end.description": "Intra inte'l portal de'l End", "advancements.story.enter_the_end.title": "Eła ła fin?", "advancements.story.enter_the_nether.description": "Costruisi, inpisa e travesa un portal de'l End", "advancements.story.enter_the_nether.title": "Vu ch'intrè ła speransa łasè indrìo...", "advancements.story.follow_ender_eye.description": "Vaghe drio a un ocio de ender", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON> spia", "advancements.story.form_obsidian.description": "Ciapa un bloco de Osidiana", "advancements.story.form_obsidian.title": "Vero de dragon", "advancements.story.iron_tools.description": "Mejora el to Picon", "advancements.story.iron_tools.title": "Eło mia un picon de fero!?", "advancements.story.lava_bucket.description": "Inpinisi un Secio co Lava", "advancements.story.lava_bucket.title": "Ocio ai diełi! ", "advancements.story.mine_diamond.description": "<PERSON><PERSON> diamanti", "advancements.story.mine_diamond.title": "<PERSON><PERSON><PERSON>!", "advancements.story.mine_stone.description": "Mina de la Piera co'l to novo Picon", "advancements.story.mine_stone.title": "Età de ła piera", "advancements.story.obtain_armor.description": "Protèjite co na parte de armadura de fero", "advancements.story.obtain_armor.title": "Vestìsete", "advancements.story.root.description": "El cor e l'istoria de'l zugo", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "L'armadura de Diamante la salva le vite", "advancements.story.shiny_gear.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.smelt_iron.description": "Fondisi un lingoto de Fero", "advancements.story.smelt_iron.title": "Acuizisi tecnołojia", "advancements.story.upgrade_tools.description": "Costruisi un Picon mejiore", "advancements.story.upgrade_tools.title": "Mejorìe in vista", "advancements.toast.challenge": "Desfida conpletada!", "advancements.toast.goal": "Obietivo razonto!", "advancements.toast.task": "Progreso conpìo!", "argument.anchor.invalid": "Pozision de ancorazo de'l entità mìa vàłida: %s", "argument.angle.incomplete": "Inconpleto (previsti 1 argomenti de ła rotasion)", "argument.angle.invalid": "Canton mì vàlido", "argument.block.id.invalid": "Bloco mìa conosesto: %s", "argument.block.property.duplicate": "Ła propietà \"%s\" de'l bloco %s ła pol èsar inpostada na volta soła", "argument.block.property.invalid": "El bloco %s no'l aceta \"%s\" par ła propietà \"%s\"", "argument.block.property.novalue": "Previsto un vałor par ła propietà \"%s\" de'l bloco %s", "argument.block.property.unclosed": "Previsto ] in sarada de łe proprietà de'l bloco", "argument.block.property.unknown": "El bloco %s no'l ga ła propietà \"%s\"", "argument.block.tag.disallowed": "Łe tag no łe ze consentide chive, ma sol blochi reałi", "argument.color.invalid": "<PERSON><PERSON><PERSON> mìa con<PERSON>: '%s'", "argument.component.invalid": "Conponente de ła chat mìa vàłido: %s", "argument.criteria.invalid": "Criterio mìa conosesto: %s", "argument.dimension.invalid": "Dimension mìa conosesta: %s", "argument.double.big": "El vałor double no'l pol èsar major de %s, (catà %s)", "argument.double.low": "El vałor double no'l pol èsar minor de %s, (catà %s)", "argument.entity.invalid": "Nome o UUID mìa vàłido", "argument.entity.notfound.entity": "Njisuna entidà l'é stada catada", "argument.entity.notfound.player": "<PERSON><PERSON><PERSON> zu<PERSON> l'é stà catà", "argument.entity.options.advancements.description": "Zugadori co' determenadi progresi", "argument.entity.options.distance.description": "<PERSON><PERSON><PERSON> da l'entidà", "argument.entity.options.distance.negative": "Ła distansa no ła pol mìa èsar negativa", "argument.entity.options.dx.description": "Entità intra x e x + dx", "argument.entity.options.dy.description": "Entidà intra y e y + dy", "argument.entity.options.dz.description": "Entità intra z e z + dz", "argument.entity.options.gamemode.description": "Ziogadori co la modalita de ziogo", "argument.entity.options.inapplicable": "L'opsion '%s' ła xe mìa aplicàbiłe cuà", "argument.entity.options.level.description": "Łivel de espariensa", "argument.entity.options.level.negative": "El łivel no'l pol mìa èsar negativo", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>si<PERSON> de entidà da restituir", "argument.entity.options.limit.toosmall": "El łìmite el ga da èsar a'l manco 1", "argument.entity.options.mode.invalid": "Modałità de zugo mìa conosesta o mìa vàłida: %s", "argument.entity.options.name.description": "Nome de l'entidà", "argument.entity.options.nbt.description": "Entidà co' un determenà NBT", "argument.entity.options.predicate.description": "Predicà parsonałizà", "argument.entity.options.scores.description": "Entidà co' un determenà pontezo", "argument.entity.options.sort.description": "Ordenamento de łe entidà", "argument.entity.options.sort.irreversible": "Tipo de orientamento mìa conosesto o mìa vàłido: %ì", "argument.entity.options.tag.description": "Entidà co' un determenà tag", "argument.entity.options.team.description": "Entidà de na determenada scuadra", "argument.entity.options.type.description": "Entidà de un determenà tipo", "argument.entity.options.type.invalid": "Tipo de entidà mia conosesto o mia vàłido: %s", "argument.entity.options.unknown": "Opsion mìa conosesta: %s", "argument.entity.options.unterminated": "Prevista fin de łe opsion", "argument.entity.options.valueless": "Previsto un vałor par l'opsion \"%s\"", "argument.entity.options.x.description": "Pozision x", "argument.entity.options.x_rotation.description": "Rotasion x de ła entità", "argument.entity.options.y.description": "Pozision y", "argument.entity.options.y_rotation.description": "Rotasion y de ła entità", "argument.entity.options.z.description": "Pozision z", "argument.entity.selector.allEntities": "<PERSON><PERSON> łe entid<PERSON>", "argument.entity.selector.allPlayers": "<PERSON><PERSON> i <PERSON>", "argument.entity.selector.missing": "Tipo de sełetor che manca", "argument.entity.selector.nearestEntity": "Entidà pì visina", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON> p<PERSON> visin", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>a <PERSON>", "argument.entity.selector.randomPlayer": "Zugador a cazo", "argument.entity.selector.self": "Entidà atual", "argument.entity.selector.unknown": "Tipo de sełetor mia conosesto: %s", "argument.entity.toomany": "L'é consentida sol na entidà, ma el sełetor doparà el de parmete anca piasè de una", "argument.enum.invalid": "Valor mìa valido \"%s\"", "argument.float.big": "El float no'l pol èsar major de %s, (catà %s)", "argument.float.low": "El vałor float no'l pol èsar minor de %s, (catà %s)", "argument.gamemode.invalid": "Modalità de ziogo invalida: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "ID mìa vàłido", "argument.id.unknown": "ID mìa conosesto: %s", "argument.integer.big": "El intiero no'l pol èsar major de %s, (catà %s)", "argument.integer.low": "El intiero no'l pol èsar minor de %s, (catà %s)", "argument.item.id.invalid": "Ozeto mìa conosesto: %s", "argument.item.tag.disallowed": "Łe tag no łe ze consentide chive, ma sol ozeti reałi", "argument.literal.incorrect": "Previsto un vałor łetaral %s", "argument.long.big": "El vałor long no'l pol èsar major de %s, (catà %s)", "argument.long.low": "El vałor long no'l pol èsar minor de %s, (catà %s)", "argument.message.too_long": "El mesagio de chat xé masa longo (%s > massimi %s carateri)", "argument.nbt.array.invalid": "Tipo de array mìa vàłido: %s", "argument.nbt.array.mixed": "Inposìbiłe inserir %s in %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "Prevista na ciave", "argument.nbt.expected.value": "Previsto un vałor", "argument.nbt.list.mixed": "Inposìbiłe inserir %s inte ła łista de %s", "argument.nbt.trailing": "<PERSON><PERSON> fin<PERSON>i mìa previsti", "argument.player.entities": "'Sto comando el pol èsar doparà sol su zugadori, ma el sełetor doparà el parmete altre entidà", "argument.player.toomany": "L'é consentìo sol un zugador, ma el sełetor doparà el de parmete anca piasè de uno", "argument.player.unknown": "El zugador l'é inezistente", "argument.pos.missing.double": "Prevista na coordenada", "argument.pos.missing.int": "Prevista ła pozision de un bloco", "argument.pos.mixed": "Inposìbiłe doparar na conbinasion de coordenade globałi e locałi (^ el va doparà da tute o da njisuna)", "argument.pos.outofbounds": "Sta posizion la zé fora dai confini lèçiti.", "argument.pos.outofworld": "'Sta posizion l'é fora da'l mondo!", "argument.pos.unloaded": "'Sta pozision no ła ze cargada", "argument.pos2d.incomplete": "Inconpleto (previsti 2 argomenti de ła rotasion)", "argument.pos3d.incomplete": "Inconpleto (previste 3 coordenade)", "argument.range.empty": "Previsto un vałor o un intarval de vałori", "argument.range.ints": "I ze consentidi sol nùmari intieri, nò de<PERSON>i", "argument.range.swapped": "El mìnimo no'l pol èsar mazor de'l màsimo", "argument.resource.invalid_type": "L’ elemento '%s' ha el tipo falao de '%s' (spetao '%s')", "argument.resource.not_found": "Inposibile catar l'elemento '%s' de tipo '%s'", "argument.resource_or_id.failed_to_parse": "No se ga podesto analizar l'strutura:%s", "argument.resource_or_id.invalid": "Id o tag invàlido", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "Nisuna corispondensa par el sełetor '%s' di sorta '%s'", "argument.resource_tag.invalid_type": "El tag '%s' ha el tipo falao de '%s' (spetao '%s')", "argument.resource_tag.not_found": "Impossibile trovare el tag '%s' del '%s'", "argument.rotation.incomplete": "Inconpleto (previsti 2 argomenti de ła rotasion)", "argument.scoreHolder.empty": "Inposìbiłe catar posesori de pontezo partinenti", "argument.scoreboardDisplaySlot.invalid": "Pozision mìa conosesta: %s", "argument.style.invalid": "Stile invalido: %s", "argument.time.invalid_tick_count": "El contezo d'i tick el ga da èsar un nùmaro mìa negativo", "argument.time.invalid_unit": "Unità mìa vàłida", "argument.time.tick_count_too_low": "El conto dei tick no'l pol èsar minor de %s (catà %s)", "argument.uuid.invalid": "UUID mìa vàłido", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Tag de blochi mìa conosesto: %s", "arguments.function.tag.unknown": "Tag in funsion mìa conosesto: %s", "arguments.function.unknown": "Funsion mìa conosesta: %s", "arguments.item.component.expected": "Se spetava on conponente oggetto", "arguments.item.component.malformed": "Componente '%s' malformà: '%s'", "arguments.item.component.repeated": "El componente oggetto '%s' l'è stà ripetùo, ma solo on valore po esare specificà", "arguments.item.component.unknown": "Componente oggetto '%s' sconosùo", "arguments.item.malformed": "Oggetto malformà: '%s'", "arguments.item.overstacked": "El ozeto %s el pol èsar inpiłà fin a %s", "arguments.item.predicate.malformed": "Predicate '%s' malformà: '%s'", "arguments.item.predicate.unknown": "Predicà mìa conosesto: '%s'", "arguments.item.tag.unknown": "Tag de ozeti mìa conosesto: %s", "arguments.nbtpath.node.invalid": "Parcorso de'l ełemento NBT mìa vàłido", "arguments.nbtpath.nothing_found": "L'é stà catà njisun ełemento corespondente a %s", "arguments.nbtpath.too_deep": "El NBT risultà zé masa anidà", "arguments.nbtpath.too_large": "El NBT risultà zé masa grande", "arguments.objective.notFound": "Obietivo mìa conosesto: \"%s\"", "arguments.objective.readonly": "El obietivo %s l'é de soła łetura", "arguments.operation.div0": "Inposìbiłe divìdar par zero", "arguments.operation.invalid": "Oparasion mìa vàłida", "arguments.swizzle.invalid": "Swizzle mìa v<PERSON>, prevista na conbinasion de \"x\", \"y\" e \"z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Duresa de l'armadura", "attribute.name.attack_damage": "<PERSON>", "attribute.name.attack_knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>aco", "attribute.name.attack_speed": "Zveltesa de ataco", "attribute.name.block_break_speed": "Zveltesa de spacatura del bloco", "attribute.name.block_interaction_range": "Distansa de interasion del bloco", "attribute.name.burning_time": "<PERSON><PERSON>", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Distansa de interasion de la entità", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Moltiplicador de'l dano da cascada", "attribute.name.flying_speed": "Sveltessa de volo", "attribute.name.follow_range": "<PERSON><PERSON> de inseguimento de łe creature", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Duresa de l'armadura", "attribute.name.generic.attack_damage": "<PERSON>", "attribute.name.generic.attack_knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>aco", "attribute.name.generic.attack_speed": "Zveltesa de ataco", "attribute.name.generic.block_interaction_range": "Distansa de interasion del bloco", "attribute.name.generic.burning_time": "<PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Distansa de interasion de la entità", "attribute.name.generic.explosion_knockback_resistance": "Resistensa al contracolpo de le esplosiòn", "attribute.name.generic.fall_damage_multiplier": "Moltiplicador de'l dano da cascada", "attribute.name.generic.flying_speed": "Sveltessa de volo", "attribute.name.generic.follow_range": "<PERSON><PERSON> de riłeva<PERSON> de łe <PERSON>", "attribute.name.generic.gravity": "Gravità", "attribute.name.generic.jump_strength": "Forsa de salto", "attribute.name.generic.knockback_resistance": "Rezistensa a'l rebàtar", "attribute.name.generic.luck": "Fortuna", "attribute.name.generic.max_absorption": "Asorbimento masimo", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON> màsi<PERSON>", "attribute.name.generic.movement_efficiency": "Eficiensa de movimento", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "Bonus de osigeno", "attribute.name.generic.safe_fall_distance": "Distansa de cascada sicura", "attribute.name.generic.scale": "Grandesa", "attribute.name.generic.step_height": "Altesa del paso", "attribute.name.generic.water_movement_efficiency": "Eficiensa de movimento in acua", "attribute.name.gravity": "Gravità", "attribute.name.horse.jump_strength": "Forsa de salto de'l caval", "attribute.name.jump_strength": "Forsa de salto", "attribute.name.knockback_resistance": "Rezistensa a'l contracolpo", "attribute.name.luck": "Fortuna", "attribute.name.max_absorption": "Asorbimento masimo", "attribute.name.max_health": "<PERSON><PERSON><PERSON> màsi<PERSON>", "attribute.name.mining_efficiency": "Eficiensa de scavo", "attribute.name.movement_efficiency": "Eficiensa de movimento", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "Bonus de osigeno", "attribute.name.player.block_break_speed": "Zveltesa de spacatura del bloco", "attribute.name.player.block_interaction_range": "Distansa de interasion del bloco", "attribute.name.player.entity_interaction_range": "Distansa de interasion de la entità", "attribute.name.player.mining_efficiency": "Eficiensa de scavo", "attribute.name.player.sneaking_speed": "Velosidà da cucià", "attribute.name.player.submerged_mining_speed": "Velosidà de scavo soto acua", "attribute.name.player.sweeping_damage_ratio": "Rateo de dani a falsà", "attribute.name.safe_fall_distance": "Distansa de cascada sicura", "attribute.name.scale": "Grandesa", "attribute.name.sneaking_speed": "Velosidà da cucià", "attribute.name.spawn_reinforcements": "Renforsi zonbi", "attribute.name.step_height": "Altesa del paso", "attribute.name.submerged_mining_speed": "Velosidà de scavo soto acua", "attribute.name.sweeping_damage_ratio": "Rateo de dani a falsà", "attribute.name.tempt_range": "Ragio de tentasiòn dele criadure", "attribute.name.water_movement_efficiency": "Eficiensa de movimento in acua", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "Renforsi zonbi", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Delta de basalto", "biome.minecraft.beach": "Spiaza", "biome.minecraft.birch_forest": "Foresta de brédołe", "biome.minecraft.cherry_grove": "Bosco de çiresari", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON><PERSON> fredo", "biome.minecraft.crimson_forest": "Foresta crèmese", "biome.minecraft.dark_forest": "Foresta scura", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON>ano fondo fredo", "biome.minecraft.deep_dark": "Scurità fonda", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON>èano fondo g<PERSON>", "biome.minecraft.deep_lukewarm_ocean": "Ocèano fondo tivio", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON><PERSON> fondo", "biome.minecraft.desert": "Dezerto", "biome.minecraft.dripstone_caves": "Sprùgia de pirołoti", "biome.minecraft.end_barrens": "<PERSON>", "biome.minecraft.end_highlands": "Altopian de'l End", "biome.minecraft.end_midlands": "Mediopian de'l End", "biome.minecraft.eroded_badlands": "Badlands eroze", "biome.minecraft.flower_forest": "Foresta de fiori", "biome.minecraft.forest": "Foresta", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON> gias<PERSON>a", "biome.minecraft.frozen_river": "Rivo g<PERSON>", "biome.minecraft.grove": "Bosche<PERSON>", "biome.minecraft.ice_spikes": "Spontoni de giaso", "biome.minecraft.jagged_peaks": "<PERSON><PERSON> frastaliata", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "biome.minecraft.lush_caves": "Sprùgie salvàdeghe", "biome.minecraft.mangrove_swamp": "Palù de Mangrovia", "biome.minecraft.meadow": "Prà", "biome.minecraft.mushroom_fields": "<PERSON><PERSON> de fongi", "biome.minecraft.nether_wastes": "<PERSON><PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Foresta de brédołe de vecia generazsion", "biome.minecraft.old_growth_pine_taiga": "Pineto de la taiga de vecia generazsion", "biome.minecraft.old_growth_spruce_taiga": "Taiga de vecia generazsion", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON>", "biome.minecraft.river": "Rivo", "biome.minecraft.savanna": "<PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Altopian de ła savana", "biome.minecraft.small_end_islands": "Ìzołe picenine de'l End", "biome.minecraft.snowy_beach": "Spiaja nevada", "biome.minecraft.snowy_plains": "<PERSON><PERSON> ne<PERSON>o", "biome.minecraft.snowy_slopes": "Versante nevegato", "biome.minecraft.snowy_taiga": "Taiga inevada", "biome.minecraft.soul_sand_valley": "Val de le àneme", "biome.minecraft.sparse_jungle": "<PERSON><PERSON>", "biome.minecraft.stony_peaks": "<PERSON><PERSON>", "biome.minecraft.stony_shore": "Costa de Piera", "biome.minecraft.sunflower_plains": "Piana de zirasołi", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "El End", "biome.minecraft.the_void": "El Vodo", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON><PERSON> caldo", "biome.minecraft.warped_forest": "Foresta strùpia", "biome.minecraft.windswept_forest": "Bosco sufiato dal vento", "biome.minecraft.windswept_gravelly_hills": "<PERSON> de giara sufiato dal vento", "biome.minecraft.windswept_hills": "<PERSON> sufiato dal vento", "biome.minecraft.windswept_savanna": "<PERSON>vana sufiata dal vento", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON> sufiati dal vento", "block.minecraft.acacia_button": "Boton de acasia", "block.minecraft.acacia_door": "Porta de acasia", "block.minecraft.acacia_fence": "Ringhiera de acasia", "block.minecraft.acacia_fence_gate": "Sarajo de acasia", "block.minecraft.acacia_hanging_sign": "Cartel de acasia picà", "block.minecraft.acacia_leaves": "Foje de acasia", "block.minecraft.acacia_log": "Fusto de acasia", "block.minecraft.acacia_planks": "Ase de acasia", "block.minecraft.acacia_pressure_plate": "Pedana a presion de acasia", "block.minecraft.acacia_sapling": "Albareło de acasia", "block.minecraft.acacia_sign": "Cartel de acasia", "block.minecraft.acacia_slab": "Łiston de acasia", "block.minecraft.acacia_stairs": "Scałe de acasia", "block.minecraft.acacia_trapdoor": "Rebalta de acasia", "block.minecraft.acacia_wall_hanging_sign": "Cartel de acasia picà a parede", "block.minecraft.acacia_wall_sign": "Cartel de acasia a parede", "block.minecraft.acacia_wood": "Ł<PERSON>jo de acasia", "block.minecraft.activator_rail": "Roara de ativasion", "block.minecraft.air": "Aria", "block.minecraft.allium": "<PERSON><PERSON>", "block.minecraft.amethyst_block": "Bloco de ametista", "block.minecraft.amethyst_cluster": "Grumo de Ametista", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite": "Andezite", "block.minecraft.andesite_slab": "<PERSON><PERSON><PERSON> de andezite", "block.minecraft.andesite_stairs": "Scałe de andezite", "block.minecraft.andesite_wall": "<PERSON><PERSON>", "block.minecraft.anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Pianta de inguria maura", "block.minecraft.attached_pumpkin_stem": "Pianta de suca maura", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Foje de azalea", "block.minecraft.azure_bluet": "<PERSON><PERSON>", "block.minecraft.bamboo": "Canavera", "block.minecraft.bamboo_block": "Bloco de canavera", "block.minecraft.bamboo_button": "Botón de canavera", "block.minecraft.bamboo_door": "Porta de canavera", "block.minecraft.bamboo_fence": "Serajo de canavera", "block.minecraft.bamboo_fence_gate": "Cancełeto de canavera", "block.minecraft.bamboo_hanging_sign": "Cartel de canavera picà", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON> spigh<PERSON>", "block.minecraft.bamboo_mosaic_slab": "Łiston de canavera spigh<PERSON>à", "block.minecraft.bamboo_mosaic_stairs": "Scałe de canavera spighetà", "block.minecraft.bamboo_planks": "<PERSON><PERSON> de can<PERSON>", "block.minecraft.bamboo_pressure_plate": "Pedana a pression de canavera", "block.minecraft.bamboo_sapling": "Buto de canavera", "block.minecraft.bamboo_sign": "Cartel de canavera", "block.minecraft.bamboo_slab": "<PERSON>ist<PERSON> de canavera", "block.minecraft.bamboo_stairs": "Scałe de canavera", "block.minecraft.bamboo_trapdoor": "Botoa de canavera", "block.minecraft.bamboo_wall_hanging_sign": "Cartel de canavera picà a parede", "block.minecraft.bamboo_wall_sign": "Cartel de canavera a parede", "block.minecraft.banner.base.black": "<PERSON><PERSON> negro", "block.minecraft.banner.base.blue": "<PERSON><PERSON> blé", "block.minecraft.banner.base.brown": "<PERSON><PERSON> maron", "block.minecraft.banner.base.cyan": "<PERSON><PERSON>", "block.minecraft.banner.base.gray": "<PERSON><PERSON> griso", "block.minecraft.banner.base.green": "<PERSON><PERSON> verdo", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON> griso ciaro", "block.minecraft.banner.base.lime": "Canpo lime", "block.minecraft.banner.base.magenta": "Canpo magenta", "block.minecraft.banner.base.orange": "<PERSON><PERSON>", "block.minecraft.banner.base.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.base.purple": "<PERSON><PERSON> viola", "block.minecraft.banner.base.red": "<PERSON><PERSON> rosso", "block.minecraft.banner.base.white": "<PERSON><PERSON> bianco", "block.minecraft.banner.base.yellow": "<PERSON><PERSON>", "block.minecraft.banner.border.black": "Bordura de negro", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON> de blè", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.gray": "Bordura de grizo", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON> de verdo", "block.minecraft.banner.border.light_blue": "Bordura de sełeste", "block.minecraft.banner.border.light_gray": "Bordura de grizo ciaro", "block.minecraft.banner.border.lime": "Bordura de lime", "block.minecraft.banner.border.magenta": "Bordura de mazenta", "block.minecraft.banner.border.orange": "Bordura de naransa", "block.minecraft.banner.border.pink": "Bordura de roza", "block.minecraft.banner.border.purple": "Bordura de vioła", "block.minecraft.banner.border.red": "Bordure de roso", "block.minecraft.banner.border.white": "Bordura de bianco", "block.minecraft.banner.border.yellow": "Bordura de zało", "block.minecraft.banner.bricks.black": "Canpo negro de cuarełi", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> bl<PERSON> de <PERSON>i", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON> ma<PERSON> de cuarełi", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON> si<PERSON> de cuarełi", "block.minecraft.banner.bricks.gray": "Canpo grizo de cuarełi", "block.minecraft.banner.bricks.green": "<PERSON><PERSON> verdo de cuarełi", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON> de cuarełi", "block.minecraft.banner.bricks.light_gray": "Canpo grizo ciaro de cuarełi", "block.minecraft.banner.bricks.lime": "Canpo lime de cuarełi", "block.minecraft.banner.bricks.magenta": "Canpo maze<PERSON> de cuarełi", "block.minecraft.banner.bricks.orange": "Canpo naransa de cuarełi", "block.minecraft.banner.bricks.pink": "Canpo roza de cuarełi", "block.minecraft.banner.bricks.purple": "Canpo vioła de cuarełi", "block.minecraft.banner.bricks.red": "<PERSON><PERSON> roso de cuarełi", "block.minecraft.banner.bricks.white": "Canpo bianco de cuarełi", "block.minecraft.banner.bricks.yellow": "Canpo zało de cuarełi", "block.minecraft.banner.circle.black": "Serciołeto negro", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON><PERSON> blè", "block.minecraft.banner.circle.brown": "Ser<PERSON><PERSON><PERSON> maron", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON><PERSON>an", "block.minecraft.banner.circle.gray": "Serciołeto grizo", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON> verdo", "block.minecraft.banner.circle.light_blue": "Serciołeto sełeste", "block.minecraft.banner.circle.light_gray": "Serciołeto grizo ciaro", "block.minecraft.banner.circle.lime": "Serciołeto lime", "block.minecraft.banner.circle.magenta": "Ser<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Serciołeto naransa", "block.minecraft.banner.circle.pink": "Serciołeto roza", "block.minecraft.banner.circle.purple": "Serciołeto vioła", "block.minecraft.banner.circle.red": "Serciołeto roso", "block.minecraft.banner.circle.white": "Serciołeto bianco", "block.minecraft.banner.circle.yellow": "Serciołeto zało", "block.minecraft.banner.creeper.black": "<PERSON><PERSON> de creeper negro", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON>er blè", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON> de creeper maron", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON> de creeper sian", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON> de creeper grizo", "block.minecraft.banner.creeper.green": "<PERSON><PERSON> de creeper verdo", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON>er sełeste", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON> de creeper grizo ciaro", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON> de creeper lime", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON> de creeper mazenta", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON> de creeper naransa", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON> de creeper roza", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON> de <PERSON>er vioła", "block.minecraft.banner.creeper.red": "<PERSON><PERSON> de creeper roso", "block.minecraft.banner.creeper.white": "<PERSON><PERSON> de creeper bianco", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON> zało", "block.minecraft.banner.cross.black": "Decusa negra", "block.minecraft.banner.cross.blue": "Decusa blè", "block.minecraft.banner.cross.brown": "Decusa maron", "block.minecraft.banner.cross.cyan": "<PERSON>usa sian", "block.minecraft.banner.cross.gray": "Decusa griza", "block.minecraft.banner.cross.green": "Decusa verda", "block.minecraft.banner.cross.light_blue": "Decusa sełeste", "block.minecraft.banner.cross.light_gray": "Decusa griza ciara", "block.minecraft.banner.cross.lime": "Decusa lime", "block.minecraft.banner.cross.magenta": "Decusa mazenta", "block.minecraft.banner.cross.orange": "Decusa naransa", "block.minecraft.banner.cross.pink": "Decusa roza", "block.minecraft.banner.cross.purple": "Decusa vioła", "block.minecraft.banner.cross.red": "Decusa rosa", "block.minecraft.banner.cross.white": "Decusa bianca", "block.minecraft.banner.cross.yellow": "Decusa zała", "block.minecraft.banner.curly_border.black": "<PERSON>rdura dentada de nero", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> dentada de blè", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> dentada de ma<PERSON>", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> dentada de <PERSON>an", "block.minecraft.banner.curly_border.gray": "Bordura dentada de grizo", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> dentada de verdo", "block.minecraft.banner.curly_border.light_blue": "Bordura dentada de sełeste", "block.minecraft.banner.curly_border.light_gray": "Bordura dentada de grizo ciaro", "block.minecraft.banner.curly_border.lime": "Bordura dentada de lime", "block.minecraft.banner.curly_border.magenta": "Bordura dentada de <PERSON>", "block.minecraft.banner.curly_border.orange": "Bordura dentada de naransa", "block.minecraft.banner.curly_border.pink": "<PERSON>rdura dentada de roza", "block.minecraft.banner.curly_border.purple": "Bordura dentada de vioła", "block.minecraft.banner.curly_border.red": "Bordura dentada de roso", "block.minecraft.banner.curly_border.white": "Bordura dentada de bianco", "block.minecraft.banner.curly_border.yellow": "Bordura dentada de zało", "block.minecraft.banner.diagonal_left.black": "Canton dx in sima de negro", "block.minecraft.banner.diagonal_left.blue": "Canton dx in sima de blè", "block.minecraft.banner.diagonal_left.brown": "Canton dx in sima de maron", "block.minecraft.banner.diagonal_left.cyan": "Canton dx in sima de sian", "block.minecraft.banner.diagonal_left.gray": "Canton dx in sima de grizo", "block.minecraft.banner.diagonal_left.green": "Canton dx in sima de verdo", "block.minecraft.banner.diagonal_left.light_blue": "Canton dx in sima de sełeste", "block.minecraft.banner.diagonal_left.light_gray": "Canton dx in sima de grizo ciaro", "block.minecraft.banner.diagonal_left.lime": "Canton dx in sima de lime", "block.minecraft.banner.diagonal_left.magenta": "Canton dx in sima de mazenta", "block.minecraft.banner.diagonal_left.orange": "Canton dx in sima de naransa", "block.minecraft.banner.diagonal_left.pink": "Canton dx in sima de roza", "block.minecraft.banner.diagonal_left.purple": "Canton dx in sima de vioła", "block.minecraft.banner.diagonal_left.red": "Canton dx in sima de roso", "block.minecraft.banner.diagonal_left.white": "Canton dx in sima de bianco", "block.minecraft.banner.diagonal_left.yellow": "Canton dx in sima de zało", "block.minecraft.banner.diagonal_right.black": "Canton sx in sima de negro", "block.minecraft.banner.diagonal_right.blue": "Canton sx in sima de blè", "block.minecraft.banner.diagonal_right.brown": "Canton sx in sima de maron", "block.minecraft.banner.diagonal_right.cyan": "Canton sx in sima de sian", "block.minecraft.banner.diagonal_right.gray": "Canton sx in sima de grizo", "block.minecraft.banner.diagonal_right.green": "Canton sx in sima de verdo", "block.minecraft.banner.diagonal_right.light_blue": "Canton sx in sima de sełeste", "block.minecraft.banner.diagonal_right.light_gray": "Canton sx in sima de grizo ciaro", "block.minecraft.banner.diagonal_right.lime": "Canton sx in sima de lime", "block.minecraft.banner.diagonal_right.magenta": "Canton sx in sima de mazenta", "block.minecraft.banner.diagonal_right.orange": "Canton sx in sima de naransa", "block.minecraft.banner.diagonal_right.pink": "Canton sx in sima de roza", "block.minecraft.banner.diagonal_right.purple": "Canton sx in sima de vioła", "block.minecraft.banner.diagonal_right.red": "Canton sx in sima de roso", "block.minecraft.banner.diagonal_right.white": "Canton sx in sima de bianco", "block.minecraft.banner.diagonal_right.yellow": "Canton sx in sima de zało", "block.minecraft.banner.diagonal_up_left.black": "Canton dx in baso de negro", "block.minecraft.banner.diagonal_up_left.blue": "Canton dx in baso de blè", "block.minecraft.banner.diagonal_up_left.brown": "Canton dx in baso de maron", "block.minecraft.banner.diagonal_up_left.cyan": "Canton dx in baso de sian", "block.minecraft.banner.diagonal_up_left.gray": "Canton dx in baso de grizo", "block.minecraft.banner.diagonal_up_left.green": "Canton dx in baso de verdo", "block.minecraft.banner.diagonal_up_left.light_blue": "Canton dx in baso de sełeste", "block.minecraft.banner.diagonal_up_left.light_gray": "Canton dx in baso de grizo ciaro", "block.minecraft.banner.diagonal_up_left.lime": "Canton dx in baso de lime", "block.minecraft.banner.diagonal_up_left.magenta": "Canton dx in baso de mazenta", "block.minecraft.banner.diagonal_up_left.orange": "Canton dx in baso de naransa", "block.minecraft.banner.diagonal_up_left.pink": "Canton dx in baso de roza", "block.minecraft.banner.diagonal_up_left.purple": "Canton dx in baso de vioła", "block.minecraft.banner.diagonal_up_left.red": "Canton dx in baso de roso", "block.minecraft.banner.diagonal_up_left.white": "Canton dx in baso de bianco", "block.minecraft.banner.diagonal_up_left.yellow": "Canton dx in baso de zało", "block.minecraft.banner.diagonal_up_right.black": "Canton sx in baso de negro", "block.minecraft.banner.diagonal_up_right.blue": "Canton sx in baso de blè", "block.minecraft.banner.diagonal_up_right.brown": "Canton sx in baso de maron", "block.minecraft.banner.diagonal_up_right.cyan": "Canton sx in baso de sian", "block.minecraft.banner.diagonal_up_right.gray": "Canton sx in baso de grizo", "block.minecraft.banner.diagonal_up_right.green": "Canton sx in baso de verdo", "block.minecraft.banner.diagonal_up_right.light_blue": "Canton sx in baso de sełeste", "block.minecraft.banner.diagonal_up_right.light_gray": "Canton sx in baso de grizo ciaro", "block.minecraft.banner.diagonal_up_right.lime": "Canton sx in baso de lime", "block.minecraft.banner.diagonal_up_right.magenta": "Canton sx in baso de mazenta", "block.minecraft.banner.diagonal_up_right.orange": "Canton sx in baso de naransa", "block.minecraft.banner.diagonal_up_right.pink": "Canton sx in baso de roza", "block.minecraft.banner.diagonal_up_right.purple": "Canton sx in baso de vioła", "block.minecraft.banner.diagonal_up_right.red": "Canton sx in baso de roso", "block.minecraft.banner.diagonal_up_right.white": "Canton sx in baso de bianco", "block.minecraft.banner.diagonal_up_right.yellow": "Canton sx in baso de zało", "block.minecraft.banner.flow.black": "Fluso negro", "block.minecraft.banner.flow.blue": "Fluso blu", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Fluso Grizo", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_gray": "Fluso Grizo Ciaro", "block.minecraft.banner.flow.lime": "Fluso Lime", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "Fluso Bianco", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.black": "<PERSON>or negro", "block.minecraft.banner.flower.blue": "<PERSON><PERSON> bl<PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> maron", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON> grizo", "block.minecraft.banner.flower.green": "<PERSON><PERSON> verdo", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON>or grizo ciaro", "block.minecraft.banner.flower.lime": "Fior lime", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON>", "block.minecraft.banner.flower.orange": "<PERSON>or <PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON> roza", "block.minecraft.banner.flower.purple": "<PERSON>or v<PERSON>ła", "block.minecraft.banner.flower.red": "<PERSON>or roso", "block.minecraft.banner.flower.white": "<PERSON>or bianco", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON>a<PERSON>", "block.minecraft.banner.globe.black": "Globo negro", "block.minecraft.banner.globe.blue": "Globo blè", "block.minecraft.banner.globe.brown": "Globo maron", "block.minecraft.banner.globe.cyan": "Globo sian", "block.minecraft.banner.globe.gray": "Globo grizo", "block.minecraft.banner.globe.green": "Globo verdo", "block.minecraft.banner.globe.light_blue": "Globo sełeste", "block.minecraft.banner.globe.light_gray": "Globo grizo ciaro", "block.minecraft.banner.globe.lime": "Globo lime", "block.minecraft.banner.globe.magenta": "Globo mazenta", "block.minecraft.banner.globe.orange": "Globo naransa", "block.minecraft.banner.globe.pink": "Globo roza", "block.minecraft.banner.globe.purple": "Globo vioła", "block.minecraft.banner.globe.red": "Globo roso", "block.minecraft.banner.globe.white": "Globo bianco", "block.minecraft.banner.globe.yellow": "Globo zało", "block.minecraft.banner.gradient.black": "Desfumà de negro", "block.minecraft.banner.gradient.blue": "Desfumà de blè", "block.minecraft.banner.gradient.brown": "Desfumà de maron", "block.minecraft.banner.gradient.cyan": "Desfum<PERSON>", "block.minecraft.banner.gradient.gray": "Desfumà de grizo", "block.minecraft.banner.gradient.green": "Desfumà de verdo", "block.minecraft.banner.gradient.light_blue": "Desfumà de sełeste", "block.minecraft.banner.gradient.light_gray": "Desfumà de grizo ciaro", "block.minecraft.banner.gradient.lime": "Desfumà de lime", "block.minecraft.banner.gradient.magenta": "Desfumà de mazenta", "block.minecraft.banner.gradient.orange": "Desfumà de naransa", "block.minecraft.banner.gradient.pink": "Desfumà de roza", "block.minecraft.banner.gradient.purple": "Desfumà de vioła", "block.minecraft.banner.gradient.red": "Desfumà de roso", "block.minecraft.banner.gradient.white": "Desfumà de bianco", "block.minecraft.banner.gradient.yellow": "Desfumà de zało", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON> s<PERSON> de negro", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON> sfu<PERSON> de blè", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON> s<PERSON>an", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON> s<PERSON> de grizo", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON> s<PERSON> de verdo", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON> s<PERSON> de se<PERSON>", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON> s<PERSON> de grizo ciaro", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON> sfu<PERSON> de lime", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON> s<PERSON> de naransa", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON> s<PERSON> de roza", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> s<PERSON> de vioła", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON> s<PERSON> roso", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON> s<PERSON> de bianco", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON> s<PERSON> de zało", "block.minecraft.banner.guster.black": "Sboàda Negra", "block.minecraft.banner.guster.blue": "Sboàda Blu", "block.minecraft.banner.guster.brown": "Sboàda <PERSON>", "block.minecraft.banner.guster.cyan": "Sboàda Cian", "block.minecraft.banner.guster.gray": "Sboàda Griza", "block.minecraft.banner.guster.green": "Sboàda Verda", "block.minecraft.banner.guster.light_blue": "Sboàda cełeste", "block.minecraft.banner.guster.light_gray": "Sboàda griza ciara", "block.minecraft.banner.guster.lime": "Sboàda Lime", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON> mazenta", "block.minecraft.banner.guster.orange": "Sboàda Naransa", "block.minecraft.banner.guster.pink": "S<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.purple": "Sboàda Viola", "block.minecraft.banner.guster.red": "Sboàda Rossa", "block.minecraft.banner.guster.white": "Sbo<PERSON><PERSON> Bianca", "block.minecraft.banner.guster.yellow": "Sboàda Zala", "block.minecraft.banner.half_horizontal.black": "Negro de sora", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON> de <PERSON>ra", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "Grizo de sora", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON>o <PERSON>ra", "block.minecraft.banner.half_horizontal.light_blue": "Sełeste de sora", "block.minecraft.banner.half_horizontal.light_gray": "Grizo ciaro de sora", "block.minecraft.banner.half_horizontal.lime": "Lime de sora", "block.minecraft.banner.half_horizontal.magenta": "Mazenta de sora", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> de <PERSON>ra", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> de <PERSON>ra", "block.minecraft.banner.half_horizontal.purple": "Vioła de sora", "block.minecraft.banner.half_horizontal.red": "Roso de sora", "block.minecraft.banner.half_horizontal.white": "Bianco de sora", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON>ł<PERSON> de sora", "block.minecraft.banner.half_horizontal_bottom.black": "Negro de soto", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON>to", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "Grizo de soto", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Sełeste de soto", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Grizo ciaro de soto", "block.minecraft.banner.half_horizontal_bottom.lime": "Lime de soto", "block.minecraft.banner.half_horizontal_bottom.magenta": "Mazen<PERSON> de soto", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON><PERSON>to", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON>to", "block.minecraft.banner.half_horizontal_bottom.purple": "Vioła de soto", "block.minecraft.banner.half_horizontal_bottom.red": "Roso de soto", "block.minecraft.banner.half_horizontal_bottom.white": "Bianco de soto", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON>to", "block.minecraft.banner.half_vertical.black": "Partìo a drita de negro", "block.minecraft.banner.half_vertical.blue": "Partìo a drita de blè", "block.minecraft.banner.half_vertical.brown": "Partìo a drita de maron", "block.minecraft.banner.half_vertical.cyan": "Partìo a drita de sian", "block.minecraft.banner.half_vertical.gray": "Partìo a drita de grizo", "block.minecraft.banner.half_vertical.green": "Partìo a drita de verdo", "block.minecraft.banner.half_vertical.light_blue": "Partìo a drita de sełeste", "block.minecraft.banner.half_vertical.light_gray": "Partìo a drita de grizo ciaro", "block.minecraft.banner.half_vertical.lime": "Partìo a drita de lime", "block.minecraft.banner.half_vertical.magenta": "Partìo a drita de mazenta", "block.minecraft.banner.half_vertical.orange": "Partìo a drita de naransa", "block.minecraft.banner.half_vertical.pink": "Partìo a drita de roza", "block.minecraft.banner.half_vertical.purple": "Partìo a drita de vioła", "block.minecraft.banner.half_vertical.red": "Partìo a drita de roso", "block.minecraft.banner.half_vertical.white": "Partìo a drita de bianco", "block.minecraft.banner.half_vertical.yellow": "Partio a drita de zało", "block.minecraft.banner.half_vertical_right.black": "Partìo a sanca de negro", "block.minecraft.banner.half_vertical_right.blue": "Partìo a sanca de blè", "block.minecraft.banner.half_vertical_right.brown": "Partìo a sanca de maron", "block.minecraft.banner.half_vertical_right.cyan": "Partìo a sanca de sian", "block.minecraft.banner.half_vertical_right.gray": "Partìo a sanca de grizo", "block.minecraft.banner.half_vertical_right.green": "Partìo a sanca de verdo", "block.minecraft.banner.half_vertical_right.light_blue": "Partìo a sanca de sełeste", "block.minecraft.banner.half_vertical_right.light_gray": "Partìo a sanca de grizo ciaro", "block.minecraft.banner.half_vertical_right.lime": "Partìo a sanca de lime", "block.minecraft.banner.half_vertical_right.magenta": "Partìo a sanca de mazenta", "block.minecraft.banner.half_vertical_right.orange": "Partìo a sanca de naransa", "block.minecraft.banner.half_vertical_right.pink": "Partìo a sanca de roza", "block.minecraft.banner.half_vertical_right.purple": "Partìo a sanca de vioła", "block.minecraft.banner.half_vertical_right.red": "Partìo a sanca de roso", "block.minecraft.banner.half_vertical_right.white": "Partìo a sanca de bianco", "block.minecraft.banner.half_vertical_right.yellow": "Partìo a sanca de zało", "block.minecraft.banner.mojang.black": "Logo negro", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON> blè", "block.minecraft.banner.mojang.brown": "Logo maron", "block.minecraft.banner.mojang.cyan": "<PERSON>go sian", "block.minecraft.banner.mojang.gray": "Logo grizo", "block.minecraft.banner.mojang.green": "Logo verdo", "block.minecraft.banner.mojang.light_blue": "Logo <PERSON>ł<PERSON>", "block.minecraft.banner.mojang.light_gray": "Logo grizo ciaro", "block.minecraft.banner.mojang.lime": "Logo lime", "block.minecraft.banner.mojang.magenta": "Logo mazenta", "block.minecraft.banner.mojang.orange": "Logo naransa", "block.minecraft.banner.mojang.pink": "Logo roza", "block.minecraft.banner.mojang.purple": "Logo vioła", "block.minecraft.banner.mojang.red": "Logo roso", "block.minecraft.banner.mojang.white": "Logo bianco", "block.minecraft.banner.mojang.yellow": "Logo zało", "block.minecraft.banner.piglin.black": "Grugno negro", "block.minecraft.banner.piglin.blue": "Grug<PERSON> blé", "block.minecraft.banner.piglin.brown": "Grugno maron", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "Grugno griso", "block.minecraft.banner.piglin.green": "G<PERSON><PERSON> verdo", "block.minecraft.banner.piglin.light_blue": "Grug<PERSON> çele<PERSON>", "block.minecraft.banner.piglin.light_gray": "Grugno griso ciaro", "block.minecraft.banner.piglin.lime": "Grugno lime", "block.minecraft.banner.piglin.magenta": "Grugno magenta", "block.minecraft.banner.piglin.orange": "Grugno naransa", "block.minecraft.banner.piglin.pink": "Grugno rosa", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON> viola", "block.minecraft.banner.piglin.red": "Grugno rosso", "block.minecraft.banner.piglin.white": "Grugno bianco", "block.minecraft.banner.piglin.yellow": "Grugno zalo", "block.minecraft.banner.rhombus.black": "Łozanga negra", "block.minecraft.banner.rhombus.blue": "Łozanga blè", "block.minecraft.banner.rhombus.brown": "Łozanga maron", "block.minecraft.banner.rhombus.cyan": "Łozanga sian", "block.minecraft.banner.rhombus.gray": "Łozanga griza", "block.minecraft.banner.rhombus.green": "Łozanga verda", "block.minecraft.banner.rhombus.light_blue": "Łozanga sełeste", "block.minecraft.banner.rhombus.light_gray": "Łozanga griza ciara", "block.minecraft.banner.rhombus.lime": "Łozanga lime", "block.minecraft.banner.rhombus.magenta": "Łozanga mazenta", "block.minecraft.banner.rhombus.orange": "Łozanga naransa", "block.minecraft.banner.rhombus.pink": "Łozanga roza", "block.minecraft.banner.rhombus.purple": "Łozanga vioła", "block.minecraft.banner.rhombus.red": "Łozanga rosa", "block.minecraft.banner.rhombus.white": "Łozanga bianca", "block.minecraft.banner.rhombus.yellow": "Łozanga zała", "block.minecraft.banner.skull.black": "<PERSON><PERSON> de tescio negro", "block.minecraft.banner.skull.blue": "<PERSON><PERSON> blè", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> de te<PERSON>cio maron", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON> te<PERSON> sian", "block.minecraft.banner.skull.gray": "<PERSON><PERSON> de tescio grizo", "block.minecraft.banner.skull.green": "<PERSON><PERSON> de te<PERSON>cio verdo", "block.minecraft.banner.skull.light_blue": "Stan<PERSON> de te<PERSON>cio se<PERSON>", "block.minecraft.banner.skull.light_gray": "Stanpo de tescio grizo ciaro", "block.minecraft.banner.skull.lime": "Stanpo de tescio lime", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON> de tescio naransa", "block.minecraft.banner.skull.pink": "<PERSON><PERSON> de te<PERSON>cio roza", "block.minecraft.banner.skull.purple": "Stanpo de tescio vioła", "block.minecraft.banner.skull.red": "<PERSON><PERSON> de te<PERSON>cio roso", "block.minecraft.banner.skull.white": "<PERSON><PERSON> de tescio bianco", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> de tescio zało", "block.minecraft.banner.small_stripes.black": "Pałà de negro", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON> de bl<PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "Pałà de grizo", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON> de verdo", "block.minecraft.banner.small_stripes.light_blue": "Pałà de sełeste", "block.minecraft.banner.small_stripes.light_gray": "Pałà de grizo ciaro", "block.minecraft.banner.small_stripes.lime": "Pałà de lime", "block.minecraft.banner.small_stripes.magenta": "Pałà <PERSON>", "block.minecraft.banner.small_stripes.orange": "Pałà de naransa", "block.minecraft.banner.small_stripes.pink": "Pałà de roza", "block.minecraft.banner.small_stripes.purple": "Pałà de vioła", "block.minecraft.banner.small_stripes.red": "Pałà de roso", "block.minecraft.banner.small_stripes.white": "Pałà de bianco", "block.minecraft.banner.small_stripes.yellow": "Pałà de zało", "block.minecraft.banner.square_bottom_left.black": "Canton destro de ła ponta de negro", "block.minecraft.banner.square_bottom_left.blue": "Canton destro de ła ponta de blè", "block.minecraft.banner.square_bottom_left.brown": "Canton destro de ła ponta de maron", "block.minecraft.banner.square_bottom_left.cyan": "Canton destro de ła ponta de sian", "block.minecraft.banner.square_bottom_left.gray": "Canton destro de ła ponta de grizo", "block.minecraft.banner.square_bottom_left.green": "Canton destro de ła ponta de verdo", "block.minecraft.banner.square_bottom_left.light_blue": "Canton destro de ła ponta de sełeste", "block.minecraft.banner.square_bottom_left.light_gray": "Canton destro de ła ponta de grizo ciaro", "block.minecraft.banner.square_bottom_left.lime": "Canton destro de ła ponta de lime", "block.minecraft.banner.square_bottom_left.magenta": "Canton destro de ła ponta de mazenta", "block.minecraft.banner.square_bottom_left.orange": "Canton destro de ła ponta de naransa", "block.minecraft.banner.square_bottom_left.pink": "Canton destro de ła ponta de roza", "block.minecraft.banner.square_bottom_left.purple": "Canton destro de ła ponta de vioła", "block.minecraft.banner.square_bottom_left.red": "Canton destro de ła ponta de roso", "block.minecraft.banner.square_bottom_left.white": "Canton destro de ła ponta de bianco", "block.minecraft.banner.square_bottom_left.yellow": "Canton destro de ła ponta de zało", "block.minecraft.banner.square_bottom_right.black": "Canton sinistro de ła ponta de negro", "block.minecraft.banner.square_bottom_right.blue": "Canton sinistro de ła ponta de blè", "block.minecraft.banner.square_bottom_right.brown": "Canton sinistro de ła ponta de maron", "block.minecraft.banner.square_bottom_right.cyan": "Canton sinistro de ła ponta de sian", "block.minecraft.banner.square_bottom_right.gray": "Canton sinistro de ła ponta de grizo", "block.minecraft.banner.square_bottom_right.green": "Canton sinistro de ła ponta de verdo", "block.minecraft.banner.square_bottom_right.light_blue": "Canton sinistro de ła ponta de sełeste", "block.minecraft.banner.square_bottom_right.light_gray": "Canton sinistro de ła ponta de grizo ciaro", "block.minecraft.banner.square_bottom_right.lime": "Canton sinistro de ła ponta de lime", "block.minecraft.banner.square_bottom_right.magenta": "Canton sinistro de ła ponta de mazenta", "block.minecraft.banner.square_bottom_right.orange": "Canton sinistro de ła ponta de naransa", "block.minecraft.banner.square_bottom_right.pink": "Canton sinistro de ła ponta de roza", "block.minecraft.banner.square_bottom_right.purple": "Canton sinistro de ła ponta de vioła", "block.minecraft.banner.square_bottom_right.red": "Canton sinistro de ła ponta de roso", "block.minecraft.banner.square_bottom_right.white": "Canton sinistro de ła ponta de bianco", "block.minecraft.banner.square_bottom_right.yellow": "Canton sinistro de ła ponta de zało", "block.minecraft.banner.square_top_left.black": "Canton destro in cao de negro", "block.minecraft.banner.square_top_left.blue": "Canton destro in cao de blè", "block.minecraft.banner.square_top_left.brown": "Canton destro in cao de maron", "block.minecraft.banner.square_top_left.cyan": "Canton destro in cao de sian", "block.minecraft.banner.square_top_left.gray": "Canton destro in cao de grizo", "block.minecraft.banner.square_top_left.green": "Canton destro in cao de verdo", "block.minecraft.banner.square_top_left.light_blue": "Canton destro in cao de sełeste", "block.minecraft.banner.square_top_left.light_gray": "Canton destro in cao de grizo ciaro", "block.minecraft.banner.square_top_left.lime": "Canton destro in cao de lime", "block.minecraft.banner.square_top_left.magenta": "Canton destro in cao de mazenta", "block.minecraft.banner.square_top_left.orange": "Canton destro in cao de naransa", "block.minecraft.banner.square_top_left.pink": "Canton destro in cao de roza", "block.minecraft.banner.square_top_left.purple": "Canton destro in cao de vioła", "block.minecraft.banner.square_top_left.red": "Canton destro in cao de roso", "block.minecraft.banner.square_top_left.white": "Canton destro in cao de bianco", "block.minecraft.banner.square_top_left.yellow": "Canton destro in cao de zało", "block.minecraft.banner.square_top_right.black": "Canton sinistro in cao de negro", "block.minecraft.banner.square_top_right.blue": "Canton sinistro in cao de blè", "block.minecraft.banner.square_top_right.brown": "Canton sinistro in cao de maron", "block.minecraft.banner.square_top_right.cyan": "Canton sinistro in cao de sian", "block.minecraft.banner.square_top_right.gray": "Canton sinistro in cao de grizo", "block.minecraft.banner.square_top_right.green": "Canton sinistro in cao de verdo", "block.minecraft.banner.square_top_right.light_blue": "Canton sinistro in cao de sełeste", "block.minecraft.banner.square_top_right.light_gray": "Canton sinistro in caos de grizo ciaro", "block.minecraft.banner.square_top_right.lime": "Canton sinistro in cao de lime", "block.minecraft.banner.square_top_right.magenta": "Canton sinistro in cao de mazenta", "block.minecraft.banner.square_top_right.orange": "Canton sinistro in cao de naransa", "block.minecraft.banner.square_top_right.pink": "Canton sinistro in cao de roza", "block.minecraft.banner.square_top_right.purple": "Canton sinistro in cao de vioła", "block.minecraft.banner.square_top_right.red": "Canton sinistro in cao de roso", "block.minecraft.banner.square_top_right.white": "Canton sinistro in cao de bianco", "block.minecraft.banner.square_top_right.yellow": "Canton sinistro in cao de zało", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON><PERSON> negra", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON> blè", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON> maron", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON> sian", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON> griza", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON> ceł<PERSON>", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON> griza ciara", "block.minecraft.banner.straight_cross.lime": "Croze lime", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON> maze<PERSON>", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON> naran<PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON> roza", "block.minecraft.banner.straight_cross.purple": "C<PERSON>ze vioła", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON> bianca", "block.minecraft.banner.straight_cross.yellow": "C<PERSON>ze zała", "block.minecraft.banner.stripe_bottom.black": "Baze negra", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON> blè", "block.minecraft.banner.stripe_bottom.brown": "Baze maron", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON> sian", "block.minecraft.banner.stripe_bottom.gray": "Baze griza", "block.minecraft.banner.stripe_bottom.green": "Baze verda", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "Baze griza ciara", "block.minecraft.banner.stripe_bottom.lime": "Baze lime", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON> mazenta", "block.minecraft.banner.stripe_bottom.orange": "Baze naransa", "block.minecraft.banner.stripe_bottom.pink": "Baze roza", "block.minecraft.banner.stripe_bottom.purple": "Baze vioła", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_bottom.white": "Baze bianca", "block.minecraft.banner.stripe_bottom.yellow": "Baze zała", "block.minecraft.banner.stripe_center.black": "Pèrtega negra", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON><PERSON> blè", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON><PERSON> maron", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON><PERSON>an", "block.minecraft.banner.stripe_center.gray": "Pèrtega griza", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.banner.stripe_center.light_blue": "Pèrtega sełeste", "block.minecraft.banner.stripe_center.light_gray": "Pèrtega griza ciara", "block.minecraft.banner.stripe_center.lime": "Pèrtega lime", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON><PERSON> mazenta", "block.minecraft.banner.stripe_center.orange": "Pèrtega naransa", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON><PERSON> roza", "block.minecraft.banner.stripe_center.purple": "Pèrtega vioła", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.stripe_center.white": "Pèrtega bianca", "block.minecraft.banner.stripe_center.yellow": "Pèrtega zała", "block.minecraft.banner.stripe_downleft.black": "Banda negra sinistra", "block.minecraft.banner.stripe_downleft.blue": "Banda blè sinistra", "block.minecraft.banner.stripe_downleft.brown": "Banda maron sinistra", "block.minecraft.banner.stripe_downleft.cyan": "Banda sian sinistra", "block.minecraft.banner.stripe_downleft.gray": "Banda griza sinistra", "block.minecraft.banner.stripe_downleft.green": "Banda verda sinistra", "block.minecraft.banner.stripe_downleft.light_blue": "Banda sełeste sinistra", "block.minecraft.banner.stripe_downleft.light_gray": "Banda griza ciara sinistra", "block.minecraft.banner.stripe_downleft.lime": "Banda lime sinsitra", "block.minecraft.banner.stripe_downleft.magenta": "Banda mazenta sinistra", "block.minecraft.banner.stripe_downleft.orange": "Banda naransa sinistra", "block.minecraft.banner.stripe_downleft.pink": "Banda roza sinistra", "block.minecraft.banner.stripe_downleft.purple": "Banda vioła sinistra", "block.minecraft.banner.stripe_downleft.red": "Banda rosa sinistra", "block.minecraft.banner.stripe_downleft.white": "Banda bianca sinistra", "block.minecraft.banner.stripe_downleft.yellow": "Banda zała sinistra", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda blè", "block.minecraft.banner.stripe_downright.brown": "Banda maron", "block.minecraft.banner.stripe_downright.cyan": "<PERSON>a sian", "block.minecraft.banner.stripe_downright.gray": "Banda griza", "block.minecraft.banner.stripe_downright.green": "Banda verda", "block.minecraft.banner.stripe_downright.light_blue": "Banda sełeste", "block.minecraft.banner.stripe_downright.light_gray": "Banda griza ciara", "block.minecraft.banner.stripe_downright.lime": "Banda lime", "block.minecraft.banner.stripe_downright.magenta": "Banda mazenta", "block.minecraft.banner.stripe_downright.orange": "Banda naransa", "block.minecraft.banner.stripe_downright.pink": "Banda roza", "block.minecraft.banner.stripe_downright.purple": "Banda vioła", "block.minecraft.banner.stripe_downright.red": "Banda rosa", "block.minecraft.banner.stripe_downright.white": "Banda bianca", "block.minecraft.banner.stripe_downright.yellow": "Banda zała", "block.minecraft.banner.stripe_left.black": "Pèrtega destra negra", "block.minecraft.banner.stripe_left.blue": "Pèrtega destra blè", "block.minecraft.banner.stripe_left.brown": "Pèrtega destra maron", "block.minecraft.banner.stripe_left.cyan": "Pèrtega destra sian", "block.minecraft.banner.stripe_left.gray": "Pèrtega destra griza", "block.minecraft.banner.stripe_left.green": "Pèrtega destra verda", "block.minecraft.banner.stripe_left.light_blue": "Pèrtega destra sełeste", "block.minecraft.banner.stripe_left.light_gray": "Pèrtega destra griza ciara", "block.minecraft.banner.stripe_left.lime": "Pèrtega destra lime", "block.minecraft.banner.stripe_left.magenta": "Pèrtega destra mazenta", "block.minecraft.banner.stripe_left.orange": "Pèrtega destra naransa", "block.minecraft.banner.stripe_left.pink": "Pèrtega destra roza", "block.minecraft.banner.stripe_left.purple": "Pèrtega destra vioła", "block.minecraft.banner.stripe_left.red": "Pèrtega destra rosa", "block.minecraft.banner.stripe_left.white": "Pèrtega destra bianca", "block.minecraft.banner.stripe_left.yellow": "Pèrtega destra zała", "block.minecraft.banner.stripe_middle.black": "Fasa negra", "block.minecraft.banner.stripe_middle.blue": "Fasa blè", "block.minecraft.banner.stripe_middle.brown": "Fasa maron", "block.minecraft.banner.stripe_middle.cyan": "<PERSON>asa sian", "block.minecraft.banner.stripe_middle.gray": "Fasa griza", "block.minecraft.banner.stripe_middle.green": "Fasa verda", "block.minecraft.banner.stripe_middle.light_blue": "Fasa sełeste", "block.minecraft.banner.stripe_middle.light_gray": "Fasa griza ciara", "block.minecraft.banner.stripe_middle.lime": "Fasa lime", "block.minecraft.banner.stripe_middle.magenta": "Fasa mazenta", "block.minecraft.banner.stripe_middle.orange": "Fasa naransa", "block.minecraft.banner.stripe_middle.pink": "Fasa roza", "block.minecraft.banner.stripe_middle.purple": "Fasa vioła", "block.minecraft.banner.stripe_middle.red": "Fasa rosa", "block.minecraft.banner.stripe_middle.white": "Fasa bianca", "block.minecraft.banner.stripe_middle.yellow": "Fasa zała", "block.minecraft.banner.stripe_right.black": "Pèrtega sinistra negra", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON><PERSON> sinistra blè", "block.minecraft.banner.stripe_right.brown": "Pèrtega sinistra maron", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON><PERSON> sinistra sian", "block.minecraft.banner.stripe_right.gray": "Pèrtega sinistra griza", "block.minecraft.banner.stripe_right.green": "Pèrtega sinistra verda", "block.minecraft.banner.stripe_right.light_blue": "Pèrtega sinistra sełeste", "block.minecraft.banner.stripe_right.light_gray": "Pèrtega sinistra griza ciara", "block.minecraft.banner.stripe_right.lime": "Pèrtega sinistra lime", "block.minecraft.banner.stripe_right.magenta": "Pèrtega sinistra mazenta", "block.minecraft.banner.stripe_right.orange": "Pèrtega sinistra naransa", "block.minecraft.banner.stripe_right.pink": "Pèrtega sinistra roza", "block.minecraft.banner.stripe_right.purple": "Pèrtega sinistra vioła", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON><PERSON> sinistra rosa", "block.minecraft.banner.stripe_right.white": "Pèrtega sinistra bianca", "block.minecraft.banner.stripe_right.yellow": "Pèrtega sinistra zała", "block.minecraft.banner.stripe_top.black": "<PERSON>ma negra", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON> blè", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON> maron", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON>an", "block.minecraft.banner.stripe_top.gray": "Si<PERSON> griza", "block.minecraft.banner.stripe_top.green": "<PERSON>ma verda", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_gray": "Sima griza ciara", "block.minecraft.banner.stripe_top.lime": "Sima lime", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON> mazenta", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON> na<PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON> roza", "block.minecraft.banner.stripe_top.purple": "Sima vioła", "block.minecraft.banner.stripe_top.red": "<PERSON>ma rosa", "block.minecraft.banner.stripe_top.white": "Sima bianca", "block.minecraft.banner.stripe_top.yellow": "Sima zała", "block.minecraft.banner.triangle_bottom.black": "Ponta de negro", "block.minecraft.banner.triangle_bottom.blue": "Ponta de <PERSON>lè", "block.minecraft.banner.triangle_bottom.brown": "Ponta de ma<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Ponta de <PERSON>", "block.minecraft.banner.triangle_bottom.gray": "Ponta de grizo", "block.minecraft.banner.triangle_bottom.green": "Ponta de verdo", "block.minecraft.banner.triangle_bottom.light_blue": "Ponta de sełeste", "block.minecraft.banner.triangle_bottom.light_gray": "Ponta de grizo ciaro", "block.minecraft.banner.triangle_bottom.lime": "Ponta de lime", "block.minecraft.banner.triangle_bottom.magenta": "Ponta de mazenta", "block.minecraft.banner.triangle_bottom.orange": "Ponta de naransa", "block.minecraft.banner.triangle_bottom.pink": "Ponta de roza", "block.minecraft.banner.triangle_bottom.purple": "Ponta de vioła", "block.minecraft.banner.triangle_bottom.red": "Ponta de roso", "block.minecraft.banner.triangle_bottom.white": "Ponta de bianco", "block.minecraft.banner.triangle_bottom.yellow": "Ponta de zało", "block.minecraft.banner.triangle_top.black": "Ponta roversa de negro", "block.minecraft.banner.triangle_top.blue": "Ponta roversa de blè", "block.minecraft.banner.triangle_top.brown": "Ponta roversa de maron", "block.minecraft.banner.triangle_top.cyan": "Ponta roversa de sian", "block.minecraft.banner.triangle_top.gray": "Ponta roversa de grizo", "block.minecraft.banner.triangle_top.green": "Ponta roversa de verdo", "block.minecraft.banner.triangle_top.light_blue": "Ponta roversa de sełeste", "block.minecraft.banner.triangle_top.light_gray": "Ponta roversa de grizo ciaro", "block.minecraft.banner.triangle_top.lime": "Ponta roversa de lime", "block.minecraft.banner.triangle_top.magenta": "Ponta roversa de mazenta", "block.minecraft.banner.triangle_top.orange": "Ponta roversa de naransa", "block.minecraft.banner.triangle_top.pink": "Ponta roversa de roza", "block.minecraft.banner.triangle_top.purple": "Ponta roversa de vioła", "block.minecraft.banner.triangle_top.red": "Ponta roversa de roso", "block.minecraft.banner.triangle_top.white": "Ponta roversa de bianco", "block.minecraft.banner.triangle_top.yellow": "Ponta roversa de zało", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON> den<PERSON> de negro", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON> den<PERSON> de blè", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON> den<PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON> den<PERSON>an", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON> den<PERSON> de grizo", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON> den<PERSON> de verdo", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON> den<PERSON> de se<PERSON>", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON> den<PERSON> de grizo ciaro", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON> dent<PERSON> de lime", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON> den<PERSON> de naran<PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON> den<PERSON> de roza", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON> den<PERSON> de vio<PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON> den<PERSON> de roso", "block.minecraft.banner.triangles_bottom.white": "Seghetadura da baso bianca", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> den<PERSON> de zało", "block.minecraft.banner.triangles_top.black": "<PERSON> dentà de negro", "block.minecraft.banner.triangles_top.blue": "<PERSON> dentà de blè", "block.minecraft.banner.triangles_top.brown": "<PERSON> den<PERSON> de maron", "block.minecraft.banner.triangles_top.cyan": "<PERSON> den<PERSON> de <PERSON>an", "block.minecraft.banner.triangles_top.gray": "<PERSON> dentà de grizo", "block.minecraft.banner.triangles_top.green": "<PERSON> dentà de verdo", "block.minecraft.banner.triangles_top.light_blue": "<PERSON> dentà de sełeste", "block.minecraft.banner.triangles_top.light_gray": "<PERSON> dentà de grizo ciaro", "block.minecraft.banner.triangles_top.lime": "<PERSON> dentà de lime", "block.minecraft.banner.triangles_top.magenta": "<PERSON> den<PERSON> de mazenta", "block.minecraft.banner.triangles_top.orange": "<PERSON> dentà de naransa", "block.minecraft.banner.triangles_top.pink": "<PERSON> dentà de roza", "block.minecraft.banner.triangles_top.purple": "<PERSON> dent<PERSON> de vioła", "block.minecraft.banner.triangles_top.red": "<PERSON> dent<PERSON> de roso", "block.minecraft.banner.triangles_top.white": "<PERSON> dent<PERSON> de bianco", "block.minecraft.banner.triangles_top.yellow": "<PERSON> dent<PERSON> de za<PERSON>o", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Basalto", "block.minecraft.beacon": "Faro", "block.minecraft.beacon.primary": "<PERSON><PERSON> prinsipal", "block.minecraft.beacon.secondary": "<PERSON><PERSON> secondario", "block.minecraft.bed.no_sleep": "Te pol pisocàre solo de note o durante el tenporale", "block.minecraft.bed.not_safe": "A te pol mìa dormir deso: ghe ze dei mostri chi visin", "block.minecraft.bed.obstructed": "'Sto łeto el ze ocupà", "block.minecraft.bed.occupied": "Sto łeto el xe ocupà", "block.minecraft.bed.too_far_away": "No te pol mìa dormir chi: el leto l'è masa distante", "block.minecraft.bedrock": "<PERSON><PERSON> de<PERSON>l fondo", "block.minecraft.bee_nest": "Avearo", "block.minecraft.beehive": "Avearo artifisial", "block.minecraft.beetroots": "<PERSON><PERSON>", "block.minecraft.bell": "Canpana", "block.minecraft.big_dripleaf": "Fojogiossa granda", "block.minecraft.big_dripleaf_stem": "Ganbo de fojogiossa granda", "block.minecraft.birch_button": "<PERSON><PERSON>", "block.minecraft.birch_door": "Porta de brédoła", "block.minecraft.birch_fence": "Ringhiera de brédoła", "block.minecraft.birch_fence_gate": "<PERSON><PERSON> bré<PERSON>", "block.minecraft.birch_hanging_sign": "Cartel de brédola picà", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON> brédoła", "block.minecraft.birch_log": "Fusto de brédoła", "block.minecraft.birch_planks": "<PERSON><PERSON> <PERSON>", "block.minecraft.birch_pressure_plate": "Pedana a presion de brédoła", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "Cartel de brédoła", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON> de brédoła", "block.minecraft.birch_stairs": "Scałe de brédoła", "block.minecraft.birch_trapdoor": "Rebalta de brédoła", "block.minecraft.birch_wall_hanging_sign": "Cartel de brédola picà a parede", "block.minecraft.birch_wall_sign": "Cartel de brédoła a muro", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.black_banner": "Gonfałon negro", "block.minecraft.black_bed": "Łeto negro", "block.minecraft.black_candle": "Candela negra", "block.minecraft.black_candle_cake": "Torta co' candela negra", "block.minecraft.black_carpet": "Tapéo negro", "block.minecraft.black_concrete": "<PERSON>on negro", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> de beton negra", "block.minecraft.black_glazed_terracotta": "Teracota negra zmaltà", "block.minecraft.black_shulker_box": "Scàtoła de shulker negra", "block.minecraft.black_stained_glass": "Vero negro", "block.minecraft.black_stained_glass_pane": "Panel de vero negro", "block.minecraft.black_terracotta": "Teracota negra", "block.minecraft.black_wool": "<PERSON><PERSON> negra", "block.minecraft.blackstone": "Pieranegra", "block.minecraft.blackstone_slab": "<PERSON><PERSON>", "block.minecraft.blackstone_stairs": "Scale de Pieranegra", "block.minecraft.blackstone_wall": "<PERSON><PERSON>", "block.minecraft.blast_furnace": "Gran fornaze", "block.minecraft.blue_banner": "Gonfałon blè", "block.minecraft.blue_bed": "<PERSON><PERSON> blè", "block.minecraft.blue_candle": "Candela blé", "block.minecraft.blue_candle_cake": "Torta co' candela blé", "block.minecraft.blue_carpet": "Tap<PERSON>o blè", "block.minecraft.blue_concrete": "<PERSON><PERSON> bl<PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>on blè", "block.minecraft.blue_glazed_terracotta": "Teracota blè <PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> bl<PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON><PERSON><PERSON> blè", "block.minecraft.blue_shulker_box": "<PERSON>à<PERSON><PERSON>ker blè", "block.minecraft.blue_stained_glass": "Vero blè", "block.minecraft.blue_stained_glass_pane": "Panel de vero blè", "block.minecraft.blue_terracotta": "Teracota blè", "block.minecraft.blue_wool": "<PERSON><PERSON> bl<PERSON>", "block.minecraft.bone_block": "Bloco de osi", "block.minecraft.bookshelf": "Librarìa", "block.minecraft.brain_coral": "Coral a sarvel", "block.minecraft.brain_coral_block": "Bloco de coral a sarvel", "block.minecraft.brain_coral_fan": "Gorgonia a sarvel", "block.minecraft.brain_coral_wall_fan": "Gorgonia a sarvel a parede", "block.minecraft.brewing_stand": "Łanbico", "block.minecraft.brick_slab": "Łist<PERSON> de cuarełi", "block.minecraft.brick_stairs": "Scałe de cuarełi", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "C<PERSON><PERSON>ł<PERSON>", "block.minecraft.brown_banner": "Gonfałon maron", "block.minecraft.brown_bed": "<PERSON><PERSON> maron", "block.minecraft.brown_candle": "Candela maron", "block.minecraft.brown_candle_cake": "Torta co' candela maron", "block.minecraft.brown_carpet": "Tapéo maron", "block.minecraft.brown_concrete": "<PERSON><PERSON> maron", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton maron", "block.minecraft.brown_glazed_terracotta": "Teracota maron zmaltà", "block.minecraft.brown_mushroom": "Fongo maron", "block.minecraft.brown_mushroom_block": "Bloco de fongo maron", "block.minecraft.brown_shulker_box": "Scàtoła de shulker maron", "block.minecraft.brown_stained_glass": "Vero maron", "block.minecraft.brown_stained_glass_pane": "Panel de vero maron", "block.minecraft.brown_terracotta": "Teracota maron", "block.minecraft.brown_wool": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.bubble_column": "Cołona de bołe", "block.minecraft.bubble_coral": "Coral a bołe", "block.minecraft.bubble_coral_block": "Bloco de coral a bołe", "block.minecraft.bubble_coral_fan": "Gorgonia a bołe", "block.minecraft.bubble_coral_wall_fan": "Gorgonia a bołe a parede", "block.minecraft.budding_amethyst": "Rabuto de Ametista", "block.minecraft.bush": "Giavasco", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "<PERSON>or de cactus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calçite", "block.minecraft.calibrated_sculk_sensor": "Sensor de sculk calibrà", "block.minecraft.campfire": "<PERSON><PERSON> da canpo", "block.minecraft.candle": "Candela", "block.minecraft.candle_cake": "<PERSON>dela su torta", "block.minecraft.carrots": "<PERSON><PERSON>", "block.minecraft.cartography_table": "Banco de'l cartògrafo", "block.minecraft.carved_pumpkin": "Suca intajada", "block.minecraft.cauldron": "Calderon", "block.minecraft.cave_air": "Aria de grota", "block.minecraft.cave_vines": "Ranpeganti de le sprugie", "block.minecraft.cave_vines_plant": "Fusto de ranpegante de le sprugie", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloco comando a cadena", "block.minecraft.cherry_button": "<PERSON><PERSON>", "block.minecraft.cherry_door": "Porta de çiresar", "block.minecraft.cherry_fence": "Ringhiera de çiresar", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "Cartel de çiresar picà", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON> de <PERSON>ar", "block.minecraft.cherry_log": "Fusto de çiresar", "block.minecraft.cherry_planks": "<PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "Pedana a pression de çiresar", "block.minecraft.cherry_sapling": "Buto de çiresar", "block.minecraft.cherry_sign": "Cartel de çiresar", "block.minecraft.cherry_slab": "<PERSON><PERSON> de çiresar", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON> ç<PERSON>ar", "block.minecraft.cherry_trapdoor": "Rebalta de çiresar", "block.minecraft.cherry_wall_hanging_sign": "Cartel de çiresar picà a parede", "block.minecraft.cherry_wall_sign": "Cartel de çiresar a parede", "block.minecraft.cherry_wood": "Legno de çiresar", "block.minecraft.chest": "Baùl", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON> sche<PERSON>", "block.minecraft.chiseled_bookshelf": "Librar<PERSON>a la<PERSON>ada", "block.minecraft.chiseled_copper": "<PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "Fondardesia <PERSON>", "block.minecraft.chiseled_nether_bricks": "Cuarèi del Nether Lavoradi", "block.minecraft.chiseled_polished_blackstone": "Pieranegra Lustra Lavorada", "block.minecraft.chiseled_quartz_block": "Bloco de Cuarso Lavorà", "block.minecraft.chiseled_red_sandstone": "Arenaria rosa <PERSON>", "block.minecraft.chiseled_resin_bricks": "Cuarèi de caràsa lavoradi", "block.minecraft.chiseled_sandstone": "Arenaria <PERSON>av<PERSON>à", "block.minecraft.chiseled_stone_bricks": "Cuarełi de piera łavoradi", "block.minecraft.chiseled_tuff": "<PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "Cuarèi de Tufo <PERSON>", "block.minecraft.chorus_flower": "Fior de chorus", "block.minecraft.chorus_plant": "Pianta de chorus", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON>cid<PERSON> serà", "block.minecraft.coal_block": "Bloco de Carbon", "block.minecraft.coal_ore": "Mineral de Carbon", "block.minecraft.coarse_dirt": "<PERSON><PERSON> seca", "block.minecraft.cobbled_deepslate": "Fondardesia Cogolada", "block.minecraft.cobbled_deepslate_slab": "Liston de Fondardesia Cogolada", "block.minecraft.cobbled_deepslate_stairs": "Scale de Fondardesia Cogolada", "block.minecraft.cobbled_deepslate_wall": "<PERSON>reta de Fondardesia Cogolada", "block.minecraft.cobblestone": "<PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON>e", "block.minecraft.cobblestone_stairs": "Scałe de piera", "block.minecraft.cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "Bloco comando", "block.minecraft.comparator": "Conparador de redstone", "block.minecraft.composter": "Conpostador", "block.minecraft.conduit": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Bloco de Ramo", "block.minecraft.copper_bulb": "Bulbo de Rame", "block.minecraft.copper_door": "Porta de Rame", "block.minecraft.copper_grate": "<PERSON><PERSON>", "block.minecraft.copper_ore": "Mineral de Ramo", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.cornflower": "Se<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Cuarèi de Fondardesia crepadi", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON> de Fondardesia <PERSON>pade", "block.minecraft.cracked_nether_bricks": "Cuarèi del Nether Crepadi", "block.minecraft.cracked_polished_blackstone_bricks": "Cuarèi de Pieranegra Lustra Crepadi", "block.minecraft.cracked_stone_bricks": "Cuarełi de piera crepadi", "block.minecraft.crafter": "Craftador", "block.minecraft.crafting_table": "Banco da łaoro", "block.minecraft.creaking_heart": "Core <PERSON>", "block.minecraft.creeper_head": "<PERSON>a de creeper", "block.minecraft.creeper_wall_head": "Testa de creeper a muro", "block.minecraft.crimson_button": "<PERSON><PERSON> crèmese", "block.minecraft.crimson_door": "Porta crèmese", "block.minecraft.crimson_fence": "Ringhiera crèmese", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON> crèmese", "block.minecraft.crimson_fungus": "Fongo crèmese", "block.minecraft.crimson_hanging_sign": "Cartel crèmexe picà", "block.minecraft.crimson_hyphae": "Ife crèmese", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON>o c<PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON> crèmese", "block.minecraft.crimson_pressure_plate": "Pedana a pression crèmese", "block.minecraft.crimson_roots": "Raixe crèmese", "block.minecraft.crimson_sign": "<PERSON>telo cr<PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON> crèmese", "block.minecraft.crimson_stairs": "Scale crèmese", "block.minecraft.crimson_stem": "Ganbo crèmese", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON> cr<PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Cartel crèmexe picà a parede", "block.minecraft.crimson_wall_sign": "Cartelo crèmexe a parede", "block.minecraft.crying_obsidian": "<PERSON>si<PERSON><PERSON>", "block.minecraft.cut_copper": "<PERSON><PERSON>", "block.minecraft.cut_copper_slab": "<PERSON><PERSON> de Ramo <PERSON>", "block.minecraft.cut_copper_stairs": "Scale de Ramo Intajà", "block.minecraft.cut_red_sandstone": "Arenaria rosa inta<PERSON>da", "block.minecraft.cut_red_sandstone_slab": "Łiston de arenaria rosa intajà", "block.minecraft.cut_sandstone": "Arenaria intajà", "block.minecraft.cut_sandstone_slab": "Łiston de arenaria intajà", "block.minecraft.cyan_banner": "Gonfałon sian", "block.minecraft.cyan_bed": "<PERSON><PERSON>", "block.minecraft.cyan_candle": "Candela ciana", "block.minecraft.cyan_candle_cake": "Torta co' candela çiana", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON> sian", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton sian", "block.minecraft.cyan_glazed_terracotta": "Teracota sian z<PERSON>", "block.minecraft.cyan_shulker_box": "Scàto<PERSON> de shulker sian", "block.minecraft.cyan_stained_glass": "Vero sian", "block.minecraft.cyan_stained_glass_pane": "Panel de vero sian", "block.minecraft.cyan_terracotta": "Teracota sian", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dandelion": "Pisacan", "block.minecraft.dark_oak_button": "<PERSON><PERSON> ròvare scura", "block.minecraft.dark_oak_door": "Porta de ròvare scura", "block.minecraft.dark_oak_fence": "Ringhiera de ròvare scura", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON> de ròvare scura", "block.minecraft.dark_oak_hanging_sign": "Cartel de ròvare scura picà", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON> de ròvare scura", "block.minecraft.dark_oak_log": "Fusto de ròvare scura", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> <PERSON> rò<PERSON> scura", "block.minecraft.dark_oak_pressure_plate": "Pedana a presion de ròvare scura", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON><PERSON> de ròvare scura", "block.minecraft.dark_oak_sign": "Cartel de ròvare scura", "block.minecraft.dark_oak_slab": "<PERSON><PERSON><PERSON> de ròvare scura", "block.minecraft.dark_oak_stairs": "Scałe de ròvare scura", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_wall_hanging_sign": "Cartel de ròvare scura picà a parede", "block.minecraft.dark_oak_wall_sign": "Cartel de ròvare scura a parede", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON> <PERSON> ròvare scura", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>in scuro", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON> de prizmarin scuro", "block.minecraft.dark_prismarine_stairs": "Scałe de prizmarin scuro", "block.minecraft.daylight_detector": "Sensor de łuze diurna", "block.minecraft.dead_brain_coral": "<PERSON> a sarvel morto", "block.minecraft.dead_brain_coral_block": "Bloco de coral a sarvel morto", "block.minecraft.dead_brain_coral_fan": "<PERSON>rgonia a sarvel morta", "block.minecraft.dead_brain_coral_wall_fan": "Gorgon<PERSON> a sarvel a parede morta", "block.minecraft.dead_bubble_coral": "Coral a bołe morto", "block.minecraft.dead_bubble_coral_block": "Bloco de coral a bołe morto", "block.minecraft.dead_bubble_coral_fan": "Gorgonia a bołe morta", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgonia a bołe a parede morta", "block.minecraft.dead_bush": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral": "Coral de fogo morto", "block.minecraft.dead_fire_coral_block": "Bloco de coral de fogo morto", "block.minecraft.dead_fire_coral_fan": "Gorgon<PERSON> de fogo morta", "block.minecraft.dead_fire_coral_wall_fan": "Gorgonia de fogo a parede morta", "block.minecraft.dead_horn_coral": "<PERSON> a corno morto", "block.minecraft.dead_horn_coral_block": "Bloco de coral a corni morto", "block.minecraft.dead_horn_coral_fan": "Gorgonia a corni morta", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON>ia a corni a parede morta", "block.minecraft.dead_tube_coral": "Coral a tubo morto", "block.minecraft.dead_tube_coral_block": "Bloco de coral a tubo morto", "block.minecraft.dead_tube_coral_fan": "Gorgonia a tubo morta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgonia a tubo a parede morta", "block.minecraft.decorated_pot": "Vaso de<PERSON>", "block.minecraft.deepslate": "Fondardesia", "block.minecraft.deepslate_brick_slab": "Liston de Cuarèi de Fondardesia", "block.minecraft.deepslate_brick_stairs": "Scale de Cuarèi de Fondardesia", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> de Cuarèi de Fondardesia", "block.minecraft.deepslate_bricks": "Cuarèi de Fondardesia", "block.minecraft.deepslate_coal_ore": "Mineral de Carbon in Fondardesia", "block.minecraft.deepslate_copper_ore": "Mineral de Ramo in Fondardesia", "block.minecraft.deepslate_diamond_ore": "Mineral de Diamante in Fondardesia", "block.minecraft.deepslate_emerald_ore": "Mineral de Smeraldo in Fondardesia", "block.minecraft.deepslate_gold_ore": "Mineral de Oro in Fondardesia", "block.minecraft.deepslate_iron_ore": "Mineral de Fero in Fondardesia", "block.minecraft.deepslate_lapis_ore": "Mineral de Lapislàsaro in Fondardesia", "block.minecraft.deepslate_redstone_ore": "Mineral de Redstone in Fondardesia", "block.minecraft.deepslate_tile_slab": "Liston de Matonele de Fondardesia", "block.minecraft.deepslate_tile_stairs": "Scale de Matonele de Fondardesia", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> de Matonele de Fondardesia", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON> de Fondardesia", "block.minecraft.detector_rail": "R<PERSON><PERSON>", "block.minecraft.diamond_block": "Bloco de Diamante", "block.minecraft.diamond_ore": "Mineral de Diamante", "block.minecraft.diorite": "Diorite", "block.minecraft.diorite_slab": "Łiston de diorite", "block.minecraft.diorite_stairs": "Scałe de diorite", "block.minecraft.diorite_wall": "<PERSON><PERSON>", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON> bat<PERSON>o", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "<PERSON><PERSON> de dragon", "block.minecraft.dragon_head": "Testa de dragon", "block.minecraft.dragon_wall_head": "Testa de dragon a muro", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Bloco de àlega seca", "block.minecraft.dripstone_block": "<PERSON><PERSON> p<PERSON>łoto", "block.minecraft.dropper": "Dropador", "block.minecraft.emerald_block": "<PERSON><PERSON>", "block.minecraft.emerald_ore": "<PERSON><PERSON>", "block.minecraft.enchanting_table": "Banco da incantamenti", "block.minecraft.end_gateway": "Varco de<PERSON>l <PERSON>", "block.minecraft.end_portal": "Portal de'l End", "block.minecraft.end_portal_frame": "<PERSON><PERSON><PERSON> de'l portal de'l End", "block.minecraft.end_rod": "<PERSON><PERSON>", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Łiston de cuarełi de piera de'l End", "block.minecraft.end_stone_brick_stairs": "Scałe de cuarełi de piera de'l End", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON> de cuarełi de piera de'<PERSON>", "block.minecraft.end_stone_bricks": "Cuarełi de piera de'l End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON> espon<PERSON>o", "block.minecraft.exposed_copper": "<PERSON><PERSON>", "block.minecraft.exposed_copper_bulb": "Lampada de ramo espon<PERSON>o", "block.minecraft.exposed_copper_door": "Porta de ramo espon<PERSON>o", "block.minecraft.exposed_copper_grate": "Gradela de ramo espon<PERSON>o", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON> de ramo espon<PERSON>o", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON>", "block.minecraft.exposed_cut_copper_slab": "Liston de Ramo Esponùo <PERSON>", "block.minecraft.exposed_cut_copper_stairs": "Scale de Ramo Esponùo <PERSON>", "block.minecraft.farmland": "<PERSON><PERSON> sapada", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "Fogo", "block.minecraft.fire_coral": "Coral de fogo", "block.minecraft.fire_coral_block": "Bloco de coral de fogo", "block.minecraft.fire_coral_fan": "Gorgonia <PERSON>", "block.minecraft.fire_coral_wall_fan": "Gorgonia de fogo a parede", "block.minecraft.firefly_bush": "Giavasco de luxarole", "block.minecraft.fletching_table": "Banco de l'arser", "block.minecraft.flower_pot": "Vazo da fiori", "block.minecraft.flowering_azalea": "Azalea fi<PERSON>a", "block.minecraft.flowering_azalea_leaves": "Foje de azalea fiorìa", "block.minecraft.frogspawn": "<PERSON><PERSON>", "block.minecraft.frosted_ice": "Acua g<PERSON>", "block.minecraft.furnace": "Fornaze", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.glass": "Vero", "block.minecraft.glass_pane": "Panel de vero", "block.minecraft.glow_lichen": "Lichen luxente", "block.minecraft.glowstone": "Zluzorite", "block.minecraft.gold_block": "Bloco de Oro", "block.minecraft.gold_ore": "Mineral de Oro", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Łiston de granito", "block.minecraft.granite_stairs": "Scałe de granito", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "Erba", "block.minecraft.grass_block": "Bloco de erba", "block.minecraft.gravel": "Giara", "block.minecraft.gray_banner": "Gonfałon grizo", "block.minecraft.gray_bed": "<PERSON> griso", "block.minecraft.gray_candle": "Candela grisa", "block.minecraft.gray_candle_cake": "Torta co' candela grisa", "block.minecraft.gray_carpet": "Tapéo griso", "block.minecraft.gray_concrete": "<PERSON><PERSON> grizo", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>on griza", "block.minecraft.gray_glazed_terracotta": "Teracota griza zmaltà", "block.minecraft.gray_shulker_box": "Scàtoła de shulker griza", "block.minecraft.gray_stained_glass": "Vero grizo", "block.minecraft.gray_stained_glass_pane": "Panel de vero grizo", "block.minecraft.gray_terracotta": "Teracota griza", "block.minecraft.gray_wool": "<PERSON><PERSON> griza", "block.minecraft.green_banner": "Gonfałon verdo", "block.minecraft.green_bed": "<PERSON><PERSON> verdo", "block.minecraft.green_candle": "Candela verda", "block.minecraft.green_candle_cake": "Torta co' candela maron", "block.minecraft.green_carpet": "Tapéo verdo", "block.minecraft.green_concrete": "<PERSON><PERSON> verdo", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton verda", "block.minecraft.green_glazed_terracotta": "Teracota verda zmaltà", "block.minecraft.green_shulker_box": "Scàtoła de shulker verda", "block.minecraft.green_stained_glass": "Vero verdo", "block.minecraft.green_stained_glass_pane": "Panel de vero verdo", "block.minecraft.green_terracotta": "Teracota verda", "block.minecraft.green_wool": "<PERSON><PERSON> verda", "block.minecraft.grindstone": "<PERSON><PERSON>", "block.minecraft.hanging_roots": "Raise pendente", "block.minecraft.hay_block": "Bała de fen", "block.minecraft.heavy_core": "Nucleo pesanto", "block.minecraft.heavy_weighted_pressure_plate": "Pedana a pezo (pezi grosi)", "block.minecraft.honey_block": "<PERSON>o de mel", "block.minecraft.honeycomb_block": "Bloco de caraze", "block.minecraft.hopper": "T<PERSON>oz<PERSON>", "block.minecraft.horn_coral": "Coral a corni", "block.minecraft.horn_coral_block": "Bloco de coral a corni", "block.minecraft.horn_coral_fan": "<PERSON><PERSON>ia a corni", "block.minecraft.horn_coral_wall_fan": "Gorgonia a corni a parede", "block.minecraft.ice": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Cuarełi de piera łavoradi infestadi", "block.minecraft.infested_cobblestone": "Piere infestade", "block.minecraft.infested_cracked_stone_bricks": "Cuarełi de piera crepè infestadi", "block.minecraft.infested_deepslate": "Fondardesia infestada", "block.minecraft.infested_mossy_stone_bricks": "Cuarełi de piera musciadi infestadi", "block.minecraft.infested_stone": "Piera infestada", "block.minecraft.infested_stone_bricks": "Cuarełi de piera infestadi", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Bloco de Fero", "block.minecraft.iron_door": "Porta de Fero", "block.minecraft.iron_ore": "Mineral de Fero", "block.minecraft.iron_trapdoor": "Rebalta de <PERSON>ro", "block.minecraft.jack_o_lantern": "Łanterna de suca", "block.minecraft.jigsaw": "Bloco de architeto", "block.minecraft.jukebox": "Jukebox", "block.minecraft.jungle_button": "<PERSON><PERSON> de ła jungla", "block.minecraft.jungle_door": "Porta de ła jungla", "block.minecraft.jungle_fence": "Ringhiera de ła jungla", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON> de <PERSON>a jungla", "block.minecraft.jungle_hanging_sign": "Cartel de la jungla picà", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON> de ła jungla", "block.minecraft.jungle_log": "Fusto de ła jungla", "block.minecraft.jungle_planks": "<PERSON><PERSON> de ła jungla", "block.minecraft.jungle_pressure_plate": "Pedana a presion de ła jungla", "block.minecraft.jungle_sapling": "Albareło de ła jungla", "block.minecraft.jungle_sign": "Cartel de ła jungla", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON> de ła jungla", "block.minecraft.jungle_stairs": "Scałe de ła jungla", "block.minecraft.jungle_trapdoor": "Rebalta de <PERSON>a jungla", "block.minecraft.jungle_wall_hanging_sign": "Cartel de la jungla picà a parede", "block.minecraft.jungle_wall_sign": "Cartel de ła jungla a parede", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON> de <PERSON>a jungla", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "Ganbo de àlega", "block.minecraft.ladder": "Scałion", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "Bloco de Lapislàsari", "block.minecraft.lapis_ore": "Mineral de Lapislàsaro", "block.minecraft.large_amethyst_bud": "Bòcolo de ametista grando", "block.minecraft.large_fern": "Felse granda", "block.minecraft.lava": "Łava", "block.minecraft.lava_cauldron": "Calderon de lava", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON> morte", "block.minecraft.lectern": "<PERSON><PERSON>", "block.minecraft.lever": "Ł<PERSON><PERSON>", "block.minecraft.light": "Luxe", "block.minecraft.light_blue_banner": "Gonfałon sełeste", "block.minecraft.light_blue_bed": "Łeto sełeste", "block.minecraft.light_blue_candle": "Candela çeleste", "block.minecraft.light_blue_candle_cake": "Torta co' candela çeleste", "block.minecraft.light_blue_carpet": "Tapéo çeleste", "block.minecraft.light_blue_concrete": "<PERSON><PERSON> se<PERSON>", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> de beton sełeste", "block.minecraft.light_blue_glazed_terracotta": "Teracota sełeste zmaltà", "block.minecraft.light_blue_shulker_box": "Scàtoła de shulker sełeste", "block.minecraft.light_blue_stained_glass": "Vero seł<PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Panel de vero sełeste", "block.minecraft.light_blue_terracotta": "Teracota sełeste", "block.minecraft.light_blue_wool": "<PERSON><PERSON>", "block.minecraft.light_gray_banner": "Gonfałon grizo ciaro", "block.minecraft.light_gray_bed": "Łeto grizo ciaro", "block.minecraft.light_gray_candle": "Candela grisa ciara", "block.minecraft.light_gray_candle_cake": "Torta co' candela grisa ciara", "block.minecraft.light_gray_carpet": "Tapéo griso ciaro", "block.minecraft.light_gray_concrete": "Beton grizo ciaro", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> de beton griza ciara", "block.minecraft.light_gray_glazed_terracotta": "Teracota griza ciara zmaltà", "block.minecraft.light_gray_shulker_box": "Scàtoła de shulker griza ciara", "block.minecraft.light_gray_stained_glass": "Vero grizo ciaro", "block.minecraft.light_gray_stained_glass_pane": "Panel de vero grizo ciaro", "block.minecraft.light_gray_terracotta": "Teracota griza ciara", "block.minecraft.light_gray_wool": "<PERSON><PERSON> griza ciara", "block.minecraft.light_weighted_pressure_plate": "Pedana a pezo (pezi picenini)", "block.minecraft.lightning_rod": "Parafulmen", "block.minecraft.lilac": "Secomoro", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON>", "block.minecraft.lime_banner": "Gonfałon lime", "block.minecraft.lime_bed": "Łeto lime", "block.minecraft.lime_candle": "Candela lime", "block.minecraft.lime_candle_cake": "Torta co' candela lime", "block.minecraft.lime_carpet": "Tapéo lime", "block.minecraft.lime_concrete": "Beton lime", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton lime", "block.minecraft.lime_glazed_terracotta": "Teracota lime zmaltà", "block.minecraft.lime_shulker_box": "Scàtoła de shulker lime", "block.minecraft.lime_stained_glass": "Vero lime", "block.minecraft.lime_stained_glass_pane": "Panel de vero lime", "block.minecraft.lime_terracotta": "Teracota lime", "block.minecraft.lime_wool": "Łana lime", "block.minecraft.lodestone": "Magnetite", "block.minecraft.loom": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_banner": "Gonfałon <PERSON>", "block.minecraft.magenta_bed": "<PERSON><PERSON>", "block.minecraft.magenta_candle": "Candela magenta", "block.minecraft.magenta_candle_cake": "Torta co' candela magenta", "block.minecraft.magenta_carpet": "Tapéo magenta", "block.minecraft.magenta_concrete": "<PERSON><PERSON> mazenta", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>on mazenta", "block.minecraft.magenta_glazed_terracotta": "Teracota mazenta zmaltà", "block.minecraft.magenta_shulker_box": "Scàtoła de shulker mazenta", "block.minecraft.magenta_stained_glass": "Vero mazenta", "block.minecraft.magenta_stained_glass_pane": "Panel de vero mazenta", "block.minecraft.magenta_terracotta": "Teracota mazenta", "block.minecraft.magenta_wool": "<PERSON><PERSON>", "block.minecraft.magma_block": "Bloco de magma", "block.minecraft.mangrove_button": "Boton de Mangrovia", "block.minecraft.mangrove_door": "Porta de Mangrovia", "block.minecraft.mangrove_fence": "Ringhiera de Mangrovia", "block.minecraft.mangrove_fence_gate": "Sarajo de Mangrovia", "block.minecraft.mangrove_hanging_sign": "Cartel de la mangrovia picà", "block.minecraft.mangrove_leaves": "Foje de Mangrovia", "block.minecraft.mangrove_log": "Fusto de Mangrovia", "block.minecraft.mangrove_planks": "Ase de Mangrovia", "block.minecraft.mangrove_pressure_plate": "Pedana a presion de Mangrovia", "block.minecraft.mangrove_propagule": "Propagulo de Mangrovia", "block.minecraft.mangrove_roots": "Radise de Mangrovia", "block.minecraft.mangrove_sign": "Cartel de la mangrovia", "block.minecraft.mangrove_slab": "Łiston de Mangrovia", "block.minecraft.mangrove_stairs": "Scałe de Mangrovia", "block.minecraft.mangrove_trapdoor": "Rebalta de Mangrovia", "block.minecraft.mangrove_wall_hanging_sign": "Cartel de la mangrovia picà a parede", "block.minecraft.mangrove_wall_sign": "Cartel de mangrovia a parede", "block.minecraft.mangrove_wood": "Legno de Mangrovia", "block.minecraft.medium_amethyst_bud": "Bòcolo de ametista medio", "block.minecraft.melon": "Inguria", "block.minecraft.melon_stem": "Pianta de inguria", "block.minecraft.moss_block": "Bloco de muscio", "block.minecraft.moss_carpet": "Tapéo de muscio", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON> muscia<PERSON>", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON> de piere musciade", "block.minecraft.mossy_cobblestone_stairs": "Scałe de piera musciada", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> de piere musciade", "block.minecraft.mossy_stone_brick_slab": "Łiston de cuarełi de piera musciadi", "block.minecraft.mossy_stone_brick_stairs": "Scałe de cuarełi de piera musciadi", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de cuarełi de piera musciada", "block.minecraft.mossy_stone_bricks": "Cuarełi de piera musciadi", "block.minecraft.moving_piston": "<PERSON><PERSON> drio mó<PERSON>", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Łiston de cuareli de Fango", "block.minecraft.mud_brick_stairs": "Scałe de cuareli de Fango", "block.minecraft.mud_brick_wall": "<PERSON><PERSON>", "block.minecraft.mud_bricks": "C<PERSON><PERSON><PERSON> de <PERSON>o", "block.minecraft.muddy_mangrove_roots": "<PERSON>dice fangos<PERSON> de Mangrovia", "block.minecraft.mushroom_stem": "Ganbo de fongo", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Ringhiera de cuarełi de'l Nether", "block.minecraft.nether_brick_slab": "<PERSON><PERSON><PERSON> de cuarełi de'l Nether", "block.minecraft.nether_brick_stairs": "Scałe de cuarełi de'l Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON>ł<PERSON>", "block.minecraft.nether_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_gold_ore": "Mineral de Oro del Nether", "block.minecraft.nether_portal": "<PERSON> de'l Nether", "block.minecraft.nether_quartz_ore": "Mineral de Cuarso del Nether", "block.minecraft.nether_sprouts": "<PERSON><PERSON>", "block.minecraft.nether_wart": "<PERSON><PERSON>", "block.minecraft.nether_wart_block": "Bloco de pori de'l Nether", "block.minecraft.netherite_block": "Bloco de netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloco sonoro", "block.minecraft.oak_button": "<PERSON><PERSON>", "block.minecraft.oak_door": "Porta de ròvare", "block.minecraft.oak_fence": "Ringhiera de <PERSON>ò<PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "Cartel de ròvare p<PERSON>à", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Fusto <PERSON> rò<PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Pedana a presion de ròvare", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sign": "Cartel de ròvare", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_stairs": "Scałe de ròvare", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_hanging_sign": "Cartel de ròvare picà a parede", "block.minecraft.oak_wall_sign": "Cartel de ròvare a parede", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Oservador", "block.minecraft.obsidian": "Osidiana", "block.minecraft.ochre_froglight": "Lumerana <PERSON>", "block.minecraft.ominous_banner": "Gonfałon de ła dezdita", "block.minecraft.open_eyeblossom": "Ocidea verta", "block.minecraft.orange_banner": "Gonfałon naransa", "block.minecraft.orange_bed": "Łeto naransa", "block.minecraft.orange_candle": "Candela naransa", "block.minecraft.orange_candle_cake": "Torta co' candela naransa", "block.minecraft.orange_carpet": "Tapéo naransa", "block.minecraft.orange_concrete": "<PERSON><PERSON> na<PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton naransa", "block.minecraft.orange_glazed_terracotta": "Teracota naransa zmaltà", "block.minecraft.orange_shulker_box": "Scàtoła de shulker naransa", "block.minecraft.orange_stained_glass": "Vero naransa", "block.minecraft.orange_stained_glass_pane": "Panel de vero naransa", "block.minecraft.orange_terracotta": "Teracota naransa", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON><PERSON> naran<PERSON>", "block.minecraft.orange_wool": "<PERSON><PERSON> na<PERSON>", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON>", "block.minecraft.oxidized_copper": "<PERSON><PERSON>", "block.minecraft.oxidized_copper_bulb": "Lam<PERSON><PERSON> de ramo o<PERSON>", "block.minecraft.oxidized_copper_door": "Porta de ramo o<PERSON>à", "block.minecraft.oxidized_copper_grate": "<PERSON>la de ramo o<PERSON>", "block.minecraft.oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON> de ramo o<PERSON>", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON>", "block.minecraft.oxidized_cut_copper_slab": "Liston de Ramo O<PERSON>d<PERSON> Intajà", "block.minecraft.oxidized_cut_copper_stairs": "Scale de Ramo Ossidà Intajà", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "<PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "Mus'cio pendente pałido", "block.minecraft.pale_moss_block": "Bloco de mus'cio pałido", "block.minecraft.pale_moss_carpet": "Tapeto de mus'cio pałido", "block.minecraft.pale_oak_button": "<PERSON><PERSON> de ròvare pa<PERSON>", "block.minecraft.pale_oak_door": "Porta de ròvare pałida", "block.minecraft.pale_oak_fence": "Ringhiera de ròvare pałida", "block.minecraft.pale_oak_fence_gate": "Sarajo de ròvare pałida", "block.minecraft.pale_oak_hanging_sign": "Cartel de ròvare pałido", "block.minecraft.pale_oak_leaves": "Foje de ròvare pałido", "block.minecraft.pale_oak_log": "Fusto de ròvare pałido", "block.minecraft.pale_oak_planks": "<PERSON><PERSON> <PERSON> rò<PERSON> pa<PERSON>", "block.minecraft.pale_oak_pressure_plate": "Pedana a presion de ròvare pałido", "block.minecraft.pale_oak_sapling": "Albareło de ròvare pałido", "block.minecraft.pale_oak_sign": "Cartel de ròvare pałido", "block.minecraft.pale_oak_slab": "Łiston de ròvare pałido", "block.minecraft.pale_oak_stairs": "Scałe de ròvare pałido", "block.minecraft.pale_oak_trapdoor": "Rebalta de ròvare pałido", "block.minecraft.pale_oak_wall_hanging_sign": "Cartel de ròvare pałido picà a parede", "block.minecraft.pale_oak_wall_sign": "Cartel de ròvare pałido a parede", "block.minecraft.pale_oak_wood": "Ł<PERSON>jo de ròvare pał<PERSON>", "block.minecraft.pearlescent_froglight": "Lumerana Viola", "block.minecraft.peony": "Fołanja", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON><PERSON> de rò<PERSON>e <PERSON>", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON> a parede", "block.minecraft.pink_banner": "Gonfałon roza", "block.minecraft.pink_bed": "Łeto roza", "block.minecraft.pink_candle": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle_cake": "Torta co' candela rosa", "block.minecraft.pink_carpet": "Tapéo rosa", "block.minecraft.pink_concrete": "<PERSON><PERSON> roza", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton roza", "block.minecraft.pink_glazed_terracotta": "Teracota roza zmaltà", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_shulker_box": "Scàtoła de shulker roza", "block.minecraft.pink_stained_glass": "Vero roza", "block.minecraft.pink_stained_glass_pane": "Panel de vero roza", "block.minecraft.pink_terracotta": "Teracota roza", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON><PERSON> roza", "block.minecraft.pink_wool": "<PERSON><PERSON> roza", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Testa de piston", "block.minecraft.pitcher_crop": "Coltura de pianta bocal", "block.minecraft.pitcher_plant": "Pianta bocal", "block.minecraft.player_head": "<PERSON><PERSON>", "block.minecraft.player_head.named": "Testa de %s", "block.minecraft.player_wall_head": "<PERSON>a de zugador a muro", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "Andezite łustra", "block.minecraft.polished_andesite_slab": "<PERSON><PERSON><PERSON> de andezite łustra", "block.minecraft.polished_andesite_stairs": "Scałe de andezite łustra", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON> lustro", "block.minecraft.polished_blackstone": "Pierane<PERSON> Lu<PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Liston de Cuarèi de Pieranegra Lustra", "block.minecraft.polished_blackstone_brick_stairs": "Scale de Cuarèi de Pieranegra Lustra", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de Cuarèi de Pieranegra Lustra", "block.minecraft.polished_blackstone_bricks": "Cuarèi de Pieranegra Lustra", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON> de Pieranegra <PERSON>", "block.minecraft.polished_blackstone_pressure_plate": "Pedana a pression de Pieranegra Lustra", "block.minecraft.polished_blackstone_slab": "Liston de Pieranegra Lustra", "block.minecraft.polished_blackstone_stairs": "Scale de Pieranegra Lustra", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON>", "block.minecraft.polished_deepslate": "Fondardesia lustra", "block.minecraft.polished_deepslate_slab": "Liston de Fondardesia Lustra", "block.minecraft.polished_deepslate_stairs": "Scale de Fondardesia Lustra", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> F<PERSON>rdes<PERSON>", "block.minecraft.polished_diorite": "Diorite łustra", "block.minecraft.polished_diorite_slab": "Łiston de diorite łustra", "block.minecraft.polished_diorite_stairs": "Scałe de diorite łustra", "block.minecraft.polished_granite": "<PERSON><PERSON>", "block.minecraft.polished_granite_slab": "<PERSON>ist<PERSON> de granito <PERSON>", "block.minecraft.polished_granite_stairs": "Scałe de granito łustro", "block.minecraft.polished_tuff": "<PERSON><PERSON> lustro", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON> de Tufo lustro", "block.minecraft.polished_tuff_stairs": "Scale de Tufo lustro", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Albareło de acasia in vazo", "block.minecraft.potted_allium": "<PERSON>or de ajo in vazo", "block.minecraft.potted_azalea_bush": "Vaso co' azalèa", "block.minecraft.potted_azure_bluet": "<PERSON><PERSON> de <PERSON> in vazo", "block.minecraft.potted_bamboo": "Canavera in vazo", "block.minecraft.potted_birch_sapling": "Albareło de brédoła in vaso", "block.minecraft.potted_blue_orchid": "Orchidèa blè in vaso", "block.minecraft.potted_brown_mushroom": "Fongo maron in vazo", "block.minecraft.potted_cactus": "Cactus in vazo", "block.minecraft.potted_cherry_sapling": "Albarelo de çiresar in vaso", "block.minecraft.potted_closed_eyeblossom": "Ocidea serà in vaso", "block.minecraft.potted_cornflower": "Sełestin in vazo", "block.minecraft.potted_crimson_fungus": "Fongo crèmese in vaso", "block.minecraft.potted_crimson_roots": "Raixe crèmese in vaso", "block.minecraft.potted_dandelion": "Pisacan in vazo", "block.minecraft.potted_dark_oak_sapling": "Albareło de ròvare scura in vazo", "block.minecraft.potted_dead_bush": "Albareło seco in vazo", "block.minecraft.potted_fern": "Felze in vazo", "block.minecraft.potted_flowering_azalea_bush": "Vaso co' azalèa in fior", "block.minecraft.potted_jungle_sapling": "Albareło de ła jungla in vaso", "block.minecraft.potted_lily_of_the_valley": "Łiłio in vazo", "block.minecraft.potted_mangrove_propagule": "Propagulo de Mangrovia in vazo", "block.minecraft.potted_oak_sapling": "Albareło de ròvare in vaso", "block.minecraft.potted_open_eyeblossom": "Ocidea verta in vaso", "block.minecraft.potted_orange_tulip": "Tułipan naransa in vazo", "block.minecraft.potted_oxeye_daisy": "Margarita in vazo", "block.minecraft.potted_pale_oak_sapling": "Albareło de ròvare pałido in vazo", "block.minecraft.potted_pink_tulip": "Tułipan roza in vazo", "block.minecraft.potted_poppy": "Papàvaro in vazo", "block.minecraft.potted_red_mushroom": "Fongo roso in vazo", "block.minecraft.potted_red_tulip": "<PERSON><PERSON><PERSON><PERSON> roso in vazo", "block.minecraft.potted_spruce_sapling": "Albareło de avede in vaso", "block.minecraft.potted_torchflower": "Luxoflora in vaso", "block.minecraft.potted_warped_fungus": "Fongo strùpio in vaso", "block.minecraft.potted_warped_roots": "Raixe strùpie in vaso", "block.minecraft.potted_white_tulip": "Tułipan bianco in vazo", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON> in vazo", "block.minecraft.powder_snow": "<PERSON>eve pol<PERSON>", "block.minecraft.powder_snow_cauldron": "Calderon de neve polvarosa", "block.minecraft.powered_rail": "Roara ał<PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Łiston de cuarełi de prizmarin", "block.minecraft.prismarine_brick_stairs": "Scałe de cuarełi de prizmarin", "block.minecraft.prismarine_bricks": "Cuarełi de prizmarin", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON> de priz<PERSON>in", "block.minecraft.prismarine_stairs": "Scałe de prizmarin", "block.minecraft.prismarine_wall": "<PERSON><PERSON>", "block.minecraft.pumpkin": "Suca", "block.minecraft.pumpkin_stem": "Pianta de suca", "block.minecraft.purple_banner": "Gonfałon vioła", "block.minecraft.purple_bed": "Łeto vioła", "block.minecraft.purple_candle": "<PERSON><PERSON> viola", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> co' candela viola", "block.minecraft.purple_carpet": "Tapéo vioła", "block.minecraft.purple_concrete": "Beton vioła", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> de beton vioła", "block.minecraft.purple_glazed_terracotta": "Teracota vioła zmaltà", "block.minecraft.purple_shulker_box": "Scàtoła de shulker vioła", "block.minecraft.purple_stained_glass": "Vero vioła", "block.minecraft.purple_stained_glass_pane": "Panel de vero vioła", "block.minecraft.purple_terracotta": "Teracota vioła", "block.minecraft.purple_wool": "Łana vioła", "block.minecraft.purpur_block": "Purpur", "block.minecraft.purpur_pillar": "Piłastro de purpur", "block.minecraft.purpur_slab": "<PERSON><PERSON><PERSON> de purpur", "block.minecraft.purpur_stairs": "Scałe de purpur", "block.minecraft.quartz_block": "Bloco de Cuarso", "block.minecraft.quartz_bricks": "Cuarèi de Cuarso", "block.minecraft.quartz_pillar": "Pilastro de Cuarso", "block.minecraft.quartz_slab": "Liston de Cuarso", "block.minecraft.quartz_stairs": "Scale de Cuarso", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Bloco de Ramo Grezo", "block.minecraft.raw_gold_block": "Bloco de Oro Grezo", "block.minecraft.raw_iron_block": "Bloco de Fero Grezo", "block.minecraft.red_banner": "Gonfałon roso", "block.minecraft.red_bed": "Łeto roso", "block.minecraft.red_candle": "Candela rossa", "block.minecraft.red_candle_cake": "Torta co' candela rossa", "block.minecraft.red_carpet": "Tapéo rosso", "block.minecraft.red_concrete": "<PERSON>on roso", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beton rosa", "block.minecraft.red_glazed_terracotta": "Teracota rosa zmaltà", "block.minecraft.red_mushroom": "<PERSON>ongo roso", "block.minecraft.red_mushroom_block": "Bloco de fongo roso", "block.minecraft.red_nether_brick_slab": "<PERSON>ist<PERSON> de cuarełi rosi de'l <PERSON>her", "block.minecraft.red_nether_brick_stairs": "Scałe de cuarełi rosi de'l Nether", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> de cuarełi rosi de'l <PERSON>", "block.minecraft.red_nether_bricks": "Cuarełi rosi de'l <PERSON>", "block.minecraft.red_sand": "Sabia rosa", "block.minecraft.red_sandstone": "Arena<PERSON> rosa", "block.minecraft.red_sandstone_slab": "Łiston de arenaria rosa", "block.minecraft.red_sandstone_stairs": "Scałe de arenaria rosa", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> de arenaria rosa", "block.minecraft.red_shulker_box": "Scàtoła de shulker rosa", "block.minecraft.red_stained_glass": "Vero roso", "block.minecraft.red_stained_glass_pane": "Panel de vero roso", "block.minecraft.red_terracotta": "Teracota rosa", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON><PERSON> roso", "block.minecraft.red_wool": "<PERSON><PERSON> rosa", "block.minecraft.redstone_block": "Bloco de Redstone", "block.minecraft.redstone_lamp": "Lànpada redstone", "block.minecraft.redstone_ore": "Mineral de Redstone", "block.minecraft.redstone_torch": "Torsa de Redstone", "block.minecraft.redstone_wall_torch": "Torsa de Redstone a muro", "block.minecraft.redstone_wire": "<PERSON>", "block.minecraft.reinforced_deepslate": "Fondardesia Rinforsà", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Bloco comando a repetision", "block.minecraft.resin_block": "Bloco de caràsa", "block.minecraft.resin_brick_slab": "Liston de cuarèi de Rasa", "block.minecraft.resin_brick_stairs": "Scala de cuarèi de Rasa", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> de cuarèi de Ra<PERSON>", "block.minecraft.resin_bricks": "Cua<PERSON><PERSON><PERSON> de Ra<PERSON>", "block.minecraft.resin_clump": "Gropo de caràsa", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON> de respawn", "block.minecraft.rooted_dirt": "<PERSON>ra inrai<PERSON>a", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sabia", "block.minecraft.sandstone": "Arenaria", "block.minecraft.sandstone_slab": "Łiston de arenaria", "block.minecraft.sandstone_stairs": "Scałe de arenaria", "block.minecraft.sandstone_wall": "<PERSON><PERSON> de arenaria", "block.minecraft.scaffolding": "Inpalcadura", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador de sculk", "block.minecraft.sculk_sensor": "Sensor de sculk", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON><PERSON> sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON>", "block.minecraft.sea_lantern": "Łanterna de'l mar", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Erba acuàdega", "block.minecraft.set_spawn": "Ponto de re-spawn inpostà", "block.minecraft.short_dry_grass": "Erba seca curta", "block.minecraft.short_grass": "Erba curta", "block.minecraft.shroomlight": "Sluxifongo", "block.minecraft.shulker_box": "Scà<PERSON><PERSON>ker", "block.minecraft.skeleton_skull": "<PERSON><PERSON><PERSON> de schèłetro", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON><PERSON> de schèłetro a parede", "block.minecraft.slime_block": "Bloco de zeładina", "block.minecraft.small_amethyst_bud": "Bòcolo de ametista picenin", "block.minecraft.small_dripleaf": "Fojogiossa picenina", "block.minecraft.smithing_table": "Banco de'l ferar", "block.minecraft.smoker": "Fumegador", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz": "Bloco de Cuarso Lustro", "block.minecraft.smooth_quartz_slab": "Liston de Cuarso Lustro", "block.minecraft.smooth_quartz_stairs": "Scale de Cuarso Lustro", "block.minecraft.smooth_red_sandstone": "Arenaria rosa łustra", "block.minecraft.smooth_red_sandstone_slab": "Łiston de arenaria rosa łustra", "block.minecraft.smooth_red_sandstone_stairs": "Scałe de arenaria rosa łustra", "block.minecraft.smooth_sandstone": "Arenaria łustra", "block.minecraft.smooth_sandstone_slab": "Łiston de arenaria łustra", "block.minecraft.smooth_sandstone_stairs": "Scałe de arenaria łustra", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON> <PERSON> piera <PERSON>", "block.minecraft.sniffer_egg": "Ovo de snasador", "block.minecraft.snow": "<PERSON>eve", "block.minecraft.snow_block": "Bloco de neve", "block.minecraft.soul_campfire": "Fogo da canpo de le àneme", "block.minecraft.soul_fire": "Fogo de le àneme", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "Sabia de łe à<PERSON>me", "block.minecraft.soul_soil": "<PERSON><PERSON> à<PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON> de le àneme a muro", "block.minecraft.spawn.not_valid": "Te ghè nissùn leto o àncora de respawn cargada, o i jera stropà", "block.minecraft.spawner": "Zenerador de mostri", "block.minecraft.spawner.desc1": "Interazissi co' un Ovo Zenerador:", "block.minecraft.spawner.desc2": "Inposta un tipo de Mob", "block.minecraft.sponge": "S<PERSON>nga", "block.minecraft.spore_blossom": "Bòcoło de spore", "block.minecraft.spruce_button": "<PERSON><PERSON>", "block.minecraft.spruce_door": "Porta de avede", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "Cartel de avede picà", "block.minecraft.spruce_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_log": "Fu<PERSON> de a<PERSON>e", "block.minecraft.spruce_planks": "<PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "Pedana a presion de avede", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "Cartel de avede", "block.minecraft.spruce_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_stairs": "<PERSON><PERSON>ł<PERSON> de a<PERSON>", "block.minecraft.spruce_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_wall_hanging_sign": "Cartel de avede picà a parede", "block.minecraft.spruce_wall_sign": "Cartel de avede a parede", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "<PERSON><PERSON> ta<PERSON>o", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Łiston de cuarełi de piera", "block.minecraft.stone_brick_stairs": "Scałe de cuarełi de piera", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de cuarełi de piera", "block.minecraft.stone_bricks": "Cuarełi de piera", "block.minecraft.stone_button": "<PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "Pedana a presion de piera", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "Scałe de piera", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Fusto de acasia descorsà", "block.minecraft.stripped_acacia_wood": "Łenjo de acasia descorsà", "block.minecraft.stripped_bamboo_block": "Bloco de canavera descorsà", "block.minecraft.stripped_birch_log": "Fusto de brédoła descorsà", "block.minecraft.stripped_birch_wood": "Łenjo de brédoła descorsà", "block.minecraft.stripped_cherry_log": "Fusto de çiresar descorsà", "block.minecraft.stripped_cherry_wood": "Legno de çiresar descorsà", "block.minecraft.stripped_crimson_hyphae": "Ife crèmese descortegàe", "block.minecraft.stripped_crimson_stem": "Ganbo crèmese descortegà", "block.minecraft.stripped_dark_oak_log": "Fusto de ròvare scura descorsà", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON> de ròvare scura descorsà", "block.minecraft.stripped_jungle_log": "Fusto de ła jungla descorsà", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON> de ła jungla descorsà", "block.minecraft.stripped_mangrove_log": "Fusto de Mangrovia descorsà", "block.minecraft.stripped_mangrove_wood": "Łegno de Mangrovia descorsà", "block.minecraft.stripped_oak_log": "Fusto de ròvare descorsà", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON> de ròvare des<PERSON>", "block.minecraft.stripped_pale_oak_log": "Fusto de ròvare ciara descorsà", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON> de ròvare ciara descorsà", "block.minecraft.stripped_spruce_log": "Fusto de avede descorsà", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON> de avede descorsà", "block.minecraft.stripped_warped_hyphae": "Ife strùpie descortegàe", "block.minecraft.stripped_warped_stem": "Ganbo strùpio <PERSON>", "block.minecraft.structure_block": "Bloco strutura", "block.minecraft.structure_void": "<PERSON><PERSON> de ła strutura", "block.minecraft.sugar_cane": "<PERSON><PERSON> <PERSON>", "block.minecraft.sunflower": "Zirasol", "block.minecraft.suspicious_gravel": "G<PERSON> so<PERSON>eta", "block.minecraft.suspicious_sand": "Sabia sospeta", "block.minecraft.sweet_berry_bush": "Giavasco de pomełe dolse", "block.minecraft.tall_dry_grass": "Erba seca elta", "block.minecraft.tall_grass": "<PERSON><PERSON>a elta", "block.minecraft.tall_seagrass": "Erba acuàdega alta", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Teracota", "block.minecraft.test_block": "Bloco Test", "block.minecraft.test_instance_block": "Bloco istansa de test", "block.minecraft.tinted_glass": "Vero scurìo", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "<PERSON><PERSON>", "block.minecraft.torchflower": "Luxoflora", "block.minecraft.torchflower_crop": "Pianta de luxoflora", "block.minecraft.trapped_chest": "Baùl <PERSON>", "block.minecraft.trial_spawner": "Zenerador de sfide", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Ingancio par fiło", "block.minecraft.tube_coral": "Coral a tubo", "block.minecraft.tube_coral_block": "Bloco de coral a tubo", "block.minecraft.tube_coral_fan": "Gorgonia a tubo", "block.minecraft.tube_coral_wall_fan": "Gorgonia a tubo a parede", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Liston de Cuarèi de Tufo", "block.minecraft.tuff_brick_stairs": "Scale de Cuarèi de Tufo", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> Cuarèi de Tufo", "block.minecraft.tuff_bricks": "Cuar<PERSON><PERSON>", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "Scale de Tufo", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Ovo de tartaruga", "block.minecraft.twisting_vines": "Ranpeganti intorcolài", "block.minecraft.twisting_vines_plant": "Fusto de ranpeganti intocrcolài", "block.minecraft.vault": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "Lumerana Verda", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Aria de'l Vodo", "block.minecraft.wall_torch": "<PERSON>sa a muro", "block.minecraft.warped_button": "<PERSON><PERSON> strù<PERSON>", "block.minecraft.warped_door": "Porta strùpia", "block.minecraft.warped_fence": "Ringhiera strù<PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON> str<PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON> strù<PERSON>", "block.minecraft.warped_hanging_sign": "Cartel strùpio picà", "block.minecraft.warped_hyphae": "<PERSON>e strù<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON> s<PERSON>", "block.minecraft.warped_pressure_plate": "Pedana a pression strùpia", "block.minecraft.warped_roots": "Raixe strùpie", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> str<PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON> str<PERSON>", "block.minecraft.warped_stairs": "Scale strùpie", "block.minecraft.warped_stem": "Ganbo strù<PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON> str<PERSON>", "block.minecraft.warped_wall_hanging_sign": "Cartel strùpio picà a parede", "block.minecraft.warped_wall_sign": "Cartelo strùpio a parede", "block.minecraft.warped_wart_block": "Bloco de pori strùpi", "block.minecraft.water": "Acua", "block.minecraft.water_cauldron": "<PERSON><PERSON>", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON>", "block.minecraft.waxed_copper_block": "Bloco de Ramo Inçerà", "block.minecraft.waxed_copper_bulb": "Lam<PERSON><PERSON> de ramo in<PERSON>", "block.minecraft.waxed_copper_door": "Porta de ramo incerà", "block.minecraft.waxed_copper_grate": "Gradela de ramo in<PERSON>", "block.minecraft.waxed_copper_trapdoor": "Rebalta de Rame Incerà", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON>", "block.minecraft.waxed_cut_copper_slab": "Liston de Ramo Intajà Inçerà", "block.minecraft.waxed_cut_copper_stairs": "Scale de Ramo Intajà Inçerà", "block.minecraft.waxed_exposed_chiseled_copper": "Ram<PERSON> E<PERSON>on<PERSON>à", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON>", "block.minecraft.waxed_exposed_copper_bulb": "Lampada de Ramo Esponùo Intajà Inçerà", "block.minecraft.waxed_exposed_copper_door": "Porta de Ramo Esponùo Intajà Inçerà", "block.minecraft.waxed_exposed_copper_grate": "Gradela de Ramo Esponùo Intajà Inçerà", "block.minecraft.waxed_exposed_copper_trapdoor": "Rebalta de Ramo Esponùo Intajà Inçerà", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON> Inçerà", "block.minecraft.waxed_exposed_cut_copper_slab": "Liston de Ramo Esponùo Intajà Inçerà", "block.minecraft.waxed_exposed_cut_copper_stairs": "Scale de Ramo Esponùo Intajà Inçerà", "block.minecraft.waxed_oxidized_chiseled_copper": "Ram<PERSON> Laorà Inçerà", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON>çerà", "block.minecraft.waxed_oxidized_copper_bulb": "Lampada de Ramo Ossidà Intajà Inçerà", "block.minecraft.waxed_oxidized_copper_door": "Porta de Ramo Ossidà Intajà Inçerà", "block.minecraft.waxed_oxidized_copper_grate": "Gradela de Ramo Ossidà Intajà Inçerà", "block.minecraft.waxed_oxidized_copper_trapdoor": "Rebalta de Ramo Ossidà Intajà Inçerà", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON> Intajà Inçerà", "block.minecraft.waxed_oxidized_cut_copper_slab": "Liston de Ramo Ossidà Intajà Inçerà", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Scale de Ramo Ossidà Intajà Inçerà", "block.minecraft.waxed_weathered_chiseled_copper": "Ramo Smagnà Laorà Inçerà", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON> Inçerà", "block.minecraft.waxed_weathered_copper_bulb": "Lampada de Ramo Smagnà Inçerà", "block.minecraft.waxed_weathered_copper_door": "Porta de Ramo Smagnà Inçerà", "block.minecraft.waxed_weathered_copper_grate": "Gradela de Ramo Smagnà Inçerà", "block.minecraft.waxed_weathered_copper_trapdoor": "Rebalta de Ramo Smagnà Inçerà", "block.minecraft.waxed_weathered_cut_copper": "Ramo <PERSON> Intajà Inçerà", "block.minecraft.waxed_weathered_cut_copper_slab": "Liston de Ramo Smagnà Intajà Inçerà", "block.minecraft.waxed_weathered_cut_copper_stairs": "Scale de Ramo Smagnà Intajà Inçerà", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON>", "block.minecraft.weathered_copper": "<PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.weathered_copper_door": "Porta de Ramo Smagnà", "block.minecraft.weathered_copper_grate": "Gradela de Ramo <PERSON>", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON>", "block.minecraft.weathered_cut_copper_slab": "Liston de Ramo <PERSON>j<PERSON> Smagnà", "block.minecraft.weathered_cut_copper_stairs": "Scale de Ramo Intajà Smagnà", "block.minecraft.weeping_vines": "<PERSON>n<PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "Fusto de ranpeganti pian<PERSON>ti", "block.minecraft.wet_sponge": "Sponga moja", "block.minecraft.wheat": "Coltivasion de formento", "block.minecraft.white_banner": "Gonfałon bianco", "block.minecraft.white_bed": "Łeto bianco", "block.minecraft.white_candle": "Candela bianca", "block.minecraft.white_candle_cake": "Torta co' candela bianca", "block.minecraft.white_carpet": "Tapéo bianco", "block.minecraft.white_concrete": "Beton bianco", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>on bianca", "block.minecraft.white_glazed_terracotta": "Teracota bianca zmaltà", "block.minecraft.white_shulker_box": "Scàtoła de shulker bianca", "block.minecraft.white_stained_glass": "Vero bianco", "block.minecraft.white_stained_glass_pane": "Panel de vero bianco", "block.minecraft.white_terracotta": "Teracota bianca", "block.minecraft.white_tulip": "Tułipan bianco", "block.minecraft.white_wool": "<PERSON><PERSON> bianca", "block.minecraft.wildflowers": "<PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON><PERSON> s<PERSON>èł<PERSON>ro wither", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON><PERSON> de schèłetro wither a parede", "block.minecraft.yellow_banner": "Gonfałon zało", "block.minecraft.yellow_bed": "Łeto zało", "block.minecraft.yellow_candle": "Candela zala", "block.minecraft.yellow_candle_cake": "Torta co' candela zala", "block.minecraft.yellow_carpet": "Tapéo zało", "block.minecraft.yellow_concrete": "<PERSON><PERSON> zało", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> zała", "block.minecraft.yellow_glazed_terracotta": "Teracota zała zmaltà", "block.minecraft.yellow_shulker_box": "Scàtoła de shulker zała", "block.minecraft.yellow_stained_glass": "Vero zało", "block.minecraft.yellow_stained_glass_pane": "Panel de vero zało", "block.minecraft.yellow_terracotta": "Teracota zała", "block.minecraft.yellow_wool": "Łana zała", "block.minecraft.zombie_head": "Testa de <PERSON>bi", "block.minecraft.zombie_wall_head": "Testa de zonbi a parede", "book.byAuthor": "de %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Tìtoło del łibro:", "book.finalizeButton": "Firma e sara", "book.finalizeWarning": "Ocio! Dopo aver firmà el łibro no te podarè pì modifegarlo.", "book.generation.0": "Orizenal", "book.generation.1": "Copia de'l orizenal", "book.generation.2": "Copia de na copia", "book.generation.3": "Strasà", "book.invalid.tag": "* Tag łibro mìa vàłido *", "book.pageIndicator": "Pàzina %1$s de %2$s", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "Firma", "book.view.title": "Book View Screen", "build.tooHigh": "El łìmite de altessa par costruir l'é de %s blochi", "chat.cannotSend": "Inposìiłe mandar el mesajo in chat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Fa' clic par tełetrasportarte", "chat.copy": "Copia inte i apunti", "chat.copy.click": "Fa' clic par copiar i ìnt'i aponti", "chat.deleted_marker": "Sto mesajo el se stato liminá da'l server.", "chat.disabled.chain_broken": "Chat smorzà par na cadena spacà. Prova a ricołegarte.", "chat.disabled.expiredProfileKey": "Chat dezativà parché tí ciave de profio publico xe scadua. Par piazer prova a re-intrar nel server.", "chat.disabled.invalid_command_signature": "Firme dei argomenti del comando no previste o che manca.", "chat.disabled.invalid_signature": "Firma deła chat non valida. Prova a ricołegarte.", "chat.disabled.launcher": "Chat desativada in le opsion laucher, no s'ha pudùo inviar el messajo.", "chat.disabled.missingProfileKey": "Chat dezativà parché tí ciave de profio publico xe scadua. Par piazer prova a re-intrar nel server.", "chat.disabled.options": "Chat desativada in le opsion client.", "chat.disabled.out_of_order_chat": "La conversasiòn gà ricevuo non funsionante. L'orario de'l to sistema zé cambià?", "chat.disabled.profile": "Chat mìa consentida par opsion de account. Struca '%s' de novo par pì informasioni.", "chat.disabled.profile.moreInfo": "Chat mìa consentida par opsion de account. Inposibile véder o mandar mesaji.", "chat.editBox": "chat", "chat.filtered": "Filtrà dal server.", "chat.filtered_full": "Sto server gá scontà ti mesajo par alcuni zogatori.", "chat.link.confirm": "Sito seguro de vołer verzar sto sito?", "chat.link.confirmTrusted": "Vuto v<PERSON>ar sto link o vuto copiarlo inte i apunti?", "chat.link.open": "Verzi inte'l browser", "chat.link.warning": "<PERSON> vè<PERSON><PERSON> link da parsone che te conosi mìa!", "chat.queue": "[+%s mesazi in ateza]", "chat.square_brackets": "[%s]", "chat.tag.error": "Mesajo del server non vałido.", "chat.tag.modified": "Mesajo modifegà dal server. Originałe:", "chat.tag.not_secure": "Mesajo no verificà. No se połe segnałare.", "chat.tag.system": "Mesajo de'l server. No se połe segnałare.", "chat.tag.system_single_player": "<PERSON><PERSON><PERSON><PERSON> dal servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s el ga conpletà ła sfida %s", "chat.type.advancement.goal": "%s el ga razonto el obietivo %s", "chat.type.advancement.task": "%s el ga reałizà el progreso %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Sc<PERSON>vi a ła scuadra", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s el dize %s", "chat.validation_error": "Errore de convalida conversasion", "chat_screen.message": "<PERSON><PERSON> da mandar: %s", "chat_screen.title": "<PERSON><PERSON><PERSON> de chat", "chat_screen.usage": "Sc<PERSON>vi un mesajo e struca Invio par mandar", "chunk.toast.checkLog": "Varda el registro par altri detagli", "chunk.toast.loadFailure": "Imposibiłe cargare el chunk a %s", "chunk.toast.lowDiskSpace": "Poco spàsio so'l disco!", "chunk.toast.lowDiskSpace.description": "El salvatajo del mondo podrìa nar male.", "chunk.toast.saveFailure": "Imposibiłe salvare el chunk a %s", "clear.failed.multiple": "Njisun ozeto l'é stà catà in %s zugadori", "clear.failed.single": "Njisun ozeto l'é stà catà in %s", "color.minecraft.black": "Negro", "color.minecraft.blue": "Blè", "color.minecraft.brown": "Mar<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "Grizo", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Sełeste", "color.minecraft.light_gray": "Grizo ciaro", "color.minecraft.lime": "Lime", "color.minecraft.magenta": "<PERSON><PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON><PERSON><PERSON>", "color.minecraft.purple": "Vioła", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "Bianco", "color.minecraft.yellow": "<PERSON><PERSON><PERSON><PERSON>", "command.context.here": "<--[<PERSON>U<PERSON>]", "command.context.parse_error": "%s in pozixion %s: %s", "command.exception": "Inposìbiłe anałizar el comando: %s", "command.expected.separator": "Previsto un spasio bianco par sarar un argomento, ma i'é stadi catadi dei dati trailing", "command.failed": "S'à verifegà un eror tentando de ezeguir 'sto comando", "command.forkLimit": "Rivà al numero massimo de contesti (%s)", "command.unknown.argument": "Argomento de'l comando mìa vàłido", "command.unknown.command": "Comando mìa conosesto o inconpleto, varda in baso l'eror", "commands.advancement.criterionNotFound": "El progreso %1$s no'l contien el criterio \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "Inposìbiłe asenjar el criterio \"%s\" de'l progreso %s a %s zugadori parché i ło posiede za", "commands.advancement.grant.criterion.to.many.success": "El criterio \"%s\" de'l progreso %s l'é stà asenjà a %s zugadori", "commands.advancement.grant.criterion.to.one.failure": "Inposìbiłe asenjar el criterio \"%s\" de'l progreso %s a %s parché el ło posiede za", "commands.advancement.grant.criterion.to.one.success": "El criterio \"%s\" de'l progreso %s l'é stà asenjà a %s", "commands.advancement.grant.many.to.many.failure": "Inposìbiłe asenjar %s progresi a %s zugadori parché i łi posiede za", "commands.advancement.grant.many.to.many.success": "%s progresi i ze stadi asenjadi a %s zugadori", "commands.advancement.grant.many.to.one.failure": "Inposìbiłe asenjar %s progresi a %s parché el łi posiede za", "commands.advancement.grant.many.to.one.success": "%s progresi i'é stadi asenjadi a %s", "commands.advancement.grant.one.to.many.failure": "Inposìbiłe asenjar el progreso %s a %s zugadori parché i ło posiede za", "commands.advancement.grant.one.to.many.success": "El progreso %s l'é stà asenjà a %s zugadori", "commands.advancement.grant.one.to.one.failure": "Inposìbiłe asenjar el progreso %s a %s parché el ło posiede za", "commands.advancement.grant.one.to.one.success": "El progreso %s l'é stà asenjà %s", "commands.advancement.revoke.criterion.to.many.failure": "Inposìbiłe remóvar el criterio \"%s\" de'l progreso %s da %s zugadori parché no i ło posiede", "commands.advancement.revoke.criterion.to.many.success": "El criterio \"%s\" de'l progreso %s l'é stà removesto da %s zugadori", "commands.advancement.revoke.criterion.to.one.failure": "Inposìbiłe remóvar el criterio \"%s\" de'l progreso %s da %s parché no'l ło posiede", "commands.advancement.revoke.criterion.to.one.success": "El criterio \"%s\" de'l progreso %s l'é stà removesto da %s", "commands.advancement.revoke.many.to.many.failure": "Inposìbiłe remóvar %s progresi da %s zugadori parché no i łi posiede", "commands.advancement.revoke.many.to.many.success": "%s progresi i ze stadi removesti da %s zugadori", "commands.advancement.revoke.many.to.one.failure": "Inposìbiłe remóvar %s progresi da %s parché no'l łi posiede", "commands.advancement.revoke.many.to.one.success": "%s progresi i ze stadi removesti da %s", "commands.advancement.revoke.one.to.many.failure": "Inposìbiłe remóvar el progreso %s da %s zugadori parché no i ło posiede", "commands.advancement.revoke.one.to.many.success": "El progreso %s l'é stà removesto da %s zugadori", "commands.advancement.revoke.one.to.one.failure": "Inposìbiłe remóvar el progreso %s da %s parché no'l ło posiede", "commands.advancement.revoke.one.to.one.success": "El progreso %s l'é stà removesto da %s", "commands.attribute.base_value.get.success": "El vałor baze de'l atribudo %s par l'entidà %s l'é %s", "commands.attribute.base_value.reset.success": "El vałor baze de'l atribudo %s par l'entidà %s reinpostà a default %s", "commands.attribute.base_value.set.success": "El vałor baze de'l atribudo %s par l'entidà %s l'é inpostà %s", "commands.attribute.failed.entity": "%s no l'é na entidà vàłida par 'sto comando", "commands.attribute.failed.modifier_already_present": "El modifegador %s l'é zà apligà a'l atribùo %s par l'entidà %s", "commands.attribute.failed.no_attribute": "L'entidà %s no ła ga l'atribudo %s", "commands.attribute.failed.no_modifier": "L'atribudo %s par l'entidà %s el ga mìa el modifegador %s", "commands.attribute.modifier.add.success": "El modifegator %s l'é stà apligà a'l atribudo %s par l'entidà %s", "commands.attribute.modifier.remove.success": "El modifegator %s l'é stà cavà da'l atribudo %s par l'entidà %s", "commands.attribute.modifier.value.get.success": "El vałor de'l modifegator %s de'l atribudo %spar l'entidà %s l'é %s", "commands.attribute.value.get.success": "El vałor de'l atribudo %s par l'entidà %s l'é %s", "commands.ban.failed": "<PERSON><PERSON><PERSON>: 'sto zugador l'é stà za banà", "commands.ban.success": "%s l'é stà banà: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON>: 'sto inderiso IP l'é za stà blocà", "commands.banip.info": "'Sto ban el intaresa %s zugador(i): %s", "commands.banip.invalid": "Inderiso IP mìa vàłido o zugador mìa conosesto", "commands.banip.success": "L'indersiso IP %s l'é stà banà: %s", "commands.banlist.entry": "%s l'é stà bandìo da %s: %s", "commands.banlist.entry.unknown": "(No conossùo)", "commands.banlist.list": "Ghe ze %s ban:", "commands.banlist.none": "No ghe ze ban", "commands.bossbar.create.failed": "Ła eziste za na bara boss co ID \"%s\"", "commands.bossbar.create.success": "Ła bara boss %s l'é stada creada", "commands.bossbar.get.max": "El vałor màsimo de ła bara boss %s l'é %s", "commands.bossbar.get.players.none": "<PERSON><PERSON><PERSON> zu<PERSON> asocià a ła bara boss %s l'é online", "commands.bossbar.get.players.some": "Ła bara boss parsonalizà %s ła gà %s zugador/i online al momento: %s", "commands.bossbar.get.value": "El vałor atual de ła bara boss %s l'é %s", "commands.bossbar.get.visible.hidden": "Ła bara boss %s l'é atualmente sconta", "commands.bossbar.get.visible.visible": "Ła bara boss %s l'é atualmente vizìbiłe", "commands.bossbar.list.bars.none": "No ghe ze bare boss ative", "commands.bossbar.list.bars.some": "Ghe ze %s bara(e) boss parsonalizà(e) ativa(e): %s", "commands.bossbar.remove.success": "Ła bara boss %s l'é stada ełimenada", "commands.bossbar.set.color.success": "El cołor de ła bara boss %s l'é stà canbià", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON> can<PERSON>: l'é za el cołor de 'sta bara boss", "commands.bossbar.set.max.success": "El vałor màsimo de ła bara boss %s l'é stà canbià in %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON> can<PERSON>: l'é za el vałor màsimo de 'sta bara boss", "commands.bossbar.set.name.success": "Ła bara boss%s l'é stada renominada", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON>: l'é za el nome de 'sta bara boss", "commands.bossbar.set.players.success.none": "Ła bara boss %s no l'é asosiada pì a un zugador", "commands.bossbar.set.players.success.some": "Ła bara boss parsonalizà %s ła gà deso %s zugador(i): %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON><PERSON> canbia<PERSON>: 'sti zu<PERSON> i'é za asociadi a ła bara boss e njisun el va zontà o cavà", "commands.bossbar.set.style.success": "El stiłe de ła bara boss %s l'é stà canbià", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON> can<PERSON>: l'é za el stiłe de 'sta bara boss", "commands.bossbar.set.value.success": "El vałor atual de ła bara boss %s l'é stà canbià in %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON> can<PERSON>: l'é za el vałor de 'sta bara boss", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON>: 'sta bara boss l'é za sconta", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON>: 'sta bara boss l'é za vizìbiłe", "commands.bossbar.set.visible.success.hidden": "Ła bara boss %s l'é deso sconta", "commands.bossbar.set.visible.success.visible": "Ła bara boss %s l'é deso in mostra", "commands.bossbar.unknown": "<PERSON><PERSON>'<PERSON> nji<PERSON>a bossbar con el ID \"%s\"", "commands.clear.success.multiple": "Removùo %s ozeto(i) da %s zugador(i)", "commands.clear.success.single": "Removùo %s ozeto(i) da %s", "commands.clear.test.multiple": "Catà na corespondensa de %s ozeto(i) so %s zugadori", "commands.clear.test.single": "Catà na corespondensa de %s ozeto(i) so %s", "commands.clone.failed": "Njisun bloco l'é stà clonà", "commands.clone.overlap": "Łe àree de orìzene e de destinasion łe pol mìa conbazar", "commands.clone.success": "Blochi clonài: %s", "commands.clone.toobig": "Masa blochi inte l'àrea (màsimo: %s, spesifegadi: %s)", "commands.damage.invulnerable": "El bersajo l'è invulnerabiłe al dano de sto tipo", "commands.damage.success": "Ti gà dà %s dani a %s", "commands.data.block.get": "%s de'l bloco in %s, %s, %s con fator de scała pari a %s, l'é %s", "commands.data.block.invalid": "El bloco de destinasion no l'é na entità bloco", "commands.data.block.modified": "I dati bloco in %s, %s, %s i'é stadi modifegadi", "commands.data.block.query": "%s,%s,%s el ga i dati de bloco drioman: %s", "commands.data.entity.get": "%s de %s, con fator de scała pari a %s, l'é %s", "commands.data.entity.invalid": "Inposìbiłe modifegar i dati de'l zugador", "commands.data.entity.modified": "I dati entità de %s i'é stadi modifegadi", "commands.data.entity.query": "%s el ga i dati de entità drioman: %s", "commands.data.get.invalid": "Inposìbiłe recuparar %s: i'è mìa parmesi tag numèreghi", "commands.data.get.multiple": "'Sto argomento el aceta un sol vałor NBT", "commands.data.get.unknown": "Inposìbiłe recuparar %s; el tag el eziste mìa", "commands.data.merge.failed": "Nissun canbiamento: le propietà spesifegade le gà zà 'sti valori", "commands.data.modify.expected_list": "Prevista na łista, resevùo: %s", "commands.data.modify.expected_object": "Previsto un ozeto, resevùo: %s", "commands.data.modify.expected_value": "Previsto on vałore, resevùo: %s", "commands.data.modify.invalid_index": "Ìndeze łista mìa vàłido: %s", "commands.data.modify.invalid_substring": "Indize de sotostringa invalidi: %s a %s", "commands.data.storage.get": "%s inte'l contenidor %s, co fator de scała de %s, l'é %s", "commands.data.storage.modified": "Contenidor %s modifegà", "commands.data.storage.query": "El contenidor %s el ga i contenjùi drioman: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "El pacheto de dati %s l'é mia ativo!", "commands.datapack.disable.failed.feature": "El pacheto '%s' non pol esare disabilità parché fa parte de na variabile ativa!", "commands.datapack.enable.failed": "El pacheto de dati %s l'é za ativo!", "commands.datapack.enable.failed.no_flags": "Inposibiłe ativare el pacheto '%s' parché le variabili richieste non jé attive in sto mondo: %s!", "commands.datapack.list.available.none": "<PERSON><PERSON><PERSON> p<PERSON> de dati l'é desponìbiłe", "commands.datapack.list.available.success": "I è desponìbiłe(i) %s pacheto(i) de dati: %s", "commands.datapack.list.enabled.none": "<PERSON><PERSON><PERSON> p<PERSON> de dati l'é ativo", "commands.datapack.list.enabled.success": "I'é ativo(i) %s pacheto(i) de dati: %s", "commands.datapack.modify.disable": "Drio dezabiłitar el pacheto de dati %s", "commands.datapack.modify.enable": "Drio abiłitar el pacheto de dati %s", "commands.datapack.unknown": "Pacheto de dati mìa conosesto: %s", "commands.debug.alreadyRunning": "La profiladure dei tick l'è za sta scominçià", "commands.debug.function.noRecursion": "Inpossibiłe traciar da rento na funsion", "commands.debug.function.noReturnRun": "Inposibiłe doparare la trazadura con \"/return run\"", "commands.debug.function.success.multiple": "%s comando/i de %s ze stà tracià nel file di output%s", "commands.debug.function.success.single": "%s comando/i dela funsion %s ze stà tracia del file output %s", "commands.debug.function.traceFailed": "Inposibile traciar la funsion", "commands.debug.notRunning": "La profiladura dei tick no l'è sta scominçià", "commands.debug.started": "Scominçià la profiladura dei tick", "commands.debug.stopped": "Blocà la profiladura dei tick dopo %s secondi e %s tick (%s tick par secondo)", "commands.defaultgamemode.success": "Ła modałità de zugo predefinìa l'é stada inpostada a %s", "commands.deop.failed": "<PERSON><PERSON><PERSON> can<PERSON>: el zugador no l'é un operador", "commands.deop.success": "%s no l'è pì un operador de'l server", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Njisun canbiamento: ła dificoltà l'é za inpostada a %s", "commands.difficulty.query": "Ła dificoltà atual l'é %s", "commands.difficulty.success": "Ła dificolà l'é stada inpostada a %s", "commands.drop.no_held_items": "L'entidà ła pol mìa tènjar ozeti", "commands.drop.no_loot_table": "L'entidà %s ła ga mia na tabeła de botini", "commands.drop.no_loot_table.block": "El bloco %s no'ł ga mia na tabeła de botini", "commands.drop.success.multiple": "Ozeti dropadi: %s", "commands.drop.success.multiple_with_table": "Ozeti dropadi da ła tabeła d'i botini %2$s: %1$s", "commands.drop.success.single": "Ozeti butà: %s %s", "commands.drop.success.single_with_table": "Butà %s %s da ła tabeła %s", "commands.effect.clear.everything.failed": "El bersajo no'l ga efeti da remóvar", "commands.effect.clear.everything.success.multiple": "<PERSON><PERSON> i efeti i ze stadi removesti da %s destinadari", "commands.effect.clear.everything.success.single": "Tuti i efeti i ze stadi removesti da %s", "commands.effect.clear.specific.failed": "El bersajo no'l ga l'efeto dimandà", "commands.effect.clear.specific.success.multiple": "L'efeto %s l'é stà removesto da %s destinadari", "commands.effect.clear.specific.success.single": "L'efeto %s l'é stà removesto da %s", "commands.effect.give.failed": "Inposìbiłe apligar 'sto efeto (el bersajo l'é imune ai efeti o el ghe n'à uno piasè podente)", "commands.effect.give.success.multiple": "L'efeto %s l'é stà apligà a %s destinadari", "commands.effect.give.success.single": "L'efeto %s l'é sta apligà a %s", "commands.enchant.failed": "<PERSON>jisun canbiamento: i bersaji i ga mìa un ozeto in man o el incantamento no'l pol èsar apligà", "commands.enchant.failed.entity": "%s no l'é na entidà vàłida par 'sto comando", "commands.enchant.failed.incompatible": "%s no el suporta 'sto incantamento", "commands.enchant.failed.itemless": "%s el tien nji<PERSON> ozeto", "commands.enchant.failed.level": "%s l'è pìasè alto de'l łivel màsimo (%s) suportà da 'sto incantamento", "commands.enchant.success.multiple": "L'incantamento %s l'é stà apligà a %s entidà", "commands.enchant.success.single": "L'incantamento %s l'é stà apligà a'l ozeto de %s", "commands.execute.blocks.toobig": "Masa blochi inte l'area (màsimo: %s, spesifegà: %s)", "commands.execute.conditional.fail": "El test l'é stà mìa pasà", "commands.execute.conditional.fail_count": "El test l'é stà mìa pasà; cuantità: %s", "commands.execute.conditional.pass": "El test l'é stà pasà", "commands.execute.conditional.pass_count": "El test l'é stà pasà; cuantità: %s", "commands.execute.function.instantiationFailure": "Inposibiłe istansiare la funsiòn %s: %s", "commands.experience.add.levels.success.multiple": "%s łivełi de espariensa i'é stadi asenjadi a %s zugadori", "commands.experience.add.levels.success.single": "%s łivełi de esperiensa i'é stadi asenjadi a %s", "commands.experience.add.points.success.multiple": "%s ponti espariensa i'é stadi asenjadi a %s zugadori", "commands.experience.add.points.success.single": "%s ponti espariensa i'é stadi asenjadi a %s", "commands.experience.query.levels": "%s el ga %s łivełi de espariensa", "commands.experience.query.points": "%s el ga %s ponti espariensa", "commands.experience.set.levels.success.multiple": "I łivełi de esperiensa de %2$s zugadori i'é stadi inpostadi a %1$s", "commands.experience.set.levels.success.single": "I łivełi de esperiensa de %2$s i'é stadi inpostadi a %1$s", "commands.experience.set.points.invalid": "Inposìbiłe inpostar ponti espariensa sora el màsimo de'l atual łivel de'l zugador", "commands.experience.set.points.success.multiple": "I ponti espariensa de %2$s zugadori i'é stadi inpostadi a %1$s", "commands.experience.set.points.success.single": "I ponti espariensa de %2$s i'é stadi inpostadi a %1$s", "commands.fill.failed": "Njisun bloco l'é stà piasà", "commands.fill.success": "Blochi piasadi: %s", "commands.fill.toobig": "Masa blochi inte l'area (màsimo: %s, spesifegà: %s)", "commands.fillbiome.success": "Bioma cambiao tra %s %s %s e %s %s %s", "commands.fillbiome.success.count": "%s bioma/i cambiao/i tra %s, %s, %s e %s, %s, %s", "commands.fillbiome.toobig": "Masa blochi inte l'area (màsimo: %s, spesifegà: %s)", "commands.forceload.added.failure": "Nisun chunk el ze stà senjà par el cargamento sforsà", "commands.forceload.added.multiple": "Marcadi %s chunk in %s da %s a %s par sforsar el cargamento", "commands.forceload.added.none": "Njisun chunk cargà sforsadamente l'é stà catà in %s", "commands.forceload.added.single": "El chunk %s in %s l'é stà marcà par el cargamento sforsà", "commands.forceload.list.multiple": "I'é stadi catadi %s chunk cargadi sforsadamente in %s a: %s", "commands.forceload.list.single": "Un chunk cargà sforsadamente el ze stà catà inte %s: %s", "commands.forceload.query.failure": "El chunk a %s in %s l'é mìa marcà par el cargamento forsà", "commands.forceload.query.success": "El chunk a %s in %s l'é marcà par el cargamento forsà", "commands.forceload.removed.all": "Tuti i chunk cargadi forsadamente in %s i'é pì marcadi", "commands.forceload.removed.failure": "Nisun chunk el ze stà marcà par el cargamento sforsà", "commands.forceload.removed.multiple": "I é pì marcadi %s chunk in %s da %s a %s par el cargamento sforsà", "commands.forceload.removed.single": "El chunk %s in %s no l'é pì marcà par el cargamento sforsà", "commands.forceload.toobig": "Masa chunk inte l'àrea spesifegada (màsimo %s, mìnimo %s)", "commands.function.error.argument_not_compound": "Tipo de argomento invalido: %s (previsto \"compound\")", "commands.function.error.missing_argument": "Argomento %2$s mancante par la funsiòn %1$s", "commands.function.error.missing_arguments": "Argomenti mancanti par la funsiòn %s", "commands.function.error.parse": "Durante l'esecuzione deła macro %s, eł comando \"%s\" el ga causà un errore: %s", "commands.function.instantiationFailure": "Inposibiłe creare n'instansa de la funsiòn %s: %s", "commands.function.result": "La funsiòn %s gà restituìo %s", "commands.function.scheduled.multiple": "Drio eseguir le funsiòn %s", "commands.function.scheduled.no_functions": "No zé stà catà nisuna funsiòn col nome %s", "commands.function.scheduled.single": "Drio eseguir la funsion %s", "commands.function.success.multiple": "Ezeguidi %s comandi da %s funsion", "commands.function.success.multiple.result": "Eseguìa la funsion %s", "commands.function.success.single": "I'é stadi ezeguidi %s órdeni de ła funsion '%s'", "commands.function.success.single.result": "La funsiòn '%2$s' gà restituìo %1$s", "commands.gamemode.success.other": "Ła modałità de zugo de %s l'é stada canbiada in %s", "commands.gamemode.success.self": "Ła to modałità de zugo l'é stada canbiada in %s", "commands.gamerule.query": "Ła règoła de zugo %s l'è atualmente inpostada a %s", "commands.gamerule.set": "Ła règoła de zugo %s l'è stada inpostada a %s", "commands.give.failed.toomanyitems": "No te poi vere più de %s de %s al colpo", "commands.give.success.multiple": "%3$s zugadori i à resevùo %2$s * %1$s", "commands.give.success.single": "%3$s l'à resevùo %2$s * %1$s", "commands.help.failed": "Comando mìa conosesto o parmesi mìa sufisienti", "commands.item.block.set.success": "Un slot in %s,%s,%s l'è stà sostituìo con %s", "commands.item.entity.set.success.multiple": "Un slot de %s entidà l'è stà sostituìo con %s", "commands.item.entity.set.success.single": "El slot %s l'è stà sostituìo con %s", "commands.item.source.no_such_slot": "La sorzente no la ga el slot %s", "commands.item.source.not_a_container": "La posision de orìzene in %s, %s, %s l'è mìa un contenidor", "commands.item.target.no_changed.known_item": "Gnisun destinadario l'ha açetà el ozeto %s inte'l slot %s", "commands.item.target.no_changes": "Gnisun destinadario l'ha açetà el ozeto inte'l slot %s", "commands.item.target.no_such_slot": "El destinadario no'l ga slot %s", "commands.item.target.not_a_container": "Ła posizion de destinazion %s, %s, %s no xé un contenidore", "commands.jfr.dump.failed": "Impossibile salvare la profilatura JFR: %s", "commands.jfr.start.failed": "Impossibiłe avviar ła pirofiłatura JFR", "commands.jfr.started": "Ła pirofiłatura JFR se sta avvià", "commands.jfr.stopped": "Ła pirofiłatura JFR se stà interrotta e salvà in %s", "commands.kick.owner.failed": "Inposibiłe tràr fora el paròn del server in'te na sesiòn LAN", "commands.kick.singleplayer.failed": "In<PERSON>si<PERSON>ł<PERSON> tràr fora in'te on mondo offline a zugadore singoło", "commands.kick.success": "%s l'é stà banà: %s", "commands.kill.success.multiple": "%s łe ze stade copade", "commands.kill.success.single": "%s l'è stà copà", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Ghi nè %s de un massimo de %s zugadori online: %s", "commands.locate.biome.not_found": "Inposìbiłe catar el bioma de tipo \"%s\" inte na distansa razonévołe", "commands.locate.biome.success": "Ła strutura de tipo %s pì visina l'è in %s (%s blochi de distansa)", "commands.locate.poi.not_found": "Inposìbiłe catar el pùnto de interese de tipo \"%s\" inte na distansa razonévołe", "commands.locate.poi.success": "Ła strutura de tipo %s pì visina l'è in %s (%s blochi de distansa)", "commands.locate.structure.invalid": "No ghe xe struture de tipo \"%s\"", "commands.locate.structure.not_found": "No se ga podesto catar struture de tipo \"%s\" chi darente", "commands.locate.structure.success": "Ła strutura de tipo %s pì visina l'è in %s (%s blochi de distansa)", "commands.message.display.incoming": "%s el t'à susurà: %s", "commands.message.display.outgoing": "T'è susurà a %s: %s", "commands.op.failed": "<PERSON><PERSON><PERSON> canbia<PERSON>: el zugador l'é za un operador", "commands.op.success": "%s l'è ora un operador de'l server", "commands.pardon.failed": "Njisun canbia<PERSON>: el zugador l'é mia banà", "commands.pardon.success": "%s no l'è pì banà", "commands.pardonip.failed": "Njisun canbiamento: el inderiso IP l'é mìa blocà", "commands.pardonip.invalid": "Inderiso IP mìa vàłido", "commands.pardonip.success": "L'indersiso IP %s no l'è pì banà", "commands.particle.failed": "Ła partezeła no l'era vizìbiłe da njisuni", "commands.particle.success": "Vizuałizasion de ła partezeła %s", "commands.perf.alreadyRunning": "La profiladura dele performance l'è za sta scominçià", "commands.perf.notRunning": "La profiladura dele performance no l'è sta scominçià", "commands.perf.reportFailed": "Inposìbełe crear senjałasion de debug", "commands.perf.reportSaved": "Senjałasion de debug creada in %s", "commands.perf.started": "Scominçià na profiladura dele prestasioni de 10 secondi (usa \"/perf stop\" par interomperla suito)", "commands.perf.stopped": "La profiładura dełe prestasion xé sta blocà dopo %s secondo/i e %s tick (%stick par secondo)", "commands.place.feature.failed": "No se ga podesto piassar l'elemento", "commands.place.feature.invalid": "No ghe xe elementi de tipo \"%s\"", "commands.place.feature.success": "Piassà \"%s\" in %s, %s, %s", "commands.place.jigsaw.failed": "Fal<PERSON>o zena<PERSON> de puzzle", "commands.place.jigsaw.invalid": "No ghe xe selesìon de modeli de tipo \"%s\"", "commands.place.jigsaw.success": "Zenerà puzzle a %s, %s, %s", "commands.place.structure.failed": "No se ga podesto piassar l'strutura", "commands.place.structure.invalid": "No ghe xe struture de tipo \"%s\"", "commands.place.structure.success": "Zenerà strutura \"%s\" a %s, %s, %s", "commands.place.template.failed": "No se ga podesto piassar el modelo", "commands.place.template.invalid": "No ghe xe modelo cò id \"%s\"", "commands.place.template.success": "Zenerà modelo \"%s\" a %s, %s, %s", "commands.playsound.failed": "El sono l'é masa distante par poder èsar sentìo", "commands.playsound.success.multiple": "El sono %s l'é stà reproduto a %s zugadori", "commands.playsound.success.single": "El sono %s l'é stà reproduto a %s", "commands.publish.alreadyPublished": "Ła partìa multizugador l'é za ospitada su ła porta %s", "commands.publish.failed": "Inposìbiłe ospitar ła partìa local", "commands.publish.started": "Ła partìa local l'é ospitada a ła porta %s", "commands.publish.success": "Ła partìa multizugador l'è ora ospidada su ła porta %s", "commands.random.error.range_too_large": "El intervallo del valore casuałe gà da esare al masimo 2147483646", "commands.random.error.range_too_small": "El intervallo del valore casuałe gà da esare almanco 2", "commands.random.reset.all.success": "%s sequense casuałi zé stà reimpostà", "commands.random.reset.success": "La sequensa casuałe %s zé stà reimpostà", "commands.random.roll": "%s gà estratto %s (fra %s e %s)", "commands.random.sample.success": "Vałore casuałe: %s", "commands.recipe.give.failed": "Njisuna nova reseta l'é stada inparada", "commands.recipe.give.success.multiple": "%s resetari i ze stadi dezblocadi par %s zugadori", "commands.recipe.give.success.single": "%s resetari i'é stadi dezblocadi par %s", "commands.recipe.take.failed": "<PERSON><PERSON><PERSON><PERSON> reseta ła pol è<PERSON>", "commands.recipe.take.success.multiple": "%s resetari i'é stadi tolti via da %s zugadori", "commands.recipe.take.success.single": "%s resetari i'é stadi tolti via da %s", "commands.reload.failure": "Inposì<PERSON><PERSON><PERSON> recargar, mantegn<PERSON> i dati veci", "commands.reload.success": "<PERSON>io cargar da novo!", "commands.ride.already_riding": "%s sta zà montando %s", "commands.ride.dismount.success": "%s l'è smontà da %s", "commands.ride.mount.failure.cant_ride_players": "I zugadori no pol esare cavalcà", "commands.ride.mount.failure.generic": "%s no pol montare so %s", "commands.ride.mount.failure.loop": "Un'entidà no po' montare so de eła o soi so pasejeri", "commands.ride.mount.failure.wrong_dimension": "Inposibiłe montare so na entidà in'te na dimension diferente", "commands.ride.mount.success": "%s l'è monta so %s", "commands.ride.not_riding": "%s non l'è so nisun veicolo", "commands.rotate.success": "Rodà %s", "commands.save.alreadyOff": "El salvatazo l'é stà dezativà", "commands.save.alreadyOn": "El salvatazo l'é stà ativà", "commands.save.disabled": "El salvatazo automàtego l'é stà abiłità", "commands.save.enabled": "El salvatazo automàtego l'é stà abiłità", "commands.save.failed": "<PERSON>pos<PERSON><PERSON><PERSON><PERSON> salvar (gheto spasio sufisiente su'l disco?)", "commands.save.saving": "<PERSON><PERSON> (podarìa vołerghe un po' de tenpo!)", "commands.save.success": "Partida salvada", "commands.schedule.cleared.failure": "Nisun programa co id %s", "commands.schedule.cleared.success": "Removesto %s programa/i co id %s", "commands.schedule.created.function": "Ła funsion \"%s\" programada in %s tick de'l tenpo de zugo %s", "commands.schedule.created.tag": "El tag \"%s\" programà in %s tick inte'l tenpo de zugo %s", "commands.schedule.macro": "Inposibiłe programàr na macro", "commands.schedule.same_tick": "Inposìbiłe programar par el tick atual", "commands.scoreboard.objectives.add.duplicate": "El eziste za un obietivo con 'sto nome", "commands.scoreboard.objectives.add.success": "El obietivo %s l'é stà creà", "commands.scoreboard.objectives.display.alreadyEmpty": "Njisun canbiamento: ła pozision l'é za vuda", "commands.scoreboard.objectives.display.alreadySet": "Njisun canbiamento: ła pozision l'é za drio vizuałizar 'sto obietivo", "commands.scoreboard.objectives.display.cleared": "Tuti i obietivi i'é stadi removesti da ła pozision %s", "commands.scoreboard.objectives.display.set": "Ła vizuałizasion de'l obietivo %2$s l'é stada inpostada inte ła pozision %1$s", "commands.scoreboard.objectives.list.empty": "No ghe ze obietivi", "commands.scoreboard.objectives.list.success": "Ghe ze %s obietivo/i: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disativà l'agiornamento visuale automatico par l'obietivo: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Ativà l'agiornamento visuale automatico par l'obietivo: %s", "commands.scoreboard.objectives.modify.displayname": "El nome de vizuałizaxion de %s l'é stà canbià in %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Canselà el formato numerico predefinìo del obietivo %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Canbià el formato numerico predefinìo del obietivo %s", "commands.scoreboard.objectives.modify.rendertype": "El tipo de rendering de'l obietivo %s l'é stà canbià", "commands.scoreboard.objectives.remove.success": "El obietivo %s l'é stà removesto", "commands.scoreboard.players.add.success.multiple": "%s ponti i'è stadi zontadi a'l obietivo %s par %s entidà", "commands.scoreboard.players.add.success.single": "%s ponti i'é stadi zontadi a'l obietivo %s par %s (deso %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Canselà el nome visualizà in %s par %s entità", "commands.scoreboard.players.display.name.clear.success.single": "Cansełà el nome visualizado in %s par %s", "commands.scoreboard.players.display.name.set.success.multiple": "Cambià el nome visualizà in %s par %s entità in %s", "commands.scoreboard.players.display.name.set.success.single": "Cambià el nome visualizà in %s par %s entità in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Scancelà el nùmaro de formà par %s entidà in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Scancelà el nùmaro de formà par %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Canbià el nùmaro de formà par %s entidà in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Canbià el nùmaro de formà par %s in %s", "commands.scoreboard.players.enable.failed": "Njisun canbiamento: el trigger l'é za ativà", "commands.scoreboard.players.enable.invalid": "\"enable\" el funsiona sol su obietivi trigger", "commands.scoreboard.players.enable.success.multiple": "El trigger %s l'é stà ativà par %s entidà", "commands.scoreboard.players.enable.success.single": "El trigger %s l'é stà ativà par %s", "commands.scoreboard.players.get.null": "Inposìbiłe recuparar el pontejo de'l obietivo %s par %s: no l'é stà inpostà", "commands.scoreboard.players.get.success": "El pontezo de'l obietivo %3$s par %1$s l'è %2$s", "commands.scoreboard.players.list.empty": "No ghe ze entidà rezistrade", "commands.scoreboard.players.list.entity.empty": "%s el ga mìa pontezi da vizuałizar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s el ga %s pontejo/i:", "commands.scoreboard.players.list.success": "Ghe xe %s entidà rezistrade: %s", "commands.scoreboard.players.operation.success.multiple": "El pontezo de'l obietivo %s par %s entidà l'è stà azornà", "commands.scoreboard.players.operation.success.single": "El pontezo de'l obietivo %s par %s entidà l'è stà inpostà a %s", "commands.scoreboard.players.remove.success.multiple": "%s ponti i'è stadi removesti da'l obietivo %s par %s entidà", "commands.scoreboard.players.remove.success.single": "%s ponti i'è stadi removesti da'l obietivo %s par %s (deso %s)", "commands.scoreboard.players.reset.all.multiple": "Tuti i pontezi par %s entidà i'é stadi rezetadi", "commands.scoreboard.players.reset.all.single": "Tuti i pontezi par %s i'é stadi rezetadi", "commands.scoreboard.players.reset.specific.multiple": "El pontezo de'l obietivo %s par %s entidà l'è stà rezetà", "commands.scoreboard.players.reset.specific.single": "El pontezo de'l obietivo %s par %s l'è stà rezetà", "commands.scoreboard.players.set.success.multiple": "El pontezo de'l obietivo %s par %s entidà l'é stà inpostà a %s", "commands.scoreboard.players.set.success.single": "El pontejo de'l obietivo %s par %s l'é stà inpostà a %s", "commands.seed.success": "Seme: %s", "commands.setblock.failed": "Inposìbiłe pozisionar el bloco", "commands.setblock.success": "El bloco in %s,%s,%s l'è sta canbià", "commands.setidletimeout.success": "El tenpo de inatività de'l zugador l'è stà inpostà a %s minuto(i)", "commands.setidletimeout.success.disabled": "El tenpo de inatività masimo dei zugadori l'é stà disativà", "commands.setworldspawn.failure.not_overworld": "Se pol inpostar el ponto de spawn sol par l'overworld", "commands.setworldspawn.success": "Inpostà el ponto de zenarasion global in %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Inposts el ponto de spawn in %s, %s, %s [%s] in %s par %s zugadori", "commands.spawnpoint.success.single": "Inpostà el ponto de spawn in %s, %s, %s [%s] in %s par %s", "commands.spectate.not_spectator": "%s no l'é mìa in modałità spetador", "commands.spectate.self": "A te pol mìa oservarte ti steso", "commands.spectate.success.started": "Drio oservar %s", "commands.spectate.success.stopped": "A te si pì drio oservar na entidà", "commands.spreadplayers.failed.entities": "Inposìbiłe destribuir %s entidà intorno a %s, %s (masa entidà par l'area: prova destribuir a'l màsimo %s entidà)", "commands.spreadplayers.failed.invalid.height": "maxHeight%s mìa vàlido; xe prevista piassè alta del mìnimo del mondo%s", "commands.spreadplayers.failed.teams": "Inposìbiłe destribuir %s scuadre intorno a %s, %s (masa entidà par l'area: prova destribuir a'l màsimo %s scuadre)", "commands.spreadplayers.success.entities": "Destribuìo %s entidà intorno a %s, %s co' na distansa media de %s blochi", "commands.spreadplayers.success.teams": "%s scuadre i'é stade destribuide intorno a%s, %s co' na distansa media de %s blochi", "commands.stop.stopping": "Drio fermar el server", "commands.stopsound.success.source.any": "Fermadi tuti i soni \"%s\"", "commands.stopsound.success.source.sound": "El sono %s con fonte %s l'é stà intaroto", "commands.stopsound.success.sourceless.any": "Tuti i soni i'é stadi fermadi", "commands.stopsound.success.sourceless.sound": "El sono %s l'é stà fermà", "commands.summon.failed": "Inposìbiłe evocar l'entidà", "commands.summon.failed.uuid": "Inposìbile evogar l'entidà par cauza de la duplegasion d'i UUID", "commands.summon.invalidPosition": "Pozision mìa vàłida par ła evogasion", "commands.summon.success": "L'entidà %s l'é stada evocada", "commands.tag.add.failed": "El destinatario el ga za el tag o el ghe n'ha masa", "commands.tag.add.success.multiple": "El tag '%s' l'è stà zontà a %s entidà", "commands.tag.add.success.single": "El tag '%s' l'è stà zontà a %s", "commands.tag.list.multiple.empty": "Łe %s entità no łe ga tag", "commands.tag.list.multiple.success": "Łe %s entità łe ga in total %s tag: %s", "commands.tag.list.single.empty": "%s no'l ga tag", "commands.tag.list.single.success": "%s el ga %s tag: %s", "commands.tag.remove.failed": "El destinatario el posiede mìa 'sto tag", "commands.tag.remove.success.multiple": "El tag '%s' l'è stà cavà a %s entidà", "commands.tag.remove.success.single": "El tag '%s' l'è stà cavà a %s", "commands.team.add.duplicate": "Ła eziste za na scuadra con 'sto nome", "commands.team.add.success": "Ła scuadra %s l'é stada creada", "commands.team.empty.success": "%s menbri i'é stadi cavadi da ła scuadra %s", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON> canbia<PERSON>: ła scuadra l'è za vuda", "commands.team.join.success.multiple": "%s menbri i'é stadi zontadi a ła scuadra %s", "commands.team.join.success.single": "%s l'é stà zontà a ła scuadra %s", "commands.team.leave.success.multiple": "%s menbri i'é stadi removesti da łe so scuadre", "commands.team.leave.success.single": "%s l'é stà removesto da ła scuadra", "commands.team.list.members.empty": "Ła scuadra %s no ła ga menbri", "commands.team.list.members.success": "La scuadra %s la ga %s menbri: %s", "commands.team.list.teams.empty": "No ghe ze scuadre", "commands.team.list.teams.success": "Ghe xe %s scuadre: %s", "commands.team.option.collisionRule.success": "Ła règoła de cołizion par ła scuadra %s l'é stada inpostada a \"%s\"", "commands.team.option.collisionRule.unchanged": "Njisun canbiamento: ła règoła de cołizion l'è za inpostada su 'sto vałor", "commands.team.option.color.success": "El cołor de ła scuadra %s l'é stà canbià in %s", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON> can<PERSON>: l'è za el cołor de ła scuadra", "commands.team.option.deathMessageVisibility.success": "Ła vizibiłità d'i mesazi de morte par ła scuadra %s l'é stada inpostada a \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Njisun canbiamento: ła vizibiłità d'i mesazi de morte l'è za inpostada su 'sto vałor", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON> can<PERSON>: el fogo amigo l'è za dezativà par sta scuadra", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON><PERSON> can<PERSON>: el fogo amigo l'è za ativà par sta scuadra", "commands.team.option.friendlyfire.disabled": "El fogo amigo par ła scuadra %s l'é stà dezativà", "commands.team.option.friendlyfire.enabled": "El fogo amigo par ła scuadra %s l'é stà ativà", "commands.team.option.name.success": "El nome de ła scuadra %s l'é stà azornà", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON> can<PERSON>: l'é za el nome de ła scuadra", "commands.team.option.nametagVisibility.success": "Ła vizibiłità d'i nomi d'i zugadori par ła scuadra %s l'é stada inpostada a \"%s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON>jisun canbiamento: ła vizibiłità d'i nomi d'i zugadori l'è za inpostada su 'sto vałor", "commands.team.option.prefix.success": "El prefiso de ła scuadra l'é stà canbià in %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Njisun canbiamento: i menbri de ła scuadra za i pol mìa védar i conpanji invizìbiłi", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Njisun canbiamento: i menbri de ła scuadra i pol za védar i conpanji invizìbiłi", "commands.team.option.seeFriendlyInvisibles.disabled": "I menbri de ła scuadra %s no i pol védar pì i conpanji invizìbiłi", "commands.team.option.seeFriendlyInvisibles.enabled": "I menbri de ła scudra %s i pol védar i conpanji invizìbiłi", "commands.team.option.suffix.success": "El sufiso de ła scuadra l'é stà canbià in %s", "commands.team.remove.success": "Ła scuadra %s l'é stada removesta", "commands.teammsg.failed.noteam": "Te ghè da èsar inte na scuadra par mesazar co ła to scuadra", "commands.teleport.invalidPosition": "Posixion mìa vàłida par el tełetrasporto", "commands.teleport.success.entity.multiple": "%s entidà i'è stade tełetrasportade arente %s", "commands.teleport.success.entity.single": "%s l'è stà tełetrasportà arente %s", "commands.teleport.success.location.multiple": "%s entidà i'è stade tełetrasportade arente %s,%s,%s", "commands.teleport.success.location.single": "%s l'è stà tełetrasportà arente %s,%s,%s", "commands.test.batch.starting": "Drio inviar el anbiente %s del loto %s", "commands.test.clear.error.no_tests": "Gnissun test da netar xe stà catà", "commands.test.clear.success": "Netà %s struture", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Fa' clic par copiar in aponti", "commands.test.create.success": "Creà inpostasion de test par el test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Gnissuna istansa de test catada", "commands.test.error.non_existant_test": "El test %s no'l xe stà catà", "commands.test.error.structure_not_found": "La strutura test %s no la xe stada catada", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "La dimension de la strutura la ga da èssar manco de %s blochi longo i assi", "commands.test.locate.done": "Localizzazion termenada, catade %s struture ", "commands.test.locate.found": "Catà strutura a: %s (distansa: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "<PERSON><PERSON><PERSON> test da inviar", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Gnissun test da resetar xe stà catà", "commands.test.reset.success": "Reseta %s struture", "commands.test.run.no_tests": "Gnissun test catà", "commands.test.run.running": "Drio inviar %s test", "commands.test.summary": "Test de zugo conpletà! %s i xe stadi inviadi", "commands.test.summary.all_required_passed": "Tuti i test dimandadi i xe stadi passadi :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Persentili: P50: %s ms, P95: %s ms, P99: %s ms; campiòn: %s", "commands.tick.query.rate.running": "Frequensa de tick indicà: %s al secondo.\nTenpo medio par tick: %s ms (indicà: %s ms)", "commands.tick.query.rate.sprinting": "Frequensa de tick indicà: %s al secondo (ignorà, solo par riferimento).\nTenpo medio par tick: %s ms", "commands.tick.rate.success": "Frequensa indicà impostà a %s tick al secondo", "commands.tick.sprint.report": "Acelerasion conpletà co' %s tick al secondo, o %s ms par tick", "commands.tick.sprint.stop.fail": "<PERSON><PERSON><PERSON> acelerasiòn de tick in corso", "commands.tick.sprint.stop.success": "Acelerasiòn de tick fermà", "commands.tick.status.frozen": "El zugo l'é stà blocà", "commands.tick.status.lagging": "El zugo l'è in esecusiòn, ma no'ł riese a tegner el paso con la frequensa de tick indicà", "commands.tick.status.running": "El zugo el va normalmente", "commands.tick.status.sprinting": "El zugo el va ràpido", "commands.tick.step.fail": "Inposibiłe fare ł'avansamento - el zugo gà da esare prima fermà", "commands.tick.step.stop.fail": "<PERSON><PERSON> a<PERSON> de tick in corso", "commands.tick.step.stop.success": "Avansamento de tick fermà", "commands.tick.step.success": "Avansamento de %s tick", "commands.time.query": "El tempo l'è %s", "commands.time.set": "El tenpo l'é stà inpostà a %s tick", "commands.title.cleared.multiple": "I tìtołi par %s zugadori i'è stadi ełimenadi", "commands.title.cleared.single": "I tìtołi par %s i'è stadi ełimenadi", "commands.title.reset.multiple": "Łe opsion de i tìtołi par %s zugadori i'è stade repristinade", "commands.title.reset.single": "Łe opsion de i tìtołi par %s i'è stade repristinade", "commands.title.show.actionbar.multiple": "Drio mostrar un novo tìtoło in actionbar par %s zugadori", "commands.title.show.actionbar.single": "Drio mostrar un novo tìtoło in actionbar par %s", "commands.title.show.subtitle.multiple": "Drio mostrar un novo sototìtoło par %s zugadori", "commands.title.show.subtitle.single": "Drio mostrar un novo sototìtoło par %s", "commands.title.show.title.multiple": "Drio mostrar un novo tìtoło par %s zugadori", "commands.title.show.title.single": "Drio mostrar un novo tìtoło par %s", "commands.title.times.multiple": "I tenpi de vizuałizasion d'i tìtołi par %s zugadori i'é stadi canbiadi", "commands.title.times.single": "I tenpi de vizuałizasion d'i tìtołi par %s i'é stadi canbiadi", "commands.transfer.error.no_players": "Va specificà almanco on zugadore da trasferire", "commands.transfer.success.multiple": "Trasferimento de %s zugadori a %s:%s", "commands.transfer.success.single": "Trasferimento de %s a %s:%s", "commands.trigger.add.success": "%s l'è stà ativà (zontà de %s)", "commands.trigger.failed.invalid": "Te pol sol ativar obietivi de tipo \"trigger\"", "commands.trigger.failed.unprimed": "Te pol njancora ativar 'sto obietivo", "commands.trigger.set.success": "%s l'è stà ativà (vałor inpostà a %s)", "commands.trigger.simple.success": "%s l'è stà ativà", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "El tenpo atmosfèrego l'é stà canbià in seren", "commands.weather.set.rain": "El tenpo atmosfèrego l'é stà canbià in piova", "commands.weather.set.thunder": "El tenpo atmosfèrego l'é stà canbià in piova e troni", "commands.whitelist.add.failed": "El zugador l'è za inte ła whitelist", "commands.whitelist.add.success": "%s l'è sta zontà a ła whitelist", "commands.whitelist.alreadyOff": "Ła whitelist l'è za dezativada", "commands.whitelist.alreadyOn": "Ła whitelist l'è za ativada", "commands.whitelist.disabled": "Ła whitelist l'é stada dezativada", "commands.whitelist.enabled": "Ła whitelist l'é stada ativada", "commands.whitelist.list": "%s zugadore/i inte ła whitelist: %s", "commands.whitelist.none": "No ghe ze zugadori inte ła whitelist", "commands.whitelist.reloaded": "Ła whitelist l'è stada recargada", "commands.whitelist.remove.failed": "El zugador el ze njancora in whitelist", "commands.whitelist.remove.success": "%s l'è sta removesto da ła whitelist", "commands.worldborder.center.failed": "Njisun canbiamento: el confin de'l mondo l'é za sentrà chive", "commands.worldborder.center.success": "El centro de'l confin de'l mondo l'è stà inpostà in %s,%s", "commands.worldborder.damage.amount.failed": "Njisun canbiamento: el confin de'l mondo el càuza za 'sta cuantità de dani", "commands.worldborder.damage.amount.success": "Inpostà la cuantità de dani fora dal confin del mondo a %s par bloco ogni secondo", "commands.worldborder.damage.buffer.failed": "Njisun canbiamento: ła zona segura fora da'l confin de'l mondo l'é za inpostada a 'sta distansa", "commands.worldborder.damage.buffer.success": "Ła zona segura fora da'l confin de'l mondo l'é stada inpostada a %s bloco/i", "commands.worldborder.get": "El confin de'l mondo l'è atualmente grando %s bloco/i", "commands.worldborder.set.failed.big": "El confin del mondo no pode esare pi granda de %s blochi", "commands.worldborder.set.failed.far": "El confin del mondo no pode èsar più granda de %s blochi", "commands.worldborder.set.failed.nochange": "Njisun canbiamento: el confin de'l mondo el ga za 'sta łarghesa", "commands.worldborder.set.failed.small": "El confin de'l mondo no'l pol èsar minor de 1 bloco", "commands.worldborder.set.grow": "El confin de'l mondo l'é drio espàndarse par deventar grando %s blochi in %s secondi", "commands.worldborder.set.immediate": "El confin de'l mondo l'é stà inpostà a %s bloco/i de łarghesa", "commands.worldborder.set.shrink": "El confin de'l mondo l'é drio strénzarse par deventar grando %s blochi in %s secondo/i", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON>sun canbia<PERSON>: ła distansa de avertimento de'l confin de'l mondo l'é za inpostada a 'sto vałor", "commands.worldborder.warning.distance.success": "Ła distansa de avertimento de'l confin de'l mondo l'é stada inpostada a %s bloco/i", "commands.worldborder.warning.time.failed": "Njisun canbiamento: el tenpo de avertimento de'l confin de'l mondo l'é za inpostà su 'sto vałor", "commands.worldborder.warning.time.success": "El tenpo de avertimento de'l confin de'l mondo l'é stà inpostà a %s secondo/i", "compliance.playtime.greaterThan24Hours": "Te si drio zugar da piassè de 24 ore", "compliance.playtime.hours": "Te si drio zugar da %s ora/e", "compliance.playtime.message": "Zugar par massa tenpo podarìa interferir co' la vita cuotidiana normal", "connect.aborted": "Oparasion intarota", "connect.authorizing": "<PERSON><PERSON><PERSON>...", "connect.connecting": "<PERSON><PERSON><PERSON> con<PERSON> al server...", "connect.encrypting": "Drio criptar...", "connect.failed": "Falìa conesion a'l server", "connect.failed.transfer": "Conesiòn non riusìa durante el trasferimento al server", "connect.joining": "Drio intrar inte'l mondo...", "connect.negotiating": "<PERSON><PERSON>...", "connect.reconfiging": "<PERSON><PERSON> re-configurar...", "connect.reconfiguring": "<PERSON><PERSON> re-configurar...", "connect.transferring": "Trasferimento a on novo server...", "container.barrel": "<PERSON><PERSON><PERSON>", "container.beacon": "Faro", "container.beehive.bees": "Ave: %s / %s", "container.beehive.honey": "Miele: %s / %s", "container.blast_furnace": "Gran fornaze", "container.brewing": "Łanbico", "container.cartography_table": "Banco de'l cartògrafo", "container.chest": "Baùl", "container.chestDouble": "Baùl grando", "container.crafter": "Craftador", "container.crafting": "Fabricasion", "container.creative": "Sełesion ozeti", "container.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "container.dropper": "Dropador", "container.enchant": "Incanta", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapislàsari", "container.enchant.lapis.one": "1 Lapislàsaro", "container.enchant.level.many": "%s łivełi de espariensa", "container.enchant.level.one": "1 łivel de espariensa", "container.enchant.level.requirement": "Livèi dimandài: %s", "container.enderchest": "<PERSON><PERSON><PERSON>", "container.furnace": "Fornaze", "container.grindstone_title": "Justa e descanta", "container.hopper": "T<PERSON>oz<PERSON>", "container.inventory": "Inventario", "container.isLocked": "%s l'è blocà!", "container.lectern": "<PERSON><PERSON>", "container.loom": "<PERSON><PERSON><PERSON>", "container.repair": "Repara e nòmina", "container.repair.cost": "Costo de incantamento: %1$s", "container.repair.expensive": "Masa caro!", "container.shulkerBox": "<PERSON><PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s × %s", "container.shulkerBox.more": "e antri %s...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Fumegador", "container.spectatorCantOpen": "Inposìbiłe vèrzar: el contenjùo l'é stà njancora inzenarà.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "Mejora un trapeło", "container.upgrade.error_tooltip": "L'ogeto no pode esare mejorà in sto modo", "container.upgrade.missing_template_tooltip": "Zonta on schema de forgiatura", "controls.keybinds": "Combinasion de tasti...", "controls.keybinds.duplicateKeybinds": "Sto tasto vien doprà anca par: \n%s", "controls.keybinds.title": "Combinasion de tasti", "controls.reset": "<PERSON><PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON><PERSON> tuto", "controls.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Parpiazer sełesiona un bioma", "createWorld.customize.buffet.title": "Parsonałizasion de'l mondo bufè", "createWorld.customize.flat.height": "Altesa", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Fondo -%s", "createWorld.customize.flat.layer.top": "Sima -%s", "createWorld.customize.flat.removeLayer": "Cava el strato", "createWorld.customize.flat.tile": "Material de'l strato", "createWorld.customize.flat.title": "Parsonałizasion de'l mondo piato", "createWorld.customize.presets": "<PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> calcheduni che gavemo creà par tenpo!", "createWorld.customize.presets.select": "Dòpara el model", "createWorld.customize.presets.share": "Voto condivìdar el to model co' cualched<PERSON>? Dòpara el recuadro chi soto!", "createWorld.customize.presets.title": "Sełesiona un model", "createWorld.preparing": "Drio pareciar la creaçion del mondo...", "createWorld.tab.game.title": "Zugo", "createWorld.tab.more.title": "De p<PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON>", "credits_and_attribution.button.attribution": "Atribusion", "credits_and_attribution.button.credits": "Reconossimenti", "credits_and_attribution.button.licenses": "License", "credits_and_attribution.screen.title": "Reconossimenti e atribusion", "dataPack.bundle.description": "Ativa el sacheto come ogeto sperimentałe", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Movimento dei caretełi mejorà", "dataPack.minecart_improvements.name": "Mejoramenti dei caretełi", "dataPack.redstone_experiments.description": "Modifeghe sperimentałi a ła redstone", "dataPack.redstone_experiments.name": "Redstone sperimentałe", "dataPack.title": "Sełexiona i pacheti de dati", "dataPack.trade_rebalance.description": "Scanbi coi paesani ajornadi", "dataPack.trade_rebalance.name": "Re-balansamento dei scanbi coi paesani", "dataPack.update_1_20.description": "Novi contenudi e funsionalità par Minecraft 1.20", "dataPack.update_1_20.name": "Ajornamento 1.20", "dataPack.update_1_21.description": "Novi contenudi e funsionalità par Minecraft 1.21", "dataPack.update_1_21.name": "Ajornamento 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Inposìbiłe convałidar i pacheti de dati!", "dataPack.validation.reset": "Reseta al Default", "dataPack.validation.working": "Drio vałidar i pacheti de dati sełexionadi...", "dataPack.vanilla.description": "I dati predefenìi par <PERSON>craft", "dataPack.vanilla.name": "Predefenìo", "dataPack.winter_drop.description": "Novi contenudi e funsionalità pa'ł rilascio invernałe", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON><PERSON>ł<PERSON>", "datapackFailure.safeMode": "Modalità secura", "datapackFailure.safeMode.failed.description": "'Sto mondo el contien dati de salvatajo mìa vàlidi o coruti.", "datapackFailure.safeMode.failed.title": "Inposibiłe cargare el mondo in modałità provisoria.", "datapackFailure.title": "<PERSON><PERSON><PERSON> in pacheti de dati selesionadi i gà inpendìo de cargar el mondo. \nTe pol provar a cargarlo doparando soło pacheti de dati vanilla (\"mod. secura\") o ndar de volta al menù prinçipal e giustar el problema manualmente.", "death.attack.anvil": "%1$s l'è stà schisà da n'ancùzene", "death.attack.anvil.player": "%1$s el ze stà schisà da n'ancùzene conbatendo co' %2$s", "death.attack.arrow": "%2$s el ga zbarà a %1$s", "death.attack.arrow.item": "%2$s armà de %3$s el ga zbarà a %1$s", "death.attack.badRespawnPoint.link": "Na sielta de design de'l zugo", "death.attack.badRespawnPoint.message": "%1$s l'é stà copà da %2$s", "death.attack.cactus": "%1$s l'è morto spuncià", "death.attack.cactus.player": "%1$s l'è finìo incosto un cactus scanpando da %2$s", "death.attack.cramming": "%1$s l'é stà schisà masa", "death.attack.cramming.player": "%1$s l'à subìo masa presion a càuza de %2$s", "death.attack.dragonBreath": "%1$s el s'à rostìo inte el supio de dragon", "death.attack.dragonBreath.player": "%1$s el s'à rostìo inte el supio de dragon par %2$s", "death.attack.drown": "%1$s el s'à negà", "death.attack.drown.player": "%1$s el s'à negà scanpando da %2$s", "death.attack.dryout": "%1$s l'é morto par desidratasion", "death.attack.dryout.player": "%1$s l'é morto par desidratasion mentre el scanpava da %2$s", "death.attack.even_more_magic": "%1$s l'é stà copà da piasè magìa ancora", "death.attack.explosion": "%1$s l'è saltà par ària", "death.attack.explosion.player": "%1$s l'è stà fato saltar par ària da %2$s", "death.attack.explosion.player.item": "%1$s l'é stà fato saltar par aria da %2$s armà de %3$s", "death.attack.fall": "%1$s l'à tocà tera masa forte", "death.attack.fall.player": "%1$s l'à toca tera masa forte scanpando da %2$s", "death.attack.fallingBlock": "%1$s l'è stà schisà da 'n bloco", "death.attack.fallingBlock.player": "%1$s el ze stà schisà da un bloco conbatendo co' %2$s", "death.attack.fallingStalactite": "%1$s xé sta inpirà da un pirołoto", "death.attack.fallingStalactite.player": "%1$s xé sta inpirà da un pirołoto mentre che'l jera drio conbatare con %2$s", "death.attack.fireball": "%1$s l'è stà ciapà a bałe de fogo da %2$s", "death.attack.fireball.item": "%1$s l'è stà ciapà a bałe de fogo da %2$s armà de %3$s", "death.attack.fireworks": "%1$s l'é ndà via co'l boto", "death.attack.fireworks.item": "%1$s l'é saltà pararia par colpa de un boto tirà co' %3$s da %2$s", "death.attack.fireworks.player": "%1$s l'é ndà via co'l boto conbatendo co' %2$s", "death.attack.flyIntoWall": "%1$s el ga sparimentà ła forsa sinètega", "death.attack.flyIntoWall.player": "%1$s no'l ga avùo timor de ła forsa sinètega scanpando da %2$s", "death.attack.freeze": "%1$s l'è morto giasà", "death.attack.freeze.player": "%1$s l'è morto giasà par colpa de %2$s", "death.attack.generic": "%1$s l'è morto", "death.attack.generic.player": "%1$s l'é morto par cauza de %2$s", "death.attack.genericKill": "%1$s l'è stà copà", "death.attack.genericKill.player": "%1$s l'è stà copà barufando co %2$s", "death.attack.hotFloor": "%1$s el s'à incorto che el pavimento l'era łava", "death.attack.hotFloor.player": "%1$s l'è fenìo inte na zona pericołoza a cauza de %2$s", "death.attack.inFire": "%1$s l'é ndà a fogo", "death.attack.inFire.player": "%1$s l'è finìo inte'l fogo conbatendo co' %2$s", "death.attack.inWall": "%1$s el se ga stofegà int'un muro", "death.attack.inWall.player": "%1$s el s'à stofegà int'un muro conbatendo co' %2$s", "death.attack.indirectMagic": "%1$s l'è stà copà da %2$s co' ła magìa", "death.attack.indirectMagic.item": "%1$s l'è stà copà da %2$s armà de %3$s", "death.attack.lava": "%1$s l'à provà nodar inte ła łava", "death.attack.lava.player": "%1$s el ga provà a nodar inte ła łava scanpando da %2$s", "death.attack.lightningBolt": "%1$s l'è stà colpìo da una sita", "death.attack.lightningBolt.player": "%1$s el xe stà colpìo da un sciantizo conbatendo co %2$s", "death.attack.mace_smash": "%1$s l'è stà mostà da %2$s", "death.attack.mace_smash.item": "%1$s el ze stà mostà da %2$s armà de %3$s", "death.attack.magic": "%1$s l'è stà copà da ła magìa", "death.attack.magic.player": "%1$s l'é stà copà da ła magìa scanpando da %2$s", "death.attack.message_too_long": "El mesazo atual l'era masa łongo par èsar invià. Ne despiaze, ècote na varsion pì curta: %s", "death.attack.mob": "%1$s l'è stà copà da %2$s", "death.attack.mob.item": "%1$s l'è stà copà da %2$s armà de %3$s", "death.attack.onFire": "%1$s l'è morto bruzà", "death.attack.onFire.item": "%1$s l'è stà carbonizà conbatendo co' %2$s dòparando %3$s", "death.attack.onFire.player": "%1$s l'è stà carbonizà conbatendo co' %2$s", "death.attack.outOfWorld": "%1$s l'è cascà fora da'l mondo", "death.attack.outOfWorld.player": "%1$s no'l voleva vìvar pì inte'l steso mondo de %2$s", "death.attack.outsideBorder": "%1$s gà oltrepasà i confini de sto mondo", "death.attack.outsideBorder.player": "%1$s gà oltrepasà i confini de sto mondo barufando co %2$s", "death.attack.player": "%1$s l'è stà copà da %2$s", "death.attack.player.item": "%1$s l'è stà copà da %2$s armà de %3$s", "death.attack.sonic_boom": "%1$s se morto da n'urlo sonico", "death.attack.sonic_boom.item": "%1$s se morto da n'urlo sonico mentre provava scanpando da %2$s cò %3$s", "death.attack.sonic_boom.player": "%1$s se morto da n'urlo sonico mentre provava scanpando da %2$s", "death.attack.stalagmite": "Un pirołoto gà inpirà %1$s", "death.attack.stalagmite.player": "%1$s xé sta inpirà da un pirołoto mentre che'l jera drio conbatare con %2$s", "death.attack.starve": "%1$s l'è morto de fame", "death.attack.starve.player": "%1$s l'é morto de fame conbatendo co' %2$s", "death.attack.sting": "%1$s el ze stà ponto a morte", "death.attack.sting.item": "%1$s el ze stà ponto a morte da%2$s dòparando %3$s", "death.attack.sting.player": "%1$s el ze stà ponto a morte da%2$s", "death.attack.sweetBerryBush": "%1$s l'é stà ponzetà a morte da un giavasco de pomełe dolse", "death.attack.sweetBerryBush.player": "%1$s l'é stà ponzetà a morte da un giavasco de pomełe dolse scanpando da %2$s", "death.attack.thorns": "%1$s l'è morto sercando de ferir %2$s", "death.attack.thorns.item": "%1$s el ze stà copà da %3$s sercando de ferir %2$s", "death.attack.thrown": "%1$s l'è stà bersajà da %2$s", "death.attack.thrown.item": "%1$s l'è stà bersajà da %2$s armà de %3$s", "death.attack.trident": "%1$s el ze stà inpałà da %2$s", "death.attack.trident.item": "%1$s el ze stà inpałà da %2$s armà de %3$s", "death.attack.wither": "%1$s el s'à infiapìo", "death.attack.wither.player": "%1$s el s'à infiapì conbatendo co' %2$s", "death.attack.witherSkull": "%1$s xe stà colpìo co' na testa dal %2$s", "death.attack.witherSkull.item": "%1$s xe stà colpìo co' na testa dal %2$s dòparando %3$s", "death.fell.accident.generic": "%1$s l'è presipità", "death.fell.accident.ladder": "%1$s l'è cascà zo da ła scała", "death.fell.accident.other_climbable": "%1$s l'é cascà drio ranpegàndose", "death.fell.accident.scaffolding": "%1$s el xe cascà da n'inpalcadura", "death.fell.accident.twisting_vines": "%1$s l'è cascà zo da d'i ranpeghìni intorcolài", "death.fell.accident.vines": "%1$s l'è cascà zo dai ranpeganti", "death.fell.accident.weeping_vines": "%1$s l'è cascà zo da dai ranpeganti pianzenti", "death.fell.assist": "%1$s l'è stà fato cascar da %2$s", "death.fell.assist.item": "%1$s l'è stà fato cascar da %2$s armà de %3$s", "death.fell.finish": "%1$s l'è cascà da masa distante ma %2$s el l'à fenìo", "death.fell.finish.item": "%1$s l'è cascà da masa distante ma %2$s el l'à fenìo armà de %3$s", "death.fell.killer": "%1$s l'è stà fato cascar", "deathScreen.quit.confirm": "Sito seguro de vołer bandonar ła partìa?", "deathScreen.respawn": "Spawna da novo", "deathScreen.score": "Pontezo", "deathScreen.score.value": "Puntejo: %s", "deathScreen.spectate": "Varda el mondo da spetador", "deathScreen.title": "Te si morto!", "deathScreen.title.hardcore": "Game Over!", "deathScreen.titleScreen": "<PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H: tooltip a<PERSON><PERSON>i", "debug.advanced_tooltips.off": "<PERSON><PERSON><PERSON>: sconti", "debug.advanced_tooltips.on": "Toolt<PERSON> a<PERSON>: in mostra", "debug.chunk_boundaries.help": "F3 + G: mostra i confini d'i chunk", "debug.chunk_boundaries.off": "Confini d'i chunk: sconti", "debug.chunk_boundaries.on": "Confini d'i chunk: in mostra", "debug.clear_chat.help": "F3 + D: neta ła chat", "debug.copy_location.help": "F3 + C: copia ła pozision cofà comando /tp; ten strucà F3 + C par zmorsar el zugo", "debug.copy_location.message": "Pozision copiada inte i apunti", "debug.crash.message": "Te si drio tènjar strucà F3 + C. Se no te łi rełasi, el zugo el crasharà.", "debug.crash.warning": "Drio crashar tra %s...", "debug.creative_spectator.error": "Inposibiłe cambiar modałità de zugo; no ti gà i permesi", "debug.creative_spectator.help": "F3 + N = Alterna mod. de zogo presedente <-> spetador", "debug.dump_dynamic_textures": "<PERSON><PERSON><PERSON>ghe salvà in %s", "debug.dump_dynamic_textures.help": "F3 + S = Esporta le gràfeghe dinàmighe", "debug.gamemodes.error": "Inposibiłe verxare el zugo col canbio modałità; no ti gà i parmesi", "debug.gamemodes.help": "F3 + F4: <PERSON><PERSON><PERSON> menù par canbio modałità de zugo", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Drio", "debug.help.help": "F3 + Q: <PERSON><PERSON> 'sta <PERSON><PERSON>", "debug.help.message": "Conbinasion de tasti:", "debug.inspect.client.block": "<PERSON><PERSON> b<PERSON> (banda client) copiadi inte i apunti", "debug.inspect.client.entity": "<PERSON><PERSON> en<PERSON> (banda client) copiadi inte i apunti", "debug.inspect.help": "F3 + I = Copia int'i apunti i dati de bloco o entidà", "debug.inspect.server.block": "<PERSON><PERSON> b<PERSON> (banda server) copiadi inte i apunti", "debug.inspect.server.entity": "<PERSON><PERSON> (banda server) copiadi inte i apunti", "debug.pause.help": "F3 + Esc: pauza sensa el menù de pauza (se posìbiłe)", "debug.pause_focus.help": "F3 + P: meti in pauza a'l canbiar de fenestra", "debug.pause_focus.off": "Pauza a ła pèrdita de'l stato ativo: dezativada", "debug.pause_focus.on": "Pauza a ła pèrdita de'l stato ativo: ativada", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = ìnvia /ferma la profiladura", "debug.profiling.start": "La profiladura de %s segondi inviada. Struca F3 + L par blocarla prima", "debug.profiling.stop": "Profiladura termenada. Resultài salvadi in %s", "debug.reload_chunks.help": "F3 + A: carga da novo i chunk", "debug.reload_chunks.message": "Drio cargar da novo i chunk", "debug.reload_resourcepacks.help": "F3 + T: carga da novo i pacheti de resorse", "debug.reload_resourcepacks.message": "Pacheti de resorse cargadi da novo", "debug.show_hitboxes.help": "F3 + B: mostra i hitbox", "debug.show_hitboxes.off": "Hitbox: sconti", "debug.show_hitboxes.on": "Hitbox: in mostra", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "'Sta demo ła durarà sincue dì de'l zugo. Fa' de'l to mejo!", "demo.day.2": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.3": "Terso dì", "demo.day.4": "Cuarto dì", "demo.day.5": "L'é el to ùltemo dì!", "demo.day.6": "T'è pasà el cuinto dì. Struca %s par salvar na schermada de łe to creasion.", "demo.day.warning": "El to tenpo l'é domenti finìo!", "demo.demoExpired": "El tenpo de ła demo l'è finìo!", "demo.help.buy": "Cronpa deso!", "demo.help.fullWrapped": "Cuesta demo ła durarà sincue dì de'l zugo (scuazi 1ora e 40 menuti de tenpo real). Da' na ociada a i progresi par aver d'i suzerimenti! Gòdete!", "demo.help.inventory": "Struca %1$s par vèrzar el inventario", "demo.help.jump": "Struca %1$s par saltar", "demo.help.later": "<PERSON><PERSON><PERSON> zoga<PERSON>!", "demo.help.movement": "Dòpara i botoni %1$s, %2$s, %3$s, %4$s e'l mouse par móvarte", "demo.help.movementMouse": "Vàrdate intorno doparando el mouse", "demo.help.movementShort": "Struca %1$s, %2$s, %3$s e %4$s par móvarte", "demo.help.title": "Modałità demo de Minecraft", "demo.remainingTime": "<PERSON>po che resta: %s", "demo.reminder": "Ła demo l'è termenà. Cronpa el zugo par continuar o crèa un novo mondo!", "difficulty.lock.question": "Sito seguro de vołer blocar ła dificoltà de'l mondo? Ła restarà blocada par senpre a %1$s e no te podarè canbiarla altro.", "difficulty.lock.title": "Bloca ła dificoltà de'l mondo", "disconnect.endOfStream": "<PERSON> de'l fluso de dati", "disconnect.exceeded_packet_rate": "Butà fora par ver spassà el numaro maximo de pachèti", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Richiesta de stato ignorà", "disconnect.loginFailedInfo": "Aceso fałìo: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El multizugador l'é desabilità. Par piaser controla le inpostasion del to account Microsoft.", "disconnect.loginFailedInfo.invalidSession": "Sesion mia vałida (prova a reinviar el zugo e el launcher)", "disconnect.loginFailedInfo.serversUnavailable": "I server de autentegasion no i'é atualmente disponibiłi. Prova piasè tardi.", "disconnect.loginFailedInfo.userBanned": "Te si banà da zugare online", "disconnect.lost": "Conesion persa", "disconnect.packetError": "Eror del protocol de rede", "disconnect.spam": "Mandà fora par spam", "disconnect.timeout": "Conesion fora tenpo", "disconnect.transfer": "Trasferìo a n'antro server", "disconnect.unknownHost": "Host m<PERSON>a <PERSON>", "download.pack.failed": "Inposibiłe scargare %s pacheto(i) so %s", "download.pack.progress.bytes": "Vansamento: %s (mesura total mì conossesto)", "download.pack.progress.percent": "Vansamento: %s%%", "download.pack.title": "Drio descargar el pacheto de resorse %s/%s", "editGamerule.default": "Predefenìo: %s", "editGamerule.title": "Modìfega regole de zugo", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Asorbimento", "effect.minecraft.bad_omen": "Małedision", "effect.minecraft.blindness": "Vista reduta", "effect.minecraft.conduit_power": "Poder de'l conduto", "effect.minecraft.darkness": "Sc<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON> dolf<PERSON>", "effect.minecraft.fire_resistance": "Rez. a'l fogo", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Presia", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON> mejorada", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON> viła<PERSON>", "effect.minecraft.hunger": "Fame", "effect.minecraft.infested": "Infestasiòn", "effect.minecraft.instant_damage": "<PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON> is<PERSON>", "effect.minecraft.invisibility": "Invizibiłità", "effect.minecraft.jump_boost": "Salto potensià", "effect.minecraft.levitation": "Łevitasion", "effect.minecraft.luck": "Fortuna", "effect.minecraft.mining_fatigue": "Fadi<PERSON>", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Vista noturna", "effect.minecraft.oozing": "Tradusasiòn", "effect.minecraft.poison": "Vełen", "effect.minecraft.raid_omen": "Presajo incursivo", "effect.minecraft.regeneration": "Rezenarasion", "effect.minecraft.resistance": "Rezistensa", "effect.minecraft.saturation": "Saturasion", "effect.minecraft.slow_falling": "Cascada łenta", "effect.minecraft.slowness": "Łentesa", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "Forsa", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON> sfidante", "effect.minecraft.unluck": "Desfortuna", "effect.minecraft.water_breathing": "Respir. acuàtega", "effect.minecraft.weakness": "Fiaca", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "<PERSON>ento <PERSON>", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON><PERSON> e<PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Favor de l'àcua", "enchantment.minecraft.bane_of_arthropods": "Flazel d'i artrò<PERSON>", "enchantment.minecraft.binding_curse": "Małedision de'l łigame", "enchantment.minecraft.blast_protection": "Protesion da i sciopi", "enchantment.minecraft.breach": "<PERSON>ssi<PERSON><PERSON>", "enchantment.minecraft.channeling": "Condutività", "enchantment.minecraft.density": "Densità", "enchantment.minecraft.depth_strider": "Paso d'i fondałi", "enchantment.minecraft.efficiency": "Eficiensa", "enchantment.minecraft.feather_falling": "Cascada łenta", "enchantment.minecraft.fire_aspect": "As<PERSON><PERSON> de <PERSON>o", "enchantment.minecraft.fire_protection": "Protes<PERSON> da'l <PERSON>o", "enchantment.minecraft.flame": "Fiama", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Caminador de giaso", "enchantment.minecraft.impaling": "Inpałamento", "enchantment.minecraft.infinity": "Infinità", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "Łealtà", "enchantment.minecraft.luck_of_the_sea": "Fortuna de'l mar", "enchantment.minecraft.lure": "Lesca", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Multiprojètiłe", "enchantment.minecraft.piercing": "Parforasion", "enchantment.minecraft.power": "<PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Protesion da i projètiłi", "enchantment.minecraft.protection": "Protesion", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "Carga ràpida", "enchantment.minecraft.respiration": "Respirasion", "enchantment.minecraft.riptide": "Propulsion acuàtega", "enchantment.minecraft.sharpness": "Gusa", "enchantment.minecraft.silk_touch": "Toco de seda", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Sveltessa <PERSON>", "enchantment.minecraft.sweeping": "Łama s<PERSON>nte", "enchantment.minecraft.sweeping_edge": "Łama s<PERSON>nte", "enchantment.minecraft.swift_sneak": "Cucià Rapido", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Indestrutìbiłità", "enchantment.minecraft.vanishing_curse": "Małedision de sparision", "enchantment.minecraft.wind_burst": "Stravento", "entity.minecraft.acacia_boat": "Barca de acasia", "entity.minecraft.acacia_chest_boat": "Barca de Acasia co Baul", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nìoła de efeto de area", "entity.minecraft.armadillo": "Armadilo", "entity.minecraft.armor_stand": "Suporto par armadura", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Zàtara de canavera co baul", "entity.minecraft.bamboo_raft": "Zàtara de canavera", "entity.minecraft.bat": "Barbastreło", "entity.minecraft.bee": "Ava", "entity.minecraft.birch_boat": "Barca de brédoła", "entity.minecraft.birch_chest_boat": "Barca de Bredola co Baul", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Visualizador de <PERSON>", "entity.minecraft.boat": "Barca", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Carga de vento", "entity.minecraft.camel": "Camel", "entity.minecraft.cat": "Gato", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "Barca de çiresar", "entity.minecraft.cherry_chest_boat": "Barca de çiresar co' Baul", "entity.minecraft.chest_boat": "Barca co' Baul", "entity.minecraft.chest_minecart": "Carel co' ba<PERSON>l", "entity.minecraft.chicken": "Połastro", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Carel co' bloco comando", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Barca de ròvare scura", "entity.minecraft.dark_oak_chest_boat": "Barca de Rovare scura co Baul", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bała de fogo de dragon", "entity.minecraft.drowned": "Negà", "entity.minecraft.egg": "<PERSON>vo tir<PERSON>", "entity.minecraft.elder_guardian": "Guardian antigo", "entity.minecraft.end_crystal": "Cristal de'l End", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON> de ender tirada", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evocador", "entity.minecraft.evoker_fangs": "Ganase de'l evocador", "entity.minecraft.experience_bottle": "Anpoła de esperiensa tirada", "entity.minecraft.experience_orb": "Spere de espariensa", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>er", "entity.minecraft.falling_block": "Bloco in cascada", "entity.minecraft.falling_block_type": "%s drio cascar", "entity.minecraft.fireball": "Bała de fogo", "entity.minecraft.firework_rocket": "<PERSON><PERSON>", "entity.minecraft.fishing_bobber": "Gałezante da pesca", "entity.minecraft.fox": "Volpe", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Carel co' fornaze", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Zigante", "entity.minecraft.glow_item_frame": "Soàxa l<PERSON>én<PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.goat": "Càvara", "entity.minecraft.guardian": "Guardian", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Carel co' tramoza", "entity.minecraft.horse": "Caval", "entity.minecraft.husk": "Zonbi seco", "entity.minecraft.illusioner": "Iłuzionista", "entity.minecraft.interaction": "Interasion", "entity.minecraft.iron_golem": "Golem de <PERSON>", "entity.minecraft.item": "Ozeto", "entity.minecraft.item_display": "Visualizador de ojeti", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Barca de la jungla", "entity.minecraft.jungle_chest_boat": "Barca de la Jungla co Baul", "entity.minecraft.killer_bunny": "<PERSON><PERSON>o killer", "entity.minecraft.leash_knot": "G<PERSON><PERSON> <PERSON><PERSON><PERSON> z<PERSON>sajo", "entity.minecraft.lightning_bolt": "Sciantizo", "entity.minecraft.lingering_potion": "Posion parsistente", "entity.minecraft.llama": "Łama", "entity.minecraft.llama_spit": "Spuo de łama", "entity.minecraft.magma_cube": "Cubo de magma", "entity.minecraft.mangrove_boat": "Barca de Mangrovia", "entity.minecraft.mangrove_chest_boat": "Barca de Mangrovia co Baul", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "<PERSON><PERSON> da <PERSON>era", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Barca de ròvare", "entity.minecraft.oak_chest_boat": "Barca de Rovare co Baul", "entity.minecraft.ocelot": "Gatopardo", "entity.minecraft.ominous_item_spawner": "Zeneradore de ogeti infàusti", "entity.minecraft.painting": "Cuadro", "entity.minecraft.pale_oak_boat": "Barca de ròvare p<PERSON>lido", "entity.minecraft.pale_oak_chest_boat": "Barca de ròvare pàlida co Baul", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "Porsel", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> bruto", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "Zugador", "entity.minecraft.polar_bear": "<PERSON><PERSON>", "entity.minecraft.potion": "Posion", "entity.minecraft.pufferfish": "Pese bała", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Devastador", "entity.minecraft.salmon": "Salmon", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Bałoto<PERSON> de <PERSON>hulker", "entity.minecraft.silverfish": "<PERSON>ese <PERSON>", "entity.minecraft.skeleton": "Schèłetro", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Zeładina", "entity.minecraft.small_fireball": "Pìcoła bała de fogo", "entity.minecraft.sniffer": "Snasador", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON> de neve", "entity.minecraft.snowball": "Bałoco", "entity.minecraft.spawner_minecart": "Carel co' zenarador de mostri", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> spetral", "entity.minecraft.spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Posion da tiro", "entity.minecraft.spruce_boat": "Barca de avede", "entity.minecraft.spruce_chest_boat": "Barca de Avede co Baul", "entity.minecraft.squid": "Ca<PERSON>mar", "entity.minecraft.stray": "Viandante", "entity.minecraft.strider": "Caminador", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Testo in mostra", "entity.minecraft.tnt": "TNT ativà", "entity.minecraft.tnt_minecart": "Carel co' TNT", "entity.minecraft.trader_llama": "Łama de'l marcante", "entity.minecraft.trident": "Forca", "entity.minecraft.tropical_fish": "Pese tropical", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Pese chirurgo moro", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "Pese paveła ornà", "entity.minecraft.tropical_fish.predefined.12": "Pese pajaso", "entity.minecraft.tropical_fish.predefined.13": "Pese ànzolo regina", "entity.minecraft.tropical_fish.predefined.14": "Cìclide roso", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON><PERSON> roso", "entity.minecraft.tropical_fish.predefined.17": "Pese capitan", "entity.minecraft.tropical_fish.predefined.18": "Pese pajaso pomidoro", "entity.minecraft.tropical_fish.predefined.19": "Pese bałestra", "entity.minecraft.tropical_fish.predefined.2": "Pese chirurgo blè", "entity.minecraft.tropical_fish.predefined.20": "Pese papagal pina zała", "entity.minecraft.tropical_fish.predefined.21": "Pese chirurgo zało", "entity.minecraft.tropical_fish.predefined.3": "Pese paveła", "entity.minecraft.tropical_fish.predefined.4": "Cìclide", "entity.minecraft.tropical_fish.predefined.5": "Pese pajaso", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON> conbatente", "entity.minecraft.tropical_fish.predefined.7": "Pseudocròmide", "entity.minecraft.tropical_fish.predefined.8": "Pese inperador roso", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "Pese betin", "entity.minecraft.tropical_fish.type.blockfish": "Pese bloco", "entity.minecraft.tropical_fish.type.brinely": "Pese pełàzego", "entity.minecraft.tropical_fish.type.clayfish": "Pese crea", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Pese saltarel", "entity.minecraft.tropical_fish.type.glitter": "Pese zluzorin", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Pese zbètega", "entity.minecraft.tropical_fish.type.spotty": "Pese macia", "entity.minecraft.tropical_fish.type.stripey": "<PERSON>ese stri<PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON>esse rajo de sol", "entity.minecraft.turtle": "Tartaruga", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "<PERSON><PERSON>", "entity.minecraft.villager.armorer": "Corasar", "entity.minecraft.villager.butcher": "<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Cartògra<PERSON>", "entity.minecraft.villager.cleric": "Cèrego", "entity.minecraft.villager.farmer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "Fàbrega frese", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.mason": "Murador", "entity.minecraft.villager.nitwit": "Baùco", "entity.minecraft.villager.none": "<PERSON><PERSON>", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "Fàbrica arte", "entity.minecraft.villager.weaponsmith": "Armarol", "entity.minecraft.vindicator": "Vendegador", "entity.minecraft.wandering_trader": "<PERSON><PERSON>", "entity.minecraft.warden": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Carga de vento", "entity.minecraft.witch": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wither", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "Łovo", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON> zonbi", "entity.minecraft.zombie_villager": "<PERSON><PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> z<PERSON>g<PERSON>", "entity.not_summonable": "No se pol evocar l'entidà de tipo %s", "event.minecraft.raid": "Incursion", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Incursion - Desfata", "event.minecraft.raid.raiders_remaining": "<PERSON><PERSON><PERSON>: %s", "event.minecraft.raid.victory": "Vitoria", "event.minecraft.raid.victory.full": "Incursion - Vitoria", "filled_map.buried_treasure": "Mapa de'l tezoro", "filled_map.explorer_jungle": "Mapa del esplorador de la jungla", "filled_map.explorer_swamp": "Mapa del esplorador de le palù", "filled_map.id": "ID nº %s", "filled_map.level": "(Liveło %s/%s)", "filled_map.locked": "Blocada", "filled_map.mansion": "Mapa del esplorador dei boschi", "filled_map.monument": "Mapa del esplorador dei ocèani", "filled_map.scale": "Scała 1:%s", "filled_map.trial_chambers": "Mapa de'l esplorador de ła sfida", "filled_map.unknown": "Mapa mìa conosesta", "filled_map.village_desert": "Mapa del vilajo del deserto", "filled_map.village_plains": "Mapa del vilajo de ła pianura", "filled_map.village_savanna": "Mapa del vilajo de ła savana", "filled_map.village_snowy": "Mapa del vilajo inevà", "filled_map.village_taiga": "Mapa del vilajo de la taiga", "flat_world_preset.minecraft.bottomless_pit": "Poso sensa fondo", "flat_world_preset.minecraft.classic_flat": "Clàsego piato", "flat_world_preset.minecraft.desert": "Dezerto", "flat_world_preset.minecraft.overworld": "Overworld", "flat_world_preset.minecraft.redstone_ready": "Parecià par ła redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "El Vodo", "flat_world_preset.minecraft.tunnelers_dream": "Insònio de'l minador", "flat_world_preset.minecraft.water_world": "Mondo marin", "flat_world_preset.unknown": "???", "gameMode.adventure": "Modałità aventura", "gameMode.changed": "Ła to modałità de zugo l'é stada canbiada in %s", "gameMode.creative": "Modałità creativa", "gameMode.hardcore": "Modałità hardcore!", "gameMode.spectator": "Modałità spetador", "gameMode.survival": "Modałità soravivensa", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "Nunsia i progresi", "gamerule.blockExplosionDropDecay": "L'esplosion de blochi desfa i ogeti rilasciabili", "gamerule.blockExplosionDropDecay.description": "Quando l'interasiòn coi blochi causa esplosiòn che desfa blochi, vien persa parte dei ogeti normalmente rilasciabili.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "<PERSON><PERSON><PERSON> (Drop)", "gamerule.category.misc": "<PERSON><PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Zugador", "gamerule.category.spawning": "Zenarasion", "gamerule.category.updates": "Azornamenti de'l mondo", "gamerule.commandBlockOutput": "Traxmeti l'output d'i blochi comando", "gamerule.commandModificationBlockLimit": "Limite dei blochi modifegabili par comando", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON><PERSON>hi che i pol èssar canbiadi int'una volta da un ordene, cofà inpinissi o clona.", "gamerule.disableElytraMovementCheck": "Dezabìłita el controło de'l movimento par łe elytre", "gamerule.disablePlayerMovementCheck": "Dezabìłita el controło de'l movimento dei zugadori", "gamerule.disableRaids": "De-ativa le incursion", "gamerule.doDaylightCycle": "El ciclo del giorno e della notte", "gamerule.doEntityDrops": "Buta ecuipajamento entidà", "gamerule.doEntityDrops.description": "Controła el relaso d'i carełi da miniera (incluzi i inventari), łe cornize, łe barche, evc.", "gamerule.doFireTick": "Azorna el fogo", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON> phantom", "gamerule.doLimitedCrafting": "Resete de frabricasion rechieste", "gamerule.doLimitedCrafting.description": "Se ativà, i zugadori i podarà fabricar sol a partir da reçete desblocade.", "gamerule.doMobLoot": "Buta el botin de łe mob", "gamerule.doMobLoot.description": "Controła łe resorse dropade da łe criature, conprendesti anca i globi de espariensa.", "gamerule.doMobSpawning": "Zènara mob", "gamerule.doMobSpawning.description": "Alcune entidà łe podarìa vere règołe difarenti.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON><PERSON> batajon de sa<PERSON>jadori", "gamerule.doTileDrops": "<PERSON><PERSON> blochi", "gamerule.doTileDrops.description": "Controła łe resorse butade da i blochi, conprendesti anca i globi de espariensa.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON><PERSON> marcanti anbulanti", "gamerule.doVinesSpread": "Propagasion dei rampeganti", "gamerule.doVinesSpread.description": "El controla se i ranpeganti i pol propagarse in manera casual sui blochi adiasenti. El influisse mìa su altri tipi de ranpeganti cofà Ranpeganti pianzenti, Ranpeganti intorcolài, etc.", "gamerule.doWardenSpawning": "<PERSON><PERSON>", "gamerule.doWeatherCycle": "Azorna el tenpo atmosfèrego", "gamerule.drowningDamage": "Inflizi dano da negamento", "gamerule.enderPearlsVanishOnDeath": "Le perle de End tirade le svanisse a la morte", "gamerule.enderPearlsVanishOnDeath.description": "El stabilisse se le perle de End tirade dal zugador le svanisse co el zugador el more.", "gamerule.entitiesWithPassengersCanUsePortals": "Entidà co' passejeri le pol travessar i portài", "gamerule.entitiesWithPassengersCanUsePortals.description": "Consenti a łe entidà con pasejeri de teletrasportarse n'te i portałi de'ł Nether, portałi de'ł End e varchi de'ł End.", "gamerule.fallDamage": "Inflizi dano da cascada", "gamerule.fireDamage": "Inflizi dano da fogo", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON> i zugadori morti", "gamerule.forgiveDeadPlayers.description": "I mob neutrài provocadi i fenisse de èssar rabiadi cuando el zugador bersajo el more in prossimità.", "gamerule.freezeDamage": "Inflizare dano da congełamento", "gamerule.globalSoundEvents": "Eventi sonori globałi", "gamerule.globalSoundEvents.description": "Al verificarse de serti eventi, come ła generasiòn de on boss, el sono sarà sentìo globalmente.", "gamerule.keepInventory": "Conserva el inventario dopo la morte", "gamerule.lavaSourceConversion": "<PERSON>va convert<PERSON>a in sorzente", "gamerule.lavaSourceConversion.description": "Quando ła łava cołante l'è sircondà da sorgente de łava so do parte, se trasformarà in sorgente.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Trasmeti i comandi dei aministradori", "gamerule.maxCommandChainLength": "Lìmite màssimo e de cadene de comandi", "gamerule.maxCommandChainLength.description": "La se àplega ai blochi ordene a cadena e funsion.", "gamerule.maxCommandForkCount": "Lìmite de contesto dei ordeni", "gamerule.maxCommandForkCount.description": "Numero masimo dei contesti che poł esare doparà da comandi come 'execute as'.", "gamerule.maxEntityCramming": "Lìmite de ingrumamento entidà", "gamerule.minecartMaxSpeed": "Massima sveltessa del carel", "gamerule.minecartMaxSpeed.description": "Massima sveltessa de un carel in movimento par tera.", "gamerule.mobExplosionDropDecay": "L'esplosion de łe criature desfa i ogeti rilasciabili", "gamerule.mobExplosionDropDecay.description": "Quando łe criature causa esplosiòn che desfa blochi, vien persa parte dei ogeti normalmente rilasciabili.", "gamerule.mobGriefing": "Parmeti asion destrutive de le mob", "gamerule.naturalRegeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.playersNetherPortalCreativeDelay": "Retardo del portal del Nether in mod creativa", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (in tick) che un zugadore in modałità creativa gà da trascorere in'te on portałe del Nether prima de cambiar dimension.", "gamerule.playersNetherPortalDefaultDelay": "Retardo deł portal del Nether (esclusa modałità creativa)", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (in tick) che un zugadore in modałità diversa da creativa gà da trascorere in'te on portałe del Nether prima de cambiar dimension.", "gamerule.playersSleepingPercentage": "Per<PERSON><PERSON>ł<PERSON> de zugadori drio pisocare", "gamerule.playersSleepingPercentage.description": "Ła percentuałe de zugadori che łi gà da pisocare par saltare la note.", "gamerule.projectilesCanBreakBlocks": "I projètili i pol spacar i blochi", "gamerule.projectilesCanBreakBlocks.description": "Controła se l'urto dei projetili po' desfare i blochi vulnerabiłi.", "gamerule.randomTickSpeed": "Frecuensa d'i tick cazuałi", "gamerule.reducedDebugInfo": "Reduzi info de debug", "gamerule.reducedDebugInfo.description": "Lìmita el contegnùo de la schermada de debug.", "gamerule.sendCommandFeedback": "Manda el rescontro d'i comandi", "gamerule.showDeathMessages": "<PERSON>ra mesaji de morte", "gamerule.snowAccumulationHeight": "Altesa de acumulo de neve", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON><PERSON>, ła neve aumentarà par tera fin al numaro masimo de strati spesifegà.", "gamerule.spawnChunkRadius": "Razo de spawn chunk", "gamerule.spawnChunkRadius.description": "Numaro de chunk che resta caricà intorno al punto de generasiòn globałe.", "gamerule.spawnRadius": "<PERSON><PERSON> da'l ponto de respawn", "gamerule.spawnRadius.description": "El controla la dimension de l'area intornovia al ponto de spawn, indove el zugador el pol zenerarse.", "gamerule.spectatorsGenerateChunks": "Parameti ai spetadori de zenarar teren", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "L'esplosion de ła dinamite desfa i ogeti rilasciabili", "gamerule.tntExplosionDropDecay.description": "Quando ła dinamite causa esplosiòn che desfa blochi, vien persa parte dei ogeti normalmente rilasciabili.", "gamerule.universalAnger": "Invełenamento univarsałe", "gamerule.universalAnger.description": "Łi mob neutrałi invełenadi łi ataca calsìase zugador in prosimità, mìa sol el zugador che'l łi ga provocadi. El funsiona mejo se ła règoła forgiveDeadPlayers l'é cavà.", "gamerule.waterSourceConversion": "Acua convertìa in sorzente", "gamerule.waterSourceConversion.description": "Quando l'acua corente l'è sircondà da sorgente de acua so do parte, se trasformarà in sorgente.", "generator.custom": "Parsonałizà", "generator.customized": "Parsonalizà (vecio)", "generator.minecraft.amplified": "ANPLIFEGÀ", "generator.minecraft.amplified.info": "Nota ben: sol par gòdarte! El dimanda un computer ciosso.", "generator.minecraft.debug_all_block_states": "Debug", "generator.minecraft.flat": "Piato", "generator.minecraft.large_biomes": "Biomi grandi", "generator.minecraft.normal": "Predefenìo", "generator.minecraft.single_biome_surface": "Bioma sìngolo", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Ìsołe flutuante", "gui.abuseReport.attestation": "Inviando sta segnalasiòn, te confermi che le informasiòn che te gà fornìo l'è juste e conplete al mejo de le to conosènse.", "gui.abuseReport.comments": "Comenti", "gui.abuseReport.describe": "<PERSON>ne i detaji ne juta a formular na justa decisiòn.", "gui.abuseReport.discard.content": "Se te bandoni, te perdarè el report e i to coment.\nSito securo de voler bandonar?", "gui.abuseReport.discard.discard": "Bandona e descarta report", "gui.abuseReport.discard.draft": "<PERSON>va bozza", "gui.abuseReport.discard.return": "Continua a modifegar", "gui.abuseReport.discard.title": "<PERSON>carta el report e i comenti?", "gui.abuseReport.draft.content": "Vuto continuar a modificar queo che te ga scrito o scartàrlo e farne uno novo?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Continua a modifegar", "gui.abuseReport.draft.quittotitle.content": "Voto continuar a modifegarla o scancelarla?", "gui.abuseReport.draft.quittotitle.title": "Te ghè na bozza de report che la ndarà persa se te bandoni", "gui.abuseReport.draft.title": "Voto modifegar la bozza de report?", "gui.abuseReport.error.title": "Ghe xe stato n'problema a mandar ti report", "gui.abuseReport.message": "Indove gh'eto osservà el conportamento scoreto?\nCuesto el ne jurarà in la reçerca del to caso.", "gui.abuseReport.more_comments": "Par piaser descri cuel che xe sucesso:", "gui.abuseReport.name.comment_box_label": "Par piaser descivi parché te vol reportar sto nome:", "gui.abuseReport.name.reporting": "Te si drio reportar \"%s\".", "gui.abuseReport.name.title": "Reporta nome inapropià del zugador", "gui.abuseReport.observed_what": "Parché sito drio reportarlo?", "gui.abuseReport.read_info": "Descoverzi de pì sui report", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Droga e alco'", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Cualchedun xe drio incorajar altri a tor parte a atività ligade a la droga o al alcolismo menorile.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Abuso o desfrutamento sessual de toseti", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> k<PERSON><PERSON>", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamasion", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Cualchedun xe drio danizar la to repudasion o cuela de altri, par exenpio condividendo informasion false co l'intento de sfrutar o busarar altri.", "gui.abuseReport.reason.description": "Descrisión:", "gui.abuseReport.reason.false_reporting": "Report falso", "gui.abuseReport.reason.generic": "V<PERSON>arli", "gui.abuseReport.reason.generic.description": "<PERSON>o zugadore me infastidìse o gà fato qualcosa che no me piaze.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON> o bullismo", "gui.abuseReport.reason.harassment_or_bullying.description": "Cualchedun el xe drio umiliar, agredir o bulizar ti cualcheduni altro. Cuesto l'include cuande cualchedun el prova contatarte ti o caulcheduni altro sensa el consenso, o el posta informasion private parsonài revardo ti ao cualcheduni altro sensa el consenso (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Discorsi de odio", "gui.abuseReport.reason.hate_speech.description": "Cualchedun el xe drio atacarte ti o altri zugardori par via de caratarìsteghe de la so identidà, cofà religion, raça o sessualidà.", "gui.abuseReport.reason.imminent_harm": "Menaza de far male a i altri", "gui.abuseReport.reason.imminent_harm.description": "Qualcheduni sta minazando de far male a ti o a qualchedun altro 'nte ła vita reale.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imazene intime non consensuałi", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Qualchedun sta mensionando, condividendo o promovendo imazene private e intime.", "gui.abuseReport.reason.self_harm_or_suicide": "Autolesionismo o suicidio", "gui.abuseReport.reason.self_harm_or_suicide.description": "Qualchedun sta minazando de farse male 'n te ła vita reale o sta mensionando el farse małe 'n te ła vita reale.", "gui.abuseReport.reason.sexually_inappropriate": "Contegnùi sesualmente inapropriài", "gui.abuseReport.reason.sexually_inappropriate.description": "Skin esplisite con riferimenti a ati sesuałi, organi sesuałi o viołensa sesuałe.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terorismo o estremismo violento", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Qualchedun sta mensionando, promovendo o minazando de cometare ati de terorismo o estremismo viołento par motivi połitici, re<PERSON><PERSON><PERSON><PERSON>, ieołogici o de altra sorta.", "gui.abuseReport.reason.title": "Selesiona la categoria de'l report", "gui.abuseReport.report_sent_msg": "Ghemo ricevuto el to report. Grasie!\n\nControleremo prima posibile.", "gui.abuseReport.select_reason": "Selesiona categoria de report", "gui.abuseReport.send": "Manda report", "gui.abuseReport.send.comment_too_long": "Par piaser scurta el comento", "gui.abuseReport.send.error_message": "N'eror se sbucà mentre stàvimo mandando ti report:\n'%s'", "gui.abuseReport.send.generic_error": "Ghe xe stato n'problema mìa previsto a mandar ti report.", "gui.abuseReport.send.http_error": "Ghe xe stato n'erore HTTP mìa previsto mentre stàvimo mandando ti report.", "gui.abuseReport.send.json_error": "Ghemo catà un Payload malformato mentre stàvimo mandando ti report.", "gui.abuseReport.send.no_reason": "Par piaser selesiona na categoria de report", "gui.abuseReport.send.not_attested": "Par piaser lezi el testo de sora e sponta el recuadro par poder inviar el report", "gui.abuseReport.send.service_unavailable": "Conesìon a el servisio de Report mìa posibile. Par piaser controla tì conesion Internet e prova de novo.", "gui.abuseReport.sending.title": "Mandando ti report...", "gui.abuseReport.sent.title": "El referìr s<PERSON> sta mandà", "gui.abuseReport.skin.title": "Reporta la skin del zugador", "gui.abuseReport.title": "Reporta un zugador", "gui.abuseReport.type.chat": "<PERSON><PERSON><PERSON> in chat", "gui.abuseReport.type.name": "Nome del zugador", "gui.abuseReport.type.skin": "Skin del zugador", "gui.acknowledge": "Açeta", "gui.advancements": "Progresi", "gui.all": "<PERSON><PERSON>", "gui.back": "<PERSON><PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nPì informasiòni en sto link: %s", "gui.banned.description.permanent": "Tì account se sospeso da el zogo online parmanentemente, no te pol zugare online o sù Realms.", "gui.banned.description.reason": "Ghemo risevùo un report de bruta condota su ti account. I nostri moderadori gan visto tì caso e indentifegato come %s, che va contro a le Regole zenerali de'la Comunity di Minecraft.", "gui.banned.description.reason_id": "Còdice: %s", "gui.banned.description.reason_id_message": "Còdice: %s - %s", "gui.banned.description.temporary": "Fin a %s no te pol zugà online o sù Realms.", "gui.banned.description.temporary.duration": "Account sospeso tenponareamente e sarà reativato in %s.", "gui.banned.description.unknownreason": "Ghemo risevùo un report de bruta condota su ti account. I nostri moderadori gan visto tì caso e indentifegato che va contro a 'e Regole zenerali de'la Comunity di Minecraft.", "gui.banned.name.description": "El to nome atual - \"%s\" - el viola i nostri Standard de Community. Te pol zugar in zugardo singolo, ma te gavarè da canbiar el to nome par zugar online.", "gui.banned.name.title": "Nome mìa parmesso in Multizugador", "gui.banned.reason.defamation_impersonation_false_information": "Sostitusion de persona o difusiòn de informasiòn al fine de tontonàr o aprofitare de i altri", "gui.banned.reason.drugs": "Refarimenti a droghe ilegài", "gui.banned.reason.extreme_violence_or_gore": "Rafigurasiòn de scene reałi masa violente o cruente", "gui.banned.reason.false_reporting": "Numero masa alto de segnalasiòn false o inprecìse", "gui.banned.reason.fraud": "Acuisisiòn o uso fraudolento de contegnùi", "gui.banned.reason.generic_violation": "Violasiòn de i standard de ła comunità", "gui.banned.reason.harassment_or_bullying": "Lenguajo ofensivo doprà in maniera direta e danosa", "gui.banned.reason.hate_speech": "Insitamento a ł'odio o discriminasiòn", "gui.banned.reason.hate_terrorism_notorious_figure": "Riferimenti a grupi de odio, organizasiòn teroristiche o figure malfamàde", "gui.banned.reason.imminent_harm_to_person_or_property": "Intento de causare dano 'n te ła vita reale a zente o beni", "gui.banned.reason.nudity_or_pornography": "Condivisiòn de materiałi sporchi o pornogràfici", "gui.banned.reason.sexually_inappropriate": "Argomenti o contegnùi de natura sesuałe", "gui.banned.reason.spam_or_advertising": "Spam o reclan", "gui.banned.skin.description": "La to skin atual - \"%s\" - la vìola i nostri standard de community. Te pol continuar a zugar co na skin predefinìa o scegliarghene una nova", "gui.banned.skin.title": "Skin mìa consentida", "gui.banned.title.permanent": "El to account ze sta sospeso par sempre", "gui.banned.title.temporary": "Account sospeso tenponareamente", "gui.cancel": "Rèvoca", "gui.chatReport.comments": "Commenti", "gui.chatReport.describe": "Dan<PERSON>ci i detaji ci giuta a formular una jùsta decision.", "gui.chatReport.discard.content": "Se bandoni, te perdarai sto report e ti comenti.\nSito seguro de bandonar?", "gui.chatReport.discard.discard": "<PERSON>ula Report", "gui.chatReport.discard.draft": "Salvà fà Bugna", "gui.chatReport.discard.return": "Continua a Modifegar", "gui.chatReport.discard.title": "<PERSON><PERSON> report e comenti?", "gui.chatReport.draft.content": "Te piasaria continuar a modificar queo che te ha scrito o scartàrlo e inciniàrne uno novo?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Continuar a modificar", "gui.chatReport.draft.quittotitle.content": "Te piasaria continuar a modificar o scartàrlo?", "gui.chatReport.draft.quittotitle.title": "Te ghè na bozza de report che la ndarà persa se te bandoni", "gui.chatReport.draft.title": "Voto modifegar la bozza de report?", "gui.chatReport.more_comments": "Par piazer spiega cosa se capità:", "gui.chatReport.observed_what": "Parchè te sti reportando sto qua?", "gui.chatReport.read_info": "Inpara come Reportare", "gui.chatReport.report_sent_msg": "Ghemo ricevuto ti report. Grasie!\n\nControleremo prima posibile.", "gui.chatReport.select_chat": "Selesiona i mesaji da reportar", "gui.chatReport.select_reason": "Selesiona la categoria de'l report", "gui.chatReport.selected_chat": "%s Mesaji/o Selesionà par el Report", "gui.chatReport.send": "Mandà el reférimento", "gui.chatReport.send.comments_too_long": "Scursa el comento", "gui.chatReport.send.no_reason": "Par piazer selesiona la categoria de'l report", "gui.chatReport.send.no_reported_messages": "Par piazer selesiona almanco un mesajo da reportare", "gui.chatReport.send.too_many_messages": "Te ghè selesionà masa mesaji par sto report", "gui.chatReport.title": "Referisci Zogatore", "gui.chatSelection.context": "Mesaji vicìn a queli che tì selesionà saran incluzi par zontar contesto", "gui.chatSelection.fold": "%s messajo(i) sconti", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s el se ga zontà a ła partida", "gui.chatSelection.message.narrate": "%s ga deto: %s a %s", "gui.chatSelection.selected": "%s/%s mesajo/i selesionà", "gui.chatSelection.title": "Selesiona i mesaji da reportar", "gui.continue": "<PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copia inte i apunti", "gui.days": "%s giorni/o", "gui.done": "<PERSON><PERSON>", "gui.down": "<PERSON><PERSON>", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s file rifiutà", "gui.fileDropFailure.title": "Inposìbiłe zontar i file", "gui.hours": "%s ore/a", "gui.loadingMinecraft": "Drio cargar Minecraft", "gui.minutes": "%s minuti/o", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Boton %s", "gui.narrate.editBox": "Bara de testo de %s: %s", "gui.narrate.slider": "%s rego<PERSON>dor", "gui.narrate.tab": "Scheda %s", "gui.no": "<PERSON>ò", "gui.none": "<PERSON><PERSON>", "gui.ok": "Ok", "gui.open_report_dir": "Verzi la carteła dei raporti", "gui.proceed": "<PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "Clic destro par altro", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Çerca...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON> mostrar tute", "gui.recipebook.toggleRecipes.blastable": "Resete par fusion", "gui.recipebook.toggleRecipes.craftable": "Reçete craftàbile", "gui.recipebook.toggleRecipes.smeltable": "Coture realizàbile", "gui.recipebook.toggleRecipes.smokable": "Resete par infumegadura", "gui.report_to_server": "<PERSON><PERSON><PERSON> al server", "gui.socialInteractions.blocking_hint": "Zestisi co' l'account Microsoft", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON><PERSON> zugador blocà co' 'sto nome", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON><PERSON> zugador sconto in te ła chat", "gui.socialInteractions.hidden_in_chat": "I mesaji de %s i sarà sconti da la chat", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON> da la chat", "gui.socialInteractions.narration.hide": "Scondi mesaji da %s", "gui.socialInteractions.narration.report": "Referisci zogatore %s", "gui.socialInteractions.narration.show": "Mostra mesaji da %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "Inposìbile catar un zugador con 'sto nome", "gui.socialInteractions.search_hint": "Serca...", "gui.socialInteractions.server_label.multiple": "%s - %s zugadori", "gui.socialInteractions.server_label.single": "%s - %s zugador", "gui.socialInteractions.show": "Mostra in la chat", "gui.socialInteractions.shown_in_chat": "I messaji de %s i sarà mostradi in te la chat", "gui.socialInteractions.status_blocked": "Blocadi", "gui.socialInteractions.status_blocked_offline": "Blocà - Offline", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Sconto - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "<PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "<PERSON>ont<PERSON>", "gui.socialInteractions.title": "Interasion soçiale", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> me<PERSON>ji", "gui.socialInteractions.tooltip.report": "Referisci zogatore", "gui.socialInteractions.tooltip.report.disabled": "Servisio de report mia desponìbile", "gui.socialInteractions.tooltip.report.no_messages": "Nisun mesajo reportabile da zogator %s", "gui.socialInteractions.tooltip.report.not_reportable": "Cuesto zogatore no pol esar reportato parché i lori mesaji son mìa verifegabili inte sto server", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> mesaji", "gui.stats": "Statìsteghe", "gui.toMenu": "De volta a la lista dei server", "gui.toRealms": "De volta a la lista dei Realms", "gui.toTitle": "De volta al menù prinsipal", "gui.toWorld": "De volta a la lista dei mondi", "gui.togglable_slot": "Fà clic par disativare el slot", "gui.up": "Su", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Sì", "hanging_sign.edit": "Modìfega el messajo del cartel picà", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Ciamo", "instrument.minecraft.dream_goat_horn": "Insonio", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Riflesìon", "instrument.minecraft.seek_goat_horn": "Ricerca", "instrument.minecraft.sing_goat_horn": "Canto", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON>", "inventory.binSlot": "<PERSON><PERSON>", "inventory.hotbarInfo": "Salva ła bara ràpida co' %1$s + %2$s", "inventory.hotbarSaved": "Bara ràpida salvada (repristinada co %1$s + %2$s)", "item.canBreak": "El pol spacar:", "item.canPlace": "Se pol piasarlo su:", "item.canUse.unknown": "No conossùo", "item.color": "Cołor: %s", "item.components": "%s componente/i", "item.disabled": "Ozeto disabiità", "item.durability": "Durevołesa: %s / %s", "item.dyed": "Piturà", "item.minecraft.acacia_boat": "Barca de acasia", "item.minecraft.acacia_chest_boat": "Barca de Acasia co Baul", "item.minecraft.allay_spawn_egg": "Ovo zenerador de Allay", "item.minecraft.amethyst_shard": "Scheza de ametista", "item.minecraft.angler_pottery_shard": "Cocio co cana da pesca", "item.minecraft.angler_pottery_sherd": "Cocio co cana da pesca", "item.minecraft.apple": "Pomo", "item.minecraft.archer_pottery_shard": "Cocio co arco e frecia", "item.minecraft.archer_pottery_sherd": "Cocio co arco e frecia", "item.minecraft.armadillo_scute": "Scuama de armadilo", "item.minecraft.armadillo_spawn_egg": "Ovo zenerador de armadilo", "item.minecraft.armor_stand": "Suporto par armadura", "item.minecraft.arms_up_pottery_shard": "Cocio co alzada de brazi", "item.minecraft.arms_up_pottery_sherd": "Cocio co alzada de brazi", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Secio con axolotl", "item.minecraft.axolotl_spawn_egg": "Ovo zenerador de axolotl", "item.minecraft.baked_potato": "Patata a'l forno", "item.minecraft.bamboo_chest_raft": "Zàtara de canavera con baul", "item.minecraft.bamboo_raft": "Zàtara de canavera", "item.minecraft.bat_spawn_egg": "Ovo zenerador de barbastreło", "item.minecraft.bee_spawn_egg": "Ovo zenerador de ava", "item.minecraft.beef": "<PERSON><PERSON> c<PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Somensa de rava", "item.minecraft.beetroot_soup": "Supa de rava", "item.minecraft.birch_boat": "Barca de brédoła", "item.minecraft.birch_chest_boat": "Barca de Bredola co Baul", "item.minecraft.black_bundle": "Sach<PERSON>to negro", "item.minecraft.black_dye": "Cołorante negro", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Cocio co łama", "item.minecraft.blade_pottery_sherd": "Cocio co łama", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "Verga de blaze", "item.minecraft.blaze_spawn_egg": "Ovo zenerador de blaze", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON><PERSON> blé", "item.minecraft.blue_dye": "Cołorante blè", "item.minecraft.blue_egg": "<PERSON><PERSON> blé", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Ovo zenarador de Paludà", "item.minecraft.bolt_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ornamento d'armadura <PERSON>", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Figura par gonfalòn con bordadura dentà", "item.minecraft.bow": "Arco", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Verga de breeze", "item.minecraft.breeze_spawn_egg": "Ovo zenerador de breeze", "item.minecraft.brewer_pottery_shard": "Cocio del vinàro", "item.minecraft.brewer_pottery_sherd": "Cocio del vinàro", "item.minecraft.brewing_stand": "Łanbico", "item.minecraft.brick": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON><PERSON> maron", "item.minecraft.brown_dye": "Cołorante maron", "item.minecraft.brown_egg": "<PERSON>vo maron", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Bruschin", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Po' tegnere na piła mista de roba", "item.minecraft.bundle.full": "Pien", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Cocio con fiameła", "item.minecraft.burn_pottery_sherd": "Cocio con fiameła", "item.minecraft.camel_spawn_egg": "Ovo zenerador de Camel", "item.minecraft.carrot": "Carota", "item.minecraft.carrot_on_a_stick": "Carota su baston", "item.minecraft.cat_spawn_egg": "Ovo zenerador de gato", "item.minecraft.cauldron": "Calderon", "item.minecraft.cave_spider_spawn_egg": "Ovo zenerador de ragno de le grote", "item.minecraft.chainmail_boots": "Stivałi de maja", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON> de maja", "item.minecraft.chainmail_helmet": "Cełada de maja", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.charcoal": "Carboneła", "item.minecraft.cherry_boat": "Barca de çiresar", "item.minecraft.cherry_chest_boat": "Barca de çiresar co' Baul", "item.minecraft.chest_minecart": "Carel co' ba<PERSON>l", "item.minecraft.chicken": "Poła<PERSON> cr<PERSON>", "item.minecraft.chicken_spawn_egg": "Ovo zenerador de polastro", "item.minecraft.chorus_fruit": "Fruto de chorus", "item.minecraft.clay_ball": "Bala de Crea", "item.minecraft.clock": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbon", "item.minecraft.coast_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.coast_armor_trim_smithing_template.new": "Ornamento d'armadura Costera", "item.minecraft.cocoa_beans": "Fave de cacào", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON> con merluso", "item.minecraft.cod_spawn_egg": "<PERSON>vo zenerador de merl<PERSON>o", "item.minecraft.command_block_minecart": "Carel co' bloco comando", "item.minecraft.compass": "<PERSON><PERSON>soła", "item.minecraft.cooked_beef": "Bisteca", "item.minecraft.cooked_chicken": "<PERSON>ła<PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "Carne de piégora co<PERSON>ta", "item.minecraft.cooked_porkchop": "Brizoła de porsel cozesta", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON> cozesto", "item.minecraft.cookie": "Biscoto", "item.minecraft.copper_ingot": "Ling<PERSON> de Ramo", "item.minecraft.cow_spawn_egg": "Ovo zenerador de vaca", "item.minecraft.creaking_spawn_egg": "Ovo zenerador de Scrico", "item.minecraft.creeper_banner_pattern": "Fegura par gonfałon", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON> de creeper", "item.minecraft.creeper_banner_pattern.new": "Figura par gonfalòn con creeper", "item.minecraft.creeper_spawn_egg": "<PERSON>vo zenerador de creeper", "item.minecraft.crossbow": "Bałestra", "item.minecraft.crossbow.projectile": "Projètiłe:", "item.minecraft.crossbow.projectile.multiple": "Projètile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projètile: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON><PERSON> ciano", "item.minecraft.cyan_dye": "Cołoran<PERSON> sian", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Cocio con avertimento", "item.minecraft.danger_pottery_sherd": "Cocio con avertimento", "item.minecraft.dark_oak_boat": "Barca de ròvare scura", "item.minecraft.dark_oak_chest_boat": "Barca de Rovare scura co Baul", "item.minecraft.debug_stick": "Baston de debug", "item.minecraft.debug_stick.empty": "%s no'l ga propietà", "item.minecraft.debug_stick.select": "sełesionà \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" canbià in %s", "item.minecraft.diamond": "Diamante", "item.minecraft.diamond_axe": "Manara de Diamante", "item.minecraft.diamond_boots": "Stivài de Diamante", "item.minecraft.diamond_chestplate": "<PERSON><PERSON>", "item.minecraft.diamond_helmet": "Çelada de Diamante", "item.minecraft.diamond_hoe": "Sapa de Diamante", "item.minecraft.diamond_horse_armor": "Bardadura de Diamante", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "Picon de Diamante", "item.minecraft.diamond_shovel": "<PERSON>ìl de Diamante", "item.minecraft.diamond_sword": "Spada de Diamante", "item.minecraft.disc_fragment_5": "Framento de Disco", "item.minecraft.disc_fragment_5.desc": "Disco muzical - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON>vo zenerador de dolfin", "item.minecraft.donkey_spawn_egg": "<PERSON>vo zenerador de à<PERSON>o", "item.minecraft.dragon_breath": "<PERSON><PERSON> de dragon", "item.minecraft.dried_kelp": "Àlega seca", "item.minecraft.drowned_spawn_egg": "Ovo zenerador de negà", "item.minecraft.dune_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.dune_armor_trim_smithing_template.new": "Ornamento d'armadura Duna", "item.minecraft.echo_shard": "Scheja de'l Eco", "item.minecraft.egg": "Ovo", "item.minecraft.elder_guardian_spawn_egg": "Ovo zenerador de guardian antigo", "item.minecraft.elytra": "<PERSON><PERSON>", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Ł<PERSON>ro incantà", "item.minecraft.enchanted_golden_apple": "Pomo indorà incantà", "item.minecraft.end_crystal": "Cristal de'l End", "item.minecraft.ender_dragon_spawn_egg": "Ovo zenerador de Dragon del End", "item.minecraft.ender_eye": "<PERSON><PERSON>er", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON>vo zenerador de enderman", "item.minecraft.endermite_spawn_egg": "Ovo zenerador de endermite", "item.minecraft.evoker_spawn_egg": "Ovo zenerador de evocador", "item.minecraft.experience_bottle": "Anpoła de espariensa", "item.minecraft.explorer_pottery_shard": "Cocio con mapa", "item.minecraft.explorer_pottery_sherd": "Cocio con mapa", "item.minecraft.eye_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.eye_armor_trim_smithing_template.new": "Ornamento d'armadura <PERSON>", "item.minecraft.feather": "<PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "Figura par gonfalòn de matoni", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Carga de fogo", "item.minecraft.firework_rocket": "<PERSON><PERSON>", "item.minecraft.firework_rocket.flight": "Durada de voło:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Steła pirotècnega", "item.minecraft.firework_star.black": "Negro", "item.minecraft.firework_star.blue": "Blè", "item.minecraft.firework_star.brown": "Mar<PERSON>", "item.minecraft.firework_star.custom_color": "Parsonałizà", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON> in", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "Grizo", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Sełeste", "item.minecraft.firework_star.light_gray": "Grizo ciaro", "item.minecraft.firework_star.lime": "Lime", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "Vioła", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "Forma mìa conosesta", "item.minecraft.firework_star.shape.burst": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "A forma de creeper", "item.minecraft.firework_star.shape.large_ball": "Bała granda", "item.minecraft.firework_star.shape.small_ball": "Bała pìcoła", "item.minecraft.firework_star.shape.star": "A forma de steła", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "Bianco", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "Cana da pesca", "item.minecraft.flint": "Séłeze", "item.minecraft.flint_and_steel": "Batifogo", "item.minecraft.flow_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.flow_armor_trim_smithing_template.new": "Ornamento d'armadura Corivo", "item.minecraft.flow_banner_pattern": "Figura par gonfałon", "item.minecraft.flow_banner_pattern.desc": "Fluso", "item.minecraft.flow_banner_pattern.new": "Figura par gonfalòn con corìvo", "item.minecraft.flow_pottery_sherd": "Cocio con corìvo", "item.minecraft.flower_banner_pattern": "Fegura par gonfałon", "item.minecraft.flower_banner_pattern.desc": "Stan<PERSON> de fi<PERSON>", "item.minecraft.flower_banner_pattern.new": "Figura par gonfalòn con fiore", "item.minecraft.flower_pot": "Vazo da fiori", "item.minecraft.fox_spawn_egg": "Ovo zenerador de volpe", "item.minecraft.friend_pottery_shard": "Cocio co amigo", "item.minecraft.friend_pottery_sherd": "Cocio co amigo", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON> z<PERSON>ador <PERSON>", "item.minecraft.furnace_minecart": "Carel co' fornaze", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> zenerador de g<PERSON>t", "item.minecraft.ghast_tear": "Łàgrema de g<PERSON>t", "item.minecraft.glass_bottle": "Anpoła de vero", "item.minecraft.glistering_melon_slice": "Feta de inguria <PERSON>e", "item.minecraft.globe_banner_pattern": "Figura par gonfałon", "item.minecraft.globe_banner_pattern.desc": "Globo", "item.minecraft.globe_banner_pattern.new": "Figura par gonfalòn con globo", "item.minecraft.glow_berries": "Pomele sluxorine", "item.minecraft.glow_ink_sac": "Saca de ingiostro l<PERSON>", "item.minecraft.glow_item_frame": "Soàxa l<PERSON>én<PERSON>", "item.minecraft.glow_squid_spawn_egg": "<PERSON>vo zenerador de całamar l<PERSON>", "item.minecraft.glowstone_dust": "Pólvar de zluzorite", "item.minecraft.goat_horn": "Corno de Cavara", "item.minecraft.goat_spawn_egg": "Ovo zenerador de càvara", "item.minecraft.gold_ingot": "Lingoto de Oro", "item.minecraft.gold_nugget": "Pepita de Oro", "item.minecraft.golden_apple": "Pomo indorà", "item.minecraft.golden_axe": "Manara de Oro", "item.minecraft.golden_boots": "Stivài de Oro", "item.minecraft.golden_carrot": "Carota indorada", "item.minecraft.golden_chestplate": "Corassa de Oro", "item.minecraft.golden_helmet": "Çelada de Oro", "item.minecraft.golden_hoe": "Sapa de Oro", "item.minecraft.golden_horse_armor": "Bardadura de oro", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON> de Oro", "item.minecraft.golden_pickaxe": "Picon de Oro", "item.minecraft.golden_shovel": "Baìl de Oro", "item.minecraft.golden_sword": "Spada de Oro", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON><PERSON> griso", "item.minecraft.gray_dye": "Cołorante grizo", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON> verdo", "item.minecraft.green_dye": "Cołorante verdo", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "<PERSON>vo zenerador de guardian", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON><PERSON> zbaro", "item.minecraft.guster_banner_pattern": "Fegura par gonfałon", "item.minecraft.guster_banner_pattern.desc": "Ràfega", "item.minecraft.guster_banner_pattern.new": "Figura par gonfalòn con ràfega", "item.minecraft.guster_pottery_sherd": "Cocio con ràfega", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON> <PERSON><PERSON>l mar", "item.minecraft.heart_pottery_shard": "Cocio con core", "item.minecraft.heart_pottery_sherd": "Cocio con core", "item.minecraft.heartbreak_pottery_shard": "Cocio con core roto", "item.minecraft.heartbreak_pottery_sherd": "Cocio con core roto", "item.minecraft.hoglin_spawn_egg": "Vòvo zenerador de Hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON> de mel", "item.minecraft.honeycomb": "Caraza", "item.minecraft.hopper_minecart": "Carel co' tramoza", "item.minecraft.horse_spawn_egg": "Ovo zenerador de caval", "item.minecraft.host_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.host_armor_trim_smithing_template.new": "Ornamento stiłe ospite", "item.minecraft.howl_pottery_shard": "Cocio con ululàto", "item.minecraft.howl_pottery_sherd": "Cocio con ululàto", "item.minecraft.husk_spawn_egg": "Ovo zenerador de zonbi seco", "item.minecraft.ink_sac": "Saca de inciostro", "item.minecraft.iron_axe": "Manara de Fero", "item.minecraft.iron_boots": "Stivài de Fero", "item.minecraft.iron_chestplate": "<PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Ovo zenerador de Golem de Fero", "item.minecraft.iron_helmet": "Çelada de Fero", "item.minecraft.iron_hoe": "Sapa de Fero", "item.minecraft.iron_horse_armor": "Bardadura de Fero", "item.minecraft.iron_ingot": "<PERSON><PERSON> de Fero", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Picon de Fero", "item.minecraft.iron_shovel": "Baìl de <PERSON>ro", "item.minecraft.iron_sword": "Spada de Fero", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Barca de ła jungla", "item.minecraft.jungle_chest_boat": "Barca de la Jungla co Baul", "item.minecraft.knowledge_book": "<PERSON><PERSON><PERSON> de ła conosensa", "item.minecraft.lapis_lazuli": "Lapislàsar<PERSON>", "item.minecraft.lava_bucket": "<PERSON>cio de <PERSON>", "item.minecraft.lead": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "Stivałi de corame", "item.minecraft.leather_chestplate": "Tùnega de corame", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON> de co<PERSON>e", "item.minecraft.leather_horse_armor": "Bardadura de corame", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_dye": "Cołorante sełeste", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON><PERSON> griso ciaro", "item.minecraft.light_gray_dye": "Cołorante grizo ciaro", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Sacheto lime", "item.minecraft.lime_dye": "Cołorante lime", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Posion parsistente", "item.minecraft.lingering_potion.effect.awkward": "Posion stranba parsistente", "item.minecraft.lingering_potion.effect.empty": "Posion parsistente mìa <PERSON>e", "item.minecraft.lingering_potion.effect.fire_resistance": "Posion parsistente de rezistensa a'l fogo", "item.minecraft.lingering_potion.effect.harming": "Posion parsistente de dano", "item.minecraft.lingering_potion.effect.healing": "Posion parsistente de cura", "item.minecraft.lingering_potion.effect.infested": "Posion parsistente de infestasiòn", "item.minecraft.lingering_potion.effect.invisibility": "Posion parsistente de l'invizibiłità", "item.minecraft.lingering_potion.effect.leaping": "Posion parsistente de salto", "item.minecraft.lingering_potion.effect.levitation": "Posion parsistente de łevitasion", "item.minecraft.lingering_potion.effect.luck": "Posion parsistente de fortuna", "item.minecraft.lingering_potion.effect.mundane": "Posion ordenaria parsistente", "item.minecraft.lingering_potion.effect.night_vision": "Posion parsistente de vista noturna", "item.minecraft.lingering_potion.effect.oozing": "Posion parsistente de trasudasiòn", "item.minecraft.lingering_potion.effect.poison": "Posion parsistente de vełen", "item.minecraft.lingering_potion.effect.regeneration": "Posion parsistente de rezenarasion", "item.minecraft.lingering_potion.effect.slow_falling": "Posion parsistente de cascada łenta", "item.minecraft.lingering_potion.effect.slowness": "Posion parsistente de łentesa", "item.minecraft.lingering_potion.effect.strength": "Posion parsistente de forsa", "item.minecraft.lingering_potion.effect.swiftness": "Posion parsistente de sveltesa", "item.minecraft.lingering_potion.effect.thick": "Posion fisa parsistente", "item.minecraft.lingering_potion.effect.turtle_master": "Posion parsistente de'l maestro tartaruga", "item.minecraft.lingering_potion.effect.water": "Anpoła de àcua parsistente", "item.minecraft.lingering_potion.effect.water_breathing": "Posion parsitente de respir. acuàtega", "item.minecraft.lingering_potion.effect.weakness": "Posion parsistente de fiaca", "item.minecraft.lingering_potion.effect.weaving": "Posion parsistente de tesidura", "item.minecraft.lingering_potion.effect.wind_charged": "Posion parsistente de carica ventosa", "item.minecraft.llama_spawn_egg": "<PERSON>vo zenerador de lama", "item.minecraft.lodestone_compass": "Bùssola magnetizada", "item.minecraft.mace": "M<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Sachéto magenta", "item.minecraft.magenta_dye": "Coł<PERSON><PERSON> mazenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Ovo zenerador de cubo de magma", "item.minecraft.mangrove_boat": "Barca de Mangrovia", "item.minecraft.mangrove_chest_boat": "Barca de Mangrovia co Baul", "item.minecraft.map": "Mapa voda", "item.minecraft.melon_seeds": "Somensa de ing<PERSON>", "item.minecraft.melon_slice": "Feta de inguria", "item.minecraft.milk_bucket": "<PERSON><PERSON>", "item.minecraft.minecart": "<PERSON><PERSON> da minera", "item.minecraft.miner_pottery_shard": "Cocio con pìco", "item.minecraft.miner_pottery_sherd": "Cocio con pìco", "item.minecraft.mojang_banner_pattern": "Fegura par gonfałon", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "Figura par gonfalòn con logo", "item.minecraft.mooshroom_spawn_egg": "<PERSON>vo zenerador de mooshroom", "item.minecraft.mourner_pottery_shard": "Cocio con luto", "item.minecraft.mourner_pottery_sherd": "Cocio con luto", "item.minecraft.mule_spawn_egg": "<PERSON>vo zenerador de mulo", "item.minecraft.mushroom_stew": "Supa de fongi", "item.minecraft.music_disc_11": "Disco muzegal", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disco muzegal", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disco muzegal", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disco muzegal", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disco muzegal", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disco muzegal", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disco muzical", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disco muzical", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (carillon)", "item.minecraft.music_disc_far": "Disco muzegal", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disco muzegal", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disco muzegal", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disco muzegal", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disco muzegal", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disco muzical", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disco muzical", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disco muzegal", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disco muzegal", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disco muzegal", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disco muzegal", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Carne de piégora crùa", "item.minecraft.name_tag": "<PERSON><PERSON>", "item.minecraft.nautilus_shell": "Zgusa de nautilus", "item.minecraft.nether_brick": "<PERSON><PERSON><PERSON>", "item.minecraft.nether_star": "Steła de'l Nether", "item.minecraft.nether_wart": "<PERSON><PERSON>", "item.minecraft.netherite_axe": "Manara de Netherite", "item.minecraft.netherite_boots": "Stivài de Netherite", "item.minecraft.netherite_chestplate": "<PERSON><PERSON> de Netherite", "item.minecraft.netherite_helmet": "Çelada de Netherite", "item.minecraft.netherite_hoe": "Sapa de Netherite", "item.minecraft.netherite_ingot": "Lingoto de Netherite", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_pickaxe": "Picon de Netherite", "item.minecraft.netherite_scrap": "Sgresenda de Netherite", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON> de <PERSON>herite", "item.minecraft.netherite_sword": "Spada de Netherite", "item.minecraft.netherite_upgrade_smithing_template": "Schema de forgiatura", "item.minecraft.netherite_upgrade_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_boat": "Barca de ròvare", "item.minecraft.oak_chest_boat": "Barca de Rovare co Baul", "item.minecraft.ocelot_spawn_egg": "Ovo zenerador de gatopardo", "item.minecraft.ominous_bottle": "Botiglia infausta", "item.minecraft.ominous_trial_key": "Ciave de ła sfida infausta", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_dye": "Cołorante naransa", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "Cuadro", "item.minecraft.pale_oak_boat": "Barca de ròvare p<PERSON>lido", "item.minecraft.pale_oak_chest_boat": "Barca de ròvare pàlida co Baul", "item.minecraft.panda_spawn_egg": "Ovo zenerador de panda", "item.minecraft.paper": "Fojo", "item.minecraft.parrot_spawn_egg": "Ovo zenerador de papagal", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON>'l phantom", "item.minecraft.phantom_spawn_egg": "<PERSON>vo zenerador de phantom", "item.minecraft.pig_spawn_egg": "Ovo zenerador de porsel", "item.minecraft.piglin_banner_pattern": "Fegura par gonfałon", "item.minecraft.piglin_banner_pattern.desc": "Grugno", "item.minecraft.piglin_banner_pattern.new": "Figura par gonfalòn con muso de mas'cio", "item.minecraft.piglin_brute_spawn_egg": "<PERSON>vo zenerador de Piglin bruto", "item.minecraft.piglin_spawn_egg": "<PERSON>vo zenarador de Piglin", "item.minecraft.pillager_spawn_egg": "Ovo zenerador de sachejador", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON><PERSON> rosa", "item.minecraft.pink_dye": "Cołorante roza", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Pianta carnì<PERSON>a", "item.minecraft.pitcher_pod": "Coltura de pianta carnìvora", "item.minecraft.plenty_pottery_shard": "Cocio con schèi", "item.minecraft.plenty_pottery_sherd": "Cocio con schèi", "item.minecraft.poisonous_potato": "Patata vełenoza", "item.minecraft.polar_bear_spawn_egg": "Ovo zenerador de orso polar", "item.minecraft.popped_chorus_fruit": "Fruto de chorus sciopà", "item.minecraft.porkchop": "Brizoła de porsel crùa", "item.minecraft.potato": "Pat<PERSON>", "item.minecraft.potion": "Posion", "item.minecraft.potion.effect.awkward": "Posion stranba", "item.minecraft.potion.effect.empty": "Posion mìa fabricà<PERSON>łe", "item.minecraft.potion.effect.fire_resistance": "Posion de rezistensa a'l fogo", "item.minecraft.potion.effect.harming": "Posion de dano", "item.minecraft.potion.effect.healing": "Posion de cura", "item.minecraft.potion.effect.infested": "Posion de infestasiòn", "item.minecraft.potion.effect.invisibility": "Posion de l'invizibiłità", "item.minecraft.potion.effect.leaping": "Posion de salto", "item.minecraft.potion.effect.levitation": "Posion de łevitasion", "item.minecraft.potion.effect.luck": "Posion de fortuna", "item.minecraft.potion.effect.mundane": "Posion ordenaria", "item.minecraft.potion.effect.night_vision": "Posion de vista noturna", "item.minecraft.potion.effect.oozing": "Posion de trasudasiòn", "item.minecraft.potion.effect.poison": "Posion de vełen", "item.minecraft.potion.effect.regeneration": "Posion de rezenarasion", "item.minecraft.potion.effect.slow_falling": "Posion de cascada łenta", "item.minecraft.potion.effect.slowness": "Posion de łentesa", "item.minecraft.potion.effect.strength": "Posion de forsa", "item.minecraft.potion.effect.swiftness": "Posion de sveltesa", "item.minecraft.potion.effect.thick": "Posion fisa", "item.minecraft.potion.effect.turtle_master": "Posion de'l maestro tartaruga", "item.minecraft.potion.effect.water": "Anpoła de àcua", "item.minecraft.potion.effect.water_breathing": "Posion de respir. acuàtega", "item.minecraft.potion.effect.weakness": "Posion de fiaca", "item.minecraft.potion.effect.weaving": "Posion de tesidura", "item.minecraft.potion.effect.wind_charged": "Posion de carga ventosa", "item.minecraft.pottery_shard_archer": "Cocio co arco e frecia", "item.minecraft.pottery_shard_arms_up": "Cocio co alzada de brazi", "item.minecraft.pottery_shard_prize": "Cocio co premio", "item.minecraft.pottery_shard_skull": "Scheza con Tescio", "item.minecraft.powder_snow_bucket": "Secio de neve polvarosa", "item.minecraft.prismarine_crystals": "C<PERSON><PERSON> p<PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON> de prizmarin", "item.minecraft.prize_pottery_shard": "Scheza con Premio", "item.minecraft.prize_pottery_sherd": "Scheza con Premio", "item.minecraft.pufferfish": "Pese bała", "item.minecraft.pufferfish_bucket": "<PERSON>cio con pese bała", "item.minecraft.pufferfish_spawn_egg": "<PERSON>vo zenerador de pesse bala", "item.minecraft.pumpkin_pie": "Torta de suca", "item.minecraft.pumpkin_seeds": "Somensa de suca", "item.minecraft.purple_bundle": "<PERSON><PERSON><PERSON> viola", "item.minecraft.purple_dye": "Cołorante vioła", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "Cuarso del Nether", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> cr<PERSON>", "item.minecraft.rabbit_foot": "Sata de conejo", "item.minecraft.rabbit_hide": "Pel de conejo", "item.minecraft.rabbit_spawn_egg": "Ovo zenerador de conejo", "item.minecraft.rabbit_stew": "Pastisada de conejo", "item.minecraft.raiser_armor_trim_smithing_template": "Schema de forzadura", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "<PERSON>vo zenarador de devastador", "item.minecraft.raw_copper": "<PERSON><PERSON>", "item.minecraft.raw_gold": "Oro Grezo", "item.minecraft.raw_iron": "<PERSON><PERSON>", "item.minecraft.recovery_compass": "Bùsola de Ritrovo", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> rossa", "item.minecraft.red_dye": "Cołorante roso", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "<PERSON><PERSON><PERSON><PERSON> de Redstone", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON>", "item.minecraft.resin_clump": "Gropo de Rasa", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON> z<PERSON>", "item.minecraft.saddle": "Seła", "item.minecraft.salmon": "Salmon crùo", "item.minecraft.salmon_bucket": "<PERSON><PERSON> con salmon", "item.minecraft.salmon_spawn_egg": "Ovo zenerador de salmon", "item.minecraft.scrape_pottery_sherd": "Sc<PERSON>za con S<PERSON><PERSON>", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Schema de forzadura", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "Fòrbeze", "item.minecraft.sheep_spawn_egg": "Ovo zenerador de pégora", "item.minecraft.shelter_pottery_shard": "Shelter Pottery Shard", "item.minecraft.shelter_pottery_sherd": "<PERSON>lter Pottery Sherd", "item.minecraft.shield": "<PERSON><PERSON>", "item.minecraft.shield.black": "Scudo negro", "item.minecraft.shield.blue": "<PERSON><PERSON> blè", "item.minecraft.shield.brown": "<PERSON><PERSON> maron", "item.minecraft.shield.cyan": "<PERSON><PERSON> sian", "item.minecraft.shield.gray": "<PERSON>udo grizo", "item.minecraft.shield.green": "<PERSON><PERSON> verdo", "item.minecraft.shield.light_blue": "<PERSON><PERSON>", "item.minecraft.shield.light_gray": "<PERSON>udo grizo ciaro", "item.minecraft.shield.lime": "Scudo lime", "item.minecraft.shield.magenta": "<PERSON><PERSON> mazenta", "item.minecraft.shield.orange": "<PERSON><PERSON> naransa", "item.minecraft.shield.pink": "<PERSON><PERSON> roza", "item.minecraft.shield.purple": "<PERSON><PERSON> vioła", "item.minecraft.shield.red": "<PERSON><PERSON> roso", "item.minecraft.shield.white": "<PERSON><PERSON> bianco", "item.minecraft.shield.yellow": "<PERSON><PERSON> zało", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON>vo z<PERSON>ador de s<PERSON>ker", "item.minecraft.sign": "Cartel", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "<PERSON>vo zenerador de pesse arzentà", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON>vo zenerador de caval sch<PERSON>letro", "item.minecraft.skeleton_spawn_egg": "<PERSON>vo zenerador de schèletro", "item.minecraft.skull_banner_pattern": "Fegura par gonfałon", "item.minecraft.skull_banner_pattern.desc": "Stanpa de tescio", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Skull Pottery Shard", "item.minecraft.skull_pottery_sherd": "Skull Pottery Sherd", "item.minecraft.slime_ball": "Bała de zeładina", "item.minecraft.slime_spawn_egg": "Ovo zenerador de zeladina", "item.minecraft.smithing_template": "Schema de forzadura", "item.minecraft.smithing_template.applies_to": "El se àplega a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Zonta un lingoto o un cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Zonta un toco de armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingoti e cristài", "item.minecraft.smithing_template.ingredients": "Ingredienti:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Zonta un lingoto de netherite", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Echipajamento de brilante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Zonta n'armadura, n'arma o n'arte de diamante", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingoto de netherite", "item.minecraft.smithing_template.upgrade": "Mejora: ", "item.minecraft.sniffer_spawn_egg": "Ovo zenerador de snasador", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> Pottery Sherd", "item.minecraft.snout_armor_trim_smithing_template": "Schema de forzadura", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Ovo zenerador de Golem de Neve", "item.minecraft.snowball": "Bałoco", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> spetral", "item.minecraft.spider_eye": "<PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "Ovo zenerador de ragno", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "Posion da tiro", "item.minecraft.splash_potion.effect.awkward": "Posion stranba da tiro", "item.minecraft.splash_potion.effect.empty": "Posion da tiro mìa fabricàbiłe", "item.minecraft.splash_potion.effect.fire_resistance": "Posion da tiro de rezistensa a'l fogo", "item.minecraft.splash_potion.effect.harming": "Posion da tiro de dano", "item.minecraft.splash_potion.effect.healing": "Posion da tiro de cura", "item.minecraft.splash_potion.effect.infested": "Posion da tiro de infestasion", "item.minecraft.splash_potion.effect.invisibility": "Posion da tiro de l'invizibiłità", "item.minecraft.splash_potion.effect.leaping": "Posion da tiro de salto", "item.minecraft.splash_potion.effect.levitation": "Posion da tiro de łevitasion", "item.minecraft.splash_potion.effect.luck": "Posion da tiro de fortuna", "item.minecraft.splash_potion.effect.mundane": "Posion ordenaria da tiro", "item.minecraft.splash_potion.effect.night_vision": "Posion da tiro de vista noturna", "item.minecraft.splash_potion.effect.oozing": "Posion da tiro de secresion", "item.minecraft.splash_potion.effect.poison": "Posion da tiro de vełen", "item.minecraft.splash_potion.effect.regeneration": "Posion da tiro de rezenarasion", "item.minecraft.splash_potion.effect.slow_falling": "Posion da tiro de cascada łenta", "item.minecraft.splash_potion.effect.slowness": "Posion da tiro de łentesa", "item.minecraft.splash_potion.effect.strength": "Posion da tiro de forsa", "item.minecraft.splash_potion.effect.swiftness": "Posion da tiro de sveltesa", "item.minecraft.splash_potion.effect.thick": "Posion fisa da tiro", "item.minecraft.splash_potion.effect.turtle_master": "Posion da tiro de'l maestro tartaruga", "item.minecraft.splash_potion.effect.water": "Anpoła d'àcua da tiro", "item.minecraft.splash_potion.effect.water_breathing": "Posion da tiro de respir. acuàtega", "item.minecraft.splash_potion.effect.weakness": "Posion da tiro de fiaca", "item.minecraft.splash_potion.effect.weaving": "Posion da tiro de tessidura", "item.minecraft.splash_potion.effect.wind_charged": "Posion da tiro de carga de vento", "item.minecraft.spruce_boat": "Barca de avede", "item.minecraft.spruce_chest_boat": "Barca de Avede co Baul", "item.minecraft.spyglass": "Canocial", "item.minecraft.squid_spawn_egg": "Ovo zenerador de calamaro", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "Manara de piera", "item.minecraft.stone_hoe": "Sapa de piera", "item.minecraft.stone_pickaxe": "Picon de piera", "item.minecraft.stone_shovel": "Baìl de <PERSON>", "item.minecraft.stone_sword": "Spada de piera", "item.minecraft.stray_spawn_egg": "Ovo zenerador de viandante", "item.minecraft.strider_spawn_egg": "Ovo zenerador de caminador", "item.minecraft.string": "Soga", "item.minecraft.sugar": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON>pa sospeta", "item.minecraft.sweet_berries": "Pomełe dolse", "item.minecraft.tadpole_bucket": "<PERSON><PERSON> con <PERSON>", "item.minecraft.tadpole_spawn_egg": "<PERSON>vo zenerador de Girin", "item.minecraft.tide_armor_trim_smithing_template": "Schema de forzadura", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON> in<PERSON>a", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON> in<PERSON>a", "item.minecraft.tipped_arrow.effect.empty": "Fresa inbevùa mìa <PERSON>e", "item.minecraft.tipped_arrow.effect.fire_resistance": "Fresa de rezistensa a'l fogo", "item.minecraft.tipped_arrow.effect.harming": "Fresa <PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Fresa de cura", "item.minecraft.tipped_arrow.effect.infested": "Fresa de infestasiòn", "item.minecraft.tipped_arrow.effect.invisibility": "Fresa de invizibiłidà", "item.minecraft.tipped_arrow.effect.leaping": "Fresa de salto", "item.minecraft.tipped_arrow.effect.levitation": "Fresa de łevitasion", "item.minecraft.tipped_arrow.effect.luck": "Fresa de fortuna", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON> in<PERSON>a", "item.minecraft.tipped_arrow.effect.night_vision": "Fresa de vizion noturna", "item.minecraft.tipped_arrow.effect.oozing": "Fresa de secresion", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "Fresa de rezenarasion", "item.minecraft.tipped_arrow.effect.slow_falling": "Fresa de cascada łenta", "item.minecraft.tipped_arrow.effect.slowness": "Fresa de łentesa", "item.minecraft.tipped_arrow.effect.strength": "Fresa de forsa", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON> in<PERSON>a", "item.minecraft.tipped_arrow.effect.turtle_master": "Fresa de'l maestro tarta<PERSON>a", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON> <PERSON><PERSON>l schisetar", "item.minecraft.tipped_arrow.effect.water_breathing": "Fresa de respir. acuàtega", "item.minecraft.tipped_arrow.effect.weakness": "Fresa de fiaca", "item.minecraft.tipped_arrow.effect.weaving": "Fresa de tessidura", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON><PERSON> de carga ventosa", "item.minecraft.tnt_minecart": "Carel co' TNT", "item.minecraft.torchflower_seeds": "Somensa de luxoflora", "item.minecraft.totem_of_undying": "Totem de l'inmortalidà", "item.minecraft.trader_llama_spawn_egg": "Ovo zenerador de lama del marcante", "item.minecraft.trial_key": "Ciave de ła sfida", "item.minecraft.trident": "Forca", "item.minecraft.tropical_fish": "Pese tropigal", "item.minecraft.tropical_fish_bucket": "Secio con pese tropical", "item.minecraft.tropical_fish_spawn_egg": "Ovo zenerador de pesse tropigal", "item.minecraft.turtle_helmet": "Zgusa de tartaruga", "item.minecraft.turtle_scute": "Scuto de tortùga", "item.minecraft.turtle_spawn_egg": "Ovo zenerador de tartaruga", "item.minecraft.vex_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.vex_armor_trim_smithing_template.new": "Ornamento d'armadura Vex", "item.minecraft.vex_spawn_egg": "Ovo zenerador de vex", "item.minecraft.villager_spawn_egg": "<PERSON>vo zenerador de paesan", "item.minecraft.vindicator_spawn_egg": "<PERSON>vo zenarador de vendegador", "item.minecraft.wandering_trader_spawn_egg": "Ovo zenarador de marcante anbułante", "item.minecraft.ward_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.ward_armor_trim_smithing_template.new": "Ornamento d'armadura Sorvejante", "item.minecraft.warden_spawn_egg": "Ovo zenerador de Sorvejante", "item.minecraft.warped_fungus_on_a_stick": "Fongo strùpio su bachéto", "item.minecraft.water_bucket": "<PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ornamento d'armadura Ricognidore", "item.minecraft.wheat": "Formento", "item.minecraft.wheat_seeds": "Somensa de formento", "item.minecraft.white_bundle": "Sach<PERSON><PERSON> bianco", "item.minecraft.white_dye": "Cołorante bianco", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "Schema de forgiatura", "item.minecraft.wild_armor_trim_smithing_template.new": "Ornamento d'armadura Selvàdego", "item.minecraft.wind_charge": "Carga de vento", "item.minecraft.witch_spawn_egg": "<PERSON>vo zena<PERSON>r de str<PERSON>a", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON> z<PERSON> de schèłetro wither", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON> z<PERSON>", "item.minecraft.wolf_armor": "Armadura par lovo", "item.minecraft.wolf_spawn_egg": "Ovo zenarador de łovo", "item.minecraft.wooden_axe": "Manara de łenjo", "item.minecraft.wooden_hoe": "<PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "Spada de łenjo", "item.minecraft.writable_book": "Łibro e pena", "item.minecraft.written_book": "<PERSON><PERSON><PERSON> scrive<PERSON>", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON><PERSON> za<PERSON>o", "item.minecraft.yellow_dye": "Cołorante zało", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Ovo zenerador de Zoglin", "item.minecraft.zombie_horse_spawn_egg": "Ovo zenarador de caval zonbi", "item.minecraft.zombie_spawn_egg": "Ovo zenarador de zonbi", "item.minecraft.zombie_villager_spawn_egg": "<PERSON>vo zenerador de paesan zonbi", "item.minecraft.zombified_piglin_spawn_egg": "Ovo zenarador de piglin zonbifegà", "item.modifiers.any": "Cuando intorno:", "item.modifiers.armor": "Cuando indoso:", "item.modifiers.body": "Cuando intorno:", "item.modifiers.chest": "Sol peto:", "item.modifiers.feet": "So i piè:", "item.modifiers.hand": "Cuando in man:", "item.modifiers.head": "In te ła suca:", "item.modifiers.legs": "Su łe ganbe:", "item.modifiers.mainhand": "In te ła man bòna:", "item.modifiers.offhand": "In te ła man mauca:", "item.modifiers.saddle": "Se inselà:", "item.nbt_tags": "NBT: %s tag", "item.op_block_warning.line1": "Atension:", "item.op_block_warning.line2": "Sto ogeto podrìa eseguìr comandi", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON><PERSON> solo se te se a cosa el serve!", "item.unbreakable": "Indestrutìbiłe", "itemGroup.buildingBlocks": "<PERSON><PERSON> da <PERSON>rus<PERSON>", "itemGroup.coloredBlocks": "Blochi coloradi", "itemGroup.combat": "Conbatimento", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Fabricasion", "itemGroup.foodAndDrink": "Ma<PERSON><PERSON> e bevare", "itemGroup.functional": "Blochi funzionali", "itemGroup.hotbar": "<PERSON><PERSON> r<PERSON><PERSON>e salvade", "itemGroup.ingredients": "Ingredienti", "itemGroup.inventory": "Invetario de soravivensa", "itemGroup.natural": "Blochi naturali", "itemGroup.op": "Utilità pa 'i operatori", "itemGroup.redstone": "<PERSON><PERSON> de Redstone", "itemGroup.search": "Serca ozeti", "itemGroup.spawnEggs": "<PERSON><PERSON>", "itemGroup.tools": "Atresi e utilità", "item_modifier.unknown": "Modifegator de ozeti mìa conosesto: %s", "jigsaw_block.final_state": "Volta in:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "In bola", "jigsaw_block.joint.rollable": "Rodolabiłe", "jigsaw_block.joint_label": "Tipo de zonta:", "jigsaw_block.keep_jigsaws": "Mantien puzzle", "jigsaw_block.levels": "Livèi: %s", "jigsaw_block.name": "Nome:", "jigsaw_block.placement_priority": "Priorità de piasamento:", "jigsaw_block.placement_priority.tooltip": "Cuando sto bloco puzzle se taca a on toco, cuesto sarà l'ordine in cui el toco l'è elaborà par le conesiòn a ła strutura conplesiva.\nI tochi vegnarà elaborà in ordine de priorità decresente, 'ndando drìo l'ordine de inserimento in caso de parità.", "jigsaw_block.pool": "S-ciapo de destinaxion:", "jigsaw_block.selection_priority": "Priorità de selesiòn:", "jigsaw_block.selection_priority.tooltip": "Cuando el toco asocià vien elaborà par le conesiòn, cuesto sarà l'ordine in cui sto bloco puzzle provarà a conetarse al so toco de destinasiòn.\nI tochi vegnarà elaborà in ordine de priorità decresente, o casualmente in caso de parità.", "jigsaw_block.target": "<PERSON><PERSON> bersajo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (carillon)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Progresi", "key.attack": "Ataca/destruzi", "key.back": "Camina indrio", "key.categories.creative": "Modałità creativa", "key.categories.gameplay": "Asion <PERSON>", "key.categories.inventory": "Inventario", "key.categories.misc": "<PERSON><PERSON><PERSON>", "key.categories.movement": "Mo<PERSON><PERSON>", "key.categories.multiplayer": "Multizogador", "key.categories.ui": "Interfasa de zugo", "key.chat": "<PERSON><PERSON><PERSON> ła chat", "key.command": "Inserissi comando", "key.drop": "Moła ozeto sełesionà", "key.forward": "<PERSON><PERSON> in<PERSON>i", "key.fullscreen": "Modałità a schermo intiero", "key.hotbar.1": "Slot de sełesion ràpida 1", "key.hotbar.2": "Slot de sełesion ràpida 2", "key.hotbar.3": "Slot de sełesion ràpida 3", "key.hotbar.4": "Slot de sełesion ràpida 4", "key.hotbar.5": "Slot de sełesion ràpida 5", "key.hotbar.6": "Slot de sełesion ràpida 6", "key.hotbar.7": "Slot de sełesion ràpida 7", "key.hotbar.8": "Slot de sełesion ràpida 8", "key.hotbar.9": "Slot de sełesion ràpida 9", "key.inventory": "Verzi/sera inventario", "key.jump": "Salta", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "<PERSON> ma<PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Scanceła", "key.keyboard.down": "Fresa zo", "key.keyboard.end": "Fin", "key.keyboard.enter": "Ìnvia", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Ins", "key.keyboard.keypad.0": "0 (Tn)", "key.keyboard.keypad.1": "1 (Tn)", "key.keyboard.keypad.2": "2 (Tn)", "key.keyboard.keypad.3": "3 (Tn)", "key.keyboard.keypad.4": "4 (Tn)", "key.keyboard.keypad.5": "5 (Tn)", "key.keyboard.keypad.6": "6 (Tn)", "key.keyboard.keypad.7": "7 (Tn)", "key.keyboard.keypad.8": "8 (Tn)", "key.keyboard.keypad.9": "9 (Tn)", "key.keyboard.keypad.add": "+ (Tn)", "key.keyboard.keypad.decimal": ". (Tn)", "key.keyboard.keypad.divide": "/ (Tn)", "key.keyboard.keypad.enter": "Ìnvia (Tn)", "key.keyboard.keypad.equal": "= (Tn)", "key.keyboard.keypad.multiply": "* (Tn)", "key.keyboard.keypad.subtract": "- (Tn)", "key.keyboard.left": "Fresa a sanca", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl a sanca", "key.keyboard.left.shift": "Shift a sanca", "key.keyboard.left.win": "Windows a sanca", "key.keyboard.menu": "<PERSON>ù", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Bloc num", "key.keyboard.page.down": "Pag zo", "key.keyboard.page.up": "<PERSON>g sù", "key.keyboard.pause": "<PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "Stamp", "key.keyboard.right": "Fresa a destra", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl a destra", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.right.win": "Windows a destra", "key.keyboard.scroll.lock": "Bloc scorr", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Bara spas.", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON><PERSON>", "key.keyboard.up": "Fresa su", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "Camina a sx", "key.loadToolbarActivator": "Carga na bara ràpida", "key.mouse": "Boton %1$s", "key.mouse.left": "Boton a sanca", "key.mouse.middle": "<PERSON><PERSON> sentral", "key.mouse.right": "<PERSON><PERSON> a destra", "key.pickItem": "Sernisi un bloco", "key.playerlist": "Ełenca i zogadori", "key.quickActions": "Quick Actions", "key.right": "Camina a dx", "key.saveToolbarActivator": "Salva na bara ràpida", "key.screenshot": "<PERSON>va una schermada", "key.smoothCamera": "Vizual sinemàtega", "key.sneak": "Cùcete", "key.socialInteractions": "Interaxion sociałe", "key.spectatorOutlines": "Evidensia i zugadori (spetadori)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Scanbia i ojeti inte la man secondaria", "key.togglePerspective": "Canbia ła modałità de vizual", "key.use": "Dòpara un ozeto/pozisiona bloco", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Linee guida de ła community", "known_server_link.feedback": "Riscontro", "known_server_link.forums": "Forum", "known_server_link.news": "Nove", "known_server_link.report_bug": "<PERSON><PERSON><PERSON> on bug de 'ł server", "known_server_link.status": "Stato", "known_server_link.support": "Supporto", "known_server_link.website": "<PERSON><PERSON>", "lanServer.otherPlayers": "Inpostasion par i altri zu<PERSON>ri", "lanServer.port": "Numero dea porta", "lanServer.port.invalid": "Porta no valida.\nLasa el ricuadro vodo o meti on numaro tra 1024 e 65535.", "lanServer.port.invalid.new": "Porta no valida.\nLasa el ricuadro vodo o meti on numaro tra %s e %s.", "lanServer.port.unavailable": "Porta no disponibiłe.\nLasa el ricuadro vodo o meti on numaro diverso tra 1024 e 65535.", "lanServer.port.unavailable.new": "Porta no disponibiłe.\nLasa el ricuadro vodo o meti on numaro diverso tra %s e %s.", "lanServer.scanning": "In serca de partìe inte ła rete łocal", "lanServer.start": "<PERSON><PERSON><PERSON> mondo in LAN", "lanServer.title": "Mondo in LAN", "language.code": "vec_IT", "language.name": "Vèneto", "language.region": "Veneto", "lectern.take_book": "Tołi el łibro", "loading.progress": "%s%%", "mco.account.privacy.info": "Descoverzi de pì su Mojang e łe łeze su ła privacy", "mco.account.privacy.info.button": "Lezi de pì revardo GDPR", "mco.account.privacy.information": "Ła Mojang ła inplementa serte procedure par jutar protèjar i minori e ła so privacy, tra łe cual ła Children Online Privacy Protection Act (COPPA) e el regołamento zenaral su ła protesion de i dati (GDPR).\n\nTe podaresi necesidar de'l consenso dei tui par acédar a'l to account Realms.", "mco.account.privacyinfo": "Ła Mojang ła inplementa serte procedure par jutar protèjar i minori e ła so privacy, tra łe cual ła Children Online Privacy Protection Act (COPPA) e el regołamento zenaral su ła protesion de i dati (RGPD).\n\nTe podaresi necesidar de'l consenso dei tui par acédar a'l to account Realms.\n\nSe te gh'è un vecio account de Minecraft (acedi co'l to nome utente), te dovarè ezeguir ła migrasion a un account Mojang par poder doparar Realms.", "mco.account.update": "Azorna el account", "mco.activity.noactivity": "Nisuna atività inte i ùltemi %s dì", "mco.activity.title": "Atività del zugador", "mco.backup.button.download": "Descarga l'últemo", "mco.backup.button.reset": "Reseta el mondo", "mco.backup.button.restore": "Reprìstina", "mco.backup.button.upload": "Carga el mondo", "mco.backup.changes.tooltip": "Mod<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Descrisión", "mco.backup.entry.enabledPack": "Pachetto ativo", "mco.backup.entry.gameDifficulty": "Dificoltà del zugo", "mco.backup.entry.gameMode": "Modałità", "mco.backup.entry.gameServerVersion": "Version del server", "mco.backup.entry.name": "Nome", "mco.backup.entry.seed": "Somensa", "mco.backup.entry.templateName": "Nome del modeło", "mco.backup.entry.undefined": "Modìfega no definìa", "mco.backup.entry.uploaded": "Cargà", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON> de mondo", "mco.backup.generate.world": "Zénara el mondo", "mco.backup.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'<PERSON> ultimo backup", "mco.backup.narration": "Backup de'ł %s", "mco.backup.nobackups": "Par el momento 'sto realm el posiede mìa backup.", "mco.backup.restoring": "<PERSON><PERSON> repristinar el to realm", "mco.backup.unknown": "SCONOSUO", "mco.brokenworld.download": "Descar<PERSON>", "mco.brokenworld.downloaded": "Descargà", "mco.brokenworld.message.line1": "Rezeta o sełesiona un antro mondo.", "mco.brokenworld.message.line2": "Te pol anca descargar el mondo in zugador sìngoło.", "mco.brokenworld.minigame.title": "Sto minizugo l'è pì suportà", "mco.brokenworld.nonowner.error": "Speta par piazer che el paron de'l realm el rezénara el mondo", "mco.brokenworld.nonowner.title": "El mondo l'è mia ajornà", "mco.brokenworld.play": "Zuga", "mco.brokenworld.reset": "<PERSON><PERSON><PERSON>", "mco.brokenworld.title": "El to mondo atual l'é suportà altro", "mco.client.incompatible.msg.line1": "El to client l'é mia conpatìbiłe co Realms.", "mco.client.incompatible.msg.line2": "Parpiazer dòpara ła últema varsion de Minecraft.", "mco.client.incompatible.msg.line3": "Realms l'é mia conpatìbiłe co łe varsion snapshot.", "mco.client.incompatible.title": "Client mìa conpatìbiłe!", "mco.client.outdated.stable.version": "La version del client (%s) no zè conpatibiłe con Realms.\nDopra la versiòn pì resénte de Minecraft.", "mco.client.unsupported.snapshot.version": "La version del client (%s) no zè conpatibiłe con Realms.\nRealms no zè disponibiłe par sta versiòn sperimentałe.", "mco.compatibility.downgrade": "<PERSON><PERSON> in'te na versiòn presedente", "mco.compatibility.downgrade.description": "'Sto mondo l'é stà cargà l'ùltema volta in ła version %s, ti te si in version %s. <PERSON><PERSON><PERSON> in'te na version presedente podrìa coromperlo: no podemo garantir che el so caricamento o el so funsionamento el sia coreto.\nNa copia del to mondo vegnarà salvà in \"Backup\". Doprala par el ripristino se nesesario.", "mco.compatibility.incompatible.popup.title": "Version inconpatìbile", "mco.compatibility.incompatible.releaseType.popup.message": "El mondo in cui te sté sercando de zontarte no zè conpatibile con la version che te sté doparando.", "mco.compatibility.incompatible.series.popup.message": "L'ultema volta sto mondo zè stà cargà in'te ła version %s; ti te sì in'te ła version %s.\nLe do version no zè conpatibiłe fra de łore. Par zugar a sta version, zè nesesario on novo mondo.", "mco.compatibility.unverifiable.message": "Inposibiłe verifegar ła version in'te la cuałe el mondo zè stà cargà l'ultema volta. Se 'ł sarà cargà in'te na version del zugo sucesiva o presedente, ghe ne vegnarà creà e salvà na copia automaticamente in \"Backup\".", "mco.compatibility.unverifiable.title": "Conpatibiłità no verifegabiłe", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "L'ultema volta sto mondo z<PERSON> stà carg<PERSON> in'te ła version %s; ti te sì in'te ła version %s.\nOn backup de'ł to mondo sar<PERSON> salv<PERSON> in \"Backup\". Doprala pa'ł ripristino se nesesario.", "mco.compatibility.upgrade.friend.description": "L'ultema volta sto mondo zè stà cargà in'te ła version %s; ti te sì in'te ła version %s.\nOn backup de'ł to mondo sar<PERSON> salvà in \"Backup\". El paròn del Realm podarà ripristinare el mondo se nesesario.", "mco.compatibility.upgrade.title": "<PERSON><PERSON> des<PERSON> mejorar 'sto mondo?", "mco.configure.current.minigame": "Atual", "mco.configure.world.activityfeed.disabled": "El feed de'l zugador l'è stà dezativà par el momento", "mco.configure.world.backup": "Backup", "mco.configure.world.buttons.activity": "Atività dei zugadori", "mco.configure.world.buttons.close": "Sara <PERSON> realm", "mco.configure.world.buttons.delete": "Ełìmen<PERSON>", "mco.configure.world.buttons.done": "Finio", "mco.configure.world.buttons.edit": "Inpostasion", "mco.configure.world.buttons.invite": "Invita un zugador", "mco.configure.world.buttons.moreoptions": "Antre opsion", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Verzi el <PERSON>", "mco.configure.world.buttons.options": "Opsion del mondo", "mco.configure.world.buttons.players": "Zugadori", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Rezeta el mondo", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Inpostasion", "mco.configure.world.buttons.subscription": "Bonamento", "mco.configure.world.buttons.switchminigame": "Canbia minizugo", "mco.configure.world.close.question.line1": "El to realm no'l sarà pì desponìbiłe.", "mco.configure.world.close.question.line2": "Sito seguro de vołer continuar?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Drio sarar el realm...", "mco.configure.world.commandBlocks": "<PERSON><PERSON>", "mco.configure.world.delete.button": "Scanceła el realm", "mco.configure.world.delete.question.line1": "El to realm el sarà ełimenà parmanentemente", "mco.configure.world.delete.question.line2": "Sito seguro de vołer continuar?", "mco.configure.world.description": "Descrision de'l realm", "mco.configure.world.edit.slot.name": "Nome de'l Mondo", "mco.configure.world.edit.subscreen.adventuremap": "Serte inpostasion i'é dezabiłitade parché el to mondo atual l'é n'aventura", "mco.configure.world.edit.subscreen.experience": "Serte inpostasion i'é dezabiłitade parché el mondo atual l'é n'espariensa", "mco.configure.world.edit.subscreen.inspiration": "Serte inpostasion łe xe dezativade parché el mondo atual l'é na ispirasion", "mco.configure.world.forceGameMode": "Forsa modałità de zugo", "mco.configure.world.invite.narration": "Te ghè %s novo/i invido/i", "mco.configure.world.invite.profile.name": "Nome", "mco.configure.world.invited": "Invitadi", "mco.configure.world.invited.number": "Invità (%s)", "mco.configure.world.invites.normal.tooltip": "Utente normal", "mco.configure.world.invites.ops.tooltip": "Operador", "mco.configure.world.invites.remove.tooltip": "Cava", "mco.configure.world.leave.question.line1": "Se te bandoni sto realm, te'l vedarè altro finché no te venjarè reinvità", "mco.configure.world.leave.question.line2": "Sito seguro de vołer continuar?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Atual: %s", "mco.configure.world.name": "Nome de'l realm", "mco.configure.world.opening": "Drio vèrzar el realm...", "mco.configure.world.players.error": "Un zugador co'l nome dizità no'l esiste mìa", "mco.configure.world.players.inviting": "Invio de'ł invido...", "mco.configure.world.players.title": "Zogador<PERSON>", "mco.configure.world.pvp": "PvP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "El to mondo el sarà rezenarà e'l to mondo atual el ndarà perso", "mco.configure.world.reset.question.line2": "Sito seguro de vołer continuar?", "mco.configure.world.resourcepack.question": "Sto Realm desmanda on pacheto de risorse personalizà.\n<PERSON><PERSON> des<PERSON> par zugar?", "mco.configure.world.resourcepack.question.line1": "Sto realm el dimanda un pacheto de resorse parsonałizà.", "mco.configure.world.resourcepack.question.line2": "<PERSON><PERSON> des<PERSON> par zugar?", "mco.configure.world.restore.download.question.line1": "El mondo el venjarà descargà e zontà a ła łista dei mondi in zugador sìngoło.", "mco.configure.world.restore.download.question.line2": "<PERSON><PERSON> continuar?", "mco.configure.world.restore.question.line1": "El to mondo el venjarà repristinà a ła data \"%s\" (%s)", "mco.configure.world.restore.question.line2": "Sito seguro de vołer continuar?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Inpostasion", "mco.configure.world.slot": "Mondo %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "El to realm el sarà canbià co' un antro mondo", "mco.configure.world.slot.switch.question.line2": "Sito seguro de vołer continuar?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON> mondo", "mco.configure.world.slot.tooltip.active": "Intra", "mco.configure.world.slot.tooltip.minigame": "Canbia in minizugo", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Zènara NPCs", "mco.configure.world.spawnProtection": "Protesion de ła zona inisial", "mco.configure.world.spawn_toggle.message": "Desativando sta opsiòn vegnarà cavà tute le entidà de col tipo", "mco.configure.world.spawn_toggle.message.npc": "Desativando sta opsiòn vegnarà cavà tute le entidà de col tipo, tipo i viłani", "mco.configure.world.spawn_toggle.title": "Atension!", "mco.configure.world.status": "Stato", "mco.configure.world.subscription.day": "Dì", "mco.configure.world.subscription.days": "Dì", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON> bona<PERSON>", "mco.configure.world.subscription.less_than_a_day": "Manco de un dì", "mco.configure.world.subscription.month": "mese", "mco.configure.world.subscription.months": "mesi", "mco.configure.world.subscription.recurring.daysleft": "Renovà automategamente intra", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s dì", "mco.configure.world.subscription.remaining.months": "%1$s mese/i", "mco.configure.world.subscription.remaining.months.days": "%1$s mese/i, %2$s dì", "mco.configure.world.subscription.start": "Data de prinsipio", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON>", "mco.configure.world.subscription.title": "Informasion del bonamento", "mco.configure.world.subscription.unknown": "No conossùo", "mco.configure.world.switch.slot": "Crèa el mondo", "mco.configure.world.switch.slot.subtitle": "<PERSON>o mondo l'é vudo, sern<PERSON> come crearlo", "mco.configure.world.title": "Configura el realm:", "mco.configure.world.uninvite.player": "Sito seguro de vołer desinvitar '%s'?", "mco.configure.world.uninvite.question": "Sito seguro de vołer desinvitar", "mco.configure.worlds.title": "<PERSON><PERSON>", "mco.connect.authorizing": "Drio a<PERSON>dar...", "mco.connect.connecting": "Conesion a el realm...", "mco.connect.failed": "Inposìbiłe conétarse a el realm", "mco.connect.region": "Server region: %s", "mco.connect.success": "<PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Te gh'è da scrìvar un nome!", "mco.create.world.failed": "Inposìbile creàr el mondo!", "mco.create.world.reset.title": "Drio crear el mondo...", "mco.create.world.skip": "Salta", "mco.create.world.subtitle": "Se te vol, sełesiona cual mondo métar inte'l to novo realm", "mco.create.world.wait": "Creasion del realm...", "mco.download.cancelled": "Download revocà", "mco.download.confirmation.line1": "Ła mapa che te stè par descargar l'é piasè granda de %s", "mco.download.confirmation.line2": "No te podarè cargar altro 'sto mondo inte'l to realm", "mco.download.confirmation.oversized": "El mondo che te ste par scargàr l'è pì grande de %s\nNo te riusirè a cargàr sto mondo 'nte 'ł to Realm", "mco.download.done": "Download conpletà", "mco.download.downloading": "<PERSON><PERSON>", "mco.download.extracting": "<PERSON><PERSON>", "mco.download.failed": "Download falìo", "mco.download.percent": "%s %%", "mco.download.preparing": "Drio pareciar el download", "mco.download.resourcePack.fail": "Inposibiłe scargàr el pacheto de risorse!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Drio des<PERSON>gar l'últema varsion", "mco.error.invalid.session.message": "Par piazer prova reinviar Minecraft", "mco.error.invalid.session.title": "Sesion mia vàłida", "mco.errorMessage.6001": "Client mia ajorn<PERSON>", "mco.errorMessage.6002": "Condision de servisio mìa acetade", "mco.errorMessage.6003": "Razonto łìmite de download", "mco.errorMessage.6004": "Razonto łì<PERSON> de upload", "mco.errorMessage.6005": "<PERSON><PERSON> b<PERSON>", "mco.errorMessage.6006": "El mondo l'è mia ajornà", "mco.errorMessage.6007": "Utente in màsa Realm", "mco.errorMessage.6008": "Nome de'ł Realm no l'è vałido", "mco.errorMessage.6009": "Descrisiòn del Realm non ł'è vałida", "mco.errorMessage.connectionFailure": "Se ga verifegà un eror, parpiazer prova da novo piasè tardi.", "mco.errorMessage.generic": "<PERSON><PERSON>'é stà un eror: ", "mco.errorMessage.initialize.failed": "Inisialisasiòn de'ł Realm non riusìa", "mco.errorMessage.noDetails": "No zé stà forn<PERSON>i detaji so'ł erore", "mco.errorMessage.realmsService": "Gh'é stà un eror (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Inposìbile conétarse a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Prova da novo l'operasion", "mco.errorMessage.serviceBusy": "Realms se occupà adeso.\nPer piasser ri-tanta la conesìon a to Realm tra cualche minuto.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Invidà el zugador %s", "mco.invites.button.accept": "Aceta", "mco.invites.button.reject": "Refuda", "mco.invites.nopending": "Nisun invido en sospezo!", "mco.invites.pending": "Novo/i invito/i!", "mco.invites.title": "Invidi en sospezo", "mco.minigame.world.changeButton": "Sełesiona un antro minizugo", "mco.minigame.world.info.line1": "Cuesto el sostituirà par el momento el to mondo co' un minizogo!", "mco.minigame.world.info.line2": "Te podarè tornar in sèvito a'l to mondo orizenal sensa pèrdar njente.", "mco.minigame.world.noSelection": "Sełesiona un minizugo", "mco.minigame.world.restore": "Drio termenar el minizugo...", "mco.minigame.world.restore.question.line1": "El minizugo el termenarà e'l to realm el venjarà reprìstinà.", "mco.minigame.world.restore.question.line2": "Sito seguro de vołer continuar?", "mco.minigame.world.selected": "Minizugo sełesionà:", "mco.minigame.world.slot.screen.title": "Drio canbiar el mondo...", "mco.minigame.world.startButton": "Canbia", "mco.minigame.world.starting.screen.title": "Ìnvio de'l minizugo...", "mco.minigame.world.stopButton": "Tèrmena el minizugo", "mco.minigame.world.switch.new": "<PERSON><PERSON> se<PERSON>r un antro minizugo?", "mco.minigame.world.switch.title": "Canbia minizogo", "mco.minigame.world.title": "Canbia el realm int' un minizugo", "mco.news": "Nove su Realms", "mco.notification.dismiss": "Refuda", "mco.notification.transferSubscription.buttonText": "Trasfer<PERSON>i desso", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Verzi link", "mco.notification.visitUrl.message.default": "Par piaser vìsita el link soto", "mco.onlinePlayers": "Zugadori online", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Aventure", "mco.reset.world.experience": "Espariense", "mco.reset.world.generate": "<PERSON><PERSON>", "mco.reset.world.inspiration": "Ispirasion", "mco.reset.world.resetting.screen.title": "Drio rezetar el mondo...", "mco.reset.world.seed": "Seme (opsional)", "mco.reset.world.template": "<PERSON><PERSON><PERSON>", "mco.reset.world.title": "Reseta el mondo", "mco.reset.world.upload": "Carga un mondo", "mco.reset.world.warning": "Cuesto el sostituirà el mondo atual de'l to realm", "mco.selectServer.buy": "Cronpa un realm!", "mco.selectServer.close": "<PERSON>", "mco.selectServer.closed": "Realm sarà", "mco.selectServer.closeserver": "Sara <PERSON> realm", "mco.selectServer.configure": "Configura el realm", "mco.selectServer.configureRealm": "Configura el realm", "mco.selectServer.create": "Crèa un realm", "mco.selectServer.create.subtitle": "Selesiona che mondo mètar sul to novo realm", "mco.selectServer.expired": "Realm scadùo", "mco.selectServer.expiredList": "El bonamento el xe scadùo", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Bònete", "mco.selectServer.expiredTrial": "Ła to prova l'é termenada", "mco.selectServer.expires.day": "El scadrà intra un dì", "mco.selectServer.expires.days": "El scadrà intra %s dì", "mco.selectServer.expires.soon": "El scadrà bonora", "mco.selectServer.leave": "Bandona el realm", "mco.selectServer.loading": "Drio cargar lista dei Realm", "mco.selectServer.mapOnlySupportedForVersion": "Mapa mia suportada inte ła %s", "mco.selectServer.minigame": "Minizugo:", "mco.selectServer.minigameName": "Minizogo: %s", "mco.selectServer.minigameNotSupportedInVersion": "Minizugo mia suportà inte ła %s", "mco.selectServer.noRealms": "Par che no te g'abi Realm. Zonta a Realm par zugar insieme coi to amighi.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Realm verto", "mco.selectServer.openserver": "Verzi el <PERSON>", "mco.selectServer.play": "Zuga", "mco.selectServer.popup": "Realm l'é un modo fàsiłe e seguro par gòdarse online co un màsimo de dieze amighi par volta. Gh'é un mar de minizughi e mondi parsonałizadi! Sol el paron de'l realm el ga da pagar.", "mco.selectServer.purchase": "<PERSON><PERSON><PERSON> re<PERSON>", "mco.selectServer.trial": "Próveło!", "mco.selectServer.uninitialized": "Fa clic par inviar el to novo realm!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Crear un Realm sparimental?", "mco.snapshot.creating": "<PERSON>io crear un Realm sparimental...", "mco.snapshot.description": "Conpagnà co \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Te ghè da èssar in varsion %s par tor parte al Realm", "mco.snapshot.friendsRealm.upgrade": "%s i ga da azornar i so Realm prima che ti te possi zugar in 'sta varsion", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Dòpara la ùltema varsion de Minecraft par zugar in 'sto Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "I Realm i xe al momento disponìbili in Snapshot a partir da Snapshot 23w41a. Ogni bonamento ai Realm el xe dotà de un Realm Snapshot despartìo dal to normal Realm Java!", "mco.snapshotRealmsPopup.title": "I Realm i xe atualmente disponìbili in Snapshot", "mco.snapshotRealmsPopup.urlText": "Lexi de pì", "mco.template.button.publisher": "Autor", "mco.template.button.select": "Sełesiona", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "<PERSON> de'l mondo", "mco.template.info.tooltip": "<PERSON><PERSON> de'l autor", "mco.template.name": "Model", "mco.template.select.failure": "Inposìbiłe recuparar ła łista dei contenjudi de sta categorìa.\nPar piazer controła ła conesion Internet o prova da novo piasè tardi.", "mco.template.select.narrate.authors": "Autori: %s", "mco.template.select.narrate.version": "varsion %s", "mco.template.select.none": "Ops, par che 'sta categorìa de cotenjudi ła sipia al momento vuda.\nPar piazer controła da novo in sèvito, o se te si un creador de contegnudi, %s.", "mco.template.select.none.linkTitle": "consìdara de inviarne calcosa ti", "mco.template.title": "<PERSON>d<PERSON><PERSON> mondo", "mco.template.title.minigame": "Mini<PERSON>gh<PERSON>", "mco.template.trailer.tooltip": "Trailer de ła mapa", "mco.terms.buttons.agree": "Aceto", "mco.terms.buttons.disagree": "No aceto", "mco.terms.sentence.1": "<PERSON><PERSON> łe", "mco.terms.sentence.2": "condision de servisio de Minecraft Realms", "mco.terms.title": "Condision de sarvisio de Realms", "mco.time.daysAgo": "%1$s dì fa", "mco.time.hoursAgo": "%1$s ore fa", "mco.time.minutesAgo": "%1$s menuti fa", "mco.time.now": "desso", "mco.time.secondsAgo": "%1$s secondi fa", "mco.trial.message.line1": "<PERSON><PERSON> el to realm?", "mco.trial.message.line2": "Fa' click par major informasion!", "mco.upload.button.name": "Carga", "mco.upload.cancelled": "Cargamento revocà", "mco.upload.close.failure": "Inposìbiłe sarar el to realm, par piazer prova piasè tardi", "mco.upload.done": "Cargamento conpletà", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Cargamento falìo! (%s)", "mco.upload.failed.too_big.description": "El mondo selesionà l'é massa grando. La dimension màssima la xe %s.", "mco.upload.failed.too_big.title": "Mondo massa grando", "mco.upload.hardcore": "I mondi hardcore i pol mia èsar cargadi!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Drio pareciar el mondo", "mco.upload.select.world.none": "<PERSON><PERSON><PERSON> mondo in zugador sìngoło l'é stà catà!", "mco.upload.select.world.subtitle": "Par piazer sełesiona un mondo in zugador sìngoło par cargar", "mco.upload.select.world.title": "Carga el mondo", "mco.upload.size.failure.line1": "\"%s\" l'è masa grando!", "mco.upload.size.failure.line2": "El òcupa %s. Ła màsima demension consentida l'é de %s.", "mco.upload.uploading": "Drio cargar \"%s\"", "mco.upload.verifying": "Drio verifegar el mondo", "mco.version": "Varsion: %s", "mco.warning": "Atension!", "mco.worldSlot.minigame": "Minizugo", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Desconeti", "menu.feedback": "Riscontro...", "menu.feedback.title": "Riscontro", "menu.game": "<PERSON><PERSON>", "menu.modded": " (Modà)", "menu.multiplayer": "Multizugador", "menu.online": "Minecraft Realms", "menu.options": "Opsion...", "menu.paused": "Zugo in pausa", "menu.playdemo": "Zuga inte'l mondo de ła demo", "menu.playerReporting": "Reporting de i Zugadori", "menu.preparingSpawn": "Drio pareciar l'àrea de spawn: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "Sara el zugo", "menu.reportBugs": "Reporta un bug", "menu.resetdemo": "Rezeta el mondo de ła demo", "menu.returnToGame": "De volta a'l zugo", "menu.returnToMenu": "Salva e va de volta a'l menù prinsipal", "menu.savingChunks": "Drio salvar i <PERSON>", "menu.savingLevel": "Dr<PERSON> salvar el mondo", "menu.sendFeedback": "Manda opinion", "menu.server_links": "Link de'ł server...", "menu.server_links.title": "Link de'<PERSON> server", "menu.shareToLan": "Verzi a'l LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON>", "menu.working": "<PERSON><PERSON>...", "merchant.deprecated": "I paesani se refornisse fin a do volte al dì.", "merchant.level.1": "Prensipiante", "merchant.level.2": "Arlievo", "merchant.level.3": "Cuałifegà", "merchant.level.4": "Pràtego", "merchant.level.5": "Mestro", "merchant.title": "%s - %s", "merchant.trades": "Scanbi", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Schicia %1$s par Desmontare", "multiplayer.applyingPack": "Drio apligar el pacheto de resorse", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "I server de autentegasion i xe fora sarvisio. Prova piasè tardi!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Te si stà banà da sto server", "multiplayer.disconnect.banned.expiration": "\nEl to ban el sarà ełimenà su %s", "multiplayer.disconnect.banned.reason": "Te si stà banà dal sto server.\nRazon: %s", "multiplayer.disconnect.banned_ip.expiration": "\nEl to ban el sarà ełimenà su %s", "multiplayer.disconnect.banned_ip.reason": "El to inderiso IP l'é stà blocà da sto server.\nRazon: %s", "multiplayer.disconnect.chat_validation_failed": "Validasìon de mesaji chat falìa", "multiplayer.disconnect.duplicate_login": "Te ghe za efetuà el aceso da un'antra pozision", "multiplayer.disconnect.expired_public_key": "Ciave de profio publico el xe scaduo. Controla che tí relojo de'l sistema se sincronizà, e reinvia tí zugo.", "multiplayer.disconnect.flying": "Vołar no'l xe parmeso inte sto server", "multiplayer.disconnect.generic": "Desconeso", "multiplayer.disconnect.idling": "Te si stà inativo par masa tenpo!", "multiplayer.disconnect.illegal_characters": "Caràtari mia consentidi in chat", "multiplayer.disconnect.incompatible": "Client inconpatìbile! Par piaser dòpara %s", "multiplayer.disconnect.invalid_entity_attacked": "Se ga provà a tacar na entità mia vałida", "multiplayer.disconnect.invalid_packet": "El server l'ha invià un pacheto mìa vàlido", "multiplayer.disconnect.invalid_player_data": "Dati del zogador mìa vàlidi", "multiplayer.disconnect.invalid_player_movement": "Pacheto de movimento de'l zugador resevùo l'é mìa vàłido", "multiplayer.disconnect.invalid_public_key_signature": "Firma de la ciave pùblega del profil mìa vàlida.\nProva reinviar el zogo.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firma de la ciave pùblega del profil mìa vàlida.\nProva reinviar el zogo.", "multiplayer.disconnect.invalid_vehicle_movement": "Resevùo un pacheto de movimento de un veìgoło mìa vałido", "multiplayer.disconnect.ip_banned": "El to inderiso IP l'é stà blocà da sto server", "multiplayer.disconnect.kicked": "Mandà fora da un operador", "multiplayer.disconnect.missing_tags": "Serie de tag resevùa la resulta mìa conpleta. \nPar piaser contata un operador del server.", "multiplayer.disconnect.name_taken": "Chel nome el ze za tolto", "multiplayer.disconnect.not_whitelisted": "No te si inte ła whitelist de sto server!", "multiplayer.disconnect.out_of_order_chat": "Pacheto chat fora de posto risevuto. Gheto cambià el tenpo de el sistema?", "multiplayer.disconnect.outdated_client": "Client mìa conpatìbile! Dòpara la varsion %s", "multiplayer.disconnect.outdated_server": "Client inconpatìbile! Par piaser dòpara %s", "multiplayer.disconnect.server_full": "El server l'é pien!", "multiplayer.disconnect.server_shutdown": "Server sarà", "multiplayer.disconnect.slow_login": "Masa tenpo par el log in", "multiplayer.disconnect.too_many_pending_chats": "<PERSON><PERSON> me<PERSON><PERSON> de la chat in atèsa", "multiplayer.disconnect.transfers_disabled": "El server no toł trasferimenti", "multiplayer.disconnect.unexpected_query_response": "<PERSON><PERSON> parson<PERSON><PERSON><PERSON><PERSON> mìa previsti resevudi da'l client", "multiplayer.disconnect.unsigned_chat": "Resevùo pacheto chat cò firma invalida o màncante.", "multiplayer.disconnect.unverified_username": "Inposìbiłe verifegar el to nome utente!", "multiplayer.downloadingStats": "Drio recuparar statì<PERSON>ghe...", "multiplayer.downloadingTerrain": "Drio cargar el teren...", "multiplayer.lan.server_found": "Catà on novo server: %s", "multiplayer.message_not_delivered": "Inposìbiłe inviar el mesazo in chat, controła inte i log de'l server: %s", "multiplayer.player.joined": "%s el se ga zontà a ła partida", "multiplayer.player.joined.renamed": "%s (in precedensa %s) el se ga zontà a ła partida", "multiplayer.player.left": "%s l'a bandonà ła partìa", "multiplayer.player.list.hp": "%spv", "multiplayer.player.list.narration": "Zugadori in linea: %s", "multiplayer.requiredTexturePrompt.disconnect": "El server el domanda un pacheto de risorse personałixà", "multiplayer.requiredTexturePrompt.line1": "El server el dimanda de doparar un pacheto de resorse parsonalizade.", "multiplayer.requiredTexturePrompt.line2": "Refudar 'sto pacheto de resorse el te disconetarà dal server.", "multiplayer.socialInteractions.not_available": "Le interasion soçiali le xe disponìbili sol in mondi multizugador", "multiplayer.status.and_more": "... e antri %s...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON> anu<PERSON>", "multiplayer.status.cannot_connect": "Inposìbiłe conétarse al server", "multiplayer.status.cannot_resolve": "Resołusion nome host mia r<PERSON>a", "multiplayer.status.finished": "Termenà", "multiplayer.status.incompatible": "Varsion inconpatìbile!", "multiplayer.status.motd.narration": "<PERSON>jo <PERSON> dì: %s", "multiplayer.status.no_connection": "(njente conesion)", "multiplayer.status.old": "Mia <PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s miłisecondi", "multiplayer.status.pinging": "Ezecusion de'l ping...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "Zugadori online: %s so %s", "multiplayer.status.quitting": "<PERSON><PERSON>", "multiplayer.status.request_handled": "Dimanda de stato tolta in zestion", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Resevùo un stato mia dimandà", "multiplayer.status.version.narration": "Version de'ł server: %s", "multiplayer.stopSleeping": "Łévate su", "multiplayer.texturePrompt.failure.line1": "<PERSON><PERSON><PERSON><PERSON> thể áp dụng gói tài nguyên máy chủ", "multiplayer.texturePrompt.failure.line2": "Calsìase funsionalidà che la dimanda resorse parsonalizade la podarìa no funsionar come spetà", "multiplayer.texturePrompt.line1": "El server el conseja de doparar un pacheto de resorse parsonałizade.", "multiplayer.texturePrompt.line2": "<PERSON>uto des<PERSON>lo e instałarlo automàgicamente?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMessajo dal server:\n%s", "multiplayer.title": "Zuga in multizugador", "multiplayer.unsecureserver.toast": "Mesaji mandà inte sto server pol esar modifegà e la podaria mìa rifleter el mesajo orizenal", "multiplayer.unsecureserver.toast.title": "Mesaji chat mìa pol esar verifegà", "multiplayerWarning.check": "No sta' pì mostrar 'sta schermada", "multiplayerWarning.header": "Atension: zugo online de terse parte", "multiplayerWarning.message": "Atension: el zogo online l'é oferto da server de terxe parte che no i xe propietà, zestidi o supervisionadi da Mojang o da Microsoft. Durante el zogo online, te podarissi èsar esposto a mesaji in chat mìa moderadi o altri tipi de contegnudi zenaradi da utenti che i podarìa no èsar adati par tuti.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Boton: %s", "narration.button.usage.focused": "Struca Invio par ativar", "narration.button.usage.hovered": "Fa' clic sanca par ativar", "narration.checkbox": "Casela de selesion: %s", "narration.checkbox.usage.focused": "Struca Invio par alternar", "narration.checkbox.usage.hovered": "Fa' clic sanca par alternar", "narration.component_list.usage": "Struca Tab par passar al elemento sucessivo", "narration.cycle_button.usage.focused": "Struca Invio par canbiar a %s", "narration.cycle_button.usage.hovered": "Fa' clic sanca par canbiar a %s", "narration.edit_box": "Casela de modifega: %s", "narration.item": "Item: %s", "narration.recipe": "Reseta par %s", "narration.recipe.usage": "Fa' clic sanca par selesionar", "narration.recipe.usage.more": "Fa' clic destra par mostrar piassè resete", "narration.selection.usage": "Struca i tasti fressa su e zo par mòvarte fra i elementi", "narration.slider.usage.focused": "Struca su la tastera sanca o destra par canbiar el valor", "narration.slider.usage.hovered": "Strapega el regolador par canbiar de valor", "narration.suggestion": "Sełesionà suzerimento %s de %s: %s", "narration.suggestion.tooltip": "Sełesionà suzerimento %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Struca Tab par passar al sujerimento seguente", "narration.suggestion.usage.cycle.hidable": "Struca Tab par passar a n'antro sujerimento o Esc par bandonar i sujerimenti", "narration.suggestion.usage.fill.fixed": "Struca Tab par doparar el sujerimento", "narration.suggestion.usage.fill.hidable": "Struca Tab par doparar el sujerimento, o Esc par bandonar i sujerimenti", "narration.tab_navigation.usage": "Struca Ctrl e Tab par canbiar scheda", "narrator.button.accessibility": "Acesibiłità", "narrator.button.difficulty_lock": "Bloco de ła dificoltà", "narrator.button.difficulty_lock.locked": "Blocada", "narrator.button.difficulty_lock.unlocked": "Dezblocada", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "L'asion %s l'é sociada a'l %s", "narrator.controls.reset": "Rezeta el boton %s", "narrator.controls.unbound": "%s l'é mìa sociada", "narrator.joining": "<PERSON><PERSON>", "narrator.loading": "Drio cargar: %s", "narrator.loading.done": "<PERSON><PERSON>", "narrator.position.list": "Selesiona la lista riga %s de %s", "narrator.position.object_list": "Selesionà l'elemento de riga %s de %s", "narrator.position.screen": "Elemento a schermo %s de %s", "narrator.position.tab": "Selesionada la scheda %s de %s", "narrator.ready_to_play": "Pronto a zogar", "narrator.screen.title": "<PERSON><PERSON>", "narrator.screen.usage": "Dòpara el cursor del mouse o el tasto Tab par selesionar un elemento", "narrator.select": "Sełesionà: %s", "narrator.select.world": "Sełesionà %s, zugà par ùltemo: %s, %s, %s, varsion: %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON>", "narrator.toast.enabled": "Na<PERSON>r <PERSON>", "optimizeWorld.confirm.description": "'Sta oparasion ła sercarà de otemizar el to mondo, segurandose che tuti i dati i sipia memorizadi inte'l formà de zugo piasè resente. El proseso el podarà durar a łongo, conforma a'l to mondo. Na volta otemizà, el mondo el podarìa cargarse piasè in presia, ma no'l sarà pì conpatìbiłe co łe vecie varsion de'l zugo. Sito seguro de vołer continuar?", "optimizeWorld.confirm.proceed": "Crea backup e otimiza", "optimizeWorld.confirm.title": "Otemiza el mondo", "optimizeWorld.info.converted": "Chunk azornadi: %s", "optimizeWorld.info.skipped": "Chunk saltadi: %s", "optimizeWorld.info.total": "Chunk totałi: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Drio contar i chunk...", "optimizeWorld.stage.failed": "Falìo! :(", "optimizeWorld.stage.finished": "Drio conpletar...", "optimizeWorld.stage.finished.chunks": "Drio conpletar l'ajornamento de le chunk...", "optimizeWorld.stage.finished.entities": "Drio conpletar l'ajornamento de le entidà...", "optimizeWorld.stage.finished.poi": "Drio conpletar l'ajornamento dei ponti de intaresse...", "optimizeWorld.stage.upgrading": "Drio ajornar tute le chunk...", "optimizeWorld.stage.upgrading.chunks": "Drio ajornar tute le chunk...", "optimizeWorld.stage.upgrading.entities": "Drio ajornar tute le entidà...", "optimizeWorld.stage.upgrading.poi": "Drio ajornar tuti i ponti de intaresse...", "optimizeWorld.title": "Drio otemizar el mondo \"%s\"", "options.accessibility": "Inpostasion de acesìbilidà...", "options.accessibility.high_contrast": "<PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available.", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Guida a l'acesìbiłità", "options.accessibility.menu_background_blurriness": "Nebia sfondo menù", "options.accessibility.menu_background_blurriness.tooltip": "El canbia la desfogatura del sfondo del menù.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON>la <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "La parmete al Narador de èssar ativà o desativà co' Cmb+B", "options.accessibility.narrator_hotkey.tooltip": "El abìlita el narador a èssar ativà o manco co \"Ctrl+ B\".", "options.accessibility.panorama_speed": "Sveltessa del panorama", "options.accessibility.text_background": "S<PERSON>ndo de'l testo", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Dapartuto", "options.accessibility.text_background_opacity": "Opasidà sfondo de'l testo", "options.accessibility.title": "Inpostasion de acesìbiłidà...", "options.allowServerListing": "Dare Server Listings", "options.allowServerListing.tooltip": "Servers podér list<PERSON><PERSON> online cóme pàrte de łori stati pùbilci.\nCon sta opsion sarrà il to nome no ensirà in ste Łiste.", "options.ao": "Iłuminasion łeziera", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "M<PERSON><PERSON><PERSON>", "options.ao.off": "Stuà", "options.attack.crosshair": "<PERSON><PERSON>", "options.attack.hotbar": "Bara", "options.attackIndicator": "Indicador de ataco", "options.audioDevice": "Dispositivo", "options.audioDevice.default": "Predefenìo del sistema", "options.autoJump": "Salto automàtego", "options.autoSuggestCommands": "Suzerimenti d'i comandi", "options.autosaveIndicator": "Indicador de Auto-salvàggio", "options.biomeBlendRadius": "Tranzision tra biomi", "options.biomeBlendRadius.1": "OFF(Pi<PERSON><PERSON>)", "options.biomeBlendRadius.11": "11x11 (Estrema)", "options.biomeBlendRadius.13": "13×13 (fora mezura)", "options.biomeBlendRadius.15": "15×15 (màsima)", "options.biomeBlendRadius.3": "3×3 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5×5 (Normal)", "options.biomeBlendRadius.7": "7x7 (<PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON>)", "options.chat": "Inpostasion de chat...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Retardo chat: %s secondi", "options.chat.delay_none": "<PERSON><PERSON>do de chat: nisun", "options.chat.height.focused": "Altesa (in uzo)", "options.chat.height.unfocused": "Altesa (mìa in uzo)", "options.chat.line_spacing": "Interłìnia", "options.chat.links": "Link esterno", "options.chat.links.prompt": "Conferma sui link", "options.chat.opacity": "Opasità testo de ła chat", "options.chat.scale": "Grandesa de testo", "options.chat.title": "Inpostasion de ła chat...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Vizìbiłe", "options.chat.visibility.hidden": "<PERSON>ont<PERSON>", "options.chat.visibility.system": "Sol comandi", "options.chat.width": "Łarghesa", "options.chunks": "%s chunk", "options.clouds.fancy": "Sofistegada", "options.clouds.fast": "Stiłizà", "options.controls": "Controłi...", "options.credits_and_attribution": "Reconossimenti e atribusion...", "options.damageTiltStrength": "Pingolamento da da<PERSON>", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Logo monocromà<PERSON>go", "options.darkMojangStudiosBackgroundColor.tooltip": "Canbia in negro el fondo de cargamento de Monjang Studios.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON> pulsa", "options.darknessEffectScale.tooltip": "Controla cuanto forte se il pulso de'l efeto Oscùrità che el Sorvejante o Urlatore de Sculk te ga dato.", "options.difficulty": "Dificoltà", "options.difficulty.easy": "Fàsiłe", "options.difficulty.easy.info": "Le mob ostili le spawna ma le progura manco dano. La bara de la fame la pol esaurirse e sbassar la salute fin a 5 cori.", "options.difficulty.hard": "Difìsiłe", "options.difficulty.hard.info": "Le mob ostili le spawna e le fa piassè dano. La bara de la fame la se esaurisse e la sbassa la salute.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "<PERSON><PERSON>", "options.difficulty.normal.info": "Le mob ostili le spawna e le progura un dano normal. La bara de la fame la se esaurisse e la sbasse la salute fin a mezo cor.", "options.difficulty.online": "Dificoltà del Server", "options.difficulty.peaceful": "Paxìfega", "options.difficulty.peaceful.info": "Gnissuna mod ostile e sol çerte mob neutrali le spawna. La bara de la fame no la se sbassa e la salude la se inpinisse col tenpo.", "options.directionalAudio": "Audio Diresionàl", "options.directionalAudio.off.tooltip": "Sòno clasego de Stereo", "options.directionalAudio.on.tooltip": "Dopera audio diresional HRTF par mejorar la simulasìon de'l sono 3D.\nDimanda hardware audio compatibile có HRTF, e se mejo se te usi cufie.", "options.discrete_mouse_scroll": "Scorimento discreto", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON> de <PERSON>e entid<PERSON>", "options.entityShadows": "<PERSON><PERSON><PERSON><PERSON><PERSON>e entid<PERSON>", "options.font": "Inpostasion del caràtare...", "options.font.title": "Inpostasion del caràtare", "options.forceUnicodeFont": "Sforsa caràtari unicode", "options.fov": "Canpo vizivo", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON>", "options.fovEffectScale": "Efeti de canpo visivo", "options.fovEffectScale.tooltip": "Controla cuanto pole canbiar el canpo visivo co' i effeti de zugo.", "options.framerate": "%s fps", "options.framerateLimit": "Fps màsimi", "options.framerateLimit.max": "<PERSON><PERSON>", "options.fullscreen": "<PERSON><PERSON><PERSON>", "options.fullscreen.current": "Atual", "options.fullscreen.entry": "%s × %s (%s Hz, %s bit)", "options.fullscreen.resolution": "Resołusion a schermo intiero", "options.fullscreen.unavailable": "Inpostasion mìa despon<PERSON>e", "options.gamma": "<PERSON><PERSON>", "options.gamma.default": "Predefenìo", "options.gamma.max": "Ciara", "options.gamma.min": "<PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Sveltessa de slux<PERSON>o", "options.glintSpeed.tooltip": "La règola la sveltessa de sluxorìo in çima ai ojeti incantadi.", "options.glintStrength": "Intensidà de sluxorìo", "options.glintStrength.tooltip": "La controla cuanto trasparente el sluxorìo el xe sui ojeti incantadi.", "options.graphics": "Gràfega", "options.graphics.fabulous": "Favołoza!", "options.graphics.fabulous.tooltip": "la gràfica %s la dòpara i shader de schermo par dessegnar el tenpo atmosfèrego, nìvoli e partesele atraverso i blochi trasparenti.\nCuesto el podarìa influensar severamente le prestasion d'i despositivi portàbili e d'i schermi 4K.", "options.graphics.fancy": "Sofistegada", "options.graphics.fancy.tooltip": "Ła gràfega \"Sofistegada\" ła bałansa łe prestasion e ła cuałità par ła mazor parte d'i dispozitivi.\n<PERSON><PERSON>, nìvołi e partezełe łe podarìa no èsar vizìbiłi dedrìo blochi trasparenti o l'àcua.", "options.graphics.fast": "Stiłizà", "options.graphics.fast.tooltip": "Ła gràfica stiłizada ła reduze el amontar de piova e neve vizìbiłi.\nI efeti de trasparensa i é dezativadi par molti blochi come łe foje.", "options.graphics.warning.accept": "Continua sensa suporto", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.warning.message": "El to dispozitivo gràfego l'é stà rełevà come mìa suportà par ła opsion gràfega %s.\nTe pol ignorar 'sto mesazo e continuar, comùncue el suporto no'l sarà pì fornìo par el to dispozidivo se te sernisi de doparar łe gràfeghe %s.", "options.graphics.warning.renderer": "Render rełevà: [%s]", "options.graphics.warning.title": "Dispozitivo gràfego mìa suportà", "options.graphics.warning.vendor": "Fornidor <PERSON>: [%s]", "options.graphics.warning.version": "Varsion di OpenGL rełevada: [%s]", "options.guiScale": "Dimension GUI", "options.guiScale.auto": "Automàtego", "options.hidden": "Sconto", "options.hideLightningFlashes": "Scondi i sciantisi", "options.hideLightningFlashes.tooltip": "Prevents Lightning Bolts from making the sky flash. The bolts themselves will still be visible.", "options.hideMatchedNames": "Scondi nomi corespondenti", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Scondi i testi Splash", "options.hideSplashTexts.tooltip": "El scondi i testi zaldi splash del menù prinçipal.", "options.inactivityFpsLimit": "Redusi frecuensa cuan'", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimizà", "options.inactivityFpsLimit.minimized.tooltip": "El lìmita la frecuensa dei fotogrami sol cuan' la fenestra l'é minimizada.", "options.invertMouse": "Invertisi el mouse", "options.japaneseGlyphVariants": "Variante de caràtari Japonesi", "options.japaneseGlyphVariants.tooltip": "El dòpara la variante japonese dei caràtari CJK par el font predefinìo.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Alterna", "options.language": "Łengua...", "options.language.title": "Lengua", "options.languageAccuracyWarning": "(Le tradusion le podarìa no èssar acurade al 100%%)", "options.languageWarning": "Łe tradusion łe podarìa no èsar acurade al 100%%", "options.mainHand": "<PERSON> prinsipal", "options.mainHand.left": "Sanca", "options.mainHand.right": "<PERSON><PERSON>", "options.mipmapLevels": "<PERSON><PERSON><PERSON>", "options.modelPart.cape": "Tabaro", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Ganba sanca", "options.modelPart.left_sleeve": "Mànega sanca", "options.modelPart.right_pants_leg": "Ganba destra", "options.modelPart.right_sleeve": "Mànega destra", "options.mouseWheelSensitivity": "Sensibiłità de scorimento", "options.mouse_settings": "Inpostasion de'l mouse...", "options.mouse_settings.title": "Inpostasion de'l mouse", "options.multiplayer.title": "Inpostasion multizugador...", "options.multiplier": "%s×", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Narador", "options.narrator.all": "<PERSON><PERSON><PERSON> tuto", "options.narrator.chat": "<PERSON><PERSON><PERSON> chat", "options.narrator.notavailable": "<PERSON><PERSON>a di<PERSON>", "options.narrator.off": "Stuà", "options.narrator.system": "<PERSON><PERSON><PERSON>", "options.notifications.display_time": "<PERSON><PERSON> de le notìfeghe", "options.notifications.display_time.tooltip": "El règola par cuanto tenpo tute le notìfeghe le resta visìbili sul schermo.", "options.off": "OFF", "options.off.composed": "%s: <PERSON><PERSON>", "options.on": "ON", "options.on.composed": "%s: <PERSON><PERSON>", "options.online": "Online...", "options.online.title": "Opsioni Online", "options.onlyShowSecureChat": "Mostra Solo Chat Segura", "options.onlyShowSecureChat.tooltip": "Mostra sol mesaji verifegati e mìa alterati da altri zugatori.", "options.operatorItemsTab": "Ojeti par i operadori", "options.particles": "Partezełe", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "Redute", "options.particles.minimal": "Mìni<PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Fàbricatore de Chunk", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Cualche àto inte un chunk farà recompilàr el chunk sùvito. Sto implicà meter e destrùser blocchi.", "options.prioritizeChunkUpdates.nearby": "Blocking dél tùto", "options.prioritizeChunkUpdates.nearby.tooltip": "Chunks vissin son senpre compìlati sùvito. Quésto può canbiàre la prestasìon del zuògo quàndo blocchi son mési o destrùzà.", "options.prioritizeChunkUpdates.none": "S<PERSON>letàto", "options.prioritizeChunkUpdates.none.tooltip": "Chunks vizìni son compilati en sfiletàture paralele. Quésto può risultàr en brevi bùsi visivi quàndo blocchi son destrùzà.", "options.rawMouseInput": "Input direto", "options.realmsNotifications": "Nove e invidi Realms", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "<PERSON><PERSON> de debug reduti", "options.renderClouds": "Nìołe", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Distansa de renderizasion", "options.resourcepack": "Pacheti de resorse...", "options.rotateWithMinecart": "Roda coi carèi", "options.rotateWithMinecart.tooltip": "El stabilisse se la visual del zugador la ga da rodar col carel. Disponìbile sol in mondi co inpostasion sparimentài \"Mejoramenti par carel\" ative.", "options.screenEffectScale": "Efeti de distorsion", "options.screenEffectScale.tooltip": "Intensità de afàno e efeti de distorxion del portałe del Nether.\nA vałori basi, l'efeto de afàno l'é canbià co' nà sorapozixion verde.", "options.sensitivity": "Sensibiłità", "options.sensitivity.max": "IPERZVELTESA!!!", "options.sensitivity.min": "*Zbadajo*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.simulationDistance": "Distansa de simulasion", "options.skinCustomisation": "Parsonałizasion de ła skin...", "options.skinCustomisation.title": "Parsonałizasion de ła skin", "options.sounds": "Mùzega e soni...", "options.sounds.title": "Opsion de mùzega e soni", "options.telemetry": "<PERSON><PERSON> de telemetrìa...", "options.telemetry.button": "Arbinamento de dati", "options.telemetry.button.tooltip": "\"%s\" includes only the required data.\n\"%s\" includes optional, as well as the required data.", "options.telemetry.disabled": "La telemetrìa la xe desativada.", "options.telemetry.state.all": "<PERSON><PERSON>", "options.telemetry.state.minimal": "Mìni<PERSON>", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "Opsion", "options.touchscreen": "Modałità touchscreen", "options.video": "Inpostasion video...", "options.videoTitle": "Inpostasion grà<PERSON>ghe", "options.viewBobbing": "Movimento de ła càmara", "options.visible": "Vizìbiłe", "options.vsync": "V-sync", "outOfMemory.message": "Minecraft gà finì a memoria.\n\nLe pol eser causà da un bug nel zogo o la Màchina Virtual Java no gavéa memoria allocata sufisiente.\n\nPar prevenìr la corusìon de to livelo, tu zogo en corso el ga sarà. Ghemo provà a salvar memorià sufisiente par portarti al menù principal e lasarte zugare ncora, ma le podarìa no aver funsionà.\n\nPar piazer prova a reinviar tu zogo se ti te vidi sto mesajo de novo.", "outOfMemory.title": "Memoria olfa!", "pack.available.title": "Disponìbili", "pack.copyFailure": "Inposìbiłe copiar i pacheti", "pack.dropConfirm": "Voto zontar i seguenti pacheti a Minecraft?", "pack.dropInfo": "Stràpega i file in 'sta fenestra par zontar i pacheti", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(Inpira chi i pacheti)", "pack.incompatible": "Inconbinàbiłe", "pack.incompatible.confirm.new": "'Sto pacheto de resorse l'é stà creà par 'na varsion pì reçente de Minecraft e'l podarìa no funsionar coretamente.", "pack.incompatible.confirm.old": "'Sto pacheto de resorse l'é stà creà par 'na varsion pì vecia de Minecraft e'l podarìa no funsionar coretamente.", "pack.incompatible.confirm.title": "Sito seguro de voler cargar 'sto pacheto?", "pack.incompatible.new": "(Creà par una varsion pì nova de Minecraft)", "pack.incompatible.old": "(Creà par una varsion pì vecia de Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Verzi la cartela dei pacheti", "pack.selected.title": "Selesionà", "pack.source.builtin": "predefen<PERSON>o", "pack.source.feature": "funsionalità", "pack.source.local": "ł<PERSON>ale", "pack.source.server": "server", "pack.source.world": "mondo", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanese", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Corte", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroco", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Barsajo bonbardà con sucesso", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Tescio in fiama", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Busto", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON><PERSON> de le sprugie", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Canbio", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bondì Sior Courbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Nemigo final", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Lotadori", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Reperto", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fogo", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Ùmile", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Caligo", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Fuminante", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Sfera", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Alocolimoni", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passazo", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Porco-sena", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pontador", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La pissina", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Cavalcada in prà", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Lido", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON><PERSON> mort<PERSON>i", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON><PERSON> e rose", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "El palco el xe pronto", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Aque grande", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Des<PERSON><PERSON><PERSON>", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "El Vodo", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Viandon", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Deserto", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Acua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vento", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Variante casual", "parsing.bool.expected": "Previsto un vałor boolean", "parsing.bool.invalid": "<PERSON><PERSON>łor boolean mìa v<PERSON><PERSON>: \"%s\" no l'è \"true\" o \"false\"", "parsing.double.expected": "Previsto un vałor double", "parsing.double.invalid": "Vałor double mìa vàłido: %s", "parsing.expected": "Previsto \"%s\"", "parsing.float.expected": "Previsto un vałor float", "parsing.float.invalid": "Vałor float mìa vàłido: %s", "parsing.int.expected": "Previsto un vałor intiero", "parsing.int.invalid": "Vałor intiero mìa vàłido: %s", "parsing.long.expected": "Previsto un vałor long", "parsing.long.invalid": "Vałor long mìa vàłido: %s", "parsing.quote.escape": "Secuensa de escape \"\\%s\" mìa vàłida inte ła stringa tra vergołete", "parsing.quote.expected.end": "Stringa mìa sarada da vergołete", "parsing.quote.expected.start": "Previste vergołete in prinsipio de ła stringa", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Partezeła mìa conosesta: %s", "permissions.requires.entity": "'Sto comando el ga da èsar ezeguìo da un entidà", "permissions.requires.player": "'Sto comando el ga da èsar ezeguìo da un zugador", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Efeto a<PERSON>:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicà mìa conosesto: %s", "quickplay.error.invalid_identifier": "Inpossibile catar un mondo con cuel indentifegador", "quickplay.error.realm_connect": "Inpossìbile conétarse al Realm", "quickplay.error.realm_permission": "Manca i parmessi par conétarse al Realm", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms no l'é mìa suportà inte łe varsion snapshot", "recipe.notFound": "Reseta mìa conosesto: %s", "recipe.toast.description": "Controła el resetario", "recipe.toast.title": "Nove resete dezblocade!", "record.nowPlaying": "Drio reprodùsar: %s", "recover_world.bug_tracker": "Reporta un bug", "recover_world.button": "Tenta el recùparo", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "File mancante", "recover_world.issue.none": "<PERSON><PERSON><PERSON> eror", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Tenta el reprìstino", "recover_world.restoring": "Drio tentar de repristinar el mondo...", "recover_world.state_entry": "Stato del %s: ", "recover_world.state_entry.unknown": "mìa con<PERSON><PERSON>o", "recover_world.title": "Inpossìbile cargar el mondo", "recover_world.warning": "Inpossìbile cargar el somario del mondo", "resourcePack.broken_assets": "REŁEVADE RESORSE DANEJADE", "resourcePack.high_contrast.name": "<PERSON><PERSON>", "resourcePack.load_fail": "Recarga de łe resorse fałìa", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "E<PERSON>r del pacheto de resorse", "resourcePack.server.name": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>l mondo", "resourcePack.title": "Sełesiona i pacheti de resorse", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "Predefenìo", "resourcepack.downloading": "<PERSON><PERSON> des<PERSON>gar el pacheto de resorse", "resourcepack.progress": "Drio descargar el file (%s MB)...", "resourcepack.requesting": "Drio inviar ła dimanda...", "screenshot.failure": "Inposìbiłe salvar ła schermada: %s", "screenshot.success": "Schermada salvada come %s", "selectServer.add": "Zonta un server", "selectServer.defaultName": "Server de Minecraft", "selectServer.delete": "Ełìmen<PERSON>", "selectServer.deleteButton": "Ełìmen<PERSON>", "selectServer.deleteQuestion": "Sito seguro de vołer ełimenar sto server?", "selectServer.deleteWarning": "'%s'' el sarà perso par senpre! (Un bel toco!)", "selectServer.direct": "Conession direta", "selectServer.edit": "Modìfega", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON>)", "selectServer.refresh": "Azorna", "selectServer.select": "Intra inte'l server", "selectWorld.access_failure": "Inposìbile intrar int'el mondo", "selectWorld.allowCommands": "Parmeti comandi", "selectWorld.allowCommands.info": "Comandi cofà /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON> ordeni", "selectWorld.backupEraseCache": "Scanceła i dati inte ła cache", "selectWorld.backupJoinConfirmButton": "Crèa un backup e carga", "selectWorld.backupJoinSkipButton": "So cuel che son drio far!", "selectWorld.backupQuestion.customized": "I mondi parsonałizadi i'é pì suportadi", "selectWorld.backupQuestion.downgrade": "Voltar a 'na varsion anterior no l'é suportà", "selectWorld.backupQuestion.experimental": "I mondi che i dòpara inpostasion sparimentali no i xe suportadi", "selectWorld.backupQuestion.snapshot": "<PERSON><PERSON> des<PERSON> cargar 'sto mondo?", "selectWorld.backupWarning.customized": "Desfortunadamente A no suportemo mondi parsonałizadi in 'sta varsion de Minecraft. Podemo comùncue cargar 'sto mondo e łasar tuto come l'era, ma el novo teren zenarà no'l sarà mìa parsonałizà. Se scuzemo par el dezguido!", "selectWorld.backupWarning.downgrade": "'Sto mondo l'é stà cargà l'ùltema volta in ła varsion %s, ti te si in varsion %s. <PERSON><PERSON><PERSON> in 'sta varsion podarìa cauzarde ła corusion - no podemo garantir che el cargarà o funsionarà. Se vol comuncue continuar, ezegui un backup!", "selectWorld.backupWarning.experimental": "'Sto mondo el dòpara inpostasion sparimentali che le podarìa desmétar de funçionar in calsìase momento. No podemo garantir che el se cargarà o'l funsionarà. Chi se nàvega a vista!", "selectWorld.backupWarning.snapshot": "'Sto mondo l'é stà cargà l'ùltema volta in ła varsion %s, ti te si in varsion %s. Par piazer ezegui un backup in cazo te gavesi problemi de corusion de'l mondo!", "selectWorld.bonusItems": "Baùl bonus", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "Ordeni", "selectWorld.conversion": "El ga da èsar convartìo!", "selectWorld.conversion.tooltip": "'Sto mondo el ga da èssar verto in na varsion piassè vecia (come 1.6.4) par èssar convertìo in seguressa", "selectWorld.create": "Crèa novo mondo", "selectWorld.customizeType": "Parsonałiza", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON>", "selectWorld.data_read": "<PERSON><PERSON> l<PERSON>zar i dati del mondo...", "selectWorld.delete": "Scanceła", "selectWorld.deleteButton": "Ełìmen<PERSON>", "selectWorld.deleteQuestion": "Sito seguro de vołer e<PERSON><PERSON> 'sto mondo?", "selectWorld.deleteWarning": "\"%s\" el ndarà perso par senpre! (Un mucio de tenpo!)", "selectWorld.delete_failure": "Inposìbile elimenar el mondo", "selectWorld.edit": "Modìfega", "selectWorld.edit.backup": "Fa' un backup", "selectWorld.edit.backupCreated": "%s", "selectWorld.edit.backupFailed": "Backup fałìo", "selectWorld.edit.backupFolder": "Verzi ła carteła d'i backup", "selectWorld.edit.backupSize": "backup ezeguìo (%s MB)", "selectWorld.edit.export_worldgen_settings": "Esporta le inpostasion de zenarasion", "selectWorld.edit.export_worldgen_settings.failure": "Esportasion falìa", "selectWorld.edit.export_worldgen_settings.success": "Esportà", "selectWorld.edit.openFolder": "Verzi ła carteła de'l mondo", "selectWorld.edit.optimize": "Otemiza el mondo", "selectWorld.edit.resetIcon": "De volta a ła icona orizenal", "selectWorld.edit.save": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.title": "Modìfega mondo", "selectWorld.enterName": "Nome de'l Mondo", "selectWorld.enterSeed": "Seme par el zenerador de mondi", "selectWorld.experimental": "Sperimental", "selectWorld.experimental.details": "Detai", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Avviso de funsion sperimentali", "selectWorld.experiments": "Esparimenti", "selectWorld.experiments.info": "I esparimenti i xe potensiài nove funsionalidà. Fa' atension parché calsossa podarìa ndar storto. I esparimenti i pol èssar desativadi dopo la creasion del mondo.", "selectWorld.futureworld.error.text": "Calcossa l'é ndà mal provando a cargar el mondo da na varsion futura. L'era na oparasion risciosa, ne indespiase che la sipia mìa funçionada.", "selectWorld.futureworld.error.title": "G<PERSON>'é stà un eror!", "selectWorld.gameMode": "Modałità", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Come la modalità soravivensa, ma i blochi i pol mìa èssar zontadi o cavadi.", "selectWorld.gameMode.adventure.line1": "Conpagna de la modalità soravivensa, ma i blochi", "selectWorld.gameMode.adventure.line2": "no i pol mia èsar zontài o cavài", "selectWorld.gameMode.creative": "Creativa", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, costruissi e esplora sensa limitasion. Te pol svolar, verghe materiài infinidi e no te ciapi dano dai mostri.", "selectWorld.gameMode.creative.line1": "Resorse sensa łì<PERSON>, zvolo łìbaro e", "selectWorld.gameMode.creative.line2": "destrusion al istante dei blochi", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Modalità soravivensa blocata in \"Difìsile\". Se te mori no te podarè re-spawnar.", "selectWorld.gameMode.hardcore.line1": "Conpagna de la modalità soravivensa, co' la piasè alta dificoltà", "selectWorld.gameMode.hardcore.line2": "e na soła vida", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "Te pol vardar, ma no tocar.", "selectWorld.gameMode.spectator.line1": "Te pol vardar, ma no tocar", "selectWorld.gameMode.survival": "Soravivensa", "selectWorld.gameMode.survival.info": "Esplora un mondo misterioso dove te pol costruir, arbinar, craftar e conbàtar i mostri.", "selectWorld.gameMode.survival.line1": "Va' in çerca de resorse, crafta, monta", "selectWorld.gameMode.survival.line2": "<PERSON> <PERSON><PERSON><PERSON>, sałude, fame", "selectWorld.gameRules": "<PERSON>è<PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Inporta inpostaçion", "selectWorld.import_worldgen_settings.failure": "Inposìbile inportar le inpostaçion", "selectWorld.import_worldgen_settings.select_file": "Selesiona i file de le inpostaçion (.json)", "selectWorld.incompatible.description": "El mondo el pol mìa èssar verto in 'sta varsion.\nEl xe stà zugà par l'ùltema volta in varsion %s.", "selectWorld.incompatible.info": "Varsion mìa conpatìbile: %s", "selectWorld.incompatible.title": "Varsion mìa conpatìbile", "selectWorld.incompatible.tooltip": "'Sto mondo el pol mìa èssar verto parché el xe stà creà int'una varsion mìa conpatìbile", "selectWorld.incompatible_series": "Creà in n'a version incompatibiłe", "selectWorld.load_folder_access": "Inposìbiłe lèzar o intrar inte ła carteła indove i xe salvadi i mondi!", "selectWorld.loading_list": "Drio cargar lista dei mondi", "selectWorld.locked": "Za verto in n'antra istansa de Minecraft", "selectWorld.mapFeatures": "Ingènera struture", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "<PERSON><PERSON><PERSON> de mondo", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Antre opsion de'l mondo...", "selectWorld.newWorld": "<PERSON><PERSON>", "selectWorld.recreate": "Re-crèa", "selectWorld.recreate.customized.text": "I mondi parsonałizadi i'é pi suportadi da sta varsion de Minecraft. A podemo re-crearli co seme compagno e compagna proprietà, ma ogni parsonałizasion del teren ła ndarà perdùa. Ne scuzemo par el dezguido!", "selectWorld.recreate.customized.title": "I mondi parsonałizadi i'é pì suportadi", "selectWorld.recreate.error.text": "Calcosa l'é ndà mal mentre se provava re-crear el mondo.", "selectWorld.recreate.error.title": "G<PERSON>'é sè un eror!", "selectWorld.resource_load": "Drio pareciar le resorse...", "selectWorld.resultFolder": "El sarà salvà in:", "selectWorld.search": "serca un mondo", "selectWorld.seedInfo": "Łasa vudo par un seme cazual", "selectWorld.select": "Zuga inte'l mondo sełesionà", "selectWorld.targetFolder": "Cartela de salvatazo: %s", "selectWorld.title": "Sełesiona mondo", "selectWorld.tooltip.fromNewerVersion1": "El mondo l'é stà salvà inte na nova varsion,", "selectWorld.tooltip.fromNewerVersion2": "car<PERSON>lo podarìa cauzar problemi!", "selectWorld.tooltip.snapshot1": "No sta' desmentegarte de far un backup de 'sto mondo", "selectWorld.tooltip.snapshot2": "prima de cargarlo in 'sto snapshot.", "selectWorld.unable_to_load": "Inposìbiłe cargar i mondi", "selectWorld.version": "Varsion:", "selectWorld.versionJoinButton": "Carga isteso", "selectWorld.versionQuestion": "<PERSON><PERSON> cargar 'sto mondo?", "selectWorld.versionUnknown": "mìa con<PERSON>sto", "selectWorld.versionWarning": "'Sto mondo el xe stà zugà par ła ùltema volta in varsion '%s' e'l cargarlo cusì el podarìa causarde ła corusion!", "selectWorld.warning.deprecated.question": "Alcune funsion doparade le xe deprecade e no le funsionarà pì in futuro. Voto proçédar isteso?", "selectWorld.warning.deprecated.title": "Atension! Cueste inpostasion le dòpara funsion deprecade", "selectWorld.warning.experimental.question": "Cueste inpostasion le xe sparimentali e le podarìa desmétar de funsionar in calsìase momento. <PERSON><PERSON>darito proçédar?", "selectWorld.warning.experimental.title": "Atension! Cueste inpostasion le dòpara funsion sparimentali", "selectWorld.warning.lowDiskSpace.description": "Ghe xe restà poco spasio lìbaro sul to dispositivo.\nTermenar el spasio del disco durante el zugo el podarìa causar dani al to mondo.", "selectWorld.warning.lowDiskSpace.title": "Atension! Poco spasio sul disco!", "selectWorld.world": "<PERSON><PERSON>", "sign.edit": "Testo de'l cartel :", "sleep.not_possible": "No xé posibiłe saltare ła note", "sleep.players_sleeping": "%s/%s zugadori drio pisocare", "sleep.skipping_night": "Drio saltare ła note", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Slot mìa conosesto: %s", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "<PERSON><PERSON><PERSON>", "soundCategory.block": "<PERSON><PERSON>", "soundCategory.hostile": "Creature osti<PERSON>e", "soundCategory.master": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "soundCategory.music": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.neutral": "<PERSON>rea<PERSON> amighe", "soundCategory.player": "Zogador<PERSON>", "soundCategory.record": "Dischi e blochi sonori", "soundCategory.ui": "UI", "soundCategory.voice": "Voze e diàłoghi", "soundCategory.weather": "<PERSON><PERSON>", "spectatorMenu.close": "Sara el <PERSON>ù", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.root.prompt": "Struca un tasto par selesionar un comando, strù<PERSON>o da novo par eseguirlo.", "spectatorMenu.team_teleport": "Tełetraspòrtate da un conpanjo de scuara", "spectatorMenu.team_teleport.prompt": "Sełesiona na scuara indove tełetrasportarte", "spectatorMenu.teleport": "Tełetraspòrtate da un zugador", "spectatorMenu.teleport.prompt": "Ser<PERSON><PERSON> un zugador indove tełetrasportarte", "stat.generalButton": "Zenaral", "stat.itemsButton": "Ozeti", "stat.minecraft.animals_bred": "Animał<PERSON>", "stat.minecraft.aviate_one_cm": "Distansa co' elytre", "stat.minecraft.bell_ring": "Canpane sonade", "stat.minecraft.boat_one_cm": "Distansa in barca", "stat.minecraft.clean_armor": "Parte de armadura netade", "stat.minecraft.clean_banner": "Gonfałoni netadi", "stat.minecraft.clean_shulker_box": "Shulker Box netadi", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> da <PERSON>", "stat.minecraft.damage_absorbed": "<PERSON>", "stat.minecraft.damage_blocked_by_shield": "Dano blocà co'l scudo", "stat.minecraft.damage_dealt": "<PERSON><PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON> (asorbìo)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> (rezistìo)", "stat.minecraft.damage_resisted": "<PERSON> re<PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON><PERSON> morte", "stat.minecraft.drop": "Ozeti dropadi", "stat.minecraft.eat_cake_slice": "<PERSON>te de torta manjade", "stat.minecraft.enchant_item": "Ozeti incantadi", "stat.minecraft.fall_one_cm": "Distansa in cascada", "stat.minecraft.fill_cauldron": "<PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON> c<PERSON>i", "stat.minecraft.fly_one_cm": "Distan<PERSON>", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Distansa a caval", "stat.minecraft.inspect_dispenser": "Despensadori <PERSON>", "stat.minecraft.inspect_dropper": "Dropadori ezaminadi", "stat.minecraft.inspect_hopper": "Tramoze ezaminade", "stat.minecraft.interact_with_anvil": "Intarasion co l'ancùzene", "stat.minecraft.interact_with_beacon": "Intarasion coi fari", "stat.minecraft.interact_with_blast_furnace": "Intarasion co ła gran fornaze", "stat.minecraft.interact_with_brewingstand": "Intarasion coi łanbichi", "stat.minecraft.interact_with_campfire": "Intarasione co'l fogo da canpo", "stat.minecraft.interact_with_cartography_table": "Intarasion co i bachi da cartògrafo", "stat.minecraft.interact_with_crafting_table": "Intarasion co i bachi da łaoro", "stat.minecraft.interact_with_furnace": "Intarasion co łe fornaze", "stat.minecraft.interact_with_grindstone": "Intarasion co ła moła", "stat.minecraft.interact_with_lectern": "Intarasion co'l łetorin", "stat.minecraft.interact_with_loom": "Intarasion co'l tełar", "stat.minecraft.interact_with_smithing_table": "Intaraxion co' banchi de'l favaro", "stat.minecraft.interact_with_smoker": "Intarasion col fumegador", "stat.minecraft.interact_with_stonecutter": "Intarasion co'l tajapiere", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "Partide bandonade", "stat.minecraft.minecart_one_cm": "Distansa su carel da miniera", "stat.minecraft.mob_kills": "<PERSON><PERSON>", "stat.minecraft.open_barrel": "<PERSON><PERSON> verte", "stat.minecraft.open_chest": "Baùłi verti", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON> de ender verti", "stat.minecraft.open_shulker_box": "Scàtołe de shulker verte", "stat.minecraft.pig_one_cm": "Distansa su porsel", "stat.minecraft.play_noteblock": "Blochi sonori sonadi", "stat.minecraft.play_record": "<PERSON><PERSON> m<PERSON>łi sonadi", "stat.minecraft.play_time": "<PERSON><PERSON>", "stat.minecraft.player_kills": "Zugadori copadi", "stat.minecraft.pot_flower": "Piante in vazo", "stat.minecraft.raid_trigger": "Incursion ativade", "stat.minecraft.raid_win": "Incursion vense", "stat.minecraft.sleep_in_bed": "Note pasade int'un łeto", "stat.minecraft.sneak_time": "<PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON>sa de corsa", "stat.minecraft.strider_one_cm": "Distansa su caminador", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> nodada", "stat.minecraft.talked_to_villager": "Conversasion coi paesani", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON>i", "stat.minecraft.time_since_death": "<PERSON><PERSON> de zugo da ła ùltema crepada", "stat.minecraft.time_since_rest": "<PERSON><PERSON> de zugo da ła ùltema dormida", "stat.minecraft.total_world_time": "<PERSON><PERSON> col mondo verto", "stat.minecraft.traded_with_villager": "Scanbi coi paesani", "stat.minecraft.trigger_trapped_chest": "Baùłi tràpoła ativadi", "stat.minecraft.tune_noteblock": "Blochi sonori acordadi", "stat.minecraft.use_cauldron": "Àcua tolta dai calderoni", "stat.minecraft.walk_on_water_one_cm": "Distansa a piè in àcua basa", "stat.minecraft.walk_one_cm": "<PERSON>stan<PERSON> a piè", "stat.minecraft.walk_under_water_one_cm": "Distansa a piè soto à<PERSON>a", "stat.mobsButton": "<PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "Volte fabricadi", "stat_type.minecraft.dropped": "Dropadi", "stat_type.minecraft.killed": "T'è copà %s %s", "stat_type.minecraft.killed.none": "Mai copà %s", "stat_type.minecraft.killed_by": "%s el t'à copà %s olte", "stat_type.minecraft.killed_by.none": "Te si mai stà copà da %s", "stat_type.minecraft.mined": "Volte minadi", "stat_type.minecraft.picked_up": "Tolti su", "stat_type.minecraft.used": "Volte doparadi", "stats.none": "-", "structure_block.button.detect_size": "REŁEVA", "structure_block.button.load": "CARGA", "structure_block.button.save": "SALVA", "structure_block.custom_data": "Nome de el dataTag parsonałizà", "structure_block.detect_size": "Releva dimension e posision de la strutura:", "structure_block.hover.corner": "Canton: %s", "structure_block.hover.data": "Dati: %s", "structure_block.hover.load": "Cargamento: %s", "structure_block.hover.save": "Salvatajo: %s", "structure_block.include_entities": "Includi entidà:", "structure_block.integrity": "Integridà e seme de ła strutura", "structure_block.integrity.integrity": "Integridà de ła strutura", "structure_block.integrity.seed": "<PERSON><PERSON> de ła strutura", "structure_block.invalid_structure_name": "Nome de ła strutura \"%s\" mìa vàłido", "structure_block.load_not_found": "Ła strutura '%s' no l'è mìa dìsponibiłe", "structure_block.load_prepare": "Ła pozision de ła strutura %s l'é stada pareciada", "structure_block.load_success": "Ła strutura l'é sta cargà da '%s'", "structure_block.mode.corner": "Canton", "structure_block.mode.data": "<PERSON><PERSON>", "structure_block.mode.load": "Cargamento", "structure_block.mode.save": "<PERSON><PERSON>", "structure_block.mode_info.corner": "Modalità canton - Marcador piassamento e dimension", "structure_block.mode_info.data": "<PERSON><PERSON>łit<PERSON> dati - <PERSON><PERSON>go", "structure_block.mode_info.load": "Modałità cargamento - Carga da file", "structure_block.mode_info.save": "Modalità Salvatajo - Scrivi su file", "structure_block.position": "Pozision rełativa", "structure_block.position.x": "pozision rełativa x", "structure_block.position.y": "pozision rełativa y", "structure_block.position.z": "pozision rełativa z", "structure_block.save_failure": "Inposìbiłe salvar ła strutura %s", "structure_block.save_success": "Ła strutura l'è stada salvada come '%s'", "structure_block.show_air": "Visualiza i blochi invisìbili:", "structure_block.show_boundingbox": "Visualiza zona de delimitasion:", "structure_block.size": "Grandesa de ła strutura", "structure_block.size.x": "grandesa de ła strutura x", "structure_block.size.y": "grandesa de ła strutura y", "structure_block.size.z": "grandesa de ła strutura z", "structure_block.size_failure": "Inposìbiłe rełevar ła dimension de ła strutura. Zonta d'i cantoni co'l nome de ła strutura corespondente", "structure_block.size_success": "Ła dimension de ła strutura %s l'é stà riłevada", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Nome de ła strutura", "subtitles.ambient.cave": "<PERSON><PERSON>or inch<PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON>or inch<PERSON>", "subtitles.block.amethyst_block.chime": "Ametista la tintina", "subtitles.block.amethyst_block.resonate": "Ametista la resona", "subtitles.block.anvil.destroy": "Ancùzene desfada", "subtitles.block.anvil.land": "Ancùzene aterada", "subtitles.block.anvil.use": "Ancùzene doparada", "subtitles.block.barrel.close": "<PERSON>ta ła se sara", "subtitles.block.barrel.open": "<PERSON>ta ła se verze", "subtitles.block.beacon.activate": "Faro in<PERSON>ç<PERSON>", "subtitles.block.beacon.ambient": "Faro el ziaoła", "subtitles.block.beacon.deactivate": "Faro s<PERSON>", "subtitles.block.beacon.power_select": "Poder de'l faro sełesionà", "subtitles.block.beehive.drip": "Mel el zgosina", "subtitles.block.beehive.enter": "Ava ła intra in l'avearo", "subtitles.block.beehive.exit": "Ava ła insia da'l avearo", "subtitles.block.beehive.shear": "Fòrbeze łe rascia", "subtitles.block.beehive.work": "Ave łe łaora", "subtitles.block.bell.resonate": "Canpana ła resona", "subtitles.block.bell.use": "Canpana ła sona", "subtitles.block.big_dripleaf.tilt_down": "Fojogiossa la cuna in zo", "subtitles.block.big_dripleaf.tilt_up": "Fojogiossa la cuna in su", "subtitles.block.blastfurnace.fire_crackle": "Gran fornaxe ła s-ciopeta", "subtitles.block.brewing_stand.brew": "Łambico el boje", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON> łe sciopa", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> łe flùtua", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON> łe zguisa", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON>e łe tùrbina", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> łe fonda", "subtitles.block.button.click": "Boton el scioca", "subtitles.block.cake.add_candle": "Candeła piasà", "subtitles.block.campfire.crackle": "El fogo el sciopeta", "subtitles.block.candle.crackle": "Candela la sciopeta", "subtitles.block.candle.extinguish": "Candela la se smorsa", "subtitles.block.chest.close": "Baùl el se sara", "subtitles.block.chest.locked": "Baùl b<PERSON>", "subtitles.block.chest.open": "Baùl el se verze", "subtitles.block.chorus_flower.death": "Fior de chorus el se infiapise", "subtitles.block.chorus_flower.grow": "Fior de chorus el crese", "subtitles.block.comparator.click": "Conpar<PERSON>", "subtitles.block.composter.empty": "Conpostador svodà", "subtitles.block.composter.fill": "Conpostador inpinio", "subtitles.block.composter.ready": "Conpostador el conposta", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.conduit.ambient": "Conduto el pulsa", "subtitles.block.conduit.attack.target": "Conduto el ataca", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Bulbo de Rame el se smorza", "subtitles.block.copper_bulb.turn_on": "Bulbo de Rame el se impissa", "subtitles.block.copper_trapdoor.close": "Rebalta la se sara", "subtitles.block.copper_trapdoor.open": "Rebalta la se verze", "subtitles.block.crafter.craft": "Craftador el crafta", "subtitles.block.crafter.fail": "Craftador el falisse", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON>or inch<PERSON>", "subtitles.block.creaking_heart.spawn": "Cor del Scrico el se sveja", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "Decorated Pot shatters", "subtitles.block.dispenser.dispense": "Ozeto despensà", "subtitles.block.dispenser.fail": "Eror de despensasion", "subtitles.block.door.toggle": "Porta ła sìgoła", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Banco da incantamenti doparà", "subtitles.block.end_portal.spawn": "Portal de'l End el se verze", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON> de ender pia<PERSON>", "subtitles.block.eyeblossom.close": "Ocidea la se sara", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Ocidea la se verze", "subtitles.block.fence_gate.toggle": "Sarajo el sìgoła", "subtitles.block.fire.ambient": "Fogo el sciopeta", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "<PERSON>irin el se verze", "subtitles.block.furnace.fire_crackle": "Fornaze ła sciopeta", "subtitles.block.generic.break": "<PERSON><PERSON>", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "<PERSON><PERSON>", "subtitles.block.generic.hit": "Bloco el se ronpe", "subtitles.block.generic.place": "Bloco p<PERSON>", "subtitles.block.grindstone.use": "Moła doparada", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON> tajata", "subtitles.block.hanging_sign.waxed_interact_fail": "Cartel el cuna", "subtitles.block.honey_block.slide": "Zbrisiamento da'l bloco de mel", "subtitles.block.iron_trapdoor.close": "Rebalta ła se sara", "subtitles.block.iron_trapdoor.open": "Rebalta ła se verze", "subtitles.block.lava.ambient": "Łava ła sciopeta", "subtitles.block.lava.extinguish": "Łava ła se sołidìfega", "subtitles.block.lever.click": "Łieva ativada", "subtitles.block.note_block.note": "Bloco sonoro el sona", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava": "Lava la sgiosina", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava la sgiosina int'un caldiron", "subtitles.block.pointed_dripstone.drip_water": "Acua la sgiosina", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Acua la sgiosina int'un caldiron", "subtitles.block.pointed_dripstone.land": "<PERSON><PERSON><PERSON><PERSON> casca baso", "subtitles.block.portal.ambient": "Portal el sìfoła", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON> de portal el sparixe", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON> de portal el crexe", "subtitles.block.pressure_plate.click": "Pedana a presion ativada", "subtitles.block.pumpkin.carve": "Forfe łe intaja", "subtitles.block.redstone_torch.burnout": "Torsa de Redstone brusada", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> whooshes", "subtitles.block.respawn_anchor.charge": "Àncora de respawn cargada", "subtitles.block.respawn_anchor.deplete": "Àncora de respawn ła se descarga", "subtitles.block.respawn_anchor.set_spawn": "Àncora de respawn ła inposta el spawn", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "<PERSON><PERSON> sculk", "subtitles.block.sculk.spread": "Sculk el se spande", "subtitles.block.sculk_catalyst.bloom": "Catalizardor de sculk el se difonde", "subtitles.block.sculk_sensor.clicking": "Sensor de sculk el se ativa", "subtitles.block.sculk_sensor.clicking_stop": "Sensor de sculk el se de-ativa", "subtitles.block.sculk_shrieker.shriek": "Sbrajador de sculk el siga", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> el se sara", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> el se verze", "subtitles.block.sign.waxed_interact_fail": "Cartel el cuna", "subtitles.block.smithing_table.use": "Banco de'l favaro doparà", "subtitles.block.smoker.smoke": "Fumegador el fuma", "subtitles.block.sniffer_egg.crack": "Ovo de snasador el se sbècola", "subtitles.block.sniffer_egg.hatch": "Ovo de snasador el se verze", "subtitles.block.sniffer_egg.plop": "Snasador el depone n'ovo", "subtitles.block.sponge.absorb": "Sponga la ciucia", "subtitles.block.sweet_berry_bush.pick_berries": "Pomełe arbinade", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "Rebalta ła sìgoła", "subtitles.block.trial_spawner.about_to_spawn_item": "Ojeto infàusto el se parecia", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Zenerador de sfide el se sara", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Zenerador de sfide el se verze", "subtitles.block.trial_spawner.spawn_item": "Ojeto infàusto <PERSON>", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Zenerador de sfide el spawna na mob", "subtitles.block.tripwire.attach": "Fiło el se taca", "subtitles.block.tripwire.click": "Fiło el se ativà", "subtitles.block.tripwire.detach": "Fiło el se destaca", "subtitles.block.vault.activate": "Cassaforte la se inpissa", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Cassaforte la se verze", "subtitles.block.vault.deactivate": "Cassaforte la se smorsa", "subtitles.block.vault.eject_item": "Cassaforte la emete un ojeto", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Cassaforte la se verze", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "Àcua ła core", "subtitles.block.wet_sponge.dries": "Sponga la suga", "subtitles.chiseled_bookshelf.insert": "Libro piassà", "subtitles.chiseled_bookshelf.insert_enchanted": "Libro incantà piassà", "subtitles.chiseled_bookshelf.take": "Libro tolto", "subtitles.chiseled_bookshelf.take_enchanted": "Libro incantà tolto", "subtitles.enchant.thorns.hit": "<PERSON><PERSON>", "subtitles.entity.allay.ambient_with_item": "Allay el cassa", "subtitles.entity.allay.ambient_without_item": "Allay el brama", "subtitles.entity.allay.death": "Allay el more", "subtitles.entity.allay.hurt": "Allay fer<PERSON>o", "subtitles.entity.allay.item_given": "Allay ride", "subtitles.entity.allay.item_taken": "Allay el ciapa", "subtitles.entity.allay.item_thrown": "Allay el buta", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> grunts", "subtitles.entity.armadillo.brush": "<PERSON>ute is brushed off", "subtitles.entity.armadillo.death": "Armadilo el more", "subtitles.entity.armadillo.eat": "Armadilo el magna", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.armadillo.hurt_reduced": "Armadilo el se fa scudo", "subtitles.entity.armadillo.land": "Armadilo el atera", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "Armadilo el se inrodola", "subtitles.entity.armadillo.scute_drop": "Armadillo sheds scute", "subtitles.entity.armadillo.unroll_finish": "Armadilo el se desrodola", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "Suporto par armadura el casca", "subtitles.entity.arrow.hit": "Fresa ła colpise", "subtitles.entity.arrow.hit_player": "<PERSON>ugador <PERSON>", "subtitles.entity.arrow.shoot": "Fresa tirada", "subtitles.entity.axolotl.attack": "Axolotl ataca", "subtitles.entity.axolotl.death": "Axolotl el more", "subtitles.entity.axolotl.hurt": "Axolotl ferìo", "subtitles.entity.axolotl.idle_air": "Axolotl el petoła", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "Axolotl el sguàsa", "subtitles.entity.axolotl.swim": "Axolotl el noa", "subtitles.entity.bat.ambient": "Barbastreło el sbraja", "subtitles.entity.bat.death": "Barbastreło el more", "subtitles.entity.bat.hurt": "Barbastreło fer<PERSON>o", "subtitles.entity.bat.takeoff": "Barbastreło el zvoła", "subtitles.entity.bee.ambient": "Ava ła ziàoła", "subtitles.entity.bee.death": "Ava ła more", "subtitles.entity.bee.hurt": "<PERSON> ferìa", "subtitles.entity.bee.loop": "Ava la ziàola", "subtitles.entity.bee.loop_aggressive": "Ava ła ziàoła nervozamente", "subtitles.entity.bee.pollinate": "Ava ła ziàoła festozamente", "subtitles.entity.bee.sting": "Ava ła beca", "subtitles.entity.blaze.ambient": "Blaze el àrfia", "subtitles.entity.blaze.burn": "Blaze el sciopeta", "subtitles.entity.blaze.death": "Blaze el more", "subtitles.entity.blaze.hurt": "<PERSON> ferìo", "subtitles.entity.blaze.shoot": "Blaze el ataca", "subtitles.entity.boat.paddle_land": "Vogada", "subtitles.entity.boat.paddle_water": "Vogada", "subtitles.entity.bogged.ambient": "Paludà el rateza", "subtitles.entity.bogged.death": "Paludà el more", "subtitles.entity.bogged.hurt": "Paludà ferìo", "subtitles.entity.breeze.charge": "Brezze el carga", "subtitles.entity.breeze.death": "Breeze el more", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> el deflete", "subtitles.entity.breeze.hurt": "Breeze ferìo", "subtitles.entity.breeze.idle_air": "Breeze el svola", "subtitles.entity.breeze.idle_ground": "Breeze el furega", "subtitles.entity.breeze.inhale": "Breeze el inala", "subtitles.entity.breeze.jump": "Breeze el salta", "subtitles.entity.breeze.land": "Breeze el atera", "subtitles.entity.breeze.shoot": "Breeze el sbara", "subtitles.entity.breeze.slide": "Breeze el sbrissia", "subtitles.entity.breeze.whirl": "Breeze el se intrivela", "subtitles.entity.breeze.wind_burst": "Carga de vento la sciopa", "subtitles.entity.camel.ambient": "Camel grunts", "subtitles.entity.camel.dash": "Camel yeets", "subtitles.entity.camel.dash_ready": "Camel el se ciapa", "subtitles.entity.camel.death": "Camel el more", "subtitles.entity.camel.eat": "Camel el magna", "subtitles.entity.camel.hurt": "Camel fer<PERSON>o", "subtitles.entity.camel.saddle": "<PERSON><PERSON> parec<PERSON>", "subtitles.entity.camel.sit": "Camel el se senta", "subtitles.entity.camel.stand": "Camel el se leva su", "subtitles.entity.camel.step": "<PERSON><PERSON> de <PERSON>", "subtitles.entity.camel.step_sand": "Camel el pisteza", "subtitles.entity.cat.ambient": "Gato el znjàoła", "subtitles.entity.cat.beg_for_food": "Gato el sgnagola", "subtitles.entity.cat.death": "Gato el more", "subtitles.entity.cat.eat": "Gato el magna", "subtitles.entity.cat.hiss": "Gato el supia", "subtitles.entity.cat.hurt": "Gato ferìo", "subtitles.entity.cat.purr": "Gato el fuxe", "subtitles.entity.chicken.ambient": "Połastro el ciochiza", "subtitles.entity.chicken.death": "Połastro el more", "subtitles.entity.chicken.egg": "Połastro el depon ovo", "subtitles.entity.chicken.hurt": "<PERSON>ła<PERSON> fer<PERSON>", "subtitles.entity.cod.death": "Merluso el more", "subtitles.entity.cod.flop": "Merluso el se remena", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.cow.ambient": "Vaca ła muła", "subtitles.entity.cow.death": "Vaca ła more", "subtitles.entity.cow.hurt": "Vaca ferìa", "subtitles.entity.cow.milk": "Vaca monta", "subtitles.entity.creaking.activate": "Scrico el osserva", "subtitles.entity.creaking.ambient": "Scrico el scrica", "subtitles.entity.creaking.attack": "Scrico el ataca", "subtitles.entity.creaking.deactivate": "Scrico el se chieta", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Scrico el se ferma", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Scrico el se move", "subtitles.entity.creeper.death": "Creeper el more", "subtitles.entity.creeper.hurt": "Creeper ferìo", "subtitles.entity.creeper.primed": "Creeper el sìfoła", "subtitles.entity.dolphin.ambient": "V<PERSON><PERSON> de dolfin", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> el fiscia", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> el ataca", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> el more", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> manja", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON> el salta", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> el zuga", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> zbro<PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON> el noda", "subtitles.entity.donkey.ambient": "Àzeno el raja", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON>er<PERSON>", "subtitles.entity.donkey.chest": "Baùl de'l àzeno pareci<PERSON>", "subtitles.entity.donkey.death": "Àzeno el more", "subtitles.entity.donkey.eat": "Muso el magna", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Negà el bùłega", "subtitles.entity.drowned.ambient_water": "Negà el bùlega", "subtitles.entity.drowned.death": "Negà el more", "subtitles.entity.drowned.hurt": "Negà ferìo", "subtitles.entity.drowned.shoot": "Negà el tira ła forca", "subtitles.entity.drowned.step": "Negà el camina", "subtitles.entity.drowned.swim": "Negà el noda", "subtitles.entity.egg.throw": "<PERSON>vo tir<PERSON>", "subtitles.entity.elder_guardian.ambient": "Guardian antigo el zeme", "subtitles.entity.elder_guardian.ambient_land": "Guardian antigo el siga", "subtitles.entity.elder_guardian.curse": "Guardian antigo el małedise", "subtitles.entity.elder_guardian.death": "Guardian antigo el more", "subtitles.entity.elder_guardian.flop": "Guardian antigo el se remena", "subtitles.entity.elder_guardian.hurt": "Guardian antigo ferìo", "subtitles.entity.ender_dragon.ambient": "Dragon el ruze", "subtitles.entity.ender_dragon.death": "Dragon el more", "subtitles.entity.ender_dragon.flap": "Dragon el zbate łe ałe", "subtitles.entity.ender_dragon.growl": "Dragon el ruze", "subtitles.entity.ender_dragon.hurt": "Dragon ferìo", "subtitles.entity.ender_dragon.shoot": "Dragon el ataca", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> de ender el casca", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> de ender tirà", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> de ender ła zvoła", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> el fa \"vwoop\"", "subtitles.entity.enderman.death": "<PERSON><PERSON> el more", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> fer<PERSON>o", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON> el zbraja", "subtitles.entity.enderman.teleport": "Enderman el se tełetrasporta", "subtitles.entity.endermite.ambient": "Endermite ła strisa", "subtitles.entity.endermite.death": "Endermite ła more", "subtitles.entity.endermite.hurt": "Endermite ferìa", "subtitles.entity.evoker.ambient": "Evocador el mórmora", "subtitles.entity.evoker.cast_spell": "Evocador el tira un strigheso", "subtitles.entity.evoker.celebrate": "Evocador el se rałegra", "subtitles.entity.evoker.death": "Evocador el more", "subtitles.entity.evoker.hurt": "Evocador fer<PERSON>o", "subtitles.entity.evoker.prepare_attack": "Evocador el parecia el ataco", "subtitles.entity.evoker.prepare_summon": "Evocador el parecia l'evocasion", "subtitles.entity.evoker.prepare_wololo": "Evocador el parecia el incantamento", "subtitles.entity.evoker_fangs.attack": "Morsegada de ganase", "subtitles.entity.experience_orb.pickup": "Espariensa recavada", "subtitles.entity.firework_rocket.blast": "Rocheta pirot. ła sciopa", "subtitles.entity.firework_rocket.launch": "Rocheta pirot. tirada", "subtitles.entity.firework_rocket.twinkle": "Rocheta pirot. ła z<PERSON>ze", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "Gałejante retirà", "subtitles.entity.fishing_bobber.splash": "Zbrofe de'l gałezante", "subtitles.entity.fishing_bobber.throw": "Gałezante tirà", "subtitles.entity.fox.aggro": "Volpe ła se infuria", "subtitles.entity.fox.ambient": "Volpe ła scaina", "subtitles.entity.fox.bite": "Volpe ła mòrsega", "subtitles.entity.fox.death": "Volpe ła more", "subtitles.entity.fox.eat": "Volpe ła manja", "subtitles.entity.fox.hurt": "<PERSON><PERSON> ferìa", "subtitles.entity.fox.screech": "Volpe ła s<PERSON>de", "subtitles.entity.fox.sleep": "Volpe ła roncheza", "subtitles.entity.fox.sniff": "Volpe ła znaza", "subtitles.entity.fox.spit": "Volpe ła spua", "subtitles.entity.fox.teleport": "Volpe ła se tełetrasporta", "subtitles.entity.frog.ambient": "<PERSON><PERSON>", "subtitles.entity.frog.death": "Rana la more", "subtitles.entity.frog.eat": "Rana la manja", "subtitles.entity.frog.hurt": "<PERSON> ferìa", "subtitles.entity.frog.lay_spawn": "Rana depone ovi", "subtitles.entity.frog.long_jump": "<PERSON>", "subtitles.entity.generic.big_fall": "Calcossa el casca", "subtitles.entity.generic.burn": "<PERSON><PERSON>", "subtitles.entity.generic.death": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.generic.drink": "Z<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Esplozion", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "Calcosa l'é ferìa", "subtitles.entity.generic.small_fall": "Pìcoła cascada", "subtitles.entity.generic.splash": "Schiseti de àcua", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "Ghast el siga", "subtitles.entity.ghast.death": "Ghast el more", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> fer<PERSON><PERSON>", "subtitles.entity.ghast.shoot": "Ghast el ataca", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Soàxa luxénte inpenìa", "subtitles.entity.glow_item_frame.break": "Soàxa luxénte la se spaca", "subtitles.entity.glow_item_frame.place": "Soàxa luxén<PERSON> p<PERSON>", "subtitles.entity.glow_item_frame.remove_item": "Soàxa luxénte desvodada", "subtitles.entity.glow_item_frame.rotate_item": "Soàxa luxénte ła se volta", "subtitles.entity.glow_squid.ambient": "Całamar l<PERSON> el noa", "subtitles.entity.glow_squid.death": "Całamar l<PERSON> el more", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "subtitles.entity.glow_squid.squirt": "Całamar l<PERSON>énte el spùa inciostro", "subtitles.entity.goat.ambient": "Cavara la sbègola", "subtitles.entity.goat.death": "Cavara la more", "subtitles.entity.goat.eat": "Cavara la magna", "subtitles.entity.goat.horn_break": "Corno de Cavara se ronpe", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> ferìa", "subtitles.entity.goat.long_jump": "C<PERSON>ra la salta", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> monta", "subtitles.entity.goat.prepare_ram": "Cavara la pesta", "subtitles.entity.goat.ram_impact": "Cavara la carga", "subtitles.entity.goat.screaming.ambient": "Cavara la çiga", "subtitles.entity.goat.step": "Cavara la camina", "subtitles.entity.guardian.ambient": "Guardian el zeme", "subtitles.entity.guardian.ambient_land": "Guardian el siga", "subtitles.entity.guardian.attack": "Guadian el ataca", "subtitles.entity.guardian.death": "Guardian el more", "subtitles.entity.guardian.flop": "Guardian el se debate", "subtitles.entity.guardian.hurt": "Guardian ferìo", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "Ho<PERSON>n el ruxe", "subtitles.entity.hoglin.angry": "Hoglin el ruxe invełenà", "subtitles.entity.hoglin.attack": "Ho<PERSON>n l' ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON><PERSON><PERSON> in zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON>n el more", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> el scanpa", "subtitles.entity.hoglin.step": "Hoglin el camina", "subtitles.entity.horse.ambient": "Caval el nitrise", "subtitles.entity.horse.angry": "Caval el nitrise", "subtitles.entity.horse.armor": "Bardadura pareciada", "subtitles.entity.horse.breathe": "Caval el àrfia", "subtitles.entity.horse.death": "Caval el more", "subtitles.entity.horse.eat": "Caval el manja", "subtitles.entity.horse.gallop": "Caval el gałopa", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.horse.jump": "Caval el salta", "subtitles.entity.horse.saddle": "Seła pareciada", "subtitles.entity.husk.ambient": "Zonbi seco el ruze", "subtitles.entity.husk.converted_to_zombie": "Zonbi seco convertìo in zonbi", "subtitles.entity.husk.death": "Zonbi seco el more", "subtitles.entity.husk.hurt": "Zonbi seco ferìo", "subtitles.entity.illusioner.ambient": "Iłuzionista el mórmora", "subtitles.entity.illusioner.cast_spell": "Iłuzionista el tira un strigheso", "subtitles.entity.illusioner.death": "Iłuzionista el more", "subtitles.entity.illusioner.hurt": "Iłuzionista ferìo", "subtitles.entity.illusioner.mirror_move": "Iłuzionista el se tełetrasporta", "subtitles.entity.illusioner.prepare_blindness": "Iłuzionista el parecia vizion reduta", "subtitles.entity.illusioner.prepare_mirror": "Iłuzionista el parecia iłuzion", "subtitles.entity.iron_golem.attack": "Gołem de fero el ataca", "subtitles.entity.iron_golem.damage": "Go<PERSON>em de fero el se ronpe", "subtitles.entity.iron_golem.death": "Go<PERSON>em de fero el more", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON><PERSON> de fero ferìo", "subtitles.entity.iron_golem.repair": "Gołem de fero reparà", "subtitles.entity.item.break": "Ozeto spacà", "subtitles.entity.item.pickup": "Ozeto tolto su", "subtitles.entity.item_frame.add_item": "Corniza inpenìa", "subtitles.entity.item_frame.break": "Corniza ła se ronpe", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "Corniza dezvodada", "subtitles.entity.item_frame.rotate_item": "Corniza ła se volta", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Sciantizo el casca", "subtitles.entity.lightning_bolt.thunder": "Tron el ruze", "subtitles.entity.llama.ambient": "Łama el zbègoła", "subtitles.entity.llama.angry": "Łama che zbègoła nervozamente", "subtitles.entity.llama.chest": "Baùl de łama parecià", "subtitles.entity.llama.death": "Łama el more", "subtitles.entity.llama.eat": "Łama el manja", "subtitles.entity.llama.hurt": "Łama ferìo", "subtitles.entity.llama.spit": "Łama el spua", "subtitles.entity.llama.step": "Łama el camina", "subtitles.entity.llama.swag": "Łama decorà", "subtitles.entity.magma_cube.death": "Cubo de magma el more", "subtitles.entity.magma_cube.hurt": "Cubo de magma ferìo", "subtitles.entity.magma_cube.squish": "Cubo de magma el fa \"squish\"", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Carel in movimento", "subtitles.entity.mooshroom.convert": "Mooshroom ła se trasforma", "subtitles.entity.mooshroom.eat": "Mooshroom ła manja", "subtitles.entity.mooshroom.milk": "Mooshroom monta", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom monta malfidentemente", "subtitles.entity.mule.ambient": "Muło el raja", "subtitles.entity.mule.angry": "Muło el nidrise", "subtitles.entity.mule.chest": "Baùl de'l muło pareci<PERSON>", "subtitles.entity.mule.death": "Muło el more", "subtitles.entity.mule.eat": "Muło el magna", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "Cuadro spacà", "subtitles.entity.painting.place": "Cuadro postà", "subtitles.entity.panda.aggressive_ambient": "Panda el zbufa", "subtitles.entity.panda.ambient": "Panda el panteza", "subtitles.entity.panda.bite": "Panda el mòrsega", "subtitles.entity.panda.cant_breed": "Panda el se łanja", "subtitles.entity.panda.death": "Panda el more", "subtitles.entity.panda.eat": "Panda el manja", "subtitles.entity.panda.hurt": "Panda el colpise", "subtitles.entity.panda.pre_sneeze": "Panda el se grata el nazo", "subtitles.entity.panda.sneeze": "Panda el stranuda", "subtitles.entity.panda.step": "<PERSON><PERSON> de panda", "subtitles.entity.panda.worried_ambient": "Panda el ronja", "subtitles.entity.parrot.ambient": "Papagal el parla", "subtitles.entity.parrot.death": "Papagal el more", "subtitles.entity.parrot.eats": "Papagal el manja", "subtitles.entity.parrot.fly": "Papagal el zvoła", "subtitles.entity.parrot.hurts": "<PERSON><PERSON> ferìo", "subtitles.entity.parrot.imitate.blaze": "Papagal el arfia", "subtitles.entity.parrot.imitate.bogged": "Papagal el rateza", "subtitles.entity.parrot.imitate.breeze": "Parrot whirs", "subtitles.entity.parrot.imitate.creaking": "Papagal ła sìgoła", "subtitles.entity.parrot.imitate.creeper": "Papagal el sìfoła", "subtitles.entity.parrot.imitate.drowned": "Papagal el bùłega", "subtitles.entity.parrot.imitate.elder_guardian": "Papagal el zeme", "subtitles.entity.parrot.imitate.ender_dragon": "Papagal el ruze", "subtitles.entity.parrot.imitate.endermite": "Papagal el strisa", "subtitles.entity.parrot.imitate.evoker": "Papagal el mórmora", "subtitles.entity.parrot.imitate.ghast": "Papagal el cria", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.hoglin": "Papagal el ruxe", "subtitles.entity.parrot.imitate.husk": "Papagal el ruze", "subtitles.entity.parrot.imitate.illusioner": "Parrot murmurs", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON> el fa \"squish\"", "subtitles.entity.parrot.imitate.phantom": "Papagal el zbraja", "subtitles.entity.parrot.imitate.piglin": "Papagal el stranua", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.pillager": "Parrot murmurs", "subtitles.entity.parrot.imitate.ravager": "Papagal el grunja", "subtitles.entity.parrot.imitate.shulker": "Papagal el oserva", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "Papagal el rateza", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Papagal el rateza", "subtitles.entity.parrot.imitate.vex": "Papagal el vesa", "subtitles.entity.parrot.imitate.vindicator": "Papagal el mórmora", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> se lamenta", "subtitles.entity.parrot.imitate.witch": "Papagal el bùłega", "subtitles.entity.parrot.imitate.wither": "Papa<PERSON> el se rabise", "subtitles.entity.parrot.imitate.wither_skeleton": "Papagal el rateza", "subtitles.entity.parrot.imitate.zoglin": "Papagal el ruxe", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON> che bùłega", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON> che bùłega", "subtitles.entity.phantom.ambient": "Phantom el zbraja", "subtitles.entity.phantom.bite": "Phantom el mòrsega", "subtitles.entity.phantom.death": "Phantom el more", "subtitles.entity.phantom.flap": "Phantom el zbate łe ałe", "subtitles.entity.phantom.hurt": "Phantom ferìo", "subtitles.entity.phantom.swoop": "Phantom el pionba da baso", "subtitles.entity.pig.ambient": "Porsel el grunja", "subtitles.entity.pig.death": "Porsel el more", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.pig.saddle": "Seła pareciada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> el amira un ozeto", "subtitles.entity.piglin.ambient": "Piglin el zbufa", "subtitles.entity.piglin.angry": "Piglin el zbufa invełenà", "subtitles.entity.piglin.celebrate": "Piglin el fa festa", "subtitles.entity.piglin.converted_to_zombified": "Piglin convertìo in piglin zonbifegà", "subtitles.entity.piglin.death": "<PERSON><PERSON> el more", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> fer<PERSON>o", "subtitles.entity.piglin.jealous": "Piglin el zbufa invidioxamente", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> el se retira", "subtitles.entity.piglin.step": "Piglin el camina", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> bruto el zbufa", "subtitles.entity.piglin_brute.angry": "Piglin bruto el zbufa invełenà", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin bruto <PERSON> in piglin zonbifegà", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> bruto el crepa", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> bruto ferìo", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> bruto el camina", "subtitles.entity.pillager.ambient": "Sachejador el mórmora", "subtitles.entity.pillager.celebrate": "Sachejador el se rałegra", "subtitles.entity.pillager.death": "Sachejador el more", "subtitles.entity.pillager.hurt": "Sachejador el colpise", "subtitles.entity.player.attack.crit": "Ataco crìtego", "subtitles.entity.player.attack.knockback": "Ataco de rincuło", "subtitles.entity.player.attack.strong": "Ataco potente", "subtitles.entity.player.attack.sweep": "Vis-ciada", "subtitles.entity.player.attack.weak": "Ataco mauco", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Zugador el more", "subtitles.entity.player.freeze_hurt": "Zugador el se conxela", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> drio negarse", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> drio bruza<PERSON>", "subtitles.entity.player.levelup": "Zugador el monta de łivel", "subtitles.entity.player.teleport": "Zugador el se teletrasporta", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON> połar el ruze", "subtitles.entity.polar_bear.ambient_baby": "Orso połar el pétoła", "subtitles.entity.polar_bear.death": "<PERSON><PERSON> połar el more", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON> po<PERSON> fer<PERSON>o", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON> połar el ruze", "subtitles.entity.potion.splash": "Anpoła ła se frantuma", "subtitles.entity.potion.throw": "Anpoła tirada", "subtitles.entity.puffer_fish.blow_out": "Pese bała el se désfia", "subtitles.entity.puffer_fish.blow_up": "Pese bała el se ìnfia", "subtitles.entity.puffer_fish.death": "Pese bała el more", "subtitles.entity.puffer_fish.flop": "Pese bała el se remena", "subtitles.entity.puffer_fish.hurt": "Pese bała ferìo", "subtitles.entity.puffer_fish.sting": "Pese bała el ponze", "subtitles.entity.rabbit.ambient": "Conejo el siga", "subtitles.entity.rabbit.attack": "Conejo el ataca", "subtitles.entity.rabbit.death": "Conejo el more", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.rabbit.jump": "Conejo el salta", "subtitles.entity.ravager.ambient": "Devastador el grunja", "subtitles.entity.ravager.attack": "Devastador el mòrsega", "subtitles.entity.ravager.celebrate": "Devastador el se rałegra", "subtitles.entity.ravager.death": "Devastador el more", "subtitles.entity.ravager.hurt": "Devastador fer<PERSON>o", "subtitles.entity.ravager.roar": "Devastador el ruze", "subtitles.entity.ravager.step": "Devastador el camina", "subtitles.entity.ravager.stunned": "Devastador stor<PERSON><PERSON>o", "subtitles.entity.salmon.death": "Salmon el more", "subtitles.entity.salmon.flop": "Salmon el se remena", "subtitles.entity.salmon.hurt": "Salmon ferìo", "subtitles.entity.sheep.ambient": "Pégora ła zbègoła", "subtitles.entity.sheep.death": "Piégora ła more", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON><PERSON> ferìa", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> el oserva", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> el se sara", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> el more", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> el se verze", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> el ataca", "subtitles.entity.shulker.teleport": "<PERSON><PERSON>er el se tełetrasporta", "subtitles.entity.shulker_bullet.hit": "Projetiłe de schulker el s-ciopa", "subtitles.entity.shulker_bullet.hurt": "Projé<PERSON>łe de shulker spacà", "subtitles.entity.silverfish.ambient": "Pese arzentà el sìfoła", "subtitles.entity.silverfish.death": "Pese arzentà el more", "subtitles.entity.silverfish.hurt": "Pese arzentà ferìo", "subtitles.entity.skeleton.ambient": "Schéłetro el rateza", "subtitles.entity.skeleton.converted_to_stray": "Schèletro convertìo in viandante", "subtitles.entity.skeleton.death": "Schèłetro el more", "subtitles.entity.skeleton.hurt": "Schèłetro ferìo", "subtitles.entity.skeleton.shoot": "Schéłetro el ataca", "subtitles.entity.skeleton_horse.ambient": "Caval schèłetro el siga", "subtitles.entity.skeleton_horse.death": "Caval schèłetro el more", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON> s<PERSON>ł<PERSON>ro ferìo", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Caval schèłetro el noda", "subtitles.entity.slime.attack": "Zeładina ła ataca", "subtitles.entity.slime.death": "Zeładina ła more", "subtitles.entity.slime.hurt": "Ze<PERSON>dina ferìa", "subtitles.entity.slime.squish": "<PERSON><PERSON>ła<PERSON> ła fa \"squish\"", "subtitles.entity.sniffer.death": "Snasador el more", "subtitles.entity.sniffer.digging": "Snasador el scava", "subtitles.entity.sniffer.digging_stop": "Snasador el se leva su", "subtitles.entity.sniffer.drop_seed": "Snasador el lassa na somensa", "subtitles.entity.sniffer.eat": "Snasador el magna", "subtitles.entity.sniffer.egg_crack": "Ovo de snasador el se spaca", "subtitles.entity.sniffer.egg_hatch": "Ovo de snasador el se verze", "subtitles.entity.sniffer.happy": "Snasador el se gode", "subtitles.entity.sniffer.hurt": "Snasador ferìo", "subtitles.entity.sniffer.idle": "Snasador el ruze", "subtitles.entity.sniffer.scenting": "Snasador el nasa", "subtitles.entity.sniffer.searching": "Snasador el çerca", "subtitles.entity.sniffer.sniffing": "Snasador el snasa", "subtitles.entity.sniffer.step": "Snasador el camina", "subtitles.entity.snow_golem.death": "Go<PERSON>em de neve el more", "subtitles.entity.snow_golem.hurt": "<PERSON><PERSON><PERSON> de neve ferìo", "subtitles.entity.snowball.throw": "Bałoco tirà", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON> el sìfoła", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON> el more", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON>er<PERSON>", "subtitles.entity.squid.ambient": "Całamar el noda", "subtitles.entity.squid.death": "Całamar el more", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON>er<PERSON>", "subtitles.entity.squid.squirt": "Całamar el spùa inciostro", "subtitles.entity.stray.ambient": "Viandante el rateza", "subtitles.entity.stray.death": "Viandante el more", "subtitles.entity.stray.hurt": "Viandante ferìo", "subtitles.entity.strider.death": "Caminador el more", "subtitles.entity.strider.eat": "Caminador el magna", "subtitles.entity.strider.happy": "Caminador el gorghiza", "subtitles.entity.strider.hurt": "Caminador ferìo", "subtitles.entity.strider.idle": "Sigo de caminador", "subtitles.entity.strider.retreat": "Caminador el scanpa", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> el more", "subtitles.entity.tadpole.flop": "<PERSON>irin che'l se remena", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.tnt.primed": "TNT inpisada", "subtitles.entity.tropical_fish.death": "Pese tropigal el crepa", "subtitles.entity.tropical_fish.flop": "Pese tropigal el se ramena", "subtitles.entity.tropical_fish.hurt": "Pese tropigal ferìo", "subtitles.entity.turtle.ambient_land": "Verso de tartaruga", "subtitles.entity.turtle.death": "Tartaruga ła more", "subtitles.entity.turtle.death_baby": "Butin de tartaruga el more", "subtitles.entity.turtle.egg_break": "Ovo de tartaruga el se spaca", "subtitles.entity.turtle.egg_crack": "Ovo de tartaruga el se crepa", "subtitles.entity.turtle.egg_hatch": "Ovo de tartaruga el se verze", "subtitles.entity.turtle.hurt": "Tartaruga ferìa", "subtitles.entity.turtle.hurt_baby": "<PERSON>in de tartaruga ferìo", "subtitles.entity.turtle.lay_egg": "Tartaruga ła depone un ovo", "subtitles.entity.turtle.shamble": "Tartaruga ła ranca", "subtitles.entity.turtle.shamble_baby": "Butin de tartaruga el ranca", "subtitles.entity.turtle.swim": "Tartaruga ła noda", "subtitles.entity.vex.ambient": "Vex el vesa", "subtitles.entity.vex.charge": "Vex el cria", "subtitles.entity.vex.death": "Vex el more", "subtitles.entity.vex.hurt": "Vex ferìo", "subtitles.entity.villager.ambient": "Paesan el borbota", "subtitles.entity.villager.celebrate": "Paesan el se rałegra", "subtitles.entity.villager.death": "Paesan el more", "subtitles.entity.villager.hurt": "<PERSON><PERSON> fer<PERSON>o", "subtitles.entity.villager.no": "Paesan el descorda", "subtitles.entity.villager.trade": "Paesan el scanbia", "subtitles.entity.villager.work_armorer": "Corasar el łaora", "subtitles.entity.villager.work_butcher": "Becar el łaora", "subtitles.entity.villager.work_cartographer": "Cartògrafo el łaora", "subtitles.entity.villager.work_cleric": "Cèrego el łaora", "subtitles.entity.villager.work_farmer": "Contadin el łaora", "subtitles.entity.villager.work_fisherman": "Pescador el łaora", "subtitles.entity.villager.work_fletcher": "Fàbrega frese el łaora", "subtitles.entity.villager.work_leatherworker": "Pełatier el łaora", "subtitles.entity.villager.work_librarian": "Łibrar el łaora", "subtitles.entity.villager.work_mason": "Murador el łaora", "subtitles.entity.villager.work_shepherd": "<PERSON>", "subtitles.entity.villager.work_toolsmith": "Fàbrica arte el łaora", "subtitles.entity.villager.work_weaponsmith": "Armarol el łaora", "subtitles.entity.villager.yes": "Paesan el concorda", "subtitles.entity.vindicator.ambient": "Vendegador el mórmora", "subtitles.entity.vindicator.celebrate": "Vendegador el se rałegra", "subtitles.entity.vindicator.death": "Vendegador el more", "subtitles.entity.vindicator.hurt": "Vendegador fer<PERSON>o", "subtitles.entity.wandering_trader.ambient": "Marcante anbułante el borbota", "subtitles.entity.wandering_trader.death": "Marcante anbułante el more", "subtitles.entity.wandering_trader.disappeared": "Marcante anbułante el sparise", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON> an<PERSON> el beve late", "subtitles.entity.wandering_trader.drink_potion": "Marcante anbu<PERSON>nte el beve posion", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON> an<PERSON> fer<PERSON>o", "subtitles.entity.wandering_trader.no": "Marcante anbułante el descorda", "subtitles.entity.wandering_trader.reappeared": "Marcante anbułante el conparise", "subtitles.entity.wandering_trader.trade": "Marcante anbu<PERSON>nte el scanbia", "subtitles.entity.wandering_trader.yes": "Marcante anbułante el concorda", "subtitles.entity.warden.agitated": "Sorvejante geme nervozamente", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON><PERSON> se lamenta", "subtitles.entity.warden.angry": "Sorvejante se rabia", "subtitles.entity.warden.attack_impact": "Sorvejante el colpise", "subtitles.entity.warden.death": "Sorvejante el more", "subtitles.entity.warden.dig": "Sorvejante el scava", "subtitles.entity.warden.emerge": "Sorvejante el emerge", "subtitles.entity.warden.heartbeat": "Core de Sorvejante el bate", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.warden.listening": "Sorvejante el scopre", "subtitles.entity.warden.listening_angry": "Sorvejante el scopre nervozamente", "subtitles.entity.warden.nearby_close": "Sorvejante se avisina", "subtitles.entity.warden.nearby_closer": "Sorvejante el avansa", "subtitles.entity.warden.nearby_closest": "Sorvejante se massa visin", "subtitles.entity.warden.roar": "Sorvejante el ruze", "subtitles.entity.warden.sniff": "Sorvejante el nusa", "subtitles.entity.warden.sonic_boom": "Sorvejante invia ataco sonico", "subtitles.entity.warden.sonic_charge": "Sorvejante el carica", "subtitles.entity.warden.step": "Sorvejante el camina", "subtitles.entity.warden.tendril_clicks": "Antene de'l Sorvejante sciocano", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON><PERSON> ła ridacia", "subtitles.entity.witch.celebrate": "Strìa ła se rałegra", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON><PERSON> ła more", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON><PERSON> ła beve", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON><PERSON> ferìa", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON>a ła tira posion", "subtitles.entity.wither.ambient": "Wither el se rabise", "subtitles.entity.wither.death": "Wither el more", "subtitles.entity.wither.hurt": "<PERSON>er fer<PERSON>o", "subtitles.entity.wither.shoot": "Wither el ataca", "subtitles.entity.wither.spawn": "Wither el se ł<PERSON>", "subtitles.entity.wither_skeleton.ambient": "Schèłetro wither el rateza", "subtitles.entity.wither_skeleton.death": "Schèłetro wither el more", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wither ferìo", "subtitles.entity.wolf.ambient": "Łovo el panteza", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "Łovo el more", "subtitles.entity.wolf.growl": "Łovo el ruze", "subtitles.entity.wolf.hurt": "Łovo ferìo", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "Łovo el se scorla", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "Zoglin el ruxe", "subtitles.entity.zoglin.angry": "Zoglin el ruxe invełenà", "subtitles.entity.zoglin.attack": "Zoglin el ataca", "subtitles.entity.zoglin.death": "Zoglin el more", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.zoglin.step": "Zoglin el camina", "subtitles.entity.zombie.ambient": "Zonbi el ruze", "subtitles.entity.zombie.attack_wooden_door": "Porta ła trabała", "subtitles.entity.zombie.break_wooden_door": "Porta ła se scavesa", "subtitles.entity.zombie.converted_to_drowned": "Zonbi convertìo in negà", "subtitles.entity.zombie.death": "Zonbi el more", "subtitles.entity.zombie.destroy_egg": "Vovo de tartarugà pestà", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>o", "subtitles.entity.zombie.infect": "Zonbi el infeta", "subtitles.entity.zombie_horse.ambient": "Caval zonbi el siga", "subtitles.entity.zombie_horse.death": "Caval zonbi el more", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> zonbi fer<PERSON>o", "subtitles.entity.zombie_villager.ambient": "Zonbi paesan el ruze", "subtitles.entity.zombie_villager.converted": "Paesan zonbi el ciàcola", "subtitles.entity.zombie_villager.cure": "Paesan zonbi el snarufa", "subtitles.entity.zombie_villager.death": "Paesan zonbi el more", "subtitles.entity.zombie_villager.hurt": "<PERSON><PERSON> zonbi ferìo", "subtitles.entity.zombified_piglin.ambient": "Piglin zonbifegà el grugna", "subtitles.entity.zombified_piglin.angry": "Piglin zonbifegà el grugna", "subtitles.entity.zombified_piglin.death": "Piglin zonbifegà el more", "subtitles.entity.zombified_piglin.hurt": "<PERSON>lin zonbifegà ferìo", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "Ciamada de corno", "subtitles.item.armor.equip": "Ozeto doparà", "subtitles.item.armor.equip_chain": "Armadura de maja ła tìntina", "subtitles.item.armor.equip_diamond": "Armadura de diamante ła tìmpana", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON><PERSON>e elytre", "subtitles.item.armor.equip_gold": "Armadura de oro ła sferaja", "subtitles.item.armor.equip_iron": "Armadura de fero ła sferaja", "subtitles.item.armor.equip_leather": "Armadura de corame ła fruscheza", "subtitles.item.armor.equip_netherite": "Armadura de netherite ła sferaja", "subtitles.item.armor.equip_turtle": "Sguso de tartaruga el resona", "subtitles.item.armor.equip_wolf": "Wolf Armor is fastened", "subtitles.item.armor.unequip_wolf": "Wolf Armor snips away", "subtitles.item.axe.scrape": "<PERSON><PERSON> s<PERSON>a", "subtitles.item.axe.strip": "Manara la descortega", "subtitles.item.axe.wax_off": "Decerà", "subtitles.item.bone_meal.use": "<PERSON><PERSON> de ossi la fruscheza", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON> łe fruscheza", "subtitles.item.book.put": "Łibro el tonfa", "subtitles.item.bottle.empty": "Anpoła svodada", "subtitles.item.bottle.fill": "Anpoła inpinida", "subtitles.item.brush.brushing.generic": "Bruschinar", "subtitles.item.brush.brushing.gravel": "Bruschinar de giara", "subtitles.item.brush.brushing.gravel.complete": "Giara buschinada", "subtitles.item.brush.brushing.sand": "Bruschinar de sabia", "subtitles.item.brush.brushing.sand.complete": "Sabia bruschinada", "subtitles.item.bucket.empty": "<PERSON><PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl ciapà", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> br<PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.drop_contents": "Saca svodà", "subtitles.item.bundle.insert": "Oggetto insacà", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.remove_one": "<PERSON><PERSON><PERSON> rimosso", "subtitles.item.chorus_fruit.teleport": "Zugador el se tełetrasporta", "subtitles.item.crop.plant": "Coltivasion piantada", "subtitles.item.crossbow.charge": "Cargamento de bałestra", "subtitles.item.crossbow.hit": "Fresa ła colpise", "subtitles.item.crossbow.load": "Bałestra cargada", "subtitles.item.crossbow.shoot": "Tiro de bałestra", "subtitles.item.dye.use": "Pitura inpitura", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Bała de fogo tirada", "subtitles.item.flintandsteel.use": "Batifogo <PERSON>", "subtitles.item.glow_ink_sac.use": "Saca de inciostro luxénte schicià", "subtitles.item.goat_horn.play": "Corno de Cavara el sona", "subtitles.item.hoe.till": "Sapa ła ara", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "Incerà", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Saca de inciostro schicià", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Bùsola de magnetite inpostada su la magnetite", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Coltivasion piantada", "subtitles.item.ominous_bottle.dispose": "Anpola la se spaca", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Sizore doparade", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Scudo el para", "subtitles.item.shovel.flatten": "Baìl el zguałiva", "subtitles.item.spyglass.stop_using": "Canocial el se retràe", "subtitles.item.spyglass.use": "Canocial el se slonga", "subtitles.item.totem.use": "Totem ativà", "subtitles.item.trident.hit": "Forca ła inpira", "subtitles.item.trident.hit_ground": "Forca ła trèmoła", "subtitles.item.trident.return": "Forca ła vien de volta", "subtitles.item.trident.riptide": "Forca ła sfresa", "subtitles.item.trident.throw": "Forca ła scrìnchena", "subtitles.item.trident.thunder": "Forca ła trona", "subtitles.item.wolf_armor.break": "Armadura par lovo la se spaca", "subtitles.item.wolf_armor.crack": "Armadura par lovo la se crepa", "subtitles.item.wolf_armor.damage": "Armadura par lovo la ciapa dano", "subtitles.item.wolf_armor.repair": "Armadura par lovo la se repara", "subtitles.particle.soul_escape": "Ànema la scanpa", "subtitles.ui.cartography_table.take_result": "Mapa dexegnada", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.weather.rain": "<PERSON><PERSON>", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "Piassè informasion", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "Senpre", "team.collision.never": "<PERSON>", "team.collision.pushOtherTeams": "Urta menbri de antre scuadre", "team.collision.pushOwnTeam": "<PERSON>rta menbri de ła propia scuadra", "team.notFound": "Scuadra mìa conosesta: %s", "team.visibility.always": "Senpre", "team.visibility.hideForOtherTeams": "Sconta par łe antre scuadre", "team.visibility.hideForOwnTeam": "Sconta par ła propia scuadra", "team.visibility.never": "<PERSON>", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (opsional)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Conossar el perfil zenaral de le prestasion de Minecraft el ne juta a datar e otimizar el zugo par na vasta sorta de dispositivi e sistemi operadivi.\nLa varsion del zugo l'é inclusa par jutarne conparar le prestasion del parfil par mejor varsion de Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (nesesario)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Saver come i zugadori i zuga a Minecraft (che modalità de zugo, client o server modadi, e varsion de zugo) el ne dà la possibilità de fogalizarne sui mejoramenti de zugo in aree che i zugadori i ghe ten de pì.\nEl evento \"Mondo Cargà\" l'é conpagnà co' l'evento \"Mondo descargà\" par calcolar la durada de le session de zugo.", "telemetry.event.world_loaded.title": "<PERSON><PERSON>", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "<PERSON>", "telemetry.property.client_id.title": "ID client", "telemetry.property.client_modded.title": "Client modà", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "Modałità", "telemetry.property.game_version.title": "Version del zogo", "telemetry.property.launcher_name.title": "Nome del launcher", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "ID session de Minecraft", "telemetry.property.new_world.title": "<PERSON><PERSON>", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Sistema Operadivo", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Piataforma", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Distansa de renderizasion", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server modà", "telemetry.property.server_type.title": "Tipo de'l server", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "ID de utente", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "ID de Session del Mondo", "telemetry_info.button.give_feedback": "Manda opinion", "telemetry_info.button.privacy_statement": "Informativa revardo la privacy", "telemetry_info.button.show_data": "<PERSON>ra i me dati", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Incluso data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "ID de test non valido", "test_instance.description.no_test": "No ghe xe 'sto test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "Sistema a 32 bi relevà: cuesto el podarìa inpedirte de zugar in futuro parché el deventarà necessario un sistema a 64 bit!", "title.32bit.deprecation.realms": "Minecraft el dimandarà presto un sistema a 64 bit, inpedéndote de zugar o de doparar Realms su 'sto desposidivo. Te gavarè da scancelar manualmente calsìase iscrision a Realms.", "title.32bit.deprecation.realms.check": "No sta' pì mostrar 'sta schermada", "title.32bit.deprecation.realms.header": "Sistema a 32 bit relevà", "title.credits": "Copyright de Mojang AB. No distribuir!", "title.multiplayer.disabled": "El multizugador l'é desabilità. Par piaser controla le inpostasion de'l launcher.", "title.multiplayer.disabled.banned.name": "Te ghe da cambiar nome prima de poder zugar online", "title.multiplayer.disabled.banned.permanent": "Tì account se sospeso da el zogo online parmanentemente", "title.multiplayer.disabled.banned.temporary": "Tì account se sospeso da el zogo online tenponareamente", "title.multiplayer.lan": "Multizugador (LAN)", "title.multiplayer.other": "Multizugador (server de terse parte)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON>r (Realms)", "title.singleplayer": "<PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Prefiso, %s%2$s ancora %s e %1$s in fin %s e anca %1$s ancora!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "ciao %", "translation.test.invalid2": "ciao %s", "translation.test.none": "<PERSON>iao, mondo!", "translation.test.world": "mondo", "trim_material.minecraft.amethyst": "De ametista", "trim_material.minecraft.copper": "<PERSON>", "trim_material.minecraft.diamond": "De brilante", "trim_material.minecraft.emerald": "<PERSON>", "trim_material.minecraft.gold": "De oro", "trim_material.minecraft.iron": "De fero", "trim_material.minecraft.lapis": "De lapis", "trim_material.minecraft.netherite": "De netherite", "trim_material.minecraft.quartz": "De cuarso", "trim_material.minecraft.redstone": "De redstone", "trim_material.minecraft.resin": "De rasa", "trim_pattern.minecraft.bolt": "Ornamento d'armadura <PERSON>", "trim_pattern.minecraft.coast": "Ornamento d'armadura Costera", "trim_pattern.minecraft.dune": "Ornamento d'armadura Duna", "trim_pattern.minecraft.eye": "Ornamento d'armadura <PERSON>", "trim_pattern.minecraft.flow": "Ornamento d'armadura Corivo", "trim_pattern.minecraft.host": "Ornamento d'armadura Foresto", "trim_pattern.minecraft.raiser": "Ornamento d'armadura Arlevador", "trim_pattern.minecraft.rib": "Ornamento d'armadura Coste", "trim_pattern.minecraft.sentry": "Ornamento d'armadura Sentinela", "trim_pattern.minecraft.shaper": "Ornamento d'armadura Modelante", "trim_pattern.minecraft.silence": "Ornamento d'armadura Silensio", "trim_pattern.minecraft.snout": "Ornamento d'armad<PERSON>", "trim_pattern.minecraft.spire": "Ornamento d'armadura Pincia", "trim_pattern.minecraft.tide": "Ornamento d'armadura Sèvente", "trim_pattern.minecraft.vex": "Ornamento d'armadura Vex", "trim_pattern.minecraft.ward": "Ornamento d'armadura Sorvejante", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Trim", "trim_pattern.minecraft.wild": "Ornamento d'armadura Selvàdego", "tutorial.bundleInsert.description": "Clic destro par zontar ojeti", "tutorial.bundleInsert.title": "Dòpara un sacheto", "tutorial.craft_planks.description": "El resetario el pol èsar de juto", "tutorial.craft_planks.title": "Fàbrica de łe ase", "tutorial.find_tree.description": "Colpisi el łenjo par arbinarlo", "tutorial.find_tree.title": "Cata un àlbaro", "tutorial.look.description": "Dòpara el mouse par voltarte", "tutorial.look.title": "<PERSON><PERSON><PERSON><PERSON>", "tutorial.move.description": "Salta co' %s", "tutorial.move.title": "Spòstete co' %s, %s, %s e %s", "tutorial.open_inventory.description": "Struca %s", "tutorial.open_inventory.title": "Ver<PERSON> el to inventario", "tutorial.punch_tree.description": "Tien strucà %s", "tutorial.punch_tree.title": "Desfa el àlbaro", "tutorial.socialInteractions.description": "Struca %s par vèrzare", "tutorial.socialInteractions.title": "Interasion soçiale", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}