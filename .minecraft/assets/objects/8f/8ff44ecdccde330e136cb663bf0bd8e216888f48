{"accessibility.onboarding.accessibility.button": "Pieejamības iestatījumi...", "accessibility.onboarding.screen.narrator": "Nospied tausti<PERSON><PERSON>, lai ieslēgtu diktoru", "accessibility.onboarding.screen.title": "Sveicināts Minecraft!\n\nVai vēlies ieslēgt Diktoru vai doties uz Pieejamības iestatījumiem?", "addServer.add": "Gatavs", "addServer.enterIp": "Servera Adrese", "addServer.enterName": "<PERSON><PERSON>", "addServer.resourcePack": "<PERSON>a resursu pakas", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON>", "addServer.title": "Rediģēt Servera Informāciju", "advMode.command": "<PERSON><PERSON><PERSON> k<PERSON>", "advMode.mode": "Režīms", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.conditional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Impulss", "advMode.mode.redstoneTriggered": "Nepieciešams Redstone", "advMode.mode.sequence": "Ķēde", "advMode.mode.unconditional": "Beznosacījuma", "advMode.notAllowed": "<PERSON><PERSON><PERSON><PERSON><PERSON> op spēlētājam radošajā režī<PERSON>ā", "advMode.notEnabled": "<PERSON><PERSON><PERSON> <PERSON><PERSON> komandu bloki nav atļauti", "advMode.previousOutput": "Iepriekšējais izvads", "advMode.setCommand": "Piešķirt blokam konsoles komandu", "advMode.setCommand.success": "Komanda iestatīta: %s", "advMode.trackOutput": "Treķera izvad<PERSON>šana", "advMode.triggering": "Tiek iz<PERSON>", "advMode.type": "Tips", "advancement.advancementNotFound": "Nezināms sasniegums: %s", "advancements.adventure.adventuring_time.description": "Atklāj visas biomas", "advancements.adventure.adventuring_time.title": "Piedzīvojumu laiks", "advancements.adventure.arbalistic.description": "Nogalini piecas unikālas radības ar vienu arbaleta <PERSON>nu", "advancements.adventure.arbalistic.title": "Arbalētika", "advancements.adventure.avoid_vibration.description": "Lavies Sculk sensora vai uzrauga tuvumā, lai tie tevi nepa<PERSON>tu", "advancements.adventure.avoid_vibration.title": "Sneak 100", "advancements.adventure.blowback.description": "<PERSON><PERSON><PERSON> vēsmu ar at<PERSON><PERSON><PERSON> vē<PERSON><PERSON> vēja lādi<PERSON>u", "advancements.adventure.blowback.title": "Pretplūsma", "advancements.adventure.brush_armadillo.description": "<PERSON>eg<PERSON><PERSON><PERSON> bruņ<PERSON> zvīņas no bruņneša, i<PERSON><PERSON><PERSON><PERSON> suku", "advancements.adventure.brush_armadillo.title": "Vai tik tās nav zvīņas?", "advancements.adventure.bullseye.description": "Trā<PERSON> vidū Mērķa blokam no vismaz 30 metru attāluma", "advancements.adventure.bullseye.title": "Tieši centrā", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Uztaisiet izdekorētu podu no 4 keramikas šķembām", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Uzmanīga restaurācija", "advancements.adventure.crafters_crafting_crafters.description": "Atrodies amatnieka tuvumā, kad tas izveido amatnieku", "advancements.adventure.crafters_crafting_crafters.title": "Amatniecības <PERSON>", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kritienā nokrist no pasaules aug<PERSON><PERSON> (b<PERSON><PERSON><PERSON><PERSON>) līdz pašai apakšai un izdzīvot", "advancements.adventure.fall_from_world_height.title": "Alas & Klintis", "advancements.adventure.heart_transplanter.description": "Novietojiet Čīksteņa <PERSON>di pareizajā pozīcijā starp diviem bālā ozola baļķa blokiem", "advancements.adventure.heart_transplanter.title": "Sirds ķirurgs", "advancements.adventure.hero_of_the_village.description": "Veiksmīgi aizsargāt ciemu no <PERSON>ebrukuma", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Ielec Medus blo<PERSON>ā, lai mī<PERSON><PERSON><PERSON><PERSON> kritienu", "advancements.adventure.honey_block_slide.title": "Lipīga situācija", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON>", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> medn<PERSON>s", "advancements.adventure.kill_all_mobs.description": "Nogalini vienu monstru no katra monstra veida", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> medn<PERSON>s", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON><PERSON> rad<PERSON><PERSON> sculk katalizatora tuvumā", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON>", "advancements.adventure.lighten_up.description": "Noskrā<PERSON>ē vara spuldzi ar cirvi, lai padar<PERSON> to spožā<PERSON>", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Pasargā ciematnieku no nevēlama šoka, neko <PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON> soli i<PERSON><PERSON><PERSON><PERSON><PERSON> kamba<PERSON>", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(u) i<PERSON><PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> ar arbalet<PERSON>", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.overoverkill.description": "Izdari 50 sir<PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON> vienā sitienā i<PERSON> vāli", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Atdzīvini pļavas ar plašu atskaņotāja skaņām", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON>zlasiet Noslīpētā grāmatas plaukta jaudas signā<PERSON> i<PERSON> salīdzin<PERSON>āju", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.revaulting.description": "Atslēdz draudīgo seifu ar draudīgo i<PERSON>ma atslēgu", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.root.description": "Piedzī<PERSON>jumi, izpēte un kaujas", "advancements.adventure.root.title": "Piedzīvojums", "advancements.adventure.salvage_sherd.description": "Noberz aizdomīgu bloku lai iegūtu keramikas šķembu", "advancements.adventure.salvage_sherd.title": "Palieku respektē<PERSON>", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON><PERSON><PERSON> jeb<PERSON>m ar bultu", "advancements.adventure.shoot_arrow.title": "Ņem loku", "advancements.adventure.sleep_in_bed.description": "<PERSON>uli gultā lai mainītu savu atdzimšanas vietu", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON> sa<PERSON>", "advancements.adventure.sniper_duel.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> skeletu vismaz no 50 metru attāluma", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON><PERSON> duelis", "advancements.adventure.spyglass_at_dragon.description": "Paskatieties uz Ender Pūķi caur tālskati", "advancements.adventure.spyglass_at_dragon.title": "Vai tā ir mana lid<PERSON>na?", "advancements.adventure.spyglass_at_ghast.description": "Paskatieties uz ghast caur tālskati", "advancements.adventure.spyglass_at_ghast.title": "Vai tas ir balons?", "advancements.adventure.spyglass_at_parrot.description": "Paskatieties uz papagaili caur tālskati", "advancements.adventure.spyglass_at_parrot.title": "Vai tas ir putns?", "advancements.adventure.summon_iron_golem.description": "Izveido d<PERSON> go<PERSON>, lai palī<PERSON><PERSON><PERSON><PERSON> a<PERSON> ciemu", "advancements.adventure.summon_iron_golem.title": "<PERSON>al<PERSON><PERSON> pal<PERSON>", "advancements.adventure.throw_trident.description": "Met trijž<PERSON>uri uz kaut ko.\nPiezīme: Mest projām savu vienīgo ieroci nav laba ideja.", "advancements.adventure.throw_trident.title": "Aizmešanas joks", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON><PERSON> totēmu lai apkrāptu nāvi", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trade.description": "Veiksmīgi tirgojies ar ciemata iedzīvotāju", "advancements.adventure.trade.title": "Kas pa darī<PERSON><PERSON>!", "advancements.adventure.trade_at_world_height.description": "Veic darījumu ar ciematnieku būvlimita augstumā", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Lietojiet šos kalēju šablonus vismaz vienu reizi: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Riba, Uzraugs, Klusums, Vekss, <PERSON><PERSON><PERSON><PERSON>, Atradējs", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> s<PERSON>u", "advancements.adventure.trim_with_any_armor_pattern.description": "Izveido ornamentētas bruņas uz kalēja galda", "advancements.adventure.trim_with_any_armor_pattern.title": "Izveido jaunu izskatu", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> divus Fantomus ar caur<PERSON><PERSON><PERSON> bultu", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON>, Viena Bulta", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON><PERSON> at<PERSON> seifam", "advancements.adventure.under_lock_and_key.title": "<PERSON>z slēdzenes un atslēgas", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON><PERSON> kompasu uz dzīslakmenss", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Mel<PERSON> B<PERSON>", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON>per ciema iedzīvotājam ar zibeni", "advancements.adventure.very_very_frightening.title": "Ļoti, <PERSON><PERSON><PERSON> bi<PERSON>i", "advancements.adventure.voluntary_exile.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON> ka<PERSON>.\n<PERSON><PERSON><PERSON><PERSON><PERSON>, pēc tam, kādu laiku turies no ciemiem pa gabalu...", "advancements.adventure.voluntary_exile.title": "Brīvpr<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Staigā pa <PERSON> sniegu... tajā ne<PERSON>t", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Viegls kā trusis", "advancements.adventure.who_needs_rockets.description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>, lai paceltu sevi 8 bloku augstumā", "advancements.adventure.who_needs_rockets.title": "Kam vajadz<PERSON>gas raķetes?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON>ā<PERSON> nogar<PERSON> viņa paša z<PERSON>", "advancements.adventure.whos_the_pillager_now.title": "<PERSON><PERSON><PERSON> ir <PERSON><PERSON>?", "advancements.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka šeit nekā nav...", "advancements.end.dragon_breath.description": "Savāc pūķa elpu stikla pudelē", "advancements.end.dragon_breath.title": "Tev vajag piparm<PERSON>tru", "advancements.end.dragon_egg.description": "<PERSON><PERSON><PERSON> pūķa olu", "advancements.end.dragon_egg.title": "<PERSON>ā<PERSON><PERSON><PERSON> paa<PERSON>ze", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON> elitru", "advancements.end.elytra.title": "<PERSON><PERSON><PERSON> ir <PERSON>", "advancements.end.enter_end_gateway.description": "Pamest salu", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.end.find_end_city.description": "Ej tik iek<PERSON>, kas varētu notikt?", "advancements.end.find_end_city.title": "<PERSON><PERSON><PERSON><PERSON> sp<PERSON><PERSON> be<PERSON>s", "advancements.end.kill_dragon.description": "Lai <PERSON>s", "advancements.end.kill_dragon.title": "Atbrīvo End", "advancements.end.levitate.description": "Lidinies 50 blokus uz augšu no shulker uzbrukumiem", "advancements.end.levitate.title": "Lielisks skats no šejienes", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> radi <PERSON> pūķi", "advancements.end.respawn_dragon.title": "Beigas... Atkal...", "advancements.end.root.description": "Vai sākums?", "advancements.end.root.title": "Beigas", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Ļauj Allajam piegādāt kūku pie nošu bloka", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> die<PERSON> d<PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "Ļauj Allajam piegādāt preces", "advancements.husbandry.allay_deliver_item_to_player.title": "Es esmu tavs labākais draugs", "advancements.husbandry.axolotl_in_a_bucket.description": "Noķer aksolotlu spainī", "advancements.husbandry.axolotl_in_a_bucket.title": "Piem<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> plē<PERSON>s", "advancements.husbandry.balanced_diet.description": "Apēd visu ko var apēst, pat ja tas ir kaitīgs", "advancements.husbandry.balanced_diet.title": "Sabalansēta diēta", "advancements.husbandry.breed_all_animals.description": "<PERSON><PERSON><PERSON><PERSON> visus dzīvniekus!", "advancements.husbandry.breed_all_animals.title": "Pa divi", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON><PERSON><PERSON> divus d<PERSON>", "advancements.husbandry.breed_an_animal.title": "Papagaiļi un sikspārņi", "advancements.husbandry.complete_catalogue.description": "<PERSON><PERSON><PERSON> visus kaķa variantus!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON><PERSON> padeve", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON> sni<PERSON>", "advancements.husbandry.fishy_business.description": "Noķert zivi", "advancements.husbandry.fishy_business.title": "Zivju bizness", "advancements.husbandry.froglights.description": "Savākt visas varžu gaismas", "advancements.husbandry.froglights.title": "Apvienojot mūsu spēkus!", "advancements.husbandry.kill_axolotl_target.description": "Sadarbojies ar aksolotlu un uzvari cīņā", "advancements.husbandry.kill_axolotl_target.title": "Draudzī<PERSON> d<PERSON>din<PERSON> spēks!", "advancements.husbandry.leash_all_frog_variants.description": "Piesien katru vardes variantu pavadā", "advancements.husbandry.leash_all_frog_variants.title": "Kad komanda ielec cie<PERSON>", "advancements.husbandry.make_a_sign_glow.description": "<PERSON><PERSON> j<PERSON> z<PERSON> teks<PERSON> spīd<PERSON>t", "advancements.husbandry.make_a_sign_glow.title": "Spīd un laistās!", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON><PERSON> stieni lai uz<PERSON>u ka<PERSON>, un tad pārvērtē savas dzīves izvēles", "advancements.husbandry.netherite_hoe.title": "Nopietns Veltījums", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> olu", "advancements.husbandry.obtain_sniffer_egg.title": "Smaržo interesanti", "advancements.husbandry.place_dried_ghast_in_water.description": "Novietojiet izžuvušā spoka bloku ūdenī", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON> mit<PERSON>!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON> s<PERSON>lu", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON><PERSON> sēklu un skaties kā tā aug", "advancements.husbandry.plant_seed.title": "Sēklaina vieta", "advancements.husbandry.remove_wolf_armor.description": "Novelc vilku bruņas no vilka izmantojot šķēres", "advancements.husbandry.remove_wolf_armor.title": "Šķērēts izg<PERSON>jiens", "advancements.husbandry.repair_wolf_armor.description": "Salabo bo<PERSON><PERSON><PERSON> vilka bruņas ar bru<PERSON><PERSON><PERSON>", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> jauns", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON><PERSON> laivā un peldi ar kazu", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Nav kazas jūrā!", "advancements.husbandry.root.description": "Pa<PERSON><PERSON> ir pilna ar draugiem un pārtikas", "advancements.husbandry.root.title": "Lopkopība", "advancements.husbandry.safely_harvest_honey.description": "<PERSON><PERSON><PERSON>, lai savāktu medu no bišu stropa, lieto<PERSON>t pudeli un nesadusmojot bites", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON> mū<PERSON>", "advancements.husbandry.silk_touch_nest.description": "Pārvieto bišu stropu vai bišu pūzni ar 3 bitēm iekš<PERSON>, izmantojot zīda piesk<PERSON>rienu", "advancements.husbandry.silk_touch_nest.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tactical_fishing.description": "Noķert zivi... bez makšķeres!", "advancements.husbandry.tactical_fishing.title": "Taktiska zvejošana", "advancements.husbandry.tadpole_in_a_bucket.description": "Noķer spainī kurkuli", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "Pieradini d<PERSON>īvnieku", "advancements.husbandry.tame_an_animal.title": "Labākie draugi uz visiem laikiem", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ē vasku no vara bloka!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Uzk<PERSON><PERSON>j medus šūnas uz vara bloka!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON> pa vienam vilkam no katra varianta", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> i<PERSON>", "advancements.nether.all_effects.description": "Esi visu efektu ietekmē vienlaicīgi", "advancements.nether.all_effects.title": "Kā mēs te nokļuvām?", "advancements.nether.all_potions.description": "Esi visu dziru efektu ietekmē vienlaicīgi", "advancements.nether.all_potions.title": "Negants kokteilis", "advancements.nether.brew_potion.description": "Uzbrū<PERSON><PERSON> dziru", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> respawn enkuru", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON> \"devi<PERSON><PERSON>\" dzī<PERSON><PERSON><PERSON>", "advancements.nether.create_beacon.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un novieto bāku", "advancements.nether.create_beacon.title": "<PERSON><PERSON> māj<PERSON> bāku", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON><PERSON><PERSON> bāku ar pilnu jaudu", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON>u u<PERSON> ar <PERSON>", "advancements.nether.distract_piglin.title": "Ak, spožš", "advancements.nether.explore_nether.description": "Izpēti visas Nether biomas", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON> Galamērķis", "advancements.nether.fast_travel.description": "<PERSON><PERSON><PERSON>, lai aiz<PERSON>ļ<PERSON>u 7km Vispasaulē", "advancements.nether.fast_travel.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> burbulis", "advancements.nether.find_bastion.description": "<PERSON><PERSON><PERSON>", "advancements.nether.find_bastion.title": "Tās bija tās dienas", "advancements.nether.find_fortress.description": "Ielauzies Nether Cietoksnī", "advancements.nether.find_fortress.title": "Briesmīgs cietoksnis", "advancements.nether.get_wither_skull.description": "<PERSON><PERSON><PERSON><PERSON> skeleta galvaskausu", "advancements.nether.get_wither_skull.title": "Spokains Biedējošs <PERSON>", "advancements.nether.loot_bastion.description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>", "advancements.nether.loot_bastion.title": "<PERSON>", "advancements.nether.netherite_armor.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> pilnu <PERSON> bruņu komplektu", "advancements.nether.netherite_armor.title": "Apsedz Mani ar Gružiem", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.title": "<PERSON>slē<PERSON>", "advancements.nether.obtain_blaze_rod.description": "Atbrīvo liesmekli no tā stieņa", "advancements.nether.obtain_blaze_rod.title": "Ugun<PERSON>", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.title": "<PERSON><PERSON><PERSON>?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar ugunsbumbu", "advancements.nether.return_to_sender.title": "Atgriezt sūtī<PERSON>ājam", "advancements.nether.ride_strider.description": "Jāj uz lavas ceļotāja ar izlocītu sēnīti uz nūjas", "advancements.nether.ride_strider.title": "Šai laivai ir kājas", "advancements.nether.ride_strider_in_overworld_lava.description": "Izved lavas ceļotāju garāāā pastaigā pa lavas ezeru Virs<PERSON>aulē", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON><PERSON> kā mājās", "advancements.nether.root.description": "Ņem līdzi vasaras drēbes", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, izved to d<PERSON><PERSON><PERSON><PERSON><PERSON> uz mājām <PERSON>... un tad nogalini to", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.use_lodestone.description": "<PERSON><PERSON><PERSON> kompasu uz dzīslakmens", "advancements.nether.use_lodestone.title": "<PERSON><PERSON>, ved mani mājās", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Novājināt un izārstēt zombiju ciema iedzīvotāju", "advancements.story.cure_zombie_villager.title": "Zombiju <PERSON>", "advancements.story.deflect_arrow.description": "<PERSON><PERSON><PERSON> ar vair<PERSON>u", "advancements.story.deflect_arrow.title": "<PERSON><PERSON>, Paldies", "advancements.story.enchant_item.description": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON> iekš Enchanting Galdu", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.title": "Beigas?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, iededz un ieej <PERSON>her portālā", "advancements.story.enter_the_nether.title": "<PERSON><PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON>", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON>", "advancements.story.form_obsidian.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> obsidi<PERSON>a bloku", "advancements.story.form_obsidian.title": "<PERSON><PERSON>", "advancements.story.iron_tools.description": "<PERSON>z<PERSON><PERSON> savu cirtni", "advancements.story.iron_tools.title": "Vai tik tas nav dzelz<PERSON> cirtnis", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON><PERSON> spaini ar lavu", "advancements.story.lava_bucket.title": "<PERSON><PERSON><PERSON> manta", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> dimantus", "advancements.story.mine_diamond.title": "<PERSON><PERSON><PERSON>!", "advancements.story.mine_stone.description": "<PERSON><PERSON><PERSON><PERSON> akmeni ar savu jauno cirtni", "advancements.story.mine_stone.title": "<PERSON>k<PERSON><PERSON>", "advancements.story.obtain_armor.description": "Aizsargā sevi ar dzel<PERSON> bru<PERSON>", "advancements.story.obtain_armor.title": "Uzpucēji<PERSON>", "advancements.story.root.description": "Sirds un spēles stāsts", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON><PERSON><PERSON> bru<PERSON> gl<PERSON><PERSON>j dzīvī<PERSON>", "advancements.story.shiny_gear.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mani ar dimantiem", "advancements.story.smelt_iron.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "<PERSON>z<PERSON><PERSON> lab<PERSON>ku cirtni", "advancements.story.upgrade_tools.title": "Saņemt uzlabojumu", "advancements.toast.challenge": "Izaicinājums izpildīts!", "advancements.toast.goal": "Mērķis sasniegts!", "advancements.toast.task": "Jauns sasniegums!", "argument.anchor.invalid": "Nederīga objekta enkura pozīcija %s", "argument.angle.incomplete": "Nepabeigts (vajadzīgs 1 leņķis)", "argument.angle.invalid": "Nederīgs leņķis", "argument.block.id.invalid": "Nezināms bloka veids: %s", "argument.block.property.duplicate": "Rekvizīts '%s' var tikt iestatīts blokam %s tikai vienreiz", "argument.block.property.invalid": "Bloks %s nepieņem '%s' rekvizītam %s", "argument.block.property.novalue": "Nepieciešama vērtība rekvizītam '%s' blokam %s", "argument.block.property.unclosed": "Vajadzīga a<PERSON>oš<PERSON> ] bloka stāvokļa rekvizītiem", "argument.block.property.unknown": "Blokam %s nav rekvizīta '%s'", "argument.block.tag.disallowed": "Tagi šeit nav atļauti, tikai bloki", "argument.color.invalid": "Nezināma krāsa %s", "argument.component.invalid": "Nepareiza tērzēšanas komponente: %s", "argument.criteria.invalid": "Nezināms kritērijs '%s'", "argument.dimension.invalid": "Nezināma dimensija '%s'", "argument.double.big": "Decimālskaitlis nedr<PERSON>t būt lielāks par %s, atrasts %s", "argument.double.low": "Decimālskaitlis nedrī<PERSON>t būt mazāks par %s, atrasts %s", "argument.entity.invalid": "Nederīgs nosaukums vai UUID", "argument.entity.notfound.entity": "Netika atrasts neviens objekts", "argument.entity.notfound.player": "<PERSON><PERSON> spēl<PERSON>tā<PERSON>s", "argument.entity.options.advancements.description": "S<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON> ar sasniegum<PERSON>m", "argument.entity.options.distance.description": "Attālums līdz objektam", "argument.entity.options.distance.negative": "Distance nevar būt ne<PERSON>va", "argument.entity.options.dx.description": "Entītijas starp x un x + dx", "argument.entity.options.dy.description": "Entītijas starp y un y + dy", "argument.entity.options.dz.description": "Entītijas starp z un z + dz", "argument.entity.options.gamemode.description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar spē<PERSON>", "argument.entity.options.inapplicable": "Opcija '%s' šeit neiederas", "argument.entity.options.level.description": "<PERSON><PERSON><PERSON> l<PERSON>", "argument.entity.options.level.negative": "<PERSON><PERSON><PERSON><PERSON> nevar būt negatīvs", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.limit.toosmall": "Limitam jābūt vismaz 1", "argument.entity.options.mode.invalid": "Nederīgs vai nezināms spēles režīms '%s'", "argument.entity.options.name.description": "Entī<PERSON>jas no<PERSON>uku<PERSON>", "argument.entity.options.nbt.description": "Entītijas ar NBT", "argument.entity.options.predicate.description": "Pašizveidots izteiciens", "argument.entity.options.scores.description": "Objekti ar punktiem", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON><PERSON> objektus", "argument.entity.options.sort.irreversible": "Nederīgs vai nezināms kārtas veids '%s'", "argument.entity.options.tag.description": "Objekti ar tagu", "argument.entity.options.team.description": "Objekti komandā", "argument.entity.options.type.description": "Veida objekti", "argument.entity.options.type.invalid": "Nederīgs vai nezināms objekta tips '%s'", "argument.entity.options.unknown": "Nezināma funkcija %s", "argument.entity.options.unterminated": "Vajadzīgas beigas", "argument.entity.options.valueless": "Vajadzīga vērtība opcijai '%s'", "argument.entity.options.x.description": "x pozīcija", "argument.entity.options.x_rotation.description": "Entītijas x rotācija", "argument.entity.options.y.description": "y pozīcija", "argument.entity.options.y_rotation.description": "Entītijas y rotācija", "argument.entity.options.z.description": "z pozīcija", "argument.entity.selector.allEntities": "Visas entītijas", "argument.entity.selector.allPlayers": "Visi spēlētāji", "argument.entity.selector.missing": "Pazudis izvēlētāja veids", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON><PERSON><PERSON> entītija", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nav <PERSON>ļ<PERSON><PERSON>", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.self": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> entītija", "argument.entity.selector.unknown": "Nezināms izvēlētāja tips '%s'", "argument.entity.toomany": "<PERSON><PERSON><PERSON>a tikai viena entīti<PERSON>, bet <PERSON><PERSON><PERSON><PERSON><PERSON> vairāk kā viena", "argument.enum.invalid": "Nepareiza vērtība \"%s\"", "argument.float.big": "<PERSON><PERSON><PERSON><PERSON><PERSON> skaitlis nedr<PERSON>t būt lielāks par %s, bet rakstīts %s", "argument.float.low": "<PERSON><PERSON><PERSON><PERSON><PERSON> skaitlis nedr<PERSON>t būt maz<PERSON>ks par %s, bet rakstīts %s", "argument.gamemode.invalid": "Nezināms spēles režīms: %s", "argument.hexcolor.invalid": "Nederīgs hex krāsu kods '%s'", "argument.id.invalid": "Nederīgs ID", "argument.id.unknown": "Nezināms ID: %s", "argument.integer.big": "<PERSON><PERSON><PERSON><PERSON> skaitlis ne<PERSON> būt lielāks par %s, atrasts %s", "argument.integer.low": "<PERSON><PERSON><PERSON><PERSON> skaitlis ne<PERSON> būt maz<PERSON>ks par %s, atrasts %s", "argument.item.id.invalid": "Nezināms priekšmets '%s'", "argument.item.tag.disallowed": "Tagi šeit nav atļauti, tikai priekšmenti", "argument.literal.incorrect": "Vajadzīgs vārds %s", "argument.long.big": "<PERSON><PERSON><PERSON>t būt lielāks par %s, atrasts %s", "argument.long.low": "<PERSON><PERSON><PERSON>t būt maz<PERSON>ks par %s, atrasts %s", "argument.message.too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON>nas ziņojums bija pārāk garš (%s > maksimālie %s simboli)", "argument.nbt.array.invalid": "Nederīgs saraksta tips '%s'", "argument.nbt.array.mixed": "Nevar ievietot %s %s", "argument.nbt.expected.compound": "<PERSON><PERSON> gaid<PERSON> sa<PERSON> tags", "argument.nbt.expected.key": "Vajadzīga vērtība", "argument.nbt.expected.value": "Vajadzīga vērtība", "argument.nbt.list.mixed": "Nevar ievietot %s sarakst<PERSON> no %s", "argument.nbt.trailing": "<PERSON><PERSON><PERSON><PERSON> beigu dati", "argument.player.entities": "Šo komandu var izman<PERSON>t tikai uz spēlētājiem, bet tā iekļauj arī entītijas", "argument.player.toomany": "Atļauts tikai viens spēl<PERSON>t<PERSON><PERSON><PERSON>, bet norādīts vairāk kā viens", "argument.player.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.pos.missing.double": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "argument.pos.missing.int": "Vajadzīga bloka pozīcija", "argument.pos.mixed": "Nevar jaukt pasaules & v<PERSON><PERSON><PERSON><PERSON><PERSON> (var izmantot tikai ^ vai arī neko)", "argument.pos.outofbounds": "<PERSON><PERSON> poz<PERSON>ja ir ārpus atļautaj<PERSON><PERSON> robežām.", "argument.pos.outofworld": "<PERSON>ī pozīcija ir ārpus pasaules!", "argument.pos.unloaded": "<PERSON>ī poz<PERSON>ja nav ielādēta", "argument.pos2d.incomplete": "Nepabeigts (tiek sagaidītas 2 koordinātas)", "argument.pos3d.incomplete": "Nepabeigts (vajadzīgas 3 koordinātas)", "argument.range.empty": "Vajadzīga vērtība vai vērtību lielums", "argument.range.ints": "Atļauti tikai veseli skaitļi", "argument.range.swapped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nevar būt lielāks par maksim<PERSON><PERSON>", "argument.resource.invalid_type": "Elementam '%s' ir nepareizs tips '%s' (tie<PERSON> '%s')", "argument.resource.not_found": "Nevar atrast elementu '%s' ar tipu '%s'", "argument.resource_or_id.failed_to_parse": "Neizdevā<PERSON> parsēt struktūru: %s", "argument.resource_or_id.invalid": "Nederīgs id vai tag", "argument.resource_or_id.no_such_element": "Neizdevās atrast elementu '%s' reģistrā '%s", "argument.resource_selector.not_found": "Nav atbilstību atlasītājam '%s' ar tipu '%s'", "argument.resource_tag.invalid_type": "Tagam '%s' ir nepareizs tips '%s' (tie<PERSON> '%s')", "argument.resource_tag.not_found": "Nevar atrast tagu '%s' ar tipu '%s'", "argument.rotation.incomplete": "Nepabeigts (vajadzīgas 2 koordinātas)", "argument.scoreHolder.empty": "Nav atrasti atbilstoši punktu turētāji", "argument.scoreboardDisplaySlot.invalid": "Nezināms displeja slots '%s'", "argument.style.invalid": "Nederīgs stils: %s", "argument.time.invalid_tick_count": "Tikšķu skaits nedrīkst būt negatīvs", "argument.time.invalid_unit": "Nederīga vienī<PERSON>", "argument.time.tick_count_too_low": "Tikšķēšanas skaits nedrīkst bū zemāks par %s, atrasts %s", "argument.uuid.invalid": "Nepareizs UUID", "argument.waypoint.invalid": "Izvēlētais objekts nav ceļapunkts", "arguments.block.tag.unknown": "Nezināms bloka tags: %s", "arguments.function.tag.unknown": "Nezināms funkcijas tags '%s'", "arguments.function.unknown": "Nezināma funkcija '%s", "arguments.item.component.expected": "<PERSON><PERSON> sagaidīta priekšmeta komponente", "arguments.item.component.malformed": "Nepareizi izveidota '%s' komponente: '%s'", "arguments.item.component.repeated": "Priek<PERSON><PERSON>a komponente '%s' at<PERSON><PERSON><PERSON><PERSON><PERSON>, bet drī<PERSON>t norād<PERSON>t tikai vienu vērtību", "arguments.item.component.unknown": "Nezināma priekšmeta komponente '%s'", "arguments.item.malformed": "Nepareizi veidots priekšmets: '%s'", "arguments.item.overstacked": "%s var tikt sakrauts kaudzē tikai līdz %s", "arguments.item.predicate.malformed": "Nepareizi izveidots '%s' predikāts: '%s'", "arguments.item.predicate.unknown": "Nezināms priekšmeta predikāts '%s'", "arguments.item.tag.unknown": "Nezināms priekšmeta tags '%s'", "arguments.nbtpath.node.invalid": "Nederīgs NBT elements", "arguments.nbtpath.nothing_found": "Neatrada sakrītošus elementus %s", "arguments.nbtpath.too_deep": "Iegūtais NBT ir pār<PERSON>k dziļi iegults", "arguments.nbtpath.too_large": "Iegūtais NBT ir pār<PERSON>k liels", "arguments.objective.notFound": "Nezināms punktu tabulas mērķis '%s'", "arguments.objective.readonly": "Punktu tabulas mērķis '%s' ir tikai lasāms", "arguments.operation.div0": "<PERSON><PERSON><PERSON> da<PERSON>t ar nulli", "arguments.operation.invalid": "Nederīga operācija", "arguments.swizzle.invalid": "<PERSON><PERSON><PERSON><PERSON> asu komb<PERSON>, vajad<PERSON><PERSON><PERSON> 'x', 'y', 'z' kombin<PERSON><PERSON>ja", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Bruņ<PERSON> i<PERSON>tur<PERSON>", "attribute.name.attack_damage": "<PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON>", "attribute.name.attack_knockback": "Uzbrukuma atsitiens", "attribute.name.attack_speed": "<PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "Bloka Pārtraukuma ātrums", "attribute.name.block_interaction_range": "Bloku mijiedarbības diapaz", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON> laiks", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON>", "attribute.name.entity_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> diapazons", "attribute.name.explosion_knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atsi<PERSON>a pretestība", "attribute.name.fall_damage_multiplier": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "attribute.name.flying_speed": "Lido<PERSON>nas ā<PERSON>ums", "attribute.name.follow_range": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON> sting<PERSON>", "attribute.name.generic.attack_damage": "<PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON>", "attribute.name.generic.attack_knockback": "Uzbrukuma atsitums", "attribute.name.generic.attack_speed": "Uzbrukuma ātrums", "attribute.name.generic.block_interaction_range": "Bloku mi<PERSON> attālums", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "attribute.name.generic.explosion_knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.fall_damage_multiplier": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "attribute.name.generic.flying_speed": "Lido<PERSON>nas ā<PERSON>ums", "attribute.name.generic.follow_range": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "Lēciena <PERSON>", "attribute.name.generic.knockback_resistance": "Atpakaļsitiena <PERSON>testī<PERSON>", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "attribute.name.generic.movement_efficiency": "Kustību Efektivitāte", "attribute.name.generic.movement_speed": "Ātrums", "attribute.name.generic.oxygen_bonus": "Skābekļa bonuss", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON> k<PERSON> distance", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "<PERSON><PERSON><PERSON> augstums", "attribute.name.generic.water_movement_efficiency": "Ūdens Kustību Efektivitāte", "attribute.name.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "<PERSON><PERSON><PERSON>", "attribute.name.jump_strength": "Lēciena <PERSON>", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.max_health": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "Rakšanas Efektivitāte", "attribute.name.movement_efficiency": "Kustību Efektivitāte", "attribute.name.movement_speed": "Ātrums", "attribute.name.oxygen_bonus": "Skābekļa bonuss", "attribute.name.player.block_break_speed": "Bloku laužšanas ātrums", "attribute.name.player.block_interaction_range": "Bloku mi<PERSON> attālums", "attribute.name.player.entity_interaction_range": "<PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "attribute.name.player.mining_efficiency": "Rakšanas Efektivitāte", "attribute.name.player.sneaking_speed": "Ložņāšanas Ātrums", "attribute.name.player.submerged_mining_speed": "Iegremdēts Rakšanas Ātrums", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON>", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "attribute.name.scale": "Lie<PERSON>", "attribute.name.sneaking_speed": "Ložņāšanas Ātrums", "attribute.name.spawn_reinforcements": "Zombiju palīgspēki", "attribute.name.step_height": "<PERSON><PERSON><PERSON> augstums", "attribute.name.submerged_mining_speed": "Iegremdētā<PERSON> ieguves ātrums", "attribute.name.sweeping_damage_ratio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON>", "attribute.name.tempt_range": "Mob Tempt diapazons", "attribute.name.water_movement_efficiency": "Ūdens kustības efektivitāte", "attribute.name.waypoint_receive_range": "Ceļapunkta uztveršanas diapazons", "attribute.name.waypoint_transmit_range": "Ceļapunk<PERSON> pārraides diapazons", "attribute.name.zombie.spawn_reinforcements": "Zombiju palīgspēki", "biome.minecraft.badlands": "Mesa", "biome.minecraft.bamboo_jungle": "Bambusa džungļi", "biome.minecraft.basalt_deltas": "Bazalta deltas", "biome.minecraft.beach": "Pludmale", "biome.minecraft.birch_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cherry_grove": "Ķiršu birzs", "biome.minecraft.cold_ocean": "Auksts okeāns", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON>, dziļš okeāns", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON><PERSON> tumsa", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>, sasalis okeāns", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON>, rem<PERSON>s ok<PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Tekak<PERSON><PERSON> alas", "biome.minecraft.end_barrens": "End tukšums", "biome.minecraft.end_highlands": "End augstienes", "biome.minecraft.end_midlands": "<PERSON> zemes", "biome.minecraft.eroded_badlands": "Izžuvusi mesa", "biome.minecraft.flower_forest": "Puķu mežs", "biome.minecraft.forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "biome.minecraft.frozen_river": "Sasalusi upe", "biome.minecraft.grove": "Birzs", "biome.minecraft.ice_spikes": "<PERSON><PERSON> smailes", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "biome.minecraft.jungle": "Džungļi", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alas", "biome.minecraft.mangrove_swamp": "Mangroves purvs", "biome.minecraft.meadow": "Pļava", "biome.minecraft.mushroom_fields": "<PERSON><PERSON><PERSON><PERSON> lauki", "biome.minecraft.nether_wastes": "<PERSON><PERSON> tuksnesis", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON> p<PERSON>", "biome.minecraft.old_growth_pine_taiga": "Priežu pirmmeža taiga", "biome.minecraft.old_growth_spruce_taiga": "Egļu pirmmeža taiga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.plains": "Pļava", "biome.minecraft.river": "Upe", "biome.minecraft.savanna": "Savanna", "biome.minecraft.savanna_plateau": "Savannas plato", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON><PERSON> salas", "biome.minecraft.snowy_beach": "Sniegota pludmale", "biome.minecraft.snowy_plains": "<PERSON>nie<PERSON><PERSON>", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_taiga": "Sniegota taiga", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> i<PERSON>", "biome.minecraft.sparse_jungle": "<PERSON><PERSON>", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "biome.minecraft.stony_shore": "Akmeņainā piekraste", "biome.minecraft.sunflower_plains": "Saulespuķu pļavas", "biome.minecraft.swamp": "Purvs", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "Tukšums", "biome.minecraft.warm_ocean": "Silts okeāns", "biome.minecraft.warped_forest": "Izlocīts mežs", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "Vējainie grants pakalni", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_savanna": "Vējainā savanna", "biome.minecraft.wooded_badlands": "Mežainā mesa", "block.minecraft.acacia_button": "Akācijas poga", "block.minecraft.acacia_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> durvis", "block.minecraft.acacia_fence": "Akācijas koka žogs", "block.minecraft.acacia_fence_gate": "Akācijas žoga vārti", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapas", "block.minecraft.acacia_log": "Akācijas baļķis", "block.minecraft.acacia_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.acacia_pressure_plate": "Akācijas spiediena plātne", "block.minecraft.acacia_sapling": "<PERSON>k<PERSON><PERSON><PERSON> stāds", "block.minecraft.acacia_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_slab": "Akācijas plāksne", "block.minecraft.acacia_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_trapdoor": "Akācijas lūka", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> siena<PERSON> z<PERSON>", "block.minecraft.acacia_wall_sign": "<PERSON>k<PERSON><PERSON><PERSON> siena<PERSON> z<PERSON>", "block.minecraft.acacia_wood": "Akācijas koks", "block.minecraft.activator_rail": "Aktivatora sliedes", "block.minecraft.air": "G<PERSON><PERSON>", "block.minecraft.allium": "<PERSON>ī<PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametista bloks", "block.minecraft.amethyst_cluster": "Ametista kopa", "block.minecraft.ancient_debris": "<PERSON><PERSON>", "block.minecraft.andesite": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite_wall": "<PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Pievienots arbūza stumbrs", "block.minecraft.attached_pumpkin_stem": "Pievienots ķirbja stumbrs", "block.minecraft.azalea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "Debeszilā rudzupuķe", "block.minecraft.bamboo": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_block": "Bambusa bloks", "block.minecraft.bamboo_button": "Bambusa poga", "block.minecraft.bamboo_door": "Bamb<PERSON> durvis", "block.minecraft.bamboo_fence": "Bambusa sēta", "block.minecraft.bamboo_fence_gate": "Bambusa žoga vārti", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bam<PERSON>a z<PERSON>", "block.minecraft.bamboo_mosaic": "Bambusa mozaīka", "block.minecraft.bamboo_mosaic_slab": "Bambusa mozaīkas plāksne", "block.minecraft.bamboo_mosaic_stairs": "Bambusa moza<PERSON>kas k<PERSON>", "block.minecraft.bamboo_planks": "Bambusa dēļi", "block.minecraft.bamboo_pressure_plate": "Bambusa spiediena plāksne", "block.minecraft.bamboo_sapling": "Bambusa dzinums", "block.minecraft.bamboo_sign": "Bambusa zīme", "block.minecraft.bamboo_slab": "Bambusa plāksne", "block.minecraft.bamboo_stairs": "Bambusa kāpnes", "block.minecraft.bamboo_trapdoor": "Bambusa lūka", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bambusa siena<PERSON> z<PERSON>me", "block.minecraft.bamboo_wall_sign": "Bambusa sienas z<PERSON>me", "block.minecraft.banner.base.black": "<PERSON>ln<PERSON>gi meln<PERSON> la<PERSON>", "block.minecraft.banner.base.blue": "<PERSON><PERSON>īgi z<PERSON> laukums", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON>gi brūns <PERSON>", "block.minecraft.banner.base.cyan": "Pilnīgi ci<PERSON><PERSON>", "block.minecraft.banner.base.gray": "Pilnīgi pelēks lauku<PERSON>", "block.minecraft.banner.base.green": "Pilnīgi z<PERSON><PERSON><PERSON>", "block.minecraft.banner.base.light_blue": "Pilnīgi gaiši z<PERSON> laukums", "block.minecraft.banner.base.light_gray": "Pilnīgi gaiši pelēks lauku<PERSON>", "block.minecraft.banner.base.lime": "Pilnīgi gaiši za<PERSON>", "block.minecraft.banner.base.magenta": "<PERSON>lnīgi fuks<PERSON>", "block.minecraft.banner.base.orange": "Pilnīgi or<PERSON><PERSON>", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON> roz<PERSON>", "block.minecraft.banner.base.purple": "Pilnīgi violets laukums", "block.minecraft.banner.base.red": "Pilnīgi sarkans laukums", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON><PERSON> balts laukums", "block.minecraft.banner.base.yellow": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.banner.border.black": "<PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "Gaiši zila apmale", "block.minecraft.banner.border.light_gray": "G<PERSON><PERSON><PERSON> p<PERSON> a<PERSON>", "block.minecraft.banner.border.lime": "Gaiši zaļa apmale", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "Oranža apmale", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "Balta apmale", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.gray": "Pelē<PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON> zils mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.light_gray": "G<PERSON>ši pelēks mūrēts lauks", "block.minecraft.banner.bricks.lime": "G<PERSON><PERSON><PERSON> za<PERSON> mūrēts lauks", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON><PERSON> mūr<PERSON>ts lauks", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON><PERSON> mūr<PERSON> lauks", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> mūr<PERSON> lauks", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON> mūr<PERSON><PERSON> lauks", "block.minecraft.banner.bricks.red": "Sarkans mūr<PERSON><PERSON> lauks", "block.minecraft.banner.bricks.white": "<PERSON>lts mūrēts lauks", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> mūr<PERSON> lauks", "block.minecraft.banner.circle.black": "<PERSON><PERSON> riņķis", "block.minecraft.banner.circle.blue": "Zils riņķis", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON><PERSON> riņķis", "block.minecraft.banner.circle.cyan": "Ciāns riņķis", "block.minecraft.banner.circle.gray": "Pelēks riņķis", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON> riņķis", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> riņķis", "block.minecraft.banner.circle.light_gray": "G<PERSON><PERSON><PERSON> pelēks riņķis", "block.minecraft.banner.circle.lime": "Gaiši zaļš riņķis", "block.minecraft.banner.circle.magenta": "<PERSON>ks<PERSON><PERSON> riņķis", "block.minecraft.banner.circle.orange": "Oranžs riņķis", "block.minecraft.banner.circle.pink": "Rozā riņķis", "block.minecraft.banner.circle.purple": "Violets disks", "block.minecraft.banner.circle.red": "Sarkans riņķis", "block.minecraft.banner.circle.white": "Balts riņķis", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON> riņķis", "block.minecraft.banner.creeper.black": "<PERSON><PERSON> creeper l<PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON> creeper l<PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON> creep<PERSON> l<PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON> creeper lā<PERSON><PERSON>š", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>er l<PERSON><PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON><PERSON> pel<PERSON> l<PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON> za<PERSON><PERSON> creeper lā<PERSON>", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON>er lā<PERSON>", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON> creeper l<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.purple": "Violets creeper l<PERSON><PERSON>", "block.minecraft.banner.creeper.red": "Sarkans creeper l<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.white": "Balts creeper l<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON> creeper lā<PERSON><PERSON>š", "block.minecraft.banner.cross.black": "Melns diagonāls krusts", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> diagon<PERSON>ls krusts", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON><PERSON> di<PERSON> krusts", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON><PERSON> di<PERSON> krusts", "block.minecraft.banner.cross.gray": "Pelēks diagonāls krusts", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.cross.light_blue": "G<PERSON>ši zils diagonāls krusts", "block.minecraft.banner.cross.light_gray": "Gaiši pelēks diagonāls krusts", "block.minecraft.banner.cross.lime": "Gaiši z<PERSON><PERSON> diagonāls krusts", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON><PERSON> di<PERSON> krusts", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.cross.pink": "<PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.cross.purple": "<PERSON><PERSON> di<PERSON> krusts", "block.minecraft.banner.cross.red": "Sarkans diagonāls krusts", "block.minecraft.banner.cross.white": "Balts diagonāls krusts", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON><PERSON> diagonāls krusts", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON> robota a<PERSON>", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.cyan": "Ciāna robota a<PERSON>", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON> robota a<PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON>ļa robota a<PERSON>", "block.minecraft.banner.curly_border.light_blue": "Gaiši zila robota a<PERSON>ale", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON><PERSON> pel<PERSON>ka robota a<PERSON>ale", "block.minecraft.banner.curly_border.lime": "Gaiši zaļa robota apmale", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON> robota a<PERSON>", "block.minecraft.banner.curly_border.orange": "Oranža robota a<PERSON>ale", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON>a a<PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON> robota a<PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> robota a<PERSON>", "block.minecraft.banner.curly_border.white": "Balta robota apmale", "block.minecraft.banner.curly_border.yellow": "Dzeltena robota a<PERSON>ale", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> augša", "block.minecraft.banner.diagonal_left.gray": "<PERSON>elē<PERSON> k<PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "G<PERSON>š<PERSON> zila k<PERSON> augša", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON><PERSON> pel<PERSON>ka kreis<PERSON> di<PERSON> augša", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON><PERSON><PERSON> zaļa k<PERSON> di<PERSON> augša", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON><PERSON> augša", "block.minecraft.banner.diagonal_left.orange": "Or<PERSON><PERSON><PERSON><PERSON> augša", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.white": "Balta kreisā<PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON> au<PERSON>", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON> <PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "Gaiši zila <PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> di<PERSON> augša", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON><PERSON><PERSON> za<PERSON> augša", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON> au<PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_right.white": "Balta diagonāles aug<PERSON>", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Gaiši zila diagonā<PERSON>", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ka diagon<PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_left.lime": "Gaiši zaļa <PERSON>agon<PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.white": "Balta diagonāles <PERSON>ša", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_right.gray": "Pelēka k<PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.light_blue": "Gaiši z<PERSON> k<PERSON>", "block.minecraft.banner.diagonal_up_right.light_gray": "G<PERSON><PERSON><PERSON> pelēka kreisā<PERSON> di<PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_right.lime": "Gaiši zaļa k<PERSON> diagon<PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.banner.diagonal_up_right.orange": "Oranž<PERSON> k<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.white": "Balta kreisās <PERSON>", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> a<PERSON>", "block.minecraft.banner.flow.black": "<PERSON><PERSON>", "block.minecraft.banner.flow.blue": "<PERSON><PERSON>", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.flow.gray": "Pelēka pl<PERSON>", "block.minecraft.banner.flow.green": "Zaļ<PERSON>", "block.minecraft.banner.flow.light_blue": "Gaiši zila plūsma", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka plū<PERSON>", "block.minecraft.banner.flow.lime": "Gaiši zaļa plūsma", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.orange": "Oranža plūsma", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.flow.purple": "<PERSON><PERSON>", "block.minecraft.banner.flow.red": "<PERSON>rka<PERSON> p<PERSON>", "block.minecraft.banner.flow.white": "Balta plūsma", "block.minecraft.banner.flow.yellow": "<PERSON>zeltena pl<PERSON>", "block.minecraft.banner.flower.black": "<PERSON><PERSON> zied<PERSON>", "block.minecraft.banner.flower.blue": "<PERSON><PERSON> z<PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON>ē<PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> z<PERSON>s", "block.minecraft.banner.flower.light_gray": "Gaiši pelēks z<PERSON>s", "block.minecraft.banner.flower.lime": "Gaiši z<PERSON><PERSON> z<PERSON>s", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON> zieds", "block.minecraft.banner.flower.red": "Sarkans zieds", "block.minecraft.banner.flower.white": "Balts zieds", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON> z<PERSON>", "block.minecraft.banner.globe.black": "Melns globuss", "block.minecraft.banner.globe.blue": "Zils globuss", "block.minecraft.banner.globe.brown": "Brūns globuss", "block.minecraft.banner.globe.cyan": "Ciāns globuss", "block.minecraft.banner.globe.gray": "Pelēks globuss", "block.minecraft.banner.globe.green": "Zaļš globuss", "block.minecraft.banner.globe.light_blue": "Gaiši zils globuss", "block.minecraft.banner.globe.light_gray": "Gaiši pelēks globuss", "block.minecraft.banner.globe.lime": "Gaiši zaļš globuss", "block.minecraft.banner.globe.magenta": "Fuksīns globuss", "block.minecraft.banner.globe.orange": "Oranžs globuss", "block.minecraft.banner.globe.pink": "Rozā globuss", "block.minecraft.banner.globe.purple": "Violets globuss", "block.minecraft.banner.globe.red": "Sarkans globuss", "block.minecraft.banner.globe.white": "Balts globuss", "block.minecraft.banner.globe.yellow": "Dzeltens globuss", "block.minecraft.banner.gradient.black": "Melns gradients", "block.minecraft.banner.gradient.blue": "Zils gradients", "block.minecraft.banner.gradient.brown": "Brūns gradients", "block.minecraft.banner.gradient.cyan": "Ciāns gradients", "block.minecraft.banner.gradient.gray": "Pelēks gradients", "block.minecraft.banner.gradient.green": "Zaļš gradients", "block.minecraft.banner.gradient.light_blue": "Gaiši zils gradients", "block.minecraft.banner.gradient.light_gray": "Gaiši pelēks gradients", "block.minecraft.banner.gradient.lime": "Gaiši zaļš gradients", "block.minecraft.banner.gradient.magenta": "Fuksīns gradients", "block.minecraft.banner.gradient.orange": "Oranžs gradients", "block.minecraft.banner.gradient.pink": "Rozā gradients", "block.minecraft.banner.gradient.purple": "Violets gradients", "block.minecraft.banner.gradient.red": "Sarkans gradients", "block.minecraft.banner.gradient.white": "Balts gradients", "block.minecraft.banner.gradient.yellow": "Dzeltens gradients", "block.minecraft.banner.gradient_up.black": "Melns apakšējais gradients", "block.minecraft.banner.gradient_up.blue": "Zils apakšējais gradients", "block.minecraft.banner.gradient_up.brown": "Brūns a<PERSON> gradients", "block.minecraft.banner.gradient_up.cyan": "Ciāns a<PERSON> gradients", "block.minecraft.banner.gradient_up.gray": "Pelēks apakšējais gradients", "block.minecraft.banner.gradient_up.green": "Z<PERSON>ļš apakšējais gradients", "block.minecraft.banner.gradient_up.light_blue": "Gaiši zils apakšējais gradients", "block.minecraft.banner.gradient_up.light_gray": "Gaiši pelēks apakšējais gradients", "block.minecraft.banner.gradient_up.lime": "Gaiši za<PERSON>š apakšējais gradients", "block.minecraft.banner.gradient_up.magenta": "Fuksīns a<PERSON>kšējais gradients", "block.minecraft.banner.gradient_up.orange": "Oranžs a<PERSON>š<PERSON>is gradients", "block.minecraft.banner.gradient_up.pink": "Rozā a<PERSON>šē<PERSON>is gradients", "block.minecraft.banner.gradient_up.purple": "Violets a<PERSON> gradients", "block.minecraft.banner.gradient_up.red": "Sarkans apakšējais gradients", "block.minecraft.banner.gradient_up.white": "Balts apakšējais gradients", "block.minecraft.banner.gradient_up.yellow": "Dzeltens apakšējais gradients", "block.minecraft.banner.guster.black": "<PERSON><PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "Gaiši z<PERSON> br<PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> br<PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON> za<PERSON> br<PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "Balta brāzma", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Gaiši zila augšdaļa", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka augš<PERSON>", "block.minecraft.banner.half_horizontal.lime": "Gaiši zaļa augšdaļa", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.orange": "Oranž<PERSON>š<PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "Balta augšdaļa", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>š<PERSON>ļ<PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "Ciāna <PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "Pelēka <PERSON>š<PERSON>ļ<PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "Zaļa a<PERSON>š<PERSON>ļ<PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Gaiši zila apakšdaļa", "block.minecraft.banner.half_horizontal_bottom.light_gray": "G<PERSON>ši pelēka apakšdaļa", "block.minecraft.banner.half_horizontal_bottom.lime": "Gaiši zaļa apakšdaļa", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "Oranža apakšdaļa", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON>rka<PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "Balta apakšdaļa", "block.minecraft.banner.half_horizontal_bottom.yellow": "Dzeltena apakšdaļa", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.gray": "Pelēka kreisā puse", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.light_blue": "Gaiši zila kreisā puse", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka kreisā puse", "block.minecraft.banner.half_vertical.lime": "Gaiši zaļa kreisā puse", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.orange": "Oranža k<PERSON>ā puse", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON> k<PERSON> puse", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>na k<PERSON> puse", "block.minecraft.banner.half_vertical.white": "Balta kreisā puse", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON><PERSON> kreis<PERSON> puse", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON> lab<PERSON> puse", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> puse", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON><PERSON><PERSON> puse", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON><PERSON><PERSON> labā puse", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON><PERSON> labā puse", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON> puse", "block.minecraft.banner.half_vertical_right.light_blue": "G<PERSON><PERSON><PERSON> zila labā puse", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka labā puse", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON><PERSON> zaļa labā puse", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON> puse", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON><PERSON> puse", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON> lab<PERSON> puse", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> lab<PERSON> puse", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON>na lab<PERSON> puse", "block.minecraft.banner.half_vertical_right.white": "Balta labā puse", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON><PERSON><PERSON> labā puse", "block.minecraft.banner.mojang.black": "<PERSON><PERSON> lieta", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Ciāna lieta", "block.minecraft.banner.mojang.gray": "Pelēka lieta", "block.minecraft.banner.mojang.green": "Zaļa lieta", "block.minecraft.banner.mojang.light_blue": "Gaiši zila lieta", "block.minecraft.banner.mojang.light_gray": "Gaiši pelēka lieta", "block.minecraft.banner.mojang.lime": "Gaiši zaļa lieta", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON>ta", "block.minecraft.banner.mojang.orange": "Oranža lieta", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "Vielota lieta", "block.minecraft.banner.mojang.red": "Sarkana lieta", "block.minecraft.banner.mojang.white": "Balta lieta", "block.minecraft.banner.mojang.yellow": "Dzeltena lieta", "block.minecraft.banner.piglin.black": "<PERSON><PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>", "block.minecraft.banner.piglin.red": "Sarkans šņukurs", "block.minecraft.banner.piglin.white": "<PERSON><PERSON>uku<PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.blue": "<PERSON>ils rombs", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON><PERSON> zils rombs", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēks rombs", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON> z<PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON> rombs", "block.minecraft.banner.rhombus.red": "Sarkans rombs", "block.minecraft.banner.rhombus.white": "Balts rombs", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> rombs", "block.minecraft.banner.skull.black": "<PERSON><PERSON>", "block.minecraft.banner.skull.blue": "<PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_gray": "Gaiši pelē<PERSON>", "block.minecraft.banner.skull.lime": "Gaiš<PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>", "block.minecraft.banner.skull.red": "Sarka<PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON> vert<PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.light_blue": "G<PERSON>š<PERSON> zilas vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON><PERSON> pel<PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.lime": "Gaiši zaļ<PERSON> vertikālas svītras", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.orange": "Oran<PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON> vert<PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.white": "Baltas vert<PERSON> sv<PERSON>", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> sv<PERSON>", "block.minecraft.banner.square_bottom_left.black": "Melns kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.blue": "Zils kreisais apakšē<PERSON> stū<PERSON>", "block.minecraft.banner.square_bottom_left.brown": "Brūns kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.cyan": "Ciāns k<PERSON> a<PERSON>kšē<PERSON> stūris", "block.minecraft.banner.square_bottom_left.gray": "Pelēks kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.green": "Zaļš kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.light_blue": "Gaiši zils kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.light_gray": "Gaiši pelēks kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.lime": "Gaiži zaļš kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.magenta": "Fuksīns kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.orange": "Oranžs kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.pink": "Rozā k<PERSON>ais apakšēja<PERSON> stūris", "block.minecraft.banner.square_bottom_left.purple": "Vieloets bāzes kreisais stūris", "block.minecraft.banner.square_bottom_left.red": "Sarkans kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.white": "Balts kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_left.yellow": "Dzeltens kreisais apakšējais stūris", "block.minecraft.banner.square_bottom_right.black": "Melns labais apa<PERSON>š<PERSON><PERSON> stūris", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON> labais apa<PERSON>š<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON><PERSON><PERSON> labais a<PERSON>š<PERSON> stūris", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON><PERSON>s labais a<PERSON> stūris", "block.minecraft.banner.square_bottom_right.gray": "Pelēks labais apakšē<PERSON> stūris", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON> apa<PERSON>šē<PERSON> stū<PERSON>", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON><PERSON><PERSON> zils labais apa<PERSON>š<PERSON><PERSON> stūris", "block.minecraft.banner.square_bottom_right.light_gray": "G<PERSON><PERSON><PERSON> pelēks labais apakšējais stūris", "block.minecraft.banner.square_bottom_right.lime": "Gaiži za<PERSON> labais apakšējais stūris", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON><PERSON> vāzes labais apakšēja<PERSON> stūris", "block.minecraft.banner.square_bottom_right.orange": "Or<PERSON><PERSON><PERSON> lab<PERSON> a<PERSON>šē<PERSON> stū<PERSON>", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON><PERSON> lab<PERSON> a<PERSON> stūris", "block.minecraft.banner.square_bottom_right.purple": "<PERSON>s labais a<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_bottom_right.red": "Sarkans labais apa<PERSON>š<PERSON><PERSON> stūris", "block.minecraft.banner.square_bottom_right.white": "Balts labais apakšē<PERSON> stūris", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON><PERSON> labais apa<PERSON>š<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_top_left.black": "Melns kreisais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON> kreisais augš<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_top_left.brown": "Brūns kreisais augšē<PERSON> stūris", "block.minecraft.banner.square_top_left.cyan": "Ciāns k<PERSON> augšē<PERSON> stūris", "block.minecraft.banner.square_top_left.gray": "Pelēks kreisais augšējais stūris", "block.minecraft.banner.square_top_left.green": "Z<PERSON>ļš kreisais augšējais stūris", "block.minecraft.banner.square_top_left.light_blue": "G<PERSON>š<PERSON> z<PERSON> kreisais augšējais stūris", "block.minecraft.banner.square_top_left.light_gray": "Gaiši pelēks kreisais augšējais stūris", "block.minecraft.banner.square_top_left.lime": "Gaiši za<PERSON>š kreisais augšējais stūris", "block.minecraft.banner.square_top_left.magenta": "Fuksīns kreisais augšējais stūris", "block.minecraft.banner.square_top_left.orange": "Oranžs kreisais augšējais stūris", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON><PERSON> k<PERSON> augšē<PERSON> stūris", "block.minecraft.banner.square_top_left.purple": "<PERSON>s kreisais augš<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_top_left.red": "Sarkans kreisais augšē<PERSON>is stūris", "block.minecraft.banner.square_top_left.white": "Balts kreisais augšē<PERSON>is stūris", "block.minecraft.banner.square_top_left.yellow": "Dzeltens kreisais augšē<PERSON> stū<PERSON>", "block.minecraft.banner.square_top_right.black": "Melns labais augš<PERSON><PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON> labais augš<PERSON><PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON><PERSON><PERSON> labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON><PERSON><PERSON> labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.gray": "Pelēks labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_top_right.light_blue": "<PERSON><PERSON><PERSON><PERSON> zils labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēks labais augšē<PERSON> stūris", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON><PERSON><PERSON> za<PERSON> labais augšē<PERSON> stūris", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON><PERSON> labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.orange": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON> augš<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON><PERSON> lab<PERSON> augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.purple": "<PERSON>s labais augš<PERSON><PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.red": "Sarkans labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.white": "Balts labais augš<PERSON><PERSON> stūris", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON><PERSON> labais augš<PERSON><PERSON> stū<PERSON>", "block.minecraft.banner.straight_cross.black": "<PERSON>ns krusts", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON> krusts", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.straight_cross.gray": "Pelēks krusts", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.straight_cross.light_blue": "G<PERSON><PERSON><PERSON> zils krusts", "block.minecraft.banner.straight_cross.light_gray": "Gaiši pelēks krusts", "block.minecraft.banner.straight_cross.lime": "Gaiši z<PERSON>ļš krusts", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.straight_cross.orange": "Oran<PERSON><PERSON> k<PERSON>s", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.straight_cross.purple": "<PERSON>s krusts", "block.minecraft.banner.straight_cross.red": "Sarkans krusts", "block.minecraft.banner.straight_cross.white": "Balts krusts", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON> krusts", "block.minecraft.banner.stripe_bottom.black": "Melna <PERSON>š<PERSON>", "block.minecraft.banner.stripe_bottom.blue": "Zila a<PERSON>šmala", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Ciāna a<PERSON>kšmala", "block.minecraft.banner.stripe_bottom.gray": "Pelēka a<PERSON>kšmala", "block.minecraft.banner.stripe_bottom.green": "Zaļa a<PERSON>kšmala", "block.minecraft.banner.stripe_bottom.light_blue": "Gaiši zila apakšmala", "block.minecraft.banner.stripe_bottom.light_gray": "Gaiši pelēka a<PERSON>kšmala", "block.minecraft.banner.stripe_bottom.lime": "Gaiši zaļa apakšmala", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "Oranža apakšmala", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "Sarkana a<PERSON>š<PERSON>", "block.minecraft.banner.stripe_bottom.white": "Balta apakšmala", "block.minecraft.banner.stripe_bottom.yellow": "Dzeltena a<PERSON>kšmala", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON> vert<PERSON> v<PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> vert<PERSON> v<PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> v<PERSON>", "block.minecraft.banner.stripe_center.cyan": "Ciāna vertik<PERSON> vid<PERSON>", "block.minecraft.banner.stripe_center.gray": "Pelēka vertikāla vid<PERSON>lī<PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON>ļ<PERSON> vertik<PERSON> vid<PERSON>", "block.minecraft.banner.stripe_center.light_blue": "Gaiši zila vertikāla viduslīnija", "block.minecraft.banner.stripe_center.light_gray": "G<PERSON>ši pelēka vertikāla viduslīnija", "block.minecraft.banner.stripe_center.lime": "Gaiši zaļa vertikāla viduslīnija", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON><PERSON> vert<PERSON> v<PERSON>", "block.minecraft.banner.stripe_center.orange": "Oranža vertikāla vid<PERSON>lī<PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON> vert<PERSON> vid<PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON> vert<PERSON> v<PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON>na vert<PERSON> vid<PERSON>", "block.minecraft.banner.stripe_center.white": "Balta vertikāla viduslīnija", "block.minecraft.banner.stripe_center.yellow": "<PERSON>zeltena vertikā<PERSON> vid<PERSON>", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "Pelēka k<PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Gaiši z<PERSON> k<PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka k<PERSON>", "block.minecraft.banner.stripe_downleft.lime": "Gaiši zaļa k<PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Oranža k<PERSON>", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "Balta kreisā <PERSON>āle", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Gaiši z<PERSON>āle", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.lime": "G<PERSON>š<PERSON> za<PERSON>", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "Balta diagonāle", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON> k<PERSON> mala", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON> k<PERSON> mala", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> mala", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON>ān<PERSON> kreis<PERSON> mala", "block.minecraft.banner.stripe_left.gray": "Pelēka kreisā mala", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON>ļ<PERSON> kreis<PERSON> mala", "block.minecraft.banner.stripe_left.light_blue": "Gaiši zila kreisā mala", "block.minecraft.banner.stripe_left.light_gray": "G<PERSON><PERSON>i pelēka kreisā mala", "block.minecraft.banner.stripe_left.lime": "Gaiši zaļa kreisā mala", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> mala", "block.minecraft.banner.stripe_left.orange": "Oranža kreisā mala", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON> k<PERSON> mala", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON> k<PERSON> mala", "block.minecraft.banner.stripe_left.red": "Sarkana kreis<PERSON> mala", "block.minecraft.banner.stripe_left.white": "Balta kreisā mala", "block.minecraft.banner.stripe_left.yellow": "<PERSON>zeltena kreisā mala", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.cyan": "C<PERSON>āna <PERSON> vid<PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON>ē<PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON> <PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Gaiši z<PERSON> horizontāla viduslīni<PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "G<PERSON>ši pelēka horizontāla viduslīni<PERSON>", "block.minecraft.banner.stripe_middle.lime": "Gaiši zaļa horizontāla viduslīnija", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.orange": "Oranža horizontāla vid<PERSON>lī<PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.white": "Balta horizontāla vid<PERSON>lī<PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON><PERSON> horizon<PERSON>ā<PERSON> v<PERSON>", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON> lab<PERSON> mala", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON> mala", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON><PERSON> mala", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON>a labā mala", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON><PERSON> labā mala", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON> mala", "block.minecraft.banner.stripe_right.light_blue": "G<PERSON><PERSON><PERSON> zila labā mala", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka labā mala", "block.minecraft.banner.stripe_right.lime": "G<PERSON><PERSON><PERSON> zaļa labā mala", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON> mala", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON> mala", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON> mala", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON> lab<PERSON> mala", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON>na lab<PERSON> mala", "block.minecraft.banner.stripe_right.white": "Balta labā mala", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON><PERSON><PERSON> labā mala", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "block.minecraft.banner.stripe_top.gray": "Pelē<PERSON> au<PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Gaiši zila augšmala", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēka augš<PERSON>a", "block.minecraft.banner.stripe_top.lime": "Gaiši zaļa augšmala", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Oranža augšmala", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "Balta augšmala", "block.minecraft.banner.stripe_top.yellow": "Dzeltena augšmala", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON> a<PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON> tr<PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> trī<PERSON>tū<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>", "block.minecraft.banner.triangle_bottom.gray": "Pelēks a<PERSON>š<PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> apa<PERSON>šē<PERSON> trīsstūris", "block.minecraft.banner.triangle_bottom.light_gray": "G<PERSON>ši pelēks apakšējais trīsstūris", "block.minecraft.banner.triangle_bottom.lime": "G<PERSON>š<PERSON> za<PERSON> a<PERSON> trīsstūris", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON> trī<PERSON>tū<PERSON>", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON><PERSON> tr<PERSON>", "block.minecraft.banner.triangle_bottom.red": "Sarkans a<PERSON>š<PERSON><PERSON> trī<PERSON>tū<PERSON>", "block.minecraft.banner.triangle_bottom.white": "Balts apakšē<PERSON><PERSON> trī<PERSON>tū<PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON> a<PERSON> trī<PERSON>", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON> aug<PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON> au<PERSON> tr<PERSON>", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON> trīsstūris", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON><PERSON> au<PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.gray": "Pelēks augš<PERSON><PERSON> trī<PERSON>tū<PERSON>", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> augš<PERSON><PERSON><PERSON> trīsstū<PERSON>", "block.minecraft.banner.triangle_top.light_gray": "<PERSON><PERSON><PERSON><PERSON> pelēks augšē<PERSON>is trīsstūris", "block.minecraft.banner.triangle_top.lime": "<PERSON><PERSON><PERSON><PERSON> za<PERSON><PERSON> augšē<PERSON> trīsstūris", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON><PERSON> trī<PERSON>tū<PERSON>", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON><PERSON><PERSON> au<PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON> au<PERSON><PERSON><PERSON><PERSON><PERSON> tr<PERSON>", "block.minecraft.banner.triangle_top.red": "Sarkans aug<PERSON><PERSON><PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.white": "<PERSON>lts augš<PERSON><PERSON><PERSON> trī<PERSON>t<PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON><PERSON> au<PERSON><PERSON> trī<PERSON>", "block.minecraft.banner.triangles_bottom.black": "Melns apakšējais roboju<PERSON>", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.triangles_bottom.gray": "Pelēks apakšējais robojums", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON>šējais roboju<PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Gaiši zils apakšējais robojums", "block.minecraft.banner.triangles_bottom.light_gray": "Gaiši pelēks apakšējais robojums", "block.minecraft.banner.triangles_bottom.lime": "Gaiši za<PERSON>š apakšējais robojums", "block.minecraft.banner.triangles_bottom.magenta": "Fuksīns a<PERSON>šē<PERSON> roboju<PERSON>", "block.minecraft.banner.triangles_bottom.orange": "Oranžs <PERSON>š<PERSON> roboju<PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON><PERSON> tr<PERSON>", "block.minecraft.banner.triangles_bottom.red": "Sarkans apakšējais robojums", "block.minecraft.banner.triangles_bottom.white": "Balts apakšējais robojums", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON> a<PERSON>šēja<PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.black": "<PERSON>ns augšē<PERSON> robo<PERSON>", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> au<PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON><PERSON> au<PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON><PERSON> au<PERSON> r<PERSON>", "block.minecraft.banner.triangles_top.gray": "Pelēks augšējais roboju<PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> augš<PERSON><PERSON> robo<PERSON>", "block.minecraft.banner.triangles_top.light_gray": "Gaiši pelēks augšējais robojums", "block.minecraft.banner.triangles_top.lime": "Gaiš<PERSON> za<PERSON> augšē<PERSON>is roboju<PERSON>", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON><PERSON> au<PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON><PERSON><PERSON> au<PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON> rob<PERSON>", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> au<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.triangles_top.red": "Sarkans augšē<PERSON>is robo<PERSON>", "block.minecraft.banner.triangles_top.white": "Balts augšējais robojums", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON><PERSON> au<PERSON><PERSON> rob<PERSON>", "block.minecraft.barrel": "Muca", "block.minecraft.barrier": "Barjera", "block.minecraft.basalt": "Bazalts", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "Sekundārais s<PERSON>", "block.minecraft.bed.no_sleep": "Tu vari gulēt tikai nakts vai negaisa laikā", "block.minecraft.bed.not_safe": "<PERSON> tagad nevari at<PERSON>, <PERSON><PERSON><PERSON> ir br<PERSON>i", "block.minecraft.bed.obstructed": "<PERSON><PERSON> gulta ir aizspros<PERSON>ta", "block.minecraft.bed.occupied": "<PERSON><PERSON> g<PERSON>a ir a<PERSON>", "block.minecraft.bed.too_far_away": "<PERSON> nevari tagad gul<PERSON>t, gulta atrodas pārāk tālu", "block.minecraft.bedrock": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON><PERSON><PERSON> strops", "block.minecraft.beetroots": "Bietes", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> k<PERSON>", "block.minecraft.birch_button": "Bērza poga", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON><PERSON> durvis", "block.minecraft.birch_fence": "Bērza žogs", "block.minecraft.birch_fence_gate": "Bērza žoga vārti", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Bērz<PERSON> baļķis", "block.minecraft.birch_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> spiediena pl<PERSON>ne", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON> st<PERSON>", "block.minecraft.birch_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>a si<PERSON>", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_wood": "Bērza koks", "block.minecraft.black_banner": "<PERSON><PERSON>s", "block.minecraft.black_bed": "<PERSON><PERSON> g<PERSON>", "block.minecraft.black_candle": "<PERSON><PERSON> svece", "block.minecraft.black_candle_cake": "<PERSON>te ar melnu sveci", "block.minecraft.black_carpet": "<PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON> betons", "block.minecraft.black_concrete_powder": "Melns betona pulveris", "block.minecraft.black_glazed_terracotta": "<PERSON>na glaz<PERSON>ta te<PERSON>ota", "block.minecraft.black_shulker_box": "<PERSON><PERSON> s<PERSON> kaste", "block.minecraft.black_stained_glass": "Melns krāsots stikls", "block.minecraft.black_stained_glass_pane": "<PERSON>na k<PERSON><PERSON> stikla plā<PERSON>", "block.minecraft.black_terracotta": "<PERSON><PERSON>", "block.minecraft.black_wool": "Melna vilna", "block.minecraft.blackstone": "Melnakmens", "block.minecraft.blackstone_slab": "<PERSON>nak<PERSON><PERSON>", "block.minecraft.blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> mūris", "block.minecraft.blast_furnace": "<PERSON><PERSON>", "block.minecraft.blue_banner": "<PERSON><PERSON>s", "block.minecraft.blue_bed": "<PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON> s<PERSON>ce", "block.minecraft.blue_candle_cake": "Torte ar zilu sveci", "block.minecraft.blue_carpet": "<PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON>ils betons", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON> betona pulveris", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON> g<PERSON> te<PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON> s<PERSON> kaste", "block.minecraft.blue_stained_glass": "Zils krāsots stikls", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON> stikla plāksne", "block.minecraft.blue_terracotta": "<PERSON><PERSON>", "block.minecraft.blue_wool": "Zila vilna", "block.minecraft.bone_block": "Kaulu bloks", "block.minecraft.bookshelf": "<PERSON>r<PERSON><PERSON><PERSON> plaukts", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "Smadzeņu koraļļu bloks", "block.minecraft.brain_coral_fan": "S<PERSON><PERSON><PERSON><PERSON> k<PERSON> spā<PERSON>", "block.minecraft.brain_coral_wall_fan": "Smadzeņ<PERSON> k<PERSON>ļļ<PERSON> si<PERSON> v<PERSON>", "block.minecraft.brewing_stand": "Brūvēšanas statīvs", "block.minecraft.brick_slab": "Ķieģeļu plāksne", "block.minecraft.brick_stairs": "Ķieģeļu kāpnes", "block.minecraft.brick_wall": "Ķieģeļu mūris", "block.minecraft.bricks": "Ķieģeļi", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ce", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> ar br<PERSON> sveci", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON><PERSON> betons", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> betona pulveris", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> gla<PERSON> te<PERSON>", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kaste", "block.minecraft.brown_stained_glass": "<PERSON>rūns kr<PERSON> stikls", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON> stikla plāksne", "block.minecraft.brown_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_wool": "Brūna vilna", "block.minecraft.bubble_column": "Burbuļu kollona", "block.minecraft.bubble_coral": "<PERSON>ur<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Burbuļu koraļļu bloks", "block.minecraft.bubble_coral_fan": "<PERSON>ur<PERSON><PERSON><PERSON> k<PERSON> spā<PERSON>", "block.minecraft.bubble_coral_wall_fan": "Burbuļu koraļļ<PERSON> si<PERSON> v<PERSON>", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON>s ametists", "block.minecraft.bush": "Krūms", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Kaktusa Zieds", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Noregulēts skalu sensors", "block.minecraft.campfire": "Ugunskurs", "block.minecraft.candle": "Svece", "block.minecraft.candle_cake": "<PERSON><PERSON> ar sveci", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Kartogrāfijas galds", "block.minecraft.carved_pumpkin": "Izgrebts ķirbis", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON> gaiss", "block.minecraft.cave_vines": "<PERSON><PERSON>\n", "block.minecraft.cave_vines_plant": "<PERSON><PERSON> li<PERSON> augs", "block.minecraft.chain": "Ķēde", "block.minecraft.chain_command_block": "Ķēdes komandu bloks", "block.minecraft.cherry_button": "Ķirša poga", "block.minecraft.cherry_door": "Ķirša durvis", "block.minecraft.cherry_fence": "Ķirša žogs", "block.minecraft.cherry_fence_gate": "Ķirša žoga vārti", "block.minecraft.cherry_hanging_sign": "Iekarinā<PERSON> ķirša zīme", "block.minecraft.cherry_leaves": "Ķirša lapas", "block.minecraft.cherry_log": "Ķirša baļķis", "block.minecraft.cherry_planks": "Ķirša dēļi", "block.minecraft.cherry_pressure_plate": "Ķirša spiediena plātne", "block.minecraft.cherry_sapling": "Ķirša stāds", "block.minecraft.cherry_sign": "Ķirša zīme", "block.minecraft.cherry_slab": "Ķirša plāksne", "block.minecraft.cherry_stairs": "Ķirša kāpnes", "block.minecraft.cherry_trapdoor": "Ķirša lūka", "block.minecraft.cherry_wall_hanging_sign": "<PERSON>ekarin<PERSON><PERSON> ķirša sienas zīme", "block.minecraft.cherry_wall_sign": "Ķirša sienas zīme", "block.minecraft.cherry_wood": "Ķirša koks", "block.minecraft.chest": "<PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lakta", "block.minecraft.chiseled_bookshelf": "Slīpēts grāmatu plaukts", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON>", "block.minecraft.chiseled_deepslate": "Slīpēts dziļslāneklis", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ķieģeļi", "block.minecraft.chiseled_polished_blackstone": "Nolīpēts pulēts melnakmens", "block.minecraft.chiseled_quartz_block": "Slīpēts kvarca bloks", "block.minecraft.chiseled_red_sandstone": "Slīpēts sarkanais smilšakmens", "block.minecraft.chiseled_resin_bricks": "Slīpē<PERSON> Tu<PERSON> Ķieģeļi", "block.minecraft.chiseled_sandstone": "Noslīpēts smilšakmens", "block.minecraft.chiseled_stone_bricks": "Noslīp<PERSON><PERSON> a<PERSON> ķieģeļi", "block.minecraft.chiseled_tuff": "Slīpēts Tufs", "block.minecraft.chiseled_tuff_bricks": "Slīpē<PERSON> Tu<PERSON> ķieģeļi", "block.minecraft.chorus_flower": "<PERSON><PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON> augs", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Aizvērts a<PERSON>ied<PERSON>", "block.minecraft.coal_block": "<PERSON>g<PERSON><PERSON> blo<PERSON>", "block.minecraft.coal_ore": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON><PERSON> zeme", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON>ā<PERSON>", "block.minecraft.cobbled_deepslate_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mū<PERSON>", "block.minecraft.cobblestone": "Bruģakmens", "block.minecraft.cobblestone_slab": "Bruģakmens plāksne", "block.minecraft.cobblestone_stairs": "Bruģakmens kāpnes", "block.minecraft.cobblestone_wall": "Bruģakmens mūris", "block.minecraft.cobweb": "Zirnekļtīkls", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Komandu bloks", "block.minecraft.comparator": "Redstone salīdzinātājs", "block.minecraft.composter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.conduit": "Konduits", "block.minecraft.copper_block": "Vara bloks", "block.minecraft.copper_bulb": "Vara spuldze", "block.minecraft.copper_door": "<PERSON><PERSON> durvis", "block.minecraft.copper_grate": "Vara režģis", "block.minecraft.copper_ore": "<PERSON>ara rūda", "block.minecraft.copper_trapdoor": "<PERSON>ara lama<PERSON>u durvis", "block.minecraft.cornflower": "Rudzupuķe", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> ķieģeļi", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ķieģeļi", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ķieģeļi", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ķieģeļi", "block.minecraft.crafter": "Amatnieks", "block.minecraft.crafting_table": "Dar<PERSON><PERSON><PERSON>", "block.minecraft.creaking_heart": "Čīkt<PERSON>ņ<PERSON> sir<PERSON>", "block.minecraft.creeper_head": "Creeper galva", "block.minecraft.creeper_wall_head": "Creeper galva uz sienas", "block.minecraft.crimson_button": "Tumšsarkana poga", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dur<PERSON>", "block.minecraft.crimson_fence": "Tumšsarkans ž<PERSON>s", "block.minecraft.crimson_fence_gate": "Tumšsarkana žoga vārti", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tum<PERSON>", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "Tumš<PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.crimson_pressure_plate": "Tumšsar<PERSON>a spiediena plāksne", "block.minecraft.crimson_roots": "Tumšsarkanas saknes", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON>š<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.crimson_trapdoor": "Tumšsarkana l<PERSON>", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tum<PERSON><PERSON> si<PERSON>", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper": "Šķelts varš", "block.minecraft.cut_copper_slab": "Šķelta vara plāksne", "block.minecraft.cut_copper_stairs": "Šķelta vara pakāpieni", "block.minecraft.cut_red_sandstone": "Griezts sarkanais smilšakmens", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON> sarka<PERSON> s<PERSON>šak<PERSON> plā<PERSON>", "block.minecraft.cut_sandstone": "Griezts smilšakmens", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> p<PERSON>", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.cyan_candle": "Ciāna svece", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> ar ci<PERSON>u sveci", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON> betons", "block.minecraft.cyan_concrete_powder": "Ciān<PERSON> betona pulveris", "block.minecraft.cyan_glazed_terracotta": "Ciāna glaz<PERSON>ta terakota", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON> shul<PERSON> kaste", "block.minecraft.cyan_stained_glass": "Ciāns kr<PERSON> stikls", "block.minecraft.cyan_stained_glass_pane": "Ciāna kr<PERSON>sota stikla plāksne", "block.minecraft.cyan_terracotta": "Ciāna te<PERSON>", "block.minecraft.cyan_wool": "Ciāna vilna", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON> lakta", "block.minecraft.dandelion": "Pienene", "block.minecraft.dark_oak_button": "Tumšā ozola poga", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON><PERSON> durvis", "block.minecraft.dark_oak_fence": "Tumšā ozola žogs", "block.minecraft.dark_oak_fence_gate": "Tumšā ozola žoga vārti", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tumš<PERSON> ozola z<PERSON>me", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON> ozola lapas", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON><PERSON> ozola baļķis", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON><PERSON> dē<PERSON>i", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>ola spiediena plātne", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON><PERSON>ola stāds", "block.minecraft.dark_oak_sign": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "block.minecraft.dark_oak_slab": "Tumšā <PERSON>ola plā<PERSON>ne", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.dark_oak_trapdoor": "Tumšā ozola lūka", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tumš<PERSON> ozola sienas z<PERSON>me", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON> ozola sienas z<PERSON>me", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON>is ozols", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.daylight_detector": "Dienasgaismas detektors", "block.minecraft.dead_brain_coral": "Beigts smadzeņu k<PERSON>lis", "block.minecraft.dead_brain_coral_block": "Beigts smadzeņu koraļļu bloks", "block.minecraft.dead_brain_coral_fan": "Beigts smadzeņu koraļļa vēdeklis", "block.minecraft.dead_brain_coral_wall_fan": "Beigts smadzeņu koraļļa sienas v<PERSON>dek<PERSON>", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON> burbuļu k<PERSON>", "block.minecraft.dead_bubble_coral_block": "Beigts burbuļu koraļļu bloks", "block.minecraft.dead_bubble_coral_fan": "Beigts burbuļu koraļļa vēdeklis", "block.minecraft.dead_bubble_coral_wall_fan": "Beigts burbuļu koraļļa siena<PERSON> v<PERSON>", "block.minecraft.dead_bush": "<PERSON>kal<PERSON> kr<PERSON>", "block.minecraft.dead_fire_coral": "<PERSON><PERSON>ts uguns koral<PERSON>", "block.minecraft.dead_fire_coral_block": "Beigts uguns koraļļu bloks", "block.minecraft.dead_fire_coral_fan": "Beigts uguns koraļļa vēdeklis", "block.minecraft.dead_fire_coral_wall_fan": "Beigts uguns k<PERSON>ļļa si<PERSON> v<PERSON>", "block.minecraft.dead_horn_coral": "<PERSON>igts raga korallis", "block.minecraft.dead_horn_coral_block": "Beigts raga koraļļu bloks", "block.minecraft.dead_horn_coral_fan": "Beigts raga koraļļa vēdeklis", "block.minecraft.dead_horn_coral_wall_fan": "Beigts raga koraļļa siena<PERSON> v<PERSON>", "block.minecraft.dead_tube_coral": "<PERSON><PERSON> cauru<PERSON> k<PERSON>", "block.minecraft.dead_tube_coral_block": "Beigts caurules koraļļu bloks", "block.minecraft.dead_tube_coral_fan": "Beigts caurules koraļļa vēdeklis", "block.minecraft.dead_tube_coral_wall_fan": "Beigts caurules koraļļa si<PERSON> v<PERSON>", "block.minecraft.decorated_pot": "Izdekorēts pods", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "D<PERSON>ļ<PERSON>lānekļa ķieģeļu plāksne", "block.minecraft.deepslate_brick_stairs": "<PERSON><PERSON>ļ<PERSON><PERSON>ān<PERSON>ļa ķieģeļu kāpnes", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a ķieģeļu mūris", "block.minecraft.deepslate_bricks": "D<PERSON><PERSON><PERSON><PERSON>ān<PERSON><PERSON>a ķieģeļi", "block.minecraft.deepslate_coal_ore": "D<PERSON>ļ<PERSON><PERSON>ān<PERSON><PERSON><PERSON> og<PERSON> rūda", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>ān<PERSON><PERSON>a vara rūda", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dimanta rūda", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> smaragda rūda", "block.minecraft.deepslate_gold_ore": "D<PERSON>ļ<PERSON><PERSON>ān<PERSON><PERSON><PERSON> z<PERSON> r<PERSON>da", "block.minecraft.deepslate_iron_ore": "D<PERSON>ļ<PERSON><PERSON>ān<PERSON><PERSON><PERSON> d<PERSON>z<PERSON> rūda", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON> rūda", "block.minecraft.deepslate_redstone_ore": "Dziļslānekļa Redstone rūda", "block.minecraft.deepslate_tile_slab": "Dziļslānekļ<PERSON> flī<PERSON> plā<PERSON>ne", "block.minecraft.deepslate_tile_stairs": "D<PERSON>ļ<PERSON><PERSON>ān<PERSON><PERSON><PERSON> fl<PERSON> k<PERSON>", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>ān<PERSON><PERSON><PERSON> flī<PERSON>u mūris", "block.minecraft.deepslate_tiles": "D<PERSON>ļ<PERSON><PERSON>ān<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "Detektorsliedes", "block.minecraft.diamond_block": "<PERSON><PERSON><PERSON> bloks", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON> mū<PERSON>", "block.minecraft.dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "Pūķa ola", "block.minecraft.dragon_head": "Pūķa galva", "block.minecraft.dragon_wall_head": "Pūķa galva uz sienas", "block.minecraft.dried_ghast": "Izžuvis spoks", "block.minecraft.dried_kelp_block": "Izkaltētu brūnaļģu bloks", "block.minecraft.dripstone_block": "Tekakmens bloks", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Smaragda bloks", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON> rūda", "block.minecraft.enchanting_table": "Burvestību galds", "block.minecraft.end_gateway": "<PERSON> vā<PERSON>ja", "block.minecraft.end_portal": "End portāls", "block.minecraft.end_portal_frame": "<PERSON> port<PERSON> rā<PERSON>", "block.minecraft.end_rod": "<PERSON> stienis", "block.minecraft.end_stone": "<PERSON> Akmens", "block.minecraft.end_stone_brick_slab": "End akmens ķieģeļu plāksne", "block.minecraft.end_stone_brick_stairs": "End akmens ķieģeļu kāpnes", "block.minecraft.end_stone_brick_wall": "End akmens ķieģeļu mūris", "block.minecraft.end_stone_bricks": "End akmens ķieģeļi", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Atklāts slīpēts varš", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_bulb": "Atklā<PERSON> vara spuldze", "block.minecraft.exposed_copper_door": "Atsegtas vara durvis", "block.minecraft.exposed_copper_grate": "Atklāts vara režģis", "block.minecraft.exposed_copper_trapdoor": "Atsegtas vara trapa durvis", "block.minecraft.exposed_cut_copper": "Atsegts šķelts varš", "block.minecraft.exposed_cut_copper_slab": "Atsegta šķelta vara plāksne", "block.minecraft.exposed_cut_copper_stairs": "Atsegti šķelti vara pakāpieni", "block.minecraft.farmland": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON>", "block.minecraft.fire": "Uguns", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON><PERSON> k<PERSON> bloks", "block.minecraft.fire_coral_fan": "<PERSON><PERSON><PERSON> spā<PERSON>", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON> k<PERSON> si<PERSON> v<PERSON>", "block.minecraft.firefly_bush": "Spīdvabo<PERSON><PERSON>", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON> galds", "block.minecraft.flower_pot": "Puķupods", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.furnace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Melnakmens", "block.minecraft.glass": "Stikls", "block.minecraft.glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.glow_lichen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ķērpji", "block.minecraft.glowstone": "Mirdzakmens", "block.minecraft.gold_block": "<PERSON><PERSON><PERSON> bloks", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.granite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_wall": "<PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.gravel": "<PERSON>s", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.gray_candle": "Pelēka svece", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> ar pelēku sveci", "block.minecraft.gray_carpet": "Pelēks p<PERSON>l<PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON><PERSON> betons", "block.minecraft.gray_concrete_powder": "Pelēks betona pulveris", "block.minecraft.gray_glazed_terracotta": "Pelēka glaz<PERSON>ta terakota", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON> kaste", "block.minecraft.gray_stained_glass": "Pelēks krāsots stikls", "block.minecraft.gray_stained_glass_pane": "Pelēka krāsota stikla plāksne", "block.minecraft.gray_terracotta": "Pelēka te<PERSON>", "block.minecraft.gray_wool": "Pelēka vilna", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.green_candle": "Zaļa svece", "block.minecraft.green_candle_cake": "Torte ar za<PERSON><PERSON> sveci", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> betona pulveris", "block.minecraft.green_glazed_terracotta": "Z<PERSON>ļa glaz<PERSON>ta terakota", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kaste", "block.minecraft.green_stained_glass": "Z<PERSON>ļš krāsots stikls", "block.minecraft.green_stained_glass_pane": "Zaļa krāsota stikla plāksne", "block.minecraft.green_terracotta": "Zaļa te<PERSON>", "block.minecraft.green_wool": "Zaļa vilna", "block.minecraft.grindstone": "Galoda", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hay_block": "Siena ķīpa", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> kodols", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON><PERSON><PERSON> spiediena pl<PERSON>ne", "block.minecraft.honey_block": "Medus bloks", "block.minecraft.honeycomb_block": "<PERSON><PERSON> bloks", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "<PERSON><PERSON> k<PERSON>", "block.minecraft.horn_coral_block": "Raga k<PERSON>ļļu bloks", "block.minecraft.horn_coral_fan": "<PERSON><PERSON> k<PERSON> spā<PERSON>", "block.minecraft.horn_coral_wall_fan": "Raga Koraļļa Sienas Vēdeklis", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Inficēti noslīpēti akmens ķieģeļi", "block.minecraft.infested_cobblestone": "Inficēts bruģakmens", "block.minecraft.infested_cracked_stone_bricks": "In<PERSON><PERSON>ti saplai<PERSON>ā<PERSON><PERSON> a<PERSON> ķieģeļi", "block.minecraft.infested_deepslate": "Inficēts dziļslāneklis", "block.minecraft.infested_mossy_stone_bricks": "Inficēti apsūnojuši akmens ķieģeļi", "block.minecraft.infested_stone": "Inficēts akmens", "block.minecraft.infested_stone_bricks": "Inficēti akmens ķieģeļi", "block.minecraft.iron_bars": "Dzelzs restes", "block.minecraft.iron_block": "Dzelzs bloks", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Dzelzs l<PERSON>", "block.minecraft.jack_o_lantern": "Ķirbja laterna", "block.minecraft.jigsaw": "<PERSON><PERSON><PERSON> bloks", "block.minecraft.jukebox": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_button": "Džungļu koka poga", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> durvis", "block.minecraft.jungle_fence": "Džungļu koka žogs", "block.minecraft.jungle_fence_gate": "Džungļu žoga vārti", "block.minecraft.jungle_hanging_sign": "Iekarin<PERSON><PERSON> d<PERSON>ļ<PERSON> koka z<PERSON>me", "block.minecraft.jungle_leaves": "Džungļu koka lapas", "block.minecraft.jungle_log": "Džungļu koka baļķis", "block.minecraft.jungle_planks": "Džungļu koka dēļi", "block.minecraft.jungle_pressure_plate": "Džungļu koka spiediena plātne", "block.minecraft.jungle_sapling": "Džungļu koka stāds", "block.minecraft.jungle_sign": "Džungļu koka zīme", "block.minecraft.jungle_slab": "Džungļu koka plāksne", "block.minecraft.jungle_stairs": "Džungļu koka kāpnes", "block.minecraft.jungle_trapdoor": "Džungļu lūka", "block.minecraft.jungle_wall_hanging_sign": "Iekarin<PERSON><PERSON> d<PERSON>u koka sienas z<PERSON>me", "block.minecraft.jungle_wall_sign": "Džungļu koka sienas z<PERSON>me", "block.minecraft.jungle_wood": "Džungļu koks", "block.minecraft.kelp": "Brūnaļģe", "block.minecraft.kelp_plant": "Brūnaļģes augs", "block.minecraft.ladder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloks", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON> ametista krist<PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON> papa<PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavas katls", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "Lektora galds", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>s", "block.minecraft.light_blue_bed": "Gaiši zila gulta", "block.minecraft.light_blue_candle": "Gaiši zila svece", "block.minecraft.light_blue_candle_cake": "Torte ar gaiši zilu sveci", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>s", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON><PERSON> zils betons", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> zils betona pulveris", "block.minecraft.light_blue_glazed_terracotta": "Gaiši zila glazēta terakota", "block.minecraft.light_blue_shulker_box": "G<PERSON>ši zila shulker kaste", "block.minecraft.light_blue_stained_glass": "Gaiši zils krāsots stikls", "block.minecraft.light_blue_stained_glass_pane": "Gaiši zila krāsota stikla plāksne", "block.minecraft.light_blue_terracotta": "Gaiši zila terakota", "block.minecraft.light_blue_wool": "Gaiši zila vilna", "block.minecraft.light_gray_banner": "Gaiši pelēks ka<PERSON>s", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ka gulta", "block.minecraft.light_gray_candle": "Gaiši pelēka svece", "block.minecraft.light_gray_candle_cake": "Torte ar gaiši pelēku sveci", "block.minecraft.light_gray_carpet": "Gaiši pelēks paklājs", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON><PERSON> pel<PERSON>ks betons", "block.minecraft.light_gray_concrete_powder": "G<PERSON>ši pelēks betona pulveris", "block.minecraft.light_gray_glazed_terracotta": "G<PERSON>ši pelēka glazēta terakota", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON> pel<PERSON>ka shulker kaste", "block.minecraft.light_gray_stained_glass": "Gaiši pelēks krāsots stikls", "block.minecraft.light_gray_stained_glass_pane": "Gaiši pelēka krāsota stikla plāksne", "block.minecraft.light_gray_terracotta": "G<PERSON>ši pelēka terakota", "block.minecraft.light_gray_wool": "G<PERSON>ši pelēka vilna", "block.minecraft.light_weighted_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> spiediena p<PERSON>", "block.minecraft.lightning_rod": "Zibensnovedējs", "block.minecraft.lilac": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "Maijpuķīte", "block.minecraft.lily_pad": "Ūdensrozes", "block.minecraft.lime_banner": "Gaiši z<PERSON><PERSON>s", "block.minecraft.lime_bed": "Gaiši zaļa gulta", "block.minecraft.lime_candle": "Gaiši zaļa svece", "block.minecraft.lime_candle_cake": "Torte ar gaiši zaļu sveci", "block.minecraft.lime_carpet": "Gaiš<PERSON> z<PERSON><PERSON> pakl<PERSON>s", "block.minecraft.lime_concrete": "Gaiš<PERSON> z<PERSON><PERSON> bet<PERSON>", "block.minecraft.lime_concrete_powder": "Gaiši za<PERSON><PERSON> betona pulveris", "block.minecraft.lime_glazed_terracotta": "Gaiši zaļa glazēta terakota", "block.minecraft.lime_shulker_box": "G<PERSON><PERSON><PERSON> zaļa shulker kaste", "block.minecraft.lime_stained_glass": "Gaiši zaļš krāsots stikls", "block.minecraft.lime_stained_glass_pane": "Gaiši zaļa krāsota stikla plāksne", "block.minecraft.lime_terracotta": "Gaiši zaļa terakota", "block.minecraft.lime_wool": "Gaiši zaļa vilna", "block.minecraft.lodestone": "Dzīslakmens", "block.minecraft.loom": "Stelles", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ce", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON> ar fuk<PERSON> sveci", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON> betons", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> betona pulveris", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> gla<PERSON> te<PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kaste", "block.minecraft.magenta_stained_glass": "Fuksīns krā<PERSON>ts stikls", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> kr<PERSON><PERSON> stikla plāksne", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "Fuksīna vilna", "block.minecraft.magma_block": "Magmas bloks", "block.minecraft.mangrove_button": "Mangroves poga", "block.minecraft.mangrove_door": "Mangroves durvis", "block.minecraft.mangrove_fence": "Mangroves žogs", "block.minecraft.mangrove_fence_gate": "Mangroves žoga vārti", "block.minecraft.mangrove_hanging_sign": "Iekarināta mangroves zīme", "block.minecraft.mangrove_leaves": "Mangroves lapas", "block.minecraft.mangrove_log": "Mangroves baļķis", "block.minecraft.mangrove_planks": "Mangroves dēļi", "block.minecraft.mangrove_pressure_plate": "Mangroves spiediena pl<PERSON>ne", "block.minecraft.mangrove_propagule": "Mangroves pumpurs", "block.minecraft.mangrove_roots": "Mangroves saknes", "block.minecraft.mangrove_sign": "Mangroves zīme", "block.minecraft.mangrove_slab": "Mangroves plāksne", "block.minecraft.mangrove_stairs": "Mangroves kāpnes", "block.minecraft.mangrove_trapdoor": "Mangroves lūka", "block.minecraft.mangrove_wall_hanging_sign": "Iekarināta mangroves sienas zīme", "block.minecraft.mangrove_wall_sign": "Mangroves sienas zīmes", "block.minecraft.mangrove_wood": "Mangroves koks", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON><PERSON><PERSON> ameti<PERSON> k<PERSON>", "block.minecraft.melon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON> stumbrs", "block.minecraft.moss_block": "<PERSON><PERSON><PERSON> bloks", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bruģakmens", "block.minecraft.mossy_cobblestone_slab": "Apsūnojusi bruģakmens plāksne", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bruģakmens kāpnes", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bruģakmens mūris", "block.minecraft.mossy_stone_brick_slab": "Apsūnojusi akmens ķieģeļu plāksne", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ķieģeļu kāpnes", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ak<PERSON> ķieģeļu mūris", "block.minecraft.mossy_stone_bricks": "Apsūnojuši akmens ķieģeļi", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.mud": "<PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Dubļu ķieģeļu plāksne", "block.minecraft.mud_brick_stairs": "<PERSON><PERSON><PERSON> ķieģeļu kāpnes", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON> ķieģeļu mūris", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON> ķieģeļi", "block.minecraft.muddy_mangrove_roots": "Dubļainas mangroves saknes", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "<PERSON><PERSON> ķieģeļu mūris", "block.minecraft.nether_brick_slab": "Nether ķieģeļu plāksne", "block.minecraft.nether_brick_stairs": "Nether ķieģeļu kāpnes", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> ķieģeļu mūris", "block.minecraft.nether_bricks": "Nether ķieģeļi", "block.minecraft.nether_gold_ore": "<PERSON>her zelta r<PERSON>da", "block.minecraft.nether_portal": "Nether port<PERSON>ls", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON> k<PERSON><PERSON> r<PERSON>da", "block.minecraft.nether_sprouts": "<PERSON><PERSON> asni", "block.minecraft.nether_wart": "<PERSON><PERSON> k<PERSON>a", "block.minecraft.nether_wart_block": "<PERSON><PERSON> k<PERSON>u bloks", "block.minecraft.netherite_block": "Nezerīta blo<PERSON>", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "<PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.oak_button": "Ozola poga", "block.minecraft.oak_door": "<PERSON><PERSON> durvis", "block.minecraft.oak_fence": "Ozola žogs", "block.minecraft.oak_fence_gate": "Ozolkoka žoga vārti", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>", "block.minecraft.oak_leaves": "<PERSON><PERSON> lapas", "block.minecraft.oak_log": "Ozola baļķis", "block.minecraft.oak_planks": "<PERSON><PERSON> d<PERSON>", "block.minecraft.oak_pressure_plate": "Ozola spiediena pl<PERSON>ne", "block.minecraft.oak_sapling": "Oz<PERSON> stāds", "block.minecraft.oak_sign": "<PERSON><PERSON>", "block.minecraft.oak_slab": "Ozola plāksne", "block.minecraft.oak_stairs": "<PERSON><PERSON>", "block.minecraft.oak_trapdoor": "Ozola lūka", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> sienas z<PERSON>me", "block.minecraft.oak_wall_sign": "<PERSON><PERSON> sienas z<PERSON>me", "block.minecraft.oak_wood": "Oz<PERSON>", "block.minecraft.observer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON>a", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>s", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_bed": "Oranža gulta", "block.minecraft.orange_candle": "Oranža svece", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> ar oran<PERSON> s<PERSON>ci", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "Oran<PERSON><PERSON> betona pulveris", "block.minecraft.orange_glazed_terracotta": "Oranža glazēta terakota", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON><PERSON> shul<PERSON> kaste", "block.minecraft.orange_stained_glass": "Oranžs krāsots stikls", "block.minecraft.orange_stained_glass_pane": "Oranža krāsota stikla plāksne", "block.minecraft.orange_terracotta": "Oranža terakota", "block.minecraft.orange_tulip": "Oranža tulpe", "block.minecraft.orange_wool": "Oranža vilna", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Oksidēts slīpēts varš", "block.minecraft.oxidized_copper": "Ok<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_bulb": "Oksidē<PERSON> vara spuldze", "block.minecraft.oxidized_copper_door": "Oksid<PERSON><PERSON> vara durvis", "block.minecraft.oxidized_copper_grate": "Oksidē<PERSON> vara režģis", "block.minecraft.oxidized_copper_trapdoor": "Oksid<PERSON><PERSON> vara lamatas durvis", "block.minecraft.oxidized_cut_copper": "Oksidēts šķelts varš", "block.minecraft.oxidized_cut_copper_slab": "Oksidēta šķelta vara plāksne", "block.minecraft.oxidized_cut_copper_stairs": "Oksidēti šķelti vara pakāpieni", "block.minecraft.packed_ice": "<PERSON><PERSON>", "block.minecraft.packed_mud": "Saspiesti <PERSON>ļi", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_moss_block": "<PERSON><PERSON><PERSON> s<PERSON> bloks", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON> s<PERSON> p<PERSON>", "block.minecraft.pale_oak_button": "Bālā ozola poga", "block.minecraft.pale_oak_door": "<PERSON><PERSON><PERSON><PERSON>ola durvis", "block.minecraft.pale_oak_fence": "Bālā ozola žogs", "block.minecraft.pale_oak_fence_gate": "Bālā ozola žoga vārti", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON> z<PERSON>", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON> ozola lapas", "block.minecraft.pale_oak_log": "<PERSON><PERSON><PERSON><PERSON> ozola baļķis", "block.minecraft.pale_oak_planks": "<PERSON><PERSON><PERSON><PERSON> dē<PERSON>i", "block.minecraft.pale_oak_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>ola spiediena plātne", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON>ola stāds", "block.minecraft.pale_oak_sign": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "block.minecraft.pale_oak_slab": "Bā<PERSON>ā <PERSON> plā<PERSON>ne", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.pale_oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON> ozola lūka", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> sienas piekari<PERSON>me", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON><PERSON> siena<PERSON> z<PERSON>", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON>s ozols", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>a", "block.minecraft.peony": "Peonija", "block.minecraft.petrified_oak_slab": "Pārakmeņojisies ozola plāksne", "block.minecraft.piglin_head": "<PERSON><PERSON> galva", "block.minecraft.piglin_wall_head": "<PERSON><PERSON> galva uz sienas", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON> s<PERSON>ce", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> ar roz<PERSON> s<PERSON>ci", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON> betona pulveris", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON> glaz<PERSON>ta terakota", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON> s<PERSON> kaste", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> stikls", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> stikla plā<PERSON>", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> tulpe", "block.minecraft.pink_wool": "<PERSON><PERSON><PERSON> vilna", "block.minecraft.piston": "Virzulis", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON> galva", "block.minecraft.pitcher_crop": "<PERSON><PERSON><PERSON><PERSON> st<PERSON>", "block.minecraft.pitcher_plant": "Nepente", "block.minecraft.player_head": "Spēlē<PERSON><PERSON><PERSON> galva", "block.minecraft.player_head.named": "%s galva", "block.minecraft.player_wall_head": "Spēlētāja galva uz sienas", "block.minecraft.podzol": "Podzols", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.polished_andesite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_slab": "<PERSON><PERSON><PERSON><PERSON> and<PERSON><PERSON> p<PERSON>", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON><PERSON><PERSON> and<PERSON><PERSON>", "block.minecraft.polished_basalt": "Nospod<PERSON><PERSON><PERSON><PERSON> baz<PERSON>", "block.minecraft.polished_blackstone": "<PERSON><PERSON>ēts melnakmens", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> ķieģeļu plāksne", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> ķieģeļu kāpnes", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON><PERSON> meln<PERSON> ķieģeļu mūris", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON> Ķieģeļi", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> Spiediena <PERSON>ne", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> p<PERSON>", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> mūris", "block.minecraft.polished_deepslate": "Pulēts dziļslāneklis", "block.minecraft.polished_deepslate_slab": "<PERSON><PERSON><PERSON><PERSON>ā<PERSON>", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mū<PERSON>", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ā<PERSON>", "block.minecraft.polished_diorite_stairs": "Pulētas diorī<PERSON>", "block.minecraft.polished_granite": "<PERSON><PERSON><PERSON><PERSON> gran<PERSON>", "block.minecraft.polished_granite_slab": "<PERSON><PERSON><PERSON><PERSON> gran<PERSON>ta p<PERSON>ā<PERSON>", "block.minecraft.polished_granite_stairs": "Pulētas gran<PERSON>", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "Pulēta <PERSON> p<PERSON>", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON><PERSON>ena", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Akācijas stāds puķupodā", "block.minecraft.potted_allium": "Sīpoli puķupodā", "block.minecraft.potted_azalea_bush": "Acālija puķupodā", "block.minecraft.potted_azure_bluet": "Debeszilā rudzupuķe puķupodā", "block.minecraft.potted_bamboo": "Bambuss puķupodā", "block.minecraft.potted_birch_sapling": "<PERSON><PERSON><PERSON><PERSON> stāds puķupodā", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON> puķupodā", "block.minecraft.potted_brown_mushroom": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> puķupodā", "block.minecraft.potted_cactus": "Kaktus puķupodā", "block.minecraft.potted_cherry_sapling": "Ķirša stāds puķupodā", "block.minecraft.potted_closed_eyeblossom": "Aizv<PERSON><PERSON> aczied<PERSON> puķupodā", "block.minecraft.potted_cornflower": "Rudzupuķe puķupodā", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>n<PERSON> puķupodā", "block.minecraft.potted_crimson_roots": "Tumšsar<PERSON><PERSON> saknes puķupodā", "block.minecraft.potted_dandelion": "Pienene puķupodā", "block.minecraft.potted_dark_oak_sapling": "<PERSON><PERSON><PERSON><PERSON> ozola stāds puķupodā", "block.minecraft.potted_dead_bush": "<PERSON><PERSON><PERSON> krūms puķupodā", "block.minecraft.potted_fern": "Paparde puķupodā", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> puķupodā", "block.minecraft.potted_jungle_sapling": "Džungļu koka stāds puķupodā", "block.minecraft.potted_lily_of_the_valley": "Maijpuķīte puķupodā", "block.minecraft.potted_mangrove_propagule": "Mangroves pumpurs puķupodā", "block.minecraft.potted_oak_sapling": "Ozola stāds puķupodā", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON><PERSON><PERSON><PERSON> puķupodā", "block.minecraft.potted_orange_tulip": "Oranža tulpe puķupodā", "block.minecraft.potted_oxeye_daisy": "Margrietiņa puķupodā", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON> ozola stāds puķupodā", "block.minecraft.potted_pink_tulip": "Rozā tulpe puķupodā", "block.minecraft.potted_poppy": "Ma<PERSON>e puķupodā", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON>na s<PERSON>ne puķupodā", "block.minecraft.potted_red_tulip": "Sarkana tulpe puķupodā", "block.minecraft.potted_spruce_sapling": "<PERSON><PERSON> stāds puķupodā", "block.minecraft.potted_torchflower": "<PERSON><PERSON><PERSON> z<PERSON> puķupodā", "block.minecraft.potted_warped_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON> sēnīte puķupodā", "block.minecraft.potted_warped_roots": "Izlocītas saknes puķupodā", "block.minecraft.potted_white_tulip": "Balta tulpe puķupodā", "block.minecraft.potted_wither_rose": "Wither roze puķupodā", "block.minecraft.powder_snow": "Drupens sniegs", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON> s<PERSON>ga katls", "block.minecraft.powered_rail": "Elektrisk<PERSON><PERSON> s<PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prizmarīna ķieģeļu plāksne", "block.minecraft.prismarine_brick_stairs": "Prizmarīna kieģeļu kāpnes", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> ķieģeļi", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "Ķirbis", "block.minecraft.pumpkin_stem": "Ķirbja stumbrs", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>s", "block.minecraft.purple_bed": "<PERSON><PERSON> g<PERSON>a", "block.minecraft.purple_candle": "<PERSON><PERSON> s<PERSON>ce", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> ar <PERSON>u sveci", "block.minecraft.purple_carpet": "<PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON> betons", "block.minecraft.purple_concrete_powder": "<PERSON>s betona pulveris", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> glaz<PERSON>ta te<PERSON>ota", "block.minecraft.purple_shulker_box": "<PERSON><PERSON> s<PERSON> kaste", "block.minecraft.purple_stained_glass": "<PERSON><PERSON> kr<PERSON><PERSON> stikls", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON><PERSON> stikla plā<PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON>", "block.minecraft.purple_wool": "Violeta vilna", "block.minecraft.purpur_block": "Purpur bloks", "block.minecraft.purpur_pillar": "Purpur pīlārs", "block.minecraft.purpur_slab": "Purpur plāksne", "block.minecraft.purpur_stairs": "Purpur kāpnes", "block.minecraft.quartz_block": "Kvarca bloks", "block.minecraft.quartz_bricks": "Kvarca Ķieģeļi", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.rail": "Sliedes", "block.minecraft.raw_copper_block": "Nesptrā<PERSON><PERSON><PERSON> vara bloks", "block.minecraft.raw_gold_block": "Neaps<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>ta bloks", "block.minecraft.raw_iron_block": "<PERSON><PERSON>ps<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> bloks", "block.minecraft.red_banner": "Sarkans karogs", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.red_candle": "Sarkana svece", "block.minecraft.red_candle_cake": "<PERSON><PERSON> ar sarkanu sveci", "block.minecraft.red_carpet": "Sarkans p<PERSON>s", "block.minecraft.red_concrete": "Sarkans betons", "block.minecraft.red_concrete_powder": "Sarkans betona pulveris", "block.minecraft.red_glazed_terracotta": "Sarkana glaz<PERSON>ta te<PERSON>ota", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "Sarkano s<PERSON> bloks", "block.minecraft.red_nether_brick_slab": "Sarkano Nether ķieģeļu plāksne", "block.minecraft.red_nether_brick_stairs": "Sarkano Nether ķieģeļu kāpnes", "block.minecraft.red_nether_brick_wall": "Sarkano Nether ķieģeļu mūris", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON> ķieģeļi", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>lt<PERSON>", "block.minecraft.red_sandstone": "Sarka<PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Sarkanā s<PERSON> p<PERSON>", "block.minecraft.red_sandstone_stairs": "Sarka<PERSON><PERSON> s<PERSON> k<PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> mūris", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON> shulker kaste", "block.minecraft.red_stained_glass": "Sarkans krāsots stikls", "block.minecraft.red_stained_glass_pane": "Sarkana kr<PERSON><PERSON>ta stikla plā<PERSON>ne", "block.minecraft.red_terracotta": "Sarkana terakota", "block.minecraft.red_tulip": "<PERSON>rkana tulpe", "block.minecraft.red_wool": "Sarkana vilna", "block.minecraft.redstone_block": "Redstone bloks", "block.minecraft.redstone_lamp": "Redstone lampa", "block.minecraft.redstone_ore": "Redstone rūda", "block.minecraft.redstone_torch": "Redstone lāpa", "block.minecraft.redstone_wall_torch": "Redstone sienas lāpa", "block.minecraft.redstone_wire": "Redstone vads", "block.minecraft.reinforced_deepslate": "Pastiprināts dziļslneklis", "block.minecraft.repeater": "Redstone atkārtotājs", "block.minecraft.repeating_command_block": "Atkārt<PERSON><PERSON><PERSON><PERSON> komandu bloks", "block.minecraft.resin_block": "Sveķu bloks", "block.minecraft.resin_brick_slab": "Sveķu ķieģeļu plāksne", "block.minecraft.resin_brick_stairs": "Sveķu ķieģeļu mūris", "block.minecraft.resin_brick_wall": "Sveķu ķieģeļu mūris", "block.minecraft.resin_bricks": "Sveķu ķieģeļi", "block.minecraft.resin_clump": "Sveķu pika", "block.minecraft.respawn_anchor": "Atdzimšanas enkurs", "block.minecraft.rooted_dirt": "Sakņota zeme", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone": "Smilšakmens", "block.minecraft.sandstone_slab": "Smilšak<PERSON><PERSON> p<PERSON>", "block.minecraft.sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.scaffolding": "Sastatnes", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk katalizators", "block.minecraft.sculk_sensor": "Sculk sensors", "block.minecraft.sculk_shrieker": "Sculk kliedzējs", "block.minecraft.sculk_vein": "Sculk vēna", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON> gurķis", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Atdzimšanas vieta iestatīta", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON> sausā z<PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON>", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON> kaste", "block.minecraft.skeleton_skull": "Skeleta gavlaskauss", "block.minecraft.skeleton_wall_skull": "Skeleta galvaskauss uz sienas", "block.minecraft.slime_block": "Gļotu bloks", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON><PERSON> ameti<PERSON> k<PERSON>", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "Ka<PERSON>ēja galds", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON> baz<PERSON>ts", "block.minecraft.smooth_quartz": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> bloks", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON><PERSON> sarka<PERSON> s<PERSON> pl<PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON><PERSON> s<PERSON> k<PERSON>", "block.minecraft.smooth_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ola", "block.minecraft.snow": "Sniegs", "block.minecraft.snow_block": "Sniega bloks", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.spawn.not_valid": "Jums nav māju gultas vai uzlādēta atdzi<PERSON> en<PERSON>, vai tas tika izn<PERSON>ts", "block.minecraft.spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Mijiedarbojoties ar rad<PERSON> olu:", "block.minecraft.spawner.desc2": "Uzst<PERSON><PERSON> radī<PERSON> tipu", "block.minecraft.sponge": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON> zieds", "block.minecraft.spruce_button": "<PERSON><PERSON> poga", "block.minecraft.spruce_door": "<PERSON><PERSON> durvis", "block.minecraft.spruce_fence": "<PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "Egles žoga vārti", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON> zīme", "block.minecraft.spruce_leaves": "<PERSON><PERSON> lapas", "block.minecraft.spruce_log": "Egles baļķis", "block.minecraft.spruce_planks": "<PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "<PERSON>gles spiediena pl<PERSON>ne", "block.minecraft.spruce_sapling": "<PERSON><PERSON> st<PERSON>", "block.minecraft.spruce_sign": "<PERSON><PERSON>", "block.minecraft.spruce_slab": "<PERSON><PERSON>", "block.minecraft.spruce_stairs": "<PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "<PERSON><PERSON> l<PERSON>", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> egles sienas z<PERSON>me", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON> sienas z<PERSON>me", "block.minecraft.spruce_wood": "<PERSON><PERSON> koks", "block.minecraft.sticky_piston": "Lipīgs virzulis", "block.minecraft.stone": "Akmens", "block.minecraft.stone_brick_slab": "Akmens ķieģeļu plāksne", "block.minecraft.stone_brick_stairs": "Akmens ķieģeļu kāpnes", "block.minecraft.stone_brick_wall": "Akmens ķieģeļu mūris", "block.minecraft.stone_bricks": "Akmens ķieģeļi", "block.minecraft.stone_button": "Akmens poga", "block.minecraft.stone_pressure_plate": "Akmens spiediena p<PERSON>ne", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Nomizots akācijas baļķis", "block.minecraft.stripped_acacia_wood": "Nomizots akācijas koks", "block.minecraft.stripped_bamboo_block": "Nomizots bambusa bloks", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON><PERSON> bērza baļķis", "block.minecraft.stripped_birch_wood": "Nomizots bērza koks", "block.minecraft.stripped_cherry_log": "Nomizots ķirša baļķis", "block.minecraft.stripped_cherry_wood": "Nomizots ķirša koks", "block.minecraft.stripped_crimson_hyphae": "Nomizotas tumšsarkanas hifas", "block.minecraft.stripped_crimson_stem": "Nomizots tumšsarkans k<PERSON>ts", "block.minecraft.stripped_dark_oak_log": "Nomizots tumšā ozola baļķis", "block.minecraft.stripped_dark_oak_wood": "Nomizots tumšais ozols", "block.minecraft.stripped_jungle_log": "Nomizots džungļu koka baļķis", "block.minecraft.stripped_jungle_wood": "Nomizots džungļu koks", "block.minecraft.stripped_mangrove_log": "Nomizots mangroves baļķis", "block.minecraft.stripped_mangrove_wood": "Nomizots mangroves koks", "block.minecraft.stripped_oak_log": "Nomizots ozola baļķis", "block.minecraft.stripped_oak_wood": "Nomizots ozols", "block.minecraft.stripped_pale_oak_log": "No<PERSON><PERSON>ts bālā ozola baļķis", "block.minecraft.stripped_pale_oak_wood": "Nomizots bālais ozols", "block.minecraft.stripped_spruce_log": "Nomi<PERSON><PERSON> egles baļķis", "block.minecraft.stripped_spruce_wood": "Nomizots egles koks", "block.minecraft.stripped_warped_hyphae": "Nomizotas izlocītas hifas", "block.minecraft.stripped_warped_stem": "Nomizots Izlocīts kāts", "block.minecraft.structure_block": "Struktūras bloks", "block.minecraft.structure_void": "Struktūras tukš<PERSON>", "block.minecraft.sugar_cane": "C<PERSON>rnied<PERSON>", "block.minecraft.sunflower": "Saulespuķe", "block.minecraft.suspicious_gravel": "Aizdomīga grants", "block.minecraft.suspicious_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON> smiltis", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON> ogu krūms", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON> sa<PERSON>", "block.minecraft.tall_grass": "<PERSON><PERSON>", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.target": "Mērķis", "block.minecraft.terracotta": "Terakota", "block.minecraft.test_block": "Testa Bloks", "block.minecraft.test_instance_block": "Testa instances bloks", "block.minecraft.tinted_glass": "Tonēts stikls", "block.minecraft.tnt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tnt.disabled": "<PERSON><PERSON><PERSON><PERSON> sprād<PERSON>ni ir iz<PERSON>lēgti", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON> zieda augs", "block.minecraft.trapped_chest": "<PERSON><PERSON>", "block.minecraft.trial_spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire": "<PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON> āķis", "block.minecraft.tube_coral": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.tube_coral_block": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> bloks", "block.minecraft.tube_coral_fan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ā<PERSON>", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> si<PERSON> v<PERSON>", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tufa ķieģeļu plāksne", "block.minecraft.tuff_brick_stairs": "Tufa ķieģeļu kāpnes", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> ķieģeļu siena", "block.minecraft.tuff_bricks": "Tufa ķieģeļi", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON><PERSON> siena", "block.minecraft.turtle_egg": "Bruņuru<PERSON>ča ola", "block.minecraft.twisting_vines": "Sagrozītais vīteņaugs", "block.minecraft.twisting_vines_plant": "Sagrieztais vī<PERSON>ņaugs", "block.minecraft.vault": "Seifs", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON>a", "block.minecraft.vine": "Vīnoglā<PERSON>", "block.minecraft.void_air": "Tukšuma gaiss", "block.minecraft.wall_torch": "<PERSON><PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> poga", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> durvis", "block.minecraft.warped_fence": "Izlocīts žogs", "block.minecraft.warped_fence_gate": "Izlocīta žoga vārti", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hyphae": "Izlocītas hifas", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON><PERSON>ts nīlijs", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON> spiediena plā<PERSON>", "block.minecraft.warped_roots": "Izlocītas saknes", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.warped_stairs": "Izlocītas kā<PERSON>nes", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON> kāts", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> siena<PERSON> z<PERSON>me", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> siena<PERSON> z<PERSON>", "block.minecraft.warped_wart_block": "Izlocīts kārpu bloks", "block.minecraft.water": "Ūdens", "block.minecraft.water_cauldron": "Ūdens katls", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> kalt<PERSON> var<PERSON>", "block.minecraft.waxed_copper_block": "Vaskots vara bloks", "block.minecraft.waxed_copper_bulb": "Vaskota vara spuldze", "block.minecraft.waxed_copper_door": "Vaskota vara durvis", "block.minecraft.waxed_copper_grate": "V<PERSON><PERSON><PERSON> vara režģis", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> vara lamatas", "block.minecraft.waxed_cut_copper": "Vaskots šķelts varš", "block.minecraft.waxed_cut_copper_slab": "Vaskota šķelta vara plāksne", "block.minecraft.waxed_cut_copper_stairs": "Vaskoti šķelti vara pakāpieni", "block.minecraft.waxed_exposed_chiseled_copper": "Vaskotais atsegtais kaltētais varš", "block.minecraft.waxed_exposed_copper": "Vaskots atsegts varš", "block.minecraft.waxed_exposed_copper_bulb": "Vaskota atklāta vara spuldze", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> vara durvis", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON> atklātās vara režģa restes", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON> vara s<PERSON>", "block.minecraft.waxed_exposed_cut_copper": "Vaskots šķelts apstrādāts varš", "block.minecraft.waxed_exposed_cut_copper_slab": "Vaskota atsegta šķelta vara plāksne", "block.minecraft.waxed_exposed_cut_copper_stairs": "Vaskoti atsegta šķelta vara pakāpieni", "block.minecraft.waxed_oxidized_chiseled_copper": "Vaskots oksidēts slīpēts varš", "block.minecraft.waxed_oxidized_copper": "Vaskots oksidēts varš", "block.minecraft.waxed_oxidized_copper_bulb": "Vaskota oksidēta vara spuldze", "block.minecraft.waxed_oxidized_copper_door": "Vaskotas ok<PERSON>dē<PERSON> vara durvis", "block.minecraft.waxed_oxidized_copper_grate": "Vaskots oksidēts slīpēta vara režģis", "block.minecraft.waxed_oxidized_copper_trapdoor": "Vaskota oksidēta slīpēta vara lūka", "block.minecraft.waxed_oxidized_cut_copper": "Vaskots oksidēts šķelts varš", "block.minecraft.waxed_oxidized_cut_copper_slab": "Vaskota oksidēta šķelta vara plāksne", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Vaskoti oksidēta šķelta vara pakāpieni", "block.minecraft.waxed_weathered_chiseled_copper": "Vaskots apsūbējis <PERSON>", "block.minecraft.waxed_weathered_copper": "Vaskots a<PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "Vaskota apsūbējusi vara spuldze", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> vara durvis", "block.minecraft.waxed_weathered_copper_grate": "Vaskots apsūbējis vara režģis", "block.minecraft.waxed_weathered_copper_trapdoor": "Vaskota apsūbējējusi vara lūka", "block.minecraft.waxed_weathered_cut_copper": "Vaskots apsūbējis šķelts varš", "block.minecraft.waxed_weathered_cut_copper_slab": "Vaskota apsūbējuša šķelta vara plāksne", "block.minecraft.waxed_weathered_cut_copper_stairs": "Vaskoti apsūbējuša šķelta vara pakāpieni", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "Apsū<PERSON><PERSON><PERSON><PERSON> vara spuldze", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vara durvis", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vara režģis", "block.minecraft.weathered_copper_trapdoor": "Apsūbējusi vara lūka", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šķelts varš", "block.minecraft.weathered_cut_copper_slab": "Apsūbējusi šķelta vara plāksne", "block.minecraft.weathered_cut_copper_stairs": "Apsūbējuši šķelti vara pakāpieni", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.wheat": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_banner": "Balts karogs", "block.minecraft.white_bed": "Balta gulta", "block.minecraft.white_candle": "Balta svece", "block.minecraft.white_candle_cake": "<PERSON><PERSON> ar baltu sveci", "block.minecraft.white_carpet": "Balts paklājs", "block.minecraft.white_concrete": "Balts betons", "block.minecraft.white_concrete_powder": "Balts betona pulveris", "block.minecraft.white_glazed_terracotta": "Balta glazēta terakota", "block.minecraft.white_shulker_box": "Balta shulker kaste", "block.minecraft.white_stained_glass": "Balts krāsots stikls", "block.minecraft.white_stained_glass_pane": "Balta krāsota stikla plāksne", "block.minecraft.white_terracotta": "Balta terakota", "block.minecraft.white_tulip": "Balta tulpe", "block.minecraft.white_wool": "Balta vilna", "block.minecraft.wildflowers": "<PERSON>v<PERSON><PERSON><PERSON> puķes", "block.minecraft.wither_rose": "Wither roze", "block.minecraft.wither_skeleton_skull": "<PERSON>er skel<PERSON>", "block.minecraft.wither_skeleton_wall_skull": "Wither skeleta galvaskauss uz sienas", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON><PERSON> gulta", "block.minecraft.yellow_candle": "Dzeltena svece", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> ar d<PERSON>tenu sveci", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON> betons", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON> betona pulveris", "block.minecraft.yellow_glazed_terracotta": "<PERSON>zeltena glazēta terakota", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON><PERSON> shulker kaste", "block.minecraft.yellow_stained_glass": "Dzeltens krāsots stikls", "block.minecraft.yellow_stained_glass_pane": "Dzeltena krāsota stikla plāksne", "block.minecraft.yellow_terracotta": "<PERSON>zeltena terakota", "block.minecraft.yellow_wool": "Dzeltena vilna", "block.minecraft.zombie_head": "Zombija galva", "block.minecraft.zombie_wall_head": "Zombija galva uz sienas", "book.byAuthor": "Autors: %1$s", "book.edit.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> rediģēšanas ekrāns", "book.editTitle": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON><PERSON>:", "book.finalizeButton": "Parakstīt un aizvērt", "book.finalizeWarning": "Piezīme! Kad grāmata tiks parakstīta, to vairs nevarēs rediģēt.", "book.generation.0": "Oriģināls", "book.generation.1": "Oriģināla kopija", "book.generation.2": "Kopijas kopija", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* nederīgs grāmatas tags *", "book.pageIndicator": "%1$s lapa no %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapa", "book.page_button.previous": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "book.sign.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.sign.titlebox": "Nosa<PERSON>ms", "book.signButton": "Parakstīt", "book.view.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> skata ekr<PERSON>s", "build.tooHigh": "Būvēšanas augstuma limits ir %s", "chat.cannotSend": "Nav iespējams nosūtīt čata ziņu", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Noklikšķiniet, lai teleportētos", "chat.copy": "Kopēt starpliktuvē", "chat.copy.click": "Klikšķini, lai kopētu starpliktuvē", "chat.deleted_marker": "Š<PERSON> t<PERSON>rz<PERSON> ziņu izdzēsa server<PERSON>.", "chat.disabled.chain_broken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atslēgta, salauz<PERSON> ķēdes dēļ. <PERSON><PERSON> mēģiniet pieslēgties atkārtoti.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, profila publis<PERSON> atslēgas derīguma termiņa be<PERSON> dēļ<PERSON>, mēģiniet pieslēgties atkārtoti.", "chat.disabled.invalid_command_signature": "Komandai bija negaidīti vai iztrūkstoši argumentu paraksti.", "chat.disabled.invalid_signature": "Ziņām bija nepareizs paraksts. Lūdzu mēģini pieslēgties vēlreiz.", "chat.disabled.launcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atslēgta palaišanas programmas uzstādījumos. Nevar nosūtīt ziņojumu.", "chat.disabled.missingProfileKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profila publiskās atslēgas dēļ. Lūd<PERSON> mēģiniet pieslēgties atkārtoti.", "chat.disabled.options": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atslēgta klienta uzstādījumos.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON><PERSON><PERSON> saņemtas nepareizā secībā. Vai mainījā<PERSON> datora pulk<PERSON>?", "chat.disabled.profile": "Konta iestatījumi <PERSON>. Spied '%s' v<PERSON><PERSON><PERSON><PERSON>, lai uzzin<PERSON><PERSON> v<PERSON>.", "chat.disabled.profile.moreInfo": "Konta iestatījumos tērz<PERSON> nav atļauta. Nevar sūtīt vai las<PERSON>t ziņ<PERSON>.", "chat.editBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chat.filtered": "Servera filtēts.", "chat.filtered_full": "Serveris ir paslēpis jūsu ziņ<PERSON><PERSON> da<PERSON> s<PERSON>ēl<PERSON>tā<PERSON>.", "chat.link.confirm": "Vai tiešām vēlaties atvērt sekojošo mā<PERSON>lapu?", "chat.link.confirmTrusted": "Vai vēlaties atvērt šo saiti vai kopēt to uz savas starpliktuves?", "chat.link.open": "<PERSON><PERSON><PERSON><PERSON> pārlūkprogrammā", "chat.link.warning": "Nekad neatveriet saites no cilvēkiem, kuriem neuz<PERSON>!", "chat.queue": "[+%s neap<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(s) līnija(s)]", "chat.square_brackets": "[%s]", "chat.tag.error": "Servers <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "chat.tag.modified": "Serveris modificēja ziņojumu. Oriģināls:", "chat.tag.not_secure": "N<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON>.", "chat.tag.system": "Servera ziņa. <PERSON><PERSON><PERSON>.", "chat.tag.system_single_player": "<PERSON><PERSON> z<PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s paveica izaicinājumu %s", "chat.type.advancement.goal": "%s sasniedza mērķi %s", "chat.type.advancement.task": "%s veica progresu %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON><PERSON><PERSON><PERSON> ziņu komandai", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s saka %s", "chat.validation_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> validācijas kļūda", "chat_screen.message": "<PERSON><PERSON><PERSON><PERSON>, ko nosūtīt: %s", "chat_screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chat_screen.usage": "Ievadiet ziņojumu un nospiediet taustiņu <PERSON>, lai nos<PERSON><PERSON>", "chunk.toast.checkLog": "Plašāku informāciju skatiet žurnālā", "chunk.toast.loadFailure": "Neizdevā<PERSON> i<PERSON> gabalu %s", "chunk.toast.lowDiskSpace": "Maz vietas diskā!", "chunk.toast.lowDiskSpace.description": "Iespējams neizdosies saglabāt pasauli.", "chunk.toast.saveFailure": "Neizdevās saglab<PERSON>t gabalu %s", "clear.failed.multiple": "%s spēlētājiem nav priekšmetu", "clear.failed.single": "Spēlētājam %s nav priekšmetu", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "Zils", "color.minecraft.brown": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.gray": "<PERSON>elē<PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.lime": "Gaiši zaļš", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "Oranž<PERSON>", "color.minecraft.pink": "Roz<PERSON>", "color.minecraft.purple": "<PERSON>s", "color.minecraft.red": "Sarkans", "color.minecraft.white": "Balts", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[ŠEIT]", "command.context.parse_error": "%s pie pozīcijas %s: %s", "command.exception": "Nevar analizēt komandu: %s", "command.expected.separator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atstarpe viena argumenta beigās, bet tā vietā seko dati", "command.failed": "<PERSON><PERSON><PERSON><PERSON>, mēģinot izpildīt šo komandu", "command.forkLimit": "Sasniegts maks<PERSON><PERSON> k<PERSON> s<PERSON> (%s)", "command.unknown.argument": "Nepareizs arguments vai komanda", "command.unknown.command": "Nezināma vai nepilna komanda, kļūdu skatiet zemāk", "commands.advancement.criterionNotFound": "Attīstība %1$s nevar būt kritērijs \"%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Nevar piešķirt kritēriju '%s' sasniegumam %s uz %s sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo viņiem tas jau ir", "commands.advancement.grant.criterion.to.many.success": "Piešķirts kritērijs \"%s\" no progresa %s spēlētājiem %s", "commands.advancement.grant.criterion.to.one.failure": "Nevar piešķirt kritēriju '%s' sasniegumam %s uz %s, jo viņiem tas jau ir", "commands.advancement.grant.criterion.to.one.success": "Piešķirts kritērijs \"%s\" no progresa %s spēlētājam %s", "commands.advancement.grant.many.to.many.failure": "Nevar piešķirt %s sasniegumus %s sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo tie jai ir", "commands.advancement.grant.many.to.many.success": "Piešķirta %s sasniegumi, lai %s dalībniekiem", "commands.advancement.grant.many.to.one.failure": "Nevarēja piešķirt %s sasniegumi, lai %s kā tās jau ir tās", "commands.advancement.grant.many.to.one.success": "Piešķirta %s sasniegumi, lai %s", "commands.advancement.grant.one.to.many.failure": "Nevarēja piešķirt attīstība %s lai %s spē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo tie jau ir", "commands.advancement.grant.one.to.many.success": "Sasniegums %s piešķirts %s spēlētājiem", "commands.advancement.grant.one.to.one.failure": "Nevarēja piešķirt attīstība %s lai %s kā viņi jau ir", "commands.advancement.grant.one.to.one.success": "Piešķirts attīstība %s lai %s", "commands.advancement.revoke.criterion.to.many.failure": "<PERSON>evar atsaukt kritēriju '%s' no sasnieguma %s no %s sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo viņiem tā nav", "commands.advancement.revoke.criterion.to.many.success": "Atsaukts kritērijs '%s' no panākuma %s no %s spē<PERSON><PERSON><PERSON><PERSON>jiem", "commands.advancement.revoke.criterion.to.one.failure": "<PERSON><PERSON>r at<PERSON><PERSON>t kritēriju '%s' no sasnieguma %s no %s, jo viņiem tā nav", "commands.advancement.revoke.criterion.to.one.success": "Atsaukts kritērijs '%s' no panākuma %s no %s", "commands.advancement.revoke.many.to.many.failure": "Nevar atsaukt %s sasniegumus no %s sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo vi<PERSON><PERSON><PERSON> to nav", "commands.advancement.revoke.many.to.many.success": "Atsaukti %s sasniegumi no %s spēlētājiem", "commands.advancement.revoke.many.to.one.failure": "Nevar atsaukt %s progresu no %s, ja viņiem nav to", "commands.advancement.revoke.many.to.one.success": "Atsauca %s progresus no %s", "commands.advancement.revoke.one.to.many.failure": "Nevarēja atsaukt sasniegumu %s no %s spē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo viņiem tā nav", "commands.advancement.revoke.one.to.many.success": "Atsauca progresu %s no %s spēlētājiem", "commands.advancement.revoke.one.to.one.failure": "Nevarēja atsaukt progresu %s no spēlētāja %s, jo viņiem tā nav", "commands.advancement.revoke.one.to.one.success": "Atsauca progresu %s no %s", "commands.attribute.base_value.get.success": "Bāzes vērtība atribūtam %s entītijai %s ir %s", "commands.attribute.base_value.reset.success": "Bāzes vērtība atribūtam %s entītijai %s atstatīts uz noklusējuma %s", "commands.attribute.base_value.set.success": "Bāzes vērtība atribūtam %s entītijai %s ir uzstādīta uz %s", "commands.attribute.failed.entity": "%s nav derīga entītija šai komandai", "commands.attribute.failed.modifier_already_present": "Modifikators %s jau ir uz<PERSON><PERSON><PERSON><PERSON><PERSON> atribūtam %s entītijai %s", "commands.attribute.failed.no_attribute": "Entītijai %s nav atribūta %s", "commands.attribute.failed.no_modifier": "Atribūts %s entītijai %s nav modifikatora %s", "commands.attribute.modifier.add.success": "Pievienots modifikators %s atribūtam %s priekš entītijas %s", "commands.attribute.modifier.remove.success": "Noņemts modifikators %s no atribūta %s priekš entītijas %s", "commands.attribute.modifier.value.get.success": "Modifikatora vērtība %s atribūtam %s priekš entītijas %s ir %s", "commands.attribute.value.get.success": "Atribūta vērtība %s entītijai %s ir %s", "commands.ban.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jau ir aizliegts", "commands.ban.success": "Aizliegts %s: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON><PERSON>. Šī <PERSON> jau ir aizliegta", "commands.banip.info": "Šis aizliegums ietekmē %s spēlētāju(s): %s", "commands.banip.invalid": "Nepareiza IP adrese vai nezināms spēlētājs", "commands.banip.success": "Aizliegta IP %s: %s", "commands.banlist.entry": "%s is aizliedzis %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "commands.banlist.list": "Ir %s aizliegums(i):", "commands.banlist.none": "Nav a<PERSON><PERSON><PERSON><PERSON>", "commands.bossbar.create.failed": "Bosa j<PERSON>la jau eksistē ar ID '%s'", "commands.bossbar.create.success": "Izveidota pielāgota bosa josla %s", "commands.bossbar.get.max": "Pielāgotajai bosa joslai %s pieder maksimums no %s", "commands.bossbar.get.players.none": "Pielāgotai bosa joslai %s pa<PERSON><PERSON><PERSON> tieš<PERSON>stē nepieder spēlētāji", "commands.bossbar.get.players.some": "Pielāgotajai bosa joslai %s ir %s spēlētājs(i) pašreiz tiešsaistē: %s", "commands.bossbar.get.value": "Pielāgotajai bosa joslai %s pieder vērtība %s", "commands.bossbar.get.visible.hidden": "Pielāgota bosa josla %s pašreiz tiek slēpta", "commands.bossbar.get.visible.visible": "Pielāgota bosa josla %s pašreiz tiek rādīta", "commands.bossbar.list.bars.none": "Nav akt<PERSON><PERSON> bosa j<PERSON>las", "commands.bossbar.list.bars.some": "<PERSON>r aktīvas %s pielā<PERSON><PERSON> bosa josla(s): %s", "commands.bossbar.remove.success": "Noņemta pielāgota bosa josla %s", "commands.bossbar.set.color.success": "Pie<PERSON>ā<PERSON>a bosa josla %s ir noma<PERSON><PERSON><PERSON><PERSON> krāsu", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> j<PERSON>la jau ir tādā krāsā", "commands.bossbar.set.max.success": "Pie<PERSON>ā<PERSON>a bosa josla %s ir no<PERSON><PERSON><PERSON><PERSON><PERSON> maks<PERSON><PERSON> v<PERSON>rt<PERSON> uz %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON>. <PERSON>s ir bosa j<PERSON> maks<PERSON>", "commands.bossbar.set.name.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> bosa josla %s ir pārdē<PERSON><PERSON>ta", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> jau ir bosa jos<PERSON> no<PERSON>", "commands.bossbar.set.players.success.none": "Pielāgotai bosa joslai %s nepieder vairs neviens spēlētājs", "commands.bossbar.set.players.success.some": "Pielāgot<PERSON>i bosa joslai %s tagad ir %s spēlētājs(i): %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> jau ir ies<PERSON>t<PERSON>ti bosa joslā bez neviena ko pievienot vai noņemt", "commands.bossbar.set.style.success": "Pie<PERSON>ā<PERSON>a bosa josla %s ir noma<PERSON><PERSON><PERSON><PERSON> stilu", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> jos<PERSON> jau pieder tāds stils", "commands.bossbar.set.value.success": "Pielāgota bosa josla %s ir nomainījusi vērtību uz %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> j<PERSON> jau ir šāda vērtība", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> j<PERSON> jau ir slēpta", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> j<PERSON> jau ir <PERSON>a", "commands.bossbar.set.visible.success.hidden": "Pie<PERSON>ā<PERSON><PERSON> bosa josla %s tagad ir slēpta", "commands.bossbar.set.visible.success.visible": "Pielāgotā bosa josla %s tagad ir redzama", "commands.bossbar.unknown": "Neeksistē bosa josla ar ID '%s'", "commands.clear.success.multiple": "Noņemts(i) %s priekšmets(i) %s splēlētājiem", "commands.clear.success.single": "Noņemts(i) %s priekšmets(i) spēlētājam %s", "commands.clear.test.multiple": "Atrasti %s atbilstošs(i) priekšmets(i) %s splēlētājiem", "commands.clear.test.single": "Atrasti %s atbilstošs(i) priekšmets(i) splēlētājam %s", "commands.clone.failed": "Neviens bloks netika klonēts", "commands.clone.overlap": "Avota un mērķa apkārt<PERSON> nevar pā<PERSON>ties", "commands.clone.success": "Veiksmīgi klonēts(i) %s bloks(i)", "commands.clone.toobig": "Pārāk daudz bloku norād<PERSON><PERSON><PERSON><PERSON> apgabal<PERSON> (maksimums %s, norādīti %s)", "commands.damage.invulnerable": "Mērķis ir neievainojams pret doto kaitējuma veidu", "commands.damage.success": "Nodarīts %s kaitējums uz %s", "commands.data.block.get": "%s uz bloka %s, %s, %s pēc %s skalas faktora ir %s", "commands.data.block.invalid": "Mērķa bloks nav bloka entītija", "commands.data.block.modified": "Modificēti %s, %s, %s, bloku dati", "commands.data.block.query": "%s, %s, %s ir seko<PERSON><PERSON>i bloka dati: %s", "commands.data.entity.get": "%s uz %s pēc %s skalas faktora ir %s", "commands.data.entity.invalid": "<PERSON>evar modific<PERSON>t spēlēt<PERSON><PERSON> datus", "commands.data.entity.modified": "Modificēti %s esamības dati", "commands.data.entity.query": "%s ir <PERSON><PERSON><PERSON> es<PERSON> dati: %s", "commands.data.get.invalid": "<PERSON>evar i<PERSON> %s; atļauti tikai ciparu tagi", "commands.data.get.multiple": "Šis arguments pieņem vienu NBT vērtību", "commands.data.get.unknown": "Nevar ieg<PERSON>t %s; tags neeksistē", "commands.data.merge.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rekvizītiem jau ir <PERSON><PERSON> vērt<PERSON>bas", "commands.data.modify.expected_list": "Nepieciešams saraksts, bet tā vietā ir: %s", "commands.data.modify.expected_object": "Nepieciešams objekts, bet tā vietā ir: %s", "commands.data.modify.expected_value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, bet tā vietā ir: %s", "commands.data.modify.invalid_index": "Nederīgs saraksta indeks: %s", "commands.data.modify.invalid_substring": "Nederīgi apakšvirknes indeksi: %s līdz %s", "commands.data.storage.get": "%s ir glabātuvē %s pēc skalas faktora no %s ir %s", "commands.data.storage.modified": "Pārveidota glabātuve %s", "commands.data.storage.query": "Glabātuve %s satur šādas lietas: %s", "commands.datapack.create.already_exists": "<PERSON><PERSON> ar no<PERSON><PERSON><PERSON> '%s' jau pastāv", "commands.datapack.create.invalid_full_name": "Nederīgs jaunās pakotnes nosaukums '%s'", "commands.datapack.create.invalid_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> pakotnes nosaukumā '%s' ir neder<PERSON>gas r<PERSON>tz<PERSON>mes", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "Paka '%s' nav iespējota!", "commands.datapack.disable.failed.feature": "Paku '%s' ne<PERSON>, jo tā ir daļa no iespējota iestatījuma!", "commands.datapack.enable.failed": "<PERSON><PERSON> '%s' jau ir ies<PERSON><PERSON>!", "commands.datapack.enable.failed.no_flags": "Paku '%s' nevar i<PERSON>, tā kā nepieciešamie iestatījumi nav iespējoti šajā pasaulē: %s!", "commands.datapack.list.available.none": "Vairs nav pieejamas datu a<PERSON>", "commands.datapack.list.available.success": "<PERSON>r pieejamas %s datu paka(s): %s", "commands.datapack.list.enabled.none": "Nav iespējotas datu pakotnes", "commands.datapack.list.enabled.success": "<PERSON>r i<PERSON><PERSON><PERSON><PERSON><PERSON> %s datu paka(s): %s", "commands.datapack.modify.disable": "Atspējota datu pakotne %s", "commands.datapack.modify.enable": "Iespējota datu pakotne %s", "commands.datapack.unknown": "Nezināma datu paka '%s'", "commands.debug.alreadyRunning": "Tikšķu profilē<PERSON>na jau ir uzsākta", "commands.debug.function.noRecursion": "Nevar izsekot no funkcijas iekšpuses", "commands.debug.function.noReturnRun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nevar i<PERSON>t kopā ar /return run", "commands.debug.function.success.multiple": "Izzsekota(s) %s komanda(s) %s funkcijā izvades failā %s", "commands.debug.function.success.single": "Izzsekota(s) %s komanda(s) funkcijai '%s' izvades failā %s", "commands.debug.function.traceFailed": "Neizdevās izsekot funkciju", "commands.debug.notRunning": "Tikšķu profilēšana nav sākusies", "commands.debug.started": "Uzsākta tikšķu profilēšana", "commands.debug.stopped": "Tikšķu profilēšana tika apstādināta pēc %s sekundēm un %s tikšķiem (%s tikšķi sekundē)", "commands.defaultgamemode.success": "Noklusējuma spēles režīms tagad ir %s", "commands.deop.failed": "<PERSON><PERSON><PERSON><PERSON>. Spēlētājs nav operators", "commands.deop.success": "%s vairs nav servera operators", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Sarežģītība neizmainījās; tā jau ir uzstādīta uz %s", "commands.difficulty.query": "Sarežģītība ir %s", "commands.difficulty.success": "Sarežģītība ir uzstād<PERSON>ta uz %s", "commands.drop.no_held_items": "<PERSON>t<PERSON><PERSON><PERSON> nevar turēt nekā<PERSON> p<PERSON>", "commands.drop.no_loot_table": "Vienībai %s nav salaupīšanas galda", "commands.drop.no_loot_table.block": "Blokā %s nav izlaupīšanas tabulas", "commands.drop.success.multiple": "Nometa %s lietas", "commands.drop.success.multiple_with_table": "Nomesti %s priekšmeti no apbalvošanas saraksta %s", "commands.drop.success.single": "Nometa %s %s", "commands.drop.success.single_with_table": "Nomesti %s %s no materiālu galda %s", "commands.effect.clear.everything.failed": "Mērķim nav noņemamu efektu", "commands.effect.clear.everything.success.multiple": "Noņemti visi efekti no %s mērķiem", "commands.effect.clear.everything.success.single": "Noņemti visi efekti no %s", "commands.effect.clear.specific.failed": "Mērķim nav pieprasītā efekta", "commands.effect.clear.specific.success.multiple": "Noņemts %s efekts no %s mērķiem", "commands.effect.clear.specific.success.single": "Noņemts %s efekts no %s", "commands.effect.give.failed": "Nevar pielietot šo efektu (mērķis ir vai nu imūns pret efektiem vai ir kaut kas stiprāks)", "commands.effect.give.success.multiple": "Pielietots efekts %s uz %s mērķiem", "commands.effect.give.success.single": "Pielietots efekts %s %s", "commands.enchant.failed": "<PERSON><PERSON><PERSON>. Mērķiem vai nu rokās nav neviena priekšmeta vai uzlabojums nevar tikt pielietots", "commands.enchant.failed.entity": "%s nav derīga entītija šai komandai", "commands.enchant.failed.incompatible": "%s nevar <PERSON> to uzlabojumu", "commands.enchant.failed.itemless": "%s netur nek<PERSON><PERSON> priekšmetu", "commands.enchant.failed.level": "%s ir augstāks par maksim<PERSON><PERSON> līmeni %s, ko atbalsta uzlabojums", "commands.enchant.success.multiple": "Pielietots %s uzlabojums uz %s objektiem", "commands.enchant.success.single": "Pielietots %s uzlabojums %s priekšmetam", "commands.execute.blocks.toobig": "Pārāk daudz bloku norād<PERSON><PERSON><PERSON><PERSON> apgabal<PERSON> (maksimums %s, norādīti %s)", "commands.execute.conditional.fail": "Tests neizdevās", "commands.execute.conditional.fail_count": "Tests neizdevās, skaits: %s", "commands.execute.conditional.pass": "Tests pagājis", "commands.execute.conditional.pass_count": "Tests pagājis, skaits: %s", "commands.execute.function.instantiationFailure": "Neizdevās instancēt funkciju %s: %s", "commands.experience.add.levels.success.multiple": "Iedoti %s pieredzes līmeņi %s spēlētājiem", "commands.experience.add.levels.success.single": "Doti %s pieredzes līmeņi priekš %s", "commands.experience.add.points.success.multiple": "Ieoti %s pieredzes punkti %s spēlētājiem", "commands.experience.add.points.success.single": "Doti %s pieredzes punkti priekš %s", "commands.experience.query.levels": "%s ir %s piered<PERSON> lī<PERSON>i", "commands.experience.query.points": "%s ir %s pieredzes punkti", "commands.experience.set.levels.success.multiple": "Uzstādīti %s pieredzes līmeņi uz %s spēl<PERSON>tājiem", "commands.experience.set.levels.success.single": "Uzstādīti %s pieredzes līmeņi uz %s", "commands.experience.set.points.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pieredzes punktus kā spēlētja pašreizējā līmeņa maksim<PERSON><PERSON> punktu skaitu", "commands.experience.set.points.success.multiple": "Uzstādīti %s pieredzes punkti uz %s spēlētājiem", "commands.experience.set.points.success.single": "Uzstādīti %s pieredzes punkti uz %s", "commands.fill.failed": "Netika piepildīts neviens bloks", "commands.fill.success": "Veiksmīgi a<PERSON>īts(i) %s bloks(i)", "commands.fill.toobig": "Pārāk daudz bloku norād<PERSON><PERSON><PERSON><PERSON> apgabal<PERSON> (maksimums %s, norādīti %s)", "commands.fillbiome.success": "Biomas iestādītas starp %s, %s, %s un %s, %s, %s", "commands.fillbiome.success.count": "%s biomas ieeraksti iestatīti starp %s, %s, %s un %s, %s, %s", "commands.fillbiome.toobig": "Pārāk daudz bloku norādītajā vietā (maksimums %s, norādīti %s)", "commands.forceload.added.failure": "Neviens gabals netika atzīmēts kā piespiedu ielādēts", "commands.forceload.added.multiple": "%s gabali %s no %s līdz %s atzīmēti kā piespiedu ielādējami", "commands.forceload.added.none": "%s netika atrasti piesp<PERSON>u i<PERSON><PERSON><PERSON><PERSON><PERSON> gabali", "commands.forceload.added.single": "Gabals %s %s atzīmēts kā piespiedu ielādējams", "commands.forceload.list.multiple": "%s piespiedu ielā<PERSON><PERSON><PERSON><PERSON> gabali tika atrasti šeit: %s %s", "commands.forceload.list.single": "Piespiedu ielādējams gabals tika atrasts šeit: %s %s", "commands.forceload.query.failure": "Gabals %s %s nav atzīmēts kā piespiedu ielādējams", "commands.forceload.query.success": "Gabals %s %s atzīmēts kā piespiedu ielādējams", "commands.forceload.removed.all": "Visi gabali %s vairs nav atzīmēti piespiedu ielādē<PERSON>mi", "commands.forceload.removed.failure": "Neviens gabals netika noņemts no piespiedu ielādējamiem", "commands.forceload.removed.multiple": "%s gabali %s no %s līdz %s vairs nav atzīmēti kā piespiedu ielādējami", "commands.forceload.removed.single": "Gabals %s %s vairs nav atzīmēts kā piespiedu ielādējams", "commands.forceload.toobig": "Nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zonā pārāk daudz gabalu (maksimums %s, norādīti %s)", "commands.function.error.argument_not_compound": "Nederīgs argumenta tips: %s, tika sagaidīts Compound", "commands.function.error.missing_argument": "Funkcijai %1$s trūkst arguments %2$s", "commands.function.error.missing_arguments": "Funkcijai %s trūkst argumenti", "commands.function.error.parse": "\"komanda. funkcija. kļūda. parsēt.\"", "commands.function.instantiationFailure": "Neizdevās instancēt funkciju %s: %s", "commands.function.result": "Funkcija %s atgrieza %s", "commands.function.scheduled.multiple": "Izpilda funkcijas %s", "commands.function.scheduled.no_functions": "Neizdevās atrast nevienu funkciju ar nosaukumu %s", "commands.function.scheduled.single": "Izpilda funkciju %s", "commands.function.success.multiple": "Izpildīta(s) %s komanda(s) no %s funkcijām", "commands.function.success.multiple.result": "Izpildītas %s funkcijas", "commands.function.success.single": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(s) %s komanda(s) funkcijā '%s'", "commands.function.success.single.result": "Funkcija '%2$s' atgrieza %1$s", "commands.gamemode.success.other": "Nomainīt %s spēles režīmu uz %s", "commands.gamemode.success.self": "Nomainīt savu spēles režīmu uz %s", "commands.gamerule.query": "Spēles noteikums %s paš<PERSON>iz ir iestatīts uz: %s", "commands.gamerule.set": "<PERSON><PERSON><PERSON><PERSON> noteikums %s tagad ir iestatīts uz: %s", "commands.give.failed.toomanyitems": "<PERSON>evar i<PERSON> vairāk par %s no %s", "commands.give.success.multiple": "Doti %s %s %s spēlētājiem", "commands.give.success.single": "Doti %s %s spēlētājam %s", "commands.help.failed": "Nez<PERSON>ā<PERSON> komanda vai nepietie<PERSON>mas atļaujas", "commands.item.block.set.success": "Aizvietotas vietas %s, %s, %s ar %s", "commands.item.entity.set.success.multiple": "Vieta aizvietota %s entītijām ar %s", "commands.item.entity.set.success.single": "Vieta %s aizvietota ar %s", "commands.item.source.no_such_slot": "Avotam nav vietas %s", "commands.item.source.not_a_container": "Avota pozīcija %s, %s, %s nav konteineris", "commands.item.target.no_changed.known_item": "Neviens mērķis nepieņēma lietu %s vietā %s", "commands.item.target.no_changes": "Neviens mērķis nepieņēma lietu vietā %s", "commands.item.target.no_such_slot": "Mērķim nav vietas %s", "commands.item.target.not_a_container": "Mērķa pozīcija %s, %s, %s nav konteineris", "commands.jfr.dump.failed": "Neizdevās saglabāt JFR ierakstu: %s", "commands.jfr.start.failed": "Neizdevās uzsākt JFR profilēšanu", "commands.jfr.started": "Sākās JFR profilēšana", "commands.jfr.stopped": "JFR profilēšana apturēta un saglabāta %s", "commands.kick.owner.failed": "Nevar izmest servera īpašnieku LAN spēlē", "commands.kick.singleplayer.failed": "Nevar izmest bezsaistes vienspēlētāja režīma spēlē", "commands.kick.success": "Izsperti %s: %s", "commands.kill.success.multiple": "Nogalināti %s objekti", "commands.kill.success.single": "Nogalināja %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Šeit ir %s no maksimālajiem %s spēlētājiem tiešsaistē: %s", "commands.locate.biome.not_found": "Nevarēja atrast biomu %s sasniedzamā attālumā", "commands.locate.biome.success": "Tuvākā bioma %s atrodas %s (%s bloku attālumā)", "commands.locate.poi.not_found": "Neizdevās atrast intereses punktu ar tipu \"%s\", saprā<PERSON><PERSON><PERSON><PERSON> attālumā", "commands.locate.poi.success": "Tuvākais punkts %s atrodas %s (%s bloku attālumā)", "commands.locate.structure.invalid": "Nav struktūras ar tipu \"%s\"", "commands.locate.structure.not_found": "Neizdevās tuvumā atrast struktūru ar tipu \"%s\"", "commands.locate.structure.success": "Tuvākais %s atrodas %s (%s bloku attālumā)", "commands.message.display.incoming": "%s tev pačukstēja: %s", "commands.message.display.outgoing": "Tu %s pačukstēji: %s", "commands.op.failed": "<PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jau ir <PERSON>", "commands.op.success": "%s ir servera operators", "commands.pardon.failed": "<PERSON><PERSON><PERSON><PERSON>. Spēlētājs nav bloķēts", "commands.pardon.success": "Atbloķēts %s", "commands.pardonip.failed": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON>. Šī IP nav aizliegta", "commands.pardonip.invalid": "Nederīga IP adrese", "commands.pardonip.success": "Atbloķēts IP %s", "commands.particle.failed": "Daļiņa nevienam nebija redzama", "commands.particle.success": "<PERSON><PERSON><PERSON> %s", "commands.perf.alreadyRunning": "Veiktspējas profilēšana jau ir sākta", "commands.perf.notRunning": "Veiktspējas profilēšana nav sākta", "commands.perf.reportFailed": "Nevarēja izveidot atkļūdošanas (debug) ziņojumu", "commands.perf.reportSaved": "Izveidots atkļūdošanas (debug) ziņojums %s", "commands.perf.started": "Sākta 10 sekunžu veiktspējas profilēšana (lai apturētu agr<PERSON>, izmantojiet '/perf stop')", "commands.perf.stopped": "Pārtraukta veiktspējas profilēšana pēc %s sekundes(ēm) un %s tikšķa(iem) (%s tikšķis(i) sekundē)", "commands.place.feature.failed": "Neizdevās no<PERSON> iezī<PERSON>", "commands.place.feature.invalid": "Nav iezīmes ar tipu \"%s\"", "commands.place.feature.success": "\"%s\" novietots %s, %s, %s", "commands.place.jigsaw.failed": "Neizdevās uzģenerēt puzles gabalu", "commands.place.jigsaw.invalid": "Veidņu pūls ar tipu \"%s\" neeksistē", "commands.place.jigsaw.success": "Uzģenerēts puzles gabals %s, %s, %s", "commands.place.structure.failed": "Neizdevās novietot struktūru", "commands.place.structure.invalid": "Struktūra ar tipu \"%s\" neeksistē", "commands.place.structure.success": "Struktūra \"%s\" uzģēnerēta %s, %s, %s", "commands.place.template.failed": "Neizdevās novietot veidni", "commands.place.template.invalid": "Veidne ar id \"%s\" neeksistē", "commands.place.template.success": "Veidne \"%s\" ielādēta %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON><PERSON> ir pārāk tālu, lai to sad<PERSON><PERSON><PERSON><PERSON>", "commands.playsound.success.multiple": "Atskaņota skaņa %s priekš %s spēlētājiem", "commands.playsound.success.single": "Atskaņota skaņa %s priekš %s", "commands.publish.alreadyPublished": "Daudzspēlētāju spēlei jau pieder ports %s", "commands.publish.failed": "Neizdevās iz<PERSON>ido<PERSON> lo<PERSON>ālo s<PERSON>", "commands.publish.started": "Lokālā spēlē izmanto portu %s", "commands.publish.success": "Daudzspēlētāju spēlei tagad pieder ports %s", "commands.random.error.range_too_large": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vē<PERSON><PERSON> robeža nedrī<PERSON>t pārsniegt 2147483646", "commands.random.error.range_too_small": "Nejauš<PERSON><PERSON> vērtības robežai jābūt vismaz 2", "commands.random.reset.all.success": "Atiest<PERSON>ī<PERSON>(s) %s ne<PERSON><PERSON><PERSON>(s) v<PERSON><PERSON>(s)", "commands.random.reset.success": "Atiestatīt nejaušo secību %s", "commands.random.roll": "%s uzmeta %s (no %s līdz %s)", "commands.random.sample.success": "Nejaušā vērtība: %s", "commands.recipe.give.failed": "<PERSON>ika a<PERSON>gūtas jaunas receptes", "commands.recipe.give.success.multiple": "Atbloķētas %s receptes %s spēlētājam", "commands.recipe.give.success.single": "Atbloķētas %s receptes spēlētājam %s", "commands.recipe.take.failed": "Neviena recepte nevar tikt aizmirsta", "commands.recipe.take.success.multiple": "Paņemtas %s receptes no %s spēlētājiem", "commands.recipe.take.success.single": "Paņemtas %s receptes no %s", "commands.reload.failure": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sag<PERSON><PERSON><PERSON> vecos datus", "commands.reload.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>!", "commands.ride.already_riding": "%s jau brauc ar %s", "commands.ride.dismount.success": "%s pārtrauca braukt ar %s", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON><PERSON> braukt ar s<PERSON>ē<PERSON><PERSON>", "commands.ride.mount.failure.generic": "%s nevarēja uzsākt braukšanu ar %s", "commands.ride.mount.failure.loop": "Nevar uzstā<PERSON><PERSON>t būtību uz sevi vai kādam no tā pasažieriem", "commands.ride.mount.failure.wrong_dimension": "<PERSON><PERSON><PERSON> vadīt entītiju citā dimensijā", "commands.ride.mount.success": "%s sāka braukt ar %s", "commands.ride.not_riding": "%s nebrauc ar transportlī<PERSON>li", "commands.rotate.success": "Pagriezts %s", "commands.save.alreadyOff": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON> jau ir atslēgta", "commands.save.alreadyOn": "Sagla<PERSON><PERSON><PERSON><PERSON> jau ir ieslēgta", "commands.save.disabled": "Automātiskā saglabā<PERSON>na tagad ir atspējota", "commands.save.enabled": "Automātiskā saglabāšana tagad ir iespējota", "commands.save.failed": "<PERSON><PERSON><PERSON>ē<PERSON> (vai diskā pietiekami daudz vietas?)", "commands.save.saving": "Sagla<PERSON><PERSON> spēli (šis var aizņemt kādu brīdi)", "commands.save.success": "<PERSON><PERSON><PERSON><PERSON>", "commands.schedule.cleared.failure": "Nav grafiki ar id %s", "commands.schedule.cleared.success": "Noņemti %s grafiki(s) ar id %s", "commands.schedule.created.function": "Ieplānota funkcija '%s' pēc %s tikšķa(iem) spēles laikā %s", "commands.schedule.created.tag": "Plānots tags '%s' %s sākas spēles laikā %s", "commands.schedule.macro": "<PERSON><PERSON><PERSON> ma<PERSON>ro", "commands.schedule.same_tick": "<PERSON><PERSON><PERSON> p<PERSON>ot pašreiz<PERSON><PERSON><PERSON>", "commands.scoreboard.objectives.add.duplicate": "Mērķis ar tādu nosaukumu jau eksistē", "commands.scoreboard.objectives.add.success": "Izveidots jauns mērķis %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON><PERSON>. Tas displeja slots jau ir tuk<PERSON>s", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON><PERSON>. Š<PERSON> displeja slots jau rāda šo mērķi", "commands.scoreboard.objectives.display.cleared": "Nodzēsti jebkādi mērķi displeja slotā %s", "commands.scoreboard.objectives.display.set": "Uzstādīt displeja slotu %s, lai rādītu mērķus %s", "commands.scoreboard.objectives.list.empty": "Nav mērķu", "commands.scoreboard.objectives.list.success": "Ir %s mērķi(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Atspējota automātiskā atjaunošana mērķim %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Iespējota automātiskā atjaunošana mērķim %s", "commands.scoreboard.objectives.modify.displayname": "Nosaukums %s nomainīts uz %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Notīrīts noklusētais skaitļu formāts mērķim %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Izmainīts noklusētais skaitļu formāts mērķim %s", "commands.scoreboard.objectives.modify.rendertype": "Nomainīja objekta renderēšanas tipu %s", "commands.scoreboard.objectives.remove.success": "Noņemts mērķis %s", "commands.scoreboard.players.add.success.multiple": "Pievienots %s %s priekš %s objektiem", "commands.scoreboard.players.add.success.single": "Pievienots %s %s priekš %s (pašreiz %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Komandas. rezultātu tablo. spēlētājiem. displejs. nosaukums. skaidrs. panākumus. vairākas", "commands.scoreboard.players.display.name.clear.success.single": "Noņemts nosaukums %s iekš %s", "commands.scoreboard.players.display.name.set.success.multiple": "<PERSON>īts parādāmais nosaukums %s uz %s entītijām šeit: %s", "commands.scoreboard.players.display.name.set.success.single": "<PERSON>īts parādāmais nosaukums uz %s vietnei %s šeit: %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Notīrīts skaitļu formāts %s entītijām %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Notīrīts %s skaitļu formāts %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Mainīts skaitļu formāts %s entītijām šeit: %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Mainīts skaitļu formāts %s šeit: %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON><PERSON>. <PERSON>s i<PERSON> jau ir ies<PERSON>", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strādā tikai uz izraisī<PERSON>-mērķiem", "commands.scoreboard.players.enable.success.multiple": "Iespējots triggers %s priekš %s objektiem", "commands.scoreboard.players.enable.success.single": "Iespējošanu izraisīja %s priekš %s", "commands.scoreboard.players.get.null": "Nevar iegūt %s vērtību %s; nekas nav uzstādīts", "commands.scoreboard.players.get.success": "%s ir %s %s", "commands.scoreboard.players.list.empty": "Nav izsekoti objekti", "commands.scoreboard.players.list.entity.empty": "%s nav punktu ko parādīt", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s ir %s punktu skaits(i):", "commands.scoreboard.players.list.success": "Ir %s izsekota(s) entītija(s): %s", "commands.scoreboard.players.operation.success.multiple": "Atjaunināts %s priekš %s objektiem", "commands.scoreboard.players.operation.success.single": "Iestatīt %s priekš %s uz %s", "commands.scoreboard.players.remove.success.multiple": "Noņemts %s no %s priekš %s objektiem", "commands.scoreboard.players.remove.success.single": "Noņemts %s no %s priekš %s (pašreiz %s)", "commands.scoreboard.players.reset.all.multiple": "Atiestatīt visus punktus priekš %s objektiem", "commands.scoreboard.players.reset.all.single": "Atiestatīt visus punktus priekš %s", "commands.scoreboard.players.reset.specific.multiple": "Atiestatīts %s priekš %s objektiem", "commands.scoreboard.players.reset.specific.single": "Atiestatīt %s priekš %s", "commands.scoreboard.players.set.success.multiple": "Iestatīts %s priekš %s objektiem uz %s", "commands.scoreboard.players.set.success.single": "Iestatīts %s priekš %s uz %s", "commands.seed.success": "Sēkla: %s", "commands.setblock.failed": "Nevar nolikt bloku", "commands.setblock.success": "Mainīts bloks %s %s, %s", "commands.setidletimeout.success": "Spē<PERSON><PERSON><PERSON><PERSON><PERSON> dīkstāves noilgums tagad ir %s minūte(s)", "commands.setidletimeout.success.disabled": "Spēlēt<PERSON><PERSON> dīkstāves taimauts tagad ir atspējots", "commands.setworldspawn.failure.not_overworld": "Atdzimšanas punktu var uzstādīt tikai virszemē", "commands.setworldspawn.success": "Iestatīta pasaules atdzimšanas vieta uz %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Iestatīta dzimšanas vieta uz %s, %s, %s [%s] atrodoties %s priekš %s spēlētājiem", "commands.spawnpoint.success.single": "Iestatīta dzimšanas vieta uz %s, %s, %s [%s] atrodoties %s priekš %s", "commands.spectate.not_spectator": "%s nav skatī<PERSON><PERSON><PERSON> re<PERSON>", "commands.spectate.self": "Nav iespē<PERSON><PERSON> vērot sevi", "commands.spectate.success.started": "Tagad tiek skatīts %s", "commands.spectate.success.stopped": "Vairs netiek skatīta <PERSON> b<PERSON>ne", "commands.spreadplayers.failed.entities": "Neizdevās izkliedēt %s entītiju/entītijas ap %s, %s (pārāk daudz entītiju vietai - mēģiniet izmantot ne vairāk kā %s izplatību)", "commands.spreadplayers.failed.invalid.height": "Nederīgs maxHeight %s; sagaidīts augstāks kā pasaules minimums %s", "commands.spreadplayers.failed.teams": "Neizdevās izkliedēt %s komandu(as) ap %s, %s (pārāk daudz entītiju vietai - mēģiniet izmantot ne vairāk kā %s izplatību)", "commands.spreadplayers.success.entities": "Izkliedēt %s spēlētāju(s) ap %s, %s ar vidējo attālumu %s bloki viens no otra", "commands.spreadplayers.success.teams": "Izkliedēt %s komandu(as) ap %s, %s ar vidējo attālumu %s bloki viens no otra", "commands.stop.stopping": "Apstādina serveri", "commands.stopsound.success.source.any": "Apturētas visas '%s' skaņas", "commands.stopsound.success.source.sound": "Apturēta ska<PERSON>a '%s' avotā '%s'", "commands.stopsound.success.sourceless.any": "Apturētas visas ska<PERSON>as", "commands.stopsound.success.sourceless.sound": "Aptur<PERSON><PERSON> ska<PERSON>a '%s'", "commands.summon.failed": "Nevar izsaukt objektu", "commands.summon.failed.uuid": "Nevar iz<PERSON>ukt būtni identisku UUIDu dēļ", "commands.summon.invalidPosition": "Nederīga pozīcija izsaukšanai", "commands.summon.success": "Radīts jauns %s", "commands.tag.add.failed": "Mērķim jau ir tags vai ir pārāk daudz tagi", "commands.tag.add.success.multiple": "Pievienota birka '%s' %s objektiem", "commands.tag.add.success.single": "Pievienota birka '%s' %s", "commands.tag.list.multiple.empty": "%s objektiem nav birku", "commands.tag.list.multiple.success": "%s objektiem ir %s kopējas birkas: %s", "commands.tag.list.single.empty": "%s nav birku", "commands.tag.list.single.success": "%s ir %s birkas: %s", "commands.tag.remove.failed": "Mērķin nav šis tags", "commands.tag.remove.success.multiple": "Noņemta birka '%s' no %s objektiem", "commands.tag.remove.success.single": "Noņemta birka '%s' no %s", "commands.team.add.duplicate": "<PERSON><PERSON><PERSON> ar tādu no<PERSON><PERSON>mu jau e<PERSON>", "commands.team.add.success": "Izveidota komanda %s", "commands.team.empty.success": "Noņemts(i) %s biedrs(i) no komandas %s", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> jau ir tukša", "commands.team.join.success.multiple": "Pievienoti %s dalībnieki komandai %s", "commands.team.join.success.single": "Pievienots %s komandai %s", "commands.team.leave.success.multiple": "Noņemti %s dalībnieki no katras komandas", "commands.team.leave.success.single": "Noņemts %s no katras komandas", "commands.team.list.members.empty": "%s komandā nav dalībnieku", "commands.team.list.members.success": "Komandai %s ir %s biedrs(i): %s", "commands.team.list.teams.empty": "Nav komandas", "commands.team.list.teams.success": "Ir %s komanda(s): %s", "commands.team.option.collisionRule.success": "Nesaskaņas noteikums komandai %s tagad ir \"%s\"", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON><PERSON>. Sadar<PERSON><PERSON><PERSON><PERSON><PERSON> noteik<PERSON> jau ir uzstādīts uz to vērtību", "commands.team.option.color.success": "Atjaunota komandas krāsa %s uz %s", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON>. Komandai jau pieder tā krāsa", "commands.team.option.deathMessageVisibility.success": "Nāves ziņas redzamība komandai %s tagad ir \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> zi<PERSON> redzamība jau ir uzstād<PERSON>ta uz to vērtību", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON>. Draudzīgs uguns tai komandai jau ir atspējots", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON><PERSON><PERSON>. Draudzīgs uguns tai komandai jau ir iespējots", "commands.team.option.friendlyfire.disabled": "Atspējots draudzīgs uguns komandai %s", "commands.team.option.friendlyfire.enabled": "Iespējots draudzīgs uguns komandai %s", "commands.team.option.name.success": "Atjaunināja komandas vārdu %s", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON>. Komandai jau pieder tas nosaukums", "commands.team.option.nametagVisibility.success": "<PERSON><PERSON><PERSON> lapas redzamība komandai %s tagad ir \"%s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> birkas red<PERSON>ba jau ir uzstā<PERSON><PERSON>ta uz to vērtību", "commands.team.option.prefix.success": "Komandas prefiks iestatīts uz %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON>. <PERSON><PERSON><PERSON> jau nevar <PERSON>t nered<PERSON>us komandas biedrus", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> Komanda j<PERSON> var redz<PERSON>t nered<PERSON>us komandas biedrus", "commands.team.option.seeFriendlyInvisibles.disabled": "Komanda %s tagad vairs nevar <PERSON>z<PERSON>t ne<PERSON><PERSON>", "commands.team.option.seeFriendlyInvisibles.enabled": "Komanda %s tagad var redzēt neredzamus da<PERSON>", "commands.team.option.suffix.success": "Komandas sufikss iestatīts uz %s", "commands.team.remove.success": "Noņemta komanda %s", "commands.teammsg.failed.noteam": "<PERSON>v jāb<PERSON>t kādā komandā, lai ziņotu savai komandai", "commands.teleport.invalidPosition": "Nederīga pozīcija teleportēšanai", "commands.teleport.success.entity.multiple": "Teleportēti %s objekti uz %s", "commands.teleport.success.entity.single": "Teleportēja %s pie %s", "commands.teleport.success.location.multiple": "Teleportēti %s objekti uz %s, %s, %s", "commands.teleport.success.location.single": "Teleportēja %s uz %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klikšķini, lai kopētu starpliktuvē", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "Nav testi kurus palaist", "commands.test.relative_position": "Pozīcija apmēram ir %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Procentiles: P50: %sms P95: %sms P99: %sms, paraugs %s", "commands.tick.query.rate.running": "Mērķa tikšķu ātrums: %s tikšķi sekundē.\nVidējais tikšķā ilgums: %s milisekundes (mērķis %s milisekundes)", "commands.tick.query.rate.sprinting": "Mērķa tikšķu ātrums: %s sekundē (nav spēkā, atskaites ātrums).\nVidējais tikšķa ilgums: %s milisekundes", "commands.tick.rate.success": "Vēlamais ātrums uzstādīts uz %s tikšķi sekundē", "commands.tick.sprint.report": "Skrējiens pabeigts ar %s tikšķiem sekundē jeb %s milisekundēm tikšķī", "commands.tick.sprint.stop.fail": "Šobrīd netiek veikts tikšķu skrējiens", "commands.tick.sprint.stop.success": "Tikšķu skr<PERSON><PERSON><PERSON> p<PERSON>ts", "commands.tick.status.frozen": "<PERSON><PERSON><PERSON><PERSON> ir aps<PERSON><PERSON><PERSON>", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON>, bet nespēj sekot mērķa tikšķu ātrumam", "commands.tick.status.running": "<PERSON><PERSON><PERSON><PERSON>", "commands.tick.status.sprinting": "<PERSON><PERSON><PERSON><PERSON>", "commands.tick.step.fail": "<PERSON><PERSON><PERSON> soļot spēli - spēlei vispirms jābūt apst<PERSON>", "commands.tick.step.stop.fail": "Tikšķu soļošana nav aktīva", "commands.tick.step.stop.success": "Tikšķu so<PERSON><PERSON><PERSON><PERSON>", "commands.tick.step.success": "Pakāpj %s tikšķi(us)", "commands.time.query": "Laiks ir %s", "commands.time.set": "Laiks iestatīts uz %s", "commands.title.cleared.multiple": "Nodzēsti virsraksti priekš %s spēlētājiem", "commands.title.cleared.single": "Nodzēsti virsraksti priekš %s", "commands.title.reset.multiple": "Atiestatītas virsraksta opcijas priekš %s spēlētājiem", "commands.title.reset.single": "Atiestatītas virsraksta opcijas priekš %s", "commands.title.show.actionbar.multiple": "<PERSON><PERSON><PERSON> jaunu darb<PERSON><PERSON> j<PERSON><PERSON> virsrakstu %s spēlētājiem", "commands.title.show.actionbar.single": "<PERSON><PERSON><PERSON> jaunu darb<PERSON><PERSON> j<PERSON><PERSON> virsrakstu %s", "commands.title.show.subtitle.multiple": "Rāda jaunu apakšvirsrakstu priekš %s spēlētājiem", "commands.title.show.subtitle.single": "Rāda apakšvirsrakstu priekš %s", "commands.title.show.title.multiple": "<PERSON><PERSON><PERSON> jaunu virsrakstu priekš %s spēlētājiem", "commands.title.show.title.single": "Rāda jaunu virsrakstu priekš %s", "commands.title.times.multiple": "Mainīti virsraksta displeja laiki %s spēlētājiem", "commands.title.times.single": "Mainīti virsraksta displeja laiki %s", "commands.transfer.error.no_players": "Jānor<PERSON>da vismaz viens spēlētājs, lai pā<PERSON><PERSON>", "commands.transfer.success.multiple": "Pārsūta %s spēlētājus uz %s:%s", "commands.transfer.success.single": "Pārsūta %s uz %s:%s", "commands.trigger.add.success": "Izraisīts %s (pievienots %s vērtībai)", "commands.trigger.failed.invalid": "Tu vari aktivizēt tikai tos mērķus, kuriem ir 'mērķa' veids", "commands.trigger.failed.unprimed": "Tu vēl nevari aktivizēt šo mērķi", "commands.trigger.set.success": "Izraisīts %s (iestatīt vērtību uz %s)", "commands.trigger.simple.success": "Izraisīts %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Laiks mainīts uz skaidru laiku", "commands.weather.set.rain": "Laiks mainīts uz lietu", "commands.weather.set.thunder": "Laiks mainīts uz lietu un pērkonu", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jau ir baltajā sa<PERSON>", "commands.whitelist.add.success": "Pievienoja %s baltajam sarakstam", "commands.whitelist.alreadyOff": "<PERSON><PERSON>is sa<PERSON> jau ir iz<PERSON>s", "commands.whitelist.alreadyOn": "<PERSON><PERSON><PERSON> sa<PERSON> jau ir i<PERSON>l<PERSON>s", "commands.whitelist.disabled": "<PERSON><PERSON><PERSON> sa<PERSON> ir i<PERSON>s", "commands.whitelist.enabled": "<PERSON><PERSON><PERSON> sa<PERSON> ir i<PERSON><PERSON>s", "commands.whitelist.list": "Baltajā sarakstā ir %s spēlētājs(i): %s", "commands.whitelist.none": "Nav spē<PERSON><PERSON><PERSON><PERSON><PERSON> baltaj<PERSON> sa<PERSON>", "commands.whitelist.reloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balto sa<PERSON>u", "commands.whitelist.remove.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nav baltajā sarakstā", "commands.whitelist.remove.success": "Izņēma %s no baltā saraksta", "commands.worldborder.center.failed": "<PERSON><PERSON><PERSON>, pasa<PERSON> robe<PERSON>a jau <PERSON> ir centrēta", "commands.worldborder.center.success": "Pasaules robežas centrs iestatīts uz %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> robe<PERSON> bojā<PERSON><PERSON> jau ir tādā <PERSON>", "commands.worldborder.damage.amount.success": "<PERSON><PERSON><PERSON> robe<PERSON> boj<PERSON><PERSON><PERSON> iestatīts uz %s par katru bloku sekundē", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON> buferis jau ir tādā attā<PERSON>", "commands.worldborder.damage.buffer.success": "<PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON> buferis iestatīts uz %s blokam(iem)", "commands.worldborder.get": "Pa<PERSON>ules robe<PERSON>a <PERSON>d ir %s bloks(us) plata", "commands.worldborder.set.failed.big": "<PERSON><PERSON><PERSON> robe<PERSON>a nevar būt liel<PERSON> par %s blokiem", "commands.worldborder.set.failed.far": "<PERSON><PERSON><PERSON> robe<PERSON>a nevar būt tā<PERSON>k par %s blokiem", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON>, pasa<PERSON> robe<PERSON>a jau ir š<PERSON><PERSON><PERSON>", "commands.worldborder.set.failed.small": "Pa<PERSON><PERSON> robe<PERSON>a nevar būt mazāka par vienu bloku", "commands.worldborder.set.grow": "Audzē pasaules robežu %s bloku platu %s sekundēs", "commands.worldborder.set.immediate": "Iestati pasaules robežu uz %s bloks(us) platu", "commands.worldborder.set.shrink": "Samazina pasaules robežu uz %s bloku(s) platu pa %s sekundes(ēm)", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON><PERSON>, pasa<PERSON> robe<PERSON><PERSON> brī<PERSON><PERSON><PERSON> jau ir šādā distancē", "commands.worldborder.warning.distance.success": "Iestati pasaules robe<PERSON><PERSON> brī<PERSON>juma attālumu uz %s blok(iem)", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON> robe<PERSON><PERSON> brī<PERSON> jau ir tāds laiks", "commands.worldborder.warning.time.success": "Iestati pasaules robe<PERSON><PERSON> brīdinājuma laiku uz %s sekundes(ēm)", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON><PERSON> vair<PERSON>k nekā 24 stundas", "compliance.playtime.hours": "<PERSON><PERSON><PERSON> s<PERSON> %s stundu(as)", "compliance.playtime.message": "Pārm<PERSON><PERSON><PERSON><PERSON> spēl<PERSON> var traucēt normālu ikdienas d<PERSON>vi", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Pieslēdzas...", "connect.connecting": "Savienojas ar serveri...", "connect.encrypting": "Notiek šifrēšana...", "connect.failed": "Neizdevās izveidot savienojumu ar serveri", "connect.failed.transfer": "Savienojums neizdevās pā<PERSON>ūtot uz serveri", "connect.joining": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>...", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON>...", "connect.reconfiging": "Pārkonfigurē...", "connect.reconfiguring": "Pārkonfigurē...", "connect.transferring": "Notiek pārsūtīšana uz jaunu serveri...", "container.barrel": "Muca", "container.beacon": "<PERSON><PERSON><PERSON>", "container.beehive.bees": "Bites: %s / %s", "container.beehive.honey": "Medus: %s / %s", "container.blast_furnace": "<PERSON><PERSON> kr<PERSON>", "container.brewing": "Brūvēšanas statīvs", "container.cartography_table": "Kartogrāfijas galds", "container.chest": "<PERSON><PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON><PERSON>", "container.crafter": "Amatnieks", "container.crafting": "Amatniecība", "container.creative": "Priekšmetu Izvēle", "container.dispenser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s la<PERSON><PERSON><PERSON><PERSON>", "container.enchant.lapis.one": "1 lazurīts", "container.enchant.level.many": "%s burvest<PERSON><PERSON> l<PERSON>i", "container.enchant.level.one": "1 burvestības līmenis", "container.enchant.level.requirement": "Nepiecie<PERSON><PERSON><PERSON> l<PERSON>: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.grindstone_title": "Salabot un noņemt uzlabojumus", "container.hopper": "Priekšmetu piltuve", "container.inventory": "Inventārs", "container.isLocked": "%s ir slēgta!", "container.lectern": "Lektora galds", "container.loom": "Stelles", "container.repair": "Salabot un nosaukt", "container.repair.cost": "Burvestības izmaksas: %1$s", "container.repair.expensive": "<PERSON><PERSON><PERSON><PERSON><PERSON> dārg<PERSON>!", "container.shulkerBox": "<PERSON><PERSON><PERSON> kaste", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "un %s vairāk...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Nevar atvērt. Laupījums vēl nav uzģenerēts.", "container.stonecutter": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "Uzlabot aprīkojumu", "container.upgrade.error_tooltip": "<PERSON><PERSON><PERSON>u nevar u<PERSON>bot šādā veidā", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON><PERSON>", "controls.keybinds": "<PERSON><PERSON> sasaistes...", "controls.keybinds.duplicateKeybinds": "Šis taustiņš arī tiek lietots priekš: %s", "controls.keybinds.title": "<PERSON><PERSON>", "controls.reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controls.title": "Vadības <PERSON>ā<PERSON>", "createWorld.customize.buffet.biome": "Lūdzu izvēlieties biomu", "createWorld.customize.buffet.title": "<PERSON><PERSON><PERSON><PERSON> bufetes pasauli", "createWorld.customize.flat.height": "Augstums", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Apakša - %s", "createWorld.customize.flat.layer.top": "Augša - %s", "createWorld.customize.flat.removeLayer": "Noņemt slāni", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON><PERSON> veida <PERSON>", "createWorld.customize.presets": "Priekšiestatījumi", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON><PERSON><PERSON>, šeit ir daži ko mēs jau agrāk izveidojām!", "createWorld.customize.presets.select": "Izmantot priekšiestatījumu", "createWorld.customize.presets.share": "Vēlies dalīties ar saglabātajiem iestatījumiem? Izmantojiet lodziņu apakšā!", "createWorld.customize.presets.title": "Izvēlēties priekšiestatījumu", "createWorld.preparing": "Sagatavojas pasaules veidošanai...", "createWorld.tab.game.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Pasaule", "credits_and_attribution.button.attribution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "Licenzes", "credits_and_attribution.screen.title": "Autori un attiecinājums", "dataPack.bundle.description": "Iespējo eksperimentālu saišķa iespēju", "dataPack.bundle.name": "Saišķi", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "<PERSON><PERSON><PERSON>bot<PERSON> vagonu kust<PERSON>ba", "dataPack.minecart_improvements.name": "<PERSON><PERSON><PERSON>", "dataPack.redstone_experiments.description": "Eksperimentālās Redstone izmaiņas", "dataPack.redstone_experiments.name": "Redstone eksperimenti", "dataPack.title": "Izvēlēties Datu <PERSON>", "dataPack.trade_rebalance.description": "Atjauninātati ciemata iedzīvotāju <PERSON>", "dataPack.trade_rebalance.name": "Ciemata <PERSON>zīvot<PERSON><PERSON> darī<PERSON><PERSON> lī<PERSON><PERSON><PERSON><PERSON>", "dataPack.update_1_20.description": "Jaunas Minecraft 1.20 iespējas un saturs", "dataPack.update_1_20.name": "Atjauninājums 1.20", "dataPack.update_1_21.description": "Jaunas Minecraft 1.21 iespējas un saturs", "dataPack.update_1_21.name": "Atjauninājums 1.21", "dataPack.validation.back": "<PERSON><PERSON>", "dataPack.validation.failed": "Datu pakas validācija neizdevās!", "dataPack.validation.reset": "Atiestatīt uz Noklusējumu", "dataPack.validation.working": "<PERSON><PERSON><PERSON><PERSON> iz<PERSON> datu pakas...", "dataPack.vanilla.description": "Minecraft noklusējuma dati", "dataPack.vanilla.name": "Noklusējums", "dataPack.winter_drop.description": "Jaunas Winter Drop funkcijas un saturs", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON> piliens", "datapackFailure.safeMode": "<PERSON><PERSON><PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "<PERSON><PERSON> pasaule satur nederīgus vai bojātus saglabātos datus.", "datapackFailure.safeMode.failed.title": "Neizdevā<PERSON> p<PERSON>uli Drošajā <PERSON>.", "datapackFailure.title": "<PERSON><PERSON><PERSON><PERSON> pašreiz izvēlētajās datu pakās neļāva pasaulei ielādēties.\nVariet mēģināt to ielādēt tikai ar vanilla datu pakām (\"d<PERSON><PERSON><PERSON> režīms\"), vai doties atpakaļ uz sākuma ekrānu un to salabot manuāli.", "death.attack.anvil": "<PERSON><PERSON><PERSON><PERSON>ša lakta saspieda %1$s", "death.attack.anvil.player": "%1$s sasp<PERSON>a k<PERSON><PERSON><PERSON><PERSON> lakta, kam<PERSON>r cīn<PERSON>j<PERSON> ar %2$s", "death.attack.arrow": "%2$s nošāva %1$s", "death.attack.arrow.item": "%2$s nošāva %1$s izmantojot %3$s", "death.attack.badRespawnPoint.link": "<PERSON><PERSON><PERSON><PERSON><PERSON> sp<PERSON> dizains", "death.attack.badRespawnPoint.message": "%2$s nogalināja %1$s", "death.attack.cactus": "%1$s tika sad<PERSON>t<PERSON>ts līdz nāvei", "death.attack.cactus.player": "%1$s ieskrēja kaktus<PERSON>, mēģinot aizbēgt no %2$s", "death.attack.cramming": "%1$s tika par daudz saspiests", "death.attack.cramming.player": "%2$s saspieda %1$s", "death.attack.dragonBreath": "%1$s tika izgrauzdēts pūķa elpā", "death.attack.dragonBreath.player": "%1$s izcepās %2$s pūķa elpā", "death.attack.drown": "%1$s noslīka", "death.attack.drown.player": "%1$s noslīka mēģinot aizbēgt no %2$s", "death.attack.dryout": "%1$s nomira no dehidrācijas", "death.attack.dryout.player": "%1$s nomira no dehidrcijas, mēģinot aizbēgt no %2$s", "death.attack.even_more_magic": "%1$s tika nogal<PERSON>, i<PERSON><PERSON><PERSON><PERSON> vēl vairāk maģijas", "death.attack.explosion": "%1$s uzsprāga", "death.attack.explosion.player": "%2$s uzspridzināja %1$s", "death.attack.explosion.player.item": "%2$s uzspridzināja %1$s izmantojot %3$s", "death.attack.fall": "%1$s nokrita no pārāk liela augstuma", "death.attack.fall.player": "%1$s pārāk smagi atsitās pret zemi, mēģinot aizbēgt no %2$s", "death.attack.fallingBlock": "Krītošs bloks saspieda %1$s", "death.attack.fallingBlock.player": "%1$s sasp<PERSON>a kr<PERSON><PERSON><PERSON><PERSON>, kam<PERSON>r cīn<PERSON><PERSON> ar %2$s", "death.attack.fallingStalactite": "%1$s tika caurdurts ar kr<PERSON><PERSON><PERSON><PERSON> stalaktītu", "death.attack.fallingStalactite.player": "%1$s tika caurdurts ar kr<PERSON><PERSON><PERSON><PERSON> s<PERSON>, kam<PERSON>r c<PERSON> ar %2$s", "death.attack.fireball": "%2$s nogalināja %1$s ar ugunsbumbu", "death.attack.fireball.item": "%2$s nogalināja %1$s ar ugunsbumbu izmantojot %3$s", "death.attack.fireworks": "%1$s aizgāja ar blīkšķi", "death.attack.fireworks.item": "%1$s uzsprāga ar blīkšķi salūta raķetes dēļ, kuru izšāva %2$s no %3$s", "death.attack.fireworks.player": "%1$s tika uzlaists gaisā, cīnoties ar %2$s", "death.attack.flyIntoWall": "%1$s piedzīvoja kinētisko enerģiju", "death.attack.flyIntoWall.player": "%1$s piered<PERSON><PERSON><PERSON> kinētisko enerģiju mēģinot aizbēgt no %2$s", "death.attack.freeze": "%1$s nosala līdz nāvei", "death.attack.freeze.player": "%2$s nosaldēja %1$s līdz nāvei", "death.attack.generic": "%1$s nomira", "death.attack.generic.player": "%1$s nomira %2$s dēļ", "death.attack.genericKill": "%1$s tika nogalin<PERSON>ts", "death.attack.genericKill.player": "%1$s tika nogalin<PERSON>ts kaujoties ar %2$s", "death.attack.hotFloor": "%1$s atkl<PERSON>ja, ka grīda ir lava", "death.attack.hotFloor.player": "%1$s iegāja bīstamā zona dēļ %2$s", "death.attack.inFire": "%1$s sadega liesmās", "death.attack.inFire.player": "%1$s cīnoties ar %2$s iegāja ugunī", "death.attack.inWall": "%1$s nosmaka sienā", "death.attack.inWall.player": "%1$s nosmaka sienā, kam<PERSON>r c<PERSON>n<PERSON>j<PERSON>s ar %2$s", "death.attack.indirectMagic": "%2$s ar maģiju nogalināja %1$s", "death.attack.indirectMagic.item": "%2$s nogalināja %1$s izmantojot %3$s", "death.attack.lava": "%1$s mēģināja nopeldēties lavā", "death.attack.lava.player": "%1$s bēgot no %2$s mēģināja peldēt lavā", "death.attack.lightningBolt": "%1$s nosita zibens", "death.attack.lightningBolt.player": "%1$s nosp<PERSON>ra zibe<PERSON>, kamēr cīn<PERSON><PERSON><PERSON><PERSON> ar %2$s", "death.attack.mace_smash": "%1$s salauza %2$s", "death.attack.mace_smash.item": "%1$s salauza %2$s ar %3$s", "death.attack.magic": "Maģija nogalināja %1$s", "death.attack.magic.player": "%1$s tika nogalināts ar maģiju, mēģinot aizbēgt no %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> ziņa bija pārāk gara lai tiktu pilnībā aizsūtīta. Atvaino! Šeit ir attīrīta versija: %s", "death.attack.mob": "%2$s nogalināja %1$s", "death.attack.mob.item": "%2$s nogalināja %1$s izmantojot %3$s", "death.attack.onFire": "%1$s sadega līdz nāvei", "death.attack.onFire.item": "%1$s sadega kraukšķīgs, kam<PERSON>r c<PERSON>j<PERSON> ar %2$s vicinot %3$s", "death.attack.onFire.player": "%1$s cīnoties ar %2$s pamatīgi izcepās", "death.attack.outOfWorld": "%1$s izkrita no pasaules", "death.attack.outOfWorld.player": "%1$s negrib dzīvot vienā pasaulē ar %2$s", "death.attack.outsideBorder": "%1$s atst<PERSON>ja <PERSON> pasaules robežas", "death.attack.outsideBorder.player": "%1$s atst<PERSON><PERSON> pasa<PERSON> robe<PERSON>, cīnoties ar %2$s", "death.attack.player": "%2$s nogalināja %1$s", "death.attack.player.item": "%2$s nogalināja %1$s izmantojot %3$s", "death.attack.sonic_boom": "%1$s tika noslaucīts no skaņas uzlādēta kliedziena", "death.attack.sonic_boom.item": "%1$s tika noslaucīts no skaņas uzlādēta kliedziena, mēģinot aizbēgt no %2$s vicinot %3$s", "death.attack.sonic_boom.player": "%1$s tika noslaucīts no skaņas uzlādēta kliedziena, mēģinot aizbēgt no %2$s", "death.attack.stalagmite": "%1$s tika sadurts uz stalagmīta", "death.attack.stalagmite.player": "%1$s tika sadurts uz stalagmīta cīnoties ar %2$s", "death.attack.starve": "%1$s nomira no bada", "death.attack.starve.player": "%1$s nomira badā, kam<PERSON>r c<PERSON>n<PERSON>j<PERSON> ar %2$s", "death.attack.sting": "%1$s tika nodzelts līdz nāvei", "death.attack.sting.item": "%2$s nodzēla %1$s līdz nāvei, izmantojot %3$s", "death.attack.sting.player": "%2$s nodzēla %1$s līdz nāvei", "death.attack.sweetBerryBush": "%1$s tika sad<PERSON>t<PERSON>ts līdz nāvei salda ogu krūma dēļ", "death.attack.sweetBerryBush.player": "%1$s tika sad<PERSON>t<PERSON>ts līdz nāvei saldo ogu krūmā, mēģinot aizbēgt no %2$s", "death.attack.thorns": "%1$s nomira cenšoties ievainot %2$s", "death.attack.thorns.item": "%3$s nogalināja %1$s, kurš mēģināja ievainot %2$s", "death.attack.thrown": "%2$s piekāva %1$s līdz nāvei", "death.attack.thrown.item": "%2$s piekāva %1$s izmantojot %3$s", "death.attack.trident": "%2$s sadūra %1$s", "death.attack.trident.item": "%1$s sad<PERSON>ra %2$s ar %3$s", "death.attack.wither": "%1$s sakalta", "death.attack.wither.player": "%1$s sakalta, kam<PERSON>r c<PERSON>n<PERSON>j<PERSON>s ar %2$s", "death.attack.witherSkull": "%1$s tika nošauts ar galvaskausu no %2$s", "death.attack.witherSkull.item": "%1$s tika nošauts ar galvaskausu no %2$s izmantojot %3$s", "death.fell.accident.generic": "%1$s nokrita no augstas vietas", "death.fell.accident.ladder": "%1$s nokrita no kāpnēm", "death.fell.accident.other_climbable": "%1$s nokrita rāpjoties", "death.fell.accident.scaffolding": "%1$s nokrita no stalažām", "death.fell.accident.twisting_vines": "%1$s nokrita no sagrieztiem vīteņaugiem", "death.fell.accident.vines": "%1$s nokrita no vīnogulāja", "death.fell.accident.weeping_vines": "%1$s nokrita no raudošajiem vīteņaugiem", "death.fell.assist": "%1$s tika nolemts krišanai no %2$s", "death.fell.assist.item": "%1$s tika nolemts krišanai no %2$s izmantojot %3$s", "death.fell.finish": "%1$s nokrita pārāk tālu un bija piebeigts ar %2$s", "death.fell.finish.item": "%1$s nokrita pārāk tālu un bija piebeigts ar %2$s izmantojot %3$s", "death.fell.killer": "%1$s bija nolemts krišanai", "deathScreen.quit.confirm": "Vai esat pārliecināts (-a) ka vēlaties iziet?", "deathScreen.respawn": "Atdzimt", "deathScreen.score": "Re<PERSON>ltā<PERSON>", "deathScreen.score.value": "Rezultāts: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON>", "deathScreen.title": "Tu Nomiri!", "deathScreen.title.hardcore": "S<PERSON><PERSON>le Beigusies!", "deathScreen.titleScreen": "Galvenais e<PERSON>", "debug.advanced_tooltips.help": "F3 + H = Papildu rīku statistika", "debug.advanced_tooltips.off": "<PERSON><PERSON><PERSON><PERSON> rīku <PERSON>: pasl<PERSON><PERSON>", "debug.advanced_tooltips.on": "<PERSON><PERSON><PERSON><PERSON> rīku informācija: parādīta", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.chunk_boundaries.off": "Gabalu barjeras: pas<PERSON><PERSON><PERSON><PERSON>", "debug.chunk_boundaries.on": "Gabalu barjeras: parād<PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> čatu", "debug.copy_location.help": "F3 + C = kopēt atrašanās vietu /tp komandai, turēt F3 + C, lai izrais<PERSON>tu spēles avāriju", "debug.copy_location.message": "<PERSON><PERSON><PERSON><PERSON> atra<PERSON> vieta k<PERSON>i", "debug.crash.message": "F3 + C ir nospiests. <PERSON><PERSON><PERSON><PERSON>b<PERSON>, ja vien F3 + C netiks atlaists.", "debug.crash.warning": "Avārija pēc %s...", "debug.creative_spectator.error": "<PERSON>evar <PERSON> s<PERSON><PERSON>; nav <PERSON><PERSON><PERSON>", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON><PERSON> i<PERSON>kšē<PERSON> spēles režīmu <-> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "debug.dump_dynamic_textures": "Saglabātas dinamiskās tekstūras uz %s", "debug.dump_dynamic_textures.help": "F3 + S = <PERSON>zgāzt dinamiskās tekstūras", "debug.gamemodes.error": "<PERSON><PERSON>r at<PERSON> spē<PERSON> p<PERSON>dz<PERSON>, nav atļ<PERSON><PERSON>", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON> spēles režīma pārslēdzēju", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s <PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.help.help": "F3 + Q = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šo sarak<PERSON>u", "debug.help.message": "Pogas stiprinājumi:", "debug.inspect.client.block": "<PERSON><PERSON><PERSON><PERSON> bloka dati klipkopai klienta servera pusē", "debug.inspect.client.entity": "<PERSON><PERSON><PERSON><PERSON> objekta dati klipkopai klienta servera pusē", "debug.inspect.help": "F3 + I = kopēt objekta vai bloka datus klip<PERSON>pai", "debug.inspect.server.block": "<PERSON><PERSON><PERSON><PERSON> bloka dati klipkopai servera pusē", "debug.inspect.server.entity": "<PERSON><PERSON><PERSON><PERSON> objekta dati klipkopai servera pusē", "debug.pause.help": "F3 + Esc = <PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON><PERSON> pauzes izvēlni (ja pauz<PERSON><PERSON>na ir iespējama)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON><PERSON><PERSON><PERSON> pazaudējot fokusu", "debug.pause_focus.off": "Nopauzēts uz pēdējo fokusu: izslēgts", "debug.pause_focus.on": "Nopauzēts uz pēdējo fokusu: ieslēgts", "debug.prefix": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]:", "debug.profiling.help": "F3 + L = Sākt/beigt profil<PERSON>nu", "debug.profiling.start": "Profilēšana sākta uz %s sekundēm. Izmanto F3 + L, lai apturētu agrāk", "debug.profiling.stop": "Profil<PERSON><PERSON><PERSON> beig<PERSON>ies. Rezultāti saglabāti %s", "debug.reload_chunks.help": "F3 + A = <PERSON><PERSON>rl<PERSON><PERSON><PERSON><PERSON> gabalus", "debug.reload_chunks.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus gabalus", "debug.reload_resourcepacks.help": "F3 + T = Pārlād<PERSON>t resursu pakas", "debug.reload_resourcepacks.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resursu pakas", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> malas", "debug.show_hitboxes.off": "<PERSON><PERSON> vienī<PERSON>: pas<PERSON><PERSON><PERSON><PERSON>", "debug.show_hitboxes.on": "<PERSON><PERSON> vienī<PERSON>: parā<PERSON><PERSON><PERSON>", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Šis demo ilgs piecas spēles dienas. Dari ko spēj!", "demo.day.2": "<PERSON><PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON><PERSON>na", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON> diena", "demo.day.5": "<PERSON>ī ir J<PERSON> pēdējā diena!", "demo.day.6": "Tu esi aizvadījis savu piekto dienu. Izmanto %s, lai uzņemtu savas celtnes attēlu.", "demo.day.warning": "<PERSON><PERSON><PERSON> laiks ir gandr<PERSON><PERSON> be<PERSON>!", "demo.demoExpired": "Demonstrācijas laiks ir beidzies!", "demo.help.buy": "Pirkt tagad!", "demo.help.fullWrapped": "Šis demo ilgs 5 spēles dienas (apmēram 1 stundu un 40 minūtes). Apskaties sasniegumus priekš padomiem! Izklaidējies!", "demo.help.inventory": "Izmantojiet taustiņu %1$s, lai atvērtu somu", "demo.help.jump": "Lec, nospiežot taustiņu %1$s", "demo.help.later": "<PERSON><PERSON><PERSON> spēlēt!", "demo.help.movement": "Izmantojiet taustiņus %1$s, %2$s, %3$s, %4$s un peli, lai pārvietotos", "demo.help.movementMouse": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> peli", "demo.help.movementShort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nospiežot taustiņ<PERSON> %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft demonstrācijas režīms", "demo.remainingTime": "Atlikušais laiks: %s", "demo.reminder": "Demonstrēju<PERSON> laiks ir beidzies. <PERSON><PERSON><PERSON><PERSON>, lai turpin<PERSON> vai uzsāktu jaunu pasauli!", "difficulty.lock.question": "Vai tiešām vēlies slēgt šīs pasaules grūtību? Šajā pasaulē grūtība vienmēr būs %1$s, un to nebūs iespējams nomainīt atkal.", "difficulty.lock.title": "<PERSON><PERSON><PERSON><PERSON> pasaules gr<PERSON><PERSON><PERSON>bu", "disconnect.endOfStream": "Plū<PERSON><PERSON> be<PERSON>s", "disconnect.exceeded_packet_rate": "Izmests par pakešu tempa pā<PERSON>", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorē statusa pieprasījumu", "disconnect.loginFailedInfo": "Neizdevās pieteikties: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Daudzspēlētā<PERSON> režīms ir atslēgts. <PERSON><PERSON><PERSON><PERSON> pārbaudiet sava Microsoft konta iestatījumus.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> (mēģiniet restartēt spēli un palaišanas programmu)", "disconnect.loginFailedInfo.serversUnavailable": "Autentifikācijas serveri pašlaik nav sasniedzami. Lūdzu mēģiniet vēlreiz.", "disconnect.loginFailedInfo.userBanned": "Tev ir aizliegts sp<PERSON><PERSON><PERSON>t <PERSON>", "disconnect.lost": "Savienojums zudis", "disconnect.packetError": "Tīkla protokola kļūda", "disconnect.spam": "Izmests spamošanas dēļ", "disconnect.timeout": "<PERSON>vie<PERSON><PERSON><PERSON> no<PERSON>", "disconnect.transfer": "<PERSON><PERSON><PERSON><PERSON><PERSON>īts uz citu serveri", "disconnect.unknownHost": "Nezināms serveris", "download.pack.failed": "Neizdev<PERSON><PERSON> %s no %s pakām", "download.pack.progress.bytes": "Progress: %s (kopējais izmērs nezinā<PERSON>)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Lejupielādē resursu paku %s/%s", "editGamerule.default": "Noklusējums: %s", "editGamerule.title": "Rediģēt spēles note<PERSON>us", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Uzsūkšana", "effect.minecraft.bad_omen": "Slikta vēsts", "effect.minecraft.blindness": "Aklums", "effect.minecraft.conduit_power": "Konduita s<PERSON>", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.fire_resistance": "Uguns noturība", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Steiga", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON>", "effect.minecraft.hunger": "Izsalkums", "effect.minecraft.infested": "Infestēts", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "effect.minecraft.invisibility": "Neredzamība", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "Levi<PERSON>ā<PERSON><PERSON>", "effect.minecraft.luck": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON> nogu<PERSON>", "effect.minecraft.nausea": "Nelabums", "effect.minecraft.night_vision": "Nakts redze", "effect.minecraft.oozing": "Izplūšana", "effect.minecraft.poison": "Inde", "effect.minecraft.raid_omen": "Iebrukuma vēsts", "effect.minecraft.regeneration": "Reģenerācija", "effect.minecraft.resistance": "Noturība", "effect.minecraft.saturation": "Piesā<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slowness": "Lēnums", "effect.minecraft.speed": "Ātrums", "effect.minecraft.strength": "S<PERSON>ē<PERSON>", "effect.minecraft.trial_omen": "Izaicināju<PERSON> vēsts", "effect.minecraft.unluck": "Neveiksme", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "<PERSON>ē<PERSON>l<PERSON>", "effect.minecraft.wither": "<PERSON><PERSON><PERSON>", "effect.none": "Nav <PERSON><PERSON><PERSON>ī<PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Ūdenslīdējs", "enchantment.minecraft.bane_of_arthropods": "Posmkāju Posts", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "enchantment.minecraft.blast_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.breach": "C<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Blīvums", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON> ceļ<PERSON>s", "enchantment.minecraft.efficiency": "Efektivitāte", "enchantment.minecraft.feather_falling": "Spalvas kritiens", "enchantment.minecraft.fire_aspect": "Uguns Aspekts", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Bagātība", "enchantment.minecraft.frost_walker": "<PERSON><PERSON> s<PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Bezgalība", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "S<PERSON>ē<PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "Aizsardzība", "enchantment.minecraft.punch": "Sitiens", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Plūdšķēlis", "enchantment.minecraft.sharpness": "Asums", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "Sods", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>s", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>s", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Ērkšķi", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akācijas laiva", "entity.minecraft.acacia_chest_boat": "Akācijas laiva ar lādi", "entity.minecraft.allay": "<PERSON><PERSON><PERSON>", "entity.minecraft.area_effect_cloud": "<PERSON>pk<PERSON><PERSON><PERSON> efekta mākonis", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON> stat<PERSON>s", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Bambusa plosts ar lādi", "entity.minecraft.bamboo_raft": "Bambusa plosts", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Bite", "entity.minecraft.birch_boat": "Bērza laiva", "entity.minecraft.birch_chest_boat": "Bērza laiva ar lādi", "entity.minecraft.blaze": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "Bloka ekrāns", "entity.minecraft.boat": "<PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON>", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "Kaķis", "entity.minecraft.cave_spider": "<PERSON><PERSON>", "entity.minecraft.cherry_boat": "Ķirša laiva", "entity.minecraft.cherry_chest_boat": "Ķirša laiva ar lādi", "entity.minecraft.chest_boat": "<PERSON><PERSON> ar l<PERSON>", "entity.minecraft.chest_minecart": "Vagons ar lādi", "entity.minecraft.chicken": "Vista", "entity.minecraft.cod": "Menca", "entity.minecraft.command_block_minecart": "Vagons ar komandu bloku", "entity.minecraft.cow": "Govs", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Tumšā ozola laiva", "entity.minecraft.dark_oak_chest_boat": "Tumšā ozola laiva ar lādi", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "Ēzelis", "entity.minecraft.dragon_fireball": "Pūķa uguns bumba", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON> ola", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>s", "entity.minecraft.end_crystal": "<PERSON> k<PERSON>", "entity.minecraft.ender_dragon": "Ender pūķis", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "Evokera ilkņi", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON><PERSON> bur<PERSON><PERSON><PERSON> pudele", "entity.minecraft.experience_orb": "Pieredzes lode", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> acs", "entity.minecraft.falling_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "entity.minecraft.falling_block_type": "Krītošs %s", "entity.minecraft.fireball": "Ugunsbumba", "entity.minecraft.firework_rocket": "<PERSON><PERSON><PERSON> raķete", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.fox": "Laps<PERSON>", "entity.minecraft.frog": "Varde", "entity.minecraft.furnace_minecart": "Vagons ar k<PERSON>ni", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vagons ar piltuvi", "entity.minecraft.horse": "Zirgs", "entity.minecraft.husk": "<PERSON><PERSON>", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.iron_golem": "Dzelzs golems", "entity.minecraft.item": "Lieta", "entity.minecraft.item_display": "Priekšmeta <PERSON>", "entity.minecraft.item_frame": "Priekš<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Džungļu koka laiva", "entity.minecraft.jungle_chest_boat": "Džungļu koka laiva ar lādi", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON><PERSON><PERSON> trusis", "entity.minecraft.leash_knot": "Pa<PERSON>das mezg<PERSON>", "entity.minecraft.lightning_bolt": "Zibens bulta", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON>", "entity.minecraft.magma_cube": "Magma<PERSON> kubs", "entity.minecraft.mangrove_boat": "Mangroves laiva", "entity.minecraft.mangrove_chest_boat": "Mangroves laiva ar lādi", "entity.minecraft.marker": "Marķieris", "entity.minecraft.minecart": "Vagons", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Ozola laiva", "entity.minecraft.oak_chest_boat": "Ozola laiva ar lādi", "entity.minecraft.ocelot": "Ocelots", "entity.minecraft.ominous_item_spawner": "Draudīgs priekšmetu raītājs", "entity.minecraft.painting": "Glezna", "entity.minecraft.pale_oak_boat": "Bālā ozola laiva", "entity.minecraft.pale_oak_chest_boat": "Bālā ozola laiva ar lādi", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "Fantoms", "entity.minecraft.pig": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> piglins", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "Dzira", "entity.minecraft.pufferfish": "Pūšļ<PERSON> z<PERSON>s", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "<PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Shulker lode", "entity.minecraft.silverfish": "Sudrabzi<PERSON><PERSON>", "entity.minecraft.skeleton": "Skelets", "entity.minecraft.skeleton_horse": "Skeleta zirgs", "entity.minecraft.slime": "Gļ<PERSON><PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON> ugun<PERSON>bu<PERSON>", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Sniega golems", "entity.minecraft.snowball": "Sniega pika", "entity.minecraft.spawner_minecart": "Vagons ar br<PERSON><PERSON>", "entity.minecraft.spectral_arrow": "Spe<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "<PERSON>gles laiva", "entity.minecraft.spruce_chest_boat": "Egles laiva ar lādi", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON> ceļ<PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Teksta <PERSON>", "entity.minecraft.tnt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> din<PERSON>", "entity.minecraft.tnt_minecart": "Vagons ar din<PERSON>", "entity.minecraft.trader_llama": "Tirgotāja lama", "entity.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish": "Tropiskā zivs", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON> tangeja", "entity.minecraft.tropical_fish.predefined.10": "Mauru elks", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "Papagaiļ<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "Karaliskā Eņģeļzivs", "entity.minecraft.tropical_fish.predefined.14": "Sarkanais Čičilds", "entity.minecraft.tropical_fish.predefined.15": "Sarkanais Lūpas Blennijs", "entity.minecraft.tropical_fish.predefined.16": "Sarkanā lutj<PERSON>s", "entity.minecraft.tropical_fish.predefined.17": "Treadfins", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Triger<PERSON>", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON> tan<PERSON>ja", "entity.minecraft.tropical_fish.predefined.20": "Dzeltenastes Papagailzivs", "entity.minecraft.tropical_fish.predefined.21": "Dzeltenā tangeja", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Cicilde", "entity.minecraft.tropical_fish.predefined.5": "Klaunzivs", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Ķeizersarkanā lutj<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Govzivs", "entity.minecraft.tropical_fish.type.betty": "Betija", "entity.minecraft.tropical_fish.type.blockfish": "Blīvjūra", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "Trieciens", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON> stari", "entity.minecraft.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vex": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager": "<PERSON>iemata <PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "Miesnieks", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "Garīdznieks", "entity.minecraft.villager.farmer": "Zemnieks", "entity.minecraft.villager.fisherman": "Zvejnieks", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotekārs", "entity.minecraft.villager.mason": "Mūrn<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Muļķis", "entity.minecraft.villager.none": "<PERSON>iemata <PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON> gans", "entity.minecraft.villager.toolsmith": "Instrumentu kal<PERSON>s", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Uzraugs", "entity.minecraft.wind_charge": "<PERSON><PERSON><PERSON>", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Wither skelets", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "Vilks", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Zombiju zirgs", "entity.minecraft.zombie_villager": "Zombiju ciema iedzīvotājs", "entity.minecraft.zombified_piglin": "Zombificēts Piglins", "entity.not_summonable": "Nevar izsaukt entītiju ar tipu %s", "event.minecraft.raid": "Iebrukums", "event.minecraft.raid.defeat": "Sakāve", "event.minecraft.raid.defeat.full": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Atlikušie iebrucēji: %s", "event.minecraft.raid.victory": "Uzvara", "event.minecraft.raid.victory.full": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON> dārgumu karte", "filled_map.explorer_jungle": "Džungļu pētnieka karte", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON> p<PERSON>ka karte", "filled_map.id": "ID #%s", "filled_map.level": "(Līmenis %s/%s)", "filled_map.locked": "Slēgta", "filled_map.mansion": "<PERSON><PERSON><PERSON> karte", "filled_map.monument": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> karte", "filled_map.scale": "Mērogs 1:%s", "filled_map.trial_chambers": "Pēt<PERSON><PERSON> i<PERSON> karte", "filled_map.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> karte", "filled_map.village_desert": "<PERSON><PERSON><PERSON><PERSON> ciemata karte", "filled_map.village_plains": "<PERSON>ļ<PERSON><PERSON> ciemata karte", "filled_map.village_savanna": "Savannas ciemata karte", "filled_map.village_snowy": "Sniegota ciemata karte", "filled_map.village_taiga": "Taigas ciemata karte", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Klasiski plats", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Virszeme", "flat_world_preset.minecraft.redstone_ready": "Gatavs Redstone", "flat_world_preset.minecraft.snowy_kingdom": "Sniega ka<PERSON>ļvalsts", "flat_world_preset.minecraft.the_void": "Tukšums", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.water_world": "Ūdens pasaule", "flat_world_preset.unknown": "???", "gameMode.adventure": "Piedzīvojumu režīms", "gameMode.changed": "Spēles režīms tika atjaunināts uz %s", "gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "Nežēlīgais Režī<PERSON>!", "gameMode.spectator": "Skatītāja <PERSON>", "gameMode.survival": "Izdzīvošanas režīms", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON><PERSON> sas<PERSON>", "gamerule.blockExplosionDropDecay": "<PERSON><PERSON><PERSON> s<PERSON>, da<PERSON><PERSON> bloki ne<PERSON> to mantas", "gamerule.blockExplosionDropDecay.description": "<PERSON><PERSON><PERSON>, ko rad<PERSON><PERSON> sprādziens no bloku mi<PERSON>, tiek zaud<PERSON> sprādzien<PERSON>.", "gamerule.category.chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.drops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.updates": "<PERSON><PERSON><PERSON>", "gamerule.commandBlockOutput": "P<PERSON><PERSON><PERSON><PERSON>t komandu bloku izeju", "gamerule.commandModificationBlockLimit": "<PERSON><PERSON><PERSON>ācijas Bloku Limits", "gamerule.commandModificationBlockLimit.description": "Bloku skaits kurus var mainīt vienlaicīgi pēc vienas komandas, kā aizpildīt vai klonēt.", "gamerule.disableElytraMovementCheck": "Atspējot elitras kustī<PERSON>", "gamerule.disablePlayerMovementCheck": "Ats<PERSON><PERSON><PERSON> spēlē<PERSON><PERSON><PERSON> kustī<PERSON>i", "gamerule.disableRaids": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doDaylightCycle": "Progresēt dienas laiku", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON><PERSON> atst<PERSON>t<PERSON> (kā arī to saturu), p<PERSON><PERSON><PERSON><PERSON><PERSON>, la<PERSON>s utt.", "gamerule.doFireTick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doImmediateRespawn": "Atdzimt nekavējoties", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doLimitedCrafting": "Veidošanai nepieciešama recepte", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, spē<PERSON><PERSON><PERSON><PERSON><PERSON> varēs veidot tikai atklātās receptes.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON> rad<PERSON><PERSON><PERSON> mantas", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON><PERSON> rad<PERSON> at<PERSON> resursus, k<PERSON> arī pieredzes punktus.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doMobSpawning.description": "<PERSON><PERSON><PERSON><PERSON> entītijām var būt atsevišķi noteikumi.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON>", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON><PERSON> bloku atst<PERSON> resursus, k<PERSON> ar<PERSON> pieredzes punktus.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doVinesSpread": "Vinogulāju izplatīša<PERSON>ās", "gamerule.doVinesSpread.description": "Kontrolē vai vīnojulāju bloki nejauši izplatās uz blakus esošajiem blokiem. Šis neietekmē citus vīnogul<PERSON><PERSON> veidus, <PERSON><PERSON><PERSON><PERSON>, sa<PERSON><PERSON><PERSON><PERSON> v<PERSON>, r<PERSON><PERSON><PERSON><PERSON> v<PERSON>, utt.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON>", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> pazūd pēc nāves", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON>ai spēlētāja izmestas End<PERSON> p<PERSON> pazūd, kad spēlēt<PERSON><PERSON><PERSON> no<PERSON>.", "gamerule.entitiesWithPassengersCanUsePortals": "Entītijas ar pasažierim var izmantot portālus", "gamerule.entitiesWithPassengersCanUsePortals.description": "Ļaut entītijām ar pasažieriem teleportēties caur Nether portāliem, End portāliem un End vārtejām.", "gamerule.fallDamage": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON> u<PERSON> bo<PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "Piedot mirušiem spēlē<PERSON>ājiem", "gamerule.forgiveDeadPlayers.description": "Sadusmotie neitrā<PERSON> rad<PERSON> beidz k<PERSON>t dusm<PERSON>gi kad attiec<PERSON>s spēlētājs tuvum<PERSON> nomirst.", "gamerule.freezeDamage": "<PERSON><PERSON><PERSON><PERSON> sala bo<PERSON><PERSON><PERSON><PERSON>", "gamerule.globalSoundEvents": "<PERSON><PERSON>b<PERSON><PERSON> s<PERSON> not<PERSON>", "gamerule.globalSoundEvents.description": "<PERSON>d notiek konkr<PERSON><PERSON> s<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> bosa r<PERSON>, tā skaņu varēs dzird<PERSON>t visur.", "gamerule.keepInventory": "Saglab<PERSON><PERSON> inventāru pēc nāves", "gamerule.lavaSourceConversion": "<PERSON>va pārtop avotā", "gamerule.lavaSourceConversion.description": "Kad plūsto<PERSON>u lavu no divām pusēm ieskauj lavas avoti, tā pārtop par avotu.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "gamerule.maxCommandChainLength": "Komandu ķēdes izmēra limits", "gamerule.maxCommandChainLength.description": "Attiecas uz komandu bloku ķēdēm un funkcijām.", "gamerule.maxCommandForkCount": "Komandas konteksta limits", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, ko var izman<PERSON>t tādas komandas kā 'execute as'.", "gamerule.maxEntityCramming": "Vienību pārblī<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "gamerule.minecartMaxSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>u ātrums", "gamerule.minecartMaxSpeed.description": "<PERSON><PERSON><PERSON> maks<PERSON> no<PERSON>tais ātrums uz zemes.", "gamerule.mobExplosionDropDecay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON> bloki ne<PERSON> to mantas", "gamerule.mobExplosionDropDecay.description": "<PERSON><PERSON><PERSON>, ko rad<PERSON><PERSON><PERSON> rad<PERSON> spr<PERSON>, tiek zaud<PERSON> sprādzien<PERSON>.", "gamerule.mobGriefing": "Atļaut <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> da<PERSON>", "gamerule.naturalRegeneration": "Atjaunot dzīvības", "gamerule.playersNetherPortalCreativeDelay": "Spē<PERSON>ē<PERSON><PERSON><PERSON>her portāla aizture radošajā režī<PERSON>ā", "gamerule.playersNetherPortalCreativeDelay.description": "Laiks (tikšķos) cik ilgi radošā režīma spēlētājam jāstāv Nether portālā, lai nomainītu dimensiju.", "gamerule.playersNetherPortalDefaultDelay": "Sp<PERSON><PERSON>ē<PERSON><PERSON>ja Nether portāla aizture ne-radošajā rež<PERSON>ā", "gamerule.playersNetherPortalDefaultDelay.description": "Laiks (tikšķos) cik ilgi ne-radošā režīma spēlētājam jāstāv Nether portālā, lai nomainītu dimensiju.", "gamerule.playersSleepingPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON> procents", "gamerule.playersSleepingPercentage.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> procentos, ka<PERSON>, lai iz<PERSON>u nak<PERSON>.", "gamerule.projectilesCanBreakBlocks": "Šāviņi var salauzt blokus", "gamerule.projectilesCanBreakBlocks.description": "Kontrolē vai tireciena šāviņi iznīcinā<PERSON> b<PERSON>, ko tie var iznīcināt.", "gamerule.randomTickSpeed": "<PERSON><PERSON>auš<PERSON> tikšķu ātrums", "gamerule.reducedDebugInfo": "Samazināt atkļūdošanas info", "gamerule.reducedDebugInfo.description": "Ierobežo atkļūdošanas ekrāna saturu.", "gamerule.sendCommandFeedback": "<PERSON><PERSON><PERSON><PERSON><PERSON> komandu atsauksmes", "gamerule.showDeathMessages": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "gamerule.snowAccumulationHeight": "Sniega kārtu augstums", "gamerule.snowAccumulationHeight.description": "Sniega laikā uz zemes veidojas sniega kārta<PERSON>, kas nep<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>du s<PERSON>ā<PERSON> s<PERSON>.", "gamerule.spawnChunkRadius": "Atdzimšanas vietas gabalu rādius<PERSON>", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON> s<PERSON>, kas vien<PERSON><PERSON>r būs i<PERSON><PERSON><PERSON> apk<PERSON>rt virszemes atdzimšanas vietai.", "gamerule.spawnRadius": "Atdzimšanas vietas rādiuss", "gamerule.spawnRadius.description": "Kontrolē izmēru apkārt atdzimšanas punktam kurā spelētāju var atdzimt.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> ģenerēt pasauli", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "TNT sprād<PERSON><PERSON>, da<PERSON>i bloki neiz<PERSON>s to mantas", "gamerule.tntExplosionDropDecay.description": "<PERSON><PERSON><PERSON>, ko rad<PERSON>jis TNT sprādziens, tiek zaud<PERSON> sprādzienā.", "gamerule.universalAnger": "Universālas <PERSON>", "gamerule.universalAnger.description": "Sadusmoti neitr<PERSON><PERSON> radī<PERSON><PERSON> u<PERSON>r<PERSON><PERSON> jeb<PERSON>m blakus eso<PERSON> spēlētājam, nevis tikai spēlētājam, <PERSON><PERSON><PERSON> vi<PERSON> sa<PERSON>. Vislabāk strādā ja forgiveDeadPlayers ir izslēgts.", "gamerule.waterSourceConversion": "Ūdens pārtop avotā", "gamerule.waterSourceConversion.description": "Kad plūstošu ūdeni no divām pusēm ieskauj ūdens avoti, tas pārtop par avotu.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.customized": "<PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "PASTIPRINĀTS", "generator.minecraft.amplified.info": "Piezīme: Tikai prieka pēc! Nepieciešams jaudīgs dators.", "generator.minecraft.debug_all_block_states": "Atkļūdošanas režīms", "generator.minecraft.flat": "<PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "Lielas biomas", "generator.minecraft.normal": "Parasts", "generator.minecraft.single_biome_surface": "Viena bioma", "generator.single_biome_caves": "Alas", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "<PERSON><PERSON><PERSON>d<PERSON><PERSON>, j<PERSON><PERSON>, ka jūsu sniegtā informācija ir precīza un pilnīga, cik jums ir zināms.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON> mums palīdzēs izveidot labi informētu lēmumu.", "gui.abuseReport.discard.content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, tad zaud<PERSON>si šo ziņojumu un savus komentārus.\nVai tiešām vēlies iziet?", "gui.abuseReport.discard.discard": "Iziet un atmest ziņojumu", "gui.abuseReport.discard.draft": "Saglabāt kā melnrakstu", "gui.abuseReport.discard.return": "<PERSON><PERSON><PERSON><PERSON><PERSON> rediģēšanu", "gui.abuseReport.discard.title": "Atmest ziņo<PERSON>mu un komentārus?", "gui.abuseReport.draft.content": "Vai jūs vēlaties turpināt labot iesākto ziņojumu vai to atmest un uzsākt jaunu?", "gui.abuseReport.draft.discard": "Atmest", "gui.abuseReport.draft.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON> rediģēšanu", "gui.abuseReport.draft.quittotitle.content": "Vai vēlaties to turpināt rediģēšanu vai to atmest?", "gui.abuseReport.draft.quittotitle.title": "Jums ir tērz<PERSON><PERSON><PERSON> ziņ<PERSON>, kas tiks zaud<PERSON> ja i<PERSON>t", "gui.abuseReport.draft.title": "Labot tērzēšanas ziņojuma melnrakstu?", "gui.abuseReport.error.title": "Problēma no<PERSON>ūtīt z<PERSON>", "gui.abuseReport.message": "Kur jūs novēroj<PERSON>t slikto uz<PERSON>?\nTas mums palīdzēs jūsu lietas izmeklēša<PERSON>ā.", "gui.abuseReport.more_comments": "<PERSON><PERSON><PERSON><PERSON> aprakstiet kas notika:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kā<PERSON><PERSON><PERSON> vēlaties ziņot par šo vārdu:", "gui.abuseReport.name.reporting": "<PERSON><PERSON><PERSON> \"%s\".", "gui.abuseReport.name.title": "<PERSON><PERSON><PERSON><PERSON> spēlētā<PERSON> vārdu", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON><PERSON><PERSON> jūs par šo z<PERSON>?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par zi<PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkotikas vai alkohols", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON><PERSON> iedroš<PERSON> citus veikt neleg<PERSON> aktivitā<PERSON>, kas saist<PERSON>tas ar narkotiku izmantošanu vai alkohola lietošanu nepilngadīgām personām.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON><PERSON> seksu<PERSON> vai <PERSON>au<PERSON>prā<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON>āds runā par vai citādi vairo nepiedienī<PERSON> u<PERSON>, ies<PERSON><PERSON><PERSON> bērn<PERSON>.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vai ne<PERSON>tiesa informācija", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON><PERSON> bojā cita reputā<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> ka ir kāds cits, vai dalās ar nepatiesu informācuiju ar mērķi izmantot vai malcināt citus.", "gui.abuseReport.reason.description": "Apraksts:", "gui.abuseReport.reason.false_reporting": "Nepatiesa <PERSON>", "gui.abuseReport.reason.generic": "<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> paziņot par viņiem", "gui.abuseReport.reason.generic.description": "<PERSON>i viņi kaitina / viņi ir izdar<PERSON><PERSON><PERSON> kaut ko, kas man nepat<PERSON>k.", "gui.abuseReport.reason.harassment_or_bullying": "Uzmākšanās vai agresija", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON><PERSON>, uz<PERSON><PERSON><PERSON><PERSON> vai iebiedē tevi vai kādu citu. Tas iekļauj kad kāds atkārtoti mēģina sazināties ar jums vai kādu citu, bez iepriekšējas piekrišanas kā arī privātas informācijas izpaušanu par jums vai kādu citu, bez piekrišanas.", "gui.abuseReport.reason.hate_speech": "<PERSON>ida runa", "gui.abuseReport.reason.hate_speech.description": "Kāds uzbrūk tev vai citam spēlētājam, pamatojoties uz viņu identitāti, <PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON>, rasi vai seksu<PERSON>ti.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON><PERSON> traumēt citus", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON><PERSON> draud nodar<PERSON>t traumas tev vai kādam citam īstajā dzīvē.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Nevien<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> intī<PERSON> attēli", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON> runā par, dal<PERSON>s ar vai citādi izplata privātus un intīmus attēlus.", "gui.abuseReport.reason.self_harm_or_suicide": "<PERSON><PERSON> traum<PERSON>na vai pašnāvība", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON><PERSON> draud traumēt sevi īstajā dzīvē vai runā par sevis traumēšanu īstajā dzīvē.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON><PERSON><PERSON><PERSON>, kurām ir grafisks raksturs saistībā ar seksuā<PERSON><PERSON>m da<PERSON>, dzimumorgāniem un seksuālu vardarbību.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terorisms vai vardarbīgs ekstrēmisms", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON>ā<PERSON> runā par, veicina, vai draud veikt terora aktus vai vardarbīgu ekstrēmismu politisku, reliģiozu, ideoloģisku vai citu iemeslu dēļ.", "gui.abuseReport.reason.title": "Izvēlēties ziņojuma kategoriju", "gui.abuseReport.report_sent_msg": "Esam veiksmīgi saņēmuši jūsu ziņojumu. Paldies!\n\n<PERSON><PERSON><PERSON> komanda to p<PERSON><PERSON><PERSON><PERSON><PERSON>, cik drīz vien iespējams.", "gui.abuseReport.select_reason": "Izvēlieties ziņojuma kategoriju", "gui.abuseReport.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>siniet komentāru", "gui.abuseReport.send.error_message": "<PERSON><PERSON>, s<PERSON><PERSON><PERSON> ziņojumu: '%s'", "gui.abuseReport.send.generic_error": "<PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON> tavu zi<PERSON>.", "gui.abuseReport.send.http_error": "<PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON> tavu zi<PERSON>.", "gui.abuseReport.send.json_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tika konstat<PERSON>ts bojāts saturs.", "gui.abuseReport.send.no_reason": "Lū<PERSON>zu izvēlieties ziņojuma kategoriju", "gui.abuseReport.send.not_attested": "Lai nosūtītu ziņo<PERSON><PERSON> lūdzu i<PERSON>lasi informāciju augstāk un veic atzīmi", "gui.abuseReport.send.service_unavailable": "<PERSON>evar sasniegt Ļaunprātīgas i<PERSON><PERSON><PERSON> ziņ<PERSON> pakalpojumu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka esi pieslēdzies internetam un mēģini vēlreiz.", "gui.abuseReport.sending.title": "Tavs ziņojums tiek sūtīts...", "gui.abuseReport.sent.title": "Ziņoju<PERSON> nosūtīts", "gui.abuseReport.skin.title": "Ziņot par spēlētāja izskatu", "gui.abuseReport.title": "Ziņot par spēl<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zi<PERSON>", "gui.abuseReport.type.name": "Spēlēt<PERSON><PERSON> vā<PERSON>", "gui.abuseReport.type.skin": "Spēlētāja izskats", "gui.acknowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.advancements": "Sasniegumi", "gui.all": "Visas", "gui.back": "Atpakaļ", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON> v<PERSON>k š<PERSON>ā saitē: %s", "gui.banned.description.permanent": "<PERSON><PERSON>s konts ir neatgriezeniski slē<PERSON>, kas <PERSON><PERSON><PERSON>, ka tu nevarēsi spēlēt tiešsaistē vai pievienoties Realms.", "gui.banned.description.reason": "<PERSON>esen sa<PERSON>m ziņojumu par sliktu tava konta uzvedību. Mūsu moderatori ir izskatījuši šo lietu un identificējuši to kā %s, kas ir pretrunā ar Minecraft Kopienas Standartiem.", "gui.banned.description.reason_id": "Kods: %s", "gui.banned.description.reason_id_message": "Kods: %s - %s", "gui.banned.description.temporary": "%s Līdz tam laikam tu nevarēsi spēlēt tiešsaistē vai pievienoties Realms.", "gui.banned.description.temporary.duration": "Tavs konts ir īslaicīgi apturēts un tiks atjaunots pēc %s.", "gui.banned.description.unknownreason": "<PERSON><PERSON>n sa<PERSON>m ziņojumu par sliktu tava konta uzvedību. Mūsu moderatori ir izskatījuši šo lietu un identificējuši, ka tā ir pretrunā ar Minecraft Kopienas Standartiem.", "gui.banned.name.description": "<PERSON><PERSON>su pašreizējais vārds - \"%s\" - pārkāpj mūsu Kopienas Standartus. Varat spēlēt viena spēlēt<PERSON><PERSON> spēli, ta<PERSON><PERSON>, lai spēlētu <PERSON>, būs j<PERSON><PERSON><PERSON> vārds.\n\nUzziniet vairāk vai iesniedziet lietas pārskati, izmantojot šo saiti: %s", "gui.banned.name.title": "<PERSON><PERSON><PERSON> nav at<PERSON><PERSON><PERSON>ēlē<PERSON><PERSON><PERSON>", "gui.banned.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON>ās par citu personu vai informācijas apmaiņa, lai i<PERSON>tu vai maldinātu citus", "gui.banned.reason.drugs": "Atsauces uz neleg<PERSON>ām narkotikām", "gui.banned.reason.extreme_violence_or_gore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vardar<PERSON> vai asiņ<PERSON>as reālās d<PERSON> att<PERSON>", "gui.banned.reason.false_reporting": "Pārm<PERSON>r<PERSON>gi nepatiesi vai neprecīzi ziņ<PERSON>ju<PERSON>", "gui.banned.reason.fraud": "Krāpnieciska satura iegūšana vai i<PERSON>šana", "gui.banned.reason.generic_violation": "<PERSON><PERSON><PERSON> standartu pā<PERSON>", "gui.banned.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas tiek lietota vērst<PERSON>, ka<PERSON><PERSON><PERSON><PERSON> veidā", "gui.banned.reason.hate_speech": "Naida runa vai diskriminācija", "gui.banned.reason.hate_terrorism_notorious_figure": "Atsauces uz naida grupām, teroristu organizācijām vai bēdīgi slavenām personām", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON>lūks nodarīt reālu kaitējumu personām vai īpa<PERSON>umam", "gui.banned.reason.nudity_or_pornography": "Netikla vai pornogrāfiska materiāla attē<PERSON>", "gui.banned.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON><PERSON> rakstura tēmas vai saturs", "gui.banned.reason.spam_or_advertising": "Surogātpasts vai reklamēšana", "gui.banned.skin.description": "<PERSON><PERSON><PERSON> pašreizējā skins pārkāpj mūsu Kopienas Standartus. <PERSON><PERSON><PERSON> joprojām varat spēlēt ar noklusējuma skinu vai izvēlēties jaunu.\n\nUzziniet vairāk vai iesniedziet lietas pārskati, izmantojot šo saiti: %s", "gui.banned.skin.title": "Skins nav atļauts", "gui.banned.title.permanent": "<PERSON>nts ir neatgriezeniski slēgts", "gui.banned.title.temporary": "<PERSON><PERSON> ir <PERSON><PERSON><PERSON> a<PERSON>ur<PERSON>", "gui.cancel": "Atcelt", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON> mums palīdzēs izveidot labi informētu lēmumu.", "gui.chatReport.discard.content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, tad zaud<PERSON>si šo ziņojumu un savus komentārus.\nVai tiešām vēlies iziet?", "gui.chatReport.discard.discard": "Iziet un atmest ziņojumu", "gui.chatReport.discard.draft": "Saglabāt kā melnrakstu", "gui.chatReport.discard.return": "<PERSON><PERSON><PERSON><PERSON><PERSON> rediģēt", "gui.chatReport.discard.title": "Atmest ziņo<PERSON>mu un komentārus?", "gui.chatReport.draft.content": "Vai jūs vēlaties turpināt labot iesākto ziņojumu vai to atmest un uzsākt jaunu?", "gui.chatReport.draft.discard": "Atmest", "gui.chatReport.draft.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON> rediģēšanu", "gui.chatReport.draft.quittotitle.content": "Vai vēlaties to turpināt rediģēšanu vai to atmest?", "gui.chatReport.draft.quittotitle.title": "Jums ir tērz<PERSON><PERSON><PERSON> ziņ<PERSON>, kas tiks zaud<PERSON> ja i<PERSON>t", "gui.chatReport.draft.title": "Labot tērzēšanas ziņojuma melnrakstu?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON><PERSON>, kas notika:", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON><PERSON><PERSON> tu par šo ziņo?", "gui.chatReport.read_info": "Uzzināt par zi<PERSON>", "gui.chatReport.report_sent_msg": "Esam veiksmīgi saņēmuši tavu ziņojumu. Paldies!\n\n<PERSON><PERSON><PERSON> komanda to pārskatīs cik drīz vien iespējams.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ziņ<PERSON>, par kurām ziņot", "gui.chatReport.select_reason": "Izvēlēties ziņošanas kategoriju", "gui.chatReport.selected_chat": "%s tē<PERSON><PERSON><PERSON><PERSON> ziņa(s) Izvēlētas ziņ<PERSON>", "gui.chatReport.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>siniet komentāru", "gui.chatReport.send.no_reason": "Lūdzu izvēlies ziņošanas kategoriju", "gui.chatReport.send.no_reported_messages": "L<PERSON><PERSON><PERSON> izvēlies vismaz vienu ziņu, par kuru ziņot", "gui.chatReport.send.too_many_messages": "Tiek mēģināts iek<PERSON><PERSON> pārk daudz ziņo<PERSON><PERSON> atskaitē", "gui.chatReport.title": "Ziņot par spēl<PERSON><PERSON><PERSON><PERSON>", "gui.chatSelection.context": "<PERSON><PERSON><PERSON><PERSON>, kas apt<PERSON> šo izvēli tiks i<PERSON>, lai piedāvātu plašāku konte<PERSON>tu", "gui.chatSelection.fold": "%s ziņ<PERSON><PERSON><PERSON> slēpti", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s pievie<PERSON><PERSON><PERSON><PERSON>", "gui.chatSelection.message.narrate": "%s teica: %s %s", "gui.chatSelection.selected": "%s/%s ziņa(s) izvēlētas", "gui.chatSelection.title": "Izvēlē<PERSON> tērz<PERSON><PERSON><PERSON> ziņ<PERSON>, par kurām ziņot", "gui.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Ko<PERSON>ēt saiti starpliktuvē", "gui.days": "%s diena(s)", "gui.done": "Gatavs", "gui.down": "Uz leju", "gui.entity_tooltip.type": "Tips: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Atraidīti %s faili", "gui.fileDropFailure.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>us", "gui.hours": "%s stunda(s)", "gui.loadingMinecraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>craft", "gui.minutes": "%s minūte(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s poga", "gui.narrate.editBox": "%s rediģēt lauciņu: %s", "gui.narrate.slider": "%s slīdnis", "gui.narrate.tab": "%s tab", "gui.no": "Nē", "gui.none": "Neviens", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Atveriet pārskatu direktoriju", "gui.proceed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "<PERSON><PERSON>, lai rā<PERSON><PERSON><PERSON>", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Meklēt...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> visu", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "Rāda izveidojamo", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON>", "gui.report_to_server": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.blocking_hint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>u", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ā nav bloķētu spēlētāju", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON><PERSON><PERSON>šanā nav paslēptu spēlētāju", "gui.socialInteractions.hidden_in_chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ziņojumi no %s būs slēpti", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.narration.hide": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ņ<PERSON> no %s", "gui.socialInteractions.narration.report": "Ziņot par spēlētāju %s", "gui.socialInteractions.narration.show": "<PERSON><PERSON><PERSON><PERSON><PERSON> no %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar tādu vārdu nav atrasts", "gui.socialInteractions.search_hint": "Meklēt...", "gui.socialInteractions.server_label.multiple": "%s - %s spēlē<PERSON>āji", "gui.socialInteractions.server_label.single": "%s - %s spēlētājs", "gui.socialInteractions.show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.shown_in_chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ziņas no %s tiks rādītas", "gui.socialInteractions.status_blocked": "Bloķētie", "gui.socialInteractions.status_blocked_offline": "Bloķēts - bezsaistē", "gui.socialInteractions.status_hidden": "Slēptie", "gui.socialInteractions.status_hidden_offline": "Slēpts - bezsaist<PERSON>", "gui.socialInteractions.status_offline": "Bezsaistē", "gui.socialInteractions.tab_all": "Visi", "gui.socialInteractions.tab_blocked": "Bloķēts", "gui.socialInteractions.tab_hidden": "Slēpts", "gui.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "Ziņot par spēl<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> serivss nav pieejams", "gui.socialInteractions.tooltip.report.no_messages": "Nav ziņojamu ziņojumu no spēlētāja %s", "gui.socialInteractions.tooltip.report.not_reportable": "Par <PERSON>o spēl<PERSON><PERSON><PERSON><PERSON> nevar zi<PERSON>, jo viņa tērzē<PERSON> ziņojumus nevar pārbaud<PERSON>t š<PERSON>ī", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.stats": "Statistika", "gui.toMenu": "Atpakaļ uz <PERSON>u sarakstu", "gui.toRealms": "Atpakaļ uz Realms sarakstu", "gui.toTitle": "Atpakaļ uz sākuma ekrānu", "gui.toWorld": "Atpakaļ uz pasauļu sarakstu", "gui.togglable_slot": "Spied lai at<PERSON><PERSON><PERSON><PERSON> slotu", "gui.up": "<PERSON><PERSON> augšu", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Jā", "hanging_sign.edit": "Rediģēt iek<PERSON><PERSON>s z<PERSON> z<PERSON>", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Sauciens", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Ilgas", "inventory.binSlot": "Iznīcin<PERSON><PERSON> p<PERSON>š<PERSON>u", "inventory.hotbarInfo": "Saglabā rīkjoslu ar %1$s+%2$s", "inventory.hotbarSaved": "Priekšmetu rīkjos<PERSON> saglabāta (atjaunot izmantojot %1$s+%2$s)", "item.canBreak": "Var sasist:", "item.canPlace": "Var novietot uz:", "item.canUse.unknown": "<PERSON><PERSON>inā<PERSON>", "item.color": "Krāsa: %s", "item.components": "%s komponente(s)", "item.disabled": "Atspējota lieta", "item.durability": "Izturība: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Akācijas laiva", "item.minecraft.acacia_chest_boat": "Akācijas laiva ar lādi", "item.minecraft.allay_spawn_egg": "Allaja radī<PERSON> ola", "item.minecraft.amethyst_shard": "Ameti<PERSON>", "item.minecraft.angler_pottery_shard": "Makšķernieka keramikas lauska", "item.minecraft.angler_pottery_sherd": "Makšķernieka keramikas šķemba", "item.minecraft.apple": "Ābols", "item.minecraft.archer_pottery_shard": "Strēlnieka keramikas lauska", "item.minecraft.archer_pottery_sherd": "Strēlnieka keramikas šķemba", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> ola", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON> stat<PERSON>s", "item.minecraft.arms_up_pottery_shard": "Paceltu roku keramikas lauska", "item.minecraft.arms_up_pottery_sherd": "Paceltu roku keramikas šķemba", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "<PERSON>is ar a<PERSON>", "item.minecraft.axolotl_spawn_egg": "<PERSON>ks<PERSON><PERSON>a rad<PERSON> ola", "item.minecraft.baked_potato": "<PERSON><PERSON> katrupelis", "item.minecraft.bamboo_chest_raft": "Bambusa plosts ar lādi", "item.minecraft.bamboo_raft": "Bambusa plosts", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.bee_spawn_egg": "Bites radī<PERSON> ola", "item.minecraft.beef": "<PERSON><PERSON><PERSON> ga<PERSON>a", "item.minecraft.beetroot": "Biete", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Bērza laiva", "item.minecraft.birch_chest_boat": "Bērza laiva ar lādi", "item.minecraft.black_bundle": "<PERSON><PERSON> saišķis", "item.minecraft.black_dye": "<PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON><PERSON> kera<PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.blaze_powder": "<PERSON><PERSON> pulveris", "item.minecraft.blaze_rod": "<PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Liesmekļ<PERSON> ola", "item.minecraft.blue_bundle": "Zils saišķis", "item.minecraft.blue_dye": "<PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> ola", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Skr<PERSON><PERSON><PERSON> bru<PERSON> a<PERSON>", "item.minecraft.bone": "<PERSON><PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON> milti", "item.minecraft.book": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Robotas apmales karoga raksts", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "Bļoda", "item.minecraft.bread": "<PERSON><PERSON>", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brewer_pottery_shard": "Brūvnieka keramikas la<PERSON>a", "item.minecraft.brewer_pottery_sherd": "Brūvnieka keramikas šķemba", "item.minecraft.brewing_stand": "Brūvēšanas statīvs", "item.minecraft.brick": "Ķieģelis", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON><PERSON> saišķis", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> ola", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Birste", "item.minecraft.bucket": "Spainis", "item.minecraft.bundle": "Saišķis", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Var saturēt da<PERSON>da veida priekšmetus", "item.minecraft.bundle.full": "Pilns", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kera<PERSON> la<PERSON>a", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.carrot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Burkāns uz kociņa", "item.minecraft.cat_spawn_egg": "Kaķa rad<PERSON><PERSON><PERSON> ola", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "<PERSON><PERSON> r<PERSON> ola", "item.minecraft.chainmail_boots": "Ķēžu zābaki", "item.minecraft.chainmail_chestplate": "Ķēžu bruņas", "item.minecraft.chainmail_helmet": "Ķēžu ķivere", "item.minecraft.chainmail_leggings": "Ķēžu bikses", "item.minecraft.charcoal": "Kokogles", "item.minecraft.cherry_boat": "Ķirša laiva", "item.minecraft.cherry_chest_boat": "Ķirša laiva ar lādi", "item.minecraft.chest_minecart": "Vagons ar lādi", "item.minecraft.chicken": "Jēla vista", "item.minecraft.chicken_spawn_egg": "Vistas radīšanas ola", "item.minecraft.chorus_fruit": "<PERSON><PERSON>", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON> pika", "item.minecraft.clock": "<PERSON><PERSON>ks<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "Piekra<PERSON><PERSON> bru<PERSON> a<PERSON>", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON>", "item.minecraft.cod_spawn_egg": "Mencas rad<PERSON> ola", "item.minecraft.command_block_minecart": "Vagons ar komandu bloku", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Bifšteks", "item.minecraft.cooked_chicken": "Cepta vista", "item.minecraft.cooked_cod": "Cepta menca", "item.minecraft.cooked_mutton": "Cepta aitas gaļa", "item.minecraft.cooked_porkchop": "<PERSON><PERSON> cū<PERSON>a", "item.minecraft.cooked_rabbit": "<PERSON><PERSON> truša gaļa", "item.minecraft.cooked_salmon": "<PERSON>pts lasis", "item.minecraft.cookie": "Cepums", "item.minecraft.copper_ingot": "<PERSON><PERSON> stienis", "item.minecraft.cow_spawn_egg": "Govs <PERSON><PERSON><PERSON>la", "item.minecraft.creaking_spawn_egg": "Čīktoņa rad<PERSON> ola", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON><PERSON> lā<PERSON><PERSON>a ka<PERSON>a r<PERSON>ts", "item.minecraft.creeper_spawn_egg": "Creeper radī<PERSON> ola", "item.minecraft.crossbow": "Arbalets", "item.minecraft.crossbow.projectile": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "Ciāns saišķis", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON> kera<PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON> kerami<PERSON> šķemba", "item.minecraft.dark_oak_boat": "Tumšā ozola laiva", "item.minecraft.dark_oak_chest_boat": "Tumšā ozola laiva ar lādi", "item.minecraft.debug_stick": "Atkļū<PERSON><PERSON><PERSON> kociņ<PERSON>", "item.minecraft.debug_stick.empty": "%s nav rekvizītu", "item.minecraft.debug_stick.select": "izvēlēts \"%s\" (%s)", "item.minecraft.debug_stick.update": "%s uz %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON> cirvis", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON> ķivere", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON> bru<PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON> zobe<PERSON>", "item.minecraft.disc_fragment_5": "Diska fragments", "item.minecraft.disc_fragment_5.desc": "Mūzikas disks - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.donkey_spawn_egg": "Ēzeļa rad<PERSON><PERSON><PERSON> ola", "item.minecraft.dragon_breath": "Pūķa elpa", "item.minecraft.dried_kelp": "Kaltētas brūnaļģes", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON>ī<PERSON><PERSON><PERSON> r<PERSON> ola", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> bru<PERSON> a<PERSON>", "item.minecraft.echo_shard": "Atblass lauska", "item.minecraft.egg": "<PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "Vecākā sarga radīša<PERSON> ola", "item.minecraft.elytra": "Elitra", "item.minecraft.emerald": "Smaragds", "item.minecraft.enchanted_book": "<PERSON><PERSON><PERSON><PERSON><PERSON> grā<PERSON>", "item.minecraft.enchanted_golden_apple": "Uzlabots zelta <PERSON>s", "item.minecraft.end_crystal": "<PERSON> k<PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Ender pūķa r<PERSON><PERSON><PERSON><PERSON> ola", "item.minecraft.ender_eye": "<PERSON><PERSON> acs", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON> ola", "item.minecraft.endermite_spawn_egg": "Endermite radīšanas ola", "item.minecraft.evoker_spawn_egg": "Evokera radīša<PERSON> ola", "item.minecraft.experience_bottle": "Burvestī<PERSON> pudele", "item.minecraft.explorer_pottery_shard": "Pētnieka keramikas la<PERSON>a", "item.minecraft.explorer_pottery_sherd": "Pētnieka keramikas šķemba", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON> br<PERSON>", "item.minecraft.feather": "Spalva", "item.minecraft.fermented_spider_eye": "Fermentēta zirnekļ<PERSON> a<PERSON>", "item.minecraft.field_masoned_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON> lauka ka<PERSON>a raksts", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "Ugunsbumba", "item.minecraft.firework_rocket": "<PERSON><PERSON><PERSON> raķete", "item.minecraft.firework_rocket.flight": "Lidoju<PERSON> ilgums:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "Zils", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON> kr<PERSON> uz", "item.minecraft.firework_star.flicker": "Mirdzums", "item.minecraft.firework_star.gray": "<PERSON>elē<PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "Gaiši zaļš", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Oranž<PERSON>", "item.minecraft.firework_star.pink": "Roz<PERSON>", "item.minecraft.firework_star.purple": "<PERSON>s", "item.minecraft.firework_star.red": "Sarkans", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON><PERSON><PERSON> forma", "item.minecraft.firework_star.shape.burst": "Eksplozija", "item.minecraft.firework_star.shape.creeper": "Creeper forma", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON> bumba", "item.minecraft.firework_star.shape.small_ball": "Maza bumba", "item.minecraft.firework_star.shape.star": "Zvaigznes forma", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "Balts", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "Makšķere", "item.minecraft.flint": "Krams", "item.minecraft.flint_and_steel": "Krams un dzelzs", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON><PERSON>a r<PERSON>", "item.minecraft.flower_pot": "Puķupods", "item.minecraft.fox_spawn_egg": "Lapsas radī<PERSON> ola", "item.minecraft.friend_pottery_shard": "Drauga kera<PERSON> la<PERSON>a", "item.minecraft.friend_pottery_sherd": "Drauga keramikas šķemba", "item.minecraft.frog_spawn_egg": "Vardes radī<PERSON> ola", "item.minecraft.furnace_minecart": "Vagons ar k<PERSON>ni", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ola", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> šķēle", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globuss", "item.minecraft.globe_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>a raksts", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ogas", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kalm<PERSON> rad<PERSON> ola", "item.minecraft.glowstone_dust": "Mirdzakmen<PERSON>", "item.minecraft.goat_horn": "<PERSON><PERSON> taure", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON> rad<PERSON> ola", "item.minecraft.gold_ingot": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> g<PERSON>", "item.minecraft.golden_apple": "<PERSON>elta <PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON> cirvis", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_helmet": "<PERSON>el<PERSON> ķivere", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON> bru<PERSON>", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.golden_shovel": "<PERSON><PERSON><PERSON> l<PERSON>", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON> zobe<PERSON>", "item.minecraft.gray_bundle": "Pelēks saišķis", "item.minecraft.gray_dye": "<PERSON><PERSON>ē<PERSON>", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON> saišķis", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON> ola", "item.minecraft.gunpowder": "Šaujampul<PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>a r<PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON> sirds", "item.minecraft.heart_pottery_shard": "Sirds keramikas lasuka", "item.minecraft.heart_pottery_sherd": "Sirds keramikas šķemba", "item.minecraft.heartbreak_pottery_shard": "Lauztas sirds keramikas lauska", "item.minecraft.heartbreak_pottery_sherd": "Lauztas sirds keramikas šķemba", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON>la", "item.minecraft.honey_bottle": "Medus pudele", "item.minecraft.honeycomb": "<PERSON><PERSON>", "item.minecraft.hopper_minecart": "Vagons ar piltuvi", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON><PERSON>la", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> kera<PERSON>a", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.husk_spawn_egg": "Huska radīšanas ola", "item.minecraft.ink_sac": "<PERSON><PERSON>", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON> cirvis", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "<PERSON>zelzs golema rad<PERSON> ola", "item.minecraft.iron_helmet": "Dzelzs ķivere", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON>zelzs z<PERSON> bru<PERSON>", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "Dzelzs bi<PERSON>es", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON> gabals", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "Dzelzs zobens", "item.minecraft.item_frame": "Priekš<PERSON><PERSON>", "item.minecraft.jungle_boat": "Džungļu koka laiva", "item.minecraft.jungle_chest_boat": "Džungļu koka laiva ar lādi", "item.minecraft.knowledge_book": "<PERSON><PERSON><PERSON><PERSON><PERSON> gr<PERSON>", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON> spainis", "item.minecraft.lead": "Pavada", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON> jaka", "item.minecraft.leather_helmet": "<PERSON><PERSON> cepure", "item.minecraft.leather_horse_armor": "<PERSON><PERSON> bru<PERSON>", "item.minecraft.leather_leggings": "<PERSON><PERSON>", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> saišķis", "item.minecraft.light_blue_dye": "Gaiši zila kr<PERSON>sa", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "G<PERSON><PERSON><PERSON> pelēks saišķis", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ka krāsa", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Gaiši zaļš saišķis", "item.minecraft.lime_dye": "Gaiši zaļa krāsa", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Ilgstošā d<PERSON>ra", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> uguns noturības dzira", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> d<PERSON>ra", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON>ša veselī<PERSON> d<PERSON>ra", "item.minecraft.lingering_potion.effect.infested": "Ilgstoša infestācijas dzira", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>ra", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> lēkšanas dzira", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> le<PERSON> d<PERSON>ra", "item.minecraft.lingering_potion.effect.luck": "īlgstošā veiksmes d<PERSON>ra", "item.minecraft.lingering_potion.effect.mundane": "Inkdienišķa ilgstošā dzira", "item.minecraft.lingering_potion.effect.night_vision": "Ilgstoša nakts redzamības dzira", "item.minecraft.lingering_potion.effect.oozing": "Ilgstoša izplūšanas dzira", "item.minecraft.lingering_potion.effect.poison": "Ilgstoša Indes dzira", "item.minecraft.lingering_potion.effect.regeneration": "Ilgstoša reģenerācijas dzira", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON>gs<PERSON><PERSON><PERSON> lēn<PERSON> k<PERSON> d<PERSON>ra", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> Dzi<PERSON>", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON>ip<PERSON>a Dzira", "item.minecraft.lingering_potion.effect.swiftness": "Ilgs<PERSON>ša <PERSON> Dzira", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Ilgstošā Bruņurupuča Meistara Dzira", "item.minecraft.lingering_potion.effect.water": "Ilgstoša Ūdens Pudele", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> elpošanas dzira", "item.minecraft.lingering_potion.effect.weakness": "Ilgstoša reģenerācijas dzira", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>ra", "item.minecraft.lingering_potion.effect.wind_charged": "Ilgstoša vēja uzlādes dzira", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "Dzīslakmens kompass", "item.minecraft.mace": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON>ks<PERSON><PERSON> saišķis", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.magma_cube_spawn_egg": "Magmas kuba radīšanas ola", "item.minecraft.mangrove_boat": "Mangroves laiva", "item.minecraft.mangrove_chest_boat": "Mangroves laiva ar lādi", "item.minecraft.map": "<PERSON><PERSON><PERSON> karte", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "Arbūza šķēle", "item.minecraft.milk_bucket": "<PERSON><PERSON>", "item.minecraft.minecart": "Vagons", "item.minecraft.miner_pottery_shard": "Ra<PERSON><PERSON><PERSON> kerami<PERSON> lasuka", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Lieta", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON> ka<PERSON>a r<PERSON>", "item.minecraft.mooshroom_spawn_egg": "Mooshroom radīšanas ola", "item.minecraft.mourner_pottery_shard": "Sērotāja kerami<PERSON> la<PERSON>a", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> keramikas šķemba", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Mūzikas plate", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Mūzikas plate", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Mūzikas plate", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> (Mū<PERSON><PERSON> kaste)", "item.minecraft.music_disc_far": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Mūzikas plate", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON> a<PERSON> gaļa", "item.minecraft.name_tag": "Birka", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.nether_brick": "Nether ķieģeļi", "item.minecraft.nether_star": "Nether zva<PERSON>ne", "item.minecraft.nether_wart": "<PERSON><PERSON> k<PERSON>a", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON><PERSON> cirvis", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_helmet": "Nezerīta ķivere", "item.minecraft.netherite_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_ingot": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON><PERSON> zobe<PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Nezerīta <PERSON>", "item.minecraft.oak_boat": "Ozola laiva", "item.minecraft.oak_chest_boat": "Ozola laiva ar lādi", "item.minecraft.ocelot_spawn_egg": "Ocelota radīšanas ola", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON>e", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>l<PERSON>ga", "item.minecraft.orange_bundle": "Oranžs saišķis", "item.minecraft.orange_dye": "Oranža krāsa", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "Glezna", "item.minecraft.pale_oak_boat": "Bālā ozola laiva", "item.minecraft.pale_oak_chest_boat": "Bālā ozola laiva ar lādi", "item.minecraft.panda_spawn_egg": "Pandas rad<PERSON> ola", "item.minecraft.paper": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON> membr<PERSON>a", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON><PERSON>la", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON> r<PERSON>la", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Snuķa karoga raksts", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rad<PERSON> ola", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> radī<PERSON> ola", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>la", "item.minecraft.pink_bundle": "Rozā saišķis", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Nepente", "item.minecraft.pitcher_pod": "Nep<PERSON>s p<PERSON>", "item.minecraft.plenty_pottery_shard": "Pārticības kera<PERSON> la<PERSON>a", "item.minecraft.plenty_pottery_sherd": "Pārticības keramikas šķemba", "item.minecraft.poisonous_potato": "Indīgs kartupelis", "item.minecraft.polar_bear_spawn_egg": "Polārlā<PERSON><PERSON> r<PERSON>la", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON><PERSON> kora auglis", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "Dzira", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "Neizveidojama d<PERSON>ra", "item.minecraft.potion.effect.fire_resistance": "Uguns noturības dzira", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "item.minecraft.potion.effect.healing": "Veselības dzira", "item.minecraft.potion.effect.infested": "Infestācijas dzira", "item.minecraft.potion.effect.invisibility": "Neredzamības dzira", "item.minecraft.potion.effect.leaping": "Augstlēkšanas dzira", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.luck": "Veiksmes dzira", "item.minecraft.potion.effect.mundane": "Ikdienišķa dzira", "item.minecraft.potion.effect.night_vision": "Nakts redzamības dzira", "item.minecraft.potion.effect.oozing": "Izplūšanas dzira", "item.minecraft.potion.effect.poison": "Indes dzira", "item.minecraft.potion.effect.regeneration": "Reģenerācijas dzira", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "item.minecraft.potion.effect.slowness": "Lēnuma d<PERSON>ra", "item.minecraft.potion.effect.strength": "Spēka d<PERSON>ra", "item.minecraft.potion.effect.swiftness": "Ātruma dzira", "item.minecraft.potion.effect.thick": "Bieza dzira", "item.minecraft.potion.effect.turtle_master": "Bruņurupuču Meistara Dzira", "item.minecraft.potion.effect.water": "Ūdens pudele", "item.minecraft.potion.effect.water_breathing": "Zemūdens elpošanas dzira", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "Au<PERSON>nas d<PERSON>", "item.minecraft.potion.effect.wind_charged": "Vēja uzlādes dzira", "item.minecraft.pottery_shard_archer": "Strēlnieka keramikas lauska", "item.minecraft.pottery_shard_arms_up": "Paceltu roku keramikas lauska", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON><PERSON> kera<PERSON> la<PERSON>a", "item.minecraft.pottery_shard_skull": "Galvaskausa keramikas la<PERSON>a", "item.minecraft.powder_snow_bucket": "Drupena s<PERSON>ga spainis", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON><PERSON> kera<PERSON> la<PERSON>a", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON><PERSON> keramikas šķemba", "item.minecraft.pufferfish": "Pūšļ<PERSON> z<PERSON>s", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>", "item.minecraft.pufferfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> ziv<PERSON> radīšanas ola", "item.minecraft.pumpkin_pie": "Ķirbju pīrāgs", "item.minecraft.pumpkin_seeds": "Ķirbju sēklas", "item.minecraft.purple_bundle": "Violets saišķis", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> ga<PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON>la", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Pacēlāja bruņu ornaments", "item.minecraft.ravager_spawn_egg": "Postīt<PERSON><PERSON> r<PERSON> ola", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON>eaps<PERSON><PERSON><PERSON><PERSON><PERSON> zelts", "item.minecraft.raw_iron": "<PERSON>eaps<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Atgū<PERSON><PERSON> kompass", "item.minecraft.red_bundle": "Sarkans saišķis", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Redstone putekļi", "item.minecraft.resin_brick": "Sveķu ķieģelis", "item.minecraft.resin_clump": "Sveķu pika", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> bru<PERSON>", "item.minecraft.rotten_flesh": "Sapuvusi gaļa", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "<PERSON><PERSON>", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON> r<PERSON>la", "item.minecraft.scrape_pottery_sherd": "Sk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.scute": "Čaula", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "Aizsarga bruņu ornaments", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "Veido<PERSON><PERSON><PERSON> bru<PERSON> a<PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> kera<PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.shears": "Šķēres", "item.minecraft.sheep_spawn_egg": "Aitas radīša<PERSON> ola", "item.minecraft.shelter_pottery_shard": "Pat<PERSON><PERSON><PERSON><PERSON> kera<PERSON> la<PERSON>a", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> kera<PERSON> šķemba", "item.minecraft.shield": "Vairogs", "item.minecraft.shield.black": "Melns vairogs", "item.minecraft.shield.blue": "<PERSON><PERSON> vairogs", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>s", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>s", "item.minecraft.shield.gray": "Pelēks vairogs", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON> vairogs", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> vairogs", "item.minecraft.shield.light_gray": "Gaiši pelēks vairogs", "item.minecraft.shield.lime": "Gaiši z<PERSON>ļš vairogs", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>s", "item.minecraft.shield.orange": "Or<PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.shield.purple": "<PERSON><PERSON> vairogs", "item.minecraft.shield.red": "Sarkans vairogs", "item.minecraft.shield.white": "Balts vairogs", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON> vairogs", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> bru<PERSON> a<PERSON>", "item.minecraft.silverfish_spawn_egg": "Sudrabzivs radīšanas ola", "item.minecraft.skeleton_horse_spawn_egg": "Zirga skeleta radīšanas ola", "item.minecraft.skeleton_spawn_egg": "Skeleta radīšanas ola", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Galvaska<PERSON> karoga raksts", "item.minecraft.skull_pottery_shard": "Galvaskausa keramikas la<PERSON>a", "item.minecraft.skull_pottery_sherd": "Galvaskausa keramikas šķemba", "item.minecraft.slime_ball": "<PERSON><PERSON><PERSON><PERSON> bumba", "item.minecraft.slime_spawn_egg": "Gļot<PERSON> rad<PERSON> ola", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.smithing_template.applies_to": "Attiecas uz:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON><PERSON><PERSON> stieni vai k<PERSON>lu", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "<PERSON><PERSON><PERSON> br<PERSON> g<PERSON>lu", "item.minecraft.smithing_template.armor_trim.ingredients": "Stieņi & kristāli", "item.minecraft.smithing_template.ingredients": "Sastāvdaļas:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON><PERSON> dimanta br<PERSON>, i<PERSON><PERSON> vai <PERSON>u", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.smithing_template.upgrade": "Uzlabojums: ", "item.minecraft.sniffer_spawn_egg": "<PERSON>š<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> ola", "item.minecraft.snort_pottery_shard": "Šņaucēja keramikas la<PERSON>a", "item.minecraft.snort_pottery_sherd": "Šņaucēja keramikas šķemba", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Sniega golema radī<PERSON> ola", "item.minecraft.snowball": "Sniega pika", "item.minecraft.spectral_arrow": "Spe<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.spider_eye": "Zirnekļa acs", "item.minecraft.spider_spawn_egg": "Zirnekļa rad<PERSON> ola", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> br<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "Neveikla <PERSON><PERSON><PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>, neizveidojama dzira", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> uguns noturības dzira", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON> infestācijas dzira", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON> augstlēkšanas dzira", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON> veiksmes dzira", "item.minecraft.splash_potion.effect.mundane": "Šļaks<PERSON><PERSON> ikdienišķā dzira", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON> nakts redzamības dzira", "item.minecraft.splash_potion.effect.oozing": "Šļ<PERSON><PERSON><PERSON> izplūšanas dzira", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON> indes dzira", "item.minecraft.splash_potion.effect.regeneration": "Šļaks<PERSON><PERSON> reģenerācijas dzira", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON> lēn<PERSON> k<PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON> lē<PERSON>a dzira", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON><PERSON> spēka dzira", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "item.minecraft.splash_potion.effect.thick": "Bieza šļ<PERSON><PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON><PERSON> bruņurupuču meistara dzira", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> elpošanas dzira", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON> vājuma dzira", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>ra", "item.minecraft.splash_potion.effect.wind_charged": "Šļ<PERSON><PERSON><PERSON> vēja uzlādes dzira", "item.minecraft.spruce_boat": "<PERSON>gles laiva", "item.minecraft.spruce_chest_boat": "Egles laiva ar lādi", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.stick": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON>k<PERSON><PERSON> cirvis", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "Akmens zobens", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.strider_spawn_egg": "Lavas ceļotāja radīšanas ola", "item.minecraft.string": "Aukla", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Aizdomīgs sautējums", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON> ogas", "item.minecraft.tadpole_bucket": "Spainis ar kurkuļ<PERSON>", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> bru<PERSON> a<PERSON>", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.empty": "Neizveidojama a<PERSON>cirsta bulta", "item.minecraft.tipped_arrow.effect.fire_resistance": "Uguns noturības bulta", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Infestācijas bulta", "item.minecraft.tipped_arrow.effect.invisibility": "Nered<PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.leaping": "Augst<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>a", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.night_vision": "Nakts redzam<PERSON> bulta", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.regeneration": "Reģenerācijas bulta", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>a", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.swiftness": "Ātruma bulta", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.turtle_master": "Bruņurupuču Meistara bulta", "item.minecraft.tipped_arrow.effect.water": "Šļakatas bulta", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> bulta", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON><PERSON><PERSON> b<PERSON>", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON>a", "item.minecraft.tnt_minecart": "Vagons ar din<PERSON>", "item.minecraft.torchflower_seeds": "<PERSON><PERSON><PERSON>", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trader_llama_spawn_egg": "Tirgotāja lamas rad<PERSON> ola", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "Tropiskā zivs", "item.minecraft.tropical_fish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>", "item.minecraft.tropical_fish_spawn_egg": "Tropu zivs radī<PERSON> ola", "item.minecraft.turtle_helmet": "Bruņurupuča čaula", "item.minecraft.turtle_scute": "Bruņurupuča čaula", "item.minecraft.turtle_spawn_egg": "Bruņurupuča radīšanas ola", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex bruņu a<PERSON>e", "item.minecraft.vex_spawn_egg": "<PERSON><PERSON><PERSON>la", "item.minecraft.villager_spawn_egg": "Ciema iedzīvotāja radīšanas ola", "item.minecraft.vindicator_spawn_egg": "Vindikatora rad<PERSON> ola", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tirgotāja rad<PERSON> ola", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON> a<PERSON>", "item.minecraft.warden_spawn_egg": "Uzrauga radīšanas ola", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON> sēn<PERSON>te uz nū<PERSON>", "item.minecraft.water_bucket": "Ūdens spainis", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "<PERSON><PERSON>a meklē<PERSON><PERSON><PERSON> bruņu ornaments", "item.minecraft.wheat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_bundle": "Balts saišķis", "item.minecraft.white_dye": "Balta krāsa", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "Savva<PERSON>as bruņu ornaments", "item.minecraft.wind_charge": "<PERSON><PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON><PERSON> r<PERSON>la", "item.minecraft.wither_skeleton_spawn_egg": "Wither skeleta radīšanas ola", "item.minecraft.wither_spawn_egg": "<PERSON>er <PERSON><PERSON><PERSON><PERSON>la", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON> bru<PERSON>", "item.minecraft.wolf_spawn_egg": "Vilka radī<PERSON> ola", "item.minecraft.wooden_axe": "<PERSON><PERSON> cirvis", "item.minecraft.wooden_hoe": "<PERSON><PERSON> ka<PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON> c<PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON> l<PERSON>", "item.minecraft.wooden_sword": "Koka zobens", "item.minecraft.writable_book": "<PERSON><PERSON><PERSON><PERSON> un spalva", "item.minecraft.written_book": "<PERSON>ks<PERSON><PERSON><PERSON> grā<PERSON>", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> saišķis", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON> radī<PERSON> ola", "item.minecraft.zombie_horse_spawn_egg": "Zombija zirga radīšanas ola", "item.minecraft.zombie_spawn_egg": "Zombija radīšanas ola", "item.minecraft.zombie_villager_spawn_egg": "Zombija ciema iedzīvotāja radīšanas ola", "item.minecraft.zombified_piglin_spawn_egg": "Zombificēta piglina rad<PERSON> ola", "item.modifiers.any": "Kad a<PERSON>r<PERSON>kots:", "item.modifiers.armor": "Kad uzvilkts:", "item.modifiers.body": "Kad a<PERSON>r<PERSON>kots:", "item.modifiers.chest": "Kad uz ķermeņa:", "item.modifiers.feet": "Kad uz pēdām:", "item.modifiers.hand": "Kad turēts:", "item.modifiers.head": "Kad uz galvas:", "item.modifiers.legs": "<PERSON><PERSON> k<PERSON>:", "item.modifiers.mainhand": "Kad galven<PERSON> rokā:", "item.modifiers.offhand": "Kad otrā rokā:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s tags(i)", "item.op_block_warning.line1": "Brīdinājums:", "item.op_block_warning.line2": "Šī priekšmeta lietošana var novest pie komandas izpildes", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ja nepārz<PERSON>et saturu!", "item.unbreakable": "Nesalaužams", "itemGroup.buildingBlocks": "Celtniecības bloki", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Amatniecība", "itemGroup.foodAndDrink": "Ēdieni un dzērieni", "itemGroup.functional": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "itemGroup.hotbar": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.ingredients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.inventory": "Izd<PERSON><PERSON><PERSON><PERSON><PERSON> inventārs", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON> bloki", "itemGroup.op": "Operatora aprīkojums", "itemGroup.redstone": "Redstone bloki", "itemGroup.search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Rīki & utilītas", "item_modifier.unknown": "Nezināms lietas modificētājs: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> par:", "jigsaw_block.generate": "Ģenerēt", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "<PERSON><PERSON>", "jigsaw_block.joint_label": "Savienojuma veids:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON><PERSON> puzles gabalus", "jigsaw_block.levels": "Līmeņi: %s", "jigsaw_block.name": "Nosaukums:", "jigsaw_block.placement_priority": "Novietojuma prioritāte:", "jigsaw_block.placement_priority.tooltip": "Kad <PERSON>is mozaīkbloks savienojas pie gavala, <PERSON><PERSON> ir secība, kād<PERSON> šis gabals tiek apstrād<PERSON>, lai izveidotu savienojumus plašākā struktūrā.\n\nGabali tiks apstrādāti dilstošā prioritātes secībā ar ievietošanas secību saraujot saites.", "jigsaw_block.pool": "Mērķa Kopa:", "jigsaw_block.selection_priority": "<PERSON>zv<PERSON>les prioritāte:", "jigsaw_block.selection_priority.tooltip": "Kad vecāka gabals tiek apstrād<PERSON><PERSON> savie<PERSON>, <PERSON><PERSON> <PERSON>r sec<PERSON>, kādā šis mozaīkbloks mēģina izveidot savienojumu ar savu mērķa daļu.\n\nMozaīka tiks apstrādāta dilstošā prioritātes secībā ar nejaušas secības saraušanas saitēm.", "jigsaw_block.target": "Mērķa nosaukums:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (Mū<PERSON><PERSON> kaste)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Sasniegumi", "key.attack": "Uzbrukt/iznī<PERSON>t", "key.back": "<PERSON><PERSON>", "key.categories.creative": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.gameplay": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.inventory": "Inventārs", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "Kustība", "key.categories.multiplayer": "Daudzspēlētā<PERSON>", "key.categories.ui": "<PERSON><PERSON><PERSON><PERSON> interfeiss", "key.chat": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "key.command": "<PERSON><PERSON><PERSON><PERSON> komandlogu", "key.drop": "Nomest izvēlēto p<PERSON>kšmetu", "key.forward": "Iet uz priekšu", "key.fullscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON> pilnekr<PERSON>", "key.hotbar.1": "Rīkjoslas slots 1", "key.hotbar.2": "Rīkjoslas slots 2", "key.hotbar.3": "Rīkjoslas slots 3", "key.hotbar.4": "Rīkjoslas slots 4", "key.hotbar.5": "Rīkjoslas slots 5", "key.hotbar.6": "Rīkjoslas slots 6", "key.hotbar.7": "Rīkjoslas slots 7", "key.hotbar.8": "Rīkjoslas slots 8", "key.hotbar.9": "Rīkjoslas slots 9", "key.inventory": "Atvērt/aizvērt inventāru", "key.jump": "<PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Keypad 0", "key.keyboard.keypad.1": "Keypad 1", "key.keyboard.keypad.2": "Keypad 2", "key.keyboard.keypad.3": "Keypad 3", "key.keyboard.keypad.4": "Keypad 4", "key.keyboard.keypad.5": "Keypad 5", "key.keyboard.keypad.6": "Keypad 6", "key.keyboard.keypad.7": "Keypad 7", "key.keyboard.keypad.8": "Keypad 8", "key.keyboard.keypad.9": "Keypad 9", "key.keyboard.keypad.add": "Keypad +", "key.keyboard.keypad.decimal": "Keypad decimālzīme", "key.keyboard.keypad.divide": "Keypad /", "key.keyboard.keypad.enter": "Keypad Enter", "key.keyboard.keypad.equal": "Keypad =", "key.keyboard.keypad.multiply": "Keypad *", "key.keyboard.keypad.subtract": "Keypad -", "key.keyboard.left": "<PERSON><PERSON><PERSON>", "key.keyboard.left.alt": "Kreisais Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Kreisais Ctrl", "key.keyboard.left.shift": "Kreisais Shi<PERSON>", "key.keyboard.left.win": "<PERSON><PERSON><PERSON>", "key.keyboard.menu": "Izvēlne", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "<PERSON><PERSON>", "key.keyboard.right.alt": "Labais Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Labais Ctrl", "key.keyboard.right.shift": "Labais Shift", "key.keyboard.right.win": "<PERSON><PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Atstarpe", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Nav piesaist<PERSON>ts", "key.keyboard.up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.world.1": "Pasaule 1", "key.keyboard.world.2": "Pasaule 2", "key.left": "<PERSON>et s<PERSON>iski pa kreisi", "key.loadToolbarActivator": "Rīkjoslas ielādēšanas aktivators", "key.mouse": "Poga %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON> k<PERSON> poga", "key.mouse.middle": "<PERSON><PERSON><PERSON> vid<PERSON> poga", "key.mouse.right": "<PERSON><PERSON><PERSON> poga", "key.pickItem": "Paņemt bloku", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "Quick Actions", "key.right": "<PERSON>et sāniski pa labi", "key.saveToolbarActivator": "Rīkjoslas saglabāšanas aktivators", "key.screenshot": "Veikt ekrānuzņēmumu", "key.smoothCamera": "<PERSON><PERSON><PERSON><PERSON><PERSON> kinematogrāfisko skatu", "key.sneak": "Lavīties", "key.socialInteractions": "<PERSON><PERSON><PERSON><PERSON>", "key.spectatorOutlines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (skatītāji)", "key.sprint": "Sk<PERSON>t", "key.swapOffhand": "A<PERSON><PERSON><PERSON>t p<PERSON>kšmetu ar otru roku", "key.togglePerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skatu", "key.use": "Izmantot priekšmetu/novietot bloku", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "<PERSON><PERSON><PERSON>", "known_server_link.community_guidelines": "<PERSON><PERSON><PERSON> vadlīnijas", "known_server_link.feedback": "Atsauksme", "known_server_link.forums": "Forumi", "known_server_link.news": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.report_bug": "<PERSON><PERSON><PERSON><PERSON> par <PERSON>a k<PERSON>", "known_server_link.status": "Statuss", "known_server_link.support": "Atbalsts", "known_server_link.website": "Tīmekļa vietne", "lanServer.otherPlayers": "Uzstādijumi citiem spēlētājiem", "lanServer.port": "Porta numurs", "lanServer.port.invalid": "Nederīgs ports.\nAtstājiet rediģēšanas lauku tukšu vai ievadiet skaitli no 1024 līdz 65535.", "lanServer.port.invalid.new": "Nederīgs ports.\nAtstājiet rediģēšanas lauku tukšu vai ievadiet skaitli no %s līdz %s.", "lanServer.port.unavailable": "Ports nav pieejams.\nAtstājiet rediģēšanas lauku tukšu vai ievadiet citu skaitli no 1024 līdz 65535.", "lanServer.port.unavailable.new": "Ports nav pieejams.\nAtstājiet rediģēšanas lauku tukšu vai ievadiet citu skaitli no %s līdz %s.", "lanServer.scanning": "<PERSON><PERSON><PERSON><PERSON> spēles lokālajā tīklā", "lanServer.start": "Sākt LAN pasauli", "lanServer.title": "LAN pasaule", "language.code": "lav_LV", "language.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language.region": "Latvija", "lectern.take_book": "<PERSON><PERSON><PERSON><PERSON> grāmatu", "loading.progress": "%s%%", "mco.account.privacy.info": "<PERSON><PERSON><PERSON> vairāk par Mojang un privātuma noteikumiem", "mco.account.privacy.info.button": "Lasīt vairāk par VDAR (GDPR)", "mco.account.privacy.information": "<PERSON><PERSON><PERSON><PERSON> note<PERSON> pro<PERSON>, lai palīdzētu aizsargāt bērnus un viņu privātumu, tostarp ievērot Bērnu tiešsaistes privātuma aizsardzības likumu (COPPA) un Vispārīgo datu aizsardz<PERSON> regulu (VDAR/GDPR).\n\nLai piekļūtu savam Realms kontam, iespējams, jums būs jāsaņem vecāku piekrišana.", "mco.account.privacyinfo": "Mojang uzstāda noteiktas procedūras lai palīdzētu aizsargāt bērnus un viņu privātumu ieskaitot pakļaušanos ar Bērnu Tiešsaistes Privātuma Aizsardzības Aktu (COPPA) un Vispārīgās Datu Aizsardzības Regulu. (VDAR)\n\nPirms piekļūt savam Realms kontam, jums var būt nepieciešams iegūt vecāku piekrišanu.\n\nJa jums ir vecāks Minecraft konts (jūs pieslēdzaties ar jūsu lietotājvārdu), jums jāp<PERSON>rceļ konts uz Mojang konta lai piekļūtu Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontu", "mco.activity.noactivity": "Nav aktivitātes pēdējā(s) %s dienā(s)", "mco.activity.title": "Spēlētāju aktivitāte", "mco.backup.button.download": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "mco.backup.button.reset": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.backup.button.restore": "At<PERSON>uno<PERSON>", "mco.backup.button.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry": "Rezerves kopija (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paka", "mco.backup.entry.gameDifficulty": "<PERSON>rū<PERSON><PERSON><PERSON> pak<PERSON>", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Spēles servera versija", "mco.backup.entry.name": "Nosa<PERSON>ms", "mco.backup.entry.seed": "Sēkla", "mco.backup.entry.templateName": "Šablona no<PERSON>ukums", "mco.backup.entry.undefined": "Nedefinētas izmaiņas", "mco.backup.entry.uploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.worldType": "Pasaules veids", "mco.backup.generate.world": "Ģenerēt pasauli", "mco.backup.info.title": "Izmaiņas kop<PERSON> pēdējā<PERSON> rezerves kopijas", "mco.backup.narration": "Rezerves kopija no %s", "mco.backup.nobackups": "Šim <PERSON> pašlaik nav nevienu kopiju.", "mco.backup.restoring": "Atgriež valstību", "mco.backup.unknown": "NEZINĀMS", "mco.brokenworld.download": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "<PERSON><PERSON><PERSON><PERSON> atiesaistiet vai atlasiet citu pasauli.", "mco.brokenworld.message.line2": "<PERSON><PERSON><PERSON> varat arī le<PERSON>lādēt mapi uz vienspēlētāja rezīmu.", "mco.brokenworld.minigame.title": "Šī mini-sp<PERSON><PERSON> vairs netiek atbalstīta", "mco.brokenworld.nonowner.error": "<PERSON><PERSON><PERSON><PERSON> uzgaidiet kamēr Valstības <PERSON>pašnieks atiestata pasauli", "mco.brokenworld.nonowner.title": "Pasaule ir novecojusi", "mco.brokenworld.play": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.title": "<PERSON><PERSON><PERSON> pašreizējā pasaulē vairs netiek atbalstīta", "mco.client.incompatible.msg.line1": "Jūsu klients nav saderīgs ar Realms.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> jaunāko Minecraft versiju.", "mco.client.incompatible.msg.line3": "Realms nav saderīgs ar testa versijām.", "mco.client.incompatible.title": "Nesaderīgs klients!", "mco.client.outdated.stable.version": "<PERSON><PERSON><PERSON> klienta versija (%s) nav saderīga ar Realms.\n\n<PERSON><PERSON><PERSON><PERSON> izmantojiet jaunāko Minecraft versiju.", "mco.client.unsupported.snapshot.version": "<PERSON><PERSON><PERSON> klienta versija (%s) nav saderīga ar Realms.\n\nRealms nav pieejams šajā snapshot versijā.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.downgrade.description": "<PERSON>ī pasaule pēdējo reizi tika spēlēta versijā %s; jūs izman<PERSON>t versiju %s. Pasaules versijas pazemin<PERSON> var izrais<PERSON>t korupciju - mēs nevar<PERSON> garan<PERSON>, ka tā ielādēsies vai darbosies.\n\n<PERSON><PERSON><PERSON> pasaules dublējums tiks saglabāts sadaļā \"Pasaules rezerves kopijas\". <PERSON><PERSON> <PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON>, atjaunojiet savu pasauli.", "mco.compatibility.incompatible.popup.title": "Nesaderīga versija", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON><PERSON>, kurai mēģini pievienoties ir nesaderīga ar versiju, kuru tu i<PERSON>.", "mco.compatibility.incompatible.series.popup.message": "<PERSON><PERSON> pasaule pēdējo reizi tika spēlēta versijā %s; tu izamnto versiju %s.\n\n<PERSON> s<PERSON>rijas nav saderīgas. Jauna pasaules versija ir nepieciešama lai spēlētu šajā versijā.", "mco.compatibility.unverifiable.message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pasaule pēdējo reizi tika spēlēta, ne<PERSON><PERSON><PERSON> pārbaudīt. Ja pasaule tiek jaunināta vai pazemin<PERSON>ta, dublējums tiks automātiski izveidots un saglabāts sadaļā \"Pasaules rezerves kopijas\".", "mco.compatibility.unverifiable.title": "<PERSON><PERSON><PERSON><PERSON> nav pārb<PERSON><PERSON>", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Šī pasaule pēdējo reizi tika spēlēta versijā %s; tu izamnto versiju %s.\n\n<PERSON><PERSON><PERSON> kopijas pasaule tiks saglabāta zem \"Pasaules rezerves kopijas\". <PERSON><PERSON>, l<PERSON><PERSON><PERSON> atjauno<PERSON>et savu pasauli.", "mco.compatibility.upgrade.friend.description": "Šī pasaule pēdējo reizi tika spēlēta %s versijā; jūs <PERSON> %s versiju.\n\nPasaules dublējums tiks saglabāts zem ''Pasauļu Dublējumi''.\n\nŠī realm īpašnieks var atjaunot pasauli, ja vajadzīgs.", "mco.compatibility.upgrade.title": "Vai tiešām vēlies jaunināt savu pasauli?", "mco.configure.current.minigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laika j<PERSON>", "mco.configure.world.backup": "Pasaules rezerves kopijas", "mco.configure.world.buttons.activity": "Spēlētāju aktivitāte", "mco.configure.world.buttons.close": "Aizvērt realm", "mco.configure.world.buttons.delete": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "Gatavs", "mco.configure.world.buttons.edit": "Iestatījumi", "mco.configure.world.buttons.invite": "Uzaicināt Spelētaju", "mco.configure.world.buttons.moreoptions": "Vairāk iestatījumu", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Atvērt realm", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Iestatījumi", "mco.configure.world.buttons.subscription": "Abonements", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>ēli", "mco.configure.world.close.question.line1": "Tava realm nebūs <PERSON>ama.", "mco.configure.world.close.question.line2": "Vai tiešām vēlaties turpināt?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Notiek realm aizvēršana...", "mco.configure.world.commandBlocks": "<PERSON><PERSON><PERSON> bloki", "mco.configure.world.delete.button": "Izdzēst Realm", "mco.configure.world.delete.question.line1": "<PERSON><PERSON><PERSON> būs neatgriezeniski izdzēsta", "mco.configure.world.delete.question.line2": "Vai tiešām vēlaties turpināt?", "mco.configure.world.description": "Realma apraksts", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON><PERSON> ir at<PERSON>, jo jū<PERSON> pa<PERSON>re<PERSON><PERSON>j<PERSON> pasaule ir piedzīvojums", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON><PERSON> ir <PERSON><PERSON>, jo jū<PERSON> pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasaule ir pieredze", "mco.configure.world.edit.subscreen.inspiration": "<PERSON><PERSON><PERSON> ir <PERSON><PERSON>, jo jū<PERSON> pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasaule ir ied<PERSON>ma", "mco.configure.world.forceGameMode": "Piespiest spēles re<PERSON>", "mco.configure.world.invite.narration": "Tev ir %s jauns(i) ielūgums(i)", "mco.configure.world.invite.profile.name": "<PERSON><PERSON><PERSON>", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Uzaicināts(i) (%)", "mco.configure.world.invites.normal.tooltip": "Parasts lietotājs", "mco.configure.world.invites.ops.tooltip": "Operators", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Ja tu pamet<PERSON>si <PERSON>, <PERSON> to <PERSON><PERSON>, ja vien tevi neuzaicin<PERSON> vēlreiz", "mco.configure.world.leave.question.line2": "Vai tiešām vēlaties turpināt?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "mco.configure.world.minigame": "Pašreiz: %s", "mco.configure.world.name": "<PERSON><PERSON>", "mco.configure.world.opening": "Atver Realm...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar norā<PERSON><PERSON>to vārdu neek<PERSON>", "mco.configure.world.players.inviting": "Uzaicina spēlētāju...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Jūsu pasaule tiks reģenerēta un jūsu tagadējā pasaule zudīs", "mco.configure.world.reset.question.line2": "Vai tiešām vēlaties turpināt?", "mco.configure.world.resourcepack.question": "Jums vajadzīga pielāgota resursu paka, lai spēlētu šo realm\n\nVai jūs velaties to nolādēt un spēlēt?", "mco.configure.world.resourcepack.question.line1": "Šai realm ir nepieciešama resursu paka", "mco.configure.world.resourcepack.question.line2": "Vai vēlaties to leju<PERSON>lādēt un spēlēt?", "mco.configure.world.restore.download.question.line1": "Pasaule tiks lejupielādēta un pievienota jūsu vienspēlētāja pasaulēm.", "mco.configure.world.restore.download.question.line2": "Vai vēlaties turpināt?", "mco.configure.world.restore.question.line1": "<PERSON>ū<PERSON> pasaule tiks atjaunota uz '%s' (%s) datumu", "mco.configure.world.restore.question.line2": "Vai tiešām vēlaties turpināt?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Iestatījumi", "mco.configure.world.slot": "Pasaule %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Jūsu realm tiks pārslēgta uz citu pasauli", "mco.configure.world.slot.switch.question.line2": "Vai tiešām vēlaties turpināt?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.configure.world.slot.tooltip.active": "Pievienoties", "mco.configure.world.slot.tooltip.minigame": "Pārslēgties uz mini-spēli", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Radīt NPCs", "mco.configure.world.spawnProtection": "Atdzīvināšanas punkta aizsardzība", "mco.configure.world.spawn_toggle.message": "Atslēdzot šo i<PERSON>ēju, tiks NOŅEMTAS VISAS entītijas ar šo tipu", "mco.configure.world.spawn_toggle.message.npc": "Atslēdzot šo i<PERSON>, tiks NOŅEMTAS VISAS entītijas ar šo tipu, pie<PERSON><PERSON><PERSON>, ciemata i<PERSON>ji", "mco.configure.world.spawn_toggle.title": "<PERSON><PERSON><PERSON><PERSON>āju<PERSON>!", "mco.configure.world.status": "Statuss", "mco.configure.world.subscription.day": "diena", "mco.configure.world.subscription.days": "dienas", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>u", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON><PERSON><PERSON> par vienu dienu", "mco.configure.world.subscription.month": "mēnesis", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Automātis<PERSON> at<PERSON> pēc", "mco.configure.world.subscription.recurring.info": "Realma abonementā veiktās <PERSON>, kā sarau<PERSON>nas laiks vai periodisko norēiķinu izs<PERSON><PERSON>g<PERSON>, netiks atspulguļota līdz nākamo rēķinu datumiem.", "mco.configure.world.subscription.remaining.days": "%1$s diena(s)", "mco.configure.world.subscription.remaining.months": "%1$s mēnesis(ši)", "mco.configure.world.subscription.remaining.months.days": "%1$s mēnesis(ši), %2$s diena(s)", "mco.configure.world.subscription.start": "<PERSON><PERSON><PERSON><PERSON> datums", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON><PERSON><PERSON><PERSON> laiks", "mco.configure.world.subscription.title": "Abonēšanas Info", "mco.configure.world.subscription.unknown": "<PERSON><PERSON>inā<PERSON>", "mco.configure.world.switch.slot": "Izveidot p<PERSON>uli", "mco.configure.world.switch.slot.subtitle": "<PERSON><PERSON> pasaule ir tuk<PERSON>, l<PERSON><PERSON><PERSON>, k<PERSON> izveidot Jū<PERSON> jauno pasauli", "mco.configure.world.title": "Konfigurēt realm:", "mco.configure.world.uninvite.player": "Vai tiešām vēlies atsaukt uzaicinājumu '%s'?", "mco.configure.world.uninvite.question": "Vai tiešām vēlies atsaukt uzaicinājumu", "mco.configure.worlds.title": "Pasaules", "mco.connect.authorizing": "Pieslēdzas...", "mco.connect.connecting": "Veido savie<PERSON>mu ar realm...", "mco.connect.failed": "Neizdevās izveidot savienojumu ar realm", "mco.connect.region": "Server region: %s", "mco.connect.success": "Gatavs", "mco.create.world": "Izveidot", "mco.create.world.error": "Ir j<PERSON><PERSON>vada nosaukums!", "mco.create.world.failed": "Neizdevās iz<PERSON>idot pasauli!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON> p<PERSON>...", "mco.create.world.skip": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "Izvēlies kuru pasauli izmantot Jūsu jaunajā Realm", "mco.create.world.wait": "Izveido realm...", "mco.download.cancelled": "Leju<PERSON><PERSON><PERSON><PERSON>", "mco.download.confirmation.line1": "Pasaule ko jūs le<PERSON>t ir lielāka kā %s", "mco.download.confirmation.line2": "<PERSON><PERSON><PERSON>t augš<PERSON>ielādēt šo pasauli uz Realm vēlreiz", "mco.download.confirmation.oversized": "<PERSON><PERSON><PERSON>, ko mēģinat nolādēt ir lielāka par %s\n\n<PERSON><PERSON><PERSON>t atkal augšuppielādēt šo pasauli savā realm", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.extracting": "Izvelk failus", "mco.download.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.resourcePack.fail": "<PERSON>eizde<PERSON><PERSON><PERSON> le<PERSON>t resursu paku!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jau<PERSON> p<PERSON>", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON><PERSON>, mēģiniet restartēt Minecraft", "mco.error.invalid.session.title": "Nederīga se<PERSON>ja", "mco.errorMessage.6001": "Klients novecojis", "mco.errorMessage.6002": "Pakalpojumu s<PERSON>g<PERSON> noteikumi nav apstiprināti", "mco.errorMessage.6003": "Lejupielādes limits ir sasniegts", "mco.errorMessage.6004": "Augšupielādes limits ir sasniegts", "mco.errorMessage.6005": "Pa<PERSON><PERSON> sl<PERSON>a", "mco.errorMessage.6006": "Pasaule ir novecojusi", "mco.errorMessage.6007": "Lietotājs pā<PERSON>k da<PERSON>z<PERSON>s Realms", "mco.errorMessage.6008": "Nederīgs Realm nosaukums", "mco.errorMessage.6009": "Nederīgs Realm apraksts", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON><PERSON>, lūd<PERSON> mēģiniet atkal vēlāk.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON><PERSON>: ", "mco.errorMessage.initialize.failed": "Neizdevās inicializ<PERSON>t Realm", "mco.errorMessage.noDetails": "<PERSON><PERSON><PERSON><PERSON> informācija nav sniegta", "mco.errorMessage.realmsService": "<PERSON><PERSON><PERSON><PERSON> (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Neizdevās pieslēgties Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>, tika sa<PERSON> atbilde: %s", "mco.errorMessage.retry": "Mēģināt darb<PERSON>bu vēlreiz", "mco.errorMessage.serviceBusy": "Realms šobrīd ir a<PERSON>.\nLūdzu mēģiniet pieslēgties savai Realm atkārtoti, pēc pāris minūtēm.", "mco.gui.button": "Poga", "mco.gui.ok": "<PERSON><PERSON>", "mco.info": "Informācija!", "mco.invited.player.narration": "Uzaicināja spēlētāju %s", "mco.invites.button.accept": "<PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "Nav ienākošu uzaicin<PERSON>mu!", "mco.invites.pending": "<PERSON><PERSON><PERSON>(Uzaicinājumi)!", "mco.invites.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.changeButton": "Izvēlēties citu Mini Spēli", "mco.minigame.world.info.line1": "Tas uz laiku nomainīs jūsu pasauli ar mini spēli!", "mco.minigame.world.info.line2": "Vēlāk jūs varat atgriezties uz jūsu oriģinālo pasauli neko ne<PERSON>.", "mco.minigame.world.noSelection": "<PERSON><PERSON><PERSON><PERSON>, ve<PERSON>et atlasi", "mco.minigame.world.restore": "Beidzam Mini Spēli...", "mco.minigame.world.restore.question.line1": "<PERSON> spēle beig<PERSON>, un jūsu Realm tiks atjaunota.", "mco.minigame.world.restore.question.line2": "Vai tiešām vēlaties turpināt?", "mco.minigame.world.selected": "Izvēlētā mini spēle:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasauli...", "mco.minigame.world.startButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Startē Mini Spēli...", "mco.minigame.world.stopButton": "Beigt Mini Spēli", "mco.minigame.world.switch.new": "Izvēlēties citu mini-spēli?", "mco.minigame.world.switch.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mini-spēli", "mco.minigame.world.title": "Pārslēgt Realm uz mini-spēli", "mco.news": "Realms jaunumi", "mco.notification.dismiss": "Atmest", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tag<PERSON>", "mco.notification.transferSubscription.message": "Java Realms abonoments tiek pārvietots uz Microsoft Store. Neļauj abonomentam beigties!\nPārsūtiet tūlīt un saņemiet Realms 30 dienas bez maksas.\nDodies uz profilu vietnē minecraft.net, lai pārsūtītu savu abonementu.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON><PERSON><PERSON> saiti", "mco.notification.visitUrl.message.default": "<PERSON><PERSON><PERSON><PERSON> saiti zem<PERSON>k", "mco.onlinePlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "J<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Piedzīvojumi", "mco.reset.world.experience": "Pier<PERSON><PERSON>", "mco.reset.world.generate": "<PERSON><PERSON><PERSON> pasaule", "mco.reset.world.inspiration": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.resetting.screen.title": "Atiestata pasauli...", "mco.reset.world.seed": "Sēkla (pēc izvēles)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.reset.world.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.reset.world.warning": "Tas neatgriezeniski izdzēsīs jūsu realm!", "mco.selectServer.buy": "Nopirkt Realm!", "mco.selectServer.close": "Aizvērt", "mco.selectServer.closed": "Aizvērta realm", "mco.selectServer.closeserver": "Aizvērt realm", "mco.selectServer.configure": "Konfigurēt Realm", "mco.selectServer.configureRealm": "Konfigurēt realm", "mco.selectServer.create": "Izveidot Realm", "mco.selectServer.create.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru pasauli iekļaut savā jaunajā realm", "mco.selectServer.expired": "Beigusies realm", "mco.selectServer.expiredList": "<PERSON><PERSON><PERSON> abonementa derīguma termi<PERSON> ir be<PERSON>s", "mco.selectServer.expiredRenew": "At<PERSON>uno<PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "<PERSON><PERSON><PERSON> izmēģinājuma periods ir beidzies", "mco.selectServer.expires.day": "Beidzas pēc 1 dienas", "mco.selectServer.expires.days": "Beidzas %s dienās", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>", "mco.selectServer.leave": "Pamest realm", "mco.selectServer.loading": "Ielādē Realms sarakstu", "mco.selectServer.mapOnlySupportedForVersion": "Šo karti neatbalsta %s", "mco.selectServer.minigame": "Mini-spēle:", "mco.selectServer.minigameName": "Mini-spēle: %s", "mco.selectServer.minigameNotSupportedInVersion": "Nevar spēlēt šo mini-spēli %s", "mco.selectServer.noRealms": "Šķiet, ka jums nav Realm. Pievienojiet Realm, lai spēlētu kopā ar draugiem.", "mco.selectServer.note": "Piezīme:", "mco.selectServer.open": "Atvērta Realm", "mco.selectServer.openserver": "Atvērt realm", "mco.selectServer.play": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms ir drošs un vienkārš<PERSON> veids, k<PERSON> <PERSON>z<PERSON><PERSON>t Minecraft līdz desmit draugiem vienlaikus.\nTas atbalsta mini-spēles un dažādas pasaules! Tikai ī<PERSON>šniekam ir jāmaksā par Realms.", "mco.selectServer.purchase": "Pievienot Realm", "mco.selectServer.trial": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> izmēģinājumu!", "mco.selectServer.uninitialized": "Noklikšķiniet, lai izveidotu Realm!", "mco.snapshot.createSnapshotPopup.text": "<PERSON><PERSON>s g<PERSON> izveidot bezmaksas Snapshot Realm, kas tiks sapārota ar jūsu maksas Realms abonementu. <PERSON><PERSON> jaunā Snapshot Realm būs pieejama tik ilgi, kamēr būs aktīvs maksas abonements. Jūsu apmaksātā Realm netiks ietekmēta.", "mco.snapshot.createSnapshotPopup.title": "Izveidot testa versijas Realm?", "mco.snapshot.creating": "Izveido testa versijas Realm...", "mco.snapshot.description": "Sapārots ar \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Tev jāizmanto versija %s, lai pievienos šai Realm", "mco.snapshot.friendsRealm.upgrade": "%s nepieciešams atjaunināt Realm, pirms jūs varas spēlēt no šīs versijas", "mco.snapshot.paired": "<PERSON><PERSON> testa versijas Realm ir sapārota ar \"%s\"", "mco.snapshot.parent.tooltip": "<PERSON><PERSON><PERSON> versiju, lai s<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.snapshot.start": "Uzsākt bezmaksas testa versija<PERSON> Realm", "mco.snapshot.subscription.info": "<PERSON><PERSON> <PERSON>r <PERSON>napshot Realm, kas ir sapā<PERSON>a ar jūsu Realm '%s' abonomentu. Tā būs aktīva tik ilgi, cik jūsu maksas Realm.", "mco.snapshot.tooltip": "<PERSON><PERSON><PERSON>hot <PERSON>s, lai gūtu ne<PERSON>lu ieskatu gaidāmaj<PERSON>s Minecraft versijās, kur<PERSON><PERSON> varētu būt iekļautas jaunas funkcijas un citas izmaiņas. \n\nJ<PERSON>s varat atrast savas parastās Realms spēles izlaiduma versijā.", "mco.snapshotRealmsPopup.message": "Realms tagad ir pieejams testa versijās, <PERSON>ākot no Snapshot 23w41a. Katrs Realms abonoments nāk ar bezmaksas testa versijas Realm, kas ir attalīts no jūsu parastās Java Realm!", "mco.snapshotRealmsPopup.title": "Realms tagad ir pieejams testa versijās", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.publisher": "<PERSON>zdevē<PERSON><PERSON>", "mco.template.button.select": "Izvēlēties", "mco.template.button.trailer": "Rek<PERSON>āmka<PERSON><PERSON>", "mco.template.default.name": "<PERSON><PERSON><PERSON> veidne", "mco.template.info.tooltip": "<PERSON>zde<PERSON><PERSON><PERSON>", "mco.template.name": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.select.failure": "<PERSON><PERSON><PERSON>m saņ<PERSON>t šīs kategorijas satura sarakstu.\nPārbaudi savu interneta savienojumu, vai mēģini vēlāk.", "mco.template.select.narrate.authors": "Autori: %s", "mco.template.select.narrate.version": "versija %s", "mco.template.select.none": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON>ī satura kategorija pašreiz ir tuk<PERSON>. Lū<PERSON>zu pārbaudiet jaunu saturu vē<PERSON><PERSON><PERSON>, vai, ja <PERSON><PERSON><PERSON> esat veidotājs, %s.", "mco.template.select.none.linkTitle": "a<PERSON>ver iespēju iesniegt kaut ko pats", "mco.template.title": "<PERSON><PERSON><PERSON> ve<PERSON>nes", "mco.template.title.minigame": "Mini-spēles", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON>", "mco.terms.buttons.agree": "Piek<PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "Nepiekrītu", "mco.terms.sentence.1": "Es piekrītu Minecraft Realms", "mco.terms.sentence.2": "Pakalpojumu <PERSON><PERSON>", "mco.terms.title": "Realms pakalpojumu s<PERSON><PERSON>", "mco.time.daysAgo": "pirms %1$s dienas(ām)", "mco.time.hoursAgo": "pirms %1$s stundas(ām)", "mco.time.minutesAgo": "pirms %1$s minūtes(ēm)", "mco.time.now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.time.secondsAgo": "pirms %1$s sekundes(ēm)", "mco.trial.message.line1": "Vē<PERSON><PERSON> dabūt pats savu <PERSON>?", "mco.trial.message.line2": "Uzklikšķināt šeit, lai iegūtu vairāk informācijas!", "mco.upload.button.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.upload.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.upload.close.failure": "Nevarēja aizvērt jūsu Realm, lūdzu mēģiniet atkal vēlāk", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Augšupielāde neizdevā<PERSON>! (%s)", "mco.upload.failed.too_big.description": "<PERSON>zv<PERSON><PERSON><PERSON><PERSON><PERSON> pasaule ir pārāk liela. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atļautais izmērs ir %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON><PERSON> p<PERSON>", "mco.upload.hardcore": "<PERSON><PERSON>r aug<PERSON>t nežēlīgās grūt<PERSON> pasauli!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Sagatavo pasaules datus", "mco.upload.select.world.none": "Netika atrasta neviena vienspēlētāja pasaule!", "mco.upload.select.world.subtitle": "Lūzu izvēlaties vienspēlētāja pasauli kuru augšupielādētu", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "mco.upload.size.failure.line1": "'%s' ir pār<PERSON>k <PERSON>!", "mco.upload.size.failure.line2": "<PERSON>s ir %s. <PERSON><PERSON><PERSON> atļautais lielums ir %s.", "mco.upload.uploading": "Augšupielād<PERSON> '%s'", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON> p<PERSON>", "mco.version": "Versija: %s", "mco.warning": "<PERSON><PERSON><PERSON><PERSON>āju<PERSON>!", "mco.worldSlot.minigame": "Mini-spēle", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Atvienoties", "menu.feedback": "Atsauksmes...", "menu.feedback.title": "Atsauk<PERSON><PERSON>", "menu.game": "Spēles izvēlne", "menu.modded": " (Modificēts)", "menu.multiplayer": "Daudzspēlētā<PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Opcijas...", "menu.paused": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ur<PERSON>ta", "menu.playdemo": "Spēlēt demo pasauli", "menu.playerReporting": "Ziņot par Sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Sagatavo rašanās vietu: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "<PERSON><PERSON><PERSON>", "menu.reportBugs": "<PERSON>iņot par kļūdām", "menu.resetdemo": "Atiestatīt demo pasauli", "menu.returnToGame": "Atgriezties spēlē", "menu.returnToMenu": "Saglabāt un iziet uz izvēlni", "menu.savingChunks": "Saglab<PERSON> gabalus", "menu.savingLevel": "Sag<PERSON><PERSON><PERSON> pasauli", "menu.sendFeedback": "<PERSON> atsauksmes", "menu.server_links": "Servera saites...", "menu.server_links.title": "Servera saites", "menu.shareToLan": "Atvērt LAN", "menu.singleplayer": "Vienspēlētāja <PERSON>", "menu.working": "Strādā...", "merchant.deprecated": "Ciema iedzīvotāji atjauno krājumus līdz divām reizēm dienā.", "merchant.level.1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "Kvalificēts", "merchant.level.4": "Eksperts", "merchant.level.5": "Meistars", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Spied %1$s lai nokāptu", "multiplayer.applyingPack": "Instalē resursu paku", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Autentifikācijas serveri nestrādā. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz vēlāk, piedodiet!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Jūs esat bloķēts šajā serverī", "multiplayer.disconnect.banned.expiration": "\nBloķēšana tiks noņemta %s", "multiplayer.disconnect.banned.reason": "Jūs esat bloķēts šajā serverī.\nIemesls: %s", "multiplayer.disconnect.banned_ip.expiration": "\nTavs aizliegums tiks noņemts %s", "multiplayer.disconnect.banned_ip.reason": "Jūsu IP adrese ir bloķēta šajā serverī.\nIemesls: %s", "multiplayer.disconnect.chat_validation_failed": "Tērzēšanas ziņas apstiprināšanas kļūda", "multiplayer.disconnect.duplicate_login": "Jūs esat pieteicies no citas vietas", "multiplayer.disconnect.expired_public_key": "Profila publiskās atslēgas derīguma termi<PERSON>. Pārbaudiet vai Jūsu sistēmas laiks ir sinhronizēts un mēģiniet pārlādēt spēli.", "multiplayer.disconnect.flying": "<PERSON><PERSON><PERSON><PERSON>ī nav iespējota", "multiplayer.disconnect.generic": "Atvienots", "multiplayer.disconnect.idling": "<PERSON><PERSON><PERSON> pār<PERSON>k ilgi esat bijis neaktīvs!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> t<PERSON>", "multiplayer.disconnect.incompatible": "Nesaderīga klienta versija! Lūd<PERSON> izmantojiet %s", "multiplayer.disconnect.invalid_entity_attacked": "Mēģina uzbrukt nederīgai entītijai", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON> paketi", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON><PERSON> spē<PERSON><PERSON><PERSON><PERSON><PERSON> dati", "multiplayer.disconnect.invalid_player_movement": "Saņem<PERSON> nederīga spēlētāja pārvie<PERSON> pakete", "multiplayer.disconnect.invalid_public_key_signature": "Nederīgs profila publiskās atslēgas paraksts.\nMēģiniet restartēt spēli.", "multiplayer.disconnect.invalid_public_key_signature.new": "Nederīgs paraksts priekš profila publiskās atslēgas. Mēģiniet restartēt jūsu spēli.", "multiplayer.disconnect.invalid_vehicle_movement": "Saņemta nederīga transporta pārvietošanas pakete", "multiplayer.disconnect.ip_banned": "Jūsu IP adrese ir bloķēta šajā serverī", "multiplayer.disconnect.kicked": "Tevi izmeta operators", "multiplayer.disconnect.missing_tags": "No servera saņemta nepilnīga birku kopa.\n<PERSON><PERSON><PERSON><PERSON> sazinies ar servera operatoru.", "multiplayer.disconnect.name_taken": "Jūsu vārds jau tiek i<PERSON>", "multiplayer.disconnect.not_whitelisted": "<PERSON><PERSON><PERSON> neesat šī servera baltajā sarakstā!", "multiplayer.disconnect.out_of_order_chat": "<PERSON>ņ<PERSON><PERSON> ārpus kārtas tērz<PERSON> pakete. Vai tavas sistēmas laiks mainījās?", "multiplayer.disconnect.outdated_client": "Nesaderīga klienta versija! <PERSON><PERSON><PERSON><PERSON>, izmantojiet %s", "multiplayer.disconnect.outdated_server": "Nesaderīga klienta versija! <PERSON><PERSON><PERSON><PERSON>, izmantojiet %s", "multiplayer.disconnect.server_full": "Serveris ir pilns!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON> s<PERSON>", "multiplayer.disconnect.slow_login": "<PERSON><PERSON><PERSON><PERSON><PERSON> pār<PERSON>k ilgs laiks, lai piete<PERSON>", "multiplayer.disconnect.too_many_pending_chats": "Pārāk da<PERSON> tērz<PERSON> ziņoju<PERSON>", "multiplayer.disconnect.transfers_disabled": "<PERSON><PERSON>", "multiplayer.disconnect.unexpected_query_response": "<PERSON><PERSON><PERSON><PERSON><PERSON> piel<PERSON>goti dati no klienta", "multiplayer.disconnect.unsigned_chat": "Saņem<PERSON> tē<PERSON> pakete bez vai ar nepareizu para<PERSON>tu.", "multiplayer.disconnect.unverified_username": "Neizdevās verificēt lietotājvārdu!", "multiplayer.downloadingStats": "Atgūst statistiku...", "multiplayer.downloadingTerrain": "<PERSON><PERSON><PERSON><PERSON><PERSON> reljefu...", "multiplayer.lan.server_found": "Atrasts jauns serveris: %s", "multiplayer.message_not_delivered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pārbaudiet servera žurnālfailus: %s", "multiplayer.player.joined": "%s pievie<PERSON><PERSON><PERSON><PERSON> s<PERSON>i", "multiplayer.player.joined.renamed": "%s (agrāk pazīstams kā %s) pievienojās spēlei", "multiplayer.player.left": "%s izg<PERSON><PERSON> no spēles", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tie<PERSON>sai<PERSON>ē: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON> pie<PERSON>āgota resursu paka", "multiplayer.requiredTexturePrompt.line1": "<PERSON><PERSON> izmantot pielāgotu resursu paku.", "multiplayer.requiredTexturePrompt.line2": "Noraidot šo pielāgoto resursu paku, jūs tik<PERSON>t atvienots no šī servera.", "multiplayer.socialInteractions.not_available": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir tikai pieejama daudz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasaulēs", "multiplayer.status.and_more": "... un %s vairāk ...", "multiplayer.status.cancelled": "Atcelts", "multiplayer.status.cannot_connect": "Nevar izveidot savienojumu ar serveri", "multiplayer.status.cannot_resolve": "<PERSON><PERSON><PERSON> servera vārdu", "multiplayer.status.finished": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Nesaderīga versija!", "multiplayer.status.motd.narration": "Dienas ziņojums: %s", "multiplayer.status.no_connection": "(nav savienojuma)", "multiplayer.status.old": "Vecs", "multiplayer.status.online": "Tiešsaiste", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milisekundes", "multiplayer.status.pinging": "Gaida datus no servera...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s no %s spē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ē", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "<PERSON>a pieprasījums ir apstrād<PERSON>ts", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Saņemts nepieprasīts statuss", "multiplayer.status.version.narration": "Servera versija: %s", "multiplayer.stopSleeping": "<PERSON><PERSON> gultu", "multiplayer.texturePrompt.failure.line1": "Neizdevās u<PERSON>t servera resursu paku", "multiplayer.texturePrompt.failure.line2": "<PERSON><PERSON><PERSON>, kam ne<PERSON>l<PERSON>goti resursi, var darboties ne tā, kā pared<PERSON>ts", "multiplayer.texturePrompt.line1": "Šis serveris iesaka i<PERSON>t pie<PERSON>āgotu tekstūru paku.", "multiplayer.texturePrompt.line2": "Vai vēlaties lejuplādēt un instalēt to automaģiski?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nZiņa no servera:\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>ēl<PERSON>t<PERSON><PERSON> režī<PERSON>", "multiplayer.unsecureserver.toast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas tiek nosūt<PERSON>ti šim <PERSON>im, var būt modificēti un var neatspoguļot orģinālo ziņojumu", "multiplayer.unsecureserver.toast.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ziņ<PERSON> nevar p<PERSON>", "multiplayerWarning.check": "<PERSON><PERSON><PERSON> ner<PERSON><PERSON><PERSON>t šo ek<PERSON>u", "multiplayerWarning.header": "Uzmanību: <PERSON><PERSON><PERSON><PERSON><PERSON> puses tie<PERSON> s<PERSON>le", "multiplayerWarning.message": "Uzmanību: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spēlēšanu piedāvā trešo pušu serveri, kuri nepieder, netiek operēti un neuzrauga Mojang vai Microsoft. Tiešsaistes spēles laik<PERSON>, <PERSON><PERSON><PERSON> varētu būt pakļauts nemodificētām tērzēšanas ziņām vai citam lietotāju ģenerētam saturam, kas varētu nebūt piemērots visiem.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Taust<PERSON><PERSON>š: %s", "narration.button.usage.focused": "Nospiediet Enter, lai aktivizētu", "narration.button.usage.hovered": "Nospiediet kreiso ta<PERSON>iņu, lai aktivizētu", "narration.checkbox": "<PERSON>zv<PERSON><PERSON> rūtiņ<PERSON>: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON><PERSON><PERSON>, lai p<PERSON>lēgtu", "narration.checkbox.usage.hovered": "Nospiediet kreiso ta<PERSON>, lai p<PERSON><PERSON><PERSON>", "narration.component_list.usage": "Nospiediet Tab lai pārietu uz nākamo elementu", "narration.cycle_button.usage.focused": "No<PERSON><PERSON><PERSON>, lai pārslēgtos uz %s", "narration.cycle_button.usage.hovered": "Kreisais klikšķis, lai pārslēgtos uz %s", "narration.edit_box": "Rediģēt lauciņu: %s", "narration.item": "Item: %s", "narration.recipe": "%s recepte", "narration.recipe.usage": "Nospiediet kreiso taustiņu, lai iz<PERSON><PERSON><PERSON><PERSON><PERSON>", "narration.recipe.usage.more": "Nospiediet labo tausti<PERSON>, lai parā<PERSON><PERSON><PERSON> vair<PERSON>k receptes", "narration.selection.usage": "Nospiediet taustiņus augš<PERSON> un lejup, lai pārietu uz citu ierakstu", "narration.slider.usage.focused": "Nospiediet kreiso vai labo bultiņu, lai i<PERSON><PERSON><PERSON><PERSON><PERSON> vērt<PERSON>bu", "narration.slider.usage.hovered": "Vel<PERSON>t slīdni, lai main<PERSON>tu vērt<PERSON>bu", "narration.suggestion": "Izvēlētais ieteikums %s no %s: %s", "narration.suggestion.tooltip": "Izvēlēts ieteikums %s no %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "No<PERSON><PERSON><PERSON>, lai pārietu uz nākamo ieteikumu", "narration.suggestion.usage.cycle.hidable": "<PERSON>pied <PERSON>, lai pārietu uz nā<PERSON>š<PERSON> ieteik<PERSON>, vai <PERSON>, lai atstātu ieteikumus", "narration.suggestion.usage.fill.fixed": "<PERSON>pied <PERSON>, lai i<PERSON><PERSON><PERSON> i<PERSON>u", "narration.suggestion.usage.fill.hidable": "<PERSON>pied <PERSON>, lai i<PERSON><PERSON>tu ieteikumu vai Escape, lai atst<PERSON>tu ieteikumus", "narration.tab_navigation.usage": "Spied <PERSON><PERSON><PERSON> un Ta<PERSON> ta<PERSON>, lai p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> starp cilnēm", "narrator.button.accessibility": "Pie<PERSON>amī<PERSON>", "narrator.button.difficulty_lock": "Grūt<PERSON><PERSON> pak<PERSON> bloķēšana", "narrator.button.difficulty_lock.locked": "Aiz<PERSON>lē<PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Atslēgts", "narrator.button.language": "Valoda", "narrator.controls.bound": "%s ir saistīts ar %s", "narrator.controls.reset": "Atiestatīt %s pogu", "narrator.controls.unbound": "%s nav saistīts", "narrator.joining": "Pievienojas", "narrator.loading": "Ielādē: %s", "narrator.loading.done": "Gatavs", "narrator.position.list": "Izvēlēta saraksta rinda %s no %s", "narrator.position.object_list": "Izvēlēts rindas elements %s no %s", "narrator.position.screen": "Ekrāna elements %s no %s", "narrator.position.tab": "Atlasīta %s cilne no %s", "narrator.ready_to_play": "Gatavs <PERSON>", "narrator.screen.title": "Galvenais e<PERSON>", "narrator.screen.usage": "<PERSON><PERSON><PERSON><PERSON><PERSON> peles kursoru vai taustiņu <PERSON>, lai atlas<PERSON><PERSON>u", "narrator.select": "Izvēlēts: %s", "narrator.select.world": "Izvēlēts %s, pēdē<PERSON><PERSON>iz spēlēts: %s, %s, %s, versija: %s", "narrator.select.world_info": "Izvēlēts %s, pēdējo reizi spēlēts %s, %s", "narrator.toast.disabled": "Diktors izslēgts", "narrator.toast.enabled": "Diktors i<PERSON>lē<PERSON>s", "optimizeWorld.confirm.description": "Tiks mēģināts optimizēt tavu pasauli, <PERSON><PERSON><PERSON><PERSON><PERSON>, ka visi dati tiek glabāti jaunākajā spēļu formātā. Tas var aizņemt ļoti ilgu laiku, atkar<PERSON>bā no jūsu pasaules. Kad tas ir izdar<PERSON>ts, jūsu pasaule ielādēsies ātrāk, taču vairs nebūs saderīga ar vecākām spēles versijām. Vai tiešām vēlaties turpināt?", "optimizeWorld.confirm.proceed": "Izveidojiet dublējumu un optimizējiet", "optimizeWorld.confirm.title": "Optimizēt p<PERSON>", "optimizeWorld.info.converted": "Uzlabotie gabali: %s", "optimizeWorld.info.skipped": "<PERSON><PERSON><PERSON><PERSON><PERSON> gaba<PERSON>: %s", "optimizeWorld.info.total": "Gabali kopā: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "<PERSON><PERSON><PERSON> gabalus...", "optimizeWorld.stage.failed": "Neizdevās! :(", "optimizeWorld.stage.finished": "Pabeidz darbību...", "optimizeWorld.stage.finished.chunks": "Pabeidz gaba<PERSON> j<PERSON>...", "optimizeWorld.stage.finished.entities": "Pabeidz entī<PERSON><PERSON>...", "optimizeWorld.stage.finished.poi": "Pabeidz intereses punk<PERSON>...", "optimizeWorld.stage.upgrading": "<PERSON>ja<PERSON> visus gabalus...", "optimizeWorld.stage.upgrading.chunks": "<PERSON>ja<PERSON> visus gabalus...", "optimizeWorld.stage.upgrading.entities": "Atjauno visas entītijas...", "optimizeWorld.stage.upgrading.poi": "Atjauno visus intereses punktus...", "optimizeWorld.title": "Optimiz<PERSON> pasauli '%s'", "options.accessibility": "Pieejamības iestatījumi...", "options.accessibility.high_contrast": "Augsts kontrasts", "options.accessibility.high_contrast.error.tooltip": "Augsta kontrasta resursu paka nav pieejama", "options.accessibility.high_contrast.tooltip": "Uzlabo lietotāja interfeisa elementu kontrastu", "options.accessibility.high_contrast_block_outline": "Augsta kontrasta bloku kontūras", "options.accessibility.high_contrast_block_outline.tooltip": "Uzlabo mērķbloka bloka kontūras kontrastu.", "options.accessibility.link": "Pieejamības rokasgrāmata", "options.accessibility.menu_background_blurriness": "Izvēlnes fona miglainums", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON> izv<PERSON>u fonu miglainumu", "options.accessibility.narrator_hotkey": "Stā<PERSON><PERSON><PERSON><PERSON><PERSON> karst<PERSON> ta<PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Atļauj ieslēgt un izslēgt stāstītāju ar 'Cmd+B'", "options.accessibility.narrator_hotkey.tooltip": "Atļauj ieslēgt un izslēgt stāstītāju ar 'Ctrl+B'", "options.accessibility.panorama_speed": "<PERSON><PERSON><PERSON><PERSON> riti<PERSON> ātrums", "options.accessibility.text_background": "Teksta fons", "options.accessibility.text_background.chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background.everywhere": "Visur", "options.accessibility.text_background_opacity": "Teksta fona caurspīdīgums", "options.accessibility.title": "Pieejamības iestatījumi...", "options.allowServerListing": "Atļaut uzskaiti serveros", "options.allowServerListing.tooltip": "Serveri var uzskaitīt spēlētājus tiešsaistē kā daļu no to publiskā statusa.\nKad <PERSON><PERSON> izvēle ir izslēgta, tad tavs vārds tādos sarakstos nerādīsies.", "options.ao": "<PERSON><PERSON><PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.ao.off": "<PERSON>z<PERSON>lē<PERSON><PERSON>", "options.attack.crosshair": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attack.hotbar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attackIndicator": "Uzbrukuma indikators", "options.audioDevice": "<PERSON><PERSON><PERSON><PERSON>", "options.audioDevice.default": "<PERSON><PERSON><PERSON><PERSON>", "options.autoJump": "Automātiska <PERSON>", "options.autoSuggestCommands": "<PERSON><PERSON><PERSON>", "options.autosaveIndicator": "Autosaglabāšanas indikators", "options.biomeBlendRadius": "<PERSON><PERSON><PERSON>", "options.biomeBlendRadius.1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Visātrākais)", "options.biomeBlendRadius.11": "11x11 (Ekstrēms)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (Ātrs)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (Augsts)", "options.biomeBlendRadius.9": "9x9 (Ļoti augsts)", "options.chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uzstādijumi...", "options.chat.color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.delay": "Tērzēšanas aizture: %s sekunde(s)", "options.chat.delay_none": "Saraks<PERSON>ās: Nav", "options.chat.height.focused": "Koncentrējies uz augstumu", "options.chat.height.unfocused": "Nefokusēts augstums", "options.chat.line_spacing": "Rindstarpas", "options.chat.links": "Interneta saites", "options.chat.links.prompt": "Piedāv<PERSON>t sa<PERSON>", "options.chat.opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teksta caurspīdīgums", "options.chat.scale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teksta lie<PERSON>", "options.chat.title": "Čata uzstādijumi...", "options.chat.visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Redzams", "options.chat.visibility.hidden": "Slēpts", "options.chat.visibility.system": "<PERSON><PERSON><PERSON> k<PERSON>", "options.chat.width": "Platums", "options.chunks": "%s gabali", "options.clouds.fancy": "Detalizēta", "options.clouds.fast": "<PERSON><PERSON>", "options.controls": "Vadības uzstādijumi...", "options.credits_and_attribution": "<PERSON><PERSON><PERSON><PERSON> & Attiecinājumi...", "options.damageTiltStrength": "Bojāju<PERSON>", "options.damageTiltStrength.tooltip": "<PERSON><PERSON><PERSON>, ko iz<PERSON> i<PERSON>.", "options.darkMojangStudiosBackgroundColor": "Vienkrāsains logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON><PERSON> ielādes ekrāna fonu uz melnu krāsu.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "Kont<PERSON>ē cik daudz tumsas efekts pulsēs, kad tev to iedod uzraugs vai sculk kliedzējs.", "options.difficulty": "<PERSON>rū<PERSON><PERSON><PERSON> pak<PERSON>", "options.difficulty.easy": "Viegla", "options.difficulty.easy.info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet nodara mazāku kaitēju<PERSON>. Izsalkuma josla noplicinās un iztukšo veselību līdz 5 sirsniņām.", "options.difficulty.hard": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.hard.info": "Naidīgās radības rodas un nodara lielāku kaitējumu. Izsalkuma josla noplicinās un iztukšo visu veselību.", "options.difficulty.hardcore": "Nežēlīga", "options.difficulty.normal": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "Na<PERSON><PERSON><PERSON><PERSON><PERSON> radības rodas un nodara standarta kaitējumu. Izsalkuma josla noplicinās un iztukšo veselību līdz pussirsniņai.", "options.difficulty.online": "Servera grūtī<PERSON> pak<PERSON>pe", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Bez naidīgām radībām un rodas tikai dažas neitrālās radības. Izsalkuma j<PERSON>la neiztukšojas un veselība ar laiku at<PERSON>.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "options.directionalAudio.off.tooltip": "Klasiska stereo skaņa", "options.directionalAudio.on.tooltip": "Izmanto HRTF-b<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> ska<PERSON>, lai uz<PERSON>botu 3D skaņas simulāciju. Nepiecišama HTRF saderīga skaņas aparatūra un vislabāk to ir izbaudītt ar austiņām.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.entityDistanceScaling": "Att<PERSON><PERSON> lī<PERSON> b<PERSON>m", "options.entityShadows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.font": "Fonta uzst<PERSON>...", "options.font.title": "<PERSON>ont<PERSON>", "options.forceUnicodeFont": "Unikoda fonts", "options.fov": "<PERSON><PERSON> lauks", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON><PERSON><PERSON>", "options.fovEffectScale": "Redzeslauku efekti", "options.fovEffectScale.tooltip": "Kontrolē cik daudz redzeslauks var mainīties ar spēles efektiem.", "options.framerate": "%s kadri sekundē", "options.framerateLimit": "<PERSON><PERSON><PERSON> kadri sekund<PERSON>", "options.framerateLimit.max": "Neierobežoti", "options.fullscreen": "Pilnekrān<PERSON>", "options.fullscreen.current": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Pilnekr<PERSON><PERSON> izšķirtspēja", "options.fullscreen.unavailable": "Iestatījumi nav pieejami", "options.gamma": "Gaišums", "options.gamma.default": "Noklusējums", "options.gamma.max": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Mir<PERSON><PERSON><PERSON><PERSON><PERSON> ā<PERSON>", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON><PERSON> ātrumu uzlabotiem priekšmetiem.", "options.glintStrength": "Mir<PERSON><PERSON><PERSON><PERSON><PERSON> stiprums", "options.glintStrength.tooltip": "Kontrolē cik caurspīdīga ir uzlaboto priekšmetu mi<PERSON>.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "Pasakaini!", "options.graphics.fabulous.tooltip": "%s grafikas režīms izmanto ekr<PERSON>, lai attēlotu laika<PERSON>t<PERSON>, mākoņus un daļiņu efektus aiz caurspīdīgiem blokiem un ūdens.\nŠis var krietni ietekmēt veiktspēju pārnēsājamajām ierīcēm kā arī 4K ekrāniem.", "options.graphics.fancy": "Detalizēta", "options.graphics.fancy.tooltip": "Glītās grafikas režīms sabalansē veiktspēju un izskatu vairākumam ierīču. Iespējams ka aiz caurspīdīgiem blokiem vai ūdens nebūs redzami laikapstākļi, mākoņi un daļiņu effekti.", "options.graphics.fast": "Ā<PERSON>", "options.graphics.fast.tooltip": "Ātrais grafikas režīms samazina redzamo lietus un sniega apjomu. Kā arī dažādiem blokiem caurspīdīgums būs atslēgts, piem. koku lapām.", "options.graphics.warning.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON> bez <PERSON>", "options.graphics.warning.cancel": "Atgriezt mani atpakaļ", "options.graphics.warning.message": "<PERSON>ūsu grafiskā ierīce nenodrošina %s iespēju.\n\n<PERSON><PERSON>s varat ignorēt šo paziņojumu un turpināt, bet ņemiet vērā ka nodrošinājums jūsu grafikajai ierīcei netiks sniegts, ja turpināsiet izmantot %s.", "options.graphics.warning.renderer": "<PERSON><PERSON><PERSON><PERSON> render<PERSON>tjs: [%s]", "options.graphics.warning.title": "<PERSON>enodr<PERSON><PERSON><PERSON><PERSON><PERSON> grafi<PERSON>", "options.graphics.warning.vendor": "<PERSON><PERSON><PERSON><PERSON> piegādātājs: [%s]", "options.graphics.warning.version": "OpenGL versija: [%s]", "options.guiScale": "GUI izmērs", "options.guiScale.auto": "Automātisks", "options.hidden": "Slēpts", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON><PERSON> zibe<PERSON> z<PERSON>", "options.hideLightningFlashes.tooltip": "Neļauj zibens spērieniem likt debesīm mirgot. Paši zibenspērieni būs <PERSON>.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "options.hideMatchedNames.tooltip": "3. puses serveri var sūtīt tērzēšanas ziņas nestandarta formātos.\n<PERSON><PERSON><PERSON>jo<PERSON> op<PERSON>, slē<PERSON><PERSON> spēlētāji tiks saska<PERSON>oti, pamatojoties uz tērzēšanas sūtītāju vārdiem.", "options.hideSplashTexts": "Slēpt uzplaiksnīju<PERSON> te<PERSON>", "options.hideSplashTexts.tooltip": "Paslēpj dzelteno uzplaiksnījuma tekstu galvenajā izvēlnē.", "options.inactivityFpsLimit": "Samaziniet FPS, kad", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Ierobežo kadru ātrumu līdz 30, ja spēle nesa<PERSON>em nevienu spēlēt<PERSON><PERSON> ievadi ilgāk par minūti. Vēl vairāk ierobežo to līdz 10 pēc vēl 9 minūtēm.", "options.inactivityFpsLimit.minimized": "Minimizēts", "options.inactivityFpsLimit.minimized.tooltip": "Ierobežo kadru <PERSON>u tikai tad, ja sp<PERSON><PERSON> ir minimiz<PERSON>ts.", "options.invertMouse": "Apgriezta pele", "options.japaneseGlyphVariants": "Jap<PERSON>ņ<PERSON> glifu varianti", "options.japaneseGlyphVariants.tooltip": "<PERSON><PERSON><PERSON>ņu CJK rakstzīmju variantus noklusētajā fontā", "options.key.hold": "<PERSON><PERSON><PERSON><PERSON>", "options.key.toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.language": "Valoda...", "options.language.title": "Valoda", "options.languageAccuracyWarning": "(<PERSON><PERSON><PERSON> tuk<PERSON> var neb<PERSON> 100%% akurāti)", "options.languageWarning": "Valodas tulkojums iespējams nebūs 100%% precīzs", "options.mainHand": "G<PERSON><PERSON>ā roka", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "Labā", "options.mipmapLevels": "'<PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON>i", "options.modelPart.cape": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.hat": "Cepure", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Bikšu kreis<PERSON> kāja", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "Labā bikšu kāja", "options.modelPart.right_sleeve": "<PERSON><PERSON>", "options.mouseWheelSensitivity": "Ritin<PERSON><PERSON><PERSON> jut<PERSON>", "options.mouse_settings": "<PERSON><PERSON><PERSON>...", "options.mouse_settings.title": "<PERSON><PERSON><PERSON>", "options.multiplayer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Diktors", "options.narrator.all": "Diktē visu", "options.narrator.chat": "<PERSON><PERSON><PERSON>", "options.narrator.notavailable": "Nav pieejams", "options.narrator.off": "<PERSON>z<PERSON>lē<PERSON><PERSON>", "options.narrator.system": "<PERSON><PERSON><PERSON> si<PERSON>", "options.notifications.display_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laiks", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> laik<PERSON>, cik ilgi visi paziņojumi ir redzami uz ekrānā.", "options.off": "<PERSON>z<PERSON>lē<PERSON><PERSON>", "options.off.composed": "%s IZSLĒGTS", "options.on": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.on.composed": "%s IESLĒGTS", "options.online": "Tiešsaiste...", "options.online.title": "Tiešsaistes <PERSON>", "options.onlyShowSecureChat": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai drošu tē<PERSON>", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai pārb<PERSON><PERSON><PERSON> spēlēt<PERSON><PERSON>, ko ir sūtījis šis spēlētājs un nav modificēti.", "options.operatorItemsTab": "Operatora lietu cilne", "options.particles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "<PERSON><PERSON><PERSON><PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON> gaba<PERSON> būv<PERSON>s", "options.prioritizeChunkUpdates.byPlayer": "Daļē<PERSON> bloķējoši", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Darbības kā bloku novietošana un iznīcināšana pasaules gabalus atjauninās uzreiz.", "options.prioritizeChunkUpdates.nearby": "Pilnībā bloķējoši", "options.prioritizeChunkUpdates.nearby.tooltip": "Tuvumā esošu pasaules gabalu momentāna izveidošana var traucēt spēles veiktspēju, kad bloki tiek novietoti un iznīcināti.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Tuvumā esošie pasaules gabali tiek veidoti paralēlos procesos. Tas var radīt īslaicīgus vizuālus caurumus, kad bloki tiek iznī<PERSON>ti.", "options.rawMouseInput": "<PERSON><PERSON><PERSON><PERSON> ievade", "options.realmsNotifications": "Realms jaunumi un uzaicinājumi", "options.realmsNotifications.tooltip": "Izgūst Realm jaunumus un ielūgumus no sākuma ekrāna un attēlo to ikonas uz Realm pogas.", "options.reducedDebugInfo": "Samazināta atkļūdošanas info", "options.renderClouds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "options.resourcepack": "Resursu pakas...", "options.rotateWithMinecart": "Griezties ar vagoniem", "options.rotateWithMinecart.tooltip": "Vai spēlētāja skatam būtu jāgrie<PERSON> kopā ar vagonu. Pieejams tikai pasaulēs ar iespējotu 'Vagonu u<PERSON>laboju<PERSON>' eksperimentālo i<PERSON>īju<PERSON>.", "options.screenEffectScale": "Kropļošanas efekti", "options.screenEffectScale.tooltip": "Nelabuma un Nether portāla ekrāna kropļojumu efekta stiprums.\nZemākās vērtībās nelabuma efektu aizstāj zaļš ekrāna pārklājs.", "options.sensitivity": "Jūtība", "options.sensitivity.max": "HIPERĀTRUMS!!!", "options.sensitivity.min": "*žāva*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "<PERSON><PERSON><PERSON><PERSON><PERSON> subtitrus", "options.simulationDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "options.skinCustomisation": "Izskata pielāgošana...", "options.skinCustomisation.title": "Izskata pielāgošana", "options.sounds": "Mūzika un skaņas...", "options.sounds.title": "Mūzikas un skaņas iestatījumi", "options.telemetry": "Telemetrijas dati...", "options.telemetry.button": "<PERSON><PERSON>", "options.telemetry.button.tooltip": "\"%s\" iekļauj tikai nepiecie<PERSON> datus.\n\"%s\" iekļ<PERSON>j gan neo<PERSON>li<PERSON><PERSON><PERSON> datus, gan nepie<PERSON><PERSON><PERSON> datus.", "options.telemetry.disabled": "Telemetrija atspējota.", "options.telemetry.state.all": "<PERSON><PERSON>", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON><PERSON>", "options.telemetry.state.none": "Nē", "options.title": "Opcijas", "options.touchscreen": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.video": "Video uzstādijumi...", "options.videoTitle": "Video iestatījumi", "options.viewBobbing": "Skata šūpošanās", "options.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft ir beigusies atmiņa.\n\nTas varētu būt saistīts ar kļūdu spēlē vai Java Virtual Machine nespēju piešķirt pietiekami daudz aymiņas.\n\nSpēle tika pārtraukta, lai novērstu spēles failu bojājumus. Mēs mēģinājām atbrīvot gana daudz atmiņas, lai jūs varētu atgriezties galvenajā izvēlnē un turpināt spēli, taču tas var nedarboties.\n\nLūdzu restartējiet spēli, ja redzat šo paziņojumu atkārtoti.", "outOfMemory.title": "Pietrūkst atmiņas!", "pack.available.title": "<PERSON><PERSON><PERSON>", "pack.copyFailure": "<PERSON><PERSON>zde<PERSON><PERSON><PERSON> kop<PERSON>t resursu pakas", "pack.dropConfirm": "Vai vēlaties pievienot š<PERSON> pakas <PERSON>?", "pack.dropInfo": "Ievelciet failus šajā lo<PERSON> lai pievienotu pakas", "pack.dropRejected.message": "Sekojošie ieraksti nebija derīgas pakas un netika kopētas:\n%s", "pack.dropRejected.title": "Ne-paku i<PERSON>", "pack.folderInfo": "(Ievietojiet resursu paku failus šeit)", "pack.incompatible": "Nesaderīgs", "pack.incompatible.confirm.new": "<PERSON><PERSON> paka tika izveidota jaunākai Minecraft versijai un, iespējams, var nedarboties pareizi.", "pack.incompatible.confirm.old": "<PERSON><PERSON> resursu paka tika veidota vecākai Minecraft versijai, tāpēc tā var nedarboties pareizi.", "pack.incompatible.confirm.title": "Vai esat pārl<PERSON>, ka vēlaties ielādēt šo paku?", "pack.incompatible.new": "(Veidots jaunākai Minecraft versijai)", "pack.incompatible.old": "(Veidots vecākai Minecraft versijai)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "At<PERSON><PERSON>rt Resursu Pak<PERSON>", "pack.selected.title": "<PERSON><PERSON><PERSON>", "pack.source.builtin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.feature": "iespēja", "pack.source.local": "lokāls", "pack.source.server": "serveris", "pack.source.world": "pasaule", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Pagalms", "painting.minecraft.baroque.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.baroque.title": "Baroks", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Buķete", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Aluput<PERSON>", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Pārģērbšanās", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON><PERSON> boss", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Atradums", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Lode", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "P<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Dīķis", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> pr<PERSON>j<PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Saulespuķes", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Izpakots", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Nejaušs variants", "parsing.bool.expected": "Vajadzīgs Būls", "parsing.bool.invalid": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 'true' vai 'false', bet tā vietā rakstīts '%s'", "parsing.double.expected": "Vajadzīgs decimālskaitlis", "parsing.double.invalid": "Nederīgs decimālskaitlis '%s'", "parsing.expected": "Vajadzīgs '%s'", "parsing.float.expected": "Vajadzīgs p<PERSON> s<PERSON>", "parsing.float.invalid": "<PERSON>erī<PERSON> p<PERSON> skai<PERSON> '%s'", "parsing.int.expected": "Vajadzīgs veselais skaitlis", "parsing.int.invalid": "Nederīgs veselais skaitlis '%s'", "parsing.long.expected": "Gaidāms ilgs laiks", "parsing.long.invalid": "Nederīgs garš '%s'", "parsing.quote.escape": "Nederīga kārtība '\\%s' citētā virknē", "parsing.quote.expected.end": "Neaizvērts citēts teksts", "parsing.quote.expected.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai sāktu tekstu", "particle.invalidOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ņ<PERSON> iestatījumus: %s", "particle.notFound": "<PERSON><PERSON><PERSON><PERSON><PERSON>: %s", "permissions.requires.entity": "Šai komandai ir vajadzīgs objekts", "permissions.requires.player": "Šai komandai ir vajadzīgs spēlētājs", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "<PERSON><PERSON>:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Nezināms izteiciens: %s", "quickplay.error.invalid_identifier": "Neizdevā<PERSON> at<PERSON>t pasauli ar norād<PERSON>to identifik<PERSON>u", "quickplay.error.realm_connect": "Neizdevās <PERSON>", "quickplay.error.realm_permission": "Pietrūkst atļaujas lai pievienotos šim Realm serverim", "quickplay.error.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms pašreiz netiek atbalstītas testa versijās", "recipe.notFound": "Nezināma recepte: %s", "recipe.toast.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> recepšu grāmatu", "recipe.toast.title": "Atklātas jaunas receptes!", "record.nowPlaying": "Tagad atskaņo: %s", "recover_world.bug_tracker": "Ziņot par k<PERSON>ūdu", "recover_world.button": "Mēģināt atgūt", "recover_world.done.failed": "Neizdevās atgūt no iepriekšējā stāvokļa.", "recover_world.done.success": "Atgūšana bija ve<PERSON>smīga!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fails", "recover_world.issue.none": "Nav problēmu", "recover_world.message": "Mēģinot lasīt pasaules mapi \"%s\", r<PERSON><PERSON><PERSON> problēmas.\n<PERSON>es<PERSON>ē<PERSON><PERSON>, var atjaunot pasauli no vecāka stāvokļa, vai arī varat ziņot par šo problēmu kļūdu izsekotājā.", "recover_world.no_fallback": "Nav pieejams stā<PERSON>, no kura atgūties", "recover_world.restore": "Mēģināt atjaunot", "recover_world.restoring": "Mēģina atjaunot pasauli...", "recover_world.state_entry": "Stāvoklis no %s: ", "recover_world.state_entry.unknown": "nezināms", "recover_world.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasa<PERSON> kops<PERSON>u", "resourcePack.broken_assets": "ATRASTAS NESADERĪGI RESURSI", "resourcePack.high_contrast.name": "Augsts kontrasts", "resourcePack.load_fail": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>", "resourcePack.programmer_art.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resourcePack.runtime_failure": "Konstatēta resursu pakotnes kļūda", "resourcePack.server.name": "Pasaulei specifiski resursi", "resourcePack.title": "Izvēlēties resursu paku", "resourcePack.vanilla.description": "Noklusētais Minecraft izskats un sajūta", "resourcePack.vanilla.name": "Noklusējums", "resourcepack.downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resursu pakas", "resourcepack.progress": "<PERSON><PERSON><PERSON> le<PERSON> (%s MB)...", "resourcepack.requesting": "<PERSON>zveidot <PERSON>...", "screenshot.failure": "Nevarēja saglabāt e<PERSON>rānuzņēmumu: %s", "screenshot.success": "Saglabāts ekrānuzņēmums kā %s", "selectServer.add": "Pievienot serveri", "selectServer.defaultName": "Minecraft serveris", "selectServer.delete": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "Vai tiešām vēlies dzēst šo serveri?", "selectServer.deleteWarning": "'%s' tiks zaudēta uz mūžu! (Ilgu laiku!)", "selectServer.direct": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(Slēpts)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Pievienoties serverim", "selectWorld.access_failure": "Neizdevā<PERSON> p<PERSON>i", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON>, piem<PERSON>ram /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON><PERSON><PERSON> ke<PERSON> datus", "selectWorld.backupJoinConfirmButton": "Izveidot dublējumu un i<PERSON>ādēt", "selectWorld.backupJoinSkipButton": "<PERSON>s zinu, ko es daru!", "selectWorld.backupQuestion.customized": "Pielāgotas pasaules vairs netiek atbalstītas", "selectWorld.backupQuestion.downgrade": "Pa<PERSON><PERSON> vers<PERSON> p<PERSON> nav atbalstīta", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON><PERSON>, kas <PERSON><PERSON><PERSON> e<PERSON>per<PERSON><PERSON><PERSON>, netiek atbalstī<PERSON>", "selectWorld.backupQuestion.snapshot": "Vai tiešām vēlaties ielādēt šo pasauli?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mēs neatbalstām pielāgotas pasaules šajā Minecraft versijā. Mēs joprojām varam ielādēt šo pasauli un paturēt visu tādu, kāds tas ir, bet jebkāds jauns ģenerēts reljefs netiks pielāgots. Atvainojamies par neērtībām!", "selectWorld.backupWarning.downgrade": "<PERSON>ī pasaule tika pēdējoreiz spēlēta %s versijā; jūs atrodaties %s versijā. Pasaules versijas pazemin<PERSON> var radīt bojā<PERSON><PERSON>, mēs nespējam garantēt ka tā ielādēsies vai strādās. Ja vēlaties turpināt, lūd<PERSON> izveidojiet dublējumu!", "selectWorld.backupWarning.experimental": "<PERSON><PERSON> pasaule i<PERSON>to e<PERSON><PERSON>, kas var pārtaukt darboties jebkurā laikā. Mēs nevaram garantēt kā tie ielādēsies vai strādās. Šeit ir pūķi!", "selectWorld.backupWarning.snapshot": "Šī pasaule pēdējo reizi tika spēlēta %s versijā; jūs pa<PERSON><PERSON>k atrodaties %s versijā. Lū<PERSON>zu izveidojiet rezerves kopiju gadīju<PERSON>m, ja saskarsieties ar pasaules bojājumiem!", "selectWorld.bonusItems": "<PERSON><PERSON> l<PERSON><PERSON>", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "<PERSON><PERSON><PERSON><PERSON>t pā<PERSON>tai!", "selectWorld.conversion.tooltip": "<PERSON><PERSON> pasaule ir jāatver vecākā versijā (piem<PERSON>ram, 1.6.4), lai to varētu droši konvert<PERSON>t", "selectWorld.create": "Izve<PERSON><PERSON> jaunu pasauli", "selectWorld.customizeType": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "<PERSON><PERSON>", "selectWorld.data_read": "<PERSON><PERSON><PERSON>st pasaules datus...", "selectWorld.delete": "Izdzēst", "selectWorld.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "Vai tu tiešām vēlies izdzēst šo pasauli?", "selectWorld.deleteWarning": "'%s' tiks zaudēta uz mūžu! (Ilgu laiku!)", "selectWorld.delete_failure": "Niezdevās i<PERSON> pasauli", "selectWorld.edit": "Rediģēt", "selectWorld.edit.backup": "Izveidot rezerves kopiju", "selectWorld.edit.backupCreated": "Kopija izveidota: %s", "selectWorld.edit.backupFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backupFolder": "Atvērt rezerves kopiju mapi", "selectWorld.edit.backupSize": "izmērs: %s MB", "selectWorld.edit.export_worldgen_settings": "Eksportēt pasaules veidošanas iestatījumus", "selectWorld.edit.export_worldgen_settings.failure": "Eksportē<PERSON><PERSON>", "selectWorld.edit.export_worldgen_settings.success": "Eksportēts", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON><PERSON> pasaules mapi", "selectWorld.edit.optimize": "Optimizēt p<PERSON>", "selectWorld.edit.resetIcon": "Atiestatīt i<PERSON>u", "selectWorld.edit.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.title": "Rediģēt pasauli", "selectWorld.enterName": "<PERSON><PERSON><PERSON>", "selectWorld.enterSeed": "Sēkla pasaules ģeneratoram", "selectWorld.experimental": "Eksperimentāls", "selectWorld.experimental.details": "Detaļ<PERSON>", "selectWorld.experimental.details.entry": "Nepieciešamas eksperimentālas iespējas: %s", "selectWorld.experimental.details.title": "Eksperimentālu iespēju p<PERSON>bas", "selectWorld.experimental.message": "Esi uzmanīgs!\n<PERSON>aj configurācikai ir nepiecieša<PERSON> i<PERSON>, kas vēl tiek izstrād<PERSON>tas. Tava pasaule var avarēt, salūzt vai k<PERSON>, pēc atjauninājumiem nākotnē.", "selectWorld.experimental.title": "Eksperimentālu iespēju brī<PERSON>ju<PERSON>", "selectWorld.experiments": "Eksperimenti", "selectWorld.experiments.info": "Eksperimenti ir potenciāli jaunas funkcijas. <PERSON><PERSON><PERSON>, jo lieta var salūzt. Eksperimentus pēc pasaules izveides nevar i<PERSON>.", "selectWorld.futureworld.error.text": "Kaut kas nogāja greizi mēģinot ielādēt pasauli no jaunākas versijas. <PERSON><PERSON> jau sākotnēji bija riskanta darbība; atvainoja<PERSON>s, ka tā neizdevās.", "selectWorld.futureworld.error.title": "<PERSON>d<PERSON><PERSON>!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Piedzīvojumu", "selectWorld.gameMode.adventure.info": "Tāds pats kā izd<PERSON><PERSON><PERSON><PERSON><PERSON>, bet blokus nevar pievienot vai noņemt.", "selectWorld.gameMode.adventure.line1": "Tāds pats kā izd<PERSON><PERSON><PERSON><PERSON>, ta<PERSON>u blo<PERSON> nevar", "selectWorld.gameMode.adventure.line2": "uzlikt vai noņemt", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, bū<PERSON><PERSON> un izpēti bez ierobežojumiem. Tu vari lidot, tev ir bezgalīgi materiāli un briesnoņi nevar tevi ievainot.", "selectWorld.gameMode.creative.line1": "Neierobežoti resursi, lidošana un", "selectWorld.gameMode.creative.line2": "sasit blokus momentā", "selectWorld.gameMode.hardcore": "Nežēlīgais", "selectWorld.gameMode.hardcore.info": "Izdzīvošanas režīms ieslēgts uz 'gr<PERSON><PERSON>' p<PERSON>. Tu nevarēsi atdzīvoties ja nomirsi.", "selectWorld.gameMode.hardcore.line1": "<PERSON>āds pats kā izd<PERSON><PERSON><PERSON>, slēgts uz augstāko", "selectWorld.gameMode.hardcore.line2": "gr<PERSON><PERSON><PERSON><PERSON> pakāpi un tikai viena dzīvība", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON><PERSON> varat skatīties bet nevarat aiztikt.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON><PERSON> varat skat<PERSON>, bet nevarat aiztikt", "selectWorld.gameMode.survival": "Izd<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "<PERSON>zpē<PERSON> mistisku pasauli kurā tu vari būvēt, vākt, nodarboties ar amatniecību un cīnīties ar briesmoņiem.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON><PERSON><PERSON> resursus, veido lietas, i<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.line2": "<PERSON><PERSON><PERSON>, veselību un izsalkumu", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Importēt iesta<PERSON>īju<PERSON>", "selectWorld.import_worldgen_settings.failure": "Iestatījumu import<PERSON>", "selectWorld.import_worldgen_settings.select_file": "Izvēlē<PERSON> iestatī<PERSON><PERSON> (.json)", "selectWorld.incompatible.description": "Šo pasaule nevar ielādēt šajā versijā.\nTā pēdējo reizi spēlēta versijā %s.", "selectWorld.incompatible.info": "Nesaderīga versija: %s", "selectWorld.incompatible.title": "Nesaderīga versija", "selectWorld.incompatible.tooltip": "<PERSON>o pasauli nevar atvērt, jo tā tika izveidota nesaderīgā versijā.", "selectWorld.incompatible_series": "Izveidots nesaderīgā versijā", "selectWorld.load_folder_access": "Nevarēja <PERSON>, kur saglab<PERSON><PERSON> spēles pasaules!", "selectWorld.loading_list": "<PERSON><PERSON><PERSON><PERSON><PERSON> pasau<PERSON>u sarak<PERSON>u", "selectWorld.locked": "Slēgts cita atvērtā Minecraft loga dēļ", "selectWorld.mapFeatures": "Ģenerēt struktūras", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, kuģu vraki utt.", "selectWorld.mapType": "Pasaules veids", "selectWorld.mapType.normal": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.moreWorldOptions": "Vairāk pasaules opciju...", "selectWorld.newWorld": "<PERSON><PERSON><PERSON> pasaule", "selectWorld.recreate": "Atkārtoti izveidot", "selectWorld.recreate.customized.text": "Pielāgotas pasaules vairs nav atbalstītas šajā Minecraft versijā. Varam mēģināt to izveidot atkārtoti ar to pašu sēklu un rekvizītiem, bet jebkādi reljefa pielāgojumi tiks zaudēti. Atvainojamies par neērtībām!", "selectWorld.recreate.customized.title": "Pielāgotas pasaules vairs netiek atbalstītas", "selectWorld.recreate.error.text": "Kaut kas nogāja greizi mēģinot atkārtoti izveidot pasauli.", "selectWorld.recreate.error.title": "<PERSON>d<PERSON><PERSON>!", "selectWorld.resource_load": "Sagatavo resursus...", "selectWorld.resultFolder": "Tiks sagla<PERSON>ta:", "selectWorld.search": "mek<PERSON><PERSON><PERSON> pasa<PERSON>", "selectWorld.seedInfo": "<PERSON><PERSON><PERSON><PERSON> tuk<PERSON>u neja<PERSON> s<PERSON>klai", "selectWorld.select": "Sp<PERSON><PERSON><PERSON>t izvēlētajā pasaulē", "selectWorld.targetFolder": "Saglabāšanas mape: %s", "selectWorld.title": "Izvēlieties Pasauli", "selectWorld.tooltip.fromNewerVersion1": "Pa<PERSON>ule tika saglabāta jaunākā versijā,", "selectWorld.tooltip.fromNewerVersion2": "pasaules ielā<PERSON><PERSON><PERSON>na varētu radīt problēmas!", "selectWorld.tooltip.snapshot1": "Neaizmirsti izveidot š<PERSON> pasaules kopiju", "selectWorld.tooltip.snapshot2": "pirms tu to ielā<PERSON><PERSON> šajā testa versijā.", "selectWorld.unable_to_load": "<PERSON>eva<PERSON><PERSON><PERSON> pasa<PERSON>", "selectWorld.version": "Versija:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.versionQuestion": "Vai tiešām vēlaties ielādēt šo pasauli?", "selectWorld.versionUnknown": "nezināms", "selectWorld.versionWarning": "<PERSON><PERSON> pasaule pēdējo reizi tika spēlēta %s versijā un palaišana šajā versijā var radīt bojāju<PERSON>!", "selectWorld.warning.deprecated.question": "<PERSON><PERSON><PERSON> iespē<PERSON> ir novecojušas un nākotnē pārtrauks darbību. Vai vēlaties turpināt?", "selectWorld.warning.deprecated.title": "Brīdinājums! Šie uzstādījumi izmanto novecojuša<PERSON> iespējas", "selectWorld.warning.experimental.question": "<PERSON>ie uzstādījumi ir eksperimentāli un kādu duenu var pārtraukt darboties. Vai vēlaties turpināt?", "selectWorld.warning.experimental.title": "Brīdinājums! Šie uzstādījumi izmanto e<PERSON>perimentālas iespē<PERSON>", "selectWorld.warning.lowDiskSpace.description": "<PERSON><PERSON><PERSON> ierīcē nav atlicis daudz vietas.\nJa spēles laikā diskā beigsies vietas jūsu pasaule var tikt sabojāta.", "selectWorld.warning.lowDiskSpace.title": "Br<PERSON>dinājums! Maz vietas diskā!", "selectWorld.world": "Pasaule", "sign.edit": "Rediģēt z<PERSON><PERSON> zi<PERSON>", "sleep.not_possible": "Nekada atpūta nevar palīdz<PERSON>t i<PERSON>st šo nakti", "sleep.players_sleeping": "%s no %s spēlētājam guļ", "sleep.skipping_night": "<PERSON><PERSON><PERSON>", "slot.only_single_allowed": "Atļautai tikai atsevišķi sloti, ieguva '%s'", "slot.unknown": "Nezināms slots '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Apk<PERSON>rtne/vide", "soundCategory.block": "Bloki", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.master": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.music": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.neutral": "Draudz<PERSON><PERSON> radī<PERSON>", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Atskaņotāji/Nošu Bloki", "soundCategory.ui": "UI", "soundCategory.voice": "Balss/runa", "soundCategory.weather": "Laikapstākļi", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "spectatorMenu.root.prompt": "Nospiediet ta<PERSON>iņu, lai izv<PERSON><PERSON><PERSON><PERSON> koman<PERSON>, un to atkal izmantot.", "spectatorMenu.team_teleport": "Teleportēties pie komandas biedra", "spectatorMenu.team_teleport.prompt": "Izvēlies komandu pie kuras teleportēties", "spectatorMenu.teleport": "Teleportēties pie spēlētāja", "spectatorMenu.teleport.prompt": "Izvēlies spēlētāju pie kā teleportēties", "stat.generalButton": "Galvenais", "stat.itemsButton": "<PERSON><PERSON>", "stat.minecraft.animals_bred": "Dzīvnie<PERSON> sa<PERSON>", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON><PERSON> att<PERSON> ar elitru", "stat.minecraft.bell_ring": "<PERSON><PERSON>", "stat.minecraft.boat_one_cm": "Attālums nobraukts ar laivu", "stat.minecraft.clean_armor": "Notīrī<PERSON> bru<PERSON>", "stat.minecraft.clean_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>i", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON> ka<PERSON>", "stat.minecraft.climb_one_cm": "Uzkā<PERSON><PERSON> augstums", "stat.minecraft.crouch_one_cm": "Attālums <PERSON>", "stat.minecraft.damage_absorbed": "<PERSON>bsor<PERSON><PERSON><PERSON><PERSON> bo<PERSON>", "stat.minecraft.damage_blocked_by_shield": "Vairoga bloķētais bojājums", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON> b<PERSON> (Absorbētais)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> b<PERSON> (Apturētais)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON><PERSON> reizes", "stat.minecraft.drop": "Lietas izmestas", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON><PERSON> gabali ap<PERSON>sti", "stat.minecraft.enchant_item": "Priekšmeti Apburti", "stat.minecraft.fall_one_cm": "Nokristais augstums", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON><PERSON><PERSON> katli", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON> noķertas", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON><PERSON> att<PERSON>", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Ar <PERSON><PERSON> noj<PERSON> distance", "stat.minecraft.inspect_dispenser": "Izmeklēti izsviedēji", "stat.minecraft.inspect_dropper": "Izmeklēti I<PERSON>ē<PERSON>", "stat.minecraft.inspect_hopper": "Piltuves Meklētas", "stat.minecraft.interact_with_anvil": "Darbības ar Laktu", "stat.minecraft.interact_with_beacon": "Darbī<PERSON> ar bāku", "stat.minecraft.interact_with_blast_furnace": "Darbības a<PERSON>", "stat.minecraft.interact_with_brewingstand": "Darbības ar brū<PERSON><PERSON> statīvu", "stat.minecraft.interact_with_campfire": "Darbības ar ugun<PERSON>", "stat.minecraft.interact_with_cartography_table": "Darb<PERSON><PERSON> a<PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "Dar<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.interact_with_furnace": "Mijiedarbības a<PERSON>", "stat.minecraft.interact_with_grindstone": "Darbības ar G<PERSON>", "stat.minecraft.interact_with_lectern": "Darbības ar Gr<PERSON><PERSON> statī<PERSON>", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.interact_with_smithing_table": "Darbības ar ka<PERSON> galdu", "stat.minecraft.interact_with_smoker": "Darbības a<PERSON>", "stat.minecraft.interact_with_stonecutter": "Darbības ar Akmeņ<PERSON>i", "stat.minecraft.jump": "Palēcieni", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.minecart_one_cm": "Attālums nobraukts ar raktuvju ratiņiem", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "<PERSON><PERSON> at<PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON> kaste<PERSON>", "stat.minecraft.pig_one_cm": "Attālums nojāts ar cūku", "stat.minecraft.play_noteblock": "<PERSON><PERSON><PERSON> bloki atskaņoti", "stat.minecraft.play_record": "Atskaņotie mūzikas diski", "stat.minecraft.play_time": "<PERSON><PERSON>, pavad<PERSON>ts spēlējot", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "<PERSON>i ievietoti puķupodos", "stat.minecraft.raid_trigger": "Aizsākt<PERSON>", "stat.minecraft.raid_win": "<PERSON>zvar<PERSON><PERSON>", "stat.minecraft.sleep_in_bed": "<PERSON>izes gulētas gultā", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laiks", "stat.minecraft.sprint_one_cm": "Distance noskrieta", "stat.minecraft.strider_one_cm": "Ar lavas ceļotāju noceļots", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "stat.minecraft.talked_to_villager": "Runāja ar ciemata <PERSON>em", "stat.minecraft.target_hit": "Mērķi <PERSON>r<PERSON><PERSON><PERSON>ti", "stat.minecraft.time_since_death": "<PERSON><PERSON> <PERSON>", "stat.minecraft.time_since_rest": "Laiks <PERSON><PERSON><PERSON>", "stat.minecraft.total_world_time": "Laiks ar atvērtu pasauli", "stat.minecraft.traded_with_villager": "Tir<PERSON>jā<PERSON> ar ciemata <PERSON>", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON>", "stat.minecraft.tune_noteblock": "<PERSON><PERSON><PERSON> bloki <PERSON>ņoti", "stat.minecraft.use_cauldron": "Ūdens paņemts no katla", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON> ūdens no<PERSON>ais att<PERSON>", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON> at<PERSON>", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON> ūdens noietais att<PERSON>", "stat.mobsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Cik Reizes Salauzts", "stat_type.minecraft.crafted": "<PERSON><PERSON>", "stat_type.minecraft.dropped": "Nomests", "stat_type.minecraft.killed": "Tu nogalināji %s %s", "stat_type.minecraft.killed.none": "Tu nekad neesi nogalinājis %s", "stat_type.minecraft.killed_by": "%s tevi no<PERSON>s %s reizi(es)", "stat_type.minecraft.killed_by.none": "Tevi nekad nav nogalinājis %s", "stat_type.minecraft.mined": "Reizes izrakts", "stat_type.minecraft.picked_up": "<PERSON><PERSON>", "stat_type.minecraft.used": "<PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "NOTEIKT", "structure_block.button.load": "IELĀDĒT", "structure_block.button.save": "SAGLABĀT", "structure_block.custom_data": "Pielāgoto datu taga nosaukums", "structure_block.detect_size": "Noteikt struktūras izmēru un pozīciju:", "structure_block.hover.corner": "Stūris: %s", "structure_block.hover.data": "Dati: %s", "structure_block.hover.load": "Ielādēt: %s", "structure_block.hover.save": "Saglabāt: %s", "structure_block.include_entities": "<PERSON><PERSON><PERSON><PERSON> entīti<PERSON>:", "structure_block.integrity": "Struktūras integritāte un sēkla", "structure_block.integrity.integrity": "Strukrūras integritāte", "structure_block.integrity.seed": "Struktūras s<PERSON>", "structure_block.invalid_structure_name": "Nederīgs struktūras nosaukums '%s'", "structure_block.load_not_found": "Struktūra '%s' nav pieejama ", "structure_block.load_prepare": "Struktūras %s pozīcija sagatavota", "structure_block.load_success": "<PERSON><PERSON><PERSON><PERSON><PERSON> ielād<PERSON>ta no '%s'", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "<PERSON><PERSON>", "structure_block.mode.load": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.mode.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "<PERSON><PERSON><PERSON> - Novietojuma un izmēra marķieris", "structure_block.mode_info.data": "<PERSON><PERSON> metode - <PERSON><PERSON><PERSON><PERSON> loģikas marķieris", "structure_block.mode_info.load": "<PERSON><PERSON><PERSON><PERSON> metode - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> no faila", "structure_block.mode_info.save": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON> metode - <PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.position": "Relatīvā pozīcija", "structure_block.position.x": "relatīvā pozīcija x", "structure_block.position.y": "relatīvā pozīcija y", "structure_block.position.z": "relatīvā pozīcija z", "structure_block.save_failure": "<PERSON><PERSON><PERSON> sagla<PERSON>t strukt<PERSON>ru '%s'", "structure_block.save_success": "Strukt<PERSON>ra saglabāta kā '%s'", "structure_block.show_air": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "structure_block.size": "Struk<PERSON><PERSON><PERSON> lie<PERSON>", "structure_block.size.x": "struk<PERSON><PERSON><PERSON> lie<PERSON> x", "structure_block.size.y": "struk<PERSON><PERSON><PERSON> lie<PERSON> y", "structure_block.size.z": "struk<PERSON><PERSON><PERSON> lie<PERSON> z", "structure_block.size_failure": "Nevar note<PERSON>t struktūras izmēru. Pievieno stūrus ar vienādiem struktūru nosaukumiem", "structure_block.size_success": "%s izmērs veiksmīgi noteikts", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Struktūras no<PERSON>", "subtitles.ambient.cave": "Baismīgs troksnis", "subtitles.ambient.sound": "Baismīgs troksnis", "subtitles.block.amethyst_block.chime": "Ametists skan", "subtitles.block.amethyst_block.resonate": "Ametists rezonē", "subtitles.block.anvil.destroy": "<PERSON><PERSON>a sa<PERSON>", "subtitles.block.anvil.land": "Lakta nokrita", "subtitles.block.anvil.use": "Lakta <PERSON>", "subtitles.block.barrel.close": "Muca aizveras", "subtitles.block.barrel.open": "Muca atver<PERSON>", "subtitles.block.beacon.activate": "Bāka aktivizējas", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.power_select": "<PERSON><PERSON><PERSON> jauda iz<PERSON>", "subtitles.block.beehive.drip": "Nopil medus", "subtitles.block.beehive.enter": "<PERSON><PERSON> i<PERSON><PERSON> stro<PERSON>", "subtitles.block.beehive.exit": "Bite pamet stropu", "subtitles.block.beehive.shear": "Šķēres skrāpē", "subtitles.block.beehive.work": "Bites strādā", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON>", "subtitles.block.bell.use": "<PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Krītlapa noliecas", "subtitles.block.big_dripleaf.tilt_up": "Krītlapa uzliecas", "subtitles.block.blastfurnace.fire_crackle": "Domnas krāsns sprakšķ", "subtitles.block.brewing_stand.brew": "Brūvēšanas Statīvs Burbuļo", "subtitles.block.bubble_column.bubble_pop": "Burbuļ<PERSON> sprāgst", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> plūst", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON><PERSON> plūst", "subtitles.block.bubble_column.whirlpool_ambient": "Burbuļi griežas", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON><PERSON><PERSON> plūst", "subtitles.block.button.click": "Uzspiež pogu", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON>", "subtitles.block.campfire.crackle": "Ugunskurs sprakšķ", "subtitles.block.candle.crackle": "Svece sprakšķ", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON> nod<PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.death": "<PERSON><PERSON> z<PERSON>", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON> auglis aug", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nokliķšķinas", "subtitles.block.composter.empty": "Komposteris iztukšots", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.ready": "Komposteris kompostē", "subtitles.block.conduit.activate": "Konduits aktivizējas", "subtitles.block.conduit.ambient": "Kond<PERSON><PERSON> pulsē", "subtitles.block.conduit.attack.target": "Konduits uzbrūk", "subtitles.block.conduit.deactivate": "Konduits <PERSON>akti<PERSON>z<PERSON>", "subtitles.block.copper_bulb.turn_off": "Vara spuldze izslēdzas", "subtitles.block.copper_bulb.turn_on": "Vara spuldze ieslēdzas", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON>", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON><PERSON>", "subtitles.block.crafter.craft": "Amatnieks veido", "subtitles.block.crafter.fail": "Amatniekam neizdodas izveidot", "subtitles.block.creaking_heart.hurt": "Čīktoņa sirds kliedz", "subtitles.block.creaking_heart.idle": "Baismīgs troksnis", "subtitles.block.creaking_heart.spawn": "Čīktoņa sirds pamostas", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Izdekorēts pods piepildās", "subtitles.block.decorated_pot.insert_fail": "Izdekorēts pods svārstās", "subtitles.block.decorated_pot.shatter": "Pods saplīst", "subtitles.block.dispenser.dispense": "Izšauta lieta", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Burvestību galds izmantots", "subtitles.block.end_portal.spawn": "End portāls atveras", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON> a<PERSON>", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.idle": "Acszieds čukst", "subtitles.block.eyeblossom.open": "Acszieds uzplaukst", "subtitles.block.fence_gate.toggle": "Žogu Vārti Čīkst", "subtitles.block.fire.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Spīdvaboles dūc", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON><PERSON> izšķiļas", "subtitles.block.furnace.fire_crackle": "Krāsns Čarkst", "subtitles.block.generic.break": "Bloks Sasists", "subtitles.block.generic.fall": "Kaut kas nokrīt uz bloka", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Bloku <PERSON>", "subtitles.block.generic.place": "Nolikts bloks", "subtitles.block.grindstone.use": "Galoda tiek izmantota", "subtitles.block.growing_plant.crop": "Augs ir apg<PERSON><PERSON>ts", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON>", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lejā pa medus bloku", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON>", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON>", "subtitles.block.lava.ambient": "<PERSON><PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON> Klikšķ", "subtitles.block.note_block.note": "Nošu Bloks spēlē", "subtitles.block.pale_hanging_moss.idle": "Baismīgs troksnis", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON><PERSON> kustas", "subtitles.block.pointed_dripstone.drip_lava": "Lavas pil", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON>va iepil katlā", "subtitles.block.pointed_dripstone.drip_water": "Ūdens pil", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Ūdens iepil katlā", "subtitles.block.pointed_dripstone.land": "Stalaktīts <PERSON>", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON> svilpo", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON> t<PERSON> i<PERSON>d", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON>", "subtitles.block.pressure_plate.click": "Spiediena <PERSON>lāksne Klikšķ", "subtitles.block.pumpkin.carve": "Šķēres izgrebj", "subtitles.block.redstone_torch.burnout": "Lāpa <PERSON>", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON> svilpo", "subtitles.block.respawn_anchor.charge": "Atdzimšanas enkurs ir uzl<PERSON>", "subtitles.block.respawn_anchor.deplete": "Atdzimšanas enkurs iztukšojas", "subtitles.block.respawn_anchor.set_spawn": "Atdzimšanas enkurs uzstāda dzimšanas vietu", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.block.sand.wind": "<PERSON><PERSON><PERSON>", "subtitles.block.sculk.charge": "Sculk burbuļo", "subtitles.block.sculk.spread": "Sculk izplatās", "subtitles.block.sculk_catalyst.bloom": "Sculk katalizators plaukst", "subtitles.block.sculk_sensor.clicking": "Sculk Sensors sāk klikšķināt", "subtitles.block.sculk_sensor.clicking_stop": "Sculk sensors pārtrauc klikšķināt", "subtitles.block.sculk_shrieker.shriek": "Sculk kliedzējs kliedz", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON>", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON>", "subtitles.block.smithing_table.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dūmo", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.block.sniffer_egg.hatch": "<PERSON>š<PERSON><PERSON><PERSON><PERSON><PERSON> ola izšķiļas", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dēj olu", "subtitles.block.sponge.absorb": "Sūklis uzsūc", "subtitles.block.sweet_berry_bush.pick_berries": "Paukšķ ogas", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "Lūka <PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Draudīgs priekšmets gatavojas", "subtitles.block.trial_spawner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rad<PERSON>ā<PERSON> sprakšķ", "subtitles.block.trial_spawner.ambient_charged": "Draudīga sprakšķēšana", "subtitles.block.trial_spawner.ambient_ominous": "Draudīga sprakšķēšana", "subtitles.block.trial_spawner.charge_activate": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.trial_spawner.close_shutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.block.trial_spawner.detect_player": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.block.trial_spawner.eject_item": "Izaicinājuma radītājs izsviež priekšmetus", "subtitles.block.trial_spawner.ominous_activate": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rad<PERSON>", "subtitles.block.trial_spawner.spawn_item": "Draudīgs priekšmets izkrīt", "subtitles.block.trial_spawner.spawn_item_begin": "Draudīgs p<PERSON>kš<PERSON>", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> radītājs rada radību", "subtitles.block.tripwire.attach": "<PERSON><PERSON> aukla Pieķeras", "subtitles.block.tripwire.click": "<PERSON><PERSON> aukla Klikšķ", "subtitles.block.tripwire.detach": "Lamatu aukla Atvienojas", "subtitles.block.vault.activate": "Se<PERSON>s <PERSON>", "subtitles.block.vault.ambient": "Seifs sprakšķ", "subtitles.block.vault.close_shutter": "Se<PERSON>s aiz<PERSON>", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "Seifs izsviež priekšmetu", "subtitles.block.vault.insert_item": "Se<PERSON><PERSON>", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.open_shutter": "Seifs atveras", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON> <PERSON><PERSON> s<PERSON>", "subtitles.block.water.ambient": "Ūdens plūst", "subtitles.block.wet_sponge.dries": "Sūklis izkalst", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "<PERSON><PERSON><PERSON>bot<PERSON> gr<PERSON><PERSON> no<PERSON>", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.chiseled_bookshelf.take_enchanted": "<PERSON><PERSON><PERSON>bot<PERSON> gr<PERSON><PERSON> pa<PERSON>", "subtitles.enchant.thorns.hit": "Ērkšķi iedur", "subtitles.entity.allay.ambient_with_item": "<PERSON><PERSON><PERSON> mek<PERSON>", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.allay.death": "<PERSON><PERSON><PERSON> mirst", "subtitles.entity.allay.hurt": "<PERSON>aja<PERSON> sāp", "subtitles.entity.allay.item_given": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.allay.item_taken": "<PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_thrown": "<PERSON>aj<PERSON>", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.brush": "Čaula tiek noslaucīta", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> ēd", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pasa<PERSON> sevi", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON><PERSON><PERSON> lūr", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON><PERSON><PERSON> lūr", "subtitles.entity.armor_stand.fall": "Kaut kas nokrita", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON> trāpa", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sit", "subtitles.entity.arrow.shoot": "Bulta izšauta", "subtitles.entity.axolotl.attack": "Aksolotls uzbrūk", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.axolotl.idle_air": "Aksolotls čivina", "subtitles.entity.axolotl.idle_water": "Aksolotls čivina", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON> peld", "subtitles.entity.bat.ambient": "Siks<PERSON><PERSON><PERSON><PERSON> ieķērcās", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lido prom", "subtitles.entity.bee.ambient": "<PERSON>e dūc", "subtitles.entity.bee.death": "Bite nomirst", "subtitles.entity.bee.hurt": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.bee.loop": "<PERSON>e dūc", "subtitles.entity.bee.loop_aggressive": "<PERSON>e du<PERSON><PERSON>gi dūc", "subtitles.entity.bee.pollinate": "Bite laimīgi dūc", "subtitles.entity.bee.sting": "Bite iedzeļ", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.blaze.burn": "Liesmeklis sprakšķ", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.breeze.deflect": "<PERSON>ē<PERSON> novir<PERSON>", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.breeze.idle_air": "<PERSON>ēsma lido", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.jump": "Vēsma lec", "subtitles.entity.breeze.land": "<PERSON>ēsma piezemē<PERSON>", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "Uzliek seglus", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON>", "subtitles.entity.cat.ambient": "Kaķis ņaud", "subtitles.entity.cat.beg_for_food": "Kaķis lūdzas", "subtitles.entity.cat.death": "Kaķis nomirst", "subtitles.entity.cat.eat": "Kaķis ēd", "subtitles.entity.cat.hiss": "Kaķis šņāc", "subtitles.entity.cat.hurt": "Kaķim sāp", "subtitles.entity.cat.purr": "Kaķis ņurrā", "subtitles.entity.chicken.ambient": "Vista klukst", "subtitles.entity.chicken.death": "Vista nomirst", "subtitles.entity.chicken.egg": "Vista dēj", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.cod.death": "Men<PERSON> no<PERSON>", "subtitles.entity.cod.flop": "Menca lec", "subtitles.entity.cod.hurt": "Men<PERSON><PERSON> s<PERSON>p", "subtitles.entity.cow.ambient": "Govs saka mū", "subtitles.entity.cow.death": "Govs no<PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.milk": "Govs tiek izslaukta", "subtitles.entity.creaking.activate": "Čīktonis aktivizē<PERSON>", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.attack": "Čīktonis <PERSON>", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.spawn": "Čīkstonis <PERSON>", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir a<PERSON>", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.creeper.death": "C<PERSON>per nomirst", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON><PERSON> svilpj", "subtitles.entity.dolphin.attack": "Delfī<PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON><PERSON> ēd", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON><PERSON> sāp", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON><PERSON> lec", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON><PERSON> peld", "subtitles.entity.donkey.ambient": "Ēzelis īi-āaa", "subtitles.entity.donkey.angry": "Ēzelis zviedz", "subtitles.entity.donkey.chest": "Ēzelis aprīkots", "subtitles.entity.donkey.death": "Ēzelis mirst", "subtitles.entity.donkey.eat": "Ēzelis ēd", "subtitles.entity.donkey.hurt": "Ēzelim sāp", "subtitles.entity.donkey.jump": "Ēzelis lec", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.swim": "Slī<PERSON><PERSON> peld", "subtitles.entity.egg.throw": "<PERSON><PERSON>o", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> sargs smagi <PERSON>", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON> sargs plivin<PERSON>s", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON><PERSON><PERSON> sarg<PERSON> no<PERSON>", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> sargs no<PERSON>", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> sargs plivin<PERSON>s", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sargam sāp", "subtitles.entity.ender_dragon.ambient": "Pūķis rūc", "subtitles.entity.ender_dragon.death": "Pūķis mirst", "subtitles.entity.ender_dragon.flap": "Pūķis plivinās", "subtitles.entity.ender_dragon.growl": "Pūķis rūc", "subtitles.entity.ender_dragon.hurt": "Pūķim sāp", "subtitles.entity.ender_dragon.shoot": "Pūķa uzbrukums", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> a<PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> p<PERSON>o", "subtitles.entity.enderman.ambient": "<PERSON>erman vūpojas", "subtitles.entity.enderman.death": "<PERSON><PERSON> nomirst", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> s<PERSON>p", "subtitles.entity.enderman.scream": "Enderman kliedz", "subtitles.entity.enderman.stare": "<PERSON><PERSON>", "subtitles.entity.enderman.teleport": "Enderman teleportējas", "subtitles.entity.endermite.ambient": "Ender<PERSON> steidzas", "subtitles.entity.endermite.death": "Endermite nomirst", "subtitles.entity.endermite.hurt": "Endermite sāp", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON> murmina", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.evoker.celebrate": "Evokers gavil<PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> sāp", "subtitles.entity.evoker.prepare_attack": "Evokeris gatavojas uzbrukumam", "subtitles.entity.evoker.prepare_summon": "Evokeris gatavojas izsaukt", "subtitles.entity.evoker.prepare_wololo": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.evoker_fangs.attack": "Ilkņi aiz<PERSON>", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON> pier<PERSON>", "subtitles.entity.firework_rocket.blast": "Raķete sprāgst", "subtitles.entity.firework_rocket.launch": "Raķete iz<PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.twinkle": "Raķete mirgo", "subtitles.entity.fish.swim": "Šļaka<PERSON>", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.splash": "Zvejas āķa šļakatas", "subtitles.entity.fishing_bobber.throw": "Izmests pludiņš", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.ambient": "Lapsa čīkst", "subtitles.entity.fox.bite": "Lapsa kož", "subtitles.entity.fox.death": "<PERSON><PERSON> no<PERSON>", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON> ēd", "subtitles.entity.fox.hurt": "Laps<PERSON> sāp", "subtitles.entity.fox.screech": "Lapsa ķērc", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sniff": "Lapsa osta", "subtitles.entity.fox.spit": "Lapsa spļauj", "subtitles.entity.fox.teleport": "Lapsa teleportējās", "subtitles.entity.frog.ambient": "Varde kurkst", "subtitles.entity.frog.death": "Varde nomirst", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON> ēd", "subtitles.entity.frog.hurt": "V<PERSON><PERSON>", "subtitles.entity.frog.lay_spawn": "Varde dēj i<PERSON>", "subtitles.entity.frog.long_jump": "Varde lec", "subtitles.entity.generic.big_fall": "Kaut kas nokrita", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Nāve", "subtitles.entity.generic.drink": "<PERSON><PERSON>", "subtitles.entity.generic.eat": "Ēšana", "subtitles.entity.generic.explode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.hurt": "Kaut kam sāp", "subtitles.entity.generic.small_fall": "Kaut kas nokr<PERSON>t", "subtitles.entity.generic.splash": "Š<PERSON><PERSON>", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> raud", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.glow_item_frame.break": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> noklikšķ", "subtitles.entity.glow_squid.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> peld", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> sāp", "subtitles.entity.glow_squid.squirt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.goat.ambient": "<PERSON><PERSON> bl<PERSON><PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON>", "subtitles.entity.goat.eat": "<PERSON><PERSON>", "subtitles.entity.goat.horn_break": "Kazas rags nolūzt", "subtitles.entity.goat.hurt": "<PERSON><PERSON> sāp", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> lec", "subtitles.entity.goat.milk": "<PERSON><PERSON> tiek slaukta", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON>", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON>", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> vaid", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> r<PERSON>c", "subtitles.entity.hoglin.attack": "Hoglins uzbrūk", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> par <PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> sāp", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.ambient": "Zirgs zviedz", "subtitles.entity.horse.angry": "Zirgs zviedz", "subtitles.entity.horse.armor": "Uzliek zirgu bruņas", "subtitles.entity.horse.breathe": "Zirgs elpo", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> ēd", "subtitles.entity.horse.gallop": "Zirgs aullēkšo", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.horse.jump": "Zirgs lec", "subtitles.entity.horse.saddle": "Uzliek seglus", "subtitles.entity.husk.ambient": "Husk<PERSON> r<PERSON>c", "subtitles.entity.husk.converted_to_zombie": "Hasks pārv<PERSON><PERSON><PERSON><PERSON> par zombiju", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON> nomirst", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lā<PERSON>u", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.illusioner.hurt": "Iluzionistam sāp", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "<PERSON><PERSON><PERSON> saga<PERSON><PERSON>", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON> saga<PERSON><PERSON> spogu<PERSON> bi<PERSON>i", "subtitles.entity.iron_golem.attack": "Dzelzs golems <PERSON>", "subtitles.entity.iron_golem.damage": "<PERSON>zelzs golems sa<PERSON>", "subtitles.entity.iron_golem.death": "<PERSON><PERSON>zs golems nomirst", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON><PERSON><PERSON> go<PERSON> s<PERSON>p", "subtitles.entity.iron_golem.repair": "<PERSON><PERSON>zs golems salabots", "subtitles.entity.item.break": "<PERSON><PERSON>", "subtitles.entity.item.pickup": "Vienība noplunkšķ", "subtitles.entity.item_frame.add_item": "Priekšmetu <PERSON>", "subtitles.entity.item_frame.break": "Priekšmetu Rā<PERSON>", "subtitles.entity.item_frame.place": "Priekšmetu Rāmis nolikts", "subtitles.entity.item_frame.remove_item": "Priekšmetu Rāmis iztuk<PERSON>s", "subtitles.entity.item_frame.rotate_item": "Priekšmetu Rā<PERSON> noklikšķ", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON> mez<PERSON> sapl<PERSON>st", "subtitles.entity.leash_knot.place": "Pavadas mezgls sasiets", "subtitles.entity.lightning_bolt.impact": "Zibens iesper", "subtitles.entity.lightning_bolt.thunder": "Pērkons auro", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON><PERSON><PERSON> bl<PERSON>j", "subtitles.entity.llama.chest": "<PERSON><PERSON> lādi", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON> ē<PERSON>", "subtitles.entity.llama.hurt": "<PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON> <PERSON><PERSON>", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON> nomirst", "subtitles.entity.magma_cube.hurt": "Magma<PERSON> kubam sāp", "subtitles.entity.magma_cube.squish": "Magmas <PERSON> mīkstel<PERSON>", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.minecart.inside_underwater": "<PERSON><PERSON><PERSON><PERSON><PERSON> zem ūden<PERSON>", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom pār<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mooshroom.eat": "Mooshroom ēd", "subtitles.entity.mooshroom.milk": "Mooshroom tiek slaukts", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom tiek aizdomīgi slaukts", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> ēd", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON> le<PERSON>", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda puko<PERSON>", "subtitles.entity.panda.ambient": "<PERSON><PERSON>", "subtitles.entity.panda.bite": "Panda kož", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> blēj", "subtitles.entity.panda.death": "Panda nomirst", "subtitles.entity.panda.eat": "<PERSON><PERSON> ēd", "subtitles.entity.panda.hurt": "Pandai sāp", "subtitles.entity.panda.pre_sneeze": "Pandai kut deguns", "subtitles.entity.panda.sneeze": "Panda nošķaudās", "subtitles.entity.panda.step": "<PERSON><PERSON>", "subtitles.entity.panda.worried_ambient": "Pan<PERSON>", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> ēd", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> vaid", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> murmina", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> vaid", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON> murmina", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON> plakšķ", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON> ieķērcas", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON> murmina", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON> plakšķ", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON> at<PERSON> veksu", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> ķiķina", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.ambient": "Fantoms ieķērcas", "subtitles.entity.phantom.bite": "Fantoms kož", "subtitles.entity.phantom.death": "<PERSON>toms mirst", "subtitles.entity.phantom.flap": "Fantoms lido", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.phantom.swoop": "Fantom<PERSON> metas lejā", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON>o", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.pig.hurt": "Cū<PERSON> sāp", "subtitles.entity.pig.saddle": "Uzliek seglus", "subtitles.entity.piglin.admiring_item": "Piglins apbrīno priekšmetu", "subtitles.entity.piglin.ambient": "<PERSON><PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON> du<PERSON>", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> gavi<PERSON>", "subtitles.entity.piglin.converted_to_zombified": "Piglins pārvēr<PERSON>s par zombificētu piglinu", "subtitles.entity.piglin.death": "<PERSON><PERSON> nomirst", "subtitles.entity.piglin.hurt": "<PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> skaudī<PERSON>", "subtitles.entity.piglin.retreat": "<PERSON><PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON>", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> piglins du<PERSON>", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> piglins pārvēršas par zombificētu piglinu", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> piglins nomirst", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pig<PERSON>m sāp", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mur<PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.player.attack.crit": "Kritiskais uzbrukums", "subtitles.entity.player.attack.knockback": "Atsitiena <PERSON>", "subtitles.entity.player.attack.strong": "Stiprais <PERSON>", "subtitles.entity.player.attack.sweep": "Slauko<PERSON><PERSON> uz<PERSON>", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "Atrauga", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sāp", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> deg", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dingst", "subtitles.entity.player.teleport": "Spēlētājs teleportē<PERSON>", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>", "subtitles.entity.puffer_fish.blow_up": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> no<PERSON>", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> lē<PERSON>", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> zivij s<PERSON>p", "subtitles.entity.puffer_fish.sting": "<PERSON>ūš<PERSON><PERSON> z<PERSON>", "subtitles.entity.rabbit.ambient": "Zaķis pīks<PERSON>", "subtitles.entity.rabbit.attack": "Zaķis uzbrūk", "subtitles.entity.rabbit.death": "Zaķis nomirst", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "Zaķis palecas", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "Postī<PERSON><PERSON><PERSON><PERSON> ko<PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON> no<PERSON>", "subtitles.entity.salmon.flop": "<PERSON><PERSON> lec", "subtitles.entity.salmon.hurt": "<PERSON><PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> bauro", "subtitles.entity.sheep.death": "<PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.teleport": "<PERSON>lker teleportējas", "subtitles.entity.shulker_bullet.hit": "<PERSON>lker Lode eksplodē", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.ambient": "Skelets klabina", "subtitles.entity.skeleton.converted_to_stray": "Skelets nomaldijās", "subtitles.entity.skeleton.death": "Skelets nomirst", "subtitles.entity.skeleton.hurt": "Skelets savainojas", "subtitles.entity.skeleton.shoot": "Skelets šauj", "subtitles.entity.skeleton_horse.ambient": "Skeletona Zirgs raud", "subtitles.entity.skeleton_horse.death": "Skeletona Zirgs nomirst", "subtitles.entity.skeleton_horse.hurt": "Skeleta zirgam sāp", "subtitles.entity.skeleton_horse.jump_water": "Zirgs skelets lec", "subtitles.entity.skeleton_horse.swim": "Zirgs skelets peld", "subtitles.entity.slime.attack": "Gļotas uzbrūk", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.slime.squish": "Gļotas <PERSON>", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rok", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kā<PERSON>", "subtitles.entity.sniffer.drop_seed": "Ošņāt<PERSON><PERSON>s iz<PERSON>", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ēd", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.sniffer.egg_hatch": "<PERSON>š<PERSON><PERSON><PERSON><PERSON><PERSON> ola izšķiļas", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.death": "Sniega golems nomirst", "subtitles.entity.snow_golem.hurt": "<PERSON><PERSON><PERSON> golemam s<PERSON>p", "subtitles.entity.snowball.throw": "Sniega Pika lido", "subtitles.entity.spider.ambient": "Zirnek<PERSON>", "subtitles.entity.spider.death": "Zirneklis no<PERSON>", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON><PERSON> peld", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON> ceļot<PERSON><PERSON>s mirst", "subtitles.entity.strider.eat": "<PERSON><PERSON> ce<PERSON><PERSON><PERSON><PERSON><PERSON> ēd", "subtitles.entity.strider.happy": "<PERSON>vas ceļ<PERSON><PERSON><PERSON><PERSON> tral<PERSON>", "subtitles.entity.strider.hurt": "<PERSON>vas ceļot<PERSON><PERSON>m sāp", "subtitles.entity.strider.idle": "Lavas c<PERSON>ļ<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "Lavas ceļot<PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.tnt.primed": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.tropical_fish.death": "Tropiskā zivs mirst", "subtitles.entity.tropical_fish.flop": "Tropiskā Zivs <PERSON>", "subtitles.entity.tropical_fish.hurt": "Tropiskajai zivij s<PERSON>p", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> no<PERSON>", "subtitles.entity.turtle.egg_break": "B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.turtle.egg_crack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.egg_hatch": "Izšķīlās bru<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.hurt": "Bru<PERSON><PERSON><PERSON><PERSON><PERSON> sāp", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.turtle.lay_egg": "Bruņurupucis izdēj olu", "subtitles.entity.turtle.shamble": "Bruņuru<PERSON>ča galds", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> galds", "subtitles.entity.turtle.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> peld", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON>", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.villager.ambient": "Ciema iedzīvotā<PERSON> murmina", "subtitles.entity.villager.celebrate": "Ciema <PERSON>edzī<PERSON>", "subtitles.entity.villager.death": "Ciema iedzīvotājs <PERSON>", "subtitles.entity.villager.hurt": "Ciema iedzīvo<PERSON>ā<PERSON> sāp", "subtitles.entity.villager.no": "Ciema iedzīvotā<PERSON>", "subtitles.entity.villager.trade": "Ciema iedzīvotājs noslēdz darījumu", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> str<PERSON>", "subtitles.entity.villager.work_butcher": "Miesnieks strādā", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strādā", "subtitles.entity.villager.work_cleric": "Garīdznieks strādā", "subtitles.entity.villager.work_farmer": "Zemnieks strādā", "subtitles.entity.villager.work_fisherman": "Z<PERSON><PERSON><PERSON><PERSON> strādā", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON> ka<PERSON> strā<PERSON>", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON> a<PERSON>tr<PERSON><PERSON><PERSON><PERSON><PERSON> strādā", "subtitles.entity.villager.work_librarian": "Bibliotek<PERSON><PERSON> strā<PERSON>", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON><PERSON><PERSON> strād<PERSON>", "subtitles.entity.villager.work_shepherd": "G<PERSON> strādā", "subtitles.entity.villager.work_toolsmith": "Instrument<PERSON><PERSON><PERSON><PERSON> str<PERSON>", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.villager.yes": "Ciema iedzīvotājs <PERSON>", "subtitles.entity.vindicator.ambient": "Vindikators murmina", "subtitles.entity.vindicator.celebrate": "Vindikators gavi<PERSON>", "subtitles.entity.vindicator.death": "Vindikators nomirst", "subtitles.entity.vindicator.hurt": "Vindikators sa<PERSON>jas", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> mur<PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> d<PERSON>nu", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> d<PERSON>ru", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tirgot<PERSON>m sāp", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tirgotā<PERSON> piedāvā dar<PERSON>jumu", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.warden.agitated": "Uzraugs dusmīgi rūc", "subtitles.entity.warden.ambient": "Uzraugs smilkst", "subtitles.entity.warden.angry": "Uzraugs trako", "subtitles.entity.warden.attack_impact": "Uzraugs iesit", "subtitles.entity.warden.death": "Uzraugs nomirst", "subtitles.entity.warden.dig": "Uzraugs rok", "subtitles.entity.warden.emerge": "Uzra<PERSON> parādās", "subtitles.entity.warden.heartbeat": "Uzrauga sirds pukst", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.warden.listening": "Uzraugs novēro", "subtitles.entity.warden.listening_angry": "Uzraugs dusmīgi novēro", "subtitles.entity.warden.nearby_close": "Uzraugs tuvojas", "subtitles.entity.warden.nearby_closer": "Uzraugs virzās", "subtitles.entity.warden.nearby_closest": "Uzraugs ir tuvu", "subtitles.entity.warden.roar": "Uzraugs rūc", "subtitles.entity.warden.sniff": "Uzraugs ošņā", "subtitles.entity.warden.sonic_boom": "Uzraugs blīkšķ", "subtitles.entity.warden.sonic_charge": "Uzraugs lādē<PERSON>", "subtitles.entity.warden.step": "Uzraugs soļo", "subtitles.entity.warden.tendril_clicks": "Uzrauga stīga klikšķ", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON><PERSON>o", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON> ķiķina", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON> met", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "Wither nomirst", "subtitles.entity.wither.hurt": "<PERSON><PERSON> s<PERSON>p", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON>er <PERSON>", "subtitles.entity.wither_skeleton.ambient": "Wither skelets klabina", "subtitles.entity.wither_skeleton.death": "Wither skelets mirst", "subtitles.entity.wither_skeleton.hurt": "<PERSON>er skeletam s<PERSON>p", "subtitles.entity.wolf.ambient": "Vilks elš", "subtitles.entity.wolf.bark": "Suns sūkā", "subtitles.entity.wolf.death": "Vilks nomirst", "subtitles.entity.wolf.growl": "Vilks rūc", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.shake": "Vilks nokratās", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> r<PERSON>c", "subtitles.entity.zoglin.attack": "Zoglins uzbrūk", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> nomir<PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.ambient": "<PERSON>omb<PERSON><PERSON><PERSON> nova<PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON>r<PERSON><PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> par <PERSON>i", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>", "subtitles.entity.zombie.destroy_egg": "Bruņuru<PERSON>č<PERSON> Ola <PERSON>mpāta", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "subtitles.entity.zombie.infect": "Zombijs infic<PERSON>", "subtitles.entity.zombie_horse.ambient": "Zombija zirgs raud", "subtitles.entity.zombie_horse.death": "Zombija zirgs nomirst", "subtitles.entity.zombie_horse.hurt": "Zombija zirgam sāp", "subtitles.entity.zombie_villager.ambient": "Zombija Ciema Iedzīvotā<PERSON>", "subtitles.entity.zombie_villager.converted": "Zombiju iedzīvotājs <PERSON>", "subtitles.entity.zombie_villager.cure": "Zombiju iezī<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.death": "Zombija Ciema Iedzīvotā<PERSON>", "subtitles.entity.zombie_villager.hurt": "Zombija ciema Iedzīvotā<PERSON> sāp", "subtitles.entity.zombified_piglin.ambient": "Zombificēts piglins <PERSON>urd", "subtitles.entity.zombified_piglin.angry": "Zombificēts piglins dusmīgi <PERSON>urd", "subtitles.entity.zombified_piglin.death": "Zombificēts piglins nomirst", "subtitles.entity.zombified_piglin.hurt": "Zombificētam piglinam sāp", "subtitles.event.mob_effect.bad_omen": "Spēkā stājas slikta vēsts", "subtitles.event.mob_effect.raid_omen": "Iebrukums tuvojas", "subtitles.event.mob_effect.trial_omen": "Tuvojas draudīgs izaicinājums", "subtitles.event.raid.horn": "Draudošs rags ietaurējas", "subtitles.item.armor.equip": "Piederumi uzvilkti", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON> br<PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON> nošķind", "subtitles.item.armor.equip_elytra": "Elitra šalc", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON> bru<PERSON> nošķind", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON> bru<PERSON> nošķind", "subtitles.item.armor.equip_leather": "<PERSON><PERSON> br<PERSON>", "subtitles.item.armor.equip_netherite": "Nezer<PERSON><PERSON> br<PERSON>", "subtitles.item.armor.equip_turtle": "Bruņurupuča čaula ietriecas", "subtitles.item.armor.equip_wolf": "Vilka bruņ<PERSON>", "subtitles.item.armor.unequip_wolf": "<PERSON>ilka bruņ<PERSON> no<PERSON>", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON>k", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON>", "subtitles.item.bone_meal.use": "<PERSON><PERSON> milti krokojas", "subtitles.item.book.page_turn": "<PERSON><PERSON>", "subtitles.item.book.put": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON> granti", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON> smilt<PERSON>", "subtitles.item.brush.brushing.sand.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON> tī<PERSON>", "subtitles.item.bucket.empty": "Spainis i<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> noķerta", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON> noķerts", "subtitles.item.bundle.drop_contents": "Saišķis iz<PERSON><PERSON><PERSON>s", "subtitles.item.bundle.insert": "Priekšmets iepakots", "subtitles.item.bundle.insert_fail": "Saišķis pilns", "subtitles.item.bundle.remove_one": "Priekšmets izpakots", "subtitles.item.chorus_fruit.teleport": "Spēlēt<PERSON><PERSON> teleport<PERSON>ā<PERSON>", "subtitles.item.crop.plant": "Labī<PERSON>", "subtitles.item.crossbow.charge": "Arb<PERSON><PERSON>", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON> trāpa", "subtitles.item.crossbow.load": "Arbalet<PERSON>", "subtitles.item.crossbow.shoot": "Arb<PERSON><PERSON>", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trai<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Ugunsbumba svilpo", "subtitles.item.flintandsteel.use": "Krams un dzelzs noklikšķ", "subtitles.item.glow_ink_sac.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tintes ma<PERSON> traipo", "subtitles.item.goat_horn.play": "Kazas rags spēlē", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON>", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "<PERSON><PERSON> ma<PERSON> traipo", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Dzīslakmens kompass noslēdz rādītāju uz dz<PERSON>slakmeni", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON><PERSON>", "subtitles.item.nether_wart.plant": "Labī<PERSON>", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON>", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Šķēres noklikšķ", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Vairogs bloķē", "subtitles.item.shovel.flatten": "Lāps<PERSON> izlīdzina", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.totem.use": "Totēms aktivizējas", "subtitles.item.trident.hit": "Tridents iedur", "subtitles.item.trident.hit_ground": "Tridents vibrē", "subtitles.item.trident.return": "Tridents atgriežas", "subtitles.item.trident.riptide": "Tridents plūst", "subtitles.item.trident.throw": "Tridents aizlido", "subtitles.item.trident.thunder": "Tridenta zibens krakšķ", "subtitles.item.wolf_armor.break": "Vilka bruņas <PERSON>", "subtitles.item.wolf_armor.crack": "<PERSON><PERSON><PERSON> bru<PERSON>", "subtitles.item.wolf_armor.damage": "Vilka bruņas tiek boj<PERSON><PERSON>", "subtitles.item.wolf_armor.repair": "Vilka bruņas tiek salabotas", "subtitles.particle.soul_escape": "Dvēsele izbēg", "subtitles.ui.cartography_table.take_result": "Karte tiek zīmēta", "subtitles.ui.hud.bubble_pop": "Gaisa indikat<PERSON>", "subtitles.ui.loom.take_result": "Stelles tiek izmantotas", "subtitles.ui.stonecutter.take_result": "Akmeņgriezējs tiek izmantots", "subtitles.weather.rain": "<PERSON><PERSON> l<PERSON>", "symlink_warning.message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ielāde no mapēm ar simboliskajām sa<PERSON>, var b<PERSON><PERSON>, ja precīzi neziniet ko darāt. <PERSON><PERSON><PERSON><PERSON>, apmeklējiet %s, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "symlink_warning.message.pack": "<PERSON>u i<PERSON>de no mapēm ar simboliskaj<PERSON>m sa<PERSON>, var bū<PERSON>, ja precīzi neziniet ko dar<PERSON>t. <PERSON><PERSON><PERSON><PERSON>, apmekl<PERSON>jiet %s, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "symlink_warning.message.world": "<PERSON><PERSON><PERSON><PERSON><PERSON> ielāde no mapēm ar simboliskajām sa<PERSON>, var b<PERSON><PERSON>, ja precīzi neziniet ko darāt. <PERSON><PERSON><PERSON><PERSON>, apmeklējiet %s, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "symlink_warning.more_info": "<PERSON><PERSON><PERSON><PERSON>", "symlink_warning.title": "Pasaules mape satur simboliskās saites", "symlink_warning.title.pack": "Pievienot<PERSON>(s) paka(s) satur simboliskās saites", "symlink_warning.title.world": "Pasaules mape satur simboliskās saites", "team.collision.always": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON><PERSON>t citas komandas", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON><PERSON><PERSON> savu komandu", "team.notFound": "Nezināma komanda: %s", "team.visibility.always": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Paslēpt citas komandas", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON><PERSON> savas komandas", "team.visibility.never": "<PERSON><PERSON><PERSON>", "telemetry.event.advancement_made.description": "Izpratne par sasniegumu saņem<PERSON> kontekstu var mums palīdz<PERSON>t labāk izprast un uzlabot spēles gaitu.", "telemetry.event.advancement_made.title": "Sasniegums izveidots", "telemetry.event.game_load_times.description": "<PERSON>is notikums var mums pal<PERSON><PERSON><PERSON><PERSON><PERSON> note<PERSON>, kur ir ne<PERSON><PERSON><PERSON> palaišanas veiktspējas uzlabojumi, m<PERSON><PERSON> pala<PERSON><PERSON> fāzes izpi<PERSON> laik<PERSON>.", "telemetry.event.game_load_times.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.optional": "%s (Neobligāts)", "telemetry.event.optional.disabled": "%s (Neobligāts) - Atslēgts", "telemetry.event.performance_metrics.description": "<PERSON><PERSON><PERSON> vispā<PERSON><PERSON>jo Minecraft veiktspējas profilu, mēs varam pielāgot un optimizēt spēli dažādām sistēmu specifikācijām un operētājsistēmām.\nSp<PERSON><PERSON> versija tiek i<PERSON>, lai mums palīdz<PERSON>tu salīdzināt veiktspējas profilu jaunajām Minecraft versijām.", "telemetry.event.performance_metrics.title": "Veiktspējas rādī<PERSON>āji", "telemetry.event.required": "%s (Nepieciešams)", "telemetry.event.world_load_times.description": "Mums ir <PERSON><PERSON><PERSON><PERSON>, cik ilgs laiks <PERSON>, lai pievie<PERSON> pasaule<PERSON>, un kā tas mainās laika gaitā. <PERSON><PERSON><PERSON><PERSON>, kad mēs pievienojam jaunas funkcijas vai veicam lielākas tehniskas i<PERSON><PERSON>, mums ir <PERSON><PERSON>, kāda ir iete<PERSON>me uz iel<PERSON> laiku.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "Zinot kā spēlētāji spēlē Minecraft (piemēram spē<PERSON>ž<PERSON>, klienta vai servera modifikācijas un spēles versija) ļauj mums koncentrēties uz spēles atjauninājumiem vietās kur spēlētājiem rūp visvairāk.\nPasaules ielādes notikums tiek lietots kopā ar pasaules izlādes notikumu, lai aprēķinātu cik ilga bija spēles sesija.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_unloaded.description": "Šis notikums tiek lietots kopā ar pasaules ielādes notikumu, lai aprēķinātu cik ilga bija spēles sesija.\nIlgums (sekundēs un tikšķos) tiek mērīts kad pasaules sesija ir pabeigta (iziet uz galveno e<PERSON>ānu, atvienojas no servera).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON> (Tiki)", "telemetry.property.advancement_id.title": "Sasnieguma ID", "telemetry.property.client_id.title": "Klienta ID", "telemetry.property.client_modded.title": "Klients modific<PERSON>", "telemetry.property.dedicated_memory_kb.title": "Piešķirtā atmiņa (kB)", "telemetry.property.event_timestamp_utc.title": "Notikuma <PERSON> (UTC)", "telemetry.property.frame_rate_samples.title": "Kadrēšanas biežuma paraugi (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "<PERSON><PERSON><PERSON><PERSON> vers<PERSON>", "telemetry.property.launcher_name.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (milisekundes)", "telemetry.property.load_time_loading_overlay_ms.title": "Laiks lādē<PERSON> e<PERSON> (Milisekundes)", "telemetry.property.load_time_pre_window_ms.title": "Laiks pirms logs atveras (Milisekundes)", "telemetry.property.load_time_total_time_ms.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (milisekundes)", "telemetry.property.minecraft_session_id.title": "Minecraft sesijas ID", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON> pasaule", "telemetry.property.number_of_samples.title": "Paraug<PERSON> skaits", "telemetry.property.operating_system.title": "Operētāj<PERSON>ē<PERSON>", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Platforma", "telemetry.property.realms_map_content.title": "<PERSON>a kartes saturs (Mini spēles nosaukums)", "telemetry.property.render_distance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "telemetry.property.render_time_samples.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON> para<PERSON>i", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON> k<PERSON><PERSON> (sekundes)", "telemetry.property.server_modded.title": "<PERSON><PERSON> modific<PERSON>", "telemetry.property.server_type.title": "Servera tips", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON> k<PERSON><PERSON> (tikšķi)", "telemetry.property.used_memory_samples.title": "Izmantotā br<PERSON>ļ<PERSON> atmiņa", "telemetry.property.user_id.title": "Lietotāja ID", "telemetry.property.world_load_time_ms.title": "<PERSON><PERSON><PERSON> i<PERSON> laiks (milisekundes)", "telemetry.property.world_session_id.title": "Pa<PERSON><PERSON> se<PERSON>", "telemetry_info.button.give_feedback": "<PERSON> atsauksmes", "telemetry_info.button.privacy_statement": "Paziņojums par konfidencialitāti", "telemetry_info.button.show_data": "<PERSON><PERSON><PERSON><PERSON> manus datus", "telemetry_info.opt_in.description": "Es piekrītu neobligātu telemetrijas datu sūt<PERSON>", "telemetry_info.property_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry_info.screen.description": "<PERSON>o datu a<PERSON><PERSON><PERSON><PERSON><PERSON> mums palī<PERSON><PERSON> uz<PERSON>bot Minecraft, s<PERSON><PERSON><PERSON><PERSON>, kas ir visatbil<PERSON>š<PERSON> mūsu spēlētājiem.\nJūs varat arī nosūtīt papildus atsauksmes, lai palīd<PERSON><PERSON><PERSON> mums turpin<PERSON>t uzlabot Minecraft.", "telemetry_info.screen.title": "Telemetrijas datu iev<PERSON>na", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "Atpazīta 32-bitu sistēma: tas, n<PERSON><PERSON><PERSON><PERSON>, var jūs iero<PERSON>ot no sp<PERSON><PERSON><PERSON><PERSON><PERSON>, jo būs <PERSON> 64-bitu sistēma!", "title.32bit.deprecation.realms": "Minecraft drīzum<PERSON> būs <PERSON> 64-bitu sistēma, kas jums liegs spēlēt vai izmantot Realms uz šīs ierīces. Jums vajadzēs manuāli atcelt Realms abonementu.", "title.32bit.deprecation.realms.check": "<PERSON><PERSON><PERSON> ner<PERSON><PERSON><PERSON>t šo ek<PERSON>u", "title.32bit.deprecation.realms.header": "Atpazīta 32-bitu sistēma", "title.credits": "Autortiesības Mojang AB. Neizplatīt!", "title.multiplayer.disabled": "Daudzspēlētā<PERSON> režīms ir atslēgts. <PERSON><PERSON><PERSON><PERSON> pārbaudiet sava Microsoft konta iestatījumus.", "title.multiplayer.disabled.banned.name": "Tev jān<PERSON>ina savs vārds pirms vari spēlēt tie<PERSON>stē", "title.multiplayer.disabled.banned.permanent": "Tavs konts ir neatgriezeniski apturēts no spēlēšanas tiešsaistē", "title.multiplayer.disabled.banned.temporary": "Tavs konts ir īslaicīgi apturēts no spēlēšanas tiešsaistē", "title.multiplayer.lan": "Daudzspēlēt<PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (3. puses serveris)", "title.multiplayer.realms": "Daudzspēlēt<PERSON><PERSON> (Realms)", "title.singleplayer": "Vienspēlētāja <PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Prefikss, %s%2$s atkal %s un %1$s visbeidzot %s un vēlreiz %1$s!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "sveiki %", "translation.test.invalid2": "sveiki %s", "translation.test.none": "Sve<PERSON>, pasaule!", "translation.test.world": "pasaule", "trim_material.minecraft.amethyst": "Ametista materiāls", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragda <PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON> mater<PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Sarkanakme<PERSON><PERSON>", "trim_material.minecraft.resin": "Sveķu materiāls", "trim_pattern.minecraft.bolt": "Bultas bruņu ornaments", "trim_pattern.minecraft.coast": "Piekrastes bruņu ornaments", "trim_pattern.minecraft.dune": "Kāpas bruņu ornaments", "trim_pattern.minecraft.eye": "Acs bruņu ornaments", "trim_pattern.minecraft.flow": "<PERSON><PERSON><PERSON><PERSON><PERSON> bru<PERSON> ornaments", "trim_pattern.minecraft.host": "<PERSON><PERSON> bruņ<PERSON>g<PERSON>", "trim_pattern.minecraft.raiser": "Pacēlāja bruņu ornaments", "trim_pattern.minecraft.rib": "Ribu bruņu ornaments", "trim_pattern.minecraft.sentry": "Aizsarga bruņu ornaments", "trim_pattern.minecraft.shaper": "Veidotāja bruņu ornaments", "trim_pattern.minecraft.silence": "Klusuma bruņu ornaments", "trim_pattern.minecraft.snout": "Snuķa bruņu ornaments", "trim_pattern.minecraft.spire": "Smailes bruņu ornaments", "trim_pattern.minecraft.tide": "Plūdmaiņu bruņu ornaments", "trim_pattern.minecraft.vex": "Niknuma bruņu ornaments", "trim_pattern.minecraft.ward": "Uzrauga bruņu ornaments", "trim_pattern.minecraft.wayfinder": "<PERSON><PERSON>a meklē<PERSON><PERSON><PERSON> bruņu ornaments", "trim_pattern.minecraft.wild": "Me<PERSON><PERSON><PERSON><PERSON> bruņu ornaments", "tutorial.bundleInsert.description": "Klikšķini ar labo taustiņu lai pievienotu priekšmetus", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON> saišķi", "tutorial.craft_planks.description": "Recepšu grāmata var pal<PERSON>", "tutorial.craft_planks.title": "Uztaisi koka dēļus", "tutorial.find_tree.description": "Sit pa to lai savāktu malku", "tutorial.find_tree.title": "<PERSON>rodi koku", "tutorial.look.description": "<PERSON><PERSON><PERSON> peli lai pagrie<PERSON>", "tutorial.look.title": "Skaties apkārt", "tutorial.move.description": "Palēkties ar %s", "tutorial.move.title": "Pārvietoties ar %s, %s, %s un %s", "tutorial.open_inventory.description": "Spied %s", "tutorial.open_inventory.title": "Atver inventāru", "tutorial.punch_tree.description": "Turiet nospiestu %s", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON><PERSON> koku", "tutorial.socialInteractions.description": "Nospied %s lai atvērtu", "tutorial.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upgrade.minecraft.netherite_upgrade": "Nezerīta <PERSON>"}