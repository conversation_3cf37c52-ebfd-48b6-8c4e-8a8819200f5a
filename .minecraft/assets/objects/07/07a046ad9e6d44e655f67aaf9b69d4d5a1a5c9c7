{"accessibility.onboarding.accessibility.button": "Tilgjengelighetsvalg...", "accessibility.onboarding.screen.narrator": "Trykk Enter for å skru på fortelleren", "accessibility.onboarding.screen.title": "Velkommen til Minecraft!\n\nVil du skru på fortelleren eller gå gjennom tilgjengelighetsinnstillingene?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "Server<PERSON><PERSON><PERSON>", "addServer.enterName": "Servernavn", "addServer.resourcePack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.disabled": "Deaktivert", "addServer.resourcePack.enabled": "Aktivert", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON>", "addServer.title": "Endre serverinfo", "advMode.command": "Konsollkommando", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Alltid aktiv", "advMode.mode.conditional": "Betinget", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Trenger redstone", "advMode.mode.sequence": "Sek<PERSON><PERSON>", "advMode.mode.unconditional": "Ubetinget", "advMode.notAllowed": "Må være en operatørspiller i kreativ modus", "advMode.notEnabled": "Kommandoblokker er ikke aktivert på denne serveren", "advMode.previousOutput": "<PERSON>rige resultat", "advMode.setCommand": "<PERSON><PERSON> for blokk", "advMode.setCommand.success": "Kommando satt: %s", "advMode.trackOutput": "Spor utdata", "advMode.triggering": "U<PERSON><PERSON>sende", "advMode.type": "Type", "advancement.advancementNotFound": "Ukjent fremskritt: %s", "advancements.adventure.adventuring_time.description": "Oppdag hvert markslag", "advancements.adventure.adventuring_time.title": "Eventyrtid", "advancements.adventure.arbalistic.description": "Drep fem ulike skapninger med ett armbrøstskudd", "advancements.adventure.arbalistic.title": "Armbrøstskyter", "advancements.adventure.avoid_vibration.description": "Snik ved en sculksensor eller forvarer så du ikke legges merke til", "advancements.adventure.avoid_vibration.title": "Snik 100", "advancements.adventure.blowback.description": "Drep et vindskrømt med en avbøyd vindkule skutt av et vindskrømt", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Få skjoldplater fra et beltedyr med en børste", "advancements.adventure.brush_armadillo.title": "Rustningsrens", "advancements.adventure.bullseye.description": "<PERSON><PERSON><PERSON> inner<PERSON>en på en blink fra minst 30 meters hold", "advancements.adventure.bullseye.title": "Blinkskudd", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Lag en dekorert potte ut fra 4 potteskår", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Varsom restaurasjon", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON> nær en tilvirker når den tilvirker en tilvirker", "advancements.adventure.crafters_crafting_crafters.title": "Tilvirker tilvirker tilvirker", "advancements.adventure.fall_from_world_height.description": "<PERSON>lev et fritt fall fra toppen av verdenen (den øvre byggegrensen) til bunnen av verdenen", "advancements.adventure.fall_from_world_height.title": "Hu<PERSON> & høyder", "advancements.adventure.heart_transplanter.description": "Sett et knirkningshjerte mellom to blokker blekeiketømmer med riktig vridning", "advancements.adventure.heart_transplanter.title": "Med hjerte på rett sted", "advancements.adventure.hero_of_the_village.description": "Beskytt en landsby mot en herjing", "advancements.adventure.hero_of_the_village.title": "Bygdens helt", "advancements.adventure.honey_block_slide.description": "<PERSON><PERSON> på en honningblokk for å dempe fallet", "advancements.adventure.honey_block_slide.title": "<PERSON> klisteret", "advancements.adventure.kill_a_mob.description": "Drep et fiendtlig monster", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "Drep et av hvert fiendtlig monster", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON> jak<PERSON>t", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Drep en skapning nær en sculkkatalysator", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Det sprer seg", "advancements.adventure.lighten_up.description": "Skrap av en kobberpære med en øks for å gjøre den lysere", "advancements.adventure.lighten_up.title": "Lysnet opp", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Beskytt en bygding mot et uønsket støt uten å forårsake en brann", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Overspenningsvern", "advancements.adventure.minecraft_trials_edition.description": "Tre inn i et prøvelseskammer", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Prøve(lses)utgave", "advancements.adventure.ol_betsy.description": "Skyt med en armbrøst", "advancements.adventure.ol_betsy.title": "Skytar'n", "advancements.adventure.overoverkill.description": "Gjør 50 hjerter skade i et eneste slag med en stridsklubbe", "advancements.adventure.overoverkill.title": "Over-overdrevent", "advancements.adventure.play_jukebox_in_meadows.description": "Gi liv til sætrene med musikkens klang fra en platespiller", "advancements.adventure.play_jukebox_in_meadows.title": "Musikkens klang", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Mål signalstyrken til en utskåret bokhylle ved å bruke en sammenligner", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Bøkenes makt", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON> opp et illevarslende hvelv ved å bruke en illevarslende prøvelsesnøkkel", "advancements.adventure.revaulting.title": "Ikke så hvelvment", "advancements.adventure.root.description": "Eventyr, utforskning og kamp", "advancements.adventure.root.title": "Eventyr", "advancements.adventure.salvage_sherd.description": "Få tak i potteskår ved å børste en mistenkelig blokk", "advancements.adventure.salvage_sherd.title": "<PERSON><PERSON><PERSON> til restene", "advancements.adventure.shoot_arrow.description": "Skyt noe med en pil", "advancements.adventure.shoot_arrow.title": "Ta sikte", "advancements.adventure.sleep_in_bed.description": "Sov i en seng for å stille ditt startpunkt", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "<PERSON><PERSON> et skjelett fra minst 50 meters avstand", "advancements.adventure.sniper_duel.title": "Snikskytterduell", "advancements.adventure.spyglass_at_dragon.description": "Se på Enderdragen med en kikkert", "advancements.adventure.spyglass_at_dragon.title": "Er det et fly?", "advancements.adventure.spyglass_at_ghast.description": "Se på en ghast med en kikkert", "advancements.adventure.spyglass_at_ghast.title": "Er det en ballong?", "advancements.adventure.spyglass_at_parrot.description": "Se på en papegøye med en kikkert", "advancements.adventure.spyglass_at_parrot.title": "Er det en fugl?", "advancements.adventure.summon_iron_golem.description": "Fremkall en jernkjempe for å forsvare en landsby", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Kast en trefork på noe.\nMerk: Å kaste bort ditt eneste våpen er kanskje ikke så lurt.", "advancements.adventure.throw_trident.title": "En bortkastet vits", "advancements.adventure.totem_of_undying.description": "Bruk et udødelighetstotem til å lure døden", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON> døden", "advancements.adventure.trade.description": "Handl med en bygding", "advancements.adventure.trade.title": "Et kupp!", "advancements.adventure.trade_at_world_height.description": "Handl med en bygding ved den øvre bygg<PERSON><PERSON>sen", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON> priser", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Smi med følgende maler minst én gang hver: spirens, snuteaktig, ribbeinsaktig, forvart, stillhetens, plagåndens, fjærens og veiviserens", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Smi med stil", "advancements.adventure.trim_with_any_armor_pattern.description": "Lag prydet rustning på en smibenk", "advancements.adventure.trim_with_any_armor_pattern.title": "Et nytt fremtreden", "advancements.adventure.two_birds_one_arrow.description": "Drep to fantomer med én spiddende pil", "advancements.adventure.two_birds_one_arrow.title": "To fugler, én pil", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON><PERSON> opp et hvelv med en prøvelsesnøkkel", "advancements.adventure.under_lock_and_key.title": "Bak lås og slå", "advancements.adventure.use_lodestone.description": "Tilknytt et kompass til en leidarstein", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, led meg hjem", "advancements.adventure.very_very_frightening.description": "Treff en bygding med lyn", "advancements.adventure.very_very_frightening.title": "Veldig veldig skremmende", "advancements.adventure.voluntary_exile.description": "Drep en herjingskaptein.\nVurder kanskje å holde deg unna landsbyer en stund...", "advancements.adventure.voluntary_exile.title": "Frivillig eksil", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "G<PERSON> på puddersnø... uten å synke ned i den", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lett som en kanin", "advancements.adventure.who_needs_rockets.description": "Bruk en vindladning til å skyte deg selv opp 8 blokker", "advancements.adventure.who_needs_rockets.title": "Hvem sa raketter?", "advancements.adventure.whos_the_pillager_now.description": "Gi en plyndrer en smak av sin egen medisin", "advancements.adventure.whos_the_pillager_now.title": "Hvem er plyndreren nå?", "advancements.empty": "Det ser ikke ut til å være noe her...", "advancements.end.dragon_breath.description": "Saml dragens ånde i en glassflaske", "advancements.end.dragon_breath.title": "Du trenger en mint", "advancements.end.dragon_egg.description": "Hold drageegget", "advancements.end.dragon_egg.title": "Den neste generasjonen", "advancements.end.elytra.description": "Finn en elytra", "advancements.end.elytra.title": "Ut i det blå", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON><PERSON><PERSON> ø<PERSON>n", "advancements.end.enter_end_gateway.title": "Øde rømningsvei", "advancements.end.find_end_city.description": "<PERSON>ig på, ingen fare!", "advancements.end.find_end_city.title": "Byen ved enden av spillet", "advancements.end.kill_dragon.description": "Lykke til", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Svev over 50 blokker fra et shulkerangrep", "advancements.end.levitate.title": "<PERSON><PERSON>t uts<PERSON>t her oppefra", "advancements.end.respawn_dragon.description": "Fremkall Enderdragen på nytt", "advancements.end.respawn_dragon.title": "Enden... igjen...", "advancements.end.root.description": "Eller <PERSON><PERSON><PERSON><PERSON>?", "advancements.end.root.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON>å en hjelpeånd til å kaste en kake på en noteblokk", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Bursdagssang", "advancements.husbandry.allay_deliver_item_to_player.description": "Få en hjel<PERSON>ånd til å levere ting til deg", "advancements.husbandry.allay_deliver_item_to_player.title": "Du har en venn i meg", "advancements.husbandry.axolotl_in_a_bucket.description": "Fang en axolotl i en bøtte", "advancements.husbandry.axolotl_in_a_bucket.title": "Det søteste rovdyret", "advancements.husbandry.balanced_diet.description": "Spis alt som er spiselig, selv om det ikke er bra for deg", "advancements.husbandry.balanced_diet.title": "Et balansert kosthold", "advancements.husbandry.breed_all_animals.description": "Avle alle dyrene!", "advancements.husbandry.breed_all_animals.title": "To og to", "advancements.husbandry.breed_an_animal.description": "Avle to dyr med hverandre", "advancements.husbandry.breed_an_animal.title": "Papegøyene og flaggermusene", "advancements.husbandry.complete_catalogue.description": "Tem en katt av hvert slag!", "advancements.husbandry.complete_catalogue.title": "En komplett kat(t)alog", "advancements.husbandry.feed_snifflet.description": "<PERSON> en snufsling", "advancements.husbandry.feed_snifflet.title": "Små snufs", "advancements.husbandry.fishy_business.description": "Fang en fisk", "advancements.husbandry.fishy_business.title": "Skitt fiske!", "advancements.husbandry.froglights.description": "Ha et av hvert froskelys i inventaret ditt", "advancements.husbandry.froglights.title": "Med våre krefter i hop!", "advancements.husbandry.kill_axolotl_target.description": "Slå deg sammen med en axolotl og vinn en kamp", "advancements.husbandry.kill_axolotl_target.title": "Vennskapets helbredende kraft!", "advancements.husbandry.leash_all_frog_variants.description": "Ha en frosk av hvert slag i bånd", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON>r gjengen bykser gjennom bygda", "advancements.husbandry.make_a_sign_glow.description": "Få teksten på et hvilket som helst slags skilt til å gløde", "advancements.husbandry.make_a_sign_glow.title": "En ramme av skinn", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON><PERSON> et grev med en netherittbarre, for så å tenke over dine livsvalg", "advancements.husbandry.netherite_hoe.title": "Alvorlig engasjement", "advancements.husbandry.obtain_sniffer_egg.description": "Få tak i et snufseregg", "advancements.husbandry.obtain_sniffer_egg.title": "Lukter merkelig", "advancements.husbandry.place_dried_ghast_in_water.description": "Sett ut en blokk uttørket ghast i vann", "advancements.husbandry.place_dried_ghast_in_water.title": "Slukk tørsten!", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant et hvilket som helst snufset fram frø", "advancements.husbandry.plant_any_sniffer_seed.title": "Dyrking av fortiden", "advancements.husbandry.plant_seed.description": "Plant et frø og se det vokse", "advancements.husbandry.plant_seed.title": "Et overgrodd sted", "advancements.husbandry.remove_wolf_armor.description": "<PERSON><PERSON><PERSON> ulverustning fra en ulv med en saks", "advancements.husbandry.remove_wolf_armor.title": "Avskalling", "advancements.husbandry.repair_wolf_armor.description": "Reparer skadet ulverustning med beltedyrskjoldplater", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> god som ny", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Seil sammen med en geit", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Ridende havbukk", "advancements.husbandry.root.description": "<PERSON>n er full av venner og mat", "advancements.husbandry.root.title": "Landbruk", "advancements.husbandry.safely_harvest_honey.description": "Bruk et bål til å samle honning fra en bikube på glassflaske uten å hisse opp biene", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON> vår gjest", "advancements.husbandry.silk_touch_nest.description": "Flytt et biebol eller bikube med 3 bier i med silkeberøring", "advancements.husbandry.silk_touch_nest.title": "Ingen bivirkning", "advancements.husbandry.tactical_fishing.description": "Fang en fisk... uten fiskestang!", "advancements.husbandry.tactical_fishing.title": "Taktisk fiske", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON> et rumpetroll i en bøtte", "advancements.husbandry.tadpole_in_a_bucket.title": "Bøttedyr", "advancements.husbandry.tame_an_animal.description": "Tem et dyr", "advancements.husbandry.tame_an_animal.title": "Bestevenner for alltid", "advancements.husbandry.wax_off.description": "Skrap bort voksen fra en kobberblokk", "advancements.husbandry.wax_off.title": "Voks av", "advancements.husbandry.wax_on.description": "Voks inn en kobberblokk", "advancements.husbandry.wax_on.title": "<PERSON><PERSON>s på", "advancements.husbandry.whole_pack.description": "Tem en av hver ulvevariant", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> flokken", "advancements.nether.all_effects.description": "Ha alle effektene samtidig", "advancements.nether.all_effects.title": "Hvordan kom vi hit?", "advancements.nether.all_potions.description": "Ha alle bryggeffektene samtidig", "advancements.nether.all_potions.title": "En rasende cocktail", "advancements.nether.brew_potion.description": "B<PERSON>gg en brygg", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON> br<PERSON>", "advancements.nether.charge_respawn_anchor.description": "Lad et livsanker helt opp", "advancements.nether.charge_respawn_anchor.title": "<PERSON>kke ak<PERSON> \"ni\" liv", "advancements.nether.create_beacon.description": "Lag og sett ut en varde", "advancements.nether.create_beacon.title": "Bring hjem varden", "advancements.nether.create_full_beacon.description": "Gi en varde full styrke", "advancements.nether.create_full_beacon.title": "Vardeb<PERSON>gger", "advancements.nether.distract_piglin.description": "Avled en piglin med gull", "advancements.nether.distract_piglin.title": "Å glimmer", "advancements.nether.explore_nether.description": "Oppdag alle nethermarkslag", "advancements.nether.explore_nether.title": "<PERSON><PERSON>", "advancements.nether.fast_travel.description": "Bruk Nether til å reise 7 km i Oververdenen", "advancements.nether.fast_travel.title": "En boble mellom dimensjonene", "advancements.nether.find_bastion.description": "Utforsk en bastionruin", "advancements.nether.find_bastion.title": "<PERSON><PERSON> gamle dager", "advancements.nether.find_fortress.description": "Bryt deg inn i en netherfestning", "advancements.nether.find_fortress.title": "En fryktelig festning", "advancements.nether.get_wither_skull.description": "Få tak i skallen til et witherskjelett", "advancements.nether.get_wither_skull.title": "Skremmende skummelt skjelett", "advancements.nether.loot_bastion.description": "<PERSON><PERSON><PERSON> fra en kiste i en bastionruin", "advancements.nether.loot_bastion.title": "Stridsgriser", "advancements.nether.netherite_armor.description": "Tilegn deg alle plaggene til en netherittrustning", "advancements.nether.netherite_armor.title": "Dekk meg med skrap", "advancements.nether.obtain_ancient_debris.description": "Få tak i eldgammelt skrap", "advancements.nether.obtain_ancient_debris.title": "Skjult i dypet", "advancements.nether.obtain_blaze_rod.description": "Skill en stang fra et flammeskrømt", "advancements.nether.obtain_blaze_rod.title": "Inn i ilden", "advancements.nether.obtain_crying_obsidian.description": "Få tak i gråtende obsidian", "advancements.nether.obtain_crying_obsidian.title": "Hvem hakker løk?", "advancements.nether.return_to_sender.description": "Drep en ghast med ei ildkule", "advancements.nether.return_to_sender.title": "Send i retur", "advancements.nether.ride_strider.description": "Ri på en lavavandrer med en forvridd sopp på stang", "advancements.nether.ride_strider.title": "<PERSON><PERSON> b<PERSON>ten har bein", "advancements.nether.ride_strider_in_overworld_lava.description": "Ta en laaaang ridetur på en lavavandrer på en lavasjø i Oververdenen", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON>k<PERSON><PERSON> som hjemme", "advancements.nether.root.description": "Ta med sommerklær", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "På visning", "advancements.nether.uneasy_alliance.description": "Redd en ghast fra <PERSON>, ta den trygt med hjem til oververdenen... og drep den", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> pakt", "advancements.nether.use_lodestone.description": "Tilknytt et kompass til en leidarstein", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, led meg hjem", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Svekk og så kurer en zombiebygding", "advancements.story.cure_zombie_villager.title": "Zombiedoktor", "advancements.story.deflect_arrow.description": "Avbø<PERSON> et prosjektil med et skjold", "advancements.story.deflect_arrow.title": "Ikke i dag, takk", "advancements.story.enchant_item.description": "Fortryll en gjenstand med et trolldomsbord", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Gå inn i Enderportalen", "advancements.story.enter_the_end.title": "Enden?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, tenn, og gå inn i en netherportal", "advancements.story.enter_the_nether.title": "Vi må gå dypere", "advancements.story.follow_ender_eye.description": "Følg et enderøye", "advancements.story.follow_ender_eye.title": "Øyesikt", "advancements.story.form_obsidian.description": "Få tak i en blokk av obsidian", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.iron_tools.description": "<PERSON><PERSON><PERSON> hakken din", "advancements.story.iron_tools.title": "Hakke-peiling", "advancements.story.lava_bucket.description": "Fyll en bøtte med lava", "advancements.story.lava_bucket.title": "<PERSON><PERSON> g<PERSON>", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "<PERSON>amanter!", "advancements.story.mine_stone.description": "Hugg stein med din nye hakke", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "Rust deg ut med et plagg jernrustning", "advancements.story.obtain_armor.title": "Rust opp", "advancements.story.root.description": "Hjertet og fortellingen til spillet", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamantrustning redder liv", "advancements.story.shiny_gear.title": "Dekk meg med diamanter", "advancements.story.smelt_iron.description": "Smelt fram en jernbarre", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "Lag en bedre hakke", "advancements.story.upgrade_tools.title": "Skaff en oppgradering", "advancements.toast.challenge": "Utfordring fullført!", "advancements.toast.goal": "M<PERSON>l nådd!", "advancements.toast.task": "Fremskritt gjort!", "argument.anchor.invalid": "%s er en ugyldig ankerposisjon for enhet", "argument.angle.incomplete": "Ufullstendig (forventet 1 vinkel)", "argument.angle.invalid": "Ugyldig vinkel", "argument.block.id.invalid": "\"%s\" er en ukjent blokktype", "argument.block.property.duplicate": "Egenskapen '%s' kan bare bli satt en gang for blokken %s", "argument.block.property.invalid": "Blokken %s aksepterer ikke '%s' som %s egenskap", "argument.block.property.novalue": "Forventet verdi for egenskap '%s' på blokk %s", "argument.block.property.unclosed": "Forventet lukkende ] for egenskapene til blokken", "argument.block.property.unknown": "Blokken %s har ikke egenskapen '%s'", "argument.block.tag.disallowed": "Tagger er ikke tillat her, bare virkelige blokker", "argument.color.invalid": "Uk<PERSON><PERSON> farge '%s'", "argument.component.invalid": "Ugyldig nettpratskomponent: %s", "argument.criteria.invalid": "Ukjent kriterie '%s'", "argument.dimension.invalid": "Ukjent dimensjon '%s'", "argument.double.big": "Dobbeltallet kan ikke være større enn %s. Fant %s", "argument.double.low": "Dobbeltallet kan ikke være mindre enn %s. <PERSON>t %s", "argument.entity.invalid": "Ugyldig navn eller UUID", "argument.entity.notfound.entity": "Ingen enheter funnet", "argument.entity.notfound.player": "Ingen spillere funnet", "argument.entity.options.advancements.description": "Spillere med fremskritt", "argument.entity.options.distance.description": "Avstand fra enhet", "argument.entity.options.distance.negative": "Avstanden kan ikke være negativ", "argument.entity.options.dx.description": "Enheter mellom x og x + dx", "argument.entity.options.dy.description": "Enheter mellom y og y + dy", "argument.entity.options.dz.description": "Enheter mellom z og z + dz", "argument.entity.options.gamemode.description": "Spillere med spillmodus", "argument.entity.options.inapplicable": "Alternativet '%s' er ikke egnet her", "argument.entity.options.level.description": "Erfaringsnivå", "argument.entity.options.level.negative": "Niv<PERSON><PERSON> bør ikke være negativt", "argument.entity.options.limit.description": "Maksimalt antall enheter til å komme tilbake", "argument.entity.options.limit.toosmall": "Grensen må være minst 1", "argument.entity.options.mode.invalid": "Ugyldig eller ukjent spillmodus '%s'", "argument.entity.options.name.description": "Enhetens navn", "argument.entity.options.nbt.description": "Enheter med NBT", "argument.entity.options.predicate.description": "Tilpasset predikat", "argument.entity.options.scores.description": "Enheter med poengsummer", "argument.entity.options.sort.description": "Sorter enhetene", "argument.entity.options.sort.irreversible": "Ugyldig eller ukjent sorteringstype '%s'", "argument.entity.options.tag.description": "Enheter med tagg", "argument.entity.options.team.description": "Enheter på laget", "argument.entity.options.type.description": "Enheter av typen", "argument.entity.options.type.invalid": "Ugyldig eller ukjent ehnhetstype '%s'", "argument.entity.options.unknown": "Ukjent alternativ '%s'", "argument.entity.options.unterminated": "Forventet slutt på alternativer", "argument.entity.options.valueless": "Forventet verdi for alternativet '%s'", "argument.entity.options.x.description": "x-p<PERSON><PERSON>", "argument.entity.options.x_rotation.description": "Enhetens x-rotasjon", "argument.entity.options.y.description": "y-p<PERSON><PERSON>", "argument.entity.options.y_rotation.description": "Enhetens y-rotasjon", "argument.entity.options.z.description": "z-posisjon", "argument.entity.selector.allEntities": "Alle enheter", "argument.entity.selector.allPlayers": "Alle spillere", "argument.entity.selector.missing": "Mangler velger-type", "argument.entity.selector.nearestEntity": "Nærmeste enhet", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON> spiller", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON> ikke tillatt", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON> spiller", "argument.entity.selector.self": "Gjeldende enhet", "argument.entity.selector.unknown": "Ukjent velgertype '%s'", "argument.entity.toomany": "Bare én enhet er tillatt, men den angitte velgeren tillater flere enn én", "argument.enum.invalid": "Ugyldig verdi \"%s\"", "argument.float.big": "Flyttallet kan ikke være større enn %s. Fant %s", "argument.float.low": "Flyttallet kan ikke være mindre enn %s. Fant %s", "argument.gamemode.invalid": "Ukjent spillmodus: %s", "argument.hexcolor.invalid": "Ugyldig hex-fargekode '%s'", "argument.id.invalid": "Ugyldig ID", "argument.id.unknown": "Ukjent ID: %s", "argument.integer.big": "Heltallet kan ikke være større enn %s. Fant %s", "argument.integer.low": "Heltallet kan ikke være mindre enn %s. Fant %s", "argument.item.id.invalid": "<PERSON>k<PERSON><PERSON> gje<PERSON>and '%s'", "argument.item.tag.disallowed": "Tagger er ikke tillat her, bare virkelige gjenstander", "argument.literal.incorrect": "Strengkonstanten %s var forventet", "argument.long.big": "Lang-verdi kan ikke være større enn %s. <PERSON>t %s", "argument.long.low": "Lang-verdi kan ikke være mindre enn %s. <PERSON> %s", "argument.message.too_long": "Nettpratsmelding var for lang (%s > høyst %s tegn)", "argument.nbt.array.invalid": "Ugyldig oppstillingstype '%s'", "argument.nbt.array.mixed": "Kan ikke sette %s inn i %s", "argument.nbt.expected.compound": "Forventet compound-tagg", "argument.nbt.expected.key": "Forventet nøkkel", "argument.nbt.expected.value": "Forventet verdi", "argument.nbt.list.mixed": "Kan ikke sette %s inn i listen over %s", "argument.nbt.trailing": "Videre data var ikke ventet", "argument.player.entities": "<PERSON>e spillere kan bli påvirket av denne kommandoen, men den angitte velgeren inkluderer også enheter", "argument.player.toomany": "Bare én spiller er tillatt, men den angitte velgeren tillater flere enn én", "argument.player.unknown": "<PERSON>ne spilleren finnes ikke", "argument.pos.missing.double": "En koordinat forventet", "argument.pos.missing.int": "Blokkposisjon forventet", "argument.pos.mixed": "Kan ikke blande verdenskoordinater og hemmelige koordinater (alt må enten bruke ^ eller ikke noe)", "argument.pos.outofbounds": "Stedet er for langt vekk.", "argument.pos.outofworld": "Dette stedet er utenfor denne verdenen!", "argument.pos.unloaded": "<PERSON>te stedet er ikke lastet inn", "argument.pos2d.incomplete": "Ufullstendig (forventet 2 koordinater)", "argument.pos3d.incomplete": "Ufullstendig (forventet 3 koordinater)", "argument.range.empty": "Forventet verdi eller verdiområde", "argument.range.ints": "<PERSON>e hele tall lov, ikke desimaler", "argument.range.swapped": "Minimum kan ikke være større enn maksimum", "argument.resource.invalid_type": "Element '%s' har feil type '%s' (forventet '%s')", "argument.resource.not_found": "Kan ikke finne element '%s' av type '%s'", "argument.resource_or_id.failed_to_parse": "Lyktes ikke med å tolke struktur: %s", "argument.resource_or_id.invalid": "Ugyldig <PERSON> eller tagg", "argument.resource_or_id.no_such_element": "Kan ikke finne element '%s' i register '%s'", "argument.resource_selector.not_found": "Ingen funn for utvelger '%s' av type '%s'", "argument.resource_tag.invalid_type": "Tagg '%s' har feil type '%s' (forventet '%s')", "argument.resource_tag.not_found": "Kan ikke finne tagg '%s' av type '%s'", "argument.rotation.incomplete": "Ufullstendig (forventet 2 koordinater)", "argument.scoreHolder.empty": "<PERSON>nne ikke finne noen relevante poengsumholdere", "argument.scoreboardDisplaySlot.invalid": "Ukjent visningsrute '%s'", "argument.style.invalid": "Ugyldig stil: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON><PERSON> tikk må være ikke-negativt", "argument.time.invalid_unit": "Ugyldig enhet", "argument.time.tick_count_too_low": "Antall tikk kan ikke være mindre enn %s, fant %s", "argument.uuid.invalid": "Ugyldig UUID", "argument.waypoint.invalid": "Ut<PERSON><PERSON> enhet er ikke et veipunkt", "arguments.block.tag.unknown": "\"%s\" er en ukjent blokktagg", "arguments.function.tag.unknown": "<PERSON><PERSON><PERSON><PERSON> funksjonstagg '%s'", "arguments.function.unknown": "Ukjent funksjon %s", "arguments.item.component.expected": "Forventet gjenstandskomponent", "arguments.item.component.malformed": "Feilaktig '%s'-komponent: '%s'", "arguments.item.component.repeated": "Gjenstandskomponent «%s» ble gje<PERSON><PERSON>, men kun én verdi kan oppgis", "arguments.item.component.unknown": "Ukjent gjenstandskomponent '%s'", "arguments.item.malformed": "Feilaktig gjenstand: '%s'", "arguments.item.overstacked": "%s kan bare hopes opp til %s", "arguments.item.predicate.malformed": "Feilaktig '%s'-predikat: '%s'", "arguments.item.predicate.unknown": "Ukjent gjenstandspredikat '%s'", "arguments.item.tag.unknown": "Ukjent gjenstandstagg '%s'", "arguments.nbtpath.node.invalid": "Ugyldig NBT-baneelement", "arguments.nbtpath.nothing_found": "Fant ingen samsvarende elementer %s", "arguments.nbtpath.too_deep": "Resulterende NBT for dypt nøstet", "arguments.nbtpath.too_large": "Resulterende NBT for stor", "arguments.objective.notFound": "\"%s\" er et ukjent mål på resultattavlen", "arguments.objective.readonly": "Målet \"%s\" på resultattavlen er skrivebeskyttet", "arguments.operation.div0": "Kan ikke dividere på null", "arguments.operation.invalid": "Ugyldig operasjon", "arguments.swizzle.invalid": "Ugyldig aksekombinasjon av 'x' 'y' og 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Rustning", "attribute.name.armor_toughness": "Rustningsherdighet", "attribute.name.attack_damage": "Angrepsskade", "attribute.name.attack_knockback": "Angrepstilbakeslag", "attribute.name.attack_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "Blokkbrytingsfart", "attribute.name.block_interaction_range": "Blokkrekkevidde", "attribute.name.burning_time": "Fyrtid", "attribute.name.camera_distance": "Kameraavstand", "attribute.name.entity_interaction_range": "Enhetsrekkevidde", "attribute.name.explosion_knockback_resistance": "Eksplosjonstilbakeslagsmotstand", "attribute.name.fall_damage_multiplier": "Fallskademultiplikator", "attribute.name.flying_speed": "Flygefart", "attribute.name.follow_range": "Skapningsforfølgelsesvidde", "attribute.name.generic.armor": "Rustning", "attribute.name.generic.armor_toughness": "Rustningsherdighet", "attribute.name.generic.attack_damage": "Angrepsstyrke", "attribute.name.generic.attack_knockback": "Tilbakeslag på angrep", "attribute.name.generic.attack_speed": "Angrepshastighet", "attribute.name.generic.block_interaction_range": "Blokkrekkevidde", "attribute.name.generic.burning_time": "Fyrtid", "attribute.name.generic.entity_interaction_range": "Enhetsrekkevidde", "attribute.name.generic.explosion_knockback_resistance": "Eksplosjonstilbakeslagsmotstand", "attribute.name.generic.fall_damage_multiplier": "Multiplikator for fallskade", "attribute.name.generic.flying_speed": "Flygefart", "attribute.name.generic.follow_range": "Mob-forfølgelsesavstand", "attribute.name.generic.gravity": "Tyngdekraft", "attribute.name.generic.jump_strength": "Hoppstyrke", "attribute.name.generic.knockback_resistance": "Tilbakeslagsmotstand", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "Høyest absorpsjon", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON><PERSON>e", "attribute.name.generic.movement_efficiency": "Fremkommelighet", "attribute.name.generic.movement_speed": "Fart", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Steghøyde", "attribute.name.generic.water_movement_efficiency": "Vannfremkommelighet", "attribute.name.gravity": "Tyngdekraft", "attribute.name.horse.jump_strength": "Hestehoppstyrke", "attribute.name.jump_strength": "Hoppstyrke", "attribute.name.knockback_resistance": "Tilbakeslagsmotstand", "attribute.name.luck": "<PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Høyeste absorbsjonsmengde", "attribute.name.max_health": "Høyest antall liv", "attribute.name.mining_efficiency": "Graveferdighet", "attribute.name.movement_efficiency": "Fremkommelighet", "attribute.name.movement_speed": "Fart", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blokkbrytningsfart", "attribute.name.player.block_interaction_range": "Blokkrekkevidde", "attribute.name.player.entity_interaction_range": "Enhetsrekkevidde", "attribute.name.player.mining_efficiency": "<PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.submerged_mining_speed": "Gravefart under vann", "attribute.name.player.sweeping_damage_ratio": "Sveipeskaderate", "attribute.name.safe_fall_distance": "<PERSON><PERSON>", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.spawn_reinforcements": "Zombieforsterkninger", "attribute.name.step_height": "Steghøyde", "attribute.name.submerged_mining_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.sweeping_damage_ratio": "Sveipeskaderate", "attribute.name.tempt_range": "Skapningslokkevidde", "attribute.name.water_movement_efficiency": "Vannfremkommelighet", "attribute.name.waypoint_receive_range": "Mottagelsesvidde for veipunkt", "attribute.name.waypoint_transmit_range": "Kringkastelsesvidde for veipunkt", "attribute.name.zombie.spawn_reinforcements": "Zombieforsterkninger", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Bambusjungel", "biome.minecraft.basalt_deltas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.beach": "Strand", "biome.minecraft.birch_forest": "Bjørkeskog", "biome.minecraft.cherry_grove": "Kirsebærlund", "biome.minecraft.cold_ocean": "Kaldt hav", "biome.minecraft.crimson_forest": "Karmosinskog", "biome.minecraft.dark_forest": "Mørk skog", "biome.minecraft.deep_cold_ocean": "<PERSON>ypt kaldt hav", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON>ypt frossent hav", "biome.minecraft.deep_lukewarm_ocean": "Dypt lunkent hav", "biome.minecraft.deep_ocean": "Dypt hav", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Dryppsteinsgrotter", "biome.minecraft.end_barrens": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_highlands": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_midlands": "Enden - Midtlandene", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "Blomsterskog", "biome.minecraft.forest": "Skog", "biome.minecraft.frozen_ocean": "Frossent hav", "biome.minecraft.frozen_peaks": "<PERSON><PERSON>ne tinder", "biome.minecraft.frozen_river": "Frossen elv", "biome.minecraft.grove": "<PERSON>", "biome.minecraft.ice_spikes": "<PERSON><PERSON><PERSON>", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Lunk<PERSON> hav", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON> grotter", "biome.minecraft.mangrove_swamp": "Mangrovesump", "biome.minecraft.meadow": "Eng", "biome.minecraft.mushroom_fields": "Soppsletter", "biome.minecraft.nether_wastes": "Netherødemark", "biome.minecraft.ocean": "Hav", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON><PERSON> bjørkeskog", "biome.minecraft.old_growth_pine_taiga": "Urgammel furutaiga", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON> hage", "biome.minecraft.plains": "Sletter", "biome.minecraft.river": "Elv", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "Enden - S<PERSON><PERSON> øyer", "biome.minecraft.snowy_beach": "Snødekt strand", "biome.minecraft.snowy_plains": "Snødekte vidder", "biome.minecraft.snowy_slopes": "Snødekte fjellsider", "biome.minecraft.snowy_taiga": "Snøtaiga", "biome.minecraft.soul_sand_valley": "Sjelesanddal", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON> jungel", "biome.minecraft.stony_peaks": "<PERSON><PERSON> f<PERSON>", "biome.minecraft.stony_shore": "Svaberg", "biome.minecraft.sunflower_plains": "Solsikkeeng", "biome.minecraft.swamp": "Sump", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON><PERSON>", "biome.minecraft.the_void": "Tomrommet", "biome.minecraft.warm_ocean": "Varmt hav", "biome.minecraft.warped_forest": "Forvridd skog", "biome.minecraft.windswept_forest": "Vindslitt skog", "biome.minecraft.windswept_gravelly_hills": "Vindslitte g<PERSON>åser", "biome.minecraft.windswept_hills": "Vindslitte åser", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON> savanne", "biome.minecraft.wooded_badlands": "Skogdekt steinøde", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "Akasiedør", "block.minecraft.acacia_fence": "Akasiegjer<PERSON>", "block.minecraft.acacia_fence_gate": "Akasiegrind", "block.minecraft.acacia_hanging_sign": "Hengende akasieskilt", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Akasietømmer", "block.minecraft.acacia_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "Akasietrykkplate", "block.minecraft.acacia_sapling": "Akasiespire", "block.minecraft.acacia_sign": "Akasieskilt", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Akasietrapp", "block.minecraft.acacia_trapdoor": "Akasiefallem", "block.minecraft.acacia_wall_hanging_sign": "Vegghengende akasieskilt", "block.minecraft.acacia_wall_sign": "Akasieveggskilt", "block.minecraft.acacia_wood": "Akasietre", "block.minecraft.activator_rail": "Aktiveringsskinne", "block.minecraft.air": "Luft", "block.minecraft.allium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametystblokk", "block.minecraft.amethyst_cluster": "Ametystklase", "block.minecraft.ancient_debris": "Eldgammelt skrap", "block.minecraft.andesite": "<PERSON><PERSON>", "block.minecraft.andesite_slab": "Helle av and<PERSON>tt", "block.minecraft.andesite_stairs": "Trapp av andesitt", "block.minecraft.andesite_wall": "<PERSON>r av and<PERSON><PERSON>", "block.minecraft.anvil": "Ambolt", "block.minecraft.attached_melon_stem": "Tilknyttet melonstilk", "block.minecraft.attached_pumpkin_stem": "Festet gresskarstilk", "block.minecraft.azalea": "Asalea", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblokk", "block.minecraft.bamboo_button": "Bambusknapp", "block.minecraft.bamboo_door": "Bambusdør", "block.minecraft.bamboo_fence": "Bambusgjerde", "block.minecraft.bamboo_fence_gate": "Bambusgrind", "block.minecraft.bamboo_hanging_sign": "Hengende bambusskilt", "block.minecraft.bamboo_mosaic": "Bambusmosaikk", "block.minecraft.bamboo_mosaic_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic_stairs": "Bambusmosaikktrapp", "block.minecraft.bamboo_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_pressure_plate": "Bambustrykkplate", "block.minecraft.bamboo_sapling": "Bambusskudd", "block.minecraft.bamboo_sign": "Bambusskilt", "block.minecraft.bamboo_slab": "Bambushelle", "block.minecraft.bamboo_stairs": "Bambustrapp", "block.minecraft.bamboo_trapdoor": "Bambusfallem", "block.minecraft.bamboo_wall_hanging_sign": "Vegghengende bambusskilt", "block.minecraft.bamboo_wall_sign": "Bambusveggskilt", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.brown": "<PERSON><PERSON> bunn", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.gray": "<PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.light_blue": "Lyseblå bunn", "block.minecraft.banner.base.light_gray": "Lysegr<PERSON> bunn", "block.minecraft.banner.base.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.magenta": "<PERSON><PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.pink": "<PERSON> bunn", "block.minecraft.banner.base.purple": "<PERSON><PERSON> bunn", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON> b<PERSON>n", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON> bunn", "block.minecraft.banner.base.yellow": "<PERSON><PERSON> bunn", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.orange": "Or<PERSON><PERSON> bord", "block.minecraft.banner.border.pink": "<PERSON> bord", "block.minecraft.banner.border.purple": "<PERSON><PERSON> bord", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON> bord", "block.minecraft.banner.border.yellow": "<PERSON><PERSON> bord", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.pink": "<PERSON>", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.blue": "Blå skive", "block.minecraft.banner.circle.brown": "<PERSON><PERSON> skive", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.gray": "Grå skive", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.light_blue": "Lyseblå skive", "block.minecraft.banner.circle.light_gray": "Lysegrå skive", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.orange": "Oransje skive", "block.minecraft.banner.circle.pink": "<PERSON> skive", "block.minecraft.banner.circle.purple": "<PERSON><PERSON> skive", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON> skive", "block.minecraft.banner.circle.yellow": "<PERSON>ul skive", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.blue": "B<PERSON>å creeper", "block.minecraft.banner.creeper.brown": "<PERSON>run creeper", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.light_blue": "Lyseblå creeper", "block.minecraft.banner.creeper.light_gray": "Lysegr<PERSON> creeper", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.pink": "<PERSON> creeper", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON> creeper", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.yellow": "Gul creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON><PERSON> and<PERSON>", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON>tt andrea<PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Turkist and<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "Lyseblått andreaskors", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.pink": "<PERSON>", "block.minecraft.banner.cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON>t bord med tannsnitt", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.cyan": "Turkist bord med tannsnitt", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.light_blue": "Lys<PERSON><PERSON><PERSON>tt bord med tannsnitt", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.orange": "Oransje bord med tannsnitt", "block.minecraft.banner.curly_border.pink": "<PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON> bord med tannsnitt", "block.minecraft.banner.curly_border.yellow": "<PERSON>ult bord med tannsnitt", "block.minecraft.banner.diagonal_left.black": "Svart venstreskrådeling", "block.minecraft.banner.diagonal_left.blue": "Blå venstreskrådeling", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "Turkis venstreskrå<PERSON>", "block.minecraft.banner.diagonal_left.gray": "Grå venstreskrådeling", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON>nst<PERSON>krå<PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "Lyseblå venstreskrådeling", "block.minecraft.banner.diagonal_left.light_gray": "Lysegrå venstreskrådeling", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> venstreskrådeling", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.orange": "Oransje venstreskrådeling", "block.minecraft.banner.diagonal_left.pink": "<PERSON>", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON>rå<PERSON>", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.diagonal_left.yellow": "Gul venstreskrådeling", "block.minecraft.banner.diagonal_right.black": "Svart skrådeling", "block.minecraft.banner.diagonal_right.blue": "Blå skrådeling", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.diagonal_right.gray": "Grå skrådeling", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "Lyseblå skrådeling", "block.minecraft.banner.diagonal_right.light_gray": "Lysegrå skrådeling", "block.minecraft.banner.diagonal_right.lime": "Limegr<PERSON><PERSON> skrådeling", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.orange": "Oransje s<PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON>", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.yellow": "Gul skrådeling", "block.minecraft.banner.diagonal_up_left.black": "Svart skrådel<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.blue": "Blå skrådeling (omvendt)", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.gray": "Grå skrådeling (omvendt)", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.light_blue": "Lyseblå skrådeling (omvendt)", "block.minecraft.banner.diagonal_up_left.light_gray": "Lysegrå skrådeling (omvendt)", "block.minecraft.banner.diagonal_up_left.lime": "Limegrø<PERSON> omvendt skrådeling", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.orange": "Oransje <PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON> s<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.black": "Svart venstreskrådeling (omvendt)", "block.minecraft.banner.diagonal_up_right.blue": "Blå venstreskrådeling (omvendt)", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.cyan": "Turkis venstreskrå<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.gray": "Grå venstreskrådeling (omvendt)", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON>nst<PERSON>rå<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.light_blue": "Lyseblå venstreskrådeling (omvendt)", "block.minecraft.banner.diagonal_up_right.light_gray": "Lysegrå venstreskrådeling (omvendt)", "block.minecraft.banner.diagonal_up_right.lime": "Limegrønn omvendt venstreskrådeling", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.orange": "Oransje venstreskrådeling (omvendt)", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON>nstres<PERSON>rå<PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> (omvendt)", "block.minecraft.banner.diagonal_up_right.yellow": "Gul venstreskrådeling (omvendt)", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON> virvel", "block.minecraft.banner.flow.blue": "Blå virvel", "block.minecraft.banner.flow.brown": "<PERSON><PERSON> virvel", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.gray": "Gr<PERSON> virvel", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.light_blue": "Lyseblå virvel", "block.minecraft.banner.flow.light_gray": "Lysegrå virvel", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> virvel", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON>a virvel", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flow.pink": "<PERSON> virvel", "block.minecraft.banner.flow.purple": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> vir<PERSON>", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> virvel", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON> blomst", "block.minecraft.banner.flower.blue": "Blå blomst", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> blo<PERSON>t", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON> blo<PERSON>t", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON> blomst", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.flower.light_blue": "Lyseblå blomst", "block.minecraft.banner.flower.light_gray": "Lysegrå blomst", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.flower.orange": "Orans<PERSON> blo<PERSON>", "block.minecraft.banner.flower.pink": "<PERSON> b<PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON> blo<PERSON>t", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> blo<PERSON>t", "block.minecraft.banner.globe.black": "Svart globus", "block.minecraft.banner.globe.blue": "Blå globus", "block.minecraft.banner.globe.brown": "Brun globus", "block.minecraft.banner.globe.cyan": "Turkis globus", "block.minecraft.banner.globe.gray": "Grå globus", "block.minecraft.banner.globe.green": "Grønn globus", "block.minecraft.banner.globe.light_blue": "Lyseblå globus", "block.minecraft.banner.globe.light_gray": "Lysegrå globus", "block.minecraft.banner.globe.lime": "Limegrønn globus", "block.minecraft.banner.globe.magenta": "L<PERSON><PERSON>lla globus", "block.minecraft.banner.globe.orange": "Oransje globus", "block.minecraft.banner.globe.pink": "Rosa globus", "block.minecraft.banner.globe.purple": "Lilla globus", "block.minecraft.banner.globe.red": "Rød globus", "block.minecraft.banner.globe.white": "Hvit globus", "block.minecraft.banner.globe.yellow": "Gul globus", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.blue": "Blå overgang", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON> overgang", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.light_blue": "Lyseblå overgang", "block.minecraft.banner.gradient.light_gray": "Lysegrå overgang", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.pink": "<PERSON> overgang", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON> overgang", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON> overgang", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON> overgang", "block.minecraft.banner.gradient_up.black": "Svart overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.blue": "Blå overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.gray": "Grå overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.light_blue": "Lyseblå overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.light_gray": "Lysegrå overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.lime": "Lim<PERSON><PERSON><PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.orange": "Oransje overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.pink": "<PERSON> overgang fra skjold<PERSON>ten", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON> overgang fra skjoldfoten", "block.minecraft.banner.guster.black": "Svart vindkaster", "block.minecraft.banner.guster.blue": "Blå vindkaster", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "Grå vindkaster", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON> vindkaster", "block.minecraft.banner.guster.light_blue": "Lyseblå vindkaster", "block.minecraft.banner.guster.light_gray": "Lysegrå vindkaster", "block.minecraft.banner.guster.lime": "Limegr<PERSON>nn vindkaster", "block.minecraft.banner.guster.magenta": "Magenta v<PERSON>", "block.minecraft.banner.guster.orange": "Oransje vindkaster", "block.minecraft.banner.guster.pink": "<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.guster.yellow": "Gul vindkaster", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.blue": "B<PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON> over<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Lyseblå overdel", "block.minecraft.banner.half_horizontal.light_gray": "Lysegr<PERSON> overdel", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.pink": "<PERSON> overdel", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> over<PERSON>", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON> overdel", "block.minecraft.banner.half_horizontal_bottom.black": "Svart underdel", "block.minecraft.banner.half_horizontal_bottom.blue": "Blå underdel", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON>run under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.gray": "Grå underdel", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Lyseblå underdel", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Lysegrå underdel", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "Oransje underdel", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON> under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON> underdel", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON> under<PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON> underdel", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON>ø<PERSON>", "block.minecraft.banner.half_vertical.blue": "Blå høyredel", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON><PERSON> hø<PERSON>del", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.light_blue": "Lyseblå høyredel", "block.minecraft.banner.half_vertical.light_gray": "Lysegrå høyredel", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "<PERSON>", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON><PERSON> venstredel", "block.minecraft.banner.half_vertical_right.blue": "Blå venstredel", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "G<PERSON><PERSON> venstredel", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Lyseblå venstredel", "block.minecraft.banner.half_vertical_right.light_gray": "Lysegrå venstredel", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.pink": "<PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.blue": "Blå dings", "block.minecraft.banner.mojang.brown": "<PERSON>run dings", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.light_blue": "Lyseblå dings", "block.minecraft.banner.mojang.light_gray": "Lysegrå dings", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.pink": "<PERSON> dings", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON> dings", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON> dings", "block.minecraft.banner.mojang.white": "<PERSON><PERSON>t dings", "block.minecraft.banner.mojang.yellow": "Gul dings", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON> snute", "block.minecraft.banner.piglin.blue": "Blå snute", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON> snute", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> s<PERSON>e", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON> snute", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>e", "block.minecraft.banner.piglin.light_blue": "Lyseblå snute", "block.minecraft.banner.piglin.light_gray": "Lysegrå snute", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> snute", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>e", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON> s<PERSON>e", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON> snute", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON>t rute", "block.minecraft.banner.rhombus.blue": "Blå rute", "block.minecraft.banner.rhombus.brown": "Brun rute", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.gray": "Grå rute", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.light_blue": "Lyseblå rute", "block.minecraft.banner.rhombus.light_gray": "Lysegrå rute", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.orange": "Oransje rute", "block.minecraft.banner.rhombus.pink": "Rosa rute", "block.minecraft.banner.rhombus.purple": "<PERSON>la rute", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON> rute", "block.minecraft.banner.rhombus.white": "<PERSON>vit rute", "block.minecraft.banner.rhombus.yellow": "Gul rute", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON> hodeskalle", "block.minecraft.banner.skull.blue": "Blå hodeskalle", "block.minecraft.banner.skull.brown": "<PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "Grå hodeskalle", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "block.minecraft.banner.skull.light_blue": "Lyseblå hodeskalle", "block.minecraft.banner.skull.light_gray": "Lysegrå hodeskalle", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "Oransje <PERSON>", "block.minecraft.banner.skull.pink": "<PERSON>", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON> ho<PERSON>", "block.minecraft.banner.skull.yellow": "Gul hodeskalle", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.blue": "Blå stolper", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON> sto<PERSON>per", "block.minecraft.banner.small_stripes.light_blue": "Lyseblå stolper", "block.minecraft.banner.small_stripes.light_gray": "Lysegrå stolper", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.small_stripes.pink": "<PERSON> stolper", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON> sto<PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> stolper", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON> stolper", "block.minecraft.banner.square_bottom_left.black": "Svart høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.blue": "Blå høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.brown": "<PERSON>run høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.cyan": "Turkis høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.gray": "Grå høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.green": "Gr<PERSON>nn høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.light_blue": "Lyseblå høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.light_gray": "Lysegrå høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.lime": "Limegrønn høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON><PERSON> høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.orange": "Oransje høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.pink": "Rosa h<PERSON>yre<PERSON> i skjoldfoten", "block.minecraft.banner.square_bottom_left.purple": "Lilla høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON><PERSON> hø<PERSON> i skjoldfoten", "block.minecraft.banner.square_bottom_left.white": "Hvit høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_left.yellow": "Gul høyrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.black": "Svart venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.blue": "Blå venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.brown": "Brun venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.cyan": "Turkis venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.gray": "Grå venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.green": "Grønn venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.light_blue": "Lyseblå venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.light_gray": "Lysegrå venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.lime": "Limegrønn venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON><PERSON> venstrek<PERSON>on i skjoldfoten", "block.minecraft.banner.square_bottom_right.orange": "Oransje venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.pink": "Rosa venstrek<PERSON> i skjoldfoten", "block.minecraft.banner.square_bottom_right.purple": "Lilla venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON> venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.white": "Hvit venstrekanton i skjoldfoten", "block.minecraft.banner.square_bottom_right.yellow": "Gul venstrekanton i skjoldfoten", "block.minecraft.banner.square_top_left.black": "Svart høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.blue": "Blå høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.brown": "<PERSON>run høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.cyan": "Turkis høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.gray": "Grå høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.green": "Grønn høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.light_blue": "Lyseblå høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.light_gray": "Lysegrå høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.lime": "Limegrønn høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.magenta": "<PERSON><PERSON><PERSON><PERSON> høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.orange": "Oransje høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.pink": "Rosa h<PERSON>yrekan<PERSON> i skjoldhodet", "block.minecraft.banner.square_top_left.purple": "Lilla høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON><PERSON> høyrekan<PERSON> i skjoldhodet", "block.minecraft.banner.square_top_left.white": "Hvit høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_left.yellow": "Gul høyrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.black": "Svart venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.blue": "Blå venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.brown": "Brun venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.cyan": "Turkis venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.gray": "Grå venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.green": "Grønn venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.light_blue": "Lyseblå venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.light_gray": "Lysegrå venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.lime": "Limegrønn venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON><PERSON> venstrek<PERSON>on i skjoldhodet", "block.minecraft.banner.square_top_right.orange": "Oransje venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.pink": "Rosa venstrek<PERSON> i skjoldhodet", "block.minecraft.banner.square_top_right.purple": "Lilla venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.red": "R<PERSON>d venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.white": "Hvit venstrekanton i skjoldhodet", "block.minecraft.banner.square_top_right.yellow": "Gul venstrekanton i skjoldhodet", "block.minecraft.banner.straight_cross.black": "Svart kors", "block.minecraft.banner.straight_cross.blue": "Blått kors", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON> kors", "block.minecraft.banner.straight_cross.cyan": "Turkist kors", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON><PERSON> kors", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON><PERSON> kors", "block.minecraft.banner.straight_cross.light_blue": "Lyseblått kors", "block.minecraft.banner.straight_cross.light_gray": "Lysegr<PERSON>tt kors", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> kors", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "Oransje kors", "block.minecraft.banner.straight_cross.pink": "<PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON> kors", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON> kors", "block.minecraft.banner.stripe_bottom.black": "Svart skjoldfot", "block.minecraft.banner.stripe_bottom.blue": "Blå skjoldfot", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON> skjo<PERSON><PERSON>t", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON> skjo<PERSON>", "block.minecraft.banner.stripe_bottom.gray": "Grå skjoldfot", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON> skjoldfot", "block.minecraft.banner.stripe_bottom.light_blue": "Lyseblå skjoldfot", "block.minecraft.banner.stripe_bottom.light_gray": "Lysegrå skjoldfot", "block.minecraft.banner.stripe_bottom.lime": "Limegrønn skjoldfot", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.stripe_bottom.orange": "Oransje skjoldfot", "block.minecraft.banner.stripe_bottom.pink": "<PERSON> s<PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON> skjo<PERSON>t", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON> skjo<PERSON><PERSON>t", "block.minecraft.banner.stripe_bottom.yellow": "Gul skjoldfot", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.blue": "Blå stolpe", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.gray": "Gr<PERSON> stolpe", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.light_blue": "Lyseblå stolpe", "block.minecraft.banner.stripe_center.light_gray": "Lysegrå stolpe", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON> s<PERSON>pe", "block.minecraft.banner.stripe_center.pink": "<PERSON> stolpe", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON> sto<PERSON>pe", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> stolpe", "block.minecraft.banner.stripe_downleft.black": "Svart venstreskråbjelke", "block.minecraft.banner.stripe_downleft.blue": "Blå venstreskråbjelke", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "Turkis venstreskråbjelke", "block.minecraft.banner.stripe_downleft.gray": "Grå venstreskråbjelke", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON><PERSON> venstreskråbjelk<PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Lyseblå venstreskråbjelke", "block.minecraft.banner.stripe_downleft.light_gray": "Lysegrå venstreskråbjelke", "block.minecraft.banner.stripe_downleft.lime": "Limegr<PERSON><PERSON> venstreskråbjelke", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Oransje venstreskråbjelke", "block.minecraft.banner.stripe_downleft.pink": "<PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON> venstreskråbjelk<PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON> venst<PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "Gul venstreskråbjelke", "block.minecraft.banner.stripe_downright.black": "Svart skråbjelke", "block.minecraft.banner.stripe_downright.blue": "Blå skråbjelke", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "Grå skråbjelke", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON>bjelke", "block.minecraft.banner.stripe_downright.light_blue": "Lyseblå skråbjelke", "block.minecraft.banner.stripe_downright.light_gray": "Lysegrå skråbjelke", "block.minecraft.banner.stripe_downright.lime": "Limegrønn skråbjelke", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "Oransje skråbjelke", "block.minecraft.banner.stripe_downright.pink": "<PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "Gul skråbjelke", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.blue": "Blå høyrestolpe", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON> høyrestolpe", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Lyseblå høyrestolpe", "block.minecraft.banner.stripe_left.light_gray": "Lysegrå høyrestolpe", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "<PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "Blå bjelke", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> bjelke", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Lyseblå bjelke", "block.minecraft.banner.stripe_middle.light_gray": "Lysegrå bjelke", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bjelk<PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Svart venstrestolpe", "block.minecraft.banner.stripe_right.blue": "Blå venstrestolpe", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.gray": "Grå venstrestolpe", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Lyseblå venstrestolpe", "block.minecraft.banner.stripe_right.light_gray": "Lysegrå venstrestolpe", "block.minecraft.banner.stripe_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> venstrestol<PERSON>", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.orange": "Oransje venstrestolpe", "block.minecraft.banner.stripe_right.pink": "<PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON> venstrest<PERSON><PERSON>", "block.minecraft.banner.stripe_right.yellow": "Gul venstrestolpe", "block.minecraft.banner.stripe_top.black": "Svart skjoldhode", "block.minecraft.banner.stripe_top.blue": "Blått skjoldhode", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON> skjoldhode", "block.minecraft.banner.stripe_top.cyan": "Turkist skjoldhode", "block.minecraft.banner.stripe_top.gray": "G<PERSON><PERSON>tt skjoldhode", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Lyseblått skjoldhode", "block.minecraft.banner.stripe_top.light_gray": "Lysegrått skjoldhode", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> skjoldho<PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Oransje skjoldhode", "block.minecraft.banner.stripe_top.pink": "<PERSON> s<PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON> s<PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> skjoldho<PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON>ult skjoldhode", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.blue": "Blå sparre", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.light_blue": "Lyseblå sparre", "block.minecraft.banner.triangle_bottom.light_gray": "Lysegrå sparre", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.pink": "<PERSON> sparre", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON> sparre", "block.minecraft.banner.triangle_bottom.yellow": "G<PERSON> sparre", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.blue": "Blå sparre (omvendt)", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.light_blue": "Lyseblå sparre (omvendt)", "block.minecraft.banner.triangle_top.light_gray": "Lysegr<PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.pink": "<PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON> spa<PERSON> (omvendt)", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON> sparre (omvendt)", "block.minecraft.banner.triangles_bottom.black": "Svart tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.blue": "Blått tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.brown": "Brunt tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.cyan": "Turkist tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.gray": "Grått tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON><PERSON> tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.light_blue": "Lyseblått tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.light_gray": "Lysegrått tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.lime": "Limegrønt tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.magenta": "L<PERSON><PERSON>lla tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.orange": "Oransje tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.pink": "Rosa tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.purple": "Lilla tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON> tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.white": "H<PERSON>tt tannsnitt i skjoldfot", "block.minecraft.banner.triangles_bottom.yellow": "Gult tannsnitt i skjoldfot", "block.minecraft.banner.triangles_top.black": "Svart tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.blue": "Blått tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.brown": "Brunt tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.cyan": "Turkist tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.gray": "Grått tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.green": "G<PERSON><PERSON>nt tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.light_blue": "Lyseblått tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.light_gray": "Lysegrått tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.lime": "Limegrønt tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.magenta": "L<PERSON><PERSON>lla tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.orange": "Oransje tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.pink": "Rosa tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.purple": "Lilla tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.red": "R<PERSON>dt tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.white": "Hvitt tannsnitt i skjoldhodet", "block.minecraft.banner.triangles_top.yellow": "Gult tannsnitt i skjoldhodet", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>", "block.minecraft.barrier": "Barriere", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Varde", "block.minecraft.beacon.primary": "Primærkraft", "block.minecraft.beacon.secondary": "Sekundærkraft", "block.minecraft.bed.no_sleep": "Du kan bare sove om natten eller under to<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bed.not_safe": "Du kan ikke sove nå; det er monstre i nærheten", "block.minecraft.bed.obstructed": "<PERSON>ne sengen er blokkert", "block.minecraft.bed.occupied": "Denne sengen er opptatt", "block.minecraft.bed.too_far_away": "Du kan ikke sove nå; sengen er for langt unna", "block.minecraft.bedrock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bee_nest": "Biebol", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Stort dråpeblad", "block.minecraft.big_dripleaf_stem": "Stor dråpebladsstilk", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON><PERSON>eknap<PERSON>", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence": "Bjørkegjerde", "block.minecraft.birch_fence_gate": "Bjørkegrind", "block.minecraft.birch_hanging_sign": "Hengende bjørkeskilt", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "block.minecraft.birch_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Bjørketrykkplate", "block.minecraft.birch_sapling": "Bjørkespire", "block.minecraft.birch_sign": "Bjørkeskilt", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "Bjørketrapp", "block.minecraft.birch_trapdoor": "Bjørkefallem", "block.minecraft.birch_wall_hanging_sign": "Vegghengende bjørkeskilt", "block.minecraft.birch_wall_sign": "Bjørkeveggskilt", "block.minecraft.birch_wood": "Bjørketre", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON> banner", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON> vok<PERSON>lys", "block.minecraft.black_candle_cake": "<PERSON>ke med svart lys", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON> teppe", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON> sement", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON>t glasert terrakotta", "block.minecraft.black_shulker_box": "Svart shulkerboks", "block.minecraft.black_stained_glass": "Svartfarget glass", "block.minecraft.black_stained_glass_pane": "Svartfarget glassrute", "block.minecraft.black_terracotta": "Svart terrakotta", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Helle av svartstein", "block.minecraft.blackstone_stairs": "Trapp av svar<PERSON>tein", "block.minecraft.blackstone_wall": "<PERSON><PERSON> av svar<PERSON><PERSON>", "block.minecraft.blast_furnace": "Masovn", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>tt banner", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.blue_candle": "Blått vokslys", "block.minecraft.blue_candle_cake": "<PERSON>ke med blått lys", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.blue_concrete": "Blå betong", "block.minecraft.blue_concrete_powder": "Blå sement", "block.minecraft.blue_glazed_terracotta": "Blå glasert terrakotta", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "Blå orkidé", "block.minecraft.blue_shulker_box": "Blå shulkerboks", "block.minecraft.blue_stained_glass": "Blåfarget glass", "block.minecraft.blue_stained_glass_pane": "Blåfarget glassrute", "block.minecraft.blue_terracotta": "Blå terrakotta", "block.minecraft.blue_wool": "Blå ull", "block.minecraft.bone_block": "Beinblokk", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "Hjernekorall", "block.minecraft.brain_coral_block": "Hjernekorallblokk", "block.minecraft.brain_coral_fan": "Hjernekorallvifte", "block.minecraft.brain_coral_wall_fan": "Hjernekorallveggvifte", "block.minecraft.brewing_stand": "Bryggestativ", "block.minecraft.brick_slab": "Helle av tegl", "block.minecraft.brick_stairs": "Trapp av tegl", "block.minecraft.brick_wall": "Mur av tegl", "block.minecraft.bricks": "Tegl", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON> banner", "block.minecraft.brown_bed": "<PERSON><PERSON> seng", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Kake med brunt lys", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> teppe", "block.minecraft.brown_concrete": "<PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Brun sement", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON> glasert terrakotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON> sopp", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Brunfarget glass", "block.minecraft.brown_stained_glass_pane": "Brunfarget glassrute", "block.minecraft.brown_terracotta": "<PERSON><PERSON> terra<PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON>l", "block.minecraft.bubble_column": "Boblekolonne", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Boblekorallblokk", "block.minecraft.bubble_coral_fan": "Boblekorallvifte", "block.minecraft.bubble_coral_wall_fan": "Boblekorallveggvifte", "block.minecraft.budding_amethyst": "<PERSON><PERSON>nde ametyst", "block.minecraft.bush": "Busk", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Ka<PERSON><PERSON>b<PERSON><PERSON>t", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON> sculksensor", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "Kake med lys", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Karttegningsbenk", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cauldron": "Gryte", "block.minecraft.cave_air": "Huleluft", "block.minecraft.cave_vines": "Grotteranke", "block.minecraft.cave_vines_plant": "Grotteranke", "block.minecraft.chain": "Kjetting", "block.minecraft.chain_command_block": "Kjede-kommandoblokk", "block.minecraft.cherry_button": "<PERSON><PERSON>b<PERSON><PERSON>reknap<PERSON>", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "Kirsebærtregjerde", "block.minecraft.cherry_fence_gate": "Kirsebærtregrind", "block.minecraft.cherry_hanging_sign": "Hengende kirsebærtreskilt", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "Kirsebærtretrykkplate", "block.minecraft.cherry_sapling": "Kirsebærtrespire", "block.minecraft.cherry_sign": "Kirsebærtreskilt", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "Kirsebærtrefallem", "block.minecraft.cherry_wall_hanging_sign": "Vegghengende kirsebærtreskilt", "block.minecraft.cherry_wall_sign": "Kirsebærtreveggskilt", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "Sprukket ambolt", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON><PERSON> bokhylle", "block.minecraft.chiseled_copper": "Uthugget kobber", "block.minecraft.chiseled_deepslate": "Uthug<PERSON> dy<PERSON>", "block.minecraft.chiseled_nether_bricks": "Uthugget nethertegl", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.chiseled_quartz_block": "Uthugget kvartsblokk", "block.minecraft.chiseled_red_sandstone": "Uthug<PERSON> rød sandstein", "block.minecraft.chiseled_resin_bricks": "Uthugget kvaetegl", "block.minecraft.chiseled_sandstone": "Uthugget sandstein", "block.minecraft.chiseled_stone_bricks": "Uthugget steinmur", "block.minecraft.chiseled_tuff": "Uthugget tuffstein", "block.minecraft.chiseled_tuff_bricks": "Uthugget tuffstein<PERSON>gl", "block.minecraft.chorus_flower": "Refrengblomst", "block.minecraft.chorus_plant": "Refrengplante", "block.minecraft.clay": "Le<PERSON>", "block.minecraft.closed_eyeblossom": "Lukket øyeblom", "block.minecraft.coal_block": "Kullblokk", "block.minecraft.coal_ore": "Kullmalm", "block.minecraft.coarse_dirt": "<PERSON><PERSON> jord", "block.minecraft.cobbled_deepslate": "Brolagt dypskifer", "block.minecraft.cobbled_deepslate_slab": "Helle av brolagt d<PERSON>", "block.minecraft.cobbled_deepslate_stairs": "Trapp av brolagt d<PERSON>fer", "block.minecraft.cobbled_deepslate_wall": "Mur av brolagt d<PERSON>", "block.minecraft.cobblestone": "<PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Helle av brostein", "block.minecraft.cobblestone_stairs": "Trapp av brostein", "block.minecraft.cobblestone_wall": "<PERSON>r av brostein", "block.minecraft.cobweb": "Spindelvev", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Kommandoblokk", "block.minecraft.comparator": "Redstonesammenligner", "block.minecraft.composter": "Kompostbinge", "block.minecraft.conduit": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Kobberblokk", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Kobbermalm", "block.minecraft.copper_trapdoor": "Kobberfallem", "block.minecraft.cornflower": "Kornb<PERSON>mst", "block.minecraft.cracked_deepslate_bricks": "Sprukken dypskifermur", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_nether_bricks": "Sprukket nethertegl", "block.minecraft.cracked_polished_blackstone_bricks": "Sprukket finslipt svartsteinsmur", "block.minecraft.cracked_stone_bricks": "Sprukket steinmur", "block.minecraft.crafter": "Tilvirker", "block.minecraft.crafting_table": "Arbeidsbenk", "block.minecraft.creaking_heart": "Knirkningshjerte", "block.minecraft.creeper_head": "Creeperhode", "block.minecraft.creeper_wall_head": "Creepervegghode", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_door": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "Karmosingrind", "block.minecraft.crimson_fungus": "Karmosinsopp", "block.minecraft.crimson_hanging_sign": "Hengende karmosinskilt", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "Karmosinnycel", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Karmosintrykkplate", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Karmosinskilt", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "Karmosintrapp", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "Karmosinfallem", "block.minecraft.crimson_wall_hanging_sign": "Vegghengende karmosinskilt", "block.minecraft.crimson_wall_sign": "Karmosinveggskilt", "block.minecraft.crying_obsidian": "Gråtende obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON><PERSON> kobber", "block.minecraft.cut_copper_slab": "Helle av skåret kobber", "block.minecraft.cut_copper_stairs": "Trapp av skåret kobber", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON><PERSON> rød <PERSON>", "block.minecraft.cut_red_sandstone_slab": "Helle av skåret rød sand<PERSON>", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_sandstone_slab": "Helle av sk<PERSON><PERSON> sandstein", "block.minecraft.cyan_banner": "Turkist banner", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON> v<PERSON>s", "block.minecraft.cyan_candle_cake": "<PERSON>ke med blågr<PERSON>nt lys", "block.minecraft.cyan_carpet": "Tu<PERSON><PERSON> teppe", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sement", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "Turkisfarget glass", "block.minecraft.cyan_stained_glass_pane": "Turkisfarget glassrute", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON> terra<PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.damaged_anvil": "Skadet ambolt", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Mørkeikeknapp", "block.minecraft.dark_oak_door": "<PERSON>ørk<PERSON>ked<PERSON><PERSON>", "block.minecraft.dark_oak_fence": "Mørkeikegjerde", "block.minecraft.dark_oak_fence_gate": "Mørkeikegrind", "block.minecraft.dark_oak_hanging_sign": "Hengende mørkeikeskilt", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "Mørkeiketømmer", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_pressure_plate": "Mørkeiketrykkplate", "block.minecraft.dark_oak_sapling": "Mørkeikespire", "block.minecraft.dark_oak_sign": "Mørkeikeskilt", "block.minecraft.dark_oak_slab": "Mørkeikehelle", "block.minecraft.dark_oak_stairs": "Mørkeiketrapp", "block.minecraft.dark_oak_trapdoor": "Mørkeikefallem", "block.minecraft.dark_oak_wall_hanging_sign": "Vegghengende mørkeikeskilt", "block.minecraft.dark_oak_wall_sign": "Mørkeikeveggskilt", "block.minecraft.dark_oak_wood": "Mørkeiketre", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Helle av mørk prismarin", "block.minecraft.dark_prismarine_stairs": "Trapp av mørk prismarin", "block.minecraft.daylight_detector": "Dagslyssensor", "block.minecraft.dead_brain_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON>allblo<PERSON>", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON>e", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON>allveggvifte", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON>allblo<PERSON>", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON> b<PERSON>e", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> b<PERSON>korallveggvifte", "block.minecraft.dead_bush": "Vissen busk", "block.minecraft.dead_fire_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON>allblokk", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON>e", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON>allveggvifte", "block.minecraft.dead_horn_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON>ggvifte", "block.minecraft.dead_tube_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON>allblo<PERSON>", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON>e", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON>allveggvifte", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON> potte", "block.minecraft.deepslate": "Dyps<PERSON>fer", "block.minecraft.deepslate_brick_slab": "Helle av dypskifermur", "block.minecraft.deepslate_brick_stairs": "Trapp av dypskifermur", "block.minecraft.deepslate_brick_wall": "Mur av dypskifermur", "block.minecraft.deepslate_bricks": "Dypskifermur", "block.minecraft.deepslate_coal_ore": "Kullmalm i dypskifer", "block.minecraft.deepslate_copper_ore": "Kobbermalm i dypskifer", "block.minecraft.deepslate_diamond_ore": "Diamantmalm i dypskifer", "block.minecraft.deepslate_emerald_ore": "Smaragdmalm i dypskifer", "block.minecraft.deepslate_gold_ore": "Gullmalm i dypskifer", "block.minecraft.deepslate_iron_ore": "Jernmalm i dypskifer", "block.minecraft.deepslate_lapis_ore": "Lasursteinmalm i dypskifer", "block.minecraft.deepslate_redstone_ore": "Redstonemalm i dypskifer", "block.minecraft.deepslate_tile_slab": "Helle av dypskiferfliser", "block.minecraft.deepslate_tile_stairs": "Trapp av dypskiferfliser", "block.minecraft.deepslate_tile_wall": "Mur av dypskiferfliser", "block.minecraft.deepslate_tiles": "Dypskiferfliser", "block.minecraft.detector_rail": "Detektorskinne", "block.minecraft.diamond_block": "Diamantblokk", "block.minecraft.diamond_ore": "Diamantmal<PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Helle av dioritt", "block.minecraft.diorite_stairs": "Trapp av dioritt", "block.minecraft.diorite_wall": "Mur av dioritt", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "Dragehode", "block.minecraft.dragon_wall_head": "Dragevegghode", "block.minecraft.dried_ghast": "Uttørket ghast", "block.minecraft.dried_kelp_block": "Tørket tareblokk", "block.minecraft.dripstone_block": "Dryppsteinsblokk", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Smaragdblokk", "block.minecraft.emerald_ore": "Smaragdmalm", "block.minecraft.enchanting_table": "Trolldomsbord", "block.minecraft.end_gateway": "End-port", "block.minecraft.end_portal": "Endeportal", "block.minecraft.end_portal_frame": "End-portalramme", "block.minecraft.end_rod": "Endestang", "block.minecraft.end_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Helle av endesteinsmur", "block.minecraft.end_stone_brick_stairs": "Trapp av endesteinsmur", "block.minecraft.end_stone_brick_wall": "<PERSON>r av endesteinmur", "block.minecraft.end_stone_bricks": "Endesteinmur", "block.minecraft.ender_chest": "Enderkis<PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON>att uthugget kobber", "block.minecraft.exposed_copper": "Utsatt kobber", "block.minecraft.exposed_copper_bulb": "Utsatt kobberpære", "block.minecraft.exposed_copper_door": "Utsatt kobberdør", "block.minecraft.exposed_copper_grate": "Utsatt kobberrist", "block.minecraft.exposed_copper_trapdoor": "Utsatt kobberfallem", "block.minecraft.exposed_cut_copper": "Utsatt skåret kobber", "block.minecraft.exposed_cut_copper_slab": "Helle av utsatt skåret kobber", "block.minecraft.exposed_cut_copper_stairs": "Trapp av utsatt skåret kobber", "block.minecraft.farmland": "Dyrkbar mark", "block.minecraft.fern": "Bregne", "block.minecraft.fire": "Ild", "block.minecraft.fire_coral": "Ildkorall", "block.minecraft.fire_coral_block": "Ildkorallblokk", "block.minecraft.fire_coral_fan": "Ildkorallvifte", "block.minecraft.fire_coral_wall_fan": "Ildkorallveggvifte", "block.minecraft.firefly_bush": "Ildfluebusk", "block.minecraft.fletching_table": "Pilmakerbenk", "block.minecraft.flower_pot": "Blomsterpotte", "block.minecraft.flowering_azalea": "Blomstrende asalea", "block.minecraft.flowering_azalea_leaves": "Blomstrende asaleablader", "block.minecraft.frogspawn": "Froskerogn", "block.minecraft.frosted_ice": "Frostdekt Is", "block.minecraft.furnace": "Ovn", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "Glass", "block.minecraft.glass_pane": "Glassrute", "block.minecraft.glow_lichen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Gullblokk", "block.minecraft.gold_ore": "Gullmalm", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Helle av granitt", "block.minecraft.granite_stairs": "Trapp av granitt", "block.minecraft.granite_wall": "<PERSON>r av granitt", "block.minecraft.grass": "<PERSON>ress", "block.minecraft.grass_block": "Gressblokk", "block.minecraft.gravel": "Grus", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON><PERSON> banner", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON> vokslys", "block.minecraft.gray_candle_cake": "<PERSON>ke med grått lys", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "Gr<PERSON> sement", "block.minecraft.gray_glazed_terracotta": "Gr<PERSON> glasert terrakotta", "block.minecraft.gray_shulker_box": "Grå shulkerboks", "block.minecraft.gray_stained_glass": "Gråfarget glass", "block.minecraft.gray_stained_glass_pane": "Gråfarget glassrute", "block.minecraft.gray_terracotta": "Grå terrakotta", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>l", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON> banner", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "block.minecraft.green_candle_cake": "Kake med grønt lys", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> sement", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON><PERSON> shulkerboks", "block.minecraft.green_stained_glass": "Grønnfarget glass", "block.minecraft.green_stained_glass_pane": "Grønnfarget glassrute", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON>l", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> røtter", "block.minecraft.hay_block": "Høyball", "block.minecraft.heavy_core": "<PERSON>ng kjerne", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON>ng tyn<PERSON>plate", "block.minecraft.honey_block": "Honningblokk", "block.minecraft.honeycomb_block": "Bikakeblokk", "block.minecraft.hopper": "Trakt", "block.minecraft.horn_coral": "Hornkorall", "block.minecraft.horn_coral_block": "Hornkorallblokk", "block.minecraft.horn_coral_fan": "Hornkorallvifte", "block.minecraft.horn_coral_wall_fan": "Hornkorallveggvifte", "block.minecraft.ice": "Is", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON>t uthugget steinmur", "block.minecraft.infested_cobblestone": "Befengt brostein", "block.minecraft.infested_cracked_stone_bricks": "Befengt sprukket steinmur", "block.minecraft.infested_deepslate": "Befengt dypskifer", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON>t mosete steinmur", "block.minecraft.infested_stone": "<PERSON><PERSON>t stein", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON><PERSON> steinmur", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Jernblokk", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "Jern<PERSON><PERSON>", "block.minecraft.iron_trapdoor": "<PERSON><PERSON> av Jern", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jigsaw": "Pusleblokk", "block.minecraft.jukebox": "Platespiller", "block.minecraft.jungle_button": "Jungeltreknapp", "block.minecraft.jungle_door": "Jungeltredør", "block.minecraft.jungle_fence": "Jungeltregjerde", "block.minecraft.jungle_fence_gate": "Jungeltregrind", "block.minecraft.jungle_hanging_sign": "Hengende jungeltreskilt", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "block.minecraft.jungle_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Jungeltretrykkplate", "block.minecraft.jungle_sapling": "Jungeltrespire", "block.minecraft.jungle_sign": "Jungeltreskilt", "block.minecraft.jungle_slab": "Jungeltrehell<PERSON>", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "Jungeltrefallem", "block.minecraft.jungle_wall_hanging_sign": "Vegghengende jungeltreskilt", "block.minecraft.jungle_wall_sign": "Jungeltreveggskilt", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp": "<PERSON><PERSON>", "block.minecraft.kelp_plant": "Tareplante", "block.minecraft.ladder": "Stige", "block.minecraft.lantern": "Lykt", "block.minecraft.lapis_block": "Lasursteinblokk", "block.minecraft.lapis_ore": "Lasursteinmalm", "block.minecraft.large_amethyst_bud": "Stor ametystknopp", "block.minecraft.large_fern": "<PERSON>or bregne", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Gryte med lava", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON> løv", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "Spak", "block.minecraft.light": "Lys", "block.minecraft.light_blue_banner": "L<PERSON><PERSON><PERSON><PERSON><PERSON> banner", "block.minecraft.light_blue_bed": "Lyseblå seng", "block.minecraft.light_blue_candle": "Lyseblått vokslys", "block.minecraft.light_blue_candle_cake": "Kake med lyseblått lys", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teppe", "block.minecraft.light_blue_concrete": "Lyseblå betong", "block.minecraft.light_blue_concrete_powder": "Lyseblå sement", "block.minecraft.light_blue_glazed_terracotta": "Lyseblå glasert terrakotta", "block.minecraft.light_blue_shulker_box": "Lyseblå shulkerboks", "block.minecraft.light_blue_stained_glass": "Lyseblåfarget glass", "block.minecraft.light_blue_stained_glass_pane": "Lyseblåfarget glassrute", "block.minecraft.light_blue_terracotta": "Lyseblå terrakotta", "block.minecraft.light_blue_wool": "Lyseblå ull", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> banner", "block.minecraft.light_gray_bed": "Lysegr<PERSON> seng", "block.minecraft.light_gray_candle": "Lysegrått vokslys", "block.minecraft.light_gray_candle_cake": "Kake med lysegr<PERSON>tt lys", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.light_gray_concrete": "Lysegrå betong", "block.minecraft.light_gray_concrete_powder": "Lysegrå sement", "block.minecraft.light_gray_glazed_terracotta": "Lysegrå glasert terrakotta", "block.minecraft.light_gray_shulker_box": "Lysegrå shulkerboks", "block.minecraft.light_gray_stained_glass": "Lysegråfarget glass", "block.minecraft.light_gray_stained_glass_pane": "Lysegråfarget glassrute", "block.minecraft.light_gray_terracotta": "Lysegrå terrakotta", "block.minecraft.light_gray_wool": "Lysegr<PERSON> ull", "block.minecraft.light_weighted_pressure_plate": "Lett tyngdet<PERSON>", "block.minecraft.lightning_rod": "Lyna<PERSON><PERSON><PERSON>", "block.minecraft.lilac": "Syrin", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "Vannliljeblad", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> banner", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.lime_candle": "<PERSON><PERSON>r<PERSON><PERSON> vok<PERSON>lys", "block.minecraft.lime_candle_cake": "<PERSON>ke med limegrønt lys", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> sement", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.lime_shulker_box": "Limegrønn shulkerboks", "block.minecraft.lime_stained_glass": "Limegrønnfarget glass", "block.minecraft.lime_stained_glass_pane": "Limegrønnfarget glassrute", "block.minecraft.lime_terracotta": "Limegr<PERSON>nn terrakotta", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Vevstol", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON> banner", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON> seng", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.magenta_candle_cake": "Kake med lyselilla lys", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> sement", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "Lyselillafarget glass", "block.minecraft.magenta_stained_glass_pane": "Lyselillafarget glassrute", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON><PERSON> ull", "block.minecraft.magma_block": "Magmablokk", "block.minecraft.mangrove_button": "Mangroveknapp", "block.minecraft.mangrove_door": "Mangrovedør", "block.minecraft.mangrove_fence": "Mangrovegjerde", "block.minecraft.mangrove_fence_gate": "Mangrovegrind", "block.minecraft.mangrove_hanging_sign": "Hengende mangroveskilt", "block.minecraft.mangrove_leaves": "Mangroveblader", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_planks": "Mangroveplanker", "block.minecraft.mangrove_pressure_plate": "Mangrovetrykkplate", "block.minecraft.mangrove_propagule": "Mangrovebulbill", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_sign": "Mangroveskilt", "block.minecraft.mangrove_slab": "Mangrovehelle", "block.minecraft.mangrove_stairs": "Mangrovetrapp", "block.minecraft.mangrove_trapdoor": "Mangrovefallem", "block.minecraft.mangrove_wall_hanging_sign": "Vegghengende mangroveskilt", "block.minecraft.mangrove_wall_sign": "Mangroveveggskilt", "block.minecraft.mangrove_wood": "Mangrovetre", "block.minecraft.medium_amethyst_bud": "Mellomstor ametystknopp", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "Melonstil<PERSON>", "block.minecraft.moss_block": "Moseblokk", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON> brostein", "block.minecraft.mossy_cobblestone_slab": "Helle av mosete brostein", "block.minecraft.mossy_cobblestone_stairs": "Trapp av mosete brostein", "block.minecraft.mossy_cobblestone_wall": "<PERSON>r av mosete brostein", "block.minecraft.mossy_stone_brick_slab": "Helle av mosete steinmur", "block.minecraft.mossy_stone_brick_stairs": "Trapp av mosete steinmur", "block.minecraft.mossy_stone_brick_wall": "Mur av mosete steinmur", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON> stein<PERSON>", "block.minecraft.moving_piston": "Bevegende stempel", "block.minecraft.mud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Helle av gjørmetegl", "block.minecraft.mud_brick_stairs": "Trapp av gjørmetegl", "block.minecraft.mud_brick_wall": "<PERSON>r av gjø<PERSON>etegl", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON>etegl", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON> mangrove<PERSON><PERSON><PERSON>", "block.minecraft.mushroom_stem": "Soppstilk", "block.minecraft.mycelium": "Mycel", "block.minecraft.nether_brick_fence": "Gjerde av nethertegl", "block.minecraft.nether_brick_slab": "Helle av nethertegl", "block.minecraft.nether_brick_stairs": "Trapp av nethertegl", "block.minecraft.nether_brick_wall": "Mur av nethertegl", "block.minecraft.nether_bricks": "Nethertegl", "block.minecraft.nether_gold_ore": "Nethergullmalm", "block.minecraft.nether_portal": "Netherportal", "block.minecraft.nether_quartz_ore": "Netherkvarts-malm", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "Nethervorte", "block.minecraft.nether_wart_block": "Nethervorteblokk", "block.minecraft.netherite_block": "Netherittblokk", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Noteblokk", "block.minecraft.oak_button": "<PERSON><PERSON>knap<PERSON>", "block.minecraft.oak_door": "<PERSON><PERSON>d<PERSON><PERSON>", "block.minecraft.oak_fence": "Eikegjer<PERSON>", "block.minecraft.oak_fence_gate": "Eikegrind", "block.minecraft.oak_hanging_sign": "Hengende eikeskilt", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Eiketrykkplate", "block.minecraft.oak_sapling": "Eikespire", "block.minecraft.oak_sign": "Eikeskilt", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_trapdoor": "Eikefallem", "block.minecraft.oak_wall_hanging_sign": "Vegghengende eikeskilt", "block.minecraft.oak_wall_sign": "Eikeveggskilt", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Observatør", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON> f<PERSON>", "block.minecraft.ominous_banner": "Illevarslen<PERSON> fane", "block.minecraft.open_eyeblossom": "<PERSON><PERSON>", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON> banner", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.orange_candle": "Oransje vok<PERSON>lys", "block.minecraft.orange_candle_cake": "Kake med oransje lys", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "Oransje sement", "block.minecraft.orange_glazed_terracotta": "Oransje glasert terrakotta", "block.minecraft.orange_shulker_box": "Oransje shulkerboks", "block.minecraft.orange_stained_glass": "Oransjefarget glass", "block.minecraft.orange_stained_glass_pane": "Oransjefarget glassrute", "block.minecraft.orange_terracotta": "Oransje terrakotta", "block.minecraft.orange_tulip": "Oransje tulipan", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.oxeye_daisy": "Prestekrage", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON>t uthugget kobber", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON> kobber", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON>t kobberpære", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON> kob<PERSON>", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON> kobberrist", "block.minecraft.oxidized_copper_trapdoor": "Irret kobberfallem", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON><PERSON> skåret kobber", "block.minecraft.oxidized_cut_copper_slab": "Helle av irret skåret kobber", "block.minecraft.oxidized_cut_copper_stairs": "Trapp av irret skåret kobber", "block.minecraft.packed_ice": "Tettpakket is", "block.minecraft.packed_mud": "Stråbunden gjørme", "block.minecraft.pale_hanging_moss": "Hengende tåkemose", "block.minecraft.pale_moss_block": "Tåkemoseblokk", "block.minecraft.pale_moss_carpet": "Tåkemoseteppe", "block.minecraft.pale_oak_button": "Blekeikeknapp", "block.minecraft.pale_oak_door": "Blekeikedør", "block.minecraft.pale_oak_fence": "Blekeikegjerde", "block.minecraft.pale_oak_fence_gate": "Blekeikegrind", "block.minecraft.pale_oak_hanging_sign": "Hengende blekeikeskilt", "block.minecraft.pale_oak_leaves": "Bleke<PERSON>løv", "block.minecraft.pale_oak_log": "Blekeiketømmer", "block.minecraft.pale_oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_pressure_plate": "Blekeiketrykkplate", "block.minecraft.pale_oak_sapling": "Blekeikespire", "block.minecraft.pale_oak_sign": "Blekeikeskilt", "block.minecraft.pale_oak_slab": "Blekeikehelle", "block.minecraft.pale_oak_stairs": "Blekeiketrapp", "block.minecraft.pale_oak_trapdoor": "Blekeikefallem", "block.minecraft.pale_oak_wall_hanging_sign": "Vegghengende blekeikeskilt", "block.minecraft.pale_oak_wall_sign": "Blekeikeveggskilt", "block.minecraft.pale_oak_wood": "Blekeiketre", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON> fros<PERSON>", "block.minecraft.peony": "<PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "Helle av forsteinet eik", "block.minecraft.piglin_head": "Piglinhode", "block.minecraft.piglin_wall_head": "Piglinvegghode", "block.minecraft.pink_banner": "<PERSON> banner", "block.minecraft.pink_bed": "<PERSON> seng", "block.minecraft.pink_candle": "<PERSON> voks<PERSON>s", "block.minecraft.pink_candle_cake": "Kake med rosa lys", "block.minecraft.pink_carpet": "<PERSON>", "block.minecraft.pink_concrete": "<PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON> sement", "block.minecraft.pink_glazed_terracotta": "<PERSON> glasert terrakotta", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "<PERSON>", "block.minecraft.pink_stained_glass": "Rosafarget glass", "block.minecraft.pink_stained_glass_pane": "Rosafarget glassrute", "block.minecraft.pink_terracotta": "<PERSON> terrakot<PERSON>", "block.minecraft.pink_tulip": "<PERSON> tulipan", "block.minecraft.pink_wool": "<PERSON>l", "block.minecraft.piston": "Stempel", "block.minecraft.piston_head": "Stempelhode", "block.minecraft.pitcher_crop": "Kanneplantebelg", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head": "Spillerhode", "block.minecraft.player_head.named": "%ss hode", "block.minecraft.player_wall_head": "Spillervegghode", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_slab": "Helle av fins<PERSON>t and<PERSON>tt", "block.minecraft.polished_andesite_stairs": "Trapp av fins<PERSON><PERSON> and<PERSON>tt", "block.minecraft.polished_basalt": "Finslipt basalt", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "Helle av fins<PERSON>t s<PERSON>", "block.minecraft.polished_blackstone_brick_stairs": "Trapp av fins<PERSON><PERSON> s<PERSON>", "block.minecraft.polished_blackstone_brick_wall": "<PERSON>r av <PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_button": "Knapp av <PERSON><PERSON><PERSON> s<PERSON>tein", "block.minecraft.polished_blackstone_pressure_plate": "Trykkplate av finslipt svartstein", "block.minecraft.polished_blackstone_slab": "<PERSON>e av <PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.polished_blackstone_stairs": "Trapp av <PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> av <PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Helle av finpusset dypskifer", "block.minecraft.polished_deepslate_stairs": "Trapp av finpusset dypskifer", "block.minecraft.polished_deepslate_wall": "Mur av <PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Helle av fins<PERSON>t di<PERSON>tt", "block.minecraft.polished_diorite_stairs": "Trapp av fins<PERSON><PERSON> di<PERSON>tt", "block.minecraft.polished_granite": "<PERSON><PERSON><PERSON><PERSON> grani<PERSON>", "block.minecraft.polished_granite_slab": "Helle av fins<PERSON>t granitt", "block.minecraft.polished_granite_stairs": "Trapp av fins<PERSON><PERSON> granitt", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON> tuffstein", "block.minecraft.polished_tuff_slab": "<PERSON>e av fins<PERSON>t tuffstein", "block.minecraft.polished_tuff_stairs": "Trapp av fins<PERSON>t tuffstein", "block.minecraft.polished_tuff_wall": "<PERSON>r av fins<PERSON>t tuffstein", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Poteter", "block.minecraft.potted_acacia_sapling": "Akasiespire i potte", "block.minecraft.potted_allium": "Prydløk i potte", "block.minecraft.potted_azalea_bush": "Asalea i potte", "block.minecraft.potted_azure_bluet": "Houstonia caerulea i potte", "block.minecraft.potted_bamboo": "Bambus i potte", "block.minecraft.potted_birch_sapling": "Bjørkespire i potte", "block.minecraft.potted_blue_orchid": "Blå orkidé i potte", "block.minecraft.potted_brown_mushroom": "<PERSON>run sopp i potte", "block.minecraft.potted_cactus": "<PERSON>kt<PERSON> i potte", "block.minecraft.potted_cherry_sapling": "Kirsebærspire i potte", "block.minecraft.potted_closed_eyeblossom": "Lukket øyeblom i potte", "block.minecraft.potted_cornflower": "Kornblost i potte", "block.minecraft.potted_crimson_fungus": "Karmosinsopp i potte", "block.minecraft.potted_crimson_roots": "Karmosinrøtter i potte", "block.minecraft.potted_dandelion": "Løvetann i potte", "block.minecraft.potted_dark_oak_sapling": "Mørkeikespire i potte", "block.minecraft.potted_dead_bush": "Død busk i potte", "block.minecraft.potted_fern": "Bregne i potte", "block.minecraft.potted_flowering_azalea_bush": "Blomstrende asalea i potte", "block.minecraft.potted_jungle_sapling": "Jungeltrespire i potte", "block.minecraft.potted_lily_of_the_valley": "Liljekonvall i potte", "block.minecraft.potted_mangrove_propagule": "Mangrovebulbill i potte", "block.minecraft.potted_oak_sapling": "Eikespire i potte", "block.minecraft.potted_open_eyeblossom": "Å<PERSON> øyeblom i potte", "block.minecraft.potted_orange_tulip": "Oransje tulipan i potte", "block.minecraft.potted_oxeye_daisy": "Prestekrage i potte", "block.minecraft.potted_pale_oak_sapling": "Blekeikespire i potte", "block.minecraft.potted_pink_tulip": "Rosa tulipan i potte", "block.minecraft.potted_poppy": "<PERSON><PERSON><PERSON> i potte", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON><PERSON> sopp i potte", "block.minecraft.potted_red_tulip": "<PERSON><PERSON><PERSON> tulipan i potte", "block.minecraft.potted_spruce_sapling": "Granspire i potte", "block.minecraft.potted_torchflower": "Fakkelblomst i potte", "block.minecraft.potted_warped_fungus": "Forvridd sopp i potte", "block.minecraft.potted_warped_roots": "Forvridde røtter i potte", "block.minecraft.potted_white_tulip": "Hvit tulipan i potte", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON> i potte", "block.minecraft.powder_snow": "Puddersnø", "block.minecraft.powder_snow_cauldron": "Gryte med snø", "block.minecraft.powered_rail": "Redstonedrevet skinne", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Helle av prismarinmur", "block.minecraft.prismarine_brick_stairs": "Trapp av prismarinmur", "block.minecraft.prismarine_bricks": "Prismarinmur", "block.minecraft.prismarine_slab": "Helle av prismarin", "block.minecraft.prismarine_stairs": "Trapp av prismarin", "block.minecraft.prismarine_wall": "<PERSON>r av prismarin", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Gresskarstilk", "block.minecraft.purple_banner": "<PERSON><PERSON> banner", "block.minecraft.purple_bed": "<PERSON><PERSON> seng", "block.minecraft.purple_candle": "<PERSON><PERSON> v<PERSON>s", "block.minecraft.purple_candle_cake": "Kake med lilla lys", "block.minecraft.purple_carpet": "<PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON> sement", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> glasert terrakotta", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Lillafarget glass", "block.minecraft.purple_stained_glass_pane": "Lillafarget glassrute", "block.minecraft.purple_terracotta": "<PERSON><PERSON> terrakotta", "block.minecraft.purple_wool": "<PERSON><PERSON> ull", "block.minecraft.purpur_block": "Purpurblokk", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "block.minecraft.purpur_slab": "Helle av purpur", "block.minecraft.purpur_stairs": "Trapp av purpur", "block.minecraft.quartz_block": "Kvartsblokk", "block.minecraft.quartz_bricks": "Kvartsmur", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON>søyle", "block.minecraft.quartz_slab": "Helle av kvarts", "block.minecraft.quartz_stairs": "Trapp av kvarts", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Blokk av kobbererts", "block.minecraft.raw_gold_block": "Blokk av gullerts", "block.minecraft.raw_iron_block": "Blokk av jernerts", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> banner", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON>ng", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON> vokslys", "block.minecraft.red_candle_cake": "<PERSON>ke med rødt lys", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON> se<PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON> g<PERSON> terrakotta", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "Helle av rødt nethertegl", "block.minecraft.red_nether_brick_stairs": "Trapp av rødt nethertegl", "block.minecraft.red_nether_brick_wall": "<PERSON>r av rødt nethertegl", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON>her<PERSON>gl", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON> sand", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON>e av rød sandstein", "block.minecraft.red_sandstone_stairs": "Trapp av rød sandstein", "block.minecraft.red_sandstone_wall": "<PERSON>r av rød <PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "Rødfarget glass", "block.minecraft.red_stained_glass_pane": "R<PERSON><PERSON><PERSON><PERSON> glassrute", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> tulipan", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.redstone_block": "Redstoneblokk", "block.minecraft.redstone_lamp": "Redstonelampe", "block.minecraft.redstone_ore": "Redstonemalm", "block.minecraft.redstone_torch": "Redstonefakkel", "block.minecraft.redstone_wall_torch": "Redstoneveggfakkel", "block.minecraft.redstone_wire": "Redstonespor", "block.minecraft.reinforced_deepslate": "<PERSON><PERSON>", "block.minecraft.repeater": "Redstoneforsterker", "block.minecraft.repeating_command_block": "Repeterende kommandoblokk", "block.minecraft.resin_block": "Kvaeblokk", "block.minecraft.resin_brick_slab": "Helle av kvaetegl", "block.minecraft.resin_brick_stairs": "Trapp av kvaetegl", "block.minecraft.resin_brick_wall": "Mur av kvaetegl", "block.minecraft.resin_bricks": "Kvaetegl", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "Livsanker", "block.minecraft.rooted_dirt": "Rotjord", "block.minecraft.rose_bush": "Rosebusk", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "<PERSON>e av sandstein", "block.minecraft.sandstone_stairs": "Trapp av sandstein", "block.minecraft.sandstone_wall": "<PERSON><PERSON> av sandstein", "block.minecraft.scaffolding": "<PERSON>as", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculkkatalysator", "block.minecraft.sculk_sensor": "Sculksensor", "block.minecraft.sculk_shrieker": "Sculkskriker", "block.minecraft.sculk_vein": "Sculkåre", "block.minecraft.sea_lantern": "Sjølanterne", "block.minecraft.sea_pickle": "Sjøagurk", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Startpunkt satt", "block.minecraft.short_dry_grass": "<PERSON>rt tørt gress", "block.minecraft.short_grass": "<PERSON>rt gress", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Shulkerboks", "block.minecraft.skeleton_skull": "Skjelettskalle", "block.minecraft.skeleton_wall_skull": "Skjelettveggskalle", "block.minecraft.slime_block": "Slimblokk", "block.minecraft.small_amethyst_bud": "Liten ametystknopp", "block.minecraft.small_dripleaf": "Lite drå<PERSON>blad", "block.minecraft.smithing_table": "Smibenk", "block.minecraft.smoker": "Røykovn", "block.minecraft.smooth_basalt": "Jevn basalt", "block.minecraft.smooth_quartz": "Jevn kvartsblokk", "block.minecraft.smooth_quartz_slab": "Helle av jevn k<PERSON>ts", "block.minecraft.smooth_quartz_stairs": "Trapp av jevn k<PERSON>ts", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON><PERSON> rød <PERSON>", "block.minecraft.smooth_red_sandstone_slab": "<PERSON>e av jevn rød <PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "Trapp av jevn rød <PERSON>", "block.minecraft.smooth_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON>e av jevn sandstein", "block.minecraft.smooth_sandstone_stairs": "Trapp av jevn sandstein", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON> stein", "block.minecraft.smooth_stone_slab": "<PERSON>e av jevn stein", "block.minecraft.sniffer_egg": "S<PERSON>fseregg", "block.minecraft.snow": "Snø", "block.minecraft.snow_block": "Snøblokk", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "Sjeleild", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "Sjelesand", "block.minecraft.soul_soil": "Sjele<PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "Sjeleveggfakkel", "block.minecraft.spawn.not_valid": "Enten har du ingen seng eller noe ladet liv<PERSON>, eller så var noe i veien", "block.minecraft.spawner": "Ska<PERSON>ningsmaner", "block.minecraft.spawner.desc1": "Benytt fremkallingsegg:", "block.minecraft.spawner.desc2": "<PERSON><PERSON> s<PERSON>", "block.minecraft.sponge": "Svamp", "block.minecraft.spore_blossom": "Mose<PERSON><PERSON>mst", "block.minecraft.spruce_button": "Granknapp", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence": "<PERSON>g<PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "Grangrind", "block.minecraft.spruce_hanging_sign": "Hengende granskilt", "block.minecraft.spruce_leaves": "Granbar", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "Grantrykkplate", "block.minecraft.spruce_sapling": "Granspire", "block.minecraft.spruce_sign": "Granskilt", "block.minecraft.spruce_slab": "Granhelle", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "Granfallem", "block.minecraft.spruce_wall_hanging_sign": "Vegghengende granskilt", "block.minecraft.spruce_wall_sign": "Granveggskilt", "block.minecraft.spruce_wood": "<PERSON><PERSON>", "block.minecraft.sticky_piston": "Klebrig stempel", "block.minecraft.stone": "<PERSON>", "block.minecraft.stone_brick_slab": "Helle av steinmur", "block.minecraft.stone_brick_stairs": "Trapp av steinmur", "block.minecraft.stone_brick_wall": "<PERSON>r av steinmur", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "Steintrykkplate", "block.minecraft.stone_slab": "Helle av stein", "block.minecraft.stone_stairs": "Trapp av stein", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON> akasie<PERSON>ømmer", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON><PERSON> aka<PERSON>", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON> bambusblokk", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON> bjørketømmer", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON> bjørketre", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON> kirseb<PERSON>ømmer", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON> kirseb<PERSON>re", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON> karm<PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON> mørkeiketømmer", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON> mørkeiketre", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON> j<PERSON>", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON> jung<PERSON>", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON> man<PERSON>", "block.minecraft.stripped_mangrove_wood": "Slindet mangrovetre", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON> ble<PERSON>", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON> grantre", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON> forvridd hyfe", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON> forvridd stilk", "block.minecraft.structure_block": "Strukturblokk", "block.minecraft.structure_void": "Struktur-tomrom", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Solsikke", "block.minecraft.suspicious_gravel": "Mistenkelig grus", "block.minecraft.suspicious_sand": "Mistenkelig sand", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tall_dry_grass": "<PERSON><PERSON> tørt gress", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON> gress", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON> sjø<PERSON>", "block.minecraft.target": "Blink", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Testblokk", "block.minecraft.test_instance_block": "Testinstanseblokk", "block.minecraft.tinted_glass": "Sotet glass", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-eksplosjoner er slått av", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Fakkelblomst", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON>mstavling", "block.minecraft.trapped_chest": "Utløserkiste", "block.minecraft.trial_spawner": "Prøvelsesfremkaller", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON>bletrådkrok", "block.minecraft.tube_coral": "Orgelkorall", "block.minecraft.tube_coral_block": "Orgelkorallblokk", "block.minecraft.tube_coral_fan": "Orgelkorallvifte", "block.minecraft.tube_coral_wall_fan": "Orgelkorallveggvifte", "block.minecraft.tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Helle av tuffsteintegl", "block.minecraft.tuff_brick_stairs": "Trapp av tuffsteintegl", "block.minecraft.tuff_brick_wall": "Mur av tuffsteintegl", "block.minecraft.tuff_bricks": "Tuffsteintegl", "block.minecraft.tuff_slab": "Helle av tuffstein", "block.minecraft.tuff_stairs": "Trapp av tuffstein", "block.minecraft.tuff_wall": "<PERSON>r av tuffstein", "block.minecraft.turtle_egg": "Skilpaddeegg", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.vault": "Hvelv", "block.minecraft.verdant_froglight": "Ferskt froskelys", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "<PERSON><PERSON>", "block.minecraft.wall_torch": "Vegg<PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON> knapp", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON> dør", "block.minecraft.warped_fence": "Forv<PERSON>d gjerde", "block.minecraft.warped_fence_gate": "Forvridd grind", "block.minecraft.warped_fungus": "Forvridd sopp", "block.minecraft.warped_hanging_sign": "Hengende forvridd skilt", "block.minecraft.warped_hyphae": "Forvridd hyfe", "block.minecraft.warped_nylium": "Forvridd nylium", "block.minecraft.warped_planks": "Forvridde planker", "block.minecraft.warped_pressure_plate": "Forvridd trykkplate", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON><PERSON> skilt", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON> helle", "block.minecraft.warped_stairs": "Forvridde trapper", "block.minecraft.warped_stem": "Forv<PERSON>d stilk", "block.minecraft.warped_trapdoor": "Forvridd fallem", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> forvridd skilt", "block.minecraft.warped_wall_sign": "Forvridd veggskilt", "block.minecraft.warped_wart_block": "Forvridd vorteblokk", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "G<PERSON>te med vann", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON> uthugget kobber", "block.minecraft.waxed_copper_block": "Blokk av vokset kobber", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON> kob<PERSON>pære", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON> kobber<PERSON>", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON><PERSON> kobberfallem", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON> sk<PERSON> kobber", "block.minecraft.waxed_cut_copper_slab": "Helle av vokset skåret kobber", "block.minecraft.waxed_cut_copper_stairs": "Trapp av vokset skåret kobber", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON> utsatt uthugget kobber", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON> u<PERSON> kobber", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON>set utsatt kobberpære", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON> utsatt kobberdør", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON> u<PERSON> kobberrist", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON>okset utsatt kobberfallem", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON><PERSON> u<PERSON> skåret kobber", "block.minecraft.waxed_exposed_cut_copper_slab": "Helle av vokset utsatt skåret kobber", "block.minecraft.waxed_exposed_cut_copper_stairs": "Trapp av vokset utsatt skåret kobber", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON> irret uthugget kobber", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON> irret kobber", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON> irret kobberpære", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON> irret kobberdør", "block.minecraft.waxed_oxidized_copper_grate": "<PERSON><PERSON><PERSON> irret kobberrist", "block.minecraft.waxed_oxidized_copper_trapdoor": "<PERSON><PERSON>set irret kobberfallem", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON> irret skåret kobber", "block.minecraft.waxed_oxidized_cut_copper_slab": "Helle av vokset irret skåret kobber", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Trapp av vokset irret skåret kobber", "block.minecraft.waxed_weathered_chiseled_copper": "Vokset værbitt uthugget kobber", "block.minecraft.waxed_weathered_copper": "Vokset værbitt kobber", "block.minecraft.waxed_weathered_copper_bulb": "Vokset værbitt kobberpære", "block.minecraft.waxed_weathered_copper_door": "Vokset værbitt kobberdør", "block.minecraft.waxed_weathered_copper_grate": "Vokset værbitt kobberrist", "block.minecraft.waxed_weathered_copper_trapdoor": "Vokset værbitt kobberfallem", "block.minecraft.waxed_weathered_cut_copper": "Vokset værbitt skåret kobber", "block.minecraft.waxed_weathered_cut_copper_slab": "Helle av vokset værbitt skåret kobber", "block.minecraft.waxed_weathered_cut_copper_stairs": "Trapp av vokset værbitt skåret kobber", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> uthugget kobber", "block.minecraft.weathered_copper": "Væ<PERSON>itt kobber", "block.minecraft.weathered_copper_bulb": "Værbitt kobberpære", "block.minecraft.weathered_copper_door": "Værbitt kobberdør", "block.minecraft.weathered_copper_grate": "Værbitt kobberrist", "block.minecraft.weathered_copper_trapdoor": "Værbitt kobberfallem", "block.minecraft.weathered_cut_copper": "<PERSON>ærb<PERSON> skåret kobber", "block.minecraft.weathered_cut_copper_slab": "Helle av værbitt skåret kobber", "block.minecraft.weathered_cut_copper_stairs": "Trapp av værbitt skåret kobber", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON> svamp", "block.minecraft.wheat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_banner": "H<PERSON>tt banner", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON> seng", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>", "block.minecraft.white_candle_cake": "<PERSON>ke med hvitt lys", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON>t sement", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON> glasert terrakotta", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.white_stained_glass": "Hvitfarget glass", "block.minecraft.white_stained_glass_pane": "Hvitfarget glassrute", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> tulipan", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON> ull", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Witherskjelettskalle", "block.minecraft.wither_skeleton_wall_skull": "Witherskjelettveggskalle", "block.minecraft.yellow_banner": "<PERSON><PERSON> banner", "block.minecraft.yellow_bed": "<PERSON><PERSON> seng", "block.minecraft.yellow_candle": "<PERSON><PERSON> vok<PERSON><PERSON>s", "block.minecraft.yellow_candle_cake": "Kake med gult lys", "block.minecraft.yellow_carpet": "<PERSON><PERSON> te<PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "Gul sement", "block.minecraft.yellow_glazed_terracotta": "Gul glasert terrakotta", "block.minecraft.yellow_shulker_box": "Gul shulkerboks", "block.minecraft.yellow_stained_glass": "Gulfarget glass", "block.minecraft.yellow_stained_glass_pane": "Gulfarget glassrute", "block.minecraft.yellow_terracotta": "Gul terrakotta", "block.minecraft.yellow_wool": "<PERSON><PERSON> ull", "block.minecraft.zombie_head": "Zombiehode", "block.minecraft.zombie_wall_head": "Zombievegghode", "book.byAuthor": "av %1$s", "book.edit.title": "Bokredigeringsskjerm", "book.editTitle": "Skriv inn boktittel:", "book.finalizeButton": "Signer og lukk", "book.finalizeWarning": "Husk! Når du har signert boka kan du ikke skrive mer i den.", "book.generation.0": "Original", "book.generation.1": "Kopi av original", "book.generation.2": "Kopi av kopi", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Ugyldig boktagg *", "book.pageIndicator": "Side %1$s av %2$s", "book.page_button.next": "Neste side", "book.page_button.previous": "Forrige side", "book.sign.title": "Bokunderskriftskjerm", "book.sign.titlebox": "<PERSON><PERSON><PERSON>", "book.signButton": "Signer", "book.view.title": "Bokvisningsskjerm", "build.tooHigh": "Høyden til den øvre byggegrensen er %s", "chat.cannotSend": "Kunne ikke sende nettpratmeldingen", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klikk for å teleportere", "chat.copy": "Legg i utklippstavle", "chat.copy.click": "Klikk for å kopiere til utklippstavlen", "chat.deleted_marker": "<PERSON>ne meldingen har blitt slettet av serveren.", "chat.disabled.chain_broken": "Nettprat avsperret grunnet kjedebrudd. Vennligst prøv å koble til på nytt.", "chat.disabled.expiredProfileKey": "Nettprat sperret grunnet utløpt offentlig nøkkel vedlagt profil. Vennligst prøv å koble til på nytt.", "chat.disabled.invalid_command_signature": "Kommandoen hadde uforventede eller manglende kommandoargument-signaturer.", "chat.disabled.invalid_signature": "Nettpraten hadde en ugyldig signatur. Vennligst prøv å koble til på nytt.", "chat.disabled.launcher": "Nettprat sperret grunnet oppstartsvalg. Kan ikke sende melding.", "chat.disabled.missingProfileKey": "Nettprat sperret grunnet manglende offentlig nøkkel vedlagt profil. Vennligst prøv å koble til på nytt.", "chat.disabled.options": "<PERSON>t<PERSON>t sperret grunnet k<PERSON>valg.", "chat.disabled.out_of_order_chat": "Nettprat mottat i feil rekkefølge. Har systemtiden din endret seg?", "chat.disabled.profile": "Nettprat sperret grunnet kontoinnstillinger. Trykk på '%s' igjen for mer informasjon.", "chat.disabled.profile.moreInfo": "<PERSON><PERSON><PERSON><PERSON> sperret grunnet kontoinnstillinger. Kan ikke sende eller se meldinger.", "chat.editBox": "nett<PERSON>t", "chat.filtered": "Filtrert av serveren.", "chat.filtered_full": "<PERSON><PERSON> har skjult meldingen din fra visse spillere.", "chat.link.confirm": "Er du sikker på at du vil åpne følgende nettside?", "chat.link.confirmTrusted": "Vil du åpne denne lenken eller kopiere den til utklippstavlen?", "chat.link.open": "Åpne i nettleser", "chat.link.warning": "Du må aldri åpne lenker fra personer du ikke stoler på!", "chat.queue": "[+%s følgende linje(r)]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sendte ugyldig melding.", "chat.tag.modified": "Melding endret av serveren. Opprinnelig:", "chat.tag.not_secure": "Uverifisert melding. Kan ikke rapporteres.", "chat.tag.system": "Servermelding. Kan ikke rapporteres.", "chat.tag.system_single_player": "Servermelding.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s har fullført utfordringen %s", "chat.type.advancement.goal": "%s har n<PERSON><PERSON> m<PERSON> %s", "chat.type.advancement.task": "%s har tatt fremskrittet %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Send melding til laget", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sier %s", "chat.validation_error": "Feil under validering av nettprat", "chat_screen.message": "Melding som sendes: %s", "chat_screen.title": "Nettpratsskjerm", "chat_screen.usage": "Skriv inn melding og trykk Enter for å sende", "chunk.toast.checkLog": "Se loggen for flere detaljer", "chunk.toast.loadFailure": "Kunne ikke laste inn stykke ved %s", "chunk.toast.lowDiskSpace": "Lite lagringsplass!", "chunk.toast.lowDiskSpace.description": "Muligvis ikke i stand til å lagre verdenen.", "chunk.toast.saveFailure": "Kunne ikke lagre stykke ved %s", "clear.failed.multiple": "Ingen gjenstander ble funnet på %s spillere", "clear.failed.single": "Ingen gjenstander ble funnet på spilleren %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blå", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "Grå", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Lyseblå", "color.minecraft.light_gray": "Lysegrå", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Oransje", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "Gul", "command.context.here": "<--[HER]", "command.context.parse_error": "%s på posisjon %s: %s", "command.exception": "Kunne ikke tolke kommando: %s", "command.expected.separator": "Mellomromstegn forventet for å ende argument, men videre data ble funnet", "command.failed": "En uventet feil oppsto under et forsøk på å utføre kommandoen", "command.forkLimit": "Maksimalt antall kontekster (%s) nådd", "command.unknown.argument": "<PERSON><PERSON><PERSON><PERSON> argument for kommando", "command.unknown.command": "<PERSON><PERSON><PERSON><PERSON> eller uferdig kommando. Se under for feil", "commands.advancement.criterionNotFound": "Fremskrittet %1$s inneholder ikke kriteriet %2$s", "commands.advancement.grant.criterion.to.many.failure": "Kunne ikke innvilge kriteriet '%s' for fremskrittet %s til %s spillere fordi de allerede har det", "commands.advancement.grant.criterion.to.many.success": "Innvilget kriteriet '%s' for fremskrittet %s til %s spillere", "commands.advancement.grant.criterion.to.one.failure": "Kunne ikke innvilge kriteriet '%s' for fremskrittet %s til %s fordi de allerede har det", "commands.advancement.grant.criterion.to.one.success": "Innvilget kriteriet '%s' for fremskrittet %s til %s", "commands.advancement.grant.many.to.many.failure": "Kunne ikke innvilge %s fremskritt til %s spillere fordi de allerede har dem", "commands.advancement.grant.many.to.many.success": "Innvilget %s fremskritt til %s spillere", "commands.advancement.grant.many.to.one.failure": "Kunne ikke innvilge %s fremskritt til %s fordi de allerede har dem", "commands.advancement.grant.many.to.one.success": "Innvilget %s fremskritt til %s", "commands.advancement.grant.one.to.many.failure": "Kunne ikke innvilge fremskrittet %s til %s spillere fordi de allerede har det", "commands.advancement.grant.one.to.many.success": "Innvilget fremskrittet %s til %s spillere", "commands.advancement.grant.one.to.one.failure": "Kunne ikke innvilge fremskrittet %s til %s fordi de allerede har det", "commands.advancement.grant.one.to.one.success": "Innvilget fremskrittet %s til %s", "commands.advancement.revoke.criterion.to.many.failure": "Kunne ikke fjerne kriteriet '%s' for fremskrittet %s fra %s spillere fordi de ikke har det", "commands.advancement.revoke.criterion.to.many.success": "Fjernet kriteriet '%s' for fremskrittet %s fra %s spillere", "commands.advancement.revoke.criterion.to.one.failure": "Kunne ikke fjerne kriteriet '%s' for fremskrittet %s fra %s fordi de ikke har det", "commands.advancement.revoke.criterion.to.one.success": "Fjernet kriteriet '%s' for fremskrittet %s fra %s", "commands.advancement.revoke.many.to.many.failure": "Kunne ikke fjerne %s fremskritt fra %s spillere fordi de ikke har dem", "commands.advancement.revoke.many.to.many.success": "Fjernet %s fremskritt fra %s spillere", "commands.advancement.revoke.many.to.one.failure": "Kunne ikke fjerne %s fremskritt fra %s fordi de ikke har dem", "commands.advancement.revoke.many.to.one.success": "Fjernet %s fremskritt fra %s", "commands.advancement.revoke.one.to.many.failure": "Kunne ikke fjerne fremskrittet %s fra %s spillere fordi de ikke har det", "commands.advancement.revoke.one.to.many.success": "Fjernet fremskrittet %s fra %s spillere", "commands.advancement.revoke.one.to.one.failure": "Kunne ikke fjerne fremskrittet %s fra %s fordi de ikke har det", "commands.advancement.revoke.one.to.one.success": "Fjernet fremskrittet %s fra %s", "commands.attribute.base_value.get.success": "Grunnverdi til attributtet %s til ening %s er %s", "commands.attribute.base_value.reset.success": "Grunnverdi til attributt %s for enhet %s tilbakestilt til forvalg %s", "commands.attribute.base_value.set.success": "Satte grunnverdi til attributtet %s til ening %s til %s", "commands.attribute.failed.entity": "%s er ikke en gyldig enhet for denne kommandoen", "commands.attribute.failed.modifier_already_present": "Modifisør %s er alt lagt til attributt %s til ening %s", "commands.attribute.failed.no_attribute": "Enhet %s har ikke attributt %s", "commands.attribute.failed.no_modifier": "Attributt %s til ening %s har ingen modifisør %s", "commands.attribute.modifier.add.success": "La til modifisør %s til attributt %s til ening %s", "commands.attribute.modifier.remove.success": "Fjernet modifisør %s fra attributt %s til ening %s", "commands.attribute.modifier.value.get.success": "Verdien til modifisør %s til attributt %s til enhet %s er %s", "commands.attribute.value.get.success": "Verdi til attributt %s til enhet %s er %s", "commands.ban.failed": "Ingenting endret, spilleren er allerede bannlyst", "commands.ban.success": "Bannlyste %s: %s", "commands.banip.failed": "Ingenting endret, denne <PERSON>en er allerede bannlyst", "commands.banip.info": "Denne bannlysningen påvirker %s spiller(e): %s", "commands.banip.invalid": "Ugyldig IP-adresse eller ukjent spiller", "commands.banip.success": "Bannlyste IP-adressen %s: %s", "commands.banlist.entry": "%s ble bannlyst av %s: %s", "commands.banlist.entry.unknown": "(<PERSON><PERSON><PERSON><PERSON>)", "commands.banlist.list": "Det er %s bannlysning(er):", "commands.banlist.none": "Det er ingen bann<PERSON><PERSON>er", "commands.bossbar.create.failed": "Et bossmeter med IDen '%s' finnes alt", "commands.bossbar.create.success": "Skapte egendefinert bossmeter %s", "commands.bossbar.get.max": "Egendefinert bossmeter %s har et maksimum på %s", "commands.bossbar.get.players.none": "Egendefinert bossmeter %s har ingen nåværende spillere koblet til", "commands.bossbar.get.players.some": "Egendefinert bossmeter %s har %s nåværende spiller(e) koblet til: %s", "commands.bossbar.get.value": "Egendefinert bossmeter %s har en verdi på %s", "commands.bossbar.get.visible.hidden": "Egendefinert bossmeter %s er for øyeblikket skjult", "commands.bossbar.get.visible.visible": "Egendefinert bossmeter %s er for øyeblikket synlig", "commands.bossbar.list.bars.none": "Det er ingen egendefinerte bossmetre aktive", "commands.bossbar.list.bars.some": "Det er %s egendefinert(e) bossmeter/bossmetre aktivt/aktive: %s", "commands.bossbar.remove.success": "Fjernet egendefinert bossmeter %s", "commands.bossbar.set.color.success": "Egendefinert bossmeter %s har endret farge", "commands.bossbar.set.color.unchanged": "Ingenting endret seg. Det er alt fargen på dette bossmeteret", "commands.bossbar.set.max.success": "Egendefinert bossmeter %s har endret maksimum til %s", "commands.bossbar.set.max.unchanged": "Ingenting endret seg. Det er alt maksimumet til dette bossmeteret", "commands.bossbar.set.name.success": "Egendefinert bossmeter %s fikk endret navn", "commands.bossbar.set.name.unchanged": "Ingenting endret seg. Det er alt navnet på dette bossmeteret", "commands.bossbar.set.players.success.none": "Egendefinert bossmeter %s har ikke lenger noen spillere", "commands.bossbar.set.players.success.some": "Egendefinert bossmeter %s har nå %s spiller(e): %s", "commands.bossbar.set.players.unchanged": "Ingenting endret seg. De spillerne er alt med på bossmeteret; ingen las til eller fjerntes", "commands.bossbar.set.style.success": "Egendefinert bossmeter %s har endret stil", "commands.bossbar.set.style.unchanged": "Ingenting endret seg. Det er alt stilen til dette bossmeteret", "commands.bossbar.set.value.success": "Egendefinert bossmeter %s har endret verdi til %s", "commands.bossbar.set.value.unchanged": "Ingenting endret seg. Det er alt verdien til dette bossmeteret", "commands.bossbar.set.visibility.unchanged.hidden": "Ingenting endret seg. Boss<PERSON> er alt skjult", "commands.bossbar.set.visibility.unchanged.visible": "Ingenting endret seg. Boss<PERSON> er alt synlig", "commands.bossbar.set.visible.success.hidden": "Egendefinert bossmeter %s er nå skjult", "commands.bossbar.set.visible.success.visible": "Egendefinert bossmeter %s er nå synlig", "commands.bossbar.unknown": "Intet bossmeter finnes med IDen '%s'", "commands.clear.success.multiple": "Fjernet %s gjenstand(er) fra %s spillere", "commands.clear.success.single": "Fjernet %s gjenstand(er) fra spiller %s", "commands.clear.test.multiple": "Fant %s passende gjenstand(er) på %s spillere", "commands.clear.test.single": "Fant %s passende gjenstand(er) på spilleren %s", "commands.clone.failed": "Ingen blokker ble klonet", "commands.clone.overlap": "Kilde og mål kan ikke overlappe", "commands.clone.success": "Lyktes med å klone %s blokk(er)", "commands.clone.toobig": "For mange blokker i det angitte området (maksimum %s, angitt %s)", "commands.damage.invulnerable": "Slik skade har ingen virkning på målet", "commands.damage.success": "Gjorde %s skade på %s", "commands.data.block.get": "%s ved blokk %s, %s, %s etter skalafaktor av %s er %s", "commands.data.block.invalid": "Målblokken er ikke en blokkenhet", "commands.data.block.modified": "<PERSON><PERSON> blok<PERSON>en ved %s, %s, %s", "commands.data.block.query": "%s, %s, %s har følgende blokkdata: %s", "commands.data.entity.get": "%s på %s etter skalafaktor av %s er %s", "commands.data.entity.invalid": "<PERSON><PERSON> ikke endre <PERSON>", "commands.data.entity.modified": "<PERSON><PERSON> enhe<PERSON>en til %s", "commands.data.entity.query": "%s har følgende enhetsdata: %s", "commands.data.get.invalid": "Kan ikke hente %s, bare numeriske tagger er tillat", "commands.data.get.multiple": "Dette argumentet aksepterer en enkel NBT-verdi", "commands.data.get.unknown": "Kan ikkje hente %s. Taggen eksisterer ikke", "commands.data.merge.failed": "Ingen endring. De gitte egenskapene har alt disse verdiene", "commands.data.modify.expected_list": "Forventet liste; fikk: %s", "commands.data.modify.expected_object": "Forventet objekt; fikk: %s", "commands.data.modify.expected_value": "Forventet verdi; fikk: %s", "commands.data.modify.invalid_index": "Ugyldig listeindeks: %s", "commands.data.modify.invalid_substring": "Ugyldige delstrengsindekser: %s til %s", "commands.data.storage.get": "%s i lagring %s etter skalafaktor av %s er %s", "commands.data.storage.modified": "Modifiserte lager %s", "commands.data.storage.query": "Lager %s har det følgende innholdet: %s", "commands.datapack.create.already_exists": "Pakke med navn '%s' finnes allerede", "commands.datapack.create.invalid_full_name": "Ugyldig nytt pakkenavn '%s'", "commands.datapack.create.invalid_name": "Ugyldige tegn i nytt pakkenavn '%s'", "commands.datapack.create.io_failure": "Kan ikke opprette pakke med navn '%s'. Sjekk loggen", "commands.datapack.create.metadata_encode_failure": "Lyktes ikke med å kode metadata for pakke med navn '%s': %s", "commands.datapack.create.success": "Skapte ny tom pakke med navn '%s'", "commands.datapack.disable.failed": "Pakke '%s' er ikke aktivert!", "commands.datapack.disable.failed.feature": "Pakke '%s' kan ikke slås av fordi den betinges av et påsl<PERSON><PERSON> flagg!", "commands.datapack.enable.failed": "Pak<PERSON> '%s' er allerede aktivert!", "commands.datapack.enable.failed.no_flags": "Pakke '%s' kan ikke legges til siden den betinger flagg som ikke er på i denne verdenen: %s!", "commands.datapack.list.available.none": "Det er ingen flere datapakker tilgjengelig", "commands.datapack.list.available.success": "Det er %s datapakke(r) tilgjengelig(e): %s", "commands.datapack.list.enabled.none": "Det er ingen datapakker aktivert", "commands.datapack.list.enabled.success": "Det er lagt til %s datapakke(r): %s", "commands.datapack.modify.disable": "Skrur av datapakke %s", "commands.datapack.modify.enable": "Skrur på datapakke %s", "commands.datapack.unknown": "Ukjent datapakke '%s'", "commands.debug.alreadyRunning": "Tikkprofiløren har alt startet", "commands.debug.function.noRecursion": "Kan ikke spore innenfra en funksjon", "commands.debug.function.noReturnRun": "Sporing kan ikke brukes med 'return run'", "commands.debug.function.success.multiple": "Skrev utfallet av sporing av %s kommando(er) fra %s funksjoner til fil %s", "commands.debug.function.success.single": "Skrev utfallet av sporing av %s kommando(er) fra funksjonen \"%s\" til fil %s", "commands.debug.function.traceFailed": "Lyktes ikke med å spore funksjon", "commands.debug.notRunning": "Tikkprofiløren har ikke startet", "commands.debug.started": "Startet tikkprofilering", "commands.debug.stopped": "Stoppet tikkprofilering etter %s sekund(er) og %s tikk (%s tikk per sekund)", "commands.defaultgamemode.success": "Standardspillmodusen er nå %s", "commands.deop.failed": "Ingenting endret. Spilleren er ikke en operatør", "commands.deop.success": "Fjernet %s som serveroperatør", "commands.dialog.clear.multiple": "Rensket dialog til %s spillere", "commands.dialog.clear.single": "Rensket dialog til %s", "commands.dialog.show.multiple": "Viste dialog til %s spillere", "commands.dialog.show.single": "Viste dialog til %s", "commands.difficulty.failure": "Vanskelighetsgraden ble ikke endret; den er allerede satt til '%s'", "commands.difficulty.query": "Vanskelighetsgraden er %s", "commands.difficulty.success": "Vanskelighetsgraden er satt til %s", "commands.drop.no_held_items": "Enheten kan ikke holde noen gjenstander", "commands.drop.no_loot_table": "Enhet %s har ingen bytte<PERSON>bell", "commands.drop.no_loot_table.block": "Blokk %s har ingen utbyttetabell", "commands.drop.success.multiple": "Slapp %s gje<PERSON><PERSON>", "commands.drop.success.multiple_with_table": "Delte ut %s ting fra byttetabell %s", "commands.drop.success.single": "Ga ut %s %s", "commands.drop.success.single_with_table": "Delte ut %s %s fra byttetabell %s", "commands.effect.clear.everything.failed": "<PERSON><PERSON><PERSON> har ingen effekter å fjerne", "commands.effect.clear.everything.success.multiple": "Fjernet alle effektene fra %s mål", "commands.effect.clear.everything.success.single": "Fjernet alle effektene fra %s", "commands.effect.clear.specific.failed": "M<PERSON>let har ikke den forespurte effekten", "commands.effect.clear.specific.success.multiple": "Fjernet effekten %s fra %s mål", "commands.effect.clear.specific.success.single": "Fjernet effekten %s fra %s", "commands.effect.give.failed": "<PERSON>nne ikke påføre denne effekten (målet er enten immun mot effekter, eller har noe sterkere)", "commands.effect.give.success.multiple": "Påførte effekten %s til %s mål", "commands.effect.give.success.single": "Påførte effekten %s til %s", "commands.enchant.failed": "Ingenting endret. <PERSON><PERSON><PERSON> har enten ingen gjenstand i hendene eller så kunne ikke trolldommen påføres", "commands.enchant.failed.entity": "%s er ikke en gyldig enhet for denne kommandoen", "commands.enchant.failed.incompatible": "%s kan ikke støtte den trolldommen", "commands.enchant.failed.itemless": "%s holder ingen gjenstand", "commands.enchant.failed.level": "%s er høyere enn maksimumnivået %s for den trolldommen", "commands.enchant.success.multiple": "Ga trolldommen %s til %s enheter", "commands.enchant.success.single": "Ga trolldommen %s til %ss gjenstand", "commands.execute.blocks.toobig": "For mange blokker i det angitte området (høyst %s, anga %s)", "commands.execute.conditional.fail": "Test feilet", "commands.execute.conditional.fail_count": "Test feilet, antall: %s", "commands.execute.conditional.pass": "Test passert", "commands.execute.conditional.pass_count": "Test passert, antall: %s", "commands.execute.function.instantiationFailure": "Mislyktes med å instansiere funksjon %s: %s", "commands.experience.add.levels.success.multiple": "Ga %s erfaringsnivå til %s spillere", "commands.experience.add.levels.success.single": "Ga %s erfaringsnivå til %s", "commands.experience.add.points.success.multiple": "Ga %s erfaringspoeng til %s spillere", "commands.experience.add.points.success.single": "Ga %s erfaringspoeng til %s", "commands.experience.query.levels": "%s har %s erfaringsnivå", "commands.experience.query.points": "%s har %s erfaringspoeng", "commands.experience.set.levels.success.multiple": "Satt %s erfaringsnivå for %s spillere", "commands.experience.set.levels.success.single": "Satt %s erfaringsnivå for %s", "commands.experience.set.points.invalid": "<PERSON><PERSON> <PERSON><PERSON><PERSON> sette erfaringspoeng over ma<PERSON><PERSON><PERSON><PERSON> poeng for spillerens nåværende nivå", "commands.experience.set.points.success.multiple": "Satt %s erfaringspoeng for %s spillere", "commands.experience.set.points.success.single": "Satt %s erfaringspoeng for %s", "commands.fill.failed": "Ingen blokker fylt", "commands.fill.success": "Lyktes med å fylle %s blokk(er)", "commands.fill.toobig": "For mange blokker i det angitte området (høyst %s, anga %s)", "commands.fillbiome.success": "Markslag satt mellom %s, %s, %s og %s, %s, %s", "commands.fillbiome.success.count": "%s markslagssøyle(r) satt mellom %s, %s, %s og %s, %s, %s", "commands.fillbiome.toobig": "For mange blokker i det angitte rommet (høyst %s, anga %s)", "commands.forceload.added.failure": "Ingen verdensbiter ble markert for tvangslasting", "commands.forceload.added.multiple": "Markerte %s verdensbiter i %s fra %s til %s som tvangslastet", "commands.forceload.added.none": "Ingen tvangslastede verdensbiter ble funnet i %s", "commands.forceload.added.single": "Markerte verdensbit %s i %s som tvangslastet", "commands.forceload.list.multiple": "%s tvangslastede verdensbiter ble funnet i %s ved: %s", "commands.forceload.list.single": "En tvangslastet verdensbit ble funnet i %s ved: %s", "commands.forceload.query.failure": "Verdensbiten ved %s i %s er ikke merket for tvunget lasting", "commands.forceload.query.success": "Verdensbiten ved %s i %s er merket for tvunget lasting", "commands.forceload.removed.all": "Avmerket alle tvangslastede verdensbiter i %s", "commands.forceload.removed.failure": "Ingen verdensbiter ble tatt bort fra tvangslasting", "commands.forceload.removed.multiple": "Avmerket %s verdensbiter for tvungen lasting i %s fra %s til %s", "commands.forceload.removed.single": "Avmerket verdensbiten %s i %s for tvunget lasting", "commands.forceload.toobig": "For mange stykker i det gitte området (%2$s av høyst %1$s)", "commands.function.error.argument_not_compound": "Ugyldig argumenttype: %s, forventet Compound", "commands.function.error.missing_argument": "Mangler argument %2$s til funksjon %1$s", "commands.function.error.missing_arguments": "Mangler argumenter til <PERSON> %s", "commands.function.error.parse": "Under instansiering av makro %s: Kommando '%s' forårsaket feil: %s", "commands.function.instantiationFailure": "Lyktes ikke med å instansiere funksjon %s: %s", "commands.function.result": "Funksjon %s returnerte %s", "commands.function.scheduled.multiple": "Kj<PERSON>rer funksjoner %s", "commands.function.scheduled.no_functions": "Kan ikke finne noen funksjoner med navn %s", "commands.function.scheduled.single": "Kjører funksjon %s", "commands.function.success.multiple": "Utførte %s kommando(er) fra %s funksjoner", "commands.function.success.multiple.result": "Utførte %s funksjoner", "commands.function.success.single": "Utførte %s kommando(er) fra funksjon '%s'", "commands.function.success.single.result": "Funksjon '%2$s' returnerte %1$s", "commands.gamemode.success.other": "Endret %ss spillmodus til %s", "commands.gamemode.success.self": "Sett egen spillmodus til %s", "commands.gamerule.query": "Spilleregelen %s er for tiden satt til: %s", "commands.gamerule.set": "Spilleregelen %s er nå satt til: %s", "commands.give.failed.toomanyitems": "Kan ikke gi flere enn %s av %s", "commands.give.success.multiple": "Ga %s %s til %s spillere", "commands.give.success.single": "Ga %s %s til %s", "commands.help.failed": "Ukjent kommando eller manglende tillatelser", "commands.item.block.set.success": "Erstattet en rute på %s, %s, %s med %s", "commands.item.entity.set.success.multiple": "Erstattet en rute på %s enheter med %s", "commands.item.entity.set.success.single": "Erstattet en rute på %s med %s", "commands.item.source.no_such_slot": "Kilden har ikke rute %s", "commands.item.source.not_a_container": "Kildeposisjon %s, %s, %s har ingen beholder", "commands.item.target.no_changed.known_item": "Ingen mål tok imot %s i rute %s", "commands.item.target.no_changes": "Ingen mål tok imot noe i rute %s", "commands.item.target.no_such_slot": "Målet har ikke rute %s", "commands.item.target.not_a_container": "Ut<PERSON><PERSON> sted %s, %s, %s har ingen beholder", "commands.jfr.dump.failed": "Lyktes ikke med å dumpe JFR-opptak: %s", "commands.jfr.start.failed": "Lyktes ikke med å starte JFR-profilering", "commands.jfr.started": "Startet JFR-profilering", "commands.jfr.stopped": "JFR-profilering stoppet og dumpet i %s", "commands.kick.owner.failed": "Kan ikke kaste ut eier av LAN-spill", "commands.kick.singleplayer.failed": "Kan ikke kaste ut i enkeltspillermodus", "commands.kick.success": "Kastet ut %s: %s", "commands.kill.success.multiple": "Drepte %s enheter", "commands.kill.success.single": "Drepte %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Det er %s av høyst %s spillere på: %s", "commands.locate.biome.not_found": "Kunne ikke finne markslaget \"%s\" i et rimelig omegn", "commands.locate.biome.success": "Nærmeste %s er ved %s (%s blokker unna)", "commands.locate.poi.not_found": "Kunne ikke finne et interessepunkt av type \"%s\" i et rimelig omegn", "commands.locate.poi.success": "Nærmeste %s er ved %s (%s blokker unna)", "commands.locate.structure.invalid": "Ingen struktur av type \"%s\" finnes", "commands.locate.structure.not_found": "Fant ingen struktur av type \"%s\" i nærheten", "commands.locate.structure.success": "Nærmeste %s er ved %s (%s blokker unna)", "commands.message.display.incoming": "%s hvisker til deg: %s", "commands.message.display.outgoing": "Du hvisker til %s: %s", "commands.op.failed": "Ingenting endret. Spilleren er allerede en operatør", "commands.op.success": "Gjorde %s til en serveroperatør", "commands.pardon.failed": "Ingenting endret. Spilleren er ikke bannlyst", "commands.pardon.success": "Opphevet bannlysing for %s", "commands.pardonip.failed": "Ingenting endret, denne <PERSON>en er ikke bannlyst", "commands.pardonip.invalid": "Ugyldig IP-adresse", "commands.pardonip.success": "Opphevet bannlysing av IP-adressen %s", "commands.particle.failed": "Partikkelen var ikke synlig for noen", "commands.particle.success": "Viser partikkel %s", "commands.perf.alreadyRunning": "Ytelsesprofiløren har alt startet", "commands.perf.notRunning": "Ytelsesprofiløren har ikke startet", "commands.perf.reportFailed": "Lyktes ikke med å skrive feilsøkingsrapport", "commands.perf.reportSaved": "Skrev feilsøkingsrapport til %s", "commands.perf.started": "Startet 10 sekunders ytelsesprofilering (bruk '/perf stop' for å stoppe tidlig)", "commands.perf.stopped": "Stoppet ytelsesprofilering etter %s sekund(er) og %s tikk (%s tikk per sekund)", "commands.place.feature.failed": "Lyktes ikke med å sette ut forekomst", "commands.place.feature.invalid": "Ingen forekomst av type \"%s\" finnes", "commands.place.feature.success": "Satte ut \"%s\" ved %s, %s, %s", "commands.place.jigsaw.failed": "Lyktes ikke med å generere puslebrikke", "commands.place.jigsaw.invalid": "Det er ingen maler av type \"%s\"", "commands.place.jigsaw.success": "Genererte puslebrikke ved %s, %s, %s", "commands.place.structure.failed": "Lyktes ikke med å plassere struktur", "commands.place.structure.invalid": "Det finnes ingen struktur av type \"%s\"", "commands.place.structure.success": "Genererte struktur \"%s\" ved %s, %s, %s", "commands.place.template.failed": "Lyktes ikke med å plassere mal", "commands.place.template.invalid": "Det er ingen mal med ID \"%s\"", "commands.place.template.success": "Lastet inn mal \"%s\" ved %s, %s, %s", "commands.playsound.failed": "Lyd<PERSON> er for langt unna til å høres", "commands.playsound.success.multiple": "Spilte lyden %s til %s spillere", "commands.playsound.success.single": "Spilte lyden %s til %s", "commands.publish.alreadyPublished": "Flerspiller-spill er nå oppe på port %s", "commands.publish.failed": "Kan ikke opprette lokalt spill", "commands.publish.started": "Lokalt spill åpnet på port %s", "commands.publish.success": "Flerspiller-spill er nå oppe på port %s", "commands.random.error.range_too_large": "Tilfeldig verdi må være høyst 2147483646", "commands.random.error.range_too_small": "Tilfeldig verdi må være minst 2", "commands.random.reset.all.success": "Tilbakestilte %s tilfeldig(e) sekvens(er)", "commands.random.reset.success": "Tilbakestilte tilfeldig sekvens %s", "commands.random.roll": "%s slo %s (mellom %s og %s)", "commands.random.sample.success": "Tilfeldig verdi: %s", "commands.recipe.give.failed": "Ingen nye oppskrifter lært", "commands.recipe.give.success.multiple": "Låste opp %s oppskrift(er) for %s spillere", "commands.recipe.give.success.single": "Låste opp %s oppskrift(er) for %s", "commands.recipe.take.failed": "Ingen oppskrifter kunne bli glemt", "commands.recipe.take.success.multiple": "Tok %s oppskrift(er) fra %s spillere", "commands.recipe.take.success.single": "Tok %s oppskrift(er) fra %s", "commands.reload.failure": "Lyktes ikke med å laste inn på nytt. Beholder gammel data", "commands.reload.success": "Laster inn på nytt!", "commands.ride.already_riding": "%s sitter allerede på %s", "commands.ride.dismount.success": "%s sluttet å sitte på %s", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON><PERSON><PERSON> kan ikke sittes på", "commands.ride.mount.failure.generic": "%s kunne ikke sette seg på %s", "commands.ride.mount.failure.loop": "Enhet kan ikke sitte på seg selv eller noen av sine passasjerer", "commands.ride.mount.failure.wrong_dimension": "Kan ikke settes på enhet i annen dimensjon", "commands.ride.mount.success": "%s satte seg på %s", "commands.ride.not_riding": "%s sitter ikke på noen farkost", "commands.rotate.success": "Snudde %s", "commands.save.alreadyOff": "Automatisk lagring er allerede skrudd av", "commands.save.alreadyOn": "Automatisk lagring er allerede skrudd på", "commands.save.disabled": "Automatisk lagring er nå slått av", "commands.save.enabled": "Automatisk lagring er nå slått på", "commands.save.failed": "Kunne ikke lagre spillet (er det nok diskplass?)", "commands.save.saving": "Lagrer spillet (dette kan ta litt tid!)", "commands.save.success": "Lagret spillet", "commands.schedule.cleared.failure": "Ingen timeplaner med id %s", "commands.schedule.cleared.success": "Fjernet %s timeplan(er) med id %s", "commands.schedule.created.function": "Tidfestet funksjon «%s» om %s tikk på spilltid %s", "commands.schedule.created.tag": "Tidfestet tagg «%s» om %s tikk på spilltid %s", "commands.schedule.macro": "Kan ikke tidfeste en macro", "commands.schedule.same_tick": "Kan ikke tidfeste til samtidig tikk", "commands.scoreboard.objectives.add.duplicate": "Det eksisterer allerede et mål med det navnet", "commands.scoreboard.objectives.add.success": "Skapte ny målsetting %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Intet endret seg. Den visning<PERSON>ruten er alt tom", "commands.scoreboard.objectives.display.alreadySet": "Intet endret seg. Den visningsruten viser alt den målsettingen", "commands.scoreboard.objectives.display.cleared": "Strøk enhver målsetting i visningsrute %s", "commands.scoreboard.objectives.display.set": "Satte visningsrute %s til å vise målsetting %s", "commands.scoreboard.objectives.list.empty": "Det finnes ingen mål", "commands.scoreboard.objectives.list.success": "Det er %s målsetting(er): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Slo av automatisk oppdatering av visning for målsetting %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Slo på automatisk oppdatering av visning for målsetting %s", "commands.scoreboard.objectives.modify.displayname": "<PERSON><PERSON> visningsnavnet til %s til %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Sløyfet forvalgt tallformat til målsetting %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "<PERSON><PERSON> forvalgt tallformat til målsetting %s", "commands.scoreboard.objectives.modify.rendertype": "<PERSON><PERSON> skild<PERSON> til måling %s", "commands.scoreboard.objectives.remove.success": "Fjernet målsetting %s", "commands.scoreboard.players.add.success.multiple": "La %s til %s for %s enheter", "commands.scoreboard.players.add.success.single": "La %s til %s for %s (nå %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Sløyfet visningsnavn til %s enheter i %s", "commands.scoreboard.players.display.name.clear.success.single": "Sløyfet visningsnavn til %s i %s", "commands.scoreboard.players.display.name.set.success.multiple": "<PERSON><PERSON> visningsnavn til %s for %s enheter i %s", "commands.scoreboard.players.display.name.set.success.single": "<PERSON><PERSON> visningsnavn til %s for %s i %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Sløyfet tallformat til %s enheter i %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Sløyfet tallformat til %s i %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Endret tallformat til %s enheter i %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Endret tallformat til %s i %s", "commands.scoreboard.players.enable.failed": "Ingenting endret. <PERSON><PERSON> u<PERSON> er allerede slått på", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON> bare på utløser-mål", "commands.scoreboard.players.enable.success.multiple": "Aktiverte utløser %s for %s enheter", "commands.scoreboard.players.enable.success.single": "Aktivert utløser %s for %s", "commands.scoreboard.players.get.null": "Kan ikke hente verdien %s for %s, ingenting er angitt", "commands.scoreboard.players.get.success": "%s har %s %s", "commands.scoreboard.players.list.empty": "Det er ingen sporede enheter", "commands.scoreboard.players.list.entity.empty": "%s har ingen poengersummer å vise", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s har %s poengsum(mer):", "commands.scoreboard.players.list.success": "Det er %s sporet/sporede enhet(er): %s", "commands.scoreboard.players.operation.success.multiple": "Oppdaterte %s for %s enheter", "commands.scoreboard.players.operation.success.single": "Satte %s for %s til %s", "commands.scoreboard.players.remove.success.multiple": "Fjernet %s fra %s for %s enheter", "commands.scoreboard.players.remove.success.single": "Fjernet %s fra %s for %s (nå %s)", "commands.scoreboard.players.reset.all.multiple": "Tilbakestilte alle poengsummer for %s enheter", "commands.scoreboard.players.reset.all.single": "Tilbakestilte alle poengsummer for %s", "commands.scoreboard.players.reset.specific.multiple": "Tilbakestilte %s for %s enheter", "commands.scoreboard.players.reset.specific.single": "Tilbaketilte %s for %s", "commands.scoreboard.players.set.success.multiple": "Satt %s for %s enheter til %s", "commands.scoreboard.players.set.success.single": "Satt %s for %s til %s", "commands.seed.success": "Frøtall: %s", "commands.setblock.failed": "<PERSON>nne ikke sette blokken", "commands.setblock.success": "<PERSON><PERSON> blokken ved %s, %s, %s", "commands.setidletimeout.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for spillerinaktivitet er nå %s minutt(er)", "commands.setidletimeout.success.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for dankeri er nå slått av", "commands.setworldspawn.failure.not_overworld": "Kan kun sette verdensstartpunkt for Oververdenen", "commands.setworldspawn.success": "Satte verdenens startpunkt til %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Satte startpunktet til %6$s spillere til %1$s, %2$s, %3$s [%4$s] i %5$s", "commands.spawnpoint.success.single": "Satte startpunktet til %6$s til %1$s, %2$s, %3$s [%4$s] i %5$s", "commands.spectate.not_spectator": "%s er ikke i tilskuermodus", "commands.spectate.self": "Kan ikke følge med på deg selv", "commands.spectate.success.started": "Følger nå med på %s", "commands.spectate.success.stopped": "<PERSON><PERSON><PERSON><PERSON> ikke lenger med på enhet", "commands.spreadplayers.failed.entities": "Kunne ikke spre %s enhet(er) omkring %s, %s (for mange enheter for plassen - prøv med spredning på høyst %s)", "commands.spreadplayers.failed.invalid.height": "Ugyldig maxHeight %s; må være større enn verdenens bunnivå %s", "commands.spreadplayers.failed.teams": "Kunne ikke spre %s lag omkring %s, %s (for mange enheter for plassen - prøv med spredning på høyst %s)", "commands.spreadplayers.success.entities": "Spredte %s enhet(er) omkring %s, %s med en gjennomsnittlig avstand på %s blokk(er) fra hverandre", "commands.spreadplayers.success.teams": "Spredte %s lag omkring %s, %s med en gjennomsnittlig avstand på %s blokk(er) fra hverandre", "commands.stop.stopping": "Stopper serveren", "commands.stopsound.success.source.any": "Stoppet alle '%s' lyder", "commands.stopsound.success.source.sound": "Stoppet lyden '%s' fra '%s'", "commands.stopsound.success.sourceless.any": "Stoppet alle lyder", "commands.stopsound.success.sourceless.sound": "Stoppet lyden '%s'", "commands.summon.failed": "Kan ikke fremkalle enhet", "commands.summon.failed.uuid": "Kunne ikke fremkalle ening på grunn av fordobling av UUIDer", "commands.summon.invalidPosition": "Ugyldig sted for fremkalling", "commands.summon.success": "Fremkalte ny %s", "commands.tag.add.failed": "<PERSON><PERSON><PERSON> har enten denne taggen fra før eller har for mange tagger", "commands.tag.add.success.multiple": "La taggen '%s' til %s enheter", "commands.tag.add.success.single": "La taggen '%s' til %s", "commands.tag.list.multiple.empty": "Det er ingen tagger på de %s enhetene", "commands.tag.list.multiple.success": "De %s enhetene har totalt %s tagger: %s", "commands.tag.list.single.empty": "%s har ingen tagger", "commands.tag.list.single.success": "%s har %s tagger: %s", "commands.tag.remove.failed": "<PERSON><PERSON><PERSON> har ikke denne taggen", "commands.tag.remove.success.multiple": "Fjernet taggen '%s' fra %s enheter", "commands.tag.remove.success.single": "Fjernet taggen '%s' fra %s", "commands.team.add.duplicate": "Det eksisterer allerede et lag med dette navnet", "commands.team.add.success": "Skapte laget %s", "commands.team.empty.success": "Fjernet %s medlem(mer) fra lag %s", "commands.team.empty.unchanged": "Ingenting endret. Det laget er allerede tomt", "commands.team.join.success.multiple": "La til %s medlemmer på laget %s", "commands.team.join.success.single": "La %s til laget %s", "commands.team.leave.success.multiple": "Fjernet %s medlemmer fra alle lag", "commands.team.leave.success.single": "Fjernet %s fra alle lag", "commands.team.list.members.empty": "Det er ingen medlemmer på laget %s", "commands.team.list.members.success": "Lag %s har %s medlem(mer): %s", "commands.team.list.teams.empty": "Det er ingen lag", "commands.team.list.teams.success": "Det er %s lag: %s", "commands.team.option.collisionRule.success": "Kollisjonsregelen for laget %s er nå \"%s\"", "commands.team.option.collisionRule.unchanged": "Ingenting endret. Kollisjonsregel har allerede den verdien", "commands.team.option.color.success": "Oppdaterte fargen til laget %s til %s", "commands.team.option.color.unchanged": "Ingenting forandret. Det laget har allerede den fargen", "commands.team.option.deathMessageVisibility.success": "Dødsmelding-synlighet for laget %s er nå \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Ingenting endret. Dødsmeldingsynligheten har allerede denne verdien", "commands.team.option.friendlyfire.alreadyDisabled": "Ingenting endret. Medlemmer av dette laget kan fortsatt ikke skade hverandre", "commands.team.option.friendlyfire.alreadyEnabled": "Ingenting endret. Medlemmer av det laget kan allerede skade hverandre", "commands.team.option.friendlyfire.disabled": "Slo av vennlig ild for laget %s", "commands.team.option.friendlyfire.enabled": "Slo på vennlig ild for laget %s", "commands.team.option.name.success": "Oppdaterte navnet til lag %s", "commands.team.option.name.unchanged": "Ingenting endret. Laget bruker allerede dette navnet", "commands.team.option.nametagVisibility.success": "Navneskiltssynlighet for laget %s er nå \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Ingenting endret. Navnetaggen har allerede den verdien", "commands.team.option.prefix.success": "Lagets prefix endret til %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Ingenting endret. Dette laget kan ikke se usynlige lagkamerater allerede", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Ingenting endret. Dette laget kan allerede se usynlige lagkamerater", "commands.team.option.seeFriendlyInvisibles.disabled": "Laget %s kan ikke lenger se usynlige lagkamerater", "commands.team.option.seeFriendlyInvisibles.enabled": "Laget %s kan nå se usynlige lagkamerater", "commands.team.option.suffix.success": "Lagets suffix endret til %s", "commands.team.remove.success": "Fjernet laget %s", "commands.teammsg.failed.noteam": "Du må være på et lag for å sende melding til laget", "commands.teleport.invalidPosition": "<PERSON><PERSON>ld<PERSON> sted for teleportering", "commands.teleport.success.entity.multiple": "Teleporterte %s enheter til %s", "commands.teleport.success.entity.single": "Teleporterte %s til %s", "commands.teleport.success.location.multiple": "Teleporterte %s enheter til %s, %s, %s", "commands.teleport.success.location.single": "Teleporterte %s til %s,%s,%s", "commands.test.batch.starting": "Starter miljø %s, batch %s", "commands.test.clear.error.no_tests": "<PERSON>nne ikke finne noen tester å fjerne", "commands.test.clear.success": "Fjernet %s struktur(er)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klikk for å kopiere til utklippstavle", "commands.test.create.success": "Laget testoppsett for test %s", "commands.test.error.no_test_containing_pos": "Kan ikke finne en testinstans som inneholder %s, %s, %s", "commands.test.error.no_test_instances": "Fant ingen testinstanser", "commands.test.error.non_existant_test": "Test %s kunne ikke finnes", "commands.test.error.structure_not_found": "Teststruktur %s kunne ikke finnes", "commands.test.error.test_instance_not_found": "Testinstans’ blokkenhet kunne ikke finnes", "commands.test.error.test_instance_not_found.position": "Testinstans’ blokkenhet kunne ikke finnes i test på %s, %s, %s", "commands.test.error.too_large": "Strukturst<PERSON><PERSON>sen må være mindre enn %s blokker langs hver akse", "commands.test.locate.done": "Leting fullført, fant %s struktur(er)", "commands.test.locate.found": "Fant struktur på: %s (avstand: %s)", "commands.test.locate.started": "Startet leting etter <PERSON>turer, dette kan ta en stund...", "commands.test.no_tests": "Ingen tester å kjøre", "commands.test.relative_position": "Posisjon i forhold til %s: %s", "commands.test.reset.error.no_tests": "<PERSON>nne ikke finne noen tester å tilbakestille", "commands.test.reset.success": "Tilbakestilte %s struktur(er)", "commands.test.run.no_tests": "Ingen tester funnet", "commands.test.run.running": "<PERSON><PERSON><PERSON><PERSON> %s test(er)...", "commands.test.summary": "Spilltest fullført! %s test(er) ble kjørt", "commands.test.summary.all_required_passed": "<PERSON>e påkrevde tester best<PERSON><PERSON> :)", "commands.test.summary.failed": "%s påkrevd(e) test(er) mislyktes :(", "commands.test.summary.optional_failed": "%s valgfri(e) test(er) mislyktes", "commands.tick.query.percentiles": "Prosentiler: P50: %sms P95: %sms P99: %sms, test: %s", "commands.tick.query.rate.running": "Tikkratemål: %s per sekund.\nGjennomsnittstid per tikk: %sms (mål: %sms)", "commands.tick.query.rate.sprinting": "Tikkratemål: %s per sekund (ubrukt, kun til opplysning).\nGjennomsnittstid per tikk: %sms", "commands.tick.rate.success": "Satte tikkratemålet til %s per sekund", "commands.tick.sprint.report": "Sprint fullført med %s tikk per sekund, eller %sms per tikk", "commands.tick.sprint.stop.fail": "Ingen tikksprint pågår", "commands.tick.sprint.stop.success": "Avbrøt tilfellets tikksprint", "commands.tick.status.frozen": "<PERSON><PERSON><PERSON><PERSON> er fryst", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON>, men klarer ikke holde tikkratemå<PERSON>", "commands.tick.status.running": "<PERSON><PERSON><PERSON><PERSON> går som normalt", "commands.tick.status.sprinting": "<PERSON><PERSON><PERSON>t spurter", "commands.tick.step.fail": "<PERSON><PERSON><PERSON>t kunne ikke ta et steg - spillet må fryses først", "commands.tick.step.stop.fail": "Intet tikksteg pågår", "commands.tick.step.stop.success": "Avbrøt tilfellets tikksteg", "commands.tick.step.success": "Stiger %s tikk", "commands.time.query": "Tiden er %s", "commands.time.set": "Setter tiden til %s", "commands.title.cleared.multiple": "Fjernet titlene til %s spillere", "commands.title.cleared.single": "Fjernet titlene til %s", "commands.title.reset.multiple": "Tilbakestilte tittel-innstillingene til %s spillere", "commands.title.reset.single": "Tilbakestilte tittel-innstillingene til %s", "commands.title.show.actionbar.multiple": "Viser ny handlingsfelt-tittel for %s spillere", "commands.title.show.actionbar.single": "Viser ny handlingsfelt-tittel for %s", "commands.title.show.subtitle.multiple": "Viser ny undertittel for %s spillere", "commands.title.show.subtitle.single": "Viser ny undertittel for %s", "commands.title.show.title.multiple": "Viser ny tittel for %s spillere", "commands.title.show.title.single": "Viser ny tittel for %s", "commands.title.times.multiple": "<PERSON><PERSON> titte<PERSON> for %s spillere", "commands.title.times.single": "<PERSON><PERSON> titte<PERSON> for %s", "commands.transfer.error.no_players": "<PERSON><PERSON> peke ut minst én spiller å overføre", "commands.transfer.success.multiple": "Overfører %s spiller(e) til %s:%s", "commands.transfer.success.single": "Overfører %s til %s:%s", "commands.trigger.add.success": "Utløste %s (lagt %s til verdi)", "commands.trigger.failed.invalid": "Du kan bare utløse mål av slaget \"trigger\"", "commands.trigger.failed.unprimed": "Du kan ikke utløse dette målet ennå", "commands.trigger.set.success": "Utløste %s (satt verdi til %s)", "commands.trigger.simple.success": "Utløste %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Info om serverversjon:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Ingen veipunkt i %s", "commands.waypoint.list.success": "%s veipunkt i %s: %s", "commands.waypoint.modify.color": "Veipunktsfarge er nå %s", "commands.waypoint.modify.color.reset": "Tilbakestilte veipunktsfarge", "commands.waypoint.modify.style": "Veipunktstil endret", "commands.weather.set.clear": "Satt været til klart", "commands.weather.set.rain": "<PERSON>tt været til regn", "commands.weather.set.thunder": "Satt være til regn og torden", "commands.whitelist.add.failed": "Spiller er allerede på hvitelisten", "commands.whitelist.add.success": "%s ble lagt til i hvitelisten", "commands.whitelist.alreadyOff": "Hviteliste er allerede deaktivert", "commands.whitelist.alreadyOn": "Hviteliste er allerede aktivert", "commands.whitelist.disabled": "Hviteliste er nå deaktivert", "commands.whitelist.enabled": "Hviteliste er nå aktivert", "commands.whitelist.list": "Det er %s spiller(e) hvitelistet: %s", "commands.whitelist.none": "Det er ingen hvitelistede spillere", "commands.whitelist.reloaded": "Hvitelisten ble oppdatert", "commands.whitelist.remove.failed": "Spiller er ikke på hvitelisten", "commands.whitelist.remove.success": "%s ble fjernet fra hvitelisten", "commands.worldborder.center.failed": "Ingenting endret. Verdensbarrieren er allerede sentrert der", "commands.worldborder.center.success": "Satt sentrum av verdensbarrieren til %s, %s", "commands.worldborder.damage.amount.failed": "Ingenting endret. Verdensbarriere-skade har allerede den mengden", "commands.worldborder.damage.amount.success": "Stilte verdensbarrierens skade per blokk per sekund til %s", "commands.worldborder.damage.buffer.failed": "Ingenting endret. B<PERSON>eren for verdensgrenseskade har allerede denne avstanden", "commands.worldborder.damage.buffer.success": "Satte verdensbarrierens skadebuffer til %s blokk(er)", "commands.worldborder.get": "Verdensbarrieren er for øyeblikket %s blokk(er) vid", "commands.worldborder.set.failed.big": "Verdensbarrieren kan ikke være bredere enn %s blokker", "commands.worldborder.set.failed.far": "Verdensbarrierens sentrum kan ikke være lengre enn %s blokker fra verdenens sentrum", "commands.worldborder.set.failed.nochange": "Ingenting endret. Verdensbarrieren har allerede den størrelsen", "commands.worldborder.set.failed.small": "Verdensbarrieren kan ikke være mindre enn 1 blokk bred", "commands.worldborder.set.grow": "Forstørrer verdensbarrieren til %s blokker vid over %s sekund", "commands.worldborder.set.immediate": "Satte verdensbarrieren til %s blokk(er) vid", "commands.worldborder.set.shrink": "Minsker verdensbarrieren til %s blokk(er) vid på %s sekund(er)", "commands.worldborder.warning.distance.failed": "Ingenting endret. Verdensbarriere-advarselen har allerede den avstanden", "commands.worldborder.warning.distance.success": "Satte verdensbarrierens advarselsavstand til %s blokk(er)", "commands.worldborder.warning.time.failed": "Ingenting endret. Verdensbarriere-advarselen har allerede den tidsmengden", "commands.worldborder.warning.time.success": "Satte verdensbarrierens advarselstid til %s sekund(er)", "compliance.playtime.greaterThan24Hours": "Du har spilt i mer enn 24 timer", "compliance.playtime.hours": "Du har spilt i %s time(r)", "compliance.playtime.message": "Overdreven spilling kan gå ut over hverdagen", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Logger inn...", "connect.connecting": "<PERSON><PERSON> til serveren...", "connect.encrypting": "<PERSON><PERSON><PERSON><PERSON>...", "connect.failed": "<PERSON>nne ikke koble til serveren", "connect.failed.transfer": "T<PERSON><PERSON><PERSON> ble brutt under overføring til serveren", "connect.joining": "Går inn i verden...", "connect.negotiating": "Forhandler...", "connect.reconfiging": "Omkonfigurerer...", "connect.reconfiguring": "Omkonfigurerer...", "connect.transferring": "Overfører til ny server...", "container.barrel": "<PERSON><PERSON><PERSON>", "container.beacon": "Varde", "container.beehive.bees": "Bier: %s / %s", "container.beehive.honey": "Honning: %s / %s", "container.blast_furnace": "Masovn", "container.brewing": "Bryggestativ", "container.cartography_table": "Karttegningsbenk", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON> kiste", "container.crafter": "Tilvirker", "container.crafting": "Håndverk", "container.creative": "Gjenstand<PERSON><PERSON>val<PERSON>", "container.dispenser": "<PERSON><PERSON><PERSON>", "container.dropper": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant": "Fortryll", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s <PERSON><PERSON>teiner", "container.enchant.lapis.one": "1 <PERSON><PERSON><PERSON>", "container.enchant.level.many": "%s fortryllelsesnivåer", "container.enchant.level.one": "1 fortryllelsesnivå", "container.enchant.level.requirement": "Nivåkrav: %s", "container.enderchest": "Enderkis<PERSON>", "container.furnace": "Ovn", "container.grindstone_title": "Reparer & avfortryll", "container.hopper": "Gjenstandstrakt", "container.inventory": "Inventar", "container.isLocked": "%s er låst!", "container.lectern": "<PERSON><PERSON>", "container.loom": "Vevstol", "container.repair": "Reparer & navngi", "container.repair.cost": "Fortryllelseskostnad: %1$s", "container.repair.expensive": "For dyrt!", "container.shulkerBox": "Shulkerboks", "container.shulkerBox.itemCount": "%s ×%s", "container.shulkerBox.more": "og %s til...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Røykovn", "container.spectatorCantOpen": "Kan ikke å<PERSON>. Utbytte ikke enda generert.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Oppgrader utstyr", "container.upgrade.error_tooltip": "Gjenstanden kan ikke oppgraderes slik", "container.upgrade.missing_template_tooltip": "Legg til smimal", "controls.keybinds": "Tastebindinger...", "controls.keybinds.duplicateKeybinds": "<PERSON>ne tasten brukes også til:\n%s", "controls.keybinds.title": "Tastebindinger", "controls.reset": "Tilbakestill", "controls.resetAll": "Tilbakestill taster", "controls.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Vennligst velg et markslag", "createWorld.customize.buffet.title": "Tilpasning av enkelt markslag", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bunn - %s", "createWorld.customize.flat.layer.top": "Topp - %s", "createWorld.customize.flat.removeLayer": "Fjern lag", "createWorld.customize.flat.tile": "Lagmateriale", "createWorld.customize.flat.title": "Tilpasning av superflat", "createWorld.customize.presets": "Forhåndsinnstillinger", "createWorld.customize.presets.list": "For ø<PERSON><PERSON>: her er noen vi laget tidligere!", "createWorld.customize.presets.select": "Bruk forhåndsinnstilling", "createWorld.customize.presets.share": "Vil du dele din forhåndsinnstilling med noen? Bruk boksen under!", "createWorld.customize.presets.title": "Velg en forhåndsinnstilling", "createWorld.preparing": "Forbereder opprettelse av verden...", "createWorld.tab.game.title": "Spill", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Verden", "credits_and_attribution.button.attribution": "Attribusjon", "credits_and_attribution.button.credits": "Medvirkende", "credits_and_attribution.button.licenses": "Lisenser", "credits_and_attribution.screen.title": "Medvirkende og attribusjon", "dataPack.bundle.description": "Legger til den eksperimentelle bunt-gjenstanden", "dataPack.bundle.name": "<PERSON>unter", "dataPack.locator_bar.description": "Vis retningen til andre spillere i flerspillermodus", "dataPack.locator_bar.name": "Retningsviser", "dataPack.minecart_improvements.description": "Forbedrede gruvevognsbevegelser", "dataPack.minecart_improvements.name": "Gruvevognsforbedringer", "dataPack.redstone_experiments.description": "Eksperimentelle redstone-endringer", "dataPack.redstone_experiments.name": "Redstone-eksperiment", "dataPack.title": "Velg datapakker", "dataPack.trade_rebalance.description": "Oppdaterte handler for bygdinger", "dataPack.trade_rebalance.name": "Ombalanserte bygdinghandler", "dataPack.update_1_20.description": "Nye virkemåter og nytt innhold i Minecraft 1.20", "dataPack.update_1_20.name": "Oppdatering 1.20", "dataPack.update_1_21.description": "Nye virkemåter og nytt innhold i Minecraft 1.21", "dataPack.update_1_21.name": "Oppdatering 1.21", "dataPack.validation.back": "<PERSON><PERSON> til<PERSON>", "dataPack.validation.failed": "Validering av datapakke feilet!", "dataPack.validation.reset": "Tilbakestill til standard", "dataPack.validation.working": "Validerer valgte datapakker...", "dataPack.vanilla.description": "Minecrafts medfølgende data", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "Nye virkemåter og nytt innhold for vinterslippet", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "Sikker-modus", "datapackFailure.safeMode.failed.description": "Denne verdenen inneholder ugyldig eller korrupt lagringsdata.", "datapackFailure.safeMode.failed.title": "Lyktes ikke med å laste inn verden i sikker modus.", "datapackFailure.title": "Feil i nåvalgte datapakker forhindret verdenen fra å laste inn.\nDu kan enten prøve å laste inn verdenen med kun Vanilla-datapakken (\"trygg modus\"), eller gå tilbake til hovedmenyen og fikse det manuelt.", "death.attack.anvil": "%1$s ble most av en fallende ambolt", "death.attack.anvil.player": "%1$s ble most av en fallende ambolt i kamp mot %2$s", "death.attack.arrow": "%1$s ble skutt av %2$s", "death.attack.arrow.item": "%1$s ble skutt av %2$s væpnet med %3$s", "death.attack.badRespawnPoint.link": "Tiltenkt spilldesign", "death.attack.badRespawnPoint.message": "%1$s ble drept av %2$s", "death.attack.cactus": "%1$s ble prikket i hjel", "death.attack.cactus.player": "%1$s vandret inn i en kaktus under flukt fra %2$s", "death.attack.cramming": "%1$s ble skvist for mye", "death.attack.cramming.player": "%1$s ble most av %2$s", "death.attack.dragonBreath": "%1$s ble ristet i dragens ånde", "death.attack.dragonBreath.player": "%1$s ble ristet i dragens ånde av %2$s", "death.attack.drown": "%1$s druknet", "death.attack.drown.player": "%1$s druknet under flukt fra %2$s", "death.attack.dryout": "%1$s tørstet i hjel", "death.attack.dryout.player": "%1$s tørstet i hjel under flukt fra %2$s", "death.attack.even_more_magic": "%1$s ble drept av enda mer magi", "death.attack.explosion": "%1$s sprang i luften", "death.attack.explosion.player": "%1$s ble sprengt i luften av %2$s", "death.attack.explosion.player.item": "%1$s ble sprengt i luften av %2$s væpnet med %3$s", "death.attack.fall": "%1$s traff bakken for hardt", "death.attack.fall.player": "%1$s traff bakken for hardt under flukt fra %2$s", "death.attack.fallingBlock": "%1$s ble most av en fallende blokk", "death.attack.fallingBlock.player": "%1$s ble most av en fallende blokk i kamp mot %2$s", "death.attack.fallingStalactite": "%1$s ble spiddet av en fallende stalaktitt", "death.attack.fallingStalactite.player": "%1$s ble spiddet av en fallende stalaktitt i kamp mot %2$s", "death.attack.fireball": "%1$s ble truffet av %2$ss ildkule", "death.attack.fireball.item": "%1$s ble truffet av %2$ss ildkule med %3$s", "death.attack.fireworks": "%1$s gikk opp i røyk", "death.attack.fireworks.item": "%1$s gikk av med et smell fra et fyrverkeri som ble skutt fra %3$s av %2$s", "death.attack.fireworks.player": "%1$s gikk av med et smell i kamp mot %2$s", "death.attack.flyIntoWall": "%1$s opplevde kinetisk energi", "death.attack.flyIntoWall.player": "%1$s kjente på kinetisk energi i forsøk på flukt fra %2$s", "death.attack.freeze": "%1$s frøs i hjel", "death.attack.freeze.player": "%1$s ble frosset i hjel av %2$s", "death.attack.generic": "%1$s døde", "death.attack.generic.player": "%1$s døde på grunn av %2$s", "death.attack.genericKill": "%1$s ble drept", "death.attack.genericKill.player": "%1$s ble drept i kamp mot %2$s", "death.attack.hotFloor": "%1$s oppdaget at gulvet var lava", "death.attack.hotFloor.player": "%1$s steg inn i faresonen grunnet %2$s", "death.attack.inFire": "%1$s ble brent levende", "death.attack.inFire.player": "%1$s vandret inn i flammer i kamp mot %2$s", "death.attack.inWall": "%1$s ble kvalt i en vegg", "death.attack.inWall.player": "%1$s ble kvalt i en vegg i kamp mot %2$s", "death.attack.indirectMagic": "%1$s ble drept av %2$s med magi", "death.attack.indirectMagic.item": "%1$s ble drept av %2$s med %3$s", "death.attack.lava": "%1$s forsøkte å svømme i lava", "death.attack.lava.player": "%1$s forsøkte å svømme i lava for å rømme fra %2$s", "death.attack.lightningBolt": "%1$s ble truffet av lynet", "death.attack.lightningBolt.player": "%1$s ble truffet av lynet i kamp mot %2$s", "death.attack.mace_smash": "%1$s ble most av %2$s", "death.attack.mace_smash.item": "%1$s ble most av %2$s væpnet med %3$s", "death.attack.magic": "%1$s ble drept av magi", "death.attack.magic.player": "%1$s ble drept av magi under flukt fra %2$s", "death.attack.message_too_long": "Faktisk var meldingen for lang til å sendes helhetlig. Beklager! Her er et forkortet utdrag: %s", "death.attack.mob": "%1$s ble drept av %2$s", "death.attack.mob.item": "%1$s ble drept av %2$s med %3$s", "death.attack.onFire": "%1$s brant i hjel", "death.attack.onFire.item": "%1$s ble sprøstekt i kamp mot %2$s væpnet med %3$s", "death.attack.onFire.player": "%1$s ble sprøstekt i kamp mot %2$s", "death.attack.outOfWorld": "%1$s falt ut av verden", "death.attack.outOfWorld.player": "%1$s ville ikke leve i samme verden som %2$s", "death.attack.outsideBorder": "%1$s forlot denne verdenens grenser", "death.attack.outsideBorder.player": "%1$s forlot denne verdenens rammer i kamp mot %2$s", "death.attack.player": "%1$s fl<PERSON>ddes av %2$s", "death.attack.player.item": "%1$s flåddes av %2$s rustet med %3$s", "death.attack.sonic_boom": "%1$s ble utslettet av et sonisk ladet skrik", "death.attack.sonic_boom.item": "%1$s ble utslettet av et drønnladet skrik under flukt fra %2$s væpnet med %3$s", "death.attack.sonic_boom.player": "%1$s ble utslettet av et drønnladet skrik under flukt fra %2$s", "death.attack.stalagmite": "%1$s ble spiddet på en stalagmitt", "death.attack.stalagmite.player": "%1$s ble spiddet på en stalagmitt i kamp mot %2$s", "death.attack.starve": "%1$s sultet i hjel", "death.attack.starve.player": "%1$s sultet i hjel i kamp mot %2$s", "death.attack.sting": "%1$s ble stukket i hjel", "death.attack.sting.item": "%1$s ble stukket i hjel av %2$s væpnet med %3$s", "death.attack.sting.player": "%1$s ble stukket i hjel av %2$s", "death.attack.sweetBerryBush": "%1$s ble prikket i hjel av en søtbærbusk", "death.attack.sweetBerryBush.player": "%1$s ble prikket i hjel av en søtbærbusk under flukt fra %2$s", "death.attack.thorns": "%1$s ble drept under forsøk på å skade %2$s", "death.attack.thorns.item": "%1$s ble drept av %3$s i forsøk på å skade %2$s", "death.attack.thrown": "%1$s ble kastet på av %2$s", "death.attack.thrown.item": "%1$s ble kastet på av %2$s med %3$s", "death.attack.trident": "%1$s ble spiddet av %2$s", "death.attack.trident.item": "%1$s ble spiddet av %2$s med %3$s", "death.attack.wither": "%1$s visnet vekk", "death.attack.wither.player": "%1$s visnet bort i kamp mot %2$s", "death.attack.witherSkull": "%1$s ble skutt med en skalle av %2$s", "death.attack.witherSkull.item": "%1$s ble skutt med en skalle av %2$s med %3$s", "death.fell.accident.generic": "%1$s falt fra et høyt sted", "death.fell.accident.ladder": "%1$s falt av en stige", "death.fell.accident.other_climbable": "%1$s mistet grepet og falt ned", "death.fell.accident.scaffolding": "%1$s falt ned fra et stillas", "death.fell.accident.twisting_vines": "%1$s glapp taket i en vridvin", "death.fell.accident.vines": "%1$s glapp taket i en ranke", "death.fell.accident.weeping_vines": "%1$s glapp taket i en slengvin", "death.fell.assist": "%1$s ble dømt til å falle av %2$s", "death.fell.assist.item": "%1$s ble dømt til å falle av %2$s med %3$s", "death.fell.finish": "%1$s falt for langt og ble drept av %2$s", "death.fell.finish.item": "%1$s falt for langt og ble drept av %2$s med %3$s", "death.fell.killer": "%1$s ble dømt til å falle", "deathScreen.quit.confirm": "Er du sikker på at du vil avslutte?", "deathScreen.respawn": "Gjennoppstå", "deathScreen.score": "Skår", "deathScreen.score.value": "Skår: %s", "deathScreen.spectate": "Betrakt verden", "deathScreen.title": "Du døde!", "deathScreen.title.hardcore": "S<PERSON><PERSON>t er over!", "deathScreen.titleScreen": "Hovedmeny", "debug.advanced_tooltips.help": "F3 + H = Avanserte verktøytips", "debug.advanced_tooltips.off": "Avanserte verktøytips: skjult", "debug.advanced_tooltips.on": "Avanserte verktøytips: synlig", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON>", "debug.chunk_boundaries.off": "Stykkekanter: skjulte", "debug.chunk_boundaries.on": "Stykkekanter: viste", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON> nettpraten", "debug.copy_location.help": "F3 + C = <PERSON><PERSON><PERSON> plassering som /tp-kommando, hold F3 + C for å krasje spillet", "debug.copy_location.message": "Kopierte plasseringen til utklippstavlen", "debug.crash.message": "F3 + <PERSON> holdes nede. <PERSON>te vil krasje spillet dersom de ikke slippes.", "debug.crash.warning": "Krasjer om %s...", "debug.creative_spectator.error": "Kan ikke bytte spillmodus; ingen tillatelse", "debug.creative_spectator.help": "F3 + N = Veksl mellom forrige spillmodus <-> tilskuer", "debug.dump_dynamic_textures": "Lagret dynamiske teksturer i %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamiske teksturer", "debug.gamemodes.error": "Kan ikke å<PERSON>ler, ikke till<PERSON>e", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON><PERSON>dus<PERSON>ksler", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s neste", "debug.help.help": "F3 + Q = Vis denne listen", "debug.help.message": "Tastebindinger:", "debug.inspect.client.block": "Ko<PERSON>rt blokkdata fra klienten til utklippstavle", "debug.inspect.client.entity": "Kopiert enhetsdata fra klienten til utklippstavle", "debug.inspect.help": "F3 + I = <PERSON><PERSON><PERSON> enhe<PERSON>- <PERSON><PERSON> blokkdata til utklippstavle", "debug.inspect.server.block": "Kopiert blokkdata fra server til utklippstavle", "debug.inspect.server.entity": "Kopiert enhetsdata fra server til utklippstavle", "debug.pause.help": "F3 + Esc = <PERSON>use uten pausemeny (om pausing er mulig)", "debug.pause_focus.help": "F3 + P = Pause ved mistet fokus", "debug.pause_focus.off": "Pause ved mistet fokus: deaktivert", "debug.pause_focus.on": "Pause ved mistet fokus: aktivert", "debug.prefix": "[<PERSON><PERSON><PERSON><PERSON>]:", "debug.profiling.help": "F3 + L = Start/stopp profilering", "debug.profiling.start": "Startet %s sekunders profilering. Bruk F3 + L for å stoppe tidlig", "debug.profiling.stop": "Profilering stoppet. Skrev utfallet til %s", "debug.reload_chunks.help": "F3 + A = Last inn alle stykker på nytt", "debug.reload_chunks.message": "Laster inn alle stykker på nytt", "debug.reload_resourcepacks.help": "F3 + T = Last ressurspakker på nytt", "debug.reload_resourcepacks.message": "Ressurspakker innlastet på nytt", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON> treff<PERSON>er", "debug.show_hitboxes.off": "Treffbokser: sk<PERSON><PERSON>", "debug.show_hitboxes.on": "Treffbokser: vist", "debug.version.header": "Info om klientversjon:", "debug.version.help": "F3 + V = Info om klientversjon", "demo.day.1": "Denne prøveversjonen vil vare i fem spilldager. <PERSON><PERSON><PERSON><PERSON> ditt beste!", "demo.day.2": "Dag to", "demo.day.3": "Dag tre", "demo.day.4": "Dag fire", "demo.day.5": "Dette er din siste dag!", "demo.day.6": "Den femte dagen er over. Trykk på %s for å lagre et skjermbilde av byggverket ditt.", "demo.day.warning": "<PERSON><PERSON> din løper snart ut!", "demo.demoExpired": "Prøveperioden er over!", "demo.help.buy": "<PERSON><PERSON><PERSON><PERSON> nå!", "demo.help.fullWrapped": "Denne prøveversjonen vil vare i 5 spilldager (ca. 1 time og 40 minutter). Sjekk fremskrittene for tips! Ha det gøy!", "demo.help.inventory": "Bruk %1$s for å åpne inventaret ditt", "demo.help.jump": "Hopp ved å trykke på %1$s", "demo.help.later": "Fortsett å spille!", "demo.help.movement": "Bruk %1$s, %2$s, %3$s, %4$s og datamusen for å bevege deg", "demo.help.movementMouse": "Se deg rundt med musen", "demo.help.movementShort": "Beveg deg rundt rundt med %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft prøveversjon", "demo.remainingTime": "Gjenstående tid: %s", "demo.reminder": "<PERSON><PERSON><PERSON><PERSON><PERSON> er over. <PERSON><PERSON><PERSON><PERSON> spillet for å fortsette, eller lag en ny verden!", "difficulty.lock.question": "Er du sikker på at du vil låse vanskelighetsgraden på denne verdenen? Dette låser verdenen til %1$s, og du vil ikke kunne endre det igjen.", "difficulty.lock.title": "<PERSON><PERSON><PERSON>", "disconnect.endOfStream": "Streamen ble avsluttet", "disconnect.exceeded_packet_rate": "Ka<PERSON>t ut for overskridelse av pakkerategrense", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorerer statusforespørsel", "disconnect.loginFailedInfo": "Innlogging mislykket: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Flerspiller er deaktivert. Vennligst sjekk innstillingene for Microsoft-kontoen din.", "disconnect.loginFailedInfo.invalidSession": "Ugyldig økt (prøv å starte spillet og oppstartsprogrammet)", "disconnect.loginFailedInfo.serversUnavailable": "Autentiseringsserverne kunne ikke nås. Vennligst prøv igjen.", "disconnect.loginFailedInfo.userBanned": "Du er bannlyst fra nettspill", "disconnect.lost": "<PERSON><PERSON><PERSON>", "disconnect.packetError": "Netverksprotokollfeil", "disconnect.spam": "<PERSON><PERSON><PERSON> ut for spamming", "disconnect.timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnect.transfer": "Overført til annen server", "disconnect.unknownHost": "<PERSON><PERSON><PERSON><PERSON> vert", "download.pack.failed": "%s av %s pakke(r) kunne ikke lastes ned", "download.pack.progress.bytes": "Fremgang: %s (total størrelse ukjent)", "download.pack.progress.percent": "Fremgang: %s%%", "download.pack.title": "Laster ned ressurspakke %s/%s", "editGamerule.default": "Standard: %s", "editGamerule.title": "<PERSON><PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorbering", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON><PERSON> jæ<PERSON>", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Fløderkraft", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Delfinens velsignelse", "effect.minecraft.fire_resistance": "Flammemotstand", "effect.minecraft.glowing": "Glødende", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Helseø<PERSON>ning", "effect.minecraft.hero_of_the_village": "Landsbyhelt", "effect.minecraft.hunger": "Sult", "effect.minecraft.infested": "Befengt", "effect.minecraft.instant_damage": "Øyeblikkelig skade", "effect.minecraft.instant_health": "Øyeblikkelig helse", "effect.minecraft.invisibility": "Usynlighet", "effect.minecraft.jump_boost": "Hop<PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "Levitasjon", "effect.minecraft.luck": "<PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Utmattelse", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON>", "effect.minecraft.poison": "Gift", "effect.minecraft.raid_omen": "Herjingsjærtegn", "effect.minecraft.regeneration": "Regenerasjon", "effect.minecraft.resistance": "Motstand", "effect.minecraft.saturation": "Metning", "effect.minecraft.slow_falling": "Fjærfall", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.speed": "Fart", "effect.minecraft.strength": "Styrke", "effect.minecraft.trial_omen": "Prøvelsesjærtegn", "effect.minecraft.unluck": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "Svakhet", "effect.minecraft.weaving": "Vevende", "effect.minecraft.wind_charged": "Vindladet", "effect.minecraft.wither": "V<PERSON><PERSON>", "effect.none": "Ingen effekter", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "Leddyrets skrekk", "enchantment.minecraft.binding_curse": "Bindelsesforbannelse", "enchantment.minecraft.blast_protection": "Eksplosjonsbeskyttelse", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Tetthet", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Effektivitet", "enchantment.minecraft.feather_falling": "Fjærfall", "enchantment.minecraft.fire_aspect": "Ildsverd", "enchantment.minecraft.fire_protection": "Brannvern", "enchantment.minecraft.flame": "<PERSON>lamme", "enchantment.minecraft.fortune": "<PERSON><PERSON>", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Tilbakeslag", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "Trofast", "enchantment.minecraft.luck_of_the_sea": "Havets hell", "enchantment.minecraft.lure": "Agn", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Flerskudd", "enchantment.minecraft.piercing": "Spiddende", "enchantment.minecraft.power": "Kraft", "enchantment.minecraft.projectile_protection": "Prosjektilbeskyttelse", "enchantment.minecraft.protection": "Beskyttelse", "enchantment.minecraft.punch": "Trykk", "enchantment.minecraft.quick_charge": "Rask ladning", "enchantment.minecraft.respiration": "Respirasjon", "enchantment.minecraft.riptide": "Tidevannstrøm", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Silkeberøring", "enchantment.minecraft.smite": "<PERSON> skrekk", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON><PERSON><PERSON> knivsegg", "enchantment.minecraft.sweeping_edge": "Sveipende sverdegg", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Torner", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Forsvinnelsesforbannelse", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_chest_boat": "Akasiebåt med kiste", "entity.minecraft.allay": "H<PERSON>lpeånd", "entity.minecraft.area_effect_cloud": "Områ<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armadillo": "Beltedyr", "entity.minecraft.armor_stand": "Rustningsstativ", "entity.minecraft.arrow": "<PERSON>l", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambusflåte med kiste", "entity.minecraft.bamboo_raft": "Bambusflåte", "entity.minecraft.bat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "Bjørkebåt med kiste", "entity.minecraft.blaze": "Flammeskrømt", "entity.minecraft.block_display": "Utstilt blokk", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "Sumprangel", "entity.minecraft.breeze": "Vindskrømt", "entity.minecraft.breeze_wind_charge": "Vindladning", "entity.minecraft.camel": "Dr<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Huleedderkopp", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "Kirsebærtrebåt med kiste", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON> med kiste", "entity.minecraft.chest_minecart": "Gruvevogn med kiste", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Torsk", "entity.minecraft.command_block_minecart": "Gruvevogn med kommandoblokk", "entity.minecraft.cow": "Ku", "entity.minecraft.creaking": "Knirkning", "entity.minecraft.creaking_transient": "Knirkning", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Mørkeikeb<PERSON>t", "entity.minecraft.dark_oak_chest_boat": "Mørkeikebåt med kiste", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Drageildkule", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Kastet egg", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "Endkrystall", "entity.minecraft.ender_dragon": "Enderdrage", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> end<PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "Kastet trolldomsflaske", "entity.minecraft.experience_orb": "Erfaringskule", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "<PERSON>de blokk", "entity.minecraft.falling_block_type": "Fallende %s", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "Fyrverkeri", "entity.minecraft.fishing_bobber": "<PERSON><PERSON>", "entity.minecraft.fox": "Rev", "entity.minecraft.frog": "Frosk", "entity.minecraft.furnace_minecart": "Gruvevogn med ovn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Kjempe", "entity.minecraft.glow_item_frame": "Gløderamme", "entity.minecraft.glow_squid": "Glosprut", "entity.minecraft.goat": "Geit", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "Lykkelig ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Gruvevogn med trakt", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "Ørkenzombie", "entity.minecraft.illusioner": "Illusjon<PERSON>", "entity.minecraft.interaction": "<PERSON><PERSON><PERSON>", "entity.minecraft.iron_golem": "Jernkjempe", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Utstilt gjenstand", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "Jungeltrebåt med kiste", "entity.minecraft.killer_bunny": "Morderkaninen", "entity.minecraft.leash_knot": "Båndknute", "entity.minecraft.lightning_bolt": "Lynnedslag", "entity.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON> br<PERSON>gg", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmakube", "entity.minecraft.mangrove_boat": "Mangrovebåt", "entity.minecraft.mangrove_chest_boat": "Mangrovebåt med kiste", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Gruvevogn", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON>ld<PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON>b<PERSON>t med kiste", "entity.minecraft.ocelot": "Ozelot", "entity.minecraft.ominous_item_spawner": "Illevarslende gjenstandsfremkaller", "entity.minecraft.painting": "<PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Blekeikebåt", "entity.minecraft.pale_oak_chest_boat": "Blekeikebåt med kiste", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Papegøye", "entity.minecraft.phantom": "<PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "Kulefisk", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON>", "entity.minecraft.salmon": "Laks", "entity.minecraft.sheep": "Sau", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Sølvkre", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "Skjeletthest", "entity.minecraft.slime": "<PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON> il<PERSON>", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snowball": "Snøball", "entity.minecraft.spawner_minecart": "Gruvevogn med skapningsmaner", "entity.minecraft.spectral_arrow": "Spektralpil", "entity.minecraft.spider": "Edderkopp", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON> brygg", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "Granb<PERSON>t med kiste", "entity.minecraft.squid": "Blekksprut", "entity.minecraft.stray": "<PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "R<PERSON>et<PERSON>", "entity.minecraft.text_display": "Utstilt tekst", "entity.minecraft.tnt": "Tent TNT", "entity.minecraft.tnt_minecart": "Gruvevogn med TNT", "entity.minecraft.trader_llama": "Handelslama", "entity.minecraft.trident": "Trefork", "entity.minecraft.tropical_fish": "Sydhavsfisk", "entity.minecraft.tropical_fish.predefined.0": "Sjørosefisk", "entity.minecraft.tropical_fish.predefined.1": "Svart kirurg", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON> cornutus", "entity.minecraft.tropical_fish.predefined.11": "Prydet skjellfinnefisk", "entity.minecraft.tropical_fish.predefined.12": "Papegøyefisk", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON> ciliaris", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Rødlippet slimfisk", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON> snapper", "entity.minecraft.tropical_fish.predefined.17": "Polynemidæ", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Avtrekkerfisk", "entity.minecraft.tropical_fish.predefined.2": "Blå kirurg", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON><PERSON><PERSON> papegøyefisk", "entity.minecraft.tropical_fish.predefined.21": "Gul kirurg", "entity.minecraft.tropical_fish.predefined.3": "Skjellfinnefisk", "entity.minecraft.tropical_fish.predefined.4": "Ciklid", "entity.minecraft.tropical_fish.predefined.5": "Klovnefisk", "entity.minecraft.tropical_fish.predefined.6": "Blårosa kampfisk", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidæ", "entity.minecraft.tropical_fish.predefined.8": "Keisersnapper", "entity.minecraft.tropical_fish.predefined.9": "<PERSON>", "entity.minecraft.tropical_fish.type.betty": "Kampfisk", "entity.minecraft.tropical_fish.type.blockfish": "Blokkfisk", "entity.minecraft.tropical_fish.type.brinely": "Saltfisk", "entity.minecraft.tropical_fish.type.clayfish": "Leirefisk", "entity.minecraft.tropical_fish.type.dasher": "Strekfisk", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitterfisk", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snokefisk", "entity.minecraft.tropical_fish.type.spotty": "Flekkefisk", "entity.minecraft.tropical_fish.type.stripey": "Striping", "entity.minecraft.tropical_fish.type.sunstreak": "Solstripefisk", "entity.minecraft.turtle": "Skilpadde", "entity.minecraft.vex": "Plageånd", "entity.minecraft.villager": "Bygding", "entity.minecraft.villager.armorer": "Rustningssmed", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Pilmaker", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotekar", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Bygding", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.witch": "<PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Withersk<PERSON>lett", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "Ulv", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Zombiebygding", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON>", "entity.not_summonable": "Kan ikke fremkalle enhet av type %s", "event.minecraft.raid": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "Nederlag", "event.minecraft.raid.defeat.full": "Herjing - Nederlag", "event.minecraft.raid.raiders_remaining": "Plyndrere igjen: %s", "event.minecraft.raid.victory": "<PERSON><PERSON>", "event.minecraft.raid.victory.full": "Herjing - Se<PERSON>", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over jungel", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over sump", "filled_map.id": "Id #%s", "filled_map.level": "(Nivå %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON>", "filled_map.mansion": "Opp<PERSON>gelseskart over skogmark", "filled_map.monument": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over hav", "filled_map.scale": "Skala på 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over <PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.unknown": "Ukjent kart", "filled_map.village_desert": "<PERSON><PERSON><PERSON><PERSON> over <PERSON><PERSON><PERSON>", "filled_map.village_plains": "<PERSON><PERSON><PERSON><PERSON> over slette", "filled_map.village_savanna": "<PERSON><PERSON><PERSON><PERSON> over sa<PERSON><PERSON>", "filled_map.village_snowy": "<PERSON><PERSON><PERSON><PERSON> over snøstrøk", "filled_map.village_taiga": "<PERSON><PERSON><PERSON><PERSON> over taiga", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hull", "flat_world_preset.minecraft.classic_flat": "Klassisk flat", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Oververdenen", "flat_world_preset.minecraft.redstone_ready": "Klar til redstone", "flat_world_preset.minecraft.snowy_kingdom": "Snødekt kongerike", "flat_world_preset.minecraft.the_void": "Tomrommet", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON> drøm", "flat_world_preset.minecraft.water_world": "Vannverden", "flat_world_preset.unknown": "???", "gameMode.adventure": "Oppdagelsesmodus", "gameMode.changed": "Din spillmodus har blitt oppdatert til %s", "gameMode.creative": "Kreativ modus", "gameMode.hardcore": "Hardbarket-modus", "gameMode.spectator": "Tilskuermodus", "gameMode.survival": "Overlevelsesmodus", "gamerule.allowFireTicksAwayFromPlayer": "Tikk ild langt vekk fra spillere", "gamerule.allowFireTicksAwayFromPlayer.description": "Bestemmer om ild og lava skal kunne tikke lenger vekk enn 8 verdensstykker fra en spiller", "gamerule.announceAdvancements": "Kringkast fremskritt", "gamerule.blockExplosionDropDecay": "I blokkpåvirkningseksplosjoner yter ikke alltid blokker utbytte", "gamerule.blockExplosionDropDecay.description": "Noe av utbyttet fra blokker som ødelegges i eksplosjoner forårsaket av blokkpåvirkning går tapt i eksplosjonen.", "gamerule.category.chat": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.drops": "<PERSON><PERSON>", "gamerule.category.misc": "Diverse", "gamerule.category.mobs": "Skapninger", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Dannelse", "gamerule.category.updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.commandBlockOutput": "Kringkast kommandoblokkutfall", "gamerule.commandModificationBlockLimit": "Blokkgrense for kommandoer", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON><PERSON> antall blokker som kan endres på en gang av én kommando, som fill eller clone.", "gamerule.disableElytraMovementCheck": "Skru av gransking av elytraflyvning", "gamerule.disablePlayerMovementCheck": "Slå av spillerbevegelsesgransking", "gamerule.disableRaids": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON><PERSON> tid", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON>rs<PERSON>tte fra <PERSON>", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON> utbytte fra gru<PERSON> (med innhold), ram<PERSON>, b<PERSON><PERSON>, osv.", "gamerule.doFireTick": "Oppdater ild", "gamerule.doImmediateRespawn": "Gjennoppstå umiddelbart", "gamerule.doInsomnia": "<PERSON><PERSON>", "gamerule.doLimitedCrafting": "Krev oppskrift til verk", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON> <PERSON>, kan spillere kun tilvirke ting med opplåste oppskrifter.", "gamerule.doMobLoot": "Skapninger yter bytte", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> fra s<PERSON>, samt erfaringskuler.", "gamerule.doMobSpawning": "<PERSON><PERSON>", "gamerule.doMobSpawning.description": "Visse eninger kan ha særskilte betingelser.", "gamerule.doPatrolSpawning": "<PERSON><PERSON> p<PERSON>", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON><PERSON> fra blokker", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON><PERSON> fra blo<PERSON>, samt erfaringskuler.", "gamerule.doTraderSpawning": "<PERSON><PERSON>", "gamerule.doVinesSpread": "Rankespredning", "gamerule.doVinesSpread.description": "Avgj<PERSON>r om ranker sprer seg vilkårlig til naboblokker. Påvirker ikke andre typer ranker som slengvin, vridvin, osv.", "gamerule.doWardenSpawning": "<PERSON><PERSON>", "gamerule.doWeatherCycle": "Oppdater været", "gamerule.drowningDamage": "G<PERSON><PERSON>r drukningsska<PERSON>", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> forsvinner ved død", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON> end<PERSON><PERSON>ler kastet av en spiller forsvinner når spilleren dør.", "gamerule.entitiesWithPassengersCanUsePortals": "Enheter med passasjerer kan bruke portaler", "gamerule.entitiesWithPassengersCanUsePortals.description": "Tillat enheter med passasjerer å teleportere gjennom netherportaler, endeportaler, og endporter.", "gamerule.fallDamage": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON> døde <PERSON>", "gamerule.forgiveDeadPlayers.description": "Sinte nøytrale skapninger slutter å være sinte når spilleren de jager dør i nærheten.", "gamerule.freezeDamage": "Utfør frost<PERSON>", "gamerule.globalSoundEvents": "<PERSON><PERSON>", "gamerule.globalSoundEvents.description": "<PERSON><PERSON>en fra visse <PERSON>, som når en boss op<PERSON><PERSON><PERSON><PERSON>, kan hø<PERSON> overalt.", "gamerule.keepInventory": "<PERSON><PERSON> inventar etter død", "gamerule.lavaSourceConversion": "<PERSON><PERSON> danner kilder", "gamerule.lavaSourceConversion.description": "Når rennende lava omgis av lavakilder på to sider gjøres lavaen om til en kilde.", "gamerule.locatorBar": "Slå på spillerretningsviser", "gamerule.locatorBar.description": "Når påslått vises en list på skjermen som opplyser om retningen til andre spillere.", "gamerule.logAdminCommands": "Kringkast administrasjonskommandoer", "gamerule.maxCommandChainLength": "Høyeste lengde til kommandokjeder", "gamerule.maxCommandChainLength.description": "G<PERSON>lder kommandoblokkjeder og funksjoner.", "gamerule.maxCommandForkCount": "Kommandokontekstgrense", "gamerule.maxCommandForkCount.description": "Høyest antall kontekster som kan brukes av kommandoer som 'execute as'.", "gamerule.maxEntityCramming": "Terskel for trengsel av eninger", "gamerule.minecartMaxSpeed": "Høyest fart for gruvevogner", "gamerule.minecartMaxSpeed.description": "Høyeste mulig fart til en gruvevogn i bevegelse på land.", "gamerule.mobExplosionDropDecay": "I skapningseksplosjoner yter ikke alltid blokker utbytte", "gamerule.mobExplosionDropDecay.description": "Noe av utbyttet fra blokker som ødelegges i eksplosjoner forårsaket av skapninger går tapt i eksplosjonen.", "gamerule.mobGriefing": "Tillat skapninger å ødelegge", "gamerule.naturalRegeneration": "Gjenoppfyll liv", "gamerule.playersNetherPortalCreativeDelay": "Spillerens forsinkelse i netherportaler i kreativ modus", "gamerule.playersNetherPortalCreativeDelay.description": "Tid (i tikk) en spiller i kreativmodus må stå i en netherportal før spilleren endrer dimensjoner.", "gamerule.playersNetherPortalDefaultDelay": "Spillerens forsinkelse i netherportaler utenom kreativ modus", "gamerule.playersNetherPortalDefaultDelay.description": "Tid (i tikk) en spiller utom kreativmodus må stå i en netherportal før spilleren endrer dimensjoner.", "gamerule.playersSleepingPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.playersSleepingPercentage.description": "Prosentandelen av spillere som må sove for å hoppe over natten.", "gamerule.projectilesCanBreakBlocks": "Prosjektiler kan ødel<PERSON>ge blokker", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> om prosjektiler ødelegger blokker de kunne ødelagt.", "gamerule.randomTickSpeed": "Fartsrate til tilfeldige tikk", "gamerule.reducedDebugInfo": "Mindre feilsøkingsinformasjon", "gamerule.reducedDebugInfo.description": "Begrenser innholdet på feilsøkingsskjermen.", "gamerule.sendCommandFeedback": "Vis utfall til kommandoer", "gamerule.showDeathMessages": "<PERSON><PERSON>", "gamerule.snowAccumulationHeight": "Høyest antall snølag", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON><PERSON><PERSON> antall lag med snø som legger seg på bakken når det snør.", "gamerule.spawnChunkRadius": "Startpunktstykkeradius", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON> stykker som forblir lastet inn rundt Oververdenens startpunkt.", "gamerule.spawnRadius": "Startpunktsradius", "gamerule.spawnRadius.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tø<PERSON> til området rundt startpunktet som spillere kan starte i.", "gamerule.spectatorsGenerateChunks": "La tilskuere generere stykker", "gamerule.tntExplodes": "La TNT antennes og eksplodere", "gamerule.tntExplosionDropDecay": "I TNT-eksplosjoner yter ikke alltid blokker utbytte", "gamerule.tntExplosionDropDecay.description": "Noe av utbyttet fra blokker som ødelegges i eksplosjoner forårsaket av TNT går tapt i eksplosjonen.", "gamerule.universalAnger": "<PERSON>lt sinne", "gamerule.universalAnger.description": "Sinte nøytrale skapninger angriper alle spillerne i nærheten, ikke bare spilleren som gjorde dem sinte. Fungerer best om forgiveDeadPlayers er skrudd av.", "gamerule.waterSourceConversion": "<PERSON><PERSON> danner kilder", "gamerule.waterSourceConversion.description": "<PERSON><PERSON>r rennende vann omgis av vannkilder på to sider gjø<PERSON> vannet om til en kilde.", "generator.custom": "Tilpasset", "generator.customized": "Gammelt tilpasset", "generator.minecraft.amplified": "FORSTERKET", "generator.minecraft.amplified.info": "Merk: Bare for moro skyld! Krever en kraftig datamaskin.", "generator.minecraft.debug_all_block_states": "Feilsøkingsmodus", "generator.minecraft.flat": "Superflat", "generator.minecraft.large_biomes": "Store markslag", "generator.minecraft.normal": "<PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Enkelt markslag", "generator.single_biome_caves": "Grotter", "generator.single_biome_floating_islands": "Svevende ø<PERSON>", "gui.abuseReport.attestation": "Ved å sende inn denne rapporten bekrefter du at informasjonen du har oppgitt er riktig og fullstendig etter din beste evne.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Deling av detaljer hjelper oss med å gjøre en velbegrunnet beslutning.", "gui.abuseReport.discard.content": "Om du går ut forkaster du denne rapporten og kommentarene dine.\nEr du sikker på at du vil gå ut?", "gui.abuseReport.discard.discard": "Forlat og forkast rapport", "gui.abuseReport.discard.draft": "Behold som utkast", "gui.abuseReport.discard.return": "Fortsett på utkast", "gui.abuseReport.discard.title": "Forkast rapport og kommentarer?", "gui.abuseReport.draft.content": "Vil du fortsette på det lagrede utkastet eller forkaste det og lage et nytt?", "gui.abuseReport.draft.discard": "Forkast", "gui.abuseReport.draft.edit": "Fortsett på utkast", "gui.abuseReport.draft.quittotitle.content": "Vil du fortsette på utkastet eller forkaste det?", "gui.abuseReport.draft.quittotitle.title": "Du har et utkast som forkastes om du går ut", "gui.abuseReport.draft.title": "Fortsette på utkastet?", "gui.abuseReport.error.title": "Problem med å sende rapporten din", "gui.abuseReport.message": "Hvor la du merke til den dårlige oppførselen?\nDette hjelper oss med å undersøke saken din.", "gui.abuseReport.more_comments": "Vennligst forklar det som skjedde:", "gui.abuseReport.name.comment_box_label": "Vennligst forklar hvorfor du vil rapportere dette navnet:", "gui.abuseReport.name.reporting": "Du rapporterer \"%s\".", "gui.abuseReport.name.title": "Rap<PERSON><PERSON> upassende <PERSON>n", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON>r rapporterer du dette?", "gui.abuseReport.read_info": "Mer om rapportering", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkotika eller alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Noen oppfordrer andre til å delta i ulovlig narkotikavirksomhet eller oppfordrer mindreårige til å drikke alkohol.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Seksuell utnyttelse eller misbruk av barn", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> snakker om eller på annet vis fremmer uanstendigheter som involverer barn.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Ærekrenking", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON> skader ditt eller noen andres rykte, for eksempel ved å dele usannheter med hensikt til å utnytte eller villede andre.", "gui.abuseReport.reason.description": "Beskrivelse:", "gui.abuseReport.reason.false_reporting": "Rapporteringsmisbruk", "gui.abuseReport.reason.generic": "Jeg vil rapportere han/henne", "gui.abuseReport.reason.generic.description": "Han/hun irriterer meg / han/hun gjorde noe jeg misliker.", "gui.abuseReport.reason.harassment_or_bullying": "T<PERSON><PERSON><PERSON> eller mobbing", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, ang<PERSON>er eller mobber deg eller noen andre. <PERSON><PERSON> gjelder når noen gjentatte ganger prøver å kontakte deg eller noen andre uten samtykke eller legger ut privat personlig informasjon om deg eller noen andre uten samtykke (\"doxing\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "<PERSON>en angriper deg eller en annen spiller grunnet identitetstrekk som religion, etnisitet, eller seksuell legning.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON>el om skade av andre", "gui.abuseReport.reason.imminent_harm.description": "Noen truer med å skade deg eller noen andre i virkeligheten.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Intime avbildninger uten samtykke", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON> snakker om, <PERSON><PERSON>, eller på annet vis fremmer private og intime bilder.", "gui.abuseReport.reason.self_harm_or_suicide": "Selvskade eller selvmord", "gui.abuseReport.reason.self_harm_or_suicide.description": "Noen truer med å skade seg selv i virkeligheten eller snakker om å skade seg selv i virkeligheten.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON> upassende", "gui.abuseReport.reason.sexually_inappropriate.description": "Skall som avbilder seksuelle handlinger, kjønnsorganer og seksuell vold.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme eller voldelig ekstremisme", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> sna<PERSON> o<PERSON>, <PERSON><PERSON><PERSON>, eller truer med å begå terrorisme eller voldelig ekstremisme av politiske, religi<PERSON>se, ideologiske eller andre grunner.", "gui.abuseReport.reason.title": "Velg rapportkategori", "gui.abuseReport.report_sent_msg": "Vi har godt og vel mottatt rapporten din. Takk!\n\nVårt team går gjennom den så snart som mulig.", "gui.abuseReport.select_reason": "Velg rapportkategori", "gui.abuseReport.send": "Send rapport", "gui.abuseReport.send.comment_too_long": "Vennligst forkort kommentaren", "gui.abuseReport.send.error_message": "En feil oppsto under sending av rapporten din:\n'%s'", "gui.abuseReport.send.generic_error": "<PERSON><PERSON><PERSON> på en uventet feil under sending av rapporten din.", "gui.abuseReport.send.http_error": "En uventet HTTP-feil inntraff under sending av rapporten din.", "gui.abuseReport.send.json_error": "<PERSON><PERSON><PERSON> på misdannet forsendelse under sending av rapporten din.", "gui.abuseReport.send.no_reason": "Vennligst velg en rapportkategori", "gui.abuseReport.send.not_attested": "Vennligst les teksten over og kryss av boksen for å kunne sende rapporten", "gui.abuseReport.send.service_unavailable": "Kunne ikke nå misbruksrapporteringstjenesten. Vennligst bekreft at du er koblet på nett og prøv igjen.", "gui.abuseReport.sending.title": "Sender rapporten din...", "gui.abuseReport.sent.title": "Rapport sendt", "gui.abuseReport.skin.title": "Rapporter spillerskall", "gui.abuseReport.title": "Rapporter spiller", "gui.abuseReport.type.chat": "Nettpratsmeldinger", "gui.abuseReport.type.name": "Spillernavn", "gui.abuseReport.type.skin": "Spillerskall", "gui.acknowledge": "<PERSON><PERSON><PERSON><PERSON>", "gui.advancements": "Fremsk<PERSON>t", "gui.all": "Alle", "gui.back": "Tilbake", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON>g lenken for å finne ut mer: %s", "gui.banned.description.permanent": "<PERSON><PERSON><PERSON> din er evig bann<PERSON>, som betyr at du ikke kan spille på nett eller bli med i Realmer.", "gui.banned.description.reason": "Vi mottok nylig en rapport om dårlig oppførsel fra kontoen din. Våre moderatorer har nå gått gjennom saken og konkludert med at den viser %s, som bryter med Minecrafts fellesskapsstandarder.", "gui.banned.description.reason_id": "Kode: %s", "gui.banned.description.reason_id_message": "Kode: %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON> det kan du ikke spille på nett eller bli med i Realms.", "gui.banned.description.temporary.duration": "Kontoen din er midlertidig utestengt og gjenaktiveres om %s.", "gui.banned.description.unknownreason": "Vi mottok nylig en rapport om dårlig oppførsel fra kontoen din. Våre moderatorer har nå gått gjennom saken og konkludert med at du har brutt Minecrafts fellesskapsstandarder.", "gui.banned.name.description": "Ditt nåværende navn - \"%s\" - bryter våre fellesskapsstandarder. Du kan spille enkeltspillermodus, men må endre navnet ditt for å spille på nett.\n\n<PERSON> ut mer eller send inn saken til vurdering på den følgende lenken: %s", "gui.banned.name.title": "Navn ikke tillatt i flerspillermodus", "gui.banned.reason.defamation_impersonation_false_information": "Etterligning eller deling av informasjon for å utnytte eller villede andre", "gui.banned.reason.drugs": "Henvisninger til ulovlige rusmiddel", "gui.banned.reason.extreme_violence_or_gore": "Forestilling av virkelig overdreven vold eller blodsutgytelse", "gui.banned.reason.false_reporting": "Overdrevent falske eller unøyaktige rapporter", "gui.banned.reason.fraud": "<PERSON><PERSON><PERSON><PERSON> tilegning eller bruk av innhold", "gui.banned.reason.generic_violation": "Brudd på fellesskapsstandardene", "gui.banned.reason.harassment_or_bullying": "Sårende språk brukt på en rettet, skadelig måte", "gui.banned.reason.hate_speech": "<PERSON><PERSON><PERSON> eller diskriminering", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON><PERSON> til hatgrupper, terrororgan<PERSON><PERSON><PERSON>er, el<PERSON> omstridte skikkelser", "gui.banned.reason.imminent_harm_to_person_or_property": "Hensikt å forårsake personskade eller eiendomsskade i virkeligheten", "gui.banned.reason.nudity_or_pornography": "Visning av uanstendig eller pornografisk materiale", "gui.banned.reason.sexually_inappropriate": "<PERSON>ner eller innhold av seksuell art", "gui.banned.reason.spam_or_advertising": "Søppelpost eller reklamering", "gui.banned.skin.description": "Ditt nåværende skall bryter våre fellesskapsstandarder. Du kan enda spille med et av de medfølgende skallene eller velge et nytt et.\n\nFinn ut mer eller send inn saken til vurdering på den følgende lenken: %s", "gui.banned.skin.title": "Skall ikke tillatt", "gui.banned.title.permanent": "Konto permanent bannlyst", "gui.banned.title.temporary": "<PERSON><PERSON> mid<PERSON> utest<PERSON>t", "gui.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Å dele detaljer hjelper oss å ta en velgrunnet avgjørelse.", "gui.chatReport.discard.content": "<PERSON><PERSON>r du ut forkaster du denne rapporten og kommentarene dine.\nEr du sikker på at du vil gå ut?", "gui.chatReport.discard.discard": "Forlat og forkast rapport", "gui.chatReport.discard.draft": "Behold som utkast", "gui.chatReport.discard.return": "Fortsett å redigere", "gui.chatReport.discard.title": "Forkast rapport og kommentarer?", "gui.chatReport.draft.content": "Vil du fortsette på utkastet som alt finnes eller forkaste det og lage en ny rapport?", "gui.chatReport.draft.discard": "Forkast", "gui.chatReport.draft.edit": "Fortsett å redigere", "gui.chatReport.draft.quittotitle.content": "Vil du fortsette på utkastet eller forkaste det?", "gui.chatReport.draft.quittotitle.title": "Du har et utkast til en nettpratsrapport som går tapt om du går ut", "gui.chatReport.draft.title": "Fortsette på utkastet?", "gui.chatReport.more_comments": "Vennligst forklar hva som skjedde:", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON>r rapporterer du dette?", "gui.chatReport.read_info": "Mer om rapportering", "gui.chatReport.report_sent_msg": "Vi har mottatt rapporten din. Takk!\n\nVårt team går gjennom den så snart som mulig.", "gui.chatReport.select_chat": "Velg nettpratsmeldinger å rapportere", "gui.chatReport.select_reason": "Velg rapportkategori", "gui.chatReport.selected_chat": "%s nettpratsmelding(er) valgt å rapportere", "gui.chatReport.send": "Send rapport", "gui.chatReport.send.comments_too_long": "Vennligst forkort kommentaren", "gui.chatReport.send.no_reason": "Vennligst velg en rapportkategori", "gui.chatReport.send.no_reported_messages": "Vennligst velg minst én nettpratsmelding å rapportere", "gui.chatReport.send.too_many_messages": "Prøver å ta med for mange meldinger i rapporten", "gui.chatReport.title": "Rapport<PERSON> spillernettprat", "gui.chatSelection.context": "Meldinger omkring dette utvalget tas med for å gi et helhetlig bilde", "gui.chatSelection.fold": "%s melding(er) skjult", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s ble med i nettpraten", "gui.chatSelection.message.narrate": "%s sa: %s kl. %s", "gui.chatSelection.selected": "%s/%s melding(er) valgt", "gui.chatSelection.title": "Velg nettpratsmeldinger å rapportere", "gui.continue": "Fortsett", "gui.copy_link_to_clipboard": "<PERSON><PERSON><PERSON> lenke til utklippstavle", "gui.days": "%s dag(er)", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "<PERSON>", "gui.entity_tooltip.type": "Type: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Avviste %s filer", "gui.fileDropFailure.title": "Lyktes ikke med å legge til filer", "gui.hours": "%s time(r)", "gui.loadingMinecraft": "Laster inn Minecraft", "gui.minutes": "%s minutt(er)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s knapp", "gui.narrate.editBox": "%s endre boks: %s", "gui.narrate.slider": "%s glider", "gui.narrate.tab": "%s-fane", "gui.no": "<PERSON><PERSON>", "gui.none": "Ingen", "gui.ok": "Ok", "gui.open_report_dir": "Åpn rapportkatalog", "gui.proceed": "Fortsett", "gui.recipebook.moreRecipes": "Høyreklikk for fler", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Søk...", "gui.recipebook.toggleRecipes.all": "Viser alle", "gui.recipebook.toggleRecipes.blastable": "Viser smeltbare", "gui.recipebook.toggleRecipes.craftable": "Viser lagbare", "gui.recipebook.toggleRecipes.smeltable": "Viser smeltbare", "gui.recipebook.toggleRecipes.smokable": "Viser røykbare", "gui.report_to_server": "Rapporter til server", "gui.socialInteractions.blocking_hint": "Tilpass med Microsoft-konto", "gui.socialInteractions.empty_blocked": "Ingen blokkerte spillere i nettpraten", "gui.socialInteractions.empty_hidden": "Ingen skjulte spillere i nettpraten", "gui.socialInteractions.hidden_in_chat": "Nettprat fra %s vil skjules", "gui.socialInteractions.hide": "Skjul i nettpraten", "gui.socialInteractions.narration.hide": "Skjul meldinger fra %s", "gui.socialInteractions.narration.report": "Rapporter spiller %s", "gui.socialInteractions.narration.show": "Vis meldinger fra %s", "gui.socialInteractions.report": "Rapporter", "gui.socialInteractions.search_empty": "Fant ingen spillere med det navnet", "gui.socialInteractions.search_hint": "Søk...", "gui.socialInteractions.server_label.multiple": "%s - %s spillere", "gui.socialInteractions.server_label.single": "%s - %s spiller", "gui.socialInteractions.show": "<PERSON>is i nettpraten", "gui.socialInteractions.shown_in_chat": "Nettprat fra %s vil vises", "gui.socialInteractions.status_blocked": "Blokkert", "gui.socialInteractions.status_blocked_offline": "Blokkert - Frakoblet", "gui.socialInteractions.status_hidden": "Skjult", "gui.socialInteractions.status_hidden_offline": "Skjult - Frakoblet", "gui.socialInteractions.status_offline": "Frakoblet", "gui.socialInteractions.tab_all": "Alle", "gui.socialInteractions.tab_blocked": "Blokkerte", "gui.socialInteractions.tab_hidden": "Skjulte", "gui.socialInteractions.title": "Samspill", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON><PERSON> meldinger", "gui.socialInteractions.tooltip.report": "Rapporter spiller", "gui.socialInteractions.tooltip.report.disabled": "Rapporteringstjenesten er utilgjengelig", "gui.socialInteractions.tooltip.report.no_messages": "Ingen rapporterbare meldinger fra spiller %s", "gui.socialInteractions.tooltip.report.not_reportable": "Denne spilleren kan ikke rapporteres, siden spillerens nettprat ikke kan verifiseres på denne serveren", "gui.socialInteractions.tooltip.show": "<PERSON>is meldinger", "gui.stats": "Statistikk", "gui.toMenu": "Tilbake til serverlisten", "gui.toRealms": "Til<PERSON><PERSON> til Realms-listen", "gui.toTitle": "Tilbake til hovedmenyen", "gui.toWorld": "Tilbake til verdenslisten", "gui.togglable_slot": "Trykk for å sperre rute", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Tilbake (%ss)", "gui.waitingForResponse.title": "Venter på server", "gui.yes": "<PERSON>a", "hanging_sign.edit": "Rediger melding på hengende skilt", "instrument.minecraft.admire_goat_horn": "Beundre", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Undre", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "Synge", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Ødelegg gjenstand", "inventory.hotbarInfo": "Lagre verktøylinje med %1$s+%2$s", "inventory.hotbarSaved": "Verktøylinje lagret (gjenopprett med %1$s+%2$s)", "item.canBreak": "<PERSON>n ø<PERSON>:", "item.canPlace": "Kan plasseres på:", "item.canUse.unknown": "<PERSON><PERSON><PERSON><PERSON>", "item.color": "Farge: %s", "item.components": "%s komponent(er)", "item.disabled": "Ubrukbar gjenstand", "item.durability": "Holdbarhet: %s / %s", "item.dyed": "Farget", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "Akasiebåt med kiste", "item.minecraft.allay_spawn_egg": "Fremkallingsegg for hjelpeånd", "item.minecraft.amethyst_shard": "Ametystskår", "item.minecraft.angler_pottery_shard": "Potteskår med fiskestang", "item.minecraft.angler_pottery_sherd": "Potteskår med fiskestang", "item.minecraft.apple": "Eple", "item.minecraft.archer_pottery_shard": "Potteskår med pil og bue", "item.minecraft.archer_pottery_sherd": "Potteskår med pil og bue", "item.minecraft.armadillo_scute": "Beltedyrskjoldplate", "item.minecraft.armadillo_spawn_egg": "Fremkallingsegg for beltedyr", "item.minecraft.armor_stand": "Rustningsstativ", "item.minecraft.arms_up_pottery_shard": "Potteskår med armer i været", "item.minecraft.arms_up_pottery_sherd": "Potteskår med armer i været", "item.minecraft.arrow": "<PERSON>l", "item.minecraft.axolotl_bucket": "Axolotl i bøtte", "item.minecraft.axolotl_spawn_egg": "Fremkallingsegg for axolotl", "item.minecraft.baked_potato": "Bakt potet", "item.minecraft.bamboo_chest_raft": "Bambusflåte med kiste", "item.minecraft.bamboo_raft": "Bambusflåte", "item.minecraft.bat_spawn_egg": "Fremkallingsegg for flaggermus", "item.minecraft.bee_spawn_egg": "Fremkallingsegg for bie", "item.minecraft.beef": "<PERSON><PERSON> biff", "item.minecraft.beetroot": "R<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Rødbetefrø", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_chest_boat": "Bjørkebåt med kiste", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON> farges<PERSON>ff", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON> seletø<PERSON>", "item.minecraft.blade_pottery_shard": "Potteskår med klinge", "item.minecraft.blade_pottery_sherd": "Potteskår med klinge", "item.minecraft.blaze_powder": "Flammepulver", "item.minecraft.blaze_rod": "Flammestang", "item.minecraft.blaze_spawn_egg": "Fremkallingsegg for flammeskrømt", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON><PERSON> far<PERSON>ff", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>tt egg", "item.minecraft.blue_harness": "B<PERSON><PERSON>tt seletøy", "item.minecraft.bogged_spawn_egg": "Framkallingsegg for sumprangel", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "Lynaktig rustningspryd", "item.minecraft.bone": "Bein", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON>", "item.minecraft.book": "Bok", "item.minecraft.bordure_indented_banner_pattern": "Bord med tannsnitt-bannermønster", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "Vindstang", "item.minecraft.breeze_spawn_egg": "Fremkallingsegg for vindskrømt", "item.minecraft.brewer_pottery_shard": "Potteskår med brygg", "item.minecraft.brewer_pottery_sherd": "Potteskår med brygg", "item.minecraft.brewing_stand": "Bryggestativ", "item.minecraft.brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "Brun bunt", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "Brunt egg", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Bunt", "item.minecraft.bundle.empty": "<PERSON>", "item.minecraft.bundle.empty.description": "Kan romme en blandet hop av gjenstander", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Potteskår med ild", "item.minecraft.burn_pottery_sherd": "Potteskår med ild", "item.minecraft.camel_spawn_egg": "Fremkallingsegg for dromedar", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON> på stang", "item.minecraft.cat_spawn_egg": "Fremkallingsegg for katt", "item.minecraft.cauldron": "Gryte", "item.minecraft.cave_spider_spawn_egg": "Fremkallingsegg for huleedderkopp", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "Ringbrynje", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "Brynje<PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "Kirsebærtrebåt med kiste", "item.minecraft.chest_minecart": "Gruvevogn med kiste", "item.minecraft.chicken": "<PERSON><PERSON> kylling", "item.minecraft.chicken_spawn_egg": "Fremkallingsegg for høne", "item.minecraft.chorus_fruit": "Refrengfrukt", "item.minecraft.clay_ball": "Leireball", "item.minecraft.clock": "K<PERSON>kke", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "Forlist rustning<PERSON>ryd", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON>rsk", "item.minecraft.cod_bucket": "Torskebøtte", "item.minecraft.cod_spawn_egg": "Fremkallingsegg for torsk", "item.minecraft.command_block_minecart": "Gruvevogn med kommandoblokk", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Stekt biff", "item.minecraft.cooked_chicken": "Stekt kylling", "item.minecraft.cooked_cod": "Stekt torsk", "item.minecraft.cooked_mutton": "Stekt få<PERSON>", "item.minecraft.cooked_porkchop": "Stekt svinekotelett", "item.minecraft.cooked_rabbit": "Stekt kanin", "item.minecraft.cooked_salmon": "Stekt laks", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "Fremkallingsegg for ku", "item.minecraft.creaking_spawn_egg": "Fremkallingsegg for knirkning", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-bannermønster", "item.minecraft.creeper_spawn_egg": "Fremkallingsegg for creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Ladning:", "item.minecraft.crossbow.projectile.multiple": "Prosjektil: %s × %s", "item.minecraft.crossbow.projectile.single": "Prosjektil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON> bunt", "item.minecraft.cyan_dye": "Turk<PERSON> fargestoff", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Potteskår med fare", "item.minecraft.danger_pottery_sherd": "Potteskår med fare", "item.minecraft.dark_oak_boat": "Mørkeikeb<PERSON>t", "item.minecraft.dark_oak_chest_boat": "Mørkeikebåt med kiste", "item.minecraft.debug_stick": "Feilsøkingspinne", "item.minecraft.debug_stick.empty": "%s har ingen egenskaper", "item.minecraft.debug_stick.select": "valgte \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" til %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "Diamantbrystplate", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Diamantgrev", "item.minecraft.diamond_horse_armor": "Diamanthesterustning", "item.minecraft.diamond_leggings": "Diamantbenplater", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Diamantsverd", "item.minecraft.disc_fragment_5": "Platefragment", "item.minecraft.disc_fragment_5.desc": "Musikkplate - 5", "item.minecraft.dolphin_spawn_egg": "Fremkallingsegg for delfin", "item.minecraft.donkey_spawn_egg": "Fremkallingsegg for esel", "item.minecraft.dragon_breath": "Dragens ånde", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON><PERSON> tare", "item.minecraft.drowned_spawn_egg": "Fremkallingsegg for draug", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "Dynens rustningspryd", "item.minecraft.echo_shard": "Ekkoskår", "item.minecraft.egg": "Egg", "item.minecraft.elder_guardian_spawn_egg": "Fremkallingsegg for urvokter", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Fortryllet bok", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> gulleple", "item.minecraft.end_crystal": "End-krystall", "item.minecraft.ender_dragon_spawn_egg": "Fremkallingsegg for enderdrage", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Fremkallingsegg for endermann", "item.minecraft.endermite_spawn_egg": "Fremkallingsegg for endermidd", "item.minecraft.evoker_spawn_egg": "Fremkallingsegg for åndemaner", "item.minecraft.experience_bottle": "Fortryllelsesflaske", "item.minecraft.explorer_pottery_shard": "Potteskår med kart", "item.minecraft.explorer_pottery_sherd": "Potteskår med kart", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> rustningspryd", "item.minecraft.feather": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.filled_map": "Kart", "item.minecraft.fire_charge": "Ildladning", "item.minecraft.firework_rocket": "Fyrverkeri", "item.minecraft.firework_rocket.flight": "Flygevarighet:", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Fyrverkeristjerne", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blå", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Tilpasset", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Ton inn til", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "Grå", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Lyseblå", "item.minecraft.firework_star.light_gray": "Lysegrå", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Oransje", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Ukjent form", "item.minecraft.firework_star.shape.burst": "Eksplosjon", "item.minecraft.firework_star.shape.creeper": "Creeper-formet", "item.minecraft.firework_star.shape.large_ball": "Stor kule", "item.minecraft.firework_star.shape.small_ball": "Liten kule", "item.minecraft.firework_star.shape.star": "Stjerneformet", "item.minecraft.firework_star.trail": "Spor", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Gul", "item.minecraft.fishing_rod": "Fiskestang", "item.minecraft.flint": "Flint", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "Virvlende rustningspryd", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON>vel<PERSON><PERSON><PERSON><PERSON><PERSON>er", "item.minecraft.flow_pottery_sherd": "Potteskår med virvel", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "B<PERSON>mst", "item.minecraft.flower_banner_pattern.new": "Blomst-bannermønster", "item.minecraft.flower_pot": "Blomsterpotte", "item.minecraft.fox_spawn_egg": "Fremkallingsegg for rev", "item.minecraft.friend_pottery_shard": "Potteskår med venn", "item.minecraft.friend_pottery_sherd": "Potteskår med venn", "item.minecraft.frog_spawn_egg": "Fremkallingsegg for frosk", "item.minecraft.furnace_minecart": "Dampdrevet gruvevogn", "item.minecraft.ghast_spawn_egg": "Fremkallingsegg for ghast", "item.minecraft.ghast_tear": "Ghasttåre", "item.minecraft.glass_bottle": "Glassflaske", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Globus-bannermønster", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON>bæ<PERSON>", "item.minecraft.glow_ink_sac": "Glosekk", "item.minecraft.glow_item_frame": "Gløderamme", "item.minecraft.glow_squid_spawn_egg": "Fremkallingsegg for glosprut", "item.minecraft.glowstone_dust": "Glødesteinspulver", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Fremkallingsegg for geit", "item.minecraft.gold_ingot": "Gullbarre", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Gullbrystplate", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "Gullhesterustning", "item.minecraft.golden_leggings": "Gullbenplater", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "Gullspade", "item.minecraft.golden_sword": "Gullsverd", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Fremkallingsegg for vokter", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "Vindkaster", "item.minecraft.guster_banner_pattern.new": "Vindkaster-bannermønster", "item.minecraft.guster_pottery_sherd": "Potteskår med vindkaster", "item.minecraft.happy_ghast_spawn_egg": "Fremkallingsegg for lykkelig ghast", "item.minecraft.harness": "Seletøy", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON> hjer<PERSON>", "item.minecraft.heart_pottery_shard": "Potteskår med hjerte", "item.minecraft.heart_pottery_sherd": "Potteskår med hjerte", "item.minecraft.heartbreak_pottery_shard": "Potteskår med hjerteskjær", "item.minecraft.heartbreak_pottery_sherd": "Potteskår med hjerteskjær", "item.minecraft.hoglin_spawn_egg": "Fremkallingsegg for hoglin", "item.minecraft.honey_bottle": "Honning på flaske", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON>", "item.minecraft.hopper_minecart": "Gruvevogn med trakt", "item.minecraft.horse_spawn_egg": "Fremkallingsegg for hest", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Vertens rustningspryd", "item.minecraft.howl_pottery_shard": "Potteskår med ulv", "item.minecraft.howl_pottery_sherd": "Potteskår med ulv", "item.minecraft.husk_spawn_egg": "Fremkallingsegg for ørkenzombie", "item.minecraft.ink_sac": "Blekksekk", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Fremkallingsegg for jernkjempe", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Jernhesterustning", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "Je<PERSON><PERSON>d", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "Jungeltrebåt med kiste", "item.minecraft.knowledge_book": "Kunnskapsbok", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lead": "Tau", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "L<PERSON>rtrø<PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Lærhesterustning", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> far<PERSON>ff", "item.minecraft.light_blue_harness": "Lyseblått seletøy", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunt", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON>g ved<PERSON><PERSON>e brygg", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>t brygg", "item.minecraft.lingering_potion.effect.fire_resistance": "Vedvarende ildmotstandsbrygg", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>b<PERSON>gg", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON>brygg", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON> levitasjonsbrygg", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON> lykkebrygg", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON> ved<PERSON><PERSON>e brygg", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON> natts<PERSON>brygg", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON> regene<PERSON>b<PERSON>gg", "item.minecraft.lingering_potion.effect.slow_falling": "Vedvarene fjærfallsbrygg", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.thick": "T<PERSON><PERSON> vedvarende brygg", "item.minecraft.lingering_potion.effect.turtle_master": "Skilpaddemesterens vedvarende brygg", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>brygg", "item.minecraft.llama_spawn_egg": "Fremkallingsegg for lama", "item.minecraft.lodestone_compass": "Leidarsteinskompass", "item.minecraft.mace": "Stridsklubbe", "item.minecraft.magenta_bundle": "Magenta bunt", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmakrem", "item.minecraft.magma_cube_spawn_egg": "Fremkallingsegg for magmakube", "item.minecraft.mangrove_boat": "Mangrovebåt", "item.minecraft.mangrove_chest_boat": "Mangrovebåt med kiste", "item.minecraft.map": "Blankt kart", "item.minecraft.melon_seeds": "Melonfrø", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.minecart": "Gruvevogn", "item.minecraft.miner_pottery_shard": "Potteskår med hakke", "item.minecraft.miner_pottery_sherd": "Potteskår med hakke", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Dings", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "Fremkallingsegg for mooshroom", "item.minecraft.mourner_pottery_shard": "Potteskår med forvarer", "item.minecraft.mourner_pottery_sherd": "Potteskår med forvarer", "item.minecraft.mule_spawn_egg": "Fremkallingsegg for muldyr", "item.minecraft.mushroom_stew": "Soppstuing", "item.minecraft.music_disc_11": "Musikkplate", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Musikkplate", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Musikkplate", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Musikkplate", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Musikkplate", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Musikkplate", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Musikkplate", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Musikkplate", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Musikkplate", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Musikkplate", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Musikkplate", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Musikkplate", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Musikkplate", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Musikkplate", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Musikkplate", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Musikkplate", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Musikkplate", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Musikkplate", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Musikkplate", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Musikkplate", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Musikkplate", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Navnelapp", "item.minecraft.nautilus_shell": "Perlebåtskall", "item.minecraft.nether_brick": "Netherteglstein", "item.minecraft.nether_star": "Netherstjerne", "item.minecraft.nether_wart": "Nethervorte", "item.minecraft.netherite_axe": "Netherittøks", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_chestplate": "Netherittbrystplate", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_hoe": "Netherittgrev", "item.minecraft.netherite_ingot": "Netherittbarre", "item.minecraft.netherite_leggings": "Netherittbenplater", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_scrap": "Netherittskrap", "item.minecraft.netherite_shovel": "Netherittspade", "item.minecraft.netherite_sword": "Netherittsverd", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherittoppgradering", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON><PERSON>b<PERSON>t med kiste", "item.minecraft.ocelot_spawn_egg": "Fremkallingsegg for ozelot", "item.minecraft.ominous_bottle": "Illevarslende flaske", "item.minecraft.ominous_trial_key": "Illevarslende prøvelsesnø<PERSON>kel", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "<PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Blekeikebåt", "item.minecraft.pale_oak_chest_boat": "Blekeikebåt med kiste", "item.minecraft.panda_spawn_egg": "Fremkallingsegg for panda", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Fremkallingsegg for papegøye", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Fremkallingsegg for fantom", "item.minecraft.pig_spawn_egg": "Fremkallingsegg for gris", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_brute_spawn_egg": "Fremkallingsegg for piglinbølle", "item.minecraft.piglin_spawn_egg": "Fremkallingsegg for piglin", "item.minecraft.pillager_spawn_egg": "Fremkallingsegg for plyndrer", "item.minecraft.pink_bundle": "<PERSON> bunt", "item.minecraft.pink_dye": "<PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Kanneplantebelg", "item.minecraft.plenty_pottery_shard": "Potteskår med rikdom", "item.minecraft.plenty_pottery_sherd": "Potteskår med rikdom", "item.minecraft.poisonous_potato": "<PERSON><PERSON> potet", "item.minecraft.polar_bear_spawn_egg": "Fremkallingsegg for isbjørn", "item.minecraft.popped_chorus_fruit": "R<PERSON>et refrengfrukt", "item.minecraft.porkchop": "Rå svinekotelett", "item.minecraft.potato": "Potet", "item.minecraft.potion": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> br<PERSON>gg", "item.minecraft.potion.effect.fire_resistance": "Ildmotstandsbrygg", "item.minecraft.potion.effect.harming": "Ska<PERSON><PERSON><PERSON>gg", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "Befengningsbrygg", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "Levitasjonsbrygg", "item.minecraft.potion.effect.luck": "Lykkebrygg", "item.minecraft.potion.effect.mundane": "Dagligdags brygg", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "Regenereringsbrygg", "item.minecraft.potion.effect.slow_falling": "Fjærfallsbrygg", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.strength": "Styrkebrygg", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.thick": "Tykk brygg", "item.minecraft.potion.effect.turtle_master": "Skilpaddemesterens brygg", "item.minecraft.potion.effect.water": "Vannflaske", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gg", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.wind_charged": "Vindladningsbrygg", "item.minecraft.pottery_shard_archer": "Potteskår med pil og bue", "item.minecraft.pottery_shard_arms_up": "Potteskår med armer i været", "item.minecraft.pottery_shard_prize": "Potteskår med edelsten", "item.minecraft.pottery_shard_skull": "Potteskår med skalle", "item.minecraft.powder_snow_bucket": "Puddersnøbøtte", "item.minecraft.prismarine_crystals": "Prismarinkrystaller", "item.minecraft.prismarine_shard": "Prismarinskår", "item.minecraft.prize_pottery_shard": "Potteskår med edelsten", "item.minecraft.prize_pottery_sherd": "Potteskår med edelsten", "item.minecraft.pufferfish": "Kulefisk", "item.minecraft.pufferfish_bucket": "Kulefiskebøtte", "item.minecraft.pufferfish_spawn_egg": "Fremkallingsegg for kulefisk", "item.minecraft.pumpkin_pie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON>karfrø", "item.minecraft.purple_bundle": "<PERSON><PERSON> bunt", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON>", "item.minecraft.quartz": "Netherkvarts", "item.minecraft.rabbit": "<PERSON><PERSON> kanin", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Fremkallingsegg for kanin", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Oppdretterens rustningspryd", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON>lingsegg for herjer", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Gjenfinnerkompass", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Redstonepulver", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "Ribbeinsaktig rustningspryd", "item.minecraft.rotten_flesh": "R<PERSON><PERSON><PERSON> kjøtt", "item.minecraft.saddle": "Sal", "item.minecraft.salmon": "Rå laks", "item.minecraft.salmon_bucket": "Lakseb<PERSON>tte", "item.minecraft.salmon_spawn_egg": "Fremkallingsegg for laks", "item.minecraft.scrape_pottery_sherd": "Potteskår med øks", "item.minecraft.scute": "S<PERSON><PERSON>ldplate", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "P<PERSON><PERSON>t rustningspryd", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "Formgiverens rustningspryd", "item.minecraft.sheaf_pottery_shard": "Potteskår med nek", "item.minecraft.sheaf_pottery_sherd": "Potteskår med nek", "item.minecraft.shears": "Saks", "item.minecraft.sheep_spawn_egg": "Fremkallingsegg for sau", "item.minecraft.shelter_pottery_shard": "Potteskår med ly", "item.minecraft.shelter_pottery_sherd": "Potteskår med ly", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.blue": "Blått skjold", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.cyan": "Turk<PERSON> skjold", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.light_blue": "Lyseblått skjold", "item.minecraft.shield.light_gray": "Lysegr<PERSON>tt skjold", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON>jo<PERSON>", "item.minecraft.shield.orange": "Oransje s<PERSON>jold", "item.minecraft.shield.pink": "<PERSON> s<PERSON>jo<PERSON>", "item.minecraft.shield.purple": "<PERSON><PERSON> skjold", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON> skjold", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON>jold", "item.minecraft.shield.yellow": "<PERSON><PERSON> skjold", "item.minecraft.shulker_shell": "<PERSON>lkerskjell", "item.minecraft.shulker_spawn_egg": "Fremkallingsegg for shulker", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON>tens rustningspryd", "item.minecraft.silverfish_spawn_egg": "Fremkallingsegg for sølvkre", "item.minecraft.skeleton_horse_spawn_egg": "Fremkallingsegg for skjeletthest", "item.minecraft.skeleton_spawn_egg": "Fremkallingsegg for skjelett", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "Hodeskalle", "item.minecraft.skull_banner_pattern.new": "Hodeskalle-bannermønster", "item.minecraft.skull_pottery_shard": "Potteskår med skalle", "item.minecraft.skull_pottery_sherd": "Potteskår med skalle", "item.minecraft.slime_ball": "Slimball", "item.minecraft.slime_spawn_egg": "Fremkallingsegg for slim", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "Brukes på:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Legg til barre eller krystall", "item.minecraft.smithing_template.armor_trim.applies_to": "Rustning", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Legg til en rustningsdel", "item.minecraft.smithing_template.armor_trim.ingredients": "Barrer & krystaller", "item.minecraft.smithing_template.ingredients": "Innhold:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Legg til netherittbarre", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamantutstyr", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Legg til rustning, våpen eller redskap av diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherittbarre", "item.minecraft.smithing_template.upgrade": "Oppgradering: ", "item.minecraft.sniffer_spawn_egg": "Fremkallingsegg for snufser", "item.minecraft.snort_pottery_shard": "Potteskår med snuser", "item.minecraft.snort_pottery_sherd": "Potteskår med snuser", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Snuteaktig rustningspryd", "item.minecraft.snow_golem_spawn_egg": "Fremkallingsegg for snømann", "item.minecraft.snowball": "Snøball", "item.minecraft.spectral_arrow": "Spektralpil", "item.minecraft.spider_eye": "Edderkoppøye", "item.minecraft.spider_spawn_egg": "Fremkallingsegg for edderkopp", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "Spirens rustningspryd", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON> brygg", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "Kastbart ildmotstandsbrygg", "item.minecraft.splash_potion.effect.harming": "Ka<PERSON><PERSON>t skadebrygg", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>brygg", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON><PERSON> ka<PERSON>", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON>brygg", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.levitation": "Ka<PERSON><PERSON><PERSON> levitasjonsbrygg", "item.minecraft.splash_potion.effect.luck": "Kastbart lykkebrygg", "item.minecraft.splash_potion.effect.mundane": "Dagligdags kastebrygg", "item.minecraft.splash_potion.effect.night_vision": "Kastbart nattsynsbrygg", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.regeneration": "Ka<PERSON><PERSON><PERSON> regenereringsbrygg", "item.minecraft.splash_potion.effect.slow_falling": "Kastbart fjærfallsbrygg", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON>re<PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> styrkebrygg", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Skilpaddemesterens kastebrygg", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.weakness": "Ka<PERSON><PERSON><PERSON> svakhetsbrygg", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON> ve<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.wind_charged": "Kastbar vindladningsbrygg", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "Granb<PERSON>t med kiste", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Fremkallingsegg for blekksprut", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "Steinspade", "item.minecraft.stone_sword": "Steinsverd", "item.minecraft.stray_spawn_egg": "Fremkallingsegg for vandrer", "item.minecraft.strider_spawn_egg": "Fremkmallingsegg for lavavandrer", "item.minecraft.string": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "Mistenkelig stuing", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON>b<PERSON><PERSON>", "item.minecraft.tadpole_bucket": "<PERSON><PERSON><PERSON> med rumpetroll", "item.minecraft.tadpole_spawn_egg": "Fremkallingsegg for rumpetroll", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.tipped_arrow": "Dyppet pil", "item.minecraft.tipped_arrow.effect.awkward": "Dyppet pil", "item.minecraft.tipped_arrow.effect.empty": "Uproduserbar dyppet pil", "item.minecraft.tipped_arrow.effect.fire_resistance": "Flammemotstandspil", "item.minecraft.tipped_arrow.effect.harming": "Skadepil", "item.minecraft.tipped_arrow.effect.healing": "Helbredelsespil", "item.minecraft.tipped_arrow.effect.infested": "Befengningspil", "item.minecraft.tipped_arrow.effect.invisibility": "Usynlighetspil", "item.minecraft.tipped_arrow.effect.leaping": "Sprettpil", "item.minecraft.tipped_arrow.effect.levitation": "Levitasjonspil", "item.minecraft.tipped_arrow.effect.luck": "Lykkepil", "item.minecraft.tipped_arrow.effect.mundane": "Dyppet pil", "item.minecraft.tipped_arrow.effect.night_vision": "Nattsynspil", "item.minecraft.tipped_arrow.effect.oozing": "Tytingspil", "item.minecraft.tipped_arrow.effect.poison": "Giftpil", "item.minecraft.tipped_arrow.effect.regeneration": "Regenereringspil", "item.minecraft.tipped_arrow.effect.slow_falling": "Fjærfallspil", "item.minecraft.tipped_arrow.effect.slowness": "Treghetspil", "item.minecraft.tipped_arrow.effect.strength": "Styrkepil", "item.minecraft.tipped_arrow.effect.swiftness": "Hurtighetspil", "item.minecraft.tipped_arrow.effect.thick": "Dyppet pil", "item.minecraft.tipped_arrow.effect.turtle_master": "Skilpaddemesterens pil", "item.minecraft.tipped_arrow.effect.water": "Sprutepil", "item.minecraft.tipped_arrow.effect.water_breathing": "Undervannspustpil", "item.minecraft.tipped_arrow.effect.weakness": "Svekkelsespil", "item.minecraft.tipped_arrow.effect.weaving": "Vevingspil", "item.minecraft.tipped_arrow.effect.wind_charged": "Vindladningspil", "item.minecraft.tnt_minecart": "Gruvevogn med TNT", "item.minecraft.torchflower_seeds": "Fakkelblomstfrø", "item.minecraft.totem_of_undying": "Udødelighetstotem", "item.minecraft.trader_llama_spawn_egg": "Fremkallingsegg for handelslama", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trident": "Trefork", "item.minecraft.tropical_fish": "Sydhavsfisk", "item.minecraft.tropical_fish_bucket": "Sydhavsfiskebøtte", "item.minecraft.tropical_fish_spawn_egg": "Fremkallingsegg for sydhavsfisk", "item.minecraft.turtle_helmet": "Skilpaddeskall", "item.minecraft.turtle_scute": "Skilpaddeskjoldplate", "item.minecraft.turtle_spawn_egg": "Fremkallingsegg for skilpadde", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Plageåndens rustningspryd", "item.minecraft.vex_spawn_egg": "Fremkallingsegg for plageånd", "item.minecraft.villager_spawn_egg": "Fremkallingsegg for bygding", "item.minecraft.vindicator_spawn_egg": "Fremkallingsegg for forsvarer", "item.minecraft.wandering_trader_spawn_egg": "Fremkallingsegg for vandrehandler", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "Forvart rustningspryd", "item.minecraft.warden_spawn_egg": "Fremkallingsegg for forvarer", "item.minecraft.warped_fungus_on_a_stick": "Forvridd sopp på stang", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Veiviserens rustningspryd", "item.minecraft.wheat": "<PERSON><PERSON><PERSON>", "item.minecraft.wheat_seeds": "H<PERSON>efrø", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON> bunt", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "Vill rustningspryd", "item.minecraft.wind_charge": "Vindladning", "item.minecraft.witch_spawn_egg": "Fremkallingsegg for heks", "item.minecraft.wither_skeleton_spawn_egg": "Fremkallingsegg for witherskjelett", "item.minecraft.wither_spawn_egg": "Frem<PERSON>lingsegg for wither", "item.minecraft.wolf_armor": "Ulverustning", "item.minecraft.wolf_spawn_egg": "Fremkallingsegg for ulv", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON>grev", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "Tresverd", "item.minecraft.writable_book": "Bok og fjærpenn", "item.minecraft.written_book": "Skrevet bok", "item.minecraft.yellow_bundle": "Gul bunt", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "Fremkallingsegg for zoglin", "item.minecraft.zombie_horse_spawn_egg": "Fremkallingsegg for zombiehest", "item.minecraft.zombie_spawn_egg": "Fremkallingsegg for zombie", "item.minecraft.zombie_villager_spawn_egg": "Fremkallingsegg for zombiebygding", "item.minecraft.zombified_piglin_spawn_egg": "Fremkallingsegg for zombiepiglin", "item.modifiers.any": "Når utrustet:", "item.modifiers.armor": "<PERSON><PERSON><PERSON> p<PERSON>d:", "item.modifiers.body": "Når utrustet:", "item.modifiers.chest": "Når på kroppen:", "item.modifiers.feet": "<PERSON><PERSON>r på føttene:", "item.modifiers.hand": "Når væpnet:", "item.modifiers.head": "<PERSON><PERSON><PERSON> på hodet:", "item.modifiers.legs": "<PERSON><PERSON><PERSON> på bena:", "item.modifiers.mainhand": "Når i hovedhånden:", "item.modifiers.offhand": "Når i annenhånden:", "item.modifiers.saddle": "<PERSON><PERSON><PERSON> salet:", "item.nbt_tags": "NBT: %s tagg(er)", "item.op_block_warning.line1": "<PERSON><PERSON><PERSON>:", "item.op_block_warning.line2": "Bruk av denne gjenstanden kan lede til kommandoutførelse", "item.op_block_warning.line3": "Bruk ikke med mindre du grundig kjenner innholdet!", "item.unbreakable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON> blokker", "itemGroup.combat": "Kamputstyr", "itemGroup.consumables": "Forb<PERSON>svarer", "itemGroup.crafting": "Håndverk", "itemGroup.foodAndDrink": "Mat & drikke", "itemGroup.functional": "<PERSON><PERSON><PERSON><PERSON> blokker", "itemGroup.hotbar": "Lag<PERSON><PERSON> ve<PERSON>ø<PERSON>", "itemGroup.ingredients": "<PERSON>g<PERSON><PERSON><PERSON>", "itemGroup.inventory": "Overlevelsesinventar", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON> blokker", "itemGroup.op": "Operatørtøy", "itemGroup.redstone": "Redstoneblokker", "itemGroup.search": "<PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "Fremkallingsegg", "itemGroup.tools": "Verktøy & utstyr", "item_modifier.unknown": "Ukjent modifisør: %s", "jigsaw_block.final_state": "Omgjøres til:", "jigsaw_block.generate": "<PERSON><PERSON>", "jigsaw_block.joint.aligned": "<PERSON><PERSON>", "jigsaw_block.joint.rollable": "Roterbar", "jigsaw_block.joint_label": "Leddtype:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON>", "jigsaw_block.levels": "Nivåer: %s", "jigsaw_block.name": "Navn:", "jigsaw_block.placement_priority": "Plasseringsprioritet:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON><PERSON> denne pusleblokken kobler seg til en puslebit, er dette rekkefølgen puslebiten behandles for tilkoblinger i den bredere strukturen.\n\nPuslebiter behandles etter synkende prioritet der insettingsrekkefølgen overtar om uavgjort.", "jigsaw_block.pool": "Utvalg:", "jigsaw_block.selection_priority": "Valgprioritet:", "jigsaw_block.selection_priority.tooltip": "<PERSON><PERSON>r forelderbiten gjennomgå<PERSON> for koblinger, er dette rekkefølgen denne pusleblokken prøver å koble seg til dens mål.\n\nPusleblokker gjennomgås med synkende prioritet hvor tilfeldighet løser uavgjort rekkefølge.", "jigsaw_block.target": "Målnavn:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (spilledåse)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Fremsk<PERSON>t", "key.attack": "Angrip/Ødelegg", "key.back": "<PERSON><PERSON> bakover", "key.categories.creative": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "key.categories.gameplay": "Spilling", "key.categories.inventory": "Inventar", "key.categories.misc": "Diverse", "key.categories.movement": "Bevegelse", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "Spillgrensesnitt", "key.chat": "<PERSON><PERSON><PERSON>", "key.command": "<PERSON><PERSON><PERSON> kommando", "key.drop": "<PERSON><PERSON><PERSON> valgt g<PERSON>and", "key.forward": "Gå fremover", "key.fullscreen": "Fullskjerm", "key.hotbar.1": "Verktøylinjeplass 1", "key.hotbar.2": "Verktøylinjeplass 2", "key.hotbar.3": "Verktøylinjeplass 3", "key.hotbar.4": "Verktøylinjeplass 4", "key.hotbar.5": "Verktøylinjeplass 5", "key.hotbar.6": "Verktøylinjeplass 6", "key.hotbar.7": "Verktøylinjeplass 7", "key.hotbar.8": "Verktøylinjeplass 8", "key.hotbar.9": "Verktøylinjeplass 9", "key.inventory": "Åpne/Lukke inventar", "key.jump": "<PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Tilbake", "key.keyboard.caps.lock": "Caps lock", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON>", "key.keyboard.down": "Pil ned", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Numpad 0", "key.keyboard.keypad.1": "Numpad 1", "key.keyboard.keypad.2": "Numpad 2", "key.keyboard.keypad.3": "Numpad 3", "key.keyboard.keypad.4": "Numpad 4", "key.keyboard.keypad.5": "Numpad 5", "key.keyboard.keypad.6": "Numpad 6", "key.keyboard.keypad.7": "Numpad 7", "key.keyboard.keypad.8": "Numpad 8", "key.keyboard.keypad.9": "Numpad 9", "key.keyboard.keypad.add": "Numpad +", "key.keyboard.keypad.decimal": "<PERSON><PERSON><PERSON> ,", "key.keyboard.keypad.divide": "<PERSON><PERSON><PERSON> /", "key.keyboard.keypad.enter": "<PERSON><PERSON><PERSON>", "key.keyboard.keypad.equal": "Numpad =", "key.keyboard.keypad.multiply": "Numpad *", "key.keyboard.keypad.subtract": "<PERSON><PERSON><PERSON> -", "key.keyboard.left": "Venstrepil", "key.keyboard.left.alt": "Venstre Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Venstre ctrl", "key.keyboard.left.shift": "Venstre skift", "key.keyboard.left.win": "Venstre Win", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num lock", "key.keyboard.page.down": "Page down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "H<PERSON>yrepil", "key.keyboard.right.alt": "H<PERSON>yre Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON><PERSON><PERSON> ctrl", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON> skift", "key.keyboard.right.win": "<PERSON><PERSON><PERSON>", "key.keyboard.scroll.lock": "Scroll lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON>kke bundet", "key.keyboard.up": "Pil opp", "key.keyboard.world.1": "Verden 1", "key.keyboard.world.2": "Verden 2", "key.left": "Gå til venstre", "key.loadToolbarActivator": "Last inn verktøylinjeaktivatoren", "key.mouse": "Knapp %1$s", "key.mouse.left": "Venstre museknapp", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "<PERSON><PERSON><PERSON> m<PERSON>", "key.pickItem": "Velg blokk", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "<PERSON><PERSON> for hånd", "key.right": "Gå til høyre", "key.saveToolbarActivator": "Lagre verktøylinjeaktivatoren", "key.screenshot": "<PERSON> skjermbilde", "key.smoothCamera": "Veksle filmatisk kamera", "key.sneak": "Snik", "key.socialInteractions": "Samspillsoversikt", "key.spectatorOutlines": "<PERSON><PERSON> (tilskuere)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Veksl gjenstand med annenhånd", "key.togglePerspective": "Veksle perspektiv", "key.use": "Bruk gjenstand/Plasser blokk", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Fellesskap", "known_server_link.community_guidelines": "Fellesskapsrettningslinjer", "known_server_link.feedback": "Tilbakemelding", "known_server_link.forums": "Forum", "known_server_link.news": "Nytt", "known_server_link.report_bug": "Rapporter serverfeil", "known_server_link.status": "Status", "known_server_link.support": "<PERSON><PERSON><PERSON>", "known_server_link.website": "Nettside", "lanServer.otherPlayers": "Innstillinger for andre spillere", "lanServer.port": "Portnummer", "lanServer.port.invalid": "Ikke en gyldig port.\nLa tekstfeltet stå blankt eller oppgi et nummer mellom 1024 og 65535.", "lanServer.port.invalid.new": "Ikke en gyldig port.\nLa tekstfeltet stå blankt eller oppgi et nummer mellom %s og %s.", "lanServer.port.unavailable": "Port ikke tilgjengelig.\nLa tekstfeltet stå blankt eller oppgi et annet nummer mellom 1024 og 65535.", "lanServer.port.unavailable.new": "Port ikke tilgjengelig.\nLa tekstfeltet stå blankt eller oppgi et annet nummer mellom %s og %s.", "lanServer.scanning": "<PERSON><PERSON><PERSON> etter spill på ditt lokale nettverk", "lanServer.start": "Start LAN-verden", "lanServer.title": "LAN-verden", "language.code": "nob_NO", "language.name": "Norsk bokmål", "language.region": "Norge", "lectern.take_book": "Ta bok", "loading.progress": "%s%%", "mco.account.privacy.info": "Les mer om Mojang og personvernlover", "mco.account.privacy.info.button": "Les mer om GDPR", "mco.account.privacy.information": "Mojang implementerer visse prosedyrer for å beskytte barn og deres personvern, inkludert overholdelse av den amerikanske loven om beskyttelse av barns personvern (COPPA) og EUs personvernforordning (GDPR).\n\nDu må kanskje få tillatelse fra en foresatt før du får tilgang til Realms-kontoen din.", "mco.account.privacyinfo": "Mojang implementerer visse prosedyrer for å beskytte barn og deres personvern, inkludert overholdelse av den amerikanske loven om beskyttelse av barns personvern (COPPA) og EUs personvernforordning (GDPR).\n\nDu må kanskje få tillatelse fra en foresatt før du får tilgang til Realms-kontoen din.\n\nHvis du har en eldre Minecraft-konto (du logger inn med brukernavnet ditt), må du overføre kontoen din til en Mojang-konto for å få tilgang til Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON> konto", "mco.activity.noactivity": "Ingen aktivitet de siste %s dagene", "mco.activity.title": "Spilleraktivitet", "mco.backup.button.download": "Last ned seneste", "mco.backup.button.reset": "Tilbakestill verden", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.upload": "Last opp verden", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON>", "mco.backup.entry": "Reservekopi (%s)", "mco.backup.entry.description": "Beskrivelse", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pakker", "mco.backup.entry.gameDifficulty": "Vanskelighetsgrad", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Spillserverversjon", "mco.backup.entry.name": "Navn", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "Malnavn", "mco.backup.entry.undefined": "Udefinert endring", "mco.backup.entry.uploaded": "Lastet opp", "mco.backup.entry.worldType": "Verdenstype", "mco.backup.generate.world": "<PERSON><PERSON> verden", "mco.backup.info.title": "<PERSON><PERSON><PERSON> siden siste lagring", "mco.backup.narration": "Reservekopi fra %s", "mco.backup.nobackups": "Denne Realmen har ingen gjeldende reservekopier.", "mco.backup.restoring": "Gjenoppretter din Realm", "mco.backup.unknown": "UKJENT", "mco.brokenworld.download": "Last ned", "mco.brokenworld.downloaded": "<PERSON>et ned", "mco.brokenworld.message.line1": "Vennligst start på nytt eller velg en annen verden.", "mco.brokenworld.message.line2": "Du kan også velge å laste ned verdenen til enkeltspiller modus.", "mco.brokenworld.minigame.title": "Dette minispillet er ikke lenger støttet", "mco.brokenworld.nonowner.error": "Vennligst påvent at Realm-eieren tilbakestiller verdenen", "mco.brokenworld.nonowner.title": "Verden er utdatert", "mco.brokenworld.play": "Spill", "mco.brokenworld.reset": "Tilbakestill", "mco.brokenworld.title": "<PERSON> nåværende verden støttes ikke lenger", "mco.client.incompatible.msg.line1": "K<PERSON><PERSON> din er ikke kompatibel med Realms.", "mco.client.incompatible.msg.line2": "Vennligst bruk den nyeste versjonen av Minecraft.", "mco.client.incompatible.msg.line3": "Realms er ikke kompatibel med utviklingsversjoner.", "mco.client.incompatible.title": "Klienten er ikke kompatibel!", "mco.client.outdated.stable.version": "<PERSON> (%s) er ikke forenelig med Realms.\n\nVennligst bruk den seneste versjonen av Minecraft.", "mco.client.unsupported.snapshot.version": "<PERSON> (%s) er ikke forenelig med Realms.\n\nRealms er ikke tilgjengelig i denne utviklingsversjonen.", "mco.compatibility.downgrade": "Nedgrader", "mco.compatibility.downgrade.description": "Denne verdenen spiltes sist på i versjon %s; du er på versjon %s. Nedgradering av en verden kan forårsake korrupsjon - vi kan ikke garantere at den vil kunne lastes inn eller fungere.\n\nEn reservekopi av verdenen din vil lagres under «Verdensreservekopier». Vennligst gjenopprett verdenen din derfra om nødvendig.", "mco.compatibility.incompatible.popup.title": "Uforenelig versjon", "mco.compatibility.incompatible.releaseType.popup.message": "Verdenen du prøver å spille på er uforenelig med versjonen du er på.", "mco.compatibility.incompatible.series.popup.message": "Denne verdenen spiltes sist i versjon %s; du er på versjon %s.\n\nDisse grenene er ikke forenelige med hverandre. En ny verden trengs for å spille på denne versjonen.", "mco.compatibility.unverifiable.message": "Versjonen denne verdenen sist spiltes på kunne ikke fastsettes. Hvis verdenen oppgraderes eller nedgraderes, lagres en reservekopi automatisk i «Verdensreservekopier».", "mco.compatibility.unverifiable.title": "Forenlighet kan ikke bekreftes", "mco.compatibility.upgrade": "Oppgrader", "mco.compatibility.upgrade.description": "Denne verdenen spiltes sist på i versjon %s; du er nå på versjon %s.\n\nEn reservekopi av verdenen din vil lagres i «Verdensreservekopier».\n\nGjenopprett verdenen din derfra om nødvendig.", "mco.compatibility.upgrade.friend.description": "Denne verdenen ble sist spilt på i versjon %s; du er nå i versjon %s.\n\nEn reservekopi av verdenen vil lagres i mappen \"World Backups\".\n\nEieren av Realmen kan gjenopprette verdenen om nødvendig.", "mco.compatibility.upgrade.title": "Vil du virkelig oppgradere denne verdenen?", "mco.configure.current.minigame": "Gjeldende", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for spiller er midlertidig slått av", "mco.configure.world.backup": "Verdenskopier", "mco.configure.world.buttons.activity": "Spilleraktivitet", "mco.configure.world.buttons.close": "Lukk Realm foreløpig", "mco.configure.world.buttons.delete": "<PERSON><PERSON>", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Innstillinger", "mco.configure.world.buttons.invite": "Inviter spiller", "mco.configure.world.buttons.moreoptions": "Flere alternativer", "mco.configure.world.buttons.newworld": "<PERSON><PERSON> verden", "mco.configure.world.buttons.open": "Gjenåpn Realm", "mco.configure.world.buttons.options": "Verdensvalg", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Velg region...", "mco.configure.world.buttons.resetworld": "Tilbakestill verden", "mco.configure.world.buttons.save": "Lagre", "mco.configure.world.buttons.settings": "Innstillinger", "mco.configure.world.buttons.subscription": "Abonnement", "mco.configure.world.buttons.switchminigame": "<PERSON>tt minispill", "mco.configure.world.close.question.line1": "Du kan lukke Realmen din foreløpig, og hindre spilling mens du stiller den inn. Åpn den opp igjen når du er klar.\n\nDette avslutter ikke Realms-abonnementet ditt.", "mco.configure.world.close.question.line2": "Er du sikker på at du vil fortsette?", "mco.configure.world.close.question.title": "Må du gjøre endringer uten å skape forstyrrelser?", "mco.configure.world.closing": "Lukker Realmen foreløpig...", "mco.configure.world.commandBlocks": "Kommandoblokker", "mco.configure.world.delete.button": "Slett Realm", "mco.configure.world.delete.question.line1": "Realmen din slettes uomgjørlig", "mco.configure.world.delete.question.line2": "Er du sikker på at du vil fortsette?", "mco.configure.world.description": "Realmbeskrivelse", "mco.configure.world.edit.slot.name": "Verdensnavn", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON>en innstillinger er deaktivert fordi din nåværende verden er et eventyrnivå", "mco.configure.world.edit.subscreen.experience": "<PERSON>en innstillinger er deaktivert fordi din nåværende verden er et opplevelseskart", "mco.configure.world.edit.subscreen.inspiration": "<PERSON>en innstillinger er deaktivert fordi din nåværende verden er et inspirasjonskart", "mco.configure.world.forceGameMode": "Påtving spillmodus", "mco.configure.world.invite.narration": "Du har %s ny(e) invitasjon(er)", "mco.configure.world.invite.profile.name": "Navn", "mco.configure.world.invited": "In<PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Inviterte (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON> bruker", "mco.configure.world.invites.ops.tooltip": "Operatør", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Forlater du denne Realmen ser du den ikke igje<PERSON>, om ikke du inviteres på nytt", "mco.configure.world.leave.question.line2": "Er du sikker på at du vil fortsette?", "mco.configure.world.loading": "Laster inn Realm", "mco.configure.world.location": "Plassering", "mco.configure.world.minigame": "Nåværende: %s", "mco.configure.world.name": "Realmnavn", "mco.configure.world.opening": "<PERSON><PERSON><PERSON>en...", "mco.configure.world.players.error": "En spiller med dette navnet finnes ikke", "mco.configure.world.players.inviting": "Inviterer spiller...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Foretrukket region", "mco.configure.world.region_preference.title": "Valgt foretrukket region", "mco.configure.world.reset.question.line1": "Verdenen din vil bli regenerert og din nåværende verden vil bli tapt for alltid", "mco.configure.world.reset.question.line2": "Er du sikker på at du vil fortsette?", "mco.configure.world.resourcepack.question": "Du må ha en tilpasset ressurspakke for å spille på denne Realmen\n\nVil du laste den ned, så spille?", "mco.configure.world.resourcepack.question.line1": "Du må ha en tilpasset ressurspakke for å spille på denne Realmen", "mco.configure.world.resourcepack.question.line2": "Vil du laste den ned og spille?", "mco.configure.world.restore.download.question.line1": "Verdenen vil bli lastet ned og lagt til i dine enkeltspiller-verdener.", "mco.configure.world.restore.download.question.line2": "Vil du fortsette?", "mco.configure.world.restore.question.line1": "Verdenen din vil bli gjenopprettet til dato '%s' (%s)", "mco.configure.world.restore.question.line2": "Er du sikker på at du vil fortsette?", "mco.configure.world.settings.expired": "Du kan ikke endre innstillingene til en utløpt Realm", "mco.configure.world.settings.title": "Innstillinger", "mco.configure.world.slot": "Verden %s", "mco.configure.world.slot.empty": "<PERSON>", "mco.configure.world.slot.switch.question.line1": "<PERSON><PERSON> din bytter til en annen verden", "mco.configure.world.slot.switch.question.line2": "Er du sikker på at du vil fortsette?", "mco.configure.world.slot.tooltip": "<PERSON>tt til verden", "mco.configure.world.slot.tooltip.active": "Bli med", "mco.configure.world.slot.tooltip.minigame": "Bytt til minispill", "mco.configure.world.spawnAnimals": "<PERSON>n dyr", "mco.configure.world.spawnMonsters": "<PERSON><PERSON> monstre", "mco.configure.world.spawnNPCs": "<PERSON><PERSON>", "mco.configure.world.spawnProtection": "Startpunktsbeskyttelse", "mco.configure.world.spawn_toggle.message": "Skrus dette valget av fjernes alle enheter som finnes av den typen", "mco.configure.world.spawn_toggle.message.npc": "Sk<PERSON> dette valget av fjernes alle enheter som finnes av den typen, som bygdinger", "mco.configure.world.spawn_toggle.title": "<PERSON><PERSON><PERSON>!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "dag", "mco.configure.world.subscription.days": "dager", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Forleng abonnement", "mco.configure.world.subscription.less_than_a_day": "<PERSON>re enn én dag", "mco.configure.world.subscription.month": "må<PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Fornyes automatisk om", "mco.configure.world.subscription.recurring.info": "<PERSON>ringer gjort på ditt Realms-abonnement, som tidsforlengelse eller å slå av gjentagende fakturering, trer ikke i kraft før neste faktureringsdato.", "mco.configure.world.subscription.remaining.days": "%1$s dag(er)", "mco.configure.world.subscription.remaining.months": "%1$s måned(er)", "mco.configure.world.subscription.remaining.months.days": "%1$s måned(er), %2$s dag(er)", "mco.configure.world.subscription.start": "Startdato", "mco.configure.world.subscription.tab": "Abonnement", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON> i<PERSON>n", "mco.configure.world.subscription.title": "Ditt abonnement", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON><PERSON> verden", "mco.configure.world.switch.slot.subtitle": "Denne verdenen er tom; velg hvordan verdenen din lages", "mco.configure.world.title": "Konfigurer Realm:", "mco.configure.world.uninvite.player": "Er du sikker på at du vil trekke tilbake invitasjonen til '%s'?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> du sikker på at du vil trekke invitasjonen tilbake", "mco.configure.worlds.title": "<PERSON><PERSON>", "mco.connect.authorizing": "Logger inn...", "mco.connect.connecting": "<PERSON><PERSON> til <PERSON>en...", "mco.connect.failed": "Lyktes ikke med å koble til Realm", "mco.connect.region": "Serverregion: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Du må angi et navn!", "mco.create.world.failed": "Mislyktes i å skape verden!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON><PERSON> verden...", "mco.create.world.skip": "<PERSON><PERSON> over", "mco.create.world.subtitle": "<PERSON><PERSON> <PERSON><PERSON>, velg hvilken verden som skal legges inn på din nye Realm", "mco.create.world.wait": "Skaper Realmen...", "mco.download.cancelled": "Nedlasting a<PERSON><PERSON><PERSON><PERSON>", "mco.download.confirmation.line1": "Verdenen du skal laste ned er større enn %s", "mco.download.confirmation.line2": "Du vil ikke kunne laste opp denne verdenen til Realmen din igjen", "mco.download.confirmation.oversized": "Verdenen du skal laste ned er større enn %s\n\nDu vil ikke kunne laste opp denne verdenen til Realmen din igjen", "mco.download.done": "Nedlasting fullført", "mco.download.downloading": "Laster ned", "mco.download.extracting": "Pakker ut", "mco.download.failed": "Nedlasting mislyktes", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON> ne<PERSON>", "mco.download.resourcePack.fail": "Lyktes ikke med å laste ned ressurspakke!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Laster ned seneste verden", "mco.error.invalid.session.message": "Vennligst prøv å starte Minecraft på nytt", "mco.error.invalid.session.title": "Ugyldig økt", "mco.errorMessage.6001": "Klient utdatert", "mco.errorMessage.6002": "Betingelsene er ikke akseptert", "mco.errorMessage.6003": "Nedlastingsgrense nådd", "mco.errorMessage.6004": "Opplastingsgrense nådd", "mco.errorMessage.6005": "<PERSON>n låst", "mco.errorMessage.6006": "Verden er utdatert", "mco.errorMessage.6007": "<PERSON>ruker i for mange Realmer", "mco.errorMessage.6008": "Ugyldig Realm-navn", "mco.errorMessage.6009": "Ugyldig Realm-beskrivelse", "mco.errorMessage.connectionFailure": "En feil oppsto, vennligst prøv igjen senere.", "mco.errorMessage.generic": "En feil oppstod: ", "mco.errorMessage.initialize.failed": "Mislyktes i å initialisere Realm", "mco.errorMessage.noDetails": "Ingen detaljer gitt om feilen", "mco.errorMessage.realmsService": "En feil oppstod (%s):", "mco.errorMessage.realmsService.configurationError": "En uforventet feil oppsto under endring av verdensinnstillinger", "mco.errorMessage.realmsService.connectivity": "<PERSON>nne ikke koble til Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kunne ikke sjekke forenlig versjon, fikk respons: %s", "mco.errorMessage.retry": "Prøv operasjon på nytt", "mco.errorMessage.serviceBusy": "Realms har det travelt for øyeblikket.\nVennligst prøv å koble til din Realm på nytt om et par minutter.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Inviterte spiller %s", "mco.invites.button.accept": "God<PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "Ingen ventende invitasjoner!", "mco.invites.pending": "Ny(e) invitasjon(er)!", "mco.invites.title": "Ventende invitasjoner", "mco.minigame.world.changeButton": "Velg et annet minispill", "mco.minigame.world.info.line1": "Dette vil midlertidig erstatte verdenen din med et minispill!", "mco.minigame.world.info.line2": "Du kan returnere til den originale verden din senere uten å miste noe.", "mco.minigame.world.noSelection": "Vennligst velg noe", "mco.minigame.world.restore": "Avslutter minispill...", "mco.minigame.world.restore.question.line1": "Minispillet tar slutt og Realmen din gjenopprettes.", "mco.minigame.world.restore.question.line2": "Er du sikker på at du vil fortsette?", "mco.minigame.world.selected": "Valgt minispill:", "mco.minigame.world.slot.screen.title": "Bytter verden...", "mco.minigame.world.startButton": "<PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Starter minispill...", "mco.minigame.world.stopButton": "Avslutt minispill", "mco.minigame.world.switch.new": "Velg et annet minispill?", "mco.minigame.world.switch.title": "<PERSON>tt minispill", "mco.minigame.world.title": "Bytt Realm til minispill", "mco.news": "Realms-nyheter", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON>f<PERSON>r nå", "mco.notification.transferSubscription.message": "Java-Realms-abonnement<PERSON> flytter til Microsoft Store. La ikke abonnementet ditt løpe ut!\nOverfør det nå og få 30 dager med Realms gratis.\nGå til profilen din på minecraft.net for å overføre abonnementet ditt.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON><PERSON> lenke", "mco.notification.visitUrl.message.default": "Vennligst besøk lenken under", "mco.onlinePlayers": "Tilkoblede spillere", "mco.play.button.realm.closed": "Realm er stengt", "mco.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Eventyr", "mco.reset.world.experience": "Opplevelser", "mco.reset.world.generate": "<PERSON><PERSON> verden", "mco.reset.world.inspiration": "Inspirasjon", "mco.reset.world.resetting.screen.title": "Tilbakestiller verden...", "mco.reset.world.seed": "<PERSON><PERSON><PERSON><PERSON> (valgfritt)", "mco.reset.world.template": "Verdensmaler", "mco.reset.world.title": "Tilbakestill verden", "mco.reset.world.upload": "Last opp verden", "mco.reset.world.warning": "<PERSON>te vil bytte ut din Realms gjeldende verden", "mco.selectServer.buy": "<PERSON><PERSON><PERSON><PERSON> en Realm!", "mco.selectServer.close": "Lukk", "mco.selectServer.closed": "Lukket Realm", "mco.selectServer.closeserver": "Lukk Realm", "mco.selectServer.configure": "Konfigurer realm", "mco.selectServer.configureRealm": "Konfigurer Realm", "mco.selectServer.create": "<PERSON><PERSON><PERSON>t <PERSON>", "mco.selectServer.create.subtitle": "Velg hvilken verden som skal legges inn på din nye Realm", "mco.selectServer.expired": "Utløpt Realm", "mco.selectServer.expiredList": "Abonnementet ditt er utløpt", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "Prøveversjonen har utløpt", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON><PERSON> om én dag", "mco.selectServer.expires.days": "Utløper om %s dager", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON><PERSON> snart", "mco.selectServer.leave": "Forlat Realm", "mco.selectServer.loading": "Laster inn <PERSON><PERSON>e", "mco.selectServer.mapOnlySupportedForVersion": "Dette nivået støttes ikke i %s", "mco.selectServer.minigame": "Minispill:", "mco.selectServer.minigameName": "Minispill: %s", "mco.selectServer.minigameNotSupportedInVersion": "Kan ikke spille dette minispillet i %s", "mco.selectServer.noRealms": "Det virker ikke som om du har en Realm. Legg til en Realm for å spille sammen med vennene dine.", "mco.selectServer.note": "Husk:", "mco.selectServer.open": "<PERSON><PERSON>", "mco.selectServer.openserver": "Åpn Realm", "mco.selectServer.play": "Spill", "mco.selectServer.popup": "Realms er en trygg og enkel måte å nyte en Minecraft-verden på nett med opptil ti venner samtidig. Den støtter massevis av minispill og tilpassede verdener! Kun eieren av en Realm må betale.", "mco.selectServer.purchase": "<PERSON><PERSON> til <PERSON>", "mco.selectServer.trial": "Ta en prøve!", "mco.selectServer.uninitialized": "Klikk for å starte din nye Realm!", "mco.snapshot.createSnapshotPopup.text": "Du oppretter nå en gratis Snapshot-Realm som kobles til ditt betalte abonnement. Denne nye Snapshot-Realmen er åpen så lenge det betalte abonnementet gjelder. Din betalte Realm påvirkes ikke.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON><PERSON>-Realm?", "mco.snapshot.creating": "<PERSON><PERSON><PERSON>-Realm...", "mco.snapshot.description": "Koblet med %s", "mco.snapshot.friendsRealm.downgrade": "Du må være på versjon %s for å bli med på denne <PERSON>en", "mco.snapshot.friendsRealm.upgrade": "%s må oppgradere Realmen sin før du kan spille på denne versjonen", "mco.snapshot.paired": "<PERSON><PERSON>-<PERSON>en er koblet med \"%s\"", "mco.snapshot.parent.tooltip": "Bruk den seneste utgaven av Minecraft for å spille på denne Realmen", "mco.snapshot.start": "Start gratis <PERSON>-Realm", "mco.snapshot.subscription.info": "Dette er en Snapshot-Realm koblet til abonnementet ditt til Realmen «%s». Den vil forbli åpen så lenge den sammenkoblede Realmen er åpen.", "mco.snapshot.tooltip": "Bruk Snapshot-Realms for å sniktitte på kommende versjoner av Minecraft, som kan inneholde nye virkemåter og andre endringer.\n\nDu kan finne dine vanlige Realmer i hovedutgaven av spillet.", "mco.snapshotRealmsPopup.message": "Realms er nå tilgjengelig i Snapshots fra og med Snapshot 23w41a. Hvert Realms-abonnement kommer med en gratis Snapshot-Realm adskilt fra din vanlige Java-Realm!", "mco.snapshotRealmsPopup.title": "Realms nå tilgjengelig i Snapshots", "mco.snapshotRealmsPopup.urlText": "<PERSON> ut mer", "mco.template.button.publisher": "Utgiver", "mco.template.button.select": "Velg", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "Verdensmal", "mco.template.info.tooltip": "Utgiverens nettside", "mco.template.name": "Mal", "mco.template.select.failure": "<PERSON>i kunne ikke hente listen over innhold for denne kate<PERSON>.\nSjekk internettilkoblingen din eller prøv på nytt senere.", "mco.template.select.narrate.authors": "Forfattere: %s", "mco.template.select.narrate.version": "versjon %s", "mco.template.select.none": "Oi sann! Det ser ut som om denne kategorien er tom for øyeblikket.\nVær vennlig og sjekk siden senere for nytt innhold, eller %s om du er en skaper.", "mco.template.select.none.linkTitle": "tenk på å sende inn noe selv", "mco.template.title": "Verdensmaler", "mco.template.title.minigame": "Minispill", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.agree": "<PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON> enig", "mco.terms.sentence.1": "<PERSON><PERSON> sam<PERSON> til Minecraft Realms", "mco.terms.sentence.2": "Vilkår for bruk", "mco.terms.title": "Realms Vilkår for bruk", "mco.time.daysAgo": "%1$s dag(er) siden", "mco.time.hoursAgo": "%1$s time(r) siden", "mco.time.minutesAgo": "%1$s minutt(er) siden", "mco.time.now": "akkurat nå", "mco.time.secondsAgo": "%1$s sekund(er) siden", "mco.trial.message.line1": "Vil du ha en egen Realm?", "mco.trial.message.line2": "<PERSON><PERSON>k her for mer informasjon!", "mco.upload.button.name": "Last opp", "mco.upload.cancelled": "Opplastingen ble avbrutt", "mco.upload.close.failure": "<PERSON>nne ikke lukke Realmen din, vennligst prøv igjen senere", "mco.upload.done": "Opplasting fullført", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Opplastningsfeil! (%s)", "mco.upload.failed.too_big.description": "Den valgte verdenen er for stor. Høyeste tillatte størrelse er %s.", "mco.upload.failed.too_big.title": "Verden for stor", "mco.upload.hardcore": "Verdener i hardbarket modus kan ikke lastes opp!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Forbereder verdenen din", "mco.upload.select.world.none": "Ingen enkeltspillerverdener funnet!", "mco.upload.select.world.subtitle": "Vennligst velg en enkeltspillerverden til å laste opp", "mco.upload.select.world.title": "Last opp verden", "mco.upload.size.failure.line1": "'%s' er for stor!", "mco.upload.size.failure.line2": "Den er %s. Den maksimalt tillatte størrelsen er %s.", "mco.upload.uploading": "Laster opp '%s'", "mco.upload.verifying": "Verifiserer verdenen din", "mco.version": "Versjon: %s", "mco.warning": "<PERSON><PERSON><PERSON>!", "mco.worldSlot.minigame": "Minispill", "menu.custom_options": "Egendefinerte innstillinger...", "menu.custom_options.title": "Egendefinerte innstillinger", "menu.custom_options.tooltip": "Merk: Egendefinerte innstillinger forsørges av tredjepartsservere og/eller -innhold.\nPass på!", "menu.custom_screen_info.button_narration": "Dette er en egendefinert skjerm. Finn ut mer.", "menu.custom_screen_info.contents": "Innholdet på denne skjermen avgjøres av tredjepartsservere og -nivåer som verken eies, styres eller overvåkes av Mojang Studios eller Microsoft.\n\nPass på! Vær alltid forsiktig når du følger lenker og gi aldri bort privat informasjon, deriblant innloggingsdetaljer.\n\nOm denne skjermen hindrer deg fra å spille, kan du også koble fra denne serveren ved å bruke knappen under.", "menu.custom_screen_info.disconnect": "Egendefinert skjerm avvist", "menu.custom_screen_info.title": "Merknad om egendefinerte skjermer", "menu.custom_screen_info.tooltip": "<PERSON>te er en egendefinert skjerm. Klikk her for å finne ut mer.", "menu.disconnect": "Kobl fra", "menu.feedback": "Tilbakemelding…", "menu.feedback.title": "Tilbakemelding", "menu.game": "S<PERSON>ll<PERSON><PERSON>", "menu.modded": " (<PERSON><PERSON><PERSON>)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Innstillinger...", "menu.paused": "<PERSON><PERSON><PERSON> satt på pause", "menu.playdemo": "<PERSON><PERSON><PERSON> demoverden", "menu.playerReporting": "Spillerrapportering", "menu.preparingSpawn": "Forbereder startområde: %s%%", "menu.quick_actions": "Handlinger for hånd...", "menu.quick_actions.title": "<PERSON><PERSON> for hånd", "menu.quit": "Avslutt spill", "menu.reportBugs": "Meld inn feil", "menu.resetdemo": "Tilbakestill prøveverden", "menu.returnToGame": "Fortsett spill", "menu.returnToMenu": "Lagre og gå til hovedmenyen", "menu.savingChunks": "<PERSON><PERSON><PERSON> stykker", "menu.savingLevel": "Lagrer verden", "menu.sendFeedback": "Gi tilbakemelding", "menu.server_links": "<PERSON><PERSON><PERSON>…", "menu.server_links.title": "<PERSON><PERSON><PERSON>", "menu.shareToLan": "<PERSON><PERSON><PERSON> for LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.working": "Arbeider...", "merchant.deprecated": "<PERSON><PERSON><PERSON><PERSON> fyller opp forr<PERSON>det sitt opp til to ganger om dagen.", "merchant.level.1": "Nybegynner", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON>", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Handler", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Stig av med %1$s", "multiplayer.applyingPack": "Laster inn ressurspakke", "multiplayer.confirm_command.parse_errors": "Du prøver å utføre en ukjent eller ugyldig kommando.\nEr du sikker?\nKommando: %s", "multiplayer.confirm_command.permissions_required": "Du prøver å utføre en kommando som betinger utvidede tillatelser.\nDette kan påvirke spillet ditt negativt.\nEr du sikker?\nKommando: %s", "multiplayer.confirm_command.title": "Bekreft utførsel av kommando", "multiplayer.disconnect.authservers_down": "Godkjenningsservere er nede. Vennligst prøv igjen senere, beklager!", "multiplayer.disconnect.bad_chat_index": "Oppdaget overhoppet eller omstokket nettpratsmelding fra serveren", "multiplayer.disconnect.banned": "Du er bannlyst fra denne serveren", "multiplayer.disconnect.banned.expiration": "\nBannlysingen din blir opphevet %s", "multiplayer.disconnect.banned.reason": "Du er bannlyst fra denne serveren. \nGrunn: %s", "multiplayer.disconnect.banned_ip.expiration": "\nBannlysningen din oppheves %s", "multiplayer.disconnect.banned_ip.reason": "IP-adressen din er bannlyst fra denne serveren.\nGrunn: %s", "multiplayer.disconnect.chat_validation_failed": "Feil under validering av nettprat", "multiplayer.disconnect.duplicate_login": "Du logget inn fra et annet sted", "multiplayer.disconnect.expired_public_key": "Utløpt offentlig nøkkel vedlagt profil. Sjekk at systemtiden din er synkronisert, og prøv deretter å starte spillet på nytt.", "multiplayer.disconnect.flying": "Flyging er ikke aktivert på denne serveren", "multiplayer.disconnect.generic": "Frakoblet", "multiplayer.disconnect.idling": "Du har vært inaktiv for lenge!", "multiplayer.disconnect.illegal_characters": "Ugyldige tegn i nettpraten", "multiplayer.disconnect.incompatible": "Inkompatibel klient! Vennligst bruk %s", "multiplayer.disconnect.invalid_entity_attacked": "Prøver å angripe en ugyldig enhet", "multiplayer.disconnect.invalid_packet": "Mottok en ugyldig nettverkspakke fra serveren", "multiplayer.disconnect.invalid_player_data": "Ugyld<PERSON>erdata", "multiplayer.disconnect.invalid_player_movement": "Ugyldig pakke for flytting av spiller mottatt", "multiplayer.disconnect.invalid_public_key_signature": "Ugyldig signatur på profilens offentlige nøkkel. Prøv å starte spillet på nytt.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ugyldig signatur på profilens offentlige nøkkel.\nPrøv å starte spillet på nytt.", "multiplayer.disconnect.invalid_vehicle_movement": "Ugyldig pakke for flytting av kjøretøy mottatt", "multiplayer.disconnect.ip_banned": "<PERSON> har blitt IP-bannlyst fra denne serveren", "multiplayer.disconnect.kicked": "Kastet ut av en operatør", "multiplayer.disconnect.missing_tags": "<PERSON>t mangelfullt sett av tagger ble mottatt fra serveren.\nVennligst kontakt serveroperatøren.", "multiplayer.disconnect.name_taken": "Dette navnet er allerede tatt", "multiplayer.disconnect.not_whitelisted": "Du er ikke hvitelistet på denne serveren!", "multiplayer.disconnect.out_of_order_chat": "Mottok nettpratspakke i feil rekkefølge. Har systemtiden din blitt endret?", "multiplayer.disconnect.outdated_client": "Uforenelig klient! Vennligst bruk %s", "multiplayer.disconnect.outdated_server": "Uforenelig klient! Vennligst bruk %s", "multiplayer.disconnect.server_full": "Serveren er full!", "multiplayer.disconnect.server_shutdown": "Server lukket", "multiplayer.disconnect.slow_login": "Innlogningen tok for lang tid", "multiplayer.disconnect.too_many_pending_chats": "For mange uanerkjente nettpratsmeldinger", "multiplayer.disconnect.transfers_disabled": "Serveren tar ikke imot overfø<PERSON>ler", "multiplayer.disconnect.unexpected_query_response": "Uforventet egendefinert data fra klienten", "multiplayer.disconnect.unsigned_chat": "Mottok nettpratspakke med manglende eller ugyldig signatur.", "multiplayer.disconnect.unverified_username": "Kunne ikke bekrefte brukernavn!", "multiplayer.downloadingStats": "<PERSON><PERSON> statistikk...", "multiplayer.downloadingTerrain": "Laster inn terreng...", "multiplayer.lan.server_found": "Ny server funnet: %s", "multiplayer.message_not_delivered": "Kan ikke sende melding. Sjekk serverloggene: %s", "multiplayer.player.joined": "%s logget seg på", "multiplayer.player.joined.renamed": "%s (tidligere kjent som %s) ble med i spillet", "multiplayer.player.left": "%s forlot spillet", "multiplayer.player.list.hp": "%sliv", "multiplayer.player.list.narration": "Tilkoblede spillere: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON> krever en tilpasset ressurspakke", "multiplayer.requiredTexturePrompt.line1": "Denne serveren krever bruk av en tilpasset ressurspakke.", "multiplayer.requiredTexturePrompt.line2": "Du vil kobles fra serveren hvis du avviser denne ressurspakken.", "multiplayer.socialInteractions.not_available": "Samspilleroversikten er kun tilgjengelig i flerspillerverdener", "multiplayer.status.and_more": "... og %s til ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Kan ikke koble til server", "multiplayer.status.cannot_resolve": "Kan ikke finne vertsnavn", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Inkompatibel versjon!", "multiplayer.status.motd.narration": "Dagens budskap: %s", "multiplayer.status.no_connection": "(ingen til<PERSON>)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "<PERSON><PERSON> nett", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisekunder", "multiplayer.status.pinging": "Pinger...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s av %s spillere tilkoblet", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "Statusforespørse<PERSON> har blitt hå<PERSON>t", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Mottok uspurt status", "multiplayer.status.version.narration": "Serverversjon: %s", "multiplayer.stopSleeping": "<PERSON><PERSON> sengen", "multiplayer.texturePrompt.failure.line1": "Ressurspakke fra server kunne ikke lastes inn", "multiplayer.texturePrompt.failure.line2": "Funksjonaliteter som krever tilleggsressurser vil kanskje ikke virke som forventet", "multiplayer.texturePrompt.line1": "Denne serveren anbefaler bruk av en tilpasset ressurspakke.", "multiplayer.texturePrompt.line2": "Ønsker du å laste ned og installere den automagisk?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMelding fra server:\n%s", "multiplayer.title": "Spill flerspiller", "multiplayer.unsecureserver.toast": "<PERSON><PERSON><PERSON> sendt på denne serveren kan endres på og vil muligens ikke gjenspeile det opprinnelige budskapet", "multiplayer.unsecureserver.toast.title": "Nettprat kan ikke verifiseres", "multiplayerWarning.check": "Ikke vis denne skjermen igjen", "multiplayerWarning.header": "Advarsel: Nettspill fra tredjeparter", "multiplayerWarning.message": "Advarsel: Nettspill tilbydes av tredjepartsservere som hverken eies, styres eller overvåkes av Mojang Studios eller Microsoft. Når du spiller på nett kan du utsettes for umodererte nettpratsmeldinger eller andre typer brukerlagd innhold som ikke passer for alle.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Knapp: %s", "narration.button.usage.focused": "Trykk Enter for å aktivere", "narration.button.usage.hovered": "Venstreklikk for å aktivere", "narration.checkbox": "Avkryssingsboks: %s", "narration.checkbox.usage.focused": "Trykk Enter for å veksle", "narration.checkbox.usage.hovered": "Venstreklikk for å veksle", "narration.component_list.usage": "Gå til neste element med Tab", "narration.cycle_button.usage.focused": "Trykk Enter for å bytte til %s", "narration.cycle_button.usage.hovered": "Venstreklikk for å bytte til %s", "narration.edit_box": "Rediger boks: %s", "narration.item": "Gjenstand: %s", "narration.recipe": "Oppskrift på %s", "narration.recipe.usage": "Venstreklikk for å velge", "narration.recipe.usage.more": "Høyreklikk for å vise flere oppskrifter", "narration.selection.usage": "Trykk på pil opp eller ned for å bytte til et annet punkt", "narration.slider.usage.focused": "Trykk på venstre eller høyre piltast for å endre verdi", "narration.slider.usage.hovered": "<PERSON>a glideren for å endre verdi", "narration.suggestion": "Valgte forslag %s av %s: %s", "narration.suggestion.tooltip": "Valgte forslag %s av %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Trykk på Tab for å gå til det neste forslaget", "narration.suggestion.usage.cycle.hidable": "Trykk på Tab for å gå til neste forslag, el<PERSON> for å gå ut av forslagene", "narration.suggestion.usage.fill.fixed": "Trykk på Tab for å bruke forslag", "narration.suggestion.usage.fill.hidable": "Trykk på Tab for å bruke forslag, el<PERSON> for å gå ut av forslagene", "narration.tab_navigation.usage": "Trykk på Ctrl og Tab for å bytte mellom faner", "narrator.button.accessibility": "Tilgjengelighet", "narrator.button.difficulty_lock": "Vanskelighetslås", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "Språk", "narrator.controls.bound": "%s er bundet til %s", "narrator.controls.reset": "Nullstill %s knapp", "narrator.controls.unbound": "%s er ikke bundet", "narrator.joining": "Blir med", "narrator.loading": "Laster: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Valgte listerad %s av %s", "narrator.position.object_list": "Valgte radelement %s av %s", "narrator.position.screen": "Skjermelement %s av %s", "narrator.position.tab": "Valgte fane %s av %s", "narrator.ready_to_play": "<PERSON>lar til å spille", "narrator.screen.title": "Hovedmeny", "narrator.screen.usage": "Velg element med peker eller Tab", "narrator.select": "Valgt: %s", "narrator.select.world": "Valgt: %s, sist spilt: %s, %s, %s, versjon: %s", "narrator.select.world_info": "Valgte %s, sist spilt på: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON> deaktivert", "narrator.toast.enabled": "Forteller aktivert", "optimizeWorld.confirm.description": "Dette vil prøve å optimalisere verdenen din, ved å sikre seg at all data blir lagret i det nyeste spillformatet. Dette kan ta veldig lang tid avhengig av verdenen din. Når verdenen din er ferdig kan den kjøre kjappere, men vil ikke lenger være kompatibel med eldre versjoner av spillet. Er du sikker på, at du vil fortsette?", "optimizeWorld.confirm.proceed": "Lag reservekopi og optimer", "optimizeWorld.confirm.title": "<PERSON><PERSON><PERSON> verden", "optimizeWorld.info.converted": "Stykker oppgraderte: %s", "optimizeWorld.info.skipped": "Stykker hoppet over: %s", "optimizeWorld.info.total": "Stykker totalt: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Teller stykker...", "optimizeWorld.stage.failed": "Mislyktes! :(", "optimizeWorld.stage.finished": "Fullfører...", "optimizeWorld.stage.finished.chunks": "<PERSON><PERSON><PERSON>rer oppgradering av stykker...", "optimizeWorld.stage.finished.entities": "<PERSON><PERSON><PERSON>rer oppgradering av enheter...", "optimizeWorld.stage.finished.poi": "Fullfører oppgradering av interessepunkter...", "optimizeWorld.stage.upgrading": "Oppgraderer alle stykker...", "optimizeWorld.stage.upgrading.chunks": "Oppgraderer alle stykker...", "optimizeWorld.stage.upgrading.entities": "Oppgraderer alle enheter...", "optimizeWorld.stage.upgrading.poi": "Oppgraderer alle interessepunkter...", "optimizeWorld.title": "Optimerer verden '%s'", "options.accessibility": "Tilgjengelighetsvalg...", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "Høykontrast-ressurspakke er ikke tilgjengelig.", "options.accessibility.high_contrast.tooltip": "Forsterker kontrasten av grensesnittelementer.", "options.accessibility.high_contrast_block_outline": "Høykontrast-blokkomriss", "options.accessibility.high_contrast_block_outline.tooltip": "Styrker kontrasten til omrisset til den utpekte blokken.", "options.accessibility.link": "Tilgjengelighetsveiledning", "options.accessibility.menu_background_blurriness": "Menybakgrunnssløring", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON>rer sløringen av menybakgrunner.", "options.accessibility.narrator_hotkey": "Forteller-<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON>r fortelleren veksles av og på med 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "<PERSON>r fort<PERSON>en bli vekslet av og på med 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panoramadrivfart", "options.accessibility.text_background": "Tekstbakgrunn", "options.accessibility.text_background.chat": "<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON><PERSON>", "options.accessibility.text_background_opacity": "Tekstbakgrunn-opasitet", "options.accessibility.title": "Tilgjengelighetsvalg", "options.allowServerListing": "Vis deg i spillerliste", "options.allowServerListing.tooltip": "Servere kan vise hvem som er koblet til i deres spillerliste.\nNavnet ditt vil ikke vises i slike lister om dette valget er skrudd av.", "options.ao": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minimum", "options.ao.off": "AV", "options.attack.crosshair": "Siktepunkt", "options.attack.hotbar": "Verktøylinje", "options.attackIndicator": "<PERSON><PERSON>psind<PERSON><PERSON>", "options.audioDevice": "<PERSON><PERSON><PERSON>", "options.audioDevice.default": "Systemstandard", "options.autoJump": "Autohopp", "options.autoSuggestCommands": "Kommandoforslag", "options.autosaveIndicator": "Opplys om autolagring", "options.biomeBlendRadius": "Markslagsblanding", "options.biomeBlendRadius.1": "AV (raskest)", "options.biomeBlendRadius.11": "11×11 (ekstrem)", "options.biomeBlendRadius.13": "13×13 (s<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15×15 (høyst)", "options.biomeBlendRadius.3": "3×3 (rask)", "options.biomeBlendRadius.5": "5×5 (normal)", "options.biomeBlendRadius.7": "7×7 (høy)", "options.biomeBlendRadius.9": "9×9 (veld<PERSON> høy)", "options.chat": "Nettpratsvalg...", "options.chat.color": "<PERSON><PERSON>", "options.chat.delay": "Nettpratsforsinkelse: %s sekund(er)", "options.chat.delay_none": "Nettprat-forsinkelse: Ingen", "options.chat.height.focused": "Fokusert høyde", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON><PERSON> hø<PERSON>", "options.chat.line_spacing": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.links": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.links.prompt": "<PERSON><PERSON><PERSON><PERSON> om lenker", "options.chat.opacity": "Gjennomskinn på prat", "options.chat.scale": "Nettprat-tekststørrelse", "options.chat.title": "Nettpratsvalg...", "options.chat.visibility": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Vist", "options.chat.visibility.hidden": "Skjult", "options.chat.visibility.system": "<PERSON>n kommandoer", "options.chat.width": "Bredde", "options.chunks": "%s stykker", "options.clouds.fancy": "Stilige", "options.clouds.fast": "Raske", "options.controls": "<PERSON><PERSON><PERSON>er...", "options.credits_and_attribution": "Medvirkende & attribusjon...", "options.damageTiltStrength": "Skaderisting", "options.damageTiltStrength.tooltip": "<PERSON><PERSON> mye kameraet rister når man tar skade.", "options.darkMojangStudiosBackgroundColor": "Monokrom logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON> bakgrunnsfargen på lasteskjermen til Mojang Studios til svart.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hvor mye mørkereffekten pulserer når en forvarer eller sculkskriker gir deg den.", "options.difficulty": "Vanskelighetsgrad", "options.difficulty.easy": "<PERSON>t", "options.difficulty.easy.info": "Fiendtlige skapninger dannes men gjør mindre skade. Sult tapper liv ned til 5 hjerter.", "options.difficulty.hard": "Vanskelig", "options.difficulty.hard.info": "Fiendtlige skapninger dannes og gjør ytterlig skade. Man blir sulten, og man kan dø av sult.", "options.difficulty.hardcore": "Hardbarket", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Fiendtlige skapninger dannes og gjør normalt med skade. Man blir sulten, og man kan sulte ned til et halvt hjerte.", "options.difficulty.online": "Servervanske", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Ingen fiendtlige skapninger. Kun visse nøytrale skapninger dannes. Man blir ikke sulten og liv fylles opp over tid.", "options.directionalAudio": "Retningslyd", "options.directionalAudio.off.tooltip": "Klassisk stereolyd.", "options.directionalAudio.on.tooltip": "Bruker HRTF-basert retningslyd til å forbedre simulasjonen av 3D-lyd. Krever HRTF-kompatibel lydvare, og erfares best med hodetelefoner.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON> rulling", "options.entityDistanceScaling": "Enhetsavstand", "options.entityShadows": "<PERSON><PERSON><PERSON><PERSON>", "options.font": "Te<PERSON><PERSON><PERSON><PERSON>...", "options.font.title": "Te<PERSON>nnstillinger", "options.forceUnicodeFont": "Bruk Unicode-skrift", "options.fov": "Synsfelt", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Synsfelteffekter", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hvor mye spilleffekter kan endre synsfeltet.", "options.framerate": "%s fps", "options.framerateLimit": "Høyest FPS", "options.framerateLimit.max": "Ubegrenset", "options.fullscreen": "Fullskjerm", "options.fullscreen.current": "Gjeldende", "options.fullscreen.entry": "%s×%s@%s (%sbit)", "options.fullscreen.resolution": "Fullskjermoppløsning", "options.fullscreen.unavailable": "Innstillingen er utilgjengelig", "options.gamma": "Lysstyrke", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "Lyst", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON><PERSON>", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hvor raskt det skimrer over fortryllede gjenstander.", "options.glintStrength": "Glinsestyrke", "options.glintStrength.tooltip": "Avg<PERSON><PERSON>r gjennomsiktigheten til skimret over fortryllede gjenstander.", "options.graphics": "<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Fabelaktig!", "options.graphics.fabulous.tooltip": "%s grafikk bruker skj<PERSON>skygger for å tegne vær, skyer og partikler bak gjennomskinnelige blokker og vann.\nDette kan i stor grad påvirke ytelsen for bærbare enheter og 4K-skjermer.", "options.graphics.fancy": "<PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "Stilig grafikk balanserer ytelse og kvalitet for de fleste maskiner.\nVær, skyer og partikler vises ikke bak gjennomskinnelige blokker eller vann.", "options.graphics.fast": "Rask", "options.graphics.fast.tooltip": "Rask grafikk reduserer mengden synlig regn og snø.\nGjennomsiktighetseffekter er deaktivert for forskjellige blokker, for eksempel treløv.", "options.graphics.warning.accept": "Fortsett uten støtte", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON> meg tilbake", "options.graphics.warning.message": "Grafikkenheten din er ikke støttet av grafikkinnstillingene \"%s\".\n\n<PERSON> kan <PERSON><PERSON> denne <PERSON>, men brukerst<PERSON>tte blir ikke tilbudt enheten din hvis du velger å bruke grafikken \"%s\".", "options.graphics.warning.renderer": "Gjengiver funnet: [%s]", "options.graphics.warning.title": "Grafikkenhet ikke støttet", "options.graphics.warning.vendor": "Leverandør funnet: [%s]", "options.graphics.warning.version": "OpenGL-versjon funnet: [%s]", "options.guiScale": "GUI-størrelse", "options.guiScale.auto": "Automatisk", "options.hidden": "Skjult", "options.hideLightningFlashes": "Skjul lynglimt", "options.hideLightningFlashes.tooltip": "<PERSON>ndrer lyn fra å lyse opp himmelen. Selve lynene vil fortsatt være synlige.", "options.hideMatchedNames": "Skjul like navn", "options.hideMatchedNames.tooltip": "Visse tredjepartsservere fastslår ikke alltid senderene av nettprat.\nOm valgt vil skjulte spillere i så fall forsøkes fastslås fra navn i teksten i nettpraten.", "options.hideSplashTexts": "Skjul splashtekster", "options.hideSplashTexts.tooltip": "Skjuler den gule splashteksten i hovedmenyen.", "options.inactivityFpsLimit": "Senk FPS når", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Begrenser bildefrekvens til 30 når spillet ikke får instruksjoner fra spilleren på over ett minutt. Begrenser videre til 10 etter 9 minutter til.", "options.inactivityFpsLimit.minimized": "Minimert", "options.inactivityFpsLimit.minimized.tooltip": "Begrenser bilderate kun når spillvinduet er minimert.", "options.invertMouse": "Inverter museretninger", "options.japaneseGlyphVariants": "Japanske tegnvarianter", "options.japaneseGlyphVariants.tooltip": "Bruker japanske varianter av CJK-tegn i den medfølgende skrifttypen.", "options.key.hold": "Hold", "options.key.toggle": "<PERSON><PERSON><PERSON>", "options.language": "Språk...", "options.language.title": "Språk", "options.languageAccuracyWarning": "(Oversettelser er muligens ikke 100%% presise)", "options.languageWarning": "Oversettelsene er kanskje ikke 100%% korrekte", "options.mainHand": "Hovedhånd", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Venstre bukseben", "options.modelPart.left_sleeve": "Venstre erme", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON> b<PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON> erme", "options.mouseWheelSensitivity": "Musehjul-følsomhet", "options.mouse_settings": "Datamusinnstillinger...", "options.mouse_settings.title": "Datamusinnstillinger", "options.multiplayer.title": "Flerspillerinnstillinger...", "options.multiplier": "%s×", "options.music_frequency": "Musikkhyppighet", "options.music_frequency.constant": "Alltid", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Ofte", "options.music_frequency.tooltip": "<PERSON><PERSON> hvor ofte musikk spilles mens man er i en spillverden.", "options.narrator": "<PERSON><PERSON>", "options.narrator.all": "<PERSON><PERSON> opp alt", "options.narrator.chat": "<PERSON><PERSON> opp nett<PERSON>t", "options.narrator.notavailable": "<PERSON><PERSON><PERSON>", "options.narrator.off": "AV", "options.narrator.system": "Leser opp systemet", "options.notifications.display_time": "Visningstid for varsler", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON> hvor lenge enhver varsling forblir synlig på skjermen.", "options.off": "AV", "options.off.composed": "%s: AV", "options.on": "PÅ", "options.on.composed": "%s: P<PERSON>", "options.online": "På nett...", "options.online.title": "<PERSON><PERSON><PERSON><PERSON>", "options.onlyShowSecureChat": "<PERSON>is kun sikker nettprat", "options.onlyShowSecureChat.tooltip": "Vis kun meldinger fra andre spillere som kan bekreftes å være sent fra den spilleren og som ikke har blitt endret.", "options.operatorItemsTab": "Operatørtøyfane", "options.particles": "<PERSON><PERSON><PERSON>", "options.particles.all": "Alle", "options.particles.decreased": "<PERSON><PERSON><PERSON>", "options.particles.minimal": "Minimalt", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Skildreprioritet", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Visse hendelser i et stykke bygger stykket på nytt med en gang. Dette omfatter plassering eller fjerning av blokker.", "options.prioritizeChunkUpdates.nearby": "Nærheten", "options.prioritizeChunkUpdates.nearby.tooltip": "Stykker i nærheten blir alltid bygde med en gang. Dette kan påvirke spillytelsen når blokker plasseres eller fjernes.", "options.prioritizeChunkUpdates.none": "Ingen", "options.prioritizeChunkUpdates.none.tooltip": "Stykker i nærheten bygges i parallelle tråder. Dette kan føre til knappe visuelle hull når blokker fjernes.", "options.rawMouseInput": "Ubehandlet inndata", "options.realmsNotifications": "Realms-nyheter & -invitasjoner", "options.realmsNotifications.tooltip": "Henter Realms-nyheter og invitasjoner til hovedmenyen og viser deres passende ikon på Realms-knappen.", "options.reducedDebugInfo": "<PERSON><PERSON> f<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "Skyavstand", "options.renderDistance": "Skildringsvidde", "options.resourcepack": "Ressurspakker...", "options.rotateWithMinecart": "Roter med gruvevogner", "options.rotateWithMinecart.tooltip": "Om spillerens synsfelt skal vende seg med en svingende gruvevogn. Kun tilgjengelig i verdener med den eksperimentelle innstillingen «Forbedringer av gruvevogner» slått på.", "options.screenEffectScale": "Forvrengningseffekter", "options.screenEffectScale.tooltip": "Styrken til forvrengningseffekten fra kvalme og Netherportal.\nVed lavere verdier erstattes kvalmeeffekten med et grønt filter.", "options.sensitivity": "Sensitivitet", "options.sensitivity.max": "HYPERFART!!!", "options.sensitivity.min": "*gjesp*", "options.showNowPlayingToast": "<PERSON><PERSON> musikk-toast", "options.showNowPlayingToast.tooltip": "Viser en toast når en sang begynner å spilles av. Den samme toasten er alltid synlig i pausemenyen mens en sang spilles av.", "options.showSubtitles": "<PERSON><PERSON>", "options.simulationDistance": "Simuleringsvidde", "options.skinCustomisation": "Skalltilpasning...", "options.skinCustomisation.title": "Skalltilpasning", "options.sounds": "Musikk & Lyder...", "options.sounds.title": "Musikk- og lydalternativer", "options.telemetry": "Telemetridata...", "options.telemetry.button": "Datainn<PERSON><PERSON>ling", "options.telemetry.button.tooltip": "\"%s\" tar kun med påkrevde data.\n\"%s\" tar med valgfrie samt påkrevde data.", "options.telemetry.disabled": "Telemetri er slått av.", "options.telemetry.state.all": "Alt", "options.telemetry.state.minimal": "Minimalt", "options.telemetry.state.none": "Intet", "options.title": "Innstillinger", "options.touchscreen": "Berøringsskjerm-modus", "options.video": "Videoinnstillinger...", "options.videoTitle": "Videoinnstillinger", "options.viewBobbing": "Vis animert gange", "options.visible": "Vist", "options.vsync": "VSync", "outOfMemory.message": "Minecraft har gått tom for minne.\n\nDette kan grunnes feil i spillet eller at Java Virtual Machine ikke tildeltes nok minne.\n\nFor å forhindre verdenskorrupsjon ble spillet avsluttet. Vi har prøvd å frigjøre nok minne til at du skal kunne gå tilbake til hovedmenyen og fortsette spillet, men det er ikke sikkert at dette har fungert.\n\nVennligst start spillet på nytt om du ser denne meldingen igjen.", "outOfMemory.title": "Tomt for minne!", "pack.available.title": "Tilgjengelig", "pack.copyFailure": "Kunne ikke kopiere pakker", "pack.dropConfirm": "Vil du legge til følgende pakker i Minecraft?", "pack.dropInfo": "<PERSON>a og slipp filer inn i dette vinduet for å legge til pakker", "pack.dropRejected.message": "Følgende oppførsler var ugyldige pakker og ble ikke kopiert:\n %s", "pack.dropRejected.title": "Ikke-pakke-oppføringer", "pack.folderInfo": "(<PERSON><PERSON> pak<PERSON><PERSON><PERSON> her)", "pack.incompatible": "Inkompatibel", "pack.incompatible.confirm.new": "<PERSON>ne pakken ble lagd for en nyere utgave av Minecraft og vil kanskje ikke fungere.", "pack.incompatible.confirm.old": "<PERSON>ne pakken ble lagd for en eldre utgave av Minecraft og vil kanskje ikke fungere.", "pack.incompatible.confirm.title": "Er du sikker på at du vil laste inn denne pakken?", "pack.incompatible.new": "(Laget for en nyere versjon av Minecraft)", "pack.incompatible.old": "(Laget for en eldre versjon av Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON> mappe", "pack.selected.title": "<PERSON><PERSON>", "pack.source.builtin": "medfølgende", "pack.source.feature": "virkemåte", "pack.source.local": "lokal", "pack.source.server": "server", "pack.source.world": "verden", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON> (Earth)", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Ild (Fire)", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON> (Water)", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON>d (Wind)", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Tilfeldig variant", "parsing.bool.expected": "Forventet boolsk verdi", "parsing.bool.invalid": "Ugyldig boolsk verdi. Forventet \"true\" eller \"false\", men fant \"%s\"", "parsing.double.expected": "Dobbeltall var forventet", "parsing.double.invalid": "«%s» er et ugyldig dobbeltall", "parsing.expected": "«%s» var forventet", "parsing.float.expected": "Flyttall var forventet", "parsing.float.invalid": "Ugyldig flyttall «%s»", "parsing.int.expected": "<PERSON><PERSON><PERSON> for<PERSON>", "parsing.int.invalid": "«%s» er et ugyldig heltall", "parsing.long.expected": "<PERSON><PERSON><PERSON><PERSON> forventet", "parsing.long.invalid": "Ugyldig lang-verdi '%s'", "parsing.quote.escape": "\"\\%s\" er en ugyldig skiftesekvens i den siterte strengen", "parsing.quote.expected.end": "<PERSON><PERSON>gen har intet lukkende anførseltegn", "parsing.quote.expected.start": "Anførseltegn forventet for å starte en streng", "particle.invalidOptions": "Kan ikke tolke partikkelvalg: %s", "particle.notFound": "<PERSON><PERSON><PERSON><PERSON> partik<PERSON>: %s", "permissions.requires.entity": "En enhet kreves for å kjøre denne kommandoen", "permissions.requires.player": "En spiller kreves for å kjøre denne kommandoen", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "<PERSON><PERSON><PERSON> brukt:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "<PERSON>k<PERSON><PERSON> predikat: %s", "quickplay.error.invalid_identifier": "<PERSON>nne ikke finne verden med gitt identifikator", "quickplay.error.realm_connect": "<PERSON>nne ikke koble til Realm", "quickplay.error.realm_permission": "<PERSON><PERSON> tillatelse til å koble til denne Realmen", "quickplay.error.title": "Feil med hurtig<PERSON>ill", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Østlige Japan", "realms.configuration.region.japan_west": "Vestlige Japan", "realms.configuration.region.korea_central": "Sør-Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sverige", "realms.configuration.region.uae_north": "De forente arabiske emirater (UAE)", "realms.configuration.region.uk_south": "Sørlige England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Nederland", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatisk (Realm-eiers ping)", "realms.configuration.region_preference.automatic_player": "Automatisk (fø<PERSON>emann til økten)", "realms.missing.snapshot.error.text": "Realms er for tiden ikke støttet i utviklingsversjoner", "recipe.notFound": "Ukjent oppskrift: %s", "recipe.toast.description": "Sjekk oppskriftsboken din", "recipe.toast.title": "<PERSON>y(e) oppskrift(er) låst opp!", "record.nowPlaying": "Spiller nå av: %s", "recover_world.bug_tracker": "Meld inn feil", "recover_world.button": "Forsøk å gjenopprette", "recover_world.done.failed": "<PERSON><PERSON><PERSON><PERSON>retting fra forrige tilstand mislyktes.", "recover_world.done.success": "Gjenoppretting lyktes!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ferdig", "recover_world.issue.missing_file": "Manglende fil", "recover_world.issue.none": "Ingen problemer", "recover_world.message": "Følgende problem inntraff under innlesing av verdensmappen \"%s\".\nDet kan være mulig å gjenopprette verdenen fra en eldre tilstand, eller så kan du melde inn denne feilen i feilsporeren.", "recover_world.no_fallback": "Ingen tilstand å gjenopprette fra tilgjengelig", "recover_world.restore": "Forsøk å gjenopprette", "recover_world.restoring": "Forsøker å gjenopprette verden...", "recover_world.state_entry": "Tilstand fra %s: ", "recover_world.state_entry.unknown": "ukjent", "recover_world.title": "Innlasting av verden mislyktes", "recover_world.warning": "Innlasting av verdenssammendrag mislyktes", "resourcePack.broken_assets": "FANT ØDELAGTE RESSURSER", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resourcePack.load_fail": "Kunne ikke laste inn ressurser på nytt", "resourcePack.programmer_art.name": "Utviklingskunst", "resourcePack.runtime_failure": "Ressurspakkefeil oppdaget", "resourcePack.server.name": "Verdensbestemte ressurser", "resourcePack.title": "<PERSON>elg ressurspakke", "resourcePack.vanilla.description": "Minecrafts opprinnelige utseende og stemning", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Laster ned ress<PERSON><PERSON><PERSON><PERSON>", "resourcepack.progress": "Laster ned fil (%s MB)...", "resourcepack.requesting": "Sender forespørsel...", "screenshot.failure": "Kunne ikke lagre skjermbilde: %s", "screenshot.success": "Lagret skjermbilde som %s", "selectServer.add": "Legg til server", "selectServer.defaultName": "Minecraft-server", "selectServer.delete": "<PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON>", "selectServer.deleteQuestion": "Er du sikker på at du vil fjerne denne serveren?", "selectServer.deleteWarning": "'%s' blir borte for alltid! (<PERSON><PERSON><PERSON> lenge!)", "selectServer.direct": "<PERSON><PERSON><PERSON> til<PERSON>", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(Sk<PERSON>lt)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Kobl til server", "selectWorld.access_failure": "Fikk ikke tilgang til verden", "selectWorld.allowCommands": "Tillat juksekoder", "selectWorld.allowCommands.info": "Kommandoer som /gamemode, /xp", "selectWorld.allowCommands.new": "Tillat kommandoer", "selectWorld.backupEraseCache": "<PERSON>lett hurtiglagrede data", "selectWorld.backupJoinConfirmButton": "Lag sikkerhetskopi og last inn", "selectWorld.backupJoinSkipButton": "Jeg vet hva jeg gjør!", "selectWorld.backupQuestion.customized": "Egendefinerte verdener støttes ikke lenger", "selectWorld.backupQuestion.downgrade": "Nedgradering av verdener er ikke støttet", "selectWorld.backupQuestion.experimental": "Verdener som bruker eksperimentelle innstillinger er ikke støttet", "selectWorld.backupQuestion.snapshot": "Vil du til tross laste inn denne verdenen?", "selectWorld.backupWarning.customized": "Vi støtter dessverre ikke egendefinerte verdener i denne versjonen av Minecraft. Vi kan enda laste inn denne verdenen og bevare alt som det var, men alt nytt generert landskap vil ikke lenger tilpasses. Vi beklager bryet!", "selectWorld.backupWarning.downgrade": "Denne verdenen ble sist spilt på i versjon %s; du er på versjon %s. Nedgradering av en verden kan medføre datakorrupsjon - det er ikke sikkert at den vil kunne lastes inn eller fungere. Om du enda vil fortsette, vennligst husk å lage en reservekopi.", "selectWorld.backupWarning.experimental": "Denne verdenen bruker eksperimentelle innstillinger som kan stoppe å fungere når som helst. Vi kan ikke garantere at den vil laste inn eller virke. Pass på!", "selectWorld.backupWarning.snapshot": "Denne verdenen ble sist spilt på i versjon %s; Du er i versjon %s. Vennligst lag en reservekopi i tilfelle du erfarer verdenskorrupsjon.", "selectWorld.bonusItems": "Bonuskiste", "selectWorld.cheats": "Ju<PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Må konverteres!", "selectWorld.conversion.tooltip": "Denne verdenen må åpnes i en eldre versjon (som 1.6.4) for at den skal konverteres trygt", "selectWorld.create": "Lag ny verden", "selectWorld.customizeType": "Tilpass", "selectWorld.dataPacks": "Datapakker", "selectWorld.data_read": "Leser verdensdata...", "selectWorld.delete": "<PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON>", "selectWorld.deleteQuestion": "Er du sikker på at du vil slette denne verdenen?", "selectWorld.deleteWarning": "'%s' vil forsvinne for evig og alltid!", "selectWorld.delete_failure": "<PERSON>nne ikke slette verden", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON>g si<PERSON>", "selectWorld.edit.backupCreated": "Sikkerhetskopierte: %s", "selectWorld.edit.backupFailed": "Sikkerhetskopi mislyktes", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON>", "selectWorld.edit.backupSize": "plass: %s MB", "selectWorld.edit.export_worldgen_settings": "Eksporter verdensgenerasjonsinnstillinger", "selectWorld.edit.export_worldgen_settings.failure": "Eksportering mislyktes", "selectWorld.edit.export_worldgen_settings.success": "Eksportert", "selectWorld.edit.openFolder": "Åpn verdensmappen", "selectWorld.edit.optimize": "<PERSON><PERSON><PERSON> verden", "selectWorld.edit.resetIcon": "Tilbakestill ikon", "selectWorld.edit.save": "Lagr", "selectWorld.edit.title": "Rediger verden", "selectWorld.enterName": "Verdensnavn", "selectWorld.enterSeed": "Frøtall til verdensgeneratoren", "selectWorld.experimental": "Eksperimentell", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Påkrevde eksperimentelle tillegg: %s", "selectWorld.experimental.details.title": "Eksperimentelle virkemåters betingelser", "selectWorld.experimental.message": "Vær forsiktig!\nDenne konfigurasjonen betinger virkemåter som enda er under utvikling. Verdenen din kan krasje, skades eller kanskje ikke fungere med fremtidige oppdateringer.", "selectWorld.experimental.title": "Advarsel om eksperimentelle funksjoner", "selectWorld.experiments": "Eksperiment", "selectWorld.experiments.info": "Eksperiment er potensielle nye virkemåter. Vær forsiktig; det kan gå galt. Eksperiment kan ikke skrus av etter at verdenen er skapt.", "selectWorld.futureworld.error.text": "Noe gikk galt mens du forsøkte å laste inn en verden fra en fremtidig versjon. Dette var tross alt et risikabel forsøk. Beklager at det ikke fungerte.", "selectWorld.futureworld.error.title": "En feil har oppst<PERSON><PERSON>!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Eventyr", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON> som overlevelsesmodus, men blokker kan ikke plasseres eller fjernes.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON> som overlevelsesmodus, men blokker kan ikke", "selectWorld.gameMode.adventure.line2": "legges til eller fjernes", "selectWorld.gameMode.creative": "K<PERSON><PERSON>v", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, bygg og utforsk uten grenser. <PERSON> kan fly, har uendelig med byggematerialer og kan ikke skades av monstre.", "selectWorld.gameMode.creative.line1": "Ubegren<PERSON><PERSON> ressurser, fly fritt og", "selectWorld.gameMode.creative.line2": "ødelegg blokker øyeblikkelig", "selectWorld.gameMode.hardcore": "Hardbarket", "selectWorld.gameMode.hardcore.info": "Overlevelsesmodus låst på 'vanskelig'. Du kan ikke fortsette om du dør.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON> som overlevelsemodus, låst på vanskeligste", "selectWorld.gameMode.hardcore.line2": "vanskelighetsgrad, og kun ett liv", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON> kan se, men ikke røre.", "selectWorld.gameMode.spectator.line1": "Du kan se, men ikke røre", "selectWorld.gameMode.survival": "Overlevelse", "selectWorld.gameMode.survival.info": "Utforsk en mystisk verden hvor du bygger, sa<PERSON>, til<PERSON><PERSON> og slåss mot monstre.", "selectWorld.gameMode.survival.line1": "Let etter ressurser, lag verktøy og våpen, få", "selectWorld.gameMode.survival.line2": "niv<PERSON><PERSON>, liv og sult", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Importer innstillinger", "selectWorld.import_worldgen_settings.failure": "Feil ved importering av innstilling", "selectWorld.import_worldgen_settings.select_file": "Velg innstillingsfil (.json)", "selectWorld.incompatible.description": "Denne verdenen kan ikke åpnes i denne versjonen.\nDen ble sist spilt på i versjon %s.", "selectWorld.incompatible.info": "Uforenelig versjon: %s", "selectWorld.incompatible.title": "Uforenelig versjon", "selectWorld.incompatible.tooltip": "Denne verdenen kan ikke åpnes siden den ble laget i en uforenelig versjon.", "selectWorld.incompatible_series": "Skapt i en uforenelig versjon", "selectWorld.load_folder_access": "Kan ikke lese eller åpne mappen der spillverdener er lagret!", "selectWorld.loading_list": "Laster inn verdensliste", "selectWorld.locked": "Låst av en annen kjørende instans av Minecraft", "selectWorld.mapFeatures": "<PERSON><PERSON> strukturer", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, skips<PERSON><PERSON> osv.", "selectWorld.mapType": "Verdenstype", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Flere verden<PERSON>...", "selectWorld.newWorld": "<PERSON><PERSON> verden", "selectWorld.recreate": "Lag på nytt", "selectWorld.recreate.customized.text": "Egendefinerte verdener er ikke lenger støttet i denne versjonen av Minecraft. Vi kan prøve å gjenskape den med samme frøtall og egenskaper, men alt tilpasset landskap vil gå tapt. Vi beklager bryet!", "selectWorld.recreate.customized.title": "Egendefinerte verdener støttes ikke lenger", "selectWorld.recreate.error.text": "Noe gikk galt mens vi prøvde å gjenskape en verden.", "selectWorld.recreate.error.title": "En feil oppsto!", "selectWorld.resource_load": "Forbereder ressurser...", "selectWorld.resultFolder": "Vil lagres i:", "selectWorld.search": "søk etter verdener", "selectWorld.seedInfo": "La stå blank for et tilfeldig frøtall", "selectWorld.select": "<PERSON><PERSON><PERSON> på valgt verden", "selectWorld.targetFolder": "Lagringsmappe: %s", "selectWorld.title": "Velg verden", "selectWorld.tooltip.fromNewerVersion1": "Verdenen ble lagret i en nyere versjon,", "selectWorld.tooltip.fromNewerVersion2": "innlasting av denne verdenen kan føre til problemer!", "selectWorld.tooltip.snapshot1": "Ikke glem å sikkerhetskopiere denne verdenen", "selectWorld.tooltip.snapshot2": "før du laster det inn i denne utviklingsversjonen.", "selectWorld.unable_to_load": "Kunne ikke laste inn verdener", "selectWorld.version": "Versjon:", "selectWorld.versionJoinButton": "Last inn likevel", "selectWorld.versionQuestion": "Vil du virkelig laste inn denne verdenen?", "selectWorld.versionUnknown": "ukjent", "selectWorld.versionWarning": "Denne verdenen ble sist spilt på med versjon %s. Å laste den inn med denne versjonen kan føre til korrupsjon!", "selectWorld.warning.deprecated.question": "<PERSON>en benyttede virkemåter er foreldede og vil slutte å fungere i fremtiden. Vil du fortsette?", "selectWorld.warning.deprecated.title": "Advarsel! Disse innstillingene bruker foreldede tilleggsfunksjoner", "selectWorld.warning.experimental.question": "Disse innstillingene er eksperimentelle og kan en vilkårlig dag slutte å virke. Vil du fortsette?", "selectWorld.warning.experimental.title": "Advarsel! Disse innstillingene avhenger av eksperimentelle virkemåter", "selectWorld.warning.lowDiskSpace.description": "Det er ikke mye plass igjen på enheten din.\nÅ gå tom for diskplass mens du spiller kan føre til at verdenen din blir skadet.", "selectWorld.warning.lowDiskSpace.title": "Advarsel! Lite lagringsplass!", "selectWorld.world": "Verden", "sign.edit": "Rediger melding på skilt", "sleep.not_possible": "Denne natten kan ikke soves gjennom", "sleep.players_sleeping": "%s/%s spillere sover", "sleep.skipping_night": "Sover gjennom denne natten", "slot.only_single_allowed": "Kun enkelte ruter tillatt, fikk '%s'", "slot.unknown": "Ukjent rute '%s'", "snbt.parser.empty_key": "<PERSON><PERSON>k<PERSON> kan ikke være tom", "snbt.parser.expected_binary_numeral": "Forventet et binærtall", "snbt.parser.expected_decimal_numeral": "Forventet et desimaltall", "snbt.parser.expected_float_type": "Forventet et flyttall", "snbt.parser.expected_hex_escape": "Forventet et tegnliteral med lengde %s", "snbt.parser.expected_hex_numeral": "Forventet et heksadesimaltall", "snbt.parser.expected_integer_type": "Forventet et heltall", "snbt.parser.expected_non_negative_number": "Forventet et ikke-negativt tall", "snbt.parser.expected_number_or_boolean": "Forventet et tall eller en boolsk verdi", "snbt.parser.expected_string_uuid": "Forventet en streng som representerer en gyldig UUID", "snbt.parser.expected_unquoted_string": "Forventet en gyldig uanført streng", "snbt.parser.infinity_not_allowed": "<PERSON>k<PERSON>-end<PERSON>ge tall er ikke tillatt", "snbt.parser.invalid_array_element_type": "Ugyldig tabellelementtype", "snbt.parser.invalid_character_name": "Ugyldig Unicodetegn-navn", "snbt.parser.invalid_codepoint": "Ugyldig Unicodetegn-verdi: %s", "snbt.parser.invalid_string_contents": "Ugyldig strenginnhold", "snbt.parser.invalid_unquoted_start": "<PERSON><PERSON><PERSON> strenger kan ikke starte med sifrene 0-9, + el<PERSON> -", "snbt.parser.leading_zero_not_allowed": "Desimaltall kan ikke starte med 0", "snbt.parser.no_such_operation": "Ingen slik operasjon: %s", "snbt.parser.number_parse_failure": "Lyktes ikke med å tolke tall: %s", "snbt.parser.undescore_not_allowed": "Understrekstegn er ikke tillatt på starten eller slutten av et tall", "soundCategory.ambient": "Omgivelser/Miljø", "soundCategory.block": "B<PERSON>kker", "soundCategory.hostile": "Fiendtlige skapninger", "soundCategory.master": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.music": "Musikk", "soundCategory.neutral": "Vennlige ska<PERSON>ninger", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Platespiller/Noteblokker", "soundCategory.ui": "<PERSON><PERSON><PERSON><PERSON><PERSON> (UI)", "soundCategory.voice": "Stemme/Tale", "soundCategory.weather": "<PERSON><PERSON><PERSON>", "spectatorMenu.close": "Lukk meny", "spectatorMenu.next_page": "Neste side", "spectatorMenu.previous_page": "Forrige side", "spectatorMenu.root.prompt": "Trykk en tast for å velge en kommando, og trykk igjen for å bruke den.", "spectatorMenu.team_teleport": "Teleporter til en lagspiller", "spectatorMenu.team_teleport.prompt": "Velg et lag å teleportere til", "spectatorMenu.teleport": "Teleporter til spiller", "spectatorMenu.teleport.prompt": "Velg en spiller å teleportere til", "stat.generalButton": "Generelt", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Dyr avlet", "stat.minecraft.aviate_one_cm": "Avstand med elytra", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON> ringt", "stat.minecraft.boat_one_cm": "Avstand med båt", "stat.minecraft.clean_armor": "Rustningsdeler vasket", "stat.minecraft.clean_banner": "Bannere vasket", "stat.minecraft.clean_shulker_box": "Shulkerbokser vasket", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON> klatret", "stat.minecraft.crouch_one_cm": "Avstand sneket", "stat.minecraft.damage_absorbed": "Skade absorbert", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> hindret med skjold", "stat.minecraft.damage_dealt": "S<PERSON><PERSON> gjort", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> gjort (absorbert)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> gjo<PERSON> (motstått)", "stat.minecraft.damage_resisted": "Ska<PERSON> m<PERSON>", "stat.minecraft.damage_taken": "Skade tatt", "stat.minecraft.deaths": "D<PERSON>dsfall", "stat.minecraft.drop": "<PERSON><PERSON><PERSON><PERSON> sluppet", "stat.minecraft.eat_cake_slice": "Kakestykker spist", "stat.minecraft.enchant_item": "Utstyr fortryllet", "stat.minecraft.fall_one_cm": "Avstand falt", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON> fylt", "stat.minecraft.fish_caught": "Fisk fanget", "stat.minecraft.fly_one_cm": "Avstand flydd", "stat.minecraft.happy_ghast_one_cm": "Avstand flydd på lykkelig ghast", "stat.minecraft.horse_one_cm": "Avstand ridd på hest", "stat.minecraft.inspect_dispenser": "Utskytere gjennomsøkt", "stat.minecraft.inspect_dropper": "Utslippere gjennomsøkt", "stat.minecraft.inspect_hopper": "Trakter gjennomsøkt", "stat.minecraft.interact_with_anvil": "Benyttelser av ambolter", "stat.minecraft.interact_with_beacon": "Benyttelser av varder", "stat.minecraft.interact_with_blast_furnace": "Benyttelser av masovner", "stat.minecraft.interact_with_brewingstand": "Benyttelser av bryggestativ", "stat.minecraft.interact_with_campfire": "Benyttelser av bål", "stat.minecraft.interact_with_cartography_table": "Benyttelser av karttegningsbenker", "stat.minecraft.interact_with_crafting_table": "Benyttelser av arbeidsbenker", "stat.minecraft.interact_with_furnace": "Benyttelser av smelteovner", "stat.minecraft.interact_with_grindstone": "Benyttelser av slipesteiner", "stat.minecraft.interact_with_lectern": "Benyttelser av katetere", "stat.minecraft.interact_with_loom": "Benyttelser av vevstoler", "stat.minecraft.interact_with_smithing_table": "Benyttelser av smibenker", "stat.minecraft.interact_with_smoker": "Benyttelser av røykovner", "stat.minecraft.interact_with_stonecutter": "Benyttelser av steinkuttere", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "Spill avsluttet", "stat.minecraft.minecart_one_cm": "Avstand med gruvevogn", "stat.minecraft.mob_kills": "Skapninger drept", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON>", "stat.minecraft.open_enderchest": "Enderkister åpnet", "stat.minecraft.open_shulker_box": "Shulkerbokser åpnet", "stat.minecraft.pig_one_cm": "Avstand ridd på gris", "stat.minecraft.play_noteblock": "Noteb<PERSON>kker spilt", "stat.minecraft.play_record": "Musikkplater spilt", "stat.minecraft.play_time": "Tid spilt", "stat.minecraft.player_kills": "<PERSON><PERSON>ller<PERSON> drept", "stat.minecraft.pot_flower": "Planter pottet", "stat.minecraft.raid_trigger": "<PERSON><PERSON><PERSON>", "stat.minecraft.raid_win": "<PERSON><PERSON><PERSON> overvunnet", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON> sovet i en seng", "stat.minecraft.sneak_time": "Sniketid", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> lø<PERSON>", "stat.minecraft.strider_one_cm": "Avstand ridd på lavavandrer", "stat.minecraft.swim_one_cm": "Avstand svømt", "stat.minecraft.talked_to_villager": "Prater med byg<PERSON>er", "stat.minecraft.target_hit": "Blinker truffet", "stat.minecraft.time_since_death": "<PERSON>id siden forrige død", "stat.minecraft.time_since_rest": "Tid siden siste søvn", "stat.minecraft.total_world_time": "Total verdenstid", "stat.minecraft.traded_with_villager": "Handler med bygdinger", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.tune_noteblock": "<PERSON>b<PERSON><PERSON>", "stat.minecraft.use_cauldron": "<PERSON>n tatt fra gryte", "stat.minecraft.walk_on_water_one_cm": "Avstand vandret på vann", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON> van<PERSON>", "stat.minecraft.walk_under_water_one_cm": "Avstand vandret under vann", "stat.mobsButton": "Skapninger", "stat_type.minecraft.broken": "<PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON> pro<PERSON>", "stat_type.minecraft.dropped": "Sluppet", "stat_type.minecraft.killed": "Du drepte %s %s", "stat_type.minecraft.killed.none": "Du har aldri drept %s", "stat_type.minecraft.killed_by": "%s drepte deg %s gang(er)", "stat_type.minecraft.killed_by.none": "Du har aldri blitt drept av %s", "stat_type.minecraft.mined": "<PERSON><PERSON>", "stat_type.minecraft.picked_up": "Plukket opp", "stat_type.minecraft.used": "<PERSON><PERSON> brukt", "stats.none": "-", "structure_block.button.detect_size": "OPPDAG", "structure_block.button.load": "LAST", "structure_block.button.save": "LAGRE", "structure_block.custom_data": "Egendefinert datatagg-navn", "structure_block.detect_size": "Gjenkjenn strukturstørrelse og posisjon:", "structure_block.hover.corner": "Hjørne: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Last: %s", "structure_block.hover.save": "Lagre: %s", "structure_block.include_entities": "Ta med enheter:", "structure_block.integrity": "Strukturintegritet og frøtall", "structure_block.integrity.integrity": "Strukturhelhet", "structure_block.integrity.seed": "Strukturfrøtall", "structure_block.invalid_structure_name": "Ugyldig strukturnavn '%s'", "structure_block.load_not_found": "Strukturen '%s' er ikke tilg<PERSON>ngelig ", "structure_block.load_prepare": "Strukturen '%s's p<PERSON><PERSON> forberedt", "structure_block.load_success": "Struktur lastet fra '%s'", "structure_block.mode.corner": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.mode.data": "Data", "structure_block.mode.load": "Last inn", "structure_block.mode.save": "Lagre", "structure_block.mode_info.corner": "Hjørnemodus - Posisjon- og størrelsemarkør", "structure_block.mode_info.data": "Datamodus - Spillogikkmarkør", "structure_block.mode_info.load": "Innlastningsmodus - Last inn fra fil", "structure_block.mode_info.save": "Lagremodus - Skriv til fil", "structure_block.position": "Relativ posisjon", "structure_block.position.x": "relativ posisjon x", "structure_block.position.y": "relativ posisjon y", "structure_block.position.z": "relativ posisjon z", "structure_block.save_failure": "Kan ikke lagre strukturen '%s'", "structure_block.save_success": "Struktur lagret som '%s'", "structure_block.show_air": "Vis usynlige blokker:", "structure_block.show_boundingbox": "Vis omfangsramme:", "structure_block.size": "Strukturstørrelse", "structure_block.size.x": "strukturstørrel<PERSON> x", "structure_block.size.y": "strukturstø<PERSON><PERSON> y", "structure_block.size.z": "strukturstørrelse z", "structure_block.size_failure": "Kunne ikke oppdage strukturstørrelse. Legg til hjørner med tilsvarende strukturnavn", "structure_block.size_success": "Størrelse er oppdaget for '%s'", "structure_block.strict": "<PERSON><PERSON><PERSON> plassering:", "structure_block.structure_name": "Strukturnavn", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON><PERSON> stø<PERSON>", "subtitles.ambient.sound": "<PERSON>fs lyd", "subtitles.block.amethyst_block.chime": "Ametystklang", "subtitles.block.amethyst_block.resonate": "Ametyst klinger", "subtitles.block.anvil.destroy": "<PERSON><PERSON>", "subtitles.block.anvil.land": "Ambolt landet", "subtitles.block.anvil.use": "Ambolt brukt", "subtitles.block.barrel.close": "<PERSON><PERSON><PERSON> luk<PERSON>", "subtitles.block.barrel.open": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.activate": "Varde aktiveres", "subtitles.block.beacon.ambient": "Varde summer", "subtitles.block.beacon.deactivate": "Varde de<PERSON>", "subtitles.block.beacon.power_select": "Vardekraft valgt", "subtitles.block.beehive.drip": "Honning drypper", "subtitles.block.beehive.enter": "Bie flyr inn i bikube", "subtitles.block.beehive.exit": "<PERSON><PERSON> for<PERSON>r bi<PERSON>be", "subtitles.block.beehive.shear": "Saks skraper", "subtitles.block.beehive.work": "<PERSON><PERSON> arbeider", "subtitles.block.bell.resonate": "Bjelleklang", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON> ringer", "subtitles.block.big_dripleaf.tilt_down": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "subtitles.block.big_dripleaf.tilt_up": "Dråpeblad reiser seg", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON> spraker", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bobler", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON> sprekker", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON> flyter", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON> s<PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON> virvler", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON> fyker", "subtitles.block.button.click": "<PERSON><PERSON><PERSON> klikker", "subtitles.block.cake.add_candle": "<PERSON><PERSON> moses", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON> spraker", "subtitles.block.candle.crackle": "<PERSON><PERSON> spra<PERSON>", "subtitles.block.candle.extinguish": "<PERSON><PERSON> sluk<PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON> luk<PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON>", "subtitles.block.chorus_flower.death": "Refrengblomst visner", "subtitles.block.chorus_flower.grow": "Refrengblomst vokser", "subtitles.block.comparator.click": "Sammenligner k<PERSON>", "subtitles.block.composter.empty": "Kompostbinge tømt", "subtitles.block.composter.fill": "Kompostbinge fylt", "subtitles.block.composter.ready": "Kompostbinge komposterer", "subtitles.block.conduit.activate": "Fløder aktiveres", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Kobberpære slås av", "subtitles.block.copper_bulb.turn_on": "Kobberpære slås på", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON> luk<PERSON>", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON>", "subtitles.block.crafter.craft": "Tilvirker tilvirker", "subtitles.block.crafter.fail": "Tilvirker mislyktes", "subtitles.block.creaking_heart.hurt": "Knirkningshjerte grumler", "subtitles.block.creaking_heart.idle": "<PERSON>fs lyd", "subtitles.block.creaking_heart.spawn": "Knirkningshjerte våkner", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON> potte fylles", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON><PERSON> potte vugger", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON> potte knuser", "subtitles.block.dispenser.dispense": "Gjenstand skytes ut", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON> misly<PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Lyder av tørrhet", "subtitles.block.dried_ghast.ambient_water": "Uttørket ghast bløter seg", "subtitles.block.dried_ghast.place_in_water": "Uttørket ghast blø<PERSON>gg<PERSON>", "subtitles.block.dried_ghast.transition": "Uttørket ghast føler seg bedre", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON><PERSON> lyder", "subtitles.block.enchantment_table.use": "Trolldomsbord brukt", "subtitles.block.end_portal.spawn": "Endportal åpnes", "subtitles.block.end_portal_frame.fill": "Enderøye festes", "subtitles.block.eyeblossom.close": "Øyeblom lukker seg", "subtitles.block.eyeblossom.idle": "<PERSON>yeb<PERSON><PERSON> h<PERSON>", "subtitles.block.eyeblossom.open": "Øyeblom åpner seg", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON> knirker", "subtitles.block.fire.ambient": "Flammer spraker", "subtitles.block.fire.extinguish": "Brann slukket", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON> surrer", "subtitles.block.frogspawn.hatch": "Rumpetroll klekker ut", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON> spraker", "subtitles.block.generic.break": "Blokk ødelagt", "subtitles.block.generic.fall": "Noe faller på en blokk", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Blokk ødelegges", "subtitles.block.generic.place": "Blokk plassert", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON> brukt", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> klippes", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON> vaier", "subtitles.block.honey_block.slide": "Sk<PERSON>r ned en honningblokk", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON> luk<PERSON>", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON>", "subtitles.block.lava.ambient": "<PERSON><PERSON> bobler", "subtitles.block.lava.extinguish": "<PERSON><PERSON> freser", "subtitles.block.lever.click": "Spaken klikker", "subtitles.block.note_block.note": "Noteblokk spiller", "subtitles.block.pale_hanging_moss.idle": "<PERSON>fs lyd", "subtitles.block.piston.move": "Stempel beveger seg", "subtitles.block.pointed_dripstone.drip_lava": "Lava drypper", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava drypper ned i gryte", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON> ned i gryte", "subtitles.block.pointed_dripstone.land": "<PERSON><PERSON><PERSON><PERSON> raser ned", "subtitles.block.portal.ambient": "Portal svirrer", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON><PERSON> ble<PERSON>", "subtitles.block.portal.trigger": "Portalstøy intensiveres", "subtitles.block.pressure_plate.click": "Trykkplaten klikker", "subtitles.block.pumpkin.carve": "Saks skjærer", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> freser", "subtitles.block.respawn_anchor.ambient": "Livsanker svirrer", "subtitles.block.respawn_anchor.charge": "Livsanker lades", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON> tø<PERSON>", "subtitles.block.respawn_anchor.set_spawn": "Livsanker ankrer startpunkt", "subtitles.block.sand.idle": "Sandete lyder", "subtitles.block.sand.wind": "<PERSON><PERSON><PERSON> lyder", "subtitles.block.sculk.charge": "<PERSON><PERSON><PERSON> bobler", "subtitles.block.sculk.spread": "Sculk sprer seg", "subtitles.block.sculk_catalyst.bloom": "Sculkkatalysator blomstrer", "subtitles.block.sculk_sensor.clicking": "Sculksensor klikker", "subtitles.block.sculk_sensor.clicking_stop": "Sculksensor slutter å klikke", "subtitles.block.sculk_shrieker.shriek": "Sculkskriker skriker", "subtitles.block.shulker_box.close": "Shulkerboks lukkes", "subtitles.block.shulker_box.open": "Shulkerboks åpnes", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON> vaier", "subtitles.block.smithing_table.use": "Smibenk smis på", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON><PERSON><PERSON> ryker", "subtitles.block.sniffer_egg.crack": "Snufseregg sprekker", "subtitles.block.sniffer_egg.hatch": "Snufseregg klekker", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON> plopper", "subtitles.block.sponge.absorb": "Svamp suger", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.close": "<PERSON><PERSON> luk<PERSON>", "subtitles.block.trapdoor.open": "<PERSON><PERSON>", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON> knaker", "subtitles.block.trial_spawner.about_to_spawn_item": "Illevarslende gjenstand forberedes", "subtitles.block.trial_spawner.ambient": "Prøvelsesfremkaller spraker", "subtitles.block.trial_spawner.ambient_charged": "Illevarslende spraking", "subtitles.block.trial_spawner.ambient_ominous": "Illevarslende spraking", "subtitles.block.trial_spawner.charge_activate": "Jærtegn omfavner prøvelsesfremkaller", "subtitles.block.trial_spawner.close_shutter": "Prøvelsesfremkaller stenger seg", "subtitles.block.trial_spawner.detect_player": "Prøvelsesfremkaller lader seg opp", "subtitles.block.trial_spawner.eject_item": "Prøvelsesfremkaller skyter ut gjenstander", "subtitles.block.trial_spawner.ominous_activate": "Jærtegn omfavner prøvelsesfremkaller", "subtitles.block.trial_spawner.open_shutter": "Prøvelsesfremkaller åpner seg", "subtitles.block.trial_spawner.spawn_item": "Illevarslende gjenstand kastes ut", "subtitles.block.trial_spawner.spawn_item_begin": "Illevarslende gjenst<PERSON> dukker opp", "subtitles.block.trial_spawner.spawn_mob": "Skapning fremkalles", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> festes", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.activate": "Hvelv fyrer seg opp", "subtitles.block.vault.ambient": "H<PERSON><PERSON> spraker", "subtitles.block.vault.close_shutter": "Hvelv lukker seg", "subtitles.block.vault.deactivate": "Hvelv slukker", "subtitles.block.vault.eject_item": "Hvelv skyter ut gjenstand", "subtitles.block.vault.insert_item": "Hvelv låses opp", "subtitles.block.vault.insert_item_fail": "Hvelv avviser gje<PERSON>and", "subtitles.block.vault.open_shutter": "Hvelv åpner seg", "subtitles.block.vault.reject_rewarded_player": "Hvelv avviser spiller", "subtitles.block.water.ambient": "<PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "Svamp tørker ut", "subtitles.chiseled_bookshelf.insert": "Bok settes inn", "subtitles.chiseled_bookshelf.insert_enchanted": "Fortryllet bok settes inn", "subtitles.chiseled_bookshelf.take": "Bok tas ut", "subtitles.chiseled_bookshelf.take_enchanted": "Fortryllet bok tas ut", "subtitles.enchant.thorns.hit": "<PERSON><PERSON> stikker", "subtitles.entity.allay.ambient_with_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> leter", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lengter", "subtitles.entity.allay.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.allay.hurt": "Hjelpeånd skades", "subtitles.entity.allay.item_given": "<PERSON><PERSON>lpeånd tar imot", "subtitles.entity.allay.item_taken": "H<PERSON>lpeånd gir fra seg", "subtitles.entity.allay.item_thrown": "H<PERSON>lpeånd kaster", "subtitles.entity.armadillo.ambient": "Beltedyr grynter", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON><PERSON>plate børstes av", "subtitles.entity.armadillo.death": "Beltedyr dør", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> spiser", "subtitles.entity.armadillo.hurt": "Beltedyr skades", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON> verger seg", "subtitles.entity.armadillo.land": "Beltedyr lander", "subtitles.entity.armadillo.peek": "Beltedyr gløtter ut", "subtitles.entity.armadillo.roll": "Beltedyr ruller i hop", "subtitles.entity.armadillo.scute_drop": "Beltedyr feller skjoldplate", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON> ruller seg ut", "subtitles.entity.armadillo.unroll_start": "Beltedyr gløtter ut", "subtitles.entity.armor_stand.fall": "Noe falt", "subtitles.entity.arrow.hit": "<PERSON><PERSON> treffer", "subtitles.entity.arrow.hit_player": "Spiller treffes", "subtitles.entity.arrow.shoot": "<PERSON>l skytes", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON> angriper", "subtitles.entity.axolotl.death": "<PERSON>x<PERSON><PERSON> dør", "subtitles.entity.axolotl.hurt": "Axolotl skades", "subtitles.entity.axolotl.idle_air": "Axolotl kvitrer", "subtitles.entity.axolotl.idle_water": "Axolotl kvitrer", "subtitles.entity.axolotl.splash": "Axolotl plasker", "subtitles.entity.axolotl.swim": "Axolotl svømmer", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Flaggermus tar av", "subtitles.entity.bee.ambient": "Bie summer", "subtitles.entity.bee.death": "<PERSON><PERSON> dør", "subtitles.entity.bee.hurt": "<PERSON><PERSON> skades", "subtitles.entity.bee.loop": "Bie summer", "subtitles.entity.bee.loop_aggressive": "Bie summer sint", "subtitles.entity.bee.pollinate": "<PERSON>ie summer glad", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Flammeskrømt puster", "subtitles.entity.blaze.burn": "Flammeskrømt spraker", "subtitles.entity.blaze.death": "Flammeskrømt dør", "subtitles.entity.blaze.hurt": "Flammeskrømt skades", "subtitles.entity.blaze.shoot": "Flammeskrømt skyter", "subtitles.entity.boat.paddle_land": "Roing", "subtitles.entity.boat.paddle_water": "Roing", "subtitles.entity.bogged.ambient": "<PERSON>mp<PERSON><PERSON> rangler", "subtitles.entity.bogged.death": "Sump<PERSON><PERSON> dør", "subtitles.entity.bogged.hurt": "Sumprangel skades", "subtitles.entity.breeze.charge": "Vindskrømt stormer frem", "subtitles.entity.breeze.death": "Vindskrømt dør", "subtitles.entity.breeze.deflect": "Vindskrømt avbøyer", "subtitles.entity.breeze.hurt": "Vindskrømt skades", "subtitles.entity.breeze.idle_air": "Vindskrømt flyr", "subtitles.entity.breeze.idle_ground": "Vindskrømt virrer", "subtitles.entity.breeze.inhale": "Vindskrømt trekker inn", "subtitles.entity.breeze.jump": "Vindskrømt hopper", "subtitles.entity.breeze.land": "Vindskrømt lander", "subtitles.entity.breeze.shoot": "Vindskrømt skyter", "subtitles.entity.breeze.slide": "Vindskrømt glir", "subtitles.entity.breeze.whirl": "Vindskrømt virvler", "subtitles.entity.breeze.wind_burst": "Vindladning revner", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON> burer", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON> springer", "subtitles.entity.camel.dash_ready": "Dromedar henter seg inn", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> spiser", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.camel.saddle": "Dromedar sales", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON> setter seg ned", "subtitles.entity.camel.stand": "Dromedar reiser seg opp", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON> tr<PERSON>", "subtitles.entity.camel.step_sand": "Dromedar trår i sand", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> tigger", "subtitles.entity.cat.death": "<PERSON><PERSON> dø<PERSON>", "subtitles.entity.cat.eat": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON> maler", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON> klukker", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.cod.death": "Torsk dør", "subtitles.entity.cod.flop": "Torsk spreller", "subtitles.entity.cod.hurt": "Torsk skades", "subtitles.entity.cow.ambient": "<PERSON>", "subtitles.entity.cow.death": "<PERSON> dør", "subtitles.entity.cow.hurt": "Ku skades", "subtitles.entity.cow.milk": "<PERSON> melkes", "subtitles.entity.creaking.activate": "Knirkning følger med", "subtitles.entity.creaking.ambient": "Knirkning knirker", "subtitles.entity.creaking.attack": "Knirkning angriper", "subtitles.entity.creaking.deactivate": "Knirkning stagger", "subtitles.entity.creaking.death": "Knirkning morkner", "subtitles.entity.creaking.freeze": "Knirkning stopper opp", "subtitles.entity.creaking.spawn": "Knirkning lever", "subtitles.entity.creaking.sway": "Knirkning treffes", "subtitles.entity.creaking.twitch": "Knirkning rykker til", "subtitles.entity.creaking.unfreeze": "Knirkning rører seg", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.creeper.hurt": "Creeper skades", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> freser", "subtitles.entity.dolphin.ambient": "Delfin klikker", "subtitles.entity.dolphin.ambient_water": "Delfin plystrer", "subtitles.entity.dolphin.attack": "<PERSON><PERSON> angriper", "subtitles.entity.dolphin.death": "<PERSON><PERSON> dør", "subtitles.entity.dolphin.eat": "<PERSON><PERSON> spiser", "subtitles.entity.dolphin.hurt": "Delfin skades", "subtitles.entity.dolphin.jump": "Delfin hopper", "subtitles.entity.dolphin.play": "Delfin leker", "subtitles.entity.dolphin.splash": "Delfin spruter", "subtitles.entity.dolphin.swim": "<PERSON><PERSON> svø<PERSON>", "subtitles.entity.donkey.ambient": "Eselet skryter", "subtitles.entity.donkey.angry": "Eselet skråler", "subtitles.entity.donkey.chest": "Eselkiste tas på", "subtitles.entity.donkey.death": "<PERSON><PERSON> dør", "subtitles.entity.donkey.eat": "<PERSON><PERSON> s<PERSON>er", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> skades", "subtitles.entity.donkey.jump": "<PERSON>sel hopper", "subtitles.entity.drowned.ambient": "<PERSON><PERSON>g gurgler", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON>g gurgler", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.drowned.hurt": "<PERSON><PERSON>g skades", "subtitles.entity.drowned.shoot": "Draug kaster trefork", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON> trår", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> s<PERSON>ø<PERSON>", "subtitles.entity.egg.throw": "Egg flyr", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> stønner", "subtitles.entity.elder_guardian.ambient_land": "<PERSON>r<PERSON><PERSON><PERSON> spreller", "subtitles.entity.elder_guardian.curse": "Urvokter forbanner", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.elder_guardian.flop": "<PERSON>r<PERSON><PERSON><PERSON> spreller", "subtitles.entity.elder_guardian.hurt": "Urvokter skades", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON> dør", "subtitles.entity.ender_dragon.flap": "Drage slår med vingene", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON> knurrer", "subtitles.entity.ender_dragon.hurt": "Drage skades", "subtitles.entity.ender_dragon.shoot": "Drage skyter", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON><PERSON> faller", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON><PERSON> skytes", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> flyr", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON> vwooper", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON>", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON> stirrer", "subtitles.entity.enderman.teleport": "<PERSON><PERSON><PERSON> teleporterer", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON><PERSON> ska<PERSON>", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON><PERSON><PERSON> forbereder angrep", "subtitles.entity.evoker.prepare_summon": "<PERSON><PERSON><PERSON><PERSON> forbereder sammenkalling", "subtitles.entity.evoker.prepare_wololo": "<PERSON><PERSON><PERSON><PERSON> for<PERSON>er sjarme", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON><PERSON> glefser", "subtitles.entity.experience_orb.pickup": "Erfaring økt", "subtitles.entity.firework_rocket.blast": "Fyrverkeri smeller", "subtitles.entity.firework_rocket.launch": "Fyrverkeri avfyres", "subtitles.entity.firework_rocket.twinkle": "Fyrverkeri spraker", "subtitles.entity.fish.swim": "Plask", "subtitles.entity.fishing_bobber.retrieve": "Dupp dratt inn", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON> plasker", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> kastet", "subtitles.entity.fox.aggro": "Rev utt<PERSON><PERSON> sinne", "subtitles.entity.fox.ambient": "Rev piper", "subtitles.entity.fox.bite": "Rev biter", "subtitles.entity.fox.death": "<PERSON>ør", "subtitles.entity.fox.eat": "<PERSON> spiser", "subtitles.entity.fox.hurt": "<PERSON> skades", "subtitles.entity.fox.screech": "<PERSON>yler", "subtitles.entity.fox.sleep": "Rev snorker", "subtitles.entity.fox.sniff": "<PERSON> s<PERSON>er", "subtitles.entity.fox.spit": "<PERSON>", "subtitles.entity.fox.teleport": "<PERSON> teleporterer", "subtitles.entity.frog.ambient": "Frosk kvekker", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.frog.hurt": "Frosk skades", "subtitles.entity.frog.lay_spawn": "Frosk gyter", "subtitles.entity.frog.long_jump": "Frosk hopper", "subtitles.entity.generic.big_fall": "Noe falt", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "Slurping", "subtitles.entity.generic.eat": "Spising", "subtitles.entity.generic.explode": "Eksplosjon", "subtitles.entity.generic.extinguish_fire": "Ilden dør", "subtitles.entity.generic.hurt": "<PERSON><PERSON> gjø<PERSON>dt", "subtitles.entity.generic.small_fall": "<PERSON>e snubler", "subtitles.entity.generic.splash": "Plasking", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Vind<PERSON>le brister", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> jam<PERSON> seg", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.ghast.shoot": "Ghast skyter", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> kurrer", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.ghastling.hurt": "G<PERSON><PERSON> skades", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> dukker opp", "subtitles.entity.glow_item_frame.add_item": "Gløderamme fylles", "subtitles.entity.glow_item_frame.break": "Gløderamme fjernes", "subtitles.entity.glow_item_frame.place": "Gløderamme plasseres", "subtitles.entity.glow_item_frame.remove_item": "Gløderam<PERSON> tø<PERSON>", "subtitles.entity.glow_item_frame.rotate_item": "Gløderamme klikker", "subtitles.entity.glow_squid.ambient": "Glosprut svømmer", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.glow_squid.hurt": "Glosp<PERSON><PERSON> skades", "subtitles.entity.glow_squid.squirt": "Glospru<PERSON> spruter blekk", "subtitles.entity.goat.ambient": "<PERSON><PERSON> mekrer", "subtitles.entity.goat.death": "<PERSON><PERSON> dør", "subtitles.entity.goat.eat": "<PERSON><PERSON> spiser", "subtitles.entity.goat.horn_break": "Bukkehorn brekker av", "subtitles.entity.goat.hurt": "Geit skades", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> <PERSON><PERSON>er", "subtitles.entity.goat.milk": "<PERSON><PERSON> melkes", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stamper", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON> stanger", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON> trår", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> s<PERSON>ø<PERSON>", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON> skyter", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> ska<PERSON>", "subtitles.entity.happy_ghast.ambient": "Lykkelig ghast fniser", "subtitles.entity.happy_ghast.death": "Lykkelig ghast dør", "subtitles.entity.happy_ghast.equip": "Seletøy festes", "subtitles.entity.happy_ghast.harness_goggles_down": "Lykkelig ghast er klar", "subtitles.entity.happy_ghast.harness_goggles_up": "Lykkelig ghast stopper", "subtitles.entity.happy_ghast.hurt": "Lykkelig ghast skades", "subtitles.entity.happy_ghast.unequip": "Seletøy tas av", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> knurrer", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> knurrer sint", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ang<PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "Hoglin omskapes til Zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.hoglin.hurt": "<PERSON>glin skades", "subtitles.entity.hoglin.retreat": "Hoglin trekker seg tilbake", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> trår", "subtitles.entity.horse.ambient": "<PERSON><PERSON> v<PERSON><PERSON>r", "subtitles.entity.horse.angry": "<PERSON><PERSON> knegger", "subtitles.entity.horse.armor": "Hesterustning blir tatt på", "subtitles.entity.horse.breathe": "<PERSON><PERSON> puster", "subtitles.entity.horse.death": "<PERSON><PERSON> dør", "subtitles.entity.horse.eat": "<PERSON><PERSON> spiser", "subtitles.entity.horse.gallop": "<PERSON><PERSON> galopperer", "subtitles.entity.horse.hurt": "He<PERSON> skades", "subtitles.entity.horse.jump": "Hest hopper", "subtitles.entity.horse.saddle": "Hest sales", "subtitles.entity.husk.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.husk.converted_to_zombie": "Ørkenzombie omskapes til zombie", "subtitles.entity.husk.death": "Ørkenzombie dør", "subtitles.entity.husk.hurt": "Ørkenzombie skades", "subtitles.entity.illusioner.ambient": "Illus<PERSON><PERSON> mumler", "subtitles.entity.illusioner.cast_spell": "Illusjonist kaster trolldom", "subtitles.entity.illusioner.death": "Illusjon<PERSON> dør", "subtitles.entity.illusioner.hurt": "Illusjonist skades", "subtitles.entity.illusioner.mirror_move": "Illusjonist fortrenger", "subtitles.entity.illusioner.prepare_blindness": "Illusjonist for<PERSON>er blindhet", "subtitles.entity.illusioner.prepare_mirror": "Illusjonist forbereder speilbilde", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> angriper", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> blir ø<PERSON>", "subtitles.entity.iron_golem.death": "<PERSON>rnk<PERSON><PERSON><PERSON> dør", "subtitles.entity.iron_golem.hurt": "Jernkjempe skades", "subtitles.entity.iron_golem.repair": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reparert", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "subtitles.entity.item.pickup": "Gjenstand pluk<PERSON> opp", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON> fylles", "subtitles.entity.item_frame.break": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON>k<PERSON><PERSON> slites opp", "subtitles.entity.leash_knot.place": "<PERSON><PERSON>ndk<PERSON><PERSON> knytes", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON> ned", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON>b<PERSON><PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON> brek<PERSON>", "subtitles.entity.llama.angry": "<PERSON> br<PERSON>er sint", "subtitles.entity.llama.chest": "<PERSON><PERSON> settes på lama", "subtitles.entity.llama.death": "<PERSON> d<PERSON>", "subtitles.entity.llama.eat": "<PERSON> spiser", "subtitles.entity.llama.hurt": "<PERSON> skades", "subtitles.entity.llama.spit": "<PERSON> spy<PERSON>", "subtitles.entity.llama.step": "<PERSON> t<PERSON>", "subtitles.entity.llama.swag": "<PERSON> pyn<PERSON>", "subtitles.entity.magma_cube.death": "Magmakube dør", "subtitles.entity.magma_cube.hurt": "Magmakube skades", "subtitles.entity.magma_cube.squish": "Magmakube klasker", "subtitles.entity.minecart.inside": "Gruvevogn skrangler", "subtitles.entity.minecart.inside_underwater": "Gruvevogn skrangler under vann", "subtitles.entity.minecart.riding": "Gruvevogn triller", "subtitles.entity.mooshroom.convert": "Mooshroom forvandles", "subtitles.entity.mooshroom.eat": "Moosh<PERSON> spiser", "subtitles.entity.mooshroom.milk": "Moosh<PERSON> melkes", "subtitles.entity.mooshroom.suspicious_milk": "Moosh<PERSON> blir mist<PERSON><PERSON><PERSON> melket", "subtitles.entity.mule.ambient": "Muldyr skråler", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> knegger", "subtitles.entity.mule.chest": "<PERSON><PERSON> settes på muldyr", "subtitles.entity.mule.death": "Muldyr dør", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> spiser", "subtitles.entity.mule.hurt": "Muldyr skades", "subtitles.entity.mule.jump": "Muldyr hopper", "subtitles.entity.painting.break": "<PERSON><PERSON>", "subtitles.entity.painting.place": "<PERSON><PERSON> henges opp", "subtitles.entity.panda.aggressive_ambient": "Panda grynter", "subtitles.entity.panda.ambient": "<PERSON><PERSON> peser", "subtitles.entity.panda.bite": "Panda biter", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.panda.death": "<PERSON><PERSON> dør", "subtitles.entity.panda.eat": "<PERSON><PERSON> spiser", "subtitles.entity.panda.hurt": "Panda skades", "subtitles.entity.panda.pre_sneeze": "Pandanese kiler", "subtitles.entity.panda.sneeze": "<PERSON>da nyser", "subtitles.entity.panda.step": "<PERSON><PERSON> trår", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.ambient": "Papegø<PERSON> snakker", "subtitles.entity.parrot.death": "Papegøye dør", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON><PERSON><PERSON> spiser", "subtitles.entity.parrot.fly": "<PERSON><PERSON>g<PERSON><PERSON> flagrer", "subtitles.entity.parrot.hurts": "Papegøye skades", "subtitles.entity.parrot.imitate.blaze": "Papegøye puster", "subtitles.entity.parrot.imitate.bogged": "Papeg<PERSON><PERSON> rangler", "subtitles.entity.parrot.imitate.breeze": "Papegøye virrer", "subtitles.entity.parrot.imitate.creaking": "Papegøye knirker", "subtitles.entity.parrot.imitate.creeper": "Papegøye freser", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON> gurgler", "subtitles.entity.parrot.imitate.elder_guardian": "Papegøye stønner", "subtitles.entity.parrot.imitate.ender_dragon": "Papeg<PERSON><PERSON> brøler", "subtitles.entity.parrot.imitate.endermite": "Papegøye løper", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON>g<PERSON><PERSON> mum<PERSON>", "subtitles.entity.parrot.imitate.ghast": "Papegøye jamrer seg", "subtitles.entity.parrot.imitate.guardian": "Papegøye stønner", "subtitles.entity.parrot.imitate.hoglin": "Papegø<PERSON> knurrer", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON><PERSON><PERSON> sukker", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON>g<PERSON><PERSON> mum<PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "Papegøye klasker", "subtitles.entity.parrot.imitate.phantom": "Papegøye skriker", "subtitles.entity.parrot.imitate.piglin": "Papegøye grynter", "subtitles.entity.parrot.imitate.piglin_brute": "Papegøye grynter", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON>g<PERSON><PERSON> mum<PERSON>", "subtitles.entity.parrot.imitate.ravager": "Papegøye grynter", "subtitles.entity.parrot.imitate.shulker": "Papegøye lurer", "subtitles.entity.parrot.imitate.silverfish": "Papegøye freser", "subtitles.entity.parrot.imitate.skeleton": "Papeg<PERSON><PERSON> rangler", "subtitles.entity.parrot.imitate.slime": "Papegøye klasker", "subtitles.entity.parrot.imitate.spider": "Papegøye freser", "subtitles.entity.parrot.imitate.stray": "Papeg<PERSON><PERSON> rangler", "subtitles.entity.parrot.imitate.vex": "Papegøye plager", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON>g<PERSON><PERSON> mum<PERSON>", "subtitles.entity.parrot.imitate.warden": "Papegøye syter", "subtitles.entity.parrot.imitate.witch": "Papegøye fniser", "subtitles.entity.parrot.imitate.wither": "Papegøye blir sint", "subtitles.entity.parrot.imitate.wither_skeleton": "Papeg<PERSON><PERSON> rangler", "subtitles.entity.parrot.imitate.zoglin": "Papegø<PERSON> knurrer", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON><PERSON><PERSON> sukker", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON><PERSON><PERSON> sukker", "subtitles.entity.phantom.ambient": "<PERSON><PERSON> skriker", "subtitles.entity.phantom.bite": "Fantom biter", "subtitles.entity.phantom.death": "<PERSON><PERSON> dør", "subtitles.entity.phantom.flap": "<PERSON><PERSON> flakser", "subtitles.entity.phantom.hurt": "Fantom skades", "subtitles.entity.phantom.swoop": "Fantom hugger til", "subtitles.entity.pig.ambient": "<PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "Gris sales", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> beundrer gjenstand", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> grynter", "subtitles.entity.piglin.angry": "<PERSON>lin grynter sint", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feirer", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> blir forvandlet til <PERSON>glin", "subtitles.entity.piglin.death": "<PERSON><PERSON> dør", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> skades", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> grynter misunnsom", "subtitles.entity.piglin.retreat": "<PERSON>lin trekker seg tilbake", "subtitles.entity.piglin.step": "<PERSON><PERSON> trår", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.piglin_brute.angry": "Piglinbølle grynter sint", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglinbølle omdannes til zombiepiglin", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.piglin_brute.hurt": "Piglinbølle skades", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON><PERSON> trår", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON> mum<PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON> jubler", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON> skades", "subtitles.entity.player.attack.crit": "Kritisk angrep", "subtitles.entity.player.attack.knockback": "Tilbakeslagsangrep", "subtitles.entity.player.attack.strong": "Sterkt angrep", "subtitles.entity.player.attack.sweep": "Sveipende angrep", "subtitles.entity.player.attack.weak": "Svakt angrep", "subtitles.entity.player.burp": "Rap", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.player.freeze_hurt": "Spiller fryser", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> drukner", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> brenner", "subtitles.entity.player.levelup": "<PERSON>piller plinger", "subtitles.entity.player.teleport": "Spiller teleporterer", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.ambient_baby": "Isbjørnunge brummer", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON> knuser", "subtitles.entity.potion.throw": "Flaske kastes", "subtitles.entity.puffer_fish.blow_out": "Kulefisk krymper", "subtitles.entity.puffer_fish.blow_up": "Kulefisk blåser seg opp", "subtitles.entity.puffer_fish.death": "Kulefisk dør", "subtitles.entity.puffer_fish.flop": "Kulefisk spreller", "subtitles.entity.puffer_fish.hurt": "Kulefisk skades", "subtitles.entity.puffer_fish.sting": "Kulefisk stikker", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON> piper", "subtitles.entity.rabbit.attack": "<PERSON><PERSON> an<PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON> dør", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> skades", "subtitles.entity.rabbit.jump": "Kanin hopper", "subtitles.entity.ravager.ambient": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.ravager.attack": "Herjer biter", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON> j<PERSON>", "subtitles.entity.ravager.death": "<PERSON><PERSON> dør", "subtitles.entity.ravager.hurt": "<PERSON><PERSON> skades", "subtitles.entity.ravager.roar": "<PERSON><PERSON>ø<PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON> trå<PERSON>", "subtitles.entity.ravager.stunned": "<PERSON><PERSON> la<PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON> dør", "subtitles.entity.salmon.flop": "<PERSON><PERSON> spreller", "subtitles.entity.salmon.hurt": "Laks skades", "subtitles.entity.sheep.ambient": "<PERSON><PERSON> breker", "subtitles.entity.sheep.death": "<PERSON><PERSON> dør", "subtitles.entity.sheep.hurt": "Sau skades", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> ligger på lur", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> lukker seg", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> seg", "subtitles.entity.shulker.shoot": "<PERSON>lk<PERSON> skyter", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleporterer", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON><PERSON> smeller", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> revner", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON>v<PERSON><PERSON> freser", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.silverfish.hurt": "Sølvkre skades", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON> rangler", "subtitles.entity.skeleton.converted_to_stray": "Skjelett omdannes til vandrer", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.skeleton.hurt": "Skjelett skades", "subtitles.entity.skeleton.shoot": "Skjelett skyter", "subtitles.entity.skeleton_horse.ambient": "Skjeletthest skriker", "subtitles.entity.skeleton_horse.death": "Skjeletthest dør", "subtitles.entity.skeleton_horse.hurt": "Skjeletthest skades", "subtitles.entity.skeleton_horse.jump_water": "Skjeletthest hopper", "subtitles.entity.skeleton_horse.swim": "Skjeletthest svømmer", "subtitles.entity.slime.attack": "<PERSON> ang<PERSON>", "subtitles.entity.slime.death": "<PERSON> d<PERSON>", "subtitles.entity.slime.hurt": "<PERSON> skades", "subtitles.entity.slime.squish": "<PERSON> klasker", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON><PERSON> graver", "subtitles.entity.sniffer.digging_stop": "Snufser reiser seg opp", "subtitles.entity.sniffer.drop_seed": "Snufser snufser fram frø", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON> spiser", "subtitles.entity.sniffer.egg_crack": "Snufseregg sprekker", "subtitles.entity.sniffer.egg_hatch": "Snufseregg klekker", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON> behager seg", "subtitles.entity.sniffer.hurt": "Snufser skades", "subtitles.entity.sniffer.idle": "Snufser grynter", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON> snøfter", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON> sø<PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON> snufser", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON>er trår", "subtitles.entity.snow_golem.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.snow_golem.hurt": "<PERSON>nømann skades", "subtitles.entity.snowball.throw": "Snøball flyr", "subtitles.entity.spider.ambient": "Edderkopp freser", "subtitles.entity.spider.death": "Edderkopp dør", "subtitles.entity.spider.hurt": "Edderkopp skades", "subtitles.entity.squid.ambient": "Blekksprut svømmer", "subtitles.entity.squid.death": "Blekksprut dør", "subtitles.entity.squid.hurt": "Blekksprut skades", "subtitles.entity.squid.squirt": "Blekksprut spruter blekk", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON> rangler", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON> ska<PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> spiser", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON> synger", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "<PERSON>va<PERSON>drer trekker seg tilbake", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON> spreller", "subtitles.entity.tadpole.grow_up": "Rumpetroll vokser opp", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> skades", "subtitles.entity.tnt.primed": "TNT freser", "subtitles.entity.tropical_fish.death": "Sydhavsfisk dør", "subtitles.entity.tropical_fish.flop": "Sydhavsfisk spreller", "subtitles.entity.tropical_fish.hurt": "Sydhavsfisk skades", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.death": "Skilpadde dør", "subtitles.entity.turtle.death_baby": "Skilpaddeunge dør", "subtitles.entity.turtle.egg_break": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> knuser", "subtitles.entity.turtle.egg_crack": "Skilpaddeegg sprekker", "subtitles.entity.turtle.egg_hatch": "<PERSON>lpad<PERSON><PERSON><PERSON> klekker", "subtitles.entity.turtle.hurt": "Skilpadde skades", "subtitles.entity.turtle.hurt_baby": "Skilpaddeunge skades", "subtitles.entity.turtle.lay_egg": "Skilpadde legger egg", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON><PERSON> kaver", "subtitles.entity.turtle.shamble_baby": "Skilpaddeunge kaver", "subtitles.entity.turtle.swim": "Skilpadde svømmer", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON><PERSON> plager", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON>nd skriker", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON>nd dør", "subtitles.entity.vex.hurt": "Plageånd skades", "subtitles.entity.villager.ambient": "<PERSON>g<PERSON> mumler", "subtitles.entity.villager.celebrate": "Byg<PERSON> jubler", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.villager.hurt": "Bygding skades", "subtitles.entity.villager.no": "Bygding avslår", "subtitles.entity.villager.trade": "Bygding handler", "subtitles.entity.villager.work_armorer": "Rustningssmed jobber", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON> jobber", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_fletcher": "Pilmaker jobber", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_librarian": "Bibliotekar jobber", "subtitles.entity.villager.work_mason": "<PERSON><PERSON> jobber", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ber", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON> jobber", "subtitles.entity.villager.yes": "Bygding godtar", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>ler", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON> ska<PERSON>", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON> mumler", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.wandering_trader.disappeared": "Vandrehandler forsvinner", "subtitles.entity.wandering_trader.drink_milk": "Vandrehandler drikker melk", "subtitles.entity.wandering_trader.drink_potion": "Vandrehandler drikker brygg", "subtitles.entity.wandering_trader.hurt": "Vandrehandler skades", "subtitles.entity.wandering_trader.no": "Vandrehandler avslår", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON><PERSON> dukker opp", "subtitles.entity.wandering_trader.trade": "Vandrehandler handler", "subtitles.entity.wandering_trader.yes": "<PERSON>dre<PERSON><PERSON> godtar", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON> stø<PERSON> sint", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON> s<PERSON>r", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON> raser", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON> s<PERSON> til", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> dø<PERSON>", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> graver", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON> opp", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> hjerte dunker", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> merker seg", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> merker seg sint", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON>er tar seg nærmere", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON>er tar seg fram", "subtitles.entity.warden.nearby_closest": "<PERSON><PERSON><PERSON> nærmer seg", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON> s<PERSON>er", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> lader opp", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.tendril_clicks": "Forvarers følehorn klikker", "subtitles.entity.wind_charge.throw": "Vindladning flyr", "subtitles.entity.wind_charge.wind_burst": "Vindladning revner", "subtitles.entity.witch.ambient": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> j<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON> dør", "subtitles.entity.witch.drink": "<PERSON><PERSON> drikker", "subtitles.entity.witch.hurt": "<PERSON><PERSON> skades", "subtitles.entity.witch.throw": "<PERSON><PERSON> kaster", "subtitles.entity.wither.ambient": "<PERSON><PERSON> blir sint", "subtitles.entity.wither.death": "<PERSON><PERSON> dør", "subtitles.entity.wither.hurt": "<PERSON><PERSON> skades", "subtitles.entity.wither.shoot": "With<PERSON> angriper", "subtitles.entity.wither.spawn": "Wither slippes løs", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> rangler", "subtitles.entity.wither_skeleton.death": "Withersk<PERSON><PERSON> dør", "subtitles.entity.wither_skeleton.hurt": "Witherskjelett skades", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON> peser", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON> knurrer", "subtitles.entity.wolf.hurt": "Ulv skades", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON> peser", "subtitles.entity.wolf.shake": "Ulv rister på seg", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> knurrer", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> knurrer sint", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> angriper", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> trår", "subtitles.entity.zombie.ambient": "Zombie sukker", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> brytes opp", "subtitles.entity.zombie.converted_to_drowned": "Zombie omskapes til draug", "subtitles.entity.zombie.death": "Zombie dør", "subtitles.entity.zombie.destroy_egg": "Skilpaddeegg trampes på", "subtitles.entity.zombie.hurt": "Zombie skades", "subtitles.entity.zombie.infect": "Zombie infiserer", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> skriker", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> dør", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> skades", "subtitles.entity.zombie_villager.ambient": "<PERSON><PERSON><PERSON><PERSON> sukker", "subtitles.entity.zombie_villager.converted": "Zombiebygding helbredes", "subtitles.entity.zombie_villager.cure": "Zombiebygding snøfter", "subtitles.entity.zombie_villager.death": "Zombiebygding dør", "subtitles.entity.zombie_villager.hurt": "Zombiebygding skades", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON>n grynter", "subtitles.entity.zombified_piglin.angry": "Zombiepiglin grynter sint", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON> dør", "subtitles.entity.zombified_piglin.hurt": "Zombiepiglin skades", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON><PERSON><PERSON> slår rot", "subtitles.event.mob_effect.raid_omen": "<PERSON><PERSON>g ruger i nærheten", "subtitles.event.mob_effect.trial_omen": "Illevarslende prøvelse ruger i nærheten", "subtitles.event.raid.horn": "Illevarslende horn ljomer", "subtitles.item.armor.equip": "Utstyr tas på", "subtitles.item.armor.equip_chain": "Brynjerustning skrangler", "subtitles.item.armor.equip_diamond": "Diamantrustning klanger", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> rasler", "subtitles.item.armor.equip_gold": "<PERSON>ull<PERSON>ning klinger", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON> klanger", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON><PERSON><PERSON> rasler", "subtitles.item.armor.equip_netherite": "Netherittrustning klinger", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dunker", "subtitles.item.armor.equip_wolf": "Ulverustning spennes på", "subtitles.item.armor.unequip_wolf": "Ulverustning klippes av", "subtitles.item.axe.scrape": "<PERSON><PERSON> skraper", "subtitles.item.axe.strip": "<PERSON><PERSON>", "subtitles.item.axe.wax_off": "Voks av", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON> rasler", "subtitles.item.book.page_turn": "Side rasler", "subtitles.item.book.put": "Bok plasseres", "subtitles.item.bottle.empty": "Flaske tø<PERSON>s", "subtitles.item.bottle.fill": "<PERSON>laske fylles", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON> bø<PERSON>", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.item.brush.brushing.sand": "<PERSON> børstes", "subtitles.item.brush.brushing.sand.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON>ø<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON> fyl<PERSON>", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> øst opp", "subtitles.item.bucket.fill_fish": "Fisk fanges", "subtitles.item.bucket.fill_tadpole": "Rumpetroll fanges", "subtitles.item.bundle.drop_contents": "<PERSON>unt tømmes", "subtitles.item.bundle.insert": "<PERSON>g pakkes ned", "subtitles.item.bundle.insert_fail": "Bunt full", "subtitles.item.bundle.remove_one": "<PERSON>g pak<PERSON> opp", "subtitles.item.chorus_fruit.teleport": "Spiller teleporteres", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON> s<PERSON>s", "subtitles.item.crossbow.charge": "<PERSON><PERSON><PERSON><PERSON><PERSON> spennes", "subtitles.item.crossbow.hit": "<PERSON><PERSON> treffer", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON><PERSON> lades", "subtitles.item.crossbow.shoot": "Armbrøst avfyres", "subtitles.item.dye.use": "<PERSON><PERSON> farger", "subtitles.item.elytra.flying": "Susing", "subtitles.item.firecharge.use": "Ildladning blusser opp", "subtitles.item.flintandsteel.use": "Tennst<PERSON>l klikker", "subtitles.item.glow_ink_sac.use": "Glosekk skvetter", "subtitles.item.goat_horn.play": "Bukkehorn spilles", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON> snur jord", "subtitles.item.honey_bottle.drink": "Belming", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON>s på", "subtitles.item.horse_armor.unequip": "Hesterustning klippes av", "subtitles.item.ink_sac.use": "Blekksekk skvetter", "subtitles.item.lead.break": "<PERSON> ryker", "subtitles.item.lead.tied": "Tau knytes", "subtitles.item.lead.untied": "Tau knytes opp", "subtitles.item.llama_carpet.unequip": "Teppe klippes av", "subtitles.item.lodestone_compass.lock": "Kompass tilknyttes leidarstein", "subtitles.item.mace.smash_air": "Stridsklubbe smadrer", "subtitles.item.mace.smash_ground": "Stridsklubbe smadrer", "subtitles.item.nether_wart.plant": "<PERSON><PERSON><PERSON> s<PERSON>s", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> knuser", "subtitles.item.saddle.unequip": "Sal klippes av", "subtitles.item.shears.shear": "Saks klikker", "subtitles.item.shears.snip": "Saks klipper", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "subtitles.item.shovel.flatten": "Spade flater til", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON> fork<PERSON><PERSON>", "subtitles.item.spyglass.use": "Kikkert forlenges", "subtitles.item.totem.use": "Totem aktiveres", "subtitles.item.trident.hit": "Trefork stikker", "subtitles.item.trident.hit_ground": "Trefork vibrerer", "subtitles.item.trident.return": "Trefork kommer tilbake", "subtitles.item.trident.riptide": "Trefork fyker", "subtitles.item.trident.throw": "Trefork klinger", "subtitles.item.trident.thunder": "Treforktorden braker", "subtitles.item.wolf_armor.break": "Ulverustning går i stykker", "subtitles.item.wolf_armor.crack": "Ulverustning sprekker", "subtitles.item.wolf_armor.damage": "Ulverustning tar skade", "subtitles.item.wolf_armor.repair": "Ulverustning repareres", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "Kart tegnes", "subtitles.ui.hud.bubble_pop": "Pustmåler synker", "subtitles.ui.loom.take_result": "Vevstol vever", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON> kutter", "subtitles.weather.rain": "<PERSON><PERSON> faller", "symlink_warning.message": "Innlasting av verdener fra mapper med symbolske lenker kan være utrygt om du ikke vet nøyaktig hva du gjør. Vennligst besøk %s for å finne ut mer.", "symlink_warning.message.pack": "Innlasting av pakker med symbolske lenker kan være utrygt om du ikke vet nøyaktig hva du gjør. Vennligst besøk %s for å finne ut mer.", "symlink_warning.message.world": "Innlasting av verdener fra mapper med symbolske lenker kan være utrygt om du ikke vet nøyaktig hva du gjør. Vennligst besøk %s for å finne ut mer.", "symlink_warning.more_info": "<PERSON><PERSON>", "symlink_warning.title": "Verdensmappe inneholder symbolske lenker", "symlink_warning.title.pack": "<PERSON><PERSON>(r) lagt til inneholder symbolske lenker", "symlink_warning.title.world": "Verdensmappen inneholder symbolske lenker", "team.collision.always": "Alltid", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "D<PERSON>t andre lag", "team.collision.pushOwnTeam": "Dytt eget lag", "team.notFound": "\"%s\" er et ukjent lag", "team.visibility.always": "Alltid", "team.visibility.hideForOtherTeams": "Skjul fra andre lag", "team.visibility.hideForOwnTeam": "Skjul fra eget lag", "team.visibility.never": "<PERSON><PERSON><PERSON>", "telemetry.event.advancement_made.description": "Forståelse av forutsetningene for fremskrittsoppnåelse kan hjelpe oss med å forbedre fremgangsmåten i spillet.", "telemetry.event.advancement_made.title": "Fremskritt gjort", "telemetry.event.game_load_times.description": "<PERSON><PERSON> hendelsen måler hvor lang tid som brukes i hver oppstartsfase, og kan hjelpe oss med å finne ut hvor det trengs ytelsesforbedringer under oppstart.", "telemetry.event.game_load_times.title": "Spillinnlastingstider", "telemetry.event.optional": "%s (valgfri)", "telemetry.event.optional.disabled": "%s (valgfritt) - Slått av", "telemetry.event.performance_metrics.description": "Å kjenne til den alminnelige ytelsesprofilen til Minecraft hjelper oss med å justere og optimere spillet for et vidt utvalg av maskinvarespesifikasjoner og operativsystemer. \nSpillversjon er inkludert for å hjelpe oss med å sammenligne ytelsesprofilen til nye versjoner av Minecraft.", "telemetry.event.performance_metrics.title": "Ytelsesmål", "telemetry.event.required": "%s (nødvendig)", "telemetry.event.world_load_times.description": "Det er viktig for oss å forstå hvor lang tid det tar å bli med på en verden, og hvordan det endrer seg over tid. For eksempel: når vi legger til nye funksjoner eller gjør større tekniske endringer må vi se på virkningene det har på innlastningstidene.", "telemetry.event.world_load_times.title": "Innlastingstider for verden", "telemetry.event.world_loaded.description": "Å vite hvordan spillere spiller Minecraft (som spillmodus, klient- eller server moddet og spillversjon) lar oss rette spilloppdateringer til å forbedre områdene som spillere bryr seg mest om.\n«Verden innlastet»-hendelsen knyttes til «Verden losset»-hendelsen for å beregne hvor lenge spilleøkten varte.", "telemetry.event.world_loaded.title": "Verden lastet inn", "telemetry.event.world_unloaded.description": "Denne hendelsen er paret med 'verdensinnlasting'-hende<PERSON>en som sammen avgjør hvor lenge verdensøkten varte.\nVarigheten (i sekunder og tikk) leses av når verdensøkten tar slutt (ved å gå ut til hovedmenyen eller koble fra en server).", "telemetry.event.world_unloaded.title": "Verden losset", "telemetry.property.advancement_game_time.title": "S<PERSON>lltid (tikk)", "telemetry.property.advancement_id.title": "Fremskritt-ID", "telemetry.property.client_id.title": "Klient-ID", "telemetry.property.client_modded.title": "Moddet klient", "telemetry.property.dedicated_memory_kb.title": "Avsatt minne (kB)", "telemetry.property.event_timestamp_utc.title": "Hendelses-tidsstempel (UTC)", "telemetry.property.frame_rate_samples.title": "Prøver av bildefrekvens (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "Spillversjon", "telemetry.property.launcher_name.title": "Oppstarternavn", "telemetry.property.load_time_bootstrap_ms.title": "Oppstartstid (millisekunder)", "telemetry.property.load_time_loading_overlay_ms.title": "Tid på innlastingsskjerm (millisekunder)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON>id før vind<PERSON> (millisekunder)", "telemetry.property.load_time_total_time_ms.title": "Total innlastingstid (millisekunder)", "telemetry.property.minecraft_session_id.title": "Minecraft-økt-ID", "telemetry.property.new_world.title": "<PERSON><PERSON> verden", "telemetry.property.number_of_samples.title": "<PERSON><PERSON><PERSON> prøver", "telemetry.property.operating_system.title": "Operativsystem", "telemetry.property.opt_in.title": "Samtykke", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Realms-nivåinnhold (Minispillnavn)", "telemetry.property.render_distance.title": "Skildringsvidde", "telemetry.property.render_time_samples.title": "Skildringstidprøver", "telemetry.property.seconds_since_load.title": "Tid siden innlasting (sekunder)", "telemetry.property.server_modded.title": "Moddet server", "telemetry.property.server_type.title": "Servertype", "telemetry.property.ticks_since_load.title": "Tid siden innlasting (tikk)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON><PERSON> (RAM)", "telemetry.property.user_id.title": "Bruker-<PERSON>", "telemetry.property.world_load_time_ms.title": "Innlastingstid for verden (millisekunder)", "telemetry.property.world_session_id.title": "Verdens-økt-ID", "telemetry_info.button.give_feedback": "Gi tilbakemelding", "telemetry_info.button.privacy_statement": "Personvernerklæring", "telemetry_info.button.show_data": "Vis mine data", "telemetry_info.opt_in.description": "<PERSON><PERSON> sam<PERSON> til å sende valgfrie telemetridata", "telemetry_info.property_title": "Inneholdt data", "telemetry_info.screen.description": "Å samle slik data hjelper oss å forbedre Minecraft, ved at den leder oss i retninger som er betydningsfulle for våre spillere.\nDu kan også sende inn særskilt tilbakemelding som hjelper oss å videre forbedre Minecraft.", "telemetry_info.screen.title": "Innsamling av telemetridata", "test.error.block_property_mismatch": "Forventet egenskap %s å være %s, var %s", "test.error.block_property_missing": "Blokkegenskap mangler, forventet egenskap %s å være %s", "test.error.entity_property": "Enhet %s besto ikke test: %s", "test.error.entity_property_details": "Enhet %s besto ikke test: %s, forventet: %s, var: %s", "test.error.expected_block": "Forventet blokk %s, fikk %s", "test.error.expected_block_tag": "Forventet blokk i #%s, fikk %s", "test.error.expected_container_contents": "Beholder forventes å inneholde: %s", "test.error.expected_container_contents_single": "Beholder forventes å ha en enkelt: %s", "test.error.expected_empty_container": "Beholder forventes å være tom", "test.error.expected_entity": "Forventet %s", "test.error.expected_entity_around": "Forventet %s å finnes omkring %s, %s, %s", "test.error.expected_entity_count": "Forventet %s enhet(er) av type %s, fant %s", "test.error.expected_entity_data": "Forventet enhetsdata å være: %s, var %s", "test.error.expected_entity_data_predicate": "Enhetsdata passer ikke med %s", "test.error.expected_entity_effect": "Forventet at %s skulle ha effekten %s %s", "test.error.expected_entity_having": "Enhets inventar forventes å inneholde %s", "test.error.expected_entity_holding": "Enhet forventes å holde %s", "test.error.expected_entity_in_test": "Forventet at %s fantes i testen", "test.error.expected_entity_not_touching": "Forventet ikke at %s skulle røre %s, %s, %s (relativt: %s, %s, %s)", "test.error.expected_entity_touching": "Forventet at %s skulle røre %s, %s, %s (relativt: %s, %s, %s)", "test.error.expected_item": "Forventet gjenstand av type %s", "test.error.expected_items_count": "Forventet %s gjenstander av type %s, fant %s", "test.error.fail": "Feilbetingelser møtte", "test.error.invalid_block_type": "Uforventet blokktype funnet: %s", "test.error.missing_block_entity": "<PERSON><PERSON> blokken<PERSON>t", "test.error.position": "%s på %s, %s, %s (relativt: %s, %s, %s) på tikk %s", "test.error.sequence.condition_already_triggered": "Betingelse allerede utløst på %s", "test.error.sequence.condition_not_triggered": "Betingelse ikke utløst", "test.error.sequence.invalid_tick": "Lyktes på feil tikk: forventet %s", "test.error.sequence.not_completed": "Test gikk tom for tid før se<PERSON><PERSON> fullførte", "test.error.set_biome": "Lyktes ikke med å sette markslag for testen", "test.error.spawn_failure": "Lyktes ikke med å skape enhet %s", "test.error.state_not_equal": "Feil tilstand. Forventet %s, var %s", "test.error.structure.failure": "Lyktes ikke med å plassere teststruktur for %s", "test.error.tick": "%s på tikk %s", "test.error.ticking_without_structure": "Tikking av test før plassering av struktur", "test.error.timeout.no_result": "Intet resultat innen %s tikk", "test.error.timeout.no_sequences_finished": "Ingen sekvenser fullført innen %s tikk", "test.error.too_many_entities": "Forventet at kun én %s fantes omkring %s, %s, %s, men fant %s", "test.error.unexpected_block": "Forventet ikke at blokk var %s", "test.error.unexpected_entity": "Forventet ikke at %s fantes", "test.error.unexpected_item": "Forventet ikke gjenstand av type %s", "test.error.unknown": "Ukjent indre feil: %s", "test.error.value_not_equal": "Forventet at %s skulle være %s, var %s", "test.error.wrong_block_entity": "Feil blokkenhetstype: %s", "test_block.error.missing": "Teststruktur mangler %s-blokk", "test_block.error.too_many": "For mange %s-blokker", "test_block.invalid_timeout": "Ugyldig tidsavbrudd (%s) - må være et positivt antall tikk", "test_block.message": "Melding:", "test_block.mode.accept": "Bestå", "test_block.mode.fail": "Feilslå", "test_block.mode.log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Beståmodus - <PERSON> for å bestå (delvis) en test", "test_block.mode_info.fail": "Feilmodus - Feilslå testen", "test_block.mode_info.log": "Loggmodus - Loggfør en melding", "test_block.mode_info.start": "Startmodus - Startpunktet til en test", "test_instance.action.reset": "Tilbakestill og last inn", "test_instance.action.run": "Last inn og kjør", "test_instance.action.save": "<PERSON><PERSON>r struktur", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Mislyktes: %s", "test_instance.description.function": "Funksjon: %s", "test_instance.description.invalid_id": "Ugyldig test-ID", "test_instance.description.no_test": "Ingen slik test", "test_instance.description.structure": "Struktur: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Blokkbasert test", "test_instance.type.function": "Innebygd funksjonstest", "test_instance_block.entities": "Enheter:", "test_instance_block.error.no_test": "Kan ikke kjøre testinstans på %s, %s, %s fordi den har en udefinert test", "test_instance_block.error.no_test_structure": "Kan ikke kjøre testinstans på %s, %s, %s fordi den ikke har en teststruktur", "test_instance_block.error.unable_to_save": "Kan ikke lagre teststrukturmal til testinstand på %s, %s, %s", "test_instance_block.invalid": "[u<PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Tilbakestilling lyktes for test: %s", "test_instance_block.rotation": "Rotasjon:", "test_instance_block.size": "Teststrukturstørrelse", "test_instance_block.starting": "Starter test %s", "test_instance_block.test_id": "Testinstans-ID", "title.32bit.deprecation": "Fant 32-bitsystem. I fremtiden kan dette hindre deg fra å spille, da et 64-bitsystem vil kreves!", "title.32bit.deprecation.realms": "<PERSON>nart vil Minecraft kreve et 64-bitsystem, som vil hindre deg fra å kjøre Realms på denne maskinen. Om du således vil avbryte Realms-abonnementet må du gjøre det selv.", "title.32bit.deprecation.realms.check": "<PERSON><PERSON><PERSON> vis dette vinduet igjen", "title.32bit.deprecation.realms.header": "Fant 32-bitsystem", "title.credits": "Opphavsrett Mojang AB. Må ikke distribueres!", "title.multiplayer.disabled": "Flerspiller er sperret. Vennligst sjekk innstillingene til Microsoft-kontoen din.", "title.multiplayer.disabled.banned.name": "Du må endre navnet ditt før du kan spille på nett", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON><PERSON> din er permanent utestengt fra nettspill", "title.multiplayer.disabled.banned.temporary": "Kontoen din er midlertidig utestengt fra nettspill", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON> (tredjepartserver)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Enspiller", "translation.test.args": "%s %s", "translation.test.complex": "Prefiks, %s%2$s igjen %s og %1$s til slutt %s, og også %1$s igjen!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hei %", "translation.test.invalid2": "hei %s", "translation.test.none": "Hallo, verden!", "translation.test.world": "verden", "trim_material.minecraft.amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.resin": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Lynaktig rustningspryd", "trim_pattern.minecraft.coast": "Forlist rustning<PERSON>ryd", "trim_pattern.minecraft.dune": "Dynens rustningspryd", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON><PERSON> rustningspryd", "trim_pattern.minecraft.flow": "Virvlende rustningspryd", "trim_pattern.minecraft.host": "Vertens rustningspryd", "trim_pattern.minecraft.raiser": "Oppdretterens rustningspryd", "trim_pattern.minecraft.rib": "Ribbeinsaktig rustningspryd", "trim_pattern.minecraft.sentry": "P<PERSON><PERSON>t rustningspryd", "trim_pattern.minecraft.shaper": "Formgiverens rustningspryd", "trim_pattern.minecraft.silence": "<PERSON><PERSON>tens rustningspryd", "trim_pattern.minecraft.snout": "Snuteaktig rustningspryd", "trim_pattern.minecraft.spire": "Spirens rustningspryd", "trim_pattern.minecraft.tide": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "trim_pattern.minecraft.vex": "Plageåndens rustningspryd", "trim_pattern.minecraft.ward": "Forvart rustningspryd", "trim_pattern.minecraft.wayfinder": "Veiviserens rustningspryd", "trim_pattern.minecraft.wild": "Vill rustningspryd", "tutorial.bundleInsert.description": "Legg til ting med høyreklikk", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON> en bunt", "tutorial.craft_planks.description": "Oppskriftsboken kan hjelpe", "tutorial.craft_planks.title": "Lag treplanker", "tutorial.find_tree.description": "<PERSON>lå det for å samle tre", "tutorial.find_tree.title": "Finn et tre", "tutorial.look.description": "Bruk datamusen din for å snu deg", "tutorial.look.title": "Se deg rundt", "tutorial.move.description": "Hopp med %s", "tutorial.move.title": "Beveg deg med %s, %s, %s, og %s", "tutorial.open_inventory.description": "Trykk på %s", "tutorial.open_inventory.title": "<PERSON><PERSON><PERSON> inventaret ditt", "tutorial.punch_tree.description": "Hold nede %s", "tutorial.punch_tree.title": "Ødelegg treet", "tutorial.socialInteractions.description": "Trykk på %s for å åpne", "tutorial.socialInteractions.title": "Samspill", "upgrade.minecraft.netherite_upgrade": "Netherittoppgradering"}