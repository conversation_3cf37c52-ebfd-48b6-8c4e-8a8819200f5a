{"accessibility.onboarding.accessibility.button": "Axustes de accesibilidade...", "accessibility.onboarding.screen.narrator": "Preme Entrar para activar o narrador", "accessibility.onboarding.screen.title": "Benvido ao Minecraft!\n\nGustaríache habilitar o Narrador ou visitar os Axustes de accesibilidade?", "addServer.add": "<PERSON><PERSON>", "addServer.enterIp": "Enderezo do servidor", "addServer.enterName": "Nome do servidor", "addServer.resourcePack": "Paquetes de recursos", "addServer.resourcePack.disabled": "Desactivado", "addServer.resourcePack.enabled": "Activado", "addServer.resourcePack.prompt": "Confirmar", "addServer.title": "Editar información do servidor", "advMode.command": "Comando de consola", "advMode.mode": "Modo", "advMode.mode.auto": "<PERSON><PERSON>r", "advMode.mode.autoexec.bat": "Sempre activo", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "<PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Precisa redstone", "advMode.mode.sequence": "En cadea", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "Tes que ser un administrador (OP) no modo creativo", "advMode.notEnabled": "Os bloques de comandos non están activados neste servidor", "advMode.previousOutput": "Saída do comando anterior", "advMode.setCommand": "Axusta un comando de consola para o bloque", "advMode.setCommand.success": "Comando aplicado: %s", "advMode.trackOutput": "<PERSON><PERSON> resultado", "advMode.triggering": "Activando", "advMode.type": "Tipo", "advancement.advancementNotFound": "Proeza descoñecida: %s", "advancements.adventure.adventuring_time.description": "Descobre todos os biomas", "advancements.adventure.adventuring_time.title": "Hora de aventuras", "advancements.adventure.arbalistic.description": "Mata a cinco criaturas únicas cun único tiro de bésta", "advancements.adventure.arbalistic.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.avoid_vibration.description": "Camiña agochado preto dun sensor de sculk ou dun vixilante para evitar ser detectado", "advancements.adventure.avoid_vibration.title": "Sixilo 100", "advancements.adventure.blowback.description": "Mata un remuíño devolvéndolle unha carga de vento", "advancements.adventure.blowback.title": "A vida é un sopro", "advancements.adventure.brush_armadillo.description": "<PERSON><PERSON><PERSON> as escamas dun armadillo cun pincel", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.bullseye.description": "Dispara ao centro dun albo dende polo menos 30 metros", "advancements.adventure.bullseye.title": "<PERSON><PERSON> de lince", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fai un tarro decorado con 4 fragmentos de cerámica", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauración coidadosa", "advancements.adventure.crafters_crafting_crafters.description": "Observa un fabricador cando fabrique outro fabricador", "advancements.adventure.crafters_crafting_crafters.title": "Fabricador fabricando fabricadores", "advancements.adventure.fall_from_world_height.description": "Cae dende o máis alto do mundo (límite de altura) até o máis baixo e vive para contalo", "advancements.adventure.fall_from_world_height.title": "Covas e cantís", "advancements.adventure.heart_transplanter.description": "Coloca un corazón de crepitante entre dous troncos de carballo esbrancuxado", "advancements.adventure.heart_transplanter.title": "Cardioloxía", "advancements.adventure.hero_of_the_village.description": "Defende unha aldea dunha invasión", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON> <PERSON> vila", "advancements.adventure.honey_block_slide.description": "Brinca nun bloque de mel para amortecer a túa caída", "advancements.adventure.honey_block_slide.title": "Situación pegañenta", "advancements.adventure.kill_a_mob.description": "Mata un monstro hostil", "advancements.adventure.kill_a_mob.title": "Cazador de monstros", "advancements.adventure.kill_all_mobs.description": "Mata un monstro de cada tipo", "advancements.adventure.kill_all_mobs.title": "Sen piedade", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Mata unha criatura preto dun catalizador de sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Efecto <PERSON>", "advancements.adventure.lighten_up.description": "Raspa unha lámpada de cobre cunha machada para que brille máis", "advancements.adventure.lighten_up.title": "Luz de presenza", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protexe un aldeán dun lóstrego sen provocar un incendio", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Alta tensión", "advancements.adventure.minecraft_trials_edition.description": "Entra nunha sala de desafío", "advancements.adventure.minecraft_trials_edition.title": "Desafío aceptado!", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> unha bésta", "advancements.adventure.ol_betsy.title": "A vella Betsy", "advancements.adventure.overoverkill.description": "Causa 50 corazóns de dano cun só golpe de clava", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON> bai<PERSON>o", "advancements.adventure.play_jukebox_in_meadows.description": "Dálle vida ás pradarías coa música dun tocadiscos", "advancements.adventure.play_jukebox_in_meadows.title": "O son da música", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Le a sinal de enerxía dun andel de libros cicelado cun comparador", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "O saber é poder", "advancements.adventure.revaulting.description": "Abre unha arca sombría cunha chave de desafío sombría", "advancements.adventure.revaulting.title": "Caixa forte", "advancements.adventure.root.description": "Aventura, exploración e combate", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Cepilla un bloque sospeitoso para obter un fragmento de cerámica", "advancements.adventure.salvage_sherd.title": "Respectando o pasado", "advancements.adventure.shoot_arrow.description": "Dispara a algo cunha frecha", "advancements.adventure.shoot_arrow.title": "Apunta ben", "advancements.adventure.sleep_in_bed.description": "Durme nunha cama para mudar o teu punto de reaparición", "advancements.adventure.sleep_in_bed.title": "Doces soños", "advancements.adventure.sniper_duel.description": "Mata un esqueleto dende polo menos 50 metros de distancia", "advancements.adventure.sniper_duel.title": "Duelo de francotiradores", "advancements.adventure.spyglass_at_dragon.description": "Mira o Dragón do Ender a través duns anteollos", "advancements.adventure.spyglass_at_dragon.title": "É un avión?", "advancements.adventure.spyglass_at_ghast.description": "Mira un ghast a través duns anteollos", "advancements.adventure.spyglass_at_ghast.title": "É un globo?", "advancements.adventure.spyglass_at_parrot.description": "Mira un papagaio a través duns anteollos", "advancements.adventure.spyglass_at_parrot.title": "É un paxaro?", "advancements.adventure.summon_iron_golem.description": "Invoca un gólem de ferro para que te axude a defender a aldea", "advancements.adventure.summon_iron_golem.title": "Gardacostas", "advancements.adventure.throw_trident.description": "T<PERSON><PERSON><PERSON> un tridente a algo.\nNota: tirar a túa única arma non é unha boa idea.", "advancements.adventure.throw_trident.title": "Era visto!", "advancements.adventure.totem_of_undying.description": "Emprega un tótem da inmortalidade para enganar á morte", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Fai un troco cun aldeán", "advancements.adventure.trade.title": "Que choio!", "advancements.adventure.trade_at_world_height.description": "Fai un troco cun aldeán no limite de altura do mundo", "advancements.adventure.trade_at_world_height.title": "Prezos polas nubes", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplica estes moldes de ferraría polo menos unha vez: agulla, fociño, costela, garda, silencio, espírito, marea, guía", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "Decora unha armadura nunha mesa de ferraría", "advancements.adventure.trim_with_any_armor_pattern.title": "Á última moda", "advancements.adventure.two_birds_one_arrow.description": "Mata dúas pantasmas furán<PERSON>lles cunha soa frecha", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON> p<PERSON>, unha frecha", "advancements.adventure.under_lock_and_key.description": "Abre unha arca cunha chave de desafío", "advancements.adventure.under_lock_and_key.title": "Baixo sete chaves", "advancements.adventure.use_lodestone.description": "Usa un compás nunha magnetita", "advancements.adventure.use_lodestone.title": "Un camiño con volta", "advancements.adventure.very_very_frightening.description": "Acerta a un aldeán cun raio", "advancements.adventure.very_very_frightening.title": "Moi moi aterrecedor", "advancements.adventure.voluntary_exile.description": "Mata ao capitán dunha invasión. \n<PERSON><PERSON> mellor manterse afastado das aldeas por un tempo...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON>o volunta<PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camiña sobre neve en po... sen afundirte", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "advancements.adventure.who_needs_rockets.description": "Usa unha carga de vento para te lanzares 8 bloques cara arriba", "advancements.adventure.who_needs_rockets.title": "Cun foguete no cu!", "advancements.adventure.whos_the_pillager_now.description": "<PERSON><PERSON>lle ao saqueador da súa propia mencina", "advancements.adventure.whos_the_pillager_now.title": "Quen é o saqueador agora?", "advancements.empty": "Parece que aquí non hai nada...", "advancements.end.dragon_breath.description": "Recolle o bafo do dragón nun frasco de vidro", "advancements.end.dragon_breath.title": "Cheira a mortizo!", "advancements.end.dragon_egg.description": "Obtén o ovo de dragón", "advancements.end.dragon_egg.title": "A nova xeración", "advancements.end.elytra.description": "Atopa uns élitros", "advancements.end.elytra.title": "O ceo é o límite", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON> <PERSON> illa", "advancements.end.enter_end_gateway.title": "Fuxida conseguida", "advancements.end.find_end_city.description": "Adiante! Que podería pasar?", "advancements.end.find_end_city.title": "A cidade ao final do xogo", "advancements.end.kill_dragon.description": "<PERSON>a sorte", "advancements.end.kill_dragon.title": "Libera o End", "advancements.end.levitate.description": "Levita máis de 50 bloques co ataque dun shulker", "advancements.end.levitate.title": "Que vistas dende aquí!", "advancements.end.respawn_dragon.description": "Volve invocar ao dragón do Ender", "advancements.end.respawn_dragon.title": "O End... Outra vez...", "advancements.end.root.description": "Ou o comezo?", "advancements.end.root.title": "O End, a fin", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fai que un allay solte unha torta xunta un bloque de notas", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Parabéns para ti", "advancements.husbandry.allay_deliver_item_to_player.description": "Fai que un allay che traia obxectos", "advancements.husbandry.allay_deliver_item_to_player.title": "Hai un amigo en min", "advancements.husbandry.axolotl_in_a_bucket.description": "Apaña un axolote nun balde", "advancements.husbandry.axolotl_in_a_bucket.title": "O predador máis adorable", "advancements.husbandry.balanced_diet.description": "Come todo o que poidas malia que non sexa saudable", "advancements.husbandry.balanced_diet.title": "Unha dieta equilibrada", "advancements.husbandry.breed_all_animals.description": "Reproduce todos os tipos de animais!", "advancements.husbandry.breed_all_animals.title": "De dous en dous", "advancements.husbandry.breed_an_animal.description": "Xunta dous animais para reproducilos", "advancements.husbandry.breed_an_animal.title": "O amor está no ar", "advancements.husbandry.complete_catalogue.description": "Domestica todas as variantes de gatos!", "advancements.husbandry.complete_catalogue.title": "Un gatálogo completo", "advancements.husbandry.feed_snifflet.description": "Alimenta unha cría de cheirador", "advancements.husbandry.feed_snifflet.title": "Pequenos cheirumes", "advancements.husbandry.fishy_business.description": "Pesca un peixe", "advancements.husbandry.fishy_business.title": "Un asunto escamoso", "advancements.husbandry.froglights.description": "Ten todas as luces anfibias no inventario", "advancements.husbandry.froglights.title": "Cos nosos poderes combinados!", "advancements.husbandry.kill_axolotl_target.description": "Alíate cun axolote e gaña unha pelexa", "advancements.husbandry.kill_axolotl_target.title": "A unión fai a forza!", "advancements.husbandry.leash_all_frog_variants.description": "Atrapa cada variante de ra cunha renda", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON> tres <PERSON>", "advancements.husbandry.make_a_sign_glow.description": "Fai brillar o texto dun letreiro", "advancements.husbandry.make_a_sign_glow.title": "Que idea brillante!", "advancements.husbandry.netherite_hoe.description": "Usa un lingote de netherita para mellorar un sacho e analiza o que acabas de facer coa túa vida", "advancements.husbandry.netherite_hoe.title": "Dedicación seria", "advancements.husbandry.obtain_sniffer_egg.description": "Obtén un ovo de cheirador", "advancements.husbandry.obtain_sniffer_egg.title": "Cheirume interesante", "advancements.husbandry.place_dried_ghast_in_water.description": "Coloca un bloque de ghast seco na auga", "advancements.husbandry.place_dried_ghast_in_water.title": "Mantente hidratado!", "advancements.husbandry.plant_any_sniffer_seed.description": "Planta calquera semente achada por un cheirador", "advancements.husbandry.plant_any_sniffer_seed.title": "Sementando o pasado", "advancements.husbandry.plant_seed.description": "Planta unha semente e mira como medra", "advancements.husbandry.plant_seed.title": "<PERSON>dran tan rápido...", "advancements.husbandry.remove_wolf_armor.description": "Quítalle a armadura a un lobo cunhas tesoiras", "advancements.husbandry.remove_wolf_armor.title": "Tarefa delicada", "advancements.husbandry.repair_wolf_armor.description": "Arranxa unha armadura de lobo danada con escamas de armadillo", "advancements.husbandry.repair_wolf_armor.title": "Novo do trinque", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Navega nunha barca cunha cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Cabra á vista!", "advancements.husbandry.root.description": "O mundo está cheo de amigos e comida", "advancements.husbandry.root.title": "Prosperidade", "advancements.husbandry.safely_harvest_honey.description": "Usa unha cacharela para recoller mel dunha abellariza empregando un frasco de vidro sen amolar ás abellas", "advancements.husbandry.safely_harvest_honey.title": "A porta está abella", "advancements.husbandry.silk_touch_nest.description": "Move unha colmea con 3 abellas dentro empregando toque de seda", "advancements.husbandry.silk_touch_nest.title": "<PERSON><PERSON><PERSON> que marchar", "advancements.husbandry.tactical_fishing.description": "Pesca un peixe... sen cana de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca táctica", "advancements.husbandry.tadpole_in_a_bucket.description": "Apaña un cágado nun balde", "advancements.husbandry.tadpole_in_a_bucket.title": "Cágado ao cubo", "advancements.husbandry.tame_an_animal.description": "Domestica a un animal", "advancements.husbandry.tame_an_animal.title": "Mellores amigos para sempre", "advancements.husbandry.wax_off.description": "Raspa a cera dun bloque de cobre!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Encera a un bloque de cobre cun favo!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON> cera", "advancements.husbandry.whole_pack.description": "Domestica todas as variantes de lobos", "advancements.husbandry.whole_pack.title": "O grupo ao completo", "advancements.nether.all_effects.description": "Obtén todos os efectos ao mesmo tempo", "advancements.nether.all_effects.title": "Como chegamos aquí?", "advancements.nether.all_potions.description": "Obtén todos os efectos de pocións ao mesmo tempo", "advancements.nether.all_potions.title": "Mistura explosiva", "advancements.nether.brew_potion.description": "Fermenta unha poción", "advancements.nether.brew_potion.title": "Destilaría local", "advancements.nether.charge_respawn_anchor.description": "Carga unha áncora de reaparición ao máximo", "advancements.nether.charge_respawn_anchor.title": "Máis vidas ca un gato", "advancements.nether.create_beacon.description": "Constrúe e coloca un faro", "advancements.nether.create_beacon.title": "Fágase a luz", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON>lle a máxima potencia a un faro", "advancements.nether.create_full_beacon.title": "Faroneitor", "advancements.nether.distract_piglin.description": "Distrae aos piglins con ouro", "advancements.nether.distract_piglin.title": "Febre do ouro", "advancements.nether.explore_nether.description": "Explora todos os biomas do Nether", "advancements.nether.explore_nether.title": "Turismo do demo", "advancements.nether.fast_travel.description": "Emprega o Nether para viaxar 7 km na superficie", "advancements.nether.fast_travel.title": "Burato negro", "advancements.nether.find_bastion.description": "Entra nun bastión en ruínas", "advancements.nether.find_bastion.title": "Tempos do<PERSON>dos", "advancements.nether.find_fortress.description": "Adéntrate nunha fortaleza do Nether", "advancements.nether.find_fortress.title": "Unha terrible fortaleza", "advancements.nether.get_wither_skull.description": "Obt<PERSON> unha caveira de es<PERSON><PERSON>", "advancements.nether.get_wither_skull.title": "Por unha cachola", "advancements.nether.loot_bastion.description": "Saquea un cofre nun bastión en ruínas", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON> bravos", "advancements.nether.netherite_armor.description": "Obtén unha armadura enteira de netherita", "advancements.nether.netherite_armor.title": "<PERSON><PERSON> duro de roer", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON><PERSON> de<PERSON>tos ancestrais", "advancements.nether.obtain_ancient_debris.title": "Agochado nas profundidades", "advancements.nether.obtain_blaze_rod.description": "Obtén unha vara de blaze", "advancements.nether.obtain_blaze_rod.title": "En chamas", "advancements.nether.obtain_crying_obsidian.description": "Obtén obsidiana chorona", "advancements.nether.obtain_crying_obsidian.title": "Quen está a cortar cebolas?", "advancements.nether.return_to_sender.description": "Mata un ghast devolvéndolle a súa bóla de lume", "advancements.nether.return_to_sender.title": "Devolver ao remitente", "advancements.nether.ride_strider.description": "<PERSON>a nun vagante cun fungo deformado nun pau", "advancements.nether.ride_strider.title": "Este barco ten pernas", "advancements.nether.ride_strider_in_overworld_lava.description": "Monta nun vagante e dá un loooongo paseo por un lago de lava fóra do Nether", "advancements.nether.ride_strider_in_overworld_lava.title": "Lava, doce lava", "advancements.nether.root.description": "Trae roupa de verán", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Invoca ao With<PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON> treboe<PERSON>", "advancements.nether.uneasy_alliance.description": "Rescata un ghast do <PERSON>her, tráeo san e salvo á superficie... e despois mátao", "advancements.nether.uneasy_alliance.title": "Falsa al<PERSON>za", "advancements.nether.use_lodestone.description": "Usa un compás nunha magnetita", "advancements.nether.use_lodestone.title": "Un camiño con volta", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilita e cura a un zombi aldeán", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON> z<PERSON>", "advancements.story.deflect_arrow.description": "Desvía un proxectil cun escudo", "advancements.story.deflect_arrow.title": "Hoxe non, grazas", "advancements.story.enchant_item.description": "Encanta un obxecto nunha mesa de encantamentos", "advancements.story.enchant_item.title": "Aprendiz de feiticeiro", "advancements.story.enter_the_end.description": "Entra no portal do End", "advancements.story.enter_the_end.title": "A fin?", "advancements.story.enter_the_nether.description": "Constrúe, activa e entra no portal do Nether", "advancements.story.enter_the_nether.title": "Temos que ir máis abaixo", "advancements.story.follow_ender_eye.description": "Segue un ollo do Ender", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON> espía", "advancements.story.form_obsidian.description": "Obtén un bloque de obsidiana", "advancements.story.form_obsidian.title": "Fusión <PERSON>", "advancements.story.iron_tools.description": "Mellora o teu pico", "advancements.story.iron_tools.title": "A revolución do ferro", "advancements.story.lava_bucket.description": "Enche un balde con lava", "advancements.story.lava_bucket.title": "A cousa ferve!", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "Diamantes!", "advancements.story.mine_stone.description": "Mina rocha co teu novo pico", "advancements.story.mine_stone.title": "<PERSON>de da Pedra", "advancements.story.obtain_armor.description": "Protéxete cunha peza de armadura de ferro", "advancements.story.obtain_armor.title": "Vístete", "advancements.story.root.description": "O corazón e a historia do xogo", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "A armadura de diamante salva vidas", "advancements.story.shiny_gear.title": "Cúbreme de diamantes", "advancements.story.smelt_iron.description": "Funde un lingote de ferro", "advancements.story.smelt_iron.title": "A Idade do Ferro", "advancements.story.upgrade_tools.description": "Fai un pico mellor", "advancements.story.upgrade_tools.title": "Mellorando ferramentas", "advancements.toast.challenge": "Desafío completado!", "advancements.toast.goal": "Obxectivo atinxido!", "advancements.toast.task": "Proeza feita!", "argument.anchor.invalid": "A posición de ancoraxe da entidade non é válida: %s", "argument.angle.incomplete": "Incompleto (requírese 1 ángulo)", "argument.angle.invalid": "<PERSON><PERSON>ulo non válido", "argument.block.id.invalid": "Tipo de bloque descoñecido: %s", "argument.block.property.duplicate": "A propiedade \"%s\" do bloque %s só pode establecerse unha vez", "argument.block.property.invalid": "O bloque %s non acepta \"%s\" para a propiedade \"%s\"", "argument.block.property.novalue": "Requírese un valor para a propiedade \"%s\" do bloque %s", "argument.block.property.unclosed": "Esperábase o peche \"]\" para as propiedades de estado do bloque", "argument.block.property.unknown": "O bloque %s non posúe a propiedade \"%s\"", "argument.block.tag.disallowed": "Non se permiten etiquetas (#) aquí, só bloques actuáis", "argument.color.invalid": "Cor descoñecida: %s", "argument.component.invalid": "Componente de chat inválido: %s", "argument.criteria.invalid": "Criterio descoñecido: '%s'", "argument.dimension.invalid": "Dimensión \"%s\" descoñecida", "argument.double.big": "O valor double non pode ser maior que %s, atopouse %s", "argument.double.low": "O valor double non pode ser menor que %s, atopouse %s", "argument.entity.invalid": "Nome ou UUID non válido", "argument.entity.notfound.entity": "Non foi atopada ningunha entidade", "argument.entity.notfound.player": "Non foi atopado ningún xogador", "argument.entity.options.advancements.description": "Xogadores con proezas", "argument.entity.options.distance.description": "Distancia á entidade", "argument.entity.options.distance.negative": "A distancia non pode ser negativa", "argument.entity.options.dx.description": "Entidades entre x e x + dx", "argument.entity.options.dy.description": "Entidades entre y e y + dy", "argument.entity.options.dz.description": "Entidades entre z e z + dz", "argument.entity.options.gamemode.description": "Xogadores no modo de xogo", "argument.entity.options.inapplicable": "A opción \"%s\" non pode aplicarse aquí", "argument.entity.options.level.description": "Nivel de experiencia", "argument.entity.options.level.negative": "O nivel non pode ser negativo", "argument.entity.options.limit.description": "Número máximo de entidades a devolver", "argument.entity.options.limit.toosmall": "O límite debe ser polo menos 1", "argument.entity.options.mode.invalid": "Modo de xogo non válido ou descoñecido: \"%s\"", "argument.entity.options.name.description": "Nome da entidade", "argument.entity.options.nbt.description": "Entidades co NBT", "argument.entity.options.predicate.description": "Predicado personalizado", "argument.entity.options.scores.description": "Entidades con puntuación", "argument.entity.options.sort.description": "Ordenar as entidades", "argument.entity.options.sort.irreversible": "Tipo de clasificación \"%s\" non válido ou descoñecido", "argument.entity.options.tag.description": "Entidades coa etiqueta", "argument.entity.options.team.description": "Entidades no equipo", "argument.entity.options.type.description": "Entidades do tipo", "argument.entity.options.type.invalid": "Tipo de entidade non válido ou descoñecido: \"%s\"", "argument.entity.options.unknown": "Opción descoñecida: %s", "argument.entity.options.unterminated": "Requírese \"]\" para pechar as opcións", "argument.entity.options.valueless": "Esperábase un valor para a opción \"%s\"", "argument.entity.options.x.description": "posición x", "argument.entity.options.x_rotation.description": "Rotación x da entidade", "argument.entity.options.y.description": "posición y", "argument.entity.options.y_rotation.description": "Rotación y da entidade", "argument.entity.options.z.description": "posición z", "argument.entity.selector.allEntities": "Tódalas entidades", "argument.entity.selector.allPlayers": "Todos os xogadores", "argument.entity.selector.missing": "Falta o tipo de selector", "argument.entity.selector.nearestEntity": "Entidade máis preto", "argument.entity.selector.nearestPlayer": "<PERSON>oga<PERSON> preto", "argument.entity.selector.not_allowed": "Selector non permitido", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON> ao chou", "argument.entity.selector.self": "Entidade actual", "argument.entity.selector.unknown": "Tipo de selector descoñecido: \"%s\"", "argument.entity.toomany": "Só unha entidade está permitida, pero o selector indicado permite máis dunha", "argument.enum.invalid": "Valor \"%s\" non válido", "argument.float.big": "O valor float non pode ser maior que %s, atopouse %s", "argument.float.low": "O valor float non pode ser menor que %s, atopouse %s", "argument.gamemode.invalid": "Modo de xogo descoñecido: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "ID inválida", "argument.id.unknown": "ID descoñecida: %s", "argument.integer.big": "O número enteiro non pode ser maior que %s, atopouse %s", "argument.integer.low": "O número enteiro non pode ser menor que %s, atopouse %s", "argument.item.id.invalid": "Obxecto descoñecido: %s", "argument.item.tag.disallowed": "Non se permiten etiquetas (#) aquí, só obxectos actuáis", "argument.literal.incorrect": "Requírese o valor literal \"%s\"", "argument.long.big": "O valor long non pode ser maior que %s (atopouse: %s)", "argument.long.low": "O valor long non pode ser menor que %s (atopouse: %s)", "argument.message.too_long": "Mensaxe de chat era demasiado longo (%s > máximo %s caracteres)", "argument.nbt.array.invalid": "O tipo de matriz non é válido: '%s'", "argument.nbt.array.mixed": "Non se pode insertar %s en %s", "argument.nbt.expected.compound": "Agardábase etiqueta composta", "argument.nbt.expected.key": "Esperábase unha clave", "argument.nbt.expected.value": "Esperábase un valor", "argument.nbt.list.mixed": "Non se pode insertar %s na lista de %s", "argument.nbt.trailing": "Hai datos de máis", "argument.player.entities": "Só os xogadores poden verse afectados por este comando, pero o selector indicado inclúe entidades", "argument.player.toomany": "Só se permite un xogador, pero o selector indicado permite máis dun", "argument.player.unknown": "O xogador non existe", "argument.pos.missing.double": "Esperábase unha coordenada", "argument.pos.missing.int": "Esperábase unha posición de bloque", "argument.pos.mixed": "Non se pode mesturar as coordenadas mundiais e locais (todo debe empregar ^ ou non)", "argument.pos.outofbounds": "Esta posición está fóra dos límites permitidos.", "argument.pos.outofworld": "Esa posición está fóra do mundo!", "argument.pos.unloaded": "Esa posición non está cargada", "argument.pos2d.incomplete": "Incompleto (requírense 2 coordenadas)", "argument.pos3d.incomplete": "Incompleto (requírense 3 coordenadas)", "argument.range.empty": "Esperábase un valor ou un intervalo de valores", "argument.range.ints": "<PERSON><PERSON> se permiten números enteiros, non decimais", "argument.range.swapped": "O mínimo non pode ser maior ca o máximo", "argument.resource.invalid_type": "O elemento '%s' ten un tipo incorrecto '%s' (esperado '%s')", "argument.resource.not_found": "Non se pode atopar o elemento '%s' do tipo '%s'", "argument.resource_or_id.failed_to_parse": "Erro ao analizar a estrutura: %s", "argument.resource_or_id.invalid": "ID ou etiqueta non válidas", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "Non hai coincidencias para selector \"%s\" de tipo \"%s\"", "argument.resource_tag.invalid_type": "A etiqueta '%s' ten un tipo incorrecto '%s' (esperábase '%s')", "argument.resource_tag.not_found": "Non pode atopar etiqueta '%s' de tipo '%s'", "argument.rotation.incomplete": "Incompleto (requírense 2 coordenadas)", "argument.scoreHolder.empty": "Non se puideron atopar marcadores de puntuación relevantes", "argument.scoreboardDisplaySlot.invalid": "Espazo de mostra descoñecido: %s", "argument.style.invalid": "Estilo in<PERSON>: %s", "argument.time.invalid_tick_count": "A cantidade de ciclos non debe ser negativa", "argument.time.invalid_unit": "Unidade inválida", "argument.time.tick_count_too_low": "A cantidade de ciclos non debe ser inferior a %s, atopou %s", "argument.uuid.invalid": "UUID non válido", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Etiqueta de bloque descoñecida: %s", "arguments.function.tag.unknown": "Etiqueta de función descoñecida: %s", "arguments.function.unknown": "Función descoñecida: %s", "arguments.item.component.expected": "Esperábase un compoñente", "arguments.item.component.malformed": "O compoñente \"%s\" non é válido: \"%s\"", "arguments.item.component.repeated": "O compoñente %s está repetido, mais só se pode especificar un valor", "arguments.item.component.unknown": "Compoñente descoñecido \"%s\"", "arguments.item.malformed": "Obxecto mal formado: %s", "arguments.item.overstacked": "%s só pode apilarse ata %s", "arguments.item.predicate.malformed": "O predicado \"%s\" non é válido: \"%s\"", "arguments.item.predicate.unknown": "Predicado de obxecto descoñecido: \"%s\"", "arguments.item.tag.unknown": "Etiqueta de obxecto descoñecida: \"%s\"", "arguments.nbtpath.node.invalid": "Ruta NBT non válida", "arguments.nbtpath.nothing_found": "Non foron atopados elementos correspondentes con %s", "arguments.nbtpath.too_deep": "Resultado NBT demasiado profundamente aniñado", "arguments.nbtpath.too_large": "Resultado NBT demasiado grande", "arguments.objective.notFound": "Obxectivo de táboa de puntación descoñecido: %s", "arguments.objective.readonly": "O obxectivo \"%s\" da táboa de puntuación é só de lectura", "arguments.operation.div0": "Non se pode dividir entre cero", "arguments.operation.invalid": "Operación inválida", "arguments.swizzle.invalid": "Eixes non válidos, requírese a combinación de \"x\", \"y\" e \"z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%2$s: %1$s%%", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "%2$s: +%1$s", "attribute.modifier.plus.1": "%2$s: +%1$s%%", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "%2$s: -%1$s", "attribute.modifier.take.1": "%2$s: -%1$s%%", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Resistencia da armadura", "attribute.name.attack_damage": "<PERSON>o por ataque", "attribute.name.attack_knockback": "Empurrón de ataque", "attribute.name.attack_speed": "Velocidade de ataque", "attribute.name.block_break_speed": "Velocidade mineira", "attribute.name.block_interaction_range": "Alcance de interacción con bloques", "attribute.name.burning_time": "Duración de queimadura", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON>ámara", "attribute.name.entity_interaction_range": "Alcance de interacción con entidades", "attribute.name.explosion_knockback_resistance": "Resistencia ao empurrón das explosións", "attribute.name.fall_damage_multiplier": "Multiplicador de dano por caída", "attribute.name.flying_speed": "Velocidade de voo", "attribute.name.follow_range": "Rango de detección de criaturas", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Resistencia da armadura", "attribute.name.generic.attack_damage": "<PERSON>o de <PERSON>aque", "attribute.name.generic.attack_knockback": "Empurrón de ataque", "attribute.name.generic.attack_speed": "Velocidade de ataque", "attribute.name.generic.block_interaction_range": "Alcance de interacción con bloques", "attribute.name.generic.burning_time": "Duración de queimadura", "attribute.name.generic.entity_interaction_range": "Alcance de interacción con entidades", "attribute.name.generic.explosion_knockback_resistance": "Resistencia ao empurrón das explosións", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de dano por caída", "attribute.name.generic.flying_speed": "Velocidade de voo", "attribute.name.generic.follow_range": "Rango de detección de criaturas", "attribute.name.generic.gravity": "Gravidade", "attribute.name.generic.jump_strength": "Forza do brinco", "attribute.name.generic.knockback_resistance": "Resistencia ao empurrón", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Absorción máxima", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Eficiencia do movemento", "attribute.name.generic.movement_speed": "Velocidade", "attribute.name.generic.oxygen_bonus": "Osíxeno adicional", "attribute.name.generic.safe_fall_distance": "Distancia de caída segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Altura dos pasos", "attribute.name.generic.water_movement_efficiency": "Eficiencia de movemento na auga", "attribute.name.gravity": "Gravidade", "attribute.name.horse.jump_strength": "Forza de brinco do cabalo", "attribute.name.jump_strength": "Forza do brinco", "attribute.name.knockback_resistance": "Resistencia ao empurrón", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Absorción máxima", "attribute.name.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "Eficiencia mineira", "attribute.name.movement_efficiency": "Eficiencia de movemento", "attribute.name.movement_speed": "Velocidade", "attribute.name.oxygen_bonus": "Osíxeno adicional", "attribute.name.player.block_break_speed": "Velocidade mineira", "attribute.name.player.block_interaction_range": "Alcance de interacción con bloques", "attribute.name.player.entity_interaction_range": "Alcance de interacción con entidades", "attribute.name.player.mining_efficiency": "Eficiencia mineira", "attribute.name.player.sneaking_speed": "Velocidade ao agocharse", "attribute.name.player.submerged_mining_speed": "Velocidade mineira mergullado", "attribute.name.player.sweeping_damage_ratio": "Taxa de dano con alcance", "attribute.name.safe_fall_distance": "Distancia de caída segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocidade ao agocharse", "attribute.name.spawn_reinforcements": "Reforzos de zombis", "attribute.name.step_height": "Altura dos pasos", "attribute.name.submerged_mining_speed": "Velocidade mineira mergullado", "attribute.name.sweeping_damage_ratio": "Taxa de dano con alcance", "attribute.name.tempt_range": "Rango de atracción de criaturas", "attribute.name.water_movement_efficiency": "Eficiencia de movemento na auga", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "Reforzos de zombis", "biome.minecraft.badlands": "<PERSON><PERSON> ermas", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON><PERSON> de <PERSON>", "biome.minecraft.basalt_deltas": "Deltas de basalto", "biome.minecraft.beach": "Praia", "biome.minecraft.birch_forest": "Bosque de bidueiros", "biome.minecraft.cherry_grove": "Cerdeiral", "biome.minecraft.cold_ocean": "Océano frío", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "Bosque escuro", "biome.minecraft.deep_cold_ocean": "Océano frío profundo", "biome.minecraft.deep_dark": "Escuridade profunda", "biome.minecraft.deep_frozen_ocean": "Océano xeado profundo", "biome.minecraft.deep_lukewarm_ocean": "Océano morno profundo", "biome.minecraft.deep_ocean": "Océano profundo", "biome.minecraft.desert": "Deserto", "biome.minecraft.dripstone_caves": "Covas de espeleotema", "biome.minecraft.end_barrens": "Zona árida do End", "biome.minecraft.end_highlands": "Outeiros do End", "biome.minecraft.end_midlands": "Zona media do End", "biome.minecraft.eroded_badlands": "<PERSON>s ermas <PERSON>", "biome.minecraft.flower_forest": "Bosque floral", "biome.minecraft.forest": "Bosque", "biome.minecraft.frozen_ocean": "Océano xeado", "biome.minecraft.frozen_peaks": "Cumios xeados", "biome.minecraft.frozen_river": "Río xeado", "biome.minecraft.grove": "Arboreda", "biome.minecraft.ice_spikes": "Picos de xeo", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Océano morno", "biome.minecraft.lush_caves": "Covas vizosas", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON>", "biome.minecraft.meadow": "Pradaría", "biome.minecraft.mushroom_fields": "Campo de cogomelos", "biome.minecraft.nether_wastes": "Desertos do Nether", "biome.minecraft.ocean": "<PERSON><PERSON>ano", "biome.minecraft.old_growth_birch_forest": "Bosque de bidueiros antigos", "biome.minecraft.old_growth_pine_taiga": "Taiga de piñeiros antigos", "biome.minecraft.old_growth_spruce_taiga": "Taiga de abetos antigos", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON>", "biome.minecraft.river": "Río", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Meseta de sabana", "biome.minecraft.small_end_islands": "Illas pequenas do End", "biome.minecraft.snowy_beach": "Praia nevada", "biome.minecraft.snowy_plains": "<PERSON><PERSON> ne<PERSON>", "biome.minecraft.snowy_slopes": "<PERSON><PERSON>", "biome.minecraft.snowy_taiga": "Taiga nevada", "biome.minecraft.soul_sand_valley": "Val de ánimas", "biome.minecraft.sparse_jungle": "Selva dispersa", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON> rochosos", "biome.minecraft.stony_shore": "Costa rochosa", "biome.minecraft.sunflower_plains": "Chairas de xirasois", "biome.minecraft.swamp": "Pantano", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "O End", "biome.minecraft.the_void": "<PERSON> baleiro", "biome.minecraft.warm_ocean": "Océano quente", "biome.minecraft.warped_forest": "Bosque deformado", "biome.minecraft.windswept_forest": "Bosque ventado", "biome.minecraft.windswept_gravelly_hills": "Outeiros pedregosos ventados", "biome.minecraft.windswept_hills": "Outeiros ventados", "biome.minecraft.windswept_savanna": "<PERSON><PERSON> ventada", "biome.minecraft.wooded_badlands": "<PERSON>s ermas bos<PERSON>as", "block.minecraft.acacia_button": "Botón de acacia", "block.minecraft.acacia_door": "Porta de acacia", "block.minecraft.acacia_fence": "Valado de acacia", "block.minecraft.acacia_fence_gate": "Cancela de acacia", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON> colgante de acacia", "block.minecraft.acacia_leaves": "Follas de acacia", "block.minecraft.acacia_log": "Tronco de acacia", "block.minecraft.acacia_planks": "Táboas de acacia", "block.minecraft.acacia_pressure_plate": "Placa de presión de acacia", "block.minecraft.acacia_sapling": "Gromo de acacia", "block.minecraft.acacia_sign": "Letreiro de acacia", "block.minecraft.acacia_slab": "Lastra de acacia", "block.minecraft.acacia_stairs": "Escaleiras de acacia", "block.minecraft.acacia_trapdoor": "Zapón de acacia", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante de acacia de parede", "block.minecraft.acacia_wall_sign": "Letreiro de acacia de parede", "block.minecraft.acacia_wood": "Madeira de acacia", "block.minecraft.activator_rail": "<PERSON><PERSON>l activador", "block.minecraft.air": "Ar", "block.minecraft.allium": "Allo bravo", "block.minecraft.amethyst_block": "Bloque de ametista", "block.minecraft.amethyst_cluster": "Acio de ametista", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON> ancestrais", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Lastra de andesita", "block.minecraft.andesite_stairs": "Escaleiras de andesita", "block.minecraft.andesite_wall": "Muro de andesita", "block.minecraft.anvil": "Engra", "block.minecraft.attached_melon_stem": "Talo de sandía unido", "block.minecraft.attached_pumpkin_stem": "Talo de cabaza unido", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Follas de azalea", "block.minecraft.azure_bluet": "Rubiácea", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "Bloque de bam<PERSON>ú", "block.minecraft.bamboo_button": "Botón de bam<PERSON>ú", "block.minecraft.bamboo_door": "Porta de bambú", "block.minecraft.bamboo_fence": "Valado de bam<PERSON>ú", "block.minecraft.bamboo_fence_gate": "Cancela de bambú", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON> de bambú", "block.minecraft.bamboo_mosaic": "Mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_slab": "Lastra de mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_stairs": "Escaleiras de mosaico de bambú", "block.minecraft.bamboo_planks": "Táboas de bambú", "block.minecraft.bamboo_pressure_plate": "Placa de presión de bambú", "block.minecraft.bamboo_sapling": "Gromo de bam<PERSON>ú", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON> de <PERSON>ú", "block.minecraft.bamboo_slab": "<PERSON>ra de bam<PERSON>ú", "block.minecraft.bamboo_stairs": "Escaleiras de bambú", "block.minecraft.bamboo_trapdoor": "Zapón de bambú", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON>e de bambú de parede", "block.minecraft.bamboo_wall_sign": "Letreiro de bambú de parede", "block.minecraft.banner.base.black": "Campo negro", "block.minecraft.banner.base.blue": "Campo azul", "block.minecraft.banner.base.brown": "Campo <PERSON>", "block.minecraft.banner.base.cyan": "Campo ciano", "block.minecraft.banner.base.gray": "Campo gris", "block.minecraft.banner.base.green": "Campo verde", "block.minecraft.banner.base.light_blue": "Campo azul claro", "block.minecraft.banner.base.light_gray": "Campo gris claro", "block.minecraft.banner.base.lime": "Campo lima", "block.minecraft.banner.base.magenta": "<PERSON> maxenta", "block.minecraft.banner.base.orange": "Campo laranxa", "block.minecraft.banner.base.pink": "Campo rosa", "block.minecraft.banner.base.purple": "Campo púrpura", "block.minecraft.banner.base.red": "Campo vermello", "block.minecraft.banner.base.white": "Campo branco", "block.minecraft.banner.base.yellow": "Campo amarelo", "block.minecraft.banner.border.black": "Bordadura negra", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.cyan": "Bordadura ciano", "block.minecraft.banner.border.gray": "Bordadura gris", "block.minecraft.banner.border.green": "Bordadura verde", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON>ra azul claro", "block.minecraft.banner.border.light_gray": "Borda<PERSON>ra gris claro", "block.minecraft.banner.border.lime": "Bordadura lima", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON><PERSON> laranxa", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.border.purple": "Bordadura p<PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON><PERSON> vermella", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON><PERSON> amarela", "block.minecraft.banner.bricks.black": "Mazonado negro", "block.minecraft.banner.bricks.blue": "Mazonado azul", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.bricks.cyan": "Mazonado ciano", "block.minecraft.banner.bricks.gray": "Mazonado gris", "block.minecraft.banner.bricks.green": "Mazonado verde", "block.minecraft.banner.bricks.light_blue": "Mazonado azul claro", "block.minecraft.banner.bricks.light_gray": "Mazonado gris claro", "block.minecraft.banner.bricks.lime": "Mazonado lima", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.bricks.orange": "Mazonado laranxa", "block.minecraft.banner.bricks.pink": "Mazonado rosa", "block.minecraft.banner.bricks.purple": "Mazonado púrpura", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON> vermello", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON> branco", "block.minecraft.banner.bricks.yellow": "Mazonado amarelo", "block.minecraft.banner.circle.black": "Besante negro", "block.minecraft.banner.circle.blue": "Besante azul", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.circle.cyan": "Besante ciano", "block.minecraft.banner.circle.gray": "Besante gris", "block.minecraft.banner.circle.green": "Besante verde", "block.minecraft.banner.circle.light_blue": "Besante azul claro", "block.minecraft.banner.circle.light_gray": "Besante gris claro", "block.minecraft.banner.circle.lime": "Besante lima", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.circle.orange": "Besante laranxa", "block.minecraft.banner.circle.pink": "Besante rosa", "block.minecraft.banner.circle.purple": "Besante púrpura", "block.minecraft.banner.circle.red": "<PERSON><PERSON>te vermello", "block.minecraft.banner.circle.white": "Besante branco", "block.minecraft.banner.circle.yellow": "Besante amarelo", "block.minecraft.banner.creeper.black": "Estampado negro de creeper", "block.minecraft.banner.creeper.blue": "Estampado azul de creeper", "block.minecraft.banner.creeper.brown": "Estampado marr<PERSON> de creeper", "block.minecraft.banner.creeper.cyan": "Estampado ciano de creeper", "block.minecraft.banner.creeper.gray": "Estampado gris de creeper", "block.minecraft.banner.creeper.green": "Estampado verde de creeper", "block.minecraft.banner.creeper.light_blue": "Estampado azul claro de creeper", "block.minecraft.banner.creeper.light_gray": "Estampado gris claro de creeper", "block.minecraft.banner.creeper.lime": "Estampado lima de creeper", "block.minecraft.banner.creeper.magenta": "Estampado maxenta de creeper", "block.minecraft.banner.creeper.orange": "Estampado laranxa de creeper", "block.minecraft.banner.creeper.pink": "Estampado rosa de creeper", "block.minecraft.banner.creeper.purple": "Estampado púrpura de creeper", "block.minecraft.banner.creeper.red": "Estampado vermello de creeper", "block.minecraft.banner.creeper.white": "Estampado branco de creeper", "block.minecraft.banner.creeper.yellow": "Estampado amarelo de creeper", "block.minecraft.banner.cross.black": "Sautor negro", "block.minecraft.banner.cross.blue": "Sautor a<PERSON>l", "block.minecraft.banner.cross.brown": "<PERSON>uto<PERSON>", "block.minecraft.banner.cross.cyan": "Sautor ciano", "block.minecraft.banner.cross.gray": "Sautor gris", "block.minecraft.banner.cross.green": "Sautor verde", "block.minecraft.banner.cross.light_blue": "Sautor azul claro", "block.minecraft.banner.cross.light_gray": "Sautor gris claro", "block.minecraft.banner.cross.lime": "Sautor lima", "block.minecraft.banner.cross.magenta": "Sautor ma<PERSON><PERSON>", "block.minecraft.banner.cross.orange": "Sautor la<PERSON>", "block.minecraft.banner.cross.pink": "Sautor rosa", "block.minecraft.banner.cross.purple": "Sauto<PERSON>", "block.minecraft.banner.cross.red": "<PERSON>utor ve<PERSON>", "block.minecraft.banner.cross.white": "Sautor branco", "block.minecraft.banner.cross.yellow": "Sautor am<PERSON>o", "block.minecraft.banner.curly_border.black": "Bordadura negro dentado", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON><PERSON> azul dentada", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON> dentada", "block.minecraft.banner.curly_border.cyan": "Bordadura ciano dentada", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON> gris dentada", "block.minecraft.banner.curly_border.green": "Borda<PERSON>ra verde<PERSON>ada", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON><PERSON> azul claro dentada", "block.minecraft.banner.curly_border.light_gray": "Bo<PERSON><PERSON>ra gris claro dentada", "block.minecraft.banner.curly_border.lime": "Bordadura lima dentada", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta dentada", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON><PERSON> laranxa dentada", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON><PERSON> rosa dentada", "block.minecraft.banner.curly_border.purple": "Bordadura púrpura dentada", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON><PERSON> vermella dentada", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON><PERSON> branca dentada", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON><PERSON> amarela dentada", "block.minecraft.banner.diagonal_left.black": "Tallada negra a sinistra", "block.minecraft.banner.diagonal_left.blue": "Tallada azul a sinistra", "block.minecraft.banner.diagonal_left.brown": "Tallada marrón a sinistra", "block.minecraft.banner.diagonal_left.cyan": "Tallada ciano a sinistra", "block.minecraft.banner.diagonal_left.gray": "Tallada gris a sinistra", "block.minecraft.banner.diagonal_left.green": "Tallada verde a sinistra", "block.minecraft.banner.diagonal_left.light_blue": "Tallada azul claro a sinistra", "block.minecraft.banner.diagonal_left.light_gray": "Tallada gris claro a sinistra", "block.minecraft.banner.diagonal_left.lime": "Tallada lima a sinistra", "block.minecraft.banner.diagonal_left.magenta": "Tallada maxenta a sinistra", "block.minecraft.banner.diagonal_left.orange": "Tallada laranxa a sinistra", "block.minecraft.banner.diagonal_left.pink": "Tallada rosa a sinistra", "block.minecraft.banner.diagonal_left.purple": "Tallada púrpura a sinistra", "block.minecraft.banner.diagonal_left.red": "Tallada vermella a sinistra", "block.minecraft.banner.diagonal_left.white": "Tallada branca a sinistra", "block.minecraft.banner.diagonal_left.yellow": "Tallada amarela a sinistra", "block.minecraft.banner.diagonal_right.black": "Tallada negra", "block.minecraft.banner.diagonal_right.blue": "Tallada azul", "block.minecraft.banner.diagonal_right.brown": "Tallada marrón", "block.minecraft.banner.diagonal_right.cyan": "Tallada ciano", "block.minecraft.banner.diagonal_right.gray": "Tallada gris", "block.minecraft.banner.diagonal_right.green": "Tallada verde", "block.minecraft.banner.diagonal_right.light_blue": "Tallada azul claro", "block.minecraft.banner.diagonal_right.light_gray": "Tallada gris claro", "block.minecraft.banner.diagonal_right.lime": "Tallada lima", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON> maxenta", "block.minecraft.banner.diagonal_right.orange": "Tallada laranxa", "block.minecraft.banner.diagonal_right.pink": "Tallada rosa", "block.minecraft.banner.diagonal_right.purple": "Tallada púrpura", "block.minecraft.banner.diagonal_right.red": "Tallada vermella", "block.minecraft.banner.diagonal_right.white": "Tallada branca", "block.minecraft.banner.diagonal_right.yellow": "Tallada amarela", "block.minecraft.banner.diagonal_up_left.black": "Tallada negra invertida", "block.minecraft.banner.diagonal_up_left.blue": "Tallada azul invertida", "block.minecraft.banner.diagonal_up_left.brown": "Tallada marrón invertida", "block.minecraft.banner.diagonal_up_left.cyan": "Tallada ciano invertida", "block.minecraft.banner.diagonal_up_left.gray": "Tallada gris invertida", "block.minecraft.banner.diagonal_up_left.green": "Tallada verde invertida", "block.minecraft.banner.diagonal_up_left.light_blue": "Tallada azul claro invertida", "block.minecraft.banner.diagonal_up_left.light_gray": "Tallada gris claro invertida", "block.minecraft.banner.diagonal_up_left.lime": "Tallada lima invertida", "block.minecraft.banner.diagonal_up_left.magenta": "Tallada maxenta invertida", "block.minecraft.banner.diagonal_up_left.orange": "Tallada laranxa invertida", "block.minecraft.banner.diagonal_up_left.pink": "Tallada rosa invertida", "block.minecraft.banner.diagonal_up_left.purple": "Tallada púrpura invertida", "block.minecraft.banner.diagonal_up_left.red": "Tallada vermella invertida", "block.minecraft.banner.diagonal_up_left.white": "Tallada branca invertida", "block.minecraft.banner.diagonal_up_left.yellow": "Tallada amarela invertida", "block.minecraft.banner.diagonal_up_right.black": "Tallada negra invertido a sinistra", "block.minecraft.banner.diagonal_up_right.blue": "Tallada azul invertida a sinistra", "block.minecraft.banner.diagonal_up_right.brown": "Tallada marrón invertida a sinistra", "block.minecraft.banner.diagonal_up_right.cyan": "Tallada ciano invertida a sinistra", "block.minecraft.banner.diagonal_up_right.gray": "Tallada gris invertida a sinistra", "block.minecraft.banner.diagonal_up_right.green": "Tallada verde invertida a sinistra", "block.minecraft.banner.diagonal_up_right.light_blue": "Tallada azul claro invertida a sinistra", "block.minecraft.banner.diagonal_up_right.light_gray": "Tallada gris claro invertida a sinistra", "block.minecraft.banner.diagonal_up_right.lime": "Tallada lima invertida a sinistra", "block.minecraft.banner.diagonal_up_right.magenta": "Tallada maxenta invertida a sinistra", "block.minecraft.banner.diagonal_up_right.orange": "Tallada laranxa invertida a sinistra", "block.minecraft.banner.diagonal_up_right.pink": "Tallada rosa invertida a sinistra", "block.minecraft.banner.diagonal_up_right.purple": "Tallada púrpura invertida a sinistra", "block.minecraft.banner.diagonal_up_right.red": "Tallada vermella invertida a sinistra", "block.minecraft.banner.diagonal_up_right.white": "Tallada branca invertida a sinistra", "block.minecraft.banner.diagonal_up_right.yellow": "Tallada amarela invertida a sinistra", "block.minecraft.banner.flow.black": "Espiral negra", "block.minecraft.banner.flow.blue": "Espiral azul", "block.minecraft.banner.flow.brown": "Espiral marrón", "block.minecraft.banner.flow.cyan": "Espiral ciano", "block.minecraft.banner.flow.gray": "Espiral gris", "block.minecraft.banner.flow.green": "Espiral verde", "block.minecraft.banner.flow.light_blue": "Espiral azul claro", "block.minecraft.banner.flow.light_gray": "Espiral gris claro", "block.minecraft.banner.flow.lime": "Espiral lima", "block.minecraft.banner.flow.magenta": "Espiral maxenta", "block.minecraft.banner.flow.orange": "Espiral laranxa", "block.minecraft.banner.flow.pink": "Espiral rosa", "block.minecraft.banner.flow.purple": "Espiral púrpura", "block.minecraft.banner.flow.red": "Espiral vermella", "block.minecraft.banner.flow.white": "Espiral branca", "block.minecraft.banner.flow.yellow": "Espiral amarela", "block.minecraft.banner.flower.black": "Estampado negro de flor", "block.minecraft.banner.flower.blue": "Estampado azul de flor", "block.minecraft.banner.flower.brown": "Estampado marrón de flor", "block.minecraft.banner.flower.cyan": "Estampado ciano de flor", "block.minecraft.banner.flower.gray": "Estampado gris de flor", "block.minecraft.banner.flower.green": "Estampado verde de flor", "block.minecraft.banner.flower.light_blue": "Estampado azul claro de flor", "block.minecraft.banner.flower.light_gray": "Estampado gris claro de flor", "block.minecraft.banner.flower.lime": "Estampado lima de flor", "block.minecraft.banner.flower.magenta": "Estampado maxenta de flor", "block.minecraft.banner.flower.orange": "Estampado laranxa de flor", "block.minecraft.banner.flower.pink": "Estampado rosa de flor", "block.minecraft.banner.flower.purple": "Estampado púrpura de flor", "block.minecraft.banner.flower.red": "Estampado vermello de flor", "block.minecraft.banner.flower.white": "Estampado branco de flor", "block.minecraft.banner.flower.yellow": "Estampado amarelo de flor", "block.minecraft.banner.globe.black": "Planeta negro", "block.minecraft.banner.globe.blue": "Planeta azul", "block.minecraft.banner.globe.brown": "Planeta marrón", "block.minecraft.banner.globe.cyan": "Planeta ciano", "block.minecraft.banner.globe.gray": "Planeta gris", "block.minecraft.banner.globe.green": "Planeta verde", "block.minecraft.banner.globe.light_blue": "Planeta azul claro", "block.minecraft.banner.globe.light_gray": "Planeta gris claro", "block.minecraft.banner.globe.lime": "Planeta lima", "block.minecraft.banner.globe.magenta": "Planeta maxenta", "block.minecraft.banner.globe.orange": "Planeta laranxa", "block.minecraft.banner.globe.pink": "Planeta rosa", "block.minecraft.banner.globe.purple": "Planeta púrpura", "block.minecraft.banner.globe.red": "Planeta vermello", "block.minecraft.banner.globe.white": "Planeta branco", "block.minecraft.banner.globe.yellow": "Planeta amarelo", "block.minecraft.banner.gradient.black": "Gradiente negro", "block.minecraft.banner.gradient.blue": "Gradiente azul", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Gradiente ciano", "block.minecraft.banner.gradient.gray": "Gradiente gris", "block.minecraft.banner.gradient.green": "Gradiente verde", "block.minecraft.banner.gradient.light_blue": "Gradiente azul claro", "block.minecraft.banner.gradient.light_gray": "Grad<PERSON>e gris claro", "block.minecraft.banner.gradient.lime": "Gradiente lima", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.gradient.orange": "Gradiente la<PERSON>", "block.minecraft.banner.gradient.pink": "Gradiente rosa", "block.minecraft.banner.gradient.purple": "Gradiente púrpura", "block.minecraft.banner.gradient.red": "<PERSON>rad<PERSON><PERSON> vermello", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.banner.gradient.yellow": "Gradiente amarelo", "block.minecraft.banner.gradient_up.black": "Gradiente negro en base", "block.minecraft.banner.gradient_up.blue": "Gradiente azul en base", "block.minecraft.banner.gradient_up.brown": "Gradiente marrón en base", "block.minecraft.banner.gradient_up.cyan": "Gradiente ciano en base", "block.minecraft.banner.gradient_up.gray": "Gradiente gris en base", "block.minecraft.banner.gradient_up.green": "Gradiente verde en base", "block.minecraft.banner.gradient_up.light_blue": "Gradiente azul claro en base", "block.minecraft.banner.gradient_up.light_gray": "Gradiente gris claro en base", "block.minecraft.banner.gradient_up.lime": "Gradiente lima en base", "block.minecraft.banner.gradient_up.magenta": "Gradiente maxenta en base", "block.minecraft.banner.gradient_up.orange": "Gradiente laranxa en base", "block.minecraft.banner.gradient_up.pink": "Gradiente rosa en base", "block.minecraft.banner.gradient_up.purple": "Gradiente púrpura en base", "block.minecraft.banner.gradient_up.red": "Gradiente vermello en base", "block.minecraft.banner.gradient_up.white": "Gradiente branco en base", "block.minecraft.banner.gradient_up.yellow": "Gradiente amarelo en base", "block.minecraft.banner.guster.black": "Remuíño negro", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON> verde", "block.minecraft.banner.guster.light_blue": "Re<PERSON><PERSON><PERSON> azul claro", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.guster.lime": "Re<PERSON><PERSON><PERSON> lima", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "Cortado negro", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> ciano", "block.minecraft.banner.half_horizontal.gray": "Cortado gris", "block.minecraft.banner.half_horizontal.green": "Cortado verde", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON>do azul claro", "block.minecraft.banner.half_horizontal.light_gray": "Cortado gris claro", "block.minecraft.banner.half_horizontal.lime": "Cortado lima", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON> ve<PERSON>ello", "block.minecraft.banner.half_horizontal.white": "<PERSON>rtado branco", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON> am<PERSON>o", "block.minecraft.banner.half_horizontal_bottom.black": "Cortado negro invertido", "block.minecraft.banner.half_horizontal_bottom.blue": "Cortado azul invertido", "block.minecraft.banner.half_horizontal_bottom.brown": "Cortado marrón invertido", "block.minecraft.banner.half_horizontal_bottom.cyan": "Cortado ciano invertido", "block.minecraft.banner.half_horizontal_bottom.gray": "Cortado gris invertido", "block.minecraft.banner.half_horizontal_bottom.green": "Cortado verde invertido", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Cortado azul claro invertido", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Cortado gris claro invertido", "block.minecraft.banner.half_horizontal_bottom.lime": "Cortado lima invertido", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON>rtado maxenta invertido", "block.minecraft.banner.half_horizontal_bottom.orange": "Cortado laranxa invertido", "block.minecraft.banner.half_horizontal_bottom.pink": "Cortado rosa invertido", "block.minecraft.banner.half_horizontal_bottom.purple": "Cortado púrpura invertido", "block.minecraft.banner.half_horizontal_bottom.red": "Cortado vermello invertido", "block.minecraft.banner.half_horizontal_bottom.white": "Cortado branco invertido", "block.minecraft.banner.half_horizontal_bottom.yellow": "Cortado amarelo invertido", "block.minecraft.banner.half_vertical.black": "Partido negro", "block.minecraft.banner.half_vertical.blue": "Partido azul", "block.minecraft.banner.half_vertical.brown": "Partido marrón", "block.minecraft.banner.half_vertical.cyan": "Partido ciano", "block.minecraft.banner.half_vertical.gray": "Partido gris", "block.minecraft.banner.half_vertical.green": "Partido verde", "block.minecraft.banner.half_vertical.light_blue": "Partido azul claro", "block.minecraft.banner.half_vertical.light_gray": "Partido gris claro", "block.minecraft.banner.half_vertical.lime": "Partido lima", "block.minecraft.banner.half_vertical.magenta": "Partido maxenta", "block.minecraft.banner.half_vertical.orange": "Partido laranxa", "block.minecraft.banner.half_vertical.pink": "Partido rosa", "block.minecraft.banner.half_vertical.purple": "Partido púrpura", "block.minecraft.banner.half_vertical.red": "Partido vermello", "block.minecraft.banner.half_vertical.white": "Partido branco", "block.minecraft.banner.half_vertical.yellow": "Partido amarelo", "block.minecraft.banner.half_vertical_right.black": "Partido negro invertido", "block.minecraft.banner.half_vertical_right.blue": "Partido azul invertido", "block.minecraft.banner.half_vertical_right.brown": "Partido marrón invertido", "block.minecraft.banner.half_vertical_right.cyan": "Partido ciano invertido", "block.minecraft.banner.half_vertical_right.gray": "Partido gris invertido", "block.minecraft.banner.half_vertical_right.green": "Partido verde invertido", "block.minecraft.banner.half_vertical_right.light_blue": "Partido azul claro invertido", "block.minecraft.banner.half_vertical_right.light_gray": "Partido gris claro invertido", "block.minecraft.banner.half_vertical_right.lime": "Partido lima invertido", "block.minecraft.banner.half_vertical_right.magenta": "Partido maxenta invertido", "block.minecraft.banner.half_vertical_right.orange": "Partido laranxa invertido", "block.minecraft.banner.half_vertical_right.pink": "Partido rosa invertido", "block.minecraft.banner.half_vertical_right.purple": "Partido púrpura invertido", "block.minecraft.banner.half_vertical_right.red": "Partido vermello invertido", "block.minecraft.banner.half_vertical_right.white": "Partido branco invertido", "block.minecraft.banner.half_vertical_right.yellow": "Partido amarelo invertido", "block.minecraft.banner.mojang.black": "Símbolo negro", "block.minecraft.banner.mojang.blue": "Símbolo azul", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Símbolo ciano", "block.minecraft.banner.mojang.gray": "Símbolo gris", "block.minecraft.banner.mojang.green": "Símbolo verde", "block.minecraft.banner.mojang.light_blue": "Símbolo azul claro", "block.minecraft.banner.mojang.light_gray": "Símbolo gris claro", "block.minecraft.banner.mojang.lime": "Símbolo lima", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.mojang.orange": "Símbolo laranxa", "block.minecraft.banner.mojang.pink": "Símbolo rosa", "block.minecraft.banner.mojang.purple": "Símbolo púrpura", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON><PERSON> vermello", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.banner.mojang.yellow": "Sí<PERSON>lo amarelo", "block.minecraft.banner.piglin.black": "Fociño negro", "block.minecraft.banner.piglin.blue": "Fociño azul", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "Fociño ciano", "block.minecraft.banner.piglin.gray": "Fociño gris", "block.minecraft.banner.piglin.green": "Fociño verde", "block.minecraft.banner.piglin.light_blue": "Fociño azul claro", "block.minecraft.banner.piglin.light_gray": "Fociño gris claro", "block.minecraft.banner.piglin.lime": "Fociño lima", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.piglin.orange": "Fociño la<PERSON>", "block.minecraft.banner.piglin.pink": "Foci<PERSON> rosa", "block.minecraft.banner.piglin.purple": "Fociño pú<PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON><PERSON> verm<PERSON>", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.banner.piglin.yellow": "Fociño amarelo", "block.minecraft.banner.rhombus.black": "Losanxe negro", "block.minecraft.banner.rhombus.blue": "Losanxe azul", "block.minecraft.banner.rhombus.brown": "Losanxe ma<PERSON>ón", "block.minecraft.banner.rhombus.cyan": "Losanxe ciano", "block.minecraft.banner.rhombus.gray": "Losanxe gris", "block.minecraft.banner.rhombus.green": "Losanxe verde", "block.minecraft.banner.rhombus.light_blue": "Losanxe azul claro", "block.minecraft.banner.rhombus.light_gray": "Losanxe gris claro", "block.minecraft.banner.rhombus.lime": "Losanxe lima", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.rhombus.orange": "Losanxe laranxa", "block.minecraft.banner.rhombus.pink": "Losanxe rosa", "block.minecraft.banner.rhombus.purple": "Losanxe púrpura", "block.minecraft.banner.rhombus.red": "Losanxe vermello", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON> branco", "block.minecraft.banner.rhombus.yellow": "Losanxe amarelo", "block.minecraft.banner.skull.black": "Estampado negro de caveira", "block.minecraft.banner.skull.blue": "Estampado azul de caveira", "block.minecraft.banner.skull.brown": "Estampado ma<PERSON>", "block.minecraft.banner.skull.cyan": "Estampado ciano de caveira", "block.minecraft.banner.skull.gray": "Estampado gris de <PERSON>ira", "block.minecraft.banner.skull.green": "Estampado verde de caveira", "block.minecraft.banner.skull.light_blue": "Estampado azul claro de <PERSON>", "block.minecraft.banner.skull.light_gray": "Estampado gris claro de <PERSON>ira", "block.minecraft.banner.skull.lime": "Estampado lima de caveira", "block.minecraft.banner.skull.magenta": "Estampado maxen<PERSON> de <PERSON>", "block.minecraft.banner.skull.orange": "Estampado laranxa de caveira", "block.minecraft.banner.skull.pink": "Estampado rosa de caveira", "block.minecraft.banner.skull.purple": "Estampado púrpura de caveira", "block.minecraft.banner.skull.red": "Estampado vermello de <PERSON>", "block.minecraft.banner.skull.white": "Estampado branco de <PERSON>ira", "block.minecraft.banner.skull.yellow": "Estampado amarelo de caveira", "block.minecraft.banner.small_stripes.black": "Barra negra", "block.minecraft.banner.small_stripes.blue": "Barra a<PERSON>l", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "Barra ciano", "block.minecraft.banner.small_stripes.gray": "Barra gris", "block.minecraft.banner.small_stripes.green": "Barra verde", "block.minecraft.banner.small_stripes.light_blue": "Barra azul claro", "block.minecraft.banner.small_stripes.light_gray": "Barra gris claro", "block.minecraft.banner.small_stripes.lime": "Barra lima", "block.minecraft.banner.small_stripes.magenta": "Ba<PERSON><PERSON> maxenta", "block.minecraft.banner.small_stripes.orange": "Barra laranxa", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.small_stripes.purple": "Barra púrpura", "block.minecraft.banner.small_stripes.red": "Barra vermella", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON> branca", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON> amarela", "block.minecraft.banner.square_bottom_left.black": "Cantón negro en base a destra", "block.minecraft.banner.square_bottom_left.blue": "Cantón azul en base a destra", "block.minecraft.banner.square_bottom_left.brown": "Cantón marrón en base a destra", "block.minecraft.banner.square_bottom_left.cyan": "Cantón ciano en base a destra", "block.minecraft.banner.square_bottom_left.gray": "Cantón gris en base a destra", "block.minecraft.banner.square_bottom_left.green": "Cantón verde en base a destra", "block.minecraft.banner.square_bottom_left.light_blue": "Cantón azul claro en base a destra", "block.minecraft.banner.square_bottom_left.light_gray": "Cantón gris claro en base a destra", "block.minecraft.banner.square_bottom_left.lime": "Cantón lima en base a destra", "block.minecraft.banner.square_bottom_left.magenta": "Cantón maxenta en base a destra", "block.minecraft.banner.square_bottom_left.orange": "Cantón laranxa en base a destra", "block.minecraft.banner.square_bottom_left.pink": "Cantón rosa en base a destra", "block.minecraft.banner.square_bottom_left.purple": "Cantón púrpura en base a destra", "block.minecraft.banner.square_bottom_left.red": "Cantón vermello en base a destra", "block.minecraft.banner.square_bottom_left.white": "Cantón branco en base a destra", "block.minecraft.banner.square_bottom_left.yellow": "Cantón amarelo en base a destra", "block.minecraft.banner.square_bottom_right.black": "Cantón negro en base a sinistra", "block.minecraft.banner.square_bottom_right.blue": "Cantón azul en base a sinistra", "block.minecraft.banner.square_bottom_right.brown": "Cantón marrón en base a sinistra", "block.minecraft.banner.square_bottom_right.cyan": "Cantón ciano en base a sinistra", "block.minecraft.banner.square_bottom_right.gray": "Cantón gris en base a sinistra", "block.minecraft.banner.square_bottom_right.green": "Cantón verde en base a sinistra", "block.minecraft.banner.square_bottom_right.light_blue": "Cantón azul claro en base a sinistra", "block.minecraft.banner.square_bottom_right.light_gray": "Cantón gris claro en base a sinistra", "block.minecraft.banner.square_bottom_right.lime": "Cantón lima en base a sinistra", "block.minecraft.banner.square_bottom_right.magenta": "Cantón maxenta en base a sinistra", "block.minecraft.banner.square_bottom_right.orange": "Cantón laranxa en base a sinistra", "block.minecraft.banner.square_bottom_right.pink": "Cantón rosa en base a sinistra", "block.minecraft.banner.square_bottom_right.purple": "Cantón púrpura en base a sinistra", "block.minecraft.banner.square_bottom_right.red": "Cantón vermello en base a sinistra", "block.minecraft.banner.square_bottom_right.white": "Cantón branco en base a sinistra", "block.minecraft.banner.square_bottom_right.yellow": "Cantón amarelo en base a sinistra", "block.minecraft.banner.square_top_left.black": "Cantón negro en xefe a destra", "block.minecraft.banner.square_top_left.blue": "Cantón azul en xefe a destra", "block.minecraft.banner.square_top_left.brown": "Cantón marrón en xefe a destra", "block.minecraft.banner.square_top_left.cyan": "Cantón ciano en xefe a destra", "block.minecraft.banner.square_top_left.gray": "Cantón gris en xefe a destra", "block.minecraft.banner.square_top_left.green": "Cantón verde en xefe a destra", "block.minecraft.banner.square_top_left.light_blue": "Cantón azul claro en xefe a destra", "block.minecraft.banner.square_top_left.light_gray": "Cantón gris claro en xefe a destra", "block.minecraft.banner.square_top_left.lime": "Cantón lima en xefe a destra", "block.minecraft.banner.square_top_left.magenta": "Cantón maxenta en xefe a destra", "block.minecraft.banner.square_top_left.orange": "Cantón laranxa en xefe a destra", "block.minecraft.banner.square_top_left.pink": "Cantón rosa en xefe a destra", "block.minecraft.banner.square_top_left.purple": "Cantón púrpura en xefe a destra", "block.minecraft.banner.square_top_left.red": "Cantón vermello en xefe a destra", "block.minecraft.banner.square_top_left.white": "Cantón branco en xefe a destra", "block.minecraft.banner.square_top_left.yellow": "Cantón amarelo en xefe a destra", "block.minecraft.banner.square_top_right.black": "Cantón negro en xefe a destra", "block.minecraft.banner.square_top_right.blue": "Cantón azul en xefe a sinistra", "block.minecraft.banner.square_top_right.brown": "Cantón marrón en xefe a sinistra", "block.minecraft.banner.square_top_right.cyan": "Cantón ciano en xefe a sinistra", "block.minecraft.banner.square_top_right.gray": "Cantón gris en xefe a sinistra", "block.minecraft.banner.square_top_right.green": "Cantón verde en xefe a sinistra", "block.minecraft.banner.square_top_right.light_blue": "Cantón azul claro en xefe a sinistra", "block.minecraft.banner.square_top_right.light_gray": "Cantón gris claro en xefe a sinistra", "block.minecraft.banner.square_top_right.lime": "Cantón lima en xefe a sinistra", "block.minecraft.banner.square_top_right.magenta": "Cantón maxenta en xefe a sinistra", "block.minecraft.banner.square_top_right.orange": "Cantón laranxa en xefe a sinistra", "block.minecraft.banner.square_top_right.pink": "Cantón rosa en xefe a sinistra", "block.minecraft.banner.square_top_right.purple": "Cantón púrpura en xefe a sinistra", "block.minecraft.banner.square_top_right.red": "Cantón vermello en xefe a sinistra", "block.minecraft.banner.square_top_right.white": "Cantón branco en xefe a sinistra", "block.minecraft.banner.square_top_right.yellow": "Cantón amarelo en xefe a sinistra", "block.minecraft.banner.straight_cross.black": "Cruz negra", "block.minecraft.banner.straight_cross.blue": "Cruz azul", "block.minecraft.banner.straight_cross.brown": "<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Cruz ciano", "block.minecraft.banner.straight_cross.gray": "<PERSON> gris", "block.minecraft.banner.straight_cross.green": "Cruz verde", "block.minecraft.banner.straight_cross.light_blue": "Cruz azul claro", "block.minecraft.banner.straight_cross.light_gray": "<PERSON> gris claro", "block.minecraft.banner.straight_cross.lime": "Cruz lima", "block.minecraft.banner.straight_cross.magenta": "<PERSON> maxenta", "block.minecraft.banner.straight_cross.orange": "<PERSON> maxenta", "block.minecraft.banner.straight_cross.pink": "<PERSON> rosa", "block.minecraft.banner.straight_cross.purple": "Cruz púrpura", "block.minecraft.banner.straight_cross.red": "<PERSON> vermella", "block.minecraft.banner.straight_cross.white": "<PERSON> branca", "block.minecraft.banner.straight_cross.yellow": "<PERSON> amarel<PERSON>", "block.minecraft.banner.stripe_bottom.black": "Base negra", "block.minecraft.banner.stripe_bottom.blue": "Base azul", "block.minecraft.banner.stripe_bottom.brown": "Base marrón", "block.minecraft.banner.stripe_bottom.cyan": "Base ciano", "block.minecraft.banner.stripe_bottom.gray": "Base gris", "block.minecraft.banner.stripe_bottom.green": "Base verde", "block.minecraft.banner.stripe_bottom.light_blue": "Base azul claro", "block.minecraft.banner.stripe_bottom.light_gray": "Base gris claro", "block.minecraft.banner.stripe_bottom.lime": "Base lima", "block.minecraft.banner.stripe_bottom.magenta": "Base maxenta", "block.minecraft.banner.stripe_bottom.orange": "Base laranxa", "block.minecraft.banner.stripe_bottom.pink": "Base rosa", "block.minecraft.banner.stripe_bottom.purple": "Base púrpura", "block.minecraft.banner.stripe_bottom.red": "Base vermella", "block.minecraft.banner.stripe_bottom.white": "Base branca", "block.minecraft.banner.stripe_bottom.yellow": "Base amarela", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON> negro", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON>u gris", "block.minecraft.banner.stripe_center.green": "Pau verde", "block.minecraft.banner.stripe_center.light_blue": "<PERSON>u azul claro", "block.minecraft.banner.stripe_center.light_gray": "<PERSON>u gris claro", "block.minecraft.banner.stripe_center.lime": "<PERSON>u lima", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON> branco", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON> am<PERSON>o", "block.minecraft.banner.stripe_downleft.black": "Banda negra a sinistra", "block.minecraft.banner.stripe_downleft.blue": "Banda azul a sinistra", "block.minecraft.banner.stripe_downleft.brown": "Banda marrón a sinistra", "block.minecraft.banner.stripe_downleft.cyan": "Banda ciano a sinistra", "block.minecraft.banner.stripe_downleft.gray": "Banda gris a sinistra", "block.minecraft.banner.stripe_downleft.green": "Banda verde a sinistra", "block.minecraft.banner.stripe_downleft.light_blue": "Banda azul claro a sinistra", "block.minecraft.banner.stripe_downleft.light_gray": "Banda gris claro a sinistra", "block.minecraft.banner.stripe_downleft.lime": "Banda lima a sinistra", "block.minecraft.banner.stripe_downleft.magenta": "Banda maxenta a sinistra", "block.minecraft.banner.stripe_downleft.orange": "Banda laranxa a sinistra", "block.minecraft.banner.stripe_downleft.pink": "Banda rosa a sinistra", "block.minecraft.banner.stripe_downleft.purple": "Banda púrpura a sinistra", "block.minecraft.banner.stripe_downleft.red": "Banda vermella a sinistra", "block.minecraft.banner.stripe_downleft.white": "Banda branca a sinistra", "block.minecraft.banner.stripe_downleft.yellow": "Banda amarela a sinistra", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda azul", "block.minecraft.banner.stripe_downright.brown": "Banda marrón", "block.minecraft.banner.stripe_downright.cyan": "Banda ciano", "block.minecraft.banner.stripe_downright.gray": "Banda gris", "block.minecraft.banner.stripe_downright.green": "Banda verde", "block.minecraft.banner.stripe_downright.light_blue": "Banda azul claro", "block.minecraft.banner.stripe_downright.light_gray": "Banda gris claro", "block.minecraft.banner.stripe_downright.lime": "Banda lima", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON> maxenta", "block.minecraft.banner.stripe_downright.orange": "Banda laranxa", "block.minecraft.banner.stripe_downright.pink": "Banda rosa", "block.minecraft.banner.stripe_downright.purple": "Banda púrpura", "block.minecraft.banner.stripe_downright.red": "Banda vermella", "block.minecraft.banner.stripe_downright.white": "Banda branca", "block.minecraft.banner.stripe_downright.yellow": "Banda amarela", "block.minecraft.banner.stripe_left.black": "Pau negro a destra", "block.minecraft.banner.stripe_left.blue": "Pau azul a destra", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON> marr<PERSON> a destra", "block.minecraft.banner.stripe_left.cyan": "<PERSON>u ciano a destra", "block.minecraft.banner.stripe_left.gray": "Pau gris a destra", "block.minecraft.banner.stripe_left.green": "Pau verde a destra", "block.minecraft.banner.stripe_left.light_blue": "<PERSON>u azul claro a destra", "block.minecraft.banner.stripe_left.light_gray": "<PERSON>u gris claro a destra", "block.minecraft.banner.stripe_left.lime": "Pau lima a destra", "block.minecraft.banner.stripe_left.magenta": "<PERSON>u maxenta a destra", "block.minecraft.banner.stripe_left.orange": "Pau laranxa a destra", "block.minecraft.banner.stripe_left.pink": "Pau rosa a destra", "block.minecraft.banner.stripe_left.purple": "Pau púrpura a destra", "block.minecraft.banner.stripe_left.red": "<PERSON>u vermello a destra", "block.minecraft.banner.stripe_left.white": "<PERSON>u branco a destra", "block.minecraft.banner.stripe_left.yellow": "Pau amarelo a destra", "block.minecraft.banner.stripe_middle.black": "Fraixa negra", "block.minecraft.banner.stripe_middle.blue": "Faixa azul", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Faixa ciano", "block.minecraft.banner.stripe_middle.gray": "Faixa gris", "block.minecraft.banner.stripe_middle.green": "Faixa verde", "block.minecraft.banner.stripe_middle.light_blue": "Faixa azul claro", "block.minecraft.banner.stripe_middle.light_gray": "Faixa gris claro", "block.minecraft.banner.stripe_middle.lime": "Faixa lima", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.stripe_middle.orange": "Faixa laranxa", "block.minecraft.banner.stripe_middle.pink": "Faixa rosa", "block.minecraft.banner.stripe_middle.purple": "Faixa púrpura", "block.minecraft.banner.stripe_middle.red": "Faixa vermella", "block.minecraft.banner.stripe_middle.white": "Faixa branca", "block.minecraft.banner.stripe_middle.yellow": "Faixa amarela", "block.minecraft.banner.stripe_right.black": "Pau negro a sinistra", "block.minecraft.banner.stripe_right.blue": "Pau azul a sinistra", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> marrón a sinistra", "block.minecraft.banner.stripe_right.cyan": "<PERSON>u ciano a sinistra", "block.minecraft.banner.stripe_right.gray": "Pau gris a sinistra", "block.minecraft.banner.stripe_right.green": "Pau verde a sinistra", "block.minecraft.banner.stripe_right.light_blue": "Pau azul claro a sinistra", "block.minecraft.banner.stripe_right.light_gray": "Pau gris claro a sinistra", "block.minecraft.banner.stripe_right.lime": "Pau lima a sinistra", "block.minecraft.banner.stripe_right.magenta": "<PERSON>u maxenta a sinistra", "block.minecraft.banner.stripe_right.orange": "Pau laranxa a sinistra", "block.minecraft.banner.stripe_right.pink": "Pau rosa a sinistra", "block.minecraft.banner.stripe_right.purple": "Pau púrpura a sinistra", "block.minecraft.banner.stripe_right.red": "<PERSON>u vermello a sinistra", "block.minecraft.banner.stripe_right.white": "Pau branco a sinistra", "block.minecraft.banner.stripe_right.yellow": "Pau amarelo a sinistra", "block.minecraft.banner.stripe_top.black": "Xefe negro", "block.minecraft.banner.stripe_top.blue": "Xefe azul", "block.minecraft.banner.stripe_top.brown": "<PERSON>efe <PERSON>", "block.minecraft.banner.stripe_top.cyan": "Xefe ciano", "block.minecraft.banner.stripe_top.gray": "Xefe gris", "block.minecraft.banner.stripe_top.green": "Xefe verde", "block.minecraft.banner.stripe_top.light_blue": "Xefe azul claro", "block.minecraft.banner.stripe_top.light_gray": "Xefe gris claro", "block.minecraft.banner.stripe_top.lime": "Xefe lima", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON>e maxenta", "block.minecraft.banner.stripe_top.orange": "Xefe laranxa", "block.minecraft.banner.stripe_top.pink": "Xefe rosa", "block.minecraft.banner.stripe_top.purple": "Xefe púrpura", "block.minecraft.banner.stripe_top.red": "<PERSON>efe vermello", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON>e branco", "block.minecraft.banner.stripe_top.yellow": "Xefe amarelo", "block.minecraft.banner.triangle_bottom.black": "Chevrón negro", "block.minecraft.banner.triangle_bottom.blue": "Chevrón azul", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Chevrón ciano", "block.minecraft.banner.triangle_bottom.gray": "Chevrón gris", "block.minecraft.banner.triangle_bottom.green": "Chevrón verde", "block.minecraft.banner.triangle_bottom.light_blue": "Chevrón azul claro", "block.minecraft.banner.triangle_bottom.light_gray": "Chevrón gris claro", "block.minecraft.banner.triangle_bottom.lime": "Chevrón lima", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> maxenta", "block.minecraft.banner.triangle_bottom.orange": "Chevrón la<PERSON>xa", "block.minecraft.banner.triangle_bottom.pink": "Chevr<PERSON> rosa", "block.minecraft.banner.triangle_bottom.purple": "Chevrón púrpura", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON><PERSON> vermello", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.banner.triangle_bottom.yellow": "Chevrón amarelo", "block.minecraft.banner.triangle_top.black": "Chevrón negro invertido", "block.minecraft.banner.triangle_top.blue": "Chevrón azul invertido", "block.minecraft.banner.triangle_top.brown": "Chevrón marrón invertido", "block.minecraft.banner.triangle_top.cyan": "Chevrón ciano invertido", "block.minecraft.banner.triangle_top.gray": "Chevrón gris invertido", "block.minecraft.banner.triangle_top.green": "Chevrón verde invertido", "block.minecraft.banner.triangle_top.light_blue": "Chevrón azul claro invertido", "block.minecraft.banner.triangle_top.light_gray": "Chevrón gris claro invertido", "block.minecraft.banner.triangle_top.lime": "Chevrón lima invertido", "block.minecraft.banner.triangle_top.magenta": "Chevrón maxenta invertido", "block.minecraft.banner.triangle_top.orange": "Chevrón laranxa invertido", "block.minecraft.banner.triangle_top.pink": "Chevrón rosa invertido", "block.minecraft.banner.triangle_top.purple": "Chevrón púrpura invertido", "block.minecraft.banner.triangle_top.red": "Chevrón vermello invertido", "block.minecraft.banner.triangle_top.white": "Chevrón blanco invertido", "block.minecraft.banner.triangle_top.yellow": "Chevrón amarelo invertido", "block.minecraft.banner.triangles_bottom.black": "Base negra dentada", "block.minecraft.banner.triangles_bottom.blue": "Base azul dentada", "block.minecraft.banner.triangles_bottom.brown": "Base marrón dentada", "block.minecraft.banner.triangles_bottom.cyan": "Base ciano dentada", "block.minecraft.banner.triangles_bottom.gray": "Base gris dentada", "block.minecraft.banner.triangles_bottom.green": "Base verde dentada", "block.minecraft.banner.triangles_bottom.light_blue": "Base azul claro dentada", "block.minecraft.banner.triangles_bottom.light_gray": "Base gris claro dentada", "block.minecraft.banner.triangles_bottom.lime": "Base lima dentada", "block.minecraft.banner.triangles_bottom.magenta": "Base maxenta dentada", "block.minecraft.banner.triangles_bottom.orange": "Base laranxa dentada", "block.minecraft.banner.triangles_bottom.pink": "Base rosa dentada", "block.minecraft.banner.triangles_bottom.purple": "Base púrpura dentada", "block.minecraft.banner.triangles_bottom.red": "Base vermella dentada", "block.minecraft.banner.triangles_bottom.white": "Base branca dentada", "block.minecraft.banner.triangles_bottom.yellow": "Base amarela dentada", "block.minecraft.banner.triangles_top.black": "Xefe negro dentado", "block.minecraft.banner.triangles_top.blue": "Xefe azul dentado", "block.minecraft.banner.triangles_top.brown": "Xefe marr<PERSON> dentado", "block.minecraft.banner.triangles_top.cyan": "Xefe ciano dentado", "block.minecraft.banner.triangles_top.gray": "Xefe gris dentado", "block.minecraft.banner.triangles_top.green": "Xefe verde dentado", "block.minecraft.banner.triangles_top.light_blue": "Xefe azul claro dentado", "block.minecraft.banner.triangles_top.light_gray": "Xefe gris claro dentado", "block.minecraft.banner.triangles_top.lime": "Xefe lima dentado", "block.minecraft.banner.triangles_top.magenta": "Xefe maxenta dentado", "block.minecraft.banner.triangles_top.orange": "Xefe laranxa dentado", "block.minecraft.banner.triangles_top.pink": "Xefe rosa dentado", "block.minecraft.banner.triangles_top.purple": "Xefe púrpura dentado", "block.minecraft.banner.triangles_top.red": "Xefe vermello dentado", "block.minecraft.banner.triangles_top.white": "Xefe branco dentado", "block.minecraft.banner.triangles_top.yellow": "Xefe amarelo dentado", "block.minecraft.barrel": "Bocoi", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Basalto", "block.minecraft.beacon": "Faro", "block.minecraft.beacon.primary": "Poder principal", "block.minecraft.beacon.secondary": "Poder secundario", "block.minecraft.bed.no_sleep": "<PERSON><PERSON> podes durmir pola noite e durante as tormentas", "block.minecraft.bed.not_safe": "Non podes durmir agora, hai monstros preto", "block.minecraft.bed.obstructed": "Esta cama está obstruída", "block.minecraft.bed.occupied": "Esta cama está ocupada", "block.minecraft.bed.too_far_away": "Non podes durmir agora, a cama está moi lonxe", "block.minecraft.bedrock": "Pedra base", "block.minecraft.bee_nest": "Colmea", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "Campá", "block.minecraft.big_dripleaf": "Planta pingueira grande", "block.minecraft.big_dripleaf_stem": "Talo de planta pingueira grande", "block.minecraft.birch_button": "Botón de bidueiro", "block.minecraft.birch_door": "Porta de bidueiro", "block.minecraft.birch_fence": "Valado de bidueiro", "block.minecraft.birch_fence_gate": "Cancela de bidueiro", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON>iro colgante de bidueiro", "block.minecraft.birch_leaves": "Follas de bidueiro", "block.minecraft.birch_log": "Tronco de bidueiro", "block.minecraft.birch_planks": "Táboas de bidueiro", "block.minecraft.birch_pressure_plate": "Placa de presión de bidueiro", "block.minecraft.birch_sapling": "Gromo de <PERSON>ueiro", "block.minecraft.birch_sign": "Let<PERSON>iro de bidueiro", "block.minecraft.birch_slab": "Lastra de bidueiro", "block.minecraft.birch_stairs": "Escaleiras de bidueiro", "block.minecraft.birch_trapdoor": "Zapón de bidueiro", "block.minecraft.birch_wall_hanging_sign": "Letreiro colgante de bidueiro de parede", "block.minecraft.birch_wall_sign": "Letreiro de bidueiro de parede", "block.minecraft.birch_wood": "Madeira de bidueiro", "block.minecraft.black_banner": "Estandarte negro", "block.minecraft.black_bed": "Cama negra", "block.minecraft.black_candle": "Candea negra", "block.minecraft.black_candle_cake": "Torta con candea negra", "block.minecraft.black_carpet": "Alfombra negra", "block.minecraft.black_concrete": "Formigón negro", "block.minecraft.black_concrete_powder": "Cemento negro", "block.minecraft.black_glazed_terracotta": "Azulexo negro", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON> de shulker negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON> de negro", "block.minecraft.black_stained_glass_pane": "Panel de vidro tinguido de negro", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "La negra", "block.minecraft.blackstone": "Pedra negra", "block.minecraft.blackstone_slab": "Lastra de pedra negra", "block.minecraft.blackstone_stairs": "Escaleiras de pedra negra", "block.minecraft.blackstone_wall": "<PERSON><PERSON> de pedra negra", "block.minecraft.blast_furnace": "<PERSON>", "block.minecraft.blue_banner": "Estandarte azul", "block.minecraft.blue_bed": "Cama a<PERSON>l", "block.minecraft.blue_candle": "Candea azul", "block.minecraft.blue_candle_cake": "Torta con candea azul", "block.minecraft.blue_carpet": "Alfombra azul", "block.minecraft.blue_concrete": "Formigón azul", "block.minecraft.blue_concrete_powder": "Cemento azul", "block.minecraft.blue_glazed_terracotta": "Azulexo azul", "block.minecraft.blue_ice": "Xeo a<PERSON>l", "block.minecraft.blue_orchid": "Orquídea a<PERSON>l", "block.minecraft.blue_shulker_box": "Cai<PERSON> de shulker azul", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON> de a<PERSON>l", "block.minecraft.blue_stained_glass_pane": "Panel de vidro tinguido de azul", "block.minecraft.blue_terracotta": "Terracota azul", "block.minecraft.blue_wool": "La azul", "block.minecraft.bone_block": "Bloque de ósos", "block.minecraft.bookshelf": "<PERSON>el de libros", "block.minecraft.brain_coral": "Coral de cerebro", "block.minecraft.brain_coral_block": "Bloque de coral de cerebro", "block.minecraft.brain_coral_fan": "Gorgon<PERSON> de cerebro", "block.minecraft.brain_coral_wall_fan": "Go<PERSON><PERSON> de cerebro de parede", "block.minecraft.brewing_stand": "Fermentador", "block.minecraft.brick_slab": "<PERSON>ra de ladrillos", "block.minecraft.brick_stairs": "Escaleiras de ladrillos", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "Ladrillos", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.brown_candle_cake": "Torta con candea marrón", "block.minecraft.brown_carpet": "Alfombra marrón", "block.minecraft.brown_concrete": "Formigón marrón", "block.minecraft.brown_concrete_powder": "Cemento <PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom": "Cogomelo <PERSON>", "block.minecraft.brown_mushroom_block": "Bloque de cogomelo marrón", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> marr<PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Panel de vidro tinguido de ma<PERSON>ón", "block.minecraft.brown_terracotta": "Terracota marrón", "block.minecraft.brown_wool": "La marrón", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "Coral de burbulla", "block.minecraft.bubble_coral_block": "Bloque de coral burbulla", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> de bur<PERSON> de parede", "block.minecraft.budding_amethyst": "Cristalizador de ametista", "block.minecraft.bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus": "Cacto", "block.minecraft.cactus_flower": "Flor de cacto", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor de sculk calibrado", "block.minecraft.campfire": "Cacharela", "block.minecraft.candle": "Candea", "block.minecraft.candle_cake": "Torta con candea", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Mesa de cartografía", "block.minecraft.carved_pumpkin": "Cabaza esculpida", "block.minecraft.cauldron": "Caldeiro", "block.minecraft.cave_air": "A<PERSON> de cova", "block.minecraft.cave_vines": "Trepadeiras de cova", "block.minecraft.cave_vines_plant": "Planta de trepadeiras de cova", "block.minecraft.chain": "Cade<PERSON>", "block.minecraft.chain_command_block": "Bloque de comandos en cadea", "block.minecraft.cherry_button": "Botón <PERSON>", "block.minecraft.cherry_door": "Porta de cerdeira", "block.minecraft.cherry_fence": "Valado de cerdeira", "block.minecraft.cherry_fence_gate": "Cancela de cerdeira", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON>eira", "block.minecraft.cherry_leaves": "Follas de cerdeira", "block.minecraft.cherry_log": "Tronco de cerdeira", "block.minecraft.cherry_planks": "Táboas de cerdeira", "block.minecraft.cherry_pressure_plate": "Placa de presión de cerdeira", "block.minecraft.cherry_sapling": "Gromo <PERSON>", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON>eira", "block.minecraft.cherry_slab": "Lastra de <PERSON>eira", "block.minecraft.cherry_stairs": "Escaleiras de cerdeira", "block.minecraft.cherry_trapdoor": "Zapón de <PERSON>erdeira", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON> de cerdeira de parede", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON> col<PERSON> de cerdeira de parede", "block.minecraft.cherry_wood": "Madeira de cerdeira", "block.minecraft.chest": "Cofre", "block.minecraft.chipped_anvil": "<PERSON><PERSON> da<PERSON>", "block.minecraft.chiseled_bookshelf": "Andel de libros cicelado", "block.minecraft.chiseled_copper": "Cobre cicelado", "block.minecraft.chiseled_deepslate": "Lousa profunda cicelada", "block.minecraft.chiseled_nether_bricks": "Ladrillos do Nether cicelados", "block.minecraft.chiseled_polished_blackstone": "Pedra negra puída cicelada", "block.minecraft.chiseled_quartz_block": "Bloque de cuarzo cicelado", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON> vermello cicelado", "block.minecraft.chiseled_resin_bricks": "Ladrillos de recina cicelada", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON> c<PERSON>o", "block.minecraft.chiseled_stone_bricks": "Ladrillos de pedra cicelados", "block.minecraft.chiseled_tuff": "<PERSON><PERSON> c<PERSON>o", "block.minecraft.chiseled_tuff_bricks": "Ladrillos de tufo cicelado", "block.minecraft.chorus_flower": "Flor coral", "block.minecraft.chorus_plant": "Planta coral", "block.minecraft.clay": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.coal_block": "Bloque de car<PERSON>ón", "block.minecraft.coal_ore": "Mineral de carbón", "block.minecraft.coarse_dirt": "<PERSON>", "block.minecraft.cobbled_deepslate": "Pedras de lousa profunda", "block.minecraft.cobbled_deepslate_slab": "Lastra de pedras de lousa profunda", "block.minecraft.cobbled_deepslate_stairs": "Escaleiras de pedras de lousa profunda", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> de pedras de lousa profunda", "block.minecraft.cobblestone": "Pedra", "block.minecraft.cobblestone_slab": "<PERSON>ra de pedra", "block.minecraft.cobblestone_stairs": "Escaleiras de pedra", "block.minecraft.cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "Bloque de comandos", "block.minecraft.comparator": "Comparador de redstone", "block.minecraft.composter": "Composteiro", "block.minecraft.conduit": "Canalizador", "block.minecraft.copper_block": "Bloque de cobre", "block.minecraft.copper_bulb": "Lámpada de cobre", "block.minecraft.copper_door": "Porta de cobre", "block.minecraft.copper_grate": "Reixa de <PERSON>", "block.minecraft.copper_ore": "Mineral de cobre", "block.minecraft.copper_trapdoor": "Zapón de cobre", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Ladrillos de lousa profunda rachados", "block.minecraft.cracked_deepslate_tiles": "Baldosas de lousa profunda rachadas", "block.minecraft.cracked_nether_bricks": "Ladrillos do Nether rachados", "block.minecraft.cracked_polished_blackstone_bricks": "Ladrillos de pedra negra puída rachados", "block.minecraft.cracked_stone_bricks": "Ladrillos de pedra rachados", "block.minecraft.crafter": "Fabricador", "block.minecraft.crafting_table": "Mesa de fabricación", "block.minecraft.creaking_heart": "Corazón crepitante", "block.minecraft.creeper_head": "C<PERSON>za de creeper", "block.minecraft.creeper_wall_head": "Cabeza de creeper de parede", "block.minecraft.crimson_button": "Bo<PERSON><PERSON>", "block.minecraft.crimson_door": "Porta carmesí", "block.minecraft.crimson_fence": "Valado car<PERSON>í", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON>a <PERSON>", "block.minecraft.crimson_fungus": "Fungo carmesí", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON> co<PERSON>", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON>", "block.minecraft.crimson_planks": "Táboas carmesí", "block.minecraft.crimson_pressure_plate": "Placa de presión carmesí", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON>", "block.minecraft.crimson_stairs": "Escaleiras <PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "Zapón carmesí", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante carmesí de parede", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON> de parede", "block.minecraft.crying_obsidian": "Obsidiana chorona", "block.minecraft.cut_copper": "Cobre cortado", "block.minecraft.cut_copper_slab": "Lastra de cobre cortado", "block.minecraft.cut_copper_stairs": "Escaleiras de cobre cortado", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON> vermello cortado", "block.minecraft.cut_red_sandstone_slab": "Lastra de arenito vermello cortado", "block.minecraft.cut_sandstone": "<PERSON><PERSON> corta<PERSON>", "block.minecraft.cut_sandstone_slab": "Lastra de arenito cortado", "block.minecraft.cyan_banner": "Estandarte ciano", "block.minecraft.cyan_bed": "Cama ciano", "block.minecraft.cyan_candle": "Candea ciano", "block.minecraft.cyan_candle_cake": "Torta con candea ciano", "block.minecraft.cyan_carpet": "Alfombra ciano", "block.minecraft.cyan_concrete": "Formigón ciano", "block.minecraft.cyan_concrete_powder": "Cemento ciano", "block.minecraft.cyan_glazed_terracotta": "Azulexo ciano", "block.minecraft.cyan_shulker_box": "Caixa de shulker ciano", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> de c<PERSON>", "block.minecraft.cyan_stained_glass_pane": "Panel de vidro tinguido de ciano", "block.minecraft.cyan_terracotta": "Terracota ciano", "block.minecraft.cyan_wool": "La ciano", "block.minecraft.damaged_anvil": "Engra moi danada", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "Botón de carballo escuro", "block.minecraft.dark_oak_door": "Porta de carballo escuro", "block.minecraft.dark_oak_fence": "Valado de carballo escuro", "block.minecraft.dark_oak_fence_gate": "Cancela de carballo escuro", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON>iro colgante de carballo escuro", "block.minecraft.dark_oak_leaves": "Follas de carballo escuro", "block.minecraft.dark_oak_log": "Tronco de carballo escuro", "block.minecraft.dark_oak_planks": "Táboas de carballo escuro", "block.minecraft.dark_oak_pressure_plate": "Placa de presión de carballo escuro", "block.minecraft.dark_oak_sapling": "Gromo de carballo escuro", "block.minecraft.dark_oak_sign": "Letreiro de carballo escuro", "block.minecraft.dark_oak_slab": "Lastra de carballo escuro", "block.minecraft.dark_oak_stairs": "Escaleiras de carballo escuro", "block.minecraft.dark_oak_trapdoor": "Zapón de carballo escuro", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON>iro colgante de carballo escuro de parede", "block.minecraft.dark_oak_wall_sign": "Letreiro de carballo escuro de parede", "block.minecraft.dark_oak_wood": "Madeira de carballo escuro", "block.minecraft.dark_prismarine": "Prismariña escura", "block.minecraft.dark_prismarine_slab": "Lastra de prismariña escura", "block.minecraft.dark_prismarine_stairs": "Escaleiras de prismariña escura", "block.minecraft.daylight_detector": "Sensor de luz solar", "block.minecraft.dead_brain_coral": "Coral de cerebro morto", "block.minecraft.dead_brain_coral_block": "Bloque de coral de cerebro morto", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> de cerebro morta", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cerebro morta de parede", "block.minecraft.dead_bubble_coral": "Coral de burbulla morto", "block.minecraft.dead_bubble_coral_block": "Bloque de coral de burbulla morto", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON> de burbulla morta", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> de burbulla morta de parede", "block.minecraft.dead_bush": "Arbusto morto", "block.minecraft.dead_fire_coral": "Coral de lume morto", "block.minecraft.dead_fire_coral_block": "Bloque de coral de lume morto", "block.minecraft.dead_fire_coral_fan": "Go<PERSON>ia de lume morta", "block.minecraft.dead_fire_coral_wall_fan": "Go<PERSON>ia de lume morta de parede", "block.minecraft.dead_horn_coral": "Coral de cornos morto", "block.minecraft.dead_horn_coral_block": "Bloque de coral de corno morto", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON> de corno morta", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON> de corno morta de parede", "block.minecraft.dead_tube_coral": "Coral de tubo morto", "block.minecraft.dead_tube_coral_block": "Bloque de coral de tubo morto", "block.minecraft.dead_tube_coral_fan": "Gorgonia de tubo morta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgonia tubular morta de parede", "block.minecraft.decorated_pot": "Tarro decorado", "block.minecraft.deepslate": "Lousa profunda", "block.minecraft.deepslate_brick_slab": "Lastra de ladrillos de lousa profunda", "block.minecraft.deepslate_brick_stairs": "Escaleiras de ladrillos de lousa profunda", "block.minecraft.deepslate_brick_wall": "<PERSON>ro de ladrillos de lousa profunda", "block.minecraft.deepslate_bricks": "Ladrillos de lousa profunda", "block.minecraft.deepslate_coal_ore": "Mineral de carbón de lousa profunda", "block.minecraft.deepslate_copper_ore": "Mineral de cobre de lousa profunda", "block.minecraft.deepslate_diamond_ore": "Mineral de diamante de lousa profunda", "block.minecraft.deepslate_emerald_ore": "Mineral de esmeralda de lousa profunda", "block.minecraft.deepslate_gold_ore": "Mineral de ouro de lousa profunda", "block.minecraft.deepslate_iron_ore": "Mineral de ferro de lousa profunda", "block.minecraft.deepslate_lapis_ore": "Mineral de lapislázuli de lousa profunda", "block.minecraft.deepslate_redstone_ore": "Mineral de redstone de lousa profunda", "block.minecraft.deepslate_tile_slab": "Lastra de baldosas de lousa profunda", "block.minecraft.deepslate_tile_stairs": "Escaleiras de baldosas de lousa profunda", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> de baldosa<PERSON> de lousa profunda", "block.minecraft.deepslate_tiles": "Baldosas de lousa profunda", "block.minecraft.detector_rail": "Ra<PERSON>l detector", "block.minecraft.diamond_block": "Bloque de diamante", "block.minecraft.diamond_ore": "Mineral de diamante", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Lastra de diorita", "block.minecraft.diorite_stairs": "Escaleiras de diorita", "block.minecraft.diorite_wall": "<PERSON>ro <PERSON> di<PERSON>", "block.minecraft.dirt": "Terra", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "Dispensador", "block.minecraft.dragon_egg": "Ovo de <PERSON>", "block.minecraft.dragon_head": "Cabeza de dragón", "block.minecraft.dragon_wall_head": "Cabeza de dragón de parede", "block.minecraft.dried_ghast": "G<PERSON><PERSON> seco", "block.minecraft.dried_kelp_block": "Bloque de alga seca", "block.minecraft.dripstone_block": "Bloque de espeleotema", "block.minecraft.dropper": "Lanzador", "block.minecraft.emerald_block": "Bloque de esmeralda", "block.minecraft.emerald_ore": "Mineral de esmeralda", "block.minecraft.enchanting_table": "Mesa de encantamentos", "block.minecraft.end_gateway": "Entrada do End", "block.minecraft.end_portal": "Portal do End", "block.minecraft.end_portal_frame": "<PERSON> do <PERSON> do End", "block.minecraft.end_rod": "Vara do End", "block.minecraft.end_stone": "Pedra do End", "block.minecraft.end_stone_brick_slab": "Lastra de ladrillos de pedra do <PERSON>", "block.minecraft.end_stone_brick_stairs": "Escaleiras de ladrillos de pedra do End", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON> de ladrillos de pedra do <PERSON>", "block.minecraft.end_stone_bricks": "Ladrillos de pedra do End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.exposed_chiseled_copper": "Cobre cicelado exposto", "block.minecraft.exposed_copper": "Cobre exposto", "block.minecraft.exposed_copper_bulb": "Lámpada de cobre exposto", "block.minecraft.exposed_copper_door": "Porta de cobre exposto", "block.minecraft.exposed_copper_grate": "Reixa de cobre exposto", "block.minecraft.exposed_copper_trapdoor": "Zapón de cobre exposto", "block.minecraft.exposed_cut_copper": "Cobre cortado exposto", "block.minecraft.exposed_cut_copper_slab": "Lastra de cobre cortado exposto", "block.minecraft.exposed_cut_copper_stairs": "Escaleiras de cobre cortado exposto", "block.minecraft.farmland": "Terra de cultivo", "block.minecraft.fern": "Fen<PERSON>", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "Coral de lume", "block.minecraft.fire_coral_block": "Bloque de coral de lume", "block.minecraft.fire_coral_fan": "Gorgonia de lume", "block.minecraft.fire_coral_wall_fan": "Gorgonia de lume de parede", "block.minecraft.firefly_bush": "<PERSON><PERSON><PERSON><PERSON> vaga<PERSON>", "block.minecraft.fletching_table": "Mesa de frechas", "block.minecraft.flower_pot": "<PERSON><PERSON>", "block.minecraft.flowering_azalea": "Azalea en flor", "block.minecraft.flowering_azalea_leaves": "Follas de azaleza florecida", "block.minecraft.frogspawn": "Ovos de ra", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.furnace": "Forno", "block.minecraft.gilded_blackstone": "Pedra negra dourada", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "Panel de vidro", "block.minecraft.glow_lichen": "Lique brillante", "block.minecraft.glowstone": "Pedra luminosa", "block.minecraft.gold_block": "Bloque de ouro", "block.minecraft.gold_ore": "Mineral de ouro", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Lastra de granito", "block.minecraft.granite_stairs": "Escaleiras de granito", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "<PERSON><PERSON>", "block.minecraft.grass_block": "Bloque de herba", "block.minecraft.gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "Estandarte gris", "block.minecraft.gray_bed": "<PERSON>a gris", "block.minecraft.gray_candle": "Candea gris", "block.minecraft.gray_candle_cake": "Torta con candea gris", "block.minecraft.gray_carpet": "Alfombra gris", "block.minecraft.gray_concrete": "Formigón gris", "block.minecraft.gray_concrete_powder": "Cemento gris", "block.minecraft.gray_glazed_terracotta": "Azulexo gris", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker gris", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> de <PERSON>ris", "block.minecraft.gray_stained_glass_pane": "Panel de vidro tinguido de gris", "block.minecraft.gray_terracotta": "Terracota gris", "block.minecraft.gray_wool": "La gris", "block.minecraft.green_banner": "Estandarte verde", "block.minecraft.green_bed": "Cama verde", "block.minecraft.green_candle": "Candea verde", "block.minecraft.green_candle_cake": "Torta con candea verde", "block.minecraft.green_carpet": "Alfombra verde", "block.minecraft.green_concrete": "Formigón verde", "block.minecraft.green_concrete_powder": "Cemento verde", "block.minecraft.green_glazed_terracotta": "Azulexo verde", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON> de shulker verde", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> de verde", "block.minecraft.green_stained_glass_pane": "Panel de vidro tinguido de verde", "block.minecraft.green_terracotta": "Terracota verde", "block.minecraft.green_wool": "La verde", "block.minecraft.grindstone": "Pedra de amolar", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON>alla", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> pesado", "block.minecraft.heavy_weighted_pressure_plate": "Placa de presión para peso elevado", "block.minecraft.honey_block": "Bloque de mel", "block.minecraft.honeycomb_block": "Bloque de favo", "block.minecraft.hopper": "Funil", "block.minecraft.horn_coral": "Coral de corno", "block.minecraft.horn_coral_block": "Bloque de coral de corno", "block.minecraft.horn_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON> de corno de parede", "block.minecraft.ice": "Xeo", "block.minecraft.infested_chiseled_stone_bricks": "Ladrillos de pedra cicelada infestados", "block.minecraft.infested_cobblestone": "Pedra infestada", "block.minecraft.infested_cracked_stone_bricks": "Ladrillos de pedra rachados infestados", "block.minecraft.infested_deepslate": "Lousa profunda infestada", "block.minecraft.infested_mossy_stone_bricks": "Ladrillos de pedra musgosa infestados", "block.minecraft.infested_stone": "Rocha infestada", "block.minecraft.infested_stone_bricks": "Ladrillos de pedra infestados", "block.minecraft.iron_bars": "Barrotes de ferro", "block.minecraft.iron_block": "Bloque de ferro", "block.minecraft.iron_door": "Porta de ferro", "block.minecraft.iron_ore": "Mineral de ferro", "block.minecraft.iron_trapdoor": "Zapón de ferro", "block.minecraft.jack_o_lantern": "Cabaza de Samaín", "block.minecraft.jigsaw": "Bloque de crebacabezas", "block.minecraft.jukebox": "Tocadiscos", "block.minecraft.jungle_button": "Botón de selva", "block.minecraft.jungle_door": "Porta de selva", "block.minecraft.jungle_fence": "Valado de selva", "block.minecraft.jungle_fence_gate": "Cancela de selva", "block.minecraft.jungle_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON> de selva", "block.minecraft.jungle_leaves": "Follas de selva", "block.minecraft.jungle_log": "Tronco de selva", "block.minecraft.jungle_planks": "Táboas de selva", "block.minecraft.jungle_pressure_plate": "Placa de presión de selva", "block.minecraft.jungle_sapling": "Gromo de selva", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON> de <PERSON>lva", "block.minecraft.jungle_slab": "Lastra de selva", "block.minecraft.jungle_stairs": "Escaleiras de selva", "block.minecraft.jungle_trapdoor": "Zapón de selva", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante de selva de parede", "block.minecraft.jungle_wall_sign": "Letreiro de selva de parede", "block.minecraft.jungle_wood": "Madeira de selva", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "<PERSON>lo de alga", "block.minecraft.ladder": "Escada", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "Bloque de lapislázuli", "block.minecraft.lapis_ore": "Mineral de lapislázuli", "block.minecraft.large_amethyst_bud": "Gromo de ametista grande", "block.minecraft.large_fern": "Fento grande", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Caldeiro de lava", "block.minecraft.leaf_litter": "Follas secas", "block.minecraft.lectern": "Atril", "block.minecraft.lever": "Panca", "block.minecraft.light": "Luz", "block.minecraft.light_blue_banner": "Estandarte azul claro", "block.minecraft.light_blue_bed": "Cama azul claro", "block.minecraft.light_blue_candle": "Candea azul claro", "block.minecraft.light_blue_candle_cake": "Torta con candea azul claro", "block.minecraft.light_blue_carpet": "Alfombra azul claro", "block.minecraft.light_blue_concrete": "Formigón azul claro", "block.minecraft.light_blue_concrete_powder": "Cemento azul claro", "block.minecraft.light_blue_glazed_terracotta": "Azulexo azul claro", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON> de shulker azul claro", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> de azul claro", "block.minecraft.light_blue_stained_glass_pane": "Panel de vidro tinguido de azul claro", "block.minecraft.light_blue_terracotta": "Terracota azul claro", "block.minecraft.light_blue_wool": "La azul claro", "block.minecraft.light_gray_banner": "Estandarte gris claro", "block.minecraft.light_gray_bed": "<PERSON>a gris claro", "block.minecraft.light_gray_candle": "Candea gris claro", "block.minecraft.light_gray_candle_cake": "<PERSON>ta con candea gris claro", "block.minecraft.light_gray_carpet": "Alfombra gris claro", "block.minecraft.light_gray_concrete": "Formigón gris claro", "block.minecraft.light_gray_concrete_powder": "Cemento gris claro", "block.minecraft.light_gray_glazed_terracotta": "Azulexo gris claro", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON> de shulker gris claro", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> de gris claro", "block.minecraft.light_gray_stained_glass_pane": "Panel de vidro tinguido de gris claro", "block.minecraft.light_gray_terracotta": "Terracota gris claro", "block.minecraft.light_gray_wool": "La gris claro", "block.minecraft.light_weighted_pressure_plate": "Placa de presión para peso lixeiro", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON>", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "Lirio do val", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Estandarte lima", "block.minecraft.lime_bed": "Cama lima", "block.minecraft.lime_candle": "Candea lima", "block.minecraft.lime_candle_cake": "Torta con candea lima", "block.minecraft.lime_carpet": "Alfombra lima", "block.minecraft.lime_concrete": "Formigón lima", "block.minecraft.lime_concrete_powder": "Cemento lima", "block.minecraft.lime_glazed_terracotta": "Azulexo lima", "block.minecraft.lime_shulker_box": "Caixa de shulker lima", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON> de lima", "block.minecraft.lime_stained_glass_pane": "Panel de vidro tinguido de lima", "block.minecraft.lime_terracotta": "Terracota lima", "block.minecraft.lime_wool": "La lima", "block.minecraft.lodestone": "Ma<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Tear", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.magenta_bed": "<PERSON><PERSON> maxenta", "block.minecraft.magenta_candle": "<PERSON><PERSON> maxenta", "block.minecraft.magenta_candle_cake": "Torta con candea maxenta", "block.minecraft.magenta_carpet": "Alfo<PERSON><PERSON> maxenta", "block.minecraft.magenta_concrete": "<PERSON>ig<PERSON> maxenta", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON> maxenta", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON> maxenta", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass_pane": "Panel de vidro tinguido de maxenta", "block.minecraft.magenta_terracotta": "Terracota maxenta", "block.minecraft.magenta_wool": "La maxenta", "block.minecraft.magma_block": "Bloque de magma", "block.minecraft.mangrove_button": "Botón de mangleiro", "block.minecraft.mangrove_door": "Porta de mangleiro", "block.minecraft.mangrove_fence": "Valado de mangleiro", "block.minecraft.mangrove_fence_gate": "Cancela de mangleiro", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON>e de mangleiro", "block.minecraft.mangrove_leaves": "Follas de mangleiro", "block.minecraft.mangrove_log": "Tronco de mangleiro", "block.minecraft.mangrove_planks": "Táboas de mangleiro", "block.minecraft.mangrove_pressure_plate": "Placa de presión de mangleiro", "block.minecraft.mangrove_propagule": "Propágulo de mangleiro", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON> de man<PERSON>iro", "block.minecraft.mangrove_sign": "<PERSON><PERSON><PERSON> de <PERSON>iro", "block.minecraft.mangrove_slab": "<PERSON><PERSON> de <PERSON><PERSON>", "block.minecraft.mangrove_stairs": "Escaleiras de mangleiro", "block.minecraft.mangrove_trapdoor": "Zapón de mangleiro", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante de mangleiro de parede", "block.minecraft.mangrove_wall_sign": "Letreiro de mangleiro de parede", "block.minecraft.mangrove_wood": "Madeira de mangleiro", "block.minecraft.medium_amethyst_bud": "Gromo de ametista mediano", "block.minecraft.melon": "Sandía", "block.minecraft.melon_stem": "<PERSON><PERSON>", "block.minecraft.moss_block": "Bloque de carriza", "block.minecraft.moss_carpet": "Alfombra de carriza", "block.minecraft.mossy_cobblestone": "Pedra musgosa", "block.minecraft.mossy_cobblestone_slab": "Lastra de pedra musgosa", "block.minecraft.mossy_cobblestone_stairs": "Escaleiras de pedra musgosa", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> de pedra musgosa", "block.minecraft.mossy_stone_brick_slab": "Lastra de ladrillos de pedra musgosa", "block.minecraft.mossy_stone_brick_stairs": "Escaleiras de ladrillos de pedra musgosa", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de ladrillos de pedra musgosa", "block.minecraft.mossy_stone_bricks": "Ladrillos de pedra musgosa", "block.minecraft.moving_piston": "Pistón en movemento", "block.minecraft.mud": "<PERSON>", "block.minecraft.mud_brick_slab": "Lastra de ladrillos de lama", "block.minecraft.mud_brick_stairs": "Escaleiras de ladrillos de lama", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> de ladrillos de lama", "block.minecraft.mud_bricks": "Ladrillos de lama", "block.minecraft.muddy_mangrove_roots": "Ra<PERSON><PERSON> de mangleiro enlamadas", "block.minecraft.mushroom_stem": "<PERSON>é <PERSON>go<PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Valado de ladrillos do Nether", "block.minecraft.nether_brick_slab": "Lastra de ladrillos do Nether", "block.minecraft.nether_brick_stairs": "Escaleiras de ladrillos do Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> de ladrillos do Nether", "block.minecraft.nether_bricks": "Ladrillos do Nether", "block.minecraft.nether_gold_ore": "Mineral de ouro do Nether", "block.minecraft.nether_portal": "Portal do Nether", "block.minecraft.nether_quartz_ore": "Mineral de cuarzo do Nether", "block.minecraft.nether_sprouts": "Gromos do Nether", "block.minecraft.nether_wart": "Espulla do <PERSON>her", "block.minecraft.nether_wart_block": "Bloque de espullas do Nether", "block.minecraft.netherite_block": "Bloque de netherita", "block.minecraft.netherrack": "Pedra do Nether", "block.minecraft.note_block": "Bloque musical", "block.minecraft.oak_button": "Botón de carballo", "block.minecraft.oak_door": "Porta de carballo", "block.minecraft.oak_fence": "Valado de carballo", "block.minecraft.oak_fence_gate": "Cancela de carballo", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON> de carballo", "block.minecraft.oak_leaves": "Follas de carballo", "block.minecraft.oak_log": "Tronco de carballo", "block.minecraft.oak_planks": "Táboas de carballo", "block.minecraft.oak_pressure_plate": "Placa de presión de carballo", "block.minecraft.oak_sapling": "Gromo de carballo", "block.minecraft.oak_sign": "Letreiro de carballo", "block.minecraft.oak_slab": "Lastra de carballo", "block.minecraft.oak_stairs": "Escaleiras de carballo", "block.minecraft.oak_trapdoor": "Zapón de carballo", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON>iro colgante de carballo de parede", "block.minecraft.oak_wall_sign": "Letreiro de carballo de parede", "block.minecraft.oak_wood": "Madeira de carballo", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON> ocre", "block.minecraft.ominous_banner": "Estandarte sinistro", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON> a<PERSON>a", "block.minecraft.orange_banner": "Estandarte laranxa", "block.minecraft.orange_bed": "<PERSON><PERSON> laranxa", "block.minecraft.orange_candle": "Candea laranxa", "block.minecraft.orange_candle_cake": "Torta con candea laranxa", "block.minecraft.orange_carpet": "Alfombra laranxa", "block.minecraft.orange_concrete": "Formigón laranxa", "block.minecraft.orange_concrete_powder": "Cemento laranxa", "block.minecraft.orange_glazed_terracotta": "Azulexo la<PERSON>", "block.minecraft.orange_shulker_box": "Caixa de shulker laranxa", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panel de vidro tinguido de laranxa", "block.minecraft.orange_terracotta": "Terracota laranxa", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.orange_wool": "La laranxa", "block.minecraft.oxeye_daisy": "Margarida", "block.minecraft.oxidized_chiseled_copper": "Cobre cicelado oxidado", "block.minecraft.oxidized_copper": "Cobre oxidado", "block.minecraft.oxidized_copper_bulb": "Lámpada de cobre oxidado", "block.minecraft.oxidized_copper_door": "Porta de cobre oxidado", "block.minecraft.oxidized_copper_grate": "Reixa de cobre oxidado", "block.minecraft.oxidized_copper_trapdoor": "Zapón de cobre oxidado", "block.minecraft.oxidized_cut_copper": "Cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_slab": "Lastra de cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_stairs": "Escaleiras de cobre cortado oxidado", "block.minecraft.packed_ice": "Xeo compactado", "block.minecraft.packed_mud": "Lama compactada", "block.minecraft.pale_hanging_moss": "Enredadeiras de musgo esbrancuxado", "block.minecraft.pale_moss_block": "Bloque de musgo esbrancuxado", "block.minecraft.pale_moss_carpet": "Tapete de musgo esbrancuxado", "block.minecraft.pale_oak_button": "Botón de carballo esbrancuxado", "block.minecraft.pale_oak_door": "Porta de carballo esbrancuxado", "block.minecraft.pale_oak_fence": "Valado de carballo esbrancuxado", "block.minecraft.pale_oak_fence_gate": "Cancela de carballo esbrancuxado", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON>iro colgante de carballo esbrancuxado", "block.minecraft.pale_oak_leaves": "Follas de carballo esbrancuxado", "block.minecraft.pale_oak_log": "Tronco de carballo esbrancuxado", "block.minecraft.pale_oak_planks": "Táboas de carballo esbrancuxado", "block.minecraft.pale_oak_pressure_plate": "Placa de presión de carballo esbrancuxado", "block.minecraft.pale_oak_sapling": "Gromo de carballo esbrancuxado", "block.minecraft.pale_oak_sign": "Letreiro de carballo esbrancuxado", "block.minecraft.pale_oak_slab": "Lastra de carballo esbrancuxado", "block.minecraft.pale_oak_stairs": "Escaleiras de carballo esbrancuxado", "block.minecraft.pale_oak_trapdoor": "Zapón de carballo esbrancuxado", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON>iro colgante de carballo esbrancuxado de parede", "block.minecraft.pale_oak_wall_sign": "Letreiro de carballo esbrancuxado de parede", "block.minecraft.pale_oak_wood": "Madeira de carballo esbrancuxado", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.peony": "Peonia", "block.minecraft.petrified_oak_slab": "Lastra de carballo petrificado", "block.minecraft.piglin_head": "Cabeza de piglin", "block.minecraft.piglin_wall_head": "Cabeza de piglin de parede", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_bed": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle_cake": "Torta con candea rosa", "block.minecraft.pink_carpet": "Alfombra rosa", "block.minecraft.pink_concrete": "Formigón rosa", "block.minecraft.pink_concrete_powder": "Cemento rosa", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON><PERSON> rosas", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker rosa", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> de rosa", "block.minecraft.pink_stained_glass_pane": "Panel de vidro tinguido de rosa", "block.minecraft.pink_terracotta": "Terracota rosa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_wool": "La rosa", "block.minecraft.piston": "Pistón", "block.minecraft.piston_head": "Cabeza de pistón", "block.minecraft.pitcher_crop": "Cultivo de planta xerra", "block.minecraft.pitcher_plant": "Planta xerra", "block.minecraft.player_head": "Cabeza de xogador", "block.minecraft.player_head.named": "Cabeza de %s", "block.minecraft.player_wall_head": "Cabeza de xogador de parede", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "Espeleotema afiado", "block.minecraft.polished_andesite": "<PERSON><PERSON>", "block.minecraft.polished_andesite_slab": "Lastra de andesita puída", "block.minecraft.polished_andesite_stairs": "Escaleiras de andesita puída", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone": "Pedra negra puída", "block.minecraft.polished_blackstone_brick_slab": "Lastra de ladrillos de pedra negra puída", "block.minecraft.polished_blackstone_brick_stairs": "Escaleiras de ladrillos de pedra negra puída", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de ladrillos de pedra negra puída", "block.minecraft.polished_blackstone_bricks": "Ladrillos de pedra negra puída", "block.minecraft.polished_blackstone_button": "Botón de pedra negra puída", "block.minecraft.polished_blackstone_pressure_plate": "Placa de presión de pedra negra puída", "block.minecraft.polished_blackstone_slab": "Lastra de pedra negra puída", "block.minecraft.polished_blackstone_stairs": "Escaleiras de pedra negra puída", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> de pedra negra puída", "block.minecraft.polished_deepslate": "Lousa profunda puída", "block.minecraft.polished_deepslate_slab": "Lastra de lousa profunda puída", "block.minecraft.polished_deepslate_stairs": "Escaleiras de lousa profunda puída", "block.minecraft.polished_deepslate_wall": "Muro de lousa profunda puída", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON>da", "block.minecraft.polished_diorite_slab": "Lastra de diorita puída", "block.minecraft.polished_diorite_stairs": "Escaleiras de diorita puída", "block.minecraft.polished_granite": "<PERSON><PERSON>", "block.minecraft.polished_granite_slab": "Lastra de granito <PERSON>", "block.minecraft.polished_granite_stairs": "Escaleiras de granito pu<PERSON>do", "block.minecraft.polished_tuff": "<PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "Lastra de tufo puído", "block.minecraft.polished_tuff_stairs": "Escaleiras de tufo puído", "block.minecraft.polished_tuff_wall": "<PERSON>ro de tufo pu<PERSON>do", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Patacas", "block.minecraft.potted_acacia_sapling": "Testo con gromo de acacia", "block.minecraft.potted_allium": "Testo con allo bravo", "block.minecraft.potted_azalea_bush": "Testo con azalea", "block.minecraft.potted_azure_bluet": "Testo con rubi<PERSON>", "block.minecraft.potted_bamboo": "Testo con bambú", "block.minecraft.potted_birch_sapling": "Testo con gromo de bidueiro", "block.minecraft.potted_blue_orchid": "Testo con orquídea azul", "block.minecraft.potted_brown_mushroom": "Testo con cogomelo marr<PERSON>", "block.minecraft.potted_cactus": "Testo con cacto", "block.minecraft.potted_cherry_sapling": "Testo con gromo de cerdeira", "block.minecraft.potted_closed_eyeblossom": "Testo con fitamel pechada", "block.minecraft.potted_cornflower": "Testo con cardafuso", "block.minecraft.potted_crimson_fungus": "Testo con fungo carmesí", "block.minecraft.potted_crimson_roots": "Testo con ra<PERSON><PERSON> car<PERSON>", "block.minecraft.potted_dandelion": "Testo con mexacán", "block.minecraft.potted_dark_oak_sapling": "Testo con gromo de carballo escuro", "block.minecraft.potted_dead_bush": "Testo con arbusto morto", "block.minecraft.potted_fern": "Testo con fento", "block.minecraft.potted_flowering_azalea_bush": "Testo con azalea en flor", "block.minecraft.potted_jungle_sapling": "Testo con gromo de selva", "block.minecraft.potted_lily_of_the_valley": "Testo con lirio dos vales", "block.minecraft.potted_mangrove_propagule": "Testo con propágulo de mangleiro", "block.minecraft.potted_oak_sapling": "Testo con gromo de carballo", "block.minecraft.potted_open_eyeblossom": "Testo con fitamel aberta", "block.minecraft.potted_orange_tulip": "Testo con tulipán laranxa", "block.minecraft.potted_oxeye_daisy": "Testo con margarida", "block.minecraft.potted_pale_oak_sapling": "Testo con gromo de carballo esbrancuxado", "block.minecraft.potted_pink_tulip": "Testo con tulipán rosa", "block.minecraft.potted_poppy": "Testo con papoula", "block.minecraft.potted_red_mushroom": "Testo con cogomelo vermello", "block.minecraft.potted_red_tulip": "Testo con tulipán vermello", "block.minecraft.potted_spruce_sapling": "Testo con gromo de abeto", "block.minecraft.potted_torchflower": "Testo con planta de lume", "block.minecraft.potted_warped_fungus": "Testo con fungo deformado", "block.minecraft.potted_warped_roots": "Testo con raíces deformadas", "block.minecraft.potted_white_tulip": "Testo con tulipán branco", "block.minecraft.potted_wither_rose": "Testo con rosa do <PERSON>", "block.minecraft.powder_snow": "Neve en po", "block.minecraft.powder_snow_cauldron": "Caldeiro de neve en po", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON> propul<PERSON>", "block.minecraft.prismarine": "Prismariña", "block.minecraft.prismarine_brick_slab": "Lastra de ladrillos de prismariña", "block.minecraft.prismarine_brick_stairs": "Escaleiras de ladrillos de prismariña", "block.minecraft.prismarine_bricks": "Ladrillos de prismariña", "block.minecraft.prismarine_slab": "Lastra de prismariña", "block.minecraft.prismarine_stairs": "Escaleiras de prismariña", "block.minecraft.prismarine_wall": "<PERSON>ro de prismariña", "block.minecraft.pumpkin": "Cabaza", "block.minecraft.pumpkin_stem": "<PERSON>lo de <PERSON>", "block.minecraft.purple_banner": "Estandarte púrpura", "block.minecraft.purple_bed": "Cama púrpura", "block.minecraft.purple_candle": "Candea púrpura", "block.minecraft.purple_candle_cake": "Torta con candea púrpura", "block.minecraft.purple_carpet": "Alfombra púrpura", "block.minecraft.purple_concrete": "Formigón púrpura", "block.minecraft.purple_concrete_powder": "Cemento púrpura", "block.minecraft.purple_glazed_terracotta": "Azulexo púrpura", "block.minecraft.purple_shulker_box": "Cai<PERSON> de shulker púrpura", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> de púrpura", "block.minecraft.purple_stained_glass_pane": "Panel de vidro tinguido de púrpura", "block.minecraft.purple_terracotta": "Terracota púrpura", "block.minecraft.purple_wool": "La púrpura", "block.minecraft.purpur_block": "Bloque de púrpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON>", "block.minecraft.purpur_slab": "Lastra de púrpur", "block.minecraft.purpur_stairs": "Escaleiras de púrpur", "block.minecraft.quartz_block": "Bloque de cu<PERSON>zo", "block.minecraft.quartz_bricks": "Ladrillos de cuarzo", "block.minecraft.quartz_pillar": "<PERSON><PERSON> <PERSON>", "block.minecraft.quartz_slab": "Lastra de cu<PERSON>zo", "block.minecraft.quartz_stairs": "Escaleiras de cuarzo", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Bloque de cobre cru", "block.minecraft.raw_gold_block": "Bloque de ouro cru", "block.minecraft.raw_iron_block": "Bloque de ferro cru", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> vermello", "block.minecraft.red_bed": "<PERSON><PERSON> vermella", "block.minecraft.red_candle": "Candea vermella", "block.minecraft.red_candle_cake": "Torta con candea vermella", "block.minecraft.red_carpet": "Alfombra vermella", "block.minecraft.red_concrete": "Formigón vermello", "block.minecraft.red_concrete_powder": "<PERSON><PERSON> vermello", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON><PERSON> vermello", "block.minecraft.red_mushroom_block": "Bloque de cogomelo vermello", "block.minecraft.red_nether_brick_slab": "Lastra de ladrillos do Nether vermellos", "block.minecraft.red_nether_brick_stairs": "Escaleiras de ladrillos do Nether vermellos", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> de ladrillos do Nether vermellos", "block.minecraft.red_nether_bricks": "Ladrillos do Nether vermellos", "block.minecraft.red_sand": "Area vermella", "block.minecraft.red_sandstone": "<PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Lastra de arenito vermello", "block.minecraft.red_sandstone_stairs": "Escaleiras de arenito vermello", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> de arenito vermello", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> vermella", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> de ve<PERSON>ello", "block.minecraft.red_stained_glass_pane": "Panel de vidro tinguido de vermello", "block.minecraft.red_terracotta": "Terracota vermella", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.red_wool": "La vermella", "block.minecraft.redstone_block": "Bloque de redstone", "block.minecraft.redstone_lamp": "Lámpada de redstone", "block.minecraft.redstone_ore": "Mineral de redstone", "block.minecraft.redstone_torch": "Facho de redstone", "block.minecraft.redstone_wall_torch": "Facho de redstone de parede", "block.minecraft.redstone_wire": "Fío de redstone", "block.minecraft.reinforced_deepslate": "Lousa profunda reforzada", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Bloque de comandos de repetición", "block.minecraft.resin_block": "Bloque de recina", "block.minecraft.resin_brick_slab": "Lastra de ladrillos de recina", "block.minecraft.resin_brick_stairs": "Escaleiras de ladrillos de recina", "block.minecraft.resin_brick_wall": "<PERSON>ro de ladrillos de recina", "block.minecraft.resin_bricks": "Ladrillos de recina", "block.minecraft.resin_clump": "<PERSON><PERSON> de recina", "block.minecraft.respawn_anchor": "Áncora de reaparición", "block.minecraft.rooted_dirt": "Terra enraizada", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Area", "block.minecraft.sandstone": "<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Lastra de arenito", "block.minecraft.sandstone_stairs": "Escaleiras de arenito", "block.minecraft.sandstone_wall": "<PERSON><PERSON>", "block.minecraft.scaffolding": "Estada", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador de sculk", "block.minecraft.sculk_sensor": "Sensor de sculk", "block.minecraft.sculk_shrieker": "<PERSON><PERSON> de sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Lanterna ma<PERSON>ña", "block.minecraft.sea_pickle": "Cogombro de <PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.set_spawn": "Punto de reaparición estabelecido", "block.minecraft.short_dry_grass": "Herba seca baixa", "block.minecraft.short_grass": "<PERSON><PERSON> baixa", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Caveira de esqueleto", "block.minecraft.skeleton_wall_skull": "Caveira de esqueleto de parede", "block.minecraft.slime_block": "Bloque de slime", "block.minecraft.small_amethyst_bud": "Gromo de ametista pequeno", "block.minecraft.small_dripleaf": "<PERSON><PERSON> pin<PERSON> pequena", "block.minecraft.smithing_table": "Mesa de ferraría", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Ba<PERSON>to liso", "block.minecraft.smooth_quartz": "Bloque de cuarzo liso", "block.minecraft.smooth_quartz_slab": "Lastra de cuarzo liso", "block.minecraft.smooth_quartz_stairs": "Escaleiras de cuarzo liso", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> vermello liso", "block.minecraft.smooth_red_sandstone_slab": "Lastra de arenito vermello liso", "block.minecraft.smooth_red_sandstone_stairs": "Escaleiras de arenito vermello liso", "block.minecraft.smooth_sandstone": "<PERSON><PERSON> liso", "block.minecraft.smooth_sandstone_slab": "Lastra de arenito liso", "block.minecraft.smooth_sandstone_stairs": "Escaleiras de arenito liso", "block.minecraft.smooth_stone": "Pedra lisa", "block.minecraft.smooth_stone_slab": "Lastra de pedra lisa", "block.minecraft.sniffer_egg": "Ovo de cheirador", "block.minecraft.snow": "<PERSON>eve", "block.minecraft.snow_block": "Bloque de neve", "block.minecraft.soul_campfire": "Cacharela de ánimas", "block.minecraft.soul_fire": "<PERSON><PERSON>", "block.minecraft.soul_lantern": "Lanterna de ánimas", "block.minecraft.soul_sand": "Area de ánimas", "block.minecraft.soul_soil": "Terra de ánimas", "block.minecraft.soul_torch": "Facho de ánimas", "block.minecraft.soul_wall_torch": "Facho de ánimas de parede", "block.minecraft.spawn.not_valid": "Non tes cama ou áncora de reaparición, ou estaban obstruídas", "block.minecraft.spawner": "Invocador de monstros", "block.minecraft.spawner.desc1": "Interactúa cun ovo de invocación:", "block.minecraft.spawner.desc2": "Estabelece o tipo de criatura", "block.minecraft.sponge": "Esponxa", "block.minecraft.spore_blossom": "Flor de esporas", "block.minecraft.spruce_button": "Botón de abeto", "block.minecraft.spruce_door": "Porta de abeto", "block.minecraft.spruce_fence": "Valado de abeto", "block.minecraft.spruce_fence_gate": "Cancela de abeto", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON> col<PERSON> de abeto", "block.minecraft.spruce_leaves": "Follas de abeto", "block.minecraft.spruce_log": "Tronco de abeto", "block.minecraft.spruce_planks": "Táboas de abeto", "block.minecraft.spruce_pressure_plate": "Placa de presión de abeto", "block.minecraft.spruce_sapling": "Gromo de abeto", "block.minecraft.spruce_sign": "Letreiro de abeto", "block.minecraft.spruce_slab": "Lastra de abeto", "block.minecraft.spruce_stairs": "Escaleiras de abeto", "block.minecraft.spruce_trapdoor": "Zapón de abeto", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante de abeto de parede", "block.minecraft.spruce_wall_sign": "Letreiro de abeto de parede", "block.minecraft.spruce_wood": "Madeira de abeto", "block.minecraft.sticky_piston": "Pistón pegañento", "block.minecraft.stone": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Lastra de ladrillos de pedra", "block.minecraft.stone_brick_stairs": "Escaleiras de ladrillos de pedra", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de ladrillos de pedra", "block.minecraft.stone_bricks": "Ladrillos de pedra", "block.minecraft.stone_button": "Botón de pedra", "block.minecraft.stone_pressure_plate": "Placa de presión de pedra", "block.minecraft.stone_slab": "Lastra de rocha", "block.minecraft.stone_stairs": "Escaleiras de rocha", "block.minecraft.stonecutter": "Cortapedras", "block.minecraft.stripped_acacia_log": "Tronco de acacia sen cortiza", "block.minecraft.stripped_acacia_wood": "Madeira de acacia sen cortiza", "block.minecraft.stripped_bamboo_block": "Bloque de bambú sen cortiza", "block.minecraft.stripped_birch_log": "Tronco de bidueiro sen cortiza", "block.minecraft.stripped_birch_wood": "Madeira de bidueiro sen cortiza", "block.minecraft.stripped_cherry_log": "Tronco de cerdeira sen cortiza", "block.minecraft.stripped_cherry_wood": "Madeira de cereixa sen cortiza", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON> car<PERSON> sen cortiza", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON> car<PERSON> sen cortiza", "block.minecraft.stripped_dark_oak_log": "Tronco de carballo escuro sen cortiza", "block.minecraft.stripped_dark_oak_wood": "Madeira de carballo escuro sen cortiza", "block.minecraft.stripped_jungle_log": "Tronco de selva sen cortiza", "block.minecraft.stripped_jungle_wood": "Madeira de selva sen cortiza", "block.minecraft.stripped_mangrove_log": "Tronco de mangleiro sen cortiza", "block.minecraft.stripped_mangrove_wood": "Madeira de mangleiro sen cortiza", "block.minecraft.stripped_oak_log": "Tronco de carballo sen cortiza", "block.minecraft.stripped_oak_wood": "Madeira de carballo sen cortiza", "block.minecraft.stripped_pale_oak_log": "Tronco de carballo esbrancuxado sen cortiza", "block.minecraft.stripped_pale_oak_wood": "Madeira de carballo esbrancuxado sen cortiza", "block.minecraft.stripped_spruce_log": "Tronco de abeto sen cortiza", "block.minecraft.stripped_spruce_wood": "Madeira de abeto sen cortiza", "block.minecraft.stripped_warped_hyphae": "Hi<PERSON>s deformadas sen cortiza", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON> deformado sen cortiza", "block.minecraft.structure_block": "Bloque de estrutura", "block.minecraft.structure_void": "<PERSON><PERSON>que baleiro", "block.minecraft.sugar_cane": "Cana de azucre", "block.minecraft.sunflower": "Xirasol", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON><PERSON> sosp<PERSON>", "block.minecraft.suspicious_sand": "Area sospeitosa", "block.minecraft.sweet_berry_bush": "Arbusto de bagas doces", "block.minecraft.tall_dry_grass": "Herba seca alta", "block.minecraft.tall_grass": "Herba alta", "block.minecraft.tall_seagrass": "<PERSON><PERSON> mariña alta", "block.minecraft.target": "Albo", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "Bloque de probas", "block.minecraft.test_instance_block": "Bloque de instancia de probas", "block.minecraft.tinted_glass": "Vidro opaco", "block.minecraft.tnt": "<PERSON><PERSON><PERSON>", "block.minecraft.tnt.disabled": "As explosións de dinamita están desactivadas", "block.minecraft.torch": "<PERSON>acho", "block.minecraft.torchflower": "Planta de lume", "block.minecraft.torchflower_crop": "Cultivo de planta de lume", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON> trampa", "block.minecraft.trial_spawner": "Invocador de desafío", "block.minecraft.tripwire": "Fío trampa", "block.minecraft.tripwire_hook": "Gancho de fío trampa", "block.minecraft.tube_coral": "Coral de tubo", "block.minecraft.tube_coral_block": "Bloque de coral de tubo", "block.minecraft.tube_coral_fan": "Gorgonia de tubo", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON>ia tubular de parede", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Lastra de ladrillos de tufo", "block.minecraft.tuff_brick_stairs": "Escaleiras de ladrillos de tufo", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> de ladrillos de tufo", "block.minecraft.tuff_bricks": "Ladrillos de tufo", "block.minecraft.tuff_slab": "Lastra de tufo", "block.minecraft.tuff_stairs": "Escaleiras de tufo", "block.minecraft.tuff_wall": "<PERSON><PERSON> de tufo", "block.minecraft.turtle_egg": "Ovo de tartaruga", "block.minecraft.twisting_vines": "Trepadeiras retortas", "block.minecraft.twisting_vines_plant": "Planta de trepadeiras retortas", "block.minecraft.vault": "Arca", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON> verde", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Ar <PERSON> baleiro", "block.minecraft.wall_torch": "Facho de parede", "block.minecraft.warped_button": "Bo<PERSON><PERSON> deformado", "block.minecraft.warped_door": "Porta deformada", "block.minecraft.warped_fence": "Valado deformado", "block.minecraft.warped_fence_gate": "Cancela deformada", "block.minecraft.warped_fungus": "Fungo deformado", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON> colgante deformado", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON> deformadas", "block.minecraft.warped_nylium": "<PERSON><PERSON> deformado", "block.minecraft.warped_planks": "Táboas deformadas", "block.minecraft.warped_pressure_plate": "Placa de presión deformada", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON> deformadas", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> deformado", "block.minecraft.warped_slab": "<PERSON>ra deformada", "block.minecraft.warped_stairs": "Escaleiras deformadas", "block.minecraft.warped_stem": "<PERSON><PERSON> deformado", "block.minecraft.warped_trapdoor": "Zapón deformado", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> colgante deformado de parede", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> deformado de parede", "block.minecraft.warped_wart_block": "Bloque de espullas deformadas", "block.minecraft.water": "<PERSON>a", "block.minecraft.water_cauldron": "Caldeiro de auga", "block.minecraft.waxed_chiseled_copper": "Cobre cicelado encerado", "block.minecraft.waxed_copper_block": "Bloque de cobre encerado", "block.minecraft.waxed_copper_bulb": "Lámpada de cobre encerado", "block.minecraft.waxed_copper_door": "Porta de cobre encerado", "block.minecraft.waxed_copper_grate": "Reixa de cobre encerado", "block.minecraft.waxed_copper_trapdoor": "Zapón de cobre encerado", "block.minecraft.waxed_cut_copper": "Cobre cortado encerado", "block.minecraft.waxed_cut_copper_slab": "Lastra de cobre cortado encerado", "block.minecraft.waxed_cut_copper_stairs": "Escaleiras de cobre encerado", "block.minecraft.waxed_exposed_chiseled_copper": "Cobre cicelado exposto encerado", "block.minecraft.waxed_exposed_copper": "Cobre exposto encerado", "block.minecraft.waxed_exposed_copper_bulb": "Lámpada de cobre exposto encerado", "block.minecraft.waxed_exposed_copper_door": "Porta de cobre exposto encerado", "block.minecraft.waxed_exposed_copper_grate": "Reixa de cobre exposto encerado", "block.minecraft.waxed_exposed_copper_trapdoor": "Zapón de cobre exposto e encerado", "block.minecraft.waxed_exposed_cut_copper": "Cobre cortado exposto encerado", "block.minecraft.waxed_exposed_cut_copper_slab": "Lastra de cobre cortado exposto encerado", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escaleiras de cobre cortado exposto encerado", "block.minecraft.waxed_oxidized_chiseled_copper": "Cobre cicelado oxidado encerado", "block.minecraft.waxed_oxidized_copper": "Cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_bulb": "Lámpada de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_door": "Porta de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_grate": "Reixa de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_trapdoor": "Zapón de cobre oxidado e encerado", "block.minecraft.waxed_oxidized_cut_copper": "Cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_slab": "Lastra de cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escaleiras de cobre cortado oxidado encerado", "block.minecraft.waxed_weathered_chiseled_copper": "Cobre cicelado corroído encerado", "block.minecraft.waxed_weathered_copper": "Cobre corroído encerado", "block.minecraft.waxed_weathered_copper_bulb": "Lámpada de cobre corroído encerado", "block.minecraft.waxed_weathered_copper_door": "Porta de cobre corroído encerado", "block.minecraft.waxed_weathered_copper_grate": "Reixa de cobre corroído encerado", "block.minecraft.waxed_weathered_copper_trapdoor": "Zapón de cobre corroído e encerado", "block.minecraft.waxed_weathered_cut_copper": "Cobre cortado corroído encerado", "block.minecraft.waxed_weathered_cut_copper_slab": "Lastra de cobre cortado corroído encerado", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escaleiras de cobre cortado corroído encerado", "block.minecraft.weathered_chiseled_copper": "Cobre cicelado corro<PERSON>do", "block.minecraft.weathered_copper": "Cobre corroído", "block.minecraft.weathered_copper_bulb": "Lámpada de cobre corroído", "block.minecraft.weathered_copper_door": "Porta de cobre corroído", "block.minecraft.weathered_copper_grate": "Reixa de cobre corroído", "block.minecraft.weathered_copper_trapdoor": "Zapón de cobre corroído", "block.minecraft.weathered_cut_copper": "Cobre cortado corroído", "block.minecraft.weathered_cut_copper_slab": "Lastra de cobre cortado corro<PERSON>do", "block.minecraft.weathered_cut_copper_stairs": "Escaleiras de cobre cortado corro<PERSON>do", "block.minecraft.weeping_vines": "Trepadeiras choronas", "block.minecraft.weeping_vines_plant": "Planta de trepadeiras choronas", "block.minecraft.wet_sponge": "Esponxa mollada", "block.minecraft.wheat": "Cultivos de trigo", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON> branco", "block.minecraft.white_bed": "<PERSON><PERSON> branca", "block.minecraft.white_candle": "<PERSON>dea branca", "block.minecraft.white_candle_cake": "Torta con candea branca", "block.minecraft.white_carpet": "Alfombra branca", "block.minecraft.white_concrete": "Formigón branco", "block.minecraft.white_concrete_powder": "<PERSON><PERSON> branco", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> branco", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON> de shulker branca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> de branco", "block.minecraft.white_stained_glass_pane": "Panel de vidro tinguido de branco", "block.minecraft.white_terracotta": "Terracota branca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> branco", "block.minecraft.white_wool": "La branca", "block.minecraft.wildflowers": "<PERSON> silvestres", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Caveira de esqueleto do Wither", "block.minecraft.wither_skeleton_wall_skull": "Caveira de esqueleto do Wither de parede", "block.minecraft.yellow_banner": "Estandarte amarelo", "block.minecraft.yellow_bed": "<PERSON><PERSON> amarela", "block.minecraft.yellow_candle": "Candea amarela", "block.minecraft.yellow_candle_cake": "Torta con candea amarela", "block.minecraft.yellow_carpet": "Alfombra amarela", "block.minecraft.yellow_concrete": "Formigón amarelo", "block.minecraft.yellow_concrete_powder": "Cemento amarelo", "block.minecraft.yellow_glazed_terracotta": "Azulexo amarelo", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> de s<PERSON>ker amarela", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> de amarelo", "block.minecraft.yellow_stained_glass_pane": "Panel de vidro tinguido de amarelo", "block.minecraft.yellow_terracotta": "Terracota amarela", "block.minecraft.yellow_wool": "La amarela", "block.minecraft.zombie_head": "Cabeza de zombi", "block.minecraft.zombie_wall_head": "Cabeza de zombi de parede", "book.byAuthor": "de %1$s", "book.edit.title": "Pantalla de edición do libro", "book.editTitle": "Título do libro:", "book.finalizeButton": "Asinar e pechar", "book.finalizeWarning": "Nota! Cando asines o libro xa non se poderá editar.", "book.generation.0": "Orixinal", "book.generation.1": "Copia do orixinal", "book.generation.2": "Copia dunha copia", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Etiqueta non válida *", "book.pageIndicator": "Páxina %1$s de %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON><PERSON>", "book.page_button.previous": "Páxina anterior", "book.sign.title": "Pantalla de sinatura do libro", "book.sign.titlebox": "<PERSON><PERSON><PERSON><PERSON>", "book.signButton": "Asinar", "book.view.title": "Pantalla de vista do libro", "build.tooHigh": "O límite de altura para a construción é de %s bloques", "chat.cannotSend": "Non se pode enviar a mensaxe no chat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Preme para teletransportarte", "chat.copy": "Copiar", "chat.copy.click": "Fai clic para copiar ó portapapeis", "chat.deleted_marker": "Esta mensaxe foi eliminada polo servidor.", "chat.disabled.chain_broken": "Desactivouse o chat porque se rompeu a cadea de mensaxes. Tenta reconectar.", "chat.disabled.expiredProfileKey": "Desactivouse o chat porque expirou a clave pública de perfil. Tenta reconectar.", "chat.disabled.invalid_command_signature": "O comando tiña sinaturas de argumento inesperadas ou faltaban.", "chat.disabled.invalid_signature": "O chat tiña unha sinatura non válida. Tenta conectar de novo.", "chat.disabled.launcher": "Chat desactivado nas opcións do lanzador. Non se puido enviar a mensaxe.", "chat.disabled.missingProfileKey": "Desactivouse o chat porque non se atopou a clave pública de perfil. Tenta reconectar.", "chat.disabled.options": "Chat desactivado nas opcións do xogo.", "chat.disabled.out_of_order_chat": "O chat foi recibido fóra de orde. Cambiou a hora do teu sistema?", "chat.disabled.profile": "As mensaxes non están permitidas pola configuración da conta. Prema '%s' de novo para mais información.", "chat.disabled.profile.moreInfo": "As mensaxes non están permitidas pola configuración da conta. Non se poden mandar ou ver mensaxes.", "chat.editBox": "chat", "chat.filtered": "Filtrado polo servidor.", "chat.filtered_full": "O servidor agochou a túa mensaxe a algúns xogadores.", "chat.link.confirm": "Estás seguro de querer abrir esta páxina web?", "chat.link.confirmTrusted": "Queres abrir esta ligazón ou copiala ao portapapeis?", "chat.link.open": "Abrir ligazón", "chat.link.warning": "Nunca abras ligazóns de persoas nas que non confíes!", "chat.queue": "[+%s liñas pendentes]", "chat.square_brackets": "[%s]", "chat.tag.error": "O servidor enviou unha mensaxe inválida.", "chat.tag.modified": "Mensaxe modificada polo servidor. Orixinal:", "chat.tag.not_secure": "Mensaxe non verificada. Non se pode denunciar.", "chat.tag.system": "Mensaxe do servidor. Non se pode denunciar.", "chat.tag.system_single_player": "Mensaxe do servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s completou o desafío %s", "chat.type.advancement.goal": "%s atinxiu o obxectivo %s", "chat.type.advancement.task": "%s completou a proeza %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Mensaxe de equipo", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s di %s", "chat.validation_error": "Erro de validación do chat", "chat_screen.message": "Mensaxe a enviar: %s", "chat_screen.title": "Pantalla do chat", "chat_screen.usage": "Escribe a mensaxe e preme Entrar para enviar", "chunk.toast.checkLog": "Consulta o rexistro para saberes máis", "chunk.toast.loadFailure": "Erro ao cargar o chunk en %s", "chunk.toast.lowDiskSpace": "Pouco espazo no disco!", "chunk.toast.lowDiskSpace.description": "Talvez non sexa probable gardar o mundo.", "chunk.toast.saveFailure": "Erro ao salvar o chunk en %s", "clear.failed.multiple": "Non se encontraron obxectos no inventario de %s xogadores", "clear.failed.single": "Non se encontraron obxectos no inventario de %s", "color.minecraft.black": "Negro", "color.minecraft.blue": "Azul", "color.minecraft.brown": "<PERSON>r<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verde", "color.minecraft.light_blue": "<PERSON><PERSON>l claro", "color.minecraft.light_gray": "<PERSON><PERSON> claro", "color.minecraft.lime": "Lima", "color.minecraft.magenta": "<PERSON>ent<PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "Púrpura", "color.minecraft.red": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.white": "Branco", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<-- [AQUÍ]", "command.context.parse_error": "%s na posición %s: %s", "command.exception": "Non se pode analizar o comando: %s", "command.expected.separator": "Esperábase un espazo en branco para separar un argumento. Revisa e separa os datos", "command.failed": "Produciuse un erro inesperado ao executar o comando", "command.forkLimit": "Alcanzouse o número máximo de contextos (%s)", "command.unknown.argument": "O comando está incompleto", "command.unknown.command": "Comando descoñecido ou incompleto, deseguido ollarás o erro", "commands.advancement.criterionNotFound": "A proeza %1$s non contén o criterio '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Non se puido dar o criterio '%s' da proeza %s a %s xogadores porque xa o teñen", "commands.advancement.grant.criterion.to.many.success": "Déuselle o criterio '%s' da proeza %s a %s xogadores", "commands.advancement.grant.criterion.to.one.failure": "Non se puido dar o criterio '%s' da proeza %s a %s porque xa o ten", "commands.advancement.grant.criterion.to.one.success": "Déuselle o criterio '%s' da proeza %s a %s", "commands.advancement.grant.many.to.many.failure": "Non se puideron dar %s proezas a %s xogadores porque xa as teñen", "commands.advancement.grant.many.to.many.success": "Déronselles %s proezas a %s xogadores", "commands.advancement.grant.many.to.one.failure": "Non se puideron dar %s proezas a %s porque xa as ten", "commands.advancement.grant.many.to.one.success": "Déronselle %s proezas a %s", "commands.advancement.grant.one.to.many.failure": "Non se puido dar a proeza %s a %s xogadores porque xa a teñen", "commands.advancement.grant.one.to.many.success": "Déuselles a proeza %s a %s xogadores", "commands.advancement.grant.one.to.one.failure": "Non se puido dar a proeza %s a %s porque xa a ten", "commands.advancement.grant.one.to.one.success": "Déuselle a proeza %s a %s", "commands.advancement.revoke.criterion.to.many.failure": "Non se lles puido revogar o criterio '%s' da proeza %s a %s xogadores porque non o tiñan", "commands.advancement.revoke.criterion.to.many.success": "Revogóuselles o criterio '%s' da proeza %s a %s xogadores", "commands.advancement.revoke.criterion.to.one.failure": "Non se lle puido revogar o criterio '%s' da proeza %s a %s porque non o tiña", "commands.advancement.revoke.criterion.to.one.success": "Revogóuselle o criterio '%s' da proeza %s a %s", "commands.advancement.revoke.many.to.many.failure": "Non se lles puideron revogar %s proezas a %s xogadores porque non as teñen", "commands.advancement.revoke.many.to.many.success": "Revogáronselles %s proezas a %s xogadores", "commands.advancement.revoke.many.to.one.failure": "Non se lle puideron revogar %s proezas a %s porque non as ten", "commands.advancement.revoke.many.to.one.success": "Revogáronselle %s proezas a %s", "commands.advancement.revoke.one.to.many.failure": "Non se lles puido revogar a proeza %s a %s xogadores porque non as tiñan", "commands.advancement.revoke.one.to.many.success": "Revogáronselle a proeza %s a %s xogadores", "commands.advancement.revoke.one.to.one.failure": "Non se lle puido revogar a proeza %s a %s porque non a tiña", "commands.advancement.revoke.one.to.one.success": "Revogóuselle a proeza %s a %s", "commands.attribute.base_value.get.success": "O valor base do atributo %s para a entidade %s é %s", "commands.attribute.base_value.reset.success": "O valor de base para o atributo %s para a entidade %s foi restaurado ao orixinal %s", "commands.attribute.base_value.set.success": "O valor base para o atributo %s para a entidade %s está en %s", "commands.attribute.failed.entity": "%s non é unha entidade válida para este comando", "commands.attribute.failed.modifier_already_present": "O modificador %s xa está presente no atributo %s para a entidade %s", "commands.attribute.failed.no_attribute": "A entidade %s non ten o atributo %s", "commands.attribute.failed.no_modifier": "O atributo %s da entidade %s non ten o modificador %s", "commands.attribute.modifier.add.success": "Modificador engadido %s ó atributo %s para a entidade %s", "commands.attribute.modifier.remove.success": "Modificador eliminado %s do atributo %s para a entidade %s", "commands.attribute.modifier.value.get.success": "O valor do modificador %s no atributo %s para a entidade %s é %s", "commands.attribute.value.get.success": "Valor do atributo %s para a entidade %s é %s", "commands.ban.failed": "Nada cambiou pois ese xogador/a xa está bloqueado/a", "commands.ban.success": "Bloqueouse a %s: %s", "commands.banip.failed": "<PERSON><PERSON>, pois ese enderezo IP xa está bloqueado", "commands.banip.info": "Esta prohibición afecta a %s player(s): %s", "commands.banip.invalid": "Enderezo IP non válido ou xogador descoñecido", "commands.banip.success": "Bloqueouse a IP %s: %s", "commands.banlist.entry": "%s foi bloqueado por %s: %s", "commands.banlist.entry.unknown": "(Descoñecido)", "commands.banlist.list": "Hai %s contas bloqueada(s):", "commands.banlist.none": "Non existen bloqueos", "commands.bossbar.create.failed": "Xa existe unha barra de xefe coa ID \"%s\"", "commands.bossbar.create.success": "Creouse a barra de xefe \"%s\"", "commands.bossbar.get.max": "A barra de xefe \"%s\" ten un máximo de %s", "commands.bossbar.get.players.none": "A barra de xefe \"%s\" non ten xogadores conectados", "commands.bossbar.get.players.some": "A barra de xefe \"%s\" ten %s xogador(es) conectado(s): %s", "commands.bossbar.get.value": "A barra de xefe \"%s\" ten un valor de %s", "commands.bossbar.get.visible.hidden": "A barra de xefe \"%s\" está agochada", "commands.bossbar.get.visible.visible": "A barra de xefe \"%s\" é visible", "commands.bossbar.list.bars.none": "Non hai barras de xefes activas", "commands.bossbar.list.bars.some": "Hai %s barra(s) de xefe activa(s): %s", "commands.bossbar.remove.success": "Eliminouse a barra de xefe \"%s\"", "commands.bossbar.set.color.success": "A barra de xefe \"%s\" cambiou de cor", "commands.bossbar.set.color.unchanged": "Nada cambio<PERSON>, esa xa é a cor desta barra de xefe", "commands.bossbar.set.max.success": "A barra de xefe \"%s\" cambiou o máximo a %s", "commands.bossbar.set.max.unchanged": "Nada cambiou, iso xa é o máximo desta barra de xefe", "commands.bossbar.set.name.success": "A barra de xefe \"%s\" renomeouse", "commands.bossbar.set.name.unchanged": "Nada cambio<PERSON>, ese xa é o nome desta barra de xefe", "commands.bossbar.set.players.success.none": "A barra de xefe \"%s\" non ten xogadores asociados", "commands.bossbar.set.players.success.some": "A barra de xefe %s ten agora %s xogador(es): %s", "commands.bossbar.set.players.unchanged": "Nada camb<PERSON>, eses xogadores xa están na barra de xefe sen ninguén para engadir ou eliminar", "commands.bossbar.set.style.success": "A barra de xefe \"%s\" cambiou de estilo", "commands.bossbar.set.style.unchanged": "Nada cambio<PERSON>, ese xa é o estilo desta barra de xefe", "commands.bossbar.set.value.success": "A barra de xefe \"%s\" cambiou o valor a %s", "commands.bossbar.set.value.unchanged": "Nada cambiou, iso xa é o valor desta barra de xefe", "commands.bossbar.set.visibility.unchanged.hidden": "Nada cambiou, a barra de xefe xa está escondida", "commands.bossbar.set.visibility.unchanged.visible": "Nada cambiou, a barra de xefe xa está visible", "commands.bossbar.set.visible.success.hidden": "A barra de xefe \"%s\" está agora agochada", "commands.bossbar.set.visible.success.visible": "A barra de xefe \"%s\" é agora visible", "commands.bossbar.unknown": "Non existe ningunha barra de xefe coa ID \"%s\"", "commands.clear.success.multiple": "Eliminados %s obxecto(s) de %s xogadores", "commands.clear.success.single": "Eliminados %s obxecto(s) do xogador %s", "commands.clear.test.multiple": "Atopado %s coincidente(s) en %s xogadores", "commands.clear.test.single": "Atopado %s coincidente(s) no xogador %s", "commands.clone.failed": "Non se clonaron bloques", "commands.clone.overlap": "As áreas de orixe e destino non poden ser iguais", "commands.clone.success": "Clonouse %s bloque(s)", "commands.clone.toobig": "Hai demasiados bloques na área especificada (o máximo é %s, especificáronse %s)", "commands.damage.invulnerable": "O obxectivo é invulnerable para o tipo de dano dado", "commands.damage.success": "Danos por %s aplicados a %s", "commands.data.block.get": "%s do bloque en %s, %s, %s, tras un factor de escalado de %s, cambiou a %s", "commands.data.block.invalid": "O bloque especificado non é un bloque-entidade", "commands.data.block.modified": "Modificáronse os datos do bloque en %s, %s, %s", "commands.data.block.query": "O bloque en %s, %s, %s contén os seguintes datos: %s", "commands.data.entity.get": "%s de %s, tras un factor de escalado de %s, cambiou a %s", "commands.data.entity.invalid": "Non se pudieron modificar os datos do xogador", "commands.data.entity.modified": "Modificáronse os datos da entidade %s", "commands.data.entity.query": "A entidade %s ten os seguintes datos: %s", "commands.data.get.invalid": "Non se puideron obter %s, só se permiten etiquetas numéricas", "commands.data.get.multiple": "Este argumento acepta un só valor NBT", "commands.data.get.unknown": "Non se puido obter %s, a etiqueta non existe", "commands.data.merge.failed": "Nada cambio<PERSON>. As propiedades especificadas xa tiñan estes valores", "commands.data.modify.expected_list": "Esperábase unha lista, recibiuse: %s", "commands.data.modify.expected_object": "Esperábase un obxeto, recibiuse: %s", "commands.data.modify.expected_value": "Valor esperado, obtido: %s", "commands.data.modify.invalid_index": "Índice de lista inválido: %s", "commands.data.modify.invalid_substring": "Índices de subcadea non válidos: de %s a %s", "commands.data.storage.get": "%s no contedor %s, tras un factor de escalado de %s é %s", "commands.data.storage.modified": "Modificouse o contedor %s", "commands.data.storage.query": "O contedor %s contén: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "O paquete de datos \"%s\" non estaba activo!", "commands.datapack.disable.failed.feature": "O paquete '%s' non se pode desactivar, xa que forma parte dunha marca activada!", "commands.datapack.enable.failed": "O paquete de datos \"%s\" xa estaba activo!", "commands.datapack.enable.failed.no_flags": "<PERSON><PERSON> <PERSON><PERSON><PERSON> '%s' non se poden habilitar, xa que as bandeiras necesarias non están habilitadas neste mundo: %s!", "commands.datapack.list.available.none": "Non hai máis paquetes de datos dispoñibles", "commands.datapack.list.available.success": "Hai %s paquete(s) de dato(s) dispoñible(s): %s", "commands.datapack.list.enabled.none": "Non hai paquetes de datos activos", "commands.datapack.list.enabled.success": "Hai %s paquete(s) de dato(s) activo(s): %s", "commands.datapack.modify.disable": "Desactivando o paquete de datos %s", "commands.datapack.modify.enable": "Activando o paquete de datos %s", "commands.datapack.unknown": "Paquete de datos descoñecido: %s", "commands.debug.alreadyRunning": "A análise de ticks xa foi iniciada", "commands.debug.function.noRecursion": "Non é posíbel rastrear a partir do interior da función", "commands.debug.function.noReturnRun": "O rastrexo non se pode usar coa execución de retorno", "commands.debug.function.success.multiple": "Trazado %s comando(s) de %s funcións para saída de ficheiro %s", "commands.debug.function.success.single": "Trazado %s comando(s) desde a función '%s' ata o ficheiro de saída %s", "commands.debug.function.traceFailed": "Non foi posíbel rastrear a función", "commands.debug.notRunning": "A análise de ticks non foi iniciada", "commands.debug.started": "Iniciouse o ciclo de perfilado", "commands.debug.stopped": "A análise de ticks foi detida após %s segundos e %s ticks (%s ticks por segundo)", "commands.defaultgamemode.success": "O modo de xogo por defecto agora é %s", "commands.deop.failed": "Non se modificou nada, o xogador non é administrador", "commands.deop.success": "%s xa non é administrador do servidor", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "A dificultade non cambiou, xa estaba configurada en %s", "commands.difficulty.query": "A dificultade é %s", "commands.difficulty.success": "A dificultade foi mudada a %s", "commands.drop.no_held_items": "A entidade non pode soster ningún elemento", "commands.drop.no_loot_table": "A entidade %s non ten táboa de botín", "commands.drop.no_loot_table.block": "O bloque %s non ten unha táboa de botín", "commands.drop.success.multiple": "Soltáronse %s obxetos", "commands.drop.success.multiple_with_table": "Soltáronse %s obxectos da táboa de botín %s", "commands.drop.success.single": "Soltouse %s de %s", "commands.drop.success.single_with_table": "Soltáronse %s de %s da táboa de botín %s", "commands.effect.clear.everything.failed": "O obxectivo non ten efectos que eliminar", "commands.effect.clear.everything.success.multiple": "Elimináronse os efectos de %s obxectivos", "commands.effect.clear.everything.success.single": "Elimináronse os efectos de %s", "commands.effect.clear.specific.failed": "O destino non ten o efecto solicitado", "commands.effect.clear.specific.success.multiple": "Eliminouse o efecto %s de %s obxectivos", "commands.effect.clear.specific.success.single": "Eliminouse o efecto %s de %s", "commands.effect.give.failed": "Non se pode aplicar este efecto (o obxectivo é inmune aos efectos ou ten algo máis forte)", "commands.effect.give.success.multiple": "Aplicouse o efecto %s aos obxectivos %s", "commands.effect.give.success.single": "Aplicouse o efecto %s a %s", "commands.enchant.failed": "Non se modificou nada, os obxectivos non tiñan ningún elemento nas súas mans ou o encantamento non se puido aplicar", "commands.enchant.failed.entity": "%s non é unha entidade válida para este comando", "commands.enchant.failed.incompatible": "%s non admite este encantamento", "commands.enchant.failed.itemless": "%s non sostén ningún objeto", "commands.enchant.failed.level": "%s é superior ao nivel máximo do encantamento (%s)", "commands.enchant.success.multiple": "Aplicouse o encantamento %s a %s entidades", "commands.enchant.success.single": "Aplicouse o encantamento %s ao obxecto de %s", "commands.execute.blocks.toobig": "Demasiados bloques na área especificada (máximo %s, %s especificados)", "commands.execute.conditional.fail": "Proba non superada", "commands.execute.conditional.fail_count": "Proba non superada, cantidade: %s", "commands.execute.conditional.pass": "Proba superada", "commands.execute.conditional.pass_count": "Proba superada, cantidade: %s", "commands.execute.function.instantiationFailure": "Produciuse un erro ao crear unha instancia da función %s: %s", "commands.experience.add.levels.success.multiple": "Déronse %s nivéis de experiencia a %s xogadores", "commands.experience.add.levels.success.single": "Déronse %s nivéis de experiencia a %s", "commands.experience.add.points.success.multiple": "Déronse %s puntos de experiencia a %s xogadores", "commands.experience.add.points.success.single": "Déronse %s puntos de experiencia a %s", "commands.experience.query.levels": "%s ten %s nivéis de experiencia", "commands.experience.query.points": "%s ten %s puntos de experiencia", "commands.experience.set.levels.success.multiple": "Cambiáronse os nivéis de experiencia de %2$s xogadores a %1$s", "commands.experience.set.levels.success.single": "Establecéronse %s nivéis de experiencia para %s", "commands.experience.set.points.invalid": "Non se poden establecer puntos de experiencia por enriba dos puntos actuáis", "commands.experience.set.points.success.multiple": "Cambiáronse os puntos de experiencia de %2$s a %1$s xogadores", "commands.experience.set.points.success.single": "Cambiáronse os puntos de experiencia de %2$s a %1$s", "commands.fill.failed": "Non se encheron bloques", "commands.fill.success": "Recheouse %s bloque(s)", "commands.fill.toobig": "Hai demasiados bloques na área especificada (o máximo é %s, especificáronse %s)", "commands.fillbiome.success": "Biomas fixados entre %s, %s, %s e %s, %s, %s", "commands.fillbiome.success.count": "%% entrada(s) de bioma fixada(s) entre %s, %s, %s e %s, %s, %s", "commands.fillbiome.toobig": "Demasiados bloques no volume especificado (máximo %s, %s especificados)", "commands.forceload.added.failure": "Non se marcou ningún chunk para cargar á forza", "commands.forceload.added.multiple": "Marcáronse %s chunks en %s de %s a %s para ser cargados á forza", "commands.forceload.added.none": "Non foi atopado ningún chunk cargado á forza en %s", "commands.forceload.added.single": "Marcouse o chunk %s en %s para ser cargado á forza", "commands.forceload.list.multiple": "%s chunks cargados á forza foron atopados en %s en: %s", "commands.forceload.list.single": "Un chunk cargado á forza foi atopando en %s en: %s", "commands.forceload.query.failure": "O chunk de %s en %s non se atopa marcado para carga forzosa", "commands.forceload.query.success": "O chunk de %s en %s atópase marcado para carga forzosa", "commands.forceload.removed.all": "Desmarcáronse todos os chunks cargados á forza en %s", "commands.forceload.removed.failure": "Non se eliminou a carga forzosa de ningún chunk", "commands.forceload.removed.multiple": "%s chunks foron desmarcados en %s dende %s ata %s pola carga forzosa", "commands.forceload.removed.single": "Desmarcado o chunk %s en %s da carga forzosa", "commands.forceload.toobig": "Hai demasiados chunks na área especificada (o máximo é %s, especificáronse %s)", "commands.function.error.argument_not_compound": "Tipo de argumento non válido: %s, composto esperado", "commands.function.error.missing_argument": "Falta o argumento %2$s para a función %1$s", "commands.function.error.missing_arguments": "Faltan argumentos na función %s", "commands.function.error.parse": "Ao instanciar a macro %s: o comando '%s' provocou un erro: %s", "commands.function.instantiationFailure": "Erro ao crear unha instancia da función %s: %s", "commands.function.result": "A función «%s» devolveu %s", "commands.function.scheduled.multiple": "Executando funcións %s", "commands.function.scheduled.no_functions": "Non se atopou ningunha función para o nome %s", "commands.function.scheduled.single": "Función en execución %s", "commands.function.success.multiple": "Executar %s comando(s) de %s funcións", "commands.function.success.multiple.result": "Executouse %s funcións", "commands.function.success.single": "Executar %s comando(s) da función '%s'", "commands.function.success.single.result": "A función '%2$s' volveu a %1$s", "commands.gamemode.success.other": "Cambiouse o modo de xogo de %s a %s", "commands.gamemode.success.self": "Cambiáches o teu modo de xogo a %s", "commands.gamerule.query": "A regra %s está establecida como \"%s\"", "commands.gamerule.set": "A regra %s estableceuse como \"%s\"", "commands.give.failed.toomanyitems": "Non se pode dar máis de %s de %s", "commands.give.success.multiple": "%3$s xogadores recibiron %1$s de %2$s", "commands.give.success.single": "%3$s recibiu %1$s de %2$s", "commands.help.failed": "Comando descoñec<PERSON> ou permisos insuficientes", "commands.item.block.set.success": "Substituíuse un espazo en %s, %s, %s, con %s", "commands.item.entity.set.success.multiple": "Substituíuse un espazo en %s entidades con %s", "commands.item.entity.set.success.single": "Substituíuse un espazo en %s con %s", "commands.item.source.no_such_slot": "A orixe non ten o espazo %s", "commands.item.source.not_a_container": "A posición de orixe %s, %s, %s non é un recipiente", "commands.item.target.no_changed.known_item": "Ningún albo aceptou o obxecto %s no espazo %s", "commands.item.target.no_changes": "Ningún albo aceptou o obxecto no espazo %s", "commands.item.target.no_such_slot": "O obxectivo non ten o espazo %s", "commands.item.target.not_a_container": "A posición de destino %s, %s, %s non é un recipiente", "commands.jfr.dump.failed": "Erro ao copiar o rexistro do JFR: %s", "commands.jfr.start.failed": "Erro ao comezar perfilado JFR", "commands.jfr.started": "Comezado perfilado JFR", "commands.jfr.stopped": "Detívose o perfilado JFR e copiouse a %s", "commands.kick.owner.failed": "Non se pode expulsar ao dono dun mundo en LAN", "commands.kick.singleplayer.failed": "Non se pode expulsar nun mundo para un xogador", "commands.kick.success": "Botouse a %s: %s", "commands.kill.success.multiple": "Eliminadas %s entidades", "commands.kill.success.single": "%s morreu", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hai %s dun máximo de %s xogadores en liña: %s", "commands.locate.biome.not_found": "Non se puido atopar o bioma \"%s\" nunha distacia razoable", "commands.locate.biome.success": "O %s máis próximo está en %s (%s bloques de distancia)", "commands.locate.poi.not_found": "Non se puido atopar un punto de interese do tipo \"%s\" a unha distancia razoable", "commands.locate.poi.success": "O %s máis próximo está en %s (%s bloques de distancia)", "commands.locate.structure.invalid": "Non hai ningunha estrutura de tipo \"%s\"", "commands.locate.structure.not_found": "Ningunha estructura de tipo \"%s\" foi encontrada preto", "commands.locate.structure.success": "A estrutura de tipo %s máis preto está en %s (a %s bloques de distancia)", "commands.message.display.incoming": "%s murmúrate: %s", "commands.message.display.outgoing": "Murmuraches a %s: %s", "commands.op.failed": "Non se modificou nada, o xogador xa é administrador", "commands.op.success": "%s convertiuse en administrador(a) do servidor", "commands.pardon.failed": "Non se modificou nada, o xogador non está bloqueado", "commands.pardon.success": "Desbloqueouse a %s", "commands.pardonip.failed": "Non se modificou nada, esa IP non estaba bloqueada", "commands.pardonip.invalid": "Dirección IP inválida", "commands.pardonip.success": "Desbloqueouse a IP %s", "commands.particle.failed": "A partícula non era visible para ninguén", "commands.particle.success": "Amosando partícula %s", "commands.perf.alreadyRunning": "A análise de desempeño xa foi iniciada", "commands.perf.notRunning": "A análise de desempeño non foi iniciada", "commands.perf.reportFailed": "Erro ao crear o informe", "commands.perf.reportSaved": "Creado informe en %s", "commands.perf.started": "A análise de desempeño foi iniciada por 10 segundos (emprega \"/perf stop\" para detela)", "commands.perf.stopped": "Perfil de rendemento parado despois de %s segundo(s) e %s tick(s) (%s tick(s) por segundo)", "commands.place.feature.failed": "Erro ao colocar elemento", "commands.place.feature.invalid": "Non se ha encontrado ningunha característica de tipo \"%s\"", "commands.place.feature.success": "Colocouse \"%s\" en %s, %s, %s", "commands.place.jigsaw.failed": "Erro ao xerar peza", "commands.place.jigsaw.invalid": "O grupo de modelos \"%s\" non existe", "commands.place.jigsaw.success": "Peza xerada en %s, %s, %s", "commands.place.structure.failed": "Erro ao xerar estrutura", "commands.place.structure.invalid": "Non hai ningunha estrutura de tipo \"%s\"", "commands.place.structure.success": "Estrutura \"%s\" xerada en %s, %s, %s", "commands.place.template.failed": "Erro ao colocar modelo", "commands.place.template.invalid": "Non existe o modelo co ID \"%s\"", "commands.place.template.success": "Modelo \"%s\" cargado en %s, %s, %s", "commands.playsound.failed": "O son está demasiado afastado para ser escoitado", "commands.playsound.success.multiple": "Reprodúxose o son %s a %s xogadores", "commands.playsound.success.single": "Reprodúxose o son %s a %s", "commands.publish.alreadyPublished": "A partida multixogador xa se aloxa no porto %s", "commands.publish.failed": "Non se pode crear unha partida local", "commands.publish.started": "Partida local creada no porto %s", "commands.publish.success": "A partida multixogador alóxase no porto %s", "commands.random.error.range_too_large": "O valor ao chou debe ser como máximo 2147483646", "commands.random.error.range_too_small": "O valor ao chou debe ser como mínimo 2", "commands.random.reset.all.success": "Reiniciar %s secuencias ao chou", "commands.random.reset.success": "Reiniciar secuencia ao chou %s", "commands.random.roll": "%s obtivo un %s (entre %s e %s)", "commands.random.sample.success": "Valor ao chou: %s", "commands.recipe.give.failed": "Non se aprenderon novas receitas", "commands.recipe.give.success.multiple": "Desbloqueáronse %s receitas para %s xogadores", "commands.recipe.give.success.single": "Desbloqueáronse %s receitas para %s", "commands.recipe.take.failed": "Non hai recetas esquecibles", "commands.recipe.take.success.multiple": "Elimináronse %s receitas a %s xogadores", "commands.recipe.take.success.single": "Elimináronse %s receitas a %s", "commands.reload.failure": "<PERSON><PERSON> <PERSON> recargar, mantendo os datos antigos", "commands.reload.success": "Recargando!", "commands.ride.already_riding": "%s xa está montando %s", "commands.ride.dismount.success": "%s deixou de montar %s", "commands.ride.mount.failure.cant_ride_players": "Os xogadores non se poden librar", "commands.ride.mount.failure.generic": "%s non puido comezar a montar %s", "commands.ride.mount.failure.loop": "Non pode montar entidade en si mesmo nin en ningún dos seus pasaxeiros", "commands.ride.mount.failure.wrong_dimension": "Non pode montar entidade en diferentes dimensións", "commands.ride.mount.success": "%s comezou a montar %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.rotate.success": "Rotado %s", "commands.save.alreadyOff": "Gardado xa desactivado", "commands.save.alreadyOn": "Gardado xa activado", "commands.save.disabled": "Autogardado desactivado", "commands.save.enabled": "Autogardado activado", "commands.save.failed": "Erro ao gardar a partida (hai espazo no disco duro?)", "commands.save.saving": "Gardando a partida... (pode tardar un chisco!)", "commands.save.success": "Partida gardada", "commands.schedule.cleared.failure": "Non hai programacións co id %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Programouse a etiqueta \"%s\" en %s ciclos de tempo de xogo%s", "commands.schedule.macro": "Non configure unha macro", "commands.schedule.same_tick": "Non se pode programar para o ciclo actual", "commands.scoreboard.objectives.add.duplicate": "Xa existe un obxectivo con ese nome", "commands.scoreboard.objectives.add.success": "Creado novo obxectivo: %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON>da mud<PERSON>. Este espazo de mostra xa estaba baleiro", "commands.scoreboard.objectives.display.alreadySet": "<PERSON>da mud<PERSON>. Este espazo de mostra xa estaba amosando este obxectivo", "commands.scoreboard.objectives.display.cleared": "Quitáronse os obxectivos que se amosaban na ranura %s", "commands.scoreboard.objectives.display.set": "Estableceuse que a ranura %s amose o obxectivo %s", "commands.scoreboard.objectives.list.empty": "\nNon hai obxectivos", "commands.scoreboard.objectives.list.success": "Hai %s obxectivo(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Desactivouse a actualización automática da pantalla para o obxectivo %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Activouse a actualización automática da pantalla para o obxectivo %s", "commands.scoreboard.objectives.modify.displayname": "Mudouse o nome de %s a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Borrouse o formato de número predeterminado do obxectivo %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Cambiouse o formato de número predeterminado do obxectivo %s", "commands.scoreboard.objectives.modify.rendertype": "Mudouse o tipo de renderizado do obxectivo %s", "commands.scoreboard.objectives.remove.success": "Eliminado obxectivo %s", "commands.scoreboard.players.add.success.multiple": "Engadíronse %s puntos ao obxectivo %s para %s entidades", "commands.scoreboard.players.add.success.single": "Engadíronse %s puntos ao obxectivo %s para %s (agora ten %s puntos)", "commands.scoreboard.players.display.name.clear.success.multiple": "Borrouse o nome da pantalla para %s entidades en %s", "commands.scoreboard.players.display.name.clear.success.single": "Borrouse o nome da pantalla para %s en %s", "commands.scoreboard.players.display.name.set.success.multiple": "Borrouse o nome da pantalla a %s por %s entidades en %s", "commands.scoreboard.players.display.name.set.success.single": "Borrouse o nome da pantalla a %s por %s en %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Borrouse o formato de numero para %s entidades en %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Borrouse o formato de numero para %s en %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Cambiouse o formato de numero para %s entidades en %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Cambiouse o formato de numero para %s en %s", "commands.scoreboard.players.enable.failed": "Nada cambiou. O desencadeante xa estaba activo", "commands.scoreboard.players.enable.invalid": "O parámetro \"enable\" só funciona en obxectivos de tipo \"trigger\"", "commands.scoreboard.players.enable.success.multiple": "Activouse o desencadeante %s para %s entidades", "commands.scoreboard.players.enable.success.single": "Activouse o desencadeante %s para %s", "commands.scoreboard.players.get.null": "Non se puido obter a puntuación do objetivo %s para %s, non hai registros", "commands.scoreboard.players.get.success": "%s ten %s %s", "commands.scoreboard.players.list.empty": "Non hai entidades rexistradas", "commands.scoreboard.players.list.entity.empty": "%s non ten puntuación para amosar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s ten %s puntuación(s):", "commands.scoreboard.players.list.success": "Hai %s entidade(s) rexistrada(s): %s", "commands.scoreboard.players.operation.success.multiple": "Actualizouse a puntuación do obxectivo %s para %s entidades", "commands.scoreboard.players.operation.success.single": "Establecer %s para %s a %s", "commands.scoreboard.players.remove.success.multiple": "Elimináronse %s puntos do obxectivo %s para %s entidades", "commands.scoreboard.players.remove.success.single": "Elimináronse %s puntos do obxectivo %s para %s (agora ten %s puntos)", "commands.scoreboard.players.reset.all.multiple": "Reiniciáronse tódalas puntuacións de %s entidades", "commands.scoreboard.players.reset.all.single": "Reiniciáronse tódalas puntuacións de %s", "commands.scoreboard.players.reset.specific.multiple": "Reiniciouse a puntuación do obxectivo %s para %s entidades", "commands.scoreboard.players.reset.specific.single": "Reiniciouse a puntuación do obxectivo %s para %s", "commands.scoreboard.players.set.success.multiple": "Cambiouse a puntuación do obxectivo %s para %s entidades a %s", "commands.scoreboard.players.set.success.single": "Cambiouse a puntuación do obxectivo %s de %s a %s", "commands.seed.success": "Semente: %s", "commands.setblock.failed": "Non se puido colocar o bloque", "commands.setblock.success": "Cambiouse o bloque en %s, %s, %s", "commands.setidletimeout.success": "O tempo ocioso do xogador é agora %s minuto(s)", "commands.setidletimeout.success.disabled": "O tempo de espera do xogador está desactivado", "commands.setworldspawn.failure.not_overworld": "Só se pode configurar a aparición do mundo na superficie", "commands.setworldspawn.success": "Estabelecido o punto de reaparición do mundo a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Estabelecido o punto de reaparición a %s, %s, %s [%s] en %s para %s xogadores", "commands.spawnpoint.success.single": "Estabelecido o punto de reaparición a %s, %s, %s [%s] en %s para %s", "commands.spectate.not_spectator": "%s non está no modo espectador", "commands.spectate.self": "Non podes observarte a ti mesmo", "commands.spectate.success.started": "Observando a %s", "commands.spectate.success.stopped": "Xa non estás a observar ningunha entidade", "commands.spreadplayers.failed.entities": "Non podería difundir %s entidade/entidades ao redor de %s, %s (demasiadas entidades para o espazo - tentar utilizar a propagación de como máximo %s)", "commands.spreadplayers.failed.invalid.height": "MaxHeight %s é inválido; é requerido un valor máis alto que o mínimo %s do mundo", "commands.spreadplayers.failed.teams": "Non se pode distribuír %s equipo(s) ao redor de %s, %s (hai demasiadas entidades para o espazo - intenta usar unha distribución de como máximo %s)", "commands.spreadplayers.success.entities": "Distribuíuse %s xogador(es) ao redor de %s, %s cunha distancia media de %s bloques entre eles", "commands.spreadplayers.success.teams": "Distribuíuse %s equipo(s) ao redor de %s, %s cunha distancia media de %s bloques entre eles", "commands.stop.stopping": "Parando o servidor", "commands.stopsound.success.source.any": "Detidos todos os sons '%s'", "commands.stopsound.success.source.sound": "Parouse o son \"%s\" da fonte \"%s\"", "commands.stopsound.success.sourceless.any": "Detidos todos os sons", "commands.stopsound.success.sourceless.sound": "Parouse o son \"%s\"", "commands.summon.failed": "Non se puido xerar a entidade", "commands.summon.failed.uuid": "Non se puido xerar a entidade debido ás UUIDs duplicadas", "commands.summon.invalidPosition": "Posición inválida para xerar", "commands.summon.success": "A entidade %s foi invocada", "commands.tag.add.failed": "O obxectivo xa tiña a etiqueta ou tiña demasiadas", "commands.tag.add.success.multiple": "Engadiuse a etiqueta \"%s\" a %s entidades", "commands.tag.add.success.single": "Engadiuse a etiqueta \"%s\" a %s", "commands.tag.list.multiple.empty": "Non hai etiquetas nas %s entidades", "commands.tag.list.multiple.success": "As %s entidades teñen un total de %s etiquetas: %s", "commands.tag.list.single.empty": "%s non ten etiquetas", "commands.tag.list.single.success": "%s ten %s etiquetas: %s", "commands.tag.remove.failed": "O obxectivo non ten esta etiqueta", "commands.tag.remove.success.multiple": "Eliminouse a etiqueta \"%s\" de %s entidades", "commands.tag.remove.success.single": "Eliminouse a etiqueta \"%s\" de %s", "commands.team.add.duplicate": "Xa existe un equipo con este nome", "commands.team.add.success": "Creouse o equipo %s", "commands.team.empty.success": "Elimináron(se) %s membro(s) do equipo %s", "commands.team.empty.unchanged": "Nada cambiou. Este equipo xa estaba baleiro", "commands.team.join.success.multiple": "Engadíronse %s membros ao equipo %s", "commands.team.join.success.single": "Engadiuse a %s ao equipo %s", "commands.team.leave.success.multiple": "Elimináronse %s membros dos equipos", "commands.team.leave.success.single": "Eliminouse a %s do equipo", "commands.team.list.members.empty": "No hai membros no equipo %s", "commands.team.list.members.success": "O equipo %s ten %s membro(s): %s", "commands.team.list.teams.empty": "Non hai equipos", "commands.team.list.teams.success": "Hai %s equipo(s): %s", "commands.team.option.collisionRule.success": "Cambiouse a regra de colisión do equipo %s a \"%s\"", "commands.team.option.collisionRule.unchanged": "Nada cambiou. A regra de colisión xa tiña este valor", "commands.team.option.color.success": "Cambiouse a cor do equipo %s a %s", "commands.team.option.color.unchanged": "Nada cambiou. Este equipo xa tiña esa cor", "commands.team.option.deathMessageVisibility.success": "Cambiouse a visibilidade dos avisos de mortes para o equipo %s a \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nada cambiou. A visibilidade de avisos de morte xa tiña ese valor", "commands.team.option.friendlyfire.alreadyDisabled": "Nada mud<PERSON>. O fogo amigo xa estaba desactivado para este equipo", "commands.team.option.friendlyfire.alreadyEnabled": "Nada mudou. O fogo amigo xa estaba activado para este equipo", "commands.team.option.friendlyfire.disabled": "Desactivouse o fogo amigo no equipo %s", "commands.team.option.friendlyfire.enabled": "Activouse o fogo amigo no equipo %s", "commands.team.option.name.success": "Actualizado o nome do equipo %s", "commands.team.option.name.unchanged": "Nada cambiou, o equipo xa tiña ese nome", "commands.team.option.nametagVisibility.success": "Mudouse a visibilidade dos nomes de xogadores para o equipo %s a \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nada mudou. A visibilidade de nomes xa tiña este valor", "commands.team.option.prefix.success": "Cambiouse o prefixo do equipo a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON>da mud<PERSON>. Este equipo xa non podía ver compañeiros de equipo invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON>da mud<PERSON>. Este equipo xa podía ver compañeiros de equipo invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "Agora os membros do equipo %s xa non poden ver os seus compañeiros invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "Agora os membros do equipo %s poden ver os seus compañeiros invisibles", "commands.team.option.suffix.success": "Cambiouse o sufixo do equipo a %s", "commands.team.remove.success": "Eliminado o equipo %s", "commands.teammsg.failed.noteam": "Para enviar unha mensaxe de equipo tes que estar nun", "commands.teleport.invalidPosition": "Posición para teletransporte non válida", "commands.teleport.success.entity.multiple": "%s entidades teletransportadas a %s", "commands.teleport.success.entity.single": "Teletransportado %s a %s", "commands.teleport.success.location.multiple": "%s entidades teletransportadas a %s, %s, %s", "commands.teleport.success.location.single": "Teletransportouse %s a %s, %s, %s", "commands.test.batch.starting": "Comezando entorno %s para lote %s", "commands.test.clear.error.no_tests": "Non se puideron atopar probas que eliminar", "commands.test.clear.success": "Eliminada(s) %s estrutura(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Prema para copiar ao portapapeis", "commands.test.create.success": "Creada organización de proba para proba %s", "commands.test.error.no_test_containing_pos": "Non se puido atopar unha instancia de proba que conteña %s, %s, %s", "commands.test.error.no_test_instances": "Non se atoparon instancias de proba", "commands.test.error.non_existant_test": "Proba %s non puido ser atopada", "commands.test.error.structure_not_found": "Proba de estrutura %s non puido ser atopada", "commands.test.error.test_instance_not_found": "Non se puido atopar bloque entidade para instancia da proba", "commands.test.error.test_instance_not_found.position": "Non se puido atopar bloque entidade para instancia da proba en %s, %s, %s", "commands.test.error.too_large": "O tamaño da estrutura debe ser de menos de %s bloques en cada eixo", "commands.test.locate.done": "Rematada a localización, atopouse %s estrutura(s)", "commands.test.locate.found": "Atopada estrutura en: %s (distancia: %s)", "commands.test.locate.started": "Comezouse a localizar estruturas para a proba, isto podería levar un intre...", "commands.test.no_tests": "Non hai probas que realizar", "commands.test.relative_position": "Posición relativa a %s: %s", "commands.test.reset.error.no_tests": "Non se puideron atopar probas que restaurar", "commands.test.reset.success": "Resetea %s estrutura(s)", "commands.test.run.no_tests": "Non se atoparon probas", "commands.test.run.running": "Realizando %s proba(s)...", "commands.test.summary": "Eliminada(s) %s estrutura(s)", "commands.test.summary.all_required_passed": "<PERSON><PERSON> as probas requiridas realizadas con éxito :)", "commands.test.summary.failed": "%s proba(s) requiridas falidas :(", "commands.test.summary.optional_failed": "%s proba(s) opcionais falidas", "commands.tick.query.percentiles": "Percentís: P50: %sms P95: %sms P99: %sms, mostra: %s", "commands.tick.query.rate.running": "Taxa de ticks: %s por segundo.\nTempo medio por tick: %sms (Obxectivo: %sms)", "commands.tick.query.rate.sprinting": "Taxa de ticks: %s por segundo (ignóreo, só como referencia).\nTempo medio por tick: %sms", "commands.tick.rate.success": "Establece a taxa de ticks en %s por segundo", "commands.tick.sprint.report": "Correr completado con %s ticks por segundo ou %s ms por tick", "commands.tick.sprint.stop.fail": "Ningunha aceleración de tics en curso", "commands.tick.sprint.stop.success": "Interrompeuse a aceleración de tics actual", "commands.tick.status.frozen": "<PERSON><PERSON> xogo con<PERSON>", "commands.tick.status.lagging": "O xogo estase a executar, pero non se pode manter á vel. de tics do obxectivo", "commands.tick.status.running": "O xogo funciona con normalidade", "commands.tick.status.sprinting": "O xogo está esprintando", "commands.tick.step.fail": "Erro ao avanzar o xogo: o xogo debe estar conxelado antes", "commands.tick.step.stop.fail": "<PERSON><PERSON><PERSON> avance de tics en curso", "commands.tick.step.stop.success": "Interrompeuse o avance de tics", "commands.tick.step.success": "Avanzando %s tic(s)", "commands.time.query": "Hora: %s ticks", "commands.time.set": "Tempo establecido en %s", "commands.title.cleared.multiple": "Borrados os títulos de %s xogadores", "commands.title.cleared.single": "Borrados os títulos de %s", "commands.title.reset.multiple": "Reiniciadas as opcións de título para %s xogadores", "commands.title.reset.single": "Reiniciadas as opcións de título para %s", "commands.title.show.actionbar.multiple": "Amosando novo título na barra de obxectos de %s xogadores", "commands.title.show.actionbar.single": "Amosando novo título na barra de obxectos de %s", "commands.title.show.subtitle.multiple": "Amosando novo subtítulo para %s xogadores", "commands.title.show.subtitle.single": "Amosando novo subtítulo para %s", "commands.title.show.title.multiple": "Amosando novo título para %s xogadores", "commands.title.show.title.single": "Amosando novo título para %s", "commands.title.times.multiple": "Cambiouse o tempo de visualización do título para %s xogadores", "commands.title.times.single": "Cambiouse o tempo de visualización do título para %s", "commands.transfer.error.no_players": "Debes especificar polo menos un xogador para transferir", "commands.transfer.success.multiple": "Transferindo %s xogadores a %s:%s", "commands.transfer.success.single": "Transferindo %s a %s:%s", "commands.trigger.add.success": "Desencadeado %s (engadiuse %s ao valor)", "commands.trigger.failed.invalid": "Só podes activar obxectivos de tipo \"trigger\"", "commands.trigger.failed.unprimed": "Aínda non podes activar este obxectivo desencadeante", "commands.trigger.set.success": "Desencadeado %s (estableceuse o valor a %s)", "commands.trigger.simple.success": "Desencadeado %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "estable = non", "commands.version.stable.yes": "estable = si", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Cambiouse o tempo a despexado", "commands.weather.set.rain": "Cambiouse o tempo a chuvioso", "commands.weather.set.thunder": "Cambiouse o tempo a tormenta", "commands.whitelist.add.failed": "O xogador xa estaba na lista branca", "commands.whitelist.add.success": "Engadido a %s á lista branca", "commands.whitelist.alreadyOff": "A lista branca xa estaba inactiva", "commands.whitelist.alreadyOn": "A lista branca xa estaba activa", "commands.whitelist.disabled": "Desactivouse a lista branca", "commands.whitelist.enabled": "Activouse a lista branca", "commands.whitelist.list": "Hai %s xogador(es) na lista branca: %s", "commands.whitelist.none": "Non hai xogadores na lista branca", "commands.whitelist.reloaded": "Lista branca recargada", "commands.whitelist.remove.failed": "O xogador non estaba na lista branca", "commands.whitelist.remove.success": "Eliminouse a %s da lista branca", "commands.worldborder.center.failed": "Nada cambio<PERSON>, o bordo do mundo xa estaba nesta posición", "commands.worldborder.center.success": "Estabeleceuse o centro do bordo do mundo a %s, %s", "commands.worldborder.damage.amount.failed": "Nada cambio<PERSON>, o bordo do mundo xa danaba esa cantidade", "commands.worldborder.damage.amount.success": "Estableceuse a cantidade de dano fóra do bordo do mundo a %s por bloque cada segundo", "commands.worldborder.damage.buffer.failed": "Nada cambio<PERSON>, o bordo do mundo xa tiña esa distancia de zona segura", "commands.worldborder.damage.buffer.success": "Estableceuse a zona de danos do bordo do mundo en %s bloque(s) de distancia", "commands.worldborder.get": "O bordo do mundo ten actualmente un ancho de %s bloque(s)", "commands.worldborder.set.failed.big": "O bordo do mundo non pode exceder de %s bloques", "commands.worldborder.set.failed.far": "O bordo do mundo non pode exceder os %s bloques", "commands.worldborder.set.failed.nochange": "Nada cambio<PERSON>, o borde do mundo xa tiña este tamaño", "commands.worldborder.set.failed.small": "O bordo do mundo non pode ter unha extensión inferior a 1 bloque", "commands.worldborder.set.grow": "Aumentando o bordo do mundo en %s bloques durante %s segundos", "commands.worldborder.set.immediate": "Estableceuse o bordo do mundo en %s bloque(s) de ancho", "commands.worldborder.set.shrink": "Reducindo o bordo do mundo en %s bloques durante %s segundo(s)", "commands.worldborder.warning.distance.failed": "Nada cambio<PERSON>, o bordo do mundo xa tiña esa distancia de aviso", "commands.worldborder.warning.distance.success": "Estableceuse a distancia de aviso do bordo do mundo a %s segundo(s)", "commands.worldborder.warning.time.failed": "Nada cambio<PERSON>, o bordo do mundo xa tiña esa cantidade de tempo de aviso", "commands.worldborder.warning.time.success": "Estableceuse o dano do bordo do mundo a %s segundo(s)", "compliance.playtime.greaterThan24Hours": "Xogaches durante máis de 24 horas", "compliance.playtime.hours": "Levas xogando %s hora(s)", "compliance.playtime.message": "Xogar sen descanso podería interferir coa túa vida diaria", "connect.aborted": "Conexión cancelada", "connect.authorizing": "Iniciando se<PERSON>...", "connect.connecting": "Conectando co servidor...", "connect.encrypting": "Encriptando...", "connect.failed": "Erro ó conectar co servidor", "connect.failed.transfer": "Fallou a conexión ao transferir ao servidor", "connect.joining": "Entrando ao mundo...", "connect.negotiating": "Conectando...", "connect.reconfiging": "Reconfigurando...", "connect.reconfiguring": "Reconfigurando...", "connect.transferring": "Transportado a un novo servidor...", "container.barrel": "Bocoi", "container.beacon": "Faro", "container.beehive.bees": "Abellas: %s / %s", "container.beehive.honey": "Mel: %s / %s", "container.blast_furnace": "<PERSON>", "container.brewing": "Fermentador", "container.cartography_table": "Mesa de cartografía", "container.chest": "Cofre", "container.chestDouble": "Cofre grande", "container.crafter": "Fabricador", "container.crafting": "Fabricación", "container.creative": "Selección de obxectos", "container.dispenser": "Dispensador", "container.dropper": "Lanzador", "container.enchant": "Encantar", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lapislázuli", "container.enchant.lapis.one": "1 lapislázuli", "container.enchant.level.many": "%s niveis de encantamento", "container.enchant.level.one": "1 nivel de encantamento", "container.enchant.level.requirement": "Nivel requirido: %s", "container.enderchest": "<PERSON><PERSON><PERSON> <PERSON>", "container.furnace": "Forno", "container.grindstone_title": "Arranxar e desencantar", "container.hopper": "Funil", "container.inventory": "Inventario", "container.isLocked": "%s está bloqueado!", "container.lectern": "Atril", "container.loom": "Tear", "container.repair": "Arranxar e renomear", "container.repair.cost": "Custo do encantamento: %1$s", "container.repair.expensive": "Demasiado caro!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "e %s máis...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Non se pode abrir. O contido aínda non foi xerado.", "container.stonecutter": "Cortapedras", "container.upgrade": "Mellorar equipo", "container.upgrade.error_tooltip": "Este obxecto no se pode mellorar desa forma", "container.upgrade.missing_template_tooltip": "Engade molde de ferraría", "controls.keybinds": "Atallos de teclas...", "controls.keybinds.duplicateKeybinds": "Esta chave úsase para:\n%s", "controls.keybinds.title": "Atallos de teclas", "controls.reset": "Reiniciar", "controls.resetAll": "Reiniciar teclas", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Escoller un bioma", "createWorld.customize.buffet.title": "<PERSON><PERSON><PERSON><PERSON> mundo bufete", "createWorld.customize.flat.height": "Altura", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Parte inferior - %s", "createWorld.customize.flat.layer.top": "Parte superior - %s", "createWorld.customize.flat.removeLayer": "Eliminar capa", "createWorld.customize.flat.tile": "Material da capa", "createWorld.customize.flat.title": "Personalizar mundo superchán", "createWorld.customize.presets": "Modelos", "createWorld.customize.presets.list": "Como alternativa, estes fixémolos nós antes!", "createWorld.customize.presets.select": "Em<PERSON><PERSON>gar modelo", "createWorld.customize.presets.share": "Queres compartir o teu modelo con alguén? Usa o cadro de embaixo!", "createWorld.customize.presets.title": "Seleccione un modelo", "createWorld.preparing": "Preparando a creación do mundo...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Mundo", "credits_and_attribution.button.attribution": "Atribucións", "credits_and_attribution.button.credits": "C<PERSON>dit<PERSON>", "credits_and_attribution.button.licenses": "Licenzas", "credits_and_attribution.screen.title": "Créditos e atribucións", "dataPack.bundle.description": "Activa o saco como obxecto experimental", "dataPack.bundle.name": "Sacos", "dataPack.locator_bar.description": "Amosar a dirección doutros xogadores no multixogador", "dataPack.locator_bar.name": "Barra localizadora", "dataPack.minecart_improvements.description": "Mellora do movemento das vagonetas", "dataPack.minecart_improvements.name": "Melloras das vagonetas", "dataPack.redstone_experiments.description": "Cambios experimentais de redstone", "dataPack.redstone_experiments.name": "Experimentos de redstone", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> de <PERSON>", "dataPack.trade_rebalance.description": "Trocos actualizados para os aldeáns", "dataPack.trade_rebalance.name": "Axustes dos trocos dos aldeáns", "dataPack.update_1_20.description": "Novas funcións e contido para Minecraft 1.20", "dataPack.update_1_20.name": "Actualización 1.20", "dataPack.update_1_21.description": "Novas funcións e contido para Minecraft 1.21", "dataPack.update_1_21.name": "Actualización 1.21", "dataPack.validation.back": "Volver", "dataPack.validation.failed": "Erro ao validar os paquetes de datos!", "dataPack.validation.reset": "Volver ós valores por defecto", "dataPack.validation.working": "Validando os paquetes de datos seleccionados...", "dataPack.vanilla.description": "Datos predefinidos do Minecraft", "dataPack.vanilla.name": "Por defecto", "dataPack.winter_drop.description": "Novas funcións e contido para a actualización de inverno", "dataPack.winter_drop.name": "Actualización de inverno", "datapackFailure.safeMode": "<PERSON><PERSON> se<PERSON>", "datapackFailure.safeMode.failed.description": "Este mundo contén datos de gardado non válidos ou corruptos.", "datapackFailure.safeMode.failed.title": "Non se pode cargar o mundo no <PERSON>do Seguro.", "datapackFailure.title": "Detectáronse erros nos paquetes de datos seleccionados que impiden que o mundo cargue.\nPodes ou ben cargar o mundo co paquete predeterminado (\"modo seguro\") ou ben regresar á pantalla de título e arranxalo manualmente.", "death.attack.anvil": "%1$s foi esmagado por unha engra", "death.attack.anvil.player": "%1$s foi esmagado por unha engra mentres loitaba con %2$s", "death.attack.arrow": "%1$s foi disparado por %2$s", "death.attack.arrow.item": "%1$s foi disparado por %2$s empregando %3$s", "death.attack.badRespawnPoint.link": "Deseño intencionado do xogo", "death.attack.badRespawnPoint.message": "%1$s morreu por %2$s", "death.attack.cactus": "%1$s foi espiñado até a morte", "death.attack.cactus.player": "%1$s espiñouse cun cacto mentres tentaba fuxir de %2$s", "death.attack.cramming": "%1$s foi esmagado moi forte", "death.attack.cramming.player": "%1$s foi esmagado por %2$s", "death.attack.dragonBreath": "%1$s foi torrado polo bafo do dragón", "death.attack.dragonBreath.player": "%1$s foi torrado polo bafo do dragón de %2$s", "death.attack.drown": "%1$s afogou", "death.attack.drown.player": "%1$s afogou mentres tentaba fuxir de %2$s", "death.attack.dryout": "%1$s morreu por deshidratación", "death.attack.dryout.player": "%1$s morreu por deshidratación mentres tentaba fuxir de %2$s", "death.attack.even_more_magic": "%1$s morreu por moito máis que arte de maxia", "death.attack.explosion": "%1$s estoupou", "death.attack.explosion.player": "%1$s foi estoupado por %2$s", "death.attack.explosion.player.item": "%1$s foi estoupado por %2$s empregando %3$s", "death.attack.fall": "%1$s bateu contra o chan moi forte", "death.attack.fall.player": "%1$s bateu contra o chan moi forte fuxindo de %2$s", "death.attack.fallingBlock": "%1$s foi esmagado por un bloque", "death.attack.fallingBlock.player": "%1$s foi esmagado por un bloque mentres loitaba con %2$s", "death.attack.fallingStalactite": "%1$s foi chantado por unha estalactita", "death.attack.fallingStalactite.player": "%1$s foi chantado por unha estalactita mentres loitaba con %2$s", "death.attack.fireball": "%1$s foi atinxido por unha bóla de lume de %2$s", "death.attack.fireball.item": "%1$s foi atinxido por unha bóla de lume de %2$s empregando %3$s", "death.attack.fireworks": "%1$s estoupou cun foguete no cu", "death.attack.fireworks.item": "%1$s estoupou cun foguete no cu disparado dende %3$s por %2$s", "death.attack.fireworks.player": "%1$s estoupou cun foguete no cu mentres loitaba con %2$s", "death.attack.flyIntoWall": "%1$s experimentou a enerxía cinética", "death.attack.flyIntoWall.player": "%1$s experimentou a enerxía cinética mentres tentaba fuxir de %2$s", "death.attack.freeze": "%1$s conxelouse até a morte", "death.attack.freeze.player": "%1$s foi conxelado até a morte por %2$s", "death.attack.generic": "%1$s morreu", "death.attack.generic.player": "%1$s morreu por mor de %2$s", "death.attack.genericKill": "%1$s morreu", "death.attack.genericKill.player": "%1$s morreu mentres loitaba con %2$s", "death.attack.hotFloor": "%1$s descubriu que o chan era lava", "death.attack.hotFloor.player": "%1$s entrou nunha zona perigosa por mor de %2$s", "death.attack.inFire": "%1$s ardeu en chamas", "death.attack.inFire.player": "%1$s camiñou no lume mentres loitaba con %2$s", "death.attack.inWall": "%1$s sufocouse nunha parede", "death.attack.inWall.player": "%1$s sufocouse nunha parede mentres loitaba con %2$s", "death.attack.indirectMagic": "%1$s morreu por %2$s empregando maxia", "death.attack.indirectMagic.item": "%1$s morreu por %2$s empregando %3$s", "death.attack.lava": "%1$s tentou nadar na lava", "death.attack.lava.player": "%1$s tentou nadar na lava fuxindo de %2$s", "death.attack.lightningBolt": "%1$s foi atinxido por un raio", "death.attack.lightningBolt.player": "%1$s foi atinxido por un raio mentres loitaba con %2$s", "death.attack.mace_smash": "%1$s foi esmagado por %2$s", "death.attack.mace_smash.item": "%1$s foi esmagado por %2$s con %3$s", "death.attack.magic": "%1$s morreu por mor da maxia", "death.attack.magic.player": "%1$s morreu por mor da maxia mentres tentaba fuxir de %2$s", "death.attack.message_too_long": "Lamentablemente, esta mensaxe era demasiado longa coma para ser enviada. Sentímolo! Aquí tes a versión abreviada: %s", "death.attack.mob": "%1$s foi asasinado por %2$s", "death.attack.mob.item": "%1$s foi asasinado por %2$s empregando %3$s", "death.attack.onFire": "%1$s queimouse até a morte", "death.attack.onFire.item": "%1$s ardeu mentres loitaba con %2$s equipando %3$s", "death.attack.onFire.player": "%1$s ardeu mentres loitaba con %2$s", "death.attack.outOfWorld": "%1$s caeu do mundo", "death.attack.outOfWorld.player": "%1$s non quere vivir no mesmo mundo que %2$s", "death.attack.outsideBorder": "%1$s saíu dos confíns deste mundo", "death.attack.outsideBorder.player": "%1$s saíu dos confíns deste mundo mentres loitaba contra %2$s", "death.attack.player": "%1$s foi asasinado por %2$s", "death.attack.player.item": "%1$s foi asasinado por %2$s empregando %3$s", "death.attack.sonic_boom": "%1$s foi varrido por un berro sónico", "death.attack.sonic_boom.item": "%1$s foi varrido por un berro sónico mentres tentaba fuxir de %2$s equipando %3$s", "death.attack.sonic_boom.player": "%1$s foi varrido por un berro sónico mentres tentaba fuxir de %2$s", "death.attack.stalagmite": "%1$s foi empalado nunha estalagmita", "death.attack.stalagmite.player": "%1$s foi empalado nunha estalagmita mentres loitaba con %2$s", "death.attack.starve": "%1$s morreu esfameado", "death.attack.starve.player": "%1$s morreu esfameado mentres loitaba con %2$s", "death.attack.sting": "%1$s morreu pola picadela dunha abella", "death.attack.sting.item": "%1$s foi picado até a morte por %2$s empregando %3$s", "death.attack.sting.player": "%1$s morreu pola picadela de %2$s", "death.attack.sweetBerryBush": "%1$s foi espiñado até a morte por un arbusto de bagas doces", "death.attack.sweetBerryBush.player": "%1$s espiñouse até a morte por un arbusto de bagas doces mentres tentaba fuxir de %2$s", "death.attack.thorns": "%1$s morreu mentres tentaba ferir a %2$s", "death.attack.thorns.item": "%1$s morreu por %3$s mentres tentaba ferir a %2$s", "death.attack.thrown": "%1$s foi esmagado por %2$s", "death.attack.thrown.item": "%1$s foi esmagado por %2$s empregando %3$s", "death.attack.trident": "%1$s foi empalado por %2$s", "death.attack.trident.item": "%1$s foi empalado por %2$s con %3$s", "death.attack.wither": "%1$s sofreu a ira do <PERSON>er", "death.attack.wither.player": "%1$s sofreu a ira do Wither mentres loitaba con %2$s", "death.attack.witherSkull": "%1$s foi atinxido por unha caveira de %2$s", "death.attack.witherSkull.item": "%1$s foi atinxido por unha caveira de %2$s empregando %3$s", "death.fell.accident.generic": "%1$s caeu dun lugar alto", "death.fell.accident.ladder": "%1$s caeu dunha escada", "death.fell.accident.other_climbable": "%1$s caeu mentres escalaba", "death.fell.accident.scaffolding": "%1$s caeu dunha estada", "death.fell.accident.twisting_vines": "%1$s caeu dunhas trepadeiras retortas", "death.fell.accident.vines": "%1$s caeu dunhas trepadeiras", "death.fell.accident.weeping_vines": "%1$s caeu dunhas trepadeiras choronas", "death.fell.assist": "%1$s foi empurrado dende moi alto por %2$s", "death.fell.assist.item": "%1$s foi empurrado dende moi alto por %2$s empregando %3$s", "death.fell.finish": "%1$s caeu de moi alto e foi rematado por %2$s", "death.fell.finish.item": "%1$s caeu de moi alto e foi rematado por %2$s empregando %3$s", "death.fell.killer": "%1$s foi empurrado dende moi alto", "deathScreen.quit.confirm": "Estás seguro de querer saír?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuación", "deathScreen.score.value": "Puntuación: %s", "deathScreen.spectate": "Observar mundo", "deathScreen.title": "Morriches!", "deathScreen.title.hardcore": "Fin da partida!", "deathScreen.titleScreen": "<PERSON><PERSON> principal", "debug.advanced_tooltips.help": "F3 + H = Descripcións avanzadas", "debug.advanced_tooltips.off": "Descripcións avanzadas: non", "debug.advanced_tooltips.on": "Descripcións avanzadas: si", "debug.chunk_boundaries.help": "F3 + G = <PERSON>ar bordos dos chunks", "debug.chunk_boundaries.off": "Bordos do chunk: agochar", "debug.chunk_boundaries.on": "Bordos de chunks: ver", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON> chat", "debug.copy_location.help": "F3 + C = Copiar a ubicación como comando /tp. Mantén premido F3 + C para forzar o peche do xogo", "debug.copy_location.message": "Ubicación copiada no portapapeis", "debug.crash.message": "Estás premendo F3 + C. Se non paras, o xogo deixará de funcionar.", "debug.crash.warning": "Pechando xogo en %s...", "debug.creative_spectator.error": "Non tes permiso para mudar o modo de xogo", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON> entre o modo de xogo anterior <-> espectador", "debug.dump_dynamic_textures": "Texturas dinámicas gardadas en %s", "debug.dump_dynamic_textures.help": "F3 + S = Volcar texturas dinámicas", "debug.gamemodes.error": "Non se puido abrir o menú de modos de xogo, non tes permisos", "debug.gamemodes.help": "F3 + F4= <PERSON><PERSON>r menú dos modos de xogo", "debug.gamemodes.press_f4": "[ F4]", "debug.gamemodes.select_next": "%s <PERSON><PERSON><PERSON>", "debug.help.help": "F3 + Q = <PERSON>ar esta listaxe", "debug.help.message": "Atallos de teclado:", "debug.inspect.client.block": "Os datos do bloque do cliente foron copiados", "debug.inspect.client.entity": "Os datos da entidade do cliente foron copiados", "debug.inspect.help": "F3 + I = <PERSON><PERSON><PERSON> datos de entidade ou bloque ao portapapeis", "debug.inspect.server.block": "Os datos do bloque do servidor foron copiados", "debug.inspect.server.entity": "Os datos da entidade do servidor foron copiados", "debug.pause.help": "F3 + Esc = Pausa sen menú (se é posible)", "debug.pause_focus.help": "F3 + P = Pausar xogo ao cambiar de xanela", "debug.pause_focus.off": "Pausar ao minimizar x<PERSON>: non", "debug.pause_focus.on": "Pausar ao minimizar xanela: sí", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Iniciar/deter análise", "debug.profiling.start": "Comezada análise durante %s segundos. Usa F₃ + L para detelo antes", "debug.profiling.stop": "An<PERSON><PERSON>e remata<PERSON>. Resultados gardados en %s", "debug.reload_chunks.help": "F3 + A = Recargar chunks", "debug.reload_chunks.message": "Recargando todos os chunks", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON><PERSON> paquetes de recursos", "debug.reload_resourcepacks.message": "Paquetes de recursos recargados", "debug.show_hitboxes.help": "F3 + B = Amosar caixas de colisións", "debug.show_hitboxes.off": "Caixas de colisións: non", "debug.show_hitboxes.on": "Caixas de colisións: si", "debug.version.header": "Información da versión do cliente:", "debug.version.help": "F3 + V = Información da versión do cliente", "demo.day.1": "Esta proba durará cinco días de xogo. Faino o mellor que poidas!", "demo.day.2": "<PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.4": "Cuarto día", "demo.day.5": "Este é o teu derradeiro día!", "demo.day.6": "Re<PERSON><PERSON> o teu quinto día, preme %s para gardar unha captura de pantalla da túa creación.", "demo.day.warning": "O teu tempo case rematou!", "demo.demoExpired": "O tempo de demostración agotouse!", "demo.help.buy": "Mercar agora!", "demo.help.fullWrapped": "Esta proba rematará de aquí a 5 días de xogo (sobre 1 hora 40 minutos reais). Comproba as proezas para obter pistas! Divírtete!", "demo.help.inventory": "Preme %1$s para abrir o inventario", "demo.help.jump": "Preme %1$s para brincar", "demo.help.later": "Continuar xogando!", "demo.help.movement": "Emprega %1$s, %2$s, %3$s, %4$s e o rato para moverte", "demo.help.movementMouse": "Mira ós teus arredores co rato", "demo.help.movementShort": "Móvete premendo %1$s, %2$s, %3$s e %4$s", "demo.help.title": "Demostración de Minecraft", "demo.remainingTime": "Tempo restante: %s", "demo.reminder": "O tempo da proba rematou, merca o xogo para continuar ou comeza un mundo novo!", "difficulty.lock.question": "Seguro que queres bloquear a dificultade deste mundo? Isto establecerao sempre en %1$s e non poderás volver a cambialo.", "difficulty.lock.title": "Bloquear dificultade do mundo", "disconnect.endOfStream": "Fin da conexión", "disconnect.exceeded_packet_rate": "Botado por exceder a taxa límite de paquetes", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorando solicitude de estado", "disconnect.loginFailedInfo": "Erro ao iniciar sesión: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "O multixogador está desactivado. Comproba os axustes da túa conta Microsoft.", "disconnect.loginFailedInfo.invalidSession": "Sesión inválida (tenta reinciar o xogo e o lanzador)", "disconnect.loginFailedInfo.serversUnavailable": "Os servidores de autenticación non están dispoñíbeis. Téntao novamente máis tarde.", "disconnect.loginFailedInfo.userBanned": "Estás bloqueado do modo en liña", "disconnect.lost": "Conexión perdida", "disconnect.packetError": "Erro no protocolo de rede", "disconnect.spam": "Botado/a por facer spam", "disconnect.timeout": "Esgotouse o tempo de espera", "disconnect.transfer": "Transferido para outro servidor", "disconnect.unknownHost": "Anfitrión descoñecido", "download.pack.failed": "%s de %s fallaron ao descargarse", "download.pack.progress.bytes": "Progreso: %s (tamaño total descoñecido)", "download.pack.progress.percent": "Progreso: %s%%", "download.pack.title": "Descargando o paquete de recursos %s/%s", "editGamerule.default": "Predefinido: %s", "editGamerule.title": "Editar regras do xogo", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorción", "effect.minecraft.bad_omen": "Mal presaxio", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Canalización", "effect.minecraft.darkness": "Escuridade", "effect.minecraft.dolphins_grace": "Graza de golfiño", "effect.minecraft.fire_resistance": "Resistencia ao lume", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Présa", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON>", "effect.minecraft.hunger": "Fame", "effect.minecraft.infested": "Infestación", "effect.minecraft.instant_damage": "<PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON>", "effect.minecraft.invisibility": "Invisibilidade", "effect.minecraft.jump_boost": "Super brinco", "effect.minecraft.levitation": "Levitación", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Fatiga de picar", "effect.minecraft.nausea": "<PERSON><PERSON>", "effect.minecraft.night_vision": "Visión nocturna", "effect.minecraft.oozing": "Pegañento", "effect.minecraft.poison": "Veleno", "effect.minecraft.raid_omen": "Presaxio de invasión", "effect.minecraft.regeneration": "Rexeneración", "effect.minecraft.resistance": "Resistencia", "effect.minecraft.saturation": "Saturación", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitude", "effect.minecraft.speed": "Velocidade", "effect.minecraft.strength": "Forza", "effect.minecraft.trial_omen": "Presaxio de desafío", "effect.minecraft.unluck": "Mala sorte", "effect.minecraft.water_breathing": "Respiración acuática", "effect.minecraft.weakness": "Frouxidade", "effect.minecraft.weaving": "Tecedura", "effect.minecraft.wind_charged": "Carga de vento", "effect.minecraft.wither": "<PERSON>er", "effect.none": "Sen efectos", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinidade acuática", "enchantment.minecraft.bane_of_arthropods": "Maldición dos artrópodos", "enchantment.minecraft.binding_curse": "Maldición de ligazón", "enchantment.minecraft.blast_protection": "Protección contra explosións", "enchantment.minecraft.breach": "Fen<PERSON>", "enchantment.minecraft.channeling": "Condutividade", "enchantment.minecraft.density": "Densidade", "enchantment.minecraft.depth_strider": "Axilidade acuática", "enchantment.minecraft.efficiency": "Eficiencia", "enchantment.minecraft.feather_falling": "Caída feble", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON> de lume", "enchantment.minecraft.fire_protection": "Protección contra o lume", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Pés de xeo", "enchantment.minecraft.impaling": "Empalamento", "enchantment.minecraft.infinity": "Infinidade", "enchantment.minecraft.knockback": "Empurrón", "enchantment.minecraft.looting": "Pillaxe", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Sorte do mar", "enchantment.minecraft.lure": "Isco", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "Perforación", "enchantment.minecraft.power": "<PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Protección contra proxectís", "enchantment.minecraft.protection": "Protección", "enchantment.minecraft.punch": "Impacto", "enchantment.minecraft.quick_charge": "Carga rápida", "enchantment.minecraft.respiration": "Respiración", "enchantment.minecraft.riptide": "Corrente a<PERSON>", "enchantment.minecraft.sharpness": "Fío", "enchantment.minecraft.silk_touch": "Toque de seda", "enchantment.minecraft.smite": "Golpeo", "enchantment.minecraft.soul_speed": "Velocidade de ánimas", "enchantment.minecraft.sweeping": "Alcance", "enchantment.minecraft.sweeping_edge": "Alcance", "enchantment.minecraft.swift_sneak": "Velocidade agochado", "enchantment.minecraft.thorns": "Espiñas", "enchantment.minecraft.unbreaking": "Irrompibilidade", "enchantment.minecraft.vanishing_curse": "Maldición de desaparición", "enchantment.minecraft.wind_burst": "Impul<PERSON> de vento", "entity.minecraft.acacia_boat": "Barca de acacia", "entity.minecraft.acacia_chest_boat": "Barca de acacia con cofre", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nube de efecto persistente", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Soporte de armadura", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "Axolote", "entity.minecraft.bamboo_chest_raft": "Balsa de bambú con cofre", "entity.minecraft.bamboo_raft": "Balsa de bambú", "entity.minecraft.bat": "Mo<PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "Barca de bidueiro", "entity.minecraft.birch_chest_boat": "Barca de bidueiro con cofre", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "<PERSON><PERSON><PERSON><PERSON> de blo<PERSON>", "entity.minecraft.boat": "Barca", "entity.minecraft.bogged": "En<PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Carga de vento", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "Gato", "entity.minecraft.cave_spider": "Araña de cova", "entity.minecraft.cherry_boat": "Barca de cerdeira", "entity.minecraft.cherry_chest_boat": "Barca de cerdeira con cofre", "entity.minecraft.chest_boat": "Barca con cofre", "entity.minecraft.chest_minecart": "Vagoneta con cofre", "entity.minecraft.chicken": "Gali<PERSON>", "entity.minecraft.cod": "Bacallau", "entity.minecraft.command_block_minecart": "Vagoneta con bloque de comandos", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "Crepitante", "entity.minecraft.creaking_transient": "Crepitante", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Barca de carballo escuro", "entity.minecraft.dark_oak_chest_boat": "Barca de carballo escuro con cofre", "entity.minecraft.dolphin": "Golfiño", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bóla de lume de dragón", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON>", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "Cristal do <PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "Perla do Ender <PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Invocador", "entity.minecraft.evoker_fangs": "Cairos de invocador", "entity.minecraft.experience_bottle": "Frasco de experiencia lanzado", "entity.minecraft.experience_orb": "Orbe de experiencia", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>", "entity.minecraft.falling_block": "Bloque en caída", "entity.minecraft.falling_block_type": "%s caendo", "entity.minecraft.fireball": "Bóla de lume", "entity.minecraft.firework_rocket": "Foguete", "entity.minecraft.fishing_bobber": "Anzol", "entity.minecraft.fox": "<PERSON><PERSON>", "entity.minecraft.frog": "Ra", "entity.minecraft.furnace_minecart": "Vagoneta con forno", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Xigante", "entity.minecraft.glow_item_frame": "<PERSON> l<PERSON>", "entity.minecraft.glow_squid": "<PERSON>ra brillante", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Gardi<PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>o", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vagoneta con funil", "entity.minecraft.horse": "C<PERSON>lo", "entity.minecraft.husk": "<PERSON><PERSON>", "entity.minecraft.illusioner": "Ilusionista", "entity.minecraft.interaction": "Interacción", "entity.minecraft.iron_golem": "Gólem de ferro", "entity.minecraft.item": "Obxecto", "entity.minecraft.item_display": "Exhibidor de obxecto", "entity.minecraft.item_frame": "<PERSON>", "entity.minecraft.jungle_boat": "Barca de selva", "entity.minecraft.jungle_chest_boat": "Barca de selva con cofre", "entity.minecraft.killer_bunny": "O coello asasino", "entity.minecraft.leash_knot": "<PERSON><PERSON>", "entity.minecraft.lightning_bolt": "Raio", "entity.minecraft.lingering_potion": "Poción prolongada", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "Cubo de magma", "entity.minecraft.mangrove_boat": "Barca de mangleiro", "entity.minecraft.mangrove_chest_boat": "Barca de mangleiro con cofre", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "Vagoneta", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Barca de carballo", "entity.minecraft.oak_chest_boat": "Barca de carballo con cofre", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Invocador de obxectos sombríos", "entity.minecraft.painting": "<PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Barca de carballo esbrancuxado", "entity.minecraft.pale_oak_chest_boat": "Barca de carballo esbrancuxado con cofre", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> bruto", "entity.minecraft.pillager": "Saqueador", "entity.minecraft.player": "Xogador", "entity.minecraft.polar_bear": "Oso polar", "entity.minecraft.potion": "Poción", "entity.minecraft.pufferfish": "Peixe globo", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "Devastador", "entity.minecraft.salmon": "Salmón", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Esqueleto", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Bóla de lume pequena", "entity.minecraft.sniffer": "Cheirador", "entity.minecraft.snow_golem": "Gólem de neve", "entity.minecraft.snowball": "Bóla de neve", "entity.minecraft.spawner_minecart": "Vagoneta con invocador de monstros", "entity.minecraft.spectral_arrow": "<PERSON><PERSON>a espectral", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Poción guindable", "entity.minecraft.spruce_boat": "Barca de abeto", "entity.minecraft.spruce_chest_boat": "Barca de abeto con cofre", "entity.minecraft.squid": "<PERSON><PERSON>", "entity.minecraft.stray": "Esqueleto glacial", "entity.minecraft.strider": "Vagante", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Exhibidor de texto", "entity.minecraft.tnt": "<PERSON><PERSON><PERSON> activada", "entity.minecraft.tnt_minecart": "Vagoneta con dinamita", "entity.minecraft.trader_llama": "Llama ambulante", "entity.minecraft.trident": "Tridente", "entity.minecraft.tropical_fish": "Peixe tropical", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Peixe cirurxián negro", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON> mouro", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON>xe bolboreta adornado", "entity.minecraft.tropical_fish.predefined.12": "Peixe p<PERSON>o", "entity.minecraft.tropical_fish.predefined.13": "Peixe anxo raíña", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON> serpe vermello", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON> vermello", "entity.minecraft.tropical_fish.predefined.17": "Barbudo", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON> pall<PERSON>o tomate", "entity.minecraft.tropical_fish.predefined.19": "Peixe porco", "entity.minecraft.tropical_fish.predefined.2": "Peixe cirurxián azul", "entity.minecraft.tropical_fish.predefined.20": "Peixe papagaio de cola amarela", "entity.minecraft.tropical_fish.predefined.21": "Peixe cirurxián amarelo", "entity.minecraft.tropical_fish.predefined.3": "Peixe bolboreta", "entity.minecraft.tropical_fish.predefined.4": "Cíclido", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON> pall<PERSON>o", "entity.minecraft.tropical_fish.predefined.6": "Peixe loitador siam<PERSON>", "entity.minecraft.tropical_fish.predefined.7": "Peixe avoa real", "entity.minecraft.tropical_fish.predefined.8": "Pargo emperador vermello", "entity.minecraft.tropical_fish.predefined.9": "Barbo", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON> betta", "entity.minecraft.tropical_fish.type.blockfish": "Peixe bloque", "entity.minecraft.tropical_fish.type.brinely": "De auga salgada", "entity.minecraft.tropical_fish.type.clayfish": "Peixe arxila", "entity.minecraft.tropical_fish.type.dasher": "A raias", "entity.minecraft.tropical_fish.type.flopper": "Saltarín", "entity.minecraft.tropical_fish.type.glitter": "Relucente", "entity.minecraft.tropical_fish.type.kob": "Riscado", "entity.minecraft.tropical_fish.type.snooper": "Peixe do recife", "entity.minecraft.tropical_fish.type.spotty": "Peixe mallado", "entity.minecraft.tropical_fish.type.stripey": "P<PERSON>xe listado", "entity.minecraft.tropical_fish.type.sunstreak": "Peixe raio de sol", "entity.minecraft.turtle": "Tartaruga", "entity.minecraft.vex": "Espírito", "entity.minecraft.villager": "Aldeán", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "Buxeo", "entity.minecraft.villager.cartographer": "Cartógrafo", "entity.minecraft.villager.cleric": "Crego", "entity.minecraft.villager.farmer": "Agricultor", "entity.minecraft.villager.fisherman": "Pescantín", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>iro", "entity.minecraft.villager.librarian": "Bibliotecario", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Pregu<PERSON>iro", "entity.minecraft.villager.none": "Aldeán", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON>", "entity.minecraft.vindicator": "Vindicador", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON> ambulante", "entity.minecraft.warden": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Carga de vento", "entity.minecraft.witch": "Meiga", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Esqueleto do Wither", "entity.minecraft.wither_skull": "Caveira do Wither", "entity.minecraft.wolf": "Lobo", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>i", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> zomb<PERSON>ado", "entity.not_summonable": "Non se puido invocar a entidade do tipo %s", "event.minecraft.raid": "Invasión", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Invasión - Derrota", "event.minecraft.raid.raiders_remaining": "Invasores restantes: %s", "event.minecraft.raid.victory": "Vitoria", "event.minecraft.raid.victory.full": "Invasión - Vitoria", "filled_map.buried_treasure": "Mapa do tesouro enterrado", "filled_map.explorer_jungle": "Mapa de exploración da xungla", "filled_map.explorer_swamp": "Mapa de exploración do pantano", "filled_map.id": "ID #%s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Bloqueado", "filled_map.mansion": "Mapa de exploración de bosques", "filled_map.monument": "Mapa de exploración de océanos", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Mapa de exploración de desafío", "filled_map.unknown": "Mapa descoñecido", "filled_map.village_desert": "Mapa da aldea do deserto", "filled_map.village_plains": "Mapa da aldea da chaira", "filled_map.village_savanna": "Mapa da aldea da sabana", "filled_map.village_snowy": "Mapa da aldea da chaira nevada", "filled_map.village_taiga": "Mapa da aldea da taiga", "flat_world_preset.minecraft.bottomless_pit": "<PERSON>zo sen fondo", "flat_world_preset.minecraft.classic_flat": "Extraplano clásico", "flat_world_preset.minecraft.desert": "Deserto", "flat_world_preset.minecraft.overworld": "Superficie", "flat_world_preset.minecraft.redstone_ready": "Ideal para redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON> ne<PERSON>do", "flat_world_preset.minecraft.the_void": "A nada", "flat_world_preset.minecraft.tunnelers_dream": "Soño mineiro", "flat_world_preset.minecraft.water_world": "Mundo acuático", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON>do avent<PERSON>", "gameMode.changed": "O teu modo de xogo foi actualizado a %s", "gameMode.creative": "Modo creativo", "gameMode.hardcore": "Modo extremo!", "gameMode.spectator": "Modo espectador", "gameMode.survival": "Modo supervivencia", "gamerule.allowFireTicksAwayFromPlayer": "Propagar lume lonxe de xogadores", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla a propagación de lume e lava ao longo de 8 chunks de distancia de calquera xogador", "gamerule.announceAdvancements": "<PERSON><PERSON> prog<PERSON>", "gamerule.blockExplosionDropDecay": "En explosións por interacción con bloques, algúns bloques non soltarán obxectos", "gamerule.blockExplosionDropDecay.description": "Algúns dos obxectos soltados de explosións de bloques causados pola interacción con bloques pérdense na explosión.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Obtención de obxectos", "gamerule.category.misc": "Diversos", "gamerule.category.mobs": "Criaturas", "gamerule.category.player": "Xogador", "gamerule.category.spawning": "Invocación", "gamerule.category.updates": "Actualizacións do mundo", "gamerule.commandBlockOutput": "Notificar uso de bloques de comandos", "gamerule.commandModificationBlockLimit": "Límite de bloques modificables por comandos", "gamerule.commandModificationBlockLimit.description": "Número de bloques que se poden cambiar dunha vez cun comando, como «/fill» ou «/clone».", "gamerule.disableElytraMovementCheck": "Desactivar controis de movemento en élitros", "gamerule.disablePlayerMovementCheck": "Inhabilitar comprobación de movemento dos xogadores", "gamerule.disableRaids": "Desactivar invasións", "gamerule.doDaylightCycle": "Avanzar hora do día", "gamerule.doEntityDrops": "Soltar obxectos de entidades", "gamerule.doEntityDrops.description": "Permite obter obxectos das vagonetas (incluídos os inventarios), marcos de ítems, barcas, etc. ao rompelas", "gamerule.doFireTick": "Propagación do lume", "gamerule.doImmediateRespawn": "Reaparición inmediata", "gamerule.doInsomnia": "Invocar pantasmas", "gamerule.doLimitedCrafting": "Esixir receitas para fabricar", "gamerule.doLimitedCrafting.description": "Se se activa, só se poderán fabricar receitas aprendidas.", "gamerule.doMobLoot": "Criaturas soltan botín", "gamerule.doMobLoot.description": "Controla que as criaturas poidan soltar recursos, incluíndo orbes de experiencia.", "gamerule.doMobSpawning": "Invocar criaturas", "gamerule.doMobSpawning.description": "Algunhas entidades poden ter as s<PERSON>as propias regras.", "gamerule.doPatrolSpawning": "Invocar patrullas de saqueadores", "gamerule.doTileDrops": "Soltar bloques", "gamerule.doTileDrops.description": "Controla que os bloques podan soltar recursos, incluíndo orbes de experiencia.", "gamerule.doTraderSpawning": "Invocar vendedores ambulantes", "gamerule.doVinesSpread": "Propagación de trepadeiras", "gamerule.doVinesSpread.description": "Controla a propagación das trepadeiras para bloques adxacentes. Non afecta a outros tipos de trepadeiras como as trepadeiras choronas, as trepadeiras retortas, etc.", "gamerule.doWardenSpawning": "Invocar vixilantes", "gamerule.doWeatherCycle": "<PERSON><PERSON>lo <PERSON>", "gamerule.drowningDamage": "<PERSON>o por afogamento", "gamerule.enderPearlsVanishOnDeath": "As perlas do <PERSON><PERSON> la<PERSON>das desaparecen ao morrer", "gamerule.enderPearlsVanishOnDeath.description": "Indica se as perlas do <PERSON>er lanzadas por un xogador desaparecen cando ese xogador morre.", "gamerule.entitiesWithPassengersCanUsePortals": "As entidades con pasaxeiros poden utilizar portais", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permite que as entidades con pasaxeiros se teletransporten a través de portais do inframundo, portais do fin e pasarelas do fin.", "gamerule.fallDamage": "<PERSON><PERSON> por caída", "gamerule.fireDamage": "Dano por lume", "gamerule.forgiveDeadPlayers": "Per<PERSON>ar xogadores mortos", "gamerule.forgiveDeadPlayers.description": "As criaturas neutrais anoxadas deixarán de estalo cando o xogador perseguido morra preto.", "gamerule.freezeDamage": "Dano por conxelación", "gamerule.globalSoundEvents": "Eventos de son globais", "gamerule.globalSoundEvents.description": "Cando un certo evento ocorre, como a invocación dun xefe, o son escoitase en todos lados.", "gamerule.keepInventory": "Manter inventario após morir", "gamerule.lavaSourceConversion": "A lava convértese en fonte", "gamerule.lavaSourceConversion.description": "Cando unha corrente de lava está rodeada en dous lados por fontes de lava convértese en fonte.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Notificar comandos de administrador", "gamerule.maxCommandChainLength": "Límite de comandos encadenados", "gamerule.maxCommandChainLength.description": "Aplica a bloque de comandos en cadea e funcións.", "gamerule.maxCommandForkCount": "Limite de contextos do comando", "gamerule.maxCommandForkCount.description": "Número máximo de contextos que pódense usar en comandos como \"executar como\".", "gamerule.maxEntityCramming": "Límite de entidades nun bloque", "gamerule.minecartMaxSpeed": "Velocidade máxima das vagonetas", "gamerule.minecartMaxSpeed.description": "Velocidade máxima por defecto dunha vagoneta no chan.", "gamerule.mobExplosionDropDecay": "En explosións de criaturas, algúns bloques non soltarán obxectos", "gamerule.mobExplosionDropDecay.description": "Algúns dos obxectos soltados por bloques destruídos por explosións provocada por criaturas pérdense na explosión.", "gamerule.mobGriefing": "Permite que as criaturas destrúan", "gamerule.naturalRegeneration": "<PERSON><PERSON><PERSON>", "gamerule.playersNetherPortalCreativeDelay": "Atraso do portal ao inframundo do xogador no modo creativo", "gamerule.playersNetherPortalCreativeDelay.description": "Tempo (en ticks) que necesita un xogador en modo creativo para estar nun portal inframundo antes de cambiar de dimensión.", "gamerule.playersNetherPortalDefaultDelay": "Atraso do portal ao inframundo do xogador nun modo que non sexa creativo", "gamerule.playersNetherPortalDefaultDelay.description": "Tempo (en ticks) que necesita un xogador nun modo que non sexa creativo para estar nun portal inframundo antes de cambiar de dimensión.", "gamerule.playersSleepingPercentage": "Porcentaxe de xogadores durmidos", "gamerule.playersSleepingPercentage.description": "A porcentaxe de xogadores que deben durmir para pasar a noite.", "gamerule.projectilesCanBreakBlocks": "<PERSON><PERSON><PERSON><PERSON> poder romper bloques", "gamerule.projectilesCanBreakBlocks.description": "Controla se os proxectís de impacto destruirán bloques que sexan destrutibles por eles.", "gamerule.randomTickSpeed": "Velocidade de tics ao chou", "gamerule.reducedDebugInfo": "Reducir datos de depuración F3", "gamerule.reducedDebugInfo.description": "Limita o contido da pantalla debug.", "gamerule.sendCommandFeedback": "<PERSON>ar respostas dos comandos", "gamerule.showDeathMessages": "<PERSON><PERSON> mensaxes de morte", "gamerule.snowAccumulationHeight": "Altura de acumulación de neve", "gamerule.snowAccumulationHeight.description": "Cando neva, formaranse este número máximo de capas de neve no chan.", "gamerule.spawnChunkRadius": "Raio do punto de reaparición", "gamerule.spawnChunkRadius.description": "Cantidade de chunks que permanecen cargados arredor do punto de reaparición.", "gamerule.spawnRadius": "Raio do lugar de reaparición", "gamerule.spawnRadius.description": "Controla o tamaño da zona arredor do punto de reaparición onde poden aparecer os xogadores.", "gamerule.spectatorsGenerateChunks": "Xerar terreo no modo espectador", "gamerule.tntExplodes": "Permite activar e explotar dinamita", "gamerule.tntExplosionDropDecay": "En explosións por dinamita, algúns bloques non soltarán obxectos", "gamerule.tntExplosionDropDecay.description": "Algúns dos obxectos soltados por explosións causadas por dinamita pérdense na explosión.", "gamerule.universalAnger": "Ira universal", "gamerule.universalAnger.description": "As criaturas neutrais anoxadas atacan a calquera xogador que estea preto, non só ao xogador que as anoxara. Funciona mellor se \"Perdoar xogadores mortos\" está desactivado.", "gamerule.waterSourceConversion": "A auga convértese en fonte", "gamerule.waterSourceConversion.description": "Cando unha corrente de auga está rodeada en dous lados por fontes de auga convértese en fonte.", "generator.custom": "Personalizado", "generator.customized": "Personalizado (antigo)", "generator.minecraft.amplified": "AMPLIFICADO", "generator.minecraft.amplified.info": "Aviso: é só para entreterse! Precisas dun ordenador potente.", "generator.minecraft.debug_all_block_states": "Modo de depuración", "generator.minecraft.flat": "Superchán", "generator.minecraft.large_biomes": "Biomas grandes", "generator.minecraft.normal": "Por defecto", "generator.minecraft.single_biome_surface": "Bioma único", "generator.single_biome_caves": "<PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON>las flotantes", "gui.abuseReport.attestation": "Ao enviar este informe, confirmas que a información que proporcionaches é precisa e completa segundo o teu coñecemento.", "gui.abuseReport.comments": "Comentarios", "gui.abuseReport.describe": "Compartir detalles connosco axudaranos a facer unha decisión ben informada.", "gui.abuseReport.discard.content": "Se saes, perderás esta denuncia e os teus comentarios.\nTes a certeza que queres saír?", "gui.abuseReport.discard.discard": "Saír e desbotar denuncia", "gui.abuseReport.discard.draft": "Gardar como borrador", "gui.abuseReport.discard.return": "Continuar a editar", "gui.abuseReport.discard.title": "Desbotar denuncia e comentarios?", "gui.abuseReport.draft.content": "Queres continuar a editar a denuncia existente ou desbotala e crear unha nova?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Continuar a editar", "gui.abuseReport.draft.quittotitle.content": "Queres continuar editando ou desbotar?", "gui.abuseReport.draft.quittotitle.title": "Tes un borrador de denuncia no chat que se perderá se saes", "gui.abuseReport.draft.title": "Editar o borrador da denuncia do chat?", "gui.abuseReport.error.title": "Ocorreu un problema ao enviar a túa denuncia", "gui.abuseReport.message": "Onde atopaste o mal comportamento?\nIso axudarános na resolución do teu caso.", "gui.abuseReport.more_comments": "Por favor describe o ocorrido:", "gui.abuseReport.name.comment_box_label": "Por favor describe por que queres denunciar este nome:", "gui.abuseReport.name.reporting": "Estás a denunciar a \"%s\".", "gui.abuseReport.name.title": "Informar sobre o xogador %s", "gui.abuseReport.observed_what": "Por que estás reportando isto?", "gui.abuseReport.read_info": "Mostrar máis información sobre reportes", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON><PERSON><PERSON> ou alcol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Alguén está incitando ós demais a participar en actividades ilegais ou que teñen relación coas drogas ou o consumo de alcohol entre menores de idades.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Explotación sexual ou abuso a menores", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Alguén está a falar ou a promover sobre escenarios de incidentes involucrando nenos.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamación, suplantación ou falsa información", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Alguén está a danar a reputación de outra persoa, pretendendo ser alguén que non é el en realidade, ou compartindo información falsa, co obxectivo de chantaxear ou enganar ós demais.", "gui.abuseReport.reason.description": "Descrición:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON>cia falsa", "gui.abuseReport.reason.generic": "<PERSON><PERSON> denunciar al<PERSON>", "gui.abuseReport.reason.generic.description": "Amoloume ou fixo algo que non me gustou.", "gui.abuseReport.reason.harassment_or_bullying": "Intimidación ou acoso", "gui.abuseReport.reason.harassment_or_bullying.description": "Alguén está acosando, a<PERSON><PERSON><PERSON><PERSON>, atacando ou intimidando a outro xogador ou a ti. <PERSON>to inclúe cando alguén intenta de maneira repetida, contactar contigo ou mesmo con outra persoa sen consentimento. Tamén se inclúe cando alguén envía información persoal privada de vostede ou outra persoa (\"doxeando\").", "gui.abuseReport.reason.hate_speech": "Discurso de odio", "gui.abuseReport.reason.hate_speech.description": "Alguén está atacarte a ti ou a outro xogador por características da súa identidade, como relixión, etnia ou sexualidade.", "gui.abuseReport.reason.imminent_harm": "Ameazas a outros", "gui.abuseReport.reason.imminent_harm.description": "Alguén está a ameazarte a ti ou outra persoa na vida real.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imaxes íntimas sen consentimento", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON> esta a falar, a compartir ou a promover imaxes íntimas e privadas.", "gui.abuseReport.reason.self_harm_or_suicide": "Autolesión ou suicidio", "gui.abuseReport.reason.self_harm_or_suicide.description": "Alguén está a tentar de danarse a el mesmo na realidade, ou falando sobre danarte a ti mesmo na realidade.", "gui.abuseReport.reason.sexually_inappropriate": "Sexualmente inapropiado", "gui.abuseReport.reason.sexually_inappropriate.description": "Aparencias que retratan actos de natureza sexual, órganos sexuais e violencia sexual.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismo ou extremismo violento", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Alguén está falando, promovendo ou ameazando con actos de terrorismo ou extremismo violento por razóns políticas, relixiosas, ideolóxicas ou outras razóns.", "gui.abuseReport.reason.title": "Escoller categoría da denuncia", "gui.abuseReport.report_sent_msg": "Recibimos de maneira adecuada o seu reporte. Moitas grazas!\n\nO noso equipo revisaralo o antes posible.", "gui.abuseReport.select_reason": "Escoller categoría da denuncia", "gui.abuseReport.send": "Enviar denuncia", "gui.abuseReport.send.comment_too_long": "Por favor, acurta o comentario", "gui.abuseReport.send.error_message": "Ocorreu un erro enviando o teu reporte: '%s'", "gui.abuseReport.send.generic_error": "Encontrado un error inesperado mentres se enviaba a reportaxe.", "gui.abuseReport.send.http_error": "Un error inesperado de HTTP ocorreu mentres enviábase a súa reportaxe.", "gui.abuseReport.send.json_error": "Encontrouse unha carga útil inválida ao enviar o teu informe.", "gui.abuseReport.send.no_reason": "Por favor selecciona unha categoría para o reporte", "gui.abuseReport.send.not_attested": "Lea o texto anterior e marque a caixa de verificación para poder enviar o informe", "gui.abuseReport.send.service_unavailable": "Non se pode chegar ao servizo de Reportaxe abusivo. Por favor, asegúrate de que estás conectado a Internet e téntao de novo.", "gui.abuseReport.sending.title": "Enviando o teu informe...", "gui.abuseReport.sent.title": "Enviar reporte", "gui.abuseReport.skin.title": "Informar sobre o xogador %s", "gui.abuseReport.title": "Reportar x<PERSON>dor", "gui.abuseReport.type.chat": "Mensaxes do chat", "gui.abuseReport.type.name": "Nome do xogador", "gui.abuseReport.type.skin": "Aparencias do xogador", "gui.acknowledge": "Entendido", "gui.advancements": "<PERSON><PERSON><PERSON>", "gui.all": "<PERSON><PERSON>", "gui.back": "Voltar", "gui.banned.description": "%s\n\n%s\n\nSaber máis na seguinte ligazón: %s", "gui.banned.description.permanent": "A túa conta foi bloqueada permanentemente, o que significa que non podes nin xogar en liña nin unirte a Realms.", "gui.banned.description.reason": "Nos recentemente recibiramos un reporte por mal comportamento pola túa conta. Os nosos moderadores han revisado o teu caso e identificárono como %s, o cal vai en contra dos estándares da comunidade de Minecraft.", "gui.banned.description.reason_id": "Código: %s", "gui.banned.description.reason_id_message": "Código: %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON> ent<PERSON>, podes xogar en liña ou unirte a Realms.", "gui.banned.description.temporary.duration": "A túa conta está suspendida temporalmente e vai ser reactivada en %s.", "gui.banned.description.unknownreason": "O noso equipo recibiu recentemente un reporte por mal comportamento da túa conta. Os nosos moderadores revisaron o teu caso e identificaron que vai en contra dos estándares da comunidade de Minecraft.", "gui.banned.name.description": "O teu nome - \"%s\" - non respeta as Normas da Comunidade. Podes xogar o modo para un xogador, pero terás que cambiar o teu nome para xogar en liña.\n\nPodes ver máis información ou abrir un caso no seguinte hipervínculo: %s", "gui.banned.name.title": "Nome non permitido no multixogador", "gui.banned.reason.defamation_impersonation_false_information": "Suplantación ou intercambio de información para enganar ou aproveitarse doutros", "gui.banned.reason.drugs": "Referencias a drogas ilegais", "gui.banned.reason.extreme_violence_or_gore": "Representacións de extrema violencia ou gore da vida real", "gui.banned.reason.false_reporting": "Exceso de informes falsos ou inexactos", "gui.banned.reason.fraud": "Adquisición ou uso fraudulento de contido", "gui.banned.reason.generic_violation": "Vulneración das normas comunitarias", "gui.banned.reason.harassment_or_bullying": "Lingua abusiva utilizada de forma directa e nociva", "gui.banned.reason.hate_speech": "Discurso de odio ou discriminación", "gui.banned.reason.hate_terrorism_notorious_figure": "Referencias a grupos de odio, organizacións terroristas ou figuras notorias", "gui.banned.reason.imminent_harm_to_person_or_property": "Intención de causar danos na vida real a persoas ou bens", "gui.banned.reason.nudity_or_pornography": "Amosar material sexual ou pornográfico", "gui.banned.reason.sexually_inappropriate": "<PERSON><PERSON> ou contidos de natureza sexual", "gui.banned.reason.spam_or_advertising": "Spam ou publicidade", "gui.banned.skin.description": "A túa apariencia non respeta as Normas da Comunidade. Podes seguir xogando cunha apariencia predefinida ou seleccionar unha nova.\n\nPodes ver máis información ou abrir un caso no seguinte hipervínculo: %s", "gui.banned.skin.title": "Apariencia non permitida", "gui.banned.title.permanent": "Conta bloqueada permanentemente", "gui.banned.title.temporary": "Conta suspendida temporalmente", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Comentarios", "gui.chatReport.describe": "Compartindo detalles axudaranos facer unha decisión ben informada.", "gui.chatReport.discard.content": "Se saes, perderás esta denuncia e os teus comentarios.\nTes a certeza que queres saír?", "gui.chatReport.discard.discard": "Saír e desbotar denuncia", "gui.chatReport.discard.draft": "Gardar como borrador", "gui.chatReport.discard.return": "Continuar a editar", "gui.chatReport.discard.title": "Desbotar denuncia e comentarios?", "gui.chatReport.draft.content": "Queres continuar a editar a denuncia existente ou desbotala e crear unha nova?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Con<PERSON><PERSON>r editando", "gui.chatReport.draft.quittotitle.content": "Queres continuar editando ou desbotar?", "gui.chatReport.draft.quittotitle.title": "Tes un borrador de denuncia no chat que se perderá se saes", "gui.chatReport.draft.title": "Editar o borrador do reporte do chat?", "gui.chatReport.more_comments": "Por favor, describa que ocorreu:", "gui.chatReport.observed_what": "Por que estás demandando isto?", "gui.chatReport.read_info": "Mostrar máis información sobre reportes", "gui.chatReport.report_sent_msg": "Recibimos de maneira adecuada o seu reporte. Moitas grazas!\n\nO noso equipo revisaralo o antes posible.", "gui.chatReport.select_chat": "Selecciona unha mensaxe da conversa para reportar", "gui.chatReport.select_reason": "Selecciona unha categoría de demanda", "gui.chatReport.selected_chat": "%s mensaxe(s) seleccionado(s) para reportar", "gui.chatReport.send": "Enviar reportaxe", "gui.chatReport.send.comments_too_long": "Por favor, acurta o comentario", "gui.chatReport.send.no_reason": "Por favor seleccione unha categoría de demanda", "gui.chatReport.send.no_reported_messages": "Por favor seleccione polo menos unha mensaxe para demandar", "gui.chatReport.send.too_many_messages": "Ten<PERSON>do incluír demasiadas mensaxes no informe", "gui.chatReport.title": "Reportar x<PERSON>dor", "gui.chatSelection.context": "As mensaxes rodeando esta selección serán enviadas para incluir contexto adicional", "gui.chatSelection.fold": "%s mensaxes agochadas", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s entrou ao chat", "gui.chatSelection.message.narrate": "%s díxolle: %s a %s", "gui.chatSelection.selected": "%s/%s mensaxe(s) seleccionado(s)", "gui.chatSelection.title": "Seleccionar mensaxes a reportar", "gui.continue": "<PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copiar ligazón ao portapapeis", "gui.days": "%s día(s)", "gui.done": "<PERSON><PERSON>", "gui.down": "Abaixo", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rexeitáronse os ficheiros %s", "gui.fileDropFailure.title": "Error ao engadir arquivos", "gui.hours": "%s hora(s)", "gui.loadingMinecraft": "Cargando Minecraft", "gui.minutes": "%s minuto(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Botón de %s", "gui.narrate.editBox": "Barra de texto de %s: %s", "gui.narrate.slider": "Barra de %s", "gui.narrate.tab": "Ficha de %s", "gui.no": "Non", "gui.none": "<PERSON><PERSON><PERSON>", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Abrir o directorio de informes", "gui.proceed": "<PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "Clic dereito para máis", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Procurar...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON> todo", "gui.recipebook.toggleRecipes.blastable": "Amosando altos fundibles", "gui.recipebook.toggleRecipes.craftable": "Amosando fabricables", "gui.recipebook.toggleRecipes.smeltable": "Amosando fundibles", "gui.recipebook.toggleRecipes.smokable": "Amosando afumeables", "gui.report_to_server": "Reportar ao servidor", "gui.socialInteractions.blocking_hint": "Xestionar coa conta de Microsoft", "gui.socialInteractions.empty_blocked": "Non hay xogadores bloqueados no chat", "gui.socialInteractions.empty_hidden": "Non hai xogadores agochados no chat", "gui.socialInteractions.hidden_in_chat": "As mensaxes de %s serán agochadas", "gui.socialInteractions.hide": "Agochar no chat", "gui.socialInteractions.narration.hide": "Agochar mensaxes de %s", "gui.socialInteractions.narration.report": "Informar sobre o xogador %s", "gui.socialInteractions.narration.show": "Mostrar mensaxes de %s", "gui.socialInteractions.report": "Reportar", "gui.socialInteractions.search_empty": "Non se puido atopar ningún xogador con ese nome", "gui.socialInteractions.search_hint": "Buscar...", "gui.socialInteractions.server_label.multiple": "%s - %s xogadores", "gui.socialInteractions.server_label.single": "%s - %s xogador", "gui.socialInteractions.show": "<PERSON>ar no chat", "gui.socialInteractions.shown_in_chat": "As mensaxes de %s serán amosadas", "gui.socialInteractions.status_blocked": "Bloqueado", "gui.socialInteractions.status_blocked_offline": "Bloqueado - Sen conexión", "gui.socialInteractions.status_hidden": "Agochado", "gui.socialInteractions.status_hidden_offline": "Agochado - Sen cone<PERSON>", "gui.socialInteractions.status_offline": "<PERSON>", "gui.socialInteractions.tab_all": "Todos", "gui.socialInteractions.tab_blocked": "Bloqueado", "gui.socialInteractions.tab_hidden": "Agochado", "gui.socialInteractions.title": "Interacións sociais", "gui.socialInteractions.tooltip.hide": "Agocha<PERSON> mensa<PERSON>", "gui.socialInteractions.tooltip.report": "Informar sobre xogador", "gui.socialInteractions.tooltip.report.disabled": "O servizo de reportes non está dispoñíbel", "gui.socialInteractions.tooltip.report.no_messages": "Non hai mensaxes que se poidan reportar do xogador", "gui.socialInteractions.tooltip.report.not_reportable": "Este xogador non pode ser reportado, xa que as súas mensaxes non poden ser verificadas neste servidor", "gui.socialInteractions.tooltip.show": "Mostrar mensaxes", "gui.stats": "Estatísticas", "gui.toMenu": "Volver á listaxe de servidores", "gui.toRealms": "Volver á lista de realms", "gui.toTitle": "<PERSON>ver ao menú principal", "gui.toWorld": "Volver á lista de mundos", "gui.togglable_slot": "Clic para rehabilitar a rañura", "gui.up": "Arriba", "gui.waitingForResponse.button.inactive": "Voltar (%ss)", "gui.waitingForResponse.title": "Agardando polo servidor", "gui.yes": "Si", "hanging_sign.edit": "Editar a mensaxe do letreiro colgante", "instrument.minecraft.admire_goat_horn": "Admiración", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Reflexión", "instrument.minecraft.seek_goat_horn": "Procura", "instrument.minecraft.sing_goat_horn": "Canto", "instrument.minecraft.yearn_goat_horn": "Devezo", "inventory.binSlot": "<PERSON><PERSON><PERSON><PERSON><PERSON> obxecto", "inventory.hotbarInfo": "Garda a barra de obxectos con %1$s+%2$s", "inventory.hotbarSaved": "Barra de obxectos gardada (recupéraa con %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON> romper:", "item.canPlace": "Pode colocarse sobre:", "item.canUse.unknown": "Descoñecido", "item.color": "Cor: %s", "item.components": "%s compoñentes", "item.disabled": "Obxecto desactivado", "item.durability": "Durabilidade: %s / %s", "item.dyed": "Tinguido", "item.minecraft.acacia_boat": "Barca de acacia", "item.minecraft.acacia_chest_boat": "Barca de acacia con cofre", "item.minecraft.allay_spawn_egg": "Ovo de invocación de allay", "item.minecraft.amethyst_shard": "Fragmento de ametista", "item.minecraft.angler_pottery_shard": "Fragmento de cerámica de pescador", "item.minecraft.angler_pottery_sherd": "Fragmento de cerámica de pescador", "item.minecraft.apple": "Mazá", "item.minecraft.archer_pottery_shard": "Fragmento de cerámica de arqueiro", "item.minecraft.archer_pottery_sherd": "Fragmento de cerámica de arqueiro", "item.minecraft.armadillo_scute": "Escama de armadillo", "item.minecraft.armadillo_spawn_egg": "Ovo de invocación de armadillo", "item.minecraft.armor_stand": "Soporte de armadura", "item.minecraft.arms_up_pottery_shard": "Fragmento de cerámica de brazos arriba", "item.minecraft.arms_up_pottery_sherd": "Fragmento de cerámica de brazos arriba", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Balde con axolote", "item.minecraft.axolotl_spawn_egg": "Ovo de invocación de axolote", "item.minecraft.baked_potato": "Pataca cociñada", "item.minecraft.bamboo_chest_raft": "Balsa de bambú con cofre", "item.minecraft.bamboo_raft": "Balsa de bambú", "item.minecraft.bat_spawn_egg": "Ovo de invocación de morcego", "item.minecraft.bee_spawn_egg": "Ovo de invocación de abella", "item.minecraft.beef": "Filete cru", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Sementes de remolacha", "item.minecraft.beetroot_soup": "Sopa de remolacha", "item.minecraft.birch_boat": "Barca de bidueiro", "item.minecraft.birch_chest_boat": "Barca de bidueiro con cofre", "item.minecraft.black_bundle": "Saco negro", "item.minecraft.black_dye": "Tintura negra", "item.minecraft.black_harness": "<PERSON><PERSON>o negro", "item.minecraft.blade_pottery_shard": "Fragmento de cerámica de espada", "item.minecraft.blade_pottery_sherd": "Fragmento de cerámica de espada", "item.minecraft.blaze_powder": "Po de blaze", "item.minecraft.blaze_rod": "Vara de blaze", "item.minecraft.blaze_spawn_egg": "Ovo de invocación de blaze", "item.minecraft.blue_bundle": "Saco azul", "item.minecraft.blue_dye": "Tintura azul", "item.minecraft.blue_egg": "<PERSON><PERSON> azul", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON> a<PERSON>l", "item.minecraft.bogged_spawn_egg": "Ovo de invocación de enlamado", "item.minecraft.bolt_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.bolt_armor_trim_smithing_template.new": "Deseño de parafuso", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON>", "item.minecraft.book": "Libro", "item.minecraft.bordure_indented_banner_pattern": "Borde recortar do patrón de bandeira", "item.minecraft.bow": "Arco", "item.minecraft.bowl": "Cunca", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Vara de remuíño", "item.minecraft.breeze_spawn_egg": "Ovo de invocación de remuíño", "item.minecraft.brewer_pottery_shard": "Fragmento de cerámica de poción", "item.minecraft.brewer_pottery_sherd": "Fragmento de cerámica de poción", "item.minecraft.brewing_stand": "Fermentador", "item.minecraft.brick": "Ladrillo", "item.minecraft.brown_bundle": "Saco <PERSON>", "item.minecraft.brown_dye": "Tintura marrón", "item.minecraft.brown_egg": "<PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Balde", "item.minecraft.bundle": "Saco", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Pode conter un conxunto diverso de obxectos", "item.minecraft.bundle.full": "<PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Fragmento de cerámica de lume", "item.minecraft.burn_pottery_sherd": "Fragmento de cerámica de lume", "item.minecraft.camel_spawn_egg": "Ovo de invocación de camelo", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON> nun pau", "item.minecraft.cat_spawn_egg": "Ovo de invocación de gato", "item.minecraft.cauldron": "Caldeiro", "item.minecraft.cave_spider_spawn_egg": "Ovo de invocación de araña de cova", "item.minecraft.chainmail_boots": "Botas de cota de malla", "item.minecraft.chainmail_chestplate": "Coiraza de cota de malla", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON> de cota de mallas", "item.minecraft.chainmail_leggings": "Perneiras de cota de malla", "item.minecraft.charcoal": "Carbón vexetal", "item.minecraft.cherry_boat": "Barca de cerdeira", "item.minecraft.cherry_chest_boat": "Barca de cerdeira con cofre", "item.minecraft.chest_minecart": "Vagoneta con cofre", "item.minecraft.chicken": "Polo cru", "item.minecraft.chicken_spawn_egg": "Ovo de invocación de galiña", "item.minecraft.chorus_fruit": "Froita coral", "item.minecraft.clay_ball": "Bóla de arxila", "item.minecraft.clock": "Reloxo", "item.minecraft.coal": "Carbón", "item.minecraft.coast_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.cocoa_beans": "Sementes de cacao", "item.minecraft.cod": "Bacallau cru", "item.minecraft.cod_bucket": "Balde con bacallau", "item.minecraft.cod_spawn_egg": "Ovo de invocación de bacallau", "item.minecraft.command_block_minecart": "Vagoneta con bloque de comandos", "item.minecraft.compass": "Compás", "item.minecraft.cooked_beef": "<PERSON><PERSON>", "item.minecraft.cooked_chicken": "Polo cociñado", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_porkchop": "Costeleta de porco cociñada", "item.minecraft.cooked_rabbit": "<PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "Galleta", "item.minecraft.copper_ingot": "Lingote de cobre", "item.minecraft.cow_spawn_egg": "Ovo de invocación de vaca", "item.minecraft.creaking_spawn_egg": "Ovo de invocación de crepitante", "item.minecraft.creeper_banner_pattern": "Deseño de estandarte", "item.minecraft.creeper_banner_pattern.desc": "Estampado de creeper", "item.minecraft.creeper_banner_pattern.new": "Patrón de bandeira de Creeper", "item.minecraft.creeper_spawn_egg": "Ovo de invocación de creeper", "item.minecraft.crossbow": "Bésta", "item.minecraft.crossbow.projectile": "Proxectil:", "item.minecraft.crossbow.projectile.multiple": "Proxectil: %s x %s", "item.minecraft.crossbow.projectile.single": "Proxectil: %s", "item.minecraft.cyan_bundle": "Saco ciano", "item.minecraft.cyan_dye": "Tintura ciano", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.danger_pottery_shard": "Fragmento de cerámica de perigo", "item.minecraft.danger_pottery_sherd": "Fragmento de cerámica de perigo", "item.minecraft.dark_oak_boat": "Barca de carballo escuro", "item.minecraft.dark_oak_chest_boat": "Barca de carballo escuro con cofre", "item.minecraft.debug_stick": "Pau de depuración", "item.minecraft.debug_stick.empty": "%s non ten propiedades", "item.minecraft.debug_stick.select": "seleccionouse \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" a %s", "item.minecraft.diamond": "Diamante", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "Botas de diamante", "item.minecraft.diamond_chestplate": "Coiraza de diamante", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Sacho <PERSON>", "item.minecraft.diamond_horse_armor": "Armadura de diamante para cabalo", "item.minecraft.diamond_leggings": "Perneiras de diamante", "item.minecraft.diamond_pickaxe": "Pico de di<PERSON>", "item.minecraft.diamond_shovel": "Pa de diamante", "item.minecraft.diamond_sword": "Espada de diamante", "item.minecraft.disc_fragment_5": "Anaco de disco", "item.minecraft.disc_fragment_5.desc": "Disco de música - 5", "item.minecraft.dolphin_spawn_egg": "Ovo de invocación de golfiño", "item.minecraft.donkey_spawn_egg": "Ovo de invocación de burro", "item.minecraft.dragon_breath": "<PERSON><PERSON>", "item.minecraft.dried_kelp": "Alga seca", "item.minecraft.drowned_spawn_egg": "Ovo de invocación de afogado", "item.minecraft.dune_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.dune_armor_trim_smithing_template.new": "Deseño de dunas", "item.minecraft.echo_shard": "Fragmento de eco", "item.minecraft.egg": "Ovo", "item.minecraft.elder_guardian_spawn_egg": "Ovo de invocación de gardián ancián", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "Esm<PERSON><PERSON>", "item.minecraft.enchanted_book": "Libro encantado", "item.minecraft.enchanted_golden_apple": "Mazá de ouro encantada", "item.minecraft.end_crystal": "Cristal do <PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Ovo de invocación de dragón do Ender", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Ovo de invocación de enderman", "item.minecraft.endermite_spawn_egg": "Ovo de invocación de endermite", "item.minecraft.evoker_spawn_egg": "Ovo de invocación de invocador", "item.minecraft.experience_bottle": "Frasco de experiencia", "item.minecraft.explorer_pottery_shard": "Fragmento de cerámica de explorador", "item.minecraft.explorer_pottery_sherd": "Fragmento de cerámica de explorador", "item.minecraft.eye_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.eye_armor_trim_smithing_template.new": "Deseño <PERSON>", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Ollo de araña fermentado", "item.minecraft.field_masoned_banner_pattern": "Patrón de bandeira de muro", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Bóla de lume", "item.minecraft.firework_rocket": "Foguete", "item.minecraft.firework_rocket.flight": "Duración do voo:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Estrela <PERSON>", "item.minecraft.firework_star.black": "Negro", "item.minecraft.firework_star.blue": "Azul", "item.minecraft.firework_star.brown": "<PERSON>r<PERSON>", "item.minecraft.firework_star.custom_color": "Personalizado", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Esvaecemento", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON>", "item.minecraft.firework_star.green": "Verde", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON>l claro", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON> claro", "item.minecraft.firework_star.lime": "Lima", "item.minecraft.firework_star.magenta": "<PERSON>ent<PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "Púrpura", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Forma descoñecida", "item.minecraft.firework_star.shape.burst": "<PERSON>mpul<PERSON>", "item.minecraft.firework_star.shape.creeper": "<PERSON><PERSON> de creeper", "item.minecraft.firework_star.shape.large_ball": "Bóla grande", "item.minecraft.firework_star.shape.small_ball": "Bóla pequena", "item.minecraft.firework_star.shape.star": "Forma de estrela", "item.minecraft.firework_star.trail": "Rastro", "item.minecraft.firework_star.white": "Branco", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "Cana de pescar", "item.minecraft.flint": "Pedernal", "item.minecraft.flint_and_steel": "Chisqueiro", "item.minecraft.flow_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.flow_armor_trim_smithing_template.new": "Deseño de remuíño", "item.minecraft.flow_banner_pattern": "Deseño de estandarte", "item.minecraft.flow_banner_pattern.desc": "Espiral", "item.minecraft.flow_banner_pattern.new": "Patròn de bandeira de remolino", "item.minecraft.flow_pottery_sherd": "Fragmento de cerámica de remuíño", "item.minecraft.flower_banner_pattern": "Deseño de estandarte", "item.minecraft.flower_banner_pattern.desc": "Estampado de flor", "item.minecraft.flower_banner_pattern.new": "Patrón de bandeira de flores", "item.minecraft.flower_pot": "<PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "Ovo de invocación de raposo", "item.minecraft.friend_pottery_shard": "Fragmento de cerámica de amigo", "item.minecraft.friend_pottery_sherd": "Fragmento de cerámica de amigo", "item.minecraft.frog_spawn_egg": "Ovo de invocación de ra", "item.minecraft.furnace_minecart": "Vagoneta con forno", "item.minecraft.ghast_spawn_egg": "Ovo de invocación de ghast", "item.minecraft.ghast_tear": "Bágoa de ghast", "item.minecraft.glass_bottle": "Frasco de vidro", "item.minecraft.glistering_melon_slice": "Anaco de sandía relucente", "item.minecraft.globe_banner_pattern": "Deseño de estandarte", "item.minecraft.globe_banner_pattern.desc": "Estampado de planeta", "item.minecraft.globe_banner_pattern.new": "Patrón de bandeira de globo", "item.minecraft.glow_berries": "Bagas brillantes", "item.minecraft.glow_ink_sac": "Saco de borra brillante", "item.minecraft.glow_item_frame": "<PERSON> brilla<PERSON>", "item.minecraft.glow_squid_spawn_egg": "Ovo de invocación de lura brillante", "item.minecraft.glowstone_dust": "Po de pedra luminosa", "item.minecraft.goat_horn": "Corno de cabra", "item.minecraft.goat_spawn_egg": "Ovo de invocación de cabra", "item.minecraft.gold_ingot": "Lingote de ouro", "item.minecraft.gold_nugget": "Pebida de ouro", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON> de <PERSON>o", "item.minecraft.golden_boots": "Botas de ouro", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON> do<PERSON>", "item.minecraft.golden_chestplate": "Coiraza de ouro", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON> de <PERSON>o", "item.minecraft.golden_hoe": "Sacho de ouro", "item.minecraft.golden_horse_armor": "Armadura de ouro para cabalo", "item.minecraft.golden_leggings": "Perneiras de ouro", "item.minecraft.golden_pickaxe": "Pico de ouro", "item.minecraft.golden_shovel": "Pa de ouro", "item.minecraft.golden_sword": "Espada de ouro", "item.minecraft.gray_bundle": "Saco gris", "item.minecraft.gray_dye": "Tintura gris", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON> gris", "item.minecraft.green_bundle": "Saco verde", "item.minecraft.green_dye": "Tintura verde", "item.minecraft.green_harness": "A<PERSON>o verde", "item.minecraft.guardian_spawn_egg": "Ovo de invocación de gardián", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "Deseño do estandarte", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Patrón de estandarte de remuíño", "item.minecraft.guster_pottery_sherd": "Fragmento de cerámica de remuíño", "item.minecraft.happy_ghast_spawn_egg": "Ovo de invocación de ghast ledo", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Corazón do océano", "item.minecraft.heart_pottery_shard": "Fragmento de cerámica de corazón", "item.minecraft.heart_pottery_sherd": "Fragmento de cerámica de corazón", "item.minecraft.heartbreak_pottery_shard": "Fragmento de cerámica de corazón escachado", "item.minecraft.heartbreak_pottery_sherd": "Fragmento de cerámica de corazón escachado", "item.minecraft.hoglin_spawn_egg": "Ovo de invocación de hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON> de mel", "item.minecraft.honeycomb": "Favo", "item.minecraft.hopper_minecart": "Vagoneta con funil", "item.minecraft.horse_spawn_egg": "Ovo de invocación de cabalo", "item.minecraft.host_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.host_armor_trim_smithing_template.new": "Deseño de anfitrión", "item.minecraft.howl_pottery_shard": "Fragmento de cerámica de ouveo", "item.minecraft.howl_pottery_sherd": "Fragmento de cerámica de ouveo", "item.minecraft.husk_spawn_egg": "Ovo de invocación de momia", "item.minecraft.ink_sac": "Saco de borra", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON>erro", "item.minecraft.iron_boots": "Botas de ferro", "item.minecraft.iron_chestplate": "Coiraza de ferro", "item.minecraft.iron_golem_spawn_egg": "Ovo de invocación de gólem de ferro", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.iron_hoe": "Sacho de ferro", "item.minecraft.iron_horse_armor": "Armadura de ferro para cabalo", "item.minecraft.iron_ingot": "Lingote de ferro", "item.minecraft.iron_leggings": "Perneiras de ferro", "item.minecraft.iron_nugget": "Pebida de ferro", "item.minecraft.iron_pickaxe": "Pico de ferro", "item.minecraft.iron_shovel": "Pa de ferro", "item.minecraft.iron_sword": "Espada de ferro", "item.minecraft.item_frame": "<PERSON>", "item.minecraft.jungle_boat": "Barca de selva", "item.minecraft.jungle_chest_boat": "Barca da selva con cofre", "item.minecraft.knowledge_book": "Enciclopedia", "item.minecraft.lapis_lazuli": "La<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Balde con lava", "item.minecraft.lead": "Renda", "item.minecraft.leather": "Coiro", "item.minecraft.leather_boots": "Botas de coiro", "item.minecraft.leather_chestplate": "Coiraza de coiro", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Armadura de coiro para cabalo", "item.minecraft.leather_leggings": "Perneiras de coiro", "item.minecraft.light_blue_bundle": "Saco azul claro", "item.minecraft.light_blue_dye": "Tintura azul claro", "item.minecraft.light_blue_harness": "<PERSON><PERSON>o azul claro", "item.minecraft.light_gray_bundle": "Saco gris claro", "item.minecraft.light_gray_dye": "Tintura gris claro", "item.minecraft.light_gray_harness": "<PERSON><PERSON>o gris claro", "item.minecraft.lime_bundle": "Saco lima", "item.minecraft.lime_dye": "Tintura lima", "item.minecraft.lime_harness": "A<PERSON>o lima", "item.minecraft.lingering_potion": "Poción prolongada", "item.minecraft.lingering_potion.effect.awkward": "Poción prolongada estraña", "item.minecraft.lingering_potion.effect.empty": "Poción prolongada non fabricable", "item.minecraft.lingering_potion.effect.fire_resistance": "Poción prolongada de resistencia ao lume", "item.minecraft.lingering_potion.effect.harming": "Poción prolongada de dano", "item.minecraft.lingering_potion.effect.healing": "Poción prolongada de curación", "item.minecraft.lingering_potion.effect.infested": "Poción prolongada de infestación", "item.minecraft.lingering_potion.effect.invisibility": "Poción prolongada de invisibilidade", "item.minecraft.lingering_potion.effect.leaping": "Poción prolongada de brinco", "item.minecraft.lingering_potion.effect.levitation": "Poción prolongada de levitación", "item.minecraft.lingering_potion.effect.luck": "Poción prolongada de sorte", "item.minecraft.lingering_potion.effect.mundane": "Poción prolongada vulgar", "item.minecraft.lingering_potion.effect.night_vision": "Poción prolongada de visión nocturna", "item.minecraft.lingering_potion.effect.oozing": "Poción prolongada pegañenta", "item.minecraft.lingering_potion.effect.poison": "Poción prolongada de veleno", "item.minecraft.lingering_potion.effect.regeneration": "Poción prolongada de rexeneración", "item.minecraft.lingering_potion.effect.slow_falling": "Poción prolongada de caída lenta", "item.minecraft.lingering_potion.effect.slowness": "Poción prolongada de lentitude", "item.minecraft.lingering_potion.effect.strength": "Poción prolongada de forza", "item.minecraft.lingering_potion.effect.swiftness": "Poción prolongada de velocidade", "item.minecraft.lingering_potion.effect.thick": "Poción prolongada mesta", "item.minecraft.lingering_potion.effect.turtle_master": "Poción prolongada do mestre tartaruga", "item.minecraft.lingering_potion.effect.water": "Frasco prolongado de auga", "item.minecraft.lingering_potion.effect.water_breathing": "Poción prolongada de respiración acuática", "item.minecraft.lingering_potion.effect.weakness": "Poción prolongada de frouxidade", "item.minecraft.lingering_potion.effect.weaving": "Poción prolongada de tecedura", "item.minecraft.lingering_potion.effect.wind_charged": "Poción prolongada de carga de vento", "item.minecraft.llama_spawn_egg": "Ovo de invocación de llama", "item.minecraft.lodestone_compass": "Compás magnetizado", "item.minecraft.mace": "Clava", "item.minecraft.magenta_bundle": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.magenta_dye": "<PERSON><PERSON> maxenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Ovo de invocación de cubo de magma", "item.minecraft.mangrove_boat": "Barca de mangleiro", "item.minecraft.mangrove_chest_boat": "Barca de mangleiro con cofre", "item.minecraft.map": "Mapa baleiro", "item.minecraft.melon_seeds": "Sementes de sandía", "item.minecraft.melon_slice": "Anaco de <PERSON>", "item.minecraft.milk_bucket": "Balde con leite", "item.minecraft.minecart": "Vagoneta", "item.minecraft.miner_pottery_shard": "Fragmento de cerámica de mineiro", "item.minecraft.miner_pottery_sherd": "Fragmento de cerámica de mineiro", "item.minecraft.mojang_banner_pattern": "Deseño de estandarte", "item.minecraft.mojang_banner_pattern.desc": "Símbolo de Mojang", "item.minecraft.mojang_banner_pattern.new": "Patrón de bandeira de chintófano", "item.minecraft.mooshroom_spawn_egg": "Ovo de invocación de mooshroom", "item.minecraft.mourner_pottery_shard": "Fragmento de cerámica de carpideira", "item.minecraft.mourner_pottery_sherd": "Fragmento de cerámica de carpideira", "item.minecraft.mule_spawn_egg": "Ovo de invocación de mula", "item.minecraft.mushroom_stew": "Estufado de cogomelos", "item.minecraft.music_disc_11": "Disco de música", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disco de música", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disco de música", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disco de música", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disco de música", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disco de música", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disco de música", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disco de música", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (caixa de música)", "item.minecraft.music_disc_far": "Disco de música", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disco de música", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disco de música", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disco de música", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disco de música", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disco de música", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disco de música", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disco de música", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disco de música", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disco de música", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disco de música", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disco de música", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disco de música", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Cordeiro cru", "item.minecraft.name_tag": "Etiqueta de nome", "item.minecraft.nautilus_shell": "Cuncha de náutilo", "item.minecraft.nether_brick": "Ladrillos do Nether", "item.minecraft.nether_star": "Estrela do Nether", "item.minecraft.nether_wart": "Espulla do <PERSON>her", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON> de <PERSON>herita", "item.minecraft.netherite_boots": "Botas de netherita", "item.minecraft.netherite_chestplate": "Coiraza de netherita", "item.minecraft.netherite_helmet": "Casco de netherita", "item.minecraft.netherite_hoe": "Sacho de netherita", "item.minecraft.netherite_ingot": "Lingote de netherita", "item.minecraft.netherite_leggings": "Perneiras de netherita", "item.minecraft.netherite_pickaxe": "Pico de netherita", "item.minecraft.netherite_scrap": "Anacos de netherita", "item.minecraft.netherite_shovel": "Pa de netherita", "item.minecraft.netherite_sword": "Espada de netherita", "item.minecraft.netherite_upgrade_smithing_template": "Molde de ferraría", "item.minecraft.netherite_upgrade_smithing_template.new": "Mellora de netherita", "item.minecraft.oak_boat": "Barca de carballo", "item.minecraft.oak_chest_boat": "Barca de carballo con cofre", "item.minecraft.ocelot_spawn_egg": "Ovo de invocación de ocelote", "item.minecraft.ominous_bottle": "Frasco sombrío", "item.minecraft.ominous_trial_key": "Chave de desafío sombría", "item.minecraft.orange_bundle": "Saco laranxa", "item.minecraft.orange_dye": "Tintura laranxa", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "<PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Barca de carballo esbrancuxado", "item.minecraft.pale_oak_chest_boat": "Barca de carballo esbrancuxado con cofre", "item.minecraft.panda_spawn_egg": "Ovo de invocación de panda", "item.minecraft.paper": "Papel", "item.minecraft.parrot_spawn_egg": "Ovo de invocación de papagaio", "item.minecraft.phantom_membrane": "Membrana de pantasma", "item.minecraft.phantom_spawn_egg": "Ovo de invocación de pantasma", "item.minecraft.pig_spawn_egg": "Ovo de invocación de porco", "item.minecraft.piglin_banner_pattern": "Deseño de estandarte", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Patrón de bandeira de fociño", "item.minecraft.piglin_brute_spawn_egg": "Ovo de invocación de piglin bruto", "item.minecraft.piglin_spawn_egg": "Ovo de invocación de piglin", "item.minecraft.pillager_spawn_egg": "Ovo de invocación de saqueador", "item.minecraft.pink_bundle": "Saco rosa", "item.minecraft.pink_dye": "Tintura rosa", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON> rosa", "item.minecraft.pitcher_plant": "Planta xerra", "item.minecraft.pitcher_pod": "Bulbo de planta xerra", "item.minecraft.plenty_pottery_shard": "Fragmento de cerámica de abundancia", "item.minecraft.plenty_pottery_sherd": "Fragmento de cerámica de abundancia", "item.minecraft.poisonous_potato": "Pataca velenosa", "item.minecraft.polar_bear_spawn_egg": "Ovo de invocación de oso polar", "item.minecraft.popped_chorus_fruit": "Flocos de froita coral", "item.minecraft.porkchop": "Costeleta de porco crúa", "item.minecraft.potato": "Pataca", "item.minecraft.potion": "Poción", "item.minecraft.potion.effect.awkward": "Poción estraña", "item.minecraft.potion.effect.empty": "Poción non fabricable", "item.minecraft.potion.effect.fire_resistance": "Poción de resistencia ao lume", "item.minecraft.potion.effect.harming": "Poción de dano", "item.minecraft.potion.effect.healing": "Poción de curación", "item.minecraft.potion.effect.infested": "Poción de infestación", "item.minecraft.potion.effect.invisibility": "Poción de invisibilidade", "item.minecraft.potion.effect.leaping": "Poción de brinco", "item.minecraft.potion.effect.levitation": "Poción de levitación", "item.minecraft.potion.effect.luck": "Poción de sorte", "item.minecraft.potion.effect.mundane": "Poción vulgar", "item.minecraft.potion.effect.night_vision": "Poción de visión nocturna", "item.minecraft.potion.effect.oozing": "Poción pegañenta", "item.minecraft.potion.effect.poison": "Poción de veleno", "item.minecraft.potion.effect.regeneration": "Poción de rexeneración", "item.minecraft.potion.effect.slow_falling": "Poción de caída lenta", "item.minecraft.potion.effect.slowness": "Poción de lentitude", "item.minecraft.potion.effect.strength": "Poción de forza", "item.minecraft.potion.effect.swiftness": "Poción de velocidade", "item.minecraft.potion.effect.thick": "Poción mesta", "item.minecraft.potion.effect.turtle_master": "Poción do mestre tartaruga", "item.minecraft.potion.effect.water": "Frasco de auga", "item.minecraft.potion.effect.water_breathing": "Poción de respiración acuática", "item.minecraft.potion.effect.weakness": "Poción de frouxidade", "item.minecraft.potion.effect.weaving": "Poción de tecedura", "item.minecraft.potion.effect.wind_charged": "Poción de carga de vento", "item.minecraft.pottery_shard_archer": "Fragmento de cerámica de arqueiro", "item.minecraft.pottery_shard_arms_up": "Fragmento de cerámica de brazos arriba", "item.minecraft.pottery_shard_prize": "Fragmento de cerámica de premio", "item.minecraft.pottery_shard_skull": "Fragmento de cerámica de caveira", "item.minecraft.powder_snow_bucket": "Balde con neve en po", "item.minecraft.prismarine_crystals": "Cristais de prismariña", "item.minecraft.prismarine_shard": "Anaco de prismariña", "item.minecraft.prize_pottery_shard": "Fragmento de cerámica de premio", "item.minecraft.prize_pottery_sherd": "Fragmento de cerámica de premio", "item.minecraft.pufferfish": "Peixe globo", "item.minecraft.pufferfish_bucket": "Balde con peixe globo", "item.minecraft.pufferfish_spawn_egg": "Ovo de invocación de peixe globo", "item.minecraft.pumpkin_pie": "Torta de cabaza", "item.minecraft.pumpkin_seeds": "Sementes de cabaza", "item.minecraft.purple_bundle": "Saco pú<PERSON>", "item.minecraft.purple_dye": "Tintura púrpura", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.quartz": "Cuarzo do Nether", "item.minecraft.rabbit": "<PERSON><PERSON> cru", "item.minecraft.rabbit_foot": "<PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON> de coello", "item.minecraft.rabbit_spawn_egg": "Ovo de invocación de coello", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.raiser_armor_trim_smithing_template.new": "Deseño de elevación", "item.minecraft.ravager_spawn_egg": "Ovo de invocación de devastador", "item.minecraft.raw_copper": "Cobre cru", "item.minecraft.raw_gold": "Ouro cru", "item.minecraft.raw_iron": "Ferro cru", "item.minecraft.recovery_compass": "Compás de recuperación", "item.minecraft.red_bundle": "<PERSON>co vermello", "item.minecraft.red_dye": "Tintura vermella", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.redstone": "Po de redstone", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON> recina", "item.minecraft.resin_clump": "<PERSON><PERSON> de recina", "item.minecraft.rib_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.rib_armor_trim_smithing_template.new": "Deseño de costelas", "item.minecraft.rotten_flesh": "<PERSON>ne podre", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "Salmón cru", "item.minecraft.salmon_bucket": "Balde con salmón", "item.minecraft.salmon_spawn_egg": "Ovo de invocación de salmón", "item.minecraft.scrape_pottery_sherd": "Fragmento de cerámica de raspado", "item.minecraft.scute": "Escama", "item.minecraft.sentry_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.sentry_armor_trim_smithing_template.new": "Deseño de sentinela", "item.minecraft.shaper_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.shaper_armor_trim_smithing_template.new": "Deseño de modelador", "item.minecraft.sheaf_pottery_shard": "Fragmento de cerámica de palla", "item.minecraft.sheaf_pottery_sherd": "Fragmento de cerámica de palla", "item.minecraft.shears": "Te<PERSON><PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Ovo de invocación de ovella", "item.minecraft.shelter_pottery_shard": "Fragmento de cerámica de refuxio", "item.minecraft.shelter_pottery_sherd": "Fragmento de cerámica de refuxio", "item.minecraft.shield": "Escudo", "item.minecraft.shield.black": "Escudo negro", "item.minecraft.shield.blue": "Escudo a<PERSON>l", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "Escudo ciano", "item.minecraft.shield.gray": "Escudo gris", "item.minecraft.shield.green": "Escudo verde", "item.minecraft.shield.light_blue": "Escudo azul claro", "item.minecraft.shield.light_gray": "Escudo gris claro", "item.minecraft.shield.lime": "Escudo lima", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "item.minecraft.shield.purple": "Escudo <PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.shield.white": "Escudo branco", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON> am<PERSON>o", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Ovo de invocación de shulker", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.silence_armor_trim_smithing_template.new": "Deseño de silencio", "item.minecraft.silverfish_spawn_egg": "Ovo de invocación de lepisma", "item.minecraft.skeleton_horse_spawn_egg": "Ovo de invocación de cabalo esqueleto", "item.minecraft.skeleton_spawn_egg": "Ovo de invocación de esqueleto", "item.minecraft.skull_banner_pattern": "Deseño de estandarte", "item.minecraft.skull_banner_pattern.desc": "Estampado de caveira", "item.minecraft.skull_banner_pattern.new": "Patrón de bandeira de caveira", "item.minecraft.skull_pottery_shard": "Fragmento de cerámica de caveira", "item.minecraft.skull_pottery_sherd": "Fragmento de cerámica de caveira", "item.minecraft.slime_ball": "Bóla de slime", "item.minecraft.slime_spawn_egg": "Ovo de invocación de slime", "item.minecraft.smithing_template": "Molde de ferraría", "item.minecraft.smithing_template.applies_to": "Aplica a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Engade un lingote ou cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Engade unha peza de armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingotes e cristais", "item.minecraft.smithing_template.ingredients": "Ingredientes:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Engade un lingote de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipamento de diamante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Engade unha armadura, arma ou ferramenta de di<PERSON>", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingote de netherita", "item.minecraft.smithing_template.upgrade": "Mellora: ", "item.minecraft.sniffer_spawn_egg": "Ovo de invocación de cheirador", "item.minecraft.snort_pottery_shard": "Fragmento de cerámica de cheirador", "item.minecraft.snort_pottery_sherd": "Fragmento de cerámica de cheirador", "item.minecraft.snout_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.snout_armor_trim_smithing_template.new": "Deseño de fociño", "item.minecraft.snow_golem_spawn_egg": "Ovo de invocación de gólem de neve", "item.minecraft.snowball": "Bóla de neve", "item.minecraft.spectral_arrow": "<PERSON><PERSON>a espectral", "item.minecraft.spider_eye": "<PERSON><PERSON>ña", "item.minecraft.spider_spawn_egg": "Ovo de invocación de araña", "item.minecraft.spire_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.spire_armor_trim_smithing_template.new": "Deseño de agulla", "item.minecraft.splash_potion": "Poción guindable", "item.minecraft.splash_potion.effect.awkward": "Poción guindable estraña", "item.minecraft.splash_potion.effect.empty": "Poción guindable non fabricable", "item.minecraft.splash_potion.effect.fire_resistance": "Poción guindable de resistencia ao lume", "item.minecraft.splash_potion.effect.harming": "Poción guindable de dano", "item.minecraft.splash_potion.effect.healing": "Poción guindable de curación", "item.minecraft.splash_potion.effect.infested": "Poción guindable de infestación", "item.minecraft.splash_potion.effect.invisibility": "Poción guindable de invisibilidade", "item.minecraft.splash_potion.effect.leaping": "Poción guindable de brinco", "item.minecraft.splash_potion.effect.levitation": "Poción guindable de levitación", "item.minecraft.splash_potion.effect.luck": "Poción guindable de sorte", "item.minecraft.splash_potion.effect.mundane": "Poción guindable vulgar", "item.minecraft.splash_potion.effect.night_vision": "Poción guindable de visión nocturna", "item.minecraft.splash_potion.effect.oozing": "Poción guindable pegañenta", "item.minecraft.splash_potion.effect.poison": "Poción guindable de veleno", "item.minecraft.splash_potion.effect.regeneration": "Poción guindable de rexeneración", "item.minecraft.splash_potion.effect.slow_falling": "Poción guindable de caída lenta", "item.minecraft.splash_potion.effect.slowness": "Poción guindable de lentitude", "item.minecraft.splash_potion.effect.strength": "Poción guindable de forza", "item.minecraft.splash_potion.effect.swiftness": "Poción guindable de velocidade", "item.minecraft.splash_potion.effect.thick": "Poción guindable mesta", "item.minecraft.splash_potion.effect.turtle_master": "Poción guindable do mestre tartaruga", "item.minecraft.splash_potion.effect.water": "Frasco guinda<PERSON> de auga", "item.minecraft.splash_potion.effect.water_breathing": "Poción guindable de respiración acuática", "item.minecraft.splash_potion.effect.weakness": "Poción guindable de frouxidade", "item.minecraft.splash_potion.effect.weaving": "Poción guindable de tecedura", "item.minecraft.splash_potion.effect.wind_charged": "Poción guindable de carga de vento", "item.minecraft.spruce_boat": "Barca de abeto", "item.minecraft.spruce_chest_boat": "Barca de abeto con cofre", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Ovo de invocación de lura", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "Sacho de pedra", "item.minecraft.stone_pickaxe": "Pico de <PERSON>edra", "item.minecraft.stone_shovel": "Pa de pedra", "item.minecraft.stone_sword": "Espada de pedra", "item.minecraft.stray_spawn_egg": "Ovo de invocación de esqueleto glacial", "item.minecraft.strider_spawn_egg": "Ovo de invocación de vagante", "item.minecraft.string": "Fío", "item.minecraft.sugar": "Azucre", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "Bagas doces", "item.minecraft.tadpole_bucket": "<PERSON><PERSON>", "item.minecraft.tadpole_spawn_egg": "Ovo de invocación de cágado", "item.minecraft.tide_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.tide_armor_trim_smithing_template.new": "Deseño de marea", "item.minecraft.tipped_arrow": "Frecha con efecto", "item.minecraft.tipped_arrow.effect.awkward": "Frecha con auga estraña", "item.minecraft.tipped_arrow.effect.empty": "Frecha con efecto non fabricable", "item.minecraft.tipped_arrow.effect.fire_resistance": "Frecha de resistencia ao lume", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Frecha de curación", "item.minecraft.tipped_arrow.effect.infested": "Frecha de infestación", "item.minecraft.tipped_arrow.effect.invisibility": "Frecha de invisibilidade", "item.minecraft.tipped_arrow.effect.leaping": "Frecha de brinco", "item.minecraft.tipped_arrow.effect.levitation": "Frecha de levitación", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON>a de sorte", "item.minecraft.tipped_arrow.effect.mundane": "Frecha con efecto", "item.minecraft.tipped_arrow.effect.night_vision": "Frecha de visión nocturna", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON>a pega<PERSON>a", "item.minecraft.tipped_arrow.effect.poison": "Frecha de veleno", "item.minecraft.tipped_arrow.effect.regeneration": "Frecha de rexeneración", "item.minecraft.tipped_arrow.effect.slow_falling": "Frecha de caída lenta", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON> lent<PERSON>", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "Frecha de velocidade", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON>a mesta", "item.minecraft.tipped_arrow.effect.turtle_master": "Frecha do mestre tartaruga", "item.minecraft.tipped_arrow.effect.water": "Frecha sen efecto", "item.minecraft.tipped_arrow.effect.water_breathing": "Frecha de respiración acuática", "item.minecraft.tipped_arrow.effect.weakness": "Frecha de frouxidade", "item.minecraft.tipped_arrow.effect.weaving": "Frecha de tecedura", "item.minecraft.tipped_arrow.effect.wind_charged": "Frecha de carga de vento", "item.minecraft.tnt_minecart": "Vagoneta con dinamita", "item.minecraft.torchflower_seeds": "Sementes de planta de lume", "item.minecraft.totem_of_undying": "Tótem da inmortalidade", "item.minecraft.trader_llama_spawn_egg": "Ovo de invocación de llama ambulante", "item.minecraft.trial_key": "Chave de <PERSON>", "item.minecraft.trident": "Tridente", "item.minecraft.tropical_fish": "Peixe tropical", "item.minecraft.tropical_fish_bucket": "Balde con peixe tropical", "item.minecraft.tropical_fish_spawn_egg": "Ovo de invocación de peixe tropical", "item.minecraft.turtle_helmet": "Cuncha de tartaruga", "item.minecraft.turtle_scute": "Escama de tartaruga", "item.minecraft.turtle_spawn_egg": "Ovo de invocación de tartaruga", "item.minecraft.vex_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.vex_armor_trim_smithing_template.new": "Deseño de espírito", "item.minecraft.vex_spawn_egg": "Ovo de invocación de espírito", "item.minecraft.villager_spawn_egg": "Ovo de invocación de aldeán", "item.minecraft.vindicator_spawn_egg": "Ovo de invocación de vindicador", "item.minecraft.wandering_trader_spawn_egg": "Ovo de invocación de vendedor ambulante", "item.minecraft.ward_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.ward_armor_trim_smithing_template.new": "Molde de armadura de vixilante", "item.minecraft.warden_spawn_egg": "Ovo de invocación de vixilante", "item.minecraft.warped_fungus_on_a_stick": "Fungo deformado nun pau", "item.minecraft.water_bucket": "Balde con auga", "item.minecraft.wayfinder_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Deseño de guía", "item.minecraft.wheat": "Trigo", "item.minecraft.wheat_seeds": "Sementes de trigo", "item.minecraft.white_bundle": "<PERSON>co branco", "item.minecraft.white_dye": "Tintura branca", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON> branco", "item.minecraft.wild_armor_trim_smithing_template": "Molde de ferraría", "item.minecraft.wild_armor_trim_smithing_template.new": "Deseño sal<PERSON>xe", "item.minecraft.wind_charge": "Carga de vento", "item.minecraft.witch_spawn_egg": "Ovo de invocación de meiga", "item.minecraft.wither_skeleton_spawn_egg": "Ovo de invocación de esqueleto do Wither", "item.minecraft.wither_spawn_egg": "Ovo de invocación de Wither", "item.minecraft.wolf_armor": "Armadura de lobo", "item.minecraft.wolf_spawn_egg": "Ovo de invocación de lobo", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "Sacho <PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON>co <PERSON>", "item.minecraft.wooden_shovel": "Pa de madeira", "item.minecraft.wooden_sword": "Espada de madeira", "item.minecraft.writable_book": "Libro e pluma", "item.minecraft.written_book": "Libro escrito", "item.minecraft.yellow_bundle": "Saco amarelo", "item.minecraft.yellow_dye": "Tintura amarela", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON> am<PERSON>o", "item.minecraft.zoglin_spawn_egg": "Ovo de invocación de zoglin", "item.minecraft.zombie_horse_spawn_egg": "Ovo de invocación de cabalo zombi", "item.minecraft.zombie_spawn_egg": "Ovo de invocación de zombi", "item.minecraft.zombie_villager_spawn_egg": "Ovo de invocación de zombi aldeán", "item.minecraft.zombified_piglin_spawn_egg": "Ovo de invocación de piglin zombificado", "item.modifiers.any": "Cando estea equipado:", "item.modifiers.armor": "No corpo:", "item.modifiers.body": "Cando estea equipado:", "item.modifiers.chest": "No peito:", "item.modifiers.feet": "Nos pés:", "item.modifiers.hand": "Na man:", "item.modifiers.head": "Na cabeza:", "item.modifiers.legs": "Nas pernas:", "item.modifiers.mainhand": "Na man principal:", "item.modifiers.offhand": "Na man secundaria:", "item.modifiers.saddle": "Con sela:", "item.nbt_tags": "NBT: %s etiqueta(s)", "item.op_block_warning.line1": "Aviso:", "item.op_block_warning.line2": "Uso desde obxecto pode levar á execución dun comando", "item.op_block_warning.line3": "Non o uses a non ser que saibas os contidos exactos!", "item.unbreakable": "Irrompible", "itemGroup.buildingBlocks": "Bloques de construción", "itemGroup.coloredBlocks": "Bloques de cores", "itemGroup.combat": "Combate", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Fabricación", "itemGroup.foodAndDrink": "Comida e bebida", "itemGroup.functional": "Bloques funcionais", "itemGroup.hotbar": "Barras de obxectos gardadas", "itemGroup.ingredients": "Ingredientes", "itemGroup.inventory": "Inventario de supervivencia", "itemGroup.natural": "Bloques naturais", "itemGroup.op": "Utilidades de operador", "itemGroup.redstone": "Bloques de redstone", "itemGroup.search": "Procurar", "itemGroup.spawnEggs": "Ovos de invocación", "itemGroup.tools": "Ferramentas e utilidades", "item_modifier.unknown": "Modificador de obxecto descoñecido: %s", "jigsaw_block.final_state": "Convértese en:", "jigsaw_block.generate": "Xerar", "jigsaw_block.joint.aligned": "Fixo", "jigsaw_block.joint.rollable": "Rotable", "jigsaw_block.joint_label": "Tipo de ligazón:", "jigsaw_block.keep_jigsaws": "Manter c<PERSON>", "jigsaw_block.levels": "Niveis: %s", "jigsaw_block.name": "Nome:", "jigsaw_block.placement_priority": "Prioridade de colocación:", "jigsaw_block.placement_priority.tooltip": "Cando este bloque comecocos se conecta a unha peza, esta é a orde na que esa peza se procesa para conexións na estrutura máis ampla.\n\nAs pezas procesaranse con prioridade descendente coa orde de inserción rompendo ataduras.", "jigsaw_block.pool": "Grupo albo:", "jigsaw_block.selection_priority": "Prioridade de selección:", "jigsaw_block.selection_priority.tooltip": "Cando se procesa a peza principal para conexións, esta é a orde na que este bloque comecocos intenta conectarse á súa peza de destino.\n\nOs rompecabezas procesaranse con prioridade descendente con orde aleatoria rompendo os lazos.", "jigsaw_block.target": "Nome do obxectivo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (caixa de música)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON>", "key.attack": "Atacar/Destruír", "key.back": "Cami<PERSON><PERSON> cara atr<PERSON>", "key.categories.creative": "Modo creativo", "key.categories.gameplay": "<PERSON><PERSON>", "key.categories.inventory": "Inventario", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "Movemento", "key.categories.multiplayer": "Multixogador", "key.categories.ui": "Interface do xogo", "key.chat": "<PERSON><PERSON><PERSON> chat", "key.command": "A<PERSON><PERSON> comando", "key.drop": "Soltar obxecto seleccionado", "key.forward": "<PERSON>i<PERSON><PERSON> cara adiante", "key.fullscreen": "Pantalla completa", "key.hotbar.1": "Acceso rápido 1", "key.hotbar.2": "Acceso rápido 2", "key.hotbar.3": "Acceso rápido 3", "key.hotbar.4": "Acceso rápido 4", "key.hotbar.5": "Acceso rápido 5", "key.hotbar.6": "Acceso rápido 6", "key.hotbar.7": "Acceso rápido 7", "key.hotbar.8": "Acceso rápido 8", "key.hotbar.9": "Acceso rápido 9", "key.inventory": "Abrir/Pechar inventario", "key.jump": "<PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Retroceso", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.down": "<PERSON><PERSON><PERSON> a<PERSON>", "key.keyboard.end": "Fin", "key.keyboard.enter": "Entrar", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>o", "key.keyboard.insert": "Inserir", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Entrar (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "<PERSON><PERSON><PERSON> es<PERSON>", "key.keyboard.left.alt": "Alt esquerdo", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Control esquerdo", "key.keyboard.left.shift": "<PERSON><PERSON>", "key.keyboard.left.win": "Windows esquerdo", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Bloq <PERSON>ú<PERSON>", "key.keyboard.page.down": "Av Páx", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.right": "<PERSON><PERSON><PERSON> dereita", "key.keyboard.right.alt": "Alt dereito", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Control dereito", "key.keyboard.right.shift": "<PERSON><PERSON>", "key.keyboard.right.win": "Windows dereito", "key.keyboard.scroll.lock": "Bloq <PERSON>r", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Espazo", "key.keyboard.tab": "Tabulador", "key.keyboard.unknown": "<PERSON>", "key.keyboard.up": "<PERSON><PERSON><PERSON> arriba", "key.keyboard.world.1": "Mundo 1", "key.keyboard.world.2": "Mundo 2", "key.left": "Camiñar á esquerda", "key.loadToolbarActivator": "Cargar barra de obxectos", "key.mouse": "Botón %1$s", "key.mouse.left": "<PERSON><PERSON> es<PERSON>do", "key.mouse.middle": "Botón central", "key.mouse.right": "<PERSON><PERSON> dereito", "key.pickItem": "<PERSON><PERSON> bloque", "key.playerlist": "Listaxe de xogadores", "key.quickActions": "Accións rápidas", "key.right": "Camiñar á dereita", "key.saveToolbarActivator": "Gardar a barra de obxectos", "key.screenshot": "Tirar captura de pantalla", "key.smoothCamera": "Mudar a cámara cinemática", "key.sneak": "Agocharse", "key.socialInteractions": "Pantalla de interaccións sociais", "key.spectatorOutlines": "Destacar <PERSON> (espectadores)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "<PERSON><PERSON> obxecto coa man secundaria", "key.togglePerspective": "Mudar perspectiva", "key.use": "Usar obxecto/Colocar bloque", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Comunidade", "known_server_link.community_guidelines": "Normas da comunidade", "known_server_link.feedback": "Opinións", "known_server_link.forums": "For<PERSON>", "known_server_link.news": "Novidades", "known_server_link.report_bug": "Informar erro do servidor", "known_server_link.status": "Estado", "known_server_link.support": "Soporte", "known_server_link.website": "Páxina web", "lanServer.otherPlayers": "Axustes para outros xogadores", "lanServer.port": "Número de porto", "lanServer.port.invalid": "Porto non válido.\nDeixa a caixa de edición baleira ou introduce un número entre 1024 e 65535.", "lanServer.port.invalid.new": "Porto non válido.\nDeixa a caixa de edición baleira ou escolle un número diferente entre %s e %s.", "lanServer.port.unavailable": "Porto non dispoñible.\nDeixa a caixa de edición baleira ou escolle un número diferente entre 1024 e 65535.", "lanServer.port.unavailable.new": "Porto non dispoñible.\nDeixa a caixa de edición baleira ou escolle un número diferente entre %s e %s.", "lanServer.scanning": "Escaneando partidas na túa rede local", "lanServer.start": "Comezar mundo LAN", "lanServer.title": "Mundo LAN", "language.code": "glg_ES", "language.name": "Galego", "language.region": "Galicia / Galiza", "lectern.take_book": "Recoller libro", "loading.progress": "%s%%", "mco.account.privacy.info": "Ler máis sobre Mojang e as leis de privacidade", "mco.account.privacy.info.button": "Ler máis sobre GDPR", "mco.account.privacy.information": "Mojang implementa certos procedementos para axudar a protexer ós menores e á súa privacidade, os cales consisten en cumprir coa Lei de Proteción da Privacidade Infantil en Internet (COPPA) e o Reglamento Xeral de Proteción de Datos (RGPD).\n\nDebes ter o consentimento dos teus pais antes de poder ter acceso á túa conta de Realms.\n\nSe tes unha conta de Minecraft antiga (inicias sesión co teu nome de usuario), precisarás migrar a túa conta a unha de Mojang para poder acceder a Realms.", "mco.account.privacyinfo": "Mojang implementa certos procedementos para axudar a protexer aos menores e á súa privacidade, os cales consisten en cumprir coa Lei de Proteción da Privacidade Infantil na internet (COPPA) e o Regulamento Xeral de Proteción de Datos (RGPD).\n\nTes que ter o consentimento dos teus pais antes de poder ter acceso á túa conta do Realms.\n\nSe tes unha conta do Minecraft antiga (inicia sesión co teu nome de usuario), precisarás migrar a túa conta a unha de Mojang para poder acceder ao Realms.", "mco.account.update": "Actualizar conta", "mco.activity.noactivity": "Sen actividade para os últimos %s día(s)", "mco.activity.title": "Actividade dos xogadores", "mco.backup.button.download": "Baixar a última", "mco.backup.button.reset": "Reiniciar mundo", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "Subir mundo", "mco.backup.changes.tooltip": "Modificacións", "mco.backup.entry": "Copia(s) de seguridade (%s)", "mco.backup.entry.description": "Descrición", "mco.backup.entry.enabledPack": "Activouse o paquete", "mco.backup.entry.gameDifficulty": "Dificultade do xogo", "mco.backup.entry.gameMode": "<PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Versión do servidor do xogo", "mco.backup.entry.name": "Nome", "mco.backup.entry.seed": "Semente", "mco.backup.entry.templateName": "Nome de patrón", "mco.backup.entry.undefined": "Cambio non identificado", "mco.backup.entry.uploaded": "Subido", "mco.backup.entry.worldType": "Tipo de mundo", "mco.backup.generate.world": "Xerar mundo", "mco.backup.info.title": "Cambios dende a última copia", "mco.backup.narration": "Copia dende %s", "mco.backup.nobackups": "Este realm non posúe ningunha copia.", "mco.backup.restoring": "Restaurando o teu realm", "mco.backup.unknown": "DESCOÑECIDA", "mco.brokenworld.download": "Baixar", "mco.brokenworld.downloaded": "Bai<PERSON><PERSON>", "mco.brokenworld.message.line1": "Por favor, restabelece ou escolle outro mundo.", "mco.brokenworld.message.line2": "<PERSON><PERSON> podes escoller baixar o mundo para un xogador.", "mco.brokenworld.minigame.title": "O minixogo xa non é compatible", "mco.brokenworld.nonowner.error": "Agarda a que o dono do realm restabeleza o mundo", "mco.brokenworld.nonowner.title": "Mundo sen actualizar", "mco.brokenworld.play": "Xogar", "mco.brokenworld.reset": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.title": "O teu mundo actual xa non é compatíbel", "mco.client.incompatible.msg.line1": "O teu cliente non é compatible co Realms.", "mco.client.incompatible.msg.line2": "Por favor, emprega a versión máis recente do Minecraft.", "mco.client.incompatible.msg.line3": "O Realms non é compatible coas versións de proba (snapshots).", "mco.client.incompatible.title": "Cliente incompatible!", "mco.client.outdated.stable.version": "A túa versión de cliente (%s) non é compatible con Realms.\n\nPor favor, actualiza a unha versión máis recente de Minecraft.", "mco.client.unsupported.snapshot.version": "A túa versión de cliente (%s) non é compatible con Realms.\n\nRealms non esta dispoñible para esta versión de proba.", "mco.compatibility.downgrade": "Desactualizar", "mco.compatibility.downgrade.description": "Este mundo foi xogado por última vez na versión %s e estás na %s. Des-actualizar un mundo pode causar corrupción; non podemos garantir que cargue ou funcione.\n\nGardarase un respaldo do teu mundo en \"Respaldos se mundos\". Restaura o teu mundo se é necesario.", "mco.compatibility.incompatible.popup.title": "Versión incompatible", "mco.compatibility.incompatible.releaseType.popup.message": "O mundo no que estas intentado entrar é incompatible coa versión na que estás.", "mco.compatibility.incompatible.series.popup.message": "Este mundo xogouse por última vez na versión %s; estás na versión %s.\n\nEstas versións non son compatibles entre si. É necesario un mundo novo para xogar nesta versión.", "mco.compatibility.unverifiable.message": "Non se puido verificar a última versión na que se xogou este mundo. Se o mundo se actualiza ou baixa, crearase automaticamente unha copia de seguridade e gardarase en \"Respaldos de mundos\".", "mco.compatibility.unverifiable.title": "Compatibilidade non verificable", "mco.compatibility.upgrade": "Actualizar", "mco.compatibility.upgrade.description": "Este mundo foi xogado por ultima vez na version %s; ti andas na version %s.\n\nO respaldo do mundo será gardada en \"Respaldo de mundos\".\n\nRestaura o teu mundo se é necesario.", "mco.compatibility.upgrade.friend.description": "Este mundo foi xogado por ultima vez na version %s; ti andas na version %s.\n\nO respaldo do mundo será gardada en \"Respaldo de mundos\".\n\nO dono do Realm pode restaurar o mundo se o require.", "mco.compatibility.upgrade.title": "Realmente queres actualizar este mundo?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "Información desactivada de xeito temporal", "mco.configure.world.backup": "Copias do mundo", "mco.configure.world.buttons.activity": "Actividade do xogador", "mco.configure.world.buttons.close": "Pechar Realm temporalmente", "mco.configure.world.buttons.delete": "Eliminar", "mco.configure.world.buttons.done": "<PERSON><PERSON>", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "Máis opcións", "mco.configure.world.buttons.newworld": "Novo mundo", "mco.configure.world.buttons.open": "<PERSON>abrir <PERSON>", "mco.configure.world.buttons.options": "Opcións do mundo", "mco.configure.world.buttons.players": "Xogadores", "mco.configure.world.buttons.region_preference": "Seleccionar rexión...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON><PERSON><PERSON> mundo", "mco.configure.world.buttons.save": "Gardar", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Subscrición", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON> de minix<PERSON>", "mco.configure.world.close.question.line1": "O teu realm deixará de estar dispoñíbel.", "mco.configure.world.close.question.line2": "Se<PERSON>ro que queres continuar?", "mco.configure.world.close.question.title": "Precisas facer cambios sen interrupcións?", "mco.configure.world.closing": "Pechando o realm...", "mco.configure.world.commandBlocks": "Bloques de comandos", "mco.configure.world.delete.button": "Eliminar Realm", "mco.configure.world.delete.question.line1": "O teu realm será eliminado de xeito permanente", "mco.configure.world.delete.question.line2": "Se<PERSON>ro que queres continuar?", "mco.configure.world.description": "Descrición do realm", "mco.configure.world.edit.slot.name": "Nome do mundo", "mco.configure.world.edit.subscreen.adventuremap": "Algúns axustes están desactivados porque o teu mundo actual é de aventura", "mco.configure.world.edit.subscreen.experience": "Algúns axustes están desactivados porque o teu mundo actual é de experiencia", "mco.configure.world.edit.subscreen.inspiration": "Algúns axustes están desactivados pois o teu mundo actual é de inspiración", "mco.configure.world.forceGameMode": "<PERSON><PERSON> modo de xogo", "mco.configure.world.invite.narration": "Tes novos convites: %s", "mco.configure.world.invite.profile.name": "Nome", "mco.configure.world.invited": "Convidado", "mco.configure.world.invited.number": "Convidados (%s)", "mco.configure.world.invites.normal.tooltip": "Usuario normal", "mco.configure.world.invites.ops.tooltip": "Operador", "mco.configure.world.invites.remove.tooltip": "Eliminar", "mco.configure.world.leave.question.line1": "Se saes deste Realm non poderás voltar agás que che conviden novamente", "mco.configure.world.leave.question.line2": "Se<PERSON>ro que queres continuar?", "mco.configure.world.loading": "Cargando Realm", "mco.configure.world.location": "Localización", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nome do Realm", "mco.configure.world.opening": "Abrindo o Realm...", "mco.configure.world.players.error": "Non existe ningún xogador con este nome", "mco.configure.world.players.inviting": "Convid<PERSON><PERSON>...", "mco.configure.world.players.title": "Xogadores", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Preferencia de rexión", "mco.configure.world.region_preference.title": "Selección de rexión", "mco.configure.world.reset.question.line1": "O teu mundo será rexenerado e o actual perderase", "mco.configure.world.reset.question.line2": "Se<PERSON>ro que queres continuar?", "mco.configure.world.resourcepack.question": "Necesita un paquete de texturas personalizado para xogar neste realm\n\nQuere descargalo e xogar?", "mco.configure.world.resourcepack.question.line1": "Necesitas un paquete de recursos personalizado para xogar", "mco.configure.world.resourcepack.question.line2": "Queres baixalo de xeito automático e instalalo para xogar?", "mco.configure.world.restore.download.question.line1": "O mundo será baixado e engadido aos teus mundos de un xogador.", "mco.configure.world.restore.download.question.line2": "Desexas continuar?", "mco.configure.world.restore.question.line1": "O teu mundo será restabelecido á data '%s' (%s)", "mco.configure.world.restore.question.line2": "Se<PERSON>ro que queres continuar?", "mco.configure.world.settings.expired": "Non podes editar os axustes dun Realm expirado", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Mundo %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "O teu realm será mudado a outro mundo", "mco.configure.world.slot.switch.question.line2": "Se<PERSON>ro que queres continuar?", "mco.configure.world.slot.tooltip": "Mudar ao mundo", "mco.configure.world.slot.tooltip.active": "Entrar", "mco.configure.world.slot.tooltip.minigame": "Mudar ao minixogo", "mco.configure.world.spawnAnimals": "Invocar animais", "mco.configure.world.spawnMonsters": "Invocar monstros", "mco.configure.world.spawnNPCs": "Invocar NPC", "mco.configure.world.spawnProtection": "Protección da área de aparición", "mco.configure.world.spawn_toggle.message": "Ao desactivar esta opción eliminaranse todas as entidades existentes dese tipo", "mco.configure.world.spawn_toggle.message.npc": "Desactivar esta opción eliminará todas as entidades dese tipo, como aldeáns", "mco.configure.world.spawn_toggle.title": "Aviso!", "mco.configure.world.status": "Estado", "mco.configure.world.subscription.day": "día", "mco.configure.world.subscription.days": "días", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Ampliar subscrición", "mco.configure.world.subscription.less_than_a_day": "Menos dun día", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "meses", "mco.configure.world.subscription.recurring.daysleft": "Renovado de xeito automático en", "mco.configure.world.subscription.recurring.info": "Os cambios realizados na subscrición a Reinos, como o tempo de apilamento ou a desactivación da facturación recorrente, non se reflectirán ata a próxima data de factura.", "mco.configure.world.subscription.remaining.days": "%1$s día(s)", "mco.configure.world.subscription.remaining.months": "%1$s mes(es)", "mco.configure.world.subscription.remaining.months.days": "%1$s mes(es), %2$s día(s)", "mco.configure.world.subscription.start": "<PERSON> de comezo", "mco.configure.world.subscription.tab": "Subscrición", "mco.configure.world.subscription.timeleft": "Tempo restante", "mco.configure.world.subscription.title": "A túa subscrición", "mco.configure.world.subscription.unknown": "Descoñecida", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> mundo", "mco.configure.world.switch.slot.subtitle": "Este mundo está baleiro, escolle como crealo", "mco.configure.world.title": "Configurar Realm:", "mco.configure.world.uninvite.player": "Se<PERSON><PERSON> que queres desconvidar a '%s'?", "mco.configure.world.uninvite.question": "Se<PERSON>ro que queres desbotar o convite de", "mco.configure.worlds.title": "Mundos", "mco.connect.authorizing": "Iniciando se<PERSON>...", "mco.connect.connecting": "Conectando ao Realm...", "mco.connect.failed": "Erro ao conectar ao realm", "mco.connect.region": "Rexión do servidor: %s", "mco.connect.success": "<PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "Tes que inserir un nome!", "mco.create.world.failed": "Erro ao crear o mundo!", "mco.create.world.reset.title": "Creando mundo...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "De xeito opcional, escolle un mundo para pólo no teu novo realm", "mco.create.world.wait": "Creando Realm...", "mco.download.cancelled": "Descarga cancelada", "mco.download.confirmation.line1": "O mundo que vas baixar ten máis de %s", "mco.download.confirmation.line2": "Non poderás subir novamente este mundo ao teu realm", "mco.download.confirmation.oversized": "O mundo que vai descarga e mais grande ca %s\n\nVostede non poderá subir este mundo ao seu realm de novo", "mco.download.done": "<PERSON><PERSON><PERSON> rematada", "mco.download.downloading": "<PERSON><PERSON><PERSON>", "mco.download.extracting": "Extraendo", "mco.download.failed": "<PERSON><PERSON><PERSON> fallida", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparando <PERSON>", "mco.download.resourcePack.fail": "Erro ó descargar o paquete de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Baixando o mundo máis recente", "mco.error.invalid.session.message": "Tenta reiniciar o Minecraft", "mco.error.invalid.session.title": "A sesión non é válida", "mco.errorMessage.6001": "Cliente desactualizado", "mco.errorMessage.6002": "Os termos do servizo non foron aceptados", "mco.errorMessage.6003": "O límite de descargas foi atinxido", "mco.errorMessage.6004": "O límite de subidas foi atinxido", "mco.errorMessage.6005": "Mundo bloqueado", "mco.errorMessage.6006": "Mundo desactualizado", "mco.errorMessage.6007": "O usuario está en demasiados realms", "mco.errorMessage.6008": "Nome de realm non válido", "mco.errorMessage.6009": "Descrición do realm non válida", "mco.errorMessage.connectionFailure": "Produciuse un erro, téntao máis tarde.", "mco.errorMessage.generic": "Ocorreu un erro: ", "mco.errorMessage.initialize.failed": "Fallo ao iniciar Realm", "mco.errorMessage.noDetails": "Non se proporcionaron detalles do error", "mco.errorMessage.realmsService": "Ocorreu un erro (%s):", "mco.errorMessage.realmsService.configurationError": "Produciuse un erro inesperado ao tentar editar as opcións do mundo", "mco.errorMessage.realmsService.connectivity": "Non se pode conectar ao realm: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Non se puido comprobar a versión compatible, obtivo a resposta: %s", "mco.errorMessage.retry": "Reintentando a operación", "mco.errorMessage.serviceBusy": "O servizo de Realms atópase ocupado neste intre.\nPor favor, tenta conectar outra vez ao teu realm nuns minutos.", "mco.gui.button": "Botón", "mco.gui.ok": "Vale", "mco.info": "Info!", "mco.invited.player.narration": "Xogador convidado %s", "mco.invites.button.accept": "Aceptar", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "Non hai convites pendentes!", "mco.invites.pending": "Novos convites!", "mco.invites.title": "Convites pendentes", "mco.minigame.world.changeButton": "Seleccionar outro minixogo", "mco.minigame.world.info.line1": "Isto substituirá de xeito temporal o teu mundo por un minixogo!", "mco.minigame.world.info.line2": "Podes voltar máis tarde ao teu mundo orixinal sen perder nada.", "mco.minigame.world.noSelection": "Por favor, escolle un minixogo", "mco.minigame.world.restore": "Rematando minixogo...", "mco.minigame.world.restore.question.line1": "O minixogo rematará e o teu Realm será restaurado.", "mco.minigame.world.restore.question.line2": "Se<PERSON>ro que queres continuar?", "mco.minigame.world.selected": "<PERSON><PERSON>ogo se<PERSON>:", "mco.minigame.world.slot.screen.title": "Mudando mundo...", "mco.minigame.world.startButton": "<PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Iniciando minixogo...", "mco.minigame.world.stopButton": "<PERSON><PERSON><PERSON> minixogo", "mco.minigame.world.switch.new": "Seleccionar outro minixogo?", "mco.minigame.world.switch.title": "Cambiar de minixogo", "mco.minigame.world.title": "Mudar realm a minixogo", "mco.news": "Novas do Realms", "mco.notification.dismiss": "Desestimación", "mco.notification.transferSubscription.buttonText": "Transferir agora", "mco.notification.transferSubscription.message": "As subscricións de Java Realms móvense á tenda de Microsoft. Non deixes que a túa subscrición caduque!\nTransfire agora e obtén 30 días de Realms gratis.\nVai a Perfil en minecraft.net para transferir a túa subscrición.", "mco.notification.visitUrl.buttonText.default": "Abrir ligazón", "mco.notification.visitUrl.message.default": "Por favor visita a seguinte ligazón", "mco.onlinePlayers": "Xogadores en liña", "mco.play.button.realm.closed": "Realm pechado", "mco.question": "Pregunta", "mco.reset.world.adventure": "Aventuras", "mco.reset.world.experience": "Experiencias", "mco.reset.world.generate": "Novo mundo", "mco.reset.world.inspiration": "Inspiración", "mco.reset.world.resetting.screen.title": "Restabelecendo mundo...", "mco.reset.world.seed": "Semente (Opcional)", "mco.reset.world.template": "Modelos de mundos", "mco.reset.world.title": "<PERSON><PERSON><PERSON><PERSON> mundo", "mco.reset.world.upload": "Subir mundo", "mco.reset.world.warning": "Isto substituirá o teu mundo actual do teu realm", "mco.selectServer.buy": "Mercar un Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Realm desactivado", "mco.selectServer.closeserver": "Pechar Realm", "mco.selectServer.configure": "Configurar realm", "mco.selectServer.configureRealm": "Configurar Realm", "mco.selectServer.create": "Crear Realm", "mco.selectServer.create.subtitle": "Seleccione que palabra colocar para o teu novo realm", "mco.selectServer.expired": "Realm expirado", "mco.selectServer.expiredList": "A túa subscrición expirou", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Subscribirse", "mco.selectServer.expiredTrial": "A túa proba rematou", "mco.selectServer.expires.day": "<PERSON><PERSON> nun día", "mco.selectServer.expires.days": "Remata en %s días", "mco.selectServer.expires.soon": "<PERSON><PERSON> pronto", "mco.selectServer.leave": "Saír do <PERSON>", "mco.selectServer.loading": "Cargando listaxe de Realms", "mco.selectServer.mapOnlySupportedForVersion": "Este mapa non é compatíbel na %s", "mco.selectServer.minigame": "Minixogo:", "mco.selectServer.minigameName": "Minixogo: %s", "mco.selectServer.minigameNotSupportedInVersion": "Non se pode xogar este minixogo na %s", "mco.selectServer.noRealms": "Parece que non tes un Realm. Engade un Realm para xogar cos teus amigos.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Realm aberto", "mco.selectServer.openserver": "Abrir Realm", "mco.selectServer.play": "Xogar", "mco.selectServer.popup": "O Realms é un xeito sinxelo e segura de desfrutar un mundo en liña do Minecraft con até dez amizades ao mesmo tempo. Ten moitos minixogos e mundos personalizados! Só paga o dono do realm.", "mco.selectServer.purchase": "Engadir Realm", "mco.selectServer.trial": "Obter unha proba!", "mco.selectServer.uninitialized": "Preme aquí para crear o teu novo realm!", "mco.snapshot.createSnapshotPopup.text": "Estás a piques de crear un Realm en versión de proba gratuíto que se asociará coa túa subscrición de Realm pagada. Este novo Realm en versión de proba estará accesible mentres a subscrición de pago estea activa. O teu Realm pagado non se verá afectado.", "mco.snapshot.createSnapshotPopup.title": "Crear un Realm en versión de proba?", "mco.snapshot.creating": "Creando un Realm en versión de proba...", "mco.snapshot.description": "Emparellado con \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Debes estar na versión %s para unirte a este Realm", "mco.snapshot.friendsRealm.upgrade": "%s Require actualizar o seu Realm antes de poder xogar con esta versión", "mco.snapshot.paired": "Este Realm en versión de proba está emparellado con \"%s\"", "mco.snapshot.parent.tooltip": "Usa a última versión de Minecraft para xogar neste Realm", "mco.snapshot.start": "Inicia Real en versión de proba gratuíto", "mco.snapshot.subscription.info": "Este é un Realm en versión de proba que está vinculado á subscrición do teu Realm '%s'. Permanecerá activo mentres estea o seu Realm emparellado.", "mco.snapshot.tooltip": "Usa o Realms en versión de proba para ver un adianto das próximas versións do Minecraft, que poden incluír novas funcións e outros cambios.\n\nPodes atopar os teus Realms normais na versión final do xogo.", "mco.snapshotRealmsPopup.message": "Realms agora dispoñible en versións de proba empezando pola 23w41a. Cada subscritor de Realms ven cun Realm en versión de proba gratuíto que vai separado do teu mundo de Realm normal en Java!", "mco.snapshotRealmsPopup.title": "Realms agora dispoñible nas versións de proba", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON>", "mco.template.button.publisher": "<PERSON><PERSON><PERSON>", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Modelo do mundo", "mco.template.info.tooltip": "Páxina web do creador", "mco.template.name": "<PERSON><PERSON>", "mco.template.select.failure": "Non se puido obter a listaxe de contido para esta categoría. Comproba a túa conexión á internet ou téntao máis tarde.", "mco.template.select.narrate.authors": "Autores: %s", "mco.template.select.narrate.version": "versión %s", "mco.template.select.none": "Vaites! Semella que a categoría está baleira nestes intres. Téntao nun intre, ou se es un creador, %s.", "mco.template.select.none.linkTitle": "pensa en suxerir algunha idea", "mco.template.title": "Modelos de mundos", "mco.template.title.minigame": "Minixogos", "mco.template.trailer.tooltip": "Tráiler do mapa", "mco.terms.buttons.agree": "Concordo", "mco.terms.buttons.disagree": "Non concordo", "mco.terms.sentence.1": "Concordo co Minecraft Realms", "mco.terms.sentence.2": "Termos do servizo", "mco.terms.title": "Termos de servizo do Realms", "mco.time.daysAgo": "Hai %1$s día(s)", "mco.time.hoursAgo": "Hai %1$s hora(s)", "mco.time.minutesAgo": "Hai %1$s minuto(s)", "mco.time.now": "agora mesmo", "mco.time.secondsAgo": "Hai %1$s segundo(s)", "mco.trial.message.line1": "Queres obter o teu propio Realm?", "mco.trial.message.line2": "Preme aquí para máis información!", "mco.upload.button.name": "Subir", "mco.upload.cancelled": "Subida cancelada", "mco.upload.close.failure": "Non se pode pechar o teu realm, téntao máis tarde", "mco.upload.done": "Subida rematada", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "A subida fallou! (%s)", "mco.upload.failed.too_big.description": "O mundo seleccionado é moi grande. O tamaño máximo permitido é %s.", "mco.upload.failed.too_big.title": "Mundo demasiado grande", "mco.upload.hardcore": "Os mundos extremos non poden ser subidos!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparando o teu mundo", "mco.upload.select.world.none": "Non se atoparon mundos dun xogador!", "mco.upload.select.world.subtitle": "Selecciona un mundo dun xogador para subir", "mco.upload.select.world.title": "Subir mundo", "mco.upload.size.failure.line1": "'%s' é moi grande!", "mco.upload.size.failure.line2": "Ocupa %s. O tamaño máximo permitido é %s.", "mco.upload.uploading": "Subindo '%s'", "mco.upload.verifying": "Verificando o teu mundo", "mco.version": "Versión: %s", "mco.warning": "Atención!", "mco.worldSlot.minigame": "Minixogo", "menu.custom_options": "Opcións personalizadas...", "menu.custom_options.title": "Opcións personalizadas", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Desconectar", "menu.feedback": "Opinións...", "menu.feedback.title": "Opinións", "menu.game": "Menú do xogo", "menu.modded": " (Modificado)", "menu.multiplayer": "Multixogador", "menu.online": "Minecraft Realms", "menu.options": "Opcións...", "menu.paused": "<PERSON><PERSON> p<PERSON>", "menu.playdemo": "Xogar mundo de demostración", "menu.playerReporting": "Reporte de xogador", "menu.preparingSpawn": "Preparando a área de aparición: %s%%", "menu.quick_actions": "Accións rápidas...", "menu.quick_actions.title": "Accións rápidas", "menu.quit": "Saír do xogo", "menu.reportBugs": "Informar de erros", "menu.resetdemo": "Reiniciar mundo de demostración", "menu.returnToGame": "Voltar á partida", "menu.returnToMenu": "Gardar e voltar á pantalla principal", "menu.savingChunks": "Gardando chunks", "menu.savingLevel": "Gardando mundo", "menu.sendFeedback": "Suxestión<PERSON>", "menu.server_links": "Ligazóns do servidor...", "menu.server_links.title": "Ligazóns do servidor", "menu.shareToLan": "Abrir en LAN", "menu.singleplayer": "Un xogador", "menu.working": "Procesando...", "merchant.deprecated": "Os aldeáns poden reabastecerse ata dúas veces por día.", "merchant.level.1": "Novato", "merchant.level.2": "Aprendiz", "merchant.level.3": "Experimentado", "merchant.level.4": "Experto", "merchant.level.5": "Mestre", "merchant.title": "%s - %s", "merchant.trades": "Trocos", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Preme %1$s para baixarte", "multiplayer.applyingPack": "Aplicando paquete de recursos", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirmar execución do comando", "multiplayer.disconnect.authservers_down": "Os servidores de autenticación non funcionan. Téntao máis tarde, sentímolo!", "multiplayer.disconnect.bad_chat_index": "Mensaxe do chat perdida ou pedida de novo do servidor", "multiplayer.disconnect.banned": "Bloqueáronte neste servidor", "multiplayer.disconnect.banned.expiration": "\nO teu bloqueo será eliminado o %s", "multiplayer.disconnect.banned.reason": "Esta conta está baneada neste servidor.\nRazón: %s", "multiplayer.disconnect.banned_ip.expiration": "\nO teu bloqueo será eliminado o %s", "multiplayer.disconnect.banned_ip.reason": "A tua IP foi baneada neste servidor.\nMotivo: %s", "multiplayer.disconnect.chat_validation_failed": "Falla na validación da mensaxe da sala de conversa", "multiplayer.disconnect.duplicate_login": "Iniciaches sesión dende outro sitio", "multiplayer.disconnect.expired_public_key": "Clave pública do perfil expirada. Comproba que a hora do sistema está sincronizada e tenta reiniciar o xogo.", "multiplayer.disconnect.flying": "Non está permitido voar neste servidor", "multiplayer.disconnect.generic": "Desconectado", "multiplayer.disconnect.idling": "Estiveches moito tempo inactivo!", "multiplayer.disconnect.illegal_characters": "Caracteres non permitidos no chat", "multiplayer.disconnect.incompatible": "Cliente incompatible! Emprega %s", "multiplayer.disconnect.invalid_entity_attacked": "Intentaches atacar unha entidade non válida", "multiplayer.disconnect.invalid_packet": "O servidor enviou un paquete inválido", "multiplayer.disconnect.invalid_player_data": "Datos do xogador inválidos", "multiplayer.disconnect.invalid_player_movement": "Detectouse un movemento non válido do xogador", "multiplayer.disconnect.invalid_public_key_signature": "Sinatura non válida para a chave pública do perfil.\nTenta reiniciar o xogo.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firma da chave pública de perfil non válida.\nProba a reiniciar o xogo.", "multiplayer.disconnect.invalid_vehicle_movement": "Detectóuse un movemento non válido dun vehículo", "multiplayer.disconnect.ip_banned": "A túa IP foi bloqueada neste servidor", "multiplayer.disconnect.kicked": "Botado/a por un administrador", "multiplayer.disconnect.missing_tags": "Conxunto incompleto de etiquetas recibidas polo servidor.\nPor favor, contacta co operador do servidor.", "multiplayer.disconnect.name_taken": "Xa hai al<PERSON><PERSON> xogando con este nome", "multiplayer.disconnect.not_whitelisted": "Non estás na lista branca deste servidor!", "multiplayer.disconnect.out_of_order_chat": "Paquete de mensaxes recibido fóra de orde. Cambiou a hora do seu sistema?", "multiplayer.disconnect.outdated_client": "Cliente incompatíbel! Por favor emprega %s", "multiplayer.disconnect.outdated_server": "Cliente incompatíbel! Por favor emprega %s", "multiplayer.disconnect.server_full": "O servidor está cheo!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> p<PERSON>ado", "multiplayer.disconnect.slow_login": "Tardouse moito en iniciar sesión", "multiplayer.disconnect.too_many_pending_chats": "De<PERSON><PERSON>das mensaxes na sala de conversa sen recoñecer", "multiplayer.disconnect.transfers_disabled": "O servidor non acepta transferencias", "multiplayer.disconnect.unexpected_query_response": "Enviáronse datos descoñecidos dende o cliente", "multiplayer.disconnect.unsigned_chat": "Recibiuse un paquete de chat sen firma ou cunha firma inválida.", "multiplayer.disconnect.unverified_username": "Non se puido verificar o nome de usuario!", "multiplayer.downloadingStats": "Obtendo estatísticas...", "multiplayer.downloadingTerrain": "Cargando terreo...", "multiplayer.lan.server_found": "Novo servidor atopado: %s", "multiplayer.message_not_delivered": "Non se puido enviar a mensaxe, revisa os rexistros do servidor: %s", "multiplayer.player.joined": "%s uniuse á partida", "multiplayer.player.joined.renamed": "%s (antes coñecido como %s) uniuse á partida", "multiplayer.player.left": "%s saíu da partida", "multiplayer.player.list.hp": "%s PV", "multiplayer.player.list.narration": "Xogadores en liña: %s", "multiplayer.requiredTexturePrompt.disconnect": "O servidor require un paquete de recursos personalizado", "multiplayer.requiredTexturePrompt.line1": "Este servidor require o emprego dun paquete de recursos personalizado.", "multiplayer.requiredTexturePrompt.line2": "Rexeitar este paquete de recursos personalizado desconectarate deste servidor.", "multiplayer.socialInteractions.not_available": "As interaccións sociais só están dispoñibles en mundos multixogador", "multiplayer.status.and_more": "... e %s máis ...", "multiplayer.status.cancelled": "Cancelado", "multiplayer.status.cannot_connect": "Non se pode conectar ao servidor", "multiplayer.status.cannot_resolve": "Erro ao procurar o nome do host", "multiplayer.status.finished": "Conexión finalizada", "multiplayer.status.incompatible": "Versión incompatible!", "multiplayer.status.motd.narration": "Mensaxe do día: %s", "multiplayer.status.no_connection": "(sen conexión)", "multiplayer.status.old": "Desactualizado", "multiplayer.status.online": "En liña", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Latencia de %s milisegundos", "multiplayer.status.pinging": "Comprobando conexión...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s de %s xogadores en liña", "multiplayer.status.quitting": "Desconectando", "multiplayer.status.request_handled": "Recibiuse a solicitude de estado", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Recibiuse un estado non solicitado", "multiplayer.status.version.narration": "Versión do servidor: %s", "multiplayer.stopSleeping": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "Non foi posíbel aplicar o paquete de recursos do servidor", "multiplayer.texturePrompt.failure.line2": "Calquera funcionalidade que requira o uso de recursos personalizados pode non funcionar de xeito correcto", "multiplayer.texturePrompt.line1": "Este servidor recomenda o uso dun paquete de recursos personalizado.", "multiplayer.texturePrompt.line2": "Queres baixalo e instalalo de xeito automático?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMensaxe do servidor:\n%s", "multiplayer.title": "Multixogador", "multiplayer.unsecureserver.toast": "As mensaxes enviadas neste servidor pódense modificar e poden non reflectir a mensaxe orixinal", "multiplayer.unsecureserver.toast.title": "As mensaxes do chat non se poden verificar", "multiplayerWarning.check": "Non amosar esta pantalla novamente", "multiplayerWarning.header": "Atención: Xogo en liña de terceiros", "multiplayerWarning.message": "Coidado: O xogo en liña é ofrecido por servidores de terceiros os cales non son propiedade de Mojang Studios nin de Microsoft nin están administrados nin supervisados por eles. Mentres xogas en liña podes estar exposto a menxases sen moderar ou outros tipo de contido xerado pola comunidade que pode non ser apropiado para todos os usuarios.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> On <PERSON>", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygene", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botón: %s", "narration.button.usage.focused": "Preme Entrar para activar", "narration.button.usage.hovered": "Clic esquerdo para activar", "narration.checkbox": "Caixa: %s", "narration.checkbox.usage.focused": "Prema Entrar para cambiar", "narration.checkbox.usage.hovered": "Fai clic esquerdo para mudar", "narration.component_list.usage": "Preme Tabulador para navegar ao seguinte elemento", "narration.cycle_button.usage.focused": "Prema Entrar para cambiar a %s", "narration.cycle_button.usage.hovered": "Fai clic esquerdo para cambiar a %s", "narration.edit_box": "Caixa de edición: %s", "narration.item": "Obxecto: %s", "narration.recipe": "Receita para %s", "narration.recipe.usage": "Clic esquerdo para seleccionar", "narration.recipe.usage.more": "Fai clic dereito para amosar máis receitas", "narration.selection.usage": "Prema os botóns arriba e abaixo para moverte a outra entrada", "narration.slider.usage.focused": "Preme os botóns dereita e esquerda do teclado para mudar o valor", "narration.slider.usage.hovered": "Arrastra o deslizador para cambiar o valor", "narration.suggestion": "Seleccionouse a suxerencia %s de %s: %s", "narration.suggestion.tooltip": "Seleccionouse a suxerencia %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Aprete tabulador para ir á seguinte suxestión", "narration.suggestion.usage.cycle.hidable": "Aperte Tabulador para usar suxestións, ou aperte Escape para saír delas", "narration.suggestion.usage.fill.fixed": "Aperte Tabulador para usar suxestións", "narration.suggestion.usage.fill.hidable": "Aperte Tabulador para usar suxestións, ou aperte Escape para saír delas", "narration.tab_navigation.usage": "Prema Ctrl e Tab para cambiar entre pestanas", "narrator.button.accessibility": "Accesibilidade", "narrator.button.difficulty_lock": "Bloquear dificultade", "narrator.button.difficulty_lock.locked": "Bloqueada", "narrator.button.difficulty_lock.unlocked": "Desbloqueada", "narrator.button.language": "<PERSON><PERSON>", "narrator.controls.bound": "%s está asignado a %s", "narrator.controls.reset": "Devolver %s ao seu valor por defecto", "narrator.controls.unbound": "%s non está asignado", "narrator.joining": "Entrando", "narrator.loading": "Cargando: %s", "narrator.loading.done": "<PERSON><PERSON>", "narrator.position.list": "Seleccionouse a fila da lista %s de %s", "narrator.position.object_list": "Seleccionouse o elemento da fila %s de %s", "narrator.position.screen": "Elemento %s de %s en pantalla", "narrator.position.tab": "Pestana seleccionada %s fóra de %s", "narrator.ready_to_play": "Listo para xogar", "narrator.screen.title": "<PERSON><PERSON> principal", "narrator.screen.usage": "Usa o cursor ou o Tabulador para seleccionar o elemento", "narrator.select": "Seleccionouse: %s", "narrator.select.world": "Seleccionouse %s, xogado por última vez %s, %s, %s, versión: %s", "narrator.select.world_info": "Escollido %s, última vez xogado: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> activado", "optimizeWorld.confirm.description": "Isto tratará de optimizar o teu mundo asegurándose de que todos os datos se garden no formato de xogo máis recente. Isto podería tomar moito tempo, dependendo do tamaño do teu mundo. Unha vez terminado o teu mundo debería funcionar aínda máis rápido, pero xa non será compatible con versións antigas. Estás seguro de querer facelo?", "optimizeWorld.confirm.proceed": "Crea unha copia de seguridade e optimiza", "optimizeWorld.confirm.title": "<PERSON>timi<PERSON> mundo", "optimizeWorld.info.converted": "Chunks actualizados: %s", "optimizeWorld.info.skipped": "Chunks omitidos: %s", "optimizeWorld.info.total": "Chunks totales: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Contabilizando chunks...", "optimizeWorld.stage.failed": "Erro! :(", "optimizeWorld.stage.finished": "Rematando...", "optimizeWorld.stage.finished.chunks": "Finalizando optimización de chunks...", "optimizeWorld.stage.finished.entities": "Rematando optimización de entidades...", "optimizeWorld.stage.finished.poi": "Rematando actualización dos puntos de interese...", "optimizeWorld.stage.upgrading": "Recargar todos os chunks...", "optimizeWorld.stage.upgrading.chunks": "Actualizar todos os chunks...", "optimizeWorld.stage.upgrading.entities": "Actualizar todas as entidades...", "optimizeWorld.stage.upgrading.poi": "Actualizar todos os puntos de interese...", "optimizeWorld.title": "Optimizando o mundo \"%s\"", "options.accessibility": "Axustes de accesibilidade...", "options.accessibility.high_contrast": "<PERSON> contraste", "options.accessibility.high_contrast.error.tooltip": "O paquete de recursos de alto contraste non está dispoñible", "options.accessibility.high_contrast.tooltip": "Mellora o contraste de elementos de interface", "options.accessibility.high_contrast_block_outline": "Alto contraste de esbozos do bloque", "options.accessibility.high_contrast_block_outline.tooltip": "Mellora o contraste do contorno do bloque seleccionado.", "options.accessibility.link": "Guía de accesibilidade", "options.accessibility.menu_background_blurriness": "Borroso no fondo do menú", "options.accessibility.menu_background_blurriness.tooltip": "Cambia o borroso no fondo do menú.", "options.accessibility.narrator_hotkey": "Tecla de acceso directo do narrador", "options.accessibility.narrator_hotkey.mac.tooltip": "Active o desactive Narrador con 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Permite que o narrador poda activarse e desactivarse con 'Ctrl+B'", "options.accessibility.panorama_speed": "Velocidade de desprazamento do panorama", "options.accessibility.text_background": "Fondo dos textos", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Todos", "options.accessibility.text_background_opacity": "Opacidade dos fondos", "options.accessibility.title": "Axustes de accesibilidade...", "options.allowServerListing": "Listaxes de servidores", "options.allowServerListing.tooltip": "Os servidores deben poñer o nome dos xogadores en liña nunha listaxe como parte do seu estado público.\nCon esta opción desactivada, o teu nome non aparecerá nestas listaxes.", "options.ao": "Iluminación suave", "options.ao.max": "Máximo", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "Non", "options.attack.crosshair": "Mira", "options.attack.hotbar": "Barr<PERSON>", "options.attackIndicator": "Indicador de ataque", "options.audioDevice": "Dispositivo", "options.audioDevice.default": "Por defecto do sistema", "options.autoJump": "Brinco automá<PERSON>", "options.autoSuggestCommands": "<PERSON><PERSON><PERSON><PERSON> comandos", "options.autosaveIndicator": "Indicador de autogardado", "options.biomeBlendRadius": "Transición de biomas", "options.biomeBlendRadius.1": "NON (máis rápido)", "options.biomeBlendRadius.11": "11x11 (extremo)", "options.biomeBlendRadius.13": "13x13 (esaxerado)", "options.biomeBlendRadius.15": "15x15 (máximo)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (alto)", "options.biomeBlendRadius.9": "9x9 (moi alto)", "options.chat": "Axustes do chat...", "options.chat.color": "Cores", "options.chat.delay": "Atraso do chat: %s segundo(s)", "options.chat.delay_none": "Atraso do chat: Ningún", "options.chat.height.focused": "Altura enfocada", "options.chat.height.unfocused": "Altura desenfocada", "options.chat.line_spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.links": "Ligazóns web", "options.chat.links.prompt": "Avisar ao abrir ligazóns", "options.chat.opacity": "Opac. texto do chat", "options.chat.scale": "Tamaño do chat", "options.chat.title": "Axustes do chat", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "Agochado", "options.chat.visibility.system": "Só comandos", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Detalladas", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controis...", "options.credits_and_attribution": "Créditos e atribucións...", "options.damageTiltStrength": "Inclinación de dano", "options.damageTiltStrength.tooltip": "A cantidade da inclinación de dano da cámara pode marearte.", "options.darkMojangStudiosBackgroundColor": "Logotipo monocromático", "options.darkMojangStudiosBackgroundColor.tooltip": "Muda o fondo da pantalla de carga de Mojang Studios para negro.", "options.darknessEffectScale": "Pulsación de escuridade", "options.darknessEffectScale.tooltip": "Controla a intensidade do efecto de escuridade cando a provoca un vixilante ou unha alarma de sculk.", "options.difficulty": "Dificultade", "options.difficulty.easy": "Doado", "options.difficulty.easy.info": "Aparecen cria<PERSON>s host<PERSON>, pero son máis febles. A barra de fame esgótase e pode reducir a saúde até 5 corazóns.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Aparecen criaturas hostís e son máis fortes. A barra de fame esgótase e pode reducir toda a saúde.", "options.difficulty.hardcore": "Extremo", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Aparecen criaturas hostís que fan dano moderado. A barra de fame esgótase e pode reducir a saúde até medio corazón.", "options.difficulty.online": "Dificultade do servidor", "options.difficulty.peaceful": "Pacífico", "options.difficulty.peaceful.info": "Non aparecen criaturas hostís, e só aparecen algunhas criaturas neutrais. A barra de fame non se esgota e a saúde rexenérase co tempo.", "options.directionalAudio": "Son direccional", "options.directionalAudio.off.tooltip": "Audio estéreo clásico", "options.directionalAudio.on.tooltip": "Emprega son direccional con HRTF para mellorar a simulación 3D do son. Require dispositivos de son compatibles con HRTF, e experimentarase mellor con auriculares.", "options.discrete_mouse_scroll": "Desprazamento discreto", "options.entityDistanceScaling": "Distancia de entidades", "options.entityShadows": "Sombras das entidades", "options.font": "Ax<PERSON><PERSON> de fonte...", "options.font.title": "<PERSON><PERSON><PERSON><PERSON> de fonte", "options.forceUnicodeFont": "Fonte unicode", "options.fov": "Campo de visión", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Efectos do campo visual (FOV)", "options.fovEffectScale.tooltip": "Controla canto pode mudar o campo de visión cos efectos do xogo.", "options.framerate": "%s FPS", "options.framerateLimit": "FPS Máximos", "options.framerateLimit.max": "<PERSON><PERSON><PERSON><PERSON>", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "Actual", "options.fullscreen.entry": "%sx%s (%s Hz, %s bits)", "options.fullscreen.resolution": "Resolución en pantalla completa", "options.fullscreen.unavailable": "Opción non dispoñible", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "Por defecto", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Escuro", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocidade do brillo", "options.glintSpeed.tooltip": "Controla a velocidade do escintileo visual nos obxectos encantados.", "options.glintStrength": "Forza do brillo", "options.glintStrength.tooltip": "Controla a transparencia do escintileo visual nos obxectos encantados.", "options.graphics": "Grá<PERSON><PERSON>", "options.graphics.fabulous": "Fabulosos!", "options.graphics.fabulous.tooltip": "Os gráficos %s empregan sombreadores de pantalla (shaders) para debuxar o tempo atmosférico, as nubes e as partículas detrás dos bloques transparentes e a auga.\nIsto pode ter un forte impacto no rendemento dos dispositivos portátiles e monitores 4K.", "options.graphics.fancy": "Detallados", "options.graphics.fancy.tooltip": "Os gráficos detallados equilibran o rendemento e a calidade para a meirande parte dos dispositivos.\nO tempo atmosférico, as nubes e as partículas poden non aparecer detrás dos bloques translúcidos ou a auga.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Os gráficos rápidos reducen a cantidade de chuvia e neve visible.\nOs efectos de transparencia están deshabilitados para varios bloques, coma as follas das árbores.", "options.graphics.warning.accept": "Continuar sen soporte", "options.graphics.warning.cancel": "Voltar", "options.graphics.warning.message": "A túa tarxeta gráfica é detectada como non compatible para a opción de gráficos %s.\n\nPodes ignoralo e continuar, mais non se ofrecerá soporte para o teu dispositivo se elixes utilizar os gráficos %s.", "options.graphics.warning.renderer": "Renderizador detectado: [%s]", "options.graphics.warning.title": "Tarxeta gráfica incompatible", "options.graphics.warning.vendor": "Fabricante detectado: [%s]", "options.graphics.warning.version": "Versión de OpenGL detectada: [%s]", "options.guiScale": "Interface", "options.guiScale.auto": "Automática", "options.hidden": "Agochado", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes.tooltip": "Impide que os lóstregos iluminen o ceo. Os raios aínda serán visíbeis.", "options.hideMatchedNames": "Agochar nomes coincidentes", "options.hideMatchedNames.tooltip": "É posible que os servidores de terceiros envíen mensaxes en formatos atípicos.\nSe esta opción está activada, os xogadores ocultos serán coincidente segundo o seu nome de emisor de chat.", "options.hideSplashTexts": "Agochar textos amarelos iniciais", "options.hideSplashTexts.tooltip": "Agocha os textos amarelos do logo de Minecraft do menú principal.", "options.inactivityFpsLimit": "Reducir FPS cando", "options.inactivityFpsLimit.afk": "Ausentarse", "options.inactivityFpsLimit.afk.tooltip": "Limita la frecuencia de imaxes a 30 cando o xogo non recibe ningunha acción do xogador durante máis de 1 minuto. Limitao de novo a 10 despois de 9 minutos máis.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Limitata la frecuencia de imaxes cando o xogo é minimizada.", "options.invertMouse": "Invertir rato", "options.japaneseGlyphVariants": "Variantes en glifos xaponeses", "options.japaneseGlyphVariants.tooltip": "Utilice variantes xaponesas de caracteres CJK na fonte predeterminada.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Alternar", "options.language": "Lingua...", "options.language.title": "<PERSON><PERSON>", "options.languageAccuracyWarning": "(As traducións lingüísticas poden non ser 100%% precisas)", "options.languageWarning": "As traducións poden non ser 100%% precisas", "options.mainHand": "Man principal", "options.mainHand.left": "E<PERSON>rda", "options.mainHand.right": "Dereita", "options.mipmapLevels": "<PERSON><PERSON>", "options.modelPart.cape": "Capa", "options.modelPart.hat": "<PERSON><PERSON>iro", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON>", "options.modelPart.left_sleeve": "Manga es<PERSON>da", "options.modelPart.right_pants_leg": "<PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON> dereita", "options.mouseWheelSensitivity": "Sensibilidade da roda", "options.mouse_settings": "Axustes do rato...", "options.mouse_settings.title": "Axustes do rato", "options.multiplayer.title": "Axustes de multixogador...", "options.multiplier": "%sx", "options.music_frequency": "Frecuencia da música", "options.music_frequency.constant": "<PERSON><PERSON><PERSON>", "options.music_frequency.default": "Por defecto", "options.music_frequency.frequent": "Frecuente", "options.music_frequency.tooltip": "Muda a frecuencia coa que se reproduce a música durante unha partida.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "<PERSON><PERSON><PERSON> todo", "options.narrator.chat": "<PERSON><PERSON><PERSON> chat", "options.narrator.notavailable": "Non dispoñible", "options.narrator.off": "Non", "options.narrator.system": "<PERSON><PERSON><PERSON> sistema", "options.notifications.display_time": "Duración das notificacións", "options.notifications.display_time.tooltip": "Controla canto tempo se manteñen as notificacións visíbeis en pantalla.", "options.off": "Non", "options.off.composed": "%s: NON", "options.on": "Si", "options.on.composed": "%s: SI", "options.online": "En liña...", "options.online.title": "Opcións en liña", "options.onlyShowSecureChat": "Amosar só o chat seguro", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON> s<PERSON> as mensaxes das que pode verificarse que foran enviadas por outro xogador, e que no sufriron modificacións.", "options.operatorItemsTab": "Lapela de elementos do operador", "options.particles": "Partículas", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "Reducidas", "options.particles.minimal": "Mínimas", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Construtor de chunks", "options.prioritizeChunkUpdates.byPlayer": "Bloqueo parcial", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Algunhas accións dentro dun chuchón van recompilar o chuchón inmediatamente. Isto inclúe a poñer e quitar bloques.", "options.prioritizeChunkUpdates.nearby": "Bloqueo total", "options.prioritizeChunkUpdates.nearby.tooltip": "Os chuchóns pretos son sempre compilados inmediatamente. <PERSON><PERSON> pode afectar no rendemento do xogo cando os bloques son postos ou eliminados.", "options.prioritizeChunkUpdates.none": "En paralelo", "options.prioritizeChunkUpdates.none.tooltip": "Os chunks cercanos están completos en fíos paralelos. Esto pode resultar en breves buratos visuais cando os bloques son destruídos.", "options.rawMouseInput": "Entrada directa", "options.realmsNotifications": "Novidades e convites do Realms", "options.realmsNotifications.tooltip": "Obtén novidades e convites do Realms no menú principal e amosa a súa respectiva icona no botón do Realms.", "options.reducedDebugInfo": "Reducir info de F3", "options.renderClouds": "Nubes", "options.renderCloudsDistance": "Distancia das nubes", "options.renderDistance": "Distancia de visión", "options.resourcepack": "Paquetes de recursos...", "options.rotateWithMinecart": "Xirar con vagonetas", "options.rotateWithMinecart.tooltip": "Indica se a vista do xogador debe xirar cun vagón xiratorio. Só dispoñible nos mundos coa configuración experimental \"Melloras de vagón\" activada.", "options.screenEffectScale": "Efectos de distorsión", "options.screenEffectScale.tooltip": "Intensidade dos efectos de distorsión na pantalla causados pola náusea e os portais do Nether.\nA valores menores, os efectos de náusea substituiranse por unha cor verdosa da pantalla.", "options.sensitivity": "Sensibilidade", "options.sensitivity.max": "DEMASIADA.", "options.sensitivity.min": "*bocexo*", "options.showNowPlayingToast": "Amosar nomes das músicas", "options.showNowPlayingToast.tooltip": "Amosa un cadro de texto cada vez que comeza a soar unha música co seu nome. Amósase constantemente no menú de pausa do xogo mentres soa unha música.", "options.showSubtitles": "<PERSON><PERSON>", "options.simulationDistance": "Distancia de simulación", "options.skinCustomisation": "Personalizar a aparencia...", "options.skinCustomisation.title": "Personalización da aparencia", "options.sounds": "<PERSON><PERSON><PERSON> e sons...", "options.sounds.title": "Opcións de son e música", "options.telemetry": "Telemetría...", "options.telemetry.button": "Recolla de da<PERSON>", "options.telemetry.button.tooltip": "\"%s\" soamente inclúe a información necesaria.\n\"%s\" inclúe tamén información opcional.", "options.telemetry.disabled": "A telemetría está desactivada.", "options.telemetry.state.all": "Total", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "<PERSON><PERSON><PERSON><PERSON>", "options.title": "Opcións", "options.touchscreen": "Modo pantalla táctil", "options.video": "Axustes de vídeo...", "options.videoTitle": "Axustes de vídeo", "options.viewBobbing": "Oscilación", "options.visible": "Visible", "options.vsync": "VSync", "outOfMemory.message": "Minecraft quedou sen memoria.\n\nIsto pode ser debido a un erro do xogo ou porque non lle asinou suficiente memoria á Máquina Virtual de Java.\n\nPara prever a corrupción de niveis, a partida actual fechouse. Tentamos liberar suficiente memoria para deixarche volver ao menú principal e que volveses xogar, mais isto quizais non funcionou.\n\nPor favor reinicia o xogo se ves esta mensaxe de novo.", "outOfMemory.title": "Sen memoria!", "pack.available.title": "Dispoñible", "pack.copyFailure": "Erro ao copiar os paquetes", "pack.dropConfirm": "Se<PERSON>ro que queres engadir os seguintes paquetes a Minecraft?", "pack.dropInfo": "Arrastra e solta ficheiros nesta xanela para engadir paquetes", "pack.dropRejected.message": "As seguintes entradas non eran paquetes válidos e non se copiaron: %s", "pack.dropRejected.title": "Entradas sen paquete", "pack.folderInfo": "(Colocar aquí os ficheiros do paquete)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "Este paquete foi feito para unha versión máis recente de Minecraft e pode que non funcione correctamente.", "pack.incompatible.confirm.old": "Este paquete foi feito para unha versión máis antiga de Minecraft e pode que non funcione correctamente.", "pack.incompatible.confirm.title": "Se<PERSON>ro que queres cargar este paquete?", "pack.incompatible.new": "(Feito para unha versión máis recente de Minecraft)", "pack.incompatible.old": "(Feito para unha versión anterior de Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Abrir cartafol dos paquetes", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "integrado", "pack.source.feature": "funcionalidade", "pack.source.local": "local", "pack.source.server": "servidor", "pack.source.world": "mundo", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "<PERSON>ariante ao chou", "parsing.bool.expected": "Esperábase un valor booleano", "parsing.bool.invalid": "<PERSON><PERSON> booleano invalido, esperábase \"true\" ou \"false\" pero atopouse \"%s\"", "parsing.double.expected": "Esperábase un valor doble", "parsing.double.invalid": "Valor doble non válido: %s", "parsing.expected": "Esperábase \"%s\"", "parsing.float.expected": "Esperábase un valor float", "parsing.float.invalid": "Valor float inválido: %s", "parsing.int.expected": "Esperábase un número enteiro", "parsing.int.invalid": "Número enteiro non válido: %s", "parsing.long.expected": "Esperábase un valor long", "parsing.long.invalid": "<PERSON>or long inválido '%s'", "parsing.quote.escape": "A secuencia de escape \"\\%s\" non é válida nunha cadea con comiñas", "parsing.quote.expected.end": "Falta o peche de comiñas da cadea", "parsing.quote.expected.start": "Faltan comiñas ao inicio da cadea", "particle.invalidOptions": "Non se poden analizar as opcións de partículas: %s", "particle.notFound": "Partícula descoñecida: %s", "permissions.requires.entity": "Requírese unha entidade para executar este comando aquí", "permissions.requires.player": "Requírese un xogador para executar este comando aquí", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Ao aplicarse:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicado descoñecido: %s", "quickplay.error.invalid_identifier": "Non se puido atopar un mundo co identificador proporcionado", "quickplay.error.realm_connect": "Non se pode conectar ao realm", "quickplay.error.realm_permission": "Non ten permiso para conectarse a este realm", "quickplay.error.title": "Erro ó ingresar en xogo rápido", "realms.configuration.region.australia_east": "Nova Gales do Sur, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, EE. UU.", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virxinia, EE. UU.", "realms.configuration.region.east_us_2": "Carolina do Norte, EE. UU.", "realms.configuration.region.france_central": "Francia", "realms.configuration.region.japan_east": "Leste do Xapón", "realms.configuration.region.japan_west": "Oeste do Xapón", "realms.configuration.region.korea_central": "Corea do Sur", "realms.configuration.region.north_central_us": "Illinois, EE. UU.", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Texas, EE. UU.", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON>", "realms.configuration.region.uae_north": "Emiratos Árabes Unidos (EAU)", "realms.configuration.region.uk_south": "Sur de Inglaterra", "realms.configuration.region.west_central_us": "Utah, EE. UU.", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "California, EE. UU.", "realms.configuration.region.west_us_2": "Washington, EE. UU.", "realms.configuration.region_preference.automatic_owner": "Automática (latencia do dono do Realm)", "realms.configuration.region_preference.automatic_player": "Automática (latencia do primeiro en unirse)", "realms.missing.snapshot.error.text": "Realms non é compatible coas versións de proba", "recipe.notFound": "Receita descoñecida: %s", "recipe.toast.description": "Revisa o teu libro de receitas", "recipe.toast.title": "Novas receitas desbloqueadas!", "record.nowPlaying": "Estase a reproducir: %s", "recover_world.bug_tracker": "Informar dun erro", "recover_world.button": "Tentar recuperar", "recover_world.done.failed": "Non se puido recuperar dende o estado anterior.", "recover_world.done.success": "A recuperación foi exitosa!", "recover_world.done.title": "Recuperación completada", "recover_world.issue.missing_file": "Arquivo faltante", "recover_world.issue.none": "<PERSON>as", "recover_world.message": "Ocorreron os seguintes problemas ao tentar ler o cartafol do mundo \"%s\".\nPode ser posible restaurar o mundo desde un estado máis antigo ou pode informar deste problema no rastreador de erros.", "recover_world.no_fallback": "Non hai estado dispoñible para recuperar", "recover_world.restore": "Tentar recuperar", "recover_world.restoring": "Tentar recuperar o mundo...", "recover_world.state_entry": "Estado dende %s: ", "recover_world.state_entry.unknown": "descoñecido", "recover_world.title": "Erro ao cargar o mundo", "recover_world.warning": "Erro ao cargar o resumo do mundo", "resourcePack.broken_assets": "DETECTÁRONSE RECURSOS DEFECTUOSOS", "resourcePack.high_contrast.name": "<PERSON> contraste", "resourcePack.load_fail": "Fallou a recarga de recursos", "resourcePack.programmer_art.name": "Arte do programador", "resourcePack.runtime_failure": "Detectouse un erro do paquete de recursos", "resourcePack.server.name": "Recursos específicos do mundo", "resourcePack.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> paque<PERSON> de recursos", "resourcePack.vanilla.description": "O aspecto por defecto de Minecraft", "resourcePack.vanilla.name": "Por defecto", "resourcepack.downloading": "<PERSON><PERSON><PERSON> paque<PERSON> de recursos", "resourcepack.progress": "<PERSON><PERSON><PERSON> (%s MB)...", "resourcepack.requesting": "Facendo solicitude...", "screenshot.failure": "Non se puido gardar a captura: %s", "screenshot.success": "Captura gardada como %s", "selectServer.add": "<PERSON><PERSON><PERSON> servidor", "selectServer.defaultName": "<PERSON><PERSON><PERSON>", "selectServer.delete": "Eliminar", "selectServer.deleteButton": "Eliminar", "selectServer.deleteQuestion": "Estás seguro de querer eliminar este servidor?", "selectServer.deleteWarning": "Perderase \"%s\" para sempre! (moito tempo!)", "selectServer.direct": "Conexión directa", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(Agochado)", "selectServer.refresh": "Actualizar", "selectServer.select": "Entrar ó servidor", "selectWorld.access_failure": "Erro ó acceder ó mundo", "selectWorld.allowCommands": "Per<PERSON><PERSON> comandos", "selectWorld.allowCommands.info": "Comandos como /gamemode, /experience", "selectWorld.allowCommands.new": "Per<PERSON><PERSON> comandos", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> datos en caché", "selectWorld.backupJoinConfirmButton": "Crear copia e cargar", "selectWorld.backupJoinSkipButton": "Sei o que fago!", "selectWorld.backupQuestion.customized": "Os mundos personalizados xa non son compatibles", "selectWorld.backupQuestion.downgrade": "Non é posíbel reverter o mundo para unha versión anterior", "selectWorld.backupQuestion.experimental": "Os mundos con axustes experimentais non son compatibles", "selectWorld.backupQuestion.snapshot": "Desexas realmente cargar este mundo?", "selectWorld.backupWarning.customized": "Desafortunadamente, os mundos personalizados non son compatibles con esta versión do Minecraft. Podemos seguir cargando este mundo e deixar todo xusto como estaba antes, pero calquera novo terreo xerado non estará personalizado. <PERSON>timos as molestias!", "selectWorld.backupWarning.downgrade": "Este mundo foi xogado por última vez na versión %s e estás na %s. Reverter o mundo para unha versión anterior pode corrompelo. Non é posíbel garantir que cargue ou funcione. Se aínda así queres continuar, fai unha copia!", "selectWorld.backupWarning.experimental": "Este mundo usa axustes experimentais que poden deixar de funcionar en calquera momento. Non podemos garantir que carguen ou funcionen correctamente. Estade alerta!", "selectWorld.backupWarning.snapshot": "Este mundo foi xogado por última vez na versión %s, ti estás na %s. Fai unha copia para evitar corrupcións no mundo!", "selectWorld.bonusItems": "<PERSON><PERSON><PERSON>", "selectWorld.cheats": "Trucos", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "Debe ser convertido!", "selectWorld.conversion.tooltip": "Este mundo ten que ser aberto nunha versión máis vella (como 1.6.4) para ser convertido sen ningún erro", "selectWorld.create": "Crear un novo mundo", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "Paquetes de <PERSON>", "selectWorld.data_read": "Lendo datos do mundo...", "selectWorld.delete": "Eliminar", "selectWorld.deleteButton": "Eliminar", "selectWorld.deleteQuestion": "Estás seguro de querer eliminar este mundo?", "selectWorld.deleteWarning": "\"%s\" desaparecerá... para sempre! (moito tempo!)", "selectWorld.delete_failure": "Erro ó borrar o mundo", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "Facer copia", "selectWorld.edit.backupCreated": "Copia: %s", "selectWorld.edit.backupFailed": "Erro ao crear a copia", "selectWorld.edit.backupFolder": "Abrir cartafol de copias", "selectWorld.edit.backupSize": "tamaño: %s MB", "selectWorld.edit.export_worldgen_settings": "Exportar axustes de xeración de mundo", "selectWorld.edit.export_worldgen_settings.failure": "Erro ao exportar", "selectWorld.edit.export_worldgen_settings.success": "Exportado", "selectWorld.edit.openFolder": "Abrir cartafol de mundos", "selectWorld.edit.optimize": "<PERSON>timi<PERSON> mundo", "selectWorld.edit.resetIcon": "Rest<PERSON><PERSON>cer icona", "selectWorld.edit.save": "Gardar", "selectWorld.edit.title": "<PERSON>ar mundo", "selectWorld.enterName": "Nome do mundo", "selectWorld.enterSeed": "Semente para xerar o mundo", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalles", "selectWorld.experimental.details.entry": "Características experimentais necesarias: %s", "selectWorld.experimental.details.title": "Requirimentos de características experimentais", "selectWorld.experimental.message": "Ten coidado!\nEsta configuración require características que aínda están en desenvolvemento. O teu mundo podería pecharse, romper ou non funcionar con futuras actualizacións.", "selectWorld.experimental.title": "Advertencia de características experimentais", "selectWorld.experiments": "Experimentos", "selectWorld.experiments.info": "Os experimentos son posibles funcións novas, pero unha vez activados, non hai volta atrás. Polo tanto, ten coidado ó seleccionalos e activalos, xa que algúns poden non funcionar correctamente.", "selectWorld.futureworld.error.text": "Algo fallou ao tentar cargar un mundo dunha versión posterior. Era una operación arriscada, sentimos as molestias.", "selectWorld.futureworld.error.title": "Ocorreu un erro!", "selectWorld.gameMode": "<PERSON><PERSON>", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Similar ó modo supervivencia, pero sen poder colocar nin romper bloques.", "selectWorld.gameMode.adventure.line1": "Igual ó modo supervivencia, pero os bloques", "selectWorld.gameMode.adventure.line2": "non poden ser colocados nin eliminados", "selectWorld.gameMode.creative": "Creativo", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, constrúe e explora sen límites. Podes voar, ter materiais sen fin, e non podes ser danado polos monstros.", "selectWorld.gameMode.creative.line1": "Recursos ilimitados, voo libre e", "selectWorld.gameMode.creative.line2": "destrución instantánea de bloques", "selectWorld.gameMode.hardcore": "Extremo", "selectWorld.gameMode.hardcore.info": "Semellante ao modo supervivencia pero coa dificultade bloqueada en \"Difícil\" e cunha única vida.", "selectWorld.gameMode.hardcore.line1": "É igual ó modo supervivencia, pero na máxima", "selectWorld.gameMode.hardcore.line2": "dificultade e cunha soa vida", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON> mirar, pero non tocar.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON><PERSON> mirar, pero non tocar", "selectWorld.gameMode.survival": "Supervivencia", "selectWorld.gameMode.survival.info": "Explora un mundo misterioso onde podes construír, recoller, fabricar e loitar contra monstros.", "selectWorld.gameMode.survival.line1": "Procura recursos, fabrica, gaña", "selectWorld.gameMode.survival.line2": "niveis, saúde e fame", "selectWorld.gameRules": "Normas do xogo", "selectWorld.import_worldgen_settings": "Importar axustes", "selectWorld.import_worldgen_settings.failure": "Erro ao importar os axustes", "selectWorld.import_worldgen_settings.select_file": "Selecciona un ficheiro de axustes (.json)", "selectWorld.incompatible.description": "Este mundo non se pode abrir nesta versión.\nFoi xogado por última vez na versión %s.", "selectWorld.incompatible.info": "Versión incompatible: %s", "selectWorld.incompatible.title": "Versión incompatible", "selectWorld.incompatible.tooltip": "O mundo non pode abrirse porque foi creado nunha versión incompatible.", "selectWorld.incompatible_series": "Creado nunha versión incompatíbel", "selectWorld.load_folder_access": "Non se pode ler ou acceder o cartafol onde se gardan os mundos!", "selectWorld.loading_list": "Cargando listaxe de mundos", "selectWorld.locked": "Mundo aberto noutra sesión do Minecraft", "selectWorld.mapFeatures": "Xerar estru<PERSON>s", "selectWorld.mapFeatures.info": "Aldeas, afundimentos, etc.", "selectWorld.mapType": "Tipo de mundo", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Máis opcións do mundo...", "selectWorld.newWorld": "Novo mundo", "selectWorld.recreate": "<PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Os mundos personalizados non son compatíbeis con esta versión do Minecraft. Podemos tentar refacelo coa mesma semente e coas mesmas propiedades, mais perderase calquera modificación do terreo. Sentimos as molestias!", "selectWorld.recreate.customized.title": "Os mundos personalizados xa non son compatíbeis", "selectWorld.recreate.error.text": "Algo fallou ao tentar refacer o mundo.", "selectWorld.recreate.error.title": "Ocorreu un erro!", "selectWorld.resource_load": "Preparando recursos...", "selectWorld.resultFolder": "Gardarase en:", "selectWorld.search": "buscar mundos", "selectWorld.seedInfo": "Deixar en branco para unha semente ao chou", "selectWorld.select": "<PERSON><PERSON><PERSON> mundo", "selectWorld.targetFolder": "Cartafol de gardado: %s", "selectWorld.title": "Seleccionar mundo", "selectWorld.tooltip.fromNewerVersion1": "O mundo foi gardado nunha versión máis recente,", "selectWorld.tooltip.fromNewerVersion2": "cargar este mundo pode causar problemas!", "selectWorld.tooltip.snapshot1": "Non esquezas gardar este mundo", "selectWorld.tooltip.snapshot2": "antes de cargar nesta versión.", "selectWorld.unable_to_load": "Non se poden cargar os mundos", "selectWorld.version": "Versión:", "selectWorld.versionJoinButton": "<PERSON><PERSON> de todas formas", "selectWorld.versionQuestion": "Realmente queres cargar este mundo?", "selectWorld.versionUnknown": "descoñecido", "selectWorld.versionWarning": "O mundo foi xogado por última vez na versión %s, cargalo nesta versión pode corrompelo!", "selectWorld.warning.deprecated.question": "Algunhas das características usadas están obsoletas e deixarán de funcionar no futuro. Desexas continuar?", "selectWorld.warning.deprecated.title": "Advertencia! Estes axustes usan funcións obsoletas", "selectWorld.warning.experimental.question": "Estes axustes son experimentais e poderían deixar de funcionar en calquera comento. Desexas continuar?", "selectWorld.warning.experimental.title": "Aviso! Estes axustes usan características experimentais", "selectWorld.warning.lowDiskSpace.description": "Non queda moito espazo no teu dispositivo.\nQuedar sen espazo no disco mentres estás no xogo pode provocar que o teu mundo d<PERSON>ese.", "selectWorld.warning.lowDiskSpace.title": "Advertencia! Queda pouco espazo no disco!", "selectWorld.world": "Mundo", "sign.edit": "<PERSON><PERSON>", "sleep.not_possible": "Non hai xogadores durmindo dabondo para pasar a noite", "sleep.players_sleeping": "%s/%s xogadores durmindo", "sleep.skipping_night": "Durm<PERSON>o até o amencer...", "slot.only_single_allowed": "<PERSON><PERSON> se <PERSON>en ra<PERSON>s valeira<PERSON>, ten '%s'", "slot.unknown": "Rañura desconocida: %s", "snbt.parser.empty_key": "Clave non pode estar baleira", "snbt.parser.expected_binary_numeral": "Agardábase un número binario", "snbt.parser.expected_decimal_numeral": "Agardábase un número decimal", "snbt.parser.expected_float_type": "Agardábase un número de punto flotante", "snbt.parser.expected_hex_escape": "Agardábase un carácter literal de lonxitude %s", "snbt.parser.expected_hex_numeral": "Agardábase un número hexadecimal", "snbt.parser.expected_integer_type": "Agardábase un número enteiro", "snbt.parser.expected_non_negative_number": "Agardábase un número non negativo", "snbt.parser.expected_number_or_boolean": "Agardábase un número ou un booleano", "snbt.parser.expected_string_uuid": "Agardábase un fío representando un UUID válido", "snbt.parser.expected_unquoted_string": "Agardábase un fío sen comiñas válido", "snbt.parser.infinity_not_allowed": "Non se permiten números non finitos", "snbt.parser.invalid_array_element_type": "Tipo de elemento matricial inválido", "snbt.parser.invalid_character_name": "Nome de carácter Unicode inválido", "snbt.parser.invalid_codepoint": "Valor de carácter Unicode inválido: %s", "snbt.parser.invalid_string_contents": "Contidos de fío inválidos", "snbt.parser.invalid_unquoted_start": "Fíos sen comiñas non poden comezar con díxitos 0-9, + ou -", "snbt.parser.leading_zero_not_allowed": "Números decimais non poden comezar con 0", "snbt.parser.no_such_operation": "Non existe a operación: %s", "snbt.parser.number_parse_failure": "Erro ao analizar o número: %s", "snbt.parser.undescore_not_allowed": "Non se permiten barras baixas ao comezo ou final dun número", "soundCategory.ambient": "Ambiente", "soundCategory.block": "Bloques", "soundCategory.hostile": "Criaturas hostís", "soundCategory.master": "Volume xeral", "soundCategory.music": "Música", "soundCategory.neutral": "Criaturas pacíficas", "soundCategory.player": "Xogadores", "soundCategory.record": "Bloques musicais", "soundCategory.ui": "Interface", "soundCategory.voice": "Voz", "soundCategory.weather": "Clima", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.previous_page": "Anterior p<PERSON>xina", "spectatorMenu.root.prompt": "Preme unha tecla para seleccionar un comando e prémea outra vez para empregalo.", "spectatorMenu.team_teleport": "Teletransportarse a un membro do equipo", "spectatorMenu.team_teleport.prompt": "Escolle un equipo ao que teletransportarte", "spectatorMenu.teleport": "Teletransportar a un xogador", "spectatorMenu.teleport.prompt": "Escolle un xogador ao que teletransportarte", "stat.generalButton": "Xeral", "stat.itemsButton": "Obxectos", "stat.minecraft.animals_bred": "Animais alimentados", "stat.minecraft.aviate_one_cm": "Distancia voada en élitros", "stat.minecraft.bell_ring": "Campás tocadas", "stat.minecraft.boat_one_cm": "Distancia en barca", "stat.minecraft.clean_armor": "Pezas da armadura limpadas", "stat.minecraft.clean_banner": "Estandartes limpados", "stat.minecraft.clean_shulker_box": "Cai<PERSON> de shulker baleirada", "stat.minecraft.climb_one_cm": "Distancia escalada", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.damage_absorbed": "Dano <PERSON>", "stat.minecraft.damage_blocked_by_shield": "Dano bloqueado polo escudo", "stat.minecraft.damage_dealt": "<PERSON><PERSON> causado", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON> (absorbido)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> (resistido)", "stat.minecraft.damage_resisted": "Dano resistido", "stat.minecraft.damage_taken": "<PERSON><PERSON> re<PERSON>o", "stat.minecraft.deaths": "<PERSON>ú<PERSON><PERSON> de mortes", "stat.minecraft.drop": "Obxectos tirados", "stat.minecraft.eat_cake_slice": "Anacos de torta comidos", "stat.minecraft.enchant_item": "Obxectos encantados", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "Caldeiros enchidos", "stat.minecraft.fish_caught": "Peixes pescados", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON> voada", "stat.minecraft.happy_ghast_one_cm": "Distancia con ghast ledo", "stat.minecraft.horse_one_cm": "Distancia a cabalo", "stat.minecraft.inspect_dispenser": "Dispensadores examinados", "stat.minecraft.inspect_dropper": "Lanzadores examinados", "stat.minecraft.inspect_hopper": "Funís exam<PERSON>", "stat.minecraft.interact_with_anvil": "Interaccións con engras", "stat.minecraft.interact_with_beacon": "Interaccións con faros", "stat.minecraft.interact_with_blast_furnace": "Interaccións con altos fornos", "stat.minecraft.interact_with_brewingstand": "Interaccións con fermentadores", "stat.minecraft.interact_with_campfire": "Interaccións con cacharelas", "stat.minecraft.interact_with_cartography_table": "Integracións con mesas de cartografía", "stat.minecraft.interact_with_crafting_table": "Interaccións con mesas de fabricación", "stat.minecraft.interact_with_furnace": "Interaccións con fornos", "stat.minecraft.interact_with_grindstone": "Interaccións con pedras de amolar", "stat.minecraft.interact_with_lectern": "Interaccións con atrís", "stat.minecraft.interact_with_loom": "Interaccións con teares", "stat.minecraft.interact_with_smithing_table": "Interaccións coa mesa de ferraría", "stat.minecraft.interact_with_smoker": "Interaccións con afumadoiros", "stat.minecraft.interact_with_stonecutter": "Interaccións con cortapedras", "stat.minecraft.jump": "Brincos", "stat.minecraft.leave_game": "Partidas abandonadas", "stat.minecraft.minecart_one_cm": "Distancia en vagoneta", "stat.minecraft.mob_kills": "Criaturas mortas", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON> do <PERSON>", "stat.minecraft.open_shulker_box": "Caixas de shulker abertas", "stat.minecraft.pig_one_cm": "Distancia en porco", "stat.minecraft.play_noteblock": "Bloques de notas tocados", "stat.minecraft.play_record": "Discos de música reproducidos", "stat.minecraft.play_time": "Tempo de xogo", "stat.minecraft.player_kills": "Xogadores asasinados", "stat.minecraft.pot_flower": "Plantas postas en testos", "stat.minecraft.raid_trigger": "Invasións activadas", "stat.minecraft.raid_win": "Invasións gañadas", "stat.minecraft.sleep_in_bed": "Veces durmidas na cama", "stat.minecraft.sneak_time": "Tempo <PERSON>do", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> correndo", "stat.minecraft.strider_one_cm": "Distancia en vagante", "stat.minecraft.swim_one_cm": "Distancia nadada", "stat.minecraft.talked_to_villager": "Conversas con aldeáns", "stat.minecraft.target_hit": "Albos atinxidos", "stat.minecraft.time_since_death": "Tempo dende a última morte", "stat.minecraft.time_since_rest": "Tempo dende o último descanso", "stat.minecraft.total_world_time": "Tempo co mundo aberto", "stat.minecraft.traded_with_villager": "Trocos con aldeáns", "stat.minecraft.trigger_trapped_chest": "Cofres trampa accionados", "stat.minecraft.tune_noteblock": "Bloques de notas afinados", "stat.minecraft.use_cauldron": "Auga sacada do caldeiro", "stat.minecraft.walk_on_water_one_cm": "Distancia camiñada na auga", "stat.minecraft.walk_one_cm": "Distancia a pé", "stat.minecraft.walk_under_water_one_cm": "Distancia camiñada baixo a auga", "stat.mobsButton": "Criaturas", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON> roto", "stat_type.minecraft.crafted": "Veces fabricado", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON> tirado", "stat_type.minecraft.killed": "Mataches a %s %s", "stat_type.minecraft.killed.none": "Nunca mataches a %s", "stat_type.minecraft.killed_by": "%s matoute %s veces", "stat_type.minecraft.killed_by.none": "Nunca morreches por %s", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON> picado", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON> recollido", "stat_type.minecraft.used": "<PERSON>eces usado", "stats.none": "-", "structure_block.button.detect_size": "DETECTAR", "structure_block.button.load": "CARGAR", "structure_block.button.save": "GARDAR", "structure_block.custom_data": "Nome de data tag personalizado", "structure_block.detect_size": "Detectar tamaño e posición da estrutura:", "structure_block.hover.corner": "Esquina: %s", "structure_block.hover.data": "Datos: %s", "structure_block.hover.load": "Cargar: %s", "structure_block.hover.save": "Gardar: %s", "structure_block.include_entities": "Incluír entidades:", "structure_block.integrity": "Integridade e semente da estrutura", "structure_block.integrity.integrity": "Integridade da estrutura", "structure_block.integrity.seed": "Semente da estrutura", "structure_block.invalid_structure_name": "'%s' non é un nome de estructura válido", "structure_block.load_not_found": "A estructura \"%s\" non está dispoñible", "structure_block.load_prepare": "Posición da estrutura \"%s\" preparada", "structure_block.load_success": "Estrutura cargada de \"%s\"", "structure_block.mode.corner": "Esquina", "structure_block.mode.data": "Datos", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Gardar", "structure_block.mode_info.corner": "<PERSON><PERSON> esquina - Marcador de lugar e tamaño", "structure_block.mode_info.data": "Modo datos - Lóxica do xogo", "structure_block.mode_info.load": "Modo cargar - Cargar dun ficheiro", "structure_block.mode_info.save": "<PERSON><PERSON> gardar - Gardar nun ficheiro", "structure_block.position": "Posición relativa", "structure_block.position.x": "posición relativa x", "structure_block.position.y": "posición relativa y", "structure_block.position.z": "posición relativa z", "structure_block.save_failure": "Non se pode gardar a estrutura \"%s\"", "structure_block.save_success": "Estrutura gardada como \"%s\"", "structure_block.show_air": "Amosar bloques invisíbeis:", "structure_block.show_boundingbox": "<PERSON><PERSON> bordos:", "structure_block.size": "Tamaño da estrutura", "structure_block.size.x": "tamano da estrutura x", "structure_block.size.y": "tamano da estrutura y", "structure_block.size.z": "tamano da estrutura z", "structure_block.size_failure": "Non foi posible detectar o tamaño da estructura. Engade máis esquinas co nome da estructura correspondente", "structure_block.size_success": "Tamaño detectado correctamente para \"%s\"", "structure_block.strict": "Colocación estrita:", "structure_block.structure_name": "Nome de estrutura", "subtitles.ambient.cave": "Ruido escalofrian<PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON>", "subtitles.block.amethyst_block.chime": "Ametista soando", "subtitles.block.amethyst_block.resonate": "Ametista resoa", "subtitles.block.anvil.destroy": "<PERSON><PERSON> des<PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON> caída", "subtitles.block.anvil.use": "Engra usada", "subtitles.block.barrel.close": "Bocoi pechado", "subtitles.block.barrel.open": "Bo<PERSON>i aberto", "subtitles.block.beacon.activate": "Faro activado", "subtitles.block.beacon.ambient": "Faro resoa", "subtitles.block.beacon.deactivate": "Faro desactivado", "subtitles.block.beacon.power_select": "Poder do faro elixido", "subtitles.block.beehive.drip": "<PERSON>ga", "subtitles.block.beehive.enter": "Abella entra na abellariza", "subtitles.block.beehive.exit": "Abella <PERSON> da abellariza", "subtitles.block.beehive.shear": "Tesoiras raspan<PERSON>", "subtitles.block.beehive.work": "<PERSON><PERSON>", "subtitles.block.bell.resonate": "Campá resoando", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Planta pin<PERSON> inclinándose cara abaixo", "subtitles.block.big_dripleaf.tilt_up": "Planta pin<PERSON> inclinándose cara arriba", "subtitles.block.blastfurnace.fire_crackle": "Alto forno faiscando", "subtitles.block.brewing_stand.brew": "B<PERSON><PERSON>as no fermentador", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON> ascendendo", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON><PERSON>mu<PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON><PERSON> descendendo", "subtitles.block.button.click": "Botón accionado", "subtitles.block.cake.add_candle": "Candea colocada", "subtitles.block.campfire.crackle": "Faísca de cacharela", "subtitles.block.candle.crackle": "<PERSON>dea faiscando", "subtitles.block.candle.extinguish": "Candea sufócase", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON> p<PERSON>ado", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> p<PERSON>ado", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.chorus_flower.death": "Flor coral murcha", "subtitles.block.chorus_flower.grow": "Flor cloral madura", "subtitles.block.comparator.click": "Clics no comparador", "subtitles.block.composter.empty": "Composteiro bale<PERSON>", "subtitles.block.composter.fill": "Composteiro enchido", "subtitles.block.composter.ready": "Composteiro listo", "subtitles.block.conduit.activate": "Canalizador activado", "subtitles.block.conduit.ambient": "Canalizador latexa", "subtitles.block.conduit.attack.target": "Canalizador ataca", "subtitles.block.conduit.deactivate": "Canalizador desactivado", "subtitles.block.copper_bulb.turn_off": "Lámpada de cobre apágase", "subtitles.block.copper_bulb.turn_on": "Lámpada de cobre acéndese", "subtitles.block.copper_trapdoor.close": "Zapón pechado", "subtitles.block.copper_trapdoor.open": "Zapón a<PERSON>", "subtitles.block.crafter.craft": "Fabricador fabrica", "subtitles.block.crafter.fail": "Fabricador falla", "subtitles.block.creaking_heart.hurt": "Corazón crepitante rosma", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.spawn": "Corazón crepitante esperta", "subtitles.block.deadbush.idle": "Sons secos", "subtitles.block.decorated_pot.insert": "Tarro decorado enchido", "subtitles.block.decorated_pot.insert_fail": "Tarro decorado cambalea", "subtitles.block.decorated_pot.shatter": "Tarro decorado racha", "subtitles.block.dispenser.dispense": "Obxecto dispensado", "subtitles.block.dispenser.fail": "Erro de dispensador", "subtitles.block.door.toggle": "Chirrío de <PERSON>a", "subtitles.block.dried_ghast.ambient": "Sons secos", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> seco mó<PERSON>e", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> seco aní<PERSON>", "subtitles.block.dry_grass.ambient": "Sons ventosos", "subtitles.block.enchantment_table.use": "Mesa de encantamentos usada", "subtitles.block.end_portal.spawn": "Portal do End abríndose", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON> <PERSON> Ender encaixado", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON> pecha", "subtitles.block.eyeblossom.idle": "Fitamel bisba", "subtitles.block.eyeblossom.open": "Fitamel abre", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON>", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON> extinguido", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON> zumban", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON> eclos<PERSON>a", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> faiscando", "subtitles.block.generic.break": "Bloque roto", "subtitles.block.generic.fall": "Algo bate contra un bloque", "subtitles.block.generic.footsteps": "Pasos", "subtitles.block.generic.hit": "<PERSON><PERSON><PERSON> rompendo", "subtitles.block.generic.place": "Bloque colocado", "subtitles.block.grindstone.use": "Pedra de amolar usada", "subtitles.block.growing_plant.crop": "Planta podada", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.block.honey_block.slide": "Escorregando por un bloque de mel", "subtitles.block.iron_trapdoor.close": "Zapón pechado", "subtitles.block.iron_trapdoor.open": "Zapón a<PERSON>", "subtitles.block.lava.ambient": "Burbulleo de lava", "subtitles.block.lava.extinguish": "Siseo de lava", "subtitles.block.lever.click": "Panca accionada", "subtitles.block.note_block.note": "Bloque de notas soando", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.piston.move": "Movemento de pistón", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> pin<PERSON>do", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> pingando nun caldeiro", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON> pingando", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON> pingando nun caldeiro", "subtitles.block.pointed_dripstone.land": "Estalactita caendo", "subtitles.block.portal.ambient": "Zumbido de portal", "subtitles.block.portal.travel": "Ruído do portal esvaécese", "subtitles.block.portal.trigger": "Ruído do portal intensifícase", "subtitles.block.pressure_plate.click": "Placa de presión activada", "subtitles.block.pumpkin.carve": "Tallando con tesoiras", "subtitles.block.redstone_torch.burnout": "Facho ardendo", "subtitles.block.respawn_anchor.ambient": "Ruídos de áncora de reaparición", "subtitles.block.respawn_anchor.charge": "A áncora de reaparición está cargada", "subtitles.block.respawn_anchor.deplete": "A áncora de reaparición está esgotada", "subtitles.block.respawn_anchor.set_spawn": "A áncora de reaparición estabeleceu o punto de reaparición", "subtitles.block.sand.idle": "Sons areosos", "subtitles.block.sand.wind": "Sons de vento", "subtitles.block.sculk.charge": "<PERSON><PERSON><PERSON> bur<PERSON>a", "subtitles.block.sculk.spread": "Sculk espállase", "subtitles.block.sculk_catalyst.bloom": "Catalizador de Sculk brotando", "subtitles.block.sculk_sensor.clicking": "Sensor de sculk activado", "subtitles.block.sculk_sensor.clicking_stop": "Sensor de sculk desactivado", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON> de sculk soa", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> de shulker pechada", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> de shulker aberta", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.block.smithing_table.use": "Mesa de ferraría usada", "subtitles.block.smoker.smoke": "Afumadoiro bota fume", "subtitles.block.sniffer_egg.crack": "Ovo de cheirador racha", "subtitles.block.sniffer_egg.hatch": "Ovo de cheirador eclosiona", "subtitles.block.sniffer_egg.plop": "Cheirador pon un ovo", "subtitles.block.sponge.absorb": "Esponxa absorbe", "subtitles.block.sweet_berry_bush.pick_berries": "Bagas collidas", "subtitles.block.trapdoor.close": "Zapón pechado", "subtitles.block.trapdoor.open": "Zapón a<PERSON>", "subtitles.block.trapdoor.toggle": "Chirrío de z<PERSON>ón", "subtitles.block.trial_spawner.about_to_spawn_item": "Obxecto sombrío prepárase", "subtitles.block.trial_spawner.ambient": "O invocador de desafío crepita", "subtitles.block.trial_spawner.ambient_charged": "Crepitación sombría", "subtitles.block.trial_spawner.ambient_ominous": "Crepitación sombría", "subtitles.block.trial_spawner.charge_activate": "Presaxio afecta un invocador de desafío", "subtitles.block.trial_spawner.close_shutter": "O invocador de desafío pechouse", "subtitles.block.trial_spawner.detect_player": "O invocador de desafío cargouse", "subtitles.block.trial_spawner.eject_item": "O invocador de desafío soltou obxectos", "subtitles.block.trial_spawner.ominous_activate": "Presaxio afecta un invocador de desafío", "subtitles.block.trial_spawner.open_shutter": "O invocador de desafío abriuse", "subtitles.block.trial_spawner.spawn_item": "Obxecto sombrío cae", "subtitles.block.trial_spawner.spawn_item_begin": "Obxecto sombrío aparece", "subtitles.block.trial_spawner.spawn_mob": "O invocador de desafío fixo aparecer unha criatura", "subtitles.block.tripwire.attach": "Fío trampa conectado", "subtitles.block.tripwire.click": "Fío trampa activado", "subtitles.block.tripwire.detach": "Fío trampa desconectado", "subtitles.block.vault.activate": "Arca activada", "subtitles.block.vault.ambient": "Arca faísca", "subtitles.block.vault.close_shutter": "Arca péchase", "subtitles.block.vault.deactivate": "Arca desactivada", "subtitles.block.vault.eject_item": "Arca solta obxecto", "subtitles.block.vault.insert_item": "Arca <PERSON>loq<PERSON>", "subtitles.block.vault.insert_item_fail": "Arca rexeita obxecto", "subtitles.block.vault.open_shutter": "<PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "Arca rexeita x<PERSON>dor", "subtitles.block.water.ambient": "<PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "A esponxa secouse", "subtitles.chiseled_bookshelf.insert": "Libro colocado", "subtitles.chiseled_bookshelf.insert_enchanted": "Libro encantado colocado", "subtitles.chiseled_bookshelf.take": "Libro collido", "subtitles.chiseled_bookshelf.take_enchanted": "Libro encantado collido", "subtitles.enchant.thorns.hit": "Pinchazo de espinas", "subtitles.entity.allay.ambient_with_item": "Allay procura", "subtitles.entity.allay.ambient_without_item": "Allay cobiza", "subtitles.entity.allay.death": "Allay morre", "subtitles.entity.allay.hurt": "Allay ferido", "subtitles.entity.allay.item_given": "Allay ri", "subtitles.entity.allay.item_taken": "Allay recolle", "subtitles.entity.allay.item_thrown": "Allay arrebola", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON> cepilla<PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON> protexeuse", "subtitles.entity.armadillo.land": "Terras do Armadillo", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON> enrolouse", "subtitles.entity.armadillo.scute_drop": "Placa de armadillo", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "Algo caeu", "subtitles.entity.arrow.hit": "Impacto de frecha", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON>", "subtitles.entity.arrow.shoot": "Disparo de frecha", "subtitles.entity.axolotl.attack": "Axolote ataca", "subtitles.entity.axolotl.death": "Axolote morre", "subtitles.entity.axolotl.hurt": "Axolote ferido", "subtitles.entity.axolotl.idle_air": "Axolote <PERSON>o", "subtitles.entity.axolotl.idle_water": "Axolote <PERSON>o", "subtitles.entity.axolotl.splash": "Ax<PERSON><PERSON> batuxando", "subtitles.entity.axolotl.swim": "Axolote nadando", "subtitles.entity.bat.ambient": "Chíos de morcego", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Morcego sae voando", "subtitles.entity.bee.ambient": "Zunidos de abella", "subtitles.entity.bee.death": "<PERSON><PERSON> morre", "subtitles.entity.bee.hurt": "<PERSON><PERSON> ferida", "subtitles.entity.bee.loop": "<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "Abella zune furiosamente", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> zu<PERSON> ledamente", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Blaze respira", "subtitles.entity.blaze.burn": "Blaze faísca", "subtitles.entity.blaze.death": "<PERSON> morre", "subtitles.entity.blaze.hurt": "<PERSON> ferido", "subtitles.entity.blaze.shoot": "<PERSON> dispara", "subtitles.entity.boat.paddle_land": "Vogando en terra", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON><PERSON>ado chocalla", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON><PERSON> morre", "subtitles.entity.breeze.deflect": "Remuíño desvía un ataque", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON><PERSON> ferido", "subtitles.entity.breeze.idle_air": "Remuíño voa", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON><PERSON> brinca", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON><PERSON> dispara", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.wind_burst": "Carga de vento impulsa", "subtitles.entity.camel.ambient": "<PERSON><PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON> recup<PERSON>", "subtitles.entity.camel.death": "<PERSON>lo morre", "subtitles.entity.camel.eat": "<PERSON><PERSON> come", "subtitles.entity.camel.hurt": "<PERSON>lo ferido", "subtitles.entity.camel.saddle": "<PERSON><PERSON> equipada", "subtitles.entity.camel.sit": "<PERSON><PERSON> senta", "subtitles.entity.camel.stand": "<PERSON><PERSON>", "subtitles.entity.camel.step": "Pasos de camelo", "subtitles.entity.camel.step_sand": "Pasos de camelo sobre area", "subtitles.entity.cat.ambient": "*miau*", "subtitles.entity.cat.beg_for_food": "Gato pide", "subtitles.entity.cat.death": "<PERSON>ato morre", "subtitles.entity.cat.eat": "Gato come", "subtitles.entity.cat.hiss": "Gato mi<PERSON>ña", "subtitles.entity.cat.hurt": "<PERSON>ato ferido", "subtitles.entity.cat.purr": "<PERSON><PERSON> ron<PERSON>e", "subtitles.entity.chicken.ambient": "Galiña cacarexa", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.chicken.egg": "Galiña pon un ovo", "subtitles.entity.chicken.hurt": "Galiña ferida", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.cow.ambient": "Muxido de vaca", "subtitles.entity.cow.death": "Vaca morre", "subtitles.entity.cow.hurt": "Vaca ferida", "subtitles.entity.cow.milk": "Vaca é muxida", "subtitles.entity.creaking.activate": "Crepitante observa", "subtitles.entity.creaking.ambient": "Crepitante crepita", "subtitles.entity.creaking.attack": "Crepitante ataca", "subtitles.entity.creaking.deactivate": "Crepitante <PERSON>", "subtitles.entity.creaking.death": "Crepitante esborrállase", "subtitles.entity.creaking.freeze": "Crepitante para", "subtitles.entity.creaking.spawn": "Crepitante abrolla", "subtitles.entity.creaking.sway": "Crepitante é golpeado", "subtitles.entity.creaking.twitch": "Crepitante t<PERSON>", "subtitles.entity.creaking.unfreeze": "Crepitante móvese", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>o", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON> chirla", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.dolphin.jump": "Golfiño brinca", "subtitles.entity.dolphin.play": "Golfiño x<PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.swim": "Golfiño nada", "subtitles.entity.donkey.ambient": "Rebuzno de burro", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.chest": "Co<PERSON>re equipado no burro", "subtitles.entity.donkey.death": "<PERSON><PERSON> morre", "subtitles.entity.donkey.eat": "<PERSON><PERSON> come", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> ferido", "subtitles.entity.donkey.jump": "<PERSON>o brinca", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.drowned.shoot": "<PERSON><PERSON>gado lanza o tridente", "subtitles.entity.drowned.step": "Pasos de afogado", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON>", "subtitles.entity.egg.throw": "<PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "Laídos de gardián an<PERSON>", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>o", "subtitles.entity.elder_guardian.curse": "Maldición de gardián ancián", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON> an<PERSON> morre", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>a", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> an<PERSON> ferido", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.ender_dragon.flap": "Aleteo de dragón", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.ender_dragon.shoot": "Disparo de dragón", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> <PERSON> cae", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> <PERSON> la<PERSON>", "subtitles.entity.ender_pearl.throw": "Perla do Ender <PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> morre", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> ferido", "subtitles.entity.enderman.scream": "<PERSON><PERSON> berra", "subtitles.entity.enderman.stare": "<PERSON><PERSON>", "subtitles.entity.enderman.teleport": "Enderman teletransportado", "subtitles.entity.endermite.ambient": "End<PERSON><PERSON> arrás<PERSON>e", "subtitles.entity.endermite.death": "Endermite morre", "subtitles.entity.endermite.hurt": "Endermite ferido", "subtitles.entity.evoker.ambient": "Invocador murmura", "subtitles.entity.evoker.cast_spell": "Invocador lanza un feitizo", "subtitles.entity.evoker.celebrate": "Invocador celebrando", "subtitles.entity.evoker.death": "Invocador morre", "subtitles.entity.evoker.hurt": "Invocador ferido", "subtitles.entity.evoker.prepare_attack": "Invocador prepara un ataque", "subtitles.entity.evoker.prepare_summon": "Invocador prepara unha invocación", "subtitles.entity.evoker.prepare_wololo": "Invocador prepara un feitizo", "subtitles.entity.evoker_fangs.attack": "Rotura de cairos", "subtitles.entity.experience_orb.pickup": "Experiencia gañada", "subtitles.entity.firework_rocket.blast": "Estoupido de <PERSON>", "subtitles.entity.firework_rocket.launch": "Lanzamento de foguete", "subtitles.entity.firework_rocket.twinkle": "Refulxencia de <PERSON>uete", "subtitles.entity.fish.swim": "<PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON> retirado", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON>o", "subtitles.entity.fishing_bobber.throw": "Cana de pescar lanzada", "subtitles.entity.fox.aggro": "<PERSON><PERSON> enfurecido", "subtitles.entity.fox.ambient": "Raposo chía", "subtitles.entity.fox.bite": "<PERSON><PERSON> morde", "subtitles.entity.fox.death": "<PERSON><PERSON> morre", "subtitles.entity.fox.eat": "<PERSON><PERSON> come", "subtitles.entity.fox.hurt": "<PERSON><PERSON> ferido", "subtitles.entity.fox.screech": "<PERSON><PERSON> berra", "subtitles.entity.fox.sleep": "Raposo ronca", "subtitles.entity.fox.sniff": "<PERSON><PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON> cuspe", "subtitles.entity.fox.teleport": "Raposo teletranspórtase", "subtitles.entity.frog.ambient": "<PERSON> croa", "subtitles.entity.frog.death": "<PERSON> morre", "subtitles.entity.frog.eat": "Ra come", "subtitles.entity.frog.hurt": "Ra ferida", "subtitles.entity.frog.lay_spawn": "<PERSON>", "subtitles.entity.frog.long_jump": "Ra brinca", "subtitles.entity.generic.big_fall": "Algo cae", "subtitles.entity.generic.burn": "Quemándose", "subtitles.entity.generic.death": "Agonía", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Comendo", "subtitles.entity.generic.explode": "Explosión", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "Algo foi danado", "subtitles.entity.generic.small_fall": "<PERSON>go tropez<PERSON>", "subtitles.entity.generic.splash": "Batuxada", "subtitles.entity.generic.swim": "Na<PERSON>do", "subtitles.entity.generic.wind_burst": "Carga de vento impulsa", "subtitles.entity.ghast.ambient": "<PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling farfalla", "subtitles.entity.ghastling.death": "Ghastling morre", "subtitles.entity.ghastling.hurt": "Ghastling ferido", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> a<PERSON>e", "subtitles.entity.glow_item_frame.add_item": "<PERSON> lumi<PERSON>o ocupado", "subtitles.entity.glow_item_frame.break": "<PERSON> lumi<PERSON>o rachado", "subtitles.entity.glow_item_frame.place": "<PERSON> lumi<PERSON> colocado", "subtitles.entity.glow_item_frame.remove_item": "<PERSON> lumi<PERSON> b<PERSON>", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON> l<PERSON>", "subtitles.entity.glow_squid.ambient": "<PERSON>ra brillante nadando", "subtitles.entity.glow_squid.death": "Lura brillante morre", "subtitles.entity.glow_squid.hurt": "<PERSON>ra brillante ferida", "subtitles.entity.glow_squid.squirt": "<PERSON>ra brillante dispara tinta", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> bea", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.goat.horn_break": "Corno de cabra racha", "subtitles.entity.goat.hurt": "C<PERSON><PERSON> ferida", "subtitles.entity.goat.long_jump": "Cabra brinca", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> muxida", "subtitles.entity.goat.prepare_ram": "<PERSON>abra prepárase para atacar", "subtitles.entity.goat.ram_impact": "Cabra ataca", "subtitles.entity.goat.screaming.ambient": "Cabra abraca", "subtitles.entity.goat.step": "<PERSON><PERSON>ra camiña", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON> ale<PERSON>o", "subtitles.entity.guardian.attack": "Disparo de gardián", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON> morre", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON><PERSON>a", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> ferido", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON> ledo cantar<PERSON>a", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> ledo morre", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> ledo preparado", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> ledo detense", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> ledo ferido", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> des<PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON>éndose en Zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n ferido", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> re<PERSON>", "subtitles.entity.hoglin.step": "Pasos de Hoglin", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> de <PERSON>alo", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> de <PERSON>alo", "subtitles.entity.horse.armor": "Armadura para cabalo equipada", "subtitles.entity.horse.breathe": "Respiración de cabalo", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> comendo", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON> galopando", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.horse.jump": "Cabalo brinca", "subtitles.entity.horse.saddle": "<PERSON><PERSON> posta", "subtitles.entity.husk.ambient": "<PERSON><PERSON> xem<PERSON>o", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.death": "<PERSON><PERSON> morre", "subtitles.entity.husk.hurt": "<PERSON><PERSON> ferida", "subtitles.entity.illusioner.ambient": "Ilusionista murmura", "subtitles.entity.illusioner.cast_spell": "Ilusinista lanza un feitizo", "subtitles.entity.illusioner.death": "Ilusionista morre", "subtitles.entity.illusioner.hurt": "Ilusionista ferido", "subtitles.entity.illusioner.mirror_move": "Ilusionista desplázase", "subtitles.entity.illusioner.prepare_blindness": "Ilusionista prepárase para cegar", "subtitles.entity.illusioner.prepare_mirror": "Ilusionista prepara espellismo", "subtitles.entity.iron_golem.attack": "Gólem de ferro ataca", "subtitles.entity.iron_golem.damage": "Gólem de ferro racha", "subtitles.entity.iron_golem.death": "Gólem de ferro morre", "subtitles.entity.iron_golem.hurt": "Gólem de ferro ferido", "subtitles.entity.iron_golem.repair": "Gólem de ferro arranxado", "subtitles.entity.item.break": "Obxecto destruído", "subtitles.entity.item.pickup": "Í<PERSON> cae", "subtitles.entity.item_frame.add_item": "Obxecto posto nun marco", "subtitles.entity.item_frame.break": "<PERSON> r<PERSON>", "subtitles.entity.item_frame.place": "<PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON>", "subtitles.entity.item_frame.rotate_item": "Obxecto do marco rotado", "subtitles.entity.leash_knot.break": "Renda rota", "subtitles.entity.leash_knot.place": "Renda posta", "subtitles.entity.lightning_bolt.impact": "Lóstre<PERSON>", "subtitles.entity.lightning_bolt.thunder": "Tronos", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.llama.angry": "Gruñido de llama furiosa", "subtitles.entity.llama.chest": "<PERSON><PERSON><PERSON> de llama equipado", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.llama.eat": "<PERSON><PERSON>a comendo", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> ferida", "subtitles.entity.llama.spit": "<PERSON><PERSON><PERSON> cuspindo", "subtitles.entity.llama.step": "Pasos de llama", "subtitles.entity.llama.swag": "Llama decorada", "subtitles.entity.magma_cube.death": "Cubo de magma morre", "subtitles.entity.magma_cube.hurt": "Cubo de magma ferido", "subtitles.entity.magma_cube.squish": "Chapoteo de cubo de magma", "subtitles.entity.minecart.inside": "Vagoneta abana", "subtitles.entity.minecart.inside_underwater": "Vagoneta abana baixo a auga", "subtitles.entity.minecart.riding": "Vagoneta en movemento", "subtitles.entity.mooshroom.convert": "Mooshroom transfórmase", "subtitles.entity.mooshroom.eat": "Mooshroom come", "subtitles.entity.mooshroom.milk": "Mooshroom muxida", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom muxida de xeito sospeitoso", "subtitles.entity.mule.ambient": "Rebuzno de mula", "subtitles.entity.mule.angry": "<PERSON><PERSON> ros<PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> de mula equipado", "subtitles.entity.mule.death": "<PERSON><PERSON> morre", "subtitles.entity.mule.eat": "<PERSON><PERSON> come", "subtitles.entity.mule.hurt": "<PERSON><PERSON> ferida", "subtitles.entity.mule.jump": "<PERSON><PERSON> brinca", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda xadea", "subtitles.entity.panda.ambient": "Panda resoña", "subtitles.entity.panda.bite": "Panda morde", "subtitles.entity.panda.cant_breed": "<PERSON>em<PERSON>da", "subtitles.entity.panda.death": "Panda morre", "subtitles.entity.panda.eat": "Panda comendo", "subtitles.entity.panda.hurt": "Panda ferido", "subtitles.entity.panda.pre_sneeze": "Ao panda pícalle o nariz", "subtitles.entity.panda.sneeze": "Panda esbirra", "subtitles.entity.panda.step": "Pegadas de panda", "subtitles.entity.panda.worried_ambient": "<PERSON>em<PERSON>da", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON> fala", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> come", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.parrot.imitate.blaze": "<PERSON>ro imita a un blaze", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON> imita a un enlamado", "subtitles.entity.parrot.imitate.breeze": "Papagaio imita un remuíño", "subtitles.entity.parrot.imitate.creaking": "<PERSON>gaio imita un crepitante", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON> imita a un creeper", "subtitles.entity.parrot.imitate.drowned": "<PERSON>gaio imita a un afogado", "subtitles.entity.parrot.imitate.elder_guardian": "Papagaio imita a un gardián", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON>o imita a un dragón", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> imita a un endermite", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> imita a un invocador", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON> imita a un ghast", "subtitles.entity.parrot.imitate.guardian": "Papagaio imita a un gardián", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> imita un Hoglin", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> imita a unha momia", "subtitles.entity.parrot.imitate.illusioner": "Papagaio imita un ilusionista", "subtitles.entity.parrot.imitate.magma_cube": "Papagaio imita a un cubo de magma", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON> imita a un pantasma", "subtitles.entity.parrot.imitate.piglin": "<PERSON>gaio imitando un piglin", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> imita un piglin bruto", "subtitles.entity.parrot.imitate.pillager": "<PERSON>gaio imita a un saqueador", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> imita a un devastador", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON> imita a un shulker", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON> imita a un lepisma", "subtitles.entity.parrot.imitate.skeleton": "<PERSON>gaio imita a un esqueleto", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON> imita a un slime", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON>o imita a unha araña", "subtitles.entity.parrot.imitate.stray": "Papagaio imita a un esqueleto glacial", "subtitles.entity.parrot.imitate.vex": "Papagaio imita a un espírito", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> imita a un vindicador", "subtitles.entity.parrot.imitate.warden": "Papagaio imita a un vixilante", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON>o imita a unha meiga", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> imita a un Wither", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON>gaio imita a un esqueleto do <PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON>o imita un Zoglin", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> imita a un zombi", "subtitles.entity.parrot.imitate.zombie_villager": "Papagaio imita a un zombi aldeán", "subtitles.entity.phantom.ambient": "Pantasma chía", "subtitles.entity.phantom.bite": "Pan<PERSON><PERSON> morde", "subtitles.entity.phantom.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.phantom.flap": "Pantasma ale<PERSON>a", "subtitles.entity.phantom.hurt": "Pantasma ferida", "subtitles.entity.phantom.swoop": "Pantasma arremete", "subtitles.entity.pig.ambient": "*oink, oink*", "subtitles.entity.pig.death": "<PERSON><PERSON>o morre", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.pig.saddle": "<PERSON><PERSON> equipada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> contempla un obxecto", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> gorla", "subtitles.entity.piglin.angry": "<PERSON><PERSON> gorla furiosamente", "subtitles.entity.piglin.celebrate": "<PERSON>lin celebra", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON>", "subtitles.entity.piglin.death": "<PERSON><PERSON> morre", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> ferido", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> gorla con envexa", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> rec<PERSON>", "subtitles.entity.piglin.step": "Pasos de piglin", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> bruto gorla", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> bruto gorla furiosamente", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> bruto z<PERSON>", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> bruto morre", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> bruto ferido", "subtitles.entity.piglin_brute.step": "<PERSON>s de piglin bruto", "subtitles.entity.pillager.ambient": "Murmurios de saqueador", "subtitles.entity.pillager.celebrate": "Saqueador cele<PERSON>o", "subtitles.entity.pillager.death": "Saqueador morre", "subtitles.entity.pillager.hurt": "Saqueador ferido", "subtitles.entity.player.attack.crit": "Ataque crítico", "subtitles.entity.player.attack.knockback": "Ataque con empurrón", "subtitles.entity.player.attack.strong": "Ataque forte", "subtitles.entity.player.attack.sweep": "Ataque con alcance", "subtitles.entity.player.attack.weak": "Ataque feble", "subtitles.entity.player.burp": "Eructo", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.player.freeze_hurt": "<PERSON>ogador <PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.player.hurt_drown": "Xogador a<PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.levelup": "Subida de nivel (XP)", "subtitles.entity.player.teleport": "Xogador teleportado", "subtitles.entity.polar_bear.ambient": "<PERSON>em<PERSON>", "subtitles.entity.polar_bear.ambient_baby": "Oso polar murmura", "subtitles.entity.polar_bear.death": "Oso polar morre", "subtitles.entity.polar_bear.hurt": "Oso polar ferido", "subtitles.entity.polar_bear.warning": "<PERSON>so polar ruxiu", "subtitles.entity.potion.splash": "Frasco rachado", "subtitles.entity.potion.throw": "<PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "Peixe globo desínflase", "subtitles.entity.puffer_fish.blow_up": "Peixe globo ínflase", "subtitles.entity.puffer_fish.death": "Peixe globo morre", "subtitles.entity.puffer_fish.flop": "Peixe globo batuxa", "subtitles.entity.puffer_fish.hurt": "Peixe globo ferido", "subtitles.entity.puffer_fish.sting": "Peixe globo pica", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON> ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON> morre", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> ferido", "subtitles.entity.rabbit.jump": "<PERSON><PERSON> brinca", "subtitles.entity.ravager.ambient": "Devast<PERSON> g<PERSON>", "subtitles.entity.ravager.attack": "Devast<PERSON> morde", "subtitles.entity.ravager.celebrate": "Devastador cele<PERSON>o", "subtitles.entity.ravager.death": "Devast<PERSON> morre", "subtitles.entity.ravager.hurt": "Devast<PERSON> ferido", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "Pegadas de devastador", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "Sal<PERSON><PERSON> morre", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON> bat<PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.sheep.ambient": "*beeee*", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON> ferida", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> aberto", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teletransportado", "subtitles.entity.shulker_bullet.hit": "Proxectil de shulker estoupa", "subtitles.entity.shulker_bullet.hurt": "Proxectil de shulker pártese", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.silverfish.hurt": "Le<PERSON><PERSON> ferida", "subtitles.entity.skeleton.ambient": "Esqueleto chocalla", "subtitles.entity.skeleton.converted_to_stray": "Esqueleto convértese en esqueleto glacial", "subtitles.entity.skeleton.death": "Esqueleto morre", "subtitles.entity.skeleton.hurt": "Esqueleto ferido", "subtitles.entity.skeleton.shoot": "Disparo de esqueleto", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> de cabalo esqueleto", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON> esqueleto morre", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON> esqueleto ferido", "subtitles.entity.skeleton_horse.jump_water": "Cabalo esqueleto brinca", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON>lo es<PERSON>", "subtitles.entity.slime.attack": "Slime atacando", "subtitles.entity.slime.death": "Slime morre", "subtitles.entity.slime.hurt": "Slime ferido", "subtitles.entity.slime.squish": "Brinco de slime", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.sniffer.digging": "Cheirador cava", "subtitles.entity.sniffer.digging_stop": "Cheirador <PERSON>", "subtitles.entity.sniffer.drop_seed": "Cheirador solta unha semente", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.sniffer.egg_crack": "Ovo de cheirador racha", "subtitles.entity.sniffer.egg_hatch": "Ovo de cheirador eclosiona", "subtitles.entity.sniffer.happy": "Cheirador <PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "Cheirador olfactea", "subtitles.entity.sniffer.searching": "Cheirador procura", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "Pasos de cheirador", "subtitles.entity.snow_golem.death": "Gólem de neve morre", "subtitles.entity.snow_golem.hurt": "Gólem de neve ferido", "subtitles.entity.snowball.throw": "Bóla de neve lanzada", "subtitles.entity.spider.ambient": "Siseo de araña", "subtitles.entity.spider.death": "<PERSON><PERSON> morre", "subtitles.entity.spider.hurt": "Araña ferida", "subtitles.entity.squid.ambient": "<PERSON><PERSON>", "subtitles.entity.squid.death": "<PERSON>ra morre", "subtitles.entity.squid.hurt": "<PERSON>ra ferida", "subtitles.entity.squid.squirt": "<PERSON>ra dispara tinta", "subtitles.entity.stray.ambient": "Esqueleto glacial chocalla", "subtitles.entity.stray.death": "Esqueleto glacial morre", "subtitles.entity.stray.hurt": "Esqueleto glacial ferido", "subtitles.entity.strider.death": "Vagante morre", "subtitles.entity.strider.eat": "Vagan<PERSON> come", "subtitles.entity.strider.happy": "Vagante alédase", "subtitles.entity.strider.hurt": "Vagante ferido", "subtitles.entity.strider.idle": "Vagante chía", "subtitles.entity.strider.retreat": "Vagante recúa", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON> crece", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.tnt.primed": "<PERSON><PERSON><PERSON> acendida", "subtitles.entity.tropical_fish.death": "Peixe tropical morre", "subtitles.entity.tropical_fish.flop": "Peixe tropical brinca", "subtitles.entity.tropical_fish.hurt": "Peixe tropical ferido", "subtitles.entity.turtle.ambient_land": "Tartaruga chirla", "subtitles.entity.turtle.death": "Tartaruga morre", "subtitles.entity.turtle.death_baby": "Cría de tartaruga morre", "subtitles.entity.turtle.egg_break": "Ovo de tartaruga rompéndose", "subtitles.entity.turtle.egg_crack": "Ovo de tartaruga rachándose", "subtitles.entity.turtle.egg_hatch": "Ovo de tartaruga eclosiona", "subtitles.entity.turtle.hurt": "Tartaruga ferida", "subtitles.entity.turtle.hurt_baby": "Cría de tartaruga ferida", "subtitles.entity.turtle.lay_egg": "Tartaruga pon un ovo", "subtitles.entity.turtle.shamble": "Tartaruga reptando", "subtitles.entity.turtle.shamble_baby": "Cría de tartaruga reptando", "subtitles.entity.turtle.swim": "Tartaruga nadando", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON><PERSON> de espírito", "subtitles.entity.vex.charge": "Berros de espírito", "subtitles.entity.vex.death": "Esp<PERSON><PERSON> morre", "subtitles.entity.vex.hurt": "Espírito ferido", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "Aldeán celebra", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON> ferido", "subtitles.entity.villager.no": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.trade": "Aldeán troca", "subtitles.entity.villager.work_armorer": "Coiraceiro traballa", "subtitles.entity.villager.work_butcher": "Buxeo traballa", "subtitles.entity.villager.work_cartographer": "Cartógrafo t<PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON> traballa", "subtitles.entity.villager.work_farmer": "Agricultor traballa", "subtitles.entity.villager.work_fisherman": "Pescantín traballa", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON> traball<PERSON>", "subtitles.entity.villager.work_leatherworker": "Pele<PERSON>iro traballa", "subtitles.entity.villager.work_librarian": "Bibliotecario traballa", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON><PERSON> traballa", "subtitles.entity.villager.work_shepherd": "<PERSON> t<PERSON>", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON><PERSON> traballa", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON> traball<PERSON>", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.ambient": "Vindicador murmura", "subtitles.entity.vindicator.celebrate": "Vindicador celebrando", "subtitles.entity.vindicator.death": "Vindicador morre", "subtitles.entity.vindicator.hurt": "Vindicador ferido", "subtitles.entity.wandering_trader.ambient": "Vendedor ambulante murmura", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON>or ambulante morre", "subtitles.entity.wandering_trader.disappeared": "Vendedor ambulante desaparece", "subtitles.entity.wandering_trader.drink_milk": "<PERSON>endedor ambulante bebendo leite", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON>or ambulante bebendo unha poción", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON> ambulante ferido", "subtitles.entity.wandering_trader.no": "Vendedor ambulante rexeita", "subtitles.entity.wandering_trader.reappeared": "Vendedor ambulante aparece", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON> ambulante trocando", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON>or ambulante acepta", "subtitles.entity.warden.agitated": "Vixilante xeme furiosamente", "subtitles.entity.warden.ambient": "Ouveo de vixilante", "subtitles.entity.warden.angry": "Vixilante enfádase", "subtitles.entity.warden.attack_impact": "Vixilante golpea", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON><PERSON> morre", "subtitles.entity.warden.dig": "Vixilante cava", "subtitles.entity.warden.emerge": "Vixilante emerxe", "subtitles.entity.warden.heartbeat": "Latexos de vixilante", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON><PERSON> ferido", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "subtitles.entity.warden.listening_angry": "Vixilante decátase furiosamente", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON>nte a<PERSON>", "subtitles.entity.warden.nearby_closer": "Vixilante a<PERSON>", "subtitles.entity.warden.nearby_closest": "Vixilante aproxímase", "subtitles.entity.warden.roar": "Vixilante ruxe", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON><PERSON> osma", "subtitles.entity.warden.sonic_boom": "Vixilante ruxe", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.step": "Pasos de vixilante", "subtitles.entity.warden.tendril_clicks": "Antenas de vixilante estalan", "subtitles.entity.wind_charge.throw": "Carga de vento voa", "subtitles.entity.wind_charge.wind_burst": "Carga de vento impulsa", "subtitles.entity.witch.ambient": "Risa de meiga", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> cele<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON> morre", "subtitles.entity.witch.drink": "<PERSON><PERSON> bebendo", "subtitles.entity.witch.hurt": "Meiga ferida", "subtitles.entity.witch.throw": "Meiga guindando pocións", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "Wither morre", "subtitles.entity.wither.hurt": "Wither ferido", "subtitles.entity.wither.shoot": "Wither ataca", "subtitles.entity.wither.spawn": "<PERSON><PERSON> liberado", "subtitles.entity.wither_skeleton.ambient": "Esqueleto do Wither chocalla", "subtitles.entity.wither_skeleton.death": "Esqueleto do Wither morre", "subtitles.entity.wither_skeleton.hurt": "Esqueleto do Wither ferido", "subtitles.entity.wolf.ambient": "Respiración de lobo", "subtitles.entity.wolf.bark": "<PERSON><PERSON>", "subtitles.entity.wolf.death": "Lobo morre", "subtitles.entity.wolf.growl": "Gruñ<PERSON> de <PERSON>", "subtitles.entity.wolf.hurt": "Lobo ferido", "subtitles.entity.wolf.pant": "Lobo boquexando", "subtitles.entity.wolf.shake": "Lobo sacudíndose", "subtitles.entity.wolf.whine": "Lobo chorando", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON><PERSON>lin", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON>n ferido", "subtitles.entity.zoglin.step": "<PERSON>s de <PERSON>lin", "subtitles.entity.zombie.ambient": "<PERSON>ombi xemendo", "subtitles.entity.zombie.attack_wooden_door": "Porta zarandeándose", "subtitles.entity.zombie.break_wooden_door": "Porta rompéndose", "subtitles.entity.zombie.converted_to_drowned": "Zombie afogándose", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> morre", "subtitles.entity.zombie.destroy_egg": "Ovo de tartaruga pisado", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> sufrindo", "subtitles.entity.zombie.infect": "Zombi inféctase", "subtitles.entity.zombie_horse.ambient": "Berro de cabalo zombi", "subtitles.entity.zombie_horse.death": "C<PERSON>lo zombi morre", "subtitles.entity.zombie_horse.hurt": "C<PERSON>lo zombi ferido", "subtitles.entity.zombie_villager.ambient": "Zombi aldeán xeme", "subtitles.entity.zombie_villager.converted": "Zombi aldeán agoniza", "subtitles.entity.zombie_villager.cure": "Zombi aldeán suspira", "subtitles.entity.zombie_villager.death": "<PERSON><PERSON>i al<PERSON> morre", "subtitles.entity.zombie_villager.hurt": "<PERSON><PERSON>i al<PERSON>án ferido", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> zombificado gorla", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON> zombificado gorla furiosamente", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombificado morre", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> zombificado ferido", "subtitles.event.mob_effect.bad_omen": "Presaxio en acción", "subtitles.event.mob_effect.raid_omen": "Invasión inminente", "subtitles.event.mob_effect.trial_omen": "Desafío sombrío inminente", "subtitles.event.raid.horn": "<PERSON><PERSON> sin<PERSON>", "subtitles.item.armor.equip": "Armadura equipada", "subtitles.item.armor.equip_chain": "Tintineo de armadura de cota de mallas", "subtitles.item.armor.equip_diamond": "Trisquido de armadura de diamante", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON><PERSON> élitros", "subtitles.item.armor.equip_gold": "Tintineo de armadura de ouro", "subtitles.item.armor.equip_iron": "Trisquido de armadura de ferro", "subtitles.item.armor.equip_leather": "Cruxido de armadura de coiro", "subtitles.item.armor.equip_netherite": "Trisquido da armadura de netherita", "subtitles.item.armor.equip_turtle": "Cuncha de tartaruga equipada", "subtitles.item.armor.equip_wolf": "Armadura de lobo equipada", "subtitles.item.armor.unequip_wolf": "Armadura de lobo soltada", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bone_meal.use": "Far<PERSON><PERSON> de óso usada", "subtitles.item.book.page_turn": "Páxina pasada", "subtitles.item.book.put": "Libro colocado", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON> bale<PERSON>", "subtitles.item.bottle.fill": "Frasco recheado", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON><PERSON> cascallo", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON><PERSON><PERSON> cepillado", "subtitles.item.brush.brushing.sand": "Cepillando area", "subtitles.item.brush.brushing.sand.complete": "Area cepillada", "subtitles.item.bucket.empty": "<PERSON>lde bale<PERSON>do", "subtitles.item.bucket.fill": "Balde enchido", "subtitles.item.bucket.fill_axolotl": "Axolote a<PERSON>", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> capturado", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON> cap<PERSON>", "subtitles.item.bundle.drop_contents": "Saco bale<PERSON>", "subtitles.item.bundle.insert": "Obxecto empaquetado", "subtitles.item.bundle.insert_fail": "Bo<PERSON>a chea", "subtitles.item.bundle.remove_one": "Obxecto desempaquetado", "subtitles.item.chorus_fruit.teleport": "Xogador teletransportado", "subtitles.item.crop.plant": "Sementes cultivadas", "subtitles.item.crossbow.charge": "Bésta cargán<PERSON>", "subtitles.item.crossbow.hit": "Impacto de frecha", "subtitles.item.crossbow.load": "Bésta cargada", "subtitles.item.crossbow.shoot": "Disparo de bésta", "subtitles.item.dye.use": "Manchas de tin<PERSON>", "subtitles.item.elytra.flying": "As<PERSON><PERSON>", "subtitles.item.firecharge.use": "Bóla de lume lanzada", "subtitles.item.flintandsteel.use": "Chisqueiro usado", "subtitles.item.glow_ink_sac.use": "Saco de borra brillante manchando", "subtitles.item.goat_horn.play": "Corno de cabra soa", "subtitles.item.hoe.till": "Terra sachada", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "Armadura para cabalo soltada", "subtitles.item.ink_sac.use": "Saco de borra manchando", "subtitles.item.lead.break": "Renda rachada", "subtitles.item.lead.tied": "Renda atada", "subtitles.item.lead.untied": "Renda desatada", "subtitles.item.llama_carpet.unequip": "Alfombra retirada", "subtitles.item.lodestone_compass.lock": "Compás magnetizado detecta magnetita", "subtitles.item.mace.smash_air": "<PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON>", "subtitles.item.nether_wart.plant": "Semente cultivada", "subtitles.item.ominous_bottle.dispose": "Bo<PERSON>la escacha", "subtitles.item.saddle.unequip": "<PERSON><PERSON> reti<PERSON>", "subtitles.item.shears.shear": "Corte de tesoiras", "subtitles.item.shears.snip": "Tesoiras cortan", "subtitles.item.shield.block": "Bloqueo de escudo", "subtitles.item.shovel.flatten": "Terra aplanada", "subtitles.item.spyglass.stop_using": "<PERSON>s anteollos retr<PERSON>", "subtitles.item.spyglass.use": "<PERSON>s anteollos esténden<PERSON>", "subtitles.item.totem.use": "Tótem activado", "subtitles.item.trident.hit": "Puñalada de tridente", "subtitles.item.trident.hit_ground": "Vibracións de tridente", "subtitles.item.trident.return": "Tridente volve", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON> de tridente", "subtitles.item.trident.throw": "Ruidos de tridente", "subtitles.item.trident.thunder": "Tronos de tridente", "subtitles.item.wolf_armor.break": "Armadura de lobo estragada", "subtitles.item.wolf_armor.crack": "Armadura de lobo rachada", "subtitles.item.wolf_armor.damage": "Armadura de lobo danada", "subtitles.item.wolf_armor.repair": "Armadura de lobo arranxada", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON> fuxindo", "subtitles.ui.cartography_table.take_result": "Mapa debuxado", "subtitles.ui.hud.bubble_pop": "Barra de respiración diminúe", "subtitles.ui.loom.take_result": "Tear usado", "subtitles.ui.stonecutter.take_result": "Cortapedras usado", "subtitles.weather.rain": "Chove", "symlink_warning.message": "Cargar mundos desde cartafois con ligazóns simbólicas pode ser pouco seguro se non sabes exactamente o que estás a facer. Visita %s para máis información.", "symlink_warning.message.pack": "Cargar paquetes con ligazóns simbólicas pode ser inseguro se non sabes exactamente o que estás facendo. Visita %s para obter máis información.", "symlink_warning.message.world": "Cargar mundos desde cartafois con ligazóns simbólicas pode ser inseguro se non sabes exactamente o que estás facendo. Visita %s para obter máis información.", "symlink_warning.more_info": "Mais información", "symlink_warning.title": "O cartafol do mundo contén ligazóns simbólicas", "symlink_warning.title.pack": "Os paquetes engadido(a) conteñen ligazóns simbólicas", "symlink_warning.title.world": "O cartafol do mundo contén ligazóns simbólicas", "team.collision.always": "Sempre", "team.collision.never": "Nunca", "team.collision.pushOtherTeams": "Empurra xogadores doutros equipos", "team.collision.pushOwnTeam": "Empurra xogadores do propio equipo", "team.notFound": "Equipo descoñecido: %s", "team.visibility.always": "Sempre", "team.visibility.hideForOtherTeams": "Agochar para outros equipos", "team.visibility.hideForOwnTeam": "Agochar para o propio equipo", "team.visibility.never": "Nunca", "telemetry.event.advancement_made.description": "Comprender o contexto no que se realiza un progreso axudaranos a entender e mellorar a progresión do xogo.", "telemetry.event.advancement_made.title": "<PERSON><PERSON><PERSON> feita", "telemetry.event.game_load_times.description": "Este evento pode axudarnos a determinar onde se necesitan as melloras de rendemento, medindo os tempos de execución das diferentes fases de arranque.", "telemetry.event.game_load_times.title": "Tempos de carga do xogo", "telemetry.event.optional": "%s (opcional)", "telemetry.event.optional.disabled": "%s (opcional): Desactivado", "telemetry.event.performance_metrics.description": "Coñecer o perfil de rendemento de Minecraft axúdanos a optimizar o xogo para moitas especificacións e sistemas operativos. \nA versión do xogo inclúese para axudarnos a comparar o rendemento de novas versións.", "telemetry.event.performance_metrics.title": "Medición do rendemento", "telemetry.event.required": "%s (necesario)", "telemetry.event.world_load_times.description": "É importante que saibamos canto tempo se tarda en entrar nun mundo e como cambia iso co tempo. Por exemplo, cando engadimos novas características ou realizamos cambios técnicos grandes, necesitamos ver que impacto teñen nos tempos de carga.", "telemetry.event.world_load_times.title": "Tempos de carga do mundo", "telemetry.event.world_loaded.description": "Saber como xogan os xogadores a Minecraft (que modo de xogo, cliente ou servidor modificado e versión do xogo utilizan) axúdanos a mellorar os aspectos que máis lle importan ós xogadores.\nO evento «Mundo cargado» está unido o de «Mundo descargado» para calcular canto durou a sesión de xogo.", "telemetry.event.world_loaded.title": "<PERSON><PERSON> cargado", "telemetry.event.world_unloaded.description": "Este evento está unido ó de mundo cargado para calcular canto durou a sesión de xogo.\nA duración (en segundos e tics) mídese cando unha sesión de xogo finaliza (saíndo ó menú principal ou desconectándose dun servidor).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON> de xogo (tics)", "telemetry.property.advancement_id.title": "ID da proeza", "telemetry.property.client_id.title": "ID de cliente", "telemetry.property.client_modded.title": "Cliente modificado", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicada (kB)", "telemetry.property.event_timestamp_utc.title": "Hora do evento (UTC)", "telemetry.property.frame_rate_samples.title": "Mostras de fotogramas por segundo (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "Versión do xogo", "telemetry.property.launcher_name.title": "Nome do lanzador", "telemetry.property.load_time_bootstrap_ms.title": "Tempo de inicio (milisegundos)", "telemetry.property.load_time_loading_overlay_ms.title": "Tempo en pantalla de carga (milisegundos)", "telemetry.property.load_time_pre_window_ms.title": "Tempo antes de que se abra a ventá (milisegundos)", "telemetry.property.load_time_total_time_ms.title": "Tempo total de carga (milisegundos)", "telemetry.property.minecraft_session_id.title": "ID de sesión de Minecraft", "telemetry.property.new_world.title": "Novo Mundo", "telemetry.property.number_of_samples.title": "Número de <PERSON>", "telemetry.property.operating_system.title": "Sistema operativo", "telemetry.property.opt_in.title": "Inscrito", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Contido do mapa de Realms (nome do minixogo)", "telemetry.property.render_distance.title": "Distancia de renderizado", "telemetry.property.render_time_samples.title": "Mostras de tempo de renderizado", "telemetry.property.seconds_since_load.title": "Tempo dende que a carga (segundos)", "telemetry.property.server_modded.title": "Servidor modificado", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON> de servidor", "telemetry.property.ticks_since_load.title": "Tempo dende que a carga (tics)", "telemetry.property.used_memory_samples.title": "Memoria de acceso aleatorio (RAM) usada", "telemetry.property.user_id.title": "ID de usuario", "telemetry.property.world_load_time_ms.title": "Tempo de carga do mundo (en milisegundos)", "telemetry.property.world_session_id.title": "ID de sesión do mundo", "telemetry_info.button.give_feedback": "Dar a miña opinión", "telemetry_info.button.privacy_statement": "Declaración de privacidade", "telemetry_info.button.show_data": "<PERSON><PERSON><PERSON> os meus datos", "telemetry_info.opt_in.description": "Acepto enviar datos de telemetría opcionais", "telemetry_info.property_title": "<PERSON>tos incluí<PERSON>", "telemetry_info.screen.description": "Recolectar esta infosmación axúdanos a mellorar Minecraft guiándonos na dirección correctar para os nosos xogadores.\nTamén podes enviar suxerencias para axudarnos a seguir mellorando Minecraft.", "telemetry_info.screen.title": "Recolección de datos de telemetría", "test.error.block_property_mismatch": "Agardábase que a propiedade %s fose %s, resultou %s", "test.error.block_property_missing": "Propiedade de bloque ausente, agardábase que a propiedade %s fose %s", "test.error.entity_property": "Entidade %s fracasou proba: %s", "test.error.entity_property_details": "Entidade %s fracasou proba: %s, agardábase %s, resultou %s", "test.error.expected_block": "Agardábase bloque %s, resultou %s", "test.error.expected_block_tag": "Agardábase bloque en #%s, resultou %s", "test.error.expected_container_contents": "Contedor debería conter: %s", "test.error.expected_container_contents_single": "Contedor debería conter un só: %s", "test.error.expected_empty_container": "Contedor de<PERSON> estar baleiro", "test.error.expected_entity": "Agardábase %s", "test.error.expected_entity_around": "Agardábase que %s existise preto de %s, %s, %s", "test.error.expected_entity_count": "Agardábanse %s entidades de tipo %s, atopáronse %s", "test.error.expected_entity_data": "Agardábase que datos da entidade fosen: %s, resultaron: %s", "test.error.expected_entity_data_predicate": "Discrepancia de datos da entidade en %s", "test.error.expected_entity_effect": "Agardábase que %s tivera o efecto %s %s", "test.error.expected_entity_having": "Inventario da entidade debería conter %s", "test.error.expected_entity_holding": "Entidade debería estar a suxeitar %s", "test.error.expected_entity_in_test": "Agardábase a existencia de %s na proba", "test.error.expected_entity_not_touching": "Non se agardaba que %s tocase %s, %s, %s (relativo a: %s, %s, %s)", "test.error.expected_entity_touching": "Agardábase que %s tocase %s, %s, %s (relativo a: %s, %s, %s)", "test.error.expected_item": "Agardábase obxecto de tipo %s", "test.error.expected_items_count": "Agardábanse %s obxectos de tipo %s, agardábanse %s", "test.error.fail": "Condicións de fracaso cumpridas", "test.error.invalid_block_type": "Tipo de bloque inesperado atopado: %s", "test.error.missing_block_entity": "Bloque entidade ausente", "test.error.position": "%s en %s, %s, %s (relativo a: %s, %s,%s) en ciclo %s", "test.error.sequence.condition_already_triggered": "Condición xa desencadeada en %s", "test.error.sequence.condition_not_triggered": "Condición non desencadeada", "test.error.sequence.invalid_tick": "Realizouse con éxito en ciclo inválido: agardábase %s", "test.error.sequence.not_completed": "Tempo de proba rematado antes da finalización da secuencia", "test.error.set_biome": "Fracaso no intento de establecer bioma para a proba", "test.error.spawn_failure": "Non se puido crear a entidade %s", "test.error.state_not_equal": "Estado incorrecto. Agardábase %s, resultou %s", "test.error.structure.failure": "Falla ao colocar estrutura de proba para %s", "test.error.tick": "%s en ciclo %s", "test.error.ticking_without_structure": "Proba de ticks antes de colocar unha estrutura", "test.error.timeout.no_result": "Falla dentro de %s ticks", "test.error.timeout.no_sequences_finished": "Ningunha secuencia rematou dentro de %s ticks", "test.error.too_many_entities": "Agardábase que só un %s existise preto de %s, %s, %s pero se atoparon %s", "test.error.unexpected_block": "Non se agardaba que o bloque fose %s", "test.error.unexpected_entity": "Non se agardaba a existencia de %s", "test.error.unexpected_item": "Non se agardaba a existencia de %s", "test.error.unknown": "Erro interno descoñecido: %s", "test.error.value_not_equal": "Agardábase que %s fose %s, resultou %s", "test.error.wrong_block_entity": "Tipo de bloque entidade erróneo: %s", "test_block.error.missing": "Estrutura da proba carece do bloque %s", "test_block.error.too_many": "Demasiados bloques %s", "test_block.invalid_timeout": "Tempo límite inválido (%s) — debe ser un número positivo de ciclos", "test_block.message": "Mensaxe:", "test_block.mode.accept": "Aceptar", "test_block.mode.fail": "<PERSON><PERSON><PERSON>", "test_block.mode.log": "<PERSON><PERSON><PERSON>", "test_block.mode.start": "<PERSON><PERSON>", "test_block.mode_info.accept": "Modo aceptación — acepta éxito para (parte de) unha proba", "test_block.mode_info.fail": "<PERSON>do fracaso — fracasa a proba", "test_block.mode_info.log": "<PERSON><PERSON> rexistro — rexistra unha mensaxe", "test_block.mode_info.start": "<PERSON><PERSON> comezo — o punto de partida para unha proba", "test_instance.action.reset": "Restaurar e cargar", "test_instance.action.run": "Cargar e executar", "test_instance.action.save": "Gardar estrutura", "test_instance.description.batch": "Lote: %s", "test_instance.description.failed": "Fracasado: %s", "test_instance.description.function": "Función: %s", "test_instance.description.invalid_id": "Identificador de proba inválido", "test_instance.description.no_test": "Non hai tal proba", "test_instance.description.structure": "Estrutura: %s", "test_instance.description.type": "Tipo: %s", "test_instance.type.block_based": "Proba baseada en bloques", "test_instance.type.function": "Proba de función por defecto", "test_instance_block.entities": "Entidades:", "test_instance_block.error.no_test": "Incapaz de realizar instancia de proba en %s, %s, %s xa que ten unha proba indefinida", "test_instance_block.error.no_test_structure": "Incapaz de realizar instancia de proba en %s, %s, %s xa que non ten unha estrutura", "test_instance_block.error.unable_to_save": "Incapaz de gardar modelo de estrutura para instancia de proba en %s, %s, %s", "test_instance_block.invalid": "[inv<PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Proba %s restaurada con éxito", "test_instance_block.rotation": "Rotación:", "test_instance_block.size": "<PERSON>bar ta<PERSON> da estrutura", "test_instance_block.starting": "Comezando proba %s", "test_instance_block.test_id": "Identificador de instancia da proba", "title.32bit.deprecation": "Sistema de 32 bits detectado: isto pode impedirche xogar nun futuro, pois un sistema de 64 bits será requirido!", "title.32bit.deprecation.realms": "Minecraft requirirá cedo un sistema de 64 bits, o que che impedirá xogar ou utilizar Realms neste dispositivo. Terás que cancelar manualmente calquera subscrición a Realms.", "title.32bit.deprecation.realms.check": "Non amosar esta mensaxe novamente", "title.32bit.deprecation.realms.header": "Sistema de 32 bits detectado", "title.credits": "Dereito de autor de Mojang AB. Non distribuír!", "title.multiplayer.disabled": "O multixogador está desactivado. Por favor, comproba os axustes da túa conta de Microsoft.", "title.multiplayer.disabled.banned.name": "Debes cambiar o teu nome antes de xogar en liña", "title.multiplayer.disabled.banned.permanent": "A túa conta de xogo en liña foi baneada de maneira permanente, polo que non poderás xogar online nunca máis", "title.multiplayer.disabled.banned.temporary": "A túa conta de xogo en liña foi baneada de maneira temporánea, polo que non poderás xogar online", "title.multiplayer.lan": "Multixogador (LAN)", "title.multiplayer.other": "Multixogador (servidores de terceiros)", "title.multiplayer.realms": "Multixogador (Realms)", "title.singleplayer": "Un xogador", "translation.test.args": "%s%s", "translation.test.complex": "Prefixo, %s%2$s outra vez %s e %1$s, por último %s e tamén %1$s de novo!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "ola %", "translation.test.invalid2": "ola %s", "translation.test.none": "Ola, mundo!", "translation.test.world": "mundo", "trim_material.minecraft.amethyst": "Material de ametista", "trim_material.minecraft.copper": "Material de cobre", "trim_material.minecraft.diamond": "Material de diamante", "trim_material.minecraft.emerald": "Material de esmeralda", "trim_material.minecraft.gold": "Material de ouro", "trim_material.minecraft.iron": "Material de ferro", "trim_material.minecraft.lapis": "Material de lapislázuli", "trim_material.minecraft.netherite": "Material de netherita", "trim_material.minecraft.quartz": "Material de cuarzo", "trim_material.minecraft.redstone": "Material de redstone", "trim_material.minecraft.resin": "Material de recina", "trim_pattern.minecraft.bolt": "Deseño de parafuso", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.dune": "Deseño de duna", "trim_pattern.minecraft.eye": "Deseño <PERSON>", "trim_pattern.minecraft.flow": "Deseño de remuíño", "trim_pattern.minecraft.host": "Deseño de anfitrión", "trim_pattern.minecraft.raiser": "Deseño de elevación", "trim_pattern.minecraft.rib": "Deseño de costelas", "trim_pattern.minecraft.sentry": "Deseño de sentinela", "trim_pattern.minecraft.shaper": "Deseño de modelador", "trim_pattern.minecraft.silence": "Deseño de silencio", "trim_pattern.minecraft.snout": "Deseño de fociño", "trim_pattern.minecraft.spire": "Deseño de agulla", "trim_pattern.minecraft.tide": "Deseño de marea", "trim_pattern.minecraft.vex": "Deseño de espírito", "trim_pattern.minecraft.ward": "Deseño de garda", "trim_pattern.minecraft.wayfinder": "Deseño de guía", "trim_pattern.minecraft.wild": "Deseño sal<PERSON>xe", "tutorial.bundleInsert.description": "Clic dereito para engadir obxectos", "tutorial.bundleInsert.title": "Usa un saco", "tutorial.craft_planks.description": "O libro de receitas pode axudarte", "tutorial.craft_planks.title": "Fabrica táboas de madeira", "tutorial.find_tree.description": "Golpea o tronco para obter madeira", "tutorial.find_tree.title": "Busca unha árbore", "tutorial.look.description": "Emprega o rato para xirar", "tutorial.look.title": "Mira ó teu redor", "tutorial.move.description": "Brinca con %s", "tutorial.move.title": "Móvete con %s, %s, %s y %s", "tutorial.open_inventory.description": "Preme %s", "tutorial.open_inventory.title": "Abre o teu inventario", "tutorial.punch_tree.description": "Mantén %s premido", "tutorial.punch_tree.title": "Destrúe a árbore", "tutorial.socialInteractions.description": "Preme %s para abrir", "tutorial.socialInteractions.title": "Interaccións sociais", "upgrade.minecraft.netherite_upgrade": "Mellora de netherita"}